#!/bin/bash

# Circuit Breaker JMeter Testing Script
# Usage: ./run-circuit-breaker-tests.sh [test_type]
# test_type: normal, stress, failure, recovery, all (default: all)

set -e

# Configuration
BASE_URL="127.0.0.1"
PORT="8081"
CB_MONITOR_URL="http://localhost:8081/clientbackend"
RESULTS_DIR="results"
TEST_PLAN="jmeter-circuit-breaker-test.jmx"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test type
TEST_TYPE=${1:-"all"}

echo -e "${BLUE}🚀 Circuit Breaker JMeter Testing Suite${NC}"
echo "========================================"
echo "Test Type: $TEST_TYPE"
echo "Base URL: $BASE_URL:$PORT"
echo "Results Directory: $RESULTS_DIR"
echo ""

# Create results directory
mkdir -p $RESULTS_DIR

# Function to check if application is running
check_application() {
    echo -e "${YELLOW}🔍 Checking if application is running...${NC}"
    if curl -s "http://$BASE_URL:$PORT/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Application is running${NC}"
    else
        echo -e "${RED}❌ Application is not running on $BASE_URL:$PORT${NC}"
        echo "Please start the application first:"
        echo "mvn spring-boot:run -Dspring.profiles.active=hotels"
        exit 1
    fi
}

# Function to check circuit breaker status
check_circuit_breaker() {
    echo -e "${YELLOW}🔍 Checking circuit breaker status...${NC}"
    if curl -s "$CB_MONITOR_URL/cg/circuit-breaker/state/mob-landing" > /dev/null 2>&1; then
        CB_STATE=$(curl -s "$CB_MONITOR_URL/cg/circuit-breaker/state/mob-landing" | jq -r '.state' 2>/dev/null || echo "UNKNOWN")
        echo -e "${GREEN}✅ Circuit breaker is accessible - State: $CB_STATE${NC}"
    else
        echo -e "${RED}❌ Circuit breaker monitoring endpoint not accessible${NC}"
        exit 1
    fi
}

# Function to run JMeter test
run_jmeter_test() {
    local test_name=$1
    local threads=$2
    local ramp_up=$3
    local duration=$4
    local description=$5
    
    echo -e "${BLUE}🧪 Running $test_name Test${NC}"
    echo "Description: $description"
    echo "Threads: $threads, Ramp-up: ${ramp_up}s, Duration: ${duration}s"
    echo ""
    
    # Create test-specific results directory
    local test_results_dir="$RESULTS_DIR/$test_name"
    mkdir -p "$test_results_dir"
    
    # Run JMeter test
    jmeter -n -t "$TEST_PLAN" \
           -l "$test_results_dir/results.jtl" \
           -e -o "$test_results_dir/report" \
           -JBASE_URL="$BASE_URL" \
           -JPORT="$PORT" \
           -JTHREADS="$threads" \
           -JRAMP_UP="$ramp_up" \
           -JDURATION="$duration" \
           > "$test_results_dir/jmeter.log" 2>&1
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ $test_name test completed successfully${NC}"
        echo "Results: $test_results_dir/report/index.html"
    else
        echo -e "${RED}❌ $test_name test failed${NC}"
        echo "Check logs: $test_results_dir/jmeter.log"
    fi
    echo ""
}

# Function to monitor circuit breaker during test
monitor_circuit_breaker() {
    local test_name=$1
    local duration=$2
    
    echo -e "${YELLOW}📊 Monitoring circuit breaker during $test_name test...${NC}"
    
    local monitor_file="$RESULTS_DIR/$test_name/circuit-breaker-monitor.log"
    local end_time=$(($(date +%s) + duration))
    
    echo "Timestamp,State,FailureRate,NumberOfBufferedCalls,NumberOfFailedCalls,NumberOfSuccessfulCalls" > "$monitor_file"
    
    while [ $(date +%s) -lt $end_time ]; do
        local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
        local cb_data=$(curl -s "$CB_MONITOR_URL/cg/circuit-breaker/metrics/mob-landing" 2>/dev/null)
        
        if [ $? -eq 0 ]; then
            local state=$(echo "$cb_data" | jq -r '.state' 2>/dev/null || echo "UNKNOWN")
            local failure_rate=$(echo "$cb_data" | jq -r '.failureRate' 2>/dev/null || echo "0")
            local buffered_calls=$(echo "$cb_data" | jq -r '.numberOfBufferedCalls' 2>/dev/null || echo "0")
            local failed_calls=$(echo "$cb_data" | jq -r '.numberOfFailedCalls' 2>/dev/null || echo "0")
            local successful_calls=$(echo "$cb_data" | jq -r '.numberOfSuccessfulCalls' 2>/dev/null || echo "0")
            
            echo "$timestamp,$state,$failure_rate,$buffered_calls,$failed_calls,$successful_calls" >> "$monitor_file"
            echo -e "${BLUE}[$timestamp] State: $state, Failure Rate: $failure_rate%${NC}"
        fi
        
        sleep 5
    done
    
    echo -e "${GREEN}✅ Circuit breaker monitoring completed${NC}"
    echo "Monitor data: $monitor_file"
    echo ""
}

# Function to run normal load test
run_normal_test() {
    echo -e "${GREEN}🟢 NORMAL LOAD TEST${NC}"
    echo "Testing normal application behavior with circuit breaker enabled"
    echo ""
    
    # Start monitoring in background
    monitor_circuit_breaker "normal" 300 &
    local monitor_pid=$!
    
    # Run test
    run_jmeter_test "normal" 50 30 300 "Normal load testing with 50 users over 5 minutes"
    
    # Wait for monitoring to complete
    wait $monitor_pid
}

# Function to run stress test
run_stress_test() {
    echo -e "${YELLOW}🟡 STRESS TEST${NC}"
    echo "Testing circuit breaker under high load"
    echo ""
    
    # Start monitoring in background
    monitor_circuit_breaker "stress" 600 &
    local monitor_pid=$!
    
    # Run test
    run_jmeter_test "stress" 200 60 600 "Stress testing with 200 users over 10 minutes"
    
    # Wait for monitoring to complete
    wait $monitor_pid
}

# Function to run failure simulation test
run_failure_test() {
    echo -e "${RED}🔴 FAILURE SIMULATION TEST${NC}"
    echo "This test requires manual intervention:"
    echo "1. Start the test"
    echo "2. Stop downstream service during test"
    echo "3. Observe circuit breaker opening"
    echo "4. Restart downstream service"
    echo "5. Observe circuit breaker recovery"
    echo ""
    
    read -p "Press Enter to start failure simulation test..."
    
    # Start monitoring in background
    monitor_circuit_breaker "failure" 600 &
    local monitor_pid=$!
    
    # Run test
    run_jmeter_test "failure" 100 30 600 "Failure simulation with manual downstream service control"
    
    # Wait for monitoring to complete
    wait $monitor_pid
}

# Function to generate summary report
generate_summary() {
    echo -e "${BLUE}📋 GENERATING SUMMARY REPORT${NC}"
    echo "==============================="
    
    local summary_file="$RESULTS_DIR/test-summary.md"
    
    cat > "$summary_file" << EOF
# Circuit Breaker Test Summary

**Test Date**: $(date)
**Application**: Hotels-ClientGateway
**Circuit Breaker**: mob-landing

## Test Results

EOF
    
    for test_dir in "$RESULTS_DIR"/*; do
        if [ -d "$test_dir" ] && [ -f "$test_dir/results.jtl" ]; then
            local test_name=$(basename "$test_dir")
            echo "### $test_name Test" >> "$summary_file"
            echo "" >> "$summary_file"
            
            # Extract key metrics from JTL file
            if command -v awk >/dev/null 2>&1; then
                local total_samples=$(awk -F',' 'NR>1 {count++} END {print count+0}' "$test_dir/results.jtl")
                local avg_response_time=$(awk -F',' 'NR>1 {sum+=$2; count++} END {print (count>0 ? sum/count : 0)}' "$test_dir/results.jtl")
                local error_rate=$(awk -F',' 'NR>1 {if($8=="false") errors++; total++} END {print (total>0 ? (errors/total)*100 : 0)}' "$test_dir/results.jtl")
                
                echo "- **Total Samples**: $total_samples" >> "$summary_file"
                echo "- **Average Response Time**: ${avg_response_time}ms" >> "$summary_file"
                echo "- **Error Rate**: ${error_rate}%" >> "$summary_file"
                echo "- **Report**: [View Report]($test_dir/report/index.html)" >> "$summary_file"
                echo "" >> "$summary_file"
            fi
        fi
    done
    
    echo -e "${GREEN}✅ Summary report generated: $summary_file${NC}"
}

# Main execution
main() {
    # Pre-flight checks
    check_application
    check_circuit_breaker
    
    # Run tests based on type
    case $TEST_TYPE in
        "normal")
            run_normal_test
            ;;
        "stress")
            run_stress_test
            ;;
        "failure")
            run_failure_test
            ;;
        "all")
            run_normal_test
            sleep 30  # Cool down period
            run_stress_test
            sleep 30  # Cool down period
            echo -e "${YELLOW}⚠️  Manual failure test skipped in 'all' mode${NC}"
            echo "Run './run-circuit-breaker-tests.sh failure' for failure simulation"
            ;;
        *)
            echo -e "${RED}❌ Invalid test type: $TEST_TYPE${NC}"
            echo "Valid options: normal, stress, failure, all"
            exit 1
            ;;
    esac
    
    # Generate summary
    generate_summary
    
    echo -e "${GREEN}🎉 Circuit Breaker Testing Complete!${NC}"
    echo "Check results in: $RESULTS_DIR/"
}

# Run main function
main
