version: '3.3'
services:
  mmt-clientbackend:
    image: "524881529748.dkr.ecr.ap-south-1.amazonaws.com/hotels-clientgateway:${TAG}"
    container_name: hotels-clientgateway
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/clientbackend/cg/healthcheck"]
      interval: 100s
      timeout: 50s
      retries: 5
    environment:
    - CONSUL_TOKEN=${CONSUL_QA_TOKEN}
    volumes:
     - /opt/logs/hotels-clientgateway:/opt/logs/tomcat
     - "/root/.aws/:/root/.aws/"
    ports:
     - "9200:8080"

