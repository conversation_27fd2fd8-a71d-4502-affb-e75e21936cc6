{"total": 1, "hotelRates": [{"isExtraAdultChild": false, "requestToBook": true, "isRTBRatePlanPreApproved": true, "roomInfo": {"13490": {"roomCode": "13490", "roomMmtId": "13490", "roomSize": "3500", "roomSizeUnit": "sq.ft", "roomViewSeq": "6", "roomViewName": "Garden View", "roomViewDescription": "Garden View", "roomName": "Hostie | Pulkavali 4 BHK Farm Villa", "bedType": "King Bed", "bedRoomCount": "4", "roomViews": [{"sequence": 6, "name": "Garden View", "description": "Garden View", "code": "gv"}], "bedInfoText": "1 Double Bed,1 King Bed,1 Queen Bed,6 Sofa Cum Beds", "beds": [{"type": "King Bed", "count": 1}, {"type": "Queen Bed", "count": 1}, {"type": "Double Bed", "count": 1}, {"type": "Sofa Cum Bed", "count": 6}], "maxGuestCount": 12, "roomAttributes": {"sellableType": "room"}, "bedCount": 9, "subRoomCount": 0, "parentRoomCode": "", "maxAdultCount": 12, "maxChildCount": 11, "propertyCount": 0, "roomSummary": {"ratingCount": 2, "reviewCount": 0, "topRated": true, "disableLowRating": false}, "privateSpaces": {"descriptive": ["1 x Kitchen", "4 x Bathroom", "1 x Swimming Pool", "1 x Living Room", "4 x Bedroom", "1 x Parking"], "spaces": [{"media": [{"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/f7363c5651e911e98acb0242ac110002.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/f724b2ec51e911e98a880242ac110002.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/03bf05a251ea11e98a880242ac110002.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/05c27d5251ea11e9b0e20242ac110002.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/107ac9f251ea11e9a61f0242ac110003.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/870abc68297411eba5230242ac110006.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/b05a8e0e297411eb8de40242ac110002.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/f2c43440897011eca7f20a58a9feac02.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/100a50ac897111eca7f20a58a9feac02.jpg"}], "name": "Bedroom 1", "subText": "Sleeps 4 guests", "descriptionText": "2 x Sofa Cum Bed, Double Bed, Swimming Pool View, Attached Bathroom", "areaText": "350 sq.ft", "openCardText": "Fan, Power backup, Luggage assistance, Private Entrance", "baseOccupancy": 2, "maxOccupancy": 4, "finalOccupancy": 0, "spaceType": "bedroom"}, {"media": [{"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/45ee424e51ea11e9a4d10242ac110003.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/4dfa3be651ea11e9a6880242ac110003.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/afc3a4ee297411eba5230242ac110006.png"}], "name": "Bathroom 1", "descriptionText": "With Bedroom 1", "areaText": "80 sq.ft", "openCardText": "Geyser/ Water heater, Western Toilet Seat, Shower cubicle, Dustbins, Toilet Papers, Hot & Cold Water, Shower, Toiletries, Bidet, Towels", "baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "spaceType": "bathroom"}, {"media": [{"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/809bf1f251ea11e9854f0242ac110003.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/8afe009a51ea11e9b3430242ac110002.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/8ddc891251ea11e984f50242ac110003.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/9900414e51ea11e9854f0242ac110003.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/9d22e8b251ea11e98f1d0242ac110002.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/af5ad130297411eb8ee30242ac110006.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/afe409f0297411ebaf210242ac110002.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/106abff0897111ec93030a58a9feac02.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/42e533f2897111eca48a0a58a9feac02.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/7e9e88eaed7a11ec922f0a58a9feac02.png"}], "name": "Bedroom 2", "subText": "Sleeps 4 guests", "descriptionText": "2 x Sofa Cum Bed, King Bed, Swimming Pool View, Attached Bathroom, Attached Balcony", "areaText": "350 sq.ft", "openCardText": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Power backup, Luggage assistance, Private Entrance, Vera<PERSON><PERSON>", "baseOccupancy": 2, "maxOccupancy": 4, "finalOccupancy": 0, "spaceType": "bedroom"}, {"media": [{"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/36f46a8e51ea11e9a4d10242ac110003.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/3ef7b57e51ea11e98acb0242ac110002.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/95257a2c297411ebaf210242ac110002.png"}], "name": "Bathroom 2", "descriptionText": "With Bedroom 2", "areaText": "80 sq.ft", "openCardText": "Bathtub, Geyser/ Water heater, Western Toilet Seat, Dustbins, Toilet Papers, Hot & Cold Water, Toiletries, Shower, Bidet, Towels", "baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "spaceType": "bathroom"}, {"media": [{"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/13c70b5c51ea11e9b5120242ac110002.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/1ebb088851ea11e9b1860242ac110002.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/21e65d3c51ea11e99ee20242ac110003.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/2cd6318651ea11e9b5120242ac110002.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/7b7a3d2851ea11e9804b0242ac110002.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/c91d10ce297411eba5230242ac110006.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/d53e57aa297411eba1950242ac110002.png"}], "name": "Bedroom 3", "subText": "Sleeps 2 guests", "descriptionText": "Queen Bed, Swimming Pool View, Attached Bathroom, Attached Balcony", "areaText": "120 sq.ft", "openCardText": "Power backup, Luggage assistance, Private Entrance, Verandah", "baseOccupancy": 2, "maxOccupancy": 2, "finalOccupancy": 0, "spaceType": "bedroom"}, {"media": [{"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/557a3ace51ea11e98a9e0242ac110003.jpg"}], "name": "Bathroom 3", "descriptionText": "With Bedroom 3", "areaText": "80 sq.ft", "openCardText": "Geyser/ Water heater, Western Toilet Seat, Dustbins, Hot & Cold Water, Shower, Toiletries, Bidet, Towels", "baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "spaceType": "bathroom"}, {"media": [{"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/649882cc51ea11e982a60242ac110003.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/6ffe5a2451ea11e984f50242ac110003.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/7212eabe51ea11e9b74f0242ac110002.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/c97329c8297411ebb7740242ac110002.png"}], "name": "Bedroom 4", "subText": "Sleeps 2 guests", "descriptionText": "2 x Sofa Cum Bed, Swimming Pool View, Attached Bathroom, Attached Balcony", "areaText": "100 sq.ft", "openCardText": "Fan, Luggage assistance, Verandah", "baseOccupancy": 2, "maxOccupancy": 2, "finalOccupancy": 0, "spaceType": "bedroom"}, {"media": [{"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/557a3ace51ea11e98a9e0242ac110003.jpg"}], "name": "Bathroom 4", "descriptionText": "With Bedroom 4", "areaText": "80 sq.ft", "openCardText": "Geyser/ Water heater, Western Toilet Seat, Dustbins, Toilet Papers, Hot & Cold Water, Shower, Toiletries, Bidet, Towels", "baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "spaceType": "bathroom"}, {"media": [{"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/440d833228a711ebba6d0242ac110002.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/86bd6562297411eb826b0242ac110006.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/86d1f4d2297411eb8ee30242ac110006.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/86cb3908297411eb8de40242ac110002.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/96785430297411eb8de40242ac110002.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/afcab09a297411eb826b0242ac110006.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/bb89662e297411eba5230242ac110006.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/bbd26504297411eb826b0242ac110006.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/bc5fd970297411eb917c0242ac110002.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/5d681e74897111eca7f20a58a9feac02.jpg"}], "name": "Living Room 1", "subText": "Sleeps 0 guest", "descriptionText": "Swimming Pool View, Attached Bathroom, Attached Balcony", "areaText": "500 sq.ft", "openCardText": "Fan, Power backup, Caretaker, Fire extinguishers, TV Area, Broadband, Sanitizers installed, Game Room, Library", "baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "spaceType": "living_room"}, {"media": [{"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/86e032ae297411ebb7740242ac110002.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/95436d52297411eba9d40242ac110006.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/bcf53768297411ebbfbb0242ac110006.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/c98a7baa297411eba9d40242ac110006.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/c9b8dd1a297411ebb0400242ac110006.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/ca40e156297411eba1950242ac110002.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/d59917bc297411ebab2f0242ac110002.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/d57a175e297411eb826b0242ac110006.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/3e7a8a42897111eca7f20a58a9feac02.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/3e9545c6897111ecae540a58a9feac02.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/55c868d6897111ec9b940a58a9feac02.jpeg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/7e883e50ed7a11eca7bc0a58a9feac02.png"}], "name": "Swimming Pool", "descriptionText": "Pool is available between 3 PM and 9 PM, >100 Sq feet (6-8 People)", "openCardText": "<PERSON><PERSON><PERSON>", "baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "spaceType": "swimming_pool"}, {"media": [{"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/a5a61d780cbd11e9b4d90242ac110002.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/a72e6cd60cbd11e993bb0242ac110003.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/cdea2f44897011ec8b770a58a9feac02.jpg"}], "name": "Kitchen", "descriptionText": "Private Kitchen where you can cook your meal", "openCardText": "Tableware, Stove", "baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "spaceType": "kitchen"}, {"media": [{"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/95c5b3c0297411ebb0400242ac110006.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/bcaf5112297411ebab2f0242ac110002.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/d59917bc297411ebab2f0242ac110002.png"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/43341fda897111ec9b940a58a9feac02.jpg"}, {"mediaType": "IMAGE", "url": "//r1imghtlak.mmtcdn.com/7aaea93a897111ec9e9d0a58a9feac02.jpg"}], "name": "Parking", "descriptionText": "Onsite Parking is available, Parking available for free, Parking is available for 4 Cars", "openCardText": "Onsite Parking is available, Parking is available for 4 Cars, Parking available for free", "baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "spaceType": "parking"}]}, "master": false}}, "showAddOn": true, "currencyCode": "inr", "cityName": "<PERSON><PERSON><PERSON>", "cityCode": "CTRHR", "countryCode": "IN", "name": "Test Hotel -Do Not Book", "id": "39494046805073279", "gstin": "29ABCDE1234Q1Z6", "addressLines": ["#68,  1st Floor, Midway City Inn, Opp. Sambhram College. MS Palyam. Near Jalahslli East", "<PERSON><PERSON>"], "checkInTime": "12 PM", "checkOutTime": "11 AM", "isPAHTariffAvailable": false, "isPAHAvailable": false, "mtKey": "N$$s9rHC9RN7n8YlRmWq2QHQJVHKO2G9MMzOvhKAnt82FGE5EeRoSWvU%2BziZccuyMaL6yi6Ux1fZE0pb2hogkLzESI%2FnA3ELgoTgmaWDsuA%2BSBniVA2Yucxg7p8JKkWFsb%2FiwQi6dXJ7Rn5qplX7foPpg7opKrldZ1S6spPB%2B%2Fxx2iV%2FQv4F8vRt%2FUC72jZwK8WXH7KOLV%2F49rFxXqev4y0quZHQX8%2FEeeLdiKOdtzXaHlEc028meORAP9B5sTFfRN1eTOcXQK0sl%2Fj%2BQ%2FaRjkEBUrA3E98A9aMhonQ7xd4BTKV%2FqldjXOxZoJX%2BAr1r%2B5Q6m7bH6NjP0Xu05cM0%2FM8sSNvtNoqJAaUkEcp7mB09NE%3D", "soldOut": false, "referenceKey": "cdccc1f6-da35-4d6b-9559-3def4b058b63", "mandatoryCharges": [{"name": "Mandatory Gala Meals payable at hotel", "description": "Occasion - Republic Day; Price Per Adult(inclusive of taxes) - 50; Price Per Child(inclusive of taxes) - 25; Type of Meal - Dinner", "refundable": false, "mandatory": true, "applicableDays": ["2020-12-24"], "subCategory": "Additional Pay at hotel charges", "leafCategory": "Gala Meals", "imageURL": null, "tags": null, "category": null, "currency": "INR", "amount": 852.48, "type": null, "price": {"defaultPrice": null, "perStayRoom": null, "perStayAdult": 106.542, "perStayChild": 79.94, "perStayInfant": null, "perNightRoom": null, "perNightAdult": null, "perNightChild": null, "perNightInfant": null}, "totalAdults": 5, "totalChild": 4, "totalRooms": 4, "applicableDaysCount": 1}, {"name": "Mandatory Tax payable at hotel", "description": "Price Per Room Night(inclusive of taxes) - 10; Type of Tax - Green Tax", "refundable": false, "mandatory": true, "applicableDays": ["2020-12-24", "2020-12-25", "2020-12-26"], "subCategory": "Additional Pay at hotel charges", "leafCategory": "Tax", "imageURL": null, "tags": null, "category": null, "currency": "INR", "amount": 340.99, "type": null, "price": {"defaultPrice": null, "perStayRoom": null, "perStayAdult": null, "perStayChild": null, "perStayInfant": null, "perNightRoom": 28.414999999999996, "perNightAdult": null, "perNightChild": null, "perNightInfant": null}, "totalAdults": 5, "totalChild": 4, "totalRooms": 4, "applicableDaysCount": 3}], "notices": [{"name": "Important Information", "description": "Description - Single occupancy strictly not allowed", "subCategory": "Other restrictions", "leafCategory": "Important Information"}, {"name": null, "description": "There is no front desk at this property. To make arrangements for check-in please contact the property at least 24 hours prior to arrival hours before arrival using the information on the booking confirmation. This property offers a breakfast bag (surcharge) delivered to the guestroom. For more details, please contact the property using the information on the reservation confirmation received after booking", "subCategory": null, "leafCategory": null}, {"name": "Mandatory Security Deposit payable at hotel", "description": "Price Per Adult(inclusive of taxes) - 100; Type of deposit - refundable", "subCategory": "Additional Pay at hotel charges", "leafCategory": "Security Deposit"}], "alerts": [{"mismatchType": "PRICE", "expectedValue": "123", "actualValue": "120"}, {"mismatchType": "CANCELLATION", "rpcc": "990000551070:MSE:1120:MSE:INGO:MSE:1$MSE$2$MSE$0"}, {"mismatchType": "MEALPLAN", "rpcc": "990000551070:MSE:1120:MSE:INGO:MSE:1$MSE$2$MSE$0"}], "roomTypeDetails": {"roomType": {"2668994": {"ratePlanList": {"990000551070:MSE:1120:MSE:INGO:MSE:1$MSE$2$MSE$0": {"checkinPolicy": {"mostRestrictive": "Y", "shortDescription": "checkin before 12 pm"}, "confirmationPolicy": {"mostRestrictive": "Y", "shortDescription": "booking confirmation in 24 hrs"}, "inclusions": [], "mealPlans": [{"code": "EP", "value": "Room Only"}], "availDetails": {"status": "B", "count": 2000, "numOfRooms": 1, "occupancyDetails": {"adult": 1, "child": 2, "infant": 0, "numOfRooms": 1, "childAges": null, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "paymentDetails": {"paymentMode": "PAS", "cardChargePolicy": null, "originalAPPayMode": "PAS"}, "cancelPenaltyList": [{"cancellationType": "FREE_CANCELLATON", "cancelPenaltiesRule": null, "penaltyDescription": {"name": null, "description": "From booking date to 2020-12-24 12:00:00,100% penalty will be charged.In case of no show : no refund.Booking cannot be cancelled/modified on or after the check in date and time mentioned in the Hotel Confirmation Voucher. All time mentioned above is in destination time."}, "tillDate": "25-Jul-2020 17:00", "sponsorer": null, "freeCancellationText": "Free Cancellation before 25-Jul-2020 17:00", "mostRestrictive": "Y"}], "displayFare": {"tax": {"value": 64.3}, "slashedPrice": {"value": 1178.9, "maxFraction": 0.0, "avgeragePriceNoTax": 589.45, "averagePriceWithTax": 621.6, "sellingPriceNoTax": 1178.9, "sellingPriceWithTax": 1243.2}, "actualPrice": {"value": 1633.9, "maxFraction": 0.0, "avgeragePriceNoTax": 816.95, "averagePriceWithTax": 849.1, "sellingPriceNoTax": 1633.9, "sellingPriceWithTax": 1698.2}, "extraAdult": {"value": 0.0}, "totalTariff": {"grandTotal": 1243.2, "subTotal": 1633.9, "discount": 455.0, "taxes": 64.3, "extraPax": 0.0, "ddmu": 0.0}, "ddmu": 0.0, "totalRoomCount": 1, "taxDetails": {"hotelTax": 107.18, "markup": 64.3, "serviceTaxOnMarkup": 0.0, "taxExcluded": 0.0, "actualMarkup": 64.3}, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 1243.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 1698.0, "savingPerc": 27.0, "totalSaving": 455.0, "basePrice": 1527.0, "hotelTax": 107.0, "hotelServiceCharge": 107.0, "mmtServiceCharge": 64.0, "mmtDiscount": 455.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n8fHLOY676NKgdsfryZqkOKz2omYnjh9adTilCFeEQesVZG5RBbExR1EKoVMUJ16gBbLRY3bPDo+KKldxtZXn5JsJGWR/+3zJ2qYBWacwbzcEcd3/dgBuq2W/MXAoRnIhLQGzA50JEVBhu3UZgh2AwZHhf82CKM8IQ+6Du3afmE8OwIFwq//b6YfoaJNM8pND+qfhKgbE2nBl0uX8NdlglJl68qY5XViLy1jZRcuaj4I7u5TpVKFQaN1uS/fHc7a9NdQlwlzTxGf9u14BqbOV3a", "pricingDivisor": 1, "totalTax": 171.0, "effectivePrice": 1243.0, "brinInclusivePrice": 0.0, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": true}, "conversionFactor": 1.0, "hotelTax": 0.0, "hotelierServiceCharge": 107.18, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "skuratePolicyInfo": {"penaltyAmount": 0.0, "refundable": false, "cloneable": false}}, "supplierDetails": {"supplierCode": "INGO", "costPrice": 1609.52, "hotelierCurrencyCode": "INR", "dmcRatePlanCode": "990000551070", "accessCode": null}, "roomTariff": [{"perNightPrice": 816.95, "extraPrice": 0.0, "taxes": 64.3, "totalPricePerRoom": 1633.9, "numberOfAdults": 1, "numberOfChildren": 2, "roomDiscountPerNight": 227.5, "roomDiscountTax": 0.0, "extraChildFarePerNight": 0.0, "startDate": "2020-12-24", "endDate": "2020-12-26", "roomNumber": "1", "hotelierServiceCharge": 107.18, "childAges": null}], "ratePlanType": "ONLINE", "mtKey": "N$$s9rHC9RN7n8oNCZ1Ux2quNN%2FwJcGr8J%2FXYvB7Lf3EgdoKvf28Eao9%2BfukX9G3x8Bst7NBw6%2B0fKNqITv8bV3RbeQdPr%2FEBCxV%2FI3fd5PGt2gip1reX3U8S%2B6fLpTMvKy9O1spoAPK396%2FPi0DfcGvYVqb0TleT6oaWZu%2B7WhYGI6tCe0vOZb10EgTqqtcIwneWibFDw4iMurjr%2B5ki1at6MPkG%2FCZ7sUflenf4%2Ba%2FCCxoZn255MpbY6bIkpaq2XvYMHrZq3krnWnX8GC%2F9FR5MTZszfs8qIqIvgDUCiK7c1XsVGUd5aPI86VGbrvV9HKetHymLILxEXPrWr%2FA0dH1giPi9u2rMXlGr4qor69Yug%3D", "promotionsList": ["5111031827,5111031827,", null, null], "apAppliedPromotions": [{"promoLevel": "apLevel", "promoType": "Mark-Up", "promotionConversionFactor": 1.0, "amount": 64.3}], "ratePlanDesc": "<PERSON><PERSON><PERSON><PERSON>", "checkCancellationPolicy": false, "checkInclusions": false, "segmentId": "1120", "specialInstructionList": [{"instructionType": "AdditionalFee", "instructionDescription": "These charge(s) are additional mandatory fees to be paid in the local currency of the hotel as per the destination: null: 267.93 inr, null: 300.0 inr, null: 107.17 inr, null: 300.0 inr."}, {"instructionType": null, "instructionDescription": "Description - Single occupancy strictly not allowed"}, {"instructionType": null, "instructionDescription": "There is no front desk at this property. To make arrangements for check-in please contact the property at least 24 hours prior to arrival hours before arrival using the information on the booking confirmation. This property offers a breakfast bag (surcharge) delivered to the guestroom. For more details, please contact the property using the information on the reservation confirmation received after booking"}], "inclusionAndPolicyAvailVar": true, "suppressPas": false, "suppressed": false, "roomPolicies": [{"description": "Photo ID: All guests including children staying at the hotel have to produce a valid Photo ID such as Drivers Licence / Adhaar Card / Passport or School ID at the reception before Check In and allow it to be copied. Guests without valid photo ID will not be allowed to Check In. Local ID's not acceptable, Guests holding foreign passport have to compulsorily fill form C at the reception and submit a copy of their passport along with the valid visa at the time of Check In. It is mandatory that the name holder of the reservation (primary guest) shall be checking in at the hotel along with other guests and must be at least 18 years of age. The standard check-in time is 12:00 PM and the standard check-out time is 11:00 AM.Early check-in or late check-out is strictly subjected to availability and may be chargeable by the hotel.Any early check-in or late check-out request must be directed and reconfirmed with hotel and may be chargeable by the hotel directly", "type": "CHECK_IN"}], "additionalFees": [{"name": "Mandatory Gala Meals payable at hotel", "description": "Occasion - Republic Day; Price Per Adult(inclusive of taxes) - 50; Price Per Child(inclusive of taxes) - 25; Type of Meal - Dinner", "refundable": false, "mandatory": true, "applicableDays": ["2020-12-24"], "subCategory": "Additional Pay at hotel charges", "leafCategory": "Gala Meals", "imageURL": null, "tags": null, "category": null, "currency": "INR", "amount": 267.93, "type": null, "price": {"defaultPrice": null, "perStayRoom": null, "perStayAdult": 89.31, "perStayChild": 89.31, "perStayInfant": null, "perNightRoom": null, "perNightAdult": null, "perNightChild": null, "perNightInfant": null}, "totalAdults": 0, "totalChild": 0, "totalRooms": 0, "applicableDaysCount": 0}, {"name": "Mandatory Security Deposit payable at hotel", "description": "Price Per Adult(inclusive of taxes) - 100; Type of deposit - refundable", "refundable": true, "mandatory": true, "applicableDays": ["2020-12-24", "2020-12-25", "2020-12-26"], "subCategory": "Additional Pay at hotel charges", "leafCategory": "Security Deposit", "imageURL": null, "tags": null, "category": null, "currency": "INR", "amount": 300.0, "type": null, "price": {"defaultPrice": null, "perStayRoom": null, "perStayAdult": null, "perStayChild": null, "perStayInfant": null, "perNightRoom": null, "perNightAdult": 100.0, "perNightChild": null, "perNightInfant": null}, "totalAdults": 0, "totalChild": 0, "totalRooms": 0, "applicableDaysCount": 0}, {"name": "Mandatory Tax payable at hotel", "description": "Price Per Room Night(inclusive of taxes) - 10; Type of Tax - Green Tax", "refundable": false, "mandatory": true, "applicableDays": ["2020-12-24", "2020-12-25", "2020-12-26"], "subCategory": "Additional Pay at hotel charges", "leafCategory": "Tax", "imageURL": null, "tags": null, "category": null, "currency": "INR", "amount": 107.17, "type": null, "price": {"defaultPrice": null, "perStayRoom": null, "perStayAdult": null, "perStayChild": null, "perStayInfant": null, "perNightRoom": 35.72, "perNightAdult": null, "perNightChild": null, "perNightInfant": null}, "totalAdults": 0, "totalChild": 0, "totalRooms": 0, "applicableDaysCount": 0}, {"name": "Other Charges payable at hotel", "description": "Description - Mandatory charges Test; Price Per Room Night(inclusive of taxes) - 100", "refundable": false, "mandatory": false, "applicableDays": ["2020-12-24", "2020-12-25", "2020-12-26"], "subCategory": "Additional Pay at hotel charges", "leafCategory": "Other Charges", "imageURL": null, "tags": null, "category": null, "currency": "INR", "amount": 300.0, "type": null, "price": {"defaultPrice": null, "perStayRoom": null, "perStayAdult": null, "perStayChild": null, "perStayInfant": null, "perNightRoom": 100.0, "perNightAdult": null, "perNightChild": null, "perNightInfant": null}, "totalAdults": 0, "totalChild": 0, "totalRooms": 0, "applicableDaysCount": 0}], "notices": [{"name": "Important Information", "description": "Description - Single occupancy strictly not allowed", "subCategory": "Other restrictions", "leafCategory": "Important Information"}, {"name": null, "description": "There is no front desk at this property. To make arrangements for check-in please contact the property at least 24 hours prior to arrival hours before arrival using the information on the booking confirmation. This property offers a breakfast bag (surcharge) delivered to the guestroom. For more details, please contact the property using the information on the reservation confirmation received after booking", "subCategory": null, "leafCategory": null}, {"name": "Mandatory Security Deposit payable at hotel", "description": "Price Per Adult(inclusive of taxes) - 100; Type of deposit - refundable", "subCategory": "Additional Pay at hotel charges", "leafCategory": "Security Deposit"}], "bnplExtended": false, "pahx": false, "rpcc": "990000551070", "groupModifiedRatePlanCode": "990000551070:MSE:1120:MSE:INGO", "netRate": false, "droolDiscountApplied": false, "instantConfirmation": "NOT_APPLICABLE", "guarantee": false, "forkedRatePlan": false, "corpRate": false, "bnplApplicable": false, "corporateCdfSkip": false, "staycationDeal": false}, "990001593952:MSE:1120:MSE:INGO:MSE:2$MSE$2$MSE$0": {"inclusions": [], "mealPlans": [{"code": "AP", "value": "All meals - Breakfast, Lunch & Dinner"}], "availDetails": {"status": "B", "count": 2000, "numOfRooms": 1, "occupancyDetails": {"adult": 2, "child": 2, "infant": 0, "numOfRooms": 1, "childAges": null, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "paymentDetails": {"paymentMode": "PAS", "cardChargePolicy": null, "originalAPPayMode": "PAS"}, "cancelPenaltyList": [{"cancellationType": "FREE_CANCELLATON", "cancelPenaltiesRule": null, "penaltyDescription": {"name": null, "description": "From booking date to 2020-12-24 12:00:00,100% penalty will be charged.In case of no show : no refund.Booking cannot be cancelled/modified on or after the check in date and time mentioned in the Hotel Confirmation Voucher. All time mentioned above is in destination time."}, "tillDate": "25-Jul-2020 17:00", "sponsorer": null, "freeCancellationText": "Free Cancellation before 25-Jul-2020 17:00", "mostRestrictive": "N"}], "displayFare": {"tax": {"value": 67.75}, "slashedPrice": {"value": 1242.1, "maxFraction": 0.0, "avgeragePriceNoTax": 621.05, "averagePriceWithTax": 654.93, "sellingPriceNoTax": 1242.1, "sellingPriceWithTax": 1309.85}, "actualPrice": {"value": 1595.6, "maxFraction": 0.0, "avgeragePriceNoTax": 797.8, "averagePriceWithTax": 831.68, "sellingPriceNoTax": 1595.6, "sellingPriceWithTax": 1663.35}, "extraAdult": {"value": 0.0}, "totalTariff": {"grandTotal": 1309.85, "subTotal": 1595.6, "discount": 353.5, "taxes": 67.75, "extraPax": 0.0, "ddmu": 0.0}, "ddmu": 0.0, "totalRoomCount": 1, "taxDetails": {"hotelTax": 112.92, "markup": 67.75, "serviceTaxOnMarkup": 0.0, "taxExcluded": 0.0, "actualMarkup": 67.75}, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 1310.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 1663.0, "savingPerc": 21.0, "totalSaving": 354.0, "basePrice": 1483.0, "hotelTax": 113.0, "hotelServiceCharge": 113.0, "mmtServiceCharge": 68.0, "mmtDiscount": 354.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n+QKf54QtYD3diJbc9wCX0M7y+WIuyC62iUG2BqBYfV5yMLcZL94kQARJDORWWYSP07rNT/mCYHgFthcB3wwsDVIQNGSWoZlGU2UrSAm1Ii9hr8QFFx8JQV/xP+1wkBlis0R5saQGM0r2eW5otkBSWaxCPLD6gVP3nuOFP8jYJxGJy81C7p4x8JCeW5qISkKYGWhXFPZvcsX68BgJfm9ZUKT5ZL+OiiIISPduBrBP2HanbSBwovx/eC2LTfNr5bkIVDAxchFspjwaXu6i9vguxo", "pricingDivisor": 1, "totalTax": 181.0, "effectivePrice": 1310.0, "brinInclusivePrice": 0.0, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": true}, "conversionFactor": 1.0, "hotelTax": 0.0, "hotelierServiceCharge": 112.92, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "skuratePolicyInfo": {"penaltyAmount": 0.0, "refundable": false, "cloneable": false}}, "supplierDetails": {"supplierCode": "INGO", "costPrice": 1441.46, "hotelierCurrencyCode": "INR", "dmcRatePlanCode": "990001593952", "accessCode": null}, "roomTariff": [{"perNightPrice": 797.8, "extraPrice": 0.0, "taxes": 67.75, "totalPricePerRoom": 1595.6, "numberOfAdults": 2, "numberOfChildren": 2, "roomDiscountPerNight": 176.75, "roomDiscountTax": 0.0, "extraChildFarePerNight": 0.0, "startDate": "2020-12-24", "endDate": "2020-12-26", "roomNumber": "1", "hotelierServiceCharge": 112.92, "childAges": null}], "ratePlanType": "ONLINE", "mtKey": "N$$s9rHC9RN7n8oNCZ1Ux2quPCfbpZWKnqEQ%2B3SX43A5aLUAGJtV8YJRMGsVb3V8UgJUJ0WzWwqRYhYADF4ynXm9dDZWNrmdgMNzhPYQEVUpKaIEwlXV7Iy3VnITOFAEcWs8U9mR4VLmlUNiKuluqlOBAFDIn9DNPJazKtaorl0tg6%2FsTcmoBgBKcGuDE3B8NPqwiBNDiIHJRVp9Dj5J8JC08Hj4BBVInLOurmWIfBlKnA0yePlZ50K3o%2BYaD0%2FJBZGaZYj5rKVGP18YF8o7rShv0ArtU7M2qa5L02hYB6ixZMEb8ZfrV3iJDxfBnC92Ui0EU6vnj9W2%2FTf256H6IASxX9D8DJd6xWflznKsIOvUCYfXnrzB98cFA%3D%3D", "promotionsList": ["5111031827,5111031827,", null, null], "apAppliedPromotions": [{"promoLevel": "apLevel", "promoType": "Mark-Up", "promotionConversionFactor": 1.0, "amount": 67.75}], "ratePlanDesc": "<PERSON><PERSON><PERSON><PERSON>", "checkCancellationPolicy": false, "checkInclusions": false, "segmentId": "1120", "specialInstructionList": [{"instructionType": "AdditionalFee", "instructionDescription": "These charge(s) are additional mandatory fees to be paid in the local currency of the hotel as per the destination: null: 282.3 inr, null: 600.0 inr, null: 112.92 inr, null: 300.0 inr."}, {"instructionType": null, "instructionDescription": "Description - Single occupancy strictly not allowed"}, {"instructionType": null, "instructionDescription": "There is no front desk at this property. To make arrangements for check-in please contact the property at least 24 hours prior to arrival hours before arrival using the information on the booking confirmation. This property offers a breakfast bag (surcharge) delivered to the guestroom. For more details, please contact the property using the information on the reservation confirmation received after booking"}], "inclusionAndPolicyAvailVar": true, "suppressPas": false, "suppressed": false, "roomPolicies": [{"description": "Photo ID: All guests including children staying at the hotel have to produce a valid Photo ID such as Drivers Licence / Adhaar Card / Passport or School ID at the reception before Check In and allow it to be copied. Guests without valid photo ID will not be allowed to Check In. Local ID's not acceptable, Guests holding foreign passport have to compulsorily fill form C at the reception and submit a copy of their passport along with the valid visa at the time of Check In. It is mandatory that the name holder of the reservation (primary guest) shall be checking in at the hotel along with other guests and must be at least 18 years of age. The standard check-in time is 12:00 PM and the standard check-out time is 11:00 AM.Early check-in or late check-out is strictly subjected to availability and may be chargeable by the hotel.Any early check-in or late check-out request must be directed and reconfirmed with hotel and may be chargeable by the hotel directly", "type": "CHECK_IN"}], "additionalFees": [{"name": "Mandatory Gala Meals payable at hotel", "description": "Occasion - Republic Day; Price Per Adult(inclusive of taxes) - 50; Price Per Child(inclusive of taxes) - 25; Type of Meal - Dinner", "refundable": false, "mandatory": true, "applicableDays": ["2020-12-24"], "subCategory": "Additional Pay at hotel charges", "leafCategory": "Gala Meals", "imageURL": null, "tags": null, "category": null, "currency": "INR", "amount": 282.3, "type": null, "price": {"defaultPrice": null, "perStayRoom": null, "perStayAdult": 70.57, "perStayChild": 70.57, "perStayInfant": null, "perNightRoom": null, "perNightAdult": null, "perNightChild": null, "perNightInfant": null}, "totalAdults": 0, "totalChild": 0, "totalRooms": 0, "applicableDaysCount": 0}, {"name": "Mandatory Security Deposit payable at hotel", "description": "Price Per Adult(inclusive of taxes) - 100; Type of deposit - refundable", "refundable": true, "mandatory": true, "applicableDays": ["2020-12-24", "2020-12-25", "2020-12-26"], "subCategory": "Additional Pay at hotel charges", "leafCategory": "Security Deposit", "imageURL": null, "tags": null, "category": null, "currency": "INR", "amount": 600.0, "type": null, "price": {"defaultPrice": null, "perStayRoom": null, "perStayAdult": null, "perStayChild": null, "perStayInfant": null, "perNightRoom": null, "perNightAdult": 100.0, "perNightChild": null, "perNightInfant": null}, "totalAdults": 0, "totalChild": 0, "totalRooms": 0, "applicableDaysCount": 0}, {"name": "Mandatory Tax payable at hotel", "description": "Price Per Room Night(inclusive of taxes) - 10; Type of Tax - Green Tax", "refundable": false, "mandatory": true, "applicableDays": ["2020-12-24", "2020-12-25", "2020-12-26"], "subCategory": "Additional Pay at hotel charges", "leafCategory": "Tax", "imageURL": null, "tags": null, "category": null, "currency": "INR", "amount": 112.92, "type": null, "price": {"defaultPrice": null, "perStayRoom": null, "perStayAdult": null, "perStayChild": null, "perStayInfant": null, "perNightRoom": 37.64, "perNightAdult": null, "perNightChild": null, "perNightInfant": null}, "totalAdults": 0, "totalChild": 0, "totalRooms": 0, "applicableDaysCount": 0}, {"name": "Other Charges payable at hotel", "description": "Description - Mandatory charges Test; Price Per Room Night(inclusive of taxes) - 100", "refundable": false, "mandatory": false, "applicableDays": ["2020-12-24", "2020-12-25", "2020-12-26"], "subCategory": "Additional Pay at hotel charges", "leafCategory": "Other Charges", "imageURL": null, "tags": null, "category": null, "currency": "INR", "amount": 300.0, "type": null, "price": {"defaultPrice": null, "perStayRoom": null, "perStayAdult": null, "perStayChild": null, "perStayInfant": null, "perNightRoom": 100.0, "perNightAdult": null, "perNightChild": null, "perNightInfant": null}, "totalAdults": 0, "totalChild": 0, "totalRooms": 0, "applicableDaysCount": 0}], "notices": [{"name": "Important Information", "description": "Description - Single occupancy strictly not allowed", "subCategory": "Other restrictions", "leafCategory": "Important Information"}, {"name": null, "description": "There is no front desk at this property. To make arrangements for check-in please contact the property at least 24 hours prior to arrival hours before arrival using the information on the booking confirmation. This property offers a breakfast bag (surcharge) delivered to the guestroom. For more details, please contact the property using the information on the reservation confirmation received after booking", "subCategory": null, "leafCategory": null}, {"name": "Mandatory Security Deposit payable at hotel", "description": "Price Per Adult(inclusive of taxes) - 100; Type of deposit - refundable", "subCategory": "Additional Pay at hotel charges", "leafCategory": "Security Deposit"}], "bnplExtended": false, "pahx": false, "rpcc": "990001593952", "groupModifiedRatePlanCode": "990001593952:MSE:1120:MSE:INGO", "netRate": false, "droolDiscountApplied": false, "instantConfirmation": "NOT_APPLICABLE", "guarantee": false, "forkedRatePlan": false, "corpRate": false, "bnplApplicable": false, "corporateCdfSkip": false, "staycationDeal": false}}, "roomTypeName": "<PERSON><PERSON><PERSON><PERSON>", "roomTypeCode": "2668994", "bedSize": null, "roomSize": null, "viewType": null, "persuasions": null, "altAccoMessage": null, "sellableType": "room"}, "2669006": {"ratePlanList": {"990001386711:MSE:1120:MSE:INGO:MSE:1$MSE$0$MSE$0": {"inclusions": [], "mealPlans": [{"code": "EP", "value": "Room Only"}], "availDetails": {"status": "B", "count": 1999, "numOfRooms": 2, "occupancyDetails": {"adult": 2, "child": 0, "infant": 0, "numOfRooms": 2, "childAges": null, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "paymentDetails": {"paymentMode": "PAS", "cardChargePolicy": null, "originalAPPayMode": "PAS"}, "cancelPenaltyList": [{"cancellationType": "FREE_CANCELLATON", "cancelPenaltiesRule": null, "penaltyDescription": {"name": null, "description": "From booking date to 2020-12-21 12:00:00,50% penalty will be charged.From 2020-12-21 12:00:00 to 2020-12-24 12:00:00,80% penalty will be charged.From 2020-12-24 12:00:00 to 2020-12-24 12:00:00,100% penalty will be charged.In case of no show : no refund.Booking cannot be cancelled/modified on or after the check in date and time mentioned in the Hotel Confirmation Voucher. All time mentioned above is in destination time."}, "tillDate": "25-Jul-2020 17:00", "sponsorer": null, "freeCancellationText": "Free Cancellation before 25-Jul-2020 17:00", "mostRestrictive": "N"}], "displayFare": {"tax": {"value": 72.54}, "slashedPrice": {"value": 1329.92, "maxFraction": 0.0, "avgeragePriceNoTax": 332.48, "averagePriceWithTax": 350.62, "sellingPriceNoTax": 1329.92, "sellingPriceWithTax": 1402.46}, "actualPrice": {"value": 1980.92, "maxFraction": 0.0, "avgeragePriceNoTax": 495.23, "averagePriceWithTax": 513.37, "sellingPriceNoTax": 1980.92, "sellingPriceWithTax": 2053.46}, "extraAdult": {"value": 0.0}, "totalTariff": {"grandTotal": 1402.46, "subTotal": 1980.92, "discount": 651.0, "taxes": 72.54, "extraPax": 0.0, "ddmu": 0.0}, "ddmu": 0.0, "totalRoomCount": 2, "taxDetails": {"hotelTax": 120.92, "markup": 72.54, "serviceTaxOnMarkup": 0.0, "taxExcluded": 0.0, "actualMarkup": 72.54}, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 1402.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 2053.0, "savingPerc": 32.0, "totalSaving": 651.0, "basePrice": 1860.0, "hotelTax": 121.0, "hotelServiceCharge": 121.0, "mmtServiceCharge": 73.0, "mmtDiscount": 651.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n8fHLOY676NKtuEboa38oegoE6tJ8wx5lH3OWoDMnv5J+oKb6nviBEEnDHT+4oZdBKtafN4Q7wRRDMNH+LScUDJKzMD/JMPDj9oErXoiTtqnt7GLZ0nyUnaLv13KUmUiy0NjLxA3/clEKWPsk3SowuyLJTnb/BWB5mjizW9t3eKqm2g0fJdDz5kWLIu2dqNmNFut1m6ic8Yk6DdKIjQOVcfEwHgUViFxPIKm0TnpABewx0OSHneM4wnS8Ri55F6HC8mLL7w1JiLmYB2DgebcHDz", "pricingDivisor": 1, "totalTax": 193.0, "effectivePrice": 1402.0, "brinInclusivePrice": 0.0, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": true}, "conversionFactor": 1.0, "hotelTax": 0.0, "hotelierServiceCharge": 120.92, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "skuratePolicyInfo": {"penaltyAmount": 0.0, "refundable": false, "cloneable": false}}, "supplierDetails": {"supplierCode": "INGO", "costPrice": 1953.4, "hotelierCurrencyCode": "INR", "dmcRatePlanCode": "990001386711", "accessCode": null}, "roomTariff": [{"perNightPrice": 495.23, "extraPrice": 0.0, "taxes": 36.27, "totalPricePerRoom": 990.46, "numberOfAdults": 1, "numberOfChildren": 0, "roomDiscountPerNight": 162.75, "roomDiscountTax": 0.0, "extraChildFarePerNight": 0.0, "startDate": "2020-12-24", "endDate": "2020-12-26", "roomNumber": "1", "hotelierServiceCharge": 60.46, "childAges": null}, {"perNightPrice": 495.23, "extraPrice": 0.0, "taxes": 36.27, "totalPricePerRoom": 990.46, "numberOfAdults": 1, "numberOfChildren": 0, "roomDiscountPerNight": 162.75, "roomDiscountTax": 0.0, "extraChildFarePerNight": 0.0, "startDate": "2020-12-24", "endDate": "2020-12-26", "roomNumber": "2", "hotelierServiceCharge": 60.46, "childAges": null}], "ratePlanType": "ONLINE", "mtKey": "N$$s9rHC9RN7n8YlRmWq2QHQJVHKO2G9MMzOvhKAnt82FGE5EeRoSWvU%2BziZccuyMaL6yi6Ux1fZE0pb2hogkLzESI%2FnA3ELgoTgmaWDsuA%2BSBniVA2Yucxg7p8JKkWFsb%2FiwQi6dXJ7Rn5qplX7foPpg7opKrldZ1S6spPB%2B%2Fxx2iV%2FQv4F8vRt%2FUC72jZwK8WXH7KOLV%2F49rFxXqev4y0quZHQX8%2FEeeLdiKOdtzXaHlEc028meORAP9B5sTFfRN1eTOcXQK0sl%2Fj%2BQ%2FaRjkEBUrA3E98A9aMhonQ7xd4BTKV%2FqldjXOxZoJX%2BAr1r%2B5Q6m7bH6NjP0Xu05cM0%2FM8sSNvtNoqJAaUkEcp7mB09NE%3D", "promotionsList": ["5111031827,5111031827,", null, null], "apAppliedPromotions": [{"promoLevel": "apLevel", "promoType": "Mark-Up", "promotionConversionFactor": 1.0, "amount": 72.54}], "ratePlanDesc": "INGOMMT TEST ROOM", "checkCancellationPolicy": false, "checkInclusions": false, "segmentId": "1120", "specialInstructionList": [{"instructionType": "AdditionalFee", "instructionDescription": "These charge(s) are additional mandatory fees to be paid in the local currency of the hotel as per the destination: null: 302.25 inr, null: 600.0 inr, null: 120.9 inr, null: 600.0 inr."}, {"instructionType": null, "instructionDescription": "Description - Single occupancy strictly not allowed"}, {"instructionType": null, "instructionDescription": "There is no front desk at this property. To make arrangements for check-in please contact the property at least 24 hours prior to arrival hours before arrival using the information on the booking confirmation. This property offers a breakfast bag (surcharge) delivered to the guestroom. For more details, please contact the property using the information on the reservation confirmation received after booking"}], "inclusionAndPolicyAvailVar": true, "suppressPas": false, "suppressed": false, "roomPolicies": [{"description": "Photo ID: All guests including children staying at the hotel have to produce a valid Photo ID such as Drivers Licence / Adhaar Card / Passport or School ID at the reception before Check In and allow it to be copied. Guests without valid photo ID will not be allowed to Check In. Local ID's not acceptable, Guests holding foreign passport have to compulsorily fill form C at the reception and submit a copy of their passport along with the valid visa at the time of Check In. It is mandatory that the name holder of the reservation (primary guest) shall be checking in at the hotel along with other guests and must be at least 18 years of age. The standard check-in time is 12:00 PM and the standard check-out time is 11:00 AM.Early check-in or late check-out is strictly subjected to availability and may be chargeable by the hotel.Any early check-in or late check-out request must be directed and reconfirmed with hotel and may be chargeable by the hotel directly", "type": "CHECK_IN"}], "additionalFees": [{"name": "Mandatory Gala Meals payable at hotel", "description": "Occasion - Republic Day; Price Per Adult(inclusive of taxes) - 50; Price Per Child(inclusive of taxes) - 25; Type of Meal - Dinner", "refundable": false, "mandatory": true, "applicableDays": ["2020-12-24"], "subCategory": "Additional Pay at hotel charges", "leafCategory": "Gala Meals", "imageURL": null, "tags": null, "category": null, "currency": "INR", "amount": 302.25, "type": null, "price": {"defaultPrice": null, "perStayRoom": null, "perStayAdult": 151.13, "perStayChild": null, "perStayInfant": null, "perNightRoom": null, "perNightAdult": null, "perNightChild": null, "perNightInfant": null}, "totalAdults": 0, "totalChild": 0, "totalRooms": 0, "applicableDaysCount": 0}, {"name": "Mandatory Security Deposit payable at hotel", "description": "Price Per Adult(inclusive of taxes) - 100; Type of deposit - refundable", "refundable": true, "mandatory": true, "applicableDays": ["2020-12-24", "2020-12-25", "2020-12-26"], "subCategory": "Additional Pay at hotel charges", "leafCategory": "Security Deposit", "imageURL": null, "tags": null, "category": null, "currency": "INR", "amount": 600.0, "type": null, "price": {"defaultPrice": null, "perStayRoom": null, "perStayAdult": null, "perStayChild": null, "perStayInfant": null, "perNightRoom": null, "perNightAdult": 100.0, "perNightChild": null, "perNightInfant": null}, "totalAdults": 0, "totalChild": 0, "totalRooms": 0, "applicableDaysCount": 0}, {"name": "Mandatory Tax payable at hotel", "description": "Price Per Room Night(inclusive of taxes) - 10; Type of Tax - Green Tax", "refundable": false, "mandatory": true, "applicableDays": ["2020-12-24", "2020-12-25", "2020-12-26"], "subCategory": "Additional Pay at hotel charges", "leafCategory": "Tax", "imageURL": null, "tags": null, "category": null, "currency": "INR", "amount": 120.9, "type": null, "price": {"defaultPrice": null, "perStayRoom": null, "perStayAdult": null, "perStayChild": null, "perStayInfant": null, "perNightRoom": 20.15, "perNightAdult": null, "perNightChild": null, "perNightInfant": null}, "totalAdults": 0, "totalChild": 0, "totalRooms": 0, "applicableDaysCount": 0}, {"name": "Other Charges payable at hotel", "description": "Description - Mandatory charges Test; Price Per Room Night(inclusive of taxes) - 100", "refundable": false, "mandatory": false, "applicableDays": ["2020-12-24", "2020-12-25", "2020-12-26"], "subCategory": "Additional Pay at hotel charges", "leafCategory": "Other Charges", "imageURL": null, "tags": null, "category": null, "currency": "INR", "amount": 600.0, "type": null, "price": {"defaultPrice": null, "perStayRoom": null, "perStayAdult": null, "perStayChild": null, "perStayInfant": null, "perNightRoom": 100.0, "perNightAdult": null, "perNightChild": null, "perNightInfant": null}, "totalAdults": 0, "totalChild": 0, "totalRooms": 0, "applicableDaysCount": 0}], "notices": [{"name": "Important Information", "description": "Description - Single occupancy strictly not allowed", "subCategory": "Other restrictions", "leafCategory": "Important Information"}, {"name": null, "description": "There is no front desk at this property. To make arrangements for check-in please contact the property at least 24 hours prior to arrival hours before arrival using the information on the booking confirmation. This property offers a breakfast bag (surcharge) delivered to the guestroom. For more details, please contact the property using the information on the reservation confirmation received after booking", "subCategory": null, "leafCategory": null}, {"name": "Mandatory Security Deposit payable at hotel", "description": "Price Per Adult(inclusive of taxes) - 100; Type of deposit - refundable", "subCategory": "Additional Pay at hotel charges", "leafCategory": "Security Deposit"}], "bnplExtended": false, "pahx": false, "rpcc": "990001386711", "groupModifiedRatePlanCode": "990001386711:MSE:1120:MSE:INGO", "netRate": false, "droolDiscountApplied": false, "instantConfirmation": "NOT_APPLICABLE", "guarantee": false, "forkedRatePlan": false, "corpRate": false, "bnplApplicable": false, "corporateCdfSkip": false, "staycationDeal": false}}, "roomTypeName": "INGOMMT TEST ROOM", "roomTypeCode": "2669006", "bedSize": null, "roomSize": null, "viewType": null, "persuasions": null, "altAccoMessage": null, "sellableType": "bed"}}, "totalDisplayFare": {"tax": {"value": 204.59}, "slashedPrice": {"value": 3750.92, "maxFraction": 0.0, "avgeragePriceNoTax": 1542.98, "averagePriceWithTax": 1627.15, "sellingPriceNoTax": 3750.92, "sellingPriceWithTax": 3955.51}, "actualPrice": {"value": 5210.42, "maxFraction": 0.0, "avgeragePriceNoTax": 2109.98, "averagePriceWithTax": 2194.15, "sellingPriceNoTax": 5210.42, "sellingPriceWithTax": 5415.01}, "bestCouponByPaymode": {"PAS": {"couponCode": "TESTCBC", "type": "DEAL_ECPN", "description": "Get inr 465.00 Cashback to Card", "discountAmount": 337.0, "doubleDiscountEnabled": true, "couponDetails": [{"moreVerificationRequired": false, "discountTypeCode": "CTC", "timeOfCredit": "Booking", "discountType": "Cashback to Card", "discount": 465.0, "cashbackAmount": 0.0, "cdfValidated": false, "ecoupon": false}, {"moreVerificationRequired": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 337.0, "cashbackAmount": 0.0, "cdfValidated": false, "ecoupon": false}], "paymentModel": "PAS", "hybridDiscounts": {"CTC": 465.0, "INSTANT": 337.0}, "tncUrl": "http://promos.makemytrip.com/mywallet-05072017.html", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0.0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "bnplAllowed": true, "autoApplicable": true}}, "otherCouponByPaymode": {"PAS": [{"couponCode": "TESTPAY1", "type": "ECOUPON", "description": "Get inr 1889.00 Off", "discountAmount": 1889.0, "doubleDiscountEnabled": true, "couponDetails": [{"moreVerificationRequired": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 1889.0, "cashbackAmount": 0.0, "cdfValidated": false, "ecoupon": false}], "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 1889.0}, "tncUrl": "http://promos.makemytrip.com/mywallet-05072017.html", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0.0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "bnplAllowed": true, "autoApplicable": true}, {"couponCode": "TESTPAY", "type": "ECOUPON", "description": "Get inr 1268.00 Off", "discountAmount": 1268.0, "doubleDiscountEnabled": true, "couponDetails": [{"moreVerificationRequired": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 1268.0, "cashbackAmount": 0.0, "cdfValidated": false, "ecoupon": false}], "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 1268.0}, "tncUrl": "http://promos.makemytrip.com/mywallet-05072017.html", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0.0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "bnplAllowed": true, "autoApplicable": true}]}, "extraAdult": {"value": 0.0}, "totalTariff": {"grandTotal": 3955.51, "subTotal": 5210.42, "discount": 1459.5, "taxes": 204.59, "extraPax": 0.0, "ddmu": 0.0}, "ddmu": 0.0, "totalRoomCount": 4, "taxDetails": {"hotelTax": 341.02, "markup": 204.59, "serviceTaxOnMarkup": 0.0, "taxExcluded": 0.0, "actualMarkup": 204.59}, "couponReceived": true, "couponSkipped": false, "ddApplied": false, "dda": "0.0", "displayPriceBreakDown": {"displayPrice": 3956.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 5415.0, "savingPerc": 33.0, "totalSaving": 1797.0, "basePrice": 4869.0, "hotelTax": 341.0, "hotelServiceCharge": 341.0, "mmtServiceCharge": 205.0, "mmtDiscount": 1460.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "couponInfo": {"couponCode": "TESTCBC", "type": "DEAL_ECPN", "description": "Get inr 465.00 Cashback to Card", "discountAmount": 337.0, "doubleDiscountEnabled": true, "paymentModel": "PAS", "hybridDiscounts": {"CTC": 465.0, "INSTANT": 337.0}, "tncUrl": "http://promos.makemytrip.com/mywallet-05072017.html", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0.0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "bnplAllowed": true, "autoApplicable": true}, "pricingKey": "s9rHC9RN7n+YGp1e+tyoI0GeNSZVewoeOQo0qPk5ZVGDM0ZPnBiDzH7SBQOhC5lD9QzXVDRmF5Pz02gIVGwyn+hLXtO2yWjpT+WkGjwqbcsbx7RRTBUrHjH4NJiv04YnwhoYbZN/6RQ+gxph9yfmR0zKPxeZkkJFPwto7vGHxiJsB4lvgpAMtK49zYftPzH5wW3vYVdFLwKein4GWVVQzeFUbK9a9AC6oVNceFwltx0D7Btr/WfE3w==", "pricingDivisor": 1, "totalTax": 546.0, "effectivePrice": 3154.0, "brinInclusivePrice": 0.0, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": true}, "conversionFactor": 1.0, "hotelTax": 0.0, "hotelierServiceCharge": 341.02, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false}, "cancellationTimeline": {"checkInDate": "24 Dec", "cancellationDate": "25 Jul", "subTitle": "Free Cancellation", "freeCancellationText": "Free Cancellation till 25 Jul 05:00 PM", "title": "STAY FLEXIBLE WITH", "bookingDate": "25 Jul"}, "staycationDeal": false}, "miscellaneous": {"htlDealCount": null, "opaqueDealCode": null, "isOpaqueAvailableAtHotel": null, "isRoomImageAvailable": null, "htlTotalPromoCount": null, "isOpaqueAvailableAtRate": null, "showECoupon": true}, "userEligiblePayMode": "PAS", "pahAuthenticationEnabled": true, "promotions": {"opaquePromotion": {"techShowOpaque": false}, "promotions": {"5111031827,5111031827,": {"description": "discount 35 percentage for b2c segment .", "code": "5111031827,5111031827,", "priceSlasher": true, "promoLevel": "tariffLevel"}}, "promotionsCount": 1}, "segments": {"segmentList": {"1120": {"id": "1120", "channelSegment": "ALL", "userSegment": "REGULAR"}}, "segmentsCount": 1}, "countryName": "India", "showFcBanner": false, "taxExcluded": false, "addOns": [{"addOnType": "ADDON", "id": "CHARITY_ID", "type": "STATIC", "category": "ADDON_CHARITY_DOB", "value": 1000, "price": 5, "alternateCurrencyPrice": 0.0, "lob": "DONATION", "title": "Donate Rs. 5 for COVID -19 Relief and Other Charity Initiatives", "validFrom": "2020-07-25", "expiry": "2020-07-27", "description": "Create an impact by donating to MakeMyTrip", "tnc": {"tncList": ["The amount received as donation will be used for the specified charitable causes. MakeMyTrip will donate the collected amount to MakeMyTrip Foundation (a public trust registered with charitable objects) or similar charitable organizations to help create a social impact "]}, "doubleRedemptionAllowed": true, "availableUnits": 1, "paymentMode": ["PAS"], "bucketId": "ADDON_CHARITY_DOB", "shortDesc": "ADDON_CHARITY_DOB", "basePrice": 1000, "imageMap": {"bigIcon": "https://promos.makemytrip.com/images/deal.png"}, "priceMap": {"adult": {"displayPrice": 5.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 5.0, "savingPerc": 0.0, "totalSaving": 0.0}}, "mobiusAddOn": false, "descriptions": [{"titleText": "Support COVID-19 relief work and safety initiatives.", "iconUrl": "https://promos.makemytrip.com/COVID/charity.png", "itemIconType": "charity_covid", "charityDescription": "Support COVID-19 relief work and safety initiatives. <a href=\"https://www.makemytrip.com/csr/covid-19-relief-efforts.html\" target=\"_blank\">Know More</a>"}, {"titleText": " Offset your carbon footprint by contributing to our green initiative.", "iconUrl": "https://imgak.mmtcdn.com/flights/assets/media/dt/review/charity_1.png?v=1", "itemIconType": "sprout_charity"}, {"titleText": "Ensure responsible tourism to restore, develop and protect heritage sites and monuments.", "iconUrl": "https://imgak.mmtcdn.com/flights/assets/media/dt/review/charity_2.png?v=1", "itemIconType": ""}], "tncUrl": "https://promos.makemytrip.com/charity-deduction-16112017.html", "hasSlot": false}], "isBNPLAvailable": false, "bnplBaseAmount": 0.0, "pahWalletApplicable": true, "bestPriceGuaranteed": false, "pahxAvailable": false, "addOnAvailableOnLowest": false, "addOnAvailable": false, "blackAccelerated": false, "preApplyCoupon": true, "specialInstructionList": [{"instructionType": "AdditionalFee", "instructionDescription": "These charge(s) are additional mandatory fees to be paid in the local currency of the hotel as per the destination: null: 267.93 inr, null: 300.0 inr, null: 107.17 inr, null: 300.0 inr."}, {"instructionType": null, "instructionDescription": "Description - Single occupancy strictly not allowed"}, {"instructionType": null, "instructionDescription": "There is no front desk at this property. To make arrangements for check-in please contact the property at least 24 hours prior to arrival hours before arrival using the information on the booking confirmation. This property offers a breakfast bag (surcharge) delivered to the guestroom. For more details, please contact the property using the information on the reservation confirmation received after booking"}], "firstTimeUser": false, "blackEligible": false, "blackEligibilityDays": 0, "panCardRequired": false, "pnAvlbl": false, "maxAdultAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0, "maxChildAtSamePrice": 0, "showOriginalPolicyToDbUsr": false, "specialRequestAvailable": {"categories": [{"code": "101", "name": "Smoking room", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": null}, {"code": "102", "name": "Late check-in", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": [{"code": "1021", "name": "CHECK-IN TIME", "type": "DROPDOWN", "placeholder": null, "values": ["04:00 PM", "05:00 PM", "06:00 PM", "07:00 PM", "08:00 PM", "09:00 PM", "10:00 PM", "11:00 PM"], "subCategories": null}]}, {"code": "103", "name": "Early check-in", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": [{"code": "1031", "name": "CHECK-IN TIME", "type": "DROPDOWN", "placeholder": null, "values": ["06:00 AM", "07:00 AM", "08:00 AM", "09:00 AM", "10:00 AM", "11:00 AM", "12:00 AM"], "subCategories": null}]}, {"code": "104", "name": "Room on a high floor", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": null}, {"code": "105", "name": "Large bed", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": null}, {"code": "106", "name": "Twin beds", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": null}, {"code": "107", "name": "Airport transfer", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": [{"code": "1071", "name": "PICKUP TIME", "type": "DROPDOWN", "placeholder": null, "values": ["12:00 AM", "01:00 AM", "02:00 AM", "03:00 AM", "04:00 AM", "05:00 AM", "06:00 AM", "07:00 AM", "08:00 AM", "09:00 AM", "10:00 AM", "11:00 AM", "12:00 PM", "01:00 PM", "02:00 PM", "03:00 PM", "04:00 PM", "05:00 PM", "06:00 PM", "07:00 PM", "08:00 PM", "09:00 PM", "10:00 PM", "11:00 PM"], "subCategories": null}, {"code": "108", "name": "You may be charged for this trip by the hotel.", "type": "LABEL", "placeholder": "In case of any issues, Please get in touch with the hotelier directly.", "values": null, "subCategories": null}]}, {"code": "109", "name": "Any other request?", "type": "INPUTBOX", "placeholder": "In case of any issues, Please get in touch with the hotelier directly.", "values": null, "subCategories": null}], "disclaimer": "Special requests are subject to each hotel's availability, may be chargeable & can't be guaranteed."}, "alternateCurrencyConversionFactor": 0.0, "propertyType": "Hotel", "listingType": "Room by Room", "corprateCdfSkip": false, "corporatePAHSkip": false, "netRateAvailable": false, "stateCode": "STKR", "stateName": "Karnataka", "corpRateAvailable": false, "corpRateExpected": false, "corpSupressChain": false, "emiBucket": "", "staycationAvailable": false, "lowestRateStaycation": false, "currencyConvFactorINR": 1.0, "altAcco": false, "recommendCouponBit": true, "freeCancellation": true, "freeCancellationAvailable": true, "breakFast": false, "breakFastAvailable": true, "suppressedPAHHotel": false}], "txnKey": "79a94cf6-9834-4fb1-a291-86490e7eb92f", "correlationKey": "e48f647a-08c4-466e-91c2-f7bfadc4eb22", "locusData": {"locusId": "CTRHR", "locusType": "city", "locusName": "<PERSON><PERSON><PERSON>"}, "expData": {"HPI": "true", "OCCFCNR": "t", "REV": "4", "CHPC": "t", "AARI": "t", "RCPN": "t", "HLMV": "t", "HBH": "f", "MRS": "t", "SRR": "t", "ADDON": "t", "FBP": "f", "MC": "t", "BNPL": "t", "PLRS": "t", "PDO": "PN", "DPCR": "0", "FLTRPRCBKT": "t", "EMI": "f", "SPCR": "2", "BRIN": "110", "ADDV": "f", "HRSRFHS": "true", "HIS": "DEFAULT", "WSP": "t", "APE": "35", "PAH": "5", "HSCFS": "4", "LVD": "", "PAH5": "t", "LCS": "t", "LVI": "", "hotelDetailCardOrder": "rc0,hfc1,qb2,man3,ws4,brn5,blk6,gr7,rr8,ef9,mbg10,ex11,ps12,aac13,alpd14,pd15,au16,db17,bpg18,am19,lcn20,aga21,ta22,ty23,cc24,fb25", "isPayLaterAnimationEnabled": "false", "allowPayLater": "false", "FilterVisibilityDesktop": "true", "htlPolarisCard": "true", "multicurrencyEnabled ": "false", "enableGuidedSearch": "false", "corpApprovalUI": "true", "AATEST": "0", "gstOnTripTags": "true", "htlAltAccoCollection": "true", "checkoutReviewDaysCount": "3", "desktopImgSizeFactorDetail": "1.0", "enablePolarisIntl": "false", "htlEnableQuickDatesAltAcco": "false", "newListingHotelCorporate": "false", "pwaImgSizeFactorDetail": "1.0", "hotel_new_ugc_card": "false", "recommendationOnSoldout": "true", "IAB_Details": "IAB_F", "desktopImgSizeFactorListing": "1.0", "filterBasePricing": "false", "corplEnableSavedLocations": "true", "newListingAltaccoDom": "true", "travellerImageBucketsSectionExp": "false", "newListingCorporate": "true", "forkFcnr": "true", "specialInvalidCorporate": "false", "test_config_str": "test", "htl_locus_enabled_altAcco": "false", "LocusDTFunnel": "true", "hotelNewMapViewShowRegion": "true", "enableCorpMultiroom": "false", "locusLocationFilterEnabled": "true", "enableNewPaymentScreen": "true", "shouldLaunchGamification": "false", "htlShowAcmeFrgament": "true", "review_intent_show": "true", "whichAreaGuide": "LOCATION_FILTER", "htlQuickBookRatingBar": "1", "showVideoOnDetail": "true", "htlNewHeaderFilterUi": "true", "showAltAccoFilterintl": "false", "htlReviewNewUI": "true", "apiExperiment": "35", "DynamicDiscount": "10", "payLaterTimeLine": "true", "homePageReviewCollectionPopUp": "true", "imgSizeFactorExp": "false", "hotelNewMapView": "true", "specialRequestCorporate": "true", "htl_show_pan_card_optional": "1", "BNVsSR": "100", "altAccoNewLanding": "false", "htlGCCThankYouV2": "false", "NewHeader": "false", "LocusDTFunnelHotels": "true", "enableMultiCurrency": "false", "htlShowNearByHotels": "true", "newListingHotelDom": "true", "htl_brin_new_ui": "false", "mobGenConfigVariant": "A", "AAEXP": "1", "enablePolarisFlow": "true", "showMapCorporate": "false", "forcedLogin": "DEFAULT_FLOW", "customerReviewNewDesign": "false", "showNewBrinAnimation": "false", "listingHeaderWithMapIconExp": "false", "htl_new_thank_you_corp": "false", "showEnhancedSeekReviews ": "true", "shouldshowQBRReviewTags": "false", "htlPricingExperiment": "PN", "htlDetailNewRatingCard": "true", "htl_new_thank_you_gcc": "false", "htlB2BThankYouV2": "true", "pwaImgSizeFactorListing": "1.0", "imageDeviceResolution": "true", "mapExploration": "false", "videoPositionOnListing": "0", "mapSearchBar": "true", "htlDarkFilterUiWithMapIcon": "true", "newListingHotelIntl": "true", "rmStarRatingAltAcco": "true", "htlLocusEnabledAltAcco": "true", "EMIDT_NEW": "1", "locusLocationFilterDisabledUI": "false", "personalEnableSavedLocations": "true", "htlLocusEnabledCorp": "false", "showVideos": "false", "htl_Cross_Sell_Comparator": "2", "AndroidImageE2Hotels": "true", "htlAltAccoWidgetType": "1", "isPayLaterEnabled": "false", "payLaterListingAnimation": "true", "altAccoLandingTravelTypeView": "1", "corporateMultiroom": "false", "htlB2CThankYouV2": "true", "hotelDetailCardorder_ios": "RC1,<PERSON><PERSON>2,WPM3,<PERSON><PERSON>4,WOO5,QB6,RD7,<PERSON>G8,AAH9,SA10,EF11,WBH12,HRP13,TA14,HO15,AM16,ATH17,DB18,HMD19,<PERSON>L20,AAM21,HPC22", "htlPolarisCardIntl": "false", "hotelWidgetType": "3", "APEINTL": "36", "isMoCharter": "true", "newCorpApprovalPage": "true", "3.\thtlNewHeaderFilterUi": "false", "shouldHideAltAccoSearchDateSelection": "true", "PAYNEW": "false", "enableLocusLocationFilter": "false", "imageParamChanges": "true", "selectroomvariant": "3", "newHeaderFilterCorp": "true", "htldetailCompareWidget": "1", "consumeManualFlyFishDom": "true", "payAtHotelB2B": "true", "allowNRRatesCorporate": "true", "shouldEnableNearByHotelList": "false", "listingMap": "true", "hotelImageCount": "100", "imagePrefetchEnabled": "true", "myBizHotelPricing": "false", "consumeManualFlyFishIntl": "true", "galleryDesign": "true", "htlNewHeaderFilterUiWithMapIcon": "true", "corpShowSimilarHtls": "false", "hotelSequenceCorporate": "sign-hsq130", "htlCorpWidgetType": "1", "newListingAltaccoIntl": "true", "myBizHotelPricingiOS": "false", "listingHeaderFooterFilterExp": "false", "htlBrinV2": "110", "FilterFlow": "OLD", "htl_locus_enabled_corp": "false", "checkoutReviewSessionCount": "3", "imageDeviceResolutionFactor": "1.0", "hotelMobConfigVariant": "A", "NewHotelMaps": "false", "subConceptsNewDesign": "false", "autoPlayVideo": "false", "showNewListing": "true", "showPancard": "false", "Pay Later Desktop": "false", "htlDummyPanCardExperiment": "1", "htl_new_thank_you_b2c": "false", "isPwaForcedLogin": "true", "showNewAltAccoFilterIntl": "false", "myBizHotelPricingAndroid": "false"}}