{"staticHotelCounts": 0, "totalHotelCounts": 5, "noMoreAvailableHotels": false, "hotelList": [{"id": "201902151551093539", "name": "Hotel Smart Plaza", "propertyType": "Hotel", "mainImages": ["//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-4eed83e80ba011eb8c6b0242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-d39926ca0b9f11eb82940242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,26&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-08c64020070f11eb923f0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,37&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-04c2d682fa6711eaa4a60242ac110007.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,37&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-54edafca070e11eb81420242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,37&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-03813b74fa6711ea98a00242ac110006.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,37&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-e0151d06061c11eb9c210242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,37&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-4457eab0070c11eb898f0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,37&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-13dd3f5c070c11eb95730242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,37&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-45155474070c11eb868f0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,37&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-0568c9b6fa6711ea819f0242ac110007.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,33&output-format=webp", "//r1imghtlak.mmtcdn.com/ed543352e69811e991350242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,26&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-a1a0e2ee070c11eba63a0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,37&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-136248ce070c11eb81420242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,37&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-051366e2fa6711ea9bef0242ac110006.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,37&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-fd100448070c11eb8fa40242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,37&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-7f35c37c070d11eb84390242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,37&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-372fd144070d11eb9c210242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,37&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-c99527b4070d11ebb7c80242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,37&output-format=webp", "//r1imghtlak.mmtcdn.com/b037ba0a767911e997ec0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,46&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-a1b382ee070e11ebbd5a0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,46&output-format=webp", "//r1imghtlak.mmtcdn.com/b982afa2767911e9bce00242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,46&output-format=webp", "//r1imghtlak.mmtcdn.com/cec9f064767911e9b6640242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,46&output-format=webp", "//r1imghtlak.mmtcdn.com/c2ba78b6767911e9811a0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,46&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-d9f213be070e11eb8d450242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,37&output-format=webp", "//r1imghtlak.mmtcdn.com/ce2620ba767911e9bce00242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,46&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-04bbe034fa6711ea881f0242ac110006.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,37&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-1982ce0c070e11eb923f0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,37&output-format=webp", "//r1imghtlak.mmtcdn.com/d2969990767911e9835a0242ac110005.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,46&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-847521d00b9e11ebaab70242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,26&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-13777cbc070c11eb868f0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,37&output-format=webp", "//r1imghtlak.mmtcdn.com/c3be11a0767911e98f540242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,46&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-85a08a400b9e11eb9c3d0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,26&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-83720cb20b9e11eba6e60242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,26&output-format=webp", "//r1imghtlak.mmtcdn.com/b9a6e5de767911e9b15f0242ac110005.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,46&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-843312220b9e11ebbe9b0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,21&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-855ab3da0b9e11eb8d590242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-d3d8d5320b9e11eb8d590242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-d49b64da0b9e11ebb7140242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,26&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201902151551093539-d47475640b9e11ebaeef0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,26&output-format=webp"], "mtkey": "defaultMtkey", "starRating": 3, "currencyCode": {"id": "inr", "value": "inr"}, "address": {"area": ["Near IGI Airport", "Indira Gandhi International Airport", "IGI Airport"], "line2": "Indira Gandhi International Airport", "line1": "N.H-8,<PERSON><PERSON><PERSON> NO 4,  <PERSON><PERSON><PERSON> NO 9, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> MARKET, A Block, NEAR DELHI IGI AIRPORT, DELHI AEROCITY"}, "displayFare": {"tax": {"value": 0.0}, "slashedPrice": {"value": 940.0, "maxFraction": 0.0, "avgeragePriceNoTax": 940.0, "averagePriceWithTax": 940.0, "sellingPriceNoTax": 1880.0, "sellingPriceWithTax": 1880.0}, "actualPrice": {"value": 1880.0, "maxFraction": 0.0, "avgeragePriceNoTax": 1880.0, "averagePriceWithTax": 1880.0, "sellingPriceNoTax": 3760.0, "sellingPriceWithTax": 3760.0}, "mmtDiscountBreakup": {}, "extraAdult": {"value": 0.0}, "segmentId": "1120", "ddmu": 0.0, "totalRoomCount": 1, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 940.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 1880.0, "savingPerc": 50.0, "totalSaving": 940.0, "basePrice": 1880.0, "hotelTax": 0.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 54.0, "mmtDiscount": 940.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n/aSEcDEbiYTtkKzWdk3MqGDE401iy27jKnNNH0Uyh8cQ79dp2Jz/fCGJb3vhAjdsN6qE0Jz8LD22Fvmv/usrAlLAqwq0tTH4DDOoXMVI1AhhN+MSrCtVeXjZpLe+jsyFv7FRzx15OPNh8EsWbwPG2p8NIrRGiWgny7Qg0imYRZW1oFXrWsV3QPCrgKZMp4HL4E9+iakLEByJU0FQ2oPNwh1Li9cue6X2Qo+LZbG0BQ3rgNYYXe2fymz47FltQRus6aJCUFuidWG3M9raXdKL1s", "pricingDivisor": 2, "totalTax": 54.0, "effectivePrice": 940.0, "brinInclusivePrice": 0.0, "affiliateFee": 0.0, "totalAmount": 1880.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}, "conversionFactor": 0.0, "hotelTax": 0.0, "hotelierServiceCharge": 0.0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false}, "categories": ["MyBiz Assured", "MySafety - Safe and Hygienic Stays", "Couple Friendly", "Great Value Deals", "MMT Assured", "Safety_Hygiene", "Inmarket", "Safe and Hygienic Stays", "Hotel_Chat_Poc"], "cityName": "Delhi", "countryName": "India", "countryCode": "IN", "cityCode": "CTDEL", "stayType": "Hotel", "maxOccupancy": 0, "ignoreEntireProperty": false, "locationPersuasion": ["Indira Gandhi International Airport", "720 m from Hotel Venus", "1.4 km from Indira Gandhi International Airport (DEL), Terminal 3, "], "quickBookInfo": {"roomPersuasion": "1 King bed or 2 Twin Bed(s) | Room Only | Free Cancellation", "titleWithPrice": "Quick Book For ₹ 6013.28"}, "isAbsorptionApplied": false, "featured": "N", "isSoldOut": false, "isFreeWifiAvail": true, "isPAHAvailable": false, "isShortlisted": false, "hotelFacilities": "Safety and Hygiene,Basic Facilities,Food and Drinks,Payment Services,Safety and Security,Media and technology,General Services,Common Area,Business Center and Conferences", "geoLocation": {"latitude": "28.5538", "longitude": "77.09727"}, "promotions": {"opaquePromotion": {"techShowOpaque": false}, "promotionsCount": 0}, "inclusions": [], "isMTKeyEnabledSearch": false, "poiTag": "1.4 km from Indira Gandhi International Airport (DEL), Terminal 3, ", "isValuePlus": false, "isFreeCancellation": false, "freeCancellationAvailable": false, "breakFast": false, "breakFastAvailable": true, "shortDescription": "Hotel Smart Plaza offers rooms in New Delhi, The property is around 3 km from Indira Gandhi International Airport. Qutub Minar i ", "userEligiblePayMode": "PAS", "couponAutoApply": true, "isPAHTariffAvailable": true, "paymentMode": "PAS", "avgTP": 0.0, "persuasions": [{"id": "48", "desc": "Great Choice! Booked 100+ times in last 15 Days", "placeholder": "SingleLine", "priority": 8001, "theme": "URGENCY"}], "categoryDetails": {"MySafety - Safe and Hygienic Stays": {"title": "This property is following safety and hygiene measures", "iconUrl": "https://promos.makemytrip.com/COVID/safe.png", "itemIconType": "", "data": ["Hygienic Rooms", "Trained Staff", "Sanitized Indoors", "Safe Dining"]}}, "reasonToBelieve": ["720 m from Hotel Venus"], "matchScore": 98.18181818181819, "hasCollections": false, "dynamicFilters": ["Location"], "bedCount": 0, "mealPlanIncluded": {"desc": "Room Only", "code": "EP", "cost": null}, "segments": {"segmentList": {"1120": {"id": "1120", "channelSegment": "ALL", "userSegment": "REGULAR"}}, "segmentsCount": 1}, "lowestRateSupplierCode": "INGO", "lowestRateSegmentId": "1120", "stateCode": "STDEL", "stateName": "Delhi", "lowestRoomAvailCount": 1, "totalRoomAvailCount": 15, "isBNPLAvailable": false, "bnplBaseAmount": 0.0, "pahWalletApplicable": false, "bestPriceGuaranteed": false, "pahxAvailable": false, "pahxApplicable": false, "usp": {"Location": {"sub_title": "Indira Gandhi International Airport is 1.2 Km from the property", "title": "Location", "subTags": null}}, "addOnAvailableOnLowest": false, "addOnAvailable": false, "sponsored": false, "listingType": "Room By Room", "facilityHighlights": ["Free Parking", "Free Wi-Fi", "Kitchenette", "Elevator/Lift", "Air Conditioning", "24-hour Room Service", "Power Backup", "Restaurant", "Doctor on Call"], "facilityCategorization": [{"subcat": "Contactless check-in", "name": "hotel"}, {"subcat": "Parking", "name": "hotel"}, {"subcat": "Wifi", "name": "hotel"}, {"subcat": "Kitchen/Kitchenette", "name": "hotel"}, {"subcat": "Elevator/ Lift", "name": "hotel"}, {"subcat": "Air Conditioning", "name": "hotel"}, {"subcat": "Room service", "name": "hotel"}, {"subcat": "Power backup", "name": "hotel"}, {"subcat": "Dry Cleaning services", "name": "hotel"}, {"subcat": "Smoking rooms", "name": "hotel"}, {"subcat": "Intercom", "name": "hotel"}, {"subcat": "Housekeeping", "name": "hotel"}, {"subcat": "<PERSON><PERSON><PERSON>", "name": "hotel"}, {"subcat": "Bathroom", "name": "hotel"}, {"subcat": "Ironing services", "name": "hotel"}, {"subcat": "Telephone", "name": "hotel"}, {"subcat": "Newspaper", "name": "hotel"}, {"subcat": "Restaurant", "name": "hotel"}, {"subcat": "Dining Area", "name": "hotel"}, {"subcat": "Currency Exchange", "name": "hotel"}, {"subcat": "CCTV", "name": "hotel"}, {"subcat": "Fire extinguishers", "name": "hotel"}, {"subcat": "Security", "name": "hotel"}, {"subcat": "TV", "name": "hotel"}, {"subcat": "Doctor on call", "name": "hotel"}, {"subcat": "Luggage storage", "name": "hotel"}, {"subcat": "Wake-up Call / Service", "name": "hotel"}, {"subcat": "Electrical Sockets", "name": "hotel"}, {"subcat": "Fireplace", "name": "hotel"}, {"subcat": "Reception", "name": "hotel"}, {"subcat": "Seating Area", "name": "hotel"}, {"subcat": "Printer", "name": "hotel"}, {"subcat": "Photocopying", "name": "hotel"}, {"subcat": "Business Center", "name": "hotel"}], "propertyActivities": [], "trackingInfo": null, "blackAccelerated": false, "maxAdultAtSamePrice": 0, "bedRoomCount": 0, "maxChildAtSamePrice": 0, "alternateDatesAvailable": false, "lowestBlackPackage": false, "blackPackageAvailable": false, "netRateAvailable": false, "lowestNetRate": false, "roomCount": 0, "poiTagList": [{"hotelId": "201902151551093539", "poiId": "POIINDI", "poiName": "Indira Gandhi International Airport (DEL), Terminal 3, ", "poiCategory": "Airport", "poiPriority": "0", "drivingDistance": "1400.0", "drivingDistanceText": "1.4 km", "drivingTimeText": "", "bestPoiPriority": 0}], "lastBooked": false, "lowestRatePlanMealDiff": 0.0, "lowestRatePlanPahDiff": 0.0, "notInterested": false, "lowestRoomCodesRatePlans": "394--990001152428:MSE:1120:MSE:INGO", "extraMeals": {"desc": "Breakfast", "code": "CP", "cost": null}, "priceDifferenceWithPivot": 0.0, "lowestRateBnpl": false, "staycationAvailable": false, "staycationRateAvlblForFilter": false, "lowestRateStaycation": false, "shortList": false, "dealOfDayApplicable": false, "topHotel": false, "pahmode": "PAS", "recommendedMultiRoom": false, "recommendCouponBit": true, "altAcco": false}, {"id": "201901282044575123", "name": "HOTEL CAPITOL", "propertyType": "Hotel", "mainImages": ["//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-90ffcdcedccd11ea80bc0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-bd549a8aee6111eaa07a0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-cab4d934dcc211ea89e80242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,23&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-d8f3b16add7011eab2870242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-cd90b65edccd11eab0740242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-44a6a302dcce11ea80bc0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,23&output-format=webp", "//r1imghtlak.mmtcdn.com/f1710e760bd411eb8afa0242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,3&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-badbd134ee6511ea91760242ac110005.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,44&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-3c1bcde4dccd11ea9f6b0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r1imghtlak.mmtcdn.com/f50172560bd411ebafb40242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-909cc710dccd11eaa0b50242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-f463f8960bd411eb8b3e0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-3e9d6072dcce11eaa73d0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-cde98586dccd11ea87400242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,20&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-c57e26500bd411ebba1a0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-c4ba726e0bd411eb8afa0242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,24&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-6f4684a20bd511eb8afa0242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-5b4709e0dccd11eab9d40242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,39&output-format=webp", "//r1imghtlak.mmtcdn.com/3be21e00dccd11ea89e80242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-ced8fbd4dccd11ea83690242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r1imghtlak.mmtcdn.com/fce014c2dccd11ea89e80242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,3&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-cf7f70f4dccd11ea9f6b0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-cfba2d2adccd11ea801f0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-fe141f8cdccd11ea80bc0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-3d4f7cbedcce11ea82330242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,21&output-format=webp", "//r1imghtlak.mmtcdn.com/3cad6104dcce11ea9f6b0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,24&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-3d797baedcce11ea801f0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,23&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-9a8f6a68dd7011eab2870242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,26&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-6f20ed3c0bd511ebaeef0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,28&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-24af77be0bd511eb90ba0242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r1imghtlak.mmtcdn.com/6e46ffe60bd511eb8c6b0242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-257249a60bd511ebafb40242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-24c8b1c00bd511eb804e0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,26&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-9b260112dd7011ea9fc70242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-244e86fc0bd511eb997e0242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r1imghtlak.mmtcdn.com/9b2acb98dd7011ea9ea40242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-9b28eb48dd7011ea91be0242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-9b201c3edd7011ea9ed60242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-2763346add7111ea91be0242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,24&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201901282044575123-2732152edd7111ea95e60242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,29&output-format=webp"], "mtkey": "defaultMtkey", "starRating": 3, "currencyCode": {"id": "inr", "value": "inr"}, "address": {"area": ["IGI Airport", "Near IGI Airport", "Indira Gandhi International Airport"], "line2": "Indira Gandhi International Airport", "line1": "HOTEL D'CAPITOL, Mata Chowk,Vasant Kunj Road,Mahipalpur, New Delhi-110037"}, "displayFare": {"tax": {"value": 0.0}, "slashedPrice": {"value": 2288.0, "maxFraction": 0.0, "avgeragePriceNoTax": 2288.0, "averagePriceWithTax": 2288.0, "sellingPriceNoTax": 4576.0, "sellingPriceWithTax": 4576.0}, "actualPrice": {"value": 5200.0, "maxFraction": 0.0, "avgeragePriceNoTax": 5200.0, "averagePriceWithTax": 5200.0, "sellingPriceNoTax": 10400.0, "sellingPriceWithTax": 10400.0}, "mmtDiscountBreakup": {}, "extraAdult": {"value": 0.0}, "segmentId": "1120", "ddmu": 0.0, "totalRoomCount": 1, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 2288.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 5200.0, "savingPerc": 56.0, "totalSaving": 2912.0, "basePrice": 5200.0, "hotelTax": 275.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 121.0, "mmtDiscount": 2912.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n+W+kZnXAFmU1tecDD/g9fBuy2R4gjC8hlSFPVzm3m3hnKw+qUQc02AwN3BlY96UFUJDIKTu6Eb5fI+1fW8JQgppUQeAjXPqZCymBVePcBpQ1cQFeqoaJWMgOVfmxZkfMg7kTjINWRSkiDigryfpVM6fQzo2v3xPY7v/WZGNVyMrS1P0PVJehB8xmI+vrN69rfPmzkeatsFKgRJRPrGrdqzZsAMxPxapBW+nsOV2tPnqeAQDWgIXT+0hZ7imKr4GwAhZ1lYbpW+iFrlWLn5Cyu9Vl3ZVdZVxww=", "pricingDivisor": 2, "totalTax": 395.0, "effectivePrice": 2288.0, "brinInclusivePrice": 0.0, "affiliateFee": 0.0, "totalAmount": 4576.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}, "conversionFactor": 0.0, "hotelTax": 274.56, "hotelierServiceCharge": 0.0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false}, "categories": ["MySafety - Safe and Hygienic Stays", "Couple Friendly", "Safety_Hygiene", "Inmarket", "Safe and Hygienic Stays", "Hotel_Chat_Poc"], "categoryDetails": {"MySafety - Safe and Hygienic Stays": {"title": "This property is following safety and hygiene measures", "iconUrl": "https://promos.makemytrip.com/COVID/safe.png", "itemIconType": "", "data": ["Hygienic Rooms", "Trained Staff", "Sanitized Indoors", "Safe Dining"]}}, "cityName": "Delhi", "countryName": "India", "countryCode": "IN", "cityCode": "CTDEL", "stayType": "Hotel", "maxOccupancy": 0, "ignoreEntireProperty": false, "locationPersuasion": ["Indira Gandhi International Airport", "990 m from Hotel Venus", "1.9 km from T2 - Delhi Airport (IGI)"], "isAbsorptionApplied": false, "featured": "N", "isSoldOut": false, "isFreeWifiAvail": true, "isPAHAvailable": false, "isShortlisted": false, "hotelFacilities": "Safety and Hygiene,Basic Facilities,Transfers,Food and Drinks,Payment Services,Safety and Security,Health and wellness,Media and technology,General Services,Outdoor Activities and Sports,Common Area,Business Center and Conferences", "geoLocation": {"latitude": "28.55466", "longitude": "77.10055"}, "promotions": {"opaquePromotion": {"techShowOpaque": false}, "promotionsCount": 0}, "inclusions": [], "isMTKeyEnabledSearch": false, "poiTag": "1.9 km from T2 - Delhi Airport (IGI)", "isValuePlus": false, "isFreeCancellation": false, "freeCancellationAvailable": false, "breakFast": false, "breakFastAvailable": true, "shortDescription": "Hotel Capitol offers rooms in New Delhi, The property is around 3.5 km from Indira Gandhi International Airport. <PERSON><PERSON><PERSON> is  ", "userEligiblePayMode": "PAS", "couponAutoApply": true, "isPAHTariffAvailable": true, "paymentMode": "PAS", "reasonToBelieve": ["990 m from Hotel Venus"], "matchScore": 96.92307692307692, "avgTP": 0.0, "persuasions": [{"id": "581", "desc": "Great Choice! Booked 50+ times in last 15 Days", "placeholder": "SingleLine", "priority": 8002, "theme": "URGENCY"}], "hasCollections": false, "dynamicFilters": ["Location"], "bedCount": 0, "mealPlanIncluded": {"desc": "Room Only", "code": "EP", "cost": null}, "segments": {"segmentList": {"1120": {"id": "1120", "channelSegment": "ALL", "userSegment": "REGULAR"}, "1180": {"id": "1180", "channelSegment": "MY_PARTNER", "userSegment": "REGULAR"}, "1121": {"id": "1121", "channelSegment": "MOB", "userSegment": "REGULAR"}, "1134": {"id": "1134", "channelSegment": "AFF", "userSegment": "REGULAR"}, "1126": {"id": "1126", "channelSegment": "ALL", "userSegment": "LOGGEDIN"}}, "segmentsCount": 5}, "lowestRateSupplierCode": "INGO", "lowestRateSegmentId": "1120", "stateCode": "STDEL", "stateName": "Delhi", "lowestRoomAvailCount": 1, "totalRoomAvailCount": 2, "isBNPLAvailable": false, "bnplBaseAmount": 0.0, "pahWalletApplicable": false, "bestPriceGuaranteed": false, "pahxAvailable": false, "pahxApplicable": false, "usp": {"Location": {"sub_title": "Close to Delhi Airport", "title": "Location", "subTags": null}}, "addOnAvailableOnLowest": false, "addOnAvailable": false, "sponsored": false, "listingType": "Room By Room", "facilityHighlights": ["Free Parking", "Free Wi-Fi", "Air Conditioning", "24-hour Room Service", "Power Backup", "Restaurant", "Doctor on Call"], "facilityCategorization": [{"subcat": "Masks", "name": "hotel"}, {"subcat": "Sanitizers", "name": "hotel"}, {"subcat": "Thermal screening at entry and exit points", "name": "hotel"}, {"subcat": "Sanitizers installed", "name": "hotel"}, {"subcat": "Parking", "name": "hotel"}, {"subcat": "Wifi", "name": "hotel"}, {"subcat": "Air Conditioning", "name": "hotel"}, {"subcat": "Room service", "name": "hotel"}, {"subcat": "Power backup", "name": "hotel"}, {"subcat": "Dry Cleaning services", "name": "hotel"}, {"subcat": "Smoking rooms", "name": "hotel"}, {"subcat": "<PERSON><PERSON><PERSON>", "name": "hotel"}, {"subcat": "Intercom", "name": "hotel"}, {"subcat": "Telephone", "name": "hotel"}, {"subcat": "Housekeeping", "name": "hotel"}, {"subcat": "Newspaper", "name": "hotel"}, {"subcat": "Smoke detector", "name": "hotel"}, {"subcat": "Bathroom", "name": "hotel"}, {"subcat": "Pickup/ Drop", "name": "hotel"}, {"subcat": "Shuttle Service", "name": "hotel"}, {"subcat": "Railway Station Transfers", "name": "hotel"}, {"subcat": "Airport Transfers", "name": "hotel"}, {"subcat": "Bus Station transfers", "name": "hotel"}, {"subcat": "Restaurant", "name": "hotel"}, {"subcat": "Dining Area", "name": "hotel"}, {"subcat": "ATM", "name": "hotel"}, {"subcat": "Currency Exchange", "name": "hotel"}, {"subcat": "CCTV", "name": "hotel"}, {"subcat": "Fire extinguishers", "name": "hotel"}, {"subcat": "Safety and Security", "name": "hotel"}, {"subcat": "Electronic keycard", "name": "hotel"}, {"subcat": "Security", "name": "hotel"}, {"subcat": "Safe", "name": "hotel"}, {"subcat": "First-aid services", "name": "hotel"}, {"subcat": "TV", "name": "hotel"}, {"subcat": "Doctor on call", "name": "hotel"}, {"subcat": "Mail services", "name": "hotel"}, {"subcat": "Electrical Sockets", "name": "hotel"}, {"subcat": "Medical centre", "name": "hotel"}, {"subcat": "Ticket/ Tour Assistance", "name": "hotel"}, {"subcat": "Bellboy service", "name": "hotel"}, {"subcat": "Wake-up Call / Service", "name": "hotel"}, {"subcat": "Vehicle Rentals", "name": "hotel"}, {"subcat": "Fireplace", "name": "hotel"}, {"subcat": "Aquarium", "name": "hotel"}, {"subcat": "Living Room", "name": "hotel"}, {"subcat": "Reception", "name": "hotel"}, {"subcat": "Balcony/ Terrace", "name": "hotel"}, {"subcat": "Seating Area", "name": "hotel"}, {"subcat": "Printer", "name": "hotel"}, {"subcat": "Photocopying", "name": "hotel"}], "propertyActivities": [], "trackingInfo": null, "blackAccelerated": false, "maxAdultAtSamePrice": 0, "bedRoomCount": 0, "maxChildAtSamePrice": 0, "alternateDatesAvailable": false, "lowestBlackPackage": false, "blackPackageAvailable": false, "netRateAvailable": false, "lowestNetRate": false, "roomCount": 0, "poiTagList": [{"hotelId": "201901282044575123", "poiId": "POITER", "poiName": "T2 - Delhi Airport (IGI)", "poiCategory": "Airport", "poiPriority": "0", "drivingDistance": "1900.0", "drivingDistanceText": "1.9 km", "drivingTimeText": "", "bestPoiPriority": 0}], "lastBooked": false, "lowestRatePlanMealDiff": 0.0, "lowestRatePlanPahDiff": 0.0, "notInterested": false, "lowestRoomCodesRatePlans": "2817186--990001548928:MSE:1120:MSE:INGO", "extraMeals": {"desc": "Breakfast", "code": "CP", "cost": null}, "priceDifferenceWithPivot": 0.0, "lowestRateBnpl": false, "staycationAvailable": false, "staycationRateAvlblForFilter": false, "lowestRateStaycation": false, "shortList": false, "dealOfDayApplicable": false, "topHotel": false, "pahmode": "PAS", "recommendedMultiRoom": false, "recommendCouponBit": true, "altAcco": false}, {"id": "201705020734585069", "name": "Hotel Aero Star", "propertyType": "Hotel", "mainImages": ["//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-c197c2460adc11e79bba02755708f0b3.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-50b2a370b28211e9be6b0242ac140005.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-62a242e8b28211e9be6b0242ac140005.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-6afaa35eb28211e9be6b0242ac140005.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-81d473cab28211e9be6b0242ac140005.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-97ebbb6eb28211e9be6b0242ac140005.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-9ea2a76ab28211e9be6b0242ac140005.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-a4965716b28211e9be6b0242ac140005.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-8a2696d4b28211e9be6b0242ac140005.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-750b1ad6b28211e9be6b0242ac140005.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-9124c60eb28211e9be6b0242ac140005.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,218&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-5a993e62b28211e9be6b0242ac140005.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r1imghtlak.mmtcdn.com/c1584fda30ca11e7852802755708f0b3.jpeg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-7ed3e5480adc11e7bb2e0a9df65c8753.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-83cb5bee0adc11e79bba02755708f0b3.jpg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r1imghtlak.mmtcdn.com/fa4dfee8b16d11e6b70e02755708f0b3.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r1imghtlak.mmtcdn.com/d60403ac30ca11e7852802755708f0b3.jpeg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-4ca3fa8a0add11e7809c02755708f0b3.jpg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r1imghtlak.mmtcdn.com/d2b6d53a30ca11e7a7620224510f5e5b.jpeg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r1imghtlak.mmtcdn.com/d4dfe14e30ca11e7891a02755708f0b3.jpeg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r1imghtlak.mmtcdn.com/d83e9d3030ca11e7b2320224510f5e5b.jpeg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r1imghtlak.mmtcdn.com/dd69e16630ca11e7ab4802755708f0b3.jpeg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r1imghtlak.mmtcdn.com/e309cc6c30ca11e79c290224510f5e5b.jpeg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r1imghtlak.mmtcdn.com/e4fd0e4e30ca11e7b75602755708f0b3.jpeg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r1imghtlak.mmtcdn.com/ec05a57ab16d11e6a8700a209fbd0127.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-58d243da0adc11e7af4202755708f0b3.jpg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-73be918a0adc11e792760224510f5e5b.jpg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-7c55478a0adc11e7af010a209fbd0127.jpg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-209de2480add11e798810a9df65c8753.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-227a248c0add11e7936b02755708f0b3.jpg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-e8a348920adc11e79a140a209fbd0127.jpg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-8b713c740adc11e798810a9df65c8753.jpg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-56ab373c0add11e79d370a209fbd0127.jpg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-94e1d9300adc11e7982d0224510f5e5b.jpg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-bcf6f9140adc11e7b60a02755708f0b3.jpg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-fd426c740adc11e781210224510f5e5b.jpg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-040afc6a0add11e78f1202755708f0b3.jpg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-377051c20add11e798810a9df65c8753.jpg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-0b1076340add11e7a31a0a209fbd0127.jpg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201705020734585069-3e4e1e8e0add11e7a60e0a9df65c8753.jpg?&output-quality=75&downsize=459:252&crop=459:252;16,0&output-format=webp"], "mtkey": "defaultMtkey", "starRating": 3, "currencyCode": {"id": "inr", "value": "inr"}, "address": {"area": ["IGI Airport", "Near IGI Airport", "Indira Gandhi International Airport"], "line2": "Indira Gandhi International Airport", "line1": "A-107,MAHIPALP<PERSON>,NH-8"}, "displayFare": {"tax": {"value": 0.0}, "slashedPrice": {"value": 999.0, "maxFraction": 0.0, "avgeragePriceNoTax": 999.0, "averagePriceWithTax": 999.0, "sellingPriceNoTax": 1998.0, "sellingPriceWithTax": 1998.0}, "actualPrice": {"value": 2099.0, "maxFraction": 0.0, "avgeragePriceNoTax": 2099.0, "averagePriceWithTax": 2099.0, "sellingPriceNoTax": 4198.0, "sellingPriceWithTax": 4198.0}, "mmtDiscountBreakup": {"zcr": 2200.0}, "extraAdult": {"value": 0.0}, "segmentId": "1120", "ddmu": 0.0, "totalRoomCount": 1, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 999.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 2099.0, "savingPerc": 52.0, "totalSaving": 1100.0, "basePrice": 2099.0, "hotelTax": 0.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 80.0, "mmtDiscount": 1100.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n9bReCEcSfDo4/PTtn2aAWpEd+tuDHWNVnw6QfjWybXIs/PAFQhJs58+2GpX9WM7Z4TF8oY9gV4q9TXqSwn8iDfOhDzGJY15hiTRf2PUBCmOvgI9UF5C76JHItRvvk/hI1YzGvQ9Mcbm83rRTvn/5TcrEI+DyYOnrmW9onvqrq6TzABHceYsA47NOiQmUoJm8tibIKZ2DFb7pgz2uyovCjyZThwfM+7u9LGul5o1Xr/SRtw3IxPjz3QZ/wXCrN0VMdbLz6GY0TpD8bzJy4NEFlQ", "pricingDivisor": 2, "totalTax": 80.0, "effectivePrice": 999.0, "brinInclusivePrice": 0.0, "affiliateFee": 0.0, "totalAmount": 1998.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}, "conversionFactor": 0.0, "hotelTax": 0.0, "hotelierServiceCharge": 0.0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "FULL", "taxExcluded": false}, "categories": ["MMT OFFER", "MyBiz Assured", "Couple Friendly", "MMT Assured", "Gostays", "GI_Focus", "Inmarket", "Hotel_Chat_Poc"], "cityName": "Delhi", "countryName": "India", "countryCode": "IN", "cityCode": "CTDEL", "stayType": "Hotel", "maxOccupancy": 0, "ignoreEntireProperty": false, "locationPersuasion": ["Indira Gandhi International Airport", "660 m from Hotel Venus", "0 m from Indira Gandhi International Airport (DEL), Terminal 3, "], "isAbsorptionApplied": false, "bestSell": "AKB", "featured": "N", "isSoldOut": false, "isFreeWifiAvail": true, "isPAHAvailable": false, "isShortlisted": false, "hotelFacilities": "Safety and Hygiene,Basic Facilities,Transfers,Family and kids,Food and Drinks,Safety and Security,Health and wellness,Media and technology,General Services,Other Facilities,Common Area,Business Center and Conferences", "geoLocation": {"latitude": "28.55508", "longitude": "77.0844"}, "promotions": {"opaquePromotion": {"techShowOpaque": false}, "promotionsCount": 0}, "inclusions": [], "isMTKeyEnabledSearch": false, "poiTag": "0 m from Indira Gandhi International Airport (DEL), Terminal 3, ", "isValuePlus": false, "isFreeCancellation": false, "freeCancellationAvailable": false, "breakFast": false, "breakFastAvailable": true, "shortDescription": "Hotel Star, Delhi, is among the best budget hotels located in Mahipalpur, Delhi. The hotel remains a favoured choice of many lei ", "userEligiblePayMode": "PAS", "couponAutoApply": true, "isPAHTariffAvailable": true, "paymentMode": "PAS", "reasonToBelieve": ["660 m from Hotel Venus"], "matchScore": 96.0, "avgTP": 0.0, "persuasions": [{"id": "48", "desc": "Great Choice! Booked 100+ times in last 15 Days", "placeholder": "SingleLine", "priority": 8001, "theme": "URGENCY"}], "hasCollections": false, "dynamicFilters": ["Location"], "hotelVideos": [{"url": "https://juggler.makemytrip.com/juggler/stream/key/b648e2de-00f9-44e3-b602-c9b2278a9a3f/master.m3u8", "sequence": 1, "tags": ["Safety and Hygiene"], "thumbnailUrl": "https://juggler.makemytrip.com/juggler/stream/key/b648e2de-00f9-44e3-b602-c9b2278a9a3f/thumbnails/360_640.jpg", "title": "", "videoFilterInfo": "Safety and Hygiene"}], "bedCount": 0, "mealPlanIncluded": {"desc": "Room Only", "code": "EP", "cost": null}, "segments": {"segmentList": {"1120": {"id": "1120", "channelSegment": "ALL", "userSegment": "REGULAR"}}, "segmentsCount": 1}, "lowestRateSupplierCode": "INGO", "lowestRateSegmentId": "1120", "stateCode": "STDEL", "stateName": "Delhi", "lowestRoomAvailCount": 1, "totalRoomAvailCount": 13, "isBNPLAvailable": false, "bnplBaseAmount": 0.0, "pahWalletApplicable": false, "bestPriceGuaranteed": false, "pahxAvailable": false, "pahxApplicable": false, "usp": {"Location": {"sub_title": "The hotel is located at a walking distance from metro station, just 10 m away from NH-8 near Delhi Airport", "title": "Location", "subTags": null}}, "addOnAvailableOnLowest": false, "addOnAvailable": false, "sponsored": false, "listingType": "Room By Room", "propertyRules": ["Government ID cards are mandatory during the time of check in."], "facilityHighlights": ["Free Parking", "Kids Play Area", "Childcare Services", "Free Wi-Fi", "Elevator/Lift", "Air Conditioning", "24-hour Room Service", "Restaurant", "Doctor on Call"], "facilityCategorization": [{"subcat": "Masks", "name": "hotel"}, {"subcat": "Sanitizers", "name": "hotel"}, {"subcat": "Thermal screening at entry and exit points", "name": "hotel"}, {"subcat": "Sanitizers installed", "name": "hotel"}, {"subcat": "Disposable serveware", "name": "hotel"}, {"subcat": "Contactless check-in", "name": "hotel"}, {"subcat": "Parking", "name": "hotel"}, {"subcat": "Wifi", "name": "hotel"}, {"subcat": "Elevator/ Lift", "name": "hotel"}, {"subcat": "Air Conditioning", "name": "hotel"}, {"subcat": "Room service", "name": "hotel"}, {"subcat": "Intercom", "name": "hotel"}, {"subcat": "Housekeeping", "name": "hotel"}, {"subcat": "Public restrooms", "name": "hotel"}, {"subcat": "Bathroom", "name": "hotel"}, {"subcat": "Pickup/ Drop", "name": "hotel"}, {"subcat": "Railway Station Transfers", "name": "hotel"}, {"subcat": "Airport Transfers", "name": "hotel"}, {"subcat": "Children's play area", "name": "hotel"}, {"subcat": "Childcare service", "name": "hotel"}, {"subcat": "Restaurant", "name": "hotel"}, {"subcat": "CCTV", "name": "hotel"}, {"subcat": "First-aid services", "name": "hotel"}, {"subcat": "TV", "name": "hotel"}, {"subcat": "Doctor on call", "name": "hotel"}, {"subcat": "Dispensors for disinfectants", "name": "hotel"}, {"subcat": "Reception", "name": "hotel"}, {"subcat": "Seating Area", "name": "hotel"}, {"subcat": "Photocopying", "name": "hotel"}], "propertyActivities": [], "trackingInfo": null, "blackAccelerated": false, "maxAdultAtSamePrice": 0, "bedRoomCount": 0, "maxChildAtSamePrice": 0, "alternateDatesAvailable": false, "lowestBlackPackage": false, "blackPackageAvailable": false, "netRateAvailable": false, "lowestNetRate": false, "roomCount": 0, "poiTagList": [{"hotelId": "201705020734585069", "poiId": "POIINDI", "poiName": "Indira Gandhi International Airport (DEL), Terminal 3, ", "poiCategory": "Airport", "poiPriority": "0", "drivingDistance": "0.45", "drivingDistanceText": "0.0 km", "drivingTimeText": "", "bestPoiPriority": 0}], "lastBooked": false, "lowestRatePlanMealDiff": 0.0, "lowestRatePlanPahDiff": 0.0, "notInterested": false, "lowestRoomCodesRatePlans": "2250--990000431972:MSE:1120:MSE:INGO", "extraMeals": {"desc": "Breakfast", "code": "CP", "cost": null}, "priceDifferenceWithPivot": 0.0, "lowestRateBnpl": false, "staycationAvailable": false, "staycationRateAvlblForFilter": false, "lowestRateStaycation": false, "shortList": false, "dealOfDayApplicable": false, "topHotel": false, "pahmode": "PAS", "recommendedMultiRoom": false, "recommendCouponBit": true, "altAcco": false}, {"id": "201909301801331019", "name": "Hotel Smart International", "propertyType": "Hotel", "mainImages": ["//r1imghtlak.mmtcdn.com/8cbdf78e129511ea86bc0242ac110004.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,25&output-format=webp", "//r1imghtlak.mmtcdn.com/79687bfa129511eaa4260242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,25&output-format=webp", "//r1imghtlak.mmtcdn.com/7b4b12de129511eab0d10242ac110007.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,25&output-format=webp", "//r1imghtlak.mmtcdn.com/7c06ddd4129511eab08d0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,25&output-format=webp", "//r1imghtlak.mmtcdn.com/7bde7d94129511ea90f50242ac110005.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,25&output-format=webp", "//r1imghtlak.mmtcdn.com/8b9f2224129511ea81940242ac110004.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,25&output-format=webp", "//r1imghtlak.mmtcdn.com/8ca2ef98129511ea8a6a0242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,25&output-format=webp", "//r1imghtlak.mmtcdn.com/8cc517e4129511eaa0850242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,25&output-format=webp", "//r1imghtlak.mmtcdn.com/d65985fc129511ea930c0242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,25&output-format=webp", "//r1imghtlak.mmtcdn.com/d69aa866129511ea93b20242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,25&output-format=webp", "//r1imghtlak.mmtcdn.com/e1aef67a12a511ea9a0b0242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,25&output-format=webp", "//r1imghtlak.mmtcdn.com/dfcbb56c12a711ea9be80242ac110004.jpg?&output-quality=75&downsize=459:252&crop=459:252;44,0&output-format=webp", "//r1imghtlak.mmtcdn.com/e025ce1c12a711ea88740242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;43,0&output-format=webp", "//r1imghtlak.mmtcdn.com/e282662a12a711ea96800242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;43,0&output-format=webp", "//r1imghtlak.mmtcdn.com/e2930c3212a711eaaf560242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;43,0&output-format=webp", "//r1imghtlak.mmtcdn.com/e2c3fefa12a711ea80740242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;43,0&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201909301801331019-ea3cf0320ece11eba5da0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201909301801331019-5e995c040ecf11eb8a130242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,46&output-format=webp", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201909301801331019-9947408c0ecf11ebb4340242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp"], "mtkey": "defaultMtkey", "starRating": 0, "currencyCode": {"id": "inr", "value": "inr"}, "address": {"area": ["Indira Gandhi International Airport", "IGI Airport", "Near IGI Airport"], "line2": "Indira Gandhi International Airport", "line1": "A BLOCK ROAD NO 4 MAHIPALPUR EXTN NEW DELHI 110037"}, "displayFare": {"tax": {"value": 0.0}, "slashedPrice": {"value": 1099.5, "maxFraction": 0.0, "avgeragePriceNoTax": 1099.5, "averagePriceWithTax": 1099.5, "sellingPriceNoTax": 2199.0, "sellingPriceWithTax": 2199.0}, "actualPrice": {"value": 2199.0, "maxFraction": 0.0, "avgeragePriceNoTax": 2199.0, "averagePriceWithTax": 2199.0, "sellingPriceNoTax": 4398.0, "sellingPriceWithTax": 4398.0}, "mmtDiscountBreakup": {}, "extraAdult": {"value": 0.0}, "segmentId": "1120", "ddmu": 0.0, "totalRoomCount": 1, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 1100.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 2199.0, "savingPerc": 50.0, "totalSaving": 1100.0, "basePrice": 2199.0, "hotelTax": 132.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 49.0, "mmtDiscount": 1100.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n9bReCEcSfDo7A9wWa6eY8T1bHkqcbD21rcnATejQoIQ7Q1NrHc+Iak6F5FlASyMikXKL5Sc7yi1JJ1ND92Ts81NYls96cH8k3WNg12agRMyAL8hbodV+4/Pe6953eEhVapwoKmpWHHLWsZ3Jv4XVu27IqDKGgsTmN1qrDDROMrYISrLFsN38n01e8lJpGN1nPh4dpTdY/gd+szQE4UGIapXA6lzBFEhGP4EJsOHlxGSjjuDkHjprAehiQ3NEoR9Dz8cU+j8wixnas4cErzf7ugf84U3ZYg5Lk=", "pricingDivisor": 2, "totalTax": 181.0, "effectivePrice": 1100.0, "brinInclusivePrice": 0.0, "affiliateFee": 0.0, "totalAmount": 2200.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": false}, "conversionFactor": 0.0, "hotelTax": 131.94, "hotelierServiceCharge": 0.0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false}, "categories": ["MyBiz Assured", "MySafety - Safe and Hygienic Stays", "Couple Friendly", "Safety_Hygiene", "Inmarket", "Safe and Hygienic Stays"], "categoryDetails": {"MySafety - Safe and Hygienic Stays": {"title": "This property is following safety and hygiene measures", "iconUrl": "https://promos.makemytrip.com/COVID/safe.png", "itemIconType": "", "data": ["Hygienic Rooms", "Trained Staff", "Sanitized Indoors", "Safe Dining"]}}, "cityName": "Delhi", "countryName": "India", "countryCode": "IN", "cityCode": "CTDEL", "stayType": "Hotel", "maxOccupancy": 0, "ignoreEntireProperty": false, "locationPersuasion": ["Indira Gandhi International Airport", "850 m from Hotel Venus", "1.5 km from T3 - Delhi Airport (IGI Airport)"], "isAbsorptionApplied": false, "featured": "N", "isSoldOut": false, "isFreeWifiAvail": false, "isPAHAvailable": false, "isShortlisted": false, "hotelFacilities": "Basic Facilities,Transfers,Family and kids,Food and Drinks,Payment Services,Safety and Security,Media and technology,General Services,Common Area,Shopping,Business Center and Conferences", "geoLocation": {"latitude": "28.55358", "longitude": "77.09858"}, "promotions": {"opaquePromotion": {"techShowOpaque": false}, "promotionsCount": 0}, "inclusions": [], "isMTKeyEnabledSearch": false, "poiTag": "1.5 km from T3 - Delhi Airport (IGI Airport)", "isValuePlus": false, "isFreeCancellation": false, "freeCancellationAvailable": false, "breakFast": false, "breakFastAvailable": true, "shortDescription": "Well set in the Mahipalpur district of New Delhi, Smart Suites is located 11 km from Qutub Minar, 12 km from Gandhi Smriti and 1 ", "userEligiblePayMode": "PAS", "couponAutoApply": true, "isPAHTariffAvailable": true, "paymentMode": "PAS", "reasonToBelieve": ["850 m from Hotel Venus"], "matchScore": 95.29411764705883, "avgTP": 0.0, "persuasions": [{"id": "581", "desc": "Great Choice! Booked 50+ times in last 15 Days", "placeholder": "SingleLine", "priority": 8002, "theme": "URGENCY"}], "hasCollections": false, "bedCount": 0, "mealPlanIncluded": {"desc": "Room Only", "code": "EP", "cost": null}, "segments": {"segmentList": {"1120": {"id": "1120", "channelSegment": "ALL", "userSegment": "REGULAR"}, "1180": {"id": "1180", "channelSegment": "MY_PARTNER", "userSegment": "REGULAR"}, "1121": {"id": "1121", "channelSegment": "MOB", "userSegment": "REGULAR"}, "1134": {"id": "1134", "channelSegment": "AFF", "userSegment": "REGULAR"}, "1126": {"id": "1126", "channelSegment": "ALL", "userSegment": "LOGGEDIN"}}, "segmentsCount": 5}, "lowestRateSupplierCode": "INGO", "lowestRateSegmentId": "1120", "stateCode": "STDEL", "stateName": "Delhi", "lowestRoomAvailCount": 1, "totalRoomAvailCount": 16, "isBNPLAvailable": false, "bnplBaseAmount": 0.0, "pahWalletApplicable": false, "bestPriceGuaranteed": false, "pahxAvailable": false, "pahxApplicable": false, "addOnAvailableOnLowest": false, "addOnAvailable": false, "sponsored": false, "listingType": "Room By Room", "facilityHighlights": ["Free Parking", "Childcare Services", "Free Wi-Fi", "Kitchenette", "Cafe", "Elevator/Lift", "Air Conditioning", "24-hour Room Service", "Power Backup", "Restaurant", "Doctor on Call"], "facilityCategorization": [{"subcat": "Parking", "name": "hotel"}, {"subcat": "Wifi", "name": "hotel"}, {"subcat": "Kitchen/Kitchenette", "name": "hotel"}, {"subcat": "Elevator/ Lift", "name": "hotel"}, {"subcat": "Air Conditioning", "name": "hotel"}, {"subcat": "Room service", "name": "hotel"}, {"subcat": "Power backup", "name": "hotel"}, {"subcat": "Dry Cleaning services", "name": "hotel"}, {"subcat": "Smoking rooms", "name": "hotel"}, {"subcat": "Intercom", "name": "hotel"}, {"subcat": "Refrigerator", "name": "hotel"}, {"subcat": "Housekeeping", "name": "hotel"}, {"subcat": "Laundromat", "name": "hotel"}, {"subcat": "<PERSON><PERSON><PERSON>", "name": "hotel"}, {"subcat": "Newspaper", "name": "hotel"}, {"subcat": "Smoke detector", "name": "hotel"}, {"subcat": "Telephone", "name": "hotel"}, {"subcat": "Bathroom", "name": "hotel"}, {"subcat": "LAN", "name": "hotel"}, {"subcat": "Ironing services", "name": "hotel"}, {"subcat": "Bus Station transfers", "name": "hotel"}, {"subcat": "Airport Transfers", "name": "hotel"}, {"subcat": "Pickup/ Drop", "name": "hotel"}, {"subcat": "Shuttle Service", "name": "hotel"}, {"subcat": "Railway Station Transfers", "name": "hotel"}, {"subcat": "Childcare service", "name": "hotel"}, {"subcat": "Cafe", "name": "hotel"}, {"subcat": "Restaurant", "name": "hotel"}, {"subcat": "Dining Area", "name": "hotel"}, {"subcat": "Coffee shop", "name": "hotel"}, {"subcat": "Kids meals", "name": "hotel"}, {"subcat": "ATM", "name": "hotel"}, {"subcat": "Currency Exchange", "name": "hotel"}, {"subcat": "CCTV", "name": "hotel"}, {"subcat": "Fire extinguishers", "name": "hotel"}, {"subcat": "Safety and Security", "name": "hotel"}, {"subcat": "Security", "name": "hotel"}, {"subcat": "Electrical Adapters Available", "name": "hotel"}, {"subcat": "Electrical chargers", "name": "hotel"}, {"subcat": "TV", "name": "hotel"}, {"subcat": "Doctor on call", "name": "hotel"}, {"subcat": "Luggage storage", "name": "hotel"}, {"subcat": "Concierge", "name": "hotel"}, {"subcat": "Butler Services", "name": "hotel"}, {"subcat": "Multilingual Staff", "name": "hotel"}, {"subcat": "Mail services", "name": "hotel"}, {"subcat": "Luggage assistance", "name": "hotel"}, {"subcat": "Medical centre", "name": "hotel"}, {"subcat": "Ticket/ Tour Assistance", "name": "hotel"}, {"subcat": "Bellboy service", "name": "hotel"}, {"subcat": "Caretaker", "name": "hotel"}, {"subcat": "Wake-up Call / Service", "name": "hotel"}, {"subcat": "Postal services", "name": "hotel"}, {"subcat": "Lounge", "name": "hotel"}, {"subcat": "Fireplace", "name": "hotel"}, {"subcat": "Reception", "name": "hotel"}, {"subcat": "Balcony/ Terrace", "name": "hotel"}, {"subcat": "Seating Area", "name": "hotel"}, {"subcat": "Souvenir shop", "name": "hotel"}, {"subcat": "Jewellery Shop", "name": "hotel"}, {"subcat": "Grocery", "name": "hotel"}, {"subcat": "Shops", "name": "hotel"}, {"subcat": "Book shop", "name": "hotel"}, {"subcat": "Printer", "name": "hotel"}, {"subcat": "Photocopying", "name": "hotel"}, {"subcat": "Business services", "name": "hotel"}, {"subcat": "Business Center", "name": "hotel"}, {"subcat": "Fax service", "name": "hotel"}], "propertyActivities": [], "trackingInfo": null, "blackAccelerated": false, "maxAdultAtSamePrice": 0, "bedRoomCount": 0, "maxChildAtSamePrice": 0, "alternateDatesAvailable": false, "lowestBlackPackage": false, "blackPackageAvailable": false, "netRateAvailable": false, "lowestNetRate": false, "roomCount": 0, "poiTagList": [{"hotelId": "201909301801331019", "poiId": "POI47726", "poiName": "T3 - Delhi Airport (IGI Airport)", "poiCategory": "Airport", "poiPriority": "0", "drivingDistance": "1500.0", "drivingDistanceText": "1.5 km", "drivingTimeText": "", "bestPoiPriority": 0}], "lastBooked": false, "lowestRatePlanMealDiff": 0.0, "lowestRatePlanPahDiff": 0.0, "notInterested": false, "lowestRoomCodesRatePlans": "2250--990001446719:MSE:1120:MSE:INGO", "extraMeals": {"desc": "Breakfast", "code": "CP", "cost": null}, "priceDifferenceWithPivot": 0.0, "lowestRateBnpl": false, "staycationAvailable": false, "staycationRateAvlblForFilter": false, "lowestRateStaycation": false, "shortList": false, "dealOfDayApplicable": false, "topHotel": false, "pahmode": "PAS", "recommendedMultiRoom": false, "recommendCouponBit": true, "altAcco": false}], "nearbyHotelList": [], "otherAltAccoHotelList": [], "nonAltAccoHotelList": [], "otherAltAccoHeading": "Remaining Alt acco properties in Delhi", "nonAltAccoHeading": "Showing other properties in Delhi", "cityName": "Delhi", "countryName": "India", "countryCode": "IN", "cityCode": "CTDEL", "failureReason": {}, "sortCriteria": {"field": "S_MM_hsq607_B_SP", "order": "asc", "dsAlgoVersion": null, "defaultRankSeq": null}, "correlationKey": "2bb0327a-5cba-4f01-a05c-48cdd1b56353", "filtersRemoved": false, "suppressPas": false, "firstTimeUser": false, "blackEligible": false, "blackEligibilityDays": 0, "lastFetchedHotelId": "201909301801331019", "usradid": "testus<PERSON><PERSON>", "currency": "inr", "currencyConvFactorINR": 1.0, "appliedFiltersMap": {}, "expData": {"APE": "10", "PAH": "5", "PAH5": "T", "WPAH": "F", "BNPL": "T", "MRS": "T", "PDO": "PN", "MCUR": "T", "ADDON": "T", "CHPC": "T", "AARI": "T", "NLP": "Y", "RCPN": "T", "PLRS": "T", "MMRVER": "V3", "BLACK": "T", "EMIDT": "2", "VIDEO": "2", "hotelDetailCardOrder": "rc0,qb1,man2,ws3,brn4,blk5,gr6,rr7,ef8,mbg9,ex10,ps11,aac12,alpd13,pd14,au15,db16,bpg17,am18,lcn19,aga20,ta21,ty22,cc23,fb24", "showSlider": "true", "allowPayLater": "false", "isWBTHPWA": "true", "FilterVisibilityDesktop": "true", "multicurrencyEnabled ": "false", "htlPolarisCard": "true", "hotelStarHost": "false", "AATEST": "0", "pahBlocker": "false", "htlAltAccoCollection": "false", "desktopImgSizeFactorDetail": "1.75", "enablePolarisIntl": "false", "recommendationOnSoldout": "true", "desktopImgSizeFactorListing": "1.0", "travellerImageBucketsSectionExp": "false", "newListingCorporate": "false", "enableNewAboutProperty": "false", "test_config_str": "test", "specialInvalidCorporate": "false", "hotelNewMapViewShowRegion": "false", "enableCorpMultiroom": "false", "locusLocationFilterEnabled": "true", "htlShowAcmeFrgament": "true", "showVideoOnDetail": "true", "DynamicDiscount": "10", "homePageReviewCollectionPopUp": "false", "newAboutPropertyCard": "false", "imgSizeFactorExp": "true", "hotelNewMapView": "true", "showTax": "true", "specialRequestCorporate": "true", "showTax_DT": "false", "htl_show_pan_card_optional": "1", "hotelDetailV2": "false", "altAccoNewLanding": "false", "NewHeader": "false", "LocusDTFunnelHotels": "true", "bookingReviewFooterV2": "true", "enableMultiCurrency": "false", "htlShowNearByHotels": "false", "mobGenConfigVariant": "A", "AAEXP": "1", "reviewDetailCardOrderB2C": "pd,dbc,pr,bpb,cd,af,td,sr,tc,tcv2,pb", "dtShowNewReviewPg_corp": "false", "htlGetaways": "false", "htl_new_thank_you_corp": "true", "pwaImgSizeFactorListing": "1.0", "imageDeviceResolution": "true", "mapExploration": "false", "videoPositionOnListing": "0", "htlDarkFilterUiWithMapIcon": "false", "corpPayLaterTimeLine": "true", "dtShowNewReviewPg": "false", "rmStarRatingAltAcco": "false", "EMIDT_NEW": "1", "locusLocationFilterDisabledUI": "false", "htl_pricing_experiment_name": "PN", "htlLocusEnabledCorp": "false", "htl_Cross_Sell_Comparator": "2", "AndroidImageE2Hotels": "true", "htlAltAccoWidgetType": "1", "payLaterListingAnimation": "false", "altAccoLandingTravelTypeView": "0", "htlB2CThankYouV2": "true", "hotelDetailCardorder_ios": "RC1,<PERSON><PERSON>2,WPM3,<PERSON><PERSON>4,WOO5,QB6,RD7,<PERSON>G8,AAH9,SA10,EF11,WBH12,HRP13,TA14,HO15,AM16,ATH17,DB18,HMD19,<PERSON>L20,AAM21,HPC22", "htlPolarisCardIntl": "false", "htlDetailMapV2": "false", "newHeaderFilterCorp": "true", "htldetailCompareWidget": "1", "consumeManualFlyFishDom": "false", "listingMap": "true", "consumeManualFlyFishIntl": "true", "myBizHotelPricing": "false", "showSponsoredTag": "true", "htlCorpWidgetType": "0", "listingHeaderFooterFilterExp": "false", "FilterFlow": "OLD", "showRatingText": "false", "htl_locus_enabled_corp": "false", "checkoutReviewSessionCount": "3", "policyvariant": "false", "noCharityAutoCheck": "false", "imageDeviceResolutionFactor": "1.0", "showSavedCards": "false", "autoPlayVideo": "false", "showNewListing": "true", "showPancard": "true", "Pay Later Desktop": "false", "showTaxAndServiceFee": "false", "htl_new_thank_you_b2c": "true", "corpBookingReviewV2": "false", "filterBasedPrice": "false", "isWBTH": "true", "newReviewPageBottomToolBarEnabled": "false", "isPayLaterAnimationEnabled": "false", "NewTY_b2c": "false", "hotelGalleryV2": "false", "NewTY_b2b": "false", "showTax_PWA": "false", "bookingReviewV2": "true", "fphDiscount": "false", "enableGuidedSearch": "false", "corpApprovalUI": "false", "pwaCheckoutBtnSticky": "false", "gstOnTripTags": "true", "isDonAmtFive": "false", "checkoutReviewDaysCount": "3", "htlEnableQuickDatesAltAcco": "false", "newListingHotelCorporate": "false", "pwaImgSizeFactorDetail": "1.0", "hotel_new_ugc_card": "false", "IAB_Details": "IAB_F", "filterBasePricing": "false", "corplEnableSavedLocations": "true", "pwaShowNewReviewPg": "true", "isRatingText": "true", "forkFcnr": "false", "htl_locus_enabled_altAcco": "false", "LocusDTFunnel": "true", "noAutoCheckBoxes": "true", "showTaxonBoarding": "false", "enableNewPaymentScreen": "true", "shouldLaunchGamification": "false", "review_intent_show": "true", "whichAreaGuide": "LOCATION_FILTER", "htlQuickBookRatingBar": "1", "htlNewHeaderFilterUi": "false", "showAltAccoFilterintl": "false", "htlReviewNewUI": "true", "apiExperiment": "10", "payLaterTimeLine": "false", "BNVsSR": "100", "htlGCCThankYouV2": "false", "loadNewStatic": "false", "showSelectRoomMoreInfoCombo": "false", "htl_brin_new_ui": "false", "enablePolarisFlow": "true", "showMapCorporate": "false", "forcedLogin": "DEFAULT_FLOW", "HIS": "DEFAULT", "customerReviewNewDesign": "false", "showNewBrinAnimation": "false", "showSocialLogin": "true", "listingHeaderWithMapIconExp": "false", "showMyraChatbot": "true", "showEnhancedSeekReviews ": "true", "shouldSuggestGetaways": "false", "hotelDetailsV2": "false", "shouldshowQBRReviewTags": "false", "htlPricingExperiment": "PN", "htlDetailNewRatingCard": "false", "htl_new_thank_you_gcc": "true", "msh": "0", "htlB2BThankYouV2": "true", "mapSearchBar": "true", "corpHotelDetailCardOrder": "rc0,hfc1,hbc2,qb3,man4,ws5,brn6,blk7,ap8,pd9,ef10,mbg11,ps12,ex13,rr14,aac15,alpd16,lcn17,au18,db19,bpg20,am21,gr22,aga23,ta24,ty25,cc26,fb27", "htlLocusEnabledAltAcco": "true", "personalEnableSavedLocations": "true", "showVideos": "false", "test_config": "true", "newReviewPageEnabled": "true", "isPayLaterEnabled": "false", "SPCR": "2", "corporateMultiroom": "false", "hotelWidgetType": "3", "APEINTL": "36", "isMoCharter": "true", "newCorpApprovalPage": "true", "3.\thtlNewHeaderFilterUi": "false", "shouldHideAltAccoSearchDateSelection": "true", "PAYNEW": "false", "enableLocusLocationFilter": "false", "imageParamChanges": "true", "selectroomvariant": "3", "payAtHotelB2B": "false", "allowNRRatesCorporate": "true", "sponsoredHotelsEnabled": "true", "shouldEnableNearByHotelList": "false", "hotelImageCount": "100", "imagePrefetchEnabled": "false", "galleryDesign": "true", "htlNewHeaderFilterUiWithMapIcon": "false", "corpShowSimilarHtls": "false", "hotelSequenceCorporate": "sign-hsq130", "htlShowNewGallery": "false", "DPCR": "0", "corpFCtimeline": "true", "myBizHotelPricingiOS": "false", "htlBrinV2": "110", "selectRoomB2C": "false", "hotelMobConfigVariant": "C", "NewHotelMaps": "true", "subConceptsNewDesign": "false", "NewTY_gcc": "false", "htlDummyPanCardExperiment": "1", "isPwaForcedLogin": "true", "showNewAltAccoFilterIntl": "false", "myBizHotelPricingAndroid": "true"}, "locusData": {"locusId": "CTDEL", "locusType": "city", "locusName": "Delhi"}, "singularityResponseDrivingDistance": false, "filterBasedRates": false, "commonHotelCategories": [], "commonHotelCategoryDetails": {}, "hotelCountInCity": 0, "hotelCountInViewPort": 0, "matchMakerResponse": true, "personalizedResponse": [{"section": "RECOMMENDED_HOTELS", "subHeading": null, "minCardCount": null, "count": 2, "categoryPersuasions": null, "hotelRatesMap": null, "heading": "Popular in Delhi", "horizontal": false, "hotelCardType": null, "seeMoreCTA": null, "orderPriority": 0, "headingParameters": null, "myBizSimilarToDirectHotel": null, "isHeadingVisible": false, "cardInsertionAllowed": false, "isMyBizAssuredRecommended": false, "hotels": [{"id": "20131126130003948", "name": "The Roseate", "propertyType": "Hotel", "propertyLabel": "Hotel", "stayType": "Hotel", "starRating": 5, "soldOut": false, "groupBookingHotel": false, "groupBookingPrice": false, "maskedPrice": false, "isGroupBookingForSimilar": false, "extraMeals": {"desc": "All meals - Breakfast, Lunch & Dinner", "code": "AP"}, "alternateDates": false, "multiRoomRecommendation": false, "isAltAcco": false, "mtKey": "defaultMtkey", "totalImageCount": 10, "travellerImageCount": 0, "categories": ["Festive Weekend Deals", "Super_Premium_seo", "Package Offer", "<PERSON><PERSON><PERSON>", "Premium", "Child Friendly", "Premium Value Packages", "luxury_hotels", "Inmarket", "Daily Dhamaka", "MySafety-Third Party Reviewed", "Flyer Deal", "Super Deals", "Family Fun", "Romantic Stays", "Spa and Wellness", "Luxurious Business Stays", "HighFiveV2_NonChain_Target", "<PERSON>", "Premium Properties", "Super Premium", "City Center Hotels"], "locationPersuasion": ["Mahipalpur", "8.0 km from Indira Gandhi International Airport"], "media": [{"url": "//r1imghtlak.mmtcdn.com/e3e11798805211e8b9110262b38ef0b6.jpg?&output-quality=75&downsize=243:162&crop=243:162;10,0&output-format=webp", "mediaType": "IMAGE"}, {"url": "//r1imghtlak.mmtcdn.com/78fb0ece9e0011ebaa530242ac110002.jpg?&output-quality=75&downsize=243:162&crop=243:162;0,40&output-format=webp", "mediaType": "IMAGE"}, {"url": "//r1imghtlak.mmtcdn.com/722fdec8334d11e4be6cdaf4768ad8d9.jfif?&output-quality=75&downsize=243:162&crop=243:162;0,6&output-format=webp", "mediaType": "IMAGE"}, {"url": "//r1imghtlak.mmtcdn.com/6619a1a49e0411eb81e30242ac110002.jpg?&output-quality=75&downsize=243:162&crop=243:162;0,40&output-format=webp", "mediaType": "IMAGE"}, {"url": "//r1imghtlak.mmtcdn.com/e759b3c6805211e897e90283eb712168.jpg?&output-quality=75&downsize=243:162&crop=243:162;0,10&output-format=webp", "mediaType": "IMAGE"}], "priceDetail": {"price": 11114, "priceWithTax": 13115, "discountedPrice": 8955, "discountedPriceWithTax": 10956, "totalTax": 2001, "emiDetails": {"type": "NO_COST", "tenure": 6, "totalInterest": 0, "bankName": "OneCard", "amount": 1826, "totalCost": 10956}, "coupon": {"code": "MYSUPERDEAL", "description": "Get additional Promo Cash of Rs 1111 on this hotel", "specialPromo": false, "type": "DEAL_ECPN", "couponAmount": 2131, "autoApplicable": false, "bnplAllowed": false, "disabled": false, "bankOffer": false}, "pricingKey": "s9rHC9RN7n+PgLJP6Mxikt+qdLRDfR7vxXXVPpyz3qw8asFSg9uKU5PLR6Bcy/w5HRAYvuaHNyQauEkwMeN15RErkzsggTQSyzo88e28tLOU84Zp9+f1ui7Nr1SNonrdiX2H4h7N4BJwqi8BiOYQFXQREl3EHoJ1geDN/xpURrHKBgAVuRdXC9kiXL4LqizEdeV9LJ1lxIbQJMK9aZSA083CdPa7SiNMrnS053hYHZtzta/hG4y5qQ9ez9OMDj4RJxnzmjo3zM0krZ4YoIQjDNcQCaQWqIL7120nNNp3f2YzA7vf5w0CUJacyjK9G+8MGPDHlc+Sq2dQ/lezEC8Fjxvwr+dng/Bp", "myPartnerDiscount": 2159, "groupPriceText": "<span class=\"latoBlack\">Total ₹10,956</span><br>for 1 Night, 0 Rooms"}, "hotelPersuasions": {}, "reviewSummary": {"source": "MMT", "cumulativeRating": 4.3, "totalReviewCount": 1198, "totalRatingCount": 2334, "hotelRatingSummary": [{"concept": "Location", "displayText": "Location", "value": 4.6, "show": true, "reviewCount": 312, "heroTag": false, "subConcepts": [{"sentiment": "POSITIVE", "subConcept": "Location", "displayText": "Location", "relatedReviewCount": 63, "priorityScore": 63, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Nightlife", "displayText": "Nightlife", "relatedReviewCount": 3, "priorityScore": 3, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "NEUTRAL", "subConcept": "Connectivity", "displayText": "Connectivity", "relatedReviewCount": 2, "priorityScore": 2, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Market", "displayText": "Market", "relatedReviewCount": 1, "priorityScore": 1, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Good Location", "displayText": "Good Location", "relatedReviewCount": 61, "priorityScore": 61, "tagType": "WHAT_GUESTS_SAY", "source": "MMT_UGC"}]}, {"concept": "Hospitality", "displayText": "Hospitality", "value": 4.6, "show": true, "reviewCount": 513, "heroTag": false, "subConcepts": [{"sentiment": "POSITIVE", "subConcept": "Staff Courtesy", "displayText": "Staff Courtesy", "relatedReviewCount": 235, "priorityScore": 235, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "NEUTRAL", "subConcept": "Service Quality", "displayText": "Service Quality", "relatedReviewCount": 151, "priorityScore": 151, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "NEUTRAL", "subConcept": "Check-in", "displayText": "Check-in", "relatedReviewCount": 1, "priorityScore": 1, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Courteous Staff", "displayText": "Courteous Staff", "relatedReviewCount": 228, "priorityScore": 228, "tagType": "WHAT_GUESTS_SAY", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Good Service", "displayText": "Good Service", "relatedReviewCount": 107, "priorityScore": 107, "tagType": "WHAT_GUESTS_SAY", "source": "MMT_UGC"}]}, {"concept": "Room", "displayText": "Room", "value": 4.5, "show": true, "reviewCount": 428, "heroTag": false, "subConcepts": [{"sentiment": "POSITIVE", "subConcept": "Room Quality", "displayText": "Room Quality", "relatedReviewCount": 135, "priorityScore": 135, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Space in Rooms", "displayText": "Space in Rooms", "relatedReviewCount": 64, "priorityScore": 64, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Bed Quality", "displayText": "Bed Quality", "relatedReviewCount": 4, "priorityScore": 4, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "NEUTRAL", "subConcept": "Room Amenities", "displayText": "Room Amenities", "relatedReviewCount": 3, "priorityScore": 3, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "NEUTRAL", "subConcept": "Bathroom Hygiene", "displayText": "Bathroom Hygiene", "relatedReviewCount": 2, "priorityScore": 2, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "NEGATIVE", "subConcept": "Balcony", "displayText": "Balcony", "relatedReviewCount": 2, "priorityScore": 2, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Good Room", "displayText": "Good Room", "relatedReviewCount": 190, "priorityScore": 190, "tagType": "WHAT_GUESTS_SAY", "source": "MMT_UGC"}]}, {"concept": "Cleanliness", "displayText": "Cleanliness", "value": 4.5, "show": true, "reviewCount": 1165, "heroTag": false, "subConcepts": [{"sentiment": "POSITIVE", "subConcept": "Resort Cleanliness", "displayText": "Resort Cleanliness", "relatedReviewCount": 46, "priorityScore": 46, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Room Cleanliness", "displayText": "Room Cleanliness", "relatedReviewCount": 29, "priorityScore": 29, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Property Cleanliness", "displayText": "Property Cleanliness", "relatedReviewCount": 5, "priorityScore": 5, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Ambience Cleanliness", "displayText": "Ambience Cleanliness", "relatedReviewCount": 3, "priorityScore": 3, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "NEUTRAL", "subConcept": "Bathroom Cleanliness", "displayText": "Bathroom Cleanliness", "relatedReviewCount": 3, "priorityScore": 3, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Food Cleanliness", "displayText": "Food Cleanliness", "relatedReviewCount": 3, "priorityScore": 3, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Hotel Cleanliness", "displayText": "Hotel Cleanliness", "relatedReviewCount": 3, "priorityScore": 3, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Pool Cleanliness", "displayText": "Pool Cleanliness", "relatedReviewCount": 2, "priorityScore": 2, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "NEUTRAL", "subConcept": "Surroundings Cleanliness", "displayText": "Surroundings Cleanliness", "relatedReviewCount": 1, "priorityScore": 1, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "NEUTRAL", "subConcept": "Area Cleanliness", "displayText": "Area Cleanliness", "relatedReviewCount": 1, "priorityScore": 1, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Atmosphere Cleanliness", "displayText": "Atmosphere Cleanliness", "relatedReviewCount": 1, "priorityScore": 1, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Place Cleanliness", "displayText": "Place Cleanliness", "relatedReviewCount": 1, "priorityScore": 1, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Staff Cleanliness", "displayText": "Staff Cleanliness", "relatedReviewCount": 1, "priorityScore": 1, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "NEUTRAL", "subConcept": "Linen Cleanliness", "displayText": "Linen Cleanliness", "relatedReviewCount": 1, "priorityScore": 1, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "NEGATIVE", "subConcept": "<PERSON><PERSON><PERSON>", "displayText": "<PERSON><PERSON><PERSON>", "relatedReviewCount": 1, "priorityScore": 1, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Clean Property", "displayText": "Clean Property", "relatedReviewCount": 50, "priorityScore": 9900, "tagType": "WHAT_GUESTS_SAY", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Clean Room", "displayText": "Clean Room", "relatedReviewCount": 29, "priorityScore": 10000, "tagType": "WHAT_GUESTS_SAY", "source": "MMT_UGC"}]}, {"concept": "Amenities", "displayText": "Amenities", "value": 4.3, "show": true, "reviewCount": 343, "heroTag": false, "subConcepts": [{"sentiment": "POSITIVE", "subConcept": "<PERSON><PERSON>", "displayText": "<PERSON><PERSON>", "relatedReviewCount": 35, "priorityScore": 35, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Pool", "displayText": "Pool", "relatedReviewCount": 30, "priorityScore": 30, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Spa/Massage", "displayText": "Spa/Massage", "relatedReviewCount": 13, "priorityScore": 13, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "NEUTRAL", "subConcept": "In-House Activities", "displayText": "In-House Activities", "relatedReviewCount": 6, "priorityScore": 6, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "NEUTRAL", "subConcept": "Wifi", "displayText": "Wifi", "relatedReviewCount": 2, "priorityScore": 2, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "NEUTRAL", "subConcept": "Airport Transfers", "displayText": "Airport Transfers", "relatedReviewCount": 2, "priorityScore": 2, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Good Pool", "displayText": "Good Pool", "relatedReviewCount": 28, "priorityScore": 28, "tagType": "WHAT_GUESTS_SAY", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Good In-House Activities", "displayText": "Good In-House Activities", "relatedReviewCount": 5, "priorityScore": 5, "tagType": "WHAT_GUESTS_SAY", "source": "MMT_UGC"}]}, {"concept": "Food", "displayText": "Food", "value": 4.2, "show": true, "reviewCount": 1032, "heroTag": false, "subConcepts": [{"sentiment": "POSITIVE", "subConcept": "Food", "displayText": "Food", "relatedReviewCount": 171, "priorityScore": 171, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Breakfast", "displayText": "Breakfast", "relatedReviewCount": 52, "priorityScore": 52, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Restaurant", "displayText": "Restaurant", "relatedReviewCount": 13, "priorityScore": 13, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Good Food", "displayText": "Good Food", "relatedReviewCount": 156, "priorityScore": 156, "tagType": "WHAT_GUESTS_SAY", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Good Breakfast", "displayText": "Good Breakfast", "relatedReviewCount": 48, "priorityScore": 48, "tagType": "WHAT_GUESTS_SAY", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Good Restaurant", "displayText": "Good Restaurant", "relatedReviewCount": 13, "priorityScore": 13, "tagType": "WHAT_GUESTS_SAY", "source": "MMT_UGC"}]}, {"concept": "Child friendliness", "displayText": "Child friendliness", "value": 4.1, "show": true, "reviewCount": 140, "heroTag": false}, {"concept": "Value for Money", "displayText": "Value for Money", "value": 4.1, "show": true, "reviewCount": 240, "heroTag": false, "subConcepts": [{"sentiment": "NEUTRAL", "subConcept": "Luxury", "displayText": "Luxury", "relatedReviewCount": 27, "priorityScore": 27, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Value for Money", "displayText": "Value for Money", "relatedReviewCount": 13, "priorityScore": 13, "tagType": "BASE", "source": "MMT_UGC"}, {"sentiment": "POSITIVE", "subConcept": "Value for Money", "displayText": "Value for Money", "relatedReviewCount": 13, "priorityScore": 13, "tagType": "WHAT_GUESTS_SAY", "source": "MMT_UGC"}]}, {"concept": "USP", "displayText": "USP", "value": 0, "show": false, "reviewCount": 0, "heroTag": false, "subConcepts": [{"sentiment": "POSITIVE", "subConcept": "Amazing Stay", "displayText": "Amazing Stay", "relatedReviewCount": 140, "priorityScore": 1140, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Respectful Staff", "displayText": "Respectful Staff", "relatedReviewCount": 112, "priorityScore": 1112, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Lovely Food", "displayText": "Lovely Food", "relatedReviewCount": 99, "priorityScore": 1099, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Excellent Service", "displayText": "Excellent Service", "relatedReviewCount": 53, "priorityScore": 1053, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Delicious Food", "displayText": "Delicious Food", "relatedReviewCount": 50, "priorityScore": 1050, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Beautiful Place", "displayText": "Beautiful Place", "relatedReviewCount": 48, "priorityScore": 1048, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Outstanding Service", "displayText": "Outstanding Service", "relatedReviewCount": 48, "priorityScore": 1048, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Neat Tidy", "displayText": "Neat Tidy", "relatedReviewCount": 26, "priorityScore": 1026, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Helpful Staff", "displayText": "Helpful Staff", "relatedReviewCount": 19, "priorityScore": 1019, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Friendly Staff", "displayText": "Friendly Staff", "relatedReviewCount": 16, "priorityScore": 1016, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Handy Location", "displayText": "Handy Location", "relatedReviewCount": 15, "priorityScore": 1015, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Comfortable Bedroom", "displayText": "Comfortable Bedroom", "relatedReviewCount": 12, "priorityScore": 1012, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Nice Facilities", "displayText": "Nice Facilities", "relatedReviewCount": 10, "priorityScore": 1010, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Impressive Breakfast", "displayText": "Impressive Breakfast", "relatedReviewCount": 10, "priorityScore": 1010, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Soothing Atmosphere", "displayText": "Soothing Atmosphere", "relatedReviewCount": 9, "priorityScore": 1009, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Value Money", "displayText": "Value Money", "relatedReviewCount": 9, "priorityScore": 1009, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Good Hygiene", "displayText": "Good Hygiene", "relatedReviewCount": 8, "priorityScore": 1008, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Beautiful Architecture", "displayText": "Beautiful Architecture", "relatedReviewCount": 7, "priorityScore": 1007, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Peaceful Retreat", "displayText": "Peaceful Retreat", "relatedReviewCount": 7, "priorityScore": 1007, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Beautiful Views", "displayText": "Beautiful Views", "relatedReviewCount": 7, "priorityScore": 1007, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Spacious And Modern", "displayText": "Spacious And Modern", "relatedReviewCount": 7, "priorityScore": 1007, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Pleasant Atmosphere", "displayText": "Pleasant Atmosphere", "relatedReviewCount": 6, "priorityScore": 1006, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "<PERSON><PERSON><PERSON>", "displayText": "<PERSON><PERSON><PERSON>", "relatedReviewCount": 6, "priorityScore": 1006, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Good Hospitality ", "displayText": "Good Hospitality ", "relatedReviewCount": 5, "priorityScore": 1005, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Staff Courtesy", "displayText": "Staff Courtesy", "relatedReviewCount": 5, "priorityScore": 1005, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Luxury And Comfort", "displayText": "Luxury And Comfort", "relatedReviewCount": 4, "priorityScore": 1004, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Comfortable Rooms", "displayText": "Comfortable Rooms", "relatedReviewCount": 3, "priorityScore": 1003, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Excellent Views", "displayText": "Excellent Views", "relatedReviewCount": 3, "priorityScore": 1003, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Location", "displayText": "Location", "relatedReviewCount": 3, "priorityScore": 1003, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Water Bodies", "displayText": "Water Bodies", "relatedReviewCount": 3, "priorityScore": 1003, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Multiple Water Bodies", "displayText": "Multiple Water Bodies", "relatedReviewCount": 3, "priorityScore": 1003, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Impressive Hospitality", "displayText": "Impressive Hospitality", "relatedReviewCount": 3, "priorityScore": 1003, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Mind Blowing Architecture", "displayText": "Mind Blowing Architecture", "relatedReviewCount": 3, "priorityScore": 1003, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Beautiful Pool", "displayText": "Beautiful Pool", "relatedReviewCount": 3, "priorityScore": 1003, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Classy Hygiene Standards", "displayText": "Classy Hygiene Standards", "relatedReviewCount": 3, "priorityScore": 1003, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Needs Improvement", "displayText": "Needs Improvement", "relatedReviewCount": 3, "priorityScore": 1003, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Close To Nature", "displayText": "Close To Nature", "relatedReviewCount": 3, "priorityScore": 1003, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Breathtaking Property", "displayText": "Breathtaking Property", "relatedReviewCount": 3, "priorityScore": 1003, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Luxurious Celebration", "displayText": "Luxurious Celebration", "relatedReviewCount": 2, "priorityScore": 1002, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Super Helpful Staff", "displayText": "Super Helpful Staff", "relatedReviewCount": 2, "priorityScore": 1002, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Luxurious Resort", "displayText": "Luxurious Resort", "relatedReviewCount": 2, "priorityScore": 1002, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Beautiful Resort", "displayText": "Beautiful Resort", "relatedReviewCount": 2, "priorityScore": 1002, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Great Rates", "displayText": "Great Rates", "relatedReviewCount": 2, "priorityScore": 1002, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Clean Rooms ", "displayText": "Clean Rooms ", "relatedReviewCount": 2, "priorityScore": 1002, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Beautiful Design", "displayText": "Beautiful Design", "relatedReviewCount": 2, "priorityScore": 1002, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Location Is Superb", "displayText": "Location Is Superb", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Convenient Location", "displayText": "Convenient Location", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Ordinary Breakfast Buffet", "displayText": "Ordinary Breakfast Buffet", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Architectural Luxury", "displayText": "Architectural Luxury", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Amazing Architecture", "displayText": "Amazing Architecture", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Lavish Breakfast", "displayText": "Lavish Breakfast", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Quick Destination", "displayText": "Quick Destination", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Near Airport", "displayText": "Near Airport", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Luxurious Weekend Getaway", "displayText": "Luxurious Weekend Getaway", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Nice Decor", "displayText": "Nice Decor", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Signs And Lights", "displayText": "Signs And Lights", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Best In Class", "displayText": "Best In Class", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Innovative Property", "displayText": "Innovative Property", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Old Upkeep", "displayText": "Old Upkeep", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Steam Bath", "displayText": "Steam Bath", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Comfortable & Spacious Rooms", "displayText": "Comfortable & Spacious Rooms", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Good Breakfast Variety", "displayText": "Good Breakfast Variety", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Space in Rooms", "displayText": "Space in Rooms", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Class Apart", "displayText": "Class Apart", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Beautiful Garden", "displayText": "Beautiful Garden", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Luxury Experience", "displayText": "Luxury Experience", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Personalized Services", "displayText": "Personalized Services", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Premium Experience", "displayText": "Premium Experience", "relatedReviewCount": 1, "priorityScore": 1001, "tagType": "BASE", "source": "CGPT"}, {"sentiment": "POSITIVE", "subConcept": "Courteous Staff", "displayText": "Courteous Staff", "relatedReviewCount": 5, "priorityScore": 1005, "tagType": "WHAT_GUESTS_SAY", "source": "CGPT"}]}], "seekTagDetails": {"seekTagsTitle": "Traveller Impressions", "seekTagsSubtitle": "Powered by GPT using recent user reviews", "maxSeekTagCount": 10, "defaultSeekTagCount": 5}, "ratingText": "Excellent", "preferredOTA": true}, "locationDetail": {"id": "CTDEL", "name": "Delhi", "type": "city", "countryId": "IN", "countryName": "India"}, "geoLocation": {"latitude": 28.53183, "longitude": 77.10555}, "freeCancellationText": "Free Cancellation", "shortList": false, "totalRoomCount": 1, "trackingInfo": null, "sponsored": false, "newType": false, "poiTag": "8.0 km from Indira Gandhi International Airport", "mmtHotelCategory": "LUXE", "heroImage": "https://promos.makemytrip.com/Hotels_product/Luxe/Hero/The Roseate 20131126130003948.webp", "appDeeplink": "mmyt://htl/detail/?topHtlId=20131126130003948&hotelId=20131126130003948&checkin=07302023&checkout=07312023&country=IN&city=CTDEL&roomStayQualifier=2e0e&_uCurrency=INR&checkAvailability=true&locusId=CTDEL&locusType=city&filterData=MMT_OFFERING%7CDaily%20Dhamaka&region=in&funnelName=HOTELS", "calendarCriteria": {"available": true, "maxDate": "2023-10-08", "advanceDays": 90, "mlos": 1}, "similarHotelsRequired": false, "isABSO": false, "isRTB": false, "isMLOS": true, "isWishListed": false}]}], "sharingUrl": "https://applinks.makemytrip.com/hotelListingShare?checkin=06232022&checkout=06242022&city=CTMALDI&country=MDV&roomStayQualifier=2e0e&checkAvailability=true&locusId=CTMALDI&locusType=city&region=in&funnelName=HOTELS"}