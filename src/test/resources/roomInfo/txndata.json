{"persistedData": {"hotelList": [{"hotelid": "201809171729412453", "hotelInfo": {"hotelIdContext": "MSE", "hotelId": "201809171729412453", "name": "DoubleTree by Hilton Goa - <PERSON><PERSON><PERSON>", "chainCode": null, "chainName": null, "cityCode": "CTGOI", "cityName": "Goa", "countryCode": "IN", "ratePlanCode": null, "roomTypeCode": null, "pahApplicable": false, "ratePlanDesc": null, "segmentId": null, "ratePlanType": null, "roomTypeName": null, "roomTypeDescription": null, "addressLines": ["Kadamba Plateau, Panjim Old Goa Bypass Road, Velha Goa, Panaji, 403006, India \r\nSurvey no 34/1- B Chimbel, Tiswadi Goa", "<PERSON><PERSON><PERSON>"], "checkInTime": "3 PM", "checkOutTime": "12 PM", "hotelIcon": "https://gos3.ibcdn.com/ec80b39efcde11ea84570242ac110002.jpg", "categories": ["Chain", "HighFiveV2_Chain", "MySafety - Safe and Hygienic Stays", "Epic Workations", "Great Value Deals", "Staycation Deals", "MMT Assured", "Workation", "Safety_Hygiene", "Safe and Hygienic Stays", "Workation Homes", "Couple Friendly", "Hotel_Chat_Poc"], "checkin24Hr": false, "availDetails": null, "roomsRequested": 0, "altAcco": false, "mappedActualCity": null, "taxExcluded": "false", "primeHotel": true, "isBNPLHotel": false, "bnplBaseAmount": 0.0, "bestPriceGuaranteed": false, "locationId": "CTGOI", "accelerated": false, "gdsHotelCode": "1000197991", "starRating": 5, "propertyType": "Hotel", "addOnBucketId": "ADDON_FLT1_OLA,PAH_HOTELIER_ADDON", "walletBucketId": "HTL5STWB0", "acmeTags": "155,139", "acmeCDFBucket": "HTLADDON", "doubleBlackInfo": null, "specialRequestAvailable": {"categories": [{"code": "101", "name": "Smoking room", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": null}, {"code": "102", "name": "Late check-in", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": [{"code": "1021", "name": "CHECK-IN TIME", "type": "DROPDOWN", "placeholder": null, "values": ["04:00 PM", "05:00 PM", "06:00 PM", "07:00 PM", "08:00 PM", "09:00 PM", "10:00 PM", "11:00 PM"], "subCategories": null}]}, {"code": "103", "name": "Early check-in", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": [{"code": "1031", "name": "CHECK-IN TIME", "type": "DROPDOWN", "placeholder": null, "values": ["06:00 AM", "07:00 AM", "08:00 AM", "09:00 AM", "10:00 AM", "11:00 AM", "12:00 AM"], "subCategories": null}]}, {"code": "104", "name": "Room on a high floor", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": null}, {"code": "105", "name": "Large bed", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": null}, {"code": "106", "name": "Twin beds", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": null}, {"code": "107", "name": "Airport transfer", "type": "CHECKBOX", "placeholder": null, "values": null, "subCategories": [{"code": "1071", "name": "PICKUP TIME", "type": "DROPDOWN", "placeholder": null, "values": ["12:00 AM", "01:00 AM", "02:00 AM", "03:00 AM", "04:00 AM", "05:00 AM", "06:00 AM", "07:00 AM", "08:00 AM", "09:00 AM", "10:00 AM", "11:00 AM", "12:00 PM", "01:00 PM", "02:00 PM", "03:00 PM", "04:00 PM", "05:00 PM", "06:00 PM", "07:00 PM", "08:00 PM", "09:00 PM", "10:00 PM", "11:00 PM"], "subCategories": null}, {"code": "108", "name": "You may be charged for this trip by the hotel.", "type": "LABEL", "placeholder": "In case of any issues, Please get in touch with the hotelier directly.", "values": null, "subCategories": null}]}, {"code": "109", "name": "Any other request?", "type": "INPUTBOX", "placeholder": "In case of any issues, Please get in touch with the hotelier directly.", "values": null, "subCategories": null}], "disclaimer": "Special requests are subject to each hotel's availability, may be chargeable & can't be guaranteed."}, "specialRequest": null, "hotelBucket": "<PERSON><PERSON>_<PERSON>TL,B2C_DND,MOB_DND,NOMETA_BD,ARR11,TOT_CHAIN,N_OYO_CHAIN,TRUEDND,ASP,MMTBIG55,SALE_HILTON", "pahAuthenticationEnabled": false, "gstin": "30AAECG3075E1Z4", "oldCityCode": "LC_CTGOI", "oldCountryCode": "IN", "areaIdList": ["ARTISW", "ARPANJ"], "regionCodeList": ["RGGOI"], "mandatoryCharges": [], "notices": [], "countryName": "India", "stateCode": "STGOA", "stateName": "Goa", "locusCityCode": "CTGOI", "locusCountryCode": "IN", "locusData": {"locusId": "CTGOI", "locusType": "city", "locusName": "Goa"}, "lat": 15.49647, "lng": 73.88381, "corpRateAvailable": false, "corpRateExpected": false, "emiBucket": "", "policyToMessagesMap": {"Extra Bed": [""], "Hotel Policy": ["According to government regulations, a valid Photo ID has to be carried by every person above the age of 18 staying at DoubleTree by Hilton Goa - Panaji. The identification proofs accepted are Drivers License, Voters Card, Passport, Ration Card. Without valid ID the guest will not be allowed to check in.", "The primary guest checking in to the hotel must be at least 18 years of age.", "Early check-in or late check-out is subject to availability and may be chargeable by DoubleTree by Hilton Goa - Panaji. The standard check-in time is 3 PM and the standard check-out time is 12 PM. After booking you will be sent an email confirmation with hotel phone number. You can contact the hotel directly for early check-in or late check-out.", "The room tariff includes all taxes. The amount paid for the room does not include charges for optional services and facilities (such as room service, mini bar, snacks or telephone calls). These will be charged at the time of check-out from the Hotel.", "MakeMyTrip will not be responsible for any check-in denied by the Hotel due to the aforesaid reason. ", "DoubleTree by Hilton Goa - <PERSON>aj<PERSON> reserves the right of admission. Accommodation can be denied to guests posing as a 'couple' if suitable proof of identification is not presented at check-in.MakeMyTrip will not be responsible for any check-in denied by the Hotel due to the aforesaid reason. ", "DoubleTree by Hilton Goa - Panaji reserves the right of admission for local residents. Accommodation can be denied to guests residing in the same city.MakeMyTrip will not be responsible for any check-in denied by the Hotel due to the aforesaid reason. ", "For any update, User shall pay applicable cancellation/modification charges.", "Modified bookings will be subject to availability and revised booking policy of the Hotel.", "The cancellation/modification charges are standard and any waiver is on the hotel's discretion.", "Number of modifications possible on a booking will be on the discretion of MakemyTrip.", "Selective offers of MakeMyTrip will not be valid on a cancellation or modification of booking.", "Any e-coupon discount on the original booking shall be forfeited in the event of cancellation or modification."], "Cancellation Policy": ["Cancellation and prepayment policies vary according to room type. Please check the Fare policy associated with your room."], "Payment Mode": ["You can pay now or you can pay at the hotel if your selected room type has this option."], "Check In/out": ["Hotel Check-in Time is 3 PM, Check-out Time is 12 PM."]}, "houseRules": {"commonRules": [{"category": "Guest Profile", "id": "Guest<PERSON><PERSON><PERSON><PERSON>", "rules": [{"text": "Suitable for children"}, {"text": "Bachelors allowed"}, {"text": "Unmarried couples allowed"}]}, {"category": "Safety and Hygiene", "id": "SafetyandHygiene", "rules": [{"text": "Quarantine protocols are being followed as per local government authorities"}, {"text": "Hotel staff is trained on hygiene guidelines"}]}, {"category": "Payment Related", "id": "PaymentRelated", "rules": [{"text": "Credit/debit cards are accepted"}]}, {"category": "Food Arrangement", "id": "FoodArrangement", "rules": [{"text": "Outside food is not allowed in property premises"}]}, {"category": "Smoking/Alcohol consumption Rules", "id": "SmokingAlcoholconsumptionRules", "rules": [{"text": "Smoking within the premises is allowed"}, {"text": "There are some restrictions on alcohol consumption."}]}, {"category": "Pet(s) Related", "id": "PetsRelated", "rules": [{"text": "Pets are not allowed."}, {"text": "There are no pets living on the property"}]}, {"category": "ID Proof Related", "id": "IDProofRelated", "rules": [{"text": "Govt. ID is accepted as ID proof(s)"}]}, {"category": "Other Rules", "id": "OtherRules", "rules": [{"text": "Allows private parties or events"}]}]}, "mustReadRules": null, "listingType": "Room By Room", "pahxavailable": false, "couponApplicable": true}, "walletPahApplicable": false, "tariffInfoList": [{"ratePlanCode": "K1RV^^^OGAD12:MSE:1121:MSE:DERBY_DOORWAY", "roomCode": "968752", "roomTypeName": "King Guest Room River View", "absorptionInfo": null, "stayDetails": {"checkIn": "2020-12-29", "checkOut": "2020-12-30", "roomStayCandidates": [{"adult": 3, "ageQualifyingCode": "10", "child": null}]}, "promotions": [], "inclusions": [{"id": "-1965691044", "startDate": "2020-12-29", "endDate": "2020-12-30", "value": "Prepay & Save", "code": "OGAD12", "imageURL": null, "tags": null, "category": null, "tagList": null, "segmentIdentifier": null}], "mealPlans": [{"startDate": "2020-12-29", "endDate": "2020-12-30", "code": "EP", "value": "Room Only"}], "cancelPenaltyList": [{"cancellationType": "FREE_CANCELLATON", "cancelPenaltiesRule": null, "cancelRules": null, "penaltyDescription": {"name": null, "description": "Free Cancellation (100% refund) if you cancel this booking before 2020-12-13 23:59:59 (destination time). Cancellations post that will be subject to a hotel fee as follows:From 2020-12-13 23:59:59 (destination time) till 2020-12-16 10:59:59 (destination time) - booking amount for 1.0 night(s).After 2020-12-16 11:00:00 (destination time) - 100.0% of booking amount. Cancellations are only allowed before Check-In. All time mentioned above is in destination time."}, "tillDate": "13-Dec-2020 23:59", "sponsorer": null, "freeCancellationText": "Free Cancellation before 13-Dec-2020 23:59", "mostRestrictive": "Y"}], "originalCancelPenaltyList": null, "childPolicy": null, "displayFare": {"tax": {"value": 0.0}, "slashedPrice": {"value": 35044.05, "maxFraction": 0.0, "avgeragePriceNoTax": 11681.35, "averagePriceWithTax": 11681.35, "sellingPriceNoTax": 35044.05, "sellingPriceWithTax": 35044.05, "basePriceWoCommision": 0.0}, "actualPrice": {"value": 35044.05, "maxFraction": 0.0, "avgeragePriceNoTax": 11681.35, "averagePriceWithTax": 11681.35, "sellingPriceNoTax": 35044.05, "sellingPriceWithTax": 35044.05, "basePriceWoCommision": 26136.0}, "extraAdult": {"value": 0.0}, "totalTariff": {"grandTotal": 35044.05, "subTotal": 35044.05, "discount": 0.0, "taxes": 0.0, "extraPax": 0.0, "ddmu": 0.0}, "ddmu": 0.0, "totalRoomCount": 1, "blackDiscount": 0.0, "taxDetails": {"hotelTax": 5345.7, "markup": 0.0, "serviceTaxOnMarkup": 0.0, "taxExcluded": 0.0, "actualMarkup": 0.0, "ddMarkUp": 0.0}, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 35044.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 35044.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 29698.0, "hotelTax": 5346.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 0.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n/oiyX0ch/FS/YLjdCfY6OLa4MykG40SeRXAu2vDdcsQ8ACs9RDxNql/Co3gnVLjC56v0UrYILGW+nISCZSX2CUDyL0RDPBqTsMNO86fGaU48vh4Qtm9Uvbxeil9TZk2izNxDlMPqEOwLi6WCD059jFpCMefx+woIVIWt76EKk1qkD4/18J/ZE/4OCvVr2UVJtCW+kLN57deGOH8He45L1v1tUnlM0P1XJrNpTIGwI87p4RmXsvFhXsurFUP5GPk8NbDt7CAhXTGoxBeGXxkBnJ16DpC4Df99w=", "pricingDivisor": 1, "totalTax": 5346.0, "effectivePrice": 35044.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 0.0, "gstDiscountHotelierCoupon": 0.0, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": true}, "commission": 3562.35, "conversionFactor": 1.0, "hotelierCouponDiscount": 0.0, "gstDiscountHotelierCoupon": 0.0, "hotelTax": 0.0, "hotelierServiceCharge": 0.0, "isBNPLApplicable": true, "originalBNPL": true, "dateOfDelayedPayment": 1607711340000, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "skuratePolicyInfo": {"penaltyAmount": 0.0, "cloneable": false, "refundable": false}}, "supplierDetails": {"supplierCode": "DERBY_DOORWAY", "costPrice": 31481.7, "hotelierCurrencyCode": "INR", "dmcRatePlanCode": "K1RV^^^OGAD12", "accessCode": null}, "roomTariff": [{"perNightPrice": 11681.35, "extraPrice": 0.0, "taxes": 0.0, "totalPricePerRoom": 35044.05, "numberOfAdults": 3, "numberOfChildren": 0, "roomDiscountPerNight": 0.0, "roomDiscountTax": 0.0, "extraChildFarePerNight": 0.0, "startDate": "2020-12-16", "endDate": "2020-12-19", "roomNumber": "1", "hotelierServiceCharge": 0.0, "childAges": null}], "specialInstructionList": null, "skuDetail": {"penaltyAmount": 0.0, "cloneable": false, "refundable": false}, "availDetails": {"status": "B", "count": 0, "numOfRooms": 1, "occupancyDetails": {"adult": 3, "child": 0, "infant": 0, "numOfRooms": 1, "childAges": null, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "hotelReservationIDList": null, "paymentMode": "PAS", "overriddenCancelPolicy": false, "markUpAdjustment": null, "bnplExtended": false, "otherDetails": {"profit": 3562.35, "commission": 3562.35}, "corpMetaData": null, "checkinPolicy": null, "confirmationPolicy": null, "instantConfirmation": "NOT_APPLICABLE", "segmentId": "G", "corpRate": false, "sellableType": "room", "roomDetails": {"roomSize": "364 sq.ft", "roomViewName": "River View", "roomName": "King Guest Room River View", "beds": [{"type": "King Bed", "count": 1}], "maxGuestCount": 4, "maxAdultCount": 3, "maxChildCount": 2, "facilityWithGrp": [{"name": "Popular with Guests", "facilities": [{"name": "Bathroom", "type": "room", "sequence": 501, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Bathroom", "displayType": null, "highlightedName": "Bathroom", "childAttributes": [{"name": "Private", "subAttributes": []}]}, {"name": "24-hour Housekeeping", "type": "room", "sequence": 525, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Housekeeping", "displayType": null, "highlightedName": "24-hour Housekeeping", "childAttributes": [{"name": "24 hours", "subAttributes": []}]}, {"name": "24-hour In-room Dining", "type": "room", "sequence": 562, "categoryName": "Popular with Guests", "tags": null, "attributeName": "In Room dining", "displayType": null, "highlightedName": "24-hour In-room Dining", "childAttributes": [{"name": "24 hours", "subAttributes": []}]}, {"name": "Laundry Service", "type": "room", "sequence": 1133, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Laundry Service", "displayType": "1", "highlightedName": "Laundry Service", "childAttributes": [{"name": "Paid", "subAttributes": []}]}, {"name": "Mineral Water", "type": "room", "sequence": 1660, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Mineral Water", "displayType": null, "highlightedName": "Mineral Water", "childAttributes": [{"name": "Free", "subAttributes": []}]}]}, {"name": "Room Features", "facilities": [{"name": "Telephone", "type": "room", "sequence": 4, "categoryName": "Room Features", "tags": null, "attributeName": "Telephone", "displayType": null, "highlightedName": "Telephone", "childAttributes": []}, {"name": "<PERSON><PERSON>", "type": "room", "sequence": 16, "categoryName": "Room Features", "tags": null, "attributeName": "Pillow menu", "displayType": null, "highlightedName": "<PERSON><PERSON>", "childAttributes": []}, {"name": "Hypoallergenic Bedding", "type": "room", "sequence": 40, "categoryName": "Room Features", "tags": null, "attributeName": "Hypoallergenic Bedding", "displayType": null, "highlightedName": "Hypoallergenic Bedding", "childAttributes": []}, {"name": "Closet", "type": "room", "sequence": 48, "categoryName": "Room Features", "tags": null, "attributeName": "Closet", "displayType": null, "highlightedName": "Closet", "childAttributes": []}, {"name": "Minibar", "type": "room", "sequence": 50, "categoryName": "Room Features", "tags": null, "attributeName": "Mini Bar", "displayType": null, "highlightedName": "Minibar", "childAttributes": []}, {"name": "Mirror", "type": "room", "sequence": 133, "categoryName": "Room Features", "tags": null, "attributeName": "Mirror", "displayType": null, "highlightedName": "Mirror", "childAttributes": []}, {"name": "Chair", "type": "room", "sequence": 174, "categoryName": "Room Features", "tags": null, "attributeName": "Chair", "displayType": null, "highlightedName": "Chair", "childAttributes": []}, {"name": "Blackout Curtains", "type": "room", "sequence": 243, "categoryName": "Room Features", "tags": null, "attributeName": "Blackout curtains", "displayType": null, "highlightedName": "Blackout Curtains", "childAttributes": []}]}, {"name": "Beds and Blanket", "facilities": [{"name": "Cushions", "type": "room", "sequence": 8, "categoryName": "Beds and Blanket", "tags": null, "attributeName": "Cushions", "displayType": null, "highlightedName": "Cushions", "childAttributes": []}, {"name": "Woollen Blanket", "type": "room", "sequence": 1380, "categoryName": "Beds and Blanket", "tags": null, "attributeName": "Blanket", "displayType": null, "highlightedName": "Woollen Blanket", "childAttributes": [{"name": "W<PERSON><PERSON>", "subAttributes": []}]}, {"name": "Pillows", "type": "room", "sequence": 4829, "categoryName": "Beds and Blanket", "tags": null, "attributeName": "Pillows", "displayType": "1", "highlightedName": "Pillows", "childAttributes": [{"name": "Non-Feather Pillow", "subAttributes": []}]}]}, {"name": "Safety and Security", "facilities": [{"name": "Safety", "type": "room", "sequence": 4025, "categoryName": "Safety and Security", "tags": null, "attributeName": "Safety", "displayType": null, "highlightedName": "Safety", "childAttributes": []}, {"name": "Security", "type": "room", "sequence": 4060, "categoryName": "Safety and Security", "tags": null, "attributeName": "Security", "displayType": "1", "highlightedName": "Security", "childAttributes": [{"name": "Access Control System", "subAttributes": []}]}, {"name": "Smoke Alarm", "type": "room", "sequence": 4065, "categoryName": "Safety and Security", "tags": null, "attributeName": "Smoke Alarm", "displayType": null, "highlightedName": "Smoke Alarm", "childAttributes": []}]}, {"name": "Media and Entertainment", "facilities": [{"name": "TV", "type": "room", "sequence": 1141, "categoryName": "Media and Entertainment", "tags": null, "attributeName": "TV", "displayType": "1", "highlightedName": "TV", "childAttributes": [{"name": "LED", "subAttributes": []}]}]}, {"name": "Bathroom", "facilities": [{"name": "Shaving Mirror", "type": "room", "sequence": 18, "categoryName": "Bathroom", "tags": null, "attributeName": "Shaving Mirror", "displayType": null, "highlightedName": "Shaving Mirror", "childAttributes": []}, {"name": "<PERSON><PERSON><PERSON>", "type": "room", "sequence": 61, "categoryName": "Bathroom", "tags": null, "attributeName": "<PERSON><PERSON><PERSON>", "displayType": null, "highlightedName": "<PERSON><PERSON><PERSON>", "childAttributes": []}, {"name": "Dental Kit", "type": "room", "sequence": 94, "categoryName": "Bathroom", "tags": null, "attributeName": "Dental Kit", "displayType": null, "highlightedName": "Dental Kit", "childAttributes": []}, {"name": "Shower Cap", "type": "room", "sequence": 141, "categoryName": "Bathroom", "tags": null, "attributeName": "Shower Cap", "displayType": null, "highlightedName": "Shower Cap", "childAttributes": []}, {"name": "Slippers", "type": "room", "sequence": 153, "categoryName": "Bathroom", "tags": null, "attributeName": "Slippers", "displayType": null, "highlightedName": "Slippers", "childAttributes": []}, {"name": "Bathrobes", "type": "room", "sequence": 154, "categoryName": "Bathroom", "tags": null, "attributeName": "Bathrobes", "displayType": null, "highlightedName": "Bathrobes", "childAttributes": []}, {"name": "Sanitary Bin", "type": "room", "sequence": 155, "categoryName": "Bathroom", "tags": null, "attributeName": "Sanitary Bin", "displayType": null, "highlightedName": "Sanitary Bin", "childAttributes": []}, {"name": "Weighing Scale", "type": "room", "sequence": 175, "categoryName": "Bathroom", "tags": null, "attributeName": "Weighing Scale", "displayType": null, "highlightedName": "Weighing Scale", "childAttributes": []}, {"name": "Western Toilet Seat", "type": "room", "sequence": 203, "categoryName": "Bathroom", "tags": null, "attributeName": "Western Toilet Seat", "displayType": null, "highlightedName": "Western Toilet Seat", "childAttributes": []}, {"name": "Shower C<PERSON>", "type": "room", "sequence": 207, "categoryName": "Bathroom", "tags": null, "attributeName": "Shower cubicle", "displayType": null, "highlightedName": "Shower C<PERSON>", "childAttributes": []}, {"name": "Bathroom Phone", "type": "room", "sequence": 216, "categoryName": "Bathroom", "tags": null, "attributeName": "Bathroom Phone", "displayType": null, "highlightedName": "Bathroom Phone", "childAttributes": []}, {"name": "<PERSON>bins", "type": "room", "sequence": 220, "categoryName": "Bathroom", "tags": null, "attributeName": "<PERSON>bins", "displayType": null, "highlightedName": "<PERSON>bins", "childAttributes": []}, {"name": "Toilet Paper", "type": "room", "sequence": 221, "categoryName": "Bathroom", "tags": null, "attributeName": "Toilet Papers", "displayType": null, "highlightedName": "Toilet Paper", "childAttributes": []}, {"name": "Hot & Cold Water", "type": "room", "sequence": 240, "categoryName": "Bathroom", "tags": null, "attributeName": "Hot & Cold Water", "displayType": null, "highlightedName": "Hot & Cold Water", "childAttributes": []}, {"name": "Shower", "type": "room", "sequence": 1334, "categoryName": "Bathroom", "tags": null, "attributeName": "Shower", "displayType": "1", "highlightedName": "Shower", "childAttributes": [{"name": "Hand shower", "subAttributes": []}, {"name": "Standing", "subAttributes": []}]}, {"name": "Toiletries", "type": "room", "sequence": 5311, "categoryName": "Bathroom", "tags": null, "attributeName": "Toiletries", "displayType": "1", "highlightedName": "Toiletries", "childAttributes": [{"name": "Comb", "subAttributes": []}, {"name": "Conditioner", "subAttributes": []}, {"name": "Moisturiser", "subAttributes": []}, {"name": "Shampoo", "subAttributes": []}, {"name": "Shower <PERSON><PERSON>", "subAttributes": []}, {"name": "Soap", "subAttributes": []}]}, {"name": "<PERSON><PERSON><PERSON>", "type": "room", "sequence": 5314, "categoryName": "Bathroom", "tags": null, "attributeName": "<PERSON><PERSON><PERSON>", "displayType": "1", "highlightedName": "<PERSON><PERSON><PERSON>", "childAttributes": [{"name": "Bath Towel", "subAttributes": []}]}]}, {"name": "Other Facilities", "facilities": [{"name": "Newspaper", "type": "room", "sequence": 192, "categoryName": "Other Facilities", "tags": null, "attributeName": "Newspaper", "displayType": null, "highlightedName": "Newspaper", "childAttributes": []}, {"name": "Alarm Clock", "type": "room", "sequence": 242, "categoryName": "Other Facilities", "tags": null, "attributeName": "Alarm Clock", "displayType": null, "highlightedName": "Alarm Clock", "childAttributes": []}, {"name": "Balcony", "type": "room", "sequence": 4966, "categoryName": "Other Facilities", "tags": null, "attributeName": "Balcony", "displayType": "1", "highlightedName": "Balcony", "childAttributes": [{"name": "Private", "subAttributes": []}]}, {"name": "Sanitizers", "type": "room", "sequence": 100000, "categoryName": "Other Facilities", "tags": null, "attributeName": "Sanitizers", "displayType": null, "highlightedName": "Sanitizers", "childAttributes": [{"name": "Hand Sanitizers", "subAttributes": []}]}]}], "highlightedFacilities": [{"name": "Popular with Guests", "facilities": [{"name": "24-hour Room Service", "type": "room", "sequence": -20072, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Room service", "displayType": null, "highlightedName": "24-hour Room Service", "childAttributes": [{"name": "24 hours", "subAttributes": []}]}, {"name": "Air Conditioning", "type": "room", "sequence": -20068, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Air Conditioning", "displayType": "1", "highlightedName": "Air Conditioning", "childAttributes": [{"name": "Room controlled", "subAttributes": []}]}, {"name": "Free Wi-Fi", "type": "room", "sequence": -20062, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Wifi", "displayType": null, "highlightedName": "Free Wi-Fi", "childAttributes": [{"name": "Free", "subAttributes": []}]}, {"name": "Electric Kettle", "type": "room", "sequence": -20060, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Electric Kettle", "displayType": null, "highlightedName": "Electric Kettle", "childAttributes": []}, {"name": "Iron/Ironing Board", "type": "room", "sequence": -20059, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Iron/Ironing Board", "displayType": null, "highlightedName": "Iron/Ironing Board", "childAttributes": []}]}, {"name": "Room Features", "facilities": [{"name": "Charging Points", "type": "room", "sequence": -20058, "categoryName": "Room Features", "tags": null, "attributeName": "Charging points", "displayType": null, "highlightedName": "Charging Points", "childAttributes": []}]}, {"name": "Childcare", "facilities": [{"name": "Cribs", "type": "room", "sequence": -20080, "categoryName": "Childcare", "tags": null, "attributeName": "Cribs", "displayType": null, "highlightedName": "Cribs", "childAttributes": []}]}, {"name": "Safety and Security", "facilities": [{"name": "Electronic Safe", "type": "room", "sequence": -20064, "categoryName": "Safety and Security", "tags": null, "attributeName": "Safe", "displayType": null, "highlightedName": "Electronic Safe", "childAttributes": [{"name": "Electronic", "subAttributes": []}]}]}], "images": ["//gos3.ibcdn.com/357bda12d8f411e8bcc50242ac110005.jpg", "//gos3.ibcdn.com/924d1d925c3311e991000242ac110002.jpg", "//gos3.ibcdn.com/94bdbd245c3411e9a2ef0242ac110002.jpg", "//gos3.ibcdn.com/24ec774c5c3411e9b3a10242ac110006.jpg", "//gos3.ibcdn.com/2ca144b85c3411e98f720242ac110002.jpg", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/room-imgs/201809171729412453-968752-f6ac1f3afe3d11e8b00f0242ac110002.jpg", "//gos3.ibcdn.com/886227041f3311eb9c860242ac110003.jpg"]}, "cancellationTimeline": {"checkInDate": "16 Dec", "cancellationDate": "13 Dec", "subTitle": "Free Cancellation", "freeCancellationText": "Free Cancellation till 13 Dec 11:59 PM", "title": "STAY FLEXIBLE WITH", "bookingDate": "07 Dec"}, "pahx": false}, {"ratePlanCode": "K1^^^OGAD12:MSE:1121:MSE:DERBY_DOORWAY", "roomCode": "25493", "roomTypeName": "King Guest Room", "absorptionInfo": null, "stayDetails": {"checkIn": "2020-12-29", "checkOut": "2020-12-30", "roomStayCandidates": [{"adult": 2, "ageQualifyingCode": "10", "child": null}]}, "promotions": [], "inclusions": [{"id": "-1965691044", "startDate": "2020-12-29", "endDate": "2020-12-30", "value": "Prepay & Save", "code": "OGAD12", "imageURL": null, "tags": null, "category": null, "tagList": null, "segmentIdentifier": null}], "mealPlans": [{"startDate": "2020-12-29", "endDate": "2020-12-30", "code": "EP", "value": "Room Only"}], "cancelPenaltyList": [{"cancellationType": "FREE_CANCELLATON", "cancelPenaltiesRule": null, "cancelRules": null, "penaltyDescription": {"name": null, "description": "Free Cancellation (100% refund) if you cancel this booking before 2020-12-13 23:59:59 (destination time). Cancellations post that will be subject to a hotel fee as follows:From 2020-12-13 23:59:59 (destination time) till 2020-12-16 10:59:59 (destination time) - booking amount for 1.0 night(s).After 2020-12-16 11:00:00 (destination time) - 100.0% of booking amount. Cancellations are only allowed before Check-In. All time mentioned above is in destination time."}, "tillDate": "13-Dec-2020 23:59", "sponsorer": null, "freeCancellationText": "Free Cancellation before 13-Dec-2020 23:59", "mostRestrictive": "N"}], "originalCancelPenaltyList": null, "childPolicy": null, "displayFare": {"tax": {"value": 0.0}, "slashedPrice": {"value": 22678.72, "maxFraction": 0.0, "avgeragePriceNoTax": 7559.57, "averagePriceWithTax": 7559.57, "sellingPriceNoTax": 22678.72, "sellingPriceWithTax": 22678.72, "basePriceWoCommision": 0.0}, "actualPrice": {"value": 22678.72, "maxFraction": 0.0, "avgeragePriceNoTax": 7559.57, "averagePriceWithTax": 7559.57, "sellingPriceNoTax": 22678.72, "sellingPriceWithTax": 22678.72, "basePriceWoCommision": 17820.0}, "extraAdult": {"value": 0.0}, "totalTariff": {"grandTotal": 22678.72, "subTotal": 22678.72, "discount": 0.0, "taxes": 0.0, "extraPax": 0.0, "ddmu": 0.0}, "ddmu": 0.0, "totalRoomCount": 1, "blackDiscount": 0.0, "taxDetails": {"hotelTax": 2429.86, "markup": 0.0, "serviceTaxOnMarkup": 0.0, "taxExcluded": 0.0, "actualMarkup": 0.0, "ddMarkUp": 0.0}, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 22679.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 22679.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 20249.0, "hotelTax": 2430.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 0.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n/vBTWYcDjoev8SFvboxaVolp9vV/8gg7G8oVL1w8rK5faGlzOZMQEZ6qxsq6gU4rd3EOyvednrCnz1JZehW5Fr7SB3hzXjaUBl9gj/y+DlTD6Q7UReRefSdZQiLyu8cAJCEh0881nSTJYFoXAQmwkrtKvkvDuZBifq/G9gzXEixwnvVRohE5KKMsAmIrsAKAnxHzo+04K0RRtD99oRhYKwfVyCrADxjDeLZoh+Y4EzVbfSfmUaaIbaX7ncuHxFcUYdcQ0nGP32i4bLGFIAXxbi", "pricingDivisor": 1, "totalTax": 2430.0, "effectivePrice": 22679.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 0.0, "gstDiscountHotelierCoupon": 0.0, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": true}, "commission": 2428.86, "conversionFactor": 1.0, "hotelierCouponDiscount": 0.0, "gstDiscountHotelierCoupon": 0.0, "hotelTax": 0.0, "hotelierServiceCharge": 0.0, "isBNPLApplicable": true, "originalBNPL": true, "dateOfDelayedPayment": 1607711340000, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "skuratePolicyInfo": {"penaltyAmount": 0.0, "cloneable": false, "refundable": false}}, "supplierDetails": {"supplierCode": "DERBY_DOORWAY", "costPrice": 20249.86, "hotelierCurrencyCode": "INR", "dmcRatePlanCode": "K1^^^OGAD12", "accessCode": null}, "roomTariff": [{"perNightPrice": 7559.57, "extraPrice": 0.0, "taxes": 0.0, "totalPricePerRoom": 22678.72, "numberOfAdults": 2, "numberOfChildren": 0, "roomDiscountPerNight": 0.0, "roomDiscountTax": 0.0, "extraChildFarePerNight": 0.0, "startDate": "2020-12-16", "endDate": "2020-12-19", "roomNumber": "1", "hotelierServiceCharge": 0.0, "childAges": null}], "specialInstructionList": null, "skuDetail": {"penaltyAmount": 0.0, "cloneable": false, "refundable": false}, "availDetails": {"status": "B", "count": 0, "numOfRooms": 1, "occupancyDetails": {"adult": 2, "child": 0, "infant": 0, "numOfRooms": 1, "childAges": null, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "hotelReservationIDList": null, "paymentMode": "PAS", "overriddenCancelPolicy": false, "markUpAdjustment": null, "bnplExtended": false, "otherDetails": {"profit": 2428.86, "commission": 2428.86}, "corpMetaData": null, "checkinPolicy": null, "confirmationPolicy": null, "instantConfirmation": "NOT_APPLICABLE", "segmentId": "G", "corpRate": false, "sellableType": "room", "roomDetails": {"roomSize": "364 sq.ft", "roomViewName": "Garden View", "roomName": "King Guest Room", "beds": [{"type": "King Bed", "count": 1}], "maxGuestCount": 4, "maxAdultCount": 3, "maxChildCount": 2, "facilityWithGrp": [{"name": "Popular with Guests", "facilities": [{"name": "Mineral Water - additional charge", "type": "room", "sequence": 323, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Mineral Water", "displayType": null, "highlightedName": "Mineral Water - additional charge", "childAttributes": [{"name": "Paid", "subAttributes": []}]}, {"name": "Bathroom", "type": "room", "sequence": 501, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Bathroom", "displayType": null, "highlightedName": "Bathroom", "childAttributes": [{"name": "Private", "subAttributes": []}]}, {"name": "24-hour Housekeeping", "type": "room", "sequence": 525, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Housekeeping", "displayType": null, "highlightedName": "24-hour Housekeeping", "childAttributes": [{"name": "24 hours", "subAttributes": []}]}, {"name": "24-hour In-room Dining", "type": "room", "sequence": 562, "categoryName": "Popular with Guests", "tags": null, "attributeName": "In Room dining", "displayType": null, "highlightedName": "24-hour In-room Dining", "childAttributes": [{"name": "24 hours", "subAttributes": []}]}, {"name": "Laundry Service", "type": "room", "sequence": 1133, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Laundry Service", "displayType": "1", "highlightedName": "Laundry Service", "childAttributes": [{"name": "Paid", "subAttributes": []}]}]}, {"name": "Room Features", "facilities": [{"name": "Telephone", "type": "room", "sequence": 4, "categoryName": "Room Features", "tags": null, "attributeName": "Telephone", "displayType": null, "highlightedName": "Telephone", "childAttributes": []}, {"name": "<PERSON><PERSON>", "type": "room", "sequence": 16, "categoryName": "Room Features", "tags": null, "attributeName": "Pillow menu", "displayType": null, "highlightedName": "<PERSON><PERSON>", "childAttributes": []}, {"name": "Closet", "type": "room", "sequence": 48, "categoryName": "Room Features", "tags": null, "attributeName": "Closet", "displayType": null, "highlightedName": "Closet", "childAttributes": []}, {"name": "Mirror", "type": "room", "sequence": 133, "categoryName": "Room Features", "tags": null, "attributeName": "Mirror", "displayType": null, "highlightedName": "Mirror", "childAttributes": []}, {"name": "Chair", "type": "room", "sequence": 174, "categoryName": "Room Features", "tags": null, "attributeName": "Chair", "displayType": null, "highlightedName": "Chair", "childAttributes": []}, {"name": "Blackout Curtains", "type": "room", "sequence": 243, "categoryName": "Room Features", "tags": null, "attributeName": "Blackout curtains", "displayType": null, "highlightedName": "Blackout Curtains", "childAttributes": []}, {"name": "Minibar", "type": "room", "sequence": 502, "categoryName": "Room Features", "tags": null, "attributeName": "Mini Bar", "displayType": null, "highlightedName": "Minibar", "childAttributes": [{"name": "Paid", "subAttributes": []}]}]}, {"name": "Beds and Blanket", "facilities": [{"name": "Cushions", "type": "room", "sequence": 8, "categoryName": "Beds and Blanket", "tags": null, "attributeName": "Cushions", "displayType": null, "highlightedName": "Cushions", "childAttributes": []}, {"name": "Woollen Blanket", "type": "room", "sequence": 1380, "categoryName": "Beds and Blanket", "tags": null, "attributeName": "Blanket", "displayType": null, "highlightedName": "Woollen Blanket", "childAttributes": [{"name": "W<PERSON><PERSON>", "subAttributes": []}]}, {"name": "Pillows", "type": "room", "sequence": 4829, "categoryName": "Beds and Blanket", "tags": null, "attributeName": "Pillows", "displayType": "1", "highlightedName": "Pillows", "childAttributes": [{"name": "Non-Feather Pillow", "subAttributes": []}]}]}, {"name": "Safety and Security", "facilities": [{"name": "Safety", "type": "room", "sequence": 4025, "categoryName": "Safety and Security", "tags": null, "attributeName": "Safety", "displayType": null, "highlightedName": "Safety", "childAttributes": []}, {"name": "Security", "type": "room", "sequence": 4060, "categoryName": "Safety and Security", "tags": null, "attributeName": "Security", "displayType": "1", "highlightedName": "Security", "childAttributes": [{"name": "Access Control System", "subAttributes": []}]}, {"name": "Smoke Alarm", "type": "room", "sequence": 4065, "categoryName": "Safety and Security", "tags": null, "attributeName": "Smoke Alarm", "displayType": null, "highlightedName": "Smoke Alarm", "childAttributes": []}]}, {"name": "Media and Entertainment", "facilities": [{"name": "TV", "type": "room", "sequence": 1141, "categoryName": "Media and Entertainment", "tags": null, "attributeName": "TV", "displayType": "1", "highlightedName": "TV", "childAttributes": [{"name": "LED", "subAttributes": []}]}]}, {"name": "Bathroom", "facilities": [{"name": "Shaving Mirror", "type": "room", "sequence": 18, "categoryName": "Bathroom", "tags": null, "attributeName": "Shaving Mirror", "displayType": null, "highlightedName": "Shaving Mirror", "childAttributes": []}, {"name": "<PERSON><PERSON><PERSON>", "type": "room", "sequence": 61, "categoryName": "Bathroom", "tags": null, "attributeName": "<PERSON><PERSON><PERSON>", "displayType": null, "highlightedName": "<PERSON><PERSON><PERSON>", "childAttributes": []}, {"name": "Dental Kit", "type": "room", "sequence": 94, "categoryName": "Bathroom", "tags": null, "attributeName": "Dental Kit", "displayType": null, "highlightedName": "Dental Kit", "childAttributes": []}, {"name": "Shower Cap", "type": "room", "sequence": 141, "categoryName": "Bathroom", "tags": null, "attributeName": "Shower Cap", "displayType": null, "highlightedName": "Shower Cap", "childAttributes": []}, {"name": "Slippers", "type": "room", "sequence": 153, "categoryName": "Bathroom", "tags": null, "attributeName": "Slippers", "displayType": null, "highlightedName": "Slippers", "childAttributes": []}, {"name": "Bathrobes", "type": "room", "sequence": 154, "categoryName": "Bathroom", "tags": null, "attributeName": "Bathrobes", "displayType": null, "highlightedName": "Bathrobes", "childAttributes": []}, {"name": "Sanitary Bin", "type": "room", "sequence": 155, "categoryName": "Bathroom", "tags": null, "attributeName": "Sanitary Bin", "displayType": null, "highlightedName": "Sanitary Bin", "childAttributes": []}, {"name": "Weighing Scale", "type": "room", "sequence": 175, "categoryName": "Bathroom", "tags": null, "attributeName": "Weighing Scale", "displayType": null, "highlightedName": "Weighing Scale", "childAttributes": []}, {"name": "Western Toilet Seat", "type": "room", "sequence": 203, "categoryName": "Bathroom", "tags": null, "attributeName": "Western Toilet Seat", "displayType": null, "highlightedName": "Western Toilet Seat", "childAttributes": []}, {"name": "Bathroom Phone", "type": "room", "sequence": 216, "categoryName": "Bathroom", "tags": null, "attributeName": "Bathroom Phone", "displayType": null, "highlightedName": "Bathroom Phone", "childAttributes": []}, {"name": "<PERSON>bins", "type": "room", "sequence": 220, "categoryName": "Bathroom", "tags": null, "attributeName": "<PERSON>bins", "displayType": null, "highlightedName": "<PERSON>bins", "childAttributes": []}, {"name": "Toilet Paper", "type": "room", "sequence": 221, "categoryName": "Bathroom", "tags": null, "attributeName": "Toilet Papers", "displayType": null, "highlightedName": "Toilet Paper", "childAttributes": []}, {"name": "Hot & Cold Water", "type": "room", "sequence": 240, "categoryName": "Bathroom", "tags": null, "attributeName": "Hot & Cold Water", "displayType": null, "highlightedName": "Hot & Cold Water", "childAttributes": []}, {"name": "Shower", "type": "room", "sequence": 1334, "categoryName": "Bathroom", "tags": null, "attributeName": "Shower", "displayType": "1", "highlightedName": "Shower", "childAttributes": [{"name": "Hand shower", "subAttributes": []}, {"name": "Standing", "subAttributes": []}]}, {"name": "Toiletries", "type": "room", "sequence": 5311, "categoryName": "Bathroom", "tags": null, "attributeName": "Toiletries", "displayType": "1", "highlightedName": "Toiletries", "childAttributes": [{"name": "Comb", "subAttributes": []}, {"name": "Conditioner", "subAttributes": []}, {"name": "Moisturiser", "subAttributes": []}, {"name": "Shampoo", "subAttributes": []}, {"name": "Shower <PERSON><PERSON>", "subAttributes": []}, {"name": "Soap", "subAttributes": []}]}, {"name": "<PERSON><PERSON><PERSON>", "type": "room", "sequence": 5314, "categoryName": "Bathroom", "tags": null, "attributeName": "<PERSON><PERSON><PERSON>", "displayType": "1", "highlightedName": "<PERSON><PERSON><PERSON>", "childAttributes": [{"name": "Bath Towel", "subAttributes": []}]}]}, {"name": "Other Facilities", "facilities": [{"name": "Newspaper", "type": "room", "sequence": 192, "categoryName": "Other Facilities", "tags": null, "attributeName": "Newspaper", "displayType": null, "highlightedName": "Newspaper", "childAttributes": []}, {"name": "Alarm Clock", "type": "room", "sequence": 242, "categoryName": "Other Facilities", "tags": null, "attributeName": "Alarm Clock", "displayType": null, "highlightedName": "Alarm Clock", "childAttributes": []}, {"name": "Balcony", "type": "room", "sequence": 4966, "categoryName": "Other Facilities", "tags": null, "attributeName": "Balcony", "displayType": "1", "highlightedName": "Balcony", "childAttributes": [{"name": "Private", "subAttributes": []}]}, {"name": "Sanitizers", "type": "room", "sequence": 100000, "categoryName": "Other Facilities", "tags": null, "attributeName": "Sanitizers", "displayType": null, "highlightedName": "Sanitizers", "childAttributes": [{"name": "Hand Sanitizers", "subAttributes": []}]}]}], "highlightedFacilities": [{"name": "Popular with Guests", "facilities": [{"name": "24-hour Room Service", "type": "room", "sequence": -20072, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Room service", "displayType": null, "highlightedName": "24-hour Room Service", "childAttributes": [{"name": "24 hours", "subAttributes": []}]}, {"name": "Air Conditioning", "type": "room", "sequence": -20068, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Air Conditioning", "displayType": "1", "highlightedName": "Air Conditioning", "childAttributes": [{"name": "Room controlled", "subAttributes": []}]}, {"name": "Free Wi-Fi", "type": "room", "sequence": -20062, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Wifi", "displayType": null, "highlightedName": "Free Wi-Fi", "childAttributes": [{"name": "Free", "subAttributes": []}]}, {"name": "Electric Kettle", "type": "room", "sequence": -20060, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Electric Kettle", "displayType": null, "highlightedName": "Electric Kettle", "childAttributes": []}, {"name": "Iron/Ironing Board", "type": "room", "sequence": -20059, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Iron/Ironing Board", "displayType": null, "highlightedName": "Iron/Ironing Board", "childAttributes": []}]}, {"name": "Room Features", "facilities": [{"name": "Charging Points", "type": "room", "sequence": -20058, "categoryName": "Room Features", "tags": null, "attributeName": "Charging points", "displayType": null, "highlightedName": "Charging Points", "childAttributes": []}]}, {"name": "Childcare", "facilities": [{"name": "Cribs", "type": "room", "sequence": -20080, "categoryName": "Childcare", "tags": null, "attributeName": "Cribs", "displayType": null, "highlightedName": "Cribs", "childAttributes": []}]}, {"name": "Safety and Security", "facilities": [{"name": "Electronic Safe", "type": "room", "sequence": -20064, "categoryName": "Safety and Security", "tags": null, "attributeName": "Safe", "displayType": null, "highlightedName": "Electronic Safe", "childAttributes": [{"name": "Electronic", "subAttributes": []}]}]}], "images": ["//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/room-imgs/201809171729412453-25493-02df4af2fe3e11e8a1430242ac110003.jpg", "//gos3.ibcdn.com/564fc7345ac811e9ab670242ac110005.jpg", "//gos3.ibcdn.com/5b503be25ac811e986830242ac110002.jpg", "//gos3.ibcdn.com/7821f83c5ac811e9920c0242ac110003.jpg", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/room-imgs/201809171729412453-25493-c5f42e96fe3d11e888420242ac110003.jpg", "//gos3.ibcdn.com/d283a6d8e32311ea9baa0242ac110003.png", "//gos3.ibcdn.com/3a4bdf501cfa11eb97a60242ac110002.jpg"]}, "cancellationTimeline": {"checkInDate": "16 Dec", "cancellationDate": "13 Dec", "subTitle": "Free Cancellation", "freeCancellationText": "Free Cancellation till 13 Dec 11:59 PM", "title": "STAY FLEXIBLE WITH", "bookingDate": "07 Dec"}, "pahx": false}, {"ratePlanCode": "T2^^^OGAD12:MSE:1121:MSE:DERBY_DOORWAY", "roomCode": "30065", "roomTypeName": "Twin Guest Room", "absorptionInfo": null, "stayDetails": {"checkIn": "2020-12-29", "checkOut": "2020-12-30", "roomStayCandidates": [{"adult": 3, "ageQualifyingCode": "10", "child": null}]}, "promotions": [], "inclusions": [{"id": "-1965691044", "startDate": "2020-12-29", "endDate": "2020-12-30", "value": "Prepay & Save", "code": "OGAD12", "imageURL": null, "tags": null, "category": null, "tagList": null, "segmentIdentifier": null}], "mealPlans": [{"startDate": "2020-12-29", "endDate": "2020-12-30", "code": "EP", "value": "Room Only"}], "cancelPenaltyList": [{"cancellationType": "FREE_CANCELLATON", "cancelPenaltiesRule": null, "cancelRules": null, "penaltyDescription": {"name": null, "description": "Free Cancellation (100% refund) if you cancel this booking before 2020-12-13 23:59:59 (destination time). Cancellations post that will be subject to a hotel fee as follows:From 2020-12-13 23:59:59 (destination time) till 2020-12-16 10:59:59 (destination time) - booking amount for 1.0 night(s).After 2020-12-16 11:00:00 (destination time) - 100.0% of booking amount. Cancellations are only allowed before Check-In. All time mentioned above is in destination time."}, "tillDate": "13-Dec-2020 23:59", "sponsorer": null, "freeCancellationText": "Free Cancellation before 13-Dec-2020 23:59", "mostRestrictive": "N"}], "originalCancelPenaltyList": null, "childPolicy": null, "displayFare": {"tax": {"value": 0.0}, "slashedPrice": {"value": 31858.23, "maxFraction": 0.0, "avgeragePriceNoTax": 10619.41, "averagePriceWithTax": 10619.41, "sellingPriceNoTax": 31858.23, "sellingPriceWithTax": 31858.23, "basePriceWoCommision": 0.0}, "actualPrice": {"value": 31858.23, "maxFraction": 0.0, "avgeragePriceNoTax": 10619.41, "averagePriceWithTax": 10619.41, "sellingPriceNoTax": 31858.23, "sellingPriceWithTax": 31858.23, "basePriceWoCommision": 23760.0}, "extraAdult": {"value": 0.0}, "totalTariff": {"grandTotal": 31858.23, "subTotal": 31858.23, "discount": 0.0, "taxes": 0.0, "extraPax": 0.0, "ddmu": 0.0}, "ddmu": 0.0, "totalRoomCount": 1, "blackDiscount": 0.0, "taxDetails": {"hotelTax": 4859.73, "markup": 0.0, "serviceTaxOnMarkup": 0.0, "taxExcluded": 0.0, "actualMarkup": 0.0, "ddMarkUp": 0.0}, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 31858.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 31858.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 26999.0, "hotelTax": 4860.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 0.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n/u4wROIisWBkUchXcb/KZQQyJGh0iEpy5ZY/xrQk6QtzKh7+dsg5q6D7bf9I3POfBWJmSYunroVryb/daBCG5rFMzDFMgJ6aXThOJKWA986RiiSC6b8u00YA+vdrUcwyXtszEhhYwntq0rQAwIWDUf05RCbxEuV3St/P6nSttMDdIit7oZVnwEi8M6DIqqAsGSIy2YCE47tEabZw7K4uPQoAd5UjVgkvYIndV5Q9JQQ/EK0TLKus5JcPjWzIGAlIl1tj7GhNSvQizzmukkgW5v", "pricingDivisor": 1, "totalTax": 4860.0, "effectivePrice": 31858.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 0.0, "gstDiscountHotelierCoupon": 0.0, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": true}, "commission": 3238.5, "conversionFactor": 1.0, "hotelierCouponDiscount": 0.0, "gstDiscountHotelierCoupon": 0.0, "hotelTax": 0.0, "hotelierServiceCharge": 0.0, "isBNPLApplicable": true, "originalBNPL": true, "dateOfDelayedPayment": 1607711340000, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "skuratePolicyInfo": {"penaltyAmount": 0.0, "cloneable": false, "refundable": false}}, "supplierDetails": {"supplierCode": "DERBY_DOORWAY", "costPrice": 28619.73, "hotelierCurrencyCode": "INR", "dmcRatePlanCode": "T2^^^OGAD12", "accessCode": null}, "roomTariff": [{"perNightPrice": 10619.41, "extraPrice": 0.0, "taxes": 0.0, "totalPricePerRoom": 31858.23, "numberOfAdults": 3, "numberOfChildren": 0, "roomDiscountPerNight": 0.0, "roomDiscountTax": 0.0, "extraChildFarePerNight": 0.0, "startDate": "2020-12-16", "endDate": "2020-12-19", "roomNumber": "1", "hotelierServiceCharge": 0.0, "childAges": null}], "specialInstructionList": null, "skuDetail": {"penaltyAmount": 0.0, "cloneable": false, "refundable": false}, "availDetails": {"status": "B", "count": 0, "numOfRooms": 1, "occupancyDetails": {"adult": 3, "child": 0, "infant": 0, "numOfRooms": 1, "childAges": null, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "hotelReservationIDList": null, "paymentMode": "PAS", "overriddenCancelPolicy": false, "markUpAdjustment": null, "bnplExtended": false, "otherDetails": {"profit": 3238.5, "commission": 3238.5}, "corpMetaData": null, "checkinPolicy": null, "confirmationPolicy": null, "instantConfirmation": "NOT_APPLICABLE", "segmentId": "G", "corpRate": false, "sellableType": "room", "roomDetails": {"roomSize": "364 sq.ft", "roomViewName": "Garden View", "roomName": "Twin Guest Room", "beds": [{"type": "Twin Bed", "count": 1}], "maxGuestCount": 4, "maxAdultCount": 2, "maxChildCount": 2, "facilityWithGrp": [{"name": "Popular with Guests", "facilities": [{"name": "Bathroom", "type": "room", "sequence": 501, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Bathroom", "displayType": null, "highlightedName": "Bathroom", "childAttributes": [{"name": "Private", "subAttributes": []}]}, {"name": "24-hour Housekeeping", "type": "room", "sequence": 525, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Housekeeping", "displayType": null, "highlightedName": "24-hour Housekeeping", "childAttributes": [{"name": "24 hours", "subAttributes": []}]}, {"name": "24-hour In-room Dining", "type": "room", "sequence": 562, "categoryName": "Popular with Guests", "tags": null, "attributeName": "In Room dining", "displayType": null, "highlightedName": "24-hour In-room Dining", "childAttributes": [{"name": "24 hours", "subAttributes": []}]}, {"name": "Laundry Service", "type": "room", "sequence": 1133, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Laundry Service", "displayType": "1", "highlightedName": "Laundry Service", "childAttributes": [{"name": "Paid", "subAttributes": []}]}, {"name": "Mineral Water", "type": "room", "sequence": 1660, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Mineral Water", "displayType": null, "highlightedName": "Mineral Water", "childAttributes": [{"name": "Free", "subAttributes": []}]}]}, {"name": "Room Features", "facilities": [{"name": "Telephone", "type": "room", "sequence": 4, "categoryName": "Room Features", "tags": null, "attributeName": "Telephone", "displayType": null, "highlightedName": "Telephone", "childAttributes": []}, {"name": "Hypoallergenic Bedding", "type": "room", "sequence": 40, "categoryName": "Room Features", "tags": null, "attributeName": "Hypoallergenic Bedding", "displayType": null, "highlightedName": "Hypoallergenic Bedding", "childAttributes": []}, {"name": "Closet", "type": "room", "sequence": 48, "categoryName": "Room Features", "tags": null, "attributeName": "Closet", "displayType": null, "highlightedName": "Closet", "childAttributes": []}, {"name": "Mirror", "type": "room", "sequence": 133, "categoryName": "Room Features", "tags": null, "attributeName": "Mirror", "displayType": null, "highlightedName": "Mirror", "childAttributes": []}, {"name": "Hangers", "type": "room", "sequence": 157, "categoryName": "Room Features", "tags": null, "attributeName": "Hangers", "displayType": null, "highlightedName": "Hangers", "childAttributes": []}, {"name": "Chair", "type": "room", "sequence": 174, "categoryName": "Room Features", "tags": null, "attributeName": "Chair", "displayType": null, "highlightedName": "Chair", "childAttributes": []}, {"name": "Blackout Curtains", "type": "room", "sequence": 243, "categoryName": "Room Features", "tags": null, "attributeName": "Blackout curtains", "displayType": null, "highlightedName": "Blackout Curtains", "childAttributes": []}, {"name": "Minibar", "type": "room", "sequence": 502, "categoryName": "Room Features", "tags": null, "attributeName": "Mini Bar", "displayType": null, "highlightedName": "Minibar", "childAttributes": [{"name": "Paid", "subAttributes": []}]}, {"name": "Coffee Machine", "type": "room", "sequence": 4069, "categoryName": "Room Features", "tags": null, "attributeName": "Coffee Machine", "displayType": null, "highlightedName": "Coffee Machine", "childAttributes": []}]}, {"name": "Beds and Blanket", "facilities": [{"name": "Cushions", "type": "room", "sequence": 8, "categoryName": "Beds and Blanket", "tags": null, "attributeName": "Cushions", "displayType": null, "highlightedName": "Cushions", "childAttributes": []}, {"name": "Woollen Blanket", "type": "room", "sequence": 1380, "categoryName": "Beds and Blanket", "tags": null, "attributeName": "Blanket", "displayType": null, "highlightedName": "Woollen Blanket", "childAttributes": [{"name": "W<PERSON><PERSON>", "subAttributes": []}]}, {"name": "Pillows", "type": "room", "sequence": 4829, "categoryName": "Beds and Blanket", "tags": null, "attributeName": "Pillows", "displayType": "1", "highlightedName": "Pillows", "childAttributes": [{"name": "Non-Feather Pillow", "subAttributes": []}]}]}, {"name": "Safety and Security", "facilities": [{"name": "Safety", "type": "room", "sequence": 4025, "categoryName": "Safety and Security", "tags": null, "attributeName": "Safety", "displayType": null, "highlightedName": "Safety", "childAttributes": []}, {"name": "Security", "type": "room", "sequence": 4060, "categoryName": "Safety and Security", "tags": null, "attributeName": "Security", "displayType": "1", "highlightedName": "Security", "childAttributes": [{"name": "Access Control System", "subAttributes": []}]}, {"name": "Smoke Alarm", "type": "room", "sequence": 4065, "categoryName": "Safety and Security", "tags": null, "attributeName": "Smoke Alarm", "displayType": null, "highlightedName": "Smoke Alarm", "childAttributes": []}]}, {"name": "Media and Entertainment", "facilities": [{"name": "TV", "type": "room", "sequence": 1141, "categoryName": "Media and Entertainment", "tags": null, "attributeName": "TV", "displayType": "1", "highlightedName": "TV", "childAttributes": [{"name": "LED", "subAttributes": []}]}]}, {"name": "Bathroom", "facilities": [{"name": "Shaving Mirror", "type": "room", "sequence": 18, "categoryName": "Bathroom", "tags": null, "attributeName": "Shaving Mirror", "displayType": null, "highlightedName": "Shaving Mirror", "childAttributes": []}, {"name": "<PERSON><PERSON><PERSON>", "type": "room", "sequence": 61, "categoryName": "Bathroom", "tags": null, "attributeName": "<PERSON><PERSON><PERSON>", "displayType": null, "highlightedName": "<PERSON><PERSON><PERSON>", "childAttributes": []}, {"name": "Dental Kit", "type": "room", "sequence": 94, "categoryName": "Bathroom", "tags": null, "attributeName": "Dental Kit", "displayType": null, "highlightedName": "Dental Kit", "childAttributes": []}, {"name": "<PERSON><PERSON><PERSON>", "type": "room", "sequence": 119, "categoryName": "Bathroom", "tags": null, "attributeName": "<PERSON><PERSON><PERSON>", "displayType": null, "highlightedName": "<PERSON><PERSON><PERSON>", "childAttributes": []}, {"name": "Shower", "type": "room", "sequence": 130, "categoryName": "Bathroom", "tags": null, "attributeName": "Shower", "displayType": null, "highlightedName": "Shower", "childAttributes": []}, {"name": "Slippers", "type": "room", "sequence": 153, "categoryName": "Bathroom", "tags": null, "attributeName": "Slippers", "displayType": null, "highlightedName": "Slippers", "childAttributes": []}, {"name": "Bathrobes", "type": "room", "sequence": 154, "categoryName": "Bathroom", "tags": null, "attributeName": "Bathrobes", "displayType": null, "highlightedName": "Bathrobes", "childAttributes": []}, {"name": "Weighing Scale", "type": "room", "sequence": 175, "categoryName": "Bathroom", "tags": null, "attributeName": "Weighing Scale", "displayType": null, "highlightedName": "Weighing Scale", "childAttributes": []}, {"name": "Toiletries", "type": "room", "sequence": 191, "categoryName": "Bathroom", "tags": null, "attributeName": "Toiletries", "displayType": null, "highlightedName": "Toiletries", "childAttributes": []}, {"name": "Hot & Cold Water", "type": "room", "sequence": 240, "categoryName": "Bathroom", "tags": null, "attributeName": "Hot & Cold Water", "displayType": null, "highlightedName": "Hot & Cold Water", "childAttributes": []}]}, {"name": "Other Facilities", "facilities": [{"name": "Newspaper", "type": "room", "sequence": 192, "categoryName": "Other Facilities", "tags": null, "attributeName": "Newspaper", "displayType": null, "highlightedName": "Newspaper", "childAttributes": []}, {"name": "Alarm Clock", "type": "room", "sequence": 242, "categoryName": "Other Facilities", "tags": null, "attributeName": "Alarm Clock", "displayType": null, "highlightedName": "Alarm Clock", "childAttributes": []}, {"name": "Balcony", "type": "room", "sequence": 4966, "categoryName": "Other Facilities", "tags": null, "attributeName": "Balcony", "displayType": "1", "highlightedName": "Balcony", "childAttributes": [{"name": "Private", "subAttributes": []}]}, {"name": "Sanitizers", "type": "room", "sequence": 100000, "categoryName": "Other Facilities", "tags": null, "attributeName": "Sanitizers", "displayType": null, "highlightedName": "Sanitizers", "childAttributes": [{"name": "Hand Sanitizers", "subAttributes": []}]}]}], "highlightedFacilities": [{"name": "Popular with Guests", "facilities": [{"name": "24-hour Room Service", "type": "room", "sequence": -20072, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Room service", "displayType": null, "highlightedName": "24-hour Room Service", "childAttributes": [{"name": "24 hours", "subAttributes": []}]}, {"name": "Air Conditioning", "type": "room", "sequence": -20068, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Air Conditioning", "displayType": "1", "highlightedName": "Air Conditioning", "childAttributes": [{"name": "Room controlled", "subAttributes": []}]}, {"name": "Free Wi-Fi", "type": "room", "sequence": -20062, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Wifi", "displayType": null, "highlightedName": "Free Wi-Fi", "childAttributes": [{"name": "Free", "subAttributes": []}]}, {"name": "Electric Kettle", "type": "room", "sequence": -20060, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Electric Kettle", "displayType": null, "highlightedName": "Electric Kettle", "childAttributes": []}, {"name": "Iron/Ironing Board", "type": "room", "sequence": -20059, "categoryName": "Popular with Guests", "tags": null, "attributeName": "Iron/Ironing Board", "displayType": null, "highlightedName": "Iron/Ironing Board", "childAttributes": []}]}, {"name": "Room Features", "facilities": [{"name": "Charging Points", "type": "room", "sequence": -20058, "categoryName": "Room Features", "tags": null, "attributeName": "Charging points", "displayType": null, "highlightedName": "Charging Points", "childAttributes": []}]}, {"name": "Childcare", "facilities": [{"name": "Cribs", "type": "room", "sequence": -20080, "categoryName": "Childcare", "tags": null, "attributeName": "Cribs", "displayType": null, "highlightedName": "Cribs", "childAttributes": []}]}, {"name": "Safety and Security", "facilities": [{"name": "Electronic Safe", "type": "room", "sequence": -20064, "categoryName": "Safety and Security", "tags": null, "attributeName": "Safe", "displayType": null, "highlightedName": "Electronic Safe", "childAttributes": [{"name": "Electronic", "subAttributes": []}]}]}], "images": ["//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/room-imgs/201809171729412453-30065-80fbcbc8fe3d11e88af70242ac110002.jpg", "//gos3.ibcdn.com/3a55000c5c3111e9a90f0242ac110006.jpg", "//gos3.ibcdn.com/6089d3c45c3111e99d000242ac110006.jpg", "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/room-imgs/201809171729412453-30065-d1169250fe3d11e89e0f0242ac110002.jpg", "//gos3.ibcdn.com/8f788c86022311eba26d0242ac110002.png", "//gos3.ibcdn.com/529b04781cfa11ebbd5b0242ac110003.jpg", "//gos3.ibcdn.com/2deb61981d8811eba6c30242ac110003.jpg"]}, "cancellationTimeline": {"checkInDate": "16 Dec", "cancellationDate": "13 Dec", "subTitle": "Free Cancellation", "freeCancellationText": "Free Cancellation till 13 Dec 11:59 PM", "title": "STAY FLEXIBLE WITH", "bookingDate": "07 Dec"}, "pahx": false}], "rpcKey": null}], "paymentInfo": {"expiryDate": null, "nameOnCard": null, "cardNumber": null, "cardCode": null, "amountAfterTax": 89581.0, "amountBeforeTax": 76945.70999999999, "paymentMode": null, "billingCity": null, "billingState": null, "billingCountry": null, "billingPinCode": null, "payModeOption": null, "amountCharged": 0.0, "cardChargePolicy": null, "remainingAmount": 0.0, "amountChargedByOtherMedium": 0.0, "palBooking": false, "easyPayUrl": null, "payLaterTillDate": null, "amountChargedByWallet": 0.0, "chargedCurrency": null, "paymentModeType": null}, "bookingMetaInfo": {"soaResponseReferenceKey": null, "requestorType": null, "requestorId": null, "requestorIdContext": null, "requestorChannel": null, "token": null, "bookingId": null, "currencyCode": null, "earnedPoints": null, "loggedInUserName": null, "moAffiliateId": null, "moCustomerId": null, "clientTokens": {"serverIps": "*************"}}, "couponInfo": {"RECOMMEND": [], "REMOVED": []}, "availReqBody": {"affiliateId": "312379", "applicationId": "310", "channel": "Native", "checkin": "2020-12-29", "checkout": "2020-12-30", "cityCode": null, "countryCode": "IN", "currency": "INR", "guestRecommendationEnabled": {"maxRecommendations": "1", "text": "true"}, "hotelIds": ["201809171729412453"], "idContext": "MOB", "roomStayCandidates": null, "token": null, "requestType": "B2CAgent", "promotionReferenceCodes": null, "roomCode": null, "ratePlanCode": null, "mtKey": null, "visitorId": "55124586401046025695196530586650671212", "visitNumber": "2", "cdfContextId": "MOB", "bookingDevice": "ANDROID", "deviceId": "6410d9524bcdd210", "couponCount": 3, "numberOfAddons": 0, "appVersion": "8.2.3.RC1", "lob": "ANDROID", "domain": "B2C", "pageContext": "REVIEW", "expiryRequired": false, "responseFilterFlags": {"soldOutInfoReq": null, "staticData": false, "roomLevelDetails": true, "freeCancellationAvail": null, "bestCoupon": true, "priceInfoReq": true, "applyAbsorption": true, "shortlistRequired": false, "topRatedRequired": null, "topRatedCommentRequired": null, "trustYouBadgeRequired": null, "walletRequired": true, "cityTaxExclusive": true, "addOnRequired": true, "mmtPrime": false, "persuasionSeg": null, "populateAllRates": null, "seekSummaryRequired": false, "flyfishSummaryRequired": false, "dealOfTheDayRequired": null, "checkAvailibility": false, "brinRequired": null, "doubleBlackUser": false, "blackUser": false, "newCorp": false, "locus": true, "unmodifiedAmenities": false, "poisRequiredOnMap": false, "hotelCatAndPropNotRequiredInMeta": false, "extraAltAccoPropertiesRequired": false, "limitedFilterCall": false, "corporateMMRLocationsRequired": false}, "experimentData": "{\"HPI\":\"true\",\"OCCFCNR\":\"t\",\"REV\":\"4\",\"CHPC\":\"t\",\"AARI\":\"t\",\"RCPN\":\"t\",\"HLMV\":\"t\",\"HBH\":\"f\",\"MRS\":\"t\",\"SRR\":\"t\",\"ADDON\":\"t\",\"FBP\":\"t\",\"MC\":\"t\",\"BNPL\":\"t\",\"PLRS\":\"t\",\"BLACK\":\"t\",\"PDO\":\"PN\",\"DPCR\":\"1\",\"FLTRPRCBKT\":\"t\",\"EMI\":\"f\",\"SPCR\":\"2\",\"BRIN\":\"110\",\"ADDV\":\"f\",\"HRSRFHS\":\"true\",\"HIS\":\"1234\",\"WSP\":\"t\",\"APE\":\"10\",\"PAH\":\"5\",\"HSCFS\":\"4\",\"LVD\":\"\",\"PAH5\":\"t\",\"LCS\":\"t\",\"LVI\":\"\"}", "correlationKey": "b0400903-cfe8-4b17-8b98-d9444c7b443f", "isWalletRequired": null, "nationalityCode": null, "os": null, "supplierCode": "DERBY_DOORWAY", "payMode": "PAS", "roomCriteria": [{"hotelId": "201809171729412453", "mtKey": "N$$s9rHC9RN7n9FTCzzJQadfCQkJClxtkHarZerYL8QPtXXoUaNMbgsY%2BK7R%2FoLcVL8TgtdgxrH8uxLYS0eTXeZHvxFJvRPkcxJ5eaM0z9cIycbxXTQTDpPUSgWZQvkXBJT7w2OOG53%2BZvB%2FKEABte0PRNR1WEBMnL8Po0CSQVDvyMyDDrw3dR1aMv%2F6gCOa19NFMJgtNUpFw01Z03vvTED9FSYZtZr72yQ9PpKtNuatdkPAvJ6QtQf28P33Zr3oB21lwv2w4b6P612%2BV0nck86FtgNFgXIXA27zDrBajT7STBsvkICk8OsXDyjMHmL6vqWcd0TQylKynzLtt8ZA4nm84xkRpV5sSeswUHhKeqbfgO6un00YUKGLoGER%2BHmzpkwRa1X3K112yatKHQGllPHmzKwMJBjng1vcG3tuNfHYe%2Fp7cCHfK6YQgHHbCtLGRWK6Bz7KO62VOaNECr6hPuR08ycoKHQChNm6AXBKZ8koA63XnWJrn1L9U36dnRnH27eR6Dv6B7psssgwyIo%2Fa4ormcPYq%2Fvc4eRQ8C%2FjlprPvekrChr7sLl8PEt6aja7KBd4vgHK2oyS9o%2Bfk9QjgGFTlt2UcJ32lmmCCGJLwqYZPzcp0zomsw3G3N2tzQsjTjrB01XT1JbbNLg2%2FDChTOoUguuJk08zQsUG4vanqd4EwntoyUDgmjKH5CZ1YMUhp%2FIFzl6QhSRdlupz4Mok47ZrMt1KVCuiDii38a9wj%2FfVIOfx2k5GHOlh4n2BHvwgHQH%2FzYzvKvzrUgRA%2FqSFvOFPq4ZcC%2FSgp9Fz5TwLKuuIysM%2Fq2gBX7%2BdzZ0z4lJPbr8Qx3DxQ5M6R%2Bl%2BXFP84kvEaKBNrmAnlwGFbMPPM3Uwu2DC3rvkQHiv7q6MyElhtPD11AeM3nwl1mmtvY%2Fx4hhrAZIGIhu0MpQ%2BZO%2FsPcdUqZKLSNLZryP9ihGbyLBS8E9", "ratePlanCode": "K1RV^^^OGAD12:MSE:1121:MSE:DERBY_DOORWAY", "roomCode": "968752", "roomStayCandidates": [{"guestCounts": [{"count": "3", "ageQualifyingCode": "10", "ages": []}]}], "supplierCode": "DERBY_DOORWAY", "pricingKey": "s9rHC9RN7n8UOxz6VmMi2jljaYb4LqnUvNJSi+Ruq+lHXlbQY8yrQMjQUNA+w0EfHNK56ilmk8J8M/8XBwH+YiPGwVjkmD6GmaazrKiq2Sm8mQCRhb0xHYEc/UC8PpH/JJ3drIQW0DwXleTgT1H0nvpOSItg9giKGsIlUCOox1XSEwGc2YcHnETs990D/UMDCgq9L0iS+6kcpZA5E6j5SpLRMPIqcMmX4kn75hZt/P6H0t3ja3tKZQ==", "rpcc": "K1RV^^^OGAD12"}, {"hotelId": "201809171729412453", "mtKey": "defaultMtkey", "ratePlanCode": "K1^^^OGAD12:MSE:1121:MSE:DERBY_DOORWAY", "roomCode": "25493", "roomStayCandidates": [{"guestCounts": [{"count": "2", "ageQualifyingCode": "10", "ages": []}]}], "supplierCode": "DERBY_DOORWAY", "pricingKey": "s9rHC9RN7n8UOxz6VmMi2jljaYb4LqnUvNJSi+Ruq+lHXlbQY8yrQMjQUNA+w0EfHNK56ilmk8J8M/8XBwH+YiPGwVjkmD6GmaazrKiq2Sm8mQCRhb0xHYEc/UC8PpH/JJ3drIQW0DwXleTgT1H0nvpOSItg9giKGsIlUCOox1XSEwGc2YcHnETs990D/UMDCgq9L0iS+6kcpZA5E6j5SpLRMPIqcMmX4kn75hZt/P6H0t3ja3tKZQ==", "rpcc": "K1^^^OGAD12"}, {"hotelId": "201809171729412453", "mtKey": "defaultMtkey", "ratePlanCode": "T2^^^OGAD12:MSE:1121:MSE:DERBY_DOORWAY", "roomCode": "30065", "roomStayCandidates": [{"guestCounts": [{"count": "3", "ageQualifyingCode": "10", "ages": []}]}], "supplierCode": "DERBY_DOORWAY", "pricingKey": "s9rHC9RN7n8UOxz6VmMi2jljaYb4LqnUvNJSi+Ruq+lHXlbQY8yrQMjQUNA+w0EfHNK56ilmk8J8M/8XBwH+YiPGwVjkmD6GmaazrKiq2Sm8mQCRhb0xHYEc/UC8PpH/JJ3drIQW0DwXleTgT1H0nvpOSItg9giKGsIlUCOox1XSEwGc2YcHnETs990D/UMDCgq9L0iS+6kcpZA5E6j5SpLRMPIqcMmX4kn75hZt/P6H0t3ja3tKZQ==", "rpcc": "T2^^^OGAD12"}], "userAgent": "Apache-HttpClient/4.5.10 (Java/1.8.0_222)", "deviceUserName": "", "extraInfo": {"searchType": "R"}, "firstTimeUser": false, "srLat": null, "srLng": null, "authToken": "MAT119cc89dbefb779934f2950fae3c13739652444464c50b42389a295dba117e944f0f42334da2f9c07c8356a79fbdcc144P", "profileType": null, "subProfileType": null, "uuid": null, "emailCommId": null, "phoneCommId": null, "trafficSource": null, "notifCoupon": null, "paymentChannel": "Native", "deviceType": "Mobile", "bestOffersRequired": null, "bestOffersLimit": 0, "pricingKey": "s9rHC9RN7n8UOxz6VmMi2jljaYb4LqnUvNJSi+Ruq+lHXlbQY8yrQMjQUNA+w0EfHNK56ilmk8J8M/8XBwH+YiPGwVjkmD6GmaazrKiq2Sm8mQCRhb0xHYEc/UC8PpH/JJ3drIQW0DwXleTgT1H0nvpOSItg9giKGsIlUCOox1XSEwGc2YcHnETs990D/UMDCgq9L0iS+6kcpZA5E6j5SpLRMPIqcMmX4kn75hZt/P6H0t3ja3tKZQ==", "ebizDetails": null, "netRateSelected": false, "hydraSegments": null, "flightBooker": false, "wizardUser": false, "pnAvlbl": false, "quoteId": null, "emiDetails": null, "locationId": "CTGOI", "locationType": "city", "srCon": "IN", "srCty": "TG", "funnelSource": "HOTELS", "travelerDetailsList": null, "mcid": null, "corpSuppressHotelChains": null, "siteDomain": "IN", "domainCountry": null, "domainLanguage": null, "appliedFilterMap": null, "corpUserID": null, "mobile": null, "email": null, "corpAuthCode": null, "firstTimeUserState": 0, "roomInfoRequired": true, "loggedIn": false}, "referenceKey": "cc0fc4be-18c7-4d0b-8aac-4481a7a8d623", "totalDisplayFare": {"tax": {"value": 0.0}, "slashedPrice": {"value": 89581.0, "maxFraction": 0.0, "avgeragePriceNoTax": 29860.33, "averagePriceWithTax": 29860.33, "sellingPriceNoTax": 89581.0, "sellingPriceWithTax": 89581.0, "basePriceWoCommision": 0.0}, "actualPrice": {"value": 89581.0, "maxFraction": 0.0, "avgeragePriceNoTax": 29860.33, "averagePriceWithTax": 29860.33, "sellingPriceNoTax": 89581.0, "sellingPriceWithTax": 89581.0, "basePriceWoCommision": 67716.0}, "extraAdult": {"value": 0.0}, "totalTariff": {"grandTotal": 89581.0, "subTotal": 89581.0, "discount": 0.0, "taxes": 0.0, "extraPax": 0.0, "ddmu": 0.0}, "ddmu": 0.0, "totalRoomCount": 3, "blackDiscount": 0.0, "taxDetails": {"hotelTax": 12635.29, "markup": 0.0, "serviceTaxOnMarkup": 0.0, "taxExcluded": 0.0, "actualMarkup": 0.0, "ddMarkUp": 0.0}, "couponReceived": false, "couponSkipped": false, "ddApplied": false, "dda": "0.0", "displayPriceBreakDown": {"displayPrice": 89581.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 89581.0, "savingPerc": 0.0, "totalSaving": 0.0, "basePrice": 76946.0, "hotelTax": 12635.0, "hotelServiceCharge": 0.0, "mmtServiceCharge": 0.0, "mmtDiscount": 0.0, "blackDiscount": 0.0, "cdfDiscount": 0.0, "wallet": 0.0, "extraPaxPrice": 0.0, "pricingKey": "s9rHC9RN7n98RXWuZtkl4xfBtjxnB0qVKJUgEaBvk2cj6LEbpy7SSDnRvXJrA8d0Aq4JTgA6KFVEgMBUrjx20fKGn+ME08mWRA476n/oCdpqArixRELc2C+pdOT5ZCqY+uj+3G8NUZ7Zl8wiyFwwqJGPR3a+8byTiduC38P4ng4LZRpaIq1rTE+CmKyzZnlDgw/01OzVwap53r5ueziBq8SJmN6+TbmLKUm5yY5CQNE=", "pricingDivisor": 1, "totalTax": 12635.0, "effectivePrice": 89581.0, "brinInclusivePrice": 0.0, "hotelierCouponDiscount": 0.0, "gstDiscountHotelierCoupon": 0.0, "affiliateFee": 0.0, "totalAmount": 0.0, "metaDiscount": 0.0, "addonPrice": 0.0, "nonDiscountedAddonPrice": 0.0, "taxIncluded": true}, "commission": 9229.71, "conversionFactor": 1.0, "hotelierCouponDiscount": 0.0, "gstDiscountHotelierCoupon": 0.0, "hotelTax": 0.0, "hotelierServiceCharge": 0.0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0.0, "aajKaBhaoApplied": "NA", "taxExcluded": false}, "suppressPas": false, "doubleBlackValidated": false, "recommendCouponReq": "{\"numberOfCoupons\":3,\"transactionKey\":\"b0400903-cfe8-4b17-8b98-d9444c7b443f\",\"travellerCount\":8,\"lob\":\"HTL\",\"domain\":\"B2C\",\"isLoggedIn\":false,\"bookingDevice\":\"ANDROID\",\"checkInDate\":\"16-12-2020\",\"checkOutDate\":\"19-12-2020\",\"countryCode\":\"IN\",\"cityCode\":\"CTGOI\",\"contextId\":\"MOB\",\"cmpId\":\"312379\",\"adultCount\":8,\"infantCount\":0,\"childCount\":0,\"expiryRequired\":false,\"pageContext\":\"REVIEW\",\"deviceId\":\"6410d9524bcdd210\",\"numRooms\":3,\"appVersion\":\"8.2.3.RC1\",\"hotelDetails\":[{\"hotelId\":\"201809171729412453\",\"hotelChain\":\"*********\",\"bucketId\":\"DOM_HTL,B2C_DND,MOB_DND,NOMETA_BD,ARR11,TOT_CHAIN,N_OYO_CHAIN,TRUEDND,ASP,MMTBIG55,SALE_HILTON\",\"contextId\":\"MOB\",\"cmpId\":\"312379\",\"starRating\":5,\"transactionAmountPreTax\":76946.0,\"transactionAmount\":89581.0,\"paymentModel\":[\"PAS\",\"Default\"],\"adultCount\":0,\"childCount\":0,\"numRooms\":0,\"refundPolicy\":[\"Default\",\"Default\"],\"rateType\":\"B2C\",\"supplier\":\"DERBY_DOORWAY\",\"addOnDiscountMeta\":{\"discount\":0.0,\"discountType\":\"INSTANT\"},\"wltApplBonusAmt\":0.0,\"hotelPriceDetails\":{\"commission\":9229.71,\"markup\":0.0,\"mmtDiscount\":0.0,\"hotelTax\":12635.29},\"prc_sell_amt_wo_tax\":89581.0,\"prc_hotelier_promotion\":0.0}],\"sessionToken\":\"MAT119cc89dbefb779934f2950fae3c13739652444464c50b42389a295dba117e944f0f42334da2f9c07c8356a79fbdcc144P\",\"sessionId\":\"6410d9524bcdd210~2\",\"htlSearch\":false,\"isFirstTimeUser\":false}", "recommendedAddOns": {"CHARITY_ID": {"addOnType": "ADDON", "id": "CHARITY_ID", "type": "STATIC", "category": "ADDON_CHARITY_DOB", "value": 10, "price": 10, "alternateCurrencyPrice": 0.0, "lob": "DONATION", "title": "Donate ₹ 10 for COVID -19 Relief and Other Charity Initiatives", "validFrom": "2020-12-07", "expiry": "2020-12-09", "description": "Create an impact by donating to MakeMyTrip", "tnc": {"tncList": ["The amount received as donation will be used for the specified charitable causes. MakeMyTrip will donate the collected amount to MakeMyTrip Foundation (a public trust registered with charitable objects) or similar charitable organizations to help create a social impact "]}, "doubleRedemptionAllowed": true, "availableUnits": 1, "noOfUnits": 1, "paymentMode": ["PAS"], "bucketId": "ADDON_CHARITY_DOB", "shortDesc": "ADDON_CHARITY_DOB", "basePrice": 1000, "imageMap": {"bigIcon": "https://promos.makemytrip.com/images/deal.png"}, "priceMap": {"adult": {"displayPrice": 10.0, "displayPriceAlternateCurrency": 0.0, "nonDiscountedPrice": 10.0, "savingPerc": 0.0, "totalSaving": 0.0}}, "unitSelected": {"adult": 1, "child": 0}, "mobiusAddOn": false, "descriptions": [{"titleText": "Support COVID-19 relief work and safety initiatives.", "iconUrl": "https://promos.makemytrip.com/COVID/charity.png", "itemIconType": "charity_covid", "charityDescription": "Support COVID-19 relief work and safety initiatives. <a href=\"https://www.makemytrip.com/csr/covid-19-relief-efforts.html\" target=\"_blank\">Know More</a>"}, {"titleText": " Offset your carbon footprint by contributing to our green initiative.", "iconUrl": "https://imgak.mmtcdn.com/flights/assets/media/dt/review/charity_1.png?v=1", "itemIconType": "sprout_charity"}, {"titleText": "Ensure responsible tourism to restore, develop and protect heritage sites and monuments.", "iconUrl": "https://imgak.mmtcdn.com/flights/assets/media/dt/review/charity_2.png?v=1", "itemIconType": ""}], "tncUrl": "https://promos.makemytrip.com/charity-deduction-16112017.html", "hasSlot": false}}, "surgePercent": 0.0, "ddTxnData": {"ddExpName": null, "ddExpId": null, "txnId": "", "version": null, "surgePercent": 0.0, "policyId": null, "applyOn": null, "policy": null, "updatedOn": null, "userWalletAmount": 0.0}, "ddTxnDataMap": {"DYNAMIC_DISCOUNTING": [{"ddExpName": null, "ddExpId": null, "txnId": "", "version": null, "surgePercent": 0.0, "policyId": null, "applyOn": null, "policy": null, "updatedOn": null, "userWalletAmount": 0.0}]}, "walletAmountPaid": 0.0, "walletRuleRequest": "{\"outputType\":\"WALLET_SURGE\",\"lob\":[\"HTL\"],\"attributes\":{\"APP\":{\"platform\":\"ANDROID\",\"appVersion\":\"8.2.3.RC1\"},\"HTL\":{\"pax\":8,\"rooms\":3,\"night\":3,\"countryCode\":\"IN\",\"adultCount\":8,\"childCount\":0,\"checkOutDate\":\"2020-12-30\",\"checkInDate\":\"2020-12-29\",\"details\":[{\"id\":\"201809171729412453\",\"hotelChain\":\"*********\",\"bucketId\":[\"HTL5STWB0\"],\"contextId\":\"MOB\",\"cmpId\":\"312379\",\"starRating\":5,\"transactionAmountPreTax\":76945.70999999999,\"transactionAmount\":76945.70999999999,\"paymentModel\":[\"PAS\",\"Default\"],\"refundPolicy\":[\"Default\"]}],\"ap\":9}},\"commonAttributes\":{\"mmtAuth\":\"MAT119cc89dbefb779934f2950fae3c13739652444464c50b42389a295dba117e944f0f42334da2f9c07c8356a79fbdcc144P\",\"deviceId\":\"6410d9524bcdd210\",\"visitorId\":\"55124586401046025695196530586650671212\"}}", "ruleId": "cc17a0ff45434f08bd90fbe250d7a07b", "sessionId": "6410d9524bcdd210~2", "cpSessionId": "", "blackEligible": false, "blackEligibilityDays": 0, "blackRegSuccess": false, "regForBlack": false, "altAccoBooking": false, "walletEntity": {"status": "N", "defaultWalletBurn": {"bonusPercentage": 0, "maxBonus": 4000, "minBookingAmount": 0}, "wpmRule": {"description": "Bucket id HTL5STWB0", "expireAt": 1640975400000, "outputDetails": {"minBookingAmount": 0.0, "persuasionText": " ", "surgeFactor": 0.0, "maxAmount": 4000.0, "couponReductionPerc": 0.0, "isSurge": false}}}, "showOriginalPolicyToDbUsr": false, "panCardRequired": false, "alternateCurrencyConversionFactor": 0.0, "alternateCurrencySelected": false, "hydraSegments": [], "blackUser": false, "netRateSelected": false, "wizardUser": false, "chainCode": "*********", "pnAvlbl": false, "emiBucket": "", "expData": {"HPI": "true", "OCCFCNR": "t", "REV": "4", "CHPC": "t", "AARI": "t", "RCPN": "t", "HLMV": "t", "HBH": "f", "MRS": "t", "SRR": "t", "ADDON": "t", "FBP": "t", "MC": "t", "BNPL": "t", "PLRS": "t", "BLACK": "t", "PDO": "PN", "DPCR": "1", "FLTRPRCBKT": "t", "EMI": "f", "SPCR": "2", "BRIN": "110", "ADDV": "f", "HRSRFHS": "true", "HIS": "1234", "WSP": "t", "APE": "10", "PAH": "5", "HSCFS": "4", "LVD": "", "PAH5": "t", "LCS": "t", "LVI": ""}, "corporateCdfSkip": false, "paxCount": 8, "cancellationTimeline": {"checkInDate": "16 Dec", "cancellationDate": "13 Dec", "subTitle": "Free Cancellation", "freeCancellationText": "Free Cancellation till 13 Dec 11:59 PM", "title": "STAY FLEXIBLE WITH", "bookingDate": "07 Dec"}, "cdfddapplied": false, "packageRequest": true}, "contactUsInfo": {"emailId": null, "phoneNo": ["0124 4628747", "0124 4628748"]}}