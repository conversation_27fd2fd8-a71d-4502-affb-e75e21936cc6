{"roomType": {"2779": {"type": "RoomType", "ratePlanList": {"-8003108356971467794": {"type": "RatePlan", "mealPlans": [{"startDate": null, "endDate": null, "code": "EP", "value": "Room Only"}], "availDetails": {"status": "B", "count": 1, "numOfRooms": 1, "occupancyDetails": {"adult": 2, "child": 0, "infant": 0, "numOfRooms": 1, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "paymentDetails": {"paymentMode": "PAS", "originalAPPayMode": "PAS"}, "cancelPenaltyList": [{"cancelRules": [{"text": "<b>This booking is non-refundable and the tariff cannot be cancelled with zero fee.</b>"}], "penaltyDescription": {"name": "NON_REFUNDABLE", "description": "This tariff cannot be cancelled with zero fee. Any cancellations will be subject to a hotel fee as follows:From 2024-08-01 20:29:27 (destination time) till 2024-11-27 14:59:58 (destination time) - 100% of booking amount.After 2024-11-27 14:59:59 (destination time) - 100% of booking amount.Cancellations are only allowed before CheckIn."}, "mostRestrictive": "N"}], "displayFare": {"tax": {"value": 0}, "slashedPrice": {"value": 3580.44, "maxFraction": 0, "avgeragePriceNoTax": 3580.44, "sellingPriceNoTax": 3580.44, "sellingPriceWithTax": 3580.44, "basePriceWoCommision": 0, "actualTax": 0, "basePriceWoCommisionWoHcp": 0}, "actualPrice": {"value": 5139.11, "maxFraction": 0, "avgeragePriceNoTax": 2181.04, "sellingPriceNoTax": 5139.11, "sellingPriceWithTax": 5139.11, "basePriceWoCommision": 3044.75, "actualTax": 633.74, "basePriceWoCommisionWoHcp": 0}, "bestCouponByPaymode": {"PAS": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 462 Off!", "discountAmount": 462, "doubleDiscountEnabled": true, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 462, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 462}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 8.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}}, "otherCouponByPaymode": {"PAS": [{"couponCode": "IDFCINTEMI", "type": "ECOUPON", "description": "Exclusive Offer - IDFC Credit Card EMI Users. Get INR 537 Off", "discountAmount": 537, "doubleDiscountEnabled": false, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 537, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 537}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": false, "autoApplicable": false, "ddApplied": false, "netMargin": 0, "configId": "Config:32977:2024-07-30T19:16:35", "configPriority": 2000, "configType": "FLAT_DISCOUNTER", "manthanDdMarkUp": 0, "subDescription": "IDFCINTEMI", "promoIconLink": "https://gos3.ibcdn.com/IDFC-**********.png", "bankOffer": true, "ctaBankText": "View More", "ctaBankUrl": "https://www.goibibo.com/offers/hotel-offers/", "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, {"couponCode": "THAILANDSPECIAL", "type": "ECOUPON", "description": "Get 147 Off!", "discountAmount": 147, "doubleDiscountEnabled": false, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 147, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 147}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": false, "ddApplied": false, "netMargin": 14.***************, "configId": "Config:63047:2024-08-01T11:10:13", "configPriority": 90, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}]}, "extraAdult": {"value": 0}, "totalTariff": {"grandTotal": 3866.87, "subTotal": 5139.11, "discount": 1558.67, "taxes": 0, "extraPax": 0, "ddmu": 0}, "segmentId": "1126", "ddmu": 0, "totalRoomCount": 1, "blackDiscount": 0, "taxDetails": {"hotelTax": 633.74, "markup": 0, "serviceTaxOnMarkup": 0, "taxExcluded": 0, "actualMarkup": 286.43, "tcsAmount": 0, "ddMarkUp": 0}, "couponReceived": true, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 3118, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 5139, "savingPerc": 39, "totalSaving": 2021, "roundedOffDelta": 0, "basePrice": 5139, "hotelTax": 634, "hotelServiceCharge": 0, "mmtServiceCharge": 286, "mmtDiscount": 1559, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 462, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 462 Off!", "discountAmount": 462, "doubleDiscountEnabled": true, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 462}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 8.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n87IrozYVq2WWHr37oY9tarG+N656cYcWsbc2HMKOli3n6mB+PqvOppDaVRHKEI9ePWXnG7zThREkh99pm4jh+eGt6ceeg3MSSyHirPJOKQGRTPK3ipX4UmI4ZkjK1+hUa7EuTA3zlvFVuRn2JUJOcHzY64PhyLxEvxk26dGPemT1DJYXQ4ESx9lrp2E7cgpE2YyWELAiwr5sbaSYMcAsKsZnZUmlCjTqd9xGNz/Zw2fA7mED01mOtqnrhb7wUaJm72tY9DxXZ5cIeoAFPfakxKlVD89oGud36RVuNtV57T79zxNnMSe4lfvPs41t2UA/RnTQXpbPV9Lw==", "pricingDivisor": 1, "totalTax": 920, "effectivePrice": 3118, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 3118, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, "displayPriceBreakDownList": [{"displayPrice": 3118, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 5139, "savingPerc": 39, "totalSaving": 2021, "roundedOffDelta": 0, "basePrice": 5139, "hotelTax": 634, "hotelServiceCharge": 0, "mmtServiceCharge": 286, "mmtDiscount": 1559, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 462, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 462 Off!", "discountAmount": 462, "doubleDiscountEnabled": true, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 462}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 8.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n87IrozYVq2WWHr37oY9tarG+N656cYcWsbc2HMKOli3n6mB+PqvOppDaVRHKEI9ePWXnG7zThREkh99pm4jh+eGt6ceeg3MSSyHirPJOKQGRTPK3ipX4UmI4ZkjK1+hUa7EuTA3zlvFVuRn2JUJOcHzY64PhyLxEvxk26dGPemT1DJYXQ4ESx9lrp2E7cgpE2YyWELAiwr5sbaSYMcAsKsZnZUmlCjTqd9xGNz/Zw2fA7mED01mOtqnrhb7wUaJm72tY9DxXZ5cIeoAFPfakxKlVD89oGud36RVuNtV57T79zxNnMSe4lfvPs41t2UA/RnTQXpbPV9Lw==", "pricingDivisor": 1, "totalTax": 920, "effectivePrice": 3118, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 3118, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, {"displayPrice": 3043, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 5139, "savingPerc": 41, "totalSaving": 2096, "roundedOffDelta": 0, "basePrice": 5139, "hotelTax": 634, "hotelServiceCharge": 0, "mmtServiceCharge": 286, "mmtDiscount": 1559, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 537, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "IDFCINTEMI", "type": "ECOUPON", "description": "Exclusive Offer - IDFC Credit Card EMI Users. Get INR 537 Off", "discountAmount": 537, "doubleDiscountEnabled": false, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 537}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": false, "autoApplicable": false, "ddApplied": false, "netMargin": 0, "configId": "Config:32977:2024-07-30T19:16:35", "configPriority": 2000, "configType": "FLAT_DISCOUNTER", "manthanDdMarkUp": 0, "subDescription": "IDFCINTEMI", "promoIconLink": "https://gos3.ibcdn.com/IDFC-**********.png", "bankOffer": true, "ctaBankText": "View More", "ctaBankUrl": "https://www.goibibo.com/offers/hotel-offers/", "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n+0sk76+SJ6rkLDwOa7OUbveMCdY33ZgCyVVnHCY6MlI4bNCih7rXe7PumFKQ29/Pho4JI2GgwAYS7RrP6mkT+nmt8Nw01g8E/so7ndI+l3HnQi0ZCmZqPFCfu7VsO7C1CSmc5UQkHTozQnTlR22GcqAd/RB4SRRGEFLDBxvbxU0fZmQPomuDRAM2xw7D6Qtob++S1yTH7zNHb5p+u2G81KkDZA0qJ0t656z39Dr++PTI2umpfyb8u1sxuAhXTkNRZeYz40T8xzjiKRGA4X8ZNaDNyLh2LVitLRiuDECh8dP60F0BYad8b4b0awgjmYPQs=", "pricingDivisor": 1, "totalTax": 920, "effectivePrice": 3043, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 3043, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, {"displayPrice": 3433, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 5139, "savingPerc": 33, "totalSaving": 1706, "roundedOffDelta": 0, "basePrice": 5139, "hotelTax": 634, "hotelServiceCharge": 0, "mmtServiceCharge": 286, "mmtDiscount": 1559, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 147, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "THAILANDSPECIAL", "type": "ECOUPON", "description": "Get 147 Off!", "discountAmount": 147, "doubleDiscountEnabled": false, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 147}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": false, "ddApplied": false, "netMargin": 14.***************, "configId": "Config:63047:2024-08-01T11:10:13", "configPriority": 90, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n/a9iuMsVATtXmviCLd5u3pA9UdVGvQinbEiz/5hsxhfe/YafNLbLA3gDq7oEIW2PcTWeFtMP3U7j3VnLEpyrfMqTBuyl2q0ILnLb5Rzf+v6HtcP3DkeQ7oHaHW1B2sU+3G0UY0j630CDFj5BMqMs29jrPDXJmGy8AMQW7MIK5A9IVocUVt0ZPHX6O5f8kH47LgDnlIfIHpiQH/JO9cH92sAilrm1NjatWrtixAWO3m9y/RsxcjOYdHk+3hKw/HW3kO1ak3mISI7gAiDJPv5fPodDqRVyj4wcPT6DYpJthMSRXMgtYOQMWPB2nRp6hPXFUf04BpqURveQ==", "pricingDivisor": 1, "totalTax": 920, "effectivePrice": 3433, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 3433, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}], "commission": 535.7, "conversionFactor": 0.4244, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "slotRate": false, "hotelTax": 268.96, "hotelierServiceCharge": 0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "offersDataMap": {"offerDataMap": [{"key": "5149106440", "offers_OfferData": {"moderatedText": null, "amount": 661.5, "offerType": 3, "offerAmount": 0, "offerMessage": null, "offerIcon": null}}]}}, "supplierDetails": {"supplierCode": "INGO", "costPrice": 1519.54, "hotelierCurrencyCode": "THB", "dmcRatePlanCode": "990579404404", "accessCode": ""}, "roomTariff": [{"perNightPrice": 5139.11, "extraPrice": 0, "taxes": 0, "totalPricePerRoom": 5139.11, "numberOfAdults": 2, "numberOfChildren": 0, "roomDiscountPerNight": 1558.67, "roomDiscountTax": 0, "extraChildFarePerNight": 0, "startDate": "2024-11-27", "endDate": "2024-11-28", "roomNumber": "1", "hotelierServiceCharge": 0}], "ratePlanType": "", "mtKey": "-8003108356971467794", "ratePlanDesc": "", "checkCancellationPolicy": false, "checkInclusions": false, "segmentId": "1126", "inclusionAndPolicyAvailVar": false, "suppressPas": false, "ratePlanCode": "-8003108356971467794", "mealUpgradeRatePlan": false, "suppressed": false, "bnplExtended": false, "pahx": false, "rpcc": "990579404404", "lowest": false, "groupModifiedRatePlanCode": "", "netRate": false, "droolDiscountApplied": true, "instantConfirmation": "", "guarantee": false, "forkedRatePlan": false, "corpRate": false, "bnplApplicable": false, "corporateCdfSkip": false, "staycationDeal": false, "campaingText": "", "allInclusiveRate": false, "vendorRoomCode": "45000432038", "lowestRateLastMinuteDeal": false, "anyRatelastMinuteDeal": false, "lowestRateEarlyBirdDeal": false, "anyRateEarlybirdDeal": false, "rpBookingModel": "", "freeChildCount": 0, "freeChildAges": [], "mostPopularRateplan": false, "flexiCancelAddOn": {}, "flexiCancelDetails": {}, "trainExclusiveRateAvailable": false, "busExclusiveRateAvailable": false, "exclusiveFlyerRateAvailable": false, "vccEnabled": false, "luckyRateAvailable": false, "packageRoomRatePlan": false}, "-8590467771798016833": {"type": "RatePlan", "mealPlans": [{"startDate": null, "endDate": null, "code": "EP", "value": "Room Only"}], "availDetails": {"status": "B", "count": 1, "numOfRooms": 1, "occupancyDetails": {"adult": 2, "child": 0, "infant": 0, "numOfRooms": 1, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "paymentDetails": {"paymentMode": "PAS", "originalAPPayMode": "PAS"}, "cancelPenaltyList": [{"cancellationType": "FREE_CANCELLATON", "cancelRules": [{"text": "<b>Free Cancellation(100% refund) if you cancel this booking before ‪28 Oct, 2:59 PM‬</b>"}, {"text": "<b>Cancellations post that will be subject to a fee as follows</b>", "descText": [{"dateText": "Before ‪28 Oct, 2:59 PM‬", "feeText": "0.0% of booking amount"}, {"dateText": "‪28 Oct, 3:00 PM‬ to ‪20 Nov, 2:59 PM‬", "feeText": "Booking amount for 1.0 nights"}, {"dateText": "After ‪20 Nov, 3:00 PM‬", "feeText": "100.0% of booking amount"}, {"dateText": "After ‪27 Nov, 3:00 PM‬", "feeText": "100.0% of booking amount"}]}, {"text": "Cancellations are only allowed before the Check-In Time. All time mentioned above is in Destination Time."}], "penaltyDescription": {"name": "FREE_CANCELLATION", "description": "Free Cancellation (100% refund) if you cancel this booking before 2024-10-28 14:59:59 (destination time). Cancellations post that will be subject to a hotel fee as follows:From 2024-10-28 15:00:00 (destination time) till 2024-11-20 14:59:59 (destination time) - booking amount for 1 night(s).From 2024-11-20 15:00:00 (destination time) till 2024-11-27 14:59:59 (destination time) - 100% of booking amount.After 2024-11-27 15:00:00 (destination time) - 100% of booking amount.Cancellations are only allowed before CheckIn."}, "tillDate": "2024-10-28 14:59:59", "freeCancellationText": "Free Cancellation before ‪28 Oct 02:59 PM‬", "mostRestrictive": "N"}], "displayFare": {"tax": {"value": 0}, "slashedPrice": {"value": 3678.53, "maxFraction": 0, "avgeragePriceNoTax": 3678.53, "sellingPriceNoTax": 3678.53, "sellingPriceWithTax": 3678.53, "basePriceWoCommision": 0, "actualTax": 0, "basePriceWoCommisionWoHcp": 0}, "actualPrice": {"value": 5121.75, "maxFraction": 0, "avgeragePriceNoTax": 2173.67, "sellingPriceNoTax": 5121.75, "sellingPriceWithTax": 5121.75, "basePriceWoCommision": 3128.16, "actualTax": 651.11, "basePriceWoCommisionWoHcp": 0}, "bestCouponByPaymode": {"PAS": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 474 Off!", "discountAmount": 474, "doubleDiscountEnabled": true, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 474, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 474}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 7.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}}, "otherCouponByPaymode": {"PAS": [{"couponCode": "IDFCINTEMI", "type": "ECOUPON", "description": "Exclusive Offer - IDFC Credit Card EMI Users. Get INR 551 Off", "discountAmount": 551, "doubleDiscountEnabled": false, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 551, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 551}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": false, "autoApplicable": false, "ddApplied": false, "netMargin": 0, "configId": "Config:32977:2024-07-30T19:16:35", "configPriority": 2000, "configType": "FLAT_DISCOUNTER", "manthanDdMarkUp": 0, "subDescription": "IDFCINTEMI", "promoIconLink": "https://gos3.ibcdn.com/IDFC-**********.png", "bankOffer": true, "ctaBankText": "View More", "ctaBankUrl": "https://www.goibibo.com/offers/hotel-offers/", "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, {"couponCode": "THAILANDSPECIAL", "type": "ECOUPON", "description": "Get 151 Off!", "discountAmount": 151, "doubleDiscountEnabled": false, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 151, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 151}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": false, "ddApplied": false, "netMargin": 15.***************, "configId": "Config:63047:2024-08-01T11:10:13", "configPriority": 90, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}]}, "extraAdult": {"value": 0}, "totalTariff": {"grandTotal": 3972.81, "subTotal": 5121.75, "discount": 1443.21, "taxes": 0, "extraPax": 0, "ddmu": 0}, "segmentId": "1126", "ddmu": 0, "totalRoomCount": 1, "blackDiscount": 0, "taxDetails": {"hotelTax": 651.11, "markup": 0, "serviceTaxOnMarkup": 0, "taxExcluded": 0, "actualMarkup": 294.27, "tcsAmount": 0, "ddMarkUp": 0}, "couponReceived": true, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 3205, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 5122, "savingPerc": 37, "totalSaving": 1917, "roundedOffDelta": 0, "basePrice": 5122, "hotelTax": 651, "hotelServiceCharge": 0, "mmtServiceCharge": 294, "mmtDiscount": 1443, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 474, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 474 Off!", "discountAmount": 474, "doubleDiscountEnabled": true, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 474}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 7.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n+VH1Zz3U8qIsIYvuswKe0FZdj7uWLCoz/s4W5jF3xuxShDwJDSnENuRiC1SN7LZWRx5DWjnXrYaRGB+5E3AyE9ay+np863OK60zDvY4aXDm8EU0KbH9QNOIovSYagCFmUPZIANzbCJ3y+PT1yXLy3xpnC4o614TLHqPsf/JfOH2aTHobXpGfEaagFrFJlyafhdhcxuxF1bTpt3FQ1S9O5x+GbhBEaUzj1PXtgCDa9SIjSqcVQp61+7wxo0bZwtkmy0/cX0VLBvU4ayw5j0j3z4SdREVkqkm7v/dE62clIIVTtYa6jRASUr9ScwKcpPrQUKCqm0UIwfFw==", "pricingDivisor": 1, "totalTax": 945, "effectivePrice": 3205, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 3205, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, "displayPriceBreakDownList": [{"displayPrice": 3205, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 5122, "savingPerc": 37, "totalSaving": 1917, "roundedOffDelta": 0, "basePrice": 5122, "hotelTax": 651, "hotelServiceCharge": 0, "mmtServiceCharge": 294, "mmtDiscount": 1443, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 474, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 474 Off!", "discountAmount": 474, "doubleDiscountEnabled": true, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 474}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 7.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n+VH1Zz3U8qIsIYvuswKe0FZdj7uWLCoz/s4W5jF3xuxShDwJDSnENuRiC1SN7LZWRx5DWjnXrYaRGB+5E3AyE9ay+np863OK60zDvY4aXDm8EU0KbH9QNOIovSYagCFmUPZIANzbCJ3y+PT1yXLy3xpnC4o614TLHqPsf/JfOH2aTHobXpGfEaagFrFJlyafhdhcxuxF1bTpt3FQ1S9O5x+GbhBEaUzj1PXtgCDa9SIjSqcVQp61+7wxo0bZwtkmy0/cX0VLBvU4ayw5j0j3z4SdREVkqkm7v/dE62clIIVTtYa6jRASUr9ScwKcpPrQUKCqm0UIwfFw==", "pricingDivisor": 1, "totalTax": 945, "effectivePrice": 3205, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 3205, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, {"displayPrice": 3128, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 5122, "savingPerc": 39, "totalSaving": 1994, "roundedOffDelta": 0, "basePrice": 5122, "hotelTax": 651, "hotelServiceCharge": 0, "mmtServiceCharge": 294, "mmtDiscount": 1443, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 551, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "IDFCINTEMI", "type": "ECOUPON", "description": "Exclusive Offer - IDFC Credit Card EMI Users. Get INR 551 Off", "discountAmount": 551, "doubleDiscountEnabled": false, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 551}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": false, "autoApplicable": false, "ddApplied": false, "netMargin": 0, "configId": "Config:32977:2024-07-30T19:16:35", "configPriority": 2000, "configType": "FLAT_DISCOUNTER", "manthanDdMarkUp": 0, "subDescription": "IDFCINTEMI", "promoIconLink": "https://gos3.ibcdn.com/IDFC-**********.png", "bankOffer": true, "ctaBankText": "View More", "ctaBankUrl": "https://www.goibibo.com/offers/hotel-offers/", "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n+0sk76+SJ6rlx5rD9gNtr+ICVnE7AvBTIrz7PGhLd3RCO0GVl9vXQfDFMY1LUctEPpLoYVDRrc/yFjXEu3/gVZPA3OXGKhifRhMTmJ841Q0W2ZynGwi8UH2DCEn7wJsVlHTdMjpwyCY25nOv0SU1ZA6hPhgYWqYzV6atbmYG20UBJk2oY0ANimK5bUMiU8mPEKQDKnIK2yJAkX0Lk1mRtwWa03A5LF70wf5TcOxHJX5q+J9Qo50sQCeE+g2LkxWiFzaoXcVrQstwu7gbHfsXLs5DPg69sWlpEiB7WI0Z8qpYjrUuMA9ZrNcroYKGR2sUY=", "pricingDivisor": 1, "totalTax": 945, "effectivePrice": 3128, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 3128, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, {"displayPrice": 3528, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 5122, "savingPerc": 31, "totalSaving": 1594, "roundedOffDelta": 0, "basePrice": 5122, "hotelTax": 651, "hotelServiceCharge": 0, "mmtServiceCharge": 294, "mmtDiscount": 1443, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 151, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "THAILANDSPECIAL", "type": "ECOUPON", "description": "Get 151 Off!", "discountAmount": 151, "doubleDiscountEnabled": false, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 151}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": false, "ddApplied": false, "netMargin": 15.***************, "configId": "Config:63047:2024-08-01T11:10:13", "configPriority": 90, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n92q7DbCCKmFwxzx9c7tXsjOiRur8VlNC0KIw/VfsYVYv5zDo1CStshKYClpSRXkyD8GdSZYSEH6MbVUb55UN6zAI4pj2/AB0yTzbVXoLieftp8I8F9v+OBnJ8j/65THfQJTsPbJkjfb/+5toIJgntDxqZHvpeKTADOG8dHVbrBL29qHzBXCsRy/GFY8NTPMnvA6h+hs6Qq+EuZ8KFnjYdQBSaUIrEoCLLZV9C8uaOqfK9kZdKOWr1liFHWMa0UqhQa6IwwewYn6kydPVcwqarfyF/q8NfoCuXpMp/D8n+rjBTte1mqM6ZlA5Sgu4WCE71d2rTX/L1svg==", "pricingDivisor": 1, "totalTax": 945, "effectivePrice": 3528, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 3528, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}], "commission": 550.38, "conversionFactor": 0.4244, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "slotRate": false, "hotelTax": 276.33, "hotelierServiceCharge": 0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "offersDataMap": {"offerDataMap": [{"key": "5149106437", "offers_OfferData": {"moderatedText": null, "amount": 612.5, "offerType": 3, "offerAmount": 0, "offerMessage": null, "offerIcon": null}}]}}, "supplierDetails": {"supplierCode": "INGO", "costPrice": 1561.17, "hotelierCurrencyCode": "THB", "dmcRatePlanCode": "990579404404", "accessCode": ""}, "roomTariff": [{"perNightPrice": 5121.75, "extraPrice": 0, "taxes": 0, "totalPricePerRoom": 5121.75, "numberOfAdults": 2, "numberOfChildren": 0, "roomDiscountPerNight": 1443.21, "roomDiscountTax": 0, "extraChildFarePerNight": 0, "startDate": "2024-11-27", "endDate": "2024-11-28", "roomNumber": "1", "hotelierServiceCharge": 0}], "ratePlanType": "", "mtKey": "-8590467771798016833", "ratePlanDesc": "", "checkCancellationPolicy": false, "checkInclusions": false, "segmentId": "1126", "inclusionAndPolicyAvailVar": false, "suppressPas": false, "ratePlanCode": "-8590467771798016833", "mealUpgradeRatePlan": false, "suppressed": false, "bnplExtended": false, "pahx": false, "rpcc": "990579404404", "lowest": false, "groupModifiedRatePlanCode": "", "netRate": false, "droolDiscountApplied": true, "instantConfirmation": "", "guarantee": false, "forkedRatePlan": false, "corpRate": false, "cancellationTimeline": {"checkInDate": "27 Nov", "checkInDateTime": "3 PM", "cancellationDate": "28 Oct", "cancellationDateTime": "02:59 PM", "cancellationDateInDateFormat": "28-Oct-2024 14:59", "subTitle": "Free Cancellation", "freeCancellationText": "Free Cancellation till ‪28 Oct 02:59 PM‬", "title": "STAY FLEXIBLE WITH", "bookingDate": "02 Aug", "freeCancellationBenefits": [{"type": "", "text": "Free Cancellation till 28 Oct, 2:59 PM"}, {"type": "", "text": "No refund if cancelled after 28 Oct, 3:00 PM"}, {"type": "", "text": "No refund if cancelled after 20 Nov, 3:00 PM"}], "fcTextForPersuasion": "Free Cancellation before ‪28 Oct 02:59 PM‬", "cancellationPolicyTimelineList": [{"startDate": "01 Aug", "startDateTime": "08:29 PM", "endDate": "28 Oct", "endDateTime": "02:59 PM", "text": "100% Refund", "fcBenefit": {"type": "", "text": "Free Cancellation till 28 Oct, 2:59 PM"}, "refundable": true, "refundText": "100%", "type": "FULL_REFUND"}, {"startDate": "28 Oct", "startDateTime": "03:00 PM", "endDate": "20 Nov", "endDateTime": "02:59 PM", "text": "Non Refundable", "fcBenefit": {"type": "", "text": "No refund if cancelled after 28 Oct, 3:00 PM"}, "refundable": false, "refundText": "0%", "type": "NO_REFUND"}, {"startDate": "20 Nov", "startDateTime": "03:00 PM", "endDate": "27 Nov", "endDateTime": "02:59 PM", "text": "Non Refundable", "fcBenefit": {"type": "", "text": "No refund if cancelled after 20 Nov, 3:00 PM"}, "refundable": false, "refundText": "0%", "type": "NO_REFUND"}], "tillDate": "28-Oct-2024 14:59", "cardChargeTextTitle": "", "cardChargeTextMsg": "", "bnplTitleText": ""}, "bnplApplicable": false, "corporateCdfSkip": false, "staycationDeal": false, "campaingText": "", "allInclusiveRate": false, "vendorRoomCode": "45000432038", "lowestRateLastMinuteDeal": false, "anyRatelastMinuteDeal": false, "lowestRateEarlyBirdDeal": false, "anyRateEarlybirdDeal": false, "rpBookingModel": "", "freeChildCount": 0, "freeChildAges": [], "mostPopularRateplan": false, "flexiCancelAddOn": {}, "flexiCancelDetails": {}, "trainExclusiveRateAvailable": false, "busExclusiveRateAvailable": false, "exclusiveFlyerRateAvailable": false, "vccEnabled": false, "luckyRateAvailable": false, "packageRoomRatePlan": false}, "-8702224855078516819": {"type": "RatePlan", "mealPlans": [{"startDate": null, "endDate": null, "code": "CP", "value": "Breakfast"}], "availDetails": {"status": "B", "count": 1, "numOfRooms": 1, "occupancyDetails": {"adult": 2, "child": 0, "infant": 0, "numOfRooms": 1, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "paymentDetails": {"paymentMode": "PAS", "originalAPPayMode": "PAS"}, "cancelPenaltyList": [{"cancelRules": [{"text": "<b>This booking is non-refundable and the tariff cannot be cancelled with zero fee.</b>"}], "penaltyDescription": {"name": "NON_REFUNDABLE", "description": "This tariff cannot be cancelled with zero fee. Any cancellations will be subject to a hotel fee as follows:From 2024-08-01 20:29:27 (destination time) till 2024-11-27 14:59:58 (destination time) - 100% of booking amount.After 2024-11-27 14:59:59 (destination time) - 100% of booking amount.Cancellations are only allowed before CheckIn."}, "mostRestrictive": "N"}], "displayFare": {"tax": {"value": 0}, "slashedPrice": {"value": 3872.74, "maxFraction": 0, "avgeragePriceNoTax": 3872.74, "sellingPriceNoTax": 3872.74, "sellingPriceWithTax": 3872.74, "basePriceWoCommision": 0, "actualTax": 0, "basePriceWoCommisionWoHcp": 0}, "actualPrice": {"value": 5558.65, "maxFraction": 0, "avgeragePriceNoTax": 2359.09, "sellingPriceNoTax": 5558.65, "sellingPriceWithTax": 5558.65, "basePriceWoCommision": 3293.31, "actualTax": 685.46, "basePriceWoCommisionWoHcp": 0}, "bestCouponByPaymode": {"PAS": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 499 Off!", "discountAmount": 499, "doubleDiscountEnabled": true, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 499, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 499}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 7.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}}, "otherCouponByPaymode": {"PAS": [{"couponCode": "IDFCINTEMI", "type": "ECOUPON", "description": "Exclusive Offer - IDFC Credit Card EMI Users. Get INR 580 Off", "discountAmount": 580, "doubleDiscountEnabled": false, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 580, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 580}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": false, "autoApplicable": false, "ddApplied": false, "netMargin": 0, "configId": "Config:32977:2024-07-30T19:16:35", "configPriority": 2000, "configType": "FLAT_DISCOUNTER", "manthanDdMarkUp": 0, "subDescription": "IDFCINTEMI", "promoIconLink": "https://gos3.ibcdn.com/IDFC-**********.png", "bankOffer": true, "ctaBankText": "View More", "ctaBankUrl": "https://www.goibibo.com/offers/hotel-offers/", "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, {"couponCode": "THAILANDSPECIAL", "type": "ECOUPON", "description": "Get 159 Off!", "discountAmount": 159, "doubleDiscountEnabled": false, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 159, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 159}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": false, "ddApplied": false, "netMargin": 14.***************, "configId": "Config:63047:2024-08-01T11:10:13", "configPriority": 90, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}]}, "extraAdult": {"value": 0}, "totalTariff": {"grandTotal": 4182.56, "subTotal": 5558.65, "discount": 1685.91, "taxes": 0, "extraPax": 0, "ddmu": 0}, "segmentId": "1126", "ddmu": 0, "totalRoomCount": 1, "blackDiscount": 0, "taxDetails": {"hotelTax": 685.46, "markup": 0, "serviceTaxOnMarkup": 0, "taxExcluded": 0, "actualMarkup": 309.83, "tcsAmount": 0, "ddMarkUp": 0}, "couponReceived": true, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 3374, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 5559, "savingPerc": 39, "totalSaving": 2185, "roundedOffDelta": 0, "basePrice": 5559, "hotelTax": 685, "hotelServiceCharge": 0, "mmtServiceCharge": 310, "mmtDiscount": 1686, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 499, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 499 Off!", "discountAmount": 499, "doubleDiscountEnabled": true, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 499}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 7.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n8To8ofl9x9MQtUX8/5p09lVipPTCIBXNMc3etEH2+1bTc5qAPhCh3wmWV+3eARHWiJtPGBalhBm0NuO5xT+3Oz73rHtU4KNP3mRxR1M8ygKY1F8Tle5B0CO2nwY9bJQxmbDcX5JezK/1OTeifBkvlnExB4IXa9tZTrXxOfAI9EEHLbFJFdHW6NNcTsYAIYewDsVzEwjlg3dvddBy3W6q8fz67gBOCWTSRilBWMpIPy5TwQkieIwQRFrgb1Z49xvI7DKq7FB+YeRmYpsoGYyjwa4W0MHTu1ban68h9k4lJAMSkFnrvwnOB8aUK5wA+3gFVboSAvDj0HEg==", "pricingDivisor": 1, "totalTax": 995, "effectivePrice": 3374, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 3374, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, "displayPriceBreakDownList": [{"displayPrice": 3374, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 5559, "savingPerc": 39, "totalSaving": 2185, "roundedOffDelta": 0, "basePrice": 5559, "hotelTax": 685, "hotelServiceCharge": 0, "mmtServiceCharge": 310, "mmtDiscount": 1686, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 499, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 499 Off!", "discountAmount": 499, "doubleDiscountEnabled": true, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 499}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 7.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n8To8ofl9x9MQtUX8/5p09lVipPTCIBXNMc3etEH2+1bTc5qAPhCh3wmWV+3eARHWiJtPGBalhBm0NuO5xT+3Oz73rHtU4KNP3mRxR1M8ygKY1F8Tle5B0CO2nwY9bJQxmbDcX5JezK/1OTeifBkvlnExB4IXa9tZTrXxOfAI9EEHLbFJFdHW6NNcTsYAIYewDsVzEwjlg3dvddBy3W6q8fz67gBOCWTSRilBWMpIPy5TwQkieIwQRFrgb1Z49xvI7DKq7FB+YeRmYpsoGYyjwa4W0MHTu1ban68h9k4lJAMSkFnrvwnOB8aUK5wA+3gFVboSAvDj0HEg==", "pricingDivisor": 1, "totalTax": 995, "effectivePrice": 3374, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 3374, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, {"displayPrice": 3293, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 5559, "savingPerc": 41, "totalSaving": 2266, "roundedOffDelta": 0, "basePrice": 5559, "hotelTax": 685, "hotelServiceCharge": 0, "mmtServiceCharge": 310, "mmtDiscount": 1686, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 580, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "IDFCINTEMI", "type": "ECOUPON", "description": "Exclusive Offer - IDFC Credit Card EMI Users. Get INR 580 Off", "discountAmount": 580, "doubleDiscountEnabled": false, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 580}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": false, "autoApplicable": false, "ddApplied": false, "netMargin": 0, "configId": "Config:32977:2024-07-30T19:16:35", "configPriority": 2000, "configType": "FLAT_DISCOUNTER", "manthanDdMarkUp": 0, "subDescription": "IDFCINTEMI", "promoIconLink": "https://gos3.ibcdn.com/IDFC-**********.png", "bankOffer": true, "ctaBankText": "View More", "ctaBankUrl": "https://www.goibibo.com/offers/hotel-offers/", "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n+0sk76+SJ6rqghJy2DaQj4rnF2rdtVTH2NYg6csEaqI8Fg8h2ZjheSP6XqO/SFph2azMmE7RpfKAV+CXIPWyIR/+LhCxTm0+YQ6uH5TLfcT2DaLemdiAZ6J5MG1Fk8tvtm17VdQpJj52C4NtM/lRIoNCcbGEYdW7TbvMXWS8Fg8nmIO8JKd9PqQ6MP/TuLWsXdAgU/KP/4osMw/rIbvLRm972W4upFKNeRR7aLkOa0X2TLXkTC1m6eLIADHihdkLr7POfVJu3Vj4vxnRKBAMQBZxhbkgFEHGCKpG4tx6a9Jayp1TlCTxBXdnHMCBL7j+w=", "pricingDivisor": 1, "totalTax": 995, "effectivePrice": 3293, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 3293, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, {"displayPrice": 3714, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 5559, "savingPerc": 33, "totalSaving": 1845, "roundedOffDelta": 0, "basePrice": 5559, "hotelTax": 685, "hotelServiceCharge": 0, "mmtServiceCharge": 310, "mmtDiscount": 1686, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 159, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "THAILANDSPECIAL", "type": "ECOUPON", "description": "Get 159 Off!", "discountAmount": 159, "doubleDiscountEnabled": false, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 159}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": false, "ddApplied": false, "netMargin": 14.***************, "configId": "Config:63047:2024-08-01T11:10:13", "configPriority": 90, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n9arbSenMJnD4G4ZRlPO+Z/luy3e0fY+dzr2y6E0MUqxRaTCeXtj5KeFlQhUTyGwHqdmellzaBdwFX8q/hPTsTbqD+Bh34cF19QkY/nyqlSuMCtyHlopTY4rdB4JJtsiKDqJl8FHNjTzEYcp3hSINetKDTRrJ33C3SByyp2kAzvSQdrhU5exgmyovWHAltuEEW0HfVmrxAYp+ECrinNyi1KKJY+ZYAJNJSMG7xeBcuEW/qHenqVsK9IUpOW0RiNkEPxdDAChXMOmkPKRvfe2DEAFNFs/DSPRzokb3YQc33lrrPJtYXNykS3IOfUUrIqsSnQ588XDTGKQg==", "pricingDivisor": 1, "totalTax": 995, "effectivePrice": 3714, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 3714, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}], "commission": 579.43, "conversionFactor": 0.4244, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "slotRate": false, "hotelTax": 290.91, "hotelierServiceCharge": 0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "offersDataMap": {"offerDataMap": [{"key": "5149106440", "offers_OfferData": {"moderatedText": null, "amount": 715.5, "offerType": 3, "offerAmount": 0, "offerMessage": null, "offerIcon": null}}]}}, "supplierDetails": {"supplierCode": "INGO", "costPrice": 1643.59, "hotelierCurrencyCode": "THB", "dmcRatePlanCode": "990000776177", "accessCode": ""}, "roomTariff": [{"perNightPrice": 5558.65, "extraPrice": 0, "taxes": 0, "totalPricePerRoom": 5558.65, "numberOfAdults": 2, "numberOfChildren": 0, "roomDiscountPerNight": 1685.91, "roomDiscountTax": 0, "extraChildFarePerNight": 0, "startDate": "2024-11-27", "endDate": "2024-11-28", "roomNumber": "1", "hotelierServiceCharge": 0}], "ratePlanType": "", "mtKey": "-8702224855078516819", "ratePlanDesc": "", "checkCancellationPolicy": false, "checkInclusions": false, "segmentId": "1126", "inclusionAndPolicyAvailVar": false, "suppressPas": false, "ratePlanCode": "-8702224855078516819", "mealUpgradeRatePlan": true, "suppressed": false, "bnplExtended": false, "pahx": false, "rpcc": "990000776177", "lowest": false, "groupModifiedRatePlanCode": "", "netRate": false, "droolDiscountApplied": true, "instantConfirmation": "", "guarantee": false, "forkedRatePlan": false, "corpRate": false, "bnplApplicable": false, "corporateCdfSkip": false, "staycationDeal": false, "campaingText": "", "allInclusiveRate": false, "vendorRoomCode": "45000432038", "lowestRateLastMinuteDeal": false, "anyRatelastMinuteDeal": false, "lowestRateEarlyBirdDeal": false, "anyRateEarlybirdDeal": false, "rpBookingModel": "", "freeChildCount": 0, "freeChildAges": [], "mostPopularRateplan": false, "flexiCancelAddOn": {}, "flexiCancelDetails": {}, "trainExclusiveRateAvailable": false, "busExclusiveRateAvailable": false, "exclusiveFlyerRateAvailable": false, "vccEnabled": false, "luckyRateAvailable": false, "packageRoomRatePlan": false}, "-3401066947991223395": {"type": "RatePlan", "mealPlans": [{"startDate": null, "endDate": null, "code": "CP", "value": "Breakfast"}], "availDetails": {"status": "B", "count": 1, "numOfRooms": 1, "occupancyDetails": {"adult": 2, "child": 0, "infant": 0, "numOfRooms": 1, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "paymentDetails": {"paymentMode": "PAS", "originalAPPayMode": "PAS"}, "cancelPenaltyList": [{"cancellationType": "FREE_CANCELLATON", "cancelRules": [{"text": "<b>Free Cancellation(100% refund) if you cancel this booking before ‪28 Oct, 2:59 PM‬</b>"}, {"text": "<b>Cancellations post that will be subject to a fee as follows</b>", "descText": [{"dateText": "Before ‪28 Oct, 2:59 PM‬", "feeText": "0.0% of booking amount"}, {"dateText": "‪28 Oct, 3:00 PM‬ to ‪20 Nov, 2:59 PM‬", "feeText": "Booking amount for 1.0 nights"}, {"dateText": "After ‪20 Nov, 3:00 PM‬", "feeText": "100.0% of booking amount"}, {"dateText": "After ‪27 Nov, 3:00 PM‬", "feeText": "100.0% of booking amount"}]}, {"text": "Cancellations are only allowed before the Check-In Time. All time mentioned above is in Destination Time."}], "penaltyDescription": {"name": "FREE_CANCELLATION", "description": "Free Cancellation (100% refund) if you cancel this booking before 2024-10-28 14:59:59 (destination time). Cancellations post that will be subject to a hotel fee as follows:From 2024-10-28 15:00:00 (destination time) till 2024-11-20 14:59:59 (destination time) - booking amount for 1 night(s).From 2024-11-20 15:00:00 (destination time) till 2024-11-27 14:59:59 (destination time) - 100% of booking amount.After 2024-11-27 15:00:00 (destination time) - 100% of booking amount.Cancellations are only allowed before CheckIn."}, "tillDate": "2024-10-28 14:59:59", "freeCancellationText": "Free Cancellation before ‪28 Oct 02:59 PM‬", "mostRestrictive": "N"}], "displayFare": {"tax": {"value": 0}, "slashedPrice": {"value": 3978.84, "maxFraction": 0, "avgeragePriceNoTax": 3978.84, "sellingPriceNoTax": 3978.84, "sellingPriceWithTax": 3978.84, "basePriceWoCommision": 0, "actualTax": 0, "basePriceWoCommisionWoHcp": 0}, "actualPrice": {"value": 5539.87, "maxFraction": 0, "avgeragePriceNoTax": 2351.12, "sellingPriceNoTax": 5539.87, "sellingPriceWithTax": 5539.87, "basePriceWoCommision": 3383.55, "actualTax": 704.24, "basePriceWoCommisionWoHcp": 0}, "bestCouponByPaymode": {"PAS": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 513 Off!", "discountAmount": 513, "doubleDiscountEnabled": true, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 513, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 513}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 7.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}}, "otherCouponByPaymode": {"PAS": [{"couponCode": "IDFCINTEMI", "type": "ECOUPON", "description": "Exclusive Offer - IDFC Credit Card EMI Users. Get INR 596 Off", "discountAmount": 596, "doubleDiscountEnabled": false, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 596, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 596}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": false, "autoApplicable": false, "ddApplied": false, "netMargin": 0, "configId": "Config:32977:2024-07-30T19:16:35", "configPriority": 2000, "configType": "FLAT_DISCOUNTER", "manthanDdMarkUp": 0, "subDescription": "IDFCINTEMI", "promoIconLink": "https://gos3.ibcdn.com/IDFC-**********.png", "bankOffer": true, "ctaBankText": "View More", "ctaBankUrl": "https://www.goibibo.com/offers/hotel-offers/", "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, {"couponCode": "THAILANDSPECIAL", "type": "ECOUPON", "description": "Get 163 Off!", "discountAmount": 163, "doubleDiscountEnabled": false, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 163, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 163}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": false, "ddApplied": false, "netMargin": 15.***************, "configId": "Config:63047:2024-08-01T11:10:13", "configPriority": 90, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}]}, "extraAdult": {"value": 0}, "totalTariff": {"grandTotal": 4297.15, "subTotal": 5539.87, "discount": 1561.03, "taxes": 0, "extraPax": 0, "ddmu": 0}, "segmentId": "1126", "ddmu": 0, "totalRoomCount": 1, "blackDiscount": 0, "taxDetails": {"hotelTax": 704.24, "markup": 0, "serviceTaxOnMarkup": 0, "taxExcluded": 0, "actualMarkup": 318.31, "tcsAmount": 0, "ddMarkUp": 0}, "couponReceived": true, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 3466, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 5540, "savingPerc": 37, "totalSaving": 2074, "roundedOffDelta": 0, "basePrice": 5540, "hotelTax": 704, "hotelServiceCharge": 0, "mmtServiceCharge": 318, "mmtDiscount": 1561, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 513, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 513 Off!", "discountAmount": 513, "doubleDiscountEnabled": true, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 513}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 7.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n8To8ofl9x9MQtUX8/5p09lpGWMSoi06INYpxhQUODKsa7FFYwgVeMFhXqG/l0e487P1NvPopFlYTlsYOyQ0r42Ck0fQdrkItQVnO/qaseyHHRasupAIFi/gdUZL2RkHM19POnxnjmbTGcUgbzLU2fx7zovKhADO4ljNqrXbUTNIo7XarDbdapuXcxowGvoIriWh9QnzUjT0NTcQaozOFAcIZ0uMqfHuwsr8DuS14ixDCgt22ltNeXDQEwxFuD/kAXC5a7Mf3cNeWJqK7lZ5pUWNEbh9R8RqhxZjVosnyWmsPEHVo6SyU5IFD/3c35geE85Kev0ECtv6w==", "pricingDivisor": 1, "totalTax": 1023, "effectivePrice": 3466, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 3466, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, "displayPriceBreakDownList": [{"displayPrice": 3466, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 5540, "savingPerc": 37, "totalSaving": 2074, "roundedOffDelta": 0, "basePrice": 5540, "hotelTax": 704, "hotelServiceCharge": 0, "mmtServiceCharge": 318, "mmtDiscount": 1561, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 513, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 513 Off!", "discountAmount": 513, "doubleDiscountEnabled": true, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 513}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 7.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n8To8ofl9x9MQtUX8/5p09lpGWMSoi06INYpxhQUODKsa7FFYwgVeMFhXqG/l0e487P1NvPopFlYTlsYOyQ0r42Ck0fQdrkItQVnO/qaseyHHRasupAIFi/gdUZL2RkHM19POnxnjmbTGcUgbzLU2fx7zovKhADO4ljNqrXbUTNIo7XarDbdapuXcxowGvoIriWh9QnzUjT0NTcQaozOFAcIZ0uMqfHuwsr8DuS14ixDCgt22ltNeXDQEwxFuD/kAXC5a7Mf3cNeWJqK7lZ5pUWNEbh9R8RqhxZjVosnyWmsPEHVo6SyU5IFD/3c35geE85Kev0ECtv6w==", "pricingDivisor": 1, "totalTax": 1023, "effectivePrice": 3466, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 3466, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, {"displayPrice": 3383, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 5540, "savingPerc": 39, "totalSaving": 2157, "roundedOffDelta": 0, "basePrice": 5540, "hotelTax": 704, "hotelServiceCharge": 0, "mmtServiceCharge": 318, "mmtDiscount": 1561, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 596, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "IDFCINTEMI", "type": "ECOUPON", "description": "Exclusive Offer - IDFC Credit Card EMI Users. Get INR 596 Off", "discountAmount": 596, "doubleDiscountEnabled": false, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 596}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": false, "autoApplicable": false, "ddApplied": false, "netMargin": 0, "configId": "Config:32977:2024-07-30T19:16:35", "configPriority": 2000, "configType": "FLAT_DISCOUNTER", "manthanDdMarkUp": 0, "subDescription": "IDFCINTEMI", "promoIconLink": "https://gos3.ibcdn.com/IDFC-**********.png", "bankOffer": true, "ctaBankText": "View More", "ctaBankUrl": "https://www.goibibo.com/offers/hotel-offers/", "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n/ilDBzZ/izbPwpI9/x3rX/+V/le53DSbITHg6AVkPQFIT7j+twZiXy8q4T9uvcU52QQaqDql/aOzioDstQEQi3n4TgPuSaez7YP+WJM/CnfvHBQ7rr5T0eZ227gHbW+/xgZKWI6n5JKeupMB/eTbkaQYiP/gJEYEU+zngPFBZ9mGxrVgpstLm9j0PCGsQNDTbEq5LUAsn6nV+75fp9idLV0tP1i5aJ6nkCmb88UC2rMl2WsAB42fLaqRracitG5Xc84eR/QKwjXNdFdioIMm6f9jP906k6+7fQhcMWyYVIqEhAVuUlC6M9s+3mFrzUROc=", "pricingDivisor": 1, "totalTax": 1023, "effectivePrice": 3383, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 3383, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, {"displayPrice": 3816, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 5540, "savingPerc": 31, "totalSaving": 1724, "roundedOffDelta": 0, "basePrice": 5540, "hotelTax": 704, "hotelServiceCharge": 0, "mmtServiceCharge": 318, "mmtDiscount": 1561, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 163, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "THAILANDSPECIAL", "type": "ECOUPON", "description": "Get 163 Off!", "discountAmount": 163, "doubleDiscountEnabled": false, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 163}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": false, "ddApplied": false, "netMargin": 15.***************, "configId": "Config:63047:2024-08-01T11:10:13", "configPriority": 90, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n8JCwWOF303F2Ixu/sqcpgq9TNKt7R5fpYoDknAcHzp3UrNdMRF0k8p6nL1ZvBgzz1h215UxARZzsCHqGFSlpbN/EtMhEmJ42s/QWejgK4SMcCWavxtkSQJc04U5hAr6D4SOdTbQ/BVr97uCnBvqLCSOZ51cJ6hDmgHJXg9j2tU04dPvXsIaN40yJpmUmeMZ9XxrwmEcwPTDayXtYL2pjveeYtfst3HPCdGwyTgfTI/L/tTRxj+BSTQrZ7I/JPvs1cLYkUsD+lfyRSuqZCLaZsio7XwGHWowimT40N+Pox2mWtWf8fL9ND6qPWJVlsJFVP8K8rqHhIkqg==", "pricingDivisor": 1, "totalTax": 1023, "effectivePrice": 3816, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 3816, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}], "commission": 595.29, "conversionFactor": 0.4244, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "slotRate": false, "hotelTax": 298.88, "hotelierServiceCharge": 0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "offersDataMap": {"offerDataMap": [{"key": "5149106437", "offers_OfferData": {"moderatedText": null, "amount": 662.5, "offerType": 3, "offerAmount": 0, "offerMessage": null, "offerIcon": null}}]}}, "supplierDetails": {"supplierCode": "INGO", "costPrice": 1688.62, "hotelierCurrencyCode": "THB", "dmcRatePlanCode": "990000776177", "accessCode": ""}, "roomTariff": [{"perNightPrice": 5539.87, "extraPrice": 0, "taxes": 0, "totalPricePerRoom": 5539.87, "numberOfAdults": 2, "numberOfChildren": 0, "roomDiscountPerNight": 1561.03, "roomDiscountTax": 0, "extraChildFarePerNight": 0, "startDate": "2024-11-27", "endDate": "2024-11-28", "roomNumber": "1", "hotelierServiceCharge": 0}], "ratePlanType": "", "mtKey": "-3401066947991223395", "ratePlanDesc": "", "checkCancellationPolicy": false, "checkInclusions": false, "segmentId": "1126", "inclusionAndPolicyAvailVar": false, "suppressPas": false, "ratePlanCode": "-3401066947991223395", "mealUpgradeRatePlan": true, "suppressed": false, "bnplExtended": false, "pahx": false, "rpcc": "990000776177", "lowest": false, "groupModifiedRatePlanCode": "", "netRate": false, "droolDiscountApplied": true, "instantConfirmation": "", "guarantee": false, "forkedRatePlan": false, "corpRate": false, "cancellationTimeline": {"checkInDate": "27 Nov", "checkInDateTime": "3 PM", "cancellationDate": "28 Oct", "cancellationDateTime": "02:59 PM", "cancellationDateInDateFormat": "28-Oct-2024 14:59", "subTitle": "Free Cancellation", "freeCancellationText": "Free Cancellation till ‪28 Oct 02:59 PM‬", "title": "STAY FLEXIBLE WITH", "bookingDate": "02 Aug", "freeCancellationBenefits": [{"type": "", "text": "Free Cancellation till 28 Oct, 2:59 PM"}, {"type": "", "text": "No refund if cancelled after 28 Oct, 3:00 PM"}, {"type": "", "text": "No refund if cancelled after 20 Nov, 3:00 PM"}], "fcTextForPersuasion": "Free Cancellation before ‪28 Oct 02:59 PM‬", "cancellationPolicyTimelineList": [{"startDate": "01 Aug", "startDateTime": "08:29 PM", "endDate": "28 Oct", "endDateTime": "02:59 PM", "text": "100% Refund", "fcBenefit": {"type": "", "text": "Free Cancellation till 28 Oct, 2:59 PM"}, "refundable": true, "refundText": "100%", "type": "FULL_REFUND"}, {"startDate": "28 Oct", "startDateTime": "03:00 PM", "endDate": "20 Nov", "endDateTime": "02:59 PM", "text": "Non Refundable", "fcBenefit": {"type": "", "text": "No refund if cancelled after 28 Oct, 3:00 PM"}, "refundable": false, "refundText": "0%", "type": "NO_REFUND"}, {"startDate": "20 Nov", "startDateTime": "03:00 PM", "endDate": "27 Nov", "endDateTime": "02:59 PM", "text": "Non Refundable", "fcBenefit": {"type": "", "text": "No refund if cancelled after 20 Nov, 3:00 PM"}, "refundable": false, "refundText": "0%", "type": "NO_REFUND"}], "tillDate": "28-Oct-2024 14:59", "cardChargeTextTitle": "", "cardChargeTextMsg": "", "bnplTitleText": ""}, "bnplApplicable": false, "corporateCdfSkip": false, "staycationDeal": false, "campaingText": "", "allInclusiveRate": false, "vendorRoomCode": "45000432038", "lowestRateLastMinuteDeal": false, "anyRatelastMinuteDeal": false, "lowestRateEarlyBirdDeal": false, "anyRateEarlybirdDeal": false, "rpBookingModel": "", "freeChildCount": 0, "freeChildAges": [], "mostPopularRateplan": false, "flexiCancelAddOn": {}, "flexiCancelDetails": {}, "trainExclusiveRateAvailable": false, "busExclusiveRateAvailable": false, "exclusiveFlyerRateAvailable": false, "vccEnabled": false, "luckyRateAvailable": false, "packageRoomRatePlan": false}, "1126443535870370092": {"type": "RatePlan", "mealPlans": [{"startDate": null, "endDate": null, "code": "EP", "value": "Room Only"}], "availDetails": {"status": "B", "count": 1, "numOfRooms": 1, "occupancyDetails": {"adult": 3, "child": 0, "infant": 0, "numOfRooms": 1, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "paymentDetails": {"paymentMode": "PAS", "originalAPPayMode": "PAS"}, "cancelPenaltyList": [{"cancelRules": [{"text": "<b>This booking is non-refundable and the tariff cannot be cancelled with zero fee.</b>"}], "penaltyDescription": {"name": "NON_REFUNDABLE", "description": "This tariff cannot be cancelled with zero fee. Any cancellations will be subject to a hotel fee as follows:From 2024-08-01 20:29:27 (destination time) till 2024-11-27 14:59:58 (destination time) - 100% of booking amount.After 2024-11-27 14:59:59 (destination time) - 100% of booking amount.Cancellations are only allowed before CheckIn."}, "mostRestrictive": "N"}], "displayFare": {"tax": {"value": 0}, "slashedPrice": {"value": 4781.6, "maxFraction": 0, "avgeragePriceNoTax": 4781.6, "sellingPriceNoTax": 4781.6, "sellingPriceWithTax": 4781.6, "basePriceWoCommision": 0, "actualTax": 0, "basePriceWoCommisionWoHcp": 0}, "actualPrice": {"value": 6340.27, "maxFraction": 0, "avgeragePriceNoTax": 2690.81, "sellingPriceNoTax": 6340.27, "sellingPriceWithTax": 6340.27, "basePriceWoCommision": 4066.19, "actualTax": 846.35, "basePriceWoCommisionWoHcp": 0}, "bestCouponByPaymode": {"PAS": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 617 Off!", "discountAmount": 617, "doubleDiscountEnabled": true, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 617, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 617}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 8.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}}, "otherCouponByPaymode": {"PAS": [{"couponCode": "IDFCINTEMI", "type": "ECOUPON", "description": "Exclusive Offer - IDFC Credit Card EMI Users. Get INR 717 Off", "discountAmount": 717, "doubleDiscountEnabled": false, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 717, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 717}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": false, "autoApplicable": false, "ddApplied": false, "netMargin": 0, "configId": "Config:32977:2024-07-30T19:16:35", "configPriority": 2000, "configType": "FLAT_DISCOUNTER", "manthanDdMarkUp": 0, "subDescription": "IDFCINTEMI", "promoIconLink": "https://gos3.ibcdn.com/IDFC-**********.png", "bankOffer": true, "ctaBankText": "View More", "ctaBankUrl": "https://www.goibibo.com/offers/hotel-offers/", "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, {"couponCode": "THAILANDSPECIAL", "type": "ECOUPON", "description": "Get 196 Off!", "discountAmount": 196, "doubleDiscountEnabled": false, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 196, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 196}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": false, "ddApplied": false, "netMargin": 14.***************, "configId": "Config:63047:2024-08-01T11:10:13", "configPriority": 90, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}]}, "extraAdult": {"value": 0}, "totalTariff": {"grandTotal": 5164.11, "subTotal": 6340.27, "discount": 1558.67, "taxes": 0, "extraPax": 0, "ddmu": 0}, "segmentId": "1126", "ddmu": 0, "totalRoomCount": 1, "blackDiscount": 0, "taxDetails": {"hotelTax": 846.35, "markup": 0, "serviceTaxOnMarkup": 0, "taxExcluded": 0, "actualMarkup": 382.52, "tcsAmount": 0, "ddMarkUp": 0}, "couponReceived": true, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 4165, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 6340, "savingPerc": 34, "totalSaving": 2176, "roundedOffDelta": 0, "basePrice": 6340, "hotelTax": 846, "hotelServiceCharge": 0, "mmtServiceCharge": 383, "mmtDiscount": 1559, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 617, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 617 Off!", "discountAmount": 617, "doubleDiscountEnabled": true, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 617}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 8.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n+VH1Zz3U8qIrnN8kg8Dau+uReLqPUJqE9QohLBPMyIlImgRAGT69EhNYynCOPQfv0+8iqi7nNaYIf/U3uGJAx791sYIRwinTIylEAMbq9QDlYG1E16Sw28vxOJT9OoFVxrqjDb30S+ZTlH3OeJ8JEu3yfI+zWyRpIiTcju0Z4Ybj4RH0vfdQsMbOil638y3s1R4NOQcu9wHOZ3oAo4T3Gwf0fxFJIVuXe6XG3Ozl8ZcBSbg8rBNYEkfuehbsikqDScvJjKEBlVr7GDVfoB/OBR3l56RiM1Nox1wrzoJfkRxLqTOdbqhMrbqE2NzbFKgV4oT4qLMTDLCQ==", "pricingDivisor": 1, "totalTax": 1229, "effectivePrice": 4165, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 4165, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, "displayPriceBreakDownList": [{"displayPrice": 4165, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 6340, "savingPerc": 34, "totalSaving": 2176, "roundedOffDelta": 0, "basePrice": 6340, "hotelTax": 846, "hotelServiceCharge": 0, "mmtServiceCharge": 383, "mmtDiscount": 1559, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 617, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 617 Off!", "discountAmount": 617, "doubleDiscountEnabled": true, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 617}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 8.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n+VH1Zz3U8qIrnN8kg8Dau+uReLqPUJqE9QohLBPMyIlImgRAGT69EhNYynCOPQfv0+8iqi7nNaYIf/U3uGJAx791sYIRwinTIylEAMbq9QDlYG1E16Sw28vxOJT9OoFVxrqjDb30S+ZTlH3OeJ8JEu3yfI+zWyRpIiTcju0Z4Ybj4RH0vfdQsMbOil638y3s1R4NOQcu9wHOZ3oAo4T3Gwf0fxFJIVuXe6XG3Ozl8ZcBSbg8rBNYEkfuehbsikqDScvJjKEBlVr7GDVfoB/OBR3l56RiM1Nox1wrzoJfkRxLqTOdbqhMrbqE2NzbFKgV4oT4qLMTDLCQ==", "pricingDivisor": 1, "totalTax": 1229, "effectivePrice": 4165, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 4165, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, {"displayPrice": 4065, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 6340, "savingPerc": 36, "totalSaving": 2276, "roundedOffDelta": 0, "basePrice": 6340, "hotelTax": 846, "hotelServiceCharge": 0, "mmtServiceCharge": 383, "mmtDiscount": 1559, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 717, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "IDFCINTEMI", "type": "ECOUPON", "description": "Exclusive Offer - IDFC Credit Card EMI Users. Get INR 717 Off", "discountAmount": 717, "doubleDiscountEnabled": false, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 717}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": false, "autoApplicable": false, "ddApplied": false, "netMargin": 0, "configId": "Config:32977:2024-07-30T19:16:35", "configPriority": 2000, "configType": "FLAT_DISCOUNTER", "manthanDdMarkUp": 0, "subDescription": "IDFCINTEMI", "promoIconLink": "https://gos3.ibcdn.com/IDFC-**********.png", "bankOffer": true, "ctaBankText": "View More", "ctaBankUrl": "https://www.goibibo.com/offers/hotel-offers/", "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n+/ZDwCARvmooS2/KcXNVTY+NobkTVqprZA8CHztxrHXOCOz9ajLtgxDimZxWON/zTkbw3s25I6WcNbN2AZUyOaSkkZfoWUd3ODBRmIHUQTm75uEirJcqkHH4uGid2gGr1BkXFQoihPhefnGqeMsovQ3vSJS+Ip/j2FPpqyzm2pkoWAiV++qo6Q/teB/PJSgIvwNFBbgIcX2fvUgk0DRlLWdd2AE9Z606pqzPTAER0wZXAjQW6tFxN+LmKhi7d/RKXtTKDS90/oKhUQ2Dwic+aEuKwuHyb/Iw/Mj2yFLHKZZF2yN2336H1sD9ub50EsmeQ=", "pricingDivisor": 1, "totalTax": 1229, "effectivePrice": 4065, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 4065, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, {"displayPrice": 4586, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 6340, "savingPerc": 28, "totalSaving": 1755, "roundedOffDelta": 0, "basePrice": 6340, "hotelTax": 846, "hotelServiceCharge": 0, "mmtServiceCharge": 383, "mmtDiscount": 1559, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 196, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "THAILANDSPECIAL", "type": "ECOUPON", "description": "Get 196 Off!", "discountAmount": 196, "doubleDiscountEnabled": false, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 196}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": false, "ddApplied": false, "netMargin": 14.***************, "configId": "Config:63047:2024-08-01T11:10:13", "configPriority": 90, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n+XTBq3LJcWBx3OP+tUc4qIKU19zw0ChAfpIJxLypuKk9f07MmGhC591UKzxa3r6AEQscfel5nD6vMLAEqtT+6CHp83GfAUmubA6HZRfTSfgbyEKgA3AVXvuo6jIBRkuBWafKxmiURUJWOfY2sWesxRMQ30q+09BTfe/9imQk+F80N39Zw4gxZs3xDj4hAo85+jVvUY2Lmglsk1l25f2iusv24Ig/d948oG0KphME1+qbTci8NYpNiTFo7erVYA9UmuhWZbdV05YDHhrSwrnBKKMfQRyJePxevfPWL1QVqmZBgczt1YWb/KBt6/t44Y90+k9rDDs96mww==", "pricingDivisor": 1, "totalTax": 1229, "effectivePrice": 4586, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 4586, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}], "commission": 715.41, "conversionFactor": 0.4244, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "slotRate": false, "hotelTax": 359.19, "hotelierServiceCharge": 0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "offersDataMap": {"offerDataMap": [{"key": "5149106440", "offers_OfferData": {"moderatedText": null, "amount": 661.5, "offerType": 3, "offerAmount": 0, "offerMessage": null, "offerIcon": null}}]}}, "supplierDetails": {"supplierCode": "INGO", "costPrice": 2029.31, "hotelierCurrencyCode": "THB", "dmcRatePlanCode": "990579404404", "accessCode": ""}, "roomTariff": [{"perNightPrice": 6340.27, "extraPrice": 0, "taxes": 0, "totalPricePerRoom": 6340.27, "numberOfAdults": 3, "numberOfChildren": 0, "roomDiscountPerNight": 1558.67, "roomDiscountTax": 0, "extraChildFarePerNight": 0, "startDate": "2024-11-27", "endDate": "2024-11-28", "roomNumber": "1", "hotelierServiceCharge": 0}], "ratePlanType": "", "mtKey": "1126443535870370092", "ratePlanDesc": "", "checkCancellationPolicy": false, "checkInclusions": false, "segmentId": "1126", "inclusionAndPolicyAvailVar": false, "suppressPas": false, "ratePlanCode": "1126443535870370092", "mealUpgradeRatePlan": false, "suppressed": false, "bnplExtended": false, "pahx": false, "rpcc": "990579404404", "lowest": false, "groupModifiedRatePlanCode": "", "netRate": false, "droolDiscountApplied": true, "instantConfirmation": "", "guarantee": false, "forkedRatePlan": false, "corpRate": false, "bnplApplicable": false, "corporateCdfSkip": false, "staycationDeal": false, "campaingText": "", "allInclusiveRate": false, "vendorRoomCode": "45000432038", "lowestRateLastMinuteDeal": false, "anyRatelastMinuteDeal": false, "lowestRateEarlyBirdDeal": false, "anyRateEarlybirdDeal": false, "rpBookingModel": "", "freeChildCount": 0, "freeChildAges": [], "mostPopularRateplan": false, "flexiCancelAddOn": {}, "flexiCancelDetails": {}, "trainExclusiveRateAvailable": false, "busExclusiveRateAvailable": false, "exclusiveFlyerRateAvailable": false, "vccEnabled": false, "luckyRateAvailable": false, "packageRoomRatePlan": false}, "1335497165002847681": {"type": "RatePlan", "mealPlans": [{"startDate": null, "endDate": null, "code": "EP", "value": "Room Only"}], "availDetails": {"status": "B", "count": 1, "numOfRooms": 1, "occupancyDetails": {"adult": 3, "child": 0, "infant": 0, "numOfRooms": 1, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "paymentDetails": {"paymentMode": "PAS", "originalAPPayMode": "PAS"}, "cancelPenaltyList": [{"cancellationType": "FREE_CANCELLATON", "cancelRules": [{"text": "<b>Free Cancellation(100% refund) if you cancel this booking before ‪28 Oct, 2:59 PM‬</b>"}, {"text": "<b>Cancellations post that will be subject to a fee as follows</b>", "descText": [{"dateText": "Before ‪28 Oct, 2:59 PM‬", "feeText": "0.0% of booking amount"}, {"dateText": "‪28 Oct, 3:00 PM‬ to ‪20 Nov, 2:59 PM‬", "feeText": "Booking amount for 1.0 nights"}, {"dateText": "After ‪20 Nov, 3:00 PM‬", "feeText": "100.0% of booking amount"}, {"dateText": "After ‪27 Nov, 3:00 PM‬", "feeText": "100.0% of booking amount"}]}, {"text": "Cancellations are only allowed before the Check-In Time. All time mentioned above is in Destination Time."}], "penaltyDescription": {"name": "FREE_CANCELLATION", "description": "Free Cancellation (100% refund) if you cancel this booking before 2024-10-28 14:59:59 (destination time). Cancellations post that will be subject to a hotel fee as follows:From 2024-10-28 15:00:00 (destination time) till 2024-11-20 14:59:59 (destination time) - booking amount for 1 night(s).From 2024-11-20 15:00:00 (destination time) till 2024-11-27 14:59:59 (destination time) - 100% of booking amount.After 2024-11-27 15:00:00 (destination time) - 100% of booking amount.Cancellations are only allowed before CheckIn."}, "tillDate": "2024-10-28 14:59:59", "freeCancellationText": "Free Cancellation before ‪28 Oct 02:59 PM‬", "mostRestrictive": "N"}], "displayFare": {"tax": {"value": 0}, "slashedPrice": {"value": 4879.69, "maxFraction": 0, "avgeragePriceNoTax": 4879.69, "sellingPriceNoTax": 4879.69, "sellingPriceWithTax": 4879.69, "basePriceWoCommision": 0, "actualTax": 0, "basePriceWoCommisionWoHcp": 0}, "actualPrice": {"value": 6322.9, "maxFraction": 0, "avgeragePriceNoTax": 2683.44, "sellingPriceNoTax": 6322.9, "sellingPriceWithTax": 6322.9, "basePriceWoCommision": 4149.6, "actualTax": 863.71, "basePriceWoCommisionWoHcp": 0}, "bestCouponByPaymode": {"PAS": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 629 Off!", "discountAmount": 629, "doubleDiscountEnabled": true, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 629, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 629}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 7.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}}, "otherCouponByPaymode": {"PAS": [{"couponCode": "IDFCINTEMI", "type": "ECOUPON", "description": "Exclusive Offer - IDFC Credit Card EMI Users. Get INR 731 Off", "discountAmount": 731, "doubleDiscountEnabled": false, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 731, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 731}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": false, "autoApplicable": false, "ddApplied": false, "netMargin": 0, "configId": "Config:32977:2024-07-30T19:16:35", "configPriority": 2000, "configType": "FLAT_DISCOUNTER", "manthanDdMarkUp": 0, "subDescription": "IDFCINTEMI", "promoIconLink": "https://gos3.ibcdn.com/IDFC-**********.png", "bankOffer": true, "ctaBankText": "View More", "ctaBankUrl": "https://www.goibibo.com/offers/hotel-offers/", "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, {"couponCode": "THAILANDSPECIAL", "type": "ECOUPON", "description": "Get 200 Off!", "discountAmount": 200, "doubleDiscountEnabled": false, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 200, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 200}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": false, "ddApplied": false, "netMargin": 15.***************, "configId": "Config:63047:2024-08-01T11:10:13", "configPriority": 90, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}]}, "extraAdult": {"value": 0}, "totalTariff": {"grandTotal": 5270.08, "subTotal": 6322.9, "discount": 1443.21, "taxes": 0, "extraPax": 0, "ddmu": 0}, "segmentId": "1126", "ddmu": 0, "totalRoomCount": 1, "blackDiscount": 0, "taxDetails": {"hotelTax": 863.71, "markup": 0, "serviceTaxOnMarkup": 0, "taxExcluded": 0, "actualMarkup": 390.39, "tcsAmount": 0, "ddMarkUp": 0}, "couponReceived": true, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 4251, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 6323, "savingPerc": 33, "totalSaving": 2072, "roundedOffDelta": 0, "basePrice": 6323, "hotelTax": 864, "hotelServiceCharge": 0, "mmtServiceCharge": 390, "mmtDiscount": 1443, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 629, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 629 Off!", "discountAmount": 629, "doubleDiscountEnabled": true, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 629}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 7.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n++gWAJnSROrhS67k2j10ha0rPkraDB4hOKCfplCTweicnU5woVFg+CErrZNsy6DP9Oh0dd/SmoWSqHg7zYsI9IQzXdiCoNZorCclGjRieY5S3XWQDhMVeVLrMwG9ADBa3k/BP4Aswh94CGzZYBhhJiv4S5W8FrA7cm4jMI9GJ7m1WMSWBKGjq8WV1Ef3TkYeUn5nFQ2H/3COWCQcwF9DrHVEpT9kmjwnK3A4bqap8F4k4KkJ0+aDRwVw79+uPODTxqWYER0GfoIe7Tue0JRsIOuiuYymPtcRsh/OT9cvYNSpjRS8Zj7TmT++0GKNM6Hk/dLq/g7AnozQ==", "pricingDivisor": 1, "totalTax": 1254, "effectivePrice": 4251, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 4251, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, "displayPriceBreakDownList": [{"displayPrice": 4251, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 6323, "savingPerc": 33, "totalSaving": 2072, "roundedOffDelta": 0, "basePrice": 6323, "hotelTax": 864, "hotelServiceCharge": 0, "mmtServiceCharge": 390, "mmtDiscount": 1443, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 629, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 629 Off!", "discountAmount": 629, "doubleDiscountEnabled": true, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 629}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 7.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n++gWAJnSROrhS67k2j10ha0rPkraDB4hOKCfplCTweicnU5woVFg+CErrZNsy6DP9Oh0dd/SmoWSqHg7zYsI9IQzXdiCoNZorCclGjRieY5S3XWQDhMVeVLrMwG9ADBa3k/BP4Aswh94CGzZYBhhJiv4S5W8FrA7cm4jMI9GJ7m1WMSWBKGjq8WV1Ef3TkYeUn5nFQ2H/3COWCQcwF9DrHVEpT9kmjwnK3A4bqap8F4k4KkJ0+aDRwVw79+uPODTxqWYER0GfoIe7Tue0JRsIOuiuYymPtcRsh/OT9cvYNSpjRS8Zj7TmT++0GKNM6Hk/dLq/g7AnozQ==", "pricingDivisor": 1, "totalTax": 1254, "effectivePrice": 4251, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 4251, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, {"displayPrice": 4149, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 6323, "savingPerc": 34, "totalSaving": 2174, "roundedOffDelta": 0, "basePrice": 6323, "hotelTax": 864, "hotelServiceCharge": 0, "mmtServiceCharge": 390, "mmtDiscount": 1443, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 731, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "IDFCINTEMI", "type": "ECOUPON", "description": "Exclusive Offer - IDFC Credit Card EMI Users. Get INR 731 Off", "discountAmount": 731, "doubleDiscountEnabled": false, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 731}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": false, "autoApplicable": false, "ddApplied": false, "netMargin": 0, "configId": "Config:32977:2024-07-30T19:16:35", "configPriority": 2000, "configType": "FLAT_DISCOUNTER", "manthanDdMarkUp": 0, "subDescription": "IDFCINTEMI", "promoIconLink": "https://gos3.ibcdn.com/IDFC-**********.png", "bankOffer": true, "ctaBankText": "View More", "ctaBankUrl": "https://www.goibibo.com/offers/hotel-offers/", "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n8JCwWOF303F/rz6aD1O1xeSQYffmuRUOE2TS5CZ4hPJx/eDMeDiVLDTGt2Wh8Q3QuyZIDERyrqJOn5v8JEQGE5tO76BBzLSbvwfSjm2fI4MYnQRkigvwYrxht9MbLIsz+ejImseB74gn2LhYeisuXIPZCAxpKuVX39K6g6S/jPnUCO42k+5aF4t9KabfAwnhK5y6xAepUA/trgQCnfN80Jb5T80syubdFeQLiAVkeXkezZhwPlmv6rX9i3873MaWLJAblKvETWbqlSjipFfmOPadY8CqOTt0+SlET7wlCx+eS2TbantDfba8DfwgX1FFY=", "pricingDivisor": 1, "totalTax": 1254, "effectivePrice": 4149, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 4149, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, {"displayPrice": 4680, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 6323, "savingPerc": 26, "totalSaving": 1643, "roundedOffDelta": 0, "basePrice": 6323, "hotelTax": 864, "hotelServiceCharge": 0, "mmtServiceCharge": 390, "mmtDiscount": 1443, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 200, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "THAILANDSPECIAL", "type": "ECOUPON", "description": "Get 200 Off!", "discountAmount": 200, "doubleDiscountEnabled": false, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 200}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": false, "ddApplied": false, "netMargin": 15.***************, "configId": "Config:63047:2024-08-01T11:10:13", "configPriority": 90, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n9rce13IarmuXea9GVX6oHdt7ZW7Re8hJ11G1jKdQmC2FjA7LqdLJG+qRu7j758/4fzRJfsDzEWJuQR44SDr2gQPI5+eKuAEFr0sH1W4b/AL6eFKpmQyWEn33Hktyp53Sat4O0OLXKYstL43ZJj2NXiOf4MRd3veRSBSWZLMkOE09+kYVacSp18D8uwDeNvMg/wDyQlv2b42qPEiJjXZy6Ay2C3GPzfIqixNu+EstVqi/sWGqWZ4sV19KFw54XZ4BqTt1j01g56W/f7hTsDOb35RXQKgeQPrFfrS4YBL7nNmMGo/RPL3DXfFcLHEsBASlE=", "pricingDivisor": 1, "totalTax": 1254, "effectivePrice": 4680, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 4680, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}], "commission": 730.09, "conversionFactor": 0.4244, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "slotRate": false, "hotelTax": 366.56, "hotelierServiceCharge": 0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "offersDataMap": {"offerDataMap": [{"key": "5149106437", "offers_OfferData": {"moderatedText": null, "amount": 612.5, "offerType": 3, "offerAmount": 0, "offerMessage": null, "offerIcon": null}}]}}, "supplierDetails": {"supplierCode": "INGO", "costPrice": 2070.94, "hotelierCurrencyCode": "THB", "dmcRatePlanCode": "990579404404", "accessCode": ""}, "roomTariff": [{"perNightPrice": 6322.9, "extraPrice": 0, "taxes": 0, "totalPricePerRoom": 6322.9, "numberOfAdults": 3, "numberOfChildren": 0, "roomDiscountPerNight": 1443.21, "roomDiscountTax": 0, "extraChildFarePerNight": 0, "startDate": "2024-11-27", "endDate": "2024-11-28", "roomNumber": "1", "hotelierServiceCharge": 0}], "ratePlanType": "", "mtKey": "1335497165002847681", "ratePlanDesc": "", "checkCancellationPolicy": false, "checkInclusions": false, "segmentId": "1126", "inclusionAndPolicyAvailVar": false, "suppressPas": false, "ratePlanCode": "1335497165002847681", "mealUpgradeRatePlan": false, "suppressed": false, "bnplExtended": false, "pahx": false, "rpcc": "990579404404", "lowest": false, "groupModifiedRatePlanCode": "", "netRate": false, "droolDiscountApplied": true, "instantConfirmation": "", "guarantee": false, "forkedRatePlan": false, "corpRate": false, "cancellationTimeline": {"checkInDate": "27 Nov", "checkInDateTime": "3 PM", "cancellationDate": "28 Oct", "cancellationDateTime": "02:59 PM", "cancellationDateInDateFormat": "28-Oct-2024 14:59", "subTitle": "Free Cancellation", "freeCancellationText": "Free Cancellation till ‪28 Oct 02:59 PM‬", "title": "STAY FLEXIBLE WITH", "bookingDate": "02 Aug", "freeCancellationBenefits": [{"type": "", "text": "Free Cancellation till 28 Oct, 2:59 PM"}, {"type": "", "text": "No refund if cancelled after 28 Oct, 3:00 PM"}, {"type": "", "text": "No refund if cancelled after 20 Nov, 3:00 PM"}], "fcTextForPersuasion": "Free Cancellation before ‪28 Oct 02:59 PM‬", "cancellationPolicyTimelineList": [{"startDate": "01 Aug", "startDateTime": "08:29 PM", "endDate": "28 Oct", "endDateTime": "02:59 PM", "text": "100% Refund", "fcBenefit": {"type": "", "text": "Free Cancellation till 28 Oct, 2:59 PM"}, "refundable": true, "refundText": "100%", "type": "FULL_REFUND"}, {"startDate": "28 Oct", "startDateTime": "03:00 PM", "endDate": "20 Nov", "endDateTime": "02:59 PM", "text": "Non Refundable", "fcBenefit": {"type": "", "text": "No refund if cancelled after 28 Oct, 3:00 PM"}, "refundable": false, "refundText": "0%", "type": "NO_REFUND"}, {"startDate": "20 Nov", "startDateTime": "03:00 PM", "endDate": "27 Nov", "endDateTime": "02:59 PM", "text": "Non Refundable", "fcBenefit": {"type": "", "text": "No refund if cancelled after 20 Nov, 3:00 PM"}, "refundable": false, "refundText": "0%", "type": "NO_REFUND"}], "tillDate": "28-Oct-2024 14:59", "cardChargeTextTitle": "", "cardChargeTextMsg": "", "bnplTitleText": ""}, "bnplApplicable": false, "corporateCdfSkip": false, "staycationDeal": false, "campaingText": "", "allInclusiveRate": false, "vendorRoomCode": "45000432038", "lowestRateLastMinuteDeal": false, "anyRatelastMinuteDeal": false, "lowestRateEarlyBirdDeal": false, "anyRateEarlybirdDeal": false, "rpBookingModel": "", "freeChildCount": 0, "freeChildAges": [], "mostPopularRateplan": false, "flexiCancelAddOn": {}, "flexiCancelDetails": {}, "trainExclusiveRateAvailable": false, "busExclusiveRateAvailable": false, "exclusiveFlyerRateAvailable": false, "vccEnabled": false, "luckyRateAvailable": false, "packageRoomRatePlan": false}, "8669545540682270395": {"type": "RatePlan", "mealPlans": [{"startDate": null, "endDate": null, "code": "CP", "value": "Breakfast"}], "availDetails": {"status": "B", "count": 1, "numOfRooms": 1, "occupancyDetails": {"adult": 3, "child": 0, "infant": 0, "numOfRooms": 1, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "paymentDetails": {"paymentMode": "PAS", "originalAPPayMode": "PAS"}, "cancelPenaltyList": [{"cancelRules": [{"text": "<b>This booking is non-refundable and the tariff cannot be cancelled with zero fee.</b>"}], "penaltyDescription": {"name": "NON_REFUNDABLE", "description": "This tariff cannot be cancelled with zero fee. Any cancellations will be subject to a hotel fee as follows:From 2024-08-01 20:29:27 (destination time) till 2024-11-27 14:59:58 (destination time) - 100% of booking amount.After 2024-11-27 14:59:59 (destination time) - 100% of booking amount.Cancellations are only allowed before CheckIn."}, "mostRestrictive": "N"}], "displayFare": {"tax": {"value": 0}, "slashedPrice": {"value": 5073.89, "maxFraction": 0, "avgeragePriceNoTax": 5073.89, "sellingPriceNoTax": 5073.89, "sellingPriceWithTax": 5073.89, "basePriceWoCommision": 0, "actualTax": 0, "basePriceWoCommisionWoHcp": 0}, "actualPrice": {"value": 6759.8, "maxFraction": 0, "avgeragePriceNoTax": 2868.86, "sellingPriceNoTax": 6759.8, "sellingPriceWithTax": 6759.8, "basePriceWoCommision": 4314.75, "actualTax": 898.07, "basePriceWoCommisionWoHcp": 0}, "bestCouponByPaymode": {"PAS": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 654 Off!", "discountAmount": 654, "doubleDiscountEnabled": true, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 654, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 654}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 8.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}}, "otherCouponByPaymode": {"PAS": [{"couponCode": "IDFCINTEMI", "type": "ECOUPON", "description": "Exclusive Offer - IDFC Credit Card EMI Users. Get INR 761 Off", "discountAmount": 761, "doubleDiscountEnabled": false, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 761, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 761}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": false, "autoApplicable": false, "ddApplied": false, "netMargin": 0, "configId": "Config:32977:2024-07-30T19:16:35", "configPriority": 2000, "configType": "FLAT_DISCOUNTER", "manthanDdMarkUp": 0, "subDescription": "IDFCINTEMI", "promoIconLink": "https://gos3.ibcdn.com/IDFC-**********.png", "bankOffer": true, "ctaBankText": "View More", "ctaBankUrl": "https://www.goibibo.com/offers/hotel-offers/", "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, {"couponCode": "THAILANDSPECIAL", "type": "ECOUPON", "description": "Get 208 Off!", "discountAmount": 208, "doubleDiscountEnabled": false, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 208, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 208}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": false, "ddApplied": false, "netMargin": 14.***************, "configId": "Config:63047:2024-08-01T11:10:13", "configPriority": 90, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}]}, "extraAdult": {"value": 0}, "totalTariff": {"grandTotal": 5479.81, "subTotal": 6759.8, "discount": 1685.91, "taxes": 0, "extraPax": 0, "ddmu": 0}, "segmentId": "1126", "ddmu": 0, "totalRoomCount": 1, "blackDiscount": 0, "taxDetails": {"hotelTax": 898.07, "markup": 0, "serviceTaxOnMarkup": 0, "taxExcluded": 0, "actualMarkup": 405.91, "tcsAmount": 0, "ddMarkUp": 0}, "couponReceived": true, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 4420, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 6760, "savingPerc": 35, "totalSaving": 2340, "roundedOffDelta": 0, "basePrice": 6760, "hotelTax": 898, "hotelServiceCharge": 0, "mmtServiceCharge": 406, "mmtDiscount": 1686, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 654, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 654 Off!", "discountAmount": 654, "doubleDiscountEnabled": true, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 654}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 8.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n+wduqkZo05dmr02J5y4nAnuIfR6GUzZMLwlqsQcARdQur6qItqE2f70MMACGWP1u5AA5wPHnoHahwBKAXrC+e/UHwJ8MnTGpR3Nw1+Yd4YY3a52V0JdT8VbhAtHjvYm9d8ge3B063uKxviUAb1LhdBkGl467qtDovP5XaHRWD0ZlY2TxOkiRvrryv0lMxDOzhUCDoT9mdvguAoVWc8kYEBPwFLITEehmZvyhlAIGlKCqae2L59zVqA6k6M3o/KYYB0d9OAFAwe5fmqwdIMXCZa09tUb0qv00/XffKcN3a7ddRiJzMtqgBmUAeU9ePawQVnnKkOLU/h4g==", "pricingDivisor": 1, "totalTax": 1304, "effectivePrice": 4420, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 4420, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, "displayPriceBreakDownList": [{"displayPrice": 4420, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 6760, "savingPerc": 35, "totalSaving": 2340, "roundedOffDelta": 0, "basePrice": 6760, "hotelTax": 898, "hotelServiceCharge": 0, "mmtServiceCharge": 406, "mmtDiscount": 1686, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 654, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 654 Off!", "discountAmount": 654, "doubleDiscountEnabled": true, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 654}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 8.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n+wduqkZo05dmr02J5y4nAnuIfR6GUzZMLwlqsQcARdQur6qItqE2f70MMACGWP1u5AA5wPHnoHahwBKAXrC+e/UHwJ8MnTGpR3Nw1+Yd4YY3a52V0JdT8VbhAtHjvYm9d8ge3B063uKxviUAb1LhdBkGl467qtDovP5XaHRWD0ZlY2TxOkiRvrryv0lMxDOzhUCDoT9mdvguAoVWc8kYEBPwFLITEehmZvyhlAIGlKCqae2L59zVqA6k6M3o/KYYB0d9OAFAwe5fmqwdIMXCZa09tUb0qv00/XffKcN3a7ddRiJzMtqgBmUAeU9ePawQVnnKkOLU/h4g==", "pricingDivisor": 1, "totalTax": 1304, "effectivePrice": 4420, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 4420, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, {"displayPrice": 4313, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 6760, "savingPerc": 36, "totalSaving": 2447, "roundedOffDelta": 0, "basePrice": 6760, "hotelTax": 898, "hotelServiceCharge": 0, "mmtServiceCharge": 406, "mmtDiscount": 1686, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 761, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "IDFCINTEMI", "type": "ECOUPON", "description": "Exclusive Offer - IDFC Credit Card EMI Users. Get INR 761 Off", "discountAmount": 761, "doubleDiscountEnabled": false, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 761}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": false, "autoApplicable": false, "ddApplied": false, "netMargin": 0, "configId": "Config:32977:2024-07-30T19:16:35", "configPriority": 2000, "configType": "FLAT_DISCOUNTER", "manthanDdMarkUp": 0, "subDescription": "IDFCINTEMI", "promoIconLink": "https://gos3.ibcdn.com/IDFC-**********.png", "bankOffer": true, "ctaBankText": "View More", "ctaBankUrl": "https://www.goibibo.com/offers/hotel-offers/", "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n+e+y+TYrSGm1O+WqESN/FlQsaTky5pvv2+r7LaUbemPRqLkx6s4HMe7pB6jW6g5WN02M2ZvS45z6aNY1CUl0Zv4Ph3BmFBPHXHazdxPN6tsp8b7UpPnFF5ErHaiLw0mWSql3xuKp2DRzFODYtHUDT2lk1YrQOAj73DvDwq9WDyH7kmO1DaWZro2UNHihnjs/SHszdXnRepOTIvfLN/Tij4YgBOeDAFuDG/T5cdXU9mTxb5I8x8PEs+mkCzhgLNW8Ia4Bt3M886bflZVqkyxXkikjr9gfkdjJ0nvGyl1qWCvUdDNAjG57YC+wX5Td76nUY=", "pricingDivisor": 1, "totalTax": 1304, "effectivePrice": 4313, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 4313, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, {"displayPrice": 4866, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 6760, "savingPerc": 28, "totalSaving": 1894, "roundedOffDelta": 0, "basePrice": 6760, "hotelTax": 898, "hotelServiceCharge": 0, "mmtServiceCharge": 406, "mmtDiscount": 1686, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 208, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "THAILANDSPECIAL", "type": "ECOUPON", "description": "Get 208 Off!", "discountAmount": 208, "doubleDiscountEnabled": false, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 208}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": false, "ddApplied": false, "netMargin": 14.***************, "configId": "Config:63047:2024-08-01T11:10:13", "configPriority": 90, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n84hkBM3gBnNq7ps7rjGnLg3x3vrueQo1bJ8aYoj+R14GBsXFnsAVXvI8bi22gXavzVoktgdGIqGQi8hNugXWyhWc2tlY8ur9gnRYdxsOLvCTHPF8hxzCi7hxu45lGX0jU8wLCqrHRvrzfrylTruxlAuaNyvPj0nd0sSZ4WHI1gmgxvKlfwS+6Ycws7A13xrqqQvWHHb0/J4BN2yk0wHHPxmFno0oWakSUjvTMRFj0h8IfjRgCpCzPBpPVP8gqwBYxui6Qidxu8UNLW01dKQnTtBh6QN8CJBy8bQ8N5LIkXtk+DhE4Zj3Nw2UwR3VEUlaw7vQ/OBlpXHA==", "pricingDivisor": 1, "totalTax": 1304, "effectivePrice": 4866, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 4866, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}], "commission": 759.14, "conversionFactor": 0.4244, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "slotRate": false, "hotelTax": 381.14, "hotelierServiceCharge": 0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "offersDataMap": {"offerDataMap": [{"key": "5149106440", "offers_OfferData": {"moderatedText": null, "amount": 715.5, "offerType": 3, "offerAmount": 0, "offerMessage": null, "offerIcon": null}}]}}, "supplierDetails": {"supplierCode": "INGO", "costPrice": 2153.36, "hotelierCurrencyCode": "THB", "dmcRatePlanCode": "990000776177", "accessCode": ""}, "roomTariff": [{"perNightPrice": 6759.8, "extraPrice": 0, "taxes": 0, "totalPricePerRoom": 6759.8, "numberOfAdults": 3, "numberOfChildren": 0, "roomDiscountPerNight": 1685.91, "roomDiscountTax": 0, "extraChildFarePerNight": 0, "startDate": "2024-11-27", "endDate": "2024-11-28", "roomNumber": "1", "hotelierServiceCharge": 0}], "ratePlanType": "", "mtKey": "8669545540682270395", "ratePlanDesc": "", "checkCancellationPolicy": false, "checkInclusions": false, "segmentId": "1126", "inclusionAndPolicyAvailVar": false, "suppressPas": false, "ratePlanCode": "8669545540682270395", "mealUpgradeRatePlan": true, "suppressed": false, "bnplExtended": false, "pahx": false, "rpcc": "990000776177", "lowest": false, "groupModifiedRatePlanCode": "", "netRate": false, "droolDiscountApplied": true, "instantConfirmation": "", "guarantee": false, "forkedRatePlan": false, "corpRate": false, "bnplApplicable": false, "corporateCdfSkip": false, "staycationDeal": false, "campaingText": "", "allInclusiveRate": false, "vendorRoomCode": "45000432038", "lowestRateLastMinuteDeal": false, "anyRatelastMinuteDeal": false, "lowestRateEarlyBirdDeal": false, "anyRateEarlybirdDeal": false, "rpBookingModel": "", "freeChildCount": 0, "freeChildAges": [], "mostPopularRateplan": false, "flexiCancelAddOn": {}, "flexiCancelDetails": {}, "trainExclusiveRateAvailable": false, "busExclusiveRateAvailable": false, "exclusiveFlyerRateAvailable": false, "vccEnabled": false, "luckyRateAvailable": false, "packageRoomRatePlan": false}, "-2382577773800493073": {"type": "RatePlan", "mealPlans": [{"startDate": null, "endDate": null, "code": "CP", "value": "Breakfast"}], "availDetails": {"status": "B", "count": 1, "numOfRooms": 1, "occupancyDetails": {"adult": 3, "child": 0, "infant": 0, "numOfRooms": 1, "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0}}, "paymentDetails": {"paymentMode": "PAS", "originalAPPayMode": "PAS"}, "cancelPenaltyList": [{"cancellationType": "FREE_CANCELLATON", "cancelRules": [{"text": "<b>Free Cancellation(100% refund) if you cancel this booking before ‪28 Oct, 2:59 PM‬</b>"}, {"text": "<b>Cancellations post that will be subject to a fee as follows</b>", "descText": [{"dateText": "Before ‪28 Oct, 2:59 PM‬", "feeText": "0.0% of booking amount"}, {"dateText": "‪28 Oct, 3:00 PM‬ to ‪20 Nov, 2:59 PM‬", "feeText": "Booking amount for 1.0 nights"}, {"dateText": "After ‪20 Nov, 3:00 PM‬", "feeText": "100.0% of booking amount"}, {"dateText": "After ‪27 Nov, 3:00 PM‬", "feeText": "100.0% of booking amount"}]}, {"text": "Cancellations are only allowed before the Check-In Time. All time mentioned above is in Destination Time."}], "penaltyDescription": {"name": "FREE_CANCELLATION", "description": "Free Cancellation (100% refund) if you cancel this booking before 2024-10-28 14:59:59 (destination time). Cancellations post that will be subject to a hotel fee as follows:From 2024-10-28 15:00:00 (destination time) till 2024-11-20 14:59:59 (destination time) - booking amount for 1 night(s).From 2024-11-20 15:00:00 (destination time) till 2024-11-27 14:59:59 (destination time) - 100% of booking amount.After 2024-11-27 15:00:00 (destination time) - 100% of booking amount.Cancellations are only allowed before CheckIn."}, "tillDate": "2024-10-28 14:59:59", "freeCancellationText": "Free Cancellation before ‪28 Oct 02:59 PM‬", "mostRestrictive": "N"}], "displayFare": {"tax": {"value": 0}, "slashedPrice": {"value": 5180, "maxFraction": 0, "avgeragePriceNoTax": 5180, "sellingPriceNoTax": 5180, "sellingPriceWithTax": 5180, "basePriceWoCommision": 0, "actualTax": 0, "basePriceWoCommisionWoHcp": 0}, "actualPrice": {"value": 6741.02, "maxFraction": 0, "avgeragePriceNoTax": 2860.89, "sellingPriceNoTax": 6741.02, "sellingPriceWithTax": 6741.02, "basePriceWoCommision": 4404.97, "actualTax": 916.85, "basePriceWoCommisionWoHcp": 0}, "bestCouponByPaymode": {"PAS": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 668 Off!", "discountAmount": 668, "doubleDiscountEnabled": true, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 668, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 668}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 8.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}}, "otherCouponByPaymode": {"PAS": [{"couponCode": "IDFCINTEMI", "type": "ECOUPON", "description": "Exclusive Offer - IDFC Credit Card EMI Users. Get INR 777 Off", "discountAmount": 777, "doubleDiscountEnabled": false, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 777, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 777}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": false, "autoApplicable": false, "ddApplied": false, "netMargin": 0, "configId": "Config:32977:2024-07-30T19:16:35", "configPriority": 2000, "configType": "FLAT_DISCOUNTER", "manthanDdMarkUp": 0, "subDescription": "IDFCINTEMI", "promoIconLink": "https://gos3.ibcdn.com/IDFC-**********.png", "bankOffer": true, "ctaBankText": "View More", "ctaBankUrl": "https://www.goibibo.com/offers/hotel-offers/", "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, {"couponCode": "THAILANDSPECIAL", "type": "ECOUPON", "description": "Get 212 Off!", "discountAmount": 212, "doubleDiscountEnabled": false, "couponDetails": [{"moreVerificationRequired": false, "doubleDiscountEnabled": false, "discountTypeCode": "INSTANT", "timeOfCredit": "Booking", "discountType": "Instant", "discount": 212, "cashbackAmount": 0, "cdfValidated": false, "ecoupon": false}], "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 212}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": false, "ddApplied": false, "netMargin": 15.**************, "configId": "Config:63047:2024-08-01T11:10:13", "configPriority": 90, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}]}, "extraAdult": {"value": 0}, "totalTariff": {"grandTotal": 5594.39, "subTotal": 6741.02, "discount": 1561.03, "taxes": 0, "extraPax": 0, "ddmu": 0}, "segmentId": "1126", "ddmu": 0, "totalRoomCount": 1, "blackDiscount": 0, "taxDetails": {"hotelTax": 916.85, "markup": 0, "serviceTaxOnMarkup": 0, "taxExcluded": 0, "actualMarkup": 414.4, "tcsAmount": 0, "ddMarkUp": 0}, "couponReceived": true, "couponSkipped": false, "ddApplied": false, "displayPriceBreakDown": {"displayPrice": 4512, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 6741, "savingPerc": 33, "totalSaving": 2229, "roundedOffDelta": 0, "basePrice": 6741, "hotelTax": 917, "hotelServiceCharge": 0, "mmtServiceCharge": 414, "mmtDiscount": 1561, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 668, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 668 Off!", "discountAmount": 668, "doubleDiscountEnabled": true, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 668}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 8.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n8To8ofl9x9MaEhYBqt0o9rbD6X5IztmCA+1J1mgbBRjHDal3CbgMYtq2Jq/DpKmlXWGP/tWcO/3zEjeCzd/Mp1FdfG25nvoP2CBg45vR0iRPO4+vJVErFS7hdjiUfjc/+CtH9YrrxIZhRpcfHty4PJMc4mr8ms6wVHhX8OMqaYkt6PvWSaDQdWRFDLeochdhtRr0ZrmYgMY1uUQSvMZvu+eGAGPhX8lZG79VITxMsRV5+s7rzXUuVKWuf4QNh0d0hgZJs4TtBNi8TCXAtrTwD3Z6iYUD4THdD/XfohMkLm+5lUufq4FJIZ8Aik1kMHIRdZjdtA5C5KFA==", "pricingDivisor": 1, "totalTax": 1331, "effectivePrice": 4512, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 4512, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, "displayPriceBreakDownList": [{"displayPrice": 4512, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 6741, "savingPerc": 33, "totalSaving": 2229, "roundedOffDelta": 0, "basePrice": 6741, "hotelTax": 917, "hotelServiceCharge": 0, "mmtServiceCharge": 414, "mmtDiscount": 1561, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 668, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "MMTINTERNATIONAL", "type": "ECOUPON", "description": "Offer For All Users: Get 668 Off!", "discountAmount": 668, "doubleDiscountEnabled": true, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 668}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": true, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": true, "ddApplied": false, "netMargin": 8.***************, "configId": "Config:1414:2024-08-01T11:09:59", "configPriority": 500, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n8To8ofl9x9MaEhYBqt0o9rbD6X5IztmCA+1J1mgbBRjHDal3CbgMYtq2Jq/DpKmlXWGP/tWcO/3zEjeCzd/Mp1FdfG25nvoP2CBg45vR0iRPO4+vJVErFS7hdjiUfjc/+CtH9YrrxIZhRpcfHty4PJMc4mr8ms6wVHhX8OMqaYkt6PvWSaDQdWRFDLeochdhtRr0ZrmYgMY1uUQSvMZvu+eGAGPhX8lZG79VITxMsRV5+s7rzXUuVKWuf4QNh0d0hgZJs4TtBNi8TCXAtrTwD3Z6iYUD4THdD/XfohMkLm+5lUufq4FJIZ8Aik1kMHIRdZjdtA5C5KFA==", "pricingDivisor": 1, "totalTax": 1331, "effectivePrice": 4512, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 4512, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, {"displayPrice": 4403, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 6741, "savingPerc": 35, "totalSaving": 2338, "roundedOffDelta": 0, "basePrice": 6741, "hotelTax": 917, "hotelServiceCharge": 0, "mmtServiceCharge": 414, "mmtDiscount": 1561, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 777, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "IDFCINTEMI", "type": "ECOUPON", "description": "Exclusive Offer - IDFC Credit Card EMI Users. Get INR 777 Off", "discountAmount": 777, "doubleDiscountEnabled": false, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 777}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": true, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": false, "autoApplicable": false, "ddApplied": false, "netMargin": 0, "configId": "Config:32977:2024-07-30T19:16:35", "configPriority": 2000, "configType": "FLAT_DISCOUNTER", "manthanDdMarkUp": 0, "subDescription": "IDFCINTEMI", "promoIconLink": "https://gos3.ibcdn.com/IDFC-**********.png", "bankOffer": true, "ctaBankText": "View More", "ctaBankUrl": "https://www.goibibo.com/offers/hotel-offers/", "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n+0sk76+SJ6rkLDwOa7OUbveMCdY33ZgCyVVnHCY6MlIxh66lHEnL5Vt9GRggipd45qR3rv16m5nBSw2an7myd2gExi/vN4PhkDBjXp/GPM7ypN4mLj45S+y1oAsaqWzs1AZOlwFpoU1OteaJYE9i6yScFMg9MYQteYqcwXytHcLKZILGOa8AQo6/uG93jiM69TZJ6EubIt2AHiHRmArCMrES4MAJAkE82/1QHyi/OP2Bi7qSNX5EtKd7nvItBQiuJEGt88Hl2ZOhlSwOaRh9mztYuUnxi1m+QgLLzDrrcrO+i1ow/0FjSJ+2d+p9NgwzQ=", "pricingDivisor": 1, "totalTax": 1331, "effectivePrice": 4403, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 4403, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}, {"displayPrice": 4968, "displayPriceAlternateCurrency": 0, "nonDiscountedPrice": 6741, "savingPerc": 26, "totalSaving": 1773, "roundedOffDelta": 0, "basePrice": 6741, "hotelTax": 917, "hotelServiceCharge": 0, "mmtServiceCharge": 414, "mmtDiscount": 1561, "blackDiscount": 0, "losBenefitsDiscount": 0, "cdfDiscount": 212, "charityAmountV2": 0, "wallet": 0, "effectiveDiscount": 0, "tdsAmount": 0, "extraPaxPrice": 0, "couponInfo": {"couponCode": "THAILANDSPECIAL", "type": "ECOUPON", "description": "Get 212 Off!", "discountAmount": 212, "doubleDiscountEnabled": false, "forexCashbackAmount": 0, "paymentModel": "PAS", "hybridDiscounts": {"INSTANT": 212}, "tncUrl": "", "blockPhoneNumber": false, "ddApplicable": false, "refundPolicy": "<PERSON><PERSON><PERSON>", "walletAmountApplicable": 0, "specialPromoCoupon": false, "showCouponExpiry": false, "brinUnlocked": false, "loyaltyOfferMessage": "", "bnplAllowed": true, "autoApplicable": false, "ddApplied": false, "netMargin": 15.**************, "configId": "Config:63047:2024-08-01T11:10:13", "configPriority": 90, "configType": "NET_MARGIN_DISCOUNTER", "manthanDdMarkUp": 0, "bankOffer": false, "policyBasedAdjustedAmount": 0, "incrementalDiscountAmount": 0, "extraDiscount": 0, "recommendedCard": false, "appliedHcpToDiscounts": true, "noCostEmiApplicable": false, "extraDiscountType": "NR", "hcpVariation": 0, "disabled": false}, "pricingKey": "s9rHC9RN7n9arbSenMJnD6f5GKAhlaKQnTjiSyi5yeqkhXjmbYJQEC+MC59Wt1w3nWpL8SisGdGCqvqGTLh1eeV7IJ3Bu89MrA5UoC89d0ZE0KoNyLaO1D93J/5pRhKdHPpnQ0YEfPfWCRzdF3uIlPvPd6mLdAUbFXaflczUdX70aqIUTcf3gzVpXnrcCwzfShYG40dBgKtpkkRu5oZ/4tIJBWpepu6woqGufe2RzEXgThk0yXOPh6LI7ypIrGRUF5NM4ST6ucNgWz+hNkg4kEB3CJG3A0mgKKGCW3xsObCRuAr+M6+90S0eYNx5caGmlH7ilNjXns6NGQ2P8QVzNg==", "pricingDivisor": 1, "totalTax": 1331, "effectivePrice": 4968, "brinInclusivePrice": 0, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "affiliateFee": 0, "pinCodeMandatory": false, "totalAmount": 4968, "metaDiscount": 0, "addonPrice": 0, "nonDiscountedAddonPrice": 0, "currencyConvFactorToINR": 1, "cbrAvailable": false, "serviceCharge": 0, "tcsAmount": 0, "flexiCancellationCharges": 0, "displayPriceWithCfar": 0, "taxIncluded": false, "ddmarkupAlreadyApplied": false, "charityV2Enable": false}], "commission": 775.02, "conversionFactor": 0.4244, "hotelierCouponDiscount": 0, "gstDiscountHotelierCoupon": 0, "slotRate": false, "hotelTax": 389.11, "hotelierServiceCharge": 0, "isBNPLApplicable": false, "originalBNPL": false, "dateOfDelayedPayment": 0, "apPromotionDiscount": 0, "aajKaBhaoApplied": "NA", "taxExcluded": false, "offersDataMap": {"offerDataMap": [{"key": "5149106437", "offers_OfferData": {"moderatedText": null, "amount": 662.5, "offerType": 3, "offerAmount": 0, "offerMessage": null, "offerIcon": null}}]}}, "supplierDetails": {"supplierCode": "INGO", "costPrice": 2198.39, "hotelierCurrencyCode": "THB", "dmcRatePlanCode": "990000776177", "accessCode": ""}, "roomTariff": [{"perNightPrice": 6741.02, "extraPrice": 0, "taxes": 0, "totalPricePerRoom": 6741.02, "numberOfAdults": 3, "numberOfChildren": 0, "roomDiscountPerNight": 1561.03, "roomDiscountTax": 0, "extraChildFarePerNight": 0, "startDate": "2024-11-27", "endDate": "2024-11-28", "roomNumber": "1", "hotelierServiceCharge": 0}], "ratePlanType": "", "mtKey": "-2382577773800493073", "ratePlanDesc": "", "checkCancellationPolicy": false, "checkInclusions": false, "segmentId": "1126", "inclusionAndPolicyAvailVar": false, "suppressPas": false, "ratePlanCode": "-2382577773800493073", "mealUpgradeRatePlan": true, "suppressed": false, "bnplExtended": false, "pahx": false, "rpcc": "990000776177", "lowest": false, "groupModifiedRatePlanCode": "", "netRate": false, "droolDiscountApplied": true, "instantConfirmation": "", "guarantee": false, "forkedRatePlan": false, "corpRate": false, "cancellationTimeline": {"checkInDate": "27 Nov", "checkInDateTime": "3 PM", "cancellationDate": "28 Oct", "cancellationDateTime": "02:59 PM", "cancellationDateInDateFormat": "28-Oct-2024 14:59", "subTitle": "Free Cancellation", "freeCancellationText": "Free Cancellation till ‪28 Oct 02:59 PM‬", "title": "STAY FLEXIBLE WITH", "bookingDate": "02 Aug", "freeCancellationBenefits": [{"type": "", "text": "Free Cancellation till 28 Oct, 2:59 PM"}, {"type": "", "text": "No refund if cancelled after 28 Oct, 3:00 PM"}, {"type": "", "text": "No refund if cancelled after 20 Nov, 3:00 PM"}], "fcTextForPersuasion": "Free Cancellation before ‪28 Oct 02:59 PM‬", "cancellationPolicyTimelineList": [{"startDate": "01 Aug", "startDateTime": "08:29 PM", "endDate": "28 Oct", "endDateTime": "02:59 PM", "text": "100% Refund", "fcBenefit": {"type": "", "text": "Free Cancellation till 28 Oct, 2:59 PM"}, "refundable": true, "refundText": "100%", "type": "FULL_REFUND"}, {"startDate": "28 Oct", "startDateTime": "03:00 PM", "endDate": "20 Nov", "endDateTime": "02:59 PM", "text": "Non Refundable", "fcBenefit": {"type": "", "text": "No refund if cancelled after 28 Oct, 3:00 PM"}, "refundable": false, "refundText": "0%", "type": "NO_REFUND"}, {"startDate": "20 Nov", "startDateTime": "03:00 PM", "endDate": "27 Nov", "endDateTime": "02:59 PM", "text": "Non Refundable", "fcBenefit": {"type": "", "text": "No refund if cancelled after 20 Nov, 3:00 PM"}, "refundable": false, "refundText": "0%", "type": "NO_REFUND"}], "tillDate": "28-Oct-2024 14:59", "cardChargeTextTitle": "", "cardChargeTextMsg": "", "bnplTitleText": ""}, "bnplApplicable": false, "corporateCdfSkip": false, "staycationDeal": false, "campaingText": "", "allInclusiveRate": false, "vendorRoomCode": "45000432038", "lowestRateLastMinuteDeal": false, "anyRatelastMinuteDeal": false, "lowestRateEarlyBirdDeal": false, "anyRateEarlybirdDeal": false, "rpBookingModel": "", "freeChildCount": 0, "freeChildAges": [], "mostPopularRateplan": false, "flexiCancelAddOn": {}, "flexiCancelDetails": {}, "trainExclusiveRateAvailable": false, "busExclusiveRateAvailable": false, "exclusiveFlyerRateAvailable": false, "vccEnabled": false, "luckyRateAvailable": false, "packageRoomRatePlan": false}}, "roomTypeName": "Executive Double", "roomTypeCode": "2779", "mealUpgradeRoom": false, "baseRoom": false}}, "bnplNewVariant": false, "bnplFinalPrice": 0, "staycationDeal": false, "freeChildCount": 0, "bnplExtraFees": 0, "flexiCancelAddOnRoomDetail": {}, "flexiCancelRoomDetail": {}, "priceDifferenceFromLowest": 0, "recommendationCommunication": "", "simulationString": "", "trackingText": ""}