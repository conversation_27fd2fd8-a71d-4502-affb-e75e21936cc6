{"roomInfo": {"roomCode": "193", "roomMmtId": "193", "roomName": "Luxury Cottage", "bedType": "Queen Bed", "extraBed": "1", "sellableType": "room", "extraBedType": "Sofa cum bed", "parentRoomCode": "", "roomSize": "", "roomSizeUnit": "", "bedRoomCount": 1, "maxGuestCount": 4, "bedCount": 1, "subRoomCount": 0, "maxAdultCount": 3, "maxChildCount": 2, "propertyCount": 0, "finalGuestCount": 0, "extraBedCount": 0, "roomFlags": {"livingRoomPresent": false, "allowExtraBed": false, "allowExtraCrib": false, "largerRoom": false, "masterRoom": false, "smokingRoom": false}, "spaces": [{"type": "PRIVATE", "descriptive": ["1 x Bathroom", "1 x Bedroom"], "spaces": [{"baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "name": "Luxury Cottage", "descriptionText": "", "spaceType": "bedroom", "spaceId": "238515", "media": [{"url": "//r1imghtlak.ibcdn.com/79e9ffacc47411ec980e0a58a9feac02.jpg", "category": "IMAGE"}], "sleepingDetails": {"minOccupancy": 4, "maxOccupancy": 4, "bedRoomCount": 1, "bedCount": 1, "extraBedCount": 1, "bedInfo": [{"type": "Queen Bed", "count": 1}], "extraBedInfo": [{"type": "Sofa cum bed", "count": 1}]}}]}, {"type": "SHARED", "descriptive": ["1 x ", "1 x Parking"], "displayItem": {"iconUrl": "https://promos.makemytrip.com/Hotels_product/Homestay/mmtHomestay.png", "text": "<font color='#CF8100'>This is a <b>Shared Space</b> and will be common to all guests at the property</font>"}, "spaces": [{"baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "name": "Parking 1", "descriptionText": "", "spaceType": "parking", "spaceId": "238587", "media": [{"url": "//r1imghtlak.ibcdn.com/7b9f3b5ac47411ec89ed0a58a9feac02.jpg", "category": "IMAGE"}, {"url": "//r1imghtlak.ibcdn.com/88ce73d2ecaa11ecad870a58a9feac02.jpg", "category": "IMAGE"}, {"url": "//r1imghtlak.ibcdn.com/dbd84e16ecac11ec8d360a58a9feac02.png", "category": "IMAGE"}]}, {"baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "name": "Swimming Pool 1", "descriptionText": "", "spaceType": "swimming pool", "spaceId": "238588", "media": [{"url": "//r1imghtlak.ibcdn.com/88643d32ecaa11ecb6e00a58a9feac02.jpg", "category": "IMAGE"}]}]}], "roomArrangementMap": {"ALTERNATE_BEDS": [], "BATHROOM": [], "EXTRA_BEDS": [{"type": "Sofa cum bed", "count": 1}], "BEDS": [{"type": "Queen Bed", "count": 1}]}, "amenities": [{"name": "Popular with Guests", "amenities": [{"name": "Daily Housekeeping", "type": "room", "categoryName": "Popular with Guests", "attributeName": "Housekeeping", "highlightedName": "Daily Housekeeping", "sequence": -49994, "childAttributes": [{"name": "Daily", "subAttributes": []}]}, {"name": "Room Service", "type": "room", "categoryName": "Popular with Guests", "attributeName": "Room service", "highlightedName": "Room Service", "sequence": 2343, "childAttributes": [{"name": "Limited duration", "subAttributes": []}]}]}, {"name": "Room Features", "amenities": [{"name": "Seating Area", "type": "room", "categoryName": "Room Features", "attributeName": "Seating Area", "highlightedName": "Seating Area", "sequence": -49943, "tags": ["Party Friendly"], "childAttributes": []}]}, {"name": "Beds and Blanket", "amenities": [{"name": "Woollen Blanket", "type": "room", "categoryName": "Beds and Blanket", "attributeName": "Blanket", "highlightedName": "Woollen Blanket", "sequence": 1380, "childAttributes": [{"name": "W<PERSON><PERSON>", "subAttributes": []}]}]}, {"name": "Safety and Security", "amenities": [{"name": "Cupboards with Locks", "type": "room", "categoryName": "Safety and Security", "attributeName": "Cupboards with locks", "highlightedName": "Cupboards with Locks", "sequence": 134, "childAttributes": []}]}, {"name": "Media and Entertainment", "amenities": [{"name": "TV", "type": "room", "categoryName": "Media and Entertainment", "attributeName": "TV", "displayType": "1", "highlightedName": "TV", "sequence": 603, "tags": ["Kids Friendly"], "childAttributes": [{"name": "Flat Screen", "subAttributes": []}, {"name": "LED", "subAttributes": []}, {"name": "Satelite channels", "subAttributes": []}]}]}, {"name": "Bathroom", "amenities": [{"name": "<PERSON><PERSON><PERSON>", "type": "room", "categoryName": "Bathroom", "attributeName": "<PERSON><PERSON><PERSON>", "highlightedName": "<PERSON><PERSON><PERSON>", "sequence": 61, "childAttributes": []}]}, {"name": "Other Facilities", "amenities": [{"name": "Balcony", "type": "room", "categoryName": "Other Facilities", "attributeName": "Balcony", "displayType": "1", "highlightedName": "Balcony", "sequence": -49939, "childAttributes": [{"name": "Private", "subAttributes": []}]}]}]}}