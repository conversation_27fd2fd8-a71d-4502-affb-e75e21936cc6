{"requestId": "f944d0f9-32c5-4d69-9c44-5d13747105ff", "journeyId": "2500CCAF-DE76-4BD4-A059-8EE1BBEBBD7A", "hotelDetails": {"id": "202104241701276878", "rooms": [{"name": "seawave", "code": "4416518", "ratePlans": [{"rateType": "", "code": "3352991442191417151", "rpcc": "************", "description": "", "segmentId": "1121", "vendorRoomCode": "45000832024", "reviewDeeplinkUrl": "https://www.makemytrip.com/hotels/hotel-review?hotelId=202104241701276878&checkin=06262025&checkout=06272025&country=IN&city=CTGOI&_uCurrency=INR&roomStayQualifier=2e0e&locusId=CTGOI&locusType=city&hotelName=Omiguel+CliffTop+Cottages+%7C+Rooms+%26+Wi-Fi&countryName=India&cityName=Goa&isEntireProperty=false&funnelName=HOMESTAY&roomCriteria=4416518~%7C~3352991442191417151~%7C~2e0e&searchType=E&mtKey=3352991442191417151&suppDetail=&payMode=PAS", "instantConfirmation": "", "inclusions": [{"id": "1339468071", "value": "Complimentary  Breakfast is available.", "code": "Free Breakfast", "imageURL": "", "tags": "", "category": "Breakfast", "tagList": [], "segmentIdentifier": "", "categoryId": "1", "type": "", "segmentId": "1120", "iconUrl": "https://promos.makemytrip.com/Hotels_product/Inclusions/Icons/Food&Beverage_Breakfast.png", "amount": "", "leafCategoryCode": "1", "inclusionType": ""}], "mealPlans": [{"code": "CP", "value": "Breakfast"}], "paymentMode": "PAS", "availDetail": {"count": 4, "numOfRooms": 1, "occupancyDetails": {"adult": 2, "child": 0, "freeChild": 0, "numberOfRooms": 1, "childAges": [], "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 1, "bedCount": 1, "amount": 0.0, "ratePlanCode": null, "pricingKey": null}, "status": "B"}, "cancellationPolicy": {"type": "", "penalties": [{"startDate": null, "endDate": "", "value": null, "type": "", "desc": "This is a Non-refundable and non-amendable tariff. Cancellations, or no-shows will be subject to a hotel fee equal to the 100% of booking amount.Cancellations are only allowed before CheckIn.", "freeCancellationText": "", "cancelRules": [{"text": "<b>This booking is non-refundable and the tariff cannot be cancelled with zero fee.</b>", "descText": []}]}], "cancellationTimeline": {"checkInDate": "", "checkInDateTime": "", "cancellationDate": "", "cancellationDateTime": "", "cancellationDateInDateFormat": "", "cardChargeDate": "", "cardChargeDateLong": "", "cardChargeDateTime": "", "dateFormat": "", "cardChargeText": "", "bookingAmountText": "", "subTitle": "", "freeCancellationText": "", "title": "", "bookingDate": "", "fcTextForPersuasion": "", "tillDate": "", "cardChargeTextTitle": "", "cardChargeTextMsg": "", "bnplTitleText": ""}, "amendmentPolicies": {"name": null, "metaData": null}}, "price": {"totalRoomCount": 1, "basePrice": 11000.0, "displayPrice": 7706.0, "totalTax": 2881.0, "totalDiscount": 3294.0, "applicableCoupons": [{"autoApplicable": true, "couponCode": "HOMESTAYS", "description": "Get  INR 2964  Off", "discount": 2964.0, "specialPromoCoupon": false, "type": "ECOUPON", "bankOffer": false}], "segmentId": "1121", "currencyConvertor": 1.0, "couponCode": "HOMESTAYS", "extraDiscount": {"discount": 0.0, "bookingCount": 0, "type": ""}, "pricingKey": "s9rHC9RN7n9bTnbKStjxtcNEEhW4pTCW8egRcDFzZgHHkR5TT/1W1OfC9W+QRIQan3v81CUckjh4MzBbygBxnTBWYEEdqSwQeCm0bXqe8CxWUi8yP3dElSbPDLcnDcsA+UpQN1ypJDVVEzGrChYThu0OhHCNcONvzdVOQYNq+sgnac8nlpSMHCoTrLx9fc5xEwIsQ42IIt2jEOT8CiDg2sPlXyE+NrlLjRyt3Ri+LdXvP0A2wszW/Tp7ZuoJmkC5VGsJmw2paM7VjZHZX4EZTh+i7AERv0JmME4hZ4a1Co3f9egd1uzEp8lHWY8A5qe5VuSVWrjv3jRMfmKF3lkEF0WN+yW1g0OQSy269ivMk44=", "pricePerOccupancy": [{"adult": 2, "child": 0, "freeChild": 0, "numberOfRooms": 0, "childAges": [], "maxAdultAtSamePrice": 0, "maxChildAtSamePrice": 0, "bedRoomCount": 0, "bedCount": 0, "amount": 11000.0, "ratePlanCode": null, "pricingKey": null}]}, "addOns": {"addOnPolicies": []}, "campaingText": "", "rtbEmail": "", "linkedRates": [], "ratePlanFlags": {"packageRatePlan": false, "checkInclusions": false, "checkCancellationPolicy": false, "lucky": false}, "trackingInfo": {"supplierCode": "INGOHS"}}], "type": "EXACT", "sellableType": "", "addOns": {"addOnPolicies": []}, "baseRoom": false, "staycationDeal": false, "desc": "", "trackingText": "", "roomInfo": {"roomCode": "4416518", "roomMmtId": "4416518", "roomName": "seawave", "roomViewName": "Sea View", "bedType": "King Bed", "sellableType": "room", "parentRoomCode": "", "roomSize": "100", "roomSizeUnit": "sq.ft", "bedRoomCount": 1, "maxGuestCount": 3, "bedCount": 1, "subRoomCount": 0, "maxAdultCount": 2, "maxChildCount": 1, "propertyCount": 0, "finalGuestCount": 0, "extraBedCount": 0, "roomFlags": {"livingRoomPresent": false, "allowExtraBed": false, "allowExtraCrib": false, "largerRoom": false, "masterRoom": false, "smokingRoom": false}, "spaces": [{"type": "PRIVATE", "descriptive": ["1 x Bathroom", "1 x Bedroom"], "spaces": [{"baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "name": "seawave", "descriptionText": "", "spaceType": "bedroom", "spaceId": "63609", "media": [{"url": "//r1imghtlak.ibcdn.com/61adf3d0a4ef11eb95c90242ac110002.jpg", "category": "IMAGE"}, {"url": "//r1imghtlak.ibcdn.com/fb60b2663f9911ecb53a0a58a9feac02.jpeg", "category": "IMAGE"}, {"url": "//r1imghtlak.ibcdn.com/86ddc67c951611ecb6490a58a9feac02.jpeg", "category": "IMAGE"}, {"url": "//r1imghtlak.ibcdn.com/a3798708951611ec935c0a58a9feac02.jpeg", "category": "IMAGE"}, {"url": "//r1imghtlak.ibcdn.com/61adf3d0a4ef11eb95c90242ac110002.jpg", "category": "IMAGE"}, {"url": "//r1imghtlak.ibcdn.com/623e358aa4ef11ebbd440242ac110004.jpg", "category": "IMAGE"}, {"url": "//r1imghtlak.ibcdn.com/86ddc67c951611ecb6490a58a9feac02.jpeg", "category": "IMAGE"}], "sleepingDetails": {"minOccupancy": 3, "maxOccupancy": 3, "bedRoomCount": 1, "bedCount": 1, "extraBedCount": 0, "bedInfo": [{"type": "King Bed", "count": 1}]}}]}, {"type": "SHARED", "descriptive": ["2 x ", "1 x Terrace", "1 x Living Room", "1 x Parking"], "displayItem": {"iconUrl": "https://promos.makemytrip.com/Hotels_product/Homestay/mmtHomestay.png", "text": "<font color='#CF8100'>This is a <b>Shared Space</b> and will be common to all guests at the property</font>"}, "spaces": [{"baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "name": "Living Room 1", "descriptionText": "", "spaceType": "living_room", "spaceId": "121903", "media": [{"url": "//r1imghtlak.ibcdn.com/86ddc67c951611ecb6490a58a9feac02.jpeg", "category": "IMAGE"}]}, {"baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "name": "Parking", "descriptionText": "", "spaceType": "parking", "spaceId": "90027", "media": [{"url": "//r1imghtlak.ibcdn.com/625f1af2a4ef11ebba4d0242ac110004.jpg", "category": "IMAGE"}, {"url": "//r1imghtlak.ibcdn.com/32a1e4a8bb0e11ec941d0a58a9feac02.png", "category": "IMAGE"}]}, {"baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "name": "Swimming Pool", "descriptionText": "", "spaceType": "swimming pool", "spaceId": "90028", "media": [{"url": "//r1imghtlak.ibcdn.com/c61e8112adb911eca4650a58a9feac02.jpg", "category": "IMAGE"}]}, {"baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "name": "Terrace 1", "descriptionText": "", "spaceType": "terrace", "spaceId": "121900", "media": [{"url": "//r1imghtlak.ibcdn.com/62ce0728a4ef11eb9aef0242ac110002.jpg", "category": "IMAGE"}]}, {"baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "name": "Dining Area", "descriptionText": "", "spaceType": "dining area", "spaceId": "806311", "media": [{"url": "//r1imghtlak.ibcdn.com/16d87af6d95b11eb9e6a0242ac110003.jpg", "category": "IMAGE"}]}]}], "roomArrangementMap": {"ALTERNATE_BEDS": [], "BATHROOM": [], "EXTRA_BEDS": [], "BEDS": [{"type": "King Bed", "count": 1}]}, "amenities": [{"name": "Popular with Guests", "amenities": [{"name": "Housekeeping", "type": "room", "categoryName": "Popular with Guests", "attributeName": "Housekeeping", "highlightedName": "Housekeeping", "sequence": -49944, "childAttributes": []}, {"name": "Air Conditioning", "type": "room", "categoryName": "Popular with Guests", "attributeName": "Air Conditioning", "highlightedName": "Air Conditioning", "sequence": -49941, "childAttributes": []}, {"name": "Wi-Fi", "type": "room", "categoryName": "Popular with Guests", "attributeName": "Wifi", "highlightedName": "Wi-Fi", "sequence": -49940, "childAttributes": []}, {"name": "Bathroom", "type": "room", "categoryName": "Popular with Guests", "attributeName": "Bathroom", "highlightedName": "Bathroom", "sequence": 118, "childAttributes": []}, {"name": "Mineral Water - additional charge", "type": "room", "categoryName": "Popular with Guests", "attributeName": "Mineral Water", "highlightedName": "Mineral Water - additional charge", "sequence": 323, "childAttributes": [{"name": "Paid", "subAttributes": []}]}]}, {"name": "Room Features", "amenities": [{"name": "Charging Points", "type": "room", "categoryName": "Room Features", "attributeName": "Charging points", "highlightedName": "Charging Points", "sequence": 5, "childAttributes": []}, {"name": "Hypoallergenic Bedding", "type": "room", "categoryName": "Room Features", "attributeName": "Hypoallergenic Bedding", "highlightedName": "Hypoallergenic Bedding", "sequence": 40, "childAttributes": []}]}, {"name": "Beds and Blanket", "amenities": [{"name": "Woollen Blanket", "type": "room", "categoryName": "Beds and Blanket", "attributeName": "Blanket", "highlightedName": "Woollen Blanket", "sequence": 1380, "childAttributes": [{"name": "W<PERSON><PERSON>", "subAttributes": []}]}]}, {"name": "Media and Entertainment", "amenities": [{"name": "TV", "type": "room", "categoryName": "Media and Entertainment", "attributeName": "TV", "highlightedName": "TV", "sequence": 239, "tags": ["Kids Friendly"], "childAttributes": []}]}, {"name": "Bathroom", "amenities": [{"name": "Toiletries", "type": "room", "categoryName": "Bathroom", "attributeName": "Toiletries", "highlightedName": "Toiletries", "sequence": 191, "childAttributes": []}, {"name": "Hot & Cold Water", "type": "room", "categoryName": "Bathroom", "attributeName": "Hot & Cold Water", "highlightedName": "Hot & Cold Water", "sequence": 240, "childAttributes": []}, {"name": "Toilet with grab rails", "type": "room", "categoryName": "Bathroom", "attributeName": "Toilet with grab rails", "highlightedName": "Toilet with grab rails", "sequence": 4826, "tags": ["Elderly Friendly"], "childAttributes": []}]}, {"name": "Other Facilities", "amenities": [{"name": "Balcony", "type": "room", "categoryName": "Other Facilities", "attributeName": "Balcony", "displayType": "1", "highlightedName": "Balcony", "sequence": -49960, "childAttributes": [{"name": "Private", "subAttributes": []}]}]}]}}], "roomCombos": [], "hotelRateFlags": {"soldOut": false, "maskedPrice": false, "groupBookingPrice": false, "negotiatedRateFlag": false, "newToCityCouponAvailable": false}, "bnplBaseAmount": 0.0, "propertyType": "Homestay", "starRating": 0, "stayTypeText": "Room in a Homestay", "sellableUnit": "room", "roomCount": 1, "additionalDetails": {"bnplBaseAmount": 0.0, "cabPrice": 0.0, "sellableCombo": 2}, "primaryOffer": {"desc": "Special discount available on this property. Book by 18th June to save BIG!", "iconUrl": "https://promos.makemytrip.com/Hotels_product/sale/oct24/limited_time_sale_gold_filter.png", "headingColor": "#FFFFFF"}, "mandatoryCharges": [], "bnplVariant": "BNPL_NOT_APPLICABLE", "walletDetail": {"walletSurge": {"persuasionText": "", "endTime": 0, "surge": false, "startTime": 0}}, "alternateDatePriceDetails": [], "trackingInfo": {"trackingText": "FLX_Property_F|FLX_User_T"}, "media": {"professionalMediaEntities": {"R": [{"url": "//r1imghtlak.ibcdn.com/a3798708951611ec935c0a58a9feac02.jpeg", "category": "R", "title": "Cottages", "sequencing": "0", "roomCode": "4416518"}, {"url": "//r1imghtlak.ibcdn.com/86ddc67c951611ecb6490a58a9feac02.jpeg", "category": "R", "title": "86ddc67c951611ecb6490a58a9feac02.jpeg", "sequencing": "1", "roomCode": "4416518"}, {"url": "//r1imghtlak.ibcdn.com/61adf3d0a4ef11eb95c90242ac110002.jpg", "category": "R", "title": "Room", "sequencing": "2", "roomCode": "4416518"}, {"url": "//r1imghtlak.ibcdn.com/fb60b2663f9911ecb53a0a58a9feac02.jpeg", "category": "R", "title": "Cottages night view", "sequencing": "3", "roomCode": "4416518"}], "H": null}, "panoramic360": {}}}}