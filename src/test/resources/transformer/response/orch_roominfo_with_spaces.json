{"roomInfo": {"roomCode": "4416518", "roomMmtId": "4416518", "roomName": "seawave", "roomViewName": "Sea View", "bedType": "King Bed", "sellableType": "room", "parentRoomCode": "", "roomSize": "100", "roomSizeUnit": "sq.ft", "bedRoomCount": 1, "maxGuestCount": 3, "bedCount": 1, "subRoomCount": 0, "maxAdultCount": 2, "maxChildCount": 1, "propertyCount": 0, "finalGuestCount": 0, "extraBedCount": 0, "roomFlags": {"livingRoomPresent": false, "allowExtraBed": false, "allowExtraCrib": false, "largerRoom": false, "masterRoom": false, "smokingRoom": false}, "spaces": [{"type": "PRIVATE", "descriptive": ["1 x Bathroom", "1 x Bedroom"], "spaces": [{"baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "name": "seawave", "descriptionText": "", "spaceType": "bedroom", "spaceId": "63609", "media": [{"url": "//r1imghtlak.ibcdn.com/61adf3d0a4ef11eb95c90242ac110002.jpg", "category": "IMAGE"}, {"url": "//r1imghtlak.ibcdn.com/fb60b2663f9911ecb53a0a58a9feac02.jpeg", "category": "IMAGE"}, {"url": "//r1imghtlak.ibcdn.com/86ddc67c951611ecb6490a58a9feac02.jpeg", "category": "IMAGE"}, {"url": "//r1imghtlak.ibcdn.com/a3798708951611ec935c0a58a9feac02.jpeg", "category": "IMAGE"}, {"url": "//r1imghtlak.ibcdn.com/61adf3d0a4ef11eb95c90242ac110002.jpg", "category": "IMAGE"}, {"url": "//r1imghtlak.ibcdn.com/623e358aa4ef11ebbd440242ac110004.jpg", "category": "IMAGE"}, {"url": "//r1imghtlak.ibcdn.com/86ddc67c951611ecb6490a58a9feac02.jpeg", "category": "IMAGE"}], "sleepingDetails": {"minOccupancy": 3, "maxOccupancy": 3, "bedRoomCount": 1, "bedCount": 1, "extraBedCount": 0, "bedInfo": [{"type": "King Bed", "count": 1}]}}]}, {"type": "SHARED", "descriptive": ["2 x ", "1 x Terrace", "1 x Living Room", "1 x Parking"], "displayItem": {"iconUrl": "https://promos.makemytrip.com/Hotels_product/Homestay/mmtHomestay.png", "text": "<font color='#CF8100'>This is a <b>Shared Space</b> and will be common to all guests at the property</font>"}, "spaces": [{"baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "name": "Living Room 1", "descriptionText": "", "spaceType": "living_room", "spaceId": "121903", "media": [{"url": "//r1imghtlak.ibcdn.com/86ddc67c951611ecb6490a58a9feac02.jpeg", "category": "IMAGE"}]}, {"baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "name": "Parking", "descriptionText": "", "spaceType": "parking", "spaceId": "90027", "media": [{"url": "//r1imghtlak.ibcdn.com/625f1af2a4ef11ebba4d0242ac110004.jpg", "category": "IMAGE"}, {"url": "//r1imghtlak.ibcdn.com/32a1e4a8bb0e11ec941d0a58a9feac02.png", "category": "IMAGE"}]}, {"baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "name": "Swimming Pool", "descriptionText": "", "spaceType": "swimming pool", "spaceId": "90028", "media": [{"url": "//r1imghtlak.ibcdn.com/c61e8112adb911eca4650a58a9feac02.jpg", "category": "IMAGE"}]}, {"baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "name": "Terrace 1", "descriptionText": "", "spaceType": "terrace", "spaceId": "121900", "media": [{"url": "//r1imghtlak.ibcdn.com/62ce0728a4ef11eb9aef0242ac110002.jpg", "category": "IMAGE"}]}, {"baseOccupancy": 0, "maxOccupancy": 0, "finalOccupancy": 0, "name": "Dining Area", "descriptionText": "", "spaceType": "dining area", "spaceId": "806311", "media": [{"url": "//r1imghtlak.ibcdn.com/16d87af6d95b11eb9e6a0242ac110003.jpg", "category": "IMAGE"}]}]}], "roomArrangementMap": {"ALTERNATE_BEDS": [], "BATHROOM": [], "EXTRA_BEDS": [], "BEDS": [{"type": "King Bed", "count": 1}]}, "amenities": [{"name": "Popular with Guests", "amenities": [{"name": "Housekeeping", "type": "room", "categoryName": "Popular with Guests", "attributeName": "Housekeeping", "highlightedName": "Housekeeping", "sequence": -49944, "childAttributes": []}, {"name": "Air Conditioning", "type": "room", "categoryName": "Popular with Guests", "attributeName": "Air Conditioning", "highlightedName": "Air Conditioning", "sequence": -49941, "childAttributes": []}, {"name": "Wi-Fi", "type": "room", "categoryName": "Popular with Guests", "attributeName": "Wifi", "highlightedName": "Wi-Fi", "sequence": -49940, "childAttributes": []}, {"name": "Bathroom", "type": "room", "categoryName": "Popular with Guests", "attributeName": "Bathroom", "highlightedName": "Bathroom", "sequence": 118, "childAttributes": []}, {"name": "Mineral Water - additional charge", "type": "room", "categoryName": "Popular with Guests", "attributeName": "Mineral Water", "highlightedName": "Mineral Water - additional charge", "sequence": 323, "childAttributes": [{"name": "Paid", "subAttributes": []}]}]}, {"name": "Room Features", "amenities": [{"name": "Charging Points", "type": "room", "categoryName": "Room Features", "attributeName": "Charging points", "highlightedName": "Charging Points", "sequence": 5, "childAttributes": []}, {"name": "Hypoallergenic Bedding", "type": "room", "categoryName": "Room Features", "attributeName": "Hypoallergenic Bedding", "highlightedName": "Hypoallergenic Bedding", "sequence": 40, "childAttributes": []}]}, {"name": "Beds and Blanket", "amenities": [{"name": "Woollen Blanket", "type": "room", "categoryName": "Beds and Blanket", "attributeName": "Blanket", "highlightedName": "Woollen Blanket", "sequence": 1380, "childAttributes": [{"name": "W<PERSON><PERSON>", "subAttributes": []}]}]}, {"name": "Media and Entertainment", "amenities": [{"name": "TV", "type": "room", "categoryName": "Media and Entertainment", "attributeName": "TV", "highlightedName": "TV", "sequence": 239, "tags": ["Kids Friendly"], "childAttributes": []}]}, {"name": "Bathroom", "amenities": [{"name": "Toiletries", "type": "room", "categoryName": "Bathroom", "attributeName": "Toiletries", "highlightedName": "Toiletries", "sequence": 191, "childAttributes": []}, {"name": "Hot & Cold Water", "type": "room", "categoryName": "Bathroom", "attributeName": "Hot & Cold Water", "highlightedName": "Hot & Cold Water", "sequence": 240, "childAttributes": []}, {"name": "Toilet with grab rails", "type": "room", "categoryName": "Bathroom", "attributeName": "Toilet with grab rails", "highlightedName": "Toilet with grab rails", "sequence": 4826, "tags": ["Elderly Friendly"], "childAttributes": []}]}, {"name": "Other Facilities", "amenities": [{"name": "Balcony", "type": "room", "categoryName": "Other Facilities", "attributeName": "Balcony", "displayType": "1", "highlightedName": "Balcony", "sequence": -49960, "childAttributes": [{"name": "Private", "subAttributes": []}]}]}]}}