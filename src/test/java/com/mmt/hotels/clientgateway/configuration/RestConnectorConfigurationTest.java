package com.mmt.hotels.clientgateway.configuration;

import com.mmt.hotels.clientgateway.enums.RestConfigurationEnums;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class RestConnectorConfigurationTest {

    @InjectMocks
    private RestConnectorConfiguration restConnectorConfiguration;

    @Before
    public void setUp() {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "searchHotelsConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "searchHotelsConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "searchHotelsSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "searchHotelsConnectionPoolSize", 100);

        ReflectionTestUtils.setField(restConnectorConfiguration, "availRoomsConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "availRoomsConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "availRoomsSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "availRoomsConnectionPoolSize", 100);

        ReflectionTestUtils.setField(restConnectorConfiguration, "staticDetailConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "staticDetailConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "staticDetailSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "staticDetailConnectionPoolSize", 100);

        ReflectionTestUtils.setField(restConnectorConfiguration, "updatedPriceConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "updatedPriceConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "updatedPriceSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "updatedPriceConnectionPoolSize", 100);

        ReflectionTestUtils.setField(restConnectorConfiguration, "pokusExperimentConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "pokusExperimentConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "pokusExperimentSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "pokusExperimentConnectionPoolSize", 100);

        ReflectionTestUtils.setField(restConnectorConfiguration, "userServiceConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "userServiceConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "userServiceSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "userServiceConnectionPoolSize", 100);
    }

    @Test
    public void testGetRequestConfig() throws Exception {
        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod(
                "getRequestConfig", int.class, int.class, int.class);
        method.setAccessible(true);

        // Call the method with test parameters
        RequestConfig config = (RequestConfig) method.invoke(restConnectorConfiguration, 5000, 5000, 30000);

        // Verify the config is not null and has expected values
        assertNotNull("RequestConfig should not be null", config);
        assertEquals("Connection request timeout should be 5000", 5000, config.getConnectionRequestTimeout());
        assertEquals("Connection timeout should be 5000", 5000, config.getConnectTimeout());
        assertEquals("Socket timeout should be 30000", 30000, config.getSocketTimeout());
    }

    @Test
    public void testGetRequestConfigWithDifferentValues() throws Exception {
        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod(
                "getRequestConfig", int.class, int.class, int.class);
        method.setAccessible(true);

        // Call the method with different test parameters
        RequestConfig config = (RequestConfig) method.invoke(restConnectorConfiguration, 3000, 4000, 20000);

        // Verify the config has the expected values
        assertEquals("Connection request timeout should be 3000", 3000, config.getConnectionRequestTimeout());
        assertEquals("Connection timeout should be 4000", 4000, config.getConnectTimeout());
        assertEquals("Socket timeout should be 20000", 20000, config.getSocketTimeout());
    }

    @Test
    public void testHttpClientWithConfiguration() throws Exception {
        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod(
                "getHttpClientWithConfiguration", int.class, int.class, int.class, int.class, int.class);
        method.setAccessible(true);

        // Call the method with test parameters
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration, 5000, 5000, 30000, 100, 200);

        // Verify the client is not null
        assertNotNull("HttpClient should not be null", client);
    }

    @Test
    public void testGetSearchHotelsClient() throws Exception {
        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getSearchHotelsClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("SearchHotelsClient should not be null", client);
    }

    @Test
    public void testGetAvailRoomsClient() throws Exception {
        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getAvailRoomsClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("AvailRoomsClient should not be null", client);
    }

    @Test
    public void testGetStaticDetailClient() throws Exception {
        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getStaticDetailClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("StaticDetailClient should not be null", client);
    }

    @Test
    public void testGetUpdatedPriceClient() throws Exception {
        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getUpdatedPriceClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("UpdatedPriceClient should not be null", client);
    }

    @Test
    public void testGetPokusClient() throws Exception {
        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getPokusClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("PokusClient should not be null", client);
    }

    @Test
    public void testGetUserServiceClient() throws Exception {
        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getUserServiceClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("UserServiceClient should not be null", client);
    }

    @Test
    public void testGetCardEngineClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "cardDataConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "cardDataConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "cardDataSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "cardDataConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getCardEngineClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("CardEngineClient should not be null", client);
    }

    @Test
    public void testGetTravelTipClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "travelTipConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "travelTipConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "travelTipSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "travelTipConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getTravelTipClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("TravelTipClient should not be null", client);
    }

    @Test
    public void testGetOrchSearchHotelsClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "listingFilterAppliedConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "searchHotelsConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "listingFilterAppliedSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "searchHotelsConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getOrchSearchHotelsClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("OrchSearchHotelsClient should not be null", client);
    }

    @Test
    public void testGetFetchUgcReviewsClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "ugcFetchReviewsConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "ugcFetchReviewsConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "ugcFetchReviewsSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "ugcFetchReviewsConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getFetchUgcReviewsClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("FetchUgcReviewsClient should not be null", client);
    }

    @Test
    public void testGetMobLandingClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "mobLandingConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "mobLandingConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "mobLandingSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "mobLandingConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getMobLandingClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("MobLandingClient should not be null", client);
    }

    @Test
    public void testGetSearchTreelsClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "searchTreelsConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "searchTreelsConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "searchTreelsSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "searchTreelsConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getSearchTreelsClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("SearchTreelsClient should not be null", client);
    }

    @Test
    public void testGetTreelsFilterClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "treelFilterConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "treelFilterConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "treelFilterSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "treelFilterConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getTreelsFilterClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("TreelsFilterClient should not be null", client);
    }

    @Test
    public void testGetFilterCountClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "filterCountConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "filterCountConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "filterCountSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "filterCountConnectionMinPoolSize", 50);
        ReflectionTestUtils.setField(restConnectorConfiguration, "filterCountConnectionMaxPoolSize", 100);
        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getFilterCountClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("FilterCountClient should not be null", client);
    }

    @Test
    public void testGetEvaluateFilterRankOrderClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "evaluateFilterRankConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "evaluateFilterRankConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "evaluateFilterRankSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "evaluateFilterRankConnectionMinPoolSize", 50);
        ReflectionTestUtils.setField(restConnectorConfiguration, "evaluateFilterRankConnectionMaxPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getEvaluateFilterRankOrderClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("EvaluateFilterRankOrderClient should not be null", client);
    }

    @Test
    public void testGetListingMapClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "listingMapConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "listingMapConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "listingMapSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "listingMapConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getListingMapClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("ListingMapClient should not be null", client);
    }

    @Test
    public void testGetAlternateDatesClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "alternateDatesConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "alternateDatesConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "alternateDatesSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "alternateDatesConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getAlternateDatesClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("AlternateDatesClient should not be null", client);
    }

    @Test
    public void testGetStaticRoomsDetailClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "staticRoomsDetailConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "staticRoomsDetailConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "staticRoomsDetailSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "staticRoomsDetailConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getStaticRoomsDetailClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("StaticRoomsDetailClient should not be null", client);
    }

    @Test
    public void testGetProgram18Client() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "getProgram18ConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "getProgram18ConnectionSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "getProgram18ConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getProgram18Client");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("Program18Client should not be null", client);
    }

    @Test
    public void testGetUgcSubmitAnswersClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "ugcSubmitAnswersConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "ugcSubmitAnswersConnectionSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "ugcSubmitAnswersConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getUgcSubmitAnswersClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("UgcSubmitAnswersClient should not be null", client);
    }

    @Test
    public void testGetUgcUploadImagesClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "ugcUploadImagesConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "ugcUploadImagesConnectionSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "ugcUploadImagesConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getUgcUploadImagesClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("UgcUploadImagesClient should not be null", client);
    }

    @Test
    public void testGetBookingDetailsClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "getBookingDetailsConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "getBookingDetailsConnectionSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "getBookingDetailsConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getBookingDetailsClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("BookingDetailsClient should not be null", client);
    }

    @Test
    public void testGetTxnDataClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "txnDataConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "txnDataConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "txnDataSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "txnDataConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getThankYouClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("TxnDataClient should not be null", client);
    }

    @Test
    public void testGetAffiliateClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "affiliateConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "affiliateConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "affiliateSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "affiliateConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getAffiliateConnector");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("AffiliateClient should not be null", client);
    }

    @Test
    public void testGetUpdatedPriceOccuLessClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "updatedPriceOccuLessConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "updatedPriceOccuLessConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "updatedPriceOccuLessSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "updatedPriceOccuLessConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getUpdatedPriceOccuLessClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("UpdatedPriceOccuLessClient should not be null", client);
    }

    @Test
    public void testGetUserFirstTimeStateClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "userFirstTimeStateConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "userFirstTimeStateConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "userFirstTimeStateSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "userFirstTimeStateConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getUserFirstTimeStateClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("UserFirstTimeStateClient should not be null", client);
    }

    @Test
    public void testGetPoliciesClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "policiesConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "policiesConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "policiesSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "policiesConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getPoliciesAPIConnector");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("PoliciesClient should not be null", client);
    }

    @Test
    public void testGetLtlngClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "ltlngConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "ltlngConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "ltlngSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "ltlngConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getLatLngPlacesAPIConnector");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("LtlngClient should not be null", client);
    }

    @Test
    public void testGetAddonsClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "addonsConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "addonsConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "addonsSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "addonsConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getAddonsConnector");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("AddonsClient should not be null", client);
    }

    @Test
    public void testGetRequestCallbackClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "requestCallbackConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "requestCallbackConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "requestCallbackSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "requestCallbackConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getRequestCallbackConnector");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("RequestCallbackClient should not be null", client);
    }

    @Test
    public void testGetWorkFlowByAuthCodeClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "workFlowByAuthCodeRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "workFlowByAuthCodeConnTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "workFlowByAuthCodeSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "workFlowByAuthCodePoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getWorkflowDataClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("WorkFlowByAuthCodeClient should not be null", client);
    }

    @Test
    public void testGetFetchLocationsClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "fetchLocationsConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "fetchLocationsConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "fetchLocationsSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "fetchLocationsConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getFetchLocationsClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("FetchLocationsClient should not be null", client);
    }

    @Test
    public void testGetOfferDetailsClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "offerDetailsConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "offerDetailsConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "offerDetailsSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "offerDetailsConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getOfferDetailsClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("OfferDetailsClient should not be null", client);
    }

    @Test
    public void testGetMyPartnerUserDetailsClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "myPartnerUserDetailsConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "myPartnerUserDetailsConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "myPartnerUserDetailsSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "myPartnerUserDetailsConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getMyPartnerUserDetailsClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("MyPartnerUserDetailsClient should not be null", client);
    }

    @Test
    public void testGetSaveGstDetailsClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "saveGstDetailsConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "saveGstDetailsConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "saveGstDetailsSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "saveGstDetailsConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getSaveGstDetailsClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("SaveGstDetailsClient should not be null", client);
    }

    @Test
    public void testBankOffersClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "mobLandingConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "mobLandingConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "mobLandingSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "mobLandingConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("bankOffersClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("BankOffersClient should not be null", client);
    }

    @Test
    public void testGetByPassConnector() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "byPassConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "byPassConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "byPassSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "byPassConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getByPassConnector");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("ByPassConnector should not be null", client);
    }

    @Test
    public void testGetEmiDetailConnector() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "emiDetailConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "emiDetailConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "emiDetailSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "emiDetailConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getEmiDetailConnector");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("EmiDetailConnector should not be null", client);
    }

    @Test
    public void testGetTotalPriceConnector() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "totalPriceConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "totalPriceConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "totalPriceSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "totalPriceConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getTotalPriceConnector");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("TotalPriceConnector should not be null", client);
    }

    @Test
    public void testGetLastBookedFlightClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "lastBookedFlightConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "lastBookedFlightConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "lastBookedFlightSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "lastBookedFlightConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getLastBookedFlightClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("LastBookedFlightClient should not be null", client);
    }

    @Test
    public void testGetStaticRoomsDetailPostClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "staticDetailConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "staticDetailConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "staticDetailSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "staticDetailConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getStaticRoomsDetailPostClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("StaticRoomsDetailPostClient should not be null", client);
    }

    @Test
    public void testGetThankYouClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "txnDataConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "txnDataConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "txnDataSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "txnDataConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getThankYouClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("ThankYouClient should not be null", client);
    }

    @Test
    public void testGetCrossSellSearchHotelsClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "crossSellSearchHotelsConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "crossSellSearchHotelsConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "crossSellSearchHotelsSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "searchHotelsConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getCrossSellSearchHotelsClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("CrossSellSearchHotelsClient should not be null", client);
    }

    @Test
    public void testGetSearchHotelsFilterAppliedClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "listingFilterAppliedConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "searchHotelsConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "listingFilterAppliedSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "searchHotelsConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getSearchHotelsFilterAppliedClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("SearchHotelsFilterAppliedClient should not be null", client);
    }

    @Test
    public void testGetPaymentCheckoutModBookingClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "paymentCheckoutModBookingConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "paymentCheckoutModBookingConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "paymentCheckoutModBookingSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "paymentCheckoutModBookingConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getPaymentCheckoutModBookingClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("PaymentCheckoutModBookingClient should not be null", client);
    }

    @Test
    public void testGetUpdateApprovalClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "updateApprovalRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "updateApprovalConnTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "updateApprovalSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "updateApprovalPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getUpdateApprovalClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("UpdateApprovalClient should not be null", client);
    }

    @Test
    public void testGetAvailableGuestHouseClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "availableGuestHouseRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "availableGuestHouseConnTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "availableGuestHouseSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "availableGuestHousePoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getAvailableGuestHouseClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("AvailableGuestHouseClient should not be null", client);
    }

    @Test
    public void testGetWorkFlowInfoAuthCodeClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "setApprovalRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "setApprovalConnTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "setApprovalSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "setApprovalPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getWorkFlowInfoAuthCodeClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("WorkFlowInfoAuthCodeClient should not be null", client);
    }

    @Test
    public void testGetApprovalsInfoClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "setApprovalRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "setApprovalConnTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "setApprovalSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "setApprovalPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getApprovalsInfoClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("ApprovalsInfoClient should not be null", client);
    }

    @Test
    public void testGetPayLaterHESClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "paylaterConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "paylaterConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "paylaterSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "paylaterConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getPayLaterHESClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("PayLaterHESClient should not be null", client);
    }

    @Test
    public void testGetCommonsWishListHotelsClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "commonsWishListHotelsConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "commonsWishListHotelsConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "commonsWishListHotelsSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "commonsWishListHotelsConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getCommonsWishListHotelsClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("CommonsWishListHotelsClient should not be null", client);
    }

    @Test
    public void testGetCommonsWishListReviewsClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "commonsWishListReviewsRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "commonsWishListReviewsConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "commonsWishListReviewsSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "commonsWishListReviewsConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getCommonsWishListReviewsClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("CommonsWishListReviewsClient should not be null", client);
    }

    @Test
    public void testGetCommonsWishListPlatformClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "platformWishListReviewsRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "platformWishListReviewsConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "platformWishListReviewsSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "platformWishListReviewsConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getCommonsWishListPlatformClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("CommonsWishListPlatformClient should not be null", client);
    }

    @Test
    public void testGetCalendarAvailabilityClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "calendarAvailabilityConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "calendarAvailabilityConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "calendarAvailabilitySocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "calendarAvailabilityConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getCalendarAvailabilityClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("CalendarAvailabilityClient should not be null", client);
    }

    @Test
    public void testGetLandingDiscoveryClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "landingDiscoveryConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "landingDiscoveryConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "landingDiscoverySocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "landingDiscoveryConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getLandingDiscoveryClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("LandingDiscoveryClient should not be null", client);
    }

    @Test
    public void testGetCityOverviewClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "cityOverviewyConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "cityOverviewConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "cityOverviewSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "cityOverviewConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getCityOverviewClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("CityOverviewClient should not be null", client);
    }

    @Test
    public void testGetWorkflowDataClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "workFlowByAuthCodeRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "workFlowByAuthCodeConnTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "workFlowByAuthCodeSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "workFlowByAuthCodePoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getWorkflowDataClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("WorkflowDataClient should not be null", client);
    }

    @Test
    public void testGetUpdatePolicyClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "updatePolicyConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "updatePolicyConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "updatePolicySocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "updatePolicyConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getUpdatePolicyClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("UpdatePolicyClient should not be null", client);
    }

    @Test
    public void testGetNearByHotelsAPIConnector() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "nearByConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "nearByConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "nearBySocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "nearByConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getNearByHotelsAPIConnector");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("NearByHotelsAPIConnector should not be null", client);
    }

    @Test
    public void testGetPolyglotClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "polyglotConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "polyglotConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "polyglotSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "polyglotConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getPolyglotClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("PolyglotClient should not be null", client);
    }

    @Test
    public void testGetAddonsConnector() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "addonsConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "addonsConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "addonsSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "addonsConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getAddonsConnector");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("AddonsConnector should not be null", client);
    }

    @Test
    public void testGetPaymentCheckoutClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "paymentCheckoutConnectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "paymentCheckoutConnectionTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "paymentCheckoutSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "paymentCheckoutConnectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getPaymentCheckoutClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("PaymentCheckoutClient should not be null", client);
    }

    @Test
    public void testGetFetchCollectionClient() throws Exception {
        // Set up the necessary fields with test values
        ReflectionTestUtils.setField(restConnectorConfiguration, "fetchCollectionRequestTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "fetchCollectionConnTimeout", 5000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "fetchCollectionSocketTimeout", 30000);
        ReflectionTestUtils.setField(restConnectorConfiguration, "fetchCollectionPoolSize", 100);

        // Use reflection to access private method
        Method method = RestConnectorConfiguration.class.getDeclaredMethod("getFetchCollectionClient");
        method.setAccessible(true);

        // Call the method
        HttpClient client = (HttpClient) method.invoke(restConnectorConfiguration);

        // Verify the client is not null
        assertNotNull("FetchCollectionClient should not be null", client);
    }
}
