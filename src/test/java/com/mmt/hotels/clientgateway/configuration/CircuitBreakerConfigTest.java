package com.mmt.hotels.clientgateway.configuration;

import com.mmt.hotels.clientgateway.util.CircuitBreakerMetricAspect;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for CircuitBreakerConfig
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class CircuitBreakerConfigTest {

    @InjectMocks
    private CircuitBreakerConfig circuitBreakerConfig;

    @Mock
    private CircuitBreakerMetricAspect circuitBreakerMetricAspect;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // Set up default property values using reflection
        ReflectionTestUtils.setField(circuitBreakerConfig, "poolName", "TEST");
        ReflectionTestUtils.setField(circuitBreakerConfig, "mobLandingFailureRateThreshold", 30.0f);
        ReflectionTestUtils.setField(circuitBreakerConfig, "mobLandingTimeoutDuration", "8s");
        ReflectionTestUtils.setField(circuitBreakerConfig, "mobLandingSlidingWindowSize", 100);
        ReflectionTestUtils.setField(circuitBreakerConfig, "mobLandingMinimumNumberOfCalls", 20);
        ReflectionTestUtils.setField(circuitBreakerConfig, "mobLandingWaitDurationInOpenState", "60s");
        ReflectionTestUtils.setField(circuitBreakerConfig, "mobLandingPermittedNumberOfCallsInHalfOpenState", 10);
        ReflectionTestUtils.setField(circuitBreakerConfig, "detailApiFailureRateThreshold", 25.0f);
        ReflectionTestUtils.setField(circuitBreakerConfig, "detailApiTimeoutDuration", "10s");
        ReflectionTestUtils.setField(circuitBreakerConfig, "listingApiFailureRateThreshold", 35.0f);
        ReflectionTestUtils.setField(circuitBreakerConfig, "listingApiTimeoutDuration", "12s");
        ReflectionTestUtils.setField(circuitBreakerConfig, "mobLandingCircuitBreakerEnabled", true);
        ReflectionTestUtils.setField(circuitBreakerConfig, "detailApiCircuitBreakerEnabled", false);
        ReflectionTestUtils.setField(circuitBreakerConfig, "listingApiCircuitBreakerEnabled", false);
        ReflectionTestUtils.setField(circuitBreakerConfig, "mobLandingFallbackCacheEnabled", true);
        ReflectionTestUtils.setField(circuitBreakerConfig, "mobLandingFallbackCacheTtl", 300000L);
    }

    @Test
    public void testCircuitBreakerRegistryCreation() {
        // When
        CircuitBreakerRegistry registry = circuitBreakerConfig.circuitBreakerRegistry();

        // Then
        assertNotNull(registry);
        
        // Verify mob-landing circuit breaker is registered (since it's enabled)
        CircuitBreaker mobLandingCB = registry.circuitBreaker("mob-landing");
        assertNotNull(mobLandingCB);
        assertEquals("mob-landing", mobLandingCB.getName());
        
        // Verify configuration values
        io.github.resilience4j.circuitbreaker.CircuitBreakerConfig config = mobLandingCB.getCircuitBreakerConfig();
        assertEquals(30.0f, config.getFailureRateThreshold(), 0.1f);
        assertEquals(100, config.getSlidingWindowSize());
        assertEquals(20, config.getMinimumNumberOfCalls());
        assertEquals(10, config.getPermittedNumberOfCallsInHalfOpenState());
    }

    @Test
    public void testMobLandingCircuitBreakerBeanCreation() {
        // Given
        CircuitBreakerRegistry registry = circuitBreakerConfig.circuitBreakerRegistry();

        // When
        CircuitBreaker circuitBreaker = circuitBreakerConfig.mobLandingCircuitBreaker(registry);

        // Then
        assertNotNull(circuitBreaker);
        assertEquals("mob-landing", circuitBreaker.getName());
        assertEquals(CircuitBreaker.State.CLOSED, circuitBreaker.getState());
    }

    @Test
    public void testMobLandingCircuitBreakerBeanWhenDisabled() {
        // Given
        ReflectionTestUtils.setField(circuitBreakerConfig, "mobLandingCircuitBreakerEnabled", false);
        CircuitBreakerRegistry registry = circuitBreakerConfig.circuitBreakerRegistry();

        // When
        CircuitBreaker circuitBreaker = circuitBreakerConfig.mobLandingCircuitBreaker(registry);

        // Then
        assertNull(circuitBreaker);
    }

    @Test
    public void testCircuitBreakerEventListeners() {
        // Given
        CircuitBreakerRegistry registry = circuitBreakerConfig.circuitBreakerRegistry();
        CircuitBreaker circuitBreaker = circuitBreakerConfig.mobLandingCircuitBreaker(registry);

        // When - Simulate state transition
        // Force circuit breaker to open state by simulating failures
        for (int i = 0; i < 25; i++) { // More than minimum calls (20) with 100% failure rate
            try {
                circuitBreaker.executeSupplier(() -> {
                    throw new RuntimeException("Test failure");
                });
            } catch (Exception e) {
                // Expected
            }
        }

        // Then
        // Circuit breaker should be in OPEN state due to high failure rate
        assertEquals(CircuitBreaker.State.OPEN, circuitBreaker.getState());
        
        // Verify metrics aspect was called (if available)
        if (circuitBreakerMetricAspect != null) {
            verify(circuitBreakerMetricAspect, atLeastOnce()).logCircuitBreakerStateChange(
                eq("mob-landing"), 
                eq(CircuitBreaker.State.CLOSED), 
                eq(CircuitBreaker.State.OPEN)
            );
            verify(circuitBreakerMetricAspect, atLeastOnce()).logCircuitBreakerFailureRateExceeded(
                eq("mob-landing"), 
                anyFloat()
            );
        }
    }

    @Test
    public void testGettersForConfigurationValues() {
        // Test getter methods
        assertTrue(circuitBreakerConfig.isMobLandingCircuitBreakerEnabled());
        //assertTrue(circuitBreakerConfig.isMobLandingFallbackCacheEnabled());
        //assertEquals(300000L, circuitBreakerConfig.getMobLandingFallbackCacheTtl());
    }

    @Test
    public void testCircuitBreakerConfigurationForDifferentPools() {
        // Test MAIN pool configuration
        ReflectionTestUtils.setField(circuitBreakerConfig, "poolName", "MAIN");
        ReflectionTestUtils.setField(circuitBreakerConfig, "mobLandingFailureRateThreshold", 30.0f);
        ReflectionTestUtils.setField(circuitBreakerConfig, "mobLandingTimeoutDuration", "8s");
        
        CircuitBreakerRegistry mainRegistry = circuitBreakerConfig.circuitBreakerRegistry();
        CircuitBreaker mainCB = mainRegistry.circuitBreaker("mob-landing");
        
        assertEquals(30.0f, mainCB.getCircuitBreakerConfig().getFailureRateThreshold(), 0.1f);
        
        // Test BOT pool configuration
        ReflectionTestUtils.setField(circuitBreakerConfig, "poolName", "BOT");
        ReflectionTestUtils.setField(circuitBreakerConfig, "mobLandingFailureRateThreshold", 50.0f);
        ReflectionTestUtils.setField(circuitBreakerConfig, "mobLandingTimeoutDuration", "12s");
        
        CircuitBreakerRegistry botRegistry = circuitBreakerConfig.circuitBreakerRegistry();
        CircuitBreaker botCB = botRegistry.circuitBreaker("mob-landing");
        
        assertEquals(50.0f, botCB.getCircuitBreakerConfig().getFailureRateThreshold(), 0.1f);
    }

    @Test
    public void testDetailApiCircuitBreakerConfiguration() {
        // Given - Enable detail API circuit breaker
        ReflectionTestUtils.setField(circuitBreakerConfig, "detailApiCircuitBreakerEnabled", true);

        // When
        CircuitBreakerRegistry registry = circuitBreakerConfig.circuitBreakerRegistry();

        // Then
        CircuitBreaker detailApiCB = registry.circuitBreaker("detail-api");
        assertNotNull(detailApiCB);
        assertEquals("detail-api", detailApiCB.getName());
        assertEquals(25.0f, detailApiCB.getCircuitBreakerConfig().getFailureRateThreshold(), 0.1f);
    }

    @Test
    public void testListingApiCircuitBreakerConfiguration() {
        // Given - Enable listing API circuit breaker
        ReflectionTestUtils.setField(circuitBreakerConfig, "listingApiCircuitBreakerEnabled", true);

        // When
        CircuitBreakerRegistry registry = circuitBreakerConfig.circuitBreakerRegistry();

        // Then
        CircuitBreaker listingApiCB = registry.circuitBreaker("listing-api");
        assertNotNull(listingApiCB);
        assertEquals("listing-api", listingApiCB.getName());
        assertEquals(35.0f, listingApiCB.getCircuitBreakerConfig().getFailureRateThreshold(), 0.1f);
    }

    @Test
    public void testCircuitBreakerWithoutMetricAspect() {
        // Given - No metric aspect
        ReflectionTestUtils.setField(circuitBreakerConfig, "circuitBreakerMetricAspect", null);

        // When
        CircuitBreakerRegistry registry = circuitBreakerConfig.circuitBreakerRegistry();
        CircuitBreaker circuitBreaker = circuitBreakerConfig.mobLandingCircuitBreaker(registry);

        // Then
        assertNotNull(circuitBreaker);
        // Should not throw any exceptions even without metric aspect
        assertEquals(CircuitBreaker.State.CLOSED, circuitBreaker.getState());
    }
}
