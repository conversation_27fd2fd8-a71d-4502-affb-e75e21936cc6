package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.enums.ConnectivityErrors;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.helpers.GstDetailsHelper;
import com.mmt.hotels.clientgateway.request.gstDetails.TravellerGstDetails;
import com.mmt.hotels.clientgateway.response.gstDetails.GstDetailsResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.AffiliateData;
import com.mmt.hotels.clientgateway.thirdparty.response.MyPartnerUserDetailsResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.TravellerGstInfo;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class MyPartnerCoreRestExecutorTest {

    @InjectMocks
    private MyPartnerCoreRestExecutor myPartnerCoreRestExecutor;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    @Mock
    private GstDetailsHelper gstDetailsHelper;

    @Before
    public void init() {
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", new ObjectMapper());

        String userDetailsUrl = "http://b2b-mypartner-core-orch.ecs.mmt/orch/v1/hotels/user-details?uuid=%s";
        ReflectionTestUtils.setField(myPartnerCoreRestExecutor, "userDetailsUrl", userDetailsUrl);

        String saveTravellergstUrl = "http://b2b-mypartner-core-orch.ecs.mmt/orch/v1/save-traveller-gst";
        ReflectionTestUtils.setField(myPartnerCoreRestExecutor, "saveTravellergstUrl", saveTravellergstUrl);
    }

    @Test
    public void testSaveGstDetails() throws ClientGatewayException {
        TravellerGstDetails travellerGstDetails = new TravellerGstDetails();
        GstDetailsResponse<TravellerGstDetails> expectedResponse = new GstDetailsResponse<>();

        Mockito.when(restConnectorUtil.performSaveGstDetailsPost(Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn("{}");

        GstDetailsResponse<TravellerGstDetails> actualResponse = myPartnerCoreRestExecutor.saveGstDetails(travellerGstDetails, new HashMap<>(), new HashMap<>(), "client");

        Assert.assertEquals(expectedResponse, actualResponse);
    }

    @Test(expected = ClientGatewayException.class)
    public void testSaveGstDetails_Exception() throws ClientGatewayException {
        TravellerGstDetails travellerGstDetails = new TravellerGstDetails();

        Mockito.when(restConnectorUtil.performSaveGstDetailsPost(Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenThrow(new RestConnectorException(DependencyLayer.CLIENTGATEWAY, ErrorType.CONNECTIVITY,
                        ConnectivityErrors.IO_ERROR.getErrorCode(), "Exception"));

        myPartnerCoreRestExecutor.saveGstDetails(travellerGstDetails, new HashMap<>(), new HashMap<>(), "client");
    }

    @Test
    public void testGetGstDetails_Error() throws ClientGatewayException {
        String uuid = "uuid";
        String errorMessage = "Gst details not received form MyPartner core API";
        MyPartnerUserDetailsResponse myPartnerUserDetailsResponse = new MyPartnerUserDetailsResponse();
        myPartnerUserDetailsResponse.setSuccess(false);
        myPartnerUserDetailsResponse.setError(errorMessage);

        Mockito.when(restConnectorUtil.performUserDetailsGet(Mockito.anyString(), Mockito.anyMap()))
                .thenReturn("{}");
        Mockito.when(gstDetailsHelper.isGstDetailsResponseValid(Mockito.any(MyPartnerUserDetailsResponse.class))
        ).thenReturn(false);

        GstDetailsResponse<List<TravellerGstDetails>> actualResponse = myPartnerCoreRestExecutor.getGstDetails(uuid, new HashMap<>(), new HashMap<>(), "client");

        Assert.assertFalse(actualResponse.getSuccess());
        Assert.assertEquals(errorMessage, actualResponse.getError());
    }

    @Test(expected = ClientGatewayException.class)
    public void testGetGstDetails_Exception() throws ClientGatewayException {
        String uuid = "uuid";

        Mockito.when(restConnectorUtil.performUserDetailsGet(Mockito.anyString(), Mockito.anyMap()))
                .thenThrow(new RestConnectorException(DependencyLayer.CLIENTGATEWAY, ErrorType.CONNECTIVITY,
                        ConnectivityErrors.IO_ERROR.getErrorCode(), "Exception"));

        myPartnerCoreRestExecutor.getGstDetails(uuid, new HashMap<>(), new HashMap<>(), "client");
    }

    @Test
    public void testGetGstDetails_SuccessWithData() throws ClientGatewayException {
        String uuid = "uuid";
        MyPartnerUserDetailsResponse myPartnerUserDetailsResponse = new MyPartnerUserDetailsResponse();
        myPartnerUserDetailsResponse.setSuccess(true);
        myPartnerUserDetailsResponse.setData(new AffiliateData());
        myPartnerUserDetailsResponse.getData().setTravellerGstInfo(new TravellerGstInfo());
        TravellerGstDetails travellerGstDetails = new TravellerGstDetails();
        myPartnerUserDetailsResponse.getData().getTravellerGstInfo().setGstDetails(Collections.singletonList(travellerGstDetails));

        Mockito.when(restConnectorUtil.performUserDetailsGet(Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(new Gson().toJson(myPartnerUserDetailsResponse));
        Mockito.when(gstDetailsHelper.isGstDetailsResponseValid(Mockito.any(MyPartnerUserDetailsResponse.class))
        ).thenReturn(true);

        GstDetailsResponse<List<TravellerGstDetails>> actualResponse = myPartnerCoreRestExecutor.getGstDetails(uuid, new HashMap<>(), new HashMap<>(), "client");

        Assert.assertTrue(actualResponse.getSuccess());
        Assert.assertNull(actualResponse.getError());
        Assert.assertNotNull(actualResponse.getData());
        Assert.assertEquals(1, actualResponse.getData().size());
        Assert.assertEquals(travellerGstDetails, actualResponse.getData().get(0));
    }
}