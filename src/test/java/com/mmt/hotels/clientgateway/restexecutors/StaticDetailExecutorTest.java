package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.request.PlatformUgcCategoryRequest;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.android.StaticDetailResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.util.HeadersUtil;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.request.CityGuideRequest;
import com.mmt.hotels.model.response.flyfish.UGCPlatformReviewSummaryDTO;
import com.mmt.hotels.model.response.flyfish.UgcSummaryRequest;
import com.mmt.hotels.model.response.flyfish.UserReviewResponseForListing;
import com.mmt.hotels.model.response.staticdata.CityGuideResponse;
import com.mmt.hotels.model.response.staticdata.StaffInfo;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.request.wishlist.FlyfishReviewRequestBody;
import com.mmt.hotels.pojo.request.wishlist.HotStoreHotelsRequestBody;
import com.mmt.hotels.pojo.response.detail.HotelDetailWrapperResponse;
import com.mmt.hotels.pojo.response.wishlist.FlyfishReviewWrapperResponse;
import com.mmt.hotels.pojo.response.wishlist.HotStoreHotelsWrapperResponse;
import com.mmt.propertymanager.config.PropertyManager;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.FileReader;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

@RunWith(MockitoJUnitRunner.class)
public class StaticDetailExecutorTest {

    @Mock
    private CommonConfigConsul commonConfigConsul;

    @Mock
    private PropertyManager propertyManager;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    private Logger logger;

    @InjectMocks
    private StaticDetailResponseTransformerAndroid transformer;

    @InjectMocks
    StaticDetailExecutor staticDetailExecutor;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    @Mock
    private MetricAspect metricAspect;

    @Spy
    private HeadersUtil headersUtil;

    @Mock
    private PricingEngineHelper pricingEngineHelper;


    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
        ReflectionTestUtils.setField(staticDetailExecutor, "staticDetailUrl", "abc");
        ReflectionTestUtils.setField(staticDetailExecutor, "ugcCategoryUrl", "v2.0/ugc/category?correlationKey=%s");
        ReflectionTestUtils.setField(staticDetailExecutor, "cityGuideUrl", "v2.0/city-guide?correlationKey=%s");
        ReflectionTestUtils.setField(staticDetailExecutor, "hotelsFromHotStoreUrl", "v2.0/hotels/hotstore?correlationKey=%s");
        ReflectionTestUtils.setField(staticDetailExecutor, "platformReviewSummaryUrl", "v2.0/platform/review-summary?correlationKey=%s");
        ReflectionTestUtils.setField(staticDetailExecutor, "hotelsReviewSummaryUrl", "v2.0/hotels/review-summary?correlationKey=%s");
        ReflectionTestUtils.setField(staticDetailExecutor, "ugcReviewUrl", "v2.0/ugc/reviews?correlationKey=%s");
    }

    @Test
    public void staticDetailTest() throws ClientGatewayException {


        String resp= null;

        try {


            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp =  jo.get("respStatic").toString();

        }catch (Exception e){
            logger.error("error occured in getting file", e.getMessage());

        }

        Mockito.when(restConnectorUtil.performStaticDetailPost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(resp);
        HotelDetailWrapperResponse response = staticDetailExecutor.getStaticDetail(new HotelDetailsMobRequestBody(), new HashMap<>(), new HashMap<String, String>());
        Assert.assertNotNull(response);

    }

    @Test
    public void testGetStaticDetailResponse() throws ClientGatewayException {
    	HotelDetailsMobRequestBody hotelDetailsMobRequestBody = new HotelDetailsMobRequestBody();
        Mockito.when(restConnectorUtil.performStaticDetailPost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("");
		String resp = staticDetailExecutor.getStaticDetailsResponse(hotelDetailsMobRequestBody, new HashMap<>(), new HashMap<>());
		Assert.assertNotNull(resp);
    }

    @Test
    public void testGetCityGuildeResponse() throws ClientGatewayException {
        CityGuideRequest cityGuideRequest = new CityGuideRequest();
        Mockito.when(restConnectorUtil.performStaticDetailPost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("{}");
        CityGuideResponse resp = staticDetailExecutor.getCityGuildeResponse(cityGuideRequest, new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(resp);
    }

    @Test
    public void testGetWishListedHotelsFromHotStore() throws ClientGatewayException {
        HotStoreHotelsRequestBody hotStoreHotelsRequestBody = new HotStoreHotelsRequestBody();
        hotStoreHotelsRequestBody.setCorrelationKey(UUID.randomUUID().toString());
        Mockito.when(restConnectorUtil.performHotelsHotStorePost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("{}");
        HotStoreHotelsWrapperResponse wishListedHotelsFromHotStore = staticDetailExecutor.getWishListedHotelsFromHotStore(hotStoreHotelsRequestBody, new HashMap<>());
        Assert.assertNotNull(wishListedHotelsFromHotStore);
    }

    @Test
    public void testGetFlyFishReviewSummary() throws ClientGatewayException {
        FlyfishReviewRequestBody flyfishReviewRequestBody = new FlyfishReviewRequestBody();
        flyfishReviewRequestBody.setCorrelationKey(UUID.randomUUID().toString());
        Mockito.when(restConnectorUtil.performHotelsReviewSummaryPost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("{}");
        FlyfishReviewWrapperResponse flyFishReviewSummary = staticDetailExecutor.getFlyFishReviewSummary(flyfishReviewRequestBody, new HashMap<>());
        Assert.assertNotNull(flyFishReviewSummary);
    }

    @Test
    public void testConvertStaffInfo_withStarHost() {
        StaffInfo staffInfo = new StaffInfo();
        staffInfo.setIsStarHost(true);

        StaffInfo result = transformer.convertStaffInfo(staffInfo);

        assertNotNull(result);
    }

    @Test
    public void testConvertStaffInfo_withoutStarHost() {
        StaffInfo staffInfo = new StaffInfo();
        staffInfo.setIsStarHost(false);

        StaffInfo result = transformer.convertStaffInfo(staffInfo);

        assertNotNull(result);
        assertNull(result.getStarHostIconUrl());
    }

    // Test cases for UGCPlatformReviewSummaryDTO - getUgcCategoryDetail method
    @Test
    public void testGetUgcCategoryDetail_Success() {
        // Arrange
        PlatformUgcCategoryRequest request = new PlatformUgcCategoryRequest();
        request.setCorrelationKey("test-ck");
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        headers.put("USER_COUNTRY", "IN");
        headers.put("USER_CURRENCY", "INR");

        String mockResponse = "{\"currency\":\"INR\",\"data\":{}}";
        try {
            Mockito.when(restConnectorUtil.performStaticDetailPost(Mockito.any(), Mockito.any(), Mockito.any()))
                   .thenReturn(mockResponse);

            // Act
            UGCPlatformReviewSummaryDTO result = staticDetailExecutor.getUgcCategoryDetail(request, parameterMap, headers);

            // Assert
            Assert.assertNotNull(result);
            Assert.assertEquals("INR", result.getCurrency());
        } catch (Exception e) {
            Assert.fail("Unexpected exception: " + e.getMessage());
        }
    }

    @Test
    public void testGetUgcCategoryDetail_WithoutUserCountry() {
        // Arrange
        PlatformUgcCategoryRequest request = new PlatformUgcCategoryRequest();
        request.setCorrelationKey("test-ck");
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>(); // No USER_COUNTRY header

        String mockResponse = "{\"data\":{}}";
        try {
            Mockito.when(restConnectorUtil.performStaticDetailPost(Mockito.any(), Mockito.any(), Mockito.any()))
                   .thenReturn(mockResponse);

            // Act
            UGCPlatformReviewSummaryDTO result = staticDetailExecutor.getUgcCategoryDetail(request, parameterMap, headers);

            // Assert
            Assert.assertNotNull(result);
            Assert.assertEquals("INR", result.getCurrency()); // Should default to INR
        } catch (Exception e) {
            Assert.fail("Unexpected exception: " + e.getMessage());
        }
    }

    // Test cases for UserReviewResponseForListing - getPlatformsUgcSummary method
    @Test
    public void testGetPlatformsUgcSummary_Success() {
        // Arrange
        UgcSummaryRequest request = new UgcSummaryRequest();
        Map<String, String> httpHeaderMap = new HashMap<>();
        String ck = "test-correlation-key";

        String mockResponse = "{\"data\":{\"reviews\":[]}}";
        try {
            Mockito.when(restConnectorUtil.performPlatformReviewSummaryPost(Mockito.any(), Mockito.any(), Mockito.any()))
                   .thenReturn(mockResponse);

            // Act
            UserReviewResponseForListing result = staticDetailExecutor.getPlatformsUgcSummary(request, httpHeaderMap, ck);

            // Assert
            Assert.assertNotNull(result);
            Mockito.verify(metricAspect).addToTime(Mockito.eq("ORCHESTRATOR"), Mockito.eq("getPlatformsUgcSummary"), Mockito.anyLong());
        } catch (Exception e) {
            Assert.fail("Unexpected exception: " + e.getMessage());
        }
    }

    @Test
    public void testGetPlatformsUgcSummary_Exception() {
        // Arrange
        UgcSummaryRequest request = new UgcSummaryRequest();
        Map<String, String> httpHeaderMap = new HashMap<>();
        String ck = "test-correlation-key";

        try {
            Mockito.when(restConnectorUtil.performPlatformReviewSummaryPost(Mockito.any(), Mockito.any(), Mockito.any()))
                   .thenThrow(new RuntimeException("Network error"));

            // Act
            UserReviewResponseForListing result = staticDetailExecutor.getPlatformsUgcSummary(request, httpHeaderMap, ck);

            // Assert
            Assert.assertNotNull(result); // Should return empty object, not null
            Mockito.verify(metricAspect).addToTime(Mockito.eq("ORCHESTRATOR"), Mockito.eq("getPlatformsUgcSummary"), Mockito.anyLong());
        } catch (Exception e) {
            Assert.fail("Unexpected exception: " + e.getMessage());
        }
    }

}
