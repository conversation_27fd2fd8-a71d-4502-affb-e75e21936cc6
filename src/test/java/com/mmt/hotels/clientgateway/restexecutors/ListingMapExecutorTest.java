package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.util.HeadersUtil;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.request.CityOverviewHesRequest;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.pojo.matchmaker.WikiResponse;
import com.mmt.hotels.pojo.response.HotelListingMapResponse;

import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.FileReader;
import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class ListingMapExecutorTest {

    @InjectMocks
    ListingMapExecutor listingMapExecutor;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    @Spy
    private HeadersUtil headersUtil;

    private static final Logger logger = LoggerFactory.getLogger(MetricErrorLogger.class);


    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
        ReflectionTestUtils.setField(listingMapExecutor, "listingMapUrl", "abc");
    }

    @Test
    public void listingMapTest() throws ClientGatewayException {

        String resp= null;

        try {


            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp =  jo.get("respListingMap").toString();

        }catch (Exception e){
            logger.error("error occured in getting file", e.getMessage());

        }

        Mockito.when(restConnectorUtil.performListingMapPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        HotelListingMapResponse response = listingMapExecutor.listingMap(new SearchWrapperInputRequest(), new HashMap<String, String>());
        Assert.assertNotNull(response);

    }

    @Test
    public void fetchCityOverviewTest() throws JsonParseException, ErrorResponseFromDownstreamException, RestConnectorException {
        CityOverviewHesRequest cityOverviewHesRequest = new CityOverviewHesRequest();
        cityOverviewHesRequest.setCorrelationKey("xyz");
        String testResponse = "{\"cityCode\":\"CTDUB\",\"countryCode\":\"IN\",\"defaultSearchText\":\"Search for Areas/Locations in Dubai\",\"questions\":[{\"name\":\"Location\",\"desc\":\"Location\",\"type\":\"LOCATION\",\"order\":1,\"category\":[{\"desc\":\"Popular Areas\",\"type\":2,\"displayType\":\"expandable\",\"tags\":[{\"id\":-1925593682,\"desc\":\"Sheikh Zayed Road\",\"typeId\":3,\"cityName\":\"Dubai\",\"wiki\":{\"wikiMetaInfo\":{\"bestForText\":\"Best For Business, Family\",\"persuasionText\":\"Home to numerous skyscrapers including Burj Khalifa, Sheikh Zayed Road offers great connectivity to most of Dubai's attractions.\",\"budget\":\"4,000 - 11,000 INR\",\"shortText\":\"Has Iconic Landmarks, Malls & Nightlife\"}},\"matchMakerTagLatLngObject\":{\"lat\":\"25.06092\",\"lng\":\"55.12898\"},\"areaIdStr\":\"ARSHEIKHZA\",\"showableEntities\":[\"CTDUB\"],\"propCount\":469,\"type\":\"area\",\"tagType\":\"area\",\"bbox\":{\"ne\":{\"lat\":25.2383194025405,\"lng\":55.316573047717},\"sw\":{\"lat\":24.8927558653326,\"lng\":54.9459956397368}},\"bbPolygon\":{\"type\":\"Polygon\",\"coordinates\":[[[54.9459956397368,24.8927558653326],[55.316573047717,24.8927558653326],[55.316573047717,25.2383194025405],[54.9459956397368,25.2383194025405],[54.9459956397368,24.8927558653326]]]},\"pivot\":false,\"isCity\":false,\"isPoi\":false,\"isPivot\":false},{\"id\":-29284730,\"desc\":\"Deira\",\"typeId\":3,\"cityName\":\"Dubai\",\"wiki\":{\"wikiMetaInfo\":{\"bestForText\":\"Best For Couple, Family\",\"persuasionText\":\"One of the most affordable places to stay in Dubai, Deira is a multicultural hub with great shopping and heritage buildings.\",\"budget\":\"2,000 - 7,000 INR\",\"shortText\":\"Relaxed & Fun-filled\"}},\"matchMakerTagLatLngObject\":{\"lat\":\"25.26942\",\"lng\":\"55.31837\"},\"areaIdStr\":\"ARDEIRA\",\"showableEntities\":[\"CTDUB\"],\"propCount\":47,\"type\":\"area\",\"tagType\":\"area\",\"bbox\":{\"ne\":{\"lat\":25.30326779222581,\"lng\":55.3597922196783},\"sw\":{\"lat\":25.23490143,\"lng\":55.29227829000001}},\"bbPolygon\":{\"type\":\"Polygon\",\"coordinates\":[[[55.29227829,25.23490143],[55.3597922196783,25.23490143],[55.3597922196783,25.3032677922258],[55.29227829,25.3032677922258],[55.29227829,25.23490143]]]},\"pivot\":false,\"isCity\":false,\"isPoi\":false,\"isPivot\":false},{\"id\":317020917,\"desc\":\"Burj Khalifa\",\"typeId\":3,\"cityName\":\"Dubai\",\"wiki\":{\"wikiMetaInfo\":{\"bestForText\":\"Best For Couple, Family\",\"persuasionText\":\"One of the most affordable places to stay in Dubai, Deira is a multicultural hub with great shopping and heritage buildings.\",\"budget\":\"2,000 - 7,000 INR\",\"shortText\":\"Relaxed & Fun-filled\"}},\"matchMakerTagLatLngObject\":{\"lat\":\"25.26942\",\"lng\":\"55.31837\"},\"areaIdStr\":\"POIBURJ\",\"showableEntities\":[\"CTDUB\"],\"propCount\":47,\"type\":\"poi\",\"tagType\":\"poi\",\"pivot\":false,\"isCity\":false,\"isPoi\":true,\"isPivot\":false}],\"isCity\":false}],\"bbox\":{\"ne\":{\"lat\":25.37412,\"lng\":55.73926},\"sw\":{\"lat\":24.62308,\"lng\":54.89107}},\"bbPolygon\":{\"type\":\"Polygon\",\"coordinates\":[[[54.89107,24.62308],[55.73926,24.62308],[55.73926,25.37412],[54.89107,25.37412],[54.89107,24.62308]]]}}],\"name\":\"Dubai\",\"description\":\"<p>Explore Dubai</p>\"}";
        Mockito.when(restConnectorUtil.performCityOverviewSearch(Mockito.any(),Mockito.anyMap(),Mockito.any())).thenReturn(testResponse);
        WikiResponse wikiResponse = listingMapExecutor.fetchCityOverview(cityOverviewHesRequest,new HashMap<>(),new HashMap<>());
        Assert.assertNotNull(wikiResponse);
    }

}
