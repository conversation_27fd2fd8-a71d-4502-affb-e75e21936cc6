package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.request.payment.BeginCheckoutReqBody;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import com.mmt.hotels.model.response.payment.offerdetails.OfferDetailsResponse;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class PaymentExecutorTest {

    @InjectMocks
    PaymentExecutor paymentExecutor;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    @Spy
    private MetricAspect metricAspect;

    @Spy
    private HeadersUtil headersUtil;

    private static final Logger logger = LoggerFactory.getLogger(MetricErrorLogger.class);


    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
        ReflectionTestUtils.setField(paymentExecutor, "paymentUrl", "abc");

    }

    @Test
    public void beginPaymentCheckoutTest() throws Exception {
        String resp = "{\"doubleBlackResponse\":{\"correlationKey\":\"123456\",\"doubleBlackValidated\":true,\"txnKey\":\"djnwjfnwe234\"}}";
        Mockito.when(restConnectorUtil.performPaymentCheckoutPost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(resp);

        BeginCheckoutReqBody paymentRequest = new BeginCheckoutReqBody();
//        Mockito.when(objectMapperUtil.getJsonFromObject(Mockito.any(), Mockito.any())).thenReturn("request");
//        Mockito.when(HeadersUtil.getHeadersFromServletRequest(Mockito.any())).thenReturn(new HashMap<>());


        PaymentCheckoutResponse response = paymentExecutor.beginPaymentCheckout(paymentRequest, new HashMap<>());
        Assert.assertNotNull(response);

    }

    @Test
    public void beginPaymentCheckoutForModifyBookingTest() throws RestConnectorException, JsonParseException, ErrorResponseFromDownstreamException {
        String resp = "{\"doubleBlackResponse\":{\"correlationKey\":\"123456\",\"doubleBlackValidated\":true,\"txnKey\":\"djnwjfnwe234\"}}";
        Mockito.when(restConnectorUtil.performPaymentCheckoutPost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(resp);

        BeginCheckoutReqBody paymentRequest = new BeginCheckoutReqBody();
        PaymentCheckoutResponse response = paymentExecutor.beginPaymentCheckoutForModifyBooking(paymentRequest, new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void offerDetailsTest() throws RestConnectorException, JsonParseException, ErrorResponseFromDownstreamException {
        String resp = "{\"success\":false,\"responseErrors\":null,\"manthanDetails\":null,\"couponDetailsList\":null}";
        Mockito.when(restConnectorUtil.performOfferDetailsPost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(resp);
        OfferDetailsResponse response = paymentExecutor.getOfferDetails("MOJO", new HydraResponse(), new ExtendedUser(), new HashMap<>(), "");
        Assert.assertNotNull(response);
    }

    @Test
    public void offerDetailsPostTest() throws RestConnectorException, JsonParseException, ErrorResponseFromDownstreamException {
        String resp = "{\"success\":false,\"responseErrors\":null,\"manthanDetails\":null,\"couponDetailsList\":null}";
        Mockito.when(restConnectorUtil.performOfferDetailsPost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(resp);
        String response = restConnectorUtil.performOfferDetailsPost("",  new HashMap<>(), "");
        Assert.assertNotNull(response);
    }

}
