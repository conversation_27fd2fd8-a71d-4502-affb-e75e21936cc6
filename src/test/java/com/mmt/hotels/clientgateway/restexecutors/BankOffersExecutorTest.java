package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.pojo.request.landing.BankOffersRequest;
import com.mmt.hotels.pojo.response.bankoffers.BankOffersResponse;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.FileReader;
import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class BankOffersExecutorTest {

    @InjectMocks
    BankOffersExecutor bankOffersExecutor;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    private static final Logger logger = LoggerFactory.getLogger(MetricErrorLogger.class);

    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
        ReflectionTestUtils.setField(bankOffersExecutor, "bankOffersUrl", "abc");
    }

    @Test
    public void bankOffersApiTest() throws ClientGatewayException {
        String resp= null;
        try {
            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp =  jo.get("bankOffers").toString();
        } catch (Exception e){
            logger.error("error occured in getting file", e.getMessage());
        }
        Mockito.when(restConnectorUtil.performGetBankOffersPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        BankOffersResponse response = bankOffersExecutor.bankOffers(new BankOffersRequest(), "", new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(response);
    }

}
