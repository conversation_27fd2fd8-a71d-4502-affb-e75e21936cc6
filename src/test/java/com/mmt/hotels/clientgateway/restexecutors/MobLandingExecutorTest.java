package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.util.HeadersUtil;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.pojo.request.landing.HotelLandingMobRequestBody;
import com.mmt.hotels.pojo.response.landing.HotelLandingWrapperResponse;

import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.FileReader;
import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class MobLandingExecutorTest {


    @InjectMocks
    MobLandingExecutor mobLandingExecutor;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    @Mock
    private PricingEngineHelper pricingEngineHelper;

    @Spy
    private HeadersUtil headersUtil;

    private static final Logger logger = LoggerFactory.getLogger(MetricErrorLogger.class);


    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
        ReflectionTestUtils.setField(mobLandingExecutor, "mobLandingUrl", "abc");
        ReflectionTestUtils.setField(mobLandingExecutor, "placeToLatLngURL", "placeToLatLngURL");
        ReflectionTestUtils.setField(mobLandingExecutor, "latLngToCityCodeURL", "latLngToCityCodeURL");
        ReflectionTestUtils.setField(mobLandingExecutor, "listPersonalizedCardURL", "listPersonalizedCardURL");
        
    }

    @Test
    public void mobLandingTest() throws ClientGatewayException {

        String resp= null;

        try {


            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp =  jo.get("respMobLanding").toString();

        }catch (Exception e){
            logger.error("error occured in getting file", e.getMessage());

        }

        Mockito.when(restConnectorUtil.performMobLandingPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        String response = mobLandingExecutor.moblanding(new HotelLandingMobRequestBody(), new HashMap<>(), new HashMap<String, String>());
        Assert.assertNotNull(response);

    }

    @Test
    public void listPersonalizedCardsTest() throws ClientGatewayException {

        String resp= null;
        try {
            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp =  jo.get("listPersonalizationResponse").toString();
        }catch (Exception e){
            logger.error("error occured in getting file", e.getMessage());
        }
        HotelLandingMobRequestBody req = new HotelLandingMobRequestBody();
        req.setHotelSearchRequest(new SearchWrapperInputRequest());
        Mockito.when(restConnectorUtil.performMobLandingPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        String response = mobLandingExecutor.listPersonalizedCards(req, new HashMap<>(), new HashMap<String, String>());
        Assert.assertNotNull(response);

    }

    @Test
    public void testGetLatLngFromGooglePlaceId() throws ClientGatewayException{
    	Mockito.when(restConnectorUtil.getLatLngFromGooglePlaceId(Mockito.anyMap(), Mockito.anyString())).thenReturn("");
    	String resp = mobLandingExecutor.getLatLngFromGooglePlaceId("ChIJjSuxGPKzbTkRe0m0TyEX2vQ", null, null);
    	Assert.assertNotNull(resp);
    	
    	//for lat lng
    	resp = mobLandingExecutor.getLatLngFromGooglePlaceId(null, 28.66175500980565, 77.36283329086558);
    	Assert.assertNotNull(resp);
    }
}
