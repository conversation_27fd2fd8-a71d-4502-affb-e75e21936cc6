package com.mmt.hotels.clientgateway.restexecutors;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.request.ValidateCouponRequestBody;
import com.mmt.hotels.model.response.discount.ValidateCouponResponse;

import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class DiscountServiceExecutorTest {

	@InjectMocks
	DiscountServiceExecutor discountServiceExecutor;
	
	@Spy
	private ObjectMapperUtil objectMapperUtil;
	
	@Mock
	private RestConnectorUtil restConnectorUtil;

    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
        ReflectionTestUtils.setField(discountServiceExecutor, "validateCouponURL", "abc");
    }
    
    @Test
    public void testGetValidateCouponResponse() throws ClientGatewayException {
    	ValidateCouponRequestBody validateCouponRequestBody = new ValidateCouponRequestBody();
    	Mockito.when(restConnectorUtil.performValidateCouponPost(Mockito.any(),
    			Mockito.anyMap(), Mockito.anyString())).thenReturn("{}");
    	ValidateCouponResponse resp = discountServiceExecutor.getValidateCouponResponse(validateCouponRequestBody,new HashMap<>(),new HashMap<>());
    	Assert.assertNotNull(resp);
    }
    
    @Test(expected=ClientGatewayException.class)
    public void testGetValidateCouponResponseException() throws ClientGatewayException {
    	ValidateCouponRequestBody validateCouponRequestBody = new ValidateCouponRequestBody();
    	Mockito.when(restConnectorUtil.performValidateCouponPost(Mockito.any(),
    			Mockito.anyMap(), Mockito.anyString()))
    	.thenReturn("{\"responseErrors\":{\"errorList\":[{\"errorCode\":\"3000\",\"errorMessage\":\"11\",\"errorAdditionalInfo\":null}]}}");
    	discountServiceExecutor.getValidateCouponResponse(validateCouponRequestBody,new HashMap<>(),new HashMap<>());
    }
}
