package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class CardEngineExecutorTest {

    @InjectMocks
    private CardEngineExecutor cardEngineExecutor;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    @Mock
    private ObjectMapperUtil objectMapperUtil;


    @Before
    public void init() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Test
    public void getCardTest() throws ClientGatewayException {
        ReflectionTestUtils.setField(cardEngineExecutor,"listingCardEngineUrl","abc");
        String resp = cardEngineExecutor.getCard(new SearchWrapperInputRequest());
        Assert.assertNull(resp);
        Mockito.when(restConnectorUtil.performCardEnginePost(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("{abc}");
        resp = cardEngineExecutor.getCard(new SearchWrapperInputRequest());
        Assert.assertNotNull(resp);
    }
}
