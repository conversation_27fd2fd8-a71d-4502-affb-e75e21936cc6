package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gommt.hotels.orchestrator.detail.enums.Funnel;
import com.gommt.hotels.orchestrator.detail.enums.TrafficType;
import com.gommt.hotels.orchestrator.detail.model.request.DetailRequest;
import com.gommt.hotels.orchestrator.detail.model.request.common.ClientDetails;
import com.gommt.hotels.orchestrator.detail.model.state.RequestDetails;
import com.gommt.hotels.orchestrator.detail.model.response.HotelStaticContentResponse;
import com.gommt.hotels.orchestrator.detail.model.response.common.ErrorResponse;
import com.gommt.hotels.orchestrator.detail.enums.Country;
import com.gommt.hotels.orchestrator.detail.enums.SiteDomain;
import com.gommt.hotels.orchestrator.detail.model.response.UpdatePriceResponse;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrchDetailExecutorTest {
    
    @InjectMocks
    OrchDetailExecutor orchDetailExecutor;

    @Mock
    MetricAspect metricAspect;

    @Mock
    ObjectMapperUtil objectMapperUtil;

    @Mock
    RestConnectorUtil restConnectorUtil;

    private ObjectMapper mapper;

    private DetailRequest detailRequest;
    private Map<String, String[]> parameterMap;
    private Map<String, String> headers;
    private UpdatePriceResponse updatePriceResponse;

    @Before
    public void init() {
        mapper = new ObjectMapper();
        mapper.writerWithView(PIIView.External.class);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
        ReflectionTestUtils.setField(orchDetailExecutor, "orchSearchRooms", "http://test.url/search-rooms");
        ReflectionTestUtils.setField(orchDetailExecutor, "orchStaticDetailsUrl", "http://test.url/static-details");
        detailRequest = new DetailRequest();
        parameterMap = new HashMap<>();
        parameterMap.put("test", new String[]{"value"});
        headers = new HashMap<>();
        headers.put("Authorization", "Bearer token");

        updatePriceResponse = new UpdatePriceResponse();
        try {
            java.lang.reflect.Field field = OrchDetailExecutor.class.getDeclaredField("orchUpdatePrice");
            field.setAccessible(true);
            field.set(orchDetailExecutor, "http://test-url/updatePrice");
        } catch (Exception e) {
            // Handle reflection exception
        }
    }

    // ===================== STATIC DETAILS METHOD TESTS =====================

    @Test
    public void testStaticDetails_ValidRequestWithFullClientDetails_ShouldReturnResponse() throws Exception {
        // Given - Line 94: Method signature and start time recording
        DetailRequest detailRequest = createDetailRequestWithClientDetails();
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("existing", new String[]{"value"});
        Map<String, String> headers = new HashMap<>();
        
        String requestJson = "{\"test\":\"json\"}";
        String responseJson = "{\"response\":\"json\"}";
        HotelStaticContentResponse expectedResponse = createValidHotelStaticContentResponse();
        
        // Mock - Line 97: JSON conversion
        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY)).thenReturn(requestJson);
        
        // Mock - Line 121: REST call (we can't easily mock the URL construction, so we'll mock the REST call with any URL)
        when(restConnectorUtil.performOrchestratorStaticDetailsPost(
                eq(requestJson), 
                any(Map.class), 
                anyString()))
                .thenReturn(responseJson);
        
        // Mock - Line 125: Response conversion
        when(objectMapperUtil.getObjectFromJson(responseJson, HotelStaticContentResponse.class, DependencyLayer.ORCHESTRATOR_DETAIL))
                .thenReturn(expectedResponse);
        
        // When
        HotelStaticContentResponse result = orchDetailExecutor.staticDetails(detailRequest, parameterMap, headers);
        
        // Then
        assertNotNull(result);
        assertEquals(expectedResponse, result);
        
        // Verify all interactions
        verify(objectMapperUtil).getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY);
        verify(restConnectorUtil).performOrchestratorStaticDetailsPost(eq(requestJson), any(Map.class), anyString());
        verify(objectMapperUtil).getObjectFromJson(responseJson, HotelStaticContentResponse.class, DependencyLayer.ORCHESTRATOR_DETAIL);
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchStaticDetails"), anyLong());
    }

    @Test
    public void testStaticDetails_ValidRequestWithPartialClientDetails_ShouldUseDefaults() throws Exception {
        // Given - Test Lines 99-105: Null value handling for client details
        DetailRequest detailRequest = createDetailRequestWithPartialClientDetails();
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        
        String requestJson = "{\"partial\":\"json\"}";
        String responseJson = "{\"partial\":\"response\"}";
        HotelStaticContentResponse expectedResponse = createValidHotelStaticContentResponse();
        
        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY)).thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorStaticDetailsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        when(objectMapperUtil.getObjectFromJson(responseJson, HotelStaticContentResponse.class, DependencyLayer.ORCHESTRATOR_DETAIL))
                .thenReturn(expectedResponse);
        
        // When
        HotelStaticContentResponse result = orchDetailExecutor.staticDetails(detailRequest, parameterMap, headers);
        
        // Then
        assertNotNull(result);
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchStaticDetails"), anyLong());
    }

    @Test
    public void testStaticDetails_NullClientDetails_ShouldUseAllDefaults() throws Exception {
        // Given - Test Lines 99-105: All null handling
        DetailRequest detailRequest = createValidDetailRequest(); // No client details
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        
        String requestJson = "{\"null\":\"client\"}";
        String responseJson = "{\"default\":\"response\"}";
        HotelStaticContentResponse expectedResponse = createValidHotelStaticContentResponse();
        
        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY)).thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorStaticDetailsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        when(objectMapperUtil.getObjectFromJson(responseJson, HotelStaticContentResponse.class, DependencyLayer.ORCHESTRATOR_DETAIL))
                .thenReturn(expectedResponse);
        
        // When
        HotelStaticContentResponse result = orchDetailExecutor.staticDetails(detailRequest, parameterMap, headers);
        
        // Then
        assertNotNull(result);
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchStaticDetails"), anyLong());
    }

    @Test
    public void testStaticDetails_NullParameterMap_ShouldCreateNewMap() throws Exception {
        // Given - Test Line 106: parameterMap != null ? new HashMap<>(parameterMap) : new HashMap<>()
        DetailRequest detailRequest = createDetailRequestWithClientDetails();
        Map<String, String[]> parameterMap = null; // Testing null parameter map
        Map<String, String> headers = new HashMap<>();
        
        String requestJson = "{\"null\":\"paramMap\"}";
        String responseJson = "{\"created\":\"map\"}";
        HotelStaticContentResponse expectedResponse = createValidHotelStaticContentResponse();
        
        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY)).thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorStaticDetailsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        when(objectMapperUtil.getObjectFromJson(responseJson, HotelStaticContentResponse.class, DependencyLayer.ORCHESTRATOR_DETAIL))
                .thenReturn(expectedResponse);
        
        // When
        HotelStaticContentResponse result = orchDetailExecutor.staticDetails(detailRequest, parameterMap, headers);
        
        // Then
        assertNotNull(result);
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchStaticDetails"), anyLong());
    }

    @Test
    public void testStaticDetails_DownstreamErrorResponse_ShouldThrowException() throws Exception {
        // Given - Test Lines 128-133: Error response handling
        DetailRequest detailRequest = createDetailRequestWithClientDetails();
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        
        String requestJson = "{\"error\":\"request\"}";
        String responseJson = "{\"error\":\"response\"}";
        HotelStaticContentResponse errorResponse = createHotelStaticContentResponseWithError();
        
        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY)).thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorStaticDetailsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        when(objectMapperUtil.getObjectFromJson(responseJson, HotelStaticContentResponse.class, DependencyLayer.ORCHESTRATOR_DETAIL))
                .thenReturn(errorResponse);
        
        // When & Then - Should throw ErrorResponseFromDownstreamException
        try {
            orchDetailExecutor.staticDetails(detailRequest, parameterMap, headers);
            fail("Expected ErrorResponseFromDownstreamException to be thrown");
        } catch (ErrorResponseFromDownstreamException exception) {
            // Verify exception details
            assertEquals(DependencyLayer.ORCHESTRATOR_NEW, exception.getDependencyLayer());
            assertEquals(ErrorType.DOWNSTREAM, exception.getErrorType());
            assertNotNull(exception.getMessage());
        }
        
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchStaticDetails"), anyLong());
    }

    @Test
    public void testStaticDetails_JsonSerializationException_ShouldThrowException() throws Exception {
        // Given - Test Line 97: objectMapperUtil.getJsonFromObject exception
        DetailRequest detailRequest = createDetailRequestWithClientDetails();
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        
        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenThrow(new RuntimeException("JSON serialization failed"));
        
        // When & Then
        try {
            orchDetailExecutor.staticDetails(detailRequest, parameterMap, headers);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException exception) {
            assertEquals("JSON serialization failed", exception.getMessage());
        }
        
        // Verify metrics are still recorded in finally block (Line 137)
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchStaticDetails"), anyLong());
    }

    @Test
    public void testStaticDetails_RestCallException_ShouldThrowException() throws Exception {
        // Given - Test Line 121: restConnectorUtil.performOrchestratorStaticDetailsPost exception
        DetailRequest detailRequest = createDetailRequestWithClientDetails();
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        
        String requestJson = "{\"rest\":\"exception\"}";
        
        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY)).thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorStaticDetailsPost(anyString(), any(Map.class), anyString()))
                .thenThrow(new RuntimeException("REST call failed"));
        
        // When & Then
        try {
            orchDetailExecutor.staticDetails(detailRequest, parameterMap, headers);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException exception) {
            assertEquals("REST call failed", exception.getMessage());
        }
        
        // Verify metrics are still recorded in finally block
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchStaticDetails"), anyLong());
    }

    @Test
    public void testStaticDetails_JsonDeserializationException_ShouldThrowException() throws Exception {
        // Given - Test Line 125: objectMapperUtil.getObjectFromJson exception
        DetailRequest detailRequest = createDetailRequestWithClientDetails();
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        
        String requestJson = "{\"deserial\":\"exception\"}";
        String responseJson = "{\"malformed\":\"json\"}";
        
        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY)).thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorStaticDetailsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        when(objectMapperUtil.getObjectFromJson(responseJson, HotelStaticContentResponse.class, DependencyLayer.ORCHESTRATOR_DETAIL))
                .thenThrow(new RuntimeException("JSON deserialization failed"));
        
        // When & Then
        try {
            orchDetailExecutor.staticDetails(detailRequest, parameterMap, headers);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException exception) {
            assertEquals("JSON deserialization failed", exception.getMessage());
        }
        
        // Verify metrics are still recorded in finally block
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchStaticDetails"), anyLong());
    }

    @Test
    public void testStaticDetails_MetricsRecording_ShouldAlwaysRecordMetrics() throws Exception {
        // Given - Test Line 137: Finally block metrics recording
        DetailRequest detailRequest = createDetailRequestWithClientDetails();
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        
        String requestJson = "{\"metrics\":\"test\"}";
        String responseJson = "{\"metrics\":\"response\"}";
        HotelStaticContentResponse expectedResponse = createValidHotelStaticContentResponse();
        
        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY)).thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorStaticDetailsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        when(objectMapperUtil.getObjectFromJson(responseJson, HotelStaticContentResponse.class, DependencyLayer.ORCHESTRATOR_DETAIL))
                .thenReturn(expectedResponse);
        
        // When
        HotelStaticContentResponse result = orchDetailExecutor.staticDetails(detailRequest, parameterMap, headers);
        
        // Then
        assertNotNull(result);
        
        // Verify metrics are recorded with correct parameters
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchStaticDetails"), anyLong());
    }

    @Test
    public void testStaticDetails_EmptyParameterMap_ShouldAddRequiredHeaders() throws Exception {
        // Given - Test parameter map handling with empty map
        DetailRequest detailRequest = createDetailRequestWithClientDetails();
        Map<String, String[]> parameterMap = new HashMap<>(); // Empty but not null
        Map<String, String> headers = new HashMap<>();
        
        String requestJson = "{\"empty\":\"paramMap\"}";
        String responseJson = "{\"headers\":\"added\"}";
        HotelStaticContentResponse expectedResponse = createValidHotelStaticContentResponse();
        
        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY)).thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorStaticDetailsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        when(objectMapperUtil.getObjectFromJson(responseJson, HotelStaticContentResponse.class, DependencyLayer.ORCHESTRATOR_DETAIL))
                .thenReturn(expectedResponse);
        
        // When
        HotelStaticContentResponse result = orchDetailExecutor.staticDetails(detailRequest, parameterMap, headers);
        
        // Then
        assertNotNull(result);
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchStaticDetails"), anyLong());
    }

    @Test
    public void testStaticDetails_ExistingParameterMapPreserved_ShouldPreserveExistingParams() throws Exception {
        // Given - Test that existing parameters in parameterMap are preserved
        DetailRequest detailRequest = createDetailRequestWithClientDetails();
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("existingParam1", new String[]{"value1"});
        parameterMap.put("existingParam2", new String[]{"value2", "value3"});
        Map<String, String> headers = new HashMap<>();
        
        String requestJson = "{\"existing\":\"params\"}";
        String responseJson = "{\"preserved\":\"params\"}";
        HotelStaticContentResponse expectedResponse = createValidHotelStaticContentResponse();
        
        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY)).thenReturn(requestJson);
        when(restConnectorUtil.performOrchestratorStaticDetailsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        when(objectMapperUtil.getObjectFromJson(responseJson, HotelStaticContentResponse.class, DependencyLayer.ORCHESTRATOR_DETAIL))
                .thenReturn(expectedResponse);
        
        // When
        HotelStaticContentResponse result = orchDetailExecutor.staticDetails(detailRequest, parameterMap, headers);
        
        // Then
        assertNotNull(result);
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchStaticDetails"), anyLong());
    }

    // ===================== BUILD HEADER MAP TESTS =====================

    @Test
    public void testBuildHeaderMap_ValidHeaders_ShouldIncludeRequiredHeaders() throws Exception {
        // Given
        Map<String, String> inputHeaders = new HashMap<>();
        inputHeaders.put("X-Custom-Header", "custom-value");
        
        // Use reflection to access private method
        java.lang.reflect.Method method = OrchDetailExecutor.class.getDeclaredMethod("buildHeaderMap", Map.class);
        method.setAccessible(true);
        
        // When
        @SuppressWarnings("unchecked")
        Map<String, String> result = (Map<String, String>) method.invoke(orchDetailExecutor, inputHeaders);
        
        // Then
        assertNotNull(result);
        assertEquals("application/json", result.get("Content-Type"));
        assertEquals("gzip", result.get("Accept-Encoding"));
    }

    @Test
    public void testBuildHeaderMap_NullHeaders_ShouldReturnDefaultHeaders() throws Exception {
        // Use reflection to access private method
        java.lang.reflect.Method method = OrchDetailExecutor.class.getDeclaredMethod("buildHeaderMap", Map.class);
        method.setAccessible(true);
        
        // When
        @SuppressWarnings("unchecked")
        Map<String, String> result = (Map<String, String>) method.invoke(orchDetailExecutor, (Map<String, String>) null);
        
        // Then
        assertNotNull(result);
        assertEquals("application/json", result.get("Content-Type"));
        assertEquals("gzip", result.get("Accept-Encoding"));
    }

    // ===================== HELPER METHODS =====================

    private DetailRequest createValidDetailRequest() {
        DetailRequest detailRequest = new DetailRequest();
        ClientDetails clientDetails = new ClientDetails();
        
        // Create basic RequestDetails to avoid NPE when accessing client details
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setRequestId("default-request-123");
        requestDetails.setSiteDomain(SiteDomain.IN);
        requestDetails.setCountry(Country.IH);
        requestDetails.setTrafficType(TrafficType.B2C);
        requestDetails.setFunnelSource(Funnel.HOTELS);
        requestDetails.setJourneyId("default-journey-123");
        
        clientDetails.setRequestDetails(requestDetails);
        detailRequest.setClientDetails(clientDetails);
        
        return detailRequest;
    }

    private DetailRequest createDetailRequestWithClientDetails() {
        DetailRequest detailRequest = new DetailRequest();
        ClientDetails clientDetails = new ClientDetails();
        
        // Create a fully populated RequestDetails for comprehensive testing
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setRequestId("test-request-123");
        requestDetails.setSiteDomain(SiteDomain.IN);
        requestDetails.setCountry(Country.IH);
        requestDetails.setTrafficType(TrafficType.B2C);
        requestDetails.setFunnelSource(Funnel.HOTELS);
        requestDetails.setJourneyId("journey-123");
        
        clientDetails.setRequestDetails(requestDetails);
        detailRequest.setClientDetails(clientDetails);
        
        return detailRequest;
    }

    private DetailRequest createDetailRequestWithPartialClientDetails() {
        DetailRequest detailRequest = new DetailRequest();
        ClientDetails clientDetails = new ClientDetails();
        
        // Create RequestDetails with some null values to test null handling
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setRequestId("partial-request-123");
        
        // Set proper enum values to avoid NPE - use the correct enum types
        try {
            requestDetails.setSiteDomain(SiteDomain.IN);
            requestDetails.setCountry(Country.IH);
            requestDetails.setTrafficType(TrafficType.B2C);
            requestDetails.setFunnelSource(Funnel.HOTELS);
        } catch (Exception e) {
            // If enum setting fails, set minimal required fields to avoid NPE
            requestDetails.setSiteDomain(null);
            requestDetails.setCountry(null);
        }
        
        // Leave other fields null to test null handling for journey id, etc.
        
        clientDetails.setRequestDetails(requestDetails);
        detailRequest.setClientDetails(clientDetails);
        
        return detailRequest;
    }



    private HotelStaticContentResponse createValidHotelStaticContentResponse() {
        HotelStaticContentResponse response = new HotelStaticContentResponse();
        // Basic setup for a valid response
        return response;
    }

    private HotelStaticContentResponse createHotelStaticContentResponseWithError() {
        HotelStaticContentResponse response = new HotelStaticContentResponse();
        ErrorResponse error = new ErrorResponse();
        error.setCode("STATIC_ERROR_CODE");
        error.setMessage("Static error message");
        error.setDescription("Static error description");
        response.setError(error);
        return response;
    }

    @Test
    public void testUpdatePrice_SuccessfulResponse() throws Exception {
        setUpCompleteDetailRequest();
        String requestJson = "{\"test\":\"request\"}";
        String responseJson = "{\"test\":\"response\"}";

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);

        when(restConnectorUtil.performOrchestratorSearchRoomsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);

        when(objectMapperUtil.getObjectFromJson(responseJson, UpdatePriceResponse.class, DependencyLayer.ORCHESTRATOR_NEW))
                .thenReturn(updatePriceResponse);

        // Act
        UpdatePriceResponse result = orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);

        // Assert
        Assert.assertNotNull(result);
        Assert.assertEquals(updatePriceResponse, result);
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
        verify(objectMapperUtil).getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY);
        verify(restConnectorUtil).performOrchestratorSearchRoomsPost(anyString(), any(Map.class), anyString());
    }

    @Test
    public void testUpdatePrice_NullDetailRequest() {
        // Act & Assert
        try {
            orchDetailExecutor.updatePrice(null, parameterMap, headers);
            Assert.fail("Expected ClientGatewayException to be thrown");
        } catch (ClientGatewayException exception) {
            Assert.assertEquals(DependencyLayer.CLIENTGATEWAY, exception.getDependencyLayer());
            Assert.assertEquals(ErrorType.VALIDATION, exception.getErrorType());
            Assert.assertEquals("INVALID_REQUEST", exception.getCode());
            Assert.assertEquals("DetailRequest cannot be null", exception.getMessage());
            verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
        }
    }

    @Test
    public void testUpdatePrice_NullParameterMap() throws Exception {
        setUpCompleteDetailRequest();
        String requestJson = "{\"test\":\"request\"}";
        String responseJson = "{\"test\":\"response\"}";

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);

        when(restConnectorUtil.performOrchestratorSearchRoomsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);

        when(objectMapperUtil.getObjectFromJson(responseJson, UpdatePriceResponse.class, DependencyLayer.ORCHESTRATOR_NEW))
                .thenReturn(updatePriceResponse);

        // Act
        UpdatePriceResponse result = orchDetailExecutor.updatePrice(detailRequest, null, headers);

        // Assert
        Assert.assertNotNull(result);
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
    }

    @Test
    public void testUpdatePrice_NullClientDetails() throws Exception {
        detailRequest.setClientDetails(null);
        String requestJson = "{\"test\":\"request\"}";
        String responseJson = "{\"test\":\"response\"}";

//        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
//                .thenReturn(requestJson);

//        when(restConnectorUtil.performOrchestratorSearchRoomsPost(anyString(), any(Map.class), anyString()))
//                .thenReturn(responseJson);

//        when(objectMapperUtil.getObjectFromJson(responseJson, UpdatePriceResponse.class, DependencyLayer.ORCHESTRATOR_NEW))
//                .thenReturn(updatePriceResponse);

        // Act & Assert - Should throw NullPointerException due to null requestDetails fields
        try {
            orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);
            Assert.fail("Expected NullPointerException to be thrown");
        } catch (NullPointerException exception) {
            // This is expected behavior when clientDetails is null
            verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
        }
    }

    @Test
    public void testUpdatePrice_NullRequestDetails() throws Exception {
        ClientDetails clientDetails = new ClientDetails();
        clientDetails.setRequestDetails(new RequestDetails());
        detailRequest.setClientDetails(clientDetails);

        String requestJson = "{\"test\":\"request\"}";
        String responseJson = "{\"test\":\"response\"}";

//        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
//                .thenReturn(requestJson);

//        when(restConnectorUtil.performOrchestratorSearchRoomsPost(anyString(), any(Map.class), anyString()))
//                .thenReturn(responseJson);

//        when(objectMapperUtil.getObjectFromJson(responseJson, UpdatePriceResponse.class, DependencyLayer.ORCHESTRATOR_NEW))
//                .thenReturn(updatePriceResponse);

        // Act & Assert - Should throw NullPointerException due to null requestDetails fields
        try {
            orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);
            Assert.fail("Expected NullPointerException to be thrown");
        } catch (NullPointerException exception) {
            // This is expected behavior when requestDetails has null fields
            verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
        }
    }

    @Test
    public void testUpdatePrice_PartialNullFields() throws Exception {
        ClientDetails clientDetails = new ClientDetails();
        RequestDetails requestDetails = new RequestDetails();
        // Set minimum required fields to avoid NPE, but leave some optional fields null
        requestDetails.setSiteDomain(com.gommt.hotels.orchestrator.detail.enums.SiteDomain.IN);
        requestDetails.setRequestId("partial-request-id"); // Cannot be null due to buildParameterMap
        requestDetails.setCountry(null); // This can be null
        requestDetails.setTrafficType(TrafficType.B2C);
        requestDetails.setFunnelSource(Funnel.HOTELS);
        requestDetails.setJourneyId(null); // This can be null
        clientDetails.setRequestDetails(requestDetails);
        detailRequest.setClientDetails(clientDetails);

        String requestJson = "{\"test\":\"request\"}";
        String responseJson = "{\"test\":\"response\"}";

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);

        when(restConnectorUtil.performOrchestratorSearchRoomsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);

        when(objectMapperUtil.getObjectFromJson(responseJson, UpdatePriceResponse.class, DependencyLayer.ORCHESTRATOR_NEW))
                .thenReturn(updatePriceResponse);

        // Act
        UpdatePriceResponse result = orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);

        // Assert
        Assert.assertNotNull(result);
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
    }

    @Test
    public void testUpdatePrice_NullResponse() throws Exception {
        setUpCompleteDetailRequest();
        String requestJson = "{\"test\":\"request\"}";

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);

        when(restConnectorUtil.performOrchestratorSearchRoomsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(null);

        // Act & Assert
        try {
            orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);
            Assert.fail("Expected ClientGatewayException to be thrown");
        } catch (ClientGatewayException exception) {
            Assert.assertEquals(DependencyLayer.ORCHESTRATOR_NEW, exception.getDependencyLayer());
            Assert.assertEquals(ErrorType.DOWNSTREAM, exception.getErrorType());
            Assert.assertEquals("EMPTY_RESPONSE", exception.getCode());
            Assert.assertEquals("Received null or empty response from orchestrator updatePrice API", exception.getMessage());
            verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
        }
    }

    @Test
    public void testUpdatePrice_EmptyResponse() throws Exception {
        setUpCompleteDetailRequest();
        String requestJson = "{\"test\":\"request\"}";

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);

        when(restConnectorUtil.performOrchestratorSearchRoomsPost(anyString(), any(Map.class), anyString()))
                .thenReturn("   ");

        // Act & Assert
        try {
            orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);
            Assert.fail("Expected ClientGatewayException to be thrown");
        } catch (ClientGatewayException exception) {
            Assert.assertEquals(DependencyLayer.ORCHESTRATOR_NEW, exception.getDependencyLayer());
            Assert.assertEquals(ErrorType.DOWNSTREAM, exception.getErrorType());
            Assert.assertEquals("EMPTY_RESPONSE", exception.getCode());
            verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
        }
    }

    @Test
    public void testUpdatePrice_ParseError() throws Exception {
        setUpCompleteDetailRequest();
        String requestJson = "{\"test\":\"request\"}";
        String responseJson = "{\"test\":\"response\"}";

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);

        when(restConnectorUtil.performOrchestratorSearchRoomsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);

        when(objectMapperUtil.getObjectFromJson(responseJson, UpdatePriceResponse.class, DependencyLayer.ORCHESTRATOR_NEW))
                .thenReturn(null);

        // Act & Assert
        try {
            orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);
            Assert.fail("Expected ClientGatewayException to be thrown");
        } catch (ClientGatewayException exception) {
            Assert.assertEquals(DependencyLayer.ORCHESTRATOR_NEW, exception.getDependencyLayer());
            Assert.assertEquals(ErrorType.DOWNSTREAM, exception.getErrorType());
            Assert.assertEquals("PARSE_ERROR", exception.getCode());
            Assert.assertEquals("Failed to parse response from orchestrator updatePrice API", exception.getMessage());
            verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
        }
    }

    @Test
    public void testUpdatePrice_ErrorResponseFromDownstream() throws Exception {
        setUpCompleteDetailRequest();
        String requestJson = "{\"test\":\"request\"}";
        String responseJson = "{\"test\":\"response\"}";

        ErrorResponse error = new ErrorResponse();
        error.setCode("ERROR_CODE");
        error.setMessage("Error message");
        error.setDescription("Error description");

        updatePriceResponse.setError(error);

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);

        when(restConnectorUtil.performOrchestratorSearchRoomsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);

        when(objectMapperUtil.getObjectFromJson(responseJson, UpdatePriceResponse.class, DependencyLayer.ORCHESTRATOR_NEW))
                .thenReturn(updatePriceResponse);

        // Act & Assert
        try {
            orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);
            Assert.fail("Expected ErrorResponseFromDownstreamException to be thrown");
        } catch (ErrorResponseFromDownstreamException exception) {
            Assert.assertEquals(DependencyLayer.ORCHESTRATOR_NEW, exception.getDependencyLayer());
            Assert.assertEquals(ErrorType.DOWNSTREAM, exception.getErrorType());
            Assert.assertEquals("ERROR_CODE", exception.getCode());
            Assert.assertEquals("Error message", exception.getMessage());
            verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
        }
    }

    @Test
    public void testUpdatePrice_JsonConversionException() throws Exception {
        setUpCompleteDetailRequest();

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenThrow(new RuntimeException("JSON conversion error"));

        // Act & Assert
        try {
            orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);
            Assert.fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException exception) {
            Assert.assertEquals("JSON conversion error", exception.getMessage());
            verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
        }
    }

    @Test
    public void testUpdatePrice_RestConnectorException() throws Exception {
        setUpCompleteDetailRequest();
        String requestJson = "{\"test\":\"request\"}";

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);

        when(restConnectorUtil.performOrchestratorSearchRoomsPost(anyString(), any(Map.class), anyString()))
                .thenThrow(new RuntimeException("REST connector error"));

        // Act & Assert
        try {
            orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);
            Assert.fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException exception) {
            Assert.assertEquals("REST connector error", exception.getMessage());
            verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
        }
    }

    @Test
    public void testUpdatePrice_WithAllNullFieldsInRequest() throws Exception {
        ClientDetails clientDetails = new ClientDetails();
        RequestDetails requestDetails = new RequestDetails();

        requestDetails.setSiteDomain(com.gommt.hotels.orchestrator.detail.enums.SiteDomain.IN);
        requestDetails.setCountry(com.gommt.hotels.orchestrator.detail.enums.Country.DH);
        requestDetails.setTrafficType(com.gommt.hotels.orchestrator.detail.enums.TrafficType.B2C);

        clientDetails.setRequestDetails(requestDetails);
        detailRequest.setClientDetails(clientDetails);

        String requestJson = "{\"test\":\"request\"}";
        String responseJson = "{\"test\":\"response\"}";

        when(objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);

        when(restConnectorUtil.performOrchestratorSearchRoomsPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);

        when(objectMapperUtil.getObjectFromJson(responseJson, UpdatePriceResponse.class, DependencyLayer.ORCHESTRATOR_NEW))
                .thenReturn(updatePriceResponse);

        // Act
        UpdatePriceResponse result = orchDetailExecutor.updatePrice(detailRequest, parameterMap, headers);

        // Assert
        Assert.assertNotNull(result);
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR_NEW.name()), eq("orchUpdatePrice"), anyLong());
    }

    private void setUpCompleteDetailRequest() {
        ClientDetails clientDetails = new ClientDetails();
        RequestDetails requestDetails = new RequestDetails();

        requestDetails.setSiteDomain(com.gommt.hotels.orchestrator.detail.enums.SiteDomain.IN);
        requestDetails.setRequestId("test-request-id");
        requestDetails.setCountry(com.gommt.hotels.orchestrator.detail.enums.Country.DH);
        requestDetails.setTrafficType(com.gommt.hotels.orchestrator.detail.enums.TrafficType.B2C);
        requestDetails.setFunnelSource(Funnel.HOTELS);
        requestDetails.setJourneyId("test-journey-id");

        clientDetails.setRequestDetails(requestDetails);
        detailRequest.setClientDetails(clientDetails);
    }

    // ===================== CALENDAR AVAILABILITY METHOD TESTS =====================

    @Test
    public void testGetCalendarAvailability_SuccessfulResponse_WithMmtAuthHeader() throws Exception {
        // Given - Testing the happy path with mmt-auth header (Line 359-360)
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("test", new String[]{"value"});
        Map<String, String> headers = new HashMap<>();
        headers.put("mmt-auth", "test-auth-token"); // Line 359-360 branch test
        
        String requestJson = "{\"calendar\":\"request\"}";
        String responseJson = "{\"calendar\":\"response\"}";
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse expectedResponse = 
            createValidConsolidatedCalendarResponse();
        
        // Set up the calendarAvailabilityUrl field
        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");
        
        // Mock - Line 362: JSON conversion
        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        
        // Mock - Line 364: URL construction (we can't easily mock static methods, so we'll rely on the actual implementation)
        
        // Mock - Line 367: REST call
        when(restConnectorUtil.performCalendarAvailabilityPost(
                eq(requestJson), 
                any(Map.class), 
                anyString()))
                .thenReturn(responseJson);
        
        // Mock - Line 368: Response conversion
        when(objectMapperUtil.getObjectFromJson(responseJson, 
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class, 
                DependencyLayer.ORCHESTRATOR))
                .thenReturn(expectedResponse);
        
        // When
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse result = 
            orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);
        
        // Then
        assertNotNull(result);
        assertEquals(expectedResponse, result);
        
        // Verify all interactions and line coverage
        verify(objectMapperUtil).getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY); // Line 362
        verify(restConnectorUtil).performCalendarAvailabilityPost(eq(requestJson), any(Map.class), anyString()); // Line 367
        verify(objectMapperUtil).getObjectFromJson(responseJson, 
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class, 
                DependencyLayer.ORCHESTRATOR); // Line 368
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong()); // Line 378
        
        // Verify header map contains mmt-auth (Line 359-360)
        verify(restConnectorUtil).performCalendarAvailabilityPost(anyString(), argThat((Map<String, String> headerMap) -> 
                headerMap.containsKey("mmt-auth") && "test-auth-token".equals(headerMap.get("mmt-auth"))), anyString());
    }

    @Test
    public void testGetCalendarAvailability_SuccessfulResponse_WithoutMmtAuthHeader() throws Exception {
        // Given - Testing without mmt-auth header (Line 359 false condition)
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        // No mmt-auth header to test the false branch of Line 359
        
        String requestJson = "{\"calendar\":\"request\"}";
        String responseJson = "{\"calendar\":\"response\"}";
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse expectedResponse = 
            createValidConsolidatedCalendarResponse();
        
        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");
        
        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        
        // URL construction uses actual implementation
        
        when(restConnectorUtil.performCalendarAvailabilityPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        
        when(objectMapperUtil.getObjectFromJson(responseJson, 
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class, 
                DependencyLayer.ORCHESTRATOR))
                .thenReturn(expectedResponse);
        
        // When
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse result = 
            orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);
        
        // Then
        assertNotNull(result);
        assertEquals(expectedResponse, result);
        
        // Verify header map does NOT contain mmt-auth (Line 359 false branch)
        verify(restConnectorUtil).performCalendarAvailabilityPost(anyString(), argThat((Map<String, String> headerMap) -> 
                !headerMap.containsKey("mmt-auth")), anyString());
        
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong());
    }

    @Test
    public void testGetCalendarAvailability_EmptyMmtAuthHeader() throws Exception {
        // Given - Testing with empty mmt-auth header (Line 359 false condition due to StringUtils.isNotEmpty)
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        headers.put("mmt-auth", ""); // Empty string should trigger false in StringUtils.isNotEmpty
        
        String requestJson = "{\"calendar\":\"request\"}";
        String responseJson = "{\"calendar\":\"response\"}";
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse expectedResponse = 
            createValidConsolidatedCalendarResponse();
        
        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");
        
        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        
        // URL construction uses actual implementation
        
        when(restConnectorUtil.performCalendarAvailabilityPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        
        when(objectMapperUtil.getObjectFromJson(responseJson, 
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class, 
                DependencyLayer.ORCHESTRATOR))
                .thenReturn(expectedResponse);
        
        // When
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse result = 
            orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);
        
        // Then
        assertNotNull(result);
        
        // Verify header map does NOT contain mmt-auth due to empty string (Line 359 false branch)
        verify(restConnectorUtil).performCalendarAvailabilityPost(anyString(), argThat((Map<String, String> headerMap) -> 
                !headerMap.containsKey("mmt-auth")), anyString());
    }

    @Test
    public void testGetCalendarAvailability_ResponseWithErrors_ShouldThrowException() throws Exception {
        // Given - Testing error response handling (Line 369-374)
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        
        String requestJson = "{\"calendar\":\"request\"}";
        String responseJson = "{\"calendar\":\"error\"}";
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse errorResponse = 
            createConsolidatedCalendarResponseWithErrors();
        
        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");
        
        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        
        // URL construction uses actual implementation
        
        when(restConnectorUtil.performCalendarAvailabilityPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        
        when(objectMapperUtil.getObjectFromJson(responseJson, 
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class, 
                DependencyLayer.ORCHESTRATOR))
                .thenReturn(errorResponse);
        
        // When & Then - Should throw ErrorResponseFromDownstreamException (Line 370-374)
        try {
            orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);
            fail("Expected ErrorResponseFromDownstreamException to be thrown");
        } catch (ErrorResponseFromDownstreamException exception) {
            // Verify exception details match Lines 370-374
            assertEquals(DependencyLayer.ORCHESTRATOR, exception.getDependencyLayer());
            assertEquals(ErrorType.DOWNSTREAM, exception.getErrorType());
            assertEquals("CAL_ERROR_001", exception.getCode());
            assertEquals("Calendar error message", exception.getMessage());
        }
        
        // Verify metrics are still recorded in finally block (Line 378)
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong());
    }

    @Test
    public void testGetCalendarAvailability_ResponseWithNullErrors_ShouldReturnResponse() throws Exception {
        // Given - Testing response with null error list (Line 369 false condition)
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        
        String requestJson = "{\"calendar\":\"request\"}";
        String responseJson = "{\"calendar\":\"response\"}";
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse responseWithNullErrors = 
            createConsolidatedCalendarResponseWithNullErrors();
        
        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");
        
        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        
        // URL construction uses actual implementation
        
        when(restConnectorUtil.performCalendarAvailabilityPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        
        when(objectMapperUtil.getObjectFromJson(responseJson, 
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class, 
                DependencyLayer.ORCHESTRATOR))
                .thenReturn(responseWithNullErrors);
        
        // When
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse result = 
            orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);
        
        // Then - Should return response (Line 376)
        assertNotNull(result);
        assertEquals(responseWithNullErrors, result);
        
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong());
    }

    @Test
    public void testGetCalendarAvailability_JsonSerializationException_ShouldThrowException() throws Exception {
        // Given - Testing JSON serialization exception (Line 362)
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        
        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");
        
        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenThrow(new RuntimeException("JSON serialization failed"));
        
        // When & Then
        try {
            orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException exception) {
            assertEquals("JSON serialization failed", exception.getMessage());
        }
        
        // Verify metrics are still recorded in finally block (Line 378)
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong());
    }

    @Test
    public void testGetCalendarAvailability_RestCallException_ShouldThrowException() throws Exception {
        // Given - Testing REST call exception (Line 367)
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        
        String requestJson = "{\"calendar\":\"request\"}";
        
        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");
        
        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        
        // URL construction uses actual implementation
        
        when(restConnectorUtil.performCalendarAvailabilityPost(anyString(), any(Map.class), anyString()))
                .thenThrow(new RuntimeException("REST call failed"));
        
        // When & Then
        try {
            orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException exception) {
            assertEquals("REST call failed", exception.getMessage());
        }
        
        // Verify metrics are still recorded in finally block (Line 378)
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong());
    }

    @Test
    public void testGetCalendarAvailability_JsonDeserializationException_ShouldThrowException() throws Exception {
        // Given - Testing JSON deserialization exception (Line 368)
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        
        String requestJson = "{\"calendar\":\"request\"}";
        String responseJson = "{\"malformed\":\"json\"}";
        
        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");
        
        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        
        // URL construction uses actual implementation
        
        when(restConnectorUtil.performCalendarAvailabilityPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        
        when(objectMapperUtil.getObjectFromJson(responseJson, 
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class, 
                DependencyLayer.ORCHESTRATOR))
                .thenThrow(new RuntimeException("JSON deserialization failed"));
        
        // When & Then
        try {
            orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException exception) {
            assertEquals("JSON deserialization failed", exception.getMessage());
        }
        
        // Verify metrics are still recorded in finally block (Line 378)
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong());
    }

    @Test
    public void testGetCalendarAvailability_NullResponse_ShouldHandleGracefully() throws Exception {
        // Given - Testing null response handling
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        
        String requestJson = "{\"calendar\":\"request\"}";
        String responseJson = "{\"calendar\":\"response\"}";
        
        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");
        
        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        
        // URL construction uses actual implementation
        
        when(restConnectorUtil.performCalendarAvailabilityPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        
        when(objectMapperUtil.getObjectFromJson(responseJson, 
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class, 
                DependencyLayer.ORCHESTRATOR))
                .thenReturn(null);
        
        // When
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse result = 
            orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);
        
        // Then - Should return null (Line 376)
        assertNull(result);
        
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong());
    }

    @Test
    public void testGetCalendarAvailability_MetricsRecording_ShouldAlwaysRecordMetrics() throws Exception {
        // Given - Testing finally block metrics recording (Line 378)
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        
        String requestJson = "{\"calendar\":\"metrics\"}";
        String responseJson = "{\"calendar\":\"metrics\"}";
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse expectedResponse = 
            createValidConsolidatedCalendarResponse();
        
        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");
        
        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        
        // URL construction uses actual implementation
        
        when(restConnectorUtil.performCalendarAvailabilityPost(anyString(), any(Map.class), anyString()))
                .thenReturn(responseJson);
        
        when(objectMapperUtil.getObjectFromJson(responseJson, 
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class, 
                DependencyLayer.ORCHESTRATOR))
                .thenReturn(expectedResponse);
        
        // When
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse result = 
            orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);
        
        // Then
        assertNotNull(result);
        
        // Verify metrics are recorded with correct parameters (Line 378)
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong());
    }

    @Test
    public void testGetCalendarAvailability_AllLines_ComprehensiveCoverage() throws Exception {
        // Given - Comprehensive test to ensure all lines are covered
        DetailRequest calendarRequest = createValidDetailRequest();
        String correlationKey = "test-correlation-key";
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("param1", new String[]{"value1"});
        Map<String, String> headers = new HashMap<>();
        headers.put("mmt-auth", "comprehensive-test-token");
        headers.put("other-header", "other-value");
        
        String requestJson = "{\"comprehensive\":\"test\"}";
        String responseJson = "{\"comprehensive\":\"response\"}";
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse expectedResponse = 
            createValidConsolidatedCalendarResponse();
        
        ReflectionTestUtils.setField(orchDetailExecutor, "calendarAvailabilityUrl", "http://test.url/calendar");
        
        when(objectMapperUtil.getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY))
                .thenReturn(requestJson);
        
        // URL construction uses actual implementation
        
        when(restConnectorUtil.performCalendarAvailabilityPost(eq(requestJson), any(Map.class), anyString()))
                .thenReturn(responseJson);
        
        when(objectMapperUtil.getObjectFromJson(responseJson, 
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class, 
                DependencyLayer.ORCHESTRATOR))
                .thenReturn(expectedResponse);
        
        // When
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse result = 
            orchDetailExecutor.getCalendarAvailability(calendarRequest, correlationKey, parameterMap, headers);
        
        // Then
        assertNotNull(result);
        assertEquals(expectedResponse, result);
        
        // Verify all method calls and line coverage
        verify(objectMapperUtil).getJsonFromObject(calendarRequest, DependencyLayer.CLIENTGATEWAY); // Line 362
        verify(restConnectorUtil).performCalendarAvailabilityPost(eq(requestJson), any(Map.class), anyString()); // Line 367
        verify(objectMapperUtil).getObjectFromJson(responseJson, 
                com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse.class, 
                DependencyLayer.ORCHESTRATOR); // Line 368
        verify(metricAspect).addToTime(eq(DependencyLayer.ORCHESTRATOR.name()), eq("calendarAvailability"), anyLong()); // Line 378
        
        // Verify header construction (Lines 356-361)
        verify(restConnectorUtil).performCalendarAvailabilityPost(anyString(), argThat((Map<String, String> headerMap) -> {
            return headerMap.containsKey("Accept-Encoding") && "gzip".equals(headerMap.get("Accept-Encoding")) &&
                   headerMap.containsKey("Content-Type") && "application/json".equals(headerMap.get("Content-Type")) &&
                   headerMap.containsKey("mmt-auth") && "comprehensive-test-token".equals(headerMap.get("mmt-auth"));
        }), anyString());
    }

    // ===================== HELPER METHODS FOR CALENDAR AVAILABILITY TESTS =====================

    private com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse createValidConsolidatedCalendarResponse() {
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse response = 
            new com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse();
        // Set up a valid response with empty error list
        response.setErrorList(new java.util.ArrayList<>());
        return response;
    }

    private com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse createConsolidatedCalendarResponseWithErrors() {
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse response = 
            new com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse();
        
        // Create error list with one error for testing Lines 369-374
        java.util.List<com.gommt.hotels.orchestrator.detail.model.response.common.ErrorResponse> errorList = 
            new java.util.ArrayList<>();
        com.gommt.hotels.orchestrator.detail.model.response.common.ErrorResponse error = 
            new com.gommt.hotels.orchestrator.detail.model.response.common.ErrorResponse();
        error.setCode("CAL_ERROR_001");
        error.setMessage("Calendar error message");
        errorList.add(error);
        
        response.setErrorList(errorList);
        return response;
    }

    private com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse createConsolidatedCalendarResponseWithNullErrors() {
        com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse response = 
            new com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse();
        // Leave errorList as null to test Line 369 false condition
        response.setErrorList(null);
        return response;
    }
}