package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.response.searchwrapper.ListingPagePersonalizationResponsBO;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperResponseBO;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.FileReader;
import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class SearchHotelsExecutorTest {

    @InjectMocks
    SearchHotelsExecutor searchHotelsExecutor;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    @Spy
    private HeadersUtil headersUtil;

    @Spy
    MetricAspect metricAspect;

    @Mock
    private PricingEngineHelper pricingEngineHelper;

    private static final Logger logger = LoggerFactory.getLogger(MetricErrorLogger.class);

    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
        ReflectionTestUtils.setField(searchHotelsExecutor, "searchHotelsUrl", "abc");
        ReflectionTestUtils.setField(searchHotelsExecutor, "metaDataUrl", "abc");
    }

    @Test
    public void searchHotelsTest() throws ClientGatewayException {
        String resp= null;
        try {
            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp =  jo.get("respSearch").toString();
        }catch (Exception e){
            //Do nothing
        }
        Mockito.when(restConnectorUtil.performSearchHotelsPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(resp);
        SearchWrapperResponseBO response = searchHotelsExecutor.searchHotels(new SearchWrapperInputRequest(),new HashMap<>(), new HashMap<String, String>());
        Assert.assertNotNull(response);
    }

    @Test
    public void searchPersonalisedHotelsTest() throws ClientGatewayException {
        String resp= null;
        try {
            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp =  jo.get("respSearch").toString();
        }catch (Exception e){
            logger.error("error occured in getting file", e.getMessage());
        }
        Mockito.when(restConnectorUtil.performSearchHotelsPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(resp);
        ListingPagePersonalizationResponsBO response = searchHotelsExecutor.searchPersonalizedHotels(new SearchWrapperInputRequest(), new HashMap<>(), new HashMap<String, String>(), false);
        Assert.assertNotNull(response);
    }

    @Test
    public void listingMapTest() throws ClientGatewayException {
        String resp= null;
        try {
            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp =  jo.get("respSearch").toString();

        }catch (Exception e){
            //Do Nothing
        }
        Mockito.when(restConnectorUtil.performSearchHotelsPost(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(resp);
        String response = searchHotelsExecutor.listingMapOld(new SearchWrapperInputRequest(), new HashMap<>(), new HashMap<String, String>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testFetchCollectionsOld() throws ClientGatewayException {
        String resp = "test";
        Mockito.when(restConnectorUtil.performFetchCollectionPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        String response = searchHotelsExecutor.fetchCollectionsOld(new SearchWrapperInputRequest(), new HashMap<>(), new HashMap<String, String>());
        Assert.assertNotNull(response);
    }

    @Test
    public void nearByHotelsOldTest() throws ClientGatewayException {
        String resp = null;
        try {
            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp =  jo.get("respSearch").toString();
        }catch (Exception e){
        }
        Mockito.when(restConnectorUtil.performNearByPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        String response = searchHotelsExecutor.nearByHotelsOld(new SearchWrapperInputRequest(), new HashMap<>(), new HashMap<String, String>());
        Assert.assertNotNull(response);
    }

    @Test
    public void nearByHotelsTest() throws ClientGatewayException {
        String resp = null;
        try {
            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp =  jo.get("respSearch").toString();
        }catch (Exception e){
        }
        SearchWrapperResponseBO<SearchWrapperHotelEntity> response;
        Mockito.when(restConnectorUtil.performNearByPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        response = searchHotelsExecutor.nearByHotels(new SearchWrapperInputRequest(),new HashMap<String, String[]>(),new HashMap<>());
        Assert.assertNotNull(response);
        Assert.assertNull(response.getResponseErrors());
    }

    @Test
    public void searchHotelsOldTest() throws ClientGatewayException {
        Mockito.when(restConnectorUtil.performSearchHotelsPost(Mockito.anyString(), Mockito.anyMap(), Mockito.any(), Mockito.anyBoolean())).thenReturn("resp");
        String response = searchHotelsExecutor.searchHotelsOld(new SearchWrapperInputRequest(), new HashMap<>(), new HashMap<>(), false);
        Assert.assertNotNull(response);
        Assert.assertEquals(response, "resp");
    }

    @Test
    public void searchPersonalizedHotelsOldTest() throws ClientGatewayException {
        Mockito.when(restConnectorUtil.performSearchHotelsPost(Mockito.anyString(), Mockito.anyMap(), Mockito.any(), Mockito.anyBoolean())).thenReturn("resp");
        String response = searchHotelsExecutor.searchPersonalizedHotelsOld(new SearchWrapperInputRequest(), new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(response);
        Assert.assertEquals(response, "resp");
    }

    @Test
    public void getMetaDataResponseTest() throws ClientGatewayException {
        Mockito.when(restConnectorUtil.performGetByPass(Mockito.anyMap(),Mockito.anyString())).thenReturn("resp");
        String response = searchHotelsExecutor.getMetaDataResponse("CTDEL", new HashMap<>());
        Assert.assertNotNull(response);
        Assert.assertEquals(response, "resp");
    }

    @Test
    public void landingDiscoveryOldTest() throws ClientGatewayException {
        Mockito.when(restConnectorUtil.performLandingDiscoveryPost(Mockito.anyString(), Mockito.anyMap(), Mockito.any())).thenReturn("resp");
        String response = searchHotelsExecutor.landingDiscoveryOld(new SearchWrapperInputRequest(), new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(response);
        Assert.assertEquals(response, "resp");
    }

}
