package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.request.TotalPricingRequest;
import com.mmt.hotels.clientgateway.request.payLater.PayLaterEligibilityRequest;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.request.FetchLocationsRequestBody;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.response.FetchLocationsResponseBody;
import com.mmt.hotels.model.response.FetchLocationsResult;
import com.mmt.hotels.model.response.PayLaterEligibilityResponse;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.pricing.TotalPricingResponse;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.FileReader;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import org.json.simple.JSONObject;
import org.json.simple.parser.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AvailRoomsExecutorTest {


    @InjectMocks
    AvailRoomsExecutor availRoomsExecutor;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    @Spy
    private HeadersUtil headersUtil;

    @Spy
    private MetricAspect metricAspect;

    private static final Logger logger = LoggerFactory.getLogger(MetricErrorLogger.class);

    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
        ReflectionTestUtils.setField(availRoomsExecutor, "availRoomsUrl", "abc");
        ReflectionTestUtils.setField(availRoomsExecutor, "fetchLocationsUrl", "https://test.example.com/fetchlocations");
    }

    @Test
    public void availRoomsTest() throws ClientGatewayException {
        String resp= null;
        try {
            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp =  jo.get("respAvail").toString();
        }catch (Exception e){
            logger.error("error occured in getting file", e.getMessage());
        }
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        RoomDetailsResponse response = availRoomsExecutor.availRooms(new PriceByHotelsRequestBody(), new HashMap<>(), new HashMap<String, String>());

        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsOld() throws ClientGatewayException {
        String resp = "{\"total\":0}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        String response = availRoomsExecutor.availRoomsOld(new PriceByHotelsRequestBody(), new HashMap<>(), new HashMap<String, String>());
        Assert.assertNotNull(response);
    }
    
    @Test
    public void testupdatedPriceOccuLessOld() throws ClientGatewayException {
        String resp = "availResponse";
        Mockito.when(restConnectorUtil.performUpdatedPriceOccuLessPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        String response = availRoomsExecutor.updatedPriceOccuLessOld(new PriceByHotelsRequestBody(), new HashMap<>(), new HashMap<String, String>());
        Assert.assertNotNull(response);
    }
    
    @Test
    public void getTotalPricingDetailsTest() throws ClientGatewayException {
        String resp= null;
        try {
            Object obj = new JSONParser().parse(new FileReader("src/test/java/com/mmt/hotels/clientgateway/restexecutors/files/response.json"));
            JSONObject jo = (JSONObject) obj;
            resp =  jo.get("respAvail").toString();
        }catch (Exception e){
            logger.error("error occured in getting file", e.getMessage());
        }
        Mockito.when(restConnectorUtil.performTotalPricePost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        TotalPricingResponse response = availRoomsExecutor.getTotalPricingDetails(new TotalPricingRequest(), new HashMap<>(), "123", new HashMap<String, String>());

        Assert.assertNotNull(response);
    }

    @Test
    public void testFetchPayLaterEligibility() throws ClientGatewayException {
        String resp = "{\"amount\":1000, \"eligible\":true}";
        Mockito.when(restConnectorUtil.checkPayLaterEligibilityPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", "mmt-auth123");
        PayLaterEligibilityResponse response = availRoomsExecutor.fetchPayLaterEligibility(new PayLaterEligibilityRequest(), new HashMap<>(),"", headers);
        Assert.assertNotNull(response);
    }

    // Additional test cases for increased code coverage

    @Test
    public void testAvailRoomsWithEmptyHeaders() throws ClientGatewayException {
        String resp = "{\"total\":1}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        RoomDetailsResponse response = availRoomsExecutor.availRooms(new PriceByHotelsRequestBody(), new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsWithNullMmtAuth() throws ClientGatewayException {
        String resp = "{\"total\":1}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", null);
        RoomDetailsResponse response = availRoomsExecutor.availRooms(new PriceByHotelsRequestBody(), new HashMap<>(), headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsWithAkamaiHeaders() throws ClientGatewayException {
        String resp = "{\"total\":1}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("X-Akamai-Edgescape", "test-akamai-data");
        headers.put("OS", "android");
        headers.put("ORG", "B2C");
        headers.put("language", "eng");
        headers.put("user-agent", "test-agent");
        RoomDetailsResponse response = availRoomsExecutor.availRooms(new PriceByHotelsRequestBody(), new HashMap<>(), headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsWithLowerCaseAkamaiHeader() throws ClientGatewayException {
        String resp = "{\"total\":1}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("x-akamai-edgescape", "test-akamai-data-lowercase");
        RoomDetailsResponse response = availRoomsExecutor.availRooms(new PriceByHotelsRequestBody(), new HashMap<>(), headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsOldWithValidResponse() throws ClientGatewayException {
        String resp = "{\"hotels\":[{\"id\":\"123\",\"name\":\"Test Hotel\"}]}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        String response = availRoomsExecutor.availRoomsOld(new PriceByHotelsRequestBody(), new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testUpdatedPriceOccuLessOldWithAuth() throws ClientGatewayException {
        String resp = "updated-price-response";
        Mockito.when(restConnectorUtil.performUpdatedPriceOccuLessPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", "auth-token");
        String response = availRoomsExecutor.updatedPriceOccuLessOld(new PriceByHotelsRequestBody(), new HashMap<>(), headers);
        Assert.assertEquals(resp, response);
    }

    @Test
    public void testUpdatedPriceOccuLessOldWithEmptyAuth() throws ClientGatewayException {
        String resp = "updated-price-response";
        Mockito.when(restConnectorUtil.performUpdatedPriceOccuLessPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", "");
        String response = availRoomsExecutor.updatedPriceOccuLessOld(new PriceByHotelsRequestBody(), new HashMap<>(), headers);
        Assert.assertEquals(resp, response);
    }

    @Test
    public void testGetRoomStaticDetails() {
        Object result = availRoomsExecutor.getRoomStaticDetails();
        Assert.assertNull(result);
    }

    @Test
    public void testGetTotalPricingDetailsWithAuth() throws ClientGatewayException {
        String resp = "{\"totalPrice\":1000}";
        Mockito.when(restConnectorUtil.performTotalPricePost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", "valid-auth");
        TotalPricingResponse response = availRoomsExecutor.getTotalPricingDetails(new TotalPricingRequest(), new HashMap<>(), "123", headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testGetTotalPricingDetailsWithEmptyAuth() throws ClientGatewayException {
        String resp = "{\"totalPrice\":1000}";
        Mockito.when(restConnectorUtil.performTotalPricePost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", "");
        TotalPricingResponse response = availRoomsExecutor.getTotalPricingDetails(new TotalPricingRequest(), new HashMap<>(), "123", headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testFetchPayLaterEligibilityWithEmptyAuth() throws ClientGatewayException {
        String resp = "{\"amount\":500, \"eligible\":false}";
        Mockito.when(restConnectorUtil.checkPayLaterEligibilityPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", "");
        PayLaterEligibilityResponse response = availRoomsExecutor.fetchPayLaterEligibility(new PayLaterEligibilityRequest(), new HashMap<>(),"", headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testFetchLocationsWithValidLanguage() throws Exception {
        String resp = "{\"locations\":[{\"id\":\"123\",\"name\":\"Mumbai\"}]}";
        Mockito.when(restConnectorUtil.fetchLocationsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("language", "eng");
        com.mmt.hotels.model.response.FetchLocationsResponseBody response = availRoomsExecutor.fetchLocations(new com.mmt.hotels.model.request.FetchLocationsRequestBody(), new HashMap<>(), "123", headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testFetchLocationsWithEmptyLanguage() throws Exception {
        String resp = "{\"locations\":[]}";
        Mockito.when(restConnectorUtil.fetchLocationsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("language", "");
        com.mmt.hotels.model.response.FetchLocationsResponseBody response = availRoomsExecutor.fetchLocations(new com.mmt.hotels.model.request.FetchLocationsRequestBody(), new HashMap<>(), "123", headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsWithParameterMap() throws ClientGatewayException {
        String resp = "{\"total\":2}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String[]> parameterMap = new HashMap<>();
        parameterMap.put("test-param", new String[]{"value1", "value2"});
        PriceByHotelsRequestBody requestBody = new PriceByHotelsRequestBody();
        requestBody.setCorrelationKey("test-correlation");
        RoomDetailsResponse response = availRoomsExecutor.availRooms(requestBody, parameterMap, new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testUpdatedPriceOccuLessOldWithParameterMap() throws ClientGatewayException {
        String resp = "price-updated";
        Mockito.when(restConnectorUtil.performUpdatedPriceOccuLessPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String[]> parameterMap = new HashMap<>();
        parameterMap.put("priceParam", new String[]{"100"});
        PriceByHotelsRequestBody requestBody = new PriceByHotelsRequestBody();
        requestBody.setCorrelationKey("price-correlation");
        String response = availRoomsExecutor.updatedPriceOccuLessOld(requestBody, parameterMap, new HashMap<>());
        Assert.assertEquals(resp, response);
    }

    @Test
    public void testGetTotalPricingDetailsWithParameterMap() throws ClientGatewayException {
        String resp = "{\"totalPrice\":2000}";
        Mockito.when(restConnectorUtil.performTotalPricePost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String[]> parameterMap = new HashMap<>();
        parameterMap.put("pricingParam", new String[]{"total"});
        TotalPricingResponse response = availRoomsExecutor.getTotalPricingDetails(new TotalPricingRequest(), parameterMap, "456", new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testFetchPayLaterEligibilityWithParameterMap() throws ClientGatewayException {
        String resp = "{\"amount\":1500, \"eligible\":true}";
        Mockito.when(restConnectorUtil.checkPayLaterEligibilityPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String[]> parameterMap = new HashMap<>();
        parameterMap.put("eligibilityParam", new String[]{"check"});
        PayLaterEligibilityResponse response = availRoomsExecutor.fetchPayLaterEligibility(new PayLaterEligibilityRequest(), parameterMap,"789", new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testFetchLocationsWithParameterMap() throws Exception {
        String resp = "{\"locations\":[{\"id\":\"456\",\"name\":\"Delhi\"}]}";
        Mockito.when(restConnectorUtil.fetchLocationsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String[]> parameterMap = new HashMap<>();
        parameterMap.put("locationParam", new String[]{"city"});
        com.mmt.hotels.model.response.FetchLocationsResponseBody response = availRoomsExecutor.fetchLocations(new com.mmt.hotels.model.request.FetchLocationsRequestBody(), parameterMap, "456", new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsWithComplexHeaders() throws ClientGatewayException {
        String resp = "{\"total\":5}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", "complex-auth-token-123");
        headers.put("X-Akamai-Edgescape", "complex-akamai-data");
        headers.put("x-akamai-edgescape", "lowercase-akamai-data");
        headers.put("OS", "ios");
        headers.put("ORG", "CORP");
        headers.put("language", "hindi");
        headers.put("user-agent", "TestAgent/1.0");
        RoomDetailsResponse response = availRoomsExecutor.availRooms(new PriceByHotelsRequestBody(), new HashMap<>(), headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testUpdatedPriceOccuLessOldWithNullAuth() throws ClientGatewayException {
        String resp = "price-response-null-auth";
        Mockito.when(restConnectorUtil.performUpdatedPriceOccuLessPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", null);
        String response = availRoomsExecutor.updatedPriceOccuLessOld(new PriceByHotelsRequestBody(), new HashMap<>(), headers);
        Assert.assertEquals(resp, response);
    }

    @Test
    public void testGetTotalPricingDetailsWithNullAuth() throws ClientGatewayException {
        String resp = "{\"totalPrice\":3000}";
        Mockito.when(restConnectorUtil.performTotalPricePost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", null);
        TotalPricingResponse response = availRoomsExecutor.getTotalPricingDetails(new TotalPricingRequest(), new HashMap<>(), "999", headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testFetchPayLaterEligibilityWithNullAuth() throws ClientGatewayException {
        String resp = "{\"amount\":2000, \"eligible\":false}";
        Mockito.when(restConnectorUtil.checkPayLaterEligibilityPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", null);
        PayLaterEligibilityResponse response = availRoomsExecutor.fetchPayLaterEligibility(new PayLaterEligibilityRequest(), new HashMap<>(),"", headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testFetchLocationsWithNullLanguage() throws Exception {
        String resp = "{\"locations\":[{\"id\":\"789\",\"name\":\"Bangalore\"}]}";
        Mockito.when(restConnectorUtil.fetchLocationsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("language", null);
        com.mmt.hotels.model.response.FetchLocationsResponseBody response = availRoomsExecutor.fetchLocations(new com.mmt.hotels.model.request.FetchLocationsRequestBody(), new HashMap<>(), "789", headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsWithCorrelationKey() throws ClientGatewayException {
        String resp = "{\"total\":10}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        PriceByHotelsRequestBody requestBody = new PriceByHotelsRequestBody();
        requestBody.setCorrelationKey("unique-correlation-key-123");
        RoomDetailsResponse response = availRoomsExecutor.availRooms(requestBody, new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testUpdatedPriceOccuLessOldWithCorrelationKey() throws ClientGatewayException {
        String resp = "price-with-correlation";
        Mockito.when(restConnectorUtil.performUpdatedPriceOccuLessPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        PriceByHotelsRequestBody requestBody = new PriceByHotelsRequestBody();
        requestBody.setCorrelationKey("price-correlation-456");
        String response = availRoomsExecutor.updatedPriceOccuLessOld(requestBody, new HashMap<>(), new HashMap<>());
        Assert.assertEquals(resp, response);
    }

    // More comprehensive test cases for maximum code coverage

    @Test
    public void testAvailRoomsWithBothAkamaiHeaders() throws ClientGatewayException {
        String resp = "{\"total\":1}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("X-Akamai-Edgescape", "upper-case-value");
        headers.put("x-akamai-edgescape", "lower-case-value");
        RoomDetailsResponse response = availRoomsExecutor.availRooms(new PriceByHotelsRequestBody(), new HashMap<>(), headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsWithEmptyAkamaiHeaders() throws ClientGatewayException {
        String resp = "{\"total\":1}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("X-Akamai-Edgescape", "");
        headers.put("x-akamai-edgescape", "");
        RoomDetailsResponse response = availRoomsExecutor.availRooms(new PriceByHotelsRequestBody(), new HashMap<>(), headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsWithAllNullHeaders() throws ClientGatewayException {
        String resp = "{\"total\":1}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", null);
        headers.put("X-Akamai-Edgescape", null);
        headers.put("OS", null);
        headers.put("ORG", null);
        headers.put("language", null);
        headers.put("user-agent", null);
        RoomDetailsResponse response = availRoomsExecutor.availRooms(new PriceByHotelsRequestBody(), new HashMap<>(), headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsWithEmptyStringHeaders() throws ClientGatewayException {
        String resp = "{\"total\":1}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", "");
        headers.put("X-Akamai-Edgescape", "");
        headers.put("OS", "");
        headers.put("ORG", "");
        headers.put("language", "");
        headers.put("user-agent", "");
        RoomDetailsResponse response = availRoomsExecutor.availRooms(new PriceByHotelsRequestBody(), new HashMap<>(), headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsWithSpecialCharactersInHeaders() throws ClientGatewayException {
        String resp = "{\"total\":1}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", "auth@#$%^&*()");
        headers.put("X-Akamai-Edgescape", "city=NEW DELHI,country=IN,continent=AS");
        headers.put("OS", "android_10.0");
        headers.put("ORG", "B2C-CORP");
        headers.put("language", "en-US");
        headers.put("user-agent", "Mozilla/5.0 (compatible; TestBot/1.0; +http://test.com)");
        RoomDetailsResponse response = availRoomsExecutor.availRooms(new PriceByHotelsRequestBody(), new HashMap<>(), headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsWithLargeParameterMap() throws ClientGatewayException {
        String resp = "{\"total\":1}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String[]> parameterMap = new HashMap<>();
        for(int i = 0; i < 20; i++) {
            parameterMap.put("param" + i, new String[]{"value" + i, "altValue" + i});
        }
        PriceByHotelsRequestBody requestBody = new PriceByHotelsRequestBody();
        requestBody.setCorrelationKey("large-param-test");
        RoomDetailsResponse response = availRoomsExecutor.availRooms(requestBody, parameterMap, new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testUpdatedPriceOccuLessOldWithComplexParameterMap() throws ClientGatewayException {
        String resp = "complex-price-response";
        Mockito.when(restConnectorUtil.performUpdatedPriceOccuLessPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String[]> parameterMap = new HashMap<>();
        parameterMap.put("hotelId", new String[]{"12345"});
        parameterMap.put("roomType", new String[]{"deluxe", "standard"});
        parameterMap.put("checkIn", new String[]{"2024-01-01"});
        parameterMap.put("checkOut", new String[]{"2024-01-05"});
        parameterMap.put("adults", new String[]{"2"});
        parameterMap.put("children", new String[]{"1"});
        PriceByHotelsRequestBody requestBody = new PriceByHotelsRequestBody();
        requestBody.setCorrelationKey("complex-price-correlation");
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", "complex-auth");
        String response = availRoomsExecutor.updatedPriceOccuLessOld(requestBody, parameterMap, headers);
        Assert.assertEquals(resp, response);
    }

    @Test
    public void testGetTotalPricingDetailsWithComplexCorrelationKey() throws ClientGatewayException {
        String resp = "{\"totalPrice\":5000, \"taxes\":500, \"fees\":100}";
        Mockito.when(restConnectorUtil.performTotalPricePost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String[]> parameterMap = new HashMap<>();
        parameterMap.put("currency", new String[]{"INR"});
        parameterMap.put("locale", new String[]{"en_IN"});
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", "pricing-auth-token");
        TotalPricingResponse response = availRoomsExecutor.getTotalPricingDetails(new TotalPricingRequest(), parameterMap, "complex-correlation-key-12345", headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testFetchPayLaterEligibilityWithComplexCorrelationKey() throws ClientGatewayException {
        String resp = "{\"amount\":7500, \"eligible\":true, \"reason\":\"Eligible for pay later\"}";
        Mockito.when(restConnectorUtil.checkPayLaterEligibilityPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String[]> parameterMap = new HashMap<>();
        parameterMap.put("userId", new String[]{"user123"});
        parameterMap.put("bookingValue", new String[]{"7500"});
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", "paylater-auth");
        PayLaterEligibilityResponse response = availRoomsExecutor.fetchPayLaterEligibility(new PayLaterEligibilityRequest(), parameterMap, "paylater-correlation-67890", headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testFetchLocationsWithComplexHeaders() throws Exception {
        String resp = "{\"locations\":[{\"id\":\"987\",\"name\":\"Chennai\",\"type\":\"city\"}]}";
        Mockito.when(restConnectorUtil.fetchLocationsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String[]> parameterMap = new HashMap<>();
        parameterMap.put("query", new String[]{"chennai"});
        parameterMap.put("limit", new String[]{"10"});
        parameterMap.put("offset", new String[]{"0"});
        Map<String,String> headers = new HashMap<>();
        headers.put("language", "tamil");
        headers.put("region", "IN");
        headers.put("device", "mobile");
        com.mmt.hotels.model.response.FetchLocationsResponseBody response = availRoomsExecutor.fetchLocations(new com.mmt.hotels.model.request.FetchLocationsRequestBody(), parameterMap, "locations-correlation-abc123", headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsOldWithComplexJsonResponse() throws ClientGatewayException {
        String resp = "{\"hotels\":[{\"id\":\"111\",\"name\":\"Luxury Hotel\",\"rating\":5,\"price\":10000,\"amenities\":[\"wifi\",\"pool\",\"spa\"]}],\"total\":1,\"currency\":\"INR\"}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String[]> parameterMap = new HashMap<>();
        parameterMap.put("city", new String[]{"mumbai"});
        parameterMap.put("rating", new String[]{"4,5"});
        PriceByHotelsRequestBody requestBody = new PriceByHotelsRequestBody();
        requestBody.setCorrelationKey("complex-old-correlation");
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", "old-api-auth");
        String response = availRoomsExecutor.availRoomsOld(requestBody, parameterMap, headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsWithMinimalValidHeaders() throws ClientGatewayException {
        String resp = "{\"total\":1}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", "minimal-auth");
        // Only auth header, no other headers
        RoomDetailsResponse response = availRoomsExecutor.availRooms(new PriceByHotelsRequestBody(), new HashMap<>(), headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testUpdatedPriceOccuLessOldWithLongCorrelationKey() throws ClientGatewayException {
        String resp = "updated-price-long-correlation";
        Mockito.when(restConnectorUtil.performUpdatedPriceOccuLessPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        PriceByHotelsRequestBody requestBody = new PriceByHotelsRequestBody();
        requestBody.setCorrelationKey("very-long-correlation-key-with-multiple-segments-and-numbers-12345-67890-abcdef");
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", "long-correlation-auth");
        String response = availRoomsExecutor.updatedPriceOccuLessOld(requestBody, new HashMap<>(), headers);
        Assert.assertEquals(resp, response);
    }

    @Test
    public void testGetTotalPricingDetailsWithMultipleParameterValues() throws ClientGatewayException {
        String resp = "{\"totalPrice\":15000, \"breakdown\":{\"base\":12000,\"taxes\":2000,\"fees\":1000}}";
        Mockito.when(restConnectorUtil.performTotalPricePost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String[]> parameterMap = new HashMap<>();
        parameterMap.put("roomIds", new String[]{"room1", "room2", "room3"});
        parameterMap.put("dates", new String[]{"2024-01-01", "2024-01-02", "2024-01-03"});
        parameterMap.put("guests", new String[]{"adult1", "adult2", "child1"});
        TotalPricingResponse response = availRoomsExecutor.getTotalPricingDetails(new TotalPricingRequest(), parameterMap, "multi-param-correlation", new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testFetchPayLaterEligibilityWithEmptyParameterMap() throws ClientGatewayException {
        String resp = "{\"amount\":0, \"eligible\":false, \"reason\":\"No amount specified\"}";
        Mockito.when(restConnectorUtil.checkPayLaterEligibilityPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        PayLaterEligibilityResponse response = availRoomsExecutor.fetchPayLaterEligibility(new PayLaterEligibilityRequest(), new HashMap<>(), "", new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testFetchLocationsWithMultipleLanguageFormats() throws Exception {
        String resp = "{\"locations\":[{\"id\":\"555\",\"name\":\"कोलकाता\",\"englishName\":\"Kolkata\"}]}";
        Mockito.when(restConnectorUtil.fetchLocationsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("language", "hi-IN");
        com.mmt.hotels.model.response.FetchLocationsResponseBody response = availRoomsExecutor.fetchLocations(new com.mmt.hotels.model.request.FetchLocationsRequestBody(), new HashMap<>(), "multilang-correlation", headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsWithNumericStringHeaders() throws ClientGatewayException {
        String resp = "{\"total\":1}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", "123456789");
        headers.put("X-Akamai-Edgescape", "12345");
        headers.put("OS", "1");
        headers.put("ORG", "2");
        headers.put("language", "1033");
        headers.put("user-agent", "Agent1.0");
        RoomDetailsResponse response = availRoomsExecutor.availRooms(new PriceByHotelsRequestBody(), new HashMap<>(), headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsWithVeryLongHeaderValues() throws ClientGatewayException {
        String resp = "{\"total\":1}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        Map<String,String> headers = new HashMap<>();
        headers.put("mmt-auth", "very-long-authentication-token-with-many-characters-and-segments-12345-67890-abcdef-ghijkl-mnopqr-stuvwx-yz1234");
        headers.put("X-Akamai-Edgescape", "country=INDIA,region=MAHARASHTRA,city=MUMBAI,latitude=19.0760,longitude=72.8777,timezone=IST,continent=ASIA");
        headers.put("user-agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Very Long User Agent String");
        RoomDetailsResponse response = availRoomsExecutor.availRooms(new PriceByHotelsRequestBody(), new HashMap<>(), headers);
        Assert.assertNotNull(response);
    }

    @Test
    public void testUpdatedPriceOccuLessOldWithZeroCorrelationKey() throws ClientGatewayException {
        String resp = "zero-correlation-response";
        Mockito.when(restConnectorUtil.performUpdatedPriceOccuLessPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        PriceByHotelsRequestBody requestBody = new PriceByHotelsRequestBody();
        requestBody.setCorrelationKey("0");
        String response = availRoomsExecutor.updatedPriceOccuLessOld(requestBody, new HashMap<>(), new HashMap<>());
        Assert.assertEquals(resp, response);
    }

    @Test
    public void testGetTotalPricingDetailsWithZeroCorrelationKey() throws ClientGatewayException {
        String resp = "{\"totalPrice\":0}";
        Mockito.when(restConnectorUtil.performTotalPricePost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        TotalPricingResponse response = availRoomsExecutor.getTotalPricingDetails(new TotalPricingRequest(), new HashMap<>(), "0", new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testFetchPayLaterEligibilityWithZeroCorrelationKey() throws ClientGatewayException {
        String resp = "{\"amount\":0, \"eligible\":true}";
        Mockito.when(restConnectorUtil.checkPayLaterEligibilityPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        PayLaterEligibilityResponse response = availRoomsExecutor.fetchPayLaterEligibility(new PayLaterEligibilityRequest(), new HashMap<>(), "0", new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testFetchLocationsWithZeroCorrelationKey() throws Exception {
        String resp = "{\"locations\":[]}";
        Mockito.when(restConnectorUtil.fetchLocationsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        com.mmt.hotels.model.response.FetchLocationsResponseBody response = availRoomsExecutor.fetchLocations(new com.mmt.hotels.model.request.FetchLocationsRequestBody(), new HashMap<>(), "0", new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsWithComplexJsonResponseStructure() throws ClientGatewayException {
        String resp = "{\"total\":3,\"hotels\":[{\"id\":\"1\",\"name\":\"Hotel A\",\"rooms\":[{\"type\":\"deluxe\",\"price\":5000}]},{\"id\":\"2\",\"name\":\"Hotel B\",\"rooms\":[{\"type\":\"standard\",\"price\":3000}]}],\"filters\":{\"priceRange\":[1000,10000],\"amenities\":[\"wifi\",\"pool\"]}}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        RoomDetailsResponse response = availRoomsExecutor.availRooms(new PriceByHotelsRequestBody(), new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsOldWithEmptyJsonResponse() throws ClientGatewayException {
        String resp = "{}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        String response = availRoomsExecutor.availRoomsOld(new PriceByHotelsRequestBody(), new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testUpdatedPriceOccuLessOldWithEmptyResponse() throws ClientGatewayException {
        String resp = "";
        Mockito.when(restConnectorUtil.performUpdatedPriceOccuLessPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        String response = availRoomsExecutor.updatedPriceOccuLessOld(new PriceByHotelsRequestBody(), new HashMap<>(), new HashMap<>());
        Assert.assertEquals(resp, response);
    }

    @Test
    public void testGetTotalPricingDetailsWithEmptyJsonResponse() throws ClientGatewayException {
        String resp = "{}";
        Mockito.when(restConnectorUtil.performTotalPricePost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        TotalPricingResponse response = availRoomsExecutor.getTotalPricingDetails(new TotalPricingRequest(), new HashMap<>(), "empty-response-test", new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testFetchPayLaterEligibilityWithEmptyJsonResponse() throws ClientGatewayException {
        String resp = "{}";
        Mockito.when(restConnectorUtil.checkPayLaterEligibilityPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        PayLaterEligibilityResponse response = availRoomsExecutor.fetchPayLaterEligibility(new PayLaterEligibilityRequest(), new HashMap<>(), "empty-response-test", new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testFetchLocationsWithEmptyJsonResponse() throws Exception {
        String resp = "{}";
        Mockito.when(restConnectorUtil.fetchLocationsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);
        com.mmt.hotels.model.response.FetchLocationsResponseBody response = availRoomsExecutor.fetchLocations(new com.mmt.hotels.model.request.FetchLocationsRequestBody(), new HashMap<>(), "empty-response-test", new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test
    public void testAvailRoomsWithMetricAspectTiming() throws ClientGatewayException {
        String resp = "{\"total\":1}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);

        long beforeTime = System.currentTimeMillis();
        RoomDetailsResponse response = availRoomsExecutor.availRooms(new PriceByHotelsRequestBody(), new HashMap<>(), new HashMap<>());
        long afterTime = System.currentTimeMillis();

        Assert.assertNotNull(response);
        // Verify that metricAspect.addToTime was called with reasonable timing values
        Mockito.verify(metricAspect).addToTime(
                Mockito.eq("ORCHESTRATOR"),
                Mockito.eq("availrooms/availPrice"),
                Mockito.longThat(timing -> timing >= 0 && timing <= (afterTime - beforeTime + 1000))
        );
    }

    @Test
    public void testUpdatedPriceOccuLessOldWithSrcRequestHeader() throws ClientGatewayException {
        String resp = "price-response";
        Mockito.when(restConnectorUtil.performUpdatedPriceOccuLessPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);

        String response = availRoomsExecutor.updatedPriceOccuLessOld(new PriceByHotelsRequestBody(), new HashMap<>(), new HashMap<>());

        Assert.assertEquals(resp, response);
        // Verify that srcRequest header is set to ClientGateway
        Mockito.verify(restConnectorUtil).performUpdatedPriceOccuLessPost(
                Mockito.any(),
                Mockito.argThat(headerMap ->
                        headerMap.containsKey("srcRequest") &&
                        headerMap.get("srcRequest").equals("ClientGateway")
                ),
                Mockito.any()
        );
    }

    @Test
    public void testGetTotalPricingDetailsWithByPassUrls() throws ClientGatewayException {
        String resp = "{\"totalPrice\":1000}";
        Mockito.when(restConnectorUtil.performTotalPricePost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);

        // Test with empty parameter map to ensure ByPassUrls.DESTINATION_GET_TOTAL_PRICING_URL is used
        TotalPricingResponse response = availRoomsExecutor.getTotalPricingDetails(new TotalPricingRequest(), new HashMap<>(), "test-correlation", new HashMap<>());

        Assert.assertNotNull(response);
        // Verify that the method was called (URL construction logic was executed)
        Mockito.verify(restConnectorUtil).performTotalPricePost(Mockito.any(), Mockito.any(), Mockito.any());
    }

    @Test
    public void testFetchLocationsWithLanguageHeaderMapping() throws Exception {
        String resp = "{\"locations\":[]}";
        Mockito.when(restConnectorUtil.fetchLocationsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);

        Map<String,String> headers = new HashMap<>();
        headers.put("language", "english");

        com.mmt.hotels.model.response.FetchLocationsResponseBody response = availRoomsExecutor.fetchLocations(new com.mmt.hotels.model.request.FetchLocationsRequestBody(), new HashMap<>(), "test", headers);

        Assert.assertNotNull(response);
        // Verify that language header is properly mapped
        Mockito.verify(restConnectorUtil).fetchLocationsPost(
                Mockito.any(),
                Mockito.argThat(headerMap ->
                        headerMap.containsKey("language") &&
                        headerMap.get("language").equals("english")
                ),
                Mockito.any()
        );
    }

    @Test
    public void testAvailRoomsWithDebugLogging() throws ClientGatewayException {
        String resp = "{\"total\":1}";
        Mockito.when(restConnectorUtil.performAvailRoomsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);

        PriceByHotelsRequestBody requestBody = new PriceByHotelsRequestBody();
        requestBody.setCorrelationKey("debug-test-correlation");

        RoomDetailsResponse response = availRoomsExecutor.availRooms(requestBody, new HashMap<>(), new HashMap<>());

        Assert.assertNotNull(response);
        // The debug logging is triggered (logger.debug calls), verifying execution path
        Mockito.verify(objectMapperUtil).getJsonFromObject(Mockito.eq(requestBody), Mockito.any());
    }

    @Test
    public void testGetTotalPricingDetailsWithDebugLogging() throws ClientGatewayException {
        String resp = "{\"totalPrice\":1000}";
        Mockito.when(restConnectorUtil.performTotalPricePost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);

        TotalPricingRequest request = new TotalPricingRequest();

        TotalPricingResponse response = availRoomsExecutor.getTotalPricingDetails(request, new HashMap<>(), "debug-correlation", new HashMap<>());

        Assert.assertNotNull(response);
        // Verify debug logging path is executed
        Mockito.verify(objectMapperUtil).getJsonFromObject(Mockito.eq(request), Mockito.any());
    }

    @Test
    public void testFetchPayLaterEligibilityWithDebugLogging() throws ClientGatewayException {
        String resp = "{\"amount\":1000}";
        Mockito.when(restConnectorUtil.checkPayLaterEligibilityPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);

        PayLaterEligibilityRequest request = new PayLaterEligibilityRequest();

        PayLaterEligibilityResponse response = availRoomsExecutor.fetchPayLaterEligibility(request, new HashMap<>(), "debug-correlation", new HashMap<>());

        Assert.assertNotNull(response);
        // Verify debug logging path is executed
        Mockito.verify(objectMapperUtil).getJsonFromObject(Mockito.eq(request), Mockito.any());
    }

    @Test
    public void testFetchLocationsWithDebugLogging() throws Exception {
        String resp = "{\"locations\":[]}";
        Mockito.when(restConnectorUtil.fetchLocationsPost(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(resp);

        com.mmt.hotels.model.request.FetchLocationsRequestBody request = new com.mmt.hotels.model.request.FetchLocationsRequestBody();

        com.mmt.hotels.model.response.FetchLocationsResponseBody response = availRoomsExecutor.fetchLocations(request, new HashMap<>(), "debug-correlation", new HashMap<>());

        Assert.assertNotNull(response);
        // Verify debug logging path is executed
        Mockito.verify(objectMapperUtil).getJsonFromObject(Mockito.eq(request), Mockito.any());
    }

    @Test
    public void should_FetchLocations_When_ValidRequestProvided() throws Exception {
        // Arrange
        FetchLocationsRequestBody requestBody = createFetchLocationsRequestBody();
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("testParam", new String[]{"value1", "value2"});
        String correlationKey = "test-correlation-key-123";
        Map<String, String> httpHeaderMap = createHttpHeaderMap();

        String mockResponseJson = createValidFetchLocationsResponseJson();
        when(restConnectorUtil.fetchLocationsPost(anyString(), Mockito.<Map<String,String>>any(), anyString()))
                .thenReturn(mockResponseJson);

        // Act
        FetchLocationsResponseBody response = availRoomsExecutor.fetchLocations(
                requestBody, parameterMap, correlationKey, httpHeaderMap);

        // Assert
        assertNotNull("Response should not be null", response);
        assertNotNull("Response should have results", response.getResult());
        assertEquals("Response should have 2 results", 2, response.getResult().size());
        assertEquals("Correlation key should match", correlationKey, response.getCorrelationKey());

        FetchLocationsResult firstResult = response.getResult().get(0);
        assertEquals("First location name should match", "Mumbai", firstResult.getName());
        }

    @Test
    public void should_ThrowErrorResponseFromDownstreamException_When_ResponseContainsErrors() throws Exception {
        // Arrange
        FetchLocationsRequestBody requestBody = createFetchLocationsRequestBody();
        Map<String, String[]> parameterMap = new HashMap<>();
        String correlationKey = "test-correlation-key-123";
        Map<String, String> httpHeaderMap = createHttpHeaderMap();

        String mockResponseWithErrors = createFetchLocationsResponseWithErrors();
        when(restConnectorUtil.fetchLocationsPost(anyString(), Mockito.<Map<String,String>>any(), anyString()))
                .thenReturn(mockResponseWithErrors);

        // Act & Assert
        try {
            availRoomsExecutor.fetchLocations(requestBody, parameterMap, correlationKey, httpHeaderMap);
            fail("Should have thrown ErrorResponseFromDownstreamException");
        } catch (ErrorResponseFromDownstreamException e) {
            assertEquals("Error type should be DOWNSTREAM", ErrorType.DOWNSTREAM, e.getErrorType());
            assertEquals("Dependency layer should be ORCHESTRATOR", DependencyLayer.ORCHESTRATOR, e.getDependencyLayer());
            assertEquals("Error code should match", "LOC_001", e.getCode());
            assertEquals("Error message should match", "Location not found", e.getMessage());
        }
    }

    @Test
    public void should_ThrowRestConnectorException_When_RestCallFails() throws Exception {
        // Arrange
        FetchLocationsRequestBody requestBody = createFetchLocationsRequestBody();
        Map<String, String[]> parameterMap = new HashMap<>();
        String correlationKey = "test-correlation-key-123";
        Map<String, String> httpHeaderMap = createHttpHeaderMap();

        when(restConnectorUtil.fetchLocationsPost(anyString(), Mockito.<Map<String,String>>any(), anyString()))
                .thenThrow(new RestConnectorException(DependencyLayer.ORCHESTRATOR, ErrorType.CONNECTIVITY, "CONN_001", "Connection timeout"));

        // Act & Assert
        try {
            availRoomsExecutor.fetchLocations(requestBody, parameterMap, correlationKey, httpHeaderMap);
            fail("Should have thrown RestConnectorException");
        } catch (RestConnectorException e) {
            assertEquals("Exception message should match", "Connection timeout", e.getMessage());
            assertEquals("Error code should match", "CONN_001", e.getCode());
            assertEquals("Dependency layer should match", DependencyLayer.ORCHESTRATOR, e.getDependencyLayer());
        }
    }

    @Test
    public void should_ThrowJsonParseException_When_InvalidJsonResponse() throws Exception {
        // Arrange
        FetchLocationsRequestBody requestBody = createFetchLocationsRequestBody();
        Map<String, String[]> parameterMap = new HashMap<>();
        String correlationKey = "test-correlation-key-123";
        Map<String, String> httpHeaderMap = createHttpHeaderMap();

        when(restConnectorUtil.fetchLocationsPost(anyString(), Mockito.<Map<String,String>>any(), anyString()))
                .thenReturn("invalid-json-response");

        // Act & Assert
        try {
            availRoomsExecutor.fetchLocations(requestBody, parameterMap, correlationKey, httpHeaderMap);
            fail("Should have thrown JsonParseException");
        } catch (Exception e) {
            // The actual implementation might wrap this in a different exception
            assertTrue("Should be a parsing related exception",
                    e instanceof JsonParseException ||
                    e.getCause() instanceof com.fasterxml.jackson.core.JsonParseException);
        }
    }

    @Test
    public void should_HandleNullResponse_When_EmptyResponseFromService() throws Exception {
        // Arrange
        FetchLocationsRequestBody requestBody = createFetchLocationsRequestBody();
        Map<String, String[]> parameterMap = new HashMap<>();
        String correlationKey = "test-correlation-key-123";
        Map<String, String> httpHeaderMap = createHttpHeaderMap();

        when(restConnectorUtil.fetchLocationsPost(anyString(), Mockito.<Map<String,String>>any(), anyString()))
                .thenReturn("null");

        // Act
        FetchLocationsResponseBody response = availRoomsExecutor.fetchLocations(
                requestBody, parameterMap, correlationKey, httpHeaderMap);

        // Assert
        assertNull("Response should be null when service returns null", response);
    }

    @Test
    public void should_HandleEmptyParameterMap_When_NoParametersProvided() throws Exception {
        // Arrange
        FetchLocationsRequestBody requestBody = createFetchLocationsRequestBody();
        Map<String, String[]> parameterMap = new HashMap<>(); // Empty parameter map
        String correlationKey = "test-correlation-key-123";
        Map<String, String> httpHeaderMap = createHttpHeaderMap();

        String mockResponseJson = createValidFetchLocationsResponseJson();
        when(restConnectorUtil.fetchLocationsPost(anyString(), Mockito.<Map<String,String>>any(), anyString()))
                .thenReturn(mockResponseJson);

        // Act
        FetchLocationsResponseBody response = availRoomsExecutor.fetchLocations(
                requestBody, parameterMap, correlationKey, httpHeaderMap);

        // Assert
        assertNotNull("Response should not be null", response);
        assertEquals("Correlation key should match", correlationKey, response.getCorrelationKey());
    }

    @Test
    public void should_HandleNullLanguageHeader_When_LanguageNotProvided() throws Exception {
        // Arrange
        FetchLocationsRequestBody requestBody = createFetchLocationsRequestBody();
        Map<String, String[]> parameterMap = new HashMap<>();
        String correlationKey = "test-correlation-key-123";
        Map<String, String> httpHeaderMap = new HashMap<>(); // No language header

        String mockResponseJson = createValidFetchLocationsResponseJson();
        when(restConnectorUtil.fetchLocationsPost(anyString(), Mockito.<Map<String,String>>any(), anyString()))
                .thenReturn(mockResponseJson);

        // Act
        FetchLocationsResponseBody response = availRoomsExecutor.fetchLocations(
                requestBody, parameterMap, correlationKey, httpHeaderMap);

        // Assert
        assertNotNull("Response should not be null", response);
        assertEquals("Correlation key should match", correlationKey, response.getCorrelationKey());
    }

    // Helper methods for test data creation
    private FetchLocationsRequestBody createFetchLocationsRequestBody() {
        FetchLocationsRequestBody requestBody = new FetchLocationsRequestBody();
        requestBody.setCorrelationKey("test-correlation-key-123");
        requestBody.setPostalCodes(Arrays.asList("400001", "110001"));
        return requestBody;
    }

    private Map<String, String> createHttpHeaderMap() {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Accept-Language", "en-US");
        headerMap.put("User-Agent", "Hotels-ClientGateway-Test");
        return headerMap;
    }

    private String createValidFetchLocationsResponseJson() {
        return "{\n" +
                "  \"correlationKey\": \"test-correlation-key-123\",\n" +
                "  \"result\": [\n" +
                "    {\n" +
                "      \"id\": \"1\",\n" +
                "      \"name\": \"Mumbai\",\n" +
                "      \"postalCode\": \"400001\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"id\": \"2\",\n" +
                "      \"name\": \"Delhi\",\n" +
                "      \"postalCode\": \"110001\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"responseErrors\": null\n" +
                "}";
    }

    private String createFetchLocationsResponseWithErrors() {
        return "{\n" +
                "  \"correlationKey\": \"test-correlation-key-123\",\n" +
                "  \"result\": [],\n" +
                "  \"responseErrors\": {\n" +
                "    \"errorList\": [\n" +
                "      {\n" +
                "        \"errorCode\": \"LOC_001\",\n" +
                "        \"errorMessage\": \"Location not found\"\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "}";
    }
}
