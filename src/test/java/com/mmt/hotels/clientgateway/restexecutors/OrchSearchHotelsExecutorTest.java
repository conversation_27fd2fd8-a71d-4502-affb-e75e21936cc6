package com.mmt.hotels.clientgateway.restexecutors;


import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gommt.hotels.orchestrator.model.request.listing.ListingRequest;
import com.gommt.hotels.orchestrator.model.response.listing.ErrorResponse;
import com.gommt.hotels.orchestrator.model.response.listing.ListingResponse;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrchSearchHotelsExecutorTest {

    @InjectMocks
    OrchSearchHotelsExecutor orchSearchHotelsExecutor;

    @Mock
    MetricAspect metricAspect;

    @Mock
    ObjectMapperUtil objectMapperUtil;

    @Mock
    RestConnectorUtil restConnectorUtil;

    private ObjectMapper mapper;


    @Before
    public void init() {
        mapper = new ObjectMapper();
        mapper.writerWithView(PIIView.External.class);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
    }


    @Test
    public void testClientGatewayException() throws ClientGatewayException {
        ListingRequest listingRequest = new ListingRequest();
        ListingResponse listingResponse = new ListingResponse();
        when(objectMapperUtil.getObjectFromJson(Mockito.any(), eq(ListingResponse.class), eq(DependencyLayer.ORCHESTRATOR_NEW))).thenReturn(listingResponse);
        orchSearchHotelsExecutor.searchHotels(listingRequest, new HashMap<>(), new HashMap<>());

        listingResponse.setError(new ErrorResponse());
        when(objectMapperUtil.getObjectFromJson(Mockito.any(), eq(ListingResponse.class), eq(DependencyLayer.ORCHESTRATOR_NEW))).thenReturn(listingResponse);

        // Act & Assert
        ClientGatewayException exception = assertThrows(ClientGatewayException.class, () -> orchSearchHotelsExecutor.searchHotels(listingRequest, new HashMap<>(), new HashMap<>()));
        Assert.assertNotNull(exception);
    }
}