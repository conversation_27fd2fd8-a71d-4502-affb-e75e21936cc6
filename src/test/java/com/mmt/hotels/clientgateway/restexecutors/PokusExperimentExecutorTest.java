package com.mmt.hotels.clientgateway.restexecutors;

import java.util.HashMap;
import java.util.Map;

import com.mmt.hotels.clientgateway.util.MetricAspect;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.thirdparty.request.PokusExperimentRequest;
import com.mmt.hotels.clientgateway.util.HeadersUtil;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;

@RunWith(MockitoJUnitRunner.class)
public class PokusExperimentExecutorTest {

	@InjectMocks
	PokusExperimentExecutor pokusExperimentExecutor;
	
	@Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;
    
    @Spy
    private HeadersUtil headersUtil;

    @Mock
    MetricAspect metricAspect;
    
    @Before
    public void init(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", mapper);
        ReflectionTestUtils.setField(pokusExperimentExecutor, "pokusUrl", "abc");
        Mockito.doNothing().when(metricAspect).addToTime(Mockito.any(),Mockito.any(),Mockito.anyLong());
    }
    
    @Test(expected = ClientGatewayException.class)
    public void getPokusExperimentResponseTest() throws ClientGatewayException {
    	Map<String, String> map = new HashMap<String, String>();
    	map.put("test", "test");
    	map.put("region", "test");
    	map.put("currency", "test");
    	map.put("language", "test");
        map.put("entity-name", "test");
        map.put("user-country", "test");
    	pokusExperimentExecutor.getPokusExperimentResponse(new PokusExperimentRequest(), map, "test");
    	
    }
}
