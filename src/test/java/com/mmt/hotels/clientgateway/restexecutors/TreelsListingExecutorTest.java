package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.request.FilterSearchMetaDataResponse;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.response.searchwrapper.TreelsListingResponseBO;
import org.apache.commons.collections.MapUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class TreelsListingExecutorTest {
    @InjectMocks
    private TreelsListingExecutor treelsListingExecutor;

    @Mock
    private MetricAspect metricAspect;

    @Mock
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    @Test
    public void searchTest() throws ClientGatewayException {
        TreelsListingResponseBO treelsListingResponseBO = new TreelsListingResponseBO();
        treelsListingResponseBO.setTreelsResponse(new com.mmt.hotels.model.response.searchwrapper.TreelsResponse());
        treelsListingResponseBO.getTreelsResponse().setListingProducts(new ArrayList<>());
        treelsListingResponseBO.getTreelsResponse().getListingProducts().add(new com.mmt.hotels.model.response.searchwrapper.ListingProduct());
        treelsListingResponseBO.getTreelsResponse().getListingProducts().get(0).setHotels(new ArrayList<>());
        treelsListingResponseBO.getTreelsResponse().getListingProducts().get(0).getHotels().add(new com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntityAbridged());
        Assert.assertNull(treelsListingExecutor.search(new SearchWrapperInputRequest(), new HashMap<>(), new HashMap<>()));
    }

    @Test
    public void filterCount() throws ClientGatewayException {
        Map<String, String> headers = new HashMap<>();
        headers.put("mmt-auth", "test");
        FilterSearchMetaDataResponse filterSearchMetaDataResponse = treelsListingExecutor.filterCount(new SearchWrapperInputRequest(), headers);
        Assert.assertNull(filterSearchMetaDataResponse);
    }


}
