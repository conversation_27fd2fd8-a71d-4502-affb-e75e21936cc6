package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.pojo.request.detail.mob.HostCallingInitiateRequestBody;
import com.mmt.hotels.pojo.response.detail.HostCallingInitiateResponse;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class HostCallingExecutorTest {

    @InjectMocks
    private HostCallingExecutor hostCallingExecutor;

    @Mock
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private RestConnectorUtil restConnectorUtil;

    @Mock
    private MetricAspect metricAspect;

    private HostCallingInitiateRequestBody mockRequest;
    private Map<String, String[]> mockParameterMap;
    private Map<String, String> mockHttpHeaderMap;
    private String mockJsonResponse;
    private HostCallingInitiateResponse mockResponse;

    @Before
    public void setUp() {
        mockRequest = new HostCallingInitiateRequestBody();
        mockRequest.setHotelId("HTL123");
        mockRequest.setVisitorId("VIS123");

        mockParameterMap = new HashMap<>();
        mockHttpHeaderMap = new HashMap<>();
        mockHttpHeaderMap.put("mmt-auth", "test-auth");

        mockJsonResponse = "{\"status\":\"success\",\"requestId\":\"REQ123\"}";
        
        mockResponse = new HostCallingInitiateResponse();
        mockResponse.setStatus("success");
        mockResponse.setRequestId("REQ123");

        // Set up MDC
        MDC.put("correlationKey", "TEST_CORRELATION");
        
        // Set the hostCallingInitiateUrl field using reflection
        try {
            java.lang.reflect.Field urlField = HostCallingExecutor.class.getDeclaredField("hostCallingInitiateUrl");
            urlField.setAccessible(true);
            urlField.set(hostCallingExecutor, "http://hotels-entity-service-detail.ecs.mmt/hostCalling/initiate");
        } catch (Exception e) {
            fail("Failed to set up test data: " + e.getMessage());
        }
    }

    @After
    public void tearDown() {
        MDC.clear();
    }

    @Test
    public void should_ReturnValidResponse_When_EntityServiceReturnsSuccess() throws Exception {
        // Arrange
        when(objectMapperUtil.getJsonFromObject(eq(mockRequest), eq(DependencyLayer.CLIENTGATEWAY)))
            .thenReturn("{\"hotelId\":\"HTL123\"}");
        when(restConnectorUtil.performHostCallingPost(anyString(), any(), anyString()))
            .thenReturn(mockJsonResponse);
        when(objectMapperUtil.getObjectFromJson(eq(mockJsonResponse), eq(HostCallingInitiateResponse.class), eq(DependencyLayer.CLIENTGATEWAY)))
            .thenReturn(mockResponse);

        // Act
        HostCallingInitiateResponse result = hostCallingExecutor.getEntityServiceResponse(
            mockRequest, mockParameterMap, mockHttpHeaderMap);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.getStatus());
        assertEquals("REQ123", result.getRequestId());

        // Verify interactions
        verify(objectMapperUtil).getJsonFromObject(eq(mockRequest), eq(DependencyLayer.CLIENTGATEWAY));
        verify(restConnectorUtil).performHostCallingPost(anyString(), any(), anyString());
        verify(objectMapperUtil).getObjectFromJson(eq(mockJsonResponse), eq(HostCallingInitiateResponse.class), eq(DependencyLayer.CLIENTGATEWAY));
    }

    @Test(expected = ClientGatewayException.class)
    public void should_ThrowException_When_JsonParsingFails() throws Exception {
        // Arrange
        when(objectMapperUtil.getJsonFromObject(eq(mockRequest), eq(DependencyLayer.CLIENTGATEWAY)))
            .thenReturn("{\"hotelId\":\"HTL123\"}");
        when(restConnectorUtil.performHostCallingPost(anyString(), any(), anyString()))
            .thenReturn("invalid-json");
        when(objectMapperUtil.getObjectFromJson(anyString(), eq(HostCallingInitiateResponse.class), eq(DependencyLayer.CLIENTGATEWAY)))
            .thenThrow(new RuntimeException("JSON parsing error"));

        // Act
        hostCallingExecutor.getEntityServiceResponse(mockRequest, mockParameterMap, mockHttpHeaderMap);
    }

    @Test(expected = ClientGatewayException.class)
    public void should_ThrowException_When_RestConnectorFails() throws Exception {
        // Arrange
        when(objectMapperUtil.getJsonFromObject(eq(mockRequest), eq(DependencyLayer.CLIENTGATEWAY)))
            .thenReturn("{\"hotelId\":\"HTL123\"}");
        when(restConnectorUtil.performHostCallingPost(anyString(), any(), anyString()))
            .thenThrow(new RuntimeException("Connection failed"));

        // Act
        hostCallingExecutor.getEntityServiceResponse(mockRequest, mockParameterMap, mockHttpHeaderMap);
    }

    @Test
    public void should_SetCorrectHeaders_When_CallingEntityService() throws Exception {
        // Arrange
        mockHttpHeaderMap.put("akamai-user-ip", "***********");
        
        when(objectMapperUtil.getJsonFromObject(eq(mockRequest), eq(DependencyLayer.CLIENTGATEWAY)))
            .thenReturn("{\"hotelId\":\"HTL123\"}");
        when(restConnectorUtil.performHostCallingPost(anyString(), any(), anyString()))
            .thenReturn(mockJsonResponse);
        when(objectMapperUtil.getObjectFromJson(eq(mockJsonResponse), eq(HostCallingInitiateResponse.class), eq(DependencyLayer.CLIENTGATEWAY)))
            .thenReturn(mockResponse);

        // Act
        hostCallingExecutor.getEntityServiceResponse(mockRequest, mockParameterMap, mockHttpHeaderMap);

        // Assert - Just verify the method was called with correct types
        verify(restConnectorUtil).performHostCallingPost(anyString(), any(), anyString());
    }

    @Test
    public void should_HandleMissingAuth_When_NoAuthHeader() throws Exception {
        // Arrange
        mockHttpHeaderMap.remove("mmt-auth");
        
        when(objectMapperUtil.getJsonFromObject(eq(mockRequest), eq(DependencyLayer.CLIENTGATEWAY)))
            .thenReturn("{\"hotelId\":\"HTL123\"}");
        when(restConnectorUtil.performHostCallingPost(anyString(), any(), anyString()))
            .thenReturn(mockJsonResponse);
        when(objectMapperUtil.getObjectFromJson(eq(mockJsonResponse), eq(HostCallingInitiateResponse.class), eq(DependencyLayer.CLIENTGATEWAY)))
            .thenReturn(mockResponse);

        // Act
        hostCallingExecutor.getEntityServiceResponse(mockRequest, mockParameterMap, mockHttpHeaderMap);

        // Assert - Just verify the method was called
        verify(restConnectorUtil).performHostCallingPost(anyString(), any(), anyString());
    }

    @Test
    public void should_HandleEmptyCorrelationKey_When_MDCIsEmpty() throws Exception {
        // Arrange
        MDC.clear(); // Clear correlation key
        
        when(objectMapperUtil.getJsonFromObject(eq(mockRequest), eq(DependencyLayer.CLIENTGATEWAY)))
            .thenReturn("{\"hotelId\":\"HTL123\"}");
        when(restConnectorUtil.performHostCallingPost(anyString(), any(), anyString()))
            .thenReturn(mockJsonResponse);
        when(objectMapperUtil.getObjectFromJson(eq(mockJsonResponse), eq(HostCallingInitiateResponse.class), eq(DependencyLayer.CLIENTGATEWAY)))
            .thenReturn(mockResponse);

        // Act
        HostCallingInitiateResponse result = hostCallingExecutor.getEntityServiceResponse(
            mockRequest, mockParameterMap, mockHttpHeaderMap);

        // Assert
        assertNotNull(result);
        verify(restConnectorUtil).performHostCallingPost(anyString(), any(), contains("default"));
    }

    @Test
    public void should_HandleLowercaseAkmaiHeader_When_HeaderIsMixedCase() throws Exception {
        // Arrange
        mockHttpHeaderMap.put("Akamai-User-IP", "***********"); // Mixed case
        
        when(objectMapperUtil.getJsonFromObject(eq(mockRequest), eq(DependencyLayer.CLIENTGATEWAY)))
            .thenReturn("{\"hotelId\":\"HTL123\"}");
        when(restConnectorUtil.performHostCallingPost(anyString(), any(), anyString()))
            .thenReturn(mockJsonResponse);
        when(objectMapperUtil.getObjectFromJson(eq(mockJsonResponse), eq(HostCallingInitiateResponse.class), eq(DependencyLayer.CLIENTGATEWAY)))
            .thenReturn(mockResponse);

        // Act
        hostCallingExecutor.getEntityServiceResponse(mockRequest, mockParameterMap, mockHttpHeaderMap);

        // Assert - Just verify the method was called
        verify(restConnectorUtil).performHostCallingPost(anyString(), any(), anyString());
    }
} 