package com.mmt.hotels.clientgateway;

import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.controller.advice.ControllerAdvice;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.ErrorHelper;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorCodes;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;


import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;
import static org.junit.Assert.assertNull;

import java.util.Arrays;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class ControllerAdviceTest {
    @InjectMocks
    private ControllerAdvice controllerAdvice;

    @Mock
    ErrorHelper errorHelper;

    @Mock
    MetricAspect metricAspect;

    @Mock
    private CommonConfigConsul commonConfigConsul;

    @Test
    public void handleExceptionTest() {
        ClientGatewayException clientGatewayException = new ClientGatewayException();
        clientGatewayException.setDependencyLayer(DependencyLayer.ORCHESTRATOR);
        clientGatewayException.setErrorType(ErrorType.DOWNSTREAM);
        clientGatewayException.setHttpStatusCode(HttpStatus.OK);
        controllerAdvice.handleException(clientGatewayException);
        MetricErrorCodes metricErrorCodes = new MetricErrorCodes();
        metricErrorCodes.getApi208();
        metricErrorCodes.getM2();
        metricErrorCodes.getBd359();
        metricErrorCodes.getM124();
        metricErrorCodes.getM123();
        metricErrorCodes.getM101();
        metricErrorCodes.getPd147();
        metricErrorCodes.getPm627();
        metricErrorCodes.getPs438();
        String toString = metricErrorCodes.toString();

    }



    @Test
    public void handleExceptionConnectivityErrorMessagePickedTest() {
        ClientGatewayException clientGatewayException = new ClientGatewayException();
        clientGatewayException.setDependencyLayer(DependencyLayer.ORCHESTRATOR);
        clientGatewayException.setErrorType(ErrorType.valueOf("CONNECTIVITY"));
        clientGatewayException.setCode("4002");
        clientGatewayException.setHttpStatusCode(HttpStatus.OK);

        when(commonConfigConsul.getGenericRestErrorMessage()).thenReturn("We are facing some issues currently, please try again");

        ResponseEntity<ResponseWrapper<Object>> responseEntity = controllerAdvice.handleException(clientGatewayException);
        assertNotNull(responseEntity);
        assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        assertEquals("We are facing some issues currently, please try again", responseEntity.getBody().getError().getMessage());
    }

    @Test
    public void handleExceptionConnectivityErrorMessageNotPickedTest() {
        ClientGatewayException clientGatewayException = new ClientGatewayException();
        clientGatewayException.setDependencyLayer(DependencyLayer.ORCHESTRATOR);
        clientGatewayException.setErrorType(ErrorType.valueOf("CONNECTIVITY"));
        clientGatewayException.setCode("4005");
        clientGatewayException.setHttpStatusCode(HttpStatus.OK);


        ResponseEntity<ResponseWrapper<Object>> responseEntity = controllerAdvice.handleException(clientGatewayException);
        assertNotNull(responseEntity);
        assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        assertNull(responseEntity.getBody().getError().getMessage());
    }

    @Test
    public void handleExceptionApiErrorTest() {

        List<String> errorCodes = Arrays.asList("402405", "400103");

        for (String errorCode : errorCodes) {
            ClientGatewayException clientGatewayException = new ClientGatewayException();
            clientGatewayException.setDependencyLayer(DependencyLayer.ORCHESTRATOR);
            clientGatewayException.setErrorType(ErrorType.valueOf("CONNECTIVITY"));
            clientGatewayException.setCode(errorCode);
            clientGatewayException.setHttpStatusCode(HttpStatus.OK);


            String expectedErrorMessage = "We are facing some issues currently, please try again";

            when(commonConfigConsul.getGenericRestErrorMessage()).thenReturn(expectedErrorMessage);

            ResponseEntity<ResponseWrapper<Object>> responseEntity = controllerAdvice.handleException(clientGatewayException);

            assertNotNull(responseEntity);
            assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
            assertNotNull(responseEntity.getBody());
            assertNotNull(responseEntity.getBody().getError());
            assertEquals(expectedErrorMessage, responseEntity.getBody().getError().getMessage());
        }
    }

    @Test
    public void handleExceptionNegativeApiErrorTest() {
        ClientGatewayException clientGatewayException = new ClientGatewayException();
        clientGatewayException.setDependencyLayer(DependencyLayer.ORCHESTRATOR);
        clientGatewayException.setErrorType(ErrorType.valueOf("CONNECTIVITY"));
        clientGatewayException.setCode("40089");
        clientGatewayException.setHttpStatusCode(HttpStatus.OK);


        ResponseEntity<ResponseWrapper<Object>> responseEntity = controllerAdvice.handleException(clientGatewayException);
        assertNotNull(responseEntity);
        assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        assertNull(responseEntity.getBody().getError().getMessage());
    }



}

