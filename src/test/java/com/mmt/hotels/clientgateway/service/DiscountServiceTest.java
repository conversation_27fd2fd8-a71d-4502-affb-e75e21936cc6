package com.mmt.hotels.clientgateway.service;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.ValidateCouponRequest;
import com.mmt.hotels.clientgateway.response.ValidateCouponResponseBody;
import com.mmt.hotels.clientgateway.restexecutors.DiscountServiceExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.DiscountServiceFactory;
import com.mmt.hotels.clientgateway.transformer.request.DiscountRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.DiscountResponseTransformer;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.model.request.UserLocation;
import com.mmt.hotels.model.request.ValidateCouponRequestBody;
import com.mmt.hotels.model.response.discount.ValidateCouponResponse;

import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class DiscountServiceTest {

	@InjectMocks
	private DiscountService discountService;
	
	@Mock
	private DiscountServiceFactory discountServiceFactory;
	
	@Mock
	private DiscountServiceExecutor discountServiceExecutor;
	
    @Mock
    ObjectMapperUtil objectMapperUtil;
    
    @Mock
    CommonHelper commonHelper;

	@Test
	public void testValidateCoupon() throws ClientGatewayException {
		ValidateCouponRequest validateCouponRequest = new ValidateCouponRequest();
		DiscountRequestTransformer requestTransformer = Mockito.mock(DiscountRequestTransformer.class);
		Mockito.when(discountServiceFactory.getRequestService(Mockito.anyString())).thenReturn(requestTransformer);
		Mockito.when(requestTransformer.convertValidateCouponRequest(Mockito.any())).thenReturn(new ValidateCouponRequestBody());
		Mockito.when(commonHelper.buildUserLocationFromHeader(Mockito.any())).thenReturn(new UserLocation());

		Mockito.when(discountServiceExecutor.getValidateCouponResponse(Mockito.any(),Mockito.any(),Mockito.any()))
		.thenReturn(new ValidateCouponResponse.Builder().build());
		
		DiscountResponseTransformer responseTransformer = Mockito.mock(DiscountResponseTransformer.class);
		Mockito.when(discountServiceFactory.getResponseService(Mockito.anyString())).thenReturn(responseTransformer);
		Mockito.when(responseTransformer.convertValidateCouponResponse(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(new ValidateCouponResponseBody());
		
		ValidateCouponResponseBody resp = discountService.validateCoupon(validateCouponRequest, new HashMap<>(), new HashMap<>(), "PWA");
		Assert.assertNotNull(resp);
	}

	@Test(expected = Exception.class)
	public void testValidateCouponException() throws ClientGatewayException{
		ValidateCouponRequest validateCouponRequest = new ValidateCouponRequest();
		ValidateCouponResponseBody resp = discountService.validateCoupon(validateCouponRequest, new HashMap<>(), null, "PWA");
	}
}
