package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.configuration.CircuitBreakerConfig;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.response.moblanding.MobLandingResponse;
import com.mmt.hotels.clientgateway.util.CircuitBreakerMetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;


import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for CircuitBreakerService
 */
@RunWith(MockitoJUnitRunner.class)
public class CircuitBreakerServiceTest {

    @InjectMocks
    private CircuitBreakerService circuitBreakerService;

    @Mock
    private CircuitBreakerRegistry circuitBreakerRegistry;

    @Mock
    private CircuitBreakerConfig circuitBreakerConfig;

    @Mock
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private CircuitBreakerMetricAspect circuitBreakerMetricAspect;

    @Mock
    private CircuitBreaker circuitBreaker;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        MDC.put("correlationKey", "test-correlation-key");
    }

    @Test
    public void testExecuteMobLandingWithCircuitBreakerDisabled() throws ClientGatewayException {
        // Given
        when(circuitBreakerConfig.isMobLandingCircuitBreakerEnabled()).thenReturn(false);
        MobLandingResponse expectedResponse = new MobLandingResponse();
        Supplier<MobLandingResponse> apiCall = () -> expectedResponse;

        // When
        MobLandingResponse result = circuitBreakerService.executeMobLandingWithCircuitBreaker(
                apiCall, new HashMap<String,String>(){{
                    put("test-key","test-header");
                }});

        // Then
        assertEquals(expectedResponse, result);
        verify(circuitBreakerConfig).isMobLandingCircuitBreakerEnabled();
        verifyNoInteractions(circuitBreakerRegistry);
    }

    @Test
    public void testExecuteMobLandingWithCircuitBreakerEnabled() throws ClientGatewayException {
        // Given
        when(circuitBreakerConfig.isMobLandingCircuitBreakerEnabled()).thenReturn(true);
        when(circuitBreakerRegistry.circuitBreaker("mob-landing")).thenReturn(circuitBreaker);
        when(circuitBreaker.getState()).thenReturn(CircuitBreaker.State.CLOSED);

        MobLandingResponse expectedResponse = new MobLandingResponse();
        Supplier<MobLandingResponse> apiCall = () -> expectedResponse;

        // When
        MobLandingResponse result = circuitBreakerService.executeMobLandingWithCircuitBreaker(
                apiCall, new HashMap<>());

        // Then
        assertEquals(expectedResponse, result);
        verify(circuitBreakerRegistry).circuitBreaker("mob-landing");
        verify(circuitBreakerMetricAspect).logCircuitBreakerCallTime(eq("mob-landing"), anyLong(), eq(true));
    }

    @Test
    public void testExecuteMobLandingWithCircuitBreakerFailure() throws ClientGatewayException {
        // Given
        when(circuitBreakerConfig.isMobLandingCircuitBreakerEnabled()).thenReturn(true);
        when(circuitBreakerRegistry.circuitBreaker("mob-landing")).thenReturn(circuitBreaker);

        Supplier<MobLandingResponse> apiCall = () -> {
            throw new RuntimeException("API failure");
        };

        // When
        MobLandingResponse result = circuitBreakerService.executeMobLandingWithCircuitBreaker(
                apiCall, new HashMap<>());

        // Then
        assertNotNull(result); // Should return fallback response
        verify(circuitBreakerMetricAspect).logCircuitBreakerFallbackExecution("mob-landing", "DEFAULT_RESPONSE");
    }

    @Test
    public void testExecuteWithCircuitBreakerGeneric() throws ClientGatewayException {
        // Given
        when(circuitBreakerRegistry.circuitBreaker("test-breaker")).thenReturn(circuitBreaker);

        String expectedResult = "success";
        Supplier<String> apiCall = () -> expectedResult;
        Supplier<String> fallbackCall = () -> "fallback";

        // When
        String result = circuitBreakerService.executeWithCircuitBreaker(
                "test-breaker", apiCall, fallbackCall);

        // Then
        assertEquals(expectedResult, result);
        verify(circuitBreakerRegistry).circuitBreaker("test-breaker");
    }

    @Test
    public void testExecuteWithCircuitBreakerNotFound() throws ClientGatewayException {
        // Given
        when(circuitBreakerRegistry.circuitBreaker("non-existent")).thenReturn(null);
        
        String expectedResult = "direct-call";
        Supplier<String> apiCall = () -> expectedResult;
        Supplier<String> fallbackCall = () -> "fallback";

        // When
        String result = circuitBreakerService.executeWithCircuitBreaker(
                "non-existent", apiCall, fallbackCall);

        // Then
        assertEquals(expectedResult, result);
        verify(circuitBreakerRegistry).circuitBreaker("non-existent");
    }

    @Test
    public void testGetCircuitBreakerState() {
        // Given
        when(circuitBreakerRegistry.circuitBreaker("test-breaker")).thenReturn(circuitBreaker);
        when(circuitBreaker.getState()).thenReturn(CircuitBreaker.State.OPEN);

        // When
        String state = circuitBreakerService.getCircuitBreakerState("test-breaker");

        // Then
        assertEquals("OPEN", state);
        verify(circuitBreakerRegistry).circuitBreaker("test-breaker");
    }

    @Test
    public void testGetCircuitBreakerStateNotFound() {
        // Given
        when(circuitBreakerRegistry.circuitBreaker("non-existent")).thenReturn(null);

        // When
        String state = circuitBreakerService.getCircuitBreakerState("non-existent");

        // Then
        assertEquals("NOT_CONFIGURED", state);
    }

    @Test
    public void testGetCircuitBreakerMetrics() {
        // Given
        CircuitBreaker.Metrics metrics = mock(CircuitBreaker.Metrics.class);
        when(circuitBreakerRegistry.circuitBreaker("test-breaker")).thenReturn(circuitBreaker);
        when(circuitBreaker.getState()).thenReturn(CircuitBreaker.State.CLOSED);
        when(circuitBreaker.getMetrics()).thenReturn(metrics);
        when(metrics.getFailureRate()).thenReturn(10.5f);
        when(metrics.getNumberOfBufferedCalls()).thenReturn(100);
        when(metrics.getNumberOfFailedCalls()).thenReturn(10);
        when(metrics.getNumberOfSuccessfulCalls()).thenReturn(90);
        when(metrics.getNumberOfSlowCalls()).thenReturn(5);
        when(metrics.getSlowCallRate()).thenReturn(5.0f);

        // When
        Map<String, Object> result = circuitBreakerService.getCircuitBreakerMetrics("test-breaker");

        // Then
        assertEquals("CLOSED", result.get("state"));
        assertEquals(10.5f, result.get("failureRate"));
        assertEquals(100, result.get("numberOfBufferedCalls"));
        assertEquals(10, result.get("numberOfFailedCalls"));
        assertEquals(90, result.get("numberOfSuccessfulCalls"));
        assertEquals(5, result.get("numberOfSlowCalls"));
        assertEquals(5.0f, result.get("slowCallRate"));
    }

    @Test
    public void testGetCircuitBreakerMetricsNotFound() {
        // Given
        when(circuitBreakerRegistry.circuitBreaker("non-existent")).thenReturn(null);

        // When
        Map<String, Object> result = circuitBreakerService.getCircuitBreakerMetrics("non-existent");

        // Then
        assertTrue(result.containsKey("error"));
        assertEquals("Circuit breaker not found: non-existent", result.get("error"));
    }


}
