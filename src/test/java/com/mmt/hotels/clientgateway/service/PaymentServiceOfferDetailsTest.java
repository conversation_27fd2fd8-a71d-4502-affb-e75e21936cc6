package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.enums.MarshallingErrors;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.PaymentHelper;
import com.mmt.hotels.clientgateway.request.OfferDetailsRequest;
import com.mmt.hotels.clientgateway.restexecutors.PaymentExecutor;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * Focused unit‐tests for {@link PaymentService#getOfferDetails}.
 * We cover both the NO_DATA_FOUND branch and the happy path that delegates to {@link PaymentExecutor}.
 */
@RunWith(MockitoJUnitRunner.class)
public class PaymentServiceOfferDetailsTest {

    @InjectMocks
    private PaymentService paymentService;

    @Mock
    private PaymentHelper paymentHelper;

    @Mock
    private CommonHelper commonHelper;

    @Mock
    private PaymentExecutor paymentExecutor;

    private HttpServletRequest servletRequest;

    @Before
    public void setUp() {
        servletRequest = new MockHttpServletRequest();
    }

    @Test
    public void testGetOfferDetails_whenUserServiceReturnsNull_shouldReturnErrorResponse() throws Exception {
        // Arrange
        Mockito.when(paymentHelper.getUserServiceResponseFromUUID(Mockito.any(OfferDetailsRequest.class), Mockito.anyMap()))
               .thenReturn(null);

        OfferDetailsRequest req = new OfferDetailsRequest();
        req.setCorrelationKey("CKEY");

        // Act
        com.mmt.hotels.model.response.payment.offerdetails.OfferDetailsResponse resp =
                paymentService.getOfferDetails(req, servletRequest);

        // Assert
        Assert.assertNotNull(resp);
        Assert.assertNotNull(resp.getResponseErrors());
        Assert.assertFalse(resp.getResponseErrors().getErrorList().isEmpty());
        Assert.assertEquals(MarshallingErrors.NO_DATA_FOUND.getErrorCode(),
                resp.getResponseErrors().getErrorList().get(0).getErrorCode());
    }

    @Test
    public void testGetOfferDetails_happyPath_shouldDelegateToPaymentExecutor() throws Exception {
        // Prepare a minimal ExtendedUser and wrap into UserServiceResponse
        ExtendedUser extUser = new ExtendedUser();
        extUser.setUuid("UUID-1");
        UserServiceResult usrResult = new UserServiceResult();
        usrResult.setExtendedUser(extUser);
        UserServiceResponse usrResp = new UserServiceResponse();
        usrResp.setResult(usrResult);
        Mockito.when(paymentHelper.getUserServiceResponseFromUUID(Mockito.any(OfferDetailsRequest.class), Mockito.anyMap()))
               .thenReturn(usrResp);

        // mock commonHelper → Hydra
        HydraResponse hydraResp = new HydraResponse();
        Mockito.when(commonHelper.executeHydraService(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.any(), Mockito.anyInt(), Mockito.any(), Mockito.eq(extUser), Mockito.anyMap(), Mockito.any()))
               .thenReturn(hydraResp);

        // mock paymentExecutor response
        com.mmt.hotels.model.response.payment.offerdetails.OfferDetailsResponse expected =
                new com.mmt.hotels.model.response.payment.offerdetails.OfferDetailsResponse();
        Mockito.when(paymentExecutor.getOfferDetails(Mockito.anyString(), Mockito.eq(hydraResp), Mockito.eq(extUser), Mockito.<Map<String,String>>any(), Mockito.anyString()))
               .thenReturn(expected);

        OfferDetailsRequest req = new OfferDetailsRequest();
        req.setCorrelationKey("CKEY");
        req.setRequester("WEB");
        req.setBookingId("BID-007");

        // Act
        com.mmt.hotels.model.response.payment.offerdetails.OfferDetailsResponse actual =
                paymentService.getOfferDetails(req, servletRequest);

        // Assert
        Assert.assertSame(expected, actual);

        // Verify delegation parameters
        ArgumentCaptor<String> bookingIdCap = ArgumentCaptor.forClass(String.class);
        Mockito.verify(paymentExecutor).getOfferDetails(Mockito.eq("WEB"), Mockito.eq(hydraResp), Mockito.eq(extUser), Mockito.anyMap(), bookingIdCap.capture());
        Assert.assertEquals("BID-007", bookingIdCap.getValue());
    }
} 