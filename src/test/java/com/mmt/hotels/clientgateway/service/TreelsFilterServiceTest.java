package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.request.TreelsFilterCountRequest;
import com.mmt.hotels.clientgateway.response.filter.FilterResponse;
import com.mmt.hotels.clientgateway.restexecutors.TreelsListingExecutor;
import com.mmt.hotels.clientgateway.transformer.request.TreelsFilterTransformer;
import com.mmt.hotels.clientgateway.transformer.response.TreelsFilterResponseTransformer;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.FilterSearchMetaDataResponse;
import com.mmt.hotels.model.response.errors.Error;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import org.apache.commons.collections.map.HashedMap;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.checkInAddition;
import static com.mmt.hotels.clientgateway.constants.Constants.checkOutAddition;
import static com.mmt.hotels.clientgateway.constants.Constants.defaultGuest;
import static com.mmt.hotels.clientgateway.constants.Constants.defaultRooms;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TreelsFilterServiceTest {
    @InjectMocks
    TreelsFilterService treelsFilterService;

    @Mock
    private MetricErrorLogger metricErrorLogger;

    @Mock
    private CommonHelper commonHelper;

    @Mock
    private CommonConfigHelper commonConfigHelper;

    @Mock
    private MetricAspect metricAspect;

    @Mock
    private TreelsFilterTransformer treelsFilterTransformer;

    @Mock
    private TreelsListingExecutor treelsListingExecutor;

    @Mock
    private TreelsFilterResponseTransformer treelsFilterResponseTransformer;


    @Test
    public void filterCountTest() throws ClientGatewayException {
        TreelsFilterCountRequest filterRequest = new TreelsFilterCountRequest();
        Map<String, String[]> parameterMap = new HashedMap();
        Map<String, String> httpHeaderMap = new HashedMap();
        Map<String,Integer> filterCountMap = new HashedMap();
        filterCountMap.put(checkInAddition,21);
        filterCountMap.put(checkOutAddition,22);
        filterCountMap.put(defaultRooms,1);
        filterCountMap.put(defaultGuest,2);
        FilterSearchMetaDataResponse filterSearchMetaDataResponse = new FilterSearchMetaDataResponse();
        Map<FilterGroup, List<Filter>> filterDataMap = new HashedMap();
        List<Filter> filterList = new ArrayList<>();
        filterList.add(new Filter());
        filterList.get(0).setFilterGroup(FilterGroup.AMENITIES);
        filterList.get(0).setFilterValue("test");
        filterDataMap.put(FilterGroup.AMENITIES, filterList);
        filterSearchMetaDataResponse.setFilterDataMap(filterDataMap);
        when(treelsListingExecutor.filterCount(Mockito.any(), Mockito.anyMap())).thenReturn(filterSearchMetaDataResponse);
        when(treelsFilterResponseTransformer.convertFilterResponse(Mockito.any(),Mockito.any())).thenReturn(new FilterResponse());
        FilterResponse filterResponse = treelsFilterService.filterCount(filterRequest, parameterMap, httpHeaderMap);
        Assert.assertNotNull(filterResponse);
    }

    @Test
    public void filterCountNPETest() throws ClientGatewayException {
        FilterResponse filterResponse = treelsFilterService.filterCount(new TreelsFilterCountRequest(), new HashMap<>(), new HashMap<>());
        Assert.assertNull(filterResponse);
    }

    @Test
    public void filterCountWithResponseErrorTest() throws ClientGatewayException {
        try {
            TreelsFilterCountRequest filterRequest = new TreelsFilterCountRequest();
            Map<String, String[]> parameterMap = new HashedMap();
            Map<String, String> httpHeaderMap = new HashedMap();
            Map<String, Integer> filterCountMap = new HashedMap();
            filterCountMap.put(checkInAddition, 21);
            filterCountMap.put(checkOutAddition, 22);
            filterCountMap.put(defaultRooms, 1);
            filterCountMap.put(defaultGuest, 2);
            FilterSearchMetaDataResponse filterSearchMetaDataResponse = new FilterSearchMetaDataResponse();
            filterSearchMetaDataResponse.setResponseErrors(new ResponseErrors());
            List<Error> errList = new ArrayList<>();
            errList.add(new Error.Builder().buildErrorCode("400000", "unexpected error").build());
            filterSearchMetaDataResponse.setResponseErrors(new ResponseErrors.Builder().buildErrorList(errList).build());
            when(treelsListingExecutor.filterCount(Mockito.any(), Mockito.anyMap())).thenReturn(filterSearchMetaDataResponse);
            FilterResponse filterResponse = treelsFilterService.filterCount(filterRequest, parameterMap, httpHeaderMap);
        } catch (Exception e) {
            Assert.assertNotNull(e);
            Assert.assertEquals(e.getMessage(), "unexpected error");
        }
    }

    @Test
    public void filterCountWithEmptyFilterMapTest() throws ClientGatewayException {
        try {
            TreelsFilterCountRequest filterRequest = new TreelsFilterCountRequest();
            Map<String, String[]> parameterMap = new HashedMap();
            Map<String, String> httpHeaderMap = new HashedMap();
            FilterSearchMetaDataResponse filterSearchMetaDataResponse = new FilterSearchMetaDataResponse();
            // Set empty map
            filterSearchMetaDataResponse.setFilterDataMap(new HashMap<>());
            when(treelsListingExecutor.filterCount(Mockito.any(), Mockito.anyMap())).thenReturn(filterSearchMetaDataResponse);

            treelsFilterService.filterCount(filterRequest, parameterMap, httpHeaderMap);
            Assert.fail("Expected ClientGatewayException due to empty filter map");
        } catch (Exception ex) {
            Assert.assertTrue(ex.getMessage() != null);
        }
    }
}
