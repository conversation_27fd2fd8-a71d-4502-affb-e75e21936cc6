package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.response.filter.FilterCategory;
import com.mmt.hotels.clientgateway.response.filter.Filter;
import com.mmt.hotels.filter.FilterShownDetails;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

/**
 * Lightweight test that focuses solely on internal helper methods of {@link ListingService}
 * without boot-strapping the Spring container.  Reflection is used to access the
 * private methods because their behaviour is pure and does not rely on any injected
 * collaborators.
 */
public class ListingServiceHelperTest {

    @Test
    public void testBuildFilterShown_returnsFlattenedList() {
        // Prepare a minimal Filter → FilterCategory structure
        Filter filter = new Filter();
        filter.setFilterGroup("STAR_RATING");
        filter.setFilterValue("5");
        filter.setRangeFilter(false);

        FilterCategory category = new FilterCategory();
        category.setCategoryName("STAR_CATEGORY");
        category.setFilters(Arrays.asList(filter));

        ListingService service = new ListingService();

        @SuppressWarnings("unchecked")
        List<FilterShownDetails> result = (List<FilterShownDetails>) ReflectionTestUtils.invokeMethod(
                service, "buildFilterShown", Arrays.asList(category));

        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
        FilterShownDetails details = result.get(0);
        Assert.assertEquals("STAR_CATEGORY", details.getCategoryName());
        Assert.assertEquals("STAR_RATING", details.getFilterGroup());
        Assert.assertEquals("5", details.getFilterValue());
        // Depending on version FilterShownDetails may not expose range flag – basic property assertions are enough
    }
} 