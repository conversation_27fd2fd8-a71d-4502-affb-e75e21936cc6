package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.response.HotelPermissions;
import com.mmt.hotels.clientgateway.restexecutors.MobLandingExecutor;
import com.mmt.hotels.clientgateway.thirdparty.request.AffiliateData;
import com.mmt.hotels.clientgateway.thirdparty.request.HotelsPermission;
import com.mmt.hotels.clientgateway.thirdparty.request.PartnerAffiliateDetails;
import com.mmt.hotels.clientgateway.thirdparty.request.Permissions;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResult;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.response.txn.Hotels;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class LandingServiceTest {

	@InjectMocks
	private LandingService landingService;
	
	@Mock
	private MobLandingExecutor mobLandingExecutor;
	
	@Mock
	private CommonHelper commonHelper;
	
	@Mock
	private Utility utility;
	
	@Test
	public void testGetLatLngFromGooglePlaceId() throws ClientGatewayException{
		Mockito.when(mobLandingExecutor.getLatLngFromGooglePlaceId(Mockito.anyString(), Mockito.any(),Mockito.any())).thenReturn("");
		Assert.assertNotNull(landingService.getLatLngFromGooglePlaceId("", null, null));
	}

	@Test
	public void testGetFeatureBasedAccessDetails_Success() throws ClientGatewayException {
		// Arrange
		SearchWrapperInputRequest request = new SearchWrapperInputRequest();
		request.setMmtAuth("test-auth");
		request.setEmail("<EMAIL>");
		request.setMobile("9876543210");
		request.setChannel("mobile");
		request.setCorrelationKey("test-correlation");
		request.setIdContext("test-context");
		request.setSiteDomain("IN");
		
		Map<String, String> headers = new HashMap<>();
		
		// Mock UserService response
		UserServiceResponse userServiceResponse = new UserServiceResponse();
		UserServiceResult result = new UserServiceResult();
		ExtendedUser extendedUser = new ExtendedUser();
		extendedUser.setUuid("test-uuid-123");
		result.setExtendedUser(extendedUser);
		userServiceResponse.setResult(result);
		
		// Mock PartnerAffiliateDetails response
		PartnerAffiliateDetails partnerAffiliateDetails = new PartnerAffiliateDetails();
		AffiliateData affiliateData = new AffiliateData();
		Permissions permissions = new Permissions();
		HotelsPermission hotelsPermission = new HotelsPermission();
		hotelsPermission.setSearchEnabled(true);
		hotelsPermission.setBookingEnabled(true);
		hotelsPermission.setBnplEnabled(false);
		permissions.setHotels(hotelsPermission);
		affiliateData.setPermissions(permissions);
		partnerAffiliateDetails.setData(affiliateData);
		
		// Mock Hotels and HotelPermissions
		Hotels hotels = new Hotels();
		hotels.setSearchEnabled(true);
		hotels.setBookingEnabled(true);
		hotels.setBnplEnabled(false);
		
		HotelPermissions expectedPermissions = new HotelPermissions();
		
		Mockito.when(commonHelper.getUserDetails(
			Mockito.eq("test-auth"), 
			Mockito.eq("<EMAIL>"), 
			Mockito.eq("9876543210"),
			Mockito.eq("mobile"), 
			Mockito.eq("test-correlation"), 
			Mockito.eq("test-context"),
			Mockito.eq("IN"), 
			Mockito.isNull(), 
			Mockito.eq(headers)
		)).thenReturn(userServiceResponse);
		
		Mockito.when(mobLandingExecutor.fetchCTAMoAffId(
			Mockito.eq("test-uuid-123"), 
			Mockito.eq("booking"), 
			Mockito.eq("hotel")
		)).thenReturn(partnerAffiliateDetails);
		
		Mockito.when(utility.buildHotelPermissions(Mockito.any(Hotels.class)))
			.thenReturn(expectedPermissions);

		// Act
		HotelPermissions actualPermissions = landingService.getFeatureBasedAccessDetails(request, headers);

		// Assert
		Assert.assertNotNull(actualPermissions);
		Assert.assertEquals(expectedPermissions, actualPermissions);
		
		Mockito.verify(commonHelper).getUserDetails(
			"test-auth", "<EMAIL>", "9876543210", "mobile", 
			"test-correlation", "test-context", "IN", null, headers);
		Mockito.verify(mobLandingExecutor).fetchCTAMoAffId("test-uuid-123", "booking", "hotel");
		Mockito.verify(utility).buildHotelPermissions(Mockito.any(Hotels.class));
	}

	@Test(expected = ClientGatewayException.class)
	public void testGetFeatureBasedAccessDetails_UserServiceResponseNull() throws ClientGatewayException {
		// Arrange
		SearchWrapperInputRequest request = new SearchWrapperInputRequest();
		request.setMmtAuth("test-auth");
		request.setEmail("<EMAIL>");
		request.setMobile("9876543210");
		request.setChannel("mobile");
		request.setCorrelationKey("test-correlation");
		request.setIdContext("test-context");
		request.setSiteDomain("IN");
		
		Map<String, String> headers = new HashMap<>();
		
		Mockito.when(commonHelper.getUserDetails(
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.isNull(), Mockito.any()
		)).thenReturn(null);

		// Act
		landingService.getFeatureBasedAccessDetails(request, headers);
	}

	@Test(expected = ClientGatewayException.class)
	public void testGetFeatureBasedAccessDetails_UserServiceResultNull() throws ClientGatewayException {
		// Arrange
		SearchWrapperInputRequest request = new SearchWrapperInputRequest();
		request.setMmtAuth("test-auth");
		request.setEmail("<EMAIL>");
		request.setMobile("9876543210");
		request.setChannel("mobile");
		request.setCorrelationKey("test-correlation");
		request.setIdContext("test-context");
		request.setSiteDomain("IN");
		
		Map<String, String> headers = new HashMap<>();
		
		UserServiceResponse userServiceResponse = new UserServiceResponse();
		userServiceResponse.setResult(null);
		
		Mockito.when(commonHelper.getUserDetails(
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.isNull(), Mockito.any()
		)).thenReturn(userServiceResponse);

		// Act
		landingService.getFeatureBasedAccessDetails(request, headers);
	}

	@Test(expected = ClientGatewayException.class)
	public void testGetFeatureBasedAccessDetails_ExtendedUserNull() throws ClientGatewayException {
		// Arrange
		SearchWrapperInputRequest request = new SearchWrapperInputRequest();
		request.setMmtAuth("test-auth");
		request.setEmail("<EMAIL>");
		request.setMobile("9876543210");
		request.setChannel("mobile");
		request.setCorrelationKey("test-correlation");
		request.setIdContext("test-context");
		request.setSiteDomain("IN");
		
		Map<String, String> headers = new HashMap<>();
		
		UserServiceResponse userServiceResponse = new UserServiceResponse();
		UserServiceResult result = new UserServiceResult();
		result.setExtendedUser(null);
		userServiceResponse.setResult(result);
		
		Mockito.when(commonHelper.getUserDetails(
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.isNull(), Mockito.any()
		)).thenReturn(userServiceResponse);

		// Act
		landingService.getFeatureBasedAccessDetails(request, headers);
	}

	@Test(expected = ClientGatewayException.class)
	public void testGetFeatureBasedAccessDetails_UuidNull() throws ClientGatewayException {
		// Arrange
		SearchWrapperInputRequest request = new SearchWrapperInputRequest();
		request.setMmtAuth("test-auth");
		request.setEmail("<EMAIL>");
		request.setMobile("9876543210");
		request.setChannel("mobile");
		request.setCorrelationKey("test-correlation");
		request.setIdContext("test-context");
		request.setSiteDomain("IN");
		
		Map<String, String> headers = new HashMap<>();
		
		UserServiceResponse userServiceResponse = new UserServiceResponse();
		UserServiceResult result = new UserServiceResult();
		ExtendedUser extendedUser = new ExtendedUser();
		extendedUser.setUuid(null);
		result.setExtendedUser(extendedUser);
		userServiceResponse.setResult(result);
		
		Mockito.when(commonHelper.getUserDetails(
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.isNull(), Mockito.any()
		)).thenReturn(userServiceResponse);

		// Act
		landingService.getFeatureBasedAccessDetails(request, headers);
	}

	@Test(expected = ClientGatewayException.class)
	public void testGetFeatureBasedAccessDetails_PartnerAffiliateDetailsNull() throws ClientGatewayException {
		// Arrange
		SearchWrapperInputRequest request = new SearchWrapperInputRequest();
		request.setMmtAuth("test-auth");
		request.setEmail("<EMAIL>");
		request.setMobile("9876543210");
		request.setChannel("mobile");
		request.setCorrelationKey("test-correlation");
		request.setIdContext("test-context");
		request.setSiteDomain("IN");
		
		Map<String, String> headers = new HashMap<>();
		
		UserServiceResponse userServiceResponse = new UserServiceResponse();
		UserServiceResult result = new UserServiceResult();
		ExtendedUser extendedUser = new ExtendedUser();
		extendedUser.setUuid("test-uuid-123");
		result.setExtendedUser(extendedUser);
		userServiceResponse.setResult(result);
		
		Mockito.when(commonHelper.getUserDetails(
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.isNull(), Mockito.any()
		)).thenReturn(userServiceResponse);
		
		Mockito.when(mobLandingExecutor.fetchCTAMoAffId(
			Mockito.eq("test-uuid-123"), 
			Mockito.eq("booking"), 
			Mockito.eq("hotel")
		)).thenReturn(null);

		// Act
		landingService.getFeatureBasedAccessDetails(request, headers);
	}

	@Test(expected = ClientGatewayException.class)
	public void testGetFeatureBasedAccessDetails_AffiliateDataNull() throws ClientGatewayException {
		// Arrange
		SearchWrapperInputRequest request = new SearchWrapperInputRequest();
		request.setMmtAuth("test-auth");
		request.setEmail("<EMAIL>");
		request.setMobile("9876543210");
		request.setChannel("mobile");
		request.setCorrelationKey("test-correlation");
		request.setIdContext("test-context");
		request.setSiteDomain("IN");
		
		Map<String, String> headers = new HashMap<>();
		
		UserServiceResponse userServiceResponse = new UserServiceResponse();
		UserServiceResult result = new UserServiceResult();
		ExtendedUser extendedUser = new ExtendedUser();
		extendedUser.setUuid("test-uuid-123");
		result.setExtendedUser(extendedUser);
		userServiceResponse.setResult(result);
		
		PartnerAffiliateDetails partnerAffiliateDetails = new PartnerAffiliateDetails();
		partnerAffiliateDetails.setData(null);
		
		Mockito.when(commonHelper.getUserDetails(
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.isNull(), Mockito.any()
		)).thenReturn(userServiceResponse);
		
		Mockito.when(mobLandingExecutor.fetchCTAMoAffId(
			Mockito.eq("test-uuid-123"), 
			Mockito.eq("booking"), 
			Mockito.eq("hotel")
		)).thenReturn(partnerAffiliateDetails);

		// Act
		landingService.getFeatureBasedAccessDetails(request, headers);
	}

	@Test(expected = ClientGatewayException.class)
	public void testGetFeatureBasedAccessDetails_PermissionsNull() throws ClientGatewayException {
		// Arrange
		SearchWrapperInputRequest request = new SearchWrapperInputRequest();
		request.setMmtAuth("test-auth");
		request.setEmail("<EMAIL>");
		request.setMobile("9876543210");
		request.setChannel("mobile");
		request.setCorrelationKey("test-correlation");
		request.setIdContext("test-context");
		request.setSiteDomain("IN");
		
		Map<String, String> headers = new HashMap<>();
		
		UserServiceResponse userServiceResponse = new UserServiceResponse();
		UserServiceResult result = new UserServiceResult();
		ExtendedUser extendedUser = new ExtendedUser();
		extendedUser.setUuid("test-uuid-123");
		result.setExtendedUser(extendedUser);
		userServiceResponse.setResult(result);
		
		PartnerAffiliateDetails partnerAffiliateDetails = new PartnerAffiliateDetails();
		AffiliateData affiliateData = new AffiliateData();
		affiliateData.setPermissions(null);
		partnerAffiliateDetails.setData(affiliateData);
		
		Mockito.when(commonHelper.getUserDetails(
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.isNull(), Mockito.any()
		)).thenReturn(userServiceResponse);
		
		Mockito.when(mobLandingExecutor.fetchCTAMoAffId(
			Mockito.eq("test-uuid-123"), 
			Mockito.eq("booking"), 
			Mockito.eq("hotel")
		)).thenReturn(partnerAffiliateDetails);

		// Act
		landingService.getFeatureBasedAccessDetails(request, headers);
	}

	@Test(expected = ClientGatewayException.class)
	public void testGetFeatureBasedAccessDetails_HotelsPermissionNull() throws ClientGatewayException {
		// Arrange
		SearchWrapperInputRequest request = new SearchWrapperInputRequest();
		request.setMmtAuth("test-auth");
		request.setEmail("<EMAIL>");
		request.setMobile("9876543210");
		request.setChannel("mobile");
		request.setCorrelationKey("test-correlation");
		request.setIdContext("test-context");
		request.setSiteDomain("IN");
		
		Map<String, String> headers = new HashMap<>();
		
		UserServiceResponse userServiceResponse = new UserServiceResponse();
		UserServiceResult result = new UserServiceResult();
		ExtendedUser extendedUser = new ExtendedUser();
		extendedUser.setUuid("test-uuid-123");
		result.setExtendedUser(extendedUser);
		userServiceResponse.setResult(result);
		
		PartnerAffiliateDetails partnerAffiliateDetails = new PartnerAffiliateDetails();
		AffiliateData affiliateData = new AffiliateData();
		Permissions permissions = new Permissions();
		permissions.setHotels(null);
		affiliateData.setPermissions(permissions);
		partnerAffiliateDetails.setData(affiliateData);
		
		Mockito.when(commonHelper.getUserDetails(
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.isNull(), Mockito.any()
		)).thenReturn(userServiceResponse);
		
		Mockito.when(mobLandingExecutor.fetchCTAMoAffId(
			Mockito.eq("test-uuid-123"), 
			Mockito.eq("booking"), 
			Mockito.eq("hotel")
		)).thenReturn(partnerAffiliateDetails);

		// Act
		landingService.getFeatureBasedAccessDetails(request, headers);
	}

	@Test(expected = ClientGatewayException.class)
	public void testGetFeatureBasedAccessDetails_JsonParseException() throws ClientGatewayException {
		// Arrange
		SearchWrapperInputRequest request = new SearchWrapperInputRequest();
		request.setMmtAuth("test-auth");
		request.setEmail("<EMAIL>");
		request.setMobile("9876543210");
		request.setChannel("mobile");
		request.setCorrelationKey("test-correlation");
		request.setIdContext("test-context");
		request.setSiteDomain("IN");
		
		Map<String, String> headers = new HashMap<>();
		
		UserServiceResponse userServiceResponse = new UserServiceResponse();
		UserServiceResult result = new UserServiceResult();
		ExtendedUser extendedUser = new ExtendedUser();
		extendedUser.setUuid("test-uuid-123");
		result.setExtendedUser(extendedUser);
		userServiceResponse.setResult(result);
		
		Mockito.when(commonHelper.getUserDetails(
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.isNull(), Mockito.any()
		)).thenReturn(userServiceResponse);
		
		Mockito.when(mobLandingExecutor.fetchCTAMoAffId(
			Mockito.eq("test-uuid-123"), 
			Mockito.eq("booking"), 
			Mockito.eq("hotel")
		)).thenThrow(new com.mmt.hotels.clientgateway.exception.JsonParseException(
			DependencyLayer.CLIENTGATEWAY, ErrorType.MARSHALLING, "JSON_PARSE_ERROR", "JSON parse error"));

		// Act
		landingService.getFeatureBasedAccessDetails(request, headers);
	}

	@Test(expected = ClientGatewayException.class)
	public void testGetFeatureBasedAccessDetails_GenericException() throws ClientGatewayException {
		// Arrange
		SearchWrapperInputRequest request = new SearchWrapperInputRequest();
		request.setMmtAuth("test-auth");
		request.setEmail("<EMAIL>");
		request.setMobile("9876543210");
		request.setChannel("mobile");
		request.setCorrelationKey("test-correlation");
		request.setIdContext("test-context");
		request.setSiteDomain("IN");
		
		Map<String, String> headers = new HashMap<>();
		
		Mockito.when(commonHelper.getUserDetails(
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
			Mockito.anyString(), Mockito.isNull(), Mockito.any()
		)).thenThrow(new RuntimeException("Unexpected error"));

		// Act
		landingService.getFeatureBasedAccessDetails(request, headers);
	}
}
