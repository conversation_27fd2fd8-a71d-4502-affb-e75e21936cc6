package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.deeplink.DeepLinkGenerationRequest;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.response.deeplink.DeepLinkGenerationResponse;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchHotelsResponseTransformer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;



@ExtendWith(MockitoExtension.class)
class DeepLinkGenerationServiceTest {

    @Mock
    private Utility utility;

    @Mock
    private MetricErrorLogger metricErrorLogger;

    @Mock
    private SearchHotelsFactory searchHotelsFactory;

    @Mock
    private OrchSearchHotelsResponseTransformer orchSearchHotelsResponseTransformer;

    @Mock
    private ExceptionHandlerResponse exceptionHandlerResponse;

    @InjectMocks
    private DeepLinkGenerationService deepLinkGenerationService;

    private DeepLinkGenerationRequest validRequest;

    @BeforeEach
    void setUp() {
        // Set up properties using ReflectionTestUtils
        ReflectionTestUtils.setField(deepLinkGenerationService, "rootLevelDeeplinkUrl", 
            "https://www.makemytrip.com/hotels/hotel-listing/");
        ReflectionTestUtils.setField(deepLinkGenerationService, "rootLevelDeeplinkHappayUrl", 
            "https://www.makemytrip.com/hotels/hotel-listing/?happay=true");

        // Create valid request first
        validRequest = createValidRequest();

        // Set up lenient mock behavior for factory and transformer (used in most tests)
        lenient().when(searchHotelsFactory.getSearchHotelsResponseService(anyString())).thenReturn(orchSearchHotelsResponseTransformer);
        lenient().when(orchSearchHotelsResponseTransformer.prepareListingSharingUrl(any(), anyString(), eq(false), eq(false), isNull()))
            .thenReturn("https://www.makemytrip.com/hotels/hotel-listing/?checkin=08152025&checkout=08162025&city=CTBOM&searchText=Mumbai");
        lenient().when(orchSearchHotelsResponseTransformer.appendAreaOrPoiUrlParameter(anyString(), any()))
            .thenAnswer(invocation -> invocation.getArgument(0)); // Return the same URL

        // Set up exception handling mocks for validation error tests
        ClientGatewayException mockException = new ClientGatewayException(DependencyLayer.CLIENTGATEWAY, ErrorType.VALIDATION, "TEST_ERROR", "Test error message");
        lenient().when(exceptionHandlerResponse.getClientGatewayException()).thenReturn(mockException);
    }

    @Test
    void testGenerateDeepLink_Success() throws ClientGatewayException {
        // Arrange
        when(utility.isDistributeRoomStayCandidates(anyList(), any())).thenReturn(true);
        when(utility.buildRscValue(anyList())).thenReturn("2e0e");

        // Act
        DeepLinkGenerationResponse response = deepLinkGenerationService.generateDeepLink(validRequest);

        // Assert
        assertNotNull(response);
        assertNotNull(response.getData());
        assertTrue(response.getData().contains("&rsc=2e0e"));
        verify(searchHotelsFactory).getSearchHotelsResponseService("DESKTOP");
        verify(orchSearchHotelsResponseTransformer).prepareListingSharingUrl(any(), anyString(), eq(false), eq(false), isNull());
        verify(orchSearchHotelsResponseTransformer).appendAreaOrPoiUrlParameter(anyString(), any());
    }

    @Test
    void testGenerateDeepLink_WithoutRscDistribution() throws ClientGatewayException {
        // Arrange
        when(utility.isDistributeRoomStayCandidates(anyList(), any())).thenReturn(false);

        // Act
        DeepLinkGenerationResponse response = deepLinkGenerationService.generateDeepLink(validRequest);

        // Assert
        assertNotNull(response);
        assertNotNull(response.getData());
        assertFalse(response.getData().contains("&rsc="));
        verify(utility, never()).buildRscValue(anyList());
    }


    @Test
    void testGenerateDeepLink_DifferentClientTypes() throws ClientGatewayException {
        // Test Android
        validRequest.getDeviceDetails().setDeviceType("Android");
        deepLinkGenerationService.generateDeepLink(validRequest);
        verify(searchHotelsFactory).getSearchHotelsResponseService("ANDROID");

        // Reset and test iOS
        reset(searchHotelsFactory);
        when(searchHotelsFactory.getSearchHotelsResponseService(anyString())).thenReturn(orchSearchHotelsResponseTransformer);
        validRequest.getDeviceDetails().setDeviceType("iOS");
        deepLinkGenerationService.generateDeepLink(validRequest);
        verify(searchHotelsFactory).getSearchHotelsResponseService("IOS");

        // Reset and test null device type (should default to DESKTOP)
        reset(searchHotelsFactory);
        when(searchHotelsFactory.getSearchHotelsResponseService(anyString())).thenReturn(orchSearchHotelsResponseTransformer);
        validRequest.setDeviceDetails(null);
        deepLinkGenerationService.generateDeepLink(validRequest);
        verify(searchHotelsFactory).getSearchHotelsResponseService("DESKTOP");
    }

    @Test
    void testGenerateDeepLink_NullRequest() {
        // The service will catch IllegalArgumentException and convert it to ClientGatewayException
        // Act & Assert
        assertThrows(ClientGatewayException.class, () -> {
            deepLinkGenerationService.generateDeepLink(null);
        });
    }

    @Test
    void testGenerateDeepLink_NullSearchCriteria() {
        // Arrange
        DeepLinkGenerationRequest request = createValidRequest();
        request.setSearchCriteria(null);

        // Act & Assert
        assertThrows(ClientGatewayException.class, () -> {
            deepLinkGenerationService.generateDeepLink(request);
        });
    }

    @Test
    void testGenerateDeepLink_MissingCheckIn() {
        // Arrange
        DeepLinkGenerationRequest request = createValidRequest();
        request.getSearchCriteria().setCheckIn(null);

        // Act & Assert
        assertThrows(ClientGatewayException.class, () -> {
            deepLinkGenerationService.generateDeepLink(request);
        });
    }

    @Test
    void testGenerateDeepLink_MissingCheckOut() {
        // Arrange
        DeepLinkGenerationRequest request = createValidRequest();
        request.getSearchCriteria().setCheckOut(null);

        // Act & Assert
        assertThrows(ClientGatewayException.class, () -> {
            deepLinkGenerationService.generateDeepLink(request);
        });
    }

    @Test
    void testGenerateDeepLink_EmptyRoomStayCandidates() {
        // Arrange
        DeepLinkGenerationRequest request = createValidRequest();
        request.getSearchCriteria().setRoomStayCandidates(Collections.emptyList());

        // Act & Assert
        assertThrows(ClientGatewayException.class, () -> {
            deepLinkGenerationService.generateDeepLink(request);
        });
    }

    @Test
    void testGenerateDeepLink_InvalidAdultCount() {
        // Arrange
        DeepLinkGenerationRequest request = createValidRequest();
        request.getSearchCriteria().getRoomStayCandidates().get(0).setAdultCount(0);

        // Act & Assert
        assertThrows(ClientGatewayException.class, () -> {
            deepLinkGenerationService.generateDeepLink(request);
        });
    }

    @Test
    void testGenerateDeepLink_WithRscValue() throws ClientGatewayException {
        // Arrange
        when(utility.isDistributeRoomStayCandidates(anyList(), any())).thenReturn(true);
        when(utility.buildRscValue(anyList())).thenReturn("1e2e5e7e");

        // Act
        DeepLinkGenerationResponse response = deepLinkGenerationService.generateDeepLink(validRequest);

        // Assert
        assertNotNull(response);
        assertNotNull(response.getData());
        assertTrue(response.getData().contains("&rsc=1e2e5e7e"));
    }

    @Test
    void testGenerateDeepLink_EmptyRscValue() throws ClientGatewayException {
        // Arrange
        when(utility.isDistributeRoomStayCandidates(anyList(), any())).thenReturn(true);
        when(utility.buildRscValue(anyList())).thenReturn("");

        // Act
        DeepLinkGenerationResponse response = deepLinkGenerationService.generateDeepLink(validRequest);

        // Assert
        assertNotNull(response);
        assertNotNull(response.getData());
        assertFalse(response.getData().contains("&rsc="));
    }

    @Test
    void testGenerateDeepLink_NoAdditionalParams() throws ClientGatewayException {
        // Arrange
        validRequest.setAdditionalParams(null);

        // Act
        DeepLinkGenerationResponse response = deepLinkGenerationService.generateDeepLink(validRequest);

        // Assert
        assertNotNull(response);
        // Should use default URL template
        verify(orchSearchHotelsResponseTransformer).prepareListingSharingUrl(any(), 
            eq("https://www.makemytrip.com/hotels/hotel-listing/"), eq(false), eq(false), isNull());
    }

    // Helper method to create valid request
    private DeepLinkGenerationRequest createValidRequest() {
        DeepLinkGenerationRequest request = new DeepLinkGenerationRequest();
        
        // Create search criteria
        SearchHotelsCriteria criteria = new SearchHotelsCriteria();
        criteria.setCheckIn("2025-08-15");
        criteria.setCheckOut("2025-08-16");
        criteria.setLocationId("CTBOM");
        criteria.setCityCode("CTBOM");
        criteria.setCityName("Mumbai");
        criteria.setCountryCode("IN");
        criteria.setLocationType("city");
        criteria.setCurrency("INR");

        // Create room stay candidate
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setAdultCount(2);
        roomStayCandidate.setChildAges(Collections.emptyList());
        criteria.setRoomStayCandidates(Arrays.asList(roomStayCandidate));

        request.setSearchCriteria(criteria);

        // Create request details
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource("WEB");
        requestDetails.setIdContext("HOTELS");
        requestDetails.setVisitorId("test-visitor-123");
        requestDetails.setVisitNumber(1);
        requestDetails.setSiteDomain("in");
        request.setRequestDetails(requestDetails);

        // Create device details
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setDeviceType("DESKTOP");
        deviceDetails.setDeviceId("test-device-123");
        deviceDetails.setAppVersion("1.0");
        deviceDetails.setBookingDevice("DESKTOP");
        request.setDeviceDetails(deviceDetails);

        // Create expDataMap
        Map<String, String> expDataMap = new HashMap<>();
        request.setExpDataMap(expDataMap);

        return request;
    }
}