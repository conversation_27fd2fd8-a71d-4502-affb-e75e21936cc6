package com.mmt.hotels.clientgateway.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.FilterHelper;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.restexecutors.CardEngineExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.transformer.request.SearchHotelsRequestTransformer;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

@RunWith(MockitoJUnitRunner.class)
public class CardEngineServiceTest {

    @InjectMocks
    private CardEngineService cardEngineService;

    @Mock
    private CardEngineExecutor cardEngineExecutor;

    @Spy
    private ThreadPoolTaskExecutor listingThreadPool;

    @Spy
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    SearchHotelsRequestTransformer searchHotelsRequestTransformer;

    @Mock
    CommonHelper commonHelper;

    @Mock
    SearchHotelsFactory searchHotelsFactory;

    @Before
    public void init(){
        MockitoAnnotations.initMocks(this);
        listingThreadPool.setCorePoolSize(1);
        listingThreadPool.initialize();
    }

    @Test
    public void executeCardEngineServiceAsyncTest() throws ClientGatewayException, ExecutionException, InterruptedException {
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        searchHotelsRequest.setCorrelationKey("ck123");
        SearchHotelsCriteria searchHotelsCriteria = new SearchHotelsCriteria();
        searchHotelsCriteria.setLocationId("CTKUU");
        searchHotelsCriteria.setCountryCode("IN");
        searchHotelsCriteria.setCheckIn("25-12-2023");
        searchHotelsCriteria.setCheckOut("26-12-2023");
        searchHotelsRequest.setSearchCriteria(searchHotelsCriteria);
        List<Filter> filterCriteria = new ArrayList<>();
        Filter filter = new Filter(FilterGroup.MERGE_PROPERTY_TYPE,"Villa");
        filterCriteria.add(filter);
        searchHotelsRequest.setFilterCriteria(filterCriteria);
        ReflectionTestUtils.setField(objectMapperUtil,"mapper", new ObjectMapper());
        ReflectionTestUtils.setField(searchHotelsRequestTransformer,"filterHelper", new FilterHelper());
        Mockito.when(cardEngineExecutor.getCard(Mockito.any())).thenReturn("cardEngine Response");
        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchHotelsRequestTransformer);
        Mockito.when(searchHotelsRequestTransformer.convertSearchRequest(Mockito.any(),Mockito.any())).thenReturn(new SearchWrapperInputRequest());
        Future<String> cardEngineServiceAsync = cardEngineService.executeCardEngineServiceAsync(searchHotelsRequest, "ck123",null,null);
        String cardEngineResponseString = cardEngineServiceAsync.get();
        Assert.assertNotNull(cardEngineResponseString);
    }

    @Test
    public void testSetCardIdInSearchHotelRequest() {
        // Arrange
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setSubPageContext("LISTING_COLLECTION");
        searchHotelsRequest.setRequestDetails(requestDetails);

        // Act
        cardEngineService.setCardIdInSearchHotelRequest(searchHotelsRequest);

        // Assert
        Assert.assertEquals(Constants.DPT_COLLECTIONS, searchHotelsRequest.getCardId());

        // Test default case
        requestDetails.setSubPageContext("OTHER_CONTEXT");
        searchHotelsRequest.setRequestDetails(requestDetails);

        // Act
        cardEngineService.setCardIdInSearchHotelRequest(searchHotelsRequest);

        // Assert
        Assert.assertEquals(Constants.SEASON_CARD_ID, searchHotelsRequest.getCardId());
    }

    @Test
    public void executeCardEngineServiceAsync_shouldReturnEmptyString_whenLastHotelIdIsNotEmpty() throws ExecutionException, InterruptedException {
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        SearchHotelsCriteria searchHotelsCriteria = new SearchHotelsCriteria();
        searchHotelsCriteria.setLastHotelId("HOTEL123"); // This should trigger early return
        searchHotelsRequest.setSearchCriteria(searchHotelsCriteria);

        Future<String> result = cardEngineService.executeCardEngineServiceAsync(searchHotelsRequest, "ck123", null, null);
        String response = result.get();

        Assert.assertEquals("", response);
    }

    @Test
    public void executeCardEngineServiceAsync_shouldReturnNull_whenExceptionOccurs() throws ExecutionException, InterruptedException, ClientGatewayException {
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        SearchHotelsCriteria searchHotelsCriteria = new SearchHotelsCriteria();
        searchHotelsCriteria.setLocationId("CTKUU");
        searchHotelsRequest.setSearchCriteria(searchHotelsCriteria);

        // Mock to throw exception
        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchHotelsRequestTransformer);
        Mockito.when(searchHotelsRequestTransformer.convertSearchRequest(Mockito.any(), Mockito.any())).thenThrow(new RuntimeException("Test exception"));

        Future<String> result = cardEngineService.executeCardEngineServiceAsync(searchHotelsRequest, "ck123", null, null);
        String response = result.get();

        Assert.assertNull(response);
    }

    @Test
    public void getCard_shouldProcessSuccessfully_whenCommonModifierResponseIsNull() throws ClientGatewayException {
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        SearchHotelsCriteria searchHotelsCriteria = new SearchHotelsCriteria();
        searchHotelsCriteria.setLocationId("CTKUU");
        searchHotelsRequest.setSearchCriteria(searchHotelsCriteria);

        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchHotelsRequestTransformer);
        Mockito.when(searchHotelsRequestTransformer.convertSearchRequest(Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());
        Mockito.when(cardEngineExecutor.getCard(Mockito.any())).thenReturn("card response");

        String result = cardEngineService.getCard(searchHotelsRequest, null, null);

        Assert.assertEquals("card response", result);
        Mockito.verify(commonHelper).processRequest(Mockito.any(), Mockito.any(), Mockito.any());
    }
}
