package com.mmt.hotels.clientgateway.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.LogicalException;
import com.mmt.hotels.clientgateway.request.ugc.ClientLoadProgramRequest;
import com.mmt.hotels.clientgateway.request.ugc.ClientSubmitApiRequest;
import com.mmt.hotels.clientgateway.request.ugc.QuestionSet;
import com.mmt.hotels.clientgateway.request.ugc.Ugc;
import com.mmt.hotels.clientgateway.response.ugc.*;
import com.mmt.hotels.clientgateway.restexecutors.UgcExecutor;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;
import org.mockito.quality.Strictness;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.MultipartRequest;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UgcServiceTest {
    @InjectMocks
    private UgcExecutor ugcExecutor;

    @InjectMocks
    private UgcService ugcService;

    @InjectMocks
    UgcExecutor ugcExecutorObj;

    @InjectMocks
    PolyglotService polyglotServiceObj;

    @Mock
    private CommonConfigConsul commonConfigConsul;

    @Rule
    public MockitoRule rule = MockitoJUnit.rule().strictness(Strictness.STRICT_STUBS);

    @Before
    public void setUp() {
        ugcExecutorObj = Mockito.mock(UgcExecutor.class);
        Field ugcServiceExecutorField = ReflectionUtils.findField(UgcService.class, "ugcExecutor");
        ReflectionUtils.makeAccessible(ugcServiceExecutorField);
        ReflectionUtils.setField(ugcServiceExecutorField, ugcService, ugcExecutorObj);

        polyglotServiceObj = Mockito.mock(PolyglotService.class);
        Field polyglotServiceField = ReflectionUtils.findField(UgcService.class, "polyglotService");
        ReflectionUtils.makeAccessible(polyglotServiceField);
        ReflectionUtils.setField(polyglotServiceField, ugcService, polyglotServiceObj);

    }

    @Test
    public void testInit() {
        when(commonConfigConsul.getRewardIconUrl()).thenReturn("http://example.com/rewardIcon.png");
        when(commonConfigConsul.getReviewLevel1Count()).thenReturn(10);
        when(commonConfigConsul.getReviewLevel1MaxPer()).thenReturn(20);
        when(commonConfigConsul.getReviewLevel1MaxAmount()).thenReturn(100);
        when(commonConfigConsul.getReviewLevel2Count()).thenReturn(15);
        when(commonConfigConsul.getReviewLevel2MaxPer()).thenReturn(25);
        when(commonConfigConsul.getReviewLevel2MaxAmount()).thenReturn(150);
        when(commonConfigConsul.getReviewLevel3Count()).thenReturn(20);
        when(commonConfigConsul.getReviewLevel3MaxPer()).thenReturn(30);
        when(commonConfigConsul.getReviewLevel3MaxAmount()).thenReturn(200);

        ugcService.init();
    }

    @Test
    public void fetchProgram18QuestionTest() throws ClientGatewayException, IOException {
        ClientLoadProgramRequest clientLoadProgramRequest = new ClientLoadProgramRequest();
        Ugc ugc = new Ugc();
        ugc.setBookingId("1234");
        clientLoadProgramRequest.setUgc(ugc);
        Map<String,String[]> params = new HashMap<>();
        UgcResponse ugcResponse = new UgcResponse();
        ugcResponse.setUgcId("1234");
        ugcResponse.setStatus("Success");
        QuestionData questionData = new QuestionData();
        questionData.setUgcId("1234");
        LobData lobData = new LobData();
        lobData.setContentId("1234");
        lobData.setHotelName("h1");
        lobData.setHotelId("1234");
        lobData.setCityCode("city1");
        questionData.setLobData(lobData);
        Map<String, LevelConfig> levelConfigMap = new HashMap<>();
        LevelConfig levelConfig = new LevelConfig();
        levelConfig.setLevelName("1234");
        levelConfig.setLevelText("text");
        levelConfig.setLevelTotalQuestions(1);
        levelConfig.setLevelTotalPageNumbers(2);
        levelConfigMap.put("1", levelConfig);
        levelConfigMap.put("2", levelConfig);
        levelConfigMap.put("3", levelConfig);
        questionData.setLevelConfig(levelConfigMap);
        questionData.setNumberOfLevels(3);

        Map<String, QuestionData> questionDataMap = new HashMap<>();
        questionDataMap.put("1234", questionData);

        Map<String, QuestionData> questionDataMap1 = new HashMap<>();
        questionDataMap1.put("1234", questionData);
        questionData.setSubmittedQuestions(questionDataMap1);
        List<Question> questions = new ArrayList<>();
        Question question = new Question();
        question.setQuestionId("1234");
        question.setLevel(1);
        question.setQuestionTitle("title");
        question.setAnswerProvided(null);
        question.setAnswerType("type");
        question.setOptionsInfo(null);
        questions.add(question);
        questionData.setQuestions(questions);
        questionData.setNumberOfLevels(3);
        questionData.setLevelConfig(levelConfigMap);

        Validators validators = new Validators();
        validators.setMandatory(true);
        question.setValidators(validators);
        ugcResponse.setQuestionData(questionData);
//        Mockito.when(ugcExecutorObj.fetchProgram18(Mockito.any(), Mockito.any())).thenReturn(ugcResponse);
//        Mockito.when(ugcExecutorObj.submitAnswersToPlatforms(Mockito.any(), Mockito.any())).thenReturn(ugcResponse);
        ClientUgcResponse resp = ugcService.fetchProgram18Question(clientLoadProgramRequest, params, "1234", new HashMap<>());
        Assert.assertEquals(resp.isSuccess(), false);
        levelConfigMap.clear();
        levelConfigMap.put("1234", levelConfig);
        ClientUgcResponse resp1 = ugcService.fetchProgram18Question(clientLoadProgramRequest, params, "1234", new HashMap<>());
        Assert.assertEquals(resp1.isSuccess(), false);

        ReviewTerminationMessage reviewTerminationMessage = new ReviewTerminationMessage();
        reviewTerminationMessage.setText("message");
        reviewTerminationMessage.setTitle("title");
        reviewTerminationMessage.setIcon("icon");
        questionData.setReviewTerminationMessage(reviewTerminationMessage);
        levelConfigMap.clear();
        levelConfigMap.put("1", levelConfig);
        levelConfigMap.put("2", levelConfig);
        levelConfigMap.put("3", levelConfig);
        ClientUgcResponse resp2 = ugcService.fetchProgram18Question(clientLoadProgramRequest, params, "1234", new HashMap<>());
        Assert.assertEquals(resp2.isSuccess(), false);
    }

    @Test
    public void createSubmitAnswersRequestTest() {
        // Arrange
        ClientSubmitApiRequest clientSubmitApiRequest = new ClientSubmitApiRequest();
        QuestionSet questionSet1 = new QuestionSet();
        questionSet1.setQuestionType("IMAGE");
        MediaDetails mediaDetails = new MediaDetails();
        mediaDetails.setMediaId("1234");
        mediaDetails.setMediaUrl("http://www.example.com");
        mediaDetails.setMediaType("IMAGE");
        List<MediaDetails> mediaDetailslist = new ArrayList<>();
        mediaDetailslist.add(mediaDetails);
        questionSet1.setMediaDetails(mediaDetailslist);

        // Assuming the request has a property named 'questionId'
        clientSubmitApiRequest.setUgcId("1234");
        clientSubmitApiRequest.setUserPage(1);
        clientSubmitApiRequest.setQuestion(questionSet1);
        List<ImageUploadResult> imageUploadResults = new ArrayList<>();
        ImageUploadResult imageUploadResult = new ImageUploadResult();
        ImageData imageData = new ImageData();
        imageData.setId("1234");
        imageData.setUrl("http://www.example.com");
        imageData.setMediaType("IMAGE");
        List<ImageData> imageDataList = new ArrayList<>();
        imageDataList.add(imageData);
        imageUploadResult.setFileList(imageDataList);
        imageUploadResults.add(imageUploadResult);

        String result = ugcExecutor.createSubmitAnswersRequest(clientSubmitApiRequest, imageUploadResults);
        assertNotNull(result);

        QuestionSet questionSet2 = new QuestionSet();
        questionSet2.setQuestionType("TEXT_AREA");
        questionSet2.setText("text");
        questionSet2.setTitle("title");
        clientSubmitApiRequest.setQuestion(questionSet2);
        String result2 = ugcExecutor.createSubmitAnswersRequest(clientSubmitApiRequest, imageUploadResults);

        assertNotNull(result2);

        QuestionSet questionSet3 = new QuestionSet();
        questionSet3.setQuestionType("DEFAULT");
        List<String> selected = new ArrayList<>();
        selected.add("1");
        questionSet3.setSelected(selected);

        clientSubmitApiRequest.setQuestion(questionSet3);

        String result3 = ugcExecutor.createSubmitAnswersRequest(clientSubmitApiRequest, imageUploadResults);
        assertNotNull(result3);

        QuestionSet questionSet4 = new QuestionSet();
        questionSet4.setRating(1);
        questionSet4.setQuestionType("RATING");
        clientSubmitApiRequest.setQuestion(questionSet4);

        String result4 = ugcExecutor.createSubmitAnswersRequest(clientSubmitApiRequest, imageUploadResults);
        assertNotNull(result4);

        questionSet4.setQuestionType("IMAGE");
        String result5 = ugcExecutor.createSubmitAnswersRequest(clientSubmitApiRequest, imageUploadResults);
        assertNotNull(result5);
    }

    @Test
    public void convertQuestionDataToQuestionDetailsTest(){
        QuestionData questionData = new QuestionData();
        LobData lobData = new LobData();
        lobData.setContentId("1234");
        lobData.setHotelName("h1");
        lobData.setHotelId("1234");
        lobData.setCityCode("city1");
        questionData.setLobData(lobData);
        Map<String, LevelConfig> levelConfigMap = new HashMap<>();
        LevelConfig levelConfig = new LevelConfig();
        levelConfig.setLevelName("1234");
        levelConfig.setLevelText("text");
        levelConfig.setLevelTotalQuestions(1);
        levelConfig.setLevelTotalPageNumbers(2);
        levelConfigMap.put("1", levelConfig);
        levelConfigMap.put("2", levelConfig);
        levelConfigMap.put("3", levelConfig);
        questionData.setLevelConfig(levelConfigMap);
        questionData.setNumberOfLevels(3);
        List<Question> questions = new ArrayList<>();
        Question question = new Question();
        question.setQuestionId("1234");
        question.setLevel(1);
        question.setQuestionTitle("title");
        question.setAnswerProvided(null);
        question.setAnswerType("type");
        question.setOptionsInfo(null);
        questions.add(question);
        questionData.setQuestions(questions);
        Mockito.when(polyglotServiceObj.getTranslatedData(Mockito.any())).thenReturn("1");

        Validators validators = new Validators();
        validators.setMandatory(false);
        question.setValidators(validators);

        // Act
        QuestionDetails questionDetails = ugcService.convertQuestionDataToQuestionDetails(questionData);

        // Assert
        assertNotNull(questionDetails);
        assertEquals(question.getQuestionId(), questionDetails.getId());
        assertEquals(question.getQuestionTitle(), questionDetails.getQuestionDescription());
        assertEquals(question.getAnswerType(), questionDetails.getDynamicQuestionType());
        assertEquals(question.getLevel(), questionDetails.getLevel());
        assertEquals(question.getValidators().isMandatory(), questionDetails.isMandatory());
    }

    @Test
    public void UgcResponseClientUgcResponseMapperTest() {
        // Arrange
        UgcResponse ugcResponse = new UgcResponse();
        ugcResponse.setUgcId("1234");
        ugcResponse.setStatus("Success");
        QuestionData questionData = new QuestionData();
        questionData.setUgcId("1234");
        LobData lobData = new LobData();
        lobData.setContentId("1234");
        lobData.setHotelName("h1");
        lobData.setHotelId("1234");
        lobData.setCityCode("city1");
        questionData.setLobData(lobData);
        Map<String, LevelConfig> levelConfigMap = new HashMap<>();
        LevelConfig levelConfig = new LevelConfig();
        levelConfig.setLevelName("1234");
        levelConfig.setLevelText("text");
        levelConfig.setLevelTotalQuestions(1);
        levelConfig.setLevelTotalPageNumbers(2);
        levelConfigMap.put("1", levelConfig);
        levelConfigMap.put("2", levelConfig);
        levelConfigMap.put("3", levelConfig);
        questionData.setLevelConfig(levelConfigMap);
        questionData.setNumberOfLevels(3);
        ugcResponse.setQuestionData(questionData);
        ClientUgcResponse clientUgcResponse = new ClientUgcResponse();

        // Act
        ugcService.UgcResponseClientUgcResponseMapper(ugcResponse,clientUgcResponse, "INR");

        // Assert
        assertNotNull(clientUgcResponse);
    }

    @Test
    public void getErrorResponseForClientTest(){
        Exception exception1 = new Exception("message");
        ClientUgcResponse clientUgcResponse = ugcService.getErrorResponseForClient(exception1);
        assertNotNull(clientUgcResponse);
    }
}