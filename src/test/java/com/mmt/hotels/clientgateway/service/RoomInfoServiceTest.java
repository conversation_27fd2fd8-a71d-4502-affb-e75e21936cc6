package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.RoomInfoRequest;
import com.mmt.hotels.clientgateway.response.RoomInfoResponse;
import com.mmt.hotels.clientgateway.restexecutors.HESRestExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.RoomInfoFactory;
import com.mmt.hotels.clientgateway.transformer.response.RoomInfoResponseTransformer;
import com.mmt.hotels.model.response.txn.PersistanceMultiRoomResponseEntity;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class RoomInfoServiceTest {
    @Mock
    private HESRestExecutor restExecutor;

    @InjectMocks
    private RoomInfoService roomInfoService;

    @Mock
    private RoomInfoFactory roomInfoFactory;

    @Test
    public void testGetRoomInfo() throws ClientGatewayException {
        Mockito.when(restExecutor.getTxnData(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new PersistanceMultiRoomResponseEntity());
        RoomInfoResponseTransformer transformer = Mockito.mock(RoomInfoResponseTransformer.class);
        Mockito.when(roomInfoFactory.getResponseService(Mockito.any())).thenReturn(transformer);
        Mockito.when(transformer.transformRoomInfoResponse(Mockito.any(), Mockito.any())).thenReturn(new RoomInfoResponse());
        RoomInfoResponse roomInfoResponse = roomInfoService.getRoomInfo(new RoomInfoRequest(), new HashMap<>());
        Assert.assertNotNull(roomInfoResponse);
    }

    @Test
    public void testGetRoomInfos() throws ClientGatewayException {
        Mockito.when(restExecutor.getTxnData(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new PersistanceMultiRoomResponseEntity());
        RoomInfoResponseTransformer transformer = Mockito.mock(RoomInfoResponseTransformer.class);
        Mockito.when(roomInfoFactory.getResponseService(Mockito.any())).thenReturn(transformer);
        Mockito.when(transformer.transformRoomInfoResponses(Mockito.any(), Mockito.any())).thenReturn(new RoomInfoResponse());
        RoomInfoResponse roomInfoResponse = roomInfoService.getRoomInfos(new RoomInfoRequest(), new HashMap<>());
        Assert.assertNotNull(roomInfoResponse);
    }
}
