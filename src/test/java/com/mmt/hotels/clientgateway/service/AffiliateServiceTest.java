package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.PaymentHelper;
import com.mmt.hotels.clientgateway.request.CreateQuoteRequest;
import com.mmt.hotels.clientgateway.request.GetQuoteRequest;
import com.mmt.hotels.clientgateway.request.UpdateAffiliateFeeRequest;
import com.mmt.hotels.clientgateway.response.AvailRoomsResponse;
import com.mmt.hotels.clientgateway.response.ValidateCouponResponseBody;
import com.mmt.hotels.clientgateway.response.affiliate.UpdatedAffiliateFeeResponse;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.restexecutors.AffiliateExecutor;
import com.mmt.hotels.clientgateway.restexecutors.UserServiceExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.AffiliateFactory;
import com.mmt.hotels.clientgateway.transformer.factory.AvailRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.factory.DiscountServiceFactory;
import com.mmt.hotels.clientgateway.transformer.response.AffiliateResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.AvailRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.DiscountResponseTransformer;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.affiliate.CreateQuoteResponse;
import com.mmt.hotels.model.affiliate.GetQuoteResponse;
import com.mmt.hotels.model.affiliate.UpdateAffiliateFeeResponse;
import com.mmt.hotels.model.request.CreateQuoteRequestBody;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.RoomCriterion;
import com.mmt.hotels.model.request.payment.TravelerDetail;
import com.mmt.hotels.model.response.GetQuoteResponseBody;
import com.mmt.hotels.model.response.discount.ValidateCouponResponse;
import com.mmt.hotels.model.response.pricing.HotelRates;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.pricing.SpecialRequest;
import com.mmt.hotels.model.response.pricing.SpecialRequestCategory;
import com.mmt.hotels.pojo.response.userservice.UserDetailsDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class AffiliateServiceTest {

    @InjectMocks
    AffiliateService affiliateService;

    @Mock
    private AffiliateExecutor affiliateExecutor;

    @Mock
    private AffiliateFactory affiliateFactory;

    @Mock
    private MetricErrorLogger metricErrorLogger;

    @Mock
    private CommonHelper commonHelper;

    @Mock
    private PaymentHelper payHelper;

    @Mock
    private AvailRoomsFactory availRoomsFactory;

    @Mock
    private DiscountServiceFactory discountServiceFactory;

    @InjectMocks
    private CommonResponseTransformer commonResponseTransformer;

    @Mock
    private AffiliateResponseTransformer affiliateResponseTransformer;

    @Mock
    private AvailRoomsResponseTransformer availRoomsResponseTransformer;

    @Mock
    private DiscountResponseTransformer discountResponseTransformer;
    
    @Mock
    private UserServiceExecutor userServiceExecutor;

    @InjectMocks
    private Utility utility;

    @Mock
    private PolyglotService polyglotService;

    @Before
    public void init(){
        commonResponseTransformer = Mockito.spy(new CommonResponseTransformer());
        MockitoAnnotations.initMocks(this);
    }
    @Test
    public void testGetUpdateAffiliateFeeResponse() throws ClientGatewayException, UnsupportedEncodingException {
        Mockito.when(affiliateExecutor.getUpdatedAffiliateFeeResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new UpdateAffiliateFeeResponse.Builder().build());
        Mockito.when(affiliateFactory.getResponseService(Mockito.any())).thenReturn(affiliateResponseTransformer);
        UpdatedAffiliateFeeResponse updatedAffiliateFeeResponse = new UpdatedAffiliateFeeResponse();
        Mockito.when(affiliateResponseTransformer.convertAffiliateFeeUpdateResponse(Mockito.any())).thenReturn(updatedAffiliateFeeResponse);

        UpdatedAffiliateFeeResponse actualResponse = affiliateService.getUpdateAffiliateFeeResponse(new UpdateAffiliateFeeRequest(), new HashMap<>(), new HashMap<>());

        Assert.assertEquals(updatedAffiliateFeeResponse, actualResponse);
    }

    @Test(expected = ClientGatewayException.class)
    public void testGetUpdateAffiliateFeeResponse_Exception() throws ClientGatewayException, UnsupportedEncodingException {
        Mockito.when(affiliateExecutor.getUpdatedAffiliateFeeResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new UpdateAffiliateFeeResponse.Builder().build());
        Mockito.when(affiliateFactory.getResponseService(Mockito.any())).thenReturn(affiliateResponseTransformer);
        UpdatedAffiliateFeeResponse updatedAffiliateFeeResponse = new UpdatedAffiliateFeeResponse();
        Mockito.when(affiliateResponseTransformer.convertAffiliateFeeUpdateResponse(Mockito.any())).thenThrow(new NullPointerException());

        affiliateService.getUpdateAffiliateFeeResponse(new UpdateAffiliateFeeRequest(), new HashMap<>(), new HashMap<>());
    }

    @Test
    public void testCreateQuote() throws ClientGatewayException, UnsupportedEncodingException {
        Mockito.when(affiliateExecutor.getCreateQuoteResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new CreateQuoteResponse.Builder().build());
        Mockito.when(affiliateFactory.getResponseService(Mockito.any())).thenReturn(affiliateResponseTransformer);
        UpdatedAffiliateFeeResponse updatedAffiliateFeeResponse = new UpdatedAffiliateFeeResponse();
        Mockito.when(affiliateResponseTransformer.convertAffiliateCreateQuoteResponse(Mockito.any())).thenReturn(new com.mmt.hotels.clientgateway.response.affiliate.CreateQuoteResponse());

        com.mmt.hotels.clientgateway.response.affiliate.CreateQuoteResponse createQuoteResponse = affiliateService.createQuote(new CreateQuoteRequest(), new HashMap<>(), new HashMap<>());

        Assert.assertNotNull(createQuoteResponse);
    }

    @Test(expected = ClientGatewayException.class)
    public void testCreateQuote_Exception() throws ClientGatewayException, UnsupportedEncodingException {
        Mockito.when(affiliateExecutor.getCreateQuoteResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new CreateQuoteResponse.Builder().build());
        Mockito.when(affiliateFactory.getResponseService(Mockito.any())).thenReturn(affiliateResponseTransformer);
        UpdatedAffiliateFeeResponse updatedAffiliateFeeResponse = new UpdatedAffiliateFeeResponse();
        Mockito.when(affiliateResponseTransformer.convertAffiliateCreateQuoteResponse(Mockito.any())).thenThrow(new NullPointerException());

        com.mmt.hotels.clientgateway.response.affiliate.CreateQuoteResponse createQuoteResponse = affiliateService.createQuote(new CreateQuoteRequest(), new HashMap<>(), new HashMap<>());

        Assert.assertNotNull(createQuoteResponse);
    }

    @Test
    public void testGetQuote() throws ClientGatewayException, UnsupportedEncodingException {
        GetQuoteResponse getQuoteResponseHES = new GetQuoteResponse();
        getQuoteResponseHES.setValidateCouponResponse(new ValidateCouponResponse.Builder().build());
        getQuoteResponseHES.setAvailResponse(new RoomDetailsResponse.Builder().buildHotelRates(new ArrayList<>(Arrays.asList(new HotelRates()))).build());
        getQuoteResponseHES.getAvailResponse().getHotelRates().get(0).setSpecialRequestAvailable(new SpecialRequest());
        SpecialRequestCategory specialRequestCategory = new SpecialRequestCategory();
        specialRequestCategory.setName("Early checkin");
        specialRequestCategory.setCode("101");
        specialRequestCategory.setSubCategories(Arrays.asList(new SpecialRequestCategory()));
        specialRequestCategory.getSubCategories().get(0).setValues(new String[]{"09:00 AM"});
        getQuoteResponseHES.getAvailResponse().getHotelRates().get(0).getSpecialRequestAvailable().setCategories(Arrays.asList(specialRequestCategory));
        getQuoteResponseHES.setSpecialRequest(new SpecialRequest());
        getQuoteResponseHES.getSpecialRequest().setCategories(Arrays.asList(specialRequestCategory));

        AvailRoomsResponse availRoomsResponse = new AvailRoomsResponse();
        availRoomsResponse.setHotelInfo(new HotelResult());

        Mockito.when(affiliateExecutor.getPersistedQuoteDataMerged(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(getQuoteResponseHES);
        Mockito.when(availRoomsFactory.getResponseService(Mockito.any())).thenReturn(availRoomsResponseTransformer);
        Mockito.when(availRoomsResponseTransformer.convertAvailRoomsResponse(Mockito.any(),Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyString(),Mockito.any(), Mockito.anyBoolean(),Mockito.any())).thenReturn(availRoomsResponse);
        Mockito.when(discountServiceFactory.getResponseService(Mockito.any())).thenReturn(discountResponseTransformer);
        Mockito.when(discountResponseTransformer.convertValidateCouponResponse(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.anyBoolean())).thenReturn(new ValidateCouponResponseBody());
        AvailRoomsResponse getQuoteResponseCG = affiliateService.getQuote(new GetQuoteRequest(), new HashMap<>(), new HashMap<>());

        Assert.assertNotNull(getQuoteResponseCG);
    }
    
    @Test
    public void createQuoteOld() throws UnsupportedEncodingException, ClientGatewayException {
    	ReflectionTestUtils.invokeMethod(affiliateService, "populateUserDetailsOld", new CreateQuoteRequestBody(), new UserDetailsDTO());
    	Assert.assertNull(affiliateService.createQuoteOld(new CreateQuoteRequestBody(), new HashMap<>(), new HashMap<>(), "test"));
    	
    }
    
    @Test
    public void getQuoteOld() throws UnsupportedEncodingException, ClientGatewayException {
    	GetQuoteResponse response = new GetQuoteResponse();
    	response.setAvailResponse(new RoomDetailsResponse());
    	response.setTravelerDetailsList(new ArrayList<>());
    	response.getTravelerDetailsList().add(new TravelerDetail());
    	Mockito.when(affiliateExecutor.getPersistedQuoteDataMerged(Mockito.any(), Mockito.anyMap(), Mockito.anyMap())).thenReturn(response);
    	Assert.assertNotNull(affiliateService.getQuoteOld(new com.mmt.hotels.model.affiliate.GetQuoteRequest(), new HashMap<>(), new HashMap<>(), "test"));
    }
    
    @Test
    public void should_PopulateParametersFromAvailRequest_When_PriceByHotelsRequestBodyIsNotNull() {
        // Arrange
        GetQuoteResponseBody response = new GetQuoteResponseBody();
        PriceByHotelsRequestBody priceByHotelsRequestBody = new PriceByHotelsRequestBody();
        priceByHotelsRequestBody.setCheckin("2025-08-13");
        priceByHotelsRequestBody.setCheckout("2025-08-14");
        priceByHotelsRequestBody.setCityCode("RGNCR");
        priceByHotelsRequestBody.setCountryCode("IN");
        priceByHotelsRequestBody.setCurrency("INR");
        List<RoomCriterion> roomCriteria = new ArrayList<>();
        RoomCriterion roomCriterion = new RoomCriterion();
        roomCriterion.setRoomCode("2e0e");
        roomCriteria.add(roomCriterion);
        priceByHotelsRequestBody.setRoomCriteria(roomCriteria);
        priceByHotelsRequestBody.setRoomStayCandidates(new ArrayList<>());
        
        // Act
        ReflectionTestUtils.invokeMethod(affiliateService, "populateParametersFromAvailRequest", response, priceByHotelsRequestBody);
        
        // Assert
        Assert.assertEquals("2025-08-13", response.getCheckin());
        Assert.assertEquals("2025-08-14", response.getCheckout());
        Assert.assertEquals("RGNCR", response.getCityCode());
        Assert.assertEquals("IN", response.getCountryCode());
        Assert.assertEquals("INR", response.getCurrency());
        Assert.assertNotNull(response.getRoomCriteria());
        Assert.assertEquals(1, response.getRoomCriteria().size());
        Assert.assertEquals("2e0e", response.getRoomCriteria().get(0).getRoomCode());
        Assert.assertNotNull(response.getRoomStayCandidates());
    }
    
    @Test
    public void should_NotPopulateParametersFromAvailRequest_When_PriceByHotelsRequestBodyIsNull() {
        // Arrange
        GetQuoteResponseBody response = new GetQuoteResponseBody();
        response.setCheckin("original-checkin");
        response.setCheckout("original-checkout");
        response.setCityCode("original-city");
        response.setCountryCode("original-country");
        response.setCurrency("original-currency");
        List<RoomCriterion> originalCriteria = new ArrayList<>();
        RoomCriterion originalRoomCriterion = new RoomCriterion();
        originalRoomCriterion.setRoomCode("original-criteria");
        originalCriteria.add(originalRoomCriterion);
        response.setRoomCriteria(originalCriteria);
        
        // Act
        ReflectionTestUtils.invokeMethod(affiliateService, "populateParametersFromAvailRequest", response, null);
        
        // Assert - Values should remain unchanged when priceByHotelsRequestBody is null
        Assert.assertEquals("original-checkin", response.getCheckin());
        Assert.assertEquals("original-checkout", response.getCheckout());
        Assert.assertEquals("original-city", response.getCityCode());
        Assert.assertEquals("original-country", response.getCountryCode());
        Assert.assertEquals("original-currency", response.getCurrency());
        Assert.assertNotNull(response.getRoomCriteria());
        Assert.assertEquals(1, response.getRoomCriteria().size());
        Assert.assertEquals("original-criteria", response.getRoomCriteria().get(0).getRoomCode());
    }
    
    @Test
    public void should_PopulateParametersFromAvailRequest_When_PriceByHotelsRequestBodyHasNullFields() {
        // Arrange
        GetQuoteResponseBody response = new GetQuoteResponseBody();
        PriceByHotelsRequestBody priceByHotelsRequestBody = new PriceByHotelsRequestBody();
        // Setting fields to null to test null handling
        priceByHotelsRequestBody.setCheckin(null);
        priceByHotelsRequestBody.setCheckout(null);
        priceByHotelsRequestBody.setCityCode(null);
        priceByHotelsRequestBody.setCountryCode(null);
        priceByHotelsRequestBody.setCurrency(null);
        priceByHotelsRequestBody.setRoomCriteria(null);
        priceByHotelsRequestBody.setRoomStayCandidates(null);
        
        // Act
        ReflectionTestUtils.invokeMethod(affiliateService, "populateParametersFromAvailRequest", response, priceByHotelsRequestBody);
        
        // Assert - Should handle null values gracefully
        Assert.assertNull(response.getCheckin());
        Assert.assertNull(response.getCheckout());
        Assert.assertNull(response.getCityCode());
        Assert.assertNull(response.getCountryCode());
        Assert.assertNull(response.getCurrency());
        Assert.assertNull(response.getRoomCriteria());
        Assert.assertNull(response.getRoomStayCandidates());
    }
    
    @Test
    public void should_PopulateParametersFromAvailRequest_When_PriceByHotelsRequestBodyHasEmptyStrings() {
        // Arrange
        GetQuoteResponseBody response = new GetQuoteResponseBody();
        PriceByHotelsRequestBody priceByHotelsRequestBody = new PriceByHotelsRequestBody();
        priceByHotelsRequestBody.setCheckin("");
        priceByHotelsRequestBody.setCheckout("");
        priceByHotelsRequestBody.setCityCode("");
        priceByHotelsRequestBody.setCountryCode("");
        priceByHotelsRequestBody.setCurrency("");
        priceByHotelsRequestBody.setRoomCriteria(new ArrayList<>());
        
        // Act
        ReflectionTestUtils.invokeMethod(affiliateService, "populateParametersFromAvailRequest", response, priceByHotelsRequestBody);
        
        // Assert - Should handle empty strings properly
        Assert.assertEquals("", response.getCheckin());
        Assert.assertEquals("", response.getCheckout());
        Assert.assertEquals("", response.getCityCode());
        Assert.assertEquals("", response.getCountryCode());
        Assert.assertEquals("", response.getCurrency());
        Assert.assertNotNull(response.getRoomCriteria());
        Assert.assertEquals(0, response.getRoomCriteria().size());
    }
}
