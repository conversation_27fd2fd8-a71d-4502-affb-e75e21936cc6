package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.BaseSearchRequest;
import com.mmt.hotels.clientgateway.request.gstDetails.SaveGstDetailsRequest;
import com.mmt.hotels.clientgateway.request.gstDetails.TravellerGstDetails;
import com.mmt.hotels.clientgateway.response.gstDetails.GstDetailsResponse;
import com.mmt.hotels.clientgateway.restexecutors.MyPartnerCoreRestExecutor;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.transformer.request.GstDetailsRequestTransformer;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class GstDetailsServiceTest {
    @InjectMocks
    private GstDetailsService gstDetailsService;

    @Mock
    private CommonHelper commonHelper;

    @Mock
    private GstDetailsRequestTransformer gstDetailsRequestTransformer;

    @Mock
    private MyPartnerCoreRestExecutor myPartnerCoreRestExecutor;

    @Before
    public void init() {
        // Initialize mocks
    }

    @Test
    public void testSaveGstDetails() throws ClientGatewayException {
        SaveGstDetailsRequest saveGstDetailsRequest = new SaveGstDetailsRequest();
        TravellerGstDetails travellerGstDetails = new TravellerGstDetails();
        GstDetailsResponse<TravellerGstDetails> expectedResponse = new GstDetailsResponse<>();

        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(new CommonModifierResponse());
        Mockito.when(gstDetailsRequestTransformer.convertToTravellerGstDetails(Mockito.any(), Mockito.any()))
                .thenReturn(travellerGstDetails);
        Mockito.when(myPartnerCoreRestExecutor.saveGstDetails(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString()))
                .thenReturn(expectedResponse);

        GstDetailsResponse<TravellerGstDetails> actualResponse = gstDetailsService.saveGstDetails(saveGstDetailsRequest, new HashMap<>(), new HashMap<>(), Constants.CLIENT_DESKTOP);

        Assert.assertEquals(expectedResponse, actualResponse);
    }

    @Test
    public void testSaveGstDetails_NegativeCase() throws ClientGatewayException {
        SaveGstDetailsRequest saveGstDetailsRequest = new SaveGstDetailsRequest();

        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Mockito.when(gstDetailsRequestTransformer.convertToTravellerGstDetails(Mockito.any(), Mockito.any()))
                .thenReturn(null);

        GstDetailsResponse<TravellerGstDetails> actualResponse = gstDetailsService.saveGstDetails(saveGstDetailsRequest, new HashMap<>(), new HashMap<>(), "client");

        Assert.assertNull(actualResponse);
    }

    @Test
    public void testGetGstDetails() throws ClientGatewayException {
        BaseSearchRequest getGstDetailsRequest = new BaseSearchRequest();
        GstDetailsResponse<List<TravellerGstDetails>> expectedResponse = new GstDetailsResponse<>();

        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setUuid("uuid");
        commonModifierResponse.setExtendedUser(extendedUser);

        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(commonModifierResponse);
        Mockito.when(myPartnerCoreRestExecutor.getGstDetails(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyString()))
                .thenReturn(expectedResponse);

        GstDetailsResponse<List<TravellerGstDetails>> actualResponse = gstDetailsService.getGstDetails(getGstDetailsRequest, new HashMap<>(), new HashMap<>(), Constants.CLIENT_DESKTOP);

        Assert.assertEquals(expectedResponse, actualResponse);
    }

    @Test
    public void testGetGstDetails_NegativeCase() throws ClientGatewayException {
        BaseSearchRequest getGstDetailsRequest = new BaseSearchRequest();

        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(null);

        GstDetailsResponse<List<TravellerGstDetails>> actualResponse = gstDetailsService.getGstDetails(getGstDetailsRequest, new HashMap<>(), new HashMap<>(), Constants.CLIENT_DESKTOP);

        Assert.assertEquals(false, actualResponse.success);
    }

    @Test
    public void testGetGstDetails_NullExtendedUser() throws ClientGatewayException {
        BaseSearchRequest getGstDetailsRequest = new BaseSearchRequest();

        // Create a non-null CommonModifierResponse but with null ExtendedUser
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExtendedUser(null);

        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(commonModifierResponse);

        GstDetailsResponse<List<TravellerGstDetails>> actualResponse = gstDetailsService.getGstDetails(getGstDetailsRequest, new HashMap<>(), new HashMap<>(), Constants.CLIENT_DESKTOP);

        Assert.assertEquals(false, actualResponse.success);
        Assert.assertEquals(Constants.ERROR_MESSAGE_COULD_NOT_FETCH_UUID, actualResponse.error);
    }
}