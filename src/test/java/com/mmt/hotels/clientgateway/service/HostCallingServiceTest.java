package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.hostcalling.HostCallingInitiateRequestBody;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.StaticDetailCriteria;
import com.mmt.hotels.clientgateway.response.hostcalling.HostCallingInitiateResponse;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.restexecutors.HostCallingExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.HostCallingFactory;
import com.mmt.hotels.clientgateway.transformer.request.HostCallingRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.HostCallingResponseTransformer;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.service.OrchHostCallingService;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.pojo.response.ErrorEntity;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class HostCallingServiceTest {

    @InjectMocks
    private HostCallingService hostCallingService;

    @Mock
    private HostCallingExecutor hostCallingExecutor;

    @Mock
    private HostCallingFactory hostCallingFactory;

    @Mock
    private CommonHelper commonHelper;

    @Mock
    private Utility utility;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    private MetricErrorLogger metricErrorLogger;

    @Mock
    private OrchHostCallingService orchHostCallingService;

    @Mock
    private HostCallingRequestTransformer requestTransformer;

    @Mock
    private HostCallingResponseTransformer responseTransformer;

    private HostCallingInitiateRequestBody mockCGRequest;
    private com.mmt.hotels.pojo.request.detail.mob.HostCallingInitiateRequestBody mockHESRequest;
    private com.mmt.hotels.pojo.response.detail.HostCallingInitiateResponse mockHESResponse;
    private HostCallingInitiateResponse mockCGResponse;
    private CommonModifierResponse mockCommonModifierResponse;
    private Map<String, String[]> mockParameterMap;
    private Map<String, String> mockHttpHeaderMap;

    @Before
    public void setUp() {
        // Setup mock request objects - using mocks since structure may be complex
        mockCGRequest = mock(HostCallingInitiateRequestBody.class);
        
        // Mock nested objects for service calls
        StaticDetailCriteria mockSearchCriteria = mock(StaticDetailCriteria.class);
        when(mockSearchCriteria.getHotelId()).thenReturn("HTL123");
        when(mockCGRequest.getSearchCriteria()).thenReturn(mockSearchCriteria);
        when(mockCGRequest.getClient()).thenReturn("ANDROID");
        
        RequestDetails mockRequestDetails = mock(RequestDetails.class);
        when(mockRequestDetails.getVisitorId()).thenReturn("VIS123");
        when(mockCGRequest.getRequestDetails()).thenReturn(mockRequestDetails);

        mockHESRequest = new com.mmt.hotels.pojo.request.detail.mob.HostCallingInitiateRequestBody();
        mockHESRequest.setHotelId("HTL123");

        mockHESResponse = new com.mmt.hotels.pojo.response.detail.HostCallingInitiateResponse();
        mockHESResponse.setStatus("success");
        mockHESResponse.setRequestId("REQ123");
        mockHESResponse.setChainName("Test Chain");
        mockHESResponse.setAvailableNow(true);
        mockHESResponse.setStartTime("09:00");
        mockHESResponse.setEndTime("21:00");

        mockCGResponse = new HostCallingInitiateResponse();
        mockCGResponse.setStatus("success");
        mockCGResponse.setRequestId("REQ123");
        mockCGResponse.setChainName("Test Chain");
        mockCGResponse.setAvailableNow(true);

        // Setup CommonModifierResponse
        mockCommonModifierResponse = new CommonModifierResponse();
        mockCommonModifierResponse.setMobile("9876543210");
        mockCommonModifierResponse.setApplicationId(12345);
        mockCommonModifierResponse.setAffiliateId("TEST_AFFILIATE");

        mockParameterMap = new HashMap<>();
        mockHttpHeaderMap = new HashMap<>();
        mockHttpHeaderMap.put("mmt-auth", "test-auth");
        
        // Set up MDC for tests
        MDC.put("correlationKey", "TEST_CORRELATION");
    }

    @After
    public void tearDown() {
        MDC.clear();
    }

    @Test
    public void should_ReturnSuccessResponse_When_ValidRequest() throws ClientGatewayException {
        // Arrange
        when(commonHelper.processRequest(any(), any(), any())).thenReturn(mockCommonModifierResponse);
        when(hostCallingFactory.getRequestService("ANDROID")).thenReturn(requestTransformer);
        when(hostCallingFactory.getResponseService("ANDROID")).thenReturn(responseTransformer);
        when(requestTransformer.convertHostCallingRequest(mockCGRequest, mockCommonModifierResponse)).thenReturn(mockHESRequest);
        when(hostCallingExecutor.getEntityServiceResponse(mockHESRequest, mockParameterMap, mockHttpHeaderMap))
            .thenReturn(mockHESResponse);
        when(responseTransformer.convertHostCallingResponse(mockHESResponse, "ANDROID", "VIS123")).thenReturn(mockCGResponse);
        when(polyglotService.getTranslatedData(anyString())).thenReturn("You will receive a call from {chainName} soon");

        // Act
        HostCallingInitiateResponse result = hostCallingService.initiateHostCalling(
            mockCGRequest, mockParameterMap, mockHttpHeaderMap);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.getStatus());
        assertEquals("Test Chain", result.getChainName());
        assertEquals("You will receive a call from Test Chain soon", result.getMissedCallMessage());

        // Verify interactions - factory pattern
        verify(commonHelper).processRequest(mockCGRequest.getSearchCriteria(), mockCGRequest, mockHttpHeaderMap);
        verify(hostCallingFactory).getRequestService("ANDROID");
        verify(hostCallingFactory).getResponseService("ANDROID");
        verify(requestTransformer).convertHostCallingRequest(mockCGRequest, mockCommonModifierResponse);
        verify(hostCallingExecutor).getEntityServiceResponse(mockHESRequest, mockParameterMap, mockHttpHeaderMap);
        verify(responseTransformer).convertHostCallingResponse(mockHESResponse, "ANDROID", "VIS123");
        verify(polyglotService).getTranslatedData(anyString());
    }

    @Test
    public void should_SetMobileFromCommonModifierResponse_When_ValidRequest() throws ClientGatewayException {
        // Arrange
        when(commonHelper.processRequest(any(), any(), any())).thenReturn(mockCommonModifierResponse);
        when(hostCallingFactory.getRequestService("ANDROID")).thenReturn(requestTransformer);
        when(hostCallingFactory.getResponseService("ANDROID")).thenReturn(responseTransformer);
        when(requestTransformer.convertHostCallingRequest(mockCGRequest, mockCommonModifierResponse)).thenReturn(mockHESRequest);
        when(hostCallingExecutor.getEntityServiceResponse(mockHESRequest, mockParameterMap, mockHttpHeaderMap))
            .thenReturn(mockHESResponse);
        when(responseTransformer.convertHostCallingResponse(mockHESResponse, "ANDROID", "VIS123")).thenReturn(mockCGResponse);

        // Act
        hostCallingService.initiateHostCalling(mockCGRequest, mockParameterMap, mockHttpHeaderMap);

        // Assert - Verify mobile is set from CommonModifierResponse by checking the HES request was used correctly
        verify(requestTransformer).convertHostCallingRequest(eq(mockCGRequest), eq(mockCommonModifierResponse));
        // Can't verify mockHESRequest.setMobile() directly since it's a real object, not a mock
        // But we can verify the service processed the CommonModifierResponse correctly
        verify(commonHelper).processRequest(mockCGRequest.getSearchCriteria(), mockCGRequest, mockHttpHeaderMap);
    }

    @Test(expected = ErrorResponseFromDownstreamException.class)
    public void should_ThrowErrorResponseFromDownstreamException_When_HESReturnsError() throws ClientGatewayException {
        // Arrange
        ErrorEntity errorEntity = new ErrorEntity();
        errorEntity.setErrorCode("ERR001");
        errorEntity.setMsg("Service unavailable");
        
        mockHESResponse.setErrorEntity(errorEntity);

        when(commonHelper.processRequest(any(), any(), any())).thenReturn(mockCommonModifierResponse);
        when(hostCallingFactory.getRequestService("ANDROID")).thenReturn(requestTransformer);
        when(requestTransformer.convertHostCallingRequest(mockCGRequest, mockCommonModifierResponse)).thenReturn(mockHESRequest);
        when(hostCallingExecutor.getEntityServiceResponse(mockHESRequest, mockParameterMap, mockHttpHeaderMap))
            .thenReturn(mockHESResponse);

        // Act - Should throw ErrorResponseFromDownstreamException
        hostCallingService.initiateHostCalling(mockCGRequest, mockParameterMap, mockHttpHeaderMap);

        // Verify that response transformation is not called when error is present
        verify(hostCallingFactory, never()).getResponseService(any());
        verify(responseTransformer, never()).convertHostCallingResponse(any(), any(), any());
        verify(polyglotService, never()).getTranslatedData(anyString());
    }

    @Test
    public void should_ReturnSuccessResponse_When_PolyglotFails() throws ClientGatewayException {
        // Arrange
        when(commonHelper.processRequest(any(), any(), any())).thenReturn(mockCommonModifierResponse);
        when(hostCallingFactory.getRequestService("ANDROID")).thenReturn(requestTransformer);
        when(hostCallingFactory.getResponseService("ANDROID")).thenReturn(responseTransformer);
        when(requestTransformer.convertHostCallingRequest(mockCGRequest, mockCommonModifierResponse)).thenReturn(mockHESRequest);
        when(hostCallingExecutor.getEntityServiceResponse(mockHESRequest, mockParameterMap, mockHttpHeaderMap))
            .thenReturn(mockHESResponse);
        when(responseTransformer.convertHostCallingResponse(mockHESResponse, "ANDROID", "VIS123")).thenReturn(mockCGResponse);
        when(polyglotService.getTranslatedData(anyString())).thenThrow(new RuntimeException("Polyglot error"));

        // Act
        HostCallingInitiateResponse result = hostCallingService.initiateHostCalling(
            mockCGRequest, mockParameterMap, mockHttpHeaderMap);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.getStatus());
        assertEquals("Host calling is currently unavailable. Please try again during operational hours.", 
            result.getMissedCallMessage());
    }

    @Test
    public void should_ReturnSuccessResponse_When_ChainNameIsEmpty() throws ClientGatewayException {
        // Arrange
        mockCGResponse.setChainName(""); // Empty chain name
        
        when(commonHelper.processRequest(any(), any(), any())).thenReturn(mockCommonModifierResponse);
        when(hostCallingFactory.getRequestService("ANDROID")).thenReturn(requestTransformer);
        when(hostCallingFactory.getResponseService("ANDROID")).thenReturn(responseTransformer);
        when(requestTransformer.convertHostCallingRequest(mockCGRequest, mockCommonModifierResponse)).thenReturn(mockHESRequest);
        when(hostCallingExecutor.getEntityServiceResponse(mockHESRequest, mockParameterMap, mockHttpHeaderMap))
            .thenReturn(mockHESResponse);
        when(responseTransformer.convertHostCallingResponse(mockHESResponse, "ANDROID", "VIS123")).thenReturn(mockCGResponse);

        // Act
        HostCallingInitiateResponse result = hostCallingService.initiateHostCalling(
            mockCGRequest, mockParameterMap, mockHttpHeaderMap);

        // Assert
        assertNotNull(result);
        assertNull(result.getMissedCallMessage());

        // Verify polyglot is not called when chain name is empty
        verify(polyglotService, never()).getTranslatedData(anyString());
    }

    @Test
    public void should_UseDifferentClientTransformers_When_DifferentClientsRequested() throws ClientGatewayException {
        // Arrange - Test PWA client (need to mock the client field)
        when(mockCGRequest.getClient()).thenReturn("PWA");
        
        when(commonHelper.processRequest(any(), any(), any())).thenReturn(mockCommonModifierResponse);
        when(hostCallingFactory.getRequestService("PWA")).thenReturn(requestTransformer);
        when(hostCallingFactory.getResponseService("PWA")).thenReturn(responseTransformer);
        when(requestTransformer.convertHostCallingRequest(mockCGRequest, mockCommonModifierResponse)).thenReturn(mockHESRequest);
        when(hostCallingExecutor.getEntityServiceResponse(mockHESRequest, mockParameterMap, mockHttpHeaderMap))
            .thenReturn(mockHESResponse);
        when(responseTransformer.convertHostCallingResponse(mockHESResponse, "PWA", "VIS123")).thenReturn(mockCGResponse);

        // Act
        hostCallingService.initiateHostCalling(mockCGRequest, mockParameterMap, mockHttpHeaderMap);

        // Assert
        verify(hostCallingFactory).getRequestService("PWA");
        verify(hostCallingFactory).getResponseService("PWA");
        verify(responseTransformer).convertHostCallingResponse(mockHESResponse, "PWA", "VIS123");
    }

    @Test(expected = ClientGatewayException.class)
    public void should_ThrowException_When_RequestIsNull() throws ClientGatewayException {
        // Act
        hostCallingService.initiateHostCalling(null, mockParameterMap, mockHttpHeaderMap);
    }

    @Test(expected = ClientGatewayException.class)
    public void should_ThrowException_When_ExecutorThrowsException() throws ClientGatewayException {
        // Arrange
        when(commonHelper.processRequest(any(), any(), any())).thenReturn(mockCommonModifierResponse);
        when(hostCallingFactory.getRequestService("ANDROID")).thenReturn(requestTransformer);
        when(requestTransformer.convertHostCallingRequest(mockCGRequest, mockCommonModifierResponse)).thenReturn(mockHESRequest);
        when(hostCallingExecutor.getEntityServiceResponse(mockHESRequest, mockParameterMap, mockHttpHeaderMap))
            .thenThrow(new RuntimeException("Connection failed"));

        // Act
        hostCallingService.initiateHostCalling(mockCGRequest, mockParameterMap, mockHttpHeaderMap);
    }

    @Test
    public void should_ReturnSuccessResponse_When_HESResponseIsNull() throws ClientGatewayException {
        // Arrange - Return null from HES 
        HostCallingInitiateResponse errorResponse = new HostCallingInitiateResponse();
        errorResponse.setStatus("error");
        errorResponse.setRequestId("VIS123");
        errorResponse.setAvailableNow(false);

        when(commonHelper.processRequest(any(), any(), any())).thenReturn(mockCommonModifierResponse);
        when(hostCallingFactory.getRequestService("ANDROID")).thenReturn(requestTransformer);
        when(hostCallingFactory.getResponseService("ANDROID")).thenReturn(responseTransformer);
        when(requestTransformer.convertHostCallingRequest(mockCGRequest, mockCommonModifierResponse)).thenReturn(mockHESRequest);
        when(hostCallingExecutor.getEntityServiceResponse(mockHESRequest, mockParameterMap, mockHttpHeaderMap))
            .thenReturn(null);
        when(responseTransformer.convertHostCallingResponse(null, "ANDROID", "VIS123")).thenReturn(errorResponse);

        // Act
        HostCallingInitiateResponse result = hostCallingService.initiateHostCalling(
            mockCGRequest, mockParameterMap, mockHttpHeaderMap);

        // Assert
        assertNotNull(result);
        assertEquals("error", result.getStatus());
        assertEquals(false, result.getAvailableNow());
    }

    @Test 
    public void should_SetDefaultValues_When_MDCIsEmpty() throws ClientGatewayException {
        // Arrange - Clear MDC to test null correlation key
        MDC.clear();
        
        when(commonHelper.processRequest(any(), any(), any())).thenReturn(mockCommonModifierResponse);
        when(hostCallingFactory.getRequestService("ANDROID")).thenReturn(requestTransformer);
        when(hostCallingFactory.getResponseService("ANDROID")).thenReturn(responseTransformer);
        when(requestTransformer.convertHostCallingRequest(mockCGRequest, mockCommonModifierResponse)).thenReturn(mockHESRequest);
        when(hostCallingExecutor.getEntityServiceResponse(mockHESRequest, mockParameterMap, mockHttpHeaderMap))
            .thenReturn(mockHESResponse);
        when(responseTransformer.convertHostCallingResponse(mockHESResponse, "ANDROID", "VIS123")).thenReturn(mockCGResponse);

        // Act
        HostCallingInitiateResponse result = hostCallingService.initiateHostCalling(
            mockCGRequest, mockParameterMap, mockHttpHeaderMap);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.getStatus());
    }

    @Test
    public void should_HandleNullChainName_When_TransformingMissedCallMessage() throws ClientGatewayException {
        // Arrange
        mockCGResponse.setChainName(null);
        
        when(commonHelper.processRequest(any(), any(), any())).thenReturn(mockCommonModifierResponse);
        when(hostCallingFactory.getRequestService("ANDROID")).thenReturn(requestTransformer);
        when(hostCallingFactory.getResponseService("ANDROID")).thenReturn(responseTransformer);
        when(requestTransformer.convertHostCallingRequest(mockCGRequest, mockCommonModifierResponse)).thenReturn(mockHESRequest);
        when(hostCallingExecutor.getEntityServiceResponse(mockHESRequest, mockParameterMap, mockHttpHeaderMap))
            .thenReturn(mockHESResponse);
        when(responseTransformer.convertHostCallingResponse(mockHESResponse, "ANDROID", "VIS123")).thenReturn(mockCGResponse);

        // Act
        HostCallingInitiateResponse result = hostCallingService.initiateHostCalling(
            mockCGRequest, mockParameterMap, mockHttpHeaderMap);

        // Assert
        assertNotNull(result);
        assertNull(result.getMissedCallMessage());

        // Verify polyglot is not called when chain name is null
        verify(polyglotService, never()).getTranslatedData(anyString());
    }

    @Test
    public void should_HandleFactoryPatternCorrectly_When_TransformersUsed() throws ClientGatewayException {
        // Arrange
        when(commonHelper.processRequest(any(), any(), any())).thenReturn(mockCommonModifierResponse);
        when(hostCallingFactory.getRequestService("ANDROID")).thenReturn(requestTransformer);
        when(hostCallingFactory.getResponseService("ANDROID")).thenReturn(responseTransformer);
        when(requestTransformer.convertHostCallingRequest(mockCGRequest, mockCommonModifierResponse)).thenReturn(mockHESRequest);
        when(hostCallingExecutor.getEntityServiceResponse(mockHESRequest, mockParameterMap, mockHttpHeaderMap))
            .thenReturn(mockHESResponse);
        when(responseTransformer.convertHostCallingResponse(mockHESResponse, "ANDROID", "VIS123")).thenReturn(mockCGResponse);

        // Act
        hostCallingService.initiateHostCalling(mockCGRequest, mockParameterMap, mockHttpHeaderMap);

        // Assert - Verify that factory pattern is used correctly for both request and response transformation
        verify(hostCallingFactory).getRequestService("ANDROID");
        verify(hostCallingFactory).getResponseService("ANDROID");
        verify(requestTransformer).convertHostCallingRequest(mockCGRequest, mockCommonModifierResponse);
        verify(responseTransformer).convertHostCallingResponse(mockHESResponse, "ANDROID", "VIS123");
    }

    // Additional test cases for improved code coverage - added without modifying existing tests

    @Test(expected = ErrorResponseFromDownstreamException.class)
    public void testInitiateHostCallingWithNullMobile() throws ClientGatewayException {
        // Setup request
        HostCallingInitiateRequestBody request = createMockRequest("ANDROID", "VIS123", "HTL123");
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> httpHeaderMap = new HashMap<>();

        // Mock CommonModifierResponse with null mobile
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setMobile(null); // This should trigger the validation error
        commonModifierResponse.setExpDataMap(new java.util.LinkedHashMap<String, String>());

        // Mock commonHelper to return response with null mobile
        when(commonHelper.processRequest(any(), any(), any())).thenReturn(commonModifierResponse);

        // Mock polyglotService for error message
        when(polyglotService.getTranslatedData(any())).thenReturn("Mobile number is required");

        // Call the method - should throw ErrorResponseFromDownstreamException
        hostCallingService.initiateHostCalling(request, parameterMap, httpHeaderMap);
    }

    @Test(expected = ErrorResponseFromDownstreamException.class)
    public void testInitiateHostCallingWithEmptyMobile() throws ClientGatewayException {
        // Setup request
        HostCallingInitiateRequestBody request = createMockRequest("ANDROID", "VIS123", "HTL123");
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> httpHeaderMap = new HashMap<>();

        // Mock CommonModifierResponse with empty mobile
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setMobile("   "); // This should trigger the validation error (empty after trim)
        commonModifierResponse.setExpDataMap(new java.util.LinkedHashMap<String, String>());

        // Mock commonHelper to return response with empty mobile
        when(commonHelper.processRequest(any(), any(), any())).thenReturn(commonModifierResponse);

        // Mock polyglotService for error message
        when(polyglotService.getTranslatedData(any())).thenReturn("Mobile number is required");

        // Call the method - should throw ErrorResponseFromDownstreamException
        hostCallingService.initiateHostCalling(request, parameterMap, httpHeaderMap);
    }

    @Test(expected = ErrorResponseFromDownstreamException.class)
    public void testInitiateHostCallingWithBlankMobile() throws ClientGatewayException {
        // Setup request
        HostCallingInitiateRequestBody request = createMockRequest("ANDROID", "VIS123", "HTL123");
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> httpHeaderMap = new HashMap<>();

        // Mock CommonModifierResponse with blank mobile
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setMobile(""); // This should trigger the validation error
        commonModifierResponse.setExpDataMap(new java.util.LinkedHashMap<String, String>());

        // Mock commonHelper to return response with blank mobile
        when(commonHelper.processRequest(any(), any(), any())).thenReturn(commonModifierResponse);

        // Mock polyglotService for error message
        when(polyglotService.getTranslatedData(any())).thenReturn("Mobile number is required");

        // Call the method - should throw ErrorResponseFromDownstreamException
        hostCallingService.initiateHostCalling(request, parameterMap, httpHeaderMap);
    }

    @Test
    public void testInitiateHostCallingWithRearchFlowTrue() throws ClientGatewayException {
        // Setup request
        HostCallingInitiateRequestBody request = createMockRequest("ANDROID", "VIS123", "HTL123");
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> httpHeaderMap = new HashMap<>();

        // Mock CommonModifierResponse with valid mobile and rearch flow enabled
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setMobile("9876543210");
        java.util.LinkedHashMap<String, String> expDataMap = new java.util.LinkedHashMap<>();
        expDataMap.put("detailPageRearch", "true"); // This should trigger rearch flow
        commonModifierResponse.setExpDataMap(expDataMap);

        // Mock commonHelper
        when(commonHelper.processRequest(any(), any(), any())).thenReturn(commonModifierResponse);

        // Mock utility to return true for rearch flow
        when(utility.isDetailPageRearchFlow(eq(true), any(), any())).thenReturn(true);

        // Mock orch service response
        HostCallingInitiateResponse mockOrchResponse = new HostCallingInitiateResponse();
        when(orchHostCallingService.hostCalling(any(), any(), any(), any())).thenReturn(mockOrchResponse);

        // Call the method
        HostCallingInitiateResponse result = hostCallingService.initiateHostCalling(request, parameterMap, httpHeaderMap);

        // Verify that orch service was called instead of regular flow
        verify(orchHostCallingService).hostCalling(eq(request), eq(parameterMap), eq(httpHeaderMap), eq(commonModifierResponse));
        assertEquals(mockOrchResponse, result);

        // Verify that factory pattern was NOT used (since we went through orch flow)
        verify(hostCallingFactory, never()).getRequestService(any());
        verify(hostCallingExecutor, never()).getEntityServiceResponse(any(), any(), any());
    }

    @Test
    public void testInitiateHostCallingWithValidMobileRearchFlowFalse() throws ClientGatewayException {
        // Setup request
        HostCallingInitiateRequestBody request = createMockRequest("IOS", "VIS456", "HTL456");
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> httpHeaderMap = new HashMap<>();

        // Mock CommonModifierResponse with valid mobile and rearch flow disabled
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setMobile("9876543210");
        java.util.LinkedHashMap<String, String> expDataMap = new java.util.LinkedHashMap<>();
        commonModifierResponse.setExpDataMap(expDataMap);

        // Mock commonHelper
        when(commonHelper.processRequest(any(), any(), any())).thenReturn(commonModifierResponse);

        // Mock utility to return false for rearch flow
        when(utility.isDetailPageRearchFlow(eq(true), any(), any())).thenReturn(false);

        // Mock factory and transformers
        HostCallingRequestTransformer requestTransformer = mock(HostCallingRequestTransformer.class);
        HostCallingResponseTransformer responseTransformer = mock(HostCallingResponseTransformer.class);
        when(hostCallingFactory.getRequestService("IOS")).thenReturn(requestTransformer);
        when(hostCallingFactory.getResponseService("IOS")).thenReturn(responseTransformer);

        // Mock HES request and response
        com.mmt.hotels.pojo.request.detail.mob.HostCallingInitiateRequestBody hesRequest = 
            new com.mmt.hotels.pojo.request.detail.mob.HostCallingInitiateRequestBody();
        com.mmt.hotels.pojo.response.detail.HostCallingInitiateResponse hesResponse = 
            new com.mmt.hotels.pojo.response.detail.HostCallingInitiateResponse();
        HostCallingInitiateResponse cgResponse = new HostCallingInitiateResponse();

        when(requestTransformer.convertHostCallingRequest(any(), any())).thenReturn(hesRequest);
        when(hostCallingExecutor.getEntityServiceResponse(any(), any(), any())).thenReturn(hesResponse);
        when(responseTransformer.convertHostCallingResponse(any(), any(), any())).thenReturn(cgResponse);

        // Call the method
        HostCallingInitiateResponse result = hostCallingService.initiateHostCalling(request, parameterMap, httpHeaderMap);

        // Verify normal flow was used (not orch flow)
        verify(hostCallingFactory).getRequestService("IOS");
        verify(hostCallingFactory).getResponseService("IOS");
        verify(hostCallingExecutor).getEntityServiceResponse(eq(hesRequest), eq(parameterMap), eq(httpHeaderMap));
        verify(orchHostCallingService, never()).hostCalling(any(), any(), any(), any());

        // Verify mobile was set on HES request
        assertEquals("9876543210", hesRequest.getMobile());
        assertEquals(cgResponse, result);
    }

    private HostCallingInitiateRequestBody createMockRequest(String client, String visitorId, String hotelId) {
        HostCallingInitiateRequestBody request = new HostCallingInitiateRequestBody();
        request.setClient(client);
        
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setVisitorId(visitorId);
        request.setRequestDetails(requestDetails);
        
        StaticDetailCriteria searchCriteria = new StaticDetailCriteria();
        searchCriteria.setHotelId(hotelId);
        request.setSearchCriteria(searchCriteria);
        
        return request;
    }
} 