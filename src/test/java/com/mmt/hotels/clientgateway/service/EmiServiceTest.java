package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.UpdatedEMISearchCriteria;
import com.mmt.hotels.clientgateway.request.UpdatedEmiRequest;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.restexecutors.EmiDetailExecutor;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResult;
import com.mmt.hotels.clientgateway.transformer.factory.EMIFactory;
import com.mmt.hotels.clientgateway.transformer.factory.SearchRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.request.EMIRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.SearchRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.model.request.emi.UpdateEmiDetailRequest;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class EmiServiceTest {

	@InjectMocks
	EmiService emiService;

	@Mock
	private EmiDetailExecutor emiExecutor;

	@Mock
	CommonHelper commonHelper;

	@Mock
	EMIFactory emiFactory;

	@Mock
	SearchRoomsFactory searchRoomsFactory;

	@Mock
	private MetricErrorLogger metricErrorLogger;

    @Test(expected = ClientGatewayException.class)
	public void getUpdateEmiResponse() throws Exception {
		UserServiceResponse user = userServiceRsp();
		Mockito.when(commonHelper.getMMTAuth(Mockito.any(), Mockito.any())).thenReturn("mmtauth");
		Mockito.when(commonHelper.getUserDetails(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(user);
		Mockito.when(emiExecutor.getUpdatedEmiDetails(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("emiRsp");
		Assert.assertNotNull(emiService.getUpdateEmiResponse(new UpdateEmiDetailRequest(), new HashMap<>(),"corKey", new HashMap<>()));

		Mockito.when(commonHelper.getUserDetails(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any())).thenThrow(new ClientGatewayException());
		emiService.getUpdateEmiResponse(new UpdateEmiDetailRequest(), new HashMap<>(),"corKey", new HashMap<>());
	}

	public UserServiceResponse userServiceRsp() {
		UserServiceResponse user = new UserServiceResponse();
		user.setResult(new UserServiceResult());
		user.getResult().setExtendedUser(new ExtendedUser());
		return user;
	}

	@Test(expected = Exception.class)
	public void updatedEmiDetailsTest() throws ClientGatewayException {
		EMIRequestTransformer emiRequestTransformer = Mockito.mock(EMIRequestTransformer.class);
		SearchRoomsResponseTransformer searchRoomsResponseTransformer = Mockito.mock(SearchRoomsResponseTransformer.class);
		UpdatedEmiRequest updatedEmiRequest = new UpdatedEmiRequest();
		updatedEmiRequest.setDeviceDetails(new DeviceDetails());
		updatedEmiRequest.setSearchCriteria(new UpdatedEMISearchCriteria());
		Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
		Mockito.when(emiFactory.getRequestService(Mockito.any())).thenReturn(emiRequestTransformer);
		Mockito.when(searchRoomsFactory.getResponseService(Mockito.any())).thenReturn(searchRoomsResponseTransformer);
		Mockito.when(emiRequestTransformer.convertEmiRequest(Mockito.any(), Mockito.any())).thenReturn(new UpdateEmiDetailRequest());
		Mockito.when(emiExecutor.getUpdatedEmi(Mockito.any(), Mockito.any(), Mockito.anyMap(), Mockito.any())).thenReturn(new RoomDetailsResponse());
		Mockito.when(searchRoomsResponseTransformer.convertSearchRoomsResponse(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new SearchRoomsResponse());
		Assert.assertNotNull(emiService.updatedEmiDetails(updatedEmiRequest, new HashMap<>(), "test", new HashMap<>()));
		Mockito.when(emiExecutor.getUpdatedEmi(Mockito.any(), Mockito.any(), Mockito.anyMap(), Mockito.any())).thenThrow(new ErrorResponseFromDownstreamException(DependencyLayer.CLIENTGATEWAY, ErrorType.CONNECTIVITY));
		emiService.updatedEmiDetails(updatedEmiRequest, new HashMap<>(), null, new HashMap<>());
	}

	@Test(expected = ClientGatewayException.class)
	public void updatedEmiDetailsRuntimeExceptionTest() throws ClientGatewayException {
		// Setup mocks for the RuntimeException path
		EMIRequestTransformer emiRequestTransformer = Mockito.mock(EMIRequestTransformer.class);
		UpdatedEmiRequest updatedEmiRequest = new UpdatedEmiRequest();
		updatedEmiRequest.setDeviceDetails(new DeviceDetails());
		updatedEmiRequest.setSearchCriteria(new UpdatedEMISearchCriteria());
		
		Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
		Mockito.when(emiFactory.getRequestService(Mockito.any())).thenReturn(emiRequestTransformer);
		Mockito.when(emiRequestTransformer.convertEmiRequest(Mockito.any(), Mockito.any())).thenReturn(new UpdateEmiDetailRequest());
		
		// This should trigger the catch (Throwable e) block
		Mockito.when(emiExecutor.getUpdatedEmi(Mockito.any(), Mockito.any(), Mockito.anyMap(), Mockito.any()))
			.thenThrow(new RuntimeException("Unexpected error"));
		
		// This should throw ClientGatewayException after handling the RuntimeException
		emiService.updatedEmiDetails(updatedEmiRequest, new HashMap<>(), "test", new HashMap<>());
	}

}
