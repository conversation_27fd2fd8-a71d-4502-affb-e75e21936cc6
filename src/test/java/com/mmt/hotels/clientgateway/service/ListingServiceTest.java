package com.mmt.hotels.clientgateway.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.FetchCollectionHelper;
import com.mmt.hotels.clientgateway.helpers.ListingHelper;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.GroupBookingResponse;
import com.mmt.hotels.clientgateway.response.filter.ContextDetails;
import com.mmt.hotels.clientgateway.response.filter.FilterCategory;
import com.mmt.hotels.clientgateway.response.filter.FilterResponse;
import com.mmt.hotels.clientgateway.response.listing.UpsellRateplanResponse;
import com.mmt.hotels.clientgateway.response.listingmap.ListingMapResponse;
import com.mmt.hotels.clientgateway.response.moblanding.MatchMakerResponseCG;
import com.mmt.hotels.clientgateway.response.moblanding.MobLandingResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.FetchCollectionResponseV2;
import com.mmt.hotels.clientgateway.response.searchHotels.SearchHotelsResponse;
import com.mmt.hotels.clientgateway.restexecutors.FilterExecutor;
import com.mmt.hotels.clientgateway.restexecutors.ListingMapExecutor;
import com.mmt.hotels.clientgateway.restexecutors.MobLandingExecutor;
import com.mmt.hotels.clientgateway.restexecutors.SearchHotelsExecutor;
import com.mmt.hotels.clientgateway.restexecutors.TravelTripExecutor;
import com.mmt.hotels.clientgateway.response.TravelTipResponse;
import com.mmt.hotels.clientgateway.response.TravelTipWrapperResponse;
import com.mmt.hotels.clientgateway.response.CardsData;
import com.mmt.hotels.clientgateway.transformer.response.cardEngine.CardEngineResponseTransformerFactory;
import com.mmt.hotels.filter.DPTExperimentDetails;
import com.mmt.hotels.clientgateway.businessobjects.FilterDetail;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.transformer.factory.FilterFactory;
import com.mmt.hotels.clientgateway.transformer.factory.ListingMapFactory;
import com.mmt.hotels.clientgateway.transformer.factory.MobLandingFactory;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.transformer.factory.TravelTipFactory;
import com.mmt.hotels.clientgateway.transformer.request.*;
import com.mmt.hotels.clientgateway.transformer.request.android.SearchHotelsRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.pwa.ListingMapRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.request.pwa.MobLandingRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.request.pwa.SearchHotelsRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.*;
import com.mmt.hotels.clientgateway.transformer.response.android.SearchHotelsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.MobLandingResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.pwa.ListingMapResponseTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.pwa.SearchHotelsResponseTransformerPWA;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.kafka.JsonKafkaProducer;
import com.mmt.hotels.model.request.CityOverviewHesRequest;
import com.mmt.hotels.model.request.FilterSearchMetaDataResponse;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.response.altaccodata.CardPayloadResponse;
import com.mmt.hotels.model.response.errors.Error;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import com.mmt.hotels.model.response.filterLogging.FilterLoggingResponseEntity;
import com.mmt.hotels.model.response.listpersonalization.GenericCardPayloadData;
import com.mmt.hotels.model.response.searchwrapper.*;
import com.mmt.hotels.pojo.listing.personalization.CardAction;
import com.mmt.hotels.pojo.listing.personalization.CardData;
import com.mmt.hotels.pojo.matchmaker.WikiResponse;
import com.mmt.hotels.pojo.request.landing.HotelLandingMobRequestBody;
import com.mmt.hotels.pojo.response.HotelListingMapResponse;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;
import javax.servlet.http.HttpServletRequest;
import com.mmt.hotels.util.Tuple;
import com.mmt.propertymanager.config.PropertyManager;
import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class ListingServiceTest {

    @Spy
    @InjectMocks
    ListingService listingService;

    @Mock
    FilterFactory filterFactory;

    @Mock
    FilterExecutor filterExecutor;

    @Mock
    FetchCollectionHelper fetchCollectionHelper;

    @Mock
    private FilterResponseTransformer filterResponseTransformer;

    @Mock
    PolyglotService polyglotService;

    @Mock
    ListingMapFactory listingMapFactory;

    @Mock
    ListingMapExecutor listingMapExecutor;
    
    @Mock
    CommonHelper commonHelper;

    @Mock
    ListingHelper listingHelper;

    @Mock
    private SearchHotelsFactory searchHotelsFactory;

    @Mock
    MetricErrorLogger metricErrorLogger;

    @Mock
    private MobLandingFactory mobLandingFactory;

    @Mock
    private SearchHotelsExecutor searchHotelsExecutor;

    @Mock
    private MobLandingExecutor mobLandingExecutor;

    @Mock
    private OldToNewerRequestTransformer oldToNewerRequestTransformer;
    
    @Mock
    private MobConfigHelper mobConfigHelper;
    
    @Mock
    private HotelMetaDataService hotelMetaDataService;

    @Mock
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private TravelTripExecutor travelTipExecutor;

    @Mock
    private TravelTipFactory travelTipFactory;

    @Mock
    private CommonResponseTransformer commonResponseTransformer;

    @Mock
    JsonKafkaProducer<GroupBookingRequest> jsonKafkaProducer;

    @Mock
    private Utility utility;

    @Mock
    MetricAspect metricAspect;
    
    @Mock
    FilterRequestTransformer filterRequestTransformer;
    
    @Mock
    CommonModifierResponse commonModifierResponse;
    
    @Mock
    SearchWrapperInputRequest searchWrapperInputRequest;

    ThreadPoolTaskExecutor pdtLoggingThreadPool = new ThreadPoolTaskExecutor();
    ThreadPoolTaskExecutor kafkaThreadPool = new ThreadPoolTaskExecutor();

    @Mock
    CrossSellUtil crossSellUtil;
    
    @Mock
    DeepLinkGenerationService deepLinkGenerationService;
    
    @Mock
    CardEngineService cardEngineService;
    
    @Mock
    CardEngineResponseTransformerFactory cardEngineResponseTransformerFactory;
    
    @Mock 
    OrchListingService orchListingService;
    
    @Mock
    PropertyManager propManager;
    
    @Mock
    CommonConfigConsul commonConfigConsul;
    
    @Before
    public void init() {
        kafkaThreadPool.setCorePoolSize(1);
        kafkaThreadPool.initialize();
        Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(Mockito.any(),Mockito.any(),Mockito.anyLong());
        ReflectionTestUtils.setField(listingService, "pdtLoggingThreadPool", pdtLoggingThreadPool);
        ReflectionTestUtils.setField(listingService, "kafkaThreadPool", kafkaThreadPool);
        ReflectionTestUtils.setField(listingService, "ruleBasedFilterSettings", true);
        ReflectionTestUtils.setField(listingService, "requestCallbackCountProp", 2);
    }

    @Test (expected = Exception.class)
    public void filterCountTest() throws ClientGatewayException {
        FilterRequestTransformer searchTransformer = Mockito.mock(FilterRequestTransformer.class);
        FilterSearchMetaDataResponse filterSearchMetaDataResponse = new FilterSearchMetaDataResponse();

        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put("user_rating", "true");
        commonModifierResponse.setExpDataMap(expDataMap);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(commonModifierResponse);
        Mockito.when(filterFactory.getRequestService(Mockito.any())).thenReturn(filterRequestTransformer);
        Mockito.lenient().when(filterExecutor.filterCount(Mockito.any(), Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(new FilterSearchMetaDataResponse());

        FilterCountRequest filterCountRequest =  new FilterCountRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource("HOMESTAY");
        filterCountRequest.setRequestDetails(requestDetails);
        filterCountRequest.setFiltersToRemove(new ArrayList<>());
        listingService.filterCount(filterCountRequest, null, null, true, new ArrayList<>());

        requestDetails.setFunnelSource("HOMESTAY_HTL");
        filterCountRequest.setRequestDetails(requestDetails);
        List<com.mmt.hotels.clientgateway.request.Filter> filterList = new ArrayList<>();
        com.mmt.hotels.clientgateway.request.Filter filter = new com.mmt.hotels.clientgateway.request.Filter();
        filter.setFilterValue("filter_value");
        filter.setFilterGroup(com.mmt.hotels.clientgateway.response.filter.FilterGroup.AMENITIES);
        filterList.add(filter);
        filterCountRequest.setFilterCriteria(filterList);
        listingService.filterCount(filterCountRequest, null, null, true, new ArrayList<>());

    }
    
    @Test
    public void filterCountLogTest() throws ClientGatewayException {
    	FilterCountRequest filterRequest = new FilterCountRequest();
    	ContextDetails contextDetails = new ContextDetails();
    	contextDetails.setAltAccoIntent(false);
    	contextDetails.setContext("CTDEL|longstay|all|all");
    	filterRequest.setContextDetails(contextDetails); 
    	filterRequest.setInitialCohortId("All|All|All|AAFalse|All|All");
        FilterResponse response = new FilterResponse();
        response.setFilteredCount(2); 
        response.setLimited(false);
        response.setTotalCount(100);
        response.setCurrentCohortId("PAX2|All|All|AAFalse|All|All");
        List<FilterCategory> filterList = new ArrayList<FilterCategory>();
        FilterCategory filterCategory = new FilterCategory();
        List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<com.mmt.hotels.clientgateway.response.filter.Filter>();
        com.mmt.hotels.clientgateway.response.filter.Filter filter = new com.mmt.hotels.clientgateway.response.filter.Filter();
        filter.setFilterGroup("STAR_RATING");
        filter.setFilterValue("5"); 
        filter.setRangeFilter(false);
        filters.add(filter);
        filterCategory.setFilters(filters);
        filterCategory.setCategoryName("STAR_CATEGORY");
        filterList.add(filterCategory);
        response.setFilterList(filterList);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(commonModifierResponse);
    	Mockito.when(filterFactory.getRequestService(Mockito.any())).thenReturn(filterRequestTransformer);
    	Mockito.when(filterRequestTransformer.convertSearchRequest(Mockito.any(),Mockito.any())).thenReturn(searchWrapperInputRequest);
    	FilterLoggingResponseEntity filterLoggingResponseEntity = new FilterLoggingResponseEntity();
    	filterLoggingResponseEntity.setSuccess(true);
        listingService.filterCountLogging(filterRequest, response, null, false, new ArrayList<>());
    	Mockito.verify(filterExecutor, Mockito.atMost(1)).filterCountLogging(Mockito.any(),Mockito.any());
    	
    }
    
    @Test (expected = Exception.class)
    public void filterCountOldTest() throws ClientGatewayException {

        FilterRequestTransformer searchTransformer = Mockito.mock(FilterRequestTransformer.class);
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        Mockito.when(oldToNewerRequestTransformer.updateSearchHotelsRequest(Mockito.any())).thenReturn(searchHotelsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(filterFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.lenient().when(searchTransformer.convertSearchRequest(Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());

        Mockito.when(filterExecutor.filterCount(Mockito.any(), Mockito.any(),Mockito.any(), Mockito.any())).thenReturn("abcd");
        SearchWrapperInputRequest filterCountRequest = new SearchWrapperInputRequest();
        String resp = listingService.filterCountOld(filterCountRequest,null,null);
        Assert.assertNotNull(resp);

        listingService.filterCountOld(null,null,null);

    }

    @Test (expected = Exception.class)
    public void mobLandingTest() throws ClientGatewayException {

        MobLandingRequestTransformerPWA mobLandingTransformer = Mockito.mock(MobLandingRequestTransformerPWA.class);
        HotelLandingMobRequestBody moblandingBody = new HotelLandingMobRequestBody();
        Mockito.when(mobLandingFactory.getRequestService(Mockito.any())).thenReturn(mobLandingTransformer);
        Mockito.when(mobLandingTransformer.convertMobLandingRequest(Mockito.any(), Mockito.any())).thenReturn(moblandingBody);

        Mockito.when(mobLandingExecutor.moblanding(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("{}");
        Mockito.when(mobLandingFactory.getResponseService(Mockito.any())).thenReturn(Mockito.mock(MobLandingResponseTransformer.class));
        MobLandingResponse resp = listingService.mobLanding(new MobLandingRequest(),null,null);
        Assert.assertNotNull(resp);
        listingService.mobLanding(null,null,null);

    }

    @Test
    public void getCardInfoTest(){
        CardCollections cardCollections = new CardCollections();
        cardCollections.setCardInfo(new CardData());
        List<CardAction> cardActionList = new ArrayList<>();
        cardActionList.add(new CardAction());
        cardCollections.getCardInfo().setCardAction(cardActionList);
        cardCollections.getCardInfo().setCardPayload(new CardPayloadResponse());
        List<GenericCardPayloadData> genericCardPayloadDataList = new ArrayList<>();
        List<GenericCardPayloadData> genericCardPayloadDataList2 = new ArrayList<>();
        genericCardPayloadDataList.add(new GenericCardPayloadData());
        genericCardPayloadDataList.get(0).setData(genericCardPayloadDataList2);
        cardCollections.getCardInfo().getCardPayload().setGenericCardData(genericCardPayloadDataList);
//        todo: uncomment this once checked
//        Assert.assertNotNull(ReflectionTestUtils.invokeMethod(listingService, "getCardInfo", cardCollections));
    }




    @Test (expected = Exception.class)
    public void listingMapTest() throws ClientGatewayException {

        ListingMapRequestTransformer searchTransformer = Mockito.mock(ListingMapRequestTransformerPWA.class);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(listingMapFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.when(searchTransformer.convertListingMapRequest(Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());

        Mockito.when(listingMapExecutor.listingMap(Mockito.any(), Mockito.any())).thenReturn(new HotelListingMapResponse());
        ListingMapResponseTransformer responseTransformer = Mockito.mock(ListingMapResponseTransformerPWA.class);
        Mockito.when(listingMapFactory.getResponseService(Mockito.any())).thenReturn(responseTransformer);
        Mockito.when(responseTransformer.convertListingMapResponse(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new ListingMapResponse());

        ListingMapResponse resp = listingService.listingMap(new ListingMapRequest(),null,null);
        Assert.assertNotNull(resp);
        listingService.listingMap(null,null,null);

    }

    @Test (expected = Exception.class)
    public void testFetchCollectionsOld() throws ClientGatewayException {
        SearchHotelsRequestTransformer searchTransformer = Mockito.mock(SearchHotelsRequestTransformerPWA.class);
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        String responseHes="{\"cardCollections\":[{\"cardType\":\"SUGGESTEDFORYOU\",\"heading\":\"Suggested for you\",\"subHeading\":\"\",\"priority\":\"7\",\"priorities\":\"9\",\"cardList\":[{\"description\":\"Explore Romantic Beach & Mountain Stays!\",\"imageUrl\":\"\",\"priority\":\"7\",\"searchContext\":{\"checkIn\":\"2021-05-27\",\"checkOut\":\"2021-05-29\",\"roomStayParams\":\"1e0e\",\"roomStayCandidates\":[{\"guestCounts\":[{\"count\":\"1\",\"ageQualifyingCode\":\"10\",\"ages\":null}]}]},\"cityTrendingDataMap\":{\"GOI\":{\"countryCode\":\"IN\",\"cityCode\":\"GOI\",\"cityName\":\"GOA\",\"locationId\":\"CTGOI\",\"locationType\":\"city\",\"deepLink\":\"https://www.makemytrip.com/hotels/hotel-listing/?checkin=05272021&checkout=05292021&city=CTGOI&country=IN&roomStayQualifier=1e0e&checkAvailability=true&_uCurrency=INR&searchText=GOA&locusId=CTGOI&locusType=city&region=IN&homestay=true&filterData=PROPERTY_TYPE%7CVilla%5EUSER_RATING%7C3\",\"deepLinkApp\":\"mmyt://htl/listing/?checkin=05272021&checkout=05292021&city=CTGOI&country=IN&roomStayQualifier=1e0e&checkAvailability=true&searchText=GOA&locusId=CTGOI&locusType=city&region=IN&homestay=true&filter=%7B%22filters%22%3A%7B%22homes%22%3A%5B%22Villa%22%5D%7D%7D\",\"cityPriority\":0,\"appliedFilterMap\":{\"PROPERTY_TYPE\":[{\"filterGroup\":\"PROPERTY_TYPE\",\"filterValue\":\"Villa\",\"rangeFilter\":false}],\"USER_RATING\":[{\"filterGroup\":\"USER_RATING\",\"filterValue\":\"3\",\"rangeFilter\":false}]}}}},{\"description\":\"Stay More,Pay Less!\",\"imageUrl\":\"\",\"priority\":\"7\",\"searchContext\":{\"checkIn\":\"2021-05-27\",\"checkOut\":\"2021-05-29\",\"roomStayParams\":\"1e0e\",\"roomStayCandidates\":[{\"guestCounts\":[{\"count\":\"1\",\"ageQualifyingCode\":\"10\",\"ages\":null}]}]},\"cityTrendingDataMap\":{\"GOI\":{\"countryCode\":\"IN\",\"cityCode\":\"GOI\",\"cityName\":\"GOA\",\"locationId\":\"CTGOI\",\"locationType\":\"city\",\"deepLink\":\"https://www.makemytrip.com/hotels/hotel-listing/?checkin=05272021&checkout=05292021&city=CTGOI&country=IN&roomStayQualifier=1e0e&checkAvailability=true&_uCurrency=INR&searchText=GOA&locusId=CTGOI&locusType=city&region=IN&homestay=true&filterData=PROPERTY_TYPE%7CVilla%5EUSER_RATING%7C3\",\"deepLinkApp\":\"mmyt://htl/listing/?checkin=05272021&checkout=05292021&city=CTGOI&country=IN&roomStayQualifier=1e0e&checkAvailability=true&searchText=GOA&locusId=CTGOI&locusType=city&region=IN&homestay=true&filter=%7B%22filters%22%3A%7B%22homes%22%3A%5B%22Villa%22%5D%7D%7D\",\"cityPriority\":0,\"appliedFilterMap\":{\"PROPERTY_TYPE\":[{\"filterGroup\":\"PROPERTY_TYPE\",\"filterValue\":\"Villa\",\"rangeFilter\":false}],\"USER_RATING\":[{\"filterGroup\":\"USER_RATING\",\"filterValue\":\"3\",\"rangeFilter\":false}]}}}}]},{\"cardType\":\"PROPERTY\",\"heading\":\"For all your travel needs\",\"subHeading\":\"\",\"priority\":\"19\",\"cardList\":[{\"description\":\"Homestays\",\"imageUrl\":\"\",\"priority\":\"19\",\"searchContext\":{\"checkIn\":\"2021-05-27\",\"checkOut\":\"2021-05-29\",\"roomStayParams\":\"2e0e\",\"roomStayCandidates\":[{\"guestCounts\":[{\"count\":\"2\",\"ageQualifyingCode\":\"10\",\"ages\":null}]}]},\"cityTrendingDataMap\":{\"GOI\":{\"countryCode\":\"IN\",\"cityCode\":\"GOI\",\"cityName\":\"\",\"locationId\":\"CTGOI\",\"locationType\":\"city\",\"deepLink\":\"https://www.makemytrip.com/hotels/hotel-listing/?checkin=05272021&checkout=05292021&city=CTGOI&country=IN&roomStayQualifier=2e0e&checkAvailability=true&_uCurrency=INR&searchText=&locusId=CTGOI&locusType=city&region=IN&homestay=true&filterData=PROPERTY_TYPE%7CHomestay%5EUSER_RATING%7C3\",\"deepLinkApp\":\"mmyt://htl/listing/?checkin=05272021&checkout=05292021&city=CTGOI&country=IN&roomStayQualifier=2e0e&checkAvailability=true&searchText=&locusId=CTGOI&locusType=city&region=IN&homestay=true&filter=%7B%22filters%22%3A%7B%22homes%22%3A%5B%22Homestay%22%5D%7D%7D\",\"cityPriority\":0,\"appliedFilterMap\":{\"PROPERTY_TYPE\":[{\"filterGroup\":\"PROPERTY_TYPE\",\"filterValue\":\"Homestay\",\"rangeFilter\":false}],\"USER_RATING\":[{\"filterGroup\":\"USER_RATING\",\"filterValue\":\"3\",\"rangeFilter\":false}]}}}},{\"description\":\"Villas\",\"imageUrl\":\"\",\"priority\":\"7\",\"searchContext\":{\"checkIn\":\"2021-05-27\",\"checkOut\":\"2021-05-29\",\"roomStayParams\":\"1e0e\",\"roomStayCandidates\":[{\"guestCounts\":[{\"count\":\"1\",\"ageQualifyingCode\":\"10\",\"ages\":null}]}]},\"cityTrendingDataMap\":{\"GOI\":{\"countryCode\":\"IN\",\"cityCode\":\"GOI\",\"cityName\":\"\",\"locationId\":\"CTGOI\",\"locationType\":\"city\",\"deepLink\":\"https://www.makemytrip.com/hotels/hotel-listing/?checkin=05272021&checkout=05292021&city=CTGOI&country=IN&roomStayQualifier=1e0e&checkAvailability=true&_uCurrency=INR&searchText=&locusId=CTGOI&locusType=city&region=IN&homestay=true&filterData=PROPERTY_TYPE%7CVilla%5EUSER_RATING%7C3\",\"deepLinkApp\":\"mmyt://htl/listing/?checkin=05272021&checkout=05292021&city=CTGOI&country=IN&roomStayQualifier=1e0e&checkAvailability=true&searchText=&locusId=CTGOI&locusType=city&region=IN&homestay=true&filter=%7B%22filters%22%3A%7B%22homes%22%3A%5B%22Villa%22%5D%7D%7D\",\"cityPriority\":0,\"appliedFilterMap\":{\"PROPERTY_TYPE\":[{\"filterGroup\":\"PROPERTY_TYPE\",\"filterValue\":\"Villa\",\"rangeFilter\":false}],\"USER_RATING\":[{\"filterGroup\":\"USER_RATING\",\"filterValue\":\"3\",\"rangeFilter\":false}]}}}}]}],\"correlationKey\":\"8474e6ec-8e43-4a39-b2d8-f4a4eac77200\",\"responseErrors\":{\"errorList\":[{\"errorCode\":\"400000\",\"errorMessage\":\"Unexpected Error happened during processing %s API, Exception Message: %s\",\"errorAdditionalInfo\":null}]}}";
        Mockito.lenient().when(listingService.convertHesCollectionResponseToOldFormat(Mockito.anyString())).thenReturn("test");
        Assert.assertNotNull(listingService.fetchCollectionsOld(new SearchWrapperInputRequest(),new HashMap<>(),new HashMap<>()));
        Mockito.when(searchHotelsExecutor.fetchCollectionsOld(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(ClientGatewayException.class);
        listingService.fetchCollectionsOld(new SearchWrapperInputRequest(),new HashMap<>(),new HashMap<>());
        
    }

    @Test (expected = Exception.class)
    public void testSearchHotelsOld() throws ClientGatewayException {
        SearchHotelsRequestTransformer searchTransformer = Mockito.mock(SearchHotelsRequestTransformerPWA.class);
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        Mockito.when(oldToNewerRequestTransformer.updateSearchHotelsRequest(Mockito.any())).thenReturn(searchHotelsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.when(searchTransformer.convertSearchRequest(Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());
        Mockito.when(searchHotelsExecutor.searchHotelsOld(Mockito.any(), Mockito.anyMap(), Mockito.anyMap(), Mockito.anyBoolean())).thenReturn("test");
        Assert.assertNotNull(listingService.searchHotelsOld(new SearchWrapperInputRequest(), new HashMap<>(), new HashMap<>(), false));
        Mockito.when(searchHotelsExecutor.searchHotelsOld(Mockito.any(), Mockito.anyMap(), Mockito.anyMap(), Mockito.anyBoolean())).thenThrow(ClientGatewayException.class);
        listingService.searchHotelsOld(new SearchWrapperInputRequest(), new HashMap<>(), new HashMap<>(), false);
    }

    @Test(expected = Exception.class)
    public void testSearchPersonalizedHotelsOld() throws ClientGatewayException {
        SearchHotelsRequestTransformer searchTransformer = Mockito.mock(SearchHotelsRequestTransformerPWA.class);
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        Mockito.when(oldToNewerRequestTransformer.updateSearchHotelsRequest(Mockito.any())).thenReturn(searchHotelsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.when(searchTransformer.convertSearchRequest(Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());
        Mockito.when(searchHotelsExecutor.searchPersonalizedHotelsOld(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("test");
        Assert.assertNotNull((listingService.searchPersonalizedHotelsOld(new SearchWrapperInputRequest(), new HashMap<>(), new HashMap<>())));
        Mockito.when(searchHotelsExecutor.searchPersonalizedHotelsOld(Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(ClientGatewayException.class);
        listingService.searchPersonalizedHotelsOld(new SearchWrapperInputRequest(), new HashMap<>(), new HashMap<>());
    }

    @Test(expected = Exception.class)
    public void testMobLandingOld() throws ClientGatewayException {
        MobLandingRequestTransformer mobLandingRequestTransformer = Mockito.mock(MobLandingRequestTransformer.class);
        MobLandingRequest mobRequestBody = new MobLandingRequest();
        Mockito.when(oldToNewerRequestTransformer.updateMobLandingRequest(Mockito.any())).thenReturn(mobRequestBody);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(mobLandingFactory.getRequestService(Mockito.any())).thenReturn(mobLandingRequestTransformer);
        Mockito.when(mobLandingRequestTransformer.convertMobLandingRequest(Mockito.any(), Mockito.any())).thenReturn(new HotelLandingMobRequestBody());
        Mockito.when(mobLandingExecutor.moblanding(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("test");
        Assert.assertNotNull((listingService.mobLandingOld(new HotelLandingMobRequestBody(), new HashMap<>(), new HashMap<>())));
        Mockito.when(mobLandingExecutor.moblanding(Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(ClientGatewayException.class);
        listingService.mobLandingOld(new HotelLandingMobRequestBody(), new HashMap<>(), new HashMap<>());
    }

    @Test(expected = Exception.class)
    public void testListingMapOld() throws ClientGatewayException {
        SearchHotelsRequestTransformer searchTransformer = Mockito.mock(SearchHotelsRequestTransformerPWA.class);
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        Mockito.when(oldToNewerRequestTransformer.updateSearchHotelsRequest(Mockito.any())).thenReturn(searchHotelsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.when(searchTransformer.convertSearchRequest(Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());
        Mockito.when(searchHotelsExecutor.searchHotelsOld(Mockito.any(), Mockito.anyMap(), Mockito.anyMap(), Mockito.anyBoolean())).thenReturn("test");
        Assert.assertNotNull(listingService.searchHotelsOld(new SearchWrapperInputRequest(),new HashMap<>(),new HashMap<>(), false));
        Mockito.when(searchHotelsExecutor.listingMapOld(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(ClientGatewayException.class);
        listingService.listingMapOld(new SearchWrapperInputRequest(),new HashMap<>(),new HashMap<>());
    }

    @Test (expected = Exception.class)
    public void searchHotelsTest() throws ClientGatewayException {

    	SearchHotelsRequestTransformer searchTransformer = Mockito.mock(SearchHotelsRequestTransformerPWA.class);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
//        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
//        Mockito.when(searchTransformer.convertSearchRequest(Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());
//
//        Mockito.when(searchHotelsExecutor.searchHotels(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new SearchWrapperResponseBO.Builder().build());
        SearchHotelsResponseTransformer responseTransformer = Mockito.mock(SearchHotelsResponseTransformerPWA.class);
//        Mockito.when(searchHotelsFactory.getResponseService(Mockito.any())).thenReturn(responseTransformer);
//        Mockito.when(responseTransformer.convertSearchHotelsResponse(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new SearchHotelsResponse());
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        searchHotelsRequest.setSearchCriteria(new SearchHotelsCriteria());
        searchHotelsRequest.getSearchCriteria().setPersonalizedSearch(false);
        SearchHotelsResponse resp = listingService.searchHotels(searchHotelsRequest,null,null);
        Assert.assertNotNull(resp);
        searchHotelsRequest.getSearchCriteria().setPersonalizedSearch(true);
//        Mockito.when(searchHotelsExecutor.searchPersonalizedHotels(Mockito.any(), Mockito.anyMap(), Mockito.anyMap(), Mockito.anyBoolean())).thenReturn(new ListingPagePersonalizationResponsBO());
        resp = listingService.searchHotels(searchHotelsRequest,null,null);
        Assert.assertNotNull(resp);
        listingService.searchHotels(null,null,null);

    }

    @Test (expected = Exception.class)
    public void testGetMobConfig() throws ClientGatewayException {
        Mockito.when(mobConfigHelper.getHotelMobConfigAsString(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("Response");
        Assert.assertNotNull(listingService.getMobConfig(new HashMap<>(),new HashMap<>()));
        
        Map<String, String[]> paramMap = new HashMap<>();
        String[] value = {"1.31"};
        paramMap.put("v", value);
        paramMap.put("variant", value);
        Mockito.when(mobConfigHelper.getHotelMobConfigAsString(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("Response");
        Assert.assertNotNull(listingService.getMobConfig(new HashMap<>(),paramMap));
        listingService.getMobConfig(new HashMap<>(),null);
    }
    
//    @Test
//    public void testGetMetaDataByCityResponse() throws ClientGatewayException{
//    	Mockito.when(commonHelper.getInboundCurrencyCode(Mockito.any(), Mockito.any(),
//    			Mockito.any())).thenReturn("INR");
//    	Mockito.when(searchHotelsExecutor.getMetaDataResponse(Mockito.any(),
//    			Mockito.anyMap())).thenReturn("");
//
//    	Mockito.when(hotelMetaDataService.filterLocationAndFaclity(Mockito.any(),
//    			Mockito.any())).thenReturn("test");
//
//    	String resp = listingService.getMetaDataByCityResponse("cityId", null, null,
//    			null, "correlationKey", new HashMap<>());
//    	Assert.assertNotNull(resp);
//    }

    @Test (expected = Exception.class)
    public void testListingPersonalizationOld() throws ClientGatewayException {
        SearchHotelsRequestTransformer searchTransformer = Mockito.mock(SearchHotelsRequestTransformerPWA.class);
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        Mockito.lenient().when(oldToNewerRequestTransformer.updateSearchHotelsRequest(Mockito.any())).thenReturn(searchHotelsRequest);
        Mockito.lenient().when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.lenient().when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.lenient().when(searchTransformer.convertSearchRequest(Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());
        
        Assert.assertNotNull(listingService.listingPersonalizationOld(new HotelLandingMobRequestBody(),Mockito.any(),new HashMap<>()));
        listingService.listingPersonalizationOld(new HotelLandingMobRequestBody(),Mockito.any(),new HashMap<>());
    }

    @Test (expected = Exception.class)
    public void testNearByOld() throws ClientGatewayException {
        SearchHotelsRequestTransformer searchTransformer = Mockito.mock(SearchHotelsRequestTransformerPWA.class);
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        Mockito.lenient().when(oldToNewerRequestTransformer.updateNearByHotelsRequest(Mockito.any())).thenReturn(searchHotelsRequest);
        Mockito.lenient().when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.lenient().when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.lenient().when(searchTransformer.convertSearchRequest(Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());
        
        Assert.assertNotNull(listingService.nearByOld(new SearchWrapperInputRequest(),Mockito.any(),new HashMap<>()));
        listingService.nearByOld(new SearchWrapperInputRequest(),Mockito.any(),new HashMap<>());
    }

    @Test
    public void testConvertHesCollectionResponseToOldFormat() throws ClientGatewayException, JsonProcessingException {
        String resp="{\"cardCollections\":[{\"cardType\":\"TRENDINGNOW\",\"heading\":\"Trending Now\",\"subHeading\":\"\",\"priority\":\"4\",\"cardList\":[{\"description\":\"Spacious apartments for a fun-filled vacay\",\"imageUrl\":\"https://promos.makemytrip.com/altaccoimages/group_2.png\",\"priority\":\"4\",\"searchContext\":{\"checkIn\":\"2021-05-28\",\"checkOut\":\"2021-05-30\",\"roomStayParams\":\"2e0e\"},\"cityTrendingDataMap\":{\"GOI\":{\"countryCode\":\"IN\",\"cityCode\":\"GOI\",\"cityName\":\"GOA\",\"locationId\":\"CTGOI\",\"locationType\":\"city\",\"cityPriority\":0,\"appliedFilterMap\":{\"USER_RATING\":[{\"filterGroup\":\"USER_RATING\",\"filterValue\":\"3\",\"rangeFilter\":false}],\"PROPERTY_TYPE\":[{\"filterGroup\":\"PROPERTY_TYPE\",\"filterValue\":\"Villa\",\"rangeFilter\":false}]}}}}]}]}";
        CollectionsResponseBo<SearchWrapperHotelEntity> collectionsResponseBO = new ObjectMapper().readValue(resp,new TypeReference<CollectionsResponseBo<SearchWrapperHotelEntity>>() {});
        Mockito.when(objectMapperUtil.getObjectFromJsonWithType(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(collectionsResponseBO);
        Mockito.when(objectMapperUtil.getJsonFromObject(Mockito.any(),Mockito.any())).thenReturn("test");
        Assert.assertNotNull(listingService.convertHesCollectionResponseToOldFormat(resp));

    }

    @Test (expected = Exception.class)
    public void testFetchCollections() throws ClientGatewayException {
        SearchHotelsRequestTransformer  searchHotelsRequestTransformer=Mockito.mock(SearchHotelsRequestTransformerAndroid.class);
        SearchHotelsResponseTransformer responseTransformer = Mockito.mock(SearchHotelsResponseTransformerAndroid.class);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);

        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchHotelsRequestTransformer);

        Mockito.when(searchHotelsExecutor.fetchCollectionsOld(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("test");

        CollectionsResponseBo<SearchWrapperHotelEntity> response=createSuccessDummyHesResponse();
        Mockito.when(objectMapperUtil.getObjectFromJsonWithType(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
        Mockito.when(searchHotelsFactory.getResponseService(Mockito.any())).thenReturn(responseTransformer);
        Assert.assertNotNull(listingService.fetchCollections(new FetchCollectionRequest(),new HashMap<>(),new HashMap<>()));

        response=createFailedDummyHesResponse();
        Mockito.when(objectMapperUtil.getObjectFromJsonWithType(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
        Assert.assertNotNull(listingService.fetchCollections(new FetchCollectionRequest(),new HashMap<>(),new HashMap<>()).getResponseErrors());

        response=createDummyHesResponseWithNoCollections();
        Mockito.when(objectMapperUtil.getObjectFromJsonWithType(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
        FetchCollectionRequest fetchCollectionRequest = new FetchCollectionRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setPageContext(Constants.PAGE_CONTEXT_REVIEW);
        fetchCollectionRequest.setRequestDetails(requestDetails);
        Assert.assertNotNull(listingService.fetchCollections(fetchCollectionRequest,new HashMap<>(),new HashMap<>()).getResponseErrors());

        Mockito.when(searchHotelsExecutor.fetchCollectionsOld(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(ClientGatewayException.class);
        listingService.fetchCollections(new FetchCollectionRequest(),new HashMap<>(),new HashMap<>());

    }

    @Test (expected = Exception.class)
    public void testFetchCollectionsV2() throws ClientGatewayException {
        SearchHotelsRequestTransformer  searchHotelsRequestTransformer=Mockito.mock(SearchHotelsRequestTransformerAndroid.class);
        SearchHotelsResponseTransformer responseTransformer = Mockito.mock(SearchHotelsResponseTransformerAndroid.class);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);

        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchHotelsRequestTransformer);

        Mockito.when(searchHotelsExecutor.fetchCollectionsOld(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("test");

//        Mockito.when(fetchCollectionHelper.shouldAddFilters(Mockito.anyString(), Mockito.any(), Mockito.anyMap())).thenReturn(true);

        CollectionsResponseBo<SearchWrapperHotelEntity> response=createSuccessDummyHesResponse();
        Mockito.when(objectMapperUtil.getObjectFromJsonWithType(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
//        Mockito.when(searchHotelsFactory.getResponseService(Mockito.any())).thenReturn(responseTransformer);
        Assert.assertNotNull(listingService.fetchCollectionsV2(new FetchCollectionRequest(),new HashMap<>(),new HashMap<>()));

        response=createFailedDummyHesResponse();
        Mockito.when(objectMapperUtil.getObjectFromJsonWithType(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
        Assert.assertNotNull(listingService.fetchCollectionsV2(new FetchCollectionRequest(),new HashMap<>(),new HashMap<>()).getResponseErrors());

        response=createDummyHesResponseWithNoCollections();
        Mockito.when(objectMapperUtil.getObjectFromJsonWithType(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
        FetchCollectionRequest fetchCollectionRequest = new FetchCollectionRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setPageContext(Constants.PAGE_CONTEXT_REVIEW);
        fetchCollectionRequest.setRequestDetails(requestDetails);
        Assert.assertNotNull(listingService.fetchCollectionsV2(fetchCollectionRequest,new HashMap<>(),new HashMap<>()).getResponseErrors());

        Mockito.when(searchHotelsExecutor.fetchCollectionsOld(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(ClientGatewayException.class);
        listingService.fetchCollectionsV2(new FetchCollectionRequest(),new HashMap<>(),new HashMap<>());

    }

    @Test
    public void testFetchCollections2() throws ClientGatewayException {
        SearchHotelsRequestTransformer  searchHotelsRequestTransformer=Mockito.mock(SearchHotelsRequestTransformerAndroid.class);
        SearchHotelsResponseTransformer responseTransformer = Mockito.mock(SearchHotelsResponseTransformerAndroid.class);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);

        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchHotelsRequestTransformer);

        Mockito.when(searchHotelsExecutor.fetchCollectionsOld(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("test");

        Mockito.when(objectMapperUtil.getObjectFromJsonWithType(Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(JsonParseException.class);
        Assert.assertNotNull(listingService.fetchCollections(new FetchCollectionRequest(),new HashMap<>(),new HashMap<>()));

    }

    private CollectionsResponseBo<SearchWrapperHotelEntity> createSuccessDummyHesResponse() {
        CollectionsResponseBo<SearchWrapperHotelEntity> resp=new CollectionsResponseBo<SearchWrapperHotelEntity>();
        resp.setCardCollections(createCardCollection());
        resp.setCollectionsResponse(createCollections());
        resp.setCorrelationKey("csdfsd");
        resp.setSuggestedFilters(new CardCollections());
        resp.setMoreFiltersMap(createDummyMoreFiltersMap());
        return resp;
    }

    private CollectionsResponseBo<SearchWrapperHotelEntity> createFailedDummyHesResponse() {
        CollectionsResponseBo<SearchWrapperHotelEntity> resp=new CollectionsResponseBo<SearchWrapperHotelEntity>();
        resp.setResponseErrors(new ResponseErrors());
        resp.setCorrelationKey("csdfsd");
        return resp;
    }

    private CollectionsResponseBo<SearchWrapperHotelEntity> createDummyHesResponseWithNoCollections() {
        CollectionsResponseBo<SearchWrapperHotelEntity> resp=new CollectionsResponseBo<SearchWrapperHotelEntity>();
        resp.setCorrelationKey("csdfsd");
        return resp;
    }

    private List<FeaturedCollections<SearchWrapperHotelEntity>> createCollections() {
        List<FeaturedCollections<SearchWrapperHotelEntity>> list=new ArrayList<>();
        FeaturedCollections<SearchWrapperHotelEntity> fc=new FeaturedCollections<SearchWrapperHotelEntity>();
        list.add(fc);
        return list;
    }

    private Map<String, List<Filter>> createDummyMoreFiltersMap() {
        Map<String, List<Filter>> moreFiltersMap = new HashMap<>();
        List<Filter> filterList = new ArrayList<>();
        Filter filter = new Filter();
        FilterGroup filterGroup = FilterGroup.UGC_GROUP_RATING;
        filter.setFilterGroup(filterGroup);
        filter.setTitle("4 to 5");
        filter.setFilterValue("4_5");
        filter.setRangeFilter(false);
        filter.setSequence(5);
        filter.setThreshold(30);
        filter.setQuantityFilter(false);
        filterList.add(filter);
        moreFiltersMap.put("Popular Filters", filterList);
        return moreFiltersMap;
    }

    private List<CardCollections> createCardCollection() {
        List<CardCollections> cardCollectionList=new ArrayList<>();
        CardCollections cardCollection=new CardCollections();
        cardCollectionList.add(cardCollection);
        return cardCollectionList;
    }

    @Test
    public void submitGroupBookingExceptionTest() throws ClientGatewayException {
        Assert.assertNotNull(listingService.submitGroupBooking(null, 1));
    }

    @Test
    public void submitGroupBookingTest() throws ClientGatewayException {
        Assert.assertNotNull(listingService.submitGroupBooking(new GroupBookingRequest(), 1));
    }

    @Test
    public void testTravelTip_Success() throws ClientGatewayException {
        // Arrange
        TravelTipRequest travelTipRequest = new TravelTipRequest();
        travelTipRequest.setClient("PWA");
        travelTipRequest.setCorrelationKey("test-correlation-key");

        Map<String, String[]> paramMap = new HashMap<>();
        Map<String, String> httpHeaderMap = new HashMap<>();
        httpHeaderMap.put("mmt-auth", "test-auth-token");

        String mockResponse = "{\"name\":\"Test City\",\"bgImg\":\"test-bg-image.jpg\",\"cardsData\":{\"overviewCards\":[],\"summaryCards\":[]}}";
        TravelTipWrapperResponse mockWrapperResponse = new TravelTipWrapperResponse();
        mockWrapperResponse.setName("Test City");
        mockWrapperResponse.setBgImg("test-bg-image.jpg");
        mockWrapperResponse.setCardsData(new CardsData());

        TravelTipResponse expectedResponse = new TravelTipResponse();
        expectedResponse.setHeader("Test City");
        expectedResponse.setBgImg("test-bg-image.jpg");
        expectedResponse.setCardsData(new CardsData());

        TravelTipResponseTransformer mockResponseTransformer = Mockito.mock(TravelTipResponseTransformer.class);

        // Mock behavior
        Mockito.when(travelTipExecutor.fetchTravelTips(Mockito.eq(travelTipRequest), Mockito.eq(paramMap), Mockito.eq(httpHeaderMap)))
               .thenReturn(mockResponse);
        Mockito.when(objectMapperUtil.getObjectFromJson(Mockito.eq(mockResponse), Mockito.eq(TravelTipWrapperResponse.class), Mockito.any()))
               .thenReturn(mockWrapperResponse);
        Mockito.when(travelTipFactory.getResponseService(Mockito.eq("PWA")))
               .thenReturn(mockResponseTransformer);
        Mockito.when(mockResponseTransformer.convertTravelTipResponse(Mockito.eq(mockWrapperResponse), Mockito.eq("PWA")))
               .thenReturn(expectedResponse);

        // Act
        TravelTipResponse actualResponse = listingService.travelTip(travelTipRequest, paramMap, httpHeaderMap);

        // Assert
        Assert.assertNotNull(actualResponse);
        Assert.assertEquals("Test City", actualResponse.getHeader());
        Assert.assertEquals("test-bg-image.jpg", actualResponse.getBgImg());
        Assert.assertNotNull(actualResponse.getCardsData());

        // Verify interactions
        Mockito.verify(travelTipExecutor).fetchTravelTips(travelTipRequest, paramMap, httpHeaderMap);
        Mockito.verify(objectMapperUtil).getObjectFromJson(mockResponse, TravelTipWrapperResponse.class, DependencyLayer.ORCHESTRATOR);
        Mockito.verify(travelTipFactory).getResponseService("PWA");
        Mockito.verify(mockResponseTransformer).convertTravelTipResponse(mockWrapperResponse, "PWA");
    }

    @Test
    public void testTravelTip_EmptyResponse() throws ClientGatewayException {
        // Arrange
        TravelTipRequest travelTipRequest = new TravelTipRequest();
        travelTipRequest.setClient("PWA");
        Map<String, String[]> paramMap = new HashMap<>();
        Map<String, String> httpHeaderMap = new HashMap<>();

        // Mock behavior - empty response
        Mockito.when(travelTipExecutor.fetchTravelTips(Mockito.any(), Mockito.any(), Mockito.any()))
               .thenReturn("");

        // Act & Assert - expect exception
        try {
            listingService.travelTip(travelTipRequest, paramMap, httpHeaderMap);
            Assert.fail("Expected ErrorResponseFromDownstreamException was not thrown");
        } catch (ClientGatewayException e) {
            // Expected exception
            Assert.assertTrue(e.getMessage().contains(Constants.ERROR_MESSAGE_CITY_DATA_NOT_AVAILABLE));
        }

        // Verify interactions
        Mockito.verify(travelTipExecutor).fetchTravelTips(travelTipRequest, paramMap, httpHeaderMap);
    }

    /*@Test
    public void testTravelTip_ErrorResponse() throws ClientGatewayException {
        // Arrange
        TravelTipRequest travelTipRequest = new TravelTipRequest();
        travelTipRequest.setClient("PWA");
        Map<String, String[]> paramMap = new HashMap<>();
        Map<String, String> httpHeaderMap = new HashMap<>();

        String mockResponse = "{\"errorResponse\":{\"errorList\":[{\"errorCode\":\"ERR001\",\"errorMessage\":\"Test error\"}]}}";
        TravelTipWrapperResponse mockWrapperResponse = new TravelTipWrapperResponse();
        Error error = new Error.Builder()
            .buildErrorCode("ERR001", "Test error")
            .build();
        ResponseErrors responseErrors = new ResponseErrors.Builder()
            .buildErrorList(Collections.singletonList(error))
            .build();
        mockWrapperResponse.setErrorResponse(responseErrors);

        // Mock behavior
        Mockito.when(travelTipExecutor.fetchTravelTips(Mockito.any(), Mockito.any(), Mockito.any()))
               .thenReturn(mockResponse);
        Mockito.when(objectMapperUtil.getObjectFromJson(Mockito.eq(mockResponse), Mockito.eq(TravelTipWrapperResponse.class), Mockito.any()))
               .thenReturn(mockWrapperResponse);

        // Act & Assert - expect exception
        try {
            listingService.travelTip(travelTipRequest, paramMap, httpHeaderMap);
            Assert.fail("Expected ErrorResponseFromDownstreamException was not thrown");
        } catch (ClientGatewayException e) {
            // Expected exception
            Assert.assertTrue(e.getMessage().contains("Test error"));
        }

        // Verify interactions
        Mockito.verify(travelTipExecutor).fetchTravelTips(travelTipRequest, paramMap, httpHeaderMap);
        Mockito.verify(objectMapperUtil).getObjectFromJson(mockResponse, TravelTipWrapperResponse.class, DependencyLayer.ORCHESTRATOR);
    }*/

    @Test (expected = Exception.class)
    public void batchFilterResponseTest() throws ClientGatewayException {

        FilterRequestTransformer searchTransformer = Mockito.mock(FilterRequestTransformer.class);
        FilterSearchMetaDataResponse filterSearchMetaDataResponse = new FilterSearchMetaDataResponse();
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(filterFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.when(searchTransformer.convertSearchRequest(Mockito.any(),Mockito.any())).thenReturn(new SearchWrapperInputRequest());
        Mockito.lenient().when(filterExecutor.filterCount(Mockito.any(), Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(filterSearchMetaDataResponse);
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        filterCountRequest.setClient("DESKTOP");
        filterCountRequest.setRequestDetails(new RequestDetails());
        FilterResponse resp = listingService.batchFilterResponse(filterCountRequest,null,true, null);
        Assert.assertNotNull(resp);

        listingService.batchFilterResponse(null,null,null, null);

    }

    @Test (expected = Exception.class)
    public void batchFilterResponseThrowsExceptionTest() throws ClientGatewayException {

        FilterRequestTransformer searchTransformer = Mockito.mock(FilterRequestTransformer.class);
        FilterSearchMetaDataResponse filterSearchMetaDataResponse = new FilterSearchMetaDataResponse();
        List<Error> errors = new ArrayList<>();
        errors.add(new Error.Builder().buildErrorCode("1234","error ").build());
        filterSearchMetaDataResponse.setResponseErrors(new ResponseErrors.Builder().buildErrorList(errors).build());
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(filterFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.lenient().when(filterExecutor.filterCount(Mockito.any(), Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(filterSearchMetaDataResponse);
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        filterCountRequest.setRequestDetails(new RequestDetails());
        FilterResponse resp = listingService.batchFilterResponse(filterCountRequest,null,true, null);
        Assert.assertNotNull(resp);

        listingService.batchFilterResponse(null,null,null, null);

    }

    @Test (expected = Exception.class)
    public void testLandingDiscoveryOld() throws ClientGatewayException {
        SearchHotelsRequestTransformer searchTransformer = Mockito.mock(SearchHotelsRequestTransformerPWA.class);
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        Mockito.when(oldToNewerRequestTransformer.updateSearchHotelsRequest(Mockito.any())).thenReturn(searchHotelsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.when(searchTransformer.convertSearchRequest(Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());
        Mockito.when(searchHotelsExecutor.landingDiscoveryOld(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("test");
        Assert.assertNotNull(listingService.landingDiscoveryOld(new SearchWrapperInputRequest(),new HashMap<>(),new HashMap<>()));
        Mockito.when(searchHotelsExecutor.landingDiscoveryOld(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(ClientGatewayException.class);
        listingService.landingDiscoveryOld(new SearchWrapperInputRequest(),new HashMap<>(),new HashMap<>());
    }

    @Test
    public void fetchCityOverViewResponseTest() throws ClientGatewayException {
        CityOverviewRequest cityOverviewRequest = new CityOverviewRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setIdContext("B2C");
        DeviceDetails deviceDetails = new DeviceDetails();
        cityOverviewRequest.setRequestDetails(requestDetails);
        cityOverviewRequest.setDeviceDetails(deviceDetails);
        Mockito.when(utility.buildCityOverviewHesRequest(Mockito.any(),Mockito.any(),Mockito.anyString())).thenReturn(new CityOverviewHesRequest());
        Mockito.when(listingMapExecutor.fetchCityOverview(Mockito.any(),Mockito.any(),Mockito.anyMap())).thenReturn(new WikiResponse());
        MobLandingResponseTransformer mobLandingResponseTransformer = new MobLandingResponseTransformerDesktop();
        mobLandingResponseTransformer.buildMatchMakerResponse(new WikiResponse());
        Mockito.when(mobLandingFactory.getResponseService(Mockito.any())).thenReturn(mobLandingResponseTransformer);
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setProfileType("Personal");
        commonModifierResponse.setExtendedUser(extendedUser);
        Mockito.when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(commonModifierResponse);
        MatchMakerResponseCG matchMakerResponseCG = listingService.fetchCityOverViewResponse(cityOverviewRequest,new HashMap<>(),new HashMap<>(),"xyz");
        Assert.assertNotNull(matchMakerResponseCG);
    }

    @Test
    public void testFetchUpsellRateplanResponse() throws ClientGatewayException {
        SearchHotelsRequest fetchUpsellRateplanRequest = new SearchHotelsRequest();
        fetchUpsellRateplanRequest.setSearchCriteria(new SearchHotelsCriteria());
        SearchHotelsRequestTransformer searchTransformer = Mockito.mock(SearchHotelsRequestTransformerPWA.class);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new CommonModifierResponse());
        Mockito.when(searchHotelsFactory.getRequestService(Mockito.any())).thenReturn(searchTransformer);
        Mockito.when(crossSellUtil.isCrossSellRequest(Mockito.any())).thenReturn(false);
        Mockito.when(searchTransformer.convertSearchRequest(Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());
        Mockito.when(listingHelper.convertFetchUpsellRateplanresponse(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new UpsellRateplanResponse());
        UpsellRateplanResponse expectedResponse = listingService.fetchUpsellRateplanResponse(fetchUpsellRateplanRequest,new HashMap<>(),new HashMap<>(),"test123");
        Assert.assertNotNull(expectedResponse);
    }
    @Test
    public void processCrossSellDataTest(){
        String response = "{\"staticHotelCounts\":0,\"totalHotelCounts\":1,\"noMoreAvailableHotels\":false,\"hotelList\":[{\"id\":\"200703161155527273\",\"name\":\"Radisson Blu Plaza Delhi Airport\",\"propertyType\":\"Hotel\",\"propertyLabel\":\"Hotel\",\"mainImages\":[\"https://r1imghtlak.mmtcdn.com/d076a970c29e11ebbbed0242ac110005.jpg?&downsize=583:388&output-format=jpg\"],\"mtkey\":\"5354803364029868196\",\"starRating\":5,\"currencyCode\":{\"id\":\"inr\",\"value\":\"inr\"},\"address\":{\"area\":[\"Mahipalpur\",\"Mahipalpur Village\",\"Block R\"],\"line2\":\"Mahipalpur\",\"line1\":\"National Highway 8,\\r\\n\"},\"displayFare\":{\"tax\":{\"value\":0},\"slashedPrice\":{\"value\":13140,\"maxFraction\":0,\"avgeragePriceNoTax\":13140,\"sellingPriceNoTax\":13140,\"sellingPriceWithTax\":13140},\"actualPrice\":{\"value\":14600,\"maxFraction\":0,\"avgeragePriceNoTax\":14600,\"sellingPriceNoTax\":14600,\"sellingPriceWithTax\":14600},\"extraAdult\":{\"value\":0},\"segmentId\":\"1120\",\"ddmu\":0,\"totalRoomCount\":1,\"couponReceived\":false,\"couponSkipped\":false,\"ddApplied\":false,\"displayPriceBreakDown\":{\"displayPrice\":13140,\"displayPriceAlternateCurrency\":0,\"nonDiscountedPrice\":14600,\"savingPerc\":10,\"totalSaving\":1460,\"roundedOffDelta\":0,\"basePrice\":14600,\"hotelTax\":3022,\"hotelServiceCharge\":0,\"mmtServiceCharge\":920,\"mmtDiscount\":1460,\"blackDiscount\":0,\"cdfDiscount\":0,\"charityAmountV2\":0,\"wallet\":0,\"effectiveDiscount\":0,\"tdsAmount\":0,\"extraPaxPrice\":0,\"pricingKey\":\"s9rHC9RN7n+MNBRmAgubuABaqPIHe3xJtEpJhWppgDQwWhu6ey+TlESjUJ3Zeu4hIJMiewtwCgqaiTlwir5WQqhz/dzHpft21ck7cdORvP29yT+ur1GP5XxWp8siw+m7K7v/5pQoNoh9BlBQttgUJbN5vJQc6oSyM+pg3c1Gp267yP27PGsXg4f50W+BZ7z2xW0mHtldBkJNSm/7H2AkZUMtaRArP5iFy+atBsdWah0LayAT7pYlwAJSHuRlV0uRsLlvP8F++3eNHdS97dS/Hy0iAsCCqwutN42Drrmo3u0=\",\"pricingDivisor\":1,\"totalTax\":3942,\"effectivePrice\":13140,\"brinInclusivePrice\":0,\"hotelierCouponDiscount\":0,\"gstDiscountHotelierCoupon\":0,\"affiliateFee\":0,\"pinCodeMandatory\":false,\"totalAmount\":13140,\"metaDiscount\":0,\"addonPrice\":0,\"nonDiscountedAddonPrice\":0,\"currencyConvFactorToINR\":1,\"cbrAvailable\":false,\"serviceCharge\":0,\"ddmarkupAlreadyApplied\":false,\"charityV2Enable\":false,\"taxIncluded\":false},\"conversionFactor\":0,\"slotRate\":false,\"hotelTax\":3022.2,\"hotelierServiceCharge\":0,\"isBNPLApplicable\":false,\"originalBNPL\":false,\"dateOfDelayedPayment\":0,\"apPromotionDiscount\":0,\"aajKaBhaoApplied\":\"NA\",\"taxExcluded\":false},\"categories\":[\"Chain\",\"MyBiz Assured\",\"Couple Friendly\",\"Premium Properties\",\"package_hotels\",\"premium_hotels\"],\"primaryAreas\":{\"locationId\":\"ARMAHI\",\"name\":\"Mahipalpur\",\"latLong\":[77.137055,28.547498]},\"cityName\":\"Delhi\",\"countryName\":\"India\",\"countryCode\":\"IN\",\"cityCode\":\"CTDEL\",\"stayType\":\"Hotel\",\"maxOccupancy\":0,\"ignoreEntireProperty\":false,\"appDeeplink\":\"mmyt://htl/detail/?topHtlId=200703161155527273&hotelId=200703161155527273&checkin=04272024&checkout=04282024&country=IN&city=CTDEL&roomStayQualifier=2e0e&_uCurrency=inr&checkAvailability=false&locusId=CTDEL&locusType=city&region=in&funnelName=hotels\",\"sharingUrl\":\"https://applinks.makemytrip.com/q8DzuKn2gU?hotelId=200703161155527273&city=CTDEL&country=IN&roomStayQualifier=2e0e&checkin=04272024&checkout=04282024&cmp=hotelAppShareNew&locusId=CTDEL&locusType=city&region=in&funnelName=hotels\",\"detailDeeplinkUrl\":\"https://www.makemytrip.com/hotels/hotel-details?hotelId=200703161155527273&checkin=04272024&checkout=04282024&country=IN&city=CTDEL&openDetail=true&currency=inr&roomStayQualifier=2e0e&locusId=CTDEL&locusType=city&region=in&viewType=PREMIUM&funnelName=hotels\",\"locationPersuasion\":[\"Mahipalpur\",\"5.2 km from Indira Gandhi International Airport\"],\"locationDist\":0,\"isAbsorptionApplied\":false,\"freeCancellationText\":\"Free Cancellation\",\"featured\":\"N\",\"isSoldOut\":false,\"isFreeWifiAvail\":true,\"isPAHAvailable\":false,\"isShortlisted\":false,\"hotelFacilities\":\"Basic Facilities,Transfers,Family and kids,Food and Drinks,Payment Services,Safety and Security,Health and wellness,Entertainment,General Services,Beauty and Spa,Outdoor Activities and Sports,Indoor Activities and Sports,Common Area,Shopping,Business Center and Conferences,Other Facilities\",\"geoLocation\":{\"latitude\":\"28.54382\",\"longitude\":\"77.1197\"},\"showBhfPersuasion\":false,\"hiddenGem\":false,\"isMTKeyEnabledSearch\":false,\"poiTag\":\"5.2 km from Indira Gandhi International Airport\",\"isValuePlus\":false,\"isFreeCancellation\":true,\"freeCancellationAvailable\":true,\"breakFast\":false,\"breakFastAvailable\":true,\"shortDescription\":\"A five-star property that has been serving smiles and hospitality par excellence for the past two decades, Radisson Blu Plaza De \",\"userEligiblePayMode\":\"PAS\",\"couponAutoApply\":true,\"isPAHTariffAvailable\":false,\"paymentMode\":\"PAS\",\"avgTP\":0,\"hasCollections\":false,\"dynamicFilters\":[\"USP\",\"Inhouse_dining\",\"Hotel Highlights\"],\"bedCount\":0,\"mealPlanIncluded\":{\"desc\":\"Room Only\",\"code\":\"EP\"},\"segments\":{\"segmentsCount\":0},\"hotelChainCode\":\"*********\",\"lowestRateSupplierCode\":\"INGO\",\"lowestRateSegmentId\":\"1120\",\"stateCode\":\"STDEL\",\"stateName\":\"Delhi\",\"twoMealAvailable\":false,\"allMealAvailable\":false,\"travellerImageCount\":0,\"lowestRoomAvailCount\":1,\"lowestAvailCount\":9,\"totalRoomAvailCount\":9,\"isBNPLAvailable\":false,\"bnplBaseAmount\":0,\"pahWalletApplicable\":false,\"bestPriceGuaranteed\":false,\"pahxAvailable\":false,\"pahxApplicable\":false,\"usp\":{\"USP\":{\"sub_title\":\"Signature vegetarian & non-vegetarian kababs at The Great Kabab Factory\",\"title\":\"USP\",\"subTags\":null},\"Inhouse_dining\":{\"sub_title\":\"Neung Roi: counted among India's best Thai restaurants\",\"title\":\"Inhouse Dining\",\"subTags\":null},\"Hotel Highlights\":{\"sub_title\":\"An expansive free-form outdoor pool & a 20,000-sq.ft. spa\",\"title\":\"Hotel Highlights\",\"subTags\":null}},\"addOnAvailableOnLowest\":false,\"addOnAvailable\":false,\"sponsored\":false,\"newType\":false,\"listingType\":\"room\",\"propertyRules\":[\"Hotel may charge compulsory gala dinner supplement on Christmas / New Year's eve or other festive periods. Any such supplements need to be paid directly at the hotel.\\r\\n\\r\\nKindly be informed that the hotel Swimming pool shall remain closed for scheduled annual maintenance till January 16th 2014. We would request your support during this period and regret inconvenience. Thank you for bearing with us.\"],\"trackingInfo\":null,\"blackAccelerated\":false,\"maxAdultAtSamePrice\":0,\"bedRoomCount\":0,\"maxChildAtSamePrice\":0,\"alternateDatesAvailable\":false,\"lowestBlackPackage\":false,\"blackPackageAvailable\":false,\"netRateAvailable\":false,\"lowestNetRate\":false,\"lowestRoomType\":\"Superior Room\",\"roomCount\":0,\"lastBooked\":false,\"lowestRatePlanMealDiff\":0,\"lowestRatePlanPahDiff\":0,\"notInterested\":false,\"lowestRoomCodesRatePlans\":\"24485--5354803364029868196\",\"priceDifferenceWithPivot\":0,\"lowestRateBnpl\":false,\"cancellationTimeline\":{\"checkInDate\":\"27 Apr\",\"checkInDateTime\":\"3 PM\",\"cancellationDate\":\"26 Apr\",\"cancellationDateTime\":\"05:59 PM\",\"cancellationDateInDateFormat\":\"26-Apr-2024 17:59\",\"subTitle\":\"Free Cancellation\",\"freeCancellationText\":\"Free Cancellation till \u202A26 Apr 05:59 PM\u202C\",\"title\":\"STAY FLEXIBLE WITH\",\"bookingDate\":\"27 Feb\",\"freeCancellationBenefits\":[{\"type\":\"\",\"text\":\"Free Cancellation till 26 Apr, 5:59 PM\"},{\"type\":\"\",\"text\":\"No refund if cancelled after 26 Apr, 6:00 PM\"}],\"fcTextForPersuasion\":\"Free Cancellation before \u202A26 Apr 05:59 PM\u202C\",\"cancellationPolicyTimelineList\":[{\"startDate\":\"27 Feb\",\"startDateTime\":\"02:45 PM\",\"endDate\":\"26 Apr\",\"endDateTime\":\"05:59 PM\",\"text\":\"100% Refund\",\"fcBenefit\":{\"type\":\"\",\"text\":\"Free Cancellation till 26 Apr, 5:59 PM\"},\"refundable\":true,\"refundText\":\"100%\",\"type\":\"FULL_REFUND\"},{\"startDate\":\"26 Apr\",\"startDateTime\":\"06:00 PM\",\"endDate\":\"27 Apr\",\"endDateTime\":\"05:59 PM\",\"text\":\"Non Refundable\",\"fcBenefit\":{\"type\":\"\",\"text\":\"No refund if cancelled after 26 Apr, 6:00 PM\"},\"refundable\":false,\"refundText\":\"0%\",\"type\":\"NO_REFUND\"}],\"tillDate\":\"26-Apr-2024 17:59\",\"bnplTitleText\":\"\"},\"staycationAvailable\":false,\"staycationRateAvlblForFilter\":false,\"lowestRateStaycation\":false,\"shortList\":false,\"mmtHotelCategory\":\"PREMIUM\",\"contextualPersuasions\":{},\"savingPercentage\":0,\"groupBookingPrice\":false,\"maskedPrice\":false,\"mpFareHoldEligible\":false,\"packageRateAvailable\":false,\"calendarCriteria\":{\"available\":true,\"maxDate\":\"2024-05-27\",\"advanceDays\":90,\"mlos\":1},\"corpBudgetHotel\":false,\"lowestRateLastMinuteDeal\":false,\"anyRatelastMinuteDeal\":false,\"lowestRateEarlyBirdDeal\":false,\"anyRateEarlybirdDeal\":false,\"lowestRateRpBookingModel\":\"\",\"newlyBuilt\":false,\"newToMMT\":false,\"freeChildCount\":0,\"dndHotel\":false,\"lowestRateLuckyAvailable\":false,\"trainExclusiveRateAvailable\":false,\"busExclusiveRateAvailable\":false,\"exclusiveFlyerRateAvailable\":false,\"lowestRateAllInclusive\":false,\"groupBookingForSimilar\":false,\"myBizAssuredRecommended\":false,\"localIdAllowed\":false,\"pahmode\":\"PAS\",\"budgetHotel\":false,\"recommendedMultiRoom\":false,\"recommendCouponBit\":true,\"anyRateLuckyAvailable\":false,\"anyRateAllInclusive\":false,\"groupBookingHotel\":false,\"dealOfDayApplicable\":true,\"topHotel\":false,\"altAcco\":false,\"abso\":false,\"rtb\":false,\"isABSO\":false,\"isRTB\":false,\"isBudgetHotel\":false,\"isWishListed\":false}],\"lastBookedHotelList\":[],\"nearbyHotelList\":[],\"sectionWiseHotelList\":{},\"sectionWiseMetaData\":{},\"otherAltAccoHotelList\":[],\"nonAltAccoHotelList\":[],\"otherAltAccoHeading\":\"Showing other properties in Delhi\",\"cityName\":\"Delhi\",\"countryName\":\"India\",\"countryCode\":\"IN\",\"cityCode\":\"CTDEL\",\"failureReason\":{},\"sortCriteria\":{\"field\":\"S_hsq626_Q\",\"order\":\"asc\",\"dsAlgoVersion\":null,\"defaultRankSeq\":null},\"correlationKey\":\"86f335e7-fdb7-42b9-8602-58539a5f5a3a_hotels\",\"filtersRemoved\":false,\"suppressPas\":false,\"firstTimeUser\":false,\"blackEligible\":false,\"blackEligibilityDays\":0,\"lastFetchedHotelId\":\"201109301501008028\",\"lastFetchedWindowInfo\":\"0010000100#0#10#false\",\"rankingHotelCount\":\"500\",\"lastHotelIndex\":\"6\",\"currency\":\"inr\",\"currencyConvFactorINR\":1,\"appliedFiltersMap\":{},\"locusData\":{\"locusId\":\"CTDEL\",\"locusType\":\"city\",\"locusName\":\"Delhi\",\"name\":null},\"singularityResponseDrivingDistance\":false,\"filterBasedRates\":false,\"listingDeepLink\":\"mmyt://htl/listing/?checkin=04272024&checkout=04282024&city=CTDEL&country=IN&roomStayQualifier=2e0e&checkAvailability=false&locusId=CTDEL&locusType=city&region=in&funnelName=hotels\",\"commonHotelCategories\":[\"Couple Friendly\"],\"commonHotelCategoryDetails\":{},\"hotelCountInCity\":0,\"hotelCountInViewPort\":0,\"sharingUrl\":\"https://applinks.makemytrip.com/hotelListingShare?checkin=04272024&checkout=04282024&city=CTDEL&country=IN&roomStayQualifier=2e0e&checkAvailability=true&locusId=CTDEL&locusType=city&region=in&funnelName=hotels\",\"listingDeepLinkWithoutFilters\":\"https://www.makemytrip.com/hotels/hotel-listing/?checkin=04272024&checkout=04282024&city=CTDEL&country=IN&roomStayQualifier=2e0e&checkAvailability=true&_uCurrency=inr&locusId=CTDEL&locusType=city&region=in&funnelName=hotels\",\"hotelCardType\":\"default\",\"exclusiveOfferTs\":0,\"matchMakerResponse\":false}";
        Assert.assertNull( listingService.processCrossSellData(response,new SearchWrapperInputRequest()));
        Assert.assertNull( listingService.processCrossSellData(null,new SearchWrapperInputRequest()));
    }

    @Test
    public void processCrossSellPersuasionTest(){
        String response = "{\"staticHotelCounts\":0,\"totalHotelCounts\":1,\"noMoreAvailableHotels\":false,\"hotelList\":[{\"id\":\"200703161155527273\",\"name\":\"Radisson Blu Plaza Delhi Airport\",\"propertyType\":\"Hotel\",\"propertyLabel\":\"Hotel\",\"mainImages\":[\"https://r1imghtlak.mmtcdn.com/d076a970c29e11ebbbed0242ac110005.jpg?&downsize=583:388&output-format=jpg\"],\"mtkey\":\"5354803364029868196\",\"starRating\":5,\"currencyCode\":{\"id\":\"inr\",\"value\":\"inr\"},\"address\":{\"area\":[\"Mahipalpur\",\"Mahipalpur Village\",\"Block R\"],\"line2\":\"Mahipalpur\",\"line1\":\"National Highway 8,\\r\\n\"},\"displayFare\":{\"tax\":{\"value\":0},\"slashedPrice\":{\"value\":13140,\"maxFraction\":0,\"avgeragePriceNoTax\":13140,\"sellingPriceNoTax\":13140,\"sellingPriceWithTax\":13140},\"actualPrice\":{\"value\":14600,\"maxFraction\":0,\"avgeragePriceNoTax\":14600,\"sellingPriceNoTax\":14600,\"sellingPriceWithTax\":14600},\"extraAdult\":{\"value\":0},\"segmentId\":\"1120\",\"ddmu\":0,\"totalRoomCount\":1,\"couponReceived\":false,\"couponSkipped\":false,\"ddApplied\":false,\"displayPriceBreakDown\":{\"displayPrice\":13140,\"displayPriceAlternateCurrency\":0,\"nonDiscountedPrice\":14600,\"savingPerc\":10,\"totalSaving\":1460,\"roundedOffDelta\":0,\"basePrice\":14600,\"hotelTax\":3022,\"hotelServiceCharge\":0,\"mmtServiceCharge\":920,\"mmtDiscount\":1460,\"blackDiscount\":0,\"cdfDiscount\":0,\"charityAmountV2\":0,\"wallet\":0,\"effectiveDiscount\":0,\"tdsAmount\":0,\"extraPaxPrice\":0,\"pricingKey\":\"s9rHC9RN7n+MNBRmAgubuABaqPIHe3xJtEpJhWppgDQwWhu6ey+TlESjUJ3Zeu4hIJMiewtwCgqaiTlwir5WQqhz/dzHpft21ck7cdORvP29yT+ur1GP5XxWp8siw+m7K7v/5pQoNoh9BlBQttgUJbN5vJQc6oSyM+pg3c1Gp267yP27PGsXg4f50W+BZ7z2xW0mHtldBkJNSm/7H2AkZUMtaRArP5iFy+atBsdWah0LayAT7pYlwAJSHuRlV0uRsLlvP8F++3eNHdS97dS/Hy0iAsCCqwutN42Drrmo3u0=\",\"pricingDivisor\":1,\"totalTax\":3942,\"effectivePrice\":13140,\"brinInclusivePrice\":0,\"hotelierCouponDiscount\":0,\"gstDiscountHotelierCoupon\":0,\"affiliateFee\":0,\"pinCodeMandatory\":false,\"totalAmount\":13140,\"metaDiscount\":0,\"addonPrice\":0,\"nonDiscountedAddonPrice\":0,\"currencyConvFactorToINR\":1,\"cbrAvailable\":false,\"serviceCharge\":0,\"ddmarkupAlreadyApplied\":false,\"charityV2Enable\":false,\"taxIncluded\":false},\"conversionFactor\":0,\"slotRate\":false,\"hotelTax\":3022.2,\"hotelierServiceCharge\":0,\"isBNPLApplicable\":false,\"originalBNPL\":false,\"dateOfDelayedPayment\":0,\"apPromotionDiscount\":0,\"aajKaBhaoApplied\":\"NA\",\"taxExcluded\":false},\"categories\":[\"Chain\",\"MyBiz Assured\",\"Couple Friendly\",\"Premium Properties\",\"package_hotels\",\"premium_hotels\"],\"primaryAreas\":{\"locationId\":\"ARMAHI\",\"name\":\"Mahipalpur\",\"latLong\":[77.137055,28.547498]},\"cityName\":\"Delhi\",\"countryName\":\"India\",\"countryCode\":\"IN\",\"cityCode\":\"CTDEL\",\"stayType\":\"Hotel\",\"maxOccupancy\":0,\"ignoreEntireProperty\":false,\"appDeeplink\":\"mmyt://htl/detail/?topHtlId=200703161155527273&hotelId=200703161155527273&checkin=04272024&checkout=04282024&country=IN&city=CTDEL&roomStayQualifier=2e0e&_uCurrency=inr&checkAvailability=false&locusId=CTDEL&locusType=city&region=in&funnelName=hotels\",\"sharingUrl\":\"https://applinks.makemytrip.com/q8DzuKn2gU?hotelId=200703161155527273&city=CTDEL&country=IN&roomStayQualifier=2e0e&checkin=04272024&checkout=04282024&cmp=hotelAppShareNew&locusId=CTDEL&locusType=city&region=in&funnelName=hotels\",\"detailDeeplinkUrl\":\"https://www.makemytrip.com/hotels/hotel-details?hotelId=200703161155527273&checkin=04272024&checkout=04282024&country=IN&city=CTDEL&openDetail=true&currency=inr&roomStayQualifier=2e0e&locusId=CTDEL&locusType=city&region=in&viewType=PREMIUM&funnelName=hotels\",\"locationPersuasion\":[\"Mahipalpur\",\"5.2 km from Indira Gandhi International Airport\"],\"locationDist\":0,\"isAbsorptionApplied\":false,\"freeCancellationText\":\"Free Cancellation\",\"featured\":\"N\",\"isSoldOut\":false,\"isFreeWifiAvail\":true,\"isPAHAvailable\":false,\"isShortlisted\":false,\"hotelFacilities\":\"Basic Facilities,Transfers,Family and kids,Food and Drinks,Payment Services,Safety and Security,Health and wellness,Entertainment,General Services,Beauty and Spa,Outdoor Activities and Sports,Indoor Activities and Sports,Common Area,Shopping,Business Center and Conferences,Other Facilities\",\"geoLocation\":{\"latitude\":\"28.54382\",\"longitude\":\"77.1197\"},\"showBhfPersuasion\":false,\"hiddenGem\":false,\"isMTKeyEnabledSearch\":false,\"poiTag\":\"5.2 km from Indira Gandhi International Airport\",\"isValuePlus\":false,\"isFreeCancellation\":true,\"freeCancellationAvailable\":true,\"breakFast\":false,\"breakFastAvailable\":true,\"shortDescription\":\"A five-star property that has been serving smiles and hospitality par excellence for the past two decades, Radisson Blu Plaza De \",\"userEligiblePayMode\":\"PAS\",\"couponAutoApply\":true,\"isPAHTariffAvailable\":false,\"paymentMode\":\"PAS\",\"avgTP\":0,\"hasCollections\":false,\"dynamicFilters\":[\"USP\",\"Inhouse_dining\",\"Hotel Highlights\"],\"bedCount\":0,\"mealPlanIncluded\":{\"desc\":\"Room Only\",\"code\":\"EP\"},\"segments\":{\"segmentsCount\":0},\"hotelChainCode\":\"*********\",\"lowestRateSupplierCode\":\"INGO\",\"lowestRateSegmentId\":\"1120\",\"stateCode\":\"STDEL\",\"stateName\":\"Delhi\",\"twoMealAvailable\":false,\"allMealAvailable\":false,\"travellerImageCount\":0,\"lowestRoomAvailCount\":1,\"lowestAvailCount\":9,\"totalRoomAvailCount\":9,\"isBNPLAvailable\":false,\"bnplBaseAmount\":0,\"pahWalletApplicable\":false,\"bestPriceGuaranteed\":false,\"pahxAvailable\":false,\"pahxApplicable\":false,\"usp\":{\"USP\":{\"sub_title\":\"Signature vegetarian & non-vegetarian kababs at The Great Kabab Factory\",\"title\":\"USP\",\"subTags\":null},\"Inhouse_dining\":{\"sub_title\":\"Neung Roi: counted among India's best Thai restaurants\",\"title\":\"Inhouse Dining\",\"subTags\":null},\"Hotel Highlights\":{\"sub_title\":\"An expansive free-form outdoor pool & a 20,000-sq.ft. spa\",\"title\":\"Hotel Highlights\",\"subTags\":null}},\"addOnAvailableOnLowest\":false,\"addOnAvailable\":false,\"sponsored\":false,\"newType\":false,\"listingType\":\"room\",\"propertyRules\":[\"Hotel may charge compulsory gala dinner supplement on Christmas / New Year's eve or other festive periods. Any such supplements need to be paid directly at the hotel.\\r\\n\\r\\nKindly be informed that the hotel Swimming pool shall remain closed for scheduled annual maintenance till January 16th 2014. We would request your support during this period and regret inconvenience. Thank you for bearing with us.\"],\"trackingInfo\":null,\"blackAccelerated\":false,\"maxAdultAtSamePrice\":0,\"bedRoomCount\":0,\"maxChildAtSamePrice\":0,\"alternateDatesAvailable\":false,\"lowestBlackPackage\":false,\"blackPackageAvailable\":false,\"netRateAvailable\":false,\"lowestNetRate\":false,\"lowestRoomType\":\"Superior Room\",\"roomCount\":0,\"lastBooked\":false,\"lowestRatePlanMealDiff\":0,\"lowestRatePlanPahDiff\":0,\"notInterested\":false,\"lowestRoomCodesRatePlans\":\"24485--5354803364029868196\",\"priceDifferenceWithPivot\":0,\"lowestRateBnpl\":false,\"cancellationTimeline\":{\"checkInDate\":\"27 Apr\",\"checkInDateTime\":\"3 PM\",\"cancellationDate\":\"26 Apr\",\"cancellationDateTime\":\"05:59 PM\",\"cancellationDateInDateFormat\":\"26-Apr-2024 17:59\",\"subTitle\":\"Free Cancellation\",\"freeCancellationText\":\"Free Cancellation till \u202A26 Apr 05:59 PM\u202C\",\"title\":\"STAY FLEXIBLE WITH\",\"bookingDate\":\"27 Feb\",\"freeCancellationBenefits\":[{\"type\":\"\",\"text\":\"Free Cancellation till 26 Apr, 5:59 PM\"},{\"type\":\"\",\"text\":\"No refund if cancelled after 26 Apr, 6:00 PM\"}],\"fcTextForPersuasion\":\"Free Cancellation before \u202A26 Apr 05:59 PM\u202C\",\"cancellationPolicyTimelineList\":[{\"startDate\":\"27 Feb\",\"startDateTime\":\"02:45 PM\",\"endDate\":\"26 Apr\",\"endDateTime\":\"05:59 PM\",\"text\":\"100% Refund\",\"fcBenefit\":{\"type\":\"\",\"text\":\"Free Cancellation till 26 Apr, 5:59 PM\"},\"refundable\":true,\"refundText\":\"100%\",\"type\":\"FULL_REFUND\"},{\"startDate\":\"26 Apr\",\"startDateTime\":\"06:00 PM\",\"endDate\":\"27 Apr\",\"endDateTime\":\"05:59 PM\",\"text\":\"Non Refundable\",\"fcBenefit\":{\"type\":\"\",\"text\":\"No refund if cancelled after 26 Apr, 6:00 PM\"},\"refundable\":false,\"refundText\":\"0%\",\"type\":\"NO_REFUND\"}],\"tillDate\":\"26-Apr-2024 17:59\",\"bnplTitleText\":\"\"},\"staycationAvailable\":false,\"staycationRateAvlblForFilter\":false,\"lowestRateStaycation\":false,\"shortList\":false,\"mmtHotelCategory\":\"PREMIUM\",\"contextualPersuasions\":{},\"savingPercentage\":0,\"groupBookingPrice\":false,\"maskedPrice\":false,\"mpFareHoldEligible\":false,\"packageRateAvailable\":false,\"calendarCriteria\":{\"available\":true,\"maxDate\":\"2024-05-27\",\"advanceDays\":90,\"mlos\":1},\"corpBudgetHotel\":false,\"lowestRateLastMinuteDeal\":false,\"anyRatelastMinuteDeal\":false,\"lowestRateEarlyBirdDeal\":false,\"anyRateEarlybirdDeal\":false,\"lowestRateRpBookingModel\":\"\",\"newlyBuilt\":false,\"newToMMT\":false,\"freeChildCount\":0,\"dndHotel\":false,\"lowestRateLuckyAvailable\":false,\"trainExclusiveRateAvailable\":false,\"busExclusiveRateAvailable\":false,\"exclusiveFlyerRateAvailable\":false,\"lowestRateAllInclusive\":false,\"groupBookingForSimilar\":false,\"myBizAssuredRecommended\":false,\"localIdAllowed\":false,\"pahmode\":\"PAS\",\"budgetHotel\":false,\"recommendedMultiRoom\":false,\"recommendCouponBit\":true,\"anyRateLuckyAvailable\":false,\"anyRateAllInclusive\":false,\"groupBookingHotel\":false,\"dealOfDayApplicable\":true,\"topHotel\":false,\"altAcco\":false,\"abso\":false,\"rtb\":false,\"isABSO\":false,\"isRTB\":false,\"isBudgetHotel\":false,\"isWishListed\":false}],\"lastBookedHotelList\":[],\"nearbyHotelList\":[],\"sectionWiseHotelList\":{},\"sectionWiseMetaData\":{},\"otherAltAccoHotelList\":[],\"nonAltAccoHotelList\":[],\"otherAltAccoHeading\":\"Showing other properties in Delhi\",\"cityName\":\"Delhi\",\"countryName\":\"India\",\"countryCode\":\"IN\",\"cityCode\":\"CTDEL\",\"failureReason\":{},\"sortCriteria\":{\"field\":\"S_hsq626_Q\",\"order\":\"asc\",\"dsAlgoVersion\":null,\"defaultRankSeq\":null},\"correlationKey\":\"86f335e7-fdb7-42b9-8602-58539a5f5a3a_hotels\",\"filtersRemoved\":false,\"suppressPas\":false,\"firstTimeUser\":false,\"blackEligible\":false,\"blackEligibilityDays\":0,\"lastFetchedHotelId\":\"201109301501008028\",\"lastFetchedWindowInfo\":\"0010000100#0#10#false\",\"rankingHotelCount\":\"500\",\"lastHotelIndex\":\"6\",\"currency\":\"inr\",\"currencyConvFactorINR\":1,\"appliedFiltersMap\":{},\"locusData\":{\"locusId\":\"CTDEL\",\"locusType\":\"city\",\"locusName\":\"Delhi\",\"name\":null},\"singularityResponseDrivingDistance\":false,\"filterBasedRates\":false,\"listingDeepLink\":\"mmyt://htl/listing/?checkin=04272024&checkout=04282024&city=CTDEL&country=IN&roomStayQualifier=2e0e&checkAvailability=false&locusId=CTDEL&locusType=city&region=in&funnelName=hotels\",\"commonHotelCategories\":[\"Couple Friendly\"],\"commonHotelCategoryDetails\":{},\"hotelCountInCity\":0,\"hotelCountInViewPort\":0,\"sharingUrl\":\"https://applinks.makemytrip.com/hotelListingShare?checkin=04272024&checkout=04282024&city=CTDEL&country=IN&roomStayQualifier=2e0e&checkAvailability=true&locusId=CTDEL&locusType=city&region=in&funnelName=hotels\",\"listingDeepLinkWithoutFilters\":\"https://www.makemytrip.com/hotels/hotel-listing/?checkin=04272024&checkout=04282024&city=CTDEL&country=IN&roomStayQualifier=2e0e&checkAvailability=true&_uCurrency=inr&locusId=CTDEL&locusType=city&region=in&funnelName=hotels\",\"hotelCardType\":\"default\",\"exclusiveOfferTs\":0,\"matchMakerResponse\":false}";
        Assert.assertNull(listingService.processCrossSellPersuasion(response));
    }

    @Test
    public void buildGroupBookingResponseReturnsExpectedResponse() {
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.CALL_TO_BOOK_REQUEST_SUBMITTED)).thenReturn("Your Request has been submitted. You will receive a callback within 30 mins.");
        GroupBookingResponse response = listingService.buildGroupBookingResponse(1, "test");
        Assert.assertNotNull(response);
        Assert.assertEquals("Your Request has been submitted. You will receive a callback within 30 mins.", response.getDesc());
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.CALL_TO_BOOK_REQUEST_SUBMITTED_DESKTOP)).thenReturn("Request Submitted. Our agent will call you from our booking desk number +91xxxxxxxxxx on your registered mobile number.");
        response = listingService.buildGroupBookingResponse(1, "desktop");
        Assert.assertNotNull(response);
        Assert.assertEquals("Request Submitted. Our agent will call you from our booking desk number +91xxxxxxxxxx on your registered mobile number.", response.getDesc());
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.CALL_TO_BOOK_REQUEST_LIMIT_EXCEEDED)).thenReturn("You have already requested for 2 callbacks today. Please visit after 24 hours for another request");
        response = listingService.buildGroupBookingResponse(3, "test");
        Assert.assertNotNull(response);
        Assert.assertEquals("You have already requested for 2 callbacks today. Please visit after 24 hours for another request", response.getDesc());
    }

    @Test
    public void getMetaDataByCityResponseTest() throws ClientGatewayException {
        listingService.getMetaDataByCityResponse("cityId", null, null, null, "correlationKey", new HashMap<>());
    }

    @Test
    public void buildingPaxFiltersTest(){
        FetchCollectionResponseV2 fetchCollectionResponseV2 = new FetchCollectionResponseV2();
        FetchCollectionRequest fetchCollectionRequest = new FetchCollectionRequest();
        fetchCollectionRequest.setExpDataMap(new HashMap<>());
        fetchCollectionRequest.getExpDataMap().put("roomCountDefault", "flexible");

        fetchCollectionRequest.setSearchCriteria(new SearchHotelsCriteria());
        fetchCollectionRequest.getSearchCriteria().setRoomStayCandidates(new ArrayList<>());
        fetchCollectionRequest.getSearchCriteria().getRoomStayCandidates().add(new RoomStayCandidate());
        fetchCollectionRequest.getSearchCriteria().getRoomStayCandidates().get(0).setAdultCount(2);
        fetchCollectionRequest.getSearchCriteria().getRoomStayCandidates().get(0).setChildAges(new ArrayList<>());
        fetchCollectionRequest.getSearchCriteria().getRoomStayCandidates().get(0).getChildAges().add(5);

        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("flexible");

        listingService.buildingPaxFilters(fetchCollectionResponseV2, fetchCollectionRequest);

        fetchCollectionRequest.getExpDataMap().put("roomCountDefault", "exactRoom");
        listingService.buildingPaxFilters(fetchCollectionResponseV2, fetchCollectionRequest);
    }

    @Test
    public void testBuildCardPayload() {
        ListingService listingService = new ListingService();

        // Test case 1: cardPayloadResponse with empty genericCardData
        CardPayloadResponse cardPayloadResponse = new CardPayloadResponse();
        cardPayloadResponse.setGenericCardData(new ArrayList<>());
        CardPayloadResponse result = listingService.buildCardPayload(cardPayloadResponse);
        Assert.assertNotNull(result);
        Assert.assertTrue(CollectionUtils.isEmpty(result.getGenericCardData()));

        // Test case 2: cardPayloadResponse with non-empty genericCardData
        GenericCardPayloadData genericCardPayloadData = new GenericCardPayloadData();
        genericCardPayloadData.setTitleText("Title");
        genericCardPayloadData.setIconUrl("IconUrl");

        List<GenericCardPayloadData> genericCardPayloadDataList = new ArrayList<>();
        genericCardPayloadDataList.add(genericCardPayloadData);

        List<GenericCardPayloadData> genericCardDataList = new ArrayList<>();
        GenericCardPayloadData genericCardData = new GenericCardPayloadData();
        genericCardData.setData(genericCardPayloadDataList);
        genericCardDataList.add(genericCardData);

        cardPayloadResponse.setGenericCardData(genericCardDataList);

        result = listingService.buildCardPayload(cardPayloadResponse);
        Assert.assertNotNull(result);
        Assert.assertTrue(CollectionUtils.isNotEmpty(result.getGenericCardData()));
        Assert.assertEquals(1, result.getGenericCardData().size());
        Assert.assertEquals("Title", result.getGenericCardData().get(0).getTitleText());
        Assert.assertEquals("IconUrl", result.getGenericCardData().get(0).getIconUrl());
    }


    @Test
    public void testGetFilterCategoryFromHistoGramBucketsWithNonExpPdoPrnt() {
        FetchCollectionRequest fetchCollectionRequest = new FetchCollectionRequest();
        fetchCollectionRequest.setSearchCriteria(new SearchHotelsCriteria());
        String expData = "";
        fetchCollectionRequest.setExpData(expData);

        List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
        filters.add(new com.mmt.hotels.clientgateway.response.filter.Filter());

        Mockito.when(filterResponseTransformer.createDefaultPriceHistogramBuckets(fetchCollectionRequest.getSearchCriteria())).thenReturn(filters);
        Mockito.when(utility.isExpPdoPrnt(fetchCollectionRequest.getExpData())).thenReturn(false);
        Mockito.when(polyglotService.getTranslatedData(Constants.PRICE_PER_NIGHT)).thenReturn("Price per night");

        FilterCategory result = listingService.getFilterCategoryFromHistoGramBuckets(fetchCollectionRequest);

        Assert.assertNotNull(result);
        Assert.assertEquals("Price per night", result.getTitle());
        Assert.assertEquals(Constants.VIEW_TYPE_GRAPH, result.getViewType());
        Assert.assertTrue(result.isVisible());
        Assert.assertEquals(Constants.PRICE_CATEGORY_NAME, result.getCategoryName());
        Assert.assertEquals(filters, result.getFilters());
    }

    @Test
    public void testGetFilterCategoryFromHistoGramBucketsWithFilters() {
        FetchCollectionRequest fetchCollectionRequest = new FetchCollectionRequest();
        fetchCollectionRequest.setSearchCriteria(new SearchHotelsCriteria());
        fetchCollectionRequest.setExpData("");

        List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
        filters.add(new com.mmt.hotels.clientgateway.response.filter.Filter());

        Mockito.when(filterResponseTransformer.createDefaultPriceHistogramBuckets(fetchCollectionRequest.getSearchCriteria())).thenReturn(filters);
        Mockito.when(utility.isExpPdoPrnt(fetchCollectionRequest.getExpData())).thenReturn(true);
        Mockito.when(polyglotService.getTranslatedData(Constants.PRICE_PER_ROOM_PER_NIGHT)).thenReturn("Price per room per night");

        FilterCategory result = listingService.getFilterCategoryFromHistoGramBuckets(fetchCollectionRequest);

        Assert.assertNotNull(result);
        Assert.assertEquals("Price per room per night", result.getTitle());
        Assert.assertEquals(Constants.VIEW_TYPE_GRAPH, result.getViewType());
        Assert.assertTrue(result.isVisible());
        Assert.assertEquals(Constants.PRICE_CATEGORY_NAME, result.getCategoryName());
        Assert.assertEquals(filters, result.getFilters());
    }

    @Test
    public void testGetFilterCategoryFromHistoGramBucketsWithoutFilters() {
        FetchCollectionRequest fetchCollectionRequest = new FetchCollectionRequest();
        fetchCollectionRequest.setSearchCriteria(new SearchHotelsCriteria());

        Mockito.when(filterResponseTransformer.createDefaultPriceHistogramBuckets(fetchCollectionRequest.getSearchCriteria())).thenReturn(new ArrayList<>());

        FilterCategory result = listingService.getFilterCategoryFromHistoGramBuckets(fetchCollectionRequest);

        Assert.assertNull(result);
    }
    @Test
    public void testGetCardInfo_AllFieldsPopulated() {
        CardCollections cardCollections = new CardCollections();
        CardData cardData = new CardData();
        cardData.setSubText("SubText");
        cardData.setTitleText("TitleText");
        cardData.setIndex(1);
        cardData.setStarText("StarText");
        cardData.setCardId("CardId");
        cardData.setIconUrl("IconUrl");
        cardData.setBgImageUrl("BgImageUrl");
        cardData.setTemplateId("TemplateId");

        List<CardAction> cardActions = new ArrayList<>();
        CardAction cardAction = new CardAction();
        cardAction.setWebViewUrl("WebViewUrl");
        cardAction.setTitle("Title");
        cardActions.add(cardAction);
        cardData.setCardAction(cardActions);

        cardCollections.setCardInfo(cardData);

        CardData result = ReflectionTestUtils.invokeMethod(listingService, "getCardInfo", cardCollections);

        Assert.assertNotNull(result);
        Assert.assertEquals("SubText", result.getSubText());
        Assert.assertEquals("TitleText", result.getTitleText());
        Assert.assertEquals("StarText", result.getStarText());
        Assert.assertEquals("CardId", result.getCardId());
        Assert.assertEquals("IconUrl", result.getIconUrl());
        Assert.assertEquals("BgImageUrl", result.getBgImageUrl());
        Assert.assertEquals("TemplateId", result.getTemplateId());
        Assert.assertTrue(CollectionUtils.isNotEmpty(result.getCardAction()));
        Assert.assertEquals("WebViewUrl", result.getCardAction().get(0).getWebViewUrl());
        Assert.assertEquals("Title", result.getCardAction().get(0).getTitle());
    }

    @Test
    public void testGetCardInfo_SomeFieldsPopulated() {
        CardCollections cardCollections = new CardCollections();
        CardData cardData = new CardData();
        cardData.setTitleText("TitleText");
        cardData.setIndex(1);

        cardCollections.setCardInfo(cardData);

        CardData result = ReflectionTestUtils.invokeMethod(listingService, "getCardInfo", cardCollections);

        Assert.assertNotNull(result);
        Assert.assertNull(result.getSubText());
        Assert.assertEquals("TitleText", result.getTitleText());
        Assert.assertNull(result.getStarText());
        Assert.assertNull(result.getCardId());
        Assert.assertNull(result.getIconUrl());
        Assert.assertNull(result.getBgImageUrl());
        Assert.assertNull(result.getTemplateId());
        Assert.assertTrue(CollectionUtils.isEmpty(result.getCardAction()));
    }

    @Test
    public void testGetCardInfo_NoFieldsPopulated() {
        CardCollections cardCollections = new CardCollections();
        CardData cardData = new CardData();

        cardCollections.setCardInfo(cardData);

        CardData result = ReflectionTestUtils.invokeMethod(listingService, "getCardInfo", cardCollections);

        Assert.assertNotNull(result);
        Assert.assertNull(result.getSubText());
        Assert.assertNull(result.getTitleText());
        Assert.assertNull(result.getStarText());
        Assert.assertNull(result.getCardId());
        Assert.assertNull(result.getIconUrl());
        Assert.assertNull(result.getBgImageUrl());
        Assert.assertNull(result.getTemplateId());
        Assert.assertTrue(CollectionUtils.isEmpty(result.getCardAction()));
    }

    @Test
    public void testExecuteListing_InvalidFunnel() throws Exception {
        // Arrange
        HttpServletRequest mockRequest = Mockito.mock(HttpServletRequest.class);
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        searchHotelsRequest.setRequestDetails(new RequestDetails());
        searchHotelsRequest.getRequestDetails().setIdContext(Constants.CORP_ID_CONTEXT); // triggers invalid funnel
        searchHotelsRequest.setSearchCriteria(new SearchHotelsCriteria());
        Tuple<String, Map<String, String>> tup = new Tuple<>("key", new HashMap<>());
        SearchHotelsResponse expectedResponse = new SearchHotelsResponse();

        Mockito.when(utility.addSrcReqToParameterMap(Mockito.any())).thenReturn(new HashMap<>());
        Mockito.doReturn(expectedResponse).when(listingService).searchHotels(Mockito.any(), Mockito.any(), Mockito.any());

        // Act
        SearchHotelsResponse actualResponse = listingService.executeListing(mockRequest, "client", searchHotelsRequest, "correlationKey", tup);

        // Assert
        Assert.assertEquals(expectedResponse, actualResponse);
    }

    @Test
    public void testExecuteListing_CardEngineBranch() throws Exception {
        // Arrange
        HttpServletRequest mockRequest = Mockito.mock(HttpServletRequest.class);
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        RequestDetails details = new RequestDetails();
        details.setIdContext("B2C");
        details.setFunnelSource("NORMAL");
        details.setSubPageContext("context");
        searchHotelsRequest.setRequestDetails(details);
        searchHotelsRequest.setSearchCriteria(new SearchHotelsCriteria());
        Tuple<String, Map<String, String>> tup = new Tuple<>("key", new HashMap<>());
        SearchHotelsResponse expectedResponse = new SearchHotelsResponse();

        Mockito.lenient().when(utility.addSrcReqToParameterMap(Mockito.any())).thenReturn(new HashMap<>());
        Mockito.doReturn(expectedResponse).when(listingService).executeSearchHotelsWithCardEngineService(Mockito.any(), Mockito.anyString(), Mockito.any(), Mockito.anyString(), Mockito.any());

        // Act
        SearchHotelsResponse actualResponse = listingService.executeListing(mockRequest, "client", searchHotelsRequest, "correlationKey", tup);

        // Assert
        Assert.assertEquals(expectedResponse, actualResponse);
    }

    @Test
    public void testProcessCrossSellData_EmptyResponse() throws Exception {
        // Arrange
        String emptyResponse = "";
        SearchWrapperInputRequest mockInputRequest = new SearchWrapperInputRequest();
        // No need to create mockResponseBO since it's not used
        Mockito.when(objectMapperUtil.getJsonFromObject(Mockito.any(), Mockito.any())).thenReturn("serialized");
        Mockito.when(crossSellUtil.getCrossSellDataScion(Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(new CrossSellDataHES());

        // Act
        String result = listingService.processCrossSellData(emptyResponse, mockInputRequest);

        // Assert
        Assert.assertEquals("serialized", result);
    }

    @Test
    public void testProcessCrossSellPersuasion_EmptyResponse() throws Exception {
        // Arrange
        String emptyResponse = "";
        Mockito.when(objectMapperUtil.getJsonFromObject(Mockito.isNull(), Mockito.any())).thenReturn("nullSerialized");

        // Act
        String result = listingService.processCrossSellPersuasion(emptyResponse);

        // Assert
        Assert.assertEquals("nullSerialized", result);
    }

    @Test
    public void testSmartFilters() throws ClientGatewayException {
        // Arrange
        FilterCountRequest filterRequest = new FilterCountRequest();
        filterRequest.setClient("PWA");
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> httpHeaderMap = new HashMap<>();
        Boolean seoCorp = false;
        List<DPTExperimentDetails> dptExperimentDetailsList = new ArrayList<>();

        FilterResponse filterResponse = new FilterResponse();
        List<FilterCategory> filterList = new ArrayList<>();
        FilterCategory filterCategory = new FilterCategory();
        List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
        com.mmt.hotels.clientgateway.response.filter.Filter filter = new com.mmt.hotels.clientgateway.response.filter.Filter();
        filter.setTitle("Budget Hotels");
        filter.setFilterValue("BUDGET");
        filters.add(filter);
        filterCategory.setFilters(filters);
        filterList.add(filterCategory);
        filterResponse.setFilterList(filterList);

        List<String> filterTitles = Arrays.asList("Budget Hotels", "Luxury Hotels");
        List<String> smartFilterTags = Arrays.asList("BUDGET", "LUXURY");

        // Mock behavior
        Mockito.doReturn(filterResponse).when(listingService).filterCount(filterRequest, parameterMap, httpHeaderMap, seoCorp, dptExperimentDetailsList);
        Mockito.when(utility.getAllFiltersFromList(filterList)).thenReturn(filters);
        Mockito.when(utility.getTitlesFromFilters(filters)).thenReturn(filterTitles);
        Mockito.doReturn(smartFilterTags).when(listingService).getSmartFilterTagsForQuery(filterRequest, filterTitles);
        Mockito.when(utility.getMatchingFilters(filters, smartFilterTags)).thenReturn(filters);

        // Act
        List<com.mmt.hotels.clientgateway.response.filter.Filter> result = 
            listingService.smartFilters(filterRequest, parameterMap, httpHeaderMap, seoCorp, dptExperimentDetailsList);

        // Assert
        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
        Assert.assertEquals("Budget Hotels", result.get(0).getTitle());
    }

    @Test
    public void testGetSmartFilterTagsForQuery() throws ClientGatewayException {
        // Arrange
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
        searchCriteria.setSearchQueryText("budget hotels near airport");
        filterCountRequest.setSearchCriteria(searchCriteria);
        
        List<String> filterTitles = Arrays.asList("Budget Hotels", "Airport Hotels", "Luxury Hotels");
        List<String> expectedTags = Arrays.asList("BUDGET", "AIRPORT");
        String mockResponse = "[\"BUDGET\", \"AIRPORT\"]";

        // Mock behavior
        Mockito.when(filterExecutor.getSmartFilterTags(Mockito.eq(filterCountRequest), Mockito.eq(filterTitles), Mockito.any(), Mockito.eq(String.class)))
               .thenReturn(mockResponse);
        Mockito.when(objectMapperUtil.getObjectFromJsonWithType(Mockito.eq(mockResponse), Mockito.<TypeReference<List<String>>>any(), Mockito.any()))
               .thenReturn(expectedTags);

        // Act
        List<String> result = listingService.getSmartFilterTagsForQuery(filterCountRequest, filterTitles);

        // Assert
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());
        Assert.assertTrue(result.contains("BUDGET"));
        Assert.assertTrue(result.contains("AIRPORT"));
    }

    @Test
    public void testGetSmartFilterTagsForQuery_EmptyResponse() throws ClientGatewayException {
        // Arrange
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
        searchCriteria.setSearchQueryText("test query");
        filterCountRequest.setSearchCriteria(searchCriteria);
        
        List<String> filterTitles = Arrays.asList("Budget Hotels");

        // Mock behavior - empty response
        Mockito.when(filterExecutor.getSmartFilterTags(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.eq(String.class)))
               .thenReturn("");

        // Act
        List<String> result = listingService.getSmartFilterTagsForQuery(filterCountRequest, filterTitles);

        // Assert
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
    }

    @Test(expected = ClientGatewayException.class)
    public void testGetSmartFilterTagsForQuery_Exception() throws ClientGatewayException {
        // Arrange
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
        searchCriteria.setSearchQueryText("test query");
        filterCountRequest.setSearchCriteria(searchCriteria);
        
        List<String> filterTitles = Arrays.asList("Budget Hotels");

        // Mock behavior - exception
        Mockito.when(filterExecutor.getSmartFilterTags(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.eq(String.class)))
               .thenThrow(new RuntimeException("Service unavailable"));

        // Act & Assert
        listingService.getSmartFilterTagsForQuery(filterCountRequest, filterTitles);
    }

    // Note: This test is commented out because DeepLinkGenerationRequest and 
    // DeepLinkGenerationResponse classes are not available in the current codebase
    // @Test
    // public void testGenerateDeepLink() throws ClientGatewayException {
    //     DeepLinkGenerationRequest request = new DeepLinkGenerationRequest();
    //     DeepLinkGenerationResponse expectedResponse = new DeepLinkGenerationResponse();
    //     
    //     Mockito.when(deepLinkGenerationService.generateDeepLink(request)).thenReturn(expectedResponse);
    //     
    //     DeepLinkGenerationResponse result = listingService.generateDeepLink(request);
    //     
    //     Assert.assertNotNull(result);
    //     Assert.assertEquals(expectedResponse, result);
    //     Mockito.verify(deepLinkGenerationService).generateDeepLink(request);
    // }

    @Test
    public void testSearchHotelsWithCommonResponse() throws ClientGatewayException {
        // Arrange
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        searchHotelsRequest.setClient("PWA");
        searchHotelsRequest.setSearchCriteria(new SearchHotelsCriteria());
        searchHotelsRequest.getSearchCriteria().setPersonalizedSearch(false);
        
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> httpHeaderMap = new HashMap<>();
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        
        SearchHotelsRequestTransformer requestTransformer = Mockito.mock(SearchHotelsRequestTransformer.class);
        SearchHotelsResponseTransformer responseTransformer = Mockito.mock(SearchHotelsResponseTransformer.class);
        @SuppressWarnings("unchecked") 
        SearchWrapperResponseBO<SearchWrapperHotelEntity> searchWrapperResponseBO = new SearchWrapperResponseBO.Builder<SearchWrapperHotelEntity>().build();
        ListingPagePersonalizationResponsBO listingPagePersonalizationResponseBO = new ListingPagePersonalizationResponsBO();
        SearchHotelsResponse expectedResponse = new SearchHotelsResponse();

        // Mock behavior
        Mockito.when(crossSellUtil.isCrossSellRequest(searchHotelsRequest)).thenReturn(false);
        Mockito.when(searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())).thenReturn(requestTransformer);
        Mockito.when(requestTransformer.convertSearchRequest(searchHotelsRequest, commonModifierResponse)).thenReturn(new SearchWrapperInputRequest());
        Mockito.when(searchHotelsExecutor.searchHotels(Mockito.any(), Mockito.eq(parameterMap), Mockito.eq(httpHeaderMap))).thenReturn(searchWrapperResponseBO);
        Mockito.when(listingHelper.convertSearchHotelsToPersonalizedHotels(searchWrapperResponseBO, searchHotelsRequest)).thenReturn(listingPagePersonalizationResponseBO);
        Mockito.when(searchHotelsFactory.getResponseService(searchHotelsRequest.getClient())).thenReturn(responseTransformer);
        Mockito.when(responseTransformer.convertSearchHotelsResponse(listingPagePersonalizationResponseBO, searchHotelsRequest, commonModifierResponse)).thenReturn(expectedResponse);

        // Act
        SearchHotelsResponse result = listingService.searchHotelsWithCommonResponse(searchHotelsRequest, parameterMap, httpHeaderMap, commonModifierResponse);

        // Assert
        Assert.assertNotNull(result);
        Assert.assertEquals(expectedResponse, result);
    }

    @Test
    public void testSearchHotelsWithCommonResponse_PersonalizedSearch() throws ClientGatewayException {
        // Arrange
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        searchHotelsRequest.setClient("PWA");
        searchHotelsRequest.setSearchCriteria(new SearchHotelsCriteria());
        searchHotelsRequest.getSearchCriteria().setPersonalizedSearch(true);
        
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> httpHeaderMap = new HashMap<>();
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        
        SearchHotelsRequestTransformer requestTransformer = Mockito.mock(SearchHotelsRequestTransformer.class);
        SearchHotelsResponseTransformer responseTransformer = Mockito.mock(SearchHotelsResponseTransformer.class);
        ListingPagePersonalizationResponsBO listingPagePersonalizationResponseBO = new ListingPagePersonalizationResponsBO();
        SearchHotelsResponse expectedResponse = new SearchHotelsResponse();

        // Mock behavior
        Mockito.when(crossSellUtil.isCrossSellRequest(searchHotelsRequest)).thenReturn(false);
        Mockito.when(searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())).thenReturn(requestTransformer);
        Mockito.when(requestTransformer.convertSearchRequest(searchHotelsRequest, commonModifierResponse)).thenReturn(new SearchWrapperInputRequest());
        Mockito.when(searchHotelsExecutor.searchPersonalizedHotels(Mockito.any(), Mockito.eq(parameterMap), Mockito.eq(httpHeaderMap), Mockito.eq(false))).thenReturn(listingPagePersonalizationResponseBO);
        Mockito.when(searchHotelsFactory.getResponseService(searchHotelsRequest.getClient())).thenReturn(responseTransformer);
        Mockito.when(responseTransformer.convertSearchHotelsResponse(listingPagePersonalizationResponseBO, searchHotelsRequest, commonModifierResponse)).thenReturn(expectedResponse);

        // Act
        SearchHotelsResponse result = listingService.searchHotelsWithCommonResponse(searchHotelsRequest, parameterMap, httpHeaderMap, commonModifierResponse);

        // Assert
        Assert.assertNotNull(result);
        Assert.assertEquals(expectedResponse, result);
    }

    @Test
    public void testFilterCountLogging_NullResponse() {
        // Arrange
        FilterCountRequest filterRequest = new FilterCountRequest();
        FilterResponse response = null;
        Map<String, String> httpHeaderMap = new HashMap<>();
        boolean seoCorp = false;
        List<DPTExperimentDetails> dptExperimentDetailsList = new ArrayList<>();

        try {
            // Act
            listingService.filterCountLogging(filterRequest, response, httpHeaderMap, seoCorp, dptExperimentDetailsList);

            // Assert - should not throw exception and should log error message
            // Verify that filterExecutor.filterCountLogging is never called
            Mockito.verify(filterExecutor, Mockito.never()).filterCountLogging(Mockito.any(), Mockito.any());
        } catch (Exception e) {
            // Should not reach here since the method handles null gracefully
            Assert.fail("Method should handle null response gracefully");
        }
    }

    @Test
    public void testInit_WithConsulFlag() {
        // Arrange
        ReflectionTestUtils.setField(listingService, "consulFlag", true);
        Map<String, FilterDetail> mockLandingFilterConditions = new HashMap<>();
        mockLandingFilterConditions.put("test", new FilterDetail());
        
        Mockito.when(commonConfigConsul.getLandingFilterConditions()).thenReturn(mockLandingFilterConditions);

        // Act
        listingService.init();

        // Assert
        Mockito.verify(commonConfigConsul).getLandingFilterConditions();
    }

    @Test
    public void testInit_WithoutConsulFlag() {
        // Arrange
        ReflectionTestUtils.setField(listingService, "consulFlag", false);
        CommonConfig mockCommonConfig = Mockito.mock(CommonConfig.class);
        Map<String, FilterDetail> mockLandingFilterConditions = new HashMap<>();
        
        Mockito.when(propManager.getProperty("commonConfig", CommonConfig.class)).thenReturn(mockCommonConfig);
        Mockito.when(mockCommonConfig.landingFilterConditions()).thenReturn(mockLandingFilterConditions);

        // Act
        listingService.init();

        // Assert
        Mockito.verify(propManager).getProperty("commonConfig", CommonConfig.class);
        Mockito.verify(mockCommonConfig).landingFilterConditions();
    }

    @Test
    public void testGetMetaDataByCityResponse_Success() throws ClientGatewayException {
        // Arrange
        String cityId = "CTDEL";
        String locationId = "LCDEL";
        String locationType = "city";
        String filterCode = "STAR_RATING";
        String correlationKey = "test-correlation-key";
        
        Map<String, String> requestParams = new HashMap<>();
        requestParams.put(Constants.SOURCE_CON, "IN");
        requestParams.put(Constants.DES_CON, "IN");
        requestParams.put(Constants.SOURCE_CLIENT, "PWA");
        
        String mockResponse = "{\"locations\": [], \"facilities\": []}";
        String expectedFilteredResponse = "{\"filteredLocations\": [], \"filteredFacilities\": []}";

        // Mock behavior
        Mockito.when(commonHelper.getInboundCurrencyCode("IN", "IN", "PWA")).thenReturn("INR");
        Mockito.when(searchHotelsExecutor.getMetaDataResponse(Mockito.eq(cityId), Mockito.any())).thenReturn(mockResponse);
        Mockito.when(hotelMetaDataService.filterLocationAndFaclity(mockResponse, filterCode)).thenReturn(expectedFilteredResponse);

        // Act
        String result = listingService.getMetaDataByCityResponse(cityId, locationId, locationType, filterCode, correlationKey, requestParams);

        // Assert
        Assert.assertNotNull(result);
        Assert.assertEquals(expectedFilteredResponse, result);
        Mockito.verify(commonHelper).getInboundCurrencyCode("IN", "IN", "PWA");
        Mockito.verify(searchHotelsExecutor).getMetaDataResponse(Mockito.eq(cityId), Mockito.any());
        Mockito.verify(hotelMetaDataService).filterLocationAndFaclity(mockResponse, filterCode);
    }
}