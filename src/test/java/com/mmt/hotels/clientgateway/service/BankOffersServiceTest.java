package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.BankOffersRequestCG;
import com.mmt.hotels.clientgateway.response.BankOffersResponseCG;
import com.mmt.hotels.clientgateway.restexecutors.BankOffersExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.BankOffersFactory;
import com.mmt.hotels.clientgateway.transformer.request.desktop.BankOffersRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.desktop.BankOffersResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.pojo.request.landing.BankOffersRequest;
import com.mmt.hotels.pojo.response.bankoffers.BankOffersResponse;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class BankOffersServiceTest {

    @InjectMocks
    BankOffersService bankOffersService;

    @Mock
    BankOffersExecutor bankOffersExecutor;

    @Mock
    CommonHelper commonHelper;

    @Mock
    BankOffersFactory bankOffersFactory;

    @Mock
    private MetricAspect metricAspect;


    @Before
    public void init() {
        Mockito.doNothing().when(metricAspect).addToTime(Mockito.any(), Mockito.any(), Mockito.anyLong());
    }


    @Test
    public void bankOffers_shouldReturnResponse_whenValidRequest() throws ClientGatewayException {
        Mockito.lenient().when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new CommonModifierResponse());

        BankOffersRequestTransformerDesktop bankOffersRequestTransformerDesktop = Mockito.mock(BankOffersRequestTransformerDesktop.class);
        Mockito.lenient().when(bankOffersFactory.getRequestService(Mockito.any())).thenReturn(bankOffersRequestTransformerDesktop);

        BankOffersRequest bankOffersRequest = new BankOffersRequest();
        bankOffersRequest.setDeltaDays(1);
        Mockito.lenient().when(bankOffersRequestTransformerDesktop.convertBankOffersRequest(Mockito.any(), Mockito.any())).thenReturn(bankOffersRequest);

        BankOffersResponseTransformerDesktop bankOffersResponseTransformerDesktop = Mockito.mock(BankOffersResponseTransformerDesktop.class);
        Mockito.lenient().when(bankOffersFactory.getResponseService(Mockito.any())).thenReturn(bankOffersResponseTransformerDesktop);
        Mockito.lenient().when(bankOffersResponseTransformerDesktop.convertBankOffersResponse(Mockito.any())).thenReturn(new BankOffersResponseCG());

        Mockito.lenient().when(bankOffersExecutor.bankOffers(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new BankOffersResponse());

        BankOffersRequestCG bankOffersRequestCG = new BankOffersRequestCG();
        bankOffersRequestCG.setDeltaDays(1);
        BankOffersResponseCG response = bankOffersService.bankOffers(bankOffersRequestCG, new HashMap<>(), new HashMap<>());
        Assert.assertNotNull(response);
    }

    @Test(expected = NullPointerException.class)
    public void bankOffers_shouldReturnNull_whenRequestIsNull() throws ClientGatewayException {
        Mockito.lenient().when(commonHelper.processRequest(Mockito.any(), Mockito.any(),Mockito.anyMap())).thenReturn(new CommonModifierResponse());
        BankOffersRequestTransformerDesktop bankOffersRequestTransformerDesktop = Mockito.mock(BankOffersRequestTransformerDesktop.class);
        Mockito.lenient().when(bankOffersFactory.getRequestService(Mockito.any())).thenReturn(bankOffersRequestTransformerDesktop);
        BankOffersResponseTransformerDesktop bankOffersResponseTransformerDesktop = Mockito.mock(BankOffersResponseTransformerDesktop.class);
        Mockito.lenient().when(bankOffersFactory.getResponseService(Mockito.any())).thenReturn(bankOffersResponseTransformerDesktop);
        bankOffersService.bankOffers(null, new HashMap<>(), new HashMap<>());
    }

    @Test
    public void bankOffers_shouldReturnNull_whenParameterMapIsNull() throws ClientGatewayException {

        Mockito.lenient().when(commonHelper.processRequest(Mockito.any(), Mockito.any(),Mockito.anyMap())).thenReturn(new CommonModifierResponse());

        BankOffersRequestTransformerDesktop bankOffersRequestTransformerDesktop = Mockito.mock(BankOffersRequestTransformerDesktop.class);
        Mockito.lenient().when(bankOffersFactory.getRequestService(Mockito.any())).thenReturn(bankOffersRequestTransformerDesktop);

        BankOffersResponseTransformerDesktop bankOffersResponseTransformerDesktop = Mockito.mock(BankOffersResponseTransformerDesktop.class);
        Mockito.lenient().when(bankOffersFactory.getResponseService(Mockito.any())).thenReturn(bankOffersResponseTransformerDesktop);

        BankOffersRequestCG bankOffersRequestCG = new BankOffersRequestCG();
        bankOffersRequestCG.setDeltaDays(1);
        BankOffersResponseCG response = bankOffersService.bankOffers(bankOffersRequestCG, null, new HashMap<>());
        Assert.assertNull(response);
    }

    @Test
    public void bankOffers_shouldReturnNull_whenHttpHeaderMapIsNull() throws ClientGatewayException {
        Mockito.lenient().when(commonHelper.processRequest(Mockito.any(), Mockito.any(),Mockito.anyMap())).thenReturn(new CommonModifierResponse());

        BankOffersRequestTransformerDesktop bankOffersRequestTransformerDesktop = Mockito.mock(BankOffersRequestTransformerDesktop.class);
        Mockito.lenient().when(bankOffersFactory.getRequestService(Mockito.any())).thenReturn(bankOffersRequestTransformerDesktop);

        BankOffersResponseTransformerDesktop bankOffersResponseTransformerDesktop = Mockito.mock(BankOffersResponseTransformerDesktop.class);
        Mockito.lenient().when(bankOffersFactory.getResponseService(Mockito.any())).thenReturn(bankOffersResponseTransformerDesktop);

        BankOffersRequestCG bankOffersRequestCG = new BankOffersRequestCG();
        bankOffersRequestCG.setDeltaDays(1);
        BankOffersResponseCG response = bankOffersService.bankOffers(bankOffersRequestCG, new HashMap<>(), null);
        Assert.assertNull(response);
    }

}
