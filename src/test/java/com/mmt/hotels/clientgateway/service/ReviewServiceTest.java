package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.AvailRoomsRequest;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.TotalPricingRequest;
import com.mmt.hotels.clientgateway.request.payLater.PayLaterEligibilityRequest;
import com.mmt.hotels.clientgateway.response.AvailRoomsResponse;
import com.mmt.hotels.clientgateway.response.TotalPriceResponse;
import com.mmt.hotels.clientgateway.restexecutors.AvailRoomsExecutor;
import com.mmt.hotels.clientgateway.restexecutors.UserServiceExecutor;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.UserLoginInfo;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResult;
import com.mmt.hotels.clientgateway.transformer.factory.AvailRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.request.AvailRoomsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.OldToNewerRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.AvailRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.response.PayLaterEligibilityResponse;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.pricing.TotalPricing;
import com.mmt.hotels.model.response.pricing.TotalPricingResponse;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ReviewServiceTest {

    @InjectMocks
    ReviewService reviewService;

    @Mock
    CommonHelper commonHelper;

    @Mock
    OldToNewerRequestTransformer oldToNewerRequestTransformer;

    @Mock
    AvailRoomsFactory availRoomsFactory;

    @Mock
    AvailRoomsExecutor availRoomsExecutor;
    
    @Mock
    ObjectMapperUtil objectMapperUtil;

    @Mock
    UserServiceExecutor userServiceExecutor;

    @Mock
    MetricAspect metricAspect;

    @Mock
    private MetricErrorLogger metricErrorLogger;

    @Mock
    private Utility utility;

    @Before
    public void init() {
        Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(Mockito.any(),Mockito.any(),Mockito.anyLong());
    }

    @Test(expected = ErrorResponseFromDownstreamException.class)
    public void testAvailRooms_ErrorResponseFromDownStreamException() throws ClientGatewayException {
        AvailRoomsRequestTransformer availRoomsRequestTransformer = Mockito.mock(AvailRoomsRequestTransformer.class);
        AvailRoomsResponseTransformer availRoomsResponseTransformer = Mockito.mock(AvailRoomsResponseTransformer.class);
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setDeviceDetails(new DeviceDetails());

        when(availRoomsExecutor.availRooms(Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(new ErrorResponseFromDownstreamException(DependencyLayer.CLIENTGATEWAY, ErrorType.DOWNSTREAM));
        when(availRoomsFactory.getRequestService(Mockito.any())).thenReturn(availRoomsRequestTransformer);

        reviewService.availRooms(new AvailRoomsRequest(),new HashMap<>(),new HashMap<>());

    }

    @Test
    public void testGetAvailPriceOld() throws ClientGatewayException {
        AvailRoomsRequestTransformer availRoomsRequestTransformer = Mockito.mock(AvailRoomsRequestTransformer.class);
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setDeviceDetails(new DeviceDetails());
        when(oldToNewerRequestTransformer.updateAvailRoomsRequest(Mockito.any())).thenReturn(availRoomsRequest);
        when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        when(availRoomsFactory.getRequestService(Mockito.any())).thenReturn(availRoomsRequestTransformer);
        when(availRoomsRequestTransformer.convertAvailRoomsRequest(Mockito.any(), Mockito.any())).thenReturn(new PriceByHotelsRequestBody());
        when(availRoomsExecutor.availRoomsOld(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("test");
        Assert.assertNotNull(reviewService.getAvailPriceOld(new PriceByHotelsRequestBody(),new HashMap<>(),new HashMap<>()));
    }
    
    @Test
    public void testGetUpdatedPriceOccuLessOld() throws ClientGatewayException {
        AvailRoomsRequestTransformer availRoomsRequestTransformer = Mockito.mock(AvailRoomsRequestTransformer.class);
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setDeviceDetails(new DeviceDetails());
        when(oldToNewerRequestTransformer.updateAvailRoomsRequest(Mockito.any())).thenReturn(availRoomsRequest);
        when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        when(availRoomsFactory.getRequestService(Mockito.any())).thenReturn(availRoomsRequestTransformer);
        when(availRoomsRequestTransformer.convertAvailRoomsRequest(Mockito.any(), Mockito.any())).thenReturn(new PriceByHotelsRequestBody());
        when(availRoomsExecutor.updatedPriceOccuLessOld(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("test");
        Assert.assertNotNull(reviewService.getUpdatedPriceOccuLessOld(new PriceByHotelsRequestBody(),new HashMap<>(),new HashMap<>()));
    }
    
    @Test
    public void testgetTotalPrice() throws ClientGatewayException {
        AvailRoomsResponseTransformer availRoomsResponseTransformer = Mockito.mock(AvailRoomsResponseTransformer.class);
        when(availRoomsFactory.getResponseService(Mockito.any())).thenReturn(availRoomsResponseTransformer);
        when(availRoomsResponseTransformer.convertTotalPricingResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new TotalPriceResponse());
        when(availRoomsExecutor.getTotalPricingDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new TotalPricingResponse());
        Assert.assertNotNull(reviewService.getTotalPrice(new TotalPricingRequest(),new HashMap<>(),"123",new HashMap<>(), "ANDROID"));
    }

    @Test
    public void testgetTotalPriceShowDisabledBnpl() throws ClientGatewayException {
        AvailRoomsResponseTransformer availRoomsResponseTransformer = Mockito.mock(AvailRoomsResponseTransformer.class);
        when(availRoomsFactory.getResponseService(Mockito.any())).thenReturn(availRoomsResponseTransformer);
        when(availRoomsResponseTransformer.convertTotalPricingResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new TotalPriceResponse());
        TotalPricingResponse totalPricingResponse = new TotalPricingResponse();
        totalPricingResponse.setPriceBreakdown(new TotalPricing());
        com.mmt.hotels.model.response.pricing.TotalPricing priceBreakdown = totalPricingResponse.getPriceBreakdown();

        priceBreakdown.setBnplVariant(null);
        totalPricingResponse.setShowDisabledBnplDetails(true);
        totalPricingResponse.setUserLevelBnplDisabled(true);
        when(availRoomsExecutor.getTotalPricingDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(totalPricingResponse);
        Assert.assertNotNull(reviewService.getTotalPrice(new TotalPricingRequest(),new HashMap<>(),"123",new HashMap<>(), "ANDROID"));

        priceBreakdown.setBnplVariant(BNPLVariant.BNPL_NOT_APPLICABLE);
        totalPricingResponse.setShowDisabledBnplDetails(true);
        totalPricingResponse.setUserLevelBnplDisabled(true);
        when(availRoomsExecutor.getTotalPricingDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(totalPricingResponse);
        Assert.assertNotNull(reviewService.getTotalPrice(new TotalPricingRequest(),new HashMap<>(),"123",new HashMap<>(), "ANDROID"));

        priceBreakdown.setBnplVariant(BNPLVariant.BNPL_AT_0);
        totalPricingResponse.setShowDisabledBnplDetails(true);
        totalPricingResponse.setUserLevelBnplDisabled(true);
        when(availRoomsExecutor.getTotalPricingDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(totalPricingResponse);
        Assert.assertNotNull(reviewService.getTotalPrice(new TotalPricingRequest(),new HashMap<>(),"123",new HashMap<>(), "ANDROID"));

        priceBreakdown.setBnplVariant(BNPLVariant.BNPL_AT_1);
        totalPricingResponse.setShowDisabledBnplDetails(true);
        totalPricingResponse.setUserLevelBnplDisabled(true);
        when(availRoomsExecutor.getTotalPricingDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(totalPricingResponse);
        Assert.assertNotNull(reviewService.getTotalPrice(new TotalPricingRequest(),new HashMap<>(),"123",new HashMap<>(), "ANDROID"));
    }

    @Test
    public void testgetTotalPriceNotShowDisabledBnpl() throws ClientGatewayException {
        AvailRoomsResponseTransformer availRoomsResponseTransformer = Mockito.mock(AvailRoomsResponseTransformer.class);
        when(availRoomsFactory.getResponseService(Mockito.any())).thenReturn(availRoomsResponseTransformer);
        when(availRoomsResponseTransformer.convertTotalPricingResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new TotalPriceResponse());
        TotalPricingResponse totalPricingResponse = new TotalPricingResponse();
        totalPricingResponse.setPriceBreakdown(new TotalPricing());
        com.mmt.hotels.model.response.pricing.TotalPricing priceBreakdown = totalPricingResponse.getPriceBreakdown();

        priceBreakdown.setBnplVariant(null);
        totalPricingResponse.setShowDisabledBnplDetails(false);
        totalPricingResponse.setUserLevelBnplDisabled(true);
        when(availRoomsExecutor.getTotalPricingDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(totalPricingResponse);
        Assert.assertNotNull(reviewService.getTotalPrice(new TotalPricingRequest(),new HashMap<>(),"123",new HashMap<>(), "ANDROID"));

        priceBreakdown.setBnplVariant(BNPLVariant.BNPL_NOT_APPLICABLE);
        totalPricingResponse.setShowDisabledBnplDetails(false);
        totalPricingResponse.setUserLevelBnplDisabled(true);
        when(availRoomsExecutor.getTotalPricingDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(totalPricingResponse);
        Assert.assertNotNull(reviewService.getTotalPrice(new TotalPricingRequest(),new HashMap<>(),"123",new HashMap<>(), "ANDROID"));

        priceBreakdown.setBnplVariant(BNPLVariant.BNPL_AT_0);
        totalPricingResponse.setShowDisabledBnplDetails(false);
        totalPricingResponse.setUserLevelBnplDisabled(true);
        when(availRoomsExecutor.getTotalPricingDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(totalPricingResponse);
        Assert.assertNotNull(reviewService.getTotalPrice(new TotalPricingRequest(),new HashMap<>(),"123",new HashMap<>(), "ANDROID"));

        priceBreakdown.setBnplVariant(BNPLVariant.BNPL_AT_1);
        totalPricingResponse.setShowDisabledBnplDetails(false);
        totalPricingResponse.setUserLevelBnplDisabled(true);
        when(availRoomsExecutor.getTotalPricingDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(totalPricingResponse);
        Assert.assertNotNull(reviewService.getTotalPrice(new TotalPricingRequest(),new HashMap<>(),"123",new HashMap<>(), "ANDROID"));
    }
    @Test(expected = ClientGatewayException.class)
    public void testGetTotalPriceException() throws Exception{
        when(availRoomsExecutor.getTotalPricingDetails(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(new ClientGatewayException());
        reviewService.getTotalPrice(new TotalPricingRequest(),new HashMap<>(), "123", new HashMap<>(), "ANDROID");
    }

    @Test(expected = ClientGatewayException.class)
    public void testGetUpdatedPriceOccuLessOldException() throws Exception {
        AvailRoomsRequestTransformer availRoomsRequestTransformer = Mockito.mock(AvailRoomsRequestTransformer.class);
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setDeviceDetails(new DeviceDetails());
        when(oldToNewerRequestTransformer.updateAvailRoomsRequest(Mockito.any())).thenReturn(availRoomsRequest);
        when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        when(availRoomsFactory.getRequestService(Mockito.any())).thenReturn(availRoomsRequestTransformer);
        when(availRoomsRequestTransformer.convertAvailRoomsRequest(Mockito.any(), Mockito.any())).thenReturn(new PriceByHotelsRequestBody());
        when(availRoomsExecutor.updatedPriceOccuLessOld(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(new ClientGatewayException());
        reviewService.getUpdatedPriceOccuLessOld(new PriceByHotelsRequestBody(),new HashMap<>(),new HashMap<>());
    }

    @Test(expected = ClientGatewayException.class)
    public void testGetAvailPriceOldException() throws Exception {
        AvailRoomsRequestTransformer availRoomsRequestTransformer = Mockito.mock(AvailRoomsRequestTransformer.class);
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setDeviceDetails(new DeviceDetails());
        when(oldToNewerRequestTransformer.updateAvailRoomsRequest(Mockito.any())).thenReturn(availRoomsRequest);
        when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        when(availRoomsFactory.getRequestService(Mockito.any())).thenReturn(availRoomsRequestTransformer);
        when(availRoomsRequestTransformer.convertAvailRoomsRequest(Mockito.any(), Mockito.any())).thenReturn(new PriceByHotelsRequestBody());
        when(availRoomsExecutor.availRoomsOld(Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(new ClientGatewayException());
        reviewService.getAvailPriceOld(new PriceByHotelsRequestBody(),new HashMap<>(),new HashMap<>());
    }

    @Test(expected = ClientGatewayException.class)
    public void testAvailRooms() throws ClientGatewayException {
        AvailRoomsRequestTransformer availRoomsRequestTransformer = Mockito.mock(AvailRoomsRequestTransformer.class);
        AvailRoomsResponseTransformer availRoomsResponseTransformer = Mockito.mock(AvailRoomsResponseTransformer.class);
        when(objectMapperUtil.getJsonFromObject(Mockito.any(),Mockito.any())).thenReturn("test");
        when(commonHelper.processRequest(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        when(availRoomsFactory.getRequestService(Mockito.any())).thenReturn(availRoomsRequestTransformer);
        when(availRoomsExecutor.availRooms(Mockito.any(),Mockito.any(),Mockito.anyMap())).thenReturn(new RoomDetailsResponse());
        RoomDetailsResponse roomDetailsResponse = new RoomDetailsResponse();
        roomDetailsResponse.setUserLevelBnplDisabled(true);
        when(availRoomsExecutor.availRooms(Mockito.any(),Mockito.any(),Mockito.anyMap())).thenReturn(roomDetailsResponse);
        when(availRoomsFactory.getResponseService(Mockito.any())).thenReturn(availRoomsResponseTransformer);
        Mockito.lenient().when(availRoomsResponseTransformer.convertAvailRoomsResponse(Mockito.any(),Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyString(),Mockito.any(),Mockito.anyBoolean(),Mockito.any())).thenReturn(new AvailRoomsResponse());
        Assert.assertNotNull(reviewService.availRooms(new AvailRoomsRequest(),new HashMap<>(),new HashMap<>()));

        when(availRoomsExecutor.availRooms(Mockito.any(),Mockito.any(),Mockito.anyMap())).thenThrow(new ClientGatewayException());
        reviewService.availRooms(new AvailRoomsRequest(),new HashMap<>(),new HashMap<>());
    }

    @Test(expected = ClientGatewayException.class)
    public void testFetchPayLaterEligibility() throws ClientGatewayException {
        when(commonHelper.getMMTAuth(Mockito.any(), Mockito.any())).thenReturn("mmt-auth");
        when(userServiceExecutor.getUserServiceResponse(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any(), Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new UserServiceResponse());
        Assert.assertNotNull(reviewService.fetchPayLaterEligibility(new PayLaterEligibilityRequest(), "123", new HashMap<>(),new HashMap<>(), "PWA"));
    }

    @Test
    public void testFetchPayLaterEligibilityException() throws Exception {
        AvailRoomsResponseTransformer availRoomsResponseTransformer = Mockito.mock(AvailRoomsResponseTransformer.class);
        when(availRoomsFactory.getResponseService(Mockito.any())).thenReturn(availRoomsResponseTransformer);
        when(availRoomsResponseTransformer.convertPayLaterEligibilityResponse(Mockito.any(), Mockito.any(),Mockito.anyBoolean())).thenReturn(new com.mmt.hotels.clientgateway.response.payLater.PayLaterEligibilityResponse());
        UserServiceResponse userServiceResponse = new UserServiceResponse();
        UserServiceResult userServiceResult = new UserServiceResult();
        ExtendedUser extendedUser = new ExtendedUser();
        List<UserLoginInfo> userLoginInfoList = new ArrayList<>();
        UserLoginInfo userLoginInfo = new UserLoginInfo();
        userLoginInfo.setLoginType("MOBILE");
        userLoginInfo.setLoginId("9101257825");
        userLoginInfoList.add(userLoginInfo);
        extendedUser.setLoginInfoList(userLoginInfoList);
        userServiceResult.setExtendedUser(extendedUser);
        userServiceResponse.setResult(userServiceResult);

        PayLaterEligibilityRequest payLaterEligibilityRequest = new PayLaterEligibilityRequest();
        payLaterEligibilityRequest.setTxnKey("txnKey");
        when(commonHelper.getMMTAuth(Mockito.any(), Mockito.any())).thenReturn("mmt-auth");
        when(userServiceExecutor.getUserServiceResponse(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any(), Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(userServiceResponse);

        when(availRoomsExecutor.fetchPayLaterEligibility(Mockito.any(),Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(new PayLaterEligibilityResponse());
        Assert.assertNotNull(reviewService.fetchPayLaterEligibility(payLaterEligibilityRequest, "123", new HashMap<>(),new HashMap<>(), "PWA"));
    }

}

