package com.mmt.hotels.clientgateway.service;

import com.gommt.hotels.orchestrator.detail.enums.*;
import com.gommt.hotels.orchestrator.detail.model.objects.BookingDevice;
import com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails;
import com.gommt.hotels.orchestrator.detail.model.request.DetailRequest;
import com.gommt.hotels.orchestrator.detail.model.request.common.ClientDetails;
import com.gommt.hotels.orchestrator.detail.model.response.HostCallingResponse;
import com.gommt.hotels.orchestrator.detail.model.state.FeatureFlags;
import com.gommt.hotels.orchestrator.detail.model.state.RequestDetails;
import com.gommt.hotels.orchestrator.detail.model.state.UserDetails;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.dayuse.Slot;
import com.mmt.hotels.clientgateway.request.hostcalling.HostCallingInitiateRequestBody;
import com.mmt.hotels.clientgateway.response.hostcalling.HostCallingInitiateResponse;
import com.mmt.hotels.clientgateway.restexecutors.OrchDetailExecutor;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.transformer.factory.HostCallingFactory;
import com.mmt.hotels.clientgateway.transformer.response.OrchHostCallingResponseTransformer;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.request.UserLocation;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;

import java.lang.reflect.Method;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class OrchHostCallingServiceTest {

    @InjectMocks
    private OrchHostCallingService orchHostCallingService;

    @Mock
    private Utility utility;

    @Mock
    private MetricAspect metricAspect;

    @Mock
    private OrchDetailExecutor orchDetailExecutor;

    @Mock
    private HostCallingFactory hostCallingFactory;

    @Mock
    private MetricErrorLogger metricErrorLogger;

    @Mock
    private OrchHostCallingResponseTransformer orchHostCallingResponseTransformer;

    private HostCallingInitiateRequestBody validRequest;
    private CommonModifierResponse validCommonModifierResponse;
    private Map<String, String[]> parameterMap;
    private Map<String, String> httpHeaderMap;
    private HostCallingResponse hostCallingResponse;
    private HostCallingInitiateResponse expectedResponse;

    @Before
    public void setUp() {
        // Setup valid request
        validRequest = createValidHostCallingRequest();
        validCommonModifierResponse = createValidCommonModifierResponse();
        parameterMap = new HashMap<String, String[]>();
        httpHeaderMap = new HashMap<String, String>();
        hostCallingResponse = new HostCallingResponse();
        expectedResponse = new HostCallingInitiateResponse();

        // Setup MDC values for coverage
        MDC.put(MDCHelper.MDCKeys.LANGUAGE.getStringValue(), "en");
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "in");
    }

    @Test
    public void testHostCalling_Success() throws Exception {
        // Given
        when(orchDetailExecutor.hostCalling(any(DetailRequest.class), any(), any())).thenReturn(hostCallingResponse);
        when(orchHostCallingResponseTransformer.convertHostCallingResponse(any(), any(), any())).thenReturn(expectedResponse);

        // When
        HostCallingInitiateResponse result = orchHostCallingService.hostCalling(validRequest, parameterMap, httpHeaderMap, validCommonModifierResponse);

        // Then
        assertNotNull(result);
        assertEquals(expectedResponse, result);
        verify(metricAspect).addToTimeInternalProcess(eq(Constants.PROCESS_DETAIL_RESPONSE_PROCESS), anyString(), anyLong());
    }

    @Test(expected = ClientGatewayException.class)
    public void testHostCalling_ErrorResponseFromDownstreamException() throws Exception {
        // Given
        ErrorResponseFromDownstreamException exception = new ErrorResponseFromDownstreamException(
                DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, "500", "Downstream error");
        when(orchDetailExecutor.hostCalling(any(DetailRequest.class), any(), any())).thenThrow(exception);

        // When & Then
        orchHostCallingService.hostCalling(validRequest, parameterMap, httpHeaderMap, validCommonModifierResponse);
    }

    @Test(expected = ClientGatewayException.class)
    public void testHostCalling_OtherException() throws Exception {
        // Given
        RuntimeException exception = new RuntimeException("Generic error");
        when(orchDetailExecutor.hostCalling(any(DetailRequest.class), any(), any())).thenThrow(exception);

        // When & Then
        orchHostCallingService.hostCalling(validRequest, parameterMap, httpHeaderMap, validCommonModifierResponse);
    }

    @Test(expected = ClientGatewayException.class)
    public void testHostCalling_NullSearchCriteria() throws Exception {
        // Given
        validRequest.setSearchCriteria(null);

        // When & Then
        orchHostCallingService.hostCalling(validRequest, parameterMap, httpHeaderMap, validCommonModifierResponse);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testBuildSearchRoomsRequest_NullCgRequest() {
        // When
        orchHostCallingService.buildSearchRoomsRequest(null, validCommonModifierResponse);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testBuildSearchRoomsRequest_NullCommonModifierResponse() {
        // When
        orchHostCallingService.buildSearchRoomsRequest(validRequest, null);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testBuildSearchRoomsRequest_NullSearchCriteria() {
        // Given
        validRequest.setSearchCriteria(null);

        // When
        orchHostCallingService.buildSearchRoomsRequest(validRequest, validCommonModifierResponse);
    }

    @Test
    public void testBuildSearchRoomsRequest_Success() {
        // When
        DetailRequest result = orchHostCallingService.buildSearchRoomsRequest(validRequest, validCommonModifierResponse);

        // Then
        assertNotNull(result);
        assertEquals("12345", result.getHotelId());
        assertNotNull(result.getLocation());
        assertNotNull(result.getClientDetails());
        assertNotNull(result.getRooms());
    }

    @Test
    public void testBuildLocationDetails_NullSearchCriteria() throws Exception {
        // Given
        Method method = OrchHostCallingService.class.getDeclaredMethod("buildLocationDetails", SearchCriteria.class);
        method.setAccessible(true);

        // When
        com.gommt.hotels.orchestrator.detail.model.request.common.LocationDetails result = 
            (com.gommt.hotels.orchestrator.detail.model.request.common.LocationDetails) method.invoke(orchHostCallingService, (SearchCriteria) null);

        // Then
        assertNotNull(result);
    }

    @Test
    public void testBuildLocationDetails_ValidSearchCriteria() throws Exception {
        // Given
        Method method = OrchHostCallingService.class.getDeclaredMethod("buildLocationDetails", SearchCriteria.class);
        method.setAccessible(true);
        
        StaticDetailCriteria searchCriteria = new StaticDetailCriteria();
        searchCriteria.setLocationId("LOC123");
        searchCriteria.setLocationType("CITY");
        searchCriteria.setCityCode("DEL");
        searchCriteria.setCityName("Delhi");
        searchCriteria.setCountryCode("IN");
        searchCriteria.setLat(28.6139);
        searchCriteria.setLng(77.2090);

        // When
        com.gommt.hotels.orchestrator.detail.model.request.common.LocationDetails result = 
            (com.gommt.hotels.orchestrator.detail.model.request.common.LocationDetails) method.invoke(orchHostCallingService, searchCriteria);

        // Then
        assertNotNull(result);
        assertEquals("LOC123", result.getId());
        assertEquals("city", result.getType());
        assertEquals("DEL", result.getCityId());
        assertEquals("Delhi", result.getCityName());
        assertEquals("IN", result.getCountryId());
        assertEquals("28.6139", result.getGeo().getLatitude());
        assertEquals("77.209", result.getGeo().getLongitude());
    }

    @Test
    public void testBuildSlotDetails_NullSlot() throws Exception {
        // Given
        Method method = OrchHostCallingService.class.getDeclaredMethod("buildSlotDetails", Slot.class);
        method.setAccessible(true);

        // When
        com.gommt.hotels.orchestrator.detail.model.request.common.SlotDetails result = 
            (com.gommt.hotels.orchestrator.detail.model.request.common.SlotDetails) method.invoke(orchHostCallingService, (Slot) null);

        // Then
        assertNotNull(result);
    }

    @Test
    public void testBuildSlotDetails_ValidSlot() throws Exception {
        // Given
        Method method = OrchHostCallingService.class.getDeclaredMethod("buildSlotDetails", Slot.class);
        method.setAccessible(true);
        
        Slot slot = new Slot();
        slot.setTimeSlot(6);
        slot.setDuration(12);

        // When
        com.gommt.hotels.orchestrator.detail.model.request.common.SlotDetails result = 
            (com.gommt.hotels.orchestrator.detail.model.request.common.SlotDetails) method.invoke(orchHostCallingService, slot);

        // Then
        assertNotNull(result);
        assertEquals(6, result.getTimeSlot());
        assertEquals(12, result.getDuration());
    }

    @Test
    public void testBuildRoomDetails_NullOrEmptyRoomStayCandidates() throws Exception {
        // Given
        Method method = OrchHostCallingService.class.getDeclaredMethod("buildRoomDetails", List.class, Map.class);
        method.setAccessible(true);

        Map<String, String> expDataMap = new HashMap<String, String>();

        // When - null list
        List<RoomDetails> result1 = (List<RoomDetails>) method.invoke(orchHostCallingService, null, expDataMap);
        
        // When - empty list
        List<RoomDetails> result2 = (List<RoomDetails>) method.invoke(orchHostCallingService, new ArrayList<RoomStayCandidate>(), expDataMap);

        // Then
        assertTrue(result1.isEmpty());
        assertTrue(result2.isEmpty());
    }

    @Test
    public void testBuildRoomDetails_WithDistribution() throws Exception {
        // Given
        Method method = OrchHostCallingService.class.getDeclaredMethod("buildRoomDetails", List.class, Map.class);
        method.setAccessible(true);
        
        List<RoomStayCandidate> roomStayCandidates = createRoomStayCandidates();
        Map<String, String> expDataMap = new HashMap<String, String>();
        
        when(utility.isDistributeRoomStayCandidates(any(), any())).thenReturn(true);
        when(utility.buildRoomStayDistribution(any(), any())).thenReturn(createDistributedRoomStayCandidates());

        // When
        List<RoomDetails> result = (List<RoomDetails>) method.invoke(orchHostCallingService, roomStayCandidates, expDataMap);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(2, result.get(0).getAdults().intValue());
    }

    @Test
    public void testBuildRoomDetails_WithoutDistribution() throws Exception {
        // Given
        Method method = OrchHostCallingService.class.getDeclaredMethod("buildRoomDetails", List.class, Map.class);
        method.setAccessible(true);
        
        List<RoomStayCandidate> roomStayCandidates = createRoomStayCandidates();
        Map<String, String> expDataMap = new HashMap<String, String>();
        
        when(utility.isDistributeRoomStayCandidates(any(), any())).thenReturn(false);

        // When
        List<RoomDetails> result = (List<RoomDetails>) method.invoke(orchHostCallingService, roomStayCandidates, expDataMap);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(2, result.get(0).getAdults().intValue());
    }

    @Test
    public void testBuildRoomDetails_WithNullRoomStayCandidate() throws Exception {
        // Given
        Method method = OrchHostCallingService.class.getDeclaredMethod("buildRoomDetails", List.class, Map.class);
        method.setAccessible(true);
        
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<RoomStayCandidate>();
        roomStayCandidates.add(null);
        roomStayCandidates.add(createValidRoomStayCandidate());
        
//        when(utility.isDistributeRoomStayCandidates(any(), any())).thenReturn(false);

        // When
        List<RoomDetails> result = (List<RoomDetails>) method.invoke(orchHostCallingService, roomStayCandidates, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size()); // Null candidate should be skipped
    }

    @Test
    public void testBuildClientDetails() throws Exception {
        // Given
        Method method = OrchHostCallingService.class.getDeclaredMethod("buildClientDetails", 
            com.mmt.hotels.clientgateway.request.RequestDetails.class, 
            DeviceDetails.class, 
            com.mmt.hotels.clientgateway.request.FeatureFlags.class, 
            List.class, 
            SearchCriteria.class, 
            Map.class, 
            CommonModifierResponse.class);
        method.setAccessible(true);

        com.mmt.hotels.clientgateway.request.RequestDetails requestDetails = createValidRequestDetails();
        DeviceDetails deviceDetails = createValidDeviceDetails();
        com.mmt.hotels.clientgateway.request.FeatureFlags featureFlags = createValidFeatureFlags();
        List<Filter> filterCriteria = new ArrayList<Filter>();
        SearchCriteria searchCriteria = createValidSearchCriteria();
        Map<String, String> expDataMap = new HashMap<String, String>();

        // When
        ClientDetails result = (ClientDetails) method.invoke(orchHostCallingService, 
            requestDetails, deviceDetails, featureFlags, filterCriteria, searchCriteria, expDataMap, validCommonModifierResponse);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFeatureFlags());
        assertNotNull(result.getRequestDetails());
        assertNotNull(result.getUserDetails());
        assertEquals("VISITOR123", result.getVisitorId());
        assertEquals("MC123", result.getMcId());
    }

    @Test
    public void testBuildImageDetails_NullImageDetailsRequest() throws Exception {
        // Given
        Method method = OrchHostCallingService.class.getDeclaredMethod("buildImageDetails", 
            com.mmt.hotels.clientgateway.request.ImageDetails.class);
        method.setAccessible(true);

        // When
        com.gommt.hotels.orchestrator.detail.model.state.ImageDetails result = 
            (com.gommt.hotels.orchestrator.detail.model.state.ImageDetails) method.invoke(orchHostCallingService, 
                (com.mmt.hotels.clientgateway.request.ImageDetails) null);

        // Then
        assertNotNull(result);
    }

    @Test
    public void testBuildImageDetails_ValidImageDetailsRequest() throws Exception {
        // Given
        Method method = OrchHostCallingService.class.getDeclaredMethod("buildImageDetails", 
            com.mmt.hotels.clientgateway.request.ImageDetails.class);
        method.setAccessible(true);

        com.mmt.hotels.clientgateway.request.ImageDetails imageDetailsRequest = new com.mmt.hotels.clientgateway.request.ImageDetails();
        List<com.mmt.hotels.clientgateway.request.ImageCategory> categories = createImageCategories();
        List<String> types = Arrays.asList("HOTEL", "ROOM");
        imageDetailsRequest.setCategories(categories);
        imageDetailsRequest.setTypes(types);

        // When
        com.gommt.hotels.orchestrator.detail.model.state.ImageDetails result = 
            (com.gommt.hotels.orchestrator.detail.model.state.ImageDetails) method.invoke(orchHostCallingService, imageDetailsRequest);

        // Then
        assertNotNull(result);
        assertNotNull(result.getCategories());
        assertEquals(1, result.getCategories().size());
        assertEquals(types, result.getTypes());
    }

    @Test
    public void testBuildFeatureFlags_AllBranches() throws Exception {
        // Given
        Method method = OrchHostCallingService.class.getDeclaredMethod("buildFeatureFlags", 
            com.mmt.hotels.clientgateway.request.RequestDetails.class, 
            DeviceDetails.class, 
            com.mmt.hotels.clientgateway.request.FeatureFlags.class, 
            List.class, 
            Map.class, 
            CommonModifierResponse.class);
        method.setAccessible(true);

        com.mmt.hotels.clientgateway.request.RequestDetails requestDetails = createValidRequestDetails();
        requestDetails.setIdContext(Constants.CORP_ID_CONTEXT); // Test CORP_ID_CONTEXT branch
        DeviceDetails deviceDetails = createValidDeviceDetails();
        com.mmt.hotels.clientgateway.request.FeatureFlags featureFlags = createValidFeatureFlags();
        List<Filter> filterCriteria = new ArrayList<Filter>();
        Map<String, String> expDataMap = new HashMap<String, String>();
        expDataMap.put("roomCountDefault", Constants.EXACT_ROOM_VALUE); // Test EXACT_ROOM_VALUE branch

        when(utility.checkIfFilterValueExistsInAppliedFilterMap(any())).thenReturn(true);

        // When
        FeatureFlags result = (FeatureFlags) method.invoke(orchHostCallingService, 
            requestDetails, deviceDetails, featureFlags, filterCriteria, expDataMap, validCommonModifierResponse);

        // Then
        assertNotNull(result);
        assertTrue(result.isRoomLevelDetails());
        assertFalse(result.isCityTaxExclusive()); // Should be false due to CORP_ID_CONTEXT
        assertFalse(result.isRoomPreferenceEnabled()); // Should be toggled due to EXACT_ROOM_VALUE
        assertTrue(result.isAdvancedFiltering());
    }

    @Test
    public void testBuildBookingDevice_NullDeviceDetails() throws Exception {
        // Given
        Method method = OrchHostCallingService.class.getDeclaredMethod("buildBookingDevice", DeviceDetails.class);
        method.setAccessible(true);

        // When
        BookingDevice result = (BookingDevice) method.invoke(orchHostCallingService, (DeviceDetails) null);

        // Then
        assertNotNull(result);
    }

    @Test
    public void testBuildBookingDevice_ValidDeviceDetails() throws Exception {
        // Given
        Method method = OrchHostCallingService.class.getDeclaredMethod("buildBookingDevice", DeviceDetails.class);
        method.setAccessible(true);

        DeviceDetails deviceDetails = createValidDeviceDetails();

        // When
        BookingDevice result = (BookingDevice) method.invoke(orchHostCallingService, deviceDetails);

        // Then
        assertNotNull(result);
        assertEquals("DEVICE123", result.getDeviceId());
        assertEquals("Samsung Galaxy", result.getDeviceName());
        assertEquals(DeviceType.MOBILE, result.getDeviceType());
        assertEquals("1.0.0", result.getAppVersion());
        assertEquals("WIFI", result.getNetworkType());
    }

    @Test
    public void testBuildMultiCurrencyInfo_Null() throws Exception {
        // Given
        Method method = OrchHostCallingService.class.getDeclaredMethod("buildMultiCurrencyInfo", 
            com.mmt.hotels.clientgateway.request.MultiCurrencyInfo.class);
        method.setAccessible(true);

        // When
        com.gommt.hotels.orchestrator.detail.model.objects.MultiCurrencyInfo result = 
            (com.gommt.hotels.orchestrator.detail.model.objects.MultiCurrencyInfo) method.invoke(orchHostCallingService, 
                (com.mmt.hotels.clientgateway.request.MultiCurrencyInfo) null);

        // Then
        assertNull(result);
    }

    @Test
    public void testBuildMultiCurrencyInfo_Valid() throws Exception {
        // Given
        Method method = OrchHostCallingService.class.getDeclaredMethod("buildMultiCurrencyInfo", 
            com.mmt.hotels.clientgateway.request.MultiCurrencyInfo.class);
        method.setAccessible(true);

        com.mmt.hotels.clientgateway.request.MultiCurrencyInfo multiCurrencyInfo = new com.mmt.hotels.clientgateway.request.MultiCurrencyInfo();
        multiCurrencyInfo.setUserCurrency("USD");
        multiCurrencyInfo.setRegionCurrency("INR");

        // When
        com.gommt.hotels.orchestrator.detail.model.objects.MultiCurrencyInfo result = 
            (com.gommt.hotels.orchestrator.detail.model.objects.MultiCurrencyInfo) method.invoke(orchHostCallingService, multiCurrencyInfo);

        // Then
        assertNotNull(result);
        assertEquals("USD", result.getUserCurrency());
        assertEquals("INR", result.getRegionCurrency());
    }

    @Test
    public void testBuildUserDetails() throws Exception {
        // Given
        Method method = OrchHostCallingService.class.getDeclaredMethod("buildUserDetails", 
            com.mmt.hotels.clientgateway.request.RequestDetails.class, 
            CommonModifierResponse.class);
        method.setAccessible(true);

        com.mmt.hotels.clientgateway.request.RequestDetails requestDetails = createValidRequestDetails();

        // When
        UserDetails result = (UserDetails) method.invoke(orchHostCallingService, requestDetails, validCommonModifierResponse);

        // Then
        assertNotNull(result);
        assertNotNull(result.getLocation());
        assertEquals("9876543210", result.getMobile());
        assertEquals("AUTH123", result.getMmtAuth());
        assertEquals("UUID123", result.getUuid());
        assertTrue(result.isLoggedIn());
        assertNotNull(result.getUserSegments());
        assertEquals(ProfileType.PERSONAL, result.getProfileType());
        assertEquals(SubProfileType.DEFAULT, result.getSubProfileType());
    }

    @Test
    public void testBuildUserGlobalInfo() {
        // Given
        UserGlobalInfo userGlobalInfo = new UserGlobalInfo();
        userGlobalInfo.setUserCountry("IN");
        userGlobalInfo.setEntityName("MMT");

        // When
        com.gommt.hotels.orchestrator.detail.model.objects.UserGlobalInfo result = 
            OrchHostCallingService.buildUserGlobalInfo(userGlobalInfo);

        // Then
        assertNotNull(result);
        assertEquals("IN", result.getUserCountry());
        assertEquals("MMT", result.getEntityName());
    }

    // Helper methods to create valid test objects
    private HostCallingInitiateRequestBody createValidHostCallingRequest() {
        HostCallingInitiateRequestBody request = new HostCallingInitiateRequestBody();
        request.setSearchCriteria(createValidStaticDetailCriteria());
        request.setRequestDetails(createValidRequestDetails());
        request.setDeviceDetails(createValidDeviceDetails());
        request.setFeatureFlags(createValidFeatureFlags());
        request.setFilterCriteria(new ArrayList<Filter>());
        request.setImageDetails(createValidImageDetails());
        // request.setExpVariantKeys(new ArrayList<String>()); // Commented out due to type mismatch
        request.setExpDataMap(new HashMap<String, String>());
        request.setClient("WEB");
        return request;
    }

    private StaticDetailCriteria createValidStaticDetailCriteria() {
        StaticDetailCriteria criteria = new StaticDetailCriteria();
        criteria.setHotelId("12345");
        criteria.setCheckIn("2023-12-01");
        criteria.setCheckOut("2023-12-03");
        criteria.setLocationId("LOC123");
        criteria.setLocationType("CITY");
        criteria.setCityCode("DEL");
        criteria.setCityName("Delhi");
        criteria.setCountryCode("IN");
        criteria.setLat(28.6139);
        criteria.setLng(77.2090);
        criteria.setRoomStayCandidates(createRoomStayCandidates());
        criteria.setSlot(createValidSlot());
        criteria.setUserSearchType("NORMAL");
        criteria.setCurrency("INR");
        criteria.setMultiCurrencyInfo(createValidMultiCurrencyInfo());
        criteria.setUserGlobalInfo(createValidUserGlobalInfo());
        return criteria;
    }

    private com.mmt.hotels.clientgateway.request.RequestDetails createValidRequestDetails() {
        com.mmt.hotels.clientgateway.request.RequestDetails requestDetails = new com.mmt.hotels.clientgateway.request.RequestDetails();
        requestDetails.setVisitorId("VISITOR123");
        requestDetails.setRequestId("REQ123");
        requestDetails.setJourneyId("JOURNEY123");
        requestDetails.setRequestor("WEB");
        requestDetails.setChannel("DIRECT");
        requestDetails.setSessionId("SESSION123");
        requestDetails.setFunnelSource("HOTELS");
        requestDetails.setPageContext("DETAIL");
        requestDetails.setBrand("MMT");
        requestDetails.setIdContext("B2C");
        requestDetails.setSiteDomain("IN");
        requestDetails.setTrafficSource(createValidTrafficSource());
        requestDetails.setSemanticSearchDetails(createValidSemanticSearchDetails());
        requestDetails.setPremium(false);
        requestDetails.setLoggedIn(true);
        requestDetails.setMyraMsgId("MYRA123");
        return requestDetails;
    }

    private DeviceDetails createValidDeviceDetails() {
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setDeviceId("DEVICE123");
        deviceDetails.setDeviceName("Samsung Galaxy");
        deviceDetails.setBookingDevice("MOBILE");
        deviceDetails.setAppVersion("1.0.0");
        deviceDetails.setNetworkType("wifi");
        return deviceDetails;
    }

    private com.mmt.hotels.clientgateway.request.FeatureFlags createValidFeatureFlags() {
        com.mmt.hotels.clientgateway.request.FeatureFlags featureFlags = new com.mmt.hotels.clientgateway.request.FeatureFlags();
        featureFlags.setWalletRequired(true);
        featureFlags.setCoupon(true);
        featureFlags.setComparator(true);
        featureFlags.setSeoDS(true);
        featureFlags.setCheckAvailability(true);
        featureFlags.setOrientation("PORTRAIT");
        featureFlags.setSimilarHotel(true);
        featureFlags.setMaskedPropertyName(true);
        featureFlags.setPropSearch(true);
        featureFlags.setPremiumThemesCardRequired(true);
        featureFlags.setUpsellRateplanRequired(true);
        featureFlags.setSeoCohort("DEFAULT");
        return featureFlags;
    }

    private CommonModifierResponse createValidCommonModifierResponse() {
        CommonModifierResponse response = new CommonModifierResponse();
        response.setMcId("MC123");
        response.setAffiliateId("AFF123");
        response.setApplicationId(1);
        response.setCdfContextId("CDF123");
        response.setMmtAuth("AUTH123");
        response.setMobile("9876543210");
        response.setOriginalTrafficSource("DIRECT");
        response.setContentExpDataMap(new HashMap<String, String>());
        response.setManthanExpDataMap(new HashMap<String, String>());
        response.setCityTaxExclusive(true);
        response.setHomestayV2Flow(true);
        
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setUuid("UUID123");
        extendedUser.setProfileType("PERSONAL");
        extendedUser.setAffiliateId("DEFAULT");
        response.setExtendedUser(extendedUser);

        HydraResponse hydraResponse = new HydraResponse();
        hydraResponse.setFlightBooker(true);
        Set<String> segments = new HashSet<String>();
        segments.add("PREMIUM");
        hydraResponse.setHydraMatchedSegment(segments);
        response.setHydraResponse(hydraResponse);

        UserLocation userLocation = new UserLocation();
        userLocation.setCity("Delhi");
        userLocation.setCountry("IN");
        userLocation.setState("DL");
        response.setUserLocation(userLocation);

        return response;
    }

    private List<RoomStayCandidate> createRoomStayCandidates() {
        List<RoomStayCandidate> candidates = new ArrayList<RoomStayCandidate>();
        candidates.add(createValidRoomStayCandidate());
        return candidates;
    }

    private RoomStayCandidate createValidRoomStayCandidate() {
        RoomStayCandidate candidate = new RoomStayCandidate();
        candidate.setAdultCount(2);
        candidate.setChildAges(Arrays.asList(5, 8));
        return candidate;
    }

    private List<com.mmt.hotels.model.request.RoomStayCandidate> createDistributedRoomStayCandidates() {
        List<com.mmt.hotels.model.request.RoomStayCandidate> candidates = new ArrayList<com.mmt.hotels.model.request.RoomStayCandidate>();
        com.mmt.hotels.model.request.RoomStayCandidate candidate = new com.mmt.hotels.model.request.RoomStayCandidate();
        
        List<com.mmt.hotels.model.request.GuestCount> guestCounts = new ArrayList<com.mmt.hotels.model.request.GuestCount>();
        com.mmt.hotels.model.request.GuestCount guestCount = new com.mmt.hotels.model.request.GuestCount();
        guestCount.setCount("2");
        guestCount.setAges(Arrays.asList(5, 8));
        guestCounts.add(guestCount);
        
        candidate.setGuestCounts(guestCounts);
        candidates.add(candidate);
        return candidates;
    }

    private Slot createValidSlot() {
        Slot slot = new Slot();
        slot.setTimeSlot(6);
        slot.setDuration(12);
        return slot;
    }

    private com.mmt.hotels.clientgateway.request.ImageDetails createValidImageDetails() {
        com.mmt.hotels.clientgateway.request.ImageDetails imageDetails = new com.mmt.hotels.clientgateway.request.ImageDetails();
        imageDetails.setCategories(createImageCategories());
        imageDetails.setTypes(Arrays.asList("HOTEL", "ROOM"));
        return imageDetails;
    }

    private List<com.mmt.hotels.clientgateway.request.ImageCategory> createImageCategories() {
        List<com.mmt.hotels.clientgateway.request.ImageCategory> categories = new ArrayList<com.mmt.hotels.clientgateway.request.ImageCategory>();
        com.mmt.hotels.clientgateway.request.ImageCategory category = new com.mmt.hotels.clientgateway.request.ImageCategory();
        category.setType("HOTEL");
        category.setCount(10);
        category.setHeight(800);
        category.setWidth(600);
        category.setImageFormat("JPG");
        categories.add(category);
        return categories;
    }

    private SearchCriteria createValidSearchCriteria() {
        SearchCriteria searchCriteria = new SearchCriteria();
        searchCriteria.setLocationId("LOC123");
        searchCriteria.setLocationType("CITY");
        searchCriteria.setCityCode("DEL");
        searchCriteria.setCityName("Delhi");
        searchCriteria.setCountryCode("IN");
        searchCriteria.setLat(28.6139);
        searchCriteria.setLng(77.2090);
        searchCriteria.setCurrency("INR");
        searchCriteria.setMultiCurrencyInfo(createValidMultiCurrencyInfo());
        searchCriteria.setUserGlobalInfo(createValidUserGlobalInfo());
        return searchCriteria;
    }

    private com.mmt.hotels.clientgateway.request.TrafficSource createValidTrafficSource() {
        com.mmt.hotels.clientgateway.request.TrafficSource trafficSource = new com.mmt.hotels.clientgateway.request.TrafficSource();
        trafficSource.setSource("DIRECT");
        trafficSource.setFlowType("NORMAL");
        trafficSource.setType("B2C");
        return trafficSource;
    }

    private SemanticSearchDetails createValidSemanticSearchDetails() {
        SemanticSearchDetails semanticSearchDetails = new SemanticSearchDetails();
        semanticSearchDetails.setQueryText("luxury hotel");
        // semanticSearchDetails.setSemanticData(new HashMap<String, String>()); // Commented out due to type mismatch
        return semanticSearchDetails;
    }

    private com.mmt.hotels.clientgateway.request.MultiCurrencyInfo createValidMultiCurrencyInfo() {
        com.mmt.hotels.clientgateway.request.MultiCurrencyInfo multiCurrencyInfo = new com.mmt.hotels.clientgateway.request.MultiCurrencyInfo();
        multiCurrencyInfo.setUserCurrency("USD");
        multiCurrencyInfo.setRegionCurrency("INR");
        return multiCurrencyInfo;
    }

    private UserGlobalInfo createValidUserGlobalInfo() {
        UserGlobalInfo userGlobalInfo = new UserGlobalInfo();
        userGlobalInfo.setUserCountry("IN");
        userGlobalInfo.setEntityName("MMT");
        return userGlobalInfo;
    }
} 