package com.mmt.hotels.clientgateway.service;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.restexecutors.SearchRoomsExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.SearchRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.request.OldToNewerRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.SearchRoomsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.pwa.SearchRoomsRequestTransformerPWA;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.model.request.*;
import com.mmt.hotels.model.request.upsell.UpsellHotelDetailResponse;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.response.HotelComparatorResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class ComparatorServiceTest {
    @InjectMocks
    ComparatorService comparatorService;

    @Mock
    CommonHelper commonHelper;

    @Mock
    OldToNewerRequestTransformer oldToNewerRequestTransformer;

    @Mock
    SearchRoomsFactory searchRoomsFactory;

    @Mock
    SearchRoomsExecutor searchRoomsExecutor;

    @Mock
    MetricErrorLogger metricErrorLogger;

    @Test
    public void testComparatorOld() throws ClientGatewayException, IOException {
        HotelDetailsMobRequestBody requestBody = getHotelDetailsMobRequestBody();
        SearchRoomsRequestTransformer searchRoomsRequestTransformer = Mockito.mock(SearchRoomsRequestTransformerPWA.class);
        SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
        searchRoomsRequest.setDeviceDetails(new DeviceDetails());
        searchRoomsRequest.setSearchCriteria(new SearchRoomsCriteria());
        Mockito.when(oldToNewerRequestTransformer.updateSearchRoomRequest(Mockito.any())).thenReturn(searchRoomsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new CommonModifierResponse());
        Mockito.when(searchRoomsFactory.getRequestService(Mockito.any())).thenReturn(searchRoomsRequestTransformer);
        Mockito.when(searchRoomsRequestTransformer.convertSearchRoomsRequest(Mockito.any(), Mockito.any())).thenReturn(new PriceByHotelsRequestBody());
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        InputStream comapratorResponse = getClass().getClassLoader().getResourceAsStream("mockrequestresponsetest/comparatorResponse.json");
        UpsellHotelDetailResponse comparatorResponse = mapper.readValue(comapratorResponse, UpsellHotelDetailResponse.class);
        Mockito.when(searchRoomsExecutor.getComparatorOld(Mockito.any(),Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(comparatorResponse);
        Assert.assertNotNull(comparatorService.comparatorOld(requestBody,new HashMap<>(), new HashMap<>(), ""));
    }

    @Test (expected = ClientGatewayException.class)
    public void testComparatorOld_withException() throws ClientGatewayException {
        HotelDetailsMobRequestBody requestBody = getHotelDetailsMobRequestBody();
        SearchRoomsRequestTransformer searchRoomsRequestTransformer = Mockito.mock(SearchRoomsRequestTransformerPWA.class);
        SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
        searchRoomsRequest.setDeviceDetails(new DeviceDetails());
        searchRoomsRequest.setSearchCriteria(new SearchRoomsCriteria());
        Mockito.when(oldToNewerRequestTransformer.updateSearchRoomRequest(Mockito.any())).thenReturn(searchRoomsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new CommonModifierResponse());
        Mockito.when(searchRoomsFactory.getRequestService(Mockito.any())).thenReturn(searchRoomsRequestTransformer);
        Mockito.when(searchRoomsRequestTransformer.convertSearchRoomsRequest(Mockito.any(), Mockito.any())).thenReturn(new PriceByHotelsRequestBody());
        Mockito.when(searchRoomsExecutor.getComparatorOld(Mockito.any(),Mockito.any(), Mockito.any(),Mockito.any())).thenThrow(ClientGatewayException.class);
        comparatorService.comparatorOld(requestBody,new HashMap<>(),new HashMap<>(), "");
    }

    @Test
    public void testComparatorOld_withResponseErrors() throws ClientGatewayException, IOException {
        HotelDetailsMobRequestBody requestBody = getHotelDetailsMobRequestBody();
        SearchRoomsRequestTransformer searchRoomsRequestTransformer = Mockito.mock(SearchRoomsRequestTransformerPWA.class);
        SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
        searchRoomsRequest.setDeviceDetails(new DeviceDetails());
        searchRoomsRequest.setSearchCriteria(new SearchRoomsCriteria());
        
        Mockito.when(oldToNewerRequestTransformer.updateSearchRoomRequest(Mockito.any())).thenReturn(searchRoomsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new CommonModifierResponse());
        Mockito.when(searchRoomsFactory.getRequestService(Mockito.any())).thenReturn(searchRoomsRequestTransformer);
        Mockito.when(searchRoomsRequestTransformer.convertSearchRoomsRequest(Mockito.any(), Mockito.any())).thenReturn(new PriceByHotelsRequestBody());
        
        // Create response with errors
        UpsellHotelDetailResponse responseWithErrors = new UpsellHotelDetailResponse();
        ResponseErrors errors = new ResponseErrors();
        responseWithErrors.setResponseErrors(errors);
        
        Mockito.when(searchRoomsExecutor.getComparatorOld(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(responseWithErrors);
        
        HotelComparatorResponse result = comparatorService.comparatorOld(requestBody, new HashMap<>(), new HashMap<>(), "");
        
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getResponseErrors());
    }

    @Test
    public void testConvertSearchRoomsRequest_withNullComparatorRequest() throws ClientGatewayException {
        HotelDetailsMobRequestBody requestBody = getHotelDetailsMobRequestBody();
        SearchRoomsRequestTransformer searchRoomsRequestTransformer = Mockito.mock(SearchRoomsRequestTransformerPWA.class);
        SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
        searchRoomsRequest.setDeviceDetails(new DeviceDetails());
        searchRoomsRequest.setSearchCriteria(new SearchRoomsCriteria());
        
        Mockito.when(oldToNewerRequestTransformer.updateSearchRoomRequest(Mockito.any())).thenReturn(searchRoomsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new CommonModifierResponse());
        Mockito.when(searchRoomsFactory.getRequestService(Mockito.any())).thenReturn(searchRoomsRequestTransformer);
        Mockito.when(searchRoomsRequestTransformer.convertSearchRoomsRequest(Mockito.any(), Mockito.any())).thenReturn(null); // This triggers the null comparatorRequest path
        
        // Create a proper response with hotelSearchResponse to avoid NPE
        UpsellHotelDetailResponse response = new UpsellHotelDetailResponse();
        com.mmt.hotels.model.response.searchwrapper.SearchWrapperResponseBO hotelSearchResponse = 
            new com.mmt.hotels.model.response.searchwrapper.SearchWrapperResponseBO.Builder()
                .buildHotelsList(new ArrayList<>())
                .buildCountryCode("IN")
                .buildCurrency("INR")
                .build();
        response.setHotelSearchResponse(hotelSearchResponse);
        
        Mockito.when(searchRoomsExecutor.getComparatorOld(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
        
        HotelComparatorResponse result = comparatorService.comparatorOld(requestBody, new HashMap<>(), new HashMap<>(), "");
        
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getHotelSearchResponse());
    }

    @Test
    public void testParseComparatorHotels_withEmptyHotelList() throws ClientGatewayException, IOException {
        HotelDetailsMobRequestBody requestBody = getHotelDetailsMobRequestBody();
        SearchRoomsRequestTransformer searchRoomsRequestTransformer = Mockito.mock(SearchRoomsRequestTransformerPWA.class);
        SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
        searchRoomsRequest.setDeviceDetails(new DeviceDetails());
        searchRoomsRequest.setSearchCriteria(new SearchRoomsCriteria());
        
        Mockito.when(oldToNewerRequestTransformer.updateSearchRoomRequest(Mockito.any())).thenReturn(searchRoomsRequest);
        Mockito.when(commonHelper.processRequest(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new CommonModifierResponse());
        Mockito.when(searchRoomsFactory.getRequestService(Mockito.any())).thenReturn(searchRoomsRequestTransformer);
        Mockito.when(searchRoomsRequestTransformer.convertSearchRoomsRequest(Mockito.any(), Mockito.any())).thenReturn(new PriceByHotelsRequestBody());
        
        // Create response with empty hotel list using Builder pattern
        UpsellHotelDetailResponse response = new UpsellHotelDetailResponse();
        com.mmt.hotels.model.response.searchwrapper.SearchWrapperResponseBO hotelSearchResponse = 
            new com.mmt.hotels.model.response.searchwrapper.SearchWrapperResponseBO.Builder()
                .buildHotelsList(new ArrayList<>()) // Empty list
                .buildCountryCode("IN")
                .buildCurrency("INR")
                .build();
        response.setHotelSearchResponse(hotelSearchResponse);
        
        Mockito.when(searchRoomsExecutor.getComparatorOld(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
        
        HotelComparatorResponse result = comparatorService.comparatorOld(requestBody, new HashMap<>(), new HashMap<>(), "");
        
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getHotelSearchResponse());
        Assert.assertEquals("IN", result.getHotelSearchResponse().getCountryCode());
        Assert.assertEquals("INR", result.getHotelSearchResponse().getCurrency());
        Assert.assertTrue(result.getHotelSearchResponse().getHotelList().isEmpty());
    }

    private HotelDetailsMobRequestBody getHotelDetailsMobRequestBody() {
        HotelDetailsMobRequestBody hotelDetailsMobRequestBody=new HotelDetailsMobRequestBody();
        hotelDetailsMobRequestBody.setBookingDevice("android");
        ResponseFilterFlags responseFilterFlags =new ResponseFilterFlags() ;
        responseFilterFlags.setWalletRequired(true);
        hotelDetailsMobRequestBody.setResponseFilterFlags(responseFilterFlags);
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate candidate =new RoomStayCandidate() ;
        List<GuestCount> guestCounts =new ArrayList<>() ;
        GuestCount guestCount =new GuestCount() ;
        guestCount.setCount("2");
        List<Integer> ages = new ArrayList<>();
        ages.add(19);
        ages.add(20);
        guestCount.setAges(ages);
        guestCounts.add(guestCount);
        candidate.setGuestCounts(guestCounts);
        roomStayCandidates.add(candidate);
        hotelDetailsMobRequestBody.setRoomStayCandidates(roomStayCandidates);
        GuestRecommendationEnabledReqBody guestRecommendation = new GuestRecommendationEnabledReqBody();
        guestRecommendation.setMaxRecommendations("1");
        guestRecommendation.setText("true");
        hotelDetailsMobRequestBody.setGuestRecommendEnabled(guestRecommendation);
        List<String> hotelList = new ArrayList<>();
        hotelList.add("123");
        hotelDetailsMobRequestBody.setComparatorHotelsList(hotelList);
        hotelDetailsMobRequestBody.setHotelId("226");
        return hotelDetailsMobRequestBody;
    }
}
