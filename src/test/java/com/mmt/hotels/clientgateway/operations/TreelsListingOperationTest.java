package com.mmt.hotels.clientgateway.operations;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.request.ListingSearchRequestV2;
import com.mmt.hotels.clientgateway.request.TreelsSearchCriteria;
import com.mmt.hotels.clientgateway.response.listing.ExploreMoreData;
import com.mmt.hotels.clientgateway.response.listing.ListingProduct;
import com.mmt.hotels.clientgateway.response.listing.TreelsListingResponse;
import com.mmt.hotels.clientgateway.restexecutors.TreelsListingExecutor;
import com.mmt.hotels.clientgateway.transformer.request.TreelsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.TreelsResponseTransformer;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class TreelsListingOperationTest {

    @InjectMocks
    TreelsListingOperation treelsListingOperation;

    @Mock
    private MetricErrorLogger metricErrorLogger;

    @Mock
    private CommonHelper commonHelper;

    @Mock
    private MetricAspect metricAspect;

    @Mock
    private TreelsRequestTransformer treelsRequestTransformer;

    @Mock
    private TreelsListingExecutor treelsListingExecutor;

    @Mock
    private TreelsResponseTransformer treelsResponseTransformer;

    @Mock
    private Utility utility;



    @Test
    public void doListingOperationTest() throws ClientGatewayException {
        ListingSearchRequestV2 listingSearchRequestV2 = new ListingSearchRequestV2();
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> httpHeaderMap = new HashMap<>();
        treelsListingOperation.doListingOperation(listingSearchRequestV2, parameterMap, httpHeaderMap);
        listingSearchRequestV2.setSearchCriteria(new TreelsSearchCriteria());
        Mockito.when(treelsRequestTransformer.convertSearchRequest((ListingSearchRequestV2) Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());
        Mockito.when(treelsListingExecutor.search(Mockito.any(),Mockito.anyMap(), Mockito.anyMap())).thenReturn(null);
        TreelsListingResponse treelsListingResponse = new TreelsListingResponse();
        treelsListingResponse.setExpVariantKeys("test");
        treelsListingResponse.setExploreMoreData(new ExploreMoreData());
        treelsListingResponse.setProducts(new ArrayList<>());
        treelsListingResponse.getProducts().add(ListingProduct.builder()
                        .description("test")
                        .title("test")
                        .subtitle("test")
                .build());
        Mockito.when(treelsResponseTransformer.convertTreelsResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(treelsListingResponse);
        TreelsListingResponse resp = treelsListingOperation.doListingOperation(listingSearchRequestV2, parameterMap, httpHeaderMap);
        Assert.assertEquals(resp.getExpVariantKeys(), "test");
        Assert.assertNotNull(resp.getExploreMoreData());
        Assert.assertEquals(resp.getProducts().get(0).getDescription(), "test");
        Assert.assertEquals(resp.getProducts().get(0).getTitle(), "test");
        Assert.assertEquals(resp.getProducts().get(0).getSubtitle(), "test");
    }

    @Test
    public void doListingOperationWithErrorTest() throws ClientGatewayException {
        try {
            ListingSearchRequestV2 listingSearchRequestV2 = new ListingSearchRequestV2();
            Map<String, String[]> parameterMap = new HashMap<>();
            Map<String, String> httpHeaderMap = new HashMap<>();
            treelsListingOperation.doListingOperation(listingSearchRequestV2, parameterMap, httpHeaderMap);
            listingSearchRequestV2.setSearchCriteria(new TreelsSearchCriteria());
            Mockito.when(treelsRequestTransformer.convertSearchRequest((ListingSearchRequestV2) Mockito.any(), Mockito.any())).thenReturn(new SearchWrapperInputRequest());
            Mockito.when(treelsListingExecutor.search(Mockito.any(),Mockito.anyMap(), Mockito.anyMap())).thenThrow(new ClientGatewayException());
            Assert.assertNotNull(treelsListingOperation.doListingOperation(listingSearchRequestV2, parameterMap, httpHeaderMap));
        } catch (Exception e) {
            Assert.assertTrue(e instanceof ClientGatewayException);
        }
    }
}
