package com.mmt.hotels.clientgateway.controller;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.restexecutors.ByPassExecutor;
import com.mmt.hotels.clientgateway.util.ClientBackendUtility;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.util.Tuple;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import com.mmt.hotels.clientgateway.constants.ByPassUrls;

import junit.framework.Assert;

@RunWith(MockitoJUnitRunner.class)
public class ByPassControllerTest {
	
	@InjectMocks
	private ByPassController byPassController;

	@Spy
	private RequestHandler requestHandler;

	@Mock
	private ByPassExecutor byPassExecutor;

	@Mock
	private MetricErrorLogger metricErrorLogger;
	
	@Test
	public void testPostByPass() {
		HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
		byPassController.postByPass("test", "test","test", request, response);
	}
	
	@Test
	public void testGetByPass() {
		HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
		byPassController.getByPass("test","test", request, response);
	}
	
	@Test
	public void testGetDestinationUrl() throws UnsupportedEncodingException {
		Map<String, String[]> parameterMap = new HashMap<String, String[]>();
		parameterMap.put("test", new String[1]);
		parameterMap.get("test")[0] = "test";
		Map<String, String> httpHeaders = new HashMap<>();
		httpHeaders.put("user-currency", "INR");
		
		Assert.assertNotNull(byPassController.getDestinationUrl(ByPassUrls.SOURCE_GET_FLYFISH_UPVOTE_DOWNVOTE_URL, 
				parameterMap, "test",null, httpHeaders));
		Assert.assertNotNull(byPassController.getDestinationUrl(ByPassUrls.SOURCE_OTP_GENERATE_URL,
				parameterMap, "test",null, httpHeaders));
		Assert.assertNotNull(byPassController.getDestinationUrl(ByPassUrls.SOURCE_OTP_VALIDATE_URL,
				parameterMap, "test",null, httpHeaders));
		Assert.assertNotNull(byPassController.getDestinationUrl(ByPassUrls.SOURCE_GET_TOTAL_PRICING_URL,
				parameterMap, "test",null, httpHeaders));
		Assert.assertNotNull(byPassController.getDestinationUrl(ByPassUrls.SOURCE_VALIDATE_COUPON_URL,
				parameterMap, "test",null, httpHeaders));
		Assert.assertNotNull(byPassController.getDestinationUrl(ByPassUrls.SOURCE_EMI_DETAILS,
				parameterMap, "test",null, httpHeaders));
		Assert.assertNotNull(byPassController.getDestinationUrl(ByPassUrls.SOURCE_STATIC_POLICIES,
				parameterMap, "test",null, httpHeaders));
	}

	@Test
	public void testPostByPass_Success() throws Exception {
		MockHttpServletRequest request = new MockHttpServletRequest();
		HttpServletResponse response = new MockHttpServletResponse();
		request.addHeader("tid", "test");
		request.setRequestURI(ByPassUrls.SOURCE_GET_PMS_CONFIG);

		// Mocking dependencies
		Mockito.when(requestHandler.effectiveCorrelationKey(Mockito.anyString(), Mockito.anyString())).thenReturn("correlationKey");
		Mockito.when(byPassExecutor.executeByPassRequest(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
				.thenReturn("response");

		String result = byPassController.postByPass("test", "test", "test", request, response);

		Assert.assertNotNull(result);
		Assert.assertEquals("response", result);
	}

	@Test
	public void testPostByPass_Exception() {
		HttpServletRequest request = new MockHttpServletRequest();
		HttpServletResponse response = new MockHttpServletResponse();
		((MockHttpServletRequest) request).addHeader("tid", "test");

		// Mocking dependencies to throw an exception
		Mockito.when(requestHandler.effectiveCorrelationKey(Mockito.anyString(), Mockito.anyString())).thenThrow(new RuntimeException("Exception"));

		String result = byPassController.postByPass("test", "test", "test", request, response);

		Assert.assertNotNull(result);
		Assert.assertEquals(ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR), result);
	}

	@Test
	public void testGetByPass_Success() throws Exception {
		MockHttpServletRequest request = new MockHttpServletRequest();
		HttpServletResponse response = new MockHttpServletResponse();
		request.addHeader("tid", "test");
		request.setRequestURI(ByPassUrls.SOURCE_GET_PMS_CONFIG);

		// Mocking dependencies
		Mockito.when(requestHandler.effectiveCorrelationKey(Mockito.anyString(), Mockito.anyString())).thenReturn("correlationKey");
		Mockito.when(byPassExecutor.executeGetByPassRequest(Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
				.thenReturn("response");

		String result = byPassController.getByPass("test", "test", request, response);

		Assert.assertNotNull(result);
		Assert.assertEquals("response", result);
	}

	@Test
	public void testGetByPass_Exception() {
		MockHttpServletRequest request = new MockHttpServletRequest();
		HttpServletResponse response = new MockHttpServletResponse();
		request.addHeader("tid", "test");

		// Mocking dependencies to throw an exception
		Mockito.when(requestHandler.effectiveCorrelationKey(Mockito.anyString(), Mockito.anyString())).thenThrow(new RuntimeException("Exception"));

		String result = byPassController.getByPass("test", "test", request, response);

		Assert.assertNotNull(result);
		Assert.assertEquals(ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR), result);
	}
}
