package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.UpvoteRequest;
import com.mmt.hotels.model.response.flyfish.UgcReviewResponseData;
import com.mmt.hotels.clientgateway.service.DetailService;
import com.mmt.hotels.model.response.flyfish.UgcReviewResponse;
import com.mmt.hotels.clientgateway.restexecutors.ByPassExecutor;
import com.mmt.hotels.clientgateway.thirdparty.request.UgcReviewRequest;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Objects;

import static com.mmt.hotels.model.request.flyfish.OTA.EXP;
import static com.mmt.hotels.model.request.flyfish.OTA.MMT;

@RunWith(MockitoJUnitRunner.class)
public class PlatformUgcControllerTest {

    @InjectMocks
    private PlatformUgcController platformUgcController;

    @InjectMocks
    private DetailController detailController;

    @Mock
    private DetailService detailService;

    @Mock
    private CommonConfigConsul commonConfigConsul;

    @Spy
    private MDCHelper mdcHelper;

    @Spy
    private RequestHandler requestHandler;

    @Mock
    private MetricAspect metricAspect;

    @Mock
    private ByPassExecutor byPassExecutor;

    @Test
    public void UGCReviewApiTest() throws Exception {
//        Mockito.when(commonConfigConsul.isUgcReviewsOrchEnabled()).thenReturn(false);
        MockHttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        request.addHeader("tid", "trinatest");

        UgcReviewResponseData ugcReviewResponseData = new UgcReviewResponseData();
        UgcReviewResponse ugcReviewResponse = new UgcReviewResponse();
        ugcReviewResponse.setMmtReviewCount(1);
        ugcReviewResponseData.setResponse(ugcReviewResponse);
        Mockito.when(detailService.getUgcReviewsFromHes(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(ugcReviewResponseData);

        UgcReviewRequest ugcReviewRequest = new UgcReviewRequest();
        ugcReviewRequest.setHotelId("");
        ugcReviewRequest.setAvailableOTA(Arrays.asList(MMT, EXP));
        ugcReviewRequest.setNextOTA(MMT);
        ugcReviewRequest.setDeviceDetails(new DeviceDetails());
        ResponseEntity<UgcReviewResponseData> resp = detailController.getUGCReviews(ugcReviewRequest, request, response, "","", "2", "android", "IN");
        Assert.assertEquals(HttpStatus.OK, resp.getStatusCode());
        Assert.assertNull(Objects.requireNonNull(resp.getBody()).getError());
    }

    @Test
    public void UgcUpvoteApiTest() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        request.addHeader("tid", "trinatest");
        UpvoteRequest upvoteRequest = new UpvoteRequest();
        ResponseEntity<String> resp = platformUgcController.UgcUpvote("android", "2", "","", upvoteRequest, "IN", "MMT", request, response);
        Assert.assertEquals(HttpStatus.OK, resp.getStatusCode());
        Assert.assertNotNull(Objects.requireNonNull(resp.getBody()));
    }
}
