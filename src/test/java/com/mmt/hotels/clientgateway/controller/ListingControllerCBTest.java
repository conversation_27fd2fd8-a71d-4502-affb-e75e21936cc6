package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.service.LandingService;
import com.mmt.hotels.clientgateway.service.ListingService;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.pojo.request.landing.HotelLandingMobRequestBody;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RunWith(MockitoJUnitRunner.class)
public class ListingControllerCBTest {
    @InjectMocks
    ListingControllerCB listingControllerCB;

    @Mock
    ListingService listingService;

    @Spy
    private RequestHandler requestHandler;

    @Mock
    private LandingService landingService;

    @Mock
    private MetricAspect metricAspect;

    @Mock
    private MetricErrorLogger metricErrorLogger;


    @Test
    public void testGetCityDataFromLatLngOrPlaceID() throws ClientGatewayException{
        HttpServletRequest httpRequest = new MockHttpServletRequest();

        Mockito.when(landingService.getLatLngFromGooglePlaceId(Mockito.anyString(),
                Mockito.any(), Mockito.any())).thenReturn("");

        String resp = listingControllerCB.getCityDataFromLatLngOrPlaceID(httpRequest,new MockHttpServletResponse(),
                "placeId", null, null, "","");
        Assert.assertNotNull(resp);

        //Exception case
        Mockito.when(landingService.getLatLngFromGooglePlaceId(Mockito.anyString(),
                Mockito.any(), Mockito.any())).thenThrow(new RuntimeException());
        resp = listingControllerCB.getCityDataFromLatLngOrPlaceID(httpRequest,new MockHttpServletResponse(),
                "placeId", null, null, "","");
        Assert.assertNotNull(resp);
    }

    @Test
    public void testGetCityDataFromLatLngOrPlaceIDInValidRequest() throws ClientGatewayException{
        HttpServletRequest httpRequest = new MockHttpServletRequest();
        String resp = listingControllerCB.getCityDataFromLatLngOrPlaceID(httpRequest,new MockHttpServletResponse(),
                null, null, null, "","");
        Assert.assertNotNull(resp);
        Assert.assertTrue(resp.contains("Bad Request"));
    }

    @Test
    public void testGetFetchCollection() throws ClientGatewayException {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(listingService.fetchCollectionsOld(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("test");
        String collectionResponse = listingControllerCB.getFetchCollection(new SearchWrapperInputRequest(),
                "test","INR",null,null,null,request,response);
        Assert.assertNotNull(collectionResponse);
        collectionResponse = listingControllerCB.getFetchCollection(new SearchWrapperInputRequest(),
                "test","INR",null,"test","test",request,response);
        Assert.assertNotNull(collectionResponse);
        collectionResponse = listingControllerCB.getFetchCollection(new SearchWrapperInputRequest(),
                "test","INR",null,null,null,request,response);
        Assert.assertNotNull(collectionResponse);
        collectionResponse = listingControllerCB.getFetchCollection(new SearchWrapperInputRequest(),
                "","INR",null,null,null,request,response);
        Assert.assertNotNull(collectionResponse);
    }

    @Test
    public void searchResultTest() throws ClientGatewayException {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(listingService.searchHotelsOld(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn("test");
        String responseStr = listingControllerCB.getSearchResult(new SearchWrapperInputRequest(),
                "test","test",false,"android",request,response);
        Assert.assertNotNull(responseStr);
        responseStr = listingControllerCB.getSearchResult(new SearchWrapperInputRequest(),
                "","",false,"android",request,response);
        Assert.assertNotNull(responseStr);
        Mockito.when(listingService.searchHotelsOld(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenThrow(new ClientGatewayException());
        responseStr = listingControllerCB.getSearchResult(new SearchWrapperInputRequest(),
                "test","",false,"android",request,response);
        Assert.assertNotNull(responseStr);
    }

    @Test
    public void listingMapTest() throws ClientGatewayException {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(listingService.listingMapOld(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("test");
        String responseStr = listingControllerCB.getSearchResultListingMap(new SearchWrapperInputRequest(),
                "test","test",request,response);
        Assert.assertNotNull(responseStr);
        responseStr = listingControllerCB.getSearchResultListingMap(new SearchWrapperInputRequest(),
                "","",request,response);
        Assert.assertNotNull(responseStr);
        Mockito.when(listingService.listingMapOld(Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(new ClientGatewayException());
        responseStr = listingControllerCB.getSearchResultListingMap(new SearchWrapperInputRequest(),
                "test","test",request,response);
        Assert.assertNotNull(responseStr);
    }

    @Test
    public void filterCountTest() throws ClientGatewayException {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(listingService.filterCountOld(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("test");
        String responseStr = listingControllerCB.getFilterCount(new SearchWrapperInputRequest(),
                "test","test","","","",true,request,response);
        Assert.assertNotNull(responseStr);
        responseStr = listingControllerCB.getFilterCount(new SearchWrapperInputRequest(),
                "", "","","","",true,request,response);
        Assert.assertNotNull(responseStr);
        Mockito.when(listingService.filterCountOld(Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(new ClientGatewayException());
        responseStr = listingControllerCB.getFilterCount(new SearchWrapperInputRequest(),
                "test", "","","","",true,request,response);
        Assert.assertNotNull(responseStr);
    }
    

    @Test
    public void getHotelMobConfigTest() throws ClientGatewayException {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(listingService.getMobConfig(Mockito.any(), Mockito.any())).thenReturn("test");
        String responseStr = listingControllerCB.getHotelMobConfig(request,response);
        Assert.assertNotNull(responseStr);
        Mockito.when(listingService.getMobConfig(Mockito.any(), Mockito.any())).thenThrow(new ClientGatewayException());
        responseStr = listingControllerCB.getHotelMobConfig(request,response);
        Assert.assertNotNull(responseStr);
    }
    
    @Test
    public void testGetHotelsMetadataByCity() throws ClientGatewayException{
        HttpServletRequest httpRequest = new MockHttpServletRequest();
        HttpServletResponse httpResponse = new MockHttpServletResponse();
    	Mockito.when(listingService.getMetaDataByCityResponse(Mockito.any(), Mockito.any(),
    			Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyMap())).thenReturn("");
    	String resp = listingControllerCB.getHotelsMetadataByCity(httpRequest, httpResponse, "cityId",
    			"filterCode", "priceBucketCriteria", "filterCriteria", null, null,
    			"IN", null, "INR", null, null, null,null, "PWA", null, null);
    	Assert.assertNotNull(resp);
    	
    	//Exception
       	Mockito.when(listingService.getMetaDataByCityResponse(Mockito.any(), Mockito.any(),
    			Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyMap())).thenThrow(new RuntimeException());
    	listingControllerCB.getHotelsMetadataByCity(httpRequest, httpResponse, "cityId",
    			"filterCode", "priceBucketCriteria", "filterCriteria", null, null,
    			"IN", null, "INR", null, null, null,null, "PWA", null, null);
    	Assert.assertNotNull(resp);
    }

    @Test
    public void testGetListingPersonalizedCardData() throws ClientGatewayException {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(listingService.listingPersonalizationOld(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("test");
        HotelLandingMobRequestBody req = new HotelLandingMobRequestBody();
        req.setHotelSearchRequest(new SearchWrapperInputRequest());
        String responseStr = listingControllerCB.getListingPersonalizedCardData(req,
                request,response,"test","test","test","test","test","test","test");
        Assert.assertNotNull(responseStr);
        responseStr = listingControllerCB.getListingPersonalizedCardData(req,
                request,response,"test","test","test","test","test","test","test");
        Assert.assertNotNull(responseStr);
    }

    @Test
    public void testGetNearBySearchResult() throws ClientGatewayException {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(listingService.nearByOld(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("test");
        String responseStr = listingControllerCB.getNearBySearchResult(new SearchWrapperInputRequest(),
                "test","test","test","test","test","test",request,response);
        Assert.assertNotNull(responseStr);
        responseStr = listingControllerCB.getNearBySearchResult(new SearchWrapperInputRequest(),
                "test","test","test","test","test","test",request,response);
        Assert.assertNotNull(responseStr);
    }

    @Test
    public void landingSearchResultTest() throws ClientGatewayException {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(listingService.landingDiscoveryOld(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("test");
        String responseStr = listingControllerCB.getLandingDiscoveryResult(new SearchWrapperInputRequest(),
                "test","test",false,"android",request,response);
        Assert.assertNotNull(responseStr);
        responseStr = listingControllerCB.getLandingDiscoveryResult(new SearchWrapperInputRequest(),
                "","",false,"android",request,response);
        Assert.assertNotNull(responseStr);
        Mockito.when(listingService.landingDiscoveryOld(Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(new ClientGatewayException());
        responseStr = listingControllerCB.getLandingDiscoveryResult(new SearchWrapperInputRequest(),
                "test","test",false,"android",request,response);
        Assert.assertNotNull(responseStr);
    }
}
