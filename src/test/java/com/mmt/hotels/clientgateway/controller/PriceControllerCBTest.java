package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.service.*;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.ValidateCouponRequestBody;
import com.mmt.hotels.model.request.addon.GetAddonsRequest;
import com.mmt.hotels.model.request.emi.UpdateEmiDetailRequest;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.response.detail.upsell.UpsellSimilarResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RunWith(MockitoJUnitRunner.class)
public class PriceControllerCBTest {
    @InjectMocks
    PriceControllerCB priceControllerCB;

    @Mock
    DetailService detailService;

    @Mock
    ReviewService reviewService;

    @Mock
    private MetricAspect metricAspect;

    @Mock
    private EmiService emiService;

    @Mock
    DiscountService discountService;

    @Spy
    private MDCHelper mdcHelper;

    @Spy
    private RequestHandler requestHandler;

    @Mock
    private AddonsService addonsService;

    @Test
    public void testGetPriceDetailsByHotels() throws ClientGatewayException {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(detailService.searchPriceOld(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("test");
        String roomDetailsResponse = priceControllerCB.getPriceDetailsByHotels(new PriceByHotelsRequestBody(),
                "test","INR","EN","123","123",request,response);
        Assert.assertNotNull(roomDetailsResponse);
        roomDetailsResponse = priceControllerCB.getPriceDetailsByHotels(new PriceByHotelsRequestBody(),
                "test","INR","EN","","",request,response);
        Assert.assertNotNull(roomDetailsResponse);
        Mockito.when(detailService.searchPriceOld(Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(new ClientGatewayException());
        roomDetailsResponse = priceControllerCB.getPriceDetailsByHotels(new PriceByHotelsRequestBody(),
                "test","INR","EN","","",request,response);
        Assert.assertNotNull(roomDetailsResponse);

    }
    
    @Test
    public void testGetAlternateDatesPriceResponse() throws ClientGatewayException {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(detailService.alternateDatesPriceOld(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("test");
        String alternateDatesResponse = priceControllerCB.getAlternateDatesPriceResponse(new PriceByHotelsRequestBody(),
                "test","INR","EN","123","123",request,response);
        Assert.assertNotNull(alternateDatesResponse);
        alternateDatesResponse = priceControllerCB.getAlternateDatesPriceResponse(new PriceByHotelsRequestBody(),
                "test","INR","EN","","",request,response);
        Assert.assertNotNull(alternateDatesResponse);
        Mockito.when(detailService.alternateDatesPriceOld(Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(new ClientGatewayException());
        alternateDatesResponse = priceControllerCB.getAlternateDatesPriceResponse(new PriceByHotelsRequestBody(),
                "test","INR","EN","","",request,response);
        Assert.assertNotNull(alternateDatesResponse);

    }


    @Test
    public void testGetAvailPrice() throws ClientGatewayException {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(reviewService.getAvailPriceOld(Mockito.any(),Mockito.any(), Mockito.any())).thenReturn("test");
        String availResponse = priceControllerCB.getAvailPrice(new PriceByHotelsRequestBody(),
                "test","INR","EN","123","123",request,response);
        Assert.assertNotNull(availResponse);
        Mockito.when(reviewService.getAvailPriceOld(Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(new ClientGatewayException());
        availResponse = priceControllerCB.getAvailPrice(new PriceByHotelsRequestBody(),
                "test","INR","EN","","",request,response);
        Assert.assertNotNull(availResponse);
    }
    
    @Test
    public void testGetUpdatedPricingByApi() throws ClientGatewayException {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        Mockito.when(reviewService.getUpdatedPriceOccuLessOld(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("test");
        String updatedPriceResponse = priceControllerCB.getUpdatedPriceOccuLess(new PriceByHotelsRequestBody(),
                "test","INR","EN","123","123",request,response);
        Assert.assertNotNull(updatedPriceResponse);
        Mockito.when(reviewService.getUpdatedPriceOccuLessOld(Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(new ClientGatewayException());
        updatedPriceResponse = priceControllerCB.getUpdatedPriceOccuLess(new PriceByHotelsRequestBody(),
                "test","INR","EN","","",request,response);
        Assert.assertNotNull(updatedPriceResponse);
    }



    @Test
    public void getUpdatedEmiTest() throws Exception {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        Mockito.when(emiService.getUpdateEmiResponse(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("emiResponse");
        String rsp = priceControllerCB.getUpdatedEmi(new UpdateEmiDetailRequest(), "","", request, response);
        Assert.assertNotNull(rsp);
        rsp = priceControllerCB.getUpdatedEmi(new UpdateEmiDetailRequest(), "123","123", request, response);
        Assert.assertNotNull(rsp);
        Mockito.when(emiService.getUpdateEmiResponse(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(new ClientGatewayException());
        rsp = priceControllerCB.getUpdatedEmi(new UpdateEmiDetailRequest(), "","", request, response);
        Assert.assertNotNull(rsp);
    }

    @Test
    public void getAddonsTest() throws Exception {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        Mockito.when(addonsService.getAddons(Mockito.any(GetAddonsRequest.class), Mockito.any(), Mockito.any())).thenReturn("emiResponse");
        String rsp = priceControllerCB.getAddOnsForHotels(new GetAddonsRequest(), "","","","","", request, response);
        Assert.assertNotNull(rsp);
        Mockito.when(addonsService.getAddons(Mockito.any(GetAddonsRequest.class), Mockito.any(), Mockito.any())).thenThrow(new ClientGatewayException());
         rsp = priceControllerCB.getAddOnsForHotels(new GetAddonsRequest(), "IN","","","","", request, response);
        Assert.assertNotNull(rsp);
    }

    @Test
    public void getUpsellHotelsTest() {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        UpsellSimilarResponse rsp = priceControllerCB.getUpsellApiResponse(new HotelDetailsMobRequestBody(), "","","","","", request, response);
        Assert.assertNotNull(rsp);
        Assert.assertNotNull(rsp.getErrorEntity());
        Assert.assertEquals(rsp.getErrorEntity().getErrorCode(), CBError.HOTEL_UPSELL_API_ERROR.getCode());

    }
}
