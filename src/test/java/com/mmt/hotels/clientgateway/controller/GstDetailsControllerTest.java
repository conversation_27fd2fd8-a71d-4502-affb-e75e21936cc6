package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.BaseSearchRequest;
import com.mmt.hotels.clientgateway.request.gstDetails.SaveGstDetailsRequest;
import com.mmt.hotels.clientgateway.request.gstDetails.TravellerGstDetails;
import com.mmt.hotels.clientgateway.response.gstDetails.GstDetailsResponse;
import com.mmt.hotels.clientgateway.service.GstDetailsService;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.util.Tuple;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class GstDetailsControllerTest {

    @InjectMocks
    private GstDetailsController gstDetailsController;

    @Mock
    private RequestHandler requestHandler;

    @Mock
    private GstDetailsService gstDetailsService;

    @Mock
    private MetricAspect metricAspect;

    @Before
    public void init() {
        Mockito.when(requestHandler.handleCommonRequest(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(new Tuple<>("abcd", new HashMap<>()));
    }

    @Test
    public void testSaveGstDetails() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        request.addHeader(Constants.REQUEST_IDENTIFIER, "testRequestId");

        SaveGstDetailsRequest saveGstDetailsRequest = new SaveGstDetailsRequest();
        GstDetailsResponse<TravellerGstDetails> gstDetailsResponse = new GstDetailsResponse<>();
        gstDetailsResponse.setCorrelationKey("ck");

        Mockito.when(gstDetailsService.saveGstDetails(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString()))
                .thenReturn(gstDetailsResponse);

        ResponseEntity<GstDetailsResponse<TravellerGstDetails>> resp = gstDetailsController.saveGstDetails(
                Constants.CLIENT_DESKTOP, "2", saveGstDetailsRequest, request, response, null, "testRequestId");

        Assert.assertEquals(HttpStatus.OK, resp.getStatusCode());
        Assert.assertNull(resp.getBody().getError());
    }

    @Test
    public void testGetGstDetails() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        request.addHeader(Constants.REQUEST_IDENTIFIER, "testRequestId");

        BaseSearchRequest getGstDetailsRequest = new BaseSearchRequest();
        GstDetailsResponse<List<TravellerGstDetails>> gstDetailsResponse = new GstDetailsResponse<>();
        gstDetailsResponse.setCorrelationKey("ck");

        Mockito.when(gstDetailsService.getGstDetails(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString()))
                .thenReturn(gstDetailsResponse);

        ResponseEntity<GstDetailsResponse<List<TravellerGstDetails>>> resp = gstDetailsController.getGstDetails(
                Constants.CLIENT_DESKTOP, "2", getGstDetailsRequest, request, response, null, "testRequestId");

        Assert.assertEquals(HttpStatus.OK, resp.getStatusCode());
        Assert.assertNull(resp.getBody().getError());
    }

}