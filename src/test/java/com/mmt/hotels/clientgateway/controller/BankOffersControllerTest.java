package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.BankOffersRequestCG;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.response.BankOffersResponseCG;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.BankOffersService;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.clientgateway.util.Utility;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RunWith(MockitoJUnitRunner.class)
public class BankOffersControllerTest {

    @InjectMocks
    private BankOffersController bankOffersController;

    @Mock
    private BankOffersService bankOffersService;

    @Mock
    private MetricAspect metricAspect;

    @Mock
    private Utility utility;

    @Spy
    private RequestHandler requestHandler;

    @Test
    public void bankOffersAPITest() throws Exception {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid", "trinatest");
        Mockito.lenient().when(bankOffersService.bankOffers(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new BankOffersResponseCG());
        BankOffersRequestCG bankOffersRequestCG = new BankOffersRequestCG();
        bankOffersRequestCG.setDeltaDays(1);
        bankOffersRequestCG.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<BankOffersResponseCG>> resp = bankOffersController.bankOffers("PWA", "1.0.0", bankOffersRequestCG, request, response, Mockito.any(), Mockito.any());
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());
    }

    @Test(expected = ClientGatewayException.class)
    public void bankOffersAPITest_withoutDeltaDaysParameter() throws Exception {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid", "trinatest");
        Mockito.lenient().when(bankOffersService.bankOffers(Mockito.any(), Mockito.anyMap(), Mockito.anyMap())).thenReturn(new BankOffersResponseCG());
        BankOffersRequestCG bankOffersRequestCG = new BankOffersRequestCG();
        bankOffersRequestCG.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<BankOffersResponseCG>> resp = bankOffersController.bankOffers("PWA", "1.0.0", bankOffersRequestCG, request, response, "", "");
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());
    }

}
