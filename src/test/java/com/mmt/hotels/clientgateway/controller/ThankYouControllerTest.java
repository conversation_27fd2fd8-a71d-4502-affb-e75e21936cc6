package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.ThankYouRequest;
import com.mmt.hotels.clientgateway.response.thankyou.ThankYouResponse;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.ThankYouService;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.util.Tuple;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class ThankYouControllerTest {

    @InjectMocks
    ThankyouController thankyouController;

    @Mock
    private RequestHandler requestHandler;

    @Mock
    private ThankYouService thankYouService;

    @Mock
    private MetricAspect metricAspect;

    @Before
    public void init() {
        Mockito.when(requestHandler.handleCommonRequest(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new Tuple<>("abcd", new HashMap<>()));
    }
    @Test
    public void testThankYouGet() throws ClientGatewayException {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid", "test");
        Mockito.when(thankYouService.getThankYouResponse(Mockito.any(),Mockito.anyMap(),Mockito.anyMap())).thenReturn(new ThankYouResponse());
        ResponseEntity<ResponseWrapper<ThankYouResponse>> resp = thankyouController.thankYouGet("PWA","1.0.0", new ThankYouRequest(),request,response, null,null);
        Assert.assertEquals(HttpStatus.OK, resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());
    }
}
