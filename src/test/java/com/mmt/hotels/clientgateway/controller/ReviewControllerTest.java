package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.payLater.PayLaterEligibilityRequest;
import com.mmt.hotels.clientgateway.request.ugc.ClientLoadProgramRequest;
import com.mmt.hotels.clientgateway.request.ugc.ClientSubmitApiRequest;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.payLater.PayLaterEligibilityResponse;
import com.mmt.hotels.clientgateway.response.ugc.ClientUgcResponse;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.*;
import com.mmt.hotels.clientgateway.util.CustomValidator;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.ArgumentCaptor;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class ReviewControllerTest {

    @InjectMocks
    private ReviewController reviewController;

    @Mock
    private ReviewService reviewService;

    @Mock
    private UgcService ugcService;

    @Spy
    private MDCHelper mdcHelper;

    @Mock
    private MetricAspect metricAspect;

    @Spy
    private CustomValidator customValidator;
    
    @Spy
    private RequestHandler requestHandler;

    @Mock
    private DiscountService discountService;

    @Mock
    private AddonsService addonsService;

    @Mock
    private PolicyService policyService;

    @Mock
    private RoomInfoService roomInfoService;

    @Test
    public  void availRoomsAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        Mockito.when(reviewService.availRooms(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new AvailRoomsResponse());
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<AvailRoomsResponse>>  resp = reviewController.availRooms("PWA","1.0.0",availRoomsRequest, request, response, null,null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public  void availRoomsV2APITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        Mockito.when(reviewService.availRooms(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new AvailRoomsResponse());
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<AvailRoomsResponse>>  resp = reviewController.availRoomsV2("PWA","1.0.0",availRoomsRequest, request, response, null,null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public void testValidateCoupon() throws ClientGatewayException {
        HttpServletRequest httpServletRequest = new MockHttpServletRequest();
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();
        ValidateCouponRequest validateCouponRequest = new ValidateCouponRequest();
        Mockito.when(discountService.validateCoupon(Mockito.any(),Mockito.any(),Mockito.anyMap(),Mockito.anyString()))
                .thenReturn(new ValidateCouponResponseBody());
        ResponseEntity<ResponseWrapper<ValidateCouponResponseBody>> resp = reviewController.validateCoupon(validateCouponRequest, "PWA", "1",
                httpServletRequest, httpServletResponse, null,null);
        Assert.assertNotNull(resp.getBody());
    }

    @Test
    public void testSearchAddons() throws ClientGatewayException {
		/*Mockito.doNothing().when(requestHandler).validatHeadersAndCreateMDC(Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any());*/
        Mockito.when(addonsService.getAddons(Mockito.any(SearchAddonsRequest.class),Mockito.anyMap(),Mockito.any())).thenReturn(new SearchAddonsResponse());
        SearchAddonsRequest searchAddonsRequest = new SearchAddonsRequest();
        searchAddonsRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<SearchAddonsResponse>> respEntity = reviewController.searchAddons(
                "PWA", "1",searchAddonsRequest ,new MockHttpServletRequest(), new MockHttpServletResponse(), null,null);
        Assert.assertNotNull(respEntity.getBody());
    }


    @Test
    public  void totalPricingAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        Mockito.when(reviewService.getTotalPrice(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new TotalPriceResponse());
        ResponseEntity<ResponseWrapper<TotalPriceResponse>>  resp = reviewController.getTotalPrice(new TotalPricingRequest(),"PWA","1.0.0", request, response, null,null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public void testGetPolicies() throws ClientGatewayException {
        PoliciesRequest policiesRequest = new PoliciesRequest();
        Mockito.doNothing().when(requestHandler).validatHeadersAndCreateMDC(Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any());
        Mockito.when(policyService.getPolicies(Mockito.any(),Mockito.any())).thenReturn(new PoliciesResponse());
        ResponseEntity<PoliciesResponse> responseEntity = reviewController
                .getPolicies("PWA", "1", policiesRequest,new MockHttpServletRequest(), new MockHttpServletResponse(), null,null);
        Assert.assertNotNull(responseEntity.getBody());
    }

    @Test
    public void testRoomInfo() throws ClientGatewayException {
        RoomInfoRequest roomInfoRequest = new RoomInfoRequest();
        Mockito.doNothing().when(requestHandler).validatHeadersAndCreateMDC(Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any());
        Mockito.when(roomInfoService.getRoomInfo(Mockito.any(),Mockito.any())).thenReturn(new RoomInfoResponse());
        ResponseEntity<RoomInfoResponse> responseEntity = reviewController
                .getRoomInfo("PWA", "1", roomInfoRequest,new MockHttpServletRequest(), new MockHttpServletResponse(), null,null);
        Assert.assertNotNull(responseEntity.getBody());
    }

    @Test
    public void testRoomInfos() throws ClientGatewayException {
        RoomInfoRequest roomInfoRequest = new RoomInfoRequest();
        Mockito.doNothing().when(requestHandler).validatHeadersAndCreateMDC(Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any());
        Mockito.when(roomInfoService.getRoomInfos(Mockito.any(),Mockito.any())).thenReturn(new RoomInfoResponse());
        ResponseEntity<ResponseWrapper<RoomInfoResponse>> responseEntity = reviewController
                .getRoomInfos("PWA", "1", roomInfoRequest, new MockHttpServletRequest(), new MockHttpServletResponse(), null,null);
        Assert.assertNotNull(responseEntity.getBody());
    }

    @Test
    public  void payLaterEligibilityAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        Mockito.when(reviewService.fetchPayLaterEligibility(Mockito.any(),Mockito.any(),Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new PayLaterEligibilityResponse());
        PayLaterEligibilityRequest payLaterEligibilityRequest = new PayLaterEligibilityRequest();
        ResponseEntity<ResponseWrapper<PayLaterEligibilityResponse>> resp = reviewController.checkPayLaterEligibility(payLaterEligibilityRequest, "android",
                "2", request, response, null,null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public  void fetchlocationsTest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        Mockito.when(reviewService.fetchLocations(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new FetchLocationsResponse());
        FetchLocationsRequest fetchLocationsRequest = new FetchLocationsRequest();
        ResponseEntity<ResponseWrapper<FetchLocationsResponse>> resp = reviewController.fetchLocations(fetchLocationsRequest, "android",
                "2", request, response, null,null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());
    }

    @Test
    public  void ugcLoadProgramTest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        Mockito.when(ugcService.fetchProgram18Question(Mockito.any(),Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new ClientUgcResponse());
        ClientLoadProgramRequest clientLoadProgramRequest = new ClientLoadProgramRequest();
        ResponseEntity<ResponseWrapper<ClientUgcResponse>> resp = reviewController.loadProgram("android",clientLoadProgramRequest, request, response, null,null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());
    }

    @Test
    public void ugcSubmitAnswersTest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        Mockito.when(ugcService.submitAnswers(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(), Mockito.any())).thenReturn(new ClientUgcResponse());
        ClientSubmitApiRequest clientSubmitApiRequest = new ClientSubmitApiRequest();
        MultipartRequest multipartRequest = new MultipartRequest() {
            @Override
            public Iterator<String> getFileNames() {
                return null;
            }

            @Override
            public MultipartFile getFile(String s) {
                return null;
            }

            @Override
            public List<MultipartFile> getFiles(String s) {
                return null;
            }

            @Override
            public Map<String, MultipartFile> getFileMap() {
                return null;
            }

            @Override
            public MultiValueMap<String, MultipartFile> getMultiFileMap() {
                return null;
            }

            @Override
            public String getMultipartContentType(String s) {
                return null;
            }
        };
        ResponseEntity<ResponseWrapper<ClientUgcResponse>> resp = reviewController.submitAnswers("android",clientSubmitApiRequest, multipartRequest, request,response, null,null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());
    }

    @Test
    public void validateRequest_deduplicatesRepeatedHalves() throws Exception {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        Mockito.when(ugcService.submitAnswers(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(new ClientUgcResponse());

        // Prepare request with duplicated halves text
        ClientSubmitApiRequest clientSubmitApiRequest = new ClientSubmitApiRequest();
        com.mmt.hotels.clientgateway.request.ugc.QuestionSet questionSet = new com.mmt.hotels.clientgateway.request.ugc.QuestionSet();
        questionSet.setText("repeatrepeat"); // halves: "repeat" and "repeat"
        clientSubmitApiRequest.setQuestion(questionSet);

        MultipartRequest multipartRequest = new MultipartRequest() {
            @Override
            public Iterator<String> getFileNames() { return null; }
            @Override
            public org.springframework.web.multipart.MultipartFile getFile(String s) { return null; }
            @Override
            public List<org.springframework.web.multipart.MultipartFile> getFiles(String s) { return null; }
            @Override
            public Map<String, org.springframework.web.multipart.MultipartFile> getFileMap() { return null; }
            @Override
            public org.springframework.util.MultiValueMap<String, org.springframework.web.multipart.MultipartFile> getMultiFileMap() { return null; }
            @Override
            public String getMultipartContentType(String s) { return null; }
        };

        ResponseEntity<ResponseWrapper<ClientUgcResponse>> resp = reviewController.submitAnswers(
                "android", clientSubmitApiRequest, multipartRequest, request, response, null, null);

        Assert.assertEquals(HttpStatus.OK, resp.getStatusCode());

        // Capture the request passed to service to ensure mutation took effect
        ArgumentCaptor<ClientSubmitApiRequest> reqCaptor = ArgumentCaptor.forClass(ClientSubmitApiRequest.class);
        Mockito.verify(ugcService).submitAnswers(reqCaptor.capture(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
        Assert.assertEquals("repeat", reqCaptor.getValue().getQuestion().getText());
        // Also the original object should be mutated
        Assert.assertEquals("repeat", clientSubmitApiRequest.getQuestion().getText());
    }

    @Test
    public void validateRequest_keepsTextWhenHalvesDiffer() throws Exception {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        Mockito.when(ugcService.submitAnswers(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(new ClientUgcResponse());

        ClientSubmitApiRequest clientSubmitApiRequest = new ClientSubmitApiRequest();
        com.mmt.hotels.clientgateway.request.ugc.QuestionSet questionSet = new com.mmt.hotels.clientgateway.request.ugc.QuestionSet();
        questionSet.setText("abcxyz"); // halves differ: "abc" vs "xyz"
        clientSubmitApiRequest.setQuestion(questionSet);

        MultipartRequest multipartRequest = new MultipartRequest() {
            @Override
            public Iterator<String> getFileNames() { return null; }
            @Override
            public org.springframework.web.multipart.MultipartFile getFile(String s) { return null; }
            @Override
            public List<org.springframework.web.multipart.MultipartFile> getFiles(String s) { return null; }
            @Override
            public Map<String, org.springframework.web.multipart.MultipartFile> getFileMap() { return null; }
            @Override
            public org.springframework.util.MultiValueMap<String, org.springframework.web.multipart.MultipartFile> getMultiFileMap() { return null; }
            @Override
            public String getMultipartContentType(String s) { return null; }
        };

        ResponseEntity<ResponseWrapper<ClientUgcResponse>> resp = reviewController.submitAnswers(
                "android", clientSubmitApiRequest, multipartRequest, request, response, null, null);

        Assert.assertEquals(HttpStatus.OK, resp.getStatusCode());

        ArgumentCaptor<ClientSubmitApiRequest> reqCaptor = ArgumentCaptor.forClass(ClientSubmitApiRequest.class);
        Mockito.verify(ugcService).submitAnswers(reqCaptor.capture(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
        Assert.assertEquals("abcxyz", reqCaptor.getValue().getQuestion().getText());
        Assert.assertEquals("abcxyz", clientSubmitApiRequest.getQuestion().getText());
    }
}
