package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.EMIDetailRequest;
import com.mmt.hotels.clientgateway.response.emi.EMIDetailResponse;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.EmiService;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.util.Tuple;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.util.Collections;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class EmiControllerTest {

    @InjectMocks
    private EmiController emiController;

    @Mock
    private RequestHandler requestHandler;

    @Mock
    private EmiService emiService;

    @Mock
    private MetricAspect metricAspect;

    @Mock
    private HttpServletRequest httpServletRequest;

    @Mock
    private HttpServletResponse httpServletResponse;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testFetchEmiDetails() throws ClientGatewayException {
        // Prepare test data
        String client = "testClient";
        String version = "v1.0";
        EMIDetailRequest emiDetailRequest = new EMIDetailRequest();
        String ck = "correlationKey";
        String brand = Constants.BRAND_MMT;

        Map<String, String> headersMap = Collections.emptyMap();
        Tuple<String, Map<String, String>> tuple = new Tuple<>(ck, headersMap);

        EMIDetailResponse emiDetailResponse = new EMIDetailResponse();
        ResponseWrapper<EMIDetailResponse> expectedResponseWrapper = new ResponseWrapper<>();
        expectedResponseWrapper.setResponse(emiDetailResponse);
        expectedResponseWrapper.setCorrelationKey(ck);

        Mockito.when(requestHandler.handleCommonRequest(Mockito.any(HttpServletRequest.class), Mockito.any(HttpServletResponse.class), Mockito.anyString(),
                Mockito.anyString(), Mockito.any(), Mockito.anyString(), Mockito.any())).thenReturn(tuple);
        Mockito.when(emiService.fetchEmiDetails(Mockito.any(EMIDetailRequest.class))).thenReturn(emiDetailResponse);

        // Perform the test
        ResponseEntity<ResponseWrapper<EMIDetailResponse>> responseEntity = emiController.fetchEmiDetails(client, version, emiDetailRequest, httpServletRequest, httpServletResponse, ck, brand);

        // Verify the results
        assertEquals(expectedResponseWrapper, responseEntity.getBody());

        verify(requestHandler).handleCommonRequest(Mockito.any(HttpServletRequest.class), Mockito.any(HttpServletResponse.class),
                Mockito.anyString(), Mockito.anyString(), Mockito.any(), Mockito.anyString(), Mockito.any());
        verify(requestHandler).validatHeadersAndCreateMDC(Mockito.any(HttpServletRequest.class), Mockito.anyString(), Mockito.any(), Mockito.anyString());
        verify(emiService).fetchEmiDetails(Mockito.any(EMIDetailRequest.class));
    }

}