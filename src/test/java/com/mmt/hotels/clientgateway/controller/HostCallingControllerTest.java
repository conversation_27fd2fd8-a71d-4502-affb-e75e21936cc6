package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.request.hostcalling.HostCallingInitiateRequestBody;
import com.mmt.hotels.clientgateway.response.hostcalling.HostCallingInitiateResponse;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.HostCallingService;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.util.Tuple;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class HostCallingControllerTest {

    @InjectMocks
    private HostCallingController hostCallingController;

    @Mock
    private HostCallingService hostCallingService;

    @Mock
    private RequestHandler requestHandler;

    @Mock
    private MetricAspect metricAspect;

    private HostCallingInitiateRequestBody mockRequest;
    private HostCallingInitiateResponse mockSuccessResponse;
    private MockHttpServletRequest httpServletRequest;
    private MockHttpServletResponse httpServletResponse;
    private Map<String, String> mockHeaderMap;

    @Before
    public void setUp() {
        // Setup mock request - using mocks since the real object has complex structure
        mockRequest = mock(HostCallingInitiateRequestBody.class);
        
        // Mock RequestDetails (used by getRequestDetails())
        RequestDetails mockRequestDetails = mock(RequestDetails.class);
        when(mockRequestDetails.getIdContext()).thenReturn("B2C");
        when(mockRequest.getRequestDetails()).thenReturn(mockRequestDetails);
        
        // Add setClient method mock to avoid NullPointerException
        doNothing().when(mockRequest).setClient(anyString());

        // Setup mock success response
        mockSuccessResponse = new HostCallingInitiateResponse();
        mockSuccessResponse.setStatus("success");
        mockSuccessResponse.setRequestId("REQ123");
        mockSuccessResponse.setChainName("Test Chain");
        mockSuccessResponse.setAvailableNow(true);
        mockSuccessResponse.setStartTime("09:00");
        mockSuccessResponse.setEndTime("21:00");
        mockSuccessResponse.setMissedCallMessage("You will receive a call from Test Chain soon");

        // Setup HTTP request/response
        httpServletRequest = new MockHttpServletRequest();
        httpServletRequest.addHeader("tid", "test-transaction-id");
        httpServletRequest.addHeader("mmt-auth", "test-auth-token");

        httpServletResponse = new MockHttpServletResponse();

        // Setup header map
        mockHeaderMap = new HashMap<>();
        mockHeaderMap.put("tid", "test-transaction-id");
        mockHeaderMap.put("mmt-auth", "test-auth-token");

        // Setup common mocks - Note: Individual tests will mock RequestHandler methods as needed
    }

    @After
    public void tearDown() {
        MDC.clear();
    }

    private void setupRequestHandlerMocks() {
        Tuple<String, Map<String, String>> mockTuple = new Tuple<>("TEST_CORRELATION", mockHeaderMap);
        when(requestHandler.effectiveCorrelationKey(any(), any())).thenReturn("TEST_CORRELATION");
        when(requestHandler.handleCommonRequest(
            any(HttpServletRequest.class),
            any(HttpServletResponse.class),
            anyString(),
            anyString(),
            anyString(),
            anyString(),
            any()
        )).thenReturn(mockTuple);
    }

    @Test
    public void should_ReturnSuccessResponse_When_ValidRequest() throws ClientGatewayException {
        // Arrange
        setupRequestHandlerMocks();
        when(hostCallingService.initiateHostCalling(
            any(HostCallingInitiateRequestBody.class),
            any(Map.class),
            any(Map.class)
        )).thenReturn(mockSuccessResponse);

        // Act
        ResponseEntity<ResponseWrapper<HostCallingInitiateResponse>> result = 
            hostCallingController.initiateHostCalling(
                "ANDROID", 
                "1.0.0", 
                mockRequest, 
                httpServletRequest, 
                httpServletResponse, 
                "TEST_CK", 
                "TEST_REQUEST_ID"
            );

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertNotNull(result.getBody());
        assertNotNull(result.getBody().getResponse());
        assertNull(result.getBody().getError());
        assertEquals("TEST_CORRELATION", result.getBody().getCorrelationKey());
        assertEquals("success", result.getBody().getResponse().getStatus());
        assertEquals("Test Chain", result.getBody().getResponse().getChainName());

        // Verify service was called with correct parameters
        verify(hostCallingService).initiateHostCalling(
            eq(mockRequest),
            eq(httpServletRequest.getParameterMap()),
            eq(mockHeaderMap)
        );

        // Verify metrics aspect was called
        verify(metricAspect).addToTime(anyLong(), eq("cg/hostCalling/initiate"), any(), any(), eq("ANDROID"), any());
        
        // Verify client was set
        verify(mockRequest).setClient("ANDROID");
    }

    @Test(expected = ErrorResponseFromDownstreamException.class)
    public void should_ThrowErrorResponseFromDownstreamException_When_ServiceThrowsDownstreamError() throws ClientGatewayException {
        // Arrange
        setupRequestHandlerMocks();
        when(hostCallingService.initiateHostCalling(
            any(HostCallingInitiateRequestBody.class),
            any(Map.class),
            any(Map.class)
        )).thenThrow(new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, "ERR001", "Service unavailable"));

        // Act - Should throw ErrorResponseFromDownstreamException
        hostCallingController.initiateHostCalling(
            "ANDROID", 
            "1.0.0", 
            mockRequest, 
            httpServletRequest, 
            httpServletResponse, 
            "TEST_CK", 
            "TEST_REQUEST_ID"
        );
    }

    @Test(expected = ClientGatewayException.class)
    public void should_ThrowException_When_ServiceThrowsException() throws ClientGatewayException {
        // Arrange
        setupRequestHandlerMocks();
        when(hostCallingService.initiateHostCalling(
            any(HostCallingInitiateRequestBody.class),
            any(Map.class),
            any(Map.class)
        )).thenThrow(new ClientGatewayException());

        // Act
        hostCallingController.initiateHostCalling(
            "ANDROID", 
            "1.0.0", 
            mockRequest, 
            httpServletRequest, 
            httpServletResponse, 
            "TEST_CK", 
            "TEST_REQUEST_ID"
        );
    }

    @Test
    public void should_ConvertClientToUppercase_When_ClientIsLowercase() throws ClientGatewayException {
        // Arrange
        setupRequestHandlerMocks();
        when(hostCallingService.initiateHostCalling(
            any(HostCallingInitiateRequestBody.class),
            any(Map.class),
            any(Map.class)
        )).thenReturn(mockSuccessResponse);

        // Act
        ResponseEntity<ResponseWrapper<HostCallingInitiateResponse>> result = 
            hostCallingController.initiateHostCalling(
                "android", // lowercase client
                "1.0.0", 
                mockRequest, 
                httpServletRequest, 
                httpServletResponse, 
                "TEST_CK", 
                "TEST_REQUEST_ID"
            );

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        
        // Verify that client was converted to uppercase when calling RequestHandler
        verify(requestHandler).handleCommonRequest(
            eq(httpServletRequest),
            eq(httpServletResponse),
            eq("TEST_CORRELATION"),
            eq("ANDROID"), // Should be uppercase
            eq("B2C"),
            eq("cg/hostCalling/initiate"),
            eq(mockRequest)
        );
        
        // Verify client was set correctly
        verify(mockRequest).setClient("ANDROID");
    }

    @Test
    public void should_SetCorrelationKey_When_ControllerCreatesResponseWrapper() throws ClientGatewayException {
        // Arrange
        setupRequestHandlerMocks();
        when(hostCallingService.initiateHostCalling(
            any(HostCallingInitiateRequestBody.class),
            any(Map.class),
            any(Map.class)
        )).thenReturn(mockSuccessResponse);

        // Act
        ResponseEntity<ResponseWrapper<HostCallingInitiateResponse>> result = 
            hostCallingController.initiateHostCalling(
                "ANDROID", 
                "1.0.0", 
                mockRequest, 
                httpServletRequest, 
                httpServletResponse, 
                "TEST_CK", 
                "TEST_REQUEST_ID"
            );

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertEquals("TEST_CORRELATION", result.getBody().getCorrelationKey());
        assertNotNull(result.getBody().getResponse());
    }

    @Test
    public void should_HandleEmptyCorrelationKeyAndRequestId_When_ParametersAreEmpty() throws ClientGatewayException {
        // Arrange
        setupRequestHandlerMocks();
        when(hostCallingService.initiateHostCalling(
            any(HostCallingInitiateRequestBody.class),
            any(Map.class),
            any(Map.class)
        )).thenReturn(mockSuccessResponse);

        // Act
        ResponseEntity<ResponseWrapper<HostCallingInitiateResponse>> result = 
            hostCallingController.initiateHostCalling(
                "ANDROID", 
                "1.0.0", 
                mockRequest, 
                httpServletRequest, 
                httpServletResponse, 
                "", // empty correlation key
                "" // empty request id
            );

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        
        // Verify that RequestHandler was called to handle empty parameters
        verify(requestHandler).effectiveCorrelationKey("", "");
    }

    @Test
    public void should_HandleDifferentClientTypes_When_CalledWithVariousClients() throws ClientGatewayException {
        // Arrange
        setupRequestHandlerMocks();
        when(hostCallingService.initiateHostCalling(
            any(HostCallingInitiateRequestBody.class),
            any(Map.class),
            any(Map.class)
        )).thenReturn(mockSuccessResponse);

        String[] clients = {"PWA", "ios", "desktop", "mweb"};

        for (String client : clients) {
            // Act
            ResponseEntity<ResponseWrapper<HostCallingInitiateResponse>> result = 
                hostCallingController.initiateHostCalling(
                    client, 
                    "1.0.0", 
                    mockRequest, 
                    httpServletRequest, 
                    httpServletResponse, 
                    "TEST_CK", 
                    "TEST_REQUEST_ID"
                );

            // Assert
            assertNotNull(result);
            assertEquals(HttpStatus.OK, result.getStatusCode());
            
            // Verify metrics were called with uppercase client
            verify(metricAspect).addToTime(anyLong(), eq("cg/hostCalling/initiate"), any(), any(), eq(client.toUpperCase()), any());
        }
    }

    @Test
    public void should_HandleMDCOperations_When_RequestIsProcessed() throws ClientGatewayException {
        // Arrange
        setupRequestHandlerMocks();
        when(hostCallingService.initiateHostCalling(
            any(HostCallingInitiateRequestBody.class),
            any(Map.class),
            any(Map.class)
        )).thenReturn(mockSuccessResponse);

        // Act
        ResponseEntity<ResponseWrapper<HostCallingInitiateResponse>> result = 
            hostCallingController.initiateHostCalling(
                "ANDROID", 
                "1.0.0", 
                mockRequest, 
                httpServletRequest, 
                httpServletResponse, 
                "TEST_CK", 
                "TEST_REQUEST_ID"
            );

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        
        // Verify that metrics call happens in finally block
        verify(metricAspect).addToTime(anyLong(), anyString(), any(), any(), anyString(), any());
    }

    @Test
    public void should_PassCorrectHeaders_When_HttpHeadersArePresent() throws ClientGatewayException {
        // Arrange
        httpServletRequest.addHeader("User-Agent", "TestAgent/1.0");
        httpServletRequest.addHeader("Accept-Language", "en-US");
        
        setupRequestHandlerMocks();
        when(hostCallingService.initiateHostCalling(
            any(HostCallingInitiateRequestBody.class),
            any(Map.class),
            any(Map.class)
        )).thenReturn(mockSuccessResponse);

        // Act
        ResponseEntity<ResponseWrapper<HostCallingInitiateResponse>> result = 
            hostCallingController.initiateHostCalling(
                "ANDROID", 
                "1.0.0", 
                mockRequest, 
                httpServletRequest, 
                httpServletResponse, 
                "TEST_CK", 
                "TEST_REQUEST_ID"
            );

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        
        // Verify that the service was called 
        verify(hostCallingService).initiateHostCalling(
            any(HostCallingInitiateRequestBody.class),
            any(Map.class),
            any(Map.class)
        );
    }

    @Test
    public void should_HandleNullRequestValues_When_OptionalParametersAreNull() throws ClientGatewayException {
        // Arrange
        setupRequestHandlerMocks();
        when(hostCallingService.initiateHostCalling(
            any(HostCallingInitiateRequestBody.class),
            any(Map.class),
            any(Map.class)
        )).thenReturn(mockSuccessResponse);

        // Act
        ResponseEntity<ResponseWrapper<HostCallingInitiateResponse>> result = 
            hostCallingController.initiateHostCalling(
                "ANDROID", 
                "1.0.0", 
                mockRequest, 
                httpServletRequest, 
                httpServletResponse, 
                null, // null correlation key
                null  // null request id
            );

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        
        // Verify RequestHandler was called with null values
        verify(requestHandler).effectiveCorrelationKey(null, null);
    }

    @Test
    public void should_ReturnResponseWrapperWithCorrectStructure_When_ServiceReturnsSuccess() throws ClientGatewayException {
        // Arrange
        setupRequestHandlerMocks();
        when(hostCallingService.initiateHostCalling(
            any(HostCallingInitiateRequestBody.class),
            any(Map.class),
            any(Map.class)
        )).thenReturn(mockSuccessResponse);

        // Act
        ResponseEntity<ResponseWrapper<HostCallingInitiateResponse>> result = 
            hostCallingController.initiateHostCalling(
                "ANDROID", 
                "1.0.0", 
                mockRequest, 
                httpServletRequest, 
                httpServletResponse, 
                "TEST_CK", 
                "TEST_REQUEST_ID"
            );

        // Assert - Verify ResponseWrapper structure
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        
        ResponseWrapper<HostCallingInitiateResponse> wrapper = result.getBody();
        assertNotNull(wrapper);
        assertNotNull(wrapper.getResponse());
        assertNull(wrapper.getError());
        assertEquals("TEST_CORRELATION", wrapper.getCorrelationKey());
        
        // Verify the response content
        HostCallingInitiateResponse response = wrapper.getResponse();
        assertEquals("success", response.getStatus());
        assertEquals("REQ123", response.getRequestId());
        assertEquals("Test Chain", response.getChainName());
        assertEquals(true, response.getAvailableNow());
        assertEquals("09:00", response.getStartTime());
        assertEquals("21:00", response.getEndTime());
        assertEquals("You will receive a call from Test Chain soon", response.getMissedCallMessage());
    }

    @Test
    public void should_SetClientFieldInRequest_When_RequestProcessed() throws ClientGatewayException {
        // Arrange
        setupRequestHandlerMocks();
        when(hostCallingService.initiateHostCalling(
            any(HostCallingInitiateRequestBody.class),
            any(Map.class),
            any(Map.class)
        )).thenReturn(mockSuccessResponse);

        // Act
        ResponseEntity<ResponseWrapper<HostCallingInitiateResponse>> result = 
            hostCallingController.initiateHostCalling(
                "ANDROID", 
                "1.0.0", 
                mockRequest, 
                httpServletRequest, 
                httpServletResponse, 
                "TEST_CK", 
                "TEST_REQUEST_ID"
            );

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        
        // Verify that the client field was set in the request
        verify(mockRequest).setClient("ANDROID");
        
        verify(hostCallingService).initiateHostCalling(
            eq(mockRequest),
            eq(httpServletRequest.getParameterMap()),
            eq(mockHeaderMap)
        );
    }

    @Test
    public void should_HandleDifferentClientTypes_For_FactoryPattern() throws ClientGatewayException {
        // Arrange
        setupRequestHandlerMocks();
        when(hostCallingService.initiateHostCalling(
            any(HostCallingInitiateRequestBody.class),
            any(Map.class),
            any(Map.class)
        )).thenReturn(mockSuccessResponse);

        String[] clientTypes = {"PWA", "ANDROID", "IOS", "DESKTOP", "MSITE"};

        for (String clientType : clientTypes) {
            // Act
            ResponseEntity<ResponseWrapper<HostCallingInitiateResponse>> result = 
                hostCallingController.initiateHostCalling(
                    clientType, 
                    "1.0.0", 
                    mockRequest, 
                    httpServletRequest, 
                    httpServletResponse, 
                    "TEST_CK", 
                    "TEST_REQUEST_ID"
                );

            // Assert
            assertNotNull(result);
            assertEquals(HttpStatus.OK, result.getStatusCode());
            
            // Verify that the client was set correctly for each type
            verify(mockRequest).setClient(clientType.toUpperCase());
        }
    }
} 