package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.CategoryWiseSummary;
import com.mmt.hotels.clientgateway.request.SummaryCategoryRequest;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.model.request.flyfish.FlyFishReviewsRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;

@RunWith(MockitoJUnitRunner.class)
public class FlyfishControllerCBTest {

    @InjectMocks
    private FlyfishControllerCB flyfishControllerCB;

    @Mock
    private CommonHelper commonHelper;

    @Mock
    private MetricAspect metricAspect;

    @Test(expected = Exception.class)
    public void testPostByPass() {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        SummaryCategoryRequest summaryCategoryRequest = new SummaryCategoryRequest();
        CategoryWiseSummary filter = new CategoryWiseSummary();
        filter.setChatGptSummary("2");
        filter.setCategoryList(new ArrayList<>());
        summaryCategoryRequest.setFilter(filter);
        flyfishControllerCB.getFlyFishCategoryList(summaryCategoryRequest, "1234","IN","INR","EN","", "", "ALL","all|budget|all|all", request, response);
    }

    @Test(expected = Exception.class)
    public void testPostReviews() {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        FlyFishReviewsRequest flyFishReviewsRequest = new FlyFishReviewsRequest();

        flyfishControllerCB.getFlyFishReviews(flyFishReviewsRequest, request,"123","desktop","IN","INR","EN","1234","1234", "style","ALL", "1234",response);
    }

//    @Test
//    public void addRoomCodeInQueryParamTest() {
//        ReflectionTestUtils.invokeMethod(flyfishControllerCB, "addRoomCodeInQueryParam", "url", "roomcode,roomcode1");
//    }
//
//    @Test
//    public void updateDestinationUrlTest() {
//        ReflectionTestUtils.invokeMethod(flyfishControllerCB, "updateDestinationUrl", "url", "roomcode");
//    }
//
//    @Test
//    public void modifyFlyFishSummaryCategoryRequestTest() {
//        SummaryCategoryRequest request = new SummaryCategoryRequest();
//        request.setFilter(new CategoryWiseSummary());
//
//        ReflectionTestUtils.invokeMethod(flyfishControllerCB, "modifyFlyFishSummaryCategoryRequest", request);
//    }


}
