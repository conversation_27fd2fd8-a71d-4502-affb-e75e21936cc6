package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CorporateHelper;
import com.mmt.hotels.clientgateway.request.InitApprovalRequest;
import com.mmt.hotels.clientgateway.request.UpdateApprovalRequest;
import com.mmt.hotels.clientgateway.request.UpdatePolicyRequest;
import com.mmt.hotels.clientgateway.response.InitApprovalResponse;
import com.mmt.hotels.clientgateway.response.corporate.GetApprovalsResponse;
import com.mmt.hotels.clientgateway.response.corporate.UpdateApprovalResponse;
import com.mmt.hotels.clientgateway.response.corporate.UpdatePolicyResponse;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.CorporateService;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.model.request.payment.BeginCheckoutReqBody;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import com.mmt.hotels.util.Tuple;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class CorporateControllerTest {

    @InjectMocks
    CorporateController corporateController;

    @Mock
    CorporateService corporateService;

    @Spy
    private RequestHandler requestHandler;

    @Mock
    CorporateHelper corporateHelper;

    @Mock
    private MetricAspect metricAspect;

    @Test
    public void updatePolicyTest() throws Exception {

        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");


        Mockito.when(corporateService.updatePolicy(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new UpdatePolicyResponse());
        UpdatePolicyResponse responseData = corporateController.updateCorpPolicy(new UpdatePolicyRequest(), "INR","rId", "eng", ""
                ,request , new MockHttpServletResponse());
        Assert.assertNotNull(responseData);
    }

    @Test
    public void approvalsTest() throws Exception {

        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");


        Mockito.when(corporateService.initiateApproval(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new InitApprovalResponse());
        InitApprovalResponse responseData = corporateController.initApproval(new InitApprovalRequest(), "INR", "rId","eng", ""
                ,request , new MockHttpServletResponse());
        Assert.assertNotNull(responseData);
    }

    @Test
    public void getApprovalsInfoTest() throws Exception {
        Mockito.when(corporateService.approvalsInfo(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new GetApprovalsResponse());
        ResponseEntity<ResponseWrapper<GetApprovalsResponse>> getApprovalsResponse =
                corporateController.getApprovalsInfo("DESKTOP", "INR", "eng", "" ,
                        "", "rId","", "", "", "",  new MockHttpServletRequest());
        Assert.assertNotNull(getApprovalsResponse);
    }

    @Test
    public void updateApprovalTest() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("tid","test");

        Mockito.when(corporateService.updateApprovals(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(new UpdateApprovalResponse());

        Assert.assertNotNull(corporateController.updateApproval(new UpdateApprovalRequest(), "eng", "", "workflow_123", "", "INR","rId","CORP",
                request,new MockHttpServletResponse()));

    }



}
