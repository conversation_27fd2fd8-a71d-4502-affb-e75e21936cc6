package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.context.ListingContext;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.operations.TreelsListingOperation;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.GroupBookingResponse;
import com.mmt.hotels.clientgateway.response.TravelTipResponse;

import com.mmt.hotels.clientgateway.response.UserSessionData;
import com.mmt.hotels.clientgateway.response.filter.Filter;
import com.mmt.hotels.clientgateway.response.filter.FilterResponse;
import com.mmt.hotels.clientgateway.response.listing.ListingResponse;
import com.mmt.hotels.clientgateway.response.listing.UpsellRateplanResponse;
import com.mmt.hotels.clientgateway.response.listingmap.ListingMapResponse;

import com.mmt.hotels.clientgateway.response.moblanding.MatchMakerResponseCG;
import com.mmt.hotels.clientgateway.response.moblanding.MobLandingResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.FetchCollectionResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.FetchCollectionResponseV2;
import com.mmt.hotels.clientgateway.response.searchHotels.SearchHotelsResponse;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.filter.DPTExperimentDetails;
import com.mmt.hotels.clientgateway.restexecutors.TravelTripExecutor;
import com.mmt.hotels.clientgateway.service.ListingService;
import com.mmt.hotels.clientgateway.service.TreelsFilterService;
import com.mmt.hotels.clientgateway.transformer.factory.TravelTipFactory;

import com.mmt.hotels.clientgateway.util.*;
import org.junit.Assert;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

import static com.mmt.hotels.clientgateway.constants.Constants.CORP_ID_CONTEXT;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ListingControllerTest {

    @InjectMocks
    private ListingController listingController;

    @Mock
    private ListingService listingService;

    @Mock
    private TreelsListingOperation treelsListingOperation;

    @Mock
    private ListingContext listingContext;

    @Spy
    private MDCHelper mdcHelper;

    @Mock
    private MetricAspect metricAspect;

    @Mock
    private Utility utility;

    @Spy
    private CustomValidator customValidator;
    
    @Spy
    private RequestHandler requestHandler;

    @Mock
    private TreelsFilterService treelsFilterService;

    @Mock
    private TravelTripExecutor travelTipExecutor;

    @Mock
    private TravelTipFactory travelTipFactory;

    @Mock
    private ObjectMapperUtil objectMapperUtil;

    @Before
    public void init() {
        ReflectionTestUtils.setField(listingController, "requestCallbackCount", 2);
    }

    @Test
    public  void filterCountAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        when(listingService.filterCount(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new FilterResponse());
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        filterCountRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<FilterResponse>>  resp = listingController.filterCount("PWA","1.0.0",filterCountRequest, true, request, response, null,null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public  void batchFilterAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","test");
        when(listingService.batchFilterResponse(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new FilterResponse());
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        filterCountRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<FilterResponse>>  resp = listingController.batchFilter("PWA","1.0.0",filterCountRequest, true, request, response, null,null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());
    }
    
    @Test
    public  void searchHotelsAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        searchHotelsRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<SearchHotelsResponse>>  resp = listingController.searchHotels("PWA","1.0.0",searchHotelsRequest, request, response, null,null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public  void listingAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        searchHotelsRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<SearchHotelsResponse>>  resp = listingController.listing("PWA","1.0.0",searchHotelsRequest, request, response, null,null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public  void groupBookingAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
//        Mockito.when(listingService.submitGroupBooking(Mockito.any(), Mockito.any())).thenReturn(new GroupBookingResponse());
        GroupBookingRequest groupBookingRequest = new GroupBookingRequest();
        ResponseEntity<ResponseWrapper<GroupBookingResponse>>  resp = listingController.groupBooking("PWA","1.0.0",groupBookingRequest, request, response, null,null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public  void searchTreelsAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        ListingSearchRequestV2 searchHotelsRequest = new ListingSearchRequestV2();
        searchHotelsRequest.setRequestDetails(new RequestDetails());
        when(listingContext.executeStrategy(Mockito.anyString(), Mockito.any(), Mockito.anyMap(), Mockito.anyMap())).thenReturn(new ListingResponse());
        ResponseEntity<ResponseWrapper<ListingResponse>>  resp = listingController.searchTreelsListing("PWA","1.0.0",searchHotelsRequest, request, response, null,null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public  void mobLandingAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        when(listingService.mobLanding(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new MobLandingResponse());
        MobLandingRequest mobLandingRequest = new MobLandingRequest();
        mobLandingRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<MobLandingResponse>>  resp = listingController.mobLanding("PWA","1.0.0",mobLandingRequest, request, response, null,null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }
    
    @Test
    public  void listingMapAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        when(listingService.listingMap(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new ListingMapResponse());
        ListingMapRequest listingMapRequest = new ListingMapRequest();
        listingMapRequest.setRequestDetails(new RequestDetails());
        listingMapRequest.setMapDetails(new MapDetails());
        ResponseEntity<ResponseWrapper<ListingMapResponse>>  resp = listingController.listingMap("PWA","1.0.0",listingMapRequest, request, response, null,null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public  void fetchCollectionsAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        when(listingService.fetchCollections(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new FetchCollectionResponse());
        FetchCollectionRequest fetchCollectionRequest = new FetchCollectionRequest();
        fetchCollectionRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<FetchCollectionResponse>>  resp = listingController.fetchCollection(fetchCollectionRequest,"ANDROID","1.0.0", request, response, "ck","rId");
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public  void fetchCollectionV2Test() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","rahultest");
        when(listingService.fetchCollectionsV2(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new FetchCollectionResponseV2());
        FetchCollectionRequest fetchCollectionRequest = new FetchCollectionRequest();
        fetchCollectionRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<FetchCollectionResponseV2>>  resp = listingController.fetchCollectionV2(fetchCollectionRequest,"ANDROID","1.0.0", request, response, "ck","rId");
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public void testWishListedHotels() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        request.addHeader("tid", "qwerty");
        when(listingService.searchHotels(Mockito.any(), Mockito.anyMap(), Mockito.anyMap())).thenReturn(new SearchHotelsResponse());
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        searchHotelsRequest.setRequestDetails(new RequestDetails());
        searchHotelsRequest.setSearchCriteria(new SearchHotelsCriteria());
        ResponseEntity<ResponseWrapper<SearchHotelsResponse>> responseEntity = listingController.wishListedHotels("ANDROID", "1.0.0", searchHotelsRequest, request, response, null, "rId");
        Assert.assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        Assert.assertNotNull(responseEntity.getBody());
        Assert.assertNotNull(responseEntity.getBody().getCorrelationKey());
        Assert.assertNull(responseEntity.getBody().getError());
    }

    @Test
    public void getCityOverviewClientTest() throws ClientGatewayException {
        MockHttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        request.addHeader("tid", "qwerty");
        CityOverviewRequest cityOverviewRequest = new CityOverviewRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setIdContext("B2C");
        cityOverviewRequest.setRequestDetails(requestDetails);
        ResponseEntity<ResponseWrapper<MatchMakerResponseCG>> responseEntity = listingController.fetchCityOverview("Desktop","",cityOverviewRequest,request,response,"","");
        Assert.assertNotNull(responseEntity);
    }

    @Test
    public  void listingMapAPITest2() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        when(listingService.listingMap(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new ListingMapResponse());
        ListingMapRequest listingMapRequest = new ListingMapRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setIdContext(CORP_ID_CONTEXT);
        listingMapRequest.setRequestDetails(requestDetails);
        SearchHotelsCriteria searchHotelsCriteria = new SearchHotelsCriteria();
        CollectionCriteria collectionCriteria = new CollectionCriteria();
        searchHotelsCriteria.setCollectionCriteria(collectionCriteria);
        listingMapRequest.setSearchCriteria(searchHotelsCriteria);
        listingMapRequest.setMapDetails(new MapDetails());
        //condition1
        ResponseEntity<ResponseWrapper<ListingMapResponse>>  resp = listingController.listingMap("PWA","1.0.0",listingMapRequest, request, response, null,null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public  void listingMapAPITest3() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        when(listingService.listingMap(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new ListingMapResponse());
        ListingMapRequest listingMapRequest = new ListingMapRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setIdContext("ABC");
        listingMapRequest.setRequestDetails(requestDetails);
        SearchHotelsCriteria searchHotelsCriteria = new SearchHotelsCriteria();
        CollectionCriteria collectionCriteria = new CollectionCriteria();
        searchHotelsCriteria.setCollectionCriteria(collectionCriteria);
        listingMapRequest.setSearchCriteria(searchHotelsCriteria);
        listingMapRequest.setMapDetails(new MapDetails());
        ResponseEntity<ResponseWrapper<ListingMapResponse>>  resp = listingController.listingMap("PWA","1.0.0",listingMapRequest, request, response, null,null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }

    @Test
    public  void listingMapAPITest4() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        ListingMapRequest listingMapRequest = new ListingMapRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setIdContext(CORP_ID_CONTEXT);
        listingMapRequest.setRequestDetails(requestDetails);
        SearchHotelsCriteria searchHotelsCriteria = new SearchHotelsCriteria();
        CollectionCriteria collectionCriteria = new CollectionCriteria();
        searchHotelsCriteria.setCollectionCriteria(collectionCriteria);
        listingMapRequest.setSearchCriteria(searchHotelsCriteria);
        listingMapRequest.setMapDetails(new MapDetails());
        ResponseWrapper<ListingMapResponse> responseResponseWrapper = new ResponseWrapper<>();
        when(requestHandler.validateListingMapRequest(Mockito.any())).thenReturn(responseResponseWrapper);
        //condition1
        ResponseEntity<ResponseWrapper<ListingMapResponse>>  resp = listingController.listingMap("PWA","1.0.0",listingMapRequest, request, response, null,null);
        Assert.assertEquals(HttpStatus.BAD_REQUEST , resp.getStatusCode());

    }

    @Test
    public void treelsFilterCountTest() throws ClientGatewayException {
        MockHttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        request.addHeader("tid", "qwerty");
        TreelsFilterCountRequest treelsFilterCountRequest = new TreelsFilterCountRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setIdContext("B2C");
        treelsFilterCountRequest.setRequestDetails(requestDetails);
        ResponseEntity<ResponseWrapper<FilterResponse>> responseEntity = listingController.treelsFilterCount("Android","",treelsFilterCountRequest, false,request,response,"","");
        Assert.assertNotNull(responseEntity);
    }

    @Test
    public void fetchUpsellRateplanTest() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        request.addHeader("tid", "qwerty");
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        searchHotelsRequest.setRequestDetails(new RequestDetails());

        when(listingService.fetchUpsellRateplanResponse(Mockito.any(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn(new UpsellRateplanResponse());

        ResponseEntity<ResponseWrapper<UpsellRateplanResponse>> resp =
                listingController.fetchUpsellRateplan("PWA", "1.0.0", searchHotelsRequest, request, response, null,null);

        Assert.assertEquals(HttpStatus.OK, resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());
    }


    @Test
    public  void travelInsightAPITest() throws Exception{
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid","trinatest");
        Mockito.when(listingService.travelTip(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new TravelTipResponse());
        TravelTipRequest travelTipRequest = new TravelTipRequest();
        travelTipRequest.setRequestDetails(new RequestDetails());
        ResponseEntity<ResponseWrapper<TravelTipResponse>>  resp = listingController. travelInsight("PWA","1.0.0",travelTipRequest, request, response, null);
        Assert.assertEquals(HttpStatus.OK , resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getCorrelationKey());
        Assert.assertNull(resp.getBody().getError());

    }








    @Test
    public void groupBooking_withListAllPropCall_shouldReturnGroupBookingResponse() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        GroupBookingRequest groupBookingRequest = new GroupBookingRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setListAllPropCall(true);
        groupBookingRequest.setRequestDetails(requestDetails);
        UserSessionData userSessionData = new UserSessionData();
        userSessionData.setRequestCallBackCount(1);


        ResponseEntity<ResponseWrapper<GroupBookingResponse>> responseEntity = listingController.groupBooking("PWA", "1.0.0", groupBookingRequest, request, response, "ck", "requestId");

        Assert.assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        Assert.assertNotNull(responseEntity.getBody().getCorrelationKey());
        Assert.assertNull(responseEntity.getBody().getError());
    }

    @Test
    public void groupBooking_withNullRequestDetails_shouldReturnGroupBookingResponse() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        GroupBookingRequest groupBookingRequest = new GroupBookingRequest();
        groupBookingRequest.setRequestDetails(null);

        when(requestHandler.effectiveCorrelationKey(Mockito.anyString(), Mockito.anyString())).thenReturn("correlationKey");
//        Mockito.when(requestHandler.handleCommonRequest(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(new Tuple<>("correlationKey", new HashMap<>()));
//        Mockito.when(listingService.submitGroupBooking(Mockito.any(), Mockito.anyInt())).thenReturn(new GroupBookingResponse());

        ResponseEntity<ResponseWrapper<GroupBookingResponse>> responseEntity = listingController.groupBooking("PWA", "1.0.0", groupBookingRequest, request, response, "ck", "requestId");

        Assert.assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        Assert.assertNotNull(responseEntity.getBody().getCorrelationKey());
        Assert.assertNull(responseEntity.getBody().getError());
    }

    @Test
    public void groupBooking_withRequestCallBackCountExceedingLimit_shouldReturnGroupBookingResponse() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        GroupBookingRequest groupBookingRequest = new GroupBookingRequest();
        groupBookingRequest.setRequestDetails(new RequestDetails());
        UserSessionData userSessionData = new UserSessionData();
        userSessionData.setRequestCallBackCount(5);

        when(requestHandler.effectiveCorrelationKey(Mockito.anyString(), Mockito.anyString())).thenReturn("correlationKey");
//        Mockito.when(requestHandler.handleCommonRequest(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(new Tuple<>("correlationKey", new HashMap<>()));
//        when(utility.addUserServiceDataToRequest(Mockito.any(), Mockito.any(), Mockito.anyString())).thenReturn(userSessionData);
//        Mockito.when(listingService.buildGroupBookingResponse(Mockito.anyInt(), Mockito.anyString())).thenReturn(new GroupBookingResponse());

        ResponseEntity<ResponseWrapper<GroupBookingResponse>> responseEntity = listingController.groupBooking("PWA", "1.0.0", groupBookingRequest, request, response, "ck", "requestId");

        Assert.assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        Assert.assertNotNull(responseEntity.getBody().getCorrelationKey());
        Assert.assertNull(responseEntity.getBody().getError());
    }

    @Test
    public void groupBooking_withValidRequest_shouldReturnGroupBookingResponse() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        GroupBookingRequest groupBookingRequest = new GroupBookingRequest();
        groupBookingRequest.setRequestDetails(new RequestDetails());
        groupBookingRequest.getRequestDetails().setListAllPropCall(true);
        UserSessionData userSessionData = new UserSessionData();
        userSessionData.setRequestCallBackCount(1);

        Mockito.when(requestHandler.effectiveCorrelationKey(Mockito.anyString(), Mockito.anyString())).thenReturn("correlationKey");

        ResponseEntity<ResponseWrapper<GroupBookingResponse>> responseEntity = listingController.groupBooking("PWA", "1.0.0", groupBookingRequest, request, response, "ck", "requestId");

        Assert.assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        Assert.assertNotNull(responseEntity.getBody().getCorrelationKey());
        Assert.assertNull(responseEntity.getBody().getError());
    }

    @Test
    public void smartFilterAPITest() throws Exception {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid", "smartFilterTest");
        
        // Create mock filter response
        List<Filter> mockFilters = new ArrayList<>();
        Filter mockFilter = new Filter();
        mockFilter.setTitle("Swimming Pool");
        mockFilter.setFilterValue("swimming_pool");
        mockFilters.add(mockFilter);
        
        // Mock the service method
        when(listingService.smartFilters(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.any()))
                .thenReturn(mockFilters);
        
        // Create request
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setIdContext("B2C");
        filterCountRequest.setRequestDetails(requestDetails);
        
        SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
        searchCriteria.setSearchQueryText("hotel with pool");
        filterCountRequest.setSearchCriteria(searchCriteria);
        
        // Execute test
        ResponseEntity<ResponseWrapper<List<Filter>>> resp = listingController.smartFilter(
                "PWA", "1.0.0", filterCountRequest, false, request, response, null, null);
        
        // Verify response
        Assert.assertEquals(HttpStatus.OK, resp.getStatusCode());
        Assert.assertNull(resp.getBody().getError());
        Assert.assertNotNull(resp.getBody().getResponse());
        Assert.assertEquals(1, resp.getBody().getResponse().size());
        Assert.assertEquals("Swimming Pool", resp.getBody().getResponse().get(0).getTitle());
    }

    @Test
    public void smartFilterAPITestNoFiltersFound() throws Exception {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid", "smartFilterNoResultsTest");
        
        // Mock the service method to return empty list
        when(listingService.smartFilters(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.any()))
                .thenReturn(new ArrayList<>());
        
        // Create request
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setIdContext("B2C");
        filterCountRequest.setRequestDetails(requestDetails);
        
        SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
        searchCriteria.setSearchQueryText("nonexistent amenity");
        filterCountRequest.setSearchCriteria(searchCriteria);
        
        // Execute test
        ResponseEntity<ResponseWrapper<List<Filter>>> resp = listingController.smartFilter(
                "PWA", "1.0.0", filterCountRequest, false, request, response, null, null);
        
        // Verify response
        Assert.assertEquals(HttpStatus.OK, resp.getStatusCode());
        Assert.assertNotNull(resp.getBody().getError());
        Assert.assertEquals("200", resp.getBody().getError().getCode());
        Assert.assertEquals("No Filters Found", resp.getBody().getError().getMessage());
        Assert.assertNull(resp.getBody().getResponse());
    }

    @Test
    public void smartFilterAPITestWithSeoCorp() throws Exception {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        ((MockHttpServletRequest) request).addHeader("tid", "smartFilterSeoCorpTest");
        
        // Create mock filter response
        List<Filter> mockFilters = new ArrayList<>();
        Filter mockFilter = new Filter();
        mockFilter.setTitle("Free WiFi");
        mockFilter.setFilterValue("wifi");
        mockFilters.add(mockFilter);
        
        // Mock the service method
        when(listingService.smartFilters(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.any()))
                .thenReturn(mockFilters);
        
        // Create request
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setIdContext("B2C");
        filterCountRequest.setRequestDetails(requestDetails);
        
        SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
        searchCriteria.setSearchQueryText("wifi hotel");
        filterCountRequest.setSearchCriteria(searchCriteria);
        
        // Execute test with seoCorp = true
        ResponseEntity<ResponseWrapper<List<Filter>>> resp = listingController.smartFilter(
                "PWA", "1.0.0", filterCountRequest, true, request, response, null, null);
        
        // Verify response
        Assert.assertEquals(HttpStatus.OK, resp.getStatusCode());
        Assert.assertNull(resp.getBody().getError());
        Assert.assertNotNull(resp.getBody().getResponse());
        Assert.assertEquals(1, resp.getBody().getResponse().size());
        Assert.assertEquals("Free WiFi", resp.getBody().getResponse().get(0).getTitle());
        
        // Verify that seoCorp flag was passed to service
        Mockito.verify(listingService).smartFilters(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.eq(true), Mockito.any());
    }
}
