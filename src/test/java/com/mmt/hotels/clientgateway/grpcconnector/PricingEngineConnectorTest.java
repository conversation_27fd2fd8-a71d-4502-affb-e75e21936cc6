package com.mmt.hotels.clientgateway.grpcconnector;

import io.grpc.ManagedChannel;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class PricingEngineConnectorTest {

    private PricingEngineConnector connector;
    private ManagedChannel desktop;
    private ManagedChannel android;
    private ManagedChannel ios;
    private ManagedChannel pwa;

    @Before
    public void setUp() {
        connector = new PricingEngineConnector();

        desktop = Mockito.mock(ManagedChannel.class);
        android = Mockito.mock(ManagedChannel.class);
        ios = Mockito.mock(ManagedChannel.class);
        pwa = Mockito.mock(ManagedChannel.class);

        // Populate the internal channel fields directly
        ReflectionTestUtils.setField(connector, "channelDesktop", desktop);
        ReflectionTestUtils.setField(connector, "channelAndroid", android);
        ReflectionTestUtils.setField(connector, "channelIos", ios);
        ReflectionTestUtils.setField(connector, "channelPwa", pwa);
    }

    @Test
    public void testGetChannelNullReturnsDesktop() {
        ManagedChannel result = connector.getChannel(null);
        Assert.assertEquals(desktop, result);
    }

    @Test
    public void testGetChannelEmptyReturnsDesktop() {
        ManagedChannel result = connector.getChannel("");
        Assert.assertEquals(desktop, result);
    }

    @Test
    public void testGetChannelAndroid() {
        ManagedChannel result = connector.getChannel("ANDROID");
        Assert.assertEquals(android, result);
    }

    @Test
    public void testGetChannelIosCaseInsensitive() {
        ManagedChannel result = connector.getChannel("ios");
        Assert.assertEquals(ios, result);
    }

    @Test
    public void testGetChannelPwa() {
        ManagedChannel result = connector.getChannel("PWA");
        Assert.assertEquals(pwa, result);
    }

    @Test
    public void testGetChannelDefaultDesktop() {
        ManagedChannel result = connector.getChannel("DESKTOP");
        Assert.assertEquals(desktop, result);
    }
} 