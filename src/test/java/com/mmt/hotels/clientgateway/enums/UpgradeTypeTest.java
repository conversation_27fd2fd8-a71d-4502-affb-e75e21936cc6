package com.mmt.hotels.clientgateway.enums;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * Comprehensive unit tests for UpgradeType enum
 * Ensures 100% branch coverage for all enum values and methods
 */
@RunWith(MockitoJUnitRunner.class)
public class UpgradeTypeTest {

    @Test
    public void should_HaveCorrectNumberOfValues_When_ValuesAreCalled() {
        // Arrange & Act
        UpgradeType[] values = UpgradeType.values();
        
        // Assert
        assertEquals(3, values.length);
    }

    @Test
    public void should_ContainAllExpectedValues_When_ValuesAreCalled() {
        // Arrange & Act
        UpgradeType[] values = UpgradeType.values();
        
        // Assert
        assertTrue(containsValue(values, UpgradeType.ROOM_AND_MEAL));
        assertTrue(containsValue(values, UpgradeType.ROOM));
        assertTrue(containsValue(values, UpgradeType.MEAL));
    }

    @Test
    public void should_ReturnCorrectEnumValue_When_ValueOfIsCalledWithValidString() {
        // Arrange & Act & Assert
        assertEquals(UpgradeType.ROOM_AND_MEAL, UpgradeType.valueOf("ROOM_AND_MEAL"));
        assertEquals(UpgradeType.ROOM, UpgradeType.valueOf("ROOM"));
        assertEquals(UpgradeType.MEAL, UpgradeType.valueOf("MEAL"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void should_ThrowIllegalArgumentException_When_ValueOfIsCalledWithInvalidString() {
        // Arrange & Act
        UpgradeType.valueOf("INVALID_UPGRADE");
        
        // Assert: Exception expected
    }

    @Test(expected = NullPointerException.class)
    public void should_ThrowNullPointerException_When_ValueOfIsCalledWithNull() {
        // Arrange & Act
        UpgradeType.valueOf(null);
        
        // Assert: Exception expected
    }

    @Test
    public void should_ReturnCorrectStringRepresentation_When_ToStringIsCalled() {
        // Arrange & Act & Assert
        assertEquals("ROOM_AND_MEAL", UpgradeType.ROOM_AND_MEAL.toString());
        assertEquals("ROOM", UpgradeType.ROOM.toString());
        assertEquals("MEAL", UpgradeType.MEAL.toString());
    }

    @Test
    public void should_ReturnCorrectName_When_NameIsCalled() {
        // Arrange & Act & Assert
        assertEquals("ROOM_AND_MEAL", UpgradeType.ROOM_AND_MEAL.name());
        assertEquals("ROOM", UpgradeType.ROOM.name());
        assertEquals("MEAL", UpgradeType.MEAL.name());
    }

    @Test
    public void should_ReturnCorrectOrdinal_When_OrdinalIsCalled() {
        // Arrange & Act & Assert
        assertEquals(0, UpgradeType.ROOM_AND_MEAL.ordinal());
        assertEquals(1, UpgradeType.ROOM.ordinal());
        assertEquals(2, UpgradeType.MEAL.ordinal());
    }

    @Test
    public void should_BeEqualToItself_When_EqualsIsCalledWithSameInstance() {
        // Arrange & Act & Assert
        assertEquals(UpgradeType.ROOM_AND_MEAL, UpgradeType.ROOM_AND_MEAL);
        assertEquals(UpgradeType.ROOM, UpgradeType.ROOM);
        assertEquals(UpgradeType.MEAL, UpgradeType.MEAL);
    }

    @Test
    public void should_NotBeEqualToDifferentEnum_When_EqualsIsCalledWithDifferentValue() {
        // Arrange & Act & Assert
        assertNotEquals(UpgradeType.ROOM_AND_MEAL, UpgradeType.ROOM);
        assertNotEquals(UpgradeType.ROOM, UpgradeType.MEAL);
        assertNotEquals(UpgradeType.MEAL, UpgradeType.ROOM_AND_MEAL);
    }

    @Test
    public void should_HaveConsistentHashCode_When_HashCodeIsCalled() {
        // Arrange & Act
        int roomAndMealHash1 = UpgradeType.ROOM_AND_MEAL.hashCode();
        int roomAndMealHash2 = UpgradeType.ROOM_AND_MEAL.hashCode();
        
        // Assert
        assertEquals(roomAndMealHash1, roomAndMealHash2);
        assertNotEquals(UpgradeType.ROOM_AND_MEAL.hashCode(), UpgradeType.ROOM.hashCode());
    }

    @Test
    public void should_WorkProperlyInComparisons_When_ComparingDifferentValues() {
        // Arrange & Act & Assert
        assertTrue(UpgradeType.ROOM_AND_MEAL.compareTo(UpgradeType.ROOM) < 0);
        assertTrue(UpgradeType.ROOM.compareTo(UpgradeType.ROOM_AND_MEAL) > 0);
        assertEquals(0, UpgradeType.MEAL.compareTo(UpgradeType.MEAL));
    }

    @Test
    public void should_WorkProperlyInSwitch_When_UsedInSwitchStatement() {
        // Arrange & Act & Assert
        assertEquals("Room and Meal Upgrade", getSwitchResult(UpgradeType.ROOM_AND_MEAL));
        assertEquals("Room Upgrade", getSwitchResult(UpgradeType.ROOM));
        assertEquals("Meal Upgrade", getSwitchResult(UpgradeType.MEAL));
    }

    @Test
    public void should_BeSerializable_When_EnumIsUsedInCollections() {
        // Arrange & Act
        java.util.Set<UpgradeType> upgradeTypes = java.util.EnumSet.of(
            UpgradeType.ROOM_AND_MEAL, 
            UpgradeType.ROOM
        );
        
        // Assert
        assertTrue(upgradeTypes.contains(UpgradeType.ROOM_AND_MEAL));
        assertTrue(upgradeTypes.contains(UpgradeType.ROOM));
        assertFalse(upgradeTypes.contains(UpgradeType.MEAL));
        assertEquals(2, upgradeTypes.size());
    }

    @Test
    public void should_HandleAllUpgradeTypesProperly_When_IteratingThroughValues() {
        // Arrange
        int count = 0;
        
        // Act
        for (UpgradeType upgradeType : UpgradeType.values()) {
            assertNotNull(upgradeType);
            assertNotNull(upgradeType.name());
            count++;
        }
        
        // Assert
        assertEquals(3, count);
    }

    // Helper method to check if an array contains a specific value
    private boolean containsValue(UpgradeType[] values, UpgradeType target) {
        for (UpgradeType value : values) {
            if (value == target) {
                return true;
            }
        }
        return false;
    }

    // Helper method for switch testing
    private String getSwitchResult(UpgradeType upgradeType) {
        switch (upgradeType) {
            case ROOM_AND_MEAL:
                return "Room and Meal Upgrade";
            case ROOM:
                return "Room Upgrade";
            case MEAL:
                return "Meal Upgrade";
            default:
                return "Unknown Upgrade";
        }
    }
}
