package com.mmt.hotels.clientgateway.enums;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * Comprehensive unit tests for TemplateType enum
 * Ensures 100% branch coverage for all enum values and methods
 */
@RunWith(MockitoJUnitRunner.class)
public class TemplateTypeTest {

    @Test
    public void should_HaveCorrectNumberOfValues_When_ValuesAreCalled() {
        // Arrange & Act
        TemplateType[] values = TemplateType.values();
        
        // Assert
        assertEquals(2, values.length);
    }

    @Test
    public void should_ContainAllExpectedValues_When_ValuesAreCalled() {
        // Arrange & Act
        TemplateType[] values = TemplateType.values();
        
        // Assert
        assertTrue(containsValue(values, TemplateType.DEFAULT));
        assertTrue(containsValue(values, TemplateType.IMAGE_ONLY));
    }

    @Test
    public void should_ReturnCorrectEnumValue_When_ValueOfIsCalledWithValidString() {
        // Arrange & Act & Assert
        assertEquals(TemplateType.DEFAULT, TemplateType.valueOf("DEFAULT"));
        assertEquals(TemplateType.IMAGE_ONLY, TemplateType.valueOf("IMAGE_ONLY"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void should_ThrowIllegalArgumentException_When_ValueOfIsCalledWithInvalidString() {
        // Arrange & Act
        TemplateType.valueOf("INVALID_TEMPLATE");
        
        // Assert: Exception expected
    }

    @Test(expected = NullPointerException.class)
    public void should_ThrowNullPointerException_When_ValueOfIsCalledWithNull() {
        // Arrange & Act
        TemplateType.valueOf(null);
        
        // Assert: Exception expected
    }

    @Test
    public void should_ReturnCorrectStringRepresentation_When_ToStringIsCalled() {
        // Arrange & Act & Assert
        assertEquals("DEFAULT", TemplateType.DEFAULT.toString());
        assertEquals("IMAGE_ONLY", TemplateType.IMAGE_ONLY.toString());
    }

    @Test
    public void should_ReturnCorrectName_When_NameIsCalled() {
        // Arrange & Act & Assert
        assertEquals("DEFAULT", TemplateType.DEFAULT.name());
        assertEquals("IMAGE_ONLY", TemplateType.IMAGE_ONLY.name());
    }

    @Test
    public void should_ReturnCorrectOrdinal_When_OrdinalIsCalled() {
        // Arrange & Act & Assert
        assertEquals(0, TemplateType.DEFAULT.ordinal());
        assertEquals(1, TemplateType.IMAGE_ONLY.ordinal());
    }

    @Test
    public void should_BeEqualToItself_When_EqualsIsCalledWithSameInstance() {
        // Arrange & Act & Assert
        assertEquals(TemplateType.DEFAULT, TemplateType.DEFAULT);
        assertEquals(TemplateType.IMAGE_ONLY, TemplateType.IMAGE_ONLY);
    }

    @Test
    public void should_NotBeEqualToDifferentEnum_When_EqualsIsCalledWithDifferentValue() {
        // Arrange & Act & Assert
        assertNotEquals(TemplateType.DEFAULT, TemplateType.IMAGE_ONLY);
        assertNotEquals(TemplateType.IMAGE_ONLY, TemplateType.DEFAULT);
    }

    @Test
    public void should_HaveConsistentHashCode_When_HashCodeIsCalled() {
        // Arrange & Act
        int defaultHash1 = TemplateType.DEFAULT.hashCode();
        int defaultHash2 = TemplateType.DEFAULT.hashCode();
        
        // Assert
        assertEquals(defaultHash1, defaultHash2);
        assertNotEquals(TemplateType.DEFAULT.hashCode(), TemplateType.IMAGE_ONLY.hashCode());
    }

    @Test
    public void should_WorkProperlyInComparisons_When_ComparingDifferentValues() {
        // Arrange & Act & Assert
        assertTrue(TemplateType.DEFAULT.compareTo(TemplateType.IMAGE_ONLY) < 0);
        assertTrue(TemplateType.IMAGE_ONLY.compareTo(TemplateType.DEFAULT) > 0);
        assertEquals(0, TemplateType.DEFAULT.compareTo(TemplateType.DEFAULT));
    }

    @Test
    public void should_WorkProperlyInSwitch_When_UsedInSwitchStatement() {
        // Arrange & Act & Assert
        assertEquals("Default Template", getSwitchResult(TemplateType.DEFAULT));
        assertEquals("Image Only Template", getSwitchResult(TemplateType.IMAGE_ONLY));
    }

    @Test
    public void should_BeSerializable_When_EnumIsUsedInCollections() {
        // Arrange & Act
        java.util.Set<TemplateType> templateTypes = java.util.EnumSet.of(TemplateType.DEFAULT);
        
        // Assert
        assertTrue(templateTypes.contains(TemplateType.DEFAULT));
        assertFalse(templateTypes.contains(TemplateType.IMAGE_ONLY));
        assertEquals(1, templateTypes.size());
    }

    @Test
    public void should_HandleAllTemplateTypesProperly_When_IteratingThroughValues() {
        // Arrange
        int count = 0;
        
        // Act
        for (TemplateType templateType : TemplateType.values()) {
            assertNotNull(templateType);
            assertNotNull(templateType.name());
            count++;
        }
        
        // Assert
        assertEquals(2, count);
    }

    @Test
    public void should_WorkInEnumSet_When_CreatingCompleteSet() {
        // Arrange & Act
        java.util.Set<TemplateType> allTemplates = java.util.EnumSet.allOf(TemplateType.class);
        
        // Assert
        assertEquals(2, allTemplates.size());
        assertTrue(allTemplates.contains(TemplateType.DEFAULT));
        assertTrue(allTemplates.contains(TemplateType.IMAGE_ONLY));
    }

    @Test
    public void should_WorkInEnumMap_When_UsedAsKey() {
        // Arrange & Act
        java.util.Map<TemplateType, String> templateMap = new java.util.EnumMap<>(TemplateType.class);
        templateMap.put(TemplateType.DEFAULT, "Standard Template");
        templateMap.put(TemplateType.IMAGE_ONLY, "Image Template");
        
        // Assert
        assertEquals("Standard Template", templateMap.get(TemplateType.DEFAULT));
        assertEquals("Image Template", templateMap.get(TemplateType.IMAGE_ONLY));
        assertEquals(2, templateMap.size());
    }

    @Test
    public void should_BeCompatibleWithSemantics_When_UsedInBusinessLogic() {
        // Arrange & Act & Assert
        // Testing that the enum values make sense in a business context
        assertTrue("DEFAULT should be the first template type", TemplateType.DEFAULT.ordinal() == 0);
        assertTrue("IMAGE_ONLY should be an alternative template type", TemplateType.IMAGE_ONLY.ordinal() == 1);
    }

    // Helper method to check if an array contains a specific value
    private boolean containsValue(TemplateType[] values, TemplateType target) {
        for (TemplateType value : values) {
            if (value == target) {
                return true;
            }
        }
        return false;
    }

    // Helper method for switch testing
    private String getSwitchResult(TemplateType templateType) {
        switch (templateType) {
            case DEFAULT:
                return "Default Template";
            case IMAGE_ONLY:
                return "Image Only Template";
            default:
                return "Unknown Template";
        }
    }
}
