package com.mmt.hotels.clientgateway.enums;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * Comprehensive unit tests for PersuasionType enum
 * Ensures 100% branch coverage for all enum values and methods
 */
@RunWith(MockitoJUnitRunner.class)
public class PersuasionTypeTest {

    @Test
    public void should_HaveCorrectNumberOfValues_When_ValuesAreCalled() {
        // Arrange & Act
        PersuasionType[] values = PersuasionType.values();
        
        // Assert
        assertEquals(2, values.length);
    }

    @Test
    public void should_ContainAllExpectedValues_When_ValuesAreCalled() {
        // Arrange & Act
        PersuasionType[] values = PersuasionType.values();
        
        // Assert
        assertTrue(containsValue(values, PersuasionType.PEITHO));
        assertTrue(containsValue(values, PersuasionType.HOTEL_CATEGORY));
    }

    @Test
    public void should_ReturnCorrectEnumValue_When_ValueOfIsCalledWithValidString() {
        // Arrange & Act & Assert
        assertEquals(PersuasionType.PEITHO, PersuasionType.valueOf("PEITHO"));
        assertEquals(PersuasionType.HOTEL_CATEGORY, PersuasionType.valueOf("HOTEL_CATEGORY"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void should_ThrowIllegalArgumentException_When_ValueOfIsCalledWithInvalidString() {
        // Arrange & Act
        PersuasionType.valueOf("INVALID_PERSUASION");
        
        // Assert: Exception expected
    }

    @Test(expected = NullPointerException.class)
    public void should_ThrowNullPointerException_When_ValueOfIsCalledWithNull() {
        // Arrange & Act
        PersuasionType.valueOf(null);
        
        // Assert: Exception expected
    }

    @Test
    public void should_ReturnCorrectStringRepresentation_When_ToStringIsCalled() {
        // Arrange & Act & Assert
        assertEquals("PEITHO", PersuasionType.PEITHO.toString());
        assertEquals("HOTEL_CATEGORY", PersuasionType.HOTEL_CATEGORY.toString());
    }

    @Test
    public void should_ReturnCorrectName_When_NameIsCalled() {
        // Arrange & Act & Assert
        assertEquals("PEITHO", PersuasionType.PEITHO.name());
        assertEquals("HOTEL_CATEGORY", PersuasionType.HOTEL_CATEGORY.name());
    }

    @Test
    public void should_ReturnCorrectOrdinal_When_OrdinalIsCalled() {
        // Arrange & Act & Assert
        assertEquals(0, PersuasionType.PEITHO.ordinal());
        assertEquals(1, PersuasionType.HOTEL_CATEGORY.ordinal());
    }

    @Test
    public void should_BeEqualToItself_When_EqualsIsCalledWithSameInstance() {
        // Arrange & Act & Assert
        assertEquals(PersuasionType.PEITHO, PersuasionType.PEITHO);
        assertEquals(PersuasionType.HOTEL_CATEGORY, PersuasionType.HOTEL_CATEGORY);
    }

    @Test
    public void should_NotBeEqualToDifferentEnum_When_EqualsIsCalledWithDifferentValue() {
        // Arrange & Act & Assert
        assertNotEquals(PersuasionType.PEITHO, PersuasionType.HOTEL_CATEGORY);
        assertNotEquals(PersuasionType.HOTEL_CATEGORY, PersuasionType.PEITHO);
    }

    @Test
    public void should_HaveConsistentHashCode_When_HashCodeIsCalled() {
        // Arrange & Act
        int peithoHash1 = PersuasionType.PEITHO.hashCode();
        int peithoHash2 = PersuasionType.PEITHO.hashCode();
        
        // Assert
        assertEquals(peithoHash1, peithoHash2);
        assertNotEquals(PersuasionType.PEITHO.hashCode(), PersuasionType.HOTEL_CATEGORY.hashCode());
    }

    @Test
    public void should_WorkProperlyInComparisons_When_ComparingDifferentValues() {
        // Arrange & Act & Assert
        assertTrue(PersuasionType.PEITHO.compareTo(PersuasionType.HOTEL_CATEGORY) < 0);
        assertTrue(PersuasionType.HOTEL_CATEGORY.compareTo(PersuasionType.PEITHO) > 0);
        assertEquals(0, PersuasionType.PEITHO.compareTo(PersuasionType.PEITHO));
    }

    @Test
    public void should_WorkProperlyInSwitch_When_UsedInSwitchStatement() {
        // Arrange & Act & Assert
        assertEquals("Peitho Persuasion", getSwitchResult(PersuasionType.PEITHO));
        assertEquals("Hotel Category Persuasion", getSwitchResult(PersuasionType.HOTEL_CATEGORY));
    }

    @Test
    public void should_BeSerializable_When_EnumIsUsedInCollections() {
        // Arrange & Act
        java.util.Set<PersuasionType> persuasionTypes = java.util.EnumSet.of(PersuasionType.PEITHO);
        
        // Assert
        assertTrue(persuasionTypes.contains(PersuasionType.PEITHO));
        assertFalse(persuasionTypes.contains(PersuasionType.HOTEL_CATEGORY));
        assertEquals(1, persuasionTypes.size());
    }

    @Test
    public void should_HandleAllPersuasionTypesProperly_When_IteratingThroughValues() {
        // Arrange
        int count = 0;
        
        // Act
        for (PersuasionType persuasionType : PersuasionType.values()) {
            assertNotNull(persuasionType);
            assertNotNull(persuasionType.name());
            count++;
        }
        
        // Assert
        assertEquals(2, count);
    }

    @Test
    public void should_WorkInEnumSet_When_CreatingCompleteSet() {
        // Arrange & Act
        java.util.Set<PersuasionType> allPersuasions = java.util.EnumSet.allOf(PersuasionType.class);
        
        // Assert
        assertEquals(2, allPersuasions.size());
        assertTrue(allPersuasions.contains(PersuasionType.PEITHO));
        assertTrue(allPersuasions.contains(PersuasionType.HOTEL_CATEGORY));
    }

    @Test
    public void should_WorkInEnumMap_When_UsedAsKey() {
        // Arrange & Act
        java.util.Map<PersuasionType, String> persuasionMap = new java.util.EnumMap<>(PersuasionType.class);
        persuasionMap.put(PersuasionType.PEITHO, "Peitho-based persuasion");
        persuasionMap.put(PersuasionType.HOTEL_CATEGORY, "Category-based persuasion");
        
        // Assert
        assertEquals("Peitho-based persuasion", persuasionMap.get(PersuasionType.PEITHO));
        assertEquals("Category-based persuasion", persuasionMap.get(PersuasionType.HOTEL_CATEGORY));
        assertEquals(2, persuasionMap.size());
    }

    @Test
    public void should_BeCompatibleWithSemantics_When_UsedInBusinessLogic() {
        // Arrange & Act & Assert
        // Testing that the enum values make sense in a business context
        assertTrue("PEITHO should be the first persuasion type", PersuasionType.PEITHO.ordinal() == 0);
        assertTrue("HOTEL_CATEGORY should be an alternative persuasion type", PersuasionType.HOTEL_CATEGORY.ordinal() == 1);
    }

    @Test
    public void should_SupportBitwiseOperations_When_UsedInLogicalOperations() {
        // Arrange & Act
        boolean isPeitho = PersuasionType.PEITHO.equals(PersuasionType.PEITHO);
        boolean isHotelCategory = PersuasionType.HOTEL_CATEGORY.equals(PersuasionType.HOTEL_CATEGORY);
        
        // Assert
        assertTrue(isPeitho);
        assertTrue(isHotelCategory);
        assertFalse(PersuasionType.PEITHO.equals(PersuasionType.HOTEL_CATEGORY));
    }

    // Helper method to check if an array contains a specific value
    private boolean containsValue(PersuasionType[] values, PersuasionType target) {
        for (PersuasionType value : values) {
            if (value == target) {
                return true;
            }
        }
        return false;
    }

    // Helper method for switch testing
    private String getSwitchResult(PersuasionType persuasionType) {
        switch (persuasionType) {
            case PEITHO:
                return "Peitho Persuasion";
            case HOTEL_CATEGORY:
                return "Hotel Category Persuasion";
            default:
                return "Unknown Persuasion";
        }
    }
}
