package com.mmt.hotels.clientgateway.enums;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * Comprehensive unit tests for ReviewCategoryConstants enum
 * Ensures 100% branch coverage for all enum values and methods
 */
@RunWith(MockitoJUnitRunner.class)
public class ReviewCategoryConstantsTest {

    @Test
    public void should_HaveCorrectNumberOfValues_When_ValuesAreCalled() {
        // Arrange & Act
        ReviewCategoryConstants[] values = ReviewCategoryConstants.values();
        
        // Assert
        assertEquals(2, values.length);
    }

    @Test
    public void should_ContainAllExpectedValues_When_ValuesAreCalled() {
        // Arrange & Act
        ReviewCategoryConstants[] values = ReviewCategoryConstants.values();
        
        // Assert
        assertTrue(containsValue(values, ReviewCategoryConstants.EVERYONE));
        assertTrue(containsValue(values, ReviewCategoryConstants.BUSINESS));
    }

    @Test
    public void should_ReturnCorrectEnumValue_When_ValueOfIsCalledWithValidString() {
        // Arrange & Act & Assert
        assertEquals(ReviewCategoryConstants.EVERYONE, ReviewCategoryConstants.valueOf("EVERYONE"));
        assertEquals(ReviewCategoryConstants.BUSINESS, ReviewCategoryConstants.valueOf("BUSINESS"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void should_ThrowIllegalArgumentException_When_ValueOfIsCalledWithInvalidString() {
        // Arrange & Act
        ReviewCategoryConstants.valueOf("INVALID_CATEGORY");
        
        // Assert: Exception expected
    }

    @Test(expected = NullPointerException.class)
    public void should_ThrowNullPointerException_When_ValueOfIsCalledWithNull() {
        // Arrange & Act
        ReviewCategoryConstants.valueOf(null);
        
        // Assert: Exception expected
    }

    @Test
    public void should_ReturnCorrectStringRepresentation_When_ToStringIsCalled() {
        // Arrange & Act & Assert
        assertEquals("EVERYONE", ReviewCategoryConstants.EVERYONE.toString());
        assertEquals("BUSINESS", ReviewCategoryConstants.BUSINESS.toString());
    }

    @Test
    public void should_ReturnCorrectName_When_NameIsCalled() {
        // Arrange & Act & Assert
        assertEquals("EVERYONE", ReviewCategoryConstants.EVERYONE.name());
        assertEquals("BUSINESS", ReviewCategoryConstants.BUSINESS.name());
    }

    @Test
    public void should_ReturnCorrectOrdinal_When_OrdinalIsCalled() {
        // Arrange & Act & Assert
        assertEquals(0, ReviewCategoryConstants.EVERYONE.ordinal());
        assertEquals(1, ReviewCategoryConstants.BUSINESS.ordinal());
    }

    @Test
    public void should_BeEqualToItself_When_EqualsIsCalledWithSameInstance() {
        // Arrange & Act & Assert
        assertEquals(ReviewCategoryConstants.EVERYONE, ReviewCategoryConstants.EVERYONE);
        assertEquals(ReviewCategoryConstants.BUSINESS, ReviewCategoryConstants.BUSINESS);
    }

    @Test
    public void should_NotBeEqualToDifferentEnum_When_EqualsIsCalledWithDifferentValue() {
        // Arrange & Act & Assert
        assertNotEquals(ReviewCategoryConstants.EVERYONE, ReviewCategoryConstants.BUSINESS);
        assertNotEquals(ReviewCategoryConstants.BUSINESS, ReviewCategoryConstants.EVERYONE);
    }

    @Test
    public void should_HaveConsistentHashCode_When_HashCodeIsCalled() {
        // Arrange & Act
        int everyoneHash1 = ReviewCategoryConstants.EVERYONE.hashCode();
        int everyoneHash2 = ReviewCategoryConstants.EVERYONE.hashCode();
        
        // Assert
        assertEquals(everyoneHash1, everyoneHash2);
        assertNotEquals(ReviewCategoryConstants.EVERYONE.hashCode(), ReviewCategoryConstants.BUSINESS.hashCode());
    }

    @Test
    public void should_WorkProperlyInComparisons_When_ComparingDifferentValues() {
        // Arrange & Act & Assert
        assertTrue(ReviewCategoryConstants.EVERYONE.compareTo(ReviewCategoryConstants.BUSINESS) < 0);
        assertTrue(ReviewCategoryConstants.BUSINESS.compareTo(ReviewCategoryConstants.EVERYONE) > 0);
        assertEquals(0, ReviewCategoryConstants.EVERYONE.compareTo(ReviewCategoryConstants.EVERYONE));
    }

    @Test
    public void should_WorkProperlyInSwitch_When_UsedInSwitchStatement() {
        // Arrange & Act & Assert
        assertEquals("Everyone Category", getSwitchResult(ReviewCategoryConstants.EVERYONE));
        assertEquals("Business Category", getSwitchResult(ReviewCategoryConstants.BUSINESS));
    }

    @Test
    public void should_BeSerializable_When_EnumIsUsedInCollections() {
        // Arrange & Act
        java.util.Set<ReviewCategoryConstants> categories = java.util.EnumSet.of(ReviewCategoryConstants.EVERYONE);
        
        // Assert
        assertTrue(categories.contains(ReviewCategoryConstants.EVERYONE));
        assertFalse(categories.contains(ReviewCategoryConstants.BUSINESS));
        assertEquals(1, categories.size());
    }

    @Test
    public void should_HandleAllCategoriesProperly_When_IteratingThroughValues() {
        // Arrange
        int count = 0;
        
        // Act
        for (ReviewCategoryConstants category : ReviewCategoryConstants.values()) {
            assertNotNull(category);
            assertNotNull(category.name());
            count++;
        }
        
        // Assert
        assertEquals(2, count);
    }

    @Test
    public void should_WorkInEnumSet_When_CreatingCompleteSet() {
        // Arrange & Act
        java.util.Set<ReviewCategoryConstants> allCategories = java.util.EnumSet.allOf(ReviewCategoryConstants.class);
        
        // Assert
        assertEquals(2, allCategories.size());
        assertTrue(allCategories.contains(ReviewCategoryConstants.EVERYONE));
        assertTrue(allCategories.contains(ReviewCategoryConstants.BUSINESS));
    }

    @Test
    public void should_WorkInEnumMap_When_UsedAsKey() {
        // Arrange & Act
        java.util.Map<ReviewCategoryConstants, String> categoryMap = new java.util.EnumMap<>(ReviewCategoryConstants.class);
        categoryMap.put(ReviewCategoryConstants.EVERYONE, "General reviews");
        categoryMap.put(ReviewCategoryConstants.BUSINESS, "Business reviews");
        
        // Assert
        assertEquals("General reviews", categoryMap.get(ReviewCategoryConstants.EVERYONE));
        assertEquals("Business reviews", categoryMap.get(ReviewCategoryConstants.BUSINESS));
        assertEquals(2, categoryMap.size());
    }

    @Test
    public void should_BeCompatibleWithSemantics_When_UsedInBusinessLogic() {
        // Arrange & Act & Assert
        // Testing that the enum values make sense in a business context
        assertTrue("EVERYONE should be the first category", ReviewCategoryConstants.EVERYONE.ordinal() == 0);
        assertTrue("BUSINESS should be a specialized category", ReviewCategoryConstants.BUSINESS.ordinal() == 1);
    }

    @Test
    public void should_SupportLogicalOperations_When_UsedInConditionals() {
        // Arrange & Act
        boolean isGeneralCategory = ReviewCategoryConstants.EVERYONE.equals(ReviewCategoryConstants.EVERYONE);
        boolean isBusinessCategory = ReviewCategoryConstants.BUSINESS.equals(ReviewCategoryConstants.BUSINESS);
        boolean isDifferent = ReviewCategoryConstants.EVERYONE != ReviewCategoryConstants.BUSINESS;
        
        // Assert
        assertTrue(isGeneralCategory);
        assertTrue(isBusinessCategory);
        assertTrue(isDifferent);
    }

    @Test
    public void should_WorkWithStreamOperations_When_ProcessedAsStream() {
        // Arrange & Act
        long count = java.util.Arrays.stream(ReviewCategoryConstants.values())
            .filter(category -> category.name().length() > 5)
            .count();
        
        // Assert
        assertEquals(2, count); // Both EVERYONE and BUSINESS have more than 5 characters
    }

    @Test
    public void should_MaintainConstantBehavior_When_AccessedMultipleTimes() {
        // Arrange & Act
        ReviewCategoryConstants first = ReviewCategoryConstants.EVERYONE;
        ReviewCategoryConstants second = ReviewCategoryConstants.EVERYONE;
        
        // Assert
        assertSame("Enum constants should be the same instance", first, second);
        assertEquals("Enum constants should be equal", first, second);
        assertEquals("Hash codes should be consistent", first.hashCode(), second.hashCode());
    }

    // Helper method to check if an array contains a specific value
    private boolean containsValue(ReviewCategoryConstants[] values, ReviewCategoryConstants target) {
        for (ReviewCategoryConstants value : values) {
            if (value == target) {
                return true;
            }
        }
        return false;
    }

    // Helper method for switch testing
    private String getSwitchResult(ReviewCategoryConstants category) {
        switch (category) {
            case EVERYONE:
                return "Everyone Category";
            case BUSINESS:
                return "Business Category";
            default:
                return "Unknown Category";
        }
    }
}
