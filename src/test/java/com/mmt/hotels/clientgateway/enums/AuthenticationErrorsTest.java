package com.mmt.hotels.clientgateway.enums;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * Comprehensive unit tests for AuthenticationErrors enum
 * Ensures 100% branch coverage for all enum values and methods
 */
@RunWith(MockitoJUnitRunner.class)
public class AuthenticationErrorsTest {

    @Test
    public void should_HaveCorrectNumberOfValues_When_ValuesAreCalled() {
        // Arrange & Act
        AuthenticationErrors[] values = AuthenticationErrors.values();
        
        // Assert
        assertEquals(3, values.length);
    }

    @Test
    public void should_ContainAllExpectedValues_When_ValuesAreCalled() {
        // Arrange & Act
        AuthenticationErrors[] values = AuthenticationErrors.values();
        
        // Assert
        assertTrue(containsValue(values, AuthenticationErrors.INVALID_CLIENT));
        assertTrue(containsValue(values, AuthenticationErrors.UUID_NOT_FOUND));
        assertTrue(containsValue(values, AuthenticationErrors.CONNECTION_FAILURE));
    }

    @Test
    public void should_ReturnCorrectEnumValue_When_ValueOfIsCalledWithValidString() {
        // Arrange & Act & Assert
        assertEquals(AuthenticationErrors.INVALID_CLIENT, AuthenticationErrors.valueOf("INVALID_CLIENT"));
        assertEquals(AuthenticationErrors.UUID_NOT_FOUND, AuthenticationErrors.valueOf("UUID_NOT_FOUND"));
        assertEquals(AuthenticationErrors.CONNECTION_FAILURE, AuthenticationErrors.valueOf("CONNECTION_FAILURE"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void should_ThrowIllegalArgumentException_When_ValueOfIsCalledWithInvalidString() {
        // Arrange & Act
        AuthenticationErrors.valueOf("INVALID_ERROR");
        
        // Assert: Exception expected
    }

    @Test(expected = NullPointerException.class)
    public void should_ThrowNullPointerException_When_ValueOfIsCalledWithNull() {
        // Arrange & Act
        AuthenticationErrors.valueOf(null);
        
        // Assert: Exception expected
    }

    @Test
    public void should_ReturnCorrectErrorCode_When_GetErrorCodeIsCalled() {
        // Arrange & Act & Assert
        assertEquals("00", AuthenticationErrors.INVALID_CLIENT.getErrorCode());
        assertEquals("01", AuthenticationErrors.UUID_NOT_FOUND.getErrorCode());
        assertEquals("02", AuthenticationErrors.CONNECTION_FAILURE.getErrorCode());
    }

    @Test
    public void should_ReturnCorrectErrorMessage_When_GetErrorMsgIsCalled() {
        // Arrange & Act & Assert
        assertEquals("invalid client in headers", AuthenticationErrors.INVALID_CLIENT.getErrorMsg());
        assertEquals("User not Authenticated", AuthenticationErrors.UUID_NOT_FOUND.getErrorMsg());
        assertEquals("We could not authenticate the logged in user", AuthenticationErrors.CONNECTION_FAILURE.getErrorMsg());
    }

    @Test
    public void should_UpdateErrorCode_When_SetErrorCodeIsCalled() {
        // Arrange
        AuthenticationErrors error = AuthenticationErrors.INVALID_CLIENT;
        String originalCode = error.getErrorCode();
        String newCode = "99";
        
        // Act
        error.setErrorCode(newCode);
        
        // Assert
        assertEquals(newCode, error.getErrorCode());
        
        // Cleanup - restore original value
        error.setErrorCode(originalCode);
    }

    @Test
    public void should_UpdateErrorMessage_When_SetErrorMsgIsCalled() {
        // Arrange
        AuthenticationErrors error = AuthenticationErrors.UUID_NOT_FOUND;
        String originalMsg = error.getErrorMsg();
        String newMsg = "New error message";
        
        // Act
        error.setErrorMsg(newMsg);
        
        // Assert
        assertEquals(newMsg, error.getErrorMsg());
        
        // Cleanup - restore original value
        error.setErrorMsg(originalMsg);
    }

    @Test
    public void should_MaintainErrorCodeAfterUpdate_When_MultipleAccessesAreMade() {
        // Arrange
        AuthenticationErrors error = AuthenticationErrors.CONNECTION_FAILURE;
        String originalCode = error.getErrorCode();
        String newCode = "88";
        
        // Act
        error.setErrorCode(newCode);
        String retrievedCode1 = error.getErrorCode();
        String retrievedCode2 = error.getErrorCode();
        
        // Assert
        assertEquals(newCode, retrievedCode1);
        assertEquals(newCode, retrievedCode2);
        assertEquals(retrievedCode1, retrievedCode2);
        
        // Cleanup
        error.setErrorCode(originalCode);
    }

    @Test
    public void should_MaintainErrorMessageAfterUpdate_When_MultipleAccessesAreMade() {
        // Arrange
        AuthenticationErrors error = AuthenticationErrors.INVALID_CLIENT;
        String originalMsg = error.getErrorMsg();
        String newMsg = "Updated error message";
        
        // Act
        error.setErrorMsg(newMsg);
        String retrievedMsg1 = error.getErrorMsg();
        String retrievedMsg2 = error.getErrorMsg();
        
        // Assert
        assertEquals(newMsg, retrievedMsg1);
        assertEquals(newMsg, retrievedMsg2);
        assertEquals(retrievedMsg1, retrievedMsg2);
        
        // Cleanup
        error.setErrorMsg(originalMsg);
    }

    @Test
    public void should_ReturnCorrectStringRepresentation_When_ToStringIsCalled() {
        // Arrange & Act & Assert
        assertEquals("INVALID_CLIENT", AuthenticationErrors.INVALID_CLIENT.toString());
        assertEquals("UUID_NOT_FOUND", AuthenticationErrors.UUID_NOT_FOUND.toString());
        assertEquals("CONNECTION_FAILURE", AuthenticationErrors.CONNECTION_FAILURE.toString());
    }

    @Test
    public void should_ReturnCorrectName_When_NameIsCalled() {
        // Arrange & Act & Assert
        assertEquals("INVALID_CLIENT", AuthenticationErrors.INVALID_CLIENT.name());
        assertEquals("UUID_NOT_FOUND", AuthenticationErrors.UUID_NOT_FOUND.name());
        assertEquals("CONNECTION_FAILURE", AuthenticationErrors.CONNECTION_FAILURE.name());
    }

    @Test
    public void should_ReturnCorrectOrdinal_When_OrdinalIsCalled() {
        // Arrange & Act & Assert
        assertEquals(0, AuthenticationErrors.INVALID_CLIENT.ordinal());
        assertEquals(1, AuthenticationErrors.UUID_NOT_FOUND.ordinal());
        assertEquals(2, AuthenticationErrors.CONNECTION_FAILURE.ordinal());
    }

    @Test
    public void should_BeEqualToItself_When_EqualsIsCalledWithSameInstance() {
        // Arrange & Act & Assert
        assertEquals(AuthenticationErrors.INVALID_CLIENT, AuthenticationErrors.INVALID_CLIENT);
        assertEquals(AuthenticationErrors.UUID_NOT_FOUND, AuthenticationErrors.UUID_NOT_FOUND);
        assertEquals(AuthenticationErrors.CONNECTION_FAILURE, AuthenticationErrors.CONNECTION_FAILURE);
    }

    @Test
    public void should_NotBeEqualToDifferentEnum_When_EqualsIsCalledWithDifferentValue() {
        // Arrange & Act & Assert
        assertNotEquals(AuthenticationErrors.INVALID_CLIENT, AuthenticationErrors.UUID_NOT_FOUND);
        assertNotEquals(AuthenticationErrors.UUID_NOT_FOUND, AuthenticationErrors.CONNECTION_FAILURE);
        assertNotEquals(AuthenticationErrors.CONNECTION_FAILURE, AuthenticationErrors.INVALID_CLIENT);
    }

    @Test
    public void should_HaveConsistentHashCode_When_HashCodeIsCalled() {
        // Arrange & Act
        int invalidClientHash1 = AuthenticationErrors.INVALID_CLIENT.hashCode();
        int invalidClientHash2 = AuthenticationErrors.INVALID_CLIENT.hashCode();
        
        // Assert
        assertEquals(invalidClientHash1, invalidClientHash2);
        assertNotEquals(AuthenticationErrors.INVALID_CLIENT.hashCode(), AuthenticationErrors.UUID_NOT_FOUND.hashCode());
    }

    @Test
    public void should_WorkProperlyInComparisons_When_ComparingDifferentValues() {
        // Arrange & Act & Assert
        assertTrue(AuthenticationErrors.INVALID_CLIENT.compareTo(AuthenticationErrors.UUID_NOT_FOUND) < 0);
        assertTrue(AuthenticationErrors.UUID_NOT_FOUND.compareTo(AuthenticationErrors.INVALID_CLIENT) > 0);
        assertEquals(0, AuthenticationErrors.CONNECTION_FAILURE.compareTo(AuthenticationErrors.CONNECTION_FAILURE));
    }

    @Test
    public void should_WorkProperlyInSwitch_When_UsedInSwitchStatement() {
        // Arrange & Act & Assert
        assertEquals("Invalid Client Error", getSwitchResult(AuthenticationErrors.INVALID_CLIENT));
        assertEquals("UUID Not Found Error", getSwitchResult(AuthenticationErrors.UUID_NOT_FOUND));
        assertEquals("Connection Failure Error", getSwitchResult(AuthenticationErrors.CONNECTION_FAILURE));
    }

    @Test
    public void should_BeSerializable_When_EnumIsUsedInCollections() {
        // Arrange & Act
        java.util.Set<AuthenticationErrors> errors = java.util.EnumSet.of(
            AuthenticationErrors.INVALID_CLIENT,
            AuthenticationErrors.UUID_NOT_FOUND
        );
        
        // Assert
        assertTrue(errors.contains(AuthenticationErrors.INVALID_CLIENT));
        assertTrue(errors.contains(AuthenticationErrors.UUID_NOT_FOUND));
        assertFalse(errors.contains(AuthenticationErrors.CONNECTION_FAILURE));
        assertEquals(2, errors.size());
    }

    @Test
    public void should_HandleAllErrorsProperly_When_IteratingThroughValues() {
        // Arrange
        int count = 0;
        
        // Act
        for (AuthenticationErrors error : AuthenticationErrors.values()) {
            assertNotNull(error);
            assertNotNull(error.name());
            assertNotNull(error.getErrorCode());
            assertNotNull(error.getErrorMsg());
            count++;
        }
        
        // Assert
        assertEquals(3, count);
    }

    @Test
    public void should_WorkInEnumMap_When_UsedAsKey() {
        // Arrange & Act
        java.util.Map<AuthenticationErrors, String> errorMap = new java.util.EnumMap<>(AuthenticationErrors.class);
        errorMap.put(AuthenticationErrors.INVALID_CLIENT, "Client validation failed");
        errorMap.put(AuthenticationErrors.UUID_NOT_FOUND, "User UUID missing");
        errorMap.put(AuthenticationErrors.CONNECTION_FAILURE, "Network connection issue");
        
        // Assert
        assertEquals("Client validation failed", errorMap.get(AuthenticationErrors.INVALID_CLIENT));
        assertEquals("User UUID missing", errorMap.get(AuthenticationErrors.UUID_NOT_FOUND));
        assertEquals("Network connection issue", errorMap.get(AuthenticationErrors.CONNECTION_FAILURE));
        assertEquals(3, errorMap.size());
    }

    @Test
    public void should_HaveUniqueErrorCodes_When_AllErrorCodesAreCompared() {
        // Arrange & Act
        String code1 = AuthenticationErrors.INVALID_CLIENT.getErrorCode();
        String code2 = AuthenticationErrors.UUID_NOT_FOUND.getErrorCode();
        String code3 = AuthenticationErrors.CONNECTION_FAILURE.getErrorCode();
        
        // Assert
        assertNotEquals(code1, code2);
        assertNotEquals(code2, code3);
        assertNotEquals(code1, code3);
    }

    @Test
    public void should_HandleNullErrorCode_When_SetErrorCodeIsCalledWithNull() {
        // Arrange
        AuthenticationErrors error = AuthenticationErrors.INVALID_CLIENT;
        String originalCode = error.getErrorCode();
        
        // Act
        error.setErrorCode(null);
        
        // Assert
        assertNull(error.getErrorCode());
        
        // Cleanup
        error.setErrorCode(originalCode);
    }

    @Test
    public void should_HandleNullErrorMessage_When_SetErrorMsgIsCalledWithNull() {
        // Arrange
        AuthenticationErrors error = AuthenticationErrors.UUID_NOT_FOUND;
        String originalMsg = error.getErrorMsg();
        
        // Act
        error.setErrorMsg(null);
        
        // Assert
        assertNull(error.getErrorMsg());
        
        // Cleanup
        error.setErrorMsg(originalMsg);
    }

    @Test
    public void should_BeCompatibleWithSemantics_When_UsedInBusinessLogic() {
        // Arrange & Act & Assert
        // Testing that error codes are in ascending order
        assertTrue("Error codes should be in ascending order", 
            AuthenticationErrors.INVALID_CLIENT.getErrorCode().compareTo(AuthenticationErrors.UUID_NOT_FOUND.getErrorCode()) < 0);
        assertTrue("Error codes should be in ascending order", 
            AuthenticationErrors.UUID_NOT_FOUND.getErrorCode().compareTo(AuthenticationErrors.CONNECTION_FAILURE.getErrorCode()) < 0);
    }

    @Test
    public void should_MaintainOriginalValuesAfterModification_When_DifferentInstancesAreModified() {
        // Arrange
        String originalCode1 = AuthenticationErrors.INVALID_CLIENT.getErrorCode();
        String originalMsg1 = AuthenticationErrors.INVALID_CLIENT.getErrorMsg();
        
        // Act - Modify one instance
        AuthenticationErrors.UUID_NOT_FOUND.setErrorCode("99");
        AuthenticationErrors.UUID_NOT_FOUND.setErrorMsg("Modified message");
        
        // Assert - Other instances should remain unchanged
        assertEquals(originalCode1, AuthenticationErrors.INVALID_CLIENT.getErrorCode());
        assertEquals(originalMsg1, AuthenticationErrors.INVALID_CLIENT.getErrorMsg());
        
        // Cleanup
        AuthenticationErrors.UUID_NOT_FOUND.setErrorCode("01");
        AuthenticationErrors.UUID_NOT_FOUND.setErrorMsg("User not Authenticated");
    }

    // Helper method to check if an array contains a specific value
    private boolean containsValue(AuthenticationErrors[] values, AuthenticationErrors target) {
        for (AuthenticationErrors value : values) {
            if (value == target) {
                return true;
            }
        }
        return false;
    }

    // Helper method for switch testing
    private String getSwitchResult(AuthenticationErrors error) {
        switch (error) {
            case INVALID_CLIENT:
                return "Invalid Client Error";
            case UUID_NOT_FOUND:
                return "UUID Not Found Error";
            case CONNECTION_FAILURE:
                return "Connection Failure Error";
            default:
                return "Unknown Error";
        }
    }
}
