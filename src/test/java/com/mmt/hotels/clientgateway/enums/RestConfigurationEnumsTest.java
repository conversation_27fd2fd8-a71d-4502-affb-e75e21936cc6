package com.mmt.hotels.clientgateway.enums;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * Comprehensive unit tests for RestConfigurationEnums enum
 * Ensures 100% branch coverage for all enum values and methods
 */
@RunWith(MockitoJUnitRunner.class)
public class RestConfigurationEnumsTest {

    @Test
    public void should_ContainAllExpectedSearchConnectors_When_ValuesAreCalled() {
        // Arrange & Act
        RestConfigurationEnums[] values = RestConfigurationEnums.values();
        
        // Assert
        assertTrue(containsValue(values, RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR));
        assertTrue(containsValue(values, RestConfigurationEnums.SEARCH_ROOMS_REST_CONNECTOR));
        assertTrue(containsValue(values, RestConfigurationEnums.ORCH_SEARCH_HOTELS_CONNECTOR));
        assertTrue(containsValue(values, RestConfigurationEnums.ORCH_SEARCH_ROOMS_CONNECTOR));
    }

    @Test
    public void should_ContainAllExpectedPaymentConnectors_When_ValuesAreCalled() {
        // Arrange & Act
        RestConfigurationEnums[] values = RestConfigurationEnums.values();
        
        // Assert
        assertTrue(containsValue(values, RestConfigurationEnums.PAYMENT_CHECKOUT_REST_CONNECTOR));
        assertTrue(containsValue(values, RestConfigurationEnums.PAYMENT_CHECKOUT_REST_CONNECTOR_MOD_BOOKING));
        assertTrue(containsValue(values, RestConfigurationEnums.PAY_LATER_CONNECTOR));
    }

    @Test
    public void should_ContainAllExpectedCorporateConnectors_When_ValuesAreCalled() {
        // Arrange & Act
        RestConfigurationEnums[] values = RestConfigurationEnums.values();
        
        // Assert
        assertTrue(containsValue(values, RestConfigurationEnums.CORPORATE_WORKFLOW_DATA_CONNECTOR));
        assertTrue(containsValue(values, RestConfigurationEnums.CORP_UPDATE_APPROVAL_CONNECTOR));
        assertTrue(containsValue(values, RestConfigurationEnums.CORP_WORKFLOW_INFO_CONNECTOR));
        assertTrue(containsValue(values, RestConfigurationEnums.CORP_APPROVALS_INFO_CONNECTOR));
    }

    @Test
    public void should_ReturnCorrectEnumValue_When_ValueOfIsCalledWithValidString() {
        // Arrange & Act & Assert
        assertEquals(RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR, 
                    RestConfigurationEnums.valueOf("SEARCH_HOTELS_REST_CONNECTOR"));
        assertEquals(RestConfigurationEnums.UGC_REVIEW_CONNECTOR, 
                    RestConfigurationEnums.valueOf("UGC_REVIEW_CONNECTOR"));
        assertEquals(RestConfigurationEnums.SMART_FILTERS_CONNECTOR, 
                    RestConfigurationEnums.valueOf("SMART_FILTERS_CONNECTOR"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void should_ThrowIllegalArgumentException_When_ValueOfIsCalledWithInvalidString() {
        // Arrange & Act
        RestConfigurationEnums.valueOf("INVALID_CONNECTOR");
        
        // Assert: Exception expected
    }

    @Test(expected = NullPointerException.class)
    public void should_ThrowNullPointerException_When_ValueOfIsCalledWithNull() {
        // Arrange & Act
        RestConfigurationEnums.valueOf(null);
        
        // Assert: Exception expected
    }

    @Test
    public void should_ReturnCorrectStringRepresentation_When_ToStringIsCalled() {
        // Arrange & Act & Assert
        assertEquals("SEARCH_HOTELS_REST_CONNECTOR", 
                    RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR.toString());
        assertEquals("PAYMENT_CHECKOUT_REST_CONNECTOR", 
                    RestConfigurationEnums.PAYMENT_CHECKOUT_REST_CONNECTOR.toString());
        assertEquals("ORCH_SEARCH_HOTELS_CONNECTOR", 
                    RestConfigurationEnums.ORCH_SEARCH_HOTELS_CONNECTOR.toString());
    }

    @Test
    public void should_ReturnCorrectName_When_NameIsCalled() {
        // Arrange & Act & Assert
        assertEquals("SEARCH_HOTELS_REST_CONNECTOR", 
                    RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR.name());
        assertEquals("FILTER_COUNT_REST_CONNECTOR", 
                    RestConfigurationEnums.FILTER_COUNT_REST_CONNECTOR.name());
        assertEquals("SMART_FILTERS_CONNECTOR", 
                    RestConfigurationEnums.SMART_FILTERS_CONNECTOR.name());
    }

    @Test
    public void should_BeEqualToItself_When_EqualsIsCalledWithSameInstance() {
        // Arrange & Act & Assert
        assertEquals(RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR, 
                    RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR);
        assertEquals(RestConfigurationEnums.PAYMENT_CHECKOUT_REST_CONNECTOR, 
                    RestConfigurationEnums.PAYMENT_CHECKOUT_REST_CONNECTOR);
    }

    @Test
    public void should_NotBeEqualToDifferentEnum_When_EqualsIsCalledWithDifferentValue() {
        // Arrange & Act & Assert
        assertNotEquals(RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR, 
                       RestConfigurationEnums.SEARCH_ROOMS_REST_CONNECTOR);
        assertNotEquals(RestConfigurationEnums.PAYMENT_CHECKOUT_REST_CONNECTOR, 
                       RestConfigurationEnums.EMI_DETAILS_CONNECTOR);
    }

    @Test
    public void should_HaveConsistentHashCode_When_HashCodeIsCalled() {
        // Arrange & Act
        int searchHash1 = RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR.hashCode();
        int searchHash2 = RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR.hashCode();
        
        // Assert
        assertEquals(searchHash1, searchHash2);
        assertNotEquals(RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR.hashCode(), 
                       RestConfigurationEnums.SEARCH_ROOMS_REST_CONNECTOR.hashCode());
    }

    @Test
    public void should_WorkProperlyInComparisons_When_ComparingDifferentValues() {
        // Arrange & Act & Assert
        assertTrue(RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR.compareTo(
                  RestConfigurationEnums.SEARCH_ROOMS_REST_CONNECTOR) < 0);
        assertTrue(RestConfigurationEnums.SMART_FILTERS_CONNECTOR.compareTo(
                  RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR) > 0);
        assertEquals(0, RestConfigurationEnums.PAYMENT_CHECKOUT_REST_CONNECTOR.compareTo(
                      RestConfigurationEnums.PAYMENT_CHECKOUT_REST_CONNECTOR));
    }

    @Test
    public void should_WorkProperlyInSwitch_When_UsedInSwitchStatement() {
        // Arrange & Act & Assert
        assertEquals("Search Hotels", getSwitchResult(RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR));
        assertEquals("Payment Checkout", getSwitchResult(RestConfigurationEnums.PAYMENT_CHECKOUT_REST_CONNECTOR));
        assertEquals("UGC Review", getSwitchResult(RestConfigurationEnums.UGC_REVIEW_CONNECTOR));
        assertEquals("Other Connector", getSwitchResult(RestConfigurationEnums.FILTER_COUNT_REST_CONNECTOR));
    }

    @Test
    public void should_BeSerializable_When_EnumIsUsedInCollections() {
        // Arrange & Act
        java.util.Set<RestConfigurationEnums> connectors = java.util.EnumSet.of(
            RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR,
            RestConfigurationEnums.SEARCH_ROOMS_REST_CONNECTOR,
            RestConfigurationEnums.PAYMENT_CHECKOUT_REST_CONNECTOR
        );
        
        // Assert
        assertTrue(connectors.contains(RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR));
        assertTrue(connectors.contains(RestConfigurationEnums.SEARCH_ROOMS_REST_CONNECTOR));
        assertTrue(connectors.contains(RestConfigurationEnums.PAYMENT_CHECKOUT_REST_CONNECTOR));
        assertFalse(connectors.contains(RestConfigurationEnums.EMI_DETAILS_CONNECTOR));
        assertEquals(3, connectors.size());
    }

    @Test
    public void should_WorkInEnumMap_When_UsedAsKey() {
        // Arrange & Act
        java.util.Map<RestConfigurationEnums, String> connectorMap = new java.util.EnumMap<>(RestConfigurationEnums.class);
        connectorMap.put(RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR, "Search hotels service");
        connectorMap.put(RestConfigurationEnums.PAYMENT_CHECKOUT_REST_CONNECTOR, "Payment processing service");
        connectorMap.put(RestConfigurationEnums.UGC_REVIEW_CONNECTOR, "User generated content service");
        
        // Assert
        assertEquals("Search hotels service", connectorMap.get(RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR));
        assertEquals("Payment processing service", connectorMap.get(RestConfigurationEnums.PAYMENT_CHECKOUT_REST_CONNECTOR));
        assertEquals("User generated content service", connectorMap.get(RestConfigurationEnums.UGC_REVIEW_CONNECTOR));
        assertEquals(3, connectorMap.size());
    }

    @Test
    public void should_CategorizeConnectorsByType_When_FilteringByNamingPattern() {
        // Arrange & Act
        long searchConnectors = java.util.Arrays.stream(RestConfigurationEnums.values())
            .filter(connector -> connector.name().contains("SEARCH"))
            .count();
        
        long paymentConnectors = java.util.Arrays.stream(RestConfigurationEnums.values())
            .filter(connector -> connector.name().contains("PAYMENT"))
            .count();
        
        long orchConnectors = java.util.Arrays.stream(RestConfigurationEnums.values())
            .filter(connector -> connector.name().contains("ORCH"))
            .count();
        
        long corpConnectors = java.util.Arrays.stream(RestConfigurationEnums.values())
            .filter(connector -> connector.name().contains("CORP"))
            .count();
        
        // Assert
        assertTrue("Should have search connectors", searchConnectors > 0);
        assertTrue("Should have payment connectors", paymentConnectors > 0);
        assertTrue("Should have orchestrator connectors", orchConnectors > 0);
        assertTrue("Should have corporate connectors", corpConnectors > 0);
    }

    @Test
    public void should_SupportBusinessSemantics_When_UsedInServiceConfiguration() {
        // Arrange & Act
        boolean hasRestConnectors = RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR.name().contains("REST");
        boolean hasOrchConnectors = RestConfigurationEnums.ORCH_SEARCH_HOTELS_CONNECTOR.name().contains("ORCH");
        boolean hasDetailConnectors = RestConfigurationEnums.STATIC_DETAIL_REST_CONNECTOR.name().contains("DETAIL");
        
        // Assert
        assertTrue("Should have REST-based connectors", hasRestConnectors);
        assertTrue("Should have orchestrator connectors", hasOrchConnectors);
        assertTrue("Should have detail-specific connectors", hasDetailConnectors);
    }

    @Test
    public void should_MaintainConstantBehavior_When_AccessedMultipleTimes() {
        // Arrange & Act
        RestConfigurationEnums first = RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR;
        RestConfigurationEnums second = RestConfigurationEnums.SEARCH_HOTELS_REST_CONNECTOR;
        
        // Assert
        assertSame("Enum constants should be the same instance", first, second);
        assertEquals("Enum constants should be equal", first, second);
        assertEquals("Hash codes should be consistent", first.hashCode(), second.hashCode());
    }

    @Test
    public void should_SupportTypoCorrection_When_ConnectorNamesHaveTypos() {
        // Arrange & Act & Assert
        // These connectors have typos in their names (CNNECTOR instead of CONNECTOR)
        assertTrue(containsValue(RestConfigurationEnums.values(), RestConfigurationEnums.SEARCH_TREEL_REST_CNNECTOR));
        assertTrue(containsValue(RestConfigurationEnums.values(), RestConfigurationEnums.TREEL_FILTER_REST_CNNECTOR));
        assertTrue(containsValue(RestConfigurationEnums.values(), RestConfigurationEnums.SUBMIT_ANSWERS_CONNTECTOR));
        
        // Verify they still function correctly despite typos
        assertEquals("SEARCH_TREEL_REST_CNNECTOR", RestConfigurationEnums.SEARCH_TREEL_REST_CNNECTOR.name());
        assertEquals("TREEL_FILTER_REST_CNNECTOR", RestConfigurationEnums.TREEL_FILTER_REST_CNNECTOR.name());
        assertEquals("SUBMIT_ANSWERS_CONNTECTOR", RestConfigurationEnums.SUBMIT_ANSWERS_CONNTECTOR.name());
    }

    // Helper method to check if an array contains a specific value
    private boolean containsValue(RestConfigurationEnums[] values, RestConfigurationEnums target) {
        for (RestConfigurationEnums value : values) {
            if (value == target) {
                return true;
            }
        }
        return false;
    }

    // Helper method for switch testing
    private String getSwitchResult(RestConfigurationEnums connector) {
        switch (connector) {
            case SEARCH_HOTELS_REST_CONNECTOR:
                return "Search Hotels";
            case PAYMENT_CHECKOUT_REST_CONNECTOR:
                return "Payment Checkout";
            case UGC_REVIEW_CONNECTOR:
                return "UGC Review";
            default:
                return "Other Connector";
        }
    }
}
