package com.mmt.hotels.clientgateway.enums;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * Comprehensive unit tests for DeviceConstant enum
 * Ensures 100% branch coverage for all enum values and methods
 */
@RunWith(MockitoJUnitRunner.class)
public class DeviceConstantTest {

    @Test
    public void should_HaveCorrectNumberOfValues_When_ValuesAreCalled() {
        // Arrange & Act
        DeviceConstant[] values = DeviceConstant.values();
        
        // Assert
        assertEquals(7, values.length);
    }

    @Test
    public void should_ContainAllExpectedValues_When_ValuesAreCalled() {
        // Arrange & Act
        DeviceConstant[] values = DeviceConstant.values();
        
        // Assert
        assertTrue(containsValue(values, DeviceConstant.DESKTOP));
        assertTrue(containsValue(values, DeviceConstant.ANDROID));
        assertTrue(containsValue(values, DeviceConstant.IOS));
        assertTrue(containsValue(values, DeviceConstant.PWA));
        assertTrue(containsValue(values, DeviceConstant.COSMOS));
        assertTrue(containsValue(values, DeviceConstant.MSITE));
        assertTrue(containsValue(values, DeviceConstant.CBINTERNAL));
    }

    @Test
    public void should_ReturnCorrectEnumValue_When_ValueOfIsCalledWithValidString() {
        // Arrange & Act & Assert
        assertEquals(DeviceConstant.DESKTOP, DeviceConstant.valueOf("DESKTOP"));
        assertEquals(DeviceConstant.ANDROID, DeviceConstant.valueOf("ANDROID"));
        assertEquals(DeviceConstant.IOS, DeviceConstant.valueOf("IOS"));
        assertEquals(DeviceConstant.PWA, DeviceConstant.valueOf("PWA"));
        assertEquals(DeviceConstant.COSMOS, DeviceConstant.valueOf("COSMOS"));
        assertEquals(DeviceConstant.MSITE, DeviceConstant.valueOf("MSITE"));
        assertEquals(DeviceConstant.CBINTERNAL, DeviceConstant.valueOf("CBINTERNAL"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void should_ThrowIllegalArgumentException_When_ValueOfIsCalledWithInvalidString() {
        // Arrange & Act
        DeviceConstant.valueOf("INVALID_DEVICE");
        
        // Assert: Exception expected
    }

    @Test(expected = NullPointerException.class)
    public void should_ThrowNullPointerException_When_ValueOfIsCalledWithNull() {
        // Arrange & Act
        DeviceConstant.valueOf(null);
        
        // Assert: Exception expected
    }

    @Test
    public void should_ReturnCorrectStringRepresentation_When_ToStringIsCalled() {
        // Arrange & Act & Assert
        assertEquals("DESKTOP", DeviceConstant.DESKTOP.toString());
        assertEquals("ANDROID", DeviceConstant.ANDROID.toString());
        assertEquals("IOS", DeviceConstant.IOS.toString());
        assertEquals("PWA", DeviceConstant.PWA.toString());
        assertEquals("COSMOS", DeviceConstant.COSMOS.toString());
        assertEquals("MSITE", DeviceConstant.MSITE.toString());
        assertEquals("CBINTERNAL", DeviceConstant.CBINTERNAL.toString());
    }

    @Test
    public void should_ReturnCorrectName_When_NameIsCalled() {
        // Arrange & Act & Assert
        assertEquals("DESKTOP", DeviceConstant.DESKTOP.name());
        assertEquals("ANDROID", DeviceConstant.ANDROID.name());
        assertEquals("IOS", DeviceConstant.IOS.name());
        assertEquals("PWA", DeviceConstant.PWA.name());
        assertEquals("COSMOS", DeviceConstant.COSMOS.name());
        assertEquals("MSITE", DeviceConstant.MSITE.name());
        assertEquals("CBINTERNAL", DeviceConstant.CBINTERNAL.name());
    }

    @Test
    public void should_ReturnCorrectOrdinal_When_OrdinalIsCalled() {
        // Arrange & Act & Assert
        assertEquals(0, DeviceConstant.DESKTOP.ordinal());
        assertEquals(1, DeviceConstant.ANDROID.ordinal());
        assertEquals(2, DeviceConstant.IOS.ordinal());
        assertEquals(3, DeviceConstant.PWA.ordinal());
        assertEquals(4, DeviceConstant.COSMOS.ordinal());
        assertEquals(5, DeviceConstant.MSITE.ordinal());
        assertEquals(6, DeviceConstant.CBINTERNAL.ordinal());
    }

    @Test
    public void should_BeEqualToItself_When_EqualsIsCalledWithSameInstance() {
        // Arrange & Act & Assert
        assertEquals(DeviceConstant.DESKTOP, DeviceConstant.DESKTOP);
        assertEquals(DeviceConstant.ANDROID, DeviceConstant.ANDROID);
        assertEquals(DeviceConstant.IOS, DeviceConstant.IOS);
        assertEquals(DeviceConstant.PWA, DeviceConstant.PWA);
        assertEquals(DeviceConstant.COSMOS, DeviceConstant.COSMOS);
        assertEquals(DeviceConstant.MSITE, DeviceConstant.MSITE);
        assertEquals(DeviceConstant.CBINTERNAL, DeviceConstant.CBINTERNAL);
    }

    @Test
    public void should_NotBeEqualToDifferentEnum_When_EqualsIsCalledWithDifferentValue() {
        // Arrange & Act & Assert
        assertNotEquals(DeviceConstant.DESKTOP, DeviceConstant.ANDROID);
        assertNotEquals(DeviceConstant.IOS, DeviceConstant.PWA);
        assertNotEquals(DeviceConstant.COSMOS, DeviceConstant.MSITE);
        assertNotEquals(DeviceConstant.MSITE, DeviceConstant.CBINTERNAL);
    }

    @Test
    public void should_HaveConsistentHashCode_When_HashCodeIsCalled() {
        // Arrange & Act
        int desktopHash1 = DeviceConstant.DESKTOP.hashCode();
        int desktopHash2 = DeviceConstant.DESKTOP.hashCode();
        
        // Assert
        assertEquals(desktopHash1, desktopHash2);
        assertNotEquals(DeviceConstant.DESKTOP.hashCode(), DeviceConstant.ANDROID.hashCode());
    }

    @Test
    public void should_WorkProperlyInComparisons_When_ComparingDifferentValues() {
        // Arrange & Act & Assert
        assertTrue(DeviceConstant.DESKTOP.compareTo(DeviceConstant.ANDROID) < 0);
        assertTrue(DeviceConstant.ANDROID.compareTo(DeviceConstant.DESKTOP) > 0);
        assertEquals(0, DeviceConstant.IOS.compareTo(DeviceConstant.IOS));
    }

    @Test
    public void should_WorkProperlyInSwitch_When_UsedInSwitchStatement() {
        // Arrange & Act & Assert
        assertEquals("Desktop Device", getSwitchResult(DeviceConstant.DESKTOP));
        assertEquals("Android Device", getSwitchResult(DeviceConstant.ANDROID));
        assertEquals("iOS Device", getSwitchResult(DeviceConstant.IOS));
        assertEquals("PWA Device", getSwitchResult(DeviceConstant.PWA));
        assertEquals("Cosmos Device", getSwitchResult(DeviceConstant.COSMOS));
        assertEquals("MSite Device", getSwitchResult(DeviceConstant.MSITE));
        assertEquals("CB Internal Device", getSwitchResult(DeviceConstant.CBINTERNAL));
    }

    @Test
    public void should_BeSerializable_When_EnumIsUsedInCollections() {
        // Arrange & Act
        java.util.Set<DeviceConstant> devices = java.util.EnumSet.of(
            DeviceConstant.DESKTOP, 
            DeviceConstant.ANDROID, 
            DeviceConstant.IOS
        );
        
        // Assert
        assertTrue(devices.contains(DeviceConstant.DESKTOP));
        assertTrue(devices.contains(DeviceConstant.ANDROID));
        assertTrue(devices.contains(DeviceConstant.IOS));
        assertFalse(devices.contains(DeviceConstant.PWA));
        assertEquals(3, devices.size());
    }

    // Helper method to check if an array contains a specific value
    private boolean containsValue(DeviceConstant[] values, DeviceConstant target) {
        for (DeviceConstant value : values) {
            if (value == target) {
                return true;
            }
        }
        return false;
    }

    // Helper method for switch testing
    private String getSwitchResult(DeviceConstant device) {
        switch (device) {
            case DESKTOP:
                return "Desktop Device";
            case ANDROID:
                return "Android Device";
            case IOS:
                return "iOS Device";
            case PWA:
                return "PWA Device";
            case COSMOS:
                return "Cosmos Device";
            case MSITE:
                return "MSite Device";
            case CBINTERNAL:
                return "CB Internal Device";
            default:
                return "Unknown Device";
        }
    }
}
