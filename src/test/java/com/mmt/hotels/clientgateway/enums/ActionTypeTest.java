package com.mmt.hotels.clientgateway.enums;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * Comprehensive unit tests for ActionType enum
 * Ensures 100% branch coverage for all enum values and methods
 */
@RunWith(MockitoJUnitRunner.class)
public class ActionTypeTest {

    @Test
    public void should_HaveCorrectNumberOfValues_When_ValuesAreCalled() {
        // Arrange & Act
        ActionType[] values = ActionType.values();
        
        // Assert
        assertEquals(1, values.length);
    }

    @Test
    public void should_ContainAllExpectedValues_When_ValuesAreCalled() {
        // Arrange & Act
        ActionType[] values = ActionType.values();
        
        // Assert
        assertTrue(containsValue(values, ActionType.LISTING_MAP_POPUP));
    }

    @Test
    public void should_ReturnCorrectEnumValue_When_ValueOfIsCalledWithValidString() {
        // Arrange & Act & Assert
        assertEquals(ActionType.LISTING_MAP_POPUP, ActionType.valueOf("LISTING_MAP_POPUP"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void should_ThrowIllegalArgumentException_When_ValueOfIsCalledWithInvalidString() {
        // Arrange & Act
        ActionType.valueOf("INVALID_ACTION");
        
        // Assert: Exception expected
    }

    @Test(expected = NullPointerException.class)
    public void should_ThrowNullPointerException_When_ValueOfIsCalledWithNull() {
        // Arrange & Act
        ActionType.valueOf(null);
        
        // Assert: Exception expected
    }

    @Test
    public void should_ReturnCorrectStringRepresentation_When_ToStringIsCalled() {
        // Arrange & Act & Assert
        assertEquals("LISTING_MAP_POPUP", ActionType.LISTING_MAP_POPUP.toString());
    }

    @Test
    public void should_ReturnCorrectName_When_NameIsCalled() {
        // Arrange & Act & Assert
        assertEquals("LISTING_MAP_POPUP", ActionType.LISTING_MAP_POPUP.name());
    }

    @Test
    public void should_ReturnCorrectOrdinal_When_OrdinalIsCalled() {
        // Arrange & Act & Assert
        assertEquals(0, ActionType.LISTING_MAP_POPUP.ordinal());
    }

    @Test
    public void should_BeEqualToItself_When_EqualsIsCalledWithSameInstance() {
        // Arrange & Act & Assert
        assertEquals(ActionType.LISTING_MAP_POPUP, ActionType.LISTING_MAP_POPUP);
    }

    @Test
    public void should_HaveConsistentHashCode_When_HashCodeIsCalled() {
        // Arrange & Act
        int hash1 = ActionType.LISTING_MAP_POPUP.hashCode();
        int hash2 = ActionType.LISTING_MAP_POPUP.hashCode();
        
        // Assert
        assertEquals(hash1, hash2);
    }

    @Test
    public void should_WorkProperlyInComparisons_When_ComparingDifferentValues() {
        // Arrange & Act & Assert
        assertEquals(0, ActionType.LISTING_MAP_POPUP.compareTo(ActionType.LISTING_MAP_POPUP));
    }

    @Test
    public void should_WorkProperlyInSwitch_When_UsedInSwitchStatement() {
        // Arrange & Act
        String result = getSwitchResult(ActionType.LISTING_MAP_POPUP);
        
        // Assert
        assertEquals("LISTING_MAP_POPUP", result);
    }

    @Test
    public void should_BeSerializable_When_EnumIsUsedInCollections() {
        // Arrange & Act
        java.util.Set<ActionType> actionTypes = java.util.EnumSet.of(ActionType.LISTING_MAP_POPUP);
        
        // Assert
        assertTrue(actionTypes.contains(ActionType.LISTING_MAP_POPUP));
        assertEquals(1, actionTypes.size());
    }

    // Helper method to check if an array contains a specific value
    private boolean containsValue(ActionType[] values, ActionType target) {
        for (ActionType value : values) {
            if (value == target) {
                return true;
            }
        }
        return false;
    }

    // Helper method for switch testing
    private String getSwitchResult(ActionType actionType) {
        switch (actionType) {
            case LISTING_MAP_POPUP:
                return "LISTING_MAP_POPUP";
            default:
                return "UNKNOWN";
        }
    }
}
