package com.mmt.hotels.clientgateway.enums;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * Comprehensive unit tests for BNPLDisabledReason enum
 * Ensures 100% branch coverage for all enum values and methods
 */
@RunWith(MockitoJUnitRunner.class)
public class BNPLDisabledReasonTest {

    @Test
    public void should_HaveCorrectNumberOfValues_When_ValuesAreCalled() {
        // Arrange & Act
        BNPLDisabledReason[] values = BNPLDisabledReason.values();
        
        // Assert
        assertEquals(3, values.length);
    }

    @Test
    public void should_ContainAllExpectedValues_When_ValuesAreCalled() {
        // Arrange & Act
        BNPLDisabledReason[] values = BNPLDisabledReason.values();
        
        // Assert
        assertTrue(containsValue(values, BNPLDisabledReason.NON_BNPL_COUPON_APPLIED));
        assertTrue(containsValue(values, BNPLDisabledReason.INSURANCE_APPLIED));
        assertTrue(containsValue(values, BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD));
    }

    @Test
    public void should_ReturnCorrectEnumValue_When_ValueOfIsCalledWithValidString() {
        // Arrange & Act & Assert
        assertEquals(BNPLDisabledReason.NON_BNPL_COUPON_APPLIED, BNPLDisabledReason.valueOf("NON_BNPL_COUPON_APPLIED"));
        assertEquals(BNPLDisabledReason.INSURANCE_APPLIED, BNPLDisabledReason.valueOf("INSURANCE_APPLIED"));
        assertEquals(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD, BNPLDisabledReason.valueOf("ACTIVE_BOOKINGS_THRESHOLD"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void should_ThrowIllegalArgumentException_When_ValueOfIsCalledWithInvalidString() {
        // Arrange & Act
        BNPLDisabledReason.valueOf("INVALID_REASON");
        
        // Assert: Exception expected
    }

    @Test(expected = NullPointerException.class)
    public void should_ThrowNullPointerException_When_ValueOfIsCalledWithNull() {
        // Arrange & Act
        BNPLDisabledReason.valueOf(null);
        
        // Assert: Exception expected
    }

    @Test
    public void should_ReturnCorrectStringRepresentation_When_ToStringIsCalled() {
        // Arrange & Act & Assert
        assertEquals("NON_BNPL_COUPON_APPLIED", BNPLDisabledReason.NON_BNPL_COUPON_APPLIED.toString());
        assertEquals("INSURANCE_APPLIED", BNPLDisabledReason.INSURANCE_APPLIED.toString());
        assertEquals("ACTIVE_BOOKINGS_THRESHOLD", BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD.toString());
    }

    @Test
    public void should_ReturnCorrectName_When_NameIsCalled() {
        // Arrange & Act & Assert
        assertEquals("NON_BNPL_COUPON_APPLIED", BNPLDisabledReason.NON_BNPL_COUPON_APPLIED.name());
        assertEquals("INSURANCE_APPLIED", BNPLDisabledReason.INSURANCE_APPLIED.name());
        assertEquals("ACTIVE_BOOKINGS_THRESHOLD", BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD.name());
    }

    @Test
    public void should_ReturnCorrectOrdinal_When_OrdinalIsCalled() {
        // Arrange & Act & Assert
        assertEquals(0, BNPLDisabledReason.NON_BNPL_COUPON_APPLIED.ordinal());
        assertEquals(1, BNPLDisabledReason.INSURANCE_APPLIED.ordinal());
        assertEquals(2, BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD.ordinal());
    }

    @Test
    public void should_BeEqualToItself_When_EqualsIsCalledWithSameInstance() {
        // Arrange & Act & Assert
        assertEquals(BNPLDisabledReason.NON_BNPL_COUPON_APPLIED, BNPLDisabledReason.NON_BNPL_COUPON_APPLIED);
        assertEquals(BNPLDisabledReason.INSURANCE_APPLIED, BNPLDisabledReason.INSURANCE_APPLIED);
        assertEquals(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD, BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD);
    }

    @Test
    public void should_NotBeEqualToDifferentEnum_When_EqualsIsCalledWithDifferentValue() {
        // Arrange & Act & Assert
        assertNotEquals(BNPLDisabledReason.NON_BNPL_COUPON_APPLIED, BNPLDisabledReason.INSURANCE_APPLIED);
        assertNotEquals(BNPLDisabledReason.INSURANCE_APPLIED, BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD);
        assertNotEquals(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD, BNPLDisabledReason.NON_BNPL_COUPON_APPLIED);
    }

    @Test
    public void should_HaveConsistentHashCode_When_HashCodeIsCalled() {
        // Arrange & Act
        int couponHash1 = BNPLDisabledReason.NON_BNPL_COUPON_APPLIED.hashCode();
        int couponHash2 = BNPLDisabledReason.NON_BNPL_COUPON_APPLIED.hashCode();
        
        // Assert
        assertEquals(couponHash1, couponHash2);
        assertNotEquals(BNPLDisabledReason.NON_BNPL_COUPON_APPLIED.hashCode(), BNPLDisabledReason.INSURANCE_APPLIED.hashCode());
    }

    @Test
    public void should_WorkProperlyInComparisons_When_ComparingDifferentValues() {
        // Arrange & Act & Assert
        assertTrue(BNPLDisabledReason.NON_BNPL_COUPON_APPLIED.compareTo(BNPLDisabledReason.INSURANCE_APPLIED) < 0);
        assertTrue(BNPLDisabledReason.INSURANCE_APPLIED.compareTo(BNPLDisabledReason.NON_BNPL_COUPON_APPLIED) > 0);
        assertEquals(0, BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD.compareTo(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD));
    }

    @Test
    public void should_WorkProperlyInSwitch_When_UsedInSwitchStatement() {
        // Arrange & Act & Assert
        assertEquals("Non-BNPL coupon applied", getSwitchResult(BNPLDisabledReason.NON_BNPL_COUPON_APPLIED));
        assertEquals("Insurance applied", getSwitchResult(BNPLDisabledReason.INSURANCE_APPLIED));
        assertEquals("Active bookings threshold reached", getSwitchResult(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD));
    }

    @Test
    public void should_BeSerializable_When_EnumIsUsedInCollections() {
        // Arrange & Act
        java.util.Set<BNPLDisabledReason> reasons = java.util.EnumSet.of(
            BNPLDisabledReason.NON_BNPL_COUPON_APPLIED,
            BNPLDisabledReason.INSURANCE_APPLIED
        );
        
        // Assert
        assertTrue(reasons.contains(BNPLDisabledReason.NON_BNPL_COUPON_APPLIED));
        assertTrue(reasons.contains(BNPLDisabledReason.INSURANCE_APPLIED));
        assertFalse(reasons.contains(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD));
        assertEquals(2, reasons.size());
    }

    @Test
    public void should_HandleAllReasonsProperly_When_IteratingThroughValues() {
        // Arrange
        int count = 0;
        
        // Act
        for (BNPLDisabledReason reason : BNPLDisabledReason.values()) {
            assertNotNull(reason);
            assertNotNull(reason.name());
            count++;
        }
        
        // Assert
        assertEquals(3, count);
    }

    @Test
    public void should_WorkInEnumSet_When_CreatingCompleteSet() {
        // Arrange & Act
        java.util.Set<BNPLDisabledReason> allReasons = java.util.EnumSet.allOf(BNPLDisabledReason.class);
        
        // Assert
        assertEquals(3, allReasons.size());
        assertTrue(allReasons.contains(BNPLDisabledReason.NON_BNPL_COUPON_APPLIED));
        assertTrue(allReasons.contains(BNPLDisabledReason.INSURANCE_APPLIED));
        assertTrue(allReasons.contains(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD));
    }

    @Test
    public void should_WorkInEnumMap_When_UsedAsKey() {
        // Arrange & Act
        java.util.Map<BNPLDisabledReason, String> reasonMap = new java.util.EnumMap<>(BNPLDisabledReason.class);
        reasonMap.put(BNPLDisabledReason.NON_BNPL_COUPON_APPLIED, "Coupon incompatible");
        reasonMap.put(BNPLDisabledReason.INSURANCE_APPLIED, "Insurance conflicts");
        reasonMap.put(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD, "Too many bookings");
        
        // Assert
        assertEquals("Coupon incompatible", reasonMap.get(BNPLDisabledReason.NON_BNPL_COUPON_APPLIED));
        assertEquals("Insurance conflicts", reasonMap.get(BNPLDisabledReason.INSURANCE_APPLIED));
        assertEquals("Too many bookings", reasonMap.get(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD));
        assertEquals(3, reasonMap.size());
    }

    @Test
    public void should_BeCompatibleWithSemantics_When_UsedInBusinessLogic() {
        // Arrange & Act & Assert
        // Testing that the enum values make sense in a business context
        assertTrue("NON_BNPL_COUPON_APPLIED should be the first reason", BNPLDisabledReason.NON_BNPL_COUPON_APPLIED.ordinal() == 0);
        assertTrue("INSURANCE_APPLIED should be a distinct reason", BNPLDisabledReason.INSURANCE_APPLIED.ordinal() == 1);
        assertTrue("ACTIVE_BOOKINGS_THRESHOLD should be a limit-based reason", BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD.ordinal() == 2);
    }

    @Test
    public void should_SupportLogicalOperations_When_UsedInConditionals() {
        // Arrange & Act
        boolean isCouponReason = BNPLDisabledReason.NON_BNPL_COUPON_APPLIED.equals(BNPLDisabledReason.NON_BNPL_COUPON_APPLIED);
        boolean isInsuranceReason = BNPLDisabledReason.INSURANCE_APPLIED.equals(BNPLDisabledReason.INSURANCE_APPLIED);
        boolean isThresholdReason = BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD.equals(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD);
        
        // Assert
        assertTrue(isCouponReason);
        assertTrue(isInsuranceReason);
        assertTrue(isThresholdReason);
    }

    @Test
    public void should_WorkWithStreamOperations_When_ProcessedAsStream() {
        // Arrange & Act
        long reasonsWithUnderscore = java.util.Arrays.stream(BNPLDisabledReason.values())
            .filter(reason -> reason.name().contains("_"))
            .count();
        
        // Assert
        assertEquals(3, reasonsWithUnderscore); // All reasons contain underscores
    }

    @Test
    public void should_MaintainConstantBehavior_When_AccessedMultipleTimes() {
        // Arrange & Act
        BNPLDisabledReason first = BNPLDisabledReason.NON_BNPL_COUPON_APPLIED;
        BNPLDisabledReason second = BNPLDisabledReason.NON_BNPL_COUPON_APPLIED;
        
        // Assert
        assertSame("Enum constants should be the same instance", first, second);
        assertEquals("Enum constants should be equal", first, second);
        assertEquals("Hash codes should be consistent", first.hashCode(), second.hashCode());
    }

    // Helper method to check if an array contains a specific value
    private boolean containsValue(BNPLDisabledReason[] values, BNPLDisabledReason target) {
        for (BNPLDisabledReason value : values) {
            if (value == target) {
                return true;
            }
        }
        return false;
    }

    // Helper method for switch testing
    private String getSwitchResult(BNPLDisabledReason reason) {
        switch (reason) {
            case NON_BNPL_COUPON_APPLIED:
                return "Non-BNPL coupon applied";
            case INSURANCE_APPLIED:
                return "Insurance applied";
            case ACTIVE_BOOKINGS_THRESHOLD:
                return "Active bookings threshold reached";
            default:
                return "Unknown reason";
        }
    }
}
