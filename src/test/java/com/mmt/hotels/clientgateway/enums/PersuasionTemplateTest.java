package com.mmt.hotels.clientgateway.enums;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * Comprehensive unit tests for PersuasionTemplate enum
 * Ensures 100% branch coverage for all enum values and methods
 */
@RunWith(MockitoJUnitRunner.class)
public class PersuasionTemplateTest {

    @Test
    public void should_HaveCorrectNumberOfValues_When_ValuesAreCalled() {
        // Arrange & Act
        PersuasionTemplate[] values = PersuasionTemplate.values();
        
        // Assert
        assertEquals(3, values.length);
    }

    @Test
    public void should_ContainAllExpectedValues_When_ValuesAreCalled() {
        // Arrange & Act
        PersuasionTemplate[] values = PersuasionTemplate.values();
        
        // Assert
        assertTrue(containsValue(values, PersuasionTemplate.MULTI_PERSUASION_H));
        assertTrue(containsValue(values, PersuasionTemplate.MULTI_PERSUASION_V));
        assertTrue(containsValue(values, PersuasionTemplate.IMAGE_TEXT_H));
    }

    @Test
    public void should_ReturnCorrectEnumValue_When_ValueOfIsCalledWithValidString() {
        // Arrange & Act & Assert
        assertEquals(PersuasionTemplate.MULTI_PERSUASION_H, PersuasionTemplate.valueOf("MULTI_PERSUASION_H"));
        assertEquals(PersuasionTemplate.MULTI_PERSUASION_V, PersuasionTemplate.valueOf("MULTI_PERSUASION_V"));
        assertEquals(PersuasionTemplate.IMAGE_TEXT_H, PersuasionTemplate.valueOf("IMAGE_TEXT_H"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void should_ThrowIllegalArgumentException_When_ValueOfIsCalledWithInvalidString() {
        // Arrange & Act
        PersuasionTemplate.valueOf("INVALID_TEMPLATE");
        
        // Assert: Exception expected
    }

    @Test(expected = NullPointerException.class)
    public void should_ThrowNullPointerException_When_ValueOfIsCalledWithNull() {
        // Arrange & Act
        PersuasionTemplate.valueOf(null);
        
        // Assert: Exception expected
    }

    @Test
    public void should_ReturnCorrectStringRepresentation_When_ToStringIsCalled() {
        // Arrange & Act & Assert
        assertEquals("MULTI_PERSUASION_H", PersuasionTemplate.MULTI_PERSUASION_H.toString());
        assertEquals("MULTI_PERSUASION_V", PersuasionTemplate.MULTI_PERSUASION_V.toString());
        assertEquals("IMAGE_TEXT_H", PersuasionTemplate.IMAGE_TEXT_H.toString());
    }

    @Test
    public void should_ReturnCorrectName_When_NameIsCalled() {
        // Arrange & Act & Assert
        assertEquals("MULTI_PERSUASION_H", PersuasionTemplate.MULTI_PERSUASION_H.name());
        assertEquals("MULTI_PERSUASION_V", PersuasionTemplate.MULTI_PERSUASION_V.name());
        assertEquals("IMAGE_TEXT_H", PersuasionTemplate.IMAGE_TEXT_H.name());
    }

    @Test
    public void should_ReturnCorrectOrdinal_When_OrdinalIsCalled() {
        // Arrange & Act & Assert
        assertEquals(0, PersuasionTemplate.MULTI_PERSUASION_H.ordinal());
        assertEquals(1, PersuasionTemplate.MULTI_PERSUASION_V.ordinal());
        assertEquals(2, PersuasionTemplate.IMAGE_TEXT_H.ordinal());
    }

    @Test
    public void should_BeEqualToItself_When_EqualsIsCalledWithSameInstance() {
        // Arrange & Act & Assert
        assertEquals(PersuasionTemplate.MULTI_PERSUASION_H, PersuasionTemplate.MULTI_PERSUASION_H);
        assertEquals(PersuasionTemplate.MULTI_PERSUASION_V, PersuasionTemplate.MULTI_PERSUASION_V);
        assertEquals(PersuasionTemplate.IMAGE_TEXT_H, PersuasionTemplate.IMAGE_TEXT_H);
    }

    @Test
    public void should_NotBeEqualToDifferentEnum_When_EqualsIsCalledWithDifferentValue() {
        // Arrange & Act & Assert
        assertNotEquals(PersuasionTemplate.MULTI_PERSUASION_H, PersuasionTemplate.MULTI_PERSUASION_V);
        assertNotEquals(PersuasionTemplate.MULTI_PERSUASION_V, PersuasionTemplate.IMAGE_TEXT_H);
        assertNotEquals(PersuasionTemplate.IMAGE_TEXT_H, PersuasionTemplate.MULTI_PERSUASION_H);
    }

    @Test
    public void should_HaveConsistentHashCode_When_HashCodeIsCalled() {
        // Arrange & Act
        int multiHHash1 = PersuasionTemplate.MULTI_PERSUASION_H.hashCode();
        int multiHHash2 = PersuasionTemplate.MULTI_PERSUASION_H.hashCode();
        
        // Assert
        assertEquals(multiHHash1, multiHHash2);
        assertNotEquals(PersuasionTemplate.MULTI_PERSUASION_H.hashCode(), PersuasionTemplate.MULTI_PERSUASION_V.hashCode());
    }

    @Test
    public void should_WorkProperlyInComparisons_When_ComparingDifferentValues() {
        // Arrange & Act & Assert
        assertTrue(PersuasionTemplate.MULTI_PERSUASION_H.compareTo(PersuasionTemplate.MULTI_PERSUASION_V) < 0);
        assertTrue(PersuasionTemplate.MULTI_PERSUASION_V.compareTo(PersuasionTemplate.MULTI_PERSUASION_H) > 0);
        assertEquals(0, PersuasionTemplate.IMAGE_TEXT_H.compareTo(PersuasionTemplate.IMAGE_TEXT_H));
    }

    @Test
    public void should_WorkProperlyInSwitch_When_UsedInSwitchStatement() {
        // Arrange & Act & Assert
        assertEquals("Multi Persuasion Horizontal", getSwitchResult(PersuasionTemplate.MULTI_PERSUASION_H));
        assertEquals("Multi Persuasion Vertical", getSwitchResult(PersuasionTemplate.MULTI_PERSUASION_V));
        assertEquals("Image Text Horizontal", getSwitchResult(PersuasionTemplate.IMAGE_TEXT_H));
    }

    @Test
    public void should_BeSerializable_When_EnumIsUsedInCollections() {
        // Arrange & Act
        java.util.Set<PersuasionTemplate> templates = java.util.EnumSet.of(
            PersuasionTemplate.MULTI_PERSUASION_H,
            PersuasionTemplate.MULTI_PERSUASION_V
        );
        
        // Assert
        assertTrue(templates.contains(PersuasionTemplate.MULTI_PERSUASION_H));
        assertTrue(templates.contains(PersuasionTemplate.MULTI_PERSUASION_V));
        assertFalse(templates.contains(PersuasionTemplate.IMAGE_TEXT_H));
        assertEquals(2, templates.size());
    }

    @Test
    public void should_HandleAllTemplatesProperly_When_IteratingThroughValues() {
        // Arrange
        int count = 0;
        
        // Act
        for (PersuasionTemplate template : PersuasionTemplate.values()) {
            assertNotNull(template);
            assertNotNull(template.name());
            count++;
        }
        
        // Assert
        assertEquals(3, count);
    }

    @Test
    public void should_WorkInEnumSet_When_CreatingCompleteSet() {
        // Arrange & Act
        java.util.Set<PersuasionTemplate> allTemplates = java.util.EnumSet.allOf(PersuasionTemplate.class);
        
        // Assert
        assertEquals(3, allTemplates.size());
        assertTrue(allTemplates.contains(PersuasionTemplate.MULTI_PERSUASION_H));
        assertTrue(allTemplates.contains(PersuasionTemplate.MULTI_PERSUASION_V));
        assertTrue(allTemplates.contains(PersuasionTemplate.IMAGE_TEXT_H));
    }

    @Test
    public void should_WorkInEnumMap_When_UsedAsKey() {
        // Arrange & Act
        java.util.Map<PersuasionTemplate, String> templateMap = new java.util.EnumMap<>(PersuasionTemplate.class);
        templateMap.put(PersuasionTemplate.MULTI_PERSUASION_H, "Horizontal multi-persuasion template");
        templateMap.put(PersuasionTemplate.MULTI_PERSUASION_V, "Vertical multi-persuasion template");
        templateMap.put(PersuasionTemplate.IMAGE_TEXT_H, "Horizontal image-text template");
        
        // Assert
        assertEquals("Horizontal multi-persuasion template", templateMap.get(PersuasionTemplate.MULTI_PERSUASION_H));
        assertEquals("Vertical multi-persuasion template", templateMap.get(PersuasionTemplate.MULTI_PERSUASION_V));
        assertEquals("Horizontal image-text template", templateMap.get(PersuasionTemplate.IMAGE_TEXT_H));
        assertEquals(3, templateMap.size());
    }

    @Test
    public void should_BeCompatibleWithSemantics_When_UsedInBusinessLogic() {
        // Arrange & Act & Assert
        // Testing that the enum values make sense in a business context for persuasion templates
        assertTrue("MULTI_PERSUASION_H should be the first template", PersuasionTemplate.MULTI_PERSUASION_H.ordinal() == 0);
        assertTrue("MULTI_PERSUASION_V should be a vertical variant", PersuasionTemplate.MULTI_PERSUASION_V.ordinal() == 1);
        assertTrue("IMAGE_TEXT_H should be an image-text template", PersuasionTemplate.IMAGE_TEXT_H.ordinal() == 2);
    }

    @Test
    public void should_SupportOrientationSemantics_When_UsedInLayoutLogic() {
        // Arrange & Act
        boolean hasHorizontalTemplates = PersuasionTemplate.MULTI_PERSUASION_H.name().endsWith("_H") && 
                                        PersuasionTemplate.IMAGE_TEXT_H.name().endsWith("_H");
        boolean hasVerticalTemplates = PersuasionTemplate.MULTI_PERSUASION_V.name().endsWith("_V");
        
        // Assert
        assertTrue("Should have horizontal templates", hasHorizontalTemplates);
        assertTrue("Should have vertical templates", hasVerticalTemplates);
    }

    @Test
    public void should_WorkWithStreamOperations_When_ProcessedAsStream() {
        // Arrange & Act
        long horizontalTemplates = java.util.Arrays.stream(PersuasionTemplate.values())
            .filter(template -> template.name().endsWith("_H"))
            .count();
        
        long verticalTemplates = java.util.Arrays.stream(PersuasionTemplate.values())
            .filter(template -> template.name().endsWith("_V"))
            .count();
        
        // Assert
        assertEquals(2, horizontalTemplates); // MULTI_PERSUASION_H and IMAGE_TEXT_H
        assertEquals(1, verticalTemplates);   // MULTI_PERSUASION_V
    }

    @Test
    public void should_MaintainConstantBehavior_When_AccessedMultipleTimes() {
        // Arrange & Act
        PersuasionTemplate first = PersuasionTemplate.MULTI_PERSUASION_H;
        PersuasionTemplate second = PersuasionTemplate.MULTI_PERSUASION_H;
        
        // Assert
        assertSame("Enum constants should be the same instance", first, second);
        assertEquals("Enum constants should be equal", first, second);
        assertEquals("Hash codes should be consistent", first.hashCode(), second.hashCode());
    }

    @Test
    public void should_SupportTemplateTypeCategorization_When_UsedInDesignContext() {
        // Arrange & Act
        boolean hasMultiPersuasionTemplates = PersuasionTemplate.MULTI_PERSUASION_H.name().contains("MULTI_PERSUASION") &&
                                             PersuasionTemplate.MULTI_PERSUASION_V.name().contains("MULTI_PERSUASION");
        boolean hasImageTextTemplates = PersuasionTemplate.IMAGE_TEXT_H.name().contains("IMAGE_TEXT");
        
        // Assert
        assertTrue("Should have multi-persuasion templates", hasMultiPersuasionTemplates);
        assertTrue("Should have image-text templates", hasImageTextTemplates);
    }

    // Helper method to check if an array contains a specific value
    private boolean containsValue(PersuasionTemplate[] values, PersuasionTemplate target) {
        for (PersuasionTemplate value : values) {
            if (value == target) {
                return true;
            }
        }
        return false;
    }

    // Helper method for switch testing
    private String getSwitchResult(PersuasionTemplate template) {
        switch (template) {
            case MULTI_PERSUASION_H:
                return "Multi Persuasion Horizontal";
            case MULTI_PERSUASION_V:
                return "Multi Persuasion Vertical";
            case IMAGE_TEXT_H:
                return "Image Text Horizontal";
            default:
                return "Unknown Template";
        }
    }
}
