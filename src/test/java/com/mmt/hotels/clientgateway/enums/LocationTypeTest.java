package com.mmt.hotels.clientgateway.enums;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * Comprehensive unit tests for LocationType enum
 * Ensures 100% branch coverage for all enum values and methods
 */
@RunWith(MockitoJUnitRunner.class)
public class LocationTypeTest {

    @Test
    public void should_HaveCorrectNumberOfValues_When_ValuesAreCalled() {
        // Arrange & Act
        LocationType[] values = LocationType.values();
        
        // Assert
        assertEquals(8, values.length);
    }

    @Test
    public void should_ContainAllExpectedValues_When_ValuesAreCalled() {
        // Arrange & Act
        LocationType[] values = LocationType.values();
        
        // Assert
        assertTrue(containsValue(values, LocationType.state));
        assertTrue(containsValue(values, LocationType.region));
        assertTrue(containsValue(values, LocationType.country));
        assertTrue(containsValue(values, LocationType.storefront));
        assertTrue(containsValue(values, LocationType.city));
        assertTrue(containsValue(values, LocationType.poi));
        assertTrue(containsValue(values, LocationType.area));
        assertTrue(containsValue(values, LocationType.zone));
    }

    @Test
    public void should_ReturnCorrectEnumValue_When_ValueOfIsCalledWithValidString() {
        // Arrange & Act & Assert
        assertEquals(LocationType.state, LocationType.valueOf("state"));
        assertEquals(LocationType.region, LocationType.valueOf("region"));
        assertEquals(LocationType.country, LocationType.valueOf("country"));
        assertEquals(LocationType.storefront, LocationType.valueOf("storefront"));
        assertEquals(LocationType.city, LocationType.valueOf("city"));
        assertEquals(LocationType.poi, LocationType.valueOf("poi"));
        assertEquals(LocationType.area, LocationType.valueOf("area"));
        assertEquals(LocationType.zone, LocationType.valueOf("zone"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void should_ThrowIllegalArgumentException_When_ValueOfIsCalledWithInvalidString() {
        // Arrange & Act
        LocationType.valueOf("invalidType");
        
        // Assert: Exception expected
    }

    @Test(expected = NullPointerException.class)
    public void should_ThrowNullPointerException_When_ValueOfIsCalledWithNull() {
        // Arrange & Act
        LocationType.valueOf(null);
        
        // Assert: Exception expected
    }

    @Test
    public void should_ReturnCorrectStringRepresentation_When_ToStringIsCalled() {
        // Arrange & Act & Assert
        assertEquals("state", LocationType.state.toString());
        assertEquals("region", LocationType.region.toString());
        assertEquals("country", LocationType.country.toString());
        assertEquals("storefront", LocationType.storefront.toString());
        assertEquals("city", LocationType.city.toString());
        assertEquals("poi", LocationType.poi.toString());
        assertEquals("area", LocationType.area.toString());
        assertEquals("zone", LocationType.zone.toString());
    }

    @Test
    public void should_ReturnCorrectName_When_NameIsCalled() {
        // Arrange & Act & Assert
        assertEquals("state", LocationType.state.name());
        assertEquals("region", LocationType.region.name());
        assertEquals("country", LocationType.country.name());
        assertEquals("storefront", LocationType.storefront.name());
        assertEquals("city", LocationType.city.name());
        assertEquals("poi", LocationType.poi.name());
        assertEquals("area", LocationType.area.name());
        assertEquals("zone", LocationType.zone.name());
    }

    @Test
    public void should_ReturnCorrectOrdinal_When_OrdinalIsCalled() {
        // Arrange & Act & Assert
        assertEquals(0, LocationType.state.ordinal());
        assertEquals(1, LocationType.region.ordinal());
        assertEquals(2, LocationType.country.ordinal());
        assertEquals(3, LocationType.storefront.ordinal());
        assertEquals(4, LocationType.city.ordinal());
        assertEquals(5, LocationType.poi.ordinal());
        assertEquals(6, LocationType.area.ordinal());
        assertEquals(7, LocationType.zone.ordinal());
    }

    @Test
    public void should_BeEqualToItself_When_EqualsIsCalledWithSameInstance() {
        // Arrange & Act & Assert
        assertEquals(LocationType.state, LocationType.state);
        assertEquals(LocationType.region, LocationType.region);
        assertEquals(LocationType.country, LocationType.country);
        assertEquals(LocationType.storefront, LocationType.storefront);
        assertEquals(LocationType.city, LocationType.city);
        assertEquals(LocationType.poi, LocationType.poi);
        assertEquals(LocationType.area, LocationType.area);
        assertEquals(LocationType.zone, LocationType.zone);
    }

    @Test
    public void should_NotBeEqualToDifferentEnum_When_EqualsIsCalledWithDifferentValue() {
        // Arrange & Act & Assert
        assertNotEquals(LocationType.state, LocationType.region);
        assertNotEquals(LocationType.city, LocationType.poi);
        assertNotEquals(LocationType.area, LocationType.zone);
        assertNotEquals(LocationType.country, LocationType.storefront);
    }

    @Test
    public void should_HaveConsistentHashCode_When_HashCodeIsCalled() {
        // Arrange & Act
        int stateHash1 = LocationType.state.hashCode();
        int stateHash2 = LocationType.state.hashCode();
        
        // Assert
        assertEquals(stateHash1, stateHash2);
        assertNotEquals(LocationType.state.hashCode(), LocationType.region.hashCode());
    }

    @Test
    public void should_WorkProperlyInComparisons_When_ComparingDifferentValues() {
        // Arrange & Act & Assert
        assertTrue(LocationType.state.compareTo(LocationType.region) < 0);
        assertTrue(LocationType.region.compareTo(LocationType.state) > 0);
        assertEquals(0, LocationType.city.compareTo(LocationType.city));
    }

    // Helper method to check if an array contains a specific value
    private boolean containsValue(LocationType[] values, LocationType target) {
        for (LocationType value : values) {
            if (value == target) {
                return true;
            }
        }
        return false;
    }
}
