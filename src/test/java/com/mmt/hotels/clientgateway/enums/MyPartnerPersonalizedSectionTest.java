package com.mmt.hotels.clientgateway.enums;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * Comprehensive unit tests for MyPartnerPersonalizedSection enum
 * Ensures 100% branch coverage for all enum values and methods
 */
@RunWith(MockitoJUnitRunner.class)
public class MyPartnerPersonalizedSectionTest {

    @Test
    public void should_HaveCorrectNumberOfValues_When_ValuesAreCalled() {
        // Arrange & Act
        MyPartnerPersonalizedSection[] values = MyPartnerPersonalizedSection.values();
        
        // Assert
        assertEquals(3, values.length);
    }

    @Test
    public void should_ContainAllExpectedValues_When_ValuesAreCalled() {
        // Arrange & Act
        MyPartnerPersonalizedSection[] values = MyPartnerPersonalizedSection.values();
        
        // Assert
        assertTrue(containsValue(values, MyPartnerPersonalizedSection.RB_RC_DEFAULT));
        assertTrue(containsValue(values, MyPartnerPersonalizedSection.RB_RC_CUSTOM));
        assertTrue(containsValue(values, MyPartnerPersonalizedSection.RB_RC_NOTSHOW));
    }

    @Test
    public void should_ReturnCorrectEnumValue_When_ValueOfIsCalledWithValidString() {
        // Arrange & Act & Assert
        assertEquals(MyPartnerPersonalizedSection.RB_RC_DEFAULT, MyPartnerPersonalizedSection.valueOf("RB_RC_DEFAULT"));
        assertEquals(MyPartnerPersonalizedSection.RB_RC_CUSTOM, MyPartnerPersonalizedSection.valueOf("RB_RC_CUSTOM"));
        assertEquals(MyPartnerPersonalizedSection.RB_RC_NOTSHOW, MyPartnerPersonalizedSection.valueOf("RB_RC_NOTSHOW"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void should_ThrowIllegalArgumentException_When_ValueOfIsCalledWithInvalidString() {
        // Arrange & Act
        MyPartnerPersonalizedSection.valueOf("INVALID_SECTION");
        
        // Assert: Exception expected
    }

    @Test(expected = NullPointerException.class)
    public void should_ThrowNullPointerException_When_ValueOfIsCalledWithNull() {
        // Arrange & Act
        MyPartnerPersonalizedSection.valueOf(null);
        
        // Assert: Exception expected
    }

    @Test
    public void should_ReturnCorrectStringRepresentation_When_ToStringIsCalled() {
        // Arrange & Act & Assert
        assertEquals("RB_RC_DEFAULT", MyPartnerPersonalizedSection.RB_RC_DEFAULT.toString());
        assertEquals("RB_RC_CUSTOM", MyPartnerPersonalizedSection.RB_RC_CUSTOM.toString());
        assertEquals("RB_RC_NOTSHOW", MyPartnerPersonalizedSection.RB_RC_NOTSHOW.toString());
    }

    @Test
    public void should_ReturnCorrectName_When_NameIsCalled() {
        // Arrange & Act & Assert
        assertEquals("RB_RC_DEFAULT", MyPartnerPersonalizedSection.RB_RC_DEFAULT.name());
        assertEquals("RB_RC_CUSTOM", MyPartnerPersonalizedSection.RB_RC_CUSTOM.name());
        assertEquals("RB_RC_NOTSHOW", MyPartnerPersonalizedSection.RB_RC_NOTSHOW.name());
    }

    @Test
    public void should_ReturnCorrectOrdinal_When_OrdinalIsCalled() {
        // Arrange & Act & Assert
        assertEquals(0, MyPartnerPersonalizedSection.RB_RC_DEFAULT.ordinal());
        assertEquals(1, MyPartnerPersonalizedSection.RB_RC_CUSTOM.ordinal());
        assertEquals(2, MyPartnerPersonalizedSection.RB_RC_NOTSHOW.ordinal());
    }

    @Test
    public void should_BeEqualToItself_When_EqualsIsCalledWithSameInstance() {
        // Arrange & Act & Assert
        assertEquals(MyPartnerPersonalizedSection.RB_RC_DEFAULT, MyPartnerPersonalizedSection.RB_RC_DEFAULT);
        assertEquals(MyPartnerPersonalizedSection.RB_RC_CUSTOM, MyPartnerPersonalizedSection.RB_RC_CUSTOM);
        assertEquals(MyPartnerPersonalizedSection.RB_RC_NOTSHOW, MyPartnerPersonalizedSection.RB_RC_NOTSHOW);
    }

    @Test
    public void should_NotBeEqualToDifferentEnum_When_EqualsIsCalledWithDifferentValue() {
        // Arrange & Act & Assert
        assertNotEquals(MyPartnerPersonalizedSection.RB_RC_DEFAULT, MyPartnerPersonalizedSection.RB_RC_CUSTOM);
        assertNotEquals(MyPartnerPersonalizedSection.RB_RC_CUSTOM, MyPartnerPersonalizedSection.RB_RC_NOTSHOW);
        assertNotEquals(MyPartnerPersonalizedSection.RB_RC_NOTSHOW, MyPartnerPersonalizedSection.RB_RC_DEFAULT);
    }

    @Test
    public void should_HaveConsistentHashCode_When_HashCodeIsCalled() {
        // Arrange & Act
        int defaultHash1 = MyPartnerPersonalizedSection.RB_RC_DEFAULT.hashCode();
        int defaultHash2 = MyPartnerPersonalizedSection.RB_RC_DEFAULT.hashCode();
        
        // Assert
        assertEquals(defaultHash1, defaultHash2);
        assertNotEquals(MyPartnerPersonalizedSection.RB_RC_DEFAULT.hashCode(), MyPartnerPersonalizedSection.RB_RC_CUSTOM.hashCode());
    }

    @Test
    public void should_WorkProperlyInComparisons_When_ComparingDifferentValues() {
        // Arrange & Act & Assert
        assertTrue(MyPartnerPersonalizedSection.RB_RC_DEFAULT.compareTo(MyPartnerPersonalizedSection.RB_RC_CUSTOM) < 0);
        assertTrue(MyPartnerPersonalizedSection.RB_RC_CUSTOM.compareTo(MyPartnerPersonalizedSection.RB_RC_DEFAULT) > 0);
        assertEquals(0, MyPartnerPersonalizedSection.RB_RC_NOTSHOW.compareTo(MyPartnerPersonalizedSection.RB_RC_NOTSHOW));
    }

    @Test
    public void should_WorkProperlyInSwitch_When_UsedInSwitchStatement() {
        // Arrange & Act & Assert
        assertEquals("Default Section", getSwitchResult(MyPartnerPersonalizedSection.RB_RC_DEFAULT));
        assertEquals("Custom Section", getSwitchResult(MyPartnerPersonalizedSection.RB_RC_CUSTOM));
        assertEquals("Not Show Section", getSwitchResult(MyPartnerPersonalizedSection.RB_RC_NOTSHOW));
    }

    @Test
    public void should_BeSerializable_When_EnumIsUsedInCollections() {
        // Arrange & Act
        java.util.Set<MyPartnerPersonalizedSection> sections = java.util.EnumSet.of(
            MyPartnerPersonalizedSection.RB_RC_DEFAULT,
            MyPartnerPersonalizedSection.RB_RC_CUSTOM
        );
        
        // Assert
        assertTrue(sections.contains(MyPartnerPersonalizedSection.RB_RC_DEFAULT));
        assertTrue(sections.contains(MyPartnerPersonalizedSection.RB_RC_CUSTOM));
        assertFalse(sections.contains(MyPartnerPersonalizedSection.RB_RC_NOTSHOW));
        assertEquals(2, sections.size());
    }

    @Test
    public void should_HandleAllSectionsProperly_When_IteratingThroughValues() {
        // Arrange
        int count = 0;
        
        // Act
        for (MyPartnerPersonalizedSection section : MyPartnerPersonalizedSection.values()) {
            assertNotNull(section);
            assertNotNull(section.name());
            count++;
        }
        
        // Assert
        assertEquals(3, count);
    }

    @Test
    public void should_WorkInEnumSet_When_CreatingCompleteSet() {
        // Arrange & Act
        java.util.Set<MyPartnerPersonalizedSection> allSections = java.util.EnumSet.allOf(MyPartnerPersonalizedSection.class);
        
        // Assert
        assertEquals(3, allSections.size());
        assertTrue(allSections.contains(MyPartnerPersonalizedSection.RB_RC_DEFAULT));
        assertTrue(allSections.contains(MyPartnerPersonalizedSection.RB_RC_CUSTOM));
        assertTrue(allSections.contains(MyPartnerPersonalizedSection.RB_RC_NOTSHOW));
    }

    @Test
    public void should_WorkInEnumMap_When_UsedAsKey() {
        // Arrange & Act
        java.util.Map<MyPartnerPersonalizedSection, String> sectionMap = new java.util.EnumMap<>(MyPartnerPersonalizedSection.class);
        sectionMap.put(MyPartnerPersonalizedSection.RB_RC_DEFAULT, "Default personalized section");
        sectionMap.put(MyPartnerPersonalizedSection.RB_RC_CUSTOM, "Custom personalized section");
        sectionMap.put(MyPartnerPersonalizedSection.RB_RC_NOTSHOW, "Hidden personalized section");
        
        // Assert
        assertEquals("Default personalized section", sectionMap.get(MyPartnerPersonalizedSection.RB_RC_DEFAULT));
        assertEquals("Custom personalized section", sectionMap.get(MyPartnerPersonalizedSection.RB_RC_CUSTOM));
        assertEquals("Hidden personalized section", sectionMap.get(MyPartnerPersonalizedSection.RB_RC_NOTSHOW));
        assertEquals(3, sectionMap.size());
    }

    @Test
    public void should_BeCompatibleWithSemantics_When_UsedInBusinessLogic() {
        // Arrange & Act & Assert
        // Testing that the enum values make sense in a business context for Recently Booked and Recently Clicked
        assertTrue("RB_RC_DEFAULT should be the first section", MyPartnerPersonalizedSection.RB_RC_DEFAULT.ordinal() == 0);
        assertTrue("RB_RC_CUSTOM should be a customizable section", MyPartnerPersonalizedSection.RB_RC_CUSTOM.ordinal() == 1);
        assertTrue("RB_RC_NOTSHOW should be a hidden section", MyPartnerPersonalizedSection.RB_RC_NOTSHOW.ordinal() == 2);
    }

    @Test
    public void should_SupportLogicalOperations_When_UsedInConditionals() {
        // Arrange & Act
        boolean isDefaultSection = MyPartnerPersonalizedSection.RB_RC_DEFAULT.equals(MyPartnerPersonalizedSection.RB_RC_DEFAULT);
        boolean isCustomSection = MyPartnerPersonalizedSection.RB_RC_CUSTOM.equals(MyPartnerPersonalizedSection.RB_RC_CUSTOM);
        boolean isNotShowSection = MyPartnerPersonalizedSection.RB_RC_NOTSHOW.equals(MyPartnerPersonalizedSection.RB_RC_NOTSHOW);
        
        // Assert
        assertTrue(isDefaultSection);
        assertTrue(isCustomSection);
        assertTrue(isNotShowSection);
    }

    @Test
    public void should_WorkWithStreamOperations_When_ProcessedAsStream() {
        // Arrange & Act
        long sectionsWithRB = java.util.Arrays.stream(MyPartnerPersonalizedSection.values())
            .filter(section -> section.name().startsWith("RB_"))
            .count();
        
        // Assert
        assertEquals(3, sectionsWithRB); // All sections start with "RB_"
    }

    @Test
    public void should_MaintainConstantBehavior_When_AccessedMultipleTimes() {
        // Arrange & Act
        MyPartnerPersonalizedSection first = MyPartnerPersonalizedSection.RB_RC_DEFAULT;
        MyPartnerPersonalizedSection second = MyPartnerPersonalizedSection.RB_RC_DEFAULT;
        
        // Assert
        assertSame("Enum constants should be the same instance", first, second);
        assertEquals("Enum constants should be equal", first, second);
        assertEquals("Hash codes should be consistent", first.hashCode(), second.hashCode());
    }

    @Test
    public void should_SupportRecentlyBookedAndClickedSemantics_When_UsedInMyPartnerContext() {
        // Arrange & Act
        boolean hasRecentlyBookedSemantics = MyPartnerPersonalizedSection.RB_RC_DEFAULT.name().contains("RB");
        boolean hasRecentlyClickedSemantics = MyPartnerPersonalizedSection.RB_RC_DEFAULT.name().contains("RC");
        
        // Assert
        assertTrue("Should contain Recently Booked semantics", hasRecentlyBookedSemantics);
        assertTrue("Should contain Recently Clicked semantics", hasRecentlyClickedSemantics);
    }

    // Helper method to check if an array contains a specific value
    private boolean containsValue(MyPartnerPersonalizedSection[] values, MyPartnerPersonalizedSection target) {
        for (MyPartnerPersonalizedSection value : values) {
            if (value == target) {
                return true;
            }
        }
        return false;
    }

    // Helper method for switch testing
    private String getSwitchResult(MyPartnerPersonalizedSection section) {
        switch (section) {
            case RB_RC_DEFAULT:
                return "Default Section";
            case RB_RC_CUSTOM:
                return "Custom Section";
            case RB_RC_NOTSHOW:
                return "Not Show Section";
            default:
                return "Unknown Section";
        }
    }
}
