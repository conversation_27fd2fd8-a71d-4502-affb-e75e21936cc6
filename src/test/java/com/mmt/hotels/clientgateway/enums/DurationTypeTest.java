package com.mmt.hotels.clientgateway.enums;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * Comprehensive unit tests for DurationType enum
 * Ensures 100% branch coverage for all enum values and methods
 */
@RunWith(MockitoJUnitRunner.class)
public class DurationTypeTest {

    @Test
    public void should_HaveCorrectNumberOfValues_When_ValuesAreCalled() {
        // Arrange & Act
        DurationType[] values = DurationType.values();
        
        // Assert
        assertEquals(2, values.length);
    }

    @Test
    public void should_ContainAllExpectedValues_When_ValuesAreCalled() {
        // Arrange & Act
        DurationType[] values = DurationType.values();
        
        // Assert
        assertTrue(containsValue(values, DurationType.DAY));
        assertTrue(containsValue(values, DurationType.NIGHT));
    }

    @Test
    public void should_ReturnCorrectEnumValue_When_ValueOfIsCalledWithValidString() {
        // Arrange & Act & Assert
        assertEquals(DurationType.DAY, DurationType.valueOf("DAY"));
        assertEquals(DurationType.NIGHT, DurationType.valueOf("NIGHT"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void should_ThrowIllegalArgumentException_When_ValueOfIsCalledWithInvalidString() {
        // Arrange & Act
        DurationType.valueOf("INVALID_DURATION");
        
        // Assert: Exception expected
    }

    @Test(expected = NullPointerException.class)
    public void should_ThrowNullPointerException_When_ValueOfIsCalledWithNull() {
        // Arrange & Act
        DurationType.valueOf(null);
        
        // Assert: Exception expected
    }

    @Test
    public void should_ReturnCorrectStringRepresentation_When_ToStringIsCalled() {
        // Arrange & Act & Assert
        assertEquals("DAY", DurationType.DAY.toString());
        assertEquals("NIGHT", DurationType.NIGHT.toString());
    }

    @Test
    public void should_ReturnCorrectName_When_NameIsCalled() {
        // Arrange & Act & Assert
        assertEquals("DAY", DurationType.DAY.name());
        assertEquals("NIGHT", DurationType.NIGHT.name());
    }

    @Test
    public void should_ReturnCorrectOrdinal_When_OrdinalIsCalled() {
        // Arrange & Act & Assert
        assertEquals(0, DurationType.DAY.ordinal());
        assertEquals(1, DurationType.NIGHT.ordinal());
    }

    @Test
    public void should_BeEqualToItself_When_EqualsIsCalledWithSameInstance() {
        // Arrange & Act & Assert
        assertEquals(DurationType.DAY, DurationType.DAY);
        assertEquals(DurationType.NIGHT, DurationType.NIGHT);
    }

    @Test
    public void should_NotBeEqualToDifferentEnum_When_EqualsIsCalledWithDifferentValue() {
        // Arrange & Act & Assert
        assertNotEquals(DurationType.DAY, DurationType.NIGHT);
        assertNotEquals(DurationType.NIGHT, DurationType.DAY);
    }

    @Test
    public void should_HaveConsistentHashCode_When_HashCodeIsCalled() {
        // Arrange & Act
        int dayHash1 = DurationType.DAY.hashCode();
        int dayHash2 = DurationType.DAY.hashCode();
        
        // Assert
        assertEquals(dayHash1, dayHash2);
        assertNotEquals(DurationType.DAY.hashCode(), DurationType.NIGHT.hashCode());
    }

    @Test
    public void should_WorkProperlyInComparisons_When_ComparingDifferentValues() {
        // Arrange & Act & Assert
        assertTrue(DurationType.DAY.compareTo(DurationType.NIGHT) < 0);
        assertTrue(DurationType.NIGHT.compareTo(DurationType.DAY) > 0);
        assertEquals(0, DurationType.DAY.compareTo(DurationType.DAY));
    }

    @Test
    public void should_WorkProperlyInSwitch_When_UsedInSwitchStatement() {
        // Arrange & Act & Assert
        assertEquals("Day Duration", getSwitchResult(DurationType.DAY));
        assertEquals("Night Duration", getSwitchResult(DurationType.NIGHT));
    }

    @Test
    public void should_BeSerializable_When_EnumIsUsedInCollections() {
        // Arrange & Act
        java.util.Set<DurationType> durationTypes = java.util.EnumSet.of(DurationType.DAY);
        
        // Assert
        assertTrue(durationTypes.contains(DurationType.DAY));
        assertFalse(durationTypes.contains(DurationType.NIGHT));
        assertEquals(1, durationTypes.size());
    }

    @Test
    public void should_HandleAllDurationTypesProperly_When_IteratingThroughValues() {
        // Arrange
        int count = 0;
        
        // Act
        for (DurationType durationType : DurationType.values()) {
            assertNotNull(durationType);
            assertNotNull(durationType.name());
            count++;
        }
        
        // Assert
        assertEquals(2, count);
    }

    @Test
    public void should_WorkInEnumSet_When_CreatingCompleteSet() {
        // Arrange & Act
        java.util.Set<DurationType> allDurations = java.util.EnumSet.allOf(DurationType.class);
        
        // Assert
        assertEquals(2, allDurations.size());
        assertTrue(allDurations.contains(DurationType.DAY));
        assertTrue(allDurations.contains(DurationType.NIGHT));
    }

    @Test
    public void should_WorkInEnumMap_When_UsedAsKey() {
        // Arrange & Act
        java.util.Map<DurationType, String> durationMap = new java.util.EnumMap<>(DurationType.class);
        durationMap.put(DurationType.DAY, "Daytime");
        durationMap.put(DurationType.NIGHT, "Nighttime");
        
        // Assert
        assertEquals("Daytime", durationMap.get(DurationType.DAY));
        assertEquals("Nighttime", durationMap.get(DurationType.NIGHT));
        assertEquals(2, durationMap.size());
    }

    // Helper method to check if an array contains a specific value
    private boolean containsValue(DurationType[] values, DurationType target) {
        for (DurationType value : values) {
            if (value == target) {
                return true;
            }
        }
        return false;
    }

    // Helper method for switch testing
    private String getSwitchResult(DurationType durationType) {
        switch (durationType) {
            case DAY:
                return "Day Duration";
            case NIGHT:
                return "Night Duration";
            default:
                return "Unknown Duration";
        }
    }
}
