package com.mmt.hotels.clientgateway.util;

import org.junit.Assert;
import org.junit.Test;

public class MaskingUtilTest {

    @Test
    public void testMaskSensitiveData_nameAndEmail() {
        String json = "{\"name\":\"<PERSON>e\",\"email\":\"<EMAIL>\"}";
        String masked = MaskingUtil.maskSensitiveDataAndLog(json);
        // Both fields should be replaced by *************
        Assert.assertTrue(masked.contains("\"name\": \"*************\""));
        Assert.assertTrue(masked.contains("@example.com") == false);
    }

    @Test
    public void testMaskSensitiveData_phone() {
        String json = "{\"mobile\":\"9876543210\"}";
        String masked = MaskingUtil.maskSensitiveDataAndLog(json);
        Assert.assertTrue(masked.contains("*************"));
    }

    @Test
    public void testMaskSensitiveDataAlreadyMaskedReturnsSame() {
        String json = "{\"name\": \"*************\"}";
        String masked = MaskingUtil.maskSensitiveDataAndLog(json);
        Assert.assertEquals(json, masked);
    }

    @Test
    public void testMaskSensitiveDataWithEmptyString() {
        String result = MaskingUtil.maskSensitiveDataAndLog("");
        Assert.assertEquals("", result);
    }

    @Test
    public void testMaskSensitiveDataWithWhitespaceOnly() {
        String input = "   \t\n   ";
        String result = MaskingUtil.maskSensitiveDataAndLog(input);
        Assert.assertEquals(input, result);
    }

    @Test
    public void testMaskSensitiveDataWithComplexJson() {
        String json = "{\"user\":{\"name\":\"John Smith\",\"email\":\"<EMAIL>\",\"phone\":\"9876543210\"},\"address\":{\"street\":\"123 Main St\",\"city\":\"Mumbai\"}}";
        String masked = MaskingUtil.maskSensitiveDataAndLog(json);
        Assert.assertNotNull(masked);
        Assert.assertNotEquals(json, masked);
    }

    @Test
    public void testMaskSensitiveDataWithMultipleFields() {
        String json = "{\"personalInfo\":{\"fullName\":\"Jane Doe\",\"emailAddress\":\"<EMAIL>\",\"mobileNumber\":\"8765432109\",\"creditCard\":\"5555-5555-5555-4444\"}}";
        String masked = MaskingUtil.maskSensitiveDataAndLog(json);
        Assert.assertNotNull(masked);
        Assert.assertTrue(masked.contains("*************"));
    }

    @Test
    public void testMaskSensitiveDataConsistency() {
        String input = "{\"email\":\"<EMAIL>\",\"name\":\"Test User\"}";
        String masked1 = MaskingUtil.maskSensitiveDataAndLog(input);
        String masked2 = MaskingUtil.maskSensitiveDataAndLog(input);
        Assert.assertEquals("Masking should be consistent", masked1, masked2);
    }

    @Test
    public void testMaskSensitiveDataWithXmlFormat() {
        String xml = "<user><name>XML User</name><email><EMAIL></email><phone>7654321098</phone></user>";
        String masked = MaskingUtil.maskSensitiveDataAndLog(xml);
        Assert.assertNotNull(masked);
        Assert.assertTrue(masked.length() > 0);
    }


    @Test
    public void testMaskSensitiveDataWithLogFormat() {
        String log = "[2024-01-01 10:00:00] INFO User login: email=<EMAIL>, session=abc123, phone=9876543210";
        String masked = MaskingUtil.maskSensitiveDataAndLog(log);
        Assert.assertNotNull(masked);
        Assert.assertTrue(masked.length() > 0);
    }


    @Test
    public void testMaskSensitiveDataWithUnicodeCharacters() {
        String unicode = "{\"name\":\"राम शर्मा\",\"email\":\"ram@भारत.com\",\"phone\":\"9876543210\"}";
        String masked = MaskingUtil.maskSensitiveDataAndLog(unicode);
        Assert.assertNotNull(masked);
        Assert.assertTrue(masked.length() > 0);
    }

    @Test
    public void testMaskSensitiveDataPerformance() {
        String input = "{\"email\":\"<EMAIL>\",\"phone\":\"9876543210\"}";
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < 100; i++) {
            MaskingUtil.maskSensitiveDataAndLog(input);
        }
        long endTime = System.currentTimeMillis();
        Assert.assertTrue("Masking should complete within reasonable time", (endTime - startTime) < 1000);
    }

    @Test
    public void testMaskSensitiveDataWithVeryLongString() {
        StringBuilder longInput = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longInput.append("User ").append(i).append(": email").append(i).append("@test.com, ");
        }
        String masked = MaskingUtil.maskSensitiveDataAndLog(longInput.toString());
        Assert.assertNotNull(masked);
        Assert.assertTrue(masked.length() > 0);
    }

    @Test
    public void testMaskSensitiveDataWithNumericStrings() {
        String input = "Numbers: 1234567890, 0987654321, 1111222233334444";
        String masked = MaskingUtil.maskSensitiveDataAndLog(input);
        Assert.assertNotNull(masked);
        Assert.assertTrue(masked.length() > 0);
    }

    @Test
    public void testMaskSensitiveDataReturnType() {
        String input = "Test return type";
        String result = MaskingUtil.maskSensitiveDataAndLog(input);
        Assert.assertTrue("Return type should be String", result instanceof String);
        Assert.assertNotNull("Result should not be null", result);
    }
} 