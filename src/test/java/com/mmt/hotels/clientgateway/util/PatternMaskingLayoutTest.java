package com.mmt.hotels.clientgateway.util;

import ch.qos.logback.classic.spi.ILoggingEvent;
import com.mmt.hotels.clientgateway.util.logging.PatternMaskingLayout;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class PatternMaskingLayoutTest {

    @InjectMocks
    private PatternMaskingLayout patternMaskingLayout;

    @Before
    public void setUp() {
        patternMaskingLayout = new PatternMaskingLayout();
    }

    @Test
    public void testAddMaskPattern() {
        // Arrange
        String maskPattern = "\\d{4}";

        // Act
        patternMaskingLayout.addMaskPattern(maskPattern);

        List<String> maskPatterns = (List<String>) ReflectionTestUtils.getField(patternMaskingLayout, "maskPatterns");
        // Assert
        assertEquals(1, maskPatterns.size());
        assertEquals(maskPattern, maskPatterns.get(0));
    }

    @Test
    public void testDoLayoutWithMasking() {
        // Arrange
        String maskPattern = "\\d{4}";
        patternMaskingLayout.addMaskPattern(maskPattern);
        ILoggingEvent event = Mockito.mock(ILoggingEvent.class);

        // Act
        String result = patternMaskingLayout.doLayout(event);

        // Assert
        assertEquals("", result);
    }

    @Test
    public void testDoLayoutWithoutMasking() {
        // Arrange
        ILoggingEvent event = Mockito.mock(ILoggingEvent.class);

        // Act
        String result = patternMaskingLayout.doLayout(event);

        // Assert
        assertEquals("", result);
    }
}
