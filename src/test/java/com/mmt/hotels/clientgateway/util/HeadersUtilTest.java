package com.mmt.hotels.clientgateway.util;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class HeadersUtilTest {

    @Test
    public void testGetHeadersFromServletRequest() {
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        // Build enumeration of header names
        Vector<String> headerNames = new Vector<>(Arrays.asList("Content-Type", "host", "X-Test-Header", "Content-Length"));
        Mockito.when(request.getHeaderNames()).thenReturn(headerNames.elements());
        Mockito.when(request.getHeader("Content-Type")).thenReturn("application/json");
        Mockito.when(request.getHeader("host")).thenReturn("example.com");
        Mockito.when(request.getHeader("X-Test-Header")).thenReturn("abc");
        Mockito.when(request.getHeader("Content-Length")).thenReturn("10");

        Map<String, String> result = HeadersUtil.getHeadersFromServletRequest(request);

        // Expect host & Content-Length to be filtered out
        Assert.assertEquals(2, result.size());
        Assert.assertEquals("application/json", result.get("Content-Type"));
        Assert.assertEquals("abc", result.get("X-Test-Header"));
    }

    @Test
    public void testPrepareHttpServletResponsefromMap() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Content-Length", "123"); // should be skipped
        headers.put("Akamai-Bot", "yes"); // should set both Akamai-Bot and mmt-bot-details

        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);

        HeadersUtil.prepareHttpServletResponsefromMap(headers, response);

        // Capture setHeader calls
        ArgumentCaptor<String> nameCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> valueCaptor = ArgumentCaptor.forClass(String.class);
        Mockito.verify(response, Mockito.times(3)).setHeader(nameCaptor.capture(), valueCaptor.capture());

        List<String> names = nameCaptor.getAllValues();
        List<String> values = valueCaptor.getAllValues();

        Assert.assertTrue(names.contains("Content-Type"));
        Assert.assertTrue(names.contains("Akamai-Bot"));
        Assert.assertTrue(names.contains("mmt-bot-details"));

        int idx = names.indexOf("Content-Type");
        Assert.assertEquals("application/json", values.get(idx));
    }


    @Test
    public void testGetHeadersFromServletRequestWithEmptyHeaders() {
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        Vector<String> headerNames = new Vector<>(Arrays.asList("Content-Type", "X-Empty-Header"));
        Mockito.when(request.getHeaderNames()).thenReturn(headerNames.elements());
        Mockito.when(request.getHeader("Content-Type")).thenReturn("application/json");
        Mockito.when(request.getHeader("X-Empty-Header")).thenReturn("");

        Map<String, String> result = HeadersUtil.getHeadersFromServletRequest(request);

        Assert.assertEquals(2, result.size());
        Assert.assertEquals("application/json", result.get("Content-Type"));
        Assert.assertEquals("", result.get("X-Empty-Header"));
    }

    @Test
    public void testGetHeadersFromServletRequestWithFilteredHeaders() {
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        Vector<String> headerNames = new Vector<>(Arrays.asList("host", "content-length", "Accept", "X-Custom"));
        Mockito.when(request.getHeaderNames()).thenReturn(headerNames.elements());
        Mockito.when(request.getHeader("host")).thenReturn("example.com");
        Mockito.when(request.getHeader("content-length")).thenReturn("100");
        Mockito.when(request.getHeader("Accept")).thenReturn("application/json");
        Mockito.when(request.getHeader("X-Custom")).thenReturn("custom-value");

        Map<String, String> result = HeadersUtil.getHeadersFromServletRequest(request);

        // host and content-length should be filtered out
        Assert.assertEquals(2, result.size());
        Assert.assertEquals("application/json", result.get("Accept"));
        Assert.assertEquals("custom-value", result.get("X-Custom"));
        Assert.assertFalse(result.containsKey("host"));
        Assert.assertFalse(result.containsKey("content-length"));
    }

    @Test
    public void testPrepareHttpServletResponsefromMapWithEmptyValues() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("X-Empty-Header", "");
        headers.put("Authorization", "Bearer token");

        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);

        HeadersUtil.prepareHttpServletResponsefromMap(headers, response);

        // All headers including empty ones should be set
        ArgumentCaptor<String> nameCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> valueCaptor = ArgumentCaptor.forClass(String.class);
        Mockito.verify(response, Mockito.times(3)).setHeader(nameCaptor.capture(), valueCaptor.capture());

        List<String> names = nameCaptor.getAllValues();
        Assert.assertTrue(names.contains("Content-Type"));
        Assert.assertTrue(names.contains("X-Empty-Header"));
        Assert.assertTrue(names.contains("Authorization"));
    }

    @Test
    public void testPrepareHttpServletResponsefromMapWithSpecialHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Akamai-Bot", "detected");
        headers.put("X-Special-Header", "special-value");

        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);

        HeadersUtil.prepareHttpServletResponsefromMap(headers, response);

        // Should set Content-Type, X-Special-Header, Akamai-Bot, and mmt-bot-details
        ArgumentCaptor<String> nameCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> valueCaptor = ArgumentCaptor.forClass(String.class);
        Mockito.verify(response, Mockito.times(4)).setHeader(nameCaptor.capture(), valueCaptor.capture());

        List<String> names = nameCaptor.getAllValues();
        Assert.assertTrue(names.contains("Content-Type"));
        Assert.assertTrue(names.contains("X-Special-Header"));
        Assert.assertTrue(names.contains("Akamai-Bot"));
        Assert.assertTrue(names.contains("mmt-bot-details"));
    }

    @Test
    public void testPrepareHttpServletResponsefromMapFiltersContentLength() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Content-Length", "1024");
        headers.put("Accept", "application/xml");

        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);

        HeadersUtil.prepareHttpServletResponsefromMap(headers, response);

        // Content-Length should be filtered out
        ArgumentCaptor<String> nameCaptor = ArgumentCaptor.forClass(String.class);
        Mockito.verify(response, Mockito.times(2)).setHeader(nameCaptor.capture(), Mockito.any());

        List<String> names = nameCaptor.getAllValues();
        Assert.assertTrue(names.contains("Content-Type"));
        Assert.assertTrue(names.contains("Accept"));
        Assert.assertFalse(names.contains("Content-Length"));
    }

    @Test
    public void testGetHeadersFromServletRequestWithCaseSensitiveHeaders() {
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        Vector<String> headerNames = new Vector<>(Arrays.asList("content-type", "CONTENT-TYPE", "User-Agent"));
        Mockito.when(request.getHeaderNames()).thenReturn(headerNames.elements());
        Mockito.when(request.getHeader("content-type")).thenReturn("application/json");
        Mockito.when(request.getHeader("CONTENT-TYPE")).thenReturn("text/html");
        Mockito.when(request.getHeader("User-Agent")).thenReturn("TestAgent/1.0");

        Map<String, String> result = HeadersUtil.getHeadersFromServletRequest(request);

        Assert.assertEquals(3, result.size());
        Assert.assertEquals("application/json", result.get("content-type"));
        Assert.assertEquals("text/html", result.get("CONTENT-TYPE"));
        Assert.assertEquals("TestAgent/1.0", result.get("User-Agent"));
    }

    @Test
    public void testGetHeadersFromServletRequestWithEmptyHeaderNames() {
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        Vector<String> headerNames = new Vector<>();
        Mockito.when(request.getHeaderNames()).thenReturn(headerNames.elements());

        Map<String, String> result = HeadersUtil.getHeadersFromServletRequest(request);

        Assert.assertNotNull(result);
        Assert.assertEquals(0, result.size());
    }

    @Test
    public void testPrepareHttpServletResponsefromMapWithEmptyMap() {
        Map<String, String> headers = new HashMap<>();
        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);

        HeadersUtil.prepareHttpServletResponsefromMap(headers, response);

        // No headers should be set
        Mockito.verify(response, Mockito.never()).setHeader(Mockito.any(), Mockito.any());
    }
} 