package com.mmt.hotels.clientgateway.util;

import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.runners.MockitoJUnitRunner;

import java.text.ParseException;

@RunWith(MockitoJUnitRunner.class)
public class DateUtilTest {
    @InjectMocks
    DateUtil dateUtil;

    @Test
    public void getDateFormattedTest(){
        Assert.assertNotNull(dateUtil.getDateFormatted("21/10/2021","dd/MM/yyyy","dd-MM-yyyy"));
    }

    @Test
    public void convertEpochToDateTimeTest() {
        long epoch = new DateTime().getMillis();
        String format = "dd MMM,hh:mm a ";
        String tillDate = dateUtil.convertEpochToDateTime(epoch,format);
        Assert.assertNotNull(tillDate);
    }

    @Test
    public void getDaysDiffTest() throws ParseException {
        Assert.assertNotNull(dateUtil.getDaysDiff("2022-10-21","2022-10-25"));
        Assert.assertNotNull(dateUtil.getDaysDiff("2022-10-05"));
        Assert.assertEquals(1,dateUtil.getDaysDiff("2024-07-09","2024-07-10"));
        Assert.assertEquals(10,dateUtil.getDaysDiff("2024-07-09","2024-07-19"));
        Assert.assertEquals(10,dateUtil.getDaysDiff("2024-07-09 12:09:20","2024-07-19 01:09:20"));
        Assert.assertEquals(1,dateUtil.getDaysDiff("20-07-2022 12:09:20","21-07-2022 01:09:20"));
        Assert.assertEquals(1,dateUtil.getDaysDiff("20/07/2022 12:09:20","21/07/2022 01:09:20"));
    }

    @Test
    public void getEpochTimeFromDateString_test() {
        String format = "yyyy-MM-dd";
        String date = "2023-07-02";
        long epochDate = dateUtil.getEpochTimeFromDateString(date,format);
        Assert.assertNotNull(epochDate);
    }

    @Test
    public void convertToTimestampTest() {
        long res = dateUtil.convertToTimestamp("2024-05-21 22:29:59");
        Assert.assertNotNull(res);
    }

    @Test
    public void convertToTimestampTest2() {
        long res = dateUtil.convertToTimestamp("2024-05-2x 22:29:59");
        Assert.assertEquals(res, 0);
    }
    @Test
    public void testConcatDate() {
        String startDate = "2023-07-02";
        String endDate = "2023-07-05";
        String expected = "Sun, 02 Jul - Wed, 05 Jul";
        String result = dateUtil.concatDate(startDate, endDate);
        Assert.assertEquals(expected, result);
    }
    @Test
    public void testConcatDateException() {
        String startDate = "2023-07-02";
        String endDate = "2023-07";
        String expected = "";
        String result = dateUtil.concatDate(startDate, endDate);
        Assert.assertEquals(expected, result);
    }

    // Additional test cases for improved code coverage

    @Test
    public void testGetDateFormattedWithNullInput() {
        String result = dateUtil.getDateFormatted(null, "dd/MM/yyyy", "dd-MM-yyyy");
        Assert.assertNull("Should return null for null input", result);
    }

    @Test
    public void testGetDateFormattedWithEmptyInput() {
        String result = dateUtil.getDateFormatted("", "dd/MM/yyyy", "dd-MM-yyyy");
        Assert.assertNull("Should return null for empty input", result);
    }

    @Test
    public void testGetDateFormattedWithInvalidInputFormat() {
        String result = dateUtil.getDateFormatted("invalid-date", "dd/MM/yyyy", "dd-MM-yyyy");
        Assert.assertNull("Should return null for invalid date format", result);
    }

    @Test
    public void testGetDateFormattedWithDifferentFormats() {
        String result = dateUtil.getDateFormatted("2021-10-21", "yyyy-MM-dd", "MMM dd, yyyy");
        Assert.assertNotNull("Should format date correctly", result);
        Assert.assertTrue("Should contain month name", result.contains("Oct"));
    }

    @Test
    public void testConvertEpochToDateTimeWithZeroEpoch() {
        String result = dateUtil.convertEpochToDateTime(0L, "dd MMM yyyy");
        Assert.assertNotNull("Should handle zero epoch", result);
    }

    @Test
    public void testConvertEpochToDateTimeWithNegativeEpoch() {
        String result = dateUtil.convertEpochToDateTime(-1000L, "dd MMM yyyy");
        Assert.assertNotNull("Should handle negative epoch", result);
    }

    @Test
    public void testConvertEpochToDateTimeWithCustomFormat() {
        long currentEpoch = System.currentTimeMillis();
        String result = dateUtil.convertEpochToDateTime(currentEpoch, "yyyy-MM-dd HH:mm:ss");
        Assert.assertNotNull("Should format with custom format", result);
        Assert.assertTrue("Should contain year", result.contains("202"));
    }

    @Test
    public void testGetDaysDiffWithNullStartDate() throws ParseException {
        int result = dateUtil.getDaysDiff(null, "2022-10-25");
        Assert.assertEquals("Should return 1 for null start date", 1, result);
    }

    @Test
    public void testGetDaysDiffWithNullEndDate() throws ParseException {
        int result = dateUtil.getDaysDiff("2022-10-21", null);
        Assert.assertEquals("Should return 1 for null end date", 1, result);
    }

    @Test
    public void testGetDaysDiffWithSameDates() throws ParseException {
        int result = dateUtil.getDaysDiff("2022-10-21", "2022-10-21");
        Assert.assertEquals("Should return 0 for same dates", 0, result);
    }


    @Test
    public void testGetDaysDiffWithInvalidDateFormat() throws ParseException {
        int result = dateUtil.getDaysDiff("invalid-date", "2022-10-25");
        Assert.assertEquals("Should return 0 for invalid date format", 1, result);
    }

    @Test
    public void testGetDaysDiffSingleDateWithCurrentDate() throws ParseException {
        String today = new DateTime().toString("yyyy-MM-dd");
        int result = dateUtil.getDaysDiff(today);
        Assert.assertEquals("Should return 0 for current date", 0, result);
    }

    @Test
    public void testGetDaysDiffSingleDateWithFutureDate() throws ParseException {
        String futureDate = new DateTime().plusDays(5).toString("yyyy-MM-dd");
        int result = dateUtil.getDaysDiff(futureDate);
        Assert.assertTrue("Should return positive for future date", result > 0);
    }

    @Test
    public void testGetEpochTimeFromDateStringWithNullDate() {
        long result = dateUtil.getEpochTimeFromDateString(null, "yyyy-MM-dd");
        Assert.assertEquals("Should return 0 for null date", 0L, result);
    }

    @Test
    public void testGetEpochTimeFromDateStringWithEmptyDate() {
        long result = dateUtil.getEpochTimeFromDateString("", "yyyy-MM-dd");
        Assert.assertEquals("Should return 0 for empty date", 0L, result);
    }

    @Test
    public void testGetEpochTimeFromDateStringWithInvalidFormat() {
        long result = dateUtil.getEpochTimeFromDateString("2023-07-02", "invalid-format");
        Assert.assertEquals("Should return 0 for invalid format", 0L, result);
    }

    @Test
    public void testGetEpochTimeFromDateStringWithMismatchedFormat() {
        long result = dateUtil.getEpochTimeFromDateString("02/07/2023", "yyyy-MM-dd");
        Assert.assertEquals("Should return 0 for mismatched format", 0L, result);
    }

    @Test
    public void testConvertToTimestampWithNullInput() {
        long result = dateUtil.convertToTimestamp(null);
        Assert.assertEquals("Should return 0 for null input", 0L, result);
    }

    @Test
    public void testConvertToTimestampWithEmptyInput() {
        long result = dateUtil.convertToTimestamp("");
        Assert.assertEquals("Should return 0 for empty input", 0L, result);
    }

    @Test
    public void testConvertToTimestampWithValidInput() {
        long result = dateUtil.convertToTimestamp("2024-05-21 22:29:59");
        Assert.assertTrue("Should return positive timestamp for valid input", result > 0);
    }

    @Test
    public void testConvertToTimestampWithPartiallyInvalidInput() {
        long result = dateUtil.convertToTimestamp("2024-05-21 22:29");
        Assert.assertTrue("Should handle partially valid timestamp format", result >= 0);
    }

    @Test
    public void testConcatDateWithEmptyDates() {
        String result = dateUtil.concatDate("", "");
        Assert.assertEquals("Should return empty string for empty dates", "", result);
    }

    @Test
    public void testConcatDateWithSameDates() {
        String result = dateUtil.concatDate("2023-07-02", "2023-07-02");
        Assert.assertNotNull("Should handle same start and end dates", result);
        Assert.assertTrue("Should contain date information", result.length() > 0);
    }

    @Test
    public void testConcatDateWithYearCrossing() {
        String result = dateUtil.concatDate("2023-12-30", "2024-01-02");
        Assert.assertNotNull("Should handle year crossing", result);
        Assert.assertTrue("Should contain date information", result.length() > 0);
    }

    @Test
    public void testDateFormattingConsistency() {
        String inputDate = "15/03/2022";
        String result1 = dateUtil.getDateFormatted(inputDate, "dd/MM/yyyy", "yyyy-MM-dd");
        String result2 = dateUtil.getDateFormatted(inputDate, "dd/MM/yyyy", "yyyy-MM-dd");
        Assert.assertEquals("Date formatting should be consistent", result1, result2);
    }

    @Test
    public void testEpochConversionRoundTrip() {
        String originalDate = "2023-07-02";
        long epoch = dateUtil.getEpochTimeFromDateString(originalDate, "yyyy-MM-dd");
        String convertedBack = dateUtil.convertEpochToDateTime(epoch, "yyyy-MM-dd");
        
        if (epoch > 0) {
            Assert.assertNotNull("Round trip conversion should work", convertedBack);
            Assert.assertTrue("Should contain year", convertedBack.contains("2023"));
        }
    }
}

