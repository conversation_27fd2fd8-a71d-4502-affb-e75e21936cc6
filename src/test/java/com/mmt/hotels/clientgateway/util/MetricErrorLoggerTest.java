package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import java.util.HashMap;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class MetricErrorLoggerTest {

    @InjectMocks
    private MetricErrorLogger metricErrorLogger;

    @Mock
    private MetricAspect metricAspect;

    /**
     * Verifies that {@link MetricErrorLogger#logErrorInMetric(MetricError, Map)} builds the correct
     * error-code string and delegates to {@link MetricAspect#addToCounter(DependencyLayer, ErrorType, String)}.
     */
    @Test
    public void testLogErrorInMetricDelegatesToMetricAspect() {
        // Arrange
        ReflectionTestUtils.setField(metricErrorLogger, "enableKafkaLogging", false);
        MetricError metricError = new MetricError(DependencyLayer.CLIENTGATEWAY, ErrorType.UNEXPECTED, "123", "test-msg");
        Map<String, String> ctx = new HashMap<>();
        ctx.put(MDCHelper.MDCKeys.CORRELATION.getStringValue(), "corrId");
        ctx.put(MDCHelper.MDCKeys.CONTROLLER.getStringValue(), "/cg/test");

        // Act
        metricErrorLogger.logErrorInMetric(metricError, ctx);

        // Assert
        ArgumentCaptor<DependencyLayer> depCaptor = ArgumentCaptor.forClass(DependencyLayer.class);
        ArgumentCaptor<ErrorType> typeCaptor = ArgumentCaptor.forClass(ErrorType.class);
        ArgumentCaptor<String> codeCaptor = ArgumentCaptor.forClass(String.class);
        Mockito.verify(metricAspect, Mockito.times(1))
                .addToCounter(depCaptor.capture(), typeCaptor.capture(), codeCaptor.capture());

        Assert.assertEquals(DependencyLayer.CLIENTGATEWAY, depCaptor.getValue());
        Assert.assertEquals(ErrorType.UNEXPECTED, typeCaptor.getValue());
        // expected code : "2" (api prefix) + layer code + errorType code + errorCode
        String expectedErrorCode = "2" + DependencyLayer.CLIENTGATEWAY.getCode() + ErrorType.UNEXPECTED.getCode() + "123";
        Assert.assertEquals(expectedErrorCode, codeCaptor.getValue());
    }

    /**
     * Directly invokes the private getMetricErrorCodes(..) method via reflection and asserts that
     * all mandatory fields are populated correctly.
     */
    @Test
    public void testGetMetricErrorCodesPopulatesFields() {
        // Arrange
        MetricError metricError = new MetricError(DependencyLayer.CLIENTBACKEND, ErrorType.CONNECTIVITY, "999", "downstream-failure");
        Map<String, String> ctx = new HashMap<>();
        ctx.put(MDCHelper.MDCKeys.CORRELATION.getStringValue(), "corr-2");
        ctx.put(MDCHelper.MDCKeys.CONTROLLER.getStringValue(), "/cg/listing");

        ReflectionTestUtils.setField(metricErrorLogger, "metricErrorLogTopicId", "48");
        ReflectionTestUtils.setField(metricErrorLogger, "metricErrorLogTemplateId", "10072");

        // Act
        MetricErrorCodes codes = ReflectionTestUtils.invokeMethod(metricErrorLogger, "getMetricErrorCodes", metricError, ctx);

        // Assert
        Assert.assertEquals("48", codes.getM2());
        Assert.assertEquals("10072", codes.getTpl1());
        Assert.assertEquals("corr-2", codes.getApi208());
        String expectedErrorCode = "2" + DependencyLayer.CLIENTBACKEND.getCode() + ErrorType.CONNECTIVITY.getCode() + "999";
        Assert.assertEquals(expectedErrorCode, codes.getPs438());
        Assert.assertEquals("downstream-failure", codes.getPd147());
        Assert.assertEquals(DependencyLayer.CLIENTBACKEND.name(), codes.getPm626());
        // Controller url should match the provided context map value
        Assert.assertEquals("/cg/listing", codes.getM101());
    }
} 