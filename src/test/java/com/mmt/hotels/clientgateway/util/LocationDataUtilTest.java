package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.model.response.staticdata.BbLatLong;
import com.mmt.hotels.model.response.staticdata.BboxLocationDetails;
import com.mmt.hotels.model.response.staticdata.LatLong;
import com.mmt.hotels.pojo.LocationRecommendation.LocationData;
import com.mmt.hotels.pojo.LocationRecommendation.LocationDataCategory;
import com.mmt.hotels.pojo.LocationRecommendation.LocationDataTag;
import com.mmt.hotels.pojo.LocationRecommendation.LocationDataTagList;
import junit.framework.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class LocationDataUtilTest {

    @InjectMocks
    LocationDataUtil locationDataUtil;

    @Test
    public void testBuildLocationData(){
        LocationDataTag locationDataTag = new LocationDataTag();
        locationDataTag.setMatchmakerType("AREA");
        locationDataTag.setMatchmakerId("1234");
        LocationDataTagList locationDataTagList = new LocationDataTagList();
        locationDataTagList.setTags(Arrays.asList(locationDataTag));
        LocationDataCategory locationDataCategory = new LocationDataCategory();
        locationDataCategory.setTags(Arrays.asList(locationDataTagList));
        com.mmt.hotels.pojo.LocationRecommendation.LocationData locationDataHES = new LocationData();
        locationDataHES.setCategory(Arrays.asList(locationDataCategory));

        com.mmt.hotels.clientgateway.response.moblanding.LocationData result = locationDataUtil.buildLocationData(locationDataHES);

        Assert.assertNotNull(result);
        Assert.assertEquals("1234",result.getCategory().get(0).getTags().get(0).getMatchmakerId());
    }

    @Test
    public void testBuildLocationData_NullData(){
        com.mmt.hotels.pojo.LocationRecommendation.LocationData locationDataHES = null;
        com.mmt.hotels.clientgateway.response.moblanding.LocationData result = locationDataUtil.buildLocationData(locationDataHES);
        Assert.assertNull(result);
    }
    @Test
    public void testBuildLocationData_EmptyData(){
        com.mmt.hotels.pojo.LocationRecommendation.LocationData locationDataHES = new LocationData();
        com.mmt.hotels.clientgateway.response.moblanding.LocationData result = locationDataUtil.buildLocationData(locationDataHES);
        Assert.assertNull(result);
    }
    @Test
    public void testBuildLocationDataCategory_ValidCategoriesAndTags() {
        List<LocationDataCategory> inputList = new ArrayList<>();
        com.mmt.hotels.pojo.LocationRecommendation.LocationDataCategory category = new com.mmt.hotels.pojo.LocationRecommendation.LocationDataCategory();
        category.setCategoryId("1");
        category.setDesc("Test Category");
        LocationDataTagList tagList = new LocationDataTagList();
        tagList.setLocusType("AREA_POI");
        LocationDataTag tag = new LocationDataTag();
        tag.setMatchmakerType("area");
        tag.setMatchmakerId("1234");
        tagList.setTags(Arrays.asList(tag));
        category.setTags(Arrays.asList(tagList));

        inputList.add(category);

        List<com.mmt.hotels.clientgateway.response.moblanding.LocationDataCategory> result = ReflectionTestUtils.invokeMethod(locationDataUtil, "buildLocationDataCategory",inputList);

        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
        Assert.assertEquals("1", result.get(0).getCategoryId());
        Assert.assertEquals("Test Category", result.get(0).getDesc());
        Assert.assertEquals("1234", result.get(0).getTags().get(0).getMatchmakerId());
    }

    @Test
    public void testBuildLocationDataCategory_CityCategoriesAndTags() {
        List<com.mmt.hotels.pojo.LocationRecommendation.LocationDataCategory> inputList = new ArrayList<>();
        com.mmt.hotels.pojo.LocationRecommendation.LocationDataCategory category = new com.mmt.hotels.pojo.LocationRecommendation.LocationDataCategory();
        category.setCategoryId("1");
        category.setDesc("Test Category");
        LocationDataTagList tagList = new LocationDataTagList();
        tagList.setLocusType("CITY");
        LocationDataTag tag = new LocationDataTag();
        tag.setMatchmakerType("area");
        tag.setMatchmakerId("1234");
        tagList.setTags(Arrays.asList(tag));
        category.setTags(Arrays.asList(tagList));

        inputList.add(category);

        List<LocationDataCategory> result = ReflectionTestUtils.invokeMethod(locationDataUtil, "buildLocationDataCategory",inputList);

        Assert.assertNull(result);
    }

    @Test
    public void testBuildCentreWithBbox(){
        BboxLocationDetails metaHES	= new BboxLocationDetails();
        BbLatLong bbox = new BbLatLong();
        LatLong ne = new LatLong();
        ne.setLat(15.0);
        ne.setLng(25.0);
        LatLong sw = new LatLong();
        sw.setLat(95.0);
        sw.setLng(185.0);
        bbox.setNe(ne);
        bbox.setSw(sw);
        metaHES.setBbox(bbox);
        com.mmt.hotels.pojo.matchmaker.LatLongAndBounds result = ReflectionTestUtils.invokeMethod(locationDataUtil, "buildCentre", metaHES);
        Assert.assertNotNull(result);
        Assert.assertEquals("55.0",result.getLat());
        Assert.assertEquals("105.0",result.getLng());
    }
}
