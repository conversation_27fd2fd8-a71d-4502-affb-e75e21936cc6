package com.mmt.hotels.clientgateway.util;

import org.jboss.logging.MDC;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Unit tests for MDCHelper class
 * Comprehensive test coverage for MDC (Mapped Diagnostic Context) helper functionality
 */
@RunWith(MockitoJUnitRunner.class)
public class MDCHelperTest {

    @Before
    public void setUp() {
        MDC.clear();
    }

    @After
    public void tearDown() {
        MDC.clear();
    }

    @Test
    public void testMDCKeysEnumValues() {
        // Test all enum values exist and have correct string values
        Assert.assertEquals("client", MDCHelper.MDCKeys.CLIENT.getStringValue());
        Assert.assertEquals("correlation", MDCHelper.MDCKeys.CORRELATION.getStringValue());
        Assert.assertEquals("tid", MDCHelper.MDCKeys.TID.getStringValue());
        Assert.assertEquals("region", MDCHelper.MDCKeys.REGION.getStringValue());
        Assert.assertEquals("language", MDCHelper.MDCKeys.LANGUAGE.getStringValue());
        Assert.assertEquals("currency", MDCHelper.MDCKeys.CURRENCY.getStringValue());
        Assert.assertEquals("idContext", MDCHelper.MDCKeys.IDCONTEXT.getStringValue());
        Assert.assertEquals("controller", MDCHelper.MDCKeys.CONTROLLER.getStringValue());
        Assert.assertEquals("src_channel", MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue());
        Assert.assertEquals("country", MDCHelper.MDCKeys.COUNTRY.getStringValue());
        Assert.assertEquals("advance_purchase", MDCHelper.MDCKeys.ADVANCE_PURCHASE.getStringValue());
        Assert.assertEquals("length_of_stay", MDCHelper.MDCKeys.LENGTH_OF_STAY.getStringValue());
        Assert.assertEquals("adult_count", MDCHelper.MDCKeys.ADULT_COUNT.getStringValue());
        Assert.assertEquals("child_count", MDCHelper.MDCKeys.CHILD_COUNT.getStringValue());
        Assert.assertEquals("paginated", MDCHelper.MDCKeys.PAGINATED.getStringValue());
        Assert.assertEquals("funnel", MDCHelper.MDCKeys.FUNNEL_SOURCE.getStringValue());
        Assert.assertEquals("trafficType", MDCHelper.MDCKeys.TRAFFIC_TYPE.getStringValue());
        Assert.assertEquals("ldrsegregation", MDCHelper.MDCKeys.LDRSEGREGATION.getStringValue());
    }

    @Test
    public void testCreateMDCWithValidValues() {
        String client = "testClient";
        String tid = "testTid123";
        String correlationKey = "testCorrelation456";
        String region = "IN";
        String language = "eng";
        String currency = "INR";
        String controller = "AvailRoomsController";
        String idContext = "testIdContext";
        String trafficType = "B2C";
        String funnelSource = "web";
        String country = "DOMESTIC";

        MDCHelper.createMDC(client, tid, correlationKey, region, language, currency, controller, idContext, trafficType, funnelSource, country);

        // Verify all values are set correctly in MDC
        Assert.assertEquals(client, MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()));
        Assert.assertEquals(tid, MDC.get(MDCHelper.MDCKeys.TID.getStringValue()));
        Assert.assertEquals(correlationKey, MDC.get(MDCHelper.MDCKeys.CORRELATION.getStringValue()));
        Assert.assertEquals(region, MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()));
        Assert.assertEquals(language, MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue()));
        Assert.assertEquals(currency, MDC.get(MDCHelper.MDCKeys.CURRENCY.getStringValue()));
        Assert.assertEquals(controller, MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue()));
        Assert.assertEquals(idContext, MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue()));
        Assert.assertEquals(trafficType, MDC.get(MDCHelper.MDCKeys.TRAFFIC_TYPE.getStringValue()));
        Assert.assertEquals(funnelSource, MDC.get(MDCHelper.MDCKeys.FUNNEL_SOURCE.getStringValue()));
        Assert.assertEquals(country, MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()));
    }

    @Test
    public void testCreateMDCWithNullValues() {
        MDCHelper.createMDC(null, null, null, null, null, null, null, null, null, null, null);

        // Verify null values are handled properly
        Assert.assertNull(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()));
        Assert.assertNull(MDC.get(MDCHelper.MDCKeys.TID.getStringValue()));
        Assert.assertNull(MDC.get(MDCHelper.MDCKeys.CORRELATION.getStringValue()));
        Assert.assertNull(MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()));
        Assert.assertNull(MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue()));
        Assert.assertNull(MDC.get(MDCHelper.MDCKeys.CURRENCY.getStringValue()));
        Assert.assertNull(MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue()));
        Assert.assertNull(MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue()));
        Assert.assertNull(MDC.get(MDCHelper.MDCKeys.TRAFFIC_TYPE.getStringValue()));
        Assert.assertNull(MDC.get(MDCHelper.MDCKeys.FUNNEL_SOURCE.getStringValue()));
        Assert.assertNull(MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()));
    }

    @Test
    public void testCreateMDCWithEmptyValues() {
        MDCHelper.createMDC("", "", "", "", "", "", "", "", "", "", "");

        // Verify empty values are handled properly
        Assert.assertEquals("", MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()));
        Assert.assertEquals("", MDC.get(MDCHelper.MDCKeys.TID.getStringValue()));
        Assert.assertEquals("", MDC.get(MDCHelper.MDCKeys.CORRELATION.getStringValue()));
        Assert.assertEquals("", MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()));
        Assert.assertEquals("", MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue()));
        Assert.assertEquals("", MDC.get(MDCHelper.MDCKeys.CURRENCY.getStringValue()));
        Assert.assertEquals("", MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue()));
        Assert.assertEquals("", MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue()));
        Assert.assertEquals("", MDC.get(MDCHelper.MDCKeys.TRAFFIC_TYPE.getStringValue()));
        Assert.assertEquals("", MDC.get(MDCHelper.MDCKeys.FUNNEL_SOURCE.getStringValue()));
        Assert.assertEquals("", MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()));
    }

    @Test
    public void testCreateMDCWithSpecialCharacters() {
        String client = "test@client#123";
        String tid = "tid$%^&*()";
        String correlationKey = "correlation-key_with.special~chars";
        String region = "US/CA";
        String language = "en-US";
        String currency = "USD$";
        String controller = "Controller@Name";
        String idContext = "id.context-123";
        String trafficType = "B2B/Enterprise";
        String funnelSource = "mobile-app";
        String country = "INTERNATIONAL";

        MDCHelper.createMDC(client, tid, correlationKey, region, language, currency, controller, idContext, trafficType, funnelSource, country);

        // Verify special characters are preserved
        Assert.assertEquals(client, MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()));
        Assert.assertEquals(tid, MDC.get(MDCHelper.MDCKeys.TID.getStringValue()));
        Assert.assertEquals(correlationKey, MDC.get(MDCHelper.MDCKeys.CORRELATION.getStringValue()));
        Assert.assertEquals(region, MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()));
        Assert.assertEquals(language, MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue()));
        Assert.assertEquals(currency, MDC.get(MDCHelper.MDCKeys.CURRENCY.getStringValue()));
        Assert.assertEquals(controller, MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue()));
        Assert.assertEquals(idContext, MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue()));
        Assert.assertEquals(trafficType, MDC.get(MDCHelper.MDCKeys.TRAFFIC_TYPE.getStringValue()));
        Assert.assertEquals(funnelSource, MDC.get(MDCHelper.MDCKeys.FUNNEL_SOURCE.getStringValue()));
        Assert.assertEquals(country, MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()));
    }

    @Test
    public void testCreateMDCWithLongValues() {
        StringBuilder longValue = new StringBuilder();
        for (int i = 0; i < 100; i++) {
            longValue.append("LongValue");
        }
        String longString = longValue.toString();

        MDCHelper.createMDC(longString, longString, longString, longString, longString, longString, longString, longString, longString, longString, longString);

        // Verify long values are handled properly
        Assert.assertEquals(longString, MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()));
        Assert.assertEquals(longString, MDC.get(MDCHelper.MDCKeys.TID.getStringValue()));
        Assert.assertEquals(longString, MDC.get(MDCHelper.MDCKeys.CORRELATION.getStringValue()));
    }

    @Test
    public void testCreateMDCOverwritesPreviousValues() {
        // Set initial values
        MDCHelper.createMDC("client1", "tid1", "corr1", "region1", "lang1", "curr1", "ctrl1", "id1", "traffic1", "funnel1", "country1");
        
        Assert.assertEquals("client1", MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()));
        Assert.assertEquals("tid1", MDC.get(MDCHelper.MDCKeys.TID.getStringValue()));

        // Set new values
        MDCHelper.createMDC("client2", "tid2", "corr2", "region2", "lang2", "curr2", "ctrl2", "id2", "traffic2", "funnel2", "country2");
        
        // Verify old values are overwritten
        Assert.assertEquals("client2", MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()));
        Assert.assertEquals("tid2", MDC.get(MDCHelper.MDCKeys.TID.getStringValue()));
        Assert.assertEquals("corr2", MDC.get(MDCHelper.MDCKeys.CORRELATION.getStringValue()));
    }

    @Test
    public void testCreateMDCWithPartialValues() {
        // Test with some null and some valid values
        MDCHelper.createMDC("validClient", null, "validCorrelation", "", "validLanguage", null, "validController", "", "validTraffic", null, "validCountry");

        Assert.assertEquals("validClient", MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()));
        Assert.assertNull(MDC.get(MDCHelper.MDCKeys.TID.getStringValue()));
        Assert.assertEquals("validCorrelation", MDC.get(MDCHelper.MDCKeys.CORRELATION.getStringValue()));
        Assert.assertEquals("", MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()));
        Assert.assertEquals("validLanguage", MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue()));
        Assert.assertNull(MDC.get(MDCHelper.MDCKeys.CURRENCY.getStringValue()));
        Assert.assertEquals("validController", MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue()));
        Assert.assertEquals("", MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue()));
        Assert.assertEquals("validTraffic", MDC.get(MDCHelper.MDCKeys.TRAFFIC_TYPE.getStringValue()));
        Assert.assertNull(MDC.get(MDCHelper.MDCKeys.FUNNEL_SOURCE.getStringValue()));
        Assert.assertEquals("validCountry", MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()));
    }

    @Test
    public void testMDCKeysCount() {
        // Ensure we have all expected MDC keys
        MDCHelper.MDCKeys[] keys = MDCHelper.MDCKeys.values();
        Assert.assertEquals("Should have 20 MDC keys", 20, keys.length);
    }

    @Test
    public void testMDCKeysUniqueness() {
        // Verify all MDC key string values are unique
        MDCHelper.MDCKeys[] keys = MDCHelper.MDCKeys.values();
        for (int i = 0; i < keys.length; i++) {
            for (int j = i + 1; j < keys.length; j++) {
                Assert.assertNotEquals("MDC key values should be unique: " + keys[i] + " vs " + keys[j], 
                    keys[i].getStringValue(), keys[j].getStringValue());
            }
        }
    }

    @Test
    public void testMDCKeysNotNull() {
        // Verify all MDC key string values are not null or empty
        for (MDCHelper.MDCKeys key : MDCHelper.MDCKeys.values()) {
            Assert.assertNotNull("MDC key value should not be null: " + key, key.getStringValue());
            Assert.assertFalse("MDC key value should not be empty: " + key, key.getStringValue().isEmpty());
        }
    }

    @Test
    public void testCreateMDCWithUnicodeCharacters() {
        String client = "测试客户端";
        String language = "हिंदी";
        String currency = "₹INR";
        String controller = "控制器";

        MDCHelper.createMDC(client, "tid", "corr", "region", language, currency, controller, "id", "traffic", "funnel", "country");

        // Verify unicode characters are preserved
        Assert.assertEquals(client, MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()));
        Assert.assertEquals(language, MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue()));
        Assert.assertEquals(currency, MDC.get(MDCHelper.MDCKeys.CURRENCY.getStringValue()));
        Assert.assertEquals(controller, MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue()));
    }

    @Test
    public void testCreateMDCThreadSafety() {
        // Basic thread safety test - ensure MDC works in test environment
        MDCHelper.createMDC("threadTest", "tid", "corr", "region", "lang", "curr", "ctrl", "id", "traffic", "funnel", "country");
        
        String retrievedClient = (String) MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
        Assert.assertEquals("threadTest", retrievedClient);
    }

    @Test
    public void testMDCKeyConsistency() {
        // Test that enum names and string values are reasonably consistent
        Assert.assertTrue("CLIENT key should contain 'client'", 
            MDCHelper.MDCKeys.CLIENT.getStringValue().toLowerCase().contains("client"));
        Assert.assertTrue("CORRELATION key should contain 'correlation'", 
            MDCHelper.MDCKeys.CORRELATION.getStringValue().toLowerCase().contains("correlation"));
        Assert.assertTrue("REGION key should contain 'region'", 
            MDCHelper.MDCKeys.REGION.getStringValue().toLowerCase().contains("region"));
        Assert.assertTrue("LANGUAGE key should contain 'language'", 
            MDCHelper.MDCKeys.LANGUAGE.getStringValue().toLowerCase().contains("language"));
        Assert.assertTrue("CURRENCY key should contain 'currency'", 
            MDCHelper.MDCKeys.CURRENCY.getStringValue().toLowerCase().contains("currency"));
    }
}