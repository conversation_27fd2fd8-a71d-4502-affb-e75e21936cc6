package com.mmt.hotels.clientgateway.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.request.TrafficSource;
import com.mmt.hotels.clientgateway.response.searchHotels.CrossSellData;
import com.mmt.hotels.clientgateway.service.CardEngineService;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.persuasion.response.Persuasion;
import com.mmt.hotels.model.persuasion.response.PersuasionData;
import com.mmt.hotels.model.persuasion.response.StyleResponseBO;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.response.landing.CardResponse;
import com.mmt.hotels.model.response.searchwrapper.CrossSellDataHES;
import com.mmt.hotels.model.response.searchwrapper.CrossSellPersuasion;
import com.mmt.hotels.model.response.searchwrapper.LoyaltyPersuasion;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntityAbridged;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class CrossSellUtilTest {

    @InjectMocks
    CrossSellUtil crossSellUtil;

    @Mock
    PolyglotService polyglotService;

    @Mock
    private CommonConfigHelper commonConfigHelper;

    @Mock
    private CardEngineService cardEngineService;

    @Spy
    private ObjectMapperUtil objectMapperUtilSpy;
    @Mock
    private ObjectMapperUtil objectMapperUtilMock;

    @Spy
    ObjectMapper mapper;

    @Test
    public void getCrossSellDataTest() {
        Map<String, String> crossSellMap = new HashMap<>();
        crossSellMap.put("loopCount", "2");
        crossSellMap.put("animationDelay", "3");
        crossSellMap.put("bgGradientColor1", "Test");
        Mockito.when(commonConfigHelper.getCrossSellDataMap()).thenReturn(crossSellMap);
        CrossSellData data = crossSellUtil.getCrossSellData(false, "DESKTOP", "Test", null, new HashMap<>(),null);
        Assert.assertNotNull(data);
        Assert.assertNotNull(data.getCoupon());
    }

    @Test
    public void isCrossSellRequestTest() {
        SearchHotelsRequest searchHotelsRequest = getSearchHotelRequest();
        boolean flag = crossSellUtil.isCrossSellRequest(searchHotelsRequest);
        Assert.assertEquals(true, flag);
    }

    private SearchHotelsRequest getSearchHotelRequest() {
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        RequestDetails requestDetails = new RequestDetails();
        TrafficSource trafficSource = new TrafficSource();
        trafficSource.setSource("FLIGHTSTY");
        requestDetails.setTrafficSource(trafficSource);
        requestDetails.setRequestor("SCION");
        searchHotelsRequest.setRequestDetails(requestDetails);
        return searchHotelsRequest;
    }

    @Test
    public void getCrossSellDataScionTest() {
        Map<String, String> crossSellMap = new HashMap<>();
        crossSellMap.put("loopCount", "2");
        crossSellMap.put("animationDelay", "3");
        crossSellMap.put("bgGradientColor1", "Test");
        Mockito.when(commonConfigHelper.getCrossSellDataMap()).thenReturn(crossSellMap);
        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("test");
        SearchWrapperInputRequest searchWrapperInputRequest = new SearchWrapperInputRequest();
        searchWrapperInputRequest.setDeviceType("DESKTOP");
        CrossSellDataHES data = crossSellUtil.getCrossSellDataScion("test", searchWrapperInputRequest, false);
        Assert.assertNotNull(data);
        Assert.assertNotNull(data.getCoupon());
    }

    @Test
    public void getCrossSellDataScionFallBackTest() {
        Map<String, String> crossSellMap = new HashMap<>();
        crossSellMap.put("loopCount", "2");
        crossSellMap.put("animationDelay", "3");
        crossSellMap.put("bgGradientColor1", "Test");
        Mockito.when(commonConfigHelper.getCrossSellDataMap()).thenReturn(crossSellMap);
        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("test");
        SearchWrapperInputRequest searchWrapperInputRequest = new SearchWrapperInputRequest();
        searchWrapperInputRequest.setDeviceType("DESKTOP");
        CrossSellDataHES data = crossSellUtil.getCrossSellDataScion("test", searchWrapperInputRequest, true);
        Assert.assertNotNull(data);
        Assert.assertNotNull(data.getCoupon());
    }

    @Test
    public void getCrossSellOfferTest() {
        String cardEngineService = "{\"cardId\":\"PERSUASION\",\"cardType\":\"STORE_AND_FETCH\",\"cardData\":{\"persuasion\":{\"text\":\"The Persuasion copy appears this container and can only span till 2 lines\",\"bgColor\":\"#IEIEOU\",\"txtColor\":\"#EUEUE\"}},\"deepLink\":\"mmyt://htl/listing/?checkin=04272024&checkout=04282024&city=CTDEL&country=IN&roomStayQualifier=2e0e&checkAvailability=false&locusId=CTDEL&locusType=city&funnelName=hotels\",\"success\":true}";
        CardResponse cardResponse = new CardResponse();
        cardResponse.setCardId("Test");
        cardResponse.setSuccess(true);
        CrossSellData crossSellData = new CrossSellData();
        CrossSellPersuasion crossSellPersuasion = new CrossSellPersuasion();
        crossSellPersuasion.setText("testing");
        crossSellData.setPersuasion(crossSellPersuasion);
        ReflectionTestUtils.setField(crossSellUtil, "objectMapperUtil", objectMapperUtilSpy);
        ReflectionTestUtils.setField(objectMapperUtilSpy,"mapper",new ObjectMapper());
        crossSellPersuasion = ReflectionTestUtils.invokeMethod(crossSellUtil, "getCrossSellPersuasion",cardEngineService);
        Assert.assertNotNull(crossSellPersuasion);
        cardEngineService = null;
        crossSellPersuasion = ReflectionTestUtils.invokeMethod(crossSellUtil, "getCrossSellPersuasion", cardEngineService);
        Assert.assertNull(crossSellPersuasion);

    }

    @Test
    public void testModifyHotelPersuasions() throws JsonParseException {
        SearchWrapperHotelEntityAbridged hotel = new SearchWrapperHotelEntityAbridged();
        Map<String, Object> persuasions = new HashMap<>();
        Persuasion persuasion = new Persuasion();
        PersuasionData persuasionData = new PersuasionData();
        persuasionData.setText("Test Text");
        persuasion.setData(Arrays.asList(persuasionData));
        persuasions.put("PC_SCION_BLACK", persuasion);
        hotel.setHotelPersuasions(persuasions);

        ReflectionTestUtils.setField(crossSellUtil, "objectMapperUtil", objectMapperUtilMock);
        Mockito.doReturn(persuasion).when(objectMapperUtilMock).getObjectFromJsonWithType(Mockito.any(),Mockito.any(),Mockito.any());

        LoyaltyPersuasion result = crossSellUtil.modifyHotelPersuasions(hotel);

        Assert.assertNotNull(result);
        Assert.assertEquals("Test Text", result.getBenefitText());
    }

    @Test
    public void mapToLoyaltyPersuasionTes(){
        StyleResponseBO styleResponseBO = new StyleResponseBO();
        styleResponseBO.setBgColor("#FFFFF");
        PersuasionData persuasionData = new PersuasionData();
        persuasionData.setText("Test");
        persuasionData.setIconurl("TestUrl");
        persuasionData.setStyle(styleResponseBO);
        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("{count} benefits");

        LoyaltyPersuasion result = ReflectionTestUtils.invokeMethod(crossSellUtil, "mapToLoyaltyPersuasion", persuasionData, 2, "Test");

        Assert.assertNotNull(result);
        Assert.assertEquals("2 benefits", result.getBenefitCountText());
        Assert.assertEquals("#FFFFF", result.getBgColor());
    }
}