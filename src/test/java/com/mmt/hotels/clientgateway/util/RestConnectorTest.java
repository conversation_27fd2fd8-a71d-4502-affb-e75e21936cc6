package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.message.BasicHeader;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;

import java.io.ByteArrayInputStream;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RestConnectorTest {

    @InjectMocks
    private RestConnector restConnector;

    @Mock
    private HttpClient httpClient;

    @Mock
    private HttpResponse httpResponse;

    @Mock
    private HttpEntity httpEntity;

    @Mock
    private StatusLine statusLine;

    private List<String> gccHeaderKeys;
    private List<String> retryEnabledUrls;

    @Before
    public void setUp() throws Exception {
        // Setup mock responses
        when(httpResponse.getEntity()).thenReturn(httpEntity);
        when(httpResponse.getStatusLine()).thenReturn(statusLine);
        when(statusLine.getStatusCode()).thenReturn(200);
        when(httpClient.execute(any(HttpPost.class))).thenReturn(httpResponse);
        when(httpClient.execute(any(HttpGet.class))).thenReturn(httpResponse);

        // Mock EntityUtils.toString to return a default response
        mockEntityUtils();

        // Initialize fields using reflection
        gccHeaderKeys = new ArrayList<>();
        gccHeaderKeys.add(Constants.REGION);
        gccHeaderKeys.add(Constants.LANGUAGE);
        gccHeaderKeys.add(Constants.CURRENCY);

        java.lang.reflect.Field gccHeaderKeysField = RestConnector.class.getDeclaredField("gccHeaderKeys");
        gccHeaderKeysField.setAccessible(true);
        gccHeaderKeysField.set(restConnector, gccHeaderKeys);

        retryEnabledUrls = new ArrayList<>();
        retryEnabledUrls.add("RETRY_URL");

        java.lang.reflect.Field retryEnabledUrlsField = RestConnector.class.getDeclaredField("retryEnabledUrls");
        retryEnabledUrlsField.setAccessible(true);
        retryEnabledUrlsField.set(restConnector, retryEnabledUrls);

        // Clear MDC before each test
        MDC.clear();
    }

    private void mockEntityUtils() throws Exception {
        // Create a mock response
        String responseJson = "{\"status\":\"success\"}";
        ByteArrayInputStream inputStream = new ByteArrayInputStream(responseJson.getBytes());
        when(httpEntity.getContent()).thenReturn(inputStream);
    }

    @Test
    public void testPostMessages_Success() throws Exception {
        // Arrange
        String requestBody = "{\"key\":\"value\"}";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        String url = "http://example.com/api";

        // Act
        String result = restConnector.postMessages(httpClient, requestBody, headers, url, DependencyLayer.CLIENTGATEWAY);

        // Assert
        assertNotNull(result);
        verify(httpClient).execute(any(HttpPost.class));
    }

    @Test(expected = RestConnectorException.class)
    public void testPostMessages_ConnectTimeoutException() throws Exception {
        // Arrange
        String requestBody = "{\"key\":\"value\"}";
        Map<String, String> headers = new HashMap<>();
        String url = "http://example.com/api";

        // Mock HttpClient to throw ConnectTimeoutException
        when(httpClient.execute(any(HttpPost.class))).thenThrow(new ConnectTimeoutException("Connection timeout"));

        // Act
        restConnector.postMessages(httpClient, requestBody, headers, url, DependencyLayer.CLIENTGATEWAY);

        // Assert: Exception expected
    }

    @Test(expected = RestConnectorException.class)
    public void testPostMessages_SocketTimeoutException() throws Exception {
        // Arrange
        String requestBody = "{\"key\":\"value\"}";
        Map<String, String> headers = new HashMap<>();
        String url = "http://example.com/api";

        // Mock HttpClient to throw SocketTimeoutException
        when(httpClient.execute(any(HttpPost.class))).thenThrow(new SocketTimeoutException("Socket timeout"));

        // Act
        restConnector.postMessages(httpClient, requestBody, headers, url, DependencyLayer.CLIENTGATEWAY);

        // Assert: Exception expected
    }

    @Test
    public void testGetMessages_Success() throws Exception {
        // Arrange
        Map<String, String> headers = new HashMap<>();
        headers.put("Accept", "application/json");
        String url = "http://example.com/api";

        // Act
        String result = restConnector.getMessages(httpClient, headers, url, DependencyLayer.CLIENTGATEWAY);

        // Assert
        assertNotNull(result);
        verify(httpClient).execute(any(HttpGet.class));
    }

    @Test
    public void testSetResponseHeadersInMdc() throws Exception {
        // Arrange
        Map<String, String> headers = new HashMap<>();
        String url = "http://example.com/api";

        Header[] responseHeaders = new Header[]{
                new BasicHeader(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue(), "WEB"),
                new BasicHeader(MDCHelper.MDCKeys.COUNTRY.getStringValue(), "IN")
        };

        when(httpResponse.getAllHeaders()).thenReturn(responseHeaders);

        // Act
        restConnector.getMessages(httpClient, headers, url, DependencyLayer.CLIENTGATEWAY);

        // Assert
        assertEquals("WEB", MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
        assertEquals("IN", MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()));
    }

    @Test
    public void testSetUgcRequestHeaders() {
        // Arrange
        Map<String, String> headers = new HashMap<>();
        String contentType = "multipart/form-data";

        // Act
        restConnector.setUgcRequestHeaders(headers, contentType);

        // Assert
        assertEquals(contentType, headers.get(Constants.HEADER_CONTENT_TYPE));
    }
}
