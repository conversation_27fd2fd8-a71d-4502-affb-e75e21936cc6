package com.mmt.hotels.clientgateway.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class ObjectMapperUtilTest {

    @InjectMocks
    private ObjectMapperUtil objectMapperUtil;

    @Before
    public void setUp() {
        objectMapperUtil.init();
    }

    @Test
    public void testGetObjectFromJson_Success() throws JsonParseException {
        // Arrange
        String json = "{\"name\":\"Test Hotel\",\"stars\":4,\"price\":100.50}";
        
        // Act
        TestHotel hotel = objectMapperUtil.getObjectFromJson(json, TestHotel.class, DependencyLayer.CLIENTGATEWAY);
        
        // Assert
        assertNotNull(hotel);
        assertEquals("Test Hotel", hotel.getName());
        assertEquals(4, hotel.getStars());
        assertEquals(100.50, hotel.getPrice(), 0.001);
    }

    @Test(expected = JsonParseException.class)
    public void testGetObjectFromJson_InvalidJson() throws JsonParseException {
        // Arrange
        String invalidJson = "{\"name\":\"Test Hotel\",\"stars\":4,price:100.50}"; // Missing quotes around price
        
        // Act
        objectMapperUtil.getObjectFromJson(invalidJson, TestHotel.class, DependencyLayer.CLIENTGATEWAY);
        
        // Assert: Exception expected
    }

    @Test
    public void testGetObjectFromJsonWithIgnoreUnknown_Success() throws JsonParseException {
        // Arrange
        String json = "{\"name\":\"Test Hotel\",\"stars\":4,\"price\":100.50,\"extraField\":\"value\"}";
        
        // Act
        TestHotel hotel = objectMapperUtil.getObjectFromJsonWithIgnoreUnknown(json, TestHotel.class, DependencyLayer.CLIENTGATEWAY);
        
        // Assert
        assertNotNull(hotel);
        assertEquals("Test Hotel", hotel.getName());
        assertEquals(4, hotel.getStars());
        assertEquals(100.50, hotel.getPrice(), 0.001);
    }

    @Test
    public void testGetJsonFromObject_Success() throws JsonParseException {
        // Arrange
        TestHotel hotel = new TestHotel();
        hotel.setName("Test Hotel");
        hotel.setStars(4);
        hotel.setPrice(100.50);
        
        // Act
        String json = objectMapperUtil.getJsonFromObject(hotel, DependencyLayer.CLIENTGATEWAY);
        
        // Assert
        assertNotNull(json);
        assertTrue(json.contains("\"name\":\"Test Hotel\""));
        assertTrue(json.contains("\"stars\":4"));
        assertTrue(json.contains("\"price\":100.5"));
    }

    @Test(expected = JsonParseException.class)
    public void testGetJsonFromObject_NonSerializableObject() throws JsonParseException {
        // Arrange
        Object nonSerializable = new Object() {
            @Override
            public String toString() {
                throw new RuntimeException("Test exception");
            }
        };
        
        // Act
        objectMapperUtil.getJsonFromObject(nonSerializable, DependencyLayer.CLIENTGATEWAY);
        
        // Assert: Exception expected
    }

    @Test
    public void testGetObjectFromJsonWithType_Success() throws JsonParseException {
        // Arrange
        String json = "[{\"name\":\"Hotel A\",\"stars\":4,\"price\":100.50},{\"name\":\"Hotel B\",\"stars\":5,\"price\":200.75}]";
        
        // Act
        List<TestHotel> hotels = objectMapperUtil.getObjectFromJsonWithType(json, new TypeReference<List<TestHotel>>() {}, DependencyLayer.CLIENTGATEWAY);
        
        // Assert
        assertNotNull(hotels);
        assertEquals(2, hotels.size());
        assertEquals("Hotel A", hotels.get(0).getName());
        assertEquals(4, hotels.get(0).getStars());
        assertEquals(100.50, hotels.get(0).getPrice(), 0.001);
        assertEquals("Hotel B", hotels.get(1).getName());
        assertEquals(5, hotels.get(1).getStars());
        assertEquals(200.75, hotels.get(1).getPrice(), 0.001);
    }

    @Test
    public void testGetObjectFromJsonNode_Success() throws Exception {
        // Arrange
        String json = "{\"hotels\":[{\"name\":\"Hotel A\",\"stars\":4,\"price\":100.50},{\"name\":\"Hotel B\",\"stars\":5,\"price\":200.75}]}";
        ObjectMapper mapper = new ObjectMapper();
        JsonNode jsonNode = mapper.readTree(json);
        
        // Act
        Map<String, List<TestHotel>> result = objectMapperUtil.getObjectFromJsonNode(jsonNode, new TypeReference<Map<String, List<TestHotel>>>() {});
        
        // Assert
        assertNotNull(result);
        assertTrue(result.containsKey("hotels"));
        List<TestHotel> hotels = result.get("hotels");
        assertEquals(2, hotels.size());
        assertEquals("Hotel A", hotels.get(0).getName());
        assertEquals("Hotel B", hotels.get(1).getName());
    }

    @Test
    public void testGetObjectFromJsonNode_ReturnsNullOnException() throws Exception {
        // Arrange
        ObjectMapper mapper = new ObjectMapper();
        JsonNode jsonNode = mapper.readTree("{\"stars\": \"test\" }");
        
        // Act
        // Using incompatible TypeReference to force an exception
        TestHotel result = objectMapperUtil.getObjectFromJsonNode(jsonNode, new TypeReference<TestHotel>() {});
        
        // Assert
        assertNull(result);
    }

    // Test class for serialization/deserialization
    public static class TestHotel {
        private String name;
        private int stars;
        private double price;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getStars() {
            return stars;
        }

        public void setStars(int stars) {
            this.stars = stars;
        }

        public double getPrice() {
            return price;
        }

        public void setPrice(double price) {
            this.price = price;
        }
    }
}
