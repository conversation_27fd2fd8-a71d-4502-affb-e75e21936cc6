package com.mmt.hotels.clientgateway.util;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.BGGradient;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.Style;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.HomeStayDetails;
import com.mmt.hotels.model.persuasion.response.Persuasion;
import com.mmt.hotels.model.response.HotelBenefitInfo;
import com.mmt.hotels.model.response.emi.NoCostEmiDetails;
import com.mmt.hotels.model.response.mmtprime.BlackInfo;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.HeroTierDetails;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static org.mockito.Mockito.mock;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.when;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SBPP_VALUE_1_PERS_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SBPP_VALUE_2_PERS_TEXT;


@RunWith(MockitoJUnitRunner.class)
public class PersuasionUtilTest {
    @InjectMocks
    PersuasionUtil persuasionUtil;

    @Mock
    PolyglotService polyglotService;

    private Map<String, Map<String, PersuasionData>> hiddenGemPersuasionConfigMap;

    private Persuasion flyerPersuasionDT;
    private Persuasion flyerPersuasionAPP;
    private Persuasion flyerPersuasionPWA;
    Persuasion flyerDealRatesPersuasionApps;
    Persuasion flyerDealRatesPersuasionDT;
    Persuasion flyerDealRatesPersuasionPWA;
    Persuasion scarcityPriceBottomPersuasionApps;

    private Persuasion noCostEmiApplicablePersuasion;
    private static final Gson gson = new Gson();

    private Persuasion gccMetaWalletCashbackPersuasionDT;
    private Persuasion gccMetaWalletCashbackPersuasionAPP;
    private Persuasion gccMetaWalletCashbackPersuasionPWA;

    private Persuasion mmtBlackPersuasion;
    private Persuasion hotelBenefitPersuasion;

    Persuasion busExclusiveRatesPersuasionApps;

    Persuasion busExclusiveRatesPersuasionDT;

    Persuasion busExclusiveRatesPersuasionPWA;

    Persuasion trainExclusiveRatesPersuasionApps;

    Persuasion trainExclusiveRatesPersuasionDT;

    Persuasion trainExclusiveRatesPersuasionPWA;

    Persuasion vistaraExclusiveRatesPersuasionApps;

    Persuasion vistaraExclusiveRatesPersuasionDT;

    Persuasion vistaraExclusiveRatesPersuasionPWA;


    @Before
    public void setup() {

        String busExclusiveRatesPersuasionConfigApps="{\"data\":[{\"id\":\"62bd9677feb541192bd483a2\",\"iconurl\":\"https://promos.makemytrip.com/Hotels_product/cross_sell/bus_listing_tag_3x.png\",\"style\":{\"iconHeight\":27,\"iconWidth\":139},\"persuasionType\":\"FLYER_EXCLUSIVE_RATES_PERSUASION\",\"hasAction\":false,\"html\":false}],\"placeholder\":\"PLACEHOLDER_TOP_RIGHT\",\"template\":\"IMAGE_TEXT_H\",\"templateType\":\"DEFAULT\"}";
        String busExclusiveRatesPersuasionConfigDT="{\"data\":[{\"id\":\"62bd9677feb541192bd483a2\",\"iconurl\":\"https://promos.makemytrip.com/Hotels_product/cross_sell/bus_listing_tag_3x.png\",\"style\":{\"iconHeight\":27,\"iconWidth\":139},\"persuasionType\":\"FLYER_EXCLUSIVE_RATES_PERSUASION\",\"hasAction\":false,\"html\":false}],\"placeholder\":\"PLACEHOLDER_TOP_RIGHT\",\"template\":\"IMAGE_TEXT_H\",\"templateType\":\"DEFAULT\"}";
        String busExclusiveRatesPersuasionConfigPWA="{\"data\":[{\"id\":\"62bd9677feb541192bd483a2\",\"iconurl\":\"https://promos.makemytrip.com/Hotels_product/cross_sell/bus_listing_tag_3x.png\",\"style\":{\"iconHeight\":27,\"iconWidth\":139},\"persuasionType\":\"FLYER_EXCLUSIVE_RATES_PERSUASION\",\"hasAction\":false,\"html\":false}],\"placeholder\":\"PLACEHOLDER_TOP_RIGHT\",\"template\":\"IMAGE_TEXT_H\",\"templateType\":\"DEFAULT\"}";
        String trainExclusiveRatesPersuasionConfigApps="{\"data\":[{\"id\":\"62bd9677feb541192bd483a2\",\"iconurl\":\"https://promos.makemytrip.com/Hotels_product/cross_sell/bus_listing_tag_3x.png\",\"style\":{\"iconHeight\":27,\"iconWidth\":139},\"persuasionType\":\"FLYER_EXCLUSIVE_RATES_PERSUASION\",\"hasAction\":false,\"html\":false}],\"placeholder\":\"PLACEHOLDER_TOP_RIGHT\",\"template\":\"IMAGE_TEXT_H\",\"templateType\":\"DEFAULT\"}";
        String trainExclusiveRatesPersuasionConfigDT="{\"data\":[{\"id\":\"62bd9677feb541192bd483a2\",\"iconurl\":\"https://promos.makemytrip.com/Hotels_product/cross_sell/bus_listing_tag_3x.png\",\"style\":{\"iconHeight\":27,\"iconWidth\":139},\"persuasionType\":\"FLYER_EXCLUSIVE_RATES_PERSUASION\",\"hasAction\":false,\"html\":false}],\"placeholder\":\"PLACEHOLDER_TOP_RIGHT\",\"template\":\"IMAGE_TEXT_H\",\"templateType\":\"DEFAULT\"}";
        String trainExclusiveRatesPersuasionConfigPWA="{\"data\":[{\"id\":\"62bd9677feb541192bd483a2\",\"iconurl\":\"https://promos.makemytrip.com/Hotels_product/cross_sell/bus_listing_tag_3x.png\",\"style\":{\"iconHeight\":27,\"iconWidth\":139},\"persuasionType\":\"FLYER_EXCLUSIVE_RATES_PERSUASION\",\"hasAction\":false,\"html\":false}],\"placeholder\":\"PLACEHOLDER_TOP_RIGHT\",\"template\":\"IMAGE_TEXT_H\",\"templateType\":\"DEFAULT\"}";

        busExclusiveRatesPersuasionApps = new Gson().fromJson(busExclusiveRatesPersuasionConfigApps, new TypeToken<Persuasion>() {
        }.getType());
        busExclusiveRatesPersuasionDT = new Gson().fromJson(busExclusiveRatesPersuasionConfigDT, new TypeToken<Persuasion>() {
        }.getType());
        busExclusiveRatesPersuasionPWA = new Gson().fromJson(busExclusiveRatesPersuasionConfigPWA, new TypeToken<Persuasion>() {
        }.getType());
        trainExclusiveRatesPersuasionApps = new Gson().fromJson(trainExclusiveRatesPersuasionConfigApps, new TypeToken<Persuasion>() {
        }.getType());
        trainExclusiveRatesPersuasionDT = new Gson().fromJson(trainExclusiveRatesPersuasionConfigDT, new TypeToken<Persuasion>() {
        }.getType());
        trainExclusiveRatesPersuasionPWA = new Gson().fromJson(trainExclusiveRatesPersuasionConfigPWA, new TypeToken<Persuasion>() {
        }.getType());

        vistaraExclusiveRatesPersuasionApps = new Gson().fromJson(trainExclusiveRatesPersuasionConfigApps, new TypeToken<Persuasion>() {
        }.getType());
        vistaraExclusiveRatesPersuasionDT = new Gson().fromJson(trainExclusiveRatesPersuasionConfigDT, new TypeToken<Persuasion>() {
        }.getType());
        vistaraExclusiveRatesPersuasionPWA = new Gson().fromJson(trainExclusiveRatesPersuasionConfigPWA, new TypeToken<Persuasion>() {
        }.getType());


        ReflectionTestUtils.setField(persuasionUtil, "busExclusiveRatesPersuasionApps", busExclusiveRatesPersuasionApps);
        ReflectionTestUtils.setField(persuasionUtil, "busExclusiveRatesPersuasionDT", busExclusiveRatesPersuasionDT);
        ReflectionTestUtils.setField(persuasionUtil, "busExclusiveRatesPersuasionPWA", busExclusiveRatesPersuasionPWA);
        ReflectionTestUtils.setField(persuasionUtil, "trainExclusiveRatesPersuasionApps", trainExclusiveRatesPersuasionApps);
        ReflectionTestUtils.setField(persuasionUtil, "trainExclusiveRatesPersuasionDT", trainExclusiveRatesPersuasionDT);
        ReflectionTestUtils.setField(persuasionUtil, "trainExclusiveRatesPersuasionPWA", trainExclusiveRatesPersuasionPWA);
        ReflectionTestUtils.setField(persuasionUtil, "vistaraExclusiveRatesPersuasionApps", vistaraExclusiveRatesPersuasionApps);
        ReflectionTestUtils.setField(persuasionUtil, "vistaraExclusiveRatesPersuasionDT", vistaraExclusiveRatesPersuasionDT);
        ReflectionTestUtils.setField(persuasionUtil, "vistaraExclusiveRatesPersuasionPWA", vistaraExclusiveRatesPersuasionPWA);

        String hiddenGemPersuasionConfigString = "{\"Desktop\":{\"hiddenGem\":{\"style\":{\"textColor\":\"#4A4A4A\"}},\"hiddenGemIcon\":{\"iconurl\":\"https://promos.makemytrip.com/Hotels_product/Hidden_Gem/Hidden_Gem.png\",\"style\":{}},\"homeStayTitle\":{\"style\":{\"styleClasses\":[\"htlHighlt__title\"]},\"topLevelStyle\":{\"styleClasses\":[\"htlHighlt\"]}},\"homeStaySubTitle\":{\"style\":{\"styleClasses\":[\"htlHighlt__subTitle\"]},\"topLevelStyle\":{\"styleClasses\":[\"htlHighlt\"]}}},\"Apps\":{\"hiddenGem\":{\"style\":{\"textColor\":\"#4A4A4A\"}},\"hiddenGemIcon\":{\"iconurl\":\"https://promos.makemytrip.com/Hotels_product/Hidden_Gem/Hidden_Gem.png\",\"style\":{}},\"homeStayTitle\":{\"style\":{\"textColor\":\"#0061aa\",\"fontSize\":\"MEDIUM\",\"fontType\":\"B\"},\"topLevelStyle\":{\"bgColor\":\"#e5f3ff\",\"fontType\":\"B\",\"bgUrl\":\"leftBlueLineBg\"}},\"homeStaySubTitle\":{\"style\":{\"textColor\":\"#4a4a4a\"},\"topLevelStyle\":{\"bgColor\":\"#e5f3ff\",\"fontType\":\"B\",\"bgUrl\":\"leftBlueLineBg\"}}},\"PWA\":{\"hiddenGem\":{\"style\":{\"textColor\":\"#4A4A4A\"}},\"hiddenGemIcon\":{\"iconurl\":\"https://promos.makemytrip.com/Hotels_product/Hidden_Gem/Hidden_Gem.png\",\"style\":{}},\"homeStayTitle\":{\"style\":{\"textColor\":\"#0061aa\",\"fontSize\":\"MEDIUM\",\"fontType\":\"B\"},\"topLevelStyle\":{\"bgColor\":\"#e5f3ff\",\"fontType\":\"B\",\"bgUrl\":\"leftBlueLineBg\"}},\"homeStaySubTitle\":{\"style\":{\"textColor\":\"#4a4a4a\"},\"topLevelStyle\":{\"bgColor\":\"#e5f3ff\",\"fontType\":\"B\",\"bgUrl\":\"leftBlueLineBg\"}}}}";
        hiddenGemPersuasionConfigMap = gson.fromJson(hiddenGemPersuasionConfigString, new TypeToken<Map<String, Map<String, PersuasionData>>>() {
        }.getType());
        ReflectionTestUtils.setField(persuasionUtil, "hiddenGemPersuasionConfigMap", hiddenGemPersuasionConfigMap);

        String flyerPersuasionConfigDT = "{\"data\":[{\"id\":\"62bd9677feb541192bd483a1\",\"iconurl\":\"https://promos.makemytrip.com/Growth/Images/B2C/flyer_new_tag_listing.png\",\"style\":{\"styleClasses\":[\"pc__flyerExclusiveTag\"]},\"persuasionType\":\"FLYER_EXCLUSIVE_RATES_PERSUASION\",\"hasAction\":false,\"html\":false}],\"placeholder\":\"PC_TOP_RIGHT\",\"template\":\"IMAGE_TEXT_H\",\"templateType\":\"DEFAULT\"}";
        flyerPersuasionDT = gson.fromJson(flyerPersuasionConfigDT, new TypeToken<Persuasion>() {
        }.getType());

        String scarcityPriceBottomPersuasionAppsConfig = "{\"data\":[{\"id\":\"3863\",\"text\":\"This is a Steal Deal!\",\"hasAction\":false,\"style\":{\"textColor\":\"#007E7D\",\"fontType\":\"B\",\"fontSize\":\"BASE\"}},{\"id\":\"3863\",\"text\":\"The price per room per night for this property is just ?2,000.\",\"hasAction\":false,\"style\":{\"fontSize\":\"BASE\"}}],\"style\":{\"horizontalSpace\":12,\"verticalSpace\":12,\"borderGradient\":{\"angle\":\"50\",\"start\":\"#43E1A8\",\"end\":\"#219393\"}},\"placeholder\":\"PLACEHOLDER_PRICE_BOTTOM\",\"template\":\"MULTI_PERSUASION_V\",\"templateType\":\"DEFAULT\"}";
        scarcityPriceBottomPersuasionApps = gson.fromJson(scarcityPriceBottomPersuasionAppsConfig, new TypeToken<Persuasion>() {
        }.getType());

        ReflectionTestUtils.setField(persuasionUtil, "flyerExclusiveRatesPersuasionDT", flyerPersuasionDT);

        String flyerPersuasionConfigAPP = "{\"data\":[{\"id\":\"62bd9677feb541192bd483a1\",\"iconurl\":\"https://promos.makemytrip.com/Growth/Images/B2C/flyer_new_tag_listing.png\",\"style\":{\"styleClasses\":[\"pc__flyerExclusiveTag\"]},\"persuasionType\":\"FLYER_EXCLUSIVE_RATES_PERSUASION\",\"hasAction\":false,\"html\":false}],\"placeholder\":\"PC_TOP_RIGHT\",\"template\":\"IMAGE_TEXT_H\",\"templateType\":\"DEFAULT\"}";
        flyerPersuasionAPP = gson.fromJson(flyerPersuasionConfigDT, new TypeToken<Persuasion>() {
        }.getType());
        ReflectionTestUtils.setField(persuasionUtil, "flyerExclusiveRatesPersuasionApps", flyerPersuasionDT);

        String flyerPersuasionConfigPWA = "{\"data\":[{\"id\":\"62bd9677feb541192bd483a1\",\"iconurl\":\"https://promos.makemytrip.com/Growth/Images/B2C/flyer_new_tag_listing.png\",\"style\":{\"styleClasses\":[\"pc__flyerExclusiveTag\"]},\"persuasionType\":\"FLYER_EXCLUSIVE_RATES_PERSUASION\",\"hasAction\":false,\"html\":false}],\"placeholder\":\"PC_TOP_RIGHT\",\"template\":\"IMAGE_TEXT_H\",\"templateType\":\"DEFAULT\"}";
        flyerPersuasionPWA = gson.fromJson(flyerPersuasionConfigDT, new TypeToken<Persuasion>() {
        }.getType());
        ReflectionTestUtils.setField(persuasionUtil, "flyerExclusiveRatesPersuasionPWA", flyerPersuasionDT);
        String flyerDealPersuasionMock = "{\"data\":[{\"iconurl\":\"https://promos.makemytrip.com/GCC/flyer/flystayreview.png\",\"style\":{\"iconHeight\":27,\"iconWidth\":139},\"persuasionType\":\"FLYER_EXCLUSIVE_RATES_PERSUASION\",\"hasAction\":false,\"html\":false}],\"placeholder\":\"PLACEHOLDER_TOP_RIGHT\",\"template\":\"IMAGE_TEXT_H\",\"templateType\":\"DEFAULT\"}";
        flyerDealRatesPersuasionDT = gson.fromJson(flyerPersuasionConfigDT, new TypeToken<Persuasion>() {
        }.getType());
        flyerDealRatesPersuasionApps = gson.fromJson(flyerPersuasionConfigDT, new TypeToken<Persuasion>() {
        }.getType());
        flyerDealRatesPersuasionPWA = gson.fromJson(flyerPersuasionConfigDT, new TypeToken<Persuasion>() {
        }.getType());
        ReflectionTestUtils.setField(persuasionUtil, "flyerDealRatesPersuasionDT", flyerDealRatesPersuasionDT);
        ReflectionTestUtils.setField(persuasionUtil, "flyerDealRatesPersuasionPWA", flyerPersuasionPWA);
        ReflectionTestUtils.setField(persuasionUtil, "flyerDealRatesPersuasionApps", flyerPersuasionAPP);
        ReflectionTestUtils.setField(persuasionUtil, "scarcityPriceBottomPersuasionApps", scarcityPriceBottomPersuasionApps);

        String oldToNewPersuasionConfig = "{\"pc__peitho\":\"test\"}";
        ReflectionTestUtils.setField(persuasionUtil,"oldPersuasionClassToNewConfig",oldToNewPersuasionConfig);


        String gccMetaWalletCashbackPersuasionConfigDT = "{\"data\":[{\"id\":\"62bd9677feb541192bd483a1\",\"iconurl\":\"https://promos.makemytrip.com/Growth/Images/B2C/flyer_new_tag_listing.png\",\"style\":{\"styleClasses\":[\"pc__flyerExclusiveTag\"]},\"persuasionType\":\"FLYER_EXCLUSIVE_RATES_PERSUASION\",\"hasAction\":false,\"html\":false}],\"placeholder\":\"PC_TOP_RIGHT\",\"template\":\"IMAGE_TEXT_H\",\"templateType\":\"DEFAULT\"}";
        gccMetaWalletCashbackPersuasionDT = gson.fromJson(gccMetaWalletCashbackPersuasionConfigDT, new TypeToken<Persuasion>() {
        }.getType());
        ReflectionTestUtils.setField(persuasionUtil, "gccMetaWalletCashbackPersuasionDT", gccMetaWalletCashbackPersuasionDT);

        String gccMetaWalletCashbackPersuasionConfigAPP = "{\"data\":[{\"id\":\"62bd9677feb541192bd483a1\",\"iconurl\":\"https://promos.makemytrip.com/Growth/Images/B2C/flyer_new_tag_listing.png\",\"style\":{\"styleClasses\":[\"pc__flyerExclusiveTag\"]},\"persuasionType\":\"FLYER_EXCLUSIVE_RATES_PERSUASION\",\"hasAction\":false,\"html\":false}],\"placeholder\":\"PC_TOP_RIGHT\",\"template\":\"IMAGE_TEXT_H\",\"templateType\":\"DEFAULT\"}";
        gccMetaWalletCashbackPersuasionAPP = gson.fromJson(gccMetaWalletCashbackPersuasionConfigAPP, new TypeToken<Persuasion>() {
        }.getType());
        ReflectionTestUtils.setField(persuasionUtil, "gccMetaWalletCashbackPersuasionApps", gccMetaWalletCashbackPersuasionAPP);

        String gccMetaWalletCashbackPersuasionConfigPWA = "{\"data\":[{\"id\":\"62bd9677feb541192bd483a1\",\"iconurl\":\"https://promos.makemytrip.com/Growth/Images/B2C/flyer_new_tag_listing.png\",\"style\":{\"styleClasses\":[\"pc__flyerExclusiveTag\"]},\"persuasionType\":\"FLYER_EXCLUSIVE_RATES_PERSUASION\",\"hasAction\":false,\"html\":false}],\"placeholder\":\"PC_TOP_RIGHT\",\"template\":\"IMAGE_TEXT_H\",\"templateType\":\"DEFAULT\"}";
        gccMetaWalletCashbackPersuasionPWA = gson.fromJson(gccMetaWalletCashbackPersuasionConfigPWA, new TypeToken<Persuasion>() {
        }.getType());
        ReflectionTestUtils.setField(persuasionUtil, "gccMetaWalletCashbackPersuasionPWA", gccMetaWalletCashbackPersuasionPWA);

        String noCostEmiPersuasion = "{\"data\":[{\"id\":\"NO_COST_EMI\",\"persuasionType\":\"NO_COST_EMI\",\"hasAction\":false,\"style\":{\"bgColor\":\"#E6FFF9\"},\"html\":true,\"isHtml\":true}],\"placeholder\":\"PLACEHOLDER_PRICE_BOTTOM\",\"template\":\"DEAL_BOX_IMAGE_TEXT\",\"templateType\":\"DEFAULT\"}";
        noCostEmiApplicablePersuasion = gson.fromJson(noCostEmiPersuasion, new TypeToken<Persuasion>() {}.getType());
        ReflectionTestUtils.setField(persuasionUtil, "noCostEmiApplicablePersuasion", noCostEmiApplicablePersuasion);

        mmtBlackPersuasion = new Persuasion();
        mmtBlackPersuasion.setData(new ArrayList<>());
        mmtBlackPersuasion.getData().add(new com.mmt.hotels.model.persuasion.response.PersuasionData());
        ReflectionTestUtils.setField(persuasionUtil, "mmtBlackMyCashPersuasion", mmtBlackPersuasion);
        Persuasion persuasion = new Persuasion();
        persuasion.setData(new ArrayList<>());
        ReflectionTestUtils.setField(persuasionUtil, "couponsReviewPagePersuasionsApps", persuasion);

        String benefitPersuasion = "{\"data\":[{\"id\":\"3863\",\"persuasionType\":\"Gift_Card\",\"text\":\"Your<b>GiftCard</b>willbesentviaWhatsAppandEmailadaybeforecheck-in\",\"extraData\":{\"iconUrl\":null,\"style\":{\"iconWidth\":41,\"iconHeight\":37}}}],\"style\":{\"horizontalSpace\":12,\"verticalSpace\":12,\"borderColor\":\"#d8d8d8\",\"borderSize\":\"2\",\"cornerRadii\":\"8\",\"textColor\":\"#4a4a4a\"},\"placeholder\":\"PLACEHOLDER_PRICE_BOTTOM_M1\",\"template\":\"IMAGE_TEXT_H\"}";
        hotelBenefitPersuasion=gson.fromJson(benefitPersuasion,new TypeToken<Persuasion>(){}.getType());
        ReflectionTestUtils.setField(persuasionUtil,"hotelBenefitPersuasion",hotelBenefitPersuasion);

    }

    @Test
    public void buildHiddenGemPersuasion() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        PersuasionObject persuasionObject = persuasionUtil.buildHiddenGemPersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNull(persuasionObject);

        searchWrapperHotelEntity.setHiddenGemPersuasionText("Hidden Gem Persuasion");
        persuasionObject = persuasionUtil.buildHiddenGemPersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNotNull(persuasionObject);
    }

    @Test
    public void buildHiddenGemIconPersuasion() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        PersuasionObject persuasionObject = persuasionUtil.buildHiddenGemIconPersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNull(persuasionObject);

        searchWrapperHotelEntity.setHiddenGem(true);
        persuasionObject = persuasionUtil.buildHiddenGemIconPersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNotNull(persuasionObject);
    }


    @Test
    public void buildHomeStaysTitlePersuasion() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        PersuasionObject persuasionObject = persuasionUtil.buildHomeStaysTitlePersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNull(persuasionObject);

        HomeStayDetails homeStayDetails = new HomeStayDetails();
        searchWrapperHotelEntity.setHomeStayDetails(homeStayDetails);
        persuasionObject = persuasionUtil.buildHomeStaysTitlePersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNull(persuasionObject);

        homeStayDetails.setStayType(Constants.STAY_TYPE_HOTEL);
        searchWrapperHotelEntity.setHomeStayDetails(homeStayDetails);
        persuasionObject = persuasionUtil.buildHomeStaysTitlePersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNull(persuasionObject);

        homeStayDetails.setStayType(Constants.STAY_TYPE_HOMESTAY);
        searchWrapperHotelEntity.setHomeStayDetails(homeStayDetails);
        persuasionObject = persuasionUtil.buildHomeStaysTitlePersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNotNull(persuasionObject);

        searchWrapperHotelEntity.setLastBooked(true);
        persuasionObject = persuasionUtil.buildHomeStaysTitlePersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNull(persuasionObject);
    }

    @Test
    public void buildHomeStaysSubTitlePersuasion() {

        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        PersuasionObject persuasionObject = persuasionUtil.buildHomeStaysSubTitlePersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNull(persuasionObject);

        HomeStayDetails homeStayDetails = new HomeStayDetails();
        searchWrapperHotelEntity.setHomeStayDetails(homeStayDetails);
        persuasionObject = persuasionUtil.buildHomeStaysSubTitlePersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNull(persuasionObject);

        homeStayDetails.setStayTypeInfo("Home Stay Sub Title");
        searchWrapperHotelEntity.setHomeStayDetails(homeStayDetails);
        persuasionObject = persuasionUtil.buildHomeStaysSubTitlePersuasion(searchWrapperHotelEntity, "Apps");
        Assert.assertNotNull(persuasionObject);
    }

    @Test
    public void buildFlyerExclusiveRatesPersuasionForReviewPageTest() {
        Map<String, Persuasion> result = persuasionUtil.buildFlyerExclusiveRatesPersuasionForReviewPage("Desktop");
        Assert.assertNotNull(result);

        result = persuasionUtil.buildFlyerExclusiveRatesPersuasionForReviewPage("PWA");
        Assert.assertNotNull(result);

        result = persuasionUtil.buildFlyerExclusiveRatesPersuasionForReviewPage("Android");
        Assert.assertNotNull(result);

        //For GCC
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");
        result = persuasionUtil.buildFlyerExclusiveRatesPersuasionForReviewPage("Desktop");
        Assert.assertNotNull(result);

        result = persuasionUtil.buildFlyerExclusiveRatesPersuasionForReviewPage("PWA");
        Assert.assertNotNull(result);

        result = persuasionUtil.buildFlyerExclusiveRatesPersuasionForReviewPage("Android");
        Assert.assertNotNull(result);
        MDC.clear();
    }
    @Test
    public void buildHotelPersuasionOfExclusiveDealForDetailTest() {
        SearchRoomsResponse searchRoomsResponse=new SearchRoomsResponse();
        persuasionUtil.buildHotelPersuasionOfExclusiveDealForDetail(searchRoomsResponse);
        Assert.assertNotNull(searchRoomsResponse.getHotelPersuasions());
        Assert.assertNotNull(((Map<String,PersuasionData>)searchRoomsResponse.getHotelPersuasions()).get(EXCLUSIVE_DEAL_TAG));
        Assert.assertEquals(((Map<String,PersuasionData>)searchRoomsResponse.getHotelPersuasions()).get(EXCLUSIVE_DEAL_TAG).getIconurl(),EXCLUSIVE_DEAL_IMAGE_URL);
    }
    @Test
    public void buildHotelPersuasionOfExclusiveDealForReviewAndThankyouTest() {
        HotelResult hotelResult=new HotelResult();
        hotelResult.setApplicableHotelCategoryDataWeb(new LinkedHashMap<>());
        persuasionUtil.buildHotelPersuasionOfExclusiveDealForReviewAndThankyou(hotelResult);
        Assert.assertNotNull(hotelResult.getApplicableHotelCategoryDataWeb());
        Assert.assertNotNull(hotelResult.getApplicableHotelCategoryDataWeb().get(EXCLUSIVE_DEAL_TAG));
        Assert.assertEquals(hotelResult.getApplicableHotelCategoryDataWeb().get(EXCLUSIVE_DEAL_TAG).getImageUrl(),EXCLUSIVE_DEAL_IMAGE_URL);
    }

    @Test
    public void initTest() {
        persuasionUtil.init();
    }

    @Test
    public void testAddShortStayPeithoPersuasionToHotelPersuasion() {
        Hotel hotel = new Hotel();
        String uspShortStayValue = "Star Gazing lights";
        List<String> hotelPersuasion = Arrays.asList("PC_MIDDLE_1");
        persuasionUtil.addShortStayPeithoPersuasionToHotelPersuasion(hotel, uspShortStayValue, hotelPersuasion);
        Map<Object, Object> hotelPersuasionsMap = (Map<Object, Object>) hotel.getHotelPersuasions();
        Assert.assertNotNull(hotelPersuasionsMap);
        PersuasionObject hotelMapPers = (PersuasionObject) hotelPersuasionsMap.get(Constants.SHORTSTAYS_PERSUASION_PLACEHOLDER_ID_MAP);
        Assert.assertNotNull(hotelMapPers);
        Assert.assertEquals(Constants.SHORTSTAYS_PERSUASION_TEMPLATE_TYPE, hotelMapPers.getTemplateType());
        Assert.assertEquals(Constants.SHORTSTAYS_PERSUASION_PLACEHOLDER_ID_MAP, hotelMapPers.getPlaceholder());
        Assert.assertEquals(Constants.IMAGE_TEXT_H, hotelMapPers.getTemplate());
        Assert.assertEquals(1, hotelMapPers.getData().size());
        PersuasionData hotelMapPersuasionData = hotelMapPers.getData().get(0);
        Assert.assertFalse(hotelMapPersuasionData.isHasAction());
        Assert.assertFalse(hotelMapPersuasionData.isHtml());
        Assert.assertEquals(Constants.SHORTSTAYS_PERSUASION_ID_MAP, hotelMapPersuasionData.getId());
        Assert.assertEquals(Constants.SHORTSTAYS_PERSUASION_TYPE, hotelMapPersuasionData.getPersuasionType());
        Assert.assertEquals(uspShortStayValue, hotelMapPersuasionData.getText());
        PersuasionStyle hotelMapPersuasionStyle = hotelMapPersuasionData.getStyle();
        Assert.assertNotNull(hotelMapPersuasionStyle);
        Assert.assertEquals(Constants.SHORTSTAYS_STYLE_TEXT_COLOR_MAP, hotelMapPersuasionStyle.getTextColor());
        Assert.assertEquals(Constants.SHORTSTAYS_STYLE_FONT, hotelMapPersuasionStyle.getFontSize());
    }

    @Test
    public void buildGccMetaWalletCashbackPersuasionForReviewPageTest() {
        Map<String, Persuasion> result = persuasionUtil.buildGccMetaWalletCashbackPersuasionForReviewPage("Desktop");
        Assert.assertNotNull(result);

        result = persuasionUtil.buildGccMetaWalletCashbackPersuasionForReviewPage("PWA");
        Assert.assertNotNull(result);

        result = persuasionUtil.buildGccMetaWalletCashbackPersuasionForReviewPage("Android");
        Assert.assertNotNull(result);
    }

    @Test
    public void updatePersuasionsForDesktopAndPwaTest(){
        String persuasion = "{\"PC_MIDDLE_9\":{\"template\":\"IMAGE_TEXT_H\",\"templateType\":\"DEFAULT\",\"data\":[{\"hasAction\":false,\"persuasionType\":\"CONTEXTUAL\",\"style\":{\"styleClasses\":[\"pc__peitho\"]},\"html\":false,\"id\":\"61279086c1d1773ceb83c662\",\"text\":\"100+guestslikeyouratedtheirstayVeryGood\",\"multiPersuasionPriority\":0}],\"placeholder\":\"PC_MIDDLE_9\"}}";
        Map<String, Map<String, Object>> persuasionMap = gson.fromJson(persuasion, new TypeToken<Map<String, Map<String, Object>>>() {
        }.getType());
        persuasionUtil.updatePersuasionsForDesktopAndPwa(persuasionMap);
    }

    @Test
    public void buildBlackPersuasionForReviewPageTest() {

        BlackInfo blackInfo = new BlackInfo();
        blackInfo.setInclusionsList(new ArrayList<>());
        Inclusion inclusion1 = new Inclusion();
        inclusion1.setValue("mmt black discount");
        Inclusion inclusion2 = new Inclusion();
        inclusion2.setValue("my cash discount");
        blackInfo.getInclusionsList().add(inclusion1);
        blackInfo.getInclusionsList().add(inclusion2);

        //Test-1 match black persuasions
        Assert.assertNotNull(persuasionUtil.buildBlackPersuasionForReviewPage(blackInfo, false));
        Assert.assertEquals("mmt black discount.\nmy cash discount", persuasionUtil.buildBlackPersuasionForReviewPage(blackInfo, false).getData().get(0).getText());

        //Test-2 BlackInfo null
        Assert.assertNull(persuasionUtil.buildBlackPersuasionForReviewPage(null, false));

    }

    @Test
    public void buildBlackPersuasionForReviewPageTrueTest() {

        BlackInfo blackInfo = new BlackInfo();
        blackInfo.setInclusionsList(new ArrayList<>());
        Inclusion inclusion1 = new Inclusion();
        inclusion1.setValue("mmt black discount");
        Inclusion inclusion2 = new Inclusion();
        inclusion2.setValue("my cash discount");
        blackInfo.getInclusionsList().add(inclusion1);
        blackInfo.getInclusionsList().add(inclusion2);

        //Test-1 match black persuasions
        persuasionUtil.buildBlackPersuasionForReviewPage(blackInfo, true);
    }

    @Test
    public void buildCouponPersuasionsForReviewPageTest(){
        Map<String, Persuasion> result = persuasionUtil.buildCouponPersuasionsForReviewPage("abc");
        Assert.assertNotNull(result);
    }

    @Test
    public void buildLoyaltyCashbackPersuasions_test() {
        BestCoupon coupon = new BestCoupon();
        coupon.setLoyaltyOfferMessage("Test");
        Map<String, PersuasionResponse > persuasionMap = new HashMap<>();
        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("Test");
        persuasionUtil.buildLoyaltyCashbackPersuasions(coupon, persuasionMap);
    }

    @Test
    public void testBuildLoyaltyCashbackPersuasions_WithValidInput() {
        // Arrange
        BestCoupon coupon = mock(BestCoupon.class);
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();
        int myPartnerCashback = 100; // Cashback amount
        HeroTierDetails heroTierDetails = mock(HeroTierDetails.class);

        // Mocking HeroTierDetails data
        when(heroTierDetails.getTierName()).thenReturn("GOLD");
        when(heroTierDetails.getTextColor()).thenReturn("#000000");
        when(heroTierDetails.getTierIcon()).thenReturn("gold_icon_url");
        when(heroTierDetails.getBgGradient()).thenReturn(mock(List.class));

        // Mocking PolyglotService
        when(polyglotService.getTranslatedData(ConstantsTranslation.MYPARTNER_TIER_OFFER_TEXT))
                .thenReturn("Reward for {TIER_NAME}: {CASHBACK_AMOUNT}");

        // Act
        persuasionUtil.buildLoyaltyCashbackPersuasions(coupon, persuasionMap, myPartnerCashback, heroTierDetails);

        // Assert
        assertTrue(persuasionMap.containsKey(CASHBACK_HERO_OFFER_PERSUASION_NODE));
        PersuasionResponse persuasion = persuasionMap.get(CASHBACK_HERO_OFFER_PERSUASION_NODE);
        assertNotNull(persuasion);

        // Check the persuasion text
        assertEquals("Reward for GOLD: 100", persuasion.getPersuasionText());

        // Check the style setup (icon and styling related tests)
        Style style = persuasion.getStyle();
        assertNotNull(style);
        assertEquals("#000000", style.getTextColor());
        assertNotNull(style.getBgGradient());
        assertEquals("gold_icon_url", persuasion.getIconUrl());
    }

    @Test
    public void testBuildLoyaltyCashbackPersuasions_WithNoCashback() {
        // Arrange
        BestCoupon coupon = new BestCoupon();
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();
        int myPartnerCashback = 0; // No cashback value
        HeroTierDetails heroTierDetails = mock(HeroTierDetails.class);
        coupon.setLoyaltyOfferMessage("Test");

        // Mocking PolyglotService
        when(polyglotService.getTranslatedData(ConstantsTranslation.MYPARTNER_TIER_OFFER_TEXT))
                .thenReturn("");
        when(polyglotService.getTranslatedData(ConstantsTranslation.LOYALTY_OFFER_TEXT))
                .thenReturn("");

        // Act
        persuasionUtil.buildLoyaltyCashbackPersuasions(coupon, persuasionMap, myPartnerCashback, heroTierDetails);

        // Assert
        // Since there's no cashback, the fallback method is expected to be called
        assertEquals(1, persuasionMap.size());
    }

    @Test
    public void testBuildLoyaltyCashbackPersuasions_WithNullTierDetails() {
        // Arrange
        BestCoupon coupon = new BestCoupon();
        coupon.setLoyaltyOfferMessage("Test");
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();
        int myPartnerCashback = 100; // Valid cashback value
        HeroTierDetails heroTierDetails = null;
        when(polyglotService.getTranslatedData(ConstantsTranslation.LOYALTY_OFFER_TEXT))
                .thenReturn("");

        // Act
        persuasionUtil.buildLoyaltyCashbackPersuasions(coupon, persuasionMap, myPartnerCashback, heroTierDetails);

        // Assert
        // Since heroTierDetails is null, the fallback method is expected to be called
        assertEquals(1, persuasionMap.size());
    }

    @Test
    public void testBuildLoyaltyCashbackPersuasions_WithEmptyPersuasionText() {
        // Arrange
        BestCoupon coupon = new BestCoupon();
        coupon.setLoyaltyOfferMessage("Test");
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();
        int myPartnerCashback = 100;
        HeroTierDetails heroTierDetails = mock(HeroTierDetails.class);

        // Mocking empty translated text
        when(polyglotService.getTranslatedData(ConstantsTranslation.MYPARTNER_TIER_OFFER_TEXT)).thenReturn("");
        when(polyglotService.getTranslatedData(ConstantsTranslation.LOYALTY_OFFER_TEXT))
                .thenReturn("");

        // Act
        persuasionUtil.buildLoyaltyCashbackPersuasions(coupon, persuasionMap, myPartnerCashback, heroTierDetails);

        // Assert
        // If there is no persuasion text, fallback logic should be followed
        assertEquals(1, persuasionMap.size());
    }

    @Test
    public void buildBusExclusiveRatesPersuasionForReviewPageTest(){
        Assert.assertNotNull(persuasionUtil.buildBusExclusiveRatesPersuasionForReviewPage("Android"));
        Assert.assertNotNull(persuasionUtil.buildBusExclusiveRatesPersuasionForReviewPage("Desktop"));
        Assert.assertNotNull(persuasionUtil.buildBusExclusiveRatesPersuasionForReviewPage("ios"));
        Assert.assertNotNull(persuasionUtil.buildBusExclusiveRatesPersuasionForReviewPage("pwa"));
    }

    @Test
    public void buildTrainExclusiveRatesPersuasionForReviewPageTest(){
        Assert.assertNotNull(persuasionUtil.buildTrainExclusiveRatesPersuasionForReviewPage("Android"));
        Assert.assertNotNull(persuasionUtil.buildTrainExclusiveRatesPersuasionForReviewPage("Desktop"));
        Assert.assertNotNull(persuasionUtil.buildTrainExclusiveRatesPersuasionForReviewPage("ios"));
        Assert.assertNotNull(persuasionUtil.buildTrainExclusiveRatesPersuasionForReviewPage("pwa"));
    }

    @Test
    public void buildVistaraExclusiveRatesPersuasionForReviewPageTest(){
        Assert.assertNotNull(persuasionUtil.buildVistaraExclusiveRatesPersuasionForReviewPage("Android"));
        Assert.assertNotNull(persuasionUtil.buildVistaraExclusiveRatesPersuasionForReviewPage("Desktop"));
        Assert.assertNotNull(persuasionUtil.buildVistaraExclusiveRatesPersuasionForReviewPage("ios"));
        Assert.assertNotNull(persuasionUtil.buildVistaraExclusiveRatesPersuasionForReviewPage("pwa"));
    }

    @Test
    public void testBuildHotelBenefitPersuasionForReviewPage_withValidBenefitInfo() {
        // Arrange
        HotelBenefitInfo benefitInfo = new HotelBenefitInfo();
        benefitInfo.setText("Benefit Text");
        benefitInfo.setIconUrl("http://example.com/icon.png");
        benefitInfo.setBenefitType("Benefit Type");
        // Act
        Persuasion result = persuasionUtil.buildHotelBenefitPersuasionForReviewPage(benefitInfo);

        // Assert
        Assert.assertNotNull(result);
        Assert.assertEquals("Benefit Text", result.getData().get(0).getText());
        Assert.assertEquals("http://example.com/icon.png", result.getData().get(0).getExtraData().getIconUrl());
        Assert.assertEquals("Benefit Type", result.getData().get(0).getPersuasionType());
    }

    @Test
    public void testBuildHotelBenefitPersuasionForReviewPage_withNullBenefitInfo() {
        // Act
        Persuasion result = persuasionUtil.buildHotelBenefitPersuasionForReviewPage(null);

        // Assert
        Assert.assertNull(result);
    }

    @Test
    public void testBuildHotelBenefitPersuasionForReviewPage_withEmptyData() {
        // Arrange
        HotelBenefitInfo benefitInfo = new HotelBenefitInfo();
        // Act
        Persuasion result = persuasionUtil.buildHotelBenefitPersuasionForReviewPage(benefitInfo);

        // Assert
        Assert.assertNotNull(result);
        Assert.assertEquals(null, result.getData().get(0).getExtraData().getIconUrl());
        Assert.assertEquals(null, result.getData().get(0).getPersuasionType());
    }

    @Test
    public void testSetIconUrl_withValidIconUrl() {
        // Arrange
        HotelBenefitInfo benefitInfo = new HotelBenefitInfo();
        benefitInfo.setText("Benefit Text");
        benefitInfo.setTitle("DummyTitle");
        benefitInfo.setIconUrl("http://example.com/icon.png");
        benefitInfo.setBenefitType("Gift_Card");
        // Act
        Persuasion result = persuasionUtil.buildHotelBenefitPersuasionForReviewPage(benefitInfo);
        String validIconUrl = "http://example.com/icon.png";
        // Act
        result.getData().get(0).getExtraData().setIconUrl(
                benefitInfo.getIconUrl() != null ? benefitInfo.getIconUrl() : null
        );

        // Assert
        assertEquals(validIconUrl, result.getData().get(0).getExtraData().getIconUrl());
    }

    @Test
    public void testSetIconUrl_withNullIconUrl() {
        // Arrange
        HotelBenefitInfo benefitInfo = new HotelBenefitInfo();
        benefitInfo.setText("Benefit Text");
        benefitInfo.setTitle("DummyTitle");
        benefitInfo.setIconUrl(null);
        benefitInfo.setBenefitType("Gift_Card");
        // Act
        Persuasion result = persuasionUtil.buildHotelBenefitPersuasionForReviewPage(benefitInfo);

        // Act
        result.getData().get(0).getExtraData().setIconUrl(
                benefitInfo.getIconUrl() != null ? benefitInfo.getIconUrl() : null
        );

        // Assert
        assertNull(result.getData().get(0).getExtraData().getIconUrl());
    }

    @Test
    public void buildHomestayPersuasionForReviewPage_ValidVariant1_ReturnsPersuasionMap() {
        String price = "1000";
        String variant = "1";
        Mockito.when(polyglotService.getTranslatedData(SBPP_VALUE_1_PERS_TEXT)).thenReturn("Price is {currency}{price}");

        Map<String, Persuasion> result = persuasionUtil.buildHomestayPersuasionForReviewPage(price, variant, Constants.HINDI_RUPEE);

        Assert.assertNotNull(result);
        Assert.assertFalse(result.isEmpty());
        Assert.assertEquals("Price is ₹1000", result.get(scarcityPriceBottomPersuasionApps.getPlaceholder()).getData().get(1).getText());
    }

    @Test
    public void buildHomestayPersuasionForReviewPage_ValidVariant2_ReturnsPersuasionMap() {
        String price = "2000";
        String variant = "2";
        Mockito.when(polyglotService.getTranslatedData(SBPP_VALUE_2_PERS_TEXT)).thenReturn("Total cost is {currency}{price}");

        Map<String, Persuasion> result = persuasionUtil.buildHomestayPersuasionForReviewPage(price, variant, Constants.HINDI_RUPEE);

        Assert.assertNotNull(result);
        Assert.assertFalse(result.isEmpty());
        Assert.assertEquals("Total cost is ₹2000", result.get(scarcityPriceBottomPersuasionApps.getPlaceholder()).getData().get(1).getText());
    }

    @Test
    public void buildHomestayPersuasionForReviewPage_InvalidVariant_ReturnsEmptyMap() {
        String price = "3000";
        String variant = "3";

        Map<String, Persuasion> result = persuasionUtil.buildHomestayPersuasionForReviewPage(price, variant, Constants.HINDI_RUPEE);

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void buildHomestayPersuasionForReviewPage_NullScarcityPriceBottomPersuasionApps_ReturnsEmptyMap() {
        ReflectionTestUtils.setField(persuasionUtil, "scarcityPriceBottomPersuasionApps", scarcityPriceBottomPersuasionApps);
        String price = "4000";
        String variant = "1";
        Mockito.when(polyglotService.getTranslatedData(SBPP_VALUE_1_PERS_TEXT)).thenReturn("Total cost is {currency}{price}");
        Map<String, Persuasion> result = persuasionUtil.buildHomestayPersuasionForReviewPage(price, variant, Constants.HINDI_RUPEE);

        Assert.assertNotNull(result);
    }

    @Test
    public void buildHomestayPersuasionForReviewPage_NullDataInScarcityPriceBottomPersuasionApps_ReturnsEmptyMap() {
        scarcityPriceBottomPersuasionApps.setData(null);
        String price = "5000";
        String variant = "1";

        Map<String, Persuasion> result = persuasionUtil.buildHomestayPersuasionForReviewPage(price, variant, Constants.HINDI_RUPEE);

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void buildHomestayPersuasionForReviewPage_EmptyDataInScarcityPriceBottomPersuasionApps_ReturnsEmptyMap() {
        scarcityPriceBottomPersuasionApps.setData(new ArrayList<>());
        String price = "6000";
        String variant = "1";

        Map<String, Persuasion> result = persuasionUtil.buildHomestayPersuasionForReviewPage(price, variant, Constants.HINDI_RUPEE);

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
    }
}
