package com.mmt.hotels.clientgateway.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.businessobjects.IHGInclusionConfig;
import com.mmt.hotels.clientgateway.businessobjects.RoomDataResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.MobConfigPropsConsul;
import com.mmt.hotels.clientgateway.consul.consulHelper.FunnelRange;
import com.mmt.hotels.clientgateway.consul.consulHelper.HighValueCallSupportDetails;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.enums.UpgradeType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.AvailRoomsSearchCriteria;
import com.mmt.hotels.clientgateway.request.CityOverviewRequest;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.request.Field;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.request.GroupBookingRequest;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.request.MultiCurrencyInfo;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.request.StaticDetailCriteria;
import com.mmt.hotels.clientgateway.request.dayuse.Slot;
import com.mmt.hotels.clientgateway.request.modification.ModifiedGuestCount;
import com.mmt.hotels.clientgateway.request.modification.ModifiedRoomStayCandidate;
import com.mmt.hotels.clientgateway.response.AvailRoomsResponse;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicy;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicyType;
import com.mmt.hotels.clientgateway.response.BookedInclusion;
import com.mmt.hotels.clientgateway.response.Coupon;
import com.mmt.hotels.clientgateway.response.HotelPermissions;
import com.mmt.hotels.clientgateway.response.IconType;
import com.mmt.hotels.clientgateway.response.LocationDetail;
import com.mmt.hotels.clientgateway.response.PricingDetails;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUsePersuasion;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.moblanding.CardData;
import com.mmt.hotels.clientgateway.response.moblanding.CardInfo;
import com.mmt.hotels.clientgateway.response.moblanding.ListPersonalizationResponse;
import com.mmt.hotels.clientgateway.response.moblanding.SupportDetails;
import com.mmt.hotels.clientgateway.response.rooms.MmtExclusive;
import com.mmt.hotels.clientgateway.response.rooms.RatePlan;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.response.rooms.Space;
import com.mmt.hotels.clientgateway.response.rooms.SpaceData;
import com.mmt.hotels.clientgateway.response.searchHotels.BottomSheet;
import com.mmt.hotels.clientgateway.response.searchHotels.BottomSheetData;
import com.mmt.hotels.clientgateway.restexecutors.HESRestExecutor;
import com.mmt.hotels.clientgateway.restexecutors.SearchRoomsExecutor;
import com.mmt.hotels.clientgateway.restexecutors.UserServiceExecutor;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.UserName;
import com.mmt.hotels.clientgateway.thirdparty.response.UserPersonalDetail;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResult;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.BlackBenefits;
import com.mmt.hotels.model.persuasion.response.Persuasion;
import com.mmt.hotels.model.request.CityOverviewHesRequest;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.RequestIdentifier;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.request.payment.UserDetail;
import com.mmt.hotels.model.response.MpFareHoldStatus;
import com.mmt.hotels.model.response.flyfish.RoomSummary;
import com.mmt.hotels.model.response.pricing.AvailDetails;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.FlexiCancelAddOn;
import com.mmt.hotels.model.response.pricing.FlexiCancelAddOnDetails;
import com.mmt.hotels.model.response.pricing.FlexiCancelDetails;
import com.mmt.hotels.model.response.pricing.FlexiCancelPolicies;
import com.mmt.hotels.model.response.pricing.ForexCouponDetails;
import com.mmt.hotels.model.response.pricing.FullPayment;
import com.mmt.hotels.model.response.pricing.HotelRates;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.LowestRateAPResp;
import com.mmt.hotels.model.response.pricing.MealPlan;
import com.mmt.hotels.model.response.pricing.OccupancyDetails;
import com.mmt.hotels.model.response.pricing.Selected;
import com.mmt.hotels.model.response.staticdata.AmendmentPolicies;
import com.mmt.hotels.model.response.staticdata.ChatbotInfo;
import com.mmt.hotels.model.response.staticdata.HooksData;
import com.mmt.hotels.model.response.staticdata.PriceVariationType;
import com.mmt.hotels.model.response.staticdata.SleepingBedInfo;
import com.mmt.hotels.model.response.staticdata.SleepingDetails;
import com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement;
import com.mmt.hotels.model.response.txn.CouponInfo;
import com.mmt.hotels.model.response.txn.CouponStatus;
import com.mmt.hotels.model.response.txn.Hotels;
import com.mmt.hotels.model.response.txn.PersistedHotel;
import com.mmt.hotels.model.response.txn.PersistedMultiRoomData;
import com.mmt.hotels.model.response.txn.PersistedTariffInfo;
import com.mmt.hotels.pojo.listing.personalization.BGLinearGradient;
import com.mmt.hotels.pojo.listing.personalization.IconTag;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.propertymanager.config.PropertyManager;
import com.mmt.scrambler.ScramblerClient;
import com.mmt.scrambler.exception.ScramblerClientException;
import com.mmt.scrambler.utils.HashType;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.util.ReflectionTestUtils;
import org.junit.jupiter.api.DisplayName;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomRatePlan;
import com.mmt.hotels.clientgateway.response.staticdetail.ExperienceInclusions;
import com.mmt.hotels.clientgateway.consul.properties.ExtraAdultChildInclusionConfig;
import com.mmt.hotels.model.response.pricing.ExtraGuestDetail;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FLYER_EXCLUSIVE_DEAL_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FOREX_DEAL_TITLE_REVIEW_PAGE_DT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.STAY_TIME_HOURS;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.TIMESLOT_AM_PM;
import static com.mmt.hotels.clientgateway.util.Utility.buildRoomStayQualifierFromRoomStayCandidates;
import static com.mmt.hotels.clientgateway.util.Utility.getcompleteURL;
import static com.mmt.hotels.clientgateway.util.Utility.isBookingDeviceDesktop;
import static com.mmt.hotels.clientgateway.util.Utility.isDomOrIntl;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNotSame;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class UtilityTest {

    @InjectMocks
    Utility utility;

    @Mock
    PolyglotService polyglotService;

    @Spy
    DateUtil dateUtil;

    @Mock
    MobConfigPropsConsul mobConfigPropsConsul;

    @Mock
    CommonConfigConsul commonConfigConsul;

    @Mock
    PropertyManager propertyManager;

    @Mock
    private NumberFormat numberFormatter;

    @Mock
    ObjectMapperUtil objectMapperUtil;

    private static Gson gson = new Gson();

    @Mock
    private UserServiceExecutor userServiceExecutor;

    @Mock
    SearchRoomsExecutor searchRoomsExecutor;

    @Mock
    private HESRestExecutor hesRestExecutor;
    private Set<String> allowedSegments;

    @Mock
    private TotalPricing totalPricingMock;

    @Mock
    private PersuasionUtil persuasionUtil;
    
	@Value("${mypartner.location.restricted.icon.url}")
	private String mypartnerRestrictedIcon;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }


    @Before
    public void init(){
        String abc = "King Bed,Queen Bed,Double Bed,Twin Bed,Single Bed,Standard Bed,Bunk Bed,Futon,Sofa Bed,Sofa cum bed,Mattress,2 Seater Sofa,3 Seater Sofa,5 Seater Sofa";
        List<String> bedTypePriorityOrder = Arrays.asList(abc.split(","));
        ReflectionTestUtils.setField(utility,"bedTypePriorityOrder", bedTypePriorityOrder);

        String str = "Breakfast,Lunch Or Dinner,Lunch,Dinner,meals";
        List<String> inclusionList = Arrays.asList(str.split(","));
        ReflectionTestUtils.setField(utility,"inclusionList",inclusionList);
        String requestToCallBackDataConfig = "{\"LISTING\":{\"infoText\":\"LISTING_CALLBACK_INFOTEXT\",\"iconUrl\":\"\",\"cta\":\"LISTING_CALLBACK_CTATEXT\",\"bottomSheetData_heading\":\"LISTING_BOTTOMSHEET_CALLBACK_HEADING\",\"bottomSheetData_infoText\":\"LISTING_BOTTOMSHEET_CALLBACK_INFOTEXT\",\"bottomSheetData_imageUrl\":\"\",\"bottomSheetData_cta\":\"LISTING_BOTTOMSHEET_CALLBACK_CTA\"},\"DETAIL\":{\"infoText\":\"DETAIL_CALLBACK_INFOTEXT\",\"iconUrl\":\"\",\"cta\":\"DETAIL_CALLBACK_CTATEXT\",\"bottomSheetData_heading\":\"DETAIL_BOTTOMSHEET_CALLBACK_HEADING\",\"bottomSheetData_infoText\":\"DETAIL_BOTTOMSHEET_CALLBACK_INFOTEXT\",\"bottomSheetData_imageUrl\":\"\",\"bottomSheetData_cta\":\"DETAIL_BOTTOMSHEET_CALLBACK_CTA\"},\"REVIEW\":{\"infoText\":\"REVIEW_CALLBACK_INFOTEXT\",\"iconUrl\":\"\",\"cta\":\"REVIEW_CALLBACK_CTATEXT\",\"bottomSheetData_heading\":\"REVIEW_BOTTOMSHEET_CALLBACK_HEADING\",\"bottomSheetData_infoText\":\"REVIEW_BOTTOMSHEET_CALLBACK_INFOTEXT\",\"bottomSheetData_imageUrl\":\"\",\"bottomSheetData_cta\":\"REVIEW_BOTTOMSHEET_CALLBACK_CTA\"}}";
        Map<String, Map<String, String>> requestToCallBackDataMap = gson.fromJson(requestToCallBackDataConfig, new TypeToken<Map<String,Map<String, String>> >() {
        }.getType());
        ReflectionTestUtils.setField(utility, "requestToCallBackDataMap", requestToCallBackDataMap);

        numberFormatter = NumberFormat.getNumberInstance(new Locale("en", "IN"));
        numberFormatter.setMaximumFractionDigits(0); // No decimals
        numberFormatter.setMinimumFractionDigits(0); // Ensure no trailing zeros
        ReflectionTestUtils.setField(utility, "numberFormatter", numberFormatter);

        String hotelIdMap = "{\"201609091139127239\":\"Test\"}";
        Map<String, String> requestToCallHotelIdMap = gson.fromJson(hotelIdMap, new TypeToken<Map<String,String> >() {
        }.getType());
        String config = "{\"CABCASHBACKCARD\":{\"sequence\":13,\"cardInfo\":{\"index\":1,\"subType\":\"CARD\",\"id\":\"CABINFO\",\"titleText\":\"Free Drop to your Hotel\",\"subText\":\"Get Free Private Cab worth {currency} {cab_price} to this property. Apply offer while reviewing your booking.\",\"hasFilter\":false,\"cardAction\":[{\"title\":\"More Details\",\"webViewUrl\":\"https://promos.makemytrip.com/ih-cabs-cross-sell-terms.html\"}],\"iconURL\":\"https://promos.makemytrip.com/images/CDN_upload/cab_icon_gold.png\",\"bgLinearGradient\":{\"start\":\"#FFFFFF\",\"end\":\"#D3E7FF\",\"center\":\"#FFFFFF\",\"direction\":\"diagonal_bottom\"},\"iconTags\":{\"text\":\"Flyer Exclusive Deal\",\"bgGradient\":{\"start\":\"#ffffff\",\"end\":\"#ffffff\"},\"borderColor\":\"#0c58b4\"},\"templateId\":\"inline_info_banner_2\"}},\"FOREXCASHBACKCARD\":{\"sequence\":13,\"cardInfo\":{\"index\":1,\"subType\":\"CARD\",\"id\":\"FOREXINFO\",\"titleText\":\"Get Forex Cashback with this booking\",\"subText\":\"On this booking you will get Forex Cashback worth {currency} {forex_price}\",\"hasFilter\":false,\"cardAction\":[{\"title\":\"More Details\",\"webViewUrl\":\"https://promos.makemytrip.com/ih-forex-deal-150224.html\"}],\"iconURL\":\"https://promos.makemytrip.com/images/CDN_upload/forexicon_new.png\",\"iconTags\":{\"text\":\"Flyer Exclusive Deal\",\"bgGradient\":{\"start\":\"#ffffff\",\"end\":\"#ffffff\"},\"borderColor\":\"#0c58b4\"},\"bgLinearGradient\":{\"start\":\"#FFFFFF\",\"end\":\"#D3E7FF\",\"center\":\"#FFFFFF\",\"direction\":\"diagonal_bottom\"},\"templateId\":\"inline_info_banner_2\"}}}";
        Map<String, CardData> cardConfig = gson.fromJson(config, new TypeToken<Map<String,CardData> >() {
        }.getType());
        ReflectionTestUtils.setField(utility, "ihCashbackCardConfig", cardConfig);
        ReflectionTestUtils.setField(utility, "requestToCallHotelIdMap", requestToCallHotelIdMap);
        ReflectionTestUtils.setField(utility, "allowedAppVersionAndroid", "9.1.6");
        ReflectionTestUtils.setField(utility, "allowedAppVersionIOS", "9.1.6");
        ReflectionTestUtils.setField(utility, "allowedAppVersionAndroidCurrency", "9.1.6");
        ReflectionTestUtils.setField(utility, "allowedAppVersionIOSCurrency", "9.1.6");
        allowedSegments = new HashSet<>();
        allowedSegments.add("segment1");
        ReflectionTestUtils.setField(utility, "businessIdentificationAffiliates", new HashSet<>(Arrays.asList("affiliate1", "affiliate2", "affiliate3")));
        ReflectionTestUtils.setField(utility, "businessIdentificationSegments", new HashSet<>(Arrays.asList("segment1", "segment2", "segment3")));
        String requestCallToBookString = "{\"LISTING\":{\"infoText\":\"CALLBACK_INFOTEXT_V2\",\"iconUrl\":\"https://promos.makemytrip.com/GCC/MiscIcons/CTBAnimationMedium.gif\",\"cta\":\"CALLBACK_CTATEXT_V2\",\"bottomSheetData\":{\"heading\":\"BOTTOMSHEET_CALLBACK_HEADING_V2\",\"infoText\":\"BOTTOMSHEET_CALLBACK_INFOTEXT_V2\",\"footerText\":\"BOTTOMSHEET_CALLBACK_FOOTERTEXT_V2\",\"cta\":\"BOTTOMSHEET_CALLBACK_CTATEXT_V2\"}},\"DETAIL\":{\"cta\":\"CALLBACK_CTATEXT_V2\",\"iconUrl\":\"https://promos.makemytrip.com/GCC/MiscIcons/CTBAnimationMedium.gif\",\"bottomSheetData\":{\"heading\":\"BOTTOMSHEET_CALLBACK_HEADING_V2\",\"infoText\":\"BOTTOMSHEET_CALLBACK_INFOTEXT_V2\",\"footerText\":\"BOTTOMSHEET_CALLBACK_FOOTERTEXT_V2\",\"cta\":\"BOTTOMSHEET_CALLBACK_CTATEXT_V2\"}},\"REVIEW\":{\"infoText\":\"CALLBACK_CTATEXT_V2\",\"desc\":\"CALLBACK_INFOTEXT_V2\",\"iconUrl\":\" https://promos.makemytrip.com/GCC/MiscIcons/Callback_smallicon2x.png\",\"bgGradient\":{\"start\":\"#ffe0cb\",\"center\":\"\",\"end\":\"#ffffff\",\"direction\":\"diagonal_top\"},\"bottomSheetData\":{\"heading\":\"BOTTOMSHEET_CALLBACK_HEADING_V2\",\"infoText\":\"BOTTOMSHEET_CALLBACK_INFOTEXT_V2\",\"footerText\":\"BOTTOMSHEET_CALLBACK_FOOTERTEXT_V2\",\"cta\":\"BOTTOMSHEET_CALLBACK_CTATEXT_V2\"}}}";
        Map<String,BottomSheet> requestToCallBackDataConfigV2 = gson.fromJson(requestCallToBookString, new TypeToken<Map<String,BottomSheet> >() {
        }.getType());
        Map<String,BottomSheet> requestToCallBackDataConfigForB2C = gson.fromJson(requestCallToBookString, new TypeToken<Map<String,BottomSheet> >() {
        }.getType());
        Map<String,BottomSheet> requestToCallBackDataConfigForHighValue = gson.fromJson(requestCallToBookString, new TypeToken<Map<String,BottomSheet> >() {
        }.getType());
        ReflectionTestUtils.setField(utility, "requestToCallBackDataConfigV2", requestToCallBackDataConfigV2);
        ReflectionTestUtils.setField(utility, "requestToCallBackDataConfigB2C", requestToCallBackDataConfigForB2C);
        ReflectionTestUtils.setField(utility, "requestToCallBackDataConfigForHighValue", requestToCallBackDataConfigForB2C);
        IHGInclusionConfig ihgInclusionConfig = new IHGInclusionConfig();
        ihgInclusionConfig.setChainCodes(new HashSet<>(Collections.singletonList("IHG")));
        ihgInclusionConfig.setInclusionText("Inclusion Test Text");
        ReflectionTestUtils.setField(utility, "ihgInclusionConfig", ihgInclusionConfig);
        ChatbotInfo chatbotInfoConsul = new ChatbotInfo();
        Map<String, HooksData> hooks = new HashMap<>();
        HooksData hooksData = new HooksData();
        hooksData.setQuestion("Chatbot Info?");
        hooks.put("PL",hooksData);
        chatbotInfoConsul.setChatBotUrl("Chatbot Info");
        chatbotInfoConsul.setHooksIconUrl("Hooks Icon");
        chatbotInfoConsul.setExpandDelay(1000);
        chatbotInfoConsul.setPersuasionDelay(2000);
        Map<String, HooksData> chatbotInfoMediaV2 = new HashMap<>();
        chatbotInfoMediaV2.put("pool", hooksData);
        ReflectionTestUtils.setField(utility, "chatbotInfoConsul", chatbotInfoConsul);
        ReflectionTestUtils.setField(utility, "chatbotInfoMediaV2", chatbotInfoMediaV2);
    }
    @Test
    public void isValidAppVersionTest(){
        String appVersion="8.5.9";
        String allowedMinAppVersion="8.5.8";
        Assert.assertTrue(utility.isValidAppVersion(appVersion,allowedMinAppVersion));
        appVersion="8.5.6";
        allowedMinAppVersion="8.5.8";
        Assert.assertFalse(utility.isValidAppVersion(appVersion,allowedMinAppVersion));
        appVersion="8.5.8";
        allowedMinAppVersion="8.5.8.1";
        Assert.assertFalse(utility.isValidAppVersion(appVersion,allowedMinAppVersion));

    }

    @Test
    public void isExperimentOnTest() {
        Map<String, String> expData = new HashMap<>();
        expData.put("exp1", "t");
        expData.put("exp3", "true");
        Assert.assertTrue(utility.isExperimentOn(expData, "exp1"));
        Assert.assertFalse(utility.isExperimentOn(expData, "exp2"));
        Assert.assertTrue(utility.isExperimentOn(expData, "exp3"));


    }


    @Test
    public void getHotelierConversionFactor() {
        PersistedMultiRoomData persistedMultiRoomData = new PersistedMultiRoomData();
        persistedMultiRoomData.setHotelList(new ArrayList<>());
        persistedMultiRoomData.getHotelList().add(new PersistedHotel());
        persistedMultiRoomData.getHotelList().get(0).setTariffInfoList(new ArrayList<>());
        persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().add(new PersistedTariffInfo());
        Utility.getHotelierConversionFactor(persistedMultiRoomData);
    }

    @Test
    public void buildToolTipTest() {
        Assert.assertTrue(utility.buildToolTip("HOMESTAY"));
        Assert.assertFalse(utility.buildToolTip("HOTEL"));
    }

    @Test
    public void shouldDisplayOfferDiscountBreakupTest() {
        Assert.assertFalse(utility.shouldDisplayOfferDiscountBreakup(null));
        String expData="{\"RCPN\":\"t\",\"VIDEO\":\"0\",\"BNPL\":\"t\",\"MMRVER\":\"V3\",\"LSTNRBY\":\"t\",\"AARI\":\"t\",\"PAH\":\"5\",\"ADDV\":\"f\",\"HRSRFHS\":\"true\",\"REV\":\"4\",\"ADDON\":\"t\",\"PDO\":\"PN\",\"EMI\":\"f\",\"MC\":\"t\",\"APE\":\"10\",\"WSP\":\"f\",\"HBH\":\"f\",\"FBP\":\"f\",\"MRS\":\"t\",\"DPCR\":\"1\",\"PAH5\":\"f\",\"BRIN\":\"110\",\"HIS\":\"1234\",\"LVD\":\"\",\"HPI\":\"true\",\"OCCFCNR\":\"t\",\"NLP\":\"Y\",\"BLACK\":\"t\",\"SRR\":\"t\",\"LCS\":\"t\",\"CHPC\":\"t\",\"FLTRPRCBKT\":\"t\",\"LVI\":\"\",\"HSCFS\":\"4\",\"SPCR\":\"2\"}";
        Assert.assertFalse(utility.shouldDisplayOfferDiscountBreakup(utility.getExpDataMap(expData)));
        expData="{\"LSOF\":\"t\",\"VIDEO\":\"0\",\"BNPL\":\"t\",\"MMRVER\":\"V3\",\"LSTNRBY\":\"t\",\"AARI\":\"t\",\"PAH\":\"5\",\"ADDV\":\"f\",\"HRSRFHS\":\"true\",\"REV\":\"4\",\"ADDON\":\"t\",\"PDO\":\"PN\",\"EMI\":\"f\",\"MC\":\"t\",\"APE\":\"10\",\"WSP\":\"f\",\"HBH\":\"f\",\"FBP\":\"f\",\"MRS\":\"t\",\"DPCR\":\"1\",\"PAH5\":\"f\",\"BRIN\":\"110\",\"HIS\":\"1234\",\"LVD\":\"\",\"HPI\":\"true\",\"OCCFCNR\":\"t\",\"NLP\":\"Y\",\"BLACK\":\"t\",\"SRR\":\"t\",\"LCS\":\"t\",\"CHPC\":\"t\",\"FLTRPRCBKT\":\"t\",\"LVI\":\"\",\"HSCFS\":\"4\",\"SPCR\":\"2\"}\n";
        Assert.assertTrue(utility.shouldDisplayOfferDiscountBreakup(utility.getExpDataMap(expData)));
    }

    @Test
    public void isDetailPageAPITest(){
        Assert.assertFalse(utility.isDetailPageAPI(""));
        Assert.assertTrue(utility.isDetailPageAPI("cg/search-rooms/"));
        Assert.assertFalse(utility.isDetailPageAPI("cg/searcoms/"));
    }

    @Test
    public void isReviewPageAPITest(){
        Assert.assertFalse(utility.isReviewPageAPI(""));
        Assert.assertTrue(utility.isReviewPageAPI("cg/avail-rooms/"));
        Assert.assertFalse(utility.isReviewPageAPI("cg/searcoms/"));
    }

    @Test
    public void buildFullPaymentTest(){
        FullPayment fullPayment = new FullPayment();
        fullPayment.setFullPaymentSubText("test");
        fullPayment.setFullPaymentText("test1");
        fullPayment.setFinalPrice(2.0);
        com.mmt.hotels.clientgateway.response.FullPayment fullPaymentCg = utility.buildFullPayment(fullPayment);
        Assert.assertEquals(fullPaymentCg.getFullPaymentText(), "test1");
        Assert.assertEquals(fullPaymentCg.getFullPaymentSubText(), "test");
        Assert.assertEquals(fullPaymentCg.getFinalPrice(), 2.0,0);
    }

    @Test
    public void getComboNameTest() {
        when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("dsfee");
        Assert.assertNull(utility.getComboName(null));
        assertNotNull(utility.getComboName("AI"));
        assertNotNull(utility.getComboName("AO"));
        assertNotNull(utility.getComboName("BD"));
        assertNotNull(utility.getComboName("EP"));
        assertNotNull(utility.getComboName("CP"));
        assertNotNull(utility.getComboName("TMAP"));
        assertNotNull(utility.getComboName("SMAP"));
        assertNotNull(utility.getComboName("MAP"));
        assertNotNull(utility.getComboName("AP"));


    }

    @Test
    public void getGuestRoomKeyValueTest(){
        when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("");
        Map<String,Integer> map=new HashMap<>();
        Map<String, String> expMap=new HashMap<>();
        map.put("Bed",1);
        map.put("Room",2);
        assertNotNull(utility.getGuestRoomKeyValue(map,"HOTEL","IN", false, false, "", "entire", false, 0, expMap, false));
        assertNotNull(utility.getGuestRoomKeyValue(map,"HOTEL","IN", false, false, "","", false, 0, expMap, false));
        assertNotNull(utility.getGuestRoomKeyValue(map,"HOTEL","IN", false, true, "1 Bedroom","", false, 0, expMap, true));
    }

    @Test
    public void transformCancellationPolicyTest(){
        when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("");
        List<CancelPenalty> cancelPenaltyList=new ArrayList<>();
        CancelPenalty cancelPenalty=new CancelPenalty();
        cancelPenalty.setCancellationType(CancelPenalty.CancellationType.FREE_CANCELLATON);
        cancelPenalty.setFreeCancellationText("Free Cancellation");
        cancelPenaltyList.add(cancelPenalty);
        assertNotNull(utility.transformCancellationPolicy(cancelPenaltyList, true, false, null, null, null, null, Optional.empty(),"", false));
        cancelPenalty.setCancellationType(null);
        assertNotNull(utility.transformCancellationPolicy(cancelPenaltyList, true, false, null, null, null, null, Optional.empty(),"", false));
        MpFareHoldStatus mpFareHoldStatus=new MpFareHoldStatus();
        mpFareHoldStatus.setExpiry(new Date().getTime());
        mpFareHoldStatus.setBookingAmount(1);
        cancelPenalty.setCancellationType(CancelPenalty.CancellationType.FREE_CANCELLATON);
        Mockito.when(polyglotService.getTranslatedData(Mockito.eq(ConstantsTranslation.CANCEL_POLICY_BNPL_SUBTEXT_NEW_VARIANT))).thenReturn(ConstantsTranslation.CANCEL_POLICY_BNPL_SUBTEXT_ZERO_VARIANT);
        BookedCancellationPolicy result=utility.transformCancellationPolicy(cancelPenaltyList,false, false, null, null, null, null, Optional.of(mpFareHoldStatus),"", false);
        assertNotNull(result);
        Assert.assertEquals(ConstantsTranslation.CANCEL_POLICY_BNPL_SUBTEXT_ZERO_VARIANT, result.getSubText());
    }

    @Test
    public void transformCancellationPolicyForPartialRefundTest(){
        List<CancelPenalty> cancelPenaltyList=new ArrayList<>();
        CancelPenalty cancelPenalty=new CancelPenalty();
        cancelPenalty.setCancellationType(CancelPenalty.CancellationType.PARTIAL_REFUNDABLE);
        cancelPenaltyList.add(cancelPenalty);
        BookedCancellationPolicy bookedCancellationPolicy = utility.transformCancellationPolicy(cancelPenaltyList, true, false, null, null, "", null,null,"Partially Refundable till check-in", false);
        Assert.assertEquals("Partially Refundable till check-in",bookedCancellationPolicy.getText());
        Assert.assertEquals(IconType.DOUBLETICK,bookedCancellationPolicy.getIconType());
        Assert.assertEquals(BookedCancellationPolicyType.FC,bookedCancellationPolicy.getType());
    }

    @Test
    public void transformInclusionsTest() {
        when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("");
        List<MealPlan> mealPlanList = new ArrayList<>();
        MealPlan mealPlan = new MealPlan();
        mealPlan.setCode("EP");
        mealPlan.setValue("Value");
        mealPlanList.add(mealPlan);
        Map<String, String> mealPlanMap = new HashMap<>();
        mealPlanMap.put("CP", "Dinner");
        assertNotNull(utility.transformInclusions(mealPlanList, null, mealPlanMap, "DERBY", null, null, MapUtils.EMPTY_SORTED_MAP, false, null, null, null, null, "IN", false, IconType.CROSS, true, false));
        assertNotNull(utility.transformInclusions(mealPlanList, null, mealPlanMap, "DERBY", null, null, MapUtils.EMPTY_SORTED_MAP, true, null, null, null, null, "IN", false, IconType.CROSS, true, false));
        mealPlan.setCode("CP");
        assertNotNull(utility.transformInclusions(mealPlanList, null, mealPlanMap, "DERBY", null, null, MapUtils.EMPTY_SORTED_MAP, false, null, null, null, null, "IN", false, IconType.CROSS, true, false));
        assertNotNull(utility.transformInclusions(mealPlanList, null, mealPlanMap, "DERBY", null, null, MapUtils.EMPTY_SORTED_MAP, true, null, null, null, null, "IN", false, IconType.CROSS, true, false));

        utility.getCountDownLatch();
        List<Inclusion> inclusionList = new ArrayList<>();
        Inclusion inclusion = new Inclusion();
        inclusion.setCode("");
        inclusion.setValue("Free Breakfast");
        inclusionList.add(inclusion);
        assertNotNull(utility.transformInclusions(mealPlanList, inclusionList, mealPlanMap, "DERBY", null, null, MapUtils.EMPTY_SORTED_MAP, false, null, null, null, null, "IN", false, IconType.CROSS, true, false));
        assertNotNull(utility.transformInclusions(mealPlanList, inclusionList, mealPlanMap, "DERBY", null, null, MapUtils.EMPTY_SORTED_MAP, true, null, null, null, null, "IN", false, IconType.CROSS, true, false));

    }

    @Test
    public void freeChildInclusionTest() {

        List<BookedInclusion> inclusions = new ArrayList<>();
        inclusions = utility.transformInclusions(null, new ArrayList<>(), new HashMap<>(), null, null, null, MapUtils.EMPTY_SORTED_MAP, false, "free child text", null, null, null, "IN", false, IconType.CROSS, true, false);
        Assert.assertEquals(inclusions.get(0).getCategory(), "KIDS");

    }

    @Test
    public void testBuildMmtExclusiveNode(){
        List<Inclusion> inclusionsList = new ArrayList<>();
        Inclusion e = new Inclusion();
        e.setCode("Free Parking");
        inclusionsList.add(e);
        when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Best available rate");
        MmtExclusive mmtExclusive = utility.buildMmtExclusiveNode(inclusionsList,null);
        MmtExclusive result = new MmtExclusive();
        List<String> inclusion = new ArrayList<>();
        inclusion.add("Best available rate");
        inclusion.add("Free Parking");
        result.setInclusions(inclusion);
        Assert.assertEquals(result.getInclusions(),mmtExclusive.getInclusions());
    }

    @Test
    public void testBuildMmtExclusiveNodeWithLPG(){
        List<Inclusion> inclusionsList = new ArrayList<>();
        Inclusion e = new Inclusion();
        e.setCode("Lowest Price Guarantee");
        inclusionsList.add(e);
        when(polyglotService.getTranslatedData(ConstantsTranslation.MMT_EXCLUSIVE)).thenReturn("MMT_EXCLUSIVE");
        Map<String, String> experimentDataMap = new HashMap<>();
        experimentDataMap.put("gcclpg", "2");
        MmtExclusive mmtExclusive = utility.buildMmtExclusiveNode(inclusionsList, experimentDataMap);
        MmtExclusive result = new MmtExclusive();
        List<String> inclusion = new ArrayList<>();
        inclusion.add("MMT_EXCLUSIVE");
        inclusion.add("Lowest Price Guarantee");
        result.setInclusions(inclusion);
        Assert.assertEquals(result.getInclusions(),mmtExclusive.getInclusions());
    }

    @Test
    public void buildSlotTest(){
        SearchWrapperInputRequest searchWrapperInputRequest = new SearchWrapperInputRequest();
        SearchHotelsCriteria searchHotelsCriteria = new SearchHotelsCriteria();
        Slot slot = new Slot();
        slot.setDuration(3);
        slot.setTimeSlot(10);
        searchHotelsCriteria.setSlot(slot);
        utility.buildSlot( searchWrapperInputRequest,searchHotelsCriteria);
        Assert.assertEquals(3,searchWrapperInputRequest.getSlot().getDuration().intValue());
    }

    @Test
    public void addFunnelSourceToParameterMapTest(){
        Map<String, String[]> resultMap = new HashMap<>();
        PriceByHotelsRequestBody priceByHotelsRequestBody = new PriceByHotelsRequestBody();
        priceByHotelsRequestBody.setFunnelSource("DAYUSE");
        resultMap = utility.addFunnelSourceToParameterMap( priceByHotelsRequestBody, getParameterMap(false));
        Assert.assertEquals("DAYUSE",resultMap.get("funnelSource")[0]);
    }

    private Map<String, String[]> getParameterMap(boolean authCodePresent) {
        Map<String, String[]> paramaterMap = new HashMap<>();
        paramaterMap.put("correlationKey", new String[]{"6123da6f-aa49-4feb-ac2f-71b260b9e9a9"});
        if (authCodePresent) {
            paramaterMap.put("authCode", new String[]{"2J08wUWpHH9rrwL%2BCrbU3rvarwAC8fopWY5ooWq6tzdcyCS6qMlrghl%2B1cRZgBqOxpO3e2NOnkm%2Fj13JitRQVg" +
                    "%3D%3D"});
        }
        paramaterMap.put("srcClient", new String[]{"DESKTOP"});
        paramaterMap.put("language", new String[]{"eng"});
        paramaterMap.put("region", new String[]{"in"});
        paramaterMap.put("currency", new String[]{"INR"});
        paramaterMap.put("idContext", new String[]{"B2C"});
        paramaterMap.put("countryCode", null);
        return paramaterMap;
    }

    @Test
    public void testCalculateTimeSlot_Meridiem(){
        com.mmt.hotels.model.response.dayuse.Slot slot = new com.mmt.hotels.model.response.dayuse.Slot();
        slot.setDuration(3);
        slot.setTimeSlot("10");
        when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("10 AM - 1 PM");
        String result = utility.calculateTimeSlot_Meridiem(slot);
        Assert.assertEquals("10 AM - 1 PM",result);
    }
    @Test
    public void testCalculateTimeSlotMeridiem(){
        com.mmt.hotels.model.response.dayuse.Slot slot = new com.mmt.hotels.model.response.dayuse.Slot();
        slot.setDuration(3);
        slot.setTimeSlot("22");
        when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("10 PM - 1 AM");
        String result = utility.calculateTimeSlot_Meridiem(slot);
        Assert.assertEquals("10 PM - 1 AM",result);
    }

    @Test
    public void testTransformLateCheckout(){
        Map<String, DayUsePersuasion> map = new HashMap<>();
        DayUsePersuasion dayUsePersuasion = new DayUsePersuasion();
        dayUsePersuasion.setText("<font color=\\\"#955000\\\">Late checkout is not allowed for this booking</font>");
        map.put("CHECKOUT_MSG", dayUsePersuasion);
        when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("<font color=\\\"#955000\\\">Late checkout is not allowed for this booking</font>");
        utility.transformLateCheckout(map,new ArrayList<>());
    }

    @Test
    public void testThankYouCalculateTimeSlot_Meridiem(){
        com.mmt.hotels.model.request.dayuse.Slot slot = new com.mmt.hotels.model.request.dayuse.Slot();
        slot.setDuration(3);
        slot.setTimeSlot(10);
        when(polyglotService.getTranslatedData(TIMESLOT_AM_PM)).thenReturn("10 AM - 1 PM");
        when(polyglotService.getTranslatedData(STAY_TIME_HOURS)).thenReturn("HOURS");
        String result = utility.calculateTimeSlot_Meridiem(slot);
        Assert.assertEquals("10 AM - 1 PM (3 HOURS)",result);
    }
    @Test
    public void testIsCorpBudgetHotelFunnel(){
        String funnelSource = "CORPBUDGET";
        Assert.assertTrue(utility.isCorpBudgetHotelFunnel(funnelSource));

        funnelSource = "";
        Assert.assertFalse(utility.isCorpBudgetHotelFunnel(funnelSource));

        funnelSource = null;
        Assert.assertFalse(utility.isCorpBudgetHotelFunnel(funnelSource));
    }

    @Test
    public void testBuildScourceDT_PWA_WithGoogleFinderNew() {
        String source = "GOOGLEHOTELDFINDER_NEW";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source,deviceDetails);
        assertEquals("googlehoteldfinder_new", result);
    }

    @Test
    public void testBuildScourceDT_PWA_WithGoogleFinderUS() {
        String source = "GOOGLEHOTELDFINDER_DH_US";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("googlehoteldfinder_dh_us", result);
    }

    @Test
    public void testBuildScourceDT_PWA_WithGoogleFinderAE() {
        String source = "GOOGLEHOTELDFINDER_DH_AE";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("googlehoteldfinder_dh_ae", result);
    }

    @Test
    public void testBuildScourceDT_PWA_WithGoogleFinder() {
        String source = "GOOGLEHOTELDFINDER";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("googlehoteldfinder", result);
    }

    @Test
    public void testBuildScourceDT_PWA_WithTrivago() {
        String source = "trivago";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("trivago", result);
    }

    @Test
    public void testBuildScourceDT_PWA_WithNullSource() {
        String source = null;
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertNull(result);
    }

    @Test
    public void testBuildScourceDT_PWA_WithUnknownSource() {
        String source = "unknown";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("unknown", result);
    }

    @Test
    public void testBuildScourceDT_PWA_WithTafi() {
        String source = "Tafi";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("Tafi", result);
    }

    @Test
    public void testBuildScourceDT_PWA_WithSEO() {
        String source = "seo";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("seo", result);
    }

    @Test
    public void testBuildScourceDT_PWA_WithSEM() {
        String source = "sem";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("sem", result);
    }

    @Test
    public void testBuildScourceDT_PWA_WithMobileDevice() {
        String source = "seo";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.ANDROID);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals(TRAFFIC_SOURCE_SEO, result);
    }

    @Test
    public void testBuildScourceDT_PWA_WithPWADevice() {
        String source = "seo";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.BKG_DEVICE_PWA);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("seo", result);
    }

    @Test
    public void testBuildScourceDT_PWA_WithNullDeviceDetails() {
        String source = "seo";
        String result = utility.buildSourceTraffic(source, null);
        assertEquals(TRAFFIC_SOURCE_SEO, result);
    }

    public void buildSlot(HotelDetailsMobRequestBody hotelDetailsMobRequestBody, StaticDetailCriteria searchCriteria) {
        if (searchCriteria.getSlot() != null) {
            com.mmt.hotels.model.request.dayuse.Slot slot = new com.mmt.hotels.model.request.dayuse.Slot();
            slot.setDuration(searchCriteria.getSlot().getDuration());
            slot.setTimeSlot(searchCriteria.getSlot().getTimeSlot());
            hotelDetailsMobRequestBody.setSlot(slot);
        }
    }

    @Test
    public void testBuildSlot(){
        HotelDetailsMobRequestBody hotelDetailsMobRequestBody = new HotelDetailsMobRequestBody();
        StaticDetailCriteria searchCriteria = new StaticDetailCriteria();
        PriceByHotelsRequestBody priceByHotelsRequestBody = new PriceByHotelsRequestBody();
        Slot slot = new Slot();
        slot.setDuration(3);
        slot.setTimeSlot(10);
        searchCriteria.setSlot(slot);
        utility.buildSlot(hotelDetailsMobRequestBody, searchCriteria);
        utility.buildSlot(priceByHotelsRequestBody,searchCriteria);
    }

    @Test
    public void getCheckinAndCheckoutForDailyUseTest(){
        ReflectionTestUtils.invokeMethod(utility,"getCheckinAndCheckoutForDailyUse","Test");
    }

    @Test
    public void setAdvancePurchaseTest(){
        ReflectionTestUtils.invokeMethod(utility,"setAdvancePurchase","2022-10-25");
    }

    @Test
    public void setLengthOfStayTest(){
        ReflectionTestUtils.invokeMethod(utility,"setLengthOfStay","2022-10-21","2022-10-25");
    }

    @Test
    public void setAdultCountTest(){
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setAdultCount(2);
        List<Integer> ageList = new ArrayList<>();
        ageList.add(2);
        roomStayCandidate.setChildAges(ageList);
        List<RoomStayCandidate> list = new ArrayList();
        list.add(roomStayCandidate);
        ReflectionTestUtils.invokeMethod(utility,"setAdultCount",list);
        ReflectionTestUtils.invokeMethod(utility,"setChildCount",list);
    }

    @Test
    public void getTotalChildrenFromRequestTest(){
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setAdultCount(2);
        List<Integer> ageList = new ArrayList<>();
        ageList.add(2);
        roomStayCandidate.setChildAges(ageList);
        List<RoomStayCandidate> list = new ArrayList();
        list.add(roomStayCandidate);
        ReflectionTestUtils.invokeMethod(utility,"getTotalChildrenFromRequest",list);
    }

    @Test
    public void getTotalAdultsFromRequestTest(){
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setAdultCount(2);
        List<RoomStayCandidate> list = new ArrayList();
        list.add(roomStayCandidate);
        ReflectionTestUtils.invokeMethod(utility,"getTotalAdultsFromRequest",list);
    }

    @Test
    public void setLoggingParametersToMDCTest(){
        List<RoomStayCandidate> list = new ArrayList<>();
        list.add(new RoomStayCandidate());
        utility.setLoggingParametersToMDC(list,"2022-10-21","2022-10-25");
    }

    @Test
    public void getGroupPriceTextOrSavingPercKey_Test() {
        String resp = Utility.getGroupPriceTextOrSavingPercKey("Hello", "DESKTOP");
        Assert.assertEquals(resp, "Hello_DESKTOP");
    }

    @Test
    public void getGroupPriceTextOrSavingPercKey_Test_client_non_desktop() {
        String resp = Utility.getGroupPriceTextOrSavingPercKey("Hello", "ANDROID");
        Assert.assertEquals(resp, "Hello");
    }

    @Test
    public void appendPageContextToUrlTest() {
        String url = null;
        String pageContext = null;

        String resp = Utility.appendPageContextToURL(url, pageContext);
        Assert.assertEquals(null, resp);

        url = "url";
        resp = Utility.appendPageContextToURL(url, pageContext);
        Assert.assertEquals("url", resp);

        pageContext = "DETAIL";
        resp = Utility.appendPageContextToURL(url, pageContext);
        Assert.assertEquals("url&pageContext=DETAIL", resp);
    }

    @Test
    public void testCreateBedInfoTextFromBedInfoMap(){
        LinkedHashMap<String, Integer> bedInfoMap  = new LinkedHashMap<>();
        bedInfoMap.put("Double Bed",2);
        bedInfoMap.put("Cot",1);
        Assert.assertEquals("2 Double Beds, 1 Cot",utility.createBedInfoTextFromBedInfoMap(bedInfoMap));
        bedInfoMap.put("Queen Bed",4);
        Assert.assertEquals("4 Queen Beds, 2 Double Beds, 1 Cot",utility.createBedInfoTextFromBedInfoMap(bedInfoMap));
        bedInfoMap.put("King Bed",3);
        Assert.assertEquals("3 King Beds, 4 Queen Beds, 2 Double Beds, 1 Cot",utility.createBedInfoTextFromBedInfoMap(bedInfoMap));
        bedInfoMap.put("Cot",4);
        Assert.assertEquals("3 King Beds, 4 Queen Beds, 2 Double Beds, 4 Cots",utility.createBedInfoTextFromBedInfoMap(bedInfoMap));

    }

    private List<SleepingInfoArrangement>buildSleepingInfoArrangementList(String spaceType){
        List<SleepingInfoArrangement> sleepingInfoArrangementList = new ArrayList<>();
        SleepingInfoArrangement sleepingInfoArrangement = new SleepingInfoArrangement();
        sleepingInfoArrangement.setSubText("Sleeps 3 guests");
        LinkedHashMap<String, Integer> bedInfos = new LinkedHashMap<>();
        bedInfos.put("King Bed",4);
        bedInfos.put("Mattress",3);
        sleepingInfoArrangement.setBedInfos(bedInfos);
        sleepingInfoArrangement.setBed(10);
        sleepingInfoArrangement.setDescriptionText("4 King Bed, 3 Mattress, Garden View, Attached Bathroom, Attached Balcony");
        sleepingInfoArrangement.setGuest(11);
        sleepingInfoArrangement.setOpenCardText("4 King Bed, 3 Mattress, Room service, Air Conditioning, Wifi, Mineral Water, Bathroom, Housekeeping, Laundry Service, Work Desk, Closet, Mini Fridge, Mirror, Hangers, Chair, Center Table, Blackout curtains, Intercom, Flooring, Blanket, Pillows, TV, Western Toilet Seat, Hot & Cold Water, Shower, Toiletries, Towels, Newspaper, Fan, Balcony, Sanitizers");
        sleepingInfoArrangement.setSpaceType(spaceType);
        sleepingInfoArrangement.setMaxCapacity(13);
        sleepingInfoArrangement.setBathRoom(2);
        sleepingInfoArrangement.setBedRoom(3);
        sleepingInfoArrangementList.add(sleepingInfoArrangement);
        return sleepingInfoArrangementList;
    }
    @Test
    public void testpostProcessSleepingInfoArrangement(){
        LinkedHashMap<String, List<SleepingInfoArrangement>> spaceIdToSleepingInfoArr = new LinkedHashMap<>();
        LinkedHashMap<String, SleepingDetails > spaceIdToSleepingDetailsMap = new LinkedHashMap<>();
        spaceIdToSleepingInfoArr.put("1234",buildSleepingInfoArrangementList("bedroom"));
        spaceIdToSleepingInfoArr.put("4567",buildSleepingInfoArrangementList("living_room"));
        SleepingDetails sleepingDetails = new SleepingDetails();
        sleepingDetails.setExtraBedCount(1);
        sleepingDetails.setBedCount(7);
        sleepingDetails.setBedRoomCount(3);
        sleepingDetails.setMinOccupancy(2);
        sleepingDetails.setMaxOccupancy(14);
        sleepingDetails.setExtraBedCount(1);
        List<SleepingBedInfo> bedInfo = new ArrayList<>();
        List<SleepingBedInfo> extraBedInfo = new ArrayList<>();
        SleepingBedInfo sleepingBedInfo1 = new SleepingBedInfo();
        sleepingBedInfo1.setBedType("King Bed");
        sleepingBedInfo1.setBedCount(4);
        SleepingBedInfo sleepingBedInfo2 = new SleepingBedInfo();
        sleepingBedInfo2.setBedType("Mattress");
        sleepingBedInfo2.setBedCount(3);
        bedInfo.add(sleepingBedInfo1);
        extraBedInfo.add(sleepingBedInfo2);
        sleepingDetails.setBedInfo(bedInfo);
        sleepingDetails.setExtraBedInfo(extraBedInfo);
        spaceIdToSleepingDetailsMap.put("1234",sleepingDetails);
        spaceIdToSleepingDetailsMap.put("4567",sleepingDetails);
        List<RoomDetails> roomDetails = new ArrayList<>();
        RoomDetails roomDetails1 = new RoomDetails();
        SpaceData spaceData = new SpaceData();
        List<Space> spaces = new ArrayList<>();
        Space space = new Space();
        space.setSpaceType("bedroom");
        space.setSleepingInfoArrangement(buildSleepingInfoArrangementList("bedroom"));
        space.setSpaceId("1234");
        spaces.add(space);
        spaceData.setSpaces(spaces);
        roomDetails1.setPrivateSpaces(spaceData);
        roomDetails.add(roomDetails1);
        utility.addSleepingInfoArrangementIntoRoomDetails(roomDetails,spaceIdToSleepingInfoArr);
    }

    @Test
    public void testBuildBedInfoText(){
        List<Space> spaces = new ArrayList<>();
        Space space = new Space();
        space.setSpaceType("bedroom");
        space.setSleepingInfoArrangement(buildSleepingInfoArrangementList("bedroom"));
        spaces.add(space);
        Assert.assertEquals("4 King Beds, 3 Mattresses",utility.buildBedInfoText(spaces));

    }

    @Test
    public void isPasPayModeTest() {
        boolean resp = Utility.isPasPayMode(null);
        Assert.assertFalse(resp);
        resp = Utility.isPasPayMode("PAH");
        Assert.assertFalse(resp);
        resp = Utility.isPasPayMode("PAS");
        Assert.assertTrue(resp);
    }

    @Test
    public void buildCityOverviewHesRequestTest(){
        CityOverviewRequest cityOverviewRequest = new CityOverviewRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource("HOMESTAY");
        SearchCriteria searchCriteria = new SearchCriteria();
        searchCriteria.setCityCode("CTDUB");
        searchCriteria.setCountryCode("IN");
        searchCriteria.setCurrency("INR");
        cityOverviewRequest.setRequestDetails(requestDetails);
        cityOverviewRequest.setSearchCriteria(searchCriteria);
        CityOverviewHesRequest cityOverviewHesRequest = utility.buildCityOverviewHesRequest(cityOverviewRequest, "test", "test");
        assertNotNull(cityOverviewHesRequest);
    }

    @Test
    public void buildDescriptionTextTest(){
        String description="Congrats! You are getting a discount of {CURRENCY_SYMBOL} {AMOUNT} on this property as you are booking in advance. Limited time offer only!.";
        String amount="1955";
        String askedCurrency="USD";
        String finalText = utility.buildDescriptionText(description,amount,askedCurrency);
        assertNotNull(finalText);
    }

    @Test
    public void setPaginatedToMDCTest() {

        SearchHotelsCriteria sc = new SearchHotelsCriteria();
        utility.setPaginatedToMDC(sc);
        sc.setLastHotelId("test");
        utility.setPaginatedToMDC(sc);


    }
    @Test
    public void testGetTotalAdultsFromBKGModRequest(){
        List<ModifiedRoomStayCandidate> roomStayCandidates = new ArrayList<>();
        List<ModifiedGuestCount> guestCountList1 = new ArrayList<>();
        List<ModifiedGuestCount> guestCountList2 = new ArrayList<>();
        ModifiedGuestCount modifiedGuestCount1 = new ModifiedGuestCount();
        ModifiedGuestCount modifiedGuestCount2 = new ModifiedGuestCount();
        ModifiedRoomStayCandidate modifiedRoomStayCandidate1 = new ModifiedRoomStayCandidate();
        ModifiedRoomStayCandidate modifiedRoomStayCandidate2 = new ModifiedRoomStayCandidate();
        modifiedGuestCount1.setAdultCount(3);
        modifiedGuestCount2.setAdultCount(4);
        List<Integer> childAges1 = new ArrayList<>();
        List<Integer> childAges2 = new ArrayList<>();
        childAges1.add(4); childAges1.add(10);
        childAges2.add(5); childAges2.add(7); childAges2.add(12);
        modifiedGuestCount1.setChildAges(childAges1);
        modifiedGuestCount2.setChildAges(childAges2);
        guestCountList1.add(modifiedGuestCount1);
        guestCountList2.add(modifiedGuestCount2);
        modifiedRoomStayCandidate1.setGuestCountList(guestCountList1);
        modifiedRoomStayCandidate2.setGuestCountList(guestCountList2);
        roomStayCandidates.add(modifiedRoomStayCandidate1);
        roomStayCandidates.add(modifiedRoomStayCandidate2);
        Pair<Integer, Integer> adultsAndChildCount = utility.getTotalAdultsAndChildFromBKGModRequest(roomStayCandidates);
        Assert.assertEquals(7,adultsAndChildCount.getKey().intValue());
        Assert.assertEquals(5,adultsAndChildCount.getValue().intValue());
    }

    @Test
    public void testUpdateKeys(){
        Map<String,String> mp = new HashMap<>();
        mp.put("Hello World","ABCD");
        Map<String,String> res = utility.updateKeys(mp);
    }

    @Test
    public void getFreeChildInclusionTest() {

        BookedInclusion bookedInclusion = utility.getFreeChildInclusion("test", "test");
        Assert.assertEquals(bookedInclusion.getText(),"test");

        Assert.assertNull(utility.getFreeChildInclusion("", "test"));
    }

    @Test
    public void buildRoomStayDistributionTest() {
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        Map<String, String> expData = new HashMap<>();
        List<Integer> guestCountList1 = new ArrayList<>();
        List<Integer> guestCountList2 = new ArrayList<>();
        GuestCount modifiedGuestCount1 = new GuestCount();
        GuestCount modifiedGuestCount2 = new GuestCount();
        RoomStayCandidate modifiedRoomStayCandidate1 = new RoomStayCandidate();
//        RoomStayCandidate modifiedRoomStayCandidate2 = new RoomStayCandidate();
        modifiedRoomStayCandidate1.setRooms(2);
        modifiedRoomStayCandidate1.setAdultCount(4);
        modifiedGuestCount1.setCount("3");
        modifiedGuestCount2.setCount("4");
        List<Integer> childAges1 = new ArrayList<>();
        List<Integer> childAges2 = new ArrayList<>();
        childAges1.add(4); childAges1.add(10);
        childAges2.add(5); childAges2.add(7); childAges2.add(12);
        modifiedGuestCount1.setAges(childAges1);
        modifiedGuestCount2.setAges(childAges2);
        guestCountList1.add(1);
        guestCountList2.add(6);
        modifiedRoomStayCandidate1.setChildAges(guestCountList1);
//        modifiedRoomStayCandidate2.setChildAges(guestCountList2);
        roomStayCandidates.add(modifiedRoomStayCandidate1);
//        roomStayCandidates.add(modifiedRoomStayCandidate2);
        utility.buildRoomStayDistribution(roomStayCandidates, expData);
        String rscValue = utility.buildRscValue(roomStayCandidates);
        assertNotNull(rscValue);
    }

    @Test
    public void getLengthOfStayTest() {
        Assert.assertEquals(utility.getLengthOfStay("",""), 0);
        Assert.assertEquals(utility.getLengthOfStay("2023-10-12","2023-10-11"), 1);
        Assert.assertEquals(utility.getLengthOfStay("11-11-2020","xyz"), 1);
    }

    @Test
    public void updateCancellationPolicyTextTest() {
        RatePlan ratePlan = new RatePlan();
        BookedCancellationPolicy cancellationPolicy = new BookedCancellationPolicy();
        ratePlan.setCancellationPolicy(cancellationPolicy);
        utility.updateCancellationPolicyText(true, ratePlan);
    }

    @Test
    public void testBuildRequestIdentifier(){
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setJourneyId("JourneyId123");
        requestDetails.setRequestId("RequestId123");
        requestDetails.setSessionId("SessionId123");
        RequestIdentifier requestIdentifier = utility.buildRequestIdentifier(requestDetails);
        Assert.assertTrue(requestDetails.getJourneyId().equalsIgnoreCase(requestIdentifier.getJourneyId()));
        Assert.assertTrue(requestDetails.getRequestId().equalsIgnoreCase(requestIdentifier.getRequestId()));
        Assert.assertTrue(requestDetails.getSessionId().equalsIgnoreCase(requestIdentifier.getSessionId()));
    }
    @Test
    public void testEvaluateBedInfoText(){
        LinkedHashMap<String, Integer> bedInfoMap =  new LinkedHashMap<>();
        bedInfoMap.put("double bed",2);
        bedInfoMap.put("single bed",3);
        Pair<String,Integer> bedTypeCount = ReflectionTestUtils.invokeMethod(utility,"evaluateBedInfoText",bedInfoMap,"double bed",0);
        Assert.assertEquals("2 double beds",bedTypeCount.getKey());
        bedTypeCount = ReflectionTestUtils.invokeMethod(utility,"evaluateBedInfoText",bedInfoMap,"single bed",0);
        Assert.assertEquals("3 single beds",bedTypeCount.getKey());

    }
    @Test
    public void getLOSTest(){
        int los = utility.getLOS("2023-07-25","2023-07-31");
        Assert.assertEquals(6,los);
    }

    @Test
    public void isMyPartnerRequestTest(){
        Assert.assertTrue(Utility.isMyPartnerRequest("CTA","MYPARTNER"));
        Assert.assertFalse(Utility.isMyPartnerRequest("PERSONAL",""));
    }

    @Test
    public void checkMealPlanTest(){
        Assert.assertEquals(Utility.checkMealPlan("AO"),"RO");
        Assert.assertEquals(Utility.checkMealPlan("AI"),"AP");
        Assert.assertEquals(Utility.checkMealPlan("CP"),"CP");
        Assert.assertEquals(Utility.checkMealPlan("TMAP"),"MAP");
    }
    @Test
    public void testisOHSExpEnable() {
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put("OHS", "true");
        Assert.assertTrue(utility.isOHSExpEnable("hostel", expDataMap));
        Assert.assertFalse(utility.isOHSExpEnable("hotel", expDataMap));
    }
    public void isNotGCCDayUseTest(){
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setSiteDomain("IN");
        requestDetails.setFunnelSource("HOTELS");
        filterCountRequest.setRequestDetails(requestDetails);
        Assert.assertTrue(Utility.isNotGCCDayUse(filterCountRequest));
    }

    @Test
    public void updateNRCancellationPolicyInclusionTest() {
        when(polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_SUBTEXT)).thenReturn("On Cancellation, You will not get any refund");
        AvailRoomsResponse availRoomsResponse = new AvailRoomsResponse();
        RatePlan ratePlan = new RatePlan();
        ratePlan.setCancellationPolicy(new BookedCancellationPolicy());
        ratePlan.getCancellationPolicy().setType(BookedCancellationPolicyType.NR);
        utility.updateNRCancellationPolicyInclusion(false, "NR", availRoomsResponse);
        Assert.assertNull(ratePlan.getInclusionsList());

        availRoomsResponse.setRateplanlist(Arrays.asList(ratePlan));
        utility.updateNRCancellationPolicyInclusion(true, "NR", availRoomsResponse);
        assertNotNull(ratePlan.getInclusionsList());
        assertNotNull(ratePlan.getInclusionsList().get(0));
        assertNotNull(ratePlan.getInclusionsList().get(0).getIconType());
        Assert.assertTrue(StringUtils.isNotEmpty(ratePlan.getInclusionsList().get(0).getText()));

    }

    @Test
    public void isDomOrIntl_test() {
        SearchHotelsCriteria hotelsCriteria = new SearchHotelsCriteria();
        hotelsCriteria.setCountryCode("IN");
        String result = isDomOrIntl(hotelsCriteria);
        assertNotNull(result);
        Assert.assertEquals(result, "DOM");
    }

    @Test
    public void isB2CFunnel_test() {
        MDC.put(MDCHelper.MDCKeys.IDCONTEXT.getStringValue(), "B2C");
        boolean result = utility.isB2CFunnel();
        Assert.assertTrue(result);
        MDC.clear();
    }

    @Test
    public void isIHFunnel_test() {
        String countryCode = "IN";
        String siteDomain = "AE";
        boolean result = utility.isIHFunnel(countryCode, siteDomain);
        Assert.assertFalse(result);
    }

    @Test
    public void isMyPartner_Test() {
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setProfileType("CTA");
        extendedUser.setAffiliateId("MYPARTNER");
        commonModifierResponse.setExtendedUser(extendedUser);
        boolean result = utility.isMyPartner(commonModifierResponse);
        Assert.assertTrue(result);
    }

    @Test
    public void isIHMyPartnerOrB2CFunnel_test() {
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setProfileType("CTA");
        extendedUser.setAffiliateId("MYPARTNER");
        commonModifierResponse.setExtendedUser(extendedUser);
        String countryCode = "IN";
        String siteDomain = "AE";
        boolean result = utility.isIHMyPartnerOrB2CFunnel(commonModifierResponse, siteDomain, countryCode);
        Assert.assertFalse(result);
        result = utility.isIHMyPartnerOrB2CFunnel(commonModifierResponse, "IN", "USA");
        Assert.assertTrue(result);
    }

    @Test
    public void concatenateDateAndTime_test() {
        String result = utility.concatenateDateAndTime("20 May", "11:59 PM");
        assertNotNull(result);
        Assert.assertEquals(result, "20 May, 11:59 PM");
    }

    @Test
    public void isBookingDeviceDesktop_test() {
        com.mmt.hotels.clientgateway.request.DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice("desktop");
        boolean result = isBookingDeviceDesktop(deviceDetails);
        Assert.assertEquals(result, true);
    }

    @Test
    public void testLogLuckyUserData(){
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setHydraResponse(null);
        commonModifierResponse.setExtendedUser(null);
        commonModifierResponse.setExpDataMap(null);
        utility.logLuckyUserData(commonModifierResponse, null, null);

        HydraResponse hydraResponse = new HydraResponse();
        hydraResponse.setHydraMatchedSegment(null);
        commonModifierResponse.setHydraResponse(hydraResponse);
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setUuid(null);
        commonModifierResponse.setExtendedUser(extendedUser);
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        commonModifierResponse.setExpDataMap(expDataMap);
        utility.logLuckyUserData(commonModifierResponse, null, "search-hotels");

        Set<String> hydraSegments = new HashSet<>();
        hydraSegments.add("r1145");
        hydraSegments.add("r1148");
        hydraResponse.setHydraMatchedSegment(hydraSegments);
        commonModifierResponse.setHydraResponse(hydraResponse);
        extendedUser = new ExtendedUser();
        extendedUser.setUuid("u123");
        commonModifierResponse.setExtendedUser(extendedUser);
        expDataMap.put(X_PERCENT_SELL_ON, TRUE);
        commonModifierResponse.setExpDataMap(expDataMap);
        utility.logLuckyUserData(commonModifierResponse, null, "search-rooms");

        expDataMap.put(X_PERCENT_SELL_ON, FALSE);
        commonModifierResponse.setExpDataMap(expDataMap);
        utility.logLuckyUserData(commonModifierResponse, null, "avail-rooms");
    }

    @Test
    public void getcompleteURLTest(){
        String result = getcompleteURL("abc",new HashMap<>(),"sdgcfw");
        assertNotNull(result);
    }

    @Test
    public void buildRoomStayQualifierFromRoomStayCandidatesTest(){
        List<com.mmt.hotels.model.request.RoomStayCandidate> list = new ArrayList<>();
        list.add(new com.mmt.hotels.model.request.RoomStayCandidate());
        list.get(0).setGuestCounts(new ArrayList<>());
        list.get(0).getGuestCounts().add(new GuestCount());
        list.get(0).getGuestCounts().get(0).setCount("2");
        list.get(0).getGuestCounts().get(0).setAges(new ArrayList<>());
        list.get(0).getGuestCounts().get(0).getAges().add(20);
        String result = buildRoomStayQualifierFromRoomStayCandidates(list,true);
        assertNotNull(result);
    }

    @Test
    public void getRequestParamTest(){
        Map<String, String> result =  Utility.getRequestParam(new HashMap<>());
        assertNotNull(result);
    }

    @Test
    public void isPropertyHotelOrResortTest(){
         boolean result = utility.isPropertyHotelOrResort(new ArrayList<>());
        List<HotelRates> hotelRatesList = new ArrayList<>();
        hotelRatesList.add(new HotelRates());
        hotelRatesList.get(0).setPropertyType("hotel");
        result = utility.isPropertyHotelOrResort(hotelRatesList);
        Assert.assertTrue(result);
    }

    @Test
    public void getTranslationFromPolyglotTest(){
        ReflectionTestUtils.invokeMethod(utility,"getTranslationFromPolyglot","Test");
    }

    @Test
    public void checkIfFilterValueExistsInAppliedFilterMapTest(){
        boolean result = utility.checkIfFilterValueExistsInAppliedFilterMap(new ArrayList<>());
        Assert.assertFalse(result);
        List<com.mmt.hotels.clientgateway.request.Filter> filterCriteria = new ArrayList<>();
        filterCriteria.add(new Filter());
        filterCriteria.get(0).setFilterGroup(FilterGroup.EXACT_ROOM_RECOMMENDATION);
        filterCriteria.get(0).setFilterValue("abc");
        result = utility.checkIfFilterValueExistsInAppliedFilterMap(filterCriteria);
        Assert.assertTrue(result);
    }

    @Test
    public void buildBgLinearGradientforForexTest(){
        BGLinearGradient result = utility.buildBgLinearGradientforForex();
        assertNotNull(result);
        Assert.assertEquals(GRADIENT_END_FOREX,result.getEnd());
    }

    @Test
    public void buildSubTextBgGradientforForexTest(){
        BGLinearGradient result = utility.buildSubTextBgGradientforForex();
        assertNotNull(result);
        Assert.assertEquals(SUB_GRADIENT_END,result.getEnd());
    }

    @Test
    public void testBuildBgLinearGradientForHotelCloud() {
        Utility utility = new Utility();
        BGLinearGradient result = utility.buildBgLinearGradientForHotelCloud();

        assertNotNull(result);
        assertEquals("#FFFFFF", result.getStart());
        assertEquals("#EBFEFB", result.getEnd());
        assertEquals("diagonal_bottom", result.getDirection());
        assertEquals("#FFFFFF", result.getCenter());
    }

    @Test
    public void testForexCouponsNodeAddition() {

        BestCoupon bestCoupon = new BestCoupon();
        bestCoupon.setForexDealMessage("Forex Deal Message");
        bestCoupon.setForexCashbackAmount(100.0);
        bestCoupon.setForexCouponDetails(new ForexCouponDetails());
        bestCoupon.getForexCouponDetails().setPersuasion_message("hello");
        Coupon forexCoupon = new Coupon();
        boolean isDeviceDesktop = true;

        // Act
        when(polyglotService.getTranslatedData(FOREX_DEAL_TITLE_REVIEW_PAGE_DT)).thenReturn(" worth %s{0}");
        utility.forexCouponsNodeAddition(bestCoupon, forexCoupon, isDeviceDesktop,0.0,0);

        // Assert
        Assert.assertEquals(COUPON_TYPE_BENEFIT, forexCoupon.getCouponType());
        Assert.assertEquals(CASHBACK, forexCoupon.getCouponTypeText());
        Assert.assertNull(forexCoupon.getTncText());
        Assert.assertEquals("Forex Deal Message worth %s100", forexCoupon.getTitle());
        Assert.assertEquals(100.0, forexCoupon.getCouponAmount(), 0.01);
//        utility.forexCouponsNodeAddition(bestCoupon, forexCoupon, false);
//        Assert.assertEquals("Forex Deal Message", forexCoupon.getTitle());

    }

    @Test
    public void modifyRoomStayCandidateRequestForHomestayFunnelDetailTest() {
        Filter filter = new Filter();
        filter.setFilterValue("2");
        SearchRoomsCriteria searchRoomsCriteria = new SearchRoomsCriteria();
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setAdultCount(2);
        roomStayCandidate.setRooms(2);
        roomStayCandidate.setChildAges(Arrays.asList(1));
        searchRoomsCriteria.setRoomStayCandidates(Arrays.asList(roomStayCandidate));
        utility.modifyRoomStayCandidateRequestForHomestayFunnelDetail(filter,searchRoomsCriteria);
    }

    @Test
    public void modifyRoomStayCandidateRequestForHomestayFunnelTest() {

        Filter filter = new Filter();
        filter.setFilterValue("2");
        SearchHotelsCriteria searchHotelsCriteria = new SearchHotelsCriteria();
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setAdultCount(2);
        roomStayCandidate.setRooms(2);
        roomStayCandidate.setChildAges(Arrays.asList(1));
        searchHotelsCriteria.setRoomStayCandidates(Arrays.asList(roomStayCandidate));
        utility.modifyRoomStayCandidateRequestForHomestayFunnel(filter,searchHotelsCriteria);
    }

    @Test
    public void concatenateWithSeparatorTest() {

        String concatenatedStr = utility.concatenateWithSeparator("-",Arrays.asList("a","b","c"));
        Assert.assertTrue(StringUtils.isNotEmpty(concatenatedStr));
    }

    @Test
    public void isFreeCancellationTest() {

        CancelPenalty cancelPenalty = new CancelPenalty();
        cancelPenalty.setCancellationType(CancelPenalty.CancellationType.FREE_CANCELLATON);
        boolean isFreeCancellation = utility.isFreeCancellation(Arrays.asList(cancelPenalty));
        Assert.assertTrue(isFreeCancellation);
    }

    @Test
    public void isMyPartnerExclusiveDealAllowedTest() {

        PersistedMultiRoomData persistedMultiRoomData = new PersistedMultiRoomData();
        PriceByHotelsRequestBody availreqBody = new PriceByHotelsRequestBody();
        availreqBody.setProfileType("CTA");
        availreqBody.setSubProfileType("MYPARTNER");
        persistedMultiRoomData.setAvailReqBody(availreqBody);
        persistedMultiRoomData.setExclusiveFlyerRateAvailable(true);
        persistedMultiRoomData.setExpData(new HashMap<>());
        persistedMultiRoomData.getExpData().put(MYPARTNER_EXCLUSIVE_DEAL,"true");
        boolean isMyPartnerExclusiveDealAllowed = utility.isMyPartnerExclusiveDealAllowed(persistedMultiRoomData);
        Assert.assertTrue(isMyPartnerExclusiveDealAllowed);
    }

    @Test
    public void buildSupportDetailsTest(){
        HighValueCallSupportDetails highValueCallSupportDetails = new HighValueCallSupportDetails();
        SupportDetails res = utility.buildSupportDetails(16000,"LISTING","DH", false);
        Assert.assertNull(res);
        highValueCallSupportDetails.setPages(Arrays.asList("LISTING","DETAIL","REVIEW"));
        Map<String, FunnelRange> mp = new HashMap<>();
        FunnelRange funnelRange = new FunnelRange();
        funnelRange.setMinRange(50000);
        funnelRange.setMaxRange(400000);
        mp.put("DH",funnelRange);
        funnelRange.setMinRange(100000);
        mp.put("IH",funnelRange);
        funnelRange.setMinRange(70000);
        mp.put("AA",funnelRange);
        highValueCallSupportDetails.setRange(mp);
        highValueCallSupportDetails.setOptions(Arrays.asList("form","option"));
        highValueCallSupportDetails.setFormUrl("https://www.makemytrip.com/hotels/group-booking/?checkin={0}&checkout={1}&city={2}&country={3}&locusId={4}&locusType={5}&rsc={6}&_uCurrency={7}&appVersion={8}&deviceId={9}&bookingDevice={10}&deviceType={11}&visitorId={12}&visitNumber={13}&funnelSource={14}&idContext={15}&funnelName={16}");
        Mockito.when(mobConfigPropsConsul.getSupportDetails()).thenReturn(highValueCallSupportDetails);
        res = utility.buildSupportDetails(16000,"LISTING","IH", false);
        assertNotNull(res);
        Assert.assertEquals(res.getOptions().get(0),"form");
        Assert.assertEquals(res.getOptions().get(1),"option");
        res = utility.buildSupportDetails(8000,"DETAIL","DH", false);
        Assert.assertNull(res);
    }

    @Test
    public void getTotalTicketValueTest(){
        TotalPricing totalPricing = new TotalPricing();
        List<PricingDetails> pricingDetails = new ArrayList<>();
        PricingDetails p1 = new PricingDetails(),p2 = new PricingDetails(),p3 = new PricingDetails();
        p1.setAmount(400);
        p1.setKey(TOTAL_AMOUNT_KEY);
        p2.setAmount(200);
        p2.setKey(TAXES_KEY);
        p3.setAmount(300);
        p3.setKey("RANDOM");
        pricingDetails.add(p1);
        pricingDetails.add(p2);
        pricingDetails.add(p3);
        totalPricing.setDetails(pricingDetails);
        int res = utility.getTotalTicketValue(totalPricing,true);
        Assert.assertEquals(600,res);
        res = utility.getTotalTicketValue(totalPricing,false);
        Assert.assertEquals(400,res);
    }

    @Test
    public void initTest(){
        ReflectionTestUtils.setField(utility, "consulFlag", true);
        utility.init();
    }

    @Test
    public void isMealRackRate_test() {
        Map<String, String> experimentMap = new HashMap<>();
        experimentMap.put("mealrackrate", "true");
        boolean result = utility.isMealRackRate(experimentMap);
        Assert.assertTrue(result);
    }

    @Test
    public void isRTBDayNightEnabledTest(){

        Map<String, String> expData = new HashMap<>();
        expData.put(RTB_DAY_NIGHT_EXP, "true");
        Assert.assertTrue(utility.isRTBDayNightEnabled(expData));
        expData.put(RTB_DAY_NIGHT_EXP, "false");
        Assert.assertFalse(utility.isRTBDayNightEnabled(expData));

    }
    @Test
    public void fetchAgencyUUIDFromCorptest(){
        String result = utility.fetchAgencyUUIDFromCorp("{\"agencyName\": \"Tripee Tours Pvt Ltd\", \"agencyUUID\": \"UVQHXOK1WZH\", \"agentRole\": \"admin\", \"walletEmail\": \"<EMAIL>\", \"walletMobile\": \"9739000969\", \"adminEmail\": \"<EMAIL>\", \"adminMobile\": \"9739000969\", \"agentStatus\": \"active\", \"agencyLogo\": \"https://mypartnerimgak.mmtcdn.com/JaAtTm2hFyG80ng9MV03Rd7XEpO6FKfm/whitelabel_logo.png\"}");
        assertNotNull(result);
        Assert.assertEquals("UVQHXOK1WZH",result);
        result = utility.fetchAgencyUUIDFromCorp("{\"agencyName\": \"Tripee Tours Pvt Ltd\", \"agentRole\": \"admin\", \"walletEmail\": \"<EMAIL>\", \"walletMobile\": \"9739000969\", \"adminEmail\": \"<EMAIL>\", \"adminMobile\": \"9739000969\", \"agentStatus\": \"active\", \"agencyLogo\": \"https://mypartnerimgak.mmtcdn.com/JaAtTm2hFyG80ng9MV03Rd7XEpO6FKfm/whitelabel_logo.png\"}");
        Assert.assertNull(result);
    }

    @Test
    public void convertToLakhOrCroreTest(){
        String res = Utility.convertToLakhOrCrore(100);
        Assert.assertEquals("100",res);
        res = Utility.convertToLakhOrCrore(1540000);
        Assert.assertEquals("15.4L",res);
        res = Utility.convertToLakhOrCrore(13440000);
        Assert.assertEquals("1.34Cr",res);
    }

    public void getHighValueCallFunnelTypeTest(){
        String propertyType = "Villa";
        String funnelSource = "HOTEL";
        String countryCode = "IN";
        Assert.assertEquals(CODE_AltAco,utility.getHighValueCallFunnelType(propertyType,funnelSource,countryCode));
        propertyType = "Resort";
        Assert.assertEquals(DOM_HOTEL,utility.getHighValueCallFunnelType(propertyType,funnelSource,countryCode));
        countryCode = "AE";
        Assert.assertEquals(INTEL_HOTEL,utility.getHighValueCallFunnelType(propertyType,funnelSource,countryCode));
    }

    @Test
    public void buildRoomNameCollapseDescTest() {
        AvailRoomsResponse availRoomsResponse = new AvailRoomsResponse();
        RatePlan ratePlan = new RatePlan();
        ratePlan.setCancellationPolicy(new BookedCancellationPolicy());
        ratePlan.getCancellationPolicy().setType(BookedCancellationPolicyType.NR);
        ratePlan.getCancellationPolicy().setText("Non-Refundable");

        List<BookedInclusion> inclusions = new ArrayList<>();
        BookedInclusion inclusion = new BookedInclusion();
        inclusion.setText("Free Breakfast");
        inclusions.add(inclusion);
        ratePlan.setInclusionsList(inclusions);

        List<RatePlan> ratePlanList = new ArrayList<>();
        ratePlanList.add(ratePlan);
        availRoomsResponse.setRateplanlist(ratePlanList);
        utility.buildRoomNameCollapseDesc(availRoomsResponse);
        String mealNcancelText = "Free Breakfast | Non-Refundable";
        Assert.assertEquals(availRoomsResponse.getRateplanlist().get(0).getRoomNameCollapseDesc(), mealNcancelText);
    }
    @Test
    public void buildRoomNameCollapseDescTest_MultipleMealInclusion(){
        AvailRoomsResponse availRoomsResponse = new AvailRoomsResponse();
        RatePlan ratePlan = new RatePlan();
        ratePlan.setCancellationPolicy(new BookedCancellationPolicy());
        ratePlan.getCancellationPolicy().setType(BookedCancellationPolicyType.NR);
        ratePlan.getCancellationPolicy().setText("Non-Refundable");

        List<BookedInclusion> inclusions = new ArrayList<>();
        BookedInclusion inclusion1 = new BookedInclusion();
        inclusion1.setText("Free Breakfast");
        inclusions.add(inclusion1);
        ratePlan.setInclusionsList(inclusions);

        BookedInclusion inclusion2 = new BookedInclusion();
        inclusion2.setText("Breakfast Buffet");
        inclusions.add(inclusion2);
        ratePlan.setInclusionsList(inclusions);

        List<RatePlan> ratePlanList = new ArrayList<>();
        ratePlanList.add(ratePlan);
        availRoomsResponse.setRateplanlist(ratePlanList);
        utility.buildRoomNameCollapseDesc(availRoomsResponse);
        String mealNcancelText = "Breakfast Buffet | Non-Refundable";
        Assert.assertEquals(availRoomsResponse.getRateplanlist().get(0).getRoomNameCollapseDesc(), mealNcancelText);
    }
    @Test
    public void buildRoomNameCollapseDescTest_NoCancelPolicy(){
        AvailRoomsResponse availRoomsResponse = new AvailRoomsResponse();
        RatePlan ratePlan = new RatePlan();
        ratePlan.setCancellationPolicy(new BookedCancellationPolicy());

        List<BookedInclusion> inclusions = new ArrayList<>();
        BookedInclusion inclusion1 = new BookedInclusion();
        inclusion1.setText("Free Breakfast");
        inclusions.add(inclusion1);
        ratePlan.setInclusionsList(inclusions);

        BookedInclusion inclusion2 = new BookedInclusion();
        inclusion2.setText("Breakfast Buffet");
        inclusions.add(inclusion2);
        ratePlan.setInclusionsList(inclusions);

        List<RatePlan> ratePlanList = new ArrayList<>();
        ratePlanList.add(ratePlan);
        availRoomsResponse.setRateplanlist(ratePlanList);
        utility.buildRoomNameCollapseDesc(availRoomsResponse);
        String mealNcancelText = "Breakfast Buffet";
        Assert.assertEquals(availRoomsResponse.getRateplanlist().get(0).getRoomNameCollapseDesc(), mealNcancelText);
    }
    @Test
    public void buildRoomNameCollapseDescTest_nullRequest(){
        utility.buildRoomNameCollapseDesc(null);
    }
    public void getHeaderMapTest(){
        assertNotNull(Utility.getHeaderMap(new HashMap<>()));
    }

    @Test
    public void getTextBasedUponCurrencyTest(){
        assertNotNull(Utility.getTextBasedUponCurrency("This is Price {currency_symbol} Text with {amount} ","inr",100.0));
    }

    @Test
    public void updateKeysTest(){
        Map<String,String> map = new HashMap<>();
        map.put("page__context__listing","searchHotels");
        assertNotNull(Utility.updateKeys(map));
    }

    @Test
    public void distributeAdultsTest(){
        List<String>adultDistribution = new ArrayList<>();
        ReflectionTestUtils.invokeMethod(utility,"distributeAdults",2,10,adultDistribution);
        assertNotNull(adultDistribution);
    }

    @Test
    public void divideChildrenInRoomsTest() {

        List<Integer> childAges = new ArrayList<>();
        childAges.add(5);
        childAges.add(8);
        List<RoomStayCandidate> roomStayCandidateList = new ArrayList<>();
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setAdultCount(2);
        roomStayCandidate.setRooms(2);
        roomStayCandidate.setChildAges(childAges);
        ReflectionTestUtils.invokeMethod(utility, "divideChildrenInRooms", childAges, 3, roomStayCandidateList);
    }

    @Test
    public void getAskedCurrencyTest(){
        PersistedMultiRoomData persistedMultiRoomData = new PersistedMultiRoomData();
        persistedMultiRoomData.setAvailReqBody(new PriceByHotelsRequestBody());
        persistedMultiRoomData.getAvailReqBody().setCurrency("INR");
        assertNotNull(Utility.getAskedCurrency(persistedMultiRoomData));
    }

    @Test
    public void getDotIconUrlTest(){
        Map<String,String> map = new HashMap<>();
        ReflectionTestUtils.setField(utility, "freeChildInclusionIcon", "freeChildInclusionIcon.url");
        assertNotNull(utility.getDotIconUrl(null,"iconUrl"));
        map.put(ExperimentKeys.MEAL_RACK_RATE.getKey(),TRUE);
        map.put(ExperimentKeys.MEAL_RACK_RATE.getKey(),TRUE);
        assertNotNull(utility.getDotIconUrl(map,"iconUrl"));
    }

    @Test
    public void testHasCommonElements() {

        List<String> list = Arrays.asList("a", "b", "c");
        Set<String> set = new HashSet<>(Arrays.asList("c", "d", "e"));

        boolean result = utility.hasCommonElements(list, set);

        Assert.assertTrue(result);

        list = Arrays.asList("a", "b", "c");
        set = new HashSet<>(Arrays.asList("d", "e", "f"));

        result = utility.hasCommonElements(list, set);

        Assert.assertFalse(result);

        list = Arrays.asList();
        set = new HashSet<>(Arrays.asList("d", "e", "f"));

        result = utility.hasCommonElements(list, set);

        Assert.assertFalse(result);

        list = Arrays.asList("a", "b", "c");
        set = new HashSet<>();

        result = utility.hasCommonElements(list, set);

        Assert.assertFalse(result);
    }


    @Test
    public void checkConditionReturnsTrueWhenValueExistsInConsulConfig() {
        String value = "test";
        Set<String> consulConfig = new HashSet<>();
        consulConfig.add("TEST");
        boolean result = utility.checkCondition(value, consulConfig);
        Assert.assertTrue(result);
    }

    @Test
    public void prepareBookedInclusionFromInclusionReturnsCorrectBookedInclusion() {
        BookedInclusion bookedInclusion = utility.prepareBookedInclusionFromInclusion(new Inclusion());
        assertNotNull(bookedInclusion);
    }

    @Test
    public void getUpgradeTypeReturnsRoomAndMealWhenBothUpgradesAreTrue() {
        BlackBenefits blackBenefits = new BlackBenefits();
        blackBenefits.setRoomUpgrade(true);
        blackBenefits.setMealUpgrade(true);
        String result = utility.getUpgradeType(blackBenefits);
        Assert.assertEquals(UpgradeType.ROOM_AND_MEAL.name(), result);
    }

    @Test
    public void getUpgradeTypeReturnsRoomWhenOnlyRoomUpgradeIsTrue() {
        BlackBenefits blackBenefits = new BlackBenefits();
        blackBenefits.setRoomUpgrade(true);
        blackBenefits.setMealUpgrade(false);

        String result = utility.getUpgradeType(blackBenefits);

        Assert.assertEquals(UpgradeType.ROOM.name(), result);
    }

    @Test
    public void getUpgradeTypeReturnsMealWhenOnlyMealUpgradeIsTrue() {
        BlackBenefits blackBenefits = new BlackBenefits();
        blackBenefits.setRoomUpgrade(false);
        blackBenefits.setMealUpgrade(true);

        String result = utility.getUpgradeType(blackBenefits);

        Assert.assertEquals(UpgradeType.MEAL.name(), result);
    }

    @Test
    public void getUpgradeTypeReturnsNullWhenBothUpgradesAreFalse() {
        BlackBenefits blackBenefits = new BlackBenefits();
        blackBenefits.setRoomUpgrade(false);
        blackBenefits.setMealUpgrade(false);

        String result = utility.getUpgradeType(blackBenefits);

        Assert.assertNull(result);
    }

    @Test
    public void getUpgradeTypeReturnsNullWhenBlackBenefitsIsNull() {
        String result = utility.getUpgradeType(null);

        Assert.assertNull(result);
    }


    @Test
    public void isNotGCCDayUseReturnsFalseWhenRequestIsNull() {
        FilterCountRequest request = null;

        boolean result = Utility.isNotGCCDayUse(request);

        Assert.assertFalse(result);
    }

    @Test
    public void isNotGCCDayUseReturnsFalseWhenSiteDomainIsAE() {
        FilterCountRequest request = new FilterCountRequest();
        request.setRequestDetails(new RequestDetails());
        request.getRequestDetails().setSiteDomain("AE");

        boolean result = Utility.isNotGCCDayUse(request);

        Assert.assertTrue(result);
    }

    @Test
    public void isNotGCCDayUseReturnsFalseWhenFunnelSourceIsShortStays() {
        FilterCountRequest request = new FilterCountRequest();
        request.setRequestDetails(new RequestDetails());
        request.getRequestDetails().setFunnelSource("shortstays");

        boolean result = Utility.isNotGCCDayUse(request);

        Assert.assertFalse(result);
    }

    @Test
    public void isNotGCCDayUseReturnsFalseWhenFunnelSourceIsDayUse() {
        FilterCountRequest request = new FilterCountRequest();
        request.setRequestDetails(new RequestDetails());
        request.getRequestDetails().setFunnelSource("dayuse");

        boolean result = Utility.isNotGCCDayUse(request);

        Assert.assertFalse(result);
    }

    @Test
    public void isNotGCCDayUseReturnsFalseWhenCountryCodeIsNotDom() {
        FilterCountRequest request = new FilterCountRequest();
        request.setSearchCriteria(new SearchHotelsCriteria());
        request.getSearchCriteria().setCountryCode("US");

        boolean result = Utility.isNotGCCDayUse(request);

        Assert.assertTrue(result);
    }

    @Test
    public void testBuildPartialRefundDateText() {
        String result = utility.buildPartialRefundDateText(null);
        Assert.assertEquals("", result);
        com.mmt.hotels.model.response.pricing.CancellationPolicyTimeline cancellationPolicyTimeline = new com.mmt.hotels.model.response.pricing.CancellationPolicyTimeline();
        cancellationPolicyTimeline.setEndDate("11 Jul");
        cancellationPolicyTimeline.setEndDateTime("11:59 am");

        CancellationTimeline cancellationTimeline = new CancellationTimeline();
        cancellationTimeline.setCheckInDate("11 Jul");
        cancellationTimeline.setCancellationPolicyTimelineList(Arrays.asList(cancellationPolicyTimeline));

        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.PARTIAL_REFUNDABLE_CHECKIN_TEXT)).thenReturn("Partially refundable till check-in");
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.PARTIAL_REFUNDABLE_WITH_REFUNDDATE_TEXT)).thenReturn("Partially refundable before {0} {1}");

        result = utility.buildPartialRefundDateText(cancellationTimeline);

        Assert.assertEquals("Partially refundable till check-in", result);
        cancellationTimeline.setCheckInDate("15 Jul");
        result = utility.buildPartialRefundDateText(cancellationTimeline);
        Assert.assertEquals("Partially refundable before 11 Jul 11:59 am", result);

        cancellationPolicyTimeline.setEndDateTime("");
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.PARTIAL_REFUNDABLE_TEXT)).thenReturn("Partially refundable");
        result = utility.buildPartialRefundDateText(cancellationTimeline);
        Assert.assertEquals("Partially refundable", result);

    }

    @Test
    public void buildRequestToCallBackDataReturnsCorrectDataWhenPageContextExists() {

        String pageContext = "LISTING";
        Map<String, String> pageCallBackConfig = new HashMap<>();
        pageCallBackConfig.put("infoText", "LISTING_CALLBACK_INFOTEXT");
        pageCallBackConfig.put("cta", "LISTING_CALLBACK_CTATEXT");
        pageCallBackConfig.put("bottomSheetData_heading", "LISTING_BOTTOMSHEET_CALLBACK_HEADING");
        pageCallBackConfig.put("bottomSheetData_infoText", "LISTING_BOTTOMSHEET_CALLBACK_INFOTEXT");
        pageCallBackConfig.put("bottomSheetData_cta", "LISTING_BOTTOMSHEET_CALLBACK_CTA");
        Map<String, Map<String, String>> requestToCallBackDataMap = mock(Map.class);
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("test");

        BottomSheet result = utility.buildRequestToCallBackData(pageContext, "test", "test");

        Assert.assertEquals("test", result.getInfoText());
        Assert.assertEquals("", result.getIconUrl());
        Assert.assertEquals("test", result.getCta());
    }

    @Test
    public void buildRequestToCallBackDataReturnsEmptyDataWhenPageContextDoesNotExist() {
        String pageContext = "testPageContext";

        BottomSheet result = utility.buildRequestToCallBackData(pageContext, "test", "test");

        Assert.assertNull(result.getInfoText());
        Assert.assertNull(result.getIconUrl());
        Assert.assertNull(result.getCta());
    }

    @Test
    public void buildBottomSheetDataReturnsCorrectDataWhenPageCallBackConfigExists() {
        Map<String, String> pageCallBackConfig = new HashMap<>();
        pageCallBackConfig.put("bottomSheetData_heading", "testHeading");
        pageCallBackConfig.put("bottomSheetData_infoText", "testInfoText");
        pageCallBackConfig.put("bottomSheetData_imageUrl", "testImageUrl");
        pageCallBackConfig.put("bottomSheetData_cta", "testCta");

        Mockito.when(polyglotService.getTranslatedData("testHeading")).thenReturn("translatedHeading");
        Mockito.when(polyglotService.getTranslatedData("testInfoText")).thenReturn("translatedInfoText");
        Mockito.when(polyglotService.getTranslatedData("testCta")).thenReturn("translatedCta");

        BottomSheetData result = ReflectionTestUtils.invokeMethod(utility,"buildBottomSheetData",pageCallBackConfig, "test", "test");

        Assert.assertEquals("translatedHeading", result.getHeading());
        Assert.assertEquals("translatedInfoText", result.getInfoText());
        Assert.assertEquals("testImageUrl", result.getImgUrl());
        Assert.assertEquals("translatedCta", result.getCta());
    }

    @Test
    public void buildBottomSheetDataReturnsEmptyDataWhenPageCallBackConfigDoesNotExist() {
        Map<String, String> pageCallBackConfig = new HashMap<>();
        BottomSheetData result = ReflectionTestUtils.invokeMethod(utility,"buildBottomSheetData",pageCallBackConfig, "test", "test");

        Assert.assertNull(result.getHeading());
        Assert.assertNull(result.getInfoText());
        Assert.assertNull(result.getImgUrl());
        Assert.assertNull(result.getCta());
    }

    @Test
    public void addUserServiceDataToRequestShouldUpdateGroupBookingRequest() throws ClientGatewayException {
        GroupBookingRequest groupBookingRequest = new GroupBookingRequest();
        groupBookingRequest.setRequestDetails(new RequestDetails());
        groupBookingRequest.setInputFields(new HashMap<>());
        Field field = new Field();
        field.setFieldValue(Arrays.asList("201609091139127239"));
        groupBookingRequest.getInputFields().put("hotelId", field);

        Map<String, String> httpHeaderMap = new HashMap<>();
        httpHeaderMap.put("mmt-auth", "authToken");

        UserServiceResponse userServiceResponse = new UserServiceResponse();
        UserServiceResult userServiceResult = new UserServiceResult();
        ExtendedUser extendedUser = new ExtendedUser();
        UserPersonalDetail userPersonalDetail = new UserPersonalDetail();
        UserName userName = new UserName();
        userName.setFirstName("John");
        userName.setMiddleName("Doe");
        userName.setLastName("Smith");
        userPersonalDetail.setName(userName);
        extendedUser.setPersonalDetails(userPersonalDetail);
        userServiceResult.setExtendedUser(extendedUser);
        userServiceResponse.setResult(userServiceResult);

        Mockito.when(userServiceExecutor.getUserServiceResponse(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(userServiceResponse);
        RoomDataResponse roomDataResponse = new RoomDataResponse();
        roomDataResponse.setHotelName("Test Hotel");
        Mockito.when(searchRoomsExecutor.getRoomData(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(roomDataResponse);

        utility.addUserServiceDataToRequest(groupBookingRequest, httpHeaderMap, "correlationKey", false);

        Assert.assertEquals("John Doe Smith", groupBookingRequest.getInputFields().get("fullName").getFieldValue().get(0));
        Assert.assertEquals("Test Hotel", groupBookingRequest.getInputFields().get("hotelName").getFieldValue().get(0));
    }

    @Test
    public void addUserServiceDataToRequestShouldUpdateGroupBookingRequest2() throws ClientGatewayException {
        GroupBookingRequest groupBookingRequest = new GroupBookingRequest();
        groupBookingRequest.setRequestDetails(new RequestDetails());
        groupBookingRequest.setInputFields(new HashMap<>());
        Field field = new Field();
        field.setFieldValue(Arrays.asList("201609091139127239"));
        groupBookingRequest.getInputFields().put("hotelId", field);

        Map<String, String> httpHeaderMap = new HashMap<>();
        httpHeaderMap.put("mmt-auth", "authToken");

        UserServiceResponse userServiceResponse = new UserServiceResponse();
        UserServiceResult userServiceResult = new UserServiceResult();
        ExtendedUser extendedUser = new ExtendedUser();
        UserPersonalDetail userPersonalDetail = new UserPersonalDetail();
        UserName userName = new UserName();
        userName.setFirstName("John");
        userName.setMiddleName("Doe");
        userName.setLastName("Smith");
        userPersonalDetail.setName(userName);
        extendedUser.setPersonalDetails(userPersonalDetail);
        userServiceResult.setExtendedUser(extendedUser);
        userServiceResponse.setResult(userServiceResult);

        Mockito.when(userServiceExecutor.getUserServiceResponse(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(userServiceResponse);
        Mockito.when(searchRoomsExecutor.getRoomData(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);

        utility.addUserServiceDataToRequest(groupBookingRequest, httpHeaderMap, "correlationKey", false);

        Assert.assertEquals("John Doe Smith", groupBookingRequest.getInputFields().get("fullName").getFieldValue().get(0));
        Assert.assertEquals("Test", groupBookingRequest.getInputFields().get("hotelName").getFieldValue().get(0));
    }

    @Test(expected = ClientGatewayException.class)
    public void addUserServiceDataToRequestShouldThrowExceptionWhenUserServiceResponseIsNull() throws ClientGatewayException {
        GroupBookingRequest groupBookingRequest = new GroupBookingRequest();
        groupBookingRequest.setRequestDetails(new RequestDetails());
        groupBookingRequest.setInputFields(new HashMap<>());

        Map<String, String> httpHeaderMap = new HashMap<>();
        httpHeaderMap.put("mmt-auth", "authToken");

        utility.addUserServiceDataToRequest(groupBookingRequest, httpHeaderMap, "correlationKey", false);
    }

    @Test
    public void buildFullNameFieldShouldReturnFieldWithFullName() {

        Field field = ReflectionTestUtils.invokeMethod(utility,"buildFullNameField","John Doe");

        Assert.assertEquals("fullName", field.getFieldName());
        Assert.assertEquals("John Doe", field.getFieldValue().get(0));
    }

    @Test
    public void buildHotelNameFieldShouldReturnFieldWithHotelName() {
        Field field = ReflectionTestUtils.invokeMethod(utility,"buildHotelNameField","201609091139127239");

        Assert.assertEquals("hotelName", field.getFieldName());
        //Assert.assertEquals("Test", field.getFieldValue().get(0));
    }
    @Test
    public void testGetAmendableTextForNRPolicy() {
        AmendmentPolicies amendmentPolicies = new AmendmentPolicies();
        amendmentPolicies.setName("Name");
        amendmentPolicies.setMetaData("{\"Hours\": 72, \"Days\": 0}");
        when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Test String: {amendType}");
        String result = utility.getAmendableTextForNRPolicy(amendmentPolicies, "cg/search-rooms/");
        assertNotNull(result);
        Assert.assertEquals(result.contains("Name"), true);
        Assert.assertEquals(result.contains("Date"), false);
    }

    @Test
    public void getAmendableSubtext_test() {
        String subtext = utility.getAmendableSubtext("test");
        assertNotNull(subtext);
        Assert.assertEquals(subtext.contains("test"), true);
    }

    @Test
    public void transformCancellationPolicyTest_AmendablePolicyCase(){
        when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("");
        List<CancelPenalty> cancelPenaltyList=new ArrayList<>();
        CancelPenalty cancelPenalty=new CancelPenalty();
        AmendmentPolicies amendablePolicy = new AmendmentPolicies();
        amendablePolicy.setMetaData("{\"Hours\": 72, \"Days\": 0}");
        amendablePolicy.setName("Date");
        cancelPenalty.setAmendmentPolicies(amendablePolicy);
        cancelPenalty.setCancellationType(CancelPenalty.CancellationType.FREE_CANCELLATON);
        cancelPenalty.setFreeCancellationText("Free Cancellation");
        cancelPenaltyList.add(cancelPenalty);
        assertNotNull(utility.transformCancellationPolicy(cancelPenaltyList, true, false, null, null, null, null, Optional.empty(),"", false));
        cancelPenalty.setCancellationType(null);
        assertNotNull(utility.transformCancellationPolicy(cancelPenaltyList, true, false, null, null, null, null, Optional.empty(),"", false));
        MpFareHoldStatus mpFareHoldStatus=new MpFareHoldStatus();
        mpFareHoldStatus.setExpiry(new Date().getTime());
        mpFareHoldStatus.setBookingAmount(1);
        cancelPenalty.setCancellationType(CancelPenalty.CancellationType.FREE_CANCELLATON);
        Mockito.when(polyglotService.getTranslatedData(Mockito.eq(ConstantsTranslation.CANCEL_POLICY_BNPL_SUBTEXT_NEW_VARIANT))).thenReturn(ConstantsTranslation.CANCEL_POLICY_BNPL_SUBTEXT_ZERO_VARIANT);
        BookedCancellationPolicy result=utility.transformCancellationPolicy(cancelPenaltyList,false, false, null, null, null, null, Optional.of(mpFareHoldStatus),"", false);
        assertNotNull(result);
        Assert.assertEquals(ConstantsTranslation.CANCEL_POLICY_BNPL_SUBTEXT_ZERO_VARIANT, result.getSubText());
    }

    @Test
    public void modifyStayType() {
        String res1 = ReflectionTestUtils.invokeMethod(utility,"modifyStayType", true, "Entire Apartment", 1);
        assertNotNull(res1);
        Assert.assertEquals(res1, "Entire Serviced Apartment");

        String res2 = ReflectionTestUtils.invokeMethod(utility,"modifyStayType", false, "Entire Apartment", 1);
        assertNotNull(res2);
        Assert.assertEquals(res2, "Entire Apartment");


        String res3 = ReflectionTestUtils.invokeMethod(utility,"modifyStayType", true, "Room in an Apartment", 1);
        assertNotNull(res3);
        Assert.assertEquals(res3, "Room in a Serviced Apartment");


        String res4 = ReflectionTestUtils.invokeMethod(utility,"modifyStayType", false, "Room in an Apartment", 1);
        assertNotNull(res4);
        Assert.assertEquals(res4, "Room in an Apartment");
    }


    @Test
    public void testGetPreAppliedFiltersForLocationId() {

        // Create a SearchHotelsCriteria object
        SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
        searchCriteria.setLocationId("testLocationId");
        searchCriteria.setPreAppliedFilter(true);

        // Create a LinkedHashMap for expDataMap
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put(ExperimentKeys.PRE_APPLIED_FILTERS_ENABLE.getKey(), "true");

        // Create a List of Filter objects
        List<Filter> filters = new ArrayList<>();
        Filter filter = new Filter();
        filter.setFilterGroup(FilterGroup.FREE_BREAKFAST_AVAIL);
        filter.setFilterValue("BREAKFAST_AVAIL");
        filter.setTitle("Breakfast Included");
        filters.add(filter);

        // Create a Map for preAppliedFiltersCityWise
        Map<String, List<Filter>> preAppliedFiltersCityWise = new HashMap<>();
        preAppliedFiltersCityWise.put("testLocationId", filters);

        // Set the preAppliedFiltersCityWise in utility
        ReflectionTestUtils.setField(utility, "preAppliedFiltersCityWise", preAppliedFiltersCityWise);

        // Call the method to test
        List<Filter> result = utility.getPreAppliedFiltersForLocationId(searchCriteria, expDataMap);

        // Verify the result
        assertNotNull(result);
        Assert.assertEquals(1, result.size());
        Assert.assertEquals("BREAKFAST_AVAIL", result.get(0).getFilterValue());
        Assert.assertEquals("Breakfast Included", result.get(0).getTitle());

    }

    @Test
    public void testFilterTimeBoundPreAppliedFiltersCityWise() {
        // Create a Map for preAppliedFiltersCityWise
        Map<String, List<Filter>> preAppliedFiltersCityWise = new HashMap<>();

        // Create a List of Filter objects
        List<Filter> filters = new ArrayList<>();
        Filter filter1 = new Filter();
        filter1.setFilterGroup(FilterGroup.BOOK_NOW_PAY_LATER);
        filters.add(filter1);

        Filter filter2 = new Filter();
        filter2.setFilterGroup(FilterGroup.MERGE_PROPERTY_TYPE);
        filters.add(filter2);

        preAppliedFiltersCityWise.put("testLocationId", filters);

        // Call the method to test
        Map<String, List<Filter>> result = ReflectionTestUtils.invokeMethod(utility, "filterTimeBoundPreAppliedFiltersCityWise", preAppliedFiltersCityWise);
        // Verify the result
        assertNotNull(result);
        Assert.assertEquals(1, result.get("testLocationId").size());
        Assert.assertFalse(result.get("testLocationId").get(0).getFilterGroup().getTimeBound());
    }

    @Test
    public void LoggedoutUserServiceResponseTest() {
        GroupBookingRequest input = new GroupBookingRequest();
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice("DESKTOP");
        deviceDetails.setDeviceId("DESK123");
        input.setDeviceDetails(deviceDetails);
        Map<String, Field> fields = new HashMap<>();
        Field field = new Field();
        List<String> list = new ArrayList<>();
        list.add("22319232323");
        field.setFieldValue(list);
        fields.put("contactNo", field);
        input.setInputFields(fields);
        UserServiceResponse result = ReflectionTestUtils.invokeMethod(utility, "LoggedoutUserServiceResponse", input);
        assertNotNull(result);
        Assert.assertEquals("DESK123", result.getResult().getExtendedUser().getUuid());
    }

    @Test
    public void LoggedoutUserServiceResponseTest_nullCase() {
        GroupBookingRequest input = new GroupBookingRequest();
        UserServiceResponse result = ReflectionTestUtils.invokeMethod(utility, "LoggedoutUserServiceResponse", input);
        Assert.assertNull(result);
    }

    @Test
    public void checkAppVersion_testAndroid() {
        boolean result = utility.checkAppVersion("ANDROID", "9.1.6");
        Assert.assertTrue(result);
    }

    @Test
    public void checkAppVersion_testIOS() {
        boolean result = utility.checkAppVersion("IOS", "9.1.6");
        Assert.assertTrue(result);
    }

    @Test
    public void checkAppVersion_testDesktop() {
        boolean result = utility.checkAppVersion("Desktop", "9.1.6");
        Assert.assertFalse(result);
    }

    @Test
    public void checkAppVersion_testAndroidFail() {
        boolean result = utility.checkAppVersion("ANDROID", "9.1.5");
        Assert.assertFalse(result);
    }

    @Test
    public void checkAppVersion_testIOSFail() {
        boolean result = utility.checkAppVersion("IOS", "9.1.3");
        Assert.assertFalse(result);
    }

    @Test
    public void testCheckSegments_AllowedSegmentsIsNull() {
        Set<String> businessIdentificationSegments = new HashSet<>();
        businessIdentificationSegments.add("segment1");
        boolean result =  ReflectionTestUtils.invokeMethod(utility, "checkSegments", null, businessIdentificationSegments);
        assertFalse(result);
    }

    @Test
    public void testCheckSegments_BusinessIdentificationSegmentsIsNull() {
        boolean result =  ReflectionTestUtils.invokeMethod(utility, "checkSegments", allowedSegments, null);
        assertFalse(result);
    }

    @Test
    public void testCheckSegments_AllowedSegmentsIsEmpty() {
        Set<String> businessIdentificationSegments = new HashSet<>();
        businessIdentificationSegments.add("segment1");
        Set<String> allowedSegments = new HashSet<>();
        boolean result =  ReflectionTestUtils.invokeMethod(utility, "checkSegments", allowedSegments, businessIdentificationSegments);
        assertFalse(result);
    }

    @Test
    public void testCheckSegments_BusinessIdentificationSegmentsIsEmpty() {
        Set<String> businessIdentificationSegments = new HashSet<>();
        boolean result =  ReflectionTestUtils.invokeMethod(utility, "checkSegments", allowedSegments, businessIdentificationSegments);
        assertFalse(result);
    }

    @Test
    public void testCheckSegments_SegmentNotPresentInBusinessIdentificationSegments() {
        Set<String> businessIdentificationSegments = new HashSet<>();
        businessIdentificationSegments.add("segment2");
        boolean result =  ReflectionTestUtils.invokeMethod(utility, "checkSegments", allowedSegments, businessIdentificationSegments);
        assertFalse(result);
    }

    @Test
    public void testCheckSegments_SegmentPresentInBusinessIdentificationSegments() {
        Set<String> businessIdentificationSegments = new HashSet<>();
        businessIdentificationSegments.add("segment1");
        boolean result =  ReflectionTestUtils.invokeMethod(utility, "checkSegments", allowedSegments, businessIdentificationSegments);
        assertTrue(result);
    }

    @Test
    public void testIsBusinessIdentificationApplicable_AllConditionsMet() {
        boolean result = utility.isBusinessIdentificationApplicable("affiliate1", allowedSegments);
        assertTrue(result);
    }

    @Test
    public void testIsBusinessIdentificationApplicable_AffiliateIdIsNull() {
        boolean result = utility.isBusinessIdentificationApplicable(null, allowedSegments);
        assertFalse(result);
    }

    @Test
    public void testIsBusinessIdentificationApplicable_AffiliateIdIsEmpty() {
        boolean result = utility.isBusinessIdentificationApplicable("", allowedSegments);
        assertFalse(result);
    }

    @Test
    public void testIsBusinessIdentificationApplicable_AffiliateIdNotInList() {
        boolean result = utility.isBusinessIdentificationApplicable("affiliate4", allowedSegments);
        assertFalse(result);
    }

    @Test
    public void isImageExperimentEnableTest(){
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put(IMAGES_EXP_ENABLE,"true");
        Assert.assertTrue(utility.isImageExperimentEnable(expDataMap));
        expDataMap.put(IMAGES_EXP_ENABLE,"false");
        Assert.assertFalse(utility.isImageExperimentEnable(expDataMap));
    }

    @Test
    public void testBuildRequestToCallBackDataV2() {
        // Scenario 1: Page context LISTING
        when(polyglotService.getTranslatedData("CALLBACK_INFOTEXT_V2")).thenReturn("infotext");
        when(polyglotService.getTranslatedData("CALLBACK_CTATEXT_V2")).thenReturn("cta");
        when(polyglotService.getTranslatedData("BOTTOMSHEET_CALLBACK_HEADING_V2")).thenReturn("bottomSheetHeading");
        when(polyglotService.getTranslatedData("BOTTOMSHEET_CALLBACK_FOOTERTEXT_V2")).thenReturn("bottomSheetFooter");
        BottomSheet result = utility.buildRequestToCallBackDataV2("LISTING");
        assertEquals("infotext", result.getInfoText());
        assertEquals("https://promos.makemytrip.com/GCC/MiscIcons/CTBAnimationMedium.gif", result.getIconUrl());
        assertEquals("cta", result.getCta());
        assertEquals("bottomSheetHeading", result.getBottomSheetData().getHeading());
        assertEquals("bottomSheetFooter", result.getBottomSheetData().getFooterText());

        // Scenario 2: Page context LISTING
        result = utility.buildRequestToCallBackDataV2("DETAIL");
        assertNull(result.getInfoText());
        assertEquals("cta", result.getCta());
    }

    @Test
    public void isExperimentValidTest(){
        Map<String, String> expData = new HashMap<>();
        expData.put("test", "4");
        Assert.assertTrue(utility.isExperimentValid(expData,"test",4));
        Assert.assertFalse(utility.isExperimentValid(expData,"test",3));
    }


    @Test
    public void constraintLengthForAmenityPersuasions_ShouldTrimDataList_WhenTextLengthExceedsLimit() {
        // Given
        PersuasionObject hotelPersuasion = new PersuasionObject();
        List<PersuasionData> dataList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            PersuasionData data = new PersuasionData();
            data.setText("1234567890"); // 10 characters
            dataList.add(data);
        }
        hotelPersuasion.setData(dataList);
        // When
        utility.constraintLengthForAmenityPersuasions(hotelPersuasion);
        // Then
        Assert.assertEquals(3, hotelPersuasion.getData().size());
    }
    @Test
    public void constraintLengthForAmenityPersuasions_ShouldNotTrimDataList_WhenTextLengthIsWithinLimit() {
        // Given
        PersuasionObject hotelPersuasion = new PersuasionObject();
        List<PersuasionData> dataList = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            PersuasionData data = new PersuasionData();
            data.setText("12345678"); //8 characters
            dataList.add(data);
        }
        hotelPersuasion.setData(dataList);
        // When
        utility.constraintLengthForAmenityPersuasions(hotelPersuasion);
        // Then
        Assert.assertEquals(3, hotelPersuasion.getData().size());
    }

    @Test
    public void constraintLengthForAmenityPersuasions_ShouldNotModifyDataList_WhenDataListIsNull() {
        // Given
        PersuasionObject hotelPersuasion = new PersuasionObject();
        // When
        utility.constraintLengthForAmenityPersuasions(hotelPersuasion);
        // Then
        Assert.assertNull(hotelPersuasion.getData());
    }
    @Test
    public void isFlexiCancelAddOnAvailableTest(){
        com.mmt.hotels.model.response.pricing.RatePlan ratePlan = new com.mmt.hotels.model.response.pricing.RatePlan();
        ratePlan.setFlexiCancelAddOn(new FlexiCancelAddOn());
        ratePlan.getFlexiCancelAddOn().setAddOnPolicies(new HashMap<>());
        FlexiCancelPolicies flexiCancelPolicies = new FlexiCancelPolicies();
        flexiCancelPolicies.setCancellationTimeline(new CancellationTimeline());
        flexiCancelPolicies.setInclusions(new ArrayList<>());
        ratePlan.getFlexiCancelAddOn().getAddOnPolicies().put("FLEXI_CANCEL", flexiCancelPolicies);
        Assert.assertTrue(utility.isFlexiCancelAddOnAvailable(ratePlan));
    }

    @Test
    public void isAddOnDetailsAvailableTest(){
        com.mmt.hotels.model.response.pricing.RatePlan ratePlan = new com.mmt.hotels.model.response.pricing.RatePlan();
        ratePlan.setFlexiCancelDetails(new FlexiCancelDetails());
        ratePlan.getFlexiCancelDetails().setAddOnDetails(new HashMap<>());
        ratePlan.getFlexiCancelDetails().getAddOnDetails().put("FLEXI_CANCEL", new FlexiCancelAddOnDetails());
        Assert.assertTrue(utility.isAddOnDetailsAvailable(ratePlan));
    }

    @Test
    public void getSelectedTest(){
        com.mmt.hotels.model.response.pricing.RatePlan ratePlan = new com.mmt.hotels.model.response.pricing.RatePlan();
        Selected selected = new Selected();
        selected.setSubTitle("test");
        utility.getSelected(selected);
    }

    @Test
    public void convertNumericValueToCommaSeparatedString_test() {
        String number = utility.convertNumericValueToCommaSeparatedString(12312, Locale.ENGLISH);
        assertNotNull(number);
        Assert.assertTrue(number.contains(","));
    }
    @Test
    public void addIsUgcV2ToURL_whenUrlIsValidAndIsUgcV2IsTrue_shouldAppendIsUgcV2True() {
        String result = Utility.addisUgcV2ToURL("http://example.com", true);
    }

    @Test
    public void addIsUgcV2ToURL_whenUrlIsValidAndIsUgcV2IsFalse_shouldAppendIsUgcV2False() {
        String result = Utility.addisUgcV2ToURL("http://example.com", false);
    }

    @Test
    public void addIsUgcV2ToURL_whenUrlIsBlank_shouldReturnBlankUrl() {
        String result = Utility.addisUgcV2ToURL("", true);
        Assert.assertEquals("", result);
    }

    @Test
    public void addIsUgcV2ToURL_whenUrlIsNull_shouldReturnNull() {
        String result = Utility.addisUgcV2ToURL(null, true);
        Assert.assertNull(result);
    }

    @Test
    public void addIsUgcV2ToURL_whenUrlContainsQueryParameters_shouldAppendIsUgcV2Correctly() {
        String result = Utility.addisUgcV2ToURL("http://example.com?param1=value1", true);
    }

    @Test
    public void addIsUgcV2ToURL_whenUrlContainsAnchor_shouldAppendIsUgcV2BeforeAnchor() {
        String result = Utility.addisUgcV2ToURL("http://example.com#anchor", true);
    }

    @Test
    public void buildLocationDetail_withValidData_returnsCorrectLocationDetail() {
        LocationDetail result = utility.buildLocationDetail("1", "Paris", "City", "FR", "France");
        assertNotNull(result);
        Assert.assertEquals("1", result.getId());
        Assert.assertEquals("Paris", result.getName());
        Assert.assertEquals("City", result.getType());
        Assert.assertEquals("FR", result.getCountryId());
        Assert.assertEquals("France", result.getCountryName());
    }

    @Test
    public void buildLocationDetail_withEmptyStrings_returnsLocationDetailWithEmptyFields() {
        LocationDetail result = utility.buildLocationDetail("", "", "", "", "");
        assertNotNull(result);
        Assert.assertEquals("", result.getId());
        Assert.assertEquals("", result.getName());
        Assert.assertEquals("", result.getType());
        Assert.assertEquals("", result.getCountryId());
        Assert.assertEquals("", result.getCountryName());
    }

    @Test
    public void buildLocationDetail_withNullValues_returnsLocationDetailWithNullFields() {
        LocationDetail result = utility.buildLocationDetail(null, null, null, null, null);
        Assert.assertNull(result.getId());
        Assert.assertNull(result.getName());
        Assert.assertNull(result.getType());
        Assert.assertNull(result.getCountryId());
        Assert.assertNull(result.getCountryName());
    }

    @Test
    public void appendQueryParamsInUrl_withValidParams_shouldAppendParams() {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("key1", "value1");
        queryParams.put("key2", "value2");
        String url = "http://example.com";
        String expected = "http://example.com?key1=value1&key2=value2";
        String result = Utility.appendQueryParamsInUrl(url, queryParams);
        Assert.assertEquals(expected, result);
    }

    @Test
    public void appendQueryParamsInUrl_withEmptyParams_shouldReturnOriginalUrl() {
        Map<String, String> queryParams = new HashMap<>();
        String url = "http://example.com";
        String expected = "http://example.com";
        String result = Utility.appendQueryParamsInUrl(url, queryParams);
        Assert.assertEquals(expected, result);
    }

    @Test
    public void appendQueryParamsInUrl_withNullParams_shouldReturnOriginalUrl() {
        String url = "http://example.com";
        String expected = "http://example.com";
        String result = Utility.appendQueryParamsInUrl(url, null);
        Assert.assertEquals(expected, result);
    }

    @Test
    public void appendQueryParamsInUrl_withBlankParamKey_shouldSkipBlankParam() {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("", "value1");
        queryParams.put("key2", "value2");
        String url = "http://example.com";
        String expected = "http://example.com?key2=value2";
        String result = Utility.appendQueryParamsInUrl(url, queryParams);
        Assert.assertEquals(expected, result);
    }

    @Test
    public void appendQueryParamsInUrl_withBlankParamValue_shouldSkipBlankValue() {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("key1", "");
        queryParams.put("key2", "value2");
        String url = "http://example.com";
        String expected = "http://example.com?key2=value2";
        String result = Utility.appendQueryParamsInUrl(url, queryParams);
        Assert.assertEquals(expected, result);
    }

    @Test
    public void appendQueryParamsInUrl_withExistingParams_shouldReplaceExistingParams() {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("key1", "newValue1");
        String url = "http://example.com?key1=value1&key2=value2";
        String expected = "http://example.com?key1=newValue1&key2=value2";
        String result = Utility.appendQueryParamsInUrl(url, queryParams);
    }

    @Test
    public void appendQueryParamsInUrl_withUrlHavingFragment_shouldAppendParamsBeforeFragment() {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("key1", "value1");
        String url = "http://example.com#section";
        String expected = "http://example.com?key1=value1#section";
        String result = Utility.appendQueryParamsInUrl(url, queryParams);
        Assert.assertEquals(expected, result);
    }

    @Test
    public void updatePayAtHotelText_whenPahOnlyPaymodeAndRegionAE_shouldRemoveCurrency() {
        // Setup
        String payMode = "PAH_WITH_CC";
        String pahText = "Pay {currency} at Hotel";
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");

        // Execute
        Utility.updatePayAtHotelText(totalPricingMock, payMode, pahText, "IN");

        // Verify - AE is GCC region, so currency should be removed
        ArgumentCaptor<String> captor = ArgumentCaptor.forClass(String.class);
        verify(totalPricingMock).setPayAtHotelText(captor.capture());
        String actualText = captor.getValue();
        assertEquals("Pay  at Hotel", actualText);
        
        // Cleanup
        MDC.clear();
    }

    @Test
    public void updatePayAtHotelText_whenPahOnlyPaymodeAndRegionIN_shouldNotUpdateText() {
        // Setup
        String payMode = "PAH_WITH_CC";
        String pahText = "Pay {currency} at Hotel";
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "IN");

        // Execute
        Utility.updatePayAtHotelText(totalPricingMock, payMode, pahText, "IN");

        // Verify - IN region with IN countryCode should not update text
        verify(totalPricingMock, never()).setPayAtHotelText(anyString());
        
        // Cleanup
        MDC.clear();
    }

    @Test
    public void updatePayAtHotelText_whenNotPahOnlyPaymode_shouldNotUpdateText() {
        // Setup
        String payMode = "ONLINE";
        String pahText = "Pay {currency} at Hotel";
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");

        // Execute
        Utility.updatePayAtHotelText(totalPricingMock, payMode, pahText, "IN");

        // Verify - Non PAH_ONLY paymode should not update text
        verify(totalPricingMock, never()).setPayAtHotelText(anyString());
        
        // Cleanup
        MDC.clear();
    }

    @Test
    public void updatePayAtHotelText_whenPahOnlyPaymodeAndRegionIsNull_shouldNotUpdateText() {
        // Setup
        String payMode = "PAH_WITH_CC";
        String pahText = "Pay {currency} at Hotel";
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), null);

        // Execute
        Utility.updatePayAtHotelText(totalPricingMock, payMode, pahText, "IN");

        // Verify - Null region with IN countryCode should not update text
        verify(totalPricingMock, never()).setPayAtHotelText(anyString());
        
        // Cleanup
        MDC.clear();
    }

    @Test
    public void updatePayAtHotelText_whenPahOnlyPaymodeAndRegionKSA_shouldRemoveCurrency() {
        // Setup
        String payMode = "PAH_WITH_CC";
        String pahText = "Pay {currency} at Hotel";
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "SA");

        // Execute
        Utility.updatePayAtHotelText(totalPricingMock, payMode, pahText, "IN");

        // Verify - SA is KSA region, so currency should be removed
        ArgumentCaptor<String> captor = ArgumentCaptor.forClass(String.class);
        verify(totalPricingMock).setPayAtHotelText(captor.capture());
        String actualText = captor.getValue();
        assertEquals("Pay  at Hotel", actualText);
        
        // Cleanup
        MDC.clear();
    }

    @Test
    public void updatePayAtHotelText_whenPahOnlyPaymodeAndNonDomCountry_shouldRemoveCurrency() {
        // Setup
        String payMode = "PAH_WITH_CC";
        String pahText = "Pay {currency} at Hotel";
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "US");

        // Execute
        Utility.updatePayAtHotelText(totalPricingMock, payMode, pahText, "US");

        // Verify - Non-DOM country should remove currency
        ArgumentCaptor<String> captor = ArgumentCaptor.forClass(String.class);
        verify(totalPricingMock).setPayAtHotelText(captor.capture());
        String actualText = captor.getValue();
        assertEquals("Pay  at Hotel", actualText);
        
        // Cleanup
        MDC.clear();
    }

    @Test
    public void updatePayAtHotelText_whenPahOnlyPaymodeAndGccRegionWithNonDomCountry_shouldRemoveCurrency() {
        // Setup
        String payMode = "PAH_WITH_CC";
        String pahText = "Pay {currency} at Hotel";
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");

        // Execute
        Utility.updatePayAtHotelText(totalPricingMock, payMode, pahText, "AE");

        // Verify - GCC region with non-DOM country should remove currency
        ArgumentCaptor<String> captor = ArgumentCaptor.forClass(String.class);
        verify(totalPricingMock).setPayAtHotelText(captor.capture());
        String actualText = captor.getValue();
        assertEquals("Pay  at Hotel", actualText);
        
        // Cleanup
        MDC.clear();
    }

    @Test
    public void showHighlightsForRoomAmenitiesTest() {
        boolean result = false;
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "IN");
        result = utility.showHighlightsForRoomAmenities("ABC", "HOTELS");
        Assert.assertTrue(result);

        result = utility.showHighlightsForRoomAmenities("ABC", "Test");
        Assert.assertFalse(result);

        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");
        result = utility.showHighlightsForRoomAmenities("ABC", "HOTELS");
        Assert.assertFalse(result);

        result = utility.showHighlightsForRoomAmenities("IN", "HOTELS");
        Assert.assertFalse(result);
        MDC.clear();
    }
    @Test
    public void testGetMMTAuth_AndroidWithoutBackupAuth() {
        Map<String, String> httpHeaderMap = new HashMap<>();
        httpHeaderMap.put("backup_auth", "someValue;");
        String bookingDevice = "ANDROID";

        String result = utility.getMMTAuth(httpHeaderMap, bookingDevice);

        assertNull(result);
    }

    @Test
    public void testGetMMTAuth_WithMMTAuthHeader() {
        Map<String, String> httpHeaderMap = new HashMap<>();
        httpHeaderMap.put("mmt-auth", "67890");
        String bookingDevice = "OTHER_DEVICE"; // Not ANDROID

        String result = utility.getMMTAuth(httpHeaderMap, bookingDevice);

        assertEquals("67890", result);
    }

    @Test
    public void testGetMMTAuth_EmptyHeaderMap() {
        Map<String, String> httpHeaderMap = new HashMap<>();
        String bookingDevice = "ANDROID";

        String result = utility.getMMTAuth(httpHeaderMap, bookingDevice);

        assertNull(result);
    }

    @Test
    public void testGetMMTAuth_BlankBackupAuth() {
        Map<String, String> httpHeaderMap = new HashMap<>();
        httpHeaderMap.put("backup_auth", "");
        httpHeaderMap.put("mmt-auth", "67890");
        String bookingDevice = "ANDROID";

        String result = utility.getMMTAuth(httpHeaderMap, bookingDevice);

        assertEquals("67890", result);
    }

    @Test
    public void testBuildRequestToCallBackDataForB2C() {
        // Scenario 1: Page context LISTING
        when(polyglotService.getTranslatedData("CALLBACK_INFOTEXT_V2")).thenReturn("infotext");
        when(polyglotService.getTranslatedData("CALLBACK_CTATEXT_V2")).thenReturn("cta");
        when(polyglotService.getTranslatedData("BOTTOMSHEET_CALLBACK_HEADING_V2")).thenReturn("bottomSheetHeading");
        when(polyglotService.getTranslatedData("BOTTOMSHEET_CALLBACK_FOOTERTEXT_V2")).thenReturn("bottomSheetFooter");
        BottomSheet result = utility.buildRequestToCallBackDataV2("LISTING");
//        assertEquals("infotext", result.getInfoText());
        assertEquals("https://promos.makemytrip.com/GCC/MiscIcons/CTBAnimationMedium.gif", result.getIconUrl());
        assertEquals("cta", result.getCta());
        assertEquals("bottomSheetHeading", result.getBottomSheetData().getHeading());
        assertEquals("bottomSheetFooter", result.getBottomSheetData().getFooterText());

        // Scenario 2: Page context LISTING
        result = utility.buildRequestToCallBackDataForB2C("DETAIL");
        assertNull(result.getInfoText());
        assertEquals("cta", result.getCta());
    }


    @Test
    public void testBuildRequestToCallBackDataForHighValue() {
        // Scenario 1: Page context LISTING
        when(polyglotService.getTranslatedData("CALLBACK_INFOTEXT_V2")).thenReturn("infotext");
        when(polyglotService.getTranslatedData("CALLBACK_CTATEXT_V2")).thenReturn("cta");
        when(polyglotService.getTranslatedData("BOTTOMSHEET_CALLBACK_HEADING_V2")).thenReturn("bottomSheetHeading");
        when(polyglotService.getTranslatedData("BOTTOMSHEET_CALLBACK_FOOTERTEXT_V2")).thenReturn("bottomSheetFooter");
        BottomSheet result = utility.buildRequestToCallBackDataForHighValue("LISTING", "test", "test", true);
        assertEquals("infotext", result.getInfoText());
        assertEquals("https://promos.makemytrip.com/GCC/MiscIcons/CTBAnimationMedium.gif", result.getIconUrl());
        assertEquals("cta", result.getCta());
        assertEquals("bottomSheetHeading", result.getBottomSheetData().getHeading());
        assertEquals("bottomSheetFooter", result.getBottomSheetData().getFooterText());

        // Scenario 2: Page context LISTING
        result = utility.buildRequestToCallBackDataForB2C("DETAIL");
        assertNull(result.getInfoText());
        assertEquals("cta", result.getCta());
    }

    @Test
    public void buildIhCashbackCardReturnsEmptyResponseWhenNoCashback() {
        HotelRates hotelRates = new HotelRates();
        hotelRates.setLowestRate(new LowestRateAPResp());
        hotelRates.setMmtIhCabCashback(1500);
        hotelRates.setCabPrice(200);
        hotelRates.setCabDetailDeepLink("https://www.makemytrip.com/hotels/hotel-cab-booking/?requestId=request123&platform=desktop&type=cabDetail");
        hotelRates.setExclusiveFlyerRateAvailable(true);
        hotelRates.setCurrencyCode("INR");
        ListPersonalizationResponse listPersonalizationResponse = utility.buildIhCashbackCard(hotelRates, new HashSet<>(),1);
        assertNotNull(listPersonalizationResponse.getCardData());
        hotelRates.setMmtIhCabCashback(0);
        hotelRates.setMmtIhForexCashback(1500);
        hotelRates.setCabDetailDeepLink(null);
        listPersonalizationResponse = utility.buildIhCashbackCard(hotelRates,new HashSet<>(),2);
        assertNotNull(listPersonalizationResponse.getCardData());
    }

    @Test
    public void getSpaceDataV2_ReturnsNull_WhenHesSpaceDataIsNull() {
        SpaceData result = utility.getSpaceDataV2(null, false, new HashSet<>());
        assertNull(result);
    }

    @Test
    public void getSpaceDataV2_ReturnsCorrectData_WhenHesSpaceDataIsNotNull() {
        com.mmt.hotels.model.response.staticdata.SpaceData hesSpaceData = new com.mmt.hotels.model.response.staticdata.SpaceData();
        hesSpaceData.setDescriptive(Arrays.asList("Descriptive Text"));
        hesSpaceData.setSharedInfo(new com.mmt.hotels.model.response.staticdata.SharedInfo());
        com.mmt.hotels.model.response.staticdata.Space hesSpace = new com.mmt.hotels.model.response.staticdata.Space();
        hesSpace.setName("Space Name");
        hesSpace.setSpaceId("Space ID");
        hesSpace.setSpaceType("BEDROOM");
        hesSpace.setAreaText("Area Text");
        hesSpace.setDescriptionText("Description Text");
        hesSpace.setFinalOccupancy(2);
        hesSpace.setBaseOccupancy(1);
        hesSpace.setSubText("Sub Text");
        hesSpace.setMedia(Collections.singletonList(new com.mmt.hotels.model.response.staticdata.MediaData()));
        hesSpaceData.setSpaces(Collections.singletonList(hesSpace));

        when(polyglotService.getTranslatedData(ConstantsTranslation.SPACE_OCCUPANCY_TEXT)).thenReturn("Occupancy Text");
//        when(polyglotService.getTranslatedData(ConstantsTranslation.SPACE_SINGLE_OCCUPANCY_TEXT)).thenReturn("Single Occupancy Text");

        SpaceData result = utility.getSpaceDataV2(hesSpaceData, false, new HashSet<>());

        assertNotNull(result);
        assertNotNull(result.getSharedInfo());
        assertEquals(1, result.getSpaces().size());
        Space space = result.getSpaces().get(0);
        assertEquals("Space Name", space.getName());
        assertEquals("Space ID", space.getSpaceId());
        assertEquals("BEDROOM", space.getSpaceType());
        assertEquals("Area Text", space.getAreaText());
        assertEquals("Description Text", space.getDescriptionText());
        assertEquals("Occupancy Text", space.getSubText());
        assertNotNull(space.getMedia());
    }

    @Test
    public void getSpaceDataV2_SetsSubTextToNull_WhenOccupancyIsZero() {
        com.mmt.hotels.model.response.staticdata.SpaceData hesSpaceData = new com.mmt.hotels.model.response.staticdata.SpaceData();
        com.mmt.hotels.model.response.staticdata.Space hesSpace = new com.mmt.hotels.model.response.staticdata.Space();
        hesSpace.setSpaceType("BEDROOM");
        hesSpace.setFinalOccupancy(0);
        hesSpace.setBaseOccupancy(0);
        hesSpaceData.setSpaces(Collections.singletonList(hesSpace));

        SpaceData result = utility.getSpaceDataV2(hesSpaceData, false, new HashSet<>());

        assertNotNull(result);
        assertEquals(1, result.getSpaces().size());
        Space space = result.getSpaces().get(0);
        assertNull(space.getSubText());
    }

    @Test
    public void getSpaceDataV2_SetsSubTextToSingleOccupancyText_WhenOccupancyIsOne() {
        com.mmt.hotels.model.response.staticdata.SpaceData hesSpaceData = new com.mmt.hotels.model.response.staticdata.SpaceData();
        com.mmt.hotels.model.response.staticdata.Space hesSpace = new com.mmt.hotels.model.response.staticdata.Space();
        hesSpace.setSpaceType("BEDROOM");
        hesSpace.setFinalOccupancy(1);
        hesSpace.setBaseOccupancy(1);
        hesSpaceData.setSpaces(Collections.singletonList(hesSpace));

        when(polyglotService.getTranslatedData(ConstantsTranslation.SPACE_SINGLE_OCCUPANCY_TEXT)).thenReturn("Single Occupancy Text");

        SpaceData result = utility.getSpaceDataV2(hesSpaceData, false, new HashSet<>());

        assertNotNull(result);
        assertEquals(1, result.getSpaces().size());
        Space space = result.getSpaces().get(0);
        assertEquals("Single Occupancy Text", space.getSubText());
    }

    @Test
    public void getSpaceDataV2_SetsSubTextToOccupancyText_WhenOccupancyIsGreaterThanOne() {
        com.mmt.hotels.model.response.staticdata.SpaceData hesSpaceData = new com.mmt.hotels.model.response.staticdata.SpaceData();
        com.mmt.hotels.model.response.staticdata.Space hesSpace = new com.mmt.hotels.model.response.staticdata.Space();
        hesSpace.setSpaceType("BEDROOM");
        hesSpace.setFinalOccupancy(2);
        hesSpace.setBaseOccupancy(1);
        hesSpaceData.setSpaces(Collections.singletonList(hesSpace));

        when(polyglotService.getTranslatedData(ConstantsTranslation.SPACE_OCCUPANCY_TEXT)).thenReturn("Occupancy Text");

        SpaceData result = utility.getSpaceDataV2(hesSpaceData, false, new HashSet<>());

        assertNotNull(result);
        assertEquals(1, result.getSpaces().size());
        Space space = result.getSpaces().get(0);
        assertEquals("Occupancy Text", space.getSubText());
    }

    @Test
    public void getSpaceDataV2_SetsSubTextToAreaText_WhenIsPrivateSpaceIsTrue() {
        com.mmt.hotels.model.response.staticdata.SpaceData hesSpaceData = new com.mmt.hotels.model.response.staticdata.SpaceData();
        com.mmt.hotels.model.response.staticdata.Space hesSpace = new com.mmt.hotels.model.response.staticdata.Space();
        hesSpace.setAreaText("Area Text");
        hesSpaceData.setSpaces(Collections.singletonList(hesSpace));

        SpaceData result = utility.getSpaceDataV2(hesSpaceData, true, new HashSet<>());

        assertNotNull(result);
        assertEquals(1, result.getSpaces().size());
        Space space = result.getSpaces().get(0);
//        assertEquals("Area Text", space.getSubText());
        assertNull(space.getAreaText());
    }

//    @Test
//    public void buildSpaceInclusion_ReturnsNull_WhenHesSpaceIsNull() {
//        List<String> result = utility.buildSpaceInclusion(null);
//        assertNull(result);
//    }

    @Test
    public void buildSpaceInclusion_ReturnsEmptyList_WhenHesSpaceHasNoDetails() {
        com.mmt.hotels.model.response.staticdata.Space hesSpace = new com.mmt.hotels.model.response.staticdata.Space();
        List<String> result = ReflectionTestUtils.invokeMethod(utility, "buildSpaceInclusion", hesSpace);
        assertNull(result);
    }

    @Test
    public void buildSpaceInclusion_ReturnsBedInfo_WhenHesSpaceHasBedInfo() {
        com.mmt.hotels.model.response.staticdata.Space hesSpace = new com.mmt.hotels.model.response.staticdata.Space();
        com.mmt.hotels.model.response.staticdata.SleepingDetails sleepingDetails = new com.mmt.hotels.model.response.staticdata.SleepingDetails();
        com.mmt.hotels.model.response.staticdata.SleepingBedInfo bedInfo = new com.mmt.hotels.model.response.staticdata.SleepingBedInfo();
        bedInfo.setBedType("King Bed");
        sleepingDetails.setBedInfo(Collections.singletonList(bedInfo));
        hesSpace.setSleepingDetails(sleepingDetails);

        List<String> result = ReflectionTestUtils.invokeMethod(utility, "buildSpaceInclusion", hesSpace);
        assertEquals(1, result.size());
        assertEquals("King Bed", result.get(0));
    }

    @Test
    public void buildSpaceInclusion_ReturnsExtraBedInfo_WhenHesSpaceHasExtraBedInfo() {
        com.mmt.hotels.model.response.staticdata.Space hesSpace = new com.mmt.hotels.model.response.staticdata.Space();
        com.mmt.hotels.model.response.staticdata.SleepingDetails sleepingDetails = new com.mmt.hotels.model.response.staticdata.SleepingDetails();
        com.mmt.hotels.model.response.staticdata.SleepingBedInfo extraBedInfo = new com.mmt.hotels.model.response.staticdata.SleepingBedInfo();
        extraBedInfo.setBedType("Sofa Bed");
        sleepingDetails.setExtraBedInfo(Collections.singletonList(extraBedInfo));
        hesSpace.setSleepingDetails(sleepingDetails);

        List<String> result = ReflectionTestUtils.invokeMethod(utility, "buildSpaceInclusion", hesSpace);
        assertEquals(1, result.size());
    }

    @Test
    public void buildSpaceInclusion_ReturnsDescriptionText_WhenHesSpaceHasDescriptionText() {
        com.mmt.hotels.model.response.staticdata.Space hesSpace = new com.mmt.hotels.model.response.staticdata.Space();
        hesSpace.setDescriptionText("Spacious room with a view");

        List<String> result = ReflectionTestUtils.invokeMethod(utility, "buildSpaceInclusion", hesSpace);
        assertEquals(1, result.size());
        assertEquals("Spacious room with a view", result.get(0));
    }

    @Test
    public void buildSpaceInclusion_ReturnsCombinedInfo_WhenHesSpaceHasAllDetails() {
        com.mmt.hotels.model.response.staticdata.Space hesSpace = new com.mmt.hotels.model.response.staticdata.Space();
        com.mmt.hotels.model.response.staticdata.SleepingDetails sleepingDetails = new com.mmt.hotels.model.response.staticdata.SleepingDetails();
        com.mmt.hotels.model.response.staticdata.SleepingBedInfo bedInfo = new com.mmt.hotels.model.response.staticdata.SleepingBedInfo();
        bedInfo.setBedType("King Bed");
        com.mmt.hotels.model.response.staticdata.SleepingBedInfo extraBedInfo = new com.mmt.hotels.model.response.staticdata.SleepingBedInfo();
        extraBedInfo.setBedType("Sofa Bed");
        sleepingDetails.setBedInfo(Collections.singletonList(bedInfo));
        sleepingDetails.setExtraBedInfo(Collections.singletonList(extraBedInfo));
        hesSpace.setSleepingDetails(sleepingDetails);
        hesSpace.setDescriptionText("Spacious room with a view");

        List<String> result = ReflectionTestUtils.invokeMethod(utility, "buildSpaceInclusion", hesSpace);
        assertEquals(2, result.size());
        assertEquals("King Bed, Extra Sofa Bed available|Spacious room with a view", String.join("|", result));
    }


    @Test
    public void testDeepCopyCardData() {
        CardData original = new CardData();
        original.setSequence(1);
        CardInfo originalCardInfo = new CardInfo();
        originalCardInfo.setIndex(2);
        originalCardInfo.setSubType("subType");
        originalCardInfo.setId("id");
        originalCardInfo.setTitleText("titleText");
        originalCardInfo.setSubText("subText");
        originalCardInfo.setIconURL("iconURL");

        List<com.mmt.hotels.clientgateway.response.moblanding.CardAction> cardActions = new ArrayList<>();
        com.mmt.hotels.clientgateway.response.moblanding.CardAction cardAction1 = new com.mmt.hotels.clientgateway.response.moblanding.CardAction();
        cardActions.add(cardAction1);
        originalCardInfo.setCardAction(cardActions);
        original.setCardInfo(originalCardInfo);
        CardData copy = utility.deepCopyCardData(original);

        Assert.assertNotNull(copy);
    }
    @Test
    public void buildMultiCurrencyInfoRequest_test() {
        com.mmt.hotels.clientgateway.request.MultiCurrencyInfo multiCurrencyInfoCG = new MultiCurrencyInfo();
        multiCurrencyInfoCG.setUserCurrency("INR");
        multiCurrencyInfoCG.setRegionCurrency("USD");
        com.mmt.hotels.model.request.MultiCurrencyInfo result = utility.buildMultiCurrencyInfoRequest(multiCurrencyInfoCG);
        assertEquals(result.getUserCurrency(), "INR");
        assertEquals(result.getRegionCurrency(), "USD");
    }


    @Test
    public void testBuildLosDiscountInclusion() {
        // Arrange
        Inclusion losInclusion = new Inclusion();
        losInclusion.setCode("los inclusion");
        losInclusion.setCategory("los");
        losInclusion.setInclusionType("testType");

        // Act
        BookedInclusion result = utility.buildLosDiscountInclusion(losInclusion);

        // Assert
        assertNotNull(result);
        assertEquals("los inclusion", result.getCode());
        assertEquals("los inclusion", result.getText());

        // When argument is null
        losInclusion = null;
        assertNull(utility.buildLosDiscountInclusion(losInclusion));
    }

    @Test
    public void testGetTotalAmount_NullDetailsList() {

        // Null totalPricing should return null
        assertNull(utility.getTotalAmount(null));
        // If the details list is null, it should return null
        TotalPricing totalPricing = new TotalPricing();
        assertNull(utility.getTotalAmount(totalPricing));
    }

    @Test
    public void testGetTotalAmount_NoTotalAmountKey() {
        // If "TOTAL_AMOUNT" is not in the details list, it should return null
        PricingDetails pricingDetail = new PricingDetails();
        pricingDetail.setKey("OTHER_KEY");
        pricingDetail.setAmount(100.0);

        TotalPricing totalPricing = new TotalPricing();
        totalPricing.setDetails(Arrays.asList(pricingDetail));

        assertNull(utility.getTotalAmount(totalPricing));
    }

    @Test
    public void testGetTotalAmount_ValidTotalAmountKey() {
        // Valid case where "TOTAL_AMOUNT" exists in the details list
        PricingDetails pricingDetail1 = new PricingDetails();
        pricingDetail1.setKey("OTHER_KEY");
        pricingDetail1.setAmount(100.0);

        PricingDetails pricingDetail2 = new PricingDetails();
        pricingDetail2.setKey("TOTAL_AMOUNT");
        pricingDetail2.setAmount(500.0);

        TotalPricing totalPricing = new TotalPricing();
        totalPricing.setDetails(Arrays.asList(pricingDetail1, pricingDetail2));

        assertEquals(Double.valueOf(500.0), utility.getTotalAmount(totalPricing));
    }


    @Test
    public void testBuildHomestayPersuasion_NotAltAccoOrEmptySbpp() {
        // Not AltAcco or sbpp is empty -> Should return an empty map
        Map<String, Persuasion> result = utility.buildHomestayPersuasion(false, "", 2, 4, 1000.0, 1, HINDI_RUPEE);
        assertTrue(result.isEmpty());
    }

    @Test
    public void buildIntlFlyerIconTag_ReturnsCorrectIconTag() {
        // Arrange
        when(polyglotService.getTranslatedData(FLYER_EXCLUSIVE_DEAL_TEXT)).thenReturn("Exclusive Deal");

        // Act
        IconTag result = utility.buildIntlFlyerIconTag();

        // Assert
        assertNotNull(result);
        assertEquals("Exclusive Deal", result.getText());
        assertNotNull(result.getBgGradient());
        assertEquals(FLYER_EXCLUSIVE_ICON_COLOR, result.getBgGradient().getStart());
        assertEquals(FLYER_EXCLUSIVE_ICON_COLOR, result.getBgGradient().getEnd());
        assertEquals(FLYER_EXCLUSIVE_ICON_BORDER_COLOR, result.getBorderColor());
    }

    @Test
    public void buildIntlFlyerIconTag_ReturnsNullTextWhenTranslationFails() {
        // Arrange
        when(polyglotService.getTranslatedData(FLYER_EXCLUSIVE_DEAL_TEXT)).thenReturn(null);

        // Act
        IconTag result = utility.buildIntlFlyerIconTag();

        // Assert
        assertNotNull(result);
        assertNull(result.getText());
        assertNotNull(result.getBgGradient());
        assertEquals(FLYER_EXCLUSIVE_ICON_COLOR, result.getBgGradient().getStart());
        assertEquals(FLYER_EXCLUSIVE_ICON_COLOR, result.getBgGradient().getEnd());
        assertEquals(FLYER_EXCLUSIVE_ICON_BORDER_COLOR, result.getBorderColor());
    }

    @Test
    public void buildHomestayPersuasion_ReturnsEmptyMap_WhenIsAltAccoIsFalse() {
        Map<String, Persuasion> result = utility.buildHomestayPersuasion(false, "1", 2, 4, 1000.0, 2, "USD");
        assertTrue(result.isEmpty());
    }

    @Test
    public void buildHomestayPersuasion_ReturnsEmptyMap_WhenSbppIsEmpty() {
        Map<String, Persuasion> result = utility.buildHomestayPersuasion(true, "", 2, 4, 1000.0, 2, "USD");
        assertTrue(result.isEmpty());
    }

    @Test
    public void buildHomestayPersuasion_ReturnsCorrectMap_WhenSbppIsOneAndRoomCountAndNoOfNightStaysGreaterThanOne() {
        when(persuasionUtil.buildHomestayPersuasionForReviewPage(anyString(), eq("1"), eq("USD"))).thenReturn(new HashMap<>());
        Map<String, Persuasion> result = utility.buildHomestayPersuasion(true, "1", 2, 4, 1000.0, 2, "USD");
        assertNotNull(result);
        verify(persuasionUtil).buildHomestayPersuasionForReviewPage(anyString(), eq("1"), eq("USD"));
    }

    @Test
    public void buildHomestayPersuasion_ReturnsEmptyMap_WhenSbppIsOneAndRoomCountAndNoOfNightStaysNotGreaterThanOne() {
        Map<String, Persuasion> result = utility.buildHomestayPersuasion(true, "1", 1, 4, 1000.0, 1, "USD");
        assertTrue(result.isEmpty());
    }

    @Test
    public void buildHomestayPersuasion_ReturnsCorrectMap_WhenSbppIsTwoAndTotalAdultsGreaterThanOne() {
        when(persuasionUtil.buildHomestayPersuasionForReviewPage(anyString(), eq("2"), eq("USD"))).thenReturn(new HashMap<>());
        Map<String, Persuasion> result = utility.buildHomestayPersuasion(true, "2", 2, 4, 1000.0, 2, "USD");
        assertNotNull(result);
        verify(persuasionUtil).buildHomestayPersuasionForReviewPage(anyString(), eq("2"), eq("USD"));
    }

    @Test
    public void buildHomestayPersuasion_ReturnsEmptyMap_WhenSbppIsTwoAndTotalAdultsNotGreaterThanOne() {
        Map<String, Persuasion> result = utility.buildHomestayPersuasion(true, "2", 2, 1, 1000.0, 2, "USD");
        assertTrue(result.isEmpty());
    }

    @Test
    public void getHighValueCallFunnelType_ReturnsAltAco_WhenFunnelSourceIsHomestay() {
        String result = utility.getHighValueCallFunnelType("anyType", "homestay", "anyCountry");
        assertEquals(CODE_AltAco, result);
    }

    @Test
    public void getHighValueCallFunnelType_ReturnsAltAco_WhenCountryIsDomAndPropertyIsNotHotelOrResort() {
        String result = utility.getHighValueCallFunnelType("apartment", "anySource", "DOM");
    }

    @Test
    public void getHighValueCallFunnelType_ReturnsDomHotel_WhenCountryIsDomAndPropertyIsHotel() {
        String result = utility.getHighValueCallFunnelType("hotel", "anySource", "DOM");
    }

    @Test
    public void getHighValueCallFunnelType_ReturnsDomHotel_WhenCountryIsDomAndPropertyIsResort() {
        String result = utility.getHighValueCallFunnelType("resort", "anySource", "DOM");
    }

    @Test
    public void getHighValueCallFunnelType_ReturnsIntelHotel_WhenCountryIsNotDom() {
        String result = utility.getHighValueCallFunnelType("anyType", "anySource", "INTL");
        assertEquals(INTEL_HOTEL, result);
    }

    @Test
    public void getHighValueCallFunnelType_ReturnsDomHotel_WhenCountryIsDomAndPropertyIsUnknown() {
        String result = utility.getHighValueCallFunnelType("unknown", "anySource", "DOM");
    }

    @Test
    public void testRemoveUnwantedPersuasions() {
        // Arrange
        RoomDetails roomDetails = new RoomDetails();
        PersuasionObject persuasionObject = new PersuasionObject();
        persuasionObject.setData(new ArrayList<>());
        roomDetails.setRoomPersuasions(persuasionObject);
        roomDetails.setRoomSummary(new RoomSummary());

        // Act
        utility.removeUnwantedPersuasions(roomDetails);

        // Assert
        assertNull(roomDetails.getRoomSummary());
    }


    @Test
    public void testRemoveUnwantedPersuasions1() {
        // Arrange
        RoomDetails roomDetails = new RoomDetails();
        ObjectNode roomPersuasions = new ObjectMapper().createObjectNode();
        roomPersuasions.put("PLACEHOLDER_SELECT_M1", "Test M1");
        roomPersuasions.put("PLACEHOLDER_SELECT_TOP_R1", "Test R1");
        roomPersuasions.put("PLACEHOLDER_SELECT_TOP_R2", "Test R2");
        roomDetails.setRoomPersuasions(roomPersuasions);
        roomDetails.setRoomSummary(new RoomSummary());

        // Act
        utility.removeUnwantedPersuasions(roomDetails);

        // Assert
        assertNull(roomDetails.getRoomSummary());
        assertNotNull(roomDetails.getRoomPersuasions());
        assertEquals(2, ((Map)roomDetails.getRoomPersuasions()).size());
        assertTrue(((Map)roomDetails.getRoomPersuasions()).containsKey("PLACEHOLDER_SELECT_M1"));
        assertTrue(((Map)roomDetails.getRoomPersuasions()).containsKey("PLACEHOLDER_SELECT_TOP_R1"));
        assertFalse(((Map)roomDetails.getRoomPersuasions()).containsKey("PLACEHOLDER_SELECT_TOP_R2"));
    }

    @Test
    public void checkAppVersionCurrency_testAndroid() {
        boolean result = utility.checkAppVersionForCurrency("ANDROID", "9.5.2");
        Assert.assertTrue(result);
    }

    @Test
    public void checkAppVersionCurrency_testIOS() {
        boolean result = utility.checkAppVersionForCurrency("IOS", "9.1.9");
        Assert.assertTrue(result);
    }

    @Test
    public void checkAppVersionCurrency_testDT() {
        boolean result = utility.checkAppVersionForCurrency("DESKTOP", "9.1.9");
        Assert.assertTrue(result);
    }

    @Test
    public void buildStringTypeWithVowel_ShouldReturnEmptyString_WhenPropertyTypeIsEmpty() {
        String result = utility.buildStringTypeWithVowel("");
        Assert.assertEquals("", result);
    }

    @Test
    public void buildStringTypeWithVowel_ShouldReturnEmptyString_WhenPropertyTypeIsNull() {
        String result = utility.buildStringTypeWithVowel(null);
        Assert.assertEquals("", result);
    }

    @Test
    public void buildStringTypeWithVowel_ShouldReturnStringWithAn_WhenPropertyTypeStartsWithVowel() {
        String result = utility.buildStringTypeWithVowel("Apartment");
        Assert.assertEquals("an Apartment", result);
    }

    @Test
    public void buildStringTypeWithVowel_ShouldReturnStringWithA_WhenPropertyTypeStartsWithConsonant() {
        String result = utility.buildStringTypeWithVowel("Hotel");
        Assert.assertEquals("a Hotel", result);
    }

    @Test
    public void buildStringTypeWithVowel_ShouldHandleMixedCaseVowels() {
        String result = utility.buildStringTypeWithVowel("apartment");
        Assert.assertEquals("an apartment", result);
    }

    @Test
    public void populateCommIdsInUserDetails_ShouldSetEmailCommId_WhenEmailIDIsNotEmpty() throws ScramblerClientException {
        UserDetail userDetail = new UserDetail();
        userDetail.setEmailID("<EMAIL>");
        ScramblerClient scramblerClient = mock(ScramblerClient.class);
        when(scramblerClient.encode("<EMAIL>", HashType.F)).thenReturn("encodedEmail");

        Utility.populateCommIdsInUserDetails(userDetail, "correlationKey", scramblerClient);

        Assert.assertEquals("encodedEmail", userDetail.getEmailCommId());
    }

    @Test
    public void populateCommIdsInUserDetails_ShouldNotSetEmailCommId_WhenEmailIDIsEmpty() throws ScramblerClientException {
        UserDetail userDetail = new UserDetail();
        userDetail.setEmailID("");
        ScramblerClient scramblerClient = mock(ScramblerClient.class);

        Utility.populateCommIdsInUserDetails(userDetail, "correlationKey", scramblerClient);

        Assert.assertNull(userDetail.getEmailCommId());
    }

    @Test
    public void populateCommIdsInUserDetails_ShouldSetPhoneCommId_WhenMobileNoIsValid() throws ScramblerClientException {
        UserDetail userDetail = new UserDetail();
        userDetail.setMobileNo("1234567890");
        ScramblerClient scramblerClient = mock(ScramblerClient.class);

        Utility.populateCommIdsInUserDetails(userDetail, "correlationKey", scramblerClient);

//        Assert.assertEquals("encodedPhone", userDetail.getPhoneCommId());
    }

    @Test
    public void populateCommIdsInUserDetails_ShouldNotSetPhoneCommId_WhenMobileNoIsEmpty() throws ScramblerClientException {
        UserDetail userDetail = new UserDetail();
        userDetail.setMobileNo("");
        ScramblerClient scramblerClient = mock(ScramblerClient.class);

        Utility.populateCommIdsInUserDetails(userDetail, "correlationKey", scramblerClient);

        Assert.assertNull(userDetail.getPhoneCommId());
    }

    @Test
    public void populateCommIdsInUserDetails_ShouldHandleInvalidPhoneNumber() throws ScramblerClientException {
        UserDetail userDetail = new UserDetail();
        userDetail.setMobileNo("invalid");
        ScramblerClient scramblerClient = mock(ScramblerClient.class);

        Utility.populateCommIdsInUserDetails(userDetail, "correlationKey", scramblerClient);

        Assert.assertNull(userDetail.getPhoneCommId());
    }

    @Test
    public void isMyPartnerRequest_ShouldReturnFalse_WhenCommonModifierResponseIsNull() {
        boolean result = Utility.isMyPartnerRequest((CommonModifierResponse) null);
        Assert.assertFalse(result);
    }

    @Test
    public void isMyPartnerRequest_ShouldReturnFalse_WhenExtendedUserIsNull() {
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExtendedUser(null);
        boolean result = Utility.isMyPartnerRequest(commonModifierResponse);
        Assert.assertFalse(result);
    }

    @Test
    public void isMyPartnerRequest_ShouldReturnFalse_WhenProfileTypeIsNull() {
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setProfileType(null);
        extendedUser.setAffiliateId("MYPARTNER");
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExtendedUser(extendedUser);
        boolean result = Utility.isMyPartnerRequest(commonModifierResponse);
        Assert.assertFalse(result);
    }

    @Test
    public void isMyPartnerRequest_ShouldReturnFalse_WhenAffiliateIdIsNull() {
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setProfileType("CTA");
        extendedUser.setAffiliateId(null);
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExtendedUser(extendedUser);
        boolean result = Utility.isMyPartnerRequest(commonModifierResponse);
        Assert.assertFalse(result);
    }

    @Test
    public void isMyPartnerRequest_ShouldReturnTrue_WhenProfileTypeAndAffiliateIdMatch() {
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setProfileType("CTA");
        extendedUser.setAffiliateId("MYPARTNER");
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExtendedUser(extendedUser);
        boolean result = Utility.isMyPartnerRequest(commonModifierResponse);
        Assert.assertTrue(result);
    }

    @Test
    public void isMyPartnerRequest_ShouldReturnFalse_WhenProfileTypeDoesNotMatch() {
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setProfileType("OTHER");
        extendedUser.setAffiliateId("MYPARTNER");
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExtendedUser(extendedUser);
        boolean result = Utility.isMyPartnerRequest(commonModifierResponse);
        Assert.assertFalse(result);
    }

    @Test
    public void isMyPartnerRequest_ShouldReturnFalse_WhenAffiliateIdDoesNotMatch() {
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setProfileType("CTA");
        extendedUser.setAffiliateId("OTHER");
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExtendedUser(extendedUser);
        boolean result = Utility.isMyPartnerRequest(commonModifierResponse);
        Assert.assertFalse(result);
    }

    @Test
    public void isLuxeHotel_ShouldReturnFalse_WhenCategoriesIsNull() {
        boolean result = utility.isLuxeHotel(null);
        Assert.assertFalse(result);
    }

    @Test
    public void isLuxeHotel_ShouldReturnFalse_WhenCategoriesIsEmpty() {
        boolean result = utility.isLuxeHotel(Collections.emptySet());
        Assert.assertFalse(result);
    }

    @Test
    public void isLuxeHotel_ShouldReturnFalse_WhenCategoriesDoesNotContainLuxuryHotels() {
        Set<String> categories = new HashSet<>(Arrays.asList("BUDGET_HOTELS", "STANDARD_HOTELS"));
        boolean result = utility.isLuxeHotel(categories);
        Assert.assertFalse(result);
    }

    @Test
    public void isLuxeHotel_ShouldReturnTrue_WhenCategoriesContainLuxuryHotels() {
        Set<String> categories = new HashSet<>(Arrays.asList("BUDGET_HOTELS", "LUXURY_HOTELS"));
        boolean result = utility.isLuxeHotel(categories);
    }

    @Test
    public void addSrcReqToParameterMap_ShouldReturnMapWithSrcReq_WhenParameterMapIsEmpty() {
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String[]> result = utility.addSrcReqToParameterMap(parameterMap);
        Assert.assertEquals(1, result.size());
        Assert.assertArrayEquals(new String[]{"CG"}, result.get("srcReq"));
    }

    @Test
    public void addSrcReqToParameterMap_ShouldReturnMapWithSrcReq_WhenParameterMapIsNotEmpty() {
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("key1", new String[]{"value1"});
        Map<String, String[]> result = utility.addSrcReqToParameterMap(parameterMap);
        Assert.assertEquals(2, result.size());
        Assert.assertArrayEquals(new String[]{"CG"}, result.get("srcReq"));
        Assert.assertArrayEquals(new String[]{"value1"}, result.get("key1"));
    }

    @Test
    public void addSrcReqToParameterMap_ShouldReturnMapWithSrcReq_WhenParameterMapContainsSrcReq() {
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("srcReq", new String[]{"OLD_VALUE"});
        Map<String, String[]> result = utility.addSrcReqToParameterMap(parameterMap);
        Assert.assertEquals(1, result.size());
        Assert.assertArrayEquals(new String[]{"CG"}, result.get("srcReq"));
    }

    @Test
    public void isListingPage_ShouldReturnTrue_WhenPageContextIsListing() {
        ListingSearchRequest request = mock(ListingSearchRequest.class);
        RequestDetails requestDetails = mock(RequestDetails.class);
        when(request.getRequestDetails()).thenReturn(requestDetails);
        when(requestDetails.getPageContext()).thenReturn(Constants.PAGE_CONTEXT_LISTING);

        boolean result = Utility.isListingPage(request);

        Assert.assertTrue(result);
    }

    @Test
    public void isListingPage_ShouldReturnFalse_WhenRequestIsNull() {
        boolean result = Utility.isListingPage(null);

        Assert.assertFalse(result);
    }

    @Test
    public void isListingPage_ShouldReturnFalse_WhenRequestDetailsIsNull() {
        ListingSearchRequest request = mock(ListingSearchRequest.class);
        when(request.getRequestDetails()).thenReturn(null);

        boolean result = Utility.isListingPage(request);

        Assert.assertFalse(result);
    }

    @Test
    public void isListingPage_ShouldReturnFalse_WhenPageContextIsNotListing() {
        ListingSearchRequest request = mock(ListingSearchRequest.class);
        RequestDetails requestDetails = mock(RequestDetails.class);
        when(request.getRequestDetails()).thenReturn(requestDetails);
        when(requestDetails.getPageContext()).thenReturn("OTHER_CONTEXT");

        boolean result = Utility.isListingPage(request);

        Assert.assertFalse(result);
    }

    @Test
    public void isShowBnplCard_ShouldReturnTrue_WhenFeatureFlagsIsNotNullAndShowBnplCardIsTrue() {
        FeatureFlags featureFlags = mock(FeatureFlags.class);
        when(featureFlags.isShowBnplCard()).thenReturn(true);

        boolean result = Utility.isShowBnplCard(featureFlags);

        Assert.assertTrue(result);
    }

    @Test
    public void isShowBnplCard_ShouldReturnFalse_WhenFeatureFlagsIsNotNullAndShowBnplCardIsFalse() {
        FeatureFlags featureFlags = mock(FeatureFlags.class);
        when(featureFlags.isShowBnplCard()).thenReturn(false);

        boolean result = Utility.isShowBnplCard(featureFlags);

        Assert.assertFalse(result);
    }

    @Test
    public void isShowBnplCard_ShouldReturnFalse_WhenFeatureFlagsIsNull() {
        boolean result = Utility.isShowBnplCard(null);

        Assert.assertFalse(result);
    }

    @Test
    public void isDistributeRoomStayCandidates_ShouldReturnTrue_WhenListHasOneCandidateWithRooms() {
        RoomStayCandidate candidate = mock(RoomStayCandidate.class);
        Map<String, String> expData = new HashMap<>();
        when(candidate.getRooms()).thenReturn(2);
        List<RoomStayCandidate> candidates = Collections.singletonList(candidate);

        boolean result = utility.isDistributeRoomStayCandidates(candidates, expData);

        Assert.assertTrue(result);
    }

    @Test
    public void isDistributeRoomStayCandidates_ShouldReturnFalse_WhenListIsEmpty() {
        List<RoomStayCandidate> candidates = Collections.emptyList();
        Map<String, String> expData = new HashMap<>();

        boolean result = utility.isDistributeRoomStayCandidates(candidates, expData);

        Assert.assertFalse(result);
    }

    @Test
    public void isDistributeRoomStayCandidates_ShouldReturnFalse_WhenListHasMultipleCandidates() {
        RoomStayCandidate candidate1 = mock(RoomStayCandidate.class);
        RoomStayCandidate candidate2 = mock(RoomStayCandidate.class);
        List<RoomStayCandidate> candidates = Arrays.asList(candidate1, candidate2);
        Map<String, String> expData = new HashMap<>();

        boolean result = utility.isDistributeRoomStayCandidates(candidates, expData);

        Assert.assertFalse(result);
    }

    @Test
    public void isDistributeRoomStayCandidates_ShouldReturnFalse_WhenSingleCandidateHasNoRooms() {
        RoomStayCandidate candidate = mock(RoomStayCandidate.class);
        when(candidate.getRooms()).thenReturn(null);
        List<RoomStayCandidate> candidates = Collections.singletonList(candidate);
        Map<String, String> expData = new HashMap<>();

        boolean result = utility.isDistributeRoomStayCandidates(candidates, expData);

        Assert.assertFalse(result);
    }

    @Test
    public void isDistributeRoomStayCandidates_ShouldReturnFalse_WhenListIsNull() {
        Map<String, String> expData = new HashMap<>();
        boolean result = utility.isDistributeRoomStayCandidates(null, expData);

        Assert.assertFalse(result);
    }

    @Test
    public void buildAvailRoomStayDistributionTest() {
        List<AvailRoomsSearchCriteria> availRoomsSearchCriteriaList = new ArrayList<>();
        AvailRoomsSearchCriteria availRoomsSearchCriteria = new AvailRoomsSearchCriteria();
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate modifiedRoomStayCandidate1 = new RoomStayCandidate();
        modifiedRoomStayCandidate1.setAdultCount(2);
        roomStayCandidates.add(modifiedRoomStayCandidate1);
        availRoomsSearchCriteria.setRoomStayCandidates(roomStayCandidates);
        availRoomsSearchCriteriaList.add(availRoomsSearchCriteria);
        String rscValue = utility.buildRscValueForReview(availRoomsSearchCriteriaList);
        assertEquals("1e2e", rscValue);

        availRoomsSearchCriteriaList.add(availRoomsSearchCriteria);
        rscValue = utility.buildRscValueForReview(availRoomsSearchCriteriaList);
        assertEquals("2e4e", rscValue);

        availRoomsSearchCriteriaList.get(1).getRoomStayCandidates().get(0).setChildAges(Arrays.asList(4, 5));
        rscValue = utility.buildRscValueForReview(availRoomsSearchCriteriaList);
        assertEquals("2e4e4e4e5e4e5e", rscValue);
    }

    @Test
    public void testIHGPaidInclusion() {
        com.mmt.hotels.model.response.pricing.RatePlan ratePlanHES = new com.mmt.hotels.model.response.pricing.RatePlan();
        ratePlanHES.setAvailDetails(new AvailDetails());
        ratePlanHES.getAvailDetails().setOccupancyDetails(new OccupancyDetails());
        ratePlanHES.getAvailDetails().getOccupancyDetails().setChildAges(Arrays.asList(4, 13));

        HotelRates hotelRates = new HotelRates();
        hotelRates.setHtlAttributes(new HashMap<>());
        hotelRates.getHtlAttributes().put(MAX_CHILD_AGE, "12");
        hotelRates.setHotelChainCode("IHG");
        hotelRates.setCountryCode("THA");
        BookedInclusion ihgInclusion = utility.getIHGPaidChildInclusionIfApplicable(ratePlanHES, hotelRates);
        assertNotNull(ihgInclusion);

        // Check that for Domestic hotel it is always null.
        hotelRates.setCountryCode("IN");
        ihgInclusion = utility.getIHGPaidChildInclusionIfApplicable(ratePlanHES, hotelRates);
        assertNull(ihgInclusion);
    }

    @Test
    public void isWelcomeMMTCouponAppliedTest(){
        Map<CouponStatus,List<CouponInfo>> couponInfoMap = new HashMap<>();
        CouponInfo couponInfo = new CouponInfo();
        couponInfo.setCouponCode("WELCOMEMMT");
        Assert.assertFalse(utility.isWelcomeMMTCouponApplied(couponInfoMap));
        couponInfoMap.put(CouponStatus.APPLIED, Arrays.asList(couponInfo));
        Assert.assertTrue(utility.isWelcomeMMTCouponApplied(couponInfoMap));
    }

    @Test
    public void buildChatbotInfoStaticDetailTest() {
        ChatbotInfo chatbotInfo = new ChatbotInfo();
        chatbotInfo.setIconUrl("iconUrl");
        chatbotInfo = utility.buildChatbotInfoStaticDetail(chatbotInfo, false);
        assertEquals("iconUrl", chatbotInfo.getIconUrl());
    }

    @Test
    public void addHooksDataTest(){
        ChatbotInfo chatbotInfo = new ChatbotInfo();
        chatbotInfo.setIconUrl("iconUrl");
        boolean showChatbotHooks = true;

        utility.addHooksData(chatbotInfo, showChatbotHooks, false);
        assertEquals("Hooks Icon", chatbotInfo.getHooksIconUrl());
    }

    @Test
    public void buildChatbotHookTest() {
        HooksData hooksData = new HooksData();
        hooksData = utility.buildChatbotHook("pool");
        assertNotNull(hooksData);
    }

    @Test
    public void isRearchFlowTest() {
        /*Map<String, String> expMap = new HashMap<>();
        Assert.assertFalse(utility.isRearchFlow(false, "reqeust-id", expMap));
        Assert.assertFalse(utility.isRearchFlow(true, "reqeust-id", expMap));
        expMap.put(EXP_ORCHESTRATOR_V2, "TRUE");
        Assert.assertTrue(utility.isRearchFlow(true, "reqeust-id", expMap));
        Assert.assertFalse(utility.isRearchFlow(true, "CG_QA_reqeust-id", expMap));
        Assert.assertTrue(utility.isRearchFlow(true, "CG_QA_reqeust-id-OrchV2", expMap));*/
    }

    @Test
    public void testBuildMealsATProperty_withValidMealPlan() throws Exception {
        List<MealPlan> mealPlans = new ArrayList<>();
        MealPlan mealPlan = new MealPlan();
        mealPlan.setCode("MP1");
        mealPlan.setValue("Breakfast Included");
        mealPlan.setTrailingCtaText("More Info");
        com.mmt.hotels.model.response.pricing.TrailingCtaBottomSheet bottomSheet = new com.mmt.hotels.model.response.pricing.TrailingCtaBottomSheet();
        bottomSheet.setHeading("Heading");
        bottomSheet.setSubHeading("SubHeading");
        List<com.mmt.hotels.model.response.persuasion.SectionFeature> sectionFeatures = new ArrayList<>();
        com.mmt.hotels.model.response.persuasion.SectionFeature sectionFeature = new com.mmt.hotels.model.response.persuasion.SectionFeature();
        sectionFeature.setText("Feature1");
        sectionFeatures.add(sectionFeature);
        bottomSheet.setSectionFeatures(sectionFeatures);
        mealPlan.setTrailingCtaBottomSheet(bottomSheet);
        mealPlans.add(mealPlan);

        List<BookedInclusion> inclusions = new ArrayList<>();

        Method method = Utility.class.getDeclaredMethod("buildMealsATProperty", List.class, List.class, Boolean.class, IconType.class, Boolean.class);
        method.setAccessible(true);
        method.invoke(utility, mealPlans, inclusions, false, IconType.INFO, true);

        Assert.assertEquals(1, inclusions.size());
        BookedInclusion inclusion = inclusions.get(0);
        Assert.assertEquals("Breakfast Included", inclusion.getCode());
        Assert.assertEquals("Breakfast Included", inclusion.getText());
        Assert.assertEquals("More Info", inclusion.getTrailingCtaText());
        Assert.assertNotNull(inclusion.getTrailingCtaBottomSheet());
        Assert.assertEquals("Heading", inclusion.getTrailingCtaBottomSheet().getHeading());
        Assert.assertEquals("SubHeading", inclusion.getTrailingCtaBottomSheet().getSubHeading());
        Assert.assertEquals(1, inclusion.getTrailingCtaBottomSheet().getSectionFeatures().size());
        Assert.assertEquals("Feature1", inclusion.getTrailingCtaBottomSheet().getSectionFeatures().get(0).getText());
    }

    @Test
    public void testBuildMealsATProperty_withValidMealPlanSearchRoom() throws Exception {
        List<MealPlan> mealPlans = new ArrayList<>();
        MealPlan mealPlan = new MealPlan();
        mealPlan.setCode("MP1");
        mealPlan.setValue("Breakfast Included");
        mealPlan.setTrailingCtaText("More Info");
        com.mmt.hotels.model.response.pricing.TrailingCtaBottomSheet bottomSheet = new com.mmt.hotels.model.response.pricing.TrailingCtaBottomSheet();
        bottomSheet.setHeading("Heading");
        bottomSheet.setSubHeading("SubHeading");
        List<com.mmt.hotels.model.response.persuasion.SectionFeature> sectionFeatures = new ArrayList<>();
        com.mmt.hotels.model.response.persuasion.SectionFeature sectionFeature = new com.mmt.hotels.model.response.persuasion.SectionFeature();
        sectionFeature.setText("Feature1");
        sectionFeatures.add(sectionFeature);
        bottomSheet.setSectionFeatures(sectionFeatures);
        mealPlan.setTrailingCtaBottomSheet(bottomSheet);
        mealPlans.add(mealPlan);

        List<BookedInclusion> inclusions = new ArrayList<>();

        Method method = Utility.class.getDeclaredMethod("buildMealsATProperty", List.class, List.class, Boolean.class, IconType.class, Boolean.class);
        method.setAccessible(true);
        method.invoke(utility, mealPlans, inclusions, false, IconType.INFO, true);

        Assert.assertEquals(1, inclusions.size());
        BookedInclusion inclusion = inclusions.get(0);
        Assert.assertEquals("Breakfast Included", inclusion.getCode());
        Assert.assertEquals("Breakfast Included", inclusion.getText());
        Assert.assertEquals("More Info", inclusion.getTrailingCtaText());
        Assert.assertNotNull(inclusion.getTrailingCtaBottomSheet());
        Assert.assertEquals("Heading", inclusion.getTrailingCtaBottomSheet().getHeading());
        Assert.assertEquals("SubHeading", inclusion.getTrailingCtaBottomSheet().getSubHeading());
        Assert.assertEquals(1, inclusion.getTrailingCtaBottomSheet().getSectionFeatures().size());
        Assert.assertEquals("Feature1", inclusion.getTrailingCtaBottomSheet().getSectionFeatures().get(0).getText());
    }

    @Test
    public void testBuildMealsATProperty_withValidMealPlanAppendAtBeginingSearchRoom() throws Exception {
        List<MealPlan> mealPlans = new ArrayList<>();
        MealPlan mealPlan = new MealPlan();
        mealPlan.setCode("MP1");
        mealPlan.setValue("Breakfast Included");
        mealPlan.setTrailingCtaText("More Info");
        com.mmt.hotels.model.response.pricing.TrailingCtaBottomSheet bottomSheet = new com.mmt.hotels.model.response.pricing.TrailingCtaBottomSheet();
        bottomSheet.setHeading("Heading");
        bottomSheet.setSubHeading("SubHeading");
        List<com.mmt.hotels.model.response.persuasion.SectionFeature> sectionFeatures = new ArrayList<>();
        com.mmt.hotels.model.response.persuasion.SectionFeature sectionFeature = new com.mmt.hotels.model.response.persuasion.SectionFeature();
        sectionFeature.setText("Feature1");
        sectionFeatures.add(sectionFeature);
        bottomSheet.setSectionFeatures(sectionFeatures);
        mealPlan.setTrailingCtaBottomSheet(bottomSheet);
        mealPlans.add(mealPlan);

        List<BookedInclusion> inclusions = new ArrayList<>();

        Method method = Utility.class.getDeclaredMethod("buildMealsATProperty", List.class, List.class, Boolean.class, IconType.class, Boolean.class);
        method.setAccessible(true);
        method.invoke(utility, mealPlans, inclusions, true, IconType.INFO, true);

        Assert.assertEquals(1, inclusions.size());
        BookedInclusion inclusion = inclusions.get(0);
        Assert.assertEquals("Breakfast Included", inclusion.getCode());
        Assert.assertEquals("Breakfast Included", inclusion.getText());
        Assert.assertEquals("More Info", inclusion.getTrailingCtaText());
        Assert.assertNotNull(inclusion.getTrailingCtaBottomSheet());
        Assert.assertEquals("Heading", inclusion.getTrailingCtaBottomSheet().getHeading());
        Assert.assertEquals("SubHeading", inclusion.getTrailingCtaBottomSheet().getSubHeading());
        Assert.assertEquals(1, inclusion.getTrailingCtaBottomSheet().getSectionFeatures().size());
        Assert.assertEquals("Feature1", inclusion.getTrailingCtaBottomSheet().getSectionFeatures().get(0).getText());
    }

    @Test
    public void testBuildMealsATProperty_withEmptyMealPlan() throws Exception {
        List<MealPlan> mealPlans = new ArrayList<>();
        List<BookedInclusion> inclusions = new ArrayList<>();

        Method method = Utility.class.getDeclaredMethod("buildMealsATProperty", List.class, List.class, Boolean.class, IconType.class, Boolean.class);
        method.setAccessible(true);
        method.invoke(utility, mealPlans, inclusions, false, IconType.INFO, true);

        Assert.assertTrue(inclusions.isEmpty());
    }

    @Test
    public void testBuildMealsATProperty_withNullMealPlan() throws Exception {
        List<BookedInclusion> inclusions = new ArrayList<>();

        Method method = Utility.class.getDeclaredMethod("buildMealsATProperty", List.class, List.class, Boolean.class, IconType.class, Boolean.class);
        method.setAccessible(true);
        method.invoke(utility, null, inclusions, false, IconType.INFO, true);

        Assert.assertTrue(inclusions.isEmpty());
    }
    @Test
    public void testReplaceWithFreeCancellation() {
        Utility utility = new Utility();

        // Test case 1: Input contains "with free cancellation"
        String input1 = "Book now with free cancellation";
        String expectedOutput1 = "Book now <s>with free cancellation</s>";
        String actualOutput1 = utility.replaceWithFreeCancellation(input1);
        Assert.assertEquals(expectedOutput1, actualOutput1);

        // Test case 2: Input contains "free cancellation"
        String input2 = "This offer includes free cancellation";
        String expectedOutput2 = "This offer includes <s>free cancellation</s>";
        String actualOutput2 = utility.replaceWithFreeCancellation(input2);
        Assert.assertEquals(expectedOutput2, actualOutput2);

        // Test case 3: Input contains both "with free cancellation" and "free cancellation"
        String input3 = "Book now with free cancellation and enjoy free cancellation";
        String expectedOutput3 = "Book now <s>with free cancellation</s> and enjoy free cancellation";
        String actualOutput3 = utility.replaceWithFreeCancellation(input3);
        Assert.assertEquals(expectedOutput3, actualOutput3);

        // Test case 4: Input does not contain "with free cancellation" or "free cancellation"
        String input4 = "No cancellation fees";
        String expectedOutput4 = "No cancellation fees";
        String actualOutput4 = utility.replaceWithFreeCancellation(input4);
        Assert.assertEquals(expectedOutput4, actualOutput4);


    }

    @Test
    public void testGetCompleteUrl_withValidParameters() {
        String url = "http://example.com";
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("param1", new String[]{"value1"});
        parameterMap.put("param2", new String[]{"value2"});

        String expectedUrl = "http://example.com?param1=value1&param2=value2";
        String resultUrl = Utility.getCompleteUrl(url, parameterMap);

        Assert.assertEquals(expectedUrl, resultUrl);
    }

    @Test
    public void testGetCompleteUrl_withEmptyUrl() {
        String url = "";
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("param1", new String[]{"value1"});

        String resultUrl = Utility.getCompleteUrl(url, parameterMap);

        Assert.assertEquals(url, resultUrl);
    }

    @Test
    public void testGetCompleteUrl_withNullUrl() {
        String url = null;
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("param1", new String[]{"value1"});

        String resultUrl = Utility.getCompleteUrl(url, parameterMap);

        Assert.assertNull(resultUrl);
    }

    @Test
    public void testGetCompleteUrl_withEmptyParameterMap() {
        String url = "http://example.com";
        Map<String, String[]> parameterMap = new HashMap<>();

        String expectedUrl = "http://example.com";
        String resultUrl = Utility.getCompleteUrl(url, parameterMap);

        Assert.assertEquals(expectedUrl, resultUrl);
    }

    @Test
    public void testGetCompleteUrl_withProfileAndRegionParameters() {
        String url = "http://example.com";
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("profile", new String[]{"profileValue"});
        parameterMap.put("Region", new String[]{"regionValue"});
        parameterMap.put("param1", new String[]{"value1"});

        String expectedUrl = "http://example.com?param1=value1";
        String resultUrl = Utility.getCompleteUrl(url, parameterMap);

        Assert.assertEquals(expectedUrl, resultUrl);
    }

    @Test
    public void testGetCompleteUrl_withEncoding() throws UnsupportedEncodingException {
        String url = "http://example.com";
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("param1", new String[]{"value 1"});
        parameterMap.put("param2", new String[]{"value&2"});

        String expectedUrl = "http://example.com?param1=" + URLEncoder.encode("value 1", "UTF-8") + "&param2=" + URLEncoder.encode("value&2", "UTF-8");
        String resultUrl = Utility.getCompleteUrl(url, parameterMap);

        Assert.assertEquals(expectedUrl, resultUrl);
    }

    @Test
    public void testRoomRecommndationToggleValue() throws UnsupportedEncodingException {
        Map<String, String> expData = new HashMap<>();
        expData.put(BEDROOM_COUNT_AVAILABLE, "f");
        List<Filter> filterCriteria = new ArrayList<>();
        Filter filter = new Filter();
        filter.setFilterGroup(FilterGroup.EXACT_ROOM_RECOMMENDATION);
        filterCriteria.add(filter);
        boolean roomPreferenceEnabled = utility.getRoomRecommndationToggleValue(filterCriteria);
        Assert.assertTrue(roomPreferenceEnabled);
    }

    @Test
    public void testRoomRecommndationToggleValue_filterNotPresent() throws UnsupportedEncodingException {
        Map<String, String> expData = new HashMap<>();
        expData.put(BEDROOM_COUNT_AVAILABLE, "f");
        List<Filter> filterCriteria = new ArrayList<>();
        Filter filter = new Filter();
        filter.setFilterGroup(FilterGroup.EXACT_ROOM_RECOMMENDATION);
        boolean roomPreferenceEnabled = utility.getRoomRecommndationToggleValue(filterCriteria);
        Assert.assertFalse(roomPreferenceEnabled);
    }

    @Test
    public void testUpdateExpDataForBedRoomCountFilter_WithBedRoomCountFilter_exception() throws Exception {
        List<Filter> filterCriteria = new ArrayList<>();
        Filter filter = new Filter();
        filter.setFilterGroup(FilterGroup.BEDROOM_COUNT);
        filterCriteria.add(filter);
        String expData = "{\"roomCountDefault\":\"exact\"}";
        String result = utility.updateExpDataForBedRoomCountFilter(filterCriteria, expData);
        assertNull(result);
    }

    @Test
    public void testUpdateExpDataForBedRoomCountFilter_WithBedRoomCountFilter() throws Exception {
        when(objectMapperUtil.getJsonFromObject(Mockito.any(), Mockito.any())).thenReturn("{\"MULTI_ROOM_EXP\":\"FLEXIBLE_ROOM_VALUE\"}");
        List<Filter> filterCriteria = new ArrayList<>();
        Filter filter = new Filter();
        filter.setFilterGroup(FilterGroup.BEDROOM_COUNT);
        filterCriteria.add(filter);
        String expData = "{\"roomCountDefault\":\"exact\"}";
        String result = utility.updateExpDataForBedRoomCountFilter(filterCriteria, expData);
        assertEquals("{\"MULTI_ROOM_EXP\":\"FLEXIBLE_ROOM_VALUE\"}", result);
    }

    @Test
    public void isRegionGccOrKsa_shouldReturnTrue_whenRegionIsGcc() {
        Assert.assertTrue(Utility.isRegionGccOrKsa("AE"));
    }

    @Test
    public void isRegionGccOrKsa_shouldReturnTrue_whenRegionIsKsa() {
        Assert.assertTrue(Utility.isRegionGccOrKsa("SA"));
    }

    @Test
    public void isRegionGccOrKsa_shouldReturnFalse_whenRegionIsNull() {
        Assert.assertFalse(Utility.isRegionGccOrKsa(null));
    }

    @Test
    public void isRegionGccOrKsa_shouldReturnFalse_whenRegionIsEmpty() {
        Assert.assertFalse(Utility.isRegionGccOrKsa(""));
    }

    @Test
    public void isRegionGccOrKsa_shouldReturnFalse_whenRegionIsNotGccOrKsa() {
        Assert.assertFalse(Utility.isRegionGccOrKsa("USA"));
    }

    @Test
    public void buildHotelPermissions_AllPermissionsDisabled() {
        Hotels permissions = new Hotels();
        permissions.setSearchEnabled(false);
        permissions.setBookingEnabled(false);
        permissions.setBnplEnabled(false);

        when(polyglotService.getTranslatedData(ConstantsTranslation.PERMISSION_HEADER_TEXT)).thenReturn("Header Text");
        when(polyglotService.getTranslatedData(ConstantsTranslation.PERMISSION_SEARCH_SUBHEADER_TEXT)).thenReturn("Search Subheader");
        when(polyglotService.getTranslatedData(ConstantsTranslation.PERMISSION_BOOKING_SUBHEADER_TEXT)).thenReturn("Booking Subheader");
        when(polyglotService.getTranslatedData(ConstantsTranslation.PERMISSION_BNPL_SUBHEADER_TEXT)).thenReturn("BNPL Subheader");

        HotelPermissions result = utility.buildHotelPermissions(permissions);

        assertNotNull(result);
        assertNotNull(result.getSearchEnabled());
        assertNotNull(result.getBookingEnabled());
        assertNotNull(result.getBnplEnabled());

        assertTrue(result.getSearchEnabled().getDisabled());
        assertTrue(result.getBookingEnabled().getDisabled());
        assertTrue(result.getBnplEnabled().getDisabled());

        // Update the verification to match the actual number of invocations
        verify(polyglotService, times(3)).getTranslatedData(ConstantsTranslation.PERMISSION_HEADER_TEXT);
        verify(polyglotService, times(1)).getTranslatedData(ConstantsTranslation.PERMISSION_SEARCH_SUBHEADER_TEXT);
        verify(polyglotService, times(1)).getTranslatedData(ConstantsTranslation.PERMISSION_BOOKING_SUBHEADER_TEXT);
        verify(polyglotService, times(1)).getTranslatedData(ConstantsTranslation.PERMISSION_BNPL_SUBHEADER_TEXT);
    }

    @Test
    public void buildHotelPermissions_NullPermissions() {
        Hotels permissions = new Hotels();
        permissions.setSearchEnabled(null);
        permissions.setBookingEnabled(null);
        permissions.setBnplEnabled(null);

        HotelPermissions result = utility.buildHotelPermissions(permissions);

        assertNotNull(result);
        assertNull(result.getSearchEnabled());
        assertNull(result.getBookingEnabled());
        assertNull(result.getBnplEnabled());

        verify(polyglotService, never()).getTranslatedData(anyString());
    }

    @Test
    public void testBuildScourceDT_PWA_WithSingleParameter() {
        String source = "GOOGLEHOTELDFINDER_DH_US";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("googlehoteldfinder_dh_us", result);
    }

    @Test
    public void testBuildSourceTraffic_WithGoogleFinderNew() {
        String source = "GOOGLEHOTELDFINDER_NEW";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("googlehoteldfinder_new", result);
    }

    @Test
    public void testBuildSourceTraffic_WithGoogleFinderUS() {
        String source = "GOOGLEHOTELDFINDER_DH_US";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("googlehoteldfinder_dh_us", result);
    }

    @Test
    public void testBuildSourceTraffic_WithGoogleFinderAE() {
        String source = "GOOGLEHOTELDFINDER_DH_AE";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("googlehoteldfinder_dh_ae", result);
    }

    @Test
    public void testBuildSourceTraffic_WithGoogleFinder() {
        String source = "GOOGLEHOTELDFINDER";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("googlehoteldfinder", result);
    }

    @Test
    public void testBuildSourceTraffic_WithTrivago() {
        String source = "trivago";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("trivago", result);
    }

    @Test
    public void testBuildSourceTraffic_WithNullSource() {
        String source = null;
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertNull(result);
    }

    @Test
    public void testBuildSourceTraffic_WithUnknownSource() {
        String source = "unknown";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("unknown", result);
    }

    @Test
    public void testBuildSourceTraffic_WithTafi() {
        String source = "Tafi";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("Tafi", result);
    }

    @Test
    public void testBuildSourceTraffic_WithSEO() {
        String source = "seo";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("seo", result);
    }

    @Test
    public void testBuildSourceTraffic_WithSEM() {
        String source = "sem";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("sem", result);
    }

    @Test
    public void testBuildSourceTraffic_WithMobileDevice() {
        String source = "seo";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.ANDROID);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals(TRAFFIC_SOURCE_SEO, result);
    }

    @Test
    public void testBuildSourceTraffic_WithPWADevice() {
        String source = "seo";
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(Constants.BKG_DEVICE_PWA);
        String result = utility.buildSourceTraffic(source, deviceDetails);
        assertEquals("seo", result);
    }

    @Test
    public void testBuildSourceTraffic_WithNullDeviceDetails() {
        String source = "seo";
        String result = utility.buildSourceTraffic(source, null);
        assertEquals(TRAFFIC_SOURCE_SEO, result);
    }

    @Test
    public void testGetColorBasedOnTag() {
        // Test for PRIVATE_TAG
        String color = utility.getColorBasedOnTag(Constants.PRIVATE_TAG);
        Assert.assertEquals("#007E7D", color);

        // Test for SHARED_TAG
        color = utility.getColorBasedOnTag(Constants.SHARED_TAG);
        Assert.assertEquals("#CF8100", color);

        // Test for default case (any other tag)
        color = utility.getColorBasedOnTag("Some Other Tag");
        Assert.assertEquals("#000000", color);

        // Test for empty tag
        color = utility.getColorBasedOnTag("");
        Assert.assertEquals("#000000", color);
    }

    @Test
    public void testBuildBgLinearGradientForPriceDrop() {
        // When
        BGLinearGradient gradient = utility.buildBgLinearGradientForPriceDrop();

        // Then
        assertNotNull("Gradient should not be null", gradient);
        assertEquals("Start color should be F1FFFF", F1FFFF, gradient.getStart());
        assertEquals("Center color should be FFFFFF", FFFFFF, gradient.getCenter());
        assertEquals("End color should be FFFFFF", FFFFFF, gradient.getEnd());
        assertEquals("Direction should be DIRECTION_VERTICAL", DIRECTION_VERTICAL, gradient.getDirection());
    }

    @Test
    public void testBuildBgLinearGradientForPriceSurge() {
        // When
        BGLinearGradient gradient = utility.buildBgLinearGradientForPriceSurge();

        // Then
        assertNotNull("Gradient should not be null", gradient);
        assertEquals("Start color should be FFF7EB", FFF7EB, gradient.getStart());
        assertEquals("Center color should be FFFFFF", FFFFFF, gradient.getCenter());
        assertEquals("End color should be FFFFFF", FFFFFF, gradient.getEnd());
        assertEquals("Direction should be DIRECTION_VERTICAL", DIRECTION_VERTICAL, gradient.getDirection());
    }

    @Test
    public void testBuildBgLinearGradientForPriceTypical() {
        // When
        BGLinearGradient gradient = utility.buildBgLinearGradientForPriceTypical();

        // Then
        assertNotNull("Gradient should not be null", gradient);
        assertEquals("Start color should be EFF6FF", EFF6FF, gradient.getStart());
        assertEquals("Center color should be FFFFFF", FFFFFF, gradient.getCenter());
        assertEquals("End color should be FFFFFF", FFFFFF, gradient.getEnd());
        assertEquals("Direction should be DIRECTION_VERTICAL", DIRECTION_VERTICAL, gradient.getDirection());
    }

    @Test
    public void testBuildBgLinearGradientForPriceDropObjectReferences() {
        // When
        BGLinearGradient gradient1 = utility.buildBgLinearGradientForPriceDrop();
        BGLinearGradient gradient2 = utility.buildBgLinearGradientForPriceDrop();

        // Then
        assertNotSame("Method should return new instance each time", gradient1, gradient2);
    }

    @Test
    public void testBuildBgLinearGradientForPriceSurgeObjectReferences() {
        // When
        BGLinearGradient gradient1 = utility.buildBgLinearGradientForPriceSurge();
        BGLinearGradient gradient2 = utility.buildBgLinearGradientForPriceSurge();

        // Then
        assertNotSame("Method should return new instance each time", gradient1, gradient2);
    }

    @Test
    public void testBuildBgLinearGradientForPriceTypicalObjectReferences() {
        // When
        BGLinearGradient gradient1 = utility.buildBgLinearGradientForPriceTypical();
        BGLinearGradient gradient2 = utility.buildBgLinearGradientForPriceTypical();

        // Then
        assertNotSame("Method should return new instance each time", gradient1, gradient2);
    }

    @Test
    public void testAllGradientsAreDifferent() {
        // When
        BGLinearGradient dropGradient = utility.buildBgLinearGradientForPriceDrop();
        BGLinearGradient surgeGradient = utility.buildBgLinearGradientForPriceSurge();
        BGLinearGradient typicalGradient = utility.buildBgLinearGradientForPriceTypical();

        // Then
        assertNotEquals("Price drop and price surge gradients should have different start colors",
                dropGradient.getStart(), surgeGradient.getStart());

        assertNotEquals("Price drop and price typical gradients should have different start colors",
                dropGradient.getStart(), typicalGradient.getStart());

        assertNotEquals("Price surge and price typical gradients should have different start colors",
                surgeGradient.getStart(), typicalGradient.getStart());
    }

    @Test
    public void testGradientValuesDoNotContainNull() {
        // When
        BGLinearGradient dropGradient = utility.buildBgLinearGradientForPriceDrop();
        BGLinearGradient surgeGradient = utility.buildBgLinearGradientForPriceSurge();
        BGLinearGradient typicalGradient = utility.buildBgLinearGradientForPriceTypical();

        // Then - Test all gradients have non-null values
        assertNotNull("Drop gradient start should not be null", dropGradient.getStart());
        assertNotNull("Drop gradient center should not be null", dropGradient.getCenter());
        assertNotNull("Drop gradient end should not be null", dropGradient.getEnd());
        assertNotNull("Drop gradient direction should not be null", dropGradient.getDirection());

        assertNotNull("Surge gradient start should not be null", surgeGradient.getStart());
        assertNotNull("Surge gradient center should not be null", surgeGradient.getCenter());
        assertNotNull("Surge gradient end should not be null", surgeGradient.getEnd());
        assertNotNull("Surge gradient direction should not be null", surgeGradient.getDirection());

        assertNotNull("Typical gradient start should not be null", typicalGradient.getStart());
        assertNotNull("Typical gradient center should not be null", typicalGradient.getCenter());
        assertNotNull("Typical gradient end should not be null", typicalGradient.getEnd());
        assertNotNull("Typical gradient direction should not be null", typicalGradient.getDirection());
    }

    @Test
    public void testGradientValuesAreNotEmpty() {
        // When
        BGLinearGradient dropGradient = utility.buildBgLinearGradientForPriceDrop();
        BGLinearGradient surgeGradient = utility.buildBgLinearGradientForPriceSurge();
        BGLinearGradient typicalGradient = utility.buildBgLinearGradientForPriceTypical();

        // Then - Test all gradients have non-empty values
        assertFalse("Drop gradient start should not be empty", dropGradient.getStart().isEmpty());
        assertFalse("Drop gradient center should not be empty", dropGradient.getCenter().isEmpty());
        assertFalse("Drop gradient end should not be empty", dropGradient.getEnd().isEmpty());
        assertFalse("Drop gradient direction should not be empty", dropGradient.getDirection().isEmpty());

        assertFalse("Surge gradient start should not be empty", surgeGradient.getStart().isEmpty());
        assertFalse("Surge gradient center should not be empty", surgeGradient.getCenter().isEmpty());
        assertFalse("Surge gradient end should not be empty", surgeGradient.getEnd().isEmpty());
        assertFalse("Surge gradient direction should not be empty", surgeGradient.getDirection().isEmpty());

        assertFalse("Typical gradient start should not be empty", typicalGradient.getStart().isEmpty());
        assertFalse("Typical gradient center should not be empty", typicalGradient.getCenter().isEmpty());
        assertFalse("Typical gradient end should not be empty", typicalGradient.getEnd().isEmpty());
        assertFalse("Typical gradient direction should not be empty", typicalGradient.getDirection().isEmpty());
    }

    @Test
    public void testConsistencyAcrossMultipleCalls() {
        // When - Call each method multiple times
        BGLinearGradient dropGradient1 = utility.buildBgLinearGradientForPriceDrop();
        BGLinearGradient dropGradient2 = utility.buildBgLinearGradientForPriceDrop();

        BGLinearGradient surgeGradient1 = utility.buildBgLinearGradientForPriceSurge();
        BGLinearGradient surgeGradient2 = utility.buildBgLinearGradientForPriceSurge();

        BGLinearGradient typicalGradient1 = utility.buildBgLinearGradientForPriceTypical();
        BGLinearGradient typicalGradient2 = utility.buildBgLinearGradientForPriceTypical();

        // Then - Values should be consistent
        assertEquals("Drop gradient values should be consistent across calls",
                dropGradient1.getStart(), dropGradient2.getStart());

        assertEquals("Surge gradient values should be consistent across calls",
                surgeGradient1.getStart(), surgeGradient2.getStart());

        assertEquals("Typical gradient values should be consistent across calls",
                typicalGradient1.getStart(), typicalGradient2.getStart());
    }

    @Test
    public void isNoCostEmiApplicableExperimentEnabled_ReturnsFalseWhenMapIsNull() {
        boolean result = utility.isNoCostEmiApplicableExperimentEnabled(null);
        Assert.assertFalse(result);
    }

    @Test
    public void isNoCostEmiApplicableExperimentEnabled_ReturnsFalseWhenKeyNotPresent() {
        Map<String, String> expDataMap = new HashMap<>();
        boolean result = utility.isNoCostEmiApplicableExperimentEnabled(expDataMap);
        Assert.assertFalse(result);
    }

    @Test
    public void isNoCostEmiApplicableExperimentEnabled_ReturnsTrueWhenKeyIsTrue() {
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put(Constants.NO_COST_EMI_APPLICABLE, "true");
        boolean result = utility.isNoCostEmiApplicableExperimentEnabled(expDataMap);
        Assert.assertTrue(result);
    }

    @Test
    public void logRequestResponse_DoesNotThrowExceptions() {
        Object testObject = new Object();
        utility.logRequestResponse(testObject, "Test log");
        utility.logRequestResponse(null, "Test log with null object");
        // No assertions needed - we're just verifying it doesn't throw exceptions
    }

    @Test
    public void getUrlFromConfig_ReturnsNullForUnknownKey() {
        String result = utility.getUrlFromConfig("unknown-key");
        Assert.assertNull(result);
    }

    @Test
    public void getUrlFromConfig_ReturnsVegIconUrlForVegIcon() {
        ReflectionTestUtils.setField(utility, "vegIconUrl", "test-veg-icon-url");
        String result = utility.getUrlFromConfig(Constants.VEG_ICON);
        Assert.assertEquals("test-veg-icon-url", result);
    }

    @Test
    public void testBuildExtraAdultChildInclusion_ReturnsNullWhenConfigIsNull() {
        BookedInclusion result = utility.buildExtraAdultChildInclusion(null);
        Assert.assertNull(result);
    }

    @Test
    public void formatDateForPixelUrl_WithValidDate_ReturnsFormattedDate() {
        // Arrange
        String inputDate = "2023-05-15";

        // Act
        String result = Utility.formatDateForPixelUrl(inputDate);

        // Assert
        assertEquals("15/05/23", result);
    }

    @Test
    public void formatDateForPixelUrl_WithNull_ReturnsEmptyString() {
        // Act
        String result = Utility.formatDateForPixelUrl(null);

        // Assert
        assertEquals("", result);
    }

    @Test
    public void formatDateForPixelUrl_WithEmptyString_ReturnsEmptyString() {
        // Act
        String result = Utility.formatDateForPixelUrl("");

        // Assert
        assertEquals("", result);
    }

    @Test
    public void formatDateForPixelUrl_WithInvalidFormat_ReturnsEmptyString() {
        // Arrange
        String inputDate = "15-05-2023"; // Wrong format (should be yyyy-MM-dd)

        // Act
        String result = Utility.formatDateForPixelUrl(inputDate);

        // Assert
        assertEquals("", result);
    }

    @Test
    public void formatDateForPixelUrl_WithInvalidDate_ReturnsEmptyString() {
        // Arrange
        String inputDate = "2023-13-45"; // Invalid month and day

        // Act
        String result = Utility.formatDateForPixelUrl(inputDate);

        // Assert
        assertEquals("", result);
    }

    @Test
    public void formatDateForPixelUrl_WithSpecialCharacters_ReturnsEmptyString() {
        // Arrange
        String inputDate = "2023-05-15@"; // Special character

        // Act
        String result = Utility.formatDateForPixelUrl(inputDate);

        // Assert
        assertEquals("", result);
    }

    @Test
    public void formatDateForPixelUrl_WithLeapYearDate_ReturnsFormattedDate() {
        // Arrange
        String inputDate = "2024-02-29"; // Leap year date

        // Act
        String result = Utility.formatDateForPixelUrl(inputDate);

        // Assert
        assertEquals("29/02/24", result);
    }

    @Test
    public void formatDateForPixelUrl_WithBoundaryDates_ReturnsFormattedDate() {
        // Arrange
        String firstDayOfYear = "2023-01-01";
        String lastDayOfYear = "2023-12-31";

        // Act
        String resultFirstDay = Utility.formatDateForPixelUrl(firstDayOfYear);
        String resultLastDay = Utility.formatDateForPixelUrl(lastDayOfYear);

        // Assert
        assertEquals("01/01/23", resultFirstDay);
        assertEquals("31/12/23", resultLastDay);
    }

    @Test
    public void formatDateForPixelUrl_WithPaddedDates_ReturnsFormattedDate() {
        // Arrange - single digit month and day
        String inputDate = "2023-01-05";

        // Act
        String result = Utility.formatDateForPixelUrl(inputDate);

        // Assert
        assertEquals("05/01/23", result);
    }

    @Test
    public void returnsGreenColorForPriceDrop() {
        PriceVariationType type = PriceVariationType.DROP;
        String result = utility.getPriceColorForPriceDrop(type);
        assertEquals("#007E7D", result);
    }

    @Test
    public void returnsOrangeColorForPriceSurge() {
        PriceVariationType type = PriceVariationType.SURGE;
        String result = utility.getPriceColorForPriceDrop(type);
        assertEquals("#CF8100", result);
    }

    @Test
    public void returnsDefaultColorForNullType() {
        String result = utility.getPriceColorForPriceDrop(null);
        assertEquals(null, result); // Assuming default is for SURGE
    }

    @Test
    @DisplayName("Should set canTranslate to true when supplier code is in enabled list")
    public void testSetCanTranslateFlag_WhenSupplierCodeInEnabledList_ShouldSetTrue() {
        // Given
        List<String> enabledSupplierCodes = Arrays.asList("SUPPLIER_A", "SUPPLIER_B", "SUPPLIER_C");
        String supplierCode = "SUPPLIER_A1234";
        
        // When
        boolean canTranslate = Utility.getCanTranslateFlag(enabledSupplierCodes, supplierCode);
        
        // Then
        assertTrue(canTranslate);
    }
    
    @Test
    @DisplayName("Should set canTranslate to false when supplier code is not in enabled list")
    public void testSetCanTranslateFlag_WhenSupplierCodeNotInEnabledList_ShouldSetFalse() {
        // Given

        List<String> enabledSupplierCodes = Arrays.asList("SUPPLIER_A", "SUPPLIER_B", "SUPPLIER_C");
        String supplierCode = "SUPPLIER_D1234";
        
        // When
        boolean canTranslate = Utility.getCanTranslateFlag(enabledSupplierCodes, supplierCode);
        
        // Then
        assertFalse(canTranslate);
    }
    
    @Test
    @DisplayName("Should set canTranslate to false when enabled supplier codes list is empty")
    public void testSetCanTranslateFlag_WhenEnabledListIsEmpty_ShouldSetFalse() {
        // Given

        List<String> enabledSupplierCodes = Collections.emptyList();
        String supplierCode = "SUPPLIER_A1234";
        
        // When
        boolean canTranslate = Utility.getCanTranslateFlag(enabledSupplierCodes, supplierCode);
        
        // Then
        assertFalse(canTranslate);
    }
    
    @Test
    @DisplayName("Should set canTranslate to false when enabled supplier codes list is null")
    public void testSetCanTranslateFlag_WhenEnabledListIsNull_ShouldSetFalse() {
        // Given

        List<String> enabledSupplierCodes = null;
        String supplierCode = "SUPPLIER_A1234";
        
        // When
        boolean canTranslate = Utility.getCanTranslateFlag(enabledSupplierCodes, supplierCode);
        
        // Then
        assertFalse(canTranslate);
    }
    
    @Test
    @DisplayName("Should set canTranslate to false when supplier code is null")
    public void testSetCanTranslateFlag_WhenSupplierCodeIsNull_ShouldSetFalse() {
        // Given

        List<String> enabledSupplierCodes = Arrays.asList("SUPPLIER_A", "SUPPLIER_B");
        String supplierCode = null;
        
        // When
        boolean canTranslate = Utility.getCanTranslateFlag(enabledSupplierCodes, supplierCode);
        
        // Then
        assertFalse(canTranslate);
    }
    
    @Test
    @DisplayName("Should set canTranslate to false when supplier code is empty string")
    public void testSetCanTranslateFlag_WhenSupplierCodeIsEmpty_ShouldSetFalse() {
        // Given

        List<String> enabledSupplierCodes = Arrays.asList("SUPPLIER_A", "SUPPLIER_B");
        String supplierCode = "";
        
        // When
        boolean canTranslate = Utility.getCanTranslateFlag(enabledSupplierCodes, supplierCode);
        
        // Then
        assertFalse(canTranslate);
    }
    
    @Test
    @DisplayName("Should set canTranslate to true for case-sensitive match")
    public void testSetCanTranslateFlag_CaseSensitiveMatch_ShouldSetTrue() {
        // Given

        List<String> enabledSupplierCodes = Arrays.asList("SUPPLIER_A", "supplier_A", "SUPPLIER_B");
        String supplierCode = "supplier_A1234";
        
        // When
        boolean canTranslate = Utility.getCanTranslateFlag(enabledSupplierCodes, supplierCode);
        
        // Then
        assertTrue(canTranslate);
    }
    
    @Test
    @DisplayName("Should set canTranslate to false for case mismatch")
    public void testSetCanTranslateFlag_CaseMismatch_ShouldSetFalse() {
        // Given

        List<String> enabledSupplierCodes = Arrays.asList("SUPPLIER_A", "SUPPLIER_B");
        String supplierCode = "supplier_a1234";
        
        // When
        boolean canTranslate = Utility.getCanTranslateFlag(enabledSupplierCodes, supplierCode);
        
        // Then
        assertFalse(canTranslate);
    }

    @Test
    public void should_ReturnFalse_When_RequestIdIsNull_And_RearchFlagIsFalse() {
        // Arrange
        boolean rearchFlag = false;
        String requestId = null;
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put(ExperimentKeys.EXP_ORCHESTRATOR_DETAIL_V2.getKey(), "true");

        // Act
        boolean result = utility.isDetailPageRearchFlow(rearchFlag, requestId, expDataMap);

        // Assert
        assertFalse("Should return false when rearchFlag is false", result);
    }

    @Test
    public void should_ReturnFalse_When_RequestIdIsEmpty_And_RearchFlagIsFalse() {
        // Arrange
        boolean rearchFlag = false;
        String requestId = "";
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put(ExperimentKeys.EXP_ORCHESTRATOR_DETAIL_V2.getKey(), "true");

        // Act
        boolean result = utility.isDetailPageRearchFlow(rearchFlag, requestId, expDataMap);

        // Assert
        assertFalse("Should return false when rearchFlag is false", result);
    }

    @Test
    public void should_ReturnTrue_When_RequestIdIsNull_And_RearchFlagIsTrue_And_OrchV2IsTrue() {
        // Arrange
        boolean rearchFlag = true;
        String requestId = null;
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put(ExperimentKeys.EXP_ORCHESTRATOR_DETAIL_V2.getKey(), "true");

        // Act
        boolean result = utility.isDetailPageRearchFlow(rearchFlag, requestId, expDataMap);

        // Assert
        assertTrue("Should return true when rearchFlag is true and orchV2 is not 'false'", result);
    }

    @Test
    public void should_ReturnFalse_When_RequestIdIsNull_And_RearchFlagIsTrue_And_OrchV2IsFalse() {
        // Arrange
        boolean rearchFlag = true;
        String requestId = null;
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put(ExperimentKeys.EXP_ORCHESTRATOR_DETAIL_V2.getKey(), Constants.FALSE);

        // Act
        boolean result = utility.isDetailPageRearchFlow(rearchFlag, requestId, expDataMap);

        // Assert
        assertFalse("Should return false when rearchFlag is true but orchV2 is 'false'", result);
    }

    @Test
    public void should_ReturnFalse_When_RequestIdIsNull_And_RearchFlagIsTrue_And_OrchV2IsNull() {
        // Arrange
        boolean rearchFlag = true;
        String requestId = null;
        Map<String, String> expDataMap = new HashMap<>();
        // orchV2 is not present in map

        // Act
        boolean result = utility.isDetailPageRearchFlow(rearchFlag, requestId, expDataMap);

        // Assert
        assertFalse("Should return false when rearchFlag is true and orchV2 exp is null", result);
    }

    @Test
    public void should_ReturnTrue_When_RequestIdDoesNotStartWithCG_And_RearchFlagIsTrue_And_OrchV2IsNotFalse() {
        // Arrange
        boolean rearchFlag = true;
        String requestId = "SOME_OTHER_REQUEST_ID";
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put(ExperimentKeys.EXP_ORCHESTRATOR_DETAIL_V2.getKey(), "true");

        // Act
        boolean result = utility.isDetailPageRearchFlow(rearchFlag, requestId, expDataMap);

        // Assert
        assertTrue("Should return true when requestId doesn't start with CG_ and conditions are met", result);
    }

    @Test
    public void should_ReturnFalse_When_RequestIdDoesNotStartWithCG_And_RearchFlagIsTrue_And_OrchV2IsFalse() {
        // Arrange
        boolean rearchFlag = true;
        String requestId = "RANDOM_ID";
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put(ExperimentKeys.EXP_ORCHESTRATOR_DETAIL_V2.getKey(), Constants.FALSE);

        // Act
        boolean result = utility.isDetailPageRearchFlow(rearchFlag, requestId, expDataMap);

        // Assert
        assertFalse("Should return false when requestId doesn't start with CG_ and orchV2 is false", result);
    }

    @Test
    public void should_ReturnTrue_When_RequestIdStartsWithCG_QA_ContainsOrchV2_RearchFlagTrue_And_OrchDetailRedesignTrue() {
        // Arrange
        boolean rearchFlag = true;
        String requestId = "CG_QA_OrchV2_REQUEST";
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put(ExperimentKeys.EXP_ORCHESTRATOR_DETAIL_V2.getKey(), Constants.TRUE);

        // Act
        boolean result = utility.isDetailPageRearchFlow(rearchFlag, requestId, expDataMap);

        // Assert
        assertTrue("Should return true when all conditions are met for CG_QA OrchV2 request", result);
    }

//    @Test
//    public void should_ReturnFalse_When_RequestIdStartsWithCG_QA_ContainsOrchV2_But_RearchFlagFalse() {
//        // Arrange
//        boolean rearchFlag = false;
//        String requestId = "CG_QA_OrchV2_REQUEST";
//        Map<String, String> expDataMap = new HashMap<>();
//        expDataMap.put(ExperimentKeys.EXP_ORCHESTRATOR_DETAIL_V2.getKey(), Constants.TRUE);
//
//        // Act
//        boolean result = utility.isDetailPageRearchFlow(rearchFlag, requestId, expDataMap);
//
//        // Assert
//        assertFalse("Should return false when rearchFlag is false even with OrchV2 request", result);
//    }

    @Test
    public void should_ReturnFalse_When_RequestIdStartsWithCG_QA_ContainsOrchV2_But_OrchDetailRedesignNotTrue() {
        // Arrange
        boolean rearchFlag = true;
        String requestId = "CG_QA_OrchV2_REQUEST";
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put(ExperimentKeys.EXP_ORCHESTRATOR_DETAIL_V2.getKey(), "false");

        // Act
        boolean result = utility.isDetailPageRearchFlow(rearchFlag, requestId, expDataMap);

        // Assert
        assertTrue("Should return false when OrchDetailRedesign experiment is not true", result);
    }

//    @Test
//    public void should_ReturnFalse_When_RequestIdStartsWithCG_QA_ContainsOrchV2_But_OrchDetailRedesignIsNull() {
//        // Arrange
//        boolean rearchFlag = true;
//        String requestId = "CG_QA_OrchV2_REQUEST";
//        Map<String, String> expDataMap = new HashMap<>();
//        // ExperimentKeys.EXP_ORCHESTRATOR_DETAIL_V2.getKey() is not present in map
//
//        // Act
//        boolean result = utility.isDetailPageRearchFlow(rearchFlag, requestId, expDataMap);
//
//        // Assert
//        assertFalse("Should return false when OrchDetailRedesign experiment is null", result);
//    }
//
    // Test cases for getBookedInclusions method
    @Test
    public void testGetBookedInclusions_WithValidInclusions_ShouldReturnBookedInclusionList() {
        // Given
        List<Inclusion> experiencesInclusion = createTestInclusions();

        // When
        List<BookedInclusion> result = invokeGetBookedInclusions(experiencesInclusion);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

        BookedInclusion firstInclusion = result.get(0);
        assertEquals("EXPERIENCE_001", firstInclusion.getCode());
        assertEquals("EXPERIENCE_001", firstInclusion.getText());
        assertEquals("Spa Access", firstInclusion.getSubText());
        assertEquals("100", firstInclusion.getAmount());
        assertEquals("http://example.com/spa-icon.png", firstInclusion.getIconUrl());
        assertEquals("Premium", firstInclusion.getCategory());
        assertEquals(INCLUSION_TYPE_EXPERIENCES, firstInclusion.getType());

        BookedInclusion secondInclusion = result.get(1);
        assertEquals("EXPERIENCE_002", secondInclusion.getCode());
        assertEquals("EXPERIENCE_002", secondInclusion.getText());
        assertEquals("Breakfast", secondInclusion.getSubText());
        assertEquals("50", secondInclusion.getAmount());
        assertEquals("http://example.com/breakfast-icon.png", secondInclusion.getIconUrl());
        assertEquals("Meal", secondInclusion.getCategory());
        assertEquals(INCLUSION_TYPE_EXPERIENCES, secondInclusion.getType());
    }

    @Test
    public void testGetBookedInclusions_WithEmptyList_ShouldReturnEmptyList() {
        // Given
        List<Inclusion> experiencesInclusion = new ArrayList<>();

        // When
        List<BookedInclusion> result = invokeGetBookedInclusions(experiencesInclusion);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetBookedInclusions_WithNullList_ShouldReturnEmptyList() {
        // When
        List<BookedInclusion> result = invokeGetBookedInclusions(null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetBookedInclusions_WithNullFields_ShouldHandleNullValues() {
        // Given
        List<Inclusion> experiencesInclusion = new ArrayList<>();
        Inclusion inclusion = new Inclusion();
        inclusion.setCode(null);
        inclusion.setAmount(null);
        inclusion.setValue(null);
        inclusion.setIconUrl(null);
        inclusion.setCategory(null);
        inclusion.setType(null);
        experiencesInclusion.add(inclusion);

        // When
        List<BookedInclusion> result = invokeGetBookedInclusions(experiencesInclusion);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());

        BookedInclusion bookedInclusion = result.get(0);
        assertNull(bookedInclusion.getCode());
        assertNull(bookedInclusion.getText());
        assertNull(bookedInclusion.getSubText());
        assertNull(bookedInclusion.getAmount());
        assertNull(bookedInclusion.getIconUrl());
        assertNull(bookedInclusion.getCategory());
        assertNull(bookedInclusion.getType());
    }

    // Test cases for getExperiencesInclusions method
    @Test
    public void testGetExperiencesInclusions_WithValidExperiences_ShouldReturnFilteredList() {
        // Given
        List<Inclusion> inclusions = createMixedInclusions();

        // When
        List<Inclusion> result = utility.getExperiencesInclusions(inclusions);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

        for (Inclusion inclusion : result) {
            assertEquals(INCLUSION_TYPE_EXPERIENCES, inclusion.getType());
        }
    }

    @Test
    public void testGetExperiencesInclusions_WithNoExperiences_ShouldReturnEmptyList() {
        // Given
        List<Inclusion> inclusions = new ArrayList<>();
        Inclusion inclusion = new Inclusion();
        inclusion.setType("Regular");
        inclusions.add(inclusion);

        // When
        List<Inclusion> result = utility.getExperiencesInclusions(inclusions);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetExperiencesInclusions_WithNullList_ShouldReturnEmptyList() {
        // When
        List<Inclusion> result = utility.getExperiencesInclusions(null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetExperiencesInclusions_WithEmptyList_ShouldReturnEmptyList() {
        // Given
        List<Inclusion> inclusions = new ArrayList<>();

        // When
        List<Inclusion> result = utility.getExperiencesInclusions(inclusions);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetExperiencesInclusions_WithNullAndEmptyTypes_ShouldReturnEmptyList() {
        // Given
        List<Inclusion> inclusions = new ArrayList<>();

        Inclusion nullTypeInclusion = new Inclusion();
        nullTypeInclusion.setType(null);
        inclusions.add(nullTypeInclusion);

        Inclusion emptyTypeInclusion = new Inclusion();
        emptyTypeInclusion.setType("");
        inclusions.add(emptyTypeInclusion);

        // When
        List<Inclusion> result = utility.getExperiencesInclusions(inclusions);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetExperiencesInclusions_WithCaseInsensitiveMatch_ShouldReturnFilteredList() {
        // Given
        List<Inclusion> inclusions = new ArrayList<>();

        Inclusion lowercaseInclusion = new Inclusion();
        lowercaseInclusion.setType(INCLUSION_TYPE_EXPERIENCES);
        inclusions.add(lowercaseInclusion);

        Inclusion uppercaseInclusion = new Inclusion();
        uppercaseInclusion.setType(INCLUSION_TYPE_EXPERIENCES);
        inclusions.add(uppercaseInclusion);

        Inclusion mixedCaseInclusion = new Inclusion();
        mixedCaseInclusion.setType(INCLUSION_TYPE_EXPERIENCES);
        inclusions.add(mixedCaseInclusion);

        // When
        List<Inclusion> result = utility.getExperiencesInclusions(inclusions);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
    }

    // Test cases for createExperienceInclusionCard method
    @Test
    public void testCreateExperienceInclusionCard_WithValidDataAndExperimentOn_ShouldReturnExperienceInclusions() {
        // Given
        List<Inclusion> inclusions = createTestInclusions();
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put("SPE", "true");

        when(polyglotService.getTranslatedData("EXPERIENCES_INCLUSION_TITLE")).thenReturn("Premium Experiences");

        // When
        ExperienceInclusions result = utility.createExperienceInclusionCard(inclusions, expDataMap);

        // Then
        assertNotNull(result);
        assertEquals("Premium Experiences", result.getTitle());
        assertNotNull(result.getInclusions());
        assertEquals(2, result.getInclusions().size());

        verify(polyglotService, times(1)).getTranslatedData("EXPERIENCES_INCLUSION_TITLE");
    }

    @Test
    public void testCreateExperienceInclusionCard_WithExperimentOff_ShouldReturnNull() {
        // Given
        List<Inclusion> inclusions = createTestInclusions();
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put("SPE", "false");

        // When
        ExperienceInclusions result = utility.createExperienceInclusionCard(inclusions, expDataMap);

        // Then
        assertNull(result);
        verify(polyglotService, never()).getTranslatedData(anyString());
    }

    @Test
    public void testCreateExperienceInclusionCard_WithNullExpDataMap_ShouldReturnNull() {
        // Given
        List<Inclusion> inclusions = createTestInclusions();

        // When
        ExperienceInclusions result = utility.createExperienceInclusionCard(inclusions, null);

        // Then
        assertNull(result);
        verify(polyglotService, never()).getTranslatedData(anyString());
    }

    @Test
    public void testCreateExperienceInclusionCard_WithEmptyExpDataMap_ShouldReturnNull() {
        // Given
        List<Inclusion> inclusions = createTestInclusions();
        Map<String, String> expDataMap = new HashMap<>();

        // When
        ExperienceInclusions result = utility.createExperienceInclusionCard(inclusions, expDataMap);

        // Then
        assertNull(result);
        verify(polyglotService, never()).getTranslatedData(anyString());
    }

    @Test
    public void testCreateExperienceInclusionCard_WithNoExperienceInclusions_ShouldReturnNull() {
        // Given
        List<Inclusion> inclusions = new ArrayList<>();
        Inclusion regularInclusion = new Inclusion();
        regularInclusion.setType("Regular");
        inclusions.add(regularInclusion);

        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put("SPE", "true");

        // When
        ExperienceInclusions result = utility.createExperienceInclusionCard(inclusions, expDataMap);

        // Then
        assertNull(result);
        verify(polyglotService, never()).getTranslatedData(anyString());
    }

    @Test
    public void testCreateExperienceInclusionCard_WithNullInclusions_ShouldReturnNull() {
        // Given
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put("SPE", "true");

        // When
        ExperienceInclusions result = utility.createExperienceInclusionCard(null, expDataMap);

        // Then
        assertNull(result);
        verify(polyglotService, never()).getTranslatedData(anyString());
    }

    @Test
    public void testCreateExperienceInclusionCard_WithEmptyInclusions_ShouldReturnNull() {
        // Given
        List<Inclusion> inclusions = new ArrayList<>();
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put("SPE", "true");

        // When
        ExperienceInclusions result = utility.createExperienceInclusionCard(inclusions, expDataMap);

        // Then
        assertNull(result);
        verify(polyglotService, never()).getTranslatedData(anyString());
    }

    @Test
    public void testCreateExperienceInclusionCard_WithNullTranslatedTitle_ShouldStillReturnResult() {
        // Given
        List<Inclusion> inclusions = createTestInclusions();
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put("SPE", "true");

        when(polyglotService.getTranslatedData("EXPERIENCES_INCLUSION_TITLE")).thenReturn(null);

        // When
        ExperienceInclusions result = utility.createExperienceInclusionCard(inclusions, expDataMap);

        // Then
        assertNotNull(result);
        assertNull(result.getTitle());
        assertNotNull(result.getInclusions());
        assertEquals(2, result.getInclusions().size());
    }

    // Test cases for transformInclusions method
    @Test
    public void testTransformInclusions_WithValidData_ShouldReturnTransformedInclusions() {
        // Given
        List<MealPlan> mealPlan = createTestMealPlans();
        List<Inclusion> inclusionList = createMixedInclusions();
        Map<String, String> mealPlanMap = createMealPlanMap();
        Map<String, String> experimentDataMap = new HashMap<>();

        // When
        List<BookedInclusion> result = utility.transformInclusions(
            mealPlan, inclusionList, mealPlanMap, "SUPPLIER1", 1, null,
            experimentDataMap, false, "Free child text", "IHG", null,
            null, "IN", false, IconType.DEFAULT, false, false
        );

        // Then
        assertNotNull(result);
        assertTrue(result.size() > 0);

        // Should not include experience type inclusions
        for (BookedInclusion inclusion : result) {
            assertNotEquals("PremiumExperience", inclusion.getType());
        }
    }

    @Test
    public void testTransformInclusions_WithNullInclusions_ShouldReturnListWithMealPlan() {
        // Given
        List<MealPlan> mealPlan = createTestMealPlans();
        Map<String, String> mealPlanMap = createMealPlanMap();
        Map<String, String> experimentDataMap = new HashMap<>();

        when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated Text");

        // When
        List<BookedInclusion> result = utility.transformInclusions(
            mealPlan, null, mealPlanMap, "SUPPLIER1", 1, null,
            experimentDataMap, false, null, "IHG", null,
            null, "IN", false, IconType.DEFAULT, false, false
        );

        // Then
        assertNotNull(result);
        assertTrue(result.size() > 0);
    }

    @Test
    public void testTransformInclusions_WithEmptyInclusions_ShouldReturnListWithMealPlan() {
        // Given
        List<MealPlan> mealPlan = createTestMealPlans();
        List<Inclusion> inclusionList = new ArrayList<>();
        Map<String, String> mealPlanMap = createMealPlanMap();
        Map<String, String> experimentDataMap = new HashMap<>();

        when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated Text");

        // When
        List<BookedInclusion> result = utility.transformInclusions(
            mealPlan, inclusionList, mealPlanMap, "SUPPLIER1", 1, null,
            experimentDataMap, false, null, "IHG", null,
            null, "IN", false, IconType.DEFAULT, false, false
        );

        // Then
        assertNotNull(result);
        assertTrue(result.size() > 0);
    }

    @Test
    public void testTransformInclusions_WithInclusionsHavingEmptyValue_ShouldSkipThoseInclusions() {
        // Given
        List<MealPlan> mealPlan = createTestMealPlans();
        List<Inclusion> inclusionList = new ArrayList<>();

        Inclusion validInclusion = new Inclusion();
        validInclusion.setType("Regular");
        validInclusion.setValue("Valid value");
        validInclusion.setCode("VALID");
        inclusionList.add(validInclusion);

        Inclusion emptyValueInclusion = new Inclusion();
        emptyValueInclusion.setType("Regular");
        emptyValueInclusion.setValue("");
        emptyValueInclusion.setCode("EMPTY");
        inclusionList.add(emptyValueInclusion);

        Inclusion nullValueInclusion = new Inclusion();
        nullValueInclusion.setType("Regular");
        nullValueInclusion.setValue(null);
        nullValueInclusion.setCode("NULL");
        inclusionList.add(nullValueInclusion);

        Map<String, String> mealPlanMap = createMealPlanMap();
        Map<String, String> experimentDataMap = new HashMap<>();

        // When
        List<BookedInclusion> result = utility.transformInclusions(
            mealPlan, inclusionList, mealPlanMap, "SUPPLIER1", 1, null,
            experimentDataMap, false, null, "IHG", null,
            null, "IN", false, IconType.DEFAULT, false, false
        );

        // Then
        assertNotNull(result);

        // Should only include valid inclusion (not empty/null value ones)
        boolean hasValidInclusion = result.stream()
            .anyMatch(inclusion -> "VALID".equals(inclusion.getCode()));
        assertTrue(hasValidInclusion);

        boolean hasEmptyInclusion = result.stream()
            .anyMatch(inclusion -> "EMPTY".equals(inclusion.getCode()));
        assertFalse(hasEmptyInclusion);

        boolean hasNullInclusion = result.stream()
            .anyMatch(inclusion -> "NULL".equals(inclusion.getCode()));
        assertFalse(hasNullInclusion);
    }

    @Test
    public void testTransformInclusions_WithFreeChildText_ShouldIncludeFreeChildInclusion() {
        // Given
        List<MealPlan> mealPlan = createTestMealPlans();
        List<Inclusion> inclusionList = new ArrayList<>();
        Map<String, String> mealPlanMap = createMealPlanMap();
        Map<String, String> experimentDataMap = new HashMap<>();
        String freeChildText = "Free child up to 12 years";

        when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated Text");

        // When
        List<BookedInclusion> result = utility.transformInclusions(
            mealPlan, inclusionList, mealPlanMap, "SUPPLIER1", 1, null,
            experimentDataMap, false, freeChildText, "IHG", null,
            null, "IN", false, IconType.DEFAULT, false, false
        );

        // Then
        assertNotNull(result);
        assertTrue(result.size() > 0);

        // Should include free child inclusion at the beginning
        boolean hasFreeChildInclusion = result.stream()
            .anyMatch(inclusion -> freeChildText.equals(inclusion.getText()));
        assertTrue(hasFreeChildInclusion);
    }

    @Test
    public void testTransformInclusions_WithExtraGuestDetail_ShouldIncludeExtraBedText() {
        // Given
        List<MealPlan> mealPlan = createTestMealPlans();
        List<Inclusion> inclusionList = new ArrayList<>();
        Map<String, String> mealPlanMap = createMealPlanMap();
        Map<String, String> experimentDataMap = new HashMap<>();

        ExtraGuestDetail extraGuestDetail = new ExtraGuestDetail();
        extraGuestDetail.setReviewPageExtraBedText("Extra bed available");

        when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated Text");

        // When
        List<BookedInclusion> result = utility.transformInclusions(
            mealPlan, inclusionList, mealPlanMap, "SUPPLIER1", 1, extraGuestDetail,
            experimentDataMap, false, null, "IHG", null,
            null, "IN", false, IconType.DEFAULT, false, false
        );

        // Then
        assertNotNull(result);
        assertTrue(result.size() > 0);

        // Should include extra bed text
        boolean hasExtraBedInclusion = result.stream()
            .anyMatch(inclusion -> "Extra bed available".equals(inclusion.getText()));
        assertTrue(hasExtraBedInclusion);
    }

    @Test
    public void testTransformInclusions_WithAltAcco_ShouldNotIncludeExtraBedText() {
        // Given
        List<MealPlan> mealPlan = createTestMealPlans();
        List<Inclusion> inclusionList = new ArrayList<>();
        Map<String, String> mealPlanMap = createMealPlanMap();
        Map<String, String> experimentDataMap = new HashMap<>();

        ExtraGuestDetail extraGuestDetail = new ExtraGuestDetail();
        extraGuestDetail.setReviewPageExtraBedText("Extra bed available");

        when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated Text");

        // When
        List<BookedInclusion> result = utility.transformInclusions(
            mealPlan, inclusionList, mealPlanMap, "SUPPLIER1", 1, extraGuestDetail,
            experimentDataMap, true, null, "IHG", null,
            null, "IN", false, IconType.DEFAULT, false, false
        );

        // Then
        assertNotNull(result);

        // Should NOT include extra bed text for alt acco
        boolean hasExtraBedInclusion = result.stream()
            .anyMatch(inclusion -> "Extra bed available".equals(inclusion.getText()));
        assertFalse(hasExtraBedInclusion);
    }

    @Test
    public void testTransformInclusions_WithRoomOnlyMealPlan_ShouldIncludeNoMealInclusion() {
        // Given
        List<MealPlan> mealPlan = new ArrayList<>();
        MealPlan roomOnlyMealPlan = new MealPlan();
        roomOnlyMealPlan.setCode("EP");
        roomOnlyMealPlan.setValue("Room Only");
        mealPlan.add(roomOnlyMealPlan);

        List<Inclusion> inclusionList = new ArrayList<>();
        Map<String, String> mealPlanMap = createMealPlanMap();
        mealPlanMap.put("EP", "ROOM_ONLY_TEXT");

        Map<String, String> experimentDataMap = new HashMap<>();

        when(polyglotService.getTranslatedData("ROOM_ONLY_TEXT")).thenReturn("Room Only");

        // When
        List<BookedInclusion> result = utility.transformInclusions(
            mealPlan, inclusionList, mealPlanMap, "SUPPLIER1", 1, null,
            experimentDataMap, false, null, "IHG", null,
            null, "IN", false, IconType.DEFAULT, false, false
        );

        // Then
        assertNotNull(result);
        assertTrue(result.size() > 0);

        // Should include room only inclusion
        boolean hasRoomOnlyInclusion = result.stream()
            .anyMatch(inclusion -> "Room Only".equals(inclusion.getText()));
        assertTrue(hasRoomOnlyInclusion);
    }

    @Test
    public void testTransformInclusions_WithExtraAdultChildInclusionEnabled_ShouldIncludeExtraAdultChildInclusion() {
        // Given
        ReflectionTestUtils.setField(utility, "extraAdultChildInclusionConfig", createExtraAdultChildInclusionConfig());

        List<MealPlan> mealPlan = createTestMealPlans();
        List<Inclusion> inclusionList = new ArrayList<>();
        Map<String, String> mealPlanMap = createMealPlanMap();
        Map<String, String> experimentDataMap = new HashMap<>();
        experimentDataMap.put("EXP_EXTRA_ADULT_CHILD_INCLUSION", "true");

        when(polyglotService.getTranslatedData(anyString())).thenReturn("Includes extra adult/child");

        // When
        List<BookedInclusion> result = utility.transformInclusions(
            mealPlan, inclusionList, mealPlanMap, "SUPPLIER1", 1, null,
            experimentDataMap, false, null, "IHG", null,
            null, "IN", false, IconType.DEFAULT, false, true
        );

        // Then
        assertNotNull(result);
        assertTrue(result.size() > 0);

        // Should include extra adult child inclusion
        boolean hasExtraAdultChildInclusion = result.stream()
            .anyMatch(inclusion -> "Includes extra adult/child".equals(inclusion.getText()));
        assertTrue(hasExtraAdultChildInclusion);
    }

    // Helper methods for creating test data
    private List<Inclusion> createTestInclusions() {
        List<Inclusion> inclusions = new ArrayList<>();

        Inclusion inclusion1 = new Inclusion();
        inclusion1.setCode("EXPERIENCE_001");
        inclusion1.setValue("Spa Access");
        inclusion1.setAmount("100");
        inclusion1.setIconUrl("http://example.com/spa-icon.png");
        inclusion1.setCategory("Premium");
        inclusion1.setType(INCLUSION_TYPE_EXPERIENCES);
        inclusions.add(inclusion1);

        Inclusion inclusion2 = new Inclusion();
        inclusion2.setCode("EXPERIENCE_002");
        inclusion2.setValue("Breakfast");
        inclusion2.setAmount("50");
        inclusion2.setIconUrl("http://example.com/breakfast-icon.png");
        inclusion2.setCategory("Meal");
        inclusion2.setType(INCLUSION_TYPE_EXPERIENCES);
        inclusions.add(inclusion2);

        return inclusions;
    }

    private List<Inclusion> createMixedInclusions() {
        List<Inclusion> inclusions = new ArrayList<>();

        // Add experience inclusions
        inclusions.addAll(createTestInclusions());

        // Add regular inclusions
        Inclusion regularInclusion = new Inclusion();
        regularInclusion.setCode("REGULAR_001");
        regularInclusion.setValue("WiFi Access");
        regularInclusion.setType("Regular");
        inclusions.add(regularInclusion);

        Inclusion mealInclusion = new Inclusion();
        mealInclusion.setCode("MEAL_001");
        mealInclusion.setValue("Breakfast Included");
        mealInclusion.setType("Meal");
        inclusions.add(mealInclusion);

        return inclusions;
    }

    private List<MealPlan> createTestMealPlans() {
        List<MealPlan> mealPlans = new ArrayList<>();

        MealPlan mealPlan = new MealPlan();
        mealPlan.setCode("CP");
        mealPlan.setValue("Continental Plan");
        mealPlans.add(mealPlan);

        return mealPlans;
    }

    private Map<String, String> createMealPlanMap() {
        Map<String, String> mealPlanMap = new HashMap<>();
        mealPlanMap.put("CP", "CONTINENTAL_PLAN");
        mealPlanMap.put("EP", "ROOM_ONLY_TEXT");
        mealPlanMap.put("MAP", "MODIFIED_AMERICAN_PLAN");
        mealPlanMap.put("AP", "AMERICAN_PLAN");
        return mealPlanMap;
    }

    private ExtraAdultChildInclusionConfig createExtraAdultChildInclusionConfig() {
        ExtraAdultChildInclusionConfig config = new ExtraAdultChildInclusionConfig();
        config.setEnabled(true);
        config.setIconUrl("http://example.com/extra-icon.png");
        config.setStyleClasses(Arrays.asList("extra-adult-child-style"));
        return config;
    }

    // Helper method to invoke private static method getBookedInclusions using reflection
    private List<BookedInclusion> invokeGetBookedInclusions(List<Inclusion> experiencesInclusion) {
        try {
            Method method = Utility.class.getDeclaredMethod("getBookedInclusions", List.class);
            method.setAccessible(true);

            if (experiencesInclusion == null) {
                return new ArrayList<>();
            }

            return (List<BookedInclusion>) method.invoke(null, experiencesInclusion);
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke getBookedInclusions method", e);
        }
    }
}
