package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.constants.MetricConstant;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class MetricAspectTest {

    private MetricAspect metricAspect;

    @Mock
    private com.mmt.lib.MetricManager metricManager;

    @Before
    public void setUp(){
        metricAspect = new MetricAspect();
        ReflectionTestUtils.setField(metricAspect, "metricManager", metricManager);
    }

    @Test
    public void testGetControllerRequestStringWoSpecialCharacters() {
        String input = "/cg/detail/{hotelId}/ratings";
        String expected = "/cg/detail/HOTELID/ratings";
        String actual = ReflectionTestUtils.invokeMethod(metricAspect, "getControllerRequestStringWoSpecialCharacters", input);
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void testAddToCounterCacheCreatesTags() {
        // This method internally calls metricManager.logCounter with generated tag map.
        metricAspect.addToCounterCache("eng", "IN", "CACHE_HIT");

        ArgumentCaptor<Map> tagCaptor = ArgumentCaptor.forClass(Map.class);
        Mockito.verify(metricManager, Mockito.times(1)).logCounter(tagCaptor.capture(), Mockito.anyLong());

        Map<String,String> tags = tagCaptor.getValue();
        // Basic sanity checks
        Assert.assertEquals("CACHE_HIT", tags.get(MetricConstant.METRIC_TYPE));
        Assert.assertEquals("IN", tags.get(MetricConstant.TAG_REGION));
        Assert.assertEquals("eng", tags.get(MetricConstant.TAG_LANGUAGE));
    }
} 