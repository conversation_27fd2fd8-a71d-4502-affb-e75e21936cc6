package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.enums.Clients;
import com.mmt.hotels.clientgateway.exception.AuthenticationException;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ValidationException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.fail;

@RunWith(MockitoJUnitRunner.class)
public class CustomValidatorTest {

    @Test
    public void testValidate_ValidInputs() throws ClientGatewayException {
        // Arrange
        String tid = "transaction123";
        String client = Clients.values()[0].name(); // Get a valid client name
        
        // Act
        try {
            CustomValidator.validate(tid, client);
            // Assert: No exception means test passes
        } catch (ClientGatewayException e) {
            fail("Should not throw exception for valid inputs");
        }
    }

    @Test(expected = ValidationException.class)
    public void testValidate_EmptyTid() throws ClientGatewayException {
        // Arrange
        String tid = "";
        String client = Clients.values()[0].name();
        
        // Act
        CustomValidator.validate(tid, client);
        
        // Assert: Exception expected
    }

    @Test(expected = ValidationException.class)
    public void testValidate_NullTid() throws ClientGatewayException {
        // Arrange
        String tid = null;
        String client = Clients.values()[0].name();
        
        // Act
        CustomValidator.validate(tid, client);
        
        // Assert: Exception expected
    }

    @Test(expected = ValidationException.class)
    public void testValidate_EmptyClient() throws ClientGatewayException {
        // Arrange
        String tid = "transaction123";
        String client = "";
        
        // Act
        CustomValidator.validate(tid, client);
        
        // Assert: Exception expected
    }

    @Test(expected = ValidationException.class)
    public void testValidate_NullClient() throws ClientGatewayException {
        // Arrange
        String tid = "transaction123";
        String client = null;
        
        // Act
        CustomValidator.validate(tid, client);
        
        // Assert: Exception expected
    }

    @Test(expected = AuthenticationException.class)
    public void testValidate_InvalidClient() throws ClientGatewayException {
        // Arrange
        String tid = "transaction123";
        String client = "INVALID_CLIENT_NAME";
        
        // Act
        CustomValidator.validate(tid, client);
        
        // Assert: Exception expected
    }
}
