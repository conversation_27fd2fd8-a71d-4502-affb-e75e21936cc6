package com.mmt.hotels.clientgateway.util;

import org.junit.Assert;
import org.junit.Test;

public class RequestHandlerTest {

    @Test
    public void testEffectiveCorrelationKey_prefersRequestId() {
        RequestHandler handler = new RequestHandler();
        String result = handler.effectiveCorrelationKey("REQ123", "CORR456");
        Assert.assertEquals("REQ123", result);
    }

    @Test
    public void testEffectiveCorrelationKey_fallbackToCorrelationKey() {
        RequestHandler handler = new RequestHandler();
        String result = handler.effectiveCorrelationKey("", "CORR456");
        Assert.assertEquals("CORR456", result);
    }
} 