package com.mmt.hotels.clientgateway.helpers;

import com.gommt.hotels.orchestrator.detail.enums.*;
import com.gommt.hotels.orchestrator.detail.model.objects.BookingDevice;
import com.gommt.hotels.orchestrator.detail.model.request.DetailRequest;
import com.gommt.hotels.orchestrator.detail.model.request.common.ClientDetails;

import com.gommt.hotels.orchestrator.detail.model.state.RequestDetails;
import com.gommt.hotels.orchestrator.detail.model.state.UserDetails;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.UserLocation;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.Assert.*;

import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anyMap;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class OrchAlternatePriceHelperTest {

    @InjectMocks
    private OrchAlternatePriceHelper orchAlternatePriceHelper;

    @Mock
    private Utility utility;

    private PriceByHotelsRequestBody cgRequest;
    private Map<String, String[]> parameterMap;
    private Map<String, String> headers;
    private CommonModifierResponse commonModifierResponse;

    @Before
    public void setUp() {
        // Initialize test data
        cgRequest = new PriceByHotelsRequestBody();
        cgRequest.setHotelIds(Arrays.asList("12345"));
        cgRequest.setCityCode("DEL");
        cgRequest.setCountryCode("IN");
        cgRequest.setCheckin("2023-12-01");
        cgRequest.setCheckout("2023-12-03");
        cgRequest.setCouponCount(1);
        cgRequest.setCorrelationKey("test-correlation-key");
        cgRequest.setLoggedIn(true);

        // Setup RoomStayCandidate with GuestCount
        List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        com.mmt.hotels.model.request.RoomStayCandidate hesRoomStayCandidate = new com.mmt.hotels.model.request.RoomStayCandidate();
        
        GuestCount guestCount = new GuestCount();
        guestCount.setCount("2");
        guestCount.setAges(Arrays.asList(5, 10));
        hesRoomStayCandidate.setGuestCounts(Arrays.asList(guestCount));
        hesRoomStayCandidate.setChildAges(Arrays.asList(5, 10));
        
        roomStayCandidates.add(hesRoomStayCandidate);
        cgRequest.setRoomStayCandidates(roomStayCandidates);

        parameterMap = new HashMap<>();
        headers = new HashMap<>();
        headers.put(Constants.OS, "ANDROID");
        headers.put(Constants.USER_CURRENCY, "INR");
        headers.put(Constants.VISITOR_ID, "visitor123");

        // Setup CommonModifierResponse
        commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setCityTaxExclusive(true);
        commonModifierResponse.setMmtAuth("test-auth");
        
        // Setup ExtendedUser
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setUuid("test-uuid");
        extendedUser.setProfileType("BUSINESS");
        extendedUser.setAffiliateId("123");
        commonModifierResponse.setExtendedUser(extendedUser);
        
        // Setup UserLocation
        UserLocation userLocation = new UserLocation();
        userLocation.setCity("Delhi");
        userLocation.setCountry("India");
        userLocation.setState("Delhi");
        commonModifierResponse.setUserLocation(userLocation);
        
        // Setup HydraResponse
        HydraResponse hydraResponse = new HydraResponse();
        hydraResponse.setHydraMatchedSegment(new HashSet<>(Arrays.asList("segment1", "segment2")));
        commonModifierResponse.setHydraResponse(hydraResponse);
        
        // Setup experiment data
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put("test-exp", "value");
        commonModifierResponse.setExpDataMap(expDataMap);
        commonModifierResponse.setExpData("test-exp-data");
    }

    @Test
    public void should_BuildAlternatePriceRequest_When_ValidInput() {
        // When
        DetailRequest result = orchAlternatePriceHelper.buildAlternatePriceRequest(cgRequest, parameterMap, headers, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertEquals("12345", result.getHotelId());
        assertEquals("DEL", result.getLocation().getCityId());
        assertEquals("IN", result.getLocation().getCountryId());
        assertEquals("2023-12-01", result.getCheckIn());
        assertEquals("2023-12-03", result.getCheckOut());
        assertEquals("alternate", result.getSourceApi());
        assertEquals("test-exp-data", result.getExperimentData());
        assertNotNull(result.getClientDetails());
        assertNotNull(result.getRooms());
        assertFalse(result.getRooms().isEmpty());
    }

    @Test(expected = IllegalArgumentException.class)
    public void should_ThrowException_When_CgRequestIsNull() {
        // When
        orchAlternatePriceHelper.buildAlternatePriceRequest(null, parameterMap, headers, commonModifierResponse);
    }

    @Test
    public void should_BuildAlternatePriceRequest_When_CgRequestCouponCountIsZero() {
        // Given
        cgRequest.setCouponCount(0);

        // When
        DetailRequest result = orchAlternatePriceHelper.buildAlternatePriceRequest(cgRequest, parameterMap, headers, commonModifierResponse);

        // Then
        assertNotNull(result);
    }

    @Test
    public void should_BuildAlternatePriceRequest_When_HeadersIsEmpty() {
        // Given
        headers.clear();

        // When
        DetailRequest result = orchAlternatePriceHelper.buildAlternatePriceRequest(cgRequest, parameterMap, headers, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertNotNull(result.getClientDetails());
    }

    @Test
    public void should_ConvertHesRoomStayCandidatesToClientGateway_When_ValidInput() throws Exception {
        // Given
        List<com.mmt.hotels.model.request.RoomStayCandidate> hesRoomStayCandidates = cgRequest.getRoomStayCandidates();

        // When
        List<RoomStayCandidate> result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "convertHesRoomStayCandidatesToClientGateway", hesRoomStayCandidates);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(Integer.valueOf(2), result.get(0).getAdultCount());
        assertEquals(Arrays.asList(5, 10), result.get(0).getChildAges());
    }

    @Test
    public void should_ReturnEmptyList_When_HesRoomStayCandidatesIsNull() throws Exception {
        // When
        List<RoomStayCandidate> result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "convertHesRoomStayCandidatesToClientGateway", (Object) null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_ReturnEmptyList_When_HesRoomStayCandidatesIsEmpty() throws Exception {
        // When
        List<RoomStayCandidate> result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "convertHesRoomStayCandidatesToClientGateway", new ArrayList<>());

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_SkipNullCandidate_When_HesRoomStayCandidateContainsNull() throws Exception {
        // Given
        List<com.mmt.hotels.model.request.RoomStayCandidate> hesRoomStayCandidates = new ArrayList<>();
        hesRoomStayCandidates.add(null);
        hesRoomStayCandidates.add(cgRequest.getRoomStayCandidates().get(0));

        // When
        List<RoomStayCandidate> result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "convertHesRoomStayCandidatesToClientGateway", hesRoomStayCandidates);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    public void should_BuildRoomDetails_When_ValidRoomStayCandidates() throws Exception {
        // Given
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate candidate = new RoomStayCandidate();
        candidate.setAdultCount(2);
        candidate.setChildAges(Arrays.asList(5, 10));
        roomStayCandidates.add(candidate);

        Map<String, String> expDataMap = new HashMap<>();

        // When
        List<com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails> result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildRoomDetails", roomStayCandidates, expDataMap);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(Integer.valueOf(2), result.get(0).getAdults());
        assertEquals(Arrays.asList(5, 10), result.get(0).getChildrenAges());
    }

    @Test
    public void should_BuildRoomDetails_When_DistributeRoomStayCandidatesIsTrue() throws Exception {
        // Given
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate candidate = new RoomStayCandidate();
        candidate.setAdultCount(2);
        candidate.setChildAges(Arrays.asList(5, 10));
        roomStayCandidates.add(candidate);

        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put("test", "value");

        // Setup mock utility behavior
        when(utility.isDistributeRoomStayCandidates(anyList(), anyMap())).thenReturn(true);
        
        List<com.mmt.hotels.model.request.RoomStayCandidate> hesRoomStayCandidates = new ArrayList<>();
        com.mmt.hotels.model.request.RoomStayCandidate hesCandidate = new com.mmt.hotels.model.request.RoomStayCandidate();
        GuestCount guestCount = new GuestCount();
        guestCount.setCount("2");
        guestCount.setAges(Arrays.asList(5, 10));
        hesCandidate.setGuestCounts(Arrays.asList(guestCount));
        hesRoomStayCandidates.add(hesCandidate);
        
        when(utility.buildRoomStayDistribution(anyList(), anyMap())).thenReturn(hesRoomStayCandidates);

        // When
        List<com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails> result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildRoomDetails", roomStayCandidates, expDataMap);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(Integer.valueOf(2), result.get(0).getAdults());
        assertEquals(Arrays.asList(5, 10), result.get(0).getChildrenAges());
        verify(utility).isDistributeRoomStayCandidates(anyList(), anyMap());
        verify(utility).buildRoomStayDistribution(anyList(), anyMap());
    }

    @Test
    public void should_ReturnEmptyList_When_RoomStayCandidatesIsNull() throws Exception {
        // When
        List<com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails> result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildRoomDetails", null, new HashMap<>());

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_ReturnEmptyList_When_RoomStayCandidatesIsEmpty() throws Exception {
        // When
        List<com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails> result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildRoomDetails", new ArrayList<>(), new HashMap<>());

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_SkipNullRoomStayCandidate_When_RoomStayCandidateIsNull() throws Exception {
        // Given
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        roomStayCandidates.add(null);
        
        RoomStayCandidate validCandidate = new RoomStayCandidate();
        validCandidate.setAdultCount(2);
        validCandidate.setChildAges(Arrays.asList(5));
        roomStayCandidates.add(validCandidate);

        // When
        List<com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails> result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildRoomDetails", roomStayCandidates, new HashMap<>());

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(Integer.valueOf(2), result.get(0).getAdults());
    }

    @Test
    public void should_BuildClientDetails_When_ValidInput() throws Exception {
        // Given
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice("ANDROID");

        // When
        ClientDetails result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildClientDetails", cgRequest, deviceDetails, headers, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFeatureFlags());
        assertTrue(result.getFeatureFlags().isCityTaxExclusive());
        assertNotNull(result.getUserDetails());
        assertNotNull(result.getRequestDetails());
    }

    @Test
    public void should_BuildUserDetails_When_ValidInput() throws Exception {
        // When
        UserDetails result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildUserDetails", cgRequest, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertNotNull(result.getLocation());
        assertEquals("Delhi", result.getLocation().getCityName());
        assertEquals("Delhi", result.getLocation().getCityId());
        assertEquals("India", result.getLocation().getCountryId());
        assertEquals("Delhi", result.getLocation().getStateId());
        assertEquals("test-auth", result.getMmtAuth());
        assertEquals("test-uuid", result.getUuid());
        assertEquals(ProfileType.BUSINESS, result.getProfileType());
        assertEquals(SubProfileType.fromValue("123"), result.getSubProfileType());
        assertTrue(result.isLoggedIn());
        assertEquals(2, result.getUserSegments().size());
        assertTrue(result.getUserSegments().contains("segment1"));
        assertTrue(result.getUserSegments().contains("segment2"));
        assertNotNull(result.getSessionData());
        assertEquals("test-uuid", result.getSessionData().getUuid());
    }

    @Test
    public void should_BuildUserDetails_When_CommonModifierResponseHasNullValues() throws Exception {
        // Given
        CommonModifierResponse nullCommonModifierResponse = new CommonModifierResponse();
        nullCommonModifierResponse.setUserLocation(null);
        nullCommonModifierResponse.setMmtAuth(null);
        nullCommonModifierResponse.setExtendedUser(null);
        nullCommonModifierResponse.setHydraResponse(null);

        // When
        UserDetails result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildUserDetails", cgRequest, nullCommonModifierResponse);

        // Then
        assertNotNull(result);
        assertNotNull(result.getLocation());
        assertEquals("", result.getLocation().getCityName());
        assertEquals("", result.getLocation().getCityId());
        assertEquals("", result.getLocation().getCountryId());
        assertEquals("", result.getLocation().getStateId());
        assertEquals("", result.getMmtAuth());
        assertEquals("", result.getUuid());
        assertNull(result.getProfileType());
        assertEquals(SubProfileType.DEFAULT, result.getSubProfileType());
        assertTrue(result.isLoggedIn());
        assertTrue(result.getUserSegments().isEmpty());
    }

    @Test
    public void should_BuildRequestDetails_When_ValidInput() throws Exception {
        // Given
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setDeviceId("device123");
        deviceDetails.setDeviceName("Samsung");
        deviceDetails.setBookingDevice("ANDROID");
        deviceDetails.setAppVersion("1.0.0");
        deviceDetails.setNetworkType("4G");

        // When
        RequestDetails result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildRequestDetails", "test-request-id", deviceDetails, headers);

        // Then
        assertNotNull(result);
        assertEquals(Brand.MMT, result.getBrand());
        assertEquals(Funnel.ALL, result.getFunnelSource());
        assertEquals(Language.ENGLISH, result.getLanguage());
        assertEquals(SiteDomain.IN, result.getSiteDomain());
        assertEquals(IdContext.B2C, result.getIdContext());
        assertEquals("B2CAgent", result.getRequestType());
        assertEquals("B2Cweb", result.getChannel());
        assertEquals(TrafficType.ALL, result.getTrafficType());
        assertEquals(PageContext.DETAIL, result.getPageContext());
        assertEquals("test-request-id", result.getRequestId());
        assertEquals(com.gommt.hotels.orchestrator.detail.enums.Currency.INR, result.getCurrency());
        assertEquals("VISITOR123", result.getJourneyId());
        assertNotNull(result.getBookingDevice());
    }

    @Test
    public void should_BuildRequestDetails_When_RequestIdIsNull() throws Exception {
        // Given
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice("ANDROID");

        // When
        RequestDetails result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildRequestDetails", (String) null, deviceDetails, headers);

        // Then
        assertNotNull(result);
        assertNotNull(result.getRequestId());
        // Should generate a UUID
        assertTrue(result.getRequestId().length() > 0);
    }

    @Test
    public void should_BuildRequestDetails_When_RequestIdIsEmpty() throws Exception {
        // Given
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice("ANDROID");

        // When
        RequestDetails result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildRequestDetails", "", deviceDetails, headers);

        // Then
        assertNotNull(result);
        assertNotNull(result.getRequestId());
        // Should generate a UUID
        assertTrue(result.getRequestId().length() > 0);
    }

    @Test
    public void should_BuildRequestDetails_When_HeadersIsNull() throws Exception {
        // Given
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice("ANDROID");

        // When
        RequestDetails result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildRequestDetails", "test-request-id", deviceDetails, (Map<String, String>) null);

        // Then
        assertNotNull(result);
        assertEquals(com.gommt.hotels.orchestrator.detail.enums.Currency.INR, result.getCurrency());
        assertEquals("", result.getJourneyId());
    }

    @Test
    public void should_BuildRequestDetails_When_HeadersIsEmpty() throws Exception {
        // Given
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice("ANDROID");
        Map<String, String> emptyHeaders = new HashMap<>();

        // When
        RequestDetails result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildRequestDetails", "test-request-id", deviceDetails, emptyHeaders);

        // Then
        assertNotNull(result);
        assertEquals(com.gommt.hotels.orchestrator.detail.enums.Currency.INR, result.getCurrency());
        assertEquals("", result.getJourneyId());
    }

    @Test
    public void should_BuildRequestDetails_When_CurrencyHeaderIsUSD() throws Exception {
        // Given
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice("ANDROID");
        headers.put(Constants.USER_CURRENCY, "usd");

        // When
        RequestDetails result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildRequestDetails", "test-request-id", deviceDetails, headers);

        // Then
        assertNotNull(result);
        assertEquals(com.gommt.hotels.orchestrator.detail.enums.Currency.USD, result.getCurrency());
    }

    @Test
    public void should_BuildBookingDevice_When_ValidInput() throws Exception {
        // Given
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setDeviceId("device123");
        deviceDetails.setDeviceName("Samsung");
        deviceDetails.setBookingDevice("ANDROID");
        deviceDetails.setAppVersion("1.0.0");
        deviceDetails.setNetworkType("4g");

        // When
        BookingDevice result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildBookingDevice", deviceDetails);

        // Then
        assertNotNull(result);
        assertEquals("device123", result.getDeviceId());
        assertEquals("Samsung", result.getDeviceName());
        assertEquals(DeviceType.ANDROID, result.getDeviceType());
        assertEquals("1.0.0", result.getAppVersion());
        assertEquals("4G", result.getNetworkType());
    }

    @Test
    public void should_BuildBookingDevice_When_DeviceDetailsIsNull() throws Exception {
        // When
        BookingDevice result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildBookingDevice", (DeviceDetails) null);

        // Then
        assertNotNull(result);
        // Should return empty BookingDevice
    }

    @Test
    public void should_BuildBookingDevice_When_DeviceDetailsHasNullValues() throws Exception {
        // Given
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setDeviceId(null);
        deviceDetails.setDeviceName(null);
        deviceDetails.setBookingDevice(null);
        deviceDetails.setAppVersion(null);
        deviceDetails.setNetworkType(null);

        // When
        BookingDevice result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildBookingDevice", deviceDetails);

        // Then
        assertNotNull(result);
        assertEquals("", result.getDeviceId());
        assertEquals("", result.getDeviceName());
        assertEquals(DeviceType.DESKTOP, result.getDeviceType());
        assertEquals("", result.getAppVersion());
        assertEquals("", result.getNetworkType());
    }

    @Test
    public void should_BuildBookingDevice_When_DeviceTypeIsInvalid() throws Exception {
        // Given
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice("INVALID_DEVICE_TYPE");

        // When
        BookingDevice result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildBookingDevice", deviceDetails);

        // Then
        assertNotNull(result);
    }

    @Test
    public void should_HandleGuestCountWithoutAges_When_ConvertingRoomStayCandidates() throws Exception {
        // Given
        List<com.mmt.hotels.model.request.RoomStayCandidate> hesRoomStayCandidates = new ArrayList<>();
        com.mmt.hotels.model.request.RoomStayCandidate hesCandidate = new com.mmt.hotels.model.request.RoomStayCandidate();
        
        GuestCount guestCount = new GuestCount();
        guestCount.setCount("3");
        guestCount.setAges(null); // No ages
        hesCandidate.setGuestCounts(Arrays.asList(guestCount));
        hesCandidate.setChildAges(null);
        
        hesRoomStayCandidates.add(hesCandidate);

        // When
        List<RoomStayCandidate> result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "convertHesRoomStayCandidatesToClientGateway", hesRoomStayCandidates);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(Integer.valueOf(3), result.get(0).getAdultCount());
        assertTrue(result.get(0).getChildAges().isEmpty());
    }

    @Test
    public void should_HandleDistributionWithEmptyGuestCounts_When_BuildingRoomDetails() throws Exception {
        // Given
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate candidate = new RoomStayCandidate();
        candidate.setAdultCount(2);
        roomStayCandidates.add(candidate);

        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put("test", "value");

        when(utility.isDistributeRoomStayCandidates(anyList(), anyMap())).thenReturn(true);
        
        // Setup HES candidate with empty guest counts
        List<com.mmt.hotels.model.request.RoomStayCandidate> hesRoomStayCandidates = new ArrayList<>();
        com.mmt.hotels.model.request.RoomStayCandidate hesCandidate = new com.mmt.hotels.model.request.RoomStayCandidate();
        
        GuestCount guestCountWithEmptyAges = new GuestCount();
        guestCountWithEmptyAges.setCount("2");
        guestCountWithEmptyAges.setAges(Collections.emptyList());
        hesCandidate.setGuestCounts(Arrays.asList(guestCountWithEmptyAges));
        hesRoomStayCandidates.add(hesCandidate);
        
        when(utility.buildRoomStayDistribution(anyList(), anyMap())).thenReturn(hesRoomStayCandidates);

        // When
        List<com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails> result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildRoomDetails", roomStayCandidates, expDataMap);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(Integer.valueOf(2), result.get(0).getAdults());
        assertTrue(result.get(0).getChildrenAges().isEmpty());
    }

    @Test
    public void should_BuildUserDetails_When_ExtendedUserProfileTypeIsInvalid() throws Exception {
        // Given
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setUuid("test-uuid");
        extendedUser.setProfileType("INVALID_PROFILE_TYPE");
        extendedUser.setAffiliateId("123");
        commonModifierResponse.setExtendedUser(extendedUser);

        // When
        UserDetails result = ReflectionTestUtils.invokeMethod(
                orchAlternatePriceHelper, "buildUserDetails", cgRequest, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertNull(result.getProfileType()); // Should be null due to invalid enum value
    }
}