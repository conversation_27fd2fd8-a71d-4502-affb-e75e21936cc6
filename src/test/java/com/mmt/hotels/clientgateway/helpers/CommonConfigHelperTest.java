package com.mmt.hotels.clientgateway.helpers;


import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.pms.PersuasionConfig;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.propertymanager.config.PropertyManager;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Map;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class CommonConfigHelperTest {
    @InjectMocks
    CommonConfigHelper commonConfigHelper;

    @Mock
    CommonConfigConsul commonConfigConsul;

    @Mock
    private ObjectMapperUtil objectMapperUtil;

    @Mock
    private PropertyManager propertyManager;


    @Test
    public void initTest() throws Exception {
        //For Consul
        Field privateConsul = CommonConfigHelper.class.getDeclaredField("consulFlag");
        privateConsul.setAccessible(true);
        privateConsul.set(commonConfigHelper, true);
        Mockito.when(commonConfigConsul.getCommonUserServiceQuery()).thenReturn("abcd");

        commonConfigHelper.init();

        Field commonUserServiceRequestBody = CommonConfigHelper.class.getDeclaredField("commonUserServiceRequestBody");
        commonUserServiceRequestBody.setAccessible(true);
        Assert.assertNotNull("abcd",(String)commonUserServiceRequestBody.get(commonConfigHelper));

        //For pms
        privateConsul.set(commonConfigHelper, false);
        CommonConfig config = Mockito.mock(CommonConfig.class);
        Mockito.when(propertyManager.getProperty(Mockito.any(), Mockito.any())).thenReturn(config);
        Mockito.when(config.ebableInboundExpDeviceList()).thenReturn(Arrays.asList("x","y","z"));
        commonConfigHelper.init();
        Field ebableInboundExpDeviceList = CommonConfigHelper.class.getDeclaredField("ebableInboundExpDeviceList");
        ebableInboundExpDeviceList.setAccessible(true);
        Assert.assertEquals(Arrays.asList("x","y","z"),ebableInboundExpDeviceList.get(commonConfigHelper));
    }

    @Test
    public void getPersuasionConfigsTest() throws Exception{
        Map<String,PersuasionConfig> res = commonConfigHelper.getPersuasionConfigs("PWA");
        Field persuasionConfigPWA = CommonConfigHelper.class.getDeclaredField("persuasionConfigPWA");
        Assert.assertNotNull(persuasionConfigPWA);
    }

}
