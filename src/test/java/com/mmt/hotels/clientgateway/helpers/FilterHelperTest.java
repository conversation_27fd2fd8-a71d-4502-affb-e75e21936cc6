package com.mmt.hotels.clientgateway.helpers;

import java.util.*;

import com.mmt.hotels.clientgateway.businessobjects.CategoryAttributes;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfigDetail;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.MetricErrorCodes;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import com.mmt.hotels.clientgateway.businessobjects.FilterConfigCategory;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfiguration;
import org.springframework.test.util.ReflectionTestUtils;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfigurationV2;
import com.mmt.hotels.clientgateway.businessobjects.FilterPage;


@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class FilterHelperTest {

	@InjectMocks
	FilterHelper filterHelper;

	@Mock
	PolyglotService polyglotService;
	
	@Test
	public void getFilterConfigTest() {
		Assert.assertNotNull(filterHelper.getFilterConfig("{}", "CORP"));
	}
	
	@Test
	public void mergeFilterConfigTest() {
		FilterConfiguration srcFilterConfig = new FilterConfiguration();
		FilterConfiguration addedFilterConfig = new FilterConfiguration();
		srcFilterConfig.setConditions(new LinkedHashMap<>());
		srcFilterConfig.getConditions().put("test", new ArrayList<String>());
		srcFilterConfig.setFilters(new LinkedHashMap<>());
		srcFilterConfig.getFilters().put("test", new FilterConfigCategory());
		addedFilterConfig.setConditions(new LinkedHashMap<>());
		addedFilterConfig.getConditions().put("test", new ArrayList<String>());
		addedFilterConfig.setFilters(new LinkedHashMap<>());
		addedFilterConfig.getFilters().put("test", new FilterConfigCategory());
		
		filterHelper.mergeFilterConfig(new FilterConfiguration(), null, "CORP");
		filterHelper.mergeFilterConfig(new FilterConfiguration(), new FilterConfiguration(), "CORP");
		Assert.assertNotNull(filterHelper.mergeFilterConfig(srcFilterConfig, addedFilterConfig, "B2C"));
	}
	
	
	@Test
	public void fetchPriceTitleTest() {
		LinkedHashMap<String,String>expDataMap = new LinkedHashMap<>();
		expDataMap.put("PDO","PN");
		Assert.assertNotNull(filterHelper.fetchPriceTitle(expDataMap));
		expDataMap.put("PDO","PNT");
		Assert.assertNotNull(filterHelper.fetchPriceTitle(expDataMap));
		expDataMap.put("PDO","PRN");
		Assert.assertNotNull(filterHelper.fetchPriceTitle(expDataMap));
		expDataMap.put("PDO","PRNT");
		Assert.assertNotNull(filterHelper.fetchPriceTitle(expDataMap));
		expDataMap.put("PDO","TP");
		Assert.assertNotNull(filterHelper.fetchPriceTitle(expDataMap));
		expDataMap.put("PDO","TPT");
		Assert.assertNotNull(filterHelper.fetchPriceTitle(expDataMap));
	}

	@Test
	public void mergeConditionsTest() {
		FilterConfigCategory fCategory = new FilterConfigCategory();
		fCategory.setCondition(new LinkedHashMap<>());
		fCategory.getCondition().put("abc","abc");
		FilterConfigCategory fCategoryAdded = new FilterConfigCategory();
		fCategoryAdded.setCondition(new LinkedHashMap<>());
		fCategoryAdded.getCondition().put("abc","abc");
		ReflectionTestUtils.invokeMethod(filterHelper, "mergeConditions", fCategory,fCategoryAdded);
	}

	@Test
	public void updateIconUrlsTest() {

		FilterConfigCategory fCategory = new FilterConfigCategory();
		fCategory.setGroups(new LinkedHashMap<>());
		fCategory.getGroups().put("abc",new LinkedHashMap<>());
		fCategory.getGroups().get("abc").put("abc",new FilterConfigDetail());
		fCategory.getGroups().get("abc").get("abc").setIconList(new ArrayList<>());
		fCategory.getGroups().get("abc").get("abc").getIconList().add("xyz");
		fCategory.getGroups().get("abc").get("abc").setImageUrl("url");

		FilterConfigCategory fCategoryAdded = new FilterConfigCategory();
		fCategoryAdded.setGroups(new LinkedHashMap<>());
		fCategoryAdded.getGroups().put("abc",new LinkedHashMap<>());
		fCategoryAdded.getGroups().get("abc").put("abc",new FilterConfigDetail());
		ReflectionTestUtils.invokeMethod(filterHelper, "updateIconUrls", fCategory,fCategoryAdded);

	}

	@Test
	public void removeIconUrlsAndImageUrlsTest() {

		FilterConfiguration mergedFilterConfig = new FilterConfiguration();

		mergedFilterConfig.setFilters(new LinkedHashMap<>());
		FilterConfigCategory filterConfig = new FilterConfigCategory();
		filterConfig.setIconUrl("xyz");
		filterConfig.setGroups(new LinkedHashMap<>());
		filterConfig.getGroups().put("abc",new LinkedHashMap<>());
		filterConfig.getGroups().get("abc").put("abc",new FilterConfigDetail());
		filterConfig.getGroups().get("abc").get("abc").setIconList(new ArrayList<>());
		filterConfig.getGroups().get("abc").get("abc").getIconList().add("xyz");
		filterConfig.getGroups().get("abc").get("abc").setImageUrl("url");
		mergedFilterConfig.getFilters().put("abc",filterConfig);

		ReflectionTestUtils.invokeMethod(filterHelper, "removeIconUrlsAndImageUrls", mergedFilterConfig);
	}

	@Test
	public void calculateFiltersToShowTest() {

		FilterConfiguration mergedFilterConfig = new FilterConfiguration();

		mergedFilterConfig.setFilters(new LinkedHashMap<>());
		FilterConfigCategory filterConfig = new FilterConfigCategory();
		filterConfig.setIconUrl("xyz");
		filterConfig.setGroups(new LinkedHashMap<>());
		filterConfig.getGroups().put("abc",new LinkedHashMap<>());
		filterConfig.getGroups().get("abc").put("abc",new FilterConfigDetail());
		filterConfig.getGroups().get("abc").get("abc").setIconList(new ArrayList<>());
		filterConfig.getGroups().get("abc").get("abc").getIconList().add("xyz");
		filterConfig.getGroups().get("abc").get("abc").setImageUrl("url");
		mergedFilterConfig.getFilters().put("abc",filterConfig);

		FilterConfiguration addedFilterConfig = new FilterConfiguration();
		addedFilterConfig.setFiltersToShow(new LinkedHashMap<>());
		addedFilterConfig.getFiltersToShow().put("abc",new LinkedHashMap<>());
		addedFilterConfig.getFiltersToShow().get("abc").put("abc",new ArrayList<>());
		addedFilterConfig.getFiltersToShow().get("abc").get("abc").add("xyz");

		ReflectionTestUtils.invokeMethod(filterHelper, "calculateFiltersToShow", mergedFilterConfig,addedFilterConfig);
	}

	@Test
	public void updateAppliedFilterMapDptCollectionsTest() {
		Map<FilterGroup, Set<Filter>> appliedFilterMap = new HashMap<>();
		Set<String> dptInlineAppliedCategories = new HashSet<>();

		Set<Filter> collectionFilterValue = new HashSet<>();
		Filter filter = new Filter();
		filter.setFilterValue("TEST1#TESTGEROUP=TESTVALUE#HOTEL_PRICE_BUCKET=1000-2000#area=TEST_AREA#poi=TEST_POI");
		collectionFilterValue.add(filter);
		appliedFilterMap.put(com.mmt.hotels.filter.FilterGroup.DPT_COLLECTIONS, collectionFilterValue);
		filterHelper.updateAppliedFilterMapDptCollections(appliedFilterMap, null, dptInlineAppliedCategories);
		Assert.assertEquals(dptInlineAppliedCategories.size(), 1);

		Filter propFilter = new Filter();
		propFilter.setFilterGroup(FilterGroup.DPT_PROP_COLLECTIONS);
		propFilter.setFilterValue("MOST_BOOKED#Filter_value");
		Set<Filter> collectionFilterProp = new HashSet<>();
		collectionFilterProp.add(propFilter);
		appliedFilterMap.put(FilterGroup.DPT_PROP_COLLECTIONS, collectionFilterProp);
		filterHelper.updateAppliedFilterMapDptCollections(appliedFilterMap, null, dptInlineAppliedCategories);
		Assert.assertTrue(dptInlineAppliedCategories.contains("MOST_BOOKED"));
		Set<Filter> appliedF = appliedFilterMap.get(FilterGroup.DPT_PROP_COLLECTIONS);
		Assert.assertEquals(appliedF.stream().findFirst().get().getFilterValue(), "Filter_value");

	}

	@Test
	public void addCategoryAttributesTest() {
		MetricErrorCodes metricErrorCodes = new MetricErrorCodes();
		FilterConfiguration srcFilterConfig = new FilterConfiguration();
		FilterConfiguration addedFilterConfig = new FilterConfiguration();
		srcFilterConfig.setConditions(new LinkedHashMap<>());
		srcFilterConfig.getConditions().put("test", new ArrayList<String>());
		srcFilterConfig.setFilters(new LinkedHashMap<>());
		srcFilterConfig.getFilters().put("test", new FilterConfigCategory());
		addedFilterConfig.setConditions(new LinkedHashMap<>());
		addedFilterConfig.getConditions().put("test", new ArrayList<String>());
		addedFilterConfig.setFilters(new LinkedHashMap<>());
		addedFilterConfig.getFilters().put("test", new FilterConfigCategory());
		addedFilterConfig.setCategoryAttributes(new LinkedHashMap<>());
		CategoryAttributes categoryAttributes = new CategoryAttributes();
		categoryAttributes.setCollapsed(true);
		categoryAttributes.setSortingRequired(true);
		categoryAttributes.setRemoveZeroCountFilters(true);
		CategoryAttributes categoryAttributes1 = CategoryAttributes.builder()
				.sortingRequired(false)
				.collapsed(false)
				.removeZeroCountFilters(false)
				.build();
		addedFilterConfig.getCategoryAttributes().put("test", categoryAttributes);
		ReflectionTestUtils.invokeMethod(filterHelper, "addCategoryAttributes", srcFilterConfig,addedFilterConfig);

	}

	@Test
	public void getFilterConfigV2Test() {
		// Null base config
		Assert.assertNull(filterHelper.getFilterConfigV2(null, new FilterConfigurationV2(), "B2C", "cohort1", "ANDROID"));
		// Null child config
		FilterConfigurationV2 base = new FilterConfigurationV2();
		Assert.assertEquals(base, filterHelper.getFilterConfigV2(base, null, "B2C", "cohort1", "ANDROID"));
		// Both present, delegate to mergedFilterConfig
		FilterConfigurationV2 child = new FilterConfigurationV2();
		base.setFilterPages(new LinkedHashMap<>());
		child.setFilterPages(new LinkedHashMap<>());
		Assert.assertEquals(base, filterHelper.getFilterConfigV2(base, child, "B2C", "cohort1", "ANDROID"));
	}

	@Test
	public void mergedFilterConfig_BaseFilterPagesNullOrEmpty_ReturnsBase() throws Exception {
		FilterConfigurationV2 base = new FilterConfigurationV2();
		FilterConfigurationV2 child = new FilterConfigurationV2();
		// base config must always have master pages list
		LinkedHashMap<String, FilterPage> basePages = new LinkedHashMap<>();
		FilterPage basePage = new FilterPage();
		basePage.setTitle("BaseTitle");
		basePage.setFilters(new LinkedHashMap<>());
		basePages.put("page1", basePage);
		base.setFilterPages(basePages);
		Assert.assertEquals(base, ReflectionTestUtils.invokeMethod(filterHelper, "mergedFilterConfig", base, child, "B2C", "cohort1","ANDROID"));
	}

	@Test
	public void mergedFilterConfig_BaseFilterPagesNotEmpty_ChildFilterPagesEmpty() throws Exception {
		FilterConfigurationV2 base = new FilterConfigurationV2();
		FilterConfigurationV2 child = new FilterConfigurationV2();
		LinkedHashMap<String, FilterPage> basePages = new LinkedHashMap<>();
		FilterPage page1 = new FilterPage();
		page1.setTitle("BaseTitle");
		page1.setFilters(new LinkedHashMap<>());
		basePages.put("page1", page1);
		base.setFilterPages(basePages);
		FilterConfigurationV2 result = (FilterConfigurationV2) ReflectionTestUtils.invokeMethod(filterHelper, "mergedFilterConfig", base, child, "B2C", "cohort1","ANDROID");
		Assert.assertTrue(result.getFilterPages().containsKey("page1"));
		Assert.assertEquals("BaseTitle", result.getFilterPages().get("page1").getTitle());
	}

	@Test
	public void mergedFilterConfig_ChildFilterPagesOverridesBase() throws Exception {
		FilterConfigurationV2 base = new FilterConfigurationV2();
		FilterConfigurationV2 child = new FilterConfigurationV2();
		LinkedHashMap<String, FilterPage> basePages = new LinkedHashMap<>();
		FilterPage page1 = new FilterPage();
		page1.setTitle("BaseTitle");
		page1.setFilters(new LinkedHashMap<>());
		basePages.put("page1", page1);
		base.setFilterPages(basePages);
		LinkedHashMap<String, FilterPage> childPages = new LinkedHashMap<>();
		FilterPage page1Child = new FilterPage();
		page1Child.setTitle("ChildTitle");
		page1Child.setFilters(new LinkedHashMap<>());
		childPages.put("page1", page1Child);
		child.setFilterPages(childPages);
		FilterConfigurationV2 result = (FilterConfigurationV2) ReflectionTestUtils.invokeMethod(filterHelper, "mergedFilterConfig", base, child, "B2C", "cohort1","ANDROID");
		Assert.assertEquals("ChildTitle", result.getFilterPages().get("page1").getTitle());
	}

	@Test
	public void mergedFilterConfig_ChildFilterPagesAddsNewPage() throws Exception {
		FilterConfigurationV2 base = new FilterConfigurationV2();
		FilterConfigurationV2 child = new FilterConfigurationV2();
		LinkedHashMap<String, FilterPage> basePages = new LinkedHashMap<>();
		FilterPage page1 = new FilterPage();
		page1.setTitle("BaseTitle");
		page1.setFilters(new LinkedHashMap<>());
		basePages.put("page1", page1);
		base.setFilterPages(basePages);
		LinkedHashMap<String, FilterPage> childPages = new LinkedHashMap<>();
		FilterPage page1Child = new FilterPage();
		page1Child.setTitle("ChildTitle");
		page1Child.setFilters(new LinkedHashMap<>());
		childPages.put("page1", page1Child);
		FilterPage page2Child = new FilterPage();
		page2Child.setTitle("ChildPage2");
		page2Child.setFilters(new LinkedHashMap<>());
		childPages.put("page2", page2Child);
		child.setFilterPages(childPages);
		FilterConfigurationV2 result = (FilterConfigurationV2) ReflectionTestUtils.invokeMethod(filterHelper, "mergedFilterConfig", base, child, "B2C", "cohort1","ANDROID");
		Assert.assertTrue(result.getFilterPages().containsKey("page2"));
		Assert.assertEquals("ChildPage2", result.getFilterPages().get("page2").getTitle());
	}

	@Test
	public void mergedFilterConfig_PagesToShowFiltersPages() throws Exception {
		FilterConfigurationV2 base = new FilterConfigurationV2();
		FilterConfigurationV2 child = new FilterConfigurationV2();
		// base config must always have master pages list
		LinkedHashMap<String, FilterPage> basePages = new LinkedHashMap<>();
		FilterPage basePage = new FilterPage();
		basePage.setTitle("BaseTitle");
		basePage.setFilters(new LinkedHashMap<>());
		basePages.put("page1", basePage);
		base.setFilterPages(basePages);
		LinkedHashMap<String, FilterPage> childPages = new LinkedHashMap<>();
		FilterPage page1Child = new FilterPage();
		page1Child.setTitle("ChildTitle");
		page1Child.setFilters(new LinkedHashMap<>());
		childPages.put("page1", page1Child);
		child.setFilterPages(childPages);
		LinkedHashMap<String, FilterPage> pagesToShow = new LinkedHashMap<>();
		FilterPage showPage = new FilterPage();
		showPage.setTitle("ShowPage");
		showPage.setFilters(new LinkedHashMap<>());
		pagesToShow.put("page1", showPage);
		child.setPagesToShow(pagesToShow);
		FilterConfigurationV2 result = (FilterConfigurationV2) ReflectionTestUtils.invokeMethod(filterHelper, "mergedFilterConfig", base, child, "B2C", "cohort1","ANDROID");
		Assert.assertEquals(1, result.getFilterPages().size());
		Assert.assertTrue(result.getFilterPages().containsKey("page1"));
	}

	@Test
	public void mergedFilterConfig_RankOrderV2ByCohort() throws Exception {
		FilterConfigurationV2 base = new FilterConfigurationV2();
		FilterConfigurationV2 child = new FilterConfigurationV2();
		LinkedHashMap<String, FilterPage> basePages = new LinkedHashMap<>();
		FilterPage basePage = new FilterPage();
		basePage.setTitle("BaseTitle");
		basePage.setFilters(new LinkedHashMap<>());
		basePages.put("page1", basePage);
		base.setFilterPages(basePages);
		LinkedHashMap<String, LinkedHashMap<String, Integer>> rankOrderV2 = new LinkedHashMap<>();
		LinkedHashMap<String, Integer> cohortOrder = new LinkedHashMap<>();
		cohortOrder.put("page1", 1);
		rankOrderV2.put("cohort1", cohortOrder);
		child.setRankOrderV2(rankOrderV2);
		FilterConfigurationV2 result = (FilterConfigurationV2) ReflectionTestUtils.invokeMethod(filterHelper, "mergedFilterConfig", base, child, "B2C", "cohort1","ANDROID");
		Assert.assertEquals(Integer.valueOf(1), result.getRankOrder().get("page1"));
	}

	@Test
	public void mergedFilterConfig_FallbackToDefaultCohort() throws Exception {
		FilterConfigurationV2 base = new FilterConfigurationV2();
		FilterConfigurationV2 child = new FilterConfigurationV2();
		LinkedHashMap<String, FilterPage> basePages = new LinkedHashMap<>();
		FilterPage basePage = new FilterPage();
		basePage.setTitle("BaseTitle");
		basePage.setFilters(new LinkedHashMap<>());
		basePages.put("page2", basePage);
		base.setFilterPages(basePages);
		LinkedHashMap<String, LinkedHashMap<String, Integer>> rankOrderV2 = new LinkedHashMap<>();
		LinkedHashMap<String, Integer> defaultOrder = new LinkedHashMap<>();
		defaultOrder.put("page2", 2);
		rankOrderV2.put("default", defaultOrder);
		child.setRankOrderV2(rankOrderV2);
		FilterConfigurationV2 result = (FilterConfigurationV2) ReflectionTestUtils.invokeMethod(filterHelper, "mergedFilterConfig", base, child, "B2C", "otherCohort","ANDROID");
		Assert.assertEquals(Integer.valueOf(2), result.getRankOrder().get("page2"));
	}

	@Test
	public void mergedFilterConfig_FallbackToChildRankOrderIfRankOrderV2Null() throws Exception {
		FilterConfigurationV2 base = new FilterConfigurationV2();
		FilterConfigurationV2 child = new FilterConfigurationV2();
		LinkedHashMap<String, FilterPage> basePages = new LinkedHashMap<>();
		FilterPage basePage = new FilterPage();
		basePage.setTitle("BaseTitle");
		basePage.setFilters(new LinkedHashMap<>());
		basePages.put("page1", basePage);
		base.setFilterPages(basePages);
		child.setRankOrderV2(null);
		LinkedHashMap<String, Integer> rankOrder = new LinkedHashMap<>();
		rankOrder.put("page1", 3);
		child.setRankOrder(rankOrder);
		FilterConfigurationV2 result = (FilterConfigurationV2) ReflectionTestUtils.invokeMethod(filterHelper, "mergedFilterConfig", base, child, "B2C", "cohort1","ANDROID");
		Assert.assertEquals(Integer.valueOf(3), result.getRankOrder().get("page1"));
	}

	@Test
	public void mergedFilterConfig_CorpIdContextDisablesHomestayBannerIconUrlCopy() throws Exception {
		FilterConfigurationV2 base = new FilterConfigurationV2();
		FilterConfigurationV2 child = new FilterConfigurationV2();
		LinkedHashMap<String, FilterPage> basePages = new LinkedHashMap<>();
		FilterPage basePage = new FilterPage();
		basePage.setTitle("BaseTitle");
		basePage.setFilters(new LinkedHashMap<>());
		basePages.put("page1", basePage);
		base.setFilterPages(basePages);
		base.setHomestayBannerIconUrl("iconUrl");
		FilterConfigurationV2 result = (FilterConfigurationV2) ReflectionTestUtils.invokeMethod(filterHelper, "mergedFilterConfig", base, child, "CORP", "cohort1","ANDROID");
		Assert.assertNull(result.getHomestayBannerIconUrl());
	}

	@Test
	public void mergedFilterConfig_NonCorpIdContextCopiesHomestayBannerIconUrl() throws Exception {
		FilterConfigurationV2 base = new FilterConfigurationV2();
		FilterConfigurationV2 child = new FilterConfigurationV2();
		LinkedHashMap<String, FilterPage> basePages = new LinkedHashMap<>();
		FilterPage basePage = new FilterPage();
		basePage.setTitle("BaseTitle");
		basePage.setFilters(new LinkedHashMap<>());
		basePages.put("page1", basePage);
		base.setFilterPages(basePages);
		base.setHomestayBannerIconUrl("iconUrl");
		FilterConfigurationV2 result = (FilterConfigurationV2) ReflectionTestUtils.invokeMethod(filterHelper, "mergedFilterConfig", base, child, "B2C", "cohort1","ANDROID");
		Assert.assertEquals("iconUrl", result.getHomestayBannerIconUrl());
	}
	
}
