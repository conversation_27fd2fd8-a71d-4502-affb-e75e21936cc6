package com.mmt.hotels.clientgateway.helpers;

import com.mmt.hotels.clientgateway.response.MarkUpConfig;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.mypartner.MarkUp;
import com.mmt.hotels.model.response.mypartner.MarkUpDetails;
import com.mmt.hotels.model.response.mypartner.MarkUpType;
import org.jboss.logging.MDC;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class PricingEngineHelperTest {

    @InjectMocks
    PricingEngineHelper pricingEngineHelper;

    @Spy
    Utility utility;

    @Test
    public void testAppendPEEInUrl() {
        String expData = "{\"PEE\":\"t\"}";
        String url = "http://test";
        String finalUrl = pricingEngineHelper.appendPEEInUrl(url, expData);
        Assert.assertEquals(finalUrl, url + "?pee=T");

        expData = "{\"PEE\":\"f\"}";
        url = "http://test";
        finalUrl = pricingEngineHelper.appendPEEInUrl(url, expData);
        Assert.assertEquals(finalUrl, url + "?pee=F");

        expData = "{\"PEE\":\"T\"}";
        url = "http://test/test?test=test";
        finalUrl = pricingEngineHelper.appendPEEInUrl(url, expData);
        Assert.assertEquals(finalUrl, url + "&pee=T");

        expData = "{\"PEE\":\"F\"}";
        url = "http://test/test?test=test";
        finalUrl = pricingEngineHelper.appendPEEInUrl(url, expData);
        Assert.assertEquals(finalUrl, url + "&pee=F");
    }

    @Test
    public void testAppendPEEDInUrl() {
        String expData = "{\"PEED\":\"t\"}";
        String url = "http://test";
        String finalUrl = pricingEngineHelper.appendPEEDInUrl(url, expData);
        Assert.assertEquals(finalUrl, url + "?peed=T");

        expData = "{\"PEED\":\"f\"}";
        url = "http://test";
        finalUrl = pricingEngineHelper.appendPEEDInUrl(url, expData);
        Assert.assertEquals(finalUrl, url + "?peed=F");

        expData = "{\"PEED\":\"T\"}";
        url = "http://test/test?test=test";
        finalUrl = pricingEngineHelper.appendPEEDInUrl(url, expData);
        Assert.assertEquals(finalUrl, url + "&peed=T");

        expData = "{\"PEED\":\"F\"}";
        url = "http://test/test?test=test";
        finalUrl = pricingEngineHelper.appendPEEDInUrl(url, expData);
        Assert.assertEquals(finalUrl, url + "&peed=F");
    }

    @Test
    public void appendFilterServiceExpInUrlTest() {
        String expData = "{\"LSTNRBY\":\"T\",\"APE\":\"NPM_51_6\",\"PAH\":\"5\",\"PAH5\":\"T\",\"WPAH\":\"F\",\"BNPL\":\"T\",\"MRS\":\"T\",\"PDO\":\"PN\",\"MCUR\":\"T\",\"ADDON\":\"T\",\"CHPC\":\"T\",\"AARI\":\"T\",\"NLP\":\"Y\",\"RCPN\":\"T\",\"PLRS\":\"T\",\"MMRVER\":\"V3\",\"BLACK\":\"T\",\"HIS\":\"DEFAULT\",\"htlPolarisCard\":\"true\",\"newFilterService\":\"true\"}";
        String result = pricingEngineHelper.appendFilterServiceExpInUrl("www.com", expData);
        Assert.assertNotNull(result);
    }

    @Test
    public void appendPricerV2InUrlTest(){
        Assert.assertNotNull(pricingEngineHelper.appendPricerV2InUrl("abc.jpg", "{VIDEO:0}"));
    }
    @Test
    public void getMarkUpForHotelsTest(){
        MarkUpDetails markUpDetails=new MarkUpDetails();
        markUpDetails.setMarkupEligible(true);
        markUpDetails.setMarkupMap(new HashMap<>());
        // Checking Domestic Percentage case
        markUpDetails.getMarkupMap().put("DH",new MarkUp());
        markUpDetails.getMarkupMap().get("DH").setType(MarkUpType.PERCENTAGE);
        markUpDetails.getMarkupMap().get("DH").setValue(10.2);
        MDC.put(MDCHelper.MDCKeys.COUNTRY.getStringValue(), "DOM");
        double result=pricingEngineHelper.getMarkUpForHotels(markUpDetails,1000.0);
        Assert.assertEquals(result,1000 *.102,0);
        // Checking Domestic Absolute case
        markUpDetails.getMarkupMap().get("DH").setType(MarkUpType.ABSOLUTE);
        markUpDetails.getMarkupMap().get("DH").setValue(100.2);
        result=pricingEngineHelper.getMarkUpForHotels(markUpDetails,1000.0);
        Assert.assertEquals(result,100.2,0);
        // Checking Internation Percentage case
        MDC.put(MDCHelper.MDCKeys.COUNTRY.getStringValue(), "INTL");
        markUpDetails.getMarkupMap().put("IH",new MarkUp());
        markUpDetails.getMarkupMap().get("IH").setType(MarkUpType.PERCENTAGE);
        markUpDetails.getMarkupMap().get("IH").setValue(10.2);
        result=pricingEngineHelper.getMarkUpForHotels(markUpDetails,1000.0);
        Assert.assertEquals(result,1000 *.102,0);
        //Edge cases
        markUpDetails.setMarkupMap(new HashMap<>());
        result=pricingEngineHelper.getMarkUpForHotels(markUpDetails,1000.0);
        Assert.assertEquals(result,0.0,0);
        result=pricingEngineHelper.getMarkUpForHotels(markUpDetails,null);
        Assert.assertEquals(result,0.0,0);
        MDC.clear();
    }
    @Test
    public void buildMarkUpConfigTest(){
        MarkUpDetails markUpDetails=new MarkUpDetails();
        markUpDetails.setMarkupEligible(true);
        markUpDetails.setEditable(true);
        MarkUpConfig markUpConfig=new MarkUpConfig();
        markUpConfig.setCtaText("Change");
        MarkUpConfig result= pricingEngineHelper.buildMarkUpConfig(markUpDetails,markUpConfig);

        // CTA should be shown when isMarkupEligible and isEditable is true
        Assert.assertNotNull(result);
        Assert.assertEquals(result.getCtaText(),"Change");

        markUpDetails.setEditable(false);
        result= pricingEngineHelper.buildMarkUpConfig(markUpDetails,markUpConfig);

        // CTA shouldn't be shown when isEditable is false
        Assert.assertNotNull(result);
        Assert.assertNull(result.getCtaText());
    }

    // Additional test cases for improved code coverage - added without modifying existing tests

    @Test
    public void testAppendPEEInUrlWithNullExperimentData() {
        String url = "http://test";
        String finalUrl = pricingEngineHelper.appendPEEInUrl(url, null);
        Assert.assertEquals("Should append default PEE value for null experiment data", url + "?pee=F", finalUrl);
    }

    @Test
    public void testAppendPEEInUrlWithEmptyExperimentData() {
        String url = "http://test";
        String finalUrl = pricingEngineHelper.appendPEEInUrl(url, "");
        Assert.assertEquals("Should append default PEE value for empty experiment data", url + "?pee=F", finalUrl);
    }

    @Test
    public void testAppendPEEDInUrlWithNullExperimentData() {
        String url = "http://test";
        String finalUrl = pricingEngineHelper.appendPEEDInUrl(url, null);
        Assert.assertEquals("Should append default PEED value for null experiment data", url + "?peed=F", finalUrl);
    }

    @Test
    public void testAppendPEEDInUrlWithEmptyExperimentData() {
        String url = "http://test";
        String finalUrl = pricingEngineHelper.appendPEEDInUrl(url, "");
        Assert.assertEquals("Should append default PEED value for empty experiment data", url + "?peed=F", finalUrl);
    }

    @Test
    public void testAppendFilterServiceExpInUrlWithNullData() {
        String url = "http://test";
        String result = pricingEngineHelper.appendFilterServiceExpInUrl(url, null);
        Assert.assertNotNull("Should handle null experiment data", result);
        Assert.assertTrue("Should contain default filter service parameters", 
            result.contains("newfilterservice=") && result.contains("newfilterserviceih=TRUE"));
    }

    @Test
    public void testAppendFilterServiceExpInUrlWithEmptyData() {
        String url = "http://test";
        String result = pricingEngineHelper.appendFilterServiceExpInUrl(url, "");
        Assert.assertNotNull("Should handle empty experiment data", result);
        Assert.assertTrue("Should contain default filter service parameters", 
            result.contains("newfilterservice=") && result.contains("newfilterserviceih=TRUE"));
    }

    @Test
    public void testAppendPricerV2InUrlWithNullData() {
        String url = "http://test";
        String result = pricingEngineHelper.appendPricerV2InUrl(url, null);
        Assert.assertNotNull("Should handle null experiment data", result);
        Assert.assertTrue("Should contain default pricer v2 value", result.contains("pricerv2=FALSE"));
    }

    @Test
    public void testAppendPricerV2InUrlWithEmptyData() {
        String url = "http://test";
        String result = pricingEngineHelper.appendPricerV2InUrl(url, "");
        Assert.assertNotNull("Should handle empty experiment data", result);
        Assert.assertTrue("Should contain default pricer v2 value", result.contains("pricerv2=FALSE"));
    }

    @Test
    public void testGetMarkUpForHotelsWithNullMarkupDetails() {
        double result = pricingEngineHelper.getMarkUpForHotels(null, 1000.0);
        Assert.assertEquals("Should return 0 for null markup details", 0.0, result, 0);
    }

    @Test
    public void testGetMarkUpForHotelsWithZeroPrice() {
        MarkUpDetails markUpDetails = new MarkUpDetails();
        markUpDetails.setMarkupEligible(true);
        double result = pricingEngineHelper.getMarkUpForHotels(markUpDetails, 0.0);
        Assert.assertEquals("Should return 0 for zero price", 0.0, result, 0);
    }

    @Test
    public void testGetMarkUpForHotelsWithNegativePrice() {
        MarkUpDetails markUpDetails = new MarkUpDetails();
        markUpDetails.setMarkupEligible(true);
        double result = pricingEngineHelper.getMarkUpForHotels(markUpDetails, -100.0);
        Assert.assertEquals("Should return 0 for negative price", 0.0, result, 0);
    }

    @Test
    public void testGetMarkUpForHotelsWithIneligibleMarkup() {
        MarkUpDetails markUpDetails = new MarkUpDetails();
        markUpDetails.setMarkupEligible(false);
        double result = pricingEngineHelper.getMarkUpForHotels(markUpDetails, 1000.0);
        Assert.assertEquals("Should return 0 for ineligible markup", 0.0, result, 0);
    }

    @Test
    public void testBuildMarkUpConfigWithNullMarkupDetails() {
        MarkUpConfig markUpConfig = new MarkUpConfig();
        markUpConfig.setText("Test text");
        MarkUpConfig result = pricingEngineHelper.buildMarkUpConfig(null, markUpConfig);
        Assert.assertNull("Should return null for null markup details", result);
    }

    @Test
    public void testBuildMarkUpConfigWithNullMarkupConfig() {
        MarkUpDetails markUpDetails = new MarkUpDetails();
        markUpDetails.setMarkupEligible(true);
        MarkUpConfig result = pricingEngineHelper.buildMarkUpConfig(markUpDetails, null);
        Assert.assertNull("Should return null for null markup config", result);
    }

    @Test
    public void testBuildMarkUpConfigWithIneligibleMarkup() {
        MarkUpDetails markUpDetails = new MarkUpDetails();
        markUpDetails.setMarkupEligible(false);
        MarkUpConfig markUpConfig = new MarkUpConfig();
        markUpConfig.setText("Test text");
        MarkUpConfig result = pricingEngineHelper.buildMarkUpConfig(markUpDetails, markUpConfig);
        Assert.assertNull("Should return null for ineligible markup", result);
    }

    @Test
    public void testBuildMarkUpConfigWithAllProperties() {
        MarkUpDetails markUpDetails = new MarkUpDetails();
        markUpDetails.setMarkupEligible(true);
        markUpDetails.setEditable(true);
        
        MarkUpConfig markUpConfig = new MarkUpConfig();
        markUpConfig.setText("Test markup text");
        markUpConfig.setFlagValue(true);
        markUpConfig.setHoverText("Test hover text");
        markUpConfig.setCtaUrl("http://test.com");
        markUpConfig.setCtaText("Test CTA");
        
        MarkUpConfig result = pricingEngineHelper.buildMarkUpConfig(markUpDetails, markUpConfig);
        
        Assert.assertNotNull("Result should not be null", result);
        Assert.assertEquals("Text should match", "Test markup text", result.getText());
        Assert.assertTrue("Flag value should match", result.isFlagValue());
        Assert.assertEquals("Hover text should match", "Test hover text", result.getHoverText());
        Assert.assertEquals("CTA URL should be set for editable markup", "http://test.com", result.getCtaUrl());
        Assert.assertEquals("CTA text should be set for editable markup", "Test CTA", result.getCtaText());
    }

    @Test
    public void testGetMarkUpForHotelsWithNullMarkupMap() {
        MarkUpDetails markUpDetails = new MarkUpDetails();
        markUpDetails.setMarkupEligible(true);
        markUpDetails.setMarkupMap(null);
        
        double result = pricingEngineHelper.getMarkUpForHotels(markUpDetails, 1000.0);
        Assert.assertEquals("Should return 0 for null markup map", 0.0, result, 0);
    }

    @Test
    public void testGetMarkUpForHotelsWithMissingDHMarkup() {
        MarkUpDetails markUpDetails = new MarkUpDetails();
        markUpDetails.setMarkupEligible(true);
        markUpDetails.setMarkupMap(new HashMap<>());
        // Set only IH markup, not DH
        markUpDetails.getMarkupMap().put("IH", new MarkUp());
        
        MDC.put(MDCHelper.MDCKeys.COUNTRY.getStringValue(), "DOM");
        double result = pricingEngineHelper.getMarkUpForHotels(markUpDetails, 1000.0);
        Assert.assertEquals("Should return 0 when DH markup is missing for domestic", 0.0, result, 0);
        MDC.clear();
    }

    @Test
    public void testGetMarkUpForHotelsWithMissingIHMarkup() {
        MarkUpDetails markUpDetails = new MarkUpDetails();
        markUpDetails.setMarkupEligible(true);
        markUpDetails.setMarkupMap(new HashMap<>());
        // Set only DH markup, not IH
        markUpDetails.getMarkupMap().put("DH", new MarkUp());
        
        MDC.put(MDCHelper.MDCKeys.COUNTRY.getStringValue(), "INTL");
        double result = pricingEngineHelper.getMarkUpForHotels(markUpDetails, 1000.0);
        Assert.assertEquals("Should return 0 when IH markup is missing for international", 0.0, result, 0);
        MDC.clear();
    }
}
