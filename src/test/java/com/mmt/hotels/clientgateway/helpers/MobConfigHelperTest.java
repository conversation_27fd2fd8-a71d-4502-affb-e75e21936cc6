package com.mmt.hotels.clientgateway.helpers;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.MobConfigPropsConsul;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.pms.*;
import com.mmt.hotels.clientgateway.response.moblanding.CardAction;
import com.mmt.hotels.clientgateway.response.moblanding.CardInfo;
import com.mmt.hotels.clientgateway.response.searchHotels.FilterConditions;
import com.mmt.hotels.clientgateway.restexecutors.PolyglotRestExecutor;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.model.polyglot.PolyglotTranslation;
import com.mmt.hotels.model.response.pricing.RangePrice;
import com.mmt.hotels.pojo.listing.personalization.CardData;
import com.mmt.propertymanager.config.PropertyManager;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.rocksdb.Cache;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;
import static org.mockito.Mockito.when;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class MobConfigHelperTest {

	@InjectMocks
	MobConfigHelper mobConfigHelper;

	@Mock
	MobConfigPropsConsul mobConfigPropsConsul;

	@Mock
	ObjectMapperUtil objectMapperUtil;

	@Mock
	CacheManager cacheManager;

	@Mock
	CommonConfigConsul commonConfigConsul;

	@Mock
	PropertyManager propManager;
	
	@Mock
	MobConfigProps mobConfigProps;
	
	@Mock
	CommonHelper commonHelper;

	@Mock
	PolyglotRestExecutor polyglotRestExecutor;

	@Mock
	CommonResponseTransformer commonResponseTransformer;

	@Mock
	PolyglotHelper polyglotHelper;

	private void mockPmsVersion(String currentVersion){
		Mockito.when(propManager.getProperty(Mockito.anyString(), Mockito.anyObject())).thenReturn(mobConfigProps);
		Mockito.when(mobConfigProps.version()).thenReturn(currentVersion);

	}
	private void mockConfigJson(String configJson){
		Mockito.when(propManager.getProperty(Mockito.anyString(), Mockito.anyObject())).thenReturn(mobConfigProps);
		Mockito.when(mobConfigProps.configJson()).thenReturn(configJson);
	}


	@Test
	public void testConvertCardDataToCardInfoMapForLPG() {
		// Setup
		MobConfigHelper mobConfigHelper = new MobConfigHelper();
		Map<String, CardData> hesCardDataMap = new HashMap<>();
		CardData cardData = new CardData();
		cardData.setIndex(1);
		cardData.setCardSubType("GCCCARD");
		cardData.setCardId("LPG");
		cardData.setTitleText("Lowest Price Guarantee");
		cardData.setSubText("Get 2X the difference if you find our MMT exclusive hotels cheaper online anywhere else");
		cardData.setIconUrl("https://promos.makemytrip.com/GCC/MiscIcons/MMTexclusivetagborder.png");
		cardData.setBgColor("#FDF7E9");

		// Setup CardAction
		com.mmt.hotels.pojo.listing.personalization.CardAction cardAction = new com.mmt.hotels.pojo.listing.personalization.CardAction();
		cardAction.setTitle("View T&Cs");
		cardAction.setWebViewUrl("https://promos.makemytrip.com/gcc-mmt-exclusive-lpg-terms.html");
		cardData.setCardAction(Collections.singletonList(cardAction));
		cardData.setTemplateId("exclusive_v2");
		hesCardDataMap.put("key", cardData);

		// Execute
		Map<String, CardInfo> cardInfoMap = mobConfigHelper.convertCardDataToCardInfoMap(hesCardDataMap);

		// Verify
		Assert.assertNotNull(cardInfoMap);
		Assert.assertEquals(1, cardInfoMap.size());
		CardInfo cardInfo = cardInfoMap.get("key");
		Assert.assertNotNull(cardInfo);
		Assert.assertEquals(Integer.valueOf(1), cardInfo.getIndex());
		Assert.assertEquals("GCCCARD", cardInfo.getSubType());
		Assert.assertEquals("LPG", cardInfo.getId());
		Assert.assertEquals("Lowest Price Guarantee", cardInfo.getTitleText());
		Assert.assertEquals("Get 2X the difference if you find our MMT exclusive hotels cheaper online anywhere else", cardInfo.getSubText());
		Assert.assertEquals("https://promos.makemytrip.com/GCC/MiscIcons/MMTexclusivetagborder.png", cardInfo.getIconURL());
		Assert.assertEquals("#FDF7E9", cardInfo.getBgColor());
		Assert.assertEquals("exclusive_v2", cardInfo.getTemplateId());

		// Verify CardAction
		Assert.assertNotNull(cardInfo.getCardAction());
		Assert.assertEquals(1, cardInfo.getCardAction().size());
		CardAction action = cardInfo.getCardAction().get(0);
		Assert.assertEquals("View T&Cs", action.getTitle());
		Assert.assertEquals("https://promos.makemytrip.com/gcc-mmt-exclusive-lpg-terms.html", action.getWebViewUrl());
	}

	@Test
	public void getPersuasionPlaceHoldersToShowTest() throws Exception{
		//consul
		Field consulFlag = MobConfigHelper.class.getDeclaredField("consulFlag");
		consulFlag.setAccessible(true);
		consulFlag.set(mobConfigHelper,true);
		Map<String, List<String>> mp = new HashMap<>();
		mp.put("abcd", Arrays.asList("x","y"));
		Mockito.when(mobConfigPropsConsul.getPersuasionPlaceHoldersToShow()).thenReturn(mp);

		Map<String,List<String>> res = mobConfigHelper.getPersuasionPlaceHoldersToShow();
		Assert.assertEquals(2,res.get("abcd").size());

		//pms
		consulFlag.set(mobConfigHelper,false);
		mp.put("xyz",Arrays.asList("helloWorld"));
		Mockito.when(mobConfigProps.persuasionPlaceHoldersToShow()).thenReturn(mp);

		Map<String,List<String>> res1 = mobConfigHelper.getPersuasionPlaceHoldersToShow();
		Assert.assertEquals("helloWorld",res1.get("xyz").get(0));
	}


	@Test
	public void getHotelMobConfigAsStringTest() throws Exception {
		mockPmsVersion("");
		Map<String, String> headerMap = new HashMap<>();


		MDC.put(MDCHelper.MDCKeys.LANGUAGE.getStringValue(),"eng");
		Path path = Paths.get("src/test/resources/mock-polyglot.json");
		String response = null;
		try {
			Stream<String> lines = Files.lines(path);
			response = lines.collect(Collectors.joining("\n"));
            lines.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        PolyglotTranslation polyglotTranslation = objectMapperUtil.getObjectFromJson(response, PolyglotTranslation.class, DependencyLayer.POLYGLOT);


//		Mockito.when(polyglotRestExecutor.getMobGenPolyglotTranslation()).thenReturn(polyglotTranslation);
        Map<String, String> countryAndCity = new HashMap<>();
        countryAndCity.put("country_code", "IN");
        countryAndCity.put("city", "DEL");
        Mockito.when(objectMapperUtil.getJsonFromObject(Mockito.any(), Mockito.any())).thenReturn("json");
        Mockito.when(commonHelper.getCountryAndCityCodeFromHeader(headerMap)).thenReturn(countryAndCity);
        assertThat(mobConfigHelper.getHotelMobConfigAsString(null, "", headerMap), containsString("json"));

        mockPmsVersion("7");
        Mockito.when(objectMapperUtil.getJsonFromObject(Mockito.any(), Mockito.any())).thenReturn("json");
        assertThat(mobConfigHelper.getHotelMobConfigAsString("7", "", headerMap), containsString("json"));

        mockPmsVersion("8");
        Mockito.when(objectMapperUtil.getJsonFromObject(Mockito.any(), Mockito.any())).thenReturn("json");
		JsonFactory factory = new JsonFactory();
		ObjectMapper mapper = new ObjectMapper(factory);
		Map<String, JsonNode> configJsonNodeMapEnglish = new HashMap<>();
		String configJson = "{\"message\": \"Success\"}";
		JsonNode jsonNode = mapper.readTree(configJson);
		configJsonNodeMapEnglish.put("", jsonNode);
		ReflectionTestUtils.setField(mobConfigHelper, "configJsonNodeMapEnglish", configJsonNodeMapEnglish);
		assertThat(mobConfigHelper.getHotelMobConfigAsString("7","",headerMap), containsString("json"));

		mockPmsVersion("d");
		mockConfigJson("json");

		Mockito.when(objectMapperUtil.getJsonFromObject(Mockito.any(), Mockito.any())).thenReturn("json");
		assertThat(mobConfigHelper.getHotelMobConfigAsString("e","",headerMap), containsString("json"));

		mockPmsVersion("8");
		mockConfigJson("json");

		assertThat(mobConfigHelper.getHotelMobConfigAsString("7","",headerMap), containsString("json"));

		mockPmsVersion("8");
		Mockito.when(objectMapperUtil.getJsonFromObject(Mockito.any(), Mockito.any())).thenReturn("json");
		assertThat(mobConfigHelper.getHotelMobConfigAsString("8","",headerMap), containsString("json"));

		mockPmsVersion("9");
		mockConfigJson("json");

		assertThat(mobConfigHelper.getHotelMobConfigAsString("9","",headerMap), containsString("json"));

		mockPmsVersion("9");
		mockConfigJson("json");

		Mockito.when(objectMapperUtil.getJsonFromObject(Mockito.any(), Mockito.any())).thenThrow(new JsonParseException(DependencyLayer.CLIENTGATEWAY, ErrorType.MARSHALLING, "errorCode", "errorMsg"));
		assertThat(mobConfigHelper.getHotelMobConfigAsString("9","",headerMap), is(notNullValue()));
	}

	@Test
	public void getConfigJsonForLangVariantTest() throws Exception {
		Map<String, JsonNode> configJsonNodeMapEnglish = new HashMap<>();
		JsonNode node = JsonNodeFactory.instance.objectNode();
		configJsonNodeMapEnglish.put("A", node);
		ReflectionTestUtils.setField(mobConfigHelper, "configJsonNodeMapEnglish", configJsonNodeMapEnglish);
		Assert.assertNotNull(mobConfigHelper.getConfigJsonForLangVariant("eng", "A"));

		Map<String, JsonNode> configJsonNodeMapHindi = new HashMap<>();
		configJsonNodeMapHindi.put("B", node);
		ReflectionTestUtils.setField(mobConfigHelper, "configJsonNodeMapHindi", configJsonNodeMapHindi);
		Assert.assertNotNull(mobConfigHelper.getConfigJsonForLangVariant("hin", "B"));

		Map<String, JsonNode> configJsonNodeMapArabic = new HashMap<>();
		configJsonNodeMapArabic.put("C", node);
		ReflectionTestUtils.setField(mobConfigHelper, "configJsonNodeMapArabic", configJsonNodeMapArabic);
		Assert.assertNotNull(mobConfigHelper.getConfigJsonForLangVariant("ara", "C"));
	}

	@Test
	public void testGetFilterConditions(){
		ReflectionTestUtils.setField(mobConfigHelper, "consulFlag", true);
		Mockito.when(mobConfigPropsConsul.getFilterConditions()).thenReturn(new FilterConditions());
		FilterConditions res = mobConfigHelper.getFilterConditions();
		Assert.assertNotNull(res);

		ReflectionTestUtils.setField(mobConfigHelper, "consulFlag", false);
		Mockito.when(mobConfigProps.filterConditions()).thenReturn(new FilterConditions());
		FilterConditions res1 = mobConfigHelper.getFilterConditions();
		Assert.assertNotNull(res1);
	}

	@Test
	public void testGetSrcRegionSpecificData() {
		// Setup
		HotelMobConfig configResponse = new HotelMobConfig.Builder().build();
		Map<String, String> httpHeaderMap = new HashMap<>();
		Map<String,String> mp = new HashMap<>();
		mp.put(Constants.HEADER_COUNTRY_CODE,"hello");
		when(commonHelper.getCountryAndCityCodeFromHeader(new HashMap<>())).thenReturn(mp);
		when(mobConfigPropsConsul.getSourceRegionSpecificDataMapping()).thenReturn(new HashMap<>());
		ReflectionTestUtils.setField(mobConfigHelper, "consulFlag", true);


		// Run the test
		mobConfigHelper.getSrcRegionSpecificData(configResponse, httpHeaderMap);

		// Verify the results
	}

	@Test
	public void testGetConfigJson(){
		ReflectionTestUtils.setField(mobConfigHelper, "consulFlag", true);
		Mockito.when(mobConfigPropsConsul.getConfigJsonA()).thenReturn("A");
		Mockito.when(mobConfigPropsConsul.getConfigJsonB()).thenReturn("B");
		Mockito.when(mobConfigPropsConsul.getConfigJsonC()).thenReturn("C");
		Mockito.when(mobConfigPropsConsul.getConfigJsonD()).thenReturn("D");
		Mockito.when(mobConfigPropsConsul.getConfigJson()).thenReturn("Z");

		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getConfigJson", "A");
		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getConfigJson", "B");
		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getConfigJson", "C");
		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getConfigJson", "D");
		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getConfigJson", "Z");

		ReflectionTestUtils.setField(mobConfigHelper, "consulFlag", false);

		Mockito.when(propManager.getProperty(Mockito.anyString(), Mockito.anyObject())).thenReturn(mobConfigProps);


		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getConfigJson", "A");
		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getConfigJson", "B");
		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getConfigJson", "C");
		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getConfigJson", "D");
		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getConfigJson", "Z");

	}

	@Test
	public void tesGetCurrentVersionOfConfig(){
		ReflectionTestUtils.setField(mobConfigHelper, "consulFlag", true);

		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getCurrentVersionOfConfig", "A");
		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getCurrentVersionOfConfig", "B");
		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getCurrentVersionOfConfig", "C");
		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getCurrentVersionOfConfig", "D");
		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getCurrentVersionOfConfig", "Z");

		ReflectionTestUtils.setField(mobConfigHelper, "consulFlag", false);

		Mockito.when(propManager.getProperty(Mockito.anyString(), Mockito.anyObject())).thenReturn(mobConfigProps);


		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getCurrentVersionOfConfig", "A");
		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getCurrentVersionOfConfig", "B");
		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getCurrentVersionOfConfig", "C");
		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getCurrentVersionOfConfig", "D");
		ReflectionTestUtils.invokeMethod(mobConfigHelper, "getCurrentVersionOfConfig", "Z");

	}

	@Test
	public void testPopulateMobgenJsonBOMap(){
		ReflectionTestUtils.invokeMethod(mobConfigHelper, "populateMobgenJsonBOMap", "ABC");
	}



}
