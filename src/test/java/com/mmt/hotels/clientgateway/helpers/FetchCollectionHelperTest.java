package com.mmt.hotels.clientgateway.helpers;

import com.mmt.hotels.clientgateway.businessobjects.FilterDetail;
import com.mmt.hotels.clientgateway.request.FetchCollectionRequest;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

@RunWith(MockitoJUnitRunner.class)
public class FetchCollectionHelperTest {
    @InjectMocks
    private FetchCollectionHelper fetchCollectionHelper;

    @Mock
    PolyglotService polyglotService;

    @Test
    public void testShouldAddFiltersWithRequestDetails() {
        FetchCollectionRequest fetchCollectionRequest = new FetchCollectionRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource(FUNNEL_SOURCE_GROUP_BOOKING);
        requestDetails.setPageContext(PAGE_CONTEXT_LISTING);
        fetchCollectionRequest.setRequestDetails(requestDetails);
        Map<String, FilterDetail> landingFilterConditions = new HashMap<>();
        FilterDetail filterDetail = new FilterDetail();
        Set<String> set = new HashSet<>();
        set.add(FUNNEL_SOURCE_GROUP_BOOKING);
        set.add(PAGE_CONTEXT_LISTING);
        filterDetail.setFunnelSource(set);
        filterDetail.setPageContext(set);
        landingFilterConditions.put(MORE_FILTERS, filterDetail);
        Assert.assertTrue(fetchCollectionHelper.shouldAddFilters(MORE_FILTERS, fetchCollectionRequest, landingFilterConditions));
    }

    @Test
    public void testShouldAddFiltersWithoutRequestDetails() {
        FetchCollectionRequest fetchCollectionRequest = new FetchCollectionRequest();
        Map<String, FilterDetail> landingFilterConditions = new HashMap<>();
        FilterDetail filterDetail = new FilterDetail();
        Set<String> set = new HashSet<>();
        set.add(FUNNEL_SOURCE_GROUP_BOOKING);
        set.add(PAGE_CONTEXT_LISTING);
        filterDetail.setFunnelSource(set);
        filterDetail.setPageContext(set);
        landingFilterConditions.put(MORE_FILTERS, filterDetail);
        Assert.assertFalse(fetchCollectionHelper.shouldAddFilters(MORE_FILTERS, fetchCollectionRequest, landingFilterConditions));
    }

    @Test
    public void getTitleForBottomFilterSheetTest() {
        FetchCollectionRequest fetchCollectionRequest = new FetchCollectionRequest();
        SearchHotelsCriteria searchHotelsCriteria = new SearchHotelsCriteria();
        searchHotelsCriteria.setTripType("trip");
        fetchCollectionRequest.setSearchCriteria(searchHotelsCriteria);

        Map<String, String> purposeFilterMap = new HashMap<>();
        purposeFilterMap.put("trip", "You are on a trip");

        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("translatedData");
        String title = fetchCollectionHelper.getTitleForBottomFilterSheet(fetchCollectionRequest, purposeFilterMap);
        Assert.assertNotNull(title);
    }
}
