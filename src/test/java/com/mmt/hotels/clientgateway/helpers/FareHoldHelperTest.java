package com.mmt.hotels.clientgateway.helpers;

import com.mmt.hotels.clientgateway.response.BookNowDetails;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.response.MpFareHoldStatus;
import com.mmt.hotels.model.response.pricing.*;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class FareHoldHelperTest {

    @Mock
    protected PolyglotService polyglotService;
    @InjectMocks
    protected FareHoldHelper fareHoldHelper;

    @Test
    public void getMpFareHoldStatusTest() {
        HotelRates hotelRates = new HotelRates();
        hotelRates.setRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getRoomTypeDetails().setRoomType(new HashMap<String, RoomType>());
        RoomType roomType = new RoomType();
        roomType.setRoomTypeCode("abc");
        roomType.setRatePlanList(new HashMap<String, RatePlan>()); // fill this from 115
        com.mmt.hotels.model.response.pricing.RatePlan ratePlanCB = new RatePlan();
        ratePlanCB.setDisplayFare(new DisplayFare());
        ratePlanCB.setCancellationTimeline(new CancellationTimeline());
        roomType.getRatePlanList().put("abc", ratePlanCB);
        hotelRates.getRoomTypeDetails().getRoomType().put("abc", roomType);
        List<SpecialRequestCategory> categories = new ArrayList<>();
        MpFareHoldStatus mpFareHoldStatus = new MpFareHoldStatus();
        mpFareHoldStatus.setBookingAmount(0f);
        mpFareHoldStatus.setHoldEligible(true);
        mpFareHoldStatus.setExpiry(new Long(123456));
        mpFareHoldStatus.setEligibleForHoldBooking(true);
        ratePlanCB.setMpFareHoldStatus(mpFareHoldStatus);

        MpFareHoldStatus result = fareHoldHelper.getMpFareHoldStatus(hotelRates);
        Assert.assertNotNull(result);
        mpFareHoldStatus.setExpiry(null);
        result = fareHoldHelper.getMpFareHoldStatus(hotelRates);
        Assert.assertNull(result);
    }
    @Test
    public void getBookNowDetailsTest(){
        MpFareHoldStatus mpFareHoldStatus = new MpFareHoldStatus();
        mpFareHoldStatus.setBookingAmount(0f);
        mpFareHoldStatus.setHoldEligible(true);
        mpFareHoldStatus.setExpiry(new Long(123456));
        mpFareHoldStatus.setEligibleForHoldBooking(true);
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Remove Coupon {coupon_code}");
        BookNowDetails result= fareHoldHelper.getBookNowDetails(mpFareHoldStatus,true,"ABC");
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isDisabled());
        Assert.assertEquals(result.getSubText(),"Remove Coupon ABC");
        result= fareHoldHelper.getBookNowDetails(mpFareHoldStatus,false,null);
        Assert.assertNotNull(result);
        Assert.assertFalse(result.isDisabled());

    }
}
