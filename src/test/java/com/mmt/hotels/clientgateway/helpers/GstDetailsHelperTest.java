package com.mmt.hotels.clientgateway.helpers;

import com.mmt.hotels.clientgateway.request.gstDetails.TravellerGstDetails;
import com.mmt.hotels.clientgateway.thirdparty.response.AffiliateData;
import com.mmt.hotels.clientgateway.thirdparty.response.MyPartnerUserDetailsResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.TravellerGstInfo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

@RunWith(MockitoJUnitRunner.class)
public class GstDetailsHelperTest {
    private GstDetailsHelper gstDetailsHelper = new GstDetailsHelper();

    @Test
    public void isGstDetailsResponseValid_ReturnsTrue_WhenAllFieldsArePresent() {
        MyPartnerUserDetailsResponse response = new MyPartnerUserDetailsResponse();
        response.setData(new AffiliateData());
        response.getData().setTravellerGstInfo(new TravellerGstInfo());
        response.getData().getTravellerGstInfo().setGstDetails(Collections.singletonList(new TravellerGstDetails()));

        boolean result = gstDetailsHelper.isGstDetailsResponseValid(response);

        Assert.assertTrue(result);
    }

    @Test
    public void isGstDetailsResponseValid_ReturnsFalse_WhenResponseIsNull() {
        boolean result = gstDetailsHelper.isGstDetailsResponseValid(null);

        Assert.assertFalse(result);
    }

    @Test
    public void isGstDetailsResponseValid_ReturnsFalse_WhenDataIsNull() {
        MyPartnerUserDetailsResponse response = new MyPartnerUserDetailsResponse();

        boolean result = gstDetailsHelper.isGstDetailsResponseValid(response);

        Assert.assertFalse(result);
    }

    @Test
    public void isGstDetailsResponseValid_ReturnsFalse_WhenTravellerGstInfoIsNull() {
        MyPartnerUserDetailsResponse response = new MyPartnerUserDetailsResponse();
        response.setData(new AffiliateData());

        boolean result = gstDetailsHelper.isGstDetailsResponseValid(response);

        Assert.assertFalse(result);
    }

    @Test
    public void isGstDetailsResponseValid_ReturnsFalse_WhenGstDetailsIsNull() {
        MyPartnerUserDetailsResponse response = new MyPartnerUserDetailsResponse();
        response.setData(new AffiliateData());
        response.getData().setTravellerGstInfo(new TravellerGstInfo());

        boolean result = gstDetailsHelper.isGstDetailsResponseValid(response);

        Assert.assertFalse(result);
    }
}