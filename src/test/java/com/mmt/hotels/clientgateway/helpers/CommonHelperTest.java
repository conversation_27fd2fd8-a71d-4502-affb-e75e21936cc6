package com.mmt.hotels.clientgateway.helpers;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierRequest;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.TrafficSourceConstants;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.AuthenticationErrors;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.AuthenticationException;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.TrafficSource;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.pms.SourceRegionSpecificDataConfig;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.modification.RatePreviewRequest;
import com.mmt.hotels.clientgateway.request.wishlist.WishListedHotelsDetailRequest;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.restexecutors.HydraExecutor;
import com.mmt.hotels.clientgateway.restexecutors.PokusExperimentExecutor;
import com.mmt.hotels.clientgateway.restexecutors.UserServiceExecutor;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.*;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.request.UserLocation;
import com.mmt.hotels.model.request.flyfish.FlyFishReviewsRequest;
import com.mmt.hotels.model.request.matchmaker.InputHotel;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.txn.CouponInfo;
import com.mmt.hotels.model.response.txn.CouponStatus;
import com.mmt.hotels.model.response.txn.PersistedMultiRoomData;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDate;
import java.util.*;


import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.any;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class CommonHelperTest {

	@InjectMocks
	CommonHelper commonHelper;

	@Spy
	ObjectMapperUtil objectMapperUtil;

	@Mock
	private UserServiceResponse userServiceResponse;

	@Mock
	private BaseSearchRequest baseSearchRequest;

	@Mock
	MetricAspect metricAspect;

	@Mock
	private CommonConfigHelper commonConfigHelper;

	@Mock
	private MobConfigHelper mobConfigHelper;

	private static ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();

	static {
		pool.setCorePoolSize(10);
		pool.setMaxPoolSize(10);
		pool.setQueueCapacity(10);
		pool.setThreadNamePrefix("filterConfigPoolExecutor");
		pool.initialize();
	}

	@Mock
	Environment env;
	
	@Mock
	MetricErrorLogger metricErrorLogger;

	@Mock
	PokusExperimentExecutor pokusExperimentExecutor;

	@Mock
	UserServiceExecutor userServiceExecutor;

	@Mock
	HydraExecutor hydraExecutor;

	@Mock
	PolyglotService polyglotService;
	@Mock
	Utility utility;

	@Mock
	DateUtil dateUtil;

	@Mock
	CommonConfigConsul commonConfigConsul;

	private static ThreadPoolTaskExecutor userServiceThreadPool = new ThreadPoolTaskExecutor();
	
	private static ThreadPoolTaskExecutor hydraServiceThreadPool = new ThreadPoolTaskExecutor();

	@Before
	public void init() {
		ObjectMapper mapper = new ObjectMapper();
		mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
		List<String> nonAltAccoPropertiesList = new ArrayList<>();
		nonAltAccoPropertiesList.add("Hostel");
		ReflectionTestUtils.setField(commonHelper, "nonAltAccoProperties", nonAltAccoPropertiesList);
		ReflectionTestUtils.setField(commonHelper, "utility", utility);
		ReflectionTestUtils.setField(commonHelper, "dateUtil", dateUtil);
		ReflectionTestUtils.setField(commonHelper, "getRateTraffic", Arrays.asList(TrafficSourceConstants.CMP_SKYSCANNER, TrafficSourceConstants.CMP_TRIVAGO));

		// Set up default behavior for utility.buildSourceTraffic
		when(utility.buildSourceTraffic(anyString(), any(DeviceDetails.class))).thenAnswer(invocation -> {
			String source = invocation.getArgument(0);
			DeviceDetails deviceDetails = invocation.getArgument(1);
			if (source == null) {
				return null;
			}
			if (deviceDetails != null && (CLIENT_DESKTOP.equalsIgnoreCase(deviceDetails.getBookingDevice()) ||
					BKG_DEVICE_PWA.equalsIgnoreCase(deviceDetails.getBookingDevice()))) {
				switch(source) {
					case TrafficSourceConstants.CMP_GOOGLE_FINDER_NEW:
						return "googlehoteldfinder_new";
					case TrafficSourceConstants.CMP_GOOGLE_FINDER_US:
						return "googlehoteldfinder_dh_us";
					case TrafficSourceConstants.CMP_GOOGLE_FINDER_AE:
						return "googlehoteldfinder_dh_ae";
					case TrafficSourceConstants.CMP_GOOGLE_FINDER:
						return "googlehoteldfinder";
					case TrafficSourceConstants.CMP_TRIVAGO:
						return "trivago";
					case TrafficSourceConstants.CMP_TAFI:
						return "Tafi";
					case TrafficSourceConstants.SEO:
						return "seo";
					case TrafficSourceConstants.SEM:
						return "sem";
					default:
						return null;
				}
			} else {
				if (source.toLowerCase().contains(TrafficSourceConstants.SEO.toLowerCase())) {
					return TrafficSourceConstants.SEO;
				}
				if (source.toLowerCase().contains(TrafficSourceConstants.SEM.toLowerCase())) {
					return TrafficSourceConstants.SEM;
				}
			}
			return null;
		});
	}

	static {
		hydraServiceThreadPool.setCorePoolSize(10);
		hydraServiceThreadPool.setMaxPoolSize(10);
		hydraServiceThreadPool.setQueueCapacity(10);
		hydraServiceThreadPool.setThreadNamePrefix("hydraServiceThreadPool");
		hydraServiceThreadPool.initialize();
		userServiceThreadPool.setCorePoolSize(10);
		userServiceThreadPool.setMaxPoolSize(10);
		userServiceThreadPool.setQueueCapacity(10);
		userServiceThreadPool.setThreadNamePrefix("userServiceThreadPool");
		userServiceThreadPool.initialize();
	}

	@Test
	public void processRequestTest() throws ClientGatewayException {
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		SearchCriteria searchCriteriaAvail = new AvailPriceCriteria();
		searchCriteriaAvail.setCountryCode("IN");
		searchCriteriaAvail.setCheckIn("2020-02-21");
		searchCriteriaAvail.setCheckOut("2020-02-22");
		SearchCriteria searchCriteria = new SearchCriteria();
		searchCriteria.setCountryCode("IN");
		searchCriteria.setCheckIn("2020-02-21");
		searchCriteria.setCheckOut("2020-02-22");
		baseSearchRequest.setRequestDetails(new RequestDetails());
		baseSearchRequest.getRequestDetails().setFunnelSource("HOMESTAY");
		baseSearchRequest.getRequestDetails().setPageContext("REVIEW");
		baseSearchRequest.getRequestDetails().setSiteDomain("US");
		baseSearchRequest.setDeviceDetails(new DeviceDetails());
		baseSearchRequest.getDeviceDetails().setDeviceId("test");
		baseSearchRequest.setExpData("{APE:36,BNPL:T,MRS:T,PDO:PN,MCUR:T,ADDON:T,CHPC:T,AARI:T,NLP:Y,RCPN:T,EMIDT:2}");
		Mockito.when(env.getProperty(Mockito.anyString())).thenReturn("1");
		ReflectionTestUtils.setField(commonHelper, "userServiceThreadPool", userServiceThreadPool);
		ReflectionTestUtils.setField(commonHelper, "hydraServiceThreadPool", hydraServiceThreadPool);
		baseSearchRequest.getRequestDetails().setVisitorId("test");
		PokusExperimentResponse pokusExperimentResponse = new PokusExperimentResponse();
		pokusExperimentResponse.setPerLobMap(new HashMap<>());
		PokusAssignVariantResponse pokusAssignVariantResponse = new PokusAssignVariantResponse();
		pokusAssignVariantResponse.setMetadataValues(new HashMap<>());
		pokusExperimentResponse.getPerLobMap().put("HOTEL", pokusAssignVariantResponse);
		//Mockito.when(pokusExperimentExecutor.getPokusExperimentResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(pokusExperimentResponse);
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("mmt-auth", "mmt-auth");
		baseSearchRequest.getRequestDetails().setFirstTimeUserState(3);
		baseSearchRequest.setCohertVar(new CohertVar());
		baseSearchRequest.getDeviceDetails().setDeviceType("MOBILE");
		ReflectionTestUtils.setField(commonHelper, "cohortValid", true);
		FeatureFlags featureFlags = new FeatureFlags();
		featureFlags.setNoOfCoupons(1);
		baseSearchRequest.setFeatureFlags(featureFlags);
		Assert.assertNotNull(commonHelper.processRequest((AvailPriceCriteria) searchCriteriaAvail, baseSearchRequest, headers));
		baseSearchRequest.getRequestDetails().setPageContext("LISTING");
		Assert.assertNotNull(commonHelper.processRequest(searchCriteria, baseSearchRequest, headers));
		baseSearchRequest.getRequestDetails().setPageContext("DETAIL");
		Assert.assertNotNull(commonHelper.processRequest(searchCriteria, baseSearchRequest, headers));
		baseSearchRequest.getRequestDetails().setPageContext("ALTACCOLANDING");
		Assert.assertNotNull(commonHelper.processRequest(searchCriteria, baseSearchRequest, headers));
		baseSearchRequest.getRequestDetails().setPageContext("PAGE_CONTEXT_HOMEPAGE");
		Assert.assertNotNull(commonHelper.processRequest(searchCriteria, baseSearchRequest, headers));
		baseSearchRequest.getRequestDetails().setPageContext("");
		Assert.assertNotNull(commonHelper.processRequest(searchCriteria, baseSearchRequest, headers));
		baseSearchRequest.getRequestDetails().setFunnelSource("GETAWAY");
		Assert.assertNotNull(commonHelper.processRequest(searchCriteria, baseSearchRequest, headers));
	}

	@Test
	public void getMMTAuthTest(){
		Map<String, String> headerMap  = new HashMap<>();
		headerMap.put("backup_auth", "authinMap");
		Assert.assertNull(commonHelper.getMMTAuth(headerMap, "ANDROID"));

		headerMap.clear();
		headerMap.put("backup_auth","mmtAuth=\"MAT123\"");
		Assert.assertNotNull(commonHelper.getMMTAuth(headerMap, "ANDROID"));
		Assert.assertEquals("MAT123",commonHelper.getMMTAuth(headerMap,"ANDROID"));

		headerMap.clear();
		headerMap.put("mmt-auth","MAT456");
		Assert.assertEquals("MAT456",commonHelper.getMMTAuth(headerMap,"DESKTOP"));
	}

	@Test(expected=Exception.class)
	public void getMMTAuthTest_Exception(){
		Map<String, String> headerMap  = new HashMap<>();
		headerMap.put("backup_auth","mmtAuth");
		commonHelper.getMMTAuth(headerMap, "ANDROID");
	}

	@Test
	public void getMcidTest(){
		Map<String, String> headerMap  = new HashMap<>();
		headerMap.put("backup_auth", "authinMap");
		headerMap.put(Constants.ANDROID_MCID, "id");
		Assert.assertNotNull(commonHelper.getMcId(headerMap, "ANDROID"));
	}

	@Test
	public void testGetInboundCurrencyCode(){
		Map<String,String> currCityMap = new HashMap<>();
		currCityMap.put("IN", "INR");
		
		List<String> enableInboundExpDeviceList = new ArrayList<>();
		enableInboundExpDeviceList.add("DESKTOP");

		Mockito.when(commonConfigHelper.getCurrCityMap()).thenReturn(currCityMap);
		Mockito.when(commonConfigHelper.getEbableInboundExpDeviceList()).thenReturn(enableInboundExpDeviceList);
		
		Assert.assertNotNull(commonHelper.getInboundCurrencyCode("", "", "DESKTOP"));
		Assert.assertNotNull(commonHelper.getInboundCurrencyCode("IN", "US", "DESKTOP"));
		Assert.assertNotNull(commonHelper.getInboundCurrencyCode("US", "", "DESKTOP"));
	}

	@Test
	public void testIsJsonString(){
		Assert.assertTrue(commonHelper.isJsonString("{\"data\":\"\"}"));
		Assert.assertFalse(commonHelper.isJsonString("text"));
	}
	
	@Test
	public void updateIdContextTest() {
		String idContext = commonHelper.updateIdContext("B2C", "ANDROID");
		Assert.assertEquals("MOB", idContext);
		idContext = commonHelper.updateIdContext("B2C", "IOS");
		Assert.assertEquals("MOB", idContext);
		idContext = commonHelper.updateIdContext("B2C", "MSITE");
		Assert.assertEquals("MOB", idContext);
		idContext = commonHelper.updateIdContext("B2C", "PWA");
		Assert.assertEquals("MOB", idContext);
		idContext = commonHelper.updateIdContext("CORP", "ANDROID");
		Assert.assertEquals("CORP", idContext);
		idContext = commonHelper.updateIdContext("B2C", "DESKTOP");
		Assert.assertEquals("B2C", idContext);
	}

	@Test
	public void testUpdateLatLngFromHeader() {
		Map<String, String> headerMap = new HashMap<>();
		headerMap.put("X-Akamai-Edgescape", "georegion=104,country_code=IN,region_code=DL,city=NEWDELHI,lat=28.60,long=77.20,timezone=GMT*****,continent=AS,throughput=vhigh,bw=5000,asnum=24560,location_id=0");
		SearchCriteria searchCriteria = new SearchCriteria();
		commonHelper.updateLatLngFromHeader(searchCriteria, headerMap);
		Assert.assertNotNull(searchCriteria.getLat());
	}

	@Test
	public void testGetCountryAndCityCodeFromHeader() {
		Map<String, String> headerMap = new HashMap<>();
		headerMap.put("X-Akamai-Edgescape", "georegion=104,country_code=IN,region_code=DL,city=NEWDELHI,lat=28.60,long=77.20,timezone=GMT*****,continent=AS,throughput=vhigh,bw=5000,asnum=24560,location_id=0");
		Assert.assertNotNull(commonHelper.getCountryAndCityCodeFromHeader(headerMap));
	}

	@Test
	public void testChangeDateToEpoch(){
		Assert.assertNotNull(ReflectionTestUtils.invokeMethod(commonHelper, "changeDateStringToInteger", "2022-05-18"));
	}

	@Test
	public void testGetAuthToken(){
		Map<String, String> headerMap = new HashMap<>();
		headerMap.put("mmt-auth","MAT1618ebdb84b078ea1c6510a73b62d41b5355c4444257c5c959b991402362bce8f64a77a4eacf4c2e07d5990c08dd1edd3P");
		Assert.assertNotNull(commonHelper.getAuthToken(headerMap));
	}

	@Test
	public void testSanitizeInput(){
		String data = "test";
		Assert.assertNotNull(commonHelper.sanitizeInput(data));
	}

	@Test
	public void testGetUserFirstTimeState(){
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		baseSearchRequest.setDeviceDetails(new DeviceDetails());
		baseSearchRequest.setRequestDetails(new RequestDetails());
		baseSearchRequest.getDeviceDetails().setBookingDevice("ANDROID");
		ExtendedUser extendedUser = new ExtendedUser();
		extendedUser.setUuid("uuid123");
		extendedUser.setProfileType("profileType123");
		ReflectionTestUtils.invokeMethod(commonHelper, "getUserFirstTimeState", "ANDROID", 1, "IN", new ExtendedUser(), "",null,null);
		ReflectionTestUtils.invokeMethod(commonHelper, "buildFlightBookerRequest",  extendedUser, "");
		extendedUser.setUuid(null);
		extendedUser.setProfileId("profile123");
		ReflectionTestUtils.invokeMethod(commonHelper, "buildFlightBookerRequest",  extendedUser, "");

		ReflectionTestUtils.invokeMethod(commonHelper, "buildUserStateFeatureRequest", baseSearchRequest, new SearchCriteria(), new ExtendedUser(), "");
		Assert.assertNotNull(commonHelper.executeHydraService("CTDEL","ANDROID","abcd","",1,"IN", new ExtendedUser(),null,""));
	}

	@Test
	public void isJsonString_stringProvidedIsNotJson_returnFalse(){
		Assert.assertFalse(commonHelper.isJsonString(""));
	}

	@Test
	public void isJsonString_stringProvidedIsJson_returnTrue(){
		Assert.assertTrue(commonHelper.isJsonString("{\n" +
				"  \"cumulativeRating\": 4.5,\n" +
				"  \"totalReviewsCount\": 89,\n" +
				"  \"totalRatingCount\": 162,\n" +
				"}\n"));
	}

	@Test(expected=Exception.class)
	public void updateReviewsRequest_userInfoIsEmpty() throws ClientGatewayException {
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		baseSearchRequest.setDeviceDetails(new DeviceDetails());
		baseSearchRequest.setRequestDetails(new RequestDetails());
		baseSearchRequest.getDeviceDetails().setBookingDevice("ANDROID");
		SearchCriteria searchCriteria = new SearchCriteria();
		searchCriteria.setCityCode("");
		ReflectionTestUtils.invokeMethod(commonHelper, "getUserLobKey", "","","");
		ReflectionTestUtils.invokeMethod(commonHelper, "getDeviceLobKey", "","");
		ReflectionTestUtils.invokeMethod(commonHelper, "checkValidUserId", new ExtendedUser(),"");
		commonHelper.updateReviewsRequest(null,new FlyFishReviewsRequest());
		Map<String,String> mcid = new HashMap<>();
		mcid.put("mcid","");
		ReflectionTestUtils.invokeMethod(commonHelper, "isFlightBooker", "ANDROID","CTDEL","abcd",new ExtendedUser(),"",mcid);
		Assert.assertEquals("",commonHelper.getMcId(mcid,"ANDROID"));

		HydraLastBookedFlightResponse response = new HydraLastBookedFlightResponse();
		response.setData(new HashMap<>());
		response.getData().put("CTDEL",new HashMap<>());
		response.getData().get("CTDEL").put("abc",new HydraLastBookedResponseEntity());
		response.getData().get("CTDEL").get("abc").getValue().setFromCity("CTDEL");
		response.getData().get("CTDEL").get("abc").getValue().setToCity("CTBOI");
		response.getData().get("CTDEL").get("abc").getValue().setToCity("CTBOI");
		Map<String,List<String>> map = new HashMap<>();
		map.put("abc", Collections.singletonList("abc"));
		Mockito.when(commonConfigHelper.getMapHotelAndFlightCityt()).thenReturn(map);
		Mockito.when(hydraExecutor.getHydraLastBookedFlightResponse(Mockito.any(),Mockito.anyString())).thenReturn(response);
		ExtendedUser extendedUser = new ExtendedUser();
		extendedUser.setUuid("UUID");
		extendedUser.setProfileType("PERSONAL");
		ReflectionTestUtils.invokeMethod(commonHelper, "isFlightBooker", "ANDROID","CTDEL","abcd",extendedUser,"",mcid);
	}

	@Test
	public void testBlockPAHExperimentOn(){
		Map<String,String> expMap = new HashMap<>();
		expMap.put("blockPAH","true");
		boolean b = commonHelper.blockPAHExperimentOn(expMap);
		Assert.assertTrue(b);
		expMap.put("blockPAH","false");
		b = commonHelper.blockPAHExperimentOn(expMap);
		Assert.assertFalse(b);
	}

	@Test(expected = AuthenticationException.class)
	public  void processRequestForBkgModFailureTest() throws Exception{
		RatePreviewRequest ratePreviewRequest = new Gson().fromJson("{\"channel\":\"B2C\",\"checkin\":\"2020-10-05\",\"checkout\":\"2020-10-06\",\"city_code\":\"2820046943342890302\",\"country_code\":\"IN\",\"currency\":\"INR\",\"hash_key\":\"\",\"hotel_ids\":[\"3447164777143948329\"],\"id_context\":\"B2C\",\"sub_vendor\":\"\",\"token\":null,\"booking_id\":\"HTLQW576HP\",\"flavour\":\"IOS\",\"app_version\":\"7.8.1\",\"device_id\":\"bc123ed132f\",\"room_criteria\":[{\"room_stay_candidates\":[{\"guest_counts\":[{\"count\":\"2\",\"ages\":[3,5]}]}],\"room_code\":\"45000000263\",\"rate_plan_code\":\"990000423293\",\"supplier_id\":\"ingoibibo\",\"sub_vendor\":\"\"}],\"traveller_email_commId\":[\"sendalltravellersassociatedwithbooker\"]}",
				RatePreviewRequest.class);
		Map<String,String> httpHeaderMap = new HashMap<>();
		httpHeaderMap.put("REGION","IN");
		httpHeaderMap.put("mmtAuth","a2sacasd");
		httpHeaderMap.put("usr-mcid","sadsasda");
		commonHelper.processRequestForBkgMod(httpHeaderMap,ratePreviewRequest,Constants.PAGE_CONTEXT_REVIEW);

	}

	@Test
	public  void processRequestForBkgModSuccessTest() throws Exception{
		RatePreviewRequest ratePreviewRequest = new Gson().fromJson("{\"channel\":\"B2C\",\"checkin\":\"2020-10-05\",\"checkout\":\"2020-10-06\",\"city_code\":\"2820046943342890302\",\"country_code\":\"IN\",\"currency\":\"INR\",\"hash_key\":\"\",\"hotel_ids\":[\"3447164777143948329\"],\"id_context\":\"B2C\",\"sub_vendor\":\"\",\"token\":null,\"booking_id\":\"HTLQW576HP\",\"flavour\":\"IOS\",\"app_version\":\"7.8.1\",\"device_id\":\"bc123ed132f\",\"room_criteria\":[{\"room_stay_candidates\":[{\"guest_counts\":[{\"count\":\"2\",\"ages\":[3,5]}]}],\"room_code\":\"45000000263\",\"rate_plan_code\":\"990000423293\",\"supplier_id\":\"ingoibibo\",\"sub_vendor\":\"\"}],\"traveller_email_commId\":[\"sendalltravellersassociatedwithbooker\"]}",
				RatePreviewRequest.class);
		Map<String,String> httpHeaderMap = new HashMap<>();
		httpHeaderMap.put("REGION","IN");
		httpHeaderMap.put("mmt-auth","a2sacasd");
		httpHeaderMap.put("usr-mcid","sadsasda");
		UserServiceResponse userServiceResponse = new UserServiceResponse();
		userServiceResponse.setResult(new UserServiceResult());
		userServiceResponse.getResult().setExtendedUser(new ExtendedUser());
		userServiceResponse.getResult().getExtendedUser().setUuid("ABC");
		userServiceResponse.getResult().getExtendedUser().setProfileType("BUSINESS");
		userServiceResponse.getResult().getExtendedUser().setAccountId("ACDFd");
		userServiceResponse.getResult().getExtendedUser().setProfileId("MMT1231");
		userServiceResponse.getResult().getExtendedUser().setLoginInfoList(new ArrayList<>());
		userServiceResponse.getResult().getExtendedUser().getLoginInfoList().add(new UserLoginInfo());
		userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(0).setLoginId("************");
		userServiceResponse.getResult().getExtendedUser().getLoginInfoList().get(0).setLoginType(Constants.LOGIN_TYPE_MOBILE);

		ReflectionTestUtils.setField(commonHelper, "userServiceThreadPool", userServiceThreadPool);
		ReflectionTestUtils.setField(commonHelper, "hydraServiceThreadPool", hydraServiceThreadPool);
		Mockito.when(userServiceExecutor.getUserServiceResponse(Mockito.any(), Mockito.any() , Mockito.any(), Mockito.any(),  Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(userServiceResponse);
		Mockito.when(env.getProperty(Mockito.anyString())).thenReturn("1");
		commonHelper.processRequestForBkgMod(httpHeaderMap,ratePreviewRequest,Constants.PAGE_CONTEXT_REVIEW);

	}

	@Test
	public void getVariantKeysTest(){
		PokusExperimentResponse pokusExperimentResponse = new PokusExperimentResponse();
		pokusExperimentResponse.setPerLobMap(new HashMap<>());
		pokusExperimentResponse.getPerLobMap().put("HOTEL", new PokusAssignVariantResponse());
		pokusExperimentResponse.getPerLobMap().get("HOTEL").setVariantKey("test");
		pokusExperimentResponse.getPerLobMap().get("HOTEL").setExperimentDetailsList(new ArrayList<>());
		PokusExperimentDetails pokusExperimentDetails = new PokusExperimentDetails();
		pokusExperimentDetails.setVariantId(1);
		pokusExperimentDetails.setExperimentVersion(1);
		pokusExperimentDetails.setExperimentVersion(1);
		pokusExperimentResponse.getPerLobMap().get("HOTEL").getExperimentDetailsList().add(pokusExperimentDetails);
		ReflectionTestUtils.invokeMethod(commonHelper, "getVariantKeys", pokusExperimentResponse);
		ReflectionTestUtils.invokeMethod(commonHelper, "getValidExpList", pokusExperimentResponse);
	}

	@Test
	public void processRequestCommonsTest() throws ClientGatewayException {
		WishListedHotelsDetailRequest wishListedHotelsDetailRequest = getWishListedHotelsDetailRequest();
		ReflectionTestUtils.setField(commonHelper, "userServiceThreadPool", userServiceThreadPool);
		ReflectionTestUtils.setField(commonHelper, "hydraServiceThreadPool", hydraServiceThreadPool);
		PokusExperimentResponse pokusExperimentResponse = new PokusExperimentResponse();
		pokusExperimentResponse.setPerLobMap(new HashMap<>());
		PokusAssignVariantResponse pokusAssignVariantResponse = new PokusAssignVariantResponse();
		pokusAssignVariantResponse.setMetadataValues(new HashMap<>());
		pokusExperimentResponse.getPerLobMap().put("HOTEL", pokusAssignVariantResponse);
		//Mockito.when(pokusExperimentExecutor.getPokusExperimentResponse(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(pokusExperimentResponse);
		Map<String, String> headers = new HashMap<>();
		headers.put("mmt-auth", "mmt-auth");
		ReflectionTestUtils.setField(commonHelper, "cohortValid", true);
		FeatureFlags featureFlags = new FeatureFlags();
		featureFlags.setNoOfCoupons(1);
		Assert.assertNotNull(commonHelper.processRequest(wishListedHotelsDetailRequest, headers));
	}

	private WishListedHotelsDetailRequest getWishListedHotelsDetailRequest() {
		return new Gson().fromJson("{\"deviceDetails\":{\"appVersion\":\"8.6.4.RC1\",\"bookingDevice\":\"ANDROID\",\"deviceId\":\"311c8e73900a8d98\",\"deviceType\":\"Mobile\"," +
				"\"networkType\":\"WiFi\",\"resolution\":\"xhdpi\"},\"requestDetails\":{\"channel\":\"Native\",\"couponCount\":3,\"funnelSource\":\"HOTELS\"," +
				"\"idContext\":\"B2C\",\"loggedIn\":true,\"pageContext\":\"DETAIL\",\"siteDomain\":\"in\",\"visitNumber\":1," + "\"visitorId\":\"74856825370536007467679936889328709665\"}," +
				"\"searchCriteria\":{\"hotelIds\":[\"201412311314381986\",\"200803050906255532\",\"201007011525075813\",\"201904081604352311\"],\"visitNumber\":1," +
				"\"visitorId\":\"74856825370536007467679936889328709665\",\"cityCode\":\"CTDEL\",\"locationId\":\"CTDEL\",\"locationType\":\"city\",\"countryCode\":\"IN\"," +
				"\"roomStayCandidates\":[{\"adultCount\":2,\"childAges\":[],\"positionsForAgeLessThan1\":[]}]}," +
				"\"imageDetails\":{\"categories\":[{\"count\":4,\"height\":252,\"imageFormat\":\"webp\",\"type\":\"H\",\"width\":459}],\"types\":[\"professional\",\"traveller\"]}," +
				"\"reviewDetails\":{\"otas\":[\"MMT\",\"TA\",\"MANUAL\",\"OTHER\",\"EXT\"],\"tagTypes\":[\"BASE\",\"WHAT_GUESTS_SAY\"]}}", WishListedHotelsDetailRequest.class);
	}
	@Test
	public void getFunnelSourceTest() {
		List<UserServiceResponse> userServiceResponseList  = new ArrayList<>();
		UserServiceResponse userServiceResponse = new UserServiceResponse();
		UserServiceResult userServiceResult = new UserServiceResult();
		ExtendedUser extendedUser = new ExtendedUser();
		extendedUser.setProfileType("CTA");
		extendedUser.setAffiliateId("MYPARTNER");
		userServiceResult.setExtendedUser(extendedUser);
		userServiceResponse.setResult(userServiceResult);
		userServiceResponseList.add(userServiceResponse);

		String resp = commonHelper.getFunnelSource("", userServiceResponseList);
		Assert.assertEquals(resp, "MYPARTNER");
	}
	@Test
	public void getApplicationIdTest()
	{
		String pageContext="LISTING";
		Mockito.when(env.getProperty("APPLICATIONID.LISTING")).thenReturn("410");
		Assert.assertEquals(commonHelper.getApplicationId(pageContext), 410);
		Mockito.when(env.getProperty("APPLICATIONID.")).thenReturn("410");
		Assert.assertEquals(commonHelper.getApplicationId(""), 410);
	}

	@Test
	public void checkValidHotelTest(){
		ListingSearchRequest searchHotelsRequest = new ListingSearchRequest();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		searchHotelsRequest.setMatchMakerDetails(new MatchMakerRequest());
		MatchMakerRequest matchMakerRequest = new MatchMakerRequest();
		List<InputHotel> hotels = new ArrayList<>();
		InputHotel inputHotel = new InputHotel();
		inputHotel.setHotelId("123456");
		hotels.add(inputHotel);
		matchMakerRequest.setHotels(hotels);
		searchHotelsRequest.setMatchMakerDetails(matchMakerRequest);
		hotelEntity.setId("123456");
		Assert.assertFalse(commonHelper.checkValidHotel(searchHotelsRequest,hotelEntity));
	}


	private Map<String, String> getHeaderMapTest() {
		Map<String, String> header = new HashMap<>();
		header.put("language","eng");
		return header;
	}

	private BaseSearchRequest getBaseSearchRequestTestObj() {
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		baseSearchRequest.setCorrelationKey("test");
		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setDeviceId("test");
		baseSearchRequest.setDeviceDetails(deviceDetails);
		MatchMakerRequest matchMakerRequest = new MatchMakerRequest();
		matchMakerRequest.setHotels(new ArrayList<>());
		baseSearchRequest.setMatchMakerDetails(matchMakerRequest);
		return baseSearchRequest;
	}

	private SearchCriteria getSearchCriteriaTestObj() {
		SearchCriteria searchCriteria = new SearchCriteria();
		searchCriteria.setCurrency("INR");
		searchCriteria.setCountryCode("IN");
		searchCriteria.setLocationId("CTDEL");
		searchCriteria.setCheckIn("2023-05-26");
		searchCriteria.setCheckOut("2023-05-27");
		searchCriteria.setRoomStayCandidates(new ArrayList<>());
		List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
		RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
		roomStayCandidate.setAdultCount(1);
		roomStayCandidates.add(roomStayCandidate);
		List<Integer> childAges = new ArrayList<>();
		childAges.add(2); childAges.add(5);
		roomStayCandidate.setChildAges(childAges);
		searchCriteria.setRoomStayCandidates(roomStayCandidates);
		return searchCriteria;
	}

	@Test
	public void checkIfLoggedInTest() {
		List<UserServiceResponse> userServiceResponseList = new ArrayList<>();
		userServiceResponseList.add(new UserServiceResponse());
		userServiceResponseList.get(0).setResult(new UserServiceResult());
		userServiceResponseList.get(0).getResult().setExtendedUser(new ExtendedUser());
		userServiceResponseList.get(0).getResult().getExtendedUser().setUuid("test-uuid");

		Assert.assertTrue(commonHelper.checkIfLoggedIn(userServiceResponseList));

		userServiceResponseList.get(0).getResult().getExtendedUser().setUuid(null);
		Assert.assertFalse(commonHelper.checkIfLoggedIn(userServiceResponseList));

	}

	@Test
	public void getUserFirstTimeStateTest() {
		ExtendedUser extendedUser = new ExtendedUser();
		extendedUser.setUuid("UUID");

		Set<String> matchedSegment = new HashSet<>();
		matchedSegment.add("r1043");
		ReflectionTestUtils.invokeMethod(commonHelper, "getUserFirstTimeState", "android", 3, "IN", extendedUser, "mcId", null, matchedSegment);

		matchedSegment = new HashSet<>();
		matchedSegment.add("r104");
		ReflectionTestUtils.invokeMethod(commonHelper, "getUserFirstTimeState", "android", 1, "IN", extendedUser, "mcId", null, matchedSegment);
		ReflectionTestUtils.invokeMethod(commonHelper, "getUserFirstTimeState", "android", 2, "IN", extendedUser, "mcId", null, matchedSegment);
	}

	@Test
	public void updateExperimentDataMapTest() {
		PokusExperimentResponse pokusExperimentResponse = new PokusExperimentResponse();
		pokusExperimentResponse.setPerLobMap(new HashMap<>());
		ReflectionTestUtils.invokeMethod(commonHelper, "updateExperimentDataMap", pokusExperimentResponse, null, null);

		Map<String,PokusAssignVariantResponse> perLobMap = new HashMap<>();
		Map<String,Object> metadataValues = new HashMap<>();
		metadataValues.put("pricerIntlV2", true);
		metadataValues.put("ddAPI", true);
		metadataValues.put("HSC", true);
		String variantKey = "variantKey";
		PokusAssignVariantResponse pokusAssignVariantResponse = new PokusAssignVariantResponse();
		pokusAssignVariantResponse.setMetadataValues(metadataValues);
		pokusAssignVariantResponse.setVariantKey(variantKey);
		pokusExperimentResponse.setPerLobMap(perLobMap);
		perLobMap.put("HOTEL", pokusAssignVariantResponse);
		ReflectionTestUtils.invokeMethod(commonHelper, "updateExperimentDataMap", pokusExperimentResponse, new CommonModifierRequest(), "HOTEL");
	}

	@Test
	public void updateExperimentDataMapTest2() {
		PokusExperimentResponse pokusExperimentResponse = new PokusExperimentResponse();
		pokusExperimentResponse.setPerLobMap(new HashMap<>());
		ReflectionTestUtils.invokeMethod(commonHelper, "updateExperimentDataMap", pokusExperimentResponse, null, null);

		Map<String,PokusAssignVariantResponse> perLobMap = new HashMap<>();
		Map<String,Object> metadataValues = new HashMap<>();
		metadataValues.put("pricerV2Pax", true);
		metadataValues.put("ddAPI", true);
		metadataValues.put("HSC", true);
		String variantKey = "variantKey";
		PokusAssignVariantResponse pokusAssignVariantResponse = new PokusAssignVariantResponse();
		pokusAssignVariantResponse.setMetadataValues(metadataValues);
		pokusAssignVariantResponse.setVariantKey(variantKey);
		pokusExperimentResponse.setPerLobMap(perLobMap);
		perLobMap.put("HOTEL", pokusAssignVariantResponse);
		ReflectionTestUtils.invokeMethod(commonHelper, "updateExperimentDataMap", pokusExperimentResponse, new CommonModifierRequest(), "HOTEL");
	}

	@Test
	public void updateExperimentDataMapTest3() {
		PokusExperimentResponse pokusExperimentResponse = new PokusExperimentResponse();
		pokusExperimentResponse.setPerLobMap(new HashMap<>());
		ReflectionTestUtils.invokeMethod(commonHelper, "updateExperimentDataMap", pokusExperimentResponse, null, null);

		Map<String,PokusAssignVariantResponse> perLobMap = new HashMap<>();
		Map<String,Object> metadataValues = new HashMap<>();
		metadataValues.put("pricerV2Full", true);
		metadataValues.put("ddAPI", true);
		metadataValues.put("HSC", true);
		String variantKey = "variantKey";
		PokusAssignVariantResponse pokusAssignVariantResponse = new PokusAssignVariantResponse();
		pokusAssignVariantResponse.setMetadataValues(metadataValues);
		pokusAssignVariantResponse.setVariantKey(variantKey);
		pokusExperimentResponse.setPerLobMap(perLobMap);
		perLobMap.put("HOTEL", pokusAssignVariantResponse);
		ReflectionTestUtils.invokeMethod(commonHelper, "updateExperimentDataMap", pokusExperimentResponse, new CommonModifierRequest(), "HOTEL");
	}

	@Test
	public void isMetaPricerV2Test1() {
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		baseSearchRequest.setRequestDetails(new RequestDetails());
		baseSearchRequest.getRequestDetails().setFunnelSource(Constants.FUNNEL_SOURCE_HOMESTAY);
		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put("intlMetaV2", "true");
		expDataMap.put("domMetaV2", "true");
		expDataMap.put("altAccoMetaV2", "true");
		expDataMap.put("gccMetaV2", "true");
		baseSearchRequest.setExpDataMap(expDataMap);
		String trafficSource = Constants.GOOGLEHOTELDFINDER;
		SearchCriteria searchCriteria = new SearchCriteria();
		searchCriteria.setCountryCode("IN");
		ReflectionTestUtils.invokeMethod(commonHelper, "isMetaPricerV2", baseSearchRequest, trafficSource, searchCriteria);
	}
	@Test
	public void isMetaPricerV2Test2() {
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put("intlMetaV2", "true");
		expDataMap.put("domMetaV2", "true");
		expDataMap.put("altAccoMetaV2", "true");
		expDataMap.put("gccMetaV2", "true");
		baseSearchRequest.setExpDataMap(expDataMap);
		String trafficSource = Constants.GOOGLEHOTELDFINDER;
		SearchCriteria searchCriteria = new SearchCriteria();
		searchCriteria.setCountryCode("AUS");
		ReflectionTestUtils.invokeMethod(commonHelper, "isMetaPricerV2", baseSearchRequest, trafficSource, searchCriteria);
	}
	@Test
	public void isMetaPricerV2Test3() {
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put("intlMetaV2", "true");
		expDataMap.put("domMetaV2", "true");
		expDataMap.put("altAccoMetaV2", "true");
		expDataMap.put("gccMetaV2", "true");
		baseSearchRequest.setExpDataMap(expDataMap);
		String trafficSource = Constants.GOOGLEHOTELDFINDER + Constants.UNDERSCORE + Constants.DOM_HOTEL + Constants.UNDERSCORE + Constants.AE;
		SearchCriteria searchCriteria = new SearchCriteria();
		searchCriteria.setCountryCode("IN");
		ReflectionTestUtils.invokeMethod(commonHelper, "isMetaPricerV2", baseSearchRequest, trafficSource, searchCriteria);
	}
	@Test
	public void isMetaPricerV2Test4() {
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		baseSearchRequest.setRequestDetails(new RequestDetails());
		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put("intlMetaV2", "true");
		expDataMap.put("domMetaV2", "false");
		expDataMap.put("altAccoMetaV2", "true");
		expDataMap.put("gccMetaV2", "true");
		baseSearchRequest.setExpDataMap(expDataMap);
		String trafficSource = Constants.GOOGLEHOTELDFINDER;
		SearchCriteria searchCriteria = new SearchCriteria();
		searchCriteria.setCountryCode("IN");
		ReflectionTestUtils.invokeMethod(commonHelper, "isMetaPricerV2", baseSearchRequest, trafficSource, searchCriteria);
	}

	@Test
	public void isMetaPricerV2Test5() {
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		baseSearchRequest.setRequestDetails(new RequestDetails());
		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put("intlMetaV2", "true");
		expDataMap.put("domMetaV2", "false");
		expDataMap.put("altAccoMetaV2", "true");
		expDataMap.put("gccMetaV2", "true");
		baseSearchRequest.setExpDataMap(expDataMap);
		String trafficSource = "SEO|Test|Test2";
		SearchCriteria searchCriteria = new SearchCriteria();
		searchCriteria.setCountryCode("IN");
		String actual = ReflectionTestUtils.invokeMethod(commonHelper, "isMetaPricerV2", baseSearchRequest, trafficSource, searchCriteria);
		Assert.assertEquals("true",actual);

		trafficSource = "Seo|Test|Test2";
		actual = ReflectionTestUtils.invokeMethod(commonHelper, "isMetaPricerV2", baseSearchRequest, trafficSource, searchCriteria);
		Assert.assertEquals("true",actual);

		trafficSource = "Wishlist";
		actual = ReflectionTestUtils.invokeMethod(commonHelper, "isMetaPricerV2", baseSearchRequest, trafficSource, searchCriteria);
		Assert.assertEquals("true",actual);

		trafficSource = "Ad";
		actual = ReflectionTestUtils.invokeMethod(commonHelper, "isMetaPricerV2", baseSearchRequest, trafficSource, searchCriteria);
		Assert.assertEquals("false",actual);
	}

	@Test
	public void setDefaultSearchContextTest() {
		Map<String, Integer> defaultSearchConfigMap = new HashMap<>();
		defaultSearchConfigMap.put(checkInAddition, 2);
		defaultSearchConfigMap.put(checkOutAddition, 4);
		defaultSearchConfigMap.put(defaultRooms, 1);
		defaultSearchConfigMap.put(defaultGuest, 2);
		Mockito.when(commonConfigHelper.getDefaultSearchContext()).thenReturn(defaultSearchConfigMap);
		ListingSearchRequestV2 listingSearchRequestV2 = new ListingSearchRequestV2();
		commonHelper.setDefaultSearchContext(listingSearchRequestV2);
		assertEquals(listingSearchRequestV2.getSearchCriteria().getCheckIn(), LocalDate.now().plusDays(2).toString());
		assertEquals(listingSearchRequestV2.getSearchCriteria().getCheckOut(), LocalDate.now().plusDays(4).toString());
	}

	@Test
	public void hydraRetryTestForRetry() throws ClientGatewayException {
		ExtendedUser extendedUser = new ExtendedUser();
		extendedUser.setUuid("uuid123");
		extendedUser.setProfileType("profileType123");
		RestConnectorException ex = new RestConnectorException(DependencyLayer.HYDRA, ErrorType.CONNECTIVITY, "500", "test");
		Mockito.when(commonConfigHelper.getHydraRetryCount()).thenReturn(3);
		Mockito.when(hydraExecutor.getHydraMatchedSegment(Mockito.any(), Mockito.anyString(), Mockito.anyString())).thenThrow(ex);
		ReflectionTestUtils.invokeMethod(commonHelper, "getHydraMatchedSegment", extendedUser, "pwa", "test", "test_mcID", "global");
	}
	@Test
	public void hydraRetryTestForNoRetry() throws ClientGatewayException {
		ExtendedUser extendedUser = new ExtendedUser();
		extendedUser.setUuid("uuid123");
		extendedUser.setProfileType("profileType123");
		Mockito.when(hydraExecutor.getHydraMatchedSegment(Mockito.any(), Mockito.anyString(), Mockito.anyString())).thenReturn(new HydraUserSegmentResponse());
		ReflectionTestUtils.invokeMethod(commonHelper, "getHydraMatchedSegment",extendedUser, "pwa", "test", "test_mcID", "global");
	}

	@Test
	public void overridePokusExperiment_test() {
		commonConfigHelper.getOverridePokusConfig();
		Map<String, Map<String, Set<String>>> map = new HashMap<>();
		Map<String, Set<String>> entry = new HashMap<>();
		Set<String> funnelSet = new HashSet<>();
		funnelSet.add("*");
		entry.put("funnel", funnelSet);
		Set<String> idContextSet = new HashSet<>();
		idContextSet.add("*");
		entry.put("idContext", idContextSet);
		Set<String> domainSet = new HashSet<>();
		domainSet.add("*");
		entry.put("domain", domainSet);
		Set<String> langSet = new HashSet<>();
		langSet.add("ENG");
		entry.put("language", langSet);
		Set<String> countrySet = new HashSet<>();
		langSet.add("*");
		entry.put("countryCode", countrySet);
		Set<String> expValue = new HashSet<>();
		expValue.add("TRUE");
		entry.put("expValue", expValue);
		map.put("convFeeManthan", entry);
		Mockito.when(commonConfigHelper.getOverridePokusConfig()).thenReturn(map);
		Mockito.when(utility.checkCondition(Mockito.any(), Mockito.any())).thenReturn(true);
		commonHelper.overridePokusExperiment(getBaseSearchRequestTestObj2(), "eng", "In");
	}

	private BaseSearchRequest getBaseSearchRequestTestObj2() {
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setIdContext("B2C");
		requestDetails.setSiteDomain("IN");
		requestDetails.setFunnelSource("HOTELS");
		baseSearchRequest.setRequestDetails(requestDetails);
		baseSearchRequest.setCorrelationKey("test");
		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setDeviceId("test");
		baseSearchRequest.setDeviceDetails(deviceDetails);
		MatchMakerRequest matchMakerRequest = new MatchMakerRequest();
		matchMakerRequest.setHotels(new ArrayList<>());
		baseSearchRequest.setMatchMakerDetails(matchMakerRequest);
		baseSearchRequest.setExpDataMap(new HashMap<>());
		return baseSearchRequest;
	}

	@Test
	public void setPricerV2Flag_Test() {
		AvailPriceCriteria searchCriteria = new AvailPriceCriteria();
		List<AvailRoomsSearchCriteria> roomCriteria = new ArrayList<>();
		AvailRoomsSearchCriteria criteria = new AvailRoomsSearchCriteria();
		criteria.setRatePlanCode("MSE");
		roomCriteria.add(criteria);
		searchCriteria.setRoomCriteria(roomCriteria);
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		baseSearchRequest.setExpDataMap(new HashMap<>());
		ReflectionTestUtils.invokeMethod(commonHelper, "setPricerV2Flag",searchCriteria, baseSearchRequest);
	}

	@Test
	public void setPricerV2FlagNonMSE_Test() {
		AvailPriceCriteria searchCriteria = new AvailPriceCriteria();
		List<AvailRoomsSearchCriteria> roomCriteria = new ArrayList<>();
		AvailRoomsSearchCriteria criteria = new AvailRoomsSearchCriteria();
		criteria.setRatePlanCode("None");
		roomCriteria.add(criteria);
		searchCriteria.setRoomCriteria(roomCriteria);
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		baseSearchRequest.setExpDataMap(new HashMap<>());
		ReflectionTestUtils.invokeMethod(commonHelper, "setPricerV2Flag",searchCriteria, baseSearchRequest);
	}

	@Test
	public void testBuildCommonRequestForNonReviewPage() {
		BaseSearchRequest baseSearchRequest = mock(BaseSearchRequest.class);
		SearchCriteria searchCriteria = mock(SearchCriteria.class);

		when(baseSearchRequest.getCorrelationKey()).thenReturn("correlationKey");
		when(baseSearchRequest.getRequestDetails()).thenReturn(new RequestDetails());
		when(baseSearchRequest.getDeviceDetails()).thenReturn(new DeviceDetails());
		when(searchCriteria.getCityCode()).thenReturn("cityCode");
		when(searchCriteria.getLocationId()).thenReturn("locationId");
		when(searchCriteria.getCheckIn()).thenReturn("2024-04-12");
		when(searchCriteria.getCheckOut()).thenReturn("2024-04-14");
		when(searchCriteria.getCountryCode()).thenReturn("countryCode");
		List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
		RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
		roomStayCandidate.setRooms(3);
		roomStayCandidate.setAdultCount(6);
		roomStayCandidates.add(roomStayCandidate);
		when(searchCriteria.getRoomStayCandidates()).thenReturn(roomStayCandidates);
		CommonModifierRequest result = ReflectionTestUtils.invokeMethod(commonHelper,"buildCommonRequest",baseSearchRequest, searchCriteria, "mmtAuth", "mcid");

		// Assert
		assertEquals("correlationKey", result.getCorrelationKey());
		assertEquals("cityCode", result.getCityCode());
		assertEquals("countryCode", result.getCountryCode());
		assertEquals(Integer.valueOf(3), result.getRoomCount());
	}

	@Test
	public void testBuildCommonRequestForReviewPage() {
		BaseSearchRequest baseSearchRequest = mock(BaseSearchRequest.class);
		SearchCriteria searchCriteria = mock(SearchCriteria.class);

		when(baseSearchRequest.getCorrelationKey()).thenReturn("correlationKey");
		when(baseSearchRequest.getDeviceDetails()).thenReturn(new DeviceDetails());
		when(searchCriteria.getCityCode()).thenReturn("cityCode");
		when(searchCriteria.getLocationId()).thenReturn("locationId");
		when(searchCriteria.getCheckIn()).thenReturn("2024-04-12");
		when(searchCriteria.getCheckOut()).thenReturn("2024-04-14");
		when(searchCriteria.getCountryCode()).thenReturn("countryCode");
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setPageContext("REVIEW");
		when(baseSearchRequest.getRequestDetails()).thenReturn(requestDetails);
		List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
		RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
		roomStayCandidate.setAdultCount(2);
		roomStayCandidates.add(roomStayCandidate);
		when(searchCriteria.getRoomStayCandidates()).thenReturn(roomStayCandidates);
		CommonModifierRequest result = ReflectionTestUtils.invokeMethod(commonHelper,"buildCommonRequest",baseSearchRequest, searchCriteria, "mmtAuth", "mcid");

		// Assert
		assertEquals("correlationKey", result.getCorrelationKey());
		assertEquals("cityCode", result.getCityCode());
		assertEquals("countryCode", result.getCountryCode());
		assertEquals(Integer.valueOf(1), result.getRoomCount());
	}
	@Test
	public void modifyPokusBySource_UpdatesExpDataMap_WhenUserServiceResponseResultIsNull() {
		// Arrange
		when(userServiceResponse.getResult()).thenReturn(null);
		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put(MP_EXPRESS_CHECKOUT_EXP_KEY, "initialValue");
		expDataMap.put(MP_MOVE_TO_TDS_STRUCTURE_EXP_KEY, "initialValue");
		expDataMap.put(MP_GST_SAVING_EXP_KEY, "initialValue");
		expDataMap.put(MP_CUSTOMER_GST_EXP_KEY, "initialValue");
		when(baseSearchRequest.getExpDataMap()).thenReturn(expDataMap);

		// Act
		commonHelper.modifyPokusBySource(userServiceResponse, baseSearchRequest);

		// Assert
		assertNull(baseSearchRequest.getExpDataMap().get(MP_EXPRESS_CHECKOUT_EXP_KEY));
		assertNull(baseSearchRequest.getExpDataMap().get(MP_MOVE_TO_TDS_STRUCTURE_EXP_KEY));
		assertNull(baseSearchRequest.getExpDataMap().get(MP_GST_SAVING_EXP_KEY));
		assertNull(baseSearchRequest.getExpDataMap().get(MP_CUSTOMER_GST_EXP_KEY));
	}

	@Test
	public void modifyPokusBySource_UpdatesExpDataMap_WhenExtendedUserIsNotMyPartner() {
		// Arrange
		UserServiceResult userServiceResult = new UserServiceResult();
		ExtendedUser extendedUser = new ExtendedUser();
		extendedUser.setAffiliateId("B2C");
		userServiceResult.setExtendedUser(extendedUser);
		when(userServiceResponse.getResult()).thenReturn(userServiceResult);
		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put(MP_EXPRESS_CHECKOUT_EXP_KEY, "initialValue");
		expDataMap.put(MP_MOVE_TO_TDS_STRUCTURE_EXP_KEY, "initialValue");
		expDataMap.put(MP_GST_SAVING_EXP_KEY, "initialValue");
		expDataMap.put(MP_CUSTOMER_GST_EXP_KEY, "initialValue");
		when(baseSearchRequest.getExpDataMap()).thenReturn(expDataMap);

		// Act
		commonHelper.modifyPokusBySource(userServiceResponse, baseSearchRequest);

		// Assert
		// Assert
		assertNull(baseSearchRequest.getExpDataMap().get(MP_EXPRESS_CHECKOUT_EXP_KEY));
		assertNull(baseSearchRequest.getExpDataMap().get(MP_MOVE_TO_TDS_STRUCTURE_EXP_KEY));
		assertNull(baseSearchRequest.getExpDataMap().get(MP_GST_SAVING_EXP_KEY));
		assertNull(baseSearchRequest.getExpDataMap().get(MP_CUSTOMER_GST_EXP_KEY));
	}

	@Test
	public void modifyPokusBySource_UpdatesExpDataMap_WhenExtendedUserIsMyPartner() {
		// Arrange
		UserServiceResult userServiceResult = new UserServiceResult();
		ExtendedUser extendedUser = new ExtendedUser();
		extendedUser.setAffiliateId("MYPARTNER");
		userServiceResult.setExtendedUser(extendedUser);
		when(userServiceResponse.getResult()).thenReturn(userServiceResult);
		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put(MP_EXPRESS_CHECKOUT_EXP_KEY, "initialValue");
		expDataMap.put(MP_MOVE_TO_TDS_STRUCTURE_EXP_KEY, "initialValue");
		expDataMap.put(MP_GST_SAVING_EXP_KEY, "initialValue");
		when(baseSearchRequest.getExpDataMap()).thenReturn(expDataMap);

		// Act
		commonHelper.modifyPokusBySource(userServiceResponse, baseSearchRequest);

		// Assert
		assertEquals("true", baseSearchRequest.getExpDataMap().get(MP_EXPRESS_CHECKOUT_EXP_KEY));
		assertEquals("true", baseSearchRequest.getExpDataMap().get(MP_MOVE_TO_TDS_STRUCTURE_EXP_KEY));
		assertEquals("true", baseSearchRequest.getExpDataMap().get(MP_GST_SAVING_EXP_KEY));
		assertEquals("false", baseSearchRequest.getExpDataMap().get(MP_CUSTOMER_GST_EXP_KEY));

	}

	@Test
	public void testGetAppliedCoupon_WithAppliedCoupons() {
		PersistedMultiRoomData persistedData = Mockito.mock(PersistedMultiRoomData.class);

		CouponInfo couponInfo = new CouponInfo();
		couponInfo.setDdCoupon(true);
		List<CouponInfo> appliedCoupons = new ArrayList<>();
		appliedCoupons.add(couponInfo);

		Map<CouponStatus, List<CouponInfo>> couponInfoMap = new HashMap<>();
		couponInfoMap.put(CouponStatus.APPLIED, appliedCoupons);

		Mockito.when(persistedData.getCouponInfo()).thenReturn(couponInfoMap);

		CouponInfo result = commonHelper.getAppliedCoupon(persistedData);

		assertEquals(couponInfo, result);
	}

	@Test
	public void testGetAppliedCoupon_WithNoAppliedCoupons() {
		PersistedMultiRoomData persistedData = Mockito.mock(PersistedMultiRoomData.class);
		Map<CouponStatus, List<CouponInfo>> couponInfoMap = new HashMap<>();
		couponInfoMap.put(CouponStatus.APPLIED, new ArrayList<>()); // No applied coupons

		Mockito.when(persistedData.getCouponInfo()).thenReturn(couponInfoMap);

		CouponInfo result = commonHelper.getAppliedCoupon(persistedData);

		assertNull(result);
	}

	@Test
	public void testGetAppliedCoupon_WithEmptyCouponInfo() {
		PersistedMultiRoomData persistedData = Mockito.mock(PersistedMultiRoomData.class);
		Mockito.when(persistedData.getCouponInfo()).thenReturn(new HashMap<>()); // Empty coupon info

		CouponInfo result = commonHelper.getAppliedCoupon(persistedData);

		assertNull(result);
	}

	@Test
	public void testGetAppliedCoupon_WithNullCouponInfo() {
		PersistedMultiRoomData persistedData = Mockito.mock(PersistedMultiRoomData.class);
		Mockito.when(persistedData.getCouponInfo()).thenReturn(null); // Null coupon info

		CouponInfo result = commonHelper.getAppliedCoupon(persistedData);

		assertNull(result);
	}


	@Test
	public void updateCurrencyAndSource_Test() {
		SearchCriteria searchCriteria = new SearchCriteria();
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setSrCon("India");
		requestDetails.setSrCty("Lucknow");
		requestDetails.setSrcState("UP");
		Map<String, String> httpHeaderMap = new HashMap<>();
		httpHeaderMap.put("user-currency", "INR");
		httpHeaderMap.put("currency", "INR");
		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice("ANDROID");
		deviceDetails.setAppVersion("9.2.5");
		commonHelper.updateCurrencyAndSource(searchCriteria, requestDetails, httpHeaderMap, deviceDetails);
	}

	@Test
	public void buildFiltersToRemove_ShouldNotModifyMap_WhenFiltersToRemoveIsEmpty() {
		Map<com.mmt.hotels.filter.FilterGroup, Set<com.mmt.hotels.filter.Filter>> removeFilterMapCB = new HashMap<>();
		List<com.mmt.hotels.clientgateway.request.Filter> filtersToRemove = Collections.emptyList();

		commonHelper.buildFiltersToRemove(removeFilterMapCB, filtersToRemove);

		Assert.assertTrue(removeFilterMapCB.isEmpty());
	}

	@Test
	public void buildFiltersToRemove_ShouldAddFiltersToMap_WhenFiltersToRemoveIsNotEmpty() {
		Map<com.mmt.hotels.filter.FilterGroup, Set<com.mmt.hotels.filter.Filter>> removeFilterMapCB = new HashMap<>();
		List<com.mmt.hotels.clientgateway.request.Filter> filtersToRemove = new ArrayList<>();
		com.mmt.hotels.clientgateway.request.Filter filter = new com.mmt.hotels.clientgateway.request.Filter();
		filter.setFilterGroup(FilterGroup.PROPERTY_CATEGORY);
		filter.setFilterValue("value1");
		filtersToRemove.add(filter);

		commonHelper.buildFiltersToRemove(removeFilterMapCB, filtersToRemove);

		Assert.assertFalse(removeFilterMapCB.isEmpty());
		Assert.assertTrue(removeFilterMapCB.containsKey(com.mmt.hotels.filter.FilterGroup.PROPERTY_CATEGORY));
		Assert.assertEquals(1, removeFilterMapCB.get(com.mmt.hotels.filter.FilterGroup.PROPERTY_CATEGORY).size());
	}

	@Test
	public void buildFiltersToRemove_ShouldAddMultipleFiltersToMap_WhenFiltersToRemoveHasMultipleFilters() {
		Map<com.mmt.hotels.filter.FilterGroup, Set<com.mmt.hotels.filter.Filter>> removeFilterMapCB = new HashMap<>();
		List<com.mmt.hotels.clientgateway.request.Filter> filtersToRemove = new ArrayList<>();
		com.mmt.hotels.clientgateway.request.Filter filter1 = new com.mmt.hotels.clientgateway.request.Filter();
		filter1.setFilterGroup(FilterGroup.PROPERTY_CATEGORY);
		filter1.setFilterValue("value1");
		com.mmt.hotels.clientgateway.request.Filter filter2 = new com.mmt.hotels.clientgateway.request.Filter();
		filter2.setFilterGroup(FilterGroup.PROPERTY_CATEGORY);
		filter2.setFilterValue("value2");
		filtersToRemove.add(filter1);
		filtersToRemove.add(filter2);

		commonHelper.buildFiltersToRemove(removeFilterMapCB, filtersToRemove);

		Assert.assertFalse(removeFilterMapCB.isEmpty());
		Assert.assertTrue(removeFilterMapCB.containsKey(com.mmt.hotels.filter.FilterGroup.PROPERTY_CATEGORY));
		Assert.assertEquals(2, removeFilterMapCB.get(com.mmt.hotels.filter.FilterGroup.PROPERTY_CATEGORY).size());
	}

//	@Test
//	public void buildFiltersToRemove_ShouldHandleNullFilterGroup() {
//		Map<com.mmt.hotels.filter.FilterGroup, Set<com.mmt.hotels.filter.Filter>> removeFilterMapCB = new HashMap<>();
//		List<com.mmt.hotels.clientgateway.request.Filter> filtersToRemove = new ArrayList<>();
//		com.mmt.hotels.clientgateway.request.Filter filter = new com.mmt.hotels.clientgateway.request.Filter();
//		filter.setFilterGroup(null);
//		filter.setFilterValue("value1");
//		filtersToRemove.add(filter);
//
//		commonHelper.buildFiltersToRemove(removeFilterMapCB, filtersToRemove);
//
//		Assert.assertTrue(removeFilterMapCB.isEmpty());
//	}

	@Test
	public void updateUserGlobalInfo_test() {
		SearchCriteria searchCriteria = new SearchCriteria();
		Map<String, String> httpHeaderMap = new HashMap<>();
		httpHeaderMap.put("user-country", "USA");
		httpHeaderMap.put("entity-name", "global");
		ReflectionTestUtils.invokeMethod(commonHelper, "updateUserGlobalInfo", searchCriteria, httpHeaderMap);
		Assert.assertNotNull(searchCriteria.getUserGlobalInfo());
	}

	@Test
	public void updateUserGlobalInfo_testNullCase() {
		SearchCriteria searchCriteria = new SearchCriteria();
		Map<String, String> httpHeaderMap = new HashMap<>();
		ReflectionTestUtils.invokeMethod(commonHelper, "updateUserGlobalInfo", searchCriteria, httpHeaderMap);
		Assert.assertNull(searchCriteria.getUserGlobalInfo());
	}

	@Test
	public void validateWishListedSearch_WhenWishListedSearchIsFalse_ShouldNotModifyFeatureFlags() {
		// Arrange
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		FeatureFlags originalFeatureFlags = new FeatureFlags();
		originalFeatureFlags.setNoOfCoupons(5); // Some value that would change if feature flags were reset
		baseSearchRequest.setFeatureFlags(originalFeatureFlags);

		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setWishListedSearch(false);

		// Act
		ReflectionTestUtils.invokeMethod(commonHelper, "validateAndUpdateTheWishListedSearch", searchCriteria, baseSearchRequest);

		// Assert
		org.junit.jupiter.api.Assertions.assertSame(originalFeatureFlags, baseSearchRequest.getFeatureFlags());
		org.junit.jupiter.api.Assertions.assertEquals(5, baseSearchRequest.getFeatureFlags().getNoOfCoupons());
	}

	@Test
	public void validateWishListedSearch_WhenSearchCriteriaIsNotHotelsCriteria_ShouldNotModifyFeatureFlags() {
		// Arrange
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		FeatureFlags originalFeatureFlags = new FeatureFlags();
		originalFeatureFlags.setNoOfCoupons(5);
		baseSearchRequest.setFeatureFlags(originalFeatureFlags);

		SearchCriteria searchCriteria = new SearchCriteria(); // Not a SearchHotelsCriteria

		// Act
		ReflectionTestUtils.invokeMethod(commonHelper, "validateAndUpdateTheWishListedSearch", searchCriteria, baseSearchRequest);

		// Assert
		org.junit.jupiter.api.Assertions.assertSame(originalFeatureFlags, baseSearchRequest.getFeatureFlags());
		org.junit.jupiter.api.Assertions.assertEquals(5, baseSearchRequest.getFeatureFlags().getNoOfCoupons());
	}

	@Test
	public void validateWishListedSearch_WhenWishListedSearchIsTrueAndFeatureFlagsIsNull_ShouldCreateFeatureFlags() {
		// Arrange
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		baseSearchRequest.setFeatureFlags(null); // Explicitly set to null

		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setWishListedSearch(true);

		// Act
		ReflectionTestUtils.invokeMethod(commonHelper, "validateAndUpdateTheWishListedSearch", searchCriteria, baseSearchRequest);

		// Assert
		org.junit.jupiter.api.Assertions.assertNotNull(baseSearchRequest.getFeatureFlags());
		// Check some of the expected values from the method
		org.junit.jupiter.api.Assertions.assertFalse(baseSearchRequest.getFeatureFlags().isAddOnRequired());
		org.junit.jupiter.api.Assertions.assertTrue(baseSearchRequest.getFeatureFlags().isCoupon());
		org.junit.jupiter.api.Assertions.assertTrue(baseSearchRequest.getFeatureFlags().isDealOfTheDayRequired());
		org.junit.jupiter.api.Assertions.assertEquals(2, baseSearchRequest.getFeatureFlags().getNoOfCoupons());
	}

	@Test
	public void validateWishListedSearch_WhenWishListedSearchIsTrueAndFeatureFlagsExists_ShouldNotChangeFeatureFlags() {
		// Arrange
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		FeatureFlags featureFlags = new FeatureFlags();
		featureFlags.setNoOfCoupons(5);
		featureFlags.setCoupon(false);
		baseSearchRequest.setFeatureFlags(featureFlags);

		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setWishListedSearch(true);

		// Act
		ReflectionTestUtils.invokeMethod(commonHelper, "validateAndUpdateTheWishListedSearch", searchCriteria, baseSearchRequest);

		// Assert
		org.junit.jupiter.api.Assertions.assertSame(featureFlags, baseSearchRequest.getFeatureFlags());
		org.junit.jupiter.api.Assertions.assertEquals(5, baseSearchRequest.getFeatureFlags().getNoOfCoupons());
		org.junit.jupiter.api.Assertions.assertFalse(baseSearchRequest.getFeatureFlags().isCoupon());
	}

	@Test
	public void validateWishListedSearch_WhenWishListedSearchIsTrueAndRequestIsListingSearchRequest_ShouldAddImageDetails() {
		// Arrange
		ListingSearchRequest listingRequest = new ListingSearchRequest();
		listingRequest.setImageDetails(null); // Explicitly set to null

		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setWishListedSearch(true);

		// Act
		ReflectionTestUtils.invokeMethod(commonHelper, "validateAndUpdateTheWishListedSearch", searchCriteria, listingRequest);

		// Assert
		org.junit.jupiter.api.Assertions.assertNotNull(listingRequest.getImageDetails());
		org.junit.jupiter.api.Assertions.assertNotNull(listingRequest.getImageDetails().getCategories());
		org.junit.jupiter.api.Assertions.assertNotNull(listingRequest.getImageDetails().getTypes());
		org.junit.jupiter.api.Assertions.assertEquals(1, listingRequest.getImageDetails().getCategories().size());
		org.junit.jupiter.api.Assertions.assertEquals(1, listingRequest.getImageDetails().getTypes().size());

		ImageCategory category = listingRequest.getImageDetails().getCategories().get(0);
		org.junit.jupiter.api.Assertions.assertEquals(4, category.getCount());
		org.junit.jupiter.api.Assertions.assertEquals(330, category.getHeight());
		org.junit.jupiter.api.Assertions.assertEquals("webp", category.getImageFormat());
		org.junit.jupiter.api.Assertions.assertEquals("H", category.getType());
		org.junit.jupiter.api.Assertions.assertEquals(602, category.getWidth());

		org.junit.jupiter.api.Assertions.assertEquals("professional", listingRequest.getImageDetails().getTypes().get(0));
	}

	@Test
	public void validateWishListedSearch_WhenWishListedSearchIsTrueAndRequestIsListingSearchRequestWithExistingImageDetails_ShouldNotChangeImageDetails() {
		// Arrange
		ListingSearchRequest listingRequest = new ListingSearchRequest();
		ImageDetails originalImageDetails = new ImageDetails();
		List<ImageCategory> categories = new ArrayList<>();
		ImageCategory category = new ImageCategory();
		category.setCount(10);
		category.setHeight(400);
		category.setImageFormat("jpg");
		category.setType("X");
		category.setWidth(800);
		categories.add(category);
		originalImageDetails.setCategories(categories);

		List<String> types = new ArrayList<>();
		types.add("traveller");
		originalImageDetails.setTypes(types);

		listingRequest.setImageDetails(originalImageDetails);

		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setWishListedSearch(true);

		// Act
		ReflectionTestUtils.invokeMethod(commonHelper, "validateAndUpdateTheWishListedSearch", searchCriteria, listingRequest);

		// Assert
		org.junit.jupiter.api.Assertions.assertSame(originalImageDetails, listingRequest.getImageDetails());
		org.junit.jupiter.api.Assertions.assertEquals(1, listingRequest.getImageDetails().getCategories().size());
		org.junit.jupiter.api.Assertions.assertEquals(1, listingRequest.getImageDetails().getTypes().size());

		ImageCategory resultCategory = listingRequest.getImageDetails().getCategories().get(0);
		org.junit.jupiter.api.Assertions.assertEquals(10, resultCategory.getCount());
		org.junit.jupiter.api.Assertions.assertEquals(400, resultCategory.getHeight());
		org.junit.jupiter.api.Assertions.assertEquals("jpg", resultCategory.getImageFormat());
		org.junit.jupiter.api.Assertions.assertEquals("X", resultCategory.getType());
		org.junit.jupiter.api.Assertions.assertEquals(800, resultCategory.getWidth());

		org.junit.jupiter.api.Assertions.assertEquals("traveller", listingRequest.getImageDetails().getTypes().get(0));
	}

	@Test
	public void validateWishListedSearch_WhenWishListedSearchIsTrueAndExpDataMapIsNull_ShouldAddPdoPn() {
		// Arrange
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		baseSearchRequest.setExpDataMap(null); // Explicitly set to null

		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setWishListedSearch(true);

		// Act
		ReflectionTestUtils.invokeMethod(commonHelper, "validateAndUpdateTheWishListedSearch", searchCriteria, baseSearchRequest);

		// Assert
		org.junit.jupiter.api.Assertions.assertNotNull(baseSearchRequest.getExpDataMap());

		// Check PDO value
		org.junit.jupiter.api.Assertions.assertTrue(baseSearchRequest.getExpDataMap().containsKey("PDO"));
		org.junit.jupiter.api.Assertions.assertEquals("PN", baseSearchRequest.getExpDataMap().get("PDO"));

		// Check other experiment values
		org.junit.jupiter.api.Assertions.assertEquals("T", baseSearchRequest.getExpDataMap().get("BNPL"));
		org.junit.jupiter.api.Assertions.assertEquals("T", baseSearchRequest.getExpDataMap().get("MRS"));
		org.junit.jupiter.api.Assertions.assertEquals("T", baseSearchRequest.getExpDataMap().get("MCUR"));
		org.junit.jupiter.api.Assertions.assertEquals("T", baseSearchRequest.getExpDataMap().get("ADDON"));
		org.junit.jupiter.api.Assertions.assertEquals("T", baseSearchRequest.getExpDataMap().get("CHPC"));
		org.junit.jupiter.api.Assertions.assertEquals("T", baseSearchRequest.getExpDataMap().get("AARI"));
		org.junit.jupiter.api.Assertions.assertEquals("Y", baseSearchRequest.getExpDataMap().get("NLP"));
		org.junit.jupiter.api.Assertions.assertEquals("T", baseSearchRequest.getExpDataMap().get("RCPN"));
		org.junit.jupiter.api.Assertions.assertEquals("V3", baseSearchRequest.getExpDataMap().get("MMRVER"));
		org.junit.jupiter.api.Assertions.assertEquals("T", baseSearchRequest.getExpDataMap().get("BLACK"));
		org.junit.jupiter.api.Assertions.assertEquals("T", baseSearchRequest.getExpDataMap().get("FLTRPRCBKT"));
		org.junit.jupiter.api.Assertions.assertEquals("T", baseSearchRequest.getExpDataMap().get("LSTNRBY"));
		org.junit.jupiter.api.Assertions.assertEquals("DEFAULT", baseSearchRequest.getExpDataMap().get("HIS"));
		org.junit.jupiter.api.Assertions.assertEquals("T", baseSearchRequest.getExpDataMap().get("AIP"));
		org.junit.jupiter.api.Assertions.assertEquals("T", baseSearchRequest.getExpDataMap().get("APT"));
		org.junit.jupiter.api.Assertions.assertEquals("F", baseSearchRequest.getExpDataMap().get("SOU"));
		org.junit.jupiter.api.Assertions.assertEquals("F", baseSearchRequest.getExpDataMap().get("CV2"));
		org.junit.jupiter.api.Assertions.assertEquals("F", baseSearchRequest.getExpDataMap().get("CRI"));

		// Check that expData string is also set
		org.junit.jupiter.api.Assertions.assertNotNull(baseSearchRequest.getExpData());
		org.junit.jupiter.api.Assertions.assertTrue(baseSearchRequest.getExpData().contains("PDO:PN"));
		org.junit.jupiter.api.Assertions.assertTrue(baseSearchRequest.getExpData().contains("BNPL:T"));
		org.junit.jupiter.api.Assertions.assertTrue(baseSearchRequest.getExpData().contains("MRS:T"));
		org.junit.jupiter.api.Assertions.assertTrue(baseSearchRequest.getExpData().contains("HIS:DEFAULT"));
	}

	@Test
	public void validateWishListedSearch_WhenWishListedSearchIsTrueAndExpDataMapHasPDO_ShouldPreserveExistingPDO() {
		// Arrange
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put("PDO", "TP"); // Different value than the default "PN"
		baseSearchRequest.setExpDataMap(expDataMap);

		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setWishListedSearch(true);

		// Act
		ReflectionTestUtils.invokeMethod(commonHelper, "validateAndUpdateTheWishListedSearch", searchCriteria, baseSearchRequest);

		// Assert
		org.junit.jupiter.api.Assertions.assertNotNull(baseSearchRequest.getExpDataMap());
		org.junit.jupiter.api.Assertions.assertTrue(baseSearchRequest.getExpDataMap().containsKey("PDO"));
		org.junit.jupiter.api.Assertions.assertEquals("TP", baseSearchRequest.getExpDataMap().get("PDO"));
		org.junit.jupiter.api.Assertions.assertSame(expDataMap, baseSearchRequest.getExpDataMap());
	}
	@Test
	public void testUpdateTrafficSource_WhenDesktopDevice_GoogleFinderNew() {
		// Arrange
		RequestDetails requestDetails = new RequestDetails();
		TrafficSource trafficSource = new TrafficSource();
		trafficSource.setSource(TrafficSourceConstants.CMP_GOOGLE_FINDER_NEW);
		requestDetails.setTrafficSource(trafficSource);

		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);

		when(utility.buildSourceTraffic(TrafficSourceConstants.CMP_GOOGLE_FINDER_NEW, deviceDetails)).thenReturn("googlehoteldfinder_new");

		// Act
		commonHelper.updateTrafficSource(requestDetails, deviceDetails);

		// Assert
		verify(utility).buildSourceTraffic(TrafficSourceConstants.CMP_GOOGLE_FINDER_NEW, deviceDetails);
		assertEquals("googlehoteldfinder_new", requestDetails.getTrafficSource().getSource());
	}

	@Test
	public void testUpdateTrafficSource_WhenDesktopDevice_GoogleFinderUS() {
		// Arrange
		RequestDetails requestDetails = new RequestDetails();
		TrafficSource trafficSource = new TrafficSource();
		trafficSource.setSource(TrafficSourceConstants.CMP_GOOGLE_FINDER_US);
		requestDetails.setTrafficSource(trafficSource);

		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);

		when(utility.buildSourceTraffic(TrafficSourceConstants.CMP_GOOGLE_FINDER_US, deviceDetails)).thenReturn("googlehoteldfinder_dh_us");

		// Act
		commonHelper.updateTrafficSource(requestDetails, deviceDetails);

		// Assert
		verify(utility).buildSourceTraffic(TrafficSourceConstants.CMP_GOOGLE_FINDER_US, deviceDetails);
		assertEquals("googlehoteldfinder_dh_us", requestDetails.getTrafficSource().getSource());
	}

	@Test
	public void testUpdateTrafficSource_WhenDesktopDevice_GoogleFinderAE() {
		// Arrange
		RequestDetails requestDetails = new RequestDetails();
		TrafficSource trafficSource = new TrafficSource();
		trafficSource.setSource(TrafficSourceConstants.CMP_GOOGLE_FINDER_AE);
		requestDetails.setTrafficSource(trafficSource);

		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);

		when(utility.buildSourceTraffic(TrafficSourceConstants.CMP_GOOGLE_FINDER_AE, deviceDetails)).thenReturn("googlehoteldfinder_dh_ae");

		// Act
		commonHelper.updateTrafficSource(requestDetails, deviceDetails);

		// Assert
		verify(utility).buildSourceTraffic(TrafficSourceConstants.CMP_GOOGLE_FINDER_AE, deviceDetails);
		assertEquals("googlehoteldfinder_dh_ae", requestDetails.getTrafficSource().getSource());
	}

	@Test
	public void testUpdateTrafficSource_WhenDesktopDevice_GoogleFinder() {
		// Arrange
		RequestDetails requestDetails = new RequestDetails();
		TrafficSource trafficSource = new TrafficSource();
		trafficSource.setSource(TrafficSourceConstants.CMP_GOOGLE_FINDER);
		requestDetails.setTrafficSource(trafficSource);

		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);

		when(utility.buildSourceTraffic(TrafficSourceConstants.CMP_GOOGLE_FINDER, deviceDetails)).thenReturn("googlehoteldfinder");

		// Act
		commonHelper.updateTrafficSource(requestDetails, deviceDetails);

		// Assert
		verify(utility).buildSourceTraffic(TrafficSourceConstants.CMP_GOOGLE_FINDER, deviceDetails);
		assertEquals("googlehoteldfinder", requestDetails.getTrafficSource().getSource());
	}

	@Test
	public void testUpdateTrafficSource_WhenDesktopDevice_Trivago() {
		// Arrange
		RequestDetails requestDetails = new RequestDetails();
		TrafficSource trafficSource = new TrafficSource();
		trafficSource.setSource(TrafficSourceConstants.CMP_TRIVAGO);
		requestDetails.setTrafficSource(trafficSource);

		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);

		when(utility.buildSourceTraffic(TrafficSourceConstants.CMP_TRIVAGO, deviceDetails)).thenReturn("trivago");

		// Act
		commonHelper.updateTrafficSource(requestDetails, deviceDetails);

		// Assert
		verify(utility).buildSourceTraffic(TrafficSourceConstants.CMP_TRIVAGO, deviceDetails);
		assertEquals("trivago", requestDetails.getTrafficSource().getSource());
	}

	@Test
	public void testUpdateTrafficSource_WhenDesktopDevice_NullSource() {
		// Arrange
		RequestDetails requestDetails = new RequestDetails();
		TrafficSource trafficSource = new TrafficSource();
		trafficSource.setSource(null);
		requestDetails.setTrafficSource(trafficSource);

		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);

		when(utility.buildSourceTraffic(null, deviceDetails)).thenReturn(null);

		// Act
		commonHelper.updateTrafficSource(requestDetails, deviceDetails);

		// Assert
		verify(utility).buildSourceTraffic(null, deviceDetails);
		assertNull(requestDetails.getTrafficSource().getSource());
	}

	@Test
	public void testUpdateTrafficSource_WhenDesktopDevice_UnknownSource() {
		// Arrange
		RequestDetails requestDetails = new RequestDetails();
		TrafficSource trafficSource = new TrafficSource();
		trafficSource.setSource("unknown");
		requestDetails.setTrafficSource(trafficSource);

		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);

		when(utility.buildSourceTraffic("unknown", deviceDetails)).thenReturn(null);

		// Act
		commonHelper.updateTrafficSource(requestDetails, deviceDetails);

		// Assert
		verify(utility).buildSourceTraffic("unknown", deviceDetails);
		assertNull(requestDetails.getTrafficSource().getSource());
	}

	@Test
	public void testUpdateTrafficSource_WhenDesktopDevice_Tafi() {
		// Arrange
		RequestDetails requestDetails = new RequestDetails();
		TrafficSource trafficSource = new TrafficSource();
		trafficSource.setSource(TrafficSourceConstants.CMP_TAFI);
		requestDetails.setTrafficSource(trafficSource);

		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);

		when(utility.buildSourceTraffic(TrafficSourceConstants.CMP_TAFI, deviceDetails)).thenReturn("Tafi");

		// Act
		commonHelper.updateTrafficSource(requestDetails, deviceDetails);

		// Assert
		verify(utility).buildSourceTraffic(TrafficSourceConstants.CMP_TAFI, deviceDetails);
		assertEquals("Tafi", requestDetails.getTrafficSource().getSource());
	}

	@Test
	public void testUpdateTrafficSource_WhenDesktopDevice_SEO() {
		// Arrange
		RequestDetails requestDetails = new RequestDetails();
		TrafficSource trafficSource = new TrafficSource();
		trafficSource.setSource(TrafficSourceConstants.SEO);
		requestDetails.setTrafficSource(trafficSource);

		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);

		when(utility.buildSourceTraffic(TrafficSourceConstants.SEO, deviceDetails)).thenReturn("seo");

		// Act
		commonHelper.updateTrafficSource(requestDetails, deviceDetails);

		// Assert
		verify(utility).buildSourceTraffic(TrafficSourceConstants.SEO, deviceDetails);
		assertEquals("seo", requestDetails.getTrafficSource().getSource());
	}

	@Test
	public void testUpdateTrafficSource_WhenDesktopDevice_SEM() {
		// Arrange
		RequestDetails requestDetails = new RequestDetails();
		TrafficSource trafficSource = new TrafficSource();
		trafficSource.setSource(TrafficSourceConstants.SEM);
		requestDetails.setTrafficSource(trafficSource);

		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice(Constants.CLIENT_DESKTOP);

		when(utility.buildSourceTraffic(TrafficSourceConstants.SEM, deviceDetails)).thenReturn("sem");

		// Act
		commonHelper.updateTrafficSource(requestDetails, deviceDetails);

		// Assert
		verify(utility).buildSourceTraffic(TrafficSourceConstants.SEM, deviceDetails);
		assertEquals("sem", requestDetails.getTrafficSource().getSource());
	}

	@Test
	public void testUpdateTrafficSource_WhenPWADevice() {
		// Arrange
		RequestDetails requestDetails = new RequestDetails();
		TrafficSource trafficSource = new TrafficSource();
		trafficSource.setSource(TrafficSourceConstants.CMP_GOOGLE_FINDER);
		requestDetails.setTrafficSource(trafficSource);

		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice(Constants.BKG_DEVICE_PWA);

		when(utility.buildSourceTraffic(TrafficSourceConstants.CMP_GOOGLE_FINDER, deviceDetails)).thenReturn("google_finder");

		// Act
		commonHelper.updateTrafficSource(requestDetails, deviceDetails);

		// Assert
		verify(utility).buildSourceTraffic(TrafficSourceConstants.CMP_GOOGLE_FINDER, deviceDetails);
		assertEquals("google_finder", requestDetails.getTrafficSource().getSource());
	}

	@Test
	public void testUpdateTrafficSource_WhenMobileDevice() {
		// Arrange
		RequestDetails requestDetails = new RequestDetails();
		TrafficSource trafficSource = new TrafficSource();
		trafficSource.setSource(TrafficSourceConstants.CMP_GOOGLE_FINDER);
		requestDetails.setTrafficSource(trafficSource);

		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice(Constants.ANDROID);

		when(utility.buildSourceTraffic(TrafficSourceConstants.CMP_GOOGLE_FINDER, deviceDetails)).thenReturn(null);

		// Act
		commonHelper.updateTrafficSource(requestDetails, deviceDetails);

		// Assert
		verify(utility).buildSourceTraffic(TrafficSourceConstants.CMP_GOOGLE_FINDER, deviceDetails);
		assertNull(requestDetails.getTrafficSource().getSource());
	}

	@Test
	public void testUpdateTrafficSource_WhenRequestDetailsIsNull() {
		// Arrange
		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice("DT");

		// Act
		commonHelper.updateTrafficSource(null, deviceDetails);

		// Assert
		verify(utility, never()).buildSourceTraffic(anyString(), any(DeviceDetails.class));
	}

	@Test
	public void testUpdateTrafficSource_WhenTrafficSourceIsNull() {
		// Arrange
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setTrafficSource(null);

		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice("DT");

		// Act
		commonHelper.updateTrafficSource(requestDetails, deviceDetails);

		// Assert
		verify(utility, never()).buildSourceTraffic(anyString(), any(DeviceDetails.class));
	}

	@Test
	public void testUpdateTrafficSource_WhenDeviceDetailsIsNull() {
		// Arrange
		RequestDetails requestDetails = new RequestDetails();
		TrafficSource trafficSource = new TrafficSource();
		trafficSource.setSource("GOOGLEHOTELDFINDER");
		requestDetails.setTrafficSource(trafficSource);

		// Act
		commonHelper.updateTrafficSource(requestDetails, null);

		// Assert
		verify(utility, never()).buildSourceTraffic(anyString(), any(DeviceDetails.class));
		assertEquals("GOOGLEHOTELDFINDER", requestDetails.getTrafficSource().getSource());
	}

	@Test
	public void getSourceCityTest() {
		Map<String, String> httpHeaderMap = new HashMap<>();
		httpHeaderMap.put("user-currency", "INR");
		httpHeaderMap.put(HEADER_AKAMAI, "city=NEWDELHI");
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		String userCity = ReflectionTestUtils.invokeMethod(commonHelper, "getSourceCity", httpHeaderMap, baseSearchRequest);
		assertEquals("NEWDELHI", userCity);
		UserLocation userLocation = new UserLocation();
		userLocation.setCity("CTDEL");
		baseSearchRequest.setUserLocation(userLocation);
		userCity = ReflectionTestUtils.invokeMethod(commonHelper, "getSourceCity", httpHeaderMap, baseSearchRequest);
		assertEquals("CTDEL", userCity);
	}

	// HTL-63666 Alternate Booking Test Cases
	@Test
	public void should_ProcessRequest_When_AlternateBookingInfoIsNull() throws ClientGatewayException {
		// Arrange
		BaseSearchRequest baseSearchRequest = createBaseSearchRequestForAlternateBooking();
		baseSearchRequest.setAlternateBookingInfo(null); // No alternate booking info
		SearchCriteria searchCriteria = createSearchCriteria();
		Map<String, String> headers = createHeaders();
		
		setupCommonMocks();
		
		// Act
		CommonModifierResponse response = commonHelper.processRequest(searchCriteria, baseSearchRequest, headers);
		
		// Assert
		Assert.assertNotNull("Response should not be null", response);
		// Should process normally without any authentication exceptions
	}

	@Test
	public void should_ProcessRequest_When_AlternateBookingIsFalse() throws ClientGatewayException {
		// Arrange
		BaseSearchRequest baseSearchRequest = createBaseSearchRequestForAlternateBooking();
		AlternateBookingInfo alternateBookingInfo = new AlternateBookingInfo();
		alternateBookingInfo.setAlternateBooking(false);
		alternateBookingInfo.setOldBookingId("HTL123456");
		baseSearchRequest.setAlternateBookingInfo(alternateBookingInfo);
		SearchCriteria searchCriteria = createSearchCriteria();
		Map<String, String> headers = createHeaders();
		
		setupCommonMocks();
		
		// Act
		CommonModifierResponse response = commonHelper.processRequest(searchCriteria, baseSearchRequest, headers);
		
		// Assert
		Assert.assertNotNull("Response should not be null", response);
		// Should process normally without any authentication exceptions
	}

	@Test
	public void should_ProcessRequest_When_AlternateBookingIsTrueAndUserUuidPresent() throws ClientGatewayException {
		// Arrange
		BaseSearchRequest baseSearchRequest = createBaseSearchRequestForAlternateBooking();
		AlternateBookingInfo alternateBookingInfo = new AlternateBookingInfo();
		alternateBookingInfo.setAlternateBooking(true);
		alternateBookingInfo.setOldBookingId("HTL123456");
		alternateBookingInfo.setKey("test-key");
		alternateBookingInfo.setAgentId("agent123");
		baseSearchRequest.setAlternateBookingInfo(alternateBookingInfo);
		SearchCriteria searchCriteria = createSearchCriteria();
		Map<String, String> headers = createHeaders();
		
		setupCommonMocksWithValidUser();
		
		// Act
		CommonModifierResponse response = commonHelper.processRequest(searchCriteria, baseSearchRequest, headers);
		
		// Assert
		Assert.assertNotNull("Response should not be null", response);
		Assert.assertNotNull("Extended user should be present", response.getExtendedUser());
		Assert.assertNotNull("UUID should be present", response.getExtendedUser().getUuid());
		// Should process normally since UUID is present
	}

	@Test(expected = AuthenticationException.class)
	public void should_ThrowAuthenticationException_When_AlternateBookingIsTrueAndUserUuidIsNull() throws ClientGatewayException {
		// Arrange
		BaseSearchRequest baseSearchRequest = createBaseSearchRequestForAlternateBooking();
		AlternateBookingInfo alternateBookingInfo = new AlternateBookingInfo();
		alternateBookingInfo.setAlternateBooking(true);
		alternateBookingInfo.setOldBookingId("HTL123456");
		alternateBookingInfo.setKey("test-key");
		baseSearchRequest.setAlternateBookingInfo(alternateBookingInfo);
		SearchCriteria searchCriteria = createSearchCriteria();
		Map<String, String> headers = createHeaders();
		
		setupCommonMocksWithNullUser();
		
		// Act
		commonHelper.processRequest(searchCriteria, baseSearchRequest, headers);
		
		// Should throw AuthenticationException with UUID_NOT_FOUND error
	}

	@Test(expected = AuthenticationException.class)
	public void should_ThrowAuthenticationException_When_AlternateBookingIsTrueAndUserUuidIsEmpty() throws ClientGatewayException {
		// Arrange
		BaseSearchRequest baseSearchRequest = createBaseSearchRequestForAlternateBooking();
		AlternateBookingInfo alternateBookingInfo = new AlternateBookingInfo();
		alternateBookingInfo.setAlternateBooking(true);
		alternateBookingInfo.setOldBookingId("HTL123456");
		baseSearchRequest.setAlternateBookingInfo(alternateBookingInfo);
		SearchCriteria searchCriteria = createSearchCriteria();
		Map<String, String> headers = createHeaders();
		
		setupCommonMocksWithEmptyUuid();
		
		// Act
		commonHelper.processRequest(searchCriteria, baseSearchRequest, headers);
		
		// Should throw AuthenticationException with UUID_NOT_FOUND error
	}

	@Test(expected = AuthenticationException.class)
	public void should_ThrowAuthenticationException_When_AlternateBookingIsTrueAndExtendedUserIsNull() throws ClientGatewayException {
		// Arrange
		BaseSearchRequest baseSearchRequest = createBaseSearchRequestForAlternateBooking();
		AlternateBookingInfo alternateBookingInfo = new AlternateBookingInfo();
		alternateBookingInfo.setAlternateBooking(true);
		alternateBookingInfo.setOldBookingId("HTL123456");
		baseSearchRequest.setAlternateBookingInfo(alternateBookingInfo);
		SearchCriteria searchCriteria = createSearchCriteria();
		Map<String, String> headers = createHeaders();
		
		setupCommonMocksWithNullExtendedUser();
		
		// Act
		try {
			commonHelper.processRequest(searchCriteria, baseSearchRequest, headers);
		} catch (AuthenticationException e) {
			// Assert exception details
			Assert.assertEquals("Error code should match", AuthenticationErrors.UUID_NOT_FOUND.getErrorCode(), e.getCode());
			Assert.assertEquals("Error message should match", AuthenticationErrors.UUID_NOT_FOUND.getErrorMsg(), e.getMessage());
			Assert.assertEquals("Dependency layer should match", DependencyLayer.USERSERVICE, e.getDependencyLayer());
			Assert.assertEquals("Error type should match", ErrorType.AUTHENTICATION, e.getErrorType());
			throw e;
		}
	}

	@Test
	public void should_ProcessRequest_When_AlternateBookingIsTrueWithValidUserAndAllFields() throws ClientGatewayException {
		// Arrange
		BaseSearchRequest baseSearchRequest = createBaseSearchRequestForAlternateBooking();
		AlternateBookingInfo alternateBookingInfo = new AlternateBookingInfo();
		alternateBookingInfo.setAlternateBooking(true);
		alternateBookingInfo.setOldBookingId("HTL123456789");
		alternateBookingInfo.setKey("alternate-booking-key-123");
		alternateBookingInfo.setAgentId("AGENT001");
		baseSearchRequest.setAlternateBookingInfo(alternateBookingInfo);
		SearchCriteria searchCriteria = createSearchCriteria();
		Map<String, String> headers = createHeaders();
		
		setupCommonMocksWithValidUser();
		
		// Act
		CommonModifierResponse response = commonHelper.processRequest(searchCriteria, baseSearchRequest, headers);
		
		// Assert
		Assert.assertNotNull("Response should not be null", response);
		Assert.assertNotNull("Extended user should be present", response.getExtendedUser());
		Assert.assertEquals("UUID should match", "test-uuid-123", response.getExtendedUser().getUuid());
		// Verify the alternate booking info is preserved
		Assert.assertTrue("Alternate booking flag should be true", baseSearchRequest.getAlternateBookingInfo().isAlternateBooking());
		Assert.assertEquals("Old booking ID should match", "HTL123456789", baseSearchRequest.getAlternateBookingInfo().getOldBookingId());
		Assert.assertEquals("Key should match", "alternate-booking-key-123", baseSearchRequest.getAlternateBookingInfo().getKey());
		Assert.assertEquals("Agent ID should match", "AGENT001", baseSearchRequest.getAlternateBookingInfo().getAgentId());
	}

	// Helper methods for alternate booking tests
	private BaseSearchRequest createBaseSearchRequestForAlternateBooking() {
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		baseSearchRequest.setCorrelationKey("test-correlation-key");
		
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setFunnelSource("HOTELS");
		requestDetails.setPageContext("LISTING");
		requestDetails.setSiteDomain("IN");
		requestDetails.setIdContext("B2C");
		requestDetails.setVisitorId("test-visitor-id");
		requestDetails.setFirstTimeUserState(1);
		baseSearchRequest.setRequestDetails(requestDetails);
		
		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setDeviceId("test-device-id");
		deviceDetails.setBookingDevice("DESKTOP");
		deviceDetails.setDeviceType("DESKTOP");
		baseSearchRequest.setDeviceDetails(deviceDetails);
		
		baseSearchRequest.setExpDataMap(new HashMap<>());
		baseSearchRequest.setCohertVar(new CohertVar());
		
		FeatureFlags featureFlags = new FeatureFlags();
		featureFlags.setNoOfCoupons(1);
		baseSearchRequest.setFeatureFlags(featureFlags);
		
		return baseSearchRequest;
	}

	private SearchCriteria createSearchCriteria() {
		SearchCriteria searchCriteria = new SearchCriteria();
		searchCriteria.setCurrency("INR");
		searchCriteria.setCountryCode("IN");
		searchCriteria.setLocationId("CTDEL");
		searchCriteria.setCheckIn("2024-02-21");
		searchCriteria.setCheckOut("2024-02-22");
		
		List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
		RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
		roomStayCandidate.setAdultCount(2);
		roomStayCandidate.setChildAges(new ArrayList<>());
		roomStayCandidates.add(roomStayCandidate);
		searchCriteria.setRoomStayCandidates(roomStayCandidates);
		
		return searchCriteria;
	}

	private Map<String, String> createHeaders() {
		Map<String, String> headers = new HashMap<>();
		headers.put("mmt-auth", "test-auth-token");
		headers.put("usr-mcid", "test-mcid");
		headers.put("X-Akamai-Edgescape", "georegion=104,country_code=IN,region_code=DL,city=NEWDELHI,lat=28.60,long=77.20,timezone=GMT*****,continent=AS,throughput=vhigh,bw=5000,asnum=24560,location_id=0");
		return headers;
	}

	private void setupCommonMocks() throws ClientGatewayException {
		Mockito.when(env.getProperty(Mockito.anyString())).thenReturn("1");
		ReflectionTestUtils.setField(commonHelper, "userServiceThreadPool", userServiceThreadPool);
		ReflectionTestUtils.setField(commonHelper, "hydraServiceThreadPool", hydraServiceThreadPool);
		ReflectionTestUtils.setField(commonHelper, "cohortValid", true);
		ReflectionTestUtils.setField(commonHelper, "mobConfigHelper", mobConfigHelper);
		
		PokusExperimentResponse pokusExperimentResponse = new PokusExperimentResponse();
		pokusExperimentResponse.setPerLobMap(new HashMap<>());
		PokusAssignVariantResponse pokusAssignVariantResponse = new PokusAssignVariantResponse();
		pokusAssignVariantResponse.setMetadataValues(new HashMap<>());
		pokusExperimentResponse.getPerLobMap().put("HOTEL", pokusAssignVariantResponse);
		
		// Mock common responses for basic functionality
		UserServiceResponse userServiceResponse = new UserServiceResponse();
		userServiceResponse.setResult(new UserServiceResult());
		userServiceResponse.getResult().setExtendedUser(new ExtendedUser());
		userServiceResponse.getResult().getExtendedUser().setUuid("test-uuid");
		userServiceResponse.getResult().getExtendedUser().setProfileType("PERSONAL");
		
		Mockito.when(userServiceExecutor.getUserServiceResponse(Mockito.any(), Mockito.any(), Mockito.any(), 
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(userServiceResponse);
		
		// Mock MobConfigHelper
		Map<String, SourceRegionSpecificDataConfig> sourceRegionMapping = new HashMap<>();
		// Add a default entry to prevent NullPointerException
		SourceRegionSpecificDataConfig defaultConfig = new SourceRegionSpecificDataConfig();
		sourceRegionMapping.put(Constants.DEFAULT_DOMAIN, defaultConfig);
		Mockito.when(mobConfigHelper.getSourceRegionSpecificDataMapping()).thenReturn(sourceRegionMapping);
	}

	private void setupCommonMocksWithValidUser() throws ClientGatewayException {
		setupCommonMocks();
		
		UserServiceResponse userServiceResponse = new UserServiceResponse();
		userServiceResponse.setResult(new UserServiceResult());
		userServiceResponse.getResult().setExtendedUser(new ExtendedUser());
		userServiceResponse.getResult().getExtendedUser().setUuid("test-uuid-123");
		userServiceResponse.getResult().getExtendedUser().setProfileType("PERSONAL");
		userServiceResponse.getResult().getExtendedUser().setAccountId("test-account-id");
		userServiceResponse.getResult().getExtendedUser().setProfileId("test-profile-id");
		userServiceResponse.getResult().getExtendedUser().setLoginInfoList(new ArrayList<>());
		
		Mockito.when(userServiceExecutor.getUserServiceResponse(Mockito.any(), Mockito.any(), Mockito.any(), 
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(userServiceResponse);
	}

	private void setupCommonMocksWithNullUser() throws ClientGatewayException {
		setupCommonMocks();
		
		UserServiceResponse userServiceResponse = new UserServiceResponse();
		userServiceResponse.setResult(new UserServiceResult());
		userServiceResponse.getResult().setExtendedUser(null);
		
		Mockito.when(userServiceExecutor.getUserServiceResponse(Mockito.any(), Mockito.any(), Mockito.any(), 
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(userServiceResponse);
	}

	private void setupCommonMocksWithEmptyUuid() throws ClientGatewayException {
		setupCommonMocks();
		
		UserServiceResponse userServiceResponse = new UserServiceResponse();
		userServiceResponse.setResult(new UserServiceResult());
		userServiceResponse.getResult().setExtendedUser(new ExtendedUser());
		userServiceResponse.getResult().getExtendedUser().setUuid(""); // Empty UUID
		
		Mockito.when(userServiceExecutor.getUserServiceResponse(Mockito.any(), Mockito.any(), Mockito.any(), 
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(userServiceResponse);
	}

	private void setupCommonMocksWithNullExtendedUser() throws ClientGatewayException {
		setupCommonMocks();
		
		UserServiceResponse userServiceResponse = new UserServiceResponse();
		userServiceResponse.setResult(new UserServiceResult());
		userServiceResponse.getResult().setExtendedUser(null);
		
		Mockito.when(userServiceExecutor.getUserServiceResponse(Mockito.any(), Mockito.any(), Mockito.any(), 
				Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(userServiceResponse);
	}

}
