package com.mmt.hotels.clientgateway.helpers;


import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.model.response.corporate.GetApprovalsResponse;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import org.json.simple.JSONObject;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.FORWARD_BOOKING_CTA;
import static com.mmt.hotels.clientgateway.constants.Constants.FORWARD_BOOKING_DEEPLINK;
import static com.mmt.hotels.clientgateway.constants.Constants.ICON_URL_KEY;

@RunWith(MockitoJUnitRunner.class)
public class ForwardBookingFlowHelperTest {
	@InjectMocks
	private ForwardBookingFlowHelper forwardBookingFlowHelper;

	@Test
	public void setForwardBookingFlowDataToMetaDataTest_null() {
		JSONObject jsonObject = new JSONObject();
		forwardBookingFlowHelper.setForwardBookingFlowDataToMetaData(null, jsonObject);
		Assert.assertNull(jsonObject.get(FORWARD_BOOKING_DEEPLINK));
	}

	@Test
	public void setForwardBookingFlowDataToMetaDataTest_non_null() {
		JSONObject jsonObject = new JSONObject();
		com.mmt.hotels.model.response.corporate.GetApprovalsResponse approvalsResponse = new GetApprovalsResponse();
		approvalsResponse.setDeepLink("deeplink");
		forwardBookingFlowHelper.setForwardBookingFlowDataToMetaData(approvalsResponse, jsonObject);
		Assert.assertNotNull(jsonObject.get(FORWARD_BOOKING_DEEPLINK));
	}

	@Test
	public void setForwardBookingFlowDataToErrorForApprovalPage_Test() {
		JSONObject metaData = new JSONObject();
		metaData.put(FORWARD_BOOKING_DEEPLINK, "dddeeplibnk");
		metaData.put(FORWARD_BOOKING_CTA, "Text");
		metaData.put(ICON_URL_KEY, "abc.png");

		Error error = forwardBookingFlowHelper.setForwardBookingFlowDataToErrorForApprovalPage(new Error(), metaData);
		Assert.assertNotNull(error.getAdditionalInfo());
	}

	@Test
	public void setForwardBookingFlowDataToErrorForPaymentPage_Test(){
		List<com.mmt.hotels.model.response.errors.Error> errorList = new ArrayList<>();
		com.mmt.hotels.model.response.errors.Error error = new com.mmt.hotels.model.response.errors.Error();
		Map<String, String> map = new HashMap<>();
		map.put(FORWARD_BOOKING_DEEPLINK, "de");
		error.setErrorAdditionalInfo(map);
		errorList.add(error);
		ResponseErrors responseErrors = new ResponseErrors.Builder().buildErrorList(errorList).build();
		PaymentCheckoutResponse checkoutResponse = new PaymentCheckoutResponse();
		checkoutResponse.setResponseErrors(responseErrors);
		MDC.put(MDCHelper.MDCKeys.IDCONTEXT.getStringValue(), "CORP");
		Error error1 = forwardBookingFlowHelper.setForwardBookingFlowDataToErrorForPaymentPage(new Error(), checkoutResponse);
		Assert.assertNotNull(error1.getAdditionalInfo());
	}
}
