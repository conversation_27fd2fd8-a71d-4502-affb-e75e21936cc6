package com.mmt.hotels.clientgateway.helpers;

import java.time.LocalDate;
import java.util.*;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicy;
import com.mmt.hotels.clientgateway.response.listing.UpsellRateplanResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.FetchCollection;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.factory.SearchRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.response.SearchRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.pwa.SearchRoomsResponseTransformerPWA;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.DPTExperimentDetails;
import com.mmt.hotels.model.request.FilterSearchMetaDataResponse;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.model.request.matchmaker.Tags;
import com.mmt.hotels.model.response.dpt.ContextualFilterResponse;
import com.mmt.hotels.model.response.dpt.ExperimentDetail;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.searchwrapper.PersonalizedResponse;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.core.env.Environment;

import com.mmt.hotels.model.response.searchwrapper.ListingPagePersonalizationResponsBO;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperResponseBO;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class ListingHelperTest {
	
	@InjectMocks
	ListingHelper listingHelper;

	@Mock
	private CommonConfigHelper commonConfigHelper;
	
	@Mock
	Environment env;

	@Mock
	PolyglotService polyglotService;

	@Mock
	DateUtil dateUtil;

	@Mock
	private Utility utility;

	@Mock
	SearchRoomsFactory searchRoomsFactory;

	private static final Gson gson = new Gson();

	@Before
	public void setUp() {
		when(dateUtil.getDaysDiff(any(LocalDate.class), any(LocalDate.class))).thenReturn(1);
		Map<String, String> addOnInfoMostPopularTag = gson.fromJson("{\"title\":\"MOST POPULAR\",\"color\":\"#007E7D\",\"persuasionBgUrl\":\"https://promos.makemytrip.com/GCC/next_best/most_popular.png\",\"horizontal\":\"8\",\"vertical\":\"0\",\"bgUrl\":\"https://promos.makemytrip.com/GCC/next_best/bg_new.png\"}",new TypeToken<Map<String, String>>() {
		}.getType());
		ReflectionTestUtils.setField(listingHelper, "addOnInfoMostPopularTag", addOnInfoMostPopularTag);
	}

	@Test
	public void testConvertSearchHotelsToPersonalizedHotels() {

		/* Response is null case */
		SearchWrapperResponseBO webApiResponse = null;
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		searchHotelsRequest.getRequestDetails().setFunnelSource("abc");
		ListingPagePersonalizationResponsBO listingBO = listingHelper.convertSearchHotelsToPersonalizedHotels(webApiResponse,searchHotelsRequest);
		Assert.assertNull(listingBO);

		/* START - test buildPersonalizedResponse */
		webApiResponse = new SearchWrapperResponseBO.Builder().build();
		listingBO = listingHelper.convertSearchHotelsToPersonalizedHotels(webApiResponse,searchHotelsRequest);
		Assert.assertNotNull(listingBO);
		Assert.assertNull(listingBO.getPersonalizedResponse());

		List<SearchWrapperHotelEntity> hotelList = new ArrayList<>();
		hotelList.add(new SearchWrapperHotelEntity());
		hotelList.add(new SearchWrapperHotelEntity());

		webApiResponse.setHotelList(hotelList);
		listingBO = listingHelper.convertSearchHotelsToPersonalizedHotels(webApiResponse,searchHotelsRequest);
		Assert.assertNotNull(listingBO);
		Assert.assertNotNull(listingBO.getPersonalizedResponse());
		Optional<PersonalizedResponse<SearchWrapperHotelEntity>> section = listingBO.getPersonalizedResponse().stream().filter(per -> Constants.RECOMMENDED_HOTELS_SECTION.equalsIgnoreCase(per.getSection())).findAny();
		Assert.assertTrue(section.isPresent());
		Assert.assertEquals(section.get().getCount(),webApiResponse.getHotelList().size());

		/* webApiResponse.setNonAltAccoHotelList(hotelList);
		webApiResponse.setNonAltAccoHeading("abd");
		listingBO = listingHelper.convertSearchHotelsToPersonalizedHotels(webApiResponse,searchHotelsRequest);
		Assert.assertNotNull(listingBO);
		Assert.assertNotNull(listingBO.getPersonalizedResponse());
		Assert.assertTrue(listingBO.getPersonalizedResponse().stream().anyMatch(per -> (Constants.RECOMMENDED_HOTELS_SECTION.equalsIgnoreCase(per.getSection()))));
		section = listingBO.getPersonalizedResponse().stream().filter(per -> Constants.NON_ALTACCO_HOTELS_SECTION.equalsIgnoreCase(per.getSection())).findAny();
		Assert.assertTrue(section.isPresent());
		Assert.assertTrue(section.get().getHeading().equalsIgnoreCase(webApiResponse.getNonAltAccoHeading()));
		Assert.assertEquals(section.get().getCount(),webApiResponse.getNonAltAccoHotelList().size());

		webApiResponse.setOtherAltAccoHotelList(hotelList);
		webApiResponse.setOtherAltAccoHeading("def");
		listingBO = listingHelper.convertSearchHotelsToPersonalizedHotels(webApiResponse,searchHotelsRequest);
		Assert.assertNotNull(listingBO);
		Assert.assertNotNull(listingBO.getPersonalizedResponse());
		Assert.assertTrue(listingBO.getPersonalizedResponse().stream().anyMatch(per -> (Constants.RECOMMENDED_HOTELS_SECTION.equalsIgnoreCase(per.getSection()))));
		Assert.assertTrue(listingBO.getPersonalizedResponse().stream().anyMatch(per -> (Constants.NON_ALTACCO_HOTELS_SECTION.equalsIgnoreCase(per.getSection()))));
		section = listingBO.getPersonalizedResponse().stream().filter(per -> Constants.OTHER_ALTACCO_HOTELS_SECTION.equalsIgnoreCase(per.getSection())).findAny();
		Assert.assertTrue(section.isPresent());
		Assert.assertTrue(section.get().getHeading().equalsIgnoreCase(webApiResponse.getOtherAltAccoHeading()));
		Assert.assertEquals(section.get().getCount(),webApiResponse.getOtherAltAccoHotelList().size());*/

		webApiResponse.setNearbyHotelList(hotelList);
		webApiResponse.setNearbyHeading("ghi");
		webApiResponse.setNearbySubHeading("jkl");
		listingBO = listingHelper.convertSearchHotelsToPersonalizedHotels(webApiResponse,searchHotelsRequest);
		Assert.assertNotNull(listingBO);
		Assert.assertNotNull(listingBO.getPersonalizedResponse());
		Assert.assertTrue(listingBO.getPersonalizedResponse().stream().anyMatch(per -> (Constants.RECOMMENDED_HOTELS_SECTION.equalsIgnoreCase(per.getSection()))));
		//Assert.assertTrue(listingBO.getPersonalizedResponse().stream().anyMatch(per -> (Constants.NON_ALTACCO_HOTELS_SECTION.equalsIgnoreCase(per.getSection()))));
		//Assert.assertTrue(listingBO.getPersonalizedResponse().stream().anyMatch(per -> (Constants.OTHER_ALTACCO_HOTELS_SECTION.equalsIgnoreCase(per.getSection()))));
		section = listingBO.getPersonalizedResponse().stream().filter(per -> Constants.NEARBY_HOTELS_SECTION.equalsIgnoreCase(per.getSection())).findAny();
		Assert.assertTrue(section.isPresent());
		Assert.assertTrue(section.get().getHeading().equalsIgnoreCase(webApiResponse.getNearbyHeading()));
		Assert.assertTrue(section.get().getSubHeading().equalsIgnoreCase(webApiResponse.getNearbySubHeading()));
		Assert.assertEquals(section.get().getCount(),webApiResponse.getNearbyHotelList().size());
		/* END - test buildPersonalizedResponse */

		webApiResponse.setSharingUrl("www.test.com");
		listingBO = listingHelper.convertSearchHotelsToPersonalizedHotels(webApiResponse,searchHotelsRequest);
		Assert.assertEquals(listingBO.getSharingUrl(),"www.test.com");

	}

	@Test
	public void testUpdateHeading(){
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("test");
		Assert.assertNotNull(ReflectionTestUtils.invokeMethod(listingHelper, "updateHeading", "Properties in {AREA}", searchHotelsRequest, null));
		Assert.assertNotNull(ReflectionTestUtils.invokeMethod(listingHelper, "updateHeading", "Properties in {AREA}", searchHotelsRequest, "test"));
		String heading = ReflectionTestUtils.invokeMethod(listingHelper, "updateHeading", "{AREA}" , searchHotelsRequest, "test");
		assertTrue(heading.equalsIgnoreCase("test"));
		searchHotelsRequest.setMatchMakerDetails(new MatchMakerRequest());
		searchHotelsRequest.getMatchMakerDetails().setSelectedTags(new ArrayList<>());
		searchHotelsRequest.getMatchMakerDetails().getSelectedTags().add(new Tags());
		searchHotelsRequest.getMatchMakerDetails().getSelectedTags().get(0).setTagDescription("test1");
		Assert.assertNotNull(ReflectionTestUtils.invokeMethod(listingHelper, "updateHeading","Properties in {AREA}" , searchHotelsRequest, "test"));
		heading = ReflectionTestUtils.invokeMethod(listingHelper, "updateHeading", "{AREA}" , searchHotelsRequest, null);
		assertTrue(heading.equalsIgnoreCase("test1"));
	}

	@Test
	public void testUpdateCollectionCounts(){
		Map<String, Map<String,String>> collCountMap = new HashMap<>();
		Map<String,String> pageContextCountMap = new HashMap<>();
		pageContextCountMap.put("LISTING","3");
		collCountMap.put("MSITE", pageContextCountMap);
		Mockito.when(commonConfigHelper.getCollectionCountMapping()).thenReturn(collCountMap);
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		searchHotelsRequest.getRequestDetails().setPageContext("LISTING");
		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice("msite");
		searchHotelsRequest.setDeviceDetails(deviceDetails);
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		CollectionCriteria collectionCriteria = new CollectionCriteria();
		searchCriteria.setCollectionCriteria(collectionCriteria);
		searchHotelsRequest.setSearchCriteria(searchCriteria);
		listingHelper.updateCollectionCounts(searchHotelsRequest);
		Assert.assertEquals("3", searchHotelsRequest.getSearchCriteria().getCollectionCriteria().getCollectionsCount());
	}

	@Test
	public void testSortBasedOnPriority() {
		List<FetchCollection> fetchCollectionList=new ArrayList<>();
		FetchCollection obj1=new FetchCollection();
		obj1.setPriority("2");
		FetchCollection obj2=new FetchCollection();
		obj2.setPriority("1");
		fetchCollectionList.add(obj1);
		fetchCollectionList.add(obj2);
		listingHelper.sortBasedOnPriority(fetchCollectionList);
		Assert.assertTrue(fetchCollectionList.get(0).getPriority()=="1");
	}

	@Test
	public void populateExperimentDetailListDPTTest() {

		FilterSearchMetaDataResponse filterSearchMetaDataResponse = new FilterSearchMetaDataResponse();
		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		contextualFilterResponse.setExpDetails(new ArrayList<>());
		contextualFilterResponse.getExpDetails().add(new ExperimentDetail());
		filterSearchMetaDataResponse.setDptContextualFilterResponse(contextualFilterResponse);

		List<DPTExperimentDetails> experimentDetailsList = new ArrayList<>();
		listingHelper.populateDPTExperimentDetailsList(experimentDetailsList, filterSearchMetaDataResponse);
		Assert.assertEquals(experimentDetailsList.size(), 1);

		experimentDetailsList = new ArrayList<>();
		contextualFilterResponse.setExpDetails(new ArrayList<>());
		listingHelper.populateDPTExperimentDetailsList(experimentDetailsList, filterSearchMetaDataResponse);
		Assert.assertEquals(experimentDetailsList.size(), 0);

	}

	@Test
	public void shouldReturnUpsellRateplanResponseWhenValidInputIsProvided() {
		ListingPagePersonalizationResponsBO searchWrapperResponseBO = new ListingPagePersonalizationResponsBO();
		SearchRoomsResponseTransformer searchTransformer = Mockito.mock(SearchRoomsResponseTransformerPWA.class);
		Mockito.when(searchRoomsFactory.getResponseService(Mockito.any())).thenReturn(searchTransformer);
		//Mockito.when(searchTransformer.getFilterCodes(Mockito.any(),anyBoolean(),anyInt(),Mockito.any(),anyBoolean(), Mockito.anyString())).thenReturn(new ArrayList<>());
		Mockito.lenient().when(searchTransformer.transformInclusions(Mockito.any(),Mockito.any(),anyInt(),anyBoolean(),anyString(),anyDouble(),anyString(), anyBoolean(), any())).thenReturn(new ArrayList<>());
		Mockito.lenient().when(searchTransformer.getTariffs(Mockito.any(),anyString(),anyString(),anyString(),anyInt(),anyString(),anyBoolean(),
				anyBoolean(),anyBoolean(),any(), anyBoolean(), Mockito.anyBoolean())).thenReturn(new ArrayList<>());
		Mockito.lenient().when(utility.transformCancellationPolicy(Mockito.any(),anyBoolean(),anyBoolean(),Mockito.any(),anyString(),anyString(),anyInt(),Mockito.any(), Mockito.any(), anyBoolean())).thenReturn(new BookedCancellationPolicy());
		Mockito.lenient().when(utility.getRatePlanName(Mockito.any(),Mockito.any(),anyString(),anyString(),anyString())).thenReturn("ratePlan123");
		SearchHotelsRequest fetchUpsellRateplanRequest = new SearchHotelsRequest();
		fetchUpsellRateplanRequest.setSearchCriteria(buildSearchCriteria());
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setTrafficSource(new TrafficSource());
		requestDetails.getTrafficSource().setSource("SEO");
		requestDetails.setMetaInfo(true);
		fetchUpsellRateplanRequest.setDeviceDetails(new DeviceDetails());
		fetchUpsellRateplanRequest.getDeviceDetails().setBookingDevice("DESKTOP");
		fetchUpsellRateplanRequest.setRequestDetails(requestDetails);
		CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
		searchWrapperResponseBO.setPersonalizedResponse(new ArrayList<>());
		searchWrapperResponseBO.setPersonalizedResponse(new ArrayList<>());
		searchWrapperResponseBO.getPersonalizedResponse().add(new PersonalizedResponse<>());
		searchWrapperResponseBO.getPersonalizedResponse().get(0).setHotels(new ArrayList<>());
		searchWrapperResponseBO.getPersonalizedResponse().get(0).getHotels().add(buildSearchWrapperHotelEntity());
		// When
		UpsellRateplanResponse result = listingHelper.convertFetchUpsellRateplanresponse(searchWrapperResponseBO, fetchUpsellRateplanRequest, commonModifierResponse);

	}

	private SearchWrapperHotelEntity buildSearchWrapperHotelEntity(){
		SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
		searchWrapperHotelEntity.setRoomCount(5);
		searchWrapperHotelEntity.setLowestRoomCode("1234");
		searchWrapperHotelEntity.setLowestRoomType("Room");
		Map<String, RatePlan> ratePlanMap = new HashMap<>();
		RatePlan ratePlan = new RatePlan();
		ratePlan.setMostPopularRateplan(true);
		ratePlan.setRatePlanCode("rpc123");
		ratePlan.setMealPlans(new ArrayList<>());
		ratePlan.setInclusions(new ArrayList<>());
		ratePlan.setDisplayFare(new DisplayFare());
		ratePlan.getDisplayFare().setDisplayPriceBreakDown(buildDisplayPriceBreakDown());
		PaymentDetails paymentDetails = new PaymentDetails();
		paymentDetails.setPaymentMode(PaymentMode.PAS);
		ratePlan.setPaymentDetails(paymentDetails);
		ratePlan.setSupplierDetails(new SupplierDetails());
		ratePlan.getSupplierDetails().setCostPrice(19d);
		ratePlanMap.put("rpc123", ratePlan);
		searchWrapperHotelEntity.setUpsellRateplanList(ratePlanMap);
		return searchWrapperHotelEntity;
	}

	private DisplayPriceBreakDown buildDisplayPriceBreakDown(){
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setDisplayPrice(36000.00);
		displayPriceBreakDown.setSavingPerc(80.00);
		return displayPriceBreakDown;
	}

	private SearchHotelsCriteria buildSearchCriteria(){
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setCheckIn("2022-11-10");
		searchCriteria.setCheckOut("2022-11-11");
		return searchCriteria;
	}

}
