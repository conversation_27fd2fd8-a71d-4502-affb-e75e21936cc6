package com.mmt.hotels.clientgateway.context;


import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.interfaces.ListingStrategy;
import com.mmt.hotels.clientgateway.request.ListingSearchRequestV2;
import com.mmt.hotels.clientgateway.response.listing.ListingResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class ListingContextTest {

    @Mock
    private Map<String, ListingStrategy> listingOperationsMap;

    @Mock
    private ListingStrategy listingStrategy;

    @InjectMocks
    ListingContext listingContext;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        listingContext = new ListingContext(listingOperationsMap);
    }

    @Test
    public void testExecuteStrategy_ValidListingType() throws ClientGatewayException {
        String listingType = "validType";
        ListingSearchRequestV2 request = new ListingSearchRequestV2();
        Map<String, String[]> parameterMap = new HashMap<>();
        Map<String, String> httpHeaderMap = new HashMap<>();

        when(listingOperationsMap.get(listingType)).thenReturn(listingStrategy);
        com.mmt.hotels.clientgateway.response.listing.ListingResponse expectedResponse = new com.mmt.hotels.clientgateway.response.listing.ListingResponse();
        when(listingStrategy.doListingOperation(request, parameterMap, httpHeaderMap)).thenReturn(expectedResponse);

        ListingResponse actualResponse = listingContext.executeStrategy(listingType, request, parameterMap, httpHeaderMap);

        assertEquals(expectedResponse, actualResponse);
        verify(listingOperationsMap).get(listingType);
        verify(listingStrategy).doListingOperation(request, parameterMap, httpHeaderMap);
    }

}