package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.gommt.hotels.orchestrator.detail.model.response.content.PlacesResponse;
import com.gommt.hotels.orchestrator.detail.model.response.content.places.Category;
import com.gommt.hotels.orchestrator.detail.model.response.content.places.CategoryDatum;
import com.gommt.hotels.orchestrator.detail.model.response.content.places.DirectionDetails;
import com.gommt.hotels.orchestrator.detail.model.response.content.places.PoiImage;
import com.gommt.hotels.orchestrator.detail.model.response.common.DisplayItem;
import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.HotelPersuasionData;
import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.PersuasionValue;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.RatingData;
import com.mmt.hotels.clientgateway.response.LocationPersuasions;
import com.mmt.hotels.clientgateway.response.PlacesResponseCG;
import com.mmt.hotels.clientgateway.util.ReArchUtility;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrchStaticDetailResponseTransformerTest {

    @Mock
    private ReArchUtility utility;

    @Spy
    @InjectMocks
    private OrchStaticDetailsResponseTransformerDesktop transformer;

    private Map<String, String> expDataMap;

    @Before
    public void setUp() {
        expDataMap = new HashMap<>();
        expDataMap.put("LOCATION_SECTION_RATING", "true");
    }

    @Test
    public void testMapToPlacesResponseCG_WithNullInput() {
        // When
        PlacesResponseCG result = transformer.mapToPlacesResponseCG(null, expDataMap);

        // Then
        assertNull(result);
    }

    @Test
    public void testMapToPlacesResponseCG_WithEmptyCategories() {
        // Given
        PlacesResponse placesResponse = new PlacesResponse();
        placesResponse.setCardType("TEST_CARD");
        placesResponse.setCategories(Collections.emptyList());

        // When
        PlacesResponseCG result = transformer.mapToPlacesResponseCG(placesResponse, expDataMap);

        // Then
        assertNotNull(result);
        assertEquals("TEST_CARD", result.getCardType());
        assertNull(result.getCategories());
        assertNull(result.getDirectionsToReach());
        assertNull(result.getLocRatingData());
    }

    @Test
    public void testMapToPlacesResponseCG_WithValidData() {
        // Given
        PlacesResponse placesResponse = createSamplePlacesResponse();
        when(utility.isExperimentTrue(expDataMap, "locationSectionRating")).thenReturn(true);

        // When
        PlacesResponseCG result = transformer.mapToPlacesResponseCG(placesResponse, expDataMap);

        // Then
        assertNotNull(result);
        assertEquals("RESTAURANT", result.getCardType());
        assertNotNull(result.getCategories());
        assertEquals(1, result.getCategories().size());
        assertNotNull(result.getDirectionsToReach());
        assertNotNull(result.getLocRatingData());
    }

    @Test
    public void testMapToPlacesResponseCG_WithCategoryDataTagLineMapping() {
        // Given
        PlacesResponse placesResponse = new PlacesResponse();
        placesResponse.setCardType("TEST_CARD");
        
        Category category = new Category();
        category.setPriority(1);
        category.setIconUrl("test-icon.png");
        category.setCategoryType("RESTAURANT");
        
        CategoryDatum categoryDatum = new CategoryDatum();
        categoryDatum.setCategory("Italian Restaurant");
        categoryDatum.setPlaceId("place123");
        categoryDatum.setPlaceName("Pizza Place");
        
        category.setCategoryData(Arrays.asList(categoryDatum));
        placesResponse.setCategories(Arrays.asList(category));

        // When
        PlacesResponseCG result = transformer.mapToPlacesResponseCG(placesResponse, expDataMap);

        // Then
        assertNotNull(result);
        assertNotNull(result.getCategories());
        assertEquals(1, result.getCategories().size());
        
        com.mmt.hotels.clientgateway.response.Category cgCategory = result.getCategories().get(0);
//        assertEquals(1, cgCategory.getPriority());
        assertEquals("test-icon.png", cgCategory.getIconUrl());
        assertEquals("RESTAURANT", cgCategory.getCategoryType());
        
        assertNotNull(cgCategory.getCategoryData());
        assertEquals(1, cgCategory.getCategoryData().size());
        
        com.mmt.hotels.clientgateway.response.CategoryDatum cgCategoryDatum = cgCategory.getCategoryData().get(0);
        assertEquals("place123", cgCategoryDatum.getPlaceId());
        assertEquals("Pizza Place", cgCategoryDatum.getPlaceName());
        assertEquals("Italian Restaurant", cgCategoryDatum.getCategory());
        assertEquals("Italian Restaurant", cgCategoryDatum.getTagLine()); // Should be set to category value
    }

    @Test
    public void testMapToPlacesResponseCG_WithPersuasionData() {
        // Given
        PlacesResponse placesResponse = new PlacesResponse();
        placesResponse.setCardType("RESTAURANT");
        placesResponse.setRatingData(createSampleRatingDataWithPersuasion());
        when(utility.isExperimentTrue(expDataMap, "locationSectionRating")).thenReturn(true);

        // When
        PlacesResponseCG result = transformer.mapToPlacesResponseCG(placesResponse, expDataMap);

        // Then
        assertNotNull(result);
        assertNotNull(result.getLocRatingData());
        assertNotNull(result.getLocRatingData().getPersuasionList());
        assertEquals(2, result.getLocRatingData().getPersuasionList().size());
        
        LocationPersuasions persuasion1 = result.getLocRatingData().getPersuasionList().get(0);
        assertEquals("Great location near metro", persuasion1.getText());
        assertEquals("1", persuasion1.getPriority());
        
        LocationPersuasions persuasion2 = result.getLocRatingData().getPersuasionList().get(1);
        assertEquals("Safe neighborhood", persuasion2.getText());
        assertEquals("2", persuasion2.getPriority());
    }

    @Test
    public void testMapToPlacesResponseCG_WithPersuasionData_NoUGC_LOCATION() {
        // Given
        PlacesResponse placesResponse = new PlacesResponse();
        placesResponse.setCardType("RESTAURANT");
        placesResponse.setRatingData(createSampleRatingDataWithoutUGC_LOCATION());
        when(utility.isExperimentTrue(expDataMap, "locationSectionRating")).thenReturn(true);

        // When
        PlacesResponseCG result = transformer.mapToPlacesResponseCG(placesResponse, expDataMap);

        // Then
        assertNotNull(result);
        assertNotNull(result.getLocRatingData());
        assertNull(result.getLocRatingData().getPersuasionList());
    }

    @Test
    public void testMapToPlacesResponseCG_WithPersuasionData_EmptyData() {
        // Given
        PlacesResponse placesResponse = new PlacesResponse();
        placesResponse.setCardType("RESTAURANT");
        placesResponse.setRatingData(createSampleRatingDataWithEmptyPersuasion());
        when(utility.isExperimentTrue(expDataMap, "locationSectionRating")).thenReturn(true);

        // When
        PlacesResponseCG result = transformer.mapToPlacesResponseCG(placesResponse, expDataMap);

        // Then
        assertNotNull(result);
        assertNotNull(result.getLocRatingData());
        assertNull(result.getLocRatingData().getPersuasionList());
    }

    @Test
    public void testMapToPlacesResponseCG_WithPersuasionData_NullText() {
        // Given
        PlacesResponse placesResponse = new PlacesResponse();
        placesResponse.setCardType("RESTAURANT");
        placesResponse.setRatingData(createSampleRatingDataWithNullText());
        when(utility.isExperimentTrue(expDataMap, "locationSectionRating")).thenReturn(true);

        // When
        PlacesResponseCG result = transformer.mapToPlacesResponseCG(placesResponse, expDataMap);

        // Then
        assertNotNull(result);
        assertNotNull(result.getLocRatingData());
        assertNotNull(result.getLocRatingData().getPersuasionList());
        assertEquals(1, result.getLocRatingData().getPersuasionList().size()); // Only valid text should be included
        
        LocationPersuasions persuasion = result.getLocRatingData().getPersuasionList().get(0);
        assertEquals("Valid text", persuasion.getText());
        assertEquals("2", persuasion.getPriority());
    }

    @Test
    public void testMapToPlacesResponseCG_ExperimentDisabled() {
        // Given
        PlacesResponse placesResponse = createSamplePlacesResponse();
        when(utility.isExperimentTrue(expDataMap, "locationSectionRating")).thenReturn(false);

        // When
        PlacesResponseCG result = transformer.mapToPlacesResponseCG(placesResponse, expDataMap);

        // Then
        assertNotNull(result);
        assertNotNull(result.getCategories());
        assertNotNull(result.getDirectionsToReach());
        assertNull(result.getLocRatingData()); // Should be null when experiment is disabled
    }

    // Helper methods to create test data
    private PlacesResponse createSamplePlacesResponse() {
        PlacesResponse placesResponse = new PlacesResponse();
        placesResponse.setCardType("RESTAURANT");
        placesResponse.setCategories(Arrays.asList(createSampleOrchestratorCategory()));
        placesResponse.setDirectionsToReach(createSampleDirectionDetails());
        placesResponse.setRatingData(createSampleRatingData());
        return placesResponse;
    }

    private Category createSampleOrchestratorCategory() {
        Category category = new Category();
        category.setPriority(1);
        category.setIconUrl("restaurant-icon.png");
        category.setCategoryType("RESTAURANT");
        category.setCategoryData(Arrays.asList(createSampleCategoryDatum()));
        return category;
    }

    private CategoryDatum createSampleCategoryDatum() {
        CategoryDatum categoryDatum = new CategoryDatum();
        categoryDatum.setPlaceId("place123");
        categoryDatum.setPlaceName("Pizza Place");
        categoryDatum.setCategory("Italian Restaurant");
        categoryDatum.setDistance("500m");
        categoryDatum.setDistanceUnit("m");
        categoryDatum.setSeoUrl("https://example.com/seo");
        categoryDatum.setVoyId("voy123");
        categoryDatum.setCtaUrl("https://example.com/cta");
        categoryDatum.setAddress("123 Main St");
        categoryDatum.setTagLine("Best Pizza in Town");
        categoryDatum.setPerformanceTags(Arrays.asList("popular"));
        
        // Create sample PoiImage for imageList
        PoiImage poiImage = new PoiImage();
        poiImage.setUrl("https://example.com/image.jpg");
        poiImage.setThumbnail(true);
        categoryDatum.setImageList(Arrays.asList(poiImage));
        
        return categoryDatum;
    }

    private DirectionDetails createSampleDirectionDetails() {
        DirectionDetails directionDetails = new DirectionDetails();
        directionDetails.setAddressDetail("Turn left at the traffic light");
        directionDetails.setCtaText("Get Directions");
        return directionDetails;
    }

    private RatingData createSampleRatingData() {
        RatingData ratingData = new RatingData();
        ratingData.setTitle("Location Rating");
        ratingData.setSubTitle("Based on user reviews");
        ratingData.setShowIcon(true);
        
        // Skip summary for now since RatingSummary class might not be available
        ratingData.setSummary(null);
        
        DisplayItem highlight = new DisplayItem();
        highlight.setText("Clean and Safe");
        highlight.setIconUrl("clean-icon.png");
        ratingData.setHighlights(Arrays.asList(highlight));
        
        Map<String, HotelPersuasionData> persuasionMap = new HashMap<>();
        HotelPersuasionData ugcLocationData = new HotelPersuasionData();
        
        PersuasionValue persuasionValue = new PersuasionValue();
        persuasionValue.setText("Great location near metro");
        persuasionValue.setPriority(1);
        
        ugcLocationData.setData(Arrays.asList(persuasionValue));
        persuasionMap.put("UGC_LOCATION", ugcLocationData);
        ratingData.setPersuasionMap(persuasionMap);
        
        return ratingData;
    }

    private RatingData createSampleRatingDataWithPersuasion() {
        RatingData ratingData = new RatingData();
        ratingData.setTitle("Location Rating");
        ratingData.setSubTitle("Based on user reviews");
        ratingData.setShowIcon(true);
        
        Map<String, HotelPersuasionData> persuasionMap = new HashMap<>();
        HotelPersuasionData ugcLocationData = new HotelPersuasionData();
        
        PersuasionValue persuasionValue1 = new PersuasionValue();
        persuasionValue1.setText("Great location near metro");
        persuasionValue1.setPriority(1);
        
        PersuasionValue persuasionValue2 = new PersuasionValue();
        persuasionValue2.setText("Safe neighborhood");
        persuasionValue2.setPriority(2);
        
        ugcLocationData.setData(Arrays.asList(persuasionValue1, persuasionValue2));
        persuasionMap.put("UGC_LOCATION", ugcLocationData);
        ratingData.setPersuasionMap(persuasionMap);
        
        return ratingData;
    }

    private RatingData createSampleRatingDataWithoutUGC_LOCATION() {
        RatingData ratingData = new RatingData();
        ratingData.setTitle("Location Rating");
        ratingData.setSubTitle("Based on user reviews");
        ratingData.setShowIcon(true);
        
        Map<String, HotelPersuasionData> persuasionMap = new HashMap<>();
        HotelPersuasionData otherData = new HotelPersuasionData();
        persuasionMap.put("OTHER_KEY", otherData);
        ratingData.setPersuasionMap(persuasionMap);
        
        return ratingData;
    }

    private RatingData createSampleRatingDataWithEmptyPersuasion() {
        RatingData ratingData = new RatingData();
        ratingData.setTitle("Location Rating");
        ratingData.setSubTitle("Based on user reviews");
        ratingData.setShowIcon(true);
        
        Map<String, HotelPersuasionData> persuasionMap = new HashMap<>();
        HotelPersuasionData ugcLocationData = new HotelPersuasionData();
        ugcLocationData.setData(Collections.emptyList());
        persuasionMap.put("UGC_LOCATION", ugcLocationData);
        ratingData.setPersuasionMap(persuasionMap);
        
        return ratingData;
    }

    private RatingData createSampleRatingDataWithNullText() {
        RatingData ratingData = new RatingData();
        ratingData.setTitle("Location Rating");
        ratingData.setSubTitle("Based on user reviews");
        ratingData.setShowIcon(true);
        
        Map<String, HotelPersuasionData> persuasionMap = new HashMap<>();
        HotelPersuasionData ugcLocationData = new HotelPersuasionData();
        
        PersuasionValue persuasionValue1 = new PersuasionValue();
        persuasionValue1.setText(null); // Null text should be filtered out
        persuasionValue1.setPriority(1);
        
        PersuasionValue persuasionValue2 = new PersuasionValue();
        persuasionValue2.setText("Valid text");
        persuasionValue2.setPriority(2);
        
        ugcLocationData.setData(Arrays.asList(persuasionValue1, persuasionValue2));
        persuasionMap.put("UGC_LOCATION", ugcLocationData);
        ratingData.setPersuasionMap(persuasionMap);
        
        return ratingData;
    }
}