package com.mmt.hotels.clientgateway.transformer.response.ios;

import com.mmt.hotels.clientgateway.businessobjects.MyTripActionUrls;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.response.moblanding.CardInfo;
import com.mmt.hotels.clientgateway.response.moblanding.CardPayloadData;
import com.mmt.hotels.clientgateway.response.thankyou.HotelCloudCallOutData;
import com.mmt.hotels.clientgateway.response.thankyou.OnlyTodayDealInfo;
import com.mmt.hotels.clientgateway.response.thankyou.ThankYouResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.ThankYouResponseTransformer;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.UserGlobalInfo;
import com.mmt.hotels.model.response.persuasion.OnlyForTodayPersuasionDetails;
import com.mmt.hotels.model.response.txn.PersistanceMultiRoomResponseEntity;
import com.mmt.hotels.model.response.txn.PersistedMultiRoomData;
import com.mmt.hotels.pojo.listing.personalization.BGLinearGradient;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ThankYouResponseTransformerIOSTest {

    @InjectMocks
    private ThankYouResponseTransformerIOS transformer;

    @Mock
    private PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity;

    @Mock
    private FeatureFlags featureFlags;

    @Mock
    private CommonResponseTransformer commonResponseTransformer;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    private Utility utility;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(transformer, "myTripsDeeplink", "http://example.com/mytrips");
        ReflectionTestUtils.setField(transformer, "hotelDetailsRawDeepLink", "http://example.com/hoteldetails");
        ReflectionTestUtils.setField(transformer, "myBizGstInvoicesIconUrl", "http://example.com/gst-icon.png");
    }

    @Test(expected = ClientGatewayException.class)
    public void testConvertThankYouResponse() throws ErrorResponseFromDownstreamException {
        PersistanceMultiRoomResponseEntity emptyResponseEntity = new PersistanceMultiRoomResponseEntity();
        transformer.convertThankYouResponse(emptyResponseEntity, featureFlags, null);
    }

    @Test
    public void testSetOnlyTodayDealInfo_WithTitle() {
        OnlyForTodayPersuasionDetails details = new OnlyForTodayPersuasionDetails();
        details.setTitle("Special Deal");

        OnlyTodayDealInfo result = transformer.setOnlyTodayDealInfo(details);

        assertNotNull(result);
        assertEquals("Special Deal", result.getTitle());
        assertEquals("#4A4A4A", result.getTitleColor());
        assertEquals("#E6FFF9", result.getBackgroundColor());
    }

    @Test
    public void testSetOnlyTodayDealInfo_WithoutTitle() {
        OnlyForTodayPersuasionDetails details = new OnlyForTodayPersuasionDetails();

        OnlyTodayDealInfo result = transformer.setOnlyTodayDealInfo(details);

        assertNotNull(result);
        assertNull(result.getTitle());
        assertNull(result.getIconUrl());
        assertNull(result.getTitleColor());
        assertNull(result.getBackgroundColor());
    }

    @Test
    public void testGetMytripsRawDeepLinkUrl() throws Exception {
        // Arrange
        UserGlobalInfo userGlobalInfo = new UserGlobalInfo();
        ReflectionTestUtils.setField(transformer, "myTripsDeeplink", "http://example.com/mytrips");

        Map<String,String> expData = new HashMap<>();
        expData.put("testKey", "testValue");

        // Use reflection to access the protected method
        Method getMytripsRawDeepLinkUrlMethod = ThankYouResponseTransformerIOS.class.getDeclaredMethod(
                "getMytripsRawDeepLinkUrl", UserGlobalInfo.class, Map.class);
        getMytripsRawDeepLinkUrlMethod.setAccessible(true);

        // Act
        String result = (String) getMytripsRawDeepLinkUrlMethod.invoke(transformer, userGlobalInfo, expData);

        // Assert
        assertEquals("http://example.com/mytrips", result);
    }

    @Test
    public void testGetMytripsRawDeepLinkUrl_withEmptyExpData() throws Exception {
        // Arrange
        UserGlobalInfo userGlobalInfo = new UserGlobalInfo();
        ReflectionTestUtils.setField(transformer, "myTripsDeeplink", "http://example.com/mytrips");

        Map<String,String> expData = new HashMap<>();

        // Use reflection to access the protected method
        Method getMytripsRawDeepLinkUrlMethod = ThankYouResponseTransformerIOS.class.getDeclaredMethod(
                "getMytripsRawDeepLinkUrl", UserGlobalInfo.class, Map.class);
        getMytripsRawDeepLinkUrlMethod.setAccessible(true);

        // Act
        String result = (String) getMytripsRawDeepLinkUrlMethod.invoke(transformer, userGlobalInfo, expData);

        // Assert
        assertEquals("http://example.com/mytrips", result);
    }

    @Test
    public void testGetMytripsRawDeepLinkUrl_withNullExpData() throws Exception {
        // Arrange
        UserGlobalInfo userGlobalInfo = new UserGlobalInfo();
        ReflectionTestUtils.setField(transformer, "myTripsDeeplink", "http://example.com/mytrips");

        // Use reflection to access the protected method
        Method getMytripsRawDeepLinkUrlMethod = ThankYouResponseTransformerIOS.class.getDeclaredMethod(
                "getMytripsRawDeepLinkUrl", UserGlobalInfo.class, Map.class);
        getMytripsRawDeepLinkUrlMethod.setAccessible(true);

        // Act
        String result = (String) getMytripsRawDeepLinkUrlMethod.invoke(transformer, userGlobalInfo, null);

        // Assert
        assertEquals("http://example.com/mytrips", result);
    }

    @Test
    public void testGetHotelDetailsRawDeepLinkUrl() throws Exception {
        // Arrange
        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        ReflectionTestUtils.setField(transformer, "hotelDetailsRawDeepLink", "http://example.com/hoteldetails");

        // Act
        String result = transformer.getHotelDetailsRawDeepLinkUrl(persistedData);

        // Assert
        assertEquals("http://example.com/hoteldetails", result);
    }

    @Test
    public void testGetMytripActionCorpUrl() throws Exception {
        // Setup test data
        String cardType = "UPCOMING";

        // Create mock myTripsCardTypeToIconUrls
        Map<String, MyTripActionUrls> myTripsCardTypeToIconUrls = new HashMap<>();
        MyTripActionUrls urls = new MyTripActionUrls();
        ReflectionTestUtils.setField(urls, "iconUrlIosCorp", "http://example.com/ios/corp/icon");
        myTripsCardTypeToIconUrls.put(cardType, urls);

        // Set the field using reflection
        ReflectionTestUtils.setField(transformer, "myTripsCardTypeToIconUrls", myTripsCardTypeToIconUrls);

        // Test the method
        String result = transformer.getMytripActionCorpUrl(cardType);

        // Verify the result
        assertEquals("http://example.com/ios/corp/icon", result);
    }

    @Test
    public void testGetMytripActionB2CUrl() throws Exception {
        // Setup test data
        String cardType = "UPCOMING";

        // Create mock myTripsCardTypeToIconUrls
        Map<String, MyTripActionUrls> myTripsCardTypeToIconUrls = new HashMap<>();
        MyTripActionUrls urls = new MyTripActionUrls();
        ReflectionTestUtils.setField(urls, "iconUrlIos", "http://example.com/ios/b2c/icon");
        myTripsCardTypeToIconUrls.put(cardType, urls);

        // Set the field using reflection
        ReflectionTestUtils.setField(transformer, "myTripsCardTypeToIconUrls", myTripsCardTypeToIconUrls);

        // Test the method
        String result = transformer.getMytripActionB2CUrl(cardType);

        // Verify the result
        assertEquals("http://example.com/ios/b2c/icon", result);
    }

    @Test
    public void testTildeRequiredInRSQ() {
        // Act
        boolean result = transformer.tildeRequiredInRSQ();

        // Assert
        assertTrue(result);
    }

    @Test
    public void testBuildHotelCloudCallOutData() throws Exception {
        // Set up mocked responses
        when(polyglotService.getTranslatedData(anyString())).thenReturn("Mock translated text");
        when(utility.buildBgLinearGradientForHotelCloud()).thenReturn(new BGLinearGradient());

        // Use reflection to access the private method
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("buildHotelCloudCallOutData");
        method.setAccessible(true);

        // Invoke the private method
        HotelCloudCallOutData result = (HotelCloudCallOutData) method.invoke(transformer);

        // Verify the result
        assertNotNull(result);
        assertEquals("Mock translated text", result.getTitle());
        assertEquals("Mock translated text", result.getDescription());
        assertEquals("Mock translated text", result.getFooterText());
        assertEquals("http://example.com/gst-icon.png", result.getImageUrl());
        assertNotNull(result.getBgLinearGradient());

        // Verify that the polyglot service was called
        verify(polyglotService, times(3)).getTranslatedData(anyString());
        verify(utility).buildBgLinearGradientForHotelCloud();
    }
} 