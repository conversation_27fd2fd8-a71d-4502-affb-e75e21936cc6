package com.mmt.hotels.clientgateway.transformer.response.desktop;

import com.mmt.hotels.clientgateway.businessobjects.MyTripActionUrls;
import com.mmt.hotels.clientgateway.response.PixelUrlConfig;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;

import com.mmt.hotels.clientgateway.response.moblanding.CardInfo;
import com.mmt.hotels.clientgateway.response.moblanding.CardPayloadData;
import com.mmt.hotels.clientgateway.response.moblanding.GenericCardPayloadDataCG;
import com.mmt.hotels.clientgateway.response.thankyou.AmountDetail;
import com.mmt.hotels.clientgateway.response.thankyou.BookingDetails;
import com.mmt.hotels.clientgateway.response.thankyou.HotelCloudCallOutData;
import com.mmt.hotels.clientgateway.response.thankyou.ThankYouResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.ThankYouResponseTransformer;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.MultiCurrencyInfo;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.response.txn.StayDetails;
import com.mmt.hotels.model.request.payment.AddOnDetail;
import com.mmt.hotels.model.response.addon.AddOnNode;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.txn.PersistedMultiRoomData;
import com.mmt.hotels.pojo.listing.personalization.BGLinearGradient;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.THANK_YOU_PRIMARY_CTA_TITLE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import java.util.Date;
import static com.mmt.hotels.clientgateway.constants.Constants.CASHBACK_TO_WALLET;
import static com.mmt.hotels.clientgateway.constants.Constants.lpgWelcomeOffer;


@RunWith(MockitoJUnitRunner.class)
public class ThankYouResponseTransformerDesktopTest {

    @InjectMocks
    ThankYouResponseTransformerDesktop thankYouResponseTransformerDesktop;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    private Utility utility;

    @Mock
    private CommonResponseTransformer commonResponseTransformer;

    @Mock
    private com.mmt.hotels.clientgateway.util.DateUtil dateUtil;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(thankYouResponseTransformerDesktop, "myBizGstInvoicesIconUrl", "http://example.com/gst-icon.png");
        ReflectionTestUtils.setField(thankYouResponseTransformerDesktop, "dateUtil", dateUtil);
        
        // Mock dateUtil.format to return formatted dates
        when(dateUtil.format(any(Date.class), anyString())).thenAnswer(invocation -> {
            Date date = invocation.getArgument(0);
            // Simple date formatting for test purposes
            if (date.getTime() == 1672531200000L) return "2023-01-01";
            if (date.getTime() == 1672617600000L) return "2023-01-02";  
            if (date.getTime() == 1672704000000L) return "2023-01-03";
            if (date.getTime() == 1672790400000L) return "2023-01-04";
            if (date.getTime() == 1672876800000L) return "2023-01-05";
            if (date.getTime() == 1672963200000L) return "2023-01-06";
            return "formatted-date";
        });
    }

    // Helper methods to simulate the condition logic
    private boolean shouldSetRoomChangeDetails(Object bookedRatePlan, int numberOfRooms) {
        if (bookedRatePlan == null) return false;

        String checkout = getCheckoutFromMockRatePlan(bookedRatePlan);
        return checkout != null && !checkout.trim().isEmpty() && numberOfRooms > 1;
    }

    private Object createMockBookedRatePlan(String checkout) {
        Map<String, Object> mockRatePlan = new HashMap<>();
        mockRatePlan.put("checkout", checkout);
        return mockRatePlan;
    }

    private String getCheckoutFromMockRatePlan(Object ratePlan) {
        if (ratePlan instanceof Map) {
            return (String) ((Map<?, ?>) ratePlan).get("checkout");
        }
        return null;
    }

    private String formatDateForTest(String date) {
        if ("2024-03-15".equals(date)) {
            return "03/15/2024";
        }
        return date;
    }

    private Object createSimplePersuasionResponse() {
        Map<String, String> response = new HashMap<>();
        response.put("title", "Room Change Available");
        response.put("description", "You can change your room selection");
        return response;
    }

    @Test
    public void testBuildLoyaltyCashbackPersuasions() {
        boolean exception = false;
        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("test_key");
        try {
            thankYouResponseTransformerDesktop.buildLoyaltyCashbackPersuasions(getCoupon1(), new HashMap<>());
        } catch (Exception ex) {
            exception = true;
            assertNotNull(ex);
        }
        assertFalse(exception);
    }

    @Test
    public void testBuildLoyaltyCashbackPersuasions2() {
        boolean exception = false;
        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("test_key");
        try {
            thankYouResponseTransformerDesktop.buildLoyaltyCashbackPersuasions(getCoupon2(), new HashMap<>());
        } catch (Exception ex) {
            exception = true;
            assertNotNull(ex);
        }
        assertFalse(exception);
    }

    @Test
    public void testGetHotelDetailsRawDeepLinkUrl() throws NoSuchFieldException, IllegalAccessException {
        Field hotelDetailsRawDeepLink = ThankYouResponseTransformerDesktop.class.getDeclaredField("hotelDetailsRawDeepLink");
        hotelDetailsRawDeepLink.setAccessible(true);
        hotelDetailsRawDeepLink.set(thankYouResponseTransformerDesktop, "test");
        String result;
        result = thankYouResponseTransformerDesktop.getHotelDetailsRawDeepLinkUrl(null);
        assertNotNull(result);
        assertEquals("test", result);
    }

    @Test
    public void testGetMytripsRawDeepLinkUrl() throws NoSuchFieldException, IllegalAccessException {
        Field myTripsDeeplink = ThankYouResponseTransformerDesktop.class.getDeclaredField("myTripsDeeplink");
        myTripsDeeplink.setAccessible(true);
        myTripsDeeplink.set(thankYouResponseTransformerDesktop, "test");

        // Test with empty expData
        Map<String, String> emptyExpData = new HashMap<>();
        String result = thankYouResponseTransformerDesktop.getMytripsRawDeepLinkUrl(null, emptyExpData);
        assertNotNull(result);
        assertEquals("test", result);

        // Test with expData containing values
        Map<String, String> expDataWithValues = new HashMap<>();
        expDataWithValues.put("testKey", "testValue");
        result = thankYouResponseTransformerDesktop.getMytripsRawDeepLinkUrl(null, expDataWithValues);
        assertNotNull(result);
        assertEquals("test", result);

        // Test with null expData
        result = thankYouResponseTransformerDesktop.getMytripsRawDeepLinkUrl(null, null);
        assertNotNull(result);
        assertEquals("test", result);
    }

    @Test
    public void getTildeRequiredInRSQ() {
        boolean exception = false;
        try {
            thankYouResponseTransformerDesktop.tildeRequiredInRSQ();
        } catch (Exception ex) {
            assertNotNull(ex);
            exception = true;
        }
        assertFalse(exception);
    }

    private BestCoupon getCoupon1() {
        BestCoupon coupon = new BestCoupon();
        coupon.setCouponCode("1234");
        coupon.setLoyaltyOfferMessage("abcd");
        return coupon;
    }

    private BestCoupon getCoupon2() {
        BestCoupon coupon = new BestCoupon();
        coupon.setCouponCode("1234");
        Map<String, Double> hybridDiscountMap = new HashMap<>();
        hybridDiscountMap.put(CASHBACK_TO_WALLET, 1000.0);
        coupon.setHybridDiscounts(hybridDiscountMap);
        return coupon;
    }

    @Test
    public void updateCurrencySymbol_test() {
        ThankYouResponse thankYouResponse = new ThankYouResponse();
        AmountDetail paidAmount = new AmountDetail();
        thankYouResponse.setPaidAmount(paidAmount);
        AmountDetail totalAmount = new AmountDetail();
        thankYouResponse.setTotalAmount(totalAmount);
        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        MultiCurrencyInfo multiCurrencyInfo = new MultiCurrencyInfo();
        multiCurrencyInfo.setIsMultiCurrencyV2FlowEnabled(true);
        multiCurrencyInfo.setUserCurrency("INR");
        persistedData.setMultiCurrencyInfo(multiCurrencyInfo);
        thankYouResponseTransformerDesktop.updateCurrencySymbol(thankYouResponse, persistedData);
    }


    @Test
    public void updateInsuranceInfo_test() {
        ThankYouResponse thankYouResponse = new ThankYouResponse();
        thankYouResponse.setBookingDetails(new BookingDetails());
        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        AddOnDetail addonInfo = new AddOnDetail();
        List<AddOnNode> addonList = new ArrayList<>();
        AddOnNode addon1 = new AddOnNode();
        addon1.setAddOnType("CHARITY");
        addonList.add(addon1);
        AddOnNode addon = new AddOnNode();
        addon.setAddOnType("INSURANCE");
        addonList.add(addon);
        addonInfo.setAddOnNode(addonList);
        persistedData.setAddOnInfo(addonInfo);
        thankYouResponseTransformerDesktop.updateInsuranceInfo(thankYouResponse, persistedData);
        Assert.assertTrue(thankYouResponse.getBookingDetails().isBookingWithInsurance());
    }

    @Test
    public void buildLpgCardMap_test() {
        ThankYouResponse thankYouResponse = new ThankYouResponse();
        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        PriceByHotelsRequestBody availReqBody = new PriceByHotelsRequestBody();
        availReqBody.setSiteDomain("IN");
        Map<String,String > expData = new HashMap<>();
        expData.put(lpgWelcomeOffer,"true");
        persistedData.setExpData(expData);
        persistedData.setAvailReqBody(availReqBody);
        Mockito.when(utility.isWelcomeMMTCouponApplied(Mockito.any())).thenReturn(true);
        Mockito.when(utility.isExperimentTrue(Mockito.any(),Mockito.anyString())).thenReturn(true);
        CardInfo cardInfo = new CardInfo();
        cardInfo.setIconURL("icon_url");
        ReflectionTestUtils.setField(thankYouResponseTransformerDesktop,"lgpWelcomeCardData",cardInfo);
        thankYouResponse.setCardsMap(thankYouResponseTransformerDesktop.buildLpgCardMap(persistedData));
        Assert.assertNotNull(thankYouResponse.getCardsMap());
        Assert.assertNotNull(thankYouResponse.getCardsMap().get("mec"));
        Assert.assertEquals("icon_url",thankYouResponse.getCardsMap().get("mec").getIconURL());
        availReqBody.setSiteDomain("AE");
        thankYouResponse.setCardsMap(thankYouResponseTransformerDesktop.buildLpgCardMap(persistedData));
        Assert.assertTrue(thankYouResponse.getCardsMap().isEmpty());
    }

    @Test
    public void testGetMytripActionCorpUrl() throws Exception {
        // Setup test data
        String cardType = "UPCOMING";

        // Create mock myTripsCardTypeToIconUrls
        Map<String, MyTripActionUrls> myTripsCardTypeToIconUrls = new HashMap<>();
        MyTripActionUrls urls = new MyTripActionUrls();
        // Use ReflectionTestUtils to set the field since it might not have a setter
        ReflectionTestUtils.setField(urls, "iconUrlAndroidCorp", "http://example.com/desktop/corp/icon");
        myTripsCardTypeToIconUrls.put(cardType, urls);

        // Set the field using reflection
        ReflectionTestUtils.setField(thankYouResponseTransformerDesktop, "myTripsCardTypeToIconUrls", myTripsCardTypeToIconUrls);

        // Use reflection to access protected method
        Method method = ThankYouResponseTransformerDesktop.class.getDeclaredMethod("getMytripActionCorpUrl", String.class);
        method.setAccessible(true);

        // Test the method
        String result = (String) method.invoke(thankYouResponseTransformerDesktop, cardType);

        // Verify the result
        assertEquals("http://example.com/desktop/corp/icon", result);
    }

    @Test
    public void testGetMytripActionB2CUrl() throws Exception {
        // Setup test data
        String cardType = "UPCOMING";

        // Create mock myTripsCardTypeToIconUrls
        Map<String, MyTripActionUrls> myTripsCardTypeToIconUrls = new HashMap<>();
        MyTripActionUrls urls = new MyTripActionUrls();
        // Use ReflectionTestUtils to set the field since it might not have a setter
        ReflectionTestUtils.setField(urls, "iconUrlAndroid", "http://example.com/desktop/b2c/icon");
        myTripsCardTypeToIconUrls.put(cardType, urls);

        // Set the field using reflection
        ReflectionTestUtils.setField(thankYouResponseTransformerDesktop, "myTripsCardTypeToIconUrls", myTripsCardTypeToIconUrls);

        // Use reflection to access protected method
        Method method = ThankYouResponseTransformerDesktop.class.getDeclaredMethod("getMytripActionB2CUrl", String.class);
        method.setAccessible(true);

        // Test the method
        String result = (String) method.invoke(thankYouResponseTransformerDesktop, cardType);

        // Verify the result
        assertEquals("http://example.com/desktop/b2c/icon", result);
    }

    @Test
    public void testBuildForexAndCabCard() {
        // Setup test data
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put("forexCard", "true");
        expDataMap.put("cabCard", "true");
        String cityCode = "DEL";
        String cabsDeepLinkUrl = "http://example.com/cabs";
        boolean bookingDeviceDesktop = true;
        boolean isThankyouV2 = false;

        // Mock behavior of polyglotService
        when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated Title");

        // Mock behavior of commonResponseTransformer
        CardPayloadData cardPayloadData = new CardPayloadData();
        // Create a properly typed list for GenericCardPayloadDataCG
        List<GenericCardPayloadDataCG> genericCardData = new ArrayList<>();
        // Add a mock GenericCardPayloadDataCG instance
        GenericCardPayloadDataCG payloadData = new GenericCardPayloadDataCG();
        genericCardData.add(payloadData);
        cardPayloadData.setGenericCardData(genericCardData);

        when(commonResponseTransformer.buildForexAndCabCardPayload(any(), anyString(), anyString(), any(Boolean.class), anyString()))
            .thenReturn(cardPayloadData);

        // Call the method
        Map<String, CardInfo> result = thankYouResponseTransformerDesktop.buildForexAndCabCard(
                expDataMap, cityCode, cabsDeepLinkUrl, bookingDeviceDesktop, isThankyouV2);

        // Verify the result
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertTrue(result.containsKey(FOREX_CAB_CARD_ID));

        CardInfo cardInfo = result.get(FOREX_CAB_CARD_ID);
        assertEquals("Translated Title", cardInfo.getTitleText());
        assertEquals(FOREX_CAB_CARD_TEMPLATE_ID, cardInfo.getTemplateId());
        assertNotNull(cardInfo.getCardPayload());
        assertNotNull(cardInfo.getCardPayload().getGenericCardData());
        assertFalse(cardInfo.getCardPayload().getGenericCardData().isEmpty());
    }

    @Test
    public void testBuildHotelCloudCallOutData() throws Exception {
        // Set up mocked responses
        when(polyglotService.getTranslatedData(anyString())).thenReturn("Mock translated text");
        when(utility.buildBgLinearGradientForHotelCloud()).thenReturn(new BGLinearGradient());

        // Use reflection to access the private method
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("buildHotelCloudCallOutData");
        method.setAccessible(true);

        // Invoke the private method
        HotelCloudCallOutData result = (HotelCloudCallOutData) method.invoke(thankYouResponseTransformerDesktop);

        // Verify the result
        assertNotNull(result);
        assertEquals("Mock translated text", result.getTitle());
        assertEquals("Mock translated text", result.getDescription());
        assertEquals("Mock translated text", result.getFooterText());
        assertEquals("http://example.com/gst-icon.png", result.getImageUrl());
        assertNotNull(result.getBgLinearGradient());

        // Verify that the polyglot service was called the right number of times
        verify(polyglotService, times(3)).getTranslatedData(anyString());
        verify(utility).buildBgLinearGradientForHotelCloud();
    }

    @Test
    public void testPrimaryCTATitleTranslation() {
        // Setup
        when(polyglotService.getTranslatedData(eq(THANK_YOU_PRIMARY_CTA_TITLE))).thenReturn("View Booking");

        // Verify that the correct translation key is used for the CTA title
        assertEquals("View Booking", polyglotService.getTranslatedData(THANK_YOU_PRIMARY_CTA_TITLE));
        verify(polyglotService).getTranslatedData(eq(THANK_YOU_PRIMARY_CTA_TITLE));
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithNullRoomStayCandidates_ShouldReturnZero() throws Exception {
        // Arrange
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", List.class);
        method.setAccessible(true);

        // Act
        int result = (int) method.invoke(thankYouResponseTransformerDesktop, (Object) null);

        // Assert
        assertEquals(0, result);
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithEmptyRoomStayCandidates_ShouldReturnZero() throws Exception {
        // Arrange
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", List.class);
        method.setAccessible(true);
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();

        // Act
        int result = (int) method.invoke(thankYouResponseTransformerDesktop, roomStayCandidates);

        // Assert
        assertEquals(0, result);
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithValidRoomStayCandidates_ShouldReturnCorrectCount() throws Exception {
        // Arrange
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", List.class);
        method.setAccessible(true);

        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        List<GuestCount> guestCounts = new ArrayList<>();
        GuestCount guestCount = new GuestCount();
        guestCount.setCount("2"); // 2 adults

        List<Integer> childAges = new ArrayList<>();
        childAges.add(5);
        childAges.add(8);
        guestCount.setAges(childAges); // 2 children

        guestCounts.add(guestCount);
        roomStayCandidate.setGuestCounts(guestCounts);
        roomStayCandidates.add(roomStayCandidate);

        // Act
        int result = (int) method.invoke(thankYouResponseTransformerDesktop, roomStayCandidates);

        // Assert
        assertEquals(4, result); // 2 adults + 2 children = 4 guests
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithMultipleRooms_ShouldReturnTotalCount() throws Exception {
        // Arrange
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", List.class);
        method.setAccessible(true);

        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();

        // Room 1: 2 adults, 1 child
        RoomStayCandidate roomStayCandidate1 = new RoomStayCandidate();
        List<GuestCount> guestCounts1 = new ArrayList<>();
        GuestCount guestCount1 = new GuestCount();
        guestCount1.setCount("2");

        List<Integer> childAges1 = new ArrayList<>();
        childAges1.add(5);
        guestCount1.setAges(childAges1);

        guestCounts1.add(guestCount1);
        roomStayCandidate1.setGuestCounts(guestCounts1);
        roomStayCandidates.add(roomStayCandidate1);

        // Room 2: 1 adult, 2 children
        RoomStayCandidate roomStayCandidate2 = new RoomStayCandidate();
        List<GuestCount> guestCounts2 = new ArrayList<>();
        GuestCount guestCount2 = new GuestCount();
        guestCount2.setCount("1");

        List<Integer> childAges2 = new ArrayList<>();
        childAges2.add(7);
        childAges2.add(9);
        guestCount2.setAges(childAges2);

        guestCounts2.add(guestCount2);
        roomStayCandidate2.setGuestCounts(guestCounts2);
        roomStayCandidates.add(roomStayCandidate2);

        // Act
        int result = (int) method.invoke(thankYouResponseTransformerDesktop, roomStayCandidates);

        // Assert
        assertEquals(6, result); // 3 adults + 3 children = 6 guests total
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithNullGuestCounts_ShouldReturnZero() throws Exception {
        // Arrange
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", List.class);
        method.setAccessible(true);

        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setGuestCounts(null);
        roomStayCandidates.add(roomStayCandidate);

        // Act
        int result = (int) method.invoke(thankYouResponseTransformerDesktop, roomStayCandidates);

        // Assert
        assertEquals(0, result);
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithEmptyGuestCounts_ShouldReturnZero() throws Exception {
        // Arrange
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", List.class);
        method.setAccessible(true);

        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setGuestCounts(new ArrayList<>());
        roomStayCandidates.add(roomStayCandidate);

        // Act
        int result = (int) method.invoke(thankYouResponseTransformerDesktop, roomStayCandidates);

        // Assert
        assertEquals(0, result);
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithNoChildrenAges_ShouldCountOnlyAdults() throws Exception {
        // Arrange
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", List.class);
        method.setAccessible(true);

        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        List<GuestCount> guestCounts = new ArrayList<>();
        GuestCount guestCount = new GuestCount();
        guestCount.setCount("3");
        guestCount.setAges(null); // No children

        guestCounts.add(guestCount);
        roomStayCandidate.setGuestCounts(guestCounts);
        roomStayCandidates.add(roomStayCandidate);

        // Act
        int result = (int) method.invoke(thankYouResponseTransformerDesktop, roomStayCandidates);

        // Assert
        assertEquals(3, result); // 3 adults, 0 children = 3 guests
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithEmptyChildrenAges_ShouldCountOnlyAdults() throws Exception {
        // Arrange
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", List.class);
        method.setAccessible(true);

        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        List<GuestCount> guestCounts = new ArrayList<>();
        GuestCount guestCount = new GuestCount();
        guestCount.setCount("3");
        guestCount.setAges(new ArrayList<>()); // Empty children list

        guestCounts.add(guestCount);
        roomStayCandidate.setGuestCounts(guestCounts);
        roomStayCandidates.add(roomStayCandidate);

        // Act
        int result = (int) method.invoke(thankYouResponseTransformerDesktop, roomStayCandidates);

        // Assert
        assertEquals(3, result); // 3 adults, 0 children = 3 guests
    }

    @Test
    public void testRoomChangeDetailsCondition_MultipleRooms_ValidCheckout() {
        // Test the specific condition: checkout != null && !isEmpty(checkout) && numberOfRooms > 1
        
        // Mock a BookedRatePlan with valid checkout
        Object mockBookedRatePlan = createMockBookedRatePlan("2024-03-15");
        int numberOfRooms = 2; // Greater than 1
        
        // Test that the condition should be true
        boolean shouldSetRoomChangeDetails = shouldSetRoomChangeDetails(mockBookedRatePlan, numberOfRooms);
        Assert.assertTrue("Room change details should be set when checkout is valid and numberOfRooms > 1", shouldSetRoomChangeDetails);
    }

    @Test
    public void testRoomChangeDetailsCondition_SingleRoom_ValidCheckout() {
        // Test the specific condition: numberOfRooms <= 1
        
        // Mock a BookedRatePlan with valid checkout
        Object mockBookedRatePlan = createMockBookedRatePlan("2024-03-15");
        int numberOfRooms = 1; // Equal to 1
        
        // Test that the condition should be false
        boolean shouldSetRoomChangeDetails = shouldSetRoomChangeDetails(mockBookedRatePlan, numberOfRooms);
        Assert.assertFalse("Room change details should NOT be set when numberOfRooms <= 1", shouldSetRoomChangeDetails);
    }

    @Test
    public void testRoomChangeDetailsCondition_NullCheckout_MultipleRooms() {
        // Test the specific condition: checkout == null
        
        // Mock a BookedRatePlan with null checkout
        Object mockBookedRatePlan = createMockBookedRatePlan(null);
        int numberOfRooms = 2; // Greater than 1
        
        // Test that the condition should be false
        boolean shouldSetRoomChangeDetails = shouldSetRoomChangeDetails(mockBookedRatePlan, numberOfRooms);
        Assert.assertFalse("Room change details should NOT be set when checkout is null", shouldSetRoomChangeDetails);
    }

    @Test
    public void testRoomChangeDetailsCondition_EmptyCheckout_MultipleRooms() {
        // Test the specific condition: isEmpty(checkout)
        
        // Mock a BookedRatePlan with empty checkout
        Object mockBookedRatePlan = createMockBookedRatePlan("");
        int numberOfRooms = 2; // Greater than 1
        
        // Test that the condition should be false
        boolean shouldSetRoomChangeDetails = shouldSetRoomChangeDetails(mockBookedRatePlan, numberOfRooms);
        Assert.assertFalse("Room change details should NOT be set when checkout is empty", shouldSetRoomChangeDetails);
    }

    @Test
    public void testRoomChangeDetailsCondition_WhitespaceCheckout_MultipleRooms() {
        // Test the specific condition: isEmpty(checkout) with whitespace
        
        // Mock a BookedRatePlan with whitespace checkout
        Object mockBookedRatePlan = createMockBookedRatePlan("   ");
        int numberOfRooms = 2; // Greater than 1
        
        // Test that the condition should be false
        boolean shouldSetRoomChangeDetails = shouldSetRoomChangeDetails(mockBookedRatePlan, numberOfRooms);
        Assert.assertFalse("Room change details should NOT be set when checkout is whitespace", shouldSetRoomChangeDetails);
    }

    @Test
    public void testDateFormattingAndPersuasionCalling() {
        // Test that when conditions are met, date formatting and persuasion building are called
        
        String testCheckout = "2024-03-15";
        int numberOfRooms = 3;
        
        // Test the scenario when all conditions are met
        boolean shouldProceed = shouldSetRoomChangeDetails(createMockBookedRatePlan(testCheckout), numberOfRooms);
        
        if (shouldProceed) {
            // Verify the date formatting logic
            String expectedFormattedDate = formatDateForTest(testCheckout);
            assertEquals("03/15/2024", expectedFormattedDate);
            
            // This test verifies the condition logic itself
            Assert.assertTrue("Should proceed when conditions are met", shouldProceed);
        }
    }

    @Test
    public void testGetUniqueCheckinCheckoutCombinationsCount_NullPersistedData_ShouldReturnZero() throws Exception {
        // Arrange
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getUniqueCheckinCheckoutCombinationsCount", PersistedMultiRoomData.class);
        method.setAccessible(true);

        // Act
        int result = (int) method.invoke(thankYouResponseTransformerDesktop, (Object) null);

        // Assert
        assertEquals(0, result);
    }

    @Test
    public void testGetUniqueCheckinCheckoutCombinationsCount_EmptyHotelList_ShouldReturnZero() throws Exception {
        // Arrange
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getUniqueCheckinCheckoutCombinationsCount", PersistedMultiRoomData.class);
        method.setAccessible(true);

        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        persistedData.setHotelList(new ArrayList<>());

        // Act
        int result = (int) method.invoke(thankYouResponseTransformerDesktop, persistedData);

        // Assert
        assertEquals(0, result);
    }

    @Test
    public void testGetUniqueCheckinCheckoutCombinationsCount_NullHotelList_ShouldReturnZero() throws Exception {
        // Arrange
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getUniqueCheckinCheckoutCombinationsCount", PersistedMultiRoomData.class);
        method.setAccessible(true);

        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        persistedData.setHotelList(null);

        // Act
        int result = (int) method.invoke(thankYouResponseTransformerDesktop, persistedData);

        // Assert
        assertEquals(0, result);
    }

    @Test
    public void testGetUniqueCheckinCheckoutCombinationsCount_EmptyTariffInfoList_ShouldReturnZero() throws Exception {
        // Arrange
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getUniqueCheckinCheckoutCombinationsCount", PersistedMultiRoomData.class);
        method.setAccessible(true);

        PersistedMultiRoomData persistedData = createPersistedDataWithEmptyTariffList();

        // Act
        int result = (int) method.invoke(thankYouResponseTransformerDesktop, persistedData);

        // Assert
        assertEquals(0, result);
    }

    @Test
    public void testGetUniqueCheckinCheckoutCombinationsCount_NullStayDetails_ShouldReturnZero() throws Exception {
        // Arrange
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getUniqueCheckinCheckoutCombinationsCount", PersistedMultiRoomData.class);
        method.setAccessible(true);

        PersistedMultiRoomData persistedData = createPersistedDataWithNullStayDetails();

        // Act
        int result = (int) method.invoke(thankYouResponseTransformerDesktop, persistedData);

        // Assert
        assertEquals(0, result);
    }

    @Test
    public void testGetUniqueCheckinCheckoutCombinationsCount_NullDates_ShouldReturnOne() throws Exception {
        // Arrange
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getUniqueCheckinCheckoutCombinationsCount", PersistedMultiRoomData.class);
        method.setAccessible(true);

        PersistedMultiRoomData persistedData = createPersistedDataWithNullDates();

        // Act
        int result = (int) method.invoke(thankYouResponseTransformerDesktop, persistedData);

        // Assert
        assertEquals(1, result); // Should return 1 for "null|null" combination
    }

    @Test
    public void testGetUniqueCheckinCheckoutCombinationsCount_SameDates_ShouldReturnOne() throws Exception {
        // Arrange
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getUniqueCheckinCheckoutCombinationsCount", PersistedMultiRoomData.class);
        method.setAccessible(true);

        PersistedMultiRoomData persistedData = createPersistedDataWithSameDates();

        // Act
        int result = (int) method.invoke(thankYouResponseTransformerDesktop, persistedData);

        // Assert
        assertEquals(1, result); // Multiple tariff infos with same dates should return 1
    }

    @Test
    public void testGetUniqueCheckinCheckoutCombinationsCount_DifferentDates_ShouldReturnCorrectCount() throws Exception {
        // Arrange
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getUniqueCheckinCheckoutCombinationsCount", PersistedMultiRoomData.class);
        method.setAccessible(true);

        PersistedMultiRoomData persistedData = createPersistedDataWithDifferentDates();

        // Act
        int result = (int) method.invoke(thankYouResponseTransformerDesktop, persistedData);

        // Assert
        assertEquals(2, result); // Two different date combinations should return 2
    }

    @Test
    public void testGetUniqueCheckinCheckoutCombinationsCount_MixedNullAndValidDates_ShouldReturnCorrectCount() throws Exception {
        // Arrange
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getUniqueCheckinCheckoutCombinationsCount", PersistedMultiRoomData.class);
        method.setAccessible(true);

        PersistedMultiRoomData persistedData = createPersistedDataWithMixedDates();

        // Act
        int result = (int) method.invoke(thankYouResponseTransformerDesktop, persistedData);

        // Assert
        assertEquals(2, result); // Should handle mix of null and valid dates properly
    }

    @Test
    public void testGetUniqueCheckinCheckoutCombinationsCount_MultipleIdenticalCombinations_ShouldReturnOne() throws Exception {
        // Arrange
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getUniqueCheckinCheckoutCombinationsCount", PersistedMultiRoomData.class);
        method.setAccessible(true);

        PersistedMultiRoomData persistedData = createPersistedDataWithMultipleIdenticalCombinations();

        // Act
        int result = (int) method.invoke(thankYouResponseTransformerDesktop, persistedData);

        // Assert
        assertEquals(1, result); // Multiple identical combinations should be deduplicated
    }

    @Test
    public void testGetUniqueCheckinCheckoutCombinationsCount_ThreeUniqueCombinations_ShouldReturnThree() throws Exception {
        // Arrange
        Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getUniqueCheckinCheckoutCombinationsCount", PersistedMultiRoomData.class);
        method.setAccessible(true);

        PersistedMultiRoomData persistedData = createPersistedDataWithThreeUniqueCombinations();

        // Act
        int result = (int) method.invoke(thankYouResponseTransformerDesktop, persistedData);

        // Assert
        assertEquals(3, result); // Three unique combinations should return 3
    }

    // Helper methods for creating test data

    private PersistedMultiRoomData createPersistedDataWithEmptyTariffList() {
        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        List<com.mmt.hotels.model.response.txn.PersistedHotel> hotelList = new ArrayList<>();
        com.mmt.hotels.model.response.txn.PersistedHotel hotel = new com.mmt.hotels.model.response.txn.PersistedHotel();
        hotel.setTariffInfoList(new ArrayList<>());
        hotelList.add(hotel);
        persistedData.setHotelList(hotelList);
        return persistedData;
    }

    private PersistedMultiRoomData createPersistedDataWithNullStayDetails() {
        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        List<com.mmt.hotels.model.response.txn.PersistedHotel> hotelList = new ArrayList<>();
        com.mmt.hotels.model.response.txn.PersistedHotel hotel = new com.mmt.hotels.model.response.txn.PersistedHotel();
        
        List<com.mmt.hotels.model.response.txn.PersistedTariffInfo> tariffInfoList = new ArrayList<>();
        com.mmt.hotels.model.response.txn.PersistedTariffInfo tariffInfo = new com.mmt.hotels.model.response.txn.PersistedTariffInfo();
        tariffInfo.setStayDetails(null);
        tariffInfoList.add(tariffInfo);
        
        hotel.setTariffInfoList(tariffInfoList);
        hotelList.add(hotel);
        persistedData.setHotelList(hotelList);
        return persistedData;
    }

    private PersistedMultiRoomData createPersistedDataWithNullDates() {
        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        List<com.mmt.hotels.model.response.txn.PersistedHotel> hotelList = new ArrayList<>();
        com.mmt.hotels.model.response.txn.PersistedHotel hotel = new com.mmt.hotels.model.response.txn.PersistedHotel();
        
        List<com.mmt.hotels.model.response.txn.PersistedTariffInfo> tariffInfoList = new ArrayList<>();
        com.mmt.hotels.model.response.txn.PersistedTariffInfo tariffInfo = new com.mmt.hotels.model.response.txn.PersistedTariffInfo();
        StayDetails stayDetails = new StayDetails();
        stayDetails.setCheckIn(null);
        stayDetails.setCheckOut(null);
        tariffInfo.setStayDetails(stayDetails);
        tariffInfoList.add(tariffInfo);
        
        hotel.setTariffInfoList(tariffInfoList);
        hotelList.add(hotel);
        persistedData.setHotelList(hotelList);
        return persistedData;
    }

    private PersistedMultiRoomData createPersistedDataWithSameDates() {
        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        List<com.mmt.hotels.model.response.txn.PersistedHotel> hotelList = new ArrayList<>();
        com.mmt.hotels.model.response.txn.PersistedHotel hotel = new com.mmt.hotels.model.response.txn.PersistedHotel();
        
        List<com.mmt.hotels.model.response.txn.PersistedTariffInfo> tariffInfoList = new ArrayList<>();
        
        // First tariff info with same dates
        com.mmt.hotels.model.response.txn.PersistedTariffInfo tariffInfo1 = new com.mmt.hotels.model.response.txn.PersistedTariffInfo();
        StayDetails stayDetails1 = new StayDetails();
        stayDetails1.setCheckIn(new Date(1672531200000L)); // 2023-01-01
        stayDetails1.setCheckOut(new Date(1672617600000L)); // 2023-01-02
        tariffInfo1.setStayDetails(stayDetails1);
        tariffInfoList.add(tariffInfo1);
        
        // Second tariff info with same dates
        com.mmt.hotels.model.response.txn.PersistedTariffInfo tariffInfo2 = new com.mmt.hotels.model.response.txn.PersistedTariffInfo();
        StayDetails stayDetails2 = new StayDetails();
        stayDetails2.setCheckIn(new Date(1672531200000L)); // 2023-01-01
        stayDetails2.setCheckOut(new Date(1672617600000L)); // 2023-01-02
        tariffInfo2.setStayDetails(stayDetails2);
        tariffInfoList.add(tariffInfo2);
        
        hotel.setTariffInfoList(tariffInfoList);
        hotelList.add(hotel);
        persistedData.setHotelList(hotelList);
        return persistedData;
    }

    private PersistedMultiRoomData createPersistedDataWithDifferentDates() {
        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        List<com.mmt.hotels.model.response.txn.PersistedHotel> hotelList = new ArrayList<>();
        com.mmt.hotels.model.response.txn.PersistedHotel hotel = new com.mmt.hotels.model.response.txn.PersistedHotel();
        
        List<com.mmt.hotels.model.response.txn.PersistedTariffInfo> tariffInfoList = new ArrayList<>();
        
        // First tariff info with first set of dates
        com.mmt.hotels.model.response.txn.PersistedTariffInfo tariffInfo1 = new com.mmt.hotels.model.response.txn.PersistedTariffInfo();
        StayDetails stayDetails1 = new StayDetails();
        stayDetails1.setCheckIn(new Date(1672531200000L)); // 2023-01-01
        stayDetails1.setCheckOut(new Date(1672617600000L)); // 2023-01-02
        tariffInfo1.setStayDetails(stayDetails1);
        tariffInfoList.add(tariffInfo1);
        
        // Second tariff info with different dates
        com.mmt.hotels.model.response.txn.PersistedTariffInfo tariffInfo2 = new com.mmt.hotels.model.response.txn.PersistedTariffInfo();
        StayDetails stayDetails2 = new StayDetails();
        stayDetails2.setCheckIn(new Date(1672704000000L)); // 2023-01-03
        stayDetails2.setCheckOut(new Date(1672790400000L)); // 2023-01-04
        tariffInfo2.setStayDetails(stayDetails2);
        tariffInfoList.add(tariffInfo2);
        
        hotel.setTariffInfoList(tariffInfoList);
        hotelList.add(hotel);
        persistedData.setHotelList(hotelList);
        return persistedData;
    }

    private PersistedMultiRoomData createPersistedDataWithMixedDates() {
        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        List<com.mmt.hotels.model.response.txn.PersistedHotel> hotelList = new ArrayList<>();
        com.mmt.hotels.model.response.txn.PersistedHotel hotel = new com.mmt.hotels.model.response.txn.PersistedHotel();
        
        List<com.mmt.hotels.model.response.txn.PersistedTariffInfo> tariffInfoList = new ArrayList<>();
        
        // First tariff info with null dates
        com.mmt.hotels.model.response.txn.PersistedTariffInfo tariffInfo1 = new com.mmt.hotels.model.response.txn.PersistedTariffInfo();
        StayDetails stayDetails1 = new StayDetails();
        stayDetails1.setCheckIn(null);
        stayDetails1.setCheckOut(null);
        tariffInfo1.setStayDetails(stayDetails1);
        tariffInfoList.add(tariffInfo1);
        
        // Second tariff info with valid dates
        com.mmt.hotels.model.response.txn.PersistedTariffInfo tariffInfo2 = new com.mmt.hotels.model.response.txn.PersistedTariffInfo();
        StayDetails stayDetails2 = new StayDetails();
        stayDetails2.setCheckIn(new Date(1672531200000L)); // 2023-01-01
        stayDetails2.setCheckOut(new Date(1672617600000L)); // 2023-01-02
        tariffInfo2.setStayDetails(stayDetails2);
        tariffInfoList.add(tariffInfo2);
        
        hotel.setTariffInfoList(tariffInfoList);
        hotelList.add(hotel);
        persistedData.setHotelList(hotelList);
        return persistedData;
    }

    private PersistedMultiRoomData createPersistedDataWithMultipleIdenticalCombinations() {
        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        List<com.mmt.hotels.model.response.txn.PersistedHotel> hotelList = new ArrayList<>();
        com.mmt.hotels.model.response.txn.PersistedHotel hotel = new com.mmt.hotels.model.response.txn.PersistedHotel();
        
        List<com.mmt.hotels.model.response.txn.PersistedTariffInfo> tariffInfoList = new ArrayList<>();
        
        // Three tariff infos with identical dates
        for (int i = 0; i < 3; i++) {
            com.mmt.hotels.model.response.txn.PersistedTariffInfo tariffInfo = new com.mmt.hotels.model.response.txn.PersistedTariffInfo();
            StayDetails stayDetails = new StayDetails();
            stayDetails.setCheckIn(new Date(1672531200000L)); // 2023-01-01
            stayDetails.setCheckOut(new Date(1672617600000L)); // 2023-01-02
            tariffInfo.setStayDetails(stayDetails);
            tariffInfoList.add(tariffInfo);
        }
        
        hotel.setTariffInfoList(tariffInfoList);
        hotelList.add(hotel);
        persistedData.setHotelList(hotelList);
        return persistedData;
    }

    private PersistedMultiRoomData createPersistedDataWithThreeUniqueCombinations() {
        PersistedMultiRoomData persistedData = new PersistedMultiRoomData();
        List<com.mmt.hotels.model.response.txn.PersistedHotel> hotelList = new ArrayList<>();
        com.mmt.hotels.model.response.txn.PersistedHotel hotel = new com.mmt.hotels.model.response.txn.PersistedHotel();
        
        List<com.mmt.hotels.model.response.txn.PersistedTariffInfo> tariffInfoList = new ArrayList<>();
        
        // First unique combination
        com.mmt.hotels.model.response.txn.PersistedTariffInfo tariffInfo1 = new com.mmt.hotels.model.response.txn.PersistedTariffInfo();
        StayDetails stayDetails1 = new StayDetails();
        stayDetails1.setCheckIn(new Date(1672531200000L)); // 2023-01-01
        stayDetails1.setCheckOut(new Date(1672617600000L)); // 2023-01-02
        tariffInfo1.setStayDetails(stayDetails1);
        tariffInfoList.add(tariffInfo1);
        
        // Second unique combination
        com.mmt.hotels.model.response.txn.PersistedTariffInfo tariffInfo2 = new com.mmt.hotels.model.response.txn.PersistedTariffInfo();
        StayDetails stayDetails2 = new StayDetails();
        stayDetails2.setCheckIn(new Date(1672704000000L)); // 2023-01-03
        stayDetails2.setCheckOut(new Date(1672790400000L)); // 2023-01-04
        tariffInfo2.setStayDetails(stayDetails2);
        tariffInfoList.add(tariffInfo2);
        
        // Third unique combination
        com.mmt.hotels.model.response.txn.PersistedTariffInfo tariffInfo3 = new com.mmt.hotels.model.response.txn.PersistedTariffInfo();
        StayDetails stayDetails3 = new StayDetails();
        stayDetails3.setCheckIn(new Date(1672876800000L)); // 2023-01-05
        stayDetails3.setCheckOut(new Date(1672963200000L)); // 2023-01-06
        tariffInfo3.setStayDetails(stayDetails3);
        tariffInfoList.add(tariffInfo3);
        
        hotel.setTariffInfoList(tariffInfoList);
        hotelList.add(hotel);
        persistedData.setHotelList(hotelList);
        return persistedData;
    }
}