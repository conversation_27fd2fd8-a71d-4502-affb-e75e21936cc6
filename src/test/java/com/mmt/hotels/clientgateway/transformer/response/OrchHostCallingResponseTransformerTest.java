package com.mmt.hotels.clientgateway.transformer.response;

import com.gommt.hotels.orchestrator.detail.model.response.HostCallingResponse;
import com.gommt.hotels.orchestrator.detail.model.response.common.ErrorResponse;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.hostcalling.HostCallingInitiateResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.response.errors.Error;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import com.mmt.hotels.pojo.response.ErrorEntity;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class OrchHostCallingResponseTransformerTest {

    @InjectMocks
    private OrchHostCallingResponseTransformer orchHostCallingResponseTransformer;

    @Mock
    private PolyglotService polyglotService;

    private HostCallingResponse validHesResponse;
    private String clientType;
    private String requestId;

    @Before
    public void setUp() {
        clientType = "ANDROID";
        requestId = "REQ123456";

        // Setup valid HES response
        validHesResponse = new HostCallingResponse();
        validHesResponse.setStatus("success");
        validHesResponse.setRequestId(requestId);
        validHesResponse.setChainName("Test Hotel Chain");
        validHesResponse.setAvailableNow(true);
        validHesResponse.setStartTime("09:00");
        validHesResponse.setEndTime("21:00");
        validHesResponse.setMaskedNumber("+91-XXXX-XX1234");

        // Setup default polyglot response
        when(polyglotService.getTranslatedData(ConstantsTranslation.MISSED_CALL_MESSAGE_CONSTANT))
                .thenReturn("You missed a call from {chainName}. They will call you back soon.");
    }

    // Tests for convertHostCallingResponse method

    @Test
    public void should_ReturnSuccessfulResponse_When_ValidHesResponse() {
        // When
        HostCallingInitiateResponse result = orchHostCallingResponseTransformer
                .convertHostCallingResponse(validHesResponse, clientType, requestId);

        // Then
        assertNotNull(result);
        assertEquals("success", result.getStatus());
        assertEquals(requestId, result.getRequestId());
        assertEquals("Test Hotel Chain", result.getChainName());
        assertTrue(result.getAvailableNow());
        assertEquals("09:00", result.getStartTime());
        assertEquals("21:00", result.getEndTime());
        assertEquals("+91-XXXX-XX1234", result.getMaskedNumber());
    }

    @Test
    public void should_SetMissedCallMessage_When_ChainNameIsProvided() {
        // When
        HostCallingInitiateResponse result = orchHostCallingResponseTransformer
                .convertHostCallingResponse(validHesResponse, clientType, requestId);

        // Then
        assertNotNull(result.getMissedCallMessage());
        assertEquals("You missed a call from Test Hotel Chain. They will call you back soon.", 
                result.getMissedCallMessage());
        verify(polyglotService).getTranslatedData(ConstantsTranslation.MISSED_CALL_MESSAGE_CONSTANT);
    }

    @Test
    public void should_NotSetMissedCallMessage_When_ChainNameIsNull() {
        // Given
        validHesResponse.setChainName(null);

        // When
        HostCallingInitiateResponse result = orchHostCallingResponseTransformer
                .convertHostCallingResponse(validHesResponse, clientType, requestId);

        // Then
        assertNull(result.getMissedCallMessage());
    }

    @Test
    public void should_NotSetMissedCallMessage_When_ChainNameIsEmpty() {
        // Given
        validHesResponse.setChainName("");

        // When
        HostCallingInitiateResponse result = orchHostCallingResponseTransformer
                .convertHostCallingResponse(validHesResponse, clientType, requestId);

        // Then
        assertNull(result.getMissedCallMessage());
    }

    @Test
    public void should_ReturnErrorResponse_When_HesResponseIsNull() {
        // When
        HostCallingInitiateResponse result = orchHostCallingResponseTransformer
                .convertHostCallingResponse(null, clientType, requestId);

        // Then
        assertNotNull(result);
        assertEquals("error", result.getStatus());
        assertEquals(requestId, result.getRequestId());
        assertFalse(result.getAvailableNow());
    }

    @Test
    public void should_ConvertErrorList_When_HesResponseHasErrors() {
        // Given
        List<ErrorResponse> errorList = new ArrayList<>();
        ErrorResponse error1 = new ErrorResponse();
        error1.setCode("HC001");
        error1.setMessage("Host calling service unavailable");
        ErrorResponse error2 = new ErrorResponse();
        error2.setCode("HC002");
        error2.setMessage("Invalid time slot");
        errorList.add(error1);
        errorList.add(error2);
        validHesResponse.setErrorList(errorList);

        // When
        HostCallingInitiateResponse result = orchHostCallingResponseTransformer
                .convertHostCallingResponse(validHesResponse, clientType, requestId);

        // Then
        assertNotNull(result.getResponseErrors());
        assertNotNull(result.getResponseErrors().getErrorList());
        assertEquals(2, result.getResponseErrors().getErrorList().size());
        
        Error cgError1 = result.getResponseErrors().getErrorList().get(0);
        assertEquals("HC001", cgError1.getErrorCode());
        assertEquals("Host calling service unavailable", cgError1.getErrorMessage());
    }

    @Test
    public void should_ConvertSingleError_When_HesResponseHasSingleError() {
        // Given
        ErrorResponse singleError = new ErrorResponse();
        singleError.setCode("HC003");
        singleError.setMessage("Validation failed");
        validHesResponse.setError(singleError);

        // When
        HostCallingInitiateResponse result = orchHostCallingResponseTransformer
                .convertHostCallingResponse(validHesResponse, clientType, requestId);

        // Then
        assertNotNull(result.getErrorEntity());
        assertEquals("HC003", result.getErrorEntity().getErrorCode());
        assertEquals("Validation failed", result.getErrorEntity().getMsg());
    }

    @Test
    public void should_ReturnErrorResponse_When_TransformationThrowsException() {
        // Given
        HostCallingResponse invalidResponse = mock(HostCallingResponse.class);
        when(invalidResponse.getStatus()).thenThrow(new RuntimeException("Unexpected error"));

        // When
        HostCallingInitiateResponse result = orchHostCallingResponseTransformer
                .convertHostCallingResponse(invalidResponse, clientType, requestId);

        // Then
        assertNotNull(result);
        assertEquals("error", result.getStatus());
        assertEquals(requestId, result.getRequestId());
        assertFalse(result.getAvailableNow());
    }

    @Test
    public void should_HandlePolyglotException_When_GettingMissedCallMessage() {
        // Given
        when(polyglotService.getTranslatedData(ConstantsTranslation.MISSED_CALL_MESSAGE_CONSTANT))
                .thenThrow(new RuntimeException("Polyglot service error"));

        // When
        HostCallingInitiateResponse result = orchHostCallingResponseTransformer
                .convertHostCallingResponse(validHesResponse, clientType, requestId);

        // Then
        assertNotNull(result);
        assertEquals("Host calling is currently unavailable. Please try again during operational hours.", 
                result.getMissedCallMessage());
    }

    // Tests for private method getMissedCallMessage using reflection

    @Test
    public void should_ReplacePlaceholder_When_GetMissedCallMessageWithValidChainName() throws Exception {
        // Given
        Method method = OrchHostCallingResponseTransformer.class.getDeclaredMethod("getMissedCallMessage", String.class);
        method.setAccessible(true);

        // When
        String result = (String) method.invoke(orchHostCallingResponseTransformer, "Marriott Hotels");

        // Then
        assertEquals("You missed a call from Marriott Hotels. They will call you back soon.", result);
    }

    @Test
    public void should_ReturnBaseMessage_When_GetMissedCallMessageWithNullChainName() throws Exception {
        // Given
        Method method = OrchHostCallingResponseTransformer.class.getDeclaredMethod("getMissedCallMessage", String.class);
        method.setAccessible(true);

        // When
        String result = (String) method.invoke(orchHostCallingResponseTransformer, (String) null);

        // Then
        assertEquals("You missed a call from {chainName}. They will call you back soon.", result);
    }

    @Test
    public void should_ReturnBaseMessage_When_GetMissedCallMessageWithEmptyChainName() throws Exception {
        // Given
        Method method = OrchHostCallingResponseTransformer.class.getDeclaredMethod("getMissedCallMessage", String.class);
        method.setAccessible(true);

        // When
        String result = (String) method.invoke(orchHostCallingResponseTransformer, "");

        // Then
        assertEquals("You missed a call from {chainName}. They will call you back soon.", result);
    }

    @Test
    public void should_ReturnFallbackMessage_When_PolyglotReturnsNull() throws Exception {
        // Given
        when(polyglotService.getTranslatedData(ConstantsTranslation.MISSED_CALL_MESSAGE_CONSTANT))
                .thenReturn(null);
        Method method = OrchHostCallingResponseTransformer.class.getDeclaredMethod("getMissedCallMessage", String.class);
        method.setAccessible(true);

        // When
        String result = (String) method.invoke(orchHostCallingResponseTransformer, "Test Chain");

    }

    @Test
    public void should_ReturnFallbackMessage_When_PolyglotThrowsException() throws Exception {
        // Given
        when(polyglotService.getTranslatedData(ConstantsTranslation.MISSED_CALL_MESSAGE_CONSTANT))
                .thenThrow(new RuntimeException("Service error"));
        Method method = OrchHostCallingResponseTransformer.class.getDeclaredMethod("getMissedCallMessage", String.class);
        method.setAccessible(true);

        // When
        String result = (String) method.invoke(orchHostCallingResponseTransformer, "Test Chain");

        // Then
        assertEquals("Host calling is currently unavailable. Please try again during operational hours.", result);
    }

    // Tests for private method convertToResponseErrors using reflection

    @Test
    public void should_ConvertToResponseErrors_When_ValidErrorList() throws Exception {
        // Given
        Method method = OrchHostCallingResponseTransformer.class.getDeclaredMethod("convertToResponseErrors", List.class);
        method.setAccessible(true);
        
        List<ErrorResponse> errorList = new ArrayList<>();
        ErrorResponse error1 = new ErrorResponse();
        error1.setCode("ERR001");
        error1.setMessage("First error");
        ErrorResponse error2 = new ErrorResponse();
        error2.setCode("ERR002");
        error2.setMessage("Second error");
        errorList.add(error1);
        errorList.add(error2);

        // When
        ResponseErrors result = (ResponseErrors) method.invoke(orchHostCallingResponseTransformer, errorList);

        // Then
        assertNotNull(result);
        assertNotNull(result.getErrorList());
        assertEquals(2, result.getErrorList().size());
        assertEquals("ERR001", result.getErrorList().get(0).getErrorCode());
        assertEquals("First error", result.getErrorList().get(0).getErrorMessage());
        assertEquals("ERR002", result.getErrorList().get(1).getErrorCode());
        assertEquals("Second error", result.getErrorList().get(1).getErrorMessage());
    }

    @Test
    public void should_ReturnNull_When_ConvertToResponseErrorsWithEmptyList() throws Exception {
        // Given
        Method method = OrchHostCallingResponseTransformer.class.getDeclaredMethod("convertToResponseErrors", List.class);
        method.setAccessible(true);
        List<ErrorResponse> emptyList = new ArrayList<>();

        // When
        ResponseErrors result = (ResponseErrors) method.invoke(orchHostCallingResponseTransformer, emptyList);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_ConvertToResponseErrorsWithNullList() throws Exception {
        // Given
        Method method = OrchHostCallingResponseTransformer.class.getDeclaredMethod("convertToResponseErrors", List.class);
        method.setAccessible(true);

        // When
        ResponseErrors result = (ResponseErrors) method.invoke(orchHostCallingResponseTransformer, (List<ErrorResponse>) null);

        // Then
        assertNull(result);
    }

    @Test
    public void should_SkipNullErrors_When_ConvertToResponseErrorsWithNullEntries() throws Exception {
        // Given
        Method method = OrchHostCallingResponseTransformer.class.getDeclaredMethod("convertToResponseErrors", List.class);
        method.setAccessible(true);
        
        List<ErrorResponse> errorList = new ArrayList<>();
        ErrorResponse validError = new ErrorResponse();
        validError.setCode("ERR001");
        validError.setMessage("Valid error");
        errorList.add(validError);
        errorList.add(null); // Null error
        errorList.add(validError);

        // When
        ResponseErrors result = (ResponseErrors) method.invoke(orchHostCallingResponseTransformer, errorList);

        // Then
        assertNotNull(result);
        assertNotNull(result.getErrorList());
        assertEquals(2, result.getErrorList().size()); // Should skip the null entry
    }

    // Tests for private method convertToErrorEntity using reflection

    @Test
    public void should_ConvertToErrorEntity_When_ValidErrorResponse() throws Exception {
        // Given
        Method method = OrchHostCallingResponseTransformer.class.getDeclaredMethod("convertToErrorEntity", ErrorResponse.class);
        method.setAccessible(true);
        
        ErrorResponse error = new ErrorResponse();
        error.setCode("ERR123");
        error.setMessage("Test error message");

        // When
        ErrorEntity result = (ErrorEntity) method.invoke(orchHostCallingResponseTransformer, error);

        // Then
        assertNotNull(result);
        assertEquals("ERR123", result.getErrorCode());
        assertEquals("Test error message", result.getMsg());
    }

    @Test
    public void should_ReturnNull_When_ConvertToErrorEntityWithNullError() throws Exception {
        // Given
        Method method = OrchHostCallingResponseTransformer.class.getDeclaredMethod("convertToErrorEntity", ErrorResponse.class);
        method.setAccessible(true);

        // When
        ErrorEntity result = (ErrorEntity) method.invoke(orchHostCallingResponseTransformer, (ErrorResponse) null);

        // Then
        assertNull(result);
    }

    // Tests for private method createErrorResponse using reflection

    @Test
    public void should_CreateErrorResponse_When_CalledWithValidParameters() throws Exception {
        // Given
        Method method = OrchHostCallingResponseTransformer.class.getDeclaredMethod("createErrorResponse", String.class, String.class);
        method.setAccessible(true);

        // When
        HostCallingInitiateResponse result = (HostCallingInitiateResponse) method.invoke(
                orchHostCallingResponseTransformer, "REQ456", "Custom error message");

        // Then
        assertNotNull(result);
        assertEquals("error", result.getStatus());
        assertEquals("REQ456", result.getRequestId());
        assertFalse(result.getAvailableNow());
    }

    @Test
    public void should_CreateErrorResponse_When_CalledWithNullRequestId() throws Exception {
        // Given
        Method method = OrchHostCallingResponseTransformer.class.getDeclaredMethod("createErrorResponse", String.class, String.class);
        method.setAccessible(true);

        // When
        HostCallingInitiateResponse result = (HostCallingInitiateResponse) method.invoke(
                orchHostCallingResponseTransformer, null, "Error message");

        // Then
        assertNotNull(result);
        assertEquals("error", result.getStatus());
    }

    @Test
    public void should_CreateErrorResponse_When_CalledWithNullErrorMessage() throws Exception {
        // Given
        Method method = OrchHostCallingResponseTransformer.class.getDeclaredMethod("createErrorResponse", String.class, String.class);
        method.setAccessible(true);

        // When
        HostCallingInitiateResponse result = (HostCallingInitiateResponse) method.invoke(
                orchHostCallingResponseTransformer, "REQ789", null);

        // Then
        assertNotNull(result);
        assertEquals("error", result.getStatus());
        assertEquals("REQ789", result.getRequestId());
        assertFalse(result.getAvailableNow());
    }

    // Edge case tests

    @Test
    public void should_HandleNullFields_When_HesResponseHasNullValues() {
        // Given
        HostCallingResponse responseWithNulls = new HostCallingResponse();
        responseWithNulls.setStatus(null);
        responseWithNulls.setRequestId(null);
        responseWithNulls.setChainName(null);
        responseWithNulls.setAvailableNow(false);
        responseWithNulls.setStartTime(null);
        responseWithNulls.setEndTime(null);
        responseWithNulls.setMaskedNumber(null);

        // When
        HostCallingInitiateResponse result = orchHostCallingResponseTransformer
                .convertHostCallingResponse(responseWithNulls, clientType, requestId);

        // Then
        assertNotNull(result);
    }

    @Test
    public void should_HandleSpecialCharacters_When_ChainNameHasSpecialChars() {
        // Given
        validHesResponse.setChainName("Hotel & Spa's \"Best\" Resort");

        // When
        HostCallingInitiateResponse result = orchHostCallingResponseTransformer
                .convertHostCallingResponse(validHesResponse, clientType, requestId);

        // Then
        assertNotNull(result);
        assertEquals("Hotel & Spa's \"Best\" Resort", result.getChainName());
        assertTrue(result.getMissedCallMessage().contains("Hotel & Spa's \"Best\" Resort"));
    }

    @Test
    public void should_HandleLongChainName_When_ChainNameIsVeryLong() {
        // Given
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            sb.append("A");
        }
        String longChainName = sb.toString();
        validHesResponse.setChainName(longChainName);

        // When
        HostCallingInitiateResponse result = orchHostCallingResponseTransformer
                .convertHostCallingResponse(validHesResponse, clientType, requestId);

        // Then
        assertNotNull(result);
        assertEquals(longChainName, result.getChainName());
        assertTrue(result.getMissedCallMessage().contains(longChainName));
    }

    @Test
    public void should_HandleDifferentClientTypes_When_ClientTypeVaries() {
        // Test various client types
        String[] clientTypes = {"ANDROID", "IOS", "PWA", "DESKTOP", null, ""};
        
        for (String client : clientTypes) {
            // When
            HostCallingInitiateResponse result = orchHostCallingResponseTransformer
                    .convertHostCallingResponse(validHesResponse, client, requestId);

            // Then
            assertNotNull(result);
            assertEquals("success", result.getStatus());
            assertEquals(requestId, result.getRequestId());
        }
    }
} 