package com.mmt.hotels.clientgateway.transformer.response;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.FlexiCancelStaticDetail;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicy;
import com.mmt.hotels.clientgateway.response.Coupon;
import com.mmt.hotels.clientgateway.response.PixelUrlConfig;
import com.mmt.hotels.clientgateway.response.moblanding.CardData;
import com.mmt.hotels.clientgateway.response.thankyou.BookedRatePlan;
import com.mmt.hotels.clientgateway.response.thankyou.ThankYouResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.pwa.ThankYouResponseTransformerPWA;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.*;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.enums.LuckyUserContext;
import com.mmt.hotels.model.response.MpFareHoldStatus;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysTooltip;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.txn.*;
import com.mmt.hotels.util.Tuple;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.io.FileUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.runners.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.*;

import static com.mmt.hotels.clientgateway.constants.ClientExpDataKeys.CLIENT_EXP_Thankyou_PAGE_REDESIGN;
import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ThankYouResponseTransformerPWATest {

    @Spy
    private DateUtil dateUtil;

    @Mock
    private CommonResponseTransformer commonResponseTransformer;

    @InjectMocks
    private ThankYouResponseTransformerPWA thankYouResponseTransformer;

    @Mock
    private PropertyManager propertyManager;

    @Mock
    private Utility utility;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    private CommonConfigConsul commonConfigConsul;

    @InjectMocks
    private ThankYouResponseTransformerPWA thankYouResponseTransformerPWA;


    String tyCard = "{\"NEXTBOOKINGDISCOUNT\":{\"cardInfo\":{\"bgLinearGradient\":{\"direction\":\"horizontal\",\"end\":\"#e9d1f1\",\"start\":\"#e9d1f1\"},\"cardPayload\":{\"metaPersuasion\":[\"Unlock Secret logged in deals\",\"Exclusive discount for MMTBlack customers\"],\"scratchText\":\"ALWAYSMMT\",\"title\":\"Exciting benefits on our app\"},\"subText\":\"Preapplied\",\"templateId\":\"htl_generic_info\",\"titleText\":\"5% extra discount on your next booking from our app\"},\"sequence\":1},\"NEXTBOOKINGSCRATCH\":{\"cardInfo\":{\"cardPayload\":{\"rewardCode\":\"ALWAYSMMT\",\"rewardStatus\":\"Code will be Preapplied\",\"scratchColor\":\"0x22222\",\"scratchText\":\"5% extra discount on your next booking from our app\"},\"subText\":\"Offer on your next hotel booking\",\"templateId\":\"htl_scratch_unlock\",\"titleText\":\"Scratch and Unlock\"},\"sequence\":2},\"POST_BOOKING_CARD\":{\"cardInfo\":{\"cardId\":\"POSTBOOKING\",\"templateId\":\"booking_info_card\",\"titleText\":\"After booking you will receive \",\"subTextList\":[\"24x7 direct connect with us\",\"Dedicated support from hotel for any queries\"],\"iconURL\":\"https://promos.makemytrip.com/Hotels_product/group/review_pb_2x.png\"},\"sequence\":1}}\n";

    @Before
    public void setup() {
        ReflectionTestUtils.setField(thankYouResponseTransformer, "maxInclusionsThankyou", 3);
        Set<String> flightsBookerHydraSegment = new HashSet<>();
        flightsBookerHydraSegment.add("r2021");flightsBookerHydraSegment.add("r2020");
        ReflectionTestUtils.setField(thankYouResponseTransformer, "flightsBookerHydraSegment", flightsBookerHydraSegment);
        ReflectionTestUtils.setField(thankYouResponseTransformer, "myTripsDeeplink", "mmyt://mytrips/hotelDetails");
        ReflectionTestUtils.setField(thankYouResponseTransformer, "hotelDetailsRawDeepLink", "mmyt://htl/listing/?hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&roomStayQualifier={5}&openDetail=true&currency={6}&checkAvailability=true");
        ReflectionTestUtils.setField(thankYouResponseTransformer, "myTripCardDetails", "{\"CONTACT_PROPERTY\":{\"text\":\"Contact Property\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"},\"CANCEL_BOOKING\":{\"text\":\"Cancel Booking\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"},\"ADD_MEAL\":{\"text\":\"Add Meal\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"},\"DIRECTION_MAP\":{\"text\":\"Directions on Map\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"},\"SPECIAL_REQUESTS\":{\"text\":\"Special Requests\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"},\"ADD_GUEST\":{\"text\":\"Add Guest\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"},\"DOWNLOAD_TICKET\":{\"text\":\"Download Ticket\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"},\"MORE_MYTRIPS\":{\"text\":\"More in My Trips\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"},\"CHAT_MYRA\":{\"text\":\"Chat with Myra\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"},\"HOTEL_POLICY\":{\"text\":\"View Hotel Policy\",\"iconUrl\":\"google.com\",\"deepLink\":\"mmyt://mytrips/hotelDetails\"}}");
        ReflectionTestUtils.setField(thankYouResponseTransformer, "myTripCardConditions", "{\"FC_AP_0\":[\"CONTACT_PROPERTY\",\"CANCEL_BOOKING\",\"ADD_MEAL\",\"DIRECTION_MAP\",\"SPECIAL_REQUESTS\",\"ADD_GUEST\",\"DOWNLOAD_TICKET\",\"MORE_MYTRIPS\"],\"FC_AP_X\":[\"CONTACT_PROPERTY\",\"CANCEL_BOOKING\",\"ADD_MEAL\",\"SPECIAL_REQUESTS\",\"ADD_GUEST\",\"DOWNLOAD_TICKET\",\"MORE_MYTRIPS\"],\"NR_AP_0\":[\"CONTACT_PROPERTY\",\"DIRECTION_MAP\",\"CHAT_MYRA\",\"SPECIAL_REQUESTS\",\"DOWNLOAD_TICKET\",\"MORE_MYTRIPS\"],\"NR_AP_X\":[\"CONTACT_PROPERTY\",\"HOTEL_POLICY\",\"SPECIAL_REQUESTS\",\"CHAT_MYRA\",\"DOWNLOAD_TICKET\",\"MORE_MYTRIPS\"],\"ZP_FC_AP_0\":[\"CONTACT_PROPERTY\",\"CANCEL_BOOKING\",\"ADD_MEAL\",\"DIRECTION_MAP\",\"SPECIAL_REQUESTS\",\"ADD_GUEST\",\"DOWNLOAD_TICKET\",\"MORE_MYTRIPS\"],\"ZP_FC_AP_X\":[\"CONTACT_PROPERTY\",\"CANCEL_BOOKING\",\"ADD_MEAL\",\"SPECIAL_REQUESTS\",\"ADD_GUEST\",\"DOWNLOAD_TICKET\",\"MORE_MYTRIPS\"]}");
        ReflectionTestUtils.setField(thankYouResponseTransformer, "myTripCardIconUrls", "{\"CONTACT_PROPERTY\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/contactproperty.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/contactproperty.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/contactproperty.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/contactproperty.png\"},\"CANCEL_BOOKING\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/cancelbooking.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/cancelbooking.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/cancelbooking.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/cancelbooking.png\"},\"ADD_MEAL\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/addmeal.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/addmeal.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/addmeal.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/addmeal.png\"},\"DIRECTION_MAP\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/directionsonmap.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/directionsonmap.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/directionsonmap.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/directionsonmap.png\"},\"SPECIAL_REQUESTS\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/specialrequest.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/specialrequest.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/specialrequest.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/specialrequest.png\"},\"ADD_GUEST\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/addguest.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/addguest.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/addguest.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/addguest.png\"},\"DOWNLOAD_TICKET\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/downloadvoucher.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/downloadvoucher.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/downloadvoucher.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/downloadvoucher.png\"},\"MORE_MYTRIPS\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/moreactions.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/moreactions.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/moreactions.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/moreactions.png\"},\"CHAT_MYRA\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/myrachat.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/myrachat.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/myrachat.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/myrachat.png\"},\"HOTEL_POLICY\":{\"iconUrlAndroid\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/Android/hotelpolicy.png\",\"iconUrlAndroidCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/Android/hotelpolicy.png\",\"iconUrlIos\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction/iOS/hotelpolicy.png\",\"iconUrlIosCorp\":\"https://promos.makemytrip.com/Hotels_product/Hotel_TY/Hotel_TY_MyTripAction_Corporate/iOS/hotelpolicy.png\"}}");
        ReflectionTestUtils.setField(thankYouResponseTransformer, "mealPlanMapPolyglot", new HashMap<>());
        ReflectionTestUtils.setField(thankYouResponseTransformer, "forexPromoIconUrl", "https://promos.makemytrip.com/images/CDN_upload/forex_tick.png");
        FlexiCancelStaticDetail flexiCancelStaticDetail = new FlexiCancelStaticDetail();
        flexiCancelStaticDetail.setBackgroundImage("test");
        flexiCancelStaticDetail.setIconUrl("test");
        flexiCancelStaticDetail.setRedirectUrl("test");
        flexiCancelStaticDetail.setFcInclusionIconUrl("test");
        ReflectionTestUtils.setField(thankYouResponseTransformer, "flexiCancelStaticDetail", flexiCancelStaticDetail);
        List<String> groupBookingCardKeys = new ArrayList<>();
        groupBookingCardKeys.add("POST_BOOKING_CARD");
        ReflectionTestUtils.setField(thankYouResponseTransformer, "groupBookingCardKeys", groupBookingCardKeys);
        Type type = new TypeToken<Map<String, CardData>>() {
        }.getType();
        String tyCard = "{\"NEXTBOOKINGDISCOUNT\":{\"cardInfo\":{\"bgLinearGradient\":{\"direction\":\"horizontal\",\"end\":\"#e9d1f1\",\"start\":\"#e9d1f1\"},\"cardPayload\":{\"metaPersuasion\":[\"Unlock Secret logged in deals\",\"Exclusive discount for MMTBlack customers\"],\"scratchText\":\"ALWAYSMMT\",\"title\":\"Exciting benefits on our app\"},\"subText\":\"Preapplied\",\"templateId\":\"htl_generic_info\",\"titleText\":\"5% extra discount on your next booking from our app\"},\"sequence\":1},\"NEXTBOOKINGSCRATCH\":{\"cardInfo\":{\"cardPayload\":{\"rewardCode\":\"ALWAYSMMT\",\"rewardStatus\":\"Code will be Preapplied\",\"scratchColor\":\"0x22222\",\"scratchText\":\"5% extra discount on your next booking from our app\"},\"subText\":\"Offer on your next hotel booking\",\"templateId\":\"htl_scratch_unlock\",\"titleText\":\"Scratch and Unlock\"},\"sequence\":2},\"POST_BOOKING_CARD\":{\"cardInfo\":{\"cardId\":\"POSTBOOKING\",\"templateId\":\"booking_info_card\",\"titleText\":\"After booking you will receive \",\"subTextList\":[\"24x7 direct connect with us\",\"Dedicated support from hotel for any queries\"],\"iconURL\":\"https://promos.makemytrip.com/Hotels_product/group/review_pb_2x.png\"},\"sequence\":1}}\n";

        ReflectionTestUtils.setField(thankYouResponseTransformer, "thankYouCards", new Gson().fromJson(tyCard, type));
        MockitoAnnotations.initMocks(this);
        CommonConfig commonConfig = Mockito.mock(CommonConfig.class);
        Mockito.when(propertyManager.getProperty("commonConfig", CommonConfig.class)).thenReturn(commonConfig);
        this.thankYouResponseTransformer.init();
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenAnswer(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                return invocationOnMock.getArgument(0);
            }
        });
        ReflectionTestUtils.setField(thankYouResponseTransformerPWA, "hotelDetailsRawDeepLink", "https://www.makemytrip.com/hotels/hotel-listing/?topHtlId={0}&checkin={1}&checkout={2}&country={3}&city={4}&roomStayQualifier={5}&currency={6}&checkAvailability=true");
        ReflectionTestUtils.setField(thankYouResponseTransformerPWA, "hotelDetailsRawDeepLinkMyPartner", "https://mypartner.makemytrip.com/hotels/hotel-listing/?topHtlId={0}&checkin={1}&checkout={2}&country={3}&city={4}&roomStayQualifier={5}&currency={6}&checkAvailability=true");
        ReflectionTestUtils.setField(thankYouResponseTransformerPWA, "hotelDetailsRawDeepLinkGlobal", "https://global.makemytrip.com/hotels/hotel-listing/?topHtlId={0}&checkin={1}&checkout={2}&country={3}&city={4}&roomStayQualifier={5}&currency={6}&checkAvailability=true");
    }

    @Test
    public void test_convertThankYouResponse_Success() throws IOException, ErrorResponseFromDownstreamException {
        PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:thankyou/successTxnData.json")),
                PersistanceMultiRoomResponseEntity.class);
        persistanceMultiRoomResponseEntity.getPersistedData().setIsExtraAdultChild(false);
        Type type = new TypeToken<Map<String, CardData>>() {
        }.getType();
        String tyCard = "{\"NEXTBOOKINGDISCOUNT\":{\"cardInfo\":{\"bgLinearGradient\":{\"direction\":\"horizontal\",\"end\":\"#e9d1f1\",\"start\":\"#e9d1f1\"},\"cardPayload\":{\"metaPersuasion\":[\"Unlock Secret logged in deals\",\"Exclusive discount for MMTBlack customers\"],\"scratchText\":\"ALWAYSMMT\",\"title\":\"Exciting benefits on our app\"},\"subText\":\"Preapplied\",\"templateId\":\"htl_generic_info\",\"titleText\":\"5% extra discount on your next booking from our app\"},\"sequence\":1},\"NEXTBOOKINGSCRATCH\":{\"cardInfo\":{\"cardPayload\":{\"rewardCode\":\"ALWAYSMMT\",\"rewardStatus\":\"Code will be Preapplied\",\"scratchColor\":\"0x22222\",\"scratchText\":\"5% extra discount on your next booking from our app\"},\"subText\":\"Offer on your next hotel booking\",\"templateId\":\"htl_scratch_unlock\",\"titleText\":\"Scratch and Unlock\"},\"sequence\":2},\"POST_BOOKING_CARD\":{\"cardInfo\":{\"cardId\":\"POSTBOOKING\",\"templateId\":\"booking_info_card\",\"titleText\":\"After booking you will receive \",\"subTextList\":[\"24x7 direct connect with us\",\"Dedicated support from hotel for any queries\"],\"iconURL\":\"https://promos.makemytrip.com/Hotels_product/group/review_pb_2x.png\"},\"sequence\":1}}\n";

        ReflectionTestUtils.setField(thankYouResponseTransformer, "thankYouCards", new Gson().fromJson(tyCard, type));

        Mockito.when(utility.getGuestRoomKeyValue(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyInt(), Mockito.any(), Mockito.anyBoolean())).thenReturn(new Tuple<>("sample key", "sample value"));

        ThankYouResponse thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, null);
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getTotalAmount());
        Assert.assertNotNull(thankYouResponse.getLogData());
        Assert.assertEquals(thankYouResponse.getLogData().get("wallet") ,"0.0");
        Assert.assertNotNull(thankYouResponse.getMetaChannelInfo());
        Assert.assertEquals((double) ((Map) thankYouResponse.getMetaChannelInfo()).get("tag_one"), 5.0, 0.0);
        Assert.assertNotNull(thankYouResponse.getBookingDetails());
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getInsuranceInfo());
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getPendingBookingCardInfo());
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getPendingBookingCardInfo().getTitle());
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getPendingBookingCardInfo().getSubtitle());

        persistanceMultiRoomResponseEntity.getPersistedData().getTotalDisplayFare().setIsBNPLApplicable(true);
        persistanceMultiRoomResponseEntity.getPersistedData().getTotalDisplayFare().setBnplVariant(BNPLVariant.BNPL_AT_0);
        com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = new CancellationTimeline();
        cancellationTimeline.setTillDate("18-Jul-2023 12:59");
        persistanceMultiRoomResponseEntity.getPersistedData().setCancellationTimeline(cancellationTimeline);
        persistanceMultiRoomResponseEntity.getPersistedData().setFlexiCancelAddOnInfo(new FlexiCancelAddOnInfo());
        persistanceMultiRoomResponseEntity.getPersistedData().getFlexiCancelAddOnInfo().setFlexiCancelBasicDetails(new FlexiCancelBasicDetails());
        persistanceMultiRoomResponseEntity.getPersistedData().getFlexiCancelAddOnInfo().getFlexiCancelBasicDetails().setFlexiCancelApplied(true);
        thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, null);
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getBookingDetails());
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getPendingBookingCardInfo());
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getPendingBookingCardInfo().getSubtitle());
    }

    @Test
    public void test_convertThankYouResponse_Success_BookingUIData() throws IOException, ErrorResponseFromDownstreamException {
        PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:thankyou/successTxnData.json")),
                PersistanceMultiRoomResponseEntity.class);
        Mockito.when(utility.getGuestRoomKeyValue(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyInt(), Mockito.any(), Mockito.anyBoolean())).thenReturn(new Tuple<>("sample key", "sample value"));

        // TPR pokus is false
        persistanceMultiRoomResponseEntity.getPersistedData().getExpData().put(CLIENT_EXP_Thankyou_PAGE_REDESIGN, "F");
        ThankYouResponse thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, null);
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getBookingDetails());
        Assert.assertNull(thankYouResponse.getBookingUIData());

        // TPR pokus is true
        persistanceMultiRoomResponseEntity.getPersistedData().getExpData().put(CLIENT_EXP_Thankyou_PAGE_REDESIGN, "T");
        thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, null);
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getBookingDetails());

        Assert.assertNull(thankYouResponse.getBookingDetails().getInsuranceInfo());
        Assert.assertNull(thankYouResponse.getBookingDetails().getCheckInPolicyDesc());
        Assert.assertNull(thankYouResponse.getBlackInfo());
        Assert.assertNull(thankYouResponse.getHotelBenefits());
        Assert.assertNull(thankYouResponse.getLongStayBenefits());

        Assert.assertNotNull(thankYouResponse.getBookingUIData());
        Assert.assertNotNull(thankYouResponse.getBookingUIData().getStatusInfo());
        Assert.assertNotNull(thankYouResponse.getBookingUIData().getInclusions());

    }

    @Test
    public void test_convertThankYouResponseMyPartner_Success() throws IOException, ErrorResponseFromDownstreamException {
        PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:thankyou/successTxnDataMyPartner.json")),
                PersistanceMultiRoomResponseEntity.class);
        persistanceMultiRoomResponseEntity.getPersistedData().setIsExtraAdultChild(false);
        Type type = new TypeToken<Map<String, CardData>>() {
        }.getType();
        String tyCard = "{\"NEXTBOOKINGDISCOUNT\":{\"cardInfo\":{\"bgLinearGradient\":{\"direction\":\"horizontal\",\"end\":\"#e9d1f1\",\"start\":\"#e9d1f1\"},\"cardPayload\":{\"metaPersuasion\":[\"Unlock Secret logged in deals\",\"Exclusive discount for MMTBlack customers\"],\"scratchText\":\"ALWAYSMMT\",\"title\":\"Exciting benefits on our app\"},\"subText\":\"Preapplied\",\"templateId\":\"htl_generic_info\",\"titleText\":\"5% extra discount on your next booking from our app\"},\"sequence\":1},\"NEXTBOOKINGSCRATCH\":{\"cardInfo\":{\"cardPayload\":{\"rewardCode\":\"ALWAYSMMT\",\"rewardStatus\":\"Code will be Preapplied\",\"scratchColor\":\"0x22222\",\"scratchText\":\"5% extra discount on your next booking from our app\"},\"subText\":\"Offer on your next hotel booking\",\"templateId\":\"htl_scratch_unlock\",\"titleText\":\"Scratch and Unlock\"},\"sequence\":2},\"POST_BOOKING_CARD\":{\"cardInfo\":{\"cardId\":\"POSTBOOKING\",\"templateId\":\"booking_info_card\",\"titleText\":\"After booking you will receive \",\"subTextList\":[\"24x7 direct connect with us\",\"Dedicated support from hotel for any queries\"],\"iconURL\":\"https://promos.makemytrip.com/Hotels_product/group/review_pb_2x.png\"},\"sequence\":1}}\n";

        ReflectionTestUtils.setField(thankYouResponseTransformer, "thankYouCards", new Gson().fromJson(tyCard, type));

        Mockito.when(utility.getGuestRoomKeyValue(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyInt(), Mockito.any(), Mockito.anyBoolean())).thenReturn(new Tuple<>("sample key", "sample value"));
        ThankYouResponse thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, null);
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getTotalAmount());
        Assert.assertNotNull(thankYouResponse.getLogData());
        Assert.assertEquals(thankYouResponse.getLogData().get("wallet") ,"0.0");
        Assert.assertNotNull(thankYouResponse.getMetaChannelInfo());
        Assert.assertEquals((double) ((Map) thankYouResponse.getMetaChannelInfo()).get("tag_one"), 5.0, 0.0);
        Assert.assertNotNull(thankYouResponse.getBookingDetails());
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getInsuranceInfo());
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getPendingBookingCardInfo());
        Assert.assertNull(thankYouResponse.getBookingDetails().getPendingBookingCardInfo().getTitle());
        Assert.assertNull(thankYouResponse.getBookingDetails().getPendingBookingCardInfo().getSubtitle());


        persistanceMultiRoomResponseEntity.getPersistedData().setRequestToBook(false);
        persistanceMultiRoomResponseEntity.getPersistedData().setBookNowFareHold(true);
        persistanceMultiRoomResponseEntity.getPersistedData().setMpaFareHoldStatus(new MpFareHoldStatus());
        persistanceMultiRoomResponseEntity.getPersistedData().getMpaFareHoldStatus().setHoldEligible(true);
        persistanceMultiRoomResponseEntity.getPersistedData().getMpaFareHoldStatus().setEligibleForHoldBooking(true);
        thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, null);
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getTotalAmount());
        Assert.assertNotNull(thankYouResponse.getLogData());
        Assert.assertEquals(thankYouResponse.getLogData().get("wallet") ,"0.0");
        Assert.assertNotNull(thankYouResponse.getMetaChannelInfo());
        Assert.assertEquals((double) ((Map) thankYouResponse.getMetaChannelInfo()).get("tag_one"), 5.0, 0.0);
        Assert.assertNotNull(thankYouResponse.getBookingDetails());
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getInsuranceInfo());
    }

    @Test
    public void test_convertThankYouResponseMyPartnerWithGSTINDetails_Success() throws IOException, ErrorResponseFromDownstreamException {
        PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:thankyou/successTxnDataMyPartner.json")),
                PersistanceMultiRoomResponseEntity.class);
        persistanceMultiRoomResponseEntity.getPersistedData().setIsExtraAdultChild(false);
        Type type = new TypeToken<Map<String, CardData>>() {
        }.getType();
        String tyCard = "{\"NEXTBOOKINGDISCOUNT\":{\"cardInfo\":{\"bgLinearGradient\":{\"direction\":\"horizontal\",\"end\":\"#e9d1f1\",\"start\":\"#e9d1f1\"},\"cardPayload\":{\"metaPersuasion\":[\"Unlock Secret logged in deals\",\"Exclusive discount for MMTBlack customers\"],\"scratchText\":\"ALWAYSMMT\",\"title\":\"Exciting benefits on our app\"},\"subText\":\"Preapplied\",\"templateId\":\"htl_generic_info\",\"titleText\":\"5% extra discount on your next booking from our app\"},\"sequence\":1},\"NEXTBOOKINGSCRATCH\":{\"cardInfo\":{\"cardPayload\":{\"rewardCode\":\"ALWAYSMMT\",\"rewardStatus\":\"Code will be Preapplied\",\"scratchColor\":\"0x22222\",\"scratchText\":\"5% extra discount on your next booking from our app\"},\"subText\":\"Offer on your next hotel booking\",\"templateId\":\"htl_scratch_unlock\",\"titleText\":\"Scratch and Unlock\"},\"sequence\":2},\"POST_BOOKING_CARD\":{\"cardInfo\":{\"cardId\":\"POSTBOOKING\",\"templateId\":\"booking_info_card\",\"titleText\":\"After booking you will receive \",\"subTextList\":[\"24x7 direct connect with us\",\"Dedicated support from hotel for any queries\"],\"iconURL\":\"https://promos.makemytrip.com/Hotels_product/group/review_pb_2x.png\"},\"sequence\":1}}\n";

        ReflectionTestUtils.setField(thankYouResponseTransformer, "thankYouCards", new Gson().fromJson(tyCard, type));

        Mockito.when(utility.getGuestRoomKeyValue(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyInt(), Mockito.any(), Mockito.anyBoolean())).thenReturn(new Tuple<>("sample key", "sample value"));

        Mockito.when(commonConfigConsul.getMmtMyPartnerTooltip()).thenReturn(new ValueStaysTooltip());

        // Testing multiple cases
        // Case: Request is from Mypartner Sublob
        persistanceMultiRoomResponseEntity.getPersistedData().getAvailReqBody().setSubProfileType("MYPARTNER");
        ThankYouResponse thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, null);
        Assert.assertNull(thankYouResponse.getHotelDetails().getCategoryTag());

        // Case: Request is from Mypartner Sublob and Agent is GST Assured
        persistanceMultiRoomResponseEntity.getPersistedData().setAgentGSTAssured(true);
        thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, null);
        Assert.assertNull(thankYouResponse.getHotelDetails().getCategoryTag());

        // Case: Request is form Mypartner Sublob and both Agent and Property are GST Assured
        persistanceMultiRoomResponseEntity.getPersistedData().setPropertyGSTAssured(true);
        thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, null);
        Assert.assertNull(thankYouResponse.getHotelDetails().getCategoryTag());

        // Case: Request is form Mypartner Sublob and both Agent and Property are GST Assured + traveller list has gstn details
        persistanceMultiRoomResponseEntity.getPersistedData().setTravelerInfoList(getTravelerInfoListWithGSTINDetails());
        thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, null);
        Assert.assertNotNull(thankYouResponse.getHotelDetails().getCategoryTag());
    }

    private List<TravelerInfo> getTravelerInfoListWithGSTINDetails() {
        List<TravelerInfo> lst = new ArrayList<TravelerInfo>();
        TravelerInfo trvlrinfo = new TravelerInfo();
        trvlrinfo.setFirstName("New");
        trvlrinfo.setLastName("Person");
        trvlrinfo.setTitle("Mr");
        trvlrinfo.setRegisteredGstinNum("1234XXXX1234XXX");
        trvlrinfo.setGstinCompanyName("New Company Pvt Ltd.");
        trvlrinfo.setGstinCompanyAddress("New Building, New Road, New City, Old Pincode");
        lst.add(trvlrinfo);
        return lst;
    }

    @Test
    public void test_convertThankYouResponse_Pending() throws IOException, ErrorResponseFromDownstreamException {
        PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:thankyou/pendingTxnData.json")),
                PersistanceMultiRoomResponseEntity.class);
        persistanceMultiRoomResponseEntity.getPersistedData().setIsExtraAdultChild(false);
        HashMap<String,String> expData= new HashMap<>();
        expData.put(MYPARTNER_EXCLUSIVE_DEAL,"true");
        persistanceMultiRoomResponseEntity.getPersistedData().setExpData(expData);
        Mockito.when(utility.getGuestRoomKeyValue(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyInt(), Mockito.any(), Mockito.anyBoolean())).thenReturn(new Tuple<>("sample key", "sample value"));

        ThankYouResponse thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, null);
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getPendingBookingCardInfo());
    }

    @Test
    public void test_convertThankYouResponse_Failed() throws IOException, ErrorResponseFromDownstreamException {
        PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:thankyou/failedTxnData.json")),
                PersistanceMultiRoomResponseEntity.class);
        persistanceMultiRoomResponseEntity.getPersistedData().setSpiderNextBookingDiscountMessage("test");
        persistanceMultiRoomResponseEntity.getPersistedData().setIsExtraAdultChild(false);
        HashMap<String,String> expData= new HashMap<>();
        expData.put(MYPARTNER_EXCLUSIVE_DEAL,"true");
        persistanceMultiRoomResponseEntity.getPersistedData().setExpData(expData);
        Map<String,CardData> thankYouCards = new HashMap<>();
        thankYouCards.put("POST_BOOKING_CARD", new CardData());
        ReflectionTestUtils.setField(thankYouResponseTransformer, "thankYouCards", thankYouCards);
        Mockito.when(utility.getGuestRoomKeyValue(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyInt(), Mockito.any(), Mockito.anyBoolean())).thenReturn(new Tuple<>("sample key", "sample value"));
        ThankYouResponse thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, null);
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getFailedBookingCardInfo());
    }

    @Test
    public void test_convertThankYouResponse_Failed_BookingUIData() throws IOException, ErrorResponseFromDownstreamException {
        PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:thankyou/failedTxnData.json")),
                PersistanceMultiRoomResponseEntity.class);

        Mockito.when(utility.getGuestRoomKeyValue(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyInt(), Mockito.any(), Mockito.anyBoolean())).thenReturn(new Tuple<>("sample key", "sample value"));

        HashMap<String,String> expData= new HashMap<>();
        expData.put(CLIENT_EXP_Thankyou_PAGE_REDESIGN,"T"); // TPR pokus is true
        persistanceMultiRoomResponseEntity.getPersistedData().setExpData(expData);

        ThankYouResponse thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, null);
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getBookingDetails());
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getFailedBookingCardInfo());

        Assert.assertNotNull(thankYouResponse.getBookingUIData());
        Assert.assertNotNull(thankYouResponse.getBookingUIData().getStatusInfo());
        Assert.assertNull(thankYouResponse.getBookingUIData().getPaymentInfo());
        Assert.assertNull(thankYouResponse.getBookingUIData().getCharityInfo());
        Assert.assertNull(thankYouResponse.getBookingUIData().getInclusions());
        Assert.assertNull(thankYouResponse.getBookingUIData().getBookingInfo());
        Assert.assertNull(thankYouResponse.getBookingUIData().getPrimaryCTA());

    }

    @Test
    public void test_convertThankYouResponse_CompletePayment() throws IOException, ErrorResponseFromDownstreamException {
        PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:thankyou/completePaymentTxnData.json")),
                PersistanceMultiRoomResponseEntity.class);
        persistanceMultiRoomResponseEntity.getPersistedData().setIsExtraAdultChild(false);

        Type type = new TypeToken<Map<String, CardData>>() {
        }.getType();
        String tyCard = "{\"NEXTBOOKINGDISCOUNT\":{\"cardInfo\":{\"bgLinearGradient\":{\"direction\":\"horizontal\",\"end\":\"#e9d1f1\",\"start\":\"#e9d1f1\"},\"cardPayload\":{\"metaPersuasion\":[\"Unlock Secret logged in deals\",\"Exclusive discount for MMTBlack customers\"],\"scratchText\":\"ALWAYSMMT\",\"title\":\"Exciting benefits on our app\"},\"subText\":\"Preapplied\",\"templateId\":\"htl_generic_info\",\"titleText\":\"5% extra discount on your next booking from our app\"},\"sequence\":1},\"NEXTBOOKINGSCRATCH\":{\"cardInfo\":{\"cardPayload\":{\"rewardCode\":\"ALWAYSMMT\",\"rewardStatus\":\"Code will be Preapplied\",\"scratchColor\":\"0x22222\",\"scratchText\":\"5% extra discount on your next booking from our app\"},\"subText\":\"Offer on your next hotel booking\",\"templateId\":\"htl_scratch_unlock\",\"titleText\":\"Scratch and Unlock\"},\"sequence\":2},\"POST_BOOKING_CARD\":{\"cardInfo\":{\"cardId\":\"POSTBOOKING\",\"templateId\":\"booking_info_card\",\"titleText\":\"After booking you will receive \",\"subTextList\":[\"24x7 direct connect with us\",\"Dedicated support from hotel for any queries\"],\"iconURL\":\"https://promos.makemytrip.com/Hotels_product/group/review_pb_2x.png\"},\"sequence\":1}}\n";

        ReflectionTestUtils.setField(thankYouResponseTransformer, "thankYouCards", new Gson().fromJson(tyCard, type));

        Mockito.when(utility.getGuestRoomKeyValue(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyInt(), Mockito.any(), Mockito.anyBoolean())).thenReturn(new Tuple<>("sample key", "sample value"));
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("test");
        ThankYouResponse thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, null);
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getBookingDetails().getCompletePaymentCard());

    }

    @Test
    public void thankYouResponseDefiniteSellingLogging() throws IOException, ErrorResponseFromDownstreamException {
        PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:thankyou/completePaymentTxnData.json")),
                PersistanceMultiRoomResponseEntity.class);
        persistanceMultiRoomResponseEntity.getPersistedData().setIsExtraAdultChild(false);

        Type type = new TypeToken<Map<String, CardData>>() {
        }.getType();

        ReflectionTestUtils.setField(thankYouResponseTransformer, "thankYouCards", new Gson().fromJson(tyCard, type));
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("test");
        utility = Mockito.spy(new Utility());
        ReflectionTestUtils.setField(thankYouResponseTransformer, "utility", utility);
        Mockito.doReturn(new Tuple<>("sample key", "sample value")).when(utility).getGuestRoomKeyValue(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyInt(), Mockito.any(), Mockito.anyBoolean());

        ThankYouResponse thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, null);
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getLuckyUserContext());
        Assert.assertEquals(thankYouResponse.getLuckyUserContext(), X_PERCENT_SELL_OFF_TEXT);


        persistanceMultiRoomResponseEntity.getPersistedData().getExpData().put(X_PERCENT_SELL_ON, "true");
        thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, null);
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getLuckyUserContext());
        Assert.assertEquals(thankYouResponse.getLuckyUserContext(), X_PERCENT_SELL_ON_TEXT);

        persistanceMultiRoomResponseEntity.getPersistedData().setLuckyUserContext(LuckyUserContext.LUCKY);
        persistanceMultiRoomResponseEntity.getPersistedData().getExpData().put(X_PERCENT_SELL_ON, "true");
        thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, null);
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getLuckyUserContext());
        Assert.assertEquals(thankYouResponse.getLuckyUserContext(), X_PERCENT_SELL_ON_TEXT + "|" + PRIVILEGED_USER);

        persistanceMultiRoomResponseEntity.getPersistedData().setLuckyUserContext(LuckyUserContext.LUCKY_UNLUCKY);
        persistanceMultiRoomResponseEntity.getPersistedData().getExpData().put(X_PERCENT_SELL_ON, "true");
        thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, null);
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getLuckyUserContext());
        Assert.assertEquals(thankYouResponse.getLuckyUserContext(), X_PERCENT_SELL_ON_TEXT + "|" + CURSED_USER);

        persistanceMultiRoomResponseEntity.getPersistedData().setLuckyUserContext(LuckyUserContext.UNLUCKY);
        persistanceMultiRoomResponseEntity.getPersistedData().getExpData().put(X_PERCENT_SELL_ON, "true");
        thankYouResponse = thankYouResponseTransformer.convertThankYouResponse(persistanceMultiRoomResponseEntity,null, null);
        Assert.assertNotNull(thankYouResponse);
        Assert.assertNotNull(thankYouResponse.getLuckyUserContext());
        Assert.assertEquals(thankYouResponse.getLuckyUserContext(), X_PERCENT_SELL_ON_TEXT + "|" + UNFORTUNATE_USER);


    }

    @Test
    public void buildBenefitDealsTest() {
        Coupon coupon = thankYouResponseTransformer.buildBenefitDeals(null, null, new ArrayList<>(), null,false);
        Assert.assertNull(coupon);
        coupon = thankYouResponseTransformer.buildBenefitDeals(new BestCoupon(), null, new ArrayList<>(), null,false);
        Assert.assertNull(coupon);
        BestCoupon bestCoupon = new BestCoupon();
        bestCoupon.setDescription("abc");
        List<String> hydraSegments = new ArrayList<>();
        hydraSegments.add("r2021");
        hydraSegments.add("r2022");
        coupon = thankYouResponseTransformer.buildBenefitDeals(bestCoupon, null, hydraSegments, null,false);
        Assert.assertNull(coupon);
        bestCoupon.setForexCouponDetails(new ForexCouponDetails());
        bestCoupon.getForexCouponDetails().setAncillaryName("mmt_ih_forex_cashback");
        bestCoupon.getForexCouponDetails().setPersuasion_message("<font color=\\\"#007E7D\\\">MMT Forex Benefits:</font> 0% Markup, Best rates");
        bestCoupon.setForexCashbackAmount(500.0);
        bestCoupon.setCouponCode("MMTIHFOREX");
        bestCoupon.setPromoIconLink("abc.abc");
        coupon = thankYouResponseTransformer.buildBenefitDeals(bestCoupon, null, null, null, false);
        Assert.assertNotNull(coupon);
        Assert.assertEquals("https://promos.makemytrip.com/images/CDN_upload/forex_tick.png", coupon.getPromoIcon());
        Assert.assertEquals("Offer T&C", coupon.getTncText());
        Assert.assertNotNull(coupon.getCta());
        bestCoupon.getForexCouponDetails().setAncillaryName("mmt_ih_cab_cashback");
        coupon = thankYouResponseTransformer.buildBenefitDeals(bestCoupon, null, null, "www.abc.com",false);
        Assert.assertNotNull("coupon", coupon.getCta().getDeeplink());
        CouponInfo couponInfo = new CouponInfo();
        couponInfo.setSuccessApplyMessage("success message");
        coupon = thankYouResponseTransformer.buildBenefitDeals(bestCoupon, couponInfo, null, "www.abc.com", true);
        Assert.assertEquals("success message", coupon.getDescription());
    }

    @Test
    public void buildCabsDeepLinkUrlTest(){
        PersistedMultiRoomData persistedMultiRoomData = new PersistedMultiRoomData();
        persistedMultiRoomData.setCabsDeepLinkUrl("mmt://cabs/booking%hotelBookingId=");
        BookingMetaInfo bookingMetaInfo = new BookingMetaInfo();
        bookingMetaInfo.setBookingId("NH12646544798");
        persistedMultiRoomData.setBookingMetaInfo(bookingMetaInfo);
        String result = ReflectionTestUtils.invokeMethod(thankYouResponseTransformer, "buildCabsDeepLinkUrl", persistedMultiRoomData);
        Assert.assertEquals("mmt://cabs/booking%hotelBookingId=NH12646544798", result);
    }

    @Test
    public void modifyCancellationPolicyForFlexiCancelTest(){
        PersistedMultiRoomData persistedMultiRoomData = new PersistedMultiRoomData();
        persistedMultiRoomData.setCabsDeepLinkUrl("mmt://cabs/booking%hotelBookingId=");
        BookingMetaInfo bookingMetaInfo = new BookingMetaInfo();
        bookingMetaInfo.setBookingId("NH12646544798");
        persistedMultiRoomData.setFlexiCancelAddOnInfo(new FlexiCancelAddOnInfo());
        persistedMultiRoomData.getFlexiCancelAddOnInfo().setFlexiCancelBasicDetails(new FlexiCancelBasicDetails());
        persistedMultiRoomData.getFlexiCancelAddOnInfo().getFlexiCancelBasicDetails().setFlexiCancelApplied(true);
        persistedMultiRoomData.getFlexiCancelAddOnInfo().getFlexiCancelBasicDetails().setFlexiCancelAddOnState(new HashMap<>());
        persistedMultiRoomData.getFlexiCancelAddOnInfo().getFlexiCancelBasicDetails().getFlexiCancelAddOnState().put("FLEXI_CANCEL", AddOnState.PAYMENT);
        persistedMultiRoomData.setBookingMetaInfo(bookingMetaInfo);
        BookedRatePlan bookedRatePlan = new BookedRatePlan();
        bookedRatePlan.setCancellationPolicy(new BookedCancellationPolicy());
        bookedRatePlan.getCancellationPolicy().setDescription("test");
        String result = ReflectionTestUtils.invokeMethod(thankYouResponseTransformer, "modifyCancellationPolicyForFlexiCancel", persistedMultiRoomData,bookedRatePlan);
    }

    @Test
    public void buildPendingAmountTest() {
        PersistedMultiRoomData persistedMultiRoomData = new PersistedMultiRoomData();
        persistedMultiRoomData.setTotalDisplayFare(new DisplayFare());
        persistedMultiRoomData.setAmountLabels(new ArrayList<>());
        ThankYouAmountLabel thankYouAmountLabel = new ThankYouAmountLabel();
        thankYouAmountLabel.setAmount(500.0);
        thankYouAmountLabel.setLabelType(AMOUNT_LABEL_PARTIAL_AMOUNT_LEFT);
        thankYouAmountLabel.setAmountText("₹500 pending payment");
        persistedMultiRoomData.getAmountLabels().add(thankYouAmountLabel);
        ThankYouResponse thankYouResponse = new ThankYouResponse();
        ReflectionTestUtils.invokeMethod(thankYouResponseTransformer, "buildPendingAmount", thankYouResponse, persistedMultiRoomData);
    }

    @Test
    public void buildPartialAmountPaidTest() {
        PersistedMultiRoomData persistedMultiRoomData = new PersistedMultiRoomData();
        persistedMultiRoomData.setTotalDisplayFare(new DisplayFare());
        persistedMultiRoomData.setAmountLabels(new ArrayList<>());
        ThankYouAmountLabel thankYouAmountLabel = new ThankYouAmountLabel();
        thankYouAmountLabel.setAmount(500.0);
        thankYouAmountLabel.setLabelType(AMOUNT_LABEL_PARTIAL_AMOUNT_PAID);
        thankYouAmountLabel.setAmountText("₹500 pending payment");
        persistedMultiRoomData.getAmountLabels().add(thankYouAmountLabel);
        ThankYouResponse thankYouResponse = new ThankYouResponse();
        ReflectionTestUtils.invokeMethod(thankYouResponseTransformer, "buildPartialAmountPaid", persistedMultiRoomData);
    }

    @Test
    public void testGetHotelDetailsRawDeepLinkUrl_GlobalEntity() {
        PersistedMultiRoomData persistedData = mock(PersistedMultiRoomData.class);
        UserGlobalInfo userGlobalInfo = mock(UserGlobalInfo.class);
        when(persistedData.getUserGlobalInfo()).thenReturn(userGlobalInfo);
        when(userGlobalInfo.getEntityName()).thenReturn("GLOBAL_ENTITY");

        String result = thankYouResponseTransformerPWA.getHotelDetailsRawDeepLinkUrl(persistedData);
        assertEquals("https://www.makemytrip.com/hotels/hotel-listing/?topHtlId={0}&checkin={1}&checkout={2}&country={3}&city={4}&roomStayQualifier={5}&currency={6}&checkAvailability=true", result);
    }

    @Test
    public void testGetHotelDetailsRawDeepLinkUrl_MyPartner() {
        PersistedMultiRoomData persistedData = mock(PersistedMultiRoomData.class);

        PriceByHotelsRequestBody priceByHotelsRequestBody = mock(PriceByHotelsRequestBody.class);
        when(priceByHotelsRequestBody.getProfileType()).thenReturn("CTA");
        when(priceByHotelsRequestBody.getSubProfileType()).thenReturn("MYPARTNER");

        when(persistedData.getAvailReqBody()).thenReturn(priceByHotelsRequestBody);

        Map<String, String> expData = new HashMap<>();
        expData.put("MYPRT","T");

        when(persistedData.getExpData()).thenReturn(expData);

        String result = thankYouResponseTransformerPWA.getHotelDetailsRawDeepLinkUrl(persistedData);
        assertEquals("https://mypartner.makemytrip.com/hotels/hotel-listing/?topHtlId={0}&checkin={1}&checkout={2}&country={3}&city={4}&roomStayQualifier={5}&currency={6}&checkAvailability=true", result);
    }

    @Test
    public void testGetHotelDetailsRawDeepLinkUrl_Default() {
        PersistedMultiRoomData persistedData = mock(PersistedMultiRoomData.class);

        String result = thankYouResponseTransformerPWA.getHotelDetailsRawDeepLinkUrl(persistedData);
        assertEquals("https://www.makemytrip.com/hotels/hotel-listing/?topHtlId={0}&checkin={1}&checkout={2}&country={3}&city={4}&roomStayQualifier={5}&currency={6}&checkAvailability=true", result);
    }



        @Test
        public void testGetGuestCountForPixelUrl_WithNullRoomStayCandidates_ShouldReturnZero() throws Exception {
            // Arrange
            Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", List.class);
            method.setAccessible(true);

            // Act
            int result = (int) method.invoke(thankYouResponseTransformerPWA, (Object) null);

            // Assert
            assertEquals(0, result);
        }

        @Test
        public void testGetGuestCountForPixelUrl_WithEmptyRoomStayCandidates_ShouldReturnZero() throws Exception {
            // Arrange
            Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", List.class);
            method.setAccessible(true);
            List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();

            // Act
            int result = (int) method.invoke(thankYouResponseTransformerPWA, roomStayCandidates);

            // Assert
            assertEquals(0, result);
        }

        @Test
        public void testGetGuestCountForPixelUrl_WithValidRoomStayCandidates_ShouldReturnCorrectCount() throws Exception {
            // Arrange
            Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", List.class);
            method.setAccessible(true);

            List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
            RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
            List<GuestCount> guestCounts = new ArrayList<>();
            GuestCount guestCount = new GuestCount();
            guestCount.setCount("2"); // 2 adults

            List<Integer> childAges = new ArrayList<>();
            childAges.add(5);
            childAges.add(8);
            guestCount.setAges(childAges); // 2 children

            guestCounts.add(guestCount);
            roomStayCandidate.setGuestCounts(guestCounts);
            roomStayCandidates.add(roomStayCandidate);

            // Act
            int result = (int) method.invoke(thankYouResponseTransformerPWA, roomStayCandidates);

            // Assert
            assertEquals(4, result); // 2 adults + 2 children = 4 guests
        }

        @Test
        public void testGetGuestCountForPixelUrl_WithMultipleRooms_ShouldReturnTotalCount() throws Exception {
            // Arrange
            Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", List.class);
            method.setAccessible(true);

            List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();

            // Room 1: 2 adults, 1 child
            RoomStayCandidate roomStayCandidate1 = new RoomStayCandidate();
            List<GuestCount> guestCounts1 = new ArrayList<>();
            GuestCount guestCount1 = new GuestCount();
            guestCount1.setCount("2");

            List<Integer> childAges1 = new ArrayList<>();
            childAges1.add(5);
            guestCount1.setAges(childAges1);

            guestCounts1.add(guestCount1);
            roomStayCandidate1.setGuestCounts(guestCounts1);
            roomStayCandidates.add(roomStayCandidate1);

            // Room 2: 1 adult, 2 children
            RoomStayCandidate roomStayCandidate2 = new RoomStayCandidate();
            List<GuestCount> guestCounts2 = new ArrayList<>();
            GuestCount guestCount2 = new GuestCount();
            guestCount2.setCount("1");

            List<Integer> childAges2 = new ArrayList<>();
            childAges2.add(7);
            childAges2.add(9);
            guestCount2.setAges(childAges2);

            guestCounts2.add(guestCount2);
            roomStayCandidate2.setGuestCounts(guestCounts2);
            roomStayCandidates.add(roomStayCandidate2);

            // Act
            int result = (int) method.invoke(thankYouResponseTransformerPWA, roomStayCandidates);

            // Assert
            assertEquals(6, result); // 3 adults + 3 children = 6 guests total
        }

        @Test
        public void testGetGuestCountForPixelUrl_WithNullGuestCounts_ShouldReturnZero() throws Exception {
            // Arrange
            Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", List.class);
            method.setAccessible(true);

            List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
            RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
            roomStayCandidate.setGuestCounts(null);
            roomStayCandidates.add(roomStayCandidate);

            // Act
            int result = (int) method.invoke(thankYouResponseTransformerPWA, roomStayCandidates);

            // Assert
            assertEquals(0, result);
        }

        @Test
        public void testGetGuestCountForPixelUrl_WithEmptyGuestCounts_ShouldReturnZero() throws Exception {
            // Arrange
            Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", List.class);
            method.setAccessible(true);

            List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
            RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
            roomStayCandidate.setGuestCounts(new ArrayList<>());
            roomStayCandidates.add(roomStayCandidate);

            // Act
            int result = (int) method.invoke(thankYouResponseTransformerPWA, roomStayCandidates);

            // Assert
            assertEquals(0, result);
        }

        @Test
        public void testGetGuestCountForPixelUrl_WithNoChildrenAges_ShouldCountOnlyAdults() throws Exception {
            // Arrange
            Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", List.class);
            method.setAccessible(true);

            List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
            RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
            List<GuestCount> guestCounts = new ArrayList<>();
            GuestCount guestCount = new GuestCount();
            guestCount.setCount("3");
            guestCount.setAges(null); // No children

            guestCounts.add(guestCount);
            roomStayCandidate.setGuestCounts(guestCounts);
            roomStayCandidates.add(roomStayCandidate);

            // Act
            int result = (int) method.invoke(thankYouResponseTransformerPWA, roomStayCandidates);

            // Assert
            assertEquals(3, result); // 3 adults, 0 children = 3 guests
        }

        @Test
        public void testGetGuestCountForPixelUrl_WithEmptyChildrenAges_ShouldCountOnlyAdults() throws Exception {
            // Arrange
            Method method = ThankYouResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", List.class);
            method.setAccessible(true);

            List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
            RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
            List<GuestCount> guestCounts = new ArrayList<>();
            GuestCount guestCount = new GuestCount();
            guestCount.setCount("3");
            guestCount.setAges(new ArrayList<>()); // Empty children list

            guestCounts.add(guestCount);
            roomStayCandidate.setGuestCounts(guestCounts);
            roomStayCandidates.add(roomStayCandidate);

            // Act
            int result = (int) method.invoke(thankYouResponseTransformerPWA, roomStayCandidates);

            // Assert
            assertEquals(3, result); // 3 adults, 0 children = 3 guests
        }

}
