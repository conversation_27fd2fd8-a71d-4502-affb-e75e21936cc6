package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.MyPartnerPersonalizedSection;
import com.mmt.hotels.clientgateway.enums.Persuasions;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.Coupon;
import com.mmt.hotels.clientgateway.response.PriceDetail;
import com.mmt.hotels.clientgateway.response.dayuse.Slot;
import com.mmt.hotels.clientgateway.response.dayuse.SlotDetail;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.searchHotels.*;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.response.ios.SearchHotelsResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.SearchHotelsResponseTransformerPWA;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.enums.LuckyUserContext;
import com.mmt.hotels.model.enums.SectionsType;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.HomeStayDetails;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.request.matchmaker.InputHotel;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.model.response.MpFareHoldStatus;
import com.mmt.hotels.model.response.addon.AddOnNode;
import com.mmt.hotels.model.response.flyfish.TravellerRatingSummaryDTO;
import com.mmt.hotels.model.response.persuasion.HotelPersuasions;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.searchwrapper.*;
import com.mmt.hotels.model.response.searchwrapper.GeoLocation;
import com.mmt.hotels.model.response.staticdata.Address;
import com.mmt.hotels.model.response.staticdata.FacilityCategorization;
import com.mmt.hotels.model.response.staticdata.POITag;
import com.mmt.hotels.model.response.staticdata.VideoInfo;
import com.mmt.hotels.pojo.response.detail.FeaturedAmenity;
import junit.framework.Assert;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class OrchSearchHotelsResponseTransformerPWATest {

	@InjectMocks
	SearchHotelsResponseTransformerPWA searchHotelsResponseTransformerPWA;

	@Mock
	CommonResponseTransformer commonResponseTransformer;

	private ObjectMapper objectMapper = new ObjectMapper();

	@Mock
	ObjectMapperUtil objectMapperUtil;

	@Mock
	private DateUtil dateUtil;

	@Mock
	CrossSellUtil crossSellUtil;

	@Mock
	private MobConfigHelper mobConfigHelper;

	@Mock
	PolyglotService polyglotService;

	@Mock
	Utility utility;

	Hotel hotel;
	@Mock
	CommonHelper commonHelper;

	@Mock
	MetricAspect metricAspect;

	@Mock
	PersuasionUtil persuasionUtil;
	@Spy
	PricingEngineHelper pricingEngineHelper;

	@InjectMocks
	private SearchHotelsResponseTransformerIOS searchHotelsResponseTransformerIOS;

	@Mock
	private ListingPagePersonalizationResponsBO listingPageResponseBO;

	@Mock
	private SearchHotelsRequest searchHotelsRequest;

	@Mock
	private CommonModifierResponse commonModifierResponse;

	private static final Logger logger = LoggerFactory.getLogger(MetricErrorLogger.class);

	@Before
	public void init() throws IOException {
			ObjectMapper mapper = new ObjectMapper();
			mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
			mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
			InputStream availPriceRequest = getClass().getClassLoader().getResourceAsStream("mockrequestresponsetest/hotel.json");
			hotel = mapper.readValue(availPriceRequest, Hotel.class);
			Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(Mockito.any(),Mockito.any(),Mockito.anyLong());
			MDC.put(MDCHelper.MDCKeys.LANGUAGE.getStringValue(),"eng");

			MyBizStaticCard myBizStaticCard = new MyBizStaticCard();
			myBizStaticCard.setText("CORPBUDGET_STATIC_TEXT");
			myBizStaticCard.setSubtext("CORPBUDGET_STATIC_SUBTEXT");
			myBizStaticCard.setCtaText("CORPBUDGET_STATIC_CTATEXT");
			ReflectionTestUtils.setField(searchHotelsResponseTransformerPWA,"myBizStaticCard",myBizStaticCard);
	}

	@Test
	public void testConvertSearchHotelsResponse_FromSampleResponse() throws IOException {
		ListingPagePersonalizationResponsBO webApiResponse = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/searchHotelsHESResponse.json")),
				ListingPagePersonalizationResponsBO.class);
		CommonModifierResponse commonModifierResponse=new CommonModifierResponse();
		commonModifierResponse.setExpDataMap(new LinkedHashMap<>());
		commonModifierResponse.getExpDataMap().put(MYPARTNER_EXCLUSIVE_DEAL,"true");

		boolean isXPercentSellOn = MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap())
				&& commonModifierResponse.getExpDataMap().containsKey(X_PERCENT_SELL_ON) &&
				TRUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(X_PERCENT_SELL_ON));

		String isXPercentSellExpText = isXPercentSellOn ? X_PERCENT_SELL_ON_TEXT : X_PERCENT_SELL_OFF_TEXT;

		webApiResponse.setLuckyUserContext(LuckyUserContext.LUCKY);
		Mockito.when(utility.logLuckyUserData(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(isXPercentSellExpText + "|" + PRIVILEGED_USER);
		SearchHotelsResponse response = searchHotelsResponseTransformerPWA.convertSearchHotelsResponse(webApiResponse,new SearchHotelsRequest(),commonModifierResponse);
		Assert.assertNotNull(response);
		Assert.assertEquals(response.getHotelCount(),webApiResponse.getTotalHotelCounts());
		Assert.assertEquals(response.getCurrency(),webApiResponse.getCurrency());
		Assert.assertEquals(response.getLastHotelId(),webApiResponse.getLastFetchedHotelId());
		Assert.assertEquals(response.getLastHotelCategory(),webApiResponse.getLastFetchedHotelCategory());
		Assert.assertTrue(compareSections(response.getPersonalizedSections(),webApiResponse.getPersonalizedResponse()));
		Assert.assertNotNull(response.getSharingUrl());
		Assert.assertNotNull(response.getLuckyUserContext());
		Assert.assertEquals(isXPercentSellExpText + "|" + PRIVILEGED_USER, response.getLuckyUserContext());

		webApiResponse.setLuckyUserContext(LuckyUserContext.LUCKY_UNLUCKY);
		Mockito.when(utility.logLuckyUserData(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(isXPercentSellExpText + "|" + CURSED_USER);
		response = searchHotelsResponseTransformerPWA.convertSearchHotelsResponse(webApiResponse,new SearchHotelsRequest(),commonModifierResponse);
		Assert.assertNotNull(response);
		Assert.assertEquals(response.getHotelCount(),webApiResponse.getTotalHotelCounts());
		Assert.assertEquals(response.getCurrency(),webApiResponse.getCurrency());
		Assert.assertEquals(response.getLastHotelId(),webApiResponse.getLastFetchedHotelId());
		Assert.assertEquals(response.getLastHotelCategory(),webApiResponse.getLastFetchedHotelCategory());
		Assert.assertTrue(compareSections(response.getPersonalizedSections(),webApiResponse.getPersonalizedResponse()));
		Assert.assertNotNull(response.getSharingUrl());
		Assert.assertNotNull(response.getLuckyUserContext());
		Assert.assertEquals(isXPercentSellExpText + "|" + CURSED_USER, response.getLuckyUserContext());

		webApiResponse.setLuckyUserContext(LuckyUserContext.UNLUCKY);
		Mockito.when(utility.logLuckyUserData(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(isXPercentSellExpText + "|" + UNFORTUNATE_USER);
		response = searchHotelsResponseTransformerPWA.convertSearchHotelsResponse(webApiResponse,new SearchHotelsRequest(),commonModifierResponse);
		Assert.assertNotNull(response);
		Assert.assertEquals(response.getHotelCount(),webApiResponse.getTotalHotelCounts());
		Assert.assertEquals(response.getCurrency(),webApiResponse.getCurrency());
		Assert.assertEquals(response.getLastHotelId(),webApiResponse.getLastFetchedHotelId());
		Assert.assertEquals(response.getLastHotelCategory(),webApiResponse.getLastFetchedHotelCategory());
		Assert.assertTrue(compareSections(response.getPersonalizedSections(),webApiResponse.getPersonalizedResponse()));
		Assert.assertNotNull(response.getSharingUrl());
		Assert.assertNotNull(response.getLuckyUserContext());
		Assert.assertEquals(isXPercentSellExpText + "|" + UNFORTUNATE_USER, response.getLuckyUserContext());

		webApiResponse.setLuckyUserContext(null);
		Mockito.when(utility.logLuckyUserData(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(X_PERCENT_SELL_OFF_TEXT);
		response = searchHotelsResponseTransformerPWA.convertSearchHotelsResponse(webApiResponse,new SearchHotelsRequest(),commonModifierResponse);
		Assert.assertNotNull(response);
		Assert.assertEquals(response.getHotelCount(),webApiResponse.getTotalHotelCounts());
		Assert.assertEquals(response.getCurrency(),webApiResponse.getCurrency());
		Assert.assertEquals(response.getLastHotelId(),webApiResponse.getLastFetchedHotelId());
		Assert.assertEquals(response.getLastHotelCategory(),webApiResponse.getLastFetchedHotelCategory());
		Assert.assertTrue(compareSections(response.getPersonalizedSections(),webApiResponse.getPersonalizedResponse()));
		Assert.assertNotNull(response.getSharingUrl());
		Assert.assertNotNull(response.getLuckyUserContext());
		Assert.assertEquals(X_PERCENT_SELL_OFF_TEXT, response.getLuckyUserContext());

		webApiResponse.getExpData().put("emptyShopSolution", "true");
		commonModifierResponse.getExpDataMap().put(APPLY_IN_POLICY_COR_FILTER_EXP, "true");
		webApiResponse.setLastFetchedHotelCategory(SectionsType.FILTER_REMOVAL.name());
		webApiResponse.setInPolicyFilterRemoved(true);
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		List<Filter> filterCriteria = new ArrayList<>();
		Filter filter = new Filter(FilterGroup.STAR_RATING,"5");
		Filter inpolicyFilter = new Filter(FilterGroup.IN_POLICY,"IN_POLICY");
		filterCriteria.add(filter);
		filterCriteria.add(inpolicyFilter);
		searchHotelsRequest.setFilterCriteria(filterCriteria);
		Mockito.when(utility.logLuckyUserData(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(isXPercentSellExpText + "|" + PRIVILEGED_USER);
		response = searchHotelsResponseTransformerPWA.convertSearchHotelsResponse(webApiResponse, searchHotelsRequest, commonModifierResponse);
		Assert.assertNotNull(response);
		Assert.assertEquals(response.getHotelCount(),webApiResponse.getTotalHotelCounts());
		Assert.assertEquals(response.getCurrency(),webApiResponse.getCurrency());
		Assert.assertEquals(response.getLastHotelId(),webApiResponse.getLastFetchedHotelId());
		Assert.assertEquals(response.getLastHotelCategory(),webApiResponse.getLastFetchedHotelCategory());
		Assert.assertNotNull(response.getFilterRemovedCriteria());
		Assert.assertNotNull(response.getSharingUrl());
		Assert.assertNotNull(response.getLuckyUserContext());
		Assert.assertEquals(isXPercentSellExpText + "|" + PRIVILEGED_USER, response.getLuckyUserContext());
	}
	@Test
	public void testConvertSearchHotelsResponseForMyPartner_ForHighGraspHotels() throws IOException {
		Map<String,Integer> map = new HashMap<>();
		map.put("SIMILAR_HOTELS",10);
		map.put("RECOMMENDED_HOTELS",10);

		ExtendedUser extendedUser=new ExtendedUser();
		extendedUser.setProfileType("CTA");
		extendedUser.setAffiliateId("MYPARTNER");

		CommonModifierResponse commonModifierResponse=new CommonModifierResponse();
		commonModifierResponse.setExtendedUser(extendedUser);
		commonModifierResponse.setExpDataMap(new LinkedHashMap<>());
		commonModifierResponse.getExpDataMap().put(MYPARTNER_HIGH_GRASP_HOTELS,"HG_POSITION_1");

		SearchHotelsRequest searchHotelsRequest=new SearchHotelsRequest();
		searchHotelsRequest.setDeviceDetails(new DeviceDetails());
		searchHotelsRequest.getDeviceDetails().setBookingDevice("DESKTOP");
		searchHotelsRequest.setRequestDetails(new RequestDetails());

		ListingPagePersonalizationResponsBO webApiResponse =
				new Gson().fromJson(
						FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/highGraspHotels.json")),
						ListingPagePersonalizationResponsBO.class
				);
		Mockito.when(mobConfigHelper.getMyPartnerSectionListCount()).thenReturn(map);
		Mockito.when(utility.isMyPartner(Mockito.any())).thenReturn(true);
		Mockito.when(utility.isExperimentTrue(Mockito.any(),Mockito.anyString())).thenReturn(true);
		SearchHotelsResponse response = searchHotelsResponseTransformerPWA.convertSearchHotelsResponse(webApiResponse,searchHotelsRequest,commonModifierResponse);
		Assert.assertNotNull(response);
		Assert.assertNotNull(response.getPersonalizedSections());
		Assert.assertNotNull(response.getPersonalizedSections().get(0));
		Assert.assertEquals(response.getPersonalizedSections().size(), 1);
		Assert.assertEquals(response.getPersonalizedSections().get(0).getHeading(), HG_HORIZONTAL_HEADING);
	}
	@Test
	public void testConvertSearchHotelsResponseForMyPartner_FromSampleResponse() throws IOException {
		Map<String,Integer> map = new HashMap<>();
		map.put("LAST_BOOKED_HOTELS",1);
		map.put("RECENTLY_VIEWED_HOTELS",1);


		ExtendedUser extendedUser=new ExtendedUser();
		extendedUser.setProfileType("CTA");
		extendedUser.setAffiliateId("MYPARTNER");

		CommonModifierResponse commonModifierResponse=new CommonModifierResponse();
		commonModifierResponse.setExtendedUser(extendedUser);
		commonModifierResponse.setExpDataMap(new LinkedHashMap<>());
		commonModifierResponse.getExpDataMap().put(MYPARTNER_EXCLUSIVE_DEAL,"true");

		ListingPagePersonalizationResponsBO webApiResponse = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/searchHotelsHESResponse.json")),
				ListingPagePersonalizationResponsBO.class
		);
		Mockito.when(mobConfigHelper.getMyPartnerSectionListCount()).thenReturn(map);
		SearchHotelsResponse response = searchHotelsResponseTransformerPWA.convertSearchHotelsResponse(webApiResponse,new SearchHotelsRequest(),commonModifierResponse);
		Assert.assertNotNull(response);
		Assert.assertEquals(response.getHotelCount(),webApiResponse.getTotalHotelCounts());
		Assert.assertEquals(response.getCurrency(),webApiResponse.getCurrency());
		Assert.assertEquals(response.getLastHotelId(),webApiResponse.getLastFetchedHotelId());
		Assert.assertEquals(response.getLastHotelCategory(),webApiResponse.getLastFetchedHotelCategory());
		Assert.assertTrue(compareSections(response.getPersonalizedSections(),webApiResponse.getPersonalizedResponse()));
		Assert.assertNotNull(response.getPersonalizedSections());
		Assert.assertEquals((int)response.getPersonalizedSections().get(0).getMinItemsToShow(),1);
		Assert.assertNotNull(response.getSharingUrl());

	}
	@Test
	public void testConvertSearchHotelsResponseForMyPartner_ForExclusiveDealHeading() throws IOException {
		Map<String,Integer> map = new HashMap<>();
		map.put("RECOMMENDED_HOTELS",10);


		ExtendedUser extendedUser=new ExtendedUser();
		extendedUser.setProfileType("CTA");
		extendedUser.setAffiliateId("MYPARTNER");

		CommonModifierResponse commonModifierResponse=new CommonModifierResponse();
		commonModifierResponse.setExtendedUser(extendedUser);
		commonModifierResponse.setExpDataMap(new LinkedHashMap<>());
		commonModifierResponse.getExpDataMap().put(MYPARTNER_EXCLUSIVE_DEAL,"true");
		ListingPagePersonalizationResponsBO webApiResponse =
						new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/exclusiveDealListingResponse.json")),
				ListingPagePersonalizationResponsBO.class
		);
		LinkedHashMap<String, Object> hotelPersuasions=new LinkedHashMap<>((LinkedTreeMap)webApiResponse.getPersonalizedResponse().get(0).getHotels().get(0).getHotelPersuasions());
		hotelPersuasions.put("PC_IMG_ANNOTATION",new LinkedHashMap<String,Object>());
		List<LinkedHashMap<String,Object>> data =new ArrayList<>();
		data.add(new LinkedHashMap<>());
		data.get(0).put("iconurl",EXCLUSIVE_DEAL_IMAGE_URL);
		((LinkedHashMap<String,Object>)hotelPersuasions.get("PC_IMG_ANNOTATION")).put("data",data);
		webApiResponse.getPersonalizedResponse().get(0).getHotels().get(0).setHotelPersuasions(hotelPersuasions);
		webApiResponse.getPersonalizedResponse().get(0).getHotels().get(0).setShowCallToBook(true);
		webApiResponse.getPersonalizedResponse().get(0).getHotels().get(0).setHotelChainName("test");
		webApiResponse.getPersonalizedResponse().get(0).getHotels().get(0).setPropertyInfo(new PropertyChainInfo());
		webApiResponse.getPersonalizedResponse().get(0).getHotels().get(0).getPropertyInfo().setChainId("test");
		webApiResponse.getPersonalizedResponse().get(0).getHotels().get(0).setActiveButOffline(true);
		webApiResponse.getLocusData().setLocusName("Goa");
		Mockito.when(mobConfigHelper.getMyPartnerSectionListCount()).thenReturn(map);
		Mockito.when(utility.isMyPartner(Mockito.any())).thenReturn(true);
		Mockito.when(utility.isExperimentTrue(Mockito.any(),Mockito.anyString())).thenReturn(true);
		SearchHotelsResponse response = searchHotelsResponseTransformerPWA.convertSearchHotelsResponse(webApiResponse,new SearchHotelsRequest(),commonModifierResponse);
		Assert.assertNotNull(response);
		Assert.assertNotNull(response.getPersonalizedSections());
		Assert.assertNotNull(response.getPersonalizedSections().get(0));
		Assert.assertEquals(response.getPersonalizedSections().get(0).getHeading(),EXCLUSIVE_DEAL_HEADING);
	}
	private boolean compareSections(List<PersonalizedSection> cgSections, List<PersonalizedResponse<SearchWrapperHotelEntity>> webApiSections){
		if (CollectionUtils.isEmpty(webApiSections) && CollectionUtils.isEmpty(cgSections))
			return true;
		boolean isEquals = true;
		for (PersonalizedResponse<SearchWrapperHotelEntity> section : webApiSections) {
			Optional<PersonalizedSection> optionalSection = cgSections.stream().filter( c -> c.getName().equalsIgnoreCase(section.getSection())).findAny();
			if (!optionalSection.isPresent() || !compareSectionNodes(optionalSection.get(),section)) {
				isEquals = false;
				break;
			}
		}
		return isEquals;
	}

	private boolean compareSectionNodes(PersonalizedSection cgSection,PersonalizedResponse<SearchWrapperHotelEntity> webApiSection) {
		if (StringUtils.isNotBlank(webApiSection.getSection()) && !webApiSection.getSection().equalsIgnoreCase(cgSection.getName()))
			return false;
		if (StringUtils.isNotBlank(webApiSection.getHeading()) && !webApiSection.getHeading().equalsIgnoreCase(cgSection.getHeading()))
			return false;
		if (StringUtils.isNotBlank(webApiSection.getSubHeading()) && !webApiSection.getSubHeading().equalsIgnoreCase(cgSection.getSubHeading()))
			return false;
		if (CollectionUtils.isNotEmpty(webApiSection.getHotels()) && CollectionUtils.isEmpty(cgSection.getHotels()))
			return false;
		if (CollectionUtils.isNotEmpty(cgSection.getHotels()) && cgSection.getMinItemsToShow()!=null && cgSection.getMinItemsToShow() < cgSection.getHotels().size() && !cgSection.isShowMore())
			return false;
		if (CollectionUtils.isEmpty(cgSection.getHotels()) && CollectionUtils.isEmpty(webApiSection.getHotels()))
			return true;

		boolean isEquals = true;
		for (SearchWrapperHotelEntity hotelEntity : webApiSection.getHotels()) {
			Optional<Hotel> optionalHotel = cgSection.getHotels().stream().filter(c -> c.getId().equalsIgnoreCase(hotelEntity.getId())).findAny();
			if (!optionalHotel.isPresent() || !compareHotel(optionalHotel.get(),hotelEntity)) {
				isEquals = false;
				break;
			}
		}
		return isEquals;
	}

	private boolean compareHotel(Hotel cgHotel,SearchWrapperHotelEntity webApiHotel) {
		if (webApiHotel.getIsSoldOut()!=null && cgHotel.isSoldOut()!=webApiHotel.getIsSoldOut())
			return false;
		if (webApiHotel.getDisplayFare()!=null && webApiHotel.getDisplayFare().getDisplayPriceBreakDown()!=null) {
			DisplayPriceBreakDown dpbd = webApiHotel.getDisplayFare().getDisplayPriceBreakDown();
			if (cgHotel.getPriceDetail()==null)
				return false;
			PriceDetail pd = cgHotel.getPriceDetail();
			if (pd.getBasePrice()!=null || pd.getCdfDiscount()!=null || pd.getDiscount()!=null || pd.getDisplayPriceAltCurrency()!=null || pd.getEffectivePrice()!=null)
				return false;
			if (pd.getWallet()!=null || pd.getMmtDiscount()!=null || pd.getHotelTax()!=null)
				return false;
			if (StringUtils.isNotBlank(dpbd.getPricingKey()) && !dpbd.getPricingKey().equalsIgnoreCase(pd.getPricingKey()))
				return false;
		}
		return true;
	}

	@Test
	public void testConvertSearchHotelsResponse_FromCorpSampleResponse() throws IOException {

		Map<String,Integer> map = new HashMap<>();
		map.put("BOOKED_BY_COMPANY",5);
		map.put("LAST_BOOKED_HOTELS",5);
		map.put("MYBIZ_RECOMMENDED_HOTELS",5);
		map.put("PREFERRED_BY_COMPANY",5);
		map.put("RECENTLY_VIEWED_HOTELS",5);

		CommonModifierResponse commonModifierResponse=new CommonModifierResponse();
		LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
		expDataMap.put(MYPARTNER_EXCLUSIVE_DEAL,"true");
		commonModifierResponse.setExpDataMap(expDataMap);

		Mockito.when(mobConfigHelper.getCorpSectionListCount()).thenReturn(map);

		ListingPagePersonalizationResponsBO webApiResponse = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/listingPersonalizationHESResponse.json")),
				ListingPagePersonalizationResponsBO.class);

		Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("sample value");
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		SearchHotelsCriteria searchHotelsCriteria = new SearchHotelsCriteria();
		searchHotelsCriteria.setCountryCode("IN");
		searchHotelsRequest.setSearchCriteria(searchHotelsCriteria);
		searchHotelsRequest.setExpData("");
		SearchHotelsResponse response = searchHotelsResponseTransformerPWA.convertSearchHotelsResponse(webApiResponse,searchHotelsRequest,commonModifierResponse);
		Assert.assertNotNull(response);
		Assert.assertEquals(response.getHotelCount(),webApiResponse.getTotalHotelCounts());
		Assert.assertEquals(response.getCurrency(),webApiResponse.getCurrency());
		Assert.assertEquals(response.getLastHotelId(),webApiResponse.getLastFetchedHotelId());
		Assert.assertEquals(response.getLastHotelCategory(),webApiResponse.getLastFetchedHotelCategory());
		Assert.assertTrue(compareSections(response.getPersonalizedSections(),webApiResponse.getPersonalizedResponse()));

		/* Test MyBiz ToolTip */
		Assert.assertTrue(response.getPersonalizedSections().stream().anyMatch(
				personalizedSection -> (Constants.MY_BIZ_ASSURED_SECTION).equalsIgnoreCase(personalizedSection.getName())
															&& personalizedSection.getToolTip() != null));
		Assert.assertNotNull(response.getSharingUrl());
	}

	@Test
	public void testConvertSearchHotelsResponse() throws JsonProcessingException {
		ListingPagePersonalizationResponsBO listingResponse = new ListingPagePersonalizationResponsBO();
		listingResponse.setPersonalizedResponse(new ArrayList<>());
		listingResponse.getPersonalizedResponse().add(new PersonalizedResponse<>());
		listingResponse.getPersonalizedResponse().get(0).setHotels(new ArrayList<>());
		listingResponse.getPersonalizedResponse().get(0).getHotels().add(buildSearchWrapperHotelEntity());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setMainImages(Collections.singletonList("adc"));
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setDisplayFare(new DisplayFare());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).getDisplayFare().setDisplayPriceBreakDown(buildDisplayPriceBreakDown());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setAddress(new Address());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).getAddress().setArea(Collections.singletonList("area"));
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setGeoLocation(new GeoLocation());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setFacilityCategorization(Collections.singletonList(new FacilityCategorization()));
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setPoiTagList(Collections.singletonList(new POITag()));
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setAddOns(Collections.singletonList(new AddOnNode()));
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setSegments(new Segments());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).getSegments().setSegmentList(new HashMap<>());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).getSegments().getSegmentList().put("test", new Segment());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setPersuasions(new HashSet<>());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).getPersuasions().add(new HotelPersuasions());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setHotelVideos(Collections.singletonList(new VideoInfo()));
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).getHotelVideos().get(0).setUrl("video_url");
		listingResponse.getPersonalizedResponse().get(0).setCategoryPersuasions(Collections.singletonList(new CategoryPersuasion()));
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setFlyfishReviewSummary(new HashMap<>());
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setQuickBookInfo(new QuickBookInfo());
		MyBizSimilarToDirectObj myBizSimilarToDirectObj = new MyBizSimilarToDirectObj();
		myBizSimilarToDirectObj.setAmenities(Arrays.asList(new FeaturedAmenity()));
		myBizSimilarToDirectObj.setDistance("test");
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).setMyBizSimilarToDirectObj(myBizSimilarToDirectObj);

		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setTrafficSource(new TrafficSource());
		requestDetails.getTrafficSource().setSource("SEO");
		requestDetails.setMetaInfo(true);
		searchHotelsRequest.setRequestDetails(requestDetails);
		searchHotelsRequest.setExpData("anc");
		searchHotelsRequest.setSearchCriteria(buildSearchCriteria());

		String ratingJson = "{\n" +
				"  \"cumulativeRating\": 4.5,\n" +
				"  \"totalReviewsCount\": 89,\n" +
				"  \"totalRatingCount\": 162,\n" +
				"  \"best\": [\n" +
				"    {\n" +
				"      \"title\": \"\"\n" +
				"    }\n" +
				"  ],\n" +
				"  \"sortingCriterion\": [\n" +
				"    \"Latest first\",\n" +
				"    \"Helpful first\",\n" +
				"    \"Positive first\",\n" +
				"    \"Negative first\"\n" +
				"  ]\n" +
				"}";
		CommonModifierResponse commonModifierResponse=new CommonModifierResponse();
		LinkedHashMap<String,String> expData=new LinkedHashMap<>();
		expData.put(MYPARTNER_EXCLUSIVE_DEAL,"true");
		commonModifierResponse.setExpDataMap(expData);
		listingResponse.getPersonalizedResponse().get(0).getHotels().get(0).getFlyfishReviewSummary().put(OTA.TA, new ObjectMapper().readTree(ratingJson));
		Assert.assertNotNull(searchHotelsResponseTransformerPWA.convertSearchHotelsResponse(listingResponse, searchHotelsRequest,commonModifierResponse));
	}

	private DisplayPriceBreakDown buildDisplayPriceBreakDown(){
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setDisplayPrice(36000.00);
		displayPriceBreakDown.setSavingPerc(80.00);
		return displayPriceBreakDown;
	}

	private SearchWrapperHotelEntity buildSearchWrapperHotelEntity(){
		SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
		searchWrapperHotelEntity.setRoomCount(5);
		return searchWrapperHotelEntity;
	}

	private SearchHotelsCriteria buildSearchCriteria(){
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setCheckIn("2022-11-10");
		searchCriteria.setCheckOut("2022-11-11");
		return searchCriteria;
	}

	@Test
	public void addLocationPersuasionToHotelPersuasionsTest(){
		List<String> locations = new ArrayList<>();
		locations.add("This is test location persuasion");
		LinkedHashSet<String> ameneties = new LinkedHashSet<>();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setDayUsePersuasionsText("Test_Persuasions");
		ameneties.add("WI-FI");
		ameneties.add("bathtub");
		hotel.setHotelPersuasions(new HashMap<>());
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		searchHotelsRequest.getRequestDetails().setFunnelSource("DAYUSE");
		searchHotelsResponseTransformerPWA.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null,null,null, false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		org.junit.Assert.assertEquals(((Map<String, Object>)hotel.getHotelPersuasions()).size(),2);

		locations.add("one more test persuasion");
		hotel.setHotelPersuasions(new HashMap<>());
		searchHotelsResponseTransformerPWA.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null,null,null, false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		org.junit.Assert.assertEquals(((Map<String, Object>)hotel.getHotelPersuasions()).size(),2);

		//Test for Secondary Location Persuasion
		String secondaryPersuasion = "Secondary Persuasion";
		locations.add(secondaryPersuasion);
		hotel.setHotelPersuasions(new HashMap<>());
		searchHotelsResponseTransformerPWA.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null,null,null, false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP));
		org.junit.Assert.assertTrue(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP) instanceof PersuasionObject);
		org.junit.Assert.assertNotNull(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP)).getData());
		org.junit.Assert.assertTrue(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP)).getData().size() > 0);
		org.junit.Assert.assertTrue(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP)).getData().get(0).getText().contains(secondaryPersuasion));

	}

	@Test
	public void overridePersonalizedSectionHeadingForDirectHotelSearchTest() {
		InputHotel inputHotel = new InputHotel();
		inputHotel.setHotelId("123456789");
		MatchMakerRequest matchMakerRequest = new MatchMakerRequest();
		matchMakerRequest.setHotels(Collections.singletonList(inputHotel));
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setFunnelSource("CORPBUDGET");
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setMatchMakerDetails(matchMakerRequest);
		searchHotelsRequest.setRequestDetails(requestDetails);
		PersonalizedSection personalizedSection = new PersonalizedSection();
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("MyBiz Recommended Properties Near This Property");
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "overridePersonalizedSectionHeadingForDirectHotelSearch", searchHotelsRequest, personalizedSection);
		org.junit.Assert.assertEquals("MyBiz Recommended Properties Near This Property", personalizedSection.getHeading());
	}

	@Test
	public void doNotOverridePersonalizedSectionHeadingForDirectHotelSearchTest() {
		MatchMakerRequest matchMakerRequest = new MatchMakerRequest();
		PersonalizedSection personalizedSection = new PersonalizedSection();
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setFunnelSource("HOTELS");
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setMatchMakerDetails(matchMakerRequest);
		searchHotelsRequest.setRequestDetails(requestDetails);
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "overridePersonalizedSectionHeadingForDirectHotelSearch", searchHotelsRequest, personalizedSection);
		org.junit.Assert.assertNull(personalizedSection.getHeading());
	}

	@Test
	public void testGetMyBizDirectHotelDistanceText() {
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("");
		org.junit.Assert.assertNotNull(searchHotelsResponseTransformerPWA.getMyBizDirectHotelDistanceText("test"));
	}

	@Test
	public void testBuildBottomSheet() throws IOException {
		ListingPagePersonalizationResponsBO webApiResponse = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/listingPersonalizationHESResponse.json")),
				ListingPagePersonalizationResponsBO.class);
		searchHotelsResponseTransformerPWA.buildBottomSheet(webApiResponse.getPersonalizedResponse().get(0));
	}
	@Test
	public void testBuildStaticCard(){
		MyBizStaticCard staticCard = new MyBizStaticCard();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setCorpBudgetHotel(false);
		hotelEntity.setDetailDeeplinkUrl("test");
		List<SearchWrapperHotelEntity> hotelEntityList = new ArrayList<>();
		hotelEntityList.add(hotelEntity);
		Mockito.when(polyglotService.getTranslatedData("CORPBUDGET_STATIC_TEXT")).thenReturn("This Is Not A Budget Hotel");
		staticCard = searchHotelsResponseTransformerPWA.buildStaticCard("DIRECT_HOTEL",hotelEntityList);
		org.junit.Assert.assertNotNull(staticCard);
		org.junit.Assert.assertEquals("This Is Not A Budget Hotel", staticCard.getText());
	}
	@Test
	public void testUpdateTopLevelHover() throws JSONException {

		// Create mock objects
		JSONObject mockJSONObject = mock(JSONObject.class);
		MpFareHoldStatus mockMpFareHoldStatus = mock(MpFareHoldStatus.class);

		// Define mock behavior
		when(mockJSONObject.optString("tooltipType")).thenReturn("MP_FARE_HOLD");
		when(mockMpFareHoldStatus.getExpiry()).thenReturn(123456789L);
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("abc");
		Mockito.when(dateUtil.convertEpochToDateTime(Mockito.anyLong(), Mockito.anyString())).thenReturn("abc");

		// Call method to test
		searchHotelsResponseTransformerPWA.updateTopLevelHover(mockJSONObject, mockMpFareHoldStatus);

		Mockito.verify(mockJSONObject, Mockito.times(1)).optString("tooltipType");
		Mockito.verify(mockMpFareHoldStatus, Mockito.times(1)).getExpiry();
	}

	@Test
	public void addPersuasionsForHiddenGemCardTest() {
		SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
		searchWrapperHotelEntity.setLocationPersuasion(Collections.singletonList("Test Location"));
		searchWrapperHotelEntity.setHiddenGem(true);
		searchWrapperHotelEntity.setHiddenGemPersuasionText("Hidden Gem Persuasion Text");
		HomeStayDetails homeStayDetails = new HomeStayDetails();
		homeStayDetails.setStayType(Constants.STAY_TYPE_HOMESTAY);
		homeStayDetails.setStayTypeInfo("Home Stay Info Test");

		PersuasionObject homeStaysTitlePersuasion = new PersuasionObject();
		List<PersuasionData> homeStaysTitlePersuasionList = new ArrayList<>();
		homeStaysTitlePersuasionList.add(new PersuasionData());
		homeStaysTitlePersuasion.setData(homeStaysTitlePersuasionList);

		PersuasionObject homestaysSubTitlePersuasion = new PersuasionObject();
		homestaysSubTitlePersuasion.setData(Arrays.asList(new PersuasionData()));

		when(persuasionUtil.buildHiddenGemPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
		when(persuasionUtil.buildHiddenGemIconPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
		when(persuasionUtil.buildHomeStaysTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homeStaysTitlePersuasion);
		when(persuasionUtil.buildHomeStaysSubTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homestaysSubTitlePersuasion);
		searchHotelsResponseTransformerPWA.addPersuasionsForHiddenGemCard(searchWrapperHotelEntity);
		Assert.assertNotNull(searchWrapperHotelEntity.getHotelPersuasions());
		Assert.assertTrue(searchWrapperHotelEntity.getHotelPersuasions() instanceof Map<?, ?>);
		Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdPWA()));
		Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdPWA()));
		Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdPWA()));
		Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdPWA()));
	}

	@Test
	public void buildPriceDetailForDayUse_withNullDisplayFare_shouldReturnNull() {
		DisplayFare displayFare = null;
		PriceDetail result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "buildPriceDetailForDayUse", displayFare);
		Assert.assertNull(result);
	}

	@Test
	public void buildPriceDetailForDayUse_withValidDisplayFare_shouldReturnPriceDetail() {
		DisplayFare displayFare = new DisplayFare();
		Tax tax = new Tax();
		tax.setValue(100d);
		displayFare.setTax(tax);
		HotelPrice slashedPrice = new HotelPrice();
		slashedPrice.setSellingPriceNoTax(1000);
		slashedPrice.setSellingPriceWithTax(1100);
		displayFare.setSlashedPrice(slashedPrice);

		PriceDetail result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "buildPriceDetailForDayUse", displayFare);

		Assert.assertNotNull(result);
		Assert.assertEquals(100, result.getTotalTax(), 0);
		Assert.assertEquals(1000, result.getPrice(), 0);
		Assert.assertEquals(1100, result.getPriceWithTax(), 0);
		Assert.assertEquals(1000, result.getDiscountedPrice(), 0);
		Assert.assertEquals(1100, result.getDiscountedPriceWithTax(), 0);
	}

	@Test
	public void buildPriceDetailForDayUse_withNullTax_shouldSetTotalTaxToZero() {
		DisplayFare displayFare = new DisplayFare();
		displayFare.setTax(null);
		HotelPrice slashedPrice = new HotelPrice();
		slashedPrice.setSellingPriceNoTax(1000);
		slashedPrice.setSellingPriceWithTax(1100);
		displayFare.setSlashedPrice(slashedPrice);

		PriceDetail result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "buildPriceDetailForDayUse", displayFare);

		Assert.assertNotNull(result);
		Assert.assertEquals(0, result.getTotalTax(), 0);
		Assert.assertEquals(1000, result.getPrice(), 0);
		Assert.assertEquals(1100, result.getPriceWithTax(), 0);
		Assert.assertEquals(1000, result.getDiscountedPrice(), 0);
		Assert.assertEquals(1100, result.getDiscountedPriceWithTax(), 0);
	}

	@Test
	public void buildPriceDetailForDayUse_withNullSlashedPrice_shouldReturnPriceDetailWithDefaultValues() {
		DisplayFare displayFare = new DisplayFare();
		Tax tax = new Tax();
		tax.setValue(100d);
		displayFare.setTax(tax);
		displayFare.setSlashedPrice(null);

		PriceDetail result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "buildPriceDetailForDayUse", displayFare);

		Assert.assertNotNull(result);
		Assert.assertEquals(100, result.getTotalTax(), 0);
		Assert.assertNull(result.getPrice());
		Assert.assertNull(result.getPriceWithTax());
		Assert.assertNull(result.getDiscountedPrice());
		Assert.assertNull(result.getDiscountedPriceWithTax());
	}

	@Test
	public void buildSlotDetails_withNullHotelEntity_shouldReturnNull() {
		List<SlotDetail> result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "buildSlotDetails", null, new HashMap<>(), new ListingSearchRequest());
		Assert.assertNull(result);
	}

	@Test
	public void buildSlotDetails_withEmptyRoomTypeDetails_shouldReturnNull() {
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setRecommendedRoomTypeDetails(Collections.emptyList());
		List<SlotDetail> result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "buildSlotDetails", hotelEntity, new HashMap<>(), new ListingSearchRequest());
		Assert.assertNull(result);
	}

//	@Test
//	public void buildSlotDetails_withValidRoomTypeDetails_shouldReturnSlotDetails() {
//		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
//		RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
//		com.mmt.hotels.model.response.dayuse.Slot slot = new com.mmt.hotels.model.response.dayuse.Slot();
//		slot.setDuration(2);
//		roomTypeDetails.setSlot(slot);
//		hotelEntity.setRecommendedRoomTypeDetails(Collections.singletonList(roomTypeDetails));
//		ListingSearchRequest searchRequest = new ListingSearchRequest();
//		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
//		com.mmt.hotels.clientgateway.request.dayuse.Slot searchSlot = new com.mmt.hotels.clientgateway.request.dayuse.Slot();
//		searchSlot.setDuration(2);
//		searchCriteria.setSlot(searchSlot);
//		searchRequest.setSearchCriteria(searchCriteria);
//
//		List<SlotDetail> result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "buildSlotDetails", hotelEntity, new HashMap<>(), searchRequest);
//
//		Assert.assertNotNull(result);
//		Assert.assertFalse(result.isEmpty());
//	}

	@Test
	public void buildSlotDetails_withMismatchedSlotDuration_shouldReturnEmptyList() {
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
		com.mmt.hotels.model.response.dayuse.Slot slot = new com.mmt.hotels.model.response.dayuse.Slot();
		slot.setDuration(3);
		roomTypeDetails.setSlot(slot);
		hotelEntity.setRecommendedRoomTypeDetails(Collections.singletonList(roomTypeDetails));
		ListingSearchRequest searchRequest = new ListingSearchRequest();

		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		com.mmt.hotels.clientgateway.request.dayuse.Slot searchSlot = new com.mmt.hotels.clientgateway.request.dayuse.Slot();
		searchSlot.setDuration(2);
		searchCriteria.setSlot(searchSlot);
		searchRequest.setSearchCriteria(searchCriteria);

		List<SlotDetail> result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "buildSlotDetails", hotelEntity, new HashMap<>(), searchRequest);

		Assert.assertNotNull(result);
		Assert.assertTrue(result.isEmpty());
	}

	@Test
	public void getMybizSimilarHotelsFeatures_withValidUrls_shouldReturnSectionFeatures() {
		List<SectionFeature> result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "getMybizSimilarHotelsFeatures");
		Assert.assertNotNull(result);
		Assert.assertEquals(3, result.size());
//		Assert.assertEquals(highRatedUrl, result.get(0).getIconUrl());
//		Assert.assertEquals(gstInvoiceUrl, result.get(1).getIconUrl());
//		Assert.assertEquals(bpgUrl, result.get(2).getIconUrl());
	}

	@Test
	public void getMybizSimilarHotelsFeatures_withTranslatedData_shouldReturnSectionFeaturesWithTranslatedText() {
		Mockito.when(polyglotService.getTranslatedData("RATED_HIGH_BT")).thenReturn("High Rated");
		Mockito.when(polyglotService.getTranslatedData("GST_INVOICE_ASSURANCE_TEXT")).thenReturn("GST Invoice Assurance");
		Mockito.when(polyglotService.getTranslatedData("BPG_TEXT")).thenReturn("Best Price Guarantee");

		List<SectionFeature> result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "getMybizSimilarHotelsFeatures");

		Assert.assertNotNull(result);
//		Assert.assertEquals("High Rated", result.get(0).getName());
//		Assert.assertEquals("GST Invoice Assurance", result.get(1).getName());
//		Assert.assertEquals("Best Price Guarantee", result.get(2).getName());
	}

	@Test
	public void buildSoldOutInfo_withNullSoldOutInfo_shouldReturnNull() {
		SoldOutInfoCG result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "buildSoldOutInfo", (SoldOutInfo) null);
		Assert.assertNull(result);
	}

	@Test
	public void buildSoldOutInfo_withValidSoldOutInfo_shouldReturnSoldOutInfoCG() {
		SoldOutInfo soldOutInfo = new SoldOutInfo();
		soldOutInfo.setSoldOutText("Sold Out");
		soldOutInfo.setSoldOutSubText("No rooms available");
		soldOutInfo.setSoldOutReason("High demand");
		soldOutInfo.setSoldOutType("Temporary");

		SoldOutInfoCG result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "buildSoldOutInfo", soldOutInfo);

		Assert.assertNotNull(result);
		Assert.assertEquals("Sold Out", result.getSoldOutText());
		Assert.assertEquals("No rooms available", result.getSoldOutSubText());
		Assert.assertEquals("High demand", result.getSoldOutReason());
		Assert.assertEquals("Temporary", result.getSoldOutType());
	}

	@Test
	public void buildCouponReturnsNullWhenCouponInfoIsNull() {
		BestCoupon couponInfo = null;

		Coupon result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "buildCoupon", couponInfo);

		Assert.assertNull(result);
	}

	@Test
	public void buildCouponReturnsCouponWithCorrectValuesWhenCouponInfoIsNotNull() {
		BestCoupon couponInfo = new BestCoupon();
		couponInfo.setDescription("10% off");
		couponInfo.setCouponCode("SAVE10");
		couponInfo.setType("Percentage");
		couponInfo.setDiscountAmount(10.0);
		couponInfo.setSpecialPromoCoupon(true);
		couponInfo.setPromoIconLink("http://example.com/icon.png");

		Coupon result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "buildCoupon", couponInfo);

		Assert.assertNotNull(result);
		Assert.assertEquals("10% off", result.getDescription());
		Assert.assertEquals("SAVE10", result.getCode());
		Assert.assertEquals("Percentage", result.getType());
		Assert.assertEquals(10.0, result.getCouponAmount(), 0.0);
		Assert.assertEquals("http://example.com/icon.png", result.getPromoIcon());
	}

	@Test
	public void buildCouponReturnsCouponWithGenericBankIconWhenPromoIconLinkIsEmpty() {
		BestCoupon couponInfo = new BestCoupon();
		couponInfo.setDescription("10% off");
		couponInfo.setCouponCode("SAVE10");
		couponInfo.setType("Percentage");
		couponInfo.setDiscountAmount(10.0);
		couponInfo.setSpecialPromoCoupon(true);
		couponInfo.setPromoIconLink("");

		Coupon result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "buildCoupon", couponInfo);

		Assert.assertNotNull(result);
		Assert.assertEquals("10% off", result.getDescription());
		Assert.assertEquals("SAVE10", result.getCode());
		Assert.assertEquals("Percentage", result.getType());
		Assert.assertEquals(10.0, result.getCouponAmount(), 0.0);
	}

	@Test
	public void buildCouponReturnsCouponWithEmptyValuesWhenCouponInfoHasEmptyFields() {
		BestCoupon couponInfo = new BestCoupon();
		couponInfo.setDescription("");
		couponInfo.setCouponCode("");
		couponInfo.setType("");
		couponInfo.setDiscountAmount(0.0);
		couponInfo.setSpecialPromoCoupon(false);
		couponInfo.setPromoIconLink("");

		Coupon result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerPWA, "buildCoupon", couponInfo);

		Assert.assertNotNull(result);
		Assert.assertEquals("", result.getDescription());
		Assert.assertEquals("", result.getCode());
		Assert.assertEquals("", result.getType());
		Assert.assertEquals(0.0, result.getCouponAmount(), 0.0);
	}


	@Test
	public void testConvertSearchHotelsResponse1() {
		// Arrange
		when(listingPageResponseBO.getPersonalizedResponse()).thenReturn(null);

		// Act
		SearchHotelsResponse response = searchHotelsResponseTransformerIOS.convertSearchHotelsResponse(listingPageResponseBO, searchHotelsRequest, commonModifierResponse);

		// Assert
		assertNotNull(response);
//		assertNull(response.getExpData());
	}
}