package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailsResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailsResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailsResponseTransformerPWA;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class OrchStaticDetailFactoryTest {

    @InjectMocks
    private OrchStaticDetailFactory orchStaticDetailFactory;

    @Mock
    private OrchStaticDetailsResponseTransformerAndroid mockAndroidTransformer;

    @Mock
    private OrchStaticDetailsResponseTransformerPWA mockPwaTransformer;

    @Mock
    private OrchStaticDetailsResponseTransformerIOS mockIosTransformer;

    @Mock
    private OrchStaticDetailsResponseTransformerDesktop mockDesktopTransformer;

    @BeforeEach
    void setUp() {
        // Inject mock dependencies using ReflectionTestUtils
        ReflectionTestUtils.setField(orchStaticDetailFactory, "orchStaticDetailsResponseTransformerAndroid", mockAndroidTransformer);
        ReflectionTestUtils.setField(orchStaticDetailFactory, "orchStaticDetailsResponseTransformerPwa", mockPwaTransformer);
        ReflectionTestUtils.setField(orchStaticDetailFactory, "orchStaticDetailsResponseTransformerIOS", mockIosTransformer);
        ReflectionTestUtils.setField(orchStaticDetailFactory, "orchStaticDetailsResponseTransformerDesktop", mockDesktopTransformer);
    }

    // =============================================================================
    // Tests for getResponseService method with different client types
    // =============================================================================

    @Test
    void testGetResponseService_WithAndroidClient() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("ANDROID");

        // Then
        assertNotNull(result);
        assertEquals(mockAndroidTransformer, result);
    }

    @Test
    void testGetResponseService_WithIOSClient() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("IOS");

        // Then
        assertNotNull(result);
        assertEquals(mockIosTransformer, result);
    }

    @Test
    void testGetResponseService_WithDesktopClient() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("DESKTOP");

        // Then
        assertNotNull(result);
        assertEquals(mockDesktopTransformer, result);
    }

    @Test
    void testGetResponseService_WithPWAClient() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("PWA");

        // Then
        assertNotNull(result);
        assertEquals(mockPwaTransformer, result);
    }

    @Test
    void testGetResponseService_WithMSITEClient() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("MSITE");

        // Then
        assertNotNull(result);
        assertEquals(mockPwaTransformer, result);
    }

    // =============================================================================
    // Tests for null and empty client inputs (should return desktop as default)
    // =============================================================================

    @Test
    void testGetResponseService_WithNullClient() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService(null);

        // Then
        assertNotNull(result);
        assertEquals(mockDesktopTransformer, result);
    }

    @Test
    void testGetResponseService_WithEmptyStringClient() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("");

        // Then
        assertNotNull(result);
        assertEquals(mockDesktopTransformer, result);
    }

    @Test
    void testGetResponseService_WithBlankStringClient() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("   ");

        // Then
        assertNotNull(result);
        assertEquals(mockDesktopTransformer, result);
    }

    // =============================================================================
    // Tests for unknown/invalid client types (should return desktop as default)
    // =============================================================================

    @Test
    void testGetResponseService_WithUnknownClient() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("UNKNOWN");

        // Then
        assertNotNull(result);
        assertEquals(mockDesktopTransformer, result);
    }

    @Test
    void testGetResponseService_WithInvalidClient() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("INVALID_CLIENT");

        // Then
        assertNotNull(result);
        assertEquals(mockDesktopTransformer, result);
    }

    @Test
    void testGetResponseService_WithRandomStringClient() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("RandomString123");

        // Then
        assertNotNull(result);
        assertEquals(mockDesktopTransformer, result);
    }

    @Test
    void testGetResponseService_WithNumericStringClient() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("12345");

        // Then
        assertNotNull(result);
        assertEquals(mockDesktopTransformer, result);
    }

    @Test
    void testGetResponseService_WithSpecialCharactersClient() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("@#$%^&*()");

        // Then
        assertNotNull(result);
        assertEquals(mockDesktopTransformer, result);
    }

    // =============================================================================
    // Tests for case sensitivity
    // =============================================================================

    @Test
    void testGetResponseService_WithLowerCaseAndroid() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("android");

        // Then
        assertNotNull(result);
        assertEquals(mockDesktopTransformer, result); // Should return desktop as default for non-exact match
    }

    @Test
    void testGetResponseService_WithLowerCaseIOS() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("ios");

        // Then
        assertNotNull(result);
        assertEquals(mockDesktopTransformer, result); // Should return desktop as default for non-exact match
    }

    @Test
    void testGetResponseService_WithLowerCaseDesktop() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("desktop");

        // Then
        assertNotNull(result);
        assertEquals(mockDesktopTransformer, result); // Should return desktop as default for non-exact match
    }

    @Test
    void testGetResponseService_WithLowerCasePWA() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("pwa");

        // Then
        assertNotNull(result);
        assertEquals(mockDesktopTransformer, result); // Should return desktop as default for non-exact match
    }

    @Test
    void testGetResponseService_WithMixedCaseClient() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("AnDrOiD");

        // Then
        assertNotNull(result);
        assertEquals(mockDesktopTransformer, result); // Should return desktop as default for non-exact match
    }

    // =============================================================================
    // Tests for edge cases with whitespace
    // =============================================================================

    @Test
    void testGetResponseService_WithClientWithLeadingSpaces() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("  ANDROID");

        // Then
        assertNotNull(result);
        assertEquals(mockDesktopTransformer, result); // Should return desktop as default for non-exact match
    }

    @Test
    void testGetResponseService_WithClientWithTrailingSpaces() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("ANDROID  ");

        // Then
        assertNotNull(result);
        assertEquals(mockDesktopTransformer, result); // Should return desktop as default for non-exact match
    }

    @Test
    void testGetResponseService_WithClientWithSurroundingSpaces() {
        // When
        OrchStaticDetailResponseTransformer result = orchStaticDetailFactory.getResponseService("  PWA  ");

        // Then
        assertNotNull(result);
        assertEquals(mockDesktopTransformer, result); // Should return desktop as default for non-exact match
    }

    // =============================================================================
    // Tests to verify all possible execution paths
    // =============================================================================

    @Test
    void testGetResponseService_VerifyAllValidClients() {
        // Test all valid client types in sequence to ensure each path is covered
        
        // Test ANDROID
        OrchStaticDetailResponseTransformer androidResult = orchStaticDetailFactory.getResponseService("ANDROID");
        assertEquals(mockAndroidTransformer, androidResult);
        
        // Test IOS
        OrchStaticDetailResponseTransformer iosResult = orchStaticDetailFactory.getResponseService("IOS");
        assertEquals(mockIosTransformer, iosResult);
        
        // Test DESKTOP
        OrchStaticDetailResponseTransformer desktopResult = orchStaticDetailFactory.getResponseService("DESKTOP");
        assertEquals(mockDesktopTransformer, desktopResult);
        
        // Test PWA
        OrchStaticDetailResponseTransformer pwaResult = orchStaticDetailFactory.getResponseService("PWA");
        assertEquals(mockPwaTransformer, pwaResult);
        
        // Test MSITE
        OrchStaticDetailResponseTransformer msiteResult = orchStaticDetailFactory.getResponseService("MSITE");
        assertEquals(mockPwaTransformer, msiteResult);
    }

    @Test
    void testGetResponseService_VerifyDefaultPaths() {
        // Test all scenarios that should return desktop transformer as default
        
        // Null input
        assertEquals(mockDesktopTransformer, orchStaticDetailFactory.getResponseService(null));
        
        // Empty input
        assertEquals(mockDesktopTransformer, orchStaticDetailFactory.getResponseService(""));
        
        // Unknown input
        assertEquals(mockDesktopTransformer, orchStaticDetailFactory.getResponseService("UNKNOWN"));
    }

    // =============================================================================
    // Tests to verify dependency injection and object state
    // =============================================================================

    @Test
    void testFactoryDependencies_AreNotNull() {
        // Verify that all dependencies are properly injected (not null)
        OrchStaticDetailResponseTransformer androidTransformer = 
            (OrchStaticDetailResponseTransformer) ReflectionTestUtils.getField(orchStaticDetailFactory, "orchStaticDetailsResponseTransformerAndroid");
        OrchStaticDetailResponseTransformer pwaTransformer = 
            (OrchStaticDetailResponseTransformer) ReflectionTestUtils.getField(orchStaticDetailFactory, "orchStaticDetailsResponseTransformerPwa");
        OrchStaticDetailResponseTransformer iosTransformer = 
            (OrchStaticDetailResponseTransformer) ReflectionTestUtils.getField(orchStaticDetailFactory, "orchStaticDetailsResponseTransformerIOS");
        OrchStaticDetailResponseTransformer desktopTransformer = 
            (OrchStaticDetailResponseTransformer) ReflectionTestUtils.getField(orchStaticDetailFactory, "orchStaticDetailsResponseTransformerDesktop");
        
        assertNotNull(androidTransformer);
        assertNotNull(pwaTransformer);
        assertNotNull(iosTransformer);
        assertNotNull(desktopTransformer);
    }

    @Test
    void testFactoryDependencies_AreCorrectInstances() {
        // Verify that the correct mock instances are injected
        assertEquals(mockAndroidTransformer, ReflectionTestUtils.getField(orchStaticDetailFactory, "orchStaticDetailsResponseTransformerAndroid"));
        assertEquals(mockPwaTransformer, ReflectionTestUtils.getField(orchStaticDetailFactory, "orchStaticDetailsResponseTransformerPwa"));
        assertEquals(mockIosTransformer, ReflectionTestUtils.getField(orchStaticDetailFactory, "orchStaticDetailsResponseTransformerIOS"));
        assertEquals(mockDesktopTransformer, ReflectionTestUtils.getField(orchStaticDetailFactory, "orchStaticDetailsResponseTransformerDesktop"));
    }

    // =============================================================================
    // Performance and behavior tests
    // =============================================================================

    @Test
    void testGetResponseService_MultipleCallsSameClient() {
        // Test that multiple calls with same client return the same instance
        OrchStaticDetailResponseTransformer result1 = orchStaticDetailFactory.getResponseService("ANDROID");
        OrchStaticDetailResponseTransformer result2 = orchStaticDetailFactory.getResponseService("ANDROID");
        OrchStaticDetailResponseTransformer result3 = orchStaticDetailFactory.getResponseService("ANDROID");
        
        assertEquals(result1, result2);
        assertEquals(result2, result3);
        assertEquals(mockAndroidTransformer, result1);
    }

    @Test
    void testGetResponseService_DifferentClientsReturnDifferentInstances() {
        // Test that different clients return different transformer instances
        OrchStaticDetailResponseTransformer androidResult = orchStaticDetailFactory.getResponseService("ANDROID");
        OrchStaticDetailResponseTransformer iosResult = orchStaticDetailFactory.getResponseService("IOS");
        OrchStaticDetailResponseTransformer pwaResult = orchStaticDetailFactory.getResponseService("PWA");
        OrchStaticDetailResponseTransformer desktopResult = orchStaticDetailFactory.getResponseService("DESKTOP");
        
        assertNotEquals(androidResult, iosResult);
        assertNotEquals(iosResult, pwaResult);
        assertNotEquals(pwaResult, desktopResult);
        assertNotEquals(androidResult, desktopResult);
    }

    @Test
    void testGetResponseService_MSITEAndPWAReturnSameInstance() {
        // Test that both MSITE and PWA return the same PWA transformer instance
        OrchStaticDetailResponseTransformer msiteResult = orchStaticDetailFactory.getResponseService("MSITE");
        OrchStaticDetailResponseTransformer pwaResult = orchStaticDetailFactory.getResponseService("PWA");
        
        assertEquals(msiteResult, pwaResult);
        assertEquals(mockPwaTransformer, msiteResult);
        assertEquals(mockPwaTransformer, pwaResult);
    }
} 