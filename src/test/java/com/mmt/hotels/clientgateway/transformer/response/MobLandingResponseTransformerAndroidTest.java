package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.Persuasions;
import com.mmt.hotels.clientgateway.request.MobLandingRequest;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.response.moblanding.QuestionCG;
import com.mmt.hotels.clientgateway.response.moblanding.SupportDetails;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.transformer.response.android.MobLandingResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.pwa.SearchHotelsResponseTransformerPWA;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.HomeStayDetails;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.pojo.matchmaker.LatLongAndBounds;
import com.mmt.hotels.pojo.matchmaker.WikiQuestion;
import junit.framework.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MobLandingResponseTransformerAndroidTest {
    @InjectMocks
    MobLandingResponseTransformerAndroid mobLandingResponseTransformerAndroid;

    @Mock
    SearchHotelsResponseTransformerPWA searchHotelsResponseTransformerPWA;

    @Mock
    SearchHotelsFactory searchHotelsFactory;

    @Spy
    ObjectMapperUtil objectMapperUtil;

    @Spy
    CommonResponseTransformer commonResponseTransformer;

    @Mock
    PersuasionUtil persuasionUtil;

    @Mock
    DateUtil dateUtil;

    @Mock
    Utility utility;

    @Test
    public void addLocationPersuasionToHotelPersuasionsTest() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        List<String> locationPersuasion = new ArrayList<>();
        searchWrapperHotelEntity.setLocationPersuasion(locationPersuasion);
        mobLandingResponseTransformerAndroid.addLocationPersuasionToHotelPersuasions(searchWrapperHotelEntity);
        locationPersuasion.add("test1");
        searchWrapperHotelEntity.setLocationPersuasion(locationPersuasion);
        mobLandingResponseTransformerAndroid.addLocationPersuasionToHotelPersuasions(searchWrapperHotelEntity);
        locationPersuasion.add("test2");
        mobLandingResponseTransformerAndroid.addLocationPersuasionToHotelPersuasions(searchWrapperHotelEntity);
    }

    @Test
    public void addPersuasionsForHiddenGemCardTest() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        searchWrapperHotelEntity.setHotelPersuasions(new HashMap<>());
        searchWrapperHotelEntity.setHiddenGem(true);
        searchWrapperHotelEntity.setHiddenGemPersuasionText("Hidden Gem Persuasion Text");
        HomeStayDetails homeStayDetails = new HomeStayDetails();
        homeStayDetails.setStayType(Constants.STAY_TYPE_HOMESTAY);
        homeStayDetails.setStayTypeInfo("Home Stay Info Test");

        PersuasionObject homeStaysTitlePersuasion = new PersuasionObject();
        List<PersuasionData> homeStaysTitlePersuasionList = new ArrayList<>();
        homeStaysTitlePersuasionList.add(new PersuasionData());
        homeStaysTitlePersuasion.setData(homeStaysTitlePersuasionList);

        PersuasionObject homestaysSubTitlePersuasion = new PersuasionObject();
        homestaysSubTitlePersuasion.setData(Arrays.asList(new PersuasionData()));

        when(persuasionUtil.buildHiddenGemPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
        when(persuasionUtil.buildHiddenGemIconPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
        when(persuasionUtil.buildHomeStaysTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homeStaysTitlePersuasion);
        when(persuasionUtil.buildHomeStaysSubTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homestaysSubTitlePersuasion);
        mobLandingResponseTransformerAndroid.addPersuasionsForHiddenGemCard(searchWrapperHotelEntity);
        Assert.assertNotNull(searchWrapperHotelEntity.getHotelPersuasions());
        Assert.assertTrue(searchWrapperHotelEntity.getHotelPersuasions() instanceof Map<?, ?>);
        Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdApps()));
        Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdApps()));
        Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdApps()));
        Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdApps()));
    }

    @Test
    public void testAddPivotAndZoomLevelForCityMap(){
        QuestionCG questionCG = new QuestionCG();
        WikiQuestion wikiQuestion = new WikiQuestion();
        LatLongAndBounds deskPivot = new LatLongAndBounds();
        deskPivot.setLat("123.4");
        deskPivot.setLng("234.5");
        wikiQuestion.setDeskPivot(deskPivot);
        wikiQuestion.setDeskZoomLevel(1);
        LatLongAndBounds pivot = new LatLongAndBounds();
        pivot.setLat("345.6");
        pivot.setLng("456.7");
        wikiQuestion.setPivot(pivot);
        wikiQuestion.setZoomLevel(2);
        mobLandingResponseTransformerAndroid.addPivotAndZoomLevelForCityMap(questionCG,wikiQuestion);
        Assert.assertEquals(questionCG.getPivot().getLat(),pivot.getLat());
        Assert.assertEquals(questionCG.getZoomLevel(),wikiQuestion.getZoomLevel());
    }

    @Test
    public void buildFormUrlForMobLandingTest(){
        MobLandingRequest mobLandingRequest = new MobLandingRequest();
        SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
        searchCriteria.setLocationId("XTZ");
        searchCriteria.setLocationType("city");
        searchCriteria.setCurrency("INR");
        searchCriteria.setCheckIn("2023-11-23");
        searchCriteria.setCheckOut("2023-11-24");
        List<RoomStayCandidate> list = new ArrayList<>();
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setAdultCount(2);
        roomStayCandidate.setRooms(2);
        list.add(roomStayCandidate);
        searchCriteria.setRoomStayCandidates(list);
        mobLandingRequest.setSearchCriteria(searchCriteria);
        SupportDetails supportDetails = new SupportDetails();
        supportDetails.setFormUrl("https://www.makemytrip.com/hotels/group-booking/?checkin={0}&checkout={1}&city={2}&country={3}&locusId={4}&locusType={5}&rsc={6}&_uCurrency={7}&appVersion={8}&deviceId={9}&bookingDevice={10}&deviceType={11}&visitorId={12}&visitNumber={13}&funnelSource={14}&idContext={15}&funnelName={16}");
        String res = ReflectionTestUtils.invokeMethod(mobLandingResponseTransformerAndroid,"buildFormUrlForMobLanding",mobLandingRequest,supportDetails);
        Assert.assertEquals("https://www.makemytrip.com/hotels/group-booking/?checkin=null&checkout=null&city=&country=IN&locusId=XTZ&locusType=city&rsc=null&_uCurrency=INR&appVersion=&deviceId=&bookingDevice=&deviceType=&visitorId=&visitNumber=1&funnelSource=&idContext=&funnelName=",res);
    }
}
