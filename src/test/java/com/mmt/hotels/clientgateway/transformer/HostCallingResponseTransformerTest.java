package com.mmt.hotels.clientgateway.transformer;

import com.mmt.hotels.clientgateway.response.hostcalling.HostCallingInitiateResponse;
import com.mmt.hotels.clientgateway.transformer.response.android.HostCallingResponseTransformerAndroid;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class HostCallingResponseTransformerTest {

    @InjectMocks
    private HostCallingResponseTransformerAndroid hostCallingResponseTransformer;

    private com.mmt.hotels.pojo.response.detail.HostCallingInitiateResponse mockHESResponse;
    private String clientType;
    private String visitorId;

    @Before
    public void setUp() {
        // Setup mock HES response
        mockHESResponse = new com.mmt.hotels.pojo.response.detail.HostCallingInitiateResponse();
        mockHESResponse.setStatus("success");
        mockHESResponse.setRequestId("REQ123");
        mockHESResponse.setChainName("Test Chain");
        mockHESResponse.setAvailableNow(true);
        mockHESResponse.setStartTime("09:00");
        mockHESResponse.setEndTime("21:00");

        clientType = "ANDROID";
        visitorId = "VIS123";
    }

    @Test
    public void should_TransformHESResponseToCGResponse_When_ValidResponse() {
        // Act
        HostCallingInitiateResponse result = 
            hostCallingResponseTransformer.convertHostCallingResponse(mockHESResponse, clientType, visitorId);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.getStatus());
        assertEquals("REQ123", result.getRequestId());
        assertEquals("Test Chain", result.getChainName());
        assertEquals(true, result.getAvailableNow());
        assertEquals("09:00", result.getStartTime());
        assertEquals("21:00", result.getEndTime());
    }

    @Test
    public void should_ReturnErrorResponse_When_HESResponseIsNull() {
        // Act
        HostCallingInitiateResponse result = 
            hostCallingResponseTransformer.convertHostCallingResponse(null, clientType, visitorId);

        // Assert
        assertNotNull(result);
        assertEquals("error", result.getStatus());
        assertEquals(visitorId, result.getRequestId());
        assertEquals(false, result.getAvailableNow());
    }

    @Test
    public void should_HandleMissingChainName_When_ChainNameIsNull() {
        // Arrange
        mockHESResponse.setChainName(null);

        // Act
        HostCallingInitiateResponse result = 
            hostCallingResponseTransformer.convertHostCallingResponse(mockHESResponse, clientType, visitorId);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.getStatus());
        assertNull(result.getChainName());
    }

    @Test
    public void should_HandleEmptyChainName_When_ChainNameIsEmpty() {
        // Arrange
        mockHESResponse.setChainName("");

        // Act
        HostCallingInitiateResponse result = 
            hostCallingResponseTransformer.convertHostCallingResponse(mockHESResponse, clientType, visitorId);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.getStatus());
        assertEquals("", result.getChainName());
    }

    @Test
    public void should_HandleNullAvailability_When_AvailableNowIsNull() {
        // Arrange
        mockHESResponse.setAvailableNow(null);

        // Act
        HostCallingInitiateResponse result = 
            hostCallingResponseTransformer.convertHostCallingResponse(mockHESResponse, clientType, visitorId);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.getStatus());
        assertNull(result.getAvailableNow());
    }

    @Test
    public void should_HandleDifferentClientTypes_When_TransformingResponse() {
        // Test different client types - the transformer should work for all
        String[] clients = {"PWA", "ANDROID", "IOS", "DESKTOP", "MSITE"};

        for (String client : clients) {
            // Act
            HostCallingInitiateResponse result = 
                hostCallingResponseTransformer.convertHostCallingResponse(mockHESResponse, client, visitorId);

            // Assert
            assertNotNull(result);
            assertEquals("success", result.getStatus());
            assertEquals("Test Chain", result.getChainName());
        }
    }

    @Test
    public void should_HandleMissingTimings_When_StartTimeOrEndTimeIsNull() {
        // Arrange
        mockHESResponse.setStartTime(null);
        mockHESResponse.setEndTime(null);

        // Act
        HostCallingInitiateResponse result = 
            hostCallingResponseTransformer.convertHostCallingResponse(mockHESResponse, clientType, visitorId);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.getStatus());
        assertNull(result.getStartTime());
        assertNull(result.getEndTime());
    }

    @Test
    public void should_HandleUnavailableStatus_When_AvailableNowIsFalse() {
        // Arrange
        mockHESResponse.setAvailableNow(false);
        mockHESResponse.setStatus("unavailable");

        // Act
        HostCallingInitiateResponse result = 
            hostCallingResponseTransformer.convertHostCallingResponse(mockHESResponse, clientType, visitorId);

        // Assert
        assertNotNull(result);
        assertEquals("unavailable", result.getStatus());
        assertEquals(false, result.getAvailableNow());
        assertEquals("Test Chain", result.getChainName());
    }

    @Test
    public void should_HandleNullVisitorId_When_VisitorIdIsNull() {
        // Act
        HostCallingInitiateResponse result = 
            hostCallingResponseTransformer.convertHostCallingResponse(mockHESResponse, clientType, null);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.getStatus());
        assertEquals("REQ123", result.getRequestId());
    }

    @Test
    public void should_HandleNullClientType_When_ClientTypeIsNull() {
        // Act
        HostCallingInitiateResponse result = 
            hostCallingResponseTransformer.convertHostCallingResponse(mockHESResponse, null, visitorId);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.getStatus());
        assertEquals("REQ123", result.getRequestId());
    }

    @Test
    public void should_HandleFactoryPatternUsage_When_DifferentTransformersUsed() {
        // This test verifies that the transformer works with the factory pattern
        // The specific client logic would be in subclasses of the base transformer
        
        // Act
        HostCallingInitiateResponse result = 
            hostCallingResponseTransformer.convertHostCallingResponse(mockHESResponse, clientType, visitorId);

        // Assert - Basic transformation should work for all client types
        assertNotNull(result);
        assertEquals("success", result.getStatus());
        assertEquals("Test Chain", result.getChainName());
        assertEquals("REQ123", result.getRequestId());
    }
} 