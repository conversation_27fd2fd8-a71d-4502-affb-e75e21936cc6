package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.SearchRoomsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.SearchRoomsRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.SearchRoomsRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.SearchRoomsRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.SearchRoomsRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.SearchRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.SearchRoomsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.SearchRoomsResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.SearchRoomsResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.SearchRoomsResponseTransformerPWA;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class SearchRoomsFactoryTest {


    @InjectMocks
    SearchRoomsFactory searchRoomsFactory;

    @Before
    public  void init(){
        ReflectionTestUtils.setField(searchRoomsFactory,"searchRoomsResponseTransformerPWA" , new SearchRoomsResponseTransformerPWA());
        ReflectionTestUtils.setField(searchRoomsFactory,"searchRoomsResponseTransformerDesktop" , new SearchRoomsResponseTransformerDesktop());
        ReflectionTestUtils.setField(searchRoomsFactory,"searchRoomsResponseTransformerAndroid" , new SearchRoomsResponseTransformerAndroid());
        ReflectionTestUtils.setField(searchRoomsFactory,"searchRoomsResponseTransformerIOS" , new SearchRoomsResponseTransformerIOS());
        ReflectionTestUtils.setField(searchRoomsFactory,"searchRoomsRequestTransformerPWA" , new SearchRoomsRequestTransformerPWA());
        ReflectionTestUtils.setField(searchRoomsFactory,"searchRoomsRequestTransformerDesktop" , new SearchRoomsRequestTransformerDesktop());
        ReflectionTestUtils.setField(searchRoomsFactory,"searchRoomsRequestTransformerAndroid" , new SearchRoomsRequestTransformerAndroid());
        ReflectionTestUtils.setField(searchRoomsFactory,"searchRoomsRequestTransformerIOS" , new SearchRoomsRequestTransformerIOS());
        
    }
    
    @Test
    public void getRequestServiceTest(){
        SearchRoomsRequestTransformer resp = searchRoomsFactory.getRequestService("PWA");
        Assert.assertTrue(resp instanceof SearchRoomsRequestTransformerPWA  );
        resp = searchRoomsFactory.getRequestService("DESKTOP");
        Assert.assertTrue(resp instanceof SearchRoomsRequestTransformerDesktop  );
        resp = searchRoomsFactory.getRequestService("ANDROID");
        Assert.assertTrue(resp instanceof SearchRoomsRequestTransformerAndroid  );
        resp = searchRoomsFactory.getRequestService("IOS");
        Assert.assertTrue(resp instanceof SearchRoomsRequestTransformerIOS  );
        resp = searchRoomsFactory.getRequestService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(searchRoomsFactory.getRequestService("test") instanceof  SearchRoomsRequestTransformerDesktop);
    }

    @Test
    public void getResponseServiceTest(){
        SearchRoomsResponseTransformer resp = searchRoomsFactory.getResponseService("PWA");
        Assert.assertTrue(resp instanceof SearchRoomsResponseTransformerPWA  );
        resp = searchRoomsFactory.getResponseService("DESKTOP");
        Assert.assertTrue(resp instanceof SearchRoomsResponseTransformerDesktop  );
        resp = searchRoomsFactory.getResponseService("ANDROID");
        Assert.assertTrue(resp instanceof SearchRoomsResponseTransformerAndroid  );
        resp = searchRoomsFactory.getResponseService("IOS");
        Assert.assertTrue(resp instanceof SearchRoomsResponseTransformerIOS  );
        resp = searchRoomsFactory.getResponseService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(searchRoomsFactory.getResponseService("test") instanceof  SearchRoomsResponseTransformerDesktop);
    }

}
