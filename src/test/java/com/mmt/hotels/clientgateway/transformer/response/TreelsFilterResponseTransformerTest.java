package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.request.TreelsFilterCountRequest;
import com.mmt.hotels.clientgateway.response.filter.FilterResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.FilterSearchMetaDataResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class TreelsFilterResponseTransformerTest {
    @InjectMocks
    private TreelsFilterResponseTransformer treelsFilterResponseTransformer;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    private CommonConfigHelper commonConfigHelper;

    @Test
    public void convertFilterResponseTest() {
        FilterSearchMetaDataResponse filterSearchMetaDataResponse = new FilterSearchMetaDataResponse();
        filterSearchMetaDataResponse.setFilterDataMap(new HashMap<>());
        List<Filter> filterList = new ArrayList<>();
        filterList.add(new Filter());
        filterList.get(0).setFilterGroup(FilterGroup.TAGS);
        filterList.get(0).setFilterValue("test");
        filterSearchMetaDataResponse.getFilterDataMap().put(FilterGroup.TAGS, filterList);
        FilterResponse filterResponse = treelsFilterResponseTransformer.convertFilterResponse(filterSearchMetaDataResponse, new TreelsFilterCountRequest());
        Assert.assertEquals(1, filterResponse.getFilterList().size());
        Assert.assertEquals(1, filterResponse.getFilterList().get(0).getFilters().size());
        Assert.assertEquals("flex", filterResponse.getFilterList().get(0).getViewType());
        Assert.assertEquals("test", filterResponse.getFilterList().get(0).getFilters().get(0).getFilterValue());
    }

    @Test
    public void convertFilterResponseNPETest() {
        FilterSearchMetaDataResponse filterSearchMetaDataResponse = new FilterSearchMetaDataResponse();
        TreelsFilterCountRequest treelsFilterCountRequest = new TreelsFilterCountRequest();
        treelsFilterResponseTransformer.convertFilterResponse(filterSearchMetaDataResponse, treelsFilterCountRequest);
        filterSearchMetaDataResponse.setFilterDataMap(new HashMap<>());
        treelsFilterCountRequest.setFilterRemovedCriteria(new ArrayList<>());
        treelsFilterResponseTransformer.convertFilterResponse(filterSearchMetaDataResponse, treelsFilterCountRequest);
    }

    @Test
    public void convertFilterExclusionResponseTest() {
        Assert.assertNull(treelsFilterResponseTransformer.convertFilterResponse(new FilterSearchMetaDataResponse(), new TreelsFilterCountRequest()));
        FilterSearchMetaDataResponse filterSearchMetaDataResponse = new FilterSearchMetaDataResponse();
        filterSearchMetaDataResponse.setFilterDataMap(new HashMap<>());
        List<Filter> filterList = new ArrayList<>();
        Filter filter = new Filter();
        filter.setFilterGroup(FilterGroup.TAGS);
        filter.setFilterValue("test");
        filterList.add(filter);
        Filter filter1 = new Filter();
        filter1.setFilterGroup(FilterGroup.TAGS);
        filter1.setFilterValue("test1");
        filterList.add(filter1);
        Filter filter2 = new Filter();
        filter2.setFilterGroup(FilterGroup.TAGS);
        filter2.setFilterValue("test2");
        filterList.add(filter2);
        TreelsFilterCountRequest treelsFilterCountRequest = new TreelsFilterCountRequest();
        treelsFilterCountRequest.setFilterRemovedCriteria(new ArrayList<>());
        treelsFilterCountRequest.getFilterRemovedCriteria().add(new com.mmt.hotels.clientgateway.request.Filter());
        treelsFilterCountRequest.getFilterRemovedCriteria().get(0).setFilterGroup(com.mmt.hotels.clientgateway.response.filter.FilterGroup.TAGS);
        treelsFilterCountRequest.getFilterRemovedCriteria().get(0).setFilterValue("test");
        filterSearchMetaDataResponse.getFilterDataMap().put(FilterGroup.TAGS, filterList);
        Mockito.when(commonConfigHelper.getTreelsFilterSize()).thenReturn(6);
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("test");
        FilterResponse filterResponse = treelsFilterResponseTransformer.convertFilterResponse(filterSearchMetaDataResponse, treelsFilterCountRequest);
        Assert.assertEquals(1, filterResponse.getFilterList().size());
        Assert.assertEquals(2, filterResponse.getFilterList().get(0).getFilters().size());
        Assert.assertEquals("test", filterResponse.getFilterList().get(0).getTitle());
        Assert.assertEquals("flex", filterResponse.getFilterList().get(0).getViewType());
        Assert.assertEquals("test1", filterResponse.getFilterList().get(0).getFilters().get(0).getFilterValue());
        Assert.assertEquals("test2", filterResponse.getFilterList().get(0).getFilters().get(1).getFilterValue());
    }
}
