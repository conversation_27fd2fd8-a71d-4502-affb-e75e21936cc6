package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.enums.Persuasions;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.PriceDetail;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard;
import com.mmt.hotels.clientgateway.response.searchHotels.PersonalizedSection;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.response.android.SearchHotelsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.HomeStayDetails;
import com.mmt.hotels.model.request.matchmaker.InputHotel;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.model.response.dayuse.MissingSlotDetail;
import com.mmt.hotels.model.response.dayuse.Slot;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.searchwrapper.ListingPagePersonalizationResponsBO;
import com.mmt.hotels.model.response.searchwrapper.PersonalizedResponse;
import com.mmt.hotels.model.response.searchwrapper.QuickBookInfo;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import org.apache.commons.io.FileUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;


import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.GROUP_PRICE_TEXT_MULTI_NIGHT_ALT_ACCO;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.GROUP_PRICE_TEXT_NEW_DESIGN;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.GROUP_PRICE_TEXT_ONE_NIGHT_ALT_ACCO;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.GROUP_PRICE_TEXT_ONE_NIGHT_ONE_ROOM_NEW_DESIGN;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.when;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class OrchSearchHotelsResponseTransformerAndroidTest {
	
	@InjectMocks
	SearchHotelsResponseTransformerAndroid searchHotelsResponseTransformerAndroid;

	private ObjectMapper objectMapper = new ObjectMapper();

	@Mock
	ObjectMapperUtil objectMapperUtil;

	@Mock
	CommonResponseTransformer commonResponseTransformer;

	@Mock
	private PolyglotService polyglotService;

	Hotel hotel;

	@Mock
	private Utility utility;

	@Spy
	private DateUtil dateUtil;

	@Mock
	PersuasionUtil persuasionUtil;

	@Mock
	SearchHotelsResponseTransformer searchHotelsResponseTransformer;

	private Map<String, Map<String, PersuasionData>> specialFarePersuasionConfigMap;

	@Before
	public void init() throws IOException {
			ObjectMapper mapper = new ObjectMapper();
			mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
			mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
			InputStream availPriceRequest = getClass().getClassLoader().getResourceAsStream("mockrequestresponsetest/hotel.json");
			hotel = mapper.readValue(availPriceRequest, Hotel.class);
			MyBizStaticCard myBizStaticCard = new MyBizStaticCard();
			myBizStaticCard.setText("CORPBUDGET_STATIC_TEXT");
			myBizStaticCard.setSubtext("CORPBUDGET_STATIC_SUBTEXT");
			myBizStaticCard.setCtaText("CORPBUDGET_STATIC_CTATEXT");
			ReflectionTestUtils.setField(searchHotelsResponseTransformerAndroid,"myBizStaticCard",myBizStaticCard);
			Gson gson = new Gson();
			String specialFarePersuasionConfigString = "{\"Desktop\":{\"PC_RIGHT_1_1\":{\"style\":{\"styleClasses\":[\"specialFareTag\",\"pushRight\"]}},\"PC_RIGHT_3\":{\"hover\":{\"style\":{\"styleClasses\":[\"specialFareInfo-tooltip\"]}},\"style\":{\"styleClasses\":[\"specialFareInfo\"]}}},\"Apps\":{\"PLACEHOLDER_BOTTOM_BOX_M\":{\"style\":{\"bgColor\":\"#FFF6E8\",\"textColor\":\"#CF8100\",\"fontType\":\"B\",\"fontSize\":\"MID\"},\"iconurl\":\"https://img.url/\"}}}";
			specialFarePersuasionConfigMap = gson.fromJson(specialFarePersuasionConfigString, new TypeToken<Map<String, Map<String, PersuasionData>>>(){
			}.getType());
			ReflectionTestUtils.setField(searchHotelsResponseTransformerAndroid, "specialFarePersuasionConfigMap", specialFarePersuasionConfigMap);
	}

	@Test
	public void addLocationPersuasionToHotelPersuasionsTest() {
		List<String> locations = new ArrayList<>();
		locations.add("This is test location persuasion");
		LinkedHashSet<String> ameneties = new LinkedHashSet<>();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setDayUsePersuasionsText("Test_Persuasions");
		ameneties.add("WI-FI");
		ameneties.add("bathtub");
		hotel.setHotelPersuasions(new HashMap<>());
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		searchHotelsRequest.getRequestDetails().setFunnelSource("DAYUSE");
		searchHotelsResponseTransformerAndroid.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null, null, null, false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		org.junit.Assert.assertEquals(((Map<String, Object>)hotel.getHotelPersuasions()).size(),2);

		locations.add("one more test persuasion");
		hotel.setHotelPersuasions(new HashMap<>());
		searchHotelsResponseTransformerAndroid.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null,"Less than 1 hr",null, false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		org.junit.Assert.assertEquals(((Map<String, Object>)hotel.getHotelPersuasions()).size(),2);

		//Test for Secondary Location Persuasion
		String secondaryPersuasion = "Secondary Persuasion";
		locations.add(secondaryPersuasion);
		hotel.setHotelPersuasions(new HashMap<>());
		searchHotelsResponseTransformerAndroid.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null, null, null, false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP));
		org.junit.Assert.assertTrue(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP) instanceof PersuasionObject);
		org.junit.Assert.assertNotNull(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP)).getData());
		org.junit.Assert.assertTrue(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP)).getData().size() > 0);
		org.junit.Assert.assertTrue(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP)).getData().get(0).getText().contains(secondaryPersuasion));
	}

	@Test
	public void testBuildQuickBookCard() {
		Assert.assertNotNull(searchHotelsResponseTransformerAndroid.buildQuickBookCard(new QuickBookInfo()));
	}

	@Test
	public void testGetMyBizDirectHotelDistanceText() {
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("");
		Assert.assertNotNull(searchHotelsResponseTransformerAndroid.getMyBizDirectHotelDistanceText("test"));
	}

	@Test
	public  void testBuildSlotDetails(){
		SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
		List<RoomTypeDetails> roomTypeDetailsList = new ArrayList<>();
		RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
		roomTypeDetailsList.add(roomTypeDetails);
		searchWrapperHotelEntity.setRecommendedRoomTypeDetails(roomTypeDetailsList);
		Assert.assertNotNull(searchHotelsResponseTransformerAndroid.buildSlotDetails(searchWrapperHotelEntity, utility.getExpDataMap(""), new ListingSearchRequest()));
	}

	@Test
	public  void testWithBuildSlotDetails(){
		ReflectionTestUtils.setField(searchHotelsResponseTransformerAndroid, "commonResponseTransformer", Mockito.mock(CommonResponseTransformer.class));
		ReflectionTestUtils.setField(searchHotelsResponseTransformerAndroid, "missingSlotDetails", Mockito.mock(MissingSlotDetail.class));
		com.mmt.hotels.clientgateway.request.dayuse.Slot slot = new com.mmt.hotels.clientgateway.request.dayuse.Slot();
		slot.setTimeSlot(10);
		slot.setDuration(3);
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setSlot(slot);
		ListingSearchRequest listingSearchRequest = new ListingSearchRequest();
		listingSearchRequest.setSearchCriteria(searchCriteria);
		SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
		List<RoomTypeDetails> roomTypeDetailsList = new ArrayList<>();
		RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
		Slot roomSlot = new Slot();
		roomSlot.setDuration(3);
		roomSlot.setTimeSlot("10");
		roomTypeDetails.setSlot(roomSlot);
		roomTypeDetailsList.add(roomTypeDetails);
		searchWrapperHotelEntity.setRecommendedRoomTypeDetails(roomTypeDetailsList);
		searchWrapperHotelEntity.getRecommendedRoomTypeDetails().get(0).setTotalDisplayFare(new DisplayFare());

		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setDisplayPrice(12d);
		displayPriceBreakDown.setDisplayPriceAlternateCurrency(12d);
		displayPriceBreakDown.setNonDiscountedPrice(12d);
		displayPriceBreakDown.setSavingPerc(5.05);
		displayPriceBreakDown.setBasePrice(13.05);
		displayPriceBreakDown.setHotelTax(4d);
		displayPriceBreakDown.setMmtDiscount(1d);
		displayPriceBreakDown.setCdfDiscount(1d);
		displayPriceBreakDown.setWallet(12d);
		displayPriceBreakDown.setPricingKey("key");
		displayPriceBreakDown.setCouponInfo(new BestCoupon());
		displayPriceBreakDown.getCouponInfo().setDescription("Coupon");
		displayPriceBreakDown.getCouponInfo().setCouponCode("code");
		displayPriceBreakDown.getCouponInfo().setSpecialPromoCoupon(false);
		displayPriceBreakDown.getCouponInfo().setType("promotional");
		displayPriceBreakDown.getCouponInfo().setDiscountAmount(100.0);

		List<DisplayPriceBreakDown> displayPriceBreakDownList = new ArrayList<>();
		displayPriceBreakDownList.add(new DisplayPriceBreakDown());
		displayPriceBreakDownList.get(0).setDisplayPrice(12d);
		displayPriceBreakDownList.get(0).setDisplayPriceAlternateCurrency(12d);
		displayPriceBreakDownList.get(0).setNonDiscountedPrice(12d);
		displayPriceBreakDownList.get(0).setSavingPerc(5.05);
		displayPriceBreakDownList.get(0).setBasePrice(13.05);
		displayPriceBreakDownList.get(0).setHotelTax(4d);
		displayPriceBreakDownList.get(0).setMmtDiscount(1d);
		displayPriceBreakDownList.get(0).setCdfDiscount(1d);
		displayPriceBreakDownList.get(0).setWallet(12d);
		displayPriceBreakDownList.get(0).setPricingKey("key");
		displayPriceBreakDownList.get(0).setCouponInfo(new BestCoupon());
		displayPriceBreakDownList.get(0).getCouponInfo().setDescription("Coupon");
		displayPriceBreakDownList.get(0).getCouponInfo().setCouponCode("code");
		displayPriceBreakDownList.get(0).getCouponInfo().setSpecialPromoCoupon(false);
		displayPriceBreakDownList.get(0).getCouponInfo().setType("promotional");
		displayPriceBreakDownList.get(0).getCouponInfo().setDiscountAmount(100.0);

		searchWrapperHotelEntity.getRecommendedRoomTypeDetails().get(0).getTotalDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
		searchWrapperHotelEntity.getRecommendedRoomTypeDetails().get(0).getTotalDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);
		Assert.assertNotNull(searchHotelsResponseTransformerAndroid.buildSlotDetails(searchWrapperHotelEntity, utility.getExpDataMap(""), listingSearchRequest));
	}

	@Test
	public void buildPriceDetailForDayUseTest(){
		DisplayFare displayFare = new DisplayFare();
		Tax tax = new Tax();
		tax.setValue(1.2);
		displayFare.setTax(tax);
		HotelPrice slashedPrice = new HotelPrice();
		slashedPrice.setSellingPriceNoTax(1.2);
		slashedPrice.setSellingPriceWithTax(1.3);
		displayFare.setSlashedPrice(slashedPrice);
		PriceDetail priceDetail = searchHotelsResponseTransformerAndroid.buildPriceDetailForDayUse(displayFare);
		Assert.assertNotNull(priceDetail);
	}

	@Test
	public void testBuildBottomSheet() throws IOException {
		ListingPagePersonalizationResponsBO webApiResponse = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/listingPersonalizationHESResponse.json")),
				ListingPagePersonalizationResponsBO.class);
		searchHotelsResponseTransformerAndroid.buildBottomSheet(webApiResponse.getPersonalizedResponse().get(0));
	}

	@Test
	public void testBuildStaticCard(){
		MyBizStaticCard staticCard = new MyBizStaticCard();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setCorpBudgetHotel(false);
		hotelEntity.setDetailDeeplinkUrl("test");
		List<SearchWrapperHotelEntity> hotelEntityList = new ArrayList<>();
		hotelEntityList.add(hotelEntity);
		Mockito.when(polyglotService.getTranslatedData("CORPBUDGET_STATIC_TEXT")).thenReturn("This Is Not A Budget Hotel");
		staticCard = searchHotelsResponseTransformerAndroid.buildStaticCard("DIRECT_HOTEL",hotelEntityList);
		Assert.assertNotNull(staticCard);
		Assert.assertEquals("This Is Not A Budget Hotel", staticCard.getText());
	}

	@Test
	public void addPersuasionsForHiddenGemCardTest() {
		SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
		searchWrapperHotelEntity.setLocationPersuasion(Collections.singletonList("Test Location"));
		searchWrapperHotelEntity.setHiddenGem(true);
		searchWrapperHotelEntity.setHiddenGemPersuasionText("Hidden Gem Persuasion Text");
		HomeStayDetails homeStayDetails = new HomeStayDetails();
		homeStayDetails.setStayType(Constants.STAY_TYPE_HOMESTAY);
		homeStayDetails.setStayTypeInfo("Home Stay Info Test");

		PersuasionObject homeStaysTitlePersuasion = new PersuasionObject();
		List<PersuasionData> homeStaysTitlePersuasionList = new ArrayList<>();
		homeStaysTitlePersuasionList.add(new PersuasionData());
		homeStaysTitlePersuasion.setData(homeStaysTitlePersuasionList);

		PersuasionObject homestaysSubTitlePersuasion = new PersuasionObject();
		homestaysSubTitlePersuasion.setData(Arrays.asList(new PersuasionData()));

		when(persuasionUtil.buildHiddenGemPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
		when(persuasionUtil.buildHiddenGemIconPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
		when(persuasionUtil.buildHomeStaysTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homeStaysTitlePersuasion);
		when(persuasionUtil.buildHomeStaysSubTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homestaysSubTitlePersuasion);
		searchHotelsResponseTransformerAndroid.addPersuasionsForHiddenGemCard(searchWrapperHotelEntity);
		junit.framework.Assert.assertNotNull(searchWrapperHotelEntity.getHotelPersuasions());
		junit.framework.Assert.assertTrue(searchWrapperHotelEntity.getHotelPersuasions() instanceof Map<?, ?>);
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdApps()));
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdApps()));
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdApps()));
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdApps()));
	}


	@Test
	public void groupPriceNewDesignTest(){
		ReflectionTestUtils.setField(searchHotelsResponseTransformerAndroid, "commonResponseTransformer", Mockito.mock(CommonResponseTransformer.class));
		ReflectionTestUtils.setField(searchHotelsResponseTransformerAndroid, "dateUtil", Mockito.mock(DateUtil.class));
		ReflectionTestUtils.setField(searchHotelsResponseTransformerAndroid, "commonHelper", Mockito.mock(CommonHelper.class));
		Mockito.when(utility.isExperimentOn(Mockito.any(), Mockito.any())).thenReturn(true);
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("");
		String sectionName="GROUP_HOTELS";
		List<SearchWrapperHotelEntity> hotelList = new ArrayList<>();
		SearchWrapperHotelEntity hotelEntity=new SearchWrapperHotelEntity();
		hotelEntity.setGroupBookingPrice(true);
		hotelEntity.setRoomCount(6);
		SearchHotelsRequest searchHotelsRequest=new SearchHotelsRequest();
		SearchHotelsCriteria searchCriteria=new SearchHotelsCriteria();
		searchCriteria.setCheckIn("2023-04-21");
		searchCriteria.setCheckOut("2023-04-22");
		searchHotelsRequest.setSearchCriteria(searchCriteria);
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		searchHotelsRequest.getRequestDetails().setFunnelSource("GROUP");
		searchHotelsRequest.setClient("DESKTOP");
		String expData= "{\"GRPN\":\"T\",\"PDO\":\"PRN\"}";
		searchHotelsRequest.setExpData(expData);
		hotelEntity.setDisplayFare(new DisplayFare());
		hotelEntity.getDisplayFare().setDisplayPriceBreakDown(new DisplayPriceBreakDown());
		hotelEntity.getDisplayFare().getDisplayPriceBreakDown().setSavingPerc(13);
		hotelEntity.getDisplayFare().getDisplayPriceBreakDown().setPricingKey("testKey");
		hotelEntity.getDisplayFare().getDisplayPriceBreakDown().setTotalAmount(15340);
		hotelEntity.setHas360Image(true);
		hotelList.add(hotelEntity);
		List<Hotel> hotelLists= searchHotelsResponseTransformerAndroid.buildPersonalizedHotels(hotelList,utility.getExpDataMap(expData), searchHotelsRequest,sectionName,null, null, null, VERTICAL);
		Assert.assertNotNull(hotelLists);
	}


	@Test
	public void addSpecialFarePersuasionTest() {
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		searchHotelsResponseTransformerAndroid.addSpecialFarePersuasion(hotelEntity);
		Assert.assertNotNull(hotelEntity.getHotelPersuasions());
		Assert.assertNotNull(((Map<?, ?>) hotelEntity.getHotelPersuasions()).get(Constants.PLACEHOLDER_PRICE_BOTTOM_M));
	}

	@Test
	public void addBookingConfirmationPersuasionTest() {
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		searchHotelsResponseTransformerAndroid.addBookingConfirmationPersuasion(hotelEntity);
		Assert.assertNotNull(hotelEntity.getHotelPersuasions());
		Assert.assertNotNull(((Map<?, ?>) hotelEntity.getHotelPersuasions()).get(Persuasions.SPECIAL_FARE_PERSUASION.getPlaceholderIdApps()));
	}

	@Test
	public void testCreatePriceDisplayMsgAsPerRoomsNights() {

		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setCheckIn("2022-12-01");
		searchCriteria.setCheckOut("2022-12-03");
		searchHotelsRequest.setSearchCriteria(searchCriteria);

		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setRoomCount(2);


		Mockito.when(commonResponseTransformer.getGroupPriceTextForGRPNT(Mockito.anyInt(),Mockito.anyInt())).thenReturn("2 Nights, 2 Rooms");

		String result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerAndroid, "createPriceDisplayMsgAsPerRoomsNights", searchHotelsRequest,hotelEntity);
		assertEquals("2 Nights, 2 Rooms", result);
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		searchHotelsRequest.getRequestDetails().setFunnelSource("GROUP");
		hotelEntity.setAltAcco(true);
		Mockito.when(commonResponseTransformer.getGroupPriceTextForGRPNTForAltAcco(Mockito.anyInt())).thenReturn("2 Nights");
		result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerAndroid, "createPriceDisplayMsgAsPerRoomsNights", searchHotelsRequest,hotelEntity);
		assertEquals("2 Nights", result);


	}


	@Test
	public void testUpdateHotelCardType_B() {
		SearchHotelsRequest searchHotelsRequest = Mockito.mock(SearchHotelsRequest.class);
		CommonModifierResponse commonModifierResponse = Mockito.mock(CommonModifierResponse.class);
		PersonalizedResponse perResponse = Mockito.mock(PersonalizedResponse.class);
		PersonalizedSection personalizedSection = new PersonalizedSection();
		personalizedSection.setName(DIRECT_HOTEL);

		when(perResponse.getHotelCardType()).thenReturn(COMPACT_V1_HOTEL_CARD_TYPE);

		searchHotelsResponseTransformerAndroid.updateHotelCardType(searchHotelsRequest, commonModifierResponse, perResponse, personalizedSection);

		assertEquals(COMPACT_V1_HOTEL_CARD_TYPE, personalizedSection.getHotelCardType());
	}
	@Test
	public void testUpdateHotelCardType_C() {
		SearchHotelsRequest searchHotelsRequest = Mockito.mock(SearchHotelsRequest.class);
		PersonalizedResponse perResponse = Mockito.mock(PersonalizedResponse.class);
		PersonalizedSection personalizedSection = new PersonalizedSection();
		CommonModifierResponse commonModifierResponse = Mockito.mock(CommonModifierResponse.class);

		personalizedSection.setName(SIMILAR_HOTELS);

		InputHotel inputHotel = Mockito.mock(InputHotel.class);
		when(inputHotel.getAttribute()).thenReturn(ATTRIBUTE_INDEPENDENT_PROPERTY);

		MatchMakerRequest matchMakerRequest = Mockito.mock(MatchMakerRequest.class);
		when(matchMakerRequest.getHotels()).thenReturn(Collections.singletonList(inputHotel));
		when(searchHotelsRequest.getMatchMakerDetails()).thenReturn(matchMakerRequest);

		LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
		expDataMap.put(ExperimentKeys.PUSH_INDEPENDENT_ON_DIRECT_SEARCH.getKey(), "C");
		when(commonModifierResponse.getExpDataMap()).thenReturn(expDataMap);

		searchHotelsResponseTransformerAndroid.updateHotelCardType(searchHotelsRequest, commonModifierResponse, perResponse, personalizedSection);

		assertEquals(COMPACT_V2_HOTEL_CARD_TYPE, personalizedSection.getHotelCardType());
		assertEquals(HORIZONTAL, personalizedSection.getOrientation());
		assertEquals(GRADIENT_END, personalizedSection.getSectionBG());
	}

	@Test
	public void testUpdateHotelCardType_Other() {
		SearchHotelsRequest searchHotelsRequest = Mockito.mock(SearchHotelsRequest.class);
		CommonModifierResponse commonModifierResponse = Mockito.mock(CommonModifierResponse.class);
		PersonalizedResponse perResponse = Mockito.mock(PersonalizedResponse.class);
		PersonalizedSection personalizedSection = new PersonalizedSection();

		LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
		expDataMap.put(ExperimentKeys.PUSH_INDEPENDENT_ON_DIRECT_SEARCH.getKey(), "D");
		when(commonModifierResponse.getExpDataMap()).thenReturn(expDataMap);
		when(perResponse.getHotelCardType()).thenReturn("anyType");
		personalizedSection.setName("other");

		searchHotelsResponseTransformerAndroid.updateHotelCardType(searchHotelsRequest, commonModifierResponse, perResponse, personalizedSection);

		assertEquals("anytype", personalizedSection.getHotelCardType());
	}


	@Test
	public void testFetchAttributeValuesFromSearchHotelsRequest() throws Exception {
		SearchHotelsRequest request = new SearchHotelsRequest();
		MatchMakerRequest matchMakerRequest = new MatchMakerRequest();
		List<InputHotel> hotels = new ArrayList<>();
		InputHotel hotel = new InputHotel();
		hotel.setAttribute("testAttribute");
		hotels.add(hotel);
		matchMakerRequest.setHotels(hotels);
		request.setMatchMakerDetails(matchMakerRequest);

		List<String> attributes = (List<String>) ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformer, "fetchAttributeValuesFromSearchHotelsRequest", request);

		assertNotNull(attributes);
		assertEquals(1, attributes.size());
		assertEquals("testAttribute", attributes.get(0));
	}

	@Test
	public void testUpdateHotelCardType_C_DirectHotel() {
		SearchHotelsRequest searchHotelsRequest = Mockito.mock(SearchHotelsRequest.class);
		CommonModifierResponse commonModifierResponse = Mockito.mock(CommonModifierResponse.class);
		PersonalizedResponse perResponse = Mockito.mock(PersonalizedResponse.class);
		PersonalizedSection personalizedSection = new PersonalizedSection();
		personalizedSection.setName(DIRECT_HOTEL);

		InputHotel inputHotel = Mockito.mock(InputHotel.class);
		when(inputHotel.getAttribute()).thenReturn(ATTRIBUTE_INDEPENDENT_PROPERTY);

		MatchMakerRequest matchMakerRequest = Mockito.mock(MatchMakerRequest.class);
		when(matchMakerRequest.getHotels()).thenReturn(Collections.singletonList(inputHotel));
		when(searchHotelsRequest.getMatchMakerDetails()).thenReturn(matchMakerRequest);

		LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
		expDataMap.put(ExperimentKeys.PUSH_INDEPENDENT_ON_DIRECT_SEARCH.getKey(), "C");
		when(commonModifierResponse.getExpDataMap()).thenReturn(expDataMap);

		searchHotelsResponseTransformerAndroid.updateHotelCardType(searchHotelsRequest, commonModifierResponse, perResponse, personalizedSection);

		assertEquals(COMPACT_V1_HOTEL_CARD_TYPE, personalizedSection.getHotelCardType());
	}

	@Test
	public void testCheckIndependentPropertyAttributeInSearchHotelsRequest() throws Exception {
		SearchHotelsRequest request = new SearchHotelsRequest();
		MatchMakerRequest matchMakerRequest = new MatchMakerRequest();
		List<InputHotel> hotels = new ArrayList<>();
		InputHotel hotel = new InputHotel();
		hotel.setAttribute(ATTRIBUTE_INDEPENDENT_PROPERTY);
		hotels.add(hotel);
		matchMakerRequest.setHotels(hotels);
		request.setMatchMakerDetails(matchMakerRequest);

		boolean result = (boolean) ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformer, "checkIndependentPropertyAttributeInSearchHotelsRequest", request);

		Assert.assertTrue(result);
	}


	@Test
	public void testGetGroupPriceAndSavingText() {
		SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
		ListingSearchRequest listingSearchRequest = new ListingSearchRequest();
		// Mocking utility and dateUtil methods
		Map<String, String> expDataMap = new HashMap<>();
		//when(utility.isExperimentOn(expDataMap, "EXP_PERNEW")).thenReturn(true);
		//when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated Text");

		// Setting up searchWrapperHotelEntity
		DisplayFare displayFare = new DisplayFare();
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setTotalAmount(1000.0);
		displayPriceBreakDown.setSavingPerc(10.0);
		displayFare.setDisplayPriceBreakDown(displayPriceBreakDown);
		searchWrapperHotelEntity.setDisplayFare(displayFare);
		searchWrapperHotelEntity.setRoomCount(1);
		searchWrapperHotelEntity.setStayType("ENTIRE");
		searchWrapperHotelEntity.setGroupBookingPrice(true);

		// Setting up listingSearchRequest
		listingSearchRequest.setSearchCriteria(new SearchHotelsCriteria());
		listingSearchRequest.getSearchCriteria().setCurrency("USD");
		listingSearchRequest.getSearchCriteria().setCheckIn("2023-01-01");
		listingSearchRequest.getSearchCriteria().setCheckOut("2023-01-03");

		// Mocking commonResponseTransformer method
		//when(commonResponseTransformer.getGroupPriceText(1, 2, "1,000", null, true)).thenReturn("Group Price Text");

		// Calling the method
		List<String> result = searchHotelsResponseTransformer.getGroupPriceAndSavingText(searchWrapperHotelEntity, listingSearchRequest, expDataMap);
	}

}