package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.response.DataList;
import com.mmt.hotels.clientgateway.response.InitApprovalResponse;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.response.cbresponse.ErrorResponse;
import com.mmt.hotels.clientgateway.response.moblanding.GenericErrorEntity;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.desktop.InitiateApprovalResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.util.Utility;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;

import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.POLICY_MISMATCH;

@RunWith(MockitoJUnitRunner.class)
public class InitiateApprovalResponseTransformerDesktopTest {

    @Mock
    PolyglotService polyglotService;
    @Mock
    Utility utility;
    @InjectMocks
    InitiateApprovalResponseTransformerDesktop initiateApprovalResponseTransformerDesktop;

    @Test
    public void test() {

        CGServerResponse cgServerResponse = new CGServerResponse();
        cgServerResponse.setResponseErrors(new ErrorResponse());
        cgServerResponse.getResponseErrors().setErrorList(new ArrayList<>());
        cgServerResponse.getResponseErrors().getErrorList().add(new GenericErrorEntity());
        cgServerResponse.getResponseErrors().getErrorList().get(0).setErrorCode("12");
        cgServerResponse.getResponseErrors().getErrorList().get(0).setErrorMessage("Invalid");
        cgServerResponse.getResponseErrors().getErrorList().get(0).setErrorAdditionalInfo(new HashMap<>());
        cgServerResponse.getResponseErrors().getErrorList().get(0).getErrorAdditionalInfo().put("error", "downstream issue");
        Assert.assertNotNull(initiateApprovalResponseTransformerDesktop.processResponse(cgServerResponse));

        CGServerResponse cgServerResponse1 = new CGServerResponse();
        cgServerResponse1.setResponseErrors(null);
        cgServerResponse1.setAdditionalProperty("error", "abc");
        cgServerResponse1.setAdditionalProperty("status", "abc");
        cgServerResponse1.setAdditionalProperty("message", "abc");
        Assert.assertNotNull(initiateApprovalResponseTransformerDesktop.processResponse(cgServerResponse1));


    }
    @Test
    public void testProcessResponseWithAdditionalPropertiesAndError() {
        CGServerResponse cgServerResponse = new CGServerResponse();
        cgServerResponse.setAdditionalProperty("responseCode", "OSBA");
        cgServerResponse.setAdditionalProperty("message", "testMessage");
        cgServerResponse.setAdditionalProperty("status", "error");
        LinkedHashMap<String, String> duplicateBookingDetails = new LinkedHashMap<>();
        duplicateBookingDetails.put("travellerName", "name");
        cgServerResponse.setAdditionalProperty("duplicateBookingDetails", duplicateBookingDetails);
        Mockito.when(utility.populateDateList(Mockito.any())).thenReturn(new LinkedHashSet<DataList>());
        InitApprovalResponse response = initiateApprovalResponseTransformerDesktop.processResponse(cgServerResponse);

        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getConsentData());
        Assert.assertNotNull(response.getConsentData().getDataList());
        Assert.assertEquals("success", response.getStatus());
    }


}
