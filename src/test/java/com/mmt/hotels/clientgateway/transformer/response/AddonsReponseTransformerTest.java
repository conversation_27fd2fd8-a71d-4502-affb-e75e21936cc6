package com.mmt.hotels.clientgateway.transformer.response;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import com.mmt.hotels.clientgateway.response.SearchAddonsResponse;
import com.mmt.hotels.model.response.addon.AddOnEntity;

@RunWith(MockitoJUnitRunner.class)
public class AddonsReponseTransformerTest {

	@InjectMocks
	AddonsReponseTransformer addonsReponseTransformer;
	
	@Spy
	CommonResponseTransformer commonResponseTransformer;
	
	@Test
	public void testConvertSearchAddonsResponse(){
		AddOnEntity getAddonsResponse = new AddOnEntity();
		SearchAddonsResponse resp = addonsReponseTransformer.convertSearchAddonsResponse(getAddonsResponse );
		Assert.assertNotNull(resp);
	
	}

	// Additional test cases for improved code coverage - appended without modifying existing tests

	@Test
	public void testConvertSearchAddonsResponseWithComplexAddOnEntity(){
		AddOnEntity getAddonsResponse = new AddOnEntity();
		// Test with a more complex entity setup if setters exist
		SearchAddonsResponse resp = addonsReponseTransformer.convertSearchAddonsResponse(getAddonsResponse);
		Assert.assertNotNull(resp);
	}
	
	@Test
	public void testAddonsReponseTransformerInitialization(){
		Assert.assertNotNull(addonsReponseTransformer);
		Assert.assertNotNull(commonResponseTransformer);
	}
	
}
