package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.AddOnDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.AddOns;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.PolicyDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.Rooms;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.FlexiCancelStaticDetail;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomBanner;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.mmt.hotels.clientgateway.constants.Constants.FLEXI_CANCEL;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static org.junit.Assert.*;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SearchRoomsBannerHelperTest {

    @InjectMocks
    private SearchRoomsBannerHelper searchRoomsBannerHelper;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    private CommonConfigConsul commonConfigConsul;

    @Before
    public void setUp() {
        FlexiCancelStaticDetail flexiCancelStaticDetail = new FlexiCancelStaticDetail();
        flexiCancelStaticDetail.setIconUrl("icon_url");
        flexiCancelStaticDetail.setRedirectUrl("redirect_url");
        flexiCancelStaticDetail.setBackgroundImage("bg_image");
        lenient().when(commonConfigConsul.getFlexiCancelStaticDetail()).thenReturn(flexiCancelStaticDetail);
        searchRoomsBannerHelper.init();

        lenient().when(polyglotService.getTranslatedData(anyString())).thenAnswer(i -> i.getArguments()[0]);
        lenient().when(polyglotService.getTranslatedData(FLEXI_CANCEL_DETAIL_BANNER_TITLE)).thenReturn("Flexi-Cancel Title");
        lenient().when(polyglotService.getTranslatedData(FLEXI_CANCEL_DETAIL_BANNER_DESC)).thenReturn("Flexi-Cancel Desc");
        lenient().when(polyglotService.getTranslatedData(FLEXI_CANCEL_LEAN_MORE)).thenReturn("Learn More");
        lenient().when(polyglotService.getTranslatedData(SELECT_ROOM_1_AMENITIES_BANNER)).thenReturn("{ROOM_NAME} with {AMENITY_1}");
        lenient().when(polyglotService.getTranslatedData(SELECT_ROOM_2_AMENITIES_BANNER)).thenReturn("{ROOM_NAME} with {AMENITY_1} and {AMENITY_2}");
        lenient().when(polyglotService.getTranslatedData(SELECT_ROOM_3_AMENITIES_BANNER)).thenReturn("{ROOM_NAME} with {AMENITY_1}, {AMENITY_2}, and {AMENITY_3}");
    }

    @Test
    public void should_ReturnNull_When_HotelDetailsIsNull() {
        assertNull(searchRoomsBannerHelper.buildBanner(new SearchRoomsResponse(), null));
    }

    @Test
    public void should_ReturnNull_When_SearchRoomsResponseIsNull() {
        assertNull(searchRoomsBannerHelper.buildBanner(null, new HotelDetails()));
    }

    @Test
    public void should_ReturnNull_When_NoBannersCanBeBuilt() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        HotelDetails hotelDetails = new HotelDetails();
        assertNull(searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails));
    }

    @Test
    public void should_ReturnFlexiCancelBanner_When_AvailableInRecommendedCombos() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        searchRoomsResponse.setRecommendedCombos(Collections.singletonList(new com.mmt.hotels.clientgateway.response.rooms.RecommendedCombo()));

        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRoomCombos(Collections.singletonList(createRoomComboWithFlexi("FC Title")));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNotNull(banner);
        assertEquals(FLEXI_CANCEL, banner.getType());
        assertEquals("Flexi-Cancel Title", banner.getTitle());
        assertEquals("FC Title", banner.getDescription());
    }

    @Test
    public void should_ReturnFlexiCancelBanner_When_AvailableInExactRooms() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        searchRoomsResponse.setExactRooms(Collections.singletonList(new RoomDetails()));
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRooms(Collections.singletonList(createRoomWithFlexi("FC Title")));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNotNull(banner);
        assertEquals(FLEXI_CANCEL, banner.getType());
        assertEquals("Flexi-Cancel Title", banner.getTitle());
        assertEquals("FC Title", banner.getDescription());
    }
    
    @Test
    public void should_ReturnAmenityBanner_When_AvailableInExactRooms() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomCode("R1");
        searchRoomsResponse.setExactRooms(Collections.singletonList(roomDetails));

        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRooms(Arrays.asList(createRoomWithAmenities("R2", "Amenity1")));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_ReturnAmenityBanner_When_AvailableInOccupancyRooms() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomCode("R1");
        searchRoomsResponse.setOccupancyRooms(Collections.singletonList(roomDetails));

        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRooms(Arrays.asList(createRoomWithAmenities("R2", "Amenity1", "Amenity2")));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNotNull(banner);
        assertEquals("Deluxe Room with Amenity1 and Amenity2", banner.getTitle());
        assertEquals(Constants.SELECT_ROOM_BANNER_TYPE, banner.getRedirectType());
    }

    @Test
    public void should_ReturnAmenityBanner_When_AvailableInRecommendedCombos() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        com.mmt.hotels.clientgateway.response.rooms.RecommendedCombo combo = new com.mmt.hotels.clientgateway.response.rooms.RecommendedCombo();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomCode("R1");
        combo.setRooms(Collections.singletonList(roomDetails));
        searchRoomsResponse.setRecommendedCombos(Collections.singletonList(combo));

        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRoomCombos(Collections.singletonList(createRoomComboWithAmenities("R2", "A1", "A2", "A3")));
        
        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_Handle_EmptyCollectionsInResponses() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        searchRoomsResponse.setExactRooms(new ArrayList<>());
        searchRoomsResponse.setRecommendedCombos(new ArrayList<>());
        searchRoomsResponse.setOccupancyRooms(new ArrayList<>());

        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRooms(new ArrayList<>());
        hotelDetails.setRoomCombos(new ArrayList<>());

        assertNull(searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails));
    }
    
    @Test
    public void should_Handle_PolyglotServiceException() {
        lenient().when(polyglotService.getTranslatedData(anyString())).thenThrow(new RuntimeException("Polyglot down"));
        
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomCode("R1");
        searchRoomsResponse.setExactRooms(Collections.singletonList(roomDetails));

        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRooms(Arrays.asList(createRoomWithAmenities("R2", "Amenity1")));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }
    
    @Test
    public void should_buildAmenityBannerText_ForOneAmenity() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomCode("R1");
        searchRoomsResponse.setExactRooms(Collections.singletonList(roomDetails));

        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRooms(Arrays.asList(createRoomWithAmenities("R2", "WiFi")));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_buildAmenityBannerText_ForTwoAmenities() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomCode("R1");
        searchRoomsResponse.setExactRooms(Collections.singletonList(roomDetails));

        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRooms(Arrays.asList(createRoomWithAmenities("R2", "WiFi", "Pool")));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_buildAmenityBannerText_ForThreeOrMoreAmenities() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomCode("R1");
        searchRoomsResponse.setExactRooms(Collections.singletonList(roomDetails));

        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRooms(Arrays.asList(createRoomWithAmenities("R2", "WiFi", "Pool", "Gym", "Spa")));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_buildAmenityBannerText_WithNullRoomName() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomCode("R1");
        searchRoomsResponse.setExactRooms(Collections.singletonList(roomDetails));

        HotelDetails hotelDetails = new HotelDetails();
        Rooms room = createRoomWithAmenities("R2", "WiFi");
        room.getRoomInfo().setRoomName(null);
        hotelDetails.setRooms(Arrays.asList(room));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_makeBanner_with_null_flexiCancelStaticDetail() {
        when(commonConfigConsul.getFlexiCancelStaticDetail()).thenReturn(null);
        searchRoomsBannerHelper.init();

        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        searchRoomsResponse.setExactRooms(Collections.singletonList(new RoomDetails()));
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRooms(Collections.singletonList(createRoomWithFlexi("FC Title")));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNotNull(banner);
        assertEquals(FLEXI_CANCEL, banner.getType());
        assertEquals("", banner.getIconUrl());
        assertEquals("", banner.getRedirectLink());
        assertEquals("", banner.getBgImageUrl());
    }

    @Test
    public void should_ReturnNull_When_FlexiCancelHasNoTitle() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        searchRoomsResponse.setExactRooms(Collections.singletonList(new RoomDetails()));
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRooms(Collections.singletonList(createRoomWithFlexiNoTitle()));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_ReturnNull_When_RoomHasNoAmenities() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomCode("R1");
        searchRoomsResponse.setExactRooms(Collections.singletonList(roomDetails));

        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRooms(Arrays.asList(createRoomWithoutAmenities("R2")));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_ReturnNull_When_NoMatchingRoomCode() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomCode("R1");
        searchRoomsResponse.setExactRooms(Collections.singletonList(roomDetails));

        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRooms(Arrays.asList(createRoomWithAmenities("R1", "WiFi")));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_Handle_NullAddOns() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        searchRoomsResponse.setExactRooms(Collections.singletonList(new RoomDetails()));
        HotelDetails hotelDetails = new HotelDetails();
        
        Rooms room = new Rooms();
        room.setCode("R1");
        RatePlan ratePlan = new RatePlan();
        ratePlan.setAddOns(null);
        room.setRatePlans(Collections.singletonList(ratePlan));
        hotelDetails.setRooms(Collections.singletonList(room));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_Handle_NullAddOnDetails() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        searchRoomsResponse.setExactRooms(Collections.singletonList(new RoomDetails()));
        HotelDetails hotelDetails = new HotelDetails();
        
        Rooms room = new Rooms();
        room.setCode("R1");
        RatePlan ratePlan = new RatePlan();
        AddOns addOns = new AddOns();
        addOns.setAddOnDetails(null);
        ratePlan.setAddOns(addOns);
        room.setRatePlans(Collections.singletonList(ratePlan));
        hotelDetails.setRooms(Collections.singletonList(room));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_Handle_EmptyAddOnDetails() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        searchRoomsResponse.setExactRooms(Collections.singletonList(new RoomDetails()));
        HotelDetails hotelDetails = new HotelDetails();
        
        Rooms room = new Rooms();
        room.setCode("R1");
        RatePlan ratePlan = new RatePlan();
        AddOns addOns = new AddOns();
        addOns.setAddOnDetails(new ArrayList<>());
        ratePlan.setAddOns(addOns);
        room.setRatePlans(Collections.singletonList(ratePlan));
        hotelDetails.setRooms(Collections.singletonList(room));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_Handle_WrongTypeAddOnDetails() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        searchRoomsResponse.setExactRooms(Collections.singletonList(new RoomDetails()));
        HotelDetails hotelDetails = new HotelDetails();
        
        Rooms room = new Rooms();
        room.setCode("R1");
        RatePlan ratePlan = new RatePlan();
        AddOns addOns = new AddOns();
        AddOnDetails addOnDetail = new AddOnDetails();
        addOnDetail.setType("INSURANCE");
        addOns.setAddOnDetails(Collections.singletonList(addOnDetail));
        ratePlan.setAddOns(addOns);
        room.setRatePlans(Collections.singletonList(ratePlan));
        hotelDetails.setRooms(Collections.singletonList(room));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_Handle_NullRoomInfo() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomCode("R1");
        searchRoomsResponse.setExactRooms(Collections.singletonList(roomDetails));

        HotelDetails hotelDetails = new HotelDetails();
        Rooms room = new Rooms();
        room.setCode("R2");
        room.setRoomInfo(null);
        hotelDetails.setRooms(Arrays.asList(room));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_Handle_NullAmenities() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomCode("R1");
        searchRoomsResponse.setExactRooms(Collections.singletonList(roomDetails));

        HotelDetails hotelDetails = new HotelDetails();
        Rooms room = new Rooms();
        room.setCode("R2");
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomCode("R2");
        roomInfo.setAmenities(null);
        room.setRoomInfo(roomInfo);
        hotelDetails.setRooms(Arrays.asList(room));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_Handle_EmptyAmenities() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomCode("R1");
        searchRoomsResponse.setExactRooms(Collections.singletonList(roomDetails));

        HotelDetails hotelDetails = new HotelDetails();
        Rooms room = new Rooms();
        room.setCode("R2");
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomCode("R2");
        roomInfo.setAmenities(new ArrayList<>());
        room.setRoomInfo(roomInfo);
        hotelDetails.setRooms(Arrays.asList(room));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_Handle_NullAmenityNames() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomCode("R1");
        searchRoomsResponse.setExactRooms(Collections.singletonList(roomDetails));

        HotelDetails hotelDetails = new HotelDetails();
        Rooms room = new Rooms();
        room.setCode("R2");
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomCode("R2");
        AmenityGroup amenity = new AmenityGroup();
        amenity.setName(null);
        roomInfo.setAmenities(Collections.singletonList(amenity));
        room.setRoomInfo(roomInfo);
        hotelDetails.setRooms(Arrays.asList(room));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_Handle_BlankAmenityNames() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomCode("R1");
        searchRoomsResponse.setExactRooms(Collections.singletonList(roomDetails));

        HotelDetails hotelDetails = new HotelDetails();
        Rooms room = new Rooms();
        room.setCode("R2");
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomCode("R2");
        AmenityGroup amenity = new AmenityGroup();
        amenity.setName("");
        roomInfo.setAmenities(Collections.singletonList(amenity));
        room.setRoomInfo(roomInfo);
        hotelDetails.setRooms(Arrays.asList(room));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_Handle_NullRatePlans() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        searchRoomsResponse.setExactRooms(Collections.singletonList(new RoomDetails()));
        HotelDetails hotelDetails = new HotelDetails();
        
        Rooms room = new Rooms();
        room.setCode("R1");
        room.setRatePlans(null);
        hotelDetails.setRooms(Collections.singletonList(room));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_Handle_EmptyRatePlans() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        searchRoomsResponse.setExactRooms(Collections.singletonList(new RoomDetails()));
        HotelDetails hotelDetails = new HotelDetails();
        
        Rooms room = new Rooms();
        room.setCode("R1");
        room.setRatePlans(new ArrayList<>());
        hotelDetails.setRooms(Collections.singletonList(room));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    @Test
    public void should_Handle_FlexiCancelWithNullRemoved() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        searchRoomsResponse.setExactRooms(Collections.singletonList(new RoomDetails()));
        HotelDetails hotelDetails = new HotelDetails();
        
        Rooms room = new Rooms();
        room.setCode("R1");
        RatePlan ratePlan = new RatePlan();
        AddOns addOns = new AddOns();
        AddOnDetails addOnDetail = new AddOnDetails();
        addOnDetail.setType(FLEXI_CANCEL);
        addOnDetail.setRemoved(null);
        addOns.setAddOnDetails(Collections.singletonList(addOnDetail));
        ratePlan.setAddOns(addOns);
        room.setRatePlans(Collections.singletonList(ratePlan));
        hotelDetails.setRooms(Collections.singletonList(room));

        SelectRoomBanner banner = searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails);

        assertNull(banner);
    }

    // Helper methods
    private Rooms createRoomWithFlexi(String flexiTitle) {
        Rooms room = new Rooms();
        room.setCode("R1");
        RatePlan ratePlan = new RatePlan();
        AddOns addOns = new AddOns();
        AddOnDetails addOnDetail = new AddOnDetails();
        addOnDetail.setType(FLEXI_CANCEL);
        PolicyDetails removed = new PolicyDetails();
        removed.setTitle(flexiTitle);
        addOnDetail.setRemoved(removed);
        addOns.setAddOnDetails(Collections.singletonList(addOnDetail));
        ratePlan.setAddOns(addOns);
        room.setRatePlans(Collections.singletonList(ratePlan));
        return room;
    }

    private Rooms createRoomWithFlexiNoTitle() {
        Rooms room = new Rooms();
        room.setCode("R1");
        RatePlan ratePlan = new RatePlan();
        AddOns addOns = new AddOns();
        AddOnDetails addOnDetail = new AddOnDetails();
        addOnDetail.setType(FLEXI_CANCEL);
        PolicyDetails removed = new PolicyDetails();
        removed.setTitle(null);
        addOnDetail.setRemoved(removed);
        addOns.setAddOnDetails(Collections.singletonList(addOnDetail));
        ratePlan.setAddOns(addOns);
        room.setRatePlans(Collections.singletonList(ratePlan));
        return room;
    }

    private Rooms createRoomWithAmenities(String roomCode, String... amenities) {
        Rooms room = new Rooms();
        room.setCode(roomCode);
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomCode(roomCode);
        roomInfo.setRoomName("Deluxe Room");
        
        List<AmenityGroup> amenityGroups = new ArrayList<>();
        for (String amenity : amenities) {
            AmenityGroup amenityGroup = new AmenityGroup();
            amenityGroup.setName(amenity);
            amenityGroups.add(amenityGroup);
        }
        roomInfo.setAmenities(amenityGroups);
        room.setRoomInfo(roomInfo);
        return room;
    }

    private Rooms createRoomWithoutAmenities(String roomCode) {
        Rooms room = new Rooms();
        room.setCode(roomCode);
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomCode(roomCode);
        roomInfo.setRoomName("Deluxe Room");
        roomInfo.setAmenities(new ArrayList<>());
        room.setRoomInfo(roomInfo);
        return room;
    }

    private RoomCombo createRoomComboWithFlexi(String flexiTitle) {
        RoomCombo roomCombo = new RoomCombo();
        Rooms room = createRoomWithFlexi(flexiTitle);
        roomCombo.setRooms(Collections.singletonList(room));
        return roomCombo;
    }

    private RoomCombo createRoomComboWithAmenities(String roomCode, String... amenities) {
        RoomCombo roomCombo = new RoomCombo();
        Rooms room = createRoomWithAmenities(roomCode, amenities);
        roomCombo.setRooms(Collections.singletonList(room));
        return roomCombo;
    }
} 