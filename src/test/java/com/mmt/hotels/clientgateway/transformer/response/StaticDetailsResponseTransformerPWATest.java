package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.StaticDetailCriteria;
import com.mmt.hotels.clientgateway.request.StaticDetailRequest;
import com.mmt.hotels.clientgateway.request.wishlist.WishListedHotelsDetailRequest;
import com.mmt.hotels.clientgateway.response.moblanding.SupportDetails;
import com.mmt.hotels.clientgateway.response.staticdetail.PropertyChainCG;
import com.mmt.hotels.clientgateway.response.staticdetail.PropertyHighlightCG;
import com.mmt.hotels.clientgateway.response.staticdetail.ReportCardPersuasion;
import com.mmt.hotels.clientgateway.response.staticdetail.StaticDetailResponse;
import com.mmt.hotels.clientgateway.response.wishlist.WishListedHotelsDetailResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.pwa.StaticDetailResponseTransformerPWA;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.persuasion.response.PersuasionData;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.request.upsell.UpsellHotelDetailResponse;
import com.mmt.hotels.model.response.flyfish.ContextualReviewData;
import com.mmt.hotels.model.response.flyfish.ContextualizeReviews;
import com.mmt.hotels.model.response.flyfish.UserReviewResponseForListing;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import com.mmt.hotels.model.response.staticdata.*;
import com.mmt.hotels.pojo.CalendarAvailability.CalendarCriteria;
import com.mmt.hotels.pojo.request.detail.mob.CBFlyFishSummaryResponse;
import com.mmt.hotels.pojo.response.detail.HotelDetailWrapperResponse;
import com.mmt.hotels.pojo.response.detail.PlacesResponse;
import com.mmt.hotels.pojo.response.detail.RoomInfoData;
import com.mmt.hotels.pojo.response.detail.placesapi.Category;
import com.mmt.hotels.pojo.response.detail.placesapi.CategoryDatum;
import com.mmt.hotels.pojo.response.wishlist.FlyfishReviewWrapperResponse;
import com.mmt.hotels.pojo.response.wishlist.HotStoreHotelsWrapperResponse;
import com.mmt.model.RoomInfo;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.runners.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class StaticDetailsResponseTransformerPWATest {

    @InjectMocks
    StaticDetailResponseTransformerPWA staticDetailResponseTransformerPWA;

    @Spy
    ObjectMapperUtil objectMapperUtil = new ObjectMapperUtil();

    @Mock
    MetricAspect metricAspect;

    @Mock
    CommonResponseTransformer commonResponseTransformer;
    @Mock
    PolyglotService polyglotService;
    @Mock
    private Utility utility;

    @Before
    public void setup() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.writerWithView(PIIView.External.class);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
        ReflectionTestUtils.setField(staticDetailResponseTransformerPWA, "objectMapperUtil", objectMapperUtil);
        List<Integer> suppressedHouseRulesList=Arrays.asList(2,47);
        ReflectionTestUtils.setField(staticDetailResponseTransformerPWA, "supressedHouseRulesList",suppressedHouseRulesList);
        ReflectionTestUtils.setField(staticDetailResponseTransformerPWA, "foodDiningMergeSections", Arrays.asList("Kitchen"));
    }

    @Test
    public void testConvertStaticDetailResponse() throws JsonProcessingException {
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenAnswer(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                return invocationOnMock.getArgument(0);
            }
        });
        HotelDetailWrapperResponse hotelDetailWrapperResponse = new HotelDetailWrapperResponse();
        hotelDetailWrapperResponse.setHotelResult(new HotelResult());
        CalendarCriteria calendarCriteria = new CalendarCriteria();
        hotelDetailWrapperResponse.getHotelResult().setCalendarCriteria(calendarCriteria);
        hotelDetailWrapperResponse.getHotelResult().setCategories(new HashSet<String>());
        hotelDetailWrapperResponse.getHotelResult().setLatitude(10d);
        hotelDetailWrapperResponse.getHotelResult().setLongitude(10d);
        hotelDetailWrapperResponse.getHotelResult().setSignatureAmenities(Collections.singletonList(new AmenitiesList()));

        List<VideoInfo> hotelVideos = new ArrayList<VideoInfo>();
        VideoInfo videoInfo = new VideoInfo();
        videoInfo.setUrl("");
        hotelVideos.add(videoInfo );
        hotelDetailWrapperResponse.getHotelResult().setHotelVideos(hotelVideos );

        Map<String, ArrayList<Type>> users = new HashMap<String, ArrayList<Type>>();
        ArrayList<Type> types = new ArrayList<Type>();
        Type type = new Type();
        types.add(type);
        users.put("hotel", types);
        hotelDetailWrapperResponse.getHotelResult().setUsers(users );

        hotelDetailWrapperResponse.getHotelResult().setHouseRules(buildHouseRules());

        hotelDetailWrapperResponse.getHotelResult().setLuxe(true);

        hotelDetailWrapperResponse.setHotelImage(new HotelImage());
        hotelDetailWrapperResponse.getHotelImage().setImageDetails(new ImageType());
        hotelDetailWrapperResponse.getHotelImage().getImageDetails().setProfessional(new HashMap<String, List<ProfessionalImageEntity>>());
        hotelDetailWrapperResponse.getHotelImage().getImageDetails().getProfessional().put("H", new ArrayList<ProfessionalImageEntity>());
        hotelDetailWrapperResponse.getHotelImage().getImageDetails().getProfessional().get("H").add(new ProfessionalImageEntity());
        hotelDetailWrapperResponse.getHotelImage().getImageDetails().getProfessional().put("R", new ArrayList<>());
        hotelDetailWrapperResponse.getHotelImage().getImageDetails().getProfessional().get("R").add(new ProfessionalImageEntity());
        hotelDetailWrapperResponse.getHotelImage().getImageDetails().setTraveller(new HashMap<String, List<TravellerImageEntity>>());
        hotelDetailWrapperResponse.getHotelImage().getImageDetails().getTraveller().put("H", new ArrayList<TravellerImageEntity>());
        hotelDetailWrapperResponse.getHotelImage().getImageDetails().getTraveller().get("H").add(new TravellerImageEntity());


        hotelDetailWrapperResponse.setPointOfInterests(new ArrayList<PointOfInterest>());
        hotelDetailWrapperResponse.getPointOfInterests().add(new PointOfInterest());
        hotelDetailWrapperResponse.getPointOfInterests().get(0).setAerialDistance(10d);
        hotelDetailWrapperResponse.getPointOfInterests().get(0).setDrivingDistance(10d);
        hotelDetailWrapperResponse.getPointOfInterests().get(0).setDrivingTimeInSeconds(10d);
        hotelDetailWrapperResponse.getPointOfInterests().get(0).setLatitude(10d);
        hotelDetailWrapperResponse.getPointOfInterests().get(0).setLongitude(10d);
        hotelDetailWrapperResponse.getPointOfInterests().get(0).setPriority(10d);
        hotelDetailWrapperResponse.getPointOfInterests().get(0).setCategories(new ArrayList<>());
        hotelDetailWrapperResponse.getPointOfInterests().get(0).getCategories().add("abc");
        hotelDetailWrapperResponse.setFlyfishSummaryResponse(new CBFlyFishSummaryResponse());
        hotelDetailWrapperResponse.getFlyfishSummaryResponse().setSummary(new HashMap<>());
        StaffInfo staffInfo = new StaffInfo();
        Staff staff = new Staff();
        staff.setData(Arrays.asList(new StaffData()));
        staffInfo.setHost(staff);
        List<CommonRules> commonRules = new ArrayList<>();
        CommonRules commonRules1 = new CommonRules();
        commonRules1.setSubCategories(new ArrayList<>());
        commonRules1.getSubCategories().add(new CommonRules());
        commonRules.add(commonRules1);
        hotelDetailWrapperResponse.getHotelResult().setFoodAndDiningRules(commonRules);
        hotelDetailWrapperResponse.getHotelResult().setMustReadRules(Arrays.asList("This is must read rule"));
        hotelDetailWrapperResponse.getHotelResult().setStaffInfo(staffInfo);
        hotelDetailWrapperResponse.setRoomInfoData(new RoomInfoData());
        hotelDetailWrapperResponse.getRoomInfoData().setRoomInfoMap(new HashMap<>());
        hotelDetailWrapperResponse.getRoomInfoData().getRoomInfoMap().put("test", new RoomInfo());

        String ratingJson = getRatingJson();
        String abc = "                   \"altAccoSummary\": [\n" +
                "                        {\n" +
                "                            \"concept\": \"Facilities\",\n" +
                "                            \"value\": 4.0,\n" +
                "                            \"reviewCount\": 118\n" +
                "                        }\n" +
                "                    ]";
        hotelDetailWrapperResponse.getFlyfishSummaryResponse().getSummary().put(OTA.MMT, new ObjectMapper().readTree(ratingJson));
        ContextualizeReviews contextualizeReviews = new ContextualizeReviews();
        ContextualReviewData contextualReviewData = new ContextualReviewData();
        contextualReviewData.iconUrl = "url";
        List<ContextualReviewData> contextualReviewDataList = new ArrayList<>();
        contextualReviewDataList.add(contextualReviewData);
        contextualizeReviews.common = contextualReviewDataList;
        contextualizeReviews.highlighted = contextualReviewDataList;
        hotelDetailWrapperResponse.getFlyfishSummaryResponse().setContextualizedReviews(contextualizeReviews);
        objectMapperUtil.init();
        StaticDetailRequest staticDetailRequest = new StaticDetailRequest();
        staticDetailRequest.setFilterCriteria(new ArrayList<>());
        StaticDetailResponse staticDetailResponse = staticDetailResponseTransformerPWA.convertStaticDetailResponse(hotelDetailWrapperResponse, "PWA", staticDetailRequest, null);
        Assert.assertNotNull(staticDetailResponse);
        hotelDetailWrapperResponse.setHotelCompareResponse(new UpsellHotelDetailResponse());
        staticDetailResponse = staticDetailResponseTransformerPWA.convertStaticDetailResponse(hotelDetailWrapperResponse, "PWA", staticDetailRequest, null);
        Assert.assertNotNull(staticDetailResponse);

        staticDetailResponse = staticDetailResponseTransformerPWA.convertStaticDetailResponse(hotelDetailWrapperResponse, "PWA", staticDetailRequest, null);
        Assert.assertNotNull(staticDetailResponse);
        Assert.assertNotNull(staticDetailResponse.getHotelDetails());
        Assert.assertNull(staticDetailResponse.getHotelDetails().getCategoryUspDetailsText());

        hotelDetailWrapperResponse.getHotelResult().setCategoryUsp("BeachFront");
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Beachfront details page text");
        staticDetailResponse = staticDetailResponseTransformerPWA.convertStaticDetailResponse(hotelDetailWrapperResponse, "PWA", staticDetailRequest, null);
        Assert.assertNotNull(staticDetailResponse);
        Assert.assertNotNull(staticDetailResponse.getHotelDetails());
        //Assert.assertEquals(staticDetailResponse.getHotelDetails().getCategoryUspDetailsText(), "Beachfront details page text");

        hotelDetailWrapperResponse.getHotelResult().setHomeStayAwardsTitleText("Winner");
        hotelDetailWrapperResponse.getHotelResult().setHomeStayAwardImageUrl("xyz.com");
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice("DESKTOP");
        staticDetailRequest.setDeviceDetails(deviceDetails);
        hotelDetailWrapperResponse.getHotelResult().setAltAcco(true);
        staticDetailRequest.setFeatureFlags(new FeatureFlags());
        staticDetailRequest.getFeatureFlags().setLiteResponse(true);
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put("GALLERYV2", "T");
        Mockito.when(utility.getExpDataMap(Mockito.any())).thenReturn(expDataMap);
        staticDetailResponse = staticDetailResponseTransformerPWA.convertStaticDetailResponse(hotelDetailWrapperResponse, "PWA", staticDetailRequest, null);
        Assert.assertNotNull(staticDetailResponse);
        Assert.assertNotNull(staticDetailResponse.getHotelDetails());
        Assert.assertNotNull(staticDetailResponse.getHotelDetails().getHomeStayAwardDetails());

    }

    private String getRatingJson() {
        return "{\n" +
                "  \"cumulativeRating\": 4.4,\n" +
                "  \"totalReviewsCount\": 370,\n" +
                "  \"totalRatingCount\": 589,\n" +
                "  \"ratingBreakup\": {\n" +
                "    \"1\": 21,\n" +
                "    \"2\": 10,\n" +
                "    \"3\": 30,\n" +
                "    \"4\": 151,\n" +
                "    \"5\": 377\n" +
                "  },\n" +
                "  \"reviewBreakup\": {\n" +
                "    \"1\": 14,\n" +
                "    \"2\": 7,\n" +
                "    \"3\": 19,\n" +
                "    \"4\": 89,\n" +
                "    \"5\": 241\n" +
                "  },\n" +
                "  \"best\": [\n" +
                "    {\n" +
                "      \"publishDate\": \"Mar 29, 2021\",\n" +
                "      \"travellerName\": \"Rajat Maurya\",\n" +
                "      \"title\": \"Nice place to spend weekend\",\n" +
                "      \"rating\": 4,\n" +
                "      \"reviewText\": \"Perfect place to spend some quality time\",\n" +
                "      \"id\": \"P4Q2S5OIMYHNNC93F60FL4ADNH95FPGB03IPZUXDBKJA59ALRSPJH28CY2ARYU4GSDRFJLIXETX6\",\n" +
                "      \"travelType\": \"SOLO\",\n" +
                "      \"crawledData\": false\n" +
                "    }\n" +
                "  ],\n" +
                "  \"travellerRatingSummary\": {\n" +
                "    \"hotelSummary\": [\n" +
                "      {\n" +
                "        \"concept\": \"Safety and Hygiene\",\n" +
                "        \"value\": 4.5,\n" +
                "        \"show\": true,\n" +
                "        \"heroTag\": true,\n" +
                "        \"reviewCount\": 298\n" +
                "      },\n" +
                "      {\n" +
                "        \"concept\": \"Location\",\n" +
                "        \"displayText\": \"सस्थान/जगह\",\n" +
                "        \"value\": 4.7,\n" +
                "        \"show\": false,\n" +
                "        \"reviewCount\": 121,\n" +
                "        \"subConcepts\": [\n" +
                "          {\n" +
                "            \"sentiment\": \"POSITIVE\",\n" +
                "            \"subConcept\": \"Location\",\n" +
                "            \"displayText\": \"सस्थान/जगह\",\n" +
                "            \"relatedReviewCount\": 40,\n" +
                "            \"tagType\": \"BASE\",\n" +
                "            \"priorityScore\": 40\n" +
                "          }\n" +
                "        ]\n" +
                "      }\n" +
                "    ],\n" +
                "                   \"altAccoSummary\": [\n" +
                "                        {\n" +
                "                            \"concept\": \"Facilities\",\n" +
                "                            \"value\": 4.0,\n" +
                "                            \"reviewCount\": 118\n" +
                "                        }\n" +
                "                    ]," +
                "    \"roomSummary\": {\n" +
                "      \"309\": {\n" +
                "        \"cumulativeRating\": 0\n" +
                "      }\n" +
                "    }\n" +
                "  },\n" +
                "  \"crawledData\": false,\n" +
                "  \"cityCode\": \"DEL\",\n" +
                "  \"images\": [\n" +
                "    {\n" +
                "      \"imgUrl\": \"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/flyfish/raw/NH7308534571632/QS1042/QS1042-Q1/1570874747454.jpeg\",\n" +
                "      \"mmtTagList\": [\n" +
                "        \"Pool\",\n" +
                "        \"Outdoors\",\n" +
                "        \"Facade\"\n" +
                "      ],\n" +
                "      \"reviewId\": \"PR2ZH5ZIMOHP4S9DUJ9TRQBKVSDEA8XBGJH2QFO0I5OS22CDRCP5H2NC3ZHZMUJNS9RF3RIXYT2R\",\n" +
                "      \"travellerName\": \"ashish golcha\",\n" +
                "      \"reviewDate\": 1570874817000,\n" +
                "      \"imgTag\": \"Pool\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"travelTypes\": [\n" +
                "    \"COUPLE\"\n" +
                "  ],\n" +
                "\"travelTypeList\": [\n" +
                "   {\n" +
                "       \"travelType\": \"COUPLE\",\n" +
                "       \"displayText\": \"कपल/जोड़ा\"\n" +
                "   }\n" +
                "   ],\n" +
                "  \"sortingCriterion\": [\n" +
                "    \"Latest first\"\n" +
                "  ],\n" +
                "\"sortingCriterionList\": [\n" +
                "   {\n" +
                "       \"criteriaType\": \"Latest first\",\n" +
                "       \"displayText\": \"नवीनतम पहलेा\"\n" +
                "   }\n" +
                "   ],\n" +
                "  \"mmtImageTags\": [\n" +
                "    {\n" +
                "      \"name\": \"Safety & Hygiene\",\n" +
                "      \"count\": 23\n" +
                "    },\n" +
                "    {\n" +
                "      \"name\": \"Outdoors\",\n" +
                "      \"count\": 22\n" +
                "    }\n" +
                "  ],\n" +
                "  \"imageTypes\": [\n" +
                "    \"Room\"\n" +
                "  ],\n" +
                "  \"ratingText\": \"Excellent\",\n" +
                "  \"postLockdownData\": {\n" +
                "    \"rating\": 4.4,\n" +
                "    \"totalReviewCount\": 305,\n" +
                "    \"ratingCount\": 90,\n" +
                "    \"textCount\": 177,\n" +
                "    \"imageCount\": 7,\n" +
                "    \"imageTextCount\": 31\n" +
                "  },\n" +
                "  \"bestReviewTitle\": \"Top Reviews\",\n" +
                "  \"selectedCategory\": \"DEFAULT\"\n" +
                "}";
    }

	private HouseRules buildHouseRules() {
		HouseRules houseRule = new HouseRules();
		ChildExtraBedPolicy childExtraBedPolicy = new ChildExtraBedPolicy();
		List<PolicyRules> policyRules = new ArrayList<PolicyRules>();
		PolicyRules plcyRule = new PolicyRules();
		Set<ExtraBedRules> extraBedTerms = new HashSet<>();
		ExtraBedRules extrabed = new ExtraBedRules();
		extraBedTerms.add(extrabed );
		plcyRule.setExtraBedTerms(extraBedTerms );
		
		policyRules.add(plcyRule );
		childExtraBedPolicy.setPolicyRules(policyRules );
		houseRule.setChildExtraBedPolicy(childExtraBedPolicy );
		List<CommonRules> commonRules = new ArrayList<CommonRules>();
		CommonRules coomonRule = new CommonRules();
		List<Rule> rules = new ArrayList<Rule>();
		Rule rule = new Rule();
		rules.add(rule );
		coomonRule.setRules(rules );
		commonRules.add(coomonRule);
		houseRule.setCommonRules(commonRules );
		List<ChildExtraBedPolicy> extraBedPolicyList = new ArrayList<ChildExtraBedPolicy>();
		extraBedPolicyList.add(childExtraBedPolicy);
		houseRule.setExtraBedPolicyList(extraBedPolicyList );
		houseRule.setOtherInfo(commonRules);

        CategoryInfo breakfastCharges = new CategoryInfo();
        breakfastCharges.setCategoryDesc("Breakfast Charges");
        breakfastCharges.setCategoryName("Breakfast Charges");
        breakfastCharges.setRuleTableInfo(new RuleTableInfo());
        breakfastCharges.getRuleTableInfo().setKeyTitle("KEY TITLE");
        breakfastCharges.getRuleTableInfo().setValueTitle("VALUE TITLE");
        breakfastCharges.getRuleTableInfo().setInfoList(new ArrayList<>());
        breakfastCharges.getRuleTableInfo().getInfoList().add(new RuleInfo());
        breakfastCharges.getRuleTableInfo().getInfoList().get(0).setKey("K1");
        breakfastCharges.getRuleTableInfo().getInfoList().get(0).setValue(Arrays.asList("V1", "V2"));
        breakfastCharges.setId("BREAKFAST_CHARGES");

        CategoryInfo extraBedPolicy = new CategoryInfo();
        extraBedPolicy.setCategoryDesc("Extra Bed Policy");
        extraBedPolicy.setCategoryName("Extra Bed Policy");
        extraBedPolicy.setId("EXTRA_BED_POLICY");

        houseRule.setCategoryInfoList(Arrays.asList(breakfastCharges, extraBedPolicy));
		return houseRule;
	}

    @Test
    public void testConvertWishListedStaticDetailResponse() throws Exception {
        WishListedHotelsDetailRequest wishListedHotelsDetailRequest = objectMapperUtil.getObjectFromJson("{\"deviceDetails\":{\"appVersion\":\"8.6.4.RC1\",\"bookingDevice\":\"ANDROID\",\"deviceId\":\"311c8e73900a8d98\",\"deviceType\":\"Mobile\",\"networkType\":\"WiFi\",\"resolution\":\"xhdpi\"},\"requestDetails\":{\"channel\":\"Native\",\"couponCount\":3,\"funnelSource\":\"HOTELS\",\"idContext\":\"B2C\",\"loggedIn\":true,\"pageContext\":\"DETAIL\",\"siteDomain\":\"in\",\"visitNumber\":1,\"visitorId\":\"74856825370536007467679936889328709665\"},\"searchCriteria\":{\"hotelIds\":[\"201412311314381986\"],\"visitNumber\":1,\"visitorId\":\"74856825370536007467679936889328709665\",\"cityCode\":\"CTDEL\",\"locationId\":\"CTDEL\",\"locationType\":\"city\",\"countryCode\":\"IN\",\"roomStayCandidates\":[{\"adultCount\":2,\"childAges\":[],\"positionsForAgeLessThan1\":[]}]},\"imageDetails\":{\"categories\":[{\"count\":4,\"height\":252,\"imageFormat\":\"webp\",\"type\":\"H\",\"width\":459}],\"types\":[\"professional\",\"traveller\"]},\"reviewDetails\":{\"otas\":[\"MMT\",\"TA\",\"MANUAL\",\"OTHER\",\"EXT\"],\"tagTypes\":[\"BASE\",\"WHAT_GUESTS_SAY\"]}}", WishListedHotelsDetailRequest.class, DependencyLayer.ORCHESTRATOR);

        HotStoreHotelsWrapperResponse hotStoreHotelsWrapperResponse = objectMapperUtil.getObjectFromJson("{\"hotelResults\":[{\"addr1\":\"1048, Main Bazar, Paharganj, New Delhi\",\"addr2\":\"Paharganj\",\"checkIntime\":\"12 PM\",\"checkOutTime\":\"11 AM\",\"cityCtyCode\":\"IN\",\"cityType\":\"L\",\"country\":\"India\",\"accelerated\":false,\"hotelChainCode\":\"*********\",\"hotelChainName\":\"Affordable Hotel And Resorts\",\"roomIdToNameMap\":{},\"roomIdToSelectedAmenitiesIdList\":{},\"id\":\"201412311314381986\",\"name\":\"Hotel Aman Continental@All Staff Vaccinated\",\"pinCode\":\"110055\",\"starRating\":3,\"state\":\"Delhi\",\"hotelCityCd\":\"CTDEL\",\"valuePlus\":false,\"cityCode\":\"CTDEL\",\"cityName\":\"Delhi\",\"propertyType\":\"Hotel\",\"freeWifi\":true,\"featured\":\"N\",\"listingType\":\"room\",\"propertyRules\":[\"Delhi based id proof and PAN card are not considered as a valid id proofs.\\r\\nUnmarried couples not allowed\"],\"propertyActivities\":[],\"facilityHighlights\":[\"ATM\",\"Free Wi-Fi\",\"Kitchenette\",\"Elevator/Lift\",\"Air Conditioning\",\"24-hour Room Service\",\"Power Backup\",\"Restaurant\"],\"categories\":[\"MMT OFFER\",\"Quarantine Hotels\",\"Walking Distance from NDLS\",\"MySafety - Safe and Hygienic Stays\",\"Budget_context0\",\"Couple Friendly\",\"Deals for Vaccinated Travellers\",\"100% Vaccinated Staff\",\"Safety_Hygiene\",\"Inmarket\",\"Safe and Hygienic Stays\"],\"categoryDetails\":{\"MySafety - Safe and Hygienic Stays\":{\"title\":\"This property is following safety and hygiene measures\",\"iconUrl\":\"https://promos.makemytrip.com/COVID/safe.png\",\"itemIconType\":\"\",\"data\":[\"Hygienic Rooms\",\"Trained Staff\",\"Sanitized Indoors\",\"Safe Dining\"]}},\"couponAutoApply\":true,\"shortDescription\":\"Location: Nestled in the chaotic tapestry of medieval fortifications and mega malls, New Delhi and NCR. The hotel is very comfor \",\"primaryCity\":\"Delhi\",\"roomCodeToAmenitiesMap\":{},\"chatEnabled\":false,\"mmtPrime\":true,\"gdsHotelCode\":\"1000046478\",\"users\":{\"Reservation\":[{\"name\":\"Kapil Vyas\",\"chatEnabled\":false},{\"name\":\"Amanpreet\",\"chatEnabled\":false}],\"Owner\":[{\"name\":\"Amanpreet\",\"chatEnabled\":false}],\"Front Desk Manager\":[{\"name\":\"Mohan\",\"chatEnabled\":false}],\"Rate and Inventory Manager\":[{\"name\":\"Rate Inventory Manager\",\"chatEnabled\":false}]},\"staffInfo\":{\"isStarHost\":false,\"chatEnabled\":false,\"host\":{\"header\":\"Property owner/manager who’d make you feel at home\",\"data\":[{\"name\":\"Kapil Vyas\",\"generalInfo\":[{\"text\":\"Shares Space with guests\",\"iconUrl\":\"https://promos.makemytrip.com/altaccoimages/icons/shares_spaces.png\"}]},{\"name\":\"Amanpreet\",\"generalInfo\":[{\"text\":\"Shares Space with guests\",\"iconUrl\":\"https://promos.makemytrip.com/altaccoimages/icons/shares_spaces.png\"}]},{\"name\":\"Amanpreet\",\"generalInfo\":[{\"text\":\"Shares Space with guests\",\"iconUrl\":\"https://promos.makemytrip.com/altaccoimages/icons/shares_spaces.png\"}]},{\"name\":\"Mohan\",\"generalInfo\":[{\"text\":\"Shares Space with guests\",\"iconUrl\":\"https://promos.makemytrip.com/altaccoimages/icons/shares_spaces.png\"}]},{\"name\":\"Rate Inventory Manager\",\"generalInfo\":[{\"text\":\"Shares Space with guests\",\"iconUrl\":\"https://promos.makemytrip.com/altaccoimages/icons/shares_spaces.png\"}]}]}},\"stateCode\":\"STDEL\",\"areas\":[{\"id\":\"ARCENTRALDE\",\"name\":\"Central Delhi\",\"type\":\"locality\",\"centre\":{\"coordinates\":[77.21668,28.66434],\"type\":\"Point\"}},{\"id\":\"ARNEARNE\",\"name\":\"Near New Delhi Train Station\",\"type\":\"locality\",\"centre\":{\"coordinates\":[77.22081,28.64155],\"type\":\"Point\"}},{\"id\":\"ARRATANL\",\"name\":\"Ratan Lal Market\",\"type\":\"sublocality\",\"centre\":{\"coordinates\":[77.2163,28.6431],\"type\":\"Point\"}},{\"id\":\"ARARAMB\",\"name\":\"Aram Bagh\",\"type\":\"sublocality\",\"centre\":{\"coordinates\":[77.21169,28.64159],\"type\":\"Point\"}},{\"id\":\"ARKASERUW\",\"name\":\"Kaseru Walan\",\"type\":\"sublocality\",\"centre\":{\"coordinates\":[77.21576,28.64223],\"type\":\"Point\"}},{\"id\":\"ARPAH\",\"name\":\"Paharganj\",\"type\":\"locality\",\"centre\":{\"coordinates\":[77.21299,28.64804],\"type\":\"Point\"}}],\"regions\":[{\"id\":\"RGNCR\",\"name\":\"New Delhi and NCR\",\"type\":\"region\",\"centre\":{\"coordinates\":[77.26255,28.48283],\"type\":\"Point\"}}],\"usp\":{\"Location\":{\"sub_title\":\"The hotel is located close to New Delhi railway station\",\"title\":\"Location\",\"subTags\":null}},\"dynamicFilters\":[\"Location\"],\"locationId\":\"CTDEL\",\"facilityCategorization\":[{\"subcat\":\"Masks\",\"name\":\"hotel\"},{\"subcat\":\"Gloves\",\"name\":\"hotel\"},{\"subcat\":\"Sanitizers\",\"name\":\"hotel\"},{\"subcat\":\"Sanitizers installed\",\"name\":\"hotel\"},{\"subcat\":\"Disinfection\",\"name\":\"hotel\"},{\"subcat\":\"Contactless room service\",\"name\":\"hotel\"},{\"subcat\":\"Contactless check-in\",\"name\":\"hotel\"},{\"subcat\":\"Wifi\",\"name\":\"hotel\"},{\"subcat\":\"Kitchen/Kitchenette\",\"name\":\"hotel\"},{\"subcat\":\"Elevator/ Lift\",\"name\":\"hotel\"},{\"subcat\":\"Air Conditioning\",\"name\":\"hotel\"},{\"subcat\":\"Room service\",\"name\":\"hotel\"},{\"subcat\":\"Power backup\",\"name\":\"hotel\"},{\"subcat\":\"Dry Cleaning services\",\"name\":\"hotel\"},{\"subcat\":\"Smoking rooms\",\"name\":\"hotel\"},{\"subcat\":\"Smoke detector\",\"name\":\"hotel\"},{\"subcat\":\"Intercom\",\"name\":\"hotel\"},{\"subcat\":\"Refrigerator\",\"name\":\"hotel\"},{\"subcat\":\"Housekeeping\",\"name\":\"hotel\"},{\"subcat\":\"Public restrooms\",\"name\":\"hotel\"},{\"subcat\":\"Umbrellas\",\"name\":\"hotel\"},{\"subcat\":\"Laundry\",\"name\":\"hotel\"},{\"subcat\":\"Telephone\",\"name\":\"hotel\"},{\"subcat\":\"Bathroom\",\"name\":\"hotel\"},{\"subcat\":\"Ironing services\",\"name\":\"hotel\"},{\"subcat\":\"Newspaper\",\"name\":\"hotel\"},{\"subcat\":\"Public transit tickets\",\"name\":\"hotel\"},{\"subcat\":\"Restaurant\",\"name\":\"hotel\"},{\"subcat\":\"Dining Area\",\"name\":\"hotel\"},{\"subcat\":\"ATM\",\"name\":\"hotel\"},{\"subcat\":\"Security\",\"name\":\"hotel\"},{\"subcat\":\"Safe\",\"name\":\"hotel\"},{\"subcat\":\"First-aid services\",\"name\":\"hotel\"},{\"subcat\":\"Electrical chargers\",\"name\":\"hotel\"},{\"subcat\":\"TV\",\"name\":\"hotel\"},{\"subcat\":\"Luggage storage\",\"name\":\"hotel\"},{\"subcat\":\"Mail services\",\"name\":\"hotel\"},{\"subcat\":\"Luggage assistance\",\"name\":\"hotel\"},{\"subcat\":\"Doctor on call\",\"name\":\"hotel\"},{\"subcat\":\"Ticket/ Tour Assistance\",\"name\":\"hotel\"},{\"subcat\":\"Bellboy service\",\"name\":\"hotel\"},{\"subcat\":\"Caretaker\",\"name\":\"hotel\"},{\"subcat\":\"Wake-up Call / Service\",\"name\":\"hotel\"},{\"subcat\":\"Electrical Sockets\",\"name\":\"hotel\"},{\"subcat\":\"Specially abled assistance\",\"name\":\"hotel\"},{\"subcat\":\"Wheelchair\",\"name\":\"hotel\"},{\"subcat\":\"Postal services\",\"name\":\"hotel\"},{\"subcat\":\"Multilingual Staff\",\"name\":\"hotel\"},{\"subcat\":\"Reception\",\"name\":\"hotel\"},{\"subcat\":\"Balcony/ Terrace\",\"name\":\"hotel\"},{\"subcat\":\"Printer\",\"name\":\"hotel\"}],\"roomCodeToVideosMap\":{},\"roomCodeToImageMap\":{},\"bestOffersEnabled\":\"Y\",\"alternateDatesAvailable\":false,\"specialRequestCodesAvailable\":[\"101\",\"102\",\"103\",\"104\",\"105\",\"106\",\"107\",\"108\",\"109\"],\"pahAuthenticationEnabled\":false,\"houseRules\":{\"commonRules\":[{\"category\":\"Safety and Hygiene\",\"id\":\"SafetyandHygiene\",\"showInHost\":true,\"hostCatHeading\":\"COVID-19 related queries\",\"showInDetailHome\":false,\"expandRules\":false,\"rules\":[{\"text\":\"Quarantine protocols are being followed as per local government authorities\"},{\"text\":\"Guests from containment zones are not allowed\"},{\"text\":\"Shared resources in common areas are properly sanitized\"},{\"text\":\"Property staff is trained on hygiene guidelines\"},{\"text\":\"Hand sanitizer is provided in guest accommodation and common areas\"},{\"text\":\"Thermal screening is done at entry and exit points\"}]},{\"category\":\"Guest Profile\",\"id\":\"GuestProfile\",\"showInHost\":false,\"showInDetailHome\":false,\"expandRules\":false,\"rules\":[{\"text\":\"Unmarried couples allowed\"},{\"text\":\"Bachelors allowed\"},{\"text\":\"Guests below 18 years of age are not allowed at the property.\"},{\"text\":\"Suitable for children\"}]},{\"category\":\"Room Safety and Hygiene\",\"id\":\"RoomSafetyandHygiene\",\"showInHost\":false,\"showInDetailHome\":false,\"expandRules\":false,\"rules\":[{\"text\":\"All rooms are disinfected using bleach or other disinfectant\"},{\"text\":\"Linens, towels, and laundry are washed as per local guidelines\"},{\"text\":\"Rooms are properly sanitized between stays\"},{\"text\":\"Hand sanitizers are available in the rooms.\"}]},{\"category\":\"Payment Related\",\"id\":\"PaymentRelated\",\"showInHost\":false,\"showInDetailHome\":false,\"expandRules\":false,\"rules\":[{\"text\":\"Credit/debit cards are accepted\"},{\"text\":\"Master Card, American Express and Visa cards are accepted\"}]},{\"category\":\"Food and Drinks Hygiene\",\"id\":\"FoodandDrinksHygiene\",\"showInHost\":false,\"showInDetailHome\":false,\"expandRules\":false,\"rules\":[{\"text\":\"COVID-19 guidelines for Food Hygiene is followed as per government guidelines\"},{\"text\":\"Social distancing is followed in restaurants\"},{\"text\":\"Serveware and supplies are sanitized before they are brought to the kitchen\"},{\"text\":\"Masks and hairnets are mandatory for staff in restaurants\"}]},{\"category\":\"Smoking/Alcohol consumption Rules\",\"id\":\"SmokingAlcoholconsumptionRules\",\"showInHost\":false,\"showInDetailHome\":false,\"expandRules\":false,\"rules\":[{\"text\":\"Smoking within the premises is allowed\"}]},{\"category\":\"Property Accessibility\",\"id\":\"PropertyAccessibility\",\"showInHost\":false,\"showInDetailHome\":false,\"expandRules\":false,\"rules\":[{\"text\":\"The property is Elderly-friendly/Disabled-friendly \"},{\"text\":\"Bed height is accessible\"},{\"text\":\"The entire unit is wheelchair accessible\"},{\"text\":\"The property has a wide entryway\"}]},{\"category\":\"Pet(s) Related\",\"id\":\"PetsRelated\",\"showInHost\":false,\"showInDetailHome\":false,\"expandRules\":false,\"rules\":[{\"text\":\"Pets are not allowed.\"},{\"text\":\"There are no pets living on the property\"}]},{\"category\":\"Physical Distancing\",\"id\":\"PhysicalDistancing\",\"showInHost\":false,\"showInDetailHome\":false,\"expandRules\":false,\"rules\":[{\"text\":\"Social distancing protocols are followed\"},{\"text\":\"Contactless Check-In and Checkout service is available\"},{\"text\":\"Contactless Room service is available\"},{\"text\":\"Physical Barriers are deployed at appropriate places\"},{\"text\":\"Cashless Payment is available\"}]},{\"category\":\"ID Proof Related\",\"id\":\"IDProofRelated\",\"showInHost\":false,\"showInDetailHome\":false,\"expandRules\":false,\"rules\":[{\"text\":\"Passport, Aadhar, Govt. ID and Driving License are accepted as ID proof(s)\"},{\"text\":\"Office ID, PAN Card and Non-Govt IDs are not accepted as ID proof(s)\"},{\"text\":\"Local ids are allowed\"}]},{\"category\":\"Directions to reach the property\",\"id\":\"Directionstoreachtheproperty\",\"showInHost\":false,\"showInDetailHome\":false,\"expandRules\":false,\"rules\":[{\"text\":\"The property is located at a distance of 0.2 KMs from the New Delhi Railway Station. From New Delhi Railway Station, take a walk of 150 meters with the easy trek. From 24, Paharganj Road, Sidarth Basti, Aram Bagh, Paharganj, New Delhi, Delhi 110055, India, take a walk of 100 meters with the easy trek. The estimated travel fare from the New Delhi Railway Station to the property is 10.0 INR and total travel time is 2 minutes.\"}]},{\"category\":\"Other Rules\",\"id\":\"OtherRules\",\"showInHost\":false,\"showInDetailHome\":false,\"expandRules\":false,\"rules\":[{\"text\":\"Allows private parties or events\"}]}],\"contextRules\":{\"category\":\"Couple Friendly\",\"title\":\"Couple, Bachelor Rules\",\"desc\":\"Unmarried couples/guests with Local IDs are allowed.\",\"tag\":\"Couple Friendly\",\"ruleIcon\":\"https://promos.makemytrip.com/Hotels_product/Details/Couplefriendly2x.png\"}},\"mustReadRules\":[\"Guests from containment zones are not allowed\",\"Guests below 18 years of age are not allowed at the property.\",\"Office ID, PAN Card and Non-Govt IDs are not accepted as ID proof(s)\",\"Passport, Aadhar, Govt. ID and Driving License are accepted as ID proof(s)\",\"Property staff is trained on hygiene guidelines\",\"Pets are not allowed.\",\"Allows private parties or events\",\"Quarantine protocols are being followed as per local government authorities\"],\"detailDeeplinkUrl\":\"&locusId=CTDEL&locusType=city&checkAvailability=true&region=in\",\"bedCount\":0,\"roomCount\":0,\"stayType\":\"Hotel\",\"stayTextWithSize\":\"Hotel\",\"maxOccupancy\":0,\"ignoreEntireProperty\":true,\"oldCityCode\":\"LC_CTDEL\",\"oldCountryCode\":\"IN\",\"emiBucket\":\"\",\"areaIdList\":[\"ARCENTRALDE\",\"ARNEARNE\",\"ARRATANL\",\"ARARAMB\",\"ARKASERUW\",\"ARPAH\"],\"locationName\":\"Delhi\",\"locationType\":\"city\",\"drivingDistance\":0.0,\"primaryArea\":\"Paharganj\",\"bestPoiText\":\"2.8 km from Jantar Mantar\",\"poiTagList\":[{\"hotelId\":\"201412311314381986\",\"poiId\":\"POI47288\",\"poiName\":\"Jantar Mantar\",\"poiCategory\":\"Tourist Attraction\",\"poiPriority\":\"1\",\"drivingDistance\":\"2800.0\",\"drivingDistanceText\":\"2.8 km\",\"drivingTimeText\":\"\",\"bestPoiPriority\":0}],\"locationPersuasion\":[\"Paharganj\",\"2.8 km from Jantar Mantar\"],\"propertyTypeId\":\"72\",\"contextType\":\"ALL\",\"requestToBook\":false,\"groupBookingAllowed\":false,\"gstStateCode\":\"07\",\"contextualPersuasions\":{},\"foodAndDiningRules\":[],\"rtbPreApprovedSegments\":[],\"altAcco\":false,\"packageHotel\":false,\"emiblocked\":false,\"dnd\":false,\"budgetHotel\":false,\"gds\":true,\"groupBookingHotel\":false,\"hotelFacility\":[\"Safety and Hygiene\",\"Basic Facilities\",\"Transfers\",\"Food and Drinks\",\"Payment Services\",\"Safety and Security\",\"Health and wellness\",\"Media and technology\",\"General Services\",\"Common Area\",\"Business Center and Conferences\"],\"lat\":28.64163,\"lng\":77.21613,\"isBudgetHotel\":false,\"isWishListed\":false}],\"hotelIdToHotelImageMap\":{\"201412311314381986\":{\"hotelId\":\"201412311314381986\",\"imageDetails\":{\"traveller\":{\"H\":[{\"title\":null,\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/flyfish/raw/NH7308534360136/QS1042/QS1042-Q1/IMG_20190918_220656.jpg\",\"imageSequencing\":null,\"imageFilterInfo\":\"Outdoors\",\"date\":\"2019-10-01 00:36:55\",\"travellerName\":\"vivek yadav\"},{\"title\":null,\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/flyfish/raw/NH7405048160196/QS1042/QS1042-Q1/IMG-20200220-WA0002.jpg\",\"imageSequencing\":null,\"imageFilterInfo\":\"Room\",\"date\":\"2020-02-20 00:42:20\",\"travellerName\":\"Vasuki G Hegde\"},{\"title\":null,\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/flyfish/raw/NH7308534360136/QS1042/QS1042-Q1/IMG_20190918_220358.jpg\",\"imageSequencing\":null,\"imageFilterInfo\":\"Outdoors\",\"date\":\"2019-10-01 00:36:55\",\"travellerName\":\"vivek yadav\"},{\"title\":null,\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/flyfish/raw/NH2316549391286/QS1042/QS1042-Q1/IMG_20200203_134313.jpg\",\"imageSequencing\":null,\"imageFilterInfo\":\"Room\",\"date\":\"2020-02-13 17:37:56\",\"travellerName\":\"Mukesh Kumar\"}]},\"professional\":{\"H\":[{\"title\":\"Guest Room\",\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201412311314381986-e9bad9f0669311e58aa3daf4768ad8d9.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Room\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Guest Room\",\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201412311314381986-d98bf6a407f611eb81ce0242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Safety and Hygiene\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Staff Hygiene\",\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201412311314381986-e0d893c607f711ebb6060242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Safety and Hygiene\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Staff Hygiene\",\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201412311314381986-e147c5b607f711ebb6190242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Safety and Hygiene\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Temperature Check\",\"url\":\"//r1imghtlak.mmtcdn.com/777a6286063211eb85ea0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,21&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Safety and Hygiene\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Reception\",\"url\":\"//r1imghtlak.mmtcdn.com/7ee9545c063011eb88250242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Safety and Hygiene\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Reception\",\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201412311314381986-0a6580c807f811eba7180242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Safety and Hygiene\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Room service\",\"url\":\"//r1imghtlak.mmtcdn.com/befc3e10063011eba73d0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,72&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Safety and Hygiene\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Staff Hygiene\",\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201412311314381986-e19066c207f711eba42f0242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Safety and Hygiene\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Restaurant\",\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201412311314381986-da8a9f9c07f611ebb6190242ac110002.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Safety and Hygiene\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Guest Room\",\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201412311314381986-e0ae61a007f711eb98e70242ac110003.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,24&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Room\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Restaurant\",\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201412311314381986-5eaca196cbe611e58c91001ec9b85d13.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,14&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Lobby/Common Area\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Rooftop Restaurant\",\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201412311314381986-306fa2facbe711e59ca8001ec9b85d13.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,16&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Lobby/Common Area\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Guest Room\",\"url\":\"//r1imghtlak.mmtcdn.com/5b471dda669511e5af91daf4768ad8d9.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Room\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Lobby Area\",\"url\":\"//r1imghtlak.mmtcdn.com/9e9f9824669911e5a2af36cfdd80c293.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,17&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Lobby/Common Area\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Rooftop Restaurant\",\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201412311314381986-6db4e436cbe711e597535ee5da2daa2a.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,16&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Swimming Pool\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Guest Room\",\"url\":\"//r1imghtlak.mmtcdn.com/8e0bb4b0669511e5a022daf4768ad8d9.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Lobby/Common Area\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Guest Room\",\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201412311314381986-aa3bee0ccbe911e5b5fb5ee5da2daa2a.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,20&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Room\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Guest Room\",\"url\":\"//r1imghtlak.mmtcdn.com/c2e86728669511e5b352daf4768ad8d9.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Room\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Guest Room\",\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201412311314381986-dec68842669711e5a15636cfdd80c293.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,26&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Room\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Guest Room\",\"url\":\"//r1imghtlak.mmtcdn.com/99aad90c669711e5b7d636cfdd80c293.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Room\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Reception\",\"url\":\"//r1imghtlak.mmtcdn.com/2c2dd698669911e5a06f36cfdd80c293.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,19&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Reception\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Guest Room\",\"url\":\"//r1imghtlak.mmtcdn.com/f2fef540cbe811e5ad715ee5da2daa2a.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,26&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Room\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Reception\",\"url\":\"//r1imghtlak.mmtcdn.com/12617478669811e5a3fe36cfdd80c293.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,23&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Reception\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Guest Room\",\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201412311314381986-7b754b72cbe911e5ae075ee5da2daa2a.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,20&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Room\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Restaurant\",\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201412311314381986-ec4f62a0cbe511e5bb09001ec9b85d13.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,19&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Lobby/Common Area\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Washroom\",\"url\":\"//r1imghtlak.mmtcdn.com/fa16b70e669511e59d15daf4768ad8d9.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Washroom\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Washroom\",\"url\":\"//r1imghtlak.mmtcdn.com/69b74acc669311e5af91daf4768ad8d9.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Washroom\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Guest Room\",\"url\":\"//r1imghtlak.mmtcdn.com/c731f0c8669611e59342daf4768ad8d9.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Room\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Guest Room\",\"url\":\"//r1imghtlak.mmtcdn.com/3b2863a0669611e590e2daf4768ad8d9.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Room\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Guest Room\",\"url\":\"//r1imghtlak.mmtcdn.com/25a89d58669411e5b81bdaf4768ad8d9.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Room\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Washroom\",\"url\":\"//r1imghtlak.mmtcdn.com/2d5538f6669711e5878cdaf4768ad8d9.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Washroom\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Guest Room\",\"url\":\"//r1imghtlak.mmtcdn.com/5f14c5a0669711e59631daf4768ad8d9.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Room\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Reception\",\"url\":\"//r1imghtlak.mmtcdn.com/48c681a2669811e5b09236cfdd80c293.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,22&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Reception\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Reception\",\"url\":\"//r1imghtlak.mmtcdn.com/7b57f646669811e5a15636cfdd80c293.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,27&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Reception\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Lobby\",\"url\":\"//r1imghtlak.mmtcdn.com/b643a7b4669811e5a87f36cfdd80c293.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,23&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Lobby/Common Area\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Lobby\",\"url\":\"//r1imghtlak.mmtcdn.com/64bbe0e0669911e5a35436cfdd80c293.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,13&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Lobby/Common Area\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Restaurant\",\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201412311314381986-7855ffeecbe511e5b0be001ec9b85d13.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,26&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Dining Area\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Restaurant\",\"url\":\"//r1imghtlak.mmtcdn.com/b3c9b00ccbe511e58c38001ec9b85d13.jfif?&output-quality=75&downsize=459:252&crop=459:252;0,26&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Dining Area\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null},{\"title\":\"Restaurant\",\"url\":\"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201412311314381986-2508305ecbe611e5b28a001ec9b85d13.jpg?&output-quality=75&downsize=459:252&crop=459:252;0,12&output-format=webp\",\"imageSequencing\":null,\"imageFilterInfo\":\"Lobby/Common Area\",\"catCode\":null,\"thumbnailURL\":null,\"seekTags\":null}]},\"panoramic\":{}}}},\"correlationKey\":\"c96ce2fc-4936-4440-b69b-ca0c5a7e1d14\"}", HotStoreHotelsWrapperResponse.class, DependencyLayer.ORCHESTRATOR);

        WishListedHotelsDetailResponse wishListedHotelsDetailResponse = staticDetailResponseTransformerPWA.convertWishListedStaticDetailResponse(hotStoreHotelsWrapperResponse, new FlyfishReviewWrapperResponse(), "PWA", wishListedHotelsDetailRequest, null, new UserReviewResponseForListing());
        Assert.assertNotNull(wishListedHotelsDetailResponse);
    }

    @Test
    public void testSuppressHouseRuleNodesDayUseFunnel() throws JsonProcessingException {
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenAnswer(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                return invocationOnMock.getArgument(0);
            }
        });
        HotelDetailWrapperResponse hotelDetailWrapperResponse = new HotelDetailWrapperResponse();
        hotelDetailWrapperResponse.setHotelResult(new HotelResult());
        hotelDetailWrapperResponse.getHotelResult().setCategories(new HashSet<String>());
        hotelDetailWrapperResponse.getHotelResult().setHouseRules(buildHouseRules());
        hotelDetailWrapperResponse.getHotelResult().getHouseRules().getCommonRules().get(0).setCategoryId(47);
        List<CommonRules> commonRules = new ArrayList<>();
        CommonRules commonRules1 = new CommonRules();
        commonRules1.setSubCategories(new ArrayList<>());
        commonRules1.getSubCategories().add(new CommonRules());
        commonRules.add(commonRules1);
        hotelDetailWrapperResponse.getHotelResult().setFoodAndDiningRules(commonRules);
        hotelDetailWrapperResponse.getHotelResult().setMustReadRules(Arrays.asList("This is must read rule"));
        hotelDetailWrapperResponse.setRoomInfoData(new RoomInfoData());
        hotelDetailWrapperResponse.getRoomInfoData().setRoomInfoMap(new HashMap<>());
        hotelDetailWrapperResponse.getRoomInfoData().getRoomInfoMap().put("test", new RoomInfo());

        objectMapperUtil.init();
        StaticDetailRequest staticDetailRequest = new StaticDetailRequest();
        staticDetailRequest.setRequestDetails(new RequestDetails());
        staticDetailRequest.getRequestDetails().setFunnelSource("DAYUSE");
        staticDetailRequest.setDeviceDetails(new DeviceDetails());
        staticDetailRequest.setSearchCriteria(new StaticDetailCriteria());
        staticDetailRequest.setFilterCriteria(new ArrayList<>());
        hotelDetailWrapperResponse.getHotelResult().setFoodDining(new ArrayList<>());
        CommonRules kitchenSection = new CommonRules();
        kitchenSection.setImages(new ArrayList<>());
        kitchenSection.setRules(new ArrayList<>());
        Rule rule = new Rule();
        rule.setText("test");
        kitchenSection.getRules().add(rule);
        kitchenSection.setCategory("Kitchen");
        hotelDetailWrapperResponse.getHotelResult().getFoodDining().add(kitchenSection);
        StaticDetailResponse staticDetailResponse = staticDetailResponseTransformerPWA.convertStaticDetailResponse(hotelDetailWrapperResponse, "PWA", staticDetailRequest, null);
        hotelDetailWrapperResponse.getHotelResult().getFoodDining().get(0).getRules().add(rule);
        hotelDetailWrapperResponse.getHotelResult().getFoodDining().get(0).getRules().add(rule);
        hotelDetailWrapperResponse.getHotelResult().getFoodDining().get(0).getRules().add(rule);
        hotelDetailWrapperResponse.getHotelResult().getFoodDining().get(0).getRules().add(rule);
        hotelDetailWrapperResponse.getHotelResult().setShowCallToBook(true);
        hotelDetailWrapperResponse.getHotelResult().setActiveButOffline(true);
        staticDetailResponse = staticDetailResponseTransformerPWA.convertStaticDetailResponse(hotelDetailWrapperResponse, "PWA", staticDetailRequest, null);
        kitchenSection.setCategory("Cook");
        hotelDetailWrapperResponse.getHotelResult().getFoodDining().add(kitchenSection);

        CommonRules mealSection = new CommonRules();
        mealSection.setRules(new ArrayList<>());
        mealSection.setCategory("Meals");
        mealSection.getRules().add(rule);
        mealSection.getRules().add(rule);
        mealSection.getRules().add(rule);

        CommonRules foodMenuImages = new CommonRules();
        foodMenuImages.setRules(new ArrayList<>());
        foodMenuImages.setCategory("Food Menu");
        rule.setImages(new ArrayList<>());
        rule.getImages().add("image.jpg");
        rule.setImageCategory("Menu");
        foodMenuImages.getRules().add(rule);
        hotelDetailWrapperResponse.getHotelResult().getFoodDining().add(mealSection);
        hotelDetailWrapperResponse.getHotelResult().getFoodDining().add(foodMenuImages);

        staticDetailResponse = staticDetailResponseTransformerPWA.convertStaticDetailResponse(hotelDetailWrapperResponse, "PWA", staticDetailRequest, null);
        Assert.assertNotNull(staticDetailResponse);

        Mockito.when(utility.isExperimentOn(Mockito.anyMap(), Mockito.anyString())).thenReturn(true);
        hotelDetailWrapperResponse.setChainCompareResponse(new UpsellHotelDetailResponse());
        staticDetailResponse = staticDetailResponseTransformerPWA.convertStaticDetailResponse(hotelDetailWrapperResponse, "PWA", staticDetailRequest, null);

    }

    @Test
    public void testHouseRulesInStaticDetails() {
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenAnswer(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                return invocationOnMock.getArgument(0);
            }
        });
        HotelDetailWrapperResponse hotelDetailWrapperResponse = new HotelDetailWrapperResponse();
        hotelDetailWrapperResponse.setHotelResult(new HotelResult());
        hotelDetailWrapperResponse.getHotelResult().setCategories(new HashSet<String>());
        hotelDetailWrapperResponse.getHotelResult().setHouseRules(buildHouseRules());
        hotelDetailWrapperResponse.getHotelResult().getHouseRules().getCommonRules().get(0).setCategoryId(47);

        StaticDetailRequest staticDetailRequest = new StaticDetailRequest();
        staticDetailRequest.setRequestDetails(new RequestDetails());
        staticDetailRequest.getRequestDetails().setFunnelSource("DAYUSE");
        staticDetailRequest.setDeviceDetails(new DeviceDetails());
        staticDetailRequest.setSearchCriteria(new StaticDetailCriteria());
        staticDetailRequest.setFilterCriteria(new ArrayList<>());

        StaticDetailResponse staticDetailResponse = staticDetailResponseTransformerPWA.convertStaticDetailResponse(hotelDetailWrapperResponse, "PWA", staticDetailRequest, null);
        Assert.assertNotNull(staticDetailResponse);
    }

    @Test
    public void buildGovtPoliesTest() {
        HotelResult hotel = new HotelResult();
        com.mmt.hotels.clientgateway.response.staticdetail.HotelResult hotelResult = new com.mmt.hotels.clientgateway.response.staticdetail.HotelResult();
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "buildGovtPolies", hotelResult, hotel);
        org.junit.Assert.assertNull(hotelResult.getGovtPolicies());

        GovtPolicies govtPoliciesContent = new GovtPolicies();
        govtPoliciesContent.setTitle("Title");
        hotel.setGovtPolicies(new ArrayList<>());
        hotel.getGovtPolicies().add(govtPoliciesContent);
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "buildGovtPolies", hotelResult, hotel);
        org.junit.Assert.assertNotNull(hotelResult.getGovtPolicies());
        org.junit.Assert.assertEquals(hotelResult.getGovtPolicies().size(), 1);

        hotel.getGovtPolicies().add(govtPoliciesContent);
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "buildGovtPolies", hotelResult, hotel);
        org.junit.Assert.assertNotNull(hotelResult.getGovtPolicies());
        org.junit.Assert.assertEquals(hotelResult.getGovtPolicies().size(), 2);

    }

    @Test
    public void getPropertyChainTest() {
        PropertyChain propertyChain = new PropertyChain();
        PropertyChainDetails detail = new PropertyChainDetails();
        List<Map<String, String>> desc = new ArrayList<>();
        desc.add(new HashMap<>());
        detail.setDesc(desc);
        detail.setTitle("Test Title");
        propertyChain.setDetails(detail);
        propertyChain.setLogo("Test Logo");
        Map<String, String> summary = new HashMap<>();
        propertyChain.setSummary(summary);
        PropertyChainCG res = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "getPropertyChain", propertyChain);
        org.junit.Assert.assertNotNull(res);
        org.junit.Assert.assertEquals(res.getDetails().getTitle(), "Test Title");
        org.junit.Assert.assertEquals(res.getLogo(), "Test Logo");
    }

    @Test
    public void getPropertyHighLightstest() {
        PropertyHighlights propertyHighlights = new PropertyHighlights();
        List<PropertyHighlightDetails> detail = new ArrayList<>();
        PropertyHighlightDetails det = new PropertyHighlightDetails();
        det.setIcon("icon");
        det.setDesc("desc");
        det.setTitle("title");
        detail.add(det);
        propertyHighlights.setDetails(detail);
        propertyHighlights.setIcon("Test Icon");
        propertyHighlights.setTitle("Test Title");
        PropertyHighlightCG res = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "getPropertyHighLights", propertyHighlights);
        org.junit.Assert.assertNotNull(res);
        org.junit.Assert.assertEquals(res.getTitle(), "Test Title");
        org.junit.Assert.assertEquals(res.getDetails().get(0).getIcon(), "icon");
        org.junit.Assert.assertEquals(res.getDetails().get(0).getDesc(), "desc");
        org.junit.Assert.assertEquals(res.getDetails().get(0).getTitle(), "title");
    }

    @Test
    public void buildSupportDetailsForABO_shouldReturnSupportDetailsWithCallToBookOption() {
        SupportDetails result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "buildSupportDetailsForABO");
        org.junit.Assert.assertNotNull(result);
        org.junit.Assert.assertEquals(1, result.getOptions().size());
        org.junit.Assert.assertEquals("callToBook", result.getOptions().get(0));
    }

    @Test
    public void buildReportCardPersuasion_ReturnsReportCardPersuasion_WithValidData() {
        PersuasionData persuasionData = new PersuasionData();
        persuasionData.setIconurl("http://example.com/icon.png");
        persuasionData.setText("Sample Text");

        ReportCardPersuasion result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "buildReportCardPersuasion", persuasionData);

        Assert.assertNotNull(result);
        Assert.assertEquals("http://example.com/icon.png", result.getIconUrl());
        Assert.assertEquals("Sample Text", result.getText());
    }

    @Test
    public void buildReportCardPersuasion_ReturnsReportCardPersuasion_WithNullIconUrl() {
        PersuasionData persuasionData = new PersuasionData();
        persuasionData.setIconurl(null);
        persuasionData.setText("Sample Text");

        ReportCardPersuasion result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "buildReportCardPersuasion", persuasionData);

        Assert.assertNotNull(result);
        Assert.assertNull(result.getIconUrl());
        Assert.assertEquals("Sample Text", result.getText());
    }

    @Test
    public void buildReportCardPersuasion_ReturnsReportCardPersuasion_WithNullText() {
        PersuasionData persuasionData = new PersuasionData();
        persuasionData.setIconurl("http://example.com/icon.png");
        persuasionData.setText(null);

        ReportCardPersuasion result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "buildReportCardPersuasion", persuasionData);

        Assert.assertNotNull(result);
        Assert.assertEquals("http://example.com/icon.png", result.getIconUrl());
        Assert.assertNull(result.getText());
    }

    @Test
    public void buildReportCardPersuasion_ReturnsReportCardPersuasion_WithEmptyData() {
        PersuasionData persuasionData = new PersuasionData();
        persuasionData.setIconurl("");
        persuasionData.setText("");

        ReportCardPersuasion result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "buildReportCardPersuasion", persuasionData);

        Assert.assertNotNull(result);
        Assert.assertEquals("", result.getIconUrl());
        Assert.assertEquals("", result.getText());
    }

//    @Test
//    public void modifyPlacesResponse_ShouldReturnNull_WhenPlacesResponseIsNull() {
//        PlacesResponse result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "modifyPlacesResponse", null);
//
//        Assert.assertNull(result);
//    }

    @Test
    public void modifyPlacesResponse_ShouldReturnSameObject_WhenCategoriesAreEmpty() {
        PlacesResponse placesResponse = new PlacesResponse();
        placesResponse.setCategories(new ArrayList<>());
        PlacesResponse result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "modifyPlacesResponse", placesResponse);
        Assert.assertSame(placesResponse, result);
    }

    @Test
    public void modifyPlacesResponse_ShouldSetTagLine_WhenCategoryDatumHasCategory() {
        PlacesResponse placesResponse = new PlacesResponse();
        CategoryDatum categoryDatum = new CategoryDatum();
        categoryDatum.setCategory("TestCategory");
        Category category = new Category();
        category.setCategoryData(Collections.singletonList(categoryDatum));
        placesResponse.setCategories(Collections.singletonList(category));

        PlacesResponse result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "modifyPlacesResponse", placesResponse);

        Assert.assertEquals("TestCategory", result.getCategories().get(0).getCategoryData().get(0).getTagLine());
    }

    @Test
    public void modifyPlacesResponse_ShouldNotSetTagLine_WhenCategoryDatumHasNoCategory() {
        PlacesResponse placesResponse = new PlacesResponse();
        CategoryDatum categoryDatum = new CategoryDatum();
        Category category = new Category();
        category.setCategoryData(Collections.singletonList(categoryDatum));
        placesResponse.setCategories(Collections.singletonList(category));

        PlacesResponse result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "modifyPlacesResponse", placesResponse);

        Assert.assertNull(result.getCategories().get(0).getCategoryData().get(0).getTagLine());
    }

    @Test
    public void modifyPlacesResponse_ShouldHandleMultipleCategoriesAndData() {
        PlacesResponse placesResponse = new PlacesResponse();
        CategoryDatum categoryDatum1 = new CategoryDatum();
        categoryDatum1.setCategory("Category1");
        CategoryDatum categoryDatum2 = new CategoryDatum();
        categoryDatum2.setCategory("Category2");
        Category category1 = new Category();
        category1.setCategoryData(Collections.singletonList(categoryDatum1));
        Category category2 = new Category();
        category2.setCategoryData(Collections.singletonList(categoryDatum2));
        placesResponse.setCategories(Arrays.asList(category1, category2));

        PlacesResponse result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "modifyPlacesResponse", placesResponse);

        Assert.assertEquals("Category1", result.getCategories().get(0).getCategoryData().get(0).getTagLine());
        Assert.assertEquals("Category2", result.getCategories().get(1).getCategoryData().get(0).getTagLine());
    }

    @Test
    public void getMediaV2HotelMediaListCount_ShouldReturnCorrectCount_WhenTagsContainImages() {
        List<Tag> tags = new ArrayList<>();
        Tag tag = new Tag();
        Subtag subtag = new Subtag();
        ImageData imageData = new ImageData();
        imageData.setMediaType("IMAGE");
        subtag.setData(Collections.singletonList(imageData));
        tag.setSubtags(Collections.singletonList(subtag));
        tags.add(tag);

        int result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "getMediaV2HotelMediaListCount", tags);

        Assert.assertEquals(1, result);
    }

    @Test
    public void getMediaV2HotelMediaListCount_ShouldReturnZero_WhenTagsContainOnlyVideos() {
        List<Tag> tags = new ArrayList<>();
        Tag tag = new Tag();
        Subtag subtag = new Subtag();
        ImageData imageData = new ImageData();
        imageData.setMediaType("VIDEO");
        subtag.setData(Collections.singletonList(imageData));
        tag.setSubtags(Collections.singletonList(subtag));
        tags.add(tag);

        int result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "getMediaV2HotelMediaListCount", tags);

        Assert.assertEquals(0, result);
    }

    @Test
    public void getMediaV2HotelMediaListCount_ShouldReturnZero_WhenTagsIsEmpty() {
        List<Tag> tags = new ArrayList<>();

        int result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "getMediaV2HotelMediaListCount", tags);

        Assert.assertEquals(0, result);
    }

    @Test
    public void getMediaV2HotelMediaListCount_ShouldReturnZero_WhenSubtagsIsEmpty() {
        List<Tag> tags = new ArrayList<>();
        Tag tag = new Tag();
        tag.setSubtags(new ArrayList<>());
        tags.add(tag);

        int result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "getMediaV2HotelMediaListCount", tags);

        Assert.assertEquals(0, result);
    }

    @Test
    public void getMediaV2HotelMediaListCount_ShouldReturnZero_WhenDataIsNull() {
        List<Tag> tags = new ArrayList<>();
        Tag tag = new Tag();
        Subtag subtag = new Subtag();
        subtag.setData(null);
        tag.setSubtags(Collections.singletonList(subtag));
        tags.add(tag);

        int result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "getMediaV2HotelMediaListCount", tags);

        Assert.assertEquals(0, result);
    }

    @Test
    public void prepareSpokenLanguagesString_ShouldReturnNull_WhenListIsEmpty() {
        List<String> spokenLanguages = new ArrayList<>();

        String result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "prepareSpokenLanguagesString", spokenLanguages);

        Assert.assertNull(result);
    }

    @Test
    public void prepareSpokenLanguagesString_ShouldReturnSingleLanguage_WhenListHasOneElement() {
        List<String> spokenLanguages = Collections.singletonList("English");

        String result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "prepareSpokenLanguagesString", spokenLanguages);

        Assert.assertEquals("English", result);
    }

    @Test
    public void prepareSpokenLanguagesString_ShouldReturnLanguagesSeparatedByComma_WhenListHasMultipleElements() {
        List<String> spokenLanguages = Arrays.asList("English", "French", "Spanish");

        String result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "prepareSpokenLanguagesString", spokenLanguages);

        Assert.assertEquals("English, French and Spanish", result);
    }

    @Test
    public void prepareSpokenLanguagesString_ShouldReturnLanguagesSeparatedByComma_WhenListHasTwoElements() {
        List<String> spokenLanguages = Arrays.asList("English", "French");

        String result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "prepareSpokenLanguagesString", spokenLanguages);

        Assert.assertEquals("English and French", result);
    }

    @Test
    public void prepareSpokenLanguagesString_ShouldTrimLanguages_WhenLanguagesHaveLeadingOrTrailingSpaces() {
        List<String> spokenLanguages = Arrays.asList(" English ", " French ", " Spanish ");

        String result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "prepareSpokenLanguagesString", spokenLanguages);

        Assert.assertEquals("English, French and Spanish", result);
    }

    @Test
    public void handleOneFoodAndDiningSection_ShouldClearSummaryList_WhenCommonRuleHasNoRules() {
        List<String> summaryList = new ArrayList<>(Arrays.asList("Rule1", "Rule2"));
        List<CommonRules> allRulesList = new ArrayList<>(Arrays.asList(new CommonRules(), new CommonRules()));
        CommonRules commonRule = new CommonRules();
        commonRule.setRules(new ArrayList<>());

        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "handleOneFoodAndDiningSection", commonRule, summaryList, allRulesList);

    }

    @Test
    public void handleOneFoodAndDiningSection_ShouldAddRulesToSummaryList_WhenCommonRuleHasRules() {
        List<String> summaryList = new ArrayList<>();
        List<CommonRules> allRulesList = new ArrayList<>(Arrays.asList(new CommonRules(), new CommonRules()));
        CommonRules commonRule = new CommonRules();
        commonRule.setRules(Arrays.asList(new Rule("Rule1"), new Rule("Rule2")));

        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "handleOneFoodAndDiningSection", commonRule, summaryList, allRulesList);

        Assert.assertTrue(summaryList.isEmpty());
        Assert.assertTrue(allRulesList.isEmpty());
    }

    @Test
    public void handleOneFoodAndDiningSection_ShouldAddLimitedRulesToSummaryList_WhenCommonRuleHasMoreThanConfiguredRules() {
        List<String> summaryList = new ArrayList<>();
        List<CommonRules> allRulesList = new ArrayList<>(Arrays.asList(new CommonRules(), new CommonRules()));
        CommonRules commonRule = new CommonRules();
        commonRule.setRules(Arrays.asList(new Rule("Rule1"), new Rule("Rule2"), new Rule("Rule3")));
        ReflectionTestUtils.setField(staticDetailResponseTransformerPWA, "foodDiningMinCountConfig", 2);

        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "handleOneFoodAndDiningSection", commonRule, summaryList, allRulesList);

        Assert.assertTrue(summaryList.isEmpty());
        Assert.assertTrue(allRulesList.isEmpty());
    }

    @Test
    public void handleOneFoodAndDiningSection_ShouldClearAllRulesList_WhenCommonRuleHasRules() {
        List<String> summaryList = new ArrayList<>();
        List<CommonRules> allRulesList = new ArrayList<>(Arrays.asList(new CommonRules(), new CommonRules()));
        CommonRules commonRule = new CommonRules();
        commonRule.setRules(Arrays.asList(new Rule("Rule1"), new Rule("Rule2")));

        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerPWA, "handleOneFoodAndDiningSection", commonRule, summaryList, allRulesList);

        Assert.assertTrue(allRulesList.isEmpty());
    }

}
