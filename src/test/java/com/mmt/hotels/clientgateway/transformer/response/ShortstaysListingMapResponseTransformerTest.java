package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.response.moblanding.LatLong;
import com.mmt.hotels.clientgateway.response.shortstays.CityGeoConfig;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.response.shortstays.CityMapDetails;
import com.mmt.hotels.model.response.shortstays.ShortstaysZoneResult;
import com.mmt.hotels.model.response.staticdata.BbCentre;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class ShortstaysListingMapResponseTransformerTest {

    @Mock
    PolyglotService polyglotService;

    @InjectMocks
    ShortstaysListingMapResponseTransformer shortstaysListingMapResponseTransformer;

    @Before
    public void setup() throws IOException {
        ReflectionTestUtils.setField(shortstaysListingMapResponseTransformer, "directionTextColor", "color");
        Map<String, LatLong> zoneBoundMap = new HashMap<>();
        zoneBoundMap.put("NE", new LatLong());
        ReflectionTestUtils.setField(shortstaysListingMapResponseTransformer, "zoneBoundMap", zoneBoundMap);
    }


    @Test
    public void buildAssociatedCitiesGeoConfigTest() {
        ShortstaysZoneResult shortstaysZoneResult = new ShortstaysZoneResult();
        CityMapDetails centralCityMapDetails = new CityMapDetails();
        centralCityMapDetails.setId("CTDEL");
        centralCityMapDetails.setCentre(new BbCentre());
        centralCityMapDetails.setName("Delhi");

        CityMapDetails associatedCityDetails = new CityMapDetails();
        associatedCityDetails.setId("CTBOM");
        associatedCityDetails.setDirection("NE");
        associatedCityDetails.setName("Mumbai");

        List<CityMapDetails> associatedCityMapList = Arrays.asList(associatedCityDetails);

        shortstaysZoneResult.setAssociatedCityDetails(associatedCityMapList);
        shortstaysZoneResult.setCentralCityDetails(centralCityMapDetails);

        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("test string");

        List<CityGeoConfig> result = shortstaysListingMapResponseTransformer.buildAssociatedCitiesGeoConfig(shortstaysZoneResult);
        Assert.assertEquals(result.size(), 1);


    }

    @Test
    public void buildCentralCityDetailsTest(){
        ShortstaysZoneResult shortstaysZoneResult = new ShortstaysZoneResult();
        CityMapDetails centralCityMapDetails = new CityMapDetails();
        centralCityMapDetails.setId("CTDEL");
        centralCityMapDetails.setCentre(new BbCentre());
        centralCityMapDetails.getCentre().setCoordinates(Arrays.asList(0.00d, 0.00d));
        centralCityMapDetails.setName("Delhi");
        shortstaysZoneResult.setCentralCityDetails(centralCityMapDetails);

        CityGeoConfig result = shortstaysListingMapResponseTransformer.buildCentralCityGeoDetails(shortstaysZoneResult);
        Assert.assertNotNull(result);


    }
}
