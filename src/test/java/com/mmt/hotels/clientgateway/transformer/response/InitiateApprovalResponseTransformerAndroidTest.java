package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.response.cbresponse.ErrorResponse;
import com.mmt.hotels.clientgateway.response.moblanding.GenericErrorEntity;
import com.mmt.hotels.clientgateway.transformer.response.android.InitiateApprovalResponseTransformerAndroid;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class InitiateApprovalResponseTransformerAndroidTest {

    @InjectMocks
    InitiateApprovalResponseTransformerAndroid initiateApprovalResponseTransformerAndroid;

    @Test
    public void test() {

        CGServerResponse cgServerResponse = new CGServerResponse();
        cgServerResponse.setResponseErrors(new ErrorResponse());
        cgServerResponse.getResponseErrors().setErrorList(new ArrayList<>());
        cgServerResponse.getResponseErrors().getErrorList().add(new GenericErrorEntity());
        cgServerResponse.getResponseErrors().getErrorList().get(0).setErrorCode("12");
        cgServerResponse.getResponseErrors().getErrorList().get(0).setErrorMessage("Invalid");
        cgServerResponse.getResponseErrors().getErrorList().get(0).setErrorAdditionalInfo(new HashMap<>());
        cgServerResponse.getResponseErrors().getErrorList().get(0).getErrorAdditionalInfo().put("error", "downstream issue");
        Assert.assertNotNull(initiateApprovalResponseTransformerAndroid.processResponse(cgServerResponse));

        CGServerResponse cgServerResponse1 = new CGServerResponse();
        cgServerResponse1.setResponseErrors(null);
        cgServerResponse1.setAdditionalProperty("error", "abc");
        cgServerResponse1.setAdditionalProperty("status", "abc");
        cgServerResponse1.setAdditionalProperty("message", "abc");
        Assert.assertNotNull(initiateApprovalResponseTransformerAndroid.processResponse(cgServerResponse1));
    }

}