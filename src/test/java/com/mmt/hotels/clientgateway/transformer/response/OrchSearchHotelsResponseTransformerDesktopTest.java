package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.PropertyTextConfigConsul;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.Persuasions;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.PropertyTextConfig;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.request.TrafficSource;
import com.mmt.hotels.clientgateway.response.searchHotels.*;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.response.desktop.SearchHotelsResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.HomeStayDetails;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.request.matchmaker.InputHotel;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.model.response.emi.Emi;
import com.mmt.hotels.model.response.flyfish.TravellerRatingSummaryDTO;
import com.mmt.hotels.model.response.listpersonalization.*;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.searchwrapper.*;
import com.mmt.hotels.model.response.staticdata.Address;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.hotels.pojo.CalendarAvailability.CalendarCriteria;
import com.mmt.hotels.pojo.response.CampaignPojo;
import com.mmt.hotels.pojo.response.DevotenessPersuasion;
import com.mmt.hotels.pojo.response.IndianessPersuasion;
import com.mmt.hotels.pojo.response.detail.FeaturedAmenity;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.EXP_MYPARTNER_LISTING_HN;
import static com.mmt.hotels.clientgateway.constants.Constants.TOOL_TIP_INDIANNESS;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class OrchSearchHotelsResponseTransformerDesktopTest {

	@InjectMocks
	SearchHotelsResponseTransformerDesktop searchHotelsResponseTransformerDesktop;

	@Spy
	CommonResponseTransformer commonResponseTransformer;

	private ObjectMapper objectMapper = new ObjectMapper();

	@Mock
	ObjectMapperUtil objectMapperUtil;

	@Mock
	PolyglotService polyglotService;

	@Mock
	PropertyTextConfig propertyTextConfig;

	@Mock
	CommonConfig commonConfig;

	@Mock
	PropertyManager propManager;

	@Mock
	PropertyTextConfigConsul propertyTextConfigConsul;

	@Mock
	CommonConfigConsul commonConfigConsul;


	@Mock
	PolyglotHelper polyglotHelper;

	@Mock
	Utility utility;

	@Mock
	DateUtil dateUtil;

	Hotel hotel;

	String hotelPersuasions;

	MyBizAssuredToolTip myBizAssuredToolTip;

	ValueStaysTooltip valueStaysToolTipDom;

	@Mock
	PersuasionUtil persuasionUtil;

	private Map<String, Map<String, PersuasionData>> specialFarePersuasionConfigMap;

	private static final Logger logger = LoggerFactory.getLogger(MetricErrorLogger.class);

	@Before
	public void init() throws IOException, JSONException {
		ObjectMapper mapper = new ObjectMapper();
		mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		MySafetyTooltip mySafetyDataTooltips = new MySafetyTooltip();
		mySafetyDataTooltips.setTitle("SAFETY_TITLE");
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("testTranslation");
		ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop,"mySafetyDataTooltips",mySafetyDataTooltips);
		List<String> amenetiesWithUrl= new ArrayList<>();
		amenetiesWithUrl.add("WI_FI");
		ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop,"amenetiesWithUrl",amenetiesWithUrl);
		InputStream availPriceRequest = getClass().getClassLoader().getResourceAsStream("mockrequestresponsetest/hotel.json");
		hotel = mapper.readValue(availPriceRequest, Hotel.class);
		hotelPersuasions = mapper.writeValueAsString(hotel.getHotelPersuasions());
		InputStream myBizAssuredToolTipStream = getClass().getClassLoader().getResourceAsStream("mockrequestresponsetest/myBizAssuredTooltip.json");
		myBizAssuredToolTip = mapper.readValue(myBizAssuredToolTipStream, MyBizAssuredToolTip.class);
		ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop,"myBizAssuredTooltipDom", myBizAssuredToolTip);
		InputStream valueStaysToolTipDomStream = getClass().getClassLoader().getResourceAsStream("mockrequestresponsetest/valueStaysToolTipDom.json");
		valueStaysToolTipDom = mapper.readValue(valueStaysToolTipDomStream, ValueStaysTooltip.class);
		ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop,"valueStaysTooltipDom", valueStaysToolTipDom);
		MDC.put(MDCHelper.MDCKeys.LANGUAGE.getStringValue(),"eng");
		MyBizStaticCard myBizStaticCard = new MyBizStaticCard();
		myBizStaticCard.setText("CORPBUDGET_STATIC_TEXT");
		myBizStaticCard.setSubtext("CORPBUDGET_STATIC_SUBTEXT");
		myBizStaticCard.setCtaText("CORPBUDGET_STATIC_CTATEXT");
		ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop,"myBizStaticCard",myBizStaticCard);
		ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop,"myBizStaticCard",myBizStaticCard);
		Gson gson = new Gson();
		String specialFarePersuasionConfigString = "{\"Desktop\":{\"PC_RIGHT_1_1\":{\"style\":{\"styleClasses\":[\"specialFareTag\",\"pushRight\"]}},\"PC_RIGHT_3\":{\"hover\":{\"style\":{\"styleClasses\":[\"specialFareInfo-tooltip\"]}},\"style\":{\"styleClasses\":[\"specialFareInfo\"]}}},\"Apps\":{\"PLACEHOLDER_BOTTOM_BOX_M\":{\"style\":{\"bgColor\":\"#FFF6E8\",\"textColor\":\"#CF8100\",\"fontType\":\"B\",\"fontSize\":\"MID\"},\"iconurl\":\"https://img.url/\"}}}";
		specialFarePersuasionConfigMap = gson.fromJson(specialFarePersuasionConfigString, new TypeToken<Map<String, Map<String, PersuasionData>>>(){
		}.getType());
		ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop, "specialFarePersuasionConfigMap", specialFarePersuasionConfigMap);
	}

	@Test
	public void addLocationPersuasionToHotelPersuasionsTest(){
		List<String> locations = new ArrayList<>();
		locations.add("This is test location persuasion");
		LinkedHashSet<String> ameneties = new LinkedHashSet<>();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setDayUsePersuasionsText("Test_Persuasions");
		ameneties.add("WI-FI");
		ameneties.add("bathtub");
		hotel.setHotelPersuasions(new HashMap<>());
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		searchHotelsRequest.getRequestDetails().setFunnelSource("DAYUSE");
        	TransportPoi transportPoi = new TransportPoi();
		transportPoi.setPoiName("Delhi Aerocity Metro Station");
		transportPoi.setDrivingDistance(810.0);
		transportPoi.setPoiId("POI48020");
		transportPoi.setDrivingDistanceText("810 m from Delhi Aerocity Metro Station");
		searchHotelsResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), transportPoi,null,null, false);
		Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		Assert.assertEquals(((Map<String, Object>)hotel.getHotelPersuasions()).size(),3);

		locations.add("one more test persuasion");
		hotel.setHotelPersuasions(new HashMap<>());
		searchHotelsResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), transportPoi,null,null, false);
		Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		Assert.assertEquals(((Map<String, Object>)hotel.getHotelPersuasions()).size(),3);

		//Test for Secondary Location Persuasion
		String secondaryPersuasion = "Secondary Persuasion";
		locations.add(secondaryPersuasion);
		hotel.setHotelPersuasions(new HashMap<>());
		searchHotelsResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null,null,null, false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID));
		org.junit.Assert.assertTrue(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID) instanceof PersuasionObject);
		org.junit.Assert.assertNotNull(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID)).getData());
		org.junit.Assert.assertTrue(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID)).getData().size() > 0);
		org.junit.Assert.assertTrue(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID)).getData().get(0).getText().contains(secondaryPersuasion));

		//Test for GCC Listing page location persuasion
		MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(),"AE");
		searchHotelsRequest.getRequestDetails().setPageContext("LISTING");
		hotel.setHotelPersuasions(new HashMap<>());
		searchHotelsResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), transportPoi,null,null, false);
		Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID));
		Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_2_PLACEHOLDER_ID));
		Mockito.when(utility.isExperimentOn(Mockito.any(), Mockito.argThat(argument -> !EXP_MYPARTNER_LISTING_HN.equals(argument)))).thenReturn(true);
		searchHotelsResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), transportPoi,null,null, false);
		// MyPartner case where HN Orientation is true
		MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(),"IN");
		Mockito.when(utility.isExperimentOn(Mockito.any(),Mockito.any())).thenReturn(true);
		searchHotelsResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), transportPoi,null,null, false);
		Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_HN_PLACEHOLDER_ID));
		Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.AMENITIES_PLACEHOLDER_ID_HN));
	}

	@Test
	public void fetchToolTipPersuasionDataTest() {
		Map<String, Object> toolTipConfigMap = new HashMap<>();
		toolTipConfigMap.put("VILLA", new HashMap<String, Object>());
		((Map<String, Object>) toolTipConfigMap.get("VILLA")).put("imageUrl", "www.mmtcdn.com/villa.png");
		((Map<String, Object>) toolTipConfigMap.get("VILLA"))
				.put("data", Arrays.asList("VALUE_FOR_MONEY", "IDEAL_FOR_GROUP_STAY", "SUPER_SPACIOUS", "COMPLETE_PRIVACY"));
		ReflectionTestUtils
				.setField(searchHotelsResponseTransformerDesktop, "desktopToolTipPersuasionsMap", toolTipConfigMap);
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("testTranslation");
		Map<String, Object> toolTipDataMap = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,
																																					"fetchToolTipPersuasionData", "VILLA");
		Assert.assertNotNull(toolTipDataMap);
		Assert.assertNotNull(toolTipDataMap.get("imageUrl"));
		Assert.assertNotNull(toolTipDataMap.get("toolTipData"));
		Assert.assertEquals(((List<String>) toolTipDataMap.get("toolTipData")).size(), 4);
	}


	@Test
	public void overridePersonalizedSectionHeadingForDirectHotelSearchTest() {
		InputHotel inputHotel = new InputHotel();
		inputHotel.setHotelId("123456789");
		MatchMakerRequest matchMakerRequest = new MatchMakerRequest();
		matchMakerRequest.setHotels(Collections.singletonList(inputHotel));
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setFunnelSource("HOTELS");
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setMatchMakerDetails(matchMakerRequest);
		searchHotelsRequest.setRequestDetails(requestDetails);
		PersonalizedSection personalizedSection = new PersonalizedSection();
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("MyBiz Recommended Properties Near This Property");
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop, "overridePersonalizedSectionHeadingForDirectHotelSearch", searchHotelsRequest, personalizedSection);
		Assert.assertEquals("MyBiz Recommended Properties Near This Property", personalizedSection.getHeading());
	}

	@Test
	public void doNotOverridePersonalizedSectionHeadingForDirectHotelSearchTest() {
		MatchMakerRequest matchMakerRequest = new MatchMakerRequest();
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setFunnelSource("CORPBUDGET");
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setMatchMakerDetails(matchMakerRequest);
		searchHotelsRequest.setRequestDetails(requestDetails);
		PersonalizedSection personalizedSection = new PersonalizedSection();
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop, "overridePersonalizedSectionHeadingForDirectHotelSearch", searchHotelsRequest, personalizedSection);
		Assert.assertNull(personalizedSection.getHeading());
	}

	@Test
	public void testBuildQuickBookCard() {
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("");
		QuickBookInfo quickBookInfo = new QuickBookInfo();
		quickBookInfo.setTitleWithPrice("");
		quickBookInfo.setRoomPersuasion("");
		Assert.assertNotNull(searchHotelsResponseTransformerDesktop.buildQuickBookCard(quickBookInfo));
	}

	@Test
	public void testGetMyBizDirectHotelDistanceText() {
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("");
		Assert.assertNotNull(searchHotelsResponseTransformerDesktop.getMyBizDirectHotelDistanceText("test"));
	}

	@Test
	public void testBuildBottomSheet() throws IOException {
		ListingPagePersonalizationResponsBO webApiResponse = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/listingPersonalizationHESResponse.json")),
				ListingPagePersonalizationResponsBO.class);
		searchHotelsResponseTransformerDesktop.buildBottomSheet(webApiResponse.getPersonalizedResponse().get(0));
	}
	@Test
	public void testBuildStaticCard(){
		MyBizStaticCard staticCard = new MyBizStaticCard();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setCorpBudgetHotel(false);
		List<SearchWrapperHotelEntity> hotelEntityList = new ArrayList<>();
		hotelEntityList.add(hotelEntity);
		Mockito.when(polyglotService.getTranslatedData("CORPBUDGET_STATIC_TEXT")).thenReturn("This Is Not A Budget Hotel");
		staticCard = searchHotelsResponseTransformerDesktop.buildStaticCard("DIRECT_HOTEL",hotelEntityList);
		Assert.assertNotNull(staticCard);
		Assert.assertEquals("This Is Not A Budget Hotel", staticCard.getText());
	}

	@Test
	public void testCreateFreeCancellationTooltip() throws IOException {
		CancellationTimeline cancellationTimelineHes = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/cancellationTimeline.json")),
				CancellationTimeline.class);
		CancellationTimeline cancellationTimeline = searchHotelsResponseTransformerDesktop.createFreeCancellationTooltip(cancellationTimelineHes);
		Assert.assertNotNull(cancellationTimeline);
	}

	@Test
	public void createIndianessPersuasionTest() {
		IndianessToolTip indianessToolTip = searchHotelsResponseTransformerDesktop.createIndianessPersuasion(null);
		Assert.assertNotNull(indianessToolTip);
		indianessToolTip = searchHotelsResponseTransformerDesktop.createIndianessPersuasion(new IndianessPersuasion());
		Assert.assertNotNull(indianessToolTip);
		IndianessPersuasion indianessPersuasion = new IndianessPersuasion();
		indianessPersuasion.setShortSummary(new ArrayList<>());
		indianessPersuasion.getShortSummary().add(new CampaignPojo());
		indianessPersuasion.getShortSummary().get(0).setHeading("abc");
		indianessPersuasion.getShortSummary().get(0).setIconUrl("123.jpg");
		indianessToolTip = searchHotelsResponseTransformerDesktop.createIndianessPersuasion(indianessPersuasion);
		Assert.assertNotNull(indianessToolTip.getData());
		Assert.assertEquals(1,indianessToolTip.getData().size());

	}

	@Test
	public void createDevotenessPersuasionTest() {
		DevotenessToolTip devotenessToolTip = searchHotelsResponseTransformerDesktop.createDevotenessPersuasion(null);
		Assert.assertNotNull(devotenessToolTip);
		devotenessToolTip = searchHotelsResponseTransformerDesktop.createDevotenessPersuasion(new DevotenessPersuasion());
		Assert.assertNotNull(devotenessToolTip);
		DevotenessPersuasion devotenessPersuasion = new DevotenessPersuasion();
		devotenessPersuasion.setShortSummary(new ArrayList<>());
		devotenessPersuasion.getShortSummary().add(new CampaignPojo());
		devotenessPersuasion.getShortSummary().get(0).setHeading("abc");
		devotenessPersuasion.getShortSummary().get(0).setIconUrl("123.jpg");
		devotenessToolTip = searchHotelsResponseTransformerDesktop.createDevotenessPersuasion(devotenessPersuasion);
		Assert.assertNotNull(devotenessToolTip.getData());
		Assert.assertEquals(1,devotenessToolTip.getData().size());

	}

	@Test
	public void testCreateValueStayToolTipDom() {
		Mockito.doNothing().when(polyglotHelper).translateValueStaysTooltip(Mockito.any(ValueStaysTooltip.class));
		ValueStaysTooltip result = searchHotelsResponseTransformerDesktop.createValueStayToolTip("IN");
		Assert.assertNotNull(result);
	}

	@Test
	public void testCreateValueStayToolTipIntl() throws IOException {
		ValueStaysTooltip valueStaysToolTipIntl = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/mmtValueStaysTooltipIntl.json")),
				ValueStaysTooltip.class);
		ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop,"valueStaysTooltipIntl", valueStaysToolTipIntl);
		Mockito.doNothing().when(polyglotHelper).translateValueStaysTooltip(Mockito.any(ValueStaysTooltip.class));
		ValueStaysTooltip result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop, "createValueStayToolTip", "AE");
		Assert.assertNotNull(result);
	}

	@Test
	public void testCreateMyBizAssuredToolTip() {
		Mockito.doNothing().when(polyglotHelper).translateMyBizAssuredTooltip(Mockito.any(MyBizAssuredToolTip.class));
		MyBizAssuredToolTip result = searchHotelsResponseTransformerDesktop.createMyBizAssuredToolTip();
		Assert.assertNotNull(result);
	}

	@Test
	public void buildCalendarCriteriaTest(){
		CalendarCriteria calendarCriteria = new CalendarCriteria();
		calendarCriteria.setAdvanceDays(5);
		calendarCriteria.setAvailable(true);
		calendarCriteria.setMaxDate("2022-10-21");
		calendarCriteria.setMlos(3);
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"buildCalendarCriteria",calendarCriteria);
	}

	@Test
	public void buildSoldOutInfo(){
		SoldOutInfo soldOutInfo = new SoldOutInfo();
		soldOutInfo.setSoldOutReason("Out of Policy");
		soldOutInfo.setSoldOutSubText("OOP");
		soldOutInfo.setSoldOutText("Sold Out hai");
		soldOutInfo.setSoldOutType("Sold out");
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"buildSoldOutInfo",soldOutInfo);
	}

	@Test
	public void getMybizSimilarHotelsFeaturesTest(){
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"getMybizSimilarHotelsFeatures");
	}

	@Test
	public void buildEmiDetailsTest(){
		Emi emi = new Emi();
		emi.setEmiAmount(32251);
		emi.setEmiType("Test1");
		emi.setBankName("HDFC");
		emi.setTenure(5);
		emi.setTotalCost(200.0);
		emi.setTotalInterest(500.0);
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"buildEmiDetails",emi);
	}

	@Test
	public void buildHotelBottomCardTest(){
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"buildHotelBottomCard",new QuickBookInfo());
	}

	@Test
	public void getFacilityTest(){
		FeaturedAmenity featuredAmenity = new FeaturedAmenity();
		featuredAmenity.setIconUrl("Icon");
		featuredAmenity.setName("Test");
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"getFacility",featuredAmenity);
	}

	@Test
	public void buildHotelTopCardTest(){
		MyBizSimilarToDirectObj myBizSimilarToDirectObj = new MyBizSimilarToDirectObj();
		myBizSimilarToDirectObj.setDistance("55");
		List<FeaturedAmenity> featuredAmenityList = new ArrayList<>();
		featuredAmenityList.add(new FeaturedAmenity());
		myBizSimilarToDirectObj.setAmenities(featuredAmenityList);
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"buildHotelTopCard",myBizSimilarToDirectObj);
	}

	@Test
	public void addToolTipTest() throws ClientGatewayException {

		try {
			Hotel hotel = new Hotel();
			SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
			CancellationTimeline cancellationTimeline = new CancellationTimeline();
			DisplayFare displayFare = new DisplayFare();

			org.json.JSONObject jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"BNPL_AVAIL\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"FCZPN\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"FREE_CANCELLATION\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

//			displayFare.setCorpMetaData(new CorpMetaInfo());
//			displayFare.getCorpMetaData().setValidationPayload(new ValidationResponse());
//			displayFare.getCorpMetaData().getValidationPayload().setFailureReasons(new ArrayList<>());
//			displayFare.getCorpMetaData().getValidationPayload().getFailureReasons().add("failure");
			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"OOP\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			Map<String, MySafetyTooltip> mySafetyToolTipTranslated = new HashMap<>();
			mySafetyToolTipTranslated.put(MDCHelper.MDCKeys.LANGUAGE.getStringValue(),new MySafetyTooltip());
			ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop,"mySafetyToolTipTranslated", mySafetyToolTipTranslated);

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"SAFETY\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");


//			Map<String, Object> toolTipConfigMap = new HashMap<>();
//			toolTipConfigMap.put("VILLA", new HashMap<String, Object>());
//			((Map<String, Object>) toolTipConfigMap.get("VILLA")).put("imageUrl", "www.mmtcdn.com/villa.png");
//			((Map<String, Object>) toolTipConfigMap.get("VILLA"))
//					.put("data", Arrays.asList("VALUE_FOR_MONEY", "IDEAL_FOR_GROUP_STAY", "SUPER_SPACIOUS", "COMPLETE_PRIVACY"));
//
//			toolTipConfigMap.put("HOSTEL", new HashMap<String, Object>());
//			((Map<String, Object>) toolTipConfigMap.get("HOSTEL")).put("imageUrl", "www.mmtcdn.com/villa.png");
//			((Map<String, Object>) toolTipConfigMap.get("HOSTEL"))
//					.put("data", Arrays.asList("VALUE_FOR_MONEY", "IDEAL_FOR_GROUP_STAY", "SUPER_SPACIOUS", "COMPLETE_PRIVACY"));
//
//			toolTipConfigMap.put("APARTMENT", new HashMap<String, Object>());
//			((Map<String, Object>) toolTipConfigMap.get("APARTMENT")).put("imageUrl", "www.mmtcdn.com/villa.png");
//			((Map<String, Object>) toolTipConfigMap.get("APARTMENT"))
//					.put("data", Arrays.asList("VALUE_FOR_MONEY", "IDEAL_FOR_GROUP_STAY", "SUPER_SPACIOUS", "COMPLETE_PRIVACY"));
//
//			toolTipConfigMap.put("COTTAGE", new HashMap<String, Object>());
//			((Map<String, Object>) toolTipConfigMap.get("COTTAGE")).put("imageUrl", "www.mmtcdn.com/villa.png");
//			((Map<String, Object>) toolTipConfigMap.get("COTTAGE"))
//					.put("data", Arrays.asList("VALUE_FOR_MONEY", "IDEAL_FOR_GROUP_STAY", "SUPER_SPACIOUS", "COMPLETE_PRIVACY"));
//
//			toolTipConfigMap.put("HOMESTAY", new HashMap<String, Object>());
//			((Map<String, Object>) toolTipConfigMap.get("HOMESTAY")).put("imageUrl", "www.mmtcdn.com/villa.png");
//			((Map<String, Object>) toolTipConfigMap.get("HOMESTAY"))
//					.put("data", Arrays.asList("VALUE_FOR_MONEY", "IDEAL_FOR_GROUP_STAY", "SUPER_SPACIOUS", "COMPLETE_PRIVACY"));
//
//			ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop,"desktopToolTipPersuasionsMap", toolTipConfigMap);

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"VILLA\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"HOSTEL\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"APARTMENT\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"COTTAGE\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"HOMESTAY\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			hotelEntity.setBudgetHotel(true);
			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"TOOL_TIP_MMT_VALUE_STAY\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"TOOL_TIP_MYBIZ_ASSURED\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"TOOL_TIP_LUXE\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

			jo = new JSONObject("{\"data\":{\"},\"tooltipType\":\"LOVED_BY_DEVOTEES\"}");
			ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"addToolTip",jo, hotelEntity, cancellationTimeline, displayFare,"key");

		} catch (Exception e){
			logger.error("error occured in getting file", e.getMessage());
		}
	}
	@Test
	public void addPersuasionsForHiddenGemCardTest() {
		SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
		searchWrapperHotelEntity.setLocationPersuasion(Collections.singletonList("Test Location"));
		searchWrapperHotelEntity.setHiddenGem(true);
		searchWrapperHotelEntity.setHiddenGemPersuasionText("Hidden Gem Persuasion Text");
		HomeStayDetails homeStayDetails = new HomeStayDetails();
		homeStayDetails.setStayType(Constants.STAY_TYPE_HOMESTAY);
		homeStayDetails.setStayTypeInfo("Home Stay Info Test");

		PersuasionObject homeStaysTitlePersuasion = new PersuasionObject();
		List<PersuasionData> homeStaysTitlePersuasionList = new ArrayList<>();
		homeStaysTitlePersuasionList.add(new PersuasionData());
		homeStaysTitlePersuasion.setData(homeStaysTitlePersuasionList);

		PersuasionObject homestaysSubTitlePersuasion = new PersuasionObject();
		homestaysSubTitlePersuasion.setData(Arrays.asList(new PersuasionData()));

		when(persuasionUtil.buildHiddenGemPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
		when(persuasionUtil.buildHiddenGemIconPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
		when(persuasionUtil.buildHomeStaysTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homeStaysTitlePersuasion);
		when(persuasionUtil.buildHomeStaysSubTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homestaysSubTitlePersuasion);
		searchHotelsResponseTransformerDesktop.addPersuasionsForHiddenGemCard(searchWrapperHotelEntity);
		junit.framework.Assert.assertNotNull(searchWrapperHotelEntity.getHotelPersuasions());
		junit.framework.Assert.assertTrue(searchWrapperHotelEntity.getHotelPersuasions() instanceof Map<?, ?>);
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdDesktop()));
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdDesktop()));
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdDesktop()));
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdDesktop()));
	}

	@Test
	public void addSpecialFarePersuasionTest() {
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		searchHotelsResponseTransformerDesktop.addSpecialFarePersuasion(hotelEntity);
		Assert.assertNotNull(hotelEntity.getHotelPersuasions());
		Assert.assertNotNull(((Map<?, ?>) hotelEntity.getHotelPersuasions()).get(Persuasions.SPECIAL_FARE_PERSUASION.getPlaceholderIdDesktop()));
	}

	@Test
	public void addBookingConfirmationPersuasionTest() {
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		searchHotelsResponseTransformerDesktop.addBookingConfirmationPersuasion(hotelEntity);
		Assert.assertNotNull(hotelEntity.getHotelPersuasions());
		Assert.assertNotNull(((Map<?, ?>) hotelEntity.getHotelPersuasions()).get(Constants.PC_RIGHT_3_PLACEHOLDER));
	}

	@Test
	public void testInit(){
		ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop, "consulFlag", true);

		Mockito.when(propertyTextConfigConsul.getMySafetyTooltipKeys()).thenReturn(null);
		Mockito.when(propertyTextConfigConsul.getAmenetiesWithUrl()).thenReturn(null);
		Mockito.when(commonConfigConsul.getMmtValueStaysTooltipDom()).thenReturn(null);
		Mockito.when(commonConfigConsul.getMmtValueStaysTooltipIntl()).thenReturn(null);
		Mockito.when(commonConfigConsul.getLuxeToolTip()).thenReturn(null);
		Mockito.when(commonConfigConsul.getMyBizAssuredTooltipDom()).thenReturn(null);
		Mockito.when(commonConfigConsul.getMyBizStaticCard()).thenReturn(null);
		Mockito.when(commonConfigConsul.getMissingSlotDetails()).thenReturn(null);
		Mockito.when(commonConfigConsul.getThresholdForSlashedAndDefaultHourPrice()).thenReturn(1);

		ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop, "activeLanguages", "eng,hin");
		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop, "init");

		ReflectionTestUtils.setField(searchHotelsResponseTransformerDesktop, "consulFlag", false);
		Mockito.when(propManager.getProperty("propertyTextConfig", PropertyTextConfig.class)).thenReturn(propertyTextConfig);
		Mockito.when(propManager.getProperty("commonConfig", CommonConfig.class)).thenReturn(commonConfig);

		ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop, "init");

	}

	@Test
	public void buildFilterInfoTest(){
		SectionFeature sectionFeature = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerDesktop,"buildFilterInfo");
		Assert.assertNotNull(sectionFeature);
		Assert.assertEquals("testTranslation",sectionFeature.getText());
		Assert.assertEquals("checkbox",sectionFeature.getIconType());
	}

	@Test
	public void isWalletSurgePersuasionEnabled_returnsTrue_whenAllConditionsAreMet() throws JSONException {
		JSONObject persuasionData = new JSONObject();
		persuasionData.put("persuasionKey", "WALLET_SURGE_PERSUASION_TEXT");
		JSONObject timer = new JSONObject();
		timer.put("expiry", 1);
		persuasionData.put("timer", timer);

		boolean result = searchHotelsResponseTransformerDesktop.isWalletSurgePersuasionEnabled(persuasionData);

		Assert.assertTrue(result);
	}

	@Test
	public void isWalletSurgePersuasionEnabled_returnsFalse_whenPersuasionKeyIsMissing() throws JSONException {
		JSONObject persuasionData = new JSONObject();
		JSONObject timer = new JSONObject();
		timer.put("expiry", 1);
		persuasionData.put("timer", timer);

		boolean result = searchHotelsResponseTransformerDesktop.isWalletSurgePersuasionEnabled(persuasionData);

		Assert.assertFalse(result);
	}

	@Test
	public void isWalletSurgePersuasionEnabled_returnsFalse_whenTimerIsMissing() throws JSONException {
		JSONObject persuasionData = new JSONObject();
		persuasionData.put("persuasionKey", "WALLET_SURGE_PERSUASION_TEXT");

		boolean result = searchHotelsResponseTransformerDesktop.isWalletSurgePersuasionEnabled(persuasionData);

		Assert.assertFalse(result);
	}

	@Test
	public void isWalletSurgePersuasionEnabled_returnsFalse_whenExpiryIsNotInteger() throws JSONException {
		JSONObject persuasionData = new JSONObject();
		persuasionData.put("persuasionKey", "WALLET_SURGE_PERSUASION_TEXT");
		JSONObject timer = new JSONObject();
		timer.put("expiry", "notAnInteger");
		persuasionData.put("timer", timer);

		boolean result = searchHotelsResponseTransformerDesktop.isWalletSurgePersuasionEnabled(persuasionData);

		Assert.assertFalse(result);
	}

	@Test
	public void isWalletSurgePersuasionEnabled_returnsFalse_whenExpiryIsNotOne() throws JSONException {
		JSONObject persuasionData = new JSONObject();
		persuasionData.put("persuasionKey", "WALLET_SURGE_PERSUASION_TEXT");
		JSONObject timer = new JSONObject();
		timer.put("expiry", 2);
		persuasionData.put("timer", timer);

		boolean result = searchHotelsResponseTransformerDesktop.isWalletSurgePersuasionEnabled(persuasionData);

		Assert.assertFalse(result);
	}

}