package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DeviceConstant;
import com.mmt.hotels.clientgateway.helpers.FareHoldHelper;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.response.BookNowDetails;
import com.mmt.hotels.clientgateway.response.ValidateCouponResponseBody;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.response.MpFareHoldStatus;
import com.mmt.hotels.model.response.discount.ValidCouponResult;
import com.mmt.hotels.model.response.discount.ValidateCouponResponse;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.txn.UpdatedUpsellOptions;
import com.mmt.model.ExpressCheckoutDetail;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;

import java.text.NumberFormat;
import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.CLIENT;
import static com.mmt.hotels.clientgateway.constants.Constants.FUNNEL_SOURCE;
import static com.mmt.hotels.clientgateway.constants.Constants.FUNNEL_SOURCE_MYPARTNER;

@RunWith(MockitoJUnitRunner.class)
public class DiscountResponseTransformerTest {

	@InjectMocks
	DiscountResponseTransformer discountResponseTransformer;
	
	@Mock
	CommonResponseTransformer commonResponseTransformer;

	@Spy
	PricingEngineHelper pricingEngineHelper;

	@Mock
	private Utility utility;

	@Mock
	private PolyglotService polyglotService;
	@Mock
	FareHoldHelper fareHoldHelper;
	@InjectMocks
	private PersuasionUtil persuasionUtil = Mockito.spy(new PersuasionUtil());

	@Mock
	private NumberFormat numberFormatter;

	@Before
	public void setup() {

		ReflectionTestUtils.setField(commonResponseTransformer, "intlNrSupplierExclusionList", new ArrayList<>());
		ReflectionTestUtils.setField(commonResponseTransformer, "propertyRulesMaxCount", 4);
		ReflectionTestUtils.setField(commonResponseTransformer, "corpSegments", new HashSet<>(Arrays.asList("1135", "1152")));
	}

	@Test
	public void
	testConvertValidateCouponResponse(){
		ValidCouponResult validCouponresult = new ValidCouponResult();
		ValidateCouponResponse validateCouponResponse = new ValidateCouponResponse.Builder()
				.buildValidCouponResult(validCouponresult )
				.build();
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		validateCouponResponse.setDisplayPriceBreakDown(displayPriceBreakDown );
		String bnplUnavailableMsg = "bnpl unavailable";
		validateCouponResponse.setBnplUnavailableMsg(bnplUnavailableMsg);
		Map<String, Object> nonBnplAppliedCouponDetailsMap = new HashMap<>();
		boolean bnplDisabledDueToNonBnplCouponApplied = false;
		String nonBnplCouponAppliedCode = "current";
		MpFareHoldStatus mpFareHoldStatus = new MpFareHoldStatus();
		mpFareHoldStatus.setBookingAmount(0f);
		mpFareHoldStatus.setHoldEligible(true);
		mpFareHoldStatus.setExpiry(new Long(123456));
		mpFareHoldStatus.setEligibleForHoldBooking(true);
		nonBnplAppliedCouponDetailsMap.put(Constants.BNPL_DISABLED_DUE_TO_NON_BNPL_COUPON_APPLIED, bnplDisabledDueToNonBnplCouponApplied);
		nonBnplAppliedCouponDetailsMap.put(Constants.NON_BNPL_COUPON_APPLIED_CODE, nonBnplCouponAppliedCode);
		Mockito.when(commonResponseTransformer.fetchNonBnplAppliedCouponDetails(Mockito.any())).thenReturn(nonBnplAppliedCouponDetailsMap);
		Mockito.when(fareHoldHelper.getMpFareHoldStatus((Collection<RoomType>) Mockito.any())).thenReturn(mpFareHoldStatus);
		Mockito.when(fareHoldHelper.getBookNowDetails(Mockito.any(),Mockito.anyBoolean(),Mockito.anyString())).thenReturn(new BookNowDetails());
		validateCouponResponse.getValidCouponResult().setFullPayment(new FullPayment());
		validateCouponResponse.setFlexiDetailBottomSheet(new FlexiDetailBottomSheet());
		validateCouponResponse.getFlexiDetailBottomSheet().setSelected(new Selected());
		validateCouponResponse.getFlexiDetailBottomSheet().getSelected().setSubTitle("test");
		validateCouponResponse.setUpdatedUpsellOptions(new ArrayList<>());
		UpdatedUpsellOptions updatedUpsellOptions = new UpdatedUpsellOptions();
		updatedUpsellOptions.setSuccessDisplayText("text");
		validateCouponResponse.getUpdatedUpsellOptions().add(updatedUpsellOptions);
		ValidateCouponResponseBody resp = discountResponseTransformer.convertValidateCouponResponse(validateCouponResponse, null,"IN", false);
		Assert.assertNotNull(resp);
		//Assert.assertEquals(bnplUnavailableMsg, resp.getTotalPricing().getBnplUnavailableMsg());

		validateCouponResponse.getValidCouponResult().setExpressCheckoutDetail(new ExpressCheckoutDetail());
		Assert.assertNotNull(discountResponseTransformer.convertValidateCouponResponse(validateCouponResponse, null,"IN",false).getTotalPricing().getExpressCheckoutDetail());

		validCouponresult.setBnplVariant(BNPLVariant.BNPL_AT_0);
		validateCouponResponse = new ValidateCouponResponse.Builder()
				.buildValidCouponResult(validCouponresult )
				.build();
		validateCouponResponse.setShowDisabledBnplDetails(true);
		validateCouponResponse.setUserLevelBnplDisabled(true);
		Assert.assertNotNull(discountResponseTransformer.convertValidateCouponResponse(validateCouponResponse, null,"IN",false));

		validCouponresult.setBnplVariant(BNPLVariant.BNPL_AT_1);
		validateCouponResponse = new ValidateCouponResponse.Builder()
				.buildValidCouponResult(validCouponresult )
				.build();
		validateCouponResponse.setShowDisabledBnplDetails(true);
		validateCouponResponse.setUserLevelBnplDisabled(true);
		Assert.assertNotNull(discountResponseTransformer.convertValidateCouponResponse(validateCouponResponse, null,"IN",false));

		validCouponresult.setBnplVariant(BNPLVariant.BNPL_NOT_APPLICABLE);
		validateCouponResponse = new ValidateCouponResponse.Builder()
				.buildValidCouponResult(validCouponresult )
				.build();
		validateCouponResponse.setShowDisabledBnplDetails(true);
		validateCouponResponse.setUserLevelBnplDisabled(true);
		Assert.assertNotNull(discountResponseTransformer.convertValidateCouponResponse(validateCouponResponse, null,"IN",false));

		validCouponresult.setBnplVariant(BNPLVariant.BNPL_AT_0);
		validateCouponResponse = new ValidateCouponResponse.Builder()
				.buildValidCouponResult(validCouponresult )
				.build();
		validateCouponResponse.setShowDisabledBnplDetails(false);
		validateCouponResponse.setUserLevelBnplDisabled(true);
		Assert.assertNotNull(discountResponseTransformer.convertValidateCouponResponse(validateCouponResponse, null,"IN",false));

		validCouponresult.setBnplVariant(BNPLVariant.BNPL_AT_0);
		validateCouponResponse = new ValidateCouponResponse.Builder()
				.buildValidCouponResult(validCouponresult )
				.build();
		validateCouponResponse.setShowDisabledBnplDetails(true);
		validateCouponResponse.setUserLevelBnplDisabled(false);
		Assert.assertNotNull(discountResponseTransformer.convertValidateCouponResponse(validateCouponResponse, null,"IN",false));

		validCouponresult.setBnplVariant(null);
		validateCouponResponse = new ValidateCouponResponse.Builder()
				.buildValidCouponResult(validCouponresult )
				.build();
		validateCouponResponse.setShowDisabledBnplDetails(true);
		validateCouponResponse.setUserLevelBnplDisabled(false);
		Assert.assertNotNull(discountResponseTransformer.convertValidateCouponResponse(validateCouponResponse, null,"IN",false));

		DisplayPriceBreakDown displayPriceBrkDwn = new DisplayPriceBreakDown();
		BestCoupon couponInfo = new BestCoupon();
		couponInfo.setCouponCode("abc");
		couponInfo.setDiscountAmount(0.0);
		couponInfo.setDescription("abc123");
		couponInfo.setAutoApplicable(true);
		couponInfo.setBnplAllowed(false);
		couponInfo.setTncUrl("https://www.abc.com");
		couponInfo.setDisabled(false);
		displayPriceBrkDwn.setCouponInfo(couponInfo);
		validCouponresult.setBnplVariant(BNPLVariant.BNPL_AT_0);
		validateCouponResponse = new ValidateCouponResponse.Builder()
				.buildValidCouponResult(validCouponresult )
				.build();
		validateCouponResponse.setDisplayPriceBreakDown(displayPriceBrkDwn);
		validateCouponResponse.setShowDisabledBnplDetails(true);
		validateCouponResponse.setUserLevelBnplDisabled(false);
		Assert.assertNotNull(discountResponseTransformer.convertValidateCouponResponse(validateCouponResponse, null,"IN",false));
		Map<String,String[]> parameterMap=new HashMap<>();
		parameterMap.put("funnel",new String[]{"myPartner"});
		Map<String, Double> hybridDiscount= new HashMap<>();
		hybridDiscount.put("CTW",126.0);
		couponInfo.setHybridDiscounts(hybridDiscount);
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("<span class=\"latoBold greenText\">Get Promo Cash worth &#8377; %s on this hotel. </span>");
		MDC.put(FUNNEL_SOURCE,FUNNEL_SOURCE_MYPARTNER);
		MDC.put(CLIENT, DeviceConstant.DESKTOP.name());
		ValidateCouponResponseBody validateCouponResponseBody=discountResponseTransformer.convertValidateCouponResponse(validateCouponResponse, null,"IN",false);
		Assert.assertNotNull(validateCouponResponseBody);
		Assert.assertNotNull(validateCouponResponseBody.getHotelPersuasions());
		Assert.assertNotNull(validateCouponResponseBody.getHotelPersuasions().get("cashbackHeroPersuasion"));
		Assert.assertEquals(validateCouponResponseBody.getHotelPersuasions().get("cashbackHeroPersuasion").getPersuasionText(),"<span class=\"latoBold greenText\">Get Promo Cash worth &#8377; 126 on this hotel. </span>");

		validateCouponResponse.setAdultCount(2);
		validateCouponResponse.setSbppExpValue("1");
		validateCouponResponse.setRoomCount(1);
		validateCouponResponseBody=discountResponseTransformer.convertValidateCouponResponse(validateCouponResponse, null,"IN",false);

		//Assertions

		validateCouponResponse.setAdultCount(2);
		validateCouponResponse.setSbppExpValue("2");
		validateCouponResponse.setRoomCount(1);
		validateCouponResponseBody=discountResponseTransformer.convertValidateCouponResponse(validateCouponResponse, null,"IN",false);
		//Assertions
	}
	
}
