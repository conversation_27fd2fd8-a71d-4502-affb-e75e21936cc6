package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.Coupon;
import com.mmt.hotels.clientgateway.response.EMIDetail;
import com.mmt.hotels.emi.detail.EmiDetailRequest;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.emi.UpdateEmiDetailRequest;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class EMIRequestTransformerTest {

    @InjectMocks
    EMIRequestTransformer emiRequestTransformer;

    @Test
    public void convertEmiRequestTest() {
        UpdatedEmiRequest updatedEmiRequest = new UpdatedEmiRequest();
        updatedEmiRequest.setDeviceDetails(new DeviceDetails());
        updatedEmiRequest.setEmiDetail(new EMIDetail());

        updatedEmiRequest.setRequestDetails(new RequestDetails());

        updatedEmiRequest.setSearchCriteria(new UpdatedEMISearchCriteria());
        updatedEmiRequest.getSearchCriteria().setRoomStayCandidates(new ArrayList<RoomStayCandidate>());
        updatedEmiRequest.getSearchCriteria().getRoomStayCandidates().add(new RoomStayCandidate());
        updatedEmiRequest.getSearchCriteria().getRoomStayCandidates().get(0).setChildAges(new ArrayList<>());
        updatedEmiRequest.getSearchCriteria().getRoomStayCandidates().get(0).getChildAges().add(1);

        UpdateEmiDetailRequest updateEmiDetailRequest = emiRequestTransformer.convertEmiRequest(updatedEmiRequest, new CommonModifierResponse());
        org.springframework.util.Assert.notNull(updateEmiDetailRequest);
    }


    @Test
    public void testBuildEmiDetailRequest() {
        // Setup
        EMIDetailRequest emiDetailRequest = new EMIDetailRequest();
        emiDetailRequest.setPricingKeys(Arrays.asList("key1", "key2"));
        emiDetailRequest.setCheckInDate("2023-01-01");
        emiDetailRequest.setCheckOutDate("2023-01-05");
        emiDetailRequest.setCorrelationKey("correlationKey");
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource("funnelSource");
        requestDetails.setPageContext("pageContext");
        emiDetailRequest.setRequestDetails(requestDetails);
        emiDetailRequest.setSelectedPricingKey("selectedKey");
        emiDetailRequest.setBrand("brand");
        emiDetailRequest.setExpData("experimentData");
        emiDetailRequest.setCountryCode("countryCode");
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setAdultCount(5);
        emiDetailRequest.setRoomStayCandidates(Collections.singletonList(roomStayCandidate));
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setDeviceType("mobile");
        deviceDetails.setBookingDevice("desktop");
        emiDetailRequest.setDeviceDetails(deviceDetails);
        Coupon coupon = new Coupon();
        coupon.setBankName("bankName");
        coupon.setCode("code");
        coupon.setCouponAmount(100);
        coupon.setNoCostEmiApplicable(true);
        emiDetailRequest.setCoupons(Collections.singletonList(coupon));

        // Invocation
        EmiDetailRequest result = emiRequestTransformer.buildEmiDetailRequest(emiDetailRequest);

        // Verification
        Assert.assertNotNull(result);
        Assert.assertEquals(Arrays.asList("key1", "key2"), result.getPricingKeysList());
        Assert.assertEquals("2023-01-01", result.getCheckin());
        Assert.assertEquals("2023-01-05", result.getCheckout());
        Assert.assertEquals("correlationKey", result.getCorrelationKey());
        Assert.assertEquals("funnelSource", result.getFunnelSource());
        Assert.assertEquals("selectedKey", result.getSelectedPricingKey());
        Assert.assertEquals("brand", result.getBrand());
        Assert.assertEquals("experimentData", result.getExperimentData());
        Assert.assertEquals("countryCode", result.getCountryCode());
        Assert.assertEquals("pageContext", result.getPageContext());
        Assert.assertEquals(1, result.getRoomStayCandidate().getGuestDetailsList().size());
    }

    @Test
    public void buildGuestCounts_shouldReturnGuestCountWithCorrectAdultCountAndChildAges() {
        com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidateCG = new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        roomStayCandidateCG.setAdultCount(2);
        roomStayCandidateCG.setChildAges(Arrays.asList(5, 8));

        List<GuestCount> result = ReflectionTestUtils.invokeMethod(emiRequestTransformer, "buildGuestCounts", roomStayCandidateCG);

        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
        Assert.assertEquals("2", result.get(0).getCount());
        Assert.assertEquals(Arrays.asList(5, 8), result.get(0).getAges());
    }
}
