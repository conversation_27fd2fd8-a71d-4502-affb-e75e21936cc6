package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gommt.hotels.orchestrator.enums.SubpageContext;
import com.gommt.hotels.orchestrator.model.objects.HotelSearchCriteria;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.*;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.helpers.FilterHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.filter.*;
import com.mmt.hotels.clientgateway.response.filter.FilterPage;
import com.mmt.hotels.clientgateway.response.searchHotels.SortCriteria;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.ContextDetails;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.filter.FilterRange;
import com.mmt.hotels.model.request.FilterSearchMetaDataResponse;
import com.mmt.hotels.model.response.dpt.ContextualFilterResponse;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import com.mmt.hotels.orchestrator.enums.SubPageContext;
import com.mmt.hotels.util.Tuple;
import junit.framework.Assert;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.lang.reflect.Method;
import java.lang.reflect.Field;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.transformer.response.SearchHotelsResponseTransformer.gson;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class FilterResponseTransformerTest {
	
	@InjectMocks
	FilterResponseTransformer filterResponseTransformer;

	@Mock
	private FilterHelper filterHelper;
	@Mock
	PolyglotService polyglotService;

	@Spy
	Utility utility;
	@Mock
	private DateUtil dateUtil;
	@Spy
	private CommonResponseTransformer commonResponseTransformer;

	private FilterPillConfig filterPillConfig;

	ObjectMapper mapper = new ObjectMapper();


	@Before
	public void init() {
		mapper.writerWithView(PIIView.External.class);
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		Map<String,List<Integer>> priceHistConfig = new HashMap<>();
		List<Integer> config = new ArrayList<>();
		config.add(10);
		config.add(1000);
		priceHistConfig.put("INR",config);

		Map<String, Map<String, AmenityCategory>> amenitiesCategoryConfig = new HashMap<>();
		AmenityCategory guestsLoveCategory = new AmenityCategory();
		guestsLoveCategory.setTitle("Guests Love");
		guestsLoveCategory.setAmenities(new ArrayList<String>(){{add("Swimming Pool"); add("Fireplace");}});

		AmenityCategory generalCategory = new AmenityCategory();
		generalCategory.setTitle("General");
		generalCategory.setAmenities(new ArrayList<String>(){{add("Outdoor Sports"); add("Bonfire");}});

		AmenityCategory transferCategory = new AmenityCategory();
		transferCategory.setTitle("Transfers");
		transferCategory.setAmenities(new ArrayList<String>(){{add("Railway Station Transfers"); add("Airport Transfers");}});


		Map<String, Map<String, AmenityCategory>> amenitiesCategoryConfigPolyGlot = new HashMap<>();
		AmenityCategory guestsLoveCategory2 = new AmenityCategory();
		guestsLoveCategory2.setTitle("GUESTS_LOVE");
		guestsLoveCategory2.setAmenities(new ArrayList<String>(){{add("Swimming Pool"); add("Fireplace");}});

		AmenityCategory generalCategory2 = new AmenityCategory();
		generalCategory2.setTitle("GENERAL");
		generalCategory2.setAmenities(new ArrayList<String>(){{add("Outdoor Sports"); add("Bonfire");}});

		AmenityCategory transferCategory2 = new AmenityCategory();
		transferCategory.setTitle("TRANSFERS");
		transferCategory2.setAmenities(new ArrayList<String>(){{add("Railway Station Transfers"); add("Airport Transfers");}});

		amenitiesCategoryConfig.put("HOMESTAY", new HashMap<String, AmenityCategory>(){{put("GUESTS_LOVE", guestsLoveCategory); put("GENERAL", generalCategory); put("TRANSFERS", transferCategory);}});
		amenitiesCategoryConfigPolyGlot.put("HOMESTAY", new HashMap<String, AmenityCategory>(){{put("GUESTS_LOVE", guestsLoveCategory2); put("GENERAL", generalCategory2); put("TRANSFERS", transferCategory2);}});
		ReflectionTestUtils.setField(filterResponseTransformer,"defaultPriceHistConfig" , priceHistConfig);
		ReflectionTestUtils.setField(filterResponseTransformer,"defaultPriceHistConfigCorp" , priceHistConfig);
		ReflectionTestUtils.setField(filterResponseTransformer,"amenitiesCategoryConfig" , amenitiesCategoryConfig);
		ReflectionTestUtils.setField(filterResponseTransformer,"amenitiesCategoryConfigPolyGlot" , amenitiesCategoryConfigPolyGlot);
		ReflectionTestUtils.setField(filterResponseTransformer, "bnplApWindowLimit", 2);
		ReflectionTestUtils.setField(filterResponseTransformer, "pillConfigOrderMyBiz", Arrays.asList("SORT", "FILTERS", "PRICE", "POPULAR", "LOCALITY", "STAR_RATING"));
		String cityWisePriceConfig = "{\"priceBucket3\":[\"CTDUB\"]}";
		String cityWisePriceBucketsConfig = "{\"INR\":{\"priceBucket1\":[8000,2000,2000],\"priceBucket2\":[9000,2000,3000],\"priceBucket3\":[13000,3000,4000],\"priceBucket4\":[15000,3000,6000],\"priceBucket5\":[20000,4000,8000],\"priceBucket6\":[25000,5000,10000]}}";
		String defaultPriceConfig = "{\"INR\":[[0,2500],[2500,5500],[5500,8500],[8500,11500],[11500,14500],[14500,15000],[15000,30000]]}";
		String dptFilterListLimit = "{\"POPULAR\":4}";
		Map<String, Integer> dptFiltersListLimitMap = gson.fromJson(dptFilterListLimit, new com.google.gson.reflect.TypeToken<Map<String, Integer>>() {
		}.getType());

		Map<String, HashSet<String>> cityWisePriceConfigMapIH = gson.fromJson(cityWisePriceConfig, new com.google.gson.reflect.TypeToken<Map<String, HashSet<String>>>() {
		}.getType());
		Map<String, Map<String, List<Integer>>> cityWisePriceBucketsConfigMapIH = gson.fromJson(cityWisePriceBucketsConfig, new com.google.gson.reflect.TypeToken<Map<String, Map<String, List<Integer>>>>() {
		}.getType());
		Map<String, List<List<Integer>>> defaultPriceConfigIHMap = gson.fromJson(defaultPriceConfig, new com.google.gson.reflect.TypeToken<Map<String, List<List<Integer>>>>() {
		}.getType());
		ReflectionTestUtils.setField(filterResponseTransformer, "filterHelper", filterHelper);
		ReflectionTestUtils.setField(filterResponseTransformer,"cityWisePriceConfigMapIH",cityWisePriceConfigMapIH);
		ReflectionTestUtils.setField(filterResponseTransformer,"cityWisePriceBucketsConfigMapIH",cityWisePriceBucketsConfigMapIH);
		ReflectionTestUtils.setField(filterResponseTransformer,"defaultPriceConfigIHMap",defaultPriceConfigIHMap);
		ReflectionTestUtils.setField(filterResponseTransformer, "dptFiltersListLimitMap", dptFiltersListLimitMap);
		List<String> amenitiesList = new ArrayList<>();
		amenitiesList.add("Kitchen");
		ReflectionTestUtils.setField(filterResponseTransformer, "altAccoAmenities",amenitiesList);
		Gson gson = new Gson();
		String pillConfig = "{\"stickyFilterPills\":{\"SORT\":{\"id\":\"SORT\",\"title\":\"SORT_PILL_TEXT\",\"sequence\":1,\"categories\":[]},\"FILTERS\":{\"id\":\"FILTERS\",\"sequence\":2,\"title\":\"FILTER_PILL_TEXT\",\"categories\":[]}},\"dynamicFilterPills\":{\"PRICE\":{\"id\":\"PRICE\",\"sequence\":3,\"title\":\"PRICE_PILL_TEXT\",\"categories\":[\"PRICE\"]},\"POPULAR\":{\"id\":\"POPULAR\",\"sequence\":4,\"title\":\"POPULAR_PILL_TEXT\",\"categories\":[\"POPULAR\"]},\"LOCALITY\":{\"id\":\"LOCALITY\",\"sequence\":5,\"title\":\"LOCALITY_PILL_TEXT\",\"categories\":[]},\"STAR_RATING\":{\"id\":\"STAR_RATING\",\"sequence\":6,\"title\":\"STAR_RATING_PILL_TEXT\",\"categories\":[\"STAR_CATEGORY\"]}},\"dynamicFilterPillsDPT\":{\"PRICE\":{\"id\":\"PRICE\",\"sequence\":3,\"title\":\"PRICE_PILL_TEXT\",\"categories\":[\"PRICE\"]},\"POPULAR\":{\"id\":\"POPULAR\",\"sequence\":4,\"title\":\"POPULAR_PILL_TEXT\",\"categories\":[\"POPULAR\"]},\"LOCALITY\":{\"id\":\"LOCALITY\",\"sequence\":5,\"title\":\"LOCALITY_PILL_TEXT\",\"categories\":[]},\"STAR_RATING\":{\"id\":\"STAR_RATING\",\"sequence\":6,\"title\":\"STAR_RATING_PILL_TEXT\",\"categories\":[\"STAR_CATEGORY\"]}}}";
		filterPillConfig = gson.fromJson(pillConfig, new TypeToken<FilterPillConfig>() {
		}.getType());

		ReflectionTestUtils.setField(filterResponseTransformer,"filterMinItemsLimit" ,7);

		Map<String, Integer> distanceSortingModesPriorityMap = new HashMap<>();
		distanceSortingModesPriorityMap.put("wd", 0);
		distanceSortingModesPriorityMap.put("dd", 1);
		ReflectionTestUtils.setField(filterResponseTransformer, "distanceSortingModesPriorityMap", distanceSortingModesPriorityMap);
	}

	@Test
	public void getExpDataMapTest() {
		// Prepare the input data
		String expDataReq = "{\"key1\": \"value1\", \"key2\": \"value2\"}";
		Map<String, String> response = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "getExpDataMap", expDataReq);
		// Assert the result
		Assert.assertNotNull(response);
		Assert.assertEquals(response.size(), 2);
	}
	@Test
	public void convertFilterResponseTest() {
		when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("{filter_text}");
		FilterSearchMetaDataResponse response = new FilterSearchMetaDataResponse();
		response.setFilterDataMap(new HashMap<FilterGroup, List<Filter>>());
		response.getFilterDataMap().put(FilterGroup.STAR_RATING, new ArrayList<>());
		FilterConfiguration filterConfiguration = new FilterConfiguration();
		filterConfiguration.setConditions(new LinkedHashMap<>());
		filterConfiguration.getConditions().put("test", new ArrayList<String>());
		filterConfiguration.setFilters(new LinkedHashMap<>());
		filterConfiguration.getFilters().put("STAR_RATING", new FilterConfigCategory());
		filterConfiguration.getFilters().get("STAR_RATING").setGroups(new LinkedHashMap<>());
		filterConfiguration.getFilters().get("STAR_RATING").getGroups().put("STAR_RATING", new LinkedHashMap<>());
		filterConfiguration.getFilters().get("STAR_RATING").getGroups().get("STAR_RATING").put("STAR_RATING", new FilterConfigDetail());
		FilterCountRequest filterCountRequest = new FilterCountRequest();
		filterCountRequest.setSearchCriteria(new SearchHotelsCriteria());
		filterCountRequest.getSearchCriteria().setCheckIn("2023-11-05");
		filterCountRequest.setRequestDetails(new RequestDetails());
		filterCountRequest.getSearchCriteria().setCurrency("INR");
		LinkedHashMap<String,String>expDataMap = new LinkedHashMap<>();
		filterResponseTransformer.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, new CommonModifierResponse(), new FilterPillConfigurationWrapper());
		filterConfiguration.getFilters().get("STAR_RATING").getGroups().put("STAR_RATING", null);
		filterConfiguration.getFilters().put("HOTEL_PRICE", new FilterConfigCategory());
		filterConfiguration.getFilters().get("HOTEL_PRICE").setGroups(new LinkedHashMap<>());
		filterConfiguration.getFilters().get("HOTEL_PRICE").getGroups().put("HOTEL_PRICE", new LinkedHashMap<>());
		filterConfiguration.getFilters().get("HOTEL_PRICE").getGroups().get("HOTEL_PRICE").put("HOTEL_PRICE", new FilterConfigDetail());
		filterConfiguration.getFilters().put("HOTEL_PRICE_BUCKET", new FilterConfigCategory());
		filterConfiguration.getFilters().get("HOTEL_PRICE_BUCKET").setGroups(new LinkedHashMap<>());
		filterConfiguration.getFilters().get("HOTEL_PRICE_BUCKET").getGroups().put("HOTEL_PRICE_BUCKET", new LinkedHashMap<>());
		filterConfiguration.getFilters().get("HOTEL_PRICE_BUCKET").getGroups().get("HOTEL_PRICE_BUCKET").put("HOTEL_PRICE_BUCKET", new FilterConfigDetail());

		Assert.assertNotNull(filterResponseTransformer.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap,new CommonModifierResponse(), new FilterPillConfigurationWrapper()));

		filterConfiguration.getFilters().put("HOUSE_RULES",new FilterConfigCategory());
		filterConfiguration.getFilters().get("HOUSE_RULES").setGroups(new LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>>(){{put("HOUSE_RULES", null);}});
		Assert.assertNotNull(filterResponseTransformer.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, new CommonModifierResponse(), new FilterPillConfigurationWrapper()));


		filterCountRequest.getSearchCriteria().setCountryCode("THA");
		filterCountRequest.getSearchCriteria().setCityCode("CTDUB");
		Assert.assertNotNull(filterResponseTransformer.convertFilterResponse(response, filterConfiguration, filterCountRequest, expDataMap, new CommonModifierResponse(), new FilterPillConfigurationWrapper()));
		filterCountRequest.getSearchCriteria().setCityCode("CTXXX");
		Assert.assertNotNull(filterResponseTransformer.convertFilterResponse(response, filterConfiguration, filterCountRequest, expDataMap, new CommonModifierResponse(), new FilterPillConfigurationWrapper()));
		filterConfiguration.getFilters().put("ROOMS_AND_BEDS",new FilterConfigCategory());
		filterConfiguration.getFilters().get("ROOMS_AND_BEDS").setGroups(new LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>>(){{
			put("BED", new LinkedHashMap<String, FilterConfigDetail>(){{put("BED", new FilterConfigDetail());}});
			put("BEDROOM", new LinkedHashMap<String, FilterConfigDetail>(){{put("BEDROOM", new FilterConfigDetail());}});
		}});
		filterConfiguration.setRankOrder(new LinkedHashMap<String,Integer>(){{put("ROOMS_AND_BEDS", 1);}});
		filterCountRequest.setExpData("{HAFC:T,CRF:B}");
		expDataMap.put("HAFC","T"); expDataMap.put("CRF","B");
		FilterResponse filterResponse = filterResponseTransformer.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, new CommonModifierResponse(), new FilterPillConfigurationWrapper());
		Assert.assertNotNull(filterResponse);
		Assert.assertEquals(filterResponse.getFilterList().size(), 1);
		Assert.assertTrue(filterResponse.getFilterList().stream().filter(filterCategory -> "ROOMS_AND_BEDS".equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().isPresent());




		filterConfiguration.getFilters().put("AMENITIES",new FilterConfigCategory());
		filterConfiguration.getFilters().get("AMENITIES").setGroups(new LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>>(){{put("AMENITIES", null);}});
		filterConfiguration.setRankOrder(new LinkedHashMap<String,Integer>(){{put("AMENITIES", 2);}});
		expDataMap.clear();expDataMap.put("HAFC","T");
		filterCountRequest.setExpData("{HAFC:T}");
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setFunnelSource("HOMESTAY");
		filterCountRequest.setRequestDetails(requestDetails);
		Filter guestLoveFilter = new Filter();
		guestLoveFilter.setFilterValue("Swimming Pool");
		guestLoveFilter.setFilterGroup(FilterGroup.AMENITIES);
		Filter generalFilter = new Filter();
		generalFilter.setFilterValue("Outdoor Sports");
		generalFilter.setFilterGroup(FilterGroup.AMENITIES);
		Filter transferFilter = new Filter();
		transferFilter.setFilterValue("Railway Station Transfers");
		transferFilter.setFilterGroup(FilterGroup.AMENITIES);
		response.getFilterDataMap().put(FilterGroup.AMENITIES, new ArrayList<Filter>(){{add(guestLoveFilter); add(generalFilter); add(transferFilter);}});
		response.setContextDetails(new ContextDetails());
		response.getContextDetails().setContext("all|all|all|all");
		response.getContextDetails().setAltAccoIntent(true);
		response.setLocationName("Test");
		FilterResponse amenitiesFilterResponse = filterResponseTransformer.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, new CommonModifierResponse(), new FilterPillConfigurationWrapper());
		Assert.assertNotNull(amenitiesFilterResponse);
		Assert.assertTrue(amenitiesFilterResponse.getFilterList().stream().filter(filterCategory -> "AMENITIES".equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().isPresent());
		Assert.assertEquals(amenitiesFilterResponse.getFilterList().stream().filter(filterCategory -> "AMENITIES".equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().get().getFilterCollection().size(),3);


		when(filterHelper.fetchPriceTitle(Mockito.any())).thenReturn("");

		filterConfiguration.getFilters().put("PRICE",new FilterConfigCategory());
		filterConfiguration.getFilters().get("PRICE").setGroups(new LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>>(){{put("HOTEL_PRICE", null);}});
		filterConfiguration.getFilters().put("POPULAR",new FilterConfigCategory());
		filterConfiguration.getFilters().get("POPULAR").setGroups(new LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>>(){{put("HOTEL_PRICE", null);}});
		filterConfiguration.getFilters().put("PRICE_BUCKET",new FilterConfigCategory());
		filterConfiguration.getFilters().get("PRICE_BUCKET").setGroups(new LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>>(){{put("HOTEL_PRICE_BUCKET", null);}});
		filterConfiguration.setRankOrder(new LinkedHashMap<String,Integer>(){{put("PRICE", 1); put("PRICE_BUCKET", 2);}});
		filterCountRequest.setClient("DESKTOP");

		Filter priceFilter = new Filter();
		priceFilter.setFilterRange(new FilterRange());
		priceFilter.getFilterRange().setMinValue(0);
		priceFilter.getFilterRange().setMinValue(500);
		priceFilter.setRangeFilter(true);
		priceFilter.setFilterGroup(FilterGroup.HOTEL_PRICE);

		response.getFilterDataMap().put(FilterGroup.HOTEL_PRICE, new ArrayList<Filter>(){{add(priceFilter);}});

		Filter priceBucketFilter = new Filter();
        priceBucketFilter.setFilterRange(new FilterRange());
        priceBucketFilter.getFilterRange().setMinValue(0);
        priceBucketFilter.getFilterRange().setMinValue(500);
        priceBucketFilter.setRangeFilter(true);
        priceBucketFilter.setFilterGroup(FilterGroup.HOTEL_PRICE_BUCKET);

        response.getFilterDataMap().put(FilterGroup.HOTEL_PRICE_BUCKET, new ArrayList<Filter>() {{
            add(priceBucketFilter);
        }});

        expDataMap.clear();
        expDataMap.put("CRF", "B");
        filterCountRequest.setExpData("{CRF:B}");
        filterCountRequest.getSearchCriteria().setCountryCode("UNI");
        filterCountRequest.setFeatureFlags(new FeatureFlags());
        filterCountRequest.getFeatureFlags().setOriginListingMap(true);
        FilterResponse priceFilterResponse = filterResponseTransformer.convertFilterResponse(response, filterConfiguration, filterCountRequest, expDataMap, new CommonModifierResponse(), new FilterPillConfigurationWrapper());
        Assert.assertTrue(priceFilterResponse.getFilterList().stream().filter(filterCategory -> "PRICE".equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().isPresent());
        Assert.assertFalse(priceFilterResponse.getFilterList().stream().filter(filterCategory -> "PRICE_BUCKET".equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().isPresent());


        expDataMap.put("HAFC", "T");
        expDataMap.put("CRF", "B");
        filterCountRequest.setExpData("{HAFC:T,CRF:B}");
        filterCountRequest.getFeatureFlags().setOriginListingMap(false);
        FilterResponse priceBucketFilterResponse = filterResponseTransformer.convertFilterResponse(response, filterConfiguration, filterCountRequest, expDataMap, new CommonModifierResponse(), new FilterPillConfigurationWrapper());
        Assert.assertTrue(priceBucketFilterResponse.getFilterList().stream().filter(filterCategory -> "PRICE_BUCKET".equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().isPresent());
        Assert.assertFalse(priceBucketFilterResponse.getFilterList().stream().filter(filterCategory -> "PRICE".equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().isPresent());

        filterCountRequest.getSearchCriteria().setCountryCode("IN");
        filterCountRequest.setFeatureFlags(new FeatureFlags());
        filterCountRequest.getFeatureFlags().setOriginListingMap(true);
        priceBucketFilterResponse = filterResponseTransformer.convertFilterResponse(response, filterConfiguration, filterCountRequest, expDataMap, new CommonModifierResponse(), new FilterPillConfigurationWrapper());
        Assert.assertTrue(priceBucketFilterResponse.getFilterList().stream().filter(filterCategory -> "PRICE_BUCKET".equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().isPresent());
        Assert.assertFalse(priceBucketFilterResponse.getFilterList().stream().filter(filterCategory -> "PRICE".equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().isPresent());

        Mockito.lenient().when(commonResponseTransformer.buildFilterCG(Mockito.any())).thenReturn(new com.mmt.hotels.clientgateway.response.filter.Filter());
        Map<String, List<Filter>> filterCategoryMap = new HashMap<>();
        List<Filter> filterList = new ArrayList<>();
        filterList.add(new Filter());
        filterCategoryMap.put("AMENITIES", filterList);
        response.setFilterCategoryMap(filterCategoryMap);
        response.setMatchmakerFilterList(new ArrayList<>());
        response.getMatchmakerFilterList().add(new Filter());
        response.setLocationName("");
		org.junit.Assert.assertNotNull(filterResponseTransformer.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, new CommonModifierResponse(), new FilterPillConfigurationWrapper()));


		FilterResponse localityFilterResponse;
		filterConfiguration.getFilters().put(Constants.LOCALITY_GROUP,new FilterConfigCategory());
		filterConfiguration.setRankOrder(new LinkedHashMap<String,Integer>(){{put(Constants.LOCALITY_GROUP, 3);}});
		filterCountRequest.setFeatureFlags(new FeatureFlags());

		filterCountRequest.getFeatureFlags().setFilterRanking(true);
		localityFilterResponse = filterResponseTransformer.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, new CommonModifierResponse(), new FilterPillConfigurationWrapper());
		Assert.assertTrue(localityFilterResponse.getFilterList().stream().filter(filterCategory -> Constants.LOCALITY_GROUP.equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().isPresent());


		FilterResponse contextualFilterResponse;
		expDataMap.put(FILTER_PILL_EXP, EXP_TRUE_VALUE);
		expDataMap.put(HTL_PILLS_ENABLED_EXP, String.valueOf(1));
		FilterPillConfigurationWrapper filterPillConfigurationWrapper = new FilterPillConfigurationWrapper();
		ContextualFilterResponse dptContextualFilterResponse = new ContextualFilterResponse();
		dptContextualFilterResponse.setCurrentCohortId("currentCohortId");
		response.setDptContextualFilterResponse(dptContextualFilterResponse);
		contextualFilterResponse = filterResponseTransformer.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, new CommonModifierResponse(), filterPillConfigurationWrapper);
		Assert.assertTrue(contextualFilterResponse.getFilterList().stream().anyMatch(filterCategory -> Constants.LOCALITY_GROUP.equalsIgnoreCase(filterCategory.getCategoryName())));

		MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), "PWA");
		filterCountRequest.getFeatureFlags().setFilterRanking(false);
		CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
		commonModifierResponse.setExtendedUser(new ExtendedUser());
		commonModifierResponse.getExtendedUser().setProfileType("CTA");
		commonModifierResponse.getExtendedUser().setAffiliateId("MYPARTNER");
		localityFilterResponse = filterResponseTransformer.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, commonModifierResponse, new FilterPillConfigurationWrapper());
		Assert.assertFalse(localityFilterResponse.getFilterList().stream().filter(filterCategory -> Constants.LOCALITY_GROUP.equalsIgnoreCase(filterCategory.getCategoryName())).findFirst().isPresent());
		// When locationtype is country, LOCALITY will be the first filter in the list
		filterCountRequest.getSearchCriteria().setLocationType("country");
		Map<String, List<Filter>> localityFilterCateogryMap = new HashMap<>();
		List<Filter> localityFilter = new ArrayList<>();
		localityFilter.add(new Filter());
		filterCountRequest.setFeatureFlags(null);
		filterCategoryMap.put("LOCALITY", localityFilter);
		filterConfiguration.getRankOrder().put("ROOMS_AND_BEDS", 3);
		filterConfiguration.getRankOrder().put("LOCALITY", 2);
		filterConfiguration.getRankOrder().put("AMENITIES",1);
		filterCountRequest.getRequestDetails().setTrafficSource(new TrafficSource());
		filterCountRequest.getRequestDetails().getTrafficSource().setSource("seo");
		localityFilterResponse = filterResponseTransformer.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, commonModifierResponse, new FilterPillConfigurationWrapper());
		Assert.assertTrue(localityFilterResponse.getFilterList().get(0).getCategoryName().equalsIgnoreCase("LOCALITY"));
		// when locationType is not country, then the order is based on the ranking.
		filterCountRequest.getSearchCriteria().setLocationType("city");
		localityFilterResponse = filterResponseTransformer.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, commonModifierResponse, new FilterPillConfigurationWrapper());

		response.setOrgPreferredFilter(true);
		filterConfiguration.getFilters().put("HOTELS_SECTION",new FilterConfigCategory());
		filterConfiguration.getFilters().get("HOTELS_SECTION").setGroups(new LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>>(){{put("HOTELS_SECTION", null);}});
		filterConfiguration.getRankOrder().put("HOTELS_SECTION",1);
		FilterResponse orgPreferredFilter = filterResponseTransformer.convertFilterResponse(response, filterConfiguration, filterCountRequest,expDataMap, commonModifierResponse, new FilterPillConfigurationWrapper());
		Assert.assertTrue(orgPreferredFilter.getFilterList().get(0).getCategoryName().equalsIgnoreCase("HOTELS_SECTION"));
	}

	@Test
	public void testIsRemovePriceBucketFilter_ShouldReturnTrue_WhenAllConditionsAreMet() {
		// Arrange
		FilterCountRequest filterCountRequest = new FilterCountRequest();
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setCountryCode("US");
		filterCountRequest.setSearchCriteria(searchCriteria);
		FeatureFlags featureFlags = new FeatureFlags();
		featureFlags.setOriginListingMap(true);
		filterCountRequest.setFeatureFlags(featureFlags);

		// Act
		boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(
                filterResponseTransformer,
                "isRemovePriceBucketFilter",
                filterCountRequest,
                "B2C"
        ));

		// Assert
		Assert.assertTrue(result);
	}

	@Test
	public void testIsRemovePriceBucketFilter_ShouldReturnFalse_WhenIdContextIsNotB2C() {
		// Arrange
		FilterCountRequest filterCountRequest = new FilterCountRequest();
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setCountryCode("US");
		filterCountRequest.setSearchCriteria(searchCriteria);
		FeatureFlags featureFlags = new FeatureFlags();
		featureFlags.setOriginListingMap(true);
		filterCountRequest.setFeatureFlags(featureFlags);

		// Act
		boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(
                filterResponseTransformer,
                "isRemovePriceBucketFilter",
                filterCountRequest,
                "CORP"
        ));

		// Assert
		Assert.assertFalse(result);
	}

	@Test
	public void testIsRemovePriceBucketFilter_ShouldReturnFalse_WhenCountryCodeIsDOM() {
		// Arrange
		FilterCountRequest filterCountRequest = new FilterCountRequest();
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setCountryCode("DOM");
		filterCountRequest.setSearchCriteria(searchCriteria);
		FeatureFlags featureFlags = new FeatureFlags();
		featureFlags.setOriginListingMap(true);
		filterCountRequest.setFeatureFlags(featureFlags);

		// Act
		boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(
                filterResponseTransformer,
                "isRemovePriceBucketFilter",
                filterCountRequest,
                "B2C"
        ));

		// Assert
		Assert.assertTrue(result);
	}

	@Test
	public void testIsRemovePriceBucketFilter_ShouldReturnFalse_WhenOriginListingMapIsFalse() {
		// Arrange
		FilterCountRequest filterCountRequest = new FilterCountRequest();
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setCountryCode("US");
		filterCountRequest.setSearchCriteria(searchCriteria);
		FeatureFlags featureFlags = new FeatureFlags();
		featureFlags.setOriginListingMap(false);
		filterCountRequest.setFeatureFlags(featureFlags);

		// Act
		boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(
                filterResponseTransformer,
                "isRemovePriceBucketFilter",
                filterCountRequest,
                "B2C"
        ));

		// Assert
		Assert.assertFalse(result);
	}

	@Test
	public void testIsRemovePriceBucketFilter_ShouldReturnFalse_WhenFilterCountRequestIsNull() {
		// Act
		boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(
                filterResponseTransformer,
                "isRemovePriceBucketFilter",
                null,
                "B2C"
        ));

		// Assert
		Assert.assertFalse(result);
	}

	@Test
	public void convertFilterResponseV2Test() {
		// Arrange
//		when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("{filter_text}");

		FilterSearchMetaDataResponse response = new FilterSearchMetaDataResponse();
		response.setFilterDataMap(new HashMap<>());

		response.getFilterDataMap().put(FilterGroup.STAR_RATING, new ArrayList<>());
		// add filters in filterDataMap of response under STAR_RATING group

		Filter filter = new Filter();
		filter.setFilterValue("STAR_RATING");
		filter.setFilterGroup(FilterGroup.STAR_RATING);
		response.getFilterDataMap().get(FilterGroup.STAR_RATING).add(filter);

		response.setCurrency("INR");

		FilterConfigurationV2 filterConfiguration = new FilterConfigurationV2();
		filterConfiguration.setFilterPages(new LinkedHashMap<>());
		filterConfiguration.setRankOrder(new LinkedHashMap<>());

		filterConfiguration.getRankOrder().put("STAR_RATING", 1);

		com.mmt.hotels.clientgateway.businessobjects.FilterPage filterPage = new com.mmt.hotels.clientgateway.businessobjects.FilterPage();
		filterPage.setPage_id("STAR_RATING");
		filterPage.setTitle("Star Rating");

		//Mock filterConfigCategory object STAR_RATING
		com.mmt.hotels.clientgateway.businessobjects.FilterConfigCategory filterConfigCategory = new com.mmt.hotels.clientgateway.businessobjects.FilterConfigCategory();
		filterConfigCategory.setGroups(new LinkedHashMap<>());
		filterConfigCategory.getGroups().put("STAR_RATING", new LinkedHashMap<>());

		//mock filterConfigDetail object STAR_RATING
		// Mock filterConfigDetail object for STAR_RATING
		FilterConfigDetail filterConfigDetail = new FilterConfigDetail();
		filterConfigDetail.setTitle("5 Star");
		filterConfigDetail.setSubTitle("Luxury Hotels");
		filterConfigDetail.setDescription("Top-rated hotels with 5-star amenities");
		filterConfigDetail.setImageUrl("https://example.com/5star.png");
		filterConfigDetail.setInfoText("Exclusive 5-star properties");

// Add the mocked filterConfigDetail to the STAR_RATING group
		filterConfigCategory.getGroups().get("STAR_RATING").put("STAR_RATING", filterConfigDetail);


		filterPage.setFilters(new LinkedHashMap<>());
		filterPage.getFilters().put("STAR_RATING", filterConfigCategory);
		filterConfiguration.getFilterPages().put("STAR_RATING", filterPage);

		FilterCountRequest filterCountRequest = new FilterCountRequest();
		filterCountRequest.setSearchCriteria(new SearchHotelsCriteria());
		filterCountRequest.getSearchCriteria().setCheckIn("2023-11-05");
		filterCountRequest.getSearchCriteria().setCurrency("INR");

		LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
		expDataMap.put("HAFC", "T");

		CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
		commonModifierResponse.setExtendedUser(new ExtendedUser());
		commonModifierResponse.getExtendedUser().setProfileType("PARTNER");

		FilterPillConfigurationWrapper filterPillConfigurationWrapper = new FilterPillConfigurationWrapper();

		// Act
		FilterResponse filterResponse = filterResponseTransformer.convertFilterResponseV2(
				response, filterConfiguration, filterCountRequest, expDataMap, commonModifierResponse, filterPillConfigurationWrapper
		);

		// Assert
		Assert.assertNotNull(filterResponse);
		assertEquals("INR", filterResponse.getCurrency());
		assertTrue(filterResponse.getFilterListV2().stream()
				.anyMatch(page -> "STAR_RATING".equalsIgnoreCase(page.getPageId())));
		// add more assertions
		Assert.assertEquals(filterResponse.getFilterListV2().get(0).getPageId(), "STAR_RATING");
		Assert.assertEquals(filterResponse.getFilterListV2().get(0).getTitle(), "Star Rating");
	}

	@Test
	public void convertFilterResponseV2Fail_Test() {
		// Arrange
//		when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Translated Text");

		FilterSearchMetaDataResponse response = new FilterSearchMetaDataResponse();
		response.setFilterDataMap(new HashMap<>());
		response.getFilterDataMap().put(FilterGroup.STAR_RATING, new ArrayList<>());

		FilterCountRequest filterCountRequest = new FilterCountRequest();
		filterCountRequest.setSearchCriteria(new SearchHotelsCriteria());
		filterCountRequest.getSearchCriteria().setCheckIn("2023-11-05");
		filterCountRequest.getSearchCriteria().setCurrency("INR");

		LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
		expDataMap.put("HAFC", "T");

		CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
		commonModifierResponse.setExtendedUser(new ExtendedUser());
		commonModifierResponse.getExtendedUser().setProfileType("PARTNER");

		FilterPillConfigurationWrapper filterPillConfigurationWrapper = new FilterPillConfigurationWrapper();

		// Act
		FilterResponse filterResponse = filterResponseTransformer.convertFilterResponseV2(
				response, null, filterCountRequest, expDataMap, commonModifierResponse, filterPillConfigurationWrapper
		);

		// Assert
		Assert.assertNull(filterResponse);
	}

	@Test
	public void convertFilterResponseV2Fail2_Test() {
		// Arrange
//		when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Translated Text");

		FilterSearchMetaDataResponse response = new FilterSearchMetaDataResponse();
		response.setFilterDataMap(new HashMap<>());
		response.getFilterDataMap().put(FilterGroup.STAR_RATING, new ArrayList<>());

		FilterCountRequest filterCountRequest = new FilterCountRequest();
		filterCountRequest.setSearchCriteria(new SearchHotelsCriteria());
		filterCountRequest.getSearchCriteria().setCheckIn("2023-11-05");
		filterCountRequest.getSearchCriteria().setCurrency("INR");

		LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
		expDataMap.put("HAFC", "T");

		CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
		commonModifierResponse.setExtendedUser(new ExtendedUser());
		commonModifierResponse.getExtendedUser().setProfileType("PARTNER");

		FilterPillConfigurationWrapper filterPillConfigurationWrapper = new FilterPillConfigurationWrapper();

		FilterConfigurationV2 filterConfigurationV2 = new FilterConfigurationV2();
		filterConfigurationV2.setFilterPages(new LinkedHashMap<>());

		// Act
		FilterResponse filterResponse = filterResponseTransformer.convertFilterResponseV2(
				response, filterConfigurationV2, filterCountRequest, expDataMap, commonModifierResponse, filterPillConfigurationWrapper
		);

		// Assert
		Assert.assertNull(filterResponse);
	}

	@Test
	public void convertFilterResponseV2_GCC_Test() {
		// Arrange
		when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("{filter_text}");

		FilterSearchMetaDataResponse response = new FilterSearchMetaDataResponse();
		response.setFilterDataMap(new HashMap<>());

		response.getFilterDataMap().put(FilterGroup.STAR_RATING, new ArrayList<>());
		// add filters in filterDataMap of response under STAR_RATING group

		Filter filter = new Filter();
		filter.setFilterValue("STAR_RATING");
		filter.setFilterGroup(FilterGroup.STAR_RATING);
		response.getFilterDataMap().get(FilterGroup.STAR_RATING).add(filter);

		response.setCurrency("INR");

		FilterConfigurationV2 filterConfiguration = new FilterConfigurationV2();
		filterConfiguration.setFilterPages(new LinkedHashMap<>());
		filterConfiguration.setRankOrder(new LinkedHashMap<>());

		filterConfiguration.getRankOrder().put("STAR_RATING", 1);
		filterConfiguration.getRankOrder().put("SUGGESTED_FOR_YOU", 2);

		com.mmt.hotels.clientgateway.businessobjects.FilterPage filterPage = new com.mmt.hotels.clientgateway.businessobjects.FilterPage();
		filterPage.setPage_id("STAR_RATING");
		filterPage.setTitle("Star Rating");

		//Mock filterConfigCategory object STAR_RATING
		com.mmt.hotels.clientgateway.businessobjects.FilterConfigCategory filterConfigCategory = new com.mmt.hotels.clientgateway.businessobjects.FilterConfigCategory();
		filterConfigCategory.setGroups(new LinkedHashMap<>());
		filterConfigCategory.getGroups().put("STAR_RATING", new LinkedHashMap<>());
		filterConfigCategory.getGroups().put("POPULAR", new LinkedHashMap<>());

		//mock filterConfigDetail object STAR_RATING
		// Mock filterConfigDetail object for STAR_RATING
		FilterConfigDetail filterConfigDetail = new FilterConfigDetail();
		filterConfigDetail.setTitle("5 Star");
		filterConfigDetail.setSubTitle("Luxury Hotels");
		filterConfigDetail.setDescription("Top-rated hotels with 5-star amenities");
		filterConfigDetail.setImageUrl("https://example.com/5star.png");
		filterConfigDetail.setInfoText("Exclusive 5-star properties");

// Add the mocked filterConfigDetail to the STAR_RATING group
		filterConfigCategory.getGroups().get("STAR_RATING").put("STAR_RATING", filterConfigDetail);


		filterPage.setFilters(new LinkedHashMap<>());
		filterPage.getFilters().put("STAR_RATING", filterConfigCategory);
		filterConfiguration.getFilterPages().put("STAR_RATING", filterPage);

		//Add popular category inside page Suggested for you
		com.mmt.hotels.clientgateway.businessobjects.FilterPage suggestedForYouPage = new com.mmt.hotels.clientgateway.businessobjects.FilterPage();
		suggestedForYouPage.setPage_id("SUGGESTED_FOR_YOU");
		suggestedForYouPage.setTitle("Suggested for you");
		com.mmt.hotels.clientgateway.businessobjects.FilterConfigCategory popularCategory = new com.mmt.hotels.clientgateway.businessobjects.FilterConfigCategory();
		popularCategory.setGroups(new LinkedHashMap<>());
		popularCategory.getGroups().put("POPULAR", new LinkedHashMap<>());
		FilterConfigDetail popularFilterConfigDetail = new FilterConfigDetail();
		popularFilterConfigDetail.setTitle("Popular");
		popularFilterConfigDetail.setSubTitle("Popular Hotels");
		popularFilterConfigDetail.setDescription("Top-rated hotels with popular amenities");
		popularFilterConfigDetail.setImageUrl("https://example.com/popular.png");
		popularFilterConfigDetail.setInfoText("Exclusive popular properties");

		popularCategory.getGroups().get("POPULAR").put("POPULAR", popularFilterConfigDetail);
		suggestedForYouPage.setFilters(new LinkedHashMap<>());
		suggestedForYouPage.getFilters().put("POPULAR", popularCategory);
		filterConfiguration.getFilterPages().put("SUGGESTED_FOR_YOU", suggestedForYouPage);

		FilterCountRequest filterCountRequest = new FilterCountRequest();
		filterCountRequest.setSearchCriteria(new SearchHotelsCriteria());
		filterCountRequest.getSearchCriteria().setCheckIn("2023-11-05");
		filterCountRequest.getSearchCriteria().setCurrency("INR");
		filterCountRequest.setRequestDetails(new RequestDetails());
		filterCountRequest.getRequestDetails().setSiteDomain(AE);
		filterCountRequest.setDeviceDetails(new DeviceDetails());
		filterCountRequest.getDeviceDetails().setBookingDevice("ANDROID");
		filterCountRequest.getDeviceDetails().setAppVersion("9.1.6");

		LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
		expDataMap.put("HAFC", "T");

		CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
		commonModifierResponse.setExtendedUser(new ExtendedUser());
		commonModifierResponse.getExtendedUser().setProfileType("PARTNER");

		FilterPillConfigurationWrapper filterPillConfigurationWrapper = new FilterPillConfigurationWrapper();

		// Act
		FilterResponse filterResponse = filterResponseTransformer.convertFilterResponseV2(
				response, filterConfiguration, filterCountRequest, expDataMap, commonModifierResponse, filterPillConfigurationWrapper
		);

		// Assert
		Assert.assertNotNull(filterResponse);
		assertEquals("INR", filterResponse.getCurrency());
		assertTrue(filterResponse.getFilterListV2().stream()
				.anyMatch(page -> "STAR_RATING".equalsIgnoreCase(page.getPageId())));
		// add more assertions
		Assert.assertEquals(filterResponse.getFilterListV2().get(0).getPageId(), "STAR_RATING");
		Assert.assertEquals(filterResponse.getFilterListV2().get(0).getTitle(), "Star Rating");
	}

	@Test
	public void testAddFilterPages_WithValidConfig() {
		// Arrange
//		when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("{filter_text}");

		FilterSearchMetaDataResponse response = new FilterSearchMetaDataResponse();
		response.setFilterDataMap(new HashMap<>());

		response.getFilterDataMap().put(FilterGroup.STAR_RATING, new ArrayList<>());
		// add filters in filterDataMap of response under STAR_RATING group

		Filter filter = new Filter();
		filter.setFilterValue("STAR_RATING");
		filter.setFilterGroup(FilterGroup.STAR_RATING);
		response.getFilterDataMap().get(FilterGroup.STAR_RATING).add(filter);

		response.setCurrency("INR");

		FilterConfigurationV2 filterConfiguration = new FilterConfigurationV2();
		filterConfiguration.setFilterPages(new LinkedHashMap<>());
		filterConfiguration.setRankOrder(new LinkedHashMap<>());

		filterConfiguration.getRankOrder().put("STAR_RATING", 1);

		com.mmt.hotels.clientgateway.businessobjects.FilterPage filterPage = new com.mmt.hotels.clientgateway.businessobjects.FilterPage();
		filterPage.setPage_id("STAR_RATING");
		filterPage.setTitle("Star Rating");

		//Mock filterConfigCategory object STAR_RATING
		com.mmt.hotels.clientgateway.businessobjects.FilterConfigCategory filterConfigCategory = new com.mmt.hotels.clientgateway.businessobjects.FilterConfigCategory();
		filterConfigCategory.setGroups(new LinkedHashMap<>());
		filterConfigCategory.getGroups().put("STAR_RATING", new LinkedHashMap<>());

		//mock filterConfigDetail object STAR_RATING
		// Mock filterConfigDetail object for STAR_RATING
		FilterConfigDetail filterConfigDetail = new FilterConfigDetail();
		filterConfigDetail.setTitle("5 Star");
		filterConfigDetail.setSubTitle("Luxury Hotels");
		filterConfigDetail.setDescription("Top-rated hotels with 5-star amenities");
		filterConfigDetail.setImageUrl("https://example.com/5star.png");
		filterConfigDetail.setInfoText("Exclusive 5-star properties");

// Add the mocked filterConfigDetail to the STAR_RATING group
		filterConfigCategory.getGroups().get("STAR_RATING").put("STAR_RATING", filterConfigDetail);


		filterPage.setFilters(new LinkedHashMap<>());
		filterPage.getFilters().put("STAR_RATING", filterConfigCategory);
		filterConfiguration.getFilterPages().put("STAR_RATING", filterPage);

		FilterCountRequest filterCountRequest = new FilterCountRequest();
		filterCountRequest.setSearchCriteria(new SearchHotelsCriteria());
		filterCountRequest.getSearchCriteria().setCheckIn("2023-11-05");
		filterCountRequest.getSearchCriteria().setCurrency("INR");

		LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
		expDataMap.put("HAFC", "T");

		CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
		commonModifierResponse.setExtendedUser(new ExtendedUser());
		commonModifierResponse.getExtendedUser().setProfileType("PARTNER");

		FilterCountRequest filterRequest = new FilterCountRequest();
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setFunnelSource("HOTELS");
		filterRequest.setRequestDetails(requestDetails);
		SearchHotelsCriteria sc = new SearchHotelsCriteria();
		sc.setLocationId("DEL");
		filterRequest.setSearchCriteria(sc);

		// Act
		List<FilterPage> result = filterResponseTransformer.addFilterPages(
				response, filterConfiguration, filterRequest, expDataMap, commonModifierResponse
		);

		// Assert
		Assert.assertNotNull(result);
		assertEquals(1, result.size());
		assertEquals("STAR_RATING", result.get(0).getPageId());
	}

	@Test
	public void testConvertFilterResponseShortStays() throws IOException, IllegalAccessException {
		FilterSearchMetaDataResponse filterResponseHES = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:shortstays/hesResponse.json")),
				FilterSearchMetaDataResponse.class);
		FilterConfiguration filterConfig = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:shortstays/filterConfig.json")),
				FilterConfiguration.class);
		FilterCountRequest filterRequest = mapper.readValue(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:shortstays/filterRequest.json")),
				FilterCountRequest.class);
		LinkedHashMap<String,String> expDataMap = new LinkedHashMap<>();
		expDataMap.put("HFC", "T");
		FilterPillConfigurationWrapper filterPillConfigurationWrapper = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:shortstays/filterPillConfiguration.json")),
				FilterPillConfigurationWrapper.class);
		FieldUtils.writeField(filterResponseTransformer, "minDynamicFilterPills", 3, true);
		FilterResponse response = filterResponseTransformer.convertFilterResponse(filterResponseHES, filterConfig,
				filterRequest, expDataMap, null, filterPillConfigurationWrapper);
		Assert.assertNotNull(response);
		Assert.assertNotNull(response.getFilterPills());
		Assert.assertFalse(response.getFilterPills().get(0).getType().isEmpty());
		FilterGroup drivingDurationHRFilterGroup = FilterGroup.getFilterGroupFromFilterName("DRIVING_DURATION_HR");
		List<FilterCategory> drivingDurationDurationFilterList =
				response.getFilterList().stream().filter(filterCategory -> filterCategory.getCategoryName().equalsIgnoreCase(drivingDurationHRFilterGroup.name())).collect(Collectors.toList());
		Assert.assertFalse(drivingDurationDurationFilterList.isEmpty());
	}

	@Test
	public void convertBatchFilterResponseTest() throws IOException {
		FilterSearchMetaDataResponse response = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/batchFilterResponse.json")),
				FilterSearchMetaDataResponse.class);
		when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Title");
		org.junit.Assert.assertNotNull(filterResponseTransformer.convertBatchFilterResponse(response));
	}

	@Test
	public void testGetPopularFilterCategoryForGCC() {
		// Arrange
		FilterResponse filterResponse = new FilterResponse();
		FilterCategory popularCategory = new FilterCategory();
		popularCategory.setCategoryName("POPULAR");
		List<FilterCategory> filterCategories = new ArrayList<>();
		filterCategories.add(popularCategory);
		filterResponse.setFilterList(filterCategories);

		// Act
		List<FilterCategory> result = filterResponseTransformer.getPopularFilterCategoryForGCC(filterResponse);

		// Assert
		Assert.assertNotNull(result);
	}

	@Test
	public void testGetPopularFilterCategoryForGCCTitle() {
		// Arrange
		FilterResponse filterResponse = new FilterResponse();
		FilterPage page = new FilterPage();
		page.setPageId(FILTER_POPULAR);
		FilterCategory popularCategory = new FilterCategory();
		popularCategory.setCategoryName(FILTER_POPULAR);
		List<FilterCategory> filterCategories = new ArrayList<>();
		page.setCategories(filterCategories);
		filterCategories.add(popularCategory);

		List<FilterPage> pages = new ArrayList<>();
		pages.add(page);
		filterResponse.setFilterListV2(pages);

		// Act
		List<FilterCategory> result = filterResponseTransformer.getPopularFilterCategoryForGCC(filterResponse);

		// Assert
		Assert.assertNull(result);
	}

	@Test
	public void appendFilterWithCountZeroAtLastTest(){
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filterList = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter filter = new com.mmt.hotels.clientgateway.response.filter.Filter();
		filter.setFilterGroup("test");
		filter.setCount(0);
		filterList.add(filter);
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "appendFilterWithCountZeroAtLast", filterList, true);
	}

	@Test
	public void createFilterTest() {
		FilterConfigDetail filterConfigDetail = new FilterConfigDetail();
		Filter filter = new Filter(FilterGroup.HOTEL_PRICE, 0, 100);
		filter.setCount(2);
		Assert.assertNotNull(filterResponseTransformer.createFilter(filterConfigDetail, filter, "CORP", "SPACE", null));
	}

	@Test
	public void createFilterTest_GCC() {
		FilterConfigDetail filterConfigDetail = new FilterConfigDetail();
		Filter filter = new Filter(FilterGroup.HOTEL_PRICE, 0, 100);
		filter.setCount(0);
		MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");
		Assert.assertNull(filterResponseTransformer.createFilter(filterConfigDetail, filter, "CORP", "SPACE", null));
		MDC.clear();
	}

	@Test
	public void createFilterTest_GCC_notNullCase() {
		FilterConfigDetail filterConfigDetail = new FilterConfigDetail();
		Filter filter = new Filter(FilterGroup.HOTEL_PRICE, 0, 100);
		filter.setCount(2);
		MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");
		List<com.mmt.hotels.clientgateway.request.Filter> filterCriteria = new ArrayList<>();
		com.mmt.hotels.clientgateway.request.Filter filterCG = new com.mmt.hotels.clientgateway.request.Filter();
		filterCG.setFilterValue("CANCELLATION_AVAIL");
		filterCG.setFilterGroup(com.mmt.hotels.clientgateway.response.filter.FilterGroup.FREE_CANCELLATION_AVAIL);
		filterCriteria.add(filterCG);
		Assert.assertNotNull(filterResponseTransformer.createFilter(filterConfigDetail, filter, "CORP", "SPACE", filterCriteria));
		MDC.clear();
	}

	@Test
	public void createFilterSubGroupTest() {
		List<FilterGroup> filterHESSubGroupList = new ArrayList<>();
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		FilterConfiguration filterConfig = new FilterConfiguration();
		filterHESSubGroupList.add(FilterGroup.AMENITIES);
		filterResponseHES.setFilterDataMap(new HashMap<>());
		filterResponseHES.getFilterDataMap().put(FilterGroup.AMENITIES, new ArrayList<Filter>());
		filterResponseHES.getFilterDataMap().get(FilterGroup.AMENITIES).add(new Filter(FilterGroup.AMENITIES, "Wifi"));
		filterConfig.setFilters(new LinkedHashMap<>());
		filterConfig.getFilters().put("AMENITIES", new FilterConfigCategory());
		filterConfig.getFilters().get("AMENITIES").setGroups(new LinkedHashMap<>());
		filterConfig.getFilters().get("AMENITIES").getGroups().put("AMENITIES", new LinkedHashMap<>());
		Assert.assertNotNull(filterResponseTransformer.createFilterSubGroup(filterHESSubGroupList, filterResponseHES, filterConfig.getFilters(), "CORP", "SPACE", null));
	}

	@Test
	public void testUpdateCategoryTitle_Success() throws IOException {
		FilterSearchMetaDataResponse filterResponseHES = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:shortstays/hesResponse.json")),
				FilterSearchMetaDataResponse.class);
		FilterConfiguration filterConfig = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:shortstays/filterConfig.json")),
				FilterConfiguration.class);
		FilterCountRequest filterRequest = mapper.readValue(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:shortstays/filterRequest.json")),
				FilterCountRequest.class);
		LinkedHashMap<String,String> expDataMap = new LinkedHashMap<>();

		// Act
		String title = filterResponseTransformer.updateCategoryTitle(FILTER_PREVIOUSLY_USED, filterConfig.getFilters().get(TOGGLE_FILTERS_CATEGORY), filterRequest, filterResponseHES, expDataMap, false);

		// Assert
		Assert.assertNull(title);
	}

	@Test
	public void testGetPopularFilterCategoryForGCC_FilterListNotEmpty() {
		// Arrange
		FilterResponse filterResponse = new FilterResponse();
		List<FilterCategory> filterList = new ArrayList<>();

		FilterCategory popularCategory = new FilterCategory();
		popularCategory.setCategoryName("DEALS_CATEGORY");

		filterList.add(popularCategory);

		filterResponse.setFilterList(filterList);

		// Act
		List<FilterCategory> result = filterResponseTransformer.getPopularFilterCategoryForGCC(filterResponse);

		// Assert
		Assert.assertNotNull(result);
		assertEquals(1, result.size());
		assertEquals("DEALS_CATEGORY", result.get(0).getCategoryName());
	}

	@Test
	public void testGetPopularFilterCategoryForGCC_FilterListV2ValidPage() {
		// Arrange
		FilterResponse filterResponse = new FilterResponse();
		List<FilterPage> filterListV2 = new ArrayList<>();
		FilterPage page = new FilterPage();
		page.setPageId(FILTER_DEALS);

		FilterCategory popularCategory = new FilterCategory();
		popularCategory.setCategoryName("DEALS_CATEGORY");

		List<FilterCategory> categories = new ArrayList<>();
		categories.add(popularCategory);
		page.setCategories(categories);
		filterListV2.add(page);
		filterResponse.setFilterListV2(filterListV2);

		// Act
		List<FilterCategory> result = filterResponseTransformer.getPopularFilterCategoryForGCC(filterResponse);

		// Assert
		Assert.assertNotNull(result);
		assertEquals(1, result.size());
		assertEquals("DEALS_CATEGORY", result.get(0).getCategoryName());
	}

	@Test
	public void testGetPopularFilterCategoryForGCC_FilterListV2NoPageFound() {
		// Arrange
		FilterResponse filterResponse = new FilterResponse();
		List<FilterPage> filterListV2 = new ArrayList<>();
		FilterPage page = new FilterPage();
		page.setPageId("OTHER_PAGE");
		filterListV2.add(page);
		filterResponse.setFilterListV2(filterListV2);

		// Act
		List<FilterCategory> result = filterResponseTransformer.getPopularFilterCategoryForGCC(filterResponse);

		// Assert
		assertNull(result);
	}

	@Test
	public void testGetPopularFilterCategoryForGCC_FilterListV2PageNoCategories() {
		// Arrange
		FilterResponse filterResponse = new FilterResponse();
		List<FilterPage> filterListV2 = new ArrayList<>();
		FilterPage page = new FilterPage();
		page.setPageId(FILTER_DEALS);
		page.setCategories(null);
		filterListV2.add(page);
		filterResponse.setFilterListV2(filterListV2);

		// Act
		List<FilterCategory> result = filterResponseTransformer.getPopularFilterCategoryForGCC(filterResponse);

		// Assert
		assertNull(result);
	}

	@Test
	public void testGetPopularFilterCategoryForGCC_BothFilterListsNull() {
		// Arrange
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(null);
		filterResponse.setFilterListV2(null);

		// Act
		List<FilterCategory> result = filterResponseTransformer.getPopularFilterCategoryForGCC(filterResponse);

		// Assert
		assertNull(result);
	}

	@Test
	public void getMaxBudgetPriceForCityTest(){
		FilterCountRequest filterCountRequest = new FilterCountRequest();
		SearchHotelsCriteria searchHotelsCriteria = new SearchHotelsCriteria();
		searchHotelsCriteria.setCityCode("CTDEL");
		filterCountRequest.setSearchCriteria(searchHotelsCriteria);
		Map<Integer, Set<String>> budgetHotelCityConfig = new HashMap<>();
		Set<String> set = new HashSet<>();
		set.add("CTDEL");
		budgetHotelCityConfig.put(3000, set);
		ReflectionTestUtils.setField(filterResponseTransformer,"budgetHotelCityConfig" , budgetHotelCityConfig);
		int budget = filterResponseTransformer.getMaxBudgetPriceForCity(filterCountRequest);
		Assert.assertEquals(budget, 3000);
	}

	@Test
	public void buildPricingOptionTest(){

		MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");

		FilterCountRequest filterCountRequest = new FilterCountRequest();
		filterCountRequest.setSearchCriteria(new SearchHotelsCriteria());
		filterCountRequest.getSearchCriteria().setCheckIn(LocalDate.now().toString());
		filterCountRequest.getSearchCriteria().setCheckOut(LocalDate.now().plusDays(2).toString());

		FilterPricingOption pricingOption = new FilterPricingOption();
		pricingOption.setHeader("PRICING_OPTION_HEADER");

		List<PricingOptionFilter> filterList = new ArrayList<PricingOptionFilter>();
		PricingOptionFilter filter1 = new PricingOptionFilter();
		filter1.setTitle("PRICING_OPTION_FILTER_TITLE_PN");
		filter1.setExpValue("PN");
		filterList.add(filter1);
		PricingOptionFilter filter2 = new PricingOptionFilter();
		filter2.setTitle("PRICING_OPTION_FILTER_TITLE_TP");
		filter2.setExpValue("TP");
		filterList.add(filter2);

		pricingOption.setFilters(filterList);

		when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("abcd");
		when(dateUtil.getDaysDiff(Mockito.any(LocalDate.class),Mockito.any(LocalDate.class))).thenReturn(2);
		Assert.assertNotNull(filterResponseTransformer.buildPricingOption(filterCountRequest,pricingOption));
	}

	@Test
	public void getCategoryNamesFromFilterResponseTest(){
		FilterCategory filterCategory = new FilterCategory();
		List<FilterCategory> filterCategories = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter f = new com.mmt.hotels.clientgateway.response.filter.Filter();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(f);
		filterCategory.setFilters(filters);
		filterCategory.setCategoryName("test");
		filterCategories.add(filterCategory);
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(filterCategories);

		ReflectionTestUtils.invokeMethod(filterResponseTransformer,"getCategoryNamesFromFilterResponse",filterResponse);
	}

	@Test
	public void translateSortPillDataTest(){
		SortList sortList = new SortList();
		sortList.setTitle("KEY");
		SortCriteria sortCriteria = new SortCriteria();
		sortCriteria.setTitle("TITLE_KEY");
		sortCriteria.setPillText("PILL_TEXT_KEY");
		List<SortCriteria> sortCriteriaList = new ArrayList<>();
		sortCriteriaList.add(sortCriteria);
		sortList.setSortCriteria(sortCriteriaList);


		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "translateSortPillData", sortList, "");

	}

	@Test
	public void buildFilterPillsTest(){
		FilterCategory filterCategory = new FilterCategory();
		List<FilterCategory> filterCategories = new ArrayList<>();
		FilterCountRequest filterRequest = new FilterCountRequest();
		RequestDetails requestDetails = new RequestDetails();
		filterRequest.setRequestDetails(requestDetails);
		com.mmt.hotels.clientgateway.response.filter.Filter f = new com.mmt.hotels.clientgateway.response.filter.Filter();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(f);
		filterCategory.setFilters(filters);
		filterCategory.setCategoryName("POPULAR");
		filterCategories.add(filterCategory);
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(filterCategories);
		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		LinkedHashSet<String> primaryFilterCategories = new LinkedHashSet<>();
		primaryFilterCategories.add("POPULAR");
		contextualFilterResponse.setPrimaryFilterCategories(primaryFilterCategories);
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);
		Map<String, String> expData = new LinkedHashMap<>();
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterPills", filterResponse, filterResponseHES, filterPillConfig, expData, "HOTELS",filterRequest, true);
		Assert.assertTrue(filterResponse.getFilterList().stream().anyMatch(fc -> "POPULAR".equalsIgnoreCase(fc.getCategoryName())));
	}

	@Test
	public void buildFilterPillsDefaultTest(){
		FilterCategory filterCategory = new FilterCategory();
		List<FilterCategory> filterCategories = new ArrayList<>();
		FilterCountRequest filterRequest = new FilterCountRequest();
		RequestDetails requestDetails = new RequestDetails();
		filterRequest.setRequestDetails(requestDetails);
		com.mmt.hotels.clientgateway.response.filter.Filter f = new com.mmt.hotels.clientgateway.response.filter.Filter();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(f);
		filterCategory.setFilters(filters);
		filterCategory.setCategoryName("POPULAR");
		filterCategories.add(filterCategory);
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(filterCategories);
		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		LinkedHashSet<String> primaryFilterCategories = new LinkedHashSet<>();
		primaryFilterCategories.add("PROPERTY_TYPE");
		contextualFilterResponse.setPrimaryFilterCategories(primaryFilterCategories);
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);
		Map<String, String> expData = new LinkedHashMap<>();
		expData.put(HTL_PILLS_ENABLED_EXP, "1");
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterPills", filterResponse, filterResponseHES, filterPillConfig, expData, "SHORTSTAYS",filterRequest, false);
		Assert.assertFalse(filterResponse.getFilterList().stream().anyMatch(fc -> "PROPERTY_TYPE".equalsIgnoreCase(fc.getCategoryName())));
	}

	@Test
	public void updateFilterListWithDptResponseTest() {
		FilterCategory filterCategory = new FilterCategory();
		List<FilterCategory> filterCategories = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter f = new com.mmt.hotels.clientgateway.response.filter.Filter();
		f.setCount(34);
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(f);
		filterCategory.setFilters(filters);
		filterCategory.setCategoryName("POPULAR");
		filterCategories.add(filterCategory);
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(filterCategories);
		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		LinkedHashSet<String> primaryFilterCategories = new LinkedHashSet<>();
		primaryFilterCategories.add("POPULAR");
		primaryFilterCategories.add("PREVIOUSLY_USED");
		contextualFilterResponse.setFilterCategories(primaryFilterCategories);
		LinkedHashMap<String, List<Filter>> primaryFilterValueMap = new LinkedHashMap<>();

		Filter resort = new Filter(FilterGroup.PROPERTY_TYPE, "Resort");
		resort.setCount(406);
		primaryFilterValueMap.put("PREVIOUSLY_USED", Collections.singletonList(resort));

		Filter starRatingFilter = new Filter(FilterGroup.STAR_RATING, "5");
		resort.setCount(34);
		primaryFilterValueMap.put("POPULAR", Collections.singletonList(starRatingFilter));

		contextualFilterResponse.setPrimaryFilterValueMap(primaryFilterValueMap);
		contextualFilterResponse.setCurrentCohortId("currentCohortId");
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();

		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);
		HashMap<FilterGroup, List<Filter>> filterDataMap = new HashMap<>();
		filterDataMap.put(FilterGroup.PROPERTY_TYPE, Collections.singletonList(resort));
		filterDataMap.put(FilterGroup.STAR_RATING, Collections.singletonList(starRatingFilter));
		filterResponseHES.setFilterDataMap(filterDataMap);

		FilterCountRequest filterRequest = new FilterCountRequest();
		LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "updateFilterListWithDptResponse", filterResponse, filterResponseHES, filterRequest, expDataMap);
		Assert.assertTrue(filterResponse.getFilterList().stream().anyMatch(fc -> "POPULAR".equalsIgnoreCase(fc.getCategoryName())));
	}

	@Test
	public void populatePreviouslyUsedFiltersTest() {
		FilterCategory filterCategory = new FilterCategory();
		List<FilterCategory> filterCategories = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter f = new com.mmt.hotels.clientgateway.response.filter.Filter();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(f);
		filterCategory.setFilters(filters);
		filterCategory.setCategoryName("PREVIOUSLY_USED");
		filterCategories.add(filterCategory);
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(filterCategories);
		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		LinkedHashSet<String> primaryFilterCategories = new LinkedHashSet<>();
		primaryFilterCategories.add("POPULAR");
		primaryFilterCategories.add("PREVIOUSLY_USED");
		contextualFilterResponse.setFilterCategories(primaryFilterCategories);
		LinkedHashMap<String, List<Filter>> primaryFilterValueMap = new LinkedHashMap<>();
		primaryFilterValueMap.put("PREVIOUSLY_USED", Collections.singletonList(new Filter()));
		primaryFilterValueMap.put("POPULAR", Collections.singletonList(new Filter()));
		contextualFilterResponse.setPrimaryFilterValueMap(primaryFilterValueMap);
		contextualFilterResponse.setCurrentCohortId("currentCohortId");
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);
		FilterCountRequest filterRequest = new FilterCountRequest();
		LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
		when(polyglotService.getTranslatedData("PREVIOUSLY_USED_FILTER_TITLE")).thenReturn("PREVIOUSLY_USED");
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "populatePreviouslyUsedFilters", filterResponse, filterResponseHES, filterRequest, expDataMap);
		Assert.assertTrue(filterResponse.getFilterList().stream().anyMatch(fc -> "PREVIOUSLY_USED".equalsIgnoreCase(fc.getCategoryName())));
	}

	@Test
	public void modifyShowImageUrlMergePropertyTypeTest() {
		List<FilterCategory> filterCategories = new ArrayList<>();
		FilterCategory filterCategory = new FilterCategory();
		com.mmt.hotels.clientgateway.response.filter.Filter f = new com.mmt.hotels.clientgateway.response.filter.Filter();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(f);
		filterCategory.setFilters(filters);
		filterCategory.setCategoryName(MERGE_PROPERTY_TYPE);
		filterCategories.add(filterCategory);
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "modifyShowImageUrlMergePropertyType", filterCategories);
		Assert.assertFalse(filterCategories.stream().anyMatch(FilterCategory::isShowImageUrl));
	}

	@Test
	public void buildFilterCtaTextTest() {

		when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Show {filtered_hotel_count} hotels");
		FilterCountRequest filterCountRequest = new FilterCountRequest();
		SearchHotelsCriteria sc = new SearchHotelsCriteria();
		sc.setLocationId("DEL");
		filterCountRequest.setSearchCriteria(sc);
		filterCountRequest.setFilterCriteria(new ArrayList<>());
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterCtaText", 10, filterCountRequest, null);
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterCtaText", 0, null, null);
	}

	@Test
	public void updateMinItemsToShowTest() {
		FilterCategory filterCategory = new FilterCategory();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		for (int i = 0; i < 3; i++) {
			filters.add(new com.mmt.hotels.clientgateway.response.filter.Filter());
		}
		filterCategory.setFilters(filters);
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "updateMinItemsToShowAndShowMore", filterCategory);
		for (int i = 0; i < 5; i++) {
			filters.add(new com.mmt.hotels.clientgateway.response.filter.Filter());
		}
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "updateMinItemsToShowAndShowMore", filterCategory);


	}

	@Test
	public void testModifyFilterViewType() {

		Map<String, String> viewMap = new HashMap<>();
		viewMap.put("STAR_CATEGORY", "flex");
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(new ArrayList<>());
		filterResponse.getFilterList().add(new FilterCategory());
		filterResponse.getFilterList().get(0).setCategoryName("STAR_CATEGORY");
		filterResponse.getFilterList().get(0).setViewType("tile");

		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "modifyFilterCategoryViewType", viewMap, filterResponse);


	}

	@Test
	public void removeFilterCategoriesFromResponseTest() {
		List<String> categoriesToRemove = new ArrayList<>();
		FilterResponse filterResponse = new FilterResponse();
		FilterCategory fc1 = new FilterCategory();
		fc1.setCategoryName("test1");
		FilterCategory fc2 = new FilterCategory();
		fc2.setCategoryName("test2");
		categoriesToRemove.add("test1");
		filterResponse.setFilterList(new ArrayList<>(Arrays.asList(fc1, fc2)));
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "removeFilterCategoriesFromResponse", categoriesToRemove, filterResponse);
		Assert.assertFalse(filterResponse.getFilterList().stream().anyMatch(fc -> "test1".equalsIgnoreCase(fc.getCategoryName())));
	}

	@Test
	public void modifyShowImageUrlMergePropertyTest() {

		List<FilterCategory> filterCategories = new ArrayList<>();
		FilterCategory filterCategory = new FilterCategory();
		filterCategory.setShowImageUrl(false);

		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "modifyShowImageUrlMergePropertyType", filterCategories);
	}

	@Test
	public void sortFilterPillsBasisSequenceTest() {
		FilterPill fp1 = new FilterPill();
		fp1.setId("test1");
		fp1.setSequence(4);
		FilterPill fp2 = new FilterPill();
		fp2.setId("test2");
		fp2.setSequence(1);

		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "sortFilterPillsBasisSequence", Arrays.asList(fp1, fp2));
	}

	@Test
	public void getFilterPillOrderedMapTest(){
		FilterPill filterPill = new FilterPill();
		filterPill.setId("test");
		LinkedHashSet<String> filterCats = new LinkedHashSet<>();
		filterCats.add("test");
		Map<String, FilterPill> filterPillMap = new HashMap<>();
		filterPillMap.put("test", filterPill);

		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "getFilterPillOrderedMap", filterCats, filterPillMap);

	}

	@Test
	public void testGetCategoryNamesFromFilterResponseV2() {
		// Arrange
		FilterResponse filterResponse = new FilterResponse();
		List<FilterCategory> filterCategories = new ArrayList<>();

		FilterCategory category1 = new FilterCategory();
		category1.setCategoryName("CATEGORY_1");
		filterCategories.add(category1);

		FilterCategory category2 = new FilterCategory();
		category2.setCategoryName("CATEGORY_2");
		filterCategories.add(category2);

		filterResponse.setFilterList(filterCategories);

		// Act
		List<String> result = ReflectionTestUtils.invokeMethod(
				filterResponseTransformer,
				"getCategoryNamesFromFilterResponseV2",
				filterResponse
		);

		// Assert
		Assert.assertNotNull(result);
	}

	@Test
	public void populateFilterPillsFromConfigTest(){
		FilterPill filterPill = new FilterPill();
		filterPill.setId("test");
		List<FilterPill> filterPillList = new ArrayList<>();
		List<String> filterCats = Arrays.asList("test");
		Map<String, FilterPill> filterPillMap = new HashMap<>();
		filterPillMap.put("test", filterPill);
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "populateFilterPillsFromConfig", filterPillList, filterCats, filterPillMap, new FilterSearchMetaDataResponse());
	}

	@Test
	public void appendContextualFiltersForMyPartnerTest(){
		List< com.mmt.hotels.clientgateway.response.filter.Filter> filterList = dummyFilterList();
		FilterSearchMetaDataResponse filterResponseHES = dummyHESResponse();
		Map<String, String> expDataMap = dummyExpData();
		
		// Setup FilterCountRequest with multiple adults to avoid unmarried couples filter suppression
		FilterCountRequest filterCountRequest = new FilterCountRequest();
		RequestDetails requestDetails = new RequestDetails();
		filterCountRequest.setRequestDetails(requestDetails);
		
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
		RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
		roomStayCandidate.setAdultCount(2); // Multiple adults to ensure filters are not suppressed
		roomStayCandidates.add(roomStayCandidate);
		searchCriteria.setRoomStayCandidates(roomStayCandidates);
		filterCountRequest.setSearchCriteria(searchCriteria);
		
		List<com.mmt.hotels.clientgateway.response.filter.Filter> inclusionListConfig = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter filter1 = new com.mmt.hotels.clientgateway.response.filter.Filter();
		filter1.setFilterGroup(FilterGroup.HOTEL_CATEGORY.name());
		filter1.setFilterValue("Free Airport transfers");
		inclusionListConfig.add(filter1);
		ReflectionTestUtils.setField(filterResponseTransformer, "mpContextualFiltersApplicable", inclusionListConfig);
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "appendContextualFiltersForMyPartner", filterList, expDataMap, filterResponseHES, "POPULAR", filterCountRequest);
		Assert.assertEquals(3, filterList.size());
	}

	@Test
	public void isRemovePriceBucketFilterTest() {
		// Arrange
		FilterCountRequest filterCountRequest = new FilterCountRequest();
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setCountryCode("US");
		filterCountRequest.setSearchCriteria(searchCriteria);
		filterCountRequest.setFeatureFlags(new FeatureFlags());
		filterCountRequest.getFeatureFlags().setOriginListingMap(true);

		// Act
		boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(
                filterResponseTransformer,
                "isRemovePriceBucketFilter",
                filterCountRequest,
                "B2C"
        ));

		// Assert
		Assert.assertTrue(result);
	}

	@Test
	public void isHotelCloudForCorpTest() {
		// Arrange
		com.mmt.hotels.filter.Filter filter = new com.mmt.hotels.filter.Filter();
		filter.setFilterGroup(FilterGroup.HOTELCLOUD);

		// Act
		boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(
                filterResponseTransformer,
                "isHotelCloudForCorp",
                filter,
                "CORP"
        ));

		// Assert
		Assert.assertTrue(result);
	}

	@Test
	public void showSupplierFiltersV2_Test() {
		// Arrange
		FilterSearchMetaDataResponse filterSearchMetaDataResponse = new FilterSearchMetaDataResponse();
		Map<FilterGroup, List<com.mmt.hotels.filter.Filter>> filterDataMap = new HashMap<>();
		List<com.mmt.hotels.filter.Filter> dealsFilters = new ArrayList<>();
		com.mmt.hotels.filter.Filter filter = new com.mmt.hotels.filter.Filter();
		filter.setFilterValue("LAST_MINUTE");
		filter.setCount(0);
		dealsFilters.add(filter);
		filterDataMap.put(FilterGroup.DEALS, dealsFilters);
		filterSearchMetaDataResponse.setFilterDataMap(filterDataMap);

		// Act
		Tuple<Boolean, Boolean> result = ReflectionTestUtils.invokeMethod(
				filterResponseTransformer,
				"showSupplierFilters",
				"2023-11-15",
				filterSearchMetaDataResponse,
				false
		);

		// Assert
		Assert.assertNotNull(result);
		Assert.assertFalse(result.getX());
		Assert.assertFalse(result.getY());
	}

	@Test
	public void sortChainInfoByNameTest() {
		// Arrange
		FilterResponse filterResponse = new FilterResponse();
		FilterCategory filterCategory = new FilterCategory();
		filterCategory.setCategoryName("BRAND_FILTER");
		com.mmt.hotels.clientgateway.response.filter.Filter filter1 = new com.mmt.hotels.clientgateway.response.filter.Filter();
		filter1.setTitle("Z");
		com.mmt.hotels.clientgateway.response.filter.Filter filter2 = new com.mmt.hotels.clientgateway.response.filter.Filter();
		filter2.setTitle("A");
		filterCategory.setFilters(Arrays.asList(filter1, filter2));
		filterResponse.setFilterList(Collections.singletonList(filterCategory));

		// Act
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "sortChainInfoByName", filterResponse);

		// Assert
		Assert.assertEquals("A", filterResponse.getFilterList().get(0).getFilters().get(0).getTitle());
		Assert.assertEquals("Z", filterResponse.getFilterList().get(0).getFilters().get(1).getTitle());
	}

	@Test
	public void setTitleForGccBnplFilterTest() {
		// Arrange
		FilterConfigCategory filterConfigCategory = new FilterConfigCategory();
		LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>> groups = new LinkedHashMap<>();
		LinkedHashMap<String, FilterConfigDetail> filterConfigDetailMap = new LinkedHashMap<>();
		FilterConfigDetail filterConfigDetail = new FilterConfigDetail();
		filterConfigDetailMap.put("PAY_LATER:20", filterConfigDetail);
		groups.put(FilterGroup.PAY_LATER.name(), filterConfigDetailMap);
		filterConfigCategory.setGroups(groups);

		when(polyglotService.getTranslatedData(ConstantsTranslation.GCC_BNPL_FILTER_TITLE))
				.thenReturn("Book @0 AED");

		// Act
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "setTitleForGccBnplFilter", filterConfigCategory);

		// Assert
		Assert.assertEquals("Book @0 AED", filterConfigDetail.getTitle());
	}

	@Test
	public void isRemoveBnplFilterTest() throws ParseException {
		FilterCountRequest filterRequest = new FilterCountRequest();
		filterRequest.setExpDataMap(new HashMap<>());
		filterRequest.getExpDataMap().put("BNPL","T");
		filterRequest.setSearchCriteria(new SearchHotelsCriteria());
		filterRequest.getSearchCriteria().setCheckIn("2030-11-05");
		when(dateUtil.getDaysDiff(Mockito.any(LocalDate.class), Mockito.any(LocalDate.class))).thenReturn(2);

		boolean result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "isRemoveBNPLFilter", filterRequest);

		when(dateUtil.getDaysDiff(Mockito.any(LocalDate.class), Mockito.any(LocalDate.class))).thenReturn(1);
		Assert.assertFalse(result);

		result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "isRemoveBNPLFilter", filterRequest);
		Assert.assertTrue(result);

		filterRequest.getExpDataMap().put("isPeakDate","true");
		filterRequest.getExpDataMap().put("bnplPeakDays","7");
		when(dateUtil.getDaysDiff(Mockito.any(LocalDate.class), Mockito.any(LocalDate.class))).thenReturn(10);
		result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "isRemoveBNPLFilter", filterRequest);
		Assert.assertFalse(result);

		when(dateUtil.getDaysDiff(Mockito.any(LocalDate.class), Mockito.any(LocalDate.class))).thenReturn(6);
		result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "isRemoveBNPLFilter", filterRequest);
		Assert.assertTrue(result);

	}

	@Test
	public void populateCategoriesFromDPTTest() {

		FilterCategory filterCategory = new FilterCategory();
		List<FilterCategory> filterCategories = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter f = new com.mmt.hotels.clientgateway.response.filter.Filter();

		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter filter1 = new com.mmt.hotels.clientgateway.response.filter.Filter();
		filter1.setFilterGroup(FilterGroup.STAR_RATING.name());
		filter1.setFilterValue("5");
		filter1.setTitle("5 star");

		com.mmt.hotels.clientgateway.response.filter.Filter filter2 = new com.mmt.hotels.clientgateway.response.filter.Filter();
		filter2.setFilterGroup(FilterGroup.FREE_BREAKFAST.name());
		filter2.setFilterValue("free breakfast");
		filter2.setTitle("breakfast included");


		filters.add(filter1);
		filters.add(filter2);
		filterCategory.setFilters(filters);
		filterCategory.setCategoryName("POPULAR");
		filterCategories.add(filterCategory);
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(filterCategories);
		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		LinkedHashMap<String, List<Filter>> primaryFilterValueMap = new LinkedHashMap<>();
		primaryFilterValueMap.put("PREVIOUSLY_USED", Collections.singletonList(new Filter()));
		primaryFilterValueMap.put("POPULAR", new ArrayList<>());

		Filter f1 = new Filter();
		f1.setFilterGroup(FilterGroup.STAR_RATING);
		f1.setFilterValue("5");
		f1.setTitle("5 star");
		f1.setCount(10);

		Filter f2 = new Filter();
		f2.setFilterGroup(FilterGroup.FREE_BREAKFAST);
		f2.setFilterValue("Free Breakfast");
		f2.setTitle("free breakfast");
		f2.setCount(20);

		primaryFilterValueMap.get("POPULAR").add(f1);
		primaryFilterValueMap.get("POPULAR").add(f2);

		contextualFilterResponse.setPrimaryFilterValueMap(primaryFilterValueMap);
		contextualFilterResponse.setCurrentCohortId("currentCohortId");
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setFilterDataMap(new HashMap<>());

		List<Filter> filterListA = new ArrayList<>();
		filterListA.add(f1);

		List<Filter> filterListB = new ArrayList<>();
		filterListB.add(f2);



		filterResponseHES.getFilterDataMap().put(FilterGroup.STAR_RATING, filterListA);
		filterResponseHES.getFilterDataMap().put(FilterGroup.FREE_BREAKFAST, filterListB);
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);
		FilterCountRequest filterRequest = new FilterCountRequest();
		LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
		when(polyglotService.getTranslatedData("FILTER_POPULAR_TITLE_IN")).thenReturn("Suggested for you");
		Assert.assertEquals(Boolean.TRUE, ReflectionTestUtils.invokeMethod(filterResponseTransformer, "populateCategoryFromDPTFilters", filterResponse, filterResponseHES, filterRequest, expDataMap, "POPULAR", false));
		Assert.assertEquals(Boolean.TRUE, ReflectionTestUtils.invokeMethod(filterResponseTransformer, "populateCategoryFromDPTFilters", filterResponse, filterResponseHES, filterRequest, expDataMap, "POPULAR", true));
		Assert.assertTrue(filterResponse.getFilterList().stream().anyMatch(fc -> "POPULAR".equalsIgnoreCase(fc.getCategoryName())));
		FilterCategory filterCategory1 = filterResponse.getFilterList().get(0);
		Assert.assertNotNull(filterCategory1.getFilters());
		Assert.assertEquals(filterCategory1.getFilters().size(), 2);

	}

	@Test
	public void showSupplierFiltersTest() {
		// Create a FilterSearchMetaDataResponse with filter data
		FilterSearchMetaDataResponse filterSearchMetaDataResponse = new FilterSearchMetaDataResponse();

		Map<FilterGroup, List<com.mmt.hotels.filter.Filter>> filterDataMap = new HashMap<>();
		List<Filter> dealsFilters = new ArrayList<>();
		Filter filter1 = new Filter();
		filter1.setFilterGroup(FilterGroup.DEALS);
		filter1.setFilterValue("test Filter value");
		dealsFilters.add(filter1);
		filterDataMap.put(FilterGroup.DEALS, dealsFilters);
		filterSearchMetaDataResponse.setFilterDataMap(filterDataMap);

		// Set the check-in date
		String checkin = "2023-11-15";

		// Mock your dateUtil to control the difference
		Mockito.when(dateUtil.getDaysDiff((LocalDate) Mockito.any(), Mockito.any())).thenReturn(7);
		Tuple<Boolean,Boolean> response = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "showSupplierFilters", checkin, filterSearchMetaDataResponse, false);
		Assert.assertNotNull(response);
	}

	private List<com.mmt.hotels.clientgateway.response.filter.Filter> dummyFilterList(){
		com.mmt.hotels.clientgateway.response.filter.Filter filter1 = new com.mmt.hotels.clientgateway.response.filter.Filter();
		filter1.setFilterGroup(FilterGroup.STAR_RATING.name());
		filter1.setFilterValue("5");
		com.mmt.hotels.clientgateway.response.filter.Filter filter2 = new com.mmt.hotels.clientgateway.response.filter.Filter();
		filter2.setFilterGroup(FilterGroup.HOTEL_CATEGORY.name());
		filter2.setFilterValue("Best deals");
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filterList = new ArrayList<>();
		filterList.add(filter1);
		filterList.add(filter2);
		return filterList;
	}

	private FilterSearchMetaDataResponse dummyHESResponse(){
		FilterSearchMetaDataResponse filterHESResponse = new FilterSearchMetaDataResponse();
		Map<String, List<Filter>> filterCategoryMap = new HashMap<>();
		List<Filter> filters = new ArrayList<>();
		filters.add(new Filter(FilterGroup.HOTEL_CATEGORY,"Free Airport transfers"));
		filterCategoryMap.put("POPULAR", filters);
		filterHESResponse.setFilterCategoryMap(filterCategoryMap);
		return filterHESResponse;
	}

	private Map<String, String> dummyExpData(){
		Map<String, String> expData = new HashMap<>();
		expData.put(EXP_CONTEXTUAL_FILTER, EXP_CONTEXTUAL_FILTER_MYPARTNER_VALUE);
		return expData;
	}
	@Test
	public void testConvertFilterResponse() throws IOException, IllegalAccessException {
		FilterSearchMetaDataResponse filterResponseHES = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:shortstays/hesResponse.json")),
				FilterSearchMetaDataResponse.class);
		FilterConfiguration filterConfig = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:shortstays/filterConfig.json")),
				FilterConfiguration.class);
		FilterCountRequest filterRequest = mapper.readValue(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:shortstays/filterRequest.json")),
				FilterCountRequest.class);
		LinkedHashMap<String,String> expDataMap = new LinkedHashMap<>();
		expDataMap.put("HFC", "T");
		expDataMap.put("htlPillsEnabled","1");
		FilterPillConfigurationWrapper filterPillConfigurationWrapper = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:shortstays/filterPillConfiguration.json")),
				FilterPillConfigurationWrapper.class);
		FieldUtils.writeField(filterResponseTransformer, "minDynamicFilterPills", 3, true);

		filterPillConfigurationWrapper.setFilterPillConfig(filterPillConfig);
		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		LinkedHashSet<String> primaryFilterCategories = new LinkedHashSet<>();
		primaryFilterCategories.add("POPULAR");
		contextualFilterResponse.setPrimaryFilterCategories(primaryFilterCategories);
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);

		FilterResponse response = filterResponseTransformer.convertFilterResponse(filterResponseHES, filterConfig,
				filterRequest, expDataMap, null, filterPillConfigurationWrapper);

		Assert.assertNotNull(response);
		Assert.assertNotNull(response.getFilterPills());
	}

	@Test
	public void setTitleForGccBnplFilterIsSuccess(){
		FilterConfigCategory filterConfigCategory = new FilterConfigCategory();
		LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>> groups = new LinkedHashMap<>();
		LinkedHashMap<String, FilterConfigDetail> filterConfigDetailMap = new LinkedHashMap<>();
		filterConfigDetailMap.put("PAY_LATER:20", new FilterConfigDetail());
		groups.put(FilterGroup.PAY_LATER.name(), filterConfigDetailMap);
		filterConfigCategory.setGroups(groups);

		when(polyglotService.getTranslatedData(ConstantsTranslation.GCC_BNPL_FILTER_TITLE)).thenReturn("Book @0 AED");
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "setTitleForGccBnplFilter", filterConfigCategory);

		Assert.assertEquals("Book @0 AED", filterConfigCategory.getGroups().get(FilterGroup.PAY_LATER.name()).get("PAY_LATER:20").getTitle());
	}
	@Test
	public void setTitleForGccBnplFilter_doesNotSetTitleWhenGroupsIsNull() {
		// Given
		FilterConfigCategory filterConfigCategory = new FilterConfigCategory();

		// When
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "setTitleForGccBnplFilter", filterConfigCategory);

		// Then
		Assert.assertNull(filterConfigCategory.getGroups());
	}

	@Test
	public void getDriveWalkProximityFilterTest() {
		List<com.mmt.hotels.filter.Filter> filterList = new ArrayList<>();
		com.mmt.hotels.filter.Filter filter = new com.mmt.hotels.filter.Filter();
		filter.setFilterValue("DISTANCE_POIBURJ#dd_0-3000");
		filter.setTitle("Filter Title");
		filter.setCount(10);
		filterList.add(filter);

		filter = new com.mmt.hotels.filter.Filter();
		filter.setFilterValue("DISTANCE_POIBURJ#wd_0-3000");
		filter.setTitle("Filter Title");
		filter.setCount(10);
		filterList.add(filter);

		filter = new com.mmt.hotels.filter.Filter();
		filter.setFilterValue("DISTANCE_POIBURJ#dd_0-5000");
		filter.setTitle("Filter Title");
		filter.setCount(10);
		filterList.add(filter);


		filter = new com.mmt.hotels.filter.Filter();
		filter.setFilterValue("DISTANCE_POIBURJ#wd_0-3");
		filter.setTitle("Filter Title");
		filter.setCount(10);
		filterList.add(filter);

		filter = new com.mmt.hotels.filter.Filter();
		filter.setFilterValue("DISTANCE_POIBURJ#wd_0");
		filter.setTitle("Filter Title");
		filter.setCount(10);
		filterList.add(filter);

		filter = new com.mmt.hotels.filter.Filter();
		filter.setFilterValue("DISTANCE_POIBURJ#dd_1");
		filter.setTitle("Filter Title");
		filter.setCount(10);
		filterList.add(filter);

		filter = new com.mmt.hotels.filter.Filter();
		filter.setFilterValue("DISTANCE_POIBURJ");
		filter.setTitle("Filter Title");
		filter.setCount(10);
		filterList.add(filter);

		Map<FilterGroup, List<com.mmt.hotels.filter.Filter>> filterDataMap = new HashMap<>();
		filterDataMap.put(FilterGroup.DRIVE_WALK_PROXIMITY_POI, filterList);

		List<com.mmt.hotels.clientgateway.response.filter.Filter> response = filterResponseTransformer.getDriveWalkProximityFilter("DRIVE_WALK_PROXIMITY_POI", filterDataMap, new FilterCategory());
		Assert.assertEquals(response.size(), 4);
		Assert.assertEquals(response.get(1).getFilterValue(), "DISTANCE_POIBURJ#wd_0-3000");
	}

	@Test
	public void buildFilterPillListFromPreAppliedFilters_withNullFilter() {
		List<com.mmt.hotels.clientgateway.request.Filter> preAppliedFilters = Collections.singletonList(null);
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		List<FilterPill> result = filterResponseTransformer.buildFilterPillListFromPreAppliedFilters(preAppliedFilters, filterResponseHES);
		assertTrue(result.isEmpty());
	}

	@Test
	public void buildFilterPillListFromPreAppliedFilters_withValidFilter() {
		com.mmt.hotels.clientgateway.request.Filter filter = new com.mmt.hotels.clientgateway.request.Filter();
		filter.setTitle("Breakfast Included");
		filter.setFilterGroup(com.mmt.hotels.clientgateway.response.filter.FilterGroup.FREE_BREAKFAST_AVAIL);
		filter.setFilterValue("BREAKFAST_AVAIL");
		List<com.mmt.hotels.clientgateway.request.Filter> preAppliedFilters = Collections.singletonList(filter);
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setFilterDataMap(new HashMap<>());
		Filter filterHES = new Filter();
		filterHES.setFilterValue("BREAKFAST_AVAIL");
		filterHES.setTitle("Breakfast Included");
		filterHES.setFilterGroup(FilterGroup.FREE_BREAKFAST_AVAIL);
		filterResponseHES.getFilterDataMap().put(FilterGroup.FREE_BREAKFAST_AVAIL, Collections.singletonList(filterHES));
		List<FilterPill> result = filterResponseTransformer.buildFilterPillListFromPreAppliedFilters(preAppliedFilters, filterResponseHES);
		assertFalse(result.isEmpty());
		assertEquals("Breakfast Included", result.get(0).getTitle());
		assertEquals("BREAKFAST_AVAIL", result.get(0).getId());
	}

	@Test
	public void buildFilterPillFromFilter() {
		com.mmt.hotels.clientgateway.response.filter.Filter filter = new com.mmt.hotels.clientgateway.response.filter.Filter();
		filter.setTitle("Breakfast Included");
		filter.setFilterValue("BREAKFAST_AVAIL");
		FilterPill result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterPillFromFilter", filter);
		assertEquals("Breakfast Included", result.getTitle());
		assertEquals("BREAKFAST_AVAIL", result.getId());
	}
	@Test
	public void sortChainInfoByName_sortsFiltersByNameWhenBrandFilterCategoryExists() {
		// Given
		FilterResponse filterResponse = new FilterResponse();
		FilterCategory filterCategory = new FilterCategory();
		filterCategory.setCategoryName("BRAND_FILTER");
		com.mmt.hotels.clientgateway.response.filter.Filter filter1 = new com.mmt.hotels.clientgateway.response.filter.Filter();
		filter1.setTitle("Z");
		com.mmt.hotels.clientgateway.response.filter.Filter filter2 = new com.mmt.hotels.clientgateway.response.filter.Filter();
		filter2.setTitle("A");
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(filter1);
		filters.add(filter2);
		filterCategory.setFilters(filters);
		List<FilterCategory> filterList = new ArrayList<>();
		filterList.add(filterCategory);
		filterResponse.setFilterList(filterList);

		// When
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "sortChainInfoByName", filterResponse);

		// Then
		assertEquals("A", filterResponse.getFilterList().get(0).getFilters().get(0).getTitle());
		assertEquals("Z", filterResponse.getFilterList().get(0).getFilters().get(1).getTitle());
	}

	@Test
	public void sortChainInfoByName_removesNullTitleFilters() {
		// Given
		FilterResponse filterResponse = new FilterResponse();
		FilterCategory filterCategory = new FilterCategory();
		filterCategory.setCategoryName("BRAND_FILTER");
		com.mmt.hotels.clientgateway.response.filter.Filter filter1 = new com.mmt.hotels.clientgateway.response.filter.Filter();
		filter1.setTitle(null);
		com.mmt.hotels.clientgateway.response.filter.Filter filter2 = new com.mmt.hotels.clientgateway.response.filter.Filter();
		filter2.setTitle("A");
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(filter1);
		filters.add(filter2);
		filterCategory.setFilters(filters);
		List<FilterCategory> filterList = new ArrayList<>();
		filterList.add(filterCategory);
		filterResponse.setFilterList(filterList);

		// When
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "sortChainInfoByName", filterResponse);

		// Then
		assertEquals(1, filterResponse.getFilterList().get(0).getFilters().size());
		assertEquals("A", filterResponse.getFilterList().get(0).getFilters().get(0).getTitle());
	}

	@Test
	public void sortChainInfoByName_doesNothingWhenFilterResponseIsNull() {
		// Given
		FilterResponse filterResponse = null;

		// When
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "sortChainInfoByName", filterResponse);

		// Then
		assertNull(filterResponse);
	}

	@Test
	public void sortChainInfoByName_doesNothingWhenFilterListIsEmpty() {
		// Given
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(Collections.emptyList());

		// When
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "sortChainInfoByName", filterResponse);

		// Then
		assertTrue(filterResponse.getFilterList().isEmpty());
	}

	@Test
	public void modifyFilterPillsGCCOffers_Test() {
		FilterResponse filterResponse = new FilterResponse();
		List<FilterPill> filterPills = new ArrayList<>();
		filterPills.add(new FilterPill());
		filterPills.add(new FilterPill());
		filterPills.add(new FilterPill());
		filterResponse.setFilterPills(filterPills);
		List<FilterCategory> filterList = new ArrayList<>();
		FilterCategory filter = new FilterCategory();
		filter.setCategoryName("DEALS");
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filterData = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter lmdFilter = new com.mmt.hotels.clientgateway.response.filter.Filter();
		lmdFilter.setFilterValue("LAST_MINUTE");
		lmdFilter.setFilterGroup("DEALS");
		lmdFilter.setCount(10);
		filterData.add(lmdFilter);
		filter.setFilters(filterData);
		filterList.add(filter);
		filterResponse.setFilterList(filterList);
		when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Last Min Deal");
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "modifyFilterPillsGCCOffers", filterResponse, SubpageContext.UNKNOWN.getName());
		Assert.assertNotNull(filterResponse);
		Assert.assertNotNull(filterResponse.getFilterPills());
		Assert.assertNotNull(filterResponse.getFilterPills().get(2));
		Assert.assertEquals("Last Min Deal", filterResponse.getFilterPills().get(2).getTitle());
	}

	@Test
	public void addRushDealFilterPillTest() {
		FilterResponse filterResponse = new FilterResponse();
		List<FilterPill> filterPills = new ArrayList<>();
		filterPills.add(new FilterPill());
		filterPills.add(new FilterPill());
		filterPills.add(new FilterPill());
		filterResponse.setFilterPills(filterPills);
		List<FilterCategory> filterList = new ArrayList<>();
		FilterCategory filter = new FilterCategory();
		filter.setCategoryName("RUSH_DEALS");
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filterData = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter lmdFilter = new com.mmt.hotels.clientgateway.response.filter.Filter();
		lmdFilter.setFilterValue("RUSH_DEALS");
		lmdFilter.setFilterGroup("RUSH_DEALS");
		lmdFilter.setCount(10);
		filterData.add(lmdFilter);
		filter.setFilters(filterData);
		filterList.add(filter);
		filterResponse.setFilterList(filterList);
		when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Rush Deal");
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "addRushDealFilterPill", filterResponse);
		Assert.assertNotNull(filterResponse);
		Assert.assertNotNull(filterResponse.getFilterPills());
		Assert.assertNotNull(filterResponse.getFilterPills().get(2));
		Assert.assertEquals("Rush Deal", filterResponse.getFilterPills().get(2).getTitle());
	}


	@Test
	public void modifyFilterPillsGCCOffers_TestFailCase() {
		FilterResponse filterResponse = new FilterResponse();
		List<FilterPill> filterPills = new ArrayList<>();
		filterPills.add(new FilterPill());
		filterPills.add(new FilterPill());
		FilterPill pill = new FilterPill();
		pill.setTitle("Pill 3");
		filterPills.add(pill);
		filterResponse.setFilterPills(filterPills);
		List<FilterCategory> filterList = new ArrayList<>();
		FilterCategory filter = new FilterCategory();
		filter.setCategoryName("DEALS");
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filterData = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter lmdFilter = new com.mmt.hotels.clientgateway.response.filter.Filter();
		lmdFilter.setFilterValue("EARLY_BIRD");
		lmdFilter.setFilterGroup("DEALS");
		lmdFilter.setCount(10);
		filterData.add(lmdFilter);
		filter.setFilters(filterData);
		filterList.add(filter);
		filterResponse.setFilterList(filterList);
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "modifyFilterPillsGCCOffers", filterResponse, SubpageContext.UNKNOWN.getName());
		Assert.assertNotNull(filterResponse);
		Assert.assertNotNull(filterResponse.getFilterPills());
		Assert.assertNotNull(filterResponse.getFilterPills().get(2));
		Assert.assertNotSame("Last Min Deal", filterResponse.getFilterPills().get(2).getTitle());
	}

	@Test
	public void getSupplierFiltersCount_Test() {
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		Map<FilterGroup, List<Filter>> filterMap = new HashMap<>();
		List<Filter> filterList = new ArrayList<>();
		Filter filter = new Filter();
		filter.setFilterValue("LAST_MINUTE");
		filter.setCount(10);
		filterList.add(filter);
		filterMap.put(FilterGroup.DEALS, filterList);
		filterResponseHES.setFilterDataMap(filterMap);
		String dealType = "LAST_MINUTE";
		int result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "getSupplierFiltersCount", filterResponseHES, dealType);
		Assert.assertEquals(10, result);
	}

	@Test
	public void getSupplierFiltersCount_TestFailCase() {
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		Map<FilterGroup, List<Filter>> filterMap = new HashMap<>();
		List<Filter> filterList = new ArrayList<>();
		Filter filter = new Filter();
		filter.setFilterValue("FILTER");
		filter.setCount(10);
		filterList.add(filter);
		filterMap.put(FilterGroup.DEALS, filterList);
		filterResponseHES.setFilterDataMap(filterMap);
		String dealType = "LAST_MINUTE";
		int result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "getSupplierFiltersCount", filterResponseHES, dealType);
		Assert.assertNotSame(10, result);
	}
	@Test
	public void buildFilterListTest_myPartner_appendDPTFilters() {
		LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>> groups = new LinkedHashMap<>();
		LinkedHashMap<String, FilterConfigDetail> value = new LinkedHashMap<>();
		value.put("ABC", new FilterConfigDetail());
		groups.put("ABC", value);
		FilterConfigCategory filterConfigCategory = new FilterConfigCategory();
		filterConfigCategory.setGroups(groups);
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		FilterCountRequest filterCountRequest = new FilterCountRequest();
		FilterConfiguration filterConfig = new FilterConfiguration();
		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put(MP_SUGGESTED_FILTER_ENABLED, TRUE);
		String filterConfigKey = "POPULAR";
		String idContext = "";
		FilterCategory fRespCategory = new FilterCategory();

		ExtendedUser extendedUser = new ExtendedUser();
		extendedUser.setProfileType("CTA");
		extendedUser.setAffiliateId("MYPARTNER");
		CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
		commonModifierResponse.setExtendedUser(extendedUser);

		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterList", filterConfigCategory, filterResponseHES, filterCountRequest, expDataMap, filterConfigKey, idContext, commonModifierResponse, fRespCategory, new LinkedHashMap<String, FilterConfigCategory>(), false);

	}

	@Test
	public void buildFilterListTest_NullCheck_appendDPTFiltersMyPartner() {
		LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>> groups = new LinkedHashMap<>();
		LinkedHashMap<String, FilterConfigDetail> value = new LinkedHashMap<>();
		value.put("ABC", new FilterConfigDetail());
		groups.put("ABC", value);
		FilterConfigCategory filterConfigCategory = new FilterConfigCategory();
		filterConfigCategory.setGroups(groups);
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		FilterCountRequest filterCountRequest = new FilterCountRequest();
		FilterConfiguration filterConfig = new FilterConfiguration();
		Map<String, String> expDataMap = new HashMap<>();
		String filterConfigKey = "POPULAR";
		String idContext = "";
		FilterCategory fRespCategory = new FilterCategory();
		CommonModifierResponse commonModifierResponse = new CommonModifierResponse();

		//extended user null
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterList", filterConfigCategory, filterResponseHES, filterCountRequest, expDataMap, filterConfigKey, idContext, commonModifierResponse, fRespCategory, new LinkedHashMap<String, FilterConfigCategory>(), false);

		ExtendedUser extendedUser = new ExtendedUser();
		extendedUser.setProfileType("CTA");
		extendedUser.setAffiliateId("MYPARTNER");
		commonModifierResponse.setExtendedUser(extendedUser);

		// common modifier response null
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterList", filterConfigCategory, filterResponseHES, filterCountRequest, expDataMap, filterConfigKey, idContext, null, fRespCategory, new LinkedHashMap<String, FilterConfigCategory>(), false);

		//filterConfigKey null
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterList", filterConfigCategory, filterResponseHES, filterCountRequest, expDataMap, null, idContext, commonModifierResponse, fRespCategory, new LinkedHashMap<String, FilterConfigCategory>(), false);

		//expDataMap null
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterList", filterConfigCategory, filterResponseHES, filterCountRequest, null, filterConfigKey, idContext, commonModifierResponse, fRespCategory, new LinkedHashMap<String, FilterConfigCategory>(), false);

		expDataMap.put(MP_SUGGESTED_FILTER_ENABLED, FALSE);

		//experiment false
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterList", filterConfigCategory, filterResponseHES, filterCountRequest, expDataMap, filterConfigKey, idContext, commonModifierResponse, fRespCategory, new LinkedHashMap<String, FilterConfigCategory>(), false);

	}

	@Test
	public void appendDPTContextualFiltersForMyPartnerTest_dptContextualFilterResponse_Null() {
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		List<Filter> filterList = new ArrayList<>();

		List<com.mmt.hotels.clientgateway.response.filter.Filter> dptFilters = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "appendDPTContextualFiltersForMyPartner", filterList, filterResponseHES, "POPULAR");

		// Should return empty list when dptContextualFilterResponse is null
		Assert.assertNotNull(dptFilters);
		Assert.assertTrue(dptFilters.isEmpty());
	}

	@Test
	public void appendDPTContextualFiltersForMyPartnerTest_primaryFilterValueMap_Empty() {
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		LinkedHashMap<String, List<Filter>> map = new LinkedHashMap<>();
		contextualFilterResponse.setPrimaryFilterValueMap(map);
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);
		List<Filter> filterList = new ArrayList<>();

		List<com.mmt.hotels.clientgateway.response.filter.Filter> dptFilters = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "appendDPTContextualFiltersForMyPartner", filterList, filterResponseHES, "POPULAR");

		// Should return empty list when primaryFilterValueMap is empty
		Assert.assertNotNull(dptFilters);
		Assert.assertTrue(dptFilters.isEmpty());
	}

	@Test
	public void testPrimaryFilterValueMapDoesNotContainKey() {
		LinkedHashMap<String, List<Filter>> map = new LinkedHashMap<>();
		map.put("ABC", new ArrayList<>());
		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		contextualFilterResponse.setPrimaryFilterValueMap(map);
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);

		List<Filter> filterList = new ArrayList<>();

		List<com.mmt.hotels.clientgateway.response.filter.Filter> dptFilters = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "appendDPTContextualFiltersForMyPartner", filterList, filterResponseHES, "POPULAR");

		// Should return empty list when key "POPULAR" is not in primaryFilterValueMap
		Assert.assertNotNull(dptFilters);
		Assert.assertTrue(dptFilters.isEmpty());
	}

	@Test
	public void testFiltersAddedToFilterList() {
		List<Filter> filters = new ArrayList<>();
		Filter filter1 = new Filter();
		filter1.setFilterGroup(FilterGroup.STAR_RATING);
		filter1.setFilterValue("5 star");
		filters.add(filter1);

		LinkedHashMap<String, List<Filter>> map = new LinkedHashMap<>();
		map.put("POPULAR", filters);
		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		contextualFilterResponse.setPrimaryFilterValueMap(map);
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);

		List<com.mmt.hotels.clientgateway.response.filter.Filter> filterList = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter filter2 = new com.mmt.hotels.clientgateway.response.filter.Filter();
		filter2.setFilterGroup("LOCATION");
		filter2.setFilterValue("ABC");
		filter2.setSequence(0);
		filterList.add(filter2);

		ReflectionTestUtils.setField(filterResponseTransformer, "maxDPTFiltersForMP", 5);
		List<com.mmt.hotels.clientgateway.response.filter.Filter> dptFilters = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "appendDPTContextualFiltersForMyPartner", filterList, filterResponseHES, "POPULAR");
		
		// Add the returned DPT filters to the original filter list
		if (dptFilters != null) {
			filterList.addAll(dptFilters);
		}

		assertEquals(2, filterList.size());
		assertEquals(1, filterList.get(filterList.size()-1).getSequence());
	}

	@Test
	public void testMaxDPTFiltersForMP() {
		List<Filter> filters = new ArrayList<>();
		for(int i = 0; i < 5 ; i++) {
			Filter filter = new Filter();
			filter.setFilterGroup(FilterGroup.STAR_RATING);
			filter.setFilterValue(i+" star");
			filters.add(filter);
		}

		LinkedHashMap<String, List<Filter>> map = new LinkedHashMap<>();
		map.put("POPULAR", filters);
		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		contextualFilterResponse.setPrimaryFilterValueMap(map);
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);

		List<com.mmt.hotels.clientgateway.response.filter.Filter> filterList = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter filter2 = new com.mmt.hotels.clientgateway.response.filter.Filter();
		filter2.setFilterGroup("LOCATION");
		filter2.setFilterValue("ABC");
		filter2.setSequence(0);
		filterList.add(filter2);

		ReflectionTestUtils.setField(filterResponseTransformer, "maxDPTFiltersForMP", 2);
		List<com.mmt.hotels.clientgateway.response.filter.Filter> dptFilters = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "appendDPTContextualFiltersForMyPartner", filterList, filterResponseHES, "POPULAR");
		
		// Add the returned DPT filters to the original filter list
		if (dptFilters != null) {
			filterList.addAll(dptFilters);
		}

		assertEquals(3, filterList.size());
		assertEquals(2, filterList.get(filterList.size()-1).getSequence());
	}

	@Test
	public void testNoDuplicateFiltersAdded() {
		List<Filter> filters = new ArrayList<>();
		Filter filter = new Filter();
		filter.setFilterGroup(FilterGroup.STAR_RATING);
		filter.setFilterValue("5 star");
		filters.add(filter);
		LinkedHashMap<String, List<Filter>> map = new LinkedHashMap<>();
		map.put("POPULAR", filters);
		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		contextualFilterResponse.setPrimaryFilterValueMap(map);
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);

		List<com.mmt.hotels.clientgateway.response.filter.Filter> filterList = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter filter2 = new com.mmt.hotels.clientgateway.response.filter.Filter();
		filter2.setFilterGroup("STAR_RATING");
		filter2.setFilterValue("5 star");
		filter2.setSequence(0);
		filterList.add(filter2);

		ReflectionTestUtils.setField(filterResponseTransformer, "maxDPTFiltersForMP", 2);
		List<com.mmt.hotels.clientgateway.response.filter.Filter> dptFilters = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "appendDPTContextualFiltersForMyPartner", filterList, filterResponseHES, "POPULAR");
		
		// Add the returned DPT filters to the original filter list
		if (dptFilters != null) {
			filterList.addAll(dptFilters);
		}

		assertEquals(1, filterList.size());
	}

	@Test
	public void buildDptInlineCollectionPillTest(){
		ArrayList<FilterPill> list = new ArrayList<>();
		list.add(new FilterPill());
		FilterResponse response = new FilterResponse();
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildDptInlineCollectionPill",list, "HOTELS", response);
		assertEquals(1, list.size());
	}

	@Test
	public void buildFilterPillsTest_WhenCanShowLocalityFilterIsFalse_WithPoiLocationType(){
		// Arrange
		FilterCategory filterCategory = new FilterCategory();
		List<FilterCategory> filterCategories = new ArrayList<>();
		FilterCountRequest filterRequest = new FilterCountRequest();
		RequestDetails requestDetails = new RequestDetails();
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setLocationType("poi"); // This should make canShowLocalityFilter = false
		filterRequest.setSearchCriteria(searchCriteria);
		filterRequest.setRequestDetails(requestDetails);

		com.mmt.hotels.clientgateway.response.filter.Filter f = new com.mmt.hotels.clientgateway.response.filter.Filter();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(f);
		filterCategory.setFilters(filters);
		filterCategory.setCategoryName("POPULAR");
		filterCategories.add(filterCategory);

		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(filterCategories);
		filterResponse.setFilterPills(new ArrayList<>());

		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		LinkedHashSet<String> primaryFilterCategories = new LinkedHashSet<>();
		primaryFilterCategories.add("POPULAR");
		contextualFilterResponse.setPrimaryFilterCategories(primaryFilterCategories);

		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);

		Map<String, String> expData = new LinkedHashMap<>();
		expData.put(Constants.ENABLE_LOCATION_RECOMMENDATION_EXP, "true");

		// Act
		List<FilterPill> result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterPills",
			filterResponse, filterResponseHES, filterPillConfig, expData, "HOTELS", filterRequest, false);

		// Assert
		Assert.assertNotNull(result);
		// Verify that no locality pills are present in the result
		Assert.assertFalse(result.stream().anyMatch(pill -> Constants.LOCALITY_GROUP.equalsIgnoreCase(pill.getId())));
	}

	@Test
	public void buildFilterPillsTest_WhenCanShowLocalityFilterIsFalse_WithAreaLocationType(){
		// Arrange
		FilterCategory filterCategory = new FilterCategory();
		List<FilterCategory> filterCategories = new ArrayList<>();
		FilterCountRequest filterRequest = new FilterCountRequest();
		RequestDetails requestDetails = new RequestDetails();
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setLocationType("area"); // This should make canShowLocalityFilter = false
		filterRequest.setSearchCriteria(searchCriteria);
		filterRequest.setRequestDetails(requestDetails);

		com.mmt.hotels.clientgateway.response.filter.Filter f = new com.mmt.hotels.clientgateway.response.filter.Filter();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(f);
		filterCategory.setFilters(filters);
		filterCategory.setCategoryName("POPULAR");
		filterCategories.add(filterCategory);

		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(filterCategories);
		filterResponse.setFilterPills(new ArrayList<>());

		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		LinkedHashSet<String> primaryFilterCategories = new LinkedHashSet<>();
		primaryFilterCategories.add("POPULAR");
		contextualFilterResponse.setPrimaryFilterCategories(primaryFilterCategories);

		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);

		Map<String, String> expData = new LinkedHashMap<>();
		expData.put(Constants.ENABLE_LOCATION_RECOMMENDATION_EXP, "true");

		// Act
		List<FilterPill> result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterPills",
			filterResponse, filterResponseHES, filterPillConfig, expData, "HOTELS", filterRequest, false);

		// Assert
		Assert.assertNotNull(result);
		// Verify that no locality pills are present in the result
		Assert.assertFalse(result.stream().anyMatch(pill -> Constants.LOCALITY_GROUP.equalsIgnoreCase(pill.getId())));
	}

	@Test
	public void buildFilterPillsTest_WhenCanShowLocalityFilterIsTrue_WithCityLocationType(){
		// Arrange
		FilterCategory filterCategory = new FilterCategory();
		List<FilterCategory> filterCategories = new ArrayList<>();
		FilterCountRequest filterRequest = new FilterCountRequest();
		RequestDetails requestDetails = new RequestDetails();
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setLocationType("city"); // This should make canShowLocalityFilter = true
		filterRequest.setSearchCriteria(searchCriteria);
		filterRequest.setRequestDetails(requestDetails);

		com.mmt.hotels.clientgateway.response.filter.Filter f = new com.mmt.hotels.clientgateway.response.filter.Filter();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(f);
		filterCategory.setFilters(filters);
		filterCategory.setCategoryName("POPULAR");
		filterCategories.add(filterCategory);

		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(filterCategories);
		filterResponse.setFilterPills(new ArrayList<>());

		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		LinkedHashSet<String> primaryFilterCategories = new LinkedHashSet<>();
		primaryFilterCategories.add("POPULAR");
		contextualFilterResponse.setPrimaryFilterCategories(primaryFilterCategories);

		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);

		Map<String, String> expData = new LinkedHashMap<>();
		expData.put(Constants.ENABLE_LOCATION_RECOMMENDATION_EXP, "true");

		// Mock PWA request to enable locality pill
		MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), "PWA");
		expData.put(Constants.FILTER_PILL_EXP, Constants.EXP_TRUE_VALUE);

		// Act
		List<FilterPill> result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterPills",
			filterResponse, filterResponseHES, filterPillConfig, expData, "HOTELS", filterRequest, false);

		// Assert
		Assert.assertNotNull(result);
		// Since canShowLocalityFilter is true and experiments are enabled, locality pill should be present
		// Note: The actual presence depends on the filterPillConfig having LOCALITY configuration
		// We're testing that the method doesn't remove locality pills when canShowLocalityFilter is true

		// Clean up MDC
		MDC.clear();
	}

	@Test
	public void buildFilterPillsTest_WhenCanShowLocalityFilterIsTrue_WithCountryLocationType(){
		// Arrange
		FilterCategory filterCategory = new FilterCategory();
		List<FilterCategory> filterCategories = new ArrayList<>();
		FilterCountRequest filterRequest = new FilterCountRequest();
		RequestDetails requestDetails = new RequestDetails();
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setLocationType("country"); // This should make canShowLocalityFilter = true
		filterRequest.setSearchCriteria(searchCriteria);
		filterRequest.setRequestDetails(requestDetails);

		com.mmt.hotels.clientgateway.response.filter.Filter f = new com.mmt.hotels.clientgateway.response.filter.Filter();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(f);
		filterCategory.setFilters(filters);
		filterCategory.setCategoryName("POPULAR");
		filterCategories.add(filterCategory);

		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(filterCategories);
		filterResponse.setFilterPills(new ArrayList<>());

		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		LinkedHashSet<String> primaryFilterCategories = new LinkedHashSet<>();
		primaryFilterCategories.add("POPULAR");
		contextualFilterResponse.setPrimaryFilterCategories(primaryFilterCategories);

		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);

		Map<String, String> expData = new LinkedHashMap<>();
		expData.put(Constants.ENABLE_LOCATION_RECOMMENDATION_EXP, "true");

		// Mock PWA request to enable locality pill
		MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), "PWA");
		expData.put(Constants.FILTER_PILL_EXP, Constants.EXP_TRUE_VALUE);

		// Act
		List<FilterPill> result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterPills",
			filterResponse, filterResponseHES, filterPillConfig, expData, "HOTELS", filterRequest, false);

		// Assert
		Assert.assertNotNull(result);
		// Since canShowLocalityFilter is true and experiments are enabled, locality pill should be present
		// Note: The actual presence depends on the filterPillConfig having LOCALITY configuration
		// We're testing that the method doesn't remove locality pills when canShowLocalityFilter is true

		// Clean up MDC
		MDC.clear();
	}

	@Test
	public void buildFilterPillsTest_WhenCanShowLocalityFilterIsTrue_ButExperimentsDisabled(){
		// Arrange
		FilterCategory filterCategory = new FilterCategory();
		List<FilterCategory> filterCategories = new ArrayList<>();
		FilterCountRequest filterRequest = new FilterCountRequest();
		RequestDetails requestDetails = new RequestDetails();
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setLocationType("city"); // This should make canShowLocalityFilter = true
		filterRequest.setSearchCriteria(searchCriteria);
		filterRequest.setRequestDetails(requestDetails);

		com.mmt.hotels.clientgateway.response.filter.Filter f = new com.mmt.hotels.clientgateway.response.filter.Filter();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(f);
		filterCategory.setFilters(filters);
		filterCategory.setCategoryName("POPULAR");
		filterCategories.add(filterCategory);

		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(filterCategories);
		filterResponse.setFilterPills(new ArrayList<>());

		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		LinkedHashSet<String> primaryFilterCategories = new LinkedHashSet<>();
		primaryFilterCategories.add("POPULAR");
		contextualFilterResponse.setPrimaryFilterCategories(primaryFilterCategories);

		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);

		Map<String, String> expData = new LinkedHashMap<>();
		// Not setting ENABLE_LOCATION_RECOMMENDATION_EXP or FILTER_PILL_EXP to test disabled experiments

		// Act
		List<FilterPill> result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterPills",
			filterResponse, filterResponseHES, filterPillConfig, expData, "HOTELS", filterRequest, false);

		// Assert
		Assert.assertNotNull(result);
		// Even though canShowLocalityFilter is true, locality pill should not be added due to disabled experiments
	}

	@Test
	public void buildFilterPillsTest_WhenLocationTypeIsNull_ShouldDefaultToCanShowLocalityFilterTrue(){
		// Arrange
		FilterCategory filterCategory = new FilterCategory();
		List<FilterCategory> filterCategories = new ArrayList<>();
		FilterCountRequest filterRequest = new FilterCountRequest();
		RequestDetails requestDetails = new RequestDetails();
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setLocationType(null); // Null location type should default to canShowLocalityFilter = true
		filterRequest.setSearchCriteria(searchCriteria);
		filterRequest.setRequestDetails(requestDetails);

		com.mmt.hotels.clientgateway.response.filter.Filter f = new com.mmt.hotels.clientgateway.response.filter.Filter();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(f);
		filterCategory.setFilters(filters);
		filterCategory.setCategoryName("POPULAR");
		filterCategories.add(filterCategory);

		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(filterCategories);
		filterResponse.setFilterPills(new ArrayList<>());

		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		LinkedHashSet<String> primaryFilterCategories = new LinkedHashSet<>();
		primaryFilterCategories.add("POPULAR");
		contextualFilterResponse.setPrimaryFilterCategories(primaryFilterCategories);

		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);

		Map<String, String> expData = new LinkedHashMap<>();
		expData.put(Constants.ENABLE_LOCATION_RECOMMENDATION_EXP, "true");

		// Mock PWA request to enable locality pill
		MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), "PWA");
		expData.put(Constants.FILTER_PILL_EXP, Constants.EXP_TRUE_VALUE);

		// Act
		List<FilterPill> result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterPills",
			filterResponse, filterResponseHES, filterPillConfig, expData, "HOTELS", filterRequest, false);

		// Assert
		Assert.assertNotNull(result);
		// Since location type is null, canShowLocalityFilter should default to true
		// and locality pills should not be removed

		// Clean up MDC
		MDC.clear();
	}

	@Test
	public void buildFilterPillsTest_WhenLocationTypeIsEmpty_ShouldDefaultToCanShowLocalityFilterTrue(){
		// Arrange
		FilterCategory filterCategory = new FilterCategory();
		List<FilterCategory> filterCategories = new ArrayList<>();
		FilterCountRequest filterRequest = new FilterCountRequest();
		RequestDetails requestDetails = new RequestDetails();
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setLocationType(""); // Empty location type should default to canShowLocalityFilter = true
		filterRequest.setSearchCriteria(searchCriteria);
		filterRequest.setRequestDetails(requestDetails);

		com.mmt.hotels.clientgateway.response.filter.Filter f = new com.mmt.hotels.clientgateway.response.filter.Filter();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(f);
		filterCategory.setFilters(filters);
		filterCategory.setCategoryName("POPULAR");
		filterCategories.add(filterCategory);

		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(filterCategories);
		filterResponse.setFilterPills(new ArrayList<>());

		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		LinkedHashSet<String> primaryFilterCategories = new LinkedHashSet<>();
		primaryFilterCategories.add("POPULAR");
		contextualFilterResponse.setPrimaryFilterCategories(primaryFilterCategories);

		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);

		Map<String, String> expData = new LinkedHashMap<>();
		expData.put(Constants.ENABLE_LOCATION_RECOMMENDATION_EXP, "true");

		// Act
		List<FilterPill> result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterPills",
			filterResponse, filterResponseHES, filterPillConfig, expData, "HOTELS", filterRequest, false);

		// Assert
		Assert.assertNotNull(result);
		// Since location type is empty, canShowLocalityFilter should default to true
		// and locality pills should not be removed
	}

	@Test
	public void buildFilterPillsTest_WhenLocationTypeCaseInsensitive_PoiUpperCase(){
		// Arrange
		FilterCategory filterCategory = new FilterCategory();
		List<FilterCategory> filterCategories = new ArrayList<>();
		FilterCountRequest filterRequest = new FilterCountRequest();
		RequestDetails requestDetails = new RequestDetails();
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setLocationType("POI"); // Uppercase POI should still make canShowLocalityFilter = false
		filterRequest.setSearchCriteria(searchCriteria);
		filterRequest.setRequestDetails(requestDetails);

		com.mmt.hotels.clientgateway.response.filter.Filter f = new com.mmt.hotels.clientgateway.response.filter.Filter();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(f);
		filterCategory.setFilters(filters);
		filterCategory.setCategoryName("POPULAR");
		filterCategories.add(filterCategory);

		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(filterCategories);
		filterResponse.setFilterPills(new ArrayList<>());

		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		LinkedHashSet<String> primaryFilterCategories = new LinkedHashSet<>();
		primaryFilterCategories.add("POPULAR");
		contextualFilterResponse.setPrimaryFilterCategories(primaryFilterCategories);

		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);

		Map<String, String> expData = new LinkedHashMap<>();
		expData.put(Constants.ENABLE_LOCATION_RECOMMENDATION_EXP, "true");

		// Act
		List<FilterPill> result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterPills",
			filterResponse, filterResponseHES, filterPillConfig, expData, "HOTELS", filterRequest, false);

		// Assert
		Assert.assertNotNull(result);
		// Verify that no locality pills are present in the result due to case-insensitive POI check
		Assert.assertFalse(result.stream().anyMatch(pill -> Constants.LOCALITY_GROUP.equalsIgnoreCase(pill.getId())));
	}

	@Test
	public void buildFilterPillsTest_WhenFilterCountRequestIsEmpty_ShouldDefaultToCanShowLocalityFilterTrue(){
		// Arrange
		FilterCategory filterCategory = new FilterCategory();
		List<FilterCategory> filterCategories = new ArrayList<>();

		FilterCountRequest filterRequest = new FilterCountRequest();
		RequestDetails requestDetails = new RequestDetails();
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		searchCriteria.setLocationType("POI"); // Uppercase POI should still make canShowLocalityFilter = false
		filterRequest.setSearchCriteria(searchCriteria);
		filterRequest.setRequestDetails(requestDetails);
		com.mmt.hotels.clientgateway.response.filter.Filter f = new com.mmt.hotels.clientgateway.response.filter.Filter();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(f);
		filterCategory.setFilters(filters);
		filterCategory.setCategoryName("POPULAR");
		filterCategories.add(filterCategory);

		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(filterCategories);
		filterResponse.setFilterPills(new ArrayList<>());

		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		LinkedHashSet<String> primaryFilterCategories = new LinkedHashSet<>();
		primaryFilterCategories.add("POPULAR");
		contextualFilterResponse.setPrimaryFilterCategories(primaryFilterCategories);

		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);

		Map<String, String> expData = new LinkedHashMap<>();
		expData.put(Constants.ENABLE_LOCATION_RECOMMENDATION_EXP, "true");

		// Act
		List<FilterPill> result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterPills",
			filterResponse, filterResponseHES, filterPillConfig, expData, "HOTELS", filterRequest, false);

		// Assert
		Assert.assertNotNull(result);
		// When filterCountRequest is null, canShowLocalityFilter should default to true
		// and locality pills should not be removed
	}

	@Test
	public void buildFilterPillsTest_WhenSearchCriteriaIsNull_ShouldDefaultToCanShowLocalityFilterTrue(){
		// Arrange
		FilterCategory filterCategory = new FilterCategory();
		List<FilterCategory> filterCategories = new ArrayList<>();
		FilterCountRequest filterRequest = new FilterCountRequest();
		RequestDetails requestDetails = new RequestDetails();
		filterRequest.setSearchCriteria(null); // Null search criteria should default to canShowLocalityFilter = true
		filterRequest.setRequestDetails(requestDetails);

		com.mmt.hotels.clientgateway.response.filter.Filter f = new com.mmt.hotels.clientgateway.response.filter.Filter();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		filters.add(f);
		filterCategory.setFilters(filters);
		filterCategory.setCategoryName("POPULAR");
		filterCategories.add(filterCategory);

		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterList(filterCategories);
		filterResponse.setFilterPills(new ArrayList<>());

		ContextualFilterResponse contextualFilterResponse = new ContextualFilterResponse();
		LinkedHashSet<String> primaryFilterCategories = new LinkedHashSet<>();
		primaryFilterCategories.add("POPULAR");
		contextualFilterResponse.setPrimaryFilterCategories(primaryFilterCategories);

		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setDptContextualFilterResponse(contextualFilterResponse);

		Map<String, String> expData = new LinkedHashMap<>();
		expData.put(Constants.ENABLE_LOCATION_RECOMMENDATION_EXP, "true");

		// Act
		List<FilterPill> result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterPills",
			filterResponse, filterResponseHES, filterPillConfig, expData, "HOTELS", filterRequest, false);

		// Assert
		Assert.assertNotNull(result);
		// When search criteria is null, canShowLocalityFilter should default to true
		// and locality pills should not be removed
	}

	@Test
	public void shouldReturnEmptyFilterMapWhenAdults10() {
		Map<String, String> expDataMap = new HashMap<>();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> result = ReflectionTestUtils.invokeMethod(filterResponseTransformer,"createDefaultBedRoomCountFilter","BEDROOM_COUNT", 5, 5, expDataMap);
		org.junit.Assert.assertNotNull(result);
	}

	@Test
	public void shouldReturnEmptyFilterMapWhenAdults1() {
		Map<String, String> expDataMap = new HashMap<>();
		List<com.mmt.hotels.clientgateway.response.filter.Filter> result = ReflectionTestUtils.invokeMethod(filterResponseTransformer,"createDefaultBedRoomCountFilter","BEDROOM_COUNT", 2, 2, expDataMap);
		org.junit.Assert.assertNotNull(result);
	}

	@Test
	public void buildFilterCollection_shouldReturnEmptyListWhenFilterListIsNull() {
		List<FilterCollection> result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterCollection", null, "HOTELS");
		Assert.assertNotNull(result);
		Assert.assertTrue(result.isEmpty());
	}

	@Test
	public void buildFilterCollection_shouldReturnEmptyListWhenFilterListIsEmpty() {
		List<FilterCollection> result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "buildFilterCollection", Collections.emptyList(), "HOTELS");
		Assert.assertNotNull(result);
		Assert.assertTrue(result.isEmpty());
	}

	@Test
	public void myPartnerContextualFilters_shouldReturnTrueWhenCRFExperimentIsF() {
		Map<String, String> expData = new HashMap<>();
		expData.put("CRF", "F");
		boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(filterResponseTransformer, "myPartnerContextualFilters", expData));
		Assert.assertTrue(result);
	}

	@Test
	public void myPartnerContextualFilters_shouldReturnFalseWhenCRFExperimentIsNotF() {
		Map<String, String> expData = new HashMap<>();
		expData.put("CRF", "T");
		boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(filterResponseTransformer, "myPartnerContextualFilters", expData));
		Assert.assertFalse(result);
	}

	@Test
	public void testModifyFilterPills_WithValidFilterPill() {
		// Arrange
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterPills(new ArrayList<>());
		
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setFilterPills(new HashMap<>());
		
		com.mmt.hotels.filter.FilterPill hesFilterPill = new com.mmt.hotels.filter.FilterPill();
		hesFilterPill.setType(FILTER);
		hesFilterPill.setFilters(new ArrayList<>());
		
		com.mmt.hotels.filter.Filter hesFilter = new com.mmt.hotels.filter.Filter();
		hesFilter.setFilterGroup(com.mmt.hotels.filter.FilterGroup.HOTEL_CATEGORY);
		hesFilter.setFilterValue("IH_PROPERTY");
		hesFilter.setTitle("IH Property");
		hesFilter.setCount(5);
		hesFilter.setFilterExist(true);
		hesFilter.setQuantityFilter(false);
		hesFilter.setRangeFilter(false);
		
		hesFilterPill.getFilters().add(hesFilter);
		filterResponseHES.getFilterPills().put(IH_PROPERTY_FILTER_PILL, hesFilterPill);
		
		// Act
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "modifyFilterPills", filterResponse, filterResponseHES);
		
		// Assert
		Assert.assertEquals(1, filterResponse.getFilterPills().size());
		FilterPill addedPill = filterResponse.getFilterPills().get(0);
		Assert.assertEquals(IH_PROPERTY_FILTER_PILL_ID, addedPill.getId());
		Assert.assertEquals("IH Property", addedPill.getTitle());
		Assert.assertEquals(FILTER, addedPill.getType());
		Assert.assertNotNull(addedPill.getPillFilter());
	}

	@Test
	public void testModifyFilterPills_WithNullFilterPills() {
		// Arrange
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterPills(new ArrayList<>());
		
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setFilterPills(null);
		
		// Act
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "modifyFilterPills", filterResponse, filterResponseHES);
		
		// Assert
		Assert.assertEquals(0, filterResponse.getFilterPills().size());
	}

	@Test
	public void testModifyFilterPills_WithEmptyFilters() {
		// Arrange
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterPills(new ArrayList<>());
		
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setFilterPills(new HashMap<>());
		
		com.mmt.hotels.filter.FilterPill hesFilterPill = new com.mmt.hotels.filter.FilterPill();
		hesFilterPill.setType(FILTER);
		hesFilterPill.setFilters(new ArrayList<>()); // Empty filters list
		
		filterResponseHES.getFilterPills().put(IH_PROPERTY_FILTER_PILL, hesFilterPill);
		
		// Act
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "modifyFilterPills", filterResponse, filterResponseHES);
		
		// Assert
		Assert.assertEquals(0, filterResponse.getFilterPills().size());
	}

	@Test
	public void testModifyFilterPills_WithIncorrectType() {
		// Arrange
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterPills(new ArrayList<>());
		
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setFilterPills(new HashMap<>());
		
		com.mmt.hotels.filter.FilterPill hesFilterPill = new com.mmt.hotels.filter.FilterPill();
		hesFilterPill.setType("INCORRECT_TYPE"); // Not FILTER
		hesFilterPill.setFilters(new ArrayList<>());
		
		com.mmt.hotels.filter.Filter hesFilter = new com.mmt.hotels.filter.Filter();
		hesFilterPill.getFilters().add(hesFilter);
		
		filterResponseHES.getFilterPills().put(IH_PROPERTY_FILTER_PILL, hesFilterPill);
		
		// Act
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "modifyFilterPills", filterResponse, filterResponseHES);
		
		// Assert
		Assert.assertEquals(0, filterResponse.getFilterPills().size());
	}

	@Test
	public void testModifyFilterPills_WithExistingPills() {
		// Arrange
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterPills(new ArrayList<>());
		
		// Add existing pills to test insertion position
		FilterPill rushDealPill = new FilterPill();
		rushDealPill.setId(RUSH_DEAL_FILTER_PILL_ID);
		filterResponse.getFilterPills().add(rushDealPill);
		
		FilterPill otherPill = new FilterPill();
		otherPill.setId("OTHER_PILL");
		filterResponse.getFilterPills().add(otherPill);
		
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setFilterPills(new HashMap<>());
		
		com.mmt.hotels.filter.FilterPill hesFilterPill = new com.mmt.hotels.filter.FilterPill();
		hesFilterPill.setType(FILTER);
		hesFilterPill.setFilters(new ArrayList<>());
		
		com.mmt.hotels.filter.Filter hesFilter = new com.mmt.hotels.filter.Filter();
		hesFilter.setFilterGroup(com.mmt.hotels.filter.FilterGroup.HOTEL_CATEGORY);
		hesFilter.setFilterValue("IH_PROPERTY");
		hesFilter.setTitle("IH Property");
		
		hesFilterPill.getFilters().add(hesFilter);
		filterResponseHES.getFilterPills().put(IH_PROPERTY_FILTER_PILL, hesFilterPill);
		
		// Act
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "modifyFilterPills", filterResponse, filterResponseHES);
		
		// Assert
		Assert.assertEquals(3, filterResponse.getFilterPills().size());
		// Should be inserted after rush deal pill (at position 1)
		FilterPill insertedPill = filterResponse.getFilterPills().get(1);
		Assert.assertEquals(IH_PROPERTY_FILTER_PILL_ID, insertedPill.getId());
	}

	@Test
	public void testExtractFilterPillFromHES_ValidInput() {
		// Arrange
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setFilterPills(new HashMap<>());
		
		com.mmt.hotels.filter.FilterPill hesFilterPill = new com.mmt.hotels.filter.FilterPill();
		hesFilterPill.setType(FILTER);
		hesFilterPill.setFilters(new ArrayList<>());
		
		com.mmt.hotels.filter.Filter hesFilter = new com.mmt.hotels.filter.Filter();
		hesFilter.setFilterGroup(com.mmt.hotels.filter.FilterGroup.HOTEL_CATEGORY);
		hesFilter.setFilterValue("IH_PROPERTY");
		hesFilter.setTitle("IH Property Test");
		hesFilter.setCount(10);
		hesFilter.setFilterExist(true);
		hesFilter.setQuantityFilter(true);
		hesFilter.setRangeFilter(false);
		
		hesFilterPill.getFilters().add(hesFilter);
		filterResponseHES.getFilterPills().put(IH_PROPERTY_FILTER_PILL, hesFilterPill);
		
		// Act
		FilterPill result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "extractFilterPillFromHES", filterResponseHES);
		
		// Assert
		Assert.assertNotNull(result);
		Assert.assertEquals(IH_PROPERTY_FILTER_PILL_ID, result.getId());
		Assert.assertEquals("IH Property Test", result.getTitle());
		Assert.assertEquals(FILTER, result.getType());
		Assert.assertNotNull(result.getCategories());
		Assert.assertNotNull(result.getPillFilter());
		Assert.assertNull(result.getPillType());
	}

	@Test
	public void testExtractFilterPillFromHES_NullFilterPill() {
		// Arrange
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setFilterPills(new HashMap<>());
		filterResponseHES.getFilterPills().put(IH_PROPERTY_FILTER_PILL, null);
		
		// Act
		FilterPill result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "extractFilterPillFromHES", filterResponseHES);
		
		// Assert
		Assert.assertNull(result);
	}

	@Test
	public void testExtractFilterPillFromHES_WrongType() {
		// Arrange
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setFilterPills(new HashMap<>());
		
		com.mmt.hotels.filter.FilterPill hesFilterPill = new com.mmt.hotels.filter.FilterPill();
		hesFilterPill.setType("WRONG_TYPE");
		hesFilterPill.setFilters(new ArrayList<>());
		
		filterResponseHES.getFilterPills().put(IH_PROPERTY_FILTER_PILL, hesFilterPill);
		
		// Act
		FilterPill result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "extractFilterPillFromHES", filterResponseHES);
		
		// Assert
		Assert.assertNull(result);
	}

	@Test
	public void testExtractFilterPillFromHES_EmptyFilters() {
		// Arrange
		FilterSearchMetaDataResponse filterResponseHES = new FilterSearchMetaDataResponse();
		filterResponseHES.setFilterPills(new HashMap<>());
		
		com.mmt.hotels.filter.FilterPill hesFilterPill = new com.mmt.hotels.filter.FilterPill();
		hesFilterPill.setType(FILTER);
		hesFilterPill.setFilters(new ArrayList<>());
		
		filterResponseHES.getFilterPills().put(IH_PROPERTY_FILTER_PILL, hesFilterPill);
		
		// Act
		FilterPill result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "extractFilterPillFromHES", filterResponseHES);
		
		// Assert
		Assert.assertNull(result);
	}

	@Test
	public void testGetIHPropertyFilter_ValidInput() {
		// Arrange
		com.mmt.hotels.filter.Filter hesFilter = new com.mmt.hotels.filter.Filter();
		hesFilter.setFilterGroup(com.mmt.hotels.filter.FilterGroup.HOTEL_CATEGORY);
		hesFilter.setFilterValue("TEST_VALUE");
		hesFilter.setCount(15);
		hesFilter.setTitle("Test Title");
		hesFilter.setFilterExist(true);
		hesFilter.setQuantityFilter(false);
		hesFilter.setRangeFilter(true);
		
		// Act
		com.mmt.hotels.clientgateway.response.filter.Filter result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "getIHPropertyFilter", hesFilter);
		
		// Assert
		Assert.assertNotNull(result);
		Assert.assertEquals("HOTEL_CATEGORY", result.getFilterGroup());
		Assert.assertEquals("TEST_VALUE", result.getFilterValue());
		Assert.assertEquals(Integer.valueOf(15), result.getCount());
		Assert.assertEquals("Test Title", result.getTitle());
		Assert.assertFalse(result.isQuantityFilter());
	}

	@Test
	public void testFindInsertPosition_AfterRushDeals() {
		// Arrange
		List<FilterPill> filterPills = new ArrayList<>();
		
		FilterPill rushDealPill = new FilterPill();
		rushDealPill.setId(RUSH_DEAL_FILTER_PILL_ID);
		filterPills.add(rushDealPill);
		
		FilterPill otherPill = new FilterPill();
		otherPill.setId("OTHER");
		filterPills.add(otherPill);
		
		// Act
		int result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "findInsertPosition", filterPills);
		
		// Assert
		Assert.assertEquals(1, result); // Should insert after rush deals (position 0)
	}

	@Test
	public void testFindInsertPosition_AfterLastMinDeal() {
		// Arrange
		List<FilterPill> filterPills = new ArrayList<>();
		
		FilterPill lastMinDealPill = new FilterPill();
		lastMinDealPill.setId(LAST_MIN_DEAL_FILTER_PILL_ID);
		filterPills.add(lastMinDealPill);
		
		FilterPill otherPill = new FilterPill();
		otherPill.setId("OTHER");
		filterPills.add(otherPill);
		
		// Act
		int result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "findInsertPosition", filterPills);
		
		// Assert
		Assert.assertEquals(1, result); // Should insert after last min deal (position 0)
	}

	@Test
	public void testFindInsertPosition_AfterEarlyBirdDeal() {
		// Arrange
		List<FilterPill> filterPills = new ArrayList<>();
		
		FilterPill earlyBirdDealPill = new FilterPill();
		earlyBirdDealPill.setId(EARLY_BIRD_DEAL_FILTER_PILL_ID);
		filterPills.add(earlyBirdDealPill);
		
		FilterPill otherPill = new FilterPill();
		otherPill.setId("OTHER");
		filterPills.add(otherPill);
		
		// Act
		int result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "findInsertPosition", filterPills);
		
		// Assert
		Assert.assertEquals(1, result); // Should insert after early bird deal (position 0)
	}

	@Test
	public void testFindInsertPosition_AfterAllFilters() {
		// Arrange
		List<FilterPill> filterPills = new ArrayList<>();
		
		FilterPill allFiltersPill = new FilterPill();
		allFiltersPill.setId(ALL_FILTERS_FILTER_PILL_ID);
		filterPills.add(allFiltersPill);
		
		FilterPill otherPill = new FilterPill();
		otherPill.setId("OTHER");
		filterPills.add(otherPill);
		
		// Act
		int result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "findInsertPosition", filterPills);
		
		// Assert
		Assert.assertEquals(1, result); // Should insert after all filters (position 0)
	}

	@Test
	public void testFindInsertPosition_AtBeginning() {
		// Arrange
		List<FilterPill> filterPills = new ArrayList<>();
		
		FilterPill otherPill1 = new FilterPill();
		otherPill1.setId("OTHER1");
		filterPills.add(otherPill1);
		
		FilterPill otherPill2 = new FilterPill();
		otherPill2.setId("OTHER2");
		filterPills.add(otherPill2);
		
		// Act
		int result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "findInsertPosition", filterPills);
		
		// Assert
		Assert.assertEquals(0, result); // Should insert at beginning when no priority pills found
	}

	@Test
	public void testFindInsertPosition_PriorityOrder() {
		// Arrange - Test that rush deals has higher priority than last min deal
		List<FilterPill> filterPills = new ArrayList<>();
		
		FilterPill lastMinDealPill = new FilterPill();
		lastMinDealPill.setId(LAST_MIN_DEAL_FILTER_PILL_ID);
		filterPills.add(lastMinDealPill);
		
		FilterPill rushDealPill = new FilterPill();
		rushDealPill.setId(RUSH_DEAL_FILTER_PILL_ID);
		filterPills.add(rushDealPill);
		
		// Act
		int result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "findInsertPosition", filterPills);
		
		// Assert
		Assert.assertEquals(2, result); // Should insert after rush deals (position 1), not after last min deal
	}

	@Test
	public void testFindFilterPillIndex_ValidPill() {
		// Arrange
		List<FilterPill> filterPills = new ArrayList<>();
		
		FilterPill pill1 = new FilterPill();
		pill1.setId("PILL1");
		filterPills.add(pill1);
		
		FilterPill pill2 = new FilterPill();
		pill2.setId("PILL2");
		filterPills.add(pill2);
		
		FilterPill pill3 = new FilterPill();
		pill3.setId("PILL3");
		filterPills.add(pill3);
		
		// Act
		int result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "findFilterPillIndex", filterPills, "PILL2");
		
		// Assert
		Assert.assertEquals(1, result);
	}

	@Test
	public void testFindFilterPillIndex_NotFound() {
		// Arrange
		List<FilterPill> filterPills = new ArrayList<>();
		
		FilterPill pill1 = new FilterPill();
		pill1.setId("PILL1");
		filterPills.add(pill1);
		
		// Act
		int result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "findFilterPillIndex", filterPills, "NON_EXISTENT");
		
		// Assert
		Assert.assertEquals(-1, result);
	}

	@Test
	public void testFindFilterPillIndex_NullList() {
		// Act
		int result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "findFilterPillIndex", null, "PILL1");
		
		// Assert
		Assert.assertEquals(-1, result);
	}

	@Test
	public void testFindFilterPillIndex_NullPillId() {
		// Arrange
		List<FilterPill> filterPills = new ArrayList<>();
		FilterPill pill1 = new FilterPill();
		pill1.setId("PILL1");
		filterPills.add(pill1);
		
		// Act
		int result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "findFilterPillIndex", filterPills, null);
		
		// Assert
		Assert.assertEquals(-1, result);
	}

	@Test
	public void testFindFilterPillIndex_NullPillInList() {
		// Arrange
		List<FilterPill> filterPills = new ArrayList<>();
		filterPills.add(null);
		
		FilterPill pill2 = new FilterPill();
		pill2.setId("PILL2");
		filterPills.add(pill2);
		
		// Act
		int result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "findFilterPillIndex", filterPills, "PILL2");
		
		// Assert
		Assert.assertEquals(1, result);
	}

	@Test
	public void testFindFilterPillIndex_EmptyList() {
		// Arrange
		List<FilterPill> filterPills = new ArrayList<>();
		
		// Act
		int result = ReflectionTestUtils.invokeMethod(filterResponseTransformer, "findFilterPillIndex", filterPills, "PILL1");
		
		// Assert
		Assert.assertEquals(-1, result);
	}

	@Test
	public void testCalculateOptimalDPTInsertionIndex_WithExplicitMinItemsToShow() {
		// Arrange
		ReflectionTestUtils.setField(filterResponseTransformer, "filterMinItemsToShow", 5);
		
		FilterCategory filterCategory = new FilterCategory();
		filterCategory.setMinItemsToShow(3); // Explicit value set
		
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = createFilterList(8); // 8 existing filters
		filterCategory.setFilters(filters);
		
		int dptFiltersCount = 2;
		
		// Act
		int result = ReflectionTestUtils.invokeMethod(
			filterResponseTransformer, "calculateOptimalDPTInsertionIndex", filterCategory, dptFiltersCount);
		
		// Assert
		// maxAllowedStartIndex = max(0, 3 - 2) = 1
		// result = min(1, 8) = 1
		Assert.assertEquals(1, result);
	}

	@Test
	public void testCalculateOptimalDPTInsertionIndex_WithoutExplicitMinItemsToShow() {
		// Arrange
		ReflectionTestUtils.setField(filterResponseTransformer, "filterMinItemsToShow", 5);
		
		FilterCategory filterCategory = new FilterCategory();
		filterCategory.setMinItemsToShow(null); // No explicit value
		
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = createFilterList(8); // 8 existing filters
		filterCategory.setFilters(filters);
		
		int dptFiltersCount = 3;
		
		// Act
		int result = ReflectionTestUtils.invokeMethod(
			filterResponseTransformer, "calculateOptimalDPTInsertionIndex", filterCategory, dptFiltersCount);
		
		// Assert
		// Uses filterMinItemsToShow = 5
		// maxAllowedStartIndex = max(0, 5 - 3) = 2
		// result = min(2, 8) = 2
		Assert.assertEquals(2, result);
	}

	@Test
	public void testCalculateOptimalDPTInsertionIndex_MinItemsToShowZero() {
		// Arrange
		ReflectionTestUtils.setField(filterResponseTransformer, "filterMinItemsToShow", 5);
		
		FilterCategory filterCategory = new FilterCategory();
		filterCategory.setMinItemsToShow(0); // Zero value
		
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = createFilterList(6);
		filterCategory.setFilters(filters);
		
		int dptFiltersCount = 2;
		
		// Act
		int result = ReflectionTestUtils.invokeMethod(
			filterResponseTransformer, "calculateOptimalDPTInsertionIndex", filterCategory, dptFiltersCount);
		
		// Assert
		// Uses filterMinItemsToShow = 5 (since 0 is not > 0)
		// maxAllowedStartIndex = max(0, 5 - 2) = 3
		// result = min(3, 6) = 3
		Assert.assertEquals(3, result);
	}

	@Test
	public void testCalculateOptimalDPTInsertionIndex_DptCountExceedsMinItems() {
		// Arrange
		ReflectionTestUtils.setField(filterResponseTransformer, "filterMinItemsToShow", 5);
		
		FilterCategory filterCategory = new FilterCategory();
		filterCategory.setMinItemsToShow(3);
		
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = createFilterList(10);
		filterCategory.setFilters(filters);
		
		int dptFiltersCount = 5; // More than minItemsToShow
		
		// Act
		int result = ReflectionTestUtils.invokeMethod(
			filterResponseTransformer, "calculateOptimalDPTInsertionIndex", filterCategory, dptFiltersCount);
		
		// Assert
		// maxAllowedStartIndex = max(0, 3 - 5) = max(0, -2) = 0
		// result = min(0, 10) = 0
		Assert.assertEquals(0, result);
	}

	@Test
	public void testCalculateOptimalDPTInsertionIndex_EmptyFilterList() {
		// Arrange
		ReflectionTestUtils.setField(filterResponseTransformer, "filterMinItemsToShow", 5);
		
		FilterCategory filterCategory = new FilterCategory();
		filterCategory.setMinItemsToShow(5);
		
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>(); // Empty list
		filterCategory.setFilters(filters);
		
		int dptFiltersCount = 2;
		
		// Act
		int result = ReflectionTestUtils.invokeMethod(
			filterResponseTransformer, "calculateOptimalDPTInsertionIndex", filterCategory, dptFiltersCount);
		
		// Assert
		// maxAllowedStartIndex = max(0, 5 - 2) = 3
		// result = min(3, 0) = 0
		Assert.assertEquals(0, result);
	}

	@Test
	public void testCalculateOptimalDPTInsertionIndex_SmallFilterList() {
		// Arrange
		ReflectionTestUtils.setField(filterResponseTransformer, "filterMinItemsToShow", 5);
		
		FilterCategory filterCategory = new FilterCategory();
		filterCategory.setMinItemsToShow(5);
		
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = createFilterList(3); // Smaller than minItemsToShow
		filterCategory.setFilters(filters);
		
		int dptFiltersCount = 2;
		
		// Act
		int result = ReflectionTestUtils.invokeMethod(
			filterResponseTransformer, "calculateOptimalDPTInsertionIndex", filterCategory, dptFiltersCount);
		
		// Assert
		// maxAllowedStartIndex = max(0, 5 - 2) = 3
		// result = min(3, 3) = 3 (at the end of the list)
		Assert.assertEquals(3, result);
	}

	@Test
	public void testCalculateOptimalDPTInsertionIndex_SingleDptFilter() {
		// Arrange
		ReflectionTestUtils.setField(filterResponseTransformer, "filterMinItemsToShow", 5);
		
		FilterCategory filterCategory = new FilterCategory();
		filterCategory.setMinItemsToShow(5);
		
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = createFilterList(7);
		filterCategory.setFilters(filters);
		
		int dptFiltersCount = 1; // Single DPT filter
		
		// Act
		int result = ReflectionTestUtils.invokeMethod(
			filterResponseTransformer, "calculateOptimalDPTInsertionIndex", filterCategory, dptFiltersCount);
		
		// Assert
		// maxAllowedStartIndex = max(0, 5 - 1) = 4
		// result = min(4, 7) = 4
		Assert.assertEquals(4, result);
	}

	@Test
	public void testCalculateOptimalDPTInsertionIndex_LargeMinItemsToShow() {
		// Arrange
		ReflectionTestUtils.setField(filterResponseTransformer, "filterMinItemsToShow", 5);
		
		FilterCategory filterCategory = new FilterCategory();
		filterCategory.setMinItemsToShow(10); // Large value
		
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = createFilterList(6);
		filterCategory.setFilters(filters);
		
		int dptFiltersCount = 3;
		
		// Act
		int result = ReflectionTestUtils.invokeMethod(
			filterResponseTransformer, "calculateOptimalDPTInsertionIndex", filterCategory, dptFiltersCount);
		
		// Assert
		// maxAllowedStartIndex = max(0, 10 - 3) = 7
		// result = min(7, 6) = 6 (constrained by filter list size)
		Assert.assertEquals(6, result);
	}

	/**
	 * Helper method to create a list of filters for testing
	 */
	private List<com.mmt.hotels.clientgateway.response.filter.Filter> createFilterList(int count) {
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
		for (int i = 0; i < count; i++) {
			com.mmt.hotels.clientgateway.response.filter.Filter filter = new com.mmt.hotels.clientgateway.response.filter.Filter();
			filter.setFilterValue("filter_" + i);
			filter.setTitle("Filter " + i);
			filters.add(filter);
		}
		return filters;
	}

	// =====================================================================================
	// Test Cases for HTL-66283 - Unmarried Couples Filter Suppression for Single Adult
	// =====================================================================================

	/**
	 * Test that unmarried couples filter is suppressed when search is for 1 adult
	 * HTL-66283: If the search is for 1 adult, then don't show unmarried couples allowed filter
	 * 
	 * @ticket HTL-66283
	 */
	@Test
	public void testAppendContextualFiltersForMyPartner_SingleAdult_SuppressUnmarriedCouplesFilter() {
		// Given - Setup filter list with existing filters
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filterList = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter existingFilter = new com.mmt.hotels.clientgateway.response.filter.Filter();
		existingFilter.setFilterGroup("AMENITIES");
		existingFilter.setFilterValue("WIFI");
		existingFilter.setTitle("Free WiFi");
		filterList.add(existingFilter);

		// Setup HES response with unmarried couples filter
		FilterSearchMetaDataResponse filterResponseHES = dummyHESResponse();
		Map<String, List<Filter>> filterCategoryMap = new HashMap<>();
		List<Filter> popularFilters = new ArrayList<>();
		
		// Add unmarried couples filter to the response
		Filter unmarriedCouplesFilter = new Filter();
		unmarriedCouplesFilter.setFilterGroup(FilterGroup.HOUSE_RULES);
		unmarriedCouplesFilter.setFilterValue(Constants.UNMARRIED_COUPLES_ALLOWED);
		unmarriedCouplesFilter.setTitle("Unmarried Couples Allowed");
		unmarriedCouplesFilter.setCount(25);
		popularFilters.add(unmarriedCouplesFilter);
		
		// Add other filter for verification
		Filter otherFilter = new Filter();
		otherFilter.setFilterGroup(FilterGroup.AMENITIES);
		otherFilter.setFilterValue("PARKING");
		otherFilter.setTitle("Free Parking");
		otherFilter.setCount(15);
		popularFilters.add(otherFilter);
		
		filterCategoryMap.put(Constants.FILTER_POPULAR, popularFilters);
		filterResponseHES.setFilterCategoryMap(filterCategoryMap);

		// Setup experiment data
		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put(Constants.EXP_CONTEXTUAL_FILTER, "F"); // Required for contextual filters

		// Setup FilterCountRequest with single adult
		FilterCountRequest filterCountRequest = new FilterCountRequest();
		RequestDetails requestDetails = new RequestDetails();
		filterCountRequest.setRequestDetails(requestDetails);
		
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
		RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
		roomStayCandidate.setAdultCount(1); // Single adult search
		roomStayCandidates.add(roomStayCandidate);
		searchCriteria.setRoomStayCandidates(roomStayCandidates);
		filterCountRequest.setSearchCriteria(searchCriteria);

		// Setup MyPartner contextual filters
		List<com.mmt.hotels.clientgateway.response.filter.Filter> mpContextualFilters = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter mpUnmarriedCouplesFilter = new com.mmt.hotels.clientgateway.response.filter.Filter();
		mpUnmarriedCouplesFilter.setFilterGroup(Constants.HOUSE_RULES);
		mpUnmarriedCouplesFilter.setFilterValue(Constants.UNMARRIED_COUPLES_ALLOWED);
		mpContextualFilters.add(mpUnmarriedCouplesFilter);
		
		com.mmt.hotels.clientgateway.response.filter.Filter mpOtherFilter = new com.mmt.hotels.clientgateway.response.filter.Filter();
		mpOtherFilter.setFilterGroup("AMENITIES");
		mpOtherFilter.setFilterValue("PARKING");
		mpContextualFilters.add(mpOtherFilter);
		
		ReflectionTestUtils.setField(filterResponseTransformer, "mpContextualFiltersApplicable", mpContextualFilters);

		int initialFilterCount = filterList.size();

		// When - Invoke the method that should suppress unmarried couples filter for single adult
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "appendContextualFiltersForMyPartner", 
			filterList, expDataMap, filterResponseHES, Constants.FILTER_POPULAR, filterCountRequest);

		// Then - Verify unmarried couples filter is NOT added for single adult search
		Assert.assertTrue("Filter list should have increased but unmarried couples filter should be suppressed", 
			filterList.size() > initialFilterCount);

		// Verify unmarried couples filter is NOT present
		boolean unmarriedCouplesFilterPresent = filterList.stream()
			.anyMatch(filter -> Constants.HOUSE_RULES.equals(filter.getFilterGroup()) 
				&& Constants.UNMARRIED_COUPLES_ALLOWED.equals(filter.getFilterValue()));
		Assert.assertFalse("Unmarried couples filter should be suppressed for single adult search", 
			unmarriedCouplesFilterPresent);

		// Verify other valid filters are still added
		boolean otherFilterPresent = filterList.stream()
			.anyMatch(filter -> "AMENITIES".equals(filter.getFilterGroup()) 
				&& "PARKING".equals(filter.getFilterValue()));
		Assert.assertTrue("Other valid filters should still be added", otherFilterPresent);
	}

	/**
	 * Test that unmarried couples filter is shown when search is for multiple adults
	 * HTL-66283: Verify that unmarried couples filter appears for searches with more than 1 adult
	 * 
	 * @ticket HTL-66283
	 */
	@Test
	public void testAppendContextualFiltersForMyPartner_MultipleAdults_ShowUnmarriedCouplesFilter() {
		// Given - Setup filter list with existing filters
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filterList = new ArrayList<>();

		// Setup HES response with unmarried couples filter
		FilterSearchMetaDataResponse filterResponseHES = dummyHESResponse();
		Map<String, List<Filter>> filterCategoryMap = new HashMap<>();
		List<Filter> popularFilters = new ArrayList<>();
		
		// Add unmarried couples filter to the response
		Filter unmarriedCouplesFilter = new Filter();
		unmarriedCouplesFilter.setFilterGroup(FilterGroup.HOUSE_RULES);
		unmarriedCouplesFilter.setFilterValue(Constants.UNMARRIED_COUPLES_ALLOWED);
		unmarriedCouplesFilter.setTitle("Unmarried Couples Allowed");
		unmarriedCouplesFilter.setCount(25);
		popularFilters.add(unmarriedCouplesFilter);
		
		filterCategoryMap.put(Constants.FILTER_POPULAR, popularFilters);
		filterResponseHES.setFilterCategoryMap(filterCategoryMap);

		// Setup experiment data
		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put(Constants.EXP_CONTEXTUAL_FILTER, "F");

		// Setup FilterCountRequest with multiple adults
		FilterCountRequest filterCountRequest = new FilterCountRequest();
		RequestDetails requestDetails = new RequestDetails();
		filterCountRequest.setRequestDetails(requestDetails);
		
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
		RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
		roomStayCandidate.setAdultCount(2); // Multiple adults search
		roomStayCandidates.add(roomStayCandidate);
		searchCriteria.setRoomStayCandidates(roomStayCandidates);
		filterCountRequest.setSearchCriteria(searchCriteria);

		// Setup MyPartner contextual filters
		List<com.mmt.hotels.clientgateway.response.filter.Filter> mpContextualFilters = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter mpUnmarriedCouplesFilter = new com.mmt.hotels.clientgateway.response.filter.Filter();
		mpUnmarriedCouplesFilter.setFilterGroup(Constants.HOUSE_RULES);
		mpUnmarriedCouplesFilter.setFilterValue(Constants.UNMARRIED_COUPLES_ALLOWED);
		mpContextualFilters.add(mpUnmarriedCouplesFilter);
		
		ReflectionTestUtils.setField(filterResponseTransformer, "mpContextualFiltersApplicable", mpContextualFilters);

		int initialFilterCount = filterList.size();

		// When - Invoke the method that should allow unmarried couples filter for multiple adults
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "appendContextualFiltersForMyPartner", 
			filterList, expDataMap, filterResponseHES, Constants.FILTER_POPULAR, filterCountRequest);

		// Then - Verify unmarried couples filter IS added for multiple adults search
		Assert.assertEquals("Filter list should have increased by 1", initialFilterCount + 1, filterList.size());

		// Verify unmarried couples filter IS present
		boolean unmarriedCouplesFilterPresent = filterList.stream()
			.anyMatch(filter -> Constants.HOUSE_RULES.equals(filter.getFilterGroup()) 
				&& Constants.UNMARRIED_COUPLES_ALLOWED.equals(filter.getFilterValue()));
		Assert.assertTrue("Unmarried couples filter should be shown for multiple adults search", 
			unmarriedCouplesFilterPresent);
	}

	/**
	 * Test edge case: unmarried couples filter suppression when total adult count across multiple rooms is 1
	 * HTL-66283: Verify suppression works correctly when total adults across all rooms is 1
	 * 
	 * @ticket HTL-66283
	 */
	@Test
	public void testTranslateFilterValues_NullFilterResponse() throws Exception {
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("translateFilterValues", FilterSearchMetaDataResponse.class);
		method.setAccessible(true);
		method.invoke(filterResponseTransformer, (FilterSearchMetaDataResponse) null);
		
		// Assert - should not throw exception
		// No verification needed as method should handle null gracefully
	}

	@Test
	public void testTranslateFilterValues_NullFilterDataMap() throws Exception {
		// Arrange
		FilterSearchMetaDataResponse filterResponse = new FilterSearchMetaDataResponse();
		filterResponse.setFilterDataMap(null);
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("translateFilterValues", FilterSearchMetaDataResponse.class);
		method.setAccessible(true);
		method.invoke(filterResponseTransformer, filterResponse);
		
		// Assert - should not throw exception
		// No verification needed as method should handle null gracefully
	}

	@Test
	public void testTranslateFilterValues_WithValidData() throws Exception {
		// Arrange
		FilterSearchMetaDataResponse filterResponse = new FilterSearchMetaDataResponse();
		Map<FilterGroup, List<com.mmt.hotels.filter.Filter>> filterDataMap = new HashMap<>();
		
		List<com.mmt.hotels.filter.Filter> filters = new ArrayList<>();
		com.mmt.hotels.filter.Filter filter = new com.mmt.hotels.filter.Filter();
		filter.setTitle("Test Title");
		filter.setFilterGroup(FilterGroup.AMENITIES);
		filters.add(filter);
		
		filterDataMap.put(FilterGroup.AMENITIES, filters);
		filterResponse.setFilterDataMap(filterDataMap);
		
		Mockito.when(polyglotService.getTranslatedData("Test Title")).thenReturn("Translated Title");
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("translateFilterValues", FilterSearchMetaDataResponse.class);
		method.setAccessible(true);
		method.invoke(filterResponseTransformer, filterResponse);
		
		// Assert
		Assert.assertEquals("Translated Title", filter.getTitle());
	}

	@Test
	public void testGetTranslatedFilterTitle_NullFilter() throws Exception {
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("getTranslatedFilterTitle", com.mmt.hotels.filter.Filter.class);
		method.setAccessible(true);
		String result = (String) method.invoke(filterResponseTransformer, (com.mmt.hotels.filter.Filter) null);
		
		// Assert
		Assert.assertNull(result);
	}

	@Test
	public void testGetTranslatedFilterTitle_NullFilterGroup() throws Exception {
		// Arrange
		com.mmt.hotels.filter.Filter filter = new com.mmt.hotels.filter.Filter();
		filter.setTitle("Test Title");
		filter.setFilterGroup(null);
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("getTranslatedFilterTitle", com.mmt.hotels.filter.Filter.class);
		method.setAccessible(true);
		String result = (String) method.invoke(filterResponseTransformer, filter);
		
		// Assert
		Assert.assertEquals("Test Title", result);
	}

	@Test
	public void testGetTranslatedFilterTitle_WithTranslatableGroup() throws Exception {
		// Arrange
		com.mmt.hotels.filter.Filter filter = new com.mmt.hotels.filter.Filter();
		filter.setTitle("Test Title");
		filter.setFilterGroup(FilterGroup.AMENITIES);
		
		Mockito.when(polyglotService.getTranslatedData("Test Title")).thenReturn("Translated Title");
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("getTranslatedFilterTitle", com.mmt.hotels.filter.Filter.class);
		method.setAccessible(true);
		String result = (String) method.invoke(filterResponseTransformer, filter);
		
		// Assert
		Assert.assertEquals("Translated Title", result);
	}

	@Test
	public void testAddSmartFilterPill_ExperimentDisabled() throws Exception {
		// Arrange
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterPills(new ArrayList<>());
		LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
		expDataMap.put("smartFilter", "false"); // Correct experiment key
		
		FilterCountRequest filterRequest = new FilterCountRequest();
		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice("android");
		filterRequest.setDeviceDetails(deviceDetails);
		
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setFunnelSource("HOTELS");
		filterRequest.setRequestDetails(requestDetails);
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("addSmartFilterPill", 
			FilterResponse.class, LinkedHashMap.class, FilterCountRequest.class);
		method.setAccessible(true);
		method.invoke(filterResponseTransformer, filterResponse, expDataMap, filterRequest);
		
		// Assert
		Assert.assertTrue(filterResponse.getFilterPills().isEmpty());
	}

	@Test
	public void testAddSmartFilterPill_NonMobileDevice() throws Exception {
		// Arrange
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterPills(new ArrayList<>());
		LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
		expDataMap.put("smartFilter", "true"); // Correct experiment key
		
		FilterCountRequest filterRequest = new FilterCountRequest();
		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice("desktop");
		filterRequest.setDeviceDetails(deviceDetails);
		
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setFunnelSource("HOTELS");
		filterRequest.setRequestDetails(requestDetails);
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("addSmartFilterPill", 
			FilterResponse.class, LinkedHashMap.class, FilterCountRequest.class);
		method.setAccessible(true);
		method.invoke(filterResponseTransformer, filterResponse, expDataMap, filterRequest);
		
		// Assert
		Assert.assertTrue(filterResponse.getFilterPills().isEmpty());
	}

	@Test
	public void testAddSmartFilterPill_ValidConditions() throws Exception {
		// Arrange
		FilterResponse filterResponse = new FilterResponse();
		List<FilterPill> filterPills = new ArrayList<>();
		// Add a dummy pill to make the list non-empty so method doesn't return early
		FilterPill dummyPill = new FilterPill();
		dummyPill.setId("DUMMY");
		filterPills.add(dummyPill);
		filterResponse.setFilterPills(filterPills);
		
		LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
		expDataMap.put("smartFilter", "true"); // Correct experiment key
		
		FilterCountRequest filterRequest = new FilterCountRequest();
		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setBookingDevice("android"); // This should match Constants.ANDROID
		filterRequest.setDeviceDetails(deviceDetails);
		
		RequestDetails requestDetails = new RequestDetails();
		requestDetails.setFunnelSource("HOTELS"); // This should match FUNNEL_SOURCE_HOTELS
		filterRequest.setRequestDetails(requestDetails);
		
		Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("Smart Filters");
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("addSmartFilterPill", 
			FilterResponse.class, LinkedHashMap.class, FilterCountRequest.class);
		method.setAccessible(true);
		method.invoke(filterResponseTransformer, filterResponse, expDataMap, filterRequest);
		
		// Assert
		Assert.assertEquals(2, filterResponse.getFilterPills().size()); // Should have dummy + smart pill
		// Find the smart pill (not the dummy one)
		FilterPill smartPill = filterResponse.getFilterPills().stream()
			.filter(pill -> !"DUMMY".equals(pill.getId()))
			.findFirst()
			.orElse(null);
		Assert.assertNotNull(smartPill);
		Assert.assertEquals("Smart Filters", smartPill.getTitle());
		Assert.assertEquals(3, smartPill.getSequence());
	}

	@Test
	public void testFindTargetPositionForPill_EmptyList() throws Exception {
		// Arrange
		List<FilterPill> filterPills = new ArrayList<>();
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("findTargetPositionForPill", List.class);
		method.setAccessible(true);
		int result = (int) method.invoke(filterResponseTransformer, filterPills);
		
		// Assert
		Assert.assertEquals(0, result);
	}

	@Test
	public void testFindTargetPositionForPill_WithFiltersAndSort() throws Exception {
		// Arrange
		List<FilterPill> filterPills = new ArrayList<>();
		
		FilterPill sortPill = new FilterPill();
		sortPill.setId("SORT");
		sortPill.setSequence(1);
		filterPills.add(sortPill);
		
		FilterPill filtersPill = new FilterPill();
		filtersPill.setId("ALL_FILTERS");
		filtersPill.setSequence(2);
		filterPills.add(filtersPill);
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("findTargetPositionForPill", List.class);
		method.setAccessible(true);
		int result = (int) method.invoke(filterResponseTransformer, filterPills);
		
		// Assert
		Assert.assertEquals(1, result); // Should be after FILTERS pill
	}

	@Test
	public void testCanShowFilterCount_NullLocationId() throws Exception {
		// Arrange
		LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("canShowFilterCount", String.class, LinkedHashMap.class);
		method.setAccessible(true);
		boolean result = (boolean) method.invoke(filterResponseTransformer, null, expDataMap);
		
		// Assert
		Assert.assertTrue(result);
	}

	@Test
	public void testCanShowFilterCount_WithRoiCity() throws Exception {
		// Arrange
		LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
		expDataMap.put("showFilterCount", "true");
		
		// Mock roiCityList using reflection
		Field roiCityListField = FilterResponseTransformer.class.getDeclaredField("roiCityList");
		roiCityListField.setAccessible(true);
		List<String> roiCityList = Arrays.asList("CTDEL", "CTMUM");
		roiCityListField.set(filterResponseTransformer, roiCityList);
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("canShowFilterCount", String.class, LinkedHashMap.class);
		method.setAccessible(true);
		boolean result = (boolean) method.invoke(filterResponseTransformer, "CTDEL", expDataMap);
		
		// Assert
		Assert.assertFalse(result);
	}

	@Test
	public void testRemoveDuplicateFilters_EmptyList() throws Exception {
		// Arrange
		List<Filter> filterList = new ArrayList<>();
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("removeDuplicateFilters", List.class);
		method.setAccessible(true);
		@SuppressWarnings("unchecked")
		List<Filter> result = (List<Filter>) method.invoke(filterResponseTransformer, filterList);
		
		// Assert
		Assert.assertTrue(result.isEmpty());
	}

	@Test
	public void testConvertDTPWAFilterResponse_NullConfig() {
		// Act
		FilterResponse result = filterResponseTransformer.convertDTPWAFilterResponse(null, null, null, null, null, null);
		
		// Assert
		Assert.assertNull(result);
	}

	@Test
	public void testConvertDTPWAFilterResponse_EmptyFilterPages() {
		// Arrange
		FilterConfigurationV2 filterConfig = new FilterConfigurationV2();
		filterConfig.setFilterPages(new LinkedHashMap<String, com.mmt.hotels.clientgateway.businessobjects.FilterPage>());
		
		// Act
		FilterResponse result = filterResponseTransformer.convertDTPWAFilterResponse(null, filterConfig, null, null, null, null);
		
		// Assert
		Assert.assertNull(result);
	}

	@Test
	public void testAddOffersFilterPillIfApplicable_V1_NullResponse() throws Exception {
		// Arrange
		FilterResponse filterResponse = null;
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("addOffersFilterPillIfApplicable", FilterResponse.class, boolean.class);
		method.setAccessible(true);
		method.invoke(filterResponseTransformer, filterResponse, false);
		
		// Assert - should not throw exception
		// No verification needed as method should handle null gracefully
	}

	@Test
	public void testAddOffersFilterPillIfApplicable_V1_EmptyFilterPills() throws Exception {
		// Arrange
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterPills(new ArrayList<>());
		filterResponse.setFilterList(new ArrayList<>());
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("addOffersFilterPillIfApplicable", FilterResponse.class, boolean.class);
		method.setAccessible(true);
		method.invoke(filterResponseTransformer, filterResponse, false);
		
		// Assert
		Assert.assertTrue(filterResponse.getFilterPills().isEmpty());
	}

	@Test
	public void testAddFreeKidStayFilterPill_NullResponse() throws Exception {
		// Arrange
		FilterResponse filterResponse = null;
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("addFreeKidStayFilterPill", FilterResponse.class);
		method.setAccessible(true);
		method.invoke(filterResponseTransformer, filterResponse);
		
		// Assert - should not throw exception
		// No verification needed as method should handle null gracefully
	}

	@Test
	public void testAddFreeKidStayFilterPill_EmptyFilterPills() throws Exception {
		// Arrange
		FilterResponse filterResponse = new FilterResponse();
		filterResponse.setFilterPills(new ArrayList<>());
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("addFreeKidStayFilterPill", FilterResponse.class);
		method.setAccessible(true);
		method.invoke(filterResponseTransformer, filterResponse);
		
		// Assert - should not throw exception (no popular category found)
		Assert.assertTrue(filterResponse.getFilterPills().isEmpty());
	}

	@Test
	public void testBuildFilterCollection_WithValidData() throws Exception {
		// Arrange
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filterList = new ArrayList<>();
		
		com.mmt.hotels.clientgateway.response.filter.Filter filter1 = new com.mmt.hotels.clientgateway.response.filter.Filter();
		filter1.setFilterValue("WIFI");
		filterList.add(filter1);
		
		com.mmt.hotels.clientgateway.response.filter.Filter filter2 = new com.mmt.hotels.clientgateway.response.filter.Filter();
		filter2.setFilterValue("POOL");
		filterList.add(filter2);
		
		String funnelSource = "HOTELS";
		
		// Mock amenitiesCategoryConfigPolyGlot using reflection
		Field amenitiesCategoryConfigField = FilterResponseTransformer.class.getDeclaredField("amenitiesCategoryConfigPolyGlot");
		amenitiesCategoryConfigField.setAccessible(true);
		
		Map<String, Map<String, AmenityCategory>> amenitiesCategoryConfig = new HashMap<>();
		Map<String, AmenityCategory> hotelCategories = new HashMap<>();
		
		AmenityCategory category = new AmenityCategory();
		category.setTitle("Essential Amenities");
		category.setAmenities(Arrays.asList("WIFI", "POOL"));
		hotelCategories.put("ESSENTIAL", category);
		
		amenitiesCategoryConfig.put("HOTELS", hotelCategories);
		amenitiesCategoryConfigField.set(filterResponseTransformer, amenitiesCategoryConfig);
		
		Mockito.when(polyglotService.getTranslatedData("Essential Amenities")).thenReturn("Essential Amenities");
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("buildFilterCollection", List.class, String.class);
		method.setAccessible(true);
		@SuppressWarnings("unchecked")
		List<FilterCollection> result = (List<FilterCollection>) method.invoke(filterResponseTransformer, filterList, funnelSource);
		
		// Assert
		Assert.assertFalse(result.isEmpty());
		Assert.assertEquals("Essential Amenities", result.get(0).getCollectionName());
		Assert.assertEquals(2, result.get(0).getFilters().size());
	}

	@Test
	public void testBuildBatchFilter_ValidInput() throws Exception {
		// Arrange
		com.mmt.hotels.filter.Filter batchFilter = new com.mmt.hotels.filter.Filter();
		batchFilter.setFilterValue("COLLECTION1");
		batchFilter.setCount(10);
		batchFilter.setIconUrl("http://example.com/icon.jpg");
		batchFilter.setTitle("Collection 1");
		batchFilter.setSubTitle("Popular Collection");
		batchFilter.setStaticBatch(true);
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("buildBatchFilter", com.mmt.hotels.filter.Filter.class);
		method.setAccessible(true);
		com.mmt.hotels.clientgateway.response.filter.Filter result = 
			(com.mmt.hotels.clientgateway.response.filter.Filter) method.invoke(filterResponseTransformer, batchFilter);
		
		// Assert
		Assert.assertEquals("COLLECTION1", result.getFilterValue());
		Assert.assertEquals(Integer.valueOf(10), result.getCount());
		Assert.assertEquals("http://example.com/icon.jpg", result.getIconUrl());
		Assert.assertEquals("Collection 1", result.getTitle());
		Assert.assertEquals("Popular Collection", result.getSubTitle());
		Assert.assertTrue(result.getStaticBatch());
	}

	@Test
	public void testSortDistanceCategoryFilter_EmptyList() {
		// Act
		List<com.mmt.hotels.filter.Filter> result = 
			FilterResponseTransformer.sortDistanceCategoryFilter(new ArrayList<>());
		
		// Assert
		Assert.assertTrue(result.isEmpty());
	}

	@Test
	public void testSortDistanceCategoryFilter_NullList() {
		// Act
		List<com.mmt.hotels.filter.Filter> result = 
			FilterResponseTransformer.sortDistanceCategoryFilter(null);
		
		// Assert
		Assert.assertTrue(result.isEmpty());
	}

	@Test
	public void testSortDistanceCategoryFilter_ValidFilters() {
		// Arrange
		List<com.mmt.hotels.filter.Filter> filterList = new ArrayList<>();
		
		com.mmt.hotels.filter.Filter filter1 = new com.mmt.hotels.filter.Filter();
		filter1.setFilterValue("POI1#wd_1000-1000");
		filterList.add(filter1);
		
		com.mmt.hotels.filter.Filter filter2 = new com.mmt.hotels.filter.Filter();
		filter2.setFilterValue("POI1#dd_500-500");
		filterList.add(filter2);
		
		// Mock distanceSortingModesPriorityMap using reflection
		try {
			Field priorityMapField = FilterResponseTransformer.class.getDeclaredField("distanceSortingModesPriorityMap");
			priorityMapField.setAccessible(true);
			Map<String, Integer> priorityMap = new HashMap<>();
			priorityMap.put("wd", 1);
			priorityMap.put("dd", 2);
			priorityMapField.set(null, priorityMap);
		} catch (Exception e) {
			// Field might not be accessible, continue with test
		}
		
		// Act
		List<com.mmt.hotels.filter.Filter> result = 
			FilterResponseTransformer.sortDistanceCategoryFilter(filterList);
		
		// Assert
		Assert.assertFalse(result.isEmpty());
	}

	@Test
	public void testCreateDefaultBedRoomCountFilter_ValidInput() throws Exception {
		// Arrange
		String filterGroup = "BEDROOM_COUNT";
		Integer adults = 4;
		Integer children = 2;
		Map<String, String> expDataMap = new HashMap<>();
		
		Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("Bedroom");
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("createDefaultBedRoomCountFilter", 
			String.class, Integer.class, Integer.class, Map.class);
		method.setAccessible(true);
		@SuppressWarnings("unchecked")
		List<com.mmt.hotels.clientgateway.response.filter.Filter> result = 
			(List<com.mmt.hotels.clientgateway.response.filter.Filter>) method.invoke(
				filterResponseTransformer, filterGroup, adults, children, expDataMap);
		
		// Assert
		Assert.assertFalse(result.isEmpty());
		// Should have filters for reasonable bedroom counts based on guest count
	}

	@Test
	public void testCreateDefaultBedRoomCountFilter_LowGuestCount() throws Exception {
		// Arrange
		String filterGroup = "BEDROOM_COUNT";
		Integer adults = 1;
		Integer children = 0;
		Map<String, String> expDataMap = new HashMap<>();
		
		// Act - use reflection to test private method
		Method method = FilterResponseTransformer.class.getDeclaredMethod("createDefaultBedRoomCountFilter", 
			String.class, Integer.class, Integer.class, Map.class);
		method.setAccessible(true);
		@SuppressWarnings("unchecked")
		List<com.mmt.hotels.clientgateway.response.filter.Filter> result = 
			(List<com.mmt.hotels.clientgateway.response.filter.Filter>) method.invoke(
				filterResponseTransformer, filterGroup, adults, children, expDataMap);
		
		// Assert
		Assert.assertTrue(result.isEmpty()); // Should return empty for low guest count
	}

	@Test
	public void testAppendContextualFiltersForMyPartner_MultipleRoomsSingleAdultTotal_SuppressUnmarriedCouplesFilter() {
		// Given - Setup filter list
		List<com.mmt.hotels.clientgateway.response.filter.Filter> filterList = new ArrayList<>();

		// Setup HES response with unmarried couples filter
		FilterSearchMetaDataResponse filterResponseHES = dummyHESResponse();
		Map<String, List<Filter>> filterCategoryMap = new HashMap<>();
		List<Filter> popularFilters = new ArrayList<>();
		
		Filter unmarriedCouplesFilter = new Filter();
		unmarriedCouplesFilter.setFilterGroup(FilterGroup.HOUSE_RULES);
		unmarriedCouplesFilter.setFilterValue(Constants.UNMARRIED_COUPLES_ALLOWED);
		unmarriedCouplesFilter.setTitle("Unmarried Couples Allowed");
		unmarriedCouplesFilter.setCount(25);
		popularFilters.add(unmarriedCouplesFilter);
		
		filterCategoryMap.put(Constants.FILTER_POPULAR, popularFilters);
		filterResponseHES.setFilterCategoryMap(filterCategoryMap);

		// Setup experiment data
		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put(Constants.EXP_CONTEXTUAL_FILTER, "F");

		// Setup FilterCountRequest with multiple rooms but total adult count = 1
		FilterCountRequest filterCountRequest = new FilterCountRequest();
		RequestDetails requestDetails = new RequestDetails();
		filterCountRequest.setRequestDetails(requestDetails);
		
		SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
		List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
		
		// Room 1: 1 adult
		RoomStayCandidate room1 = new RoomStayCandidate();
		room1.setAdultCount(1);
		roomStayCandidates.add(room1);
		
		// Room 2: 0 adults (edge case)
		RoomStayCandidate room2 = new RoomStayCandidate();
		room2.setAdultCount(0);
		roomStayCandidates.add(room2);
		
		searchCriteria.setRoomStayCandidates(roomStayCandidates);
		filterCountRequest.setSearchCriteria(searchCriteria);

		// Setup MyPartner contextual filters
		List<com.mmt.hotels.clientgateway.response.filter.Filter> mpContextualFilters = new ArrayList<>();
		com.mmt.hotels.clientgateway.response.filter.Filter mpUnmarriedCouplesFilter = new com.mmt.hotels.clientgateway.response.filter.Filter();
		mpUnmarriedCouplesFilter.setFilterGroup(Constants.HOUSE_RULES);
		mpUnmarriedCouplesFilter.setFilterValue(Constants.UNMARRIED_COUPLES_ALLOWED);
		mpContextualFilters.add(mpUnmarriedCouplesFilter);
		
		ReflectionTestUtils.setField(filterResponseTransformer, "mpContextualFiltersApplicable", mpContextualFilters);

		int initialFilterCount = filterList.size();

		// When - Invoke the method
		ReflectionTestUtils.invokeMethod(filterResponseTransformer, "appendContextualFiltersForMyPartner", 
			filterList, expDataMap, filterResponseHES, Constants.FILTER_POPULAR, filterCountRequest);

		// Then - Verify unmarried couples filter is suppressed even with multiple rooms
		Assert.assertEquals("Filter list should not increase when unmarried couples filter is suppressed", 
			initialFilterCount, filterList.size());

		// Verify unmarried couples filter is NOT present
		boolean unmarriedCouplesFilterPresent = filterList.stream()
			.anyMatch(filter -> Constants.HOUSE_RULES.equals(filter.getFilterGroup()) 
				&& Constants.UNMARRIED_COUPLES_ALLOWED.equals(filter.getFilterValue()));
		Assert.assertFalse("Unmarried couples filter should be suppressed when total adults across all rooms is 1", 
			unmarriedCouplesFilterPresent);
	}
}
