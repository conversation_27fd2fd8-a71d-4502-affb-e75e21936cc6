package com.mmt.hotels.clientgateway.transformer.response;

import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.request.RoomInfoRequest;
import com.mmt.hotels.clientgateway.response.BNPLDetails;
import com.mmt.hotels.clientgateway.response.RoomInfoResponse;
import com.mmt.hotels.clientgateway.response.staticdetail.Image360.View360Image;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.flyfish.RoomSummary;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.staticdata.CategoryInfo;
import com.mmt.hotels.model.response.staticdata.Image360.Image360;
import com.mmt.hotels.model.response.staticdata.RuleTableInfo;
import com.mmt.hotels.model.response.txn.FlexiCancelAddOnInfo;
import com.mmt.hotels.model.response.txn.PersistanceMultiRoomResponseEntity;
import org.apache.commons.io.FileUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.util.*;

import com.mmt.hotels.model.response.staticdata.HouseRules;
import com.mmt.hotels.clientgateway.response.rooms.ExtraBedPolicy;
import com.mmt.hotels.model.response.txn.PersistedTariffInfo;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicy;

@RunWith(MockitoJUnitRunner.class)
public class RoomInfoResponseTransformerTest {

    @InjectMocks
    private RoomInfoResponseTransformer roomInfoResponseTransformer;

    @InjectMocks
    CommonResponseTransformer commonResponseTransformer;

    @InjectMocks
    private Utility utility;

    @Spy
    private DateUtil dateUtil;

    @Mock
    private PolyglotService polyglotService;

    @Before
    public void setup() {
        commonResponseTransformer = Mockito.spy(new CommonResponseTransformer());
        utility = Mockito.spy(new Utility());
        MockitoAnnotations.initMocks(this);
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenAnswer(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                return invocationOnMock.getArgument(0);
            }
        });
        ReflectionTestUtils.setField(roomInfoResponseTransformer, "dateUtil", dateUtil);
        ReflectionTestUtils.setField(roomInfoResponseTransformer, "mealPlanMapPolyglot", new HashMap<>());
        ReflectionTestUtils.setField(roomInfoResponseTransformer, "view360IconUrl", "www.sample360Url.com");
    }

    @Test
    public void testTransformRoomInfoResponse() throws IOException {
        PersistanceMultiRoomResponseEntity txnDataEntity = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:review/txndata.json")),
                PersistanceMultiRoomResponseEntity.class);
        txnDataEntity.getPersistedData().getHotelList().get(0).getTariffInfoList().get(0).getRoomDetails().setRoomSummary(new RoomSummary());
        txnDataEntity.getPersistedData().setIsExtraAdultChild(false);
        RoomInfoRequest request = new RoomInfoRequest();
        request.setRoomCode("2312");
        request.setTxnKey("123test");
        request.setRatePlanCode("990000630470:MSE:1120:MSE:INGO");
//        Mockito.when(commonResponseTransformer.buildBNPLDetails(true, null, null, null, null, true, false, null, Mockito.anyDouble())).thenReturn(new BNPLDetails());
        RoomInfoResponse response  = roomInfoResponseTransformer.transformRoomInfoResponse(request,txnDataEntity);
        Assert.assertNotNull(response.getBeds());
        Assert.assertNotNull(response.getCancellationPolicy());
        Assert.assertNotNull(response.getCancellationTimeline());
        Assert.assertNotNull(response.getHighlightedAmenities());
        Assert.assertNotNull(response.getImages());
        Assert.assertNotNull(response.getAmenities());
    }

    @Test
    public void testTransformRoomInfoResponses() throws IOException {
        PersistanceMultiRoomResponseEntity txnDataEntity = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:roomInfo/txndata.json")),
                PersistanceMultiRoomResponseEntity.class);
        RoomInfoRequest request = new RoomInfoRequest();
        request.setRoomCode("968752");
        request.setTxnKey("7a027853-1002-4c0f-973a-588155ca7b39");
        request.setRatePlanCode("K1RV^^^OGAD12:MSE:1121:MSE:DERBY_DOORWAY");
        txnDataEntity.getPersistedData().getCancellationTimeline().setCancellationPolicyTimelineList(new ArrayList<>());
        txnDataEntity.getPersistedData().getCancellationTimeline().getCancellationPolicyTimelineList().add(new CancellationPolicyTimeline());
        txnDataEntity.getPersistedData().setFlexiCancelAddOnInfo(new FlexiCancelAddOnInfo());
        txnDataEntity.getPersistedData().getFlexiCancelAddOnInfo().setFlexiDetailBottomSheet(new FlexiDetailBottomSheet());
        txnDataEntity.getPersistedData().getFlexiCancelAddOnInfo().getFlexiDetailBottomSheet().setSelected(new Selected());
        txnDataEntity.getPersistedData().getFlexiCancelAddOnInfo().getFlexiDetailBottomSheet().getSelected().setSubTitle("test");
        txnDataEntity.getPersistedData().setIsExtraAdultChild(false);
        //Mockito.when(commonResponseTransformer.buildBNPLDetails(true, null, null)).thenReturn(new BNPLDetails());
        RoomInfoResponse response  = roomInfoResponseTransformer.transformRoomInfoResponses(request,txnDataEntity);
        Assert.assertNotNull(response.getRoomInfo());
        Assert.assertNotNull(response.getRoomInfo().getHighlightedAmenities());
        Assert.assertNotNull(response.getRoomInfo().getBeds());
        Assert.assertNotNull(response.getRoomInfo().getImages());
        Assert.assertNotNull(response.getRoomInfo().getRoomName());
        Assert.assertNotNull(response.getRoomInfo().getRoomSize());
        Assert.assertNotNull(response.getRoomInfo().getRatePlans());
        Assert.assertNotNull(response.getRoomInfo().getRatePlans().get(0).getCancellationTimeline());
        Assert.assertNotNull(response.getRoomInfo().getRatePlans().get(0).getCancellationPolicy());
        Assert.assertNotNull(response.getRoomInfo().getRatePlans().get(0).getInclusionsList());
        Assert.assertNotNull(response.getRoomInfo().getRatePlans().get(0).getTariffs());

    }

    @Test
    public void shouldReturnView360ImageWhenImage360ListIsNotEmpty() {
        // Given
        List<Image360> image360List = null;
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.CTA_360_TEXT)).thenReturn("Sample Text");
        // When
        View360Image result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer,"buildView360Images",image360List);
        // Then
        Assert.assertNull(result);

        image360List = new ArrayList<>();
        Image360 image360 = new Image360();
        image360.setImageUrl("http://www.sample.com");
        image360.setThumbnail("http://www.thumbnail.com");
        image360List.add(image360);

        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.CTA_360_TEXT)).thenReturn("Sample Text");
        // When
        result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer,"buildView360Images",image360List);
        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals("Sample Text", result.getCtaText());
        Assert.assertEquals("www.sample360Url.com", result.getCtaIcon());
        Assert.assertEquals(image360List, result.getImages());
        Assert.assertEquals("http://www.sample.com", result.getImages().get(0).getImageUrl());
        Assert.assertEquals("http://www.thumbnail.com", result.getImages().get(0).getThumbnail());
    }

    @Test
    public void getBlackInclusions_shouldReturnOnlyBlackInclusionsWhenListContainsMultipleInclusions() {
        List<Inclusion> inclusionList = new ArrayList<>();
        Inclusion inclusion1 = new Inclusion();
        inclusion1.setSegmentIdentifier(Constants.BLACK_INCLUSION_IDENTIFIER);
        Inclusion inclusion2 = new Inclusion();
        inclusion2.setSegmentIdentifier("OTHER_IDENTIFIER");
        inclusionList.add(inclusion1);
        inclusionList.add(inclusion2);

        List<Inclusion> result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getBlackInclusions", inclusionList);

        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
        Assert.assertEquals(Constants.BLACK_INCLUSION_IDENTIFIER, result.get(0).getSegmentIdentifier());
    }

    @Test
    public void getExtraBedPolicy_shouldReturnNullWhenRuleTableInfoIsNull() {
        HouseRules houseRules = new HouseRules();
        CategoryInfo categoryInfo = new CategoryInfo();
        categoryInfo.setId("EXTRA_BED_POLICY");
        categoryInfo.setCategoryDesc("Extra bed policy description");
        categoryInfo.setCategoryHeading("Extra Bed Policy");
        categoryInfo.setRuleDesc(Collections.singletonList("Rules for extra bed"));
        houseRules.setCategoryInfoList(Collections.singletonList(categoryInfo));
        ExtraBedPolicy result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getExtraBedPolicy", houseRules);
        Assert.assertNull(result);
    }

    @Test
    public void getBnplDetails_shouldReturnBnplDetailsWhenDisplayFareIsValid() {
        DisplayFare displayFare = new DisplayFare();
        displayFare.setIsBNPLApplicable(true);
        displayFare.setBnplPersuasionMsg("Persuasion Message");
        displayFare.setBnplPolicyText("Policy Text");
        displayFare.setOriginalBNPL(true);

        BNPLDetails result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getBnplDetails", displayFare);

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isBnplApplicable());
        Assert.assertEquals("Persuasion Message", result.getBnplPersuasionMsg());
        Assert.assertEquals("Policy Text", result.getBnplPolicyText());
    }

    @Test
    public void getConfirmationPolicy_shouldReturnMostRestrictedPolicyWhenPresent() {
        List<PersistedTariffInfo> tariffs = new ArrayList<>();
        PersistedTariffInfo tariff1 = new PersistedTariffInfo();
        RatePolicy ratePolicy1 = new RatePolicy();
        ratePolicy1.setMostRestrictive(Constants.MOST_RESTRICTED_POLICY);
        tariff1.setConfirmationPolicy(ratePolicy1);

        PersistedTariffInfo tariff2 = new PersistedTariffInfo();
        RatePolicy ratePolicy2 = new RatePolicy();
        ratePolicy2.setMostRestrictive("OTHER_POLICY");
        tariff2.setConfirmationPolicy(ratePolicy2);

        tariffs.add(tariff1);
        tariffs.add(tariff2);

        RatePolicy result = ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "getConfirmationPolicy", tariffs);

        Assert.assertNotNull(result);
        Assert.assertEquals(Constants.MOST_RESTRICTED_POLICY, result.getMostRestrictive());
    }

    @Test
    public void updateCancelPolicyDescription_shouldSetDescriptionWhenPenaltyDescriptionExists() {
        BookedCancellationPolicy bookedCancellationPolicy = new BookedCancellationPolicy();
        List<CancelPenalty> cancelPenalties = new ArrayList<>();
        CancelPenalty cancelPenalty = new CancelPenalty();
        Penalty penaltyDescription = new Penalty();
        penaltyDescription.setDescription("Cancellation is non-refundable.");
        cancelPenalty.setPenaltyDescription(penaltyDescription);
        cancelPenalties.add(cancelPenalty);

        ReflectionTestUtils.invokeMethod(roomInfoResponseTransformer, "updateCancelPolicyDescription", bookedCancellationPolicy, cancelPenalties);

        Assert.assertNotNull(bookedCancellationPolicy.getDescription());
        Assert.assertEquals("Cancellation is non-refundable.", bookedCancellationPolicy.getDescription());
    }

    @Test
    public void testTransformRoomInfoResponsesWithImageDetailsAndSharedSpaces() throws IOException {
        // Arrange
        PersistanceMultiRoomResponseEntity txnDataEntity = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:roomInfo/txndata.json")),
                PersistanceMultiRoomResponseEntity.class);

        // Create shared space data
        com.mmt.hotels.model.response.staticdata.SpaceData sharedSpaceData = new com.mmt.hotels.model.response.staticdata.SpaceData();
        sharedSpaceData.setDescriptive(Arrays.asList("Common lounge and swimming pool area shared with other guests"));
        txnDataEntity.getPersistedData().setSharedSpace(sharedSpaceData);

        // Setup a proper room with image details
        RoomInfoRequest request = new RoomInfoRequest();
        request.setRoomCode("968752");
        request.setTxnKey("7a027853-1002-4c0f-973a-588155ca7b39");
        request.setRatePlanCode("K1RV^^^OGAD12:MSE:1121:MSE:DERBY_DOORWAY");

        // Ensure the room has images
        LinkedList<String> roomImages = new LinkedList<>();
        roomImages.add("//path/to/image1.jpg");
        roomImages.add("//path/to/image2.jpg");
        txnDataEntity.getPersistedData().getHotelList().get(0).getTariffInfoList().get(0).getRoomDetails().setImages(roomImages);

        // Add VideoInfo
        List<com.mmt.hotels.model.response.staticdata.VideoInfo> videoInfoList = new ArrayList<>();
        com.mmt.hotels.model.response.staticdata.VideoInfo videoInfo = new com.mmt.hotels.model.response.staticdata.VideoInfo();
        videoInfo.setUrl("https://example.com/video.mp4");
        videoInfo.setThumbnailUrl("https://example.com/thumbnail.jpg");
        videoInfo.setText("Room video tour");
        videoInfoList.add(videoInfo);
        txnDataEntity.getPersistedData().getHotelList().get(0).getTariffInfoList().get(0).getRoomDetails().setRoomLevelVideos(videoInfoList);

        // Add Image360 data
        List<Image360> image360List = new ArrayList<>();
        Image360 image360 = new Image360();
        image360.setImageUrl("https://example.com/360-image.jpg");
        image360.setThumbnail("https://example.com/360-thumbnail.jpg");
        image360List.add(image360);
        txnDataEntity.getPersistedData().getHotelList().get(0).getTariffInfoList().get(0).getRoomDetails().setImage360List(image360List);

        // Set All Inclusive flag
        txnDataEntity.getPersistedData().getHotelList().get(0).getTariffInfoList().get(0).setAllInclusiveRate(true);
        txnDataEntity.getPersistedData().setExpData(new HashMap<>());
        txnDataEntity.getPersistedData().getExpData().put(Constants.ALL_INCLUSIVE_PLAN_EXPERIMENT, "true");
        
        // Act
        RoomInfoResponse response = roomInfoResponseTransformer.transformRoomInfoResponses(request, txnDataEntity);

        // Assert
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getRoomInfo());
        
        // Verify images
        Assert.assertNotNull(response.getRoomInfo().getImages());
        Assert.assertEquals(2, response.getRoomInfo().getImages().size());
        Assert.assertTrue(response.getRoomInfo().getImages().get(0).startsWith("https:"));
        
        // Verify media
        Assert.assertNotNull(response.getRoomInfo().getMedia());
        Assert.assertEquals(3, response.getRoomInfo().getMedia().size());
        Assert.assertEquals(Constants.VIDEO_TYPE, response.getRoomInfo().getMedia().get(0).getMediaType());
        Assert.assertEquals(Constants.IMAGE_TYPE, response.getRoomInfo().getMedia().get(1).getMediaType());
        
        // Verify 360 images
        Assert.assertNotNull(response.getRoomInfo().getView360());
        Assert.assertEquals(1, response.getRoomInfo().getView360().getImages().size());
        Assert.assertEquals("https://example.com/360-image.jpg", response.getRoomInfo().getView360().getImages().get(0).getImageUrl());
        
        // Verify shared spaces
        Assert.assertNotNull(response.getSharedSpaces());
        Assert.assertEquals("Common lounge and swimming pool area shared with other guests", response.getSharedSpaces().getDescriptive().get(0));
        
        // Verify all-inclusive data
        Assert.assertTrue(response.getRoomInfo().getRatePlans().get(0).isAllInclusiveRate());
    }

}
