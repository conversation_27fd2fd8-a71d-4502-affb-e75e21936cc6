package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.LinkedRatePlanStyle;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.dayuse.DayUseRoomsRequest;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.availrooms.DoubleBlackInfo;
import com.mmt.hotels.clientgateway.response.dayuse.Slot;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseRoomsResponse;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseSlotPlan;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.moblanding.SupportDetails;
import com.mmt.hotels.clientgateway.response.rooms.*;
import com.mmt.hotels.clientgateway.response.rooms.StayDetail;
import com.mmt.hotels.clientgateway.response.staticdetail.PrimaryOffer;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.android.SearchRoomsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.enums.LuckyUserContext;
import com.mmt.hotels.model.response.dayuse.MissingSlotDetail;
import com.mmt.hotels.model.response.emi.NoCostEmiDetails;
import com.mmt.hotels.model.response.flyfish.RoomSummary;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.LinkedRate;
import com.mmt.hotels.model.response.pricing.RatePlan;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import com.mmt.hotels.model.response.pricing.jsonviews.RTBPreApprovedCard;
import com.mmt.hotels.model.response.staticdata.*;
import com.mmt.hotels.model.response.staticdata.MediaData;
import com.mmt.hotels.model.response.staticdata.Space;
import com.mmt.hotels.model.response.staticdata.SpaceData;
import com.mmt.hotels.model.response.staticdata.Image360.Image360;
import com.mmt.hotels.pojo.response.CampaignPojo;
import com.mmt.model.Facility;
import com.mmt.model.FacilityGroup;
import com.mmt.model.RoomInfo;
import com.mmt.model.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.runners.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDate;
import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.HIGH_DEMAND_PERSUASION;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.times;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class SearchRoomsResponseTransformerAndroidTest {

    private static final String CORP_ALIAS = "TEST_ORGANIZATION";

    @InjectMocks
    SearchRoomsResponseTransformerAndroid  searchRoomsResponseTransformerAndroid;

    @InjectMocks
    DateUtil dateUtil;

    @Mock
    CommonResponseTransformer commonResponseTransformer;

    @InjectMocks
    private Utility utility;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    MetricAspect metricAspect;

    @Mock
    private MobConfigHelper mobConfigHelper;

    @Mock
    SearchRoomsResponseTransformer searchRoomsResponseTransformer;

    @Mock
    CommonConfigConsul commonConfigConsul;

    ObjectMapperUtil objectMapperUtil = new ObjectMapperUtil();

    @Before
    public void setup() {
        commonResponseTransformer = Mockito.spy(new CommonResponseTransformer());
        utility = Mockito.spy(new Utility());
        MockitoAnnotations.initMocks(this);
        when(polyglotService.getTranslatedData(Mockito.anyString())).thenAnswer(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                return invocationOnMock.getArgument(0);
            }
        });
        Map<String,Map<String, Map<String,String>>> ratePlanNameMap = new HashMap<>();
        ratePlanNameMap.put(Constants.DEFAULT,new HashMap<>());
        ratePlanNameMap.get(Constants.DEFAULT).put(Constants.CANCELLATION_TYPE_FC,new HashMap<>());
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_FC).put(Constants.SELLABLE_ROOM_TYPE,"{NR}");
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_FC).put(Constants.SELLABLE_BED_TYPE,"{NR}");
        ratePlanNameMap.get(Constants.DEFAULT).put(Constants.CANCELLATION_TYPE_NR,new HashMap<>());
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_NR).put(Constants.SELLABLE_ROOM_TYPE,"{NR}");
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_NR).put(Constants.SELLABLE_BED_TYPE,"{NR}");

        ReflectionTestUtils.setField(utility, "ratePlanNameMap", ratePlanNameMap);
        ReflectionTestUtils.setField(utility, "ratePlanNameMapRedesign", ratePlanNameMap);
        ReflectionTestUtils.setField(utility,"apLimitForInclusionIcons",1);
        List<String> mealPlanCodeList = Arrays.asList("AP", "CP");
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid,"mealPlanCodeList",mealPlanCodeList);
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid, "rtbCardConfigs", new HashMap<>());
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid, "corpPreferredRateSegmentId", "1135");
        ReflectionTestUtils.setField(searchRoomsResponseTransformer, "view360IconUrl", "www.sample360Url.com");
        ReflectionTestUtils.setField(searchRoomsResponseTransformer, "callToBookIconUrl", "icon url");
        ReflectionTestUtils.setField(searchRoomsResponseTransformer, "callToBookTitle", "title");
        ReflectionTestUtils.setField(searchRoomsResponseTransformer, "callToBookOption", "option");

        ObjectMapper mapper = new ObjectMapper();
        mapper.writerWithView(PIIView.External.class);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
        MissingSlotDetail missingSlotDetails = new MissingSlotDetail();
        missingSlotDetails.setDuration(new HashSet<>());
        missingSlotDetails.getDuration().add(6);
        ReflectionTestUtils.setField(searchRoomsResponseTransformer, "missingSlotDetails", missingSlotDetails);
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid, "highDemandPersuasionColor", "#EC2127");
        Mockito.when(commonResponseTransformer.buildEmiPlanDetails(Mockito.any())).thenReturn(new EMIPlanDetail());

           LinkedRatePlanStyle linkedRatePlanStyle = new LinkedRatePlanStyle();
            linkedRatePlanStyle.setStart("startValue");
            linkedRatePlanStyle.setEnd("endValue");
            linkedRatePlanStyle.setCenter("centerValue");
            linkedRatePlanStyle.setDirection("directionValue");
            linkedRatePlanStyle.setAngle("angleValue");
            ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid, "linkedRatePlanStyle", linkedRatePlanStyle);

        }

    @Test
    public void testConvertSearchRoomsResponse_suppress_rate_plan(){
        Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(Mockito.any(),Mockito.any(),Mockito.anyLong());
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), "ANDROID");
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid,"mealplanFilterEnable",true);

        Map<String,Map<String,List<String>>> supplierToRateSegmentMapping = new HashMap<>();
        supplierToRateSegmentMapping.put(Constants.PACKAGE_RATE_KEY,new HashMap<>());
        supplierToRateSegmentMapping.put(Constants.TC_CLAUSE_KEY,new HashMap<>());
        supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).put("EPXX0034",Arrays.asList("1180"));
        supplierToRateSegmentMapping.get(Constants.TC_CLAUSE_KEY).put("EPXX0034",Arrays.asList("1180"));
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid,"supplierToRateSegmentMapping",supplierToRateSegmentMapping);

        HotelRates hotelRates = new HotelRates();
        hotelRates.setIsExtraAdultChild(false);
        hotelRates.setBnplBaseAmount(10d);
        hotelRates.setBlackEligible(false);
        hotelRates.setIsBNPLAvailable(false);
        hotelRates.setPahWalletApplicable(false);
        hotelRates.setPnAvlbl(true);
        hotelRates.setPanCardRequired(true);
        hotelRates.setShowFcBanner(true);
        hotelRates.setSoldOut(false);
        hotelRates.setBreakFast(true);
        hotelRates.setFreeCancellation(true);
        hotelRates.setBreakFastAvailable(true);
        hotelRates.setFreeCancellationAvailable(true);
        hotelRates.setPAHTariffAvailable(true);

        hotelRates.setRecommendedRoomTypeDetails(new
                RoomTypeDetails());
        hotelRates.getRecommendedRoomTypeDetails().setTotalDisplayFare(new DisplayFare());

        DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
        displayPriceBreakDown.setDisplayPrice(12d);
        displayPriceBreakDown.setDisplayPriceAlternateCurrency(12d);
        displayPriceBreakDown.setNonDiscountedPrice(12d);
        displayPriceBreakDown.setSavingPerc(5.05);
        displayPriceBreakDown.setBasePrice(13.05);
        displayPriceBreakDown.setHotelTax(4d);
        displayPriceBreakDown.setMmtDiscount(1d);
        displayPriceBreakDown.setCdfDiscount(1d);
        displayPriceBreakDown.setWallet(12d);
        displayPriceBreakDown.setPricingKey("key");
        displayPriceBreakDown.setCouponInfo(new BestCoupon());
        displayPriceBreakDown.getCouponInfo().setDescription("Coupon");
        displayPriceBreakDown.getCouponInfo().setCouponCode("code");
        displayPriceBreakDown.getCouponInfo().setSpecialPromoCoupon(false);
        displayPriceBreakDown.getCouponInfo().setType("promotional");
        displayPriceBreakDown.getCouponInfo().setDiscountAmount(100.0);
        HashMap<String,String> supplierDealsDetailsMap = new HashMap<>();
        supplierDealsDetailsMap.put(DEAL,EARLY_BIRD);
        supplierDealsDetailsMap.put(DISCOUNT,"0.5");
        supplierDealsDetailsMap.put(EXPIRY,"*********");
        displayPriceBreakDown.setSupplierDealsDetailMap(supplierDealsDetailsMap);

        NoCostEmiDetails noCostEmiDetails = new NoCostEmiDetails();
        noCostEmiDetails.setEmiAmount(10000);
        noCostEmiDetails.setTenure(3);
        noCostEmiDetails.setCouponCode("code");
        displayPriceBreakDown.setNoCostEmiDetailsList(Collections.singletonList(noCostEmiDetails));

        List<DisplayPriceBreakDown> displayPriceBreakDownList = new ArrayList<>();
        displayPriceBreakDownList.add(new DisplayPriceBreakDown());
        displayPriceBreakDownList.get(0).setDisplayPrice(12d);
        displayPriceBreakDownList.get(0).setDisplayPriceAlternateCurrency(12d);
        displayPriceBreakDownList.get(0).setNonDiscountedPrice(12d);
        displayPriceBreakDownList.get(0).setSavingPerc(5.05);
        displayPriceBreakDownList.get(0).setBasePrice(13.05);
        displayPriceBreakDownList.get(0).setHotelTax(4d);
        displayPriceBreakDownList.get(0).setMmtDiscount(1d);
        displayPriceBreakDownList.get(0).setCdfDiscount(1d);
        displayPriceBreakDownList.get(0).setWallet(12d);
        displayPriceBreakDownList.get(0).setPricingKey("key");
        displayPriceBreakDownList.get(0).setCouponInfo(new BestCoupon());
        displayPriceBreakDownList.get(0).getCouponInfo().setDescription("Coupon");
        displayPriceBreakDownList.get(0).getCouponInfo().setCouponCode("code");
        displayPriceBreakDownList.get(0).getCouponInfo().setSpecialPromoCoupon(false);
        displayPriceBreakDownList.get(0).getCouponInfo().setType("promotional");
        displayPriceBreakDownList.get(0).getCouponInfo().setDiscountAmount(100.0);

        hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);

        hotelRates.getRecommendedRoomTypeDetails().setRoomType(new HashMap<String, RoomType>());
        hotelRates.getRecommendedRoomTypeDetails().setOccupancyDetails(new OccupancyDetails());
        hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails().setAdult(2);

        RoomType roomType = new RoomType();
        roomType.setRoomTypeCode("abc");
        roomType.setRatePlanList(new HashMap<String, RatePlan>()); // fill this from 115
        com.mmt.hotels.model.response.pricing.RatePlan ratePlanCB = new RatePlan();

        ratePlanCB.setAvailDetails(new AvailDetails());
        ratePlanCB.getAvailDetails().setOccupancyDetails(new OccupancyDetails());
        ratePlanCB.getAvailDetails().getOccupancyDetails().setNumOfRooms(1);
        ratePlanCB.setCancelPenaltyList(new ArrayList<CancelPenalty>());
        ratePlanCB.getCancelPenaltyList().add(new CancelPenalty());
        ratePlanCB.getCancelPenaltyList().get(0).setPenaltyDescription(new Penalty());
        ratePlanCB.setInclusions(new ArrayList<Inclusion>());
        ratePlanCB.getInclusions().add(new Inclusion());
        ratePlanCB.getInclusions().get(0).setCode("abcd");
        ratePlanCB.getInclusions().get(0).setValue("abcd");
        ratePlanCB.setMealPlans(new ArrayList<MealPlan>());
        ratePlanCB.getMealPlans().add(new MealPlan());
        ratePlanCB.getMealPlans().get(0).setCode("SMAP");
        ratePlanCB.getMealPlans().get(0).setValue("abcd");
        ratePlanCB.setPaymentDetails(new PaymentDetails());
        ratePlanCB.getPaymentDetails().setPaymentMode(PaymentMode.PAS);
        ratePlanCB.setDisplayFare(new DisplayFare());
        ratePlanCB.getDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        ratePlanCB.getDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);
        ratePlanCB.setSupplierDetails(new SupplierDetails());
        ratePlanCB.setCancellationTimeline(new CancellationTimeline());
        ratePlanCB.getSupplierDetails().setCostPrice(19d);
        ratePlanCB.setCampaingText("Free Cancellation till 24 hrs");

        roomType.getRatePlanList().put("abc", ratePlanCB);

        Map<String, TotalPricing> priceMap = new HashMap<>();
        TotalPricing totalPricing = new TotalPricing();
        totalPricing.setPayAtHotelText("text");;
        priceMap.put("abc", totalPricing);

        hotelRates.getRecommendedRoomTypeDetails().getRoomType().put("abc", roomType);
        hotelRates.setRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getRoomTypeDetails().setRoomType(new HashMap<>());
        hotelRates.getRoomTypeDetails().getRoomType().put("abc", roomType);
        hotelRates.setOffers(new ArrayList<>());
        hotelRates.getOffers().add(new RangePrice());
        hotelRates.setRtbPreApproved(false);
        hotelRates.setRequestToBook(true);
        hotelRates.setNegotiatedRateFlag(true);

        List<HotelRates> hotelRatesList = new ArrayList<HotelRates>();
        hotelRatesList.add(hotelRates);

        RoomDetailsResponse roomDetailsResponse = new RoomDetailsResponse.Builder().buildHotelRates(hotelRatesList).build();


        List<HtlRmInfo> htlRmInfoList = new ArrayList<HtlRmInfo>();
        htlRmInfoList.add(new HtlRmInfo());
        htlRmInfoList.get(0).setHotelRoomInfo(new HashMap<String, RoomInfo>());
        SpaceData spaceData = new SpaceData();
        Space space = new Space();
        space.setDescriptionText("");
        space.setOpenCardText("test");
        space.setMedia(Arrays.asList(new MediaData()));
        spaceData.setSpaces(Arrays.asList(space));
        htlRmInfoList.get(0).setSharedSpaces(spaceData);

        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setMaster(true);
        roomInfo.setMaxAdultCount(5);
        roomInfo.setRoomSize("1");
        roomInfo.setRoomViewName("view");
        roomInfo.setBedType("king");
        roomInfo.setRoomSummary(new RoomSummary());
        roomInfo.getRoomSummary().setTopRated(true);

        List<SleepingArrangement> beds = new ArrayList<>();
        SleepingArrangement bed1 = new SleepingArrangement();
        bed1.setCount(3);
        bed1.setType("");
        beds.add(bed1);
        SleepingArrangement bed2 = new SleepingArrangement();
        bed2.setCount(1);
        bed2.setType("");
        beds.add(bed2);
        roomInfo.setBeds(beds);

        htlRmInfoList.get(0).getHotelRoomInfo().put("abc", roomInfo);
        roomInfo.setFacilityWithGrp(new ArrayList<>());
        roomInfo.getFacilityWithGrp().add(new FacilityGroup());
        roomInfo.getFacilityWithGrp().get(0).setName("test");
        roomInfo.getFacilityWithGrp().get(0).setFacilities(new ArrayList<>());
        roomInfo.getFacilityWithGrp().get(0).getFacilities().add(new Facility());
        roomInfo.setRoomLevelVideos(new ArrayList<>());
        roomInfo.getRoomLevelVideos().add(new VideoInfo());
        roomInfo.getRoomLevelVideos().get(0).setTags(new ArrayList<>());
        roomInfo.getRoomLevelVideos().get(0).setUrl("url");

        HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity = new HotelsRoomInfoResponseEntity.Builder().buildHtlRmInfo(htlRmInfoList).build();

        when(commonResponseTransformer.getDoubleBlackInfo(Mockito.any())).thenReturn(new DoubleBlackInfo());

        HotelImage hotelImage = new HotelImage();
        hotelImage.setHotelId("test");

        //Tests for 360 Images
        ImageType imageType = new ImageType();
        Map<String, List<Image360>> images360 = new HashMap<>();
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.CTA_360_TEXT)).thenReturn("View 360 Image");
        List<Image360> image360List = new ArrayList<>();
        Image360 image360 = new Image360();
        image360.setImageUrl("abc.jpg");
        image360.setThumbnail("xyz.jpg");
        image360.setRoomCode("abc");
        image360List.add(image360);
        images360.put("R",image360List);
        imageType.setImages360(images360);
        imageType.setProfessional(new HashMap<String, List<ProfessionalImageEntity>>());
        imageType.getProfessional().put("R", new ArrayList<>());
        imageType.getProfessional().get("R").add(new ProfessionalImageEntity());
        hotelImage.setImageDetails(imageType);
        SearchRoomsCriteria criteria = new SearchRoomsCriteria();
        criteria.setCheckIn("2022-11-03");
        criteria.setCheckOut("2022-11-04");
        criteria.setLocationType("region");
        criteria.setCountryCode("IN");
        ReflectionTestUtils
                .setField(searchRoomsResponseTransformerAndroid, "dateUtil", dateUtil);
        List<Filter> filterlist = new ArrayList<>();
        filterlist.add(new Filter());
        filterlist.get(0).setFilterGroup(FilterGroup.MEAL_PLAN_AVAIL);
        filterlist.get(0).setFilterValue("TWO_MEAL_AVAIL");
        Map<String,String> expDataMap = new HashMap<>();
        expDataMap.put("plcnew","true");

        boolean isXPercentSellOn = MapUtils.isNotEmpty(expDataMap)
                && expDataMap.containsKey(X_PERCENT_SELL_ON) && TRUE.equalsIgnoreCase(expDataMap.get(X_PERCENT_SELL_ON));
        String isXPercentSellExpText = isXPercentSellOn ? X_PERCENT_SELL_ON_TEXT : X_PERCENT_SELL_OFF_TEXT;

        roomDetailsResponse.setLuckyUserContext(LuckyUserContext.LUCKY);

        SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
        searchRoomsRequest.setFeatureFlags(new FeatureFlags());
        searchRoomsRequest.getFeatureFlags().setLiteResponse(true);
        searchRoomsRequest.setSearchCriteria(new SearchRoomsCriteria());
        // Initialize DeviceDetails with appVersion
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setAppVersion("9.0.3"); // Example app version
        deviceDetails.setBookingDevice("ANDROID");
        deviceDetails.setDeviceType("Mobile");
        searchRoomsRequest.setDeviceDetails(deviceDetails);
        
        // Initialize RequestDetails
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setVisitNumber(12); // Example visit number
        searchRoomsRequest.setRequestDetails(requestDetails);

        SearchRoomsResponse searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(searchRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap.toString(), null, criteria,filterlist,new RequestDetails(), "", new CommonModifierResponse());
        Assert.assertEquals(0, searchRoomsResponse.getExactRooms().get(0).getRatePlans().size());
        Assert.assertNotNull(searchRoomsResponse.getLuckyUserContext());
        Assert.assertEquals(isXPercentSellExpText + "|" + PRIVILEGED_USER, searchRoomsResponse.getLuckyUserContext());
        Assert.assertEquals(image360List, searchRoomsResponse.getExactRooms().get(0).getView360().getImages());
        Assert.assertEquals("abc.jpg", searchRoomsResponse.getExactRooms().get(0).getView360().getImages().get(0).getImageUrl());
        Assert.assertEquals("xyz.jpg", searchRoomsResponse.getExactRooms().get(0).getView360().getImages().get(0).getThumbnail());


        roomDetailsResponse.setLuckyUserContext(LuckyUserContext.LUCKY_UNLUCKY);
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(searchRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap.toString(), null, criteria,filterlist,new RequestDetails(), "", new CommonModifierResponse());
        Assert.assertEquals(0, searchRoomsResponse.getExactRooms().get(0).getRatePlans().size());
        Assert.assertNotNull(searchRoomsResponse.getLuckyUserContext());
        Assert.assertEquals(isXPercentSellExpText + "|" + CURSED_USER, searchRoomsResponse.getLuckyUserContext());

        roomDetailsResponse.setLuckyUserContext(LuckyUserContext.UNLUCKY);
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(searchRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap.toString(), null, criteria,filterlist,new RequestDetails(), "", new CommonModifierResponse());
        Assert.assertEquals(0, searchRoomsResponse.getExactRooms().get(0).getRatePlans().size());
        Assert.assertNotNull(searchRoomsResponse.getLuckyUserContext());
        Assert.assertEquals(isXPercentSellExpText + "|" + UNFORTUNATE_USER, searchRoomsResponse.getLuckyUserContext());

        roomDetailsResponse.setLuckyUserContext(null);
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(searchRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap.toString(), null, criteria,filterlist,new RequestDetails(), "", new CommonModifierResponse());
        Assert.assertEquals(0, searchRoomsResponse.getExactRooms().get(0).getRatePlans().size());
        Assert.assertNotNull(searchRoomsResponse.getLuckyUserContext());
        Assert.assertEquals(X_PERCENT_SELL_OFF_TEXT, searchRoomsResponse.getLuckyUserContext());

        MDC.clear();
    }

    @Test
    public void testConvertSearchRoomsResponse() {
        Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(Mockito.any(),Mockito.any(),Mockito.anyLong());
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid,"mealplanFilterEnable",true);
        org.jboss.logging.MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), "ANDROID");
        Map<String,Map<String,List<String>>> supplierToRateSegmentMapping = new HashMap<>();
        supplierToRateSegmentMapping.put(Constants.PACKAGE_RATE_KEY,new HashMap<>());
        supplierToRateSegmentMapping.put(Constants.TC_CLAUSE_KEY,new HashMap<>());
        supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).put("EPXX0034",Arrays.asList("1180"));
        supplierToRateSegmentMapping.get(Constants.TC_CLAUSE_KEY).put("EPXX0034",Arrays.asList("1180"));
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid,"supplierToRateSegmentMapping",supplierToRateSegmentMapping);

        HotelRates hotelRates = new HotelRates();
        hotelRates.setIsExtraAdultChild(false);
        hotelRates.setBnplBaseAmount(10d);
        hotelRates.setBlackEligible(false);
        hotelRates.setIsBNPLAvailable(false);
        hotelRates.setPahWalletApplicable(false);
        hotelRates.setPnAvlbl(true);
        hotelRates.setPanCardRequired(true);
        hotelRates.setShowFcBanner(true);
        hotelRates.setSoldOut(false);
        hotelRates.setBreakFast(true);
        hotelRates.setFreeCancellation(true);
        hotelRates.setBreakFastAvailable(true);
        hotelRates.setFreeCancellationAvailable(true);
        hotelRates.setPAHTariffAvailable(true);

        hotelRates.setRecommendedRoomTypeDetails(new
                RoomTypeDetails());
        hotelRates.getRecommendedRoomTypeDetails().setTotalDisplayFare(new DisplayFare());

        DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
        displayPriceBreakDown.setDisplayPrice(12d);
        displayPriceBreakDown.setDisplayPriceAlternateCurrency(12d);
        displayPriceBreakDown.setNonDiscountedPrice(12d);
        displayPriceBreakDown.setSavingPerc(5.05);
        displayPriceBreakDown.setBasePrice(13.05);
        displayPriceBreakDown.setHotelTax(4d);
        displayPriceBreakDown.setMmtDiscount(1d);
        displayPriceBreakDown.setCdfDiscount(1d);
        displayPriceBreakDown.setWallet(12d);
        displayPriceBreakDown.setPricingKey("key");
        displayPriceBreakDown.setCouponInfo(new BestCoupon());
        displayPriceBreakDown.getCouponInfo().setDescription("Coupon");
        displayPriceBreakDown.getCouponInfo().setCouponCode("code");
        displayPriceBreakDown.getCouponInfo().setSpecialPromoCoupon(false);
        displayPriceBreakDown.getCouponInfo().setType("promotional");
        displayPriceBreakDown.getCouponInfo().setDiscountAmount(100.0);
        HashMap<String,String> supplierDealsDetailsMap = new HashMap<>();
        supplierDealsDetailsMap.put(DEAL,EARLY_BIRD);
        supplierDealsDetailsMap.put(DISCOUNT,"0.5");
        supplierDealsDetailsMap.put(EXPIRY,"*********");
        displayPriceBreakDown.setSupplierDealsDetailMap(supplierDealsDetailsMap);

        NoCostEmiDetails noCostEmiDetails = new NoCostEmiDetails();
        noCostEmiDetails.setEmiAmount(10000);
        noCostEmiDetails.setTenure(3);
        noCostEmiDetails.setCouponCode("code");
        displayPriceBreakDown.setNoCostEmiDetailsList(Collections.singletonList(noCostEmiDetails));

        List<DisplayPriceBreakDown> displayPriceBreakDownList = new ArrayList<>();
        displayPriceBreakDownList.add(new DisplayPriceBreakDown());
        displayPriceBreakDownList.get(0).setDisplayPrice(12d);
        displayPriceBreakDownList.get(0).setDisplayPriceAlternateCurrency(12d);
        displayPriceBreakDownList.get(0).setNonDiscountedPrice(12d);
        displayPriceBreakDownList.get(0).setSavingPerc(5.05);
        displayPriceBreakDownList.get(0).setBasePrice(13.05);
        displayPriceBreakDownList.get(0).setHotelTax(4d);
        displayPriceBreakDownList.get(0).setMmtDiscount(1d);
        displayPriceBreakDownList.get(0).setCdfDiscount(1d);
        displayPriceBreakDownList.get(0).setWallet(12d);
        displayPriceBreakDownList.get(0).setPricingKey("key");
        displayPriceBreakDownList.get(0).setCouponInfo(new BestCoupon());
        displayPriceBreakDownList.get(0).getCouponInfo().setDescription("Coupon");
        displayPriceBreakDownList.get(0).getCouponInfo().setCouponCode("code");
        displayPriceBreakDownList.get(0).getCouponInfo().setSpecialPromoCoupon(false);
        displayPriceBreakDownList.get(0).getCouponInfo().setType("promotional");
        displayPriceBreakDownList.get(0).getCouponInfo().setDiscountAmount(100.0);

        hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);

        hotelRates.getRecommendedRoomTypeDetails().setRoomType(new HashMap<String, RoomType>());
        hotelRates.getRecommendedRoomTypeDetails().setOccupancyDetails(new OccupancyDetails());
        hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails().setAdult(2);

        RoomType roomType = new RoomType();
        roomType.setRoomTypeCode("abc");
        roomType.setRatePlanList(new HashMap<String, RatePlan>()); // fill this from 115
        com.mmt.hotels.model.response.pricing.RatePlan ratePlanCB = new RatePlan();

        ratePlanCB.setAvailDetails(new AvailDetails());
        ratePlanCB.getAvailDetails().setOccupancyDetails(new OccupancyDetails());
        ratePlanCB.getAvailDetails().getOccupancyDetails().setNumOfRooms(1);
        ratePlanCB.setCancelPenaltyList(new ArrayList<CancelPenalty>());
        ratePlanCB.getCancelPenaltyList().add(new CancelPenalty());
        ratePlanCB.getCancelPenaltyList().get(0).setPenaltyDescription(new Penalty());
        ratePlanCB.setInclusions(new ArrayList<Inclusion>());
        ratePlanCB.getInclusions().add(new Inclusion());
        ratePlanCB.getInclusions().get(0).setCode("abcd");
        ratePlanCB.getInclusions().get(0).setValue("abcd");
        ratePlanCB.setMealPlans(new ArrayList<MealPlan>());
        ratePlanCB.getMealPlans().add(new MealPlan());
        ratePlanCB.getMealPlans().get(0).setCode("SMAP");
        ratePlanCB.getMealPlans().get(0).setValue("abcd");
        ratePlanCB.setPaymentDetails(new PaymentDetails());
        ratePlanCB.getPaymentDetails().setPaymentMode(PaymentMode.PAS);
        ratePlanCB.setDisplayFare(new DisplayFare());
        ratePlanCB.getDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        ratePlanCB.getDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);
        ratePlanCB.setSupplierDetails(new SupplierDetails());
        ratePlanCB.setCancellationTimeline(new CancellationTimeline());
        ratePlanCB.getSupplierDetails().setCostPrice(19d);
        ratePlanCB.setCampaingText("Free Cancellation till 24 hrs");

        roomType.getRatePlanList().put("abc", ratePlanCB);

        Map<String, TotalPricing> priceMap = new HashMap<>();
        TotalPricing totalPricing = new TotalPricing();
        totalPricing.setPayAtHotelText("text");;
        priceMap.put("abc", totalPricing);

        when(commonResponseTransformer.getPriceMap(ratePlanCB.getDisplayFare().getDisplayPriceBreakDown(),ratePlanCB.getDisplayFare().getDisplayPriceBreakDownList(),"{plcnew=true}",1,null,null,1,false,null,false,false,false,false, false, null, new NoCostEmiDetails(), null, false, false)).thenReturn(priceMap);


        hotelRates.getRecommendedRoomTypeDetails().getRoomType().put("abc", roomType);
        hotelRates.setRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getRoomTypeDetails().setRoomType(new HashMap<>());
        hotelRates.getRoomTypeDetails().getRoomType().put("abc", roomType);
        hotelRates.setOffers(new ArrayList<>());
        hotelRates.getOffers().add(new RangePrice());
        hotelRates.setRtbPreApproved(false);
        hotelRates.setRequestToBook(true);
        hotelRates.setNegotiatedRateFlag(true);

        List<HotelRates> hotelRatesList = new ArrayList<HotelRates>();
        hotelRatesList.add(hotelRates);

        RoomDetailsResponse roomDetailsResponse = new RoomDetailsResponse.Builder().buildHotelRates(hotelRatesList).build();


        List<HtlRmInfo> htlRmInfoList = new ArrayList<HtlRmInfo>();
        htlRmInfoList.add(new HtlRmInfo());
        htlRmInfoList.get(0).setHotelRoomInfo(new HashMap<String, RoomInfo>());
        SpaceData spaceData = new SpaceData();
        Space space = new Space();
        space.setDescriptionText("");
        space.setOpenCardText("test");
        space.setMedia(Arrays.asList(new MediaData()));
        spaceData.setSpaces(Arrays.asList(space));
        htlRmInfoList.get(0).setSharedSpaces(spaceData);
        htlRmInfoList.get(0).setSpaceDetailsRequired(true);

        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setMaster(true);
        roomInfo.setMaxAdultCount(5);
        roomInfo.setRoomSize("1");
        roomInfo.setRoomViewName("view");
        roomInfo.setBedType("king");
        roomInfo.setRoomSummary(new RoomSummary());
        roomInfo.getRoomSummary().setTopRated(true);

        List<SleepingArrangement> beds = new ArrayList<>();
        SleepingArrangement bed1 = new SleepingArrangement();
        bed1.setCount(3);
        bed1.setType("");
        beds.add(bed1);
        SleepingArrangement bed2 = new SleepingArrangement();
        bed2.setCount(1);
        bed2.setType("");
        beds.add(bed2);
        roomInfo.setBeds(beds);

        htlRmInfoList.get(0).getHotelRoomInfo().put("abc", roomInfo);
        roomInfo.setFacilityWithGrp(new ArrayList<>());
        roomInfo.getFacilityWithGrp().add(new FacilityGroup());
        roomInfo.getFacilityWithGrp().get(0).setName("test");
        roomInfo.getFacilityWithGrp().get(0).setFacilities(new ArrayList<>());
        roomInfo.getFacilityWithGrp().get(0).getFacilities().add(new Facility());
        roomInfo.setRoomLevelVideos(new ArrayList<>());
        roomInfo.getRoomLevelVideos().add(new VideoInfo());
        roomInfo.getRoomLevelVideos().get(0).setTags(new ArrayList<>());
        roomInfo.getRoomLevelVideos().get(0).setUrl("url");

        HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity = new HotelsRoomInfoResponseEntity.Builder().buildHtlRmInfo(htlRmInfoList).build();

        when(commonResponseTransformer.getDoubleBlackInfo(Mockito.any())).thenReturn(new DoubleBlackInfo());

        HotelImage hotelImage = new HotelImage();
        hotelImage.setHotelId("test");
        hotelImage.setImageDetails(new ImageType());
        hotelImage.getImageDetails().setProfessional(new HashMap<String, List<ProfessionalImageEntity>>());
        hotelImage.getImageDetails().getProfessional().put("R", new ArrayList<>());
        hotelImage.getImageDetails().getProfessional().get("R").add(new ProfessionalImageEntity());
        SearchRoomsCriteria criteria = new SearchRoomsCriteria();
        criteria.setCheckIn("2022-11-03");
        criteria.setCheckOut("2022-11-04");
        criteria.setLocationType("region");
        criteria.setCountryCode("IN");
        ReflectionTestUtils
                .setField(searchRoomsResponseTransformerAndroid, "dateUtil", dateUtil);
       List<Filter> filterlist = new ArrayList<>();
       filterlist.add(new Filter());
       filterlist.get(0).setFilterGroup(FilterGroup.MEAL_PLAN_AVAIL);
       filterlist.get(0).setFilterValue("TWO_MEAL_AVAIL");
        Map<String,String> expDataMap = new HashMap<>();
        expDataMap.put("plcnew","true");
        SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
        searchRoomsRequest.setSearchCriteria(criteria);
        searchRoomsRequest.setDeviceDetails(new DeviceDetails());
        SearchRoomsResponse searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(searchRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap.toString(), null, criteria,filterlist,new RequestDetails(), "", new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse);
        Assert.assertNotNull(searchRoomsResponse.getSpecialFareInfo());

        ratePlanCB.getMealPlans().get(0).setCode("AP");
        filterlist.get(0).setFilterValue("ALL_MEAL_AVAIL");
        hotelRates.setNegotiatedRateFlag(false);

        when(commonResponseTransformer.getPriceMap(ratePlanCB.getDisplayFare().getDisplayPriceBreakDown(),ratePlanCB.getDisplayFare().getDisplayPriceBreakDownList(),"{\"ratePlanRedesign\":\"true\"}",1,null,null,1,false,null,false,false,false,false, false, null, new NoCostEmiDetails(), null, false, false)).thenReturn(priceMap);
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(searchRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria,filterlist,new RequestDetails(), "", new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse);
        Assert.assertNull(searchRoomsResponse.getSpecialFareInfo());

        // test case for sleepInfoText in stayDetail node
        Space bedroom1 = new Space();
        bedroom1.setSpaceType(Constants.BEDROOM);
        bedroom1.setSpaceId("1234");
        bedroom1.setBaseOccupancy(2);
        bedroom1.setMaxOccupancy(2);
        spaceData = new SpaceData();
        SleepingDetails sleepingDetails = new SleepingDetails();
        sleepingDetails.setExtraBedCount(1);
        sleepingDetails.setBedCount(7);
        sleepingDetails.setBedRoomCount(2);
        sleepingDetails.setMinOccupancy(2);
        sleepingDetails.setMaxOccupancy(2);
        sleepingDetails.setExtraBedCount(1);
        bedroom1.setSleepingDetails(sleepingDetails);
        spaceData.setSpaces(Arrays.asList(bedroom1));
        roomInfo.setPrivateSpaces(spaceData);
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(searchRoomsRequest, roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria, null, new RequestDetails(), "", new CommonModifierResponse());
        Assert.assertNull(searchRoomsResponse.getExactRooms().get(0).getStayDetail().getSleepInfoText());
        Assert.assertNull(searchRoomsResponse.getExactRooms().get(0).getStayDetail().getAdditionalSleepInfoText());

        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), Constants.ANDROID);
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(searchRoomsRequest, roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria, null, new RequestDetails(), "", new CommonModifierResponse());
        Assert.assertNull(searchRoomsResponse.getRoomInfo().getStayDetail().getAdditionalSleepInfoText());

        bedroom1.setMaxOccupancy(3);
        sleepingDetails.setMaxOccupancy(3);
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(searchRoomsRequest, roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria, null, new RequestDetails(), "", new CommonModifierResponse());
        Assert.assertNull(searchRoomsResponse.getRoomInfo().getStayDetail().getAdditionalSleepInfoText());

        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), Constants.CLIENT_DESKTOP);
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(searchRoomsRequest, roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria, null, new RequestDetails(), "", new CommonModifierResponse());

        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), Constants.ANDROID);

        hotelRates.setOtherRecommendedRooms(new ArrayList<>());
        hotelRates.getOtherRecommendedRooms().add(new RoomTypeDetails());
        hotelRates.getOtherRecommendedRooms().get(0).setRoomType(new HashMap<>());
        hotelRates.getOtherRecommendedRooms().get(0).getRoomType().put("def",roomType);
        hotelRates.getOtherRecommendedRooms().get(0).setTotalDisplayFare(new DisplayFare());
        hotelRates.getOtherRecommendedRooms().get(0).getTotalDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        hotelRates.getOtherRecommendedRooms().get(0).getTotalDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);

        LowestRateAPResp lowestRateAPResp = new LowestRateAPResp();
        AvailDetails availDetails = new AvailDetails();
        availDetails.setOccupancyDetails(new OccupancyDetails());
        lowestRateAPResp.setAvailDetails(availDetails);
        hotelRates.setLowestRate(lowestRateAPResp);

        hotelRates.getRoomTypeDetails().getRoomType().get("abc").getRatePlanList().get("abc").setSegmentId("1135");
        hotelRates.getRoomTypeDetails().getRoomType().get("abc").getRatePlanList().get("abc").getSupplierDetails().setSupplierCode("EPXX0034");

        when(commonResponseTransformer.getPriceMap(ratePlanCB.getDisplayFare().getDisplayPriceBreakDown(),ratePlanCB.getDisplayFare().getDisplayPriceBreakDownList(),"{\"ratePlanRedesign\":\"true\"}",1,null,null,1,false,"1135",false,false,false,false, false, null, new NoCostEmiDetails(), null, false, false)).thenReturn(priceMap);
        RequestDetails requestDetails = new RequestDetails();

        MDC.put(MDCHelper.MDCKeys.IDCONTEXT.getStringValue(), "CORP");
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(searchRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria,null,requestDetails, "", new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse);
        Assert.assertNotNull(searchRoomsResponse.getExactRooms().get(0).getRatePlans().get(0).getPersuasions());
        Assert.assertFalse(searchRoomsResponse.getFilters().stream().anyMatch(e->e.getCode().equals(CONTRACTED_FARE)));

        requestDetails.setPageContext("DETAIL");
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(searchRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria,null,requestDetails, "", new CommonModifierResponse());
        Assert.assertNotNull(searchRoomsResponse);
        Assert.assertTrue(searchRoomsResponse.getFilters().stream().anyMatch(e->e.getCode().equals(CONTRACTED_FARE)));



        hotelRates.setRoomTypeDetails(null); hotelRates.setPropertyType("Hostel");
        when(commonResponseTransformer.getPriceMap(ratePlanCB.getDisplayFare().getDisplayPriceBreakDown(),ratePlanCB.getDisplayFare().getDisplayPriceBreakDownList(),"{\"ratePlanRedesign\":\"true\"}",0,null,"Room",1,false,"",false,false,false,false, false, null, new NoCostEmiDetails(), null, false, false)).thenReturn(priceMap);
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(searchRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria,null,new RequestDetails(), "", new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse);
        hotelRates.setRecommendedRoomTypeDetails(null);
        hotelRates.setRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getRoomTypeDetails().setRoomType(new HashMap<>());
        hotelRates.getRoomTypeDetails().getRoomType().put("abc", roomType);
        hotelRates.setOccupencyLessRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getOccupencyLessRoomTypeDetails().setRoomType(new HashMap<>());
        hotelRates.getOccupencyLessRoomTypeDetails().getRoomType().put("Def",roomType);
        hotelRates.getOccupencyLessRoomTypeDetails().setTotalDisplayFare(new DisplayFare());
        hotelRates.getOccupencyLessRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);
        hotelRates.getOccupencyLessRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        hotelRates.setCategories(new HashSet<String>(){{add("luxury_hotels");}});

        hotelRates.setRtbPreApprovedCard(new RTBPreApprovedCard());
        hotelRates.setRTBRatePlanPreApproved(true);

        hotelRates.setRTBRatePlanPreApproved(false);
        hotelRates.setRequestToBook(false);
//        when(commonResponseTransformer.getPriceMap(ratePlanCB.getDisplayFare().getDisplayPriceBreakDown(),ratePlanCB.getDisplayFare().getDisplayPriceBreakDownList(),"{\"ratePlanRedesign\":\"true\"}",1,null,"Room",1,false,"1180",false,false,false,false, false, null)).thenReturn(priceMap);
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(searchRoomsRequest, roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria,null, new RequestDetails(), "", new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse);

        //for package rate plans
        setupForPackgeRatePlans(hotelRates);
        hotelRates.setOccupencyLessRoomTypeDetails(null);
        Mockito.when(commonResponseTransformer.getPriceMap(null,null,"{\"ratePlanRedesign\":\"true\"}",null,null,null,1,false,null,false,false,false,false, false, null, new NoCostEmiDetails(), null, false, false)).thenReturn(priceMap);
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(searchRoomsRequest, roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria, null, new RequestDetails(), "", new CommonModifierResponse());
        Assert.assertNotNull(searchRoomsResponse);
        Assert.assertNotNull(searchRoomsResponse.getPackageRooms());
        Assert.assertTrue(searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0).getFilterCode().contains("PACKAGE_RATE"));
        //if package in locked then LUXE PACKAGE filter should not be present
        Assert.assertFalse(searchRoomsResponse.getFilters().contains("PACKAGE_RATE"));

        //setting first room and rate plan as package
        hotelRates.getRoomTypeDetails().getRoomType().entrySet().iterator().next().getValue().getRatePlanList()
                  .entrySet().iterator().next().getValue().setPackageRoomRatePlan(true);
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(searchRoomsRequest, roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria, null, new RequestDetails(), "", new CommonModifierResponse());
        //first filter has to be package
        Assert.assertEquals(searchRoomsResponse.getFilters().get(0).getCode(), "PACKAGE_RATE");

        hotelRates.setCategories(new HashSet<String>(){{add("package_hotels");}});

        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(searchRoomsRequest, roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria, null, new RequestDetails(), "", new CommonModifierResponse());
        Assert.assertEquals(searchRoomsResponse.getFilters().get(0).getCode(), "PACKAGE_RATE");

        hotelRates.setDetailDeeplinkUrl("detaildeeplinkurl?city=City&checkAvailability=true");
        hotelRates.setName("hotelName");
        hotelRates.setHotelIcon("hotelIcon");

        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(searchRoomsRequest, roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria, null, new RequestDetails(), "", new CommonModifierResponse());
        Assert.assertNotNull(searchRoomsResponse.getDetailDeeplinkUrl());
        Assert.assertNotNull(searchRoomsResponse.getHotelDetails());
        Assert.assertNotNull(searchRoomsResponse.getHotelDetails().getUrl());

        criteria.setCountryCode("AE");
        searchRoomsResponse = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(searchRoomsRequest, roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"OHS\":\"true\"}", null, criteria, null, new RequestDetails(), "", new CommonModifierResponse());
        Assert.assertNull(searchRoomsResponse.getRoomInfo().getDescription());

        MDC.clear();
    }

    @Test
    public void buildExtraGuestDetailPersuasionTest() {

        RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
        roomTypeDetails.setRoomType(new HashMap<>());
        roomTypeDetails.getRoomType().put("test", new RoomType());
        roomTypeDetails.setFreeChildCount(2);
        roomTypeDetails.setFreeChildText("Free stay for both the children");
        ExtraGuestDetailPersuasion extraGuestDetailPersuasion = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildExtraGuestDetailPersuasion", false,roomTypeDetails, false);
        Assert.assertEquals(extraGuestDetailPersuasion.getText(), "<font color=\"null\">Free stay for both the children</font>");

        roomTypeDetails.setFreeChildCount(0);
        extraGuestDetailPersuasion = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildExtraGuestDetailPersuasion", false,roomTypeDetails, false);
        Assert.assertNull(extraGuestDetailPersuasion);

        roomTypeDetails.setRoomType(new HashMap<>());
        roomTypeDetails.getRoomType().put("test", new RoomType());
        roomTypeDetails.getRoomType().get("test").setRatePlanList(new HashMap<>());
        RatePlan rateplan = new RatePlan();
        rateplan.setFreeChildCount(2);
        rateplan.setFreeChildText("Free stay for both the children");
        roomTypeDetails.getRoomType().get("test").getRatePlanList().put("test", rateplan);
        extraGuestDetailPersuasion = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildExtraGuestDetailPersuasion", false,roomTypeDetails, false);
        Assert.assertEquals(extraGuestDetailPersuasion.getText(), "<font color=\"null\">Free stay for both the children</font>");

        Mockito.when(polyglotService.getTranslatedData(HIGH_DEMAND_PERSUASION)).thenReturn("High Demand");
        extraGuestDetailPersuasion = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildExtraGuestDetailPersuasion", true,roomTypeDetails, false);
        Assert.assertEquals(extraGuestDetailPersuasion.getText(), "<font color=\"#EC2127\">High Demand</font>");

    }



    @Test
    public void testBuildLongStayGCCNudgePersuasion_Success() {

        // Mocking necessary objects and methods
        HotelRates hotelRates = mock(HotelRates.class);
        DateUtil dateUtil = mock(DateUtil.class);
        CommonConfigConsul commonConfigConsul = mock(CommonConfigConsul.class);
        PolyglotService polyglotService = mock(PolyglotService.class);

        String checkIn = "2024-08-01";
        String checkOut = "2024-08-04";

        // Expected persuasion object
        LongStayGCCNudgePersuasion expectedPersuasion = new LongStayGCCNudgePersuasion();
        expectedPersuasion.setText("<font color=\"" +extraGuestFreeChildColor + "\">null</font>");
        expectedPersuasion.setIconUrl(longStayGccNudgeIconUrl);

        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");  // "AE" stands for United Arab Emirates

        // Mock the necessary methods
        when(hotelRates.isShowOfferType()).thenReturn(true);
        when(hotelRates.getCountryCode()).thenReturn("AE"); // Not "IN"
        when(dateUtil.getDaysDiff(LocalDate.parse(checkIn), LocalDate.parse(checkOut))).thenReturn(3);
        when(commonConfigConsul.getLosFosGCCNudgePersuasion()).thenReturn(2);
        // when(polyglotService.getTranslatedData(LONG_STAY_GCC_NUDGE_PERSUASION)).thenReturn("Translated Text");

        // Inject dependencies into the class under test
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid, "dateUtil", dateUtil);
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid, "commonConfigConsul", commonConfigConsul);
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid, "polyglotService", polyglotService);
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid, "extraGuestFreeChildColor", extraGuestFreeChildColor);
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid, "longStayGccNudgeIconUrl", longStayGccNudgeIconUrl);

        // Invoke the method and check the results
        LongStayGCCNudgePersuasion result = ReflectionTestUtils.invokeMethod(
                searchRoomsResponseTransformerAndroid,
                "buildLongStayGCCNudgePersuasion",
                hotelRates,
                checkIn,
                checkOut
        );

        when(hotelRates.getCountryCode()).thenReturn("IN"); // Now, country code is "IN"
        result = ReflectionTestUtils.invokeMethod(
                searchRoomsResponseTransformerAndroid,
                "buildLongStayGCCNudgePersuasion",
                hotelRates,
                checkIn,
                checkOut
        );
        assertNull(result); // Should return null because the country code is "IN"

        // Additional assertions to check for the new condition (commonConfigConsul is null or getLosFosGCCNudgePersuasion returns 0)
        when(commonConfigConsul.getLosFosGCCNudgePersuasion()).thenReturn(0); // Now, getLosFosGCCNudgePersuasion returns 0
        result = ReflectionTestUtils.invokeMethod(
                searchRoomsResponseTransformerAndroid,
                "buildLongStayGCCNudgePersuasion",
                hotelRates,
                checkIn,
                checkOut
        );
        assertNull(result); // Should return null because getLosFosGCCNudgePersuasion returns 0

        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid, "commonConfigConsul", null); // Now, commonConfigConsul is null
        result = ReflectionTestUtils.invokeMethod(
                searchRoomsResponseTransformerAndroid,
                "buildLongStayGCCNudgePersuasion",
                hotelRates,
                checkIn,
                checkOut
        );
        assertNull(result); // Should return null because commonConfigConsul is null
    }
    @Test
    public void testBuildLongStayGCCNudgePersuasion_NullArguments() {
        // Given null arguments
        HotelRates hotelRates = null;
        String checkIn = null;
        String checkOut = null;

        // When the method is called
        LongStayGCCNudgePersuasion result = ReflectionTestUtils.invokeMethod(
                searchRoomsResponseTransformerAndroid,
                "buildLongStayGCCNudgePersuasion",
                hotelRates,
                checkIn,
                checkOut
        );

        // Then the result should be null
        assertNull(result);
    }
    private void setupForPackgeRatePlans(HotelRates hotelRates) {
        hotelRates.setPackageRoomDetails(new RoomTypeDetails());
        hotelRates.getPackageRoomDetails().setRoomType(new HashMap<>());
        hotelRates.getPackageRoomDetails().getRoomType().put("packageRoom1", new PackageRoomType());
        ((PackageRoomType) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1"))
            .setAnimationType("UNLOCKED");
        ((PackageRoomType) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1"))
            .setRecommendText("#MMT Recommends");
        ((PackageRoomType) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1"))
            .setCtaText("Select Package");
        hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").setRatePlanList(new HashMap<>());
        hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                  .put("packageRatePlan1", new PackageRoomRatePlan());
        ((PackageRoomRatePlan) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                                         .get("packageRatePlan1")).setExtendedCheckOutDate("2021-07-24");
        ((PackageRoomRatePlan) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                                         .get("packageRatePlan1")).setExtendedCheckInDate("2021-07-21");
        hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                  .get("packageRatePlan1").setDisplayFare(new DisplayFare());
        hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                  .get("packageRatePlan1").setPackageRoomRatePlan(true);
    }

    @Test
    public void testBuildSpecialFarePersuasion() {
        when(polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_FARE_TAG_MOBILE)).thenReturn("{CORP_ALIAS} Fare");
        PersuasionResponse specialFarePersuasion = searchRoomsResponseTransformerAndroid.buildSpecialFareTagWithInfoPersuasion(CORP_ALIAS,false);
        Assert.assertNotNull(specialFarePersuasion);
        Assert.assertEquals(PRICE_BOTTOM_PLACEHOLDER_ID, specialFarePersuasion.getPlaceholderId());
        Assert.assertEquals(CORP_ALIAS + " Fare", specialFarePersuasion.getPersuasionText());
        Assert.assertTrue(specialFarePersuasion.isHtml());
        Assert.assertNotNull(specialFarePersuasion.getStyle());
        Assert.assertNotNull(specialFarePersuasion.getStyle().getBgGradient());
        Assert.assertEquals("SMALL", specialFarePersuasion.getStyle().getFontSize());
    }

    @Test
    public void testBuildBookingConfirmationPersuasion() {
        when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("test string");
        PersuasionResponse persuasionResponse = searchRoomsResponseTransformerAndroid.buildConfirmationTextPersuasion(CORP_ALIAS,true, false);
        Assert.assertNotNull(persuasionResponse);
    }

    @Test
    public void getRoomTariffInThisRatePlanCountTest(){
        List<Tariff> tariffList = new ArrayList<>();
        Tariff tariff = new Tariff();
        com.mmt.hotels.clientgateway.response.rooms.RoomTariff roomTariff = new com.mmt.hotels.clientgateway.response.rooms.RoomTariff();
        tariff.setRoomTariffs(new ArrayList<>());
        tariff.getRoomTariffs().add(roomTariff);
        tariff.getRoomTariffs().add(roomTariff);
        tariffList.add(tariff);
        Assert.assertNotNull(ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "getRoomTariffInThisRatePlanCount", tariffList));
    }

    @Test
    public void getFreeChildTextfromHotelRatesTest() {
        HotelRates hotelRates = new HotelRates();
        hotelRates.setRecommendedRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getRecommendedRoomTypeDetails().setFreeChildCount(2);
        hotelRates.getRecommendedRoomTypeDetails().setFreeChildText("Free stay for both your kids");

        String freeChildText = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "getFreeChildTextfromHotelRates", hotelRates);
        Assert.assertEquals("Free stay for both your kids", freeChildText);

        hotelRates.setRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getRoomTypeDetails().setRoomType(new HashMap<>());
        hotelRates.getRoomTypeDetails().getRoomType().put("test1",new RoomType());
        hotelRates.getRoomTypeDetails().getRoomType().get("test1").setRatePlanList(new HashMap<>());
        hotelRates.getRoomTypeDetails().getRoomType().get("test1").getRatePlanList().put("rateplan1",new RatePlan());
        hotelRates.getRoomTypeDetails().getRoomType().get("test1").getRatePlanList().get("rateplan1").setFreeChildCount(1);
        hotelRates.getRoomTypeDetails().getRoomType().get("test1").getRatePlanList().get("rateplan1").setFreeChildText("free stay text");

        hotelRates.getRecommendedRoomTypeDetails().setFreeChildCount(0);
        freeChildText = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "getFreeChildTextfromHotelRates", hotelRates);
        Assert.assertEquals("free stay text", freeChildText);

    }

    @Test
    public void updateRoomPersuasionTest() throws JsonProcessingException {
       String jsonString = "{\"PLACEHOLDER_SELECT_M1\":{\"data\":[{\"icontype\":\"lightning_icon\",\"style\":{\"textColor\":\"#249995\",\"bgColor\":\"#f2ffff\",\"fontType\":\"B\"}}]}}";
       ObjectMapper objectMapper = new ObjectMapper();
       JsonNode req = objectMapper.readTree(jsonString);
       ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformer,"updateRoomPersuasion",req);
       String expectedJson = "{\"PLACEHOLDER_SELECT_M1\":{\"data\":[{\"icontype\":\"lightning_icon_v2\",\"style\":{\"textColor\":\"#CF8100\"}}]}}";
       Assert.assertEquals(expectedJson,objectMapper.writeValueAsString(req));

       jsonString = "{\"PLACEHOLDER_SELECT_M1\":{\"data\":[{\"icontype\":\"lightning_icon\",\"style\":{}}]}}";
       req = objectMapper.readTree(jsonString);
       ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformer,"updateRoomPersuasion",req);
       expectedJson = "{\"PLACEHOLDER_SELECT_M1\":{\"data\":[{\"icontype\":\"lightning_icon_v2\",\"style\":{}}]}}";
       Assert.assertEquals(expectedJson,objectMapper.writeValueAsString(req));

        jsonString = "{\"PLACEHOLDER_SELECT_M1\":{\"data\":[]}}";
        req = objectMapper.readTree(jsonString);
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformer,"updateRoomPersuasion",req);
        expectedJson = "{\"PLACEHOLDER_SELECT_M1\":{\"data\":[]}}";
        Assert.assertEquals(expectedJson,objectMapper.writeValueAsString(req));

        jsonString = "{\"PLACEHOLDER_SELECT_M1\":{}}";
        req = objectMapper.readTree(jsonString);
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformer,"updateRoomPersuasion",req);
        expectedJson = "{\"PLACEHOLDER_SELECT_M1\":{}}";
        Assert.assertEquals(expectedJson,objectMapper.writeValueAsString(req));

        jsonString = "{\"hello\":{}}";
        req = objectMapper.readTree(jsonString);
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformer,"updateRoomPersuasion",req);
        expectedJson = "{\"hello\":{}}";
        Assert.assertEquals(expectedJson,objectMapper.writeValueAsString(req));

        jsonString = "{}";
        req = objectMapper.readTree(jsonString);
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformer,"updateRoomPersuasion",req);
        expectedJson = "{}";
        Assert.assertEquals(expectedJson,objectMapper.writeValueAsString(req));

        JsonNode x = null;
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformer,"updateRoomPersuasion",x);
    }

    @Test
    public void buildRoomInfoTextTest(){
        RoomDetails roomDetails = new RoomDetails();
        String roomSize = "260";
        RoomType roomType = new RoomType();
        Map<String, RatePlan> mp = new HashMap<>();
        RatePlan ratePlan = new RatePlan();
        DisplayFare displayFare = new DisplayFare();
        DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
        displayPriceBreakDown.setDisplayPrice(1000);
        displayFare.setDisplayPriceBreakDown(displayPriceBreakDown);
        ratePlan.setDisplayFare(displayFare);
        mp.put("abcd",ratePlan);
        roomType.setRatePlanList(mp);
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformer,"buildRoomInfoText",roomDetails,roomSize,roomType,"INR");
        Assert.assertEquals("Starts at ₹ 1,000 | 260 sq ft",roomDetails.getRoomInfoText());
    }

    @Test
    public void populateMissingSlotsTest(){
        DayUseRoomsResponse dayUseRoomsResponse = new DayUseRoomsResponse();
        dayUseRoomsResponse.setSlotPlans(new ArrayList<>());
        dayUseRoomsResponse.getSlotPlans().add(new DayUseSlotPlan());
        dayUseRoomsResponse.getSlotPlans().get(0).setSlot(new Slot());
        dayUseRoomsResponse.getSlotPlans().get(0).getSlot().setDuration(6);
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformer,"populateMissingSlots",dayUseRoomsResponse,6);

    }

    @Test
    public void buildFormUrlForMobLandingTest(){
        SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
        searchRoomsRequest.setSearchCriteria(new SearchRoomsCriteria());
        searchRoomsRequest.getSearchCriteria().setCheckIn("2022-11-03");
        searchRoomsRequest.getSearchCriteria().setCheckOut("2022-11-04");
        searchRoomsRequest.setDeviceDetails(new DeviceDetails());
        searchRoomsRequest.setRequestDetails(new RequestDetails());
        searchRoomsRequest.getRequestDetails().setVisitNumber(12);
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setAppVersion("9.0.3");
        deviceDetails.setBookingDevice("ANDROID");
        deviceDetails.setDeviceType("Mobile");
        SupportDetails supportDetails = new SupportDetails();
        ReflectionTestUtils
                .setField(searchRoomsResponseTransformer, "dateUtil", dateUtil);
        ReflectionTestUtils
                .setField(searchRoomsResponseTransformer,"utility",utility);
        supportDetails.setFormUrl("https://www.makemytrip.com/hotels/group-booking/?checkin={0}&checkout={1}&city={2}&country={3}&locusId={4}&locusType={5}&rsc={6}&_uCurrency={7}&appVersion={8}&deviceId={9}&bookingDevice={10}&deviceType={11}&visitorId={12}&visitNumber={13}&funnelSource={14}&idContext={15}&funnelName={16}&propertyType={17}&hotelName={18}");
        String res = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformer,"buildFormUrlForDetail",searchRoomsRequest,supportDetails,"Resort","The Taj");
        Assert.assertEquals("https://www.makemytrip.com/hotels/group-booking/?checkin=03112022&checkout=04112022&city=null&country=null&locusId=null&locusType=null&rsc=&_uCurrency=null&appVersion=null&deviceId=null&bookingDevice=null&deviceType=null&visitorId=null&visitNumber=12&funnelSource=null&idContext=null&funnelName=null&propertyType=Resort&hotelName=The+Taj",res);
    }

    @Test
    public void calculateTotalTicketValueTest(){
        String defaultPriceKey = "key";
        Map<String, TotalPricing> pricingMap = new HashMap<>();
        pricingMap.put("key1",new TotalPricing());
        int res = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformer,"calculateTotalTicketValue",defaultPriceKey,pricingMap);
        Assert.assertEquals(0,res);
    }

    @Test
    public void getPrimaryOfferForNoCostEmiTest() {
        HotelRates hotelRates = buildHotelRates();
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), "IOS");
        ReflectionTestUtils.setField(searchRoomsResponseTransformer, "noCostEmiIconUrl", "icon.url");
        ReflectionTestUtils.setField(searchRoomsResponseTransformer, "supplierBgColor", "green");
        ReflectionTestUtils.setField(searchRoomsResponseTransformer, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Avail no cost at {0} for {1} months");
        PrimaryOffer primaryOffer = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformer, "getPrimaryOfferForNoCostEmi", hotelRates);
        Assert.assertNotNull(primaryOffer);
        Assert.assertNotNull(primaryOffer.getDescription());
    }

    private HotelRates buildHotelRates() {
        NoCostEmiDetails noCostEmiDetails = new NoCostEmiDetails();
        noCostEmiDetails.setTenure(3);
        noCostEmiDetails.setEmiAmount(5000);

        DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
        displayPriceBreakDown.setNoCostEmiDetailsList(Collections.singletonList(noCostEmiDetails));

        List<DisplayPriceBreakDown> displayPriceBreakDownList = new ArrayList<>();
        displayPriceBreakDownList.add(displayPriceBreakDown);

        DisplayFare displayFare = new DisplayFare();
        displayFare.setDisplayPriceBreakDownList(displayPriceBreakDownList);

        RatePlan ratePlan = new RatePlan();
        ratePlan.setDisplayFare(displayFare);

        Map<String, RatePlan> ratePlanList = new HashMap<>();
        ratePlanList.put("RATE_PLAN_1", ratePlan);

        RoomType roomType = new RoomType();
        roomType.setRatePlanList(ratePlanList);

        Map<String, RoomType> roomTypeMap = new HashMap<>();
        roomTypeMap.put("ROOM_TYPE_1", roomType);

        RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
        roomTypeDetails.setRoomType(roomTypeMap);

        HotelRates hotelRates = new HotelRates();
        hotelRates.setRoomTypeDetails(roomTypeDetails);

        return hotelRates;
    }

    @Test
    public void getMealUpsellSubtext_test() {
        List<MealPlan> mealPlan = new ArrayList<>();
        MealPlan meal = new MealPlan();
        meal.setCode("CP");
        mealPlan.add(meal);
        searchRoomsResponseTransformer.getMealUpsellSubtext("10", mealPlan, "INR");
    }

    @Test
    public void transformMealInclusions_test() {
        List<MealPlan> mealPlan = new ArrayList<>();
        MealPlan meal = new MealPlan();
        meal.setCode("CP");
        mealPlan.add(meal);
        List<BookedInclusion> inclusions = new ArrayList<>();
        BookedInclusion inclusion = new BookedInclusion();
        inclusion.setCode("20");
        inclusion.setCategory("MEAL_UPSELL");
        inclusion.setSubText("test meal upsell inclusion");
        inclusions.add(inclusion);
        searchRoomsResponseTransformer.transformMealInclusions(inclusions, mealPlan, 120d, "INR");
    }

    @Test
    public void testPrivateMethod() {
        List<RecommendedCombo> recommendedCombos = new ArrayList<>();
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setAdultCount(2);
        roomStayCandidate.setRooms(1);
        roomStayCandidates.add(roomStayCandidate);
        RecommendedCombo recommendedCombo = new RecommendedCombo();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRoomName("test");
        SelectRoomRatePlan rate = new SelectRoomRatePlan();
        rate.setName("testRatePlan");
        rate.setTariffs(new ArrayList<>());
        roomDetails.setRatePlans(Arrays.asList(rate));
        recommendedCombo.setRooms(Arrays.asList(roomDetails));
        recommendedCombos.add(recommendedCombo);
        ReflectionTestUtils.setField(searchRoomsResponseTransformer, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("Important");
        SelectRoomImpInfo selectRoomImpInfo = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformer, "getImpInfo",recommendedCombos,roomStayCandidates);
        Assert.assertNotNull(selectRoomImpInfo);
    }

    @Test
    public void testGetPrimaryOfferForSaleCampaign() {
        CampaignPojo campaignPojo = new CampaignPojo();
        campaignPojo.setIconUrl("test");
        campaignPojo.setDescription("test");
        campaignPojo.setHeadingColor("color");
        PrimaryOffer primaryOffer = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformer, "getPrimaryOfferForSaleCampaign",campaignPojo);
        Assert.assertNotNull(primaryOffer);
    }
    @Test
    public void initConsulTest(){
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid, "consulFlag", true);
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid, "commonConfigConsul", new CommonConfigConsul());
        searchRoomsResponseTransformerAndroid.init();
    }

    @Test
    public void testBuildAdditionalFees() {
        RoomTypeDetails roomTypeDetails = getRoomTypeDetails();

        AdditionalFees additionalFees = new AdditionalFees();
        additionalFees.setPropertyType(Collections.singletonList("Transfers"));
        additionalFees.setDescription("Transfers");
        additionalFees.setAmount(100.0);
        additionalFees.setCurrency("USD");
        additionalFees.setTotalAdults(2);
        additionalFees.setTotalRooms(1);
        additionalFees.setApplicableDaysCount(1);
        additionalFees.setLeafCategory("Transfers");

        HotelRates hotelRates = new HotelRates();
        hotelRates.setRoomTypeDetails(roomTypeDetails);
        hotelRates.setMandatoryCharges(Collections.singletonList(additionalFees));
        hotelRates.setCountryCode("IN");
        hotelRates.setCityCode("DEL");
        hotelRates.setCurrencyCode("INR");
        hotelRates.setPropertyType("Hotel");
        hotelRates.setListingType("Hotel");

        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put("TFT", "true");
        commonModifierResponse.setExpDataMap(expDataMap);
        ReflectionTestUtils.setField(searchRoomsResponseTransformer, "commonResponseTransformer", commonResponseTransformer);
        Mockito.when(commonResponseTransformer.buildAdditionalCharges(Mockito.any(), Mockito.anyBoolean(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap())).thenReturn(new AdditionalMandatoryCharges());
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformer, "buildAdditionalFees", hotelRates, commonModifierResponse, expDataMap);
    }

    @Test
    public void testBuildAdditionalFeesForRatePlan_WhenTrafficSourceIsNull_ShouldReturnNull() {
        // Given
        HotelRates hotelRates = createTestHotelRates();
        RatePlan ratePlan = createTestRatePlan();
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setTrafficSource(null); // Traffic source is null
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        String roomName = "Test Room";

        // When
        AdditionalMandatoryCharges result = ReflectionTestUtils.invokeMethod(
                searchRoomsResponseTransformer, 
                "buildAdditionalFeesForRatePlan", 
                hotelRates, ratePlan, commonModifierResponse, expDataMap, roomName
        );

        // Then
        assertNull(result);
    }

    @Test
    public void testBuildAdditionalFeesForRatePlan_WhenTrafficSourceIsNotFlywheel_ShouldReturnNull() {
        // Given
        HotelRates hotelRates = createTestHotelRates();
        RatePlan ratePlan = createTestRatePlan();
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setTrafficSource("booking"); // Not flywheel
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        String roomName = "Test Room";

        // When
        AdditionalMandatoryCharges result = ReflectionTestUtils.invokeMethod(
                searchRoomsResponseTransformer, 
                "buildAdditionalFeesForRatePlan", 
                hotelRates, ratePlan, commonModifierResponse, expDataMap, roomName
        );

        // Then
        assertNull(result);
    }

    @Test
    public void testBuildAdditionalFeesForRatePlan_WhenTrafficSourceIsFlywheel_ShouldReturnAdditionalCharges() {
        // Given
        HotelRates hotelRates = createTestHotelRates();
        RatePlan ratePlan = createTestRatePlan();
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setTrafficSource("flywheel"); // Valid flywheel traffic source
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        String roomName = "Test Room";
        
        AdditionalMandatoryCharges expectedResult = new AdditionalMandatoryCharges();
        Mockito.when(commonResponseTransformer.buildAdditionalCharges(
                Mockito.any(), Mockito.anyBoolean(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()
        )).thenReturn(expectedResult);
        ReflectionTestUtils.setField(searchRoomsResponseTransformer, "commonResponseTransformer", commonResponseTransformer);

        // When
        AdditionalMandatoryCharges result = ReflectionTestUtils.invokeMethod(
                searchRoomsResponseTransformer, 
                "buildAdditionalFeesForRatePlan", 
                hotelRates, ratePlan, commonModifierResponse, expDataMap, roomName
        );

        // Then
        assertNotNull(result);
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBuildAdditionalFeesForRatePlan_WithTransfersFeeTextEnabled_ShouldPassCorrectFlag() {
        // Given
        HotelRates hotelRates = createTestHotelRates();
        RatePlan ratePlan = createTestRatePlan();
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setTrafficSource("flywheel");
        
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put(Constants.TRANSFERS_FEE_TEXT_KEY, Constants.EXP_TRUE_VALUE);
        commonModifierResponse.setExpDataMap(expDataMap);
        
        String roomName = "Test Room";
        
        AdditionalMandatoryCharges expectedResult = new AdditionalMandatoryCharges();
        Mockito.when(commonResponseTransformer.buildAdditionalCharges(
                Mockito.any(), Mockito.eq(true), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()
        )).thenReturn(expectedResult);
        ReflectionTestUtils.setField(searchRoomsResponseTransformer, "commonResponseTransformer", commonResponseTransformer);

        // When
        AdditionalMandatoryCharges result = ReflectionTestUtils.invokeMethod(
                searchRoomsResponseTransformer, 
                "buildAdditionalFeesForRatePlan", 
                hotelRates, ratePlan, commonModifierResponse, expDataMap, roomName
        );

        // Then
        assertNotNull(result);
        Mockito.verify(commonResponseTransformer).buildAdditionalCharges(
                Mockito.any(), Mockito.eq(true), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()
        );
    }

    @Test
    public void testBuildAdditionalFeesForRatePlan_WithTransfersFeeTextDisabled_ShouldPassCorrectFlag() {
        // Given
        HotelRates hotelRates = createTestHotelRates();
        RatePlan ratePlan = createTestRatePlan();
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setTrafficSource("flywheel");
        
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put(Constants.TRANSFERS_FEE_TEXT_KEY, "false");
        commonModifierResponse.setExpDataMap(expDataMap);
        
        String roomName = "Test Room";
        
        AdditionalMandatoryCharges expectedResult = new AdditionalMandatoryCharges();
        Mockito.when(commonResponseTransformer.buildAdditionalCharges(
                Mockito.any(), Mockito.eq(false), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()
        )).thenReturn(expectedResult);
        ReflectionTestUtils.setField(searchRoomsResponseTransformer, "commonResponseTransformer", commonResponseTransformer);

        // When
        AdditionalMandatoryCharges result = ReflectionTestUtils.invokeMethod(
                searchRoomsResponseTransformer, 
                "buildAdditionalFeesForRatePlan", 
                hotelRates, ratePlan, commonModifierResponse, expDataMap, roomName
        );

        // Then
        assertNotNull(result);
        Mockito.verify(commonResponseTransformer).buildAdditionalCharges(
                Mockito.any(), Mockito.eq(false), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()
        );
    }

    @Test
    public void testBuildAdditionalFeesForRatePlan_WithoutTransfersFeeTextKey_ShouldDefaultToFalse() {
        // Given
        HotelRates hotelRates = createTestHotelRates();
        RatePlan ratePlan = createTestRatePlan();
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setTrafficSource("flywheel");
        
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        // No TRANSFERS_FEE_TEXT_KEY in expDataMap
        commonModifierResponse.setExpDataMap(expDataMap);
        
        String roomName = "Test Room";
        
        AdditionalMandatoryCharges expectedResult = new AdditionalMandatoryCharges();
        Mockito.when(commonResponseTransformer.buildAdditionalCharges(
                Mockito.any(), Mockito.eq(false), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()
        )).thenReturn(expectedResult);
        ReflectionTestUtils.setField(searchRoomsResponseTransformer, "commonResponseTransformer", commonResponseTransformer);

        // When
        AdditionalMandatoryCharges result = ReflectionTestUtils.invokeMethod(
                searchRoomsResponseTransformer, 
                "buildAdditionalFeesForRatePlan", 
                hotelRates, ratePlan, commonModifierResponse, expDataMap, roomName
        );

        // Then
        assertNotNull(result);
        Mockito.verify(commonResponseTransformer).buildAdditionalCharges(
                Mockito.any(), Mockito.eq(false), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()
        );
    }

    @Test
    public void testBuildAdditionalFeesForRatePlan_WithNullSupplierDetails_ShouldUseDefaultValues() {
        // Given
        HotelRates hotelRates = createTestHotelRates();
        RatePlan ratePlan = createTestRatePlan();
        ratePlan.setSupplierDetails(null); // Null supplier details
        
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setTrafficSource("flywheel");
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        String roomName = "Test Room";
        
        AdditionalMandatoryCharges expectedResult = new AdditionalMandatoryCharges();
        Mockito.when(commonResponseTransformer.buildAdditionalCharges(
                Mockito.any(), Mockito.anyBoolean(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()
        )).thenReturn(expectedResult);
        ReflectionTestUtils.setField(searchRoomsResponseTransformer, "commonResponseTransformer", commonResponseTransformer);

        // When
        AdditionalMandatoryCharges result = ReflectionTestUtils.invokeMethod(
                searchRoomsResponseTransformer, 
                "buildAdditionalFeesForRatePlan", 
                hotelRates, ratePlan, commonModifierResponse, expDataMap, roomName
        );

        // Then
        assertNotNull(result);
        // Verify that buildAdditionalCharges was called with the correct parameters
        Mockito.verify(commonResponseTransformer).buildAdditionalCharges(
                Mockito.any(), Mockito.anyBoolean(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()
        );
    }

    @Test
    public void testBuildAdditionalFeesForRatePlan_WithZeroConversionFactor_ShouldUseDefaultConversionFactor() {
        // Given
        HotelRates hotelRates = createTestHotelRates();
        RatePlan ratePlan = createTestRatePlan();
        ratePlan.getDisplayFare().setConversionFactor(0.0); // Zero conversion factor
        
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setTrafficSource("flywheel");
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        String roomName = "Test Room";
        
        AdditionalMandatoryCharges expectedResult = new AdditionalMandatoryCharges();
        Mockito.when(commonResponseTransformer.buildAdditionalCharges(
                Mockito.any(), Mockito.anyBoolean(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()
        )).thenReturn(expectedResult);
        ReflectionTestUtils.setField(searchRoomsResponseTransformer, "commonResponseTransformer", commonResponseTransformer);

        // When
        AdditionalMandatoryCharges result = ReflectionTestUtils.invokeMethod(
                searchRoomsResponseTransformer, 
                "buildAdditionalFeesForRatePlan", 
                hotelRates, ratePlan, commonModifierResponse, expDataMap, roomName
        );

        // Then
        assertNotNull(result);
        Mockito.verify(commonResponseTransformer).buildAdditionalCharges(
                Mockito.any(), Mockito.anyBoolean(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()
        );
    }

    @Test
    public void testBuildAdditionalFeesForRatePlan_WithRecommendedRoomTypeDetails_ShouldSetRecommendationFlowTrue() {
        // Given
        HotelRates hotelRates = createTestHotelRates();
        hotelRates.setRecommendedRoomTypeDetails(new RoomTypeDetails()); // Set recommended room type details
        
        RatePlan ratePlan = createTestRatePlan();
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setTrafficSource("flywheel");
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        String roomName = "Test Room";
        
        AdditionalMandatoryCharges expectedResult = new AdditionalMandatoryCharges();
        Mockito.when(commonResponseTransformer.buildAdditionalCharges(
                Mockito.any(), Mockito.anyBoolean(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()
        )).thenReturn(expectedResult);
        ReflectionTestUtils.setField(searchRoomsResponseTransformer, "commonResponseTransformer", commonResponseTransformer);

        // When
        AdditionalMandatoryCharges result = ReflectionTestUtils.invokeMethod(
                searchRoomsResponseTransformer, 
                "buildAdditionalFeesForRatePlan", 
                hotelRates, ratePlan, commonModifierResponse, expDataMap, roomName
        );

        // Then
        assertNotNull(result);
        Mockito.verify(commonResponseTransformer).buildAdditionalCharges(
                Mockito.any(), Mockito.anyBoolean(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()
        );
    }

    @Test
    public void testBuildAdditionalFeesForRatePlan_WithNullExpDataMap_ShouldHandleGracefully() {
        // Given
        HotelRates hotelRates = createTestHotelRates();
        RatePlan ratePlan = createTestRatePlan();
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setTrafficSource("flywheel");
        commonModifierResponse.setExpDataMap(null); // Null expDataMap
        
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        String roomName = "Test Room";
        
        AdditionalMandatoryCharges expectedResult = new AdditionalMandatoryCharges();
        Mockito.when(commonResponseTransformer.buildAdditionalCharges(
                Mockito.any(), Mockito.anyBoolean(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()
        )).thenReturn(expectedResult);
        ReflectionTestUtils.setField(searchRoomsResponseTransformer, "commonResponseTransformer", commonResponseTransformer);

        // When
        AdditionalMandatoryCharges result = ReflectionTestUtils.invokeMethod(
                searchRoomsResponseTransformer, 
                "buildAdditionalFeesForRatePlan", 
                hotelRates, ratePlan, commonModifierResponse, expDataMap, roomName
        );

        // Then
        assertNotNull(result);
        Mockito.verify(commonResponseTransformer).buildAdditionalCharges(
                Mockito.any(), Mockito.eq(false), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()
        );
    }

    private HotelRates createTestHotelRates() {
        HotelRates hotelRates = new HotelRates();
        hotelRates.setCurrencyCode("USD");
        hotelRates.setPropertyType("Hotel");
        hotelRates.setCityCode("NYC");
        hotelRates.setCountryCode("US");
        hotelRates.setCityName("New York");
        hotelRates.setListingType("Hotel");
        return hotelRates;
    }

    private RatePlan createTestRatePlan() {
        RatePlan ratePlan = new RatePlan();
        
        // Set up supplier details
        SupplierDetails supplierDetails = new SupplierDetails();
        supplierDetails.setHotelierCurrencyCode("EUR");
        ratePlan.setSupplierDetails(supplierDetails);
        
        // Set up display fare
        DisplayFare displayFare = new DisplayFare();
        displayFare.setConversionFactor(1.2);
        ratePlan.setDisplayFare(displayFare);
        
        // Set up additional fees
        List<AdditionalFees> additionalFees = new ArrayList<>();
        AdditionalFees fee = new AdditionalFees();
        fee.setDescription("Test Fee");
        fee.setAmount(50.0);
        additionalFees.add(fee);
        ratePlan.setAdditionalFees(additionalFees);
        
        return ratePlan;
    }

    private RoomTypeDetails getRoomTypeDetails() {
        RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
        roomTypeDetails.setRoomType(new HashMap<>());
        
        RoomType roomType = new RoomType();
        roomType.setRoomTypeName("Test Room");
        roomType.setRatePlanList(new HashMap<>());
        
        RatePlan ratePlan = new RatePlan();
        SupplierDetails supplierDetails = new SupplierDetails();
        supplierDetails.setHotelierCurrencyCode("USD");
        ratePlan.setSupplierDetails(supplierDetails);
        
        DisplayFare displayFare = new DisplayFare();
        displayFare.setConversionFactor(1.0);
        ratePlan.setDisplayFare(displayFare);
        
        roomType.getRatePlanList().put("testRatePlan", ratePlan);
        roomTypeDetails.getRoomType().put("testRoom", roomType);
        
        return roomTypeDetails;
    }

    @Test
    public void updateSupportDetailsTest(){
        // Given
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();

        // When
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformer, "updateSupportDetails", searchRoomsResponse);

        // Then
        SupportDetails supportDetails = searchRoomsResponse.getSupportDetails();
        assertNotNull(supportDetails);
        assertEquals("icon url", supportDetails.getIconUrl());
        assertEquals("title", supportDetails.getTitle());
        assertNotNull(supportDetails.getOptions());
        assertEquals("option", supportDetails.getOptions().get(0));
    }

    @Test
    public void createLosInclusionTest() {
        Inclusion losInclusion = new Inclusion();
        List<BookedInclusion> inclusions = new ArrayList<>();
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformer,"createLosInclusion", losInclusion, inclusions);
        Assert.assertEquals(1, inclusions.size());
    }

    @Test
    public void buildSleepingArrangementFromBedroomInfoTest() {

        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity = new HotelsRoomInfoResponseEntity();
        SleepingArrangementRoomInfo roomInfo = new SleepingArrangementRoomInfo();
        String countryCode = "USA";
        ExternalVendorBedRoomInfo externalVendorBedRoomInfo = new ExternalVendorBedRoomInfo();
        externalVendorBedRoomInfo.setBedRoomDescription("bathrooms count");
        externalVendorBedRoomInfo.setBedRoomName("bathrooms");
        hotelsRoomInfoResponseEntity.setHtlRmInfo(new ArrayList<>());
        hotelsRoomInfoResponseEntity.getHtlRmInfo().add(new HtlRmInfo());
        hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).setHotelRoomInfo(new HashMap<>());
        hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().put("test", new RoomInfo());
        hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().get("test").setExternalVendorBedRoomInfoList(new ArrayList<>());
        hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().get("test").getExternalVendorBedRoomInfoList().add(externalVendorBedRoomInfo);
        hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().get("test").setBedRoomCount("2");

        searchRoomsResponse.setExactRooms(new ArrayList<>());
        searchRoomsResponse.getExactRooms().add(new RoomDetails());
        searchRoomsResponse.getExactRooms().get(0).setRoomCode("test");


        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformer, "buildSleepingArrangementFromBedroomInfo", searchRoomsResponse, hotelsRoomInfoResponseEntity, roomInfo, countryCode);
        Assert.assertNotNull(roomInfo.getSleepingArrangement());

        searchRoomsResponse.setExactRooms(null);
        roomInfo.setSleepingArrangement(null);
        searchRoomsResponse.setRecommendedCombos(new ArrayList<>());
        searchRoomsResponse.getRecommendedCombos().add(new RecommendedCombo());
        searchRoomsResponse.getRecommendedCombos().get(0).setRooms(new ArrayList<>());
        searchRoomsResponse.getRecommendedCombos().get(0).getRooms().add(new RoomDetails());
        searchRoomsResponse.getRecommendedCombos().get(0).getRooms().get(0).setRoomCode("test");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformer, "buildSleepingArrangementFromBedroomInfo", searchRoomsResponse, hotelsRoomInfoResponseEntity, roomInfo, countryCode);
        Assert.assertNotNull(roomInfo.getSleepingArrangement());


    }

    @Test
    public void testBuildStayInfoList() {
        // Given
        String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), "ANDROID");
        StayDetail stayDetail = new StayDetail();
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity = new HotelsRoomInfoResponseEntity();
        List<RoomDetails> recommendedRooms = new ArrayList<>();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setBedCount(1);
        roomDetails.setMaxGuest(2);
        roomDetails.setBathroomCount(1);
        recommendedRooms.add(roomDetails);
//        when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("test");
        HotelRates hotelRates = new HotelRates();

        searchRoomsResponse.setRecommendedCombos(new ArrayList<>());
        searchRoomsResponse.getRecommendedCombos().add(new RecommendedCombo());
        searchRoomsResponse.getRecommendedCombos().get(0).setRooms(new ArrayList<>());
        searchRoomsResponse.getRecommendedCombos().get(0).getRooms().add(roomDetails);
        searchRoomsResponse.getRecommendedCombos().get(0).getRooms().get(0).setRoomCode("test");

        ExternalVendorBedRoomInfo externalVendorBedRoomInfo = new ExternalVendorBedRoomInfo();
        externalVendorBedRoomInfo.setBedRoomDescription("bathrooms");
        externalVendorBedRoomInfo.setBedRoomName("1");

        ExternalVendorBedRoomInfo externalVendorBedRoomInfo1 = new ExternalVendorBedRoomInfo();
        externalVendorBedRoomInfo1.setBedRoomDescription("bedroom 1");
        externalVendorBedRoomInfo1.setBedRoomName("1 queen bed");

        hotelsRoomInfoResponseEntity.setHtlRmInfo(new ArrayList<>());
        hotelsRoomInfoResponseEntity.getHtlRmInfo().add(new HtlRmInfo());
        hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).setHotelRoomInfo(new HashMap<>());
        hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().put("test", new RoomInfo());
        hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().get("test").setExternalVendorBedRoomInfoList(new ArrayList<>());
        hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().get("test").getExternalVendorBedRoomInfoList().add(externalVendorBedRoomInfo);
        hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().get("test").getExternalVendorBedRoomInfoList().add(externalVendorBedRoomInfo1);
        hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().get("test").setFacilityWithGrp(new ArrayList<>());
        FacilityGroup facilityWithGrp = new FacilityGroup();
        facilityWithGrp.setName("kitchen");
        hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().get("test").getFacilityWithGrp().add(facilityWithGrp);

        searchRoomsResponse.setExactRooms(new ArrayList<>());
        searchRoomsResponse.getExactRooms().add(roomDetails);
        searchRoomsResponse.getExactRooms().get(0).setRoomCode("test");

        // EXACT ROOM SCENARIO
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildStayInfoList", stayDetail, hotelsRoomInfoResponseEntity, searchRoomsResponse, new HotelRates());
        assertNotNull(stayDetail.getStayInfoList());
        assertFalse(stayDetail.getStayInfoList().isEmpty());

        //bedroomcount 2
        roomDetails.setBedroomCount(2);
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildStayInfoList", stayDetail, hotelsRoomInfoResponseEntity, searchRoomsResponse, new HotelRates());
        assertNotNull(stayDetail.getStayInfoList());
        assertFalse(stayDetail.getStayInfoList().isEmpty());

        //bedroomcount 1
        roomDetails.setBedroomCount(1);
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildStayInfoList", stayDetail, hotelsRoomInfoResponseEntity, searchRoomsResponse, new HotelRates());
        assertNotNull(stayDetail.getStayInfoList());
        assertFalse(stayDetail.getStayInfoList().isEmpty());

        hotelRates.setPropertyType("hostel");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildStayInfoList", stayDetail, hotelsRoomInfoResponseEntity, searchRoomsResponse, hotelRates);
        assertNotNull(stayDetail.getStayInfoList());
        assertFalse(stayDetail.getStayInfoList().isEmpty());

        //bedroomcount 0
        roomDetails.setBedroomCount(0);
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildStayInfoList", stayDetail, hotelsRoomInfoResponseEntity, searchRoomsResponse, new HotelRates());
        assertNotNull(stayDetail.getStayInfoList());
        assertFalse(stayDetail.getStayInfoList().isEmpty());

        hotelRates.setPropertyType("hostel");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildStayInfoList", stayDetail, hotelsRoomInfoResponseEntity, searchRoomsResponse, hotelRates);
        assertNotNull(stayDetail.getStayInfoList());
        assertFalse(stayDetail.getStayInfoList().isEmpty());



        searchRoomsResponse.setExactRooms(null);
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildStayInfoList", stayDetail, hotelsRoomInfoResponseEntity, searchRoomsResponse, new HotelRates());
        assertNotNull(stayDetail.getStayInfoList());
        assertFalse(stayDetail.getStayInfoList().isEmpty());

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildStayInfoList", stayDetail, hotelsRoomInfoResponseEntity, searchRoomsResponse, new HotelRates());
        assertNotNull(stayDetail.getStayInfoList());
        assertFalse(stayDetail.getStayInfoList().isEmpty());

        roomDetails.setBedroomCount(2);
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildStayInfoList", stayDetail, hotelsRoomInfoResponseEntity, searchRoomsResponse, new HotelRates());
        assertNotNull(stayDetail.getStayInfoList());
        assertFalse(stayDetail.getStayInfoList().isEmpty());

        roomDetails.setBedroomCount(0);
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildStayInfoList", stayDetail, hotelsRoomInfoResponseEntity, searchRoomsResponse, new HotelRates());
        assertNotNull(stayDetail.getStayInfoList());
        assertFalse(stayDetail.getStayInfoList().isEmpty());

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildStayInfoList", stayDetail, hotelsRoomInfoResponseEntity, searchRoomsResponse, hotelRates);
        assertNotNull(stayDetail.getStayInfoList());
        assertFalse(stayDetail.getStayInfoList().isEmpty());

        searchRoomsResponse.getRecommendedCombos().get(0).getRooms().add(roomDetails);
        searchRoomsResponse.getRecommendedCombos().get(0).getRooms().get(0).setRoomCode("test");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildStayInfoList", stayDetail, hotelsRoomInfoResponseEntity, searchRoomsResponse, hotelRates);
        assertNotNull(stayDetail.getStayInfoList());
        assertFalse(stayDetail.getStayInfoList().isEmpty());
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), client);

    }

    @Test
    public void testBuildParentLinkedRates() throws Exception {
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        List<LinkedRate> linkedRates = new ArrayList<>();
        LinkedRate linkedRate = new LinkedRate();
        linkedRate.setType("type");
        linkedRate.setPricingKey("pricingKey");
        linkedRates.add(linkedRate);

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildParentLinkedRates", ratePlan, linkedRates);

        org.junit.Assert.assertTrue(CollectionUtils.isNotEmpty(ratePlan.getParentLinkedRates()));
        org.junit.Assert.assertEquals("type", ratePlan.getParentLinkedRates().get(0).getType());
        org.junit.Assert.assertEquals("pricingKey", ratePlan.getParentLinkedRates().get(0).getPricingKey());
    }

    @Test
    public void testBuildChildLinkedRates() throws Exception {
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        List<LinkedRate> linkedRates = new ArrayList<>();
        LinkedRate linkedRate = new LinkedRate();
        linkedRate.setType("type");
        linkedRate.setPricingKey("pricingKey");
        linkedRates.add(linkedRate);

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildChildLinkedRates", ratePlan, linkedRates);

        org.junit.Assert.assertTrue(CollectionUtils.isNotEmpty(ratePlan.getChildLinkedRates()));
        org.junit.Assert.assertEquals("type", ratePlan.getChildLinkedRates().get(0).getType());
        org.junit.Assert.assertEquals("pricingKey", ratePlan.getChildLinkedRates().get(0).getPricingKey());
    }
    @Test
    public void testLinkRatePlans() throws Exception {
        SelectRoomRatePlan ratePlan1 = new SelectRoomRatePlan();
        ratePlan1.setRpc("rpc1");
        ratePlan1.setLinkedRatePlanName("Linked Rate Plan 1");

        SelectRoomRatePlan ratePlan2 = new SelectRoomRatePlan();
        ratePlan2.setRpc("rpc2");
        ratePlan2.setLinkedRatePlanName("Linked Rate Plan 2");

        com.mmt.hotels.model.response.pricing.LinkedRate pricingLinkedRate = new com.mmt.hotels.model.response.pricing.LinkedRate();
        pricingLinkedRate.setPricingKey("rpc2");
        pricingLinkedRate.setType("CANCELLATION_POLICY_NR");

        com.mmt.hotels.clientgateway.response.rooms.LinkedRate clientGatewayLinkedRate = convertToClientGatewayLinkedRate(pricingLinkedRate);
        ratePlan1.setChildLinkedRates(Collections.singletonList(clientGatewayLinkedRate));

        List<SelectRoomRatePlan> ratePlans = new ArrayList<>();
        ratePlans.add(ratePlan1);
        ratePlans.add(ratePlan2);

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "linkRatePlans", ratePlans);

        Assert.assertTrue(CollectionUtils.isNotEmpty(ratePlan1.getLinkedRatePlans()));
        Assert.assertEquals("Linked Rate Plan 2", ratePlan1.getLinkedRatePlans().get(0).getRatePlan().getLinkedRatePlanName());
    }

    private com.mmt.hotels.clientgateway.response.rooms.LinkedRate convertToClientGatewayLinkedRate(com.mmt.hotels.model.response.pricing.LinkedRate pricingLinkedRate) {
        com.mmt.hotels.clientgateway.response.rooms.LinkedRate clientGatewayLinkedRate = new com.mmt.hotels.clientgateway.response.rooms.LinkedRate();
        clientGatewayLinkedRate.setPricingKey(pricingLinkedRate.getPricingKey());
        clientGatewayLinkedRate.setType(pricingLinkedRate.getType());
        return clientGatewayLinkedRate;
    }

    @Test
    public void buildRoomInfoTitleSetsTitleWithSizeBedWhenNotEmpty() {
        HotelRates hotelRates = new HotelRates();
        hotelRates.setStayTypeWithSizeBed("Room in a Villa with King Bed");
        SleepingArrangementRoomInfo roomInfo = new SleepingArrangementRoomInfo();

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildRoomInfoTitle",false, false, hotelRates, "IN", true, roomInfo);

        assertEquals("Room in a Villa with King Bed", roomInfo.getTitle());
    }

    @Test
    public void buildRoomInfoTitleSetsTitleWithSizeWhenSizeBedIsEmpty() {
        HotelRates hotelRates = new HotelRates();
        hotelRates.setStayTypeWithSize("Room in a Villa");
        SleepingArrangementRoomInfo roomInfo = new SleepingArrangementRoomInfo();

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildRoomInfoTitle", false, false,hotelRates, "IN", true, roomInfo);

        assertEquals("Room in a Villa", roomInfo.getTitle());
    }

    @Test
    public void buildRoomInfoTitleForEntirePropertyWithMultipleRoomsInIndia() {
        HotelRates hotelRates = new HotelRates();
        hotelRates.setStayTypeWithSize("Service Apartment");
        hotelRates.setRoomCount(3);
        hotelRates.setListingType("Entire");
        SleepingArrangementRoomInfo roomInfo = new SleepingArrangementRoomInfo();

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildRoomInfoTitle", false,false,hotelRates, "IN", true, roomInfo);

        assertEquals("3 Service Apartments", roomInfo.getTitle());
    }

    @Test
    public void buildRoomInfoTitleForEntirePropertyWithMultipleRoomsIH() {
        HotelRates hotelRates = new HotelRates();
        hotelRates.setStayTypeWithSize("Service Apartment");
        hotelRates.setRoomCount(3);
        hotelRates.setListingType("Entire");
        SleepingArrangementRoomInfo roomInfo = new SleepingArrangementRoomInfo();

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildRoomInfoTitle",false, false, hotelRates, "US", true, roomInfo);

        assertEquals("3 Service Apartments", roomInfo.getTitle());
    }

    @Test
    public void buildRoomInfoTitleForSingleRoom() {
        HotelRates hotelRates = new HotelRates();
        hotelRates.setStayTypeWithSize("Room in a Villa");
        hotelRates.setRoomCount(1);
        SleepingArrangementRoomInfo roomInfo = new SleepingArrangementRoomInfo();

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildRoomInfoTitle", false, false,hotelRates, "IN", true, roomInfo);

        assertEquals("Room in a Villa", roomInfo.getTitle());
    }

    @Test
    public void buildRoomInfoTitleForNonEntireAltAccoProperty() {
        HotelRates hotelRates = new HotelRates();
        hotelRates.setStayTypeWithSize("Rooms in a Villa");
        hotelRates.setRoomCount(3);
        hotelRates.setListingType("room");
        hotelRates.setAltAcco(true);
        SleepingArrangementRoomInfo roomInfo = new SleepingArrangementRoomInfo();

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildRoomInfoTitle", false,false,hotelRates, "IN", true, roomInfo);

        assertEquals("3 Rooms in a Villa", roomInfo.getTitle());
    }

    @Test
    public void buildRoomInfoTitleForHotel() {
        HotelRates hotelRates = new HotelRates();
        hotelRates.setStayTypeWithSize("Hotel");
        hotelRates.setRoomCount(3);
        hotelRates.setListingType("room");
        hotelRates.setAltAcco(false);
        SleepingArrangementRoomInfo roomInfo = new SleepingArrangementRoomInfo();

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildRoomInfoTitle", false, false,hotelRates, "IN", true, roomInfo);

        assertEquals("Hotel", roomInfo.getTitle());
    }

    @Test
    public void testBuildAternateDatesPrice_AllScenarios() {
        // Mock utility
        Utility utilityMock = mock(Utility.class);
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid, "utility", utilityMock);

        // Test Scenario 1: Valid INR currency
        when(utilityMock.convertNumericValueToCommaSeparatedString(1500, Locale.ENGLISH)).thenReturn("1,500");
        String result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 1500, "INR");
        assertEquals("₹1,500", result);

        // Test Scenario 2: Valid USD currency
        when(utilityMock.convertNumericValueToCommaSeparatedString(2000, Locale.ENGLISH)).thenReturn("2,000");
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 2000, "USD");
        assertEquals("$2,000", result);

        // Test Scenario 3: Valid EUR currency
        when(utilityMock.convertNumericValueToCommaSeparatedString(1800, Locale.ENGLISH)).thenReturn("1,800");
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 1800, "EUR");
        assertEquals("€1,800", result);

        // Test Scenario 4: Valid GBP currency
        when(utilityMock.convertNumericValueToCommaSeparatedString(1200, Locale.ENGLISH)).thenReturn("1,200");
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 1200, "GBP");
        assertEquals("£1,200", result);

        // Test Scenario 5: Valid AED currency
        when(utilityMock.convertNumericValueToCommaSeparatedString(850, Locale.ENGLISH)).thenReturn("850");
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 850, "AED");
        assertEquals("AED850", result);

        // Test Scenario 6: Valid SGD currency
        when(utilityMock.convertNumericValueToCommaSeparatedString(1350, Locale.ENGLISH)).thenReturn("1,350");
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 1350, "SGD");
        assertEquals("SGD1,350", result);

        // Test Scenario 7: Valid JPY currency
        when(utilityMock.convertNumericValueToCommaSeparatedString(15000, Locale.ENGLISH)).thenReturn("15,000");
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 15000, "JPY");
        assertEquals("¥15,000", result);

        // Test Scenario 8: Valid CNY currency
        when(utilityMock.convertNumericValueToCommaSeparatedString(999, Locale.ENGLISH)).thenReturn("999");
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 999, "CNY");
        assertEquals("CNY999", result);

        // Test Scenario 9: Valid THB currency
        when(utilityMock.convertNumericValueToCommaSeparatedString(5000, Locale.ENGLISH)).thenReturn("5,000");
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 5000, "THB");
        assertEquals("THB5,000", result);

        // Test Scenario 10: Valid RUB currency
        when(utilityMock.convertNumericValueToCommaSeparatedString(7500, Locale.ENGLISH)).thenReturn("7,500");
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 7500, "RUB");
        assertEquals("₽7,500", result);

        // Test Scenario 11: Valid KRW currency
        when(utilityMock.convertNumericValueToCommaSeparatedString(120000, Locale.ENGLISH)).thenReturn("120,000");
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 120000, "KRW");
        assertEquals("₩120,000", result);

        // Test Scenario 12: Valid BDT currency
        when(utilityMock.convertNumericValueToCommaSeparatedString(8500, Locale.ENGLISH)).thenReturn("8,500");
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 8500, "BDT");
        assertEquals("৳8,500", result);

        // Test Scenario 13: Valid VND currency
        when(utilityMock.convertNumericValueToCommaSeparatedString(2300000, Locale.ENGLISH)).thenReturn("2,300,000");
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 2300000, "VND");
        assertEquals("₫2,300,000", result);

        // Test Scenario 14: Null currency (should default to INR)
        when(utilityMock.convertNumericValueToCommaSeparatedString(3000, Locale.ENGLISH)).thenReturn("3,000");
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 3000, null);
        assertEquals("₹3,000", result);

        // Test Scenario 15: Empty currency (should default to INR)
        when(utilityMock.convertNumericValueToCommaSeparatedString(2500, Locale.ENGLISH)).thenReturn("2,500");
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 2500, "");
        assertEquals("₹2,500", result);

        // Test Scenario 16: Blank currency with spaces (should default to INR)
        when(utilityMock.convertNumericValueToCommaSeparatedString(1750, Locale.ENGLISH)).thenReturn("1,750");
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 1750, "   ");
        assertEquals("₹1,750", result);

        // Test Scenario 17: Zero price
        when(utilityMock.convertNumericValueToCommaSeparatedString(0, Locale.ENGLISH)).thenReturn("0");
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 0, "USD");
        assertEquals("$0", result);
    }

    @Test
    public void testBuildAternateDatesPrice_GCCCurrencies() {
        // Mock utility
        Utility utilityMock = mock(Utility.class);
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid, "utility", utilityMock);

        // Test GCC region currencies
        when(utilityMock.convertNumericValueToCommaSeparatedString(500, Locale.ENGLISH)).thenReturn("500");

        // Test SAR (Saudi Arabian Riyal)
        String result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 500, "SAR");
        assertEquals("SAR500", result);

        // Test KWD (Kuwaiti Dinar)
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 500, "KWD");
        assertEquals("KWD500", result);

        // Test QAR (Qatari Riyal)
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 500, "QAR");
        assertEquals("QAR500", result);

        // Test OMR (Omani Rial)
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 500, "OMR");
        assertEquals("OMR500", result);

        // Test BHD (Bahraini Dinar)
        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 500, "BHD");
        assertEquals("BHD500", result);
    }

    @Test
    public void testBuildAternateDatesPrice_CommonCurrencies() {
        // Mock utility
        Utility utilityMock = mock(Utility.class);
        ReflectionTestUtils.setField(searchRoomsResponseTransformerAndroid, "utility", utilityMock);

        when(utilityMock.convertNumericValueToCommaSeparatedString(1000, Locale.ENGLISH)).thenReturn("1,000");

        // Test commonly used currencies
        String result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 1000, "AUD");
        assertEquals("AUD1,000", result);

        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 1000, "CAD");
        assertEquals("CAD1,000", result);

        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 1000, "MYR");
        assertEquals("MYR1,000", result);

        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 1000, "NZD");
        assertEquals("NZD1,000", result);

        result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerAndroid, "buildAternateDatesPrice", 1000, "HKD");
        assertEquals("HKD1,000", result);
    }

    // =========================================================================
    // COMPREHENSIVE TEST CASES FOR MAXIMUM CODE COVERAGE - APPENDED WITHOUT MODIFYING EXISTING TESTS
    // =========================================================================

    @Test
    public void testConvertSearchRoomsResponseWithEmptyHotelRates() {
        // Test empty hotel rates handling
        SearchRoomsRequest request = new SearchRoomsRequest();
        request.setClient("ANDROID");
        
        RoomDetailsResponse roomDetailsResponse = new RoomDetailsResponse();
        roomDetailsResponse.setHotelRates(new ArrayList<>());
        roomDetailsResponse.setCurrency("INR");
        
        SearchCriteria criteria = new SearchCriteria();
        criteria.setCheckIn("2024-01-15");
        criteria.setCheckOut("2024-01-16");
        
        SearchRoomsResponse result = searchRoomsResponseTransformerAndroid.convertSearchRoomsResponse(
                request, roomDetailsResponse, null, null, null, null, criteria, null, 
                new RequestDetails(), null, new CommonModifierResponse());
        
        Assert.assertNotNull("Should return non-null response", result);
        Assert.assertEquals("Currency should be set", "INR", result.getCurrency());
    }

    @Test
    public void testConvertSearchSlotsResponseWithEmptyHotelRates() {
        // Test day use response with empty hotel rates
        RoomDetailsResponse roomDetailsResponse = new RoomDetailsResponse();
        roomDetailsResponse.setHotelRates(new ArrayList<>());
        
        DayUseRoomsRequest dayUseRequest = new DayUseRoomsRequest();
        dayUseRequest.setClient("ANDROID");
        
        DayUseRoomsResponse result = searchRoomsResponseTransformerAndroid.convertSearchSlotsResponse(
                roomDetailsResponse, null, null, dayUseRequest, null);
        
        Assert.assertNotNull("Should handle empty hotel rates", result);
    }

    // Test abstract method implementations
    @Test
    public void testGetHtmlMethod() {
        String html = searchRoomsResponseTransformerAndroid.getHtml();
        Assert.assertNotNull("Should return HTML content", html);
        Assert.assertTrue("Should contain HTML", html.contains("html") || html.length() > 0);
    }
}
