package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.DiscountDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.ExtraDiscount;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.LinkedRate;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.MarkUpDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.NoCostEmiDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceCouponInfo;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail;
import com.ibm.icu.text.NumberFormat;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.response.CouponPersuasion;
import com.mmt.hotels.clientgateway.response.EMIPlanDetail;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.rooms.RecommendedCombo;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomRatePlan;
import com.mmt.hotels.clientgateway.response.rooms.Tariff;
import com.mmt.hotels.clientgateway.response.searchHotels.BgGradient;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.anyDouble;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.isNull;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class SearchRoomsPriceHelperTest {

    @Mock
    private CommonResponseTransformer commonResponseTransformer;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    private PricingEngineHelper pricingEngineHelper;

    @Mock
    private Utility utility;

    @InjectMocks
    private SearchRoomsPriceHelper searchRoomsPriceHelper;

    @Mock
    private CommonConfigConsul commonConfigConsul;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(searchRoomsPriceHelper, "genericBankIcon", "https://example.com/bank-icon.png");
        
        // Initialize the NumberFormat manually since @PostConstruct won't work in unit tests
        NumberFormat numberFormatter = NumberFormat.getNumberInstance(new Locale("en", "IN"));
        numberFormatter.setMaximumFractionDigits(0);
        numberFormatter.setMinimumFractionDigits(0);
        ReflectionTestUtils.setField(searchRoomsPriceHelper, "numberFormatter", numberFormatter);
        
        // Mock the noCostEmiIconConfig
        BgGradient noCostEmiIconConfig = new BgGradient();
        noCostEmiIconConfig.setStart("#FF0000");
        noCostEmiIconConfig.setEnd("#00FF00");
        ReflectionTestUtils.setField(searchRoomsPriceHelper, "noCostEmiIconConfig", noCostEmiIconConfig);
        
        // Setup common mock behaviors
        lenient().when(utility.getExpDataMap(anyString())).thenReturn(new HashMap<>());
        lenient().when(utility.isExperimentTrue(any(Map.class), anyString())).thenReturn(false);
        lenient().when(commonResponseTransformer.getPriceDisplayMessage(anyString(), any(), anyString(), any(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn("Per Night");
        lenient().when(commonResponseTransformer.getShowTaxMessage(anyString(), any(), anyDouble(), anyString())).thenReturn("Taxes Included");
        lenient().when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated Text");
        lenient().when(utility.showReviewOffersCategory(any())).thenReturn(false);
        lenient().when(utility.isDetailPageAPI(anyString())).thenReturn(false);
        //lenient().when(utility.isGccOrKsa()).thenReturn(false);
    }

    @Test
    public void init_test() {
        searchRoomsPriceHelper.init();
    }

    @Test
    public void should_ReturnNull_When_PriceDetailIsNull() {
        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                null, "expData", null, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false,
                false, null, null, null, false, false);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_NoCostEmiDetailsIsNull() {
        // When
        EMIPlanDetail result = searchRoomsPriceHelper.buildEmiPlanDetails(null);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildPriceToolTipWithNullPriceDetail() {
        // When
        String result = searchRoomsPriceHelper.buildPriceToolTipFromPriceDetail(null, 2, "INR");

        // Then
        assertNull(result);
    }

    @Test
    public void should_BuildEmiPlanDetails_When_ValidNoCostEmiDetailsProvided() {
        // Given
        NoCostEmiDetails noCostEmiDetails = new NoCostEmiDetails();
        noCostEmiDetails.setTenure(12);
        noCostEmiDetails.setEmiAmount(1000.0);
        noCostEmiDetails.setBankName("TEST_BANK");

        // When
        EMIPlanDetail result = searchRoomsPriceHelper.buildEmiPlanDetails(noCostEmiDetails);

        // Then
        assertNotNull(result);
    }

    @Test
    public void should_GetPriceMap_When_ValidPriceDetailWithCouponCode() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        priceDetail.setCouponCode("TEST_COUPON");
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", true, false, false, false,
                false, new MarkUpDetails(), new NoCostEmiDetails(), new ArrayList<>(),
                false, false);

        // Then
        assertNotNull(result);
        assertTrue(result.containsKey("TEST_COUPON"));
        TotalPricing totalPricing = result.get("TEST_COUPON");
        assertNotNull(totalPricing);
        assertEquals("TEST_PRICING_KEY", totalPricing.getPricingKey());
        assertEquals("Per Night", totalPricing.getPriceDisplayMsg());
        assertEquals("Taxes Included", totalPricing.getPriceTaxMsg());
    }

    @Test
    public void should_GetPriceMap_When_ValidPriceDetailWithoutCouponCode() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        priceDetail.setCouponCode(null);
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false,
                false, new MarkUpDetails(), new NoCostEmiDetails(), new ArrayList<>(),
                false, false);

        // Then
        assertNotNull(result);
        assertTrue(result.containsKey("DEFAULT"));
    }

    @Test
    public void should_GetPriceMap_When_NewSelectRoomPageExperimentEnabled() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();
        
        Map<String, String> expDataMap = new HashMap<>();
        lenient().when(utility.getExpDataMap(anyString())).thenReturn(expDataMap);
        lenient().when(utility.isExperimentTrue(expDataMap, "NEW_SELECT_ROOM_PAGE")).thenReturn(true);
        lenient().when(commonResponseTransformer.getPriceDisplayMessage(anyString(), any(), anyString(), any(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn("Per Night");

        // Mock MDC for Android client
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), "ANDROID");

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false,
                false, new MarkUpDetails(), new NoCostEmiDetails(), new ArrayList<>(),
                false, false);

        // Then
        assertNotNull(result);
        TotalPricing totalPricing = result.get("TEST_COUPON");
        assertEquals("Per Night", totalPricing.getPriceDisplayMsg());
        
        // Clean up MDC
        MDC.clear();
    }

    @Test
    public void should_GetPriceMap_When_IOSClientWithNewSelectRoomPage() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();
        
        Map<String, String> expDataMap = new HashMap<>();
        lenient().when(utility.getExpDataMap(anyString())).thenReturn(expDataMap);
        lenient().when(utility.isExperimentTrue(expDataMap, "NEW_SELECT_ROOM_PAGE")).thenReturn(true);
        lenient().when(commonResponseTransformer.getPriceDisplayMessage(anyString(), any(), anyString(), any(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn("Per Night");

        // Mock MDC for iOS client
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), "IOS");

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false,
                false, new MarkUpDetails(), new NoCostEmiDetails(), new ArrayList<>(),
                false, false);

        // Then
        assertNotNull(result);
        TotalPricing totalPricing = result.get("TEST_COUPON");
        assertEquals("Per Night", totalPricing.getPriceDisplayMsg());
        
        // Clean up MDC
        MDC.clear();
    }

    @Test
    public void should_GetPriceMap_When_GroupFunnelEnhancementEnabled() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();
        
        Map<String, String> expDataMap = new HashMap<>();
        lenient().when(utility.getExpDataMap(anyString())).thenReturn(expDataMap);
        lenient().when(utility.isExperimentTrue(expDataMap, "GROUP_FUNNEL_ENHANCEMENT_EXP")).thenReturn(true);
        
        // Add additional mocks needed for group booking
        lenient().when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated Text");

        // Mock MDC for B2C context
        MDC.put(MDCHelper.MDCKeys.IDCONTEXT.getStringValue(), "B2C");

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, true, true, true,
                false, new MarkUpDetails(), new NoCostEmiDetails(), new ArrayList<>(),
                false, false);

        // Then
        assertNotNull(result);
        verify(polyglotService, atLeastOnce()).getTranslatedData(anyString());
        
        // Clean up MDC
        MDC.clear();
    }

    @Test
    public void should_GetPriceMap_When_ApplicableCouponsProvided() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        
        // Add applicable coupons
        List<PriceCouponInfo> applicableCoupons = new ArrayList<>();
        
        PriceCouponInfo coupon1 = new PriceCouponInfo();
        coupon1.setCouponCode("TEST_COUPON");
        coupon1.setDescription("Test Coupon Description");
        coupon1.setDiscount(500.0);
        applicableCoupons.add(coupon1);
        
        PriceCouponInfo coupon2 = new PriceCouponInfo();
        coupon2.setCouponCode("EXTRA_COUPON");
        coupon2.setDescription("Extra Coupon Description");
        coupon2.setDiscount(300.0);
        applicableCoupons.add(coupon2);
        
        priceDetail.setApplicableCoupons(applicableCoupons);
        
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false,
                false, new MarkUpDetails(), new NoCostEmiDetails(), new ArrayList<>(),
                false, false);

        // Then
        assertNotNull(result);
        assertTrue(result.containsKey("TEST_COUPON"));
        assertTrue(result.containsKey("EXTRA_COUPON"));
        
        TotalPricing mainCouponPricing = result.get("TEST_COUPON");
        assertEquals("Test Coupon Description", mainCouponPricing.getCouponDesc());
        assertEquals(500.0, mainCouponPricing.getCouponAmount(), 0);
        
        TotalPricing extraCouponPricing = result.get("EXTRA_COUPON");
        assertEquals("Extra Coupon Description", extraCouponPricing.getCouponDesc());
        assertEquals(300.0, extraCouponPricing.getCouponAmount(), 0);
    }

    @Test
    public void should_GetPriceMap_When_ExtraDiscountProvided() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        
        ExtraDiscount extraDiscount = new ExtraDiscount();
        extraDiscount.setType("FIRST_BOOKING");
        extraDiscount.setDiscount(200.0);
        extraDiscount.setBookingCount(1);
        priceDetail.setExtraDiscount(extraDiscount);
        
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();

        lenient().when(commonResponseTransformer.buildCouponPersuasion(anyString(), any(), any(), anyBoolean())).thenReturn(new CouponPersuasion());

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false,
                false, new MarkUpDetails(), new NoCostEmiDetails(), new ArrayList<>(),
                true, false);

        // Then
        assertNotNull(result);
        verify(commonResponseTransformer).buildCouponPersuasion(eq("FIRST_BOOKING"), any(), any(), eq(true));
    }

    @Test
    public void should_GetPriceMap_When_FBPHostNMMTExtraDiscountType() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        
        ExtraDiscount extraDiscount = new ExtraDiscount();
        extraDiscount.setType("FBPHostNMMT");
        extraDiscount.setDiscount(200.0);
        extraDiscount.setBookingCount(1);
        priceDetail.setExtraDiscount(extraDiscount);
        
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();

        lenient().when(commonResponseTransformer.buildCouponPersuasion(anyString(), any(), any(), anyBoolean())).thenReturn(new CouponPersuasion());

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false,
                false, new MarkUpDetails(), new NoCostEmiDetails(), new ArrayList<>(),
                true, false);

        // Then
        assertNotNull(result);
        verify(commonResponseTransformer).buildCouponPersuasion(eq("FIRST_FIVE_BOOKING"), any(), any(), eq(true));
    }

    @Test
    public void should_GetPriceMap_When_NullOccupancyDetails() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();

        lenient().when(commonResponseTransformer.getPriceDisplayMessage(anyString(), isNull(), anyString(), any(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn("Per Night");

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", null, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false,
                false, new MarkUpDetails(), new NoCostEmiDetails(), new ArrayList<>(),
                false, false);

        // Then
        assertNotNull(result);
        assertTrue(result.containsKey("TEST_COUPON"));
    }

    @Test
    public void should_GetPriceMap_When_CorpRequest() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                true, "CORP_SEGMENT", false, false, false, false,
                false, new MarkUpDetails(), new NoCostEmiDetails(), new ArrayList<>(),
                false, false);

        // Then
        assertNotNull(result);
        assertTrue(result.containsKey("TEST_COUPON"));
    }

    @Test
    public void should_GetPriceMap_When_LinkedRatesProvided() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();
        
        List<com.gommt.hotels.orchestrator.detail.model.response.pricing.LinkedRate> linkedRates = new ArrayList<>();
        LinkedRate linkedRate = new LinkedRate();
        linkedRate.setType("LINKED_RATE_TYPE");
        linkedRate.setPricingKey("LINKED_RATE_1");
        linkedRates.add(linkedRate);

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false,
                false, new MarkUpDetails(), new NoCostEmiDetails(), linkedRates,
                false, false);

        // Then
        assertNotNull(result);
        assertTrue(result.containsKey("TEST_COUPON"));
    }

    @Test
    public void should_BuildPriceToolTip_When_ValidPriceDetail() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();

        // When
        String result = searchRoomsPriceHelper.buildPriceToolTipFromPriceDetail(priceDetail, 2, "INR");

        // Then
        assertNull(result);
    }

    @Test
    public void should_BuildEmiPlanDetails_When_ValidNoCostEmiDetails() {
        // Given
        NoCostEmiDetails noCostEmiDetails = new NoCostEmiDetails();
        noCostEmiDetails.setTenure(6);
        noCostEmiDetails.setEmiAmount(800.0);
        noCostEmiDetails.setBankName("HDFC_BANK");

        // When
        EMIPlanDetail result = searchRoomsPriceHelper.buildEmiPlanDetails(noCostEmiDetails);

        // Then
        assertNotNull(result);
    }

    @Test
    public void should_HandleMyPartnerRequest_When_MyPartnerFlagTrue() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();
        
        MarkUpDetails markUpDetails = new MarkUpDetails();
        
        //lenient().when(pricingEngineHelper.getMarkUpForHotels(any(MarkUpDetails.class), any(Double.class))).thenReturn(100.0);

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, true,
                false, markUpDetails, new NoCostEmiDetails(), new ArrayList<>(),
                false, false);

        // Then
        assertNotNull(result);
        //verify(pricingEngineHelper).getMarkUpForHotels(eq(markUpDetails), any(Double.class));
    }

    // Additional test cases for 100% coverage

    @Test
    public void should_ReturnNull_When_EmiAmountIsZero() {
        // Given
        NoCostEmiDetails noCostEmiDetails = new NoCostEmiDetails();
        noCostEmiDetails.setEmiAmount(0.0);
        noCostEmiDetails.setTenure(12);

        // When
        EMIPlanDetail result = searchRoomsPriceHelper.buildEmiPlanDetails(noCostEmiDetails);

        // Then
        assertNull(result);
    }

    @Test
    public void should_SetCouponTiedEmiAmount_When_Available() {
        // Given
        NoCostEmiDetails noCostEmiDetails = new NoCostEmiDetails();
        noCostEmiDetails.setEmiAmount(1000.0);
        noCostEmiDetails.setCouponTiedEmiAmount(900.0);
        noCostEmiDetails.setTenure(12);

        // When
        EMIPlanDetail result = searchRoomsPriceHelper.buildEmiPlanDetails(noCostEmiDetails);

        // Then
        assertNotNull(result);
        assertNotNull(result.getEmiTagDetail());
    }

    @Test
    public void should_BuildPriceToolTip_When_BasePriceAndTaxAvailable() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        priceDetail.setBasePrice(2000.0);
        priceDetail.setTotalTax(400.0);
        priceDetail.setTotalDiscount(200.0);
        priceDetail.setDiscount(new DiscountDetails());
        priceDetail.getDiscount().setLongStay(100.0);

        // When
        String result = searchRoomsPriceHelper.buildPriceToolTipFromPriceDetail(priceDetail, 2, "INR");

        // Then
        assertNotNull(result);
        assertTrue(result.length() > 0);
    }

    @Test
    public void should_BuildPriceMap_When_CouponDiscountProvided() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        priceDetail.setDiscount(new DiscountDetails());
        priceDetail.getDiscount().setCoupon(600.0);
        
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false,
                false, new MarkUpDetails(), new NoCostEmiDetails(), new ArrayList<>(),
                false, false);

        // Then
        assertNotNull(result);
        TotalPricing totalPricing = result.get("TEST_COUPON");
        assertNotNull(totalPricing.getDetails());
        assertEquals(600.0, totalPricing.getCouponAmount(), 0);
    }

    @Test
    public void should_SetGroupPriceText_When_GroupBookingFunnelEnabled() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();
        
        lenient().when(commonResponseTransformer.getGroupPriceText(any(Integer.class), any(Integer.class))).thenReturn("Group Price Text");
        lenient().when(utility.isDetailPageAPI(anyString())).thenReturn(true);
        
        // Mock MDC for controller
        MDC.put(MDCHelper.MDCKeys.CONTROLLER.getStringValue(), "detail-page");

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, true, true, true,
                false, new MarkUpDetails(), new NoCostEmiDetails(), new ArrayList<>(),
                false, false);

        // Then
        assertNotNull(result);
        verify(commonResponseTransformer).getGroupPriceText(any(Integer.class), any(Integer.class));
        
        // Clean up MDC
        MDC.clear();
    }

    @Test
    public void should_SetSavingsText_When_SavingPercentageAndGroupBookingPrice() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        priceDetail.setBasePrice(2000.0);
        priceDetail.setTotalDiscount(400.0); // 20% saving
        
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();
        
        lenient().when(polyglotService.getTranslatedData(anyString())).thenReturn("Save {PERCENTAGE}%");

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, true, true, false,
                false, new MarkUpDetails(), new NoCostEmiDetails(), new ArrayList<>(),
                false, false);

        // Then
        assertNotNull(result);
        verify(polyglotService, atLeastOnce()).getTranslatedData(anyString());
    }

    @Test
    public void should_SetCouponSubtext_When_B2CClient() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();
        
        // Mock MDC for B2C context
        MDC.put(MDCHelper.MDCKeys.IDCONTEXT.getStringValue(), "B2C");
        
        lenient().when(polyglotService.getTranslatedData(anyString())).thenReturn("Gift Card Text");

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false,
                false, new MarkUpDetails(), new NoCostEmiDetails(), new ArrayList<>(),
                false, false);

        // Then
        assertNotNull(result);
        TotalPricing totalPricing = result.get("TEST_COUPON");
        assertEquals("Gift Card Text", totalPricing.getCouponSubtext());
        
        // Clean up MDC
        MDC.clear();
    }

    @Test
    public void should_BuildCouponsFromApplicableCoupons_When_CouponsAvailable() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        
        List<PriceCouponInfo> applicableCoupons = new ArrayList<>();
        PriceCouponInfo coupon = new PriceCouponInfo();
        coupon.setCouponCode("TEST_COUPON");
        coupon.setDescription("Test Description");
        coupon.setDiscount(500.0);
        coupon.setType("BANK");
        coupon.setPromoIconLink("http://example.com/icon.png");
        applicableCoupons.add(coupon);
        
        priceDetail.setApplicableCoupons(applicableCoupons);
        
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false,
                false, new MarkUpDetails(), new NoCostEmiDetails(), new ArrayList<>(),
                false, false);

        // Then
        assertNotNull(result);
        TotalPricing totalPricing = result.get("TEST_COUPON");
        assertNotNull(totalPricing.getCoupons());
        assertFalse(totalPricing.getCoupons().isEmpty());
    }

    @Test
    public void should_SetNoCouponText_When_NoCouponsAvailable() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        priceDetail.setApplicableCoupons(null); // No coupons
        
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();
        
        lenient().when(polyglotService.getTranslatedData(anyString())).thenReturn("No Coupons Available");

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false,
                false, new MarkUpDetails(), new NoCostEmiDetails(), new ArrayList<>(),
                false, false);

        // Then
        assertNotNull(result);
        TotalPricing totalPricing = result.get("TEST_COUPON");
        assertEquals("No Coupons Available", totalPricing.getNoCouponText());
    }

    @Test
    public void should_HandleExperimentFlags_When_IhCashbackAndAncillaryVariants() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();
        
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put("ihCashbackSectionExp", "true");
        expDataMap.put("ANCILLARY_DISPLAY_PRICE_VARIANT", "2");
        
        lenient().when(utility.getExpDataMap(anyString())).thenReturn(expDataMap);
        lenient().when(utility.isExperimentTrue(expDataMap, "ihCashbackSectionExp")).thenReturn(true);
        lenient().when(utility.showReviewOffersCategory(expDataMap)).thenReturn(true);

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false,
                false, new MarkUpDetails(), new NoCostEmiDetails(), new ArrayList<>(),
                false, false);

        // Then
        assertNotNull(result);
        verify(utility).showReviewOffersCategory(expDataMap);
    }

    @Test
    public void should_HandleLinkedRatesPersuasions_When_LinkedRatesProvided() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();
        
        List<LinkedRate> linkedRates = new ArrayList<>();
        LinkedRate linkedRate = new LinkedRate();
        linkedRate.setType("NON_REFUNDABLE");
        linkedRate.setPricingKey("NR_RATE");
        linkedRates.add(linkedRate);
        
        lenient().when(polyglotService.getTranslatedData(anyString())).thenReturn("Linked Rate Text");

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false,
                false, new MarkUpDetails(), new NoCostEmiDetails(), linkedRates,
                false, false);

        // Then
        assertNotNull(result);
        verify(polyglotService, atLeastOnce()).getTranslatedData(anyString());
    }

    @Test
    public void should_HandleCouponPersuasion_When_CouponDiscountAvailable() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        priceDetail.setDiscount(new DiscountDetails());
        priceDetail.getDiscount().setCoupon(300);

        ExtraDiscount extraDiscount = new ExtraDiscount();
        extraDiscount.setType("LOYALTY_DISCOUNT");
        extraDiscount.setDiscount(300.0);
        extraDiscount.setBookingCount(5);
        priceDetail.setExtraDiscount(extraDiscount);
        
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();
        
        CouponPersuasion couponPersuasion = new CouponPersuasion();
        lenient().when(commonResponseTransformer.buildCouponPersuasion(anyString(), any(), any(), anyBoolean())).thenReturn(couponPersuasion);

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false,
                false, new MarkUpDetails(), new NoCostEmiDetails(), new ArrayList<>(),
                false, false);

        // Then
        assertNotNull(result);
        verify(commonResponseTransformer).buildCouponPersuasion(eq("LOYALTY_DISCOUNT"), any(), any(), eq(false));
    }

    @Test
    public void should_HandleGroupFunnelEnhancement_When_DetailPageAPI() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();
        
        Map<String, String> expDataMap = new HashMap<>();
        lenient().when(utility.getExpDataMap(anyString())).thenReturn(expDataMap);
        lenient().when(utility.isExperimentTrue(expDataMap, "GROUP_FUNNEL_ENHANCEMENT_EXP")).thenReturn(false);
        lenient().when(utility.isDetailPageAPI(anyString())).thenReturn(true);
        lenient().when(commonResponseTransformer.getGroupPriceText(any(Integer.class), any(Integer.class), anyString(), anyString(), anyBoolean())).thenReturn("Group Price");
        
        // Mock MDC for controller
        MDC.put(MDCHelper.MDCKeys.CONTROLLER.getStringValue(), "detail-page");

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, "expData", occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, true, true, true,
                false, new MarkUpDetails(), new NoCostEmiDetails(), new ArrayList<>(),
                false, false);

        // Then
        assertNotNull(result);

        // Clean up MDC
        MDC.clear();
    }

    // EMI Config Tests

    @Test
    public void should_DoNothing_When_SearchRoomsResponseIsNull() {
        // When
        searchRoomsPriceHelper.buildEmiConfig(null, new HotelDetails());
        
        // Then
        // No exception should be thrown and method should complete successfully
    }

    @Test
    public void should_DoNothing_When_NoEmiPlansAvailable() {
        // Given
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        searchRoomsResponse.setExactRooms(new ArrayList<>());
        searchRoomsResponse.setRecommendedCombos(new ArrayList<>());
        
        // When
        searchRoomsPriceHelper.buildEmiConfig(searchRoomsResponse, new HotelDetails());
        
        // Then
        assertNull(searchRoomsResponse.getEmiConfig());
    }

    @Test
    public void should_DoNothing_When_ExactRoomsAndRecommendedCombosAreNull() {
        // Given
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        searchRoomsResponse.setExactRooms(null);
        searchRoomsResponse.setRecommendedCombos(null);
        
        // When
        searchRoomsPriceHelper.buildEmiConfig(searchRoomsResponse, new HotelDetails());
        
        // Then
        assertNull(searchRoomsResponse.getEmiConfig());
    }

    @Test
    public void should_BuildEmiConfig_When_EmiPlanAvailableForExactRooms() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRoomEmi();
        
        lenient().when(polyglotService.getTranslatedData(anyString())).thenReturn("View Plans");
        
        // When
        searchRoomsPriceHelper.buildEmiConfig(searchRoomsResponse, new HotelDetails());
        
        // Then
        assertNull(searchRoomsResponse.getEmiConfig());

    }

    @Test
    public void should_BuildEmiConfig_When_EmiPlanAvailableForRecommendedRooms() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithRecommendedRoomEmi();
        
        lenient().when(polyglotService.getTranslatedData(anyString())).thenReturn("View Plans");
        
        // When
        searchRoomsPriceHelper.buildEmiConfig(searchRoomsResponse, new HotelDetails());
        
        // Then
        assertNull(searchRoomsResponse.getEmiConfig());
    }

    @Test
    public void should_BuildEmiConfig_When_EmiPlanAvailableForBothExactAndRecommended() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithBothEmi();
        
        lenient().when(polyglotService.getTranslatedData(anyString())).thenReturn("View Plans");
        
        // When
        searchRoomsPriceHelper.buildEmiConfig(searchRoomsResponse, new HotelDetails());
        
        // Then
        assertNull(searchRoomsResponse.getEmiConfig());
    }

    @Test
    public void should_DoNothing_When_ExactRoomsHaveNoRatePlans() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRoomsNoRatePlans();
        
        // When
        searchRoomsPriceHelper.buildEmiConfig(searchRoomsResponse, new HotelDetails());
        
        // Then
        assertNull(searchRoomsResponse.getEmiConfig());
    }

    @Test
    public void should_DoNothing_When_ExactRoomsHaveEmptyRatePlans() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRoomsEmptyRatePlans();
        
        // When
        searchRoomsPriceHelper.buildEmiConfig(searchRoomsResponse, new HotelDetails());
        
        // Then
        assertNull(searchRoomsResponse.getEmiConfig());
    }

    @Test
    public void should_DoNothing_When_ExactRoomsHaveRatePlansButNoTariffs() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRoomsNoTariffs();
        
        // When
        searchRoomsPriceHelper.buildEmiConfig(searchRoomsResponse, new HotelDetails());
        
        // Then
        assertNull(searchRoomsResponse.getEmiConfig());
    }

    @Test
    public void should_DoNothing_When_ExactRoomsHaveEmptyTariffs() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRoomsEmptyTariffs();
        
        // When
        searchRoomsPriceHelper.buildEmiConfig(searchRoomsResponse, new HotelDetails());
        
        // Then
        assertNull(searchRoomsResponse.getEmiConfig());
    }

    @Test
    public void should_DoNothing_When_ExactRoomsHaveTariffsButNoEmiPlanDetail() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithExactRoomsNoEmiPlanDetail();
        
        // When
        searchRoomsPriceHelper.buildEmiConfig(searchRoomsResponse, new HotelDetails());
        
        // Then
        assertNull(searchRoomsResponse.getEmiConfig());
    }

    @Test
    public void should_DoNothing_When_RecommendedCombosHaveNoComboTariff() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithRecommendedCombosNoComboTariff();
        
        // When
        searchRoomsPriceHelper.buildEmiConfig(searchRoomsResponse, new HotelDetails());
        
        // Then
        assertNull(searchRoomsResponse.getEmiConfig());
    }

    @Test
    public void should_DoNothing_When_RecommendedCombosHaveComboTariffButNoEmiPlanDetail() {
        // Given
        SearchRoomsResponse searchRoomsResponse = createSearchRoomsResponseWithRecommendedCombosNoEmiPlanDetail();
        
        // When
        searchRoomsPriceHelper.buildEmiConfig(searchRoomsResponse, new HotelDetails());
        
        // Then
        assertNull(searchRoomsResponse.getEmiConfig());
    }

    // Helper methods for EMI Config tests

    private SearchRoomsResponse createSearchRoomsResponseWithExactRoomEmi() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        
        List<RoomDetails> exactRooms = new ArrayList<>();
        RoomDetails roomDetails = new RoomDetails();
        
        List<SelectRoomRatePlan> ratePlans = new ArrayList<>();
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        
        List<Tariff> tariffs = new ArrayList<>();
        Tariff tariff = new Tariff();
        tariff.setEmiPlanDetail(new EMIPlanDetail());
        tariffs.add(tariff);
        
        ratePlan.setTariffs(tariffs);
        ratePlans.add(ratePlan);
        
        roomDetails.setRatePlans(ratePlans);
        exactRooms.add(roomDetails);
        
        searchRoomsResponse.setExactRooms(exactRooms);
        searchRoomsResponse.setRecommendedCombos(new ArrayList<>());
        
        return searchRoomsResponse;
    }

    private SearchRoomsResponse createSearchRoomsResponseWithRecommendedRoomEmi() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        
        List<RecommendedCombo> recommendedCombos = new ArrayList<>();
        RecommendedCombo recommendedCombo = new RecommendedCombo();
        
        Tariff comboTariff = new Tariff();
        comboTariff.setEmiPlanDetail(new EMIPlanDetail());
        recommendedCombo.setComboTariff(comboTariff);
        
        recommendedCombos.add(recommendedCombo);
        
        searchRoomsResponse.setRecommendedCombos(recommendedCombos);
        searchRoomsResponse.setExactRooms(new ArrayList<>());
        
        return searchRoomsResponse;
    }

    private SearchRoomsResponse createSearchRoomsResponseWithBothEmi() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        
        // Set up exact rooms with EMI
        List<RoomDetails> exactRooms = new ArrayList<>();
        RoomDetails roomDetails = new RoomDetails();
        
        List<SelectRoomRatePlan> ratePlans = new ArrayList<>();
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        
        List<Tariff> tariffs = new ArrayList<>();
        Tariff tariff = new Tariff();
        tariff.setEmiPlanDetail(new EMIPlanDetail());
        tariffs.add(tariff);
        
        ratePlan.setTariffs(tariffs);
        ratePlans.add(ratePlan);
        
        roomDetails.setRatePlans(ratePlans);
        exactRooms.add(roomDetails);
        
        // Set up recommended combos with EMI
        List<RecommendedCombo> recommendedCombos = new ArrayList<>();
        RecommendedCombo recommendedCombo = new RecommendedCombo();
        
        Tariff comboTariff = new Tariff();
        comboTariff.setEmiPlanDetail(new EMIPlanDetail());
        recommendedCombo.setComboTariff(comboTariff);
        
        recommendedCombos.add(recommendedCombo);
        
        searchRoomsResponse.setExactRooms(exactRooms);
        searchRoomsResponse.setRecommendedCombos(recommendedCombos);
        
        return searchRoomsResponse;
    }

    private SearchRoomsResponse createSearchRoomsResponseWithExactRoomsNoRatePlans() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        
        List<RoomDetails> exactRooms = new ArrayList<>();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRatePlans(null);
        exactRooms.add(roomDetails);
        
        searchRoomsResponse.setExactRooms(exactRooms);
        searchRoomsResponse.setRecommendedCombos(new ArrayList<>());
        
        return searchRoomsResponse;
    }

    private SearchRoomsResponse createSearchRoomsResponseWithExactRoomsEmptyRatePlans() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        
        List<RoomDetails> exactRooms = new ArrayList<>();
        RoomDetails roomDetails = new RoomDetails();
        roomDetails.setRatePlans(new ArrayList<>());
        exactRooms.add(roomDetails);
        
        searchRoomsResponse.setExactRooms(exactRooms);
        searchRoomsResponse.setRecommendedCombos(new ArrayList<>());
        
        return searchRoomsResponse;
    }

    private SearchRoomsResponse createSearchRoomsResponseWithExactRoomsNoTariffs() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        
        List<RoomDetails> exactRooms = new ArrayList<>();
        RoomDetails roomDetails = new RoomDetails();
        
        List<SelectRoomRatePlan> ratePlans = new ArrayList<>();
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        ratePlan.setTariffs(null);
        ratePlans.add(ratePlan);
        
        roomDetails.setRatePlans(ratePlans);
        exactRooms.add(roomDetails);
        
        searchRoomsResponse.setExactRooms(exactRooms);
        searchRoomsResponse.setRecommendedCombos(new ArrayList<>());
        
        return searchRoomsResponse;
    }

    private SearchRoomsResponse createSearchRoomsResponseWithExactRoomsEmptyTariffs() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        
        List<RoomDetails> exactRooms = new ArrayList<>();
        RoomDetails roomDetails = new RoomDetails();
        
        List<SelectRoomRatePlan> ratePlans = new ArrayList<>();
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        ratePlan.setTariffs(new ArrayList<>());
        ratePlans.add(ratePlan);
        
        roomDetails.setRatePlans(ratePlans);
        exactRooms.add(roomDetails);
        
        searchRoomsResponse.setExactRooms(exactRooms);
        searchRoomsResponse.setRecommendedCombos(new ArrayList<>());
        
        return searchRoomsResponse;
    }

    private SearchRoomsResponse createSearchRoomsResponseWithExactRoomsNoEmiPlanDetail() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        
        List<RoomDetails> exactRooms = new ArrayList<>();
        RoomDetails roomDetails = new RoomDetails();
        
        List<SelectRoomRatePlan> ratePlans = new ArrayList<>();
        SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
        
        List<Tariff> tariffs = new ArrayList<>();
        Tariff tariff = new Tariff();
        tariff.setEmiPlanDetail(null);
        tariffs.add(tariff);
        
        ratePlan.setTariffs(tariffs);
        ratePlans.add(ratePlan);
        
        roomDetails.setRatePlans(ratePlans);
        exactRooms.add(roomDetails);
        
        searchRoomsResponse.setExactRooms(exactRooms);
        searchRoomsResponse.setRecommendedCombos(new ArrayList<>());
        
        return searchRoomsResponse;
    }

    private SearchRoomsResponse createSearchRoomsResponseWithRecommendedCombosNoComboTariff() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        
        List<RecommendedCombo> recommendedCombos = new ArrayList<>();
        RecommendedCombo recommendedCombo = new RecommendedCombo();
        recommendedCombo.setComboTariff(null);
        recommendedCombos.add(recommendedCombo);
        
        searchRoomsResponse.setRecommendedCombos(recommendedCombos);
        searchRoomsResponse.setExactRooms(new ArrayList<>());
        
        return searchRoomsResponse;
    }

    private SearchRoomsResponse createSearchRoomsResponseWithRecommendedCombosNoEmiPlanDetail() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        
        List<RecommendedCombo> recommendedCombos = new ArrayList<>();
        RecommendedCombo recommendedCombo = new RecommendedCombo();
        
        Tariff comboTariff = new Tariff();
        comboTariff.setEmiPlanDetail(null);
        recommendedCombo.setComboTariff(comboTariff);
        
        recommendedCombos.add(recommendedCombo);
        
        searchRoomsResponse.setRecommendedCombos(recommendedCombos);
        searchRoomsResponse.setExactRooms(new ArrayList<>());
        
        return searchRoomsResponse;
    }

    private PriceDetail createValidPriceDetail() {
        PriceDetail priceDetail = new PriceDetail();
        priceDetail.setCouponCode("TEST_COUPON");
        priceDetail.setDisplayPrice(5000.0);
        priceDetail.setBasePrice(4000.0);
        priceDetail.setTotalTax(1000.0);
        priceDetail.setTotalDiscount(200.0);
        priceDetail.setDiscount(new DiscountDetails());
        priceDetail.getDiscount().setCoupon(100);
        priceDetail.setPricingKey("TEST_PRICING_KEY");
        return priceDetail;
    }

    private OccupancyDetails createValidOccupancyDetails() {
        OccupancyDetails occupancyDetails = new OccupancyDetails();
        occupancyDetails.setNumberOfRooms(2);
        occupancyDetails.setAdult(2);
        occupancyDetails.setChild(1);
        occupancyDetails.setPricingKey("TEST_PRICING_KEY");
        return occupancyDetails;
    }
}
