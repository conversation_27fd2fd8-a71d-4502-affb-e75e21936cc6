package com.mmt.hotels.clientgateway.transformer.response.desktop;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.request.StaticDetailRequest;
import com.mmt.hotels.clientgateway.response.LocationDetail;
import com.mmt.hotels.clientgateway.response.PriceDetail;
import com.mmt.hotels.clientgateway.response.ReviewSummary;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.response.staticdetail.LiteResponseTravellerImage;
import com.mmt.hotels.clientgateway.response.staticdetail.MediaInfo;
import com.mmt.hotels.clientgateway.response.staticdetail.MediaV2;
import com.mmt.hotels.clientgateway.response.staticdetail.StaticDetailResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.StaticDetailResponseTransformer;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.request.upsell.UpsellHotelDetailResponse;
import com.mmt.hotels.model.response.flyfish.*;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysTooltip;
import com.mmt.hotels.model.response.staticdata.*;
import com.mmt.hotels.model.response.staticdata.Image360.Image360;
import com.mmt.hotels.pojo.CalendarAvailability.CalendarCriteria;
import com.mmt.hotels.pojo.request.detail.mob.CBFlyFishSummaryResponse;
import com.mmt.hotels.pojo.response.detail.HotelDetailWrapperResponse;
import com.mmt.hotels.pojo.response.detail.PlacesResponse;
import com.mmt.hotels.pojo.response.detail.RoomInfoData;
import com.mmt.hotels.pojo.response.detail.placesapi.Category;
import com.mmt.hotels.pojo.response.detail.placesapi.CategoryDatum;
import com.mmt.model.RoomInfo;
import com.mmt.propertymanager.config.PropertyManager;
import junit.framework.Assert;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.springframework.test.util.ReflectionTestUtils;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.assertNull;
@RunWith(MockitoJUnitRunner.class)
public class StaticDetailResponseTransformerDesktopTest {

    @Mock
    ObjectMapperUtil objectMapperUtil;
    @Mock
    CommonResponseTransformer commonResponseTransformer;
    @Mock
    private PropertyManager propertyManager;
    @Mock
    private PolyglotService polyglotService;
    @Mock
    private PolyglotHelper polyglotHelper;
    @InjectMocks
    private StaticDetailResponseTransformerDesktop staticDetailResponseTransformerDesktop;
    @Mock
    private Utility utility;

    @Mock
    StaticDetailResponseTransformer staticDetailResponseTransformer;

    private static final String TEST_COUNTRY = "INDIA";
    private static final String TEST_REVIEW_TEXT = "Great reviews from {NATIONALITY}!";
    private static final String TEST_TEXT_COLOR = "#A5572A";
    private static final String TEST_CTA_TEXT = "See reviews from {NATIONALITY}!";

    @Before
    public void init(){
       ReflectionTestUtils.setField(staticDetailResponseTransformerDesktop, "valueStaysTooltipDom", new ValueStaysTooltip());
       ReflectionTestUtils.setField(staticDetailResponseTransformerDesktop, "valueStaysTooltipIntl", new ValueStaysTooltip());
       ReflectionTestUtils.setField(staticDetailResponseTransformer, "view360IconUrl", "www.sample360Url.com");
    }
    @Test
    public void addTitleDataTest() {
        HotelResult hotelResult = new HotelResult();
        System.out.println("staticDetailResponseTransformerDesktop object is "+ staticDetailResponseTransformerDesktop);
        staticDetailResponseTransformerDesktop.addTitleData(hotelResult, "IN",true);
    }

    @Test
    public void testSetLuxeIcon() {
        assertNotNull(staticDetailResponseTransformerDesktop.getLuxeIcon());
    }

    @Test
    public void testConvertStaticDetailResponse() throws JsonProcessingException {
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenAnswer(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                return invocationOnMock.getArgument(0);
            }
        });
        HotelDetailWrapperResponse hotelDetailWrapperResponse = new HotelDetailWrapperResponse();
        hotelDetailWrapperResponse.setHotelResult(new com.mmt.hotels.model.response.staticdata.HotelResult());
        CalendarCriteria calendarCriteria = new CalendarCriteria();
        hotelDetailWrapperResponse.getHotelResult().setCategoryIcon("categoryIcon");
        hotelDetailWrapperResponse.getHotelResult().setCalendarCriteria(calendarCriteria);
        hotelDetailWrapperResponse.getHotelResult().setCategories(new HashSet<String>());
        hotelDetailWrapperResponse.getHotelResult().setLatitude(10d);
        hotelDetailWrapperResponse.getHotelResult().setLongitude(10d);
        hotelDetailWrapperResponse.getHotelResult().setSignatureAmenities(Collections.singletonList(new AmenitiesList()));

        List<VideoInfo> hotelVideos = new ArrayList<VideoInfo>();
        VideoInfo videoInfo = new VideoInfo();
        videoInfo.setUrl("");
        hotelVideos.add(videoInfo );
        hotelDetailWrapperResponse.getHotelResult().setHotelVideos(hotelVideos );

        Map<String, ArrayList<Type>> users = new HashMap<String, ArrayList<Type>>();
        ArrayList<Type> types = new ArrayList<Type>();
        Type type = new Type();
        types.add(type);
        users.put("hotel", types);
        hotelDetailWrapperResponse.getHotelResult().setUsers(users );

        hotelDetailWrapperResponse.getHotelResult().setHouseRules(buildHouseRules());

        hotelDetailWrapperResponse.getHotelResult().setLuxe(true);

        hotelDetailWrapperResponse.setHotelImage(new HotelImage());
        hotelDetailWrapperResponse.getHotelImage().setImageDetails(new ImageType());
        hotelDetailWrapperResponse.getHotelImage().getImageDetails().setProfessional(new HashMap<String, List<ProfessionalImageEntity>>());
        hotelDetailWrapperResponse.getHotelImage().getImageDetails().getProfessional().put("H", new ArrayList<ProfessionalImageEntity>());
        hotelDetailWrapperResponse.getHotelImage().getImageDetails().getProfessional().get("H").add(new ProfessionalImageEntity());
        hotelDetailWrapperResponse.getHotelImage().getImageDetails().getProfessional().put("R", new ArrayList<>());
        hotelDetailWrapperResponse.getHotelImage().getImageDetails().getProfessional().get("R").add(new ProfessionalImageEntity());
        hotelDetailWrapperResponse.getHotelImage().getImageDetails().setTraveller(new HashMap<String, List<TravellerImageEntity>>());
        hotelDetailWrapperResponse.getHotelImage().getImageDetails().getTraveller().put("H", new ArrayList<TravellerImageEntity>());
        hotelDetailWrapperResponse.getHotelImage().getImageDetails().getTraveller().get("H").add(new TravellerImageEntity());


        hotelDetailWrapperResponse.setPointOfInterests(new ArrayList<PointOfInterest>());
        hotelDetailWrapperResponse.getPointOfInterests().add(new PointOfInterest());
        hotelDetailWrapperResponse.getPointOfInterests().get(0).setAerialDistance(10d);
        hotelDetailWrapperResponse.getPointOfInterests().get(0).setDrivingDistance(10d);
        hotelDetailWrapperResponse.getPointOfInterests().get(0).setDrivingTimeInSeconds(10d);
        hotelDetailWrapperResponse.getPointOfInterests().get(0).setLatitude(10d);
        hotelDetailWrapperResponse.getPointOfInterests().get(0).setLongitude(10d);
        hotelDetailWrapperResponse.getPointOfInterests().get(0).setPriority(10d);
        hotelDetailWrapperResponse.getPointOfInterests().get(0).setCategories(new ArrayList<>());
        hotelDetailWrapperResponse.getPointOfInterests().get(0).getCategories().add("abc");
        hotelDetailWrapperResponse.setFlyfishSummaryResponse(new CBFlyFishSummaryResponse());
        hotelDetailWrapperResponse.getFlyfishSummaryResponse().setSummary(new HashMap<>());
        StaffInfo staffInfo = new StaffInfo();
        Staff staff = new Staff();
        staff.setData(Arrays.asList(new StaffData()));
        staffInfo.setHost(staff);
        List<CommonRules> commonRules = new ArrayList<>();
        CommonRules commonRules1 = new CommonRules();
        commonRules1.setSubCategories(new ArrayList<>());
        commonRules1.getSubCategories().add(new CommonRules());
        commonRules.add(commonRules1);
        hotelDetailWrapperResponse.getHotelResult().setFoodAndDiningRules(commonRules);
        hotelDetailWrapperResponse.getHotelResult().setMustReadRules(Arrays.asList("This is must read rule"));
        hotelDetailWrapperResponse.getHotelResult().setStaffInfo(staffInfo);
        hotelDetailWrapperResponse.setRoomInfoData(new RoomInfoData());
        hotelDetailWrapperResponse.getRoomInfoData().setRoomInfoMap(new HashMap<>());
        hotelDetailWrapperResponse.getRoomInfoData().getRoomInfoMap().put("test", new RoomInfo());

        String ratingJson = getRatingJson();
        String abc = "                   \"altAccoSummary\": [\n" +
                "                        {\n" +
                "                            \"concept\": \"Facilities\",\n" +
                "                            \"value\": 4.0,\n" +
                "                            \"reviewCount\": 118\n" +
                "                        }\n" +
                "                    ]";
        hotelDetailWrapperResponse.getFlyfishSummaryResponse().getSummary().put(OTA.MMT, new ObjectMapper().readTree(ratingJson));
        ContextualizeReviews contextualizeReviews = new ContextualizeReviews();
        ContextualReviewData contextualReviewData = new ContextualReviewData();
        contextualReviewData.iconUrl = "url";
        List<ContextualReviewData> contextualReviewDataList = new ArrayList<>();
        contextualReviewDataList.add(contextualReviewData);
        contextualizeReviews.common = contextualReviewDataList;
        contextualizeReviews.highlighted = contextualReviewDataList;
        hotelDetailWrapperResponse.getFlyfishSummaryResponse().setContextualizedReviews(contextualizeReviews);
        objectMapperUtil.init();
        StaticDetailRequest staticDetailRequest = new StaticDetailRequest();
        staticDetailRequest.setFilterCriteria(new ArrayList<>());
        ReflectionTestUtils.setField(staticDetailResponseTransformerDesktop, "mmtValueStaysCategoryIconUrlDesktop", "https://promos.makemytrip.com/Hotels_product/Value_Stays/v2/logo/ValueStays-2.png");
        StaticDetailResponse staticDetailResponse = staticDetailResponseTransformerDesktop.convertStaticDetailResponse(hotelDetailWrapperResponse, "DESKTOP", staticDetailRequest, null);
        Assert.assertNotNull(staticDetailResponse);
        hotelDetailWrapperResponse.setHotelCompareResponse(new UpsellHotelDetailResponse());
        staticDetailResponse = staticDetailResponseTransformerDesktop.convertStaticDetailResponse(hotelDetailWrapperResponse, "DESKTOP", staticDetailRequest, null);
        Assert.assertNotNull(staticDetailResponse);
        Assert.assertNotNull(staticDetailResponse.getHotelDetails());
        Assert.assertEquals("https://promos.makemytrip.com/Hotels_product/Value_Stays/v2/logo/ValueStays-2.png", staticDetailResponse.getHotelDetails().getCategoryIcon());

        //Test for beachFront properties static-details changes
        staticDetailResponse = staticDetailResponseTransformerDesktop.convertStaticDetailResponse(hotelDetailWrapperResponse, "PWA", staticDetailRequest, null);
        Assert.assertNotNull(staticDetailResponse);
        Assert.assertNotNull(staticDetailResponse.getHotelDetails());
        Assert.assertNull(staticDetailResponse.getHotelDetails().getCategoryUspDetailsText());

        hotelDetailWrapperResponse.getHotelResult().setCategoryUsp("BeachFront");
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Beachfront details page text");
        staticDetailResponse = staticDetailResponseTransformerDesktop.convertStaticDetailResponse(hotelDetailWrapperResponse, "PWA", staticDetailRequest, null);
        Assert.assertNotNull(staticDetailResponse);
        Assert.assertNotNull(staticDetailResponse.getHotelDetails());
        Assert.assertEquals(staticDetailResponse.getHotelDetails().getCategoryUspDetailsText(), "Beachfront details page text");

    }


    private HouseRules buildHouseRules() {
        HouseRules houseRule = new HouseRules();
        ChildExtraBedPolicy childExtraBedPolicy = new ChildExtraBedPolicy();
        List<PolicyRules> policyRules = new ArrayList<PolicyRules>();
        PolicyRules plcyRule = new PolicyRules();
        Set<ExtraBedRules> extraBedTerms = new HashSet<>();
        ExtraBedRules extrabed = new ExtraBedRules();
        extraBedTerms.add(extrabed );
        plcyRule.setExtraBedTerms(extraBedTerms );

        policyRules.add(plcyRule );
        childExtraBedPolicy.setPolicyRules(policyRules );
        houseRule.setChildExtraBedPolicy(childExtraBedPolicy );
        List<CommonRules> commonRules = new ArrayList<CommonRules>();
        CommonRules coomonRule = new CommonRules();
        List<Rule> rules = new ArrayList<Rule>();
        Rule rule = new Rule();
        rules.add(rule );
        coomonRule.setRules(rules );
        commonRules.add(coomonRule);
        houseRule.setCommonRules(commonRules );
        List<ChildExtraBedPolicy> extraBedPolicyList = new ArrayList<ChildExtraBedPolicy>();
        extraBedPolicyList.add(childExtraBedPolicy);
        houseRule.setExtraBedPolicyList(extraBedPolicyList );
        houseRule.setOtherInfo(commonRules);
        return houseRule;
    }

    private String getRatingJson() {
        return "{\n" +
                "  \"cumulativeRating\": 4.4,\n" +
                "  \"totalReviewsCount\": 370,\n" +
                "  \"totalRatingCount\": 589,\n" +
                "  \"ratingBreakup\": {\n" +
                "    \"1\": 21,\n" +
                "    \"2\": 10,\n" +
                "    \"3\": 30,\n" +
                "    \"4\": 151,\n" +
                "    \"5\": 377\n" +
                "  },\n" +
                "  \"reviewBreakup\": {\n" +
                "    \"1\": 14,\n" +
                "    \"2\": 7,\n" +
                "    \"3\": 19,\n" +
                "    \"4\": 89,\n" +
                "    \"5\": 241\n" +
                "  },\n" +
                "  \"best\": [\n" +
                "    {\n" +
                "      \"publishDate\": \"Mar 29, 2021\",\n" +
                "      \"travellerName\": \"Rajat Maurya\",\n" +
                "      \"title\": \"Nice place to spend weekend\",\n" +
                "      \"rating\": 4,\n" +
                "      \"reviewText\": \"Perfect place to spend some quality time\",\n" +
                "      \"id\": \"P4Q2S5OIMYHNNC93F60FL4ADNH95FPGB03IPZUXDBKJA59ALRSPJH28CY2ARYU4GSDRFJLIXETX6\",\n" +
                "      \"travelType\": \"SOLO\",\n" +
                "      \"crawledData\": false\n" +
                "    }\n" +
                "  ],\n" +
                "  \"travellerRatingSummary\": {\n" +
                "    \"hotelSummary\": [\n" +
                "      {\n" +
                "        \"concept\": \"Safety and Hygiene\",\n" +
                "        \"value\": 4.5,\n" +
                "        \"show\": true,\n" +
                "        \"heroTag\": true,\n" +
                "        \"reviewCount\": 298\n" +
                "      },\n" +
                "      {\n" +
                "        \"concept\": \"Location\",\n" +
                "        \"displayText\": \"सस्थान/जगह\",\n" +
                "        \"value\": 4.7,\n" +
                "        \"show\": false,\n" +
                "        \"reviewCount\": 121,\n" +
                "        \"subConcepts\": [\n" +
                "          {\n" +
                "            \"sentiment\": \"POSITIVE\",\n" +
                "            \"subConcept\": \"Location\",\n" +
                "            \"displayText\": \"सस्थान/जगह\",\n" +
                "            \"relatedReviewCount\": 40,\n" +
                "            \"tagType\": \"BASE\",\n" +
                "            \"priorityScore\": 40\n" +
                "          }\n" +
                "        ]\n" +
                "      }\n" +
                "    ],\n" +
                "                   \"altAccoSummary\": [\n" +
                "                        {\n" +
                "                            \"concept\": \"Facilities\",\n" +
                "                            \"value\": 4.0,\n" +
                "                            \"reviewCount\": 118\n" +
                "                        }\n" +
                "                    ]," +
                "    \"roomSummary\": {\n" +
                "      \"309\": {\n" +
                "        \"cumulativeRating\": 0\n" +
                "      }\n" +
                "    }\n" +
                "  },\n" +
                "  \"crawledData\": false,\n" +
                "  \"cityCode\": \"DEL\",\n" +
                "  \"images\": [\n" +
                "    {\n" +
                "      \"imgUrl\": \"//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/flyfish/raw/NH7308534571632/QS1042/QS1042-Q1/1570874747454.jpeg\",\n" +
                "      \"mmtTagList\": [\n" +
                "        \"Pool\",\n" +
                "        \"Outdoors\",\n" +
                "        \"Facade\"\n" +
                "      ],\n" +
                "      \"reviewId\": \"PR2ZH5ZIMOHP4S9DUJ9TRQBKVSDEA8XBGJH2QFO0I5OS22CDRCP5H2NC3ZHZMUJNS9RF3RIXYT2R\",\n" +
                "      \"travellerName\": \"ashish golcha\",\n" +
                "      \"reviewDate\": 1570874817000,\n" +
                "      \"imgTag\": \"Pool\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"travelTypes\": [\n" +
                "    \"COUPLE\"\n" +
                "  ],\n" +
                "\"travelTypeList\": [\n" +
                "   {\n" +
                "       \"travelType\": \"COUPLE\",\n" +
                "       \"displayText\": \"कपल/जोड़ा\"\n" +
                "   }\n" +
                "   ],\n" +
                "  \"sortingCriterion\": [\n" +
                "    \"Latest first\"\n" +
                "  ],\n" +
                "\"sortingCriterionList\": [\n" +
                "   {\n" +
                "       \"criteriaType\": \"Latest first\",\n" +
                "       \"displayText\": \"नवीनतम पहलेा\"\n" +
                "   }\n" +
                "   ],\n" +
                "  \"mmtImageTags\": [\n" +
                "    {\n" +
                "      \"name\": \"Safety & Hygiene\",\n" +
                "      \"count\": 23\n" +
                "    },\n" +
                "    {\n" +
                "      \"name\": \"Outdoors\",\n" +
                "      \"count\": 22\n" +
                "    }\n" +
                "  ],\n" +
                "  \"imageTypes\": [\n" +
                "    \"Room\"\n" +
                "  ],\n" +
                "  \"ratingText\": \"Excellent\",\n" +
                "  \"postLockdownData\": {\n" +
                "    \"rating\": 4.4,\n" +
                "    \"totalReviewCount\": 305,\n" +
                "    \"ratingCount\": 90,\n" +
                "    \"textCount\": 177,\n" +
                "    \"imageCount\": 7,\n" +
                "    \"imageTextCount\": 31\n" +
                "  },\n" +
                "  \"bestReviewTitle\": \"Top Reviews\",\n" +
                "  \"selectedCategory\": \"DEFAULT\"\n" +
                "}";
    }

    @Test
    public void prepareSpokenLanguagesString_test() {
        List<String> list = new ArrayList<>();
        list.add("Hindi");
        list.add("English");
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformerDesktop,"prepareSpokenLanguagesString", list);
    }


    @Test
    public void spaceNameToImageEntityMapTest(){
        List<TravellerImageEntity> travelerList = new ArrayList<>();
        TravellerImageEntity travellerImageEntity = new TravellerImageEntity();
        travellerImageEntity.setTravellerName("ankit");
        travellerImageEntity.setDate("10-02-2022");
        travellerImageEntity.setUrl("abc.jpg");
        travellerImageEntity.setImageFilterInfo("OTHERS");
        travelerList.add(travellerImageEntity);

        Map<String, List<TravellerImageEntity>> spaceNameToImageEntityMap = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,"spaceNameToImageEntityMap",travelerList);
        assertNotNull(spaceNameToImageEntityMap);
        assertTrue(spaceNameToImageEntityMap.containsKey("OTHERS"));
        assertFalse(spaceNameToImageEntityMap.containsKey("abc"));
    }

    @Test
    public void createTagsFromTravellerImagesTest(){
        List<TravellerImageEntity> travelerList = new ArrayList<>();
        TravellerImageEntity travellerImageEntity = new TravellerImageEntity();
        travellerImageEntity.setTravellerName("ankit");
        travellerImageEntity.setDate("10-02-2022");
        travellerImageEntity.setUrl("abc.jpg");
        travellerImageEntity.setImageFilterInfo("OTHERS");
        travelerList.add(travellerImageEntity);
        ReflectionTestUtils.setField(staticDetailResponseTransformer, "utility", utility);
        Map<String, List<TravellerImageEntity>> spaceNameToImageEntityMap = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,"spaceNameToImageEntityMap",travelerList);

        List<Tag> tags = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,"createTagsFromTravellerImages",spaceNameToImageEntityMap, false);

        assertNotNull(tags);
        assertEquals(1,tags.size());

    }

    @Test
    public void buildMediaV2Test(){
        HotelImage hotelImage = new HotelImage();
        hotelImage.setHotelId("123");
        ImageType imageType = new ImageType();

        List<TravellerImageEntity> travelerList = new ArrayList<>();
        TravellerImageEntity travellerImageEntity = new TravellerImageEntity();
        travellerImageEntity.setTravellerName("ankit");
        travellerImageEntity.setDate("10-02-2022");
        travellerImageEntity.setUrl("abc.jpg");
        travellerImageEntity.setImageFilterInfo("Others");
        travelerList.add(travellerImageEntity);

        TravellerImageEntity travellerImageEntity1 = new TravellerImageEntity();
        travellerImageEntity1.setTravellerName("maxwell");
        travellerImageEntity1.setDate("07-11-2023");
        travellerImageEntity1.setUrl("xyz.jpg");
        travellerImageEntity1.setImageFilterInfo("Room");
        travelerList.add(travellerImageEntity1);

        TravellerImageEntity travellerImageEntity2 = new TravellerImageEntity();
        travellerImageEntity2.setTravellerName("elon musk");
        travellerImageEntity2.setDate("07-11-2023");
        travellerImageEntity2.setUrl("xyz.jpg");
        travellerImageEntity2.setImageFilterInfo("Facade");
        travelerList.add(travellerImageEntity2);

        imageType.setTraveller(new HashMap<>());
        imageType.getTraveller().put("H", travelerList);

        Tag tag  = new Tag();
        tag.setName("room");
        Subtag subtag = new Subtag();
        subtag.setName("xyz");
        subtag.setSpaceId("efg");
        ImageData imageData = new ImageData();
        imageData.setThumbnailURL("abc");
        imageData.setTitle("area");
        imageData.setDescription("abc");
        imageData.setRoomCode("123");
        imageData.setUrl("xyz.jpg");
        imageData.setMediaType("IMAGE");
        subtag.setData(new ArrayList<ImageData>(){{add(imageData);}});

        tag.setSubtags(new ArrayList<Subtag>(){{add(subtag);}});

        imageType.setProfessionalV2(new HashMap<>());
        imageType.getProfessionalV2().put("H", new ArrayList<Tag>(){{add(tag);}});

        hotelImage.setImageDetails(imageType);

        LinkedHashSet<String> imageTagsOrder = new LinkedHashSet<>();
        imageTagsOrder.add("Facade");
        imageTagsOrder.add("Room");
        imageTagsOrder.add("Washroom");
        imageTagsOrder.add("Restaurant");
        imageTagsOrder.add("OutDoors");
        ReflectionTestUtils.setField(staticDetailResponseTransformer, "utility", utility);
        MediaV2 mediaV2 = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,"buildMediaV2",hotelImage,"", imageTagsOrder,true,false,"ANDROID",false, false);

        assertNotNull(mediaV2);

        assertNotNull(mediaV2.getTraveller());
        assertNotNull(mediaV2.getHotel());

        List<Tag> tags = mediaV2 != null && mediaV2.getTraveller() != null ? mediaV2.getTraveller().getTags() : new ArrayList<>();

        if(CollectionUtils.isNotEmpty(tags)){
            assertEquals("Others",tags.get(tags.size()-1).getName());
            assertEquals("Facade",tags.get(0).getName());
        }

        //Tests for 360 Images
        Map<String, List<Image360>> images360 = new HashMap<>();
        List<Image360> image360List = new ArrayList<>();
        Image360 image360 = new Image360();
        image360.setImageUrl("abc.jpg");
        image360.setThumbnail("xyz.jpg");
        image360List.add(image360);
        images360.put("H",image360List);
        imageType.setImages360(images360);
        mediaV2 = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,"buildMediaV2",hotelImage,"", imageTagsOrder,false,false,"ANDROID",false, false);

        assertNotNull(mediaV2);
        assertEquals("www.sample360Url.com",mediaV2.getView360().getCtaIcon());
        assertEquals(image360List, mediaV2.getView360().getImages());
        assertEquals("abc.jpg", mediaV2.getView360().getImages().get(0).getImageUrl());
        assertEquals("xyz.jpg", mediaV2.getView360().getImages().get(0).getThumbnail());
    }

    @Test
    public void sortTravellerTagsBasedImageTagsOrderTest(){

        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,"sortTravellerTagsBasedImageTagsOrder",null,null);

        List<String> imageTagsOrder = new ArrayList<>();

        imageTagsOrder.add("Room");
        imageTagsOrder.add("Washroom");
        imageTagsOrder.add("Restaurant");
        imageTagsOrder.add("Facade");
        imageTagsOrder.add("OutDoors");
        imageTagsOrder.add("Gym");

        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,"sortTravellerTagsBasedImageTagsOrder",null,imageTagsOrder);

        List<Tag> tags = new ArrayList<>();
        Tag tag1 = new Tag();
        tag1.setName("Restaurant");
        tags.add(tag1);
        Tag tag2 = new Tag();
        tag2.setName("Room");
        tags.add(tag2);
        Tag tag3 = new Tag();
        tag3.setName("Others");
        tags.add(tag3);
        Tag tag4 = new Tag();
        tag4.setName("OutDoors");
        tags.add(tag4);

        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,"sortTravellerTagsBasedImageTagsOrder",tags,null);

        assertEquals("OutDoors",tags.get(tags.size()-1).getName());

        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,"sortTravellerTagsBasedImageTagsOrder",tags,imageTagsOrder);

        assertEquals("Others",tags.get(tags.size()-1).getName());
        assertEquals("Room",tags.get(0).getName());

    }

    @Test
    public void testGetMediaV2HotelMediaListCount() {
        // Arrange
        List<Tag> tags = new ArrayList<>();
        Tag tag = new Tag();
        Subtag subtag = new Subtag();
        ImageData imageData = new ImageData();
        imageData.setMediaType("IMAGE");
        imageData.setUrl("Test URL");
        subtag.setData(Arrays.asList(imageData, imageData, imageData)); // 3 images
        tag.setSubtags(Arrays.asList(subtag, subtag)); // 2 subtags, total 6 images
        tags.add(tag);
        tags.add(tag); // 2 tags, total 12 images

        // Act
        int result = staticDetailResponseTransformer.getMediaV2HotelMediaListCount(tags);
    }


    @Test
    public void testGetMediaV2TravellerMediaList() {
        // Arrange
        List<Tag> tags = new ArrayList<>();
        Tag tag = new Tag();
        Subtag subtag = new Subtag();
        ImageData imageData = new ImageData();
        imageData.setMediaType("IMAGE");
        imageData.setUrl("Test URL");
        subtag.setData(Arrays.asList(imageData, imageData, imageData)); // 3 images
        tag.setSubtags(Arrays.asList(subtag, subtag)); // 2 subtags, total 6 images
        tags.add(tag);
        tags.add(tag); // 2 tags, total 12 images

        // Act
        List<LiteResponseTravellerImage> result = staticDetailResponseTransformer.getMediaV2TravellerMediaList(tags);

        // Assert
        //assertEquals(12, result.size());
        for (LiteResponseTravellerImage image : result) {
            assertEquals("Test URL", image.getUrl());
        }
    }


    @Test
    public void testLiteHotelLists() {
        // Arrange
        List<Hotel> hotels = new ArrayList<>();
        Hotel hotel = new Hotel();
        hotel.setId("1");
        hotel.setName("Test Hotel");
        hotel.setStarRating(5);
        hotel.setSoldOut(false);
        hotel.setIsAltAcco(false);
        hotel.setPriceDetail(new PriceDetail());
        hotel.setLocationDetail(new LocationDetail());
        hotel.setDetailDeeplinkUrl("Test URL");
        hotel.setLocationPersuasion(new ArrayList<>());
        hotel.setRatePersuasionText("Test Rate Persuasion Text");
        MediaInfo mediaInfo = new MediaInfo();
        mediaInfo.setMediaType("IMAGE");
        hotel.setMedia(Collections.singletonList(mediaInfo));
        ReviewSummary reviewSummary = new ReviewSummary();
        reviewSummary.setSource("Test Source");
        reviewSummary.setRatingText("Test Rating Text");
        reviewSummary.setPreferredOTA(true);
        hotel.setReviewSummary(reviewSummary);
        Map<String, Object> hotelPersuasions = new HashMap<>();
        hotelPersuasions.put("PLACEHOLDER_CARD_M4", "Test Placeholder");
        hotel.setHotelPersuasions(hotelPersuasions);
        hotels.add(hotel);

        // Act
        List<Hotel> result = staticDetailResponseTransformerDesktop.liteHotelLists(hotels, true, true);

        // Assert
        assertEquals(1, result.size());
        Hotel resultHotel = result.get(0);
        assertEquals("1", resultHotel.getId());
        assertEquals("Test Hotel", resultHotel.getName());
        assertFalse(resultHotel.isSoldOut());
        assertFalse(resultHotel.getIsAltAcco());
        assertNotNull(resultHotel.getPriceDetail());
        assertNotNull(resultHotel.getHotelPersuasions());
        assertNotNull(resultHotel.getLocationDetail());
    }


    @Test
    public void modifyPlacesResponse_Test() {
        PlacesResponse placesResponse = new PlacesResponse();
        List<Category> categories = new ArrayList<>();
        Category cat = new Category();
        List<CategoryDatum> data = new ArrayList<>();
        CategoryDatum datum = new CategoryDatum();
        datum.setTagLine("");
        datum.setCategory("Test");
        data.add(datum);
        cat.setCategoryData(data);
        categories.add(cat);
        placesResponse.setCategories(categories);
        staticDetailResponseTransformerDesktop.modifyPlacesResponse(placesResponse);
    }

    @Test
    public void testBuildCountryWiseData_NullCountryWiseReviewCount() {
        UGCPlatformReviewSummaryDTO summary = Mockito.mock(UGCPlatformReviewSummaryDTO.class);

        Mockito.when(summary.getCountryWiseReviewCount()).thenReturn(null);

        assertNull(staticDetailResponseTransformerDesktop.buildCountryWiseData(summary));
    }

    @Test
    public void testBuildCountryWiseData_NullCountry() {
        UGCPlatformReviewSummaryDTO summary = Mockito.mock(UGCPlatformReviewSummaryDTO.class);
        CountryWiseReviewCount reviewCountDTO = Mockito.mock(CountryWiseReviewCount.class);

        Mockito.when(summary.getCountryWiseReviewCount()).thenReturn(reviewCountDTO);
        Mockito.when(reviewCountDTO.getCountry()).thenReturn(null);

        assertNull(staticDetailResponseTransformerDesktop.buildCountryWiseData(summary));
    }

    @Test
    public void testBuildCountryWiseData_ReviewCountTooLow() {
        UGCPlatformReviewSummaryDTO summary = Mockito.mock(UGCPlatformReviewSummaryDTO.class);
        CountryWiseReviewCount reviewCountDTO = Mockito.mock(CountryWiseReviewCount.class);

        Mockito.when(summary.getCountryWiseReviewCount()).thenReturn(reviewCountDTO);
        Mockito.when(reviewCountDTO.getCountry()).thenReturn(TEST_COUNTRY);
        Mockito.when(reviewCountDTO.getReviewCount()).thenReturn(5); // Less than the threshold

        assertNull(staticDetailResponseTransformerDesktop.buildCountryWiseData(summary));
    }

    @Test
    public void testBuildCountryWiseData_InvalidNationality() {
        UGCPlatformReviewSummaryDTO summary = Mockito.mock(UGCPlatformReviewSummaryDTO.class);
        CountryWiseReviewCount reviewCountDTO = Mockito.mock(CountryWiseReviewCount.class);

        Mockito.when(summary.getCountryWiseReviewCount()).thenReturn(reviewCountDTO);
        Mockito.when(reviewCountDTO.getCountry()).thenReturn("InvalidCountry"); // No matching nationality
        Mockito.when(reviewCountDTO.getReviewCount()).thenReturn(15);

        assertNull(staticDetailResponseTransformerDesktop.buildCountryWiseData(summary));
    }

    @Test
    public void testBuildCountryWiseData_EmptyReviewText() {
        UGCPlatformReviewSummaryDTO summary = Mockito.mock(UGCPlatformReviewSummaryDTO.class);
        CountryWiseReviewCount reviewCountDTO = Mockito.mock(CountryWiseReviewCount.class);

        Mockito.when(summary.getCountryWiseReviewCount()).thenReturn(reviewCountDTO);
        Mockito.when(reviewCountDTO.getCountry()).thenReturn(TEST_COUNTRY);
        Mockito.when(reviewCountDTO.getReviewCount()).thenReturn(15);

        Mockito.when(polyglotService.getTranslatedData(COUNTRY_WISE_REVIEW_TEXT)).thenReturn(""); // Empty review text

        assertNull(staticDetailResponseTransformerDesktop.buildCountryWiseData(summary));
    }

    @Test
    public void testBuildCountryWiseData_FullValidCase() {
        UGCPlatformReviewSummaryDTO summary = Mockito.mock(UGCPlatformReviewSummaryDTO.class);
        CountryWiseReviewCount reviewCountDTO = Mockito.mock(CountryWiseReviewCount.class);

        Mockito.when(summary.getCountryWiseReviewCount()).thenReturn(reviewCountDTO);
        Mockito.when(reviewCountDTO.getCountry()).thenReturn(TEST_COUNTRY);
        Mockito.when(reviewCountDTO.getReviewCount()).thenReturn(15);

        Mockito.when(polyglotService.getTranslatedData(COUNTRY_WISE_REVIEW_TEXT)).thenReturn(TEST_REVIEW_TEXT);
        Mockito.when(polyglotService.getTranslatedData(COUNTRY_WISE_REVIEW_CTA_TEXT)).thenReturn(TEST_CTA_TEXT);
        CountryWiseReviewData result = staticDetailResponseTransformerDesktop.buildCountryWiseData(summary);

        assertNotNull(result);
        assertEquals("Great reviews from Indian!", result.getText());
        assertEquals(TEST_TEXT_COLOR, result.getColor());
        assertEquals("See reviews from Indian!", result.getCtaText());
    }

    @Test
    public void testBuildCountryWiseData_EmptyCtaText() {
        UGCPlatformReviewSummaryDTO summary = Mockito.mock(UGCPlatformReviewSummaryDTO.class);
        CountryWiseReviewCount reviewCountDTO = Mockito.mock(CountryWiseReviewCount.class);

        Mockito.when(summary.getCountryWiseReviewCount()).thenReturn(reviewCountDTO);
        Mockito.when(reviewCountDTO.getCountry()).thenReturn(TEST_COUNTRY);
        Mockito.when(reviewCountDTO.getReviewCount()).thenReturn(15);
        Mockito.when(polyglotService.getTranslatedData(COUNTRY_WISE_REVIEW_TEXT)).thenReturn(TEST_REVIEW_TEXT);
        Mockito.when(polyglotService.getTranslatedData(COUNTRY_WISE_REVIEW_CTA_TEXT)).thenReturn(null); // Empty CTA text

        CountryWiseReviewData result = staticDetailResponseTransformerDesktop.buildCountryWiseData(summary);

        assertNotNull(result);
        assertEquals("Great reviews from Indian!", result.getText());
        assertEquals(TEST_TEXT_COLOR, result.getColor());
        assertNull(result.getCtaText()); // CTA text should remain null
    }



}