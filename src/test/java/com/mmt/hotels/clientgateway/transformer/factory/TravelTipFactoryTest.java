package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.TravelTipRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.TravelTipRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.TravelTipRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.TravelTipRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.TravelTipRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.TravelTipResponseTransformer;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TravelTipFactoryTest {

    @InjectMocks
    private TravelTipFactory travelTipFactory;

    @Mock
    private TravelTipRequestTransformerPWA travelTipRequestTransformerPWA;

    @Mock
    private TravelTipRequestTransformerDesktop travelTipRequestTransformerDesktop;

    @Mock
    private TravelTipRequestTransformerAndroid travelTipRequestTransformerAndroid;

    @Mock
    private TravelTipRequestTransformerIOS travelTipRequestTransformerIOS;

    @Test
    public void getRequestService_ReturnsDesktopTransformer_WhenClientIsEmpty() {
        TravelTipRequestTransformer result = travelTipFactory.getRequestService("");
        Assert.assertNotNull(result);
        Assert.assertTrue(result instanceof TravelTipRequestTransformerDesktop);
    }

    @Test
    public void getRequestService_ReturnsPwaTransformer_WhenClientIsPwa() {
        TravelTipRequestTransformer result = travelTipFactory.getRequestService("PWA");
    }

    @Test
    public void getRequestService_ReturnsPwaTransformer_WhenClientIsMsite() {
        TravelTipRequestTransformer result = travelTipFactory.getRequestService("MSITE");
        Assert.assertNotNull(result);
        Assert.assertTrue(result instanceof TravelTipRequestTransformerPWA);
    }

    @Test
    public void getRequestService_ReturnsDesktopTransformer_WhenClientIsDesktop() {
        TravelTipRequestTransformer result = travelTipFactory.getRequestService("DESKTOP");
        Assert.assertNotNull(result);
        Assert.assertTrue(result instanceof TravelTipRequestTransformerDesktop);
    }

    @Test
    public void getRequestService_ReturnsAndroidTransformer_WhenClientIsAndroid() {
        TravelTipRequestTransformer result = travelTipFactory.getRequestService("ANDROID");
        Assert.assertNotNull(result);
        Assert.assertTrue(result instanceof TravelTipRequestTransformerAndroid);
    }

    @Test
    public void getRequestService_ReturnsIosTransformer_WhenClientIsIos() {
        TravelTipRequestTransformer result = travelTipFactory.getRequestService("IOS");
        Assert.assertNotNull(result);
        Assert.assertTrue(result instanceof TravelTipRequestTransformerIOS);
    }

    @Test
    public void getRequestService_ReturnsDesktopTransformer_WhenClientIsUnknown() {
        TravelTipRequestTransformer result = travelTipFactory.getRequestService("UNKNOWN");
        Assert.assertNotNull(result);
        Assert.assertTrue(result instanceof TravelTipRequestTransformerDesktop);
    }

    @Test
    public void getResponseService_ReturnsDesktopTransformer_WhenClientIsEmpty() {
        TravelTipResponseTransformer result = travelTipFactory.getResponseService("");
    }

    @Test
    public void getResponseService_ReturnsPwaTransformer_WhenClientIsPwa() {
        TravelTipResponseTransformer result = travelTipFactory.getResponseService("PWA");
    }

    @Test
    public void getResponseService_ReturnsPwaTransformer_WhenClientIsMsite() {
        TravelTipResponseTransformer result = travelTipFactory.getResponseService("MSITE");
    }

    @Test
    public void getResponseService_ReturnsDesktopTransformer_WhenClientIsDesktop() {
        TravelTipResponseTransformer result = travelTipFactory.getResponseService("DESKTOP");

    }

    @Test
    public void getResponseService_ReturnsAndroidTransformer_WhenClientIsAndroid() {
        TravelTipResponseTransformer result = travelTipFactory.getResponseService("ANDROID");

    }

    @Test
    public void getResponseService_ReturnsIosTransformer_WhenClientIsIos() {
        TravelTipResponseTransformer result = travelTipFactory.getResponseService("IOS");
    }

    @Test
    public void getResponseService_ReturnsDesktopTransformer_WhenClientIsUnknown() {
        TravelTipResponseTransformer result = travelTipFactory.getResponseService("UNKNOWN");
    }

}
