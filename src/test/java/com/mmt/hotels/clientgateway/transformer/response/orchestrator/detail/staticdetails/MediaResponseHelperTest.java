package com.mmt.hotels.clientgateway.transformer.response.orchestrator.detail.staticdetails;

import com.gommt.hotels.orchestrator.detail.model.response.content.Media;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.PanoramicMedia360;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.ProfessionalMediaEntity;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.RoomEntity;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.SubTagInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.TagInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.TravellerMediaEntity;
import com.mmt.hotels.clientgateway.response.staticdetail.MediaV2;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.MediaResponseHelper;
import com.mmt.hotels.clientgateway.util.ReArchUtility;
import com.mmt.hotels.model.response.staticdata.Tag;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.LISTING_TYPE_ENTIRE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class MediaResponseHelperTest {

    @Mock
    private ReArchUtility utility;

    @Mock
    private PolyglotService polyglotService;

    @InjectMocks
    private MediaResponseHelper mediaResponseHelper;

    @BeforeEach
    void setUp() {
        // Set up property values
        ReflectionTestUtils.setField(mediaResponseHelper, "view360IconUrl", "http://test-icon.com");
        ReflectionTestUtils.setField(mediaResponseHelper, "view360PersuasionIconUrl", "http://test-persuasion-icon.com");
        ReflectionTestUtils.setField(mediaResponseHelper, "detailGridImageLimit", 10);
        ReflectionTestUtils.setField(mediaResponseHelper, "listingMediaLimitExp", 20);
        
        // Set up common mock behaviors
        lenient().when(polyglotService.getTranslatedData(anyString())).thenReturn("TRANSLATED_TEXT");
        lenient().when(utility.buildChatbotHook(anyString())).thenReturn(null);
    }

    @Test
    void testBuildMedia_NullSource() {
        MediaV2 result = mediaResponseHelper.buildMedia(null, false, false, false, "DESKTOP", "HOTEL", false);
        assertNull(result);
    }

    @Test
    void testBuildMedia_ValidSource() {
        Media media = createValidMedia();
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, true, true, true, "MOBILE", "HOTEL", false);
        
        assertNotNull(result);
        assertNotNull(result.getTraveller());
        assertNotNull(result.getHotel());
        assertNull(result.getView360());
        assertNotNull(result.getGrid());
    }

    @Test
    void testSetGridImages_NullProfessionalMediaEntities() {
        Media media = new Media();
        media.setProfessionalMediaEntities(null);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getGrid());
        assertNull(result.getGrid().getImages());
    }

    @Test
    void testSetGridImages_EmptyProfessionalMediaEntities() {
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMap = new HashMap<>();
        media.setProfessionalMediaEntities(professionalMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getGrid());
        assertNull(result.getGrid().getImages());
    }

    @Test
    void testSetGridImages_NoProfessionalListForH() {
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMap = new HashMap<>();
        professionalMap.put("R", Arrays.asList(createProfessionalMediaEntity()));
        media.setProfessionalMediaEntities(professionalMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getGrid());
        assertNull(result.getGrid().getImages());
    }

    @Test
    void testSetGridImages_NullProfessionalList() {
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMap = new HashMap<>();
        professionalMap.put("H", null);
        media.setProfessionalMediaEntities(professionalMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getGrid());
        assertNull(result.getGrid().getImages());
    }

    @Test
    void testSetGridImages_EmptyProfessionalList() {
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMap = new HashMap<>();
        professionalMap.put("H", new ArrayList<>());
        media.setProfessionalMediaEntities(professionalMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getGrid());
        assertNull(result.getGrid().getImages());
    }

    @Test
    void testSetGridImages_WithImageExpEnabled() {
        Media media = createValidMedia();
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, true, false, "MOBILE", "HOTEL", false);
        
        assertNotNull(result.getGrid());
        assertNotNull(result.getGrid().getImages());
        assertTrue(result.getGrid().getImages().size() <= 10); // detailGridImageLimit
    }

    @Test
    void testSetGridImages_WithImageExpDisabled() {
        Media media = createValidMedia();
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "MOBILE", "HOTEL", false);
        
        assertNotNull(result.getGrid());
        assertNotNull(result.getGrid().getImages());
        assertTrue(result.getGrid().getImages().size() <= 10); // detailGridImageLimit
    }

    @Test
    void testSetGridImages_WithValidProfessionalList() {
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMap = new HashMap<>();
        List<ProfessionalMediaEntity> professionalList = Arrays.asList(createProfessionalMediaEntity());
        professionalMap.put("H", professionalList);
        media.setProfessionalMediaEntities(professionalMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getGrid());
        assertNotNull(result.getGrid().getImages());
    }

    @Test
    void testSet360Images_NullPanoramic360() {
        Media media = new Media();
        media.setPanoramic360(null);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNull(result.getView360());
    }

    @Test
    void testSet360Images_NoPanoramicListForH() {
        Media media = new Media();
        Map<String, List<PanoramicMedia360>> panoramicMap = new HashMap<>();
        panoramicMap.put("R", Arrays.asList(createPanoramicMedia360()));
        media.setPanoramic360(panoramicMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNull(result.getView360());
    }

    @Test
    void testSet360Images_NullPanoramicList() {
        Media media = new Media();
        Map<String, List<PanoramicMedia360>> panoramicMap = new HashMap<>();
        panoramicMap.put("H", null);
        media.setPanoramic360(panoramicMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getView360());
        assertNull(result.getView360().getImages());
    }

    @Test
    void testSet360Images_EmptyPanoramicList() {
        Media media = new Media();
        Map<String, List<PanoramicMedia360>> panoramicMap = new HashMap<>();
        panoramicMap.put("H", new ArrayList<>());
        media.setPanoramic360(panoramicMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL",false);
        
        assertNotNull(result.getView360());
        assertNull(result.getView360().getImages());
    }

    @Test
    void testSet360Images_WithNullIconUrls() {
        ReflectionTestUtils.setField(mediaResponseHelper, "view360IconUrl", null);
        ReflectionTestUtils.setField(mediaResponseHelper, "view360PersuasionIconUrl", null);
        
        Media media = createValidMediaWith360();
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getView360());
        assertNotNull(result.getView360().getImages());
        assertNull(result.getView360().getCtaIcon());
        assertNull(result.getView360().getPersuasionIcon());
    }

    @Test
    void testSet360Images_LuxeNonDesktop() {
        Media media = createValidMediaWith360();
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, true, "MOBILE", "HOTEL", false);
        
        assertNotNull(result.getView360());
        assertNotNull(result.getView360().getImages());
        verify(polyglotService).getTranslatedData("VIEW_360_IMAGE");
    }

    @Test
    void testSet360Images_LuxeDesktop() {
        Media media = createValidMediaWith360();
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, true, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getView360());
        assertNotNull(result.getView360().getImages());
        verify(polyglotService).getTranslatedData("VIEW_IMAGE");
    }

    @Test
    void testSet360Images_NonLuxe() {
        Media media = createValidMediaWith360();
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "MOBILE", "HOTEL", false);
        
        assertNotNull(result.getView360());
        assertNotNull(result.getView360().getImages());
        verify(polyglotService).getTranslatedData("VIEW_IMAGE");
    }

    @Test
    void testSetHotelImages_EmptyTagInfoList() {
        Media media = new Media();
        Map<String, List<TagInfo>> tagInfoMap = new HashMap<>();
        media.setTagInfoList(tagInfoMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);

        assertNull(result.getHotel());
    }

    @Test
    void testSetHotelImages_NoTagInfoForH() {
        Media media = new Media();
        Map<String, List<TagInfo>> tagInfoMap = new HashMap<>();
        tagInfoMap.put("R", Arrays.asList(createTagInfo()));
        media.setTagInfoList(tagInfoMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);

        assertNull(result.getHotel());
    }

    @Test
    void testSetHotelImages_WithChatBotEnabled() {
        Media media = createValidMediaWithHotelImages();
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, true, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getHotel());
        assertNotNull(result.getHotel().getTags());
        verify(utility, atLeastOnce()).buildChatbotHook(anyString());
    }

    @Test
    void testSetHotelImages_WithStreetViewTag() {
        Media media = new Media();
        Map<String, List<TagInfo>> tagInfoMap = new HashMap<>();
        TagInfo tagInfo = createTagInfo();
        tagInfo.setName("Street View");
        tagInfoMap.put("H", Arrays.asList(tagInfo));
        media.setTagInfoList(tagInfoMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getHotel());
        assertNotNull(result.getHotel().getTags());
        assertEquals("http://test-persuasion-icon.com", result.getHotel().getTags().get(0).getTagIconUrl());
    }

    @Test
    void testSetHotelImages_WithNullSubTagInfoList() {
        Media media = new Media();
        Map<String, List<TagInfo>> tagInfoMap = new HashMap<>();
        TagInfo tagInfo = createTagInfo();
        tagInfo.setSubTagInfoList(null);
        tagInfoMap.put("H", Arrays.asList(tagInfo));
        media.setTagInfoList(tagInfoMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getHotel());
        assertNotNull(result.getHotel().getTags());
        assertTrue(result.getHotel().getTags().get(0).getSubtags().isEmpty());
    }

    @Test
    void testSetHotelImages_WithNullSubTagData() {
        Media media = new Media();
        Map<String, List<TagInfo>> tagInfoMap = new HashMap<>();
        TagInfo tagInfo = createTagInfo();
        SubTagInfo subTagInfo = createSubTagInfo();
        subTagInfo.setData(null);
        tagInfo.setSubTagInfoList(Arrays.asList(subTagInfo));
        tagInfoMap.put("H", Arrays.asList(tagInfo));
        media.setTagInfoList(tagInfoMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getHotel());
        assertNotNull(result.getHotel().getTags());
        assertTrue(result.getHotel().getTags().get(0).getSubtags().get(0).getData().isEmpty());
    }

    @Test
    void testSetTravellerImages_NullTraveller() {
        Media media = new Media();
        media.setTraveller(null);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNull(result.getTraveller());
    }

    @Test
    void testSetTravellerImages_EmptyTraveller() {
        Media media = new Media();
        Map<String, List<TravellerMediaEntity>> travellerMap = new HashMap<>();
        media.setTraveller(travellerMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);

        assertNull(result.getTraveller());
    }

    @Test
    void testSetTravellerImages_NoTravellerForH() {
        Media media = new Media();
        Map<String, List<TravellerMediaEntity>> travellerMap = new HashMap<>();
        travellerMap.put("R", Arrays.asList(createTravellerMediaEntity()));
        media.setTraveller(travellerMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);

        assertNull(result.getTraveller());
    }

    @Test
    void testSetTravellerImages_WithChatBotEnabled() {
        Media media = createValidMediaWithTravellerImages();
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, true, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getTraveller());
        assertNotNull(result.getTraveller().getTags());
        verify(utility, atLeastOnce()).buildChatbotHook(anyString());
    }

    @Test
    void testSetTravellerImages_WithDuplicateImgTags() {
        Media media = new Media();
        Map<String, List<TravellerMediaEntity>> travellerMap = new HashMap<>();
        TravellerMediaEntity entity1 = createTravellerMediaEntity();
        entity1.setImgTag("Bathroom");
        TravellerMediaEntity entity2 = createTravellerMediaEntity();
        entity2.setImgTag("Bathroom");
        travellerMap.put("H", Arrays.asList(entity1, entity2));
        media.setTraveller(travellerMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getTraveller());
        assertNotNull(result.getTraveller().getTags());
        assertEquals(1, result.getTraveller().getTags().size());
        assertEquals(2, result.getTraveller().getTags().get(0).getSubtags().get(0).getData().size());
    }

    @Test
    void testSetTravellerImages_WithNullImageTagOrder() {
        Media media = createValidMediaWithTravellerImages();
        media.setProfessionalImageTagOrder(null);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getTraveller());
        assertNotNull(result.getTraveller().getTags());
    }

    @Test
    void testSetTravellerImages_WithEmptyImageTagOrder() {
        Media media = createValidMediaWithTravellerImages();
        media.setProfessionalImageTagOrder(new LinkedHashSet<>());
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false   );
        
        assertNotNull(result.getTraveller());
        assertNotNull(result.getTraveller().getTags());
    }

    @Test
    void testSetTravellerImages_WithImageTagOrder() {
        Media media = createValidMediaWithTravellerImages();
        LinkedHashSet<String> tagOrder = new LinkedHashSet<>();
        tagOrder.add("Bathroom");
        tagOrder.add("Bedroom");
        media.setProfessionalImageTagOrder(tagOrder);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getTraveller());
        assertNotNull(result.getTraveller().getTags());
    }

    @Test
    void testSortTravellerTagsBasedImageTagsOrder_NullImageTagsOrderList() {
        // This tests the early return with null imageTagsOrderList
        List<Tag> tags = Arrays.asList(createTag("Tag1"), createTag("Tag2"));
        
        // Use reflection to call the private method
        try {
            java.lang.reflect.Method method = MediaResponseHelper.class.getDeclaredMethod(
                "sortTravellerTagsBasedImageTagsOrder", List.class, List.class);
            method.setAccessible(true);
            method.invoke(null, tags, null);
            
            // If we reach here, the method executed without exception
            assertEquals(2, tags.size());
        } catch (Exception e) {
            fail("Method should handle null imageTagsOrderList gracefully");
        }
    }

    @Test
    void testSortTravellerTagsBasedImageTagsOrder_EmptyImageTagsOrderList() {
        // This tests the early return with empty imageTagsOrderList
        List<Tag> tags = Arrays.asList(createTag("Tag1"), createTag("Tag2"));
        
        try {
            java.lang.reflect.Method method = MediaResponseHelper.class.getDeclaredMethod(
                "sortTravellerTagsBasedImageTagsOrder", List.class, List.class);
            method.setAccessible(true);
            method.invoke(null, tags, new ArrayList<>());
            
            assertEquals(2, tags.size());
        } catch (Exception e) {
            fail("Method should handle empty imageTagsOrderList gracefully");
        }
    }

    @Test
    void testSortTravellerTagsBasedImageTagsOrder_NullTags() {
        // This tests the early return with null tags
        List<String> tagOrder = Arrays.asList("Tag1", "Tag2");
        
        try {
            java.lang.reflect.Method method = MediaResponseHelper.class.getDeclaredMethod(
                "sortTravellerTagsBasedImageTagsOrder", List.class, List.class);
            method.setAccessible(true);
            method.invoke(null, null, tagOrder);
            
            // Method should handle null tags gracefully
        } catch (Exception e) {
            fail("Method should handle null tags gracefully");
        }
    }

    @Test
    void testSortTravellerTagsBasedImageTagsOrder_EmptyTags() {
        // This tests the early return with empty tags
        List<String> tagOrder = Arrays.asList("Tag1", "Tag2");
        
        try {
            java.lang.reflect.Method method = MediaResponseHelper.class.getDeclaredMethod(
                "sortTravellerTagsBasedImageTagsOrder", List.class, List.class);
            method.setAccessible(true);
            method.invoke(null, new ArrayList<>(), tagOrder);
            
            // Method should handle empty tags gracefully
        } catch (Exception e) {
            fail("Method should handle empty tags gracefully");
        }
    }

    @Test
    void testSortTravellerTagsBasedImageTagsOrder_WithOthersTag() {
        List<Tag> tags = new ArrayList<>();
        tags.add(createTag("Others"));
        tags.add(createTag("Bathroom"));
        List<String> tagOrder = Arrays.asList("Bathroom", "Others");
        
        try {
            java.lang.reflect.Method method = MediaResponseHelper.class.getDeclaredMethod(
                "sortTravellerTagsBasedImageTagsOrder", List.class, List.class);
            method.setAccessible(true);
            method.invoke(null, tags, tagOrder);
            
            // "Others" should be moved to the end
            assertEquals("Bathroom", tags.get(0).getName());
            assertEquals("Others", tags.get(1).getName());
        } catch (Exception e) {
            fail("Method should sort tags with Others at the end");
        }
    }

    @Test
    void testSortTravellerTagsBasedImageTagsOrder_WithMissingTagInOrder() {
        List<Tag> tags = new ArrayList<>();
        tags.add(createTag("UnknownTag"));
        tags.add(createTag("Bathroom"));
        List<String> tagOrder = Arrays.asList("Bathroom");
        
        try {
            java.lang.reflect.Method method = MediaResponseHelper.class.getDeclaredMethod(
                "sortTravellerTagsBasedImageTagsOrder", List.class, List.class);
            method.setAccessible(true);
            method.invoke(null, tags, tagOrder);
            
            // Bathroom should come first (as it's in the order list)
            assertEquals("Bathroom", tags.get(0).getName());
            assertEquals("UnknownTag", tags.get(1).getName());
        } catch (Exception e) {
            fail("Method should handle tags not in order list");
        }
    }

    @Test
    void testBuildMedia_WithNullProfessionalEntities() {
        // Test via public buildMedia method
        Media media = new Media();
        media.setProfessionalMediaEntities(null);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getGrid());
        assertNull(result.getGrid().getImages());
    }

    @Test
    void testBuildMedia_WithEmptyProfessionalEntities() {
        // Test via public buildMedia method
        Media media = new Media();
        media.setProfessionalMediaEntities(new HashMap<>());
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getGrid());
        assertNull(result.getGrid().getImages());
    }

    @Test
    void testBuildMedia_WithNullProfessionalListForH() {
        // Test via public buildMedia method
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMap = new HashMap<>();
        professionalMap.put("H", null);
        media.setProfessionalMediaEntities(professionalMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getGrid());
        assertNull(result.getGrid().getImages());
    }

    @Test
    void testBuildMedia_WithEmptyProfessionalListForH() {
        // Test via public buildMedia method
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMap = new HashMap<>();
        professionalMap.put("H", new ArrayList<>());
        media.setProfessionalMediaEntities(professionalMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getGrid());
        assertNull(result.getGrid().getImages());
    }

    @Test
    void testBuildMedia_WithValidProfessionalListForH() {
        // Test via public buildMedia method
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMap = new HashMap<>();
        professionalMap.put("H", Arrays.asList(createProfessionalMediaEntity()));
        media.setProfessionalMediaEntities(professionalMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getGrid());
        assertNotNull(result.getGrid().getImages());
        assertEquals(1, result.getGrid().getImages().size());
    }

    @Test
    void testBuildProfessionalImagesFromContentResponse_WithRoomImagesNonEntireProperty() {
        // Test the public buildMedia method which internally calls the private method
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMap = new HashMap<>();
        professionalMap.put("H", new ArrayList<>(Arrays.asList(createProfessionalMediaEntity())));
        ProfessionalMediaEntity roomEntity = createProfessionalMediaEntity();
        roomEntity.setSeekTags(new ArrayList<>(Arrays.asList("Room")));
        professionalMap.put("R", new ArrayList<>(Arrays.asList(roomEntity)));
        media.setProfessionalMediaEntities(professionalMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getGrid());
        assertNotNull(result.getGrid().getImages());
        assertTrue(result.getGrid().getImages().size() >= 2); // Should include both H and R images
    }

    @Test
    void testBuildMedia_WithRoomImagesEntireProperty() {
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMap = new HashMap<>();
        professionalMap.put("H", new ArrayList<>(Arrays.asList(createProfessionalMediaEntity())));
        professionalMap.put("R", new ArrayList<>(Arrays.asList(createProfessionalMediaEntity())));
        media.setProfessionalMediaEntities(professionalMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", LISTING_TYPE_ENTIRE, false   );
        
        assertNotNull(result.getGrid());
        assertNotNull(result.getGrid().getImages());
        assertTrue(result.getGrid().getImages().size() >= 1); // Should include at least H images
    }

    @Test
    void testBuildMedia_WithNullSeekTags() {
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMap = new HashMap<>();
        professionalMap.put("H", new ArrayList<>(Arrays.asList(createProfessionalMediaEntity())));
        ProfessionalMediaEntity roomEntity = createProfessionalMediaEntity();
        roomEntity.setSeekTags(null); // Null seek tags
        professionalMap.put("R", new ArrayList<>(Arrays.asList(roomEntity)));
        media.setProfessionalMediaEntities(professionalMap);
        
        MediaV2 result = mediaResponseHelper.buildMedia(media, false, false, false, "DESKTOP", "HOTEL", false);
        
        assertNotNull(result.getGrid());
        assertNotNull(result.getGrid().getImages());
        assertTrue(result.getGrid().getImages().size() >= 2); // Should include both H and R images
    }

    // Helper methods to create test objects
    private Media createValidMedia() {
        Media media = new Media();
        
        // Add professional media entities
        Map<String, List<ProfessionalMediaEntity>> professionalMap = new HashMap<>();
        professionalMap.put("H", new ArrayList<>(Arrays.asList(createProfessionalMediaEntity())));
        media.setProfessionalMediaEntities(professionalMap);
        
        // Add traveller images
        Map<String, List<TravellerMediaEntity>> travellerMap = new HashMap<>();
        travellerMap.put("H", new ArrayList<>(Arrays.asList(createTravellerMediaEntity())));
        media.setTraveller(travellerMap);
        
        // Add hotel images
        Map<String, List<TagInfo>> tagInfoMap = new HashMap<>();
        tagInfoMap.put("H", new ArrayList<>(Arrays.asList(createTagInfo())));
        media.setTagInfoList(tagInfoMap);
        
        return media;
    }

    private Media createValidMediaWith360() {
        Media media = new Media();
        
        // Add panoramic 360 images
        Map<String, List<PanoramicMedia360>> panoramicMap = new HashMap<>();
        panoramicMap.put("H", new ArrayList<>(Arrays.asList(createPanoramicMedia360())));
        media.setPanoramic360(panoramicMap);
        
        return media;
    }

    private Media createValidMediaWithHotelImages() {
        Media media = new Media();
        
        Map<String, List<TagInfo>> tagInfoMap = new HashMap<>();
        tagInfoMap.put("H", new ArrayList<>(Arrays.asList(createTagInfo())));
        media.setTagInfoList(tagInfoMap);
        
        return media;
    }

    private Media createValidMediaWithTravellerImages() {
        Media media = new Media();
        
        Map<String, List<TravellerMediaEntity>> travellerMap = new HashMap<>();
        TravellerMediaEntity entity1 = createTravellerMediaEntity();
        entity1.setImgTag("Bathroom");
        TravellerMediaEntity entity2 = createTravellerMediaEntity();
        entity2.setImgTag("Bedroom");
        travellerMap.put("H", new ArrayList<>(Arrays.asList(entity1, entity2)));
        media.setTraveller(travellerMap);
        
        LinkedHashSet<String> tagOrder = new LinkedHashSet<>();
        tagOrder.add("Bathroom");
        tagOrder.add("Bedroom");
        media.setProfessionalImageTagOrder(tagOrder);
        
        return media;
    }

    private ProfessionalMediaEntity createProfessionalMediaEntity() {
        ProfessionalMediaEntity entity = new ProfessionalMediaEntity();
        entity.setUrl("http://test-image.com");
        entity.setThumbnailUrl("http://test-thumbnail.com");
        entity.setTitle("Test Image");
        entity.setFilterInfo("Test Filter");
        entity.setSeekTags(new ArrayList<>(Arrays.asList("Tag1", "Tag2")));
        entity.setRoomCode("ROOM001");
        return entity;
    }

    private TravellerMediaEntity createTravellerMediaEntity() {
        TravellerMediaEntity entity = new TravellerMediaEntity();
        entity.setUrl("http://test-traveller-image.com");
        entity.setThumbnailUrl("http://test-traveller-thumbnail.com");
        entity.setTitle("Test Traveller Image");
        entity.setDescription("Test Description");
        entity.setImgTag("Bathroom");
        entity.setTravellerName("Test Traveller");
        entity.setDate("2023-01-01");
        return entity;
    }

    private PanoramicMedia360 createPanoramicMedia360() {
        PanoramicMedia360 entity = new PanoramicMedia360();
        entity.setUrl("http://test-360-image.com");
        entity.setThumbnailUrl("http://test-360-thumbnail.com");
        entity.setDescription("Test 360 Description");
        entity.setId("360-ID");
        entity.setName("Test 360 Name");
        entity.setRoomCode("ROOM001");
        entity.setSpaceType("BATHROOM");
        entity.setTiles("tiles-data");
        entity.setPanoramaImg("panorama-img-data");
        return entity;
    }

    private TagInfo createTagInfo() {
        TagInfo tagInfo = new TagInfo();
        tagInfo.setName("Test Tag");
        tagInfo.setSubTagInfoList(new ArrayList<>(Arrays.asList(createSubTagInfo())));
        return tagInfo;
    }

    private SubTagInfo createSubTagInfo() {
        SubTagInfo subTagInfo = new SubTagInfo();
        subTagInfo.setName("Test SubTag");
        subTagInfo.setAccess("public");
        subTagInfo.setAccessType("standard");
        subTagInfo.setSpaceId("SPACE001");
        subTagInfo.setText("Test Text");
        subTagInfo.setData(new ArrayList<>(Arrays.asList(createRoomEntity())));
        return subTagInfo;
    }

    private RoomEntity createRoomEntity() {
        RoomEntity entity = new RoomEntity();
        entity.setUrl("http://test-room-image.com");
        entity.setThumbnailUrl("http://test-room-thumbnail.com");
        entity.setTitle("Test Room Image");
        entity.setDescription("Test Room Description");
        entity.setPreviewUrl("http://test-preview.com");
        entity.setMediaType("IMAGE");
        entity.setRoomCode("ROOM001");
        entity.setRoomName("Test Room");
        return entity;
    }

    private Tag createTag(String name) {
        Tag tag = new Tag();
        tag.setName(name);
        return tag;
    }

    @Test
    void testBasic() {
        // Basic test to start with
    }
    
    // ========== Hero Media Tests ==========
    
    @Test
    void testSetHeroMedia_PremiumExperienceDisabled() throws Exception {
        Method method = MediaResponseHelper.class.getDeclaredMethod("setHeroMedia", Media.class, boolean.class);
        method.setAccessible(true);
        
        Media media = new Media();
        
        Object result = method.invoke(mediaResponseHelper, media, false);
        
        assertNull(result);
    }
    
    @Test
    void testSetHeroMedia_NullHeroMedia() throws Exception {
        Method method = MediaResponseHelper.class.getDeclaredMethod("setHeroMedia", Media.class, boolean.class);
        method.setAccessible(true);
        
        Media media = new Media();
        media.setHeroMedia(null);
        
        Object result = method.invoke(mediaResponseHelper, media, true);
        
        assertNull(result);
    }
    
    @Test
    void testSetHeroMedia_EmptyHeroMedia() throws Exception {
        Method method = MediaResponseHelper.class.getDeclaredMethod("setHeroMedia", Media.class, boolean.class);
        method.setAccessible(true);
        
        Media media = new Media();
        media.setHeroMedia(new ArrayList<>());
        
        Object result = method.invoke(mediaResponseHelper, media, true);
        
        assertNull(result);
    }
    
    @Test
    void testSetHeroMedia_ValidHeroMedia() throws Exception {
        Method method = MediaResponseHelper.class.getDeclaredMethod("setHeroMedia", Media.class, boolean.class);
        method.setAccessible(true);
        
        Media media = new Media();
        
        // Create orchestrator hero media
        List<com.gommt.hotels.orchestrator.detail.model.response.content.experience.HeroMedia> orchestratorHeroMedia = 
            new ArrayList<>();
        
        com.gommt.hotels.orchestrator.detail.model.response.content.experience.HeroMedia heroMedia1 = 
            new com.gommt.hotels.orchestrator.detail.model.response.content.experience.HeroMedia();
        heroMedia1.setMediaUrl("https://example.com/hero1.jpg");
        heroMedia1.setThumbnailUrl("https://example.com/thumb1.jpg");
        heroMedia1.setMediaType("image");
        heroMedia1.setGifUrl("https://example.com/gif1.gif");
        heroMedia1.setTitle("Hero Media 1");
        heroMedia1.setDescription("Description of hero media 1");
        heroMedia1.setCategory("wellness");
        
        com.gommt.hotels.orchestrator.detail.model.response.content.experience.HeroMedia heroMedia2 = 
            new com.gommt.hotels.orchestrator.detail.model.response.content.experience.HeroMedia();
        heroMedia2.setMediaUrl("https://example.com/hero2.mp4");
        heroMedia2.setThumbnailUrl("https://example.com/thumb2.jpg");
        heroMedia2.setMediaType("video");
        heroMedia2.setGifUrl("https://example.com/gif2.gif");
        heroMedia2.setTitle("Hero Media 2");
        heroMedia2.setDescription("Description of hero media 2");
        heroMedia2.setCategory("dining");
        
        orchestratorHeroMedia.add(heroMedia1);
        orchestratorHeroMedia.add(heroMedia2);
        
        media.setHeroMedia(orchestratorHeroMedia);
        
        Object result = method.invoke(mediaResponseHelper, media, true);
        
        assertNotNull(result);
        assertTrue(result instanceof List);
        
        @SuppressWarnings("unchecked")
        List<com.mmt.hotels.model.response.experience.HeroMedia> resultList = 
            (List<com.mmt.hotels.model.response.experience.HeroMedia>) result;
        
        assertEquals(2, resultList.size());
        
        // Verify first hero media
        com.mmt.hotels.model.response.experience.HeroMedia clientHeroMedia1 = resultList.get(0);
        assertEquals("https://example.com/hero1.jpg", clientHeroMedia1.getMediaUrl());
        assertEquals("https://example.com/thumb1.jpg", clientHeroMedia1.getThumbnailUrl());
        assertEquals("image", clientHeroMedia1.getMediaType());
        assertEquals("https://example.com/gif1.gif", clientHeroMedia1.getGifUrl());
        assertEquals("Hero Media 1", clientHeroMedia1.getTitle());
        assertEquals("Description of hero media 1", clientHeroMedia1.getDescription());
        assertEquals("wellness", clientHeroMedia1.getCategory());
        
        // Verify second hero media
        com.mmt.hotels.model.response.experience.HeroMedia clientHeroMedia2 = resultList.get(1);
        assertEquals("https://example.com/hero2.mp4", clientHeroMedia2.getMediaUrl());
        assertEquals("https://example.com/thumb2.jpg", clientHeroMedia2.getThumbnailUrl());
        assertEquals("video", clientHeroMedia2.getMediaType());
        assertEquals("https://example.com/gif2.gif", clientHeroMedia2.getGifUrl());
        assertEquals("Hero Media 2", clientHeroMedia2.getTitle());
        assertEquals("Description of hero media 2", clientHeroMedia2.getDescription());
        assertEquals("dining", clientHeroMedia2.getCategory());
    }
    
    @Test
    void testSetHeroMedia_PartialData() throws Exception {
        Method method = MediaResponseHelper.class.getDeclaredMethod("setHeroMedia", Media.class, boolean.class);
        method.setAccessible(true);
        
        Media media = new Media();
        
        List<com.gommt.hotels.orchestrator.detail.model.response.content.experience.HeroMedia> orchestratorHeroMedia = 
            new ArrayList<>();
        
        com.gommt.hotels.orchestrator.detail.model.response.content.experience.HeroMedia heroMedia = 
            new com.gommt.hotels.orchestrator.detail.model.response.content.experience.HeroMedia();
        heroMedia.setMediaUrl("https://example.com/hero.jpg");
        heroMedia.setMediaType("image");
        // Other fields are null/empty
        
        orchestratorHeroMedia.add(heroMedia);
        media.setHeroMedia(orchestratorHeroMedia);
        
        Object result = method.invoke(mediaResponseHelper, media, true);
        
        assertNotNull(result);
        assertTrue(result instanceof List);
        
        @SuppressWarnings("unchecked")
        List<com.mmt.hotels.model.response.experience.HeroMedia> resultList = 
            (List<com.mmt.hotels.model.response.experience.HeroMedia>) result;
        
        assertEquals(1, resultList.size());
        
        com.mmt.hotels.model.response.experience.HeroMedia clientHeroMedia = resultList.get(0);
        assertEquals("https://example.com/hero.jpg", clientHeroMedia.getMediaUrl());
        assertEquals("image", clientHeroMedia.getMediaType());
        assertNull(clientHeroMedia.getThumbnailUrl());
        assertNull(clientHeroMedia.getGifUrl());
        assertNull(clientHeroMedia.getTitle());
        assertNull(clientHeroMedia.getDescription());
        assertNull(clientHeroMedia.getCategory());
    }
} 