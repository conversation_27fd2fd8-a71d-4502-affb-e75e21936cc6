package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gommt.hotels.orchestrator.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.model.response.listing.TrackingVariables;
import com.gommt.hotels.orchestrator.model.response.listing.HotelDetails;
import com.gommt.hotels.orchestrator.model.response.listing.PersonalizedSectionDetails;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.BottomSheet;
import com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.hotels.orchestrator.enums.Funnel;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.*;

import com.mmt.hotels.clientgateway.response.searchHotels.HotelCard;
import com.mmt.hotels.pojo.listing.personalization.BGLinearGradient;
import com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.service.PolyglotService;

@RunWith(MockitoJUnitRunner.class)
public class OrchSearchHotelsResponseTransformerPWATest {
     @InjectMocks
    OrchSearchHotelsResponseTransformerPWA orchSearchHotelsResponseTransformerPWA;

    @Spy
    ObjectMapperUtil objectMapperUtil;

    ObjectMapper mapper;

    @Mock
    private PolyglotService polyglotService;
    
    @Before
    public void setUp() throws JsonParseException {
        mapper = new ObjectMapper();
        mapper.writerWithView(PIIView.External.class);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
    }

    @Test
    public void buildBottomSheet() {
        orchSearchHotelsResponseTransformerPWA.buildBottomSheet(null);
    }

    @Test
    public void addPersuasionHoverData() {
        orchSearchHotelsResponseTransformerPWA.addPersuasionHoverData(null, null, null, null, null);
    }

    @Test
    public void addLocationPersuasionToHotelPersuasions() throws JsonProcessingException {
        Hotel hotelEntity = new Hotel();
        List<String> locationPersuasions = Collections.singletonList("Chanakyapuri");
        LinkedHashSet<String> facilities = new LinkedHashSet<>(Arrays.asList("Spa", "Restaurant", "Bar"));
        TransportPoi nearestGroundTransportPoi = new TransportPoi();
        LocationDetails locusData = new LocationDetails();
        locusData.setCityName("Delhi");
        SearchHotelsRequest searchHotelsRequest = createSearchHotelsRequest();
        searchHotelsRequest.getRequestDetails().setPageContext("LISTING");
        String dayUsePersuasionsText = "Day Use";
        String drivingTimeText = "10.0 km drive to T1 - Delhi Airport (IGI Airport)";
        orchSearchHotelsResponseTransformerPWA.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, null, true);
        Assert.assertTrue(hotelEntity.getHotelPersuasions() instanceof HashMap);
        searchHotelsRequest.getRequestDetails().setFunnelSource(Funnel.DAYUSE.name());

        orchSearchHotelsResponseTransformerPWA.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, null, true);
    }

    private SearchHotelsRequest createSearchHotelsRequest() throws JsonProcessingException {
        String requestString = "{\"correlationKey\":null,\"brand\":null,\"client\":null,\"blackInfo\":null,\"deviceDetails\":{\"appVersion\":\"128.0.0.0\",\"deviceId\":\"d7bb97cf-762b-4484-91fc-a224c03cdc96\",\"deviceType\":\"DESKTOP\",\"bookingDevice\":\"DESKTOP\",\"networkType\":\"WiFi\",\"deviceName\":null,\"appVersionIntGi\":null,\"simSerialNo\":null},\"lastProductId\":null,\"limit\":null,\"requestDetails\":{\"visitorId\":\"d23c479b373ee283\",\"visitNumber\":1,\"trafficSource\":null,\"srCon\":null,\"srCty\":null,\"srcState\":null,\"srLat\":null,\"srLng\":null,\"funnelSource\":\"HOTELS\",\"idContext\":\"B2C\",\"notifCoupon\":null,\"callBackType\":null,\"pushDataToCallToBookQ\":null,\"pushDataToListAllPropQ\":null,\"payMode\":null,\"loggedIn\":true,\"couponCount\":10,\"siteDomain\":\"in\",\"channel\":\"B2Cweb\",\"pageContext\":\"LISTING\",\"firstTimeUserState\":0,\"uuid\":null,\"corpAuthCode\":null,\"corpUserId\":null,\"seoCorp\":false,\"requestor\":null,\"wishCode\":null,\"preApprovedValidity\":null,\"metaInfo\":false,\"zcp\":null,\"requisitionID\":null,\"myBizFlowIdentifier\":null,\"brand\":\"MMT\",\"previousTxnKey\":null,\"oldWorkflowId\":null,\"journeyId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"requestId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"sessionId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"promoConsent\":false,\"flyerInfo\":null,\"premium\":false,\"semanticSearchDetails\":null,\"forwardBookingFlow\":false,\"extendedPackageCall\":false,\"isIgnoreSEO\":false,\"isRequestCallBack\":false,\"isListAllPropCall\":false},\"detailDeepLinkUrl\":null,\"sortCriteria\":null,\"filterCriteria\":[],\"appliedBatchKeys\":[],\"filterGroupsToRemove\":null,\"filtersToRemove\":null,\"featureFlags\":{\"staticData\":true,\"reviewSummaryRequired\":true,\"walletRequired\":true,\"shortlistingRequired\":false,\"noOfCoupons\":0,\"noOfAddons\":0,\"noOfPersuasions\":0,\"noOfSoldouts\":0,\"coupon\":true,\"mmtPrime\":false,\"persuationSeg\":null,\"persuasionsEngineHit\":true,\"checkAvailability\":true,\"liteResponse\":false,\"applyAbsorption\":false,\"bestOffersLimit\":0,\"dealOfTheDayRequired\":false,\"addOnRequired\":false,\"roomInfoRequired\":false,\"allInclusions\":false,\"hotelCatAndPropNotRequiredInMeta\":false,\"extraAltAccoRequired\":false,\"limitedFilterCall\":false,\"corpMMRRequired\":false,\"unmodifiedAmenities\":false,\"poisRequiredOnMap\":true,\"persuasionsRequired\":true,\"similarHotel\":false,\"locus\":false,\"comparator\":false,\"originListingMap\":false,\"mostBooked\":false,\"detailMap\":false,\"showUpsell\":false,\"filterRanking\":false,\"quickReview\":false,\"dayUsePersuasion\":false,\"selectiveHotels\":false,\"persuasionSuppression\":false,\"hidePrice\":false,\"showBnplCard\":false,\"modifyBooking\":false,\"cardRequired\":false,\"topCard\":false,\"filters\":false,\"seoDS\":false,\"seoCohort\":null,\"roomPreferenceEnabled\":false,\"flashDealClaimed\":false,\"upsellRateplanRequired\":false,\"orientation\":null,\"elitePackageEnabled\":false,\"premiumThemesCardRequired\":false,\"isGoTribe3_0\":null},\"matchMakerDetails\":{},\"imageDetails\":{\"types\":[\"professional\"],\"categories\":[{\"type\":\"H\",\"count\":1,\"height\":162,\"width\":243,\"imageFormat\":\"webp\"}]},\"reviewDetails\":{\"otas\":[\"MMT\",\"TA\"],\"tagTypes\":[\"BASE\",\"WHAT_GUESTS_SAY\"]},\"expData\":\"{\\\"EMIDT\\\":2,\\\"UGCV2\\\":\\\"T\\\",\\\"HFC\\\":\\\"F\\\",\\\"VIDEO\\\":0,\\\"APT\\\":\\\"T\\\",\\\"CHPC\\\":\\\"T\\\",\\\"LSTNRBY\\\":\\\"T\\\",\\\"AARI\\\":\\\"T\\\",\\\"RCPN\\\":\\\"T\\\",\\\"MRS\\\":\\\"T\\\",\\\"ADDON\\\":\\\"T\\\",\\\"NLP\\\":\\\"Y\\\",\\\"PERNEW\\\":\\\"T\\\",\\\"GRPN\\\":\\\"T\\\",\\\"BNPL\\\":\\\"T\\\",\\\"MCUR\\\":\\\"T\\\",\\\"HAFC\\\":\\\"T\\\",\\\"PLRS\\\":\\\"T\\\",\\\"MMRVER\\\":\\\"V3\\\",\\\"PDO\\\":\\\"PN\\\",\\\"BLACK\\\":\\\"T\\\",\\\"CV2\\\":\\\"T\\\",\\\"FLTRPRCBKT\\\":\\\"T\\\",\\\"RTBC\\\":\\\"T\\\",\\\"MLOS\\\":\\\"T\\\",\\\"WPAH\\\":\\\"F\\\",\\\"AIP\\\":\\\"T\\\",\\\"BNPL0\\\":\\\"T\\\",\\\"HIS\\\":\\\"DEFAULT\\\",\\\"APE\\\":10,\\\"PAH\\\":5,\\\"IAO\\\":\\\"F\\\",\\\"CRF\\\":\\\"B\\\",\\\"ALC\\\":\\\"T\\\",\\\"SOU\\\":\\\"T\\\",\\\"PAH5\\\":\\\"T\\\",\\\"rearch\\\":\\\"True\\\"}\",\"expVariantKeys\":null,\"cohertVar\":null,\"multiCityFilter\":null,\"additionalProperties\":null,\"cardId\":null,\"manthanExpDataMap\":null,\"expDataMap\":null,\"contentExpDataMap\":null,\"userLocation\":null,\"clusterId\":null,\"orgId\":null,\"validExpList\":null,\"variantKeys\":null,\"businessIdentificationEnableFromUserService\":false,\"selectedTabId\":null,\"searchCriteria\":{\"checkIn\":\"2024-10-26\",\"checkOut\":\"2024-10-27\",\"countryCode\":\"IN\",\"cityCode\":\"ZNSHIM\",\"cityName\":null,\"locationId\":\"CTDEL\",\"locationType\":\"znshim\",\"lat\":null,\"lng\":null,\"currency\":\"INR\",\"personalCorpBooking\":false,\"rmDHS\":false,\"boostProperty\":null,\"baseRateplanCode\":null,\"selectedRatePlan\":null,\"multiCurrencyInfo\":null,\"preAppliedFilter\":false,\"roomStayCandidates\":[{\"rooms\":1,\"adultCount\":2,\"childAges\":[]}],\"parentLocationId\":null,\"parentLocationType\":null,\"tripType\":null,\"slot\":null,\"giHotelId\":null,\"hotelIds\":null,\"limit\":1,\"lastHotelId\":null,\"lastFetchedWindowInfo\":null,\"lastHotelCategory\":null,\"personalizedSearch\":true,\"nearBySearch\":false,\"wishListedSearch\":false,\"totalHotelsShown\":null,\"sectionsType\":null,\"collectionCriteria\":null,\"bookingForGuest\":false,\"travellerEmailID\":null,\"vcId\":null},\"lastPeekedOnMapHotelIds\":null,\"mapDetails\":null,\"nearbyFilter\":null,\"filterRemovedCriteria\":null}";
        return mapper.readValue(requestString, SearchHotelsRequest.class);
    }

    @Test
    public void buildBGColor() {
        orchSearchHotelsResponseTransformerPWA.buildBGColor(null, null, null);
    }

    @Test
    public void addBookingConfirmationPersuasion() {
        orchSearchHotelsResponseTransformerPWA.addBookingConfirmationPersuasion(null);
    }

    @Test
    public void buildStaticCard() {
        orchSearchHotelsResponseTransformerPWA.buildStaticCard(null, null, "test");
    }

    @Test
    public void testBuildBottomSheetImplementation() {
        // Create test data
        PersonalizedSectionDetails personalizedSectionDetails = new PersonalizedSectionDetails();
        personalizedSectionDetails.setName("TEST_SECTION");
        
        // Execute the method - PWA implementation returns null
        BottomSheet bottomSheet = orchSearchHotelsResponseTransformerPWA.buildBottomSheet(personalizedSectionDetails);
        
        // Verify the result
        Assert.assertNull(bottomSheet);
    }

    @Test
    public void testAddPersuasionHoverDataImplementation() {
        // Create test data
        Hotel hotel = new Hotel();
        hotel.setHotelPersuasions(new HashMap<>());
        
        HotelDetails hotelEntity = new HotelDetails();
        Map<String, Object> persuasionsMap = new HashMap<>();
        hotelEntity.setHotelPersuasions(persuasionsMap);
        
        // Execute the method - this is a no-op in PWA but should not throw exceptions
        orchSearchHotelsResponseTransformerPWA.addPersuasionHoverData(hotel, hotelEntity, null, null, null);
        
        // No assertions needed as the PWA implementation is empty
    }

    @Test
    public void testBuildBGColorImplementation() {
        // Test RECENTLY_VIEWED_HOTELS section
        String bgColor = orchSearchHotelsResponseTransformerPWA.buildBGColor("RECENTLY_VIEWED_HOTELS", "H", "default");
        Assert.assertNull(bgColor); // PWA implementation returns null
        
        // Test SIMILAR_HOTELS section
        bgColor = orchSearchHotelsResponseTransformerPWA.buildBGColor("SIMILAR_HOTELS", "H", "default");
        Assert.assertNull(bgColor); // PWA implementation returns null
    }
    
    @Test
    public void testAddBookingConfirmationPersuasionImplementation() {
        // Create hotel entity for testing
        HotelDetails hotelEntity = new HotelDetails();
        
        // Execute method - this is a no-op in PWA but should not throw exceptions
        orchSearchHotelsResponseTransformerPWA.addBookingConfirmationPersuasion(hotelEntity);
        
        // No assertions needed as the PWA implementation is empty
    }

    @Test
    public void testBuildStaticCardImplementation() {
        // Test with null section
        Object result = orchSearchHotelsResponseTransformerPWA.buildStaticCard(null, null, "test");
        Assert.assertNull(result); // PWA implementation returns null
        
        // Test with valid section and hotels data
        List<HotelDetails> hotels = new ArrayList<>();
        HotelDetails hotel = new HotelDetails();
        hotels.add(hotel);
        
        result = orchSearchHotelsResponseTransformerPWA.buildStaticCard("MYBIZ_ASSURED_SHOWN", hotels, "test");
        Assert.assertNull(result); // PWA implementation returns null
    }

    /*@Test
    public void buildTrackingMapReturnsEmptyMapWhenTrackingVariablesIsNull() {
        TrackingVariables trackingVariables = null;

        Map<String, String> result = ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerPWA, "buildTrackingMap", trackingVariables);

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void buildTrackingMapReturnsEmptyMapWhenFamilyFriendlyIsFalse() {
        TrackingVariables trackingVariables = new TrackingVariables();
        trackingVariables.setFamilyFriendly(false);

        Map<String, String> result = ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerPWA, "buildTrackingMap", trackingVariables);

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void buildTrackingMapReturnsMapWithFamilyFriendlyTrackingWhenFamilyFriendlyIsTrue() {
        TrackingVariables trackingVariables = new TrackingVariables();
        trackingVariables.setFamilyFriendly(true);

        Map<String, String> result = ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerPWA, "buildTrackingMap", trackingVariables);

        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
    }*/

    @Test
    public void testGetGuestCountForPixelUrl_WithAllValuesPresent_ShouldReturnSum() throws Exception {
        // Arrange
        Method method = OrchSearchHotelsResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", com.mmt.hotels.clientgateway.request.RoomStayCandidate.class);
        method.setAccessible(true);

        com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate = new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        roomStayCandidate.setAdultCount(2);
        List<Integer> childAges = Arrays.asList(5, 8);
        roomStayCandidate.setChildAges(childAges);

        // Act
        int result = (int) method.invoke(orchSearchHotelsResponseTransformerPWA, roomStayCandidate);

        // Assert
        assertEquals("Should return sum of adults and children", 4, result);
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithNullAdultCount_ShouldReturnChildrenCount() throws Exception {
        // Arrange
        Method method = OrchSearchHotelsResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl",com.mmt.hotels.clientgateway.request.RoomStayCandidate.class);
        method.setAccessible(true);

        com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate = new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        roomStayCandidate.setAdultCount(null);
        List<Integer> childAges = Arrays.asList(5, 8);
        roomStayCandidate.setChildAges(childAges);

        // Act
        int result = (int) method.invoke(orchSearchHotelsResponseTransformerPWA, roomStayCandidate);

        // Assert
        assertEquals("Should return only children count when adult count is null", 2, result);
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithNullChildAges_ShouldReturnAdultCount() throws Exception {
        // Arrange
        Method method = OrchSearchHotelsResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", com.mmt.hotels.clientgateway.request.RoomStayCandidate.class);
        method.setAccessible(true);

        com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate = new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        roomStayCandidate.setAdultCount(3);
        roomStayCandidate.setChildAges(null);

        // Act
        int result = (int) method.invoke(orchSearchHotelsResponseTransformerPWA, roomStayCandidate);

        // Assert
        assertEquals("Should return only adult count when child ages is null", 3, result);
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithEmptyChildAges_ShouldReturnAdultCount() throws Exception {
        // Arrange
        Method method = OrchSearchHotelsResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", com.mmt.hotels.clientgateway.request.RoomStayCandidate.class);
        method.setAccessible(true);

        com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate = new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        roomStayCandidate.setAdultCount(3);
        roomStayCandidate.setChildAges(Collections.emptyList());

        // Act
        int result = (int) method.invoke(orchSearchHotelsResponseTransformerPWA, roomStayCandidate);

        // Assert
        assertEquals("Should return only adult count when child ages is empty", 3, result);
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithZeroAdults_ShouldReturnChildrenCount() throws Exception {
        // Arrange
        Method method = OrchSearchHotelsResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", com.mmt.hotels.clientgateway.request.RoomStayCandidate.class);
        method.setAccessible(true);

        com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate = new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        roomStayCandidate.setAdultCount(0);
        List<Integer> childAges = Arrays.asList(5, 8, 10);
        roomStayCandidate.setChildAges(childAges);

        // Act
        int result = (int) method.invoke(orchSearchHotelsResponseTransformerPWA, roomStayCandidate);

        // Assert
        assertEquals("Should return only children count when adult count is zero", 3, result);
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithBothValuesNull_ShouldReturnZero() throws Exception {
        // Arrange
        Method method = OrchSearchHotelsResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", com.mmt.hotels.clientgateway.request.RoomStayCandidate.class);
        method.setAccessible(true);

        com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate = new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        roomStayCandidate.setAdultCount(null);
        roomStayCandidate.setChildAges(null);

        // Act
        int result = (int) method.invoke(orchSearchHotelsResponseTransformerPWA, roomStayCandidate);

        // Assert
        assertEquals("Should return zero when both adult count and child ages are null", 0, result);
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithLargeNumbers_ShouldReturnCorrectSum() throws Exception {
        // Arrange
        Method method = OrchSearchHotelsResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", com.mmt.hotels.clientgateway.request.RoomStayCandidate.class);
        method.setAccessible(true);

        com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate = new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        roomStayCandidate.setAdultCount(10);

        // Create a list with 15 child ages
        List<Integer> childAges = new ArrayList<>();
        for (int i = 0; i < 15; i++) {
            childAges.add(i + 1);
        }
        roomStayCandidate.setChildAges(childAges);

        // Act
        int result = (int) method.invoke(orchSearchHotelsResponseTransformerPWA, roomStayCandidate);

        // Assert
        assertEquals("Should return correct sum for large numbers", 25, result);
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithNullRoomStayCandidate_ShouldHandleNPE() throws Exception {
        // Arrange
        Method method = OrchSearchHotelsResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", com.mmt.hotels.clientgateway.request.RoomStayCandidate.class);
        method.setAccessible(true);

        // Act & Assert
        try {
            method.invoke(orchSearchHotelsResponseTransformerPWA, (Object) null);
            // If we reach here, the test has failed because we expect an exception
            org.junit.Assert.fail("Expected NullPointerException was not thrown");
        } catch (Exception e) {
            // We expect an InvocationTargetException wrapping an NullPointerException
            assertEquals("Should throw InvocationTargetException for null input",
                    java.lang.reflect.InvocationTargetException.class, e.getClass());
            assertEquals("Cause should be NullPointerException",
                    NullPointerException.class, e.getCause().getClass());
        }
    }

    // ========== buildStaticCard Tests ==========
    
    @Test
    public void should_ReturnNull_When_SectionIsNull() {
        // Given
        String section = null;
        List<HotelDetails> hotels = Collections.singletonList(new HotelDetails());
        String detailDeepLinkUrl = "https://example.com/detail";
        
        // When
        MyBizStaticCard result = orchSearchHotelsResponseTransformerPWA.buildStaticCard(section, hotels, detailDeepLinkUrl);
        
        // Then
        Assert.assertNull(result);
    }
    
    @Test
    public void should_ReturnNull_When_SectionIsNotCorpBudgetDirectHotel() {
        // Given
        String section = "OTHER_SECTION";
        List<HotelDetails> hotels = Collections.singletonList(new HotelDetails());
        String detailDeepLinkUrl = "https://example.com/detail";
        
        // When
        MyBizStaticCard result = orchSearchHotelsResponseTransformerPWA.buildStaticCard(section, hotels, detailDeepLinkUrl);
        
        // Then
        Assert.assertNull(result);
    }
    
    @Test
    public void should_ReturnNull_When_HotelsListIsEmpty() {
        // Given
        String section = Constants.CORPBUDGET_DIRECT_HOTEL;
        List<HotelDetails> hotels = Collections.emptyList();
        String detailDeepLinkUrl = "https://example.com/detail";
        
        // When
        MyBizStaticCard result = orchSearchHotelsResponseTransformerPWA.buildStaticCard(section, hotels, detailDeepLinkUrl);
        
        // Then
        Assert.assertNull(result);
    }
    
    @Test
    public void should_ReturnNull_When_HotelsListIsNull() {
        // Given
        String section = Constants.CORPBUDGET_DIRECT_HOTEL;
        List<HotelDetails> hotels = null;
        String detailDeepLinkUrl = "https://example.com/detail";
        
        // When
        MyBizStaticCard result = orchSearchHotelsResponseTransformerPWA.buildStaticCard(section, hotels, detailDeepLinkUrl);
        
        // Then
        Assert.assertNull(result);
    }
    
    @Test
    public void should_ReturnNull_When_FirstHotelIsBudgetHotel() {
        // Given
        String section = Constants.CORPBUDGET_DIRECT_HOTEL;
        List<HotelDetails> hotels = Collections.singletonList(new HotelDetails());
        hotels.get(0).setBudgetHotel(true);
        String detailDeepLinkUrl = "https://example.com/detail";
        
        // When
        MyBizStaticCard result = orchSearchHotelsResponseTransformerPWA.buildStaticCard(section, hotels, detailDeepLinkUrl);
        
        // Then
        Assert.assertNull(result);
    }
    
    @Test
    public void should_ReturnNull_When_MyBizStaticCardIsNull() {
        // Given
        String section = Constants.CORPBUDGET_DIRECT_HOTEL;
        List<HotelDetails> hotels = Collections.singletonList(new HotelDetails());
        hotels.get(0).setBudgetHotel(false);
        String detailDeepLinkUrl = "https://example.com/detail";
        
        // Setup null myBizStaticCard
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "myBizStaticCard", null);
        
        // When
        MyBizStaticCard result = orchSearchHotelsResponseTransformerPWA.buildStaticCard(section, hotels, detailDeepLinkUrl);
        
        // Then
        Assert.assertNull(result);
    }
    
    @Test
    public void should_CreateStaticCard_When_AllConditionsAreMet() {
        // Given
        String section = Constants.CORPBUDGET_DIRECT_HOTEL;
        List<HotelDetails> hotels = Collections.singletonList(new HotelDetails());
        hotels.get(0).setBudgetHotel(false);
        String detailDeepLinkUrl = "https://example.com/detail";
        
        // Setup mock myBizStaticCard
        MyBizStaticCard mockStaticCard = new MyBizStaticCard();
        mockStaticCard.setText("Original Text");
        mockStaticCard.setSubtext("Original Subtext");
        mockStaticCard.setCtaText("Original CTA");
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "myBizStaticCard", mockStaticCard);
        
        // Setup polyglot service mock
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData("Original Text")).thenReturn("Translated Text");
        when(polyglotService.getTranslatedData("Original Subtext")).thenReturn("Translated Subtext");
        when(polyglotService.getTranslatedData("Original CTA")).thenReturn("Translated CTA");
        
        // When
        MyBizStaticCard result = orchSearchHotelsResponseTransformerPWA.buildStaticCard(section, hotels, detailDeepLinkUrl);
        
        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals(detailDeepLinkUrl, result.getActionUrl());
        Assert.assertEquals("Translated Text", result.getText());
        Assert.assertEquals("Translated Subtext", result.getSubtext());
        Assert.assertEquals("Translated CTA", result.getCtaText());
        
        // Verify polyglot service was called for translation
        verify(polyglotService).getTranslatedData("Original Text");
        verify(polyglotService).getTranslatedData("Original Subtext");
        verify(polyglotService).getTranslatedData("Original CTA");
    }
    
    @Test
    public void should_HandleMultipleHotels_When_OnlyFirstHotelMatters() {
        // Given
        String section = Constants.CORPBUDGET_DIRECT_HOTEL;
        List<HotelDetails> hotels = Arrays.asList(
            createNonBudgetHotel(),
            createBudgetHotel(),
            createNonBudgetHotel()
        );
        String detailDeepLinkUrl = "https://example.com/detail";
        
        // Setup mock myBizStaticCard
        MyBizStaticCard mockStaticCard = new MyBizStaticCard();
        mockStaticCard.setText("Test Text");
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "myBizStaticCard", mockStaticCard);
        
        // Setup polyglot service mock
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated");
        
        // When
        MyBizStaticCard result = orchSearchHotelsResponseTransformerPWA.buildStaticCard(section, hotels, detailDeepLinkUrl);
        
        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals(detailDeepLinkUrl, result.getActionUrl());
    }
    
    @Test
    public void should_HandleEdgeCases_When_SpecialCharactersInUrl() {
        // Given
        String section = Constants.CORPBUDGET_DIRECT_HOTEL;
        List<HotelDetails> hotels = Collections.singletonList(createNonBudgetHotel());
        String detailDeepLinkUrl = "https://example.com/detail?param=value&special=@#$%";
        
        // Setup mock myBizStaticCard
        MyBizStaticCard mockStaticCard = new MyBizStaticCard();
        mockStaticCard.setText("Test");
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "myBizStaticCard", mockStaticCard);
        
        // Setup polyglot service mock
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated");
        
        // When
        MyBizStaticCard result = orchSearchHotelsResponseTransformerPWA.buildStaticCard(section, hotels, detailDeepLinkUrl);
        
        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals(detailDeepLinkUrl, result.getActionUrl());
    }
    
    // ========== translateStaticCard Tests ==========
    
    @Test
    public void should_TranslateAllFields_When_StaticCardProvided() {
        // Given
        MyBizStaticCard staticCard = new MyBizStaticCard();
        staticCard.setText("Original Text");
        staticCard.setSubtext("Original Subtext");
        staticCard.setCtaText("Original CTA");
        
        // Setup polyglot service mock
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData("Original Text")).thenReturn("Translated Text");
        when(polyglotService.getTranslatedData("Original Subtext")).thenReturn("Translated Subtext");
        when(polyglotService.getTranslatedData("Original CTA")).thenReturn("Translated CTA");
        
        // When
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerPWA, "translateStaticCard", staticCard);
        
        // Then
        Assert.assertEquals("Translated Text", staticCard.getText());
        Assert.assertEquals("Translated Subtext", staticCard.getSubtext());
        Assert.assertEquals("Translated CTA", staticCard.getCtaText());
        
        // Verify all translation calls were made
        verify(polyglotService).getTranslatedData("Original Text");
        verify(polyglotService).getTranslatedData("Original Subtext");
        verify(polyglotService).getTranslatedData("Original CTA");
    }
    
    @Test
    public void should_HandleNullFields_When_TranslatingStaticCard() {
        // Given
        MyBizStaticCard staticCard = new MyBizStaticCard();
        staticCard.setText(null);
        staticCard.setSubtext(null);
        staticCard.setCtaText(null);
        
        // Setup polyglot service mock
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData(null)).thenReturn("Default Translation");
        
        // When
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerPWA, "translateStaticCard", staticCard);
        
        // Then
        Assert.assertEquals("Default Translation", staticCard.getText());
        Assert.assertEquals("Default Translation", staticCard.getSubtext());
        Assert.assertEquals("Default Translation", staticCard.getCtaText());
        
        // Verify translation calls were made for null values
        verify(polyglotService, times(3)).getTranslatedData(null);
    }
    
    @Test
    public void should_HandleEmptyFields_When_TranslatingStaticCard() {
        // Given
        MyBizStaticCard staticCard = new MyBizStaticCard();
        staticCard.setText("");
        staticCard.setSubtext("");
        staticCard.setCtaText("");
        
        // Setup polyglot service mock
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData("")).thenReturn("Empty Translation");
        
        // When
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerPWA, "translateStaticCard", staticCard);
        
        // Then
        Assert.assertEquals("Empty Translation", staticCard.getText());
        Assert.assertEquals("Empty Translation", staticCard.getSubtext());
        Assert.assertEquals("Empty Translation", staticCard.getCtaText());
        
        // Verify translation calls were made for empty strings
        verify(polyglotService, times(3)).getTranslatedData("");
    }
    
    @Test
    public void should_HandleSpecialCharacters_When_TranslatingStaticCard() {
        // Given
        MyBizStaticCard staticCard = new MyBizStaticCard();
        staticCard.setText("Text with special chars: @#$%^&*()");
        staticCard.setSubtext("Subtext with unicode: ñáéíóú");
        staticCard.setCtaText("CTA with symbols: €£¥");
        
        // Setup polyglot service mock
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated Special");
        
        // When
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerPWA, "translateStaticCard", staticCard);
        
        // Then
        Assert.assertEquals("Translated Special", staticCard.getText());
        Assert.assertEquals("Translated Special", staticCard.getSubtext());
        Assert.assertEquals("Translated Special", staticCard.getCtaText());
    }
    
    // ========== buildQuickBookCard Tests ==========
    
    @Test
    public void should_CreateCompleteHotelCard_When_QuickBookInfoProvided() {
        // Given
        QuickBookInfo quickBookInfo = createQuickBookInfo();
        
        // Setup polyglot service mock
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW)).thenReturn("Book Now");
        
        // When
        HotelCard result = orchSearchHotelsResponseTransformerPWA.buildQuickBookCard(quickBookInfo);
        
        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals("Test Title With Price", result.getHeading());
        Assert.assertEquals("Test Room Persuasion", result.getSubHeading());
        Assert.assertEquals("Test Room Persuasion With Size", result.getRoomSubHeading());
        Assert.assertTrue(result.getShowCard());
        Assert.assertEquals("Book Now", result.getCta());
        
        // Verify gradient settings
        Assert.assertNotNull(result.getBgLinearGradient());
        Assert.assertEquals("leftBottomToTopRight", result.getBgLinearGradient().getDirection());
        Assert.assertEquals("#2D6F95", result.getBgLinearGradient().getStart());
        Assert.assertEquals("#192B43", result.getBgLinearGradient().getEnd());
        
        // Verify polyglot service was called
        verify(polyglotService).getTranslatedData(ConstantsTranslation.BOOK_NOW);
    }
    
    @Test
    public void should_HandleNullQuickBookInfo_When_BuildingCard() {
        // Given
        QuickBookInfo quickBookInfo = null;
        
        // When
        HotelCard result = orchSearchHotelsResponseTransformerPWA.buildQuickBookCard(quickBookInfo);
        
        // Then
        Assert.assertNull(result);
    }
    
    @Test
    public void should_HandlePartialQuickBookInfo_When_SomeFieldsAreNull() {
        // Given
        QuickBookInfo quickBookInfo = new QuickBookInfo();
        quickBookInfo.setTitleWithPrice("Only Title");
        quickBookInfo.setRoomPersuasion(null);
        quickBookInfo.setRoomPersuasionWithSize(null);
        quickBookInfo.setShowQuickBookCard(false);
        
        // Setup polyglot service mock
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW)).thenReturn("Book Now");
        
        // When
        HotelCard result = orchSearchHotelsResponseTransformerPWA.buildQuickBookCard(quickBookInfo);
        
        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals("Only Title", result.getHeading());
        Assert.assertNull(result.getSubHeading());
        Assert.assertNull(result.getRoomSubHeading());
        Assert.assertFalse(result.getShowCard());
        Assert.assertEquals("Book Now", result.getCta());
        Assert.assertNotNull(result.getBgLinearGradient());
    }
    
    @Test
    public void should_HandleEmptyQuickBookInfo_When_AllFieldsAreEmpty() {
        // Given
        QuickBookInfo quickBookInfo = new QuickBookInfo();
        quickBookInfo.setTitleWithPrice("");
        quickBookInfo.setRoomPersuasion("");
        quickBookInfo.setRoomPersuasionWithSize("");
        quickBookInfo.setShowQuickBookCard(true);
        
        // Setup polyglot service mock
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW)).thenReturn("Book Now");
        
        // When
        HotelCard result = orchSearchHotelsResponseTransformerPWA.buildQuickBookCard(quickBookInfo);
        
        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals("", result.getHeading());
        Assert.assertEquals("", result.getSubHeading());
        Assert.assertEquals("", result.getRoomSubHeading());
        Assert.assertTrue(result.getShowCard());
        Assert.assertEquals("Book Now", result.getCta());
        Assert.assertNotNull(result.getBgLinearGradient());
    }
    
    @Test
    public void should_AlwaysSetCorrectGradient_When_BuildingCard() {
        // Given
        QuickBookInfo quickBookInfo = createQuickBookInfo();
        
        // Setup polyglot service mock
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW)).thenReturn("Book Now");
        
        // When
        HotelCard result = orchSearchHotelsResponseTransformerPWA.buildQuickBookCard(quickBookInfo);
        
        // Then
        BGLinearGradient gradient = result.getBgLinearGradient();
        Assert.assertNotNull(gradient);
        Assert.assertEquals("leftBottomToTopRight", gradient.getDirection());
        Assert.assertEquals("#2D6F95", gradient.getStart());
        Assert.assertEquals("#192B43", gradient.getEnd());
    }
    
    @Test
    public void should_HandleSpecialCharactersInQuickBookInfo_When_BuildingCard() {
        // Given
        QuickBookInfo quickBookInfo = new QuickBookInfo();
        quickBookInfo.setTitleWithPrice("Special chars: @#$%^&*()");
        quickBookInfo.setRoomPersuasion("Unicode: ñáéíóú");
        quickBookInfo.setRoomPersuasionWithSize("Symbols: €£¥");
        quickBookInfo.setShowQuickBookCard(true);
        
        // Setup polyglot service mock
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW)).thenReturn("Book Now");
        
        // When
        HotelCard result = orchSearchHotelsResponseTransformerPWA.buildQuickBookCard(quickBookInfo);
        
        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals("Special chars: @#$%^&*()", result.getHeading());
        Assert.assertEquals("Unicode: ñáéíóú", result.getSubHeading());
        Assert.assertEquals("Symbols: €£¥", result.getRoomSubHeading());
        Assert.assertTrue(result.getShowCard());
        Assert.assertEquals("Book Now", result.getCta());
    }
    
    @Test
    public void should_HandleLongStringsInQuickBookInfo_When_BuildingCard() {
        // Given
        QuickBookInfo quickBookInfo = new QuickBookInfo();
        String longTitle = "This is a very long title that might exceed normal length limits and should be handled gracefully by the system";
        String longPersuasion = "This is a very long room persuasion text that contains multiple sentences and detailed information about the room amenities and features";
        String longPersuasionWithSize = "This is a very long room persuasion with size information that includes detailed descriptions of room dimensions and layout";
        
        quickBookInfo.setTitleWithPrice(longTitle);
        quickBookInfo.setRoomPersuasion(longPersuasion);
        quickBookInfo.setRoomPersuasionWithSize(longPersuasionWithSize);
        quickBookInfo.setShowQuickBookCard(true);
        
        // Setup polyglot service mock
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW)).thenReturn("Book Now");
        
        // When
        HotelCard result = orchSearchHotelsResponseTransformerPWA.buildQuickBookCard(quickBookInfo);
        
        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals(longTitle, result.getHeading());
        Assert.assertEquals(longPersuasion, result.getSubHeading());
        Assert.assertEquals(longPersuasionWithSize, result.getRoomSubHeading());
        Assert.assertTrue(result.getShowCard());
        Assert.assertEquals("Book Now", result.getCta());
    }
    
    @Test
    public void should_HandleNullTranslationService_When_BuildingCard() {
        // Given
        QuickBookInfo quickBookInfo = createQuickBookInfo();
        
        // Setup null polyglot service
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "polyglotService", null);
        
        // When & Then - should handle gracefully without throwing NPE
        try {
            HotelCard result = orchSearchHotelsResponseTransformerPWA.buildQuickBookCard(quickBookInfo);
            // If no exception, verify basic structure
            Assert.assertNotNull(result);
            Assert.assertNotNull(result.getBgLinearGradient());
        } catch (Exception e) {
            // If exception occurs, it should be handled gracefully
            Assert.assertTrue("Should handle null polyglot service gracefully", e instanceof NullPointerException);
        }
    }
    
    @Test
    public void should_HandleShowCardFalse_When_QuickBookInfoShowsCardFalse() {
        // Given
        QuickBookInfo quickBookInfo = createQuickBookInfo();
        quickBookInfo.setShowQuickBookCard(false);
        
        // Setup polyglot service mock
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW)).thenReturn("Book Now");
        
        // When
        HotelCard result = orchSearchHotelsResponseTransformerPWA.buildQuickBookCard(quickBookInfo);
        
        // Then
        Assert.assertNotNull(result);
        Assert.assertFalse(result.getShowCard());
        Assert.assertEquals("Test Title With Price", result.getHeading());
        Assert.assertEquals("Test Room Persuasion", result.getSubHeading());
        Assert.assertEquals("Test Room Persuasion With Size", result.getRoomSubHeading());
        Assert.assertEquals("Book Now", result.getCta());
        Assert.assertNotNull(result.getBgLinearGradient());
    }
    
    @Test
    public void should_HandleCompleteScenario_When_AllFieldsAndTranslationWork() {
        // Given
        QuickBookInfo quickBookInfo = new QuickBookInfo();
        quickBookInfo.setTitleWithPrice("Deluxe Room - ₹5000");
        quickBookInfo.setRoomPersuasion("Free Wi-Fi, Breakfast included");
        quickBookInfo.setRoomPersuasionWithSize("King bed, 25 sqm");
        quickBookInfo.setShowQuickBookCard(true);
        
        // Setup polyglot service mock
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW)).thenReturn("अभी बुक करें");
        
        // When
        HotelCard result = orchSearchHotelsResponseTransformerPWA.buildQuickBookCard(quickBookInfo);
        
        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals("Deluxe Room - ₹5000", result.getHeading());
        Assert.assertEquals("Free Wi-Fi, Breakfast included", result.getSubHeading());
        Assert.assertEquals("King bed, 25 sqm", result.getRoomSubHeading());
        Assert.assertTrue(result.getShowCard());
        Assert.assertEquals("अभी बुक करें", result.getCta());
        
        // Verify gradient is correctly set
        BGLinearGradient gradient = result.getBgLinearGradient();
        Assert.assertNotNull(gradient);
        Assert.assertEquals("leftBottomToTopRight", gradient.getDirection());
        Assert.assertEquals("#2D6F95", gradient.getStart());
        Assert.assertEquals("#192B43", gradient.getEnd());
        
        // Verify polyglot service was called
        verify(polyglotService).getTranslatedData(ConstantsTranslation.BOOK_NOW);
    }
    
    @Test
    public void should_HandleEdgeCasesInGradientCreation_When_BuildingCard() {
        // Given
        QuickBookInfo quickBookInfo = createQuickBookInfo();
        
        // Setup polyglot service mock
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerPWA, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW)).thenReturn("Book Now");
        
        // When
        HotelCard result = orchSearchHotelsResponseTransformerPWA.buildQuickBookCard(quickBookInfo);
        
        // Then - Verify gradient is always created with correct PWA-specific values
        BGLinearGradient gradient = result.getBgLinearGradient();
        Assert.assertNotNull(gradient);
        
        // PWA uses simple mobile-style gradient (same as Android/iOS)
        Assert.assertEquals("leftBottomToTopRight", gradient.getDirection());
        Assert.assertEquals("#2D6F95", gradient.getStart());
        Assert.assertEquals("#192B43", gradient.getEnd());
        
        // Verify no desktop-style percentage values
        Assert.assertFalse(gradient.getStart().contains("%"));
        Assert.assertFalse(gradient.getEnd().contains("%"));
        Assert.assertFalse(gradient.getDirection().contains("deg"));
    }
    
    // ========== Helper Methods ==========
    
    private QuickBookInfo createQuickBookInfo() {
        QuickBookInfo quickBookInfo = new QuickBookInfo();
        quickBookInfo.setTitleWithPrice("Test Title With Price");
        quickBookInfo.setRoomPersuasion("Test Room Persuasion");
        quickBookInfo.setRoomPersuasionWithSize("Test Room Persuasion With Size");
        quickBookInfo.setShowQuickBookCard(true);
        return quickBookInfo;
    }
    
    private HotelDetails createNonBudgetHotel() {
        HotelDetails hotel = new HotelDetails();
        hotel.setBudgetHotel(false);
        return hotel;
    }
    
    private HotelDetails createBudgetHotel() {
        HotelDetails hotel = new HotelDetails();
        hotel.setBudgetHotel(true);
        return hotel;
    }
}