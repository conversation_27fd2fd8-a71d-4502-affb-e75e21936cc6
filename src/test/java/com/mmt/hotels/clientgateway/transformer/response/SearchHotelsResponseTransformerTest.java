package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.request.AlternateBookingInfo;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class SearchHotelsResponseTransformerTest {


    // HTL-63666 Alternate Booking Deep Link Test Cases
    @Test
    public void should_AppendOldBookingIdToDetailDeepLink_When_AlternateBookingIsTrue() {
        // Arrange
        SearchWrapperHotelEntity hotelEntity = createHotelEntityWithDetailDeeplink();
        SearchHotelsRequest searchHotelsRequest = createSearchHotelsRequestWithAlternateBooking();
        
        // Test the logic directly by examining how it modifies the detail deeplink URL
        String originalUrl = "https://www.makemytrip.com/hotels/hotel-details?hotelId=123";
        hotelEntity.setDetailDeeplinkUrl(originalUrl);

        // Act - simulate the logic from buildPersonalizedHotels method
        String detailDeeplinkUrl = hotelEntity.getDetailDeeplinkUrl();
        if (searchHotelsRequest != null && 
            searchHotelsRequest.getAlternateBookingInfo() != null && 
            searchHotelsRequest.getAlternateBookingInfo().isAlternateBooking() &&
            org.apache.commons.lang3.StringUtils.isNotEmpty(searchHotelsRequest.getAlternateBookingInfo().getOldBookingId())) {
            
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(detailDeeplinkUrl)) {
                String separator = detailDeeplinkUrl.contains("?") ? "&" : "?";
                detailDeeplinkUrl += separator + "oldBookingId=" + searchHotelsRequest.getAlternateBookingInfo().getOldBookingId();
            }
        }

        // Assert
        Assert.assertNotNull("Detail deeplink URL should not be null", detailDeeplinkUrl);
        Assert.assertTrue("Detail deeplink should contain oldBookingId parameter", 
                detailDeeplinkUrl.contains("oldBookingId=HTL123456789"));
        Assert.assertTrue("Detail deeplink should contain original URL content", 
                detailDeeplinkUrl.contains("hotelId=123"));
        Assert.assertTrue("Detail deeplink should use & separator for additional parameter", 
                detailDeeplinkUrl.contains("&oldBookingId="));
    }

    @Test
    public void should_NotAppendOldBookingId_When_AlternateBookingIsFalse() {
        // Arrange
        SearchWrapperHotelEntity hotelEntity = createHotelEntityWithDetailDeeplink();
        SearchHotelsRequest searchHotelsRequest = createSearchHotelsRequestWithAlternateBookingFalse();
        
        String originalUrl = "https://www.makemytrip.com/hotels/hotel-details?hotelId=123";
        hotelEntity.setDetailDeeplinkUrl(originalUrl);

        // Act - simulate the logic from buildPersonalizedHotels method
        String detailDeeplinkUrl = hotelEntity.getDetailDeeplinkUrl();
        if (searchHotelsRequest != null && 
            searchHotelsRequest.getAlternateBookingInfo() != null && 
            searchHotelsRequest.getAlternateBookingInfo().isAlternateBooking() &&
            org.apache.commons.lang3.StringUtils.isNotEmpty(searchHotelsRequest.getAlternateBookingInfo().getOldBookingId())) {
            
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(detailDeeplinkUrl)) {
                String separator = detailDeeplinkUrl.contains("?") ? "&" : "?";
                detailDeeplinkUrl += separator + "oldBookingId=" + searchHotelsRequest.getAlternateBookingInfo().getOldBookingId();
            }
        }

        // Assert
        Assert.assertNotNull("Detail deeplink URL should not be null", detailDeeplinkUrl);
        Assert.assertFalse("Detail deeplink should not contain oldBookingId parameter", 
                detailDeeplinkUrl.contains("oldBookingId="));
        Assert.assertEquals("Detail deeplink should remain unchanged", originalUrl, detailDeeplinkUrl);
    }

    @Test
    public void should_NotAppendOldBookingId_When_AlternateBookingInfoIsNull() {
        // Arrange
        SearchWrapperHotelEntity hotelEntity = createHotelEntityWithDetailDeeplink();
        SearchHotelsRequest searchHotelsRequest = createSearchHotelsRequestWithoutAlternateBooking();
        
        String originalUrl = "https://www.makemytrip.com/hotels/hotel-details?hotelId=123";
        hotelEntity.setDetailDeeplinkUrl(originalUrl);

        // Act - simulate the logic from buildPersonalizedHotels method
        String detailDeeplinkUrl = hotelEntity.getDetailDeeplinkUrl();
        if (searchHotelsRequest != null && 
            searchHotelsRequest.getAlternateBookingInfo() != null && 
            searchHotelsRequest.getAlternateBookingInfo().isAlternateBooking() &&
            org.apache.commons.lang3.StringUtils.isNotEmpty(searchHotelsRequest.getAlternateBookingInfo().getOldBookingId())) {
            
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(detailDeeplinkUrl)) {
                String separator = detailDeeplinkUrl.contains("?") ? "&" : "?";
                detailDeeplinkUrl += separator + "oldBookingId=" + searchHotelsRequest.getAlternateBookingInfo().getOldBookingId();
            }
        }

        // Assert
        Assert.assertNotNull("Detail deeplink URL should not be null", detailDeeplinkUrl);
        Assert.assertFalse("Detail deeplink should not contain oldBookingId parameter", 
                detailDeeplinkUrl.contains("oldBookingId="));
        Assert.assertEquals("Detail deeplink should remain unchanged", originalUrl, detailDeeplinkUrl);
    }

    @Test
    public void should_NotAppendOldBookingId_When_OldBookingIdIsEmpty() {
        // Arrange
        SearchWrapperHotelEntity hotelEntity = createHotelEntityWithDetailDeeplink();
        SearchHotelsRequest searchHotelsRequest = createSearchHotelsRequestWithEmptyOldBookingId();
        
        String originalUrl = "https://www.makemytrip.com/hotels/hotel-details?hotelId=123";
        hotelEntity.setDetailDeeplinkUrl(originalUrl);

        // Act - simulate the logic from buildPersonalizedHotels method
        String detailDeeplinkUrl = hotelEntity.getDetailDeeplinkUrl();
        if (searchHotelsRequest != null && 
            searchHotelsRequest.getAlternateBookingInfo() != null && 
            searchHotelsRequest.getAlternateBookingInfo().isAlternateBooking() &&
            org.apache.commons.lang3.StringUtils.isNotEmpty(searchHotelsRequest.getAlternateBookingInfo().getOldBookingId())) {
            
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(detailDeeplinkUrl)) {
                String separator = detailDeeplinkUrl.contains("?") ? "&" : "?";
                detailDeeplinkUrl += separator + "oldBookingId=" + searchHotelsRequest.getAlternateBookingInfo().getOldBookingId();
            }
        }

        // Assert
        Assert.assertNotNull("Detail deeplink URL should not be null", detailDeeplinkUrl);
        Assert.assertFalse("Detail deeplink should not contain oldBookingId parameter", 
                detailDeeplinkUrl.contains("oldBookingId="));
        Assert.assertEquals("Detail deeplink should remain unchanged", originalUrl, detailDeeplinkUrl);
    }

    @Test
    public void should_AppendOldBookingIdWithQuestionMark_When_URLHasNoQueryParameters() {
        // Arrange
        SearchWrapperHotelEntity hotelEntity = createHotelEntityWithDetailDeeplink();
        SearchHotelsRequest searchHotelsRequest = createSearchHotelsRequestWithAlternateBooking();
        
        String originalUrl = "https://www.makemytrip.com/hotels/hotel-details";
        hotelEntity.setDetailDeeplinkUrl(originalUrl);

        // Act - simulate the logic from buildPersonalizedHotels method
        String detailDeeplinkUrl = hotelEntity.getDetailDeeplinkUrl();
        if (searchHotelsRequest != null && 
            searchHotelsRequest.getAlternateBookingInfo() != null && 
            searchHotelsRequest.getAlternateBookingInfo().isAlternateBooking() &&
            org.apache.commons.lang3.StringUtils.isNotEmpty(searchHotelsRequest.getAlternateBookingInfo().getOldBookingId())) {
            
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(detailDeeplinkUrl)) {
                String separator = detailDeeplinkUrl.contains("?") ? "&" : "?";
                detailDeeplinkUrl += separator + "oldBookingId=" + searchHotelsRequest.getAlternateBookingInfo().getOldBookingId();
            }
        }

        // Assert
        Assert.assertNotNull("Detail deeplink URL should not be null", detailDeeplinkUrl);
        Assert.assertTrue("Detail deeplink should contain oldBookingId parameter with ? separator", 
                detailDeeplinkUrl.contains("?oldBookingId=HTL123456789"));
        Assert.assertTrue("Detail deeplink should contain original URL content", 
                detailDeeplinkUrl.contains("hotel-details"));
    }

    @Test
    public void should_HandleSpecialCharactersInOldBookingId_When_OldBookingIdContainsSpecialChars() {
        // Arrange
        SearchWrapperHotelEntity hotelEntity = createHotelEntityWithDetailDeeplink();
        SearchHotelsRequest searchHotelsRequest = createSearchHotelsRequestWithSpecialCharsInBookingId();
        
        String originalUrl = "https://www.makemytrip.com/hotels/hotel-details?hotelId=123";
        hotelEntity.setDetailDeeplinkUrl(originalUrl);

        // Act - simulate the logic from buildPersonalizedHotels method
        String detailDeeplinkUrl = hotelEntity.getDetailDeeplinkUrl();
        if (searchHotelsRequest != null && 
            searchHotelsRequest.getAlternateBookingInfo() != null && 
            searchHotelsRequest.getAlternateBookingInfo().isAlternateBooking() &&
            org.apache.commons.lang3.StringUtils.isNotEmpty(searchHotelsRequest.getAlternateBookingInfo().getOldBookingId())) {
            
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(detailDeeplinkUrl)) {
                String separator = detailDeeplinkUrl.contains("?") ? "&" : "?";
                detailDeeplinkUrl += separator + "oldBookingId=" + searchHotelsRequest.getAlternateBookingInfo().getOldBookingId();
            }
        }

        // Assert
        Assert.assertNotNull("Detail deeplink URL should not be null", detailDeeplinkUrl);
        Assert.assertTrue("Detail deeplink should contain oldBookingId parameter with special characters", 
                detailDeeplinkUrl.contains("oldBookingId=HTL-123&456"));
    }

    @Test
    public void should_NotAppendOldBookingId_When_DetailDeeplinkUrlIsEmpty() {
        // Arrange
        SearchWrapperHotelEntity hotelEntity = createHotelEntityWithDetailDeeplink();
        SearchHotelsRequest searchHotelsRequest = createSearchHotelsRequestWithAlternateBooking();
        
        String originalUrl = ""; // Empty URL
        hotelEntity.setDetailDeeplinkUrl(originalUrl);

        // Act - simulate the logic from buildPersonalizedHotels method
        String detailDeeplinkUrl = hotelEntity.getDetailDeeplinkUrl();
        if (searchHotelsRequest != null && 
            searchHotelsRequest.getAlternateBookingInfo() != null && 
            searchHotelsRequest.getAlternateBookingInfo().isAlternateBooking() &&
            org.apache.commons.lang3.StringUtils.isNotEmpty(searchHotelsRequest.getAlternateBookingInfo().getOldBookingId())) {
            
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(detailDeeplinkUrl)) {
                String separator = detailDeeplinkUrl.contains("?") ? "&" : "?";
                detailDeeplinkUrl += separator + "oldBookingId=" + searchHotelsRequest.getAlternateBookingInfo().getOldBookingId();
            }
        }

        // Assert
        Assert.assertEquals("Detail deeplink URL should remain empty", "", detailDeeplinkUrl);
        Assert.assertFalse("Detail deeplink should not contain oldBookingId parameter", 
                detailDeeplinkUrl.contains("oldBookingId="));
    }

    @Test
    public void should_NotAppendOldBookingId_When_SearchHotelsRequestIsNull() {
        // Arrange
        SearchWrapperHotelEntity hotelEntity = createHotelEntityWithDetailDeeplink();
        SearchHotelsRequest searchHotelsRequest = null;
        
        String originalUrl = "https://www.makemytrip.com/hotels/hotel-details?hotelId=123";
        hotelEntity.setDetailDeeplinkUrl(originalUrl);

        // Act - simulate the logic from buildPersonalizedHotels method
        String detailDeeplinkUrl = hotelEntity.getDetailDeeplinkUrl();
        if (searchHotelsRequest != null && 
            searchHotelsRequest.getAlternateBookingInfo() != null && 
            searchHotelsRequest.getAlternateBookingInfo().isAlternateBooking() &&
            org.apache.commons.lang3.StringUtils.isNotEmpty(searchHotelsRequest.getAlternateBookingInfo().getOldBookingId())) {
            
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(detailDeeplinkUrl)) {
                String separator = detailDeeplinkUrl.contains("?") ? "&" : "?";
                detailDeeplinkUrl += separator + "oldBookingId=" + searchHotelsRequest.getAlternateBookingInfo().getOldBookingId();
            }
        }

        // Assert
        Assert.assertNotNull("Detail deeplink URL should not be null", detailDeeplinkUrl);
        Assert.assertFalse("Detail deeplink should not contain oldBookingId parameter", 
                detailDeeplinkUrl.contains("oldBookingId="));
        Assert.assertEquals("Detail deeplink should remain unchanged", originalUrl, detailDeeplinkUrl);
    }

    // Helper methods for creating test objects
    private SearchWrapperHotelEntity createHotelEntityWithDetailDeeplink() {
        SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
        hotelEntity.setId("TEST_HOTEL_123");
        hotelEntity.setDetailDeeplinkUrl("https://www.makemytrip.com/hotels/hotel-details?hotelId=123");
        return hotelEntity;
    }

    private SearchHotelsRequest createSearchHotelsRequestWithAlternateBooking() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        
        AlternateBookingInfo alternateBookingInfo = new AlternateBookingInfo();
        alternateBookingInfo.setAlternateBooking(true);
        alternateBookingInfo.setOldBookingId("HTL123456789");
        alternateBookingInfo.setKey("test-key");
        request.setAlternateBookingInfo(alternateBookingInfo);
        
        return request;
    }

    private SearchHotelsRequest createSearchHotelsRequestWithAlternateBookingFalse() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        
        AlternateBookingInfo alternateBookingInfo = new AlternateBookingInfo();
        alternateBookingInfo.setAlternateBooking(false); // False
        alternateBookingInfo.setOldBookingId("HTL123456789");
        request.setAlternateBookingInfo(alternateBookingInfo);
        
        return request;
    }

    private SearchHotelsRequest createSearchHotelsRequestWithoutAlternateBooking() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        request.setAlternateBookingInfo(null); // Null
        
        return request;
    }

    private SearchHotelsRequest createSearchHotelsRequestWithEmptyOldBookingId() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        
        AlternateBookingInfo alternateBookingInfo = new AlternateBookingInfo();
        alternateBookingInfo.setAlternateBooking(true);
        alternateBookingInfo.setOldBookingId(""); // Empty string
        request.setAlternateBookingInfo(alternateBookingInfo);
        
        return request;
    }

    private SearchHotelsRequest createSearchHotelsRequestWithSpecialCharsInBookingId() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        
        AlternateBookingInfo alternateBookingInfo = new AlternateBookingInfo();
        alternateBookingInfo.setAlternateBooking(true);
        alternateBookingInfo.setOldBookingId("HTL-123&456"); // Special characters
        request.setAlternateBookingInfo(alternateBookingInfo);
        
        return request;
    }
}
