package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.gstDetails.SaveGstDetailsRequest;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.runners.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class GstDetailsRequestTransformerTest {

    @InjectMocks
    GstDetailsRequestTransformer gstDetailsRequestTransformer;

    @Test
    public void convertToTravellerGstDetails_test() {
        SaveGstDetailsRequest saveGstDetailsRequest = new SaveGstDetailsRequest();
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setUuid("test");
        commonModifierResponse.setExtendedUser(extendedUser);
        gstDetailsRequestTransformer.convertToTravellerGstDetails(saveGstDetailsRequest, commonModifierResponse);
    }

}