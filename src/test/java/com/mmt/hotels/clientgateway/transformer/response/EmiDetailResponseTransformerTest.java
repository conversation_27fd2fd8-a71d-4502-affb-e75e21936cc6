package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.response.emi.EMIDetailResponse;
import com.mmt.hotels.clientgateway.response.emi.EmiBankDetails;
import com.mmt.hotels.clientgateway.response.emi.EmiPlanType;
import com.mmt.hotels.clientgateway.response.searchHotels.BgGradient;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.emi.detail.Error;
import com.mmt.hotels.emi.detail.*;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class EmiDetailResponseTransformerTest {

    @InjectMocks
    private EmiDetailResponseTransformer emiDetailResponseTransformer;

    @Mock
    private CommonConfigConsul commonConfigConsul;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    private BgGradient noCostEmiIconConfig;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        Mockito.when(commonConfigConsul.getNoCostEmiIconConfig()).thenReturn(noCostEmiIconConfig);
        emiDetailResponseTransformer.init();
    }

    @Test
    public void testConvertEmiDetailResponse_NullEmiDetailResponse() {
        EMIDetailResponse result = emiDetailResponseTransformer.convertEmiDetailResponse(null);
        Assert.assertEquals(Constants.FAILURE, result.getStatus());
    }

    @Test
    public void testConvertEmiDetailResponse_FailureStatus() {
        EmiDetailResponse emiDetailResponse = EmiDetailResponse.newBuilder().setStatus(Constants.FAILURE).build();
        EMIDetailResponse result = emiDetailResponseTransformer.convertEmiDetailResponse(emiDetailResponse);
        Assert.assertEquals(Constants.FAILURE, result.getStatus());
    }

    @Test
    public void testConvertEmiDetailResponse_ErrorResponse() {
        EmiDetailResponse.Builder emiDetailResponse = EmiDetailResponse.newBuilder().setStatus(Constants.SUCCESS);
        ErrorResponse.Builder errorResponse = ErrorResponse.newBuilder();
        errorResponse.addAllErrorList(Collections.singletonList(Error.newBuilder().build()));
        emiDetailResponse.setErrorResponse(errorResponse.build());
        EMIDetailResponse result = emiDetailResponseTransformer.convertEmiDetailResponse(emiDetailResponse.build());
        Assert.assertEquals(Constants.FAILURE, result.getStatus());
    }

    @Test
    public void testConvertEmiDetailResponse_EmptyBanksMap() {
        EmiDetailResponse.Builder emiDetailResponse = EmiDetailResponse.newBuilder().setStatus(Constants.SUCCESS);
        emiDetailResponse.putAllBanks(Collections.emptyMap());
        EMIDetailResponse result = emiDetailResponseTransformer.convertEmiDetailResponse(emiDetailResponse.build());
        Assert.assertEquals(Constants.FAILURE, result.getStatus());
    }

    @Test
    public void testConvertEmiDetailResponse_ValidBanksMap() {
        EmiDetailResponse.Builder emiDetailResponse = EmiDetailResponse.newBuilder().setStatus(Constants.SUCCESS);
        emiDetailResponse.setNoCostEmiAvailable(false);

        Map<String, BankDetailsMap> banksMap = new HashMap<>();
        BankDetailsMap.Builder ccBanks = BankDetailsMap.newBuilder();
        banksMap.put("ccBanks", ccBanks.build());
        emiDetailResponse.putAllBanks(banksMap);

        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.EMI_L1_PAGE_TITLE)).thenReturn("Page Title");
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.EMI_L1_PAGE_SUB_TITLE)).thenReturn("Sub Page Title");

        EMIDetailResponse result = emiDetailResponseTransformer.convertEmiDetailResponse(emiDetailResponse.build());

        assertNotNull(result.getHeader());
        Assert.assertEquals("Page Title", result.getHeader().getPageTitle());
        Assert.assertEquals("Sub Page Title", result.getHeader().getSubPageTitle());
    }

    @Test
    public void testProcessBankDetails_ValidBankDetailsMap() {
        BankDetailsMap.Builder bankDetailsMap = BankDetailsMap.newBuilder();
        Map<String, BankDetailsList> bankDetailMapMap = new HashMap<>();
        BankDetailsList.Builder bankDetailsList = BankDetailsList.newBuilder();
        BankDetails.Builder bankDetail = BankDetails.newBuilder();
        bankDetail.setName("Bank Name");
        bankDetailsList.addAllBankDetails(Collections.singletonList(bankDetail.build()));
        bankDetailMapMap.put("Bank Key", bankDetailsList.build());
        bankDetailsMap.putAllBankDetailMap(bankDetailMapMap);

        ReflectionTestUtils.invokeMethod(emiDetailResponseTransformer, "processBankDetails", bankDetailsMap.build(), "Credit Card", "Credit Card EMI Plans");
    }

    @Test
    public void testProcessBankDetail_EmptyBankDetails() {
        List<BankDetails> bankDetails = Collections.emptyList();
        List<EmiBankDetails> emiBankDetailsList = new ArrayList<>();
        ReflectionTestUtils.invokeMethod(emiDetailResponseTransformer, "processBankDetail", bankDetails, emiBankDetailsList, "Key");

        Assert.assertTrue(emiBankDetailsList.isEmpty());
    }

    @Test
    public void testProcessBankDetail_ValidBankDetails() {
        BankDetails.Builder bankDetail = BankDetails.newBuilder();
        bankDetail.setName("Bank Name");
        bankDetail.setBankLogo("Bank Logo");
        bankDetail.setBestEmiType("No Cost Emi");
        bankDetail.setCouponCode("Coupon Code");
        bankDetail.setCouponDiscount(10);
        List<BankDetails> bankDetails = Collections.singletonList(bankDetail.build());
        List<EmiBankDetails> emiBankDetailsList = new ArrayList<>();

        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.NO_COST_EMI_COUPON_CODE_AND_DISCOUNT_TEXT))
                .thenReturn("{0}% off with coupon code {1}");

        ReflectionTestUtils.invokeMethod(emiDetailResponseTransformer, "processBankDetail", bankDetails, emiBankDetailsList, "Key");

        Assert.assertEquals(1, emiBankDetailsList.size());
        EmiBankDetails result = emiBankDetailsList.get(0);
        Assert.assertEquals("Bank Name", result.getName());
        Assert.assertEquals("Bank Logo", result.getBankLogo());
        Assert.assertEquals(EmiPlanType.NO_COST.name(), result.getTagType());
        Assert.assertEquals("10% off with coupon code Coupon Code", result.getDescription());
    }

    @Test
    public void testProcessEmiPlanDetails_ValidEmiPlanDetailsList() {
        EMIPlanDetails.Builder planDetail = EMIPlanDetails.newBuilder();
        planDetail.setTotalCost(10000);
        planDetail.setEmiAmount(1000);
        planDetail.setEmiType("NO_COST");
        planDetail.setTenure(10);
        planDetail.setTotalInterest(500);
        planDetail.setAnnualInterest(10);

        List<EMIPlanDetails> emiPlanDetailsList = Collections.singletonList(planDetail.build());
        EmiBankDetails emiBankDetails = new EmiBankDetails();

        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.EMI_L2_PAGE_TITLE)).thenReturn("EMI Title {0}");
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.EMI_L1_ALERT_TEXT)).thenReturn("Lowest EMI {0}");

        ReflectionTestUtils.invokeMethod(emiDetailResponseTransformer, "processEmiPlanDetails", emiPlanDetailsList, "Bank Name", emiBankDetails);
    }

}