package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.HostCallingRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.HostCallingRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.HostCallingRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.HostCallingRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.HostCallingRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.HostCallingResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.HostCallingResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.HostCallingResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.HostCallingResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.HostCallingResponseTransformerPWA;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class HostCallingFactoryTest {

    @InjectMocks
    HostCallingFactory hostCallingFactory;

    @Before
    public void init() {
        ReflectionTestUtils.setField(hostCallingFactory, "hostCallingResponseTransformerPWA", new HostCallingResponseTransformerPWA());
        ReflectionTestUtils.setField(hostCallingFactory, "hostCallingResponseTransformerDesktop", new HostCallingResponseTransformerDesktop());
        ReflectionTestUtils.setField(hostCallingFactory, "hostCallingResponseTransformerAndroid", new HostCallingResponseTransformerAndroid());
        ReflectionTestUtils.setField(hostCallingFactory, "hostCallingResponseTransformerIOS", new HostCallingResponseTransformerIOS());
        ReflectionTestUtils.setField(hostCallingFactory, "hostCallingRequestTransformerPWA", new HostCallingRequestTransformerPWA());
        ReflectionTestUtils.setField(hostCallingFactory, "hostCallingRequestTransformerDesktop", new HostCallingRequestTransformerDesktop());
        ReflectionTestUtils.setField(hostCallingFactory, "hostCallingRequestTransformerAndroid", new HostCallingRequestTransformerAndroid());
        ReflectionTestUtils.setField(hostCallingFactory, "hostCallingRequestTransformerIOS", new HostCallingRequestTransformerIOS());
    }

    @Test
    public void should_ReturnPWARequestTransformer_When_PWAClientRequested() {
        // Act
        HostCallingRequestTransformer result = hostCallingFactory.getRequestService("PWA");
        
        // Assert
        Assert.assertTrue(result instanceof HostCallingRequestTransformerPWA);
    }

    @Test
    public void should_ReturnMSITERequestTransformer_When_MSITEClientRequested() {
        // Act
        HostCallingRequestTransformer result = hostCallingFactory.getRequestService("MSITE");
        
        // Assert
        Assert.assertTrue(result instanceof HostCallingRequestTransformerPWA);
    }

    @Test
    public void should_ReturnDesktopRequestTransformer_When_DesktopClientRequested() {
        // Act
        HostCallingRequestTransformer result = hostCallingFactory.getRequestService("DESKTOP");
        
        // Assert
        Assert.assertTrue(result instanceof HostCallingRequestTransformerDesktop);
    }

    @Test
    public void should_ReturnAndroidRequestTransformer_When_AndroidClientRequested() {
        // Act
        HostCallingRequestTransformer result = hostCallingFactory.getRequestService("ANDROID");
        
        // Assert
        Assert.assertTrue(result instanceof HostCallingRequestTransformerAndroid);
    }

    @Test
    public void should_ReturnIOSRequestTransformer_When_IOSClientRequested() {
        // Act
        HostCallingRequestTransformer result = hostCallingFactory.getRequestService("IOS");
        
        // Assert
        Assert.assertTrue(result instanceof HostCallingRequestTransformerIOS);
    }

    @Test
    public void should_ReturnDesktopRequestTransformer_When_EmptyClientRequested() {
        // Act
        HostCallingRequestTransformer result = hostCallingFactory.getRequestService("");
        
        // Assert
        Assert.assertNotNull(result);
        Assert.assertTrue(result instanceof HostCallingRequestTransformerDesktop);
    }

    @Test
    public void should_ReturnDesktopRequestTransformer_When_NullClientRequested() {
        // Act
        HostCallingRequestTransformer result = hostCallingFactory.getRequestService(null);
        
        // Assert
        Assert.assertNotNull(result);
        Assert.assertTrue(result instanceof HostCallingRequestTransformerDesktop);
    }

    @Test
    public void should_ReturnDesktopRequestTransformer_When_UnknownClientRequested() {
        // Act
        HostCallingRequestTransformer result = hostCallingFactory.getRequestService("UNKNOWN");
        
        // Assert
        Assert.assertTrue(result instanceof HostCallingRequestTransformerDesktop);
    }

    @Test
    public void should_ReturnPWAResponseTransformer_When_PWAClientRequested() {
        // Act
        HostCallingResponseTransformer result = hostCallingFactory.getResponseService("PWA");
        
        // Assert
        Assert.assertTrue(result instanceof HostCallingResponseTransformerPWA);
    }

    @Test
    public void should_ReturnMSITEResponseTransformer_When_MSITEClientRequested() {
        // Act
        HostCallingResponseTransformer result = hostCallingFactory.getResponseService("MSITE");
        
        // Assert
        Assert.assertTrue(result instanceof HostCallingResponseTransformerPWA);
    }

    @Test
    public void should_ReturnDesktopResponseTransformer_When_DesktopClientRequested() {
        // Act
        HostCallingResponseTransformer result = hostCallingFactory.getResponseService("DESKTOP");
        
        // Assert
        Assert.assertTrue(result instanceof HostCallingResponseTransformerDesktop);
    }

    @Test
    public void should_ReturnAndroidResponseTransformer_When_AndroidClientRequested() {
        // Act
        HostCallingResponseTransformer result = hostCallingFactory.getResponseService("ANDROID");
        
        // Assert
        Assert.assertTrue(result instanceof HostCallingResponseTransformerAndroid);
    }

    @Test
    public void should_ReturnIOSResponseTransformer_When_IOSClientRequested() {
        // Act
        HostCallingResponseTransformer result = hostCallingFactory.getResponseService("IOS");
        
        // Assert
        Assert.assertTrue(result instanceof HostCallingResponseTransformerIOS);
    }

    @Test
    public void should_ReturnDesktopResponseTransformer_When_EmptyClientRequested() {
        // Act
        HostCallingResponseTransformer result = hostCallingFactory.getResponseService("");
        
        // Assert
        Assert.assertNotNull(result);
        Assert.assertTrue(result instanceof HostCallingResponseTransformerDesktop);
    }

    @Test
    public void should_ReturnDesktopResponseTransformer_When_NullClientRequested() {
        // Act
        HostCallingResponseTransformer result = hostCallingFactory.getResponseService(null);
        
        // Assert
        Assert.assertNotNull(result);
        Assert.assertTrue(result instanceof HostCallingResponseTransformerDesktop);
    }

    @Test
    public void should_ReturnDesktopResponseTransformer_When_UnknownClientRequested() {
        // Act
        HostCallingResponseTransformer result = hostCallingFactory.getResponseService("UNKNOWN");
        
        // Assert
        Assert.assertTrue(result instanceof HostCallingResponseTransformerDesktop);
    }

    @Test
    public void should_HandleCaseInsensitiveClientNames() {
        // Test lowercase client names are handled correctly
        HostCallingRequestTransformer request1 = hostCallingFactory.getRequestService("pwa");
        HostCallingRequestTransformer request2 = hostCallingFactory.getRequestService("android");
        HostCallingRequestTransformer request3 = hostCallingFactory.getRequestService("ios");

        HostCallingResponseTransformer response1 = hostCallingFactory.getResponseService("pwa");
        HostCallingResponseTransformer response2 = hostCallingFactory.getResponseService("android");
        HostCallingResponseTransformer response3 = hostCallingFactory.getResponseService("ios");

        // Should default to desktop for non-matching cases
        Assert.assertTrue(request1 instanceof HostCallingRequestTransformerDesktop);
        Assert.assertTrue(request2 instanceof HostCallingRequestTransformerDesktop);
        Assert.assertTrue(request3 instanceof HostCallingRequestTransformerDesktop);

        Assert.assertTrue(response1 instanceof HostCallingResponseTransformerDesktop);
        Assert.assertTrue(response2 instanceof HostCallingResponseTransformerDesktop);
        Assert.assertTrue(response3 instanceof HostCallingResponseTransformerDesktop);
    }
} 