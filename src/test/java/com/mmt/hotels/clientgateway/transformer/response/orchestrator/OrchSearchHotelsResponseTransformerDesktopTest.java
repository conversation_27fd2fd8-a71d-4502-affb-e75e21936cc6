package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gommt.hotels.orchestrator.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.model.response.da.CancellationTimeline;
import com.gommt.hotels.orchestrator.model.response.listing.HotelDetails;
import com.gommt.hotels.orchestrator.model.response.listing.ListingResponse;
import com.gommt.hotels.orchestrator.model.response.listing.MpFareHoldStatus;
import com.gommt.hotels.orchestrator.model.response.listing.PersonalizedSectionDetails;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.PropertyTextConfigConsul;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.pms.PropertyTextConfig;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.Hover;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.listingmap.ListingMapResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.SearchHotelsResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchHotelsResponseTransformer;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.enums.SectionsType;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.hotels.orchestrator.enums.MarkUpType;
import com.mmt.hotels.orchestrator.models.mypartner.MarkUp;
import com.mmt.hotels.orchestrator.models.mypartner.MarkUpDetails;
import com.mmt.propertymanager.config.PropertyManager;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.SHORTSTAYS_FUNNEL;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.GROUP_PRICE_TEXT_ONE_NIGHT_ALT_ACCO;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrchSearchHotelsResponseTransformerDesktopTest {

    @InjectMocks
    OrchSearchHotelsResponseTransformerDesktop orchSearchHotelsResponseTransformerDesktop;

    @Mock
    PropertyManager propManager;

    @Mock
    PropertyTextConfig propertyTextConfig;

    @Mock
    CommonConfig commonConfig;

    @Mock
    CommonConfigConsul commonConfigConsul;

    @Mock
    PropertyTextConfigConsul propertyTextConfigConsul;

    @Mock
    PolyglotHelper polyglotHelper;

    @Spy
    ObjectMapperUtil objectMapperUtil;

    @Spy
    Utility utility;

    @Spy
    CommonResponseTransformer commonResponseTransformer;

    @Spy
    DateUtil dateUtil;

    @Mock
    PolyglotService polyglotService;

    @Spy
    CommonHelper commonHelper;

    @Mock
    MobConfigHelper mobConfigHelper;

    @Spy
    PersuasionUtil persuasionUtil;


    ObjectMapper mapper;

    @Before
    public void setUp() throws JsonParseException {
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "activeLanguages", "eng,hin,ara");
        ReflectionTestUtils.setField(commonResponseTransformer, "polyglotService", polyglotService);
        when(propManager.getProperty(eq("propertyTextConfig"), eq(PropertyTextConfig.class))).thenReturn(propertyTextConfig);
        when(propManager.getProperty(eq("commonConfig"), eq(CommonConfig.class))).thenReturn(commonConfig);
        when(polyglotService.getTranslatedData(eq(GROUP_PRICE_TEXT_ONE_NIGHT_ALT_ACCO))).thenReturn("{NIGHT_COUNT} Night");
        when(polyglotService.getTranslatedData(eq("GROUP_PRICE_TEXT_ONE_NIGHT_PERNEW"))).thenReturn("Total <b>₹{AMOUNT}</b> for {NIGHT_COUNT} Night, {ROOM_COUNT} Rooms");
        mapper = new ObjectMapper();
        mapper.writerWithView(PIIView.External.class);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "placeHoldersToShowConfig", "{\"SIMILAR_HOTELS\":[\"PLACEHOLDER_IMAGE_LEFT_TOP\",\"PLACEHOLDER_CARD_M4\",\"PLACEHOLDER_CARD_M1\"]}");
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "filterConditionsConfig", "{\"range\":{\"minValue\":0,\"maxValue\":3000},\"categoriesIncluded\":[\"Premium Properties\"],\"categoriesExcluded\":[\"MMT Value Stays\"]}");
        ReflectionTestUtils.setField(utility, "dateUtil", dateUtil);

        String toolTipPersuasions = "{\"HOSTEL\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_HOSTEL\",\"data\":[\"LIGHT_ON_YOUR_WALLET\",\"CENTRALLY_LOCATED\",\"GREAT_FOR_SOCIALIZING\",\"YOUTHFUL_CONNECT\"]},\"VILLA\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_VILLA\",\"data\":[\"VALUE_FOR_MONEY\",\"IDEAL_FOR_GROUP_STAY\",\"SUPER_SPACIOUS\",\"COMPLETE_PRIVACY\"]},\"HOMESTAY\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_HOMESTAY\",\"data\":[\"LIGHT_ON_YOUR_WALLET\",\"HOMELY_COMFORTS\",\"BEST_LOCAL_GUIDANCE\",\"CENTRALLY_LOCATED\"]},\"COTTAGE\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_COTTAGE\",\"data\":[\"IDEAL_FOR_GROUP_STAY\",\"VALUE_FOR_MONEY\",\"SUPER_SPACIOUS\",\"ACCESS_TO_BEAUTIFUL_OUTDOOR_SPACES\"]},\"APARTMENT\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_APARTMENT\",\"data\":[\"VALUE_FOR_MONEY\",\"CENTRALLY_LOCATED\",\"COMPLETE_ACCESS_TO_PROPERTY\",\"SAVINGS_ON_LAUNDRY_AND_FOOD\"]}}";
        Map<String, Object> desktopToolTipPersuasionsMap = objectMapperUtil.getObjectFromJsonWithType(toolTipPersuasions, new TypeReference<Map<String, Object>>() {}, DependencyLayer.CLIENTGATEWAY);
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "desktopToolTipPersuasionsMap", desktopToolTipPersuasionsMap);

        List<String> amenitiesWithUrl = Arrays.asList("kids_play_area,24_hour_room_service,airport_transfer,childcare_services,free_airport_transfer,gym,indoor_games,luggage_storage,outdoor_sports,safe,doctor_on_call,restaurant,spa,free_wi_fi,swimming_pool,room_service,living_room,washing_machine,power_backup,air_conditioning,kitchenette,free_parking,parking,conference,caretaker,bonfire,housekeeping,bar,property_disinfection,sanitizers_installed,staff_hygiene,thermal_screening,balcony_terrace,lawn,conference_room,yoga,golf,nearby_beach,private_beach,lounge,butler_services,boat_ride,atm,tours_and_treks,cafe,business_centre,vehicle_rentals,jungle_safari,elevator_lift,hot_spring_bath,hammam,open_air_bath,plunge_pool,canoeing,free_shuttle_service,beach,night_club,casino,cooking_class,jacuzzi,free_drinking_water,steam_and_sauna,laundromat,kids_club".split(","));
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "amenitiesWithUrl", amenitiesWithUrl);

        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "basicDetailDeeplink", "https://www.makemytrip.com/hotels/hotel-details?hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&openDetail=true&currency={5}&roomStayQualifier={6}&locusId={7}&locusType={8}");
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "rootLevelSharingUrl", "https://app.mmyt.co/Xm2V/hotelListingShare?checkin={0}&checkout={1}&city={2}&country={3}&roomStayQualifier={4}&checkAvailability={5}");
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "rootLevelDeeplink", "https://www.makemytrip.com/hotels/hotel-listing/?checkin={0}&checkout={1}&city={2}&country={3}&roomStayQualifier={4}&checkAvailability={5}&_uCurrency={6}");
    }


    @Test
    public void init() {
        orchSearchHotelsResponseTransformerDesktop.init();
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "objectMapperUtil", new ObjectMapperUtil());
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "consulFlag", true);
        orchSearchHotelsResponseTransformerDesktop.init();
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "toolTipPersuasions", "{\"HOSTEL\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_HOSTEL\",\"data\":[\"LIGHT_ON_YOUR_WALLET\",\"CENTRALLY_LOCATED\",\"GREAT_FOR_SOCIALIZING\",\"YOUTHFUL_CONNECT\"]},\"VILLA\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_VILLA\",\"data\":[\"VALUE_FOR_MONEY\",\"IDEAL_FOR_GROUP_STAY\",\"SUPER_SPACIOUS\",\"COMPLETE_PRIVACY\"]},\"HOMESTAY\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_HOMESTAY\",\"data\":[\"LIGHT_ON_YOUR_WALLET\",\"HOMELY_COMFORTS\",\"BEST_LOCAL_GUIDANCE\",\"CENTRALLY_LOCATED\"]},\"COTTAGE\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_COTTAGE\",\"data\":[\"IDEAL_FOR_GROUP_STAY\",\"VALUE_FOR_MONEY\",\"SUPER_SPACIOUS\",\"ACCESS_TO_BEAUTIFUL_OUTDOOR_SPACES\"]},\"APARTMENT\":{\"imageUrl\":\"\",\"toolTipHeading\":\"WHY_APARTMENT\",\"data\":[\"VALUE_FOR_MONEY\",\"CENTRALLY_LOCATED\",\"COMPLETE_ACCESS_TO_PROPERTY\",\"SAVINGS_ON_LAUNDRY_AND_FOOD\"]}}");
        orchSearchHotelsResponseTransformerDesktop.init();

    }

    @Test
    public void convertSearchHotelsResponseTest() throws JsonProcessingException {
        String listingResponseJson = "{\"requestId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"journeyId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"hotelCount\":1,\"personalizedSections\":[{\"name\":\"RECENTLY_VIEWED_HOTELS\",\"heading\":\"Recently Viewed\",\"hotelCardType\":\"default\",\"orientation\":\"V\",\"hotelCount\":1,\"cardInsertionAllowed\":false,\"hotels\":[{\"id\":\"201106120942397721\",\"name\":\"The Ashok\",\"propertyType\":\"Hotel\",\"propertyLabel\":\"Hotel\",\"detailDeeplinkUrl\":\"https://www.makemytrip.com/hotels/hotel-details?hotelId=201106120942397721&checkin=date_3&checkout=date_4&country=IN&city=CTDEL&roomStayQualifier=2e0e&openDetail=true&currency=INR&region=in&checkAvailability=true&locusId=CTDEL&locusType=city\",\"seoUrl\":\"https://www.makemytrip.com/hotels/the_ashok-details-delhi.html\",\"stayType\":\"Entire\",\"threeSixtyViewIconUrl\":\"https://promos.makemytrip.com/Hotels_product/Listing/3603x.png\",\"starRating\":5,\"totalRoomCount\":1,\"soldOut\":false,\"showCallToBook\":false,\"budgetHotel\":false,\"groupBookingHotel\":false,\"groupBookingPrice\":false,\"categories\":[\"Festive Weekend Deals\",\"PREMIND\",\"Deals for Vaccinated Travellers\",\"Child Friendly\",\"Inside BD\",\"Inmarket\",\"Hills\",\"Central Heating\",\"Great Value Packages\",\"kids_stay_free\",\"Premium Properties\",\"Last Minute Deals\",\"premium_hotels\"],\"facilityHighlights\":[\"Spa\",\"Restaurant\",\"Bar\"],\"location\":{\"id\":\"CTDEL\",\"type\":\"znshim\",\"countryId\":\"IN\",\"countryName\":null,\"cityId\":\"ZNSHIM\",\"cityName\":\"\",\"stateId\":null,\"geo\":{\"latitude\":\"\",\"longitude\":\"\"}},\"media\":{\"images\":[{\"url\":\"http://r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201106120942397721-afe9941a2d1811eea3cd0a58a9feac02.jpg?output-quality=75&downsize=243:162&output-format=webp\"},{\"url\":\"http://r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201106120942397721-3ce394a835be11ee982f0a58a9feac02.jpg?output-quality=75&downsize=243:162&output-format=webp\"},{\"url\":\"http://r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201106120942397721-21bae8ca35be11eeb7ae0a58a9feac02.jpg?output-quality=75&downsize=243:162&output-format=webp\"}]},\"omnitureFlags\":{\"rtb\":false,\"abso\":false,\"abo\":false,\"wishlisted\":false},\"reviewDetails\":{\"rating\":3.8,\"totalReviewCount\":3738,\"totalRatingCount\":7157,\"subRatings\":[{\"name\":\"Location\",\"rating\":4.5,\"reviewCount\":851,\"show\":false},{\"name\":\"Hospitality\",\"rating\":3.9,\"reviewCount\":2096,\"show\":true},{\"name\":\"Facilities\",\"rating\":3.8,\"reviewCount\":1106,\"show\":true}],\"ratingText\":\"Very Good\",\"ota\":\"MMT\"},\"rooms\":[{\"name\":\"The Ashok\",\"code\":\"201106120942397721\",\"ratePlans\":[{\"code\":\"1618459331976956240\",\"inclusions\":[{\"category\":\"Free Breakfast\",\"name\":\"Complimentary  Breakfast is available.\"}],\"price\":{\"basePrice\":14999.0,\"displayPrice\":12555.0,\"totalTax\":3375.0,\"savingPerc\":0.0,\"couponCode\":\"MMTBESTBUY\",\"couponDiscount\":944.0,\"hotelDiscount\":0.0,\"applicableCoupons\":[{\"autoApplicable\":false,\"couponCode\":\"MMTBESTBUY\",\"discount\":944.0,\"specialPromoCoupon\":false,\"type\":\"Get INR 944  Off\"}],\"taxBreakUp\":{\"hotelTax\":2430.0,\"hotelServiceCharge\":0.0,\"hcpGst\":0.0,\"serviceFee\":945.0,\"totalTax\":0.0}},\"paymentMode\":\"PAS\",\"cancellationPolicy\":{\"cancelPaneltyDesc\":\"Free Cancellation (100% refund) if you cancel this booking before 2024-10-24 13:59:59 (destination time). Cancellations post that will be subject to a hotel fee as follows:From 2024-10-24 14:00:00 (destination time) till 2024-10-26 13:59:59 (destination time) - 100% of booking amount.After 2024-10-26 14:00:00 (destination time) - 100% of booking amount.Cancellations are only allowed before CheckIn.\",\"penalties\":[{\"startDate\":null,\"endDate\":\"2024-10-24 13:59:59\",\"penaltyValue\":\"FREE_CANCELLATION\",\"penaltyType\":\"F\"}]},\"mealPlans\":[{\"code\":\"CP\",\"value\":\"Breakfast\"}]}]}],\"altAcco\":false}]}],\"lastHotelId\":\"201512161105316614\",\"lastFetchedWindowInfo\":\"000#0#3#false\",\"location\":{\"id\":\"CTDEL\",\"type\":\"znshim\",\"countryId\":\"IN\",\"countryName\":null,\"cityId\":\"ZNSHIM\",\"cityName\":\"\",\"stateId\":null,\"geo\":{\"latitude\":\"\",\"longitude\":\"\"}},\"sortCriteria\":{\"field\":\"S_hsq610_dspers_v2_LC_Per\",\"order\":\"asc\"},\"trackingInfo\":{\"exp\":\"S_hsq610_dspers_v2_LC_Per\"}}";
        ListingResponse listingResponse = mapper.readValue(listingResponseJson, ListingResponse.class);
        SearchHotelsRequest searchHotelsRequest = createSearchHotelsRequest();
        searchHotelsRequest.getRequestDetails().setMetaInfo(true);
        searchHotelsRequest.getFeatureFlags().setPersuasionSuppression(true);
        CommonModifierResponse commonModifierResponse = getCommonModifierResponse();
        SearchHotelsResponse searchHotelsResponse = orchSearchHotelsResponseTransformerDesktop.convertSearchHotelsResponse(listingResponse, searchHotelsRequest, commonModifierResponse);
        Assert.assertNotNull(searchHotelsResponse);


        listingResponseJson = "{\"requestId\":\"bca1d743-ea59-4539-a515-0271a43f5284\",\"journeyId\":\"82909517392a9898b-5da3-4458-b986-2741f54ac819\",\"hotelCount\":1,\"personalizedSections\":[{\"name\":\"NOT_MYBIZ_ASSURED_SHOWN\",\"hotelCardType\":\"default\",\"orientation\":\"V\",\"hotelCount\":1,\"cardInsertionAllowed\":false,\"hotels\":[{\"id\":\"201211081220304096\",\"name\":\"Park Plaza, Shahdara\",\"propertyType\":\"Hotel\",\"propertyLabel\":\"Hotel\",\"detailDeeplinkUrl\":\"https://www.makemytrip.com/hotels/hotel-details?hotelId=201211081220304096&checkin=date_3&checkout=date_4&country=IN&city=CTDEL&roomStayQualifier=2e0e&openDetail=true&currency=INR&region=in&checkAvailability=true&locusId=CTDEL&locusType=city\",\"seoUrl\":\"https://www.makemytrip.com/hotels/park_plaza_shahdara-details-delhi.html\",\"stayType\":\"Entire\",\"starRating\":5,\"totalRoomCount\":1,\"soldOut\":false,\"showCallToBook\":false,\"budgetHotel\":false,\"groupBookingHotel\":false,\"groupBookingPrice\":false,\"categories\":[\"Chain\",\"HighFiveV2_Chain\",\"MMTFest\",\"Couple Friendly\",\"Premium PropertiesFlyer Deal\",\"Premium\",\"Workation\",\"MyBiz_Assured\",\"Daily Dhamaka\"],\"facilityHighlights\":[\"Spa\",\"Swimming Pool\",\"Gym\",\"Restaurant\",\"Steam and Sauna\"],\"location\":{\"id\":\"RGNCR\",\"type\":\"region\",\"countryId\":\"IN\",\"countryName\":\"India\",\"cityId\":\"CTDEL\",\"cityName\":\"Delhi\",\"stateId\":null,\"geo\":{\"latitude\":\"28.658848\",\"longitude\":\"77.297844\"}},\"media\":{\"images\":[{\"url\":\"http://r1imghtlak.mmtcdn.com/82090bda780511e7bf27025f77df004f.jpg?output-quality=75&downsize=243:162&output-format=webp\"},{\"url\":\"http://r1imghtlak.mmtcdn.com/84a383ca780511e78bf8025f77df004f.jpg?output-quality=75&downsize=243:162&output-format=webp\"}]},\"omnitureFlags\":{\"rtb\":false,\"abso\":false,\"abo\":false,\"wishlisted\":false},\"reviewDetails\":{\"rating\":3.8,\"totalReviewCount\":2083,\"totalRatingCount\":4118,\"subRatings\":[{\"name\":\"Location\",\"rating\":3.8,\"reviewCount\":1385,\"show\":false},{\"name\":\"Hospitality\",\"rating\":3.9,\"reviewCount\":1017,\"show\":true},{\"name\":\"Facilities\",\"rating\":3.9,\"reviewCount\":1045,\"show\":true},{\"name\":\"Food\",\"rating\":3.7,\"reviewCount\":836,\"show\":true},{\"name\":\"Room\",\"rating\":4.0,\"reviewCount\":837,\"show\":true},{\"name\":\"Cleanliness\",\"rating\":4.1,\"reviewCount\":2290,\"show\":true},{\"name\":\"Value For Money\",\"rating\":3.9,\"reviewCount\":778,\"show\":true},{\"name\":\"Child Friendliness\",\"rating\":3.5,\"reviewCount\":46,\"show\":true}],\"ratingText\":\"Very Good\",\"ota\":\"MMT\"},\"rooms\":[{\"name\":\"Park Plaza, Shahdara\",\"code\":\"201211081220304096\",\"ratePlans\":[{\"code\":\"3028728164758747328\",\"price\":{\"basePrice\":5000.0,\"displayPrice\":4500.0,\"totalTax\":540.0,\"savingPerc\":0.0,\"couponCode\":\"DHCASHBACK\",\"couponDiscount\":225.0,\"hotelDiscount\":0.0,\"applicableCoupons\":[{\"autoApplicable\":false,\"couponCode\":\"DHCASHBACK\",\"description\":\"Get  INR 225 Cashback to Card on payments via credit/debit cards\",\"discount\":225.0,\"specialPromoCoupon\":true,\"type\":\"Cashback to Card\"}],\"taxBreakUp\":{\"hotelTax\":540.0,\"hotelServiceCharge\":0.0,\"hcpGst\":0.0,\"serviceFee\":0.0,\"totalTax\":0.0}},\"paymentMode\":\"PAS\",\"cancellationPolicy\":{\"cancelPaneltyDesc\":\"This is a Non-refundable and non-amendable tariff. Cancellations, or no-shows will be subject to a hotel fee equal to the 100% of booking amount.Cancellations are only allowed before CheckIn.\",\"penalties\":[{\"startDate\":null,\"endDate\":null,\"penaltyValue\":\"NON_REFUNDABLE\",\"penaltyType\":null}]},\"mealPlans\":[{\"code\":\"EP\",\"value\":\"Room Only\"}]}]}],\"calendarCriteria\":{},\"soldOutInfo\":{},\"altAcco\":true}]}],\"lastHotelId\":\"201512151820116224\",\"lastFetchedWindowInfo\":\"000#0#3#false\",\"location\":{\"id\":\"RGNCR\",\"type\":\"region\",\"countryId\":\"IN\",\"countryName\":\"India\",\"cityId\":\"CTDEL\",\"cityName\":\"Delhi\",\"stateId\":null,\"geo\":{\"latitude\":\"28.658848\",\"longitude\":\"77.297844\"}},\"sortCriteria\":{\"field\":\"S_MM_DHS_DEFAULT_v.1_rec_LC_Per\",\"order\":\"asc\"},\"trackingInfo\":{\"exp\":\"S_MM_DHS_DEFAULT_v.1_rec_LC_Per\"}}";
        listingResponse = mapper.readValue(listingResponseJson, ListingResponse.class);
        searchHotelsRequest.getRequestDetails().setFunnelSource("GROUP");
        searchHotelsResponse = orchSearchHotelsResponseTransformerDesktop.convertSearchHotelsResponse(listingResponse, searchHotelsRequest, commonModifierResponse);
        Assert.assertNotNull(searchHotelsResponse);

        listingResponse.setLastFetchedHotelCategory(SectionsType.FILTER_REMOVAL.name());
        searchHotelsRequest.setFilterCriteria(new ArrayList<>());
        Filter filter = new Filter();
        filter.setFilterValue("5");
        filter.setFilterGroup(FilterGroup.STAR_RATING);
        searchHotelsRequest.getFilterCriteria().add(filter);
        searchHotelsRequest.getRequestDetails().setFunnelSource(SHORTSTAYS_FUNNEL);
        searchHotelsResponse = orchSearchHotelsResponseTransformerDesktop.convertSearchHotelsResponse(listingResponse, searchHotelsRequest, commonModifierResponse);
        Assert.assertNotNull(searchHotelsResponse);
        Assert.assertFalse(searchHotelsResponse.getFilterRemovedCriteria().isEmpty());
    }


    @Test
    public  void addPersuasionHoverDataTest() throws JsonParseException, JSONException {
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "mySafetyToolTipTranslated", new HashMap<>());
        Map<String, Object> desktopToolTipPersuasionsMap = new HashMap<>();
        //desktopToolTipPersuasionsMap.put(TOOL_TIP_VILLA_STAY, "");
        HotelDetails hotelEntity = new HotelDetails();
        CancellationTimeline cancellationTimeline = new CancellationTimeline();
        ListingSearchRequest listingSearchRequest = new ListingSearchRequest();
        listingSearchRequest.setSearchCriteria(new SearchHotelsCriteria());
        listingSearchRequest.getSearchCriteria().setCountryCode("IN");
        listingSearchRequest.setRequestDetails(new RequestDetails());
        listingSearchRequest.getRequestDetails().setPageContext("LISTING");
        JSONObject hoverData = new JSONObject("{\"tooltipType\":\"TOOL_TIP_LUXE\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity, cancellationTimeline, listingSearchRequest);

        hoverData = new JSONObject("{\"tooltipType\":\"BNPL_AVAIL\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity, cancellationTimeline, listingSearchRequest);

        hoverData = new JSONObject("{\"tooltipType\":\"FCZPN\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity, cancellationTimeline, listingSearchRequest);

        hoverData = new JSONObject("{\"tooltipType\":\"FREE_CANCELLATION\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity, cancellationTimeline, listingSearchRequest);

        hoverData = new JSONObject("{\"tooltipType\":\"SAFETY\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity, cancellationTimeline, listingSearchRequest);

        hoverData = new JSONObject("{\"tooltipType\":\"VILLA\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity, cancellationTimeline, listingSearchRequest);

        hoverData = new JSONObject("{\"tooltipType\":\"APARTMENT\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity, cancellationTimeline, listingSearchRequest);

        hoverData = new JSONObject("{\"tooltipType\":\"HOSTEL\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity, cancellationTimeline, listingSearchRequest);

        hoverData = new JSONObject("{\"tooltipType\":\"HOSTEL\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity, cancellationTimeline, listingSearchRequest);

        hoverData = new JSONObject("{\"tooltipType\":\"COTTAGE\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity, cancellationTimeline, listingSearchRequest);

        hoverData = new JSONObject("{\"tooltipType\":\"HOMESTAY\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity, cancellationTimeline, listingSearchRequest);

        hoverData = new JSONObject("{\"tooltipType\":\"TOOL_TIP_MMT_VALUE_STAY\"}");
        hotelEntity.setBudgetHotel(true);
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity, cancellationTimeline, listingSearchRequest);

        hoverData = new JSONObject("{\"tooltipType\":\"TOOL_TIP_MYBIZ_ASSURED\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity, cancellationTimeline, listingSearchRequest);

        hoverData = new JSONObject("{\"tooltipType\":\"TOOL_TIP_LUXE\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity, cancellationTimeline, listingSearchRequest);

        hoverData = new JSONObject("{\"tooltipType\":\"TOOL_TIP_INDIANNESS\"}");
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "addToolTip", hoverData, hotelEntity, cancellationTimeline, listingSearchRequest);
    }


    @Test
    public void addLocationPersuasionToHotelPersuasionsTest() throws JsonProcessingException {
        Hotel hotelEntity = new Hotel();
        List<String> locationPersuasions = Collections.singletonList("Chanakyapuri");
        LinkedHashSet<String> facilities = new LinkedHashSet<>(Arrays.asList("Spa", "Restaurant", "Bar"));
        TransportPoi nearestGroundTransportPoi = new TransportPoi();
        LocationDetails locusData = new LocationDetails();
        locusData.setCityName("Delhi");
        SearchHotelsRequest searchHotelsRequest = createSearchHotelsRequest();
        searchHotelsRequest.getRequestDetails().setPageContext("LISTING");
        String dayUsePersuasionsText = "Day Use";
        String drivingTimeText = "10.0 km drive to T1 - Delhi Airport (IGI Airport)";
        orchSearchHotelsResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, null, true);

        assertTrue(hotelEntity.getHotelPersuasions() instanceof HashMap);
        //Assert.assertEquals(((HashMap) hotelEntity.getHotelPersuasions()).size(), 2);

        locationPersuasions = Arrays.asList("Chanakyapuri", "10.0 km drive to T1 - Delhi Airport (IGI Airport)", "Delhi");
        hotelEntity.setHotelPersuasions(null);
        searchHotelsRequest.getRequestDetails().setFunnelSource("DAYUSE");
        orchSearchHotelsResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, locusData, true);
        assertTrue(hotelEntity.getHotelPersuasions() instanceof HashMap);
        //Assert.assertEquals(((HashMap) hotelEntity.getHotelPersuasions()).size(), 3);

        locationPersuasions = Arrays.asList("Chanakyapuri", "10.0 km drive to T1 - Delhi Airport (IGI Airport)", "Delhi", "Test");
        hotelEntity.setHotelPersuasions(null);
        orchSearchHotelsResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, locusData, true);

        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");
        hotelEntity.setHotelPersuasions(null);
        orchSearchHotelsResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, locusData, true);
        MDC.clear();
    }

    @Test
    public void getGroundTransportPersuasionDataTest() {
        TransportPoi nearestGroundTransportPoi = new TransportPoi();
        PersuasionData persuasionData = ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "getGroundTransportPersuasionData", nearestGroundTransportPoi, 1);
        Assert.assertNotNull(persuasionData);
    }

    @Test
    public void buildBGColorTest() {
        orchSearchHotelsResponseTransformerDesktop.buildBGColor(null, null, null);
    }

    @Test
    public void addBookingConfirmationPersuasionTest() {
        orchSearchHotelsResponseTransformerDesktop.addBookingConfirmationPersuasion(null);
    }

    @Test
    public void buildStaticCardTest() {
        orchSearchHotelsResponseTransformerDesktop.buildStaticCard(null, null, "test");
    }

    @Test
    public void convertListingMapResponseTest() throws Exception {
        // Prepare minimal ListingResponse
        ListingResponse listingResponse = new ListingResponse();
        listingResponse.setHotelCount(1);
        LocationDetails locationDetails = new LocationDetails();
        locationDetails.setId("CTDEL");
        locationDetails.setCityName("Delhi");
        locationDetails.setType("city");
        locationDetails.setCountryId("IN");
        locationDetails.setCountryName("India");
        listingResponse.setLocation(locationDetails);

        // Add a personalized section with a hotel
        PersonalizedSectionDetails section = new PersonalizedSectionDetails();
        section.setName("TestSection");
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setId("H1");
        section.setHotels(Collections.singletonList(hotelDetails));
        listingResponse.setPersonalizedSections(Collections.singletonList(section));

        // Prepare SearchHotelsRequest and CommonModifierResponse
        SearchHotelsRequest searchHotelsRequest = createSearchHotelsRequest();
        CommonModifierResponse commonModifierResponse = getCommonModifierResponse();

        // Call the method under test
        ListingMapResponse response = orchSearchHotelsResponseTransformerDesktop.convertListingMapResponse(listingResponse, searchHotelsRequest, commonModifierResponse);
        Assert.assertNotNull(response);
        org.junit.Assert.assertNotNull(response.getLocationDetail());
        assertEquals("CTDEL", response.getLocationDetail().getId());
        org.junit.Assert.assertNotNull(response.getHotels());
    }

    private CommonModifierResponse getCommonModifierResponse() {
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExpDataMap(new LinkedHashMap<>());
        commonModifierResponse.setHydraResponse(new HydraResponse());
        commonModifierResponse.getHydraResponse().setHydraMatchedSegment(new HashSet<>(Arrays.asList("r1", "r2", "r3")));
        return commonModifierResponse;
    }

    private SearchHotelsRequest createSearchHotelsRequest() throws JsonProcessingException {


        String requestString = "{\"correlationKey\":null,\"brand\":null,\"client\":null,\"blackInfo\":null,\"deviceDetails\":{\"appVersion\":\"128.0.0.0\",\"deviceId\":\"d7bb97cf-762b-4484-91fc-a224c03cdc96\",\"deviceType\":\"DESKTOP\",\"bookingDevice\":\"DESKTOP\",\"networkType\":\"WiFi\",\"deviceName\":null,\"appVersionIntGi\":null,\"simSerialNo\":null},\"lastProductId\":null,\"limit\":null,\"requestDetails\":{\"visitorId\":\"d23c479b373ee283\",\"visitNumber\":1,\"trafficSource\":null,\"srCon\":null,\"srCty\":null,\"srcState\":null,\"srLat\":null,\"srLng\":null,\"funnelSource\":\"HOTELS\",\"idContext\":\"B2C\",\"notifCoupon\":null,\"callBackType\":null,\"pushDataToCallToBookQ\":null,\"pushDataToListAllPropQ\":null,\"payMode\":null,\"loggedIn\":true,\"couponCount\":10,\"siteDomain\":\"in\",\"channel\":\"B2Cweb\",\"pageContext\":\"LISTING\",\"firstTimeUserState\":0,\"uuid\":null,\"corpAuthCode\":null,\"corpUserId\":null,\"seoCorp\":false,\"requestor\":null,\"wishCode\":null,\"preApprovedValidity\":null,\"metaInfo\":false,\"zcp\":null,\"requisitionID\":null,\"myBizFlowIdentifier\":null,\"brand\":\"MMT\",\"previousTxnKey\":null,\"oldWorkflowId\":null,\"journeyId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"requestId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"sessionId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"promoConsent\":false,\"flyerInfo\":null,\"premium\":false,\"semanticSearchDetails\":null,\"forwardBookingFlow\":false,\"extendedPackageCall\":false,\"isIgnoreSEO\":false,\"isRequestCallBack\":false,\"isListAllPropCall\":false},\"detailDeepLinkUrl\":null,\"sortCriteria\":null,\"filterCriteria\":[],\"appliedBatchKeys\":[],\"filterGroupsToRemove\":null,\"filtersToRemove\":null,\"featureFlags\":{\"staticData\":true,\"reviewSummaryRequired\":true,\"walletRequired\":true,\"shortlistingRequired\":false,\"noOfCoupons\":0,\"noOfAddons\":0,\"noOfPersuasions\":0,\"noOfSoldouts\":0,\"coupon\":true,\"mmtPrime\":false,\"persuationSeg\":null,\"persuasionsEngineHit\":true,\"checkAvailability\":true,\"liteResponse\":false,\"applyAbsorption\":false,\"bestOffersLimit\":0,\"dealOfTheDayRequired\":false,\"addOnRequired\":false,\"roomInfoRequired\":false,\"allInclusions\":false,\"hotelCatAndPropNotRequiredInMeta\":false,\"extraAltAccoRequired\":false,\"limitedFilterCall\":false,\"corpMMRRequired\":false,\"unmodifiedAmenities\":false,\"poisRequiredOnMap\":true,\"persuasionsRequired\":true,\"similarHotel\":false,\"locus\":false,\"comparator\":false,\"originListingMap\":false,\"mostBooked\":false,\"detailMap\":false,\"showUpsell\":false,\"filterRanking\":false,\"quickReview\":false,\"dayUsePersuasion\":false,\"selectiveHotels\":false,\"persuasionSuppression\":false,\"hidePrice\":false,\"showBnplCard\":false,\"modifyBooking\":false,\"cardRequired\":false,\"topCard\":false,\"filters\":false,\"seoDS\":false,\"seoCohort\":null,\"roomPreferenceEnabled\":false,\"flashDealClaimed\":false,\"upsellRateplanRequired\":false,\"orientation\":null,\"elitePackageEnabled\":false,\"premiumThemesCardRequired\":false,\"isGoTribe3_0\":null},\"matchMakerDetails\":{},\"imageDetails\":{\"types\":[\"professional\"],\"categories\":[{\"type\":\"H\",\"count\":1,\"height\":162,\"width\":243,\"imageFormat\":\"webp\"}]},\"reviewDetails\":{\"otas\":[\"MMT\",\"TA\"],\"tagTypes\":[\"BASE\",\"WHAT_GUESTS_SAY\"]},\"expData\":\"{\\\"EMIDT\\\":2,\\\"UGCV2\\\":\\\"T\\\",\\\"HFC\\\":\\\"F\\\",\\\"VIDEO\\\":0,\\\"APT\\\":\\\"T\\\",\\\"CHPC\\\":\\\"T\\\",\\\"LSTNRBY\\\":\\\"T\\\",\\\"AARI\\\":\\\"T\\\",\\\"RCPN\\\":\\\"T\\\",\\\"MRS\\\":\\\"T\\\",\\\"ADDON\\\":\\\"T\\\",\\\"NLP\\\":\\\"Y\\\",\\\"PERNEW\\\":\\\"T\\\",\\\"GRPN\\\":\\\"T\\\",\\\"BNPL\\\":\\\"T\\\",\\\"MCUR\\\":\\\"T\\\",\\\"HAFC\\\":\\\"T\\\",\\\"PLRS\\\":\\\"T\\\",\\\"MMRVER\\\":\\\"V3\\\",\\\"PDO\\\":\\\"PN\\\",\\\"BLACK\\\":\\\"T\\\",\\\"CV2\\\":\\\"T\\\",\\\"FLTRPRCBKT\\\":\\\"T\\\",\\\"RTBC\\\":\\\"T\\\",\\\"MLOS\\\":\\\"T\\\",\\\"WPAH\\\":\\\"F\\\",\\\"AIP\\\":\\\"T\\\",\\\"BNPL0\\\":\\\"T\\\",\\\"HIS\\\":\\\"DEFAULT\\\",\\\"APE\\\":10,\\\"PAH\\\":5,\\\"IAO\\\":\\\"F\\\",\\\"CRF\\\":\\\"B\\\",\\\"ALC\\\":\\\"T\\\",\\\"SOU\\\":\\\"T\\\",\\\"PAH5\\\":\\\"T\\\",\\\"rearch\\\":\\\"True\\\"}\",\"expVariantKeys\":null,\"cohertVar\":null,\"multiCityFilter\":null,\"additionalProperties\":null,\"cardId\":null,\"manthanExpDataMap\":null,\"expDataMap\":null,\"contentExpDataMap\":null,\"userLocation\":null,\"clusterId\":null,\"orgId\":null,\"validExpList\":null,\"variantKeys\":null,\"businessIdentificationEnableFromUserService\":false,\"selectedTabId\":null,\"searchCriteria\":{\"checkIn\":\"2024-10-26\",\"checkOut\":\"2024-10-27\",\"countryCode\":\"IN\",\"cityCode\":\"ZNSHIM\",\"cityName\":null,\"locationId\":\"CTDEL\",\"locationType\":\"znshim\",\"lat\":null,\"lng\":null,\"currency\":\"INR\",\"personalCorpBooking\":false,\"rmDHS\":false,\"boostProperty\":null,\"baseRateplanCode\":null,\"selectedRatePlan\":null,\"multiCurrencyInfo\":null,\"preAppliedFilter\":false,\"roomStayCandidates\":[{\"rooms\":1,\"adultCount\":2,\"childAges\":[]}],\"parentLocationId\":null,\"parentLocationType\":null,\"tripType\":null,\"slot\":null,\"giHotelId\":null,\"hotelIds\":null,\"limit\":1,\"lastHotelId\":null,\"lastFetchedWindowInfo\":null,\"lastHotelCategory\":null,\"personalizedSearch\":true,\"nearBySearch\":false,\"wishListedSearch\":false,\"totalHotelsShown\":null,\"sectionsType\":null,\"collectionCriteria\":null,\"bookingForGuest\":false,\"travellerEmailID\":null,\"vcId\":null},\"lastPeekedOnMapHotelIds\":null,\"mapDetails\":null,\"nearbyFilter\":null,\"filterRemovedCriteria\":null}";
        return mapper.readValue(requestString, SearchHotelsRequest.class);
    }

    @Test
    public void testUpdateTopLevelHover_NullTopLevelHoverData() {
        // Test when topLevelHoverData is null
        orchSearchHotelsResponseTransformerDesktop.updateTopLevelHover(null, new MpFareHoldStatus());
        // No exception should be thrown
    }

    @Test
    public void testUpdateTopLevelHover_NullMpFareHoldStatus() {
        // Test when mpFareHoldStatus is null
        JSONObject topLevelHoverData = new JSONObject();
        orchSearchHotelsResponseTransformerDesktop.updateTopLevelHover(topLevelHoverData, null);
        // No exception should be thrown and no changes should be made
//        assertTrue(topLevelHoverData.isNull());
    }

    @Test
    public void testUpdateTopLevelHover_NonMatchingTooltipType() throws JSONException {
        // Test when tooltip type doesn't match MP_FARE_HOLD
        JSONObject topLevelHoverData = new JSONObject();
        topLevelHoverData.put("tooltipType", "SOME_OTHER_TYPE");
        MpFareHoldStatus mpFareHoldStatus = new MpFareHoldStatus();
        
        orchSearchHotelsResponseTransformerDesktop.updateTopLevelHover(topLevelHoverData, mpFareHoldStatus);
        
        // Verify no changes were made except the original tooltip type
        assertEquals(1, topLevelHoverData.length());
        assertEquals("SOME_OTHER_TYPE", topLevelHoverData.getString("tooltipType"));
    }

    @Test
    public void testUpdateTopLevelHover_WithNullExpiry() throws JSONException {
        // Test MP_FARE_HOLD case with null expiry
        JSONObject topLevelHoverData = new JSONObject();
        topLevelHoverData.put("tooltipType", "MP_FARE_HOLD");
        MpFareHoldStatus mpFareHoldStatus = new MpFareHoldStatus();
        mpFareHoldStatus.setExpiry(null);

        when(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_GENERIC_TITLE))
            .thenReturn("Generic Title");
        when(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_SUB_TITLE))
            .thenReturn("Sub Title");

        orchSearchHotelsResponseTransformerDesktop.updateTopLevelHover(topLevelHoverData, mpFareHoldStatus);

        // Verify all data modifications
        Hover hover = (Hover) topLevelHoverData.get("data");
        assertEquals("Generic Title", hover.getTitleText());
        assertEquals("Sub Title", hover.getSubText());
        assertEquals("TITLE_SUBTITLE_TOOLTIP", topLevelHoverData.getString("tooltipType"));
        assertTrue(topLevelHoverData.has("logHover"));

        // Verify the correct method was called for null expiry case
        verify(polyglotService).getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_GENERIC_TITLE);
        verify(polyglotService).getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_SUB_TITLE);
    }

    @Test
    public void testUpdateTopLevelHover_WithValidExpiry() throws JSONException {
        // Test MP_FARE_HOLD case with valid expiry
        JSONObject topLevelHoverData = new JSONObject();
        topLevelHoverData.put("tooltipType", "MP_FARE_HOLD");
        MpFareHoldStatus mpFareHoldStatus = new MpFareHoldStatus();
        mpFareHoldStatus.setExpiry(1234567890L);

        String formattedDate = "01 Jan 2024 12:00 PM";
        String titleTemplate = "Book Now Title {0}";
        String expectedTitle = "Book Now Title " + formattedDate;
        String subTitle = "Sub Title";

        when(dateUtil.convertEpochToDateTime(eq(1234567890L), any()))
            .thenReturn(formattedDate);
        when(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_TITLE))
            .thenReturn(titleTemplate);
        when(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_SUB_TITLE))
            .thenReturn(subTitle);

        orchSearchHotelsResponseTransformerDesktop.updateTopLevelHover(topLevelHoverData, mpFareHoldStatus);

        // Verify all data modifications
        Hover hover = (Hover) topLevelHoverData.get("data");
        assertEquals(expectedTitle, hover.getTitleText());
        assertEquals(subTitle, hover.getSubText());
        assertEquals("TITLE_SUBTITLE_TOOLTIP", topLevelHoverData.getString("tooltipType"));
        assertTrue(topLevelHoverData.has("logHover"));
        
        // Verify all required methods were called
        verify(dateUtil).convertEpochToDateTime(eq(1234567890L), any());
        verify(polyglotService).getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_TITLE);
        verify(polyglotService).getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_SUB_TITLE);
    }

    @Test
    public void testGetPartnerDetails_NullInput() {
        // Test with null input
        com.mmt.hotels.clientgateway.response.PriceDetail result = ReflectionTestUtils.invokeMethod(
            orchSearchHotelsResponseTransformerDesktop,
            "getPartnerDetails",
            new com.mmt.hotels.clientgateway.response.PriceDetail(),
            100.0
        );
        
        // Verify empty price detail is returned
        assertNotNull(result);
        assertNull(result.getPrice());
        assertNull(result.getPriceWithTax());
        assertNull(result.getDiscountedPrice());
        assertNull(result.getDiscountedPriceWithTax());
    }

    @Test
    public void testGetPartnerDetails_PartialNullPrices() {
        // Test with some null price fields
        com.mmt.hotels.clientgateway.response.PriceDetail priceDetail = new com.mmt.hotels.clientgateway.response.PriceDetail();
        priceDetail.setPrice(1000.0);
        priceDetail.setPriceWithTax(1100.0);
        // Leaving discounted prices as null
        
        com.mmt.hotels.clientgateway.response.PriceDetail result = ReflectionTestUtils.invokeMethod(
            orchSearchHotelsResponseTransformerDesktop,
            "getPartnerDetails",
            priceDetail,
            100.0
        );
        
        // Verify empty price detail is returned when any required field is null
        assertNotNull(result);
        assertNull(result.getPrice());
        assertNull(result.getPriceWithTax());
        assertNull(result.getDiscountedPrice());
        assertNull(result.getDiscountedPriceWithTax());
    }

    @Test
    public void testGetPartnerDetails_AllValidPrices() {
        // Test with all valid price fields
        double markup = 100.0;
        com.mmt.hotels.clientgateway.response.PriceDetail priceDetail = new com.mmt.hotels.clientgateway.response.PriceDetail();
        priceDetail.setPrice(1000.0);
        priceDetail.setPriceWithTax(1100.0);
        priceDetail.setDiscountedPrice(900.0);
        priceDetail.setDiscountedPriceWithTax(990.0);
        
        com.mmt.hotels.clientgateway.response.PriceDetail result = ReflectionTestUtils.invokeMethod(
            orchSearchHotelsResponseTransformerDesktop,
            "getPartnerDetails",
            priceDetail,
            markup
        );
        
        // Verify all prices are correctly marked up
        assertNotNull(result);
        assertEquals(1100.0, result.getPrice(), 0.001);  // Original 1000 + 100 markup
        assertEquals(1200.0, result.getPriceWithTax(), 0.001);  // Original 1100 + 100 markup
        assertEquals(1000.0, result.getDiscountedPrice(), 0.001);  // Original 900 + 100 markup
        assertEquals(1090.0, result.getDiscountedPriceWithTax(), 0.001);  // Original 990 + 100 markup
    }

    @Test
    public void testGetPartnerDetails_ZeroMarkup() {
        // Test with zero markup
        com.mmt.hotels.clientgateway.response.PriceDetail priceDetail = new com.mmt.hotels.clientgateway.response.PriceDetail();
        priceDetail.setPrice(1000.0);
        priceDetail.setPriceWithTax(1100.0);
        priceDetail.setDiscountedPrice(900.0);
        priceDetail.setDiscountedPriceWithTax(990.0);
        
        com.mmt.hotels.clientgateway.response.PriceDetail result = ReflectionTestUtils.invokeMethod(
            orchSearchHotelsResponseTransformerDesktop,
            "getPartnerDetails",
            priceDetail,
            0.0
        );
        
        // Verify prices remain unchanged with zero markup
        assertNotNull(result);
        assertEquals(1000.0, result.getPrice(), 0.001);
        assertEquals(1100.0, result.getPriceWithTax(), 0.001);
        assertEquals(900.0, result.getDiscountedPrice(), 0.001);
        assertEquals(990.0, result.getDiscountedPriceWithTax(), 0.001);
    }

    @Test
    public void testGetPartnerDetails_NegativeMarkup() {
        // Test with negative markup (price reduction)
        com.mmt.hotels.clientgateway.response.PriceDetail priceDetail = new com.mmt.hotels.clientgateway.response.PriceDetail();
        priceDetail.setPrice(1000.0);
        priceDetail.setPriceWithTax(1100.0);
        priceDetail.setDiscountedPrice(900.0);
        priceDetail.setDiscountedPriceWithTax(990.0);
        
        com.mmt.hotels.clientgateway.response.PriceDetail result = ReflectionTestUtils.invokeMethod(
            orchSearchHotelsResponseTransformerDesktop,
            "getPartnerDetails",
            priceDetail,
            -100.0
        );
        
        // Verify prices are correctly reduced
        assertNotNull(result);
        assertEquals(900.0, result.getPrice(), 0.001);  // Original 1000 - 100
        assertEquals(1000.0, result.getPriceWithTax(), 0.001);  // Original 1100 - 100
        assertEquals(800.0, result.getDiscountedPrice(), 0.001);  // Original 900 - 100
        assertEquals(890.0, result.getDiscountedPriceWithTax(), 0.001);  // Original 990 - 100
    }

    @Test
    public void testGetMarkUpForHotels_NullInputs() {
        // Test with null inputs
        assertEquals(0.0, orchSearchHotelsResponseTransformerDesktop.getMarkUpForHotels(null, null), 0.001);
        assertEquals(0.0, orchSearchHotelsResponseTransformerDesktop.getMarkUpForHotels(new MarkUpDetails(), null), 0.001);
        assertEquals(0.0, orchSearchHotelsResponseTransformerDesktop.getMarkUpForHotels(null, 1000.0), 0.001);
    }

    @Test
    public void testGetMarkUpForHotels_ZeroOrNegativePrice() {
        MarkUpDetails markUpDetails = new MarkUpDetails();
        markUpDetails.setMarkupEligible(true);
        
        // Test with zero price
        assertEquals(0.0, orchSearchHotelsResponseTransformerDesktop.getMarkUpForHotels(markUpDetails, 0.0), 0.001);
        
        // Test with negative price
        assertEquals(0.0, orchSearchHotelsResponseTransformerDesktop.getMarkUpForHotels(markUpDetails, -100.0), 0.001);
    }

    @Test
    public void testGetMarkUpForHotels_NotEligibleForMarkup() {
        MarkUpDetails markUpDetails = new MarkUpDetails();
        markUpDetails.setMarkupEligible(false);
        
        // Test when markup is not eligible
        assertEquals(0.0, orchSearchHotelsResponseTransformerDesktop.getMarkUpForHotels(markUpDetails, 1000.0), 0.001);
    }

    @Test
    public void testGetMarkUpForHotels_DomesticPercentageMarkup() {
        // Setup domestic context
        MDC.put(MDCHelper.MDCKeys.COUNTRY.getStringValue(), "DOMESTIC");
        
        // Create markup details with percentage markup for domestic hotels
        MarkUpDetails markUpDetails = new MarkUpDetails();
        markUpDetails.setMarkupEligible(true);
        Map<String, MarkUp> markupMap = new HashMap<>();
        
        MarkUp domesticMarkup = new MarkUp();
        domesticMarkup.setType(MarkUpType.PERCENTAGE);
        domesticMarkup.setValue(10.0); // 10% markup
        markupMap.put("DH", domesticMarkup);
        markUpDetails.setMarkupMap(markupMap);
        
        MDC.remove(MDCHelper.MDCKeys.COUNTRY.getStringValue());
    }

    @Test
    public void testGetMarkUpForHotels_InternationalFixedMarkup() {
        // Setup international context
        MDC.put(MDCHelper.MDCKeys.COUNTRY.getStringValue(), "INTERNATIONAL");
        
        // Create markup details with fixed markup for international hotels
        MarkUpDetails markUpDetails = new MarkUpDetails();
        markUpDetails.setMarkupEligible(true);
        Map<String, MarkUp> markupMap = new HashMap<>();
        
        MarkUp internationalMarkup = new MarkUp();
        internationalMarkup.setType(MarkUpType.ABSOLUTE);
        internationalMarkup.setValue(50.0); // Fixed markup of 50
        markupMap.put("IH", internationalMarkup);
        markUpDetails.setMarkupMap(markupMap);
        
        // Test with any price, should always return fixed markup of 50.0
        assertEquals(50.0, orchSearchHotelsResponseTransformerDesktop.getMarkUpForHotels(markUpDetails, 1000.0), 0.001);
        assertEquals(50.0, orchSearchHotelsResponseTransformerDesktop.getMarkUpForHotels(markUpDetails, 2000.0), 0.001);
        
        MDC.remove(MDCHelper.MDCKeys.COUNTRY.getStringValue());
    }

    @Test
    public void testGetMarkUpForHotels_NoMarkupForCountry() {
        // Setup domestic context but only provide international markup
        MDC.put(MDCHelper.MDCKeys.COUNTRY.getStringValue(), "DOMESTIC");
        
        MarkUpDetails markUpDetails = new MarkUpDetails();
        markUpDetails.setMarkupEligible(true);
        Map<String, MarkUp> markupMap = new HashMap<>();

        MarkUp internationalMarkup = new MarkUp();
        internationalMarkup.setType(MarkUpType.ABSOLUTE);
        internationalMarkup.setValue(50.0);
        markupMap.put("IH", internationalMarkup); // Only international markup
        markUpDetails.setMarkupMap(markupMap);
        
        MDC.remove(MDCHelper.MDCKeys.COUNTRY.getStringValue());
    }

    @Test
    public void testGetMarkUpForHotels_NullMarkupMap() {
        MarkUpDetails markUpDetails = new MarkUpDetails();
        markUpDetails.setMarkupEligible(true);
        markUpDetails.setMarkupMap(null);
        
        // Should return 0 as markup map is null
        assertEquals(0.0, orchSearchHotelsResponseTransformerDesktop.getMarkUpForHotels(markUpDetails, 1000.0), 0.001);
    }

    @Test
    public void testGetMarkUpForHotels_EmptyMarkupMap() {
        MarkUpDetails markUpDetails = new MarkUpDetails();
        markUpDetails.setMarkupEligible(true);
        markUpDetails.setMarkupMap(new HashMap<>()); // Empty map
        
        // Should return 0 as markup map is empty
        assertEquals(0.0, orchSearchHotelsResponseTransformerDesktop.getMarkUpForHotels(markUpDetails, 1000.0), 0.001);
    }



    // ================================
    // Tests for buildStaticCard function
    // ================================

    @Test
    public void should_ReturnNull_When_SectionIsNull() {
        // Given
        String section = null;
        List<HotelDetails> hotels = new ArrayList<>();
        String detailDeepLinkUrl = "https://example.com/hotel";

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard result = 
            orchSearchHotelsResponseTransformerDesktop.buildStaticCard(section, hotels, detailDeepLinkUrl);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_SectionIsNotCorpBudgetDirectHotel() {
        // Given
        String section = "REGULAR_HOTELS";
        List<HotelDetails> hotels = new ArrayList<>();
        HotelDetails hotel = new HotelDetails();
        hotel.setBudgetHotel(false);
        hotels.add(hotel);
        String detailDeepLinkUrl = "https://example.com/hotel";

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard result = 
            orchSearchHotelsResponseTransformerDesktop.buildStaticCard(section, hotels, detailDeepLinkUrl);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_HotelsListIsEmpty() {
        // Given
        String section = "DIRECT_HOTEL";
        List<HotelDetails> hotels = new ArrayList<>();
        String detailDeepLinkUrl = "https://example.com/hotel";

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard result = 
            orchSearchHotelsResponseTransformerDesktop.buildStaticCard(section, hotels, detailDeepLinkUrl);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_HotelsListIsNull() {
        // Given
        String section = "DIRECT_HOTEL";
        List<HotelDetails> hotels = null;
        String detailDeepLinkUrl = "https://example.com/hotel";

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard result = 
            orchSearchHotelsResponseTransformerDesktop.buildStaticCard(section, hotels, detailDeepLinkUrl);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_FirstHotelIsBudgetHotel() {
        // Given
        String section = "DIRECT_HOTEL";
        List<HotelDetails> hotels = new ArrayList<>();
        HotelDetails hotel = new HotelDetails();
        hotel.setBudgetHotel(true); // Budget hotel
        hotels.add(hotel);
        String detailDeepLinkUrl = "https://example.com/hotel";

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard result = 
            orchSearchHotelsResponseTransformerDesktop.buildStaticCard(section, hotels, detailDeepLinkUrl);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_MyBizStaticCardIsNull() {
        // Given
        String section = "DIRECT_HOTEL";
        List<HotelDetails> hotels = new ArrayList<>();
        HotelDetails hotel = new HotelDetails();
        hotel.setBudgetHotel(false);
        hotels.add(hotel);
        String detailDeepLinkUrl = "https://example.com/hotel";

        // Mock myBizStaticCard as null
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "myBizStaticCard", null);

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard result = 
            orchSearchHotelsResponseTransformerDesktop.buildStaticCard(section, hotels, detailDeepLinkUrl);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnClonedStaticCard_When_AllConditionsAreMet() {
        // Given
        String section = "DIRECT_HOTEL";
        List<HotelDetails> hotels = new ArrayList<>();
        HotelDetails hotel = new HotelDetails();
        hotel.setBudgetHotel(false);
        hotels.add(hotel);
        String detailDeepLinkUrl = "https://example.com/hotel-details";

        // Create mock MyBizStaticCard
        com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard mockStaticCard = 
            new com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard();
        mockStaticCard.setText("ORIGINAL_TEXT");
        mockStaticCard.setSubtext("ORIGINAL_SUBTEXT");
        mockStaticCard.setCtaText("ORIGINAL_CTA");

        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "myBizStaticCard", mockStaticCard);

        // Mock polyglot service for translation
        when(polyglotService.getTranslatedData("ORIGINAL_TEXT")).thenReturn("Translated Text");
        when(polyglotService.getTranslatedData("ORIGINAL_SUBTEXT")).thenReturn("Translated Subtext");
        when(polyglotService.getTranslatedData("ORIGINAL_CTA")).thenReturn("Translated CTA");

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard result = 
            orchSearchHotelsResponseTransformerDesktop.buildStaticCard(section, hotels, detailDeepLinkUrl);

        // Then
        assertNotNull(result);
        assertEquals(detailDeepLinkUrl, result.getActionUrl());
        assertEquals("Translated Text", result.getText());
        assertEquals("Translated Subtext", result.getSubtext());
        assertEquals("Translated CTA", result.getCtaText());
    }

    @Test
    public void should_HandleCaseInsensitiveSection_When_SectionIsLowerCase() {
        // Given
        String section = "direct_hotel"; // lowercase
        List<HotelDetails> hotels = new ArrayList<>();
        HotelDetails hotel = new HotelDetails();
        hotel.setBudgetHotel(false);
        hotels.add(hotel);
        String detailDeepLinkUrl = "https://example.com/hotel";

        // Create mock MyBizStaticCard
        com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard mockStaticCard = 
            new com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard();
        mockStaticCard.setText("TEST_TEXT");
        mockStaticCard.setSubtext("TEST_SUBTEXT");
        mockStaticCard.setCtaText("TEST_CTA");

        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "myBizStaticCard", mockStaticCard);

        // Mock polyglot service
        when(polyglotService.getTranslatedData("TEST_TEXT")).thenReturn("Translated Text");
        when(polyglotService.getTranslatedData("TEST_SUBTEXT")).thenReturn("Translated Subtext");
        when(polyglotService.getTranslatedData("TEST_CTA")).thenReturn("Translated CTA");

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard result = 
            orchSearchHotelsResponseTransformerDesktop.buildStaticCard(section, hotels, detailDeepLinkUrl);

        // Then
        assertNotNull(result);
        assertEquals(detailDeepLinkUrl, result.getActionUrl());
    }

    @Test
    public void should_HandleNullDeepLinkUrl_When_DeepLinkIsNull() {
        // Given
        String section = "DIRECT_HOTEL";
        List<HotelDetails> hotels = new ArrayList<>();
        HotelDetails hotel = new HotelDetails();
        hotel.setBudgetHotel(false);
        hotels.add(hotel);
        String detailDeepLinkUrl = null;

        // Create mock MyBizStaticCard
        com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard mockStaticCard = 
            new com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard();
        mockStaticCard.setText("TEST_TEXT");
        mockStaticCard.setSubtext("TEST_SUBTEXT");
        mockStaticCard.setCtaText("TEST_CTA");

        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerDesktop, "myBizStaticCard", mockStaticCard);

        // Mock polyglot service
        when(polyglotService.getTranslatedData("TEST_TEXT")).thenReturn("Translated Text");
        when(polyglotService.getTranslatedData("TEST_SUBTEXT")).thenReturn("Translated Subtext");
        when(polyglotService.getTranslatedData("TEST_CTA")).thenReturn("Translated CTA");

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard result = 
            orchSearchHotelsResponseTransformerDesktop.buildStaticCard(section, hotels, detailDeepLinkUrl);

        // Then
        assertNotNull(result);
        assertNull(result.getActionUrl());
    }

    // ================================
    // Tests for translateStaticCard function
    // ================================

    @Test
    public void should_TranslateAllFields_When_StaticCardHasAllFields() throws Exception {
        // Given
        com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard staticCard = 
            new com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard();
        staticCard.setText("ORIGINAL_TEXT_KEY");
        staticCard.setSubtext("ORIGINAL_SUBTEXT_KEY");
        staticCard.setCtaText("ORIGINAL_CTA_KEY");

        // Mock polyglot service
        when(polyglotService.getTranslatedData("ORIGINAL_TEXT_KEY")).thenReturn("Translated Main Text");
        when(polyglotService.getTranslatedData("ORIGINAL_SUBTEXT_KEY")).thenReturn("Translated Sub Text");
        when(polyglotService.getTranslatedData("ORIGINAL_CTA_KEY")).thenReturn("Translated CTA Button");

        // When
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "translateStaticCard", staticCard);

        // Then
        assertEquals("Translated Main Text", staticCard.getText());
        assertEquals("Translated Sub Text", staticCard.getSubtext());
        assertEquals("Translated CTA Button", staticCard.getCtaText());
    }

    @Test
    public void should_HandleNullFields_When_StaticCardHasNullFields() throws Exception {
        // Given
        com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard staticCard = 
            new com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard();
        staticCard.setText(null);
        staticCard.setSubtext(null);
        staticCard.setCtaText(null);

        // Mock polyglot service to return null for null input
        when(polyglotService.getTranslatedData(null)).thenReturn(null);

        // When
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "translateStaticCard", staticCard);

        // Then
        assertNull(staticCard.getText());
        assertNull(staticCard.getSubtext());
        assertNull(staticCard.getCtaText());
    }

    @Test
    public void should_HandleEmptyFields_When_StaticCardHasEmptyFields() throws Exception {
        // Given
        com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard staticCard = 
            new com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard();
        staticCard.setText("");
        staticCard.setSubtext("");
        staticCard.setCtaText("");

        // Mock polyglot service
        when(polyglotService.getTranslatedData("")).thenReturn("Default Translation");

        // When
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "translateStaticCard", staticCard);

        // Then
        assertEquals("Default Translation", staticCard.getText());
        assertEquals("Default Translation", staticCard.getSubtext());
        assertEquals("Default Translation", staticCard.getCtaText());
    }

    @Test
    public void should_HandleSpecialCharacters_When_StaticCardHasSpecialCharacterKeys() throws Exception {
        // Given
        com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard staticCard = 
            new com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard();
        staticCard.setText("TEXT_WITH_SPECIAL_CHARS_@#$");
        staticCard.setSubtext("SUBTEXT_WITH_UNICODE_αβγ");
        staticCard.setCtaText("CTA_WITH_NUMBERS_123");

        // Mock polyglot service
        when(polyglotService.getTranslatedData("TEXT_WITH_SPECIAL_CHARS_@#$")).thenReturn("Special Characters Translated");
        when(polyglotService.getTranslatedData("SUBTEXT_WITH_UNICODE_αβγ")).thenReturn("Unicode Characters Translated");
        when(polyglotService.getTranslatedData("CTA_WITH_NUMBERS_123")).thenReturn("Numbers Translated");

        // When
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerDesktop, "translateStaticCard", staticCard);

        // Then
        assertEquals("Special Characters Translated", staticCard.getText());
        assertEquals("Unicode Characters Translated", staticCard.getSubtext());
        assertEquals("Numbers Translated", staticCard.getCtaText());
    }

    // ================================
    // Tests for buildQuickBookCard function
    // ================================

    @Test
    public void should_BuildCompleteHotelCard_When_QuickBookInfoHasAllFields() {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo quickBookInfo = 
            new com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo();
        quickBookInfo.setTitleWithPrice("Deluxe Room - ₹5000");
        quickBookInfo.setRoomPersuasion("Free WiFi, Breakfast Included");
        quickBookInfo.setRoomPersuasionWithSize("Free WiFi, Breakfast Included - 25 sqm");
        quickBookInfo.setShowQuickBookCard(true);

        // Mock polyglot service for desktop-specific translations
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_TITLE))
            .thenReturn("Quick Book: {TITLE}");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_SUBTITLE))
            .thenReturn("Features: {SUBTITLE}");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_MODAL_SUBTITLE))
            .thenReturn("Room Details: {SUBTITLE}");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_CTA))
            .thenReturn("Book Instantly");

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.HotelCard result = 
            orchSearchHotelsResponseTransformerDesktop.buildQuickBookCard(quickBookInfo);

        // Then
        assertNotNull(result);
        assertEquals("Quick Book: Deluxe Room - ₹5000", result.getHeading());
        assertEquals("Features: Free WiFi, Breakfast Included", result.getSubHeading());
        assertEquals("Room Details: Free WiFi, Breakfast Included - 25 sqm", result.getRoomSubHeading());
        assertTrue(result.getShowCard());
        assertEquals("Book Instantly", result.getCta());
        
        // Verify background gradient (Desktop specific)
        assertNotNull(result.getBgLinearGradient());
        assertEquals("91.03deg", result.getBgLinearGradient().getDirection());
        assertEquals("#2D6F95 1.46%", result.getBgLinearGradient().getStart());
        assertEquals("#192B43 99.9%", result.getBgLinearGradient().getEnd());
        
        // Verify desktop styling classes
        assertNotNull(result.getDesktopStylingClassesObj());
        assertEquals("myBizQuickBook", result.getDesktopStylingClassesObj().getOuterClass());
        assertEquals("quick__book-btn", result.getDesktopStylingClassesObj().getCtaClass1());
        assertEquals("double--arw", result.getDesktopStylingClassesObj().getCtaClass2());
    }

    @Test
    public void should_BuildHotelCardWithNullFields_When_QuickBookInfoHasNullFields() {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo quickBookInfo = 
            new com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo();
        quickBookInfo.setTitleWithPrice(null);
        quickBookInfo.setRoomPersuasion(null);
        quickBookInfo.setRoomPersuasionWithSize(null);
        quickBookInfo.setShowQuickBookCard(false);

        // Mock polyglot service
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_TITLE))
            .thenReturn("Quick Book: {TITLE}");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_SUBTITLE))
            .thenReturn("Features: {SUBTITLE}");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_MODAL_SUBTITLE))
            .thenReturn("Room Details: {SUBTITLE}");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_CTA))
            .thenReturn("Book Instantly");

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.HotelCard result = 
            orchSearchHotelsResponseTransformerDesktop.buildQuickBookCard(quickBookInfo);

    }

    @Test
    public void should_BuildHotelCardWithEmptyFields_When_QuickBookInfoHasEmptyFields() {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo quickBookInfo = 
            new com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo();
        quickBookInfo.setTitleWithPrice("");
        quickBookInfo.setRoomPersuasion("");
        quickBookInfo.setRoomPersuasionWithSize("");
        quickBookInfo.setShowQuickBookCard(true);

        // Mock polyglot service
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_TITLE))
            .thenReturn("Réservation Rapide: {TITLE}"); // French translation
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_SUBTITLE))
            .thenReturn("Caractéristiques: {SUBTITLE}");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_MODAL_SUBTITLE))
            .thenReturn("Détails de la chambre: {SUBTITLE}");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_CTA))
            .thenReturn("Réserver Instantanément");

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.HotelCard result = 
            orchSearchHotelsResponseTransformerDesktop.buildQuickBookCard(quickBookInfo);

        // Then
        assertNotNull(result);
        assertEquals("Réservation Rapide: ", result.getHeading());
        assertEquals("Caractéristiques: ", result.getSubHeading());
        assertEquals("Détails de la chambre: ", result.getRoomSubHeading());
        assertTrue(result.getShowCard());
        assertEquals("Réserver Instantanément", result.getCta());
    }

    @Test
    public void should_HandleLongText_When_QuickBookInfoHasLongStrings() {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo quickBookInfo = 
            new com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo();
        
        String longTitle = "Premium Executive Suite with Ocean View, King Size Bed, Living Area, and Complimentary Airport Transfer - ₹15000 per night";
        String longPersuasion = "Free High-Speed WiFi, Complimentary Breakfast Buffet, Access to Executive Lounge, Swimming Pool, Fitness Center, Spa Services, 24/7 Room Service, Concierge Service, Valet Parking";
        String longPersuasionWithSize = longPersuasion + " - Spacious 45 square meters with panoramic city and ocean views";
        
        quickBookInfo.setTitleWithPrice(longTitle);
        quickBookInfo.setRoomPersuasion(longPersuasion);
        quickBookInfo.setRoomPersuasionWithSize(longPersuasionWithSize);
        quickBookInfo.setShowQuickBookCard(true);

        // Mock polyglot service
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_TITLE))
            .thenReturn("Quick Book: {TITLE}");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_SUBTITLE))
            .thenReturn("Features: {SUBTITLE}");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_MODAL_SUBTITLE))
            .thenReturn("Room Details: {SUBTITLE}");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_CTA))
            .thenReturn("Book Instantly");

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.HotelCard result = 
            orchSearchHotelsResponseTransformerDesktop.buildQuickBookCard(quickBookInfo);

        // Then
        assertNotNull(result);
        assertEquals("Quick Book: " + longTitle, result.getHeading());
        assertEquals("Features: " + longPersuasion, result.getSubHeading());
        assertEquals("Room Details: " + longPersuasionWithSize, result.getRoomSubHeading());
        assertTrue(result.getShowCard());
        assertEquals("Book Instantly", result.getCta());
    }

    @Test
    public void should_HandleSpecialCharacters_When_QuickBookInfoHasSpecialCharacters() {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo quickBookInfo = 
            new com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo();
        quickBookInfo.setTitleWithPrice("Deluxe Room with A/C & WiFi - ₹3,500.50");
        quickBookInfo.setRoomPersuasion("Free WiFi @ 100Mbps, A/C 24x7, TV w/ 200+ channels");
        quickBookInfo.setRoomPersuasionWithSize("Free WiFi @ 100Mbps, A/C 24x7, TV w/ 200+ channels - 30m²");
        quickBookInfo.setShowQuickBookCard(true);

        // Mock polyglot service
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_TITLE))
            .thenReturn("Quick Book: {TITLE}");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_SUBTITLE))
            .thenReturn("Features: {SUBTITLE}");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_MODAL_SUBTITLE))
            .thenReturn("Room Details: {SUBTITLE}");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_CTA))
            .thenReturn("Book Instantly");

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.HotelCard result = 
            orchSearchHotelsResponseTransformerDesktop.buildQuickBookCard(quickBookInfo);

        // Then
        assertNotNull(result);
        assertEquals("Quick Book: Deluxe Room with A/C & WiFi - ₹3,500.50", result.getHeading());
        assertEquals("Features: Free WiFi @ 100Mbps, A/C 24x7, TV w/ 200+ channels", result.getSubHeading());
        assertEquals("Room Details: Free WiFi @ 100Mbps, A/C 24x7, TV w/ 200+ channels - 30m²", result.getRoomSubHeading());
        assertTrue(result.getShowCard());
        assertEquals("Book Instantly", result.getCta());
    }

    @Test
    public void should_AlwaysSetBgLinearGradientAndStyling_When_BuildingQuickBookCard() {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo quickBookInfo = 
            new com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo();
        quickBookInfo.setShowQuickBookCard(false);

        // Mock polyglot service
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_TITLE))
            .thenReturn("Quick Book: {TITLE}");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_SUBTITLE))
            .thenReturn("Features: {SUBTITLE}");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_MODAL_SUBTITLE))
            .thenReturn("Room Details: {SUBTITLE}");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_CTA))
            .thenReturn("Book Instantly");

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.HotelCard result = 
            orchSearchHotelsResponseTransformerDesktop.buildQuickBookCard(quickBookInfo);

        // Then
        assertNotNull(result);
        
        // Verify background gradient is always set
        assertNotNull(result.getBgLinearGradient());
        assertEquals("91.03deg", result.getBgLinearGradient().getDirection());
        assertEquals("#2D6F95 1.46%", result.getBgLinearGradient().getStart());
        assertEquals("#192B43 99.9%", result.getBgLinearGradient().getEnd());
        
        // Verify desktop styling classes are always set
        assertNotNull(result.getDesktopStylingClassesObj());
        assertEquals("myBizQuickBook", result.getDesktopStylingClassesObj().getOuterClass());
        assertEquals("quick__book-btn", result.getDesktopStylingClassesObj().getCtaClass1());
        assertEquals("double--arw", result.getDesktopStylingClassesObj().getCtaClass2());
    }

    @Test
    public void should_HandleNullQuickBookInfo_When_QuickBookInfoIsNull() {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo quickBookInfo = null;

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.HotelCard result = 
            orchSearchHotelsResponseTransformerDesktop.buildQuickBookCard(quickBookInfo);

        // Then
        assertNull(result);
    }

    @Test
    public void should_HandleStringReplacement_When_TemplateStringsContainPlaceholders() {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo quickBookInfo = 
            new com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo();
        quickBookInfo.setTitleWithPrice("Superior Room");
        quickBookInfo.setRoomPersuasion("Free WiFi");
        quickBookInfo.setRoomPersuasionWithSize("Free WiFi - 20 sqm");
        quickBookInfo.setShowQuickBookCard(true);

        // Mock polyglot service with complex template strings
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_TITLE))
            .thenReturn("Book Now: {TITLE} - Special Offer");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_SUBTITLE))
            .thenReturn("Includes: {SUBTITLE} + More");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_MODAL_SUBTITLE))
            .thenReturn("Full Details: {SUBTITLE} with extras");
        when(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_CTA))
            .thenReturn("Reserve Now");

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.HotelCard result = 
            orchSearchHotelsResponseTransformerDesktop.buildQuickBookCard(quickBookInfo);

        // Then
        assertNotNull(result);
        assertEquals("Book Now: Superior Room - Special Offer", result.getHeading());
        assertEquals("Includes: Free WiFi + More", result.getSubHeading());
        assertEquals("Full Details: Free WiFi - 20 sqm with extras", result.getRoomSubHeading());
        assertTrue(result.getShowCard());
        assertEquals("Reserve Now", result.getCta());
    }

    // ==================== BUILD PAX DETAILS TESTS ====================

    @Test
    public void testBuildPaxDetails_NullInput_ReturnsEmptyList() throws Exception {
        // Given
        List<com.gommt.hotels.orchestrator.model.response.listing.PaxDetails> nullInput = null;

        // When
        List<com.mmt.hotels.model.response.corporate.ItineraryPaxDetails> result = invokeBuildPaxDetails(nullInput);

        // Then
        assertNotNull("Result should not be null", result);
        assertTrue("Result should be empty list", result.isEmpty());
    }

    @Test
    public void testBuildPaxDetails_EmptyInput_ReturnsEmptyList() throws Exception {
        // Given
        List<com.gommt.hotels.orchestrator.model.response.listing.PaxDetails> emptyInput = new ArrayList<>();

        // When
        List<com.mmt.hotels.model.response.corporate.ItineraryPaxDetails> result = invokeBuildPaxDetails(emptyInput);

        // Then
        assertNotNull("Result should not be null", result);
        assertTrue("Result should be empty list", result.isEmpty());
    }

    @Test
    public void testBuildPaxDetails_SinglePaxWithAllFields_MapsCorrectly() throws Exception {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.PaxDetails orchestratorPax = 
            new com.gommt.hotels.orchestrator.model.response.listing.PaxDetails();
        orchestratorPax.setFirstName("John");
        orchestratorPax.setLastName("Doe");
        orchestratorPax.setEmail("<EMAIL>");
        orchestratorPax.setPhoneNumber("9876543210");
        orchestratorPax.setPrimaryPax(true);
        orchestratorPax.setGender("M");

        List<com.gommt.hotels.orchestrator.model.response.listing.PaxDetails> input = 
            Collections.singletonList(orchestratorPax);

        // When
        List<com.mmt.hotels.model.response.corporate.ItineraryPaxDetails> result = invokeBuildPaxDetails(input);

        // Then
        assertNotNull("Result should not be null", result);
        assertEquals("Result should contain one item", 1, result.size());
        
        com.mmt.hotels.model.response.corporate.ItineraryPaxDetails clientGatewayPax = result.get(0);
        assertEquals("FirstName should match", "John", clientGatewayPax.getFirstName());
        assertEquals("LastName should match", "Doe", clientGatewayPax.getLastName());
        assertEquals("Email should match", "<EMAIL>", clientGatewayPax.getEmail());
        assertEquals("PhoneNumber should match", "9876543210", clientGatewayPax.getPhoneNumber());
        assertTrue("PrimaryPax should be true", clientGatewayPax.isPrimaryPax());
        assertEquals("Gender should match", "M", clientGatewayPax.getGender());
        assertNull("IsdCode should be null", clientGatewayPax.getIsdCode());
    }

    @Test
    public void testBuildPaxDetails_SinglePaxWithNullFields_HandlesGracefully() throws Exception {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.PaxDetails orchestratorPax = 
            new com.gommt.hotels.orchestrator.model.response.listing.PaxDetails();
        orchestratorPax.setFirstName(null);
        orchestratorPax.setLastName(null);
        orchestratorPax.setEmail(null);
        orchestratorPax.setPhoneNumber(null);
        orchestratorPax.setPrimaryPax(false);
        orchestratorPax.setGender(null);

        List<com.gommt.hotels.orchestrator.model.response.listing.PaxDetails> input = 
            Collections.singletonList(orchestratorPax);

        // When
        List<com.mmt.hotels.model.response.corporate.ItineraryPaxDetails> result = invokeBuildPaxDetails(input);

        // Then
        assertNotNull("Result should not be null", result);
        assertEquals("Result should contain one item", 1, result.size());
        
        com.mmt.hotels.model.response.corporate.ItineraryPaxDetails clientGatewayPax = result.get(0);
        assertNull("FirstName should be null", clientGatewayPax.getFirstName());
        assertNull("LastName should be null", clientGatewayPax.getLastName());
        assertNull("Email should be null", clientGatewayPax.getEmail());
        assertNull("PhoneNumber should be null", clientGatewayPax.getPhoneNumber());
        assertEquals("PrimaryPax should be false", false, clientGatewayPax.isPrimaryPax());
        assertNull("Gender should be null", clientGatewayPax.getGender());
        assertNull("IsdCode should be null", clientGatewayPax.getIsdCode());
    }

    @Test
    public void testBuildPaxDetails_MultiplePaxDetails_MapsAllCorrectly() throws Exception {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.PaxDetails primaryPax = 
            new com.gommt.hotels.orchestrator.model.response.listing.PaxDetails();
        primaryPax.setFirstName("John");
        primaryPax.setLastName("Doe");
        primaryPax.setEmail("<EMAIL>");
        primaryPax.setPhoneNumber("9876543210");
        primaryPax.setPrimaryPax(true);
        primaryPax.setGender("M");

        com.gommt.hotels.orchestrator.model.response.listing.PaxDetails secondaryPax = 
            new com.gommt.hotels.orchestrator.model.response.listing.PaxDetails();
        secondaryPax.setFirstName("Jane");
        secondaryPax.setLastName("Smith");
        secondaryPax.setEmail("<EMAIL>");
        secondaryPax.setPhoneNumber("9876543211");
        secondaryPax.setPrimaryPax(false);
        secondaryPax.setGender("F");

        List<com.gommt.hotels.orchestrator.model.response.listing.PaxDetails> input = 
            Arrays.asList(primaryPax, secondaryPax);

        // When
        List<com.mmt.hotels.model.response.corporate.ItineraryPaxDetails> result = invokeBuildPaxDetails(input);

        // Then
        assertNotNull("Result should not be null", result);
        assertEquals("Result should contain two items", 2, result.size());
        
        // Verify first pax (primary)
        com.mmt.hotels.model.response.corporate.ItineraryPaxDetails firstPax = result.get(0);
        assertEquals("FirstName should match", "John", firstPax.getFirstName());
        assertEquals("LastName should match", "Doe", firstPax.getLastName());
        assertEquals("Email should match", "<EMAIL>", firstPax.getEmail());
        assertEquals("PhoneNumber should match", "9876543210", firstPax.getPhoneNumber());
        assertTrue("PrimaryPax should be true", firstPax.isPrimaryPax());
        assertEquals("Gender should match", "M", firstPax.getGender());
        assertNull("IsdCode should be null", firstPax.getIsdCode());

        // Verify second pax (secondary)
        com.mmt.hotels.model.response.corporate.ItineraryPaxDetails secondPax = result.get(1);
        assertEquals("FirstName should match", "Jane", secondPax.getFirstName());
        assertEquals("LastName should match", "Smith", secondPax.getLastName());
        assertEquals("Email should match", "<EMAIL>", secondPax.getEmail());
        assertEquals("PhoneNumber should match", "9876543211", secondPax.getPhoneNumber());
        assertEquals("PrimaryPax should be false", false, secondPax.isPrimaryPax());
        assertEquals("Gender should match", "F", secondPax.getGender());
        assertNull("IsdCode should be null", secondPax.getIsdCode());
    }

    @Test
    public void testBuildPaxDetails_PaxWithEmptyStrings_MapsEmptyStrings() throws Exception {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.PaxDetails orchestratorPax = 
            new com.gommt.hotels.orchestrator.model.response.listing.PaxDetails();
        orchestratorPax.setFirstName("");
        orchestratorPax.setLastName("");
        orchestratorPax.setEmail("");
        orchestratorPax.setPhoneNumber("");
        orchestratorPax.setPrimaryPax(true);
        orchestratorPax.setGender("");

        List<com.gommt.hotels.orchestrator.model.response.listing.PaxDetails> input = 
            Collections.singletonList(orchestratorPax);

        // When
        List<com.mmt.hotels.model.response.corporate.ItineraryPaxDetails> result = invokeBuildPaxDetails(input);

        // Then
        assertNotNull("Result should not be null", result);
        assertEquals("Result should contain one item", 1, result.size());
        
        com.mmt.hotels.model.response.corporate.ItineraryPaxDetails clientGatewayPax = result.get(0);
        assertEquals("FirstName should be empty string", "", clientGatewayPax.getFirstName());
        assertEquals("LastName should be empty string", "", clientGatewayPax.getLastName());
        assertEquals("Email should be empty string", "", clientGatewayPax.getEmail());
        assertEquals("PhoneNumber should be empty string", "", clientGatewayPax.getPhoneNumber());
        assertTrue("PrimaryPax should be true", clientGatewayPax.isPrimaryPax());
        assertEquals("Gender should be empty string", "", clientGatewayPax.getGender());
        assertNull("IsdCode should be null", clientGatewayPax.getIsdCode());
    }

    @Test
    public void testBuildPaxDetails_LargePaxList_ProcessesAll() throws Exception {
        // Given
        List<com.gommt.hotels.orchestrator.model.response.listing.PaxDetails> input = new ArrayList<>();
        
        for (int i = 0; i < 5; i++) {
            com.gommt.hotels.orchestrator.model.response.listing.PaxDetails pax = 
                new com.gommt.hotels.orchestrator.model.response.listing.PaxDetails();
            pax.setFirstName("FirstName" + i);
            pax.setLastName("LastName" + i);
            pax.setEmail("email" + i + "@example.com");
            pax.setPhoneNumber("987654321" + i);
            pax.setPrimaryPax(i == 0); // Only first pax is primary
            pax.setGender(i % 2 == 0 ? "M" : "F");
            input.add(pax);
        }

        // When
        List<com.mmt.hotels.model.response.corporate.ItineraryPaxDetails> result = invokeBuildPaxDetails(input);

        // Then
        assertNotNull("Result should not be null", result);
        assertEquals("Result should contain five items", 5, result.size());
        
        for (int i = 0; i < 5; i++) {
            com.mmt.hotels.model.response.corporate.ItineraryPaxDetails pax = result.get(i);
            assertEquals("FirstName should match", "FirstName" + i, pax.getFirstName());
            assertEquals("LastName should match", "LastName" + i, pax.getLastName());
            assertEquals("Email should match", "email" + i + "@example.com", pax.getEmail());
            assertEquals("PhoneNumber should match", "987654321" + i, pax.getPhoneNumber());
            assertEquals("PrimaryPax should match", i == 0, pax.isPrimaryPax());
            assertEquals("Gender should match", i % 2 == 0 ? "M" : "F", pax.getGender());
            assertNull("IsdCode should be null", pax.getIsdCode());
        }
    }

    // Helper method to invoke the private buildPaxDetails method using reflection
    @SuppressWarnings("unchecked")
    private List<com.mmt.hotels.model.response.corporate.ItineraryPaxDetails> invokeBuildPaxDetails(
            List<com.gommt.hotels.orchestrator.model.response.listing.PaxDetails> input) throws Exception {
        
        java.lang.reflect.Method buildPaxDetailsMethod = OrchSearchHotelsResponseTransformer.class
            .getDeclaredMethod("buildPaxDetails", List.class);
        buildPaxDetailsMethod.setAccessible(true);
        
        return (List<com.mmt.hotels.model.response.corporate.ItineraryPaxDetails>) 
            buildPaxDetailsMethod.invoke(orchSearchHotelsResponseTransformerDesktop, input);
    }

}