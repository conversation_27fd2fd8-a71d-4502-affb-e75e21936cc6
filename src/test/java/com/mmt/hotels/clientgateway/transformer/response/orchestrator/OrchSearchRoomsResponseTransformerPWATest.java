package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetailsResponse;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.rooms.LoginPersuasion;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.AddOnHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.CancellationPolicyHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.RoomAmentiesHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.RoomInfoHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.SearchRoomsFilter;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.SearchRoomsMediaHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.SearchRoomsPersuasionHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.SearchRoomsPriceHelper;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.DayUseUtil;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.cache.CacheManager;

import java.io.IOException;
import java.util.Collections;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

@RunWith(MockitoJUnitRunner.class)
public class OrchSearchRoomsResponseTransformerPWATest {
    @InjectMocks
    private OrchSearchRoomsResponseTransformerPWA transformer;

    @Mock
    private CommonResponseTransformer commonResponseTransformer;
    @Mock
    private SearchRoomsPriceHelper searchRoomsPriceHelper;
    @Mock
    private PersuasionUtil persuasionUtil;
    @Mock
    private Utility utility;
    @Mock
    private SearchRoomsFilter searchRoomsFilter;
    @Mock
    private CancellationPolicyHelper cancellationPolicyHelper;
    @Mock
    private SearchRoomsPersuasionHelper searchRoomsPersuasionHelper;
    @Spy
    private DateUtil dateUtil;
    @Mock
    private DayUseUtil dayUseUtil;
    @Mock
    private MetricAspect metricAspect;
    @Mock
    private CommonConfigConsul commonConfigConsul;
    @Mock
    private PolyglotService polyglotService;
    @Mock
    private ObjectMapperUtil objectMapperUtil;
    @Mock
    private CacheManager cacheManager;
    @Mock
    private MobConfigHelper mobConfigHelper;
    @Mock
    private AddOnHelper addOnHelper;
    @Mock
    private RoomInfoHelper roomInfoHelper;
    @Mock
    private RoomAmentiesHelper roomAmentiesHelper;
    @Mock
    private SearchRoomsMediaHelper searchRoomsMediaHelper;

    private SearchRoomsRequest searchRoomsRequest;
    private HotelDetailsResponse hotelDetailsResponseExact;
    private SearchCriteria searchCriteria;
    private RequestDetails requestDetails;
    private CommonModifierResponse commonModifierResponse;
    private ObjectMapper objectMapper;

    @Before
    public void setUp() throws IOException {
        objectMapper = new ObjectMapper();
    }

    @Test
    public void should_ReturnNull_When_CreateTopRatedPersuasion() {
        PersuasionObject result = transformer.createTopRatedPersuasion(true);
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_CreateTopRatedPersuasionWithFalse() {
        PersuasionObject result = transformer.createTopRatedPersuasion(false);
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildLoginPersuasion() {
        LoginPersuasion result = transformer.buildLoginPersuasion();
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildDelayedConfirmationPersuasion() {
        PersuasionResponse result = transformer.buildDelayedConfirmationPersuasion("CORP_ALIAS", true);
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildDelayedConfirmationPersuasionWithFalse() {
        PersuasionResponse result = transformer.buildDelayedConfirmationPersuasion("CORP_ALIAS", false);
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildDelayedConfirmationPersuasionWithNullCorpAlias() {
        PersuasionResponse result = transformer.buildDelayedConfirmationPersuasion(null, true);
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildSpecialFareTagPersuasion() {
        PersuasionResponse result = transformer.buildSpecialFareTagPersuasion("CORP_ALIAS");
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildSpecialFareTagPersuasionWithNullCorpAlias() {
        PersuasionResponse result = transformer.buildSpecialFareTagPersuasion(null);
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildSpecialFareTagWithInfoPersuasion() {
        PersuasionResponse result = transformer.buildSpecialFareTagWithInfoPersuasion("CORP_ALIAS", true);
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildSpecialFareTagWithInfoPersuasionWithFalse() {
        PersuasionResponse result = transformer.buildSpecialFareTagWithInfoPersuasion("CORP_ALIAS", false);
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildConfirmationTextPersuasion() {
        PersuasionResponse result = transformer.buildConfirmationTextPersuasion("CORP_ALIAS", true, false);
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildConfirmationTextPersuasionWithDifferentParams() {
        PersuasionResponse result = transformer.buildConfirmationTextPersuasion("DIFFERENT_ALIAS", false, false);
        assertNull(result);
    }

    @Test
    public void should_ReturnAppsInclusionHtml_When_GetHtml() {
        String html = "DT_INCLUSION_HTML";
        String result = transformer.getHtml();
        assertEquals("Should return the same HTML", html, result);
    }

    // ==================== MAIN CONVERSION METHOD TESTS ====================

    @Test
    public void should_ReturnEmptyResponse_When_HotelDetailsResponseIsNull() {
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
                searchRoomsRequest, null, "expData",
                Collections.emptyList(), searchCriteria, Collections.emptyList(),
                "expVariantKeys", requestDetails, commonModifierResponse);

        assertNotNull("SearchRoomsResponse should not be null", result);
        // Verify basic structure is created even with null input
    }
}
