package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.util.Utility;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import com.mmt.hotels.clientgateway.request.ValidateCouponRequest;
import com.mmt.hotels.model.request.ValidateCouponRequestBody;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;

@RunWith(MockitoJUnitRunner.class)
public class DiscountRequestTransformerTest {

	@InjectMocks
	DiscountRequestTransformer discountRequestTransformer;

	@Spy
	CommonResponseTransformer commonResponseTransformer;

	@Spy
	Utility utility;

	@Before
	public void setup() {
		ReflectionTestUtils.setField(commonResponseTransformer, "intlNrSupplierExclusionList", new ArrayList<>());
		ReflectionTestUtils.setField(commonResponseTransformer, "propertyRulesMaxCount", 4);
		ReflectionTestUtils.setField(commonResponseTransformer, "corpSegments", new HashSet<>(Arrays.asList("1135", "1152")));
	}

	@Test
	public void testConvertValidateCouponRequest(){
		ValidateCouponRequest validateCouponRequest = new ValidateCouponRequest();
		ValidateCouponRequestBody validateCouponRequestBody = discountRequestTransformer.convertValidateCouponRequest(validateCouponRequest);
		Assert.assertNotNull(validateCouponRequestBody);
	}
}
