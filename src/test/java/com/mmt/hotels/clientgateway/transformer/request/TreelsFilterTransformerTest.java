package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.ListingSearchRequestV2;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;

@RunWith(MockitoJUnitRunner.class)
public class TreelsFilterTransformerTest {
    @InjectMocks
    private TreelsFilterTransformer treelsFilterTransformer;

    @Mock
    private MetricAspect metricAspect;

    @Mock
    private Utility utility;


    @Test
    public void convertSearchRequestNullPointerTest() {
        // this test checks that there is no null pointer in the function we are
        // not passing null as the arguments because the function is
        // written in such a way that the arguments are never passed as nulls
        Assert.assertNotNull(treelsFilterTransformer.convertSearchRequest(new ListingSearchRequestV2(), new CommonModifierResponse()));
    }

    @Test
    public void convertSearchRequestTest() {
        ListingSearchRequestV2 listingSearchRequestV2 = new ListingSearchRequestV2();
        listingSearchRequestV2.setFilterRemovedCriteria(new ArrayList<>());
        listingSearchRequestV2.getFilterRemovedCriteria().add(new Filter());
        listingSearchRequestV2.getFilterRemovedCriteria().get(0).setFilterGroup(FilterGroup.TAGS);
        listingSearchRequestV2.getFilterRemovedCriteria().get(0).setFilterValue("test");
        listingSearchRequestV2.setCorrelationKey("test");
        listingSearchRequestV2.setVariantKeys("testVariantKeys");
        listingSearchRequestV2.setRequestDetails(new RequestDetails());
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExtendedUser(new ExtendedUser());
        SearchWrapperInputRequest searchWrapperInputRequest = treelsFilterTransformer.convertSearchRequest(listingSearchRequestV2, commonModifierResponse);
        Assert.assertEquals(searchWrapperInputRequest.isBuildTreelsExploreMoreData(), true);
        Assert.assertEquals(searchWrapperInputRequest.getCorrelationKey(), "test");
        Assert.assertEquals(searchWrapperInputRequest.getVariantKeys(), "testVariantKeys");
    }
}
