package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.BankOffersRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.BankOffersRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.BankOffersRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.BankOffersRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.BankOffersRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.BankOffersResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.BankOffersResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.BankOffersResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.BankOffersResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.BankOffersResponseTransformerPWA;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class BankOffersFactoryTest {
    
    @InjectMocks
    BankOffersFactory bankOffersFactory;

    @Before
    public  void init(){
        ReflectionTestUtils.setField(bankOffersFactory, "bankOffersRequestTransformerPWA", new BankOffersRequestTransformerPWA());
        ReflectionTestUtils.setField(bankOffersFactory, "bankOffersRequestTransformerDesktop", new BankOffersRequestTransformerDesktop());
        ReflectionTestUtils.setField(bankOffersFactory, "bankOffersRequestTransformerIOS", new BankOffersRequestTransformerIOS());
        ReflectionTestUtils.setField(bankOffersFactory, "bankOffersRequestTransformerAndroid", new BankOffersRequestTransformerAndroid());

        ReflectionTestUtils.setField(bankOffersFactory, "bankOffersResponseTransformerPWA", new BankOffersResponseTransformerPWA());
        ReflectionTestUtils.setField(bankOffersFactory, "bankOffersResponseTransformerDesktop", new BankOffersResponseTransformerDesktop());
        ReflectionTestUtils.setField(bankOffersFactory, "bankOffersResponseTransformerIOS", new BankOffersResponseTransformerIOS());
        ReflectionTestUtils.setField(bankOffersFactory, "bankOffersResponseTransformerAndroid", new BankOffersResponseTransformerAndroid());
    }

    @Test
    public void getResponseServiceTest() {
        BankOffersResponseTransformer resp = bankOffersFactory.getResponseService("PWA");
        Assert.assertTrue(resp instanceof BankOffersResponseTransformerPWA);
        resp = bankOffersFactory.getResponseService("DESKTOP");
        Assert.assertTrue(resp instanceof BankOffersResponseTransformerDesktop);
        resp = bankOffersFactory.getResponseService("ANDROID");
        Assert.assertTrue(resp instanceof BankOffersResponseTransformerAndroid);
        resp = bankOffersFactory.getResponseService("IOS");
        Assert.assertTrue(resp instanceof BankOffersResponseTransformerIOS);
        resp = bankOffersFactory.getResponseService("");
        Assert.assertTrue(resp instanceof BankOffersResponseTransformerDesktop);
        resp = bankOffersFactory.getResponseService("test");
        Assert.assertTrue(resp instanceof BankOffersResponseTransformerDesktop);
    }

    @Test
    public void getRequestServiceTest() {
        BankOffersRequestTransformer resp = bankOffersFactory.getRequestService("PWA");
        Assert.assertTrue(resp instanceof BankOffersRequestTransformerPWA);
        resp = bankOffersFactory.getRequestService("DESKTOP");
        Assert.assertTrue(resp instanceof BankOffersRequestTransformerDesktop);
        resp = bankOffersFactory.getRequestService("ANDROID");
        Assert.assertTrue(resp instanceof BankOffersRequestTransformerAndroid);
        resp = bankOffersFactory.getRequestService("IOS");
        Assert.assertTrue(resp instanceof BankOffersRequestTransformerIOS);
        resp = bankOffersFactory.getRequestService("");
        Assert.assertTrue(resp instanceof BankOffersRequestTransformerDesktop);
        resp = bankOffersFactory.getRequestService("test");
        Assert.assertTrue(resp instanceof BankOffersRequestTransformerDesktop);
    }
}
