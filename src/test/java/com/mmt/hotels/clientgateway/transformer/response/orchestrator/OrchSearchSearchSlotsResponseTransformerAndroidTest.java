package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetailsResponse;
import com.gommt.hotels.orchestrator.detail.model.response.content.Media;
import com.gommt.hotels.orchestrator.detail.model.response.da.AvailDetails;
import com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.HotelRateFlags;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.Rooms;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.request.dayuse.DayUseRoomsRequest;
import com.mmt.hotels.clientgateway.response.ResponseContextDetail;
import com.mmt.hotels.clientgateway.response.availrooms.FeatureFlags;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUsePersuasion;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUsePriceDetail;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseRoom;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseRoomCriteria;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseRoomsResponse;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseSlotPlan;
import com.mmt.hotels.clientgateway.response.rooms.RoomHighlight;
import com.mmt.hotels.clientgateway.response.rooms.RoomTariff;
import com.mmt.hotels.clientgateway.service.OrchDetailService;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.RoomAmentiesHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.RoomInfoHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.SearchRoomsMediaHelper;
import com.mmt.hotels.clientgateway.util.DayUseUtil;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.dayuse.MissingSlotDetail;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doNothing;

/**
 * Comprehensive unit tests for OrchSearchSearchSlotsResponseTransformerAndroid
 * Tests the actual business logic to achieve 100% line coverage for OrchSearchSearchSlotsResponseTransformer
 */
@RunWith(MockitoJUnitRunner.class)
public class OrchSearchSearchSlotsResponseTransformerAndroidTest {

    @InjectMocks
    private OrchSearchSearchSlotsResponseTransformerAndroid transformer;

    @Mock
    private Utility utility;
    @Mock
    private PolyglotService polyglotService;
    @Mock
    private MetricAspect metricAspect;
    @Mock
    private CommonConfigConsul commonConfigConsul;
    @Mock
    private RoomInfoHelper roomInfoHelper;
    @Mock
    private RoomAmentiesHelper roomAmentiesHelper;
    @Mock
    private SearchRoomsMediaHelper searchRoomsMediaHelper;
    @Mock
    private OrchDetailService orchDetailService;
    @Mock
    private DayUseUtil dayUseUtil;

    private ObjectMapper objectMapper;
    private DayUseRoomsRequest dayUseRoomsRequest;
    private HotelDetailsResponse hotelDetailsResponse;
    private CommonModifierResponse commonModifierResponse;
    private SearchRoomsCriteria searchRoomsCriteria;

    @Before
    public void setUp() throws Exception {
        objectMapper = new ObjectMapper();
        setupTestData();
        setupMocks();
        setupPrivateFields();
    }

    private void setupTestData() {
        // Setup DayUseRoomsRequest
        dayUseRoomsRequest = new DayUseRoomsRequest();
        
        searchRoomsCriteria = new SearchRoomsCriteria();
        searchRoomsCriteria.setCheckIn("2025-07-01");
        searchRoomsCriteria.setCheckOut("2025-07-02");
        searchRoomsCriteria.setCurrency("INR");
        
        com.mmt.hotels.clientgateway.request.dayuse.Slot slot = new com.mmt.hotels.clientgateway.request.dayuse.Slot();
        slot.setTimeSlot(6);
        slot.setDuration(6);
        searchRoomsCriteria.setSlot(slot);
        
        dayUseRoomsRequest.setSearchCriteria(searchRoomsCriteria);
        dayUseRoomsRequest.setExpVariantKeys("test_variant");
        
        com.mmt.hotels.clientgateway.request.FeatureFlags featureFlags = new com.mmt.hotels.clientgateway.request.FeatureFlags();
        featureFlags.setDayUsePersuasion(true);
        dayUseRoomsRequest.setFeatureFlags(featureFlags);
        
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource("HOTELS");
        dayUseRoomsRequest.setRequestDetails(requestDetails);

        // Setup HotelDetailsResponse
        hotelDetailsResponse = new HotelDetailsResponse();
        HotelDetails hotelDetails = createMockHotelDetails();
        hotelDetailsResponse.setHotelDetails(hotelDetails);

        // Setup CommonModifierResponse
        commonModifierResponse = new CommonModifierResponse();
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put("RTBC", "1");
        expDataMap.put("NEW_DETAIL_PAGE", "1");
        commonModifierResponse.setExpDataMap(expDataMap);
    }

    private HotelDetails createMockHotelDetails() {
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setId("test_hotel_id");
        hotelDetails.setCurrencyCode("INR");
        hotelDetails.setHotelCategory("4_STAR");
        
        // Create hotel rate flags
        HotelRateFlags hotelRateFlags = new HotelRateFlags();
        hotelRateFlags.setBestPriceGuaranteed(true);
        hotelRateFlags.setBnplAvailable(true);
        hotelRateFlags.setFirstTimeUser(false);
        hotelRateFlags.setFreeCancellationAvailable(true);
        hotelRateFlags.setPahAvailable(true);
        hotelRateFlags.setRequestToBook(false);
        hotelRateFlags.setAltAcco(false);
        hotelDetails.setHotelRateFlags(hotelRateFlags);

        // Create rooms with slots
        List<Rooms> rooms = new ArrayList<>();
        Rooms room = createMockRoom();
        rooms.add(room);
        hotelDetails.setRooms(rooms);

        // Create day use persuasions
        List<com.gommt.hotels.orchestrator.detail.model.response.content.DayUsePersuasion> dayUsePersuasions = new ArrayList<>();
        com.gommt.hotels.orchestrator.detail.model.response.content.DayUsePersuasion persuasion = 
            new com.gommt.hotels.orchestrator.detail.model.response.content.DayUsePersuasion();
        persuasion.setId("COUPLE_FRIENDLY");
        persuasion.setText("Couple Friendly");
        persuasion.setIcon("icon_url");
        persuasion.setIconType("b_dot");
        dayUsePersuasions.add(persuasion);
        hotelDetails.setDayUsePersuasions(dayUsePersuasions);

        // Create media - HotelDetails.setMedia expects single Media object, not List
        Media media = new Media();
        hotelDetails.setMedia(media);

        return hotelDetails;
    }

    private Rooms createMockRoom() {
        Rooms room = new Rooms();
        room.setCode("DELUXE");
        room.setName("Deluxe Room");
        room.setType("DAYUSE");

        // Create slot
        com.gommt.hotels.orchestrator.detail.model.response.pricing.Slot slot = 
            new com.gommt.hotels.orchestrator.detail.model.response.pricing.Slot();
        slot.setDuration(6);
        slot.setTimeSlot("06:00");
        room.setSlot(slot);

        // Create rate plans
        List<RatePlan> ratePlans = new ArrayList<>();
        RatePlan ratePlan = createMockRatePlan();
        ratePlans.add(ratePlan);
        room.setRatePlans(ratePlans);

        // Create occupancy details
        OccupancyDetails occupancyDetails = new OccupancyDetails();
        occupancyDetails.setAdult(2);
        occupancyDetails.setChild(0);
        occupancyDetails.setNumberOfRooms(1);
        room.setOccupancyDetails(occupancyDetails);

        return room;
    }

    private RatePlan createMockRatePlan() {
        RatePlan ratePlan = new RatePlan();
        ratePlan.setCode("RATE_001");
        ratePlan.setSegmentId("INGO_SEGMENT");

        // Create price detail
        PriceDetail priceDetail = new PriceDetail();
        priceDetail.setDisplayPrice(5000.0);
        priceDetail.setTotalTax(500.0);
        priceDetail.setCouponCode("DISCOUNT10");
        priceDetail.setPricingKey("pricing_key_123");
        ratePlan.setPrice(priceDetail);

        // Create avail details
        AvailDetails availDetails = new AvailDetails();
        availDetails.setCount(5);
        OccupancyDetails occupancyDetails = new OccupancyDetails();
        occupancyDetails.setAdult(2);
        occupancyDetails.setChild(0);
        occupancyDetails.setNumberOfRooms(1);
        occupancyDetails.setRatePlanCode("RATE_001");
        availDetails.setOccupancyDetails(occupancyDetails);
        ratePlan.setAvailDetail(availDetails);

        return ratePlan;
    }

    private void setupMocks() {
        // Setup MissingSlotDetail mock
        MissingSlotDetail missingSlotDetail = new MissingSlotDetail();
        missingSlotDetail.setDuration(new HashSet<>(Arrays.asList(3, 6, 12)));
        when(commonConfigConsul.getMissingSlotDetails()).thenReturn(missingSlotDetail);

        // Setup DayUseFunnelPersuasions mock
        Map<String, DayUsePersuasion> dayUseFunnelPersuasions = new HashMap<>();
        DayUsePersuasion persuasion = new DayUsePersuasion();
        persuasion.setId("LATE_CHECKOUT");
        persuasion.setText("Late Checkout Available");
        dayUseFunnelPersuasions.put("LATE_CHECKOUT", persuasion);
        when(commonConfigConsul.getDayUseFunnelPersuasions()).thenReturn(dayUseFunnelPersuasions);

        // Setup utility mocks
        when(utility.calculateTimeSlot_Meridiem(any(com.mmt.hotels.model.response.dayuse.Slot.class))).thenReturn("6:00 AM - 12:00 PM");
        when(utility.isExperimentTrue(any(), anyString())).thenReturn(false);
        lenient().doNothing().when(utility).transformLateCheckout(any(), any());

        // Setup polyglot service mocks
        lenient().when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated Text");
        lenient().when(polyglotService.getTranslatedData("DAYUSE_PER_HOUR")).thenReturn("per hour");
        lenient().when(polyglotService.getTranslatedData("DAYUSE_PER_NIGHT")).thenReturn("per night");
        lenient().when(polyglotService.getTranslatedData("DAYUSE_PER_NIGHT_TAX")).thenReturn("per night + tax");
        lenient().when(polyglotService.getTranslatedData("TOTAL_AMOUNT_LABEL")).thenReturn("Total Amount");
        lenient().when(polyglotService.getTranslatedData("HOTEL_TAX_LABEL")).thenReturn("Tax");

        // Setup room info helper mocks
        when(roomInfoHelper.transformRoomHighlights(any(), any(), anyBoolean(), anyBoolean(), any(), anyString(), anyBoolean(), anyBoolean()))
            .thenReturn(Collections.singletonList(new RoomHighlight()));

        // Setup room amenities helper mocks
        when(roomAmentiesHelper.buildAmenities(any(), anyBoolean())).thenReturn(Collections.emptyList());

        // Setup search rooms media helper mocks
        when(searchRoomsMediaHelper.extractRoomImagesFromMedia(any(), anyString())).thenReturn(Collections.singletonList("image_url"));

        // Setup day use util mocks
        when(dayUseUtil.shouldSetPriceDetailsForDayUseOnDetailPage(any(), anyDouble())).thenReturn(true);

        // Setup metric aspect mock (void method)
        doNothing().when(metricAspect).addToTimeInternalProcess(anyString(), anyString(), anyLong());
    }

    private void setupPrivateFields() {
        // Initialize private fields using ReflectionTestUtils
        MissingSlotDetail missingSlotDetail = new MissingSlotDetail();
        missingSlotDetail.setDuration(new HashSet<>(Arrays.asList(3, 6, 12)));
        ReflectionTestUtils.setField(transformer, "missingSlotDetails", missingSlotDetail);

        Map<String, DayUsePersuasion> dayUseFunnelPersuasions = new HashMap<>();
        ReflectionTestUtils.setField(transformer, "dayUseFunnelPersuasions", dayUseFunnelPersuasions);
    }

    // ==================== MAIN BUSINESS LOGIC TESTS ====================

    @Test
    public void should_ConvertSearchSlotsResponse_When_ValidInput() {
        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("DayUseRoomsResponse should not be null", result);
        assertEquals("Currency should match", "INR", result.getCurrency());
        assertEquals("Search type should be R", "R", result.getSearchType());
        assertEquals("Default price key should be DEFAULT", "DEFAULT", result.getDefaultPriceKey());
        assertNotNull("Feature flags should be set", result.getFeatureFlags());
        assertNotNull("Context details should be set", result.getContextDetails());
        
        // Verify method calls
        verify(metricAspect).addToTimeInternalProcess(anyString(), anyString(), anyLong());
    }

    @Test
    public void should_ReturnEmptyResponse_When_HotelDetailsResponseIsNull() {
        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, null, commonModifierResponse);

        // Then
        assertNotNull("Response should not be null", result);
        assertNull("Slot plans should be null", result.getSlotPlans());
        assertNull("Rooms should be null", result.getRooms());
    }

    @Test
    public void should_ReturnEmptyResponse_When_HotelDetailsIsNull() {
        // Given
        hotelDetailsResponse.setHotelDetails(null);

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Response should not be null", result);
        assertNull("Slot plans should be null", result.getSlotPlans());
    }

    @Test
    public void should_ReturnEmptyResponse_When_NoRoomsOrRoomCombos() {
        // Given
        hotelDetailsResponse.getHotelDetails().setRooms(Collections.emptyList());
        hotelDetailsResponse.getHotelDetails().setRoomCombos(Collections.emptyList());

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Response should not be null", result);
        assertNull("Slot plans should be null", result.getSlotPlans());
    }

    @Test
    public void should_HandleNullSearchCriteria_When_ProcessingRequest() {
        // Given
        dayUseRoomsRequest.setSearchCriteria(null);

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Response should not be null", result);
        assertEquals("Should use default currency", Constants.DEFAULT_CUR_INR, result.getCurrency());
    }

    @Test
    public void should_HandleNullSlotInSearchCriteria_When_ProcessingRequest() {
        // Given
        searchRoomsCriteria.setSlot(null);

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Response should not be null", result);
        // Should not call populateMissingSlots
    }

    @Test
    public void should_HandleNullCommonModifierResponse_When_ProcessingRequest() {
        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, null);

        // Then
        assertNotNull("Response should not be null", result);
        assertNotNull("Feature flags should still be set", result.getFeatureFlags());
    }

    @Test
    public void should_ProcessRoomCombos_When_Available() {
        // Given
        List<RoomCombo> roomCombos = new ArrayList<>();
        RoomCombo roomCombo = new RoomCombo();
        List<Rooms> comboRooms = new ArrayList<>();
        comboRooms.add(createMockRoom());
        roomCombo.setRooms(comboRooms);
        roomCombos.add(roomCombo);
        hotelDetailsResponse.getHotelDetails().setRoomCombos(roomCombos);

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Response should not be null", result);
        assertNotNull("Slot plans should be created", result.getSlotPlans());
        assertTrue("Should have slot plans", CollectionUtils.isNotEmpty(result.getSlotPlans()));
    }

    // ==================== DAY USE PERSUASION TESTS ====================

    @Test
    public void should_BuildDayUsePersuasions_When_PersuasionsExist() {
        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Day use persuasions should be built", result.getDayUsePersuasions());
        assertTrue("Should have at least one persuasion", CollectionUtils.isNotEmpty(result.getDayUsePersuasions()));
        assertEquals("Should have correct persuasion ID", "COUPLE_FRIENDLY", result.getDayUsePersuasions().get(0).getId());
    }

    @Test
    public void should_HandleNullDayUsePersuasions_When_NoneExist() {
        // Given
        hotelDetailsResponse.getHotelDetails().setDayUsePersuasions(null);

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNull("Day use persuasions should be null", result.getDayUsePersuasions());
    }

    @Test
    public void should_HandleEmptyDayUsePersuasions_When_EmptyList() {
        // Given
        hotelDetailsResponse.getHotelDetails().setDayUsePersuasions(Collections.emptyList());

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNull("Day use persuasions should be null", result.getDayUsePersuasions());
    }

    // ==================== PRICE DETAIL TESTS ====================

    @Test
    public void should_BuildPriceDetail_When_RatePlansExist() {
        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Price detail should be built", result.getPriceDetail());
        assertTrue("Total price should be greater than 0", result.getPriceDetail().getTotalPrice() > 0);
    }

    @Test
    public void should_HandleNullPriceDetail_When_NoRatePlans() {
        // Given
        hotelDetailsResponse.getHotelDetails().getRooms().get(0).setRatePlans(Collections.emptyList());

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNull("Price detail should be null", result.getPriceDetail());
    }

    @Test
    public void should_NotSetPriceDetail_When_DayUseUtilReturnsFalse() {
        // Given
        when(dayUseUtil.shouldSetPriceDetailsForDayUseOnDetailPage(any(), anyDouble())).thenReturn(false);

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNull("Price detail should not be set", result.getPriceDetail());
    }

    // ==================== SLOT PLAN TESTS ====================

    @Test
    public void should_BuildSlotPlans_When_RoomsHaveSlots() {
        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Slot plans should be built", result.getSlotPlans());
        assertTrue("Should have at least one slot plan", CollectionUtils.isNotEmpty(result.getSlotPlans()));
        
        DayUseSlotPlan slotPlan = result.getSlotPlans().get(0);
        assertNotNull("Slot should be set", slotPlan.getSlot());
        assertEquals("Slot duration should match", Integer.valueOf(6), slotPlan.getSlot().getDuration());
        assertNotNull("Room criteria should be set", slotPlan.getRoomCriteria());
    }

    @Test
    public void should_HandleRoomWithoutSlot_When_ProcessingRooms() {
        // Given
        hotelDetailsResponse.getHotelDetails().getRooms().get(0).setSlot(null);

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Response should not be null", result);
        assertTrue("Slot plans should be empty when no slots", CollectionUtils.isEmpty(result.getSlotPlans()));
    }

    @Test
    public void should_UseRoomPriceWhenAvailable_When_BuildingSlotPlans() {
        // Given
        PriceDetail roomPrice = new PriceDetail();
        roomPrice.setDisplayPrice(6000.0);
        roomPrice.setTotalTax(600.0);
        hotelDetailsResponse.getHotelDetails().getRooms().get(0).setPrice(roomPrice);

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Slot plans should be built", result.getSlotPlans());
        assertFalse("Should have slot plans", result.getSlotPlans().isEmpty());
    }

    @Test
    public void should_FallbackToRatePlanPrice_When_RoomPriceIsZero() {
        // Given
        PriceDetail roomPrice = new PriceDetail();
        roomPrice.setDisplayPrice(0.0);
        hotelDetailsResponse.getHotelDetails().getRooms().get(0).setPrice(roomPrice);

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Slot plans should be built", result.getSlotPlans());
        // Should fallback to rate plan price
    }

    // ==================== MISSING SLOTS TESTS ====================

    @Test
    public void should_PopulateMissingSlots_When_FewerThanThreeSlots() {
        // Given - Only one slot exists, should add missing ones
        // Setup missing slot details with more options
        MissingSlotDetail missingSlotDetail = new MissingSlotDetail();
        missingSlotDetail.setDuration(new HashSet<>(Arrays.asList(3, 12))); // 6 already exists, should add 3 and 12
        ReflectionTestUtils.setField(transformer, "missingSlotDetails", missingSlotDetail);

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Slot plans should be built", result.getSlotPlans());
        assertTrue("Should have multiple slot plans", result.getSlotPlans().size() > 1);
        
        // Verify utility method was called for time calculation
        verify(utility, atLeastOnce()).calculateTimeSlot_Meridiem(any(com.mmt.hotels.model.response.dayuse.Slot.class));
    }

    @Test
    public void should_NotPopulateMissingSlots_When_NoExistingSlots() {
        // Given
        hotelDetailsResponse.getHotelDetails().getRooms().get(0).setSlot(null);

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        // Should not populate missing slots when no existing slots
        assertTrue("Should not add missing slots when no existing slots", 
                   result.getSlotPlans() == null || result.getSlotPlans().isEmpty());
    }

    @Test
    public void should_HandleNullMissingSlotDetails_When_PopulatingSlots() {
        // Given
        ReflectionTestUtils.setField(transformer, "missingSlotDetails", null);

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Response should not be null", result);
        // Should not crash when missing slot details is null
    }

    // ==================== ROOM STAY CANDIDATE TESTS ====================

    @Test
    public void should_BuildRoomStayCandidatesFromPricePerOccupancy_When_Available() {
        // Given
        List<OccupancyDetails> pricePerOccupancy = new ArrayList<>();
        OccupancyDetails occupancy1 = new OccupancyDetails();
        occupancy1.setAdult(2);
        occupancy1.setChild(1);
        occupancy1.setChildAges(Arrays.asList(5));
        pricePerOccupancy.add(occupancy1);
        
        OccupancyDetails occupancy2 = new OccupancyDetails();
        occupancy2.setAdult(4);
        occupancy2.setChild(0);
        pricePerOccupancy.add(occupancy2);
        
        hotelDetailsResponse.getHotelDetails().getRooms().get(0).getRatePlans().get(0).getPrice().setPricePerOccupancy(pricePerOccupancy);

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Slot plans should be built", result.getSlotPlans());
        assertFalse("Should have slot plans", result.getSlotPlans().isEmpty());
        
        DayUseSlotPlan slotPlan = result.getSlotPlans().get(0);
        assertNotNull("Room criteria should be set", slotPlan.getRoomCriteria());
        assertTrue("Should have multiple room stay candidates", slotPlan.getRoomCriteria().get(0).getRoomStayCandidates().size() > 1);
    }

    @Test
    public void should_BuildRoomStayCandidatesFromAvailDetail_When_NoPricePerOccupancy() {
        // Given - Clear pricePerOccupancy to test fallback
        hotelDetailsResponse.getHotelDetails().getRooms().get(0).getRatePlans().get(0).getPrice().setPricePerOccupancy(null);

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Slot plans should be built", result.getSlotPlans());
        DayUseSlotPlan slotPlan = result.getSlotPlans().get(0);
        assertNotNull("Room criteria should be set", slotPlan.getRoomCriteria());
        assertFalse("Should have room stay candidates", slotPlan.getRoomCriteria().get(0).getRoomStayCandidates().isEmpty());
    }

    @Test
    public void should_HandleChildAges_When_BuildingRoomStayCandidates() {
        // Given
        OccupancyDetails occupancyWithChildren = new OccupancyDetails();
        occupancyWithChildren.setAdult(2);
        occupancyWithChildren.setChild(2);
        occupancyWithChildren.setChildAges(Arrays.asList(5, 8));
        occupancyWithChildren.setNumberOfRooms(1);
        hotelDetailsResponse.getHotelDetails().getRooms().get(0).getRatePlans().get(0).getAvailDetail().setOccupancyDetails(occupancyWithChildren);

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Slot plans should be built", result.getSlotPlans());
        DayUseSlotPlan slotPlan = result.getSlotPlans().get(0);
        List<DayUseRoomCriteria> roomCriteria = slotPlan.getRoomCriteria();
        assertNotNull("Room criteria should be set", roomCriteria);
        assertNotNull("Child ages should be set", roomCriteria.get(0).getRoomStayCandidates().get(0).getChildAges());
        assertEquals("Should have correct number of child ages", 2, roomCriteria.get(0).getRoomStayCandidates().get(0).getChildAges().size());
    }

    @Test
    public void should_SetNullChildAges_When_NoChildren() {
        // Given
        OccupancyDetails occupancyNoChildren = new OccupancyDetails();
        occupancyNoChildren.setAdult(2);
        occupancyNoChildren.setChild(0);
        occupancyNoChildren.setChildAges(null);
        hotelDetailsResponse.getHotelDetails().getRooms().get(0).getRatePlans().get(0).getAvailDetail().setOccupancyDetails(occupancyNoChildren);

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Slot plans should be built", result.getSlotPlans());
        DayUseSlotPlan slotPlan = result.getSlotPlans().get(0);
        assertNull("Child ages should be null when no children", 
                   slotPlan.getRoomCriteria().get(0).getRoomStayCandidates().get(0).getChildAges());
    }

    // ==================== SUPPLIER CODE EXTRACTION TESTS ====================

    @Test
    public void should_ExtractSupplierCodeFromSegmentId_When_Available() {
        // Given
        hotelDetailsResponse.getHotelDetails().getRooms().get(0).getRatePlans().get(0).setSegmentId("GTA_SEGMENT_123");

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Slot plans should be built", result.getSlotPlans());
        DayUseSlotPlan slotPlan = result.getSlotPlans().get(0);
        assertNotNull("Room criteria should be set", slotPlan.getRoomCriteria());
        assertEquals("Should extract GTA supplier code", "GTA", slotPlan.getRoomCriteria().get(0).getSupplierCode());
    }

    @Test
    public void should_UseDefaultSupplierCode_When_CannotExtract() {
        // Given
        hotelDetailsResponse.getHotelDetails().getRooms().get(0).getRatePlans().get(0).setSegmentId("UNKNOWN_SEGMENT");

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Slot plans should be built", result.getSlotPlans());
        DayUseSlotPlan slotPlan = result.getSlotPlans().get(0);
        assertEquals("Should use default INGO supplier code", "INGO", slotPlan.getRoomCriteria().get(0).getSupplierCode());
    }

    // ==================== OCCUPANCY DETAILS TESTS ====================

    @Test
    public void should_BuildOccupancyDetails_When_ValidOccupancy() {
        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Rooms should be built", result.getRooms());
        assertFalse("Should have rooms", result.getRooms().isEmpty());
        
        DayUseRoom dayUseRoom = result.getRooms().get(0);
        assertNotNull("Occupancy details should be set", dayUseRoom.getOccupancydetails());
        assertEquals("Adult count should match", 2, (int) dayUseRoom.getOccupancydetails().getNumberOfAdults());
        assertEquals("Room count should match", 1, (int) dayUseRoom.getOccupancydetails().getRoomCount());
    }

    @Test
    public void should_SetChildAgesInOccupancy_When_ChildrenPresent() {
        // Given
        OccupancyDetails occupancyWithChildren = new OccupancyDetails();
        occupancyWithChildren.setAdult(2);
        occupancyWithChildren.setChild(1);
        occupancyWithChildren.setChildAges(Arrays.asList(7));
        occupancyWithChildren.setNumberOfRooms(1);
        hotelDetailsResponse.getHotelDetails().getRooms().get(0).setOccupancyDetails(occupancyWithChildren);

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        DayUseRoom dayUseRoom = result.getRooms().get(0);
        assertNotNull("Child ages should be set", dayUseRoom.getOccupancydetails().getChildAges());
        assertEquals("Should have correct child age", Integer.valueOf(7), dayUseRoom.getOccupancydetails().getChildAges().get(0));
    }

    @Test
    public void should_HandleNullOccupancyDetails_When_Building() {
        // Given
        hotelDetailsResponse.getHotelDetails().getRooms().get(0).setOccupancyDetails(null);

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Rooms should still be built", result.getRooms());
        DayUseRoom dayUseRoom = result.getRooms().get(0);
        assertNull("Occupancy details should be null", dayUseRoom.getOccupancydetails());
    }

    // ==================== CONTEXT DETAILS TESTS ====================

    @Test
    public void should_BuildContextDetails_When_HotelDetailsExist() {
        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Context details should be set", result.getContextDetails());
        assertEquals("Currency should match", "INR", result.getContextDetails().getCurrency());
        assertEquals("Hotel category should match", "4_STAR", result.getContextDetails().getMmtHotelCategory());
    }

    // ==================== FEATURE FLAGS TESTS ====================

    @Test
    public void should_BuildFeatureFlags_When_HotelDetailsExist() {
        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Feature flags should be set", result.getFeatureFlags());
        assertTrue("Day use persuasion flag should be set", result.getFeatureFlags().isDayUsePersuasion());
    }

    @Test
    public void should_HandleNullFeatureFlags_When_RequestFlagsAreNull() {
        // Given
        dayUseRoomsRequest.setFeatureFlags(null);

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Feature flags should still be set", result.getFeatureFlags());
        assertFalse("Day use persuasion flag should be false", result.getFeatureFlags().isDayUsePersuasion());
    }

    // ==================== ERROR HANDLING TESTS ====================

    @Test
    public void should_HandleException_When_ProcessingFails() {
        // Given
        when(roomInfoHelper.transformRoomHighlights(any(), any(), anyBoolean(), anyBoolean(), any(), anyString(), anyBoolean(), anyBoolean()))
            .thenThrow(new RuntimeException("Test exception"));

        // When
        DayUseRoomsResponse result = transformer.convertSearchSlotsResponse(
            dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        assertNotNull("Response should not be null even with exception", result);
        // Should not crash and return gracefully
    }

    @Test
    public void should_LogMetrics_When_ProcessingCompletes() {
        // When
        transformer.convertSearchSlotsResponse(dayUseRoomsRequest, hotelDetailsResponse, commonModifierResponse);

        // Then
        verify(metricAspect).addToTimeInternalProcess(
            eq(Constants.PROCESS_DETAIL_RESPONSE_PROCESS), 
            eq("DETAIL_SEARCH_SLOTS"), 
            anyLong()
        );
    }

    // ==================== INITIALIZATION TESTS ====================

    @Test
    public void should_InitializeConfigurations_When_Called() throws Exception {
        // Given - Create new transformer to test @PostConstruct
        OrchSearchSearchSlotsResponseTransformerAndroid newTransformer = new OrchSearchSearchSlotsResponseTransformerAndroid();
        ReflectionTestUtils.setField(newTransformer, "commonConfigConsul", commonConfigConsul);

        // When - Call init method manually (simulating @PostConstruct)
        ReflectionTestUtils.invokeMethod(newTransformer, "init");

        // Then
        verify(commonConfigConsul).getMissingSlotDetails();
        verify(commonConfigConsul).getDayUseFunnelPersuasions();
    }

    @Test
    public void should_HandleConfigurationException_When_InitializingFails() throws Exception {
        // Given
        when(commonConfigConsul.getMissingSlotDetails()).thenThrow(new RuntimeException("Config error"));
        OrchSearchSearchSlotsResponseTransformerAndroid newTransformer = new OrchSearchSearchSlotsResponseTransformerAndroid();
        ReflectionTestUtils.setField(newTransformer, "commonConfigConsul", commonConfigConsul);

        // When - Should not throw exception
        ReflectionTestUtils.invokeMethod(newTransformer, "init");

        // Then - Should handle gracefully and create empty objects
        // Verify it attempted to get configurations
        verify(commonConfigConsul).getMissingSlotDetails();
    }
} 