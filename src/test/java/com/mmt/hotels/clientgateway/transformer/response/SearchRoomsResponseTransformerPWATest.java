package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.MobConfigPropsConsul;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.request.dayuse.DayUseRoomsRequest;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.availrooms.Alert;
import com.mmt.hotels.clientgateway.response.availrooms.DoubleBlackInfo;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseRoomsResponse;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.moblanding.CardData;
import com.mmt.hotels.clientgateway.response.moblanding.SupportDetails;
import com.mmt.hotels.clientgateway.response.rooms.PackageInclusionDetails;
import com.mmt.hotels.clientgateway.response.rooms.*;
import com.mmt.hotels.clientgateway.response.staticdetail.PrimaryOffer;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.transformer.response.pwa.SearchRoomsResponseTransformerPWA;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.enums.LuckyUserContext;
import com.mmt.hotels.model.response.MpFareHoldStatus;
import com.mmt.hotels.model.response.dayuse.MissingSlotDetail;
import com.mmt.hotels.model.response.emi.NoCostEmiDetails;
import com.mmt.hotels.model.response.flyfish.RoomSummary;
import com.mmt.hotels.model.response.mypartner.MarkUpDetails;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.PolicyDetails;
import com.mmt.hotels.model.response.pricing.RatePlan;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.pricing.WalletSurge;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import com.mmt.hotels.model.response.pricing.jsonviews.RTBPreApprovedCard;
import com.mmt.hotels.model.response.staticdata.MediaData;
import com.mmt.hotels.model.response.staticdata.SleepingBedInfo;
import com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement;
import com.mmt.hotels.model.response.staticdata.Space;
import com.mmt.hotels.model.response.staticdata.SpaceData;
import com.mmt.hotels.model.response.staticdata.*;
import com.mmt.hotels.pojo.response.CampaignPojo;
import com.mmt.model.Facility;
import com.mmt.model.FacilityGroup;
import com.mmt.model.RoomInfo;
import com.mmt.model.*;
import junit.framework.Assert;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.runners.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static com.mmt.hotels.clientgateway.transformer.response.SearchHotelsResponseTransformer.gson;
import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class SearchRoomsResponseTransformerPWATest {

    @InjectMocks
    SearchRoomsResponseTransformerPWA searchRoomsResponseTransformerPWA;

    @InjectMocks
    DateUtil dateUtil;

    @Mock
    CommonResponseTransformer commonResponseTransformer;

    @Mock
    DayUseUtil dayUseUtil;

    @InjectMocks
    private Utility utility;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    MetricAspect metricAspect;
    @Mock
    private Set<String> mypatExclusiveRateSegmentIdList;

    @Mock
    MobConfigPropsConsul mobConfigPropsConsul;

    ObjectMapperUtil objectMapperUtil = new ObjectMapperUtil();
    @Spy
    PricingEngineHelper pricingEngineHelper;

    @Mock
    private MobConfigHelper mobConfigHelper;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    @Before
    public void setup() {
        mypatExclusiveRateSegmentIdList =new HashSet<>();
        commonResponseTransformer = Mockito.spy(new CommonResponseTransformer());
        utility = Mockito.spy(new Utility());
        MockitoAnnotations.initMocks(this);
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenAnswer(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                return invocationOnMock.getArgument(0);
            }
        });

        // Keep only the required stubs
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.ROOM_DETAILS_BATHROOMS_TEXT))
                .thenReturn("Bathrooms");
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.ROOM_DETAILS_BATHROOMS_TEXT))
                .thenReturn("Bathrooms");

        Map<String,Map<String, Map<String,String>>> ratePlanNameMap = new HashMap<>();
        ratePlanNameMap.put(Constants.DEFAULT,new HashMap<>());
        ratePlanNameMap.get(Constants.DEFAULT).put(Constants.CANCELLATION_TYPE_FC,new HashMap<>());
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_FC).put(Constants.SELLABLE_ROOM_TYPE,"{NR}");
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_FC).put(Constants.SELLABLE_BED_TYPE, "{NR}");
        ratePlanNameMap.get(Constants.DEFAULT).put(Constants.CANCELLATION_TYPE_NR, new HashMap<>());
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_NR).put(Constants.SELLABLE_ROOM_TYPE, "{NR}");
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_NR).put(Constants.SELLABLE_BED_TYPE, "{NR}");

        ReflectionTestUtils.setField(utility, "ratePlanNameMap", ratePlanNameMap);
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA, "vistaraPersuasionImageUrlDetail", "test");
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA, "vistaraPersuasionImageUrlDetailDT", "test");
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA, "vistaraPersuasionColorDetail", "test");
        ReflectionTestUtils.setField(utility, "ratePlanNameMapRedesign", ratePlanNameMap);
        ReflectionTestUtils.setField(utility, "apLimitForInclusionIcons", 1);
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA, "rtbCardConfigs", new HashMap<>());
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA, "corpPreferredRateSegmentId", "1135");
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA, "pahGccText", "No Prepayment - Pay at Hotel");
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA, "defaultSearchRoomUrl", "https://promos.makemytrip.com/Growth/Images/B2C/Upgrade_Stay_Logo.png");
        ObjectMapper mapper = new ObjectMapper();
        mapper.writerWithView(PIIView.External.class);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
        String ihConfig = "{\"CABCASHBACKCARD\":{\"sequence\":13,\"cardInfo\":{\"index\":1,\"subType\":\"CABCARD\",\"id\":\"CROSSSELL\",\"titleText\":\"Free Drop to your Hotel\",\"subText\":\"Get Free Private Cab worth {currency} {cab_price} to this property. Apply offer while reviewing your booking.\",\"hasFilter\":false,\"cardAction\":[{\"title\":\"More Details\",\"webViewUrl\":\"https://promos.makemytrip.com/ih-cabs-cross-sell-terms-310724.html\"}],\"iconURL\":\"https://promos.makemytrip.com/images/CDN_upload/cab_icon_gold.png\",\"bgLinearGradient\":{\"start\":\"#FFFFFF\",\"end\":\"#FAF2E4\",\"direction\":\"diagonal_bottom\"},\"templateId\":\"IH_CROSSSELL_CARD\"}},\"FOREXCASHBACKCARD\":{\"sequence\":13,\"cardInfo\":{\"index\":1,\"subType\":\"FOREXCARD\",\"id\":\"CROSSSELL\",\"titleText\":\"Get Forex Cashback with this booking\",\"subText\":\"On this booking you will get Forex Cashback\",\"hasFilter\":false,\"cardAction\":[{\"title\":\"More Details\",\"webViewUrl\":\"https://promos.makemytrip.com/ih-forex-deal-150224.html\"}],\"iconURL\":\"https://promos.makemytrip.com/images/CDN_upload/forexicon_new.png\",\"bgLinearGradient\":{\"start\":\"#FFFFFF\",\"end\":\"#FAF2E4\",\"direction\":\"diagonal_bottom\"},\"templateId\":\"IH_CROSSSELL_CARD\"}}}\n";
        Map<String, com.mmt.hotels.clientgateway.response.moblanding.CardData> ihCashbackCardConfig = gson.fromJson(ihConfig,new TypeToken<Map<String, CardData>>(){
        }.getType());
        ReflectionTestUtils.setField(utility,"ihCashbackCardConfig",ihCashbackCardConfig);
    }

    @Test
    public void testConvertSearchRoomsResponse() {
        Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(any(), any(),Mockito.anyLong());
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), "ANDROID");
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA,"mealplanFilterEnable",true);

        Map<String,Map<String,List<String>>> supplierToRateSegmentMapping = new HashMap<>();
        supplierToRateSegmentMapping.put(Constants.PACKAGE_RATE_KEY,new HashMap<>());
        supplierToRateSegmentMapping.put(Constants.TC_CLAUSE_KEY,new HashMap<>());
        supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).put("EPXX0034",Arrays.asList("1180"));
        supplierToRateSegmentMapping.get(Constants.TC_CLAUSE_KEY).put("EPXX0034",Arrays.asList("1180"));
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA,"supplierToRateSegmentMapping",supplierToRateSegmentMapping);

        HotelRates hotelRates = new HotelRates();
        hotelRates.setIsExtraAdultChild(false);
        hotelRates.setBnplBaseAmount(10d);
        hotelRates.setBlackEligible(false);
        hotelRates.setIsBNPLAvailable(false);
        hotelRates.setPahWalletApplicable(false);
        hotelRates.setPnAvlbl(true);
        hotelRates.setPanCardRequired(true);
        hotelRates.setShowFcBanner(true);
        hotelRates.setSoldOut(false);
        hotelRates.setBreakFast(true);
        hotelRates.setFreeCancellation(true);
        hotelRates.setBreakFastAvailable(true);
        hotelRates.setFreeCancellationAvailable(true);
        hotelRates.setPAHTariffAvailable(true);

        hotelRates.setRecommendedRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getRecommendedRoomTypeDetails().setTotalDisplayFare(new DisplayFare());
        hotelRates.getRecommendedRoomTypeDetails().setFlexiCancelRoomDetail(new FlexiCancelDetails());
        FlexiCancelAddOnDetails flexiCancelAddOnDetails = new FlexiCancelAddOnDetails();
        flexiCancelAddOnDetails.setCtaUrl("test");
        PolicyDetails policyDetails = new PolicyDetails();
        policyDetails.setCurrencyCode("INR");
        policyDetails.setTitle("test");
        flexiCancelAddOnDetails.setApplied(policyDetails);
        flexiCancelAddOnDetails.setRemoved(policyDetails);
        Map<String, FlexiCancelAddOnDetails> flexiCancelAddOnDetailsMap = new HashMap<>();
        flexiCancelAddOnDetailsMap.put("FLEXI_CANCEL", flexiCancelAddOnDetails);
        hotelRates.getRecommendedRoomTypeDetails().getFlexiCancelRoomDetail().setAddOnDetails(flexiCancelAddOnDetailsMap);

        DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
        displayPriceBreakDown.setDisplayPrice(12d);
        displayPriceBreakDown.setDisplayPriceAlternateCurrency(12d);
        displayPriceBreakDown.setNonDiscountedPrice(12d);
        displayPriceBreakDown.setSavingPerc(5.05);
        displayPriceBreakDown.setBasePrice(13.05);
        displayPriceBreakDown.setHotelTax(4d);
        displayPriceBreakDown.setMmtDiscount(1d);
        displayPriceBreakDown.setCdfDiscount(1d);
        displayPriceBreakDown.setWallet(12d);
        displayPriceBreakDown.setPricingKey("key");
        displayPriceBreakDown.setCouponInfo(new BestCoupon());
        displayPriceBreakDown.getCouponInfo().setDescription("Coupon");
        displayPriceBreakDown.getCouponInfo().setCouponCode("code");
        displayPriceBreakDown.getCouponInfo().setSpecialPromoCoupon(false);
        displayPriceBreakDown.getCouponInfo().setType("promotional");
        displayPriceBreakDown.getCouponInfo().setDiscountAmount(100.0);

        List<DisplayPriceBreakDown> displayPriceBreakDownList = new ArrayList<>();
        displayPriceBreakDownList.add(new DisplayPriceBreakDown());
        displayPriceBreakDownList.get(0).setDisplayPrice(12d);
        displayPriceBreakDownList.get(0).setDisplayPriceAlternateCurrency(12d);
        displayPriceBreakDownList.get(0).setNonDiscountedPrice(12d);
        displayPriceBreakDownList.get(0).setSavingPerc(5.05);
        displayPriceBreakDownList.get(0).setBasePrice(13.05);
        displayPriceBreakDownList.get(0).setHotelTax(4d);
        displayPriceBreakDownList.get(0).setMmtDiscount(1d);
        displayPriceBreakDownList.get(0).setCdfDiscount(1d);
        displayPriceBreakDownList.get(0).setWallet(12d);
        displayPriceBreakDownList.get(0).setPricingKey("key");
        displayPriceBreakDownList.get(0).setCouponInfo(new BestCoupon());
        displayPriceBreakDownList.get(0).getCouponInfo().setDescription("Coupon");
        displayPriceBreakDownList.get(0).getCouponInfo().setCouponCode("code");
        displayPriceBreakDownList.get(0).getCouponInfo().setSpecialPromoCoupon(false);
        displayPriceBreakDownList.get(0).getCouponInfo().setType("promotional");
        displayPriceBreakDownList.get(0).getCouponInfo().setDiscountAmount(100.0);

        hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);

        hotelRates.getRecommendedRoomTypeDetails().setRoomType(new HashMap<String, RoomType>());
        hotelRates.getRecommendedRoomTypeDetails().setOccupancyDetails(new OccupancyDetails());
        hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails().setAdult(2);

        RoomType roomType = new RoomType();
        roomType.setRoomTypeCode("abc");
        roomType.setRatePlanList(new HashMap<String, RatePlan>()); // fill this from 115
        com.mmt.hotels.model.response.pricing.RatePlan ratePlanCB = new RatePlan();

        ratePlanCB.setAvailDetails(new AvailDetails());
        ratePlanCB.getAvailDetails().setOccupancyDetails(new OccupancyDetails());
        ratePlanCB.setCancelPenaltyList(new ArrayList<CancelPenalty>());
        ratePlanCB.getCancelPenaltyList().add(new CancelPenalty());
        ratePlanCB.getCancelPenaltyList().get(0).setPenaltyDescription(new Penalty());
        ratePlanCB.setInclusions(new ArrayList<Inclusion>());
        ratePlanCB.getInclusions().add(new Inclusion());
        ratePlanCB.getInclusions().get(0).setCode("abcd");
        ratePlanCB.getInclusions().get(0).setValue("abcd");
        ratePlanCB.setMealPlans(new ArrayList<MealPlan>());
        ratePlanCB.getMealPlans().add(new MealPlan());
        ratePlanCB.getMealPlans().get(0).setCode("SMAP");
        ratePlanCB.getMealPlans().get(0).setValue("abcd");
        ratePlanCB.setPaymentDetails(new PaymentDetails());
        ratePlanCB.getPaymentDetails().setPaymentMode(PaymentMode.PAS);
        ratePlanCB.setDisplayFare(new DisplayFare());
        ratePlanCB.getDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        ratePlanCB.getDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);
        ratePlanCB.setSupplierDetails(new SupplierDetails());
        ratePlanCB.setCancellationTimeline(new CancellationTimeline());
        ratePlanCB.getSupplierDetails().setCostPrice(19d);
        ratePlanCB.setCampaingText("Free Cancellation till 24 hrs");

        roomType.getRatePlanList().put("abc", ratePlanCB);


        hotelRates.getRecommendedRoomTypeDetails().getRoomType().put("abc", roomType);
        hotelRates.setRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getRoomTypeDetails().setRoomType(new HashMap<>());
        hotelRates.getRoomTypeDetails().getRoomType().put("abc", roomType);
        hotelRates.setOffers(new ArrayList<>());
        hotelRates.getOffers().add(new RangePrice());
        hotelRates.setRtbPreApproved(false);
        hotelRates.setRequestToBook(true);
        hotelRates.setExclusiveFlyerRateAvailable(true);

        List<HotelRates> hotelRatesList = new ArrayList<HotelRates>();
        hotelRatesList.add(hotelRates);

        RoomDetailsResponse roomDetailsResponse = new RoomDetailsResponse.Builder().buildHotelRates(hotelRatesList).build();


        List<HtlRmInfo> htlRmInfoList = new ArrayList<HtlRmInfo>();
        htlRmInfoList.add(new HtlRmInfo());
        htlRmInfoList.get(0).setHotelRoomInfo(new HashMap<String, RoomInfo>());
        SpaceData spaceData = new SpaceData();
        Space space = new Space();
        space.setDescriptionText("");
        space.setOpenCardText("test");
        space.setMedia(Arrays.asList(new MediaData()));
        SleepingDetails sleepingDetails = new SleepingDetails();
        sleepingDetails.setBedRoomCount(1);
        sleepingDetails.setMinOccupancy(2);
        sleepingDetails.setMaxOccupancy(3);
        sleepingDetails.setBedCount(1);
        sleepingDetails.setExtraBedCount(1);
        List<SleepingBedInfo> bedsInfoList = new ArrayList<>();
        SleepingBedInfo sleepingBedInfo = new SleepingBedInfo();
        sleepingBedInfo.setBedCount(1);
        sleepingBedInfo.setBedType("Double Bed");
        bedsInfoList.add(sleepingBedInfo);
        sleepingDetails.setBedInfo(bedsInfoList);
        sleepingDetails.setExtraBedInfo(bedsInfoList);
        space.setSleepingDetails(sleepingDetails);
        com.mmt.hotels.model.response.staticdata.SharedInfo sharedInfo = new com.mmt.hotels.model.response.staticdata.SharedInfo();
        sharedInfo.setInfoText("Info Text");
        sharedInfo.setIconUrl("www.Url.com");
        spaceData.setSharedInfo(sharedInfo);
        spaceData.setSpaces(Arrays.asList(space));
        htlRmInfoList.get(0).setSharedSpaces(spaceData);
        htlRmInfoList.get(0).setSpaceDetailsRequired(true);
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setMaster(true);
        roomInfo.setMaxAdultCount(5);
        roomInfo.setRoomSize("1");
        roomInfo.setRoomViewName("view");
        roomInfo.setBedType("king");
        roomInfo.setRoomSummary(new RoomSummary());
        roomInfo.getRoomSummary().setTopRated(true);

        List<SleepingArrangement> beds = new ArrayList<>();
        SleepingArrangement bed1 = new SleepingArrangement();
        bed1.setCount(3);
        bed1.setType("");
        beds.add(bed1);
        SleepingArrangement bed2 = new SleepingArrangement();
        bed2.setCount(1);
        bed2.setType("");
        beds.add(bed2);
        roomInfo.setBeds(beds);
        roomInfo.setPrivateSpaces(spaceData);

        htlRmInfoList.get(0).getHotelRoomInfo().put("abc", roomInfo);
        roomInfo.setFacilityWithGrp(new ArrayList<>());
        roomInfo.getFacilityWithGrp().add(new FacilityGroup());
        roomInfo.getFacilityWithGrp().get(0).setName("test");
        roomInfo.getFacilityWithGrp().get(0).setFacilities(new ArrayList<>());
        roomInfo.getFacilityWithGrp().get(0).getFacilities().add(new Facility());
        roomInfo.setRoomLevelVideos(new ArrayList<>());
        roomInfo.getRoomLevelVideos().add(new VideoInfo());
        roomInfo.getRoomLevelVideos().get(0).setTags(new ArrayList<>());
        roomInfo.getRoomLevelVideos().get(0).setUrl("url");

        LinkedHashMap<String, List<RoomSleepingInfoLayout>> roomToSleepingInfoArrangementMap = new LinkedHashMap<>();
        List<RoomSleepingInfoLayout> sleepingInfoLayouts = new ArrayList<>();
        RoomSleepingInfoLayout roomSleepingInfoLayout = new RoomSleepingInfoLayout();
        LinkedHashMap<String, SleepingInfoArrangement> stringSleepingInfoArrangementLinkedHashMap = new LinkedHashMap<>();
        SleepingInfoArrangement so = new SleepingInfoArrangement();
        so.setBedRoom(2);
        so.setGuest(4);
        so.setMaxCapacity(5);
        so.setBathRoom(3);
        so.setBedInfos(new LinkedHashMap<>());
        stringSleepingInfoArrangementLinkedHashMap.put("space1",so);
        roomSleepingInfoLayout.setSpaceIdToSleepingInfoArrangementMap(stringSleepingInfoArrangementLinkedHashMap);
        sleepingInfoLayouts.add(roomSleepingInfoLayout);
        roomToSleepingInfoArrangementMap.put("abc | abc | roomId",sleepingInfoLayouts);

        List<ExternalVendorBedRoomInfo> externalVendorBedRoomInfos = new ArrayList<>();
        ExternalVendorBedRoomInfo externalVendorBedRoomInfo = new ExternalVendorBedRoomInfo();
        externalVendorBedRoomInfo.setBedRoomDescription("description");
        externalVendorBedRoomInfo.setBedRoomName("bedRoom name");
        externalVendorBedRoomInfos.add(externalVendorBedRoomInfo);
        roomInfo.setExternalVendorBedRoomInfoList(externalVendorBedRoomInfos);

        HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity = new HotelsRoomInfoResponseEntity.Builder().buildHtlRmInfo(htlRmInfoList).build();

        Mockito.when(commonResponseTransformer.getDoubleBlackInfo(any())).thenReturn(new DoubleBlackInfo());

        HotelImage hotelImage = new HotelImage();
        hotelImage.setHotelId("test");
        hotelImage.setImageDetails(new ImageType());
        hotelImage.getImageDetails().setProfessional(new HashMap<String, List<ProfessionalImageEntity>>());
        hotelImage.getImageDetails().getProfessional().put("R", new ArrayList<>());
        hotelImage.getImageDetails().getProfessional().get("R").add(new ProfessionalImageEntity());
        SearchRoomsCriteria criteria = new SearchRoomsCriteria();
        criteria.setCheckIn("2021-01-03");
        criteria.setCheckOut("2021-01-04");
        criteria.setLocationType("region");
        ReflectionTestUtils
                .setField(searchRoomsResponseTransformerPWA, "dateUtil" , dateUtil);
       List<Filter> filterlist = new ArrayList<>();
       filterlist.add(new Filter());
       filterlist.get(0).setFilterGroup(FilterGroup.MEAL_PLAN_AVAIL);
       filterlist.get(0).setFilterValue("TWO_MEAL_AVAIL");
        Map<String,String> expDataMap = new HashMap<>();
        expDataMap.put("plcnew","true");
        CommonModifierResponse commonModifierResponse=new CommonModifierResponse();
        Mockito.when(utility.isExperimentTrue(any(),Mockito.anyString())).thenReturn(true);
        roomDetailsResponse.setLuckyUserContext(LuckyUserContext.LUCKY);
        boolean isXPercentSellOn = MapUtils.isNotEmpty(expDataMap)
                && expDataMap.containsKey(X_PERCENT_SELL_ON) && TRUE.equalsIgnoreCase(expDataMap.get(X_PERCENT_SELL_ON));

        String isXPercentSellExpText = isXPercentSellOn ? X_PERCENT_SELL_ON_TEXT : X_PERCENT_SELL_OFF_TEXT;

        SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
        searchRoomsRequest.setFeatureFlags(new FeatureFlags());
        searchRoomsRequest.getFeatureFlags().setLiteResponse(true);
        searchRoomsRequest.setSearchCriteria(new SearchRoomsCriteria());

        SearchRoomsResponse searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(searchRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap.toString(), null, criteria,filterlist,new RequestDetails(),"", commonModifierResponse);

        Assert.assertNotNull(searchRoomsResponse);
        Assert.assertNotNull(searchRoomsResponse.getLuckyUserContext());
        Assert.assertEquals(isXPercentSellExpText + "|" + PRIVILEGED_USER, searchRoomsResponse.getLuckyUserContext());
        ratePlanCB.getMealPlans().get(0).setCode("AP");
        filterlist.get(0).setFilterValue("ALL_MEAL_AVAIL");
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");
        roomDetailsResponse.getHotelRates().get(0).setRecommendedRooms(new ArrayList<>());
        roomDetailsResponse.getHotelRates().get(0).getRecommendedRooms().add(new RoomTypeDetails());
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(searchRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria,filterlist,new RequestDetails(), "",new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse);

        when(utility.isExperimentTrue(anyMap(), eq(ExperimentKeys.recommendationV1.getKey()))).thenReturn(true);
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(searchRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap.toString(), null, criteria,filterlist,new RequestDetails(),"", commonModifierResponse);

        hotelRates.setOtherRecommendedRooms(new ArrayList<>());
        hotelRates.getOtherRecommendedRooms().add(new RoomTypeDetails());
        hotelRates.getOtherRecommendedRooms().get(0).setRoomType(new HashMap<>());
        hotelRates.getOtherRecommendedRooms().get(0).getRoomType().put("def",roomType);
        hotelRates.getOtherRecommendedRooms().get(0).setTotalDisplayFare(new DisplayFare());
        hotelRates.getOtherRecommendedRooms().get(0).getTotalDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        hotelRates.getOtherRecommendedRooms().get(0).getTotalDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);

        LowestRateAPResp lowestRateAPResp = new LowestRateAPResp();
        AvailDetails availDetails = new AvailDetails();
        availDetails.setOccupancyDetails(new OccupancyDetails());
        lowestRateAPResp.setAvailDetails(availDetails);
        hotelRates.setLowestRate(lowestRateAPResp);

        hotelRates.getRoomTypeDetails().getRoomType().get("abc").getRatePlanList().get("abc").setSegmentId(Constants.MYPARTNER_SEGMENT_ID);
        hotelRates.getRoomTypeDetails().getRoomType().get("abc").getRatePlanList().get("abc").getSupplierDetails().setSupplierCode("EPXX0034");

        Map<String, TotalPricing> priceMap = new HashMap<>();
        TotalPricing totalPricing = new TotalPricing();
        totalPricing.setPayAtHotelText("text");;
        priceMap.put("abc", totalPricing);

        Mockito.when(commonResponseTransformer.getPriceMap(ratePlanCB.getDisplayFare().getDisplayPriceBreakDown(),ratePlanCB.getDisplayFare().getDisplayPriceBreakDownList(),
                "{\"ratePlanRedesign\":\"true\"}",0,null,null,1,false,"1180",
                false,false,false,false, false, null,
                new NoCostEmiDetails(), null, false, false)).thenReturn(priceMap);

        roomDetailsResponse.setLuckyUserContext(LuckyUserContext.LUCKY_UNLUCKY);
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(searchRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria,null,new RequestDetails(), "", new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse);
        Assert.assertNotNull(searchRoomsResponse.getExactRooms().get(0).getRatePlans().get(0).getPersuasions());
        Assert.assertNotNull(searchRoomsResponse.getLuckyUserContext());
        Assert.assertEquals(isXPercentSellExpText + "|" + CURSED_USER, searchRoomsResponse.getLuckyUserContext());

        hotelRates.setRoomTypeDetails(null); hotelRates.setPropertyType("Hostel");
        roomDetailsResponse.setLuckyUserContext(LuckyUserContext.UNLUCKY);
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(searchRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria,null,new RequestDetails(), "", new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse);
        Assert.assertNotNull(searchRoomsResponse.getLuckyUserContext());
        Assert.assertEquals(isXPercentSellExpText + "|" + UNFORTUNATE_USER, searchRoomsResponse.getLuckyUserContext());
        hotelRates.setRecommendedRoomTypeDetails(null);
        hotelRates.setRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getRoomTypeDetails().setRoomType(new HashMap<>());
        hotelRates.getRoomTypeDetails().getRoomType().put("abc", roomType);
        hotelRates.setOccupencyLessRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getOccupencyLessRoomTypeDetails().setRoomType(new HashMap<>());
        hotelRates.getOccupencyLessRoomTypeDetails().getRoomType().put("Def",roomType);
        hotelRates.getOccupencyLessRoomTypeDetails().setTotalDisplayFare(new DisplayFare());
        hotelRates.getOccupencyLessRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);
        hotelRates.getOccupencyLessRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        hotelRates.setCategories(new HashSet<String>(){{add("luxury_hotels");}});
        roomDetailsResponse.setLuckyUserContext(null);
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(searchRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria,null,new RequestDetails(), "", new CommonModifierResponse());

        Assert.assertNotNull(searchRoomsResponse);
        Assert.assertNotNull(searchRoomsResponse.getLuckyUserContext());
        Assert.assertEquals(X_PERCENT_SELL_OFF_TEXT, searchRoomsResponse.getLuckyUserContext());
        hotelRates.setRtbPreApprovedCard(new RTBPreApprovedCard());
        hotelRates.setRTBRatePlanPreApproved(true);
        hotelRates.setNegotiatedRateFlag(true);
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria,null,"", new RequestDetails());
        Assert.assertNotNull(searchRoomsResponse);
        Assert.assertNotNull(searchRoomsResponse.getSpecialFareInfo());

        hotelRates.setRTBRatePlanPreApproved(false);
        hotelRates.setRequestToBook(false);
        hotelRates.setNegotiatedRateFlag(false);
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria,null,"", new RequestDetails());

        Assert.assertNotNull(searchRoomsResponse);
        Assert.assertNull(searchRoomsResponse.getSpecialFareInfo());

        //for package rate plans
        setupForPackgeRatePlans(hotelRates);
        hotelRates.setOccupencyLessRoomTypeDetails(null);
        Mockito.when(commonResponseTransformer.getPriceMap(null,null,"{\"ratePlanRedesign\":\"true\"}",
                null,null,null,1,false,null,false,false,
                false,false, false, null, new NoCostEmiDetails(), null,
                false, false)).thenReturn(priceMap);
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria, null, "", new RequestDetails());
        Assert.assertNotNull(searchRoomsResponse);
        Assert.assertNotNull(searchRoomsResponse.getPackageRooms());
        Assert.assertTrue(
            searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0).getFilterCode().contains("PACKAGE_RATE"));
        //if package in locked then LUXE PACKAGE filter should not be present
        Assert.assertFalse(searchRoomsResponse.getFilters().contains("PACKAGE_RATE"));

        //setting first room and rate plan as package
        hotelRates.getRoomTypeDetails().getRoomType().entrySet().iterator().next().getValue().getRatePlanList()
                  .entrySet().iterator().next().getValue().setPackageRoomRatePlan(true);
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria, null, "", new RequestDetails());
        //first filter has to be package
        Assert.assertEquals(searchRoomsResponse.getFilters().get(0).getCode(), "PACKAGE_RATE");

        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\",\"SPKG\":\"T\"}", null, criteria, null, "", new RequestDetails());
        hotelRates.setCategories(new HashSet<String>(){{add("package_hotels");}});

        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria, null, "", new RequestDetails());

        Assert.assertEquals(searchRoomsResponse.getFilters().get(0).getCode(), "PACKAGE_RATE");

        hotelRates.setDetailDeeplinkUrl("detaildeeplinkurl?city=City&checkAvailability=true");
        hotelRates.setName("hotelName");
        hotelRates.setHotelIcon("hotelIcon");
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\"}", null, criteria, null, "", new RequestDetails());

        Assert.assertNotNull(searchRoomsResponse.getDetailDeeplinkUrl());
        Assert.assertNotNull(searchRoomsResponse.getHotelDetails());
        Assert.assertNotNull(searchRoomsResponse.getHotelDetails().getUrl());
        roomDetailsResponse.getHotelRates().get(0).getRoomTypeDetails().setRoomToSleepingInfoArrangementMap(roomToSleepingInfoArrangementMap);
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\",\"PLV2\":\"t\"}", null, criteria, null, "", new RequestDetails());
        Assert.assertNotNull(searchRoomsResponse);
        hotelRates.setRoomTypeDetails(null);
        hotelRates.setRecommendedRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getRecommendedRoomTypeDetails().setTotalDisplayFare(new DisplayFare());
        hotelRates.getRecommendedRoomTypeDetails().setRoomType(new HashMap<String, RoomType>());
        hotelRates.getRecommendedRoomTypeDetails().getRoomType().put("abc", roomType);
        roomDetailsResponse.getHotelRates().get(0).getRecommendedRoomTypeDetails().setRoomToSleepingInfoArrangementMap(roomToSleepingInfoArrangementMap);
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, "{\"ratePlanRedesign\":\"true\",\"PLV2\":\"t\"}", null, criteria, null, "", new RequestDetails());
        Assert.assertNotNull(searchRoomsResponse);
        LinkedHashMap<String, String> expdataMap2 = new LinkedHashMap<>();

        expdataMap2.put("PLV2","t");
        expdataMap2.put("mmt.backend.hotel.default.detail.default.showRecommendedRooms", "true");
        expdataMap2.put("showUpsellRecommendation", "true");

        commonModifierResponse.setExpDataMap(expdataMap2);
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(null,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap.toString(), null, criteria,filterlist,new RequestDetails(),"", commonModifierResponse);

        Assert.assertNotNull(searchRoomsResponse);

        expdataMap2.put("mmt.backend.hotel.default.detail.default.showRecommendedRooms", "true");
        expdataMap2.put("showUpsellRecommendation", "true");

        commonModifierResponse.setExpDataMap(expdataMap2);
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(searchRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap.toString(), null, criteria,filterlist,new RequestDetails(),"", commonModifierResponse);

        roomDetailsResponse.setVistaraDealAvailable(true);
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(searchRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap.toString(), null, criteria,filterlist,new RequestDetails(),"", commonModifierResponse);
        org.junit.Assert.assertNotNull(searchRoomsResponse.getPrimaryOffer());
        org.junit.Assert.assertNotNull(searchRoomsResponse.getPrimaryOffer().getDescription());
        org.junit.Assert.assertNotNull(searchRoomsResponse.getPrimaryOffer().getType());
        org.junit.Assert.assertEquals(searchRoomsResponse.getPrimaryOffer().getStyle().getBgColor(), "test");
        org.junit.Assert.assertEquals(searchRoomsResponse.getPrimaryOffer().getIconUrl(), "test");

        roomDetailsResponse.getHotelRates().get(0).setMmtIhCabCashback(100.0);
        roomDetailsResponse.getHotelRates().get(0).setMmtIhForexCashback(200.0);
        roomDetailsResponse.getHotelRates().get(0).setCabPrice(400.0);
        roomDetailsResponse.getHotelRates().get(0).setCabDetailDeepLink("www.deeplink.com");
        roomDetailsResponse.getHotelRates().get(0).setCurrencyCode("INR");
        searchRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchRoomsResponse(searchRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, expDataMap.toString(), null, criteria,filterlist,new RequestDetails(),"", commonModifierResponse);
        Assert.assertNotNull(searchRoomsResponse);
        MDC.clear();

    }

    private void setupForPackgeRatePlans(HotelRates hotelRates) {
        hotelRates.setPackageRoomDetails(new RoomTypeDetails());
        hotelRates.getPackageRoomDetails().setRoomType(new HashMap<>());
        hotelRates.getPackageRoomDetails().getRoomType().put("packageRoom1", new PackageRoomType());
        ((PackageRoomType) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1"))
            .setAnimationType("UNLOCKED");
        ((PackageRoomType) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1"))
            .setRecommendText("#MMT Recommends");
        ((PackageRoomType) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1"))
            .setCtaText("Select Package");
        hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").setRatePlanList(new HashMap<>());
        hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                  .put("packageRatePlan1", new PackageRoomRatePlan());
        ((PackageRoomRatePlan) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                                         .get("packageRatePlan1")).setExtendedCheckOutDate("2021-07-24");
        ((PackageRoomRatePlan) hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                                         .get("packageRatePlan1")).setExtendedCheckInDate("2021-07-21");
        hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                  .get("packageRatePlan1").setDisplayFare(new DisplayFare());
        hotelRates.getPackageRoomDetails().getRoomType().get("packageRoom1").getRatePlanList()
                  .get("packageRatePlan1").setPackageRoomRatePlan(true);
    }

    @Test
    public void testGetRoomHighlightsBathrooms() {

        // Create a minimal RoomInfo with only bathroom data
        RoomInfo roomInfo = new RoomInfo();
        
        // Add bathrooms
        List<BathroomArrangement> bathrooms = new ArrayList<>();
        BathroomArrangement bathroomArrangement = new BathroomArrangement();
        bathroomArrangement.setCount(2);
        bathrooms.add(bathroomArrangement);
        roomInfo.setBathrooms(bathrooms);
        
        // Test with only necessary parameters
        List<RoomHighlight> result = ReflectionTestUtils.invokeMethod(
            searchRoomsResponseTransformerPWA, 
            "getRoomHighlights", 
            roomInfo, 
            null,   // extraGuestDetail not needed for bathroom test
            false,  // altAccoHotel
            false,  // isOHSExpEnable
            null,   // roomAmenities
            "IN",   // countryCode
            false,  // amendRoomHighlights
            false   // pilgrimageBedInfoEnable
        );
        
        // Only verify bathroom-related highlights
        Assert.assertNotNull(result);
        boolean hasBathroomHighlight = false;
        for (RoomHighlight highlight : result) {
            if (highlight.getText().contains("2 Bathrooms")) {
                hasBathroomHighlight = true;
                break;
            }
        }
        Assert.assertTrue("Should have bathroom highlight", hasBathroomHighlight);
    }
    
    @Test
    public void testGetRoomHighlightsSingleBathroom() {
        // Only mock the bathroom text translation
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.ROOM_DETAILS_BATHROOM_TEXT)).thenReturn("Bathroom");
        
        // Create a minimal RoomInfo with single bathroom
        RoomInfo roomInfo = new RoomInfo();
        
        // Add single bathroom
        List<BathroomArrangement> bathrooms = new ArrayList<>();
        BathroomArrangement bathroomArrangement = new BathroomArrangement();
        bathroomArrangement.setCount(1);
        bathrooms.add(bathroomArrangement);
        roomInfo.setBathrooms(bathrooms);
        
        // Test with only necessary parameters
        List<RoomHighlight> result = ReflectionTestUtils.invokeMethod(
            searchRoomsResponseTransformerPWA, 
            "getRoomHighlights", 
            roomInfo, 
            null,   // extraGuestDetail not needed for bathroom test
            false,  // altAccoHotel
            false,  // isOHSExpEnable
            null,   // roomAmenities
            "IN",   // countryCode
            false,  // amendRoomHighlights
            false   // pilgrimageBedInfoEnable
        );
        
        // Only verify bathroom-related highlights
        Assert.assertNotNull(result);
        boolean hasBathroomHighlight = false;
        for (RoomHighlight highlight : result) {
            if (highlight.getText().contains("1 Bathroom")) {
                hasBathroomHighlight = true;
                break;
            }
        }
        Assert.assertTrue("Should have bathroom highlight", hasBathroomHighlight);
    }
    
    @Test
    public void testGetRoomHighlightsAlternateBeds() {
        // Only mock what's needed for this test
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.ROOM_DETAILS_ALTERNATE_BED_TYPE_OR_TEXT)).thenReturn("or");
        
        // Create a minimal RoomInfo with beds
        RoomInfo roomInfo = new RoomInfo();
        
        // Add beds
        List<SleepingArrangement> beds = new ArrayList<>();
        SleepingArrangement bed = new SleepingArrangement();
        bed.setCount(1);
        bed.setType("King Bed");
        beds.add(bed);
        roomInfo.setBeds(beds);
        
        // Add alternate beds
        List<SleepingArrangement> alternateBeds = new ArrayList<>();
        SleepingArrangement alternateBed = new SleepingArrangement();
        alternateBed.setCount(1);
        alternateBed.setType("Sofa Bed");
        alternateBeds.add(alternateBed);
        roomInfo.setAlternateBeds(alternateBeds);
        
        // Create minimal extra guest detail
        ExtraGuestDetail extraGuestDetail = new ExtraGuestDetail();
        extraGuestDetail.setRoomSelectionExtraBedText("Extra beds available on request");
        
        // Test with alternate beds
        List<RoomHighlight> result = ReflectionTestUtils.invokeMethod(
            searchRoomsResponseTransformerPWA, 
            "getRoomHighlights", 
            roomInfo, 
            extraGuestDetail,
            false,  // altAccoHotel
            false,  // isOHSExpEnable
            null,   // roomAmenities
            "IN",   // countryCode
            false,  // amendRoomHighlights
            true    // pilgrimageBedInfoEnable is true for this test
        );
        
        // Verify bed info
        Assert.assertNotNull(result);
        boolean hasBedHighlight = false;
        for (RoomHighlight highlight : result) {
            String text = highlight.getText();
            if (text != null && text.contains("King Bed") && text.contains("or") && text.contains("Sofa Bed")) {
                hasBedHighlight = true;
                Assert.assertEquals("Extra beds available on request", highlight.getSubText());
                break;
            }
        }
        Assert.assertTrue("Should have bed highlight with alternate beds", hasBedHighlight);
    }
    
    @Test
    public void testGetRoomHighlightsExternalVendor() {
        // No mocks needed for this test
        
        // Create a minimal RoomInfo
        RoomInfo roomInfo = new RoomInfo();
        
        // Setup external vendor bed room info list
        List<ExternalVendorBedRoomInfo> externalVendorBedRoomInfos = new ArrayList<>();
        ExternalVendorBedRoomInfo bedRoomInfo = new ExternalVendorBedRoomInfo();
        bedRoomInfo.setBedRoomName("Bedroom");
        bedRoomInfo.setBedRoomDescription("Luxury king bed");
        externalVendorBedRoomInfos.add(bedRoomInfo);
        
        roomInfo.setExternalVendorBedRoomInfoList(externalVendorBedRoomInfos);
        
        // Test with external vendor info for international hotel
        List<RoomHighlight> result = ReflectionTestUtils.invokeMethod(
            searchRoomsResponseTransformerPWA, 
            "getRoomHighlights", 
            roomInfo, 
            null,   // extraGuestDetail
            false,  // altAccoHotel
            false,  // isOHSExpEnable
            null,   // roomAmenities
            "US",   // international country code
            false,  // amendRoomHighlights
            false   // pilgrimageBedInfoEnable
        );
        
        // Verify external vendor info
        Assert.assertNotNull(result);
        boolean hasExternalVendorInfo = false;
        for (RoomHighlight highlight : result) {
            if ("Bedroom - Luxury king bed".equals(highlight.getText())) {
                hasExternalVendorInfo = true;
                break;
            }
        }
        Assert.assertTrue("Should contain external vendor info", hasExternalVendorInfo);
    }
    
    @Test
    public void testGetRoomHighlightsAmendedHighlights() {
        // No mocks needed for this test
        
        // Create a minimal RoomInfo
        RoomInfo roomInfo = new RoomInfo();

        // Add highlighted facilities
        List<com.mmt.model.FacilityGroup> highlightedFacilities = new ArrayList<>();
        com.mmt.model.FacilityGroup facilityGroup = new com.mmt.model.FacilityGroup();
        facilityGroup.setName("Popular Amenities");
        List<com.mmt.model.Facility> facilities = new ArrayList<>();
        com.mmt.model.Facility facility = new com.mmt.model.Facility();
        facility.setName("Free Wi-Fi");
        facility.setSequence(1);
        facilities.add(facility);
        facilityGroup.setFacilities(facilities);
        highlightedFacilities.add(facilityGroup);
        roomInfo.setHighlightedFacilities(highlightedFacilities);
        
        // Test with amended highlights
        List<RoomHighlight> result = ReflectionTestUtils.invokeMethod(
            searchRoomsResponseTransformerPWA, 
            "getRoomHighlights", 
            roomInfo, 
            null,   // extraGuestDetail
            false,  // altAccoHotel
            false,  // isOHSExpEnable
            null,   // roomAmenities
            "IN",   // countryCode
            true,   // amendRoomHighlights is true for this test
            false   // pilgrimageBedInfoEnable
        );
        
        // Verify highlighted facility
        Assert.assertNotNull(result);
        boolean hasHighlightedFacility = false;
        for (RoomHighlight highlight : result) {
            if ("Free Wi-Fi".equals(highlight.getText())) {
                hasHighlightedFacility = true;
                break;
            }
        }
        Assert.assertTrue("Should contain highlighted facility", hasHighlightedFacility);
    }

    @Test
    public void buildBasicRecommendedComboTest(){
        List<RoomDetails> roomDetailList = new ArrayList<>();
        RoomDetails roomDetails = new RoomDetails();
        roomDetailList.add(roomDetails);

        List<SelectRoomRatePlan> ratePlanList = new ArrayList<>();
        SelectRoomRatePlan roomRatePlan = new SelectRoomRatePlan();
        roomRatePlan.setName("Free cancellation | Breakfast");
        ratePlanList.add(roomRatePlan);
        roomDetails.setRatePlans(ratePlanList);


        BookedCancellationPolicy cancellationPolicy = new BookedCancellationPolicy();
        cancellationPolicy.setType(BookedCancellationPolicyType.FC);
        roomRatePlan.setCancellationPolicy(cancellationPolicy);

        List<Tariff> tariffList = new ArrayList<>();
        Tariff newTariff = new Tariff();
        newTariff.setCampaignAlert(new Alert());
        newTariff.getCampaignAlert().setText("Free Cancellation Summer Campaign");
        tariffList.add(newTariff);
        newTariff = new Tariff();
        tariffList.add(newTariff);

        roomRatePlan.setTariffs(tariffList);

        RecommendedCombo recommendedCombo = ReflectionTestUtils
            .invokeMethod(searchRoomsResponseTransformerPWA, "buildBasicRecommendedCombo", roomDetailList,
                          "recomendedCombo", false, true, "GROUP", null);
        Assert.assertNotNull(recommendedCombo);
        Assert.assertNotNull(recommendedCombo.getComboTariff());
        Assert.assertNotNull(recommendedCombo.getComboTariff().getCampaignAlert());
        Assert.assertEquals(recommendedCombo.getComboTariff().getCampaignAlert().getText(),"Free Cancellation Summer Campaign");

    }

    @Test
    public void reorderInclusionsTest() {
        List<BookedInclusion> inclusions = new ArrayList<>();
        inclusions.add(new BookedInclusion());
        List<BookedInclusion> result = ReflectionTestUtils
                .invokeMethod(searchRoomsResponseTransformerPWA, "reorderInclusions", inclusions);
        Assert.assertNotNull(result);
        inclusions.get(0).setCategory("ZPN");
        result = ReflectionTestUtils
                .invokeMethod(searchRoomsResponseTransformerPWA, "reorderInclusions", inclusions);
        Assert.assertNotNull(result);
    }

    @Test
    public void getTotalSearchedPaxCountTest() throws Exception {
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setAdultCount(1);
        List<Integer> childAges = Arrays.asList(5,7);
        roomStayCandidate.setChildAges(childAges);
        roomStayCandidates.add(roomStayCandidate);
        int totalSearchedPaxCount = searchRoomsResponseTransformerPWA.getTotalSearchedPaxCount(roomStayCandidates);
        Assert.assertEquals(totalSearchedPaxCount, 3);
    }

    @Test
    public void testConvertSearchSlotResponse() {
        MissingSlotDetail missingSlotDetail = new MissingSlotDetail();
        missingSlotDetail.setDuration(new HashSet<>(Arrays.asList(3, 6, 9)));
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA, "missingSlotDetails", missingSlotDetail);
        Mockito.when(polyglotService.getTranslatedData(any())).thenReturn("+₹ 2.2 taxes & service fees");
        RoomDetailsResponse roomDetailsResponseFile = null;
        HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntityFile = null;
        HotelImage hotelImageFile = null;
        DayUseRoomsRequest dayUseRoomsRequest = null;
        try {
            roomDetailsResponseFile = objectMapperUtil.getObjectFromJson(FileUtils.readFileToString(ResourceUtils.getFile("classpath:dayuse/dayuse.json")),RoomDetailsResponse.class, DependencyLayer.ORCHESTRATOR);
            hotelsRoomInfoResponseEntityFile = objectMapperUtil.getObjectFromJson(FileUtils.readFileToString(ResourceUtils.getFile("classpath:dayuse/HotelEntity.json")),HotelsRoomInfoResponseEntity.class, DependencyLayer.ORCHESTRATOR);
            hotelImageFile = objectMapperUtil.getObjectFromJson(FileUtils.readFileToString(ResourceUtils.getFile("classpath:dayuse/HotelImage.json")),HotelImage.class, DependencyLayer.ORCHESTRATOR);
            dayUseRoomsRequest = objectMapperUtil.getObjectFromJson(FileUtils.readFileToString(ResourceUtils.getFile("classpath:dayuse/dayUseRequest.json")),DayUseRoomsRequest.class, DependencyLayer.ORCHESTRATOR);

        } catch (IOException | JsonParseException e) {
            e.printStackTrace();
        }
        Mockito.when(polyglotService.getTranslatedData(any())).thenReturn("10 AM - 1 PM");
        Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(any(), any(),Mockito.anyLong());
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA,"mealplanFilterEnable",true);

        Map<String,Map<String,List<String>>> supplierToRateSegmentMapping = new HashMap<>();
        supplierToRateSegmentMapping.put(Constants.PACKAGE_RATE_KEY,new HashMap<>());
        supplierToRateSegmentMapping.put(Constants.TC_CLAUSE_KEY,new HashMap<>());
        supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).put("EPXX0034",Arrays.asList("1180"));
        supplierToRateSegmentMapping.get(Constants.TC_CLAUSE_KEY).put("EPXX0034",Arrays.asList("1180"));
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA,"supplierToRateSegmentMapping",supplierToRateSegmentMapping);

        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA, "dateUtil" , dateUtil);
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExpDataMap(new LinkedHashMap<>());
        commonModifierResponse.getExpDataMap().put("SPKG", "T");
        DayUseRoomsResponse dayUseRoomsResponse = searchRoomsResponseTransformerPWA.convertSearchSlotsResponse(roomDetailsResponseFile, hotelsRoomInfoResponseEntityFile, hotelImageFile, dayUseRoomsRequest, commonModifierResponse);

        Assert.assertNotNull(dayUseRoomsResponse);
        Assert.assertNotNull(dayUseRoomsResponse.getSlotPlans());
    }
    @Test
    public void testGetComboText(){
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Combo Text");
        String[] mealPlanCodes = {MEAL_PLAN_CODE_ACC_ONLY, MEAL_PLAN_CODE_BED_ONLY, MEAL_PLAN_CODE_ROOM_ONLY, MEAL_PLAN_CODE_BREAKFAST
                , MEAL_PLAN_CODE_BREAKFAST_LUNCH, MEAL_PLAN_CODE_BREAKFAST_DINNER, MEAL_PLAN_CODE_BREAKFAST_LUNCH_OR_DINNER, MEAL_PLAN_CODE_ALL_MEALS_AI};
        for(String mealPlan : mealPlanCodes){
            String res = searchRoomsResponseTransformerPWA.getComboText(mealPlan, 2);
            Assert.assertNotNull(res);
        }
    }

    @Test
    public void  sortBySellableTypeTest()
    {
        SearchRoomsResponse searchRoomsResponse;
        try {
            searchRoomsResponse  = new ObjectMapper().readValue(ResourceUtils.getFile("classpath:searchRoomsResponse.json"),SearchRoomsResponse.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        LinkedHashMap<String,String> expDataMap = new LinkedHashMap<>();
        expDataMap.put(Constants.OPTIMIZE_HOSTEL_SELECTION_EXP,Constants.TRUE);
        searchRoomsResponseTransformerPWA.sortBySellableType(searchRoomsResponse);
        org.junit.Assert.assertEquals(getResponseForSearchRooms(0,searchRoomsResponse),5921,0);
        org.junit.Assert.assertEquals(getResponseForSearchRooms(1,searchRoomsResponse),6736,0);
        org.junit.Assert.assertEquals(getResponseForSearchRooms(2,searchRoomsResponse),7736,0);
        org.junit.Assert.assertEquals(getResponseForSearchRooms(3,searchRoomsResponse),4140,0);
        org.junit.Assert.assertEquals(getResponseForSearchRooms(4,searchRoomsResponse),12540,0);
        org.junit.Assert.assertEquals(getResponseForSearchRooms(5,searchRoomsResponse),22300,0);

    }

    private double getResponseForSearchRooms(int index,SearchRoomsResponse searchRoomsResponse)
    {
        String defaultPriceKey = searchRoomsResponse.getOccupancyRooms().get(index).getRatePlans().get(0).getTariffs().get(0).getDefaultPriceKey();
        return searchRoomsResponse.getOccupancyRooms().get(index).getRatePlans().get(0).getTariffs().get(0).getPriceMap().get(defaultPriceKey).getDetails().stream().filter(e -> Constants.TOTAL_AMOUNT_KEY.equalsIgnoreCase(e.getKey())).findFirst().get().getAmount();
    }

    @Test
    public void  addSellableLabelFromSellableTypeTest()
    {
        SearchRoomsResponse searchRoomsResponse;
        try {
            searchRoomsResponse  = new ObjectMapper().readValue(ResourceUtils.getFile("classpath:searchRoomsResponse.json"),SearchRoomsResponse.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        LinkedHashMap<String,String> expDataMap = new LinkedHashMap<>();
        expDataMap.put(Constants.OPTIMIZE_HOSTEL_SELECTION_EXP,Constants.TRUE);
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.BEDS_SELLABLE_LABEL)).thenReturn("DORMITORY BEDS");
        searchRoomsResponseTransformerPWA.addSellableLabelFromSellableType(searchRoomsResponse);
        org.junit.Assert.assertEquals(getSellableLabel(searchRoomsResponse,0),"DORMITORY BEDS");
        org.junit.Assert.assertEquals(getSellableLabel(searchRoomsResponse,1),"DORMITORY BEDS");
        org.junit.Assert.assertEquals(getSellableLabel(searchRoomsResponse,2),"DORMITORY BEDS");
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.ROOMS_SELLABLE_LABEL)).thenReturn("PRIVATE ROOMS");
        searchRoomsResponseTransformerPWA.addSellableLabelFromSellableType(searchRoomsResponse);
        org.junit.Assert.assertEquals(getSellableLabel(searchRoomsResponse,3),"PRIVATE ROOMS");
        org.junit.Assert.assertEquals(getSellableLabel(searchRoomsResponse,4),"PRIVATE ROOMS");
        org.junit.Assert.assertEquals(getSellableLabel(searchRoomsResponse,5),"PRIVATE ROOMS");

    }

    private String getSellableLabel(SearchRoomsResponse searchRoomsResponse,int index)
    {
        return searchRoomsResponse.getOccupancyRooms().get(index).getSellableLabel();
    }

    @Test
    public void testSetBnplNewVariantDetails() {
        Map<String, String> experimentDataMap = new HashMap<>();
        experimentDataMap.put(EXP_BNPL_NEW_VARIANT, "true");
        String bnplNewVariantText = "Book now @ just Rs. 1";
        BookedInclusion bookedInclusion = new BookedInclusion();
        Mockito.when(polyglotService.getTranslatedData(BNPL_NEW_VARIANT_TEXT)).thenReturn(bnplNewVariantText);
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "setInclusionCodeAndText", bookedInclusion, BNPLVariant.BNPL_AT_1, "IN","INR");
        assertNotNull(bookedInclusion);
        assertEquals(bnplNewVariantText, bookedInclusion.getCode());
        assertEquals(bnplNewVariantText, bookedInclusion.getText());
    }

    @Test
    public void testSetBnplZeroVariantDetails() {
        Map<String, String> experimentDataMap = new HashMap<>();
        experimentDataMap.put(EXP_BNPL_NEW_VARIANT, "false");
        String bnplNewVariantText = "Book with 0 Payment";
        BookedInclusion bookedInclusion = new BookedInclusion();
        Mockito.when(polyglotService.getTranslatedData(BNPL_ZERO_VARIANT_TEXT)).thenReturn(bnplNewVariantText);
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "setInclusionCodeAndText", bookedInclusion, BNPLVariant.BNPL_AT_0, "IN", "INR");
        assertNotNull(bookedInclusion);
        assertEquals(bnplNewVariantText, bookedInclusion.getCode());
        assertEquals(bnplNewVariantText, bookedInclusion.getText());
    }

    @Test
    public void testSetBnplOldFlow() {
        Map<String, String> experimentDataMap = new HashMap<>();
        String bnplNewVariantText = "Credit card is charged";
        BookedInclusion bookedInclusion = new BookedInclusion();
        Mockito.when(polyglotService.getTranslatedData(ZERO_PAYMENT_NOW_WITH_CC)).thenReturn(bnplNewVariantText);
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "setInclusionCodeAndText", bookedInclusion, null, "IN", "INR");
        assertNotNull(bookedInclusion);
        assertEquals(bnplNewVariantText, bookedInclusion.getCode());
        assertEquals(bnplNewVariantText, bookedInclusion.getText());
    }

    @Test
    public void testSetBnplGCCDetails() {
        Map<String, String> experimentDataMap = new HashMap<>();
        experimentDataMap.put(EXP_BNPL_NEW_VARIANT, "true");
        String bnplNewVariantText = "Value";
        BookedInclusion bookedInclusion = new BookedInclusion();
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("Value");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "setInclusionCodeAndText", bookedInclusion, BNPLVariant.BNPL_AT_1, "AE", "INR");
        assertNotNull(bookedInclusion);
        assertEquals(bnplNewVariantText, bookedInclusion.getCode());
    }

    @Test
    public void buildRatePlanPersuasionsMapTest() {

        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("test");
        RatePlan ratePlanHes = new RatePlan();
        ratePlanHes.setMpFareHoldStatus(new MpFareHoldStatus());
        ratePlanHes.getMpFareHoldStatus().setHoldEligible(true);

        CommonModifierResponse commonModifierResponse=new CommonModifierResponse();
        ExtendedUser user=new ExtendedUser();
        user.setProfileType("CTA");
        user.setAffiliateId("MYPARTNER");
        commonModifierResponse.setExtendedUser(user);
        HeroTierDetails heroTierDetails=new HeroTierDetails();

        Map<String, PersuasionResponse> hotelPersuasions = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "buildRatePlanPersuasionsMap", ratePlanHes,commonModifierResponse, heroTierDetails);
        org.junit.Assert.assertNotNull(hotelPersuasions.get(Constants.BOOK_NOW_PERSUASION_KEY));
    }

    @Test
    public void setSuperPackageCardTest() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        searchRoomsResponse.setPackageRooms(new ArrayList<>());
        searchRoomsResponse.getPackageRooms().add(new RoomDetails());
        searchRoomsResponse.getPackageRooms().get(0).setRatePlans(new ArrayList<>());
        searchRoomsResponse.getPackageRooms().get(0).getRatePlans().add(new PackageSelectRoomRatePlan());
        ((PackageSelectRoomRatePlan)searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0)).setPackageInclusionDetails(new PackageInclusionDetails());
        ((PackageSelectRoomRatePlan)searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0)).getPackageInclusionDetails().setPackageBenefitsSlashedPrice("900");
        ((PackageSelectRoomRatePlan)searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0)).getPackageInclusionDetails().setPackageBenefitsPrice("300");
        ((PackageSelectRoomRatePlan)searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0)).setInclusionsList(new ArrayList<>());
        ((PackageSelectRoomRatePlan)searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0)).getInclusionsList().add(new BookedInclusion());
        ((PackageSelectRoomRatePlan)searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0)).getInclusionsList().get(0).setText("Test");
        ((PackageSelectRoomRatePlan)searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0)).getInclusionsList().get(0).setCode("Test");
        Mockito.when(polyglotService.getTranslatedData(SUPER_PACKAGE_CARD_TEXT)).thenReturn("<b>Get {incText} and more (worth ₹{1}) as a Super Package Deal just for {2}</b>");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "setSuperPackageCard",searchRoomsResponse,"USD");
        org.junit.Assert.assertNotNull(searchRoomsResponse.getRoomFilterCard());
        Assert.assertEquals(searchRoomsResponse.getRoomFilterCard().getTitle(), "<b>Get Test and more (worth ₹900) as a Super Package Deal just for 300</b>");
    }

    @Test
    public void buildPackageBenefitsTextTest() {
        com.mmt.hotels.model.response.pricing.PackageInclusionDetails packageInclusionDetails = new com.mmt.hotels.model.response.pricing.PackageInclusionDetails();
        String text = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "buildPackageBenefitsText",packageInclusionDetails,false,"INR","");
        Assert.assertEquals(text, ENJOY_PACKAGE_BENEFITS);
        packageInclusionDetails.setPackageBenefitsSlashedPrice("900");
        packageInclusionDetails.setPackageBenefitsPrice("300");
        Mockito.when(polyglotService.getTranslatedData(PACKAGE_BENEFITS_TEXT)).thenReturn("Enjoy benefits worth {cur} {1} at just ₹{2} only");
        text = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "buildPackageBenefitsText",packageInclusionDetails,true,"USD","");
        Assert.assertNotNull(ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "getSuperPackagePersuasion"));
    }
    @Test
    public void transformInclusionsTest() {
        HotelRates hotelRates = new HotelRates();
        hotelRates.setIsExtraAdultChild(false);
        RatePlan ratePlanHES = new RatePlan();
        Map<String, String> mealPlanMap = new HashMap<>();
        int ap = 1;
        boolean isBlockPah = false;
        String expData = "{\"noMealInclusionRemove\", \"true\"}";
        ratePlanHES.setMealPlans(new ArrayList<>());
        ratePlanHES.getMealPlans().add(new MealPlan());
        ratePlanHES.getMealPlans().get(0).setCode("AO");
        mealPlanMap.put("test", "test");
        Map<String, String> expDataMap = new HashMap<>();
//        expDataMap.put("noMealInclusionRemove", "true");
        ratePlanHES.setInclusions(new ArrayList<>());
        ratePlanHES.getInclusions().add(new Inclusion());
        ratePlanHES.getInclusions().get(0).setValue("meals");
        Mockito.when(utility.getExpDataMap(Mockito.anyString())).thenReturn(expDataMap);
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA,"transformInclusions",ratePlanHES,mealPlanMap,ap, isBlockPah, expData, 120d, "INR", true, hotelRates);
        ratePlanHES.getMealPlans().get(0).setCode("MAP");
        ratePlanHES.setExtraGuestDetail(new ExtraGuestDetail());
        ratePlanHES.getExtraGuestDetail().setRatePlanExtraBedText("test");
        ratePlanHES.getInclusions().get(0).setCategory("MMTBLACK");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA,"transformInclusions",ratePlanHES,mealPlanMap,ap, isBlockPah, expData, 120d, "USD", true, hotelRates);
        ap=2;
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA,"transformInclusions",ratePlanHES,mealPlanMap,ap, isBlockPah, expData, 120d,"NPR", true, hotelRates);
        ratePlanHES.getMealPlans().get(0).setCode("MAP");
        ratePlanHES.setSupplierDetails(new SupplierDetails());
        ratePlanHES.getSupplierDetails().setSupplierCode("INGOii");
        ratePlanHES.getInclusions().get(0).setValue("");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA,"transformInclusions",ratePlanHES,mealPlanMap,ap, isBlockPah, expData, 120d,"GBP", true, hotelRates);
        ratePlanHES.getExtraGuestDetail().setRatePlanExtraBedText("");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA,"transformInclusions",ratePlanHES,mealPlanMap,ap, isBlockPah, expData, 120d,"INR", true, hotelRates);
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "in");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA,"transformInclusions",ratePlanHES,mealPlanMap,ap, isBlockPah, expData, 120d, "INR", true, hotelRates);
    }

    @Test
    public void shouldReturnMealUpgradeTypeWhenRoomTypeDetailsAreNotNull() {
        RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
        roomTypeDetails.setRoomType(new HashMap<>());
        RoomType roomType = new RoomType();
        roomType.setRatePlanList(new HashMap<>());
        RatePlan ratePlan = new RatePlan();
        ratePlan.setMealPlans(new ArrayList<>());
        MealPlan mealPlan = new MealPlan();
        mealPlan.setCode("mealCode");
        ratePlan.getMealPlans().add(mealPlan);
        roomType.getRatePlanList().put("ratePlanCode", ratePlan);
        roomTypeDetails.getRoomType().put("roomTypeCode", roomType);

        String result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "getMealUpgradeType", roomTypeDetails);

        org.junit.Assert.assertEquals("mealCode", result);
    }

    @Test
    public void shouldReturnEmptyStringWhenRoomTypeDetailsAreNull() {
        String result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "getMealUpgradeType", new RoomTypeDetails());

        org.junit.Assert.assertEquals("", result);
    }

    @Test
    public void shouldReturnMealUpgradeIconWhenMealPlanCodeIsNotBlank() {
        String mealPlanCode = "mealPlanCode";
        Map<String, Map<String, String>> recommendedRoomPropertiesMap = new HashMap<>();
        Map<String, String> properties = new HashMap<>();
        properties.put("iconUrl", "iconUrl");
        recommendedRoomPropertiesMap.put(mealPlanCode, properties);
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA, "recommendedRoomPropertiesMap", recommendedRoomPropertiesMap);

        String result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "buildMealUpgradeIcon", mealPlanCode);

        org.junit.Assert.assertEquals("iconUrl", result);
    }

    @Test
    public void shouldReturnDefaultIconWhenMealPlanCodeIsBlank() {
        String result =  ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "buildMealUpgradeIcon", "");

        org.junit.Assert.assertEquals("https://promos.makemytrip.com/Growth/Images/B2C/Upgrade_Stay_Logo.png", result);
    }

    @Test
    public void shouldReturnMealUpgradeTextWhenMealPlanCodeIsNotBlank() {
        String mealPlanCode = "mealPlanCode";
        Map<String, Map<String, String>> recommendedRoomPropertiesMap = new HashMap<>();
        Map<String, String> properties = new HashMap<>();
        properties.put("benefitText", "benefitText");
        recommendedRoomPropertiesMap.put(mealPlanCode, properties);
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA, "recommendedRoomPropertiesMap", recommendedRoomPropertiesMap);
        Mockito.when(polyglotService.getTranslatedData("benefitText")).thenReturn("translatedBenefitText");

        String result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "buildMealUpgradeText", mealPlanCode);

        org.junit.Assert.assertEquals("translatedBenefitText", result);
    }

    @Test
    public void shouldReturnDefaultTextWhenMealPlanCodeIsBlank() {
        Mockito.when(polyglotService.getTranslatedData(CP_MEAL_TEXT)).thenReturn("translatedCPMealText");

        String result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "buildMealUpgradeText", "");

        org.junit.Assert.assertEquals("translatedCPMealText", result);
    }

    @Test
    public void updateRoomPersuasionWithNonMatchingPlaceholderDoesNotModifyData() {
        ObjectNode roomPersuasions = new ObjectMapper().createObjectNode();
        roomPersuasions.putObject("NON_MATCHING_KEY");
        JsonNode original = roomPersuasions.deepCopy();
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "updateRoomPersuasion", roomPersuasions);
        assertEquals(original, roomPersuasions);
    }

    @Test
    public void updateRoomPersuasionUpdatesIconTypeForLightningIcon() {
        ObjectNode roomPersuasions = new ObjectMapper().createObjectNode();
        ObjectNode placeholder = roomPersuasions.putObject("PLACEHOLDER_SELECT_M1");
        ArrayNode data = placeholder.putArray("data");
        ObjectNode item = data.addObject();
        item.put("icontype", "lightning_icon");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "updateRoomPersuasion", roomPersuasions);
        assertEquals("lightning_icon_v2", item.get("icontype").asText());
    }

    @Test
    public void updateRoomPersuasionRemovesFontTypeFromStyle() {
        ObjectNode roomPersuasions = new ObjectMapper().createObjectNode();
        ObjectNode placeholder = roomPersuasions.putObject("PLACEHOLDER_SELECT_M1");
        ArrayNode data = placeholder.putArray("data");
        ObjectNode item = data.addObject();
        ObjectNode style = item.putObject("style");
        style.put("fontType", "someFont");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "updateRoomPersuasion", roomPersuasions);
        assertFalse(style.has("fontType"));
    }

    @Test
    public void updateRoomPersuasionRemovesBgColorFromStyle() {
        ObjectNode roomPersuasions = new ObjectMapper().createObjectNode();
        ObjectNode placeholder = roomPersuasions.putObject("PLACEHOLDER_SELECT_M1");
        ArrayNode data = placeholder.putArray("data");
        ObjectNode item = data.addObject();
        ObjectNode style = item.putObject("style");
        style.put("bgColor", "#FFFFFF");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "updateRoomPersuasion", roomPersuasions);
        assertFalse(style.has("bgColor"));
    }

    @Test
    public void updateRoomPersuasionUpdatesTextColorInStyle() {
        ObjectNode roomPersuasions = new ObjectMapper().createObjectNode();
        ObjectNode placeholder = roomPersuasions.putObject("PLACEHOLDER_SELECT_M1");
        ArrayNode data = placeholder.putArray("data");
        ObjectNode item = data.addObject();
        ObjectNode style = item.putObject("style");
        style.put("textColor", "#000000");
        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "updateRoomPersuasion", roomPersuasions);
        assertEquals("#CF8100", style.get("textColor").asText());
    }

    @Test
    public void buildIconUrlReturnsEarlyBirdIconUrlForEarlyBirdDeal() {
        String deal = "EARLY_BIRD";
        String expectedIconUrl = "expectedEarlyBirdIconUrl";
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA, "earlyBirdIconUrl", expectedIconUrl);
        String actualIconUrl = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "buildIconUrlForPrimaryOffer", deal);
        assertEquals(expectedIconUrl, actualIconUrl);
    }

    @Test
    public void buildIconUrlReturnsLastMinuteIconUrlForLastMinuteDeal() {
        String deal = "LAST_MINUTE";
        String expectedIconUrl = "expectedLastMinuteIconUrl";
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA, "lastMinuteIconUrl", expectedIconUrl);
        String actualIconUrl = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "buildIconUrlForPrimaryOffer", deal);
        assertEquals(expectedIconUrl, actualIconUrl);
    }

    @Test
    public void buildIconUrlReturnsLastMinuteIconUrlForUnrecognizedDeal() {
        String deal = "UNRECOGNIZED_DEAL";
        String expectedIconUrl = "expectedLastMinuteIconUrl";
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA, "lastMinuteIconUrl", expectedIconUrl);

        String actualIconUrl = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "buildIconUrlForPrimaryOffer", deal);
        assertEquals(expectedIconUrl, actualIconUrl);
    }

    @Test
    public void setPrimaryOfferFieldsForSupplierWithEarlyBirdDealSetsCorrectDescription() {
        String deal = "EARLY_BIRD";
        String amount = "100";
        long expiry = System.currentTimeMillis() + 10000; // 10 seconds from now
        PrimaryOffer primaryOffer = new PrimaryOffer();
        String askedCurrency = "USD";

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "setPrimaryOfferFieldsForSupplier", deal, amount, expiry, primaryOffer, askedCurrency);
    }

    @Test
    public void setPrimaryOfferFieldsForSupplierWithLastMinuteDealSetsCorrectDescription() {
        String deal = "LAST_MINUTE";
        String amount = "200";
        long expiry = System.currentTimeMillis() + 20000; // 20 seconds from now
        PrimaryOffer primaryOffer = new PrimaryOffer();
        String askedCurrency = "EUR";

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "setPrimaryOfferFieldsForSupplier", deal, amount, expiry, primaryOffer, askedCurrency);

    }

    @Test
    public void setPrimaryOfferFieldsForSupplierWithNoExpiryDoesNotSetExpiry() {
        String deal = "EARLY_BIRD";
        String amount = "300";
        long expiry = 0; // No expiry
        PrimaryOffer primaryOffer = new PrimaryOffer();
        String askedCurrency = "INR";

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "setPrimaryOfferFieldsForSupplier", deal, amount, expiry, primaryOffer, askedCurrency);
    }

    @Test
    public void setPrimaryOfferFieldsForSupplierWithNegativeExpiryDoesNotSetExpiry() {
        String deal = "LAST_MINUTE";
        String amount = "400";
        long expiry = -1; // Invalid expiry
        PrimaryOffer primaryOffer = new PrimaryOffer();
        String askedCurrency = "GBP";

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "setPrimaryOfferFieldsForSupplier", deal, amount, expiry, primaryOffer, askedCurrency);
    }

    @Test
    public void setPrimaryOfferFieldsForSupplierWithUnrecognizedDealUsesDefaultDescription() {
        String deal = "UNRECOGNIZED_DEAL";
        String amount = "500";
        long expiry = System.currentTimeMillis() + 30000; // 30 seconds from now
        PrimaryOffer primaryOffer = new PrimaryOffer();
        String askedCurrency = "JPY";

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "setPrimaryOfferFieldsForSupplier", deal, amount, expiry, primaryOffer, askedCurrency);

        assertNotNull(primaryOffer.getDescription());
        assertFalse(primaryOffer.getDescription().isEmpty());
        assertNull(primaryOffer.getIconUrl()); // Assuming buildIconUrlForPrimaryOffer returns null for unrecognized deals
    }

    @Test
    public void settingPrimaryOfferFieldsForNoCostEmiSetsCorrectStyleAndDescription() {
        long emiAmount = 5000;
        int tenure = 12;
        PrimaryOffer primaryOffer = new PrimaryOffer();

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "setPrimaryOfferFieldsForNoCostEmi", emiAmount, tenure, primaryOffer);

        assertNotNull(primaryOffer.getStyle());
    }

    @Test
    public void settingPrimaryOfferFieldsForNoCostEmiWithZeroEmiAmountSetsCorrectDescription() {
        long emiAmount = 0;
        int tenure = 12;
        PrimaryOffer primaryOffer = new PrimaryOffer();

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "setPrimaryOfferFieldsForNoCostEmi", emiAmount, tenure, primaryOffer);

    }

    @Test
    public void settingPrimaryOfferFieldsForNoCostEmiWithZeroTenureSetsCorrectDescription() {
        long emiAmount = 5000;
        int tenure = 0;
        PrimaryOffer primaryOffer = new PrimaryOffer();

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "setPrimaryOfferFieldsForNoCostEmi", emiAmount, tenure, primaryOffer);

    }

    @Test
    public void testGetMealPlanCode() {
        RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
        roomTypeDetails.setRoomType(new HashMap<>());
        RoomType roomType = new RoomType();
        roomType.setRatePlanList(new HashMap<>());
        RatePlan ratePlan = new RatePlan();
        ratePlan.setMealPlans(new ArrayList<>());
        MealPlan mealPlan = new MealPlan();
        mealPlan.setCode("mealCode");
        ratePlan.getMealPlans().add(mealPlan);
        roomType.getRatePlanList().put("ratePlanCode", ratePlan);
        roomTypeDetails.getRoomType().put("roomTypeCode", roomType);

        String result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "getMealPlanCode",roomTypeDetails);

        org.junit.Assert.assertEquals("mealCode", result);
    }

    @Test
    public void testGetMealPlanCodeWhenRoomTypeDetailsAreNull() {
        String result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "getMealPlanCode",new RoomTypeDetails());
        assertNull(result);
    }
    @Test
    public void getRatePlansTest_ratePlanWithoutCancelPolicy() throws IOException {
        RoomTypeDetails roomTypeDetails = new ObjectMapper().readValue(ResourceUtils.getFile("classpath:roomType1.json"), RoomTypeDetails.class);
        Map<String, String> mealPlanMap = new HashMap<>();
        mealPlanMap.put("CP","CP_MEALPLAN");
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA,"mealPlanMapPolyglot",mealPlanMap);
        String expData = "{\"plcnew\":\"true\"}";
        Map<String, TotalPricing> priceMap = new HashMap<>();
        priceMap.put("123", new TotalPricing());
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), "PWA");
        when(commonResponseTransformer.getPriceMap(any(),anyList(),anyString(),anyInt(),anyString() ,anyString(),anyInt(),anyBoolean(),anyString(),
                anyBoolean(),anyBoolean(),anyBoolean(),anyBoolean(),anyBoolean(), any(), any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyBoolean())).thenReturn(priceMap);
        HotelRates hotelRates = new HotelRates();
        hotelRates.setIsExtraAdultChild(false);
        List<SelectRoomRatePlan> result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "getRatePlans",
                roomTypeDetails.getRoomType().get("2779"),"",expData,false,"INR","","",0,0,false,new CommonModifierResponse(),false,
                new HashMap<>(),false, false,false, false,new InstantFareInfo(),"",new MarkUpDetails(),null,false,"",false, hotelRates, false, false);
        Assert.assertNotNull(result);
        Assert.assertEquals(5,result.size());
    }

    @Test
    public void getRatePlansTest_ratePlanWithoutCancelPolicy_Desktop() throws IOException {
        RoomTypeDetails roomTypeDetails = new ObjectMapper().readValue(ResourceUtils.getFile("classpath:roomType1.json"), RoomTypeDetails.class);
        Map<String, String> mealPlanMap = new HashMap<>();
        mealPlanMap.put("CP","CP_MEALPLAN");
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA,"mealPlanMapPolyglot",mealPlanMap);
        String expData = "{\"plcnew\":\"true\"}";
        Map<String, TotalPricing> priceMap = new HashMap<>();
        priceMap.put("123", new TotalPricing());
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), "DESKTOP");
        Mockito.when(commonResponseTransformer.getPriceMap(Mockito.any(),Mockito.anyList(),anyString(),Mockito.anyInt(),anyString() ,
                anyString(),Mockito.anyInt(),Mockito.anyBoolean(),anyString(),Mockito.anyBoolean(),Mockito.anyBoolean(),
                Mockito.anyBoolean(),Mockito.anyBoolean(),Mockito.anyBoolean(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.anyBoolean(), Mockito.anyBoolean())).thenReturn(priceMap);
        HotelRates hotelRates = new HotelRates();
        hotelRates.setIsExtraAdultChild(false);
        List<SelectRoomRatePlan> result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "getRatePlans", roomTypeDetails.getRoomType().get("2779"),"",expData,
                false,"INR","","",0,0,false,new CommonModifierResponse(),false,new HashMap<>(),false, false,false, false,
                new InstantFareInfo(),"",new MarkUpDetails(),null,false,"",false, hotelRates, false , false);
        Assert.assertNotNull(result);
        Assert.assertEquals(5,result.size());
    }
    @Test
    public void getRatePlansTest_Desktop() throws IOException {
        RoomTypeDetails roomTypeDetails = new ObjectMapper().readValue(ResourceUtils.getFile("classpath:roomType2.json"), RoomTypeDetails.class);
        Map<String, String> mealPlanMap = new HashMap<>();
        mealPlanMap.put("CP","CP_MEALPLAN");
        ReflectionTestUtils.setField(searchRoomsResponseTransformerPWA,"mealPlanMapPolyglot",mealPlanMap);
        String expData = "{\"plcnew\":\"true\"}";
        Map<String, TotalPricing> priceMap = new HashMap<>();
        priceMap.put("123", new TotalPricing());
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), "DESKTOP");
        Mockito.when(commonResponseTransformer.getPriceMap(Mockito.any(),Mockito.anyList(),anyString(),Mockito.anyInt(),anyString() ,anyString(),
                Mockito.anyInt(),Mockito.anyBoolean(),anyString(),Mockito.anyBoolean(),Mockito.anyBoolean(),Mockito.anyBoolean(),
                Mockito.anyBoolean(),Mockito.anyBoolean(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean(), Mockito.anyBoolean())).thenReturn(priceMap);
        HotelRates hotelRates = new HotelRates();
        hotelRates.setIsExtraAdultChild(false);
        List<SelectRoomRatePlan> result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "getRatePlans", roomTypeDetails.getRoomType().get("2779"),"",expData,false,"INR","","",0,0,false,new CommonModifierResponse(),false,
                new HashMap<>(),false, false,false, false,new InstantFareInfo(),"",new MarkUpDetails(),null,false,"",false, hotelRates, false, false);
        Assert.assertNotNull(result);
        Assert.assertEquals(4,result.size());
    }

    @Test
    public void getSegmentsTest(){
        com.mmt.hotels.model.response.searchwrapper.Segments segments1 = new com.mmt.hotels.model.response.searchwrapper.Segments();

        segments1.setSegmentList(new HashMap<>());

        segments1.getSegmentList().put("1", new com.mmt.hotels.model.response.searchwrapper.Segment());

        segments1.getSegmentList().get("1").setId("1");

        Segments segments = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA,"getSegments",segments1);
    }


    @Test
    public void getWalletSurgeTest(){
        WalletSurge walletSurge1 = new WalletSurge();

        walletSurge1.setSurge(true);

        com.mmt.hotels.clientgateway.response.rooms.WalletSurge walletSurge = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA,"getWalletSurge",walletSurge1);
    }

    @Test
    public void getPrimaryOfferForSaleCampaignReturnsPrimaryOfferWhenCampaignPojoIsValid() {
        CampaignPojo campaignPojo = new CampaignPojo();
        campaignPojo.setIconUrl("https://example.com/icon.png");
        campaignPojo.setDescription("Special Offer");
        campaignPojo.setHeadingColor("#FFFFFF");

        PrimaryOffer result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA,"getPrimaryOfferForSaleCampaign",campaignPojo);

        assertNotNull(result);
        assertEquals("Special Offer", result.getDescription());
        assertEquals("https://example.com/icon.png", result.getIconUrl());
        assertEquals(SALE_CAMPAIGN, result.getType());
        assertEquals("#FFFFFF", result.getStyle().getBgColor());
    }

    @Test
    public void getPrimaryOfferForSaleCampaignReturnsNullWhenCampaignPojoIsNull() {
        CampaignPojo campaignPojo = null;

        PrimaryOffer result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA,"getPrimaryOfferForSaleCampaign",campaignPojo);

        assertNull(result);
    }

    @Test
    public void getPrimaryOfferForSaleCampaignReturnsNullWhenIconUrlIsBlank() {
        CampaignPojo campaignPojo = new CampaignPojo();
        campaignPojo.setIconUrl("");
        campaignPojo.setDescription("Special Offer");
        campaignPojo.setHeadingColor("#FFFFFF");

        PrimaryOffer result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA,"getPrimaryOfferForSaleCampaign",campaignPojo);

        assertNull(result);
    }

    @Test
    public void getPrimaryOfferForSaleCampaignReturnsNullWhenDescriptionIsBlank() {
        CampaignPojo campaignPojo = new CampaignPojo();
        campaignPojo.setIconUrl("https://example.com/icon.png");
        campaignPojo.setDescription("");
        campaignPojo.setHeadingColor("#FFFFFF");

        PrimaryOffer result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA,"getPrimaryOfferForSaleCampaign",campaignPojo);

        assertNull(result);
    }

    @Test
    public void getPrimaryOfferForSaleCampaignReturnsNullWhenHeadingColorIsBlank() {
        CampaignPojo campaignPojo = new CampaignPojo();
        campaignPojo.setIconUrl("https://example.com/icon.png");
        campaignPojo.setDescription("Special Offer");
        campaignPojo.setHeadingColor("");

        PrimaryOffer result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA,"getPrimaryOfferForSaleCampaign",campaignPojo);

        assertNull(result);
    }

    @Test
    public void updateSupportDetailsSetsSupportDetailsWhenNull() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        searchRoomsResponse.setSupportDetails(null);

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "updateSupportDetails", searchRoomsResponse);

        assertNotNull(searchRoomsResponse.getSupportDetails());
    }

    @Test
    public void updateSupportDetailsAddsOptionWhenOptionsNotEmpty() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        SupportDetails supportDetails = new SupportDetails();
        supportDetails.setOptions(new ArrayList<>(Collections.singletonList("Existing Option")));
        searchRoomsResponse.setSupportDetails(supportDetails);

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "updateSupportDetails", searchRoomsResponse);
    }

    @Test
    public void updateSupportDetailsAddsOptionWhenOptionsEmpty() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        SupportDetails supportDetails = new SupportDetails();
        supportDetails.setOptions(new ArrayList<>());
        searchRoomsResponse.setSupportDetails(supportDetails);

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "updateSupportDetails", searchRoomsResponse);
    }

    @Test
    public void updateSupportDetailsSetsIconUrlAndTitle() {
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
        SupportDetails supportDetails = new SupportDetails();
        searchRoomsResponse.setSupportDetails(supportDetails);

        ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "updateSupportDetails", searchRoomsResponse);

//        assertEquals(callToBookIconUrl, searchRoomsResponse.getSupportDetails().getIconUrl());
//        assertEquals(callToBookTitle, searchRoomsResponse.getSupportDetails().getTitle());
    }

    @Test
    public void getRoomTypeReturnsRoomTypeWhenRoomTypeDetailsAreNotEmpty() {
        HotelRates hotelRates = new HotelRates();
        RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
        roomTypeDetails.setRoomType(Collections.singletonMap("room1", new RoomType()));
        hotelRates.setRoomTypeDetails(roomTypeDetails);

        Map<String, RoomType> result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "getRoomType", hotelRates);


        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey("room1"));
    }

    @Test
    public void getRoomTypeReturnsRecommendedRoomTypeWhenRoomTypeDetailsAreEmpty() {
        HotelRates hotelRates = new HotelRates();
        RoomTypeDetails recommendedRoomTypeDetails = new RoomTypeDetails();
        recommendedRoomTypeDetails.setRoomType(Collections.singletonMap("room2", new RoomType()));
        hotelRates.setRecommendedRoomTypeDetails(recommendedRoomTypeDetails);

        Map<String, RoomType> result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "getRoomType", hotelRates);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey("room2"));
    }


    @Test
    public void getRoomTypeReturnsNullWhenRoomTypeDetailsAndRecommendedRoomTypeDetailsAreEmpty() {
        HotelRates hotelRates = new HotelRates();
        hotelRates.setRoomTypeDetails(new RoomTypeDetails());
        hotelRates.setRecommendedRoomTypeDetails(new RoomTypeDetails());

        Map<String, RoomType> result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "getRoomType", hotelRates);

        assertNull(result);
    }

    @Test
    public void buildRoomHighlightsBasicOfRoomAmenitiesReturnsRoomHighlightWithCorrectTextAndDescription() {
        String facilityName = "Free WiFi";
        RoomHighlight result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "buildRoomHighlightsBasicOfRoomAmenities", facilityName);


        assertNotNull(result);
        assertEquals(facilityName, result.getText());
        assertEquals(facilityName, result.getDescription());
    }

    @Test
    public void buildRoomHighlightsBasicOfRoomAmenitiesReturnsRoomHighlightWithEmptyTextAndDescriptionWhenFacilityNameIsEmpty() {
        String facilityName = "";
        RoomHighlight result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "buildRoomHighlightsBasicOfRoomAmenities", facilityName);

        assertNotNull(result);
        assertEquals(facilityName, result.getText());
        assertEquals(facilityName, result.getDescription());
    }

    @Test
    public void buildRoomHighlightsBasicOfRoomAmenitiesReturnsRoomHighlightWithNullTextAndDescriptionWhenFacilityNameIsNull() {
        String facilityName = null;
        RoomHighlight result = ReflectionTestUtils.invokeMethod(searchRoomsResponseTransformerPWA, "buildRoomHighlightsBasicOfRoomAmenities", facilityName);

        assertNotNull(result);
        assertNull(result.getText());
        assertNull(result.getDescription());
    }

}
