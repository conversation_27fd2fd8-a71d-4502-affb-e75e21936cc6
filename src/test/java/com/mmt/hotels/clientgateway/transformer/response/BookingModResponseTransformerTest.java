package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.modification.RatePreviewRequest;
import com.mmt.hotels.clientgateway.response.Facility;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.response.cbresponse.ErrorResponse;
import com.mmt.hotels.clientgateway.response.moblanding.GenericErrorEntity;
import com.mmt.hotels.clientgateway.response.modification.ProBookingData;
import com.mmt.hotels.clientgateway.response.modification.ProBookingResponse;
import com.mmt.hotels.clientgateway.response.modification.RatePreviewResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;

import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.response.errors.Error;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import com.mmt.hotels.model.response.pricing.HotelRates;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.pricing.RatePlan;
import com.mmt.hotels.model.response.pricing.RoomType;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import org.apache.commons.io.FileUtils;
import org.codehaus.jackson.map.DeserializationConfig;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class BookingModResponseTransformerTest {

    @InjectMocks
    BookingModResponseTransformer bkgModRespTransfrmr;

    @Mock
    CommonModifierResponse commonModifierResponse;
    @Mock
    private Utility utility;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        Mockito.when(utility.isMyPartner(Mockito.any(CommonModifierResponse.class))).thenReturn(true);
    }

    @Test
    public void convertPriceResponseTest() throws  Exception{
        RoomDetailsResponse roomDetailsResponse = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(FileUtils.readFileToString(ResourceUtils.getFile("classpath:amendment/AmendmentExactMatch.json")),
                RoomDetailsResponse.class);

        RatePreviewRequest ratePreviewRequest = new ObjectMapper().readValue("{\"channel\":\"B2C\",\"checkin\":\"2021-04-05\",\"checkout\":\"2021-04-06\",\"city_code\":\"CTGOI\",\"country_code\":\"IN\",\"currency\":\"INR\",\"hash_key\":\"\",\"hotel_ids\":[\"201811201259373755\"],\"id_context\":\"CORP\",\"booking_id\":\"HTLQW576HP\",\"flavour\":\"DESKTOP\",\"app_version\":\"7.8.1\",\"device_id\":\"bc123ed132f\",\"room_criteria\":[{\"room_stay_candidates\":[{\"guest_counts\":[{\"count\":\"2\"}]}],\"room_code\":\"45000000263\",\"rate_plan_code\":\"990000423293\",\"supplier_id\":\"ingoibibo\",\"sub_vendor\":\"\"}],\"traveller_email_commId\":[\"adasfa\"]}"
                ,RatePreviewRequest.class);

        CommonModifierResponse commonModifierResponse = buildCommonModifierResponse();


        RatePreviewResponse ratePreviewResponse = bkgModRespTransfrmr.convertPriceResponse(roomDetailsResponse,ratePreviewRequest,"DETAIL",commonModifierResponse);
        Assert.assertNotNull(ratePreviewResponse);

        Map<String, RoomType> roomTypeMap = roomDetailsResponse.getHotelRates().get(0).getRoomTypeDetails().getRoomType();
        for(Map.Entry<String, RoomType> roomTypeEntry: roomTypeMap.entrySet()){
            for(Map.Entry<String, RatePlan> ratePlanEntry: roomTypeEntry.getValue().getRatePlanList().entrySet()){
                ratePlanEntry.getValue().setBnplVariant(BNPLVariant.BNPL_AT_0);
                ratePlanEntry.getValue().setCancellationTimeline(new CancellationTimeline());
                break;
            }
            break;
        }

        ratePreviewResponse = bkgModRespTransfrmr.convertPriceResponse(roomDetailsResponse,ratePreviewRequest,"DETAIL",commonModifierResponse);
        Assert.assertNotNull(ratePreviewResponse);
        Assert.assertEquals(BNPLVariant.BNPL_AT_0, ratePreviewResponse.getData().getBnplVariant());

        for(Map.Entry<String, RoomType> roomTypeEntry: roomTypeMap.entrySet()){
            for(Map.Entry<String, RatePlan> ratePlanEntry: roomTypeEntry.getValue().getRatePlanList().entrySet()){
                ratePlanEntry.getValue().setBnplVariant(BNPLVariant.BNPL_AT_1);
                ratePlanEntry.getValue().setCancellationTimeline(new CancellationTimeline());
                break;
            }
            break;
        }

        ratePreviewResponse = bkgModRespTransfrmr.convertPriceResponse(roomDetailsResponse,ratePreviewRequest,"DETAIL",commonModifierResponse);
        Assert.assertNotNull(ratePreviewResponse);
        Assert.assertEquals(BNPLVariant.BNPL_AT_1, ratePreviewResponse.getData().getBnplVariant());

        roomDetailsResponse = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(FileUtils.readFileToString(ResourceUtils.getFile("classpath:amendment/AmendmentORMatch.json")),
                RoomDetailsResponse.class);
         ratePreviewResponse = bkgModRespTransfrmr.convertPriceResponse(roomDetailsResponse,ratePreviewRequest,"DETAIL",commonModifierResponse);
        Assert.assertNotNull(ratePreviewResponse);

        roomDetailsResponse = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(FileUtils.readFileToString(ResourceUtils.getFile("classpath:amendment/AmendmentRRMatch.json")),
                RoomDetailsResponse.class);
        ratePreviewResponse = bkgModRespTransfrmr.convertPriceResponse(roomDetailsResponse,ratePreviewRequest,"DETAIL",commonModifierResponse);
        Assert.assertNotNull(ratePreviewResponse);



        List<Error> errorList = new ArrayList<>();
        errorList.add(new Error.Builder().buildErrorCode("400000","unexpected error").build());
        ResponseErrors responseErrors = new ResponseErrors.Builder().buildErrorList(errorList).build();

        roomDetailsResponse = new RoomDetailsResponse.Builder().buildResponseErrors(responseErrors).build();
        ratePreviewResponse = bkgModRespTransfrmr.convertPriceResponse(roomDetailsResponse,ratePreviewRequest,"DETAIL",commonModifierResponse);
        Assert.assertNotNull(ratePreviewResponse);
        Assert.assertNotNull(ratePreviewResponse.getErrorCode());
        Assert.assertNotNull(ratePreviewResponse.getErrorMessage());

    }

    private CommonModifierResponse buildCommonModifierResponse() {
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setProfileType("CTA");
        extendedUser.setAffiliateId("MYPARTNER");
        commonModifierResponse.setExtendedUser(extendedUser);
        return commonModifierResponse;
    }

    @Test
    public  void convertPaymentRsponseTest(){
        PaymentCheckoutResponse paymentCheckoutResponse = new PaymentCheckoutResponse();
        String txnkey = "txb";

        List<Error> errorList = new ArrayList<>();
        errorList.add(new Error.Builder().buildErrorCode("123","abc").build());
        paymentCheckoutResponse.setResponseErrors(new ResponseErrors.Builder().buildErrorList(errorList).build());

        ProBookingResponse resp = bkgModRespTransfrmr.convertPaymentRsponse(paymentCheckoutResponse, txnkey);
        Assert.assertNotNull(resp);
        Assert.assertEquals("abc",resp.getErrorMessage());
        Assert.assertEquals("123", resp.getErrorCode());

       paymentCheckoutResponse = new PaymentCheckoutResponse.Builder().buildResponseErrors(null)
               .buildBookingID("NH123").buildAlternateCurrencySelected(false).buildTotalAmount("12.35").buildPaymentParams(new HashMap<>()).build();
       paymentCheckoutResponse.setThankYouURL("abcty");
        resp = bkgModRespTransfrmr.convertPaymentRsponse(paymentCheckoutResponse, txnkey);
        Assert.assertNotNull(resp);
        Assert.assertEquals(ProBookingData.State.THANKYOU,resp.getData().getState());
        Assert.assertEquals("txb", resp.getData().getStateId());
        Assert.assertEquals("abcty", resp.getData().getRedirectUrl());

        paymentCheckoutResponse.getPaymentParams().put("checkoutId","check123");
        paymentCheckoutResponse.getPaymentParams().put("checkoutUrl","check123URL");
        resp = bkgModRespTransfrmr.convertPaymentRsponse(paymentCheckoutResponse, txnkey);
        Assert.assertNotNull(resp);
        Assert.assertEquals(ProBookingData.State.PAYMENT,resp.getData().getState());
        Assert.assertEquals("check123", resp.getData().getStateId());
        Assert.assertEquals("check123URL", resp.getData().getRedirectUrl());

        paymentCheckoutResponse.getPaymentParams().put("checkoutId",null);
        paymentCheckoutResponse.getPaymentParams().put("checkoutUrl",null);
        resp = bkgModRespTransfrmr.convertPaymentRsponse(paymentCheckoutResponse, txnkey);
        Assert.assertNotNull(resp);
        Assert.assertEquals(ProBookingData.State.THANKYOU,resp.getData().getState());
    }


    @Test
    public  void convertRequestApprovalResponseTest() throws  Exception{
        CGServerResponse response = new ObjectMapper().readValue("{\"workflowId\":\"12312\"}", CGServerResponse.class );
        ReflectionTestUtils.setField(bkgModRespTransfrmr,"approvalPageUrl","https://abc/workflowid=");
        ProBookingResponse proBookingResponse = bkgModRespTransfrmr.convertRequestApprovalResponse(response, "123");

        Assert.assertNotNull(proBookingResponse);
        Assert.assertEquals(ProBookingData.State.APPROVAL,proBookingResponse.getData().getState());
        Assert.assertEquals("12312", proBookingResponse.getData().getStateId());
        Assert.assertEquals("https://abc/workflowid=12312", proBookingResponse.getData().getRedirectUrl());

        List<GenericErrorEntity> errorList = new ArrayList<>();
        errorList.add(new GenericErrorEntity("123","abc"));
        response.setResponseErrors(new ErrorResponse(errorList));

        proBookingResponse = bkgModRespTransfrmr.convertRequestApprovalResponse(response, "123");
        Assert.assertNotNull(proBookingResponse);
        Assert.assertEquals("abc",proBookingResponse.getErrorMessage());
        Assert.assertEquals("123", proBookingResponse.getErrorCode());
    }

    @Test
    public void testConvertPriceResponse_withValidData() throws Exception {
        RoomDetailsResponse roomDetailsResponse = new ObjectMapper()
                .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                .readValue(FileUtils.readFileToString(ResourceUtils.getFile("classpath:amendment/AmendmentExactMatch.json")),
                        RoomDetailsResponse.class);

        RatePreviewRequest ratePreviewRequest = new ObjectMapper().readValue("{\"channel\":\"B2C\",\"checkin\":\"2021-04-05\",\"checkout\":\"2021-04-06\",\"city_code\":\"CTGOI\",\"country_code\":\"IN\",\"currency\":\"INR\",\"hash_key\":\"\",\"hotel_ids\":[\"201811201259373755\"],\"id_context\":\"CORP\",\"booking_id\":\"HTLQW576HP\",\"flavour\":\"DESKTOP\",\"app_version\":\"7.8.1\",\"device_id\":\"bc123ed132f\",\"room_criteria\":[{\"room_stay_candidates\":[{\"guest_counts\":[{\"count\":\"2\"}]}],\"room_code\":\"45000000263\",\"rate_plan_code\":\"990000423293\",\"supplier_id\":\"ingoibibo\",\"sub_vendor\":\"\"}],\"traveller_email_commId\":[\"adasfa\"]}",
                RatePreviewRequest.class);

        CommonModifierResponse commonModifierResponse = buildCommonModifierResponse();

        RatePreviewResponse ratePreviewResponse = bkgModRespTransfrmr.convertPriceResponse(roomDetailsResponse, ratePreviewRequest, "DETAIL", commonModifierResponse);
        Assert.assertNotNull(ratePreviewResponse);
        Assert.assertTrue(ratePreviewResponse.isSuccess());

        // Additional assertions to cover more branches
        HotelRates hotelRates = roomDetailsResponse.getHotelRates().get(0);
        hotelRates.getRoomTypeDetails().getRoomType().values().forEach(roomType -> {
            roomType.getRatePlanList().values().forEach(ratePlan -> {
                ratePlan.setBnplVariant(BNPLVariant.BNPL_AT_0);
                ratePlan.setCancellationTimeline(new CancellationTimeline());
            });
        });

        ratePreviewResponse = bkgModRespTransfrmr.convertPriceResponse(roomDetailsResponse, ratePreviewRequest, "DETAIL", commonModifierResponse);
        Assert.assertNotNull(ratePreviewResponse);
        Assert.assertEquals(BNPLVariant.BNPL_AT_0, ratePreviewResponse.getData().getBnplVariant());

        hotelRates.getRoomTypeDetails().getRoomType().values().forEach(roomType -> {
            roomType.getRatePlanList().values().forEach(ratePlan -> {
                ratePlan.setBnplVariant(BNPLVariant.BNPL_AT_1);
                ratePlan.setCancellationTimeline(new CancellationTimeline());
            });
        });

        ratePreviewResponse = bkgModRespTransfrmr.convertPriceResponse(roomDetailsResponse, ratePreviewRequest, "DETAIL", commonModifierResponse);
        Assert.assertNotNull(ratePreviewResponse);
        Assert.assertEquals(BNPLVariant.BNPL_AT_1, ratePreviewResponse.getData().getBnplVariant());
    }

    @Test
    public void testConvertPriceResponse_withErrorResponse() throws Exception {
        List<Error> errorList = new ArrayList<>();
        errorList.add(new Error.Builder().buildErrorCode("400000", "unexpected error").build());
        ResponseErrors responseErrors = new ResponseErrors.Builder().buildErrorList(errorList).build();

        RoomDetailsResponse roomDetailsResponse = new RoomDetailsResponse.Builder().buildResponseErrors(responseErrors).build();
        RatePreviewRequest ratePreviewRequest = new RatePreviewRequest();
        CommonModifierResponse commonModifierResponse = buildCommonModifierResponse();

        RatePreviewResponse ratePreviewResponse = bkgModRespTransfrmr.convertPriceResponse(roomDetailsResponse, ratePreviewRequest, "DETAIL", commonModifierResponse);
        Assert.assertNotNull(ratePreviewResponse);
        Assert.assertFalse(ratePreviewResponse.isSuccess());
        Assert.assertEquals("400000", ratePreviewResponse.getErrorCode());
        Assert.assertEquals("unexpected error", ratePreviewResponse.getErrorMessage());
    }

}
