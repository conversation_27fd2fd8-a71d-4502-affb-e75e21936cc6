package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.AlternatePriceCard;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.response.PriceCardDetail;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.util.DateUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.text.ParseException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.mmt.hotels.clientgateway.constants.Constants.CHEAPER_BY;
import static com.mmt.hotels.clientgateway.constants.Constants.CHEAPEST_PRICE;
import static com.mmt.hotels.clientgateway.constants.Constants.EXPENSIVE_BY;
import static com.mmt.hotels.clientgateway.constants.Constants.SELECTED_PRICE;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

/**
 * Unit tests for AlternatePriceHelper class.
 * Tests all methods with comprehensive coverage including edge cases.
 */
@RunWith(MockitoJUnitRunner.class)
public class AlternatePriceHelperTest {

    @InjectMocks
    private AlternatePriceHelper alternatePriceHelper;

    @Mock
    private DateUtil dateUtil;

    @Mock
    private CommonConfigConsul commonConfigConsul;

    private static final String TEST_CHECK_IN = "2024-01-15";
    private static final String TEST_CHECK_OUT = "2024-01-16";
    private static final String ALT_CHECK_IN = "2024-01-16";
    private static final String ALT_CHECK_OUT = "2024-01-17";
    private static final String PRICE_WIDGET_HEADLINE = "Alternate Dates";
    private static final String PRICE_WIDGET_SUBHEADLINE = "Save more on different dates";
    private static final String PRICE_WIDGET_NEW_FEATURE_TAG = "NEW";
    private static final String PRICE_WIDGET_HOVER_HTML = "Click to select this date";
    private static final String PRICE_WIDGET_HOVER_HTML_SELECTED = "Currently selected date";

    @Before
    public void setUp() {
        // Setup common mock responses
        lenient().when(commonConfigConsul.getPriceWidgetHeadline()).thenReturn(PRICE_WIDGET_HEADLINE);
        lenient().when(commonConfigConsul.getPriceWidgetSubHeadline()).thenReturn(PRICE_WIDGET_SUBHEADLINE);
        lenient().when(commonConfigConsul.getPriceWidgetNewFeatureTag()).thenReturn(PRICE_WIDGET_NEW_FEATURE_TAG);
        lenient().when(commonConfigConsul.getPriceWidgetHoverHtml()).thenReturn(PRICE_WIDGET_HOVER_HTML);
        lenient().when(commonConfigConsul.getPriceWidgetHoverHtmlForSelected()).thenReturn(PRICE_WIDGET_HOVER_HTML_SELECTED);
        
        // Setup DateUtil mocks
        lenient().when(dateUtil.concatDate(anyString(), anyString())).thenReturn("Jan 15 - Jan 16");
        try {
            lenient().when(dateUtil.getDaysDiff(anyString(), anyString())).thenReturn(1);
        } catch (ParseException e) {
            // Handle ParseException for mock setup
        }
        lenient().when(dateUtil.dayOfWeek(anyString())).thenReturn("Monday");
        lenient().when(dateUtil.isSameDay(anyString(), anyString())).thenReturn(true);
        lenient().when(dateUtil.getLocalDate(anyString(), eq(DateUtil.YYYY_MM_DD))).thenReturn(LocalDate.of(2024, 1, 15));
    }

    // ========== buildAlternatePriceCard Tests ==========

    @Test
    public void should_BuildAlternatePriceCard_When_ValidDataProvided() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithAlternatePrices();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequest();
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();

        // When
        alternatePriceHelper.buildAlternatePriceCard(hotelDetails, searchRoomsResponse, searchRoomsRequest);

        // Then
        assertNotNull(searchRoomsResponse.getAlternatePriceCard());
        assertEquals(PRICE_WIDGET_HEADLINE, searchRoomsResponse.getAlternatePriceCard().getHeading());
        assertEquals(PRICE_WIDGET_SUBHEADLINE, searchRoomsResponse.getAlternatePriceCard().getSubheading());
        assertEquals(PRICE_WIDGET_NEW_FEATURE_TAG, searchRoomsResponse.getAlternatePriceCard().getNewFeatureTag());
        assertEquals(2, searchRoomsResponse.getAlternatePriceCard().getData().size());
        
        // Verify config consul calls
        verify(commonConfigConsul).getPriceWidgetHeadline();
        verify(commonConfigConsul).getPriceWidgetSubHeadline();
        verify(commonConfigConsul).getPriceWidgetNewFeatureTag();
    }

    @Test
    public void should_SetCorrectPriceCardDetails_When_ProcessingSelectedCard() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithSelectedCard();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequest();
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();

        // When
        alternatePriceHelper.buildAlternatePriceCard(hotelDetails, searchRoomsResponse, searchRoomsRequest);

        // Then
        List<PriceCardDetail> priceCardDetails = searchRoomsResponse.getAlternatePriceCard().getData();
        assertEquals(1, priceCardDetails.size());
        
        PriceCardDetail selectedCard = priceCardDetails.get(0);
        assertTrue(selectedCard.isSelected());
        assertTrue(selectedCard.isCheaper());
        assertEquals(5000.0, selectedCard.getPrice(), 0.001);
        assertEquals("INR", selectedCard.getCurrency());
        assertFalse(selectedCard.isSameDayOfWeek()); // Selected cards have sameDayOfWeek = false
        assertEquals(PRICE_WIDGET_HOVER_HTML_SELECTED, selectedCard.getHoverText());
        
        // Verify date range
        assertNotNull(selectedCard.getDateRange());
        assertEquals(ALT_CHECK_IN, selectedCard.getDateRange().getCheckIn());
        assertEquals(ALT_CHECK_OUT, selectedCard.getDateRange().getCheckOut());
    }

    @Test
    public void should_SetCorrectPriceCardDetails_When_ProcessingNonSelectedCard() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithNonSelectedCard();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequest();
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();

        when(dateUtil.isSameDay(anyString(), anyString())).thenReturn(true);

        // When
        alternatePriceHelper.buildAlternatePriceCard(hotelDetails, searchRoomsResponse, searchRoomsRequest);

        // Then
        List<PriceCardDetail> priceCardDetails = searchRoomsResponse.getAlternatePriceCard().getData();
        assertEquals(1, priceCardDetails.size());
        
        PriceCardDetail nonSelectedCard = priceCardDetails.get(0);
        assertFalse(nonSelectedCard.isSelected());
        assertFalse(nonSelectedCard.isCheaper());
        assertEquals(6000.0, nonSelectedCard.getPrice(), 0.001);
        assertTrue(nonSelectedCard.isSameDayOfWeek()); // Non-selected cards use calculated value
        assertEquals(PRICE_WIDGET_HOVER_HTML, nonSelectedCard.getHoverText());
    }

    @Test
    public void should_SetSamePrice_When_DeltaIsZero() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithZeroDelta();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequest();
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();

        // When
        alternatePriceHelper.buildAlternatePriceCard(hotelDetails, searchRoomsResponse, searchRoomsRequest);

        // Then
        List<PriceCardDetail> priceCardDetails = searchRoomsResponse.getAlternatePriceCard().getData();
        assertEquals(1, priceCardDetails.size());
        
        PriceCardDetail priceCardDetail = priceCardDetails.get(0);
        assertTrue(priceCardDetail.isSamePrice());
    }

    @Test
    public void should_SetNotSamePrice_When_DeltaIsNonZero() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithNonZeroDelta();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequest();
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();

        // When
        alternatePriceHelper.buildAlternatePriceCard(hotelDetails, searchRoomsResponse, searchRoomsRequest);

        // Then
        List<PriceCardDetail> priceCardDetails = searchRoomsResponse.getAlternatePriceCard().getData();
        assertEquals(1, priceCardDetails.size());
        
        PriceCardDetail priceCardDetail = priceCardDetails.get(0);
        assertFalse(priceCardDetail.isSamePrice());
    }

    @Test
    public void should_SetSubTextData_When_DeltaIsSignificant() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithSignificantDelta();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequest();
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();

        // When
        alternatePriceHelper.buildAlternatePriceCard(hotelDetails, searchRoomsResponse, searchRoomsRequest);

        // Then
        List<PriceCardDetail> priceCardDetails = searchRoomsResponse.getAlternatePriceCard().getData();
        assertEquals(2, priceCardDetails.size()); // We have 2 cards: selected and alternate
        
        // Find the non-selected card
        PriceCardDetail priceCardDetail = priceCardDetails.stream()
                .filter(card -> !card.isSelected())
                .findFirst()
                .orElse(null);
        
        assertNotNull(priceCardDetail);
        assertNotNull(priceCardDetail.getSubTextData());
        assertEquals(200.0, priceCardDetail.getSubTextData().getAmount(), 0.001);
        assertEquals(CHEAPER_BY, priceCardDetail.getSubTextData().getText());
    }

    @Test
    public void should_SetWeekendFlag_When_WeekendRateAvailable() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithAlternatePrices();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequest();
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();

        // Mock weekend scenario
        LocalDate saturdayDate = LocalDate.of(2024, 1, 20); // Saturday
        LocalDate sundayDate = LocalDate.of(2024, 1, 21);   // Sunday
        LocalDate searchDate = LocalDate.of(2024, 1, 15);   // Monday
        
        when(dateUtil.getLocalDate(eq(ALT_CHECK_IN), eq(DateUtil.YYYY_MM_DD))).thenReturn(saturdayDate);
        when(dateUtil.getLocalDate(eq(ALT_CHECK_OUT), eq(DateUtil.YYYY_MM_DD))).thenReturn(sundayDate);
        when(dateUtil.getLocalDate(eq(TEST_CHECK_IN), eq(DateUtil.YYYY_MM_DD))).thenReturn(searchDate);

        // When
        alternatePriceHelper.buildAlternatePriceCard(hotelDetails, searchRoomsResponse, searchRoomsRequest);

        // Then
        List<PriceCardDetail> priceCardDetails = searchRoomsResponse.getAlternatePriceCard().getData();
        assertTrue(priceCardDetails.get(0).isComingWeekend());
    }

    @Test
    public void should_SetNextDayFlag_When_NextDayRateAvailable() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithAlternatePrices();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequest();
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();

        // Mock next day scenario
        LocalDate searchDate = LocalDate.of(2024, 1, 15);
        LocalDate nextDate = LocalDate.of(2024, 1, 16);
        
        when(dateUtil.getLocalDate(eq(TEST_CHECK_IN), eq(DateUtil.YYYY_MM_DD))).thenReturn(searchDate);
        when(dateUtil.getLocalDate(eq(ALT_CHECK_IN), eq(DateUtil.YYYY_MM_DD))).thenReturn(nextDate);

        // When
        alternatePriceHelper.buildAlternatePriceCard(hotelDetails, searchRoomsResponse, searchRoomsRequest);

        // Then
        List<PriceCardDetail> priceCardDetails = searchRoomsResponse.getAlternatePriceCard().getData();
        assertTrue(priceCardDetails.get(0).isNextDay());
    }

    @Test
    public void should_HandleException_When_ProcessingFails() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithAlternatePrices();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequest();
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();

        // Mock exception
        when(dateUtil.concatDate(anyString(), anyString())).thenThrow(new RuntimeException("Test exception"));

        // When
        alternatePriceHelper.buildAlternatePriceCard(hotelDetails, searchRoomsResponse, searchRoomsRequest);

        // Then - Should not throw exception, should handle gracefully
        // The method catches exceptions and logs them, so we verify it doesn't crash
        assertNotNull(searchRoomsResponse); // Verify object is still valid
    }

    @Test
    public void should_HandleNullSelectedPriceCard_When_NoSelectedCardExists() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithoutSelectedCard();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequest();
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();

        // When
        alternatePriceHelper.buildAlternatePriceCard(hotelDetails, searchRoomsResponse, searchRoomsRequest);

        // Then
        List<PriceCardDetail> priceCardDetails = searchRoomsResponse.getAlternatePriceCard().getData();
        assertEquals(1, priceCardDetails.size());
        // Should handle null selected card gracefully in delta calculation
    }

    @Test
    public void should_HandleNullDates_When_DateUtilReturnsNull() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithAlternatePrices();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequest();
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();

        // Mock null dates
        when(dateUtil.getLocalDate(anyString(), eq(DateUtil.YYYY_MM_DD))).thenReturn(null);

        // When
        alternatePriceHelper.buildAlternatePriceCard(hotelDetails, searchRoomsResponse, searchRoomsRequest);

        // Then - Should handle gracefully without throwing NPE
        assertNotNull(searchRoomsResponse.getAlternatePriceCard());
        List<PriceCardDetail> priceCardDetails = searchRoomsResponse.getAlternatePriceCard().getData();
        assertNotNull(priceCardDetails);
        assertFalse(priceCardDetails.isEmpty());
        assertFalse(priceCardDetails.get(0).isComingWeekend());
        assertFalse(priceCardDetails.get(0).isNextDay());
    }

    @Test
    public void should_SetExpensiveBy_When_PriceCardIsNotCheaper() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithExpensiveCard();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequest();
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();

        // When
        alternatePriceHelper.buildAlternatePriceCard(hotelDetails, searchRoomsResponse, searchRoomsRequest);

        // Then
        List<PriceCardDetail> priceCardDetails = searchRoomsResponse.getAlternatePriceCard().getData();
        assertEquals(2, priceCardDetails.size()); // We have 2 cards: selected and expensive
        
        // Find the non-selected card
        PriceCardDetail priceCardDetail = priceCardDetails.stream()
                .filter(card -> !card.isSelected())
                .findFirst()
                .orElse(null);
        
        assertNotNull(priceCardDetail);
        assertNotNull(priceCardDetail.getSubTextData());
        assertEquals(EXPENSIVE_BY, priceCardDetail.getSubTextData().getText());
    }

    @Test
    public void should_SetSelectedPriceText_When_CardIsSelected() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithSelectedCheapestCard();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequest();
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();

        // When
        alternatePriceHelper.buildAlternatePriceCard(hotelDetails, searchRoomsResponse, searchRoomsRequest);

        // Then
        List<PriceCardDetail> priceCardDetails = searchRoomsResponse.getAlternatePriceCard().getData();
        assertEquals(1, priceCardDetails.size());
        
        PriceCardDetail priceCardDetail = priceCardDetails.get(0);
        assertNotNull(priceCardDetail.getSubTextData());
        assertEquals(CHEAPEST_PRICE, priceCardDetail.getSubTextData().getText());
    }

    @Test
    public void should_SetSelectedPriceText_When_CardIsSelectedButNotCheaper() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithSelectedExpensiveCard();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequest();
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();

        // When
        alternatePriceHelper.buildAlternatePriceCard(hotelDetails, searchRoomsResponse, searchRoomsRequest);

        // Then
        List<PriceCardDetail> priceCardDetails = searchRoomsResponse.getAlternatePriceCard().getData();
        assertEquals(1, priceCardDetails.size());
        
        PriceCardDetail priceCardDetail = priceCardDetails.get(0);
        assertNotNull(priceCardDetail.getSubTextData());
        assertEquals(SELECTED_PRICE, priceCardDetail.getSubTextData().getText());
    }

    @Test
    public void should_HandleMultiDayLengthOfStay_When_LOSIsNotOne() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithAlternatePrices();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequest();
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();

        // Mock multi-day LOS
        try {
            when(dateUtil.getDaysDiff(anyString(), anyString())).thenReturn(3);
        } catch (ParseException e) {
            // Handle ParseException for test
        }

        // When
        alternatePriceHelper.buildAlternatePriceCard(hotelDetails, searchRoomsResponse, searchRoomsRequest);

        // Then
        List<PriceCardDetail> priceCardDetails = searchRoomsResponse.getAlternatePriceCard().getData();
        assertFalse(priceCardDetails.get(0).isComingWeekend());
        assertFalse(priceCardDetails.get(0).isNextDay());
    }

    @Test
    public void should_HandleDifferentDayOfWeek_When_DaysDoNotMatch() {
        // Given
        HotelDetails hotelDetails = createHotelDetailsWithAlternatePrices();
        SearchRoomsRequest searchRoomsRequest = createSearchRoomsRequest();
        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();

        // Mock different days
        lenient().when(dateUtil.dayOfWeek(eq(ALT_CHECK_IN))).thenReturn("Tuesday");
        lenient().when(dateUtil.dayOfWeek(eq(ALT_CHECK_OUT))).thenReturn("Wednesday");
        lenient().when(dateUtil.dayOfWeek(eq(TEST_CHECK_IN))).thenReturn("Monday");
        lenient().when(dateUtil.dayOfWeek(eq(TEST_CHECK_OUT))).thenReturn("Tuesday");
        lenient().when(dateUtil.isSameDay("Tuesday", "Monday")).thenReturn(false);
        lenient().when(dateUtil.isSameDay("Wednesday", "Tuesday")).thenReturn(false);

        // When
        alternatePriceHelper.buildAlternatePriceCard(hotelDetails, searchRoomsResponse, searchRoomsRequest);

        // Then
        List<PriceCardDetail> priceCardDetails = searchRoomsResponse.getAlternatePriceCard().getData();
        assertFalse(priceCardDetails.get(0).isSameDayOfWeek());
    }

    // ========== Helper Methods ==========

    private HotelDetails createHotelDetailsWithAlternatePrices() {
        HotelDetails hotelDetails = new HotelDetails();
        List<AlternatePriceCard> alternatePriceCards = new ArrayList<>();
        
        // Add selected card
        AlternatePriceCard selectedCard = createAlternatePriceCard(true, true, 5000.0, 0.0);
        alternatePriceCards.add(selectedCard);
        
        // Add non-selected card
        AlternatePriceCard nonSelectedCard = createAlternatePriceCard(false, false, 6000.0, 1000.0);
        alternatePriceCards.add(nonSelectedCard);
        
        hotelDetails.setAlternateDatePriceDetails(alternatePriceCards);
        return hotelDetails;
    }

    private HotelDetails createHotelDetailsWithSelectedCard() {
        HotelDetails hotelDetails = new HotelDetails();
        List<AlternatePriceCard> alternatePriceCards = new ArrayList<>();
        
        AlternatePriceCard selectedCard = createAlternatePriceCard(true, true, 5000.0, 0.0);
        alternatePriceCards.add(selectedCard);
        
        hotelDetails.setAlternateDatePriceDetails(alternatePriceCards);
        return hotelDetails;
    }

    private HotelDetails createHotelDetailsWithNonSelectedCard() {
        HotelDetails hotelDetails = new HotelDetails();
        List<AlternatePriceCard> alternatePriceCards = new ArrayList<>();
        
        AlternatePriceCard nonSelectedCard = createAlternatePriceCard(false, false, 6000.0, 1000.0);
        alternatePriceCards.add(nonSelectedCard);
        
        hotelDetails.setAlternateDatePriceDetails(alternatePriceCards);
        return hotelDetails;
    }

    private HotelDetails createHotelDetailsWithZeroDelta() {
        HotelDetails hotelDetails = new HotelDetails();
        List<AlternatePriceCard> alternatePriceCards = new ArrayList<>();
        
        AlternatePriceCard card = createAlternatePriceCard(false, true, 5000.0, 0.0);
        alternatePriceCards.add(card);
        
        hotelDetails.setAlternateDatePriceDetails(alternatePriceCards);
        return hotelDetails;
    }

    private HotelDetails createHotelDetailsWithNonZeroDelta() {
        HotelDetails hotelDetails = new HotelDetails();
        List<AlternatePriceCard> alternatePriceCards = new ArrayList<>();
        
        AlternatePriceCard card = createAlternatePriceCard(false, true, 5000.0, 100.0);
        alternatePriceCards.add(card);
        
        hotelDetails.setAlternateDatePriceDetails(alternatePriceCards);
        return hotelDetails;
    }

    private HotelDetails createHotelDetailsWithSignificantDelta() {
        HotelDetails hotelDetails = new HotelDetails();
        List<AlternatePriceCard> alternatePriceCards = new ArrayList<>();
        
        // Add selected card for delta calculation
        AlternatePriceCard selectedCard = createAlternatePriceCard(true, true, 5000.0, 0.0);
        alternatePriceCards.add(selectedCard);
        
        // Add card with significant delta (> 3% of selected price)
        AlternatePriceCard card = createAlternatePriceCard(false, true, 4800.0, 200.0);
        alternatePriceCards.add(card);
        
        hotelDetails.setAlternateDatePriceDetails(alternatePriceCards);
        return hotelDetails;
    }

    private HotelDetails createHotelDetailsWithoutSelectedCard() {
        HotelDetails hotelDetails = new HotelDetails();
        List<AlternatePriceCard> alternatePriceCards = new ArrayList<>();
        
        AlternatePriceCard card = createAlternatePriceCard(false, true, 5000.0, 100.0);
        alternatePriceCards.add(card);
        
        hotelDetails.setAlternateDatePriceDetails(alternatePriceCards);
        return hotelDetails;
    }

    private HotelDetails createHotelDetailsWithExpensiveCard() {
        HotelDetails hotelDetails = new HotelDetails();
        List<AlternatePriceCard> alternatePriceCards = new ArrayList<>();
        
        // Add selected card for delta calculation
        AlternatePriceCard selectedCard = createAlternatePriceCard(true, true, 5000.0, 0.0);
        alternatePriceCards.add(selectedCard);
        
        // Add expensive card with significant delta
        AlternatePriceCard expensiveCard = createAlternatePriceCard(false, false, 5300.0, 300.0);
        alternatePriceCards.add(expensiveCard);
        
        hotelDetails.setAlternateDatePriceDetails(alternatePriceCards);
        return hotelDetails;
    }

    private HotelDetails createHotelDetailsWithSelectedCheapestCard() {
        HotelDetails hotelDetails = new HotelDetails();
        List<AlternatePriceCard> alternatePriceCards = new ArrayList<>();
        
        AlternatePriceCard selectedCard = createAlternatePriceCard(true, true, 5000.0, 0.0);
        alternatePriceCards.add(selectedCard);
        
        hotelDetails.setAlternateDatePriceDetails(alternatePriceCards);
        return hotelDetails;
    }

    private HotelDetails createHotelDetailsWithSelectedExpensiveCard() {
        HotelDetails hotelDetails = new HotelDetails();
        List<AlternatePriceCard> alternatePriceCards = new ArrayList<>();
        
        AlternatePriceCard selectedCard = createAlternatePriceCard(true, false, 6000.0, 0.0);
        alternatePriceCards.add(selectedCard);
        
        hotelDetails.setAlternateDatePriceDetails(alternatePriceCards);
        return hotelDetails;
    }

    private AlternatePriceCard createAlternatePriceCard(boolean selected, boolean cheaper, double price, double delta) {
        AlternatePriceCard card = new AlternatePriceCard();
        card.setSelected(selected);
        card.setCheaper(cheaper);
        card.setPrice(price);
        card.setDelta(delta);
        card.setCurrency("INR");
        card.setCheckIn(ALT_CHECK_IN);
        card.setCheckOut(ALT_CHECK_OUT);
        return card;
    }

    private SearchRoomsRequest createSearchRoomsRequest() {
        SearchRoomsRequest request = new SearchRoomsRequest();
        SearchRoomsCriteria criteria = new SearchRoomsCriteria();
        criteria.setCheckIn(TEST_CHECK_IN);
        criteria.setCheckOut(TEST_CHECK_OUT);
        request.setSearchCriteria(criteria);
        return request;
    }
} 