package com.mmt.hotels.clientgateway.transformer.request;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.*;

import com.gommt.hotels.orchestrator.detail.enums.Currency;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import com.gommt.hotels.orchestrator.detail.enums.*;
import com.gommt.hotels.orchestrator.detail.model.objects.*;
import com.gommt.hotels.orchestrator.detail.model.request.DetailRequest;
import com.gommt.hotels.orchestrator.detail.model.request.common.ClientDetails;
import com.gommt.hotels.orchestrator.detail.model.request.common.FilterDetails;
import com.gommt.hotels.orchestrator.detail.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.detail.model.state.*;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.GuestCount;

/**
 * Comprehensive test class for OrchUpdatedPriceRequestTransformer
 * Ensures 100% line coverage for all methods and branches using ReflectionTestUtils
 */
@RunWith(MockitoJUnitRunner.class)
public class OrchUpdatedPriceRequestTransformerTest {

    @InjectMocks
    private OrchUpdatedPriceRequestTransformer transformer;

    @Mock
    private Utility utility;

    private UpdatePriceRequest updatePriceRequest;
    private CommonModifierResponse commonModifierResponse;
    private UpdatedPriceCriteria searchCriteria;
    private com.mmt.hotels.clientgateway.request.RequestDetails requestDetails;
    private ExtendedUser extendedUser;
    private DeviceDetails deviceDetails;
    private com.mmt.hotels.clientgateway.request.FeatureFlags featureFlags;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        setupTestData();
    }

    private void setupTestData() {
        // Setup UpdatePriceRequest
        updatePriceRequest = new UpdatePriceRequest();
        updatePriceRequest.setExpData("{\"test\":\"data\"}");
        updatePriceRequest.setExpVariantKeys("variant1,variant2");
        updatePriceRequest.setExpDataMap(new HashMap<String, String>() {{
            put("key1", "value1");
        }});

        // Setup SearchCriteria
        searchCriteria = new UpdatedPriceCriteria();
        searchCriteria.setHotelId("12345");
        searchCriteria.setCheckIn("2024-01-01");
        searchCriteria.setCheckOut("2024-01-02");
        searchCriteria.setCityCode("DEL");
        searchCriteria.setCountryCode("IN");
        searchCriteria.setLocationId("LOC123");
        searchCriteria.setLocationType("CITY");
        searchCriteria.setCurrency("INR");
        searchCriteria.setSearchType("NORMAL");

        // Setup RoomStayCandidates
        com.mmt.hotels.clientgateway.request.RoomStayCandidate roomCandidate = new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        roomCandidate.setAdultCount(2);
        roomCandidate.setChildAges(Arrays.asList(5, 10));
        searchCriteria.setRoomStayCandidates(Arrays.asList(roomCandidate));

        // Setup RoomCriteria
        UpdatedPriceRoomCriteria roomCriteria = new UpdatedPriceRoomCriteria();
        roomCriteria.setRoomCode("ROOM001");
        roomCriteria.setRatePlanCode("RATE001");
        roomCriteria.setMtKey("MT123");
        roomCriteria.setPricingKey("PRICE123");
        roomCriteria.setSupplierCode("SUP001");
        roomCriteria.setRoomStayCandidates(Arrays.asList(roomCandidate));
        searchCriteria.setRoomCriteria(Arrays.asList(roomCriteria));

        // Setup MultiCurrencyInfo
        com.mmt.hotels.clientgateway.request.MultiCurrencyInfo multiCurrencyInfo = new com.mmt.hotels.clientgateway.request.MultiCurrencyInfo();
        multiCurrencyInfo.setUserCurrency("USD");
        multiCurrencyInfo.setRegionCurrency("INR");
        searchCriteria.setMultiCurrencyInfo(multiCurrencyInfo);

        // Setup UserGlobalInfo
        com.mmt.hotels.clientgateway.request.UserGlobalInfo userGlobalInfo = new com.mmt.hotels.clientgateway.request.UserGlobalInfo();
        userGlobalInfo.setUserCountry("IN");
        userGlobalInfo.setEntityName("MMT");
        searchCriteria.setUserGlobalInfo(userGlobalInfo);

        updatePriceRequest.setSearchCriteria(searchCriteria);

        // Setup RequestDetails
        requestDetails = new com.mmt.hotels.clientgateway.request.RequestDetails();
        requestDetails.setRequestId("REQ123");
        requestDetails.setJourneyId("JOURNEY123");
        requestDetails.setVisitorId("VISITOR123");
        requestDetails.setFunnelSource(Funnel.HOTELS.getName());
        requestDetails.setPageContext("DETAIL");
        requestDetails.setChannel("WEB");
        requestDetails.setBrand("MMT");
        requestDetails.setIdContext("B2C");
        requestDetails.setSiteDomain("IN");
        requestDetails.setRequestor("TEST");
        requestDetails.setLoggedIn(true);
        requestDetails.setMyraMsgId("MYRA123");
        requestDetails.setCouponCount(2);
        requestDetails.setPayMode("CC");

        // Setup TrafficSource
        com.mmt.hotels.clientgateway.request.TrafficSource trafficSource = new com.mmt.hotels.clientgateway.request.TrafficSource();
        trafficSource.setSource("DIRECT");
        trafficSource.setType("B2C");
        trafficSource.setFlowType("NORMAL");
        requestDetails.setTrafficSource(trafficSource);

        updatePriceRequest.setRequestDetails(requestDetails);

        // Setup DeviceDetails
        deviceDetails = new DeviceDetails();
        deviceDetails.setDeviceId("DEVICE123");
        deviceDetails.setDeviceName("iPhone 12");
        deviceDetails.setBookingDevice("MOBILE");
        deviceDetails.setAppVersion("1.0.0");
        deviceDetails.setNetworkType("WIFI");
        updatePriceRequest.setDeviceDetails(deviceDetails);

        // Setup FeatureFlags
        featureFlags = new com.mmt.hotels.clientgateway.request.FeatureFlags();
        featureFlags.setWalletRequired(true);
        featureFlags.setCoupon(true);
        featureFlags.setComparator(true);
        featureFlags.setCheckAvailability(true);
        updatePriceRequest.setFeatureFlags(featureFlags);

        // Setup CommonModifierResponse
        commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setMcId("MC123");
        commonModifierResponse.setMmtAuth("AUTH123");
        commonModifierResponse.setOriginalTrafficSource("DIRECT");
        
        // Setup data maps using Lombok generated setters
        Map<String, String> manthanMap = new HashMap<>();
        manthanMap.put("manthan1", "value1");
        commonModifierResponse.setManthanExpDataMap(manthanMap);
        
        Map<String, String> contentMap = new HashMap<>();
        contentMap.put("content1", "value1");
        commonModifierResponse.setContentExpDataMap(contentMap);

        // Setup ExtendedUser
        extendedUser = new ExtendedUser();
        extendedUser.setUuid("USER123");
        extendedUser.setProfileType("PERSONAL");
        commonModifierResponse.setExtendedUser(extendedUser);
    }

    // ==================== PUBLIC METHOD TESTS ====================

    @Test
    public void should_BuildDetailRequest_When_ValidInput() {
        // When
        DetailRequest result = transformer.buildUpdatePriceRequest(updatePriceRequest, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertEquals("12345", result.getHotelId());
        assertEquals("2024-01-01", result.getCheckIn());
        assertEquals("2024-01-02", result.getCheckOut());
        assertEquals("{\"test\":\"data\"}", result.getExperimentData());
        assertNotNull(result.getLocation());
        assertNotNull(result.getRooms());
        assertNotNull(result.getClientDetails());
        assertNotNull(result.getFilters());
        assertNotNull(result.getImageDetails());
        assertNotNull(result.getExtraInfo());
        assertEquals(2, result.getCouponCount());
    }

    @Test(expected = IllegalArgumentException.class)
    public void should_ThrowException_When_UpdatePriceRequestIsNull() {
        transformer.buildUpdatePriceRequest(null, commonModifierResponse);
    }

    @Test(expected = IllegalArgumentException.class)
    public void should_ThrowException_When_CommonModifierResponseIsNull() {
        transformer.buildUpdatePriceRequest(updatePriceRequest, null);
    }

    @Test(expected = IllegalArgumentException.class)
    public void should_ThrowException_When_SearchCriteriaIsNull() {
        updatePriceRequest.setSearchCriteria(null);
        transformer.buildUpdatePriceRequest(updatePriceRequest, commonModifierResponse);
    }

    @Test
    public void should_HandleNullRequestDetails_When_BuildingRequest() {
        updatePriceRequest.setRequestDetails(null);
        DetailRequest result = transformer.buildUpdatePriceRequest(updatePriceRequest, commonModifierResponse);
        assertNotNull(result);
        assertEquals(0, result.getCouponCount());
    }

    // ==================== PRIVATE METHOD TESTS USING REFLECTION ====================

    @Test
    public void should_BuildLocationDetails_When_ValidSearchCriteria() {
        // When
        LocationDetails result = ReflectionTestUtils.invokeMethod(transformer, "buildUpdatePriceLocationDetails", updatePriceRequest);

        // Then
        assertNotNull(result);
        assertEquals("DEL", result.getCityId());
        assertEquals("IN", result.getCountryId());
        assertEquals("LOC123", result.getId());
//        assertEquals("CITY", result.getType());
    }

    @Test
    public void should_HandleNullSearchCriteria_When_BuildingLocationDetails() {
        // Given
        updatePriceRequest.setSearchCriteria(null);

        // When
        LocationDetails result = ReflectionTestUtils.invokeMethod(transformer, "buildUpdatePriceLocationDetails", updatePriceRequest);

        // Then
        assertNotNull(result);
        assertNull(result.getCityId());
        assertNull(result.getCountryId());
        assertNull(result.getId());
        assertNull(result.getType());
    }

    @Test
    public void should_BuildExtraInfo_When_SearchTypeProvided() {
        // When
        ExtraInfo result = ReflectionTestUtils.invokeMethod(transformer, "buildExtraInfo", "NORMAL");

        // Then
        assertNotNull(result);
        assertEquals("NORMAL", result.getSearchType());
    }

    @Test
    public void should_BuildExtraInfo_When_SearchTypeIsNull() {
        // When
        ExtraInfo result = ReflectionTestUtils.invokeMethod(transformer, "buildExtraInfo", (String) null);

        // Then
        assertNotNull(result);
        assertNull(result.getSearchType());
    }

    @Test
    public void should_BuildExtraInfo_When_SearchTypeIsEmpty() {
        // When
        ExtraInfo result = ReflectionTestUtils.invokeMethod(transformer, "buildExtraInfo", "");

        // Then
        assertNotNull(result);
        assertNull(result.getSearchType());
    }

    @Test
    public void should_BuildClientDetails_When_ValidInput() {
        // When
        ClientDetails result = ReflectionTestUtils.invokeMethod(transformer, "buildClientDetailsForUpdatePrice", updatePriceRequest, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFeatureFlags());
        assertNotNull(result.getRequestDetails());
        assertNotNull(result.getUserDetails());
        assertEquals("VISITOR123", result.getVisitorId());
        assertEquals("MC123", result.getMcId());
        assertNotNull(result.getChatbotDetails());
    }

    @Test
    public void should_HandleNullRequestDetails_When_BuildingClientDetails() {
        // Given
        updatePriceRequest.setRequestDetails(null);

        // When
        ClientDetails result = ReflectionTestUtils.invokeMethod(transformer, "buildClientDetailsForUpdatePrice", updatePriceRequest, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertEquals("", result.getVisitorId());
        assertNull(result.getChatbotDetails());
    }

    @Test
    public void should_ReturnEmptyList_When_BuildingFilterDetails() {
        // When
        List<FilterDetails> result = ReflectionTestUtils.invokeMethod(transformer, "buildUpdatePriceFilterDetails", updatePriceRequest);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_ReturnEmptyList_When_UpdatePriceRequestIsNull_ForFilterDetails() {
        // When
        List<FilterDetails> result = ReflectionTestUtils.invokeMethod(transformer, "buildUpdatePriceFilterDetails", (UpdatePriceRequest) null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_BuildImageDetails_When_Called() {
        // When
        com.gommt.hotels.orchestrator.detail.model.state.ImageDetails result = 
            ReflectionTestUtils.invokeMethod(transformer, "buildUpdatePriceImageDetails", updatePriceRequest);

        // Then
        assertNotNull(result);
        assertNotNull(result.getCategories());
        assertNotNull(result.getTypes());
        assertTrue(result.getCategories().isEmpty());
        assertTrue(result.getTypes().isEmpty());
    }

    @Test
    public void should_BuildFeatureFlags_When_ValidFeatureFlags() {
        // When
        com.gommt.hotels.orchestrator.detail.model.state.FeatureFlags result = 
            ReflectionTestUtils.invokeMethod(transformer, "buildFeatureFlagsForUpdatePrice", updatePriceRequest, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertTrue(result.isWalletRequired());
        assertTrue(result.isBestCoupon());
        assertTrue(result.isComparatorHotelRequest());
        assertTrue(result.isCheckAvailability());
        assertFalse(result.isBookingModification());
    }

    @Test
    public void should_BuildFeatureFlags_When_FeatureFlagsIsNull() {
        // Given
        updatePriceRequest.setFeatureFlags(null);

        // When
        com.gommt.hotels.orchestrator.detail.model.state.FeatureFlags result = 
            ReflectionTestUtils.invokeMethod(transformer, "buildFeatureFlagsForUpdatePrice", updatePriceRequest, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertFalse(result.isWalletRequired());
        assertFalse(result.isBestCoupon());
        assertFalse(result.isComparatorHotelRequest());
        assertFalse(result.isCheckAvailability());
        assertFalse(result.isBookingModification());
    }

    @Test
    public void should_BuildRequestDetails_When_ValidInput() {
        // When
        com.gommt.hotels.orchestrator.detail.model.state.RequestDetails result = 
            ReflectionTestUtils.invokeMethod(transformer, "buildRequestDetailsForUpdatePrice", updatePriceRequest, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertNotNull(result.getBookingDevice());
        assertEquals("B2CAgent", result.getRequestType());
        assertEquals(Currency.INR, result.getCurrency());
        assertEquals("CC", result.getPayMode());
        assertNotNull(result.getMultiCurrencyInfo());
        assertNotNull(result.getUserGlobalInfo());
        assertEquals("TEST", result.getRequestor());
        assertEquals("REQ123", result.getRequestId());
        assertEquals("JOURNEY123", result.getJourneyId());
        assertEquals(Funnel.HOTELS, result.getFunnelSource());
        assertEquals(PageContext.DETAIL, result.getPageContext());
        assertEquals("VISITOR123", result.getVisitorId());
        assertEquals("DIRECT", result.getOriginalTrafficSource());
        assertEquals(TrafficType.B2C, result.getTrafficType());
        assertEquals("WEB", result.getChannel());
        assertEquals("DIRECT", result.getTrafficSource());
        assertEquals("NORMAL", result.getTrafficFlowType());
        assertEquals(Brand.MMT, result.getBrand());
        assertEquals(IdContext.B2C, result.getIdContext());
        assertEquals(SiteDomain.IN, result.getSiteDomain());
        assertEquals(Language.ENGLISH, result.getLanguage());
        assertEquals(Region.IN, result.getRegion());
        assertNotNull(result.getRoomCriteria());
    }

    @Test
    public void should_HandleNullUpdatePriceRequest_When_BuildingRequestDetails() {
        // When
        com.gommt.hotels.orchestrator.detail.model.state.RequestDetails result = 
            ReflectionTestUtils.invokeMethod(transformer, "buildRequestDetailsForUpdatePrice", null, commonModifierResponse);

        // Then
        assertNotNull(result);
//        assertEquals("B2CAgent", result.getRequestType());
//        assertEquals(Currency.INR, result.getCurrency());
    }

    @Test
    public void should_HandleNullSearchCriteria_When_BuildingRequestDetails() {
        // Given
        updatePriceRequest.setSearchCriteria(null);

        // When
        com.gommt.hotels.orchestrator.detail.model.state.RequestDetails result = 
            ReflectionTestUtils.invokeMethod(transformer, "buildRequestDetailsForUpdatePrice", updatePriceRequest, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertEquals(Currency.INR, result.getCurrency());
        assertNull(result.getMultiCurrencyInfo());
        assertNull(result.getUserGlobalInfo());
        assertNull(result.getRoomCriteria());
    }

    @Test
    public void should_HandleNullRequestDetails_When_BuildingRequestDetails() {
        // Given
        updatePriceRequest.setRequestDetails(null);

        // When
        com.gommt.hotels.orchestrator.detail.model.state.RequestDetails result = 
            ReflectionTestUtils.invokeMethod(transformer, "buildRequestDetailsForUpdatePrice", updatePriceRequest, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertNotNull(result.getRequestId()); // Should generate UUID
        assertEquals("", result.getJourneyId());
        assertEquals(Funnel.HOTELS, result.getFunnelSource());
        assertEquals(PageContext.DETAIL, result.getPageContext());
        assertEquals("", result.getVisitorId());
        assertEquals(TrafficType.B2C, result.getTrafficType());
        assertEquals("", result.getChannel());
        assertEquals("", result.getTrafficSource());
        assertEquals(Brand.MMT, result.getBrand());
        assertEquals(IdContext.B2C, result.getIdContext());
        assertEquals(SiteDomain.IN, result.getSiteDomain());
    }

    @Test
    public void should_SetGCCTrafficType_When_SiteDomainIsAE() {
        // Given
        requestDetails.setSiteDomain("AE");

        // When
        com.gommt.hotels.orchestrator.detail.model.state.RequestDetails result = 
            ReflectionTestUtils.invokeMethod(transformer, "buildRequestDetailsForUpdatePrice", updatePriceRequest, commonModifierResponse);

        // Then
        assertEquals(TrafficType.GCC, result.getTrafficType());
    }

    @Test
    public void should_SetMyPartnerTrafficType_When_IsMyPartner() {
        // Given
        when(utility.isMyPartner(commonModifierResponse)).thenReturn(true);

        // When
        com.gommt.hotels.orchestrator.detail.model.state.RequestDetails result = 
            ReflectionTestUtils.invokeMethod(transformer, "buildRequestDetailsForUpdatePrice", updatePriceRequest, commonModifierResponse);

        // Then
        assertEquals(TrafficType.MYPARTNER, result.getTrafficType());
    }

    @Test
    public void should_HandleInvalidCurrency_When_BuildingRequestDetails() {
        // Given
        searchCriteria.setCurrency("INR");

        // When
        com.gommt.hotels.orchestrator.detail.model.state.RequestDetails result = 
            ReflectionTestUtils.invokeMethod(transformer, "buildRequestDetailsForUpdatePrice", updatePriceRequest, commonModifierResponse);

        // Then
        assertEquals(Currency.INR, result.getCurrency());
    }

    @Test
    public void should_BuildUserDetails_When_ValidExtendedUser() {
        // When
        UserDetails result = ReflectionTestUtils.invokeMethod(transformer, "buildUserDetailsForUpdatePrice", updatePriceRequest, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertEquals("USER123", result.getUuid());
        assertTrue(result.isLoggedIn());
        assertEquals(ProfileType.PERSONAL, result.getProfileType());
        assertEquals("AUTH123", result.getMmtAuth());
    }

    @Test
    public void should_HandleNullExtendedUser_When_BuildingUserDetails() {
        // Given
        commonModifierResponse.setExtendedUser(null);

        // When
        UserDetails result = ReflectionTestUtils.invokeMethod(transformer, "buildUserDetailsForUpdatePrice", updatePriceRequest, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertNull(result.getUuid());
        assertFalse(result.isLoggedIn());
        assertNull(result.getProfileType());
        assertEquals("AUTH123", result.getMmtAuth());
    }

    @Test
    public void should_HandleNullRequestDetails_When_BuildingUserDetails() {
        // Given
        updatePriceRequest.setRequestDetails(null);

        // When
        UserDetails result = ReflectionTestUtils.invokeMethod(transformer, "buildUserDetailsForUpdatePrice", updatePriceRequest, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertFalse(result.isLoggedIn());
    }

    @Test
    public void should_HandleNullProfileType_When_BuildingUserDetails() {
        // Given
        extendedUser.setProfileType(null);

        // When
        UserDetails result = ReflectionTestUtils.invokeMethod(transformer, "buildUserDetailsForUpdatePrice", updatePriceRequest, commonModifierResponse);

        // Then
        assertNotNull(result);
        assertNull(result.getProfileType());
    }

    @Test
    public void should_BuildChatbotDetails_When_MyraMsgIdProvided() {
        // When
        ChatbotDetails result = ReflectionTestUtils.invokeMethod(transformer, "buildChatbotDetailsForUpdatePrice", updatePriceRequest);

        // Then
        assertNotNull(result);
        assertEquals("MYRA123", result.getMyraMsgId());
    }

    @Test
    public void should_ReturnNull_When_RequestDetailsIsNull() {
        // Given
        updatePriceRequest.setRequestDetails(null);

        // When
        ChatbotDetails result = ReflectionTestUtils.invokeMethod(transformer, "buildChatbotDetailsForUpdatePrice", updatePriceRequest);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_MyraMsgIdIsEmpty() {
        // Given
        requestDetails.setMyraMsgId("");

        // When
        ChatbotDetails result = ReflectionTestUtils.invokeMethod(transformer, "buildChatbotDetailsForUpdatePrice", updatePriceRequest);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_MyraMsgIdIsNull() {
        // Given
        requestDetails.setMyraMsgId(null);

        // When
        ChatbotDetails result = ReflectionTestUtils.invokeMethod(transformer, "buildChatbotDetailsForUpdatePrice", updatePriceRequest);

        // Then
        assertNull(result);
    }

    @Test
    public void should_BuildBookingDevice_When_ValidDeviceDetails() {
        // When
        BookingDevice result = ReflectionTestUtils.invokeMethod(transformer, "buildBookingDevice", deviceDetails);

        // Then
        assertNotNull(result);
        assertEquals("DEVICE123", result.getDeviceId());
        assertEquals("iPhone 12", result.getDeviceName());
        assertEquals(DeviceType.MOBILE, result.getDeviceType());
        assertEquals("1.0.0", result.getAppVersion());
        assertEquals("WIFI", result.getNetworkType());
    }

    @Test
    public void should_BuildEmptyBookingDevice_When_DeviceDetailsIsNull() {
        // When
        BookingDevice result = ReflectionTestUtils.invokeMethod(transformer, "buildBookingDevice", (DeviceDetails) null);

        // Then
        assertNotNull(result);
//        assertEquals("", result.getDeviceId());
//        assertEquals("", result.getDeviceName());
//        assertEquals(DeviceType.DESKTOP, result.getDeviceType());
//        assertEquals("", result.getAppVersion());
//        assertEquals("", result.getNetworkType());
    }

    @Test
    public void should_HandleNullFields_When_BuildingBookingDevice() {
        // Given
        DeviceDetails nullFieldsDevice = new DeviceDetails();
        nullFieldsDevice.setBookingDevice("Desktop");

        // When
        BookingDevice result = ReflectionTestUtils.invokeMethod(transformer, "buildBookingDevice", nullFieldsDevice);

        // Then
        assertNotNull(result);
        assertEquals("", result.getDeviceId());
        assertEquals("", result.getDeviceName());
        assertEquals(DeviceType.DESKTOP, result.getDeviceType());
        assertEquals("", result.getAppVersion());
        assertEquals("", result.getNetworkType());
    }

    @Test
    public void should_ConvertRoomCriteria_When_ValidInput() {
        // Given
        List<UpdatedPriceRoomCriteria> roomCriteria = searchCriteria.getRoomCriteria();

        // When
        List<RoomCriteria> result = ReflectionTestUtils.invokeMethod(transformer, "convertToOrchRoomCriteria", roomCriteria, updatePriceRequest);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        RoomCriteria orchCriteria = result.get(0);
        assertEquals("ROOM001", orchCriteria.getRoomCode());
        assertEquals("RATE001", orchCriteria.getRatePlanCode());
        assertEquals("MT123", orchCriteria.getMtKey());
        assertEquals("PRICE123", orchCriteria.getPricingKey());
//        assertEquals(1, orchCriteria.getCount());
        assertEquals("SUP001", orchCriteria.getSupplierCode());
        assertNotNull(orchCriteria.getRooms());
    }

    @Test
    public void should_ReturnNull_When_RoomCriteriaIsNull() {
        // When
        List<RoomCriteria> result = ReflectionTestUtils.invokeMethod(transformer, "convertToOrchRoomCriteria", null, updatePriceRequest);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_RoomCriteriaIsEmpty() {
        // When
        List<RoomCriteria> result = ReflectionTestUtils.invokeMethod(transformer, "convertToOrchRoomCriteria", Collections.emptyList(), updatePriceRequest);

        // Then
        assertNull(result);
    }

    @Test
    public void should_HandleNullRoomStayCandidates_When_ConvertingRoomCriteria() {
        // Given
        UpdatedPriceRoomCriteria roomCriteria = new UpdatedPriceRoomCriteria();
        roomCriteria.setRoomCode("ROOM001");
        roomCriteria.setRoomStayCandidates(null);

        // When
        List<RoomCriteria> result = ReflectionTestUtils.invokeMethod(transformer, "convertToOrchRoomCriteria", Arrays.asList(roomCriteria), updatePriceRequest);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
//        assertEquals(1, result.get(0).getCount()); // Default count when null
    }

    @Test
    public void should_BuildRoomDetails_When_ValidRoomStayCandidates() {
        // Given
        List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> candidates = Arrays.asList(
            createRoomStayCandidate(2, Arrays.asList(5, 10)),
            createRoomStayCandidate(1, Collections.emptyList())
        );

        // When
        List<com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails> result = 
            ReflectionTestUtils.invokeMethod(transformer, "buildRoomDetails", candidates, updatePriceRequest.getExpDataMap());

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
//        assertEquals(2, result.get(0).getAdults());
        assertEquals(Arrays.asList(5, 10), result.get(0).getChildrenAges());
//        assertEquals(1, result.get(1).getAdults());
        assertTrue(result.get(1).getChildrenAges().isEmpty());
    }

    @Test
    public void should_ReturnEmptyList_When_RoomStayCandidatesIsNull() {
        // When
        List<com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails> result = 
            ReflectionTestUtils.invokeMethod(transformer, "buildRoomDetails", null, updatePriceRequest.getExpDataMap());

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_ReturnEmptyList_When_RoomStayCandidatesIsEmpty() {
        // When
        List<com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails> result = 
            ReflectionTestUtils.invokeMethod(transformer, "buildRoomDetails", Collections.emptyList(), updatePriceRequest.getExpDataMap());

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_SkipNullCandidates_When_BuildingRoomDetails() {
        // Given
        List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> candidates = Arrays.asList(
            createRoomStayCandidate(2, Arrays.asList(5, 10)),
            null,
            createRoomStayCandidate(1, Collections.emptyList())
        );

        // When
        List<com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails> result = 
            ReflectionTestUtils.invokeMethod(transformer, "buildRoomDetails", candidates, updatePriceRequest.getExpDataMap());

        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // Null candidate should be skipped
    }

    @Test
    public void should_UseDistributedRoomStay_When_DistributeRoomStayCandidatesIsTrue() {
        // Given
        List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> candidates = Arrays.asList(createRoomStayCandidate(2, Arrays.asList(5, 10)));
        when(utility.isDistributeRoomStayCandidates(candidates, updatePriceRequest.getExpDataMap())).thenReturn(true);
        
        // Setup distributed room stay candidates
        com.mmt.hotels.model.request.RoomStayCandidate distributedCandidate = new com.mmt.hotels.model.request.RoomStayCandidate();
        GuestCount guestCount1 = new GuestCount();
        guestCount1.setCount("2");
        guestCount1.setAges(Arrays.asList(25, 30));
        GuestCount guestCount2 = new GuestCount();
        guestCount2.setCount("1");
        guestCount2.setAges(Arrays.asList(5));
        distributedCandidate.setGuestCounts(Arrays.asList(guestCount1, guestCount2));
        
        when(utility.buildRoomStayDistribution(candidates, updatePriceRequest.getExpDataMap()))
            .thenReturn(Arrays.asList(distributedCandidate));

        // When
        List<com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails> result = 
            ReflectionTestUtils.invokeMethod(transformer, "buildRoomDetails", candidates, updatePriceRequest.getExpDataMap());

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
//        assertEquals(3, result.get(0).getAdults()); // 2 + 1
        assertEquals(Arrays.asList(25, 30, 5), result.get(0).getChildrenAges());
    }

    @Test
    public void should_BuildMultiCurrencyInfo_When_ValidInput() {
        // Given
        com.mmt.hotels.clientgateway.request.MultiCurrencyInfo input = new com.mmt.hotels.clientgateway.request.MultiCurrencyInfo();
        input.setUserCurrency("USD");
        input.setRegionCurrency("INR");

        // When
        com.gommt.hotels.orchestrator.detail.model.objects.MultiCurrencyInfo result = 
            ReflectionTestUtils.invokeMethod(transformer, "buildMultiCurrencyInfo", input);

        // Then
        assertNotNull(result);
        assertEquals("USD", result.getUserCurrency());
        assertEquals("INR", result.getRegionCurrency());
    }

    @Test
    public void should_ReturnNull_When_MultiCurrencyInfoIsNull() {
        // When
        com.gommt.hotels.orchestrator.detail.model.objects.MultiCurrencyInfo result = 
            ReflectionTestUtils.invokeMethod(transformer, "buildMultiCurrencyInfo", (com.mmt.hotels.clientgateway.request.MultiCurrencyInfo) null);

        // Then
        assertNull(result);
    }

    @Test
    public void should_BuildUserGlobalInfo_When_ValidInput() {
        // Given
        com.mmt.hotels.clientgateway.request.UserGlobalInfo input = new com.mmt.hotels.clientgateway.request.UserGlobalInfo();
        input.setUserCountry("IN");
        input.setEntityName("MMT");

        // When
        com.gommt.hotels.orchestrator.detail.model.objects.UserGlobalInfo result = 
            ReflectionTestUtils.invokeMethod(transformer, "buildUserGlobalInfo", input);

        // Then
        assertNotNull(result);
        assertEquals("IN", result.getUserCountry());
        assertEquals("MMT", result.getEntityName());
    }

    // ==================== HELPER METHODS ====================

    private com.mmt.hotels.clientgateway.request.RoomStayCandidate createRoomStayCandidate(int adultCount, List<Integer> childAges) {
        com.mmt.hotels.clientgateway.request.RoomStayCandidate candidate = new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        candidate.setAdultCount(adultCount);
        candidate.setChildAges(childAges);
        return candidate;
    }
} 