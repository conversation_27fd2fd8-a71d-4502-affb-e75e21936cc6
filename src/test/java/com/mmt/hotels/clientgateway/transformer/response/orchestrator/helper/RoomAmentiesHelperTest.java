package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityAttribute;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.AttributesFacility;
import com.mmt.hotels.clientgateway.response.Facility;
import com.mmt.hotels.clientgateway.response.FacilityGroup;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class RoomAmentiesHelperTest {

    @Mock
    private PolyglotService polyglotService;

    @InjectMocks
    private RoomAmentiesHelper roomAmentiesHelper;

    // Test constants
    private static final String STAR_FACILITIES_TRANSLATED = "Star Facilities";
    private static final String POPULAR_WITH_GUESTS = "Popular with guests";
    private static final String WIFI_GROUP = "WiFi";
    private static final String PARKING_GROUP = "Parking";

    @Before
    public void setUp() {
        // Setup polyglot service responses
        when(polyglotService.getTranslatedData(ConstantsTranslation.STAR_FACILITIES))
                .thenReturn(STAR_FACILITIES_TRANSLATED);
    }

    // buildAmenities Tests

    @Test
    public void should_ReturnNull_When_RoomInfoIsNull() {
        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(null, false);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_AmenitiesIsNull() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setAmenities(null);

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(roomInfo, false);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_AmenitiesIsEmpty() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setAmenities(new ArrayList<>());

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(roomInfo, false);

        // Then
        assertNull(result);
    }

    @Test
    public void should_BuildAmenities_When_ValidAmenitiesProvided() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithAmenities();

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(roomInfo, false);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        FacilityGroup wifiGroup = result.get(0);
        assertEquals(WIFI_GROUP, wifiGroup.getName());
        assertNotNull(wifiGroup.getFacilities());
        assertEquals(1, wifiGroup.getFacilities().size());
        
        Facility wifiFacility = wifiGroup.getFacilities().get(0);
        assertEquals("Free WiFi", wifiFacility.getName());
        assertEquals("WIFI", wifiFacility.getAttributeName());
        assertEquals("Connectivity", wifiFacility.getCategoryName());
    }

    @Test
    public void should_BuildStarFacilities_When_HighlightedAmenitiesProvided() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithHighlightedAmenities();

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(roomInfo, false);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // First group should be star facilities
        FacilityGroup starGroup = result.get(0);
        assertEquals(STAR_FACILITIES_TRANSLATED, starGroup.getName());
        assertEquals(Constants.BOLD_TYPE, starGroup.getType());
        assertNotNull(starGroup.getFacilities());
        assertEquals(1, starGroup.getFacilities().size());
        
        Facility starFacility = starGroup.getFacilities().get(0);
        assertEquals("Premium WiFi", starFacility.getName());
        
        verify(polyglotService).getTranslatedData(ConstantsTranslation.STAR_FACILITIES);
    }

    @Test
    public void should_SortStarFacilitiesBySequence_When_MultipleStarFacilities() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithMultipleHighlightedAmenities();

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(roomInfo, false);

        // Then
        assertNotNull(result);
        FacilityGroup starGroup = result.get(0);
        assertEquals(STAR_FACILITIES_TRANSLATED, starGroup.getName());
        
        List<Facility> starFacilities = starGroup.getFacilities();
        assertEquals(2, starFacilities.size());
        
        // Should be sorted by sequence (1, 2)
        assertEquals(Integer.valueOf(1), starFacilities.get(0).getSequence());
        assertEquals(Integer.valueOf(2), starFacilities.get(1).getSequence());
    }

    @Test
    public void should_HandleChildAttributes_When_AmenityHasChildAttributes() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithChildAttributes();

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(roomInfo, false);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        FacilityGroup group = result.get(0);
        Facility facility = group.getFacilities().get(0);
        assertNotNull(facility.getChildAttributes());
        assertEquals(2, facility.getChildAttributes().size());
    }

    @Test
    public void should_SkipPopularWithGuests_When_AmendRoomHighlightsAndNotAvailable() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithPopularWithGuestsOnly();

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(roomInfo, true);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_UseHighlightedAmenities_When_PopularWithGuestsAvailableAndAmendRoomHighlights() {
        // Given
        RoomInfo roomInfo = createRoomInfoWithPopularWithGuestsInHighlighted();

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildAmenities(roomInfo, true);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        // The highlighted amenity gets moved to star facilities since it's in highlighted amenities
        // The Popular with guests group becomes empty and gets filtered out
        FacilityGroup starGroup = result.get(0);
        assertEquals(STAR_FACILITIES_TRANSLATED, starGroup.getName());
        assertEquals(Constants.BOLD_TYPE, starGroup.getType());
        assertEquals(1, starGroup.getFacilities().size());
        assertEquals("Highlighted Popular Amenity", starGroup.getFacilities().get(0).getName());
        
        verify(polyglotService).getTranslatedData(ConstantsTranslation.STAR_FACILITIES);
    }

    // buildHighlightedAmenities Tests

    @Test
    public void should_ReturnNull_When_RoomInfoIsNullForHighlighted() {
        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildHighlightedAmenities(null);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_HighlightedAmenitiesIsNull() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setHighlightedAmenities(null);

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildHighlightedAmenities(roomInfo);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_HighlightedAmenitiesIsEmpty() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setHighlightedAmenities(new ArrayList<>());

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildHighlightedAmenities(roomInfo);

        // Then
        assertNull(result);
    }

    @Test
    public void should_BuildHighlightedAmenities_When_ValidHighlightedAmenitiesProvided() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setHighlightedAmenities(createHighlightedAmenityGroups());

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildHighlightedAmenities(roomInfo);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        FacilityGroup group = result.get(0);
        assertEquals("Premium Features", group.getName());
        assertNotNull(group.getFacilities());
        assertEquals(2, group.getFacilities().size());
        
        Facility firstFacility = group.getFacilities().get(0);
        assertEquals("Premium WiFi", firstFacility.getName());
        assertEquals("PREMIUM_WIFI", firstFacility.getAttributeName());
    }

    @Test
    public void should_HandleChildAttributesInHighlighted_When_AmenityHasChildAttributes() {
        // Given
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setHighlightedAmenities(createHighlightedAmenityGroupsWithChildAttributes());

        // When
        List<FacilityGroup> result = roomAmentiesHelper.buildHighlightedAmenities(roomInfo);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        FacilityGroup group = result.get(0);
        Facility facility = group.getFacilities().get(0);
        assertNotNull(facility.getChildAttributes());
        assertEquals(1, facility.getChildAttributes().size());
        
        AttributesFacility childAttribute = facility.getChildAttributes().get(0);
        assertNotNull(childAttribute);
    }

    // buildFacilityHighlights Tests

    @Test
    public void should_ReturnEmptyList_When_HighlightedAmenitiesIsNull() {
        // When
        List<String> result = roomAmentiesHelper.buildFacilityHighlights(null);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnEmptyList_When_HighlightedAmenitiesIsEmpty() {
        // Given
        List<AmenityGroup> highlightedAmenities = new ArrayList<>();

        // When
        List<String> result = roomAmentiesHelper.buildFacilityHighlights(highlightedAmenities);

        // Then
        assertNull(result);
    }

    @Test
    public void should_BuildFacilityHighlights_When_ValidHighlightedAmenitiesProvided() {
        // Given
        List<AmenityGroup> highlightedAmenities = createHighlightedAmenityGroups();

        // When
        List<String> result = roomAmentiesHelper.buildFacilityHighlights(highlightedAmenities);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Premium Features", result.get(0));
    }

    @Test
    public void should_PreserveDuplicates_When_MultipleGroupsWithSameName() {
        // Given
        List<AmenityGroup> highlightedAmenities = createDuplicateAmenityGroups();

        // When
        List<String> result = roomAmentiesHelper.buildFacilityHighlights(highlightedAmenities);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // LinkedHashSet should preserve unique names
        assertEquals("WiFi", result.get(0));
        assertEquals("Parking", result.get(1));
    }

    @Test
    public void should_PreserveOrder_When_MultipleGroupsProvided() {
        // Given
        List<AmenityGroup> highlightedAmenities = createMultipleAmenityGroups();

        // When
        List<String> result = roomAmentiesHelper.buildFacilityHighlights(highlightedAmenities);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("First Group", result.get(0));
        assertEquals("Second Group", result.get(1));
        assertEquals("Third Group", result.get(2));
    }

    // Helper methods for creating test data

    private RoomInfo createRoomInfoWithAmenities() {
        RoomInfo roomInfo = new RoomInfo();
        
        List<AmenityGroup> amenityGroups = new ArrayList<>();
        
        // WiFi group
        AmenityGroup wifiGroup = new AmenityGroup();
        wifiGroup.setName(WIFI_GROUP);
        
        Amenity wifiAmenity = new Amenity();
        wifiAmenity.setName("Free WiFi");
        wifiAmenity.setAttributeName("WIFI");
        wifiAmenity.setCategoryName("Connectivity");
        wifiAmenity.setDisplayType("text");
        wifiAmenity.setSequence(1);
        
        wifiGroup.setAmenities(Arrays.asList(wifiAmenity));
        amenityGroups.add(wifiGroup);
        
        // Parking group
        AmenityGroup parkingGroup = new AmenityGroup();
        parkingGroup.setName(PARKING_GROUP);
        
        Amenity parkingAmenity = new Amenity();
        parkingAmenity.setName("Free Parking");
        parkingAmenity.setAttributeName("PARKING");
        parkingAmenity.setCategoryName("Transportation");
        parkingAmenity.setDisplayType("text");
        parkingAmenity.setSequence(2);
        
        parkingGroup.setAmenities(Arrays.asList(parkingAmenity));
        amenityGroups.add(parkingGroup);
        
        roomInfo.setAmenities(amenityGroups);
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithHighlightedAmenities() {
        RoomInfo roomInfo = createRoomInfoWithAmenities();
        
        // Create highlighted amenities
        List<AmenityGroup> highlightedGroups = new ArrayList<>();
        AmenityGroup highlightedGroup = new AmenityGroup();
        highlightedGroup.setName("Premium Features");
        
        Amenity premiumWifi = new Amenity();
        premiumWifi.setName("Premium WiFi");
        premiumWifi.setAttributeName("PREMIUM_WIFI");
        premiumWifi.setCategoryName("Connectivity");
        premiumWifi.setSequence(1);
        
        highlightedGroup.setAmenities(Arrays.asList(premiumWifi));
        highlightedGroups.add(highlightedGroup);
        
        roomInfo.setHighlightedAmenities(highlightedGroups);
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithMultipleHighlightedAmenities() {
        RoomInfo roomInfo = createRoomInfoWithAmenities();
        
        // Create multiple highlighted amenities with different sequences
        List<AmenityGroup> highlightedGroups = new ArrayList<>();
        AmenityGroup highlightedGroup = new AmenityGroup();
        highlightedGroup.setName("Premium Features");
        
        Amenity amenity1 = new Amenity();
        amenity1.setName("Second Amenity");
        amenity1.setAttributeName("SECOND");
        amenity1.setSequence(2);
        
        Amenity amenity2 = new Amenity();
        amenity2.setName("First Amenity");
        amenity2.setAttributeName("FIRST");
        amenity2.setSequence(1);
        
        highlightedGroup.setAmenities(Arrays.asList(amenity1, amenity2));
        highlightedGroups.add(highlightedGroup);
        
        roomInfo.setHighlightedAmenities(highlightedGroups);
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithChildAttributes() {
        RoomInfo roomInfo = new RoomInfo();
        
        List<AmenityGroup> amenityGroups = new ArrayList<>();
        AmenityGroup group = new AmenityGroup();
        group.setName("Complex Amenities");
        
        Amenity amenity = new Amenity();
        amenity.setName("Complex Amenity");
        amenity.setAttributeName("COMPLEX");
        
        // Create child attributes
        List<AmenityAttribute> childAttributes = new ArrayList<>();
        
        AmenityAttribute child1 = new AmenityAttribute();
        child1.setName("Child Attribute 1");
        childAttributes.add(child1);
        
        AmenityAttribute child2 = new AmenityAttribute();
        child2.setName("Child Attribute 2");
        childAttributes.add(child2);
        
        amenity.setChildAttributes(childAttributes);
        group.setAmenities(Arrays.asList(amenity));
        amenityGroups.add(group);
        
        roomInfo.setAmenities(amenityGroups);
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithPopularWithGuestsOnly() {
        RoomInfo roomInfo = new RoomInfo();
        
        List<AmenityGroup> amenityGroups = new ArrayList<>();
        AmenityGroup popularGroup = new AmenityGroup();
        popularGroup.setName(POPULAR_WITH_GUESTS);
        
        Amenity amenity = new Amenity();
        amenity.setName("Popular Amenity");
        amenity.setAttributeName("POPULAR");
        
        popularGroup.setAmenities(Arrays.asList(amenity));
        amenityGroups.add(popularGroup);
        
        roomInfo.setAmenities(amenityGroups);
        // No highlighted amenities, so Popular with guests should be skipped when amendRoomHighlights=true
        return roomInfo;
    }

    private RoomInfo createRoomInfoWithPopularWithGuestsInHighlighted() {
        RoomInfo roomInfo = new RoomInfo();
        
        // Regular amenities with Popular with guests
        List<AmenityGroup> amenityGroups = new ArrayList<>();
        AmenityGroup popularGroup = new AmenityGroup();
        popularGroup.setName(POPULAR_WITH_GUESTS);
        
        Amenity regularAmenity = new Amenity();
        regularAmenity.setName("Regular Popular Amenity");
        regularAmenity.setAttributeName("REGULAR_POPULAR");
        
        popularGroup.setAmenities(Arrays.asList(regularAmenity));
        amenityGroups.add(popularGroup);
        roomInfo.setAmenities(amenityGroups);
        
        // Highlighted amenities with Popular with guests (different amenity that won't be in star facilities)
        List<AmenityGroup> highlightedGroups = new ArrayList<>();
        AmenityGroup highlightedPopularGroup = new AmenityGroup();
        highlightedPopularGroup.setName(POPULAR_WITH_GUESTS);
        
        Amenity highlightedAmenity = new Amenity();
        highlightedAmenity.setName("Highlighted Popular Amenity");
        highlightedAmenity.setAttributeName("HIGHLIGHTED_POPULAR");
        
        highlightedPopularGroup.setAmenities(Arrays.asList(highlightedAmenity));
        highlightedGroups.add(highlightedPopularGroup);
        
        roomInfo.setHighlightedAmenities(highlightedGroups);
        return roomInfo;
    }

    private List<AmenityGroup> createHighlightedAmenityGroups() {
        List<AmenityGroup> groups = new ArrayList<>();
        AmenityGroup group = new AmenityGroup();
        group.setName("Premium Features");
        
        List<Amenity> amenities = new ArrayList<>();
        
        Amenity amenity1 = new Amenity();
        amenity1.setName("Premium WiFi");
        amenity1.setAttributeName("PREMIUM_WIFI");
        amenity1.setCategoryName("Connectivity");
        amenities.add(amenity1);
        
        Amenity amenity2 = new Amenity();
        amenity2.setName("Concierge Service");
        amenity2.setAttributeName("CONCIERGE");
        amenity2.setCategoryName("Service");
        amenities.add(amenity2);
        
        group.setAmenities(amenities);
        groups.add(group);
        
        return groups;
    }

    private List<AmenityGroup> createHighlightedAmenityGroupsWithChildAttributes() {
        List<AmenityGroup> groups = new ArrayList<>();
        AmenityGroup group = new AmenityGroup();
        group.setName("Complex Features");
        
        Amenity amenity = new Amenity();
        amenity.setName("Complex Amenity");
        amenity.setAttributeName("COMPLEX");
        
        // Create child attributes
        List<AmenityAttribute> childAttributes = new ArrayList<>();
        AmenityAttribute child = new AmenityAttribute();
        child.setName("Child Attribute");
        childAttributes.add(child);
        
        amenity.setChildAttributes(childAttributes);
        group.setAmenities(Arrays.asList(amenity));
        groups.add(group);
        
        return groups;
    }

    private List<AmenityGroup> createDuplicateAmenityGroups() {
        List<AmenityGroup> groups = new ArrayList<>();
        
        AmenityGroup group1 = new AmenityGroup();
        group1.setName("WiFi");
        groups.add(group1);
        
        AmenityGroup group2 = new AmenityGroup();
        group2.setName("WiFi"); // Duplicate name
        groups.add(group2);
        
        AmenityGroup group3 = new AmenityGroup();
        group3.setName("Parking");
        groups.add(group3);
        
        return groups;
    }

    private List<AmenityGroup> createMultipleAmenityGroups() {
        List<AmenityGroup> groups = new ArrayList<>();
        
        AmenityGroup group1 = new AmenityGroup();
        group1.setName("First Group");
        groups.add(group1);
        
        AmenityGroup group2 = new AmenityGroup();
        group2.setName("Second Group");
        groups.add(group2);
        
        AmenityGroup group3 = new AmenityGroup();
        group3.setName("Third Group");
        groups.add(group3);
        
        return groups;
    }
}
