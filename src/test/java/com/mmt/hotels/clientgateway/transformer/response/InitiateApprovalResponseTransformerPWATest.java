package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.response.DataList;
import com.mmt.hotels.clientgateway.response.InitApprovalResponse;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.response.cbresponse.ErrorResponse;
import com.mmt.hotels.clientgateway.response.corporate.UpdatePolicyResponse;
import com.mmt.hotels.clientgateway.response.moblanding.GenericErrorEntity;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.pwa.InitiateApprovalResponseTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.pwa.UpdatePolicyResponseTransformerPWA;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.corporate.CorpMetaInfo;
import com.mmt.hotels.model.response.corporate.CorpPolicyUpdateResponse;
import junit.framework.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;

import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.POLICY_MISMATCH;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class InitiateApprovalResponseTransformerPWATest {

    @Mock
    PolyglotService polyglotService;
    @Mock
    Utility utility;
    @Mock
    CommonHelper commonHelper;

    @Mock
    CommonResponseTransformer commonResponseTransformer;


    @InjectMocks
    InitiateApprovalResponseTransformerPWA initiateApprovalResponseTransformerPWA;

    @Test
    public void testConvertInitiateApprovalResponse() {


        CGServerResponse cgServerResponse = new CGServerResponse();
        cgServerResponse.setResponseErrors(new ErrorResponse());
        cgServerResponse.getResponseErrors().setErrorList(new ArrayList<>());
        cgServerResponse.getResponseErrors().getErrorList().add(new GenericErrorEntity("500", "Unexpected error"));


        InitApprovalResponse initApprovalResponse = initiateApprovalResponseTransformerPWA.processResponse(cgServerResponse);
        Assert.assertNotNull(initApprovalResponse);

    }

    @Test
    public void testProcessResponseWithAdditionalPropertiesAndError() {
        CGServerResponse cgServerResponse = new CGServerResponse();
        cgServerResponse.setAdditionalProperty("responseCode", "OSBA");
        cgServerResponse.setAdditionalProperty("message", "testMessage");
        cgServerResponse.setAdditionalProperty("status", "error");
        LinkedHashMap<String, String> duplicateBookingDetails = new LinkedHashMap<>();
        duplicateBookingDetails.put("travellerName", "name");
        cgServerResponse.setAdditionalProperty("duplicateBookingDetails", duplicateBookingDetails);
        Mockito.when(utility.populateDateList(Mockito.any())).thenReturn(new LinkedHashSet<DataList>());
        InitApprovalResponse response = initiateApprovalResponseTransformerPWA.processResponse(cgServerResponse);

        org.junit.Assert.assertNotNull(response);
        org.junit.Assert.assertNotNull(response.getConsentData());
        org.junit.Assert.assertNotNull(response.getConsentData().getDataList());
        org.junit.Assert.assertEquals("success", response.getStatus());
    }
}