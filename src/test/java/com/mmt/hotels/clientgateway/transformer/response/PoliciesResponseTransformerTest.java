package com.mmt.hotels.clientgateway.transformer.response;

import java.util.*;

import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.staticdata.*;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.mmt.hotels.clientgateway.request.PoliciesRequest;
import com.mmt.hotels.clientgateway.response.PoliciesResponse;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.CancelPenalty.CancellationType;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.RatePolicy;
import com.mmt.hotels.model.response.pricing.SupplierDetails;
import com.mmt.hotels.model.response.txn.HotelInfo;
import com.mmt.hotels.model.response.txn.PersistanceMultiRoomResponseEntity;
import com.mmt.hotels.model.response.txn.PersistedHotel;
import com.mmt.hotels.model.response.txn.PersistedMultiRoomData;
import com.mmt.hotels.model.response.txn.PersistedTariffInfo;

@RunWith(MockitoJUnitRunner.class)
public class PoliciesResponseTransformerTest {

	@InjectMocks
	private PoliciesResponseTransformer policiesResponseTransformer;

	@Mock
	private Utility utility;

	@Mock
	private PolyglotService polyglotService;
	
	@Test
	public void testTransformPolicyResponse(){
		PoliciesRequest policiesRequest = new PoliciesRequest();
		PersistanceMultiRoomResponseEntity txnDataEntity = new PersistanceMultiRoomResponseEntity();
		txnDataEntity.setPersistedData(buildPersistedData());
		PoliciesResponse resp = policiesResponseTransformer.transformPolicyResponse(policiesRequest , txnDataEntity  );	
		Assert.assertNotNull(resp);
	}

	private PersistedMultiRoomData buildPersistedData() {
		PersistedMultiRoomData persistedMultiRoomData = new PersistedMultiRoomData();
		
		List<PersistedHotel> hotelList = new ArrayList<>();
		PersistedHotel persistedHotel = new PersistedHotel();
		
		HotelInfo hotelInfo = new HotelInfo();
		hotelInfo.setHouseRules(getHouseRules());
		hotelInfo.setPolicyToMessagesMap(getPolicyToMessagesMap());
		hotelInfo.setCountryCode("IN");
		DepositPolicy depositPolicy = new DepositPolicy();
		depositPolicy.setId("DEPOSIT_CHARGES");
		depositPolicy.setCategoryName("Deposit Charges");
		depositPolicy.setRuleInfo("Deposit Charges");
		persistedHotel.setHotelInfo(hotelInfo );
		
		List<PersistedTariffInfo> tariffInfoList = new ArrayList<>();
		PersistedTariffInfo tarrifInfo = new PersistedTariffInfo();
		
		List<CancelPenalty> cancelPenaltyList = new ArrayList<>();
		CancelPenalty canPenlty = new CancelPenalty();
		canPenlty.setCancellationType(CancellationType.FREE_CANCELLATON);
		cancelPenaltyList.add(canPenlty );
		tarrifInfo.setCancelPenaltyList(cancelPenaltyList );
		
		SupplierDetails supplierDetails = new SupplierDetails();
		supplierDetails.setSupplierCode("INGO");
		tarrifInfo.setSupplierDetails(supplierDetails );
		
		RatePolicy checkinPolicy = new RatePolicy();
		checkinPolicy.setMostRestrictive("Y");
		tarrifInfo.setCheckinPolicy(checkinPolicy );
		
		RatePolicy confirmationPolicy = new RatePolicy();
		confirmationPolicy.setMostRestrictive("Y");
		tarrifInfo.setConfirmationPolicy(confirmationPolicy );
		
		tariffInfoList.add(tarrifInfo );
		persistedHotel.setTariffInfoList(tariffInfoList );
		
		hotelList.add(persistedHotel );
		persistedMultiRoomData.setHotelList(hotelList );
		
		CancellationTimeline cancellationTimeline = new CancellationTimeline();
		persistedMultiRoomData.setCancellationTimeline(cancellationTimeline );
		
		PriceByHotelsRequestBody availReqBody = new PriceByHotelsRequestBody();
		availReqBody.setPayMode("PAH_WITH_CC");
		persistedMultiRoomData.setAvailReqBody(availReqBody );
		return persistedMultiRoomData;
	}

	private HouseRules getHouseRules() {
		HouseRules houseRules = new HouseRules();
		
		List<CommonRules> commonRules = new ArrayList<CommonRules>();
		CommonRules cmnRule = new CommonRules();
		cmnRule.setCategory("");
		List<Rule> rules = new ArrayList<>();
		Rule rule = new Rule();
		rule.setText("rule txst");
		rules.add(rule );
		cmnRule.setRules(rules );
		commonRules.add(cmnRule );
		houseRules.setCommonRules(commonRules );

		
		ChildExtraBedPolicy extraBedPolicy = new ChildExtraBedPolicy();
		extraBedPolicy.setLabel("extra bed");
		extraBedPolicy.setPolicyInfo("extra bed available");
		List<PolicyRules> policyRules = new ArrayList<>();
		PolicyRules policyRule = new PolicyRules();
		policyRule.setAgeGroup("10 to 11");
		Set<ExtraBedRules> extraBedTerms = new HashSet<ExtraBedRules>();
		ExtraBedRules extraBedTerm = new ExtraBedRules();
		extraBedTerm.setLabel("chargable");
		extraBedTerm.setValue("INR 1000");
		extraBedTerms.add(extraBedTerm );
		policyRule.setExtraBedTerms(extraBedTerms );
		policyRules.add(policyRule );
		extraBedPolicy.setPolicyRules(policyRules );
		houseRules.setChildExtraBedPolicy(extraBedPolicy );
		
		
		List<ChildExtraBedPolicy> extraBedPolicyList = new ArrayList<>();
		extraBedPolicyList.add(extraBedPolicy);
		houseRules.setExtraBedPolicyList(extraBedPolicyList );

		CategoryInfo categoryInfo = new CategoryInfo();
		categoryInfo.setId("BREAKFAST_CHARGES");
		categoryInfo.setCategoryName("Breakfast Charges");
		categoryInfo.setRuleInfo("Breakfast Charges");
		categoryInfo.setRuleDesc(Arrays.asList("R1", "R2"));

		CategoryInfo categoryInfoEB = new CategoryInfo();
		categoryInfoEB.setId("EXTRA_BED_POLICY");
		categoryInfoEB.setCategoryName("EXTRA BED POLICY");
		categoryInfoEB.setRuleInfo("EXTRA BED POLICY");
		categoryInfoEB.setRuleDesc(Arrays.asList("R3", "R4"));
		houseRules.setCategoryInfoList(Arrays.asList(categoryInfoEB, categoryInfo));
		
		return houseRules;
	}

	private Map<String, List<String>> getPolicyToMessagesMap() {
		Map<String, List<String>> map = new HashMap<>();
		List<String> rules = new ArrayList<>();
		rules.add("rule1");
		map.put("Category", rules );
		return map;
	}
}
