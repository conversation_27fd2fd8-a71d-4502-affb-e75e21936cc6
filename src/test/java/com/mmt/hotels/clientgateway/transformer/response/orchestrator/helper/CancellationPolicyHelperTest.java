package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.pricing.*;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.rooms.CancellationPolicyTimeline;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CancellationPolicyHelperTest {

    @Mock
    private PolyglotService polyglotService;

    @InjectMocks
    private CancellationPolicyHelper cancellationPolicyHelper;

    // Test constants
    private static final String FREE_CANCELLATION_TEXT = "Free Cancellation";
    private static final String NON_REFUNDABLE_TEXT = "Non-Refundable";
    private static final String PARTIAL_REFUNDABLE_TEXT = "Partial Refundable";
    private static final String NON_REFUNDABLE_SUBTEXT = "No refund on cancellation";
    private static final String AMENDABLE_DETAIL_TEXT = "Amendable {amendType} for {duration}";
    private static final String GREEN_TICK_ICON = "green_tick.png";
    private static final String RED_CROSS_ICON = "red_cross.png";
    private static final String INCLUSION_GREEN_ICON = "inclusion_green.png";
    private static final String INCLUSION_RED_ICON = "inclusion_red.png";
    private static final String RATEPLAN_CANCELLATION_POLICY = "Cancellation Policy";
    private static final String STAR_FACILITIES = "Star Facilities";
    private static final String APPLICABLE_REFUND_TEXT = "Applicable Refund";

    @Before
    public void setUp() {
        // Set up @Value injected fields using ReflectionTestUtils
        ReflectionTestUtils.setField(cancellationPolicyHelper, "greenTickIcon", GREEN_TICK_ICON);
        ReflectionTestUtils.setField(cancellationPolicyHelper, "redCrossIconUrl", RED_CROSS_ICON);
        ReflectionTestUtils.setField(cancellationPolicyHelper, "inclusionIconDoubleTickGreen", INCLUSION_GREEN_ICON);
        ReflectionTestUtils.setField(cancellationPolicyHelper, "inclusionIconRedCross", INCLUSION_RED_ICON);

        // Setup common polyglot service responses
        when(polyglotService.getTranslatedData("FREE_CANCELLATION_TEXT")).thenReturn(FREE_CANCELLATION_TEXT);
        when(polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_TEXT)).thenReturn(NON_REFUNDABLE_TEXT);
        when(polyglotService.getTranslatedData(ConstantsTranslation.PARTIAL_REFUNDABLE_TEXT)).thenReturn(PARTIAL_REFUNDABLE_TEXT);
        when(polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_SUBTEXT)).thenReturn(NON_REFUNDABLE_SUBTEXT);
        when(polyglotService.getTranslatedData(ConstantsTranslation.RATEPLAN_CANCELLATION_POLICY)).thenReturn(RATEPLAN_CANCELLATION_POLICY);
        when(polyglotService.getTranslatedData(ConstantsTranslation.APPLICABLE_REFUND_TEXT)).thenReturn(APPLICABLE_REFUND_TEXT);
    }

    // transformCancellationPolicy Tests

    @Test
    public void should_ReturnNull_When_PolicyIsNull() {
        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(null, null);

        // Then
        assertNull(result);
    }

    @Test
    public void should_TransformFreeCancellationPolicy_When_PolicyTypeIsFC() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("FC", "Free cancellation until 24 hours");

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, null);

        // Then
        assertNotNull(result);
        assertEquals(BookedCancellationPolicyType.FC, result.getType());
        assertEquals(IconType.DOUBLETICK, result.getIconType());
        assertEquals(GREEN_TICK_ICON, result.getIconUrl());
        assertEquals(INCLUSION_GREEN_ICON, result.getIconUrlV2());
        assertEquals("Free cancellation until 24 hours", result.getText());
        assertNotNull(result.getCancelRules());
    }

    @Test
    public void should_TransformFreeCancellationPolicy_When_PolicyTypeIsFREE_CANCELLATION() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("FREE_CANCELLATION", null);

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, null);

        // Then
        assertNotNull(result);
        assertEquals(BookedCancellationPolicyType.FC, result.getType());
        assertEquals(IconType.DOUBLETICK, result.getIconType());
        assertEquals(FREE_CANCELLATION_TEXT, result.getText());
    }

    @Test
    public void should_TransformPartialRefundablePolicy_When_PolicyTypeIsPR() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("PR", null);

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, null);

        // Then
        assertNotNull(result);
        assertEquals(BookedCancellationPolicyType.FC, result.getType());
        assertEquals(IconType.DOUBLETICK, result.getIconType());
        assertEquals(GREEN_TICK_ICON, result.getIconUrl());
        assertEquals(INCLUSION_GREEN_ICON, result.getIconUrlV2());
        assertEquals(PARTIAL_REFUNDABLE_TEXT, result.getText());
    }

    @Test
    public void should_TransformPartialRefundablePolicy_When_PolicyTypeIsPARTIAL_REFUNDABLE() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("PARTIAL_REFUNDABLE", null);

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, null);

        // Then
        assertNotNull(result);
        assertEquals(BookedCancellationPolicyType.FC, result.getType());
        assertEquals(IconType.DOUBLETICK, result.getIconType());
        assertEquals(PARTIAL_REFUNDABLE_TEXT, result.getText());
    }

    @Test
    public void should_TransformNonRefundablePolicy_When_PolicyTypeIsNR() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("NR", null);

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, null);

        // Then
        assertNotNull(result);
        assertEquals(BookedCancellationPolicyType.NR, result.getType());
        assertEquals(IconType.BIGCROSS, result.getIconType());
        assertEquals(RED_CROSS_ICON, result.getIconUrl());
        assertEquals(INCLUSION_RED_ICON, result.getIconUrlV2());
        assertEquals(NON_REFUNDABLE_TEXT, result.getText());
        assertEquals(NON_REFUNDABLE_SUBTEXT, result.getSubText());
    }

    @Test
    public void should_TransformNonRefundablePolicy_When_PolicyTypeIsNON_REFUNDABLE() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("NON_REFUNDABLE", null);

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, null);

        // Then
        assertNotNull(result);
        assertEquals(BookedCancellationPolicyType.NR, result.getType());
        assertEquals(IconType.BIGCROSS, result.getIconType());
        assertEquals(NON_REFUNDABLE_TEXT, result.getText());
    }

    @Test
    public void should_SetDefaultIconType_When_AdvancePurchaseIsLessThanLimit() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("NR", null);

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, 1);

        // Then
        assertNotNull(result);
        assertEquals(IconType.DEFAULT, result.getIconType());
    }

    @Test
    public void should_SetBigCrossIconType_When_AdvancePurchaseIsGreaterThanOrEqualToLimit() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("NR", null);

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, 2);

        // Then
        assertNotNull(result);
        assertEquals(IconType.BIGCROSS, result.getIconType());
    }

    @Test
    public void should_HandleEmptyPenalties_When_TransformingPolicy() {
        // Given
        CancellationPolicy policy = new CancellationPolicy();
        policy.setPenalties(new ArrayList<>());

        when(polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_TEXT)).thenReturn(NON_REFUNDABLE_TEXT);
        when(polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_SUBTEXT)).thenReturn(NON_REFUNDABLE_SUBTEXT);

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, null);

        // Then
        assertNotNull(result);
        assertEquals(BookedCancellationPolicyType.NR, result.getType());
        assertEquals(NON_REFUNDABLE_TEXT, result.getText());
        assertEquals(NON_REFUNDABLE_SUBTEXT, result.getSubText());
        assertNull(result.getCancelRules());
    }

    @Test
    public void should_UseFallbackText_When_FreeCancellationTextIsBlank() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("FC", "");

        // When
        BookedCancellationPolicy result = cancellationPolicyHelper.transformCancellationPolicy(policy, null);

        // Then
        assertNotNull(result);
        assertEquals(FREE_CANCELLATION_TEXT, result.getText());
    }

    // buildCancellationTimelineFromOrchV2 Tests

    @Test
    public void should_ReturnNull_When_TimelineDetailsIsNull() {
        // When
        CancellationTimeline result = cancellationPolicyHelper.buildCancellationTimelineFromOrchV2(null, null);

        // Then
        assertNull(result);
    }

    @Test
    public void should_BuildCancellationTimeline_When_ValidTimelineDetails() {
        // Given
        CancellationTimelineDetails timelineDetails = createCancellationTimelineDetails();

        // When
        CancellationTimeline result = cancellationPolicyHelper.buildCancellationTimelineFromOrchV2(timelineDetails, null);

        // Then
        assertNotNull(result);
        assertEquals("2023-12-01", result.getBookingDate());
        assertEquals("2023-12-10", result.getCancellationDate());
        assertEquals("2023-12-10T10:00:00", result.getCancellationDateTime());
        assertEquals("2023-12-11", result.getCardChargeDate());
        assertEquals("2023-12-11T12:00:00", result.getCardChargeDateTime());
        assertEquals("dd/MM/yyyy", result.getDateFormat());
        assertEquals("Card will be charged", result.getCardChargeText());
        assertEquals("Booking amount: $100", result.getBookingAmountText());
        assertEquals("2023-12-15", result.getCheckInDate());
        assertEquals("2023-12-15T15:00:00", result.getCheckInDateTime());
        assertEquals("Free cancellation available", result.getFreeCancellationText());
        assertEquals("Cancellation details", result.getSubTitle());
        assertEquals(RATEPLAN_CANCELLATION_POLICY, result.getTitle());
    }

    @Test
    public void should_TransformFreeCancellationBenefits_When_BenefitsExist() {
        // Given
        CancellationTimelineDetails timelineDetails = createCancellationTimelineDetailsWithBenefits();

        // When
        CancellationTimeline result = cancellationPolicyHelper.buildCancellationTimelineFromOrchV2(timelineDetails, null);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFreeCancellationBenefits());
        assertEquals(3, result.getFreeCancellationBenefits().size());

        FCBenefit bnplBenefit = result.getFreeCancellationBenefits().get(0);
        assertEquals("BNPL disabled benefit", bnplBenefit.getText());
        assertEquals(IconType.DEFAULT, bnplBenefit.getIconType());

        FCBenefit fczpnBenefit = result.getFreeCancellationBenefits().get(1);
        assertEquals("FCZPN benefit", fczpnBenefit.getText());
        assertEquals(IconType.DOUBLETICK, fczpnBenefit.getIconType());

        FCBenefit defaultBenefit = result.getFreeCancellationBenefits().get(2);
        assertEquals("Regular benefit", defaultBenefit.getText());
        assertEquals(IconType.SINGLETICK, defaultBenefit.getIconType());
    }

    @Test
    public void should_HandleNullBenefitType_When_TransformingBenefits() {
        // Given
        CancellationTimelineDetails timelineDetails = new CancellationTimelineDetails();
        List<FreeCancellationBenefitDetails> benefits = new ArrayList<>();
        FreeCancellationBenefitDetails benefit = new FreeCancellationBenefitDetails();
        benefit.setText("Benefit with null type");
        benefit.setType(null);
        benefits.add(benefit);
        timelineDetails.setFreeCancellationBenefits(benefits);

        // When
        CancellationTimeline result = cancellationPolicyHelper.buildCancellationTimelineFromOrchV2(timelineDetails, null);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFreeCancellationBenefits());
        assertEquals(1, result.getFreeCancellationBenefits().size());
        assertEquals(IconType.SINGLETICK, result.getFreeCancellationBenefits().get(0).getIconType());
    }

    // buildCancellationPolicyTimelineFromOrchV2 Tests

    @Test
    public void should_ReturnNull_When_TimelineDetailsIsNullForPolicyTimeline() {
        // When
        CancellationPolicyTimeline result = cancellationPolicyHelper.buildCancellationPolicyTimelineFromOrchV2(null, false);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_PolicyTimelineListIsEmpty() {
        // Given
        CancellationTimelineDetails timelineDetails = new CancellationTimelineDetails();
        timelineDetails.setCancellationPolicyTimelineList(new ArrayList<>());

        // When
        CancellationPolicyTimeline result = cancellationPolicyHelper.buildCancellationPolicyTimelineFromOrchV2(timelineDetails, false);

        // Then
        assertNull(result);
    }

    @Test
    public void should_BuildPolicyTimeline_When_ThemificationDisabled() {
        // Given
        CancellationTimelineDetails timelineDetails = createTimelineDetailsWithPolicyTimeline();

        // When
        CancellationPolicyTimeline result = cancellationPolicyHelper.buildCancellationPolicyTimelineFromOrchV2(timelineDetails, false);

        // Then
        assertNotNull(result);
        assertEquals("2023-12-10", result.getCancellationDate());
        assertEquals("2023-12-10T10:00:00", result.getCancellationDateTime());
        assertEquals("dd/MM/yyyy", result.getDateFormat());
        assertEquals("Booking amount: $100", result.getBookingAmountText());
        assertEquals(RATEPLAN_CANCELLATION_POLICY, result.getTitle());
        assertNotNull(result.getTimeline());
        assertEquals(2, result.getTimeline().size());
        assertNull(result.getTimelinesV2());
        assertNull(result.getHeaderLeft());
        assertNull(result.getHeaderRight());
    }

    @Test
    public void should_BuildPolicyTimeline_When_ThemificationEnabled() {
        // Given
        CancellationTimelineDetails timelineDetails = createTimelineDetailsWithPolicyTimeline();

        // When
        CancellationPolicyTimeline result = cancellationPolicyHelper.buildCancellationPolicyTimelineFromOrchV2(timelineDetails, true);

        // Then
        assertNotNull(result);
        assertNull(result.getTimeline());
        assertNotNull(result.getTimelinesV2());
        assertEquals(2, result.getTimelinesV2().size());
        assertEquals(RATEPLAN_CANCELLATION_POLICY, result.getHeaderLeft());
        assertEquals(APPLICABLE_REFUND_TEXT, result.getHeaderRight());
    }

    @Test
    public void should_SetCardChargeDetails_When_TimelineListSizeIsLessThan3() {
        // Given
        CancellationTimelineDetails timelineDetails = createTimelineDetailsWithPolicyTimeline();
        // Ensure only 2 items in timeline (less than 3)

        // When
        CancellationPolicyTimeline result = cancellationPolicyHelper.buildCancellationPolicyTimelineFromOrchV2(timelineDetails, false);

        // Then
        assertNotNull(result);
        assertEquals("2023-12-11", result.getCardChargeDate());
        assertEquals("2023-12-11T12:00:00", result.getCardChargeDateTime());
        assertEquals("Card will be charged", result.getCardChargeText());
    }

    // getCancellationPolicyType Tests

    @Test
    public void should_ReturnFC_When_PenaltyTypeIsFC() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("FC", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_FC, result);
    }

    @Test
    public void should_ReturnFC_When_PenaltyTypeIsFREE_CANCELLATON() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("FREE_CANCELLATON", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_FC, result);
    }

    @Test
    public void should_ReturnFC_When_PenaltyTypeIsFREE_CANCELLATION() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("FREE_CANCELLATION", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_FC, result);
    }

    @Test
    public void should_ReturnFCZPN_When_PenaltyTypeIsPR() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("PR", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_FCZPN, result);
    }

    @Test
    public void should_ReturnFCZPN_When_PenaltyTypeIsPARTIAL_REFUNDABLE() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("PARTIAL_REFUNDABLE", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_FCZPN, result);
    }

    @Test
    public void should_ReturnNR_When_PenaltyTypeIsNR() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("NR", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_NR, result);
    }

    @Test
    public void should_ReturnNR_When_PenaltyTypeIsNON_REFUNDABLE() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("NON_REFUNDABLE", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_NR, result);
    }

    @Test
    public void should_ReturnNR_When_PenaltyTypeIsUnknown() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("UNKNOWN_TYPE", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_NR, result);
    }

    @Test
    public void should_ReturnNR_When_PenaltyTypeIsBlank() {
        // Given
        CancellationPolicy policy = createCancellationPolicy("", null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_NR, result);
    }

    @Test
    public void should_ReturnNR_When_PenaltiesListIsEmpty() {
        // Given
        CancellationPolicy policy = new CancellationPolicy();
        policy.setPenalties(new ArrayList<>());

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_NR, result);
    }

    @Test
    public void should_ReturnNR_When_PenaltiesListIsNull() {
        // Given
        CancellationPolicy policy = new CancellationPolicy();
        policy.setPenalties(null);

        // When
        String result = cancellationPolicyHelper.getCancellationPolicyType(policy);

        // Then
        assertEquals(Constants.CANCELLATION_TYPE_NR, result);
    }

    // Helper methods for creating test data

    private CancellationPolicy createCancellationPolicy(String penaltyType, String freeCancellationText) {
        CancellationPolicy policy = new CancellationPolicy();
        
        List<CancellationPenalty> penalties = new ArrayList<>();
        CancellationPenalty penalty = new CancellationPenalty();
        penalty.setType(penaltyType);
        penalty.setFreeCancellationText(freeCancellationText);
        
        // Add cancel rules for testing
        List<CancelRules> cancelRules = new ArrayList<>();
        CancelRules rule = new CancelRules();
        rule.setText("Cancel rule text");
        
        List<CancelRulesDesc> descText = new ArrayList<>();
        CancelRulesDesc desc = new CancelRulesDesc();
        desc.setFeeText("Fee: $50");
        desc.setDateText("Before 24 hours");
        descText.add(desc);
        rule.setDescText(descText);
        
        cancelRules.add(rule);
        penalty.setCancelRules(cancelRules);
        
        penalties.add(penalty);
        policy.setPenalties(penalties);
        
        return policy;
    }

    private CancellationTimelineDetails createCancellationTimelineDetails() {
        CancellationTimelineDetails details = new CancellationTimelineDetails();
        details.setBookingDate("2023-12-01");
        details.setCancellationDate("2023-12-10");
        details.setCancellationDateTime("2023-12-10T10:00:00");
        details.setCardChargeDate("2023-12-11");
        details.setCardChargeDateTime("2023-12-11T12:00:00");
        details.setDateFormat("dd/MM/yyyy");
        details.setCardChargeText("Card will be charged");
        details.setBookingAmountText("Booking amount: $100");
        details.setCheckInDate("2023-12-15");
        details.setCheckInDateTime("2023-12-15T15:00:00");
        details.setFreeCancellationText("Free cancellation available");
        details.setSubTitle("Cancellation details");
        return details;
    }

    private CancellationTimelineDetails createCancellationTimelineDetailsWithBenefits() {
        CancellationTimelineDetails details = createCancellationTimelineDetails();
        
        List<FreeCancellationBenefitDetails> benefits = new ArrayList<>();
        
        FreeCancellationBenefitDetails bnplBenefit = new FreeCancellationBenefitDetails();
        bnplBenefit.setText("BNPL disabled benefit");
        bnplBenefit.setType("BNPL_DISABLED");
        benefits.add(bnplBenefit);
        
        FreeCancellationBenefitDetails fczpnBenefit = new FreeCancellationBenefitDetails();
        fczpnBenefit.setText("FCZPN benefit");
        fczpnBenefit.setType("FCZPN");
        benefits.add(fczpnBenefit);
        
        FreeCancellationBenefitDetails defaultBenefit = new FreeCancellationBenefitDetails();
        defaultBenefit.setText("Regular benefit");
        defaultBenefit.setType("OTHER");
        benefits.add(defaultBenefit);
        
        details.setFreeCancellationBenefits(benefits);
        return details;
    }

    private CancellationTimelineDetails createTimelineDetailsWithPolicyTimeline() {
        CancellationTimelineDetails details = createCancellationTimelineDetails();
        
        List<CancellationPolicyTimelineDetails> policyTimelineList = new ArrayList<>();
        
        CancellationPolicyTimelineDetails timeline1 = new CancellationPolicyTimelineDetails();
        timeline1.setRefundable(true);
        timeline1.setText("Full refund available");
        timeline1.setStartDate("2023-12-01");
        timeline1.setEndDate("2023-12-08");
        timeline1.setEndDateTime("2023-12-08T23:59:59");
        timeline1.setType("FULL_REFUND");
        timeline1.setRefundText("100% refund");
        policyTimelineList.add(timeline1);
        
        CancellationPolicyTimelineDetails timeline2 = new CancellationPolicyTimelineDetails();
        timeline2.setRefundable(false);
        timeline2.setText("No refund");
        timeline2.setStartDate("2023-12-09");
        timeline2.setEndDate("2023-12-15");
        timeline2.setEndDateTime("2023-12-15T23:59:59");
        timeline2.setType("NO_REFUND");
        timeline2.setRefundText("0% refund");
        policyTimelineList.add(timeline2);
        
        details.setCancellationPolicyTimelineList(policyTimelineList);
        return details;
    }
} 