package com.mmt.hotels.clientgateway.transformer.response.pwa;

import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class AvailRoomsResponseTransformerPWATest {

    @InjectMocks
    private AvailRoomsResponseTransformerPWA availRoomsResponseTransformerPWA;

    @Mock
    private PolyglotService polyglotService;
    @InjectMocks
    private PersuasionUtil persuasionUtil = Mockito.spy(new PersuasionUtil());

    @Test
    public void testBuildLoyaltyCashbackPersuasions() {
        boolean exception = false;
        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("test_key");
        try {
            availRoomsResponseTransformerPWA.buildLoyaltyCashbackPersuasions(getCoupon1(), new HashMap<>());
        } catch (Exception ex) {
            assertNotNull(ex);
            exception = true;
        }
        assertFalse(exception);
    }

    @Test
    public void testBuildLoyaltyCashbackPersuasions1() {
        boolean exception = false;
        Mockito.when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("test_key");
        try {
            availRoomsResponseTransformerPWA.buildLoyaltyCashbackPersuasions(getCoupon2(), new HashMap<>());
        } catch (Exception ex) {
            assertNotNull(ex);
            exception = true;
        }
        assertFalse(exception);
    }

    @Test
    public void testBuildSpecialFareTagPersuasion() {
        boolean exception = false;
        try {
            availRoomsResponseTransformerPWA.buildSpecialFareTagPersuasion("test");
        } catch (Exception ex) {
            assertNotNull(ex);
            exception = true;
        }
        assertFalse(exception);
    }

    private BestCoupon getCoupon1() {
        BestCoupon coupon = new BestCoupon();
        coupon.setCouponCode("1234");
        coupon.setLoyaltyOfferMessage("abcd");
        return coupon;
    }

    private BestCoupon getCoupon2() {
        BestCoupon coupon = new BestCoupon();
        coupon.setCouponCode("1234");
        Map<String, Double> hybridDiscountMap = new HashMap<>();
        hybridDiscountMap.put("CTW", 1000.0);
        coupon.setHybridDiscounts(hybridDiscountMap);
        return coupon;

    }

}