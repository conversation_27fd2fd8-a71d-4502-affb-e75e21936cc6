package com.mmt.hotels.clientgateway.transformer.response.cardEngine;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

public class CardEngineResponseTransformerFactoryTest {

    @Mock
    private CardEngineResponseTransformer cardEngineResponseTransformer;

    @Mock
    private ListingCollectionCardEngineResponseTransformer listingCollectionCardEngineResponseTransformer;

    @InjectMocks
    private CardEngineResponseTransformerFactory factory;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetResponseService_ListingCollection() {
        String subPageContext = "LISTING_COLLECTION";
        CardEngineResponseTransformer result = factory.getResponseService(subPageContext);
        assertEquals(listingCollectionCardEngineResponseTransformer, result);
    }

    @Test
    public void testGetResponseService_Empty() {
        String subPageContext = StringUtils.EMPTY;
        CardEngineResponseTransformer result = factory.getResponseService(subPageContext);
        assertEquals(cardEngineResponseTransformer, result);
    }

    @Test
    public void testGetResponseService_Null() {
        String subPageContext = null;
        CardEngineResponseTransformer result = factory.getResponseService(subPageContext);
        assertEquals(cardEngineResponseTransformer, result);
    }

    @Test
    public void testGetResponseService_Other() {
        String subPageContext = "OTHER";
        CardEngineResponseTransformer result = factory.getResponseService(subPageContext);
        assertEquals(cardEngineResponseTransformer, result);
    }
}