package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.businessobjects.*;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.consul.FilterConfigConsul;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.helpers.FilterHelper;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.FilterConfig;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.filter.FilterPricingOption;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.transformer.request.FilterRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.FilterResponseTransformer;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.dpt.UserCohort;
import com.mmt.propertymanager.config.PropertyManager;
import org.aeonbits.owner.event.ReloadListener;
import org.apache.commons.io.FileUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.beans.PropertyChangeListener;
import java.io.IOException;
import java.io.InputStream;
import java.io.Reader;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FilterFactoryTest {


    @InjectMocks
    FilterFactory filterFactory;

    @Mock
    PolyglotHelper polyglotHelper;

    @Mock
    FilterHelper filterHelper;

    @Mock
    FilterConfigConsul filterConfigConsul;

    @Mock
    PropertyManager propertyManager;

    @Mock
    private FilterConfiguration filterConfiguration;

    @Mock
    private LinkedHashMap<String, FilterConfigCategory> filters;

    @Before
    public void init() {
        ReflectionTestUtils.setField(filterFactory, "filterResponseTransformer", new FilterResponseTransformer());
        ReflectionTestUtils.setField(filterFactory, "filterRequestTransformer", new FilterRequestTransformer());
        String mealPreference = "{\"groups\":{\"MEAL_PLAN_AVAIL\":{\"TWO_MEAL_AVAIL\":{\"title\":\"BUSINESS_TWO_MEAL_AVAIL_TITLE\"},\"ALL_MEAL_AVAIL\":{\"title\":\"BUSINESS_ALL_MEAL_AVAIL_TITLE\"}},\"FREE_BREAKFAST_AVAIL\":{\"BREAKFAST_AVAIL\":{\"title\":\"POPULAR_GROUPS_BREAKFAST_TITLE\"}}},\"title\":\"MEAL_PREFERENCES_FILTER_TITLE\",\"viewType\":\"flex\",\"visible\":true,\"showMore\":true,\"minItemsToShow\":4}\n";
        ReflectionTestUtils.setField(filterFactory, "mealPrefConfig", mealPreference);
        String homestayToggleFilter = "{\"groups\":{\"FREE_CANCELLATION_AVAIL\":{\"CANCELLATION_AVAIL\":{\"title\":\"POPULAR_GROUPS_CANCELLATION_AVAIL_TITLE\",\"subTitle\":\"FREE_CANCELLATION_AVAIL_SUB_TITLE\"}},\"HOTEL_CATEGORY\":{\"luxury_hotels\":{\"title\":\"LUX_CATEGORY_TITLE\",\"subTitle\":\"LUX_FILTER_TITLE\"},\"MMT Value Stays\":{\"title\":\"MMT_VALUESTAYFILTER_TITLE\",\"subTitle\":\"MMT_VALUESTAYFILTER_SUB_TEXT\"}},\"MISCELLANEOUS\":{\"Entire Property\":{\"title\":\"SPACE_GROUPS_MISCELLANEOUS_TITLE\",\"subTitle\":\"SPACE_GROUPS_MISCELLANEOUS_SUB_TITLE\"}},\"BOOKING\":{\"RTB\":{\"title\":\"SPACE_GROUPS_BOOKING_RTB_TITLE\",\"subTitle\":\"SPACE_GROUPS_BOOKING_RTB_SUB_TITLE\"}},\"AMENITIES\":{\"Caretaker\":{\"title\":\"SPACE_GROUPS_AMENITIES_TITLE\",\"subTitle\":\"SPACE_GROUPS_AMENITIES_SUB_TITLE\"}}},\"title\":\"TOGGLE_FILTER_TITLE\",\"viewType\":\"toggle\",\"visible\":true}\n";
        ReflectionTestUtils.setField(filterFactory, "toggleFilterConfigHomestay", homestayToggleFilter);
        String toggleFilter = "{\"groups\":{\"FREE_CANCELLATION_AVAIL\":{\"CANCELLATION_AVAIL\":{\"title\":\"POPULAR_GROUPS_CANCELLATION_AVAIL_TITLE\",\"subTitle\":\"FREE_CANCELLATION_AVAIL_SUB_TITLE\"}},\"HOTEL_CATEGORY\":{\"luxury_hotels\":{\"title\":\"LUX_CATEGORY_TITLE\",\"subTitle\":\"LUX_FILTER_TITLE\"},\"MMT Value Stays\":{\"title\":\"MMT_VALUESTAYFILTER_TITLE\",\"subTitle\":\"MMT_VALUESTAYFILTER_SUB_TEXT\"}},\"MISCELLANEOUS\":{\"Entire Property\":{\"title\":\"SPACE_GROUPS_MISCELLANEOUS_TITLE\",\"subTitle\":\"SPACE_GROUPS_MISCELLANEOUS_SUB_TITLE\",\"description\":\"FREQUENTLY_APPLIED_HOMESTAY_TEXT\"}},\"BOOKING\":{\"RTB\":{\"title\":\"SPACE_GROUPS_BOOKING_RTB_TITLE\",\"subTitle\":\"SPACE_GROUPS_BOOKING_RTB_SUB_TITLE\",\"description\":\"FREQUENTLY_APPLIED_HOMESTAY_TEXT\"}},\"AMENITIES\":{\"Caretaker\":{\"title\":\"SPACE_GROUPS_AMENITIES_TITLE\",\"subTitle\":\"SPACE_GROUPS_AMENITIES_SUB_TITLE\",\"description\":\"FREQUENTLY_APPLIED_HOMESTAY_TEXT\"}}},\"title\":\"TOGGLE_FILTER_TITLE\",\"viewType\":\"toggle\",\"visible\":true}\n";
        ReflectionTestUtils.setField(filterFactory, "toggleFilterConfig", toggleFilter);
    }

    @Test
    public void testInit(){
        ReflectionTestUtils.setField(filterFactory, "consulFlag", true);

        Mockito.when(filterConfigConsul.getBaseFilterSettings()).thenReturn(null);

        Mockito.when(filterConfigConsul.getCompositeFilterConfig()).thenReturn(null);
        Mockito.when(filterConfigConsul.getPwaMyPartnerFilterSettings()).thenReturn(null);
        Mockito.when(filterConfigConsul.getDesktopMyPartnerFilterSettings()).thenReturn(null);
        Mockito.when(filterConfigConsul.getDesktopGCCFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getAndroidGCCFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getIosGCCFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getPwaGCCFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getSeoIntlFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getSeoDomFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getMetaIntlFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getMetaDomFilterSetting()).thenReturn(null);

        Mockito.when(filterConfigConsul.getSemIntlFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getSemDomFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getPhonePeFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getDesktopIntlHotelsFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getAppsIntlHotelsFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getPwaIntlHotelsFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getDesktopDomHotelsFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getAppsDomHotelsFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getPwaDomHotelsFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getDesktopIntlHomestayFilterSetting()).thenReturn(null);

        Mockito.when(filterConfigConsul.getAppsIntlHomestayFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getPwaIntlHomestayFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getDesktopDomHomestayFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getAppsDomHomestayFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getPwaDomHomestayFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getDayuseFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getDesktopCorpIntlFilterSetting()).thenReturn(null);

        Mockito.when(filterConfigConsul.getAppsCorpIntlFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getPwaCorpIntlFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getDesktopCorpDomFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getAppsCorpDomFilterSetting()).thenReturn(null);
        Mockito.when(filterConfigConsul.getPwaCorpDomFilterSetting()).thenReturn(null);

        ReflectionTestUtils.invokeMethod(filterFactory, "init");

        ReflectionTestUtils.setField(filterFactory, "consulFlag", false);

        Mockito.when(propertyManager.getProperty(Mockito.anyString(), Mockito.any())).thenReturn(getFilterConfig());

        ReflectionTestUtils.invokeMethod(filterFactory, "init");

    }

    private FilterConfig getFilterConfig(){
        return new FilterConfig() {
            @Override
            public String baseFilterSettings() {
                return null;
            }

            @Override
            public String baseFilterSettingsV2() {
                return null;
            }

            @Override
            public String appsDomFilterSettingsV2() {
                return null;
            }

            @Override
            public String defaultPriceHistogram() {
                return null;
            }

            @Override
            public String defaultPriceHistogramCorp() {
                return null;
            }

            @Override
            public String pwaMyPartnerFilterSettings() {
                return null;
            }

            @Override
            public String desktopMyPartnerFilterSettings() {
                return null;
            }

            @Override
            public String desktopGCCFilterSetting() {
                return null;
            }

            @Override
            public String androidGCCFilterSetting() {
                return null;
            }

            @Override
            public String pwaGCCFilterSetting() {
                return null;
            }

            @Override
            public String seoIntlFilterSetting() {
                return null;
            }

            @Override
            public String seoDomFilterSetting() {
                return null;
            }

            @Override
            public String metaIntlFilterSetting() {
                return null;
            }

            @Override
            public String metaDomFilterSetting() {
                return null;
            }

            @Override
            public String semIntlFilterSetting() {
                return null;
            }

            @Override
            public String semDomFilterSetting() {
                return null;
            }

            @Override
            public String phonePeFilterSetting() {
                return null;
            }

            @Override
            public String desktopIntlHotelsFilterSetting() {
                return null;
            }

            @Override
            public String appsIntlHotelsFilterSetting() {
                return null;
            }

            @Override
            public String pwaIntlHotelsFilterSetting() {
                return null;
            }

            @Override
            public String desktopDomHotelsFilterSetting() {
                return null;
            }

            @Override
            public String appsDomHotelsFilterSetting() {
                return null;
            }

            @Override
            public String pwaDomHotelsFilterSetting() {
                return null;
            }

            @Override
            public String desktopIntlHomestayFilterSetting() {
                return null;
            }

            @Override
            public String appsIntlHomestayFilterSetting() {
                return null;
            }

            @Override
            public String pwaIntlHomestayFilterSetting() {
                return null;
            }

            @Override
            public String desktopDomHomestayFilterSetting() {
                return null;
            }

            @Override
            public String appsDomHomestayFilterSetting() {
                return null;
            }

            @Override
            public String pwaDomHomestayFilterSetting() {
                return null;
            }

            @Override
            public String dayuseFilterSetting() {
                return null;
            }

            @Override
            public String desktopCorpIntlFilterSetting() {
                return null;
            }

            @Override
            public String appsCorpIntlFilterSetting() {
                return null;
            }

            @Override
            public String pwaCorpIntlFilterSetting() {
                return null;
            }

            @Override
            public String desktopCorpDomFilterSetting() {
                return null;
            }

            @Override
            public String appsCorpDomFilterSetting() {
                return null;
            }

            @Override
            public String pwaCorpDomFilterSetting() {
                return null;
            }

            @Override
            public String iosGCCFilterSetting() {
                return null;
            }

            @Override
            public Map<String, List<Filter>> compositeFilterConfig() {
                return null;
            }

            @Override
            public String amenitiesCategoryConfig() {
                return null;
            }

            @Override
            public String amenitiesCategoryConfigPolyGlot() {
                return null;
            }

            @Override
            public FilterPricingOption pricingOption() {
                return null;
            }

            @Override
            public String setProperty(String s, String s1) {
                return null;
            }

            @Override
            public String removeProperty(String s) {
                return null;
            }

            @Override
            public void clear() {

            }

            @Override
            public void load(InputStream inputStream) throws IOException {

            }

            @Override
            public void load(Reader reader) throws IOException {

            }

            @Override
            public void addPropertyChangeListener(PropertyChangeListener propertyChangeListener) {

            }

            @Override
            public void removePropertyChangeListener(PropertyChangeListener propertyChangeListener) {

            }

            @Override
            public void addPropertyChangeListener(String s, PropertyChangeListener propertyChangeListener) {

            }

            @Override
            public void reload() {

            }

            @Override
            public void addReloadListener(ReloadListener reloadListener) {

            }

            @Override
            public void removeReloadListener(ReloadListener reloadListener) {

            }
        };
    }
    
    @Test
    public void getRequestServiceTest(){
        FilterRequestTransformer resp = filterFactory.getRequestService("PWA");
        Assert.assertTrue(resp instanceof FilterRequestTransformer  );
        resp = filterFactory.getRequestService("DESKTOP");
        Assert.assertTrue(resp instanceof FilterRequestTransformer  );
        resp = filterFactory.getRequestService("ANDROID");
        Assert.assertTrue(resp instanceof FilterRequestTransformer  );
        resp = filterFactory.getRequestService("IOS");
        Assert.assertTrue(resp instanceof FilterRequestTransformer  );
        resp = filterFactory.getRequestService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(filterFactory.getRequestService("test") instanceof FilterRequestTransformer);
    }

    @Test
    public void getResponseServiceTest(){
        FilterResponseTransformer resp = filterFactory.getResponseService("PWA");
        Assert.assertTrue(resp instanceof FilterResponseTransformer  );
        resp = filterFactory.getResponseService("DESKTOP");
        Assert.assertTrue(resp instanceof FilterResponseTransformer  );
        resp = filterFactory.getResponseService("ANDROID");
        Assert.assertTrue(resp instanceof FilterResponseTransformer  );
        resp = filterFactory.getResponseService("IOS");
        Assert.assertTrue(resp instanceof FilterResponseTransformer  );
        resp = filterFactory.getResponseService("");
        Assert.assertNotNull(resp);
        Assert.assertTrue(filterFactory.getResponseService("test") instanceof FilterResponseTransformer );
    }

    @Test
    public void modifiedFilterWithPolyglotData() {
        String filter = "{\"filtersToShow\":{\"AMENITIES\":{\"AMENITIES\":[]},\"BUSINESS\":{\"AMENITIES\":[\"Wi-Fi:5\"],\"FREE_BREAKFAST_AVAIL\":[\"BREAKFAST_AVAIL:0\"],\"IN_POLICY\":[\"IN_POLICY:3\"],\"MEAL_PLAN_AVAIL\":[\"TWO_MEAL_AVAIL:1\",\"ALL_MEAL_AVAIL:2\"],\"MMT_OFFERING\":[\"MyBiz Assured:4\",\"MySafety - Safe and Hygienic Stays:7\"],\"PAY_AT_HOTEL_AVAIL\":[\"PAH_AVAIL:6\"]},\"DRIVING_DISTANCE_KM\":{\"DRIVING_DISTANCE_KM\":[]},\"HOTEL_PRICE_MANUAL\":{\"HOTEL_PRICE_MANUAL\":[]},\"OTHER\":{\"FREE_CANCELLATION_AVAIL\":[\"CANCELLATION_AVAIL:1\"],\"PAY_LATER\":[\"PAY_LATER:0\"]},\"PRICE_BUCKET\":{\"HOTEL_PRICE_BUCKET\":[]},\"STAR_CATEGORY\":{\"STAR_RATING\":[\"3:0\",\"4:1\",\"5:2\",\"Unrated:3\"]},\"USER_RATING\":{\"USER_RATING\":[\"3:0\",\"4:1\",\"4_5:2\"]}},\"rankOrder\":{\"AMENITIES\":6,\"BUSINESS\":1,\"DRIVING_DISTANCE_KM\":9,\"HOTEL_PRICE_MANUAL\":4,\"OTHER\":7,\"PRICE_BUCKET\":3,\"STAR_CATEGORY\":2,\"USER_RATING\":5}}\n";
        String funnelSource = "HOMESTAY";
        String modifiedFilter = filterFactory.modifiedFilterWithPolyglotData(filter, funnelSource);
        Assert.assertNotNull(modifiedFilter);
    }

    @Test
    public void modifyFilterConfigForPillsExperimentTest() {
        String funnelSource = "HOMESTAY";
        FilterConfiguration filterConfig = new FilterConfiguration();
        filterConfig.setFilters(new LinkedHashMap<>());
        ReflectionTestUtils.invokeMethod(filterFactory, "modifyFilterConfigForPillsExperiment", funnelSource, filterConfig);
        funnelSource = "HOTELS";
        ReflectionTestUtils.invokeMethod(filterFactory, "modifyFilterConfigForPillsExperiment", funnelSource, filterConfig);

    }

    @Test
    public void getFilterConfigurationTest() throws IOException {
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put("HFC", "T");

        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExpDataMap(expDataMap);
        commonModifierResponse.setExtendedUser(new ExtendedUser());

        String baseFilter = FileUtils.readFileToString(ResourceUtils.getFile("classpath:baseFilterSetting.json"));
        ReflectionTestUtils.setField(filterFactory, "baseFilterSettings", baseFilter);

        Mockito.when(filterHelper.getFilterConfig(Mockito.anyString(), Mockito.any(), Mockito.any())).thenReturn(new FilterConfiguration());

        FilterConfiguration baseFilterSettingsModified = new FilterConfiguration();
        LinkedHashMap<String, FilterConfigCategory> drivingDurationHR = new LinkedHashMap<>();
        FilterConfigCategory drivingDurationHRConfig = new FilterConfigCategory();
        drivingDurationHRConfig.setTitle("Title");
        drivingDurationHR.put(Constants.DRIVING_DURATION_HR, drivingDurationHRConfig);
        baseFilterSettingsModified.setFilters(drivingDurationHR);
        Mockito.when(polyglotHelper.translateFilterConfig(Mockito.any(), Mockito.anyString())).thenReturn(baseFilterSettingsModified);
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        filterCountRequest.setRequestDetails(new RequestDetails());
        filterCountRequest.getRequestDetails().setTrafficSource(new TrafficSource());
        filterCountRequest.setSearchCriteria(new SearchHotelsCriteria());
        filterCountRequest.getSearchCriteria().setCountryCode("in");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "DESKTOP", "B2C", "Hotels", commonModifierResponse, "city", filterCountRequest, "");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "IOS", "B2C", "Hotels", commonModifierResponse, "city", filterCountRequest, "");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "PWA", "B2C", "Hotels", commonModifierResponse, "city", filterCountRequest, "");
        filterCountRequest.getSearchCriteria().setCountryCode("ae");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "DESKTOP", "B2C", "Hotels", commonModifierResponse, "city", filterCountRequest, "");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "IOS", "B2C", "Hotels", commonModifierResponse, "city", filterCountRequest, "");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "PWA", "B2C", "Hotels", commonModifierResponse, "city", filterCountRequest, "");
        filterCountRequest.getSearchCriteria().setCountryCode("in");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "DESKTOP", "B2C", "HOMESTAY", commonModifierResponse, "city", filterCountRequest, "");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "IOS", "B2C", "HOMESTAY", commonModifierResponse, "city", filterCountRequest, "");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "PWA", "B2C", "HOMESTAY", commonModifierResponse, "city", filterCountRequest, "");
        filterCountRequest.getSearchCriteria().setCountryCode("ae");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "DESKTOP", "B2C", "HOMESTAY", commonModifierResponse, "city", filterCountRequest, "");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "IOS", "B2C", "HOMESTAY", commonModifierResponse, "city", filterCountRequest, "");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "PWA", "B2C", "HOMESTAY", commonModifierResponse, "city", filterCountRequest, "");
        filterCountRequest.getSearchCriteria().setCountryCode("in");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "DESKTOP", "CORP", "Hotels", commonModifierResponse, "city", filterCountRequest, "");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "IOS", "CORP", "Hotels", commonModifierResponse, "city",filterCountRequest, "");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "PWA", "CORP", "Hotels", commonModifierResponse, "city", filterCountRequest, "");
        filterCountRequest.getSearchCriteria().setCountryCode("ae");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "DESKTOP", "CORP", "Hotels", commonModifierResponse, "city",filterCountRequest, "");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "IOS", "CORP", "Hotels", commonModifierResponse, "city", filterCountRequest, "");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "PWA", "CORP", "Hotels", commonModifierResponse, "city",filterCountRequest, "");
        filterCountRequest.getRequestDetails().getTrafficSource().setSource("seo");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "DESKTOP", "B2C", "Hotels", commonModifierResponse, "city", filterCountRequest, "");
        filterCountRequest.getSearchCriteria().setCountryCode("in");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "DESKTOP", "B2C", "Hotels", commonModifierResponse, "city",filterCountRequest, "");
        filterCountRequest.getRequestDetails().getTrafficSource().setSource("meta");
        filterCountRequest.getSearchCriteria().setCountryCode("ae");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "DESKTOP", "B2C", "Hotels", commonModifierResponse, "city", filterCountRequest, "");
        filterCountRequest.getSearchCriteria().setCountryCode("in");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "DESKTOP", "B2C", "Hotels", commonModifierResponse, "city", filterCountRequest, "");
        filterCountRequest.getRequestDetails().getTrafficSource().setSource("sem");
        filterCountRequest.getSearchCriteria().setCountryCode("ae");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "DESKTOP", "B2C", "Hotels", commonModifierResponse, "city",filterCountRequest, "");
        filterCountRequest.getSearchCriteria().setCountryCode("in");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "DESKTOP", "B2C", "Hotels", commonModifierResponse, "city",filterCountRequest, "");
        filterCountRequest.getRequestDetails().getTrafficSource().setSource("phonePe");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "DESKTOP", "B2C", "Hotels", commonModifierResponse, "city",filterCountRequest, "");
        filterCountRequest.getRequestDetails().setSiteDomain("ae");
        filterCountRequest.getRequestDetails().getTrafficSource().setSource(null);
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "DESKTOP", "B2C", "Hotels", commonModifierResponse, "city", filterCountRequest, "");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "ANDROID", "B2C", "Hotels", commonModifierResponse, "city",filterCountRequest, "");
        filterCountRequest.getSearchCriteria().setCountryCode("in");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "IOS", "B2C", "Hotels", commonModifierResponse, "city",filterCountRequest, "");
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "PWA", "B2C", "Hotels", commonModifierResponse, "city",filterCountRequest, "");
        filterCountRequest.getRequestDetails().setSiteDomain(null);
        ReflectionTestUtils.invokeMethod(filterFactory, "getFilterConfiguration", "PWA", "B2C", "Dayuse", commonModifierResponse, "city",filterCountRequest, "");
    }

    @Test
    public void testModifyFilterConfigFromExperiments_SpotlightExperimentFalse() {
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put(ExperimentKeys.MMT_SPOTLIGHT.getKey(), Constants.FALSE);

        FilterConfigCategory toggleFiltersCategory = mock(FilterConfigCategory.class);
        LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>> groups = mock(LinkedHashMap.class);
        LinkedHashMap<String, FilterConfigDetail> spotlightGroup = mock(LinkedHashMap.class);
        spotlightGroup.put(FilterGroup.SPOTLIGHT.name(), new FilterConfigDetail());
        groups.put(FilterGroup.SPOTLIGHT.name(), spotlightGroup);

        when(filterConfiguration.getFilters()).thenReturn(filters);

        filterFactory.modifyFilterConfigFromExperiments(filterConfiguration, expDataMap);

        assertFalse(groups.containsKey(FilterGroup.SPOTLIGHT.name()));
    }

    @Test
    public void testModifyFilterConfigFromExperiments_SpotlightExperimentTrue() {
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put(ExperimentKeys.MMT_SPOTLIGHT.getKey(), Constants.TRUE);

        FilterConfigCategory toggleFiltersCategory = mock(FilterConfigCategory.class);
        LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>> groups = new LinkedHashMap<>();
        LinkedHashMap<String, FilterConfigDetail> spotlightGroup = new LinkedHashMap<>();
        spotlightGroup.put(FilterGroup.SPOTLIGHT.name(), new FilterConfigDetail());
        groups.put(FilterGroup.SPOTLIGHT.name(), spotlightGroup);

        when(filterConfiguration.getFilters()).thenReturn(filters);

        filterFactory.modifyFilterConfigFromExperiments(filterConfiguration, expDataMap);

        assertTrue(groups.containsKey(FilterGroup.SPOTLIGHT.name()));
    }

    // Test cases for getFilterConfigurationV2 method
    @Test
    public void testGetFilterConfigurationV2_MyPartnerRequest() {
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setProfileType("BUSINESS");
        extendedUser.setAffiliateId("123");
        commonModifierResponse.setExtendedUser(extendedUser);
        
        FilterCountRequest filterRequest = createFilterCountRequest();
        UserCohort userCohort = new UserCohort();
        userCohort.setSearchContext("domestic");
        userCohort.setUserSegment("premium");
        userCohort.setCityGroup("tier1");
        
        ReflectionTestUtils.setField(filterFactory, "baseFilterSettingsV2", "{\"filters\":{},\"title\":\"Test\"}");
        
        Mockito.when(polyglotHelper.translateFilterPageData(Mockito.any(), Mockito.anyString()))
               .thenReturn(new FilterConfigurationV2());
        Mockito.when(filterHelper.getFilterConfigV2(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
               .thenReturn(new FilterConfigurationV2());
        
        FilterConfigurationV2 result = filterFactory.getFilterConfigurationV2(commonModifierResponse, filterRequest, userCohort);
        
        Assert.assertNotNull(result);
        Mockito.verify(filterHelper).getFilterConfigV2(Mockito.any(), Mockito.isNull(), Mockito.anyString(), Mockito.eq("domestic_premium_tier1"), Mockito.anyString());
    }
    
    @Test
    public void testGetFilterConfigurationV2_PremiumRequest() {
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        ExtendedUser extendedUser = new ExtendedUser();
        extendedUser.setProfileType("INDIVIDUAL");
        commonModifierResponse.setExtendedUser(extendedUser);
        
        FilterCountRequest filterRequest = createFilterCountRequest();
        filterRequest.getRequestDetails().setPremium(true);
        UserCohort userCohort = new UserCohort();
        
        ReflectionTestUtils.setField(filterFactory, "baseFilterSettingsV2", "{\"filters\":{},\"title\":\"Test\"}");
        ReflectionTestUtils.setField(filterFactory, "premiumFunnelFilterSettingsV2", "{\"filters\":{},\"title\":\"Premium\"}");
        
        Mockito.when(polyglotHelper.translateFilterPageData(Mockito.any(), Mockito.anyString()))
               .thenReturn(new FilterConfigurationV2());
        Mockito.when(filterHelper.getFilterConfigV2(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
               .thenReturn(new FilterConfigurationV2());
        
        FilterConfigurationV2 result = filterFactory.getFilterConfigurationV2(commonModifierResponse, filterRequest, userCohort);
        
        Assert.assertNotNull(result);
        Mockito.verify(filterHelper).getFilterConfigV2(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.eq("default"), Mockito.anyString());
    }
    
    @Test
    public void testGetFilterConfigurationV2_GCCRequest() {
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        FilterCountRequest filterRequest = createFilterCountRequest();
        filterRequest.getRequestDetails().setSiteDomain("AE");
        UserCohort userCohort = new UserCohort();
        
        ReflectionTestUtils.setField(filterFactory, "baseFilterSettingsV2", "{\"filters\":{},\"title\":\"Test\"}");
        ReflectionTestUtils.setField(filterFactory, "gccFilterSettingsV2", "{\"filters\":{},\"title\":\"GCC\"}");
        
        Mockito.when(polyglotHelper.translateFilterPageData(Mockito.any(), Mockito.anyString()))
               .thenReturn(new FilterConfigurationV2());
        Mockito.when(filterHelper.getFilterConfigV2(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
               .thenReturn(new FilterConfigurationV2());
        
        FilterConfigurationV2 result = filterFactory.getFilterConfigurationV2(commonModifierResponse, filterRequest, userCohort);
        
        Assert.assertNotNull(result);
    }

    @Test
    public void testGetFilterConfigurationV2_JsonParseException() {
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        FilterCountRequest filterRequest = createFilterCountRequest();
        UserCohort userCohort = new UserCohort();
        
        ReflectionTestUtils.setField(filterFactory, "baseFilterSettingsV2", "invalid_json");
        
        Mockito.when(polyglotHelper.translateFilterPageData(Mockito.any(), Mockito.anyString()))
               .thenReturn(new FilterConfigurationV2());
        Mockito.when(filterHelper.getFilterConfigV2(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
               .thenReturn(new FilterConfigurationV2());
        
        FilterConfigurationV2 result = filterFactory.getFilterConfigurationV2(commonModifierResponse, filterRequest, userCohort);
        
        Assert.assertNotNull(result);
    }
    
    // Test cases for getUserCohortTag method
    @Test
    public void testGetUserCohortTag_AllFields() {
        UserCohort userCohort = new UserCohort();
        userCohort.setSearchContext("Domestic");
        userCohort.setUserSegment("Premium");
        userCohort.setCityGroup("Tier1");
        
        String result = ReflectionTestUtils.invokeMethod(filterFactory, "getUserCohortTag", userCohort);
        
        Assert.assertEquals("domestic_premium_tier1", result);
    }
    
    @Test
    public void testGetUserCohortTag_NullUserCohort() {
        String result = ReflectionTestUtils.invokeMethod(filterFactory, "getUserCohortTag", (UserCohort) null);
        
        Assert.assertEquals("default", result);
    }
    
    @Test
    public void testGetUserCohortTag_EmptyFields() {
        UserCohort userCohort = new UserCohort();
        userCohort.setSearchContext("");
        userCohort.setUserSegment("");
        userCohort.setCityGroup("");
        
        String result = ReflectionTestUtils.invokeMethod(filterFactory, "getUserCohortTag", userCohort);
        
        Assert.assertEquals("default", result);
    }
    
    // Test cases for modifiedFilterConfigWithPolyglotDataV2 method
    @Test
    public void testModifiedFilterConfigWithPolyglotDataV2_ValidJson() {
        String filterJson = "{\"filters\":{},\"title\":\"Test Filter\"}";
        String funnelSource = "HOTELS";
        
        FilterConfigurationV2 mockConfig = new FilterConfigurationV2();
        Mockito.when(polyglotHelper.translateFilterPageData(Mockito.any(), Mockito.eq(funnelSource)))
               .thenReturn(mockConfig);
        
        FilterConfigurationV2 result = filterFactory.modifiedFilterConfigWithPolyglotDataV2(filterJson, funnelSource);
        
        Assert.assertNotNull(result);
        Mockito.verify(polyglotHelper).translateFilterPageData(Mockito.any(), Mockito.eq(funnelSource));
    }
    
    @Test
    public void testModifiedFilterConfigWithPolyglotDataV2_InvalidJson() {
        String filterJson = "invalid_json";
        String funnelSource = "HOTELS";
        
        FilterConfigurationV2 result = filterFactory.modifiedFilterConfigWithPolyglotDataV2(filterJson, funnelSource);
        
        // The method still calls translateFilterPageData even when JSON parsing fails (with null fConfig)
        Assert.assertNull(result);
        Mockito.verify(polyglotHelper, Mockito.times(1)).translateFilterPageData(Mockito.isNull(), Mockito.eq(funnelSource));
    }
    
    // Test cases for getCompositeFilterConfig method
    @Test
    public void testGetCompositeFilterConfig() {
        Map<String, List<Filter>> mockCompositeMapping = new LinkedHashMap<>();
        List<Filter> filters = new ArrayList<>();
        Filter filter = new Filter();
        filter.setTitle("test-filter");
        filter.setFilterValue("test-value");
        filters.add(filter);
        mockCompositeMapping.put("category1", filters);
        
        ReflectionTestUtils.setField(filterFactory, "compositeFilterMapping", mockCompositeMapping);
        
        Map<String, List<Filter>> result = filterFactory.getCompositeFilterConfig();
        
        Assert.assertNotNull(result);
        Assert.assertEquals(mockCompositeMapping, result);
        Assert.assertEquals(1, result.size());
        Assert.assertTrue(result.containsKey("category1"));
        Assert.assertEquals(1, result.get("category1").size());
        Assert.assertEquals("test-filter", result.get("category1").get(0).getTitle());
        Assert.assertEquals("test-value", result.get("category1").get(0).getFilterValue());
    }
    
    @Test
    public void testGetCompositeFilterConfig_NullMapping() {
        ReflectionTestUtils.setField(filterFactory, "compositeFilterMapping", null);
        
        Map<String, List<Filter>> result = filterFactory.getCompositeFilterConfig();
        
        Assert.assertNull(result);
    }
    
    // Helper method to create FilterCountRequest
    private FilterCountRequest createFilterCountRequest() {
        FilterCountRequest request = new FilterCountRequest();
        request.setClient("ANDROID");
        
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setIdContext("B2C");
        requestDetails.setFunnelSource("HOTELS");
        requestDetails.setSiteDomain("IN");
        requestDetails.setPremium(false);
        request.setRequestDetails(requestDetails);
        
        SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
        searchCriteria.setCountryCode("IN");
        request.setSearchCriteria(searchCriteria);
        
        return request;
    }

}
