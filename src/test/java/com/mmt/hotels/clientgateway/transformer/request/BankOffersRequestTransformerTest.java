package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.BankOffersRequestCG;
import com.mmt.hotels.clientgateway.transformer.factory.MobLandingFactory;
import com.mmt.hotels.clientgateway.transformer.request.desktop.BankOffersRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.desktop.MobLandingRequestTransformerDesktop;
import com.mmt.hotels.pojo.request.landing.BankOffersRequest;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class BankOffersRequestTransformerTest {

    @Mock
    MobLandingFactory mobLandingFactory;

    @Mock
    MobLandingRequestTransformerDesktop mobLandingRequestTransformerDesktop;

    @InjectMocks
    BankOffersRequestTransformerDesktop bankOffersRequestTransformerDesktop;

    @Test
    public void convertBankOffersRequest_shouldReturnBankOffersRequest_whenValidInput() {
        ReflectionTestUtils.setField(mobLandingFactory, "mobLandingRequestTransformerDesktop", mobLandingRequestTransformerDesktop);
        ReflectionTestUtils.setField(bankOffersRequestTransformerDesktop, "mobLandingFactory", mobLandingFactory);
        Mockito.when(mobLandingFactory.getRequestService(Mockito.any())).thenReturn(mobLandingRequestTransformerDesktop);
        Mockito.doNothing().when(mobLandingRequestTransformerDesktop).convertMobLandingRequest(Mockito.any(), Mockito.any(), Mockito.any());
        BankOffersRequestCG requestCG = new BankOffersRequestCG();
        requestCG.setClient("DESKTOP");
        requestCG.setDeltaDays(5);
        BankOffersRequest result = bankOffersRequestTransformerDesktop.convertBankOffersRequest(requestCG, new CommonModifierResponse());
        Assert.assertNotNull(result);
        Assert.assertEquals(requestCG.getDeltaDays(), result.getDeltaDays());
    }

    @Test
    public void convertBankOffersRequest_shouldHandleNullCommonModifierResponseGracefully() {
        ReflectionTestUtils.setField(mobLandingFactory, "mobLandingRequestTransformerDesktop", mobLandingRequestTransformerDesktop);
        ReflectionTestUtils.setField(bankOffersRequestTransformerDesktop, "mobLandingFactory", mobLandingFactory);
        Mockito.when(mobLandingFactory.getRequestService(Mockito.any())).thenReturn(mobLandingRequestTransformerDesktop);
        Mockito.doNothing().when(mobLandingRequestTransformerDesktop).convertMobLandingRequest(Mockito.any(), Mockito.any(), Mockito.any());
        BankOffersRequestCG requestCG = new BankOffersRequestCG();
        requestCG.setClient("DESKTOP");
        requestCG.setDeltaDays(5);
        BankOffersRequest result = bankOffersRequestTransformerDesktop.convertBankOffersRequest(requestCG, null);
        Assert.assertNotNull(result);
        Assert.assertEquals(requestCG.getDeltaDays(), result.getDeltaDays());
    }

    @Test
    public void convertBankOffersRequest_shouldHandleEmptyClientGracefully() {
        ReflectionTestUtils.setField(mobLandingFactory, "mobLandingRequestTransformerDesktop", mobLandingRequestTransformerDesktop);
        ReflectionTestUtils.setField(bankOffersRequestTransformerDesktop, "mobLandingFactory", mobLandingFactory);
        Mockito.when(mobLandingFactory.getRequestService(Mockito.any())).thenReturn(mobLandingRequestTransformerDesktop);
        Mockito.doNothing().when(mobLandingRequestTransformerDesktop).convertMobLandingRequest(Mockito.any(), Mockito.any(), Mockito.any());
        BankOffersRequestCG requestCG = new BankOffersRequestCG();
        requestCG.setClient("");
        requestCG.setDeltaDays(5);
        BankOffersRequest result = bankOffersRequestTransformerDesktop.convertBankOffersRequest(requestCG, new CommonModifierResponse());
        Assert.assertNotNull(result);
        Assert.assertEquals(requestCG.getDeltaDays(), result.getDeltaDays());
    }

    @Test
    public void convertBankOffersRequest_shouldHandleNegativeDeltaDays() {
        ReflectionTestUtils.setField(mobLandingFactory, "mobLandingRequestTransformerDesktop", mobLandingRequestTransformerDesktop);
        ReflectionTestUtils.setField(bankOffersRequestTransformerDesktop, "mobLandingFactory", mobLandingFactory);
        Mockito.when(mobLandingFactory.getRequestService(Mockito.any())).thenReturn(mobLandingRequestTransformerDesktop);
        Mockito.doNothing().when(mobLandingRequestTransformerDesktop).convertMobLandingRequest(Mockito.any(), Mockito.any(), Mockito.any());
        BankOffersRequestCG requestCG = new BankOffersRequestCG();
        requestCG.setClient("DESKTOP");
        requestCG.setDeltaDays(-5);
        BankOffersRequest result = bankOffersRequestTransformerDesktop.convertBankOffersRequest(requestCG, new CommonModifierResponse());
        Assert.assertNotNull(result);
        Assert.assertEquals(requestCG.getDeltaDays(), result.getDeltaDays());
    }
}
