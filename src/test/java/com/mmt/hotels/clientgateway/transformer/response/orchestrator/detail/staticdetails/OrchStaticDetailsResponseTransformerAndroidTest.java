package com.mmt.hotels.clientgateway.transformer.response.orchestrator.detail.staticdetails;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gommt.hotels.orchestrator.detail.model.response.content.HotelMetaData;
import com.gommt.hotels.orchestrator.detail.model.response.content.PropertyDetails;
import com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffInfo;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.propertymanager.config.PropertyManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.mmt.hotels.clientgateway.constants.Constants.HIDDEN_GEM;
import static com.mmt.hotels.clientgateway.constants.Constants.LUXE_ICON_APPS;
import static com.mmt.hotels.clientgateway.constants.Constants.MMT_VALUE_STAYS;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrchStaticDetailsResponseTransformerAndroidTest {

    @InjectMocks
    private OrchStaticDetailsResponseTransformerAndroid transformer;

    @Mock
    private CommonConfigConsul commonConfigConsul;

    @Mock
    private PropertyManager propertyManager;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    private Utility utility;

    @Mock
    private CommonConfig commonConfig;

    private ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(transformer, "starHostIconApp", "https://test.starhost.icon.app");
    }

    @Test
    void testInitWithConsulEnabled() throws Exception {
        // Given
        Map<String, String> mockCardTitleMap = new HashMap<>();
        mockCardTitleMap.put("key1", "value1");
        when(commonConfigConsul.getCardTitleMap()).thenReturn(mockCardTitleMap);

        // When
        transformer.init();

        // Then
        verify(commonConfigConsul).getCardTitleMap();
        Map<String, String> cardTitleMap = (Map<String, String>) ReflectionTestUtils.getField(transformer, "cardTitleMap");
        assertEquals(mockCardTitleMap, cardTitleMap);
    }

    @Test
    void testBuildCardTitleMapWithValidData() {
        // Given
        Map<String, String> mockCardTitleMap = new HashMap<>();
        mockCardTitleMap.put("key1", "value1");
        mockCardTitleMap.put("key2", "value2");
        ReflectionTestUtils.setField(transformer, "cardTitleMap", mockCardTitleMap);
        
        when(polyglotService.getTranslatedData("value1")).thenReturn("translated_value1");
        when(polyglotService.getTranslatedData("value2")).thenReturn("translated_value2");

        // When
        Map<String, String> result = transformer.buildCardTitleMap();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("translated_value1", result.get("key1"));
        assertEquals("translated_value2", result.get("key2"));
        verify(polyglotService, times(2)).getTranslatedData(anyString());
    }

    @Test
    void testBuildCardTitleMapWithEmptyMap() {
        // Given
        ReflectionTestUtils.setField(transformer, "cardTitleMap", new HashMap<>());

        // When
        Map<String, String> result = transformer.buildCardTitleMap();

        // Then
        assertNull(result);
        verify(polyglotService, never()).getTranslatedData(anyString());
    }

    @Test
    void testBuildCardTitleMapWithNullMap() {
        // Given
        ReflectionTestUtils.setField(transformer, "cardTitleMap", null);

        // When
        Map<String, String> result = transformer.buildCardTitleMap();

        // Then
        assertNull(result);
        verify(polyglotService, never()).getTranslatedData(anyString());
    }

    @Test
    void testBuildCardTitleMapWithException() {
        // Given
        Map<String, String> mockCardTitleMap = new HashMap<>();
        mockCardTitleMap.put("key1", "value1");
        ReflectionTestUtils.setField(transformer, "cardTitleMap", mockCardTitleMap);
        
        when(polyglotService.getTranslatedData("value1")).thenThrow(new RuntimeException("Translation error"));

        // When
        Map<String, String> result = transformer.buildCardTitleMap();

        // Then
        assertNull(result);
        verify(polyglotService).getTranslatedData("value1");
    }

    @Test
    void testAddTitleData() {
        // Given
        com.mmt.hotels.clientgateway.response.staticdetail.HotelResult hotelResult = 
            new com.mmt.hotels.clientgateway.response.staticdetail.HotelResult();
        String countryCode = "IN";
        boolean isNewDetailPageDesktop = true;

        // When
        transformer.addTitleData(hotelResult, countryCode, isNewDetailPageDesktop);

        // Then - Method is empty in implementation, just verify it doesn't throw exception
        assertDoesNotThrow(() -> transformer.addTitleData(hotelResult, countryCode, isNewDetailPageDesktop));
    }

    @Test
    void testGetLuxeIcon() {
        // When
        String result = transformer.getLuxeIcon();

        // Then
        assertEquals(LUXE_ICON_APPS, result);
    }

    @Test
    void testConvertStaffInfoWithStarHost() {
        // Given
        StaffInfo staffInfo = new StaffInfo();
        staffInfo.setIsStarHost(true);

        // When
        com.mmt.hotels.model.response.staticdata.StaffInfo result = transformer.convertStaffInfo(staffInfo);

        // Then
        assertNotNull(result);
        assertEquals("https://test.starhost.icon.app", staffInfo.getStarHostIconUrl());
    }

    @Test
    void testConvertStaffInfoWithoutStarHost() {
        // Given
        StaffInfo staffInfo = new StaffInfo();
        staffInfo.setIsStarHost(false);

        // When
        com.mmt.hotels.model.response.staticdata.StaffInfo result = transformer.convertStaffInfo(staffInfo);

        // Then
        assertNotNull(result);
        assertNull(staffInfo.getStarHostIconUrl());
    }

    @Test
    void testConvertStaffInfoWithNullStaffInfo() {
        // When
        com.mmt.hotels.model.response.staticdata.StaffInfo result = transformer.convertStaffInfo(null);

        // Then
        assertNull(result);
    }

    @Test
    void testConvertStaffInfoWithNullIsStarHost() {
        // Given
        StaffInfo staffInfo = new StaffInfo();
        staffInfo.setIsStarHost(null);

        // When
        com.mmt.hotels.model.response.staticdata.StaffInfo result = transformer.convertStaffInfo(staffInfo);

        // Then
        assertNotNull(result);
        assertNull(staffInfo.getStarHostIconUrl());
    }

    @Test
    void testBuildCategoryIconWithValueStays() {
        // Given
        HotelMetaData hotelMetaData = createHotelMetaData(new HashSet<>(Arrays.asList(MMT_VALUE_STAYS)));
        boolean isCorpIdContext = false;
        Map<String, String> expDataMap = new HashMap<>();

        // When
        String result = transformer.buildCategoryIcon(hotelMetaData, isCorpIdContext, expDataMap);

        // Then
        assertEquals("https://promos.makemytrip.com/Growth/Images/B2C/mmt_vs_details.png", result);
    }

    @Test
    void testBuildCategoryIconWithValueStaysAndCorpContext() {
        // Given
        HotelMetaData hotelMetaData = createHotelMetaData(new HashSet<>(Arrays.asList(MMT_VALUE_STAYS)));
        boolean isCorpIdContext = true;
        Map<String, String> expDataMap = new HashMap<>();

        // When
        String result = transformer.buildCategoryIcon(hotelMetaData, isCorpIdContext, expDataMap);

        // Then
        assertEquals(null, result);
    }

    @Test
    void testBuildCategoryIconWithHiddenGem() {
        // Given
        HotelMetaData hotelMetaData = createHotelMetaData(new HashSet<>(Arrays.asList(HIDDEN_GEM)));
        boolean isCorpIdContext = false;
        Map<String, String> expDataMap = new HashMap<>();
        when(utility.hasHiddenGemExpEnabled(expDataMap)).thenReturn(true);

        // When
        String result = transformer.buildCategoryIcon(hotelMetaData, isCorpIdContext, expDataMap);

        // Then
        assertEquals("https://promos.makemytrip.com/Growth/Images/B2C/mmt_hg_details.png", result);
        verify(utility).hasHiddenGemExpEnabled(expDataMap);
    }

    @Test
    void testBuildCategoryIconWithHiddenGemButExpDisabled() {
        // Given
        HotelMetaData hotelMetaData = createHotelMetaData(new HashSet<>(Arrays.asList(HIDDEN_GEM)));
        boolean isCorpIdContext = false;
        Map<String, String> expDataMap = new HashMap<>();
        when(utility.hasHiddenGemExpEnabled(expDataMap)).thenReturn(false);

        // When
        String result = transformer.buildCategoryIcon(hotelMetaData, isCorpIdContext, expDataMap);

        // Then
        assertEquals(null, result);
        verify(utility).hasHiddenGemExpEnabled(expDataMap);
    }

    @Test
    void testBuildCategoryIconWithEmptyCategories() {
        // Given
        HotelMetaData hotelMetaData = createHotelMetaData(new HashSet<>());
        boolean isCorpIdContext = false;
        Map<String, String> expDataMap = new HashMap<>();

        // When
        String result = transformer.buildCategoryIcon(hotelMetaData, isCorpIdContext, expDataMap);

        // Then
        assertEquals(null, result);
    }

    @Test
    void testBuildCategoryIconWithNullCategories() {
        // Given
        HotelMetaData hotelMetaData = new HotelMetaData();
        PropertyDetails propertyDetails = new PropertyDetails();
        propertyDetails.setCategories(null);
        hotelMetaData.setPropertyDetails(propertyDetails);
        boolean isCorpIdContext = false;
        Map<String, String> expDataMap = new HashMap<>();

        // When
        String result = transformer.buildCategoryIcon(hotelMetaData, isCorpIdContext, expDataMap);

        // Then
        assertEquals(null, result);
    }

    @Test
    void testBuildCategoryIconWithOtherCategories() {
        // Given
        HotelMetaData hotelMetaData = createHotelMetaData(new HashSet<>(Arrays.asList("OTHER_CATEGORY")));
        boolean isCorpIdContext = false;
        Map<String, String> expDataMap = new HashMap<>();

        // When
        String result = transformer.buildCategoryIcon(hotelMetaData, isCorpIdContext, expDataMap);

        // Then
        assertEquals(null, result);
    }

    @Test
    void testBuildWeaverResponse() throws Exception {
        // Given
        JsonNode weaverResponse = objectMapper.createObjectNode();

        // When
        JsonNode result = transformer.buildWeaverResponse(weaverResponse);

        // Then
        assertNotNull(result);
        assertEquals(weaverResponse, result);
        verify(utility).updateWeaverResponse(weaverResponse);
    }

    @Test
    void testBuildWeaverResponseWithNull() {
        // When
        JsonNode result = transformer.buildWeaverResponse(null);

        // Then
        assertNull(result);
        verify(utility).updateWeaverResponse(null);
    }

    // Helper method to create HotelMetaData with categories
    private HotelMetaData createHotelMetaData(Set<String> categories) {
        HotelMetaData hotelMetaData = new HotelMetaData();
        PropertyDetails propertyDetails = new PropertyDetails();
        propertyDetails.setCategories(categories);
        hotelMetaData.setPropertyDetails(propertyDetails);
        return hotelMetaData;
    }
} 