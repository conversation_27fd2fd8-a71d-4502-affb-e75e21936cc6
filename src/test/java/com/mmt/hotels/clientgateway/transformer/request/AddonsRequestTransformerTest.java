package com.mmt.hotels.clientgateway.transformer.request;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.mmt.hotels.model.request.GuestCount;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.AvailRoomsSearchCriteria;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchAddonsCriteria;
import com.mmt.hotels.clientgateway.request.SearchAddonsRequest;
import com.mmt.hotels.clientgateway.request.TrafficSource;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.model.request.addon.GetAddonsRequest;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class AddonsRequestTransformerTest {

	@InjectMocks
	AddonsRequestTransformer addonsRequestTransformer;

	@Test
	public void testConvertSearchAddonsRequest(){
		SearchAddonsRequest searchAddonsRequest = new SearchAddonsRequest();

		searchAddonsRequest.setDeviceDetails(new DeviceDetails());

		SearchAddonsCriteria searchCriteria = new SearchAddonsCriteria();
		searchCriteria.setRoomCriteria(getRoomCriteria());
		searchAddonsRequest.setSearchCriteria(searchCriteria );

		RequestDetails requestDetails = new RequestDetails();
		TrafficSource trafficSource = new TrafficSource();
		requestDetails.setTrafficSource(trafficSource );
		searchAddonsRequest.setRequestDetails(requestDetails );

		CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
		ExtendedUser extendedUser = new ExtendedUser();
		commonModifierResponse.setExtendedUser(extendedUser );

		GetAddonsRequest req = addonsRequestTransformer.convertSearchAddonsRequest(searchAddonsRequest , commonModifierResponse );
		Assert.assertNotNull(req);
	}

	private List<AvailRoomsSearchCriteria> getRoomCriteria() {
		List<AvailRoomsSearchCriteria> roomCriteria = new ArrayList<>();
		AvailRoomsSearchCriteria crtria = new AvailRoomsSearchCriteria();
		List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
		RoomStayCandidate rmstyCandt = new RoomStayCandidate();
		rmstyCandt.setAdultCount(1);
		roomStayCandidates.add(rmstyCandt );
		crtria.setRoomStayCandidates(roomStayCandidates );
		roomCriteria.add(crtria );
		return roomCriteria;
	}

	@Test
	public void buildTrafficSource_shouldReturnTrafficSourceWithCorrectValues() {
		com.mmt.hotels.clientgateway.request.TrafficSource trafficSource = new com.mmt.hotels.clientgateway.request.TrafficSource();
		trafficSource.setSource("WEB");
		trafficSource.setType("DIRECT");
		trafficSource.setAud("test-audience");

		com.mmt.hotels.model.request.TrafficSource result = ReflectionTestUtils.invokeMethod(addonsRequestTransformer, "buildTrafficSource", trafficSource);

		Assert.assertNotNull(result);
		Assert.assertEquals("WEB", result.getSource());
		Assert.assertEquals("DIRECT", result.getType());
		Assert.assertEquals("test-audience", result.getAud());
	}

	@Test
	public void buildGuestCounts() {
		com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidateCG = new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
		roomStayCandidateCG.setAdultCount(1);
		roomStayCandidateCG.setChildAges(Arrays.asList(5, 8));

		List<GuestCount> result = ReflectionTestUtils.invokeMethod(addonsRequestTransformer, "buildGuestCounts", roomStayCandidateCG);

		Assert.assertNotNull(result);
		Assert.assertEquals(1, result.size());
		Assert.assertEquals("1", result.get(0).getCount());
		Assert.assertTrue(CollectionUtils.isNotEmpty(result.get(0).getAges()));
	}

	@Test
	public void populateDeviceDetails_shouldSetAllDeviceDetailsCorrectly() {
		GetAddonsRequest getAddonsRequest = new GetAddonsRequest();
		DeviceDetails deviceDetails = new DeviceDetails();
		deviceDetails.setAppVersion("1.0.0");
		deviceDetails.setBookingDevice("MOBILE");
		deviceDetails.setDeviceId("12345");
		deviceDetails.setDeviceType("ANDROID");

		ReflectionTestUtils.invokeMethod(addonsRequestTransformer, "populateDeviceDetails", getAddonsRequest, deviceDetails);

		Assert.assertEquals("1.0.0", getAddonsRequest.getAppVersion());
		Assert.assertEquals("MOBILE", getAddonsRequest.getBookingDevice());
		Assert.assertEquals("12345", getAddonsRequest.getDeviceId());
		Assert.assertEquals("ANDROID", getAddonsRequest.getDeviceType());
	}
}
