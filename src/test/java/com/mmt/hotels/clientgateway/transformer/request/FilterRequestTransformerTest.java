package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.filter.EvaluateFilterRankOrderRequest;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.Assert;

import java.util.*;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class FilterRequestTransformerTest {
    
    @InjectMocks
    FilterRequestTransformer filterRequestTransformer;

    @Mock
    Utility utility;
    
    @Test
    public void convertFilterRequestTest() {
        FilterCountRequest request = new FilterCountRequest();
        request.setRequestDetails(new RequestDetails());
        request.setDeviceDetails(new DeviceDetails());
        request.setExpData("abc");


        request.setRequestDetails(new RequestDetails());
        request.getRequestDetails().setTrafficSource(new TrafficSource());
        request.getRequestDetails().setLoggedIn(false);
        request.getRequestDetails().setSrLng(10d);
        request.getRequestDetails().setSrLat(10d);
        request.getRequestDetails().setCouponCount(2);

        request.setSearchCriteria(new SearchHotelsCriteria());
        request.getSearchCriteria().setRoomStayCandidates(new ArrayList<RoomStayCandidate>());
        request.getSearchCriteria().getRoomStayCandidates().add(new RoomStayCandidate());
        request.getSearchCriteria().getRoomStayCandidates().get(0).setAdultCount(2);

        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        SemanticSearchDetails semanticSearchDetails = new SemanticSearchDetails();
        semanticSearchDetails.setQueryText("some query text");
        semanticSearchDetails.setSemanticData("some semantic data");
        request.getRequestDetails().setSemanticSearchDetails(semanticSearchDetails);

        Assert.notNull(filterRequestTransformer.convertSearchRequest(request,commonModifierResponse));
    }

    @Test
    public void setInitialCohortIdTest() {
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        filterCountRequest.setInitialCohortId("initialCohortId");
        SearchWrapperInputRequest searchWrapperInputRequest = new SearchWrapperInputRequest();
        ReflectionTestUtils.invokeMethod(filterRequestTransformer, "setInitialCohortId", filterCountRequest, searchWrapperInputRequest);
        assertNotNull(searchWrapperInputRequest.getInitialCohortId());
    }

    @Test
    public void buildEvaluateFilterRankOrderRequestTest() {
        EvaluateFilterRankOrderRequest evaluateFilterRankOrderRequest = filterRequestTransformer.buildEvaluateFilterRankOrderRequest(new SearchWrapperInputRequest());
        assertNotNull(evaluateFilterRankOrderRequest);
        assertNotNull(evaluateFilterRankOrderRequest.getFilterAppliedData());
        assertTrue(CollectionUtils.isEmpty(evaluateFilterRankOrderRequest.getFilterAppliedData().getAppliedFilters()));

        SearchWrapperInputRequest searchWrapperInputRequest = new SearchWrapperInputRequest();
        searchWrapperInputRequest.setAppliedFilterMap(new HashMap<>());
        Set<Filter> filters = new HashSet<>();
        Filter filter = new Filter();
        filter.setFilterGroup(FilterGroup.HOTEL_CATEGORY);
        filter.setFilterValue("MMT_VALUE_STAYS");
        filters.add(filter);

        com.mmt.hotels.model.request.RoomStayCandidate roomStayCandidate = new com.mmt.hotels.model.request.RoomStayCandidate();
        roomStayCandidate.setRooms(1);
        List<GuestCount> guestCounts = new ArrayList<>();
        GuestCount guestCount = new GuestCount();
        guestCount.setCount("2");
        guestCounts.add(guestCount);
        roomStayCandidate.setGuestCounts(guestCounts);
        List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        roomStayCandidates.add(roomStayCandidate);
        searchWrapperInputRequest.setRoomStayCandidates(roomStayCandidates);
        searchWrapperInputRequest.getAppliedFilterMap().put(FilterGroup.HOTEL_CATEGORY, filters);
        evaluateFilterRankOrderRequest = filterRequestTransformer.buildEvaluateFilterRankOrderRequest(searchWrapperInputRequest);
        assertNotNull(evaluateFilterRankOrderRequest);
        assertNotNull(evaluateFilterRankOrderRequest.getFilterAppliedData());
        assertTrue(CollectionUtils.isNotEmpty(evaluateFilterRankOrderRequest.getFilterAppliedData().getAppliedFilters()));
    }
}
