package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.da.Inclusion;
import com.mmt.hotels.clientgateway.response.BookedInclusion;
import com.mmt.hotels.clientgateway.response.IconType;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class InclusionHelperTest {

    @Mock
    private PolyglotService polyglotService;

    @InjectMocks
    private InclusionHelper inclusionHelper;

    // Test constants
    private static final String MEAL_ICON_URL = "https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/meal.png";
    private static final String DOT_ICON_URL = "dot_icon.png";
    private static final String FREE_CHILD_ICON_URL = "free_child_icon.png";
    private static final String PAH_GCC_TEXT = "PAH GCC Text";
    private static final String CUSTOM_ICON_URL = "custom_icon.png";

    // Test translations
    private static final String PAH_WITHOUT_CC_TRANSLATED = "PAH Without CC";
    private static final String PAH_WITH_CC_TRANSLATED = "PAH With CC";
    private static final String PAH_GCC_TRANSLATED = "PAH GCC";
    private static final String FCZPN_TRANSLATED = "Free Cancellation";
    private static final String LOS_BENEFIT_TRANSLATED = "Length of Stay Benefit";

    @Before
    public void setUp() {
        // Set up @Value injected fields using ReflectionTestUtils
        ReflectionTestUtils.setField(inclusionHelper, "dotIconUrl", DOT_ICON_URL);
        ReflectionTestUtils.setField(inclusionHelper, "freeChildInclusionIcon", FREE_CHILD_ICON_URL);
        ReflectionTestUtils.setField(inclusionHelper, "pahGccText", PAH_GCC_TEXT);

        // Setup polyglot service responses
        when(polyglotService.getTranslatedData("PAH_WITHOUT_CC_TEXT")).thenReturn(PAH_WITHOUT_CC_TRANSLATED);
        when(polyglotService.getTranslatedData("PAH_WITH_CC_TEXT")).thenReturn(PAH_WITH_CC_TRANSLATED);
        when(polyglotService.getTranslatedData("PAH_GCC_TEXT")).thenReturn(PAH_GCC_TRANSLATED);
        when(polyglotService.getTranslatedData("FCZPN_TEXT")).thenReturn(FCZPN_TRANSLATED);
        when(polyglotService.getTranslatedData("LOS_BENEFIT_TEXT")).thenReturn(LOS_BENEFIT_TRANSLATED);
    }

    // transformInclusions Tests

    @Test
    public void should_ReturnEmptyList_When_InclusionsIsNull() {
        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(null, null, null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_ReturnEmptyList_When_InclusionsIsEmpty() {
        // Given
        List<Inclusion> inclusions = new ArrayList<>();

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(inclusions, null, null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_TransformInclusions_When_ValidInclusionsProvided() {
        // Given
        List<Inclusion> inclusions = createTestInclusions();
        Map<String, String> experimentDataMap = new HashMap<>();
        String region = "US";

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(inclusions, experimentDataMap, region);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        BookedInclusion firstInclusion = result.get(0);
        assertEquals("MEAL_PLAN", firstInclusion.getCode());
        assertEquals("MEAL", firstInclusion.getCategory());
        assertEquals("MEAL_PLAN", firstInclusion.getInclusionCode());
    }

    @Test
    public void should_ApplyMealIconLogic_When_InclusionTypeIsMealPlan() {
        // Given
        Inclusion inclusion = createInclusion("MEAL_PLAN", "MEAL_PLAN", "MEAL", "Premium", "100", "SEGMENT1");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Collections.singletonList(inclusion), null, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals(MEAL_ICON_URL, bookedInclusion.getIconUrl());
        assertEquals(IconType.DEFAULT, bookedInclusion.getIconType());
    }

    @Test
    public void should_ApplyMealIconLogic_When_CategoryIsMeal() {
        // Given
        Inclusion inclusion = createInclusion("BREAKFAST", "BREAKFAST", "MEAL", "Breakfast included", "0", "SEGMENT1");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), null, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals(MEAL_ICON_URL, bookedInclusion.getIconUrl());
    }

    @Test
    public void should_ApplyCustomIcon_When_USPCategoryWithIconUrl() {
        // Given
        Inclusion inclusion = createInclusion("USP_CODE", "USP_TYPE", "USP", "Value", "0", "SEGMENT1");
        inclusion.setIconUrl(CUSTOM_ICON_URL);

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), null, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals(CUSTOM_ICON_URL, bookedInclusion.getIconUrl());
    }

    @Test
    public void should_ApplyBlackSegmentIcon_When_BlackRevampExperimentActive() {
        // Given
        Inclusion inclusion = createInclusion("BLACK_CODE", "BLACK_TYPE", "CATEGORY", "Value", "0", "BLACK");
        inclusion.setIconUrl(CUSTOM_ICON_URL);
        Map<String, String> experimentDataMap = new HashMap<>();
        experimentDataMap.put("blackRevamp", "true");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), experimentDataMap, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals(CUSTOM_ICON_URL, bookedInclusion.getIconUrl());
    }

    @Test
    public void should_NotApplyBlackSegmentIcon_When_BlackRevampExperimentInactive() {
        // Given
        Inclusion inclusion = createInclusion("BLACK_CODE", "BLACK_TYPE", "CATEGORY", "Value", "0", "BLACK");
        inclusion.setIconUrl(CUSTOM_ICON_URL);
        Map<String, String> experimentDataMap = new HashMap<>();
        experimentDataMap.put("blackRevamp", "false");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), experimentDataMap, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals(DOT_ICON_URL, bookedInclusion.getIconUrl());
    }

    @Test
    public void should_ApplyFreeChildIcon_When_TypeIsFreeChild() {
        // Given
        Inclusion inclusion = createInclusion("FREE_CHILD_CODE", "FREE_CHILD", "CATEGORY", "Free child benefit", "0", "SEGMENT1");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), null, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals(FREE_CHILD_ICON_URL, bookedInclusion.getIconUrl());
    }

    @Test
    public void should_ApplyDefaultIcon_When_NoSpecialConditions() {
        // Given
        Inclusion inclusion = createInclusion("REGULAR_CODE", "REGULAR_TYPE", "REGULAR", "Regular value", "0", "SEGMENT1");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), null, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals(DOT_ICON_URL, bookedInclusion.getIconUrl());
        assertEquals(IconType.DEFAULT, bookedInclusion.getIconType());
    }

    @Test
    public void should_ApplyTextConcatenation_When_TextIsShortAndDifferent() {
        // Given
        Inclusion inclusion = createInclusion("SHORT", "LONG_TYPE_VALUE", "CATEGORY", "Long description value", "0", "SEGMENT1");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), null, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals("SHORT - Long description value", bookedInclusion.getText());
    }

    @Test
    public void should_NotConcatenateText_When_TextIsLong() {
        // Given
        Inclusion inclusion = createInclusion("VERY_LONG_CODE_NAME", "TYPE_VALUE", "CATEGORY", "Value", "0", "SEGMENT1");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), null, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals("VERY_LONG_CODE_NAME", bookedInclusion.getText());
    }

    @Test
    public void should_NotConcatenateText_When_TextAndSubTextAreSame() {
        // Given
        Inclusion inclusion = createInclusion("SAME_TEXT", "SAME_TEXT", "CATEGORY", "SAME_TEXT", "0", "SEGMENT1");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), null, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals("SAME_TEXT", bookedInclusion.getText());
    }

    @Test
    public void should_UseTypeAsSubText_When_ValueIsBlank() {
        // Given
        Inclusion inclusion = createInclusion("CODE", "TYPE_VALUE", "CATEGORY", "", "0", "SEGMENT1");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), null, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals("TYPE_VALUE", bookedInclusion.getSubText());
    }

    @Test
    public void should_UseCodeAsSubText_When_ValueAndTypeAreBlank() {
        // Given
        Inclusion inclusion = createInclusion("CODE", "", "CATEGORY", "", "0", "SEGMENT1");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), null, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals("CODE", bookedInclusion.getSubText());
    }

    // Localization Tests

    @Test
    public void should_LocalizePAHWithoutCC_When_CodeMatches() {
        // Given
        Inclusion inclusion = createInclusion("PAH_WITHOUT_CC_TEXT", "TYPE", "CATEGORY", "Value", "0", "SEGMENT1");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), null, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals(PAH_WITHOUT_CC_TRANSLATED, bookedInclusion.getText());
    }

    @Test
    public void should_LocalizePAHWithCC_When_CodeMatches() {
        // Given
        Inclusion inclusion = createInclusion("PAH_WITH_CC_TEXT", "TYPE", "CATEGORY", "Value", "0", "SEGMENT1");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), null, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals(PAH_WITH_CC_TRANSLATED, bookedInclusion.getText());
    }

    @Test
    public void should_LocalizePAHGCC_When_CodeMatches() {
        // Given
        Inclusion inclusion = createInclusion("PAH_GCC_TEXT", "TYPE", "CATEGORY", "Value", "0", "SEGMENT1");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), null, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals(PAH_GCC_TRANSLATED, bookedInclusion.getText());
    }

    @Test
    public void should_LocalizeFCZPN_When_CodeMatches() {
        // Given
        Inclusion inclusion = createInclusion("FCZPN_TEXT", "TYPE", "CATEGORY", "Value", "0", "SEGMENT1");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), null, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals(FCZPN_TRANSLATED, bookedInclusion.getText());
    }

    @Test
    public void should_LocalizeLOSBenefit_When_CodeMatches() {
        // Given
        Inclusion inclusion = createInclusion("LOS_BENEFIT_TEXT", "TYPE", "CATEGORY", "Value", "0", "SEGMENT1");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), null, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals(LOS_BENEFIT_TRANSLATED, bookedInclusion.getText());
    }

    // Region-specific Tests

    @Test
    public void should_ApplyGCCSpecificText_When_RegionIsGCCAndCodeIsPAH() {
        // Given
        Inclusion inclusion = createInclusion("PAH_WITHOUT_CC_TEXT", "TYPE", "CATEGORY", "Value", "0", "SEGMENT1");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), null, "GCC");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals(PAH_GCC_TEXT, bookedInclusion.getText());
    }

    @Test
    public void should_ApplyKSASpecificText_When_RegionIsKSAAndCodeIsPAH() {
        // Given
        Inclusion inclusion = createInclusion("PAH_WITH_CC_TEXT", "TYPE", "CATEGORY", "Value", "0", "SEGMENT1");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), null, "KSA");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals(PAH_GCC_TEXT, bookedInclusion.getText());
    }

    @Test
    public void should_NotApplyGCCSpecificText_When_RegionIsNotGCCOrKSA() {
        // Given
        Inclusion inclusion = createInclusion("PAH_WITHOUT_CC_TEXT", "TYPE", "CATEGORY", "Value", "0", "SEGMENT1");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), null, "US");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals(PAH_WITHOUT_CC_TRANSLATED, bookedInclusion.getText());
    }

    @Test
    public void should_HandleNullExperimentDataMap_When_CheckingBlackRevamp() {
        // Given
        Inclusion inclusion = createInclusion("BLACK_CODE", "BLACK_TYPE", "CATEGORY", "Value", "0", "BLACK");
        inclusion.setIconUrl(CUSTOM_ICON_URL);

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), null, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals(DOT_ICON_URL, bookedInclusion.getIconUrl());
    }

    @Test
    public void should_HandleNullRegion_When_CheckingGCCOrKSA() {
        // Given
        Inclusion inclusion = createInclusion("PAH_WITHOUT_CC_TEXT", "TYPE", "CATEGORY", "Value", "0", "SEGMENT1");

        // When
        List<BookedInclusion> result = inclusionHelper.transformInclusions(Arrays.asList(inclusion), null, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        BookedInclusion bookedInclusion = result.get(0);
        assertEquals(PAH_WITHOUT_CC_TRANSLATED, bookedInclusion.getText());
    }

    // Helper methods for creating test data

    private List<Inclusion> createTestInclusions() {
        List<Inclusion> inclusions = new ArrayList<>();
        
        inclusions.add(createInclusion("MEAL_PLAN", "MEAL_PLAN", "MEAL", "Premium Meal Plan", "100", "SEGMENT1"));
        inclusions.add(createInclusion("WIFI", "FREE_WIFI", "CONNECTIVITY", "Free WiFi", "0", "SEGMENT2"));
        
        return inclusions;
    }

    private Inclusion createInclusion(String code, String type, String category, String value, String amount, String segmentIdentifier) {
        Inclusion inclusion = new Inclusion();
        inclusion.setCode(code);
        inclusion.setType(type);
        inclusion.setCategory(category);
        inclusion.setValue(value);
        inclusion.setAmount(amount);
        inclusion.setSegmentIdentifier(segmentIdentifier);
        return inclusion;
    }
}
