package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.response.affiliate.UpdatedAffiliateFeeResponse;
import com.mmt.hotels.model.affiliate.CreateQuoteResponse;
import com.mmt.hotels.model.affiliate.UpdateAffiliateFeeResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;

@RunWith(MockitoJUnitRunner.class)
public class AffiliateResponseTransformerTest {
    @InjectMocks
    AffiliateResponseTransformer affiliateResponseTransformer;

    @Spy
    CommonResponseTransformer commonResponseTransformer;


    @Test
    public void convertAffiliateFeeUpdateResponseTest() {
        Mockito.when(commonResponseTransformer.buildAffiliateFeeDetails(Mockito.any())).thenReturn(new ArrayList<>());
        UpdatedAffiliateFeeResponse updatedAffiliateFeeResponse = affiliateResponseTransformer.convertAffiliateFeeUpdateResponse(new UpdateAffiliateFeeResponse.Builder().build());
        Assert.assertNotNull(updatedAffiliateFeeResponse);
    }

    @Test
    public void convertAffiliateCreateQuoteResponseTest() {
        CreateQuoteResponse createQuoteResponse = new CreateQuoteResponse.Builder().buildId("id").build();
        Assert.assertNotNull(affiliateResponseTransformer.convertAffiliateCreateQuoteResponse(createQuoteResponse));
    }
    @Test
    public void convertAffiliateFeeUpdateResponseTest_withValidData() {
        // Mock the commonResponseTransformer method
        Mockito.when(commonResponseTransformer.buildAffiliateFeeDetails(Mockito.any())).thenReturn(new ArrayList<>());

        // Create a valid UpdateAffiliateFeeResponse object
        UpdateAffiliateFeeResponse updateAffiliateFeeResponse = new UpdateAffiliateFeeResponse.Builder().build();

        // Call the method
        UpdatedAffiliateFeeResponse updatedAffiliateFeeResponse = affiliateResponseTransformer.convertAffiliateFeeUpdateResponse(updateAffiliateFeeResponse);

        // Assert the results
        Assert.assertNotNull(updatedAffiliateFeeResponse);
//        Assert.assertNotNull(updatedAffiliateFeeResponse.getAffiliateFeeDetails());
    }

    @Test
    public void convertAffiliateFeeUpdateResponseTest_withEmptyAffiliateFeeDetails() {
        // Mock the commonResponseTransformer method to return an empty list
        Mockito.when(commonResponseTransformer.buildAffiliateFeeDetails(Mockito.any())).thenReturn(new ArrayList<>());

        // Create an UpdateAffiliateFeeResponse object with empty details
        UpdateAffiliateFeeResponse updateAffiliateFeeResponse = new UpdateAffiliateFeeResponse.Builder().build();

        // Call the method
        UpdatedAffiliateFeeResponse updatedAffiliateFeeResponse = affiliateResponseTransformer.convertAffiliateFeeUpdateResponse(updateAffiliateFeeResponse);

        // Assert the results
        Assert.assertNotNull(updatedAffiliateFeeResponse);
//        Assert.assertTrue(updatedAffiliateFeeResponse.getAffiliateFeeDetails().isEmpty());
    }

    @Test
    public void convertAffiliateCreateQuoteResponse_withValidData() {
        // Create a valid ModelCreateQuoteResponse object
        com.mmt.hotels.model.affiliate.CreateQuoteResponse modelCreateQuoteResponse = new com.mmt.hotels.model.affiliate.CreateQuoteResponse.Builder().buildId("id").build();

        // Call the method
        com.mmt.hotels.clientgateway.response.affiliate.CreateQuoteResponse createQuoteResponse = affiliateResponseTransformer.convertAffiliateCreateQuoteResponse(modelCreateQuoteResponse);

        // Assert the results
        Assert.assertNotNull(createQuoteResponse);
        Assert.assertEquals("id", createQuoteResponse.getId());
    }

}
