package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.response.BankOffersResponseCG;
import com.mmt.hotels.clientgateway.transformer.response.desktop.BankOffersResponseTransformerDesktop;
import com.mmt.hotels.model.response.errors.Error;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import com.mmt.hotels.pojo.response.bankoffers.BankOffer;
import com.mmt.hotels.pojo.response.bankoffers.BankOffersResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Collections;

@RunWith(MockitoJUnitRunner.class)
public class BankOffersResponseTransformerTest {

    @InjectMocks
    BankOffersResponseTransformerDesktop bankOffersResponseTransformerDesktop;

    @Test
    public void convertBankOffersResponse_shouldReturnBankOffersResponseCG_whenValidResponse() {
        BankOffersResponse response = new BankOffersResponse();
        BankOffer offer = new BankOffer();
        offer.setIconUrl("iconUrl");
        offer.setPromoCode("promoCode");
        offer.setText("text");
        offer.setSubText("subText");
        response.setBankOffersData(Collections.singletonList(offer));
        BankOffersResponseCG result = bankOffersResponseTransformerDesktop.convertBankOffersResponse(response);
        Assert.assertNotNull(result);
        Assert.assertNull(response.getResponseErrors());
        Assert.assertEquals(1, result.getBankOffersData().size());
        Assert.assertEquals(offer.getIconUrl(), result.getBankOffersData().get(0).getIconUrl());
        Assert.assertEquals(offer.getPromoCode(), result.getBankOffersData().get(0).getPromoCode());
        Assert.assertEquals(offer.getText(), result.getBankOffersData().get(0).getText());
        Assert.assertEquals(offer.getSubText(), result.getBankOffersData().get(0).getSubText());
    }

    @Test
    public void convertBankOffersResponse_shouldReturnBankOffersResponseCG_withResponseErrors() {
        BankOffersResponse response = new BankOffersResponse();
        ResponseErrors responseErrors = new ResponseErrors.Builder()
                .buildErrorList(Collections.singletonList(new Error.Builder().buildErrorCode("101", "Error").build()))
                .build();
        response.setResponseErrors(responseErrors);
        BankOffersResponseCG result = bankOffersResponseTransformerDesktop.convertBankOffersResponse(response);
        Assert.assertNotNull(result);
        Assert.assertNull(result.getBankOffersData());
        Assert.assertNotNull(response.getResponseErrors());
        Assert.assertEquals(responseErrors.getErrorList().get(0).getErrorCode(), result.getError().getErrorCode());
        Assert.assertEquals(responseErrors.getErrorList().get(0).getErrorMessage(), result.getError().getErrorMessage());

    }

    // Additional test cases for improved code coverage - appended without modifying existing tests

    @Test
    public void testConvertBankOffersResponseWithNullInput() {
        BankOffersResponseCG result = bankOffersResponseTransformerDesktop.convertBankOffersResponse(null);
        Assert.assertNotNull(result);
    }

    @Test
    public void testConvertBankOffersResponseWithMultipleBankOffers() {
        BankOffersResponse response = new BankOffersResponse();
        
        BankOffer offer1 = new BankOffer();
        offer1.setIconUrl("iconUrl1");
        offer1.setPromoCode("promoCode1");
        offer1.setText("text1");
        offer1.setSubText("subText1");
        
        BankOffer offer2 = new BankOffer();
        offer2.setIconUrl("iconUrl2");
        offer2.setPromoCode("promoCode2");
        offer2.setText("text2");
        offer2.setSubText("subText2");
        
        response.setBankOffersData(java.util.Arrays.asList(offer1, offer2));
        BankOffersResponseCG result = bankOffersResponseTransformerDesktop.convertBankOffersResponse(response);
        
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.getBankOffersData().size());
        Assert.assertEquals("iconUrl1", result.getBankOffersData().get(0).getIconUrl());
        Assert.assertEquals("iconUrl2", result.getBankOffersData().get(1).getIconUrl());
    }

    @Test
    public void testConvertBankOffersResponseWithNullBankOfferFields() {
        BankOffersResponse response = new BankOffersResponse();
        BankOffer offer = new BankOffer();
        // All fields are null by default
        response.setBankOffersData(Collections.singletonList(offer));
        BankOffersResponseCG result = bankOffersResponseTransformerDesktop.convertBankOffersResponse(response);
        
        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.getBankOffersData().size());
        Assert.assertNull(result.getBankOffersData().get(0).getIconUrl());
        Assert.assertNull(result.getBankOffersData().get(0).getPromoCode());
        Assert.assertNull(result.getBankOffersData().get(0).getText());
        Assert.assertNull(result.getBankOffersData().get(0).getSubText());
    }

    @Test
    public void testConvertBankOffersResponseWithMixedNullAndValidOffers() {
        BankOffersResponse response = new BankOffersResponse();
        
        BankOffer validOffer = new BankOffer();
        validOffer.setIconUrl("validIconUrl");
        validOffer.setPromoCode("validPromoCode");
        validOffer.setText("validText");
        validOffer.setSubText("validSubText");
        
        BankOffer nullOffer = new BankOffer();
        // Fields are null by default
        
        response.setBankOffersData(java.util.Arrays.asList(validOffer, nullOffer));
        BankOffersResponseCG result = bankOffersResponseTransformerDesktop.convertBankOffersResponse(response);
        
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.getBankOffersData().size());
        
        // Valid offer should have values
        Assert.assertEquals("validIconUrl", result.getBankOffersData().get(0).getIconUrl());
        Assert.assertEquals("validPromoCode", result.getBankOffersData().get(0).getPromoCode());
        
        // Null offer should have null values
        Assert.assertNull(result.getBankOffersData().get(1).getIconUrl());
        Assert.assertNull(result.getBankOffersData().get(1).getPromoCode());
    }

    @Test
    public void testBankOffersResponseTransformerInitialization() {
        Assert.assertNotNull(bankOffersResponseTransformerDesktop);
    }
}
