package com.mmt.hotels.clientgateway.transformer.response.cardEngine;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.LocationDetail;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.PersonalizedSection;
import com.mmt.hotels.clientgateway.response.searchHotels.SearchHotelsResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@ExtendWith(MockitoExtension.class)
public class ListingCollectionCardEngineResponseTransformerTest {
    @InjectMocks
    ListingCollectionCardEngineResponseTransformer transformer;

    @Mock
    PolyglotService polyglotService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testTransformCardEngineResponse_ValidInput() throws JsonProcessingException {
        String inputJson = "{ \"cardId\": \"123\", \"cardType\": \"type1\", \"cardData\": { \"inlineCollections\": [{ \"title\": \"title1\", \"roundImgUrl\": \"url1\", \"filters\": [{ \"filterGroup\": \"group1\", \"filterValue\": \"value1\", \"isRangeFilter\": true, \"filterRange\": { \"min\": 1, \"max\": 10 } }] }], \"cityCollectionData\": { \"mediaUrl\": \"mediaUrl1\", \"mediaType\": \"IMAGE\" } } }";
        String expectedOutput = "{ \"cardId\": \"123\", \"cardType\": \"type1\", \"cardData\": { \"inlineCollections\": [{ \"title\": \"title1\", \"roundImgUrl\": \"url1\", \"filters\": [{ \"filterGroup\": \"group1\", \"filterValue\": \"value1\", \"isRangeFilter\": true, \"filterRange\": { \"min\": 1, \"max\": 10 } }] }], \"cityCollectionData\": { \"mediaUrl\": \"mediaUrl1\", \"mediaType\": \"IMAGE\" } } }";
        String result = transformer.transformCardEngineResponse(inputJson);
        assertEquals(expectedOutput, result);
    }

    @Test
    public void testTransformCardEngineResponse_MissingOptionalFields() throws JsonProcessingException {
        String inputJson = "{ \"cardId\": \"123\", \"cardType\": \"type1\", \"cardData\": { \"inlineCollections\": [{ \"title\": \"title1\", \"roundImgUrl\": \"url1\" }], \"cityCollectionData\": { \"mediaUrl\": \"mediaUrl1\", \"mediaType\": \"IMAGE\" } } }";
        String expectedOutput = "{ \"cardId\": \"123\", \"cardType\": \"type1\", \"cardData\": { \"inlineCollections\": [{ \"title\": \"title1\", \"roundImgUrl\": \"url1\" }], \"cityCollectionData\": { \"mediaUrl\": \"mediaUrl1\", \"mediaType\": \"IMAGE\" } } }";
        String result = transformer.transformCardEngineResponse(inputJson);
        assertEquals(expectedOutput, result);
    }


    @Test
    public void testUpdateSearchHotelsResponse_ValidData() {
        // Arrange
        SearchHotelsResponse response = new SearchHotelsResponse();
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        response.setPersonalizedSections(createMockSections());
        LocationDetail locationDetail = new LocationDetail();
        locationDetail.setName("Test City");
        response.setLocationDetail(locationDetail);

        String cardEngineResponse = "{ \"cardData\": { \"data\": { \"subcard\": { \"items\": [ { \"title\": \"Luxury\" } ] } } } }";



        // Act
        SearchHotelsResponse result = transformer.updateSearchHotelsResponse(response, cardEngineResponse, searchHotelsRequest);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getPersonalizedSections());
        assertEquals(1, result.getPersonalizedSections().size());
    }

    // Helper methods to create mock data
    private List<PersonalizedSection> createMockSections() {
        PersonalizedSection section = new PersonalizedSection();
        section.setName("RECOMMENDED_HOTELS");
        section.setHeading("Recommended");
        section.setHotels(createMockHotels());
        return Collections.singletonList(section);
    }

    private List<Hotel> createMockHotels() {
        List<Hotel> hotels = new ArrayList<>();
        hotels.add(new Hotel());
        hotels.add(new Hotel());
        return hotels;
    }

    @Test
    public void testGetPersonalizedSectionHeading_ValidInput() throws Exception {
        // Arrange
        String cardEngineResponse = "{ \"cardData\": { \"data\": { \"subcard\": { \"items\": [ { \"title\": \"Luxury\" } ] } } } }";
        SearchHotelsResponse searchHotelsResponse = new SearchHotelsResponse();
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        LocationDetail locationDetail = new LocationDetail();
        locationDetail.setName("Delhi");
        searchHotelsResponse.setLocationDetail(locationDetail);

        //Act
        Method method = ListingCollectionCardEngineResponseTransformer.class
                .getDeclaredMethod("getPersonalizedSectionHeading", String.class, SearchHotelsResponse.class, SearchHotelsRequest.class);
        method.setAccessible(true);
        String heading = (String) method.invoke(transformer, cardEngineResponse, searchHotelsResponse, searchHotelsRequest);

        // Assert
        assertNotNull(heading);
        assertEquals("", heading);
    }


}