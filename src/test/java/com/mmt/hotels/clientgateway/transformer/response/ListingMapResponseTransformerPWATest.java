package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.NullNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.shortstays.CityGeoConfig;
import com.mmt.hotels.clientgateway.restexecutors.CorporateExecutor;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.pwa.ListingMapResponseTransformerPWA;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.response.emi.Emi;
import com.mmt.hotels.model.response.flyfish.RatingSummaryDTO;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.pricing.DisplayPriceBreakDown;
import com.mmt.hotels.model.response.searchwrapper.GeoLocation;
import com.mmt.hotels.model.response.searchwrapper.SoldOutInfo;
import com.mmt.hotels.model.response.staticdata.Address;
import com.mmt.hotels.pojo.response.HotelListingMapResponse;
import com.mmt.hotels.pojo.response.ListingHotelMapEntity;
import com.mmt.model.LocusData;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.ANDROID;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class ListingMapResponseTransformerPWATest {

    private static final Logger logger = LoggerFactory.getLogger(ListingMapResponseTransformerPWATest.class);

    @InjectMocks
    ListingMapResponseTransformerPWA listingMapResponseTransformerPWA;

    @Spy
    CommonResponseTransformer commonResponseTransformer;

    @Spy
    ShortstaysListingMapResponseTransformer shortstaysListingMapResponseTransformer;

    @Spy
    PersuasionUtil persuasionUtil;

    @Mock
    PolyglotService polyglotService;

    @Spy
    Utility utility;

    protected Map<String, String> listingDrivingDurationBucketMap;

    private Map<String, Integer> mediaLimitMap;

    Gson gson = new Gson();

    @Before
    public void setup() {
        ReflectionTestUtils.setField(commonResponseTransformer,"polyglotService",polyglotService);
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("abc {duration} {city_name} {driving_text} {city_text} {date}");
        String buckets = "{\"0-60\":\"LISTING_DRIVING_DURATION_BUCKET_1\",\"60-90\":\"LISTING_DRIVING_DURATION_BUCKET_2\",\"90-120\":\"LISTING_DRIVING_DURATION_BUCKET_3\",\"120-150\":\"LISTING_DRIVING_DURATION_BUCKET_4\",\"150-180\":\"LISTING_DRIVING_DURATION_BUCKET_5\",\"180-240\":\"LISTING_DRIVING_DURATION_BUCKET_6\",\"240-300\":\"LISTING_DRIVING_DURATION_BUCKET_7\",\"300-360\":\"LISTING_DRIVING_DURATION_BUCKET_8\",\"360\":\"LISTING_DRIVING_DURATION_BUCKET_9\"}";
        listingDrivingDurationBucketMap = gson.fromJson(buckets, HashMap.class);
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("abc {duration} {city_name} {driving_text} {city_text} {date}");
        ReflectionTestUtils.setField(commonResponseTransformer, "listingDrivingDurationBucketMap", listingDrivingDurationBucketMap);
        String listingHotelMediaLimit = "{\"ANDROID\":5,\"PWA\":5,\"IOS\":5,\"DESKTOP\":5}";
        mediaLimitMap = gson.fromJson(listingHotelMediaLimit, new TypeToken<Map<String, Integer>>() {
        }.getType());
        ReflectionTestUtils.setField(commonResponseTransformer, "mediaLimitMap", mediaLimitMap);
    }

    @Test
    public void testConvertListingMapResponse() throws JsonProcessingException {

        Assert.assertNull(listingMapResponseTransformerPWA.convertListingMapResponse(null, utility.getExpDataMap(""), null, null, null));
        Assert.assertNotNull(listingMapResponseTransformerPWA.convertListingMapResponse(new HotelListingMapResponse(), utility.getExpDataMap(""), null, null, null));

        HotelListingMapResponse hotelListingMapResponse = new HotelListingMapResponse();
        hotelListingMapResponse.setCityCode("DEL");
        hotelListingMapResponse.setCityName("Delhi");
        hotelListingMapResponse.setCorrelationKey("12345678");
        hotelListingMapResponse.setCountryCode("IN");
        hotelListingMapResponse.setCountryName("IN");
        hotelListingMapResponse.setLastFetchedHotelId("1234");
        hotelListingMapResponse.setNoMoreAvailableHotels(true);
//        hotelListingMapResponse.setResponseErrors(new ResponseErrors());
        hotelListingMapResponse.setStaticHotelCounts(5);
        hotelListingMapResponse.setTotalHotelCounts(5);
        hotelListingMapResponse.setHotelList(new ArrayList<>());
        hotelListingMapResponse.getHotelList().add(new ListingHotelMapEntity());
        hotelListingMapResponse.getHotelList().get(0).setAddress(new Address());
        hotelListingMapResponse.getHotelList().get(0).getAddress().setArea(new ArrayList<String>());
        hotelListingMapResponse.getHotelList().get(0).getAddress().getArea().add("area");
        hotelListingMapResponse.getHotelList().get(0).getAddress().setLine1("abcd");
        hotelListingMapResponse.getHotelList().get(0).getAddress().setLine2("abcd");

        hotelListingMapResponse.getHotelList().get(0).setCityCode("1234");
        hotelListingMapResponse.getHotelList().get(0).setDisplayFare(new DisplayFare());
        hotelListingMapResponse.getHotelList().get(0).getDisplayFare().setDisplayPriceBreakDown(new DisplayPriceBreakDown());
        hotelListingMapResponse.getHotelList().get(0).getDisplayFare().getDisplayPriceBreakDown().setDisplayPrice(12d);
        hotelListingMapResponse.getHotelList().get(0).getDisplayFare().getDisplayPriceBreakDown().setNonDiscountedPrice(12d);
        hotelListingMapResponse.getHotelList().get(0).getDisplayFare().getDisplayPriceBreakDown().setCouponInfo(new BestCoupon());
        hotelListingMapResponse.getHotelList().get(0).getDisplayFare().getDisplayPriceBreakDown().setEmiDetails(new Emi());
        hotelListingMapResponse.getHotelList().get(0).setSoldOutInfo(new SoldOutInfo());

        hotelListingMapResponse.getHotelList().get(0).setGeoLocation(new GeoLocation());
        hotelListingMapResponse.getHotelList().get(0).getGeoLocation().setDistanceMeter(10d);
        hotelListingMapResponse.getHotelList().get(0).getGeoLocation().setLatitude("11");
        hotelListingMapResponse.getHotelList().get(0).getGeoLocation().setLongitude("12");

        hotelListingMapResponse.getHotelList().get(0).setUspShortStayValue("abc");
        hotelListingMapResponse.getHotelList().get(0).setLocationPersuasion(new ArrayList<>());
        hotelListingMapResponse.getHotelList().get(0).getLocationPersuasion().add("abc");

        hotelListingMapResponse.getHotelList().get(0).setId("id");
        hotelListingMapResponse.getHotelList().get(0).setMainImages(new ArrayList<String>());
        hotelListingMapResponse.getHotelList().get(0).getMainImages().add("http://mainImages.com");
        hotelListingMapResponse.getHotelList().get(0).setName("hotel");
        hotelListingMapResponse.getHotelList().get(0).setStarRating(3);
        hotelListingMapResponse.getHotelList().get(0).setIsSoldOut(false);
        hotelListingMapResponse.getHotelList().get(0).setFlyfishReviewSummary(new HashMap<OTA, JsonNode>());
        RatingSummaryDTO ratingSummaryDTO = new RatingSummaryDTO();
        ratingSummaryDTO.setCumulativeRating(new Float(4));
        ratingSummaryDTO.setTotalRatingCount(10);
        ratingSummaryDTO.setTotalReviewsCount(7);
        String ratingJson = "{\n" +
                "  \"cumulativeRating\": 4.5,\n" +
                "  \"totalReviewsCount\": 89,\n" +
                "  \"totalRatingCount\": 162,\n" +
                "  \"best\": [\n" +
                "    {\n" +
                "      \"title\": \"\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"sortingCriterion\": [\n" +
                "    \"Latest first\",\n" +
                "    \"Helpful first\",\n" +
                "    \"Positive first\",\n" +
                "    \"Negative first\"\n" +
                "  ]\n" +
                "}";


        hotelListingMapResponse.getHotelList().add(new ListingHotelMapEntity());
        hotelListingMapResponse.getHotelList().get(0).getFlyfishReviewSummary().put(OTA.MMT, new ObjectMapper().readTree(ratingJson));
        DeviceDetails dd = new DeviceDetails();
        dd.setDeviceId("abc");
        //Mockito.when(shortstaysListingMapResponseTransformer.buildCentralCityGeoDetails(Mockito.any())).thenReturn(new CityGeoConfig());
        //Mockito.when(shortstaysListingMapResponseTransformer.buildAssociatedCitiesGeoConfig(Mockito.any())).thenReturn(new ArrayList<>());
        try {
            Assert.assertNotNull(listingMapResponseTransformerPWA.convertListingMapResponse(hotelListingMapResponse, Mockito.anyMap(), dd, new RequestDetails(), null));
        } catch (Exception e) {
            logger.warn("{}",e.getStackTrace());
        }

        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource(Constants.SHORTSTAYS_FUNNEL);
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(ANDROID);
        hotelListingMapResponse.setLocusData(new LocusData());
        hotelListingMapResponse.getLocusData().setLocusName("12fd");
        hotelListingMapResponse.getHotelList().get(0).setDrivingTime(12.0d);
        hotelListingMapResponse.getHotelList().get(0).setLocationPersuasion(new ArrayList<>());
        hotelListingMapResponse.getHotelList().get(0).getLocationPersuasion().add("adb");
        hotelListingMapResponse.getHotelList().get(0).getDisplayFare().getDisplayPriceBreakDown().setTaxIncluded(true);
        Assert.assertNotNull(listingMapResponseTransformerPWA.convertListingMapResponse(hotelListingMapResponse, Mockito.anyMap(), deviceDetails, requestDetails, null));

        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put(Constants.UNIFIED_USER_RATING, "TRUE");
        commonModifierResponse.setExpDataMap(expDataMap);
        Assert.assertNotNull(listingMapResponseTransformerPWA.convertListingMapResponse(hotelListingMapResponse, Mockito.anyMap(), deviceDetails, requestDetails, commonModifierResponse));

        hotelListingMapResponse.getHotelList().get(0).getDisplayFare().getDisplayPriceBreakDown().setCouponInfo(null);
        hotelListingMapResponse.getHotelList().get(0).getDisplayFare().getDisplayPriceBreakDown().setEmiDetails(null);
        Assert.assertNotNull(listingMapResponseTransformerPWA.convertListingMapResponse(hotelListingMapResponse, Mockito.anyMap(), deviceDetails, requestDetails, null));

    }

    @Test
    public void addLocationPersuasionToHotelPersuasionsTest(){
        List<String> locations = new ArrayList<>();
        locations.add("This is test location persuasion");
        Hotel hotel = new Hotel();
        hotel.setHotelPersuasions(new HashMap<>());
        listingMapResponseTransformerPWA.addLocationPersuasionToHotelPersuasions(hotel, locations,null,null,null);
        Assert.assertNotNull(hotel.getHotelPersuasions());
        Assert.assertEquals(((Map<String, Object>)hotel.getHotelPersuasions()).size(),1);

        locations.add("one more test persuasion");
        hotel.setHotelPersuasions(new HashMap<>());
        listingMapResponseTransformerPWA.addLocationPersuasionToHotelPersuasions(hotel, locations,null,null,null);
        Assert.assertNotNull(hotel.getHotelPersuasions());
        Assert.assertEquals(((Map<String, Object>)hotel.getHotelPersuasions()).size(),1);
    }

}
