package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.response.filter.Filter;
import com.mmt.hotels.clientgateway.response.Action;
import com.mmt.hotels.clientgateway.response.TravelTipWrapperResponse;
import com.mmt.hotels.clientgateway.response.OverviewCard;
import com.mmt.hotels.clientgateway.response.SummaryCard;
import com.mmt.hotels.clientgateway.response.CardsData;
import com.mmt.hotels.clientgateway.response.TravelTipResponse;

import com.mmt.hotels.clientgateway.constants.Constants;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class TravelTipResponseTransformerTest {

    private final TravelTipResponseTransformer transformer = new TravelTipResponseTransformer() {
    };

    @Test
    void testConvertTravelTipResponse() {
        TravelTipWrapperResponse wrapperResponse = new TravelTipWrapperResponse();
        wrapperResponse.setName("Test Name");
        wrapperResponse.setBgImg("testBgImg");

        CardsData cardsData = new CardsData();

        OverviewCard overviewCard = new OverviewCard();
        overviewCard.setIconUrl("iconUrl");
        overviewCard.setTitle("title");
        overviewCard.setSubtitle("subtitle");

        SummaryCard summaryCard = new SummaryCard();
        summaryCard.setTitle("title");
        summaryCard.setHighlights(Collections.singletonList("highlight"));
        summaryCard.setActions(Collections.emptyList());

        cardsData.setOverviewCards(Collections.singletonList(overviewCard));
        cardsData.setSummaryCards(Collections.singletonList(summaryCard));

        wrapperResponse.setCardsData(cardsData);

        TravelTipResponse result = transformer.convertTravelTipResponse(wrapperResponse, "client");

        assertNotNull(result);
        assertEquals("Test Name", result.getHeader());
        assertEquals(Constants.TRAVEL_TIPS_SUB_HEADER, result.getSubHeader());
        assertEquals("testBgImg", result.getBgImg());
        assertNotNull(result.getCardsData());
        assertEquals(1, result.getCardsData().getOverviewCards().size());
        assertEquals(1, result.getCardsData().getSummaryCards().size());

        // Assuming the highlights field is a List<String>
        SummaryCard actualSummaryCard = result.getCardsData().getSummaryCards().get(0);
        assertEquals(Collections.singletonList("highlight"), actualSummaryCard.getHighlights());
    }
    @Test
    void testConvertOverviewCards() {
        OverviewCard card = new OverviewCard();
        card.setIconUrl("iconUrl");
        card.setTitle("title");
        card.setSubtitle("subtitle");
        List<OverviewCard> overviewCardsFromWrapper = Collections.singletonList(card);

        List<OverviewCard> result = transformer.convertOverviewCards(overviewCardsFromWrapper);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("iconUrl", result.get(0).getIconUrl());
        assertEquals("title", result.get(0).getTitle());
        assertEquals("subtitle", result.get(0).getSubtitle());
    }

    @Test
    void testConvertSummaryCards() {

        SummaryCard card = new SummaryCard();
        card.setTitle("title");
        card.setHighlights(Collections.singletonList("highlight")); // Assuming highlights is a List<String>
        List<SummaryCard> summaryCardsFromWrapper = Collections.singletonList(card);

        List<SummaryCard> result = transformer.convertSummaryCards(summaryCardsFromWrapper);

        assertNotNull(result);
        assertEquals(1, result.size());

        assertEquals("title", result.get(0).getTitle());
        assertEquals(Collections.singletonList("highlight"), result.get(0).getHighlights());
    }


    @Test
    void testConvertActions() {
        Action action = new Action();
        action.setPersuasionText("text");
        action.setFilters(Collections.emptyList());
        List<Action> actionsFromWrapper = Collections.singletonList(action);

        List<Action> result = transformer.convertActions(actionsFromWrapper);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("text", result.get(0).getText());
        assertEquals(0, result.get(0).getFilters().size());
    }

    @Test
    void testConvertFilters() {
        Filter filter = new Filter();
        filter.setTitle("title");
        filter.setFilter_category("category");
        filter.setId("id");
        filter.setType(Constants.FILTER_CATEGORY_TYPE);
        filter.setSub_filter_category(Arrays.asList("sub1", "sub2"));
        List<Filter> filtersFromWrapper = Collections.singletonList(filter);

        List<Filter> result = transformer.convertFilters(filtersFromWrapper);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("title", result.get(0).getTitle());
        assertEquals("category", result.get(0).getFilterGroup());
        assertEquals("sub1", result.get(0).getFilterValue());
        assertEquals("title", result.get(1).getTitle());
        assertEquals("category", result.get(1).getFilterGroup());
        assertEquals("sub2", result.get(1).getFilterValue());
    }

    @Test
    void testConvertFilters_NonCategoryType() {
        Filter filter = new Filter();
        filter.setTitle("title");
        filter.setFilter_category("category");
        filter.setId("id");
        filter.setType("nonCategoryType");
        List<Filter> filtersFromWrapper = Collections.singletonList(filter);

        List<Filter> result = transformer.convertFilters(filtersFromWrapper);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("title", result.get(0).getTitle());
        assertEquals("category", result.get(0).getFilterGroup());
        assertEquals("id", result.get(0).getFilterValue());
        assertEquals("nonCategoryType", result.get(0).getMatchmakerType());
    }
}
