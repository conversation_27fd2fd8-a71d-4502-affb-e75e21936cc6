package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.gommt.hotels.orchestrator.detail.enums.ReviewCountType;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.CohortDetails;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.ImageDetail;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.ResponseToReview;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.ReviewData;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.StayDetails;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.TravellerDetails;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.TravellerReviewResponse;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.response.flyfish.ReviewResponse;
import com.mmt.hotels.model.response.flyfish.UGCReviewData;
import com.mmt.hotels.model.response.flyfish.UgcReview;
import com.mmt.hotels.model.response.flyfish.UgcReviewResponse;
import com.mmt.hotels.model.response.flyfish.UgcReviewResponseData;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class OrchTravellerReviewsResponseTransformerTest {

    @InjectMocks
    private OrchTravellerReviewsResponseTransformer transformer;

    @Before
    public void setUp() {
        // Setup if needed
    }

    // ===================== SUCCESSFUL CONVERSION TESTS =====================

    @Test
    public void testConvertReviewsResponse_SuccessfulConversion_WithCompleteData() {
        // Given
        TravellerReviewResponse input = createCompleteReviewResponse();

        // When
        UgcReviewResponseData result = transformer.convertReviewsResponse(input);

        // Then
        assertNotNull(result);
        assertNotNull(result.getResponse());
        
        UgcReviewResponse response = result.getResponse();
        assertTrue(response.isMoreReviewsAvailable());
        assertEquals(OTA.MMT, response.getNextOTA());
        assertEquals(Integer.valueOf(50), response.getMmtReviewCount());
        assertEquals(Integer.valueOf(25), response.getExpReviewCount());
        assertEquals(Integer.valueOf(30), response.getIndianReviewCount());
        assertEquals(Integer.valueOf(15), response.getGiReviewCount());
        
        UgcReview reviews = response.getReviews();
        assertNotNull(reviews);
        assertEquals(OTA.EXP, reviews.getOta());
        assertNotNull(reviews.getReviewList());
        assertEquals(2, reviews.getReviewList().size());
        
        // Verify first review data
        UGCReviewData firstReview = reviews.getReviewList().get(0);
        assertEquals("REVIEW123", firstReview.getId());
        assertEquals("Great Hotel Experience", firstReview.getTitle());
        assertEquals("Excellent service and facilities", firstReview.getReviewText());
        assertEquals(4.5, firstReview.getRating(), 0.01);
        assertEquals("2024-01-15", firstReview.getPublishDate());
        assertEquals(10, firstReview.getUpvote());
        assertTrue(firstReview.isUpvoted());
        assertFalse(firstReview.isCrawledData());
        assertEquals("John Doe", firstReview.getTravellerName());
        assertEquals("LEISURE", firstReview.getTravelType());
        assertEquals("2024-01-10", firstReview.getStayDate());
        assertEquals("3 nights", firstReview.getStayDetails());
        assertEquals("Deluxe Room", firstReview.getRoomType());
        assertEquals("FAMILY", firstReview.getCohortDetails());
        
        // Verify images
        assertNotNull(firstReview.getImages());
        assertEquals(1, firstReview.getImages().size());
        assertEquals("https://example.com/image1.jpg", firstReview.getImages().get(0).getImgUrl());
        
        // Verify response to review
        assertNotNull(firstReview.getResponseToReview());
        assertEquals(1, firstReview.getResponseToReview().size());
        assertEquals("Hotel Manager", firstReview.getResponseToReview().get(0).getName());
        assertEquals("Thank you for your feedback!", firstReview.getResponseToReview().get(0).getResponseText());
    }

    @Test
    public void testConvertReviewsResponse_SuccessfulConversion_WithMinimalData() {
        // Given
        TravellerReviewResponse input = createMinimalReviewResponse();

        // When
        UgcReviewResponseData result = transformer.convertReviewsResponse(input);

        // Then
        assertNotNull(result);
        assertNotNull(result.getResponse());
        
        UgcReviewResponse response = result.getResponse();
        assertFalse(response.isMoreReviewsAvailable());
        assertNull(response.getNextOTA());
        assertNull(response.getMmtReviewCount());
        
        UgcReview reviews = response.getReviews();
        assertNotNull(reviews);
        assertNull(reviews.getOta());
        // When reviewData is empty, reviewList is not set, so it will be null
        assertNull(reviews.getReviewList());
    }

    // ===================== NULL INPUT TESTS =====================

    @Test
    public void testConvertReviewsResponse_NullInput() {
        // When - This should throw NullPointerException since transformer doesn't handle null input
        try {
            UgcReviewResponseData result = transformer.convertReviewsResponse(null);
            // If we reach here, the transformer was updated to handle null - verify the result
            assertNotNull(result);
            assertNotNull(result.getResponse());
            
            UgcReview reviews = result.getResponse().getReviews();
            assertNotNull(reviews);
            // For null input, we expect empty review list
            if (reviews.getReviewList() != null) {
                assertTrue(reviews.getReviewList().isEmpty());
            }
        } catch (NullPointerException e) {
            // This is expected behavior since the transformer doesn't handle null input
            // The transformer should be updated to handle null input gracefully
            assertTrue("Expected NullPointerException for null input", true);
        }
    }

    @Test
    public void testConvertReviewsResponse_NullReviewData() {
        // Given
        TravellerReviewResponse input = new TravellerReviewResponse();
        input.setMoreReviewsAvailable(true);
        input.setNextOTA(com.gommt.hotels.orchestrator.detail.enums.OTA.MMT);
        input.setReviewData(null); // Null review data

        // When
        UgcReviewResponseData result = transformer.convertReviewsResponse(input);

        // Then
        assertNotNull(result);
        UgcReview reviews = result.getResponse().getReviews();
        // When reviewData is null, reviewList is not set, so it will be null
        assertNull(reviews.getReviewList());
    }

    @Test
    public void testConvertReviewsResponse_EmptyReviewData() {
        // Given
        TravellerReviewResponse input = new TravellerReviewResponse();
        input.setMoreReviewsAvailable(true);
        input.setReviewData(new ArrayList<>()); // Empty review data

        // When
        UgcReviewResponseData result = transformer.convertReviewsResponse(input);

        // Then
        assertNotNull(result);
        UgcReview reviews = result.getResponse().getReviews();
        // When reviewData is empty list, reviewList is also not set, so it will be null
        assertNull(reviews.getReviewList());
    }

    // ===================== EMPTY/NULL FIELD TESTS =====================

    @Test
    public void testConvertReviewsResponse_NullReviewCounts() {
        // Given
        TravellerReviewResponse input = new TravellerReviewResponse();
        input.setMoreReviewsAvailable(true);
        input.setReviewCounts(null); // Null review counts

        // When
        UgcReviewResponseData result = transformer.convertReviewsResponse(input);

        // Then
        assertNotNull(result);
        UgcReviewResponse response = result.getResponse();
        assertNull(response.getMmtReviewCount());
        assertNull(response.getExpReviewCount());
        assertNull(response.getIndianReviewCount());
        assertNull(response.getGiReviewCount());
    }

    @Test
    public void testConvertReviewsResponse_EmptyReviewCounts() {
        // Given
        TravellerReviewResponse input = new TravellerReviewResponse();
        input.setMoreReviewsAvailable(true);
        input.setReviewCounts(new HashMap<>()); // Empty review counts

        // When
        UgcReviewResponseData result = transformer.convertReviewsResponse(input);

        // Then
        assertNotNull(result);
        UgcReviewResponse response = result.getResponse();
        assertNull(response.getMmtReviewCount());
        assertNull(response.getExpReviewCount());
        assertNull(response.getIndianReviewCount());
        assertNull(response.getGiReviewCount());
    }

    @Test
    public void testConvertReviewsResponse_ReviewDataWithNullFields() {
        // Given
        TravellerReviewResponse input = new TravellerReviewResponse();
        ReviewData reviewData = new ReviewData();
        // Set only some fields, leave others null
        reviewData.setReviewId("REVIEW123");
        reviewData.setTitle(null); // Null title
        reviewData.setReviewText(""); // Empty text
        reviewData.setRating(null); // Null rating
        reviewData.setTravellerDetails(null); // Null traveller details
        reviewData.setStayDetails(null); // Null stay details
        reviewData.setCohortDetails(null); // Null cohort details
        reviewData.setImageDetails(null); // Null image details
        reviewData.setResponseToReviews(null); // Null responses
        
        input.setReviewData(Collections.singletonList(reviewData));

        // When
        UgcReviewResponseData result = transformer.convertReviewsResponse(input);

        // Then
        assertNotNull(result);
        UgcReview reviews = result.getResponse().getReviews();
        assertEquals(1, reviews.getReviewList().size());
        
        UGCReviewData transformedReview = reviews.getReviewList().get(0);
        assertEquals("REVIEW123", transformedReview.getId());
        assertNull(transformedReview.getTitle());
        assertNull(transformedReview.getReviewText());
        assertNull(transformedReview.getTravellerName());
        assertNull(transformedReview.getStayDate());
        assertNull(transformedReview.getCohortDetails());
        assertNull(transformedReview.getImages());
        assertNull(transformedReview.getResponseToReview());
    }

    @Test
    public void testConvertReviewsResponse_ReviewDataWithEmptyCollections() {
        // Given
        TravellerReviewResponse input = new TravellerReviewResponse();
        ReviewData reviewData = new ReviewData();
        reviewData.setReviewId("REVIEW123");
        reviewData.setImageDetails(new ArrayList<>()); // Empty image details
        reviewData.setResponseToReviews(new ArrayList<>()); // Empty responses
        reviewData.setTitleSpan(new ArrayList<>()); // Empty title span
        reviewData.setReviewSpan(new ArrayList<>()); // Empty review span
        reviewData.setReviewSpanList(new ArrayList<>()); // Empty review span list
        
        input.setReviewData(Collections.singletonList(reviewData));

        // When
        UgcReviewResponseData result = transformer.convertReviewsResponse(input);

        // Then
        assertNotNull(result);
        UgcReview reviews = result.getResponse().getReviews();
        assertNotNull(reviews.getReviewList());
        assertEquals(1, reviews.getReviewList().size());
        
        UGCReviewData transformedReview = reviews.getReviewList().get(0);
        assertEquals("REVIEW123", transformedReview.getId());
        // Empty collections are not set, so they will be null
        assertNull(transformedReview.getImages());
        assertNull(transformedReview.getResponseToReview());
    }

    // ===================== PARTIAL DATA TESTS =====================

    @Test
    public void testConvertReviewsResponse_PartialReviewCounts() {
        // Given
        TravellerReviewResponse input = new TravellerReviewResponse();
        Map<ReviewCountType, Integer> reviewCounts = new HashMap<>();
        reviewCounts.put(ReviewCountType.MMT, 50);
        // Only set MMT count, others should be null
        input.setReviewCounts(reviewCounts);

        // When
        UgcReviewResponseData result = transformer.convertReviewsResponse(input);

        // Then
        assertNotNull(result);
        UgcReviewResponse response = result.getResponse();
        assertEquals(Integer.valueOf(50), response.getMmtReviewCount());
        assertNull(response.getExpReviewCount());
        assertNull(response.getIndianReviewCount());
        assertNull(response.getGiReviewCount());
    }

    @Test
    public void testConvertReviewsResponse_PartialTravellerDetails() {
        // Given
        TravellerReviewResponse input = new TravellerReviewResponse();
        ReviewData reviewData = new ReviewData();
        reviewData.setReviewId("REVIEW123");
        
        TravellerDetails travellerDetails = new TravellerDetails();
        travellerDetails.setTravellerName("John Doe");
        travellerDetails.setTravellerImage(null); // Null image
        travellerDetails.setTravelType(""); // Empty travel type
        travellerDetails.setLoyaltyIcon("   "); // Whitespace only
        
        reviewData.setTravellerDetails(travellerDetails);
        input.setReviewData(Collections.singletonList(reviewData));

        // When
        UgcReviewResponseData result = transformer.convertReviewsResponse(input);

        // Then
        assertNotNull(result);
        UGCReviewData transformedReview = result.getResponse().getReviews().getReviewList().get(0);
        assertEquals("John Doe", transformedReview.getTravellerName());
        assertNull(transformedReview.getTravellerImage());
        assertNull(transformedReview.getTravelType()); // Empty string should not be set
        assertNull(transformedReview.getLoyaltyIcon()); // Whitespace should not be set
    }

    @Test
    public void testConvertReviewsResponse_PartialStayDetails() {
        // Given
        TravellerReviewResponse input = new TravellerReviewResponse();
        ReviewData reviewData = new ReviewData();
        reviewData.setReviewId("REVIEW123");
        
        StayDetails stayDetails = new StayDetails();
        stayDetails.setStayDate("2024-01-10");
        stayDetails.setStayDuration(null); // Null duration
        stayDetails.setRoomType(""); // Empty room type
        
        reviewData.setStayDetails(stayDetails);
        input.setReviewData(Collections.singletonList(reviewData));

        // When
        UgcReviewResponseData result = transformer.convertReviewsResponse(input);

        // Then
        assertNotNull(result);
        UGCReviewData transformedReview = result.getResponse().getReviews().getReviewList().get(0);
        assertEquals("2024-01-10", transformedReview.getStayDate());
        assertNull(transformedReview.getStayDetails()); // Null duration should not be set
        assertNull(transformedReview.getRoomType()); // Empty room type should not be set
    }

    // ===================== EXCEPTION HANDLING TESTS =====================

    @Test
    public void testConvertReviewsResponse_InvalidOTAEnum() {
        // Given
        TravellerReviewResponse input = new TravellerReviewResponse();
        // Create a mock OTA that might not exist in client OTA enum
        input.setNextOTA(com.gommt.hotels.orchestrator.detail.enums.OTA.MMT);
        input.setCurrentOTA(com.gommt.hotels.orchestrator.detail.enums.OTA.EXP);

        // When - Should handle gracefully even if OTA conversion fails
        UgcReviewResponseData result = transformer.convertReviewsResponse(input);

        // Then
        assertNotNull(result);
        assertEquals(OTA.MMT, result.getResponse().getNextOTA());
        assertEquals(OTA.EXP, result.getResponse().getReviews().getOta());
    }

    @Test
    public void testConvertReviewsResponse_NullImageDetailUrl() {
        // Given
        TravellerReviewResponse input = new TravellerReviewResponse();
        ReviewData reviewData = new ReviewData();
        reviewData.setReviewId("REVIEW123");
        
        ImageDetail imageDetail = new ImageDetail();
        imageDetail.setUrl(null); // Null URL
        imageDetail.setTags(Arrays.asList("tag1", "tag2"));
        
        reviewData.setImageDetails(Collections.singletonList(imageDetail));
        input.setReviewData(Collections.singletonList(reviewData));

        // When
        UgcReviewResponseData result = transformer.convertReviewsResponse(input);

        // Then
        assertNotNull(result);
        UGCReviewData transformedReview = result.getResponse().getReviews().getReviewList().get(0);
        assertNotNull(transformedReview.getImages());
        assertEquals(1, transformedReview.getImages().size());
        assertNull(transformedReview.getImages().get(0).getImgUrl());
        assertEquals(2, transformedReview.getImages().get(0).getMmtTagList().size());
    }

    @Test
    public void testConvertReviewsResponse_NullResponseToReviewFields() {
        // Given
        TravellerReviewResponse input = new TravellerReviewResponse();
        ReviewData reviewData = new ReviewData();
        reviewData.setReviewId("REVIEW123");
        
        ResponseToReview responseToReview = new ResponseToReview();
        responseToReview.setName(null); // Null name
        responseToReview.setResponseText(""); // Empty response text
        responseToReview.setResponseDate("   "); // Whitespace date
        responseToReview.setRepliedAgoText("2 days ago");
        
        reviewData.setResponseToReviews(Collections.singletonList(responseToReview));
        input.setReviewData(Collections.singletonList(reviewData));

        // When
        UgcReviewResponseData result = transformer.convertReviewsResponse(input);

        // Then
        assertNotNull(result);
        UGCReviewData transformedReview = result.getResponse().getReviews().getReviewList().get(0);
        assertNotNull(transformedReview.getResponseToReview());
        assertEquals(1, transformedReview.getResponseToReview().size());
        
        ReviewResponse response = transformedReview.getResponseToReview().get(0);
        assertNull(response.getName()); // Null should remain null
        assertNull(response.getResponseText()); // Empty should remain null
        assertNull(response.getResponseDate()); // Whitespace should remain null
        assertEquals("2 days ago", response.getRepliedAgoText());
    }

    // ===================== EDGE CASE TESTS =====================

    @Test
    public void testConvertReviewsResponse_LargeReviewList() {
        // Given
        TravellerReviewResponse input = new TravellerReviewResponse();
        List<ReviewData> reviewDataList = new ArrayList<>();
        
        // Create 100 review items
        for (int i = 0; i < 100; i++) {
            ReviewData reviewData = new ReviewData();
            reviewData.setReviewId("REVIEW" + i);
            reviewData.setTitle("Review Title " + i);
            reviewData.setRating(4.0 + (i % 10) * 0.1);
            reviewDataList.add(reviewData);
        }
        
        input.setReviewData(reviewDataList);

        // When
        UgcReviewResponseData result = transformer.convertReviewsResponse(input);

        // Then
        assertNotNull(result);
        UgcReview reviews = result.getResponse().getReviews();
        assertEquals(100, reviews.getReviewList().size());
        
        // Verify first and last items
        assertEquals("REVIEW0", reviews.getReviewList().get(0).getId());
        assertEquals("REVIEW99", reviews.getReviewList().get(99).getId());
        assertEquals("Review Title 0", reviews.getReviewList().get(0).getTitle());
        assertEquals("Review Title 99", reviews.getReviewList().get(99).getTitle());
    }

    @Test
    public void testConvertReviewsResponse_SpecialCharactersInText() {
        // Given
        TravellerReviewResponse input = new TravellerReviewResponse();
        ReviewData reviewData = new ReviewData();
        reviewData.setReviewId("REVIEW123");
        reviewData.setTitle("Special chars: !@#$%^&*()");
        reviewData.setReviewText("Unicode: 🏨🌟⭐ and émojis");
        
        TravellerDetails travellerDetails = new TravellerDetails();
        travellerDetails.setTravellerName("José María");
        reviewData.setTravellerDetails(travellerDetails);
        
        input.setReviewData(Collections.singletonList(reviewData));

        // When
        UgcReviewResponseData result = transformer.convertReviewsResponse(input);

        // Then
        assertNotNull(result);
        UGCReviewData transformedReview = result.getResponse().getReviews().getReviewList().get(0);
        assertEquals("Special chars: !@#$%^&*()", transformedReview.getTitle());
        assertEquals("Unicode: 🏨🌟⭐ and émojis", transformedReview.getReviewText());
        assertEquals("José María", transformedReview.getTravellerName());
    }

    // ===================== HELPER METHODS =====================

    private TravellerReviewResponse createCompleteReviewResponse() {
        TravellerReviewResponse response = new TravellerReviewResponse();
        response.setMoreReviewsAvailable(true);
        response.setNextOTA(com.gommt.hotels.orchestrator.detail.enums.OTA.MMT);
        response.setCurrentOTA(com.gommt.hotels.orchestrator.detail.enums.OTA.EXP);
        
        // Set review counts
        Map<ReviewCountType, Integer> reviewCounts = new HashMap<>();
        reviewCounts.put(ReviewCountType.MMT, 50);
        reviewCounts.put(ReviewCountType.EXP, 25);
        reviewCounts.put(ReviewCountType.DH, 30);
        reviewCounts.put(ReviewCountType.GI, 15);
        response.setReviewCounts(reviewCounts);
        
        // Create review data
        List<ReviewData> reviewDataList = new ArrayList<>();
        
        // First review - complete data
        ReviewData review1 = new ReviewData();
        review1.setReviewId("REVIEW123");
        review1.setTitle("Great Hotel Experience");
        review1.setReviewText("Excellent service and facilities");
        review1.setRating(4.5);
        review1.setReviewDate("2024-01-15");
        review1.setUpvoteCount(10);
        review1.setShowUpvote(true);
        review1.setCrawledData(false);
        review1.setLogo("https://example.com/logo.png");
        review1.setBadgeUrl("https://example.com/badge.png");
        review1.setReviewsWrittenInfo("5 reviews written");
        
        // Traveller details
        TravellerDetails travellerDetails = new TravellerDetails();
        travellerDetails.setTravellerName("John Doe");
        travellerDetails.setTravellerImage("https://example.com/traveller.jpg");
        travellerDetails.setTravelType("LEISURE");
        travellerDetails.setLoyaltyIcon("https://example.com/loyalty.png");
        review1.setTravellerDetails(travellerDetails);
        
        // Stay details
        StayDetails stayDetails = new StayDetails();
        stayDetails.setStayDate("2024-01-10");
        stayDetails.setStayDuration("3 nights");
        stayDetails.setRoomType("Deluxe Room");
        review1.setStayDetails(stayDetails);
        
        // Cohort details
        CohortDetails cohortDetails = new CohortDetails();
        cohortDetails.setCohortType("FAMILY");
        review1.setCohortDetails(cohortDetails);
        
        // Image details
        ImageDetail imageDetail = new ImageDetail();
        imageDetail.setUrl("https://example.com/image1.jpg");
        imageDetail.setTags(Arrays.asList("room", "view"));
        review1.setImageDetails(Collections.singletonList(imageDetail));
        
        // Response to review
        ResponseToReview responseToReview = new ResponseToReview();
        responseToReview.setName("Hotel Manager");
        responseToReview.setResponseText("Thank you for your feedback!");
        responseToReview.setResponseDate("2024-01-16");
        responseToReview.setRepliedAgoText("1 day ago");
        review1.setResponseToReviews(Collections.singletonList(responseToReview));
        
        // Spans
        // Note: These are likely List<SpanObject> not List<String>, commenting out for now
        // review1.setTitleSpan(Arrays.asList("title", "span"));
        // review1.setReviewSpan(Arrays.asList("review", "span"));
        // review1.setReviewSpanList(Arrays.asList("span1", "span2"));
        
        reviewDataList.add(review1);
        
        // Second review - minimal data
        ReviewData review2 = new ReviewData();
        review2.setReviewId("REVIEW456");
        review2.setTitle("Good stay");
        review2.setRating(4.0);
        reviewDataList.add(review2);
        
        response.setReviewData(reviewDataList);
        return response;
    }

    private TravellerReviewResponse createMinimalReviewResponse() {
        TravellerReviewResponse response = new TravellerReviewResponse();
        // Set minimal data - most fields null/empty
        response.setReviewData(new ArrayList<>());
        return response;
    }
}
