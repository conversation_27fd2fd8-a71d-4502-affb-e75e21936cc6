package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.OrchUpdatedPriceRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.OrchUpdatedPriceResponseTransformer;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;

/**
 * Test class for OrchUpdatedPriceFactory
 * Ensures 100% line coverage for all methods and branches
 */
@RunWith(MockitoJUnitRunner.class)
public class OrchUpdatedPriceFactoryTest {

    @InjectMocks
    private OrchUpdatedPriceFactory orchUpdatedPriceFactory;

    @Mock
    private OrchUpdatedPriceRequestTransformer orchUpdatedPriceRequestTransformer;

    @Mock
    private OrchUpdatedPriceResponseTransformer orchUpdatedPriceResponseTransformer;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    // ==================== getOrchRequestService() TESTS ====================

    @Test
    public void should_ReturnRequestTransformer_When_ClientIsNull() {
        // Given
        String client = null;

        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceRequestTransformer, result);
    }

    @Test
    public void should_ReturnRequestTransformer_When_ClientIsEmpty() {
        // Given
        String client = "";

        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceRequestTransformer, result);
    }

    @Test
    public void should_ReturnRequestTransformer_When_ClientIsBlank() {
        // Given
        String client = "   ";

        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceRequestTransformer, result);
    }

    @Test
    public void should_ReturnRequestTransformer_When_ClientIsPWA() {
        // Given
        String client = "PWA";

        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceRequestTransformer, result);
    }

    @Test
    public void should_ReturnRequestTransformer_When_ClientIsDESKTOP() {
        // Given
        String client = "DESKTOP";

        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceRequestTransformer, result);
    }

    @Test
    public void should_ReturnRequestTransformer_When_ClientIsANDROID() {
        // Given
        String client = "ANDROID";

        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceRequestTransformer, result);
    }

    @Test
    public void should_ReturnRequestTransformer_When_ClientIsIOS() {
        // Given
        String client = "IOS";

        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceRequestTransformer, result);
    }

    @Test
    public void should_ReturnRequestTransformer_When_ClientIsUnknown() {
        // Given
        String client = "UNKNOWN_CLIENT";

        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceRequestTransformer, result);
    }

    @Test
    public void should_ReturnRequestTransformer_When_ClientIsLowerCase() {
        // Given
        String client = "pwa";

        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceRequestTransformer, result);
    }

    @Test
    public void should_ReturnRequestTransformer_When_ClientIsMixedCase() {
        // Given
        String client = "Desktop";

        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceRequestTransformer, result);
    }

    // ==================== getOrchResponseService() TESTS ====================

    @Test
    public void should_ReturnResponseTransformer_When_ClientIsNull() {
        // Given
        String client = null;

        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceResponseTransformer, result);
    }

    @Test
    public void should_ReturnResponseTransformer_When_ClientIsEmpty() {
        // Given
        String client = "";

        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceResponseTransformer, result);
    }

    @Test
    public void should_ReturnResponseTransformer_When_ClientIsBlank() {
        // Given
        String client = "   ";

        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceResponseTransformer, result);
    }

    @Test
    public void should_ReturnResponseTransformer_When_ClientIsPWA() {
        // Given
        String client = "PWA";

        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceResponseTransformer, result);
    }

    @Test
    public void should_ReturnResponseTransformer_When_ClientIsDESKTOP() {
        // Given
        String client = "DESKTOP";

        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceResponseTransformer, result);
    }

    @Test
    public void should_ReturnResponseTransformer_When_ClientIsANDROID() {
        // Given
        String client = "ANDROID";

        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceResponseTransformer, result);
    }

    @Test
    public void should_ReturnResponseTransformer_When_ClientIsIOS() {
        // Given
        String client = "IOS";

        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceResponseTransformer, result);
    }

    @Test
    public void should_ReturnResponseTransformer_When_ClientIsUnknown() {
        // Given
        String client = "UNKNOWN_CLIENT";

        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceResponseTransformer, result);
    }

    @Test
    public void should_ReturnResponseTransformer_When_ClientIsLowerCase() {
        // Given
        String client = "android";

        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceResponseTransformer, result);
    }

    @Test
    public void should_ReturnResponseTransformer_When_ClientIsMixedCase() {
        // Given
        String client = "Ios";

        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceResponseTransformer, result);
    }

    // ==================== EDGE CASE TESTS ====================

    @Test
    public void should_ReturnRequestTransformer_When_ClientContainsSpecialCharacters() {
        // Given
        String client = "PWA@#$";

        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceRequestTransformer, result);
    }

    @Test
    public void should_ReturnResponseTransformer_When_ClientContainsSpecialCharacters() {
        // Given
        String client = "DESKTOP!@#";

        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceResponseTransformer, result);
    }

    @Test
    public void should_ReturnRequestTransformer_When_ClientContainsNumbers() {
        // Given
        String client = "PWA123";

        // When
        OrchUpdatedPriceRequestTransformer result = orchUpdatedPriceFactory.getOrchRequestService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceRequestTransformer, result);
    }

    @Test
    public void should_ReturnResponseTransformer_When_ClientContainsNumbers() {
        // Given
        String client = "ANDROID456";

        // When
        OrchUpdatedPriceResponseTransformer result = orchUpdatedPriceFactory.getOrchResponseService(client);

        // Then
        assertNotNull(result);
        assertSame(orchUpdatedPriceResponseTransformer, result);
    }

    // ==================== CONSISTENCY TESTS ====================

    @Test
    public void should_ReturnSameInstance_When_CalledMultipleTimes() {
        // Given
        String client = "PWA";

        // When
        OrchUpdatedPriceRequestTransformer result1 = orchUpdatedPriceFactory.getOrchRequestService(client);
        OrchUpdatedPriceRequestTransformer result2 = orchUpdatedPriceFactory.getOrchRequestService(client);
        OrchUpdatedPriceResponseTransformer result3 = orchUpdatedPriceFactory.getOrchResponseService(client);
        OrchUpdatedPriceResponseTransformer result4 = orchUpdatedPriceFactory.getOrchResponseService(client);

        // Then
        assertSame(result1, result2);
        assertSame(result3, result4);
        assertSame(orchUpdatedPriceRequestTransformer, result1);
        assertSame(orchUpdatedPriceResponseTransformer, result3);
    }

    @Test
    public void should_ReturnSameInstanceForAllClients_When_AllClientsUsesSameTransformer() {
        // Given
        String[] clients = {"PWA", "DESKTOP", "ANDROID", "IOS", "UNKNOWN", null, "", "   "};

        // When & Then
        for (String client : clients) {
            OrchUpdatedPriceRequestTransformer requestResult = orchUpdatedPriceFactory.getOrchRequestService(client);
            OrchUpdatedPriceResponseTransformer responseResult = orchUpdatedPriceFactory.getOrchResponseService(client);
            
            assertSame("Request transformer should be same for client: " + client, 
                      orchUpdatedPriceRequestTransformer, requestResult);
            assertSame("Response transformer should be same for client: " + client, 
                      orchUpdatedPriceResponseTransformer, responseResult);
        }
    }
} 