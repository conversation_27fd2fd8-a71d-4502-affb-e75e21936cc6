package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.Persuasions;
import com.mmt.hotels.clientgateway.response.moblanding.MobLandingResponse;
import com.mmt.hotels.clientgateway.response.moblanding.QuestionCG;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.transformer.response.pwa.MobLandingResponseTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.pwa.SearchHotelsResponseTransformerPWA;
import com.mmt.hotels.clientgateway.util.LocationDataUtil;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.HomeStayDetails;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.response.altaccodata.AltAccoResponse;
import com.mmt.hotels.model.response.altaccodata.CardPayloadResponse;
import com.mmt.hotels.model.response.listpersonalization.MatchmakerTag;
import com.mmt.hotels.model.response.listpersonalization.PriceBucket;
import com.mmt.hotels.model.response.searchwrapper.*;
import com.mmt.hotels.model.response.staticdata.BbLatLong;
import com.mmt.hotels.model.response.staticdata.BboxLocationDetails;
import com.mmt.hotels.model.response.staticdata.LatLong;
import com.mmt.hotels.pojo.LocationRecommendation.LocationData;
import com.mmt.hotels.pojo.LocationRecommendation.LocationDataCategory;
import com.mmt.hotels.pojo.LocationRecommendation.LocationDataTag;
import com.mmt.hotels.pojo.LocationRecommendation.LocationDataTagList;
import com.mmt.hotels.pojo.landing.metadata.HotelsMetadata;
import com.mmt.hotels.pojo.listing.personalization.CardAction;
import com.mmt.hotels.pojo.listing.personalization.CardData;
import com.mmt.hotels.pojo.listing.personalization.ListPersonalizationResponse;
import com.mmt.hotels.pojo.matchmaker.*;
import com.mmt.hotels.pojo.response.ErrorResponse;
import com.mmt.hotels.pojo.response.GenericErrorEntity;
import com.mmt.hotels.pojo.response.landing.HotelLandingWrapperResponse;
import com.mmt.model.PropertyPersuasions;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.mockito.Mockito.when;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class MobLandingResponseTransformerPWATest {

	@InjectMocks
	MobLandingResponseTransformerPWA mobLandingResponseTransformerPWA;
	
	@Mock
	SearchHotelsResponseTransformerPWA searchHotelsResponseTransformerPWA;
	
	@Mock
	SearchHotelsFactory searchHotelsFactory;
	
	@Spy
	ObjectMapperUtil objectMapperUtil;

	@Spy
	CommonResponseTransformer commonResponseTransformer;

	@Mock
	PersuasionUtil persuasionUtil;

	@Mock
	LocationDataUtil locationDataUtil;

	@Before
	public void setup() {
		ReflectionTestUtils.setField(commonResponseTransformer, "searchHotelsFactory", searchHotelsFactory);
	}

	@Test
	public void testConvertMobLandingResponse() {

		HotelLandingWrapperResponse hotelLandingWrapperResponse = new HotelLandingWrapperResponse();

		hotelLandingWrapperResponse.setMatchmakerResponse(new WikiResponse());
		hotelLandingWrapperResponse.getMatchmakerResponse().setResponseError(new ErrorResponse());
		hotelLandingWrapperResponse.getMatchmakerResponse().getResponseError().setErrorList(new ArrayList<>());
		hotelLandingWrapperResponse.getMatchmakerResponse().getResponseError().getErrorList().add(new GenericErrorEntity());
		hotelLandingWrapperResponse.getMatchmakerResponse().setQuestions(new ArrayList<>());
		hotelLandingWrapperResponse.getMatchmakerResponse().setSavedLocations(new ArrayList<>());

		WikiQuestion wikiQuestion = new WikiQuestion();
		wikiQuestion.setCategory(new ArrayList<>());
		wikiQuestion.getCategory().add(new WikiQuestionCategory());
		wikiQuestion.getCategory().get(0).setTags(new ArrayList<>());
		wikiQuestion.getCategory().get(0).getTags().add(new com.mmt.hotels.pojo.matchmaker.MatchmakerTag());
		wikiQuestion.getCategory().get(0).getTags().get(0).setMatchMakerTagLatLngObject(new LatLongAndBounds());
		BbLatLong bbLatLong = new BbLatLong();
		bbLatLong.setNe(new LatLong());
		bbLatLong.setSw(new LatLong());
		wikiQuestion.getCategory().get(0).getTags().get(0).setBbox(bbLatLong);
		wikiQuestion.getCategory().get(0).getTags().get(0).setWiki(new WikiDetail());
		wikiQuestion.getCategory().get(0).getTags().get(0).getWiki().setWikiMetaInfo(new WikiMetaInfo());
		wikiQuestion.getCategory().get(0).getTags().get(0).getWiki().setTransitTags(new ArrayList<>());
		wikiQuestion.getCategory().get(0).getTags().get(0).getWiki().setReasonToStays(new ArrayList<>());
		wikiQuestion.getCategory().get(0).getTags().get(0).getWiki().getReasonToStays().add(new WikiInfo());
		wikiQuestion.getCategory().get(0).getTags().get(0).getWiki().setPoiTags(new ArrayList<>());
		wikiQuestion.getCategory().get(0).getTags().get(0).getWiki().getPoiTags().add(new com.mmt.hotels.pojo.matchmaker.WikiPoi());
		wikiQuestion.getCategory().get(0).getTags().get(0).getWiki().getTransitTags().add(new WikiTransitTag());
		wikiQuestion.getCategory().get(0).getTags().get(0).getWiki().getWikiMetaInfo().setCaution(new WikiInfo());
		hotelLandingWrapperResponse.getMatchmakerResponse().getQuestions().add(wikiQuestion);

		SavedLocations savedLocations = new SavedLocations();
		hotelLandingWrapperResponse.getMatchmakerResponse().getSavedLocations().add(savedLocations);

		hotelLandingWrapperResponse.setListPersonalizationResponse(new ListPersonalizationResponse());
		hotelLandingWrapperResponse.getListPersonalizationResponse().setCardData(new HashMap<>());
		hotelLandingWrapperResponse.getListPersonalizationResponse().getCardData().put(1, new ArrayList<>());
		hotelLandingWrapperResponse.getListPersonalizationResponse().getCardData().get(1).add(new CardData());
		hotelLandingWrapperResponse.getListPersonalizationResponse().getCardData().get(1).get(0).setCardPayload(new CardPayloadResponse());
		hotelLandingWrapperResponse.getListPersonalizationResponse().getCardData().get(1).get(0).getCardPayload().setAltAccoDiscovery(new ArrayList<>());
		hotelLandingWrapperResponse.getListPersonalizationResponse().getCardData().get(1).get(0).getCardPayload().setAltAccoData(new ArrayList<>());
		hotelLandingWrapperResponse.getListPersonalizationResponse().getCardData().get(1).get(0).setCardAction(new ArrayList<>());
		hotelLandingWrapperResponse.getListPersonalizationResponse().getCardData().get(1).get(0).getCardAction().add(new CardAction());
		hotelLandingWrapperResponse.getListPersonalizationResponse().getCardData().get(1).get(0).getCardAction().get(0).setPriceBucket(new PriceBucket());
		hotelLandingWrapperResponse.getListPersonalizationResponse().getCardData().get(1).get(0).getCardAction().get(0).setMatchmakerTags(new ArrayList<>());
		hotelLandingWrapperResponse.getListPersonalizationResponse().getCardData().get(1).get(0).getCardAction().get(0).getMatchmakerTags().add(new MatchmakerTag());

		AltAccoResponse altAccoResponse = new AltAccoResponse();
		altAccoResponse.setPropertyPersuasions(new ArrayList<>());
		PropertyPersuasions pp  = new PropertyPersuasions();
		altAccoResponse.getPropertyPersuasions().add(pp);
		altAccoResponse.setPropertyTypeList(new ArrayList<>());

		hotelLandingWrapperResponse.getListPersonalizationResponse().getCardData().get(1).get(0).getCardPayload().getAltAccoData().add(altAccoResponse);
		CollectionsResponseBo collectionsResponseBo = new CollectionsResponseBo.Builder().buildCollectionResponse(new ArrayList<FeaturedCollections<SearchWrapperHotelEntityAbridged>>()).build();
		collectionsResponseBo.getCollectionsResponse().add(new FeaturedCollections<>());
		((FeaturedCollections<SearchWrapperHotelEntityAbridged>) collectionsResponseBo.getCollectionsResponse().get(0)).setSearchContext(new RecommendedSearchContext());
		((FeaturedCollections<SearchWrapperHotelEntityAbridged>) collectionsResponseBo.getCollectionsResponse().get(0)).getSearchContext().setRoomStayCandidates(new ArrayList<>());
		((FeaturedCollections<SearchWrapperHotelEntityAbridged>) collectionsResponseBo.getCollectionsResponse().get(0)).getSearchContext().getRoomStayCandidates().add(new RoomStayCandidate());
		((FeaturedCollections<SearchWrapperHotelEntityAbridged>) collectionsResponseBo.getCollectionsResponse().get(0)).getSearchContext().getRoomStayCandidates().get(0).setGuestCounts(new ArrayList<>());
		((FeaturedCollections<SearchWrapperHotelEntityAbridged>) collectionsResponseBo.getCollectionsResponse().get(0)).getSearchContext().getRoomStayCandidates().get(0).getGuestCounts().add(new GuestCount());
		((FeaturedCollections<SearchWrapperHotelEntityAbridged>) collectionsResponseBo.getCollectionsResponse().get(0)).getSearchContext().getRoomStayCandidates().get(0).getGuestCounts().get(0).setCount("1");
		Map<FilterGroup, Set<Filter>> filterMap = new HashMap<>();
		filterMap.put(FilterGroup.HOTEL_PRICE, new HashSet<>());
		filterMap.get(FilterGroup.HOTEL_PRICE).add(new Filter(FilterGroup.HOTEL_PRICE, 1, 2));
		((FeaturedCollections<SearchWrapperHotelEntityAbridged>) collectionsResponseBo.getCollectionsResponse().get(0)).setAppliedFilterMap(filterMap);

		((FeaturedCollections<SearchWrapperHotelEntityAbridged>) collectionsResponseBo.getCollectionsResponse().get(0)).setHotels(new ArrayList<>());
		((FeaturedCollections<SearchWrapperHotelEntityAbridged>) collectionsResponseBo.getCollectionsResponse().get(0)).getHotels().add(new SearchWrapperHotelEntity());


		hotelLandingWrapperResponse.getListPersonalizationResponse().getCardData().get(1).get(0).getCardPayload().getAltAccoDiscovery().add(collectionsResponseBo);

		hotelLandingWrapperResponse.getCompletedRequests().add("abc");
		hotelLandingWrapperResponse.getCompletedRequests().add("def");

		hotelLandingWrapperResponse.setCurrentTimeStamp(10);

		hotelLandingWrapperResponse.setUuids(new HashSet<>());
		hotelLandingWrapperResponse.getUuids().add("abc");
		hotelLandingWrapperResponse.getUuids().add("def");

		hotelLandingWrapperResponse.setMetadata(new HotelsMetadata());
		MobLandingResponse response = mobLandingResponseTransformerPWA.convertMobLandingResponse(null,hotelLandingWrapperResponse, "",new LinkedHashMap<>());
		Assert.assertNotNull(response);
		Assert.assertNotNull(response.getMatchMakerResponse());
		Assert.assertNotNull(response.getListPersonalizationResponse());
		Assert.assertNotNull(response.getCompletedRequests());
	}

	@Test
	public void addLocationPersuasionToHotelPersuasionsTest() {
		SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
		List<String> locationPersuasion = new ArrayList<>();
		searchWrapperHotelEntity.setLocationPersuasion(locationPersuasion);
		mobLandingResponseTransformerPWA.addLocationPersuasionToHotelPersuasions(searchWrapperHotelEntity);
		locationPersuasion.add("test1");
		searchWrapperHotelEntity.setLocationPersuasion(locationPersuasion);
		mobLandingResponseTransformerPWA.addLocationPersuasionToHotelPersuasions(searchWrapperHotelEntity);
		locationPersuasion.add("test2");
		mobLandingResponseTransformerPWA.addLocationPersuasionToHotelPersuasions(searchWrapperHotelEntity);
	}

	@Test
	public void addPersuasionsForHiddenGemCardTest() {
		SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
		searchWrapperHotelEntity.setHotelPersuasions(new HashMap<>());
		searchWrapperHotelEntity.setHiddenGem(true);
		searchWrapperHotelEntity.setHiddenGemPersuasionText("Hidden Gem Persuasion Text");
		HomeStayDetails homeStayDetails = new HomeStayDetails();
		homeStayDetails.setStayType(Constants.STAY_TYPE_HOMESTAY);
		homeStayDetails.setStayTypeInfo("Home Stay Info Test");

		PersuasionObject homeStaysTitlePersuasion = new PersuasionObject();
		List<PersuasionData> homeStaysTitlePersuasionList = new ArrayList<>();
		homeStaysTitlePersuasionList.add(new PersuasionData());
		homeStaysTitlePersuasion.setData(homeStaysTitlePersuasionList);

		PersuasionObject homestaysSubTitlePersuasion = new PersuasionObject();
		homestaysSubTitlePersuasion.setData(Arrays.asList(new PersuasionData()));

		when(persuasionUtil.buildHiddenGemPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
		when(persuasionUtil.buildHiddenGemIconPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
		when(persuasionUtil.buildHomeStaysTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homeStaysTitlePersuasion);
		when(persuasionUtil.buildHomeStaysSubTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homestaysSubTitlePersuasion);
		mobLandingResponseTransformerPWA.addPersuasionsForHiddenGemCard(searchWrapperHotelEntity);
		Assert.assertNotNull(searchWrapperHotelEntity.getHotelPersuasions());
		Assert.assertTrue(searchWrapperHotelEntity.getHotelPersuasions() instanceof Map<?, ?>);
		Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdPWA()));
		Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdPWA()));
		Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdPWA()));
		Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdPWA()));
	}

	@Test
	public void testAddPivotAndZoomLevelForCityMap(){
		QuestionCG questionCG = new QuestionCG();
		WikiQuestion wikiQuestion = new WikiQuestion();
		LatLongAndBounds deskPivot = new LatLongAndBounds();
		deskPivot.setLat("123.4");
		deskPivot.setLng("234.5");
		wikiQuestion.setDeskPivot(deskPivot);
		wikiQuestion.setDeskZoomLevel(1);
		LatLongAndBounds pivot = new LatLongAndBounds();
		pivot.setLat("345.6");
		pivot.setLng("456.7");
		wikiQuestion.setPivot(pivot);
		wikiQuestion.setZoomLevel(2);
		mobLandingResponseTransformerPWA.addPivotAndZoomLevelForCityMap(questionCG,wikiQuestion);
		Assert.assertEquals(questionCG.getPivot().getLat(),pivot.getLat());
		Assert.assertEquals(questionCG.getZoomLevel(),wikiQuestion.getZoomLevel());
	}
}