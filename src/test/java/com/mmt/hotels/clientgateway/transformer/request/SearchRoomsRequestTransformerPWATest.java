package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.transformer.request.pwa.SearchRoomsRequestTransformerPWA;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.UserLocation;
import com.mmt.hotels.model.response.pricing.HotelRates;
import com.mmt.hotels.model.response.txn.CorporateData;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class SearchRoomsRequestTransformerPWATest {

    @InjectMocks
    SearchRoomsRequestTransformerPWA searchRoomsRequestTransformerPWA;

    @Mock
    MetricAspect metricAspect;

    @Mock
    Utility utility;

    @Test
    public void testConvertSearchRoomsRequest(){
        Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(Mockito.any(),Mockito.any(),Mockito.anyLong());
        SearchRoomsRequest searchRoomsRequest = new SearchRoomsRequest();
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExtendedUser(new ExtendedUser());
        commonModifierResponse.getExtendedUser().setUuid("123");
        commonModifierResponse.getExtendedUser().setProfileType("profileType");
        commonModifierResponse.getExtendedUser().setAffiliateId("affiliateId");
        commonModifierResponse.getExtendedUser().setProfileId("profileId");
        commonModifierResponse.getExtendedUser().setCorporateData("");
        commonModifierResponse.setHydraResponse(new HydraResponse());
        commonModifierResponse.getHydraResponse().setHydraMatchedSegment(new HashSet<>());
        commonModifierResponse.getHydraResponse().getHydraMatchedSegment().add("abc");
        commonModifierResponse.getHydraResponse().setFlightBooker(true);

        commonModifierResponse.setUserLocation(new UserLocation());
        commonModifierResponse.getUserLocation().setCity("city");
        commonModifierResponse.getUserLocation().setCountry("country");
        commonModifierResponse.getUserLocation().setState("state");
        searchRoomsRequest.setDeviceDetails(new DeviceDetails());
        searchRoomsRequest.setExpData("abc");

        searchRoomsRequest.setFeatureFlags(new FeatureFlags());
        searchRoomsRequest.getFeatureFlags().setStaticData(true);
        searchRoomsRequest.getFeatureFlags().setReviewSummaryRequired(true);
        searchRoomsRequest.getFeatureFlags().setWalletRequired(true);
        searchRoomsRequest.getFeatureFlags().setShortlistingRequired(false);

        searchRoomsRequest.setRequestDetails(new RequestDetails());
        searchRoomsRequest.getRequestDetails().setTrafficSource(new TrafficSource());
        searchRoomsRequest.getRequestDetails().setLoggedIn(false);
        searchRoomsRequest.getRequestDetails().setSrLng(10d);
        searchRoomsRequest.getRequestDetails().setSrLat(10d);

        searchRoomsRequest.setSearchCriteria(new SearchRoomsCriteria());
        searchRoomsRequest.getSearchCriteria().setRoomStayCandidates(new ArrayList<RoomStayCandidate>());
        searchRoomsRequest.getSearchCriteria().getRoomStayCandidates().add(new RoomStayCandidate());
        searchRoomsRequest.getSearchCriteria().getRoomStayCandidates().get(0).setRooms(1);
        searchRoomsRequest.getSearchCriteria().getRoomStayCandidates().get(0).setAdultCount(2);
        searchRoomsRequest.getSearchCriteria().getRoomStayCandidates().get(0).setChildAges(new ArrayList<>());
        searchRoomsRequest.getSearchCriteria().getRoomStayCandidates().get(0).getChildAges().add(1);
        searchRoomsRequest.getSearchCriteria().setTravellerEmailID(new ArrayList<>());
        searchRoomsRequest.getSearchCriteria().getTravellerEmailID().add("<EMAIL>");

        Filter filter = new Filter();
        filter.setFilterValue("abc");
        filter.setRangeFilter(true);
        filter.setFilterRange(new FilterRange());
        filter.setFilterGroup(FilterGroup.AMENITIES);
        filter.getFilterRange().setMinValue(1);
        filter.getFilterRange().setMaxValue(2);

        searchRoomsRequest.setFilterCriteria(new ArrayList<>());
        searchRoomsRequest.getFilterCriteria().add(filter);
        Mockito.when(utility.isDistributeRoomStayCandidates(Mockito.any(), Mockito.any())).thenReturn(true);
        com.mmt.hotels.model.request.MultiCurrencyInfo info = new com.mmt.hotels.model.request.MultiCurrencyInfo();
        info.setUserCurrency("INR");
        info.setRegionCurrency("INR");
        Mockito.when(utility.buildMultiCurrencyInfoRequest(Mockito.any())).thenReturn(info);
        MultiCurrencyInfo multiCurrencyInfo = new MultiCurrencyInfo();
        multiCurrencyInfo.setRegionCurrency("INR");
        multiCurrencyInfo.setUserCurrency("INR");
        searchRoomsRequest.getSearchCriteria().setMultiCurrencyInfo(multiCurrencyInfo);
        PriceByHotelsRequestBody priceByHotelsRequestBody = searchRoomsRequestTransformerPWA.convertSearchRoomsRequest(searchRoomsRequest, commonModifierResponse);
        org.springframework.util.Assert.notNull(priceByHotelsRequestBody);
    }

}
