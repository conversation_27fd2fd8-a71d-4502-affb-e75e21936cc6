package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.helpers.FilterHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.transformer.request.android.SearchHotelsRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashSet;

import static com.mmt.hotels.clientgateway.constants.Constants.FUNNEL_SOURCE_CORPBUDGET;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class SearchHotelsRequestTransformerAndroidTest {

    @InjectMocks
    SearchHotelsRequestTransformerAndroid searchHotelsRequestTransformerAndroid;

    @Mock
    MetricAspect metricAspect;

    @Mock
    Utility utility;

    @Mock
    FilterHelper filterHelper;

    @Test
    public void testConvertSearchHotelsRequest() {
        Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(Mockito.any(),Mockito.any(),Mockito.anyLong());
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        searchHotelsRequest.setDeviceDetails(new DeviceDetails());
        searchHotelsRequest.getDeviceDetails().setBookingDevice(Constants.DEVICE_OS_ANDROID);
        searchHotelsRequest.getDeviceDetails().setDeviceType("MOBILE");
        searchHotelsRequest.setRequestDetails(new RequestDetails());
        searchHotelsRequest.getRequestDetails().setSrLat(28d);
        searchHotelsRequest.getRequestDetails().setSrLng(28d);
        searchHotelsRequest.setSearchCriteria(new SearchHotelsCriteria());
        searchHotelsRequest.getSearchCriteria().setRoomStayCandidates(new ArrayList<>());
        searchHotelsRequest.getSearchCriteria().getRoomStayCandidates().add(new RoomStayCandidate());
        searchHotelsRequest.getSearchCriteria().setLat(28d);
        searchHotelsRequest.getSearchCriteria().setLng(28d);
        searchHotelsRequest.setFilterCriteria(new ArrayList<>());
        searchHotelsRequest.getFilterCriteria().add(new Filter(FilterGroup.HOTEL_CATEGORY, "swimming"));
        searchHotelsRequest.setImageDetails(new ImageDetails());
        searchHotelsRequest.getImageDetails().setCategories(new ArrayList<ImageCategory>());
        searchHotelsRequest.getImageDetails().getCategories().add(new ImageCategory());
        searchHotelsRequest.getImageDetails().getCategories().get(0).setCount(2);
        searchHotelsRequest.getImageDetails().getCategories().get(0).setHeight(2);
        searchHotelsRequest.getImageDetails().getCategories().get(0).setWidth(2);
        searchHotelsRequest.setFeatureFlags(new FeatureFlags());
        searchHotelsRequest.setSortCriteria(new SortCriteria());
        searchHotelsRequest.getRequestDetails().setTrafficSource(new TrafficSource());
        searchHotelsRequest.setFilterGroupsToRemove(new ArrayList<>());
        searchHotelsRequest.getFilterGroupsToRemove().add(FilterGroup.AMENITIES);
        searchHotelsRequest.setFiltersToRemove(new ArrayList<>());
        searchHotelsRequest.getFiltersToRemove().add(new Filter(FilterGroup.AMENITIES, "test"));
        searchHotelsRequest.setFilterCriteria(new ArrayList<>());
        searchHotelsRequest.getFilterCriteria().add(new Filter(FilterGroup.AMENITIES, "test"));
        searchHotelsRequest.setReviewDetails(new ReviewDetails());
        searchHotelsRequest.getReviewDetails().setOtas(new ArrayList<>());
        searchHotelsRequest.getReviewDetails().setTagTypes(new ArrayList<>());
        searchHotelsRequest.setMapDetails(new MapDetails());
        searchHotelsRequest.getMapDetails().setLngSegments(5);
        searchHotelsRequest.getMapDetails().setLatSegments(5);
        searchHotelsRequest.getMapDetails().setLatLngBounds(new LatLngBounds());
        searchHotelsRequest.getFilterCriteria().add(new Filter(FilterGroup.VILLA_AND_APPT, "VILLA_AND_APPT"));
        searchHotelsRequest.getFilterCriteria().add(new Filter(FilterGroup.DRIVE_WALK_PROXIMITY_KEY_POI, "DISTANCE_POIBURJ#dd_0-3000"));
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExtendedUser(new ExtendedUser());
        commonModifierResponse.setHydraResponse(new HydraResponse());
        commonModifierResponse.getHydraResponse().setHydraMatchedSegment(new HashSet<>());
        commonModifierResponse.getHydraResponse().getHydraMatchedSegment().add("r123");
        searchHotelsRequest.setMultiCityFilter(new MultiCityFilter());
        searchHotelsRequest.getSearchCriteria().setNearBySearch(true);
        SemanticSearchDetails semanticSearchDetails = new SemanticSearchDetails();
        semanticSearchDetails.setQueryText("some query text");
        semanticSearchDetails.setSemanticData("some semantic data");
        searchHotelsRequest.getRequestDetails().setSemanticSearchDetails(semanticSearchDetails);
        SearchWrapperInputRequest searchWrapperInputRequest = searchHotelsRequestTransformerAndroid.convertSearchRequest(searchHotelsRequest, new CommonModifierResponse());
        Assert.assertTrue(searchWrapperInputRequest.getAppliedFilterMap().containsKey(com.mmt.hotels.filter.FilterGroup.ALT_ACCO_PROPERTY));
        Assert.assertNotNull(searchWrapperInputRequest);
        Assert.assertNotNull(searchWrapperInputRequest.getMatchMakerRequest());
        Assert.assertNotNull(searchWrapperInputRequest.getMatchMakerRequest().getLatLng());
        Assert.assertEquals(searchWrapperInputRequest.getMatchMakerRequest().getLatLng().size(), 1);

        // when filter criteria is not null but filter value is not right
        searchHotelsRequest.setFilterCriteria(new ArrayList<>());
        searchHotelsRequest.getFilterCriteria().add(new Filter(FilterGroup.DRIVE_WALK_PROXIMITY_KEY_POI, "DISTANCE_POIBURJ"));
        com.mmt.hotels.model.request.MultiCurrencyInfo info = new com.mmt.hotels.model.request.MultiCurrencyInfo();
        info.setUserCurrency("INR");
        info.setRegionCurrency("INR");
        Mockito.when(utility.buildMultiCurrencyInfoRequest(Mockito.any())).thenReturn(info);
        MultiCurrencyInfo multiCurrencyInfo = new MultiCurrencyInfo();
        multiCurrencyInfo.setRegionCurrency("INR");
        multiCurrencyInfo.setUserCurrency("INR");
        searchHotelsRequest.getSearchCriteria().setMultiCurrencyInfo(multiCurrencyInfo);
        searchWrapperInputRequest = searchHotelsRequestTransformerAndroid.convertSearchRequest(searchHotelsRequest, new CommonModifierResponse());
        Assert.assertNull(searchWrapperInputRequest.getMatchMakerRequest());

        searchHotelsRequest.getRequestDetails().setFunnelSource(FUNNEL_SOURCE_CORPBUDGET);
        searchWrapperInputRequest = searchHotelsRequestTransformerAndroid.convertSearchRequest(searchHotelsRequest, new CommonModifierResponse());
        Assert.assertNull(searchWrapperInputRequest.getMatchMakerRequest());

        // when filter criteria is null
        searchHotelsRequest.setFilterCriteria(null);
        searchWrapperInputRequest = searchHotelsRequestTransformerAndroid.convertSearchRequest(searchHotelsRequest, new CommonModifierResponse());
        Assert.assertNull(searchWrapperInputRequest.getMatchMakerRequest());
    }
}
