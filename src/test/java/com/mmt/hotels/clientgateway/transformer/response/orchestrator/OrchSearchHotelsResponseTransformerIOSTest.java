package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gommt.hotels.orchestrator.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.model.response.listing.HotelDetails;
import com.gommt.hotels.orchestrator.model.response.listing.ListingResponse;
import com.gommt.hotels.orchestrator.model.response.listing.PersonalizedSectionDetails;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.pms.PropertyTextConfig;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.searchHotels.BottomSheet;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard;
import com.mmt.hotels.clientgateway.response.searchHotels.SearchHotelsResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.propertymanager.config.PropertyManager;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.DRIVING_DURATION_ZONE_SHORTSTAY;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.GROUP_PRICE_TEXT_ONE_NIGHT_ALT_ACCO;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SHORTSTAY_LOCATION_PERSUASION_PREFIX_TEXT_FONT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SHORTSTAY_LOCATION_PERSUASION_SUFFIX_TEXT_FONT;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrchSearchHotelsResponseTransformerIOSTest {

    @InjectMocks
    OrchSearchHotelsResponseTransformerIOS orchSearchHotelsResponseTransformerIOS;


    @Mock
    PropertyManager propManager;

    @Mock
    PropertyTextConfig propertyTextConfig;

    @Mock
    CommonConfig commonConfig;

    @Mock
    CommonConfigConsul commonConfigConsul;

    @Spy
    ObjectMapperUtil objectMapperUtil;

    @Spy
    Utility utility;

    @Spy
    CommonResponseTransformer commonResponseTransformer;

    @Spy
    DateUtil dateUtil;

    @Mock
    PolyglotService polyglotService;

    @Spy
    CommonHelper commonHelper;

    @Mock
    MobConfigHelper mobConfigHelper;

    @Spy
    PersuasionUtil persuasionUtil;

    ObjectMapper mapper;

    Gson gson = new Gson();


    @Test
    public void init() {
        orchSearchHotelsResponseTransformerIOS.init();
    }

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(commonResponseTransformer, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData(eq(GROUP_PRICE_TEXT_ONE_NIGHT_ALT_ACCO))).thenReturn("{NIGHT_COUNT} Night");
        when(polyglotService.getTranslatedData(eq("GROUP_PRICE_TEXT_ONE_NIGHT_PERNEW"))).thenReturn("Total <b>₹{AMOUNT}</b> for {NIGHT_COUNT} Night, {ROOM_COUNT} Rooms");
        when(polyglotService.getTranslatedData(eq(SHORTSTAY_LOCATION_PERSUASION_PREFIX_TEXT_FONT))).thenReturn("<font color='#FFFFFF'><b>{city_text}</b></font>");
        when(polyglotService.getTranslatedData(eq(SHORTSTAY_LOCATION_PERSUASION_SUFFIX_TEXT_FONT))).thenReturn("<font color='#FD9C35'><b>{driving_text}</b></font>");
        when(polyglotService.getTranslatedData(eq(DRIVING_DURATION_ZONE_SHORTSTAY))).thenReturn("({duration} drive from {city_name})");
        mapper = new ObjectMapper();
        mapper.writerWithView(PIIView.External.class);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerIOS, "placeHoldersToShowConfig", "{\"SIMILAR_HOTELS\":[\"PLACEHOLDER_IMAGE_LEFT_TOP\",\"PLACEHOLDER_CARD_M4\",\"PLACEHOLDER_CARD_M1\"]}");
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerIOS, "filterConditionsConfig", "{\"range\":{\"minValue\":0,\"maxValue\":3000},\"categoriesIncluded\":[\"Premium Properties\"],\"categoriesExcluded\":[\"MMT Value Stays\"]}");
        ReflectionTestUtils.setField(utility, "dateUtil", dateUtil);

        String specialFarePersuasionConfig = "{\"DESKTOP\":{\"PC_RIGHT_1_1\":{\"style\":{\"styleClasses\":[\"specialFareTag\",\"pushRight\"]}},\"PC_RIGHT_3\":{\"hover\":{\"style\":{\"styleClasses\":[\"specialFareInfo-tooltip\"]}},\"style\":{\"styleClasses\":[\"specialFareInfo\"]}}},\"APPS\":{\"PLACEHOLDER_BOTTOM_BOX_M\":{\"style\":{\"bgColor\":\"#FFF6E8\",\"textColor\":\"#CF8100\",\"fontType\":\"B\",\"fontSize\":\"SMALL\",\"iconHeight\":16,\"iconWidth\":16},\"iconurl\":\"https://promos.makemytrip.com/images/myBiz/hotels/Info_Icon.png\", \"topLevelStyle\":{\"gravity\":\"center\"}},\"PLACEHOLDER_PRICE_BOTTOM_M\":{\"topLevelStyle\":{\"bgGradient\":{\"start\":\"#EEAF4C\",\"end\":\"#CE6112\"}},\"style\":{\"textColor\":\"#FFFFFF\",\"fontType\":\"B\",\"fontSize\":\"SMALL\",\"maxLines\":1}}}}";
        Map<String, Map<String, PersuasionData>> specialFarePersuasionConfigMap = gson.fromJson(specialFarePersuasionConfig, new TypeToken<Map<String, Map<String, PersuasionData>>>() {
        }.getType());

        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerIOS, "specialFarePersuasionConfigMap", specialFarePersuasionConfigMap);

        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerIOS, "basicDetailDeeplink", "https://www.makemytrip.com/hotels/hotel-details?hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&openDetail=true&currency={5}&roomStayQualifier={6}&locusId={7}&locusType={8}");
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerIOS, "rootLevelSharingUrl", "https://app.mmyt.co/Xm2V/hotelListingShare?checkin={0}&checkout={1}&city={2}&country={3}&roomStayQualifier={4}&checkAvailability={5}");
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerIOS, "rootLevelDeeplink", "https://www.makemytrip.com/hotels/hotel-listing/?checkin={0}&checkout={1}&city={2}&country={3}&roomStayQualifier={4}&checkAvailability={5}&_uCurrency={6}");


    }

    @Test
    public void convertSearchHotelsResponseTest() throws JsonProcessingException {
        String listingResponseJson = "{\"requestId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"journeyId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"hotelCount\":1,\"personalizedSections\":[{\"name\":\"RECENTLY_VIEWED_HOTELS\",\"heading\":\"Recently Viewed\",\"hotelCardType\":\"default\",\"orientation\":\"V\",\"hotelCount\":1,\"cardInsertionAllowed\":false,\"hotels\":[{\"id\":\"201106120942397721\",\"name\":\"The Ashok\",\"propertyType\":\"Hotel\",\"propertyLabel\":\"Hotel\",\"detailDeeplinkUrl\":\"https://www.makemytrip.com/hotels/hotel-details?hotelId=201106120942397721&checkin=date_3&checkout=date_4&country=IN&city=CTDEL&roomStayQualifier=2e0e&openDetail=true&currency=INR&region=in&checkAvailability=true&locusId=CTDEL&locusType=city\",\"seoUrl\":\"https://www.makemytrip.com/hotels/the_ashok-details-delhi.html\",\"stayType\":\"Entire\",\"threeSixtyViewIconUrl\":\"https://promos.makemytrip.com/Hotels_product/Listing/3603x.png\",\"starRating\":5,\"totalRoomCount\":1,\"soldOut\":false,\"showCallToBook\":false,\"budgetHotel\":false,\"groupBookingHotel\":false,\"groupBookingPrice\":false,\"categories\":[\"Festive Weekend Deals\",\"PREMIND\",\"Deals for Vaccinated Travellers\",\"Child Friendly\",\"Inside BD\",\"Inmarket\",\"Hills\",\"Central Heating\",\"Great Value Packages\",\"kids_stay_free\",\"Premium Properties\",\"Last Minute Deals\",\"premium_hotels\"],\"locationPersuasions\":[\"Chanakyapuri\",\"10.0 km drive to T1 - Delhi Airport (IGI Airport)\"],\"facilityHighlights\":[\"Spa\",\"Restaurant\",\"Bar\"],\"location\":{\"id\":\"CTDEL\",\"type\":\"znshim\",\"countryId\":\"IN\",\"countryName\":null,\"cityId\":\"ZNSHIM\",\"cityName\":\"\",\"stateId\":null,\"geo\":{\"latitude\":\"\",\"longitude\":\"\"}},\"media\":{\"images\":[{\"url\":\"http://r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201106120942397721-afe9941a2d1811eea3cd0a58a9feac02.jpg?output-quality=75&downsize=243:162&output-format=webp\"},{\"url\":\"http://r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201106120942397721-3ce394a835be11ee982f0a58a9feac02.jpg?output-quality=75&downsize=243:162&output-format=webp\"},{\"url\":\"http://r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201106120942397721-21bae8ca35be11eeb7ae0a58a9feac02.jpg?output-quality=75&downsize=243:162&output-format=webp\"}]},\"omnitureFlags\":{\"rtb\":false,\"abso\":false,\"abo\":false,\"wishlisted\":false},\"reviewDetails\":{\"rating\":3.8,\"totalReviewCount\":3738,\"totalRatingCount\":7157,\"subRatings\":[{\"name\":\"Location\",\"rating\":4.5,\"reviewCount\":851,\"show\":false},{\"name\":\"Hospitality\",\"rating\":3.9,\"reviewCount\":2096,\"show\":true},{\"name\":\"Facilities\",\"rating\":3.8,\"reviewCount\":1106,\"show\":true}],\"ratingText\":\"Very Good\",\"ota\":\"MMT\"},\"rooms\":[{\"name\":\"The Ashok\",\"code\":\"201106120942397721\",\"ratePlans\":[{\"code\":\"1618459331976956240\",\"inclusions\":[{\"category\":\"Free Breakfast\",\"name\":\"Complimentary  Breakfast is available.\"}],\"price\":{\"basePrice\":14999.0,\"displayPrice\":12555.0,\"totalTax\":3375.0,\"savingPerc\":0.0,\"couponCode\":\"MMTBESTBUY\",\"couponDiscount\":944.0,\"hotelDiscount\":0.0,\"applicableCoupons\":[{\"autoApplicable\":false,\"couponCode\":\"MMTBESTBUY\",\"discount\":944.0,\"specialPromoCoupon\":false,\"type\":\"Get INR 944  Off\"}],\"taxBreakUp\":{\"hotelTax\":2430.0,\"hotelServiceCharge\":0.0,\"hcpGst\":0.0,\"serviceFee\":945.0,\"totalTax\":0.0}},\"paymentMode\":\"PAS\",\"cancellationPolicy\":{\"cancelPaneltyDesc\":\"Free Cancellation (100% refund) if you cancel this booking before 2024-10-24 13:59:59 (destination time). Cancellations post that will be subject to a hotel fee as follows:From 2024-10-24 14:00:00 (destination time) till 2024-10-26 13:59:59 (destination time) - 100% of booking amount.After 2024-10-26 14:00:00 (destination time) - 100% of booking amount.Cancellations are only allowed before CheckIn.\",\"penalties\":[{\"startDate\":null,\"endDate\":\"2024-10-24 13:59:59\",\"penaltyValue\":\"FREE_CANCELLATION\",\"penaltyType\":\"F\"}]},\"mealPlans\":[{\"code\":\"CP\",\"value\":\"Breakfast\"}]}]}],\"altAcco\":false}]}],\"lastHotelId\":\"201512161105316614\",\"lastFetchedWindowInfo\":\"000#0#3#false\",\"location\":{\"id\":\"CTDEL\",\"type\":\"znshim\",\"countryId\":\"IN\",\"countryName\":null,\"cityId\":\"ZNSHIM\",\"cityName\":\"\",\"stateId\":null,\"geo\":{\"latitude\":\"\",\"longitude\":\"\"}},\"sortCriteria\":{\"field\":\"S_hsq610_dspers_v2_LC_Per\",\"order\":\"asc\"},\"trackingInfo\":{\"exp\":\"S_hsq610_dspers_v2_LC_Per\"}}";
        ListingResponse listingResponse = mapper.readValue(listingResponseJson, ListingResponse.class);
        SearchHotelsRequest searchHotelsRequest = createSearchHotelsRequest();
        searchHotelsRequest.getRequestDetails().setMetaInfo(true);
        searchHotelsRequest.getFeatureFlags().setPersuasionSuppression(true);
        CommonModifierResponse commonModifierResponse = getCommonModifierResponse();
        SearchHotelsResponse searchHotelsResponse = orchSearchHotelsResponseTransformerIOS.convertSearchHotelsResponse(listingResponse, searchHotelsRequest, commonModifierResponse);
        Assert.assertNotNull(searchHotelsResponse);


        listingResponseJson = "{\"requestId\":\"bca1d743-ea59-4539-a515-0271a43f5284\",\"journeyId\":\"82909517392a9898b-5da3-4458-b986-2741f54ac819\",\"hotelCount\":1,\"personalizedSections\":[{\"name\":\"NOT_MYBIZ_ASSURED_SHOWN\",\"hotelCardType\":\"default\",\"orientation\":\"V\",\"hotelCount\":1,\"cardInsertionAllowed\":false,\"hotels\":[{\"id\":\"201211081220304096\",\"name\":\"Park Plaza, Shahdara\",\"propertyType\":\"Hotel\",\"propertyLabel\":\"Hotel\",\"detailDeeplinkUrl\":\"https://www.makemytrip.com/hotels/hotel-details?hotelId=201211081220304096&checkin=date_3&checkout=date_4&country=IN&city=CTDEL&roomStayQualifier=2e0e&openDetail=true&currency=INR&region=in&checkAvailability=true&locusId=CTDEL&locusType=city\",\"seoUrl\":\"https://www.makemytrip.com/hotels/park_plaza_shahdara-details-delhi.html\",\"stayType\":\"Entire\",\"starRating\":5,\"totalRoomCount\":1,\"soldOut\":false,\"showCallToBook\":false,\"budgetHotel\":false,\"groupBookingHotel\":false,\"groupBookingPrice\":false,\"categories\":[\"Chain\",\"HighFiveV2_Chain\",\"MMTFest\",\"Couple Friendly\",\"Premium PropertiesFlyer Deal\",\"Premium\",\"Workation\",\"MyBiz_Assured\",\"Daily Dhamaka\"],\"locationPersuasions\":[\"East Delhi, Delhi\",\"2.2 km drive to Shahdra Railway Station\"],\"facilityHighlights\":[\"Spa\",\"Swimming Pool\",\"Gym\",\"Restaurant\",\"Steam and Sauna\"],\"location\":{\"id\":\"RGNCR\",\"type\":\"region\",\"countryId\":\"IN\",\"countryName\":\"India\",\"cityId\":\"CTDEL\",\"cityName\":\"Delhi\",\"stateId\":null,\"geo\":{\"latitude\":\"28.658848\",\"longitude\":\"77.297844\"}},\"media\":{\"images\":[{\"url\":\"http://r1imghtlak.mmtcdn.com/82090bda780511e7bf27025f77df004f.jpg?output-quality=75&downsize=243:162&output-format=webp\"},{\"url\":\"http://r1imghtlak.mmtcdn.com/84a383ca780511e78bf8025f77df004f.jpg?output-quality=75&downsize=243:162&output-format=webp\"}]},\"omnitureFlags\":{\"rtb\":false,\"abso\":false,\"abo\":false,\"wishlisted\":false},\"reviewDetails\":{\"rating\":3.8,\"totalReviewCount\":2083,\"totalRatingCount\":4118,\"subRatings\":[{\"name\":\"Location\",\"rating\":3.8,\"reviewCount\":1385,\"show\":false},{\"name\":\"Hospitality\",\"rating\":3.9,\"reviewCount\":1017,\"show\":true},{\"name\":\"Facilities\",\"rating\":3.9,\"reviewCount\":1045,\"show\":true},{\"name\":\"Food\",\"rating\":3.7,\"reviewCount\":836,\"show\":true},{\"name\":\"Room\",\"rating\":4.0,\"reviewCount\":837,\"show\":true},{\"name\":\"Cleanliness\",\"rating\":4.1,\"reviewCount\":2290,\"show\":true},{\"name\":\"Value For Money\",\"rating\":3.9,\"reviewCount\":778,\"show\":true},{\"name\":\"Child Friendliness\",\"rating\":3.5,\"reviewCount\":46,\"show\":true}],\"ratingText\":\"Very Good\",\"ota\":\"MMT\"},\"rooms\":[{\"name\":\"Park Plaza, Shahdara\",\"code\":\"201211081220304096\",\"ratePlans\":[{\"code\":\"3028728164758747328\",\"price\":{\"basePrice\":5000.0,\"displayPrice\":4500.0,\"totalTax\":540.0,\"savingPerc\":0.0,\"couponCode\":\"DHCASHBACK\",\"couponDiscount\":225.0,\"hotelDiscount\":0.0,\"applicableCoupons\":[{\"autoApplicable\":false,\"couponCode\":\"DHCASHBACK\",\"description\":\"Get  INR 225 Cashback to Card on payments via credit/debit cards\",\"discount\":225.0,\"specialPromoCoupon\":true,\"type\":\"Cashback to Card\"}],\"taxBreakUp\":{\"hotelTax\":540.0,\"hotelServiceCharge\":0.0,\"hcpGst\":0.0,\"serviceFee\":0.0,\"totalTax\":0.0}},\"paymentMode\":\"PAS\",\"cancellationPolicy\":{\"cancelPaneltyDesc\":\"This is a Non-refundable and non-amendable tariff. Cancellations, or no-shows will be subject to a hotel fee equal to the 100% of booking amount.Cancellations are only allowed before CheckIn.\",\"penalties\":[{\"startDate\":null,\"endDate\":null,\"penaltyValue\":\"NON_REFUNDABLE\",\"penaltyType\":null}]},\"mealPlans\":[{\"code\":\"EP\",\"value\":\"Room Only\"}]}]}],\"calendarCriteria\":{},\"soldOutInfo\":{},\"altAcco\":true}]}],\"lastHotelId\":\"201512151820116224\",\"lastFetchedWindowInfo\":\"000#0#3#false\",\"location\":{\"id\":\"RGNCR\",\"type\":\"region\",\"countryId\":\"IN\",\"countryName\":\"India\",\"cityId\":\"CTDEL\",\"cityName\":\"Delhi\",\"stateId\":null,\"geo\":{\"latitude\":\"28.658848\",\"longitude\":\"77.297844\"}},\"sortCriteria\":{\"field\":\"S_MM_DHS_DEFAULT_v.1_rec_LC_Per\",\"order\":\"asc\"},\"trackingInfo\":{\"exp\":\"S_MM_DHS_DEFAULT_v.1_rec_LC_Per\"}}";
        listingResponse = mapper.readValue(listingResponseJson, ListingResponse.class);
        searchHotelsRequest.getRequestDetails().setFunnelSource("GROUP");
        searchHotelsResponse = orchSearchHotelsResponseTransformerIOS.convertSearchHotelsResponse(listingResponse, searchHotelsRequest, commonModifierResponse);
        Assert.assertNotNull(searchHotelsResponse);
    }

    @Test
    public void addLocationPersuasionToHotelPersuasionsTest() throws JsonProcessingException {
        Hotel hotelEntity = new Hotel();
        List<String> locationPersuasions = Collections.singletonList("Chanakyapuri");
        LinkedHashSet<String> facilities = new LinkedHashSet<>(Arrays.asList("Spa", "Restaurant", "Bar"));
        TransportPoi nearestGroundTransportPoi = new TransportPoi();
        LocationDetails locusData = new LocationDetails();
        locusData.setCityName("Delhi");
        SearchHotelsRequest searchHotelsRequest = createSearchHotelsRequest();
        String dayUsePersuasionsText = "Day Use";
        String drivingTimeText = "10.0 km drive to T1 - Delhi Airport (IGI Airport)";
        orchSearchHotelsResponseTransformerIOS.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, null, true);

        Assert.assertTrue(hotelEntity.getHotelPersuasions() instanceof HashMap);
        Assert.assertEquals(((HashMap) hotelEntity.getHotelPersuasions()).size(), 2);

        hotelEntity.setHotelPersuasions(null);
        searchHotelsRequest.getRequestDetails().setFunnelSource("SHORTSTAYS");
        orchSearchHotelsResponseTransformerIOS.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, null, true);
        Assert.assertTrue(hotelEntity.getHotelPersuasions() instanceof HashMap);
        Assert.assertEquals(((HashMap) hotelEntity.getHotelPersuasions()).size(), 0);

        orchSearchHotelsResponseTransformerIOS.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, locusData, true);
        Assert.assertTrue(hotelEntity.getHotelPersuasions() instanceof HashMap);
        Assert.assertEquals(((HashMap) hotelEntity.getHotelPersuasions()).size(), 2);


        locationPersuasions = Arrays.asList("Chanakyapuri", "10.0 km drive to T1 - Delhi Airport (IGI Airport)", "Delhi");
        hotelEntity.setHotelPersuasions(null);
        searchHotelsRequest.getRequestDetails().setFunnelSource("DAYUSE");
        orchSearchHotelsResponseTransformerIOS.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, locusData, true);
        Assert.assertTrue(hotelEntity.getHotelPersuasions() instanceof HashMap);
        Assert.assertEquals(((HashMap) hotelEntity.getHotelPersuasions()).size(), 2);

    }

    @Test
    public void addBookingConfirmationPersuasionTest() {
        HotelDetails hotelEntity = new HotelDetails();
        orchSearchHotelsResponseTransformerIOS.addBookingConfirmationPersuasion(hotelEntity);
    }
    @Test
    public void buildStaticCardTest() {
        orchSearchHotelsResponseTransformerIOS.buildStaticCard(null, null, "test");
    }

    private CommonModifierResponse getCommonModifierResponse() {
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExpDataMap(new LinkedHashMap<>());
        commonModifierResponse.setHydraResponse(new HydraResponse());
        commonModifierResponse.getHydraResponse().setHydraMatchedSegment(new HashSet<>(Arrays.asList("r1", "r2", "r3")));
        return commonModifierResponse;
    }

    private SearchHotelsRequest createSearchHotelsRequest() throws JsonProcessingException {


        String requestString = "{\"correlationKey\":null,\"brand\":null,\"client\":null,\"blackInfo\":null,\"deviceDetails\":{\"appVersion\":\"128.0.0.0\",\"deviceId\":\"d7bb97cf-762b-4484-91fc-a224c03cdc96\",\"deviceType\":\"DESKTOP\",\"bookingDevice\":\"DESKTOP\",\"networkType\":\"WiFi\",\"deviceName\":null,\"appVersionIntGi\":null,\"simSerialNo\":null},\"lastProductId\":null,\"limit\":null,\"requestDetails\":{\"visitorId\":\"d23c479b373ee283\",\"visitNumber\":1,\"trafficSource\":null,\"srCon\":null,\"srCty\":null,\"srcState\":null,\"srLat\":null,\"srLng\":null,\"funnelSource\":\"HOTELS\",\"idContext\":\"B2C\",\"notifCoupon\":null,\"callBackType\":null,\"pushDataToCallToBookQ\":null,\"pushDataToListAllPropQ\":null,\"payMode\":null,\"loggedIn\":true,\"couponCount\":10,\"siteDomain\":\"in\",\"channel\":\"B2Cweb\",\"pageContext\":\"LISTING\",\"firstTimeUserState\":0,\"uuid\":null,\"corpAuthCode\":null,\"corpUserId\":null,\"seoCorp\":false,\"requestor\":null,\"wishCode\":null,\"preApprovedValidity\":null,\"metaInfo\":false,\"zcp\":null,\"requisitionID\":null,\"myBizFlowIdentifier\":null,\"brand\":\"MMT\",\"previousTxnKey\":null,\"oldWorkflowId\":null,\"journeyId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"requestId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"sessionId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"promoConsent\":false,\"flyerInfo\":null,\"premium\":false,\"semanticSearchDetails\":null,\"forwardBookingFlow\":false,\"extendedPackageCall\":false,\"isIgnoreSEO\":false,\"isRequestCallBack\":false,\"isListAllPropCall\":false},\"detailDeepLinkUrl\":null,\"sortCriteria\":null,\"filterCriteria\":[],\"appliedBatchKeys\":[],\"filterGroupsToRemove\":null,\"filtersToRemove\":null,\"featureFlags\":{\"staticData\":true,\"reviewSummaryRequired\":true,\"walletRequired\":true,\"shortlistingRequired\":false,\"noOfCoupons\":0,\"noOfAddons\":0,\"noOfPersuasions\":0,\"noOfSoldouts\":0,\"coupon\":true,\"mmtPrime\":false,\"persuationSeg\":null,\"persuasionsEngineHit\":true,\"checkAvailability\":true,\"liteResponse\":false,\"applyAbsorption\":false,\"bestOffersLimit\":0,\"dealOfTheDayRequired\":false,\"addOnRequired\":false,\"roomInfoRequired\":false,\"allInclusions\":false,\"hotelCatAndPropNotRequiredInMeta\":false,\"extraAltAccoRequired\":false,\"limitedFilterCall\":false,\"corpMMRRequired\":false,\"unmodifiedAmenities\":false,\"poisRequiredOnMap\":true,\"persuasionsRequired\":true,\"similarHotel\":false,\"locus\":false,\"comparator\":false,\"originListingMap\":false,\"mostBooked\":false,\"detailMap\":false,\"showUpsell\":false,\"filterRanking\":false,\"quickReview\":false,\"dayUsePersuasion\":false,\"selectiveHotels\":false,\"persuasionSuppression\":false,\"hidePrice\":false,\"showBnplCard\":false,\"modifyBooking\":false,\"cardRequired\":false,\"topCard\":false,\"filters\":false,\"seoDS\":false,\"seoCohort\":null,\"roomPreferenceEnabled\":false,\"flashDealClaimed\":false,\"upsellRateplanRequired\":false,\"orientation\":null,\"elitePackageEnabled\":false,\"premiumThemesCardRequired\":false,\"isGoTribe3_0\":null},\"matchMakerDetails\":{},\"imageDetails\":{\"types\":[\"professional\"],\"categories\":[{\"type\":\"H\",\"count\":1,\"height\":162,\"width\":243,\"imageFormat\":\"webp\"}]},\"reviewDetails\":{\"otas\":[\"MMT\",\"TA\"],\"tagTypes\":[\"BASE\",\"WHAT_GUESTS_SAY\"]},\"expData\":\"{\\\"EMIDT\\\":2,\\\"UGCV2\\\":\\\"T\\\",\\\"HFC\\\":\\\"F\\\",\\\"VIDEO\\\":0,\\\"APT\\\":\\\"T\\\",\\\"CHPC\\\":\\\"T\\\",\\\"LSTNRBY\\\":\\\"T\\\",\\\"AARI\\\":\\\"T\\\",\\\"RCPN\\\":\\\"T\\\",\\\"MRS\\\":\\\"T\\\",\\\"ADDON\\\":\\\"T\\\",\\\"NLP\\\":\\\"Y\\\",\\\"PERNEW\\\":\\\"T\\\",\\\"GRPN\\\":\\\"T\\\",\\\"BNPL\\\":\\\"T\\\",\\\"MCUR\\\":\\\"T\\\",\\\"HAFC\\\":\\\"T\\\",\\\"PLRS\\\":\\\"T\\\",\\\"MMRVER\\\":\\\"V3\\\",\\\"PDO\\\":\\\"PN\\\",\\\"BLACK\\\":\\\"T\\\",\\\"CV2\\\":\\\"T\\\",\\\"FLTRPRCBKT\\\":\\\"T\\\",\\\"RTBC\\\":\\\"T\\\",\\\"MLOS\\\":\\\"T\\\",\\\"WPAH\\\":\\\"F\\\",\\\"AIP\\\":\\\"T\\\",\\\"BNPL0\\\":\\\"T\\\",\\\"HIS\\\":\\\"DEFAULT\\\",\\\"APE\\\":10,\\\"PAH\\\":5,\\\"IAO\\\":\\\"F\\\",\\\"CRF\\\":\\\"B\\\",\\\"ALC\\\":\\\"T\\\",\\\"SOU\\\":\\\"T\\\",\\\"PAH5\\\":\\\"T\\\",\\\"rearch\\\":\\\"True\\\"}\",\"expVariantKeys\":null,\"cohertVar\":null,\"multiCityFilter\":null,\"additionalProperties\":null,\"cardId\":null,\"manthanExpDataMap\":null,\"expDataMap\":null,\"contentExpDataMap\":null,\"userLocation\":null,\"clusterId\":null,\"orgId\":null,\"validExpList\":null,\"variantKeys\":null,\"businessIdentificationEnableFromUserService\":false,\"selectedTabId\":null,\"searchCriteria\":{\"checkIn\":\"2024-10-26\",\"checkOut\":\"2024-10-27\",\"countryCode\":\"IN\",\"cityCode\":\"ZNSHIM\",\"cityName\":null,\"locationId\":\"CTDEL\",\"locationType\":\"znshim\",\"lat\":null,\"lng\":null,\"currency\":\"INR\",\"personalCorpBooking\":false,\"rmDHS\":false,\"boostProperty\":null,\"baseRateplanCode\":null,\"selectedRatePlan\":null,\"multiCurrencyInfo\":null,\"preAppliedFilter\":false,\"roomStayCandidates\":[{\"rooms\":1,\"adultCount\":2,\"childAges\":[]}],\"parentLocationId\":null,\"parentLocationType\":null,\"tripType\":null,\"slot\":null,\"giHotelId\":null,\"hotelIds\":null,\"limit\":1,\"lastHotelId\":null,\"lastFetchedWindowInfo\":null,\"lastHotelCategory\":null,\"personalizedSearch\":true,\"nearBySearch\":false,\"wishListedSearch\":false,\"totalHotelsShown\":null,\"sectionsType\":null,\"collectionCriteria\":null,\"bookingForGuest\":false,\"travellerEmailID\":null,\"vcId\":null},\"lastPeekedOnMapHotelIds\":null,\"mapDetails\":null,\"nearbyFilter\":null,\"filterRemovedCriteria\":null}";
        return mapper.readValue(requestString, SearchHotelsRequest.class);
    }

    @Test
    public void testBuildBottomSheetImplementation() {
        // Create test data
        PersonalizedSectionDetails personalizedSectionDetails = new PersonalizedSectionDetails();
        personalizedSectionDetails.setName("TEST_SECTION");
        
        // Execute the method - iOS implementation returns null
        BottomSheet bottomSheet = orchSearchHotelsResponseTransformerIOS.buildBottomSheet(personalizedSectionDetails);
        
        // Verify the result
        Assert.assertNull(bottomSheet);
    }

    @Test
    public void testAddPersuasionHoverDataImplementation() {
        // Create test data
        Hotel hotel = new Hotel();
        hotel.setHotelPersuasions(new HashMap<>());
        
        HotelDetails hotelEntity = new HotelDetails();
        Map<String, Object> persuasionsMap = new HashMap<>();
        hotelEntity.setHotelPersuasions(persuasionsMap);
        
        // Setup mocks
        when(persuasionUtil.checkIfIndianessPersuasionExists(any())).thenReturn(true);
        
        // Execute the method
        orchSearchHotelsResponseTransformerIOS.addPersuasionHoverData(hotel, hotelEntity, null, null, null);
        
        // Verify that the method sets lovedByIndians flag based on persuasion data
        Assert.assertTrue(hotel.isLovedByIndians());
    }

    @Test
    public void testBuildBGColorImplementation() {
        // Test RECENTLY_VIEWED_HOTELS section
        String bgColor = orchSearchHotelsResponseTransformerIOS.buildBGColor("RECENTLY_VIEWED_HOTELS", "H", "default");
        Assert.assertNull(bgColor); // iOS implementation returns null
        
        // Test SIMILAR_HOTELS section
        bgColor = orchSearchHotelsResponseTransformerIOS.buildBGColor("SIMILAR_HOTELS", "H", "default");
        Assert.assertNull(bgColor); // iOS implementation returns null
    }
    
    @Test
    public void testAddBookingConfirmationPersuasionImplementation() {
        // Create hotel entity for testing
        HotelDetails hotelEntity = new HotelDetails();
        
        // Execute method
        orchSearchHotelsResponseTransformerIOS.addBookingConfirmationPersuasion(hotelEntity);
        
        // This method calls addBookingConfirmationPersuasionForMobile
        // We're just testing that it executes without exceptions
    }

    @Test
    public void testBuildStaticCardImplementation() {
        // Create mock MyBizStaticCard
        MyBizStaticCard mockStaticCard = new MyBizStaticCard();
        mockStaticCard.setText("ORIGINAL_TEXT");
        mockStaticCard.setSubtext("ORIGINAL_SUBTEXT");
        mockStaticCard.setCtaText("ORIGINAL_CTA");

        // Mock commonConfigConsul
        when(commonConfigConsul.getMyBizStaticCard()).thenReturn(mockStaticCard);

        // Mock polyglot service for translation
        when(polyglotService.getTranslatedData("ORIGINAL_TEXT")).thenReturn("Translated Text");
        when(polyglotService.getTranslatedData("ORIGINAL_SUBTEXT")).thenReturn("Translated Subtext");
        when(polyglotService.getTranslatedData("ORIGINAL_CTA")).thenReturn("Translated CTA");

        // Initialize the transformer
        orchSearchHotelsResponseTransformerIOS.init();

        // Test with valid inputs
        List<HotelDetails> hotels = new ArrayList<>();
        HotelDetails hotel = new HotelDetails();
        hotel.setBudgetHotel(false);
        hotels.add(hotel);

        MyBizStaticCard result = orchSearchHotelsResponseTransformerIOS.buildStaticCard("DIRECT_HOTEL", hotels, "https://example.com");

        Assert.assertNotNull(result);
        Assert.assertEquals("https://example.com", result.getActionUrl());
        Assert.assertEquals("Translated Text", result.getText());
        Assert.assertEquals("Translated Subtext", result.getSubtext());
        Assert.assertEquals("Translated CTA", result.getCtaText());
    }

    // ================================
    // Tests for buildStaticCard function
    // ================================

    @Test
    public void should_ReturnNull_When_SectionIsNull() {
        // Given
        String section = null;
        List<HotelDetails> hotels = new ArrayList<>();
        String detailDeepLinkUrl = "https://example.com/hotel";

        // When
        MyBizStaticCard result = 
            orchSearchHotelsResponseTransformerIOS.buildStaticCard(section, hotels, detailDeepLinkUrl);

        // Then
        Assert.assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_SectionIsNotCorpBudgetDirectHotel() {
        // Given
        String section = "REGULAR_HOTELS";
        List<HotelDetails> hotels = new ArrayList<>();
        HotelDetails hotel = new HotelDetails();
        hotel.setBudgetHotel(false);
        hotels.add(hotel);
        String detailDeepLinkUrl = "https://example.com/hotel";

        // When
        MyBizStaticCard result = 
            orchSearchHotelsResponseTransformerIOS.buildStaticCard(section, hotels, detailDeepLinkUrl);

        // Then
        Assert.assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_HotelsListIsEmpty() {
        // Given
        String section = "DIRECT_HOTEL";
        List<HotelDetails> hotels = new ArrayList<>();
        String detailDeepLinkUrl = "https://example.com/hotel";

        // When
        MyBizStaticCard result = 
            orchSearchHotelsResponseTransformerIOS.buildStaticCard(section, hotels, detailDeepLinkUrl);

        // Then
        Assert.assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_HotelsListIsNull() {
        // Given
        String section = "DIRECT_HOTEL";
        List<HotelDetails> hotels = null;
        String detailDeepLinkUrl = "https://example.com/hotel";

        // When
        MyBizStaticCard result = 
            orchSearchHotelsResponseTransformerIOS.buildStaticCard(section, hotels, detailDeepLinkUrl);

        // Then
        Assert.assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_FirstHotelIsBudgetHotel() {
        // Given
        String section = "DIRECT_HOTEL";
        List<HotelDetails> hotels = new ArrayList<>();
        HotelDetails hotel = new HotelDetails();
        hotel.setBudgetHotel(true); // Budget hotel
        hotels.add(hotel);
        String detailDeepLinkUrl = "https://example.com/hotel";

        // When
        MyBizStaticCard result = 
            orchSearchHotelsResponseTransformerIOS.buildStaticCard(section, hotels, detailDeepLinkUrl);

        // Then
        Assert.assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_MyBizStaticCardIsNull() {
        // Given
        String section = "DIRECT_HOTEL";
        List<HotelDetails> hotels = new ArrayList<>();
        HotelDetails hotel = new HotelDetails();
        hotel.setBudgetHotel(false);
        hotels.add(hotel);
        String detailDeepLinkUrl = "https://example.com/hotel";

        // Mock myBizStaticCard as null
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerIOS, "myBizStaticCard", null);

        // When
        MyBizStaticCard result = 
            orchSearchHotelsResponseTransformerIOS.buildStaticCard(section, hotels, detailDeepLinkUrl);

        // Then
        Assert.assertNull(result);
    }

    @Test
    public void should_ReturnClonedStaticCard_When_AllConditionsAreMet() {
        // Given
        String section = "DIRECT_HOTEL";
        List<HotelDetails> hotels = new ArrayList<>();
        HotelDetails hotel = new HotelDetails();
        hotel.setBudgetHotel(false);
        hotels.add(hotel);
        String detailDeepLinkUrl = "https://example.com/hotel-details";

        // Create mock MyBizStaticCard
        MyBizStaticCard mockStaticCard = new MyBizStaticCard();
        mockStaticCard.setText("ORIGINAL_TEXT");
        mockStaticCard.setSubtext("ORIGINAL_SUBTEXT");
        mockStaticCard.setCtaText("ORIGINAL_CTA");
        mockStaticCard.setActionUrl("https://original-url.com");

        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerIOS, "myBizStaticCard", mockStaticCard);

        // Mock polyglot service for translation
        when(polyglotService.getTranslatedData("ORIGINAL_TEXT")).thenReturn("Translated Text");
        when(polyglotService.getTranslatedData("ORIGINAL_SUBTEXT")).thenReturn("Translated Subtext");
        when(polyglotService.getTranslatedData("ORIGINAL_CTA")).thenReturn("Translated CTA");

        // When
        MyBizStaticCard result = 
            orchSearchHotelsResponseTransformerIOS.buildStaticCard(section, hotels, detailDeepLinkUrl);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals(detailDeepLinkUrl, result.getActionUrl());
        Assert.assertEquals("Translated Text", result.getText());
        Assert.assertEquals("Translated Subtext", result.getSubtext());
        Assert.assertEquals("Translated CTA", result.getCtaText());
    }

    @Test
    public void should_HandleCaseInsensitiveSection_When_SectionIsLowerCase() {
        // Given
        String section = "direct_hotel"; // lowercase
        List<HotelDetails> hotels = new ArrayList<>();
        HotelDetails hotel = new HotelDetails();
        hotel.setBudgetHotel(false);
        hotels.add(hotel);
        String detailDeepLinkUrl = "https://example.com/hotel";

        // Create mock MyBizStaticCard
        MyBizStaticCard mockStaticCard = new MyBizStaticCard();
        mockStaticCard.setText("TEST_TEXT");
        mockStaticCard.setSubtext("TEST_SUBTEXT");
        mockStaticCard.setCtaText("TEST_CTA");

        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerIOS, "myBizStaticCard", mockStaticCard);

        // Mock polyglot service
        when(polyglotService.getTranslatedData("TEST_TEXT")).thenReturn("Translated Text");
        when(polyglotService.getTranslatedData("TEST_SUBTEXT")).thenReturn("Translated Subtext");
        when(polyglotService.getTranslatedData("TEST_CTA")).thenReturn("Translated CTA");

        // When
        MyBizStaticCard result = 
            orchSearchHotelsResponseTransformerIOS.buildStaticCard(section, hotels, detailDeepLinkUrl);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals(detailDeepLinkUrl, result.getActionUrl());
    }

    @Test
    public void should_HandleNullDeepLinkUrl_When_DeepLinkIsNull() {
        // Given
        String section = "DIRECT_HOTEL";
        List<HotelDetails> hotels = new ArrayList<>();
        HotelDetails hotel = new HotelDetails();
        hotel.setBudgetHotel(false);
        hotels.add(hotel);
        String detailDeepLinkUrl = null;

        // Create mock MyBizStaticCard
        MyBizStaticCard mockStaticCard = new MyBizStaticCard();
        mockStaticCard.setText("TEST_TEXT");
        mockStaticCard.setSubtext("TEST_SUBTEXT");
        mockStaticCard.setCtaText("TEST_CTA");

        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerIOS, "myBizStaticCard", mockStaticCard);

        // Mock polyglot service
        when(polyglotService.getTranslatedData("TEST_TEXT")).thenReturn("Translated Text");
        when(polyglotService.getTranslatedData("TEST_SUBTEXT")).thenReturn("Translated Subtext");
        when(polyglotService.getTranslatedData("TEST_CTA")).thenReturn("Translated CTA");

        // When
        MyBizStaticCard result = 
            orchSearchHotelsResponseTransformerIOS.buildStaticCard(section, hotels, detailDeepLinkUrl);

        // Then
        Assert.assertNotNull(result);
        Assert.assertNull(result.getActionUrl());
    }

    // ================================
    // Tests for translateStaticCard function
    // ================================

    @Test
    public void should_TranslateAllFields_When_StaticCardHasAllFields() throws Exception {
        // Given
        MyBizStaticCard staticCard = new MyBizStaticCard();
        staticCard.setText("ORIGINAL_TEXT_KEY");
        staticCard.setSubtext("ORIGINAL_SUBTEXT_KEY");
        staticCard.setCtaText("ORIGINAL_CTA_KEY");

        // Mock polyglot service
        when(polyglotService.getTranslatedData("ORIGINAL_TEXT_KEY")).thenReturn("Translated Main Text");
        when(polyglotService.getTranslatedData("ORIGINAL_SUBTEXT_KEY")).thenReturn("Translated Sub Text");
        when(polyglotService.getTranslatedData("ORIGINAL_CTA_KEY")).thenReturn("Translated CTA Button");

        // When
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerIOS, "translateStaticCard", staticCard);

        // Then
        Assert.assertEquals("Translated Main Text", staticCard.getText());
        Assert.assertEquals("Translated Sub Text", staticCard.getSubtext());
        Assert.assertEquals("Translated CTA Button", staticCard.getCtaText());
    }

    @Test
    public void should_HandleNullFields_When_StaticCardHasNullFields() throws Exception {
        // Given
        MyBizStaticCard staticCard = new MyBizStaticCard();
        staticCard.setText(null);
        staticCard.setSubtext(null);
        staticCard.setCtaText(null);

        // Mock polyglot service to return null for null input
        when(polyglotService.getTranslatedData(null)).thenReturn(null);

        // When
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerIOS, "translateStaticCard", staticCard);

        // Then
        Assert.assertNull(staticCard.getText());
        Assert.assertNull(staticCard.getSubtext());
        Assert.assertNull(staticCard.getCtaText());
    }

    @Test
    public void should_HandleEmptyFields_When_StaticCardHasEmptyFields() throws Exception {
        // Given
        MyBizStaticCard staticCard = new MyBizStaticCard();
        staticCard.setText("");
        staticCard.setSubtext("");
        staticCard.setCtaText("");

        // Mock polyglot service
        when(polyglotService.getTranslatedData("")).thenReturn("Default Translation");

        // When
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerIOS, "translateStaticCard", staticCard);

        // Then
        Assert.assertEquals("Default Translation", staticCard.getText());
        Assert.assertEquals("Default Translation", staticCard.getSubtext());
        Assert.assertEquals("Default Translation", staticCard.getCtaText());
    }

    @Test
    public void should_HandleSpecialCharacters_When_StaticCardHasSpecialCharacterKeys() throws Exception {
        // Given
        MyBizStaticCard staticCard = new MyBizStaticCard();
        staticCard.setText("TEXT_WITH_SPECIAL_CHARS_@#$");
        staticCard.setSubtext("SUBTEXT_WITH_UNICODE_αβγ");
        staticCard.setCtaText("CTA_WITH_NUMBERS_123");

        // Mock polyglot service
        when(polyglotService.getTranslatedData("TEXT_WITH_SPECIAL_CHARS_@#$")).thenReturn("Special Characters Translated");
        when(polyglotService.getTranslatedData("SUBTEXT_WITH_UNICODE_αβγ")).thenReturn("Unicode Characters Translated");
        when(polyglotService.getTranslatedData("CTA_WITH_NUMBERS_123")).thenReturn("Numbers Translated");

        // When
        ReflectionTestUtils.invokeMethod(orchSearchHotelsResponseTransformerIOS, "translateStaticCard", staticCard);

        // Then
        Assert.assertEquals("Special Characters Translated", staticCard.getText());
        Assert.assertEquals("Unicode Characters Translated", staticCard.getSubtext());
        Assert.assertEquals("Numbers Translated", staticCard.getCtaText());
    }

    // ================================
    // Tests for buildQuickBookCard function
    // ================================

    @Test
    public void should_BuildCompleteHotelCard_When_QuickBookInfoHasAllFields() {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo quickBookInfo = 
            new com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo();
        quickBookInfo.setTitleWithPrice("Deluxe Room - ₹5000");
        quickBookInfo.setRoomPersuasion("Free WiFi, Breakfast Included");
        quickBookInfo.setRoomPersuasionWithSize("Free WiFi, Breakfast Included - 25 sqm");
        quickBookInfo.setShowQuickBookCard(true);

        // Mock polyglot service for iOS-specific translations
        when(polyglotService.getTranslatedData(com.mmt.hotels.clientgateway.constants.ConstantsTranslation.BOOK_NOW))
            .thenReturn("Book Now");

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.HotelCard result = 
            orchSearchHotelsResponseTransformerIOS.buildQuickBookCard(quickBookInfo);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals("Deluxe Room - ₹5000", result.getHeading());
        Assert.assertEquals("Free WiFi, Breakfast Included", result.getSubHeading());
        Assert.assertEquals("Free WiFi, Breakfast Included - 25 sqm", result.getRoomSubHeading());
        Assert.assertTrue(result.getShowCard());
        Assert.assertEquals("Book Now", result.getCta());
        
        // Verify background gradient (iOS specific)
        Assert.assertNotNull(result.getBgLinearGradient());
        Assert.assertEquals("leftBottomToTopRight", result.getBgLinearGradient().getDirection());
        Assert.assertEquals("#2D6F95", result.getBgLinearGradient().getStart());
        Assert.assertEquals("#192B43", result.getBgLinearGradient().getEnd());
    }

    @Test
    public void should_BuildHotelCardWithNullFields_When_QuickBookInfoHasNullFields() {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo quickBookInfo = 
            new com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo();
        quickBookInfo.setTitleWithPrice(null);
        quickBookInfo.setRoomPersuasion(null);
        quickBookInfo.setRoomPersuasionWithSize(null);
        quickBookInfo.setShowQuickBookCard(false);

        // Mock polyglot service
        when(polyglotService.getTranslatedData(com.mmt.hotels.clientgateway.constants.ConstantsTranslation.BOOK_NOW))
            .thenReturn("Book Now");

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.HotelCard result = 
            orchSearchHotelsResponseTransformerIOS.buildQuickBookCard(quickBookInfo);

        // Then
        Assert.assertNotNull(result);
        Assert.assertNull(result.getHeading());
        Assert.assertNull(result.getSubHeading());
        Assert.assertNull(result.getRoomSubHeading());
        Assert.assertFalse(result.getShowCard());
        Assert.assertEquals("Book Now", result.getCta());
        
        // Background gradient should still be set
        Assert.assertNotNull(result.getBgLinearGradient());
    }

    @Test
    public void should_BuildHotelCardWithEmptyFields_When_QuickBookInfoHasEmptyFields() {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo quickBookInfo = 
            new com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo();
        quickBookInfo.setTitleWithPrice("");
        quickBookInfo.setRoomPersuasion("");
        quickBookInfo.setRoomPersuasionWithSize("");
        quickBookInfo.setShowQuickBookCard(true);

        // Mock polyglot service
        when(polyglotService.getTranslatedData(com.mmt.hotels.clientgateway.constants.ConstantsTranslation.BOOK_NOW))
            .thenReturn("Réserver Maintenant"); // French translation

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.HotelCard result = 
            orchSearchHotelsResponseTransformerIOS.buildQuickBookCard(quickBookInfo);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals("", result.getHeading());
        Assert.assertEquals("", result.getSubHeading());
        Assert.assertEquals("", result.getRoomSubHeading());
        Assert.assertTrue(result.getShowCard());
        Assert.assertEquals("Réserver Maintenant", result.getCta());
    }

    @Test
    public void should_HandleLongText_When_QuickBookInfoHasLongStrings() {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo quickBookInfo = 
            new com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo();
        
        String longTitle = "Premium Executive Suite with Ocean View, King Size Bed, Living Area, and Complimentary Airport Transfer - ₹15000 per night";
        String longPersuasion = "Free High-Speed WiFi, Complimentary Breakfast Buffet, Access to Executive Lounge, Swimming Pool, Fitness Center, Spa Services, 24/7 Room Service, Concierge Service, Valet Parking";
        String longPersuasionWithSize = longPersuasion + " - Spacious 45 square meters with panoramic city and ocean views";
        
        quickBookInfo.setTitleWithPrice(longTitle);
        quickBookInfo.setRoomPersuasion(longPersuasion);
        quickBookInfo.setRoomPersuasionWithSize(longPersuasionWithSize);
        quickBookInfo.setShowQuickBookCard(true);

        // Mock polyglot service
        when(polyglotService.getTranslatedData(com.mmt.hotels.clientgateway.constants.ConstantsTranslation.BOOK_NOW))
            .thenReturn("Book Now");

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.HotelCard result = 
            orchSearchHotelsResponseTransformerIOS.buildQuickBookCard(quickBookInfo);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals(longTitle, result.getHeading());
        Assert.assertEquals(longPersuasion, result.getSubHeading());
        Assert.assertEquals(longPersuasionWithSize, result.getRoomSubHeading());
        Assert.assertTrue(result.getShowCard());
        Assert.assertEquals("Book Now", result.getCta());
    }

    @Test
    public void should_HandleSpecialCharacters_When_QuickBookInfoHasSpecialCharacters() {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo quickBookInfo = 
            new com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo();
        quickBookInfo.setTitleWithPrice("Deluxe Room with A/C & WiFi - ₹3,500.50");
        quickBookInfo.setRoomPersuasion("Free WiFi @ 100Mbps, A/C 24x7, TV w/ 200+ channels");
        quickBookInfo.setRoomPersuasionWithSize("Free WiFi @ 100Mbps, A/C 24x7, TV w/ 200+ channels - 30m²");
        quickBookInfo.setShowQuickBookCard(true);

        // Mock polyglot service
        when(polyglotService.getTranslatedData(com.mmt.hotels.clientgateway.constants.ConstantsTranslation.BOOK_NOW))
            .thenReturn("Book Now");

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.HotelCard result = 
            orchSearchHotelsResponseTransformerIOS.buildQuickBookCard(quickBookInfo);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals("Deluxe Room with A/C & WiFi - ₹3,500.50", result.getHeading());
        Assert.assertEquals("Free WiFi @ 100Mbps, A/C 24x7, TV w/ 200+ channels", result.getSubHeading());
        Assert.assertEquals("Free WiFi @ 100Mbps, A/C 24x7, TV w/ 200+ channels - 30m²", result.getRoomSubHeading());
        Assert.assertTrue(result.getShowCard());
        Assert.assertEquals("Book Now", result.getCta());
    }

    @Test
    public void should_AlwaysSetBgLinearGradient_When_BuildingQuickBookCard() {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo quickBookInfo = 
            new com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo();
        quickBookInfo.setShowQuickBookCard(false);

        // Mock polyglot service
        when(polyglotService.getTranslatedData(com.mmt.hotels.clientgateway.constants.ConstantsTranslation.BOOK_NOW))
            .thenReturn("Book Now");

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.HotelCard result = 
            orchSearchHotelsResponseTransformerIOS.buildQuickBookCard(quickBookInfo);

        // Then
        Assert.assertNotNull(result);
        
        // Verify background gradient is always set
        Assert.assertNotNull(result.getBgLinearGradient());
        Assert.assertEquals("leftBottomToTopRight", result.getBgLinearGradient().getDirection());
        Assert.assertEquals("#2D6F95", result.getBgLinearGradient().getStart());
        Assert.assertEquals("#192B43", result.getBgLinearGradient().getEnd());
    }

    @Test
    public void should_HandleNullQuickBookInfo_When_QuickBookInfoIsNull() {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo quickBookInfo = null;


        // When
        com.mmt.hotels.clientgateway.response.searchHotels.HotelCard result = 
            orchSearchHotelsResponseTransformerIOS.buildQuickBookCard(quickBookInfo);

        // Then
        Assert.assertNull(result);
    }

    @Test
    public void should_NotHaveDesktopStylingClasses_When_BuildingIOSQuickBookCard() {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo quickBookInfo = 
            new com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo();
        quickBookInfo.setTitleWithPrice("Test Room");
        quickBookInfo.setShowQuickBookCard(true);

        // Mock polyglot service
        when(polyglotService.getTranslatedData(com.mmt.hotels.clientgateway.constants.ConstantsTranslation.BOOK_NOW))
            .thenReturn("Book Now");

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.HotelCard result = 
            orchSearchHotelsResponseTransformerIOS.buildQuickBookCard(quickBookInfo);

        // Then
        Assert.assertNotNull(result);
        
        // Verify iOS implementation does NOT include DesktopStylingClassesObj
        Assert.assertNull(result.getDesktopStylingClassesObj());
        
        // But background gradient should still be set with iOS-specific values
        Assert.assertNotNull(result.getBgLinearGradient());
        Assert.assertEquals("leftBottomToTopRight", result.getBgLinearGradient().getDirection());
    }

    @Test
    public void should_UseDirectAssignment_When_BuildingIOSQuickBookCard() {
        // Given
        com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo quickBookInfo = 
            new com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo();
        quickBookInfo.setTitleWithPrice("Standard Room");
        quickBookInfo.setRoomPersuasion("Free WiFi");
        quickBookInfo.setRoomPersuasionWithSize("Free WiFi - 20 sqm");
        quickBookInfo.setShowQuickBookCard(true);

        // Mock polyglot service
        when(polyglotService.getTranslatedData(com.mmt.hotels.clientgateway.constants.ConstantsTranslation.BOOK_NOW))
            .thenReturn("Book Now");

        // When
        com.mmt.hotels.clientgateway.response.searchHotels.HotelCard result = 
            orchSearchHotelsResponseTransformerIOS.buildQuickBookCard(quickBookInfo);

        // Then
        Assert.assertNotNull(result);
        // iOS uses direct assignment (no template replacement like Desktop)
        Assert.assertEquals("Standard Room", result.getHeading());
        Assert.assertEquals("Free WiFi", result.getSubHeading());
        Assert.assertEquals("Free WiFi - 20 sqm", result.getRoomSubHeading());
        Assert.assertTrue(result.getShowCard());
        Assert.assertEquals("Book Now", result.getCta());
    }

}