package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.Persuasions;
import com.mmt.hotels.clientgateway.response.moblanding.QuestionCG;
import com.mmt.hotels.clientgateway.response.moblanding.WikiPoi;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.transformer.response.ios.MobLandingResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.SearchHotelsResponseTransformerPWA;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.HomeStayDetails;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.pojo.matchmaker.LatLongAndBounds;
import com.mmt.hotels.pojo.matchmaker.WikiQuestion;
import junit.framework.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MobLandingResponseTransformerIOSTest {
    @InjectMocks
    MobLandingResponseTransformerIOS mobLandingResponseTransformerIOS;

    @Mock
    SearchHotelsResponseTransformerPWA searchHotelsResponseTransformerPWA;

    @Mock
    SearchHotelsFactory searchHotelsFactory;

    @Spy
    ObjectMapperUtil objectMapperUtil;

    @Spy
    CommonResponseTransformer commonResponseTransformer;

    @Mock
    PersuasionUtil persuasionUtil;

    @Test
    public void addLocationPersuasionToHotelPersuasionsTest() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        List<String> locationPersuasion = new ArrayList<>();
        searchWrapperHotelEntity.setLocationPersuasion(locationPersuasion);
        mobLandingResponseTransformerIOS.addLocationPersuasionToHotelPersuasions(searchWrapperHotelEntity);
        locationPersuasion.add("test1");
        searchWrapperHotelEntity.setLocationPersuasion(locationPersuasion);
        mobLandingResponseTransformerIOS.addLocationPersuasionToHotelPersuasions(searchWrapperHotelEntity);
        locationPersuasion.add("test2");
        mobLandingResponseTransformerIOS.addLocationPersuasionToHotelPersuasions(searchWrapperHotelEntity);
    }

    @Test
    public void addPersuasionsForHiddenGemCardTest() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        searchWrapperHotelEntity.setHotelPersuasions(new HashMap<>());
        searchWrapperHotelEntity.setHiddenGem(true);
        searchWrapperHotelEntity.setHiddenGemPersuasionText("Hidden Gem Persuasion Text");
        HomeStayDetails homeStayDetails = new HomeStayDetails();
        homeStayDetails.setStayType(Constants.STAY_TYPE_HOMESTAY);
        homeStayDetails.setStayTypeInfo("Home Stay Info Test");

        PersuasionObject homeStaysTitlePersuasion = new PersuasionObject();
        List<PersuasionData> homeStaysTitlePersuasionList = new ArrayList<>();
        homeStaysTitlePersuasionList.add(new PersuasionData());
        homeStaysTitlePersuasion.setData(homeStaysTitlePersuasionList);

        PersuasionObject homestaysSubTitlePersuasion = new PersuasionObject();
        homestaysSubTitlePersuasion.setData(Arrays.asList(new PersuasionData()));

        when(persuasionUtil.buildHiddenGemPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
        when(persuasionUtil.buildHiddenGemIconPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
        when(persuasionUtil.buildHomeStaysTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homeStaysTitlePersuasion);
        when(persuasionUtil.buildHomeStaysSubTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homestaysSubTitlePersuasion);
        mobLandingResponseTransformerIOS.addPersuasionsForHiddenGemCard(searchWrapperHotelEntity);
        Assert.assertNotNull(searchWrapperHotelEntity.getHotelPersuasions());
        Assert.assertTrue(searchWrapperHotelEntity.getHotelPersuasions() instanceof Map<?, ?>);
        Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdApps()));
        Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdApps()));
        Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdApps()));
        Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdApps()));
    }

    @Test
    public void testAddPivotAndZoomLevelForCityMap(){
        QuestionCG questionCG = new QuestionCG();
        WikiQuestion wikiQuestion = new WikiQuestion();
        LatLongAndBounds deskPivot = new LatLongAndBounds();
        deskPivot.setLat("123.4");
        deskPivot.setLng("234.5");
        wikiQuestion.setDeskPivot(deskPivot);
        wikiQuestion.setDeskZoomLevel(1);
        LatLongAndBounds pivot = new LatLongAndBounds();
        pivot.setLat("345.6");
        pivot.setLng("456.7");
        wikiQuestion.setPivot(pivot);
        wikiQuestion.setZoomLevel(2);
        mobLandingResponseTransformerIOS.addPivotAndZoomLevelForCityMap(questionCG,wikiQuestion);
        Assert.assertEquals(questionCG.getPivot().getLat(),pivot.getLat());
        Assert.assertEquals(questionCG.getZoomLevel(),wikiQuestion.getZoomLevel());
    }

    @Test
    public void buildCompletedRequests_ShouldReturnNull_WhenCompletedRequestsIsEmpty() {
        Set<String> completedRequests = new HashSet<>();

        List<String> result = ReflectionTestUtils.invokeMethod(mobLandingResponseTransformerIOS, "buildCompletedRequests", completedRequests);

        Assert.assertNull(result);
    }

    @Test
    public void buildCompletedRequests_ShouldReturnListOfRequests_WhenCompletedRequestsIsNotEmpty() {
        Set<String> completedRequests = new HashSet<>(Arrays.asList("Request1", "Request2"));

        List<String> result = ReflectionTestUtils.invokeMethod(mobLandingResponseTransformerIOS, "buildCompletedRequests", completedRequests);

    }

    @Test
    public void buildCompletedRequests_ShouldReturnNull_WhenCompletedRequestsIsNull() {
        Set<String> completedRequests = null;

        List<String> result = ReflectionTestUtils.invokeMethod(mobLandingResponseTransformerIOS, "buildCompletedRequests", completedRequests);

        Assert.assertNull(result);
    }

    @Test
    public void buildUuids_ShouldReturnNull_WhenUuidsIsEmpty() {
        Set<String> uuids = new HashSet<>();

        List<String> result = ReflectionTestUtils.invokeMethod(mobLandingResponseTransformerIOS, "buildUuids", uuids);

        Assert.assertNull(result);
    }

    @Test
    public void buildUuids_ShouldReturnListOfUuids_WhenUuidsIsNotEmpty() {
        Set<String> uuids = new HashSet<>(Arrays.asList("UUID1", "UUID2"));

        List<String> result = ReflectionTestUtils.invokeMethod(mobLandingResponseTransformerIOS, "buildUuids", uuids);

    }

    @Test
    public void buildUuids_ShouldReturnNull_WhenUuidsIsNull() {
        Set<String> uuids = null;

        List<String> result = ReflectionTestUtils.invokeMethod(mobLandingResponseTransformerIOS, "buildUuids", uuids);

        Assert.assertNull(result);
    }

    @Test
    public void buildPoiTags_ShouldReturnNull_WhenListIsEmpty() {
        List<com.mmt.hotels.pojo.matchmaker.WikiPoi> list = new ArrayList<>();

        List<WikiPoi> result = ReflectionTestUtils.invokeMethod(mobLandingResponseTransformerIOS, "buildPoiTags", list);

        Assert.assertNull(result);
    }

    @Test
    public void buildPoiTags_ShouldReturnNull_WhenListIsNull() {
        List<com.mmt.hotels.pojo.matchmaker.WikiPoi> list = null;

        List<WikiPoi> result = ReflectionTestUtils.invokeMethod(mobLandingResponseTransformerIOS, "buildPoiTags", list);

        Assert.assertNull(result);
    }

    @Test
    public void buildPoiTags_ShouldReturnListOfWikiPoi_WhenListIsNotEmpty() {
        List<com.mmt.hotels.pojo.matchmaker.WikiPoi> list =new ArrayList<>();

        List<WikiPoi> result = ReflectionTestUtils.invokeMethod(mobLandingResponseTransformerIOS, "buildPoiTags", list);
    }
}
