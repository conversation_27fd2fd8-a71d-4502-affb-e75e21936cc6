package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.Persuasions;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.Coupon;
import com.mmt.hotels.clientgateway.response.EMIDetail;
import com.mmt.hotels.clientgateway.response.PriceDetail;
import com.mmt.hotels.clientgateway.response.dayuse.SlotDetail;
import com.mmt.hotels.clientgateway.response.searchHotels.*;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.response.ios.SearchHotelsResponseTransformerIOS;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.HomeStayDetails;
import com.mmt.hotels.model.response.emi.Emi;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.pricing.HotelPrice;
import com.mmt.hotels.model.response.pricing.Tax;
import com.mmt.hotels.model.response.searchwrapper.*;
import com.mmt.hotels.pojo.response.detail.FeaturedAmenity;
import com.mmt.model.LocusData;
import org.apache.commons.io.FileUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.SHORTSTAYS_FUNNEL;
import static org.mockito.Mockito.when;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class OrchSearchHotelsResponseTransformerIOSTest {
	
	@InjectMocks
	SearchHotelsResponseTransformerIOS searchHotelsResponseTransformerIOS;

	private ObjectMapper objectMapper = new ObjectMapper();

	@Mock
	ObjectMapperUtil objectMapperUtil;

	@Mock
	PolyglotService polyglotService;

	Hotel hotel;

	@Mock
	PersuasionUtil persuasionUtil;

	@Before
	public void init() throws IOException {
			ObjectMapper mapper = new ObjectMapper();
			mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
			mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
			InputStream availPriceRequest = getClass().getClassLoader().getResourceAsStream("mockrequestresponsetest/hotel.json");
			hotel = mapper.readValue(availPriceRequest, Hotel.class);
			MyBizStaticCard myBizStaticCard = new MyBizStaticCard();
			myBizStaticCard.setText("CORPBUDGET_STATIC_TEXT");
			myBizStaticCard.setSubtext("CORPBUDGET_STATIC_SUBTEXT");
			myBizStaticCard.setCtaText("CORPBUDGET_STATIC_CTATEXT");
			ReflectionTestUtils.setField(searchHotelsResponseTransformerIOS,"myBizStaticCard",myBizStaticCard);
	}

	@Test
	public void addLocationPersuasionToHotelPersuasionsTest(){
		List<String> locations = new ArrayList<>();
		locations.add("This is test location persuasion");
		LinkedHashSet<String> ameneties = new LinkedHashSet<>();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setDayUsePersuasionsText("Test_Persuasions");
		ameneties.add("WI-FI");
		ameneties.add("bathtub");
		hotel.setHotelPersuasions(new HashMap<>());
		SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
		searchHotelsRequest.setRequestDetails(new RequestDetails());
		searchHotelsRequest.getRequestDetails().setFunnelSource("DAYUSE");
		searchHotelsResponseTransformerIOS.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null,null,null, false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		org.junit.Assert.assertEquals(((Map<String, Object>)hotel.getHotelPersuasions()).size(),2);

		locations.add("one more test persuasion");
		hotel.setHotelPersuasions(new HashMap<>());
		searchHotelsResponseTransformerIOS.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null,"Less than 1 hr",null, false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		org.junit.Assert.assertEquals(((Map<String, Object>)hotel.getHotelPersuasions()).size(),2);

		//Test for Secondary Location Persuasion
		String secondaryPersuasion = "Secondary Persuasion";
		locations.add(secondaryPersuasion);
		hotel.setHotelPersuasions(new HashMap<>());
		searchHotelsResponseTransformerIOS.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null,null,null, false);
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).size());
		org.junit.Assert.assertNotNull(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP));
		org.junit.Assert.assertTrue(((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP) instanceof PersuasionObject);
		org.junit.Assert.assertNotNull(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP)).getData());
		org.junit.Assert.assertTrue(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP)).getData().size() > 0);
		org.junit.Assert.assertTrue(((PersuasionObject)((Map<String, Object>)hotel.getHotelPersuasions()).get(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP)).getData().get(0).getText().contains(secondaryPersuasion));


		searchHotelsRequest.getRequestDetails().setFunnelSource(SHORTSTAYS_FUNNEL);
		LocusData locusData = new LocusData();
		locusData.setLocusName("anc");
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("{city_text} {city_text} {driving_text}");
		searchHotelsResponseTransformerIOS.addLocationPersuasionToHotelPersuasions(hotel, locations, ameneties, searchHotelsRequest, true, false, hotelEntity.getDayUsePersuasionsText(), null,"drive time",locusData, false);
	}

	@Test
	public void testBuildQuickBookCard() {
		Assert.assertNotNull(searchHotelsResponseTransformerIOS.buildQuickBookCard(new QuickBookInfo()));
	}

	@Test
	public void testGetMyBizDirectHotelDistanceText() {
		Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("");
		Assert.assertNotNull(searchHotelsResponseTransformerIOS.getMyBizDirectHotelDistanceText("test"));
	}

	@Test
	public void testBuildBottomSheet() throws IOException {
		ListingPagePersonalizationResponsBO webApiResponse = new Gson().fromJson(
				FileUtils.readFileToString(ResourceUtils.getFile("classpath:listing/listingPersonalizationHESResponse.json")),
				ListingPagePersonalizationResponsBO.class);
		searchHotelsResponseTransformerIOS.buildBottomSheet(webApiResponse.getPersonalizedResponse().get(0));
	}

	@Test
	public void testBuildStaticCard(){
		MyBizStaticCard staticCard = new MyBizStaticCard();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setCorpBudgetHotel(false);
		hotelEntity.setDetailDeeplinkUrl("test");
		List<SearchWrapperHotelEntity> hotelEntityList = new ArrayList<>();
		hotelEntityList.add(hotelEntity);
		Mockito.when(polyglotService.getTranslatedData("CORPBUDGET_STATIC_TEXT")).thenReturn("This Is Not A Budget Hotel");
		staticCard = searchHotelsResponseTransformerIOS.buildStaticCard("DIRECT_HOTEL",hotelEntityList);
		Assert.assertNotNull(staticCard);
		Assert.assertEquals("This Is Not A Budget Hotel", staticCard.getText());
	}

	@Test
	public void addPersuasionsForHiddenGemCardTest() {
		SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
		searchWrapperHotelEntity.setLocationPersuasion(Collections.singletonList("Test Location"));
		searchWrapperHotelEntity.setHiddenGem(true);
		searchWrapperHotelEntity.setHiddenGemPersuasionText("Hidden Gem Persuasion Text");
		HomeStayDetails homeStayDetails = new HomeStayDetails();
		homeStayDetails.setStayType(Constants.STAY_TYPE_HOMESTAY);
		homeStayDetails.setStayTypeInfo("Home Stay Info Test");

		PersuasionObject homeStaysTitlePersuasion = new PersuasionObject();
		List<PersuasionData> homeStaysTitlePersuasionList = new ArrayList<>();
		homeStaysTitlePersuasionList.add(new PersuasionData());
		homeStaysTitlePersuasion.setData(homeStaysTitlePersuasionList);

		PersuasionObject homestaysSubTitlePersuasion = new PersuasionObject();
		homestaysSubTitlePersuasion.setData(Arrays.asList(new PersuasionData()));

		when(persuasionUtil.buildHiddenGemPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
		when(persuasionUtil.buildHiddenGemIconPersuasion(Mockito.any(), Mockito.anyString())).thenReturn(new PersuasionObject());
		when(persuasionUtil.buildHomeStaysTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homeStaysTitlePersuasion);
		when(persuasionUtil.buildHomeStaysSubTitlePersuasion(Mockito.any(), Mockito.anyString())).thenReturn(homestaysSubTitlePersuasion);
		searchHotelsResponseTransformerIOS.addPersuasionsForHiddenGemCard(searchWrapperHotelEntity);
		junit.framework.Assert.assertNotNull(searchWrapperHotelEntity.getHotelPersuasions());
		junit.framework.Assert.assertTrue(searchWrapperHotelEntity.getHotelPersuasions() instanceof Map<?, ?>);
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdApps()));
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdApps()));
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdApps()));
		junit.framework.Assert.assertNotNull(((Map<?, ?>)searchWrapperHotelEntity.getHotelPersuasions()).get(Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdApps()));
	}

	@Test
	public void getPartnerDetails_ShouldReturnPriceDetailWithMarkUp_WhenAllPricesAreNonNull() {
		PriceDetail priceDetail = new PriceDetail();
		priceDetail.setPrice(100.0);
		priceDetail.setPriceWithTax(110.0);
		priceDetail.setDiscountedPrice(90.0);
		priceDetail.setDiscountedPriceWithTax(99.0);
		double markUp = 10.0;

		PriceDetail result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "getPartnerDetails", priceDetail, markUp);



		Assert.assertEquals(110.0, result.getPrice(), 0.0);
		Assert.assertEquals(120.0, result.getPriceWithTax(), 0.0);
		Assert.assertEquals(100.0, result.getDiscountedPrice(), 0.0);
		Assert.assertEquals(109.0, result.getDiscountedPriceWithTax(), 0.0);
	}

	@Test
	public void getPartnerDetails_ShouldReturnPriceDetailWithOriginalPrices_WhenAnyPriceIsNull() {
		PriceDetail priceDetail = new PriceDetail();
		priceDetail.setPrice(null);
		priceDetail.setPriceWithTax(110.0);
		priceDetail.setDiscountedPrice(90.0);
		priceDetail.setDiscountedPriceWithTax(99.0);
		double markUp = 10.0;

		PriceDetail result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "getPartnerDetails", priceDetail, markUp);

	}

	@Test
	public void getPartnerDetails_ShouldReturnPriceDetailWithOriginalPrices_WhenAllPricesAreNull() {
		PriceDetail priceDetail = new PriceDetail();
		priceDetail.setPrice(null);
		priceDetail.setPriceWithTax(null);
		priceDetail.setDiscountedPrice(null);
		priceDetail.setDiscountedPriceWithTax(null);
		double markUp = 10.0;

		PriceDetail result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "getPartnerDetails", priceDetail, markUp);

		Assert.assertNull(result.getPrice());
		Assert.assertNull(result.getPriceWithTax());
		Assert.assertNull(result.getDiscountedPrice());
		Assert.assertNull(result.getDiscountedPriceWithTax());
	}

	@Test
	public void buildHiddenGemPersuasions_ShouldAddPersuasions_WhenHotelListIsNotEmpty() {
		List<SearchWrapperHotelEntity> hotelList = new ArrayList<>();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelList.add(hotelEntity);

		searchHotelsResponseTransformerIOS.buildHiddenGemPersuasions(hotelList);

		Assert.assertNotNull(hotelEntity.getHotelPersuasions());
	}

	@Test
	public void buildHiddenGemPersuasions_ShouldNotThrowException_WhenHotelListIsEmpty() {
		List<SearchWrapperHotelEntity> hotelList = new ArrayList<>();

		searchHotelsResponseTransformerIOS.buildHiddenGemPersuasions(hotelList);

		Assert.assertTrue(hotelList.isEmpty());
	}

	@Test
	public void buildHiddenGemPersuasions_ShouldHandleNullHotelPersuasions() {
		List<SearchWrapperHotelEntity> hotelList = new ArrayList<>();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setHotelPersuasions(null);
		hotelList.add(hotelEntity);

		searchHotelsResponseTransformerIOS.buildHiddenGemPersuasions(hotelList);

		Assert.assertNotNull(hotelEntity.getHotelPersuasions());
	}

	@Test
	public void buildPriceDetailForDayUse_ShouldReturnNull_WhenDisplayFareIsNull() {
		DisplayFare displayFare = null;

		PriceDetail result = searchHotelsResponseTransformerIOS.buildPriceDetailForDayUse(displayFare);

		Assert.assertNull(result);
	}

	@Test
	public void buildPriceDetailForDayUse_ShouldReturnPriceDetailWithZeroTax_WhenTaxIsNull() {
		DisplayFare displayFare = new DisplayFare();
		displayFare.setTax(null);

		PriceDetail result = searchHotelsResponseTransformerIOS.buildPriceDetailForDayUse(displayFare);
	}

	@Test
	public void buildPriceDetailForDayUse_ShouldReturnPriceDetailWithZeroValues_WhenSlashedPriceIsNull() {
		DisplayFare displayFare = new DisplayFare();
		Tax tax = new Tax();
		tax.setValue(10.0);
		displayFare.setTax(tax);
		displayFare.setSlashedPrice(null);

		PriceDetail result = searchHotelsResponseTransformerIOS.buildPriceDetailForDayUse(displayFare);

		Assert.assertNotNull(result);
		Assert.assertEquals(10.0, result.getTotalTax(), 0.0);
		Assert.assertNull(result.getPrice());
		Assert.assertNull(result.getPriceWithTax());
		Assert.assertNull(result.getDiscountedPrice());
		Assert.assertNull(result.getDiscountedPriceWithTax());
	}

	@Test
	public void buildHotelTopCard_ShouldReturnNull_WhenMyBizSimilarToDirectObjIsNull() {
		MyBizSimilarToDirectObj myBizSimilarToDirectObj = null;

		HotelCard result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildHotelTopCard", myBizSimilarToDirectObj);

		Assert.assertNull(result);
	}

	@Test
	public void buildHotelTopCard_ShouldReturnHotelCardWithCorrectValues_WhenMyBizSimilarToDirectObjIsNotNull() {
		MyBizSimilarToDirectObj myBizSimilarToDirectObj = new MyBizSimilarToDirectObj();
		myBizSimilarToDirectObj.setDistance("5 km");
//		myBizSimilarToDirectObj.setAmenities(Collections.singletonList(new FeaturedAmenity("WiFi")));

		Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_DIRECT_HOTEL_SUBHEADING)).thenReturn("Subheading");
		Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_DIRECT_HOTEL_TAG)).thenReturn("Tag");

		HotelCard result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildHotelTopCard", myBizSimilarToDirectObj);
	}

	@Test
	public void buildHotelTopCard_ShouldReturnHotelCardWithoutAmenities_WhenAmenitiesListIsEmpty() {
		MyBizSimilarToDirectObj myBizSimilarToDirectObj = new MyBizSimilarToDirectObj();
		myBizSimilarToDirectObj.setDistance("5 km");
		myBizSimilarToDirectObj.setAmenities(Collections.emptyList());

		Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_DIRECT_HOTEL_SUBHEADING)).thenReturn("Subheading");
		Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_DIRECT_HOTEL_TAG)).thenReturn("Tag");

		HotelCard result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildHotelTopCard", myBizSimilarToDirectObj);
	}

	@Test
	public void getFacility_ShouldReturnFacilityWithCorrectValues_WhenFeaturedAmenityIsNotNull() {
		FeaturedAmenity myBizAmenity = new FeaturedAmenity();
		myBizAmenity.setIconUrl("http://example.com/icon.png");
		myBizAmenity.setName("WiFi");

		Facility result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "getFacility", myBizAmenity);


		Assert.assertNotNull(result);
		Assert.assertEquals("http://example.com/icon.png", result.getIconUrl());
		Assert.assertEquals("WiFi", result.getName());
	}

	@Test
	public void getFacility_ShouldReturnFacilityWithEmptyValues_WhenFeaturedAmenityHasEmptyFields() {
		FeaturedAmenity myBizAmenity = new FeaturedAmenity();
		myBizAmenity.setIconUrl("");
		myBizAmenity.setName("");

		Facility result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "getFacility", myBizAmenity);

		Assert.assertNotNull(result);
		Assert.assertEquals("", result.getIconUrl());
		Assert.assertEquals("", result.getName());
	}

	@Test
	public void buildEmiDetails_ShouldReturnNull_WhenEmiInfoIsNull() {
		Emi emiInfo = null;

		EMIDetail result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildEmiDetails", emiInfo);


		Assert.assertNull(result);
	}

	@Test
	public void buildEmiDetails_ShouldReturnEmiDetailWithCorrectValues_WhenEmiInfoIsNotNull() {
		Emi emiInfo = new Emi();
		emiInfo.setEmiAmount(1000);
		emiInfo.setEmiType("Type1");
		emiInfo.setBankName("Bank1");
		emiInfo.setTenure(12);
		emiInfo.setTotalCost(1200);
		emiInfo.setTotalInterest(200);

		EMIDetail result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildEmiDetails", emiInfo);

		Assert.assertNotNull(result);
		Assert.assertEquals(1000.0, result.getAmount(), 0.0);
		Assert.assertEquals("Type1", result.getType());
		Assert.assertEquals("Bank1", result.getBankName());
		Assert.assertEquals(12, result.getTenure());
		Assert.assertEquals(1200.0, result.getTotalCost(), 0.0);
		Assert.assertEquals(200.0, result.getTotalInterest(), 0.0);
	}

	@Test
	public void buildEmiDetails_ShouldReturnEmiDetailWithZeroValues_WhenEmiInfoHasZeroValues() {
		Emi emiInfo = new Emi();
		emiInfo.setEmiAmount(0);
		emiInfo.setEmiType("");
		emiInfo.setBankName("");
		emiInfo.setTenure(0);
		emiInfo.setTotalCost(0);
		emiInfo.setTotalInterest(0);

		EMIDetail result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildEmiDetails", emiInfo);

		Assert.assertNotNull(result);
		Assert.assertEquals(0.0, result.getAmount(), 0.0);
		Assert.assertEquals("", result.getType());
		Assert.assertEquals("", result.getBankName());
		Assert.assertEquals(0, result.getTenure());
		Assert.assertEquals(0.0, result.getTotalCost(), 0.0);
		Assert.assertEquals(0.0, result.getTotalInterest(), 0.0);
	}

	@Test
	public void buildCoupon_ShouldReturnNull_WhenCouponInfoIsNull() {
		BestCoupon couponInfo = null;

		Coupon result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildCoupon", couponInfo);

		Assert.assertNull(result);
	}

	@Test
	public void buildCoupon_ShouldReturnCouponWithCorrectValues_WhenCouponInfoIsNotNull() {
		BestCoupon couponInfo = new BestCoupon();
		couponInfo.setDescription("10% off");
		couponInfo.setCouponCode("SAVE10");
		couponInfo.setType("Percentage");
		couponInfo.setDiscountAmount(10.0);
		couponInfo.setPromoIconLink("http://example.com/icon.png");

		Coupon result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildCoupon", couponInfo);

		Assert.assertNotNull(result);
		Assert.assertEquals("10% off", result.getDescription());
		Assert.assertEquals("SAVE10", result.getCode());
		Assert.assertEquals("Percentage", result.getType());
		Assert.assertEquals(10.0, result.getCouponAmount(), 0.0);
		Assert.assertEquals("http://example.com/icon.png", result.getPromoIcon());
	}

	@Test
	public void buildCoupon_ShouldReturnCouponWithGenericBankIcon_WhenPromoIconLinkIsEmpty() {
		BestCoupon couponInfo = new BestCoupon();
		couponInfo.setDescription("10% off");
		couponInfo.setCouponCode("SAVE10");
		couponInfo.setType("Percentage");
		couponInfo.setDiscountAmount(10.0);
		couponInfo.setPromoIconLink("");

		Coupon result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildCoupon", couponInfo);

		Assert.assertNotNull(result);
		Assert.assertEquals("10% off", result.getDescription());
		Assert.assertEquals("SAVE10", result.getCode());
		Assert.assertEquals("Percentage", result.getType());
		Assert.assertEquals(10.0, result.getCouponAmount(), 0.0);
//		Assert.assertTrue(result.isSpecialPromo());
//		Assert.assertEquals(genericBankIcon, result.getPromoIcon());
	}

	@Test
	public void buildSortCriteria_ShouldReturnNull_WhenCriteriaIsNull() {
		com.mmt.hotels.model.request.SortCriteria criteria = null;

		SortCriteria result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildSortCriteria", criteria);

		Assert.assertNull(result);
	}

	@Test
	public void buildSortCriteria_ShouldReturnSortCriteriaWithCorrectValues_WhenCriteriaIsNotNull() {
		com.mmt.hotels.model.request.SortCriteria criteria = new com.mmt.hotels.model.request.SortCriteria();
		criteria.setField("price");
		criteria.setOrder("asc");

		SortCriteria result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildSortCriteria", criteria);

		Assert.assertNotNull(result);
		Assert.assertEquals("price", result.getField());
		Assert.assertEquals("asc", result.getOrder());
	}

	@Test
	public void buildMyBizAssuredToolTip_ShouldReturnToolTipWithCorrectValues() {
		Mockito.when(polyglotService.getTranslatedData("WHY_MYBIZ_ASSURED_TEXT")).thenReturn("Why MyBiz Assured");
		Mockito.when(polyglotService.getTranslatedData("RATED_HIGH_BT")).thenReturn("Rated High");
		Mockito.when(polyglotService.getTranslatedData("GST_INVOICE_ASSURANCE_TEXT")).thenReturn("GST Invoice Assurance");
		Mockito.when(polyglotService.getTranslatedData("BPG_TEXT")).thenReturn("Best Price Guarantee");

		ToolTip result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildMyBizAssuredToolTip");

		Assert.assertNotNull(result);
		Assert.assertEquals("Why MyBiz Assured", result.getHeading());
		Assert.assertNotNull(result.getData());
		Assert.assertEquals(3, result.getData().size());
		Assert.assertTrue(result.getData().contains("Rated High"));
		Assert.assertTrue(result.getData().contains("GST Invoice Assurance"));
		Assert.assertTrue(result.getData().contains("Best Price Guarantee"));
	}

	@Test
	public void buildMyBizAssuredToolTip_ShouldReturnToolTipWithEmptyValues_WhenTranslationsAreEmpty() {
		Mockito.when(polyglotService.getTranslatedData("WHY_MYBIZ_ASSURED_TEXT")).thenReturn("");
		Mockito.when(polyglotService.getTranslatedData("RATED_HIGH_BT")).thenReturn("");
		Mockito.when(polyglotService.getTranslatedData("GST_INVOICE_ASSURANCE_TEXT")).thenReturn("");
		Mockito.when(polyglotService.getTranslatedData("BPG_TEXT")).thenReturn("");

		ToolTip result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildMyBizAssuredToolTip");

		Assert.assertNotNull(result);
		Assert.assertEquals("", result.getHeading());
		Assert.assertNotNull(result.getData());
		Assert.assertEquals(3, result.getData().size());
		Assert.assertTrue(result.getData().contains(""));
	}

	@Test
	public void getMybizSimilarHotelsFeatures_ShouldReturnListWithCorrectUrlsAndTexts() {
		Mockito.when(polyglotService.getTranslatedData("RATED_HIGH_BT")).thenReturn("Rated High");
		Mockito.when(polyglotService.getTranslatedData("GST_INVOICE_ASSURANCE_TEXT")).thenReturn("GST Invoice Assurance");
		Mockito.when(polyglotService.getTranslatedData("BPG_TEXT")).thenReturn("Best Price Guarantee");

		List<SectionFeature> result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "getMybizSimilarHotelsFeatures");

		Assert.assertNotNull(result);
		Assert.assertEquals(3, result.size());
		Assert.assertEquals("Best Price Guarantee", result.get(2).getText());
	}

	@Test
	public void getMybizSimilarHotelsFeatures_ShouldReturnListWithEmptyTexts_WhenTranslationsAreEmpty() {
		Mockito.when(polyglotService.getTranslatedData("RATED_HIGH_BT")).thenReturn("");
		Mockito.when(polyglotService.getTranslatedData("GST_INVOICE_ASSURANCE_TEXT")).thenReturn("");
		Mockito.when(polyglotService.getTranslatedData("BPG_TEXT")).thenReturn("");

		List<SectionFeature> result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "getMybizSimilarHotelsFeatures");

		Assert.assertNotNull(result);
		Assert.assertEquals(3, result.size());
		Assert.assertEquals("", result.get(2).getText());
	}

	@Test
	public void buildSoldOutInfo_ShouldReturnNull_WhenSoldOutInfoIsNull() {
		SoldOutInfo soldOutInfo = null;

		SoldOutInfoCG result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildSoldOutInfo", soldOutInfo);

		Assert.assertNull(result);
	}

	@Test
	public void buildSoldOutInfo_ShouldReturnSoldOutInfoCGWithCorrectValues_WhenSoldOutInfoIsNotNull() {
		SoldOutInfo soldOutInfo = new SoldOutInfo();
		soldOutInfo.setSoldOutText("Sold Out");
		soldOutInfo.setSoldOutSubText("No rooms available");
		soldOutInfo.setSoldOutReason("High demand");
		soldOutInfo.setSoldOutType("Type1");

		SoldOutInfoCG result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildSoldOutInfo", soldOutInfo);

		Assert.assertNotNull(result);
		Assert.assertEquals("Sold Out", result.getSoldOutText());
		Assert.assertEquals("No rooms available", result.getSoldOutSubText());
		Assert.assertEquals("High demand", result.getSoldOutReason());
		Assert.assertEquals("Type1", result.getSoldOutType());
	}

	@Test
	public void buildSoldOutInfo_ShouldReturnSoldOutInfoCGWithEmptyValues_WhenSoldOutInfoHasEmptyFields() {
		SoldOutInfo soldOutInfo = new SoldOutInfo();
		soldOutInfo.setSoldOutText("");
		soldOutInfo.setSoldOutSubText("");
		soldOutInfo.setSoldOutReason("");
		soldOutInfo.setSoldOutType("");

		SoldOutInfoCG result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildSoldOutInfo", soldOutInfo);

		Assert.assertNotNull(result);
		Assert.assertEquals("", result.getSoldOutText());
		Assert.assertEquals("", result.getSoldOutSubText());
		Assert.assertEquals("", result.getSoldOutReason());
		Assert.assertEquals("", result.getSoldOutType());
	}

	@Test
	public void buildCohortReviewSummary_ShouldReturnEmptyString_WhenCohortReviewSummaryIsNull() {
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setCohortReviewSummary(null);

		String result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildCohortReviewSummary", hotelEntity);

		Assert.assertEquals("", result);
	}

	@Test
	public void buildCohortReviewSummary_ShouldReturnEmptyString_WhenCohortReviewSummaryIsEmpty() {
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setCohortReviewSummary("");

		String result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildCohortReviewSummary", hotelEntity);

		Assert.assertEquals("", result);
	}

	@Test
	public void buildCohortReviewSummary_ShouldReturnFormattedString_WhenCohortReviewSummaryIsNotEmpty() {
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setCohortReviewSummary("Great hotel with excellent service");

		String result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildCohortReviewSummary", hotelEntity);

		Assert.assertEquals("<span>Great hotel with excellent service</span>", result);
	}

	@Test
	public void buildShortDescSeo_ShouldReturnFormattedString_WhenShortDescSeoIsNotEmptyAndViewOnMapIsTrue() {
		String shortDescSeo = "Beautiful hotel with great amenities";
		boolean viewOnMap = true;

		String result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildShortDescSeo", shortDescSeo, viewOnMap);

		Assert.assertEquals("<span>Beautiful hotel with great amenities<span class=\"blueText\"><b>View On Map</b></span></span>", result);
	}

	@Test
	public void buildShortDescSeo_ShouldReturnFormattedString_WhenShortDescSeoIsNotEmptyAndViewOnMapIsFalse() {
		String shortDescSeo = "Beautiful hotel with great amenities";
		boolean viewOnMap = false;

		String result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildShortDescSeo", shortDescSeo, viewOnMap);

		Assert.assertEquals("<span>Beautiful hotel with great amenities</span>", result);
	}

	@Test
	public void buildShortDescSeo_ShouldReturnFormattedString_WhenShortDescSeoIsEmptyAndViewOnMapIsTrue() {
		String shortDescSeo = "";
		boolean viewOnMap = true;

		String result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildShortDescSeo", shortDescSeo, viewOnMap);

		Assert.assertEquals("<span><span class=\"blueText\"><b>View On Map</b></span></span>", result);
	}

	@Test
	public void buildShortDescSeo_ShouldReturnFormattedString_WhenShortDescSeoIsEmptyAndViewOnMapIsFalse() {
		String shortDescSeo = "";
		boolean viewOnMap = false;

		String result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildShortDescSeo", shortDescSeo, viewOnMap);

		Assert.assertEquals("<span></span>", result);
	}

	@Test
	public void buildCalendarCriteria_ShouldReturnNull_WhenCalendarCriteriaHESIsNull() {
		CalendarCriteria result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildCalendarCriteria", (Object) null);

		Assert.assertNull(result);
	}

	@Test
	public void buildCalendarCriteria_ShouldReturnCalendarCriteriaWithCorrectValues_WhenCalendarCriteriaHESIsNotNull() {
		com.mmt.hotels.pojo.CalendarAvailability.CalendarCriteria calendarCriteriaHES = new com.mmt.hotels.pojo.CalendarAvailability.CalendarCriteria();
		calendarCriteriaHES.setAdvanceDays(10);
		calendarCriteriaHES.setAvailable(true);
		calendarCriteriaHES.setMaxDate("2023-12-31");
		calendarCriteriaHES.setMlos(2);

		CalendarCriteria result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildCalendarCriteria", calendarCriteriaHES);

		Assert.assertNotNull(result);
		Assert.assertEquals(10, result.getAdvanceDays());
		Assert.assertTrue(result.isAvailable());
		Assert.assertEquals("2023-12-31", result.getMaxDate());
		Assert.assertEquals(2, result.getMlos());
	}

	@Test
	public void buildTrackingMapReturnsEmptyMapWhenTrackingVariablesIsNull() {
		TrackingVariables trackingVariables = null;

		Map<String, String> result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildTrackingMap", trackingVariables);

		Assert.assertNotNull(result);
		Assert.assertTrue(result.isEmpty());
	}

	@Test
	public void buildTrackingMapReturnsEmptyMapWhenFamilyFriendlyIsFalse() {
		TrackingVariables trackingVariables = new TrackingVariables();
		trackingVariables.setFamilyFriendly(false);

		Map<String, String> result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildTrackingMap", trackingVariables);

		Assert.assertNotNull(result);
		Assert.assertTrue(result.isEmpty());
	}

	@Test
	public void buildTrackingMapReturnsMapWithFamilyFriendlyTrackingWhenFamilyFriendlyIsTrue() {
		TrackingVariables trackingVariables = new TrackingVariables();
		trackingVariables.setFamilyFriendly(true);

		Map<String, String> result = ReflectionTestUtils.invokeMethod(searchHotelsResponseTransformerIOS, "buildTrackingMap", trackingVariables);

		Assert.assertNotNull(result);
		Assert.assertEquals(1, result.size());
	}
	
}