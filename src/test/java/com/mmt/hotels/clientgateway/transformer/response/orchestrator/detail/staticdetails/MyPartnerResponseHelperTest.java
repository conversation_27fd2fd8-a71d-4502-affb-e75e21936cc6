package com.mmt.hotels.clientgateway.transformer.response.orchestrator.detail.staticdetails;

import com.gommt.hotels.orchestrator.detail.model.response.common.DisplayItem;
import com.gommt.hotels.orchestrator.detail.model.response.mypartner.DisplayMeta;
import com.gommt.hotels.orchestrator.detail.model.response.mypartner.ErrorResponse;
import com.gommt.hotels.orchestrator.detail.model.response.mypartner.HeroCard;
import com.gommt.hotels.orchestrator.detail.model.response.mypartner.LoyaltyResponse;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.MarkUpDetails;
import com.mmt.hotels.clientgateway.response.MarkUpConfig;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.MyPartnerResponseHelper;
import com.mmt.hotels.model.response.errors.LoyaltyError;
import com.mmt.hotels.model.response.persuasion.MyPartnerLoyaltyResponse;
import com.mmt.hotels.model.response.persuasion.TextIconUrl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class MyPartnerResponseHelperTest {

    @InjectMocks
    private MyPartnerResponseHelper myPartnerResponseHelper;
    
    // Test subclass to access protected methods
    private static class TestableMyPartnerResponseHelper extends MyPartnerResponseHelper {
        @Override
        public MyPartnerLoyaltyResponse mapLoyaltyResponseToMyPartnerLoyaltyResponse(LoyaltyResponse loyaltyResponse) {
            return super.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        }
    }
    
    private TestableMyPartnerResponseHelper testableMapper;

    private LoyaltyResponse loyaltyResponse;
    private HeroCard heroCard;
    private DisplayMeta displayMeta;
    private ErrorResponse errorResponse;
    private MarkUpDetails markUpDetails;
    private MarkUpConfig markUpConfig;

    @BeforeEach
    void setUp() {
        testableMapper = new TestableMyPartnerResponseHelper();
        setupTestData();
    }

    private void setupTestData() {
        // Setup DisplayMeta
        displayMeta = DisplayMeta.builder()
                .iconUrl("https://example.com/icon.png")
                .iconText("Icon Text")
                .bannerBgImage("https://example.com/banner.jpg")
                .bgGradient(Arrays.asList("#FF0000", "#00FF00"))
                .iconBgGradient(Arrays.asList("#0000FF", "#FFFF00"))
                .bannerBgGradient(Arrays.asList("#FF00FF", "#00FFFF"))
                .headerTextColorBg(Arrays.asList("#000000", "#FFFFFF"))
                .build();

        // Setup DisplayItem
        DisplayItem displayItem = DisplayItem.builder()
                .iconUrl("https://example.com/item-icon.png")
                .text("Display Item Text")
                .build();

        // Setup HeroCard
        heroCard = HeroCard.builder()
                .cardId("card-123")
                .messageTitle("Test Message Title")
                .messageText("Test Message Text")
                .tierName("Gold")
                .cta("Book Now")
                .ctaLink("https://example.com/book")
                .ctaActionType("REDIRECT")
                .textIconUrlList(Arrays.asList(displayItem))
                .displayMeta(displayMeta)
                .build();

        // Setup ErrorResponse
        errorResponse = ErrorResponse.builder()
                .code("E001")
                .msg("Test error message")
                .type("VALIDATION_ERROR")
                .build();

        // Setup LoyaltyResponse
        loyaltyResponse = LoyaltyResponse.builder()
                .tierNumber(2)
                .loyaltyEligible(true)
                .walletEarn(150.0)
                .tierName("Gold")
                .status("ACTIVE")
                .errors(Arrays.asList(errorResponse))
                .primaryCard(heroCard)
                .secondaryCards(Arrays.asList(heroCard))
                .build();

        // Setup MarkUpDetails
        markUpDetails = new MarkUpDetails();
        markUpDetails.setMarkupEligible(true);
        markUpDetails.setEditable(true);

        // Setup MarkUpConfig
        markUpConfig = new MarkUpConfig();
        markUpConfig.setText("Markup Text");
        markUpConfig.setFlagValue(true);
        markUpConfig.setHoverText("Hover Text");
        markUpConfig.setCtaUrl("https://example.com/cta");
        markUpConfig.setCtaText("CTA Text");
    }

    // =============================================================================
    // Tests for mapLoyaltyResponseToMyPartnerLoyaltyResponse (protected method)
    // =============================================================================

    @Test
    void testMapLoyaltyResponseToMyPartnerLoyaltyResponse_NullInput() {
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(null);
        assertNull(result);
    }

    @Test
    void testMapLoyaltyResponseToMyPartnerLoyaltyResponse_ValidInput() {
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        
        assertNotNull(result);
        assertEquals(2, result.getTierNumber());
        assertTrue(result.isLoyaltyEligible());
        assertEquals(150.0, result.getWalletEarn());
        assertEquals("Gold", result.getTierName());
        assertEquals("ACTIVE", result.getStatus());
        
        // Verify errors mapping
        assertNotNull(result.getErrors());
        assertEquals(1, result.getErrors().size());
        LoyaltyError mappedError = result.getErrors().get(0);
        assertEquals("E001", mappedError.getCode());
        assertEquals("Test error message", mappedError.getMsg());
        assertEquals("VALIDATION_ERROR", mappedError.getType());
        
        // Verify primary card mapping
        assertNotNull(result.getPrimaryCard());
        assertEquals("card-123", result.getPrimaryCard().getCardId());
        
        // Verify secondary cards mapping
        assertNotNull(result.getSecondaryCards());
        assertEquals(1, result.getSecondaryCards().size());
        assertEquals("card-123", result.getSecondaryCards().get(0).getCardId());
    }

    @Test
    void testMapLoyaltyResponseToMyPartnerLoyaltyResponse_NullErrors() {
        loyaltyResponse.setErrors(null);
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        
        assertNotNull(result);
        assertNull(result.getErrors());
    }

    @Test
    void testMapLoyaltyResponseToMyPartnerLoyaltyResponse_EmptyErrors() {
        loyaltyResponse.setErrors(new ArrayList<>());
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        
        assertNotNull(result);
        assertNull(result.getErrors());
    }

    @Test
    void testMapLoyaltyResponseToMyPartnerLoyaltyResponse_NullPrimaryCard() {
        loyaltyResponse.setPrimaryCard(null);
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        
        assertNotNull(result);
        assertNull(result.getPrimaryCard());
    }

    @Test
    void testMapLoyaltyResponseToMyPartnerLoyaltyResponse_NullSecondaryCards() {
        loyaltyResponse.setSecondaryCards(null);
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        
        assertNotNull(result);
        assertNull(result.getSecondaryCards());
    }

    @Test
    void testMapLoyaltyResponseToMyPartnerLoyaltyResponse_EmptySecondaryCards() {
        loyaltyResponse.setSecondaryCards(new ArrayList<>());
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        
        assertNotNull(result);
        assertNull(result.getSecondaryCards());
    }

    @Test
    void testMapLoyaltyResponseToMyPartnerLoyaltyResponse_MultipleErrors() {
        ErrorResponse error2 = ErrorResponse.builder()
                .code("E002")
                .msg("Second error message")
                .type("BUSINESS_ERROR")
                .build();
        loyaltyResponse.setErrors(Arrays.asList(errorResponse, error2));
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        
        assertNotNull(result.getErrors());
        assertEquals(2, result.getErrors().size());
        assertEquals("E001", result.getErrors().get(0).getCode());
        assertEquals("E002", result.getErrors().get(1).getCode());
    }

    @Test
    void testMapLoyaltyResponseToMyPartnerLoyaltyResponse_MultipleSecondaryCards() {
        HeroCard heroCard2 = HeroCard.builder()
                .cardId("card-456")
                .messageTitle("Second Card Title")
                .build();
        loyaltyResponse.setSecondaryCards(Arrays.asList(heroCard, heroCard2));
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        
        assertNotNull(result.getSecondaryCards());
        assertEquals(2, result.getSecondaryCards().size());
        assertEquals("card-123", result.getSecondaryCards().get(0).getCardId());
        assertEquals("card-456", result.getSecondaryCards().get(1).getCardId());
    }

    @Test
    void testMapLoyaltyResponseToMyPartnerLoyaltyResponse_AllFieldsNull() {
        LoyaltyResponse emptyResponse = LoyaltyResponse.builder().build();
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(emptyResponse);
        
        assertNotNull(result);
        assertEquals(0, result.getTierNumber());
        assertFalse(result.isLoyaltyEligible());
        assertNull(result.getWalletEarn());
        assertNull(result.getTierName());
        assertNull(result.getStatus());
        assertNull(result.getErrors());
        assertNull(result.getPrimaryCard());
        assertNull(result.getSecondaryCards());
    }

    @Test
    void testMapLoyaltyResponseToMyPartnerLoyaltyResponse_MinimalValidData() {
        LoyaltyResponse minimalResponse = LoyaltyResponse.builder()
                .tierNumber(1)
                .loyaltyEligible(false)
                .walletEarn(0.0)
                .tierName("")
                .status("")
                .build();
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(minimalResponse);
        
        assertNotNull(result);
        assertEquals(1, result.getTierNumber());
        assertFalse(result.isLoyaltyEligible());
        assertEquals(0.0, result.getWalletEarn());
        assertEquals("", result.getTierName());
        assertEquals("", result.getStatus());
    }

    @Test
    void testMapLoyaltyResponseToMyPartnerLoyaltyResponse_NegativeValues() {
        LoyaltyResponse negativeResponse = LoyaltyResponse.builder()
                .tierNumber(-1)
                .loyaltyEligible(false)
                .walletEarn(-50.0)
                .build();
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(negativeResponse);
        
        assertNotNull(result);
        assertEquals(-1, result.getTierNumber());
        assertFalse(result.isLoyaltyEligible());
        assertEquals(-50.0, result.getWalletEarn());
    }

    // =============================================================================
    // Tests for mapHeroCardToMyPartnerHeroCard (private method via reflection)
    // =============================================================================

    @Test
    void testMapHeroCardToMyPartnerHeroCard_NullInput() {
        try {
            Method method = MyPartnerResponseHelper.class.getDeclaredMethod(
                    "mapHeroCardToMyPartnerHeroCard", HeroCard.class);
            method.setAccessible(true);
            
            com.mmt.hotels.model.response.persuasion.MyPartnerHeroCard result = 
                (com.mmt.hotels.model.response.persuasion.MyPartnerHeroCard) method.invoke(myPartnerResponseHelper, (HeroCard) null);
            
            assertNull(result);
        } catch (Exception e) {
            fail("Exception occurred while testing mapHeroCardToMyPartnerHeroCard with null input: " + e.getMessage());
        }
    }

    @Test
    void testMapHeroCardToMyPartnerHeroCard_ValidInput() {
        // Test through public method that calls private method
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        
        assertNotNull(result.getPrimaryCard());
        com.mmt.hotels.model.response.persuasion.MyPartnerHeroCard mappedCard = result.getPrimaryCard();
        
        assertEquals("card-123", mappedCard.getCardId());
        assertEquals("Test Message Title", mappedCard.getMessageTitle());
        assertEquals("Test Message Text", mappedCard.getMessageText());
        assertEquals("Gold", mappedCard.getTierName());
        assertEquals("Book Now", mappedCard.getCta());
        assertEquals("https://example.com/book", mappedCard.getCtaLink());
        assertEquals("REDIRECT", mappedCard.getCtaActionType());
        
        // Verify DisplayMeta mapping
        assertNotNull(mappedCard.getDisplayMeta());
        assertEquals("https://example.com/icon.png", mappedCard.getDisplayMeta().getIconUrl());
        assertEquals("Icon Text", mappedCard.getDisplayMeta().getIconText());
        
        // Verify TextIconUrl mapping
        assertNotNull(mappedCard.getTextIconUrlList());
        assertEquals(1, mappedCard.getTextIconUrlList().size());
        TextIconUrl textIconUrl = mappedCard.getTextIconUrlList().get(0);
        assertEquals("https://example.com/item-icon.png", textIconUrl.getIconUrl());
        assertEquals("Display Item Text", textIconUrl.getText());
    }

    @Test
    void testMapHeroCardToMyPartnerHeroCard_NullDisplayMeta() {
        heroCard.setDisplayMeta(null);
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        
        assertNotNull(result.getPrimaryCard());
        assertNull(result.getPrimaryCard().getDisplayMeta());
    }

    @Test
    void testMapHeroCardToMyPartnerHeroCard_NullTextIconUrlList() {
        heroCard.setTextIconUrlList(null);
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        
        assertNotNull(result.getPrimaryCard());
        assertNull(result.getPrimaryCard().getTextIconUrlList());
    }

    @Test
    void testMapHeroCardToMyPartnerHeroCard_EmptyTextIconUrlList() {
        heroCard.setTextIconUrlList(new ArrayList<>());
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        
        assertNotNull(result.getPrimaryCard());
        assertNull(result.getPrimaryCard().getTextIconUrlList());
    }

    @Test
    void testMapHeroCardToMyPartnerHeroCard_MultipleTextIconUrls() {
        DisplayItem displayItem2 = DisplayItem.builder()
                .iconUrl("https://example.com/item-icon2.png")
                .text("Display Item Text 2")
                .build();
        heroCard.setTextIconUrlList(Arrays.asList(
            DisplayItem.builder().iconUrl("https://example.com/item-icon.png").text("Display Item Text").build(),
            displayItem2
        ));
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        
        assertNotNull(result.getPrimaryCard().getTextIconUrlList());
        assertEquals(2, result.getPrimaryCard().getTextIconUrlList().size());
        assertEquals("https://example.com/item-icon2.png", result.getPrimaryCard().getTextIconUrlList().get(1).getIconUrl());
        assertEquals("Display Item Text 2", result.getPrimaryCard().getTextIconUrlList().get(1).getText());
    }

    @Test
    void testMapHeroCardToMyPartnerHeroCard_AllFieldsNull() {
        HeroCard emptyCard = HeroCard.builder().build();
        loyaltyResponse.setPrimaryCard(emptyCard);
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        
        assertNotNull(result.getPrimaryCard());
        assertNull(result.getPrimaryCard().getCardId());
        assertNull(result.getPrimaryCard().getMessageTitle());
        assertNull(result.getPrimaryCard().getMessageText());
        assertNull(result.getPrimaryCard().getTierName());
        assertNull(result.getPrimaryCard().getCta());
        assertNull(result.getPrimaryCard().getCtaLink());
        assertNull(result.getPrimaryCard().getCtaActionType());
        assertNull(result.getPrimaryCard().getDisplayMeta());
        assertNull(result.getPrimaryCard().getTextIconUrlList());
    }

    // =============================================================================
    // Tests for mapDisplayMetaToMyPartnerDisplayMeta (private method via reflection)
    // =============================================================================

    @Test
    void testMapDisplayMetaToMyPartnerDisplayMeta_NullInput() {
        try {
            Method method = MyPartnerResponseHelper.class.getDeclaredMethod(
                    "mapDisplayMetaToMyPartnerDisplayMeta", DisplayMeta.class);
            method.setAccessible(true);
            
            com.mmt.hotels.model.response.persuasion.DisplayMeta result = 
                (com.mmt.hotels.model.response.persuasion.DisplayMeta) method.invoke(myPartnerResponseHelper, (DisplayMeta) null);
            
            assertNull(result);
        } catch (Exception e) {
            fail("Exception occurred while testing mapDisplayMetaToMyPartnerDisplayMeta with null input: " + e.getMessage());
        }
    }

    @Test
    void testMapDisplayMetaToMyPartnerDisplayMeta_ValidInput() {
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        
        assertNotNull(result.getPrimaryCard().getDisplayMeta());
        com.mmt.hotels.model.response.persuasion.DisplayMeta mappedDisplayMeta = result.getPrimaryCard().getDisplayMeta();
        
        assertEquals("https://example.com/icon.png", mappedDisplayMeta.getIconUrl());
        assertEquals("Icon Text", mappedDisplayMeta.getIconText());
        assertEquals(Arrays.asList("#FF0000", "#00FF00"), mappedDisplayMeta.getBgGradient());
        assertEquals(Arrays.asList("#000000", "#FFFFFF"), mappedDisplayMeta.getHeaderTextColorBg());
        assertEquals("https://example.com/banner.jpg", mappedDisplayMeta.getBannerBgImage());
        assertEquals(Arrays.asList("#FF00FF", "#00FFFF"), mappedDisplayMeta.getBannerBgGradient());
        assertEquals(Arrays.asList("#0000FF", "#FFFF00"), mappedDisplayMeta.getIconBgGradient());
    }

    @Test
    void testMapDisplayMetaToMyPartnerDisplayMeta_AllFieldsNull() {
        DisplayMeta emptyDisplayMeta = DisplayMeta.builder().build();
        heroCard.setDisplayMeta(emptyDisplayMeta);
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        
        assertNotNull(result.getPrimaryCard().getDisplayMeta());
        com.mmt.hotels.model.response.persuasion.DisplayMeta mappedDisplayMeta = result.getPrimaryCard().getDisplayMeta();
        
        assertNull(mappedDisplayMeta.getIconUrl());
        assertNull(mappedDisplayMeta.getIconText());
        assertNull(mappedDisplayMeta.getBgGradient());
        assertNull(mappedDisplayMeta.getHeaderTextColorBg());
        assertNull(mappedDisplayMeta.getBannerBgImage());
        assertNull(mappedDisplayMeta.getBannerBgGradient());
        assertNull(mappedDisplayMeta.getIconBgGradient());
    }

    @Test
    void testMapDisplayMetaToMyPartnerDisplayMeta_EmptyLists() {
        DisplayMeta emptyListsDisplayMeta = DisplayMeta.builder()
                .iconUrl("https://example.com/icon.png")
                .iconText("Icon Text")
                .bgGradient(Collections.emptyList())
                .iconBgGradient(Collections.emptyList())
                .bannerBgGradient(Collections.emptyList())
                .headerTextColorBg(Collections.emptyList())
                .build();
        heroCard.setDisplayMeta(emptyListsDisplayMeta);
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        
        assertNotNull(result.getPrimaryCard().getDisplayMeta());
        com.mmt.hotels.model.response.persuasion.DisplayMeta mappedDisplayMeta = result.getPrimaryCard().getDisplayMeta();
        
        assertEquals("https://example.com/icon.png", mappedDisplayMeta.getIconUrl());
        assertEquals("Icon Text", mappedDisplayMeta.getIconText());
        assertEquals(Collections.emptyList(), mappedDisplayMeta.getBgGradient());
        assertEquals(Collections.emptyList(), mappedDisplayMeta.getIconBgGradient());
        assertEquals(Collections.emptyList(), mappedDisplayMeta.getBannerBgGradient());
        assertEquals(Collections.emptyList(), mappedDisplayMeta.getHeaderTextColorBg());
    }

    // =============================================================================
    // Tests for buildMarkUpConfig (public method)
    // =============================================================================

    @Test
    void testBuildMarkUpConfig_NullMarkUpDetails() {
        MarkUpConfig result = myPartnerResponseHelper.buildMarkUpConfig(null, markUpConfig);
        assertNull(result);
    }

    @Test
    void testBuildMarkUpConfig_NullMarkUpConfig() {
        MarkUpConfig result = myPartnerResponseHelper.buildMarkUpConfig(markUpDetails, null);
        assertNull(result);
    }

    @Test
    void testBuildMarkUpConfig_BothNull() {
        MarkUpConfig result = myPartnerResponseHelper.buildMarkUpConfig(null, null);
        assertNull(result);
    }

    @Test
    void testBuildMarkUpConfig_NotMarkupEligible() {
        markUpDetails.setMarkupEligible(false);
        
        MarkUpConfig result = myPartnerResponseHelper.buildMarkUpConfig(markUpDetails, markUpConfig);
        assertNull(result);
    }

    @Test
    void testBuildMarkUpConfig_ValidInputEditableTrue() {
        MarkUpConfig result = myPartnerResponseHelper.buildMarkUpConfig(markUpDetails, markUpConfig);
        
        assertNotNull(result);
        assertEquals("Markup Text", result.getText());
        assertTrue(result.isFlagValue());
        assertEquals("Hover Text", result.getHoverText());
        assertEquals("https://example.com/cta", result.getCtaUrl());
        assertEquals("CTA Text", result.getCtaText());
    }

    @Test
    void testBuildMarkUpConfig_ValidInputEditableFalse() {
        markUpDetails.setEditable(false);
        
        MarkUpConfig result = myPartnerResponseHelper.buildMarkUpConfig(markUpDetails, markUpConfig);
        
        assertNotNull(result);
        assertEquals("Markup Text", result.getText());
        assertTrue(result.isFlagValue());
        assertEquals("Hover Text", result.getHoverText());
        assertNull(result.getCtaUrl());
        assertNull(result.getCtaText());
    }

    @Test
    void testBuildMarkUpConfig_ValidInputWithNullValues() {
        markUpConfig.setText(null);
        markUpConfig.setHoverText(null);
        markUpConfig.setCtaUrl(null);
        markUpConfig.setCtaText(null);
        markUpConfig.setFlagValue(false);
        
        MarkUpConfig result = myPartnerResponseHelper.buildMarkUpConfig(markUpDetails, markUpConfig);
        
        assertNotNull(result);
        assertNull(result.getText());
        assertFalse(result.isFlagValue());
        assertNull(result.getHoverText());
        assertNull(result.getCtaUrl());
        assertNull(result.getCtaText());
    }

    @Test
    void testBuildMarkUpConfig_EdgeCaseMarkupEligibleFalse() {
        markUpDetails.setMarkupEligible(false);
        markUpDetails.setEditable(true);
        
        MarkUpConfig result = myPartnerResponseHelper.buildMarkUpConfig(markUpDetails, markUpConfig);
        assertNull(result);
    }

    @Test
    void testBuildMarkUpConfig_MarkUpDetailsWithNullEditable() {
        MarkUpDetails markUpDetailsWithNullEditable = new MarkUpDetails();
        markUpDetailsWithNullEditable.setMarkupEligible(true);
        // editable is null/false by default
        
        MarkUpConfig result = myPartnerResponseHelper.buildMarkUpConfig(markUpDetailsWithNullEditable, markUpConfig);
        
        assertNotNull(result);
        assertEquals("Markup Text", result.getText());
        assertTrue(result.isFlagValue());
        assertEquals("Hover Text", result.getHoverText());
        assertNull(result.getCtaUrl());
        assertNull(result.getCtaText());
    }

    @Test
    void testBuildMarkUpConfig_EmptyStrings() {
        markUpConfig.setText("");
        markUpConfig.setHoverText("");
        markUpConfig.setCtaUrl("");
        markUpConfig.setCtaText("");
        
        MarkUpConfig result = myPartnerResponseHelper.buildMarkUpConfig(markUpDetails, markUpConfig);
        
        assertNotNull(result);
        assertEquals("", result.getText());
        assertEquals("", result.getHoverText());
        assertEquals("", result.getCtaUrl());
        assertEquals("", result.getCtaText());
    }

    @Test
    void testBuildMarkUpConfig_LongStrings() {
        String longText = "This is a very long markup text that contains many characters and should be handled properly by the mapper";
        markUpConfig.setText(longText);
        markUpConfig.setHoverText(longText);
        markUpConfig.setCtaUrl("https://example.com/very/long/url/path/with/many/segments/and/parameters?param1=value1&param2=value2");
        markUpConfig.setCtaText(longText);
        
        MarkUpConfig result = myPartnerResponseHelper.buildMarkUpConfig(markUpDetails, markUpConfig);
        
        assertNotNull(result);
        assertEquals(longText, result.getText());
        assertEquals(longText, result.getHoverText());
        assertEquals("https://example.com/very/long/url/path/with/many/segments/and/parameters?param1=value1&param2=value2", result.getCtaUrl());
        assertEquals(longText, result.getCtaText());
    }

    // =============================================================================
    // Additional comprehensive edge case tests
    // =============================================================================

    @Test
    void testMapLoyaltyResponseToMyPartnerLoyaltyResponse_ErrorsWithNullFields() {
        ErrorResponse nullFieldsError = ErrorResponse.builder()
                .code(null)
                .msg(null)
                .type(null)
                .build();
        loyaltyResponse.setErrors(Arrays.asList(nullFieldsError));
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        
        assertNotNull(result.getErrors());
        assertEquals(1, result.getErrors().size());
        LoyaltyError mappedError = result.getErrors().get(0);
        assertNull(mappedError.getCode());
        assertNull(mappedError.getMsg());
        assertNull(mappedError.getType());
    }

    @Test
    void testMapLoyaltyResponseToMyPartnerLoyaltyResponse_SingleSecondaryCard() {
        loyaltyResponse.setSecondaryCards(Arrays.asList(heroCard));
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(loyaltyResponse);
        
        assertNotNull(result.getSecondaryCards());
        assertEquals(1, result.getSecondaryCards().size());
        assertEquals("card-123", result.getSecondaryCards().get(0).getCardId());
    }

    @Test
    void testMapLoyaltyResponseToMyPartnerLoyaltyResponse_LargeValues() {
        LoyaltyResponse largeValuesResponse = LoyaltyResponse.builder()
                .tierNumber(Integer.MAX_VALUE)
                .loyaltyEligible(true)
                .walletEarn(Double.MAX_VALUE)
                .tierName("VeryLongTierNameThatExceedsNormalLimits")
                .status("VeryLongStatusThatExceedsNormalLimits")
                .build();
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(largeValuesResponse);
        
        assertNotNull(result);
        assertEquals(Integer.MAX_VALUE, result.getTierNumber());
        assertTrue(result.isLoyaltyEligible());
        assertEquals(Double.MAX_VALUE, result.getWalletEarn());
        assertEquals("VeryLongTierNameThatExceedsNormalLimits", result.getTierName());
        assertEquals("VeryLongStatusThatExceedsNormalLimits", result.getStatus());
    }

    @Test
    void testCompleteWorkflow_NullInputsAtEachLevel() {
        // Test complete workflow with null inputs at different levels
        LoyaltyResponse workflowResponse = LoyaltyResponse.builder()
                .tierNumber(1)
                .loyaltyEligible(true)
                .walletEarn(100.0)
                .tierName("Silver")
                .status("ACTIVE")
                .primaryCard(HeroCard.builder()
                        .cardId("workflow-card")
                        .messageTitle("Workflow Title")
                        .displayMeta(null)
                        .textIconUrlList(null)
                        .build())
                .secondaryCards(null)
                .errors(null)
                .build();
        
        MyPartnerLoyaltyResponse result = testableMapper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(workflowResponse);
        
        assertNotNull(result);
        assertEquals(1, result.getTierNumber());
        assertTrue(result.isLoyaltyEligible());
        assertEquals(100.0, result.getWalletEarn());
        assertEquals("Silver", result.getTierName());
        assertEquals("ACTIVE", result.getStatus());
        
        assertNotNull(result.getPrimaryCard());
        assertEquals("workflow-card", result.getPrimaryCard().getCardId());
        assertEquals("Workflow Title", result.getPrimaryCard().getMessageTitle());
        assertNull(result.getPrimaryCard().getDisplayMeta());
        assertNull(result.getPrimaryCard().getTextIconUrlList());
        
        assertNull(result.getSecondaryCards());
        assertNull(result.getErrors());
    }
} 