package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.request.ListingSearchRequestV2;
import com.mmt.hotels.clientgateway.response.LocationDetail;
import com.mmt.hotels.clientgateway.response.ReviewSummary;
import com.mmt.hotels.clientgateway.response.listing.ExploreMoreData;
import com.mmt.hotels.clientgateway.response.listing.TreelsListingResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.FilterCriteria;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.response.searchwrapper.ListingProduct;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntityAbridged;
import com.mmt.hotels.model.response.searchwrapper.TreelsCTA;
import com.mmt.hotels.model.response.searchwrapper.TreelsListingResponseBO;
import com.mmt.hotels.model.response.searchwrapper.TreelsResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;

@RunWith(MockitoJUnitRunner.class)
public class TreelsResponseTransformerTest {

    @InjectMocks
    private TreelsResponseTransformer treelsResponseTransformer;

    @Mock
    private CommonResponseTransformer commonResponseTransformer;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    private Utility utility;

    @Test
    public void convertTreelsResponseTest() {
        TreelsListingResponseBO treelsListingResponseBO = new TreelsListingResponseBO();
        treelsListingResponseBO.setTreelsResponse(new TreelsResponse());
        treelsListingResponseBO.getTreelsResponse().setListingProducts(new ArrayList<>());
        treelsListingResponseBO.getTreelsResponse().getListingProducts().add(new ListingProduct());
        treelsListingResponseBO.getTreelsResponse().getListingProducts().get(0).setHotels(new ArrayList<>());
        treelsListingResponseBO.getTreelsResponse().getListingProducts().get(0).getHotels().add(new SearchWrapperHotelEntityAbridged());
//        Mockito.when(utility.buildLocationDetail(Mockito.anyString(),Mockito.anyString(), Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(new LocationDetail());
        TreelsListingResponse treelsListingResponse = treelsResponseTransformer.convertTreelsResponse(treelsListingResponseBO, new ListingSearchRequestV2(), new SearchWrapperInputRequest());
        Assert.assertFalse(treelsListingResponse.isAllTreelsShown());
        Assert.assertNotNull(treelsListingResponse.getProducts());
        Assert.assertNotNull(treelsListingResponse.getProducts().get(0).getHotels());
    }

    @Test
    public void convertTreelsResponseNPETest() {
        TreelsListingResponseBO treelsListingResponseBO = new TreelsListingResponseBO();
        Assert.assertNull(treelsResponseTransformer.convertTreelsResponse(treelsListingResponseBO, new ListingSearchRequestV2(), new SearchWrapperInputRequest()));
        treelsListingResponseBO.setTreelsResponse(new TreelsResponse());
        Assert.assertNotNull(treelsResponseTransformer.convertTreelsResponse(treelsListingResponseBO, new ListingSearchRequestV2(), new SearchWrapperInputRequest()));
    }

    @Test
    public void convertTreelsResponseWithExploreMoreTest() {
        TreelsListingResponseBO treelsListingResponseBO = new TreelsListingResponseBO();
        treelsListingResponseBO.setTreelsResponse(new TreelsResponse());
        treelsListingResponseBO.getTreelsResponse().setListingProducts(new ArrayList<>());
        treelsListingResponseBO.getTreelsResponse().getListingProducts().add(new ListingProduct());
        treelsListingResponseBO.getTreelsResponse().getListingProducts().get(0).setHotels(new ArrayList<>());
        treelsListingResponseBO.getTreelsResponse().getListingProducts().get(0).getHotels().add(new SearchWrapperHotelEntityAbridged());
        treelsListingResponseBO.getTreelsResponse().setExploreMoreCta(new TreelsCTA());
        treelsListingResponseBO.getTreelsResponse().getExploreMoreCta().setText("test");
        treelsListingResponseBO.getTreelsResponse().getExploreMoreCta().setDeeplink("test");
        treelsListingResponseBO.getTreelsResponse().setNoMoreProduct(true);
        Mockito.when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("test");
        TreelsListingResponse treelsListingResponse = treelsResponseTransformer.convertTreelsResponse(treelsListingResponseBO, new ListingSearchRequestV2(), new SearchWrapperInputRequest());
        Assert.assertNotNull(treelsListingResponse);
        Assert.assertNotNull(treelsListingResponse.getExploreMoreData());
        Assert.assertTrue(treelsListingResponse.isAllTreelsShown());
        Assert.assertTrue(treelsListingResponse.isNoMoreProducts());
        Assert.assertEquals(treelsListingResponse.getExploreMoreData().getHeader(), "test");
        Assert.assertEquals(treelsListingResponse.getExploreMoreData().getTitle(), "test");
        Assert.assertEquals(treelsListingResponse.getExploreMoreData().getSubtitle(), "test");
        Assert.assertNotNull(treelsListingResponse.getExploreMoreData().getCta());
        Assert.assertEquals(treelsListingResponse.getExploreMoreData().getCta().getText(), "test");
        Assert.assertEquals(treelsListingResponse.getExploreMoreData().getCta().getDeeplink(), "test");
    }

    @Test
    public void convertTreelsResponseWithFlterRemoveTest() {
        TreelsListingResponseBO treelsListingResponseBO = new TreelsListingResponseBO();
        treelsListingResponseBO.setTreelsResponse(new TreelsResponse());
        treelsListingResponseBO.getTreelsResponse().setListingProducts(new ArrayList<>());
        treelsListingResponseBO.getTreelsResponse().getListingProducts().add(new ListingProduct());
        treelsListingResponseBO.getTreelsResponse().getListingProducts().get(0).setHotels(new ArrayList<>());
        treelsListingResponseBO.getTreelsResponse().getListingProducts().get(0).getHotels().add(new SearchWrapperHotelEntityAbridged());
        treelsListingResponseBO.getTreelsResponse().setNoMoreProduct(true);
        SearchWrapperInputRequest searchWrapperInputRequest = new SearchWrapperInputRequest();
        searchWrapperInputRequest.setAppliedFilterMap(new HashMap<>());
        searchWrapperInputRequest.getAppliedFilterMap().put(FilterGroup.LOCATION, new HashSet<>());
        Filter filter = new Filter();;
        filter.setFilterValue("test");
        filter.setFilterGroup(FilterGroup.LOCATION);
        searchWrapperInputRequest.getAppliedFilterMap().get(FilterGroup.LOCATION).add(filter);
        TreelsListingResponse treelsListingResponse = treelsResponseTransformer.convertTreelsResponse(treelsListingResponseBO, new ListingSearchRequestV2(), searchWrapperInputRequest);
        Assert.assertNotNull(treelsListingResponse);
        Assert.assertNotNull(treelsListingResponse.getFilterRemovedCriteria());
        Assert.assertEquals(treelsListingResponse.getFilterRemovedCriteria().size(), 1);
    }
}
