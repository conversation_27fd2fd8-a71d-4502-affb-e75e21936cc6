package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.response.DataList;
import com.mmt.hotels.clientgateway.response.InitApprovalResponse;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.android.InitiateApprovalResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.corporate.ManagerInfo;
import org.apache.commons.collections.MapUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;

import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.POLICY_MISMATCH;

@RunWith(MockitoJUnitRunner.class)
public class InitiateApprovalResponseTransformerTest {

    @InjectMocks
    InitiateApprovalResponseTransformerAndroid initiateApprovalResponseTransformerAndroid;
    @Mock
    PolyglotService polyglotService;
    @Mock
    Utility utility;
    @Test
    public void testForOSBAError() {

        CGServerResponse cgServerResponse = new CGServerResponse();
        cgServerResponse.setAdditionalProperty("responseCode", "OSBA");
        cgServerResponse.setAdditionalProperty("message", "testMessage");
        cgServerResponse.setAdditionalProperty("status", "failure");
        Mockito.when(polyglotService.getTranslatedData(POLICY_MISMATCH)).thenReturn("Policy Mismatch");
        InitApprovalResponse response = initiateApprovalResponseTransformerAndroid.processResponse(cgServerResponse);
        Assert.assertNotNull(response);
        Assert.assertEquals(response.getError().getMessage(),"testMessage");
        Assert.assertEquals(response.getError().getCode(),"OSBA");
        Assert.assertEquals(response.getError().getErrorTitle(),"Policy Mismatch");
    }
    @Test
    public void testForAnyError() {

        CGServerResponse cgServerResponse = new CGServerResponse();
        cgServerResponse.setAdditionalProperty("responseCode", "testError");
        cgServerResponse.setAdditionalProperty("message", "testMessage");
        cgServerResponse.setAdditionalProperty("status", "failure");
        InitApprovalResponse response = initiateApprovalResponseTransformerAndroid.processResponse(cgServerResponse);
        Assert.assertNotNull(response);
        Assert.assertEquals(response.getError().getMessage(),"testMessage");
        Assert.assertEquals(response.getError().getCode(),"testError");
        Assert.assertNull(response.getError().getErrorTitle());
    }

    @Test
    public void testForNoError(){
        CGServerResponse cgServerResponse = new CGServerResponse();
        List<ManagerInfo> approverList = new ArrayList<>();
        ManagerInfo approverMap = new ManagerInfo();
        approverMap.setName("test");
        approverMap.setEmailId("testEmail");
        approverList.add(approverMap);
        cgServerResponse.setAdditionalProperty("approverList", approverList);
        cgServerResponse.setAdditionalProperty("message", "testMessage");
        cgServerResponse.setAdditionalProperty("status", "success");
        InitApprovalResponse response = initiateApprovalResponseTransformerAndroid.processResponse(cgServerResponse);
        Assert.assertNotNull(response);
        Assert.assertNull(response.getError());
    }

    @Test
    public void testProcessResponseWithAdditionalPropertiesAndError() {
        CGServerResponse cgServerResponse = new CGServerResponse();
        cgServerResponse.setAdditionalProperty("responseCode", "OSBA");
        cgServerResponse.setAdditionalProperty("message", "testMessage");
        cgServerResponse.setAdditionalProperty("status", "error");
        LinkedHashMap<String, String> duplicateBookingDetails = new LinkedHashMap<>();
        duplicateBookingDetails.put("travellerName", "name");
        cgServerResponse.setAdditionalProperty("duplicateBookingDetails", duplicateBookingDetails);
        Mockito.when(utility.populateDateList(Mockito.any())).thenReturn(new LinkedHashSet<DataList>());
        InitApprovalResponse response = initiateApprovalResponseTransformerAndroid.processResponse(cgServerResponse);

        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getConsentData());
        Assert.assertNotNull(response.getConsentData().getDataList());
        Assert.assertEquals("success", response.getStatus());
    }
}
