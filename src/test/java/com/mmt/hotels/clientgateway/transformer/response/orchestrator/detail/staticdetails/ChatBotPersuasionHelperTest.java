package com.mmt.hotels.clientgateway.transformer.response.orchestrator.detail.staticdetails;

import com.gommt.hotels.orchestrator.detail.model.response.peithos.PersuasionTitle;
import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.*;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.ChatBotPersuasionHelper;
import com.mmt.hotels.model.persuasion.response.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class ChatBotPersuasionHelperTest {

    @InjectMocks
    private ChatBotPersuasionHelper chatBotPersuasionHelper;

    private HotelPersuasionData hotelPersuasionData;
    private PersuasionValue persuasionValue;
    private Style style;
    private Hover hover;
    private Button button;
    private TImer timer;

    @BeforeEach
    void setUp() {
        setupTestData();
    }

    private void setupTestData() {
        // Setup Style with gradients for comprehensive testing
        style = Style.builder()
                .textColor("#000000")
                .bgColor("#ffffff")
                .secondaryBgColor("#f0f0f0")
                .fontSize("14px")
                .borderColor("#cccccc")
                .borderSize(1)
                .iconWidth(24)
                .iconHeight(24)
                .styleClasses(Arrays.asList("class1", "class2"))
                .fontType("Arial")
                .bgUrl("http://example.com/bg.jpg")
                .cornerRadii("5px")
                .corners(Arrays.asList(5, 5, 5, 5))
                .horizontalSpace(10)
                .verticalSpace(10)
                .borderStyle(Arrays.asList("solid"))
                .maxLines(3)
                .separatorType("vertical")
                .maxCount(5)
                .imageUrl("http://example.com/image.jpg")
                .gravity("center")
                .titleColor("#333333")
                .title("Test Title")
                .imageWidth(100)
                .imageHeight(100)
                // Add gradients for comprehensive testing
                .bgGradient(com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.BgGradient.builder()
                        .angle("45")
                        .start("#ffffff")
                        .end("#000000")
                        .build())
                .textGradient(com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.BgGradient.builder()
                        .angle("90")
                        .start("#ff0000")
                        .end("#00ff00")
                        .build())
                .borderGradient(BorderGradient.builder()
                        .angle("180")
                        .start("#0000ff")
                        .end("#ffff00")
                        .build())
                .build();

        // Setup Hover
        hover = Hover.builder()
                .headingText("Test Heading")
                .style(style)
                .data("test data")
                .tooltipType("info")
                .ctaText("Click Here")
                .ctaColor("#007bff")
                .openHoverThreshold(500)
                .headingStyle(style)
                .build();

        // Setup Button
        button = Button.builder()
                .text("Button Text")
                .actionUrl("http://example.com/action")
                .hasAction(true)
                .actionType("navigate")
                .style(style)
                .hover(hover)
                .build();

        // Setup Timer
        timer = TImer.builder()
                .expiry(1735689599L) // Long timestamp for 2024-12-31T23:59:59Z
                .expiryFormat("yyyy-MM-dd'T'HH:mm:ss'Z'")
                .style(style)
                .hover(hover)
                .build();

        // Setup PersuasionTitle
        PersuasionTitle persuasionTitle = new PersuasionTitle();
        persuasionTitle.setText("Persuasion Title");
        persuasionTitle.setStyle(style);

        // Setup ExtraDetails
        com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.ExtraDetails extraDetails = 
                com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.ExtraDetails.builder()
                .iconUrl("http://example.com/extra-icon.jpg")
                .actionType("click")
                .title("Extra Title")
                .style(style)
                .build();

        // Setup PersuasionValue with child persuasions
        PersuasionValue childPersuasion = PersuasionValue.builder()
                .id("child-id")
                .text("Child Text")
                .persuasionType("CHILD_OFFER")
                .build();

        persuasionValue = PersuasionValue.builder()
                .id("test-id")
                .persuasionType("OFFER")
                .iconurl("http://example.com/icon.jpg")
                .imageUrl("http://example.com/image.jpg")
                .text("Test Text")
                .subtext("Test Subtext")
                .isHtml(true)
                .hasAction(true)
                .actionurl("http://example.com/action")
                .icontype("star")
                .button(button)
                .style(style)
                .subtextStyle(style)
                .hover(hover)
                .separator("|")
                .multiPersuasionCount(3)
                .multiPersuasionPriority(1)
                .actionType("navigate")
                .timer(timer)
                .persuasionKey("test-key")
                .outerLevelPersuasionTextKey("outer-key")
                .horizontal(true)
                .childPersuasions(Arrays.asList(childPersuasion))
                .persuasionTemplate("template1")
                .inclusions(Arrays.asList("inclusion1", "inclusion2"))
                .persuasionText("Persuasion Text")
                .persuasionTitle(persuasionTitle)
                .extraData(extraDetails)
                .build();

        // Setup HotelPersuasionData
        hotelPersuasionData = HotelPersuasionData.builder()
                .data(Arrays.asList(persuasionValue))
                .style(style)
                .hover(hover)
                .placeholder("test-placeholder")
                .placeholderType("banner")
                .template("test-template")
                .templateType("card")
                .topLevelText("Top Level Text")
                .separator("||")
                .extraDetails(extraDetails)
                .build();
    }

    // ===== mapChatBotPersuasions Tests =====

    @Test
    void testMapChatBotPersuasions_WithNullInput() {
        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(null);
        assertNull(result);
    }

    @Test
    void testMapChatBotPersuasions_WithEmptyMap() {
        Map<String, HotelPersuasionData> input = new HashMap<>();
        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(input);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testMapChatBotPersuasions_WithValidData() {
        Map<String, HotelPersuasionData> input = new HashMap<>();
        input.put("key1", hotelPersuasionData);

        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(input);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey("key1"));

        Persuasion persuasion = result.get("key1");
        assertNotNull(persuasion);
        assertEquals("test-placeholder", persuasion.getPlaceholder());
        assertEquals("test-template", persuasion.getTemplate());
        assertEquals("card", persuasion.getTemplateType());
        assertEquals("Top Level Text", persuasion.getTopLevelText());
        assertEquals("||", persuasion.getSeparator());
        assertNotNull(persuasion.getData());
        assertEquals(1, persuasion.getData().size());
        assertNotNull(persuasion.getStyle());
        assertNotNull(persuasion.getHover());
        assertNotNull(persuasion.getExtraDetails());
    }

    @Test
    void testMapChatBotPersuasions_WithNullHotelPersuasionData() {
        Map<String, HotelPersuasionData> input = new HashMap<>();
        input.put("key1", null);

        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(input);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertNotNull(result.get("key1")); // The method creates an empty Persuasion object
    }

    @Test
    void testMapChatBotPersuasions_WithMultipleEntries() {
        Map<String, HotelPersuasionData> input = new HashMap<>();
        input.put("key1", hotelPersuasionData);
        
        HotelPersuasionData secondData = HotelPersuasionData.builder()
                .placeholder("second-placeholder")
                .template("second-template")
                .build();
        input.put("key2", secondData);

        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(input);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey("key1"));
        assertTrue(result.containsKey("key2"));
        assertEquals("test-placeholder", result.get("key1").getPlaceholder());
        assertEquals("second-placeholder", result.get("key2").getPlaceholder());
    }

    @Test
    void testMapChatBotPersuasions_WithEmptyDataList() {
        HotelPersuasionData dataWithEmptyList = HotelPersuasionData.builder()
                .data(Arrays.asList())
                .placeholder("empty-data")
                .build();
        
        Map<String, HotelPersuasionData> input = new HashMap<>();
        input.put("key1", dataWithEmptyList);

        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(input);

        assertNotNull(result);
        assertEquals(1, result.size());
        Persuasion persuasion = result.get("key1");
        assertNotNull(persuasion);
        assertEquals("empty-data", persuasion.getPlaceholder());
        // Data list should be null or empty
        assertTrue(persuasion.getData() == null || persuasion.getData().isEmpty());
    }

    @Test
    void testMapChatBotPersuasions_WithNullDataList() {
        HotelPersuasionData dataWithNullList = HotelPersuasionData.builder()
                .data(null)
                .placeholder("null-data")
                .build();
        
        Map<String, HotelPersuasionData> input = new HashMap<>();
        input.put("key1", dataWithNullList);

        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(input);

        assertNotNull(result);
        assertEquals(1, result.size());
        Persuasion persuasion = result.get("key1");
        assertNotNull(persuasion);
        assertEquals("null-data", persuasion.getPlaceholder());
        assertNull(persuasion.getData());
    }

    // ===== Exception Handling Tests =====

    @Test
    void testMapChatBotPersuasions_ExceptionHandling() {
        // Create a problematic PersuasionValue that might cause issues
        PersuasionValue problematicValue = new PersuasionValue() {
            @Override
            public String getId() {
                throw new RuntimeException("Simulated exception");
            }
        };

        HotelPersuasionData problematicData = HotelPersuasionData.builder()
                .data(Arrays.asList(problematicValue))
                .placeholder("problematic")
                .build();

        Map<String, HotelPersuasionData> input = new HashMap<>();
        input.put("problematic-key", problematicData);

        // This should not throw exception but handle it gracefully
        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(input);

        assertNotNull(result);
        assertEquals(0, result.size()); // Exception during processing results in no entries
    }

    // ===== Private Method Tests via Reflection =====

    @Test
    void testMapStyleToStyleResponseBO_WithNullInput() throws Exception {
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod("mapStyleToStyleResponseBO", Style.class);
        method.setAccessible(true);
        
        StyleResponseBO result = (StyleResponseBO) method.invoke(chatBotPersuasionHelper, (Style) null);
        assertNull(result);
    }

    @Test
    void testMapStyleToStyleResponseBO_WithValidInput() throws Exception {
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod("mapStyleToStyleResponseBO", Style.class);
        method.setAccessible(true);
        
        StyleResponseBO result = (StyleResponseBO) method.invoke(chatBotPersuasionHelper, style);
        
        assertNotNull(result);
        assertEquals("#000000", result.getTextColor());
        assertEquals("#ffffff", result.getBgColor());
        assertEquals("#f0f0f0", result.getSecondaryBgColor());
        assertEquals("14px", result.getFontSize());
        assertEquals("#cccccc", result.getBorderColor());
        assertEquals(1, result.getBorderSize());
        assertEquals(24, result.getIconWidth());
        assertEquals(24, result.getIconHeight());
        assertEquals("Arial", result.getFontType());
        assertEquals("http://example.com/bg.jpg", result.getBgUrl());
        assertEquals("5px", result.getCornerRadii());
        assertEquals(10, result.getHorizontalSpace());
        assertEquals(10, result.getVerticalSpace());
        assertEquals(3, result.getMaxLines());
        assertEquals("vertical", result.getSeparatorType());
        assertEquals(5, result.getMaxCount());
        assertEquals("http://example.com/image.jpg", result.getImageUrl());
        assertEquals("center", result.getGravity());
        assertEquals("#333333", result.getTitleColor());
        assertEquals("Test Title", result.getTitle());
        assertEquals(100, result.getImageWidth());
        assertEquals(100, result.getImageHeight());
        assertNotNull(result.getStyleClasses());
        assertEquals(2, result.getStyleClasses().size());
        assertNotNull(result.getCorners());
        assertEquals(4, result.getCorners().size());
        assertNotNull(result.getBorderStyle());
        assertEquals(1, result.getBorderStyle().size());
        
        // Test gradients
        assertNotNull(result.getBgGradient());
        assertEquals("45", result.getBgGradient().getAngle());
        assertEquals("#ffffff", result.getBgGradient().getStart());
        assertEquals("#000000", result.getBgGradient().getEnd());
        
        assertNotNull(result.getTextGradient());
        assertEquals("90", result.getTextGradient().getAngle());
        assertEquals("#ff0000", result.getTextGradient().getStart());
        assertEquals("#00ff00", result.getTextGradient().getEnd());
    }

    @Test
    void testMapStyleToStyleResponseBO_WithMinimalData() throws Exception {
        Style minimalStyle = Style.builder()
                .textColor("#000000")
                .build();
        
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod("mapStyleToStyleResponseBO", Style.class);
        method.setAccessible(true);
        
        StyleResponseBO result = (StyleResponseBO) method.invoke(chatBotPersuasionHelper, minimalStyle);
        
        assertNotNull(result);
        assertEquals("#000000", result.getTextColor());
        assertNull(result.getBgColor());
        assertNull(result.getBgGradient());
        assertNull(result.getTextGradient());
    }

    @Test
    void testMapStyleToStyleResponseBO_WithBorderGradient() throws Exception {
        // Test the reflection-based borderGradient mapping
                 Style styleWithBorderGradient = Style.builder()
                 .textColor("#000000")
                 .borderGradient(BorderGradient.builder()
                         .angle("270")
                         .start("#ff00ff")
                         .end("#00ffff")
                         .build())
                 .build();
        
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod("mapStyleToStyleResponseBO", Style.class);
        method.setAccessible(true);
        
        StyleResponseBO result = (StyleResponseBO) method.invoke(chatBotPersuasionHelper, styleWithBorderGradient);
        
        assertNotNull(result);
        assertEquals("#000000", result.getTextColor());
        // The borderGradient should be set via reflection (this tests the reflection code path)
    }

    @Test
    void testMapHoverToHoverResponseBO_WithNullInput() throws Exception {
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod("mapHoverToHoverResponseBO", Hover.class);
        method.setAccessible(true);
        
        HoverResponseBO result = (HoverResponseBO) method.invoke(chatBotPersuasionHelper, (Hover) null);
        assertNull(result);
    }

    @Test
    void testMapHoverToHoverResponseBO_WithValidInput() throws Exception {
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod("mapHoverToHoverResponseBO", Hover.class);
        method.setAccessible(true);
        
        HoverResponseBO result = (HoverResponseBO) method.invoke(chatBotPersuasionHelper, hover);
        
        assertNotNull(result);
        assertEquals("Test Heading", result.getHeadingText());
        assertEquals("info", result.getTooltipType());
        assertEquals("Click Here", result.getCtaText());
        assertEquals("#007bff", result.getCtaColor());
        assertEquals(500, result.getOpenHoverThreshold());
        assertNotNull(result.getStyle());
        assertNotNull(result.getHeadingStyle());
        assertEquals("test data", result.getData());
    }

    @Test
    void testMapHoverToHoverResponseBO_WithNullStyles() throws Exception {
        Hover hoverWithNullStyles = Hover.builder()
                .headingText("Test Heading")
                .style(null)
                .headingStyle(null)
                .data("test data")
                .build();
        
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod("mapHoverToHoverResponseBO", Hover.class);
        method.setAccessible(true);
        
        HoverResponseBO result = (HoverResponseBO) method.invoke(chatBotPersuasionHelper, hoverWithNullStyles);
        
        assertNotNull(result);
        assertEquals("Test Heading", result.getHeadingText());
        assertNull(result.getStyle());
        assertNull(result.getHeadingStyle());
        assertEquals("test data", result.getData());
    }

    @Test
    void testMapExtraDetailsToExtraDetails_WithNullInput() throws Exception {
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod("mapExtraDetailsToExtraDetails",
                com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.ExtraDetails.class);
        method.setAccessible(true);
        
        com.mmt.hotels.model.persuasion.response.ExtraDetails result = 
                (com.mmt.hotels.model.persuasion.response.ExtraDetails) method.invoke(chatBotPersuasionHelper, (Object) null);
        assertNull(result);
    }

    @Test
    void testMapExtraDetailsToExtraDetails_WithValidInput() throws Exception {
        com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.ExtraDetails extraDetails = 
                com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.ExtraDetails.builder()
                .iconUrl("http://example.com/extra-icon.jpg")
                .actionType("click")
                .title("Extra Title")
                .style(style)
                .build();
        
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod("mapExtraDetailsToExtraDetails",
                com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.ExtraDetails.class);
        method.setAccessible(true);
        
        com.mmt.hotels.model.persuasion.response.ExtraDetails result = 
                (com.mmt.hotels.model.persuasion.response.ExtraDetails) method.invoke(chatBotPersuasionHelper, extraDetails);
        
        assertNotNull(result);
        assertEquals("http://example.com/extra-icon.jpg", result.getIconUrl());
        assertEquals("click", result.getActionType());
        assertEquals("Extra Title", result.getTitle());
        assertNotNull(result.getStyle());
    }

    @Test
    void testMapPersuasionValueToPersuasionData_WithNullInput() throws Exception {
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod("mapPersuasionValueToPersuasionData", PersuasionValue.class);
        method.setAccessible(true);
        
        com.mmt.hotels.model.persuasion.response.PersuasionData result = 
                (com.mmt.hotels.model.persuasion.response.PersuasionData) method.invoke(chatBotPersuasionHelper, (PersuasionValue) null);
        assertNull(result);
    }

    @Test
    void testMapPersuasionValueToPersuasionData_WithValidInput() throws Exception {
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod("mapPersuasionValueToPersuasionData", PersuasionValue.class);
        method.setAccessible(true);
        
        com.mmt.hotels.model.persuasion.response.PersuasionData result = 
                (com.mmt.hotels.model.persuasion.response.PersuasionData) method.invoke(chatBotPersuasionHelper, persuasionValue);
        
        assertNotNull(result);
        assertEquals("test-id", result.getId());
        assertEquals("Test Text", result.getText());
        assertEquals("Test Subtext", result.getSubtext());
        assertEquals("OFFER", result.getPersuasionType());
        assertEquals("http://example.com/icon.jpg", result.getIconurl());
        assertEquals("http://example.com/image.jpg", result.getImageUrl());
        assertEquals("http://example.com/action", result.getActionurl());
        assertEquals("star", result.getIcontype());
        assertEquals("|", result.getSeparator());
        assertEquals("navigate", result.getActionType());
        assertEquals("test-key", result.getPersuasionKey());
        assertEquals("outer-key", result.getOuterLevelPersuasionTextKey());
        assertEquals("template1", result.getPersuasionTemplate());
        assertEquals("Persuasion Text", result.getPersuasionText());
        
        assertTrue(result.isHasAction());
        assertTrue(result.isHtml());
        assertTrue(result.isHorizontal());
        
        assertEquals(3, result.getMultiPersuasionCount());
        assertEquals(1, result.getMultiPersuasionPriority());
        
        assertNotNull(result.getStyle());
        assertNotNull(result.getSubtextStyle());
        assertNotNull(result.getHover());
        assertNotNull(result.getButton());
        assertNotNull(result.getTimer());
        assertNotNull(result.getInclusions());
        assertEquals(2, result.getInclusions().size());
        assertNotNull(result.getPersuasionTitle());
        assertNotNull(result.getExtraData());
        assertNotNull(result.getChildPersuasions());
        assertEquals(1, result.getChildPersuasions().size());
    }

    @Test
    void testMapPersuasionValueToPersuasionData_WithNullOptionalFields() throws Exception {
        PersuasionValue minimalValue = PersuasionValue.builder()
                .id("minimal-id")
                .text("Minimal Text")
                .persuasionType("MINIMAL")
                .isHtml(false)
                .hasAction(false)
                .horizontal(false)
                .multiPersuasionCount(null)
                .multiPersuasionPriority(null)
                .style(null)
                .subtextStyle(null)
                .hover(null)
                .button(null)
                .timer(null)
                .inclusions(null)
                .persuasionTitle(null)
                .extraData(null)
                .childPersuasions(null)
                .build();
        
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod("mapPersuasionValueToPersuasionData", PersuasionValue.class);
        method.setAccessible(true);
        
        com.mmt.hotels.model.persuasion.response.PersuasionData result = 
                (com.mmt.hotels.model.persuasion.response.PersuasionData) method.invoke(chatBotPersuasionHelper, minimalValue);
        
        assertNotNull(result);
        assertEquals("minimal-id", result.getId());
        assertEquals("Minimal Text", result.getText());
        assertEquals("MINIMAL", result.getPersuasionType());
        assertFalse(result.isHasAction());
        assertFalse(result.isHtml());
        assertFalse(result.isHorizontal());
        assertNull(result.getStyle());
        assertNull(result.getSubtextStyle());
        assertNull(result.getHover());
        assertNull(result.getButton());
        assertNull(result.getTimer());
        assertNull(result.getInclusions());
        assertNull(result.getPersuasionTitle());
        assertNull(result.getExtraData());
        assertNull(result.getChildPersuasions());
    }

    @Test
    void testMapPersuasionValueToPersuasionData_WithChildPersuasions() throws Exception {
        PersuasionValue child1 = PersuasionValue.builder()
                .id("child1")
                .text("Child 1")
                .build();
        
        PersuasionValue child2 = PersuasionValue.builder()
                .id("child2")
                .text("Child 2")
                .build();
        
        PersuasionValue parentValue = PersuasionValue.builder()
                .id("parent")
                .text("Parent")
                .childPersuasions(Arrays.asList(child1, child2))
                .build();
        
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod("mapPersuasionValueToPersuasionData", PersuasionValue.class);
        method.setAccessible(true);
        
        com.mmt.hotels.model.persuasion.response.PersuasionData result = 
                (com.mmt.hotels.model.persuasion.response.PersuasionData) method.invoke(chatBotPersuasionHelper, parentValue);
        
        assertNotNull(result);
        assertEquals("parent", result.getId());
        assertNotNull(result.getChildPersuasions());
        assertEquals(2, result.getChildPersuasions().size());
        assertEquals("child1", result.getChildPersuasions().get(0).getId());
        assertEquals("child2", result.getChildPersuasions().get(1).getId());
    }

    @Test
    void testMapPersuasionValueToPersuasionData_WithEmptyChildPersuasions() throws Exception {
        PersuasionValue valueWithEmptyChildren = PersuasionValue.builder()
                .id("parent")
                .text("Parent")
                .childPersuasions(Arrays.asList())
                .build();
        
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod("mapPersuasionValueToPersuasionData", PersuasionValue.class);
        method.setAccessible(true);
        
        com.mmt.hotels.model.persuasion.response.PersuasionData result = 
                (com.mmt.hotels.model.persuasion.response.PersuasionData) method.invoke(chatBotPersuasionHelper, valueWithEmptyChildren);
        
        assertNotNull(result);
        assertEquals("parent", result.getId());
        // Empty list should not create child persuasions
        assertNull(result.getChildPersuasions());
    }

    @Test
    void testMapPersuasionValueToPersuasionData_WithNullChildInList() throws Exception {
        PersuasionValue validChild = PersuasionValue.builder()
                .id("valid-child")
                .text("Valid Child")
                .build();
        
        PersuasionValue valueWithNullChild = PersuasionValue.builder()
                .id("parent")
                .text("Parent")
                .childPersuasions(Arrays.asList(validChild, null))
                .build();
        
        Method method = ChatBotPersuasionHelper.class.getDeclaredMethod("mapPersuasionValueToPersuasionData", PersuasionValue.class);
        method.setAccessible(true);
        
        com.mmt.hotels.model.persuasion.response.PersuasionData result = 
                (com.mmt.hotels.model.persuasion.response.PersuasionData) method.invoke(chatBotPersuasionHelper, valueWithNullChild);
        
        assertNotNull(result);
        assertEquals("parent", result.getId());
        assertNotNull(result.getChildPersuasions());
        assertEquals(1, result.getChildPersuasions().size()); // Null child should be filtered out
        assertEquals("valid-child", result.getChildPersuasions().get(0).getId());
    }

    // ===== Integration and Edge Case Tests =====

    @Test
    void testMapChatBotPersuasions_PerformanceWithLargeDataset() {
        Map<String, HotelPersuasionData> largeInput = new HashMap<>();
        for (int i = 0; i < 100; i++) {
            HotelPersuasionData data = HotelPersuasionData.builder()
                    .placeholder("placeholder-" + i)
                    .template("template-" + i)
                    .data(Arrays.asList(persuasionValue))
                    .build();
            largeInput.put("key-" + i, data);
        }

        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(largeInput);

        assertNotNull(result);
        assertEquals(100, result.size());
        for (int i = 0; i < 100; i++) {
            assertTrue(result.containsKey("key-" + i));
            assertEquals("placeholder-" + i, result.get("key-" + i).getPlaceholder());
        }
    }

    @Test
    void testMapChatBotPersuasions_WithComplexNestedStructure() {
        // Create a complex nested structure with multiple levels
        PersuasionValue grandChild = PersuasionValue.builder()
                .id("grandchild")
                .text("Grand Child")
                .persuasionType("GRANDCHILD_OFFER")
                .build();

        PersuasionValue child = PersuasionValue.builder()
                .id("child")
                .text("Child")
                .persuasionType("CHILD_OFFER")
                .childPersuasions(Arrays.asList(grandChild))
                .style(style)
                .hover(hover)
                .button(button)
                .timer(timer)
                .build();

        PersuasionValue parent = PersuasionValue.builder()
                .id("parent")
                .text("Parent")
                .persuasionType("PARENT_OFFER")
                .childPersuasions(Arrays.asList(child))
                .style(style)
                .hover(hover)
                .build();

        HotelPersuasionData complexData = HotelPersuasionData.builder()
                .data(Arrays.asList(parent))
                .style(style)
                .hover(hover)
                .placeholder("complex-placeholder")
                .build();

        Map<String, HotelPersuasionData> input = new HashMap<>();
        input.put("complex", complexData);

        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(input);

        assertNotNull(result);
        assertEquals(1, result.size());
        Persuasion persuasion = result.get("complex");
        assertNotNull(persuasion);
        assertEquals("complex-placeholder", persuasion.getPlaceholder());
        
        // Verify nested structure
        assertNotNull(persuasion.getData());
        assertEquals(1, persuasion.getData().size());
        com.mmt.hotels.model.persuasion.response.PersuasionData parentData = persuasion.getData().get(0);
        assertEquals("parent", parentData.getId());
        
        assertNotNull(parentData.getChildPersuasions());
        assertEquals(1, parentData.getChildPersuasions().size());
        com.mmt.hotels.model.persuasion.response.PersuasionData childData = parentData.getChildPersuasions().get(0);
        assertEquals("child", childData.getId());
        
        assertNotNull(childData.getChildPersuasions());
        assertEquals(1, childData.getChildPersuasions().size());
        com.mmt.hotels.model.persuasion.response.PersuasionData grandChildData = childData.getChildPersuasions().get(0);
        assertEquals("grandchild", grandChildData.getId());
    }

    @Test
    void testChatBotPersuasionMapper_IsSpringComponent() {
        assertTrue(chatBotPersuasionHelper.getClass().isAnnotationPresent(org.springframework.stereotype.Component.class));
    }

    @Test
    void testChatBotPersuasionMapper_CanBeInstantiated() {
        ChatBotPersuasionHelper newInstance = new ChatBotPersuasionHelper();
        assertNotNull(newInstance);
    }

    @Test
    void testChatBotPersuasionMapper_InjectedInstance() {
        assertNotNull(chatBotPersuasionHelper);
    }

    @Test
    void testMapChatBotPersuasions_BoundaryValues() {
        // Test with extreme values
        Style extremeStyle = Style.builder()
                .borderSize(Integer.MAX_VALUE)
                .iconWidth(Integer.MAX_VALUE)
                .iconHeight(Integer.MAX_VALUE)
                .horizontalSpace(Integer.MAX_VALUE)
                .verticalSpace(Integer.MAX_VALUE)
                .maxLines(Integer.MAX_VALUE)
                .maxCount(Integer.MAX_VALUE)
                .imageWidth(Integer.MAX_VALUE)
                .imageHeight(Integer.MAX_VALUE)
                .textColor("")
                .bgColor("")
                .build();

        PersuasionValue extremeValue = PersuasionValue.builder()
                .id("")
                .text("")
                .multiPersuasionCount(Integer.MAX_VALUE)
                .multiPersuasionPriority(Integer.MAX_VALUE)
                .style(extremeStyle)
                .build();

        HotelPersuasionData extremeData = HotelPersuasionData.builder()
                .data(Arrays.asList(extremeValue))
                .placeholder("")
                .template("")
                .build();

        Map<String, HotelPersuasionData> input = new HashMap<>();
        input.put("extreme", extremeData);

        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(input);

        assertNotNull(result);
        assertEquals(1, result.size());
        Persuasion persuasion = result.get("extreme");
        assertNotNull(persuasion);
        assertEquals("", persuasion.getPlaceholder());
        assertEquals("", persuasion.getTemplate());
        
        assertNotNull(persuasion.getData());
        assertEquals(1, persuasion.getData().size());
        com.mmt.hotels.model.persuasion.response.PersuasionData data = persuasion.getData().get(0);
        assertEquals("", data.getId());
        assertEquals("", data.getText());
        assertEquals(Integer.MAX_VALUE, data.getMultiPersuasionCount());
        assertEquals(Integer.MAX_VALUE, data.getMultiPersuasionPriority());
    }

    @Test
    void testMapChatBotPersuasions_WithDataContainingNullValues() {
        // Test with data list containing null values
        PersuasionValue validValue = PersuasionValue.builder()
                .id("valid")
                .text("Valid")
                .build();

        HotelPersuasionData dataWithNulls = HotelPersuasionData.builder()
                .data(Arrays.asList(validValue, null))
                .placeholder("mixed-data")
                .build();

        Map<String, HotelPersuasionData> input = new HashMap<>();
        input.put("mixed", dataWithNulls);

        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(input);

        assertNotNull(result);
        assertEquals(1, result.size());
        Persuasion persuasion = result.get("mixed");
        assertNotNull(persuasion);
        assertEquals("mixed-data", persuasion.getPlaceholder());
        
        // Should only contain the valid value, null should be filtered out
        assertNotNull(persuasion.getData());
        assertEquals(1, persuasion.getData().size());
        assertEquals("valid", persuasion.getData().get(0).getId());
    }

    @Test
    void testMapChatBotPersuasions_WithAllNullFields() {
        // Test with HotelPersuasionData having all null fields
        HotelPersuasionData allNullData = HotelPersuasionData.builder()
                .data(null)
                .style(null)
                .hover(null)
                .placeholder(null)
                .placeholderType(null)
                .template(null)
                .templateType(null)
                .topLevelText(null)
                .separator(null)
                .extraDetails(null)
                .build();

        Map<String, HotelPersuasionData> input = new HashMap<>();
        input.put("all-null", allNullData);

        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(input);

        assertNotNull(result);
        assertEquals(1, result.size());
        Persuasion persuasion = result.get("all-null");
        assertNotNull(persuasion);
        assertNull(persuasion.getPlaceholder());
        assertNull(persuasion.getTemplate());
        assertNull(persuasion.getTemplateType());
        assertNull(persuasion.getTopLevelText());
        assertNull(persuasion.getSeparator());
        assertNull(persuasion.getData());
        assertNull(persuasion.getStyle());
        assertNull(persuasion.getHover());
        assertNull(persuasion.getExtraDetails());
    }

    @Test
    void testMapChatBotPersuasions_WithSpecialCharacters() {
        // Test with special characters and Unicode
        PersuasionValue specialValue = PersuasionValue.builder()
                .id("special-🎯-id")
                .text("Special Text with émojis 🚀 and ñoñó characters")
                .subtext("Subtext with <script>alert('xss')</script> and & entities")
                .persuasionType("SPECIAL_OFFER")
                .separator("⭐")
                .build();

        HotelPersuasionData specialData = HotelPersuasionData.builder()
                .data(Arrays.asList(specialValue))
                .placeholder("Special placeholder with 中文 characters")
                .template("Template with русский text")
                .separator("🔥")
                .build();

        Map<String, HotelPersuasionData> input = new HashMap<>();
        input.put("special-key-🌟", specialData);

        Map<String, Persuasion> result = chatBotPersuasionHelper.mapChatBotPersuasions(input);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey("special-key-🌟"));
        
        Persuasion persuasion = result.get("special-key-🌟");
        assertNotNull(persuasion);
        assertEquals("Special placeholder with 中文 characters", persuasion.getPlaceholder());
        assertEquals("Template with русский text", persuasion.getTemplate());
        assertEquals("🔥", persuasion.getSeparator());
        
        assertNotNull(persuasion.getData());
        assertEquals(1, persuasion.getData().size());
        com.mmt.hotels.model.persuasion.response.PersuasionData data = persuasion.getData().get(0);
        assertEquals("special-🎯-id", data.getId());
        assertEquals("Special Text with émojis 🚀 and ñoñó characters", data.getText());
        assertEquals("Subtext with <script>alert('xss')</script> and & entities", data.getSubtext());
        assertEquals("⭐", data.getSeparator());
    }

    @Test
    void testMapChatBotPersuasions_LoggerCoverage() {
        // This test ensures the logger is properly initialized and accessible
        assertNotNull(chatBotPersuasionHelper);
        
        // Test that the class has a logger field
        try {
            java.lang.reflect.Field loggerField = ChatBotPersuasionHelper.class.getDeclaredField("LOGGER");
            loggerField.setAccessible(true);
            Object logger = loggerField.get(chatBotPersuasionHelper);
            assertNotNull(logger);
        } catch (Exception e) {
            fail("Logger field should be accessible");
        }
    }
} 