package com.mmt.hotels.clientgateway.transformer.response.orchestrator.detail.staticdetails;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.Staff;
import com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffData;
import com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffInfo;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailsResponseTransformerDesktop;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysTooltip;
import com.mmt.propertymanager.config.PropertyManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.LUXE_ICON_DESKTOP;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OrchStaticDetailsResponseTransformerDesktopTest {

    @InjectMocks
    private OrchStaticDetailsResponseTransformerDesktop transformer;

    @Mock
    private CommonConfigConsul commonConfigConsul;

    @Mock
    private PropertyManager propertyManager;

    @Mock
    private PolyglotHelper polyglotHelper;

    @Mock
    private CommonConfig commonConfig;

    private ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(transformer, "consulFlag", false);
        ReflectionTestUtils.setField(transformer, "valueStatysTitleIcon", "https://test.value.stays.title.icon");
        ReflectionTestUtils.setField(transformer, "valueStaysIconNewDetailPageDt", "https://test.value.stays.icon.new.detail.page");
        ReflectionTestUtils.setField(transformer, "valueStatysTitleIconGcc", "https://test.value.stays.title.icon.gcc");
        ReflectionTestUtils.setField(transformer, "starHostIconDesktop", "https://test.starhost.icon.desktop");
        ReflectionTestUtils.setField(transformer, "mmtValueStaysCategoryIconUrlDesktop", "https://test.mmt.value.stays.category.icon.url.desktop");
    }

    @Test
    void testInitWithConsulEnabled() throws Exception {
        // Given
        ReflectionTestUtils.setField(transformer, "consulFlag", true);
        ValueStaysTooltip mockTooltipDom = new ValueStaysTooltip();
        ValueStaysTooltip mockTooltipIntl = new ValueStaysTooltip();
        when(commonConfigConsul.getMmtValueStaysTooltipDom()).thenReturn(mockTooltipDom);
        when(commonConfigConsul.getMmtValueStaysTooltipIntl()).thenReturn(mockTooltipIntl);

        // When
        transformer.init();

        // Then
        verify(commonConfigConsul).getMmtValueStaysTooltipDom();
        verify(commonConfigConsul).getMmtValueStaysTooltipIntl();
        ValueStaysTooltip tooltipDom = (ValueStaysTooltip) ReflectionTestUtils.getField(transformer, "valueStaysTooltipDom");
        ValueStaysTooltip tooltipIntl = (ValueStaysTooltip) ReflectionTestUtils.getField(transformer, "valueStaysTooltipIntl");
        assertEquals(mockTooltipDom, tooltipDom);
        assertEquals(mockTooltipIntl, tooltipIntl);
    }

    @Test
    void testInitWithConsulDisabled() throws Exception {
        // Given
        ReflectionTestUtils.setField(transformer, "consulFlag", false);
        ValueStaysTooltip mockTooltipDom = new ValueStaysTooltip();
        ValueStaysTooltip mockTooltipIntl = new ValueStaysTooltip();
        when(propertyManager.getProperty("commonConfig", CommonConfig.class)).thenReturn(commonConfig);
        when(commonConfig.mmtValueStaysTooltipDom()).thenReturn(mockTooltipDom);
        when(commonConfig.mmtValueStaysTooltipIntl()).thenReturn(mockTooltipIntl);

        // When
        transformer.init();

        // Then
        verify(propertyManager).getProperty("commonConfig", CommonConfig.class);
        verify(commonConfig).mmtValueStaysTooltipDom();
        verify(commonConfig).mmtValueStaysTooltipIntl();
    }

    @Test
    void testInitWithException() throws Exception {
        // Given
        ReflectionTestUtils.setField(transformer, "consulFlag", false);
        when(propertyManager.getProperty("commonConfig", CommonConfig.class)).thenThrow(new RuntimeException("Test exception"));

        // When & Then
        assertDoesNotThrow(() -> transformer.init());
        verify(propertyManager).getProperty("commonConfig", CommonConfig.class);
    }

    @Test
    void testBuildCardTitleMap() {
        // When
        Map<String, String> result = transformer.buildCardTitleMap();

        // Then
        assertNull(result);
    }

    @Test
    void testAddTitleDataWithNullHotelResult() {
        // When & Then
        assertDoesNotThrow(() -> transformer.addTitleData(null, "IN", false));
    }

    @Test
    void testAddTitleDataWithIndiaCountryCode() {
        // Given
        HotelResult hotelResult = new HotelResult();
        ValueStaysTooltip mockTooltipDom = new ValueStaysTooltip();
        ReflectionTestUtils.setField(transformer, "valueStaysTooltipDom", mockTooltipDom);

        // When
        transformer.addTitleData(hotelResult, "IN", false);

        // Then
        assertNotNull(hotelResult.getTitleData());
        assertEquals("https://test.value.stays.title.icon", hotelResult.getTitleData().getTitleIcon());
        verify(polyglotHelper).translateValueStaysTooltip(any(ValueStaysTooltip.class));
    }

    @Test
    void testAddTitleDataWithBlankCountryCode() {
        // Given
        HotelResult hotelResult = new HotelResult();
        ValueStaysTooltip mockTooltipDom = new ValueStaysTooltip();
        ReflectionTestUtils.setField(transformer, "valueStaysTooltipDom", mockTooltipDom);

        // When
        transformer.addTitleData(hotelResult, "", false);

        // Then
        assertNotNull(hotelResult.getTitleData());
        assertEquals("https://test.value.stays.title.icon", hotelResult.getTitleData().getTitleIcon());
        verify(polyglotHelper).translateValueStaysTooltip(any(ValueStaysTooltip.class));
    }

    @Test
    void testAddTitleDataWithNullCountryCode() {
        // Given
        HotelResult hotelResult = new HotelResult();
        ValueStaysTooltip mockTooltipDom = new ValueStaysTooltip();
        ReflectionTestUtils.setField(transformer, "valueStaysTooltipDom", mockTooltipDom);

        // When
        transformer.addTitleData(hotelResult, null, false);

        // Then
        assertNotNull(hotelResult.getTitleData());
        assertEquals("https://test.value.stays.title.icon", hotelResult.getTitleData().getTitleIcon());
        verify(polyglotHelper).translateValueStaysTooltip(any(ValueStaysTooltip.class));
    }

    @Test
    void testAddTitleDataWithInternationalCountryCode() {
        // Given
        HotelResult hotelResult = new HotelResult();
        ValueStaysTooltip mockTooltipIntl = new ValueStaysTooltip();
        ReflectionTestUtils.setField(transformer, "valueStaysTooltipIntl", mockTooltipIntl);

        // When
        transformer.addTitleData(hotelResult, "US", false);

        // Then
        assertNotNull(hotelResult.getTitleData());
        assertEquals("https://test.value.stays.title.icon", hotelResult.getTitleData().getTitleIcon());
        verify(polyglotHelper).translateValueStaysTooltip(any(ValueStaysTooltip.class));
    }

    @Test
    void testAddTitleDataWithNewDetailPageDesktop() {
        // Given
        HotelResult hotelResult = new HotelResult();
        ValueStaysTooltip mockTooltipDom = new ValueStaysTooltip();
        ReflectionTestUtils.setField(transformer, "valueStaysTooltipDom", mockTooltipDom);

        // When
        transformer.addTitleData(hotelResult, "IN", true);

        // Then
        assertNotNull(hotelResult.getTitleData());
        assertNotNull(hotelResult.getCategoryTag());
        assertEquals("https://test.value.stays.icon.new.detail.page", hotelResult.getCategoryTag().getIconUrl());
        assertEquals(29, hotelResult.getCategoryTag().getStyle().getIconHeight());
        assertEquals(134, hotelResult.getCategoryTag().getStyle().getIconWidth());
        verify(polyglotHelper).translateValueStaysTooltip(any(ValueStaysTooltip.class));
    }

    @Test
    void testGetLuxeIcon() {
        // When
        String result = transformer.getLuxeIcon();

        // Then
        assertEquals(LUXE_ICON_DESKTOP, result);
    }

    @Test
    void testConvertStaffInfoWithStarHost() {
        // Given
        StaffInfo staffInfo = new StaffInfo();
        staffInfo.setIsStarHost(true);
        Staff staff = new Staff();
        StaffData staffData = new StaffData();
        staffData.setAbout("abc");
        staff.setHeader("abc");
        staff.setData(Arrays.asList(staffData));
        staffInfo.setCook(staff);


        // When
        com.mmt.hotels.model.response.staticdata.StaffInfo result = transformer.convertStaffInfo(staffInfo);

        // Then
        assertNotNull(result);
        assertEquals("https://test.starhost.icon.desktop", staffInfo.getStarHostIconUrl());
    }

    @Test
    void testConvertStaffInfoWithoutStarHost() {
        // Given
        StaffInfo staffInfo = new StaffInfo();
        staffInfo.setIsStarHost(false);
        Staff staff = new Staff();
        StaffData staffData = new StaffData();
        staffData.setAbout("abc");
        staff.setHeader("abc");
        staff.setData(Arrays.asList(staffData));
        staffInfo.setCook(staff);

        // When
        com.mmt.hotels.model.response.staticdata.StaffInfo result = transformer.convertStaffInfo(staffInfo);

        // Then
        assertNotNull(result);
        assertNull(staffInfo.getStarHostIconUrl());
    }

    @Test
    void testConvertStaffInfoWithNullStaffInfo() {
        // When
        com.mmt.hotels.model.response.staticdata.StaffInfo result = transformer.convertStaffInfo(null);

        // Then
        assertNull(result);
    }

    @Test
    void testBuildWeaverResponse() throws Exception {
        // Given
        JsonNode weaverResponse = objectMapper.createObjectNode();

        // When
        JsonNode result = transformer.buildWeaverResponse(weaverResponse);

        // Then
        assertNotNull(result);
        assertEquals(weaverResponse, result);
    }

    @Test
    void testBuildWeaverResponseWithNull() {
        // When
        JsonNode result = transformer.buildWeaverResponse(null);

        // Then
        assertNull(result);
    }
} 