package com.mmt.hotels.clientgateway.transformer.response.android;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.Persuasions;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.searchHotels.*;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.searchwrapper.ListingPagePersonalizationResponsBO;
import com.mmt.hotels.model.response.searchwrapper.PersonalizedResponse;
import com.mmt.hotels.model.response.searchwrapper.QuickBookInfo;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.pojo.listing.personalization.BGLinearGradient;
import com.mmt.model.LocusData;
import com.mmt.propertymanager.config.PropertyManager;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SearchHotelsResponseTransformerAndroidTest {

    @InjectMocks
    private SearchHotelsResponseTransformerAndroid transformer;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    private com.mmt.hotels.clientgateway.util.Utility utility;

    @Mock
    private com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer commonResponseTransformer;

    @Mock
    private com.mmt.hotels.clientgateway.util.CrossSellUtil crossSellUtil;

    @Mock
    private CommonConfigConsul commonConfigConsul;

    @Mock
    private PropertyManager propManager;

    @Mock
    private PersuasionUtil persuasionUtil;

    @Mock
    private MetricAspect metricAspect;

    @Before
    public void setup() {
        ReflectionTestUtils.setField(transformer, "consulFlag", false);
        ReflectionTestUtils.setField(transformer, "myBizBottomSheetImage", "test-image-url");
        ReflectionTestUtils.setField(transformer, "highRatedUrl", "high-rated-url");
        ReflectionTestUtils.setField(transformer, "gstInvoiceUrl", "gst-invoice-url");
        ReflectionTestUtils.setField(transformer, "bpgUrl", "bpg-url");
        ReflectionTestUtils.setField(transformer, "pixelTrackingLocations", new HashSet<>(Arrays.asList("LOC1")));
    }

   /* @Test
    public void testConvertSearchHotelsResponse() {
        // Arrange
        ListingPagePersonalizationResponsBO listingPageResponseBO = new ListingPagePersonalizationResponsBO();
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();

        // Act
        SearchHotelsResponse response = transformer.convertSearchHotelsResponse(
            listingPageResponseBO, searchHotelsRequest, commonModifierResponse);

        // Assert
        assertNotNull(response);
        assertNull(response.getExpData());
    }
*/
    @Test
    public void testBuildQuickBookCard() {
        // Arrange
        QuickBookInfo quickBookInfo = new QuickBookInfo();
        quickBookInfo.setTitleWithPrice("Test Title");
        quickBookInfo.setRoomPersuasion("Test Room");
        quickBookInfo.setRoomPersuasionWithSize("Test Room Size");
        quickBookInfo.setShowQuickBookCard(true);

        when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated Text");

        // Act
        HotelCard card = transformer.buildQuickBookCard(quickBookInfo);

        // Assert
        assertNotNull(card);
        assertEquals("Test Title", card.getHeading());
        assertEquals("Test Room", card.getSubHeading());
        assertEquals("Test Room Size", card.getRoomSubHeading());
        assertTrue(card.getShowCard());
        assertEquals("Translated Text", card.getCta());
        assertNotNull(card.getBgLinearGradient());
    }

    @Test
    public void testAddLocationPersuasionToHotelPersuasions() {
        // Arrange
        Hotel hotel = new Hotel();
        List<String> locationPersuasion = Arrays.asList("Location 1", "Location 2");
        LinkedHashSet<String> facilities = new LinkedHashSet<>(Arrays.asList("Facility 1", "Facility 2"));
        ListingSearchRequest searchHotelsRequest = new ListingSearchRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource("SHORTSTAYS");
        searchHotelsRequest.setRequestDetails(requestDetails);

        //when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated Text");

        // Act
        transformer.addLocationPersuasionToHotelPersuasions(
            hotel, locationPersuasion, facilities, searchHotelsRequest, true, false, null, null, null, null, false);

        // Assert
        assertNotNull(hotel.getHotelPersuasions());
        assertTrue(hotel.getHotelPersuasions() instanceof Map);
    }

    @Test
    public void testBuildBottomSheet() {
        // Arrange
        PersonalizedResponse<SearchWrapperHotelEntity> perResponse = new PersonalizedResponse<>();
        when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated Text");

        // Act
        BottomSheet bottomSheet = transformer.buildBottomSheet(perResponse);

        // Assert
        assertNotNull(bottomSheet);
        assertEquals("Translated Text", bottomSheet.getHeading());
        assertEquals("Translated Text", bottomSheet.getSubHeading());
        assertEquals("test-image-url", bottomSheet.getImgUrl());
        assertEquals("Translated Text", bottomSheet.getCta());
        assertNotNull(bottomSheet.getSectionFeatures());
        assertEquals(3, bottomSheet.getSectionFeatures().size());
    }

    @Test
    public void testConvertSearchHotelsResponse_basicFieldsAndPixel() {
        // Arrange
        ListingPagePersonalizationResponsBO listing = new ListingPagePersonalizationResponsBO();
        listing.setCurrency("INR");
        listing.setTotalHotelCounts(123);
        listing.setHotelCountInCity(120);
        listing.setLastFetchedHotelCategory(null);
        listing.setCityCode("BOM");
        listing.setCityName("Mumbai");
        LocusData locus = new LocusData();
        locus.setLocusId("LOC1");
        locus.setLocusName("Andheri");
        listing.setLocusData(locus);

        SearchHotelsRequest request = new SearchHotelsRequest();
        request.setRequestDetails(new RequestDetails());

        CommonModifierResponse cm = new CommonModifierResponse();

        // Act
        SearchHotelsResponse response = transformer.convertSearchHotelsResponse(listing, request, cm);

        // Assert
        assertNotNull(response);
        assertEquals("INR", response.getCurrency());
        assertEquals(Integer.valueOf(123), response.getHotelCount());
        assertEquals(Integer.valueOf(120), response.getHotelCountInCity());
        assertNotNull(response.getCityLocationDetail());
    }

    @Test
    public void testConvertSearchHotelsResponse_filterRemovalSetsFilterRemovedCriteria() {
        // Arrange: lastFetchedHotelCategory set to FILTER_REMOVAL and request has STAR_RATING filter
        ListingPagePersonalizationResponsBO listing = new ListingPagePersonalizationResponsBO();
        listing.setCurrency("INR");
        listing.setTotalHotelCounts(10);
        listing.setHotelCountInCity(10);
        listing.setLastFetchedHotelCategory("FILTER_REMOVAL");

        SearchHotelsRequest request = new SearchHotelsRequest();
        com.mmt.hotels.clientgateway.request.Filter f = new com.mmt.hotels.clientgateway.request.Filter();
        f.setFilterGroup(com.mmt.hotels.clientgateway.response.filter.FilterGroup.STAR_RATING);
        f.setFilterValue("4+");
        request.setFilterCriteria(Collections.singletonList(f));

        CommonModifierResponse cm = new CommonModifierResponse();

        when(utility.isB2CFunnel()).thenReturn(true);

        // Act
        SearchHotelsResponse response = transformer.convertSearchHotelsResponse(listing, request, cm);

        // Assert
        assertNotNull(response);
    }

    @Test
    public void testAddPersuasionsForHiddenGemCard() {
        // Arrange
        SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
        PersuasionObject hiddenGemPersuasion = new PersuasionObject();
        hiddenGemPersuasion.setData(new ArrayList<>());
        
        when(persuasionUtil.buildHiddenGemPersuasion(any(), anyString())).thenReturn(hiddenGemPersuasion);
        when(persuasionUtil.buildHiddenGemIconPersuasion(any(), anyString())).thenReturn(hiddenGemPersuasion);
        when(persuasionUtil.buildHomeStaysTitlePersuasion(any(), anyString())).thenReturn(hiddenGemPersuasion);

        // Act
        transformer.addPersuasionsForHiddenGemCard(hotelEntity);

        // Assert
        assertNotNull(hotelEntity.getHotelPersuasions());
        verify(persuasionUtil).buildHiddenGemPersuasion(any(), anyString());
        verify(persuasionUtil).buildHiddenGemIconPersuasion(any(), anyString());
        verify(persuasionUtil).buildHomeStaysTitlePersuasion(any(), anyString());
    }

 /*   @Test
    public void testAddSpecialFarePersuasion() {
        // Arrange
        SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
        hotelEntity.setHotelPersuasions(new HashMap<>());

        // Act
        transformer.addSpecialFarePersuasion(hotelEntity);

        // Assert
        assertNotNull(hotelEntity.getHotelPersuasions());
    }

    @Test
    public void testAddBookingConfirmationPersuasion() {
        // Arrange
        SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
        hotelEntity.setHotelPersuasions(new HashMap<>());

        // Act
        transformer.addBookingConfirmationPersuasion(hotelEntity);

        // Assert
        assertNotNull(hotelEntity.getHotelPersuasions());
    }*/

    @Test
    public void testBuildBGColor() {
        // Act
        String bgColor = transformer.buildBGColor("section", "orientation", "cardType");

        // Assert
        assertNull(bgColor);
    }
} 