package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.EMIDetail;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.*;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.FetchLocationsRequestBody;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.UserLocation;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.util.*;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class AvailRoomsRequestTransformerTest {

    @InjectMocks
    AvailRoomsRequestTransformer availRoomsRequestTransformer;
    
    @Mock
    CommonHelper commonHelper;

    @Spy
    private Utility utility;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    MetricAspect metricAspect;

    @Test
    public void testConvertAvailRoomsRequest(){
        Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(Mockito.any(),Mockito.any(),Mockito.anyLong());

        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setDeviceDetails(new DeviceDetails());
        availRoomsRequest.setExpData("abc");


        availRoomsRequest.setRequestDetails(new RequestDetails());
        availRoomsRequest.getRequestDetails().setTrafficSource(new TrafficSource());
        availRoomsRequest.getRequestDetails().setLoggedIn(false);
        availRoomsRequest.getRequestDetails().setSrLng(10d);
        availRoomsRequest.getRequestDetails().setSrLat(10d);
        availRoomsRequest.getRequestDetails().setCouponCount(2);

        availRoomsRequest.setSearchCriteria(new AvailPriceCriteria());
        availRoomsRequest.getSearchCriteria().setRoomCriteria(new ArrayList<AvailRoomsSearchCriteria>());
        availRoomsRequest.getSearchCriteria().getRoomCriteria().add(new AvailRoomsSearchCriteria());
        availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).setRoomStayCandidates(new ArrayList<RoomStayCandidate>());
        availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().add(new RoomStayCandidate());
        availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().get(0).setChildAges(new ArrayList<>());
        availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().get(0).getChildAges().add(1);
        availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().get(0).setRooms(2);
        availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().get(0).setAdultCount(4);

        AddOnDetailState addOnDetailState = new AddOnDetailState();
        addOnDetailState.setAddOnType("addOnType");
        addOnDetailState.setSelected(AddOnState.REVIEW);
        availRoomsRequest.getSearchCriteria().setAddOnDetail(new HashMap<>());
        availRoomsRequest.getSearchCriteria().getAddOnDetail().put("abc", addOnDetailState);
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExtendedUser(new ExtendedUser());
        commonModifierResponse.getExtendedUser().setUuid("123");
        commonModifierResponse.getExtendedUser().setProfileType("profileType");
        commonModifierResponse.getExtendedUser().setAffiliateId("affiliateId");
        commonModifierResponse.getExtendedUser().setProfileId("profileId");
        commonModifierResponse.getExtendedUser().setCorporateData("");

        UserTravelDocument userTravelDocument = new UserTravelDocument();
        userTravelDocument.setDocNumber("docNumber");
        userTravelDocument.setDocType("PAN");
        UserName userName = new UserName();
        userName.setFirstName("firstName");
        userName.setLastName("lastName");
        userName.setMiddleName("middleName");
        UserPersonalDetail userPersonalDetail = new UserPersonalDetail();
        userPersonalDetail.setName(userName);
        UserAssociatedTraveller userAssociatedTraveller = new UserAssociatedTraveller();
        userAssociatedTraveller.setName(userName);
        commonModifierResponse.getExtendedUser().setAssociatedTravellers(Arrays.asList(userAssociatedTraveller));

        commonModifierResponse.getExtendedUser().setPersonalDetails(userPersonalDetail);
        commonModifierResponse.setHydraResponse(new HydraResponse());
        commonModifierResponse.getHydraResponse().setHydraMatchedSegment(new HashSet<>());
        commonModifierResponse.getHydraResponse().getHydraMatchedSegment().add("abc");
        commonModifierResponse.getHydraResponse().setFlightBooker(true);

        commonModifierResponse.setUserLocation(new UserLocation());
        commonModifierResponse.getUserLocation().setCity("city");
        commonModifierResponse.getUserLocation().setCountry("country");
        commonModifierResponse.getUserLocation().setState("state");

        availRoomsRequest.getSearchCriteria().setTravellerEmailID(new ArrayList<>());
        availRoomsRequest.getSearchCriteria().getTravellerEmailID().add("<EMAIL>");

        availRoomsRequest.setEmiDetail(new EMIDetail());
        com.mmt.hotels.model.request.MultiCurrencyInfo info = new com.mmt.hotels.model.request.MultiCurrencyInfo();
        info.setUserCurrency("INR");
        info.setRegionCurrency("INR");
        MultiCurrencyInfo multiCurrencyInfo = new MultiCurrencyInfo();
        multiCurrencyInfo.setRegionCurrency("INR");
        multiCurrencyInfo.setUserCurrency("INR");
        availRoomsRequest.getSearchCriteria().setMultiCurrencyInfo(multiCurrencyInfo);
        PriceByHotelsRequestBody priceByHotelsRequestBody = availRoomsRequestTransformer.convertAvailRoomsRequest(availRoomsRequest, commonModifierResponse);
        Assert.notNull(priceByHotelsRequestBody);
        Map<Object,Object> map = new LinkedHashMap<>();
        map.put("trv_reference","sessionKey");
        map.put("tag_one", 5);
        map.put("tag_two", false);
        map.put("tag_three", new LinkedHashMap<String, String>());
        ((LinkedHashMap) map.get("tag_three")).put("trivago", "booking");
        availRoomsRequest.getSearchCriteria().setMetaChannelInfo(map);
        priceByHotelsRequestBody = availRoomsRequestTransformer.convertAvailRoomsRequest(availRoomsRequest, commonModifierResponse);
        Assert.notNull(priceByHotelsRequestBody);
        Assert.notNull(priceByHotelsRequestBody.getMetaChannelInfo());
        Assert.notNull(((LinkedHashMap)priceByHotelsRequestBody.getMetaChannelInfo()).get("trv_reference"));
        Assert.notNull(priceByHotelsRequestBody.getRoomStayCandidates());

        commonModifierResponse.getExtendedUser().setTravelDocuments(Arrays.asList(userTravelDocument));
        priceByHotelsRequestBody = availRoomsRequestTransformer.convertAvailRoomsRequest(availRoomsRequest, commonModifierResponse);
    }

    @Test
    public void convertFetchLocationsRequestTest() {
        FetchLocationsRequest fetchLocationsRequest = new FetchLocationsRequest();
        fetchLocationsRequest.setPostalCodes(Arrays.asList("123"));
        fetchLocationsRequest.setCorrelationKey("correlationKey");

        FetchLocationsRequestBody fetchLocationsRequest1 = availRoomsRequestTransformer.convertFetchLocationsRequest(fetchLocationsRequest);
        Assert.notNull(fetchLocationsRequest1);

        Assert.isNull(availRoomsRequestTransformer.convertFetchLocationsRequest(null));
    }


    @Test
    public void testConvertAvailRoomsRequestCorp(){
        Mockito.doNothing().when(metricAspect).addToTimeInternalProcess(Mockito.any(),Mockito.any(),Mockito.anyLong());

        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setDeviceDetails(new DeviceDetails());
        availRoomsRequest.setExpData("abc");


        availRoomsRequest.setRequestDetails(new RequestDetails());
        availRoomsRequest.getRequestDetails().setTrafficSource(new TrafficSource());
        availRoomsRequest.getRequestDetails().setLoggedIn(false);
        availRoomsRequest.getRequestDetails().setSrLng(10d);
        availRoomsRequest.getRequestDetails().setSrLat(10d);
        availRoomsRequest.getRequestDetails().setCouponCount(2);
        availRoomsRequest.getRequestDetails().setPageContext(Constants.PAGE_CONTEXT_REVIEW);
        availRoomsRequest.getRequestDetails().setIdContext(Constants.CORP_ID_CONTEXT);
        availRoomsRequest.setSearchCriteria(new AvailPriceCriteria());
        availRoomsRequest.getSearchCriteria().setRoomCriteria(new ArrayList<AvailRoomsSearchCriteria>());
        availRoomsRequest.getSearchCriteria().getRoomCriteria().add(new AvailRoomsSearchCriteria());
        availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).setRoomStayCandidates(new ArrayList<RoomStayCandidate>());
        availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().add(new RoomStayCandidate());
        availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().get(0).setChildAges(new ArrayList<>());
        availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().get(0).getChildAges().add(1);
        availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().get(0).setRooms(2);
        availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().get(0).setAdultCount(4);
        availRoomsRequest.getSearchCriteria().setTravellerEmailID(new ArrayList<>());
        availRoomsRequest.getSearchCriteria().getTravellerEmailID().add("<EMAIL>");

        AddOnDetailState addOnDetailState = new AddOnDetailState();
        addOnDetailState.setAddOnType("addOnType");
        addOnDetailState.setSelected(AddOnState.REVIEW);
        availRoomsRequest.getSearchCriteria().setAddOnDetail(new HashMap<>());
        availRoomsRequest.getSearchCriteria().getAddOnDetail().put("abc", addOnDetailState);
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExtendedUser(new ExtendedUser());
        commonModifierResponse.getExtendedUser().setUuid("123");
        commonModifierResponse.getExtendedUser().setProfileType("profileType");
        commonModifierResponse.getExtendedUser().setAffiliateId("affiliateId");
        commonModifierResponse.getExtendedUser().setProfileId("profileId");
        commonModifierResponse.getExtendedUser().setCorporateData("");

        UserTravelDocument userTravelDocument = new UserTravelDocument();
        userTravelDocument.setDocNumber("docNumber");
        userTravelDocument.setDocType("PAN");
        UserName userName = new UserName();
        userName.setFirstName("firstName");
        userName.setLastName("lastName");
        userName.setMiddleName("middleName");
        UserPersonalDetail userPersonalDetail = new UserPersonalDetail();
        userPersonalDetail.setName(userName);
        UserAssociatedTraveller userAssociatedTraveller = new UserAssociatedTraveller();
        userAssociatedTraveller.setName(userName);
        commonModifierResponse.getExtendedUser().setAssociatedTravellers(Arrays.asList(userAssociatedTraveller));

        commonModifierResponse.getExtendedUser().setPersonalDetails(userPersonalDetail);
        commonModifierResponse.setHydraResponse(new HydraResponse());
        commonModifierResponse.getHydraResponse().setHydraMatchedSegment(new HashSet<>());
        commonModifierResponse.getHydraResponse().getHydraMatchedSegment().add("abc");
        commonModifierResponse.getHydraResponse().setFlightBooker(true);

        commonModifierResponse.setUserLocation(new UserLocation());
        commonModifierResponse.getUserLocation().setCity("city");
        commonModifierResponse.getUserLocation().setCountry("country");
        commonModifierResponse.getUserLocation().setState("state");

        availRoomsRequest.getSearchCriteria().setTravellerEmailID(new ArrayList<>());
        availRoomsRequest.getSearchCriteria().getTravellerEmailID().add("<EMAIL>");

        availRoomsRequest.setEmiDetail(new EMIDetail());
        com.mmt.hotels.model.request.MultiCurrencyInfo info = new com.mmt.hotels.model.request.MultiCurrencyInfo();
        info.setUserCurrency("INR");
        info.setRegionCurrency("INR");
        MultiCurrencyInfo multiCurrencyInfo = new MultiCurrencyInfo();
        multiCurrencyInfo.setRegionCurrency("INR");
        multiCurrencyInfo.setUserCurrency("INR");
        availRoomsRequest.getSearchCriteria().setMultiCurrencyInfo(multiCurrencyInfo);
        PriceByHotelsRequestBody priceByHotelsRequestBody = availRoomsRequestTransformer.convertAvailRoomsRequest(availRoomsRequest, commonModifierResponse);
        Assert.notNull(priceByHotelsRequestBody);
        Map<Object,Object> map = new LinkedHashMap<>();
        map.put("trv_reference","sessionKey");
        map.put("tag_one", 5);
        map.put("tag_two", false);
        map.put("tag_three", new LinkedHashMap<String, String>());
        ((LinkedHashMap) map.get("tag_three")).put("trivago", "booking");
        availRoomsRequest.getSearchCriteria().setMetaChannelInfo(map);
        priceByHotelsRequestBody = availRoomsRequestTransformer.convertAvailRoomsRequest(availRoomsRequest, commonModifierResponse);
        Assert.notNull(priceByHotelsRequestBody);
        Assert.notNull(priceByHotelsRequestBody.getMetaChannelInfo());
        Assert.notNull(((LinkedHashMap)priceByHotelsRequestBody.getMetaChannelInfo()).get("trv_reference"));
        Assert.notNull(priceByHotelsRequestBody.getRoomStayCandidates());

        commonModifierResponse.getExtendedUser().setTravelDocuments(Arrays.asList(userTravelDocument));
        priceByHotelsRequestBody = availRoomsRequestTransformer.convertAvailRoomsRequest(availRoomsRequest, commonModifierResponse);
    }

    // HTL-63666 Alternate Booking Test Cases
    @Test
    public void should_PopulateAlternateBookingInfo_When_AlternateBookingInfoPresent() {
        // Arrange
        AvailRoomsRequest availRoomsRequest = createBasicAvailRoomsRequest();
        AlternateBookingInfo alternateBookingInfo = new AlternateBookingInfo();
        alternateBookingInfo.setAlternateBooking(true);
        alternateBookingInfo.setOldBookingId("HTL123456789");
        alternateBookingInfo.setKey("alternate-booking-key-123");
        availRoomsRequest.setAlternateBookingInfo(alternateBookingInfo);

        CommonModifierResponse commonModifierResponse = createBasicCommonModifierResponse();

        // Act
        PriceByHotelsRequestBody result = availRoomsRequestTransformer.convertAvailRoomsRequest(
                availRoomsRequest, commonModifierResponse);

        // Assert
        Assert.notNull(result.getAlternateBookingInfo());
        Assert.isTrue(result.getAlternateBookingInfo().isAlternateBooking());
        Assert.isTrue("HTL123456789".equals(result.getAlternateBookingInfo().getExistingBookingId()));
        Assert.isTrue("alternate-booking-key-123".equals(result.getAlternateBookingInfo().getKey()));
    }

    @Test
    public void should_NotPopulateAlternateBookingInfo_When_AlternateBookingInfoIsNull() {
        // Arrange
        AvailRoomsRequest availRoomsRequest = createBasicAvailRoomsRequest();
        availRoomsRequest.setAlternateBookingInfo(null); // Null alternate booking info

        CommonModifierResponse commonModifierResponse = createBasicCommonModifierResponse();

        // Act
        PriceByHotelsRequestBody result = availRoomsRequestTransformer.convertAvailRoomsRequest(
                availRoomsRequest, commonModifierResponse);

        // Assert
        Assert.isNull(result.getAlternateBookingInfo());
    }

    @Test
    public void should_PopulateAlternateBookingInfo_When_AlternateBookingIsFalse() {
        // Arrange
        AvailRoomsRequest availRoomsRequest = createBasicAvailRoomsRequest();
        AlternateBookingInfo alternateBookingInfo = new AlternateBookingInfo();
        alternateBookingInfo.setAlternateBooking(false); // False
        alternateBookingInfo.setOldBookingId("HTL123456");
        alternateBookingInfo.setKey("test-key");
        availRoomsRequest.setAlternateBookingInfo(alternateBookingInfo);

        CommonModifierResponse commonModifierResponse = createBasicCommonModifierResponse();

        // Act
        PriceByHotelsRequestBody result = availRoomsRequestTransformer.convertAvailRoomsRequest(
                availRoomsRequest, commonModifierResponse);

        // Assert
        Assert.notNull(result.getAlternateBookingInfo());
        Assert.isTrue(!result.getAlternateBookingInfo().isAlternateBooking()); // Should be false
        Assert.isTrue("HTL123456".equals(result.getAlternateBookingInfo().getExistingBookingId()));
        Assert.isTrue("test-key".equals(result.getAlternateBookingInfo().getKey()));
    }

    @Test
    public void should_PopulateAlternateBookingInfo_When_OnlyRequiredFieldsPresent() {
        // Arrange
        AvailRoomsRequest availRoomsRequest = createBasicAvailRoomsRequest();
        AlternateBookingInfo alternateBookingInfo = new AlternateBookingInfo();
        alternateBookingInfo.setAlternateBooking(true);
        alternateBookingInfo.setOldBookingId("HTL999888777");
        // Not setting key
        availRoomsRequest.setAlternateBookingInfo(alternateBookingInfo);

        CommonModifierResponse commonModifierResponse = createBasicCommonModifierResponse();

        // Act
        PriceByHotelsRequestBody result = availRoomsRequestTransformer.convertAvailRoomsRequest(
                availRoomsRequest, commonModifierResponse);

        // Assert
        Assert.notNull(result.getAlternateBookingInfo());
        Assert.isTrue(result.getAlternateBookingInfo().isAlternateBooking());
        Assert.isTrue("HTL999888777".equals(result.getAlternateBookingInfo().getExistingBookingId()));
        Assert.isNull(result.getAlternateBookingInfo().getKey());
    }

    @Test
    public void should_PopulateAlternateBookingInfo_When_EmptyStringFields() {
        // Arrange
        AvailRoomsRequest availRoomsRequest = createBasicAvailRoomsRequest();
        AlternateBookingInfo alternateBookingInfo = new AlternateBookingInfo();
        alternateBookingInfo.setAlternateBooking(true);
        alternateBookingInfo.setOldBookingId(""); // Empty string
        alternateBookingInfo.setKey(""); // Empty string
        availRoomsRequest.setAlternateBookingInfo(alternateBookingInfo);

        CommonModifierResponse commonModifierResponse = createBasicCommonModifierResponse();

        // Act
        PriceByHotelsRequestBody result = availRoomsRequestTransformer.convertAvailRoomsRequest(
                availRoomsRequest, commonModifierResponse);

        // Assert
        Assert.notNull(result.getAlternateBookingInfo());
        Assert.isTrue(result.getAlternateBookingInfo().isAlternateBooking());
        Assert.isTrue("".equals(result.getAlternateBookingInfo().getExistingBookingId()));
        Assert.isTrue("".equals(result.getAlternateBookingInfo().getKey()));
    }

    @Test
    public void should_HandleGracefully_When_AvailRoomsRequestIsNull() {
        // Arrange
        AvailRoomsRequest availRoomsRequest = null;
        CommonModifierResponse commonModifierResponse = createBasicCommonModifierResponse();

        // Act
        PriceByHotelsRequestBody result = availRoomsRequestTransformer.convertAvailRoomsRequest(
                availRoomsRequest, commonModifierResponse);

        // Assert
        // When input is null, the transformer should handle gracefully
        // Either result is null (which is fine) or result.getAlternateBookingInfo() is null
        if (result != null) {
            Assert.isNull(result.getAlternateBookingInfo());
        }
    }

    @Test
    public void should_PopulateAlternateBookingInfo_When_ComplexScenarioWithAllFields() {
        // Arrange
        AvailRoomsRequest availRoomsRequest = createComplexAvailRoomsRequest();
        AlternateBookingInfo alternateBookingInfo = new AlternateBookingInfo();
        alternateBookingInfo.setAlternateBooking(true);
        alternateBookingInfo.setOldBookingId("HTL987654321");
        alternateBookingInfo.setKey("complex-alternate-booking-key");
        alternateBookingInfo.setAgentId("COMPLEX_AGENT_001");
        availRoomsRequest.setAlternateBookingInfo(alternateBookingInfo);

        CommonModifierResponse commonModifierResponse = createComplexCommonModifierResponse();

        // Act
        PriceByHotelsRequestBody result = availRoomsRequestTransformer.convertAvailRoomsRequest(
                availRoomsRequest, commonModifierResponse);

        // Assert
        Assert.notNull(result.getAlternateBookingInfo());
        Assert.isTrue(result.getAlternateBookingInfo().isAlternateBooking());
        Assert.isTrue("HTL987654321".equals(result.getAlternateBookingInfo().getExistingBookingId()));
        Assert.isTrue("complex-alternate-booking-key".equals(result.getAlternateBookingInfo().getKey()));
        // Verify other properties are still correctly populated
        Assert.notNull(result.getRoomStayCandidates());
        Assert.notNull(result.getCorrelationKey());
    }

    // Helper methods for creating test objects
    private AvailRoomsRequest createBasicAvailRoomsRequest() {
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setCorrelationKey("test-correlation-key");
        availRoomsRequest.setDeviceDetails(new DeviceDetails());
        availRoomsRequest.setRequestDetails(new RequestDetails());
        availRoomsRequest.getRequestDetails().setTrafficSource(new TrafficSource());
        availRoomsRequest.getRequestDetails().setLoggedIn(false);
        
        availRoomsRequest.setSearchCriteria(new AvailPriceCriteria());
        availRoomsRequest.getSearchCriteria().setRoomCriteria(new ArrayList<>());
        AvailRoomsSearchCriteria searchCriteria = new AvailRoomsSearchCriteria();
        searchCriteria.setRoomStayCandidates(new ArrayList<>());
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setChildAges(new ArrayList<>());
        roomStayCandidate.setRooms(1);
        roomStayCandidate.setAdultCount(2);
        searchCriteria.getRoomStayCandidates().add(roomStayCandidate);
        availRoomsRequest.getSearchCriteria().getRoomCriteria().add(searchCriteria);
        
        return availRoomsRequest;
    }

    private CommonModifierResponse createBasicCommonModifierResponse() {
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExtendedUser(new ExtendedUser());
        commonModifierResponse.getExtendedUser().setUuid("test-uuid");
        commonModifierResponse.getExtendedUser().setProfileType("PERSONAL");
        return commonModifierResponse;
    }

    private AvailRoomsRequest createComplexAvailRoomsRequest() {
        AvailRoomsRequest availRoomsRequest = createBasicAvailRoomsRequest();
        availRoomsRequest.setExpData("complex-exp-data");
        availRoomsRequest.getRequestDetails().setCouponCount(3);
        availRoomsRequest.getRequestDetails().setSrLat(28.6139);
        availRoomsRequest.getRequestDetails().setSrLng(77.2090);
        return availRoomsRequest;
    }

    private CommonModifierResponse createComplexCommonModifierResponse() {
        CommonModifierResponse commonModifierResponse = createBasicCommonModifierResponse();
        commonModifierResponse.getExtendedUser().setAffiliateId("COMPLEX_AFFILIATE");
        commonModifierResponse.getExtendedUser().setProfileId("COMPLEX_PROFILE");
        commonModifierResponse.setHydraResponse(new HydraResponse());
        commonModifierResponse.getHydraResponse().setHydraMatchedSegment(new HashSet<>());
        commonModifierResponse.getHydraResponse().getHydraMatchedSegment().add("complex-segment");
        return commonModifierResponse;
    }
}
