package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.helpers.ErrorHelper;
import com.mmt.hotels.clientgateway.response.CorpAutobookRequestorConfig;
import com.mmt.hotels.clientgateway.response.CtaBo;
import com.mmt.hotels.clientgateway.response.corporate.UpdateApprovalResponse;
import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.response.base.FailureReason;
import com.mmt.hotels.model.response.corporate.CtaBO;
import com.mmt.hotels.model.response.corporate.ResponseConfigBO;
import com.mmt.hotels.model.response.corporate.UpdateWorkflowResponse;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class UpdateApprovalResponseTransformerTest {

    @InjectMocks
    UpdateApprovalResponseTransformer updateApprovalResponseTransformer;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    private ErrorHelper errorHelper;

    private UpdateWorkflowResponse responseHES;

    @Before
    public void setUp() {
        responseHES = new UpdateWorkflowResponse();
    }

    @Test
    public void testConvertUpdateApprovalResponse_withValidData() {
        responseHES.setMessage("msg");
        responseHES.setResponseCode("300");
        responseHES.setStatus("ok");
        responseHES.setStatusCode(200);

        UpdateApprovalResponse updateApprovalResponse = updateApprovalResponseTransformer.convertUpdateApprovalResponse(responseHES);

        assertNotNull(updateApprovalResponse);
        assertEquals("msg", updateApprovalResponse.getMessage());
        assertEquals("300", updateApprovalResponse.getResponseCode());
        assertEquals("ok", updateApprovalResponse.getStatus());
        assertEquals(new Integer(200), updateApprovalResponse.getStatusCode());
    }

    @Test
    public void testConvertUpdateApprovalResponse_withResponseConfig() {
        // Arrange
        responseHES.setMessage("msg");
        responseHES.setResponseCode("300");
        responseHES.setStatus("ok");
        responseHES.setStatusCode(200);

        ResponseConfigBO responseConfig = new ResponseConfigBO();
        responseConfig.setTitle("Config Title");
        responseConfig.setSubTitle("Config SubTitle");
        CtaBO cta = new CtaBO();
        cta.setText("CTA Text");
        responseConfig.setCta(cta);
        responseHES.setResponseConfig(responseConfig);

        // Act
        UpdateApprovalResponse updateApprovalResponse = updateApprovalResponseTransformer.convertUpdateApprovalResponse(responseHES);

        // Assert
        assertNotNull(updateApprovalResponse);
        assertEquals("msg", updateApprovalResponse.getMessage());
        assertEquals("300", updateApprovalResponse.getResponseCode());
        assertEquals("ok", updateApprovalResponse.getStatus());
        assertEquals(new Integer(200), updateApprovalResponse.getStatusCode());
        assertNotNull(updateApprovalResponse.getUpdateConfig());
        assertEquals("Config Title", updateApprovalResponse.getUpdateConfig().getTitle());
        assertEquals("Config SubTitle", updateApprovalResponse.getUpdateConfig().getSubTitle());
        assertNotNull(updateApprovalResponse.getUpdateConfig().getCta());
        assertEquals("CTA Text", updateApprovalResponse.getUpdateConfig().getCta().getText());
    }

    @Test
    public void testConvertUpdateApprovalResponse_withFailureReason() {
        responseHES.setFailureReason(new FailureReason("FAIL_CODE", "Failure message"));

        UpdateApprovalResponse updateApprovalResponse = updateApprovalResponseTransformer.convertUpdateApprovalResponse(responseHES);

        assertNotNull(updateApprovalResponse);
        assertNotNull(updateApprovalResponse.getError());
//        assertEquals("FAIL_CODE", updateApprovalResponse.getError().getErrorCode());
//        assertEquals("Failure message", updateApprovalResponse.getError().getErrorMessage());
    }
    @Test
    public void convertUpdateApprovalResponseTest() {
        UpdateWorkflowResponse responseHES = new UpdateWorkflowResponse();
        UpdateApprovalResponse updateApprovalResponse = new UpdateApprovalResponse();
        responseHES.setMessage("msg");
        responseHES.setResponseCode("300");
        responseHES.setStatus("ok");
        responseHES.setStatusCode(200);
        updateApprovalResponse = updateApprovalResponseTransformer.convertUpdateApprovalResponse(responseHES);
        Assert.assertNotNull(updateApprovalResponse);
        Assert.assertEquals("msg", updateApprovalResponse.getMessage());
        Assert.assertEquals("300", updateApprovalResponse.getResponseCode());
        Assert.assertEquals("ok", updateApprovalResponse.getStatus());
        Assert.assertEquals(new Integer(200), updateApprovalResponse.getStatusCode());
    }
}
