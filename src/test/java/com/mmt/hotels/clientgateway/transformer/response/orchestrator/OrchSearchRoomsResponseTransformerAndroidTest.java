package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gommt.hotels.orchestrator.detail.enums.RoomType;
import com.gommt.hotels.orchestrator.detail.model.request.prime.BlackInfo;
import com.gommt.hotels.orchestrator.detail.model.request.prime.BorderGradient;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetailsResponse;
import com.gommt.hotels.orchestrator.detail.model.response.common.RangePrice;
import com.gommt.hotels.orchestrator.detail.model.response.da.Inclusion;
import com.gommt.hotels.orchestrator.detail.model.response.da.PriceVariation;
import com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData;
import com.gommt.hotels.orchestrator.detail.model.response.persuasion.BenefitType;
import com.gommt.hotels.orchestrator.detail.model.response.persuasion.DealBenefits;
import com.gommt.hotels.orchestrator.detail.model.response.persuasion.PrimaryOffer;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.AlternatePriceCard;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.ComboType;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.PaymentPlan;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.Rooms;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.moblanding.CardAction;
import com.mmt.hotels.clientgateway.response.moblanding.CardInfo;
import com.mmt.hotels.clientgateway.response.rooms.LoginPersuasion;
import com.mmt.hotels.clientgateway.response.rooms.PriceGraphInfo;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomRatePlan;
import com.mmt.hotels.clientgateway.response.rooms.TariffViewType;
import com.mmt.hotels.clientgateway.response.staticdetail.AllInclusiveCard;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.AddOnHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.AlternatePriceHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.CancellationPolicyHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.DeepLinkHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.RoomAmentiesHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.RoomInfoHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.SearchRoomsBannerHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.SearchRoomsFilter;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.SearchRoomsMediaHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.SearchRoomsPersuasionHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.SearchRoomsPriceHelper;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.DayUseUtil;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.staticdata.PriceVariationType;
import com.mmt.hotels.pojo.listing.personalization.BGLinearGradient;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.CacheManager;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.EXP_RTBC;
import static com.mmt.hotels.clientgateway.constants.Constants.MYB_NEW_DETAILS_EXP_KEY;
import static com.mmt.hotels.clientgateway.constants.Constants.NEW_BLACK_DEAL;
import static com.mmt.hotels.clientgateway.constants.Constants.NEW_DETAIL_PAGE;
import static com.mmt.hotels.clientgateway.constants.Constants.SHOW_OCC_PACKAGE;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Comprehensive unit tests for OrchSearchRoomsResponseTransformerAndroid
 * Tests the actual business logic using real JSON data to achieve maximum coverage
 */
@RunWith(MockitoJUnitRunner.class)
public class OrchSearchRoomsResponseTransformerAndroidTest {

    @InjectMocks
    private OrchSearchRoomsResponseTransformerAndroid transformer;

    @Mock
    private CommonResponseTransformer commonResponseTransformer;
    @Mock
    private SearchRoomsPriceHelper searchRoomsPriceHelper;
    @Mock
    private PersuasionUtil persuasionUtil;
    @Mock
    private Utility utility;
    @Mock
    private SearchRoomsFilter searchRoomsFilter;
    @Mock
    private CancellationPolicyHelper cancellationPolicyHelper;
    @Mock
    private SearchRoomsPersuasionHelper searchRoomsPersuasionHelper;
    @Spy
    private DateUtil dateUtil;
    @Mock
    private DayUseUtil dayUseUtil;
    @Mock
    private MetricAspect metricAspect;
    @Mock
    private CommonConfigConsul commonConfigConsul;
    @Mock
    private PolyglotService polyglotService;
    @Mock
    private ObjectMapperUtil objectMapperUtil;
    @Mock
    private CacheManager cacheManager;
    @Mock
    private MobConfigHelper mobConfigHelper;
    @Mock
    private AddOnHelper addOnHelper;
    @Mock
    private RoomInfoHelper roomInfoHelper;
    @Mock
    private RoomAmentiesHelper roomAmentiesHelper;
    @Mock
    private SearchRoomsMediaHelper searchRoomsMediaHelper;
    @Mock
    private DeepLinkHelper deepLinkHelper;
    @Mock
    private SearchRoomsBannerHelper searchRoomsBannerHelper;
    @Mock
    private AlternatePriceHelper alternatePriceHelper;

    private SearchRoomsRequest searchRoomsRequest;
    private HotelDetailsResponse hotelDetailsResponseExact;
    private SearchCriteria searchCriteria;
    private SearchRoomsCriteria searchRoomsCriteria;
    private RequestDetails requestDetails;
    private CommonModifierResponse commonModifierResponse;
    private ObjectMapper objectMapper;

    @Before
    public void setUp() throws IOException {
        objectMapper = new ObjectMapper();
        setupTestData();
        setupMocks();
        setupPrivateFields();
    }

    private void setupTestData() throws IOException {
        // Load real JSON data
        try (InputStream inputStream = getClass().getClassLoader()
                .getResourceAsStream("transformer/response/orch_search_rooms.json")) {
            assertNotNull("JSON file should exist", inputStream);
            hotelDetailsResponseExact = objectMapper.readValue(inputStream, HotelDetailsResponse.class);
        }

        // Setup request objects
        searchRoomsRequest = new SearchRoomsRequest();

        searchCriteria = new SearchCriteria();
        searchCriteria.setCheckIn("2025-07-01");
        searchCriteria.setCheckOut("2025-07-02");
        searchCriteria.setCurrency("INR");

        searchRoomsCriteria = new SearchRoomsCriteria();
        searchRoomsCriteria.setCheckIn("2025-07-01");
        searchRoomsCriteria.setCheckOut("2025-07-02");
        searchRoomsCriteria.setCurrency("INR");
        searchRoomsRequest.setSearchCriteria(searchRoomsCriteria);

        requestDetails = new RequestDetails();
        
        requestDetails.setFunnelSource("HOTELS");

        commonModifierResponse = new CommonModifierResponse();
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put("RTBC", "1");
        expDataMap.put("NEW_DETAIL_PAGE", "1");
        expDataMap.put("PRICE_VARIATION_V2", "1");
        commonModifierResponse.setExpDataMap(expDataMap);
    }

    private void setupMocks() {
        // Setup comprehensive mocks for all dependencies
        //lenient().when(dateUtil.getDaysDiff(any(LocalDate.class), any(LocalDate.class))).thenReturn(2);
        lenient().when(utility.isLuxeHotel(any())).thenReturn(true);
        lenient().when(utility.isOHSExpEnable(anyString(), any(LinkedHashMap.class))).thenReturn(true);
        //lenient().when(utility.isExperimentTrue(any(LinkedHashMap.class), anyString())).thenReturn(true);
        lenient().when(utility.isExperimentOn(any(LinkedHashMap.class), anyString())).thenReturn(true);
        //lenient().when(utility.isExperimentValid(any(LinkedHashMap.class), anyString(), anyInt())).thenReturn(true);
        lenient().when(utility.isPriceVariationV2Enabled(any(LinkedHashMap.class))).thenReturn(true);
        lenient().when(utility.getBgLinearGradientForPriceVariationType(any(PriceVariationType.class)))
                .thenReturn(new BGLinearGradient());
        lenient().when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated Text");
        lenient().when(commonResponseTransformer.buildTrackingMap(any(LinkedHashMap.class))).thenReturn(new HashMap<>());

        MDC.put("CLIENT", "ANDROID");
    }

    private void setupPrivateFields() {
        // Set private configuration fields using reflection
        //ReflectionTestUtils.setField(transformer, "EXP_RTBC", "RTBC");
        //ReflectionTestUtils.setField(transformer, "NEW_DETAIL_PAGE", "NEW_DETAIL_PAGE");
        //ReflectionTestUtils.setField(transformer, "NEW_BLACK_DEAL", "NEW_BLACK_DEAL");
        //ReflectionTestUtils.setField(transformer, "PRICE_VARIATION_V2", "PRICE_VARIATION_V2");
        
        // Add additional configuration fields that might be needed
        //ReflectionTestUtils.setField(transformer, "callToBook", "CALL_TO_BOOK");
        //ReflectionTestUtils.setField(transformer, "pahWithoutCCText", "Pay at Hotel");
        ReflectionTestUtils.setField(transformer, "priceGraphTextIcon", "https://example.com/price-graph-text.png");
        ReflectionTestUtils.setField(transformer, "priceGraphIcon", "https://example.com/price-graph.png");
        ReflectionTestUtils.setField(transformer, "allInclusiveCard", new AllInclusiveCard());
        ReflectionTestUtils.setField(transformer, "paymentPlanIcon", "https://promos.makemytrip.com/Hotels_product/group/ic_rupee_2x.png");
        //ReflectionTestUtils.setField(transformer, "ratePlanNameConfigProperty", "{}");
        //ReflectionTestUtils.setField(transformer, "ratePlanNameConfigRedesign", "{}");
    }

    @Test
    public void init_test() throws JsonParseException {
        transformer.init();
    }

    // ========== COMPREHENSIVE BUSINESS LOGIC COVERAGE TEST ==========

    private SearchRoomsRequest getSearchRoomsRequest() {
        SearchRoomsRequest detailedRequest = new SearchRoomsRequest();
        SearchRoomsCriteria detailedSearchCriteria = new SearchRoomsCriteria();
        detailedRequest.setSearchCriteria(detailedSearchCriteria);
        detailedSearchCriteria.setCheckIn("2025-07-01");
        detailedSearchCriteria.setCheckOut("2025-07-02");
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setAdultCount(2);
        roomStayCandidate.setRooms(1);
        roomStayCandidates.add(roomStayCandidate);
        detailedSearchCriteria.setRoomStayCandidates(roomStayCandidates);
        return detailedRequest;
    }

    private CommonModifierResponse getCommonModifierResponse() {
// Setup comprehensive experiment flags
        LinkedHashMap<String, String> comprehensiveExpDataMap = new LinkedHashMap<>();
        comprehensiveExpDataMap.put(EXP_RTBC, "t");
        comprehensiveExpDataMap.put(NEW_DETAIL_PAGE, "t");
        comprehensiveExpDataMap.put(NEW_BLACK_DEAL, "t");
        comprehensiveExpDataMap.put(ExperimentKeys.BLACK_REVAMP.getKey(), "true");
        comprehensiveExpDataMap.put(ExperimentKeys.PRICE_VARIATION_V2.getKey(), "true");
        comprehensiveExpDataMap.put("CALL_TO_BOOK", "1");
        comprehensiveExpDataMap.put(MYB_NEW_DETAILS_EXP_KEY, "true");
        comprehensiveExpDataMap.put(ExperimentKeys.ANCILLARY_DISPLAY_PRICE_VARIANT.getKey(), "1");

        CommonModifierResponse comprehensiveModifierResponse = new CommonModifierResponse();
        comprehensiveModifierResponse.setExpDataMap(comprehensiveExpDataMap);
        return comprehensiveModifierResponse;
    }

    @Test
    public void should_ExecuteMainBusinessLogic_When_ProcessingRealHotelData() throws JsonProcessingException {
        // Given - Setup comprehensive test data
        SearchRoomsRequest detailedRequest = getSearchRoomsRequest();
        
        List<Filter> detailedFilterCriteria = Collections.singletonList(new Filter());
        
        RequestDetails detailedRequestDetails = new RequestDetails();
        detailedRequestDetails.setFunnelSource("HOTELS");
        
        // Setup comprehensive experiment flags
        CommonModifierResponse comprehensiveModifierResponse = getCommonModifierResponse();
        String comprehensiveExpDataString = objectMapper.writeValueAsString(comprehensiveModifierResponse.getExpDataMap());

        // Verify that our real JSON data has the expected structure
        assertNotNull("Hotel details should be loaded from JSON", hotelDetailsResponseExact);
        assertNotNull("Hotel details object should exist", hotelDetailsResponseExact.getHotelDetails());
        assertNotNull("Hotel ID should exist", hotelDetailsResponseExact.getHotelDetails().getId());

        // Verify rooms exist in the test data
        assertNotNull("Rooms should exist in test data", hotelDetailsResponseExact.getHotelDetails().getRooms());
        assertFalse("Should have at least one room", hotelDetailsResponseExact.getHotelDetails().getRooms().isEmpty());
        
        // Setup additional mocks to ensure the business logic can execute
        lenient().when(utility.isExperimentValid(any(LinkedHashMap.class), anyString(), anyInt())).thenReturn(true);
        
        // When - Execute the main business logic
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
                detailedRequest, hotelDetailsResponseExact, "comprehensive_exp_data",
                Collections.emptyList(), detailedRequest.getSearchCriteria(), detailedFilterCriteria,
                "comprehensive_variant_keys", detailedRequestDetails, comprehensiveModifierResponse);

        // Then - Verify comprehensive business logic execution
        assertNotNull("SearchRoomsResponse should not be null", result);
        
        // Verify that utility methods were called (indicating business logic execution)
        // Additional verifications to ensure the method processed the hotel data
        assertFalse("Method should have processed hotel data", hotelDetailsResponseExact.getHotelDetails().getRooms().isEmpty());

        HotelDetailsResponse hotelDetailsResponse = new HotelDetailsResponse();
        BeanUtils.copyProperties(hotelDetailsResponseExact, hotelDetailsResponse);

        hotelDetailsResponse.getHotelDetails().setAlternateDatePriceDetails(new ArrayList<>());
        AlternatePriceCard alternatePriceCard = new AlternatePriceCard();
        alternatePriceCard.setBasePrice(2000);
        hotelDetailsResponse.getHotelDetails().getAlternateDatePriceDetails().add(alternatePriceCard);
        PriceVariation priceVariation = new PriceVariation();
        priceVariation.setPercentage(10);
        priceVariation.setType("TEST");
        priceVariation.setVariationType(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.TYPICAL);
        hotelDetailsResponse.getHotelDetails().setPriceVariation(priceVariation);
        result = transformer.convertSearchRoomsResponse(
                detailedRequest, hotelDetailsResponse, comprehensiveExpDataString,
                Collections.emptyList(), detailedRequest.getSearchCriteria(), detailedFilterCriteria,
                "comprehensive_variant_keys", detailedRequestDetails, comprehensiveModifierResponse);

        // Then - Verify comprehensive business logic execution
        assertNotNull("SearchRoomsResponse should not be null", result);

    }

    @Test
    public void benefit_Test() throws JsonProcessingException {
        SearchRoomsRequest detailedRequest = getSearchRoomsRequest();

        List<Filter> detailedFilterCriteria = Collections.singletonList(new Filter());

        RequestDetails detailedRequestDetails = new RequestDetails();
        detailedRequestDetails.setFunnelSource("HOTELS");

        // Setup comprehensive experiment flags
        CommonModifierResponse comprehensiveModifierResponse = getCommonModifierResponse();
        String comprehensiveExpDataString = objectMapper.writeValueAsString(comprehensiveModifierResponse.getExpDataMap());

        HotelDetailsResponse hotelDetailsResponse = new HotelDetailsResponse();
        BeanUtils.copyProperties(hotelDetailsResponseExact, hotelDetailsResponse);
        List<Inclusion> inclusions = new ArrayList<>();
        inclusions.add(Inclusion.builder()
                        .type("LONGSTAY")
                        .code("LONGSTAY_DISCOUNT")
                        .value("discount")
                .build());
        DealBenefits dealBenefits = DealBenefits.builder().benefitType(BenefitType.LONG_STAY_BENEFITS)
                .inclusionsList(inclusions)
                .borderGradient(BorderGradient.builder().build())
                .build();
        hotelDetailsResponse.getHotelDetails().setDealBenefits(new ArrayList<>());
        hotelDetailsResponse.getHotelDetails().getDealBenefits().add(dealBenefits);
        commonModifierResponse.getExpDataMap().put(SHOW_OCC_PACKAGE, "false");
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
                detailedRequest, hotelDetailsResponse, comprehensiveExpDataString,
                Collections.emptyList(), detailedRequest.getSearchCriteria(), detailedFilterCriteria,
                "comprehensive_variant_keys", detailedRequestDetails, comprehensiveModifierResponse);

        // Then - Verify comprehensive business logic execution
        assertNotNull("SearchRoomsResponse should not be null", result);

        DealBenefits blackDealBenefits = DealBenefits.builder().benefitType(BenefitType.BLACK_BENEFITS)
                .inclusionsList(inclusions)
                .borderGradient(BorderGradient.builder().build())
                .loyaltyDetails(BlackInfo.builder().build())
                .build();
        hotelDetailsResponse.getHotelDetails().getDealBenefits().add(blackDealBenefits);
        result = transformer.convertSearchRoomsResponse(
                detailedRequest, hotelDetailsResponse, comprehensiveExpDataString,
                Collections.emptyList(), detailedRequest.getSearchCriteria(), detailedFilterCriteria,
                "comprehensive_variant_keys", detailedRequestDetails, comprehensiveModifierResponse);

        // Then - Verify comprehensive business logic execution
        assertNotNull("SearchRoomsResponse should not be null", result);

        List<RangePrice> offerList = new ArrayList<>();
        offerList.add(RangePrice.builder().build());
        hotelDetailsResponse.getHotelDetails().setOffers(offerList);
        hotelDetailsResponse.getHotelDetails().setPrimaryOffer(PrimaryOffer.builder()
                .desc("test_desc")
                .iconUrl("test_icon")
                .build());
        result = transformer.convertSearchRoomsResponse(
                detailedRequest, hotelDetailsResponse, comprehensiveExpDataString,
                Collections.emptyList(), detailedRequest.getSearchCriteria(), detailedFilterCriteria,
                "comprehensive_variant_keys", detailedRequestDetails, comprehensiveModifierResponse);
        assertNotNull("SearchRoomsResponse should not be null", result);


    }

    @Test
    public void combo_Test() throws JsonProcessingException {
        HotelDetailsResponse hotelDetailsResponse = null;
        try (InputStream inputStream = getClass().getClassLoader()
                .getResourceAsStream("transformer/response/orch_search_rooms_combo.json")) {
            assertNotNull("JSON file should exist", inputStream);
            hotelDetailsResponse = objectMapper.readValue(inputStream, HotelDetailsResponse.class);
        } catch (Exception ignored) {

        }

        SearchRoomsRequest detailedRequest = getSearchRoomsRequest();

        List<Filter> detailedFilterCriteria = Collections.singletonList(new Filter());

        RequestDetails detailedRequestDetails = new RequestDetails();
        detailedRequestDetails.setFunnelSource("HOTELS");

        // Setup comprehensive experiment flags
        CommonModifierResponse comprehensiveModifierResponse = getCommonModifierResponse();
        String comprehensiveExpDataString = objectMapper.writeValueAsString(comprehensiveModifierResponse.getExpDataMap());
        commonModifierResponse.getExpDataMap().put(SHOW_OCC_PACKAGE, "false");
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
                detailedRequest, hotelDetailsResponse, comprehensiveExpDataString,
                Collections.emptyList(), detailedRequest.getSearchCriteria(), detailedFilterCriteria,
                "comprehensive_variant_keys", detailedRequestDetails, comprehensiveModifierResponse);

        // Then - Verify comprehensive business logic execution
        assertNotNull("SearchRoomsResponse should not be null", result);

    }

    @Test
    public void package_Test() throws JsonProcessingException {
        HotelDetailsResponse hotelDetailsResponse = null;
        try (InputStream inputStream = getClass().getClassLoader()
                .getResourceAsStream("transformer/response/orch_search_rooms_package.json")) {
            assertNotNull("JSON file should exist", inputStream);
            hotelDetailsResponse = objectMapper.readValue(inputStream, HotelDetailsResponse.class);
        } catch (Exception ignored) {

        }
        SearchRoomsRequest detailedRequest = getSearchRoomsRequest();

        List<Filter> detailedFilterCriteria = Collections.singletonList(new Filter());

        RequestDetails detailedRequestDetails = new RequestDetails();
        detailedRequestDetails.setFunnelSource("HOTELS");

        // Setup comprehensive experiment flags
        CommonModifierResponse comprehensiveModifierResponse = getCommonModifierResponse();
        String comprehensiveExpDataString = objectMapper.writeValueAsString(comprehensiveModifierResponse.getExpDataMap());


        comprehensiveModifierResponse.getExpDataMap().put(SHOW_OCC_PACKAGE, "false");
        lenient().when(utility.isExperimentTrue(any(LinkedHashMap.class), eq(ExperimentKeys.SHOW_RECOMMENDED_ROOMS.getKey()))).thenReturn(true);
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
                detailedRequest, hotelDetailsResponse, comprehensiveExpDataString,
                Collections.emptyList(), detailedRequest.getSearchCriteria(), detailedFilterCriteria,
                "comprehensive_variant_keys", detailedRequestDetails, comprehensiveModifierResponse);

        // Then - Verify comprehensive business logic execution
        assertNotNull("SearchRoomsResponse should not be null", result);

        if (hotelDetailsResponse!=null && hotelDetailsResponse.getHotelDetails() != null && CollectionUtils.isNotEmpty(hotelDetailsResponse.getHotelDetails().getRooms())) {
            Rooms packageRooms = hotelDetailsResponse.getHotelDetails().getRooms().stream().filter(rooms -> RoomType.MMT_RECOMMEND.name().equalsIgnoreCase(rooms.getType())).findFirst().orElse(null);
            if(packageRooms!=null) {
                hotelDetailsResponse.getHotelDetails().setListingType("entire");
                hotelDetailsResponse.getHotelDetails().setRoomCount(2);

                packageRooms.setType(RoomType.SUPER_PACKAGE.name());
                result = transformer.convertSearchRoomsResponse(
                        detailedRequest, hotelDetailsResponse, comprehensiveExpDataString,
                        Collections.emptyList(), detailedRequest.getSearchCriteria(), detailedFilterCriteria,
                        "comprehensive_variant_keys", detailedRequestDetails, comprehensiveModifierResponse);

                // Then - Verify comprehensive business logic execution
                assertNotNull("SearchRoomsResponse should not be null", result);

                lenient().when(utility.isExperimentTrue(any(LinkedHashMap.class), eq(ExperimentKeys.SHOW_RECOMMENDED_ROOMS.getKey()))).thenReturn(false);
                hotelDetailsResponse.getHotelDetails().setRoomCount(1);
                result = transformer.convertSearchRoomsResponse(
                        detailedRequest, hotelDetailsResponse, comprehensiveExpDataString,
                        Collections.emptyList(), detailedRequest.getSearchCriteria(), detailedFilterCriteria,
                        "comprehensive_variant_keys", detailedRequestDetails, comprehensiveModifierResponse);
                assertNotNull("SearchRoomsResponse should not be null", result);


                lenient().when(utility.isExperimentTrue(any(LinkedHashMap.class), eq(SHOW_OCC_PACKAGE))).thenReturn(true);
                lenient().when(utility.isExperimentTrue(any(LinkedHashMap.class), eq(ExperimentKeys.SHOW_RECOMMENDED_ROOMS.getKey()))).thenReturn(true);

                packageRooms.setType(RoomType.OCCASION_PACKAGE.name());
                result = transformer.convertSearchRoomsResponse(
                        detailedRequest, hotelDetailsResponse, comprehensiveExpDataString,
                        Collections.emptyList(), detailedRequest.getSearchCriteria(), detailedFilterCriteria,
                        "comprehensive_variant_keys", detailedRequestDetails, comprehensiveModifierResponse);

                // Then - Verify comprehensive business logic execution
                assertNotNull("SearchRoomsResponse should not be null", result);
            }
        }

    }

    @Test
    public void occupancyRooms_Test() throws JsonProcessingException {
        HotelDetailsResponse hotelDetailsResponse = null;
        try (InputStream inputStream = getClass().getClassLoader()
                .getResourceAsStream("transformer/response/orch_search_rooms_occupancy.json")) {
            assertNotNull("JSON file should exist", inputStream);
            hotelDetailsResponse = objectMapper.readValue(inputStream, HotelDetailsResponse.class);
        } catch (Exception ignored) {

        }
        SearchRoomsRequest detailedRequest = getSearchRoomsRequest();

        List<Filter> detailedFilterCriteria = Collections.singletonList(new Filter());

        RequestDetails detailedRequestDetails = new RequestDetails();
        detailedRequestDetails.setFunnelSource("HOTELS");

        // Setup comprehensive experiment flags
        CommonModifierResponse comprehensiveModifierResponse = getCommonModifierResponse();
        String comprehensiveExpDataString = objectMapper.writeValueAsString(comprehensiveModifierResponse.getExpDataMap());


        comprehensiveModifierResponse.getExpDataMap().put(SHOW_OCC_PACKAGE, "false");
        lenient().when(utility.isExperimentTrue(any(LinkedHashMap.class), eq(ExperimentKeys.SHOW_RECOMMENDED_ROOMS.getKey()))).thenReturn(true);
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
                detailedRequest, hotelDetailsResponse, comprehensiveExpDataString,
                Collections.emptyList(), detailedRequest.getSearchCriteria(), detailedFilterCriteria,
                "comprehensive_variant_keys", detailedRequestDetails, comprehensiveModifierResponse);

        // Then - Verify comprehensive business logic execution
        assertNotNull("SearchRoomsResponse should not be null", result);


    }

    @Test
    public void searchRooms_GCC_Test() throws JsonProcessingException {
        HotelDetailsResponse hotelDetailsResponse = null;
        try (InputStream inputStream = getClass().getClassLoader()
                .getResourceAsStream("transformer/response/orch_search_rooms_gcc.json")) {
            assertNotNull("JSON file should exist", inputStream);
            hotelDetailsResponse = objectMapper.readValue(inputStream, HotelDetailsResponse.class);
        } catch (Exception ignored) {

        }
        SearchRoomsRequest detailedRequest = getSearchRoomsRequest();

        List<Filter> detailedFilterCriteria = Collections.singletonList(new Filter());

        RequestDetails detailedRequestDetails = new RequestDetails();
        detailedRequestDetails.setFunnelSource("HOTELS");

        // Setup comprehensive experiment flags
        CommonModifierResponse comprehensiveModifierResponse = getCommonModifierResponse();
        String comprehensiveExpDataString = objectMapper.writeValueAsString(comprehensiveModifierResponse.getExpDataMap());


        comprehensiveModifierResponse.getExpDataMap().put(SHOW_OCC_PACKAGE, "false");
        lenient().when(utility.isExperimentTrue(any(LinkedHashMap.class), eq(ExperimentKeys.SHOW_RECOMMENDED_ROOMS.getKey()))).thenReturn(true);
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(detailedRequest, hotelDetailsResponse, comprehensiveExpDataString,
                Collections.emptyList(), detailedRequest.getSearchCriteria(), detailedFilterCriteria,
                "comprehensive_variant_keys", detailedRequestDetails, comprehensiveModifierResponse);

        // Then - Verify comprehensive business logic execution
        assertNotNull("SearchRoomsResponse should not be null", result);


    }

    // ==================== ABSTRACT METHODS TESTS ====================

    @Test
    public void should_ReturnNull_When_CreateTopRatedPersuasion() {
        PersuasionObject result = transformer.createTopRatedPersuasion(true);
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_CreateTopRatedPersuasionWithFalse() {
        PersuasionObject result = transformer.createTopRatedPersuasion(false);
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildLoginPersuasion() {
        LoginPersuasion result = transformer.buildLoginPersuasion();
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildDelayedConfirmationPersuasion() {
        PersuasionResponse result = transformer.buildDelayedConfirmationPersuasion("CORP_ALIAS", true);
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildDelayedConfirmationPersuasionWithFalse() {
        PersuasionResponse result = transformer.buildDelayedConfirmationPersuasion("CORP_ALIAS", false);
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildDelayedConfirmationPersuasionWithNullCorpAlias() {
        PersuasionResponse result = transformer.buildDelayedConfirmationPersuasion(null, true);
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildSpecialFareTagPersuasion() {
        PersuasionResponse result = transformer.buildSpecialFareTagPersuasion("CORP_ALIAS");
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildSpecialFareTagPersuasionWithNullCorpAlias() {
        PersuasionResponse result = transformer.buildSpecialFareTagPersuasion(null);
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildSpecialFareTagWithInfoPersuasion() {
        PersuasionResponse result = transformer.buildSpecialFareTagWithInfoPersuasion("CORP_ALIAS", true);
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildSpecialFareTagWithInfoPersuasionWithFalse() {
        PersuasionResponse result = transformer.buildSpecialFareTagWithInfoPersuasion("CORP_ALIAS", false);
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildConfirmationTextPersuasion() {
        PersuasionResponse result = transformer.buildConfirmationTextPersuasion("CORP_ALIAS", true, false);
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildConfirmationTextPersuasionWithDifferentParams() {
        PersuasionResponse result = transformer.buildConfirmationTextPersuasion("DIFFERENT_ALIAS", false, false);
        assertNull(result);
    }

    @Test
    public void should_ReturnAppsInclusionHtml_When_GetHtml() {
        String html = "APPS_INCLUSION_HTML";
        String result = transformer.getHtml();
        assertEquals("Should return the same HTML", html, result);
    }

    // ==================== MAIN CONVERSION METHOD TESTS ====================

    @Test
    public void should_ReturnEmptyResponse_When_HotelDetailsResponseIsNull() {
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
                searchRoomsRequest, null, "expData",
                Collections.emptyList(), searchCriteria, Collections.emptyList(),
                "expVariantKeys", requestDetails, commonModifierResponse);

        assertNotNull("SearchRoomsResponse should not be null", result);
        // Verify basic structure is created even with null input
    }

    @Test
    public void should_ReturnEmptyResponse_When_HotelDetailsIsNull() {
        HotelDetailsResponse emptyResponse = new HotelDetailsResponse();
        emptyResponse.setHotelDetails(null);

        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
                searchRoomsRequest, emptyResponse, "expData",
                Collections.emptyList(), searchCriteria, Collections.emptyList(),
                "expVariantKeys", requestDetails, commonModifierResponse);

        assertNotNull("SearchRoomsResponse should not be null", result);
    }

    @Test
    public void should_SetCurrency_When_ValidHotelDetailsProvided() {
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
                searchRoomsRequest, hotelDetailsResponseExact, "expData",
                Collections.emptyList(), searchCriteria, Collections.emptyList(),
                "expVariantKeys", requestDetails, commonModifierResponse);

        assertNotNull("SearchRoomsResponse should not be null", result);
        // Currency should be set from hotel details
    }

    @Test
    public void should_ReturnEmptyResponse_When_NoRoomsOrRoomCombos() {
        HotelDetailsResponse emptyRoomsResponse = new HotelDetailsResponse();
        HotelDetails emptyHotelDetails = new HotelDetails();
        emptyHotelDetails.setRooms(Collections.emptyList());
        emptyHotelDetails.setRoomCombos(Collections.emptyList());
        emptyRoomsResponse.setHotelDetails(emptyHotelDetails);

        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
                searchRoomsRequest, emptyRoomsResponse, "expData",
                Collections.emptyList(), searchCriteria, Collections.emptyList(),
                "expVariantKeys", requestDetails, commonModifierResponse);

        assertNotNull("SearchRoomsResponse should not be null", result);
    }

    // ==================== PRICE GRAPH INFO TESTS ====================

    @Test
    public void should_ReturnNull_When_PriceVariationIsNull() {
        PriceGraphInfo result = transformer.getPriceGraphInfo(null, "INR", new HashMap<>());
        assertNull("Should return null for null price variation", result);
    }

    @Test
    public void should_ReturnNull_When_PriceVariationTypeIsBlank() {
        PriceVariation priceVariation = new PriceVariation();
        priceVariation.setType("");
        
        PriceGraphInfo result = transformer.getPriceGraphInfo(priceVariation, "INR", new HashMap<>());
        assertNull("Should return null for blank price variation type", result);
    }

    @Test
    public void should_ReturnNull_When_PriceVariationTypeIsNull() {
        PriceVariation priceVariation = new PriceVariation();
        priceVariation.setType(null);
        
        PriceGraphInfo result = transformer.getPriceGraphInfo(priceVariation, "INR", new HashMap<>());
        assertNull("Should return null for null price variation type", result);
    }

    @Test
    public void should_ReturnNull_When_PriceVariationTypeIsUnavailable() {
        PriceVariation priceVariation = new PriceVariation();
        priceVariation.setType("UNAVAILABLE");
        
        PriceGraphInfo result = transformer.getPriceGraphInfo(priceVariation, "INR", new HashMap<>());
        assertNull("Should return null for unavailable price variation type", result);
    }

    @Test
    public void should_BuildPriceGraphInfo_When_ValidTypicalPriceVariation() {
        PriceVariation priceVariation = new PriceVariation();
        priceVariation.setType("TYPICAL");
        priceVariation.setPercentage(10);
        priceVariation.setVariationType(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.TYPICAL);
        
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put("PRICE_VARIATION_V2", "false");
        
        PriceGraphInfo result = transformer.getPriceGraphInfo(priceVariation, "INR", expDataMap);
        
        assertNotNull("Should return price graph info for typical variation", result);
    }

    @Test
    public void should_BuildPriceGraphInfo_When_ValidDropPriceVariation() {
        PriceVariation priceVariation = new PriceVariation();
        priceVariation.setType("DROP");
        priceVariation.setPercentage(15);
        priceVariation.setVariationType(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.DROP);
        
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        
        PriceGraphInfo result = transformer.getPriceGraphInfo(priceVariation, "INR", expDataMap);
        
        assertNotNull("Should return price graph info for drop variation", result);
    }

    @Test
    public void should_BuildPriceGraphInfo_When_ValidSurgePriceVariation() {
        PriceVariation priceVariation = new PriceVariation();
        priceVariation.setType("SURGE");
        priceVariation.setPercentage(25);
        priceVariation.setVariationType(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.SURGE);
        
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        
        PriceGraphInfo result = transformer.getPriceGraphInfo(priceVariation, "INR", expDataMap);
        
        assertNotNull("Should return price graph info for surge variation", result);
    }

    @Test
    public void should_HandleUnknownPriceVariationType_When_InvalidTypeProvided() {
        PriceVariation priceVariation = new PriceVariation();
        priceVariation.setType("UNKNOWN_TYPE");
        priceVariation.setPercentage(5);
        priceVariation.setVariationType(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.UNAVAILABLE);
        
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        
        PriceGraphInfo result = transformer.getPriceGraphInfo(priceVariation, "INR", expDataMap);
        
        assertNotNull("Should handle unknown price variation type", result);
    }

    @Test
    public void should_UsePriceVariationV2_When_V2ExperimentEnabled() {
        PriceVariation priceVariation = new PriceVariation();
        priceVariation.setType("TYPICAL");
        priceVariation.setPercentage(10);
        priceVariation.setVariationType(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.TYPICAL);
        
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put("PRICE_VARIATION_V2", "true");
        
        when(utility.isPriceVariationV2Enabled(expDataMap)).thenReturn(true);
        
        PriceGraphInfo result = transformer.getPriceGraphInfo(priceVariation, "INR", expDataMap);
        
        assertNotNull("Should return price graph info with V2 enabled", result);
        verify(utility).isPriceVariationV2Enabled(expDataMap);
    }

    @Test
    public void should_HandleNullCurrencyInPriceGraph_When_CurrencyIsNull() {
        PriceVariation priceVariation = new PriceVariation();
        priceVariation.setType("TYPICAL");
        priceVariation.setPercentage(10);
        priceVariation.setVariationType(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.TYPICAL);

        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        
        PriceGraphInfo result = transformer.getPriceGraphInfo(priceVariation, null, expDataMap);
        
        assertNotNull("Should handle null currency in price graph", result);
    }

    @Test
    public void should_HandleEmptyCurrencyInPriceGraph_When_CurrencyIsEmpty() {
        PriceVariation priceVariation = new PriceVariation();
        priceVariation.setType("DROP");
        priceVariation.setPercentage(15);
        priceVariation.setVariationType(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.DROP);
        
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        
        PriceGraphInfo result = transformer.getPriceGraphInfo(priceVariation, "", expDataMap);
        
        assertNotNull("Should handle empty currency in price graph", result);
    }

    @Test
    public void should_HandleNullExperimentDataInPriceGraph_When_ExperimentDataIsNull() {
        PriceVariation priceVariation = new PriceVariation();
        priceVariation.setType("SURGE");
        priceVariation.setPercentage(20);
        priceVariation.setVariationType(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.SURGE);
        
        PriceGraphInfo result = transformer.getPriceGraphInfo(priceVariation, "INR", null);
        
        assertNotNull("Should handle null experiment data in price graph", result);
    }

    // ==================== EDGE CASES AND NULL HANDLING ====================

    @Test
    public void should_HandleNullSearchRoomsRequest_When_SearchRoomsRequestIsNull() {
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
                new SearchRoomsRequest(), hotelDetailsResponseExact, "expData",
                Collections.emptyList(), searchCriteria, Collections.emptyList(),
                "expVariantKeys", requestDetails, commonModifierResponse);

        assertNotNull("SearchRoomsResponse should not be null", result);
    }

    @Test
    public void should_HandleNullExpData_When_ExpDataIsNull() {
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
                searchRoomsRequest, hotelDetailsResponseExact, null,
                Collections.emptyList(), searchCriteria, Collections.emptyList(),
                "expVariantKeys", requestDetails, commonModifierResponse);

        assertNotNull("SearchRoomsResponse should not be null", result);
    }

    @Test
    public void should_HandleEmptyExpData_When_ExpDataIsEmpty() {
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
                searchRoomsRequest, hotelDetailsResponseExact, "",
                Collections.emptyList(), searchCriteria, Collections.emptyList(),
                "expVariantKeys", requestDetails, commonModifierResponse);

        assertNotNull("SearchRoomsResponse should not be null", result);
    }

    @Test
    public void should_HandleNullRoomStayCandidates_When_RoomStayCandidatesIsNull() {
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
                searchRoomsRequest, hotelDetailsResponseExact, "expData",
                null, searchCriteria, Collections.emptyList(),
                "expVariantKeys", requestDetails, commonModifierResponse);

        assertNotNull("SearchRoomsResponse should not be null", result);
    }

    @Test
    public void should_HandleNullFilterCriteria_When_FilterCriteriaIsNull() {
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
                searchRoomsRequest, hotelDetailsResponseExact, "expData",
                Collections.emptyList(), searchCriteria, null,
                "expVariantKeys", requestDetails, commonModifierResponse);

        assertNotNull("SearchRoomsResponse should not be null", result);
    }

    @Test
    public void should_HandleNullExpVariantKeys_When_ExpVariantKeysIsNull() {
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
                searchRoomsRequest, hotelDetailsResponseExact, "expData",
                Collections.emptyList(), searchCriteria, Collections.emptyList(),
                null, requestDetails, commonModifierResponse);

        assertNotNull("SearchRoomsResponse should not be null", result);
    }

    @Test
    public void should_HandleBlankExpVariantKeys_When_ExpVariantKeysIsBlank() {
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
                searchRoomsRequest, hotelDetailsResponseExact, "expData",
                Collections.emptyList(), searchCriteria, Collections.emptyList(),
                "", requestDetails, commonModifierResponse);

        assertNotNull("SearchRoomsResponse should not be null", result);
    }

    @Test
    public void should_HandleValidExpVariantKeys_When_ExpVariantKeysProvided() {
        SearchRoomsResponse result = transformer.convertSearchRoomsResponse(
                searchRoomsRequest, hotelDetailsResponseExact, "expData",
                Collections.emptyList(), searchCriteria, Collections.emptyList(),
                "variant1,variant2,variant3", requestDetails, commonModifierResponse);

        assertNotNull("SearchRoomsResponse should not be null", result);
        verify(utility, atLeastOnce()).isExperimentTrue(any(LinkedHashMap.class), anyString());
    }

    // ========== TARIFF VIEW TYPE TESTS (getTariffViewType method) ==========

    @Test
    public void should_ReturnTariffViewTypeWithInitialVisible1_When_SingleRatePlan() throws Exception {
        // Given
        RoomDetails roomDetails = createRoomDetailsWithRatePlans(1);
        Integer starRating = 4;
        boolean isFirstRoom = true;
        Map<String, String> expDataMap = new HashMap<>();

        // When
        TariffViewType result = invokeTariffViewType(roomDetails, starRating, isFirstRoom, expDataMap);

        // Then
        assertNotNull("TariffViewType should not be null", result);
        assertEquals("Total tariffs should be 1", 1, result.getTotalTariffs());
        assertEquals("Initial visible should be 1 for single rate plan", 1, result.getInitialVisible());
        assertNotNull("Base tariff text should not be null", result.getBaseTariffText());
    }

    @Test
    public void should_ReturnTariffViewTypeWithPremiumLogic_When_PremiumSRTrueAndRoomCountSRTrue() throws Exception {
        // Given
        RoomDetails roomDetails = createRoomDetailsWithRatePlans(5);
        Integer starRating = 4;
        boolean isFirstRoom = true;
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put("Premium_SR", "true");
        expDataMap.put("Room_Count_SR", "true");

        // Setup rate plan display logic
        Map<String, Map<String, Map<String, Integer>>> ratePlanDisplayLogic = createRatePlanDisplayLogic();
        ReflectionTestUtils.setField(transformer, "ratePlanDisplayLogic", ratePlanDisplayLogic);

        // When
        TariffViewType result = invokeTariffViewType(roomDetails, starRating, isFirstRoom, expDataMap);

        // Then
        assertNotNull("TariffViewType should not be null", result);
        assertEquals("Total tariffs should be 5", 5, result.getTotalTariffs());
        assertTrue("Initial visible should be set from premium logic", result.getInitialVisible() > 0);
    }

    @Test
    public void should_ReturnTariffViewTypeWithPremiumDefaultLogic_When_PremiumSRTrueAndRoomCountSRFalse() throws Exception {
        // Given
        RoomDetails roomDetails = createRoomDetailsWithRatePlans(3);
        Integer starRating = 5;
        boolean isFirstRoom = false;
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put("Premium_SR", "true");
        expDataMap.put("Room_Count_SR", "false");

        // Setup rate plan display logic
        Map<String, Map<String, Map<String, Integer>>> ratePlanDisplayLogic = createRatePlanDisplayLogic();
        ReflectionTestUtils.setField(transformer, "ratePlanDisplayLogic", ratePlanDisplayLogic);

        // When
        TariffViewType result = invokeTariffViewType(roomDetails, starRating, isFirstRoom, expDataMap);

        // Then
        assertNotNull("TariffViewType should not be null", result);
        assertEquals("Total tariffs should be 3", 3, result.getTotalTariffs());
        assertTrue("Initial visible should be set from premium default logic", result.getInitialVisible() > 0);
    }

    @Test
    public void should_ReturnTariffViewTypeWithBudgetLogic_When_PremiumSRFalseAndRoomCountSRTrue() throws Exception {
        // Given
        RoomDetails roomDetails = createRoomDetailsWithRatePlans(4);
        Integer starRating = 3;
        boolean isFirstRoom = true;
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put("Premium_SR", "false");
        expDataMap.put("Room_Count_SR", "true");

        // Setup rate plan display logic
        Map<String, Map<String, Map<String, Integer>>> ratePlanDisplayLogic = createRatePlanDisplayLogic();
        ReflectionTestUtils.setField(transformer, "ratePlanDisplayLogic", ratePlanDisplayLogic);

        // When
        TariffViewType result = invokeTariffViewType(roomDetails, starRating, isFirstRoom, expDataMap);

        // Then
        assertNotNull("TariffViewType should not be null", result);
        assertEquals("Total tariffs should be 4", 4, result.getTotalTariffs());
        assertTrue("Initial visible should be set from budget logic", result.getInitialVisible() > 0);
    }

    @Test
    public void should_ReturnTariffViewTypeWithBudgetDefaultLogic_When_PremiumSRFalseAndRoomCountSRFalse() throws Exception {
        // Given
        RoomDetails roomDetails = createRoomDetailsWithRatePlans(2);
        Integer starRating = 2;
        boolean isFirstRoom = false;
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put("Premium_SR", "false");
        expDataMap.put("Room_Count_SR", "false");

        // Setup rate plan display logic
        Map<String, Map<String, Map<String, Integer>>> ratePlanDisplayLogic = createRatePlanDisplayLogic();
        ReflectionTestUtils.setField(transformer, "ratePlanDisplayLogic", ratePlanDisplayLogic);

        // When
        TariffViewType result = invokeTariffViewType(roomDetails, starRating, isFirstRoom, expDataMap);

        // Then
        assertNotNull("TariffViewType should not be null", result);
        assertEquals("Total tariffs should be 2", 2, result.getTotalTariffs());
        assertTrue("Initial visible should be set from budget default logic", result.getInitialVisible() > 0);
    }

    @Test
    public void should_ReturnTariffViewTypeWithPremiumStarRatingLessThan4_When_PremiumSRTrueButLowStarRating() throws Exception {
        // Given
        RoomDetails roomDetails = createRoomDetailsWithRatePlans(3);
        Integer starRating = 3; // Less than 4
        boolean isFirstRoom = true;
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put("Premium_SR", "true");
        expDataMap.put("Room_Count_SR", "true");

        // Setup rate plan display logic
        Map<String, Map<String, Map<String, Integer>>> ratePlanDisplayLogic = createRatePlanDisplayLogic();
        ReflectionTestUtils.setField(transformer, "ratePlanDisplayLogic", ratePlanDisplayLogic);

        // When
        TariffViewType result = invokeTariffViewType(roomDetails, starRating, isFirstRoom, expDataMap);

        // Then
        assertNotNull("TariffViewType should not be null", result);
        assertEquals("Total tariffs should be 3", 3, result.getTotalTariffs());
        assertTrue("Initial visible should be set from budget logic due to low star rating", result.getInitialVisible() > 0);
    }

    @Test
    public void should_ReturnTariffViewTypeWithDefaultLimit_When_NoExperimentData() throws Exception {
        // Given
        RoomDetails roomDetails = createRoomDetailsWithRatePlans(6);
        Integer starRating = 4;
        boolean isFirstRoom = true;
        Map<String, String> expDataMap = new HashMap<>(); // Empty experiment data

        // Setup rate plan display logic but empty exp data should fall to default
        Map<String, Map<String, Map<String, Integer>>> ratePlanDisplayLogic = createRatePlanDisplayLogic();
        ReflectionTestUtils.setField(transformer, "ratePlanDisplayLogic", ratePlanDisplayLogic);
        ReflectionTestUtils.setField(transformer, "ratePlanMoreOptionsLimit", 2);

        // When
        TariffViewType result = invokeTariffViewType(roomDetails, starRating, isFirstRoom, expDataMap);

        // Then
        assertNotNull("TariffViewType should not be null", result);
        assertEquals("Total tariffs should be 6", 6, result.getTotalTariffs());
        assertEquals("Initial visible should be default limit", 2, result.getInitialVisible());
    }

    @Test
    public void should_ReturnTariffViewTypeWithDefaultLimit_When_NoRatePlanDisplayLogic() throws Exception {
        // Given
        RoomDetails roomDetails = createRoomDetailsWithRatePlans(4);
        Integer starRating = 4;
        boolean isFirstRoom = true;
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put("Premium_SR", "true");
        expDataMap.put("Room_Count_SR", "true");

        // No rate plan display logic set
        ReflectionTestUtils.setField(transformer, "ratePlanDisplayLogic", null);
        ReflectionTestUtils.setField(transformer, "ratePlanMoreOptionsLimit", 3);

        // When
        TariffViewType result = invokeTariffViewType(roomDetails, starRating, isFirstRoom, expDataMap);

        // Then
        assertNotNull("TariffViewType should not be null", result);
        assertEquals("Total tariffs should be 4", 4, result.getTotalTariffs());
        assertEquals("Initial visible should be default limit", 3, result.getInitialVisible());
    }

    @Test
    public void should_CorrectInitialVisible_When_InitialVisibleGreaterThanTotalTariffs() throws Exception {
        // Given
        RoomDetails roomDetails = createRoomDetailsWithRatePlans(2);
        Integer starRating = 4;
        boolean isFirstRoom = true;
        Map<String, String> expDataMap = new HashMap<>();

        // Set a high default limit that exceeds total tariffs
        ReflectionTestUtils.setField(transformer, "ratePlanMoreOptionsLimit", 5);

        // When
        TariffViewType result = invokeTariffViewType(roomDetails, starRating, isFirstRoom, expDataMap);

        // Then
        assertNotNull("TariffViewType should not be null", result);
        assertEquals("Total tariffs should be 2", 2, result.getTotalTariffs());
        assertEquals("Initial visible should be corrected to total tariffs", 2, result.getInitialVisible());
    }

    @Test
    public void should_ReturnTariffViewTypeWithNullStarRating_When_StarRatingIsNull() throws Exception {
        // Given
        RoomDetails roomDetails = createRoomDetailsWithRatePlans(3);
        Integer starRating = null;
        boolean isFirstRoom = true;
        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put("Premium_SR", "true");
        expDataMap.put("Room_Count_SR", "true");

        // Setup rate plan display logic
        Map<String, Map<String, Map<String, Integer>>> ratePlanDisplayLogic = createRatePlanDisplayLogic();
        ReflectionTestUtils.setField(transformer, "ratePlanDisplayLogic", ratePlanDisplayLogic);
        ReflectionTestUtils.setField(transformer, "ratePlanMoreOptionsLimit", 2);

        // When
        TariffViewType result = invokeTariffViewType(roomDetails, starRating, isFirstRoom, expDataMap);

        // Then
        assertNotNull("TariffViewType should not be null", result);
        assertEquals("Total tariffs should be 3", 3, result.getTotalTariffs());
        assertEquals("Initial visible should be default limit when star rating is null", 2, result.getInitialVisible());
    }

    @Test
    public void should_HandleEmptyExpDataMap_When_ExpDataMapIsEmpty() throws Exception {
        // Given
        RoomDetails roomDetails = createRoomDetailsWithRatePlans(4);
        Integer starRating = 4;
        boolean isFirstRoom = true;
        Map<String, String> expDataMap = Collections.emptyMap();

        // Setup rate plan display logic
        Map<String, Map<String, Map<String, Integer>>> ratePlanDisplayLogic = createRatePlanDisplayLogic();
        ReflectionTestUtils.setField(transformer, "ratePlanDisplayLogic", ratePlanDisplayLogic);
        ReflectionTestUtils.setField(transformer, "ratePlanMoreOptionsLimit", 1);

        // When
        TariffViewType result = invokeTariffViewType(roomDetails, starRating, isFirstRoom, expDataMap);

        // Then
        assertNotNull("TariffViewType should not be null", result);
        assertEquals("Total tariffs should be 4", 4, result.getTotalTariffs());
        assertEquals("Initial visible should be default limit", 1, result.getInitialVisible());
    }

    @Test
    public void should_SetBaseTariffText_When_PolyglotServiceCalled() throws Exception {
        // Given
        RoomDetails roomDetails = createRoomDetailsWithRatePlans(2);
        Integer starRating = 4;
        boolean isFirstRoom = true;
        Map<String, String> expDataMap = new HashMap<>();

        String expectedText = "Starting from";
        when(polyglotService.getTranslatedData(anyString())).thenReturn(expectedText);

        // When
        TariffViewType result = invokeTariffViewType(roomDetails, starRating, isFirstRoom, expDataMap);

        // Then
        assertNotNull("TariffViewType should not be null", result);
        assertEquals("Base tariff text should be set from polyglot service", expectedText, result.getBaseTariffText());
        verify(polyglotService).getTranslatedData(anyString());
    }

    // ========== HELPER METHODS FOR TARIFF VIEW TYPE TESTS ==========

    /**
     * Helper method to invoke the private getTariffViewType method using reflection
     */
    private TariffViewType invokeTariffViewType(RoomDetails roomDetails, Integer starRating,
                                                boolean isFirstRoom, Map<String, String> expDataMap) throws Exception {
        Method getTariffViewTypeMethod = transformer.getClass().getSuperclass()
                .getDeclaredMethod("getTariffViewType", RoomDetails.class, Integer.class, boolean.class, Map.class);
        getTariffViewTypeMethod.setAccessible(true);
        return (TariffViewType) getTariffViewTypeMethod.invoke(transformer, roomDetails, starRating, isFirstRoom, expDataMap);
    }

    /**
     * Helper method to create RoomDetails with specified number of rate plans
     */
    private RoomDetails createRoomDetailsWithRatePlans(int ratePlanCount) {
        RoomDetails roomDetails = new RoomDetails();
        List<SelectRoomRatePlan> ratePlans = new ArrayList<>();

        for (int i = 0; i < ratePlanCount; i++) {
            SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
            ratePlan.setName("Rate Plan " + (i + 1));
            ratePlan.setRpc("RPC_" + (i + 1));
            ratePlans.add(ratePlan);
        }

        roomDetails.setRatePlans(ratePlans);
        return roomDetails;
    }

    /**
     * Helper method to create rate plan display logic configuration
     */
    private Map<String, Map<String, Map<String, Integer>>> createRatePlanDisplayLogic() {
        Map<String, Map<String, Map<String, Integer>>> ratePlanDisplayLogic = new HashMap<>();

        // Premium configuration
        Map<String, Map<String, Integer>> premiumConfig = new HashMap<>();
        Map<String, Integer> premiumDefault = new HashMap<>();
        premiumDefault.put("FIRST_ROOM_RPC_COUNT", 3);
        premiumDefault.put("OTHER_ROOM_RPC_COUNT", 2);
        premiumConfig.put("DEFAULT", premiumDefault);

        Map<String, Integer> premium5 = new HashMap<>();
        premium5.put("FIRST_ROOM_RPC_COUNT", 4);
        premium5.put("OTHER_ROOM_RPC_COUNT", 3);
        premiumConfig.put("5", premium5);

        ratePlanDisplayLogic.put("PREMIUM", premiumConfig);

        // Budget configuration
        Map<String, Map<String, Integer>> budgetConfig = new HashMap<>();
        Map<String, Integer> budgetDefault = new HashMap<>();
        budgetDefault.put("FIRST_ROOM_RPC_COUNT", 2);
        budgetDefault.put("OTHER_ROOM_RPC_COUNT", 1);
        budgetConfig.put("DEFAULT", budgetDefault);

        Map<String, Integer> budget4 = new HashMap<>();
        budget4.put("FIRST_ROOM_RPC_COUNT", 3);
        budget4.put("OTHER_ROOM_RPC_COUNT", 2);
        budgetConfig.put("4", budget4);

        ratePlanDisplayLogic.put("BUDGET", budgetConfig);

        return ratePlanDisplayLogic;
    }

    @Test
    public void testConvertCardDataToCardInfoMapForLPG() {
        // Setup
        MobConfigHelper mobConfigHelper = new MobConfigHelper();
        Map<String, CardData> cardData = new HashMap<>();
        CardData data = new CardData();
        data.setCardSubType("GCCCARD");
        data.setCardId("LPG");
        data.setTitleText("Lowest Price Guarantee");
        data.setSubText("Get 2X the difference if you find our MMT exclusive hotels cheaper online anywhere else");
        data.setIconUrl("https://promos.makemytrip.com/GCC/MiscIcons/MMTexclusivetagborder.png");
        data.setBgColor("#FDF7E9");

        // Setup CardAction
        CardData.CardAction cardAction = new CardData.CardAction();
        cardAction.setTitle("View T&Cs");
        cardAction.setWebViewUrl("https://promos.makemytrip.com/gcc-mmt-exclusive-lpg-terms.html");
        data.setCardAction(Collections.singletonList(cardAction));
        data.setTemplateId("exclusive_v2");
        cardData.put("key", data);

        Map<String, CardInfo> cardInfoMap = ReflectionTestUtils.invokeMethod(transformer, "buildCardMap", cardData);

        // Verify
        Assert.assertNotNull(cardInfoMap);
        Assert.assertEquals(1, cardInfoMap.size());
        CardInfo cardInfo = cardInfoMap.get("key");
        Assert.assertNotNull(cardInfo);
        Assert.assertEquals("GCCCARD", cardInfo.getSubType());
        Assert.assertEquals("LPG", cardInfo.getId());
        Assert.assertEquals("Lowest Price Guarantee", cardInfo.getTitleText());
        Assert.assertEquals("Get 2X the difference if you find our MMT exclusive hotels cheaper online anywhere else", cardInfo.getSubText());
        Assert.assertEquals("https://promos.makemytrip.com/GCC/MiscIcons/MMTexclusivetagborder.png", cardInfo.getIconURL());
        Assert.assertEquals("#FDF7E9", cardInfo.getBgColor());
        Assert.assertEquals("exclusive_v2", cardInfo.getTemplateId());

        // Verify CardAction
        Assert.assertNotNull(cardInfo.getCardAction());
        Assert.assertEquals(1, cardInfo.getCardAction().size());
        CardAction action = cardInfo.getCardAction().get(0);
        Assert.assertEquals("View T&Cs", action.getTitle());
        Assert.assertEquals("https://promos.makemytrip.com/gcc-mmt-exclusive-lpg-terms.html", action.getWebViewUrl());
    }

    @Test
    public void testBuildPaymentCard_WithNullRequestDetails_ShouldReturnNull() {
        // Setup - null requestDetails
        RequestDetails requestDetails = null;
        HotelDetails hotelDetails = new HotelDetails();

        // Execute
        com.mmt.hotels.clientgateway.response.Card result = ReflectionTestUtils.invokeMethod(
                transformer, "buildPaymentCard", requestDetails, hotelDetails);

        // Verify
        Assert.assertNull("Should return null when requestDetails is null", result);
    }

    @Test
    public void testBuildPaymentCard_WithNonGroupBookingFunnel_ShouldReturnNull() {
        // Setup - non-group booking funnel
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource("HOTELS"); // Not group booking
        HotelDetails hotelDetails = new HotelDetails();

        // Execute
        com.mmt.hotels.clientgateway.response.Card result = ReflectionTestUtils.invokeMethod(
                transformer, "buildPaymentCard", requestDetails, hotelDetails);

        // Verify
        Assert.assertNull("Should return null when not group booking funnel", result);
    }

    @Test
    public void testBuildPaymentCard_WithEmptyRoomCombos_ShouldReturnNull() {
        // Setup - empty room combos
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource("GROUP"); // Group booking
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRoomCombos(new ArrayList<>()); // Empty list

        // Execute
        com.mmt.hotels.clientgateway.response.Card result = ReflectionTestUtils.invokeMethod(
                transformer, "buildPaymentCard", requestDetails, hotelDetails);

        // Verify
        Assert.assertNull("Should return null when room combos is empty", result);
    }

    @Test
    public void testBuildPaymentCard_WithNullRoomCombos_ShouldReturnNull() {
        // Setup - null room combos
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource("GROUP"); // Group booking
        HotelDetails hotelDetails = new HotelDetails();
        hotelDetails.setRoomCombos(null); // Null list

        // Execute
        com.mmt.hotels.clientgateway.response.Card result = ReflectionTestUtils.invokeMethod(
                transformer, "buildPaymentCard", requestDetails, hotelDetails);

        // Verify
        Assert.assertNull("Should return null when room combos is null", result);
    }

    @Test
    public void testBuildPaymentCard_WithNoRecommendedRoom_ShouldReturnNull() {
        // Setup - no recommended room combo
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource("GROUP"); // Group booking
        HotelDetails hotelDetails = new HotelDetails();

        List<com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo> roomCombos = new ArrayList<>();
        com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo roomCombo = new com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo();
        roomCombo.setComboType(ComboType.EXACT_MATCH); // Not RECOMMENDED_ROOM
        roomCombos.add(roomCombo);
        hotelDetails.setRoomCombos(roomCombos);

        // Execute
        com.mmt.hotels.clientgateway.response.Card result = ReflectionTestUtils.invokeMethod(
                transformer, "buildPaymentCard", requestDetails, hotelDetails);

        // Verify
        Assert.assertNull("Should return null when no recommended room combo found", result);
    }

    @Test
    public void testBuildPaymentCard_WithNullPaymentPlan_ShouldReturnNull() {
        // Setup - null payment plan
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource("GROUP"); // Group booking
        HotelDetails hotelDetails = new HotelDetails();

        List<com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo> roomCombos = new ArrayList<>();
        com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo roomCombo = new com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo();
        roomCombo.setComboType(ComboType.RECOMMENDED_ROOM);
        roomCombo.setPaymentPlan(null); // Null payment plan
        roomCombos.add(roomCombo);
        hotelDetails.setRoomCombos(roomCombos);

        // Execute
        com.mmt.hotels.clientgateway.response.Card result = ReflectionTestUtils.invokeMethod(
                transformer, "buildPaymentCard", requestDetails, hotelDetails);

        // Verify
        Assert.assertNull("Should return null when payment plan is null", result);
    }

    @Test
    public void testBuildPaymentCard_WithEmptyPaymentCardText_ShouldReturnNull() {
        // Setup - empty payment card text
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource("GROUP"); // Group booking
        HotelDetails hotelDetails = new HotelDetails();

        List<com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo> roomCombos = new ArrayList<>();
        com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo roomCombo = new com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo();
        roomCombo.setComboType(ComboType.RECOMMENDED_ROOM);

        PaymentPlan paymentPlan = new PaymentPlan();
        paymentPlan.setPaymentCardText(""); // Empty text
        roomCombo.setPaymentPlan(paymentPlan);
        roomCombos.add(roomCombo);
        hotelDetails.setRoomCombos(roomCombos);

        // Execute
        com.mmt.hotels.clientgateway.response.Card result = ReflectionTestUtils.invokeMethod(
                transformer, "buildPaymentCard", requestDetails, hotelDetails);

        // Verify
        Assert.assertNull("Should return null when payment card text is empty", result);
    }

    @Test
    public void testBuildPaymentCard_WithNullPaymentCardText_ShouldReturnNull() {
        // Setup - null payment card text
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource("GROUP"); // Group booking
        HotelDetails hotelDetails = new HotelDetails();

        List<com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo> roomCombos = new ArrayList<>();
        com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo roomCombo = new com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo();
        roomCombo.setComboType(ComboType.RECOMMENDED_ROOM);

        PaymentPlan paymentPlan = new PaymentPlan();
        paymentPlan.setPaymentCardText(null); // Null text
        roomCombo.setPaymentPlan(paymentPlan);
        roomCombos.add(roomCombo);
        hotelDetails.setRoomCombos(roomCombos);

        // Execute
        com.mmt.hotels.clientgateway.response.Card result = ReflectionTestUtils.invokeMethod(
                transformer, "buildPaymentCard", requestDetails, hotelDetails);

        // Verify
        Assert.assertNull("Should return null when payment card text is null", result);
    }

    @Test
    public void testBuildPaymentCard_WithValidData_ShouldReturnCard() {
        // Setup - valid data
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource("GROUP"); // Group booking
        HotelDetails hotelDetails = new HotelDetails();

        List<com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo> roomCombos = new ArrayList<>();
        com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo roomCombo = new com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo();
        roomCombo.setComboType(ComboType.RECOMMENDED_ROOM);

        PaymentPlan paymentPlan = new PaymentPlan();
        paymentPlan.setPaymentCardText("Pay 50% now, 50% later"); // Valid text
        roomCombo.setPaymentPlan(paymentPlan);
        roomCombos.add(roomCombo);
        hotelDetails.setRoomCombos(roomCombos);

        // Mock polyglot service
        when(polyglotService.getTranslatedData("KNOW_MORE")).thenReturn("Know More");

        // Execute
        com.mmt.hotels.clientgateway.response.Card result = ReflectionTestUtils.invokeMethod(
                transformer, "buildPaymentCard", requestDetails, hotelDetails);

        // Verify
        Assert.assertNotNull("Should return Card when valid data is provided", result);
        Assert.assertEquals("Pay 50% now, 50% later", result.getTitle());
        Assert.assertEquals("Know More", result.getCta());
        Assert.assertEquals("https://promos.makemytrip.com/Hotels_product/group/ic_rupee_2x.png", result.getIconUrl());
    }

    @Test
    public void testBuildPaymentCard_WithMultipleRoomCombos_ShouldReturnFirstRecommended() {
        // Setup - multiple room combos with first recommended
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setFunnelSource("GROUP"); // Group booking
        HotelDetails hotelDetails = new HotelDetails();

        List<com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo> roomCombos = new ArrayList<>();

        // First combo - not recommended
        com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo roomCombo1 = new com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo();
        roomCombo1.setComboType(ComboType.EXACT_MATCH);
        PaymentPlan paymentPlan1 = new PaymentPlan();
        paymentPlan1.setPaymentCardText("Should not be used");
        roomCombo1.setPaymentPlan(paymentPlan1);
        roomCombos.add(roomCombo1);

        // Second combo - recommended
        com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo roomCombo2 = new com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo();
        roomCombo2.setComboType(ComboType.RECOMMENDED_ROOM);
        PaymentPlan paymentPlan2 = new PaymentPlan();
        paymentPlan2.setPaymentCardText("First recommended text");
        roomCombo2.setPaymentPlan(paymentPlan2);
        roomCombos.add(roomCombo2);

        // Third combo - also recommended
        com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo roomCombo3 = new com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo();
        roomCombo3.setComboType(ComboType.RECOMMENDED_ROOM);
        PaymentPlan paymentPlan3 = new PaymentPlan();
        paymentPlan3.setPaymentCardText("Second recommended text");
        roomCombo3.setPaymentPlan(paymentPlan3);
        roomCombos.add(roomCombo3);

        hotelDetails.setRoomCombos(roomCombos);

        // Mock polyglot service
        when(polyglotService.getTranslatedData("KNOW_MORE")).thenReturn("Know More");

        // Execute
        com.mmt.hotels.clientgateway.response.Card result = ReflectionTestUtils.invokeMethod(
                transformer, "buildPaymentCard", requestDetails, hotelDetails);

        // Verify - should use first recommended combo
        Assert.assertNotNull("Should return Card when valid data is provided", result);
        Assert.assertEquals("First recommended text", result.getTitle());
        Assert.assertEquals("Know More", result.getCta());
        Assert.assertEquals("https://promos.makemytrip.com/Hotels_product/group/ic_rupee_2x.png", result.getIconUrl());
    }

    @Test
    public void testBuildPaymentPlan_WithNullPaymentPlan_ShouldReturnNull() {
        // Setup - null payment plan
        PaymentPlan paymentPlan = null;

        // Execute
        com.mmt.hotels.clientgateway.response.rooms.PaymentPlan result = transformer.buildPaymentPlan(paymentPlan);

        // Verify
        Assert.assertNull("Should return null when payment plan is null", result);
    }

    @Test
    public void testBuildPaymentPlan_WithZeroAmount_ShouldReturnNull() {
        // Setup - zero amount
        PaymentPlan paymentPlan = new PaymentPlan();
        paymentPlan.setAmount(0.0);
        paymentPlan.setText("Test text");

        // Execute
        com.mmt.hotels.clientgateway.response.rooms.PaymentPlan result = transformer.buildPaymentPlan(paymentPlan);

        // Verify
        Assert.assertNull("Should return null when amount is zero", result);
    }

    @Test
    public void testBuildPaymentPlan_WithValidAmountButNoPolicy_ShouldReturnBasicPlan() {
        // Setup - valid amount but no payment policy
        PaymentPlan paymentPlan = new PaymentPlan();
        paymentPlan.setAmount(1000.0);
        paymentPlan.setText("Pay in installments");
        paymentPlan.setPaymentPolicy(null); // No policy

        // Execute
        com.mmt.hotels.clientgateway.response.rooms.PaymentPlan result = transformer.buildPaymentPlan(paymentPlan);

        // Verify
        Assert.assertNotNull("Should return PaymentPlan when amount is valid", result);
        Assert.assertEquals("Pay in installments", result.getText());
        Assert.assertEquals(1000.0, result.getAmount(), 0.001);
        Assert.assertNull("Payment policy should be null", result.getPaymentPolicy());
        Assert.assertNull("Penalty text should be null", result.getPenaltyText());
    }

    @Test
    public void testBuildPaymentPlan_WithEmptyPaymentPolicy_ShouldReturnBasicPlan() {
        // Setup - valid amount but empty payment policy
        PaymentPlan paymentPlan = new PaymentPlan();
        paymentPlan.setAmount(1500.0);
        paymentPlan.setText("Flexible payment");
        paymentPlan.setPaymentPolicy(new ArrayList<>()); // Empty policy list

        // Execute
        com.mmt.hotels.clientgateway.response.rooms.PaymentPlan result = transformer.buildPaymentPlan(paymentPlan);

        // Verify
        Assert.assertNotNull("Should return PaymentPlan when amount is valid", result);
        Assert.assertEquals("Flexible payment", result.getText());
        Assert.assertEquals(1500.0, result.getAmount(), 0.001);
        Assert.assertNull("Payment policy should be null", result.getPaymentPolicy());
        Assert.assertNull("Penalty text should be null", result.getPenaltyText());
    }

    @Test
    public void testBuildPaymentPlan_WithValidPaymentPolicy_ShouldReturnCompletePlan() {
        // Setup - valid payment plan with policy
        PaymentPlan paymentPlan = new PaymentPlan();
        paymentPlan.setAmount(2000.0);
        paymentPlan.setText("Split payment plan");

        List<PaymentPlan> paymentPolicyList = new ArrayList<>();

        // First policy
        PaymentPlan policy1 = new PaymentPlan();
        policy1.setText("First installment");
        policy1.setAmount(800.0);
        policy1.setPaymentDateText("2024-01-15, Monday");
        paymentPolicyList.add(policy1);

        // Second policy
        PaymentPlan policy2 = new PaymentPlan();
        policy2.setText("Second installment");
        policy2.setAmount(1200.0);
        policy2.setPaymentDateText("2024-02-15, Thursday");
        paymentPolicyList.add(policy2);

        paymentPlan.setPaymentPolicy(paymentPolicyList);

        // Mock polyglot service
        when(polyglotService.getTranslatedData("PAYMENT_PLAN_PENALTY_TEXT")).thenReturn("Cancellation charges apply");

        // Execute
        com.mmt.hotels.clientgateway.response.rooms.PaymentPlan result = transformer.buildPaymentPlan(paymentPlan);

        // Verify
        Assert.assertNotNull("Should return PaymentPlan when payment policy is provided", result);
        Assert.assertEquals("Split payment plan", result.getText());
        Assert.assertEquals(2000.0, result.getAmount(), 0.001);
        Assert.assertEquals("Cancellation charges apply", result.getPenaltyText());

        // Verify payment policy
        Assert.assertNotNull("Payment policy should not be null", result.getPaymentPolicy());
        Assert.assertEquals(2, result.getPaymentPolicy().size());

        // Verify first policy
        com.mmt.hotels.clientgateway.response.rooms.PaymentPlan firstPolicy = result.getPaymentPolicy().get(0);
        Assert.assertEquals(1, firstPolicy.getSequence().intValue());
        Assert.assertEquals("First installment", firstPolicy.getText());
        Assert.assertEquals(800.0, firstPolicy.getAmount(), 0.001);
        Assert.assertEquals("2024-01-15", firstPolicy.getPaymentDate());

        // Verify second policy
        com.mmt.hotels.clientgateway.response.rooms.PaymentPlan secondPolicy = result.getPaymentPolicy().get(1);
        Assert.assertEquals(2, secondPolicy.getSequence().intValue());
        Assert.assertEquals("Second installment", secondPolicy.getText());
        Assert.assertEquals(1200.0, secondPolicy.getAmount(), 0.001);
        Assert.assertEquals("2024-02-15", secondPolicy.getPaymentDate());
    }

    @Test
    public void testBuildPaymentPlan_WithPaymentDateWithoutComma_ShouldReturnNull() {
        // Setup - payment date without comma
        PaymentPlan paymentPlan = new PaymentPlan();
        paymentPlan.setAmount(1000.0);
        paymentPlan.setText("Single payment");

        List<PaymentPlan> paymentPolicyList = new ArrayList<>();
        PaymentPlan policy = new PaymentPlan();
        policy.setText("Payment");
        policy.setAmount(1000.0);
        policy.setPaymentDateText("2024-01-15"); // No comma
        paymentPolicyList.add(policy);

        paymentPlan.setPaymentPolicy(paymentPolicyList);

        // Mock polyglot service
        when(polyglotService.getTranslatedData("PAYMENT_PLAN_PENALTY_TEXT")).thenReturn("Cancellation charges apply");

        // Execute
        com.mmt.hotels.clientgateway.response.rooms.PaymentPlan result = transformer.buildPaymentPlan(paymentPlan);

        // Verify
        Assert.assertNotNull("Should return PaymentPlan", result);
        Assert.assertNotNull("Payment policy should not be null", result.getPaymentPolicy());
        Assert.assertEquals(1, result.getPaymentPolicy().size());

        com.mmt.hotels.clientgateway.response.rooms.PaymentPlan policy1 = result.getPaymentPolicy().get(0);
        Assert.assertNull("Payment date should be null when no comma present", policy1.getPaymentDate());
    }

    @Test
    public void testBuildPaymentPlan_WithNullPaymentDateText_ShouldReturnNull() {
        // Setup - null payment date text
        PaymentPlan paymentPlan = new PaymentPlan();
        paymentPlan.setAmount(1000.0);
        paymentPlan.setText("Single payment");

        List<PaymentPlan> paymentPolicyList = new ArrayList<>();
        PaymentPlan policy = new PaymentPlan();
        policy.setText("Payment");
        policy.setAmount(1000.0);
        policy.setPaymentDateText(null); // Null date text
        paymentPolicyList.add(policy);

        paymentPlan.setPaymentPolicy(paymentPolicyList);

        // Mock polyglot service
        when(polyglotService.getTranslatedData("PAYMENT_PLAN_PENALTY_TEXT")).thenReturn("Cancellation charges apply");

        // Execute
        com.mmt.hotels.clientgateway.response.rooms.PaymentPlan result = transformer.buildPaymentPlan(paymentPlan);

        // Verify
        Assert.assertNotNull("Should return PaymentPlan", result);
        Assert.assertNotNull("Payment policy should not be null", result.getPaymentPolicy());
        Assert.assertEquals(1, result.getPaymentPolicy().size());

        com.mmt.hotels.clientgateway.response.rooms.PaymentPlan policy1 = result.getPaymentPolicy().get(0);
        Assert.assertNull("Payment date should be null when date text is null", policy1.getPaymentDate());
    }

    @Test
    public void testBuildPaymentPlan_WithComplexPaymentPolicySequence_ShouldMaintainOrder() {
        // Setup - multiple policies to test sequence handling
        PaymentPlan paymentPlan = new PaymentPlan();
        paymentPlan.setAmount(3000.0);
        paymentPlan.setText("Three-part payment plan");

        List<PaymentPlan> paymentPolicyList = new ArrayList<>();

        // Add three policies
        for (int i = 1; i <= 3; i++) {
            PaymentPlan policy = new PaymentPlan();
            policy.setText("Installment " + i);
            policy.setAmount(1000.0);
            policy.setPaymentDateText("2024-0" + i + "-15, Day" + i);
            paymentPolicyList.add(policy);
        }

        paymentPlan.setPaymentPolicy(paymentPolicyList);

        // Mock polyglot service
        when(polyglotService.getTranslatedData("PAYMENT_PLAN_PENALTY_TEXT")).thenReturn("Terms apply");

        // Execute
        com.mmt.hotels.clientgateway.response.rooms.PaymentPlan result = transformer.buildPaymentPlan(paymentPlan);

        // Verify sequence numbers are correctly assigned
        Assert.assertNotNull("Should return PaymentPlan", result);
        Assert.assertNotNull("Payment policy should not be null", result.getPaymentPolicy());
        Assert.assertEquals(3, result.getPaymentPolicy().size());

        for (int i = 0; i < 3; i++) {
            com.mmt.hotels.clientgateway.response.rooms.PaymentPlan policy = result.getPaymentPolicy().get(i);
            Assert.assertEquals("Sequence should be " + (i + 1), i + 1, policy.getSequence().intValue());
            Assert.assertEquals("Installment " + (i + 1), policy.getText());
            Assert.assertEquals("2024-0" + (i + 1) + "-15", policy.getPaymentDate());
        }
    }

} 