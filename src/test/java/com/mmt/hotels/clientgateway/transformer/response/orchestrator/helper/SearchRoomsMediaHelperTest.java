package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.content.Media;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.PanoramicMedia360;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.ProfessionalMediaEntity;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.response.rooms.MediaData;
import com.mmt.hotels.clientgateway.response.staticdetail.Image360.View360Image;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.response.staticdata.Image360.Image360;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SearchRoomsMediaHelperTest {

    @Mock
    private PolyglotService polyglotService;

    @InjectMocks
    private SearchRoomsMediaHelper searchRoomsMediaHelper;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(searchRoomsMediaHelper, "view360IconUrl", "https://example.com/360-icon.png");
    }

    @Test
    public void should_ExtractRoomImages_When_MediaHasValidProfessionalImages() {
        // Given
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMediaEntities = new HashMap<>();
        
        ProfessionalMediaEntity image1 = new ProfessionalMediaEntity();
        image1.setRoomCode("ROOM001");
        image1.setUrl("https://example.com/image1.jpg");
        
        List<ProfessionalMediaEntity> imageList = new ArrayList<>();
        imageList.add(image1);
        professionalMediaEntities.put("R", imageList);
        media.setProfessionalMediaEntities(professionalMediaEntities);

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/image1.jpg", result.get(0));
    }

    @Test
    public void should_ReturnEmptyList_When_MediaIsNull() {
        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(null, "ROOM001");

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnEmptyList_When_ProfessionalMediaEntitiesIsEmpty() {
        // Given
        Media media = new Media();
        media.setProfessionalMediaEntities(new HashMap<>());

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnEmptyList_When_ProfessionalMediaEntitiesIsNull() {
        // Given
        Media media = new Media();
        media.setProfessionalMediaEntities(null);

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnEmptyList_When_ProfessionalMediaEntitiesDoesNotContainRKey() {
        // Given
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMediaEntities = new HashMap<>();
        professionalMediaEntities.put("H", new ArrayList<>());  // Different key, not "R"
        media.setProfessionalMediaEntities(professionalMediaEntities);

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnEmptyList_When_ProfessionalImagesListIsEmpty() {
        // Given
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMediaEntities = new HashMap<>();
        professionalMediaEntities.put("R", new ArrayList<>());  // Empty list
        media.setProfessionalMediaEntities(professionalMediaEntities);

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnEmptyList_When_ProfessionalImagesListIsNull() {
        // Given
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMediaEntities = new HashMap<>();
        professionalMediaEntities.put("R", null);  // Null list
        media.setProfessionalMediaEntities(professionalMediaEntities);

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNull(result);
    }

    @Test
    public void should_FilterImagesByRoomCode_When_MultipleRoomCodesExist() {
        // Given
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMediaEntities = new HashMap<>();
        
        ProfessionalMediaEntity image1 = new ProfessionalMediaEntity();
        image1.setRoomCode("ROOM001");
        image1.setUrl("https://example.com/room001.jpg");
        
        ProfessionalMediaEntity image2 = new ProfessionalMediaEntity();
        image2.setRoomCode("ROOM002");
        image2.setUrl("https://example.com/room002.jpg");
        
        professionalMediaEntities.put("R", Arrays.asList(image1, image2));
        media.setProfessionalMediaEntities(professionalMediaEntities);

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/room001.jpg", result.get(0));
    }

    @Test
    public void should_SkipImagesWithBlankUrl_When_ExtractingRoomImages() {
        // Given
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMediaEntities = new HashMap<>();
        
        ProfessionalMediaEntity validImage = new ProfessionalMediaEntity();
        validImage.setRoomCode("ROOM001");
        validImage.setUrl("https://example.com/valid.jpg");
        
        ProfessionalMediaEntity invalidImage = new ProfessionalMediaEntity();
        invalidImage.setRoomCode("ROOM001");
        invalidImage.setUrl(""); // Blank URL
        
        professionalMediaEntities.put("R", Arrays.asList(validImage, invalidImage));
        media.setProfessionalMediaEntities(professionalMediaEntities);

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/valid.jpg", result.get(0));
    }

    @Test
    public void should_SkipImagesWithNullUrl_When_ExtractingRoomImages() {
        // Given
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMediaEntities = new HashMap<>();
        
        ProfessionalMediaEntity validImage = new ProfessionalMediaEntity();
        validImage.setRoomCode("ROOM001");
        validImage.setUrl("https://example.com/valid.jpg");
        
        ProfessionalMediaEntity invalidImage = new ProfessionalMediaEntity();
        invalidImage.setRoomCode("ROOM001");
        invalidImage.setUrl(null); // Null URL
        
        professionalMediaEntities.put("R", Arrays.asList(validImage, invalidImage));
        media.setProfessionalMediaEntities(professionalMediaEntities);

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/valid.jpg", result.get(0));
    }

    @Test
    public void should_AddHttpsPrefix_When_UrlDoesNotStartWithHttp() {
        // Given
        Media media = new Media();
        Map<String, List<ProfessionalMediaEntity>> professionalMediaEntities = new HashMap<>();
        
        ProfessionalMediaEntity image1 = new ProfessionalMediaEntity();
        image1.setRoomCode("ROOM001");
        image1.setUrl("//example.com/image1.jpg");
        
        professionalMediaEntities.put("R", Arrays.asList(image1));
        media.setProfessionalMediaEntities(professionalMediaEntities);

        // When
        List<String> result = searchRoomsMediaHelper.extractRoomImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/image1.jpg", result.get(0));
    }

    @Test
    public void should_Extract360Images_When_MediaHasValidPanoramicImages() {
        // Given
        Media media = new Media();
        Map<String, List<PanoramicMedia360>> panoramic360 = new HashMap<>();
        
        PanoramicMedia360 panoramic1 = new PanoramicMedia360();
        panoramic1.setRoomCode("ROOM001");
        panoramic1.setUrl("https://example.com/360-image1.jpg");
        
        panoramic360.put("R", Arrays.asList(panoramic1));
        media.setPanoramic360(panoramic360);

        when(polyglotService.getTranslatedData(anyString())).thenReturn("View 360°");

        // When
        View360Image result = searchRoomsMediaHelper.extract360ImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertNotNull(result.getImages());
        assertEquals(1, result.getImages().size());
        assertEquals("View 360°", result.getCtaText());
        assertEquals("https://example.com/360-icon.png", result.getCtaIcon());
        
        Image360 image360 = result.getImages().get(0);
        assertEquals("ROOM001", image360.getRoomCode());
        assertEquals("https://example.com/360-image1.jpg", image360.getImageUrl());
    }

    @Test
    public void should_ReturnNull_When_No360ImagesForRoomCode() {
        // Given
        Media media = new Media();
        Map<String, List<PanoramicMedia360>> panoramic360 = new HashMap<>();
        
        PanoramicMedia360 panoramic1 = new PanoramicMedia360();
        panoramic1.setRoomCode("ROOM002");
        panoramic1.setUrl("https://example.com/360-image1.jpg");
        
        panoramic360.put("R", Arrays.asList(panoramic1));
        media.setPanoramic360(panoramic360);

        // When
        View360Image result = searchRoomsMediaHelper.extract360ImagesFromMedia(media, "ROOM001");

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_MediaIsNullFor360Images() {
        // When
        View360Image result = searchRoomsMediaHelper.extract360ImagesFromMedia(null, "ROOM001");

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_Panoramic360IsNull() {
        // Given
        Media media = new Media();
        media.setPanoramic360(null);

        // When
        View360Image result = searchRoomsMediaHelper.extract360ImagesFromMedia(media, "ROOM001");

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_Panoramic360IsEmpty() {
        // Given
        Media media = new Media();
        media.setPanoramic360(new HashMap<>());

        // When
        View360Image result = searchRoomsMediaHelper.extract360ImagesFromMedia(media, "ROOM001");

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_Panoramic360DoesNotContainRKey() {
        // Given
        Media media = new Media();
        Map<String, List<PanoramicMedia360>> panoramic360 = new HashMap<>();
        panoramic360.put("H", new ArrayList<>());  // Different key, not "R"
        media.setPanoramic360(panoramic360);

        // When
        View360Image result = searchRoomsMediaHelper.extract360ImagesFromMedia(media, "ROOM001");

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_Panoramic360ListIsNull() {
        // Given
        Media media = new Media();
        Map<String, List<PanoramicMedia360>> panoramic360 = new HashMap<>();
        panoramic360.put("R", null);  // Null list
        media.setPanoramic360(panoramic360);

        // When
        View360Image result = searchRoomsMediaHelper.extract360ImagesFromMedia(media, "ROOM001");

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_Panoramic360ListIsEmpty() {
        // Given
        Media media = new Media();
        Map<String, List<PanoramicMedia360>> panoramic360 = new HashMap<>();
        panoramic360.put("R", new ArrayList<>());  // Empty list
        media.setPanoramic360(panoramic360);

        // When
        View360Image result = searchRoomsMediaHelper.extract360ImagesFromMedia(media, "ROOM001");

        // Then
        assertNull(result);
    }

    @Test
    public void should_ExtractMultiple360Images_When_MediaHasMultiplePanoramicImagesForSameRoom() {
        // Given
        Media media = new Media();
        Map<String, List<PanoramicMedia360>> panoramic360 = new HashMap<>();
        
        PanoramicMedia360 panoramic1 = new PanoramicMedia360();
        panoramic1.setRoomCode("ROOM001");
        panoramic1.setUrl("https://example.com/360-image1.jpg");
        
        PanoramicMedia360 panoramic2 = new PanoramicMedia360();
        panoramic2.setRoomCode("ROOM001");
        panoramic2.setUrl("https://example.com/360-image2.jpg");
        
        panoramic360.put("R", Arrays.asList(panoramic1, panoramic2));
        media.setPanoramic360(panoramic360);

        when(polyglotService.getTranslatedData(anyString())).thenReturn("View 360°");

        // When
        View360Image result = searchRoomsMediaHelper.extract360ImagesFromMedia(media, "ROOM001");

        // Then
        assertNotNull(result);
        assertNotNull(result.getImages());
        assertEquals(2, result.getImages().size());
        assertEquals("View 360°", result.getCtaText());
        assertEquals("https://example.com/360-icon.png", result.getCtaIcon());
        
        Image360 image360_1 = result.getImages().get(0);
        assertEquals("ROOM001", image360_1.getRoomCode());
        assertEquals("https://example.com/360-image1.jpg", image360_1.getImageUrl());
        
        Image360 image360_2 = result.getImages().get(1);
        assertEquals("ROOM001", image360_2.getRoomCode());
        assertEquals("https://example.com/360-image2.jpg", image360_2.getImageUrl());
    }

    @Test
    public void should_PopulateMediaData_When_RoomImagesProvided() {
        // Given
        List<String> roomImages = new ArrayList<>();
        roomImages.add("https://example.com/image1.jpg");
        roomImages.add("//example.com/image2.jpg");

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(null, roomImages, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        MediaData mediaData1 = result.get(0);
        assertEquals("https://example.com/image1.jpg", mediaData1.getUrl());
        assertEquals(Constants.IMAGE_TYPE, mediaData1.getMediaType());
        
        MediaData mediaData2 = result.get(1);
        assertEquals("https://example.com/image2.jpg", mediaData2.getUrl());
        assertEquals(Constants.IMAGE_TYPE, mediaData2.getMediaType());
    }

    @Test
    public void should_ReturnEmptyList_When_NoRoomImagesProvided() {
        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(null, null, "ROOM001");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_NormalizeUrls_When_PopulatingMediaData() {
        // Given
        List<String> roomImages = Arrays.asList("//example.com/image.jpg");

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(null, roomImages, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/image.jpg", result.get(0).getUrl());
    }

    @Test
    public void should_HandleEmptyRoomImagesList_When_PopulatingMediaData() {
        // Given
        List<String> roomImages = new ArrayList<>();

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(null, roomImages, "ROOM001");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void should_HandleHttpsUrlCorrectly_When_PopulatingMediaData() {
        // Given
        List<String> roomImages = new ArrayList<>();
        roomImages.add("https://example.com/image.jpg");

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(null, roomImages, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/image.jpg", result.get(0).getUrl());
    }

    @Test
    public void should_AddHttpsPrefix_When_UrlStartsWithDoubleSlash() {
        // Given
        List<String> roomImages = new ArrayList<>();
        roomImages.add("//example.com/image.jpg");

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(null, roomImages, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/image.jpg", result.get(0).getUrl());
    }

    @Test
    public void should_HandleNullRoomCode_When_PopulatingMediaData() {
        // Given
        List<String> roomImages = new ArrayList<>();
        roomImages.add("https://example.com/image.jpg");

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(null, roomImages, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("https://example.com/image.jpg", result.get(0).getUrl());
    }

    @Test
    public void should_HandleMultipleImages_When_PopulatingMediaData() {
        // Given
        List<String> roomImages = new ArrayList<>();
        roomImages.add("https://example.com/image1.jpg");
        roomImages.add("https://example.com/image2.jpg");
        roomImages.add("//example.com/image3.jpg");

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(null, roomImages, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("https://example.com/image1.jpg", result.get(0).getUrl());
        assertEquals("https://example.com/image2.jpg", result.get(1).getUrl());
        assertEquals("https://example.com/image3.jpg", result.get(2).getUrl());
    }

    @Test
    public void should_HandleHttpUrlCorrectly_When_PopulatingMediaData() {
        // Given
        List<String> roomImages = new ArrayList<>();
        roomImages.add("http://example.com/image.jpg");

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(null, roomImages, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("http://example.com/image.jpg", result.get(0).getUrl());
    }

    @Test
    public void should_HandleNonHttpUrls_When_PopulatingMediaData() {
        // Given
        List<String> roomImages = new ArrayList<>();
        roomImages.add("ftp://example.com/image.jpg");
        roomImages.add("data:image/png;base64,abc123");

        // When
        List<MediaData> result = searchRoomsMediaHelper.populateMedia(null, roomImages, "ROOM001");

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("https:ftp://example.com/image.jpg", result.get(0).getUrl());
        assertEquals("https:data:image/png;base64,abc123", result.get(1).getUrl());
    }
}
