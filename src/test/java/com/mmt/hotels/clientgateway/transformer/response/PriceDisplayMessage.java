package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.Utility;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PriceDisplayMessage {

    @InjectMocks
    private CommonResponseTransformer commonResponseTransformer;

    @Mock
    private PolyglotService polyglotService;
    
    @Mock
    private Utility utility;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // Setup mock for utility methods
//        when(utility.getExpDataMap(any())).thenReturn(null);
        
        // Setup common translation responses
        when(polyglotService.getTranslatedData(ConstantsTranslation.BED_SELLABLE_TYPE)).thenReturn("Bed");
        when(polyglotService.getTranslatedData(ConstantsTranslation.ROOM_SELLABLE_TYPE)).thenReturn("Room");
        when(polyglotService.getTranslatedData(ConstantsTranslation.PER_NIGHT_TEXT)).thenReturn("Per Night");
        when(polyglotService.getTranslatedData(ConstantsTranslation.PER_ROOM_PER_NIGHT)).thenReturn("Per {room} per night");
        when(polyglotService.getTranslatedData(ConstantsTranslation.GROUP_PER_ROOM_PER_NIGHT)).thenReturn("Per Room/Night");
        when(polyglotService.getTranslatedData(ConstantsTranslation.PER_NIGHT_FOR_NUM_ROOMS)).thenReturn("Per night for {num} {rooms}s");
        when(polyglotService.getTranslatedData(ConstantsTranslation.PER_NIGHT_WITH_TAX)).thenReturn("Per Night with Tax");
        when(polyglotService.getTranslatedData(ConstantsTranslation.FOR_NUM_ROOMS_PER_NIGHT_WITH_TAX)).thenReturn("For {num} Rooms Per Night with Tax");
        when(polyglotService.getTranslatedData(ConstantsTranslation.PER_NIGHT_FOR_NUM_ROOMS_TEXT)).thenReturn("Per Night for {num} Rooms");
        when(polyglotService.getTranslatedData(ConstantsTranslation.TOTAL_PRICE_TEXT)).thenReturn("Total Price");
        when(polyglotService.getTranslatedData(ConstantsTranslation.FOR_NUM_NIGHTS)).thenReturn("For {num} Nights");
        when(polyglotService.getTranslatedData(ConstantsTranslation.FOR_NUM_NIGHTS_NUM_ROOMS)).thenReturn("For {num_nights} Nights, {num_rooms} Rooms");
    }

    // Test cases for PDO:PRN (Per Room per Night)
    @Test
    public void testGetPriceDisplayMessage_PRN_Regular() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PRN\"}", 1, "Room", 1, false, false, false);
        Assert.assertEquals("Per Room per night", result);
    }

    @Test
    public void testGetPriceDisplayMessage_PRN_AltAcco() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PRN\"}", 1, "Room", 1, false, true, false);
        Assert.assertEquals("Per Night", result);
    }

    @Test
    public void testGetPriceDisplayMessage_PRN_GroupBooking() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PRN\"}", 1, "Room", 1, true, false, false);
        Assert.assertEquals("Per Room/Night", result);
    }

    // Test cases for PDO:PRNT (Per Room per Night with Tax)
    @Test
    public void testGetPriceDisplayMessage_PRNT_Regular() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PRNT\"}", 1, "Room", 1, false, false, false);
        Assert.assertEquals("Per Room per night", result);
    }

    @Test
    public void testGetPriceDisplayMessage_PRNT_AltAcco() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PRNT\"}", 1, "Room", 1, false, true, false);
        Assert.assertEquals("Per Night", result);
    }

    @Test
    public void testGetPriceDisplayMessage_PRNT_GroupBooking() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PRNT\"}", 1, "Room", 1, true, false, false);
        Assert.assertEquals("Per Room/Night", result);
    }

    // Test cases for PDO:PN (Per Night)
    @Test
    public void testGetPriceDisplayMessage_PN_SingleRoom() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PN\"}", 1, "Room", 1, false, false, false);
        Assert.assertEquals("Per Night", result);
    }

    @Test
    public void testGetPriceDisplayMessage_PN_MultipleRooms() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PN\"}", 2, "Room", 1, false, false, false);
        Assert.assertEquals("Per night for 2 Rooms", result);
    }

    @Test
    public void testGetPriceDisplayMessage_PN_AltAcco() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PN\"}", 2, "Room", 1, false, true, false);
        Assert.assertEquals("Per Night", result);
    }

    // Test cases for PDO:PNT (Per Night with Tax)
    @Test
    public void testGetPriceDisplayMessage_PNT_SingleRoomSingleNight() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PNT\"}", 1, "Room", 1, false, false, false);
        Assert.assertEquals("Per Night", result);
    }

    @Test
    public void testGetPriceDisplayMessage_PNT_SingleRoomMultipleNights() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PNT\"}", 1, "Room", 3, false, false, false);
        Assert.assertEquals("For 1 Rooms Per Night with Tax", result);
    }

    @Test
    public void testGetPriceDisplayMessage_PNT_MultipleRoomsSingleNight() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PNT\"}", 2, "Room", 1, false, false, false);
        Assert.assertEquals("Per Night for 2 Rooms", result);
    }

    @Test
    public void testGetPriceDisplayMessage_PNT_MultipleRoomsMultipleNights() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PNT\"}", 2, "Room", 3, false, false, false);
        Assert.assertEquals("For 2 Rooms Per Night with Tax", result);
    }

    @Test
    public void testGetPriceDisplayMessage_PNT_AltAcco() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PNT\"}", 2, "Room", 3, false, true, false);
        Assert.assertEquals("Per Night with Tax", result);
    }

    // Test cases for PDO:TP (Total Price)
    @Test
    public void testGetPriceDisplayMessage_TP_SingleRoomSingleNight() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"TP\"}", 1, "Room", 1, false, false, false);
        Assert.assertEquals("Per Night", result);
    }

    @Test
    public void testGetPriceDisplayMessage_TP_SingleRoomMultipleNights() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"TP\"}", 1, "Room", 3, false, false, false);
        Assert.assertEquals("For 3 Nights", result);
    }

    @Test
    public void testGetPriceDisplayMessage_TP_MultipleRoomsSingleNight() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"TP\"}", 2, "Room", 1, false, false, false);
        Assert.assertEquals("Per Night for 2 Rooms", result);
    }

    @Test
    public void testGetPriceDisplayMessage_TP_MultipleRoomsMultipleNights() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"TP\"}", 2, "Room", 3, false, false, false);
        Assert.assertEquals("For 3 Nights, 2 Rooms", result);
    }

    @Test
    public void testGetPriceDisplayMessage_TP_AltAccoSingleNight() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"TP\"}", 2, "Room", 1, false, true, false);
        Assert.assertEquals("Per Night", result);
    }

    @Test
    public void testGetPriceDisplayMessage_TP_AltAccoMultipleNights() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"TP\"}", 2, "Room", 3, false, true, false);
        Assert.assertEquals("For 3 Nights", result);
    }

    // Test cases for PDO:TPT (Total Price with Tax)
    @Test
    public void testGetPriceDisplayMessage_TPT() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"TPT\"}", 2, "Room", 3, false, false, false);
        Assert.assertEquals("Total Price", result);
    }

    // Test cases for different sellable types
    @Test
    public void testGetPriceDisplayMessage_DifferentSellableTypes() {
        // For "Bed" type
        String resultBed = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PRN\"}", 1, "bed", 1, false, false, false);
        Assert.assertEquals("Per Bed per night", resultBed);

        // For "Apartment" type
        String resultApartment = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PRN\"}", 1, "Apartment", 1, false, false, false);
        Assert.assertEquals("Per Room per night", resultApartment);
    }

    // Test for missing or invalid PDO
    @Test
    public void testGetPriceDisplayMessage_InvalidPDO() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"INVALID\"}", 1, "Room", 1, false, false, false);
        Assert.assertEquals("", result);
    }

    @Test
    public void testGetPriceDisplayMessage_NullPDO() {
        String result = commonResponseTransformer.getPriceDisplayMessage(null, 1, "Room", 1, false, false, false);
        Assert.assertEquals("Per Night", result);  // Default is PN
    }

    // Test with null room count or night count failing need to discuss Shobhit
//    @Test
//    public void testGetPriceDisplayMessage_NullRoomCount() {
//        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PN\"}", null, "Room", 1, false, false);
//        Assert.assertEquals("Per Night", result);
//    }

    @Test
    public void testGetPriceDisplayMessage_NullNightCount() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PNT\"}", 2, "Room", null, false, false, false);
        Assert.assertEquals("Per Night", result);
    }

    // Test combination of group booking and alt acco
    @Test
    public void testGetPriceDisplayMessage_GroupBookingAndAltAcco() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PRN\"}", 2, "Room", 3, true, true, false);
        Assert.assertEquals("Per Night", result);  // Alt Acco takes precedence
    }
    
    // Extra test case to match the function signature in your example response
    @Test
    public void testGetPriceDisplayMessage_PerNightForTwoRooms() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PN\"}", 2, "Room", 1, false, false, false);
        Assert.assertEquals("Per night for 2 Rooms", result);
    }
    
    // Add new test cases for isHighSellingAltAcco parameter
    @Test
    public void testGetPriceDisplayMessage_PN_AltAcco_HighSelling() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PN\"}", 2, "Room", 1, false, true, true);
        Assert.assertEquals("Per night for 2 Rooms", result);
    }
    
    @Test
    public void testGetPriceDisplayMessage_PRN_AltAcco_HighSelling() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"PRN\"}", 1, "Room", 1, false, true, true);
        Assert.assertEquals("Per Night", result);
    }
    
    @Test
    public void testGetPriceDisplayMessage_TP_SingleRoom_AltAcco_HighSelling() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"TP\"}", 1, "Room", 1, false, true, true);
        Assert.assertEquals("Per Night", result);
    }
    
    @Test
    public void testGetPriceDisplayMessage_TP_MultipleRooms_AltAcco_HighSelling() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"TP\"}", 2, "Room", 1, false, true, true);
        Assert.assertEquals("Per Night for 2 Rooms", result);
    }
    
    @Test
    public void testGetPriceDisplayMessage_TP_MultipleNights_AltAcco_HighSelling() {
        String result = commonResponseTransformer.getPriceDisplayMessage("{\"PDO\":\"TP\"}", 2, "Room", 3, false, true, true);
        Assert.assertEquals("For 3 Nights, 2 Rooms", result);
    }
} 