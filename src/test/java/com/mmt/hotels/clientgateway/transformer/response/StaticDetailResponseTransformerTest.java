package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.response.staticdetail.HouseRulesV2;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.desktop.StaticDetailResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysTooltip;
import com.mmt.hotels.model.response.staticdata.CommonRules;
import com.mmt.hotels.model.response.staticdata.ContextRules;
import com.mmt.hotels.model.response.staticdata.Rule;
import com.mmt.hotels.model.response.staticdata.RuleInfo;
import com.mmt.hotels.pojo.FoodAndDining.FoodAndDiningEnums;
import com.mmt.model.util.RatingDetail;
import com.mmt.propertymanager.config.PropertyManager;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.model.UGCRatingData;
import com.mmt.hotels.clientgateway.response.staticdetail.Restaurant;
import com.mmt.hotels.clientgateway.response.HighlightedAmenity;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.staticdetail.DiningRuleItem;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class StaticDetailResponseTransformerTest {

    @Mock
    ObjectMapperUtil objectMapperUtil;
    @Mock
    CommonResponseTransformer commonResponseTransformer;
    @Mock
    private PropertyManager propertyManager;
    @Mock
    private PolyglotService polyglotService;
    @Mock
    private PolyglotHelper polyglotHelper;
    @Mock
    private Utility utility;

    @InjectMocks
    private StaticDetailResponseTransformerDesktop staticDetailResponseTransformer;

    @Before
    public void init(){
        // Mock utility.getUrlFromConfig to return the same URL that was passed in
        when(utility.getUrlFromConfig(anyString())).thenAnswer(invocation -> invocation.getArgument(0));
        
        ReflectionTestUtils.setField(staticDetailResponseTransformer, "view360IconUrl", "www.sample360Url.com");
        ReflectionTestUtils.setField(staticDetailResponseTransformer, "foodDiningMergeSections", 
            Arrays.asList("/FOOD_AND_DINING/", "KITCHEN"));
    }

    @Test
    public void buildFoodDining_WhenFoodDiningRuleIsEmpty_ShouldReturnEmptyHouseRulesV2() {
        // Arrange
        List<CommonRules> foodDiningRule = new ArrayList<>();
        boolean foodDiningHighlight = true;
        StaticDetailCriteria staticDetailRequest = new StaticDetailCriteria();
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean foodAndDiningV2 = false;
        boolean foodDiningRevamp = true;
        UGCRatingData foodRatingData = null;
        boolean isAltAcco = false;
        boolean isDhCall = false;

        // Act
        HouseRulesV2 result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDining", foodDiningRule, foodDiningHighlight, staticDetailRequest,
                deviceDetails, foodAndDiningV2, foodDiningRevamp, foodRatingData, isAltAcco, isDhCall, true, false);

        // Assert
        assertNotNull(result);
        assertNull(result.getAllRules());
        assertNull(result.getSummary());
        assertNull(result.getSummaryV2());
        assertNull(result.getRestaurants());
    }

    @Test
    public void buildFoodDining_WhenFoodDiningRuleHasRestaurant_ShouldIncludeRestaurantDetails() {
        // Arrange
        List<CommonRules> foodDiningRule = new ArrayList<>();
        CommonRules restaurantRule = new CommonRules();
        restaurantRule.setCategory(FoodAndDiningEnums.Restaurant.getName());
        restaurantRule.setHostCatHeading("Restaurant Name");
        restaurantRule.setId("restaurant1");
        restaurantRule.setImages(Arrays.asList("image1.jpg"));
        restaurantRule.setUspText("USP Text");
        foodDiningRule.add(restaurantRule);

        boolean foodDiningHighlight = true;
        StaticDetailCriteria staticDetailRequest = new StaticDetailCriteria();
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean foodAndDiningV2 = false;
        boolean foodDiningRevamp = true;
        UGCRatingData foodRatingData = null;
        boolean isAltAcco = false;
        boolean isDhCall = true;

        // Act
        HouseRulesV2 result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDining", foodDiningRule, foodDiningHighlight, staticDetailRequest,
                deviceDetails, foodAndDiningV2, foodDiningRevamp, foodRatingData, isAltAcco, isDhCall, true, false);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRestaurants());
        assertEquals(1, result.getRestaurants().size());
        Restaurant restaurant = result.getRestaurants().get(0);
        assertEquals("Restaurant Name", restaurant.getTitle());
        assertEquals("image1.jpg", restaurant.getImageUrl());
        assertEquals("restaurant1", restaurant.getCategoryId());
    }

    @Test
    public void buildFoodDining_WhenFoodDiningRevampIsTrue_ShouldSetCorrectTitle() {
        // Arrange
        List<CommonRules> foodDiningRule = new ArrayList<>();
        boolean foodDiningHighlight = true;
        StaticDetailCriteria staticDetailRequest = new StaticDetailCriteria();
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean foodAndDiningV2 = false;
        boolean foodDiningRevamp = true;
        UGCRatingData foodRatingData = null;
        boolean isAltAcco = true;
        boolean isDhCall = false;

        when(polyglotService.getTranslatedData("FOOD_DINING_CARD_TITLE")).thenReturn("Food and Dining");

        // Act
        HouseRulesV2 result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDining", foodDiningRule, foodDiningHighlight, staticDetailRequest,
                deviceDetails, foodAndDiningV2, foodDiningRevamp, foodRatingData, isAltAcco, isDhCall, true, false);

        // Assert
        assertNotNull(result);
        assertEquals("Food and Dining", result.getTitle());
    }

    @Test
    public void buildFoodDining_WhenFoodRatingDataIsPresent_ShouldSetRatingData() {
        // Arrange
        List<CommonRules> foodDiningRule = new ArrayList<>();
        boolean foodDiningHighlight = true;
        StaticDetailCriteria staticDetailRequest = new StaticDetailCriteria();
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean foodAndDiningV2 = false;
        boolean foodDiningRevamp = true;
        UGCRatingData foodRatingData = new UGCRatingData();
        RatingDetail ratingDetail = new RatingDetail();
        ratingDetail.setText("4.5");
        foodRatingData.setSummary(ratingDetail);
        boolean isAltAcco = false;
        boolean isDhCall = false;

        // Act
        HouseRulesV2 result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDining", foodDiningRule, foodDiningHighlight, staticDetailRequest,
                deviceDetails, foodAndDiningV2, foodDiningRevamp, foodRatingData, isAltAcco, isDhCall, true, false);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRatingData());
        assertEquals("4.5", result.getRatingData().getSummary().getText());
    }

    @Test
    public void buildFoodDining_WhenCommonRuleHasRules_ShouldAddToSummaryList() {
        // Arrange
        List<CommonRules> foodDiningRule = new ArrayList<>();
        CommonRules commonRule = new CommonRules();
        commonRule.setCategory(FoodAndDiningEnums.IndianFoodOptions.getName());
        List<Rule> rules = new ArrayList<>();
        Rule rule = new Rule();
        rule.setText("Rule Text");
        rule.setIconUrl("ic_nonveg_not_allowed");
        rules.add(rule);
        commonRule.setRules(rules);
        foodDiningRule.add(commonRule);

        boolean foodDiningHighlight = true;
        StaticDetailCriteria staticDetailRequest = new StaticDetailCriteria();
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean foodAndDiningV2 = false;
        boolean foodDiningRevamp = true;
        UGCRatingData foodRatingData = null;
        boolean isAltAcco = false;
        boolean isDhCall = false;

        // Act
        HouseRulesV2 result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDining", foodDiningRule, foodDiningHighlight, staticDetailRequest,
                deviceDetails, foodAndDiningV2, foodDiningRevamp, foodRatingData, isAltAcco, isDhCall, true, false);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getSummary());
        assertEquals(1, result.getSummary().size());
        assertEquals("Rule Text", result.getSummary().get(0));
    }

    @Test
    public void limitAmenitiesCount_WithStringList_ShouldLimitToSix() {
        // Arrange
        List<String> amenities = new ArrayList<>(Arrays.asList(
                "WiFi", "Parking", "Pool", "Gym", "Restaurant", "Bar", "Spa", "Airport Shuttle", "Laundry"
        ));
        boolean serviceApartment = false;
        boolean isAltAcco = false;

        // Act
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "limitAmenitiesCount", amenities, serviceApartment, isAltAcco);

        // Assert
        assertEquals(6, amenities.size());
        assertEquals("WiFi", amenities.get(0));
        assertEquals("Parking", amenities.get(1));
        assertEquals("Pool", amenities.get(2));
        assertEquals("Gym", amenities.get(3));
        assertEquals("Restaurant", amenities.get(4));
        assertEquals("Bar", amenities.get(5));
    }

    @Test
    public void limitAmenitiesCount_WithHighlightedAmenityList_ShouldLimitToSix() {
        // Arrange
        List<HighlightedAmenity> amenities = new ArrayList<>();
        for (String name : Arrays.asList("WiFi", "Parking", "Pool", "Gym", "Restaurant", "Bar", "Spa", "Airport Shuttle", "Laundry")) {
            HighlightedAmenity amenity = new HighlightedAmenity();
            amenity.setTitle(name);
            amenities.add(amenity);
        }
        boolean serviceApartment = false;
        boolean isAltAcco = false;

        // Act
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "limitAmenitiesCount", amenities, serviceApartment, isAltAcco);

        // Assert
        assertEquals(6, amenities.size());
        assertEquals("WiFi", amenities.get(0).getTitle());
        assertEquals("Parking", amenities.get(1).getTitle());
        assertEquals("Pool", amenities.get(2).getTitle());
        assertEquals("Gym", amenities.get(3).getTitle());
        assertEquals("Restaurant", amenities.get(4).getTitle());
        assertEquals("Bar", amenities.get(5).getTitle());
    }

    @Test
    public void limitAmenitiesCount_WithLessThanSixAmenities_ShouldNotChange() {
        // Arrange
        List<String> amenities = new ArrayList<>(Arrays.asList(
                "WiFi", "Parking", "Pool", "Gym"
        ));
        boolean serviceApartment = false;
        boolean isAltAcco = false;

        // Act
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "limitAmenitiesCount", amenities, serviceApartment, isAltAcco);

        // Assert
        assertEquals(4, amenities.size());
        assertEquals("WiFi", amenities.get(0));
        assertEquals("Parking", amenities.get(1));
        assertEquals("Pool", amenities.get(2));
        assertEquals("Gym", amenities.get(3));
    }

    @Test
    public void limitAmenitiesCount_WithExactlySixAmenities_ShouldNotChange() {
        // Arrange
        List<String> amenities = new ArrayList<>(Arrays.asList(
                "WiFi", "Parking", "Pool", "Gym", "Restaurant", "Bar"
        ));
        boolean serviceApartment = false;
        boolean isAltAcco = false;

        // Act
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "limitAmenitiesCount", amenities, serviceApartment, isAltAcco);

        // Assert
        assertEquals(6, amenities.size());
        assertEquals("WiFi", amenities.get(0));
        assertEquals("Parking", amenities.get(1));
        assertEquals("Pool", amenities.get(2));
        assertEquals("Gym", amenities.get(3));
        assertEquals("Restaurant", amenities.get(4));
        assertEquals("Bar", amenities.get(5));
    }

    @Test
    public void limitAmenitiesCount_WithNullList_ShouldHandleGracefully() {
        // Arrange
        List<String> amenities = null;
        boolean serviceApartment = false;
        boolean isAltAcco = false;

        // Act & Assert - Should not throw exception
        try {
            ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "limitAmenitiesCount", amenities, serviceApartment, isAltAcco);
            // If we reach here, the test passes (no exception was thrown)
            assertTrue(true);
        } catch (Exception e) {
            fail("limitAmenitiesCount should handle null list gracefully");
        }
    }

    @Test
    public void limitAmenitiesCount_WithEmptyList_ShouldNotChange() {
        // Arrange
        List<String> amenities = new ArrayList<>();
        boolean serviceApartment = false;
        boolean isAltAcco = false;

        // Act
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "limitAmenitiesCount", amenities, serviceApartment, isAltAcco);

        // Assert
        assertEquals(0, amenities.size());
    }

    @Test
    public void limitAmenitiesCount_WithCustomObjects_ShouldLimitToSix() {
        // Arrange - Create a list of custom objects
        class CustomAmenity {
            private String name;
            public CustomAmenity(String name) { this.name = name; }
            public String getName() { return name; }
        }

        List<CustomAmenity> amenities = new ArrayList<>();
        for (String name : Arrays.asList("WiFi", "Parking", "Pool", "Gym", "Restaurant", "Bar", "Spa", "Airport Shuttle", "Laundry")) {
            amenities.add(new CustomAmenity(name));
        }
        boolean serviceApartment = false;
        boolean isAltAcco = false;

        // Act
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "limitAmenitiesCount", amenities, serviceApartment, isAltAcco);

        // Assert
        assertEquals(6, amenities.size());
        assertEquals("WiFi", amenities.get(0).getName());
        assertEquals("Parking", amenities.get(1).getName());
        assertEquals("Pool", amenities.get(2).getName());
        assertEquals("Gym", amenities.get(3).getName());
        assertEquals("Restaurant", amenities.get(4).getName());
        assertEquals("Bar", amenities.get(5).getName());
    }

    @Test
    public void limitAmenitiesCount_WithNineAmenities_ShouldRemoveThree() {
        // Arrange
        List<String> amenities = new ArrayList<>(Arrays.asList(
                "WiFi", "Parking", "Pool", "Gym", "Restaurant", "Bar", "Spa", "Airport Shuttle", "Laundry"
        ));
        boolean serviceApartment = false;
        boolean isAltAcco = false;

        // Act
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "limitAmenitiesCount", amenities, serviceApartment, isAltAcco);

        // Assert
        assertEquals(6, amenities.size());
        // Check that the first 6 amenities are preserved
        assertEquals("WiFi", amenities.get(0));
        assertEquals("Parking", amenities.get(1));
        assertEquals("Pool", amenities.get(2));
        assertEquals("Gym", amenities.get(3));
        assertEquals("Restaurant", amenities.get(4));
        assertEquals("Bar", amenities.get(5));
        // Check that the last 3 amenities are removed
        assertFalse(amenities.contains("Spa"));
        assertFalse(amenities.contains("Airport Shuttle"));
        assertFalse(amenities.contains("Laundry"));
    }

    @Test
    public void limitAmenitiesCount_WithSevenAmenities_ShouldRemoveOne() {
        // Arrange
        List<String> amenities = new ArrayList<>(Arrays.asList(
                "WiFi", "Parking", "Pool", "Gym", "Restaurant", "Bar", "Spa"
        ));
        boolean serviceApartment = false;
        boolean isAltAcco = false;

        // Act
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "limitAmenitiesCount", amenities, serviceApartment, isAltAcco);

        // Assert
        assertEquals(6, amenities.size());
        // Check that the first 6 amenities are preserved
        assertEquals("WiFi", amenities.get(0));
        assertEquals("Parking", amenities.get(1));
        assertEquals("Pool", amenities.get(2));
        assertEquals("Gym", amenities.get(3));
        assertEquals("Restaurant", amenities.get(4));
        assertEquals("Bar", amenities.get(5));
        // Check that the last amenity is removed
        assertFalse(amenities.contains("Spa"));
    }

    @Test
    public void buildCategoryCommonRules_WhenCategoryInfoIsNull_ShouldReturnNull() {
        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.CommonRules result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildCategoryCommonRules",
                (Object) null);

        // Assert
        assertNull(result);
    }

    @Test
    public void buildCategoryCommonRules_WhenLanguagesSpokenCategory_ShouldSetCorrectFlags() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.CategoryInfo categoryInfo = new com.mmt.hotels.model.response.staticdata.CategoryInfo();
        categoryInfo.setId("LANGUAGES_SPOKEN");
        categoryInfo.setCategoryName("Languages Spoken");
        categoryInfo.setCategoryDesc("Languages spoken by staff");
        categoryInfo.setRuleDesc(Arrays.asList("English", "Hindi"));
        categoryInfo.setCategoryHeading("Staff Languages");

        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.CommonRules result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildCategoryCommonRules",
                categoryInfo);

        // Assert
        assertNotNull(result);
        assertEquals("LANGUAGESSPOKEN", result.getId());
        assertEquals("Languages Spoken", result.getCategory());
        assertEquals("Languages spoken by staff", result.getCategoryDesc());
        assertEquals("Staff Languages", result.getCategoryHeading());
        assertTrue(result.isShowInDetailHome());
        assertFalse(result.isShowArrowInDetailHome());
        assertFalse(result.getShowInL2Page());
        assertEquals(2, result.getRules().size());
        assertEquals("English", result.getRules().get(0).getText());
        assertEquals("Hindi", result.getRules().get(1).getText());
    }

    @Test
    public void buildCategoryCommonRules_WhenNonLanguageCategory_ShouldSetCorrectFlags() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.CategoryInfo categoryInfo = new com.mmt.hotels.model.response.staticdata.CategoryInfo();
        categoryInfo.setId("BREAKFAST_CHARGES");
        categoryInfo.setCategoryName("Breakfast Charges");
        categoryInfo.setCategoryDesc("Breakfast charges info");
        categoryInfo.setRuleDesc(Collections.singletonList("Complimentary breakfast"));
        categoryInfo.setCategoryHeading("Breakfast Info");

        // Create RuleTableInfo with proper structure
        com.mmt.hotels.model.response.staticdata.RuleTableInfo ruleTableInfo = new com.mmt.hotels.model.response.staticdata.RuleTableInfo();
        ruleTableInfo.setKeyTitle("AGE");
        ruleTableInfo.setValueTitle("COST");
        RuleInfo ruleInfo = new RuleInfo();
        ruleInfo.setKey("up to 1 years");
        ruleInfo.setValue(Collections.singletonList("Free Crib"));
        ruleTableInfo.setInfoList(Collections.singletonList(ruleInfo));
        categoryInfo.setRuleTableInfo(ruleTableInfo);

        // Create the expected return value
        com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo expectedRuleTableInfo =
            new com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo();

        com.mmt.hotels.clientgateway.response.staticdetail.CommonRules result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildCategoryCommonRules",
                categoryInfo);

        // Assert
        assertNotNull(result);
        assertEquals("BREAKFASTCHARGES", result.getId());
        assertEquals("Breakfast Charges", result.getCategory());
        assertEquals("Breakfast charges info", result.getCategoryDesc());
        assertEquals("Breakfast Info", result.getCategoryHeading());
        assertTrue(result.isShowInDetailHome());
        assertTrue(result.isShowArrowInDetailHome());
        assertTrue(result.getShowInL2Page());
        assertNotNull(result.getRuleTableInfo());
        assertEquals(1, result.getRules().size());
        assertEquals("Complimentary breakfast", result.getRules().get(0).getText());
    }

    // ==================== buildFoodDiningV2 Test Methods ====================

    @Test
    public void buildFoodDiningV2_WhenFoodDiningV2HESIsNull_ShouldReturnNull() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.FoodDiningV2 foodDiningV2HES = null;
        UGCRatingData foodRatingData = null;
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean isAltAcco = false;
        boolean isDhCall = false;
        boolean foodAndDiningEnhancement = false;
        boolean isMealDetailsPresent = false;

        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningV2",
                foodDiningV2HES, foodRatingData, deviceDetails, isAltAcco, isDhCall, foodAndDiningEnhancement, isMealDetailsPresent);

        // Assert
        assertNull(result);
    }

    @Test
    public void buildFoodDiningV2_WhenDiningInfoIsEmpty_ShouldReturnBasicFoodDiningV2() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.FoodDiningV2 foodDiningV2HES =
            new com.mmt.hotels.model.response.staticdata.FoodDiningV2();
        foodDiningV2HES.setDiningInfo(new ArrayList<>());
        com.mmt.hotels.model.response.staticdata.FoodDiningSummaryItem summaryItem1 = new com.mmt.hotels.model.response.staticdata.FoodDiningSummaryItem();
        summaryItem1.setText("Vegetarian options available");
        summaryItem1.setIconUrl("veg_icon.png");
        //Set Summary Item to foodDiningV2HES
        foodDiningV2HES.setSummary(Collections.singletonList(summaryItem1));

        UGCRatingData foodRatingData = null;
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean isAltAcco = false;
        boolean isDhCall = false;
        boolean foodAndDiningEnhancement = false;
        boolean isMealDetailsPresent = true;

        when(polyglotService.getTranslatedData("FOOD_DINING_CARD_TITLE")).thenReturn("Food & Dining");

        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningV2",
                foodDiningV2HES, foodRatingData, deviceDetails, isAltAcco, isDhCall, foodAndDiningEnhancement, isMealDetailsPresent);

        // Assert
        assertNotNull(result);
        assertEquals("Food & Dining", result.getTitle());
        assertTrue(result.isMealDetailsPresent());
        assertNull(result.getAllRules());
        assertNotNull(result.getSummary());
        assertNull(result.getRestaurants());
    }

    @Test
    public void buildFoodDiningV2_WhenDiningInfoContainsFoodMenuAndCook_ShouldSkipThoseCategories() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.FoodDiningV2 foodDiningV2HES =
            new com.mmt.hotels.model.response.staticdata.FoodDiningV2();

        List<CommonRules> diningInfo = new ArrayList<>();

        // Add FoodMenu category (should be skipped)
        CommonRules foodMenuRule = new CommonRules();
        foodMenuRule.setCategory("Food Menu");
        foodMenuRule.setId("foodmenu1");
        foodMenuRule.setHeading("Food Menu");
        diningInfo.add(foodMenuRule);

        // Add Cook category (should be skipped)
        CommonRules cookRule = new CommonRules();
        cookRule.setCategory("Cook");
        cookRule.setId("cook1");
        cookRule.setHeading("Cook Details");
        diningInfo.add(cookRule);

        // Add valid category (should be processed)
        CommonRules validRule = new CommonRules();
        validRule.setCategory("Dining");
        validRule.setId("dining1");
        validRule.setHeading("Dining Options");
        diningInfo.add(validRule);

        foodDiningV2HES.setDiningInfo(diningInfo);

        UGCRatingData foodRatingData = null;
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean isAltAcco = false;
        boolean isDhCall = false;
        boolean foodAndDiningEnhancement = false;
        boolean isMealDetailsPresent = false;

        when(polyglotService.getTranslatedData("FOOD_DINING_CARD_TITLE")).thenReturn("Food & Dining");

        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningV2",
                foodDiningV2HES, foodRatingData, deviceDetails, isAltAcco, isDhCall, foodAndDiningEnhancement, isMealDetailsPresent);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getAllRules());
        assertEquals(1, result.getAllRules().size()); // Only the valid rule should be processed
        assertEquals("dining1", result.getAllRules().get(0).getId());
        assertEquals("Dining", result.getAllRules().get(0).getCategory());
    }

    @Test
    public void buildFoodDiningV2_WhenKitchenCategory_ShouldTransformToKitchenCategoryRevamp() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.FoodDiningV2 foodDiningV2HES =
            new com.mmt.hotels.model.response.staticdata.FoodDiningV2();

        List<CommonRules> diningInfo = new ArrayList<>();
        CommonRules kitchenRule = new CommonRules();
        kitchenRule.setCategory("Kitchen");
        kitchenRule.setId("kitchen1");
        kitchenRule.setHeading("Kitchen Facilities");
        kitchenRule.setDescription("Kitchen description");
        kitchenRule.setShowInL2Page(true);
        kitchenRule.setShowInDetailHome(true);
        diningInfo.add(kitchenRule);

        foodDiningV2HES.setDiningInfo(diningInfo);

        UGCRatingData foodRatingData = null;
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean isAltAcco = false;
        boolean isDhCall = false;
        boolean foodAndDiningEnhancement = false;
        boolean isMealDetailsPresent = false;

        when(polyglotService.getTranslatedData("FOOD_DINING_CARD_TITLE")).thenReturn("Food & Dining");

        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningV2",
                foodDiningV2HES, foodRatingData, deviceDetails, isAltAcco, isDhCall, foodAndDiningEnhancement, isMealDetailsPresent);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getAllRules());
        assertEquals(1, result.getAllRules().size());
        assertEquals("Kitchen Information", result.getAllRules().get(0).getCategory()); // Should be transformed
        assertEquals("kitchen1", result.getAllRules().get(0).getId());
        assertEquals("Kitchen Facilities", result.getAllRules().get(0).getHeading());
        assertTrue(result.getAllRules().get(0).isShowArrowInDetailHome()); // Should be set to true
    }

    @Test
    public void buildFoodDiningV2_WhenRestaurantCategoryWithDhCall_ShouldAddToRestaurantsList() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.FoodDiningV2 foodDiningV2HES =
            new com.mmt.hotels.model.response.staticdata.FoodDiningV2();

        List<CommonRules> diningInfo = new ArrayList<>();
        CommonRules restaurantRule = new CommonRules();
        restaurantRule.setCategory("Restaurants at the property");
        restaurantRule.setId("restaurant1");
        restaurantRule.setHeading("Main Restaurant");
        restaurantRule.setImages(Arrays.asList("restaurant_image.jpg", "restaurant_image2.jpg"));
        diningInfo.add(restaurantRule);

        foodDiningV2HES.setDiningInfo(diningInfo);

        UGCRatingData foodRatingData = null;
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean isAltAcco = false;
        boolean isDhCall = true; // DH call enabled
        boolean foodAndDiningEnhancement = false;
        boolean isMealDetailsPresent = false;

        when(polyglotService.getTranslatedData("FOOD_DINING_CARD_TITLE")).thenReturn("Food & Dining");

        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningV2",
                foodDiningV2HES, foodRatingData, deviceDetails, isAltAcco, isDhCall, foodAndDiningEnhancement, isMealDetailsPresent);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRestaurants());
        assertEquals(1, result.getRestaurants().size());

        Restaurant restaurant = result.getRestaurants().get(0);
        assertEquals("Main Restaurant", restaurant.getTitle());
        assertEquals("restaurant_image.jpg", restaurant.getImageUrl());
        assertEquals("restaurant1", restaurant.getCategoryId());

        // Check that showArrowInDetailHome is set to false for restaurants with DH call
        assertNotNull(result.getAllRules());
        assertEquals(1, result.getAllRules().size());
        assertFalse(result.getAllRules().get(0).isShowArrowInDetailHome());
    }

    @Test
    public void buildFoodDiningV2_WhenRestaurantCategoryWithDesktopDevice_ShouldSetShowInDetailHomeToFalse() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.FoodDiningV2 foodDiningV2HES =
            new com.mmt.hotels.model.response.staticdata.FoodDiningV2();

        List<CommonRules> diningInfo = new ArrayList<>();
        CommonRules restaurantRule = new CommonRules();
        restaurantRule.setCategory("Restaurants at the property");
        restaurantRule.setId("restaurant1");
        restaurantRule.setHeading("Main Restaurant");
        diningInfo.add(restaurantRule);

        foodDiningV2HES.setDiningInfo(diningInfo);

        UGCRatingData foodRatingData = null;
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice("desktop"); // Desktop device
        boolean isAltAcco = false;
        boolean isDhCall = false;
        boolean foodAndDiningEnhancement = false;
        boolean isMealDetailsPresent = false;

        when(polyglotService.getTranslatedData("FOOD_DINING_CARD_TITLE")).thenReturn("Food & Dining");

        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningV2",
                foodDiningV2HES, foodRatingData, deviceDetails, isAltAcco, isDhCall, foodAndDiningEnhancement, isMealDetailsPresent);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getAllRules());
        assertEquals(1, result.getAllRules().size());
        assertFalse(result.getAllRules().get(0).isShowInDetailHome()); // Should be false for desktop
    }

    @Test
    public void buildFoodDiningV2_WhenRestaurantCategoryWithFoodAndDiningEnhancement_ShouldChangeTitle() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.FoodDiningV2 foodDiningV2HES =
            new com.mmt.hotels.model.response.staticdata.FoodDiningV2();

        List<CommonRules> diningInfo = new ArrayList<>();
        CommonRules restaurantRule = new CommonRules();
        restaurantRule.setCategory("Restaurants at the property");
        restaurantRule.setId("restaurant1");
        restaurantRule.setHeading("Main Restaurant");
        diningInfo.add(restaurantRule);

        foodDiningV2HES.setDiningInfo(diningInfo);

        UGCRatingData foodRatingData = null;
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean isAltAcco = false; // Not alt accommodation
        boolean isDhCall = false;
        boolean foodAndDiningEnhancement = true; // Enhancement enabled
        boolean isMealDetailsPresent = false;

        when(polyglotService.getTranslatedData("FOOD_DINING_CARD_TITLE")).thenReturn("Food & Dining");

        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningV2",
                foodDiningV2HES, foodRatingData, deviceDetails, isAltAcco, isDhCall, foodAndDiningEnhancement, isMealDetailsPresent);

        // Assert
        assertNotNull(result);
        assertEquals("Restaurants", result.getTitle()); // Should be changed to "Restaurants"
    }

    @Test
    public void buildFoodDiningV2_WhenSummaryIsPresent_ShouldBuildSummaryItems() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.FoodDiningV2 foodDiningV2HES =
            new com.mmt.hotels.model.response.staticdata.FoodDiningV2();

        List<com.mmt.hotels.model.response.staticdata.FoodDiningSummaryItem> summaryItems = new ArrayList<>();

        com.mmt.hotels.model.response.staticdata.FoodDiningSummaryItem summaryItem1 =
            new com.mmt.hotels.model.response.staticdata.FoodDiningSummaryItem();
        summaryItem1.setText("Vegetarian options available");
        summaryItem1.setIconUrl("veg_icon.png");
        summaryItems.add(summaryItem1);

        com.mmt.hotels.model.response.staticdata.FoodDiningSummaryItem summaryItem2 =
            new com.mmt.hotels.model.response.staticdata.FoodDiningSummaryItem();
        summaryItem2.setText("24/7 room service");
        summaryItem2.setIconUrl("room_service_icon.png");
        summaryItems.add(summaryItem2);

        foodDiningV2HES.setSummary(summaryItems);

        UGCRatingData foodRatingData = null;
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean isAltAcco = false;
        boolean isDhCall = false;
        boolean foodAndDiningEnhancement = false;
        boolean isMealDetailsPresent = false;

        when(polyglotService.getTranslatedData("FOOD_DINING_CARD_TITLE")).thenReturn("Food & Dining");

        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningV2",
                foodDiningV2HES, foodRatingData, deviceDetails, isAltAcco, isDhCall, foodAndDiningEnhancement, isMealDetailsPresent);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getSummary());
        assertEquals(2, result.getSummary().size());

        assertEquals("Vegetarian options available", result.getSummary().get(0).getText());
        assertEquals("veg_icon.png", result.getSummary().get(0).getIconUrl());

        assertEquals("24/7 room service", result.getSummary().get(1).getText());
        assertEquals("room_service_icon.png", result.getSummary().get(1).getIconUrl());
    }

    @Test
    public void buildFoodDiningV2_WhenSummaryHasEmptyTextOrIcon_ShouldHandleGracefully() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.FoodDiningV2 foodDiningV2HES =
            new com.mmt.hotels.model.response.staticdata.FoodDiningV2();

        List<com.mmt.hotels.model.response.staticdata.FoodDiningSummaryItem> summaryItems = new ArrayList<>();

        com.mmt.hotels.model.response.staticdata.FoodDiningSummaryItem summaryItem1 =
            new com.mmt.hotels.model.response.staticdata.FoodDiningSummaryItem();
        summaryItem1.setText(""); // Empty text
        summaryItem1.setIconUrl("icon.png");
        summaryItems.add(summaryItem1);

        com.mmt.hotels.model.response.staticdata.FoodDiningSummaryItem summaryItem2 =
            new com.mmt.hotels.model.response.staticdata.FoodDiningSummaryItem();
        summaryItem2.setText("Valid text");
        summaryItem2.setIconUrl(""); // Empty icon
        summaryItems.add(summaryItem2);

        foodDiningV2HES.setSummary(summaryItems);

        UGCRatingData foodRatingData = null;
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean isAltAcco = false;
        boolean isDhCall = false;
        boolean foodAndDiningEnhancement = false;
        boolean isMealDetailsPresent = false;

        when(polyglotService.getTranslatedData("FOOD_DINING_CARD_TITLE")).thenReturn("Food & Dining");

        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningV2",
                foodDiningV2HES, foodRatingData, deviceDetails, isAltAcco, isDhCall, foodAndDiningEnhancement, isMealDetailsPresent);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getSummary());
        assertEquals(2, result.getSummary().size());

        // First item should have empty text but valid icon
        assertNull(result.getSummary().get(0).getText());
        assertEquals("icon.png", result.getSummary().get(0).getIconUrl());

        // Second item should have valid text but empty icon
        assertEquals("Valid text", result.getSummary().get(1).getText());
        assertNull(result.getSummary().get(1).getIconUrl());
    }

    @Test
    public void buildFoodDiningV2_WhenDiningRuleHasRules_ShouldBuildDiningRuleItems() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.FoodDiningV2 foodDiningV2HES =
            new com.mmt.hotels.model.response.staticdata.FoodDiningV2();

        List<CommonRules> diningInfo = new ArrayList<>();
        CommonRules diningRule = new CommonRules();
        diningRule.setCategory("Dining Options");
        diningRule.setId("dining1");
        diningRule.setHeading("Restaurant Services");

        List<com.mmt.hotels.model.response.staticdata.Rule> rules = new ArrayList<>();
        com.mmt.hotels.model.response.staticdata.Rule rule1 = new com.mmt.hotels.model.response.staticdata.Rule();
        rule1.setTitleText("Breakfast");
        rule1.setText("Continental breakfast available");
        rule1.setIconUrl("breakfast_icon.png");
        rules.add(rule1);

        com.mmt.hotels.model.response.staticdata.Rule rule2 = new com.mmt.hotels.model.response.staticdata.Rule();
        rule2.setTitleText("Dinner");
        rule2.setText("Multi-cuisine dinner");
        rule2.setIconUrl("dinner_icon.png");
        rules.add(rule2);

        diningRule.setRules(rules);
        diningInfo.add(diningRule);

        foodDiningV2HES.setDiningInfo(diningInfo);

        UGCRatingData foodRatingData = null;
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean isAltAcco = false;
        boolean isDhCall = false;
        boolean foodAndDiningEnhancement = false;
        boolean isMealDetailsPresent = false;

        when(polyglotService.getTranslatedData("FOOD_DINING_CARD_TITLE")).thenReturn("Food & Dining");

        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningV2",
                foodDiningV2HES, foodRatingData, deviceDetails, isAltAcco, isDhCall, foodAndDiningEnhancement, isMealDetailsPresent);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getAllRules());
        assertEquals(1, result.getAllRules().size());

        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningRule foodDiningRule = result.getAllRules().get(0);
        assertNotNull(foodDiningRule.getRules());
        assertEquals(2, foodDiningRule.getRules().size());

        assertEquals("Breakfast", foodDiningRule.getRules().get(0).getTitleText());
        assertEquals("Continental breakfast available", foodDiningRule.getRules().get(0).getText());
        assertEquals("breakfast_icon.png", foodDiningRule.getRules().get(0).getIconUrl());

        assertEquals("Dinner", foodDiningRule.getRules().get(1).getTitleText());
        assertEquals("Multi-cuisine dinner", foodDiningRule.getRules().get(1).getText());
        assertEquals("dinner_icon.png", foodDiningRule.getRules().get(1).getIconUrl());
    }

    @Test
    public void buildFoodDiningV2_WhenMealDetailsArePresent_ShouldSetMergeIdOnRatingData() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.FoodDiningV2 foodDiningV2HES =
            new com.mmt.hotels.model.response.staticdata.FoodDiningV2();

        List<CommonRules> diningInfo = new ArrayList<>();
        CommonRules diningRule = new CommonRules();
        diningRule.setCategory("Dining Options");
        diningRule.setId("dining1");
        diningRule.setHeading("Restaurant Services");

        // Add meal details
        com.mmt.hotels.model.response.staticdata.meals.MealClarity mealDetails =
            new com.mmt.hotels.model.response.staticdata.meals.MealClarity();
        diningRule.setMealDetails(mealDetails);

        diningInfo.add(diningRule);
        foodDiningV2HES.setDiningInfo(diningInfo);

        UGCRatingData foodRatingData = new UGCRatingData();
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean isAltAcco = false;
        boolean isDhCall = false;
        boolean foodAndDiningEnhancement = false;
        boolean isMealDetailsPresent = false;

        when(polyglotService.getTranslatedData("FOOD_DINING_CARD_TITLE")).thenReturn("Food & Dining");

        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningV2",
                foodDiningV2HES, foodRatingData, deviceDetails, isAltAcco, isDhCall, foodAndDiningEnhancement, isMealDetailsPresent);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRatingData());
        assertEquals("dining1", result.getRatingData().getMergeId()); // Should be set to the dining rule ID
        verify(commonResponseTransformer).transformAndUpdateMealDetails(any(), eq(mealDetails));
    }

    @Test
    public void buildFoodDiningV2_WhenMultipleDiningRulesWithDifferentCategories_ShouldProcessAll() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.FoodDiningV2 foodDiningV2HES =
            new com.mmt.hotels.model.response.staticdata.FoodDiningV2();

        List<CommonRules> diningInfo = new ArrayList<>();

        // Add Kitchen rule (should be transformed)
        CommonRules kitchenRule = new CommonRules();
        kitchenRule.setCategory("Kitchen");
        kitchenRule.setId("kitchen1");
        kitchenRule.setHeading("Kitchen Facilities");
        diningInfo.add(kitchenRule);

        // Add Restaurant rule
        CommonRules restaurantRule = new CommonRules();
        restaurantRule.setCategory("Restaurants at the property");
        restaurantRule.setId("restaurant1");
        restaurantRule.setHeading("Main Restaurant");
        restaurantRule.setImages(Arrays.asList("restaurant.jpg"));
        diningInfo.add(restaurantRule);

        // Add regular dining rule
        CommonRules diningRule = new CommonRules();
        diningRule.setCategory("Dining Options");
        diningRule.setId("dining1");
        diningRule.setHeading("Dining Services");
        diningInfo.add(diningRule);

        foodDiningV2HES.setDiningInfo(diningInfo);

        UGCRatingData foodRatingData = null;
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean isAltAcco = false;
        boolean isDhCall = true; // Enable DH call for restaurant
        boolean foodAndDiningEnhancement = false;
        boolean isMealDetailsPresent = false;

        when(polyglotService.getTranslatedData("FOOD_DINING_CARD_TITLE")).thenReturn("Food & Dining");

        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningV2",
                foodDiningV2HES, foodRatingData, deviceDetails, isAltAcco, isDhCall, foodAndDiningEnhancement, isMealDetailsPresent);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getAllRules());
        assertEquals(3, result.getAllRules().size());

        // Check Kitchen rule transformation
        assertEquals("Kitchen Information", result.getAllRules().get(0).getCategory());
        assertEquals("kitchen1", result.getAllRules().get(0).getId());

        // Check Restaurant rule
        assertEquals("Restaurants at the property", result.getAllRules().get(1).getCategory());
        assertEquals("restaurant1", result.getAllRules().get(1).getId());
        assertFalse(result.getAllRules().get(1).isShowArrowInDetailHome()); // Should be false for DH call

        // Check regular dining rule
        assertEquals("Dining Options", result.getAllRules().get(2).getCategory());
        assertEquals("dining1", result.getAllRules().get(2).getId());

        // Check restaurants list
        assertNotNull(result.getRestaurants());
        assertEquals(1, result.getRestaurants().size());
        assertEquals("Main Restaurant", result.getRestaurants().get(0).getTitle());
    }

    @Test
    public void buildFoodDiningV2_WhenAllBooleanFlagsAreTrue_ShouldHandleCorrectly() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.FoodDiningV2 foodDiningV2HES =
            new com.mmt.hotels.model.response.staticdata.FoodDiningV2();

        List<CommonRules> diningInfo = new ArrayList<>();
        CommonRules restaurantRule = new CommonRules();
        restaurantRule.setCategory("Restaurants at the property");
        restaurantRule.setId("restaurant1");
        restaurantRule.setHeading("Main Restaurant");
        restaurantRule.setImages(Arrays.asList("restaurant.jpg"));
        diningInfo.add(restaurantRule);

        foodDiningV2HES.setDiningInfo(diningInfo);

        UGCRatingData foodRatingData = new UGCRatingData();
        RatingDetail ratingDetail = new RatingDetail();
        ratingDetail.setText("4.8");
        foodRatingData.setSummary(ratingDetail);

        DeviceDetails deviceDetails = new DeviceDetails();
        boolean isAltAcco = true;
        boolean isDhCall = true;
        boolean foodAndDiningEnhancement = true;
        boolean isMealDetailsPresent = true;

        when(polyglotService.getTranslatedData("FOOD_DINING_CARD_TITLE")).thenReturn("Food & Dining");

        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningV2",
                foodDiningV2HES, foodRatingData, deviceDetails, isAltAcco, isDhCall, foodAndDiningEnhancement, isMealDetailsPresent);

        // Assert
        assertNotNull(result);
        assertEquals("Food & Dining", result.getTitle()); // Should NOT change to "Restaurants" because isAltAcco is true
        assertTrue(result.isMealDetailsPresent());
        assertNotNull(result.getRatingData());
        assertEquals("4.8", result.getRatingData().getSummary().getText());
        assertNotNull(result.getRestaurants());
        assertEquals(1, result.getRestaurants().size());
    }

    @Test
    public void buildFoodDiningV2_WhenRestaurantWithoutImages_ShouldNotAddToRestaurantsList() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.FoodDiningV2 foodDiningV2HES =
            new com.mmt.hotels.model.response.staticdata.FoodDiningV2();

        List<CommonRules> diningInfo = new ArrayList<>();
        CommonRules restaurantRule = new CommonRules();
        restaurantRule.setCategory("Restaurants at the property");
        restaurantRule.setId("restaurant1");
        restaurantRule.setHeading("Main Restaurant");
        // No images set
        diningInfo.add(restaurantRule);

        foodDiningV2HES.setDiningInfo(diningInfo);

        UGCRatingData foodRatingData = null;
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean isAltAcco = false;
        boolean isDhCall = true; // DH call enabled but no images
        boolean foodAndDiningEnhancement = false;
        boolean isMealDetailsPresent = false;

        when(polyglotService.getTranslatedData("FOOD_DINING_CARD_TITLE")).thenReturn("Food & Dining");

        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningV2",
                foodDiningV2HES, foodRatingData, deviceDetails, isAltAcco, isDhCall, foodAndDiningEnhancement, isMealDetailsPresent);

        // Assert
        assertNotNull(result);
        assertNull(result.getRestaurants()); // Should be null because no images
        assertNotNull(result.getAllRules());
        assertEquals(1, result.getAllRules().size());
        assertTrue(result.getAllRules().get(0).isShowArrowInDetailHome()); // Should remain true
    }

    @Test
    public void buildFoodDiningV2_WhenNullRulesInDiningRule_ShouldHandleGracefully() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.FoodDiningV2 foodDiningV2HES =
            new com.mmt.hotels.model.response.staticdata.FoodDiningV2();

        List<CommonRules> diningInfo = new ArrayList<>();
        CommonRules diningRule = new CommonRules();
        diningRule.setCategory("Dining Options");
        diningRule.setId("dining1");
        diningRule.setHeading("Restaurant Services");
        diningRule.setRules(null); // Null rules
        diningInfo.add(diningRule);

        foodDiningV2HES.setDiningInfo(diningInfo);

        UGCRatingData foodRatingData = null;
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean isAltAcco = false;
        boolean isDhCall = false;
        boolean foodAndDiningEnhancement = false;
        boolean isMealDetailsPresent = false;

        when(polyglotService.getTranslatedData("FOOD_DINING_CARD_TITLE")).thenReturn("Food & Dining");

        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningV2",
                foodDiningV2HES, foodRatingData, deviceDetails, isAltAcco, isDhCall, foodAndDiningEnhancement, isMealDetailsPresent);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getAllRules());
        assertEquals(1, result.getAllRules().size());

        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningRule foodDiningRule = result.getAllRules().get(0);
        assertNull(foodDiningRule.getRules()); // Should be null
        assertEquals("dining1", foodDiningRule.getId());
        assertEquals("Dining Options", foodDiningRule.getCategory());
    }

    @Test
    public void buildFoodDiningV2_WhenEmptyRulesInDiningRule_ShouldSetRulesToNull() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.FoodDiningV2 foodDiningV2HES =
            new com.mmt.hotels.model.response.staticdata.FoodDiningV2();

        List<CommonRules> diningInfo = new ArrayList<>();
        CommonRules diningRule = new CommonRules();
        diningRule.setCategory("Dining Options");
        diningRule.setId("dining1");
        diningRule.setHeading("Restaurant Services");
        diningRule.setRules(new ArrayList<>()); // Empty rules list
        diningInfo.add(diningRule);

        foodDiningV2HES.setDiningInfo(diningInfo);

        UGCRatingData foodRatingData = null;
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean isAltAcco = false;
        boolean isDhCall = false;
        boolean foodAndDiningEnhancement = false;
        boolean isMealDetailsPresent = false;

        when(polyglotService.getTranslatedData("FOOD_DINING_CARD_TITLE")).thenReturn("Food & Dining");

        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningV2",
                foodDiningV2HES, foodRatingData, deviceDetails, isAltAcco, isDhCall, foodAndDiningEnhancement, isMealDetailsPresent);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getAllRules());
        assertEquals(1, result.getAllRules().size());

        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningRule foodDiningRule = result.getAllRules().get(0);
        assertNull(foodDiningRule.getRules()); // Should be null for empty list
    }

    @Test
    public void buildFoodDiningV2_WhenComplexScenarioWithAllFeatures_ShouldWorkCorrectly() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.FoodDiningV2 foodDiningV2HES =
            new com.mmt.hotels.model.response.staticdata.FoodDiningV2();

        // Set up dining info with multiple categories
        List<CommonRules> diningInfo = new ArrayList<>();

        // Kitchen rule
        CommonRules kitchenRule = new CommonRules();
        kitchenRule.setCategory("Kitchen");
        kitchenRule.setId("kitchen1");
        kitchenRule.setHeading("Kitchen Facilities");
        kitchenRule.setDescription("Fully equipped kitchen");
        kitchenRule.setShowInL2Page(true);
        kitchenRule.setShowInDetailHome(true);
        diningInfo.add(kitchenRule);

        // Restaurant rule with images and meal details
        CommonRules restaurantRule = new CommonRules();
        restaurantRule.setCategory("Restaurants at the property");
        restaurantRule.setId("restaurant1");
        restaurantRule.setHeading("Main Restaurant");
        restaurantRule.setImages(Arrays.asList("restaurant1.jpg", "restaurant2.jpg"));

        com.mmt.hotels.model.response.staticdata.meals.MealClarity mealDetails =
            new com.mmt.hotels.model.response.staticdata.meals.MealClarity();
        restaurantRule.setMealDetails(mealDetails);

        List<com.mmt.hotels.model.response.staticdata.Rule> restaurantRules = new ArrayList<>();
        com.mmt.hotels.model.response.staticdata.Rule rule1 = new com.mmt.hotels.model.response.staticdata.Rule();
        rule1.setTitleText("Cuisine");
        rule1.setText("Multi-cuisine restaurant");
        rule1.setIconUrl("cuisine_icon.png");
        restaurantRules.add(rule1);
        restaurantRule.setRules(restaurantRules);

        diningInfo.add(restaurantRule);

        foodDiningV2HES.setDiningInfo(diningInfo);

        // Set up summary
        List<com.mmt.hotels.model.response.staticdata.FoodDiningSummaryItem> summaryItems = new ArrayList<>();
        com.mmt.hotels.model.response.staticdata.FoodDiningSummaryItem summaryItem =
            new com.mmt.hotels.model.response.staticdata.FoodDiningSummaryItem();
        summaryItem.setText("Vegetarian options available");
        summaryItem.setIconUrl("veg_icon.png");
        summaryItems.add(summaryItem);
        foodDiningV2HES.setSummary(summaryItems);

        // Set up rating data
        UGCRatingData foodRatingData = new UGCRatingData();
        RatingDetail ratingDetail = new RatingDetail();
        ratingDetail.setText("4.5");
        foodRatingData.setSummary(ratingDetail);

        DeviceDetails deviceDetails = new DeviceDetails();
        boolean isAltAcco = false;
        boolean isDhCall = true;
        boolean foodAndDiningEnhancement = true;
        boolean isMealDetailsPresent = true;

        when(polyglotService.getTranslatedData("FOOD_DINING_CARD_TITLE")).thenReturn("Food & Dining");

        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningV2",
                foodDiningV2HES, foodRatingData, deviceDetails, isAltAcco, isDhCall, foodAndDiningEnhancement, isMealDetailsPresent);

        // Assert
        assertNotNull(result);

        // Check title (should be "Restaurants" due to restaurant category and foodAndDiningEnhancement)
        assertEquals("Restaurants", result.getTitle());

        // Check meal details present
        assertTrue(result.isMealDetailsPresent());

        // Check rating data
        assertNotNull(result.getRatingData());
        assertEquals("4.5", result.getRatingData().getSummary().getText());
        assertEquals("restaurant1", result.getRatingData().getMergeId()); // Should be set from restaurant rule

        // Check all rules
        assertNotNull(result.getAllRules());
        assertEquals(2, result.getAllRules().size());

        // Check kitchen rule transformation
        assertEquals("Kitchen Information", result.getAllRules().get(0).getCategory());
        assertEquals("kitchen1", result.getAllRules().get(0).getId());

        // Check restaurant rule
        assertEquals("Restaurants at the property", result.getAllRules().get(1).getCategory());
        assertEquals("restaurant1", result.getAllRules().get(1).getId());
        assertFalse(result.getAllRules().get(1).isShowArrowInDetailHome()); // Should be false for DH call
        assertNotNull(result.getAllRules().get(1).getRules());
        assertEquals(1, result.getAllRules().get(1).getRules().size());

        // Check restaurants list
        assertNotNull(result.getRestaurants());
        assertEquals(1, result.getRestaurants().size());
        assertEquals("Main Restaurant", result.getRestaurants().get(0).getTitle());
        assertEquals("restaurant1.jpg", result.getRestaurants().get(0).getImageUrl());
        assertEquals("restaurant1", result.getRestaurants().get(0).getCategoryId());

        // Check summary
        assertNotNull(result.getSummary());
        assertEquals(1, result.getSummary().size());
        assertEquals("Vegetarian options available", result.getSummary().get(0).getText());
        assertEquals("veg_icon.png", result.getSummary().get(0).getIconUrl());

        // Check feedback data
        assertNotNull(result.getFeedbackData());

        // Verify meal details building was called
        verify(commonResponseTransformer).transformAndUpdateMealDetails(any(), eq(mealDetails));
    }

    // ==================== End of buildFoodDiningV2 Test Methods ====================

    // ==================== Start of buildFoodDiningV2RuleItem Test Methods ====================

    @Test
    public void buildFoodDiningV2RuleItem_WhenRuleHESIsNull_ShouldReturnNull() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.Rule ruleHES = null;

        // Act
        DiningRuleItem result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDiningV2RuleItem", ruleHES);

        // Assert
        assertNull(result);
    }

    @Test
    public void buildFoodDiningV2RuleItem_WhenBasicRuleProvided_ShouldCreateDiningRuleItem() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.Rule ruleHES = new com.mmt.hotels.model.response.staticdata.Rule();
        ruleHES.setTitleText("Breakfast Service");
        ruleHES.setText("Continental breakfast available from 7 AM to 10 AM");
        ruleHES.setIconUrl("breakfast_icon.png");

        // Act
        DiningRuleItem result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDiningV2RuleItem", ruleHES);

        // Assert
        assertNotNull(result);
        assertEquals("Breakfast Service", result.getTitleText());
        assertEquals("Continental breakfast available from 7 AM to 10 AM", result.getText());
        assertEquals("breakfast_icon.png", result.getIconUrl());
        assertNull(result.getMealTypes()); // No meal types provided
    }

    @Test
    public void buildFoodDiningV2RuleItem_WhenRuleWithMealTypes_ShouldConvertMealTypes() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.Rule ruleHES = new com.mmt.hotels.model.response.staticdata.Rule();
        ruleHES.setTitleText("Dining Options");
        ruleHES.setText("Multiple dining options available");
        ruleHES.setIconUrl("dining_icon.png");

        List<com.mmt.hotels.model.response.staticdata.meals.MealType> hesMealTypes = new ArrayList<>();

        com.mmt.hotels.model.response.staticdata.meals.MealType mealType1 =
            new com.mmt.hotels.model.response.staticdata.meals.MealType();
        mealType1.setName("Breakfast");
        mealType1.setIcon("breakfast.png");
        hesMealTypes.add(mealType1);

        com.mmt.hotels.model.response.staticdata.meals.MealType mealType2 =
            new com.mmt.hotels.model.response.staticdata.meals.MealType();
        mealType2.setName("Lunch");
        mealType2.setIcon("lunch.png");
        hesMealTypes.add(mealType2);

        ruleHES.setMealTypes(hesMealTypes);

        // Act
        DiningRuleItem result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDiningV2RuleItem", ruleHES);

        // Assert
        assertNotNull(result);
        assertEquals("Dining Options", result.getTitleText());
        assertEquals("Multiple dining options available", result.getText());
        assertEquals("dining_icon.png", result.getIconUrl());
        assertNotNull(result.getMealTypes());
        assertEquals(2, result.getMealTypes().size());

        assertEquals("Breakfast", result.getMealTypes().get(0).getName());
        assertEquals("breakfast.png", result.getMealTypes().get(0).getIcon());
        assertEquals("Lunch", result.getMealTypes().get(1).getName());
        assertEquals("lunch.png", result.getMealTypes().get(1).getIcon());
    }

    @Test
    public void buildFoodDiningV2RuleItem_WhenEmptyMealTypesList_ShouldNotSetMealTypes() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.Rule ruleHES = new com.mmt.hotels.model.response.staticdata.Rule();
        ruleHES.setTitleText("Restaurant Info");
        ruleHES.setText("Restaurant information");
        ruleHES.setIconUrl("restaurant_icon.png");
        ruleHES.setMealTypes(new ArrayList<>()); // Empty list

        // Act
        DiningRuleItem result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDiningV2RuleItem", ruleHES);

        // Assert
        assertNotNull(result);
        assertEquals("Restaurant Info", result.getTitleText());
        assertEquals("Restaurant information", result.getText());
        assertEquals("restaurant_icon.png", result.getIconUrl());
        assertNull(result.getMealTypes()); // Should be null for empty list
    }

    @Test
    public void buildFoodDiningV2RuleItem_WhenNullMealTypesList_ShouldNotSetMealTypes() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.Rule ruleHES = new com.mmt.hotels.model.response.staticdata.Rule();
        ruleHES.setTitleText("Kitchen Facilities");
        ruleHES.setText("Fully equipped kitchen available");
        ruleHES.setIconUrl("kitchen_icon.png");
        ruleHES.setMealTypes(null); // Null list

        // Act
        DiningRuleItem result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDiningV2RuleItem", ruleHES);

        // Assert
        assertNotNull(result);
        assertEquals("Kitchen Facilities", result.getTitleText());
        assertEquals("Fully equipped kitchen available", result.getText());
        assertEquals("kitchen_icon.png", result.getIconUrl());
        assertNull(result.getMealTypes()); // Should be null
    }

    @Test
    public void buildFoodDiningV2RuleItem_WhenMealTypesContainNullItems_ShouldFilterNullItems() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.Rule ruleHES = new com.mmt.hotels.model.response.staticdata.Rule();
        ruleHES.setTitleText("Meal Services");
        ruleHES.setText("Various meal services available");
        ruleHES.setIconUrl("meal_icon.png");

        List<com.mmt.hotels.model.response.staticdata.meals.MealType> hesMealTypes = new ArrayList<>();

        com.mmt.hotels.model.response.staticdata.meals.MealType validMealType =
            new com.mmt.hotels.model.response.staticdata.meals.MealType();
        validMealType.setName("Dinner");
        validMealType.setIcon("dinner.png");
        hesMealTypes.add(validMealType);

        hesMealTypes.add(null); // Null meal type

        com.mmt.hotels.model.response.staticdata.meals.MealType anotherValidMealType =
            new com.mmt.hotels.model.response.staticdata.meals.MealType();
        anotherValidMealType.setName("Snacks");
        anotherValidMealType.setIcon("snacks.png");
        hesMealTypes.add(anotherValidMealType);

        ruleHES.setMealTypes(hesMealTypes);

        // Act
        DiningRuleItem result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDiningV2RuleItem", ruleHES);

        // Assert
        assertNotNull(result);
        assertEquals("Meal Services", result.getTitleText());
        assertEquals("Various meal services available", result.getText());
        assertEquals("meal_icon.png", result.getIconUrl());
        assertNotNull(result.getMealTypes());
        assertEquals(2, result.getMealTypes().size()); // Should only contain non-null items

        assertEquals("Dinner", result.getMealTypes().get(0).getName());
        assertEquals("dinner.png", result.getMealTypes().get(0).getIcon());
        assertEquals("Snacks", result.getMealTypes().get(1).getName());
        assertEquals("snacks.png", result.getMealTypes().get(1).getIcon());
    }

    @Test
    public void buildFoodDiningV2RuleItem_WhenOnlyTitleTextProvided_ShouldCreatePartialDiningRuleItem() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.Rule ruleHES = new com.mmt.hotels.model.response.staticdata.Rule();
        ruleHES.setTitleText("Special Offers");
        // text and iconUrl are null

        // Act
        DiningRuleItem result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDiningV2RuleItem", ruleHES);

        // Assert
        assertNotNull(result);
        assertEquals("Special Offers", result.getTitleText());
        assertNull(result.getText());
        assertNull(result.getIconUrl());
        assertNull(result.getMealTypes());
    }

    @Test
    public void buildFoodDiningV2RuleItem_WhenOnlyTextProvided_ShouldCreatePartialDiningRuleItem() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.Rule ruleHES = new com.mmt.hotels.model.response.staticdata.Rule();
        ruleHES.setText("Room service available 24/7");
        // titleText and iconUrl are null

        // Act
        DiningRuleItem result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDiningV2RuleItem", ruleHES);

        // Assert
        assertNotNull(result);
        assertNull(result.getTitleText());
        assertEquals("Room service available 24/7", result.getText());
        assertNull(result.getIconUrl());
        assertNull(result.getMealTypes());
    }

    @Test
    public void buildFoodDiningV2RuleItem_WhenOnlyIconUrlProvided_ShouldCreatePartialDiningRuleItem() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.Rule ruleHES = new com.mmt.hotels.model.response.staticdata.Rule();
        ruleHES.setIconUrl("service_icon.png");
        // titleText and text are null

        // Act
        DiningRuleItem result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDiningV2RuleItem", ruleHES);

        // Assert
        assertNotNull(result);
        assertNull(result.getTitleText());
        assertNull(result.getText());
        assertEquals("service_icon.png", result.getIconUrl());
        assertNull(result.getMealTypes());
    }

    @Test
    public void buildFoodDiningV2RuleItem_WhenAllFieldsAreNull_ShouldCreateEmptyDiningRuleItem() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.Rule ruleHES = new com.mmt.hotels.model.response.staticdata.Rule();
        // All fields are null by default

        // Act
        DiningRuleItem result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDiningV2RuleItem", ruleHES);

        // Assert
        assertNotNull(result);
        assertNull(result.getTitleText());
        assertNull(result.getText());
        assertNull(result.getIconUrl());
        assertNull(result.getMealTypes());
    }

    @Test
    public void buildFoodDiningV2RuleItem_WhenMealTypesHavePartialData_ShouldHandlePartialData() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.Rule ruleHES = new com.mmt.hotels.model.response.staticdata.Rule();
        ruleHES.setTitleText("Beverage Service");
        ruleHES.setText("Hot and cold beverages available");
        ruleHES.setIconUrl("beverage_icon.png");

        List<com.mmt.hotels.model.response.staticdata.meals.MealType> hesMealTypes = new ArrayList<>();

        // Meal type with only name
        com.mmt.hotels.model.response.staticdata.meals.MealType mealTypeOnlyName =
            new com.mmt.hotels.model.response.staticdata.meals.MealType();
        mealTypeOnlyName.setName("Tea");
        // icon is null
        hesMealTypes.add(mealTypeOnlyName);

        // Meal type with only icon
        com.mmt.hotels.model.response.staticdata.meals.MealType mealTypeOnlyIcon =
            new com.mmt.hotels.model.response.staticdata.meals.MealType();
        mealTypeOnlyIcon.setIcon("coffee.png");
        // name is null
        hesMealTypes.add(mealTypeOnlyIcon);

        // Meal type with both fields
        com.mmt.hotels.model.response.staticdata.meals.MealType mealTypeBothFields =
            new com.mmt.hotels.model.response.staticdata.meals.MealType();
        mealTypeBothFields.setName("Juice");
        mealTypeBothFields.setIcon("juice.png");
        hesMealTypes.add(mealTypeBothFields);

        ruleHES.setMealTypes(hesMealTypes);

        // Act
        DiningRuleItem result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDiningV2RuleItem", ruleHES);

        // Assert
        assertNotNull(result);
        assertEquals("Beverage Service", result.getTitleText());
        assertEquals("Hot and cold beverages available", result.getText());
        assertEquals("beverage_icon.png", result.getIconUrl());
        assertNotNull(result.getMealTypes());
        assertEquals(3, result.getMealTypes().size());

        // First meal type - only name
        assertEquals("Tea", result.getMealTypes().get(0).getName());
        assertNull(result.getMealTypes().get(0).getIcon());

        // Second meal type - only icon
        assertNull(result.getMealTypes().get(1).getName());
        assertEquals("coffee.png", result.getMealTypes().get(1).getIcon());

        // Third meal type - both fields
        assertEquals("Juice", result.getMealTypes().get(2).getName());
        assertEquals("juice.png", result.getMealTypes().get(2).getIcon());
    }

    @Test
    public void buildFoodDiningV2RuleItem_WhenAllMealTypesAreNull_ShouldNotSetMealTypes() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.Rule ruleHES = new com.mmt.hotels.model.response.staticdata.Rule();
        ruleHES.setTitleText("Catering Service");
        ruleHES.setText("Professional catering available");
        ruleHES.setIconUrl("catering_icon.png");

        List<com.mmt.hotels.model.response.staticdata.meals.MealType> hesMealTypes = new ArrayList<>();
        hesMealTypes.add(null);
        hesMealTypes.add(null);
        hesMealTypes.add(null);

        ruleHES.setMealTypes(hesMealTypes);

        // Act
        DiningRuleItem result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDiningV2RuleItem", ruleHES);

        // Assert
        assertNotNull(result);
        assertEquals("Catering Service", result.getTitleText());
        assertEquals("Professional catering available", result.getText());
        assertEquals("catering_icon.png", result.getIconUrl());
        assertNull(result.getMealTypes()); // Should be null since all meal types were null
    }

    @Test
    public void buildFoodDiningV2RuleItem_WhenComprehensiveDataProvided_ShouldCreateCompleteRuleItem() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.Rule ruleHES = new com.mmt.hotels.model.response.staticdata.Rule();
        ruleHES.setTitleText("Complete Dining Experience");
        ruleHES.setText("Full-service dining with multiple cuisines and meal options available throughout the day");
        ruleHES.setIconUrl("complete_dining_icon.png");

        List<com.mmt.hotels.model.response.staticdata.meals.MealType> hesMealTypes = new ArrayList<>();

        // Add multiple meal types with complete data
        String[] mealNames = {"Breakfast", "Lunch", "Dinner", "Snacks", "Beverages"};
        String[] mealIcons = {"breakfast.png", "lunch.png", "dinner.png", "snacks.png", "beverages.png"};

        for (int i = 0; i < mealNames.length; i++) {
            com.mmt.hotels.model.response.staticdata.meals.MealType mealType =
                new com.mmt.hotels.model.response.staticdata.meals.MealType();
            mealType.setName(mealNames[i]);
            mealType.setIcon(mealIcons[i]);
            hesMealTypes.add(mealType);
        }

        ruleHES.setMealTypes(hesMealTypes);

        // Act
        DiningRuleItem result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDiningV2RuleItem", ruleHES);

        // Assert
        assertNotNull(result);
        assertEquals("Complete Dining Experience", result.getTitleText());
        assertEquals("Full-service dining with multiple cuisines and meal options available throughout the day", result.getText());
        assertEquals("complete_dining_icon.png", result.getIconUrl());
        assertNotNull(result.getMealTypes());
        assertEquals(5, result.getMealTypes().size());

        // Verify all meal types are correctly converted
        for (int i = 0; i < mealNames.length; i++) {
            assertEquals(mealNames[i], result.getMealTypes().get(i).getName());
            assertEquals(mealIcons[i], result.getMealTypes().get(i).getIcon());
        }
    }

    @Test
    public void buildFoodDiningV2RuleItem_WhenEmptyStringsProvided_ShouldHandleEmptyStrings() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.Rule ruleHES = new com.mmt.hotels.model.response.staticdata.Rule();
        ruleHES.setTitleText(""); // Empty string
        ruleHES.setText(""); // Empty string
        ruleHES.setIconUrl(""); // Empty string

        List<com.mmt.hotels.model.response.staticdata.meals.MealType> hesMealTypes = new ArrayList<>();

        com.mmt.hotels.model.response.staticdata.meals.MealType mealType =
            new com.mmt.hotels.model.response.staticdata.meals.MealType();
        mealType.setName(""); // Empty string
        mealType.setIcon(""); // Empty string
        hesMealTypes.add(mealType);

        ruleHES.setMealTypes(hesMealTypes);

        // Act
        DiningRuleItem result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDiningV2RuleItem", ruleHES);

        // Assert
        assertNotNull(result);
        assertEquals("", result.getTitleText());
        assertEquals("", result.getText());
        assertEquals("", result.getIconUrl());
        assertNotNull(result.getMealTypes());
        assertEquals(1, result.getMealTypes().size());
        assertEquals("", result.getMealTypes().get(0).getName());
        assertEquals("", result.getMealTypes().get(0).getIcon());
    }

    // ==================== End of buildFoodDiningV2RuleItem Test Methods ====================

    // ==================== buildFoodDiningFeedbackData Test Methods ====================

    @Test
    public void buildFoodDiningFeedbackData_ShouldCreateCompleteCardActionStructure() {
        // Arrange
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_CARD_TITLE")).thenReturn("Feedback");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_TITLE")).thenReturn("Help us improve");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_DESCRIPTION")).thenReturn("Why are you not interested in this section?");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_ONE")).thenReturn("The list was very long");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_TWO")).thenReturn("Did not find the amenity I was looking for");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_THREE")).thenReturn("My reason is not listed here");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_TEXT_BOX_TITLE")).thenReturn("Please specify");

        // Act
        com.mmt.hotels.clientgateway.response.moblanding.CardAction result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningFeedbackData");

        // Assert
        assertNotNull(result);
        assertEquals("Feedback", result.getTitle());

        assertNotNull(result.getData());
        assertEquals("Help us improve", result.getData().getTitle());

        assertNotNull(result.getData().getSections());
        assertEquals(1, result.getData().getSections().size());

        com.mmt.hotels.clientgateway.response.moblanding.Section section = result.getData().getSections().get(0);
        assertEquals("Why are you not interested in this section?", section.getTitle());

        assertNotNull(section.getItems());
        assertEquals(3, section.getItems().size());
    }

    @Test
    public void buildFoodDiningFeedbackData_ShouldCreateCorrectItemTypes() {
        // Arrange
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_CARD_TITLE")).thenReturn("Feedback");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_TITLE")).thenReturn("Help us improve");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_DESCRIPTION")).thenReturn("Why are you not interested?");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_ONE")).thenReturn("The list was very long");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_TWO")).thenReturn("Did not find the amenity I was looking for");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_THREE")).thenReturn("My reason is not listed here");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_TEXT_BOX_TITLE")).thenReturn("Please specify");

        // Act
        com.mmt.hotels.clientgateway.response.moblanding.CardAction result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningFeedbackData");

        // Assert
        assertNotNull(result);
        List<com.mmt.hotels.clientgateway.response.moblanding.Item> items = result.getData().getSections().get(0).getItems();

        // First item should be text type
        assertEquals("The list was very long", items.get(0).getText());
        assertEquals("text", items.get(0).getType());
        assertNull(items.get(0).getTextBoxTitle());

        // Second item should be text type
        assertEquals("Did not find the amenity I was looking for", items.get(1).getText());
        assertEquals("text", items.get(1).getType());
        assertNull(items.get(1).getTextBoxTitle());

        // Third item should be textBox type
        assertEquals("My reason is not listed here", items.get(2).getText());
        assertEquals("textBox", items.get(2).getType());
        assertEquals("Please specify", items.get(2).getTextBoxTitle());
    }

    @Test
    public void buildFoodDiningFeedbackData_WhenTranslationsAreNull_ShouldHandleGracefully() {
        // Arrange
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_CARD_TITLE")).thenReturn(null);
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_TITLE")).thenReturn(null);
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_DESCRIPTION")).thenReturn(null);
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_ONE")).thenReturn(null);
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_TWO")).thenReturn(null);
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_THREE")).thenReturn(null);
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_TEXT_BOX_TITLE")).thenReturn(null);

        // Act
        com.mmt.hotels.clientgateway.response.moblanding.CardAction result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningFeedbackData");

        // Assert
        assertNotNull(result);
        assertNull(result.getTitle());

        assertNotNull(result.getData());
        assertNull(result.getData().getTitle());

        assertNotNull(result.getData().getSections());
        assertEquals(1, result.getData().getSections().size());

        com.mmt.hotels.clientgateway.response.moblanding.Section section = result.getData().getSections().get(0);
        assertNull(section.getTitle());

        assertNotNull(section.getItems());
        assertEquals(3, section.getItems().size());

        // All items should have null text but correct types
        assertNull(section.getItems().get(0).getText());
        assertEquals("text", section.getItems().get(0).getType());

        assertNull(section.getItems().get(1).getText());
        assertEquals("text", section.getItems().get(1).getType());

        assertNull(section.getItems().get(2).getText());
        assertEquals("textBox", section.getItems().get(2).getType());
        assertNull(section.getItems().get(2).getTextBoxTitle());
    }

    @Test
    public void buildFoodDiningFeedbackData_WhenTranslationsAreEmpty_ShouldHandleGracefully() {
        // Arrange
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_CARD_TITLE")).thenReturn("");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_TITLE")).thenReturn("");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_DESCRIPTION")).thenReturn("");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_ONE")).thenReturn("");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_TWO")).thenReturn("");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_THREE")).thenReturn("");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_TEXT_BOX_TITLE")).thenReturn("");

        // Act
        com.mmt.hotels.clientgateway.response.moblanding.CardAction result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningFeedbackData");

        // Assert
        assertNotNull(result);
        assertEquals("", result.getTitle());

        assertNotNull(result.getData());
        assertEquals("", result.getData().getTitle());

        assertNotNull(result.getData().getSections());
        assertEquals(1, result.getData().getSections().size());

        com.mmt.hotels.clientgateway.response.moblanding.Section section = result.getData().getSections().get(0);
        assertEquals("", section.getTitle());

        assertNotNull(section.getItems());
        assertEquals(3, section.getItems().size());

        // All items should have empty text but correct types
        assertEquals("", section.getItems().get(0).getText());
        assertEquals("text", section.getItems().get(0).getType());

        assertEquals("", section.getItems().get(1).getText());
        assertEquals("text", section.getItems().get(1).getType());

        assertEquals("", section.getItems().get(2).getText());
        assertEquals("textBox", section.getItems().get(2).getType());
        assertEquals("", section.getItems().get(2).getTextBoxTitle());
    }

    @Test
    public void buildFoodDiningFeedbackData_ShouldUseCorrectTranslationConstants() {
        // Arrange
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_CARD_TITLE")).thenReturn("Test Title");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_TITLE")).thenReturn("Test Sheet Title");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_DESCRIPTION")).thenReturn("Test Description");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_ONE")).thenReturn("Test Reason 1");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_TWO")).thenReturn("Test Reason 2");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_THREE")).thenReturn("Test Reason 3");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_TEXT_BOX_TITLE")).thenReturn("Test TextBox Title");

        // Act
        com.mmt.hotels.clientgateway.response.moblanding.CardAction result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningFeedbackData");

        // Assert - Verify all translation constants are called
        verify(polyglotService).getTranslatedData("FOOD_DINING_FEEDBACK_CARD_TITLE");
        verify(polyglotService).getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_TITLE");
        verify(polyglotService).getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_DESCRIPTION");
        verify(polyglotService).getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_ONE");
        verify(polyglotService).getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_TWO");
        verify(polyglotService).getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_THREE");
        verify(polyglotService).getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_TEXT_BOX_TITLE");

        // Verify the translated values are set correctly
        assertNotNull(result);
        assertEquals("Test Title", result.getTitle());
        assertEquals("Test Sheet Title", result.getData().getTitle());
        assertEquals("Test Description", result.getData().getSections().get(0).getTitle());
        assertEquals("Test Reason 1", result.getData().getSections().get(0).getItems().get(0).getText());
        assertEquals("Test Reason 2", result.getData().getSections().get(0).getItems().get(1).getText());
        assertEquals("Test Reason 3", result.getData().getSections().get(0).getItems().get(2).getText());
        assertEquals("Test TextBox Title", result.getData().getSections().get(0).getItems().get(2).getTextBoxTitle());
    }

    @Test
    public void buildFoodDiningFeedbackData_ShouldCreateNonNullObjectsEvenWithNullTranslations() {
        // Arrange
        when(polyglotService.getTranslatedData(anyString())).thenReturn(null);

        // Act
        com.mmt.hotels.clientgateway.response.moblanding.CardAction result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningFeedbackData");

        // Assert - Verify structure is created even with null translations
        assertNotNull(result);
        assertNotNull(result.getData());
        assertNotNull(result.getData().getSections());
        assertFalse(result.getData().getSections().isEmpty());
        assertNotNull(result.getData().getSections().get(0));
        assertNotNull(result.getData().getSections().get(0).getItems());
        assertEquals("Should have exactly 3 items", 3, result.getData().getSections().get(0).getItems().size());

        // Verify all items are created
        for (int i = 0; i < 3; i++) {
            assertNotNull(result.getData().getSections().get(0).getItems().get(i));
        }
    }

    @Test
    public void buildFoodDiningFeedbackData_ShouldSetCorrectItemTypesRegardlessOfTranslations() {
        // Arrange - Mix of null, empty, and valid translations
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_CARD_TITLE")).thenReturn("Valid Title");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_TITLE")).thenReturn(null);
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_DESCRIPTION")).thenReturn("");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_ONE")).thenReturn("Valid Reason 1");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_TWO")).thenReturn(null);
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_THREE")).thenReturn("");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_TEXT_BOX_TITLE")).thenReturn("Valid TextBox Title");

        // Act
        com.mmt.hotels.clientgateway.response.moblanding.CardAction result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningFeedbackData");

        // Assert - Item types should be correct regardless of translation values
        List<com.mmt.hotels.clientgateway.response.moblanding.Item> items = result.getData().getSections().get(0).getItems();

        assertEquals("First item should be text type", "text", items.get(0).getType());
        assertEquals("Second item should be text type", "text", items.get(1).getType());
        assertEquals("Third item should be textBox type", "textBox", items.get(2).getType());

        // Verify textBoxTitle is only set for the third item
        assertNull(items.get(0).getTextBoxTitle());
        assertNull(items.get(1).getTextBoxTitle());
        assertEquals("Third item should have textBoxTitle", "Valid TextBox Title", items.get(2).getTextBoxTitle());
    }

    @Test
    public void buildFoodDiningFeedbackData_ShouldCreateExactlyThreeItems() {
        // Arrange
        when(polyglotService.getTranslatedData(anyString())).thenReturn("Test Value");

        // Act
        com.mmt.hotels.clientgateway.response.moblanding.CardAction result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningFeedbackData");

        // Assert
        assertNotNull(result);
        assertNotNull(result.getData());
        assertNotNull(result.getData().getSections());
        assertEquals("Should have exactly one section", 1, result.getData().getSections().size());

        com.mmt.hotels.clientgateway.response.moblanding.Section section = result.getData().getSections().get(0);
        assertNotNull(section.getItems());
        assertEquals("Should have exactly three items", 3, section.getItems().size());

        // Verify each item is properly initialized
        for (int i = 0; i < 3; i++) {
            com.mmt.hotels.clientgateway.response.moblanding.Item item = section.getItems().get(i);
            assertNotNull(item);
            assertNotNull(item.getType());
            assertNotNull(item.getText());
        }
    }

    @Test
    public void buildFoodDiningFeedbackData_ShouldCreateExactlyOneSectionWithCorrectStructure() {
        // Arrange
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_CARD_TITLE")).thenReturn("Feedback Card");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_TITLE")).thenReturn("Sheet Title");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_DESCRIPTION")).thenReturn("Section Description");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_ONE")).thenReturn("Reason 1");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_TWO")).thenReturn("Reason 2");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_REASON_THREE")).thenReturn("Reason 3");
        when(polyglotService.getTranslatedData("FOOD_DINING_FEEDBACK_SHEET_TEXT_BOX_TITLE")).thenReturn("TextBox Title");

        // Act
        com.mmt.hotels.clientgateway.response.moblanding.CardAction result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildFoodDiningFeedbackData");

        // Assert
        assertNotNull(result);
        assertEquals("Feedback Card", result.getTitle());

        assertNotNull(result.getData());
        assertEquals("Sheet Title", result.getData().getTitle());

        assertNotNull(result.getData().getSections());
        assertEquals("Should have exactly one section", 1, result.getData().getSections().size());

        com.mmt.hotels.clientgateway.response.moblanding.Section section = result.getData().getSections().get(0);
        assertEquals("Section Description", section.getTitle());
        assertNotNull(section.getItems());
        assertEquals(3, section.getItems().size());
    }

    // ==================== End of buildFoodDiningFeedbackData Test Methods ====================

    @Test
    public void buildCategoryCommonRules_WhenNoRuleTableInfo_AndNotLanguageCategory_ShouldReturnNull() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.CategoryInfo categoryInfo = new com.mmt.hotels.model.response.staticdata.CategoryInfo();
        categoryInfo.setId("BREAKFAST_CHARGES");
        categoryInfo.setCategoryName("Breakfast Charges");
        categoryInfo.setRuleDesc(Arrays.asList("Complimentary breakfast"));


        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.CommonRules result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildCategoryCommonRules",
                categoryInfo);

        // Assert
        assertNull(result);
    }

    @Test
    public void buildHouseRulesV2_WhenLanguagesSpokenAndIhHouseRuleUiV2Disabled_ShouldIncludeLanguages() {
        // Arrange
        com.mmt.hotels.clientgateway.response.staticdetail.HouseRules houseRules = new com.mmt.hotels.clientgateway.response.staticdetail.HouseRules();
        List<com.mmt.hotels.model.response.staticdata.CommonRules> foodAndDiningRule = new ArrayList<>();
        List<String> spokenLanguages = Arrays.asList("English", "Hindi", "French");
        com.mmt.hotels.model.response.staticdata.HouseRules houseRulesCB = new com.mmt.hotels.model.response.staticdata.HouseRules();
        com.mmt.hotels.model.response.staticdata.DepositPolicy depositPolicy = null;
        boolean isInternationalHotel = false;
        Map<String, String> expDataMap = new HashMap<>();

        when(polyglotService.getTranslatedData("SPOKEN_LANGUAGE_HEADER")).thenReturn("Languages Spoken");

        // Act
        HouseRulesV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildHouseRulesV2",
                houseRules, foodAndDiningRule, spokenLanguages, houseRulesCB,
                depositPolicy, isInternationalHotel, expDataMap,null);

        // Assert
        assertNotNull(result);
        assertEquals("English, Hindi and French", result.getLanguage());
        assertEquals("Languages Spoken", result.getLanguageHeader());
    }

    // ========================================
    // PlacesResponseV2 (HTL-64116) Test Cases
    // ========================================

    @Test
    public void testUtilityMapToPlacesResponseCGIsCalled() {
        // Arrange
        com.mmt.hotels.pojo.response.detail.PlacesResponse mockPlacesResponse = 
            new com.mmt.hotels.pojo.response.detail.PlacesResponse();
        mockPlacesResponse.setCardType("TEST_CARD_TYPE");

        com.mmt.hotels.clientgateway.response.PlacesResponseCG mockPlacesResponseCG = 
            new com.mmt.hotels.clientgateway.response.PlacesResponseCG();
        mockPlacesResponseCG.setCardType("TEST_CARD_TYPE");

        // Mock utility method
        when(utility.mapToPlacesResponseCG(mockPlacesResponse)).thenReturn(mockPlacesResponseCG);

        // Act
        com.mmt.hotels.clientgateway.response.PlacesResponseCG result = utility.mapToPlacesResponseCG(mockPlacesResponse);

        // Assert
        assertNotNull(result);
        assertEquals("TEST_CARD_TYPE", result.getCardType());
        verify(utility, times(1)).mapToPlacesResponseCG(mockPlacesResponse);
    }

    @Test
    public void testUtilityMapToPlacesResponseCGWithNullInput() {
        // Arrange
        when(utility.mapToPlacesResponseCG(null)).thenReturn(null);

        // Act
        com.mmt.hotels.clientgateway.response.PlacesResponseCG result = utility.mapToPlacesResponseCG(null);

        // Assert
        assertNull(result);
        verify(utility, times(1)).mapToPlacesResponseCG(null);}
    @Test
    public void buildHouseRulesV2_WhenContextualEnabledAndHighlightPresent_ShouldSetHighlightMustReadRules() {
        // Arrange
        com.mmt.hotels.clientgateway.response.staticdetail.HouseRules houseRules = new com.mmt.hotels.clientgateway.response.staticdetail.HouseRules();
        List<com.mmt.hotels.model.response.staticdata.CommonRules> foodAndDiningRule = new ArrayList<>();
        List<String> spokenLanguages = Collections.emptyList();
        com.mmt.hotels.model.response.staticdata.HouseRules houseRulesCB = new com.mmt.hotels.model.response.staticdata.HouseRules();
        com.mmt.hotels.model.response.staticdata.DepositPolicy depositPolicy = null;
        boolean isInternationalHotel = false;
        Map<String, String> expDataMap = new HashMap<>();

        // Enable contextual house rules, disable IH revamp
        Mockito.when(utility.isExperimentTrue(anyMap(), eq("contextualhouserules"))).thenReturn(true);
        // Provide a HES highlight and stub transformer to return CG highlight
        com.mmt.hotels.model.response.staticdata.HighlightMustReadRule hesHighlight = new com.mmt.hotels.model.response.staticdata.HighlightMustReadRule();
        hesHighlight.setHighlightedTitle("Must Read");
        com.mmt.hotels.clientgateway.response.staticdetail.HighlightMustReadRule cgHighlight = new com.mmt.hotels.clientgateway.response.staticdetail.HighlightMustReadRule();
        cgHighlight.setHighlightedTitle("Must Read");
        when(commonResponseTransformer.getHighlightMustReadRule(any())).thenReturn(cgHighlight);

        // Act
        HouseRulesV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildHouseRulesV2",
                houseRules, foodAndDiningRule, spokenLanguages, houseRulesCB,
                depositPolicy, isInternationalHotel, expDataMap, hesHighlight);

        // Assert: contextual highlight should be set
        assertNotNull(result);
        assertNotNull(result.getHighlightMustReadRules());
        assertEquals("Must Read", result.getHighlightMustReadRules().getHighlightedTitle());
        // Since contextual is enabled, contextRules should be null (not copied)
        assertNull(result.getContextRules());
    }

    @Test
    public void buildHouseRulesV2_WhenContextualDisabled_ShouldCopyContextRules() {
        // Arrange
        com.mmt.hotels.clientgateway.response.staticdetail.HouseRules houseRules = new com.mmt.hotels.clientgateway.response.staticdetail.HouseRules();
        // Prepare a context rule to verify propagation
        ContextRules cr = new ContextRules();
        cr.setTitle("GUEST_PROFILE");
        houseRules.setContextRules(cr);

        List<com.mmt.hotels.model.response.staticdata.CommonRules> foodAndDiningRule = new ArrayList<>();
        List<String> spokenLanguages = Collections.emptyList();
        com.mmt.hotels.model.response.staticdata.HouseRules houseRulesCB = new com.mmt.hotels.model.response.staticdata.HouseRules();
        com.mmt.hotels.model.response.staticdata.DepositPolicy depositPolicy = null;
        boolean isInternationalHotel = false;
        Map<String, String> expDataMap = new HashMap<>();

        // Act
        HouseRulesV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildHouseRulesV2",
                houseRules, foodAndDiningRule, spokenLanguages, houseRulesCB,
                depositPolicy, isInternationalHotel, expDataMap, null);

        // Assert: context rules propagated and no highlight
        assertNotNull(result);
        assertNotNull(result.getContextRules());
        assertEquals("GUEST_PROFILE", result.getContextRules().getTitle());
        assertNull(result.getHighlightMustReadRules());
    }

    @Test
    public void buildHouseRulesV2_WhenInternationalAndIhRevampEnabled_ShouldNotSetLanguage() {
        // Arrange
        com.mmt.hotels.clientgateway.response.staticdetail.HouseRules houseRules = new com.mmt.hotels.clientgateway.response.staticdetail.HouseRules();
        List<com.mmt.hotels.model.response.staticdata.CommonRules> foodAndDiningRule = new ArrayList<>();
        List<String> spokenLanguages = Arrays.asList("English", "Hindi");
        com.mmt.hotels.model.response.staticdata.HouseRules houseRulesCB = new com.mmt.hotels.model.response.staticdata.HouseRules();
        com.mmt.hotels.model.response.staticdata.DepositPolicy depositPolicy = null;
        boolean isInternationalHotel = true;
        Map<String, String> expDataMap = new HashMap<>();

        // IH revamp enabled
        Mockito.when(utility.isExperimentTrue(anyMap(), eq("ihHouseRuleUIRevamp"))).thenReturn(true);

        // Act
        HouseRulesV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildHouseRulesV2",
                houseRules, foodAndDiningRule, spokenLanguages, houseRulesCB,
                depositPolicy, isInternationalHotel, expDataMap, null);

        // Assert: language and languageHeader should not be set
        assertNotNull(result);
        assertNull(result.getLanguage());
        assertNull(result.getLanguageHeader());
    }

    @Test
    public void buildHouseRulesV2_WhenHouseRulesIsNull_ShouldReturnNull() {
        // Arrange
        Map<String, String> expDataMap = new HashMap<>();

        // Act
        HouseRulesV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildHouseRulesV2",
                (Object) null, new ArrayList<>(), new ArrayList<>(), new com.mmt.hotels.model.response.staticdata.HouseRules(),
                null, false, expDataMap, null);

        // Assert
        assertNull(result);
    }

    @Test
    public void buildLbiPersuasion_WhenTitleIsNull_ShouldReturnNull() {
        // Arrange
        ReflectionTestUtils.setField(staticDetailResponseTransformer, "lovedByIndiansIconUrl", "www.lbi-icon.com");
        when(polyglotService.getTranslatedData("LBI_TITLE")).thenReturn(null);

        // Act
        Object result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "buildLbiPersuasion");

        // Assert
        assertNull(result);
    }

    @Test
    public void buildLbiPersuasion_WhenTitleIsEmpty_ShouldReturnNull() {
        // Arrange
        ReflectionTestUtils.setField(staticDetailResponseTransformer, "lovedByIndiansIconUrl", "www.lbi-icon.com");
        when(polyglotService.getTranslatedData("LBI_TITLE")).thenReturn("");

        // Act
        Object result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "buildLbiPersuasion");

        // Assert
        assertNull(result);
    }

    @Test
    public void buildLbiPersuasion_WhenTitleIsValid_ShouldReturnPersuasionResponse() {
        // Arrange
        String expectedTitle = "Loved by Indians";
        String expectedIconUrl = "www.lbi-icon.com";
        ReflectionTestUtils.setField(staticDetailResponseTransformer, "lovedByIndiansIconUrl", expectedIconUrl);
        when(polyglotService.getTranslatedData("LBI_TITLE")).thenReturn(expectedTitle);

        // Act
        Object result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "buildLbiPersuasion");

        // Assert
        assertNotNull(result);
        assertTrue(result instanceof PersuasionResponse);
        PersuasionResponse persuasionResponse = (PersuasionResponse) result;
        assertEquals(expectedTitle, persuasionResponse.getTitle());
        assertEquals(expectedIconUrl, persuasionResponse.getIconUrl());
    }

    @Test
    public void buildLbiPersuasion_WhenTitleIsValidButIconUrlIsNull_ShouldStillReturnPersuasionResponse() {
        // Arrange
        String expectedTitle = "Loved by Indians";
        ReflectionTestUtils.setField(staticDetailResponseTransformer, "lovedByIndiansIconUrl", null);
        when(polyglotService.getTranslatedData("LBI_TITLE")).thenReturn(expectedTitle);

        // Act
        Object result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "buildLbiPersuasion");

        // Assert
        assertNotNull(result);
        assertTrue(result instanceof PersuasionResponse);
        PersuasionResponse persuasionResponse = (PersuasionResponse) result;
        assertEquals(expectedTitle, persuasionResponse.getTitle());
        assertNull(persuasionResponse.getIconUrl());
    }

}