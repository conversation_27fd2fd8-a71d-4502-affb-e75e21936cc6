package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gommt.hotels.orchestrator.model.response.listing.ListingResponse;
import com.gommt.hotels.orchestrator.model.response.listing.MealPlan;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicy;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicyType;
import com.mmt.hotels.clientgateway.response.BookedInclusion;
import com.mmt.hotels.clientgateway.response.IconType;
import com.mmt.hotels.clientgateway.response.listing.UpsellRateplanResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.DEVICE_IOS;
import static com.mmt.hotels.clientgateway.constants.Constants.Per_Night;

@RunWith(MockitoJUnitRunner.class)
public class OrchUpsellRatePlanResponseTransformerTest {
    @InjectMocks
    OrchUpsellRatePlanResponseTransformer orchUpsellRatePlanResponseTransformer;

    @Mock
    DateUtil dateUtil;

    @Spy
    Utility utility;

    @Mock
    PolyglotService polyglotService;

    @Mock
    CommonConfigConsul commonConfigConsul;

    @Mock
    CommonResponseTransformer commonResponseTransformer;

    ObjectMapper mapper;

    @Before
    public void init() {
        mapper = new ObjectMapper();
        mapper.writerWithView(PIIView.External.class);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        String ratePlanNameConfigRedesign = "{\"AP\":{\"FC\":{\"ROOM\":\"{B_L_D_NEW} | {ROOM_WITH_FREE_CAN_NEW}\",\"BED\":\"{BED_WITH_FREE_CAN} | {B_L_D}\",\"ENTIRE\":\"{STAY_WITH_FREE_CAN} | {B_L_D}\"},\"NR\":{\"ROOM\":\"{B_L_D_NEW} | {non_refundable_text}\",\"BED\":\"{ROOM_WITH_BLD}\",\"ENTIRE\":\"{STAY_WITH_BLD}\"},\"FCZPN\":{\"ROOM\":\"{B_L_D_NEW} | {ROOM_WITH_FREE_CAN_NEW}\",\"BED\":\"{BED_WITH_FREE_CAN} | {B_L_D}\",\"ENTIRE\":\"{STAY_WITH_FREE_CAN} | {B_L_D}\"},\"PAH\":{\"ROOM\":\"{ROOM_WITH_BLD}\",\"BED\":\"{ROOM_WITH_BLD}\",\"ENTIRE\":\"{STAY_WITH_BLD}\"},\"PAS\":{\"ROOM\":\"{ROOM_WITH_BLD}\",\"BED\":\"{ROOM_WITH_BLD}\",\"ENTIRE\":\"{STAY_WITH_BLD}\"}},\"CP\":{\"FC\":{\"ROOM\":\"{BREAKFAST_ONLY_NEW} | {ROOM_WITH_FREE_CAN_NEW}\",\"BED\":\"{BED_WITH_FREE_CAN} | {BREAKFAST_ONLY}\",\"ENTIRE\":\"{STAY_WITH_FREE_CAN} | {BREAKFAST_ONLY}\"},\"NR\":{\"ROOM\":\"{BREAKFAST_ONLY_NEW} | {non_refundable_text}\",\"BED\":\"{BED_WITH_BREAKFAST}\",\"ENTIRE\":\"{STAY_WITH_BREAKFAST}\"},\"FCZPN\":{\"ROOM\":\"{BREAKFAST_ONLY_NEW} | {ROOM_WITH_FREE_CAN_NEW}\",\"BED\":\"{BED_WITH_FREE_CAN} | {BREAKFAST_ONLY}\",\"ENTIRE\":\"{STAY_WITH_FREE_CAN} | {BREAKFAST_ONLY}\"},\"PAH\":{\"ROOM\":\"{ROOM_WITH_BREAKFAST}\",\"BED\":\"{BED_WITH_BREAKFAST}\",\"ENTIRE\":\"{STAY_WITH_BREAKFAST}\"},\"PAS\":{\"ROOM\":\"{ROOM_WITH_BREAKFAST}\",\"BED\":\"{BED_WITH_BREAKFAST}\",\"ENTIRE\":\"{STAY_WITH_BREAKFAST}\"}},\"DEFAULT\":{\"FC\":{\"ROOM\":\"{ROOM_ONLY_TEXT_NEW} | {ROOM_WITH_FREE_CAN_NEW}\",\"BED\":\"{BED_WITH_FREE_CAN}\",\"ENTIRE\":\"{STAY_WITH_FREE_CAN}\"},\"NR\":{\"ROOM\":\"{ROOM_ONLY_TEXT_NEW} | {non_refundable_text}\",\"BED\":\"{BED_ONLY}\",\"ENTIRE\":\"{STAY_ONLY}\"},\"FCZPN\":{\"ROOM\":\"{ROOM_ONLY_TEXT_NEW} | {ROOM_WITH_FREE_CAN_NEW}\",\"BED\":\"{BED_WITH_FREE_CAN}\",\"ENTIRE\":\"{STAY_WITH_FREE_CAN}\"}}}";
        Map<String, String> addOnInfoMostPopularTag = new HashMap<>();
        Map<String, String> mealPlanMapPolyglot = new HashMap<>();
        mealPlanMapPolyglot.put("CP", "CP_MEALPLAN_TEXT");
        mealPlanMapPolyglot.put("EP", "EP_MEALPLAN_TEXT");
        Gson gson = new Gson();
        Map<String, Map<String, Map<String, String>>> ratePlanNameMapRedesign = gson.fromJson(ratePlanNameConfigRedesign, new TypeToken<Map<String, Map<String, Map<String, String>>>>() {
        }.getType());
        ReflectionTestUtils.setField(orchUpsellRatePlanResponseTransformer, "addOnInfoMostPopularTag", addOnInfoMostPopularTag);
        ReflectionTestUtils.setField(orchUpsellRatePlanResponseTransformer, "mealPlanMapPolyglot", mealPlanMapPolyglot);
        ReflectionTestUtils.setField(orchUpsellRatePlanResponseTransformer, "ratePlanNameMapRedesign", ratePlanNameMapRedesign);
        ReflectionTestUtils.setField(orchUpsellRatePlanResponseTransformer, "mealplanFilterEnable", true);
        Mockito.when(commonResponseTransformer.getPriceDisplayMessage(Mockito.anyString(), Mockito.anyInt(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.anyBoolean())).thenReturn(Per_Night);
    }

    @Test
    public void initTest() {
        orchUpsellRatePlanResponseTransformer.init();
    }

    @Test
    public void convertUpsellRatePlanResponse() throws JsonProcessingException {
        String listingResponseJson = "{\"requestId\":\"380360a7-9979-422e-a500-da9d32dc17b1\",\"journeyId\":\"B3B8C78E-247F-4586-BB40-8FF0361187CD\",\"hotelCount\":1,\"personalizedSections\":[{\"name\":\"DIRECT_HOTEL\",\"headingVisible\":false,\"hotelCardType\":\"default\",\"orientation\":\"V\",\"hotelCount\":1,\"minHotelToShow\":0,\"cardInsertionAllowed\":false,\"hotels\":[{\"id\":\"202111301521173519\",\"name\":\"Paramount Hotel\",\"maskedPropertyName\":false,\"propertyType\":\"Hotel\",\"propertyLabel\":\"Hotel\",\"stayType\":\"Hotel\",\"hotelCategory\":\"LUXE\",\"starRating\":5,\"totalRoomCount\":1,\"drivingTime\":0.0,\"omnitureFlags\":{\"rtb\":false,\"abso\":false,\"abo\":false,\"wishlisted\":false,\"sponsored\":false},\"listingType\":\"room\",\"rooms\":[{\"name\":\"Paramount Hotel\",\"code\":\"202111301521173519\",\"ratePlans\":[{\"code\":\"*******************\",\"rpcc\":\"990001952425\",\"price\":{\"basePrice\":1000.0,\"displayPrice\":787.0,\"totalTax\":247.0,\"savingPerc\":0.0,\"displayPriceType\":\"PN\",\"couponCode\":\"DEAL4U\",\"couponDiscount\":113.0,\"hotelDiscount\":0.0,\"totalDiscount\":0.0,\"applicableCoupons\":[{\"autoApplicable\":false,\"couponCode\":\"DEAL4U\",\"description\":\"#DEAL4U: Get up to 30% OFF on select hotels!\",\"discount\":113.0,\"specialPromoCoupon\":true,\"type\":\"ECOUPON\"}],\"taxBreakUp\":{\"hotelTax\":203.0,\"hotelServiceCharge\":20.0,\"hcpGst\":0.0,\"serviceFee\":45.0,\"totalTax\":0.0},\"mmtDiscount\":100.0,\"blackDiscount\":40.0,\"taxIncluded\":false,\"totalAmount\":787.0},\"paymentMode\":\"PAS\",\"cancellationPolicy\":{\"cancelPaneltyDesc\":\"This tariff cannot be cancelled with zero fee. Any cancellations will be subject to a hotel fee as follows:From 2025-02-14 19:28:00 (destination time) till 2025-02-21 14:59:59 (destination time) - 100% of booking amount.After 2025-02-21 15:00:00 (destination time) - 100% of booking amount.Cancellations are only allowed before CheckIn.\",\"penalties\":[{\"startDate\":null,\"endDate\":null,\"penaltyValue\":\"NON_REFUNDABLE\",\"penaltyType\":null}]},\"mealPlans\":[{\"code\":\"EP\",\"value\":\"Room Only\"}],\"sellableType\":\"room\",\"roomName\":\"Scene Room\",\"upsellRateplanInfo\":{\"upsellRatePlanPriceDiff\":165.0,\"baseRoomCode\":\"1904642\",\"baseRateplanCode\":\"*******************\",\"upsellType\":\"Breakfast available\"},\"supplierCode\":\"INGO\"},{\"code\":\"-3416927097390046107\",\"rpcc\":\"*********#^#^#4646186#^#^#*********#^#^#1#^#^#*********#^#^#Yzc5OThkMzYtOGJkMy1kNzkxLTZmYmYtZjE4NTkxZjI2ODI3OjMzMg==\",\"inclusions\":[{\"category\":\"MEAL_UPSELL\",\"name\":\"Breakfast\",\"amount\":400},{\"category\":\"Coffee & tea\",\"name\":\"Coffee & tea\"},{\"category\":\"MMTBLACK\",\"name\":\"Express check-in\"},{\"category\":\"Free WiFi\",\"name\":\"Free WiFi\"},{\"category\":\"Drinking water\",\"name\":\"Drinking water\"}],\"price\":{\"basePrice\":1046.0,\"displayPrice\":952.0,\"totalTax\":287.0,\"savingPerc\":0.0,\"displayPriceType\":\"PN\",\"hotelDiscount\":0.0,\"totalDiscount\":0.0,\"applicableCoupons\":[{\"autoApplicable\":false,\"couponCode\":\"MMTAPP\",\"description\":\"Flat 12% OFF Up to AED 500 on this hotel for new users; Use code \\\"MMTAPP\\\"!\",\"discount\":94.0,\"specialPromoCoupon\":true,\"type\":\"ECOUPON\"}],\"taxBreakUp\":{\"hotelTax\":235.0,\"hotelServiceCharge\":20.0,\"hcpGst\":0.0,\"serviceFee\":52.0,\"totalTax\":0.0},\"mmtDiscount\":0.0,\"blackDiscount\":30.0,\"taxIncluded\":false,\"totalAmount\":952.0},\"paymentMode\":\"PAS\",\"cancellationPolicy\":{\"cancelPaneltyDesc\":\"This tariff cannot be cancelled with zero fee. Any cancellations will be subject to a hotel fee as follows:From 2025-02-15 13:50:00 (destination time) till 2025-02-20 23:59:59 (destination time) - 100% of booking amount.After 2025-02-21 11:00:00 (destination time) - 100% of booking amount.Cancellations are only allowed before CheckIn.\",\"penalties\":[{\"startDate\":null,\"endDate\":null,\"penaltyValue\":\"NON_REFUNDABLE\",\"penaltyType\":\"PARTIAL_REFUNDABLE\"}]},\"mealPlans\":[{\"code\":\"CP\",\"value\":\"Breakfast\"}],\"sellableType\":\"room\",\"roomName\":\"Scene Room\",\"upsellRateplanInfo\":{\"upsellRatePlanPriceDiff\":20.0,\"baseRoomCode\":\"1904642\",\"baseRateplanCode\":\"-3416927097390046107\",\"upsellType\":\"Free Cancellation & Breakfast available\"},\"supplierCode\":\"AGXX0011\",\"roomTariffs\":[{\"displayPrice\":952.0,\"roomCount\":1,\"adult\":2,\"children\":1,\"childrenAges\":[7]}]},{\"code\":\"-4836136355124780169\",\"rpcc\":\"990580949456\",\"price\":{\"basePrice\":1235.0,\"displayPrice\":972.0,\"totalTax\":305.0,\"savingPerc\":0.0,\"displayPriceType\":\"PN\",\"couponCode\":\"DEAL4U\",\"couponDiscount\":140.0,\"hotelDiscount\":0.0,\"totalDiscount\":0.0,\"applicableCoupons\":[{\"autoApplicable\":false,\"couponCode\":\"DEAL4U\",\"description\":\"#DEAL4U: Get up to 30% OFF on select hotels!\",\"discount\":140.0,\"specialPromoCoupon\":true,\"type\":\"ECOUPON\"}],\"taxBreakUp\":{\"hotelTax\":250.0,\"hotelServiceCharge\":30.0,\"hcpGst\":0.0,\"serviceFee\":55.0,\"totalTax\":0.0},\"mmtDiscount\":124.0,\"blackDiscount\":20.0,\"taxIncluded\":false,\"totalAmount\":972.0},\"cancellationPolicy\":{\"cancelPaneltyDesc\":\"Free Cancellation (100% refund) if you cancel this booking before 2025-02-19 14:59:59 (destination time). Cancellations post that will be subject to a hotel fee as follows:From 2025-02-19 15:00:00 (destination time) till 2025-02-21 14:59:59 (destination time) - 100% of booking amount.After 2025-02-21 15:00:00 (destination time) - 100% of booking amount.Cancellations are only allowed before CheckIn.\",\"penalties\":[{\"startDate\":null,\"endDate\":\"2025-02-19 14:59:59\",\"penaltyValue\":\"FREE_CANCELLATION\",\"penaltyType\":\"F\"}]},\"cancellationTimeline\":{\"checkInDate\":\"21 Feb\",\"checkInDateTime\":\"3 PM\",\"cancellationDate\":\"19 Feb\",\"cancellationDateTime\":\"02:59 PM\",\"cancellationDateInDateFormat\":\"19-Feb-2025 14:59\",\"bookingDate\":\"16 Feb\",\"title\":\"STAY FLEXIBLE WITH\",\"subTitle\":\"Free Cancellation\",\"tillDate\":\"19-Feb-2025 14:59\",\"fcTextForPersuasion\":\"Free Cancellation before \u202A19 Feb 02:59 PM\u202C\",\"freeCancellationText\":\"Free Cancellation till \u202A19 Feb 02:59 PM\u202C\",\"freeCancellationBenefits\":[{\"type\":\"\",\"text\":\"Free Cancellation till 19 Feb, 2:59 PM\"},{\"type\":\"\",\"text\":\"No refund if cancelled after 19 Feb, 3:00 PM\"}],\"cancellationPolicyTimelineList\":[{\"type\":\"FULL_REFUND\",\"text\":\"100% Refund\",\"startDate\":\"14 Feb\",\"startDateTime\":\"07:28 PM\",\"endDate\":\"19 Feb\",\"endDateTime\":\"02:59 PM\",\"refundable\":true,\"refundText\":\"100%\",\"fcBenefit\":{\"type\":\"\",\"text\":\"Free Cancellation till 19 Feb, 2:59 PM\"}},{\"type\":\"NO_REFUND\",\"text\":\"Non Refundable\",\"startDate\":\"19 Feb\",\"startDateTime\":\"03:00 PM\",\"endDate\":\"21 Feb\",\"endDateTime\":\"02:59 PM\",\"refundable\":false,\"refundText\":\"0%\",\"fcBenefit\":{\"type\":\"\",\"text\":\"No refund if cancelled after 19 Feb, 3:00 PM\"}}]},\"mealPlans\":[{\"code\":\"EP\",\"value\":\"Room Only\"}],\"sellableType\":\"room\",\"roomName\":\"Scene Room\",\"supplierCode\":\"INGO\"}]}],\"dndHotel\":false,\"breakFastAvailable\":false,\"twoMealAvailable\":false,\"allMealAvailable\":false,\"highSellingAltAcco\":false,\"altAcco\":false}]}]}";
        ListingResponse listingResponse = mapper.readValue(listingResponseJson, ListingResponse.class);
        String searchHotelsRequestJson = "{\"correlationKey\":\"380360a7-9979-422e-a500-da9d32dc17b1\",\"brand\":null,\"client\":\"IOS\",\"blackInfo\":null,\"deviceDetails\":{\"appVersion\":\"9.3.0\",\"deviceId\":\"00BA75C3-E0D7-4EA1-AB82-5FB8E0DC4D3E\",\"deviceType\":\"Mobile\",\"bookingDevice\":\"IOS\",\"networkType\":\"WiFi\",\"deviceName\":\"iPhone15,2\",\"appVersionIntGi\":null,\"simSerialNo\":null},\"lastProductId\":null,\"limit\":null,\"requestDetails\":{\"visitorId\":\"1\",\"visitNumber\":1,\"trafficSource\":null,\"srCon\":null,\"srCty\":null,\"srcState\":null,\"srLat\":28.46573004810307,\"srLng\":77.08991418426366,\"funnelSource\":\"HOTELS\",\"idContext\":\"B2C\",\"notifCoupon\":null,\"callBackType\":null,\"pushDataToCallToBookQ\":null,\"pushDataToListAllPropQ\":null,\"payMode\":null,\"loggedIn\":true,\"couponCount\":null,\"siteDomain\":\"AE\",\"channel\":\"Native\",\"pageContext\":\"DETAIL\",\"subPageContext\":null,\"firstTimeUserState\":3,\"uuid\":null,\"corpAuthCode\":null,\"corpUserId\":null,\"seoCorp\":false,\"requestor\":null,\"wishCode\":null,\"preApprovedValidity\":null,\"metaInfo\":false,\"zcp\":null,\"requisitionID\":null,\"myBizFlowIdentifier\":null,\"brand\":null,\"previousTxnKey\":null,\"oldWorkflowId\":null,\"journeyId\":\"B3B8C78E-247F-4586-BB40-8FF0361187CD\",\"requestId\":\"380360a7-9979-422e-a500-da9d32dc17b1\",\"sessionId\":\"5e7e0f11-f04f-4cd7-88e1-8654847fa555\",\"promoConsent\":false,\"flyerInfo\":null,\"premium\":false,\"semanticSearchDetails\":null,\"checkDuplicateBooking\":false,\"myraMsgId\":null,\"tickTockDealApplied\":false,\"forwardBookingFlow\":false,\"extendedPackageCall\":false,\"isIgnoreSEO\":false,\"isRequestCallBack\":false,\"isListAllPropCall\":false},\"detailDeepLinkUrl\":null,\"sortCriteria\":null,\"filterCriteria\":null,\"appliedBatchKeys\":null,\"featureFlags\":{\"staticData\":true,\"walletRequired\":true,\"noOfCoupons\":0,\"noOfAddons\":0,\"noOfPersuasions\":0,\"noOfSoldouts\":0,\"coupon\":true,\"applyAbsorption\":true,\"bestOffersLimit\":1,\"addOnRequired\":true,\"locus\":true},\"matchMakerDetails\":null,\"imageDetails\":null,\"reviewDetails\":null,\"expData\":\"{MBDTC:F,UGCV2:T,CV2:f,MMRVER:V3,detailV3:t,FLTRPRCBKT:t,RCPN:T,SOC:T,ALC:f,LSTNRBY:f,mmc:T,IAO:t,BNPL:t,DPCR:0,MLOS:f,MCUR:t,HFC:T,FBP:t,AABI:f,ADDON:T,MPN:f,AALV2:T,HAFC:T,CHPC:t,MRS:T,ADC:t,APT:f,GBRP:t,HSTV2:T,SOU:t,AIP:t,CGC:T,SPCR:2,RTBC:T,CRF:B,GBE:f,APEINTL:6,BLACK:T,WPAH:t,FE:t,GRPN:T,PLRS:T,GEC:T,WSP:t,SRRP:T,CL:F,FBS:C,PAH:5,TFT:t,BNPL0:T,LSOF:t,HSCFS:4,PAV:1,RRR:3,IWD:f,NHL:t,EMI:F,ST:T,VIDEO:0,HRNB:3,OCCFCNR:f,SMC:f,NLP:Y,GALLERYV2:T,PDO:PN,SPKG:T,B2BPAH:t,CRI:f,PLV2:T,AARI:t,ratePlanRedesign:true,reorderInclusions:true,mealrackrate:True,newSelectRoomPage:TRUE}\",\"expVariantKeys\":\"8lf,8v2,8vo,7mf,7uf,8na,8wf,8ht,8q6,95l,965,7cm,97k\",\"cohertVar\":null,\"multiCityFilter\":null,\"additionalProperties\":null,\"cardId\":null,\"manthanExpDataMap\":{\"ddAPI\":\"10\",\"dynamichcp\":\"static\",\"ddAPI_ih\":\"10\"},\"contentExpDataMap\":{\"HSC_mmt_DS_ImageRank\":\"0\"},\"userLocation\":{\"city\":\"CTGGN\",\"state\":\"STHR\",\"country\":\"IN\"},\"clusterId\":null,\"orgId\":null,\"variantKeys\":\"8lf,8v2,8vo,7mf,7uf,8na,8wf,8ht,8q6,95l,965,7cm,97k\",\"businessIdentificationEnableFromUserService\":false,\"selectedTabId\":null,\"searchCriteria\":{\"checkIn\":\"2025-02-21\",\"checkOut\":\"2025-02-22\",\"countryCode\":\"UNI\",\"cityCode\":null,\"cityName\":null,\"locationId\":\"CTDUB\",\"locationType\":\"city\",\"userSearchType\":\"city\",\"lat\":25.0615787,\"lng\":55.22037,\"currency\":\"AED\",\"personalCorpBooking\":false,\"rmDHS\":false,\"boostProperty\":null,\"baseRateplanCode\":\"*******************\",\"selectedRatePlan\":null,\"multiCurrencyInfo\":{\"regionCurrency\":\"AED\",\"userCurrency\":\"AED\"},\"userGlobalInfo\":null,\"preAppliedFilter\":false,\"roomStayCandidates\":[{\"rooms\":1,\"adultCount\":2,\"childAges\":[]}],\"parentLocationId\":null,\"parentLocationType\":null,\"tripType\":null,\"slot\":null,\"giHotelId\":null,\"hotelIds\":[\"202111301521173519\"],\"limit\":1,\"lastHotelId\":null,\"lastFetchedWindowInfo\":null,\"guestHouseAvailable\":null,\"lastHotelCategory\":null,\"personalizedSearch\":true,\"nearBySearch\":false,\"wishListedSearch\":false,\"totalHotelsShown\":null,\"sectionsType\":null,\"collectionCriteria\":null,\"travellerEmailID\":null,\"bookingForGuest\":false,\"vcId\":null},\"lastPeekedOnMapHotelIds\":null,\"mapDetails\":null,\"nearbyFilter\":null,\"filterRemovedCriteria\":null}";
        SearchHotelsRequest searchHotelsRequest = mapper.readValue(searchHotelsRequestJson, SearchHotelsRequest.class);
        CommonModifierResponse commonModifierResponse = getCommonModifierResponse();
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");
        MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), DEVICE_IOS);
        Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.MEAL_UPSELL_SUBTEXT)).thenReturn("You’re saving Rs. %s with this plan as compared to buying these meals separately at the property");
        Mockito.when(utility.getTranslationFromPolyglot(Mockito.anyString())).thenReturn("");
        UpsellRateplanResponse upsellRateplanResponse = orchUpsellRatePlanResponseTransformer.convertUpsellRatePlanResponse(listingResponse, searchHotelsRequest, commonModifierResponse);
        Assert.assertNotNull(upsellRateplanResponse);
        MDC.clear();
    }

    @Test
    public void buildCancellationTextsTest() {
        BookedCancellationPolicy bookedCancellationPolicy = new BookedCancellationPolicy();
        ReflectionTestUtils.invokeMethod(orchUpsellRatePlanResponseTransformer, "buildCancellationTexts", true, true, bookedCancellationPolicy, "", false,"");
        Assert.assertEquals(bookedCancellationPolicy.getType(), BookedCancellationPolicyType.FC);
        ReflectionTestUtils.invokeMethod(orchUpsellRatePlanResponseTransformer, "buildCancellationTexts", true, false, bookedCancellationPolicy, "", true,"");
        Assert.assertEquals(bookedCancellationPolicy.getType(), BookedCancellationPolicyType.FC);
        ReflectionTestUtils.invokeMethod(orchUpsellRatePlanResponseTransformer, "buildCancellationTexts", true, false, bookedCancellationPolicy, "", false,"");
        Assert.assertEquals(bookedCancellationPolicy.getType(), BookedCancellationPolicyType.NR);
    }

    @Test
    public void buildNoMealInclusionsTest() {
        Map<String, String> expData = new HashMap<>();
        Map<String, String> mealPlanMapPolyglot = new HashMap<>();
        mealPlanMapPolyglot.put("CP", "CP_MEALPLAN_TEXT");
        mealPlanMapPolyglot.put("EP", "EP_MEALPLAN_TEXT");
        List<BookedInclusion> inclusions = new ArrayList<>();
        ReflectionTestUtils.invokeMethod(orchUpsellRatePlanResponseTransformer, "buildNoMealInclusions", expData, Collections.singletonList(MealPlan.builder().code("EP").build()), mealPlanMapPolyglot, inclusions, false, "INGO", 2);
        Assert.assertEquals(1, inclusions.size());
        Assert.assertEquals(IconType.CROSS, inclusions.get(0).getIconType());

        inclusions = new ArrayList<>();
        ReflectionTestUtils.invokeMethod(orchUpsellRatePlanResponseTransformer, "buildNoMealInclusions", expData, Collections.singletonList(MealPlan.builder().code("EP").build()), mealPlanMapPolyglot, inclusions, false, "INGO", 1);
        Assert.assertEquals(1, inclusions.size());
        Assert.assertEquals(IconType.DEFAULT, inclusions.get(0).getIconType());

        inclusions = new ArrayList<>();
        ReflectionTestUtils.invokeMethod(orchUpsellRatePlanResponseTransformer, "buildNoMealInclusions", expData, Collections.singletonList(MealPlan.builder().code("XP").build()), mealPlanMapPolyglot, inclusions, false, "EXPEDIA", 1);
        Assert.assertEquals(1, inclusions.size());
        Assert.assertEquals("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/meal.png", inclusions.get(0).getIconUrl());
    }

    private CommonModifierResponse getCommonModifierResponse() {
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExpDataMap(new LinkedHashMap<>());
        commonModifierResponse.setHydraResponse(new HydraResponse());
        commonModifierResponse.getHydraResponse().setHydraMatchedSegment(new HashSet<>(Arrays.asList("r1", "r2", "r3")));
        return commonModifierResponse;
    }

}