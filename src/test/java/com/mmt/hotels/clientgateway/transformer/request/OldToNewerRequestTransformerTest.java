package com.mmt.hotels.clientgateway.transformer.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.request.AvailRoomsRequest;
import com.mmt.hotels.clientgateway.request.SearchAddonsRequest;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.model.request.*;
import com.mmt.hotels.model.request.addon.GetAddonsRequest;
import com.mmt.hotels.model.request.flyfish.FlyFishSummaryRequest;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.request.flyfish.SubConceptFilterDTO;
import com.mmt.hotels.model.request.flyfish.SummaryFilterCriteriaDTO;

import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.response.HotelComparatorResponse;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.runners.MockitoJUnitRunner;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class OldToNewerRequestTransformerTest {
    @InjectMocks
    OldToNewerRequestTransformer oldToNewerRequestTransformer;

    @Test
    public void testUpdateSearchRoomsRequest() throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        InputStream searchPriceRequest = getClass().getClassLoader().getResourceAsStream("mockrequestresponsetest/searchPriceRequestOld.json");

        PriceByHotelsRequestBody priceByHotelsRequestBody = mapper.readValue(searchPriceRequest, PriceByHotelsRequestBody.class);
        priceByHotelsRequestBody.getRoomStayCandidates().get(0).setRooms(1);
        priceByHotelsRequestBody.getRoomStayCandidates().get(0).setAdultCount(2);
        SearchRoomsRequest searchRoomsRequest = oldToNewerRequestTransformer.updateSearchRoomsRequest(priceByHotelsRequestBody);

        //test for device details
        Assert.assertNotNull(searchRoomsRequest.getDeviceDetails().getAppVersion());

        //test for exp data
        Assert.assertNotNull(searchRoomsRequest.getExpData());

        //test for search criteria
        Assert.assertNotNull(searchRoomsRequest.getSearchCriteria().getHotelId());
        Assert.assertNotNull(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates());

        //test for request details
        Assert.assertNotNull(searchRoomsRequest.getRequestDetails().getFunnelSource());

        //test for filter criteria
        Assert.assertEquals(2,searchRoomsRequest.getFilterCriteria().size());
    }

    @Test
    public void testUpdateAvailRoomsRequest() throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        InputStream availPriceRequest = getClass().getClassLoader().getResourceAsStream("mockrequestresponsetest/availPriceRequestOld.json");

        PriceByHotelsRequestBody priceByHotelsRequestBody = mapper.readValue(availPriceRequest, PriceByHotelsRequestBody.class);

        AvailRoomsRequest availRoomsRequest = oldToNewerRequestTransformer.updateAvailRoomsRequest(priceByHotelsRequestBody);

        //test for device details
        Assert.assertNotNull(availRoomsRequest.getDeviceDetails().getAppVersion());

        //test for exp data
        Assert.assertNotNull(availRoomsRequest.getExpData());

        //test for search criteria
        Assert.assertNotNull(availRoomsRequest.getSearchCriteria().getHotelId());
        Assert.assertNotNull(availRoomsRequest.getSearchCriteria().getSearchType());
        Assert.assertTrue(CollectionUtils.isNotEmpty(availRoomsRequest.getSearchCriteria().getRoomCriteria()));

        //test for request details
        Assert.assertNotNull(availRoomsRequest.getRequestDetails().getFunnelSource());
        Assert.assertNotNull(availRoomsRequest.getRequestDetails().getPayMode());


    }
    
    @Test
    public void testupdateSearchHotelsRequest() {
    	SearchWrapperInputRequest searchWrapperInputRequest = new SearchWrapperInputRequest();
    	searchWrapperInputRequest.setResponseFilterFlags(new ResponseFilterFlags());
    	searchWrapperInputRequest.getResponseFilterFlags().setBestCoupon(true);
    	searchWrapperInputRequest.setCollectionRequired(true);
    	searchWrapperInputRequest.setTrendingNow(true);
    	searchWrapperInputRequest.setFlyfishSummaryRequest(new FlyFishSummaryRequest());
    	searchWrapperInputRequest.getFlyfishSummaryRequest().setFilter(new SummaryFilterCriteriaDTO());
    	searchWrapperInputRequest.getFlyfishSummaryRequest().getFilter().setSubConcept(new SubConceptFilterDTO());
    	searchWrapperInputRequest.getFlyfishSummaryRequest().getFilter().getSubConcept().setTagTypes(new ArrayList<String>());
    	searchWrapperInputRequest.getFlyfishSummaryRequest().getFilter().setOtas(new ArrayList<>());
    	searchWrapperInputRequest.getFlyfishSummaryRequest().getFilter().getOtas().add(OTA.AGD);
    	searchWrapperInputRequest.setImageCategory(new ArrayList<>());
    	searchWrapperInputRequest.getImageCategory().add(new ImageCategoryEntityBO());
    	searchWrapperInputRequest.setImageType(new ArrayList<>());
    	Assert.assertNotNull(oldToNewerRequestTransformer.updateSearchHotelsRequest(searchWrapperInputRequest));
    }

    @Test
    public  void testUpdateAddonRequest(){
        GetAddonsRequest getAddonsRequest = new Gson().fromJson("{\"appVersion\":\"8.1.0.RC3\",\"bookingDevice\":\"ANDROID\",\"checkin\":\"2020-10-20\",\"checkout\":\"2020-10-21\",\"countryCode\":\"IN\",\"currency\":\"INR\",\"deviceId\":\"afdcacd9d56bc88a\",\"deviceType\":\"Mobile\",\"experimentData\":\"{\\\"HPI\\\":\\\"true\\\",\\\"OCCFCNR\\\":\\\"t\\\",\\\"REV\\\":\\\"4\\\",\\\"CHPC\\\":\\\"t\\\",\\\"AARI\\\":\\\"t\\\",\\\"RCPN\\\":\\\"t\\\",\\\"HLMV\\\":\\\"t\\\",\\\"HBH\\\":\\\"f\\\",\\\"MRS\\\":\\\"t\\\",\\\"SRR\\\":\\\"t\\\",\\\"ADDON\\\":\\\"t\\\",\\\"FBP\\\":\\\"f\\\",\\\"MC\\\":\\\"t\\\",\\\"BNPL\\\":\\\"t\\\",\\\"PLRS\\\":\\\"t\\\",\\\"PDO\\\":\\\"PN\\\",\\\"DPCR\\\":\\\"1\\\",\\\"FLTRPRCBKT\\\":\\\"t\\\",\\\"EMI\\\":\\\"f\\\",\\\"SPCR\\\":\\\"2\\\",\\\"BRIN\\\":\\\"110\\\",\\\"ADDV\\\":\\\"f\\\",\\\"HRSRFHS\\\":\\\"true\\\",\\\"HIS\\\":\\\"1234\\\",\\\"WSP\\\":\\\"t\\\",\\\"APE\\\":\\\"35\\\",\\\"PAH\\\":\\\"5\\\",\\\"HSCFS\\\":\\\"4\\\",\\\"LVD\\\":\\\"\\\",\\\"PAH5\\\":\\\"t\\\",\\\"LCS\\\":\\\"t\\\",\\\"LVI\\\":\\\"\\\"}\",\"idContext\":\"MOB\",\"isLoggedIn\":true,\"locus\":true,\"os\":\"ANDROID\",\"payMode\":\"PAS\",\"profileType\":\"PERSONAL\",\"roomCriteria\":[{\"hotelId\":\"200810221411529549\",\"mtKey\":\"N$$s9rHC9RN7n8KbbAF7hiZ4knlktIA1OVVgKiyA7d0b%2BSfnmON7BhpJBqEsXFMJoGRMfj7V9I8%2FK0tU7mh8EaFu2xHJV6IFfi2KuWtDYEyY9f8vhcq3DRSQXOqY%2FRCJ1sHo7%2FgoRM1uZELyNTQa17ANFNw3sR9bGC7XGr649DbMv8Z9Qs01EApH7PI3SQfkDk8Rl9eqHWy%2FPGcSHBl33xFL%2BA6e0fhfZcTB71F11FrkE1VYIBAer5XRfKlrmB%2F6HWgeFSPhibBsy7SO6dD46DWoauPETBRyloT8F5gOAKiA%2Ff3mfh4V6L8ccY0A1s19qoxVEWP5sBRD1A%3D\",\"pricingKey\":\"s9rHC9RN7n979hlqfldyG4t9T0yyEpCsnoLAMAxV8YMb9YG6hfGZw9Z3NFmcDuBENRnSceYiHnJaIiijOnFyZXTj+h8xmD4ud0YhG5LSKKiJG6XLNXBTbZQpB7q6NhcVYxto9y+f924QPDBtQcZ/P5CyCbbmJfbcM9Z0r5w4zcKqujd+Ys1KW1ZB8oG+6lQIjlFgziZ8rrXVa2nVTNeaalHkCsyO16rA/ZYOk3hTqPuk/VWZ/qY/p41LSwG/MQ2qvCRXmxZuhM57tXk3lceUCoMpx9z6BasFZt8KzcjzSD4u003d\",\"ratePlanCode\":\"990000500175:MSE:1120:MSE:INGO\",\"roomCode\":\"4270\",\"roomStayCandidates\":[{\"guestCounts\":[{\"ageQualifyingCode\":\"10\",\"ages\":[],\"count\":\"2\"}]}],\"rpcc\":\"990000500175\",\"supplierCode\":\"INGO\"}],\"totalPriceWithTax\":10864.0,\"totalPriceWithoutTax\":8176.0,\"visitNumber\":\"1\",\"visitorId\":\"64193450281172815468424215564277283067\",\"locationId\":\"CTDEL\",\"locationType\":\"city\"}",
                GetAddonsRequest.class);
        SearchAddonsRequest searchAddonsRequest = oldToNewerRequestTransformer.updateAddonsRequest(getAddonsRequest);
        Assert.assertNotEquals(0.0,searchAddonsRequest.getTotalPriceWithoutTax(),0);
        Assert.assertNotEquals(0.0,searchAddonsRequest.getTotalPriceWithTax(),0);
        Assert.assertFalse(searchAddonsRequest.isNetRateSelected());
        Assert.assertNotNull(searchAddonsRequest.getSearchCriteria());
        Assert.assertNotNull(searchAddonsRequest.getSearchCriteria().getHotelId());
        Assert.assertNotNull(searchAddonsRequest.getSearchCriteria().getRoomCriteria());
        Assert.assertEquals(1,searchAddonsRequest.getSearchCriteria().getRoomCriteria().size());
        Assert.assertNotNull(searchAddonsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates());
        Assert.assertNotNull(searchAddonsRequest.getSearchCriteria().getRoomCriteria().get(0).getRatePlanCode());
        Assert.assertNotNull(searchAddonsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomCode());
    }

    @Test
    public void testUpdateSearchRoomRequest(){
        HotelDetailsMobRequestBody body = getHotelDetailsMobRequestBody();
        SearchRoomsRequest searchRoomsRequest =oldToNewerRequestTransformer.updateSearchRoomRequest(body);
        //test for device details
        Assert.assertNotNull(searchRoomsRequest.getDeviceDetails().getAppVersion());

        //test for exp data
        Assert.assertNotNull(searchRoomsRequest.getExpData());

        //test for search criteria
        Assert.assertNotNull(searchRoomsRequest.getSearchCriteria().getHotelId());
        Assert.assertNotNull(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates());
    }

    private HotelDetailsMobRequestBody getHotelDetailsMobRequestBody() {
        HotelDetailsMobRequestBody hotelDetailsMobRequestBody=new HotelDetailsMobRequestBody();
        hotelDetailsMobRequestBody.setTrafficSource(new TrafficSource());
        hotelDetailsMobRequestBody.setBookingDevice("android");
        hotelDetailsMobRequestBody.setVisitNumber("23");
        hotelDetailsMobRequestBody.setAppVersion("test");
        ResponseFilterFlags responseFilterFlags =new ResponseFilterFlags() ;
        responseFilterFlags.setWalletRequired(true);
        responseFilterFlags.setBestCoupon(true);
        hotelDetailsMobRequestBody.setResponseFilterFlags(responseFilterFlags);
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate candidate =new RoomStayCandidate() ;
        List<GuestCount> guestCounts =new ArrayList<>() ;
        GuestCount guestCount =new GuestCount() ;
        guestCount.setCount("2");
        List<Integer> ages = new ArrayList<>();
        ages.add(19);
        ages.add(20);
        guestCount.setAges(ages);
        guestCounts.add(guestCount);
        candidate.setGuestCounts(guestCounts);
        roomStayCandidates.add(candidate);
        hotelDetailsMobRequestBody.setRoomStayCandidates(roomStayCandidates);
        GuestRecommendationEnabledReqBody guestRecommendation = new GuestRecommendationEnabledReqBody();
        guestRecommendation.setMaxRecommendations("1");
        guestRecommendation.setText("true");
        hotelDetailsMobRequestBody.setGuestRecommendEnabled(guestRecommendation);
        List<String> hotelList = new ArrayList<>();
        hotelList.add("123");
        hotelDetailsMobRequestBody.setComparatorHotelsList(hotelList);
        hotelDetailsMobRequestBody.setHotelId("226");
        hotelDetailsMobRequestBody.setExperimentData("test");
        return hotelDetailsMobRequestBody;
    }
}
