package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.helpers.ForwardBookingFlowHelper;
import com.mmt.hotels.clientgateway.response.DataList;
import com.mmt.hotels.clientgateway.response.PaymentResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.desktop.PaymentResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.RequestIdentifier;
import com.mmt.hotels.model.request.payment.BeginCheckoutReqBody;
import com.mmt.hotels.model.response.corporate.DuplicateBookingDetails;
import com.mmt.hotels.model.response.errors.Error;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.LinkedHashSet;

@RunWith(MockitoJUnitRunner.class)
public class PaymentResponseTransformerDesktopTest {

    @InjectMocks
    PaymentResponseTransformerDesktop prTransformerD;

    @Mock
    private ForwardBookingFlowHelper forwardBookingFlowHelper;

    @Mock
    private PolyglotService polyglotService;
    @Mock
    private Utility utility;
    @Test
    public void processResponseTest(){
        PaymentCheckoutResponse paymentCheckoutResponse = new PaymentCheckoutResponse.Builder()
                .buildPaymentResponseMessage("SUCCESS")
                .buildAlternateCurrencyCode("USD")
                .buildAlternateCurrencyConversionFactor(0.5)
                .buildAlternateCurrencySelected(false)
                .buildBookingID("NH1234")
                .buildCurrency("INR")
                .buildDisplayPriceAlternateCurrency("200.0")
                .buildPaymentParams(new HashMap<>())
                .buildTotalAmount("100.0")
                .build();
        paymentCheckoutResponse.setThankYouURL("thankyouurl");
        paymentCheckoutResponse.setCorrelationKey("corr");

        paymentCheckoutResponse.getPaymentParams().put("checkoutId","C1234");
        paymentCheckoutResponse.getPaymentParams().put("checkoutUrl","Curl");
        paymentCheckoutResponse.getPaymentParams().put("paymentPlatform","FK123");

        BeginCheckoutReqBody beginCheckoutReqBody = new BeginCheckoutReqBody();
        beginCheckoutReqBody.setTransactionKey("txn123");
        PaymentResponse response = prTransformerD.processResponse(paymentCheckoutResponse,beginCheckoutReqBody);
        Assert.assertNotNull(response);
        Assert.assertEquals("FK123",response.getFkToken());
        Assert.assertEquals("thankyouurl",response.getThankYouUrl());
        Assert.assertEquals("Curl",response.getCheckoutUrl());
        Assert.assertEquals("100.0",response.getTotalAmount());
        Assert.assertEquals("SUCCESS",response.getPaymentRespMessage());
        Assert.assertEquals("INR",response.getCurrency());
        Assert.assertEquals("corr",response.getCorrelationKey());
        Assert.assertEquals("NH1234",response.getBookingID());
        Assert.assertEquals("txn123",response.getTransactionKey());
        Assert.assertEquals("C1234",response.getCheckoutId());

    }

    @Test
    public void processResponseAlternateTest(){
        PaymentCheckoutResponse paymentCheckoutResponse = new PaymentCheckoutResponse.Builder()
                .buildPaymentResponseMessage("SUCCESS")
                .buildAlternateCurrencyCode("USD")
                .buildAlternateCurrencyConversionFactor(0.5)
                .buildAlternateCurrencySelected(true)
                .buildBookingID("NH1234")
                .buildCurrency("INR")
                .buildDisplayPriceAlternateCurrency("200.0")
                .buildPaymentParams(new HashMap<>())
                .buildTotalAmount("100.0")
                .build();
        paymentCheckoutResponse.setThankYouURL("thankyouurl");
        paymentCheckoutResponse.setCorrelationKey("corr");

        paymentCheckoutResponse.getPaymentParams().put("checkoutId","C1234");
        paymentCheckoutResponse.getPaymentParams().put("checkoutUrl","Curl");
        paymentCheckoutResponse.getPaymentParams().put("paymentPlatform","FK123");
        BeginCheckoutReqBody beginCheckoutReqBody = new BeginCheckoutReqBody();
        beginCheckoutReqBody.setTransactionKey("txn123");
        PaymentResponse response = prTransformerD.processResponse(paymentCheckoutResponse,beginCheckoutReqBody);
        Assert.assertNotNull(response);
        Assert.assertEquals("FK123",response.getFkToken());
        Assert.assertEquals("thankyouurl",response.getThankYouUrl());
        Assert.assertEquals("Curl",response.getCheckoutUrl());
        Assert.assertEquals("200.0",response.getTotalAmount());
        Assert.assertEquals("SUCCESS",response.getPaymentRespMessage());
        Assert.assertEquals("USD",response.getCurrency());
        Assert.assertEquals("corr",response.getCorrelationKey());
        Assert.assertEquals("NH1234",response.getBookingID());
        Assert.assertEquals("txn123",response.getTransactionKey());
        Assert.assertEquals("C1234",response.getCheckoutId());

    }

    @Test
    public void processResponseErrorTest(){
        List<Error> errors = new ArrayList<>();
        errors.add(new Error());
        PaymentCheckoutResponse paymentCheckoutResponse = new PaymentCheckoutResponse.Builder()
               .buildResponseErrors(new ResponseErrors.Builder().buildErrorList(errors).build())
                .build();
        BeginCheckoutReqBody beginCheckoutReqBody = new BeginCheckoutReqBody();
        beginCheckoutReqBody.setTransactionKey("txn123");
        PaymentResponse response = prTransformerD.processResponse(paymentCheckoutResponse,beginCheckoutReqBody);
        Assert.assertNull(response.getError());

        paymentCheckoutResponse = new PaymentCheckoutResponse();

        Error error  = new Error();
        Map<String ,String> errorAdditionalInfoMap = new HashMap<>();
        errorAdditionalInfoMap.put("ERROR_TITLE","error");
        error.setErrorAdditionalInfo(errorAdditionalInfoMap);
        List<Error> errorList = new ArrayList<>();
        errorList.add(error);
        ResponseErrors responseErrors = new ResponseErrors.Builder().buildErrorList(errorList).build();

        paymentCheckoutResponse.setResponseErrors(responseErrors);
        response = prTransformerD.processResponse(paymentCheckoutResponse,beginCheckoutReqBody);
        Assert.assertNull(response.getError());
    }

    @Test
    public void testProcessResponseWithDuplicateBookingDetails() {
        PaymentCheckoutResponse checkoutResponse = new PaymentCheckoutResponse();
        DuplicateBookingDetails duplicateBookingDetails = new DuplicateBookingDetails();
        checkoutResponse.setDuplicateBookingDetails(duplicateBookingDetails);
        RequestIdentifier requestIdentifier = new RequestIdentifier();
        requestIdentifier.setCheckDuplicateBooking(true);
        BeginCheckoutReqBody beginCheckoutReqBody = new BeginCheckoutReqBody();
        beginCheckoutReqBody.setRequestIdentifier(requestIdentifier);
        Mockito.when(utility.populateDateList(Mockito.any())).thenReturn(new LinkedHashSet<DataList>());

        PaymentResponse paymentResponse = prTransformerD.processResponse(checkoutResponse, beginCheckoutReqBody);

        Assert.assertNotNull(paymentResponse);
        Assert.assertNotNull(paymentResponse.getConsentData());
        Assert.assertEquals(checkoutResponse.getCorrelationKey(), paymentResponse.getCorrelationKey());
        Assert.assertEquals(checkoutResponse.getPaymentRespMessage(), paymentResponse.getPaymentRespMessage());
    }
}
