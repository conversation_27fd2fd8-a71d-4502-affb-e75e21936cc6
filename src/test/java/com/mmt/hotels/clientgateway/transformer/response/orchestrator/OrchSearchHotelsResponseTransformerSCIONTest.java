package com.mmt.hotels.clientgateway.transformer.response.orchestrator;


import com.fasterxml.jackson.databind.JsonNode;
import com.gommt.hotels.orchestrator.enums.OTA;
import com.gommt.hotels.orchestrator.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails;
import com.gommt.hotels.orchestrator.model.response.listing.*;
import com.gommt.hotels.orchestrator.model.response.listing.ImageDetails;
import com.gommt.hotels.orchestrator.model.response.ugc.ListingReviewDetails;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.meta.response.others.Room;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.flyfish.ReviewSummary;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import org.apache.commons.collections.CollectionUtils;
import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class OrchSearchHotelsResponseTransformerSCIONTest {

    @InjectMocks
    private OrchSearchHotelsResponseTransformerSCION transformer;

    @Mock
    PolyglotService polyglotService;

    @Mock
    DateUtil dateUtil;

    @Mock
    private Utility utility;

    private static final String CORRELATION_ID = "test-correlation-id";
    private static final String CATEGORY_DETAILS = "{\"LUXURY\":{\"data\":[\"Luxury Hotel\"]}}";
    private static final String DEEP_LINK = "http://example.com/deeplink";
    private static final String ROOT_LEVEL_DEEPLINK = "http://example.com/root";
    private static final String ROOT_LEVEL_DEEPLINK_GLOBAL = "http://example.com/global";

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(transformer, "categoryDetailsText", CATEGORY_DETAILS);
        ReflectionTestUtils.setField(transformer, "deepLink", DEEP_LINK);
        ReflectionTestUtils.setField(transformer, "rootLevelDeeplink", ROOT_LEVEL_DEEPLINK);
        ReflectionTestUtils.setField(transformer, "rootLevelDeeplinkGlobal", ROOT_LEVEL_DEEPLINK_GLOBAL);
        ReflectionTestUtils.setField(transformer, "hotelLevelAppDeepLink", ROOT_LEVEL_DEEPLINK_GLOBAL);
        ReflectionTestUtils.setField(transformer, "rootLevelSharingUrl", ROOT_LEVEL_DEEPLINK_GLOBAL);
        ReflectionTestUtils.setField(transformer, "gson", new Gson());
        MDC.put(MDCHelper.MDCKeys.CORRELATION.getStringValue(), CORRELATION_ID);
        when(polyglotService.getTranslatedData(any())).thenReturn("testTranslation");
        when(dateUtil.getDateFormatted(any(), any(), any())).thenReturn("2024-01-01");
        when(utility.isDistributeRoomStayCandidates(any(), any())).thenReturn(false);

        transformer.init();
    }

    @Test
    public void testConvertSearchHotelsResponse_NullListingResponse() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        String response = transformer.convertSearchHotelsResponse(request, null);
        assertNull(response);
    }

    @Test
    public void testConvertSearchHotelsResponse_NullPersonalizedSections() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        ListingResponse listingResponse = new ListingResponse();
        String response = transformer.convertSearchHotelsResponse(request, listingResponse);
        assertNull(response);
    }

    @Test
    public void testConvertSearchHotelsResponse_WithError() {
        SearchHotelsRequest request = createSearchRequest(false);
        ListingResponse listingResponse = createListingResponse();

        ErrorResponse error = new ErrorResponse();
        error.setCode("TEST_ERROR");
        error.setMessage("Test error message");
        listingResponse.setError(error);

        String response = transformer.convertSearchHotelsResponse(request, listingResponse);

        assertNotNull(response);
        assertTrue(response.contains("TEST_ERROR"));
    }

    @Test
    public void testConvertSearchHotelsResponse_WithGlobalUser() {
        SearchHotelsRequest request = createSearchRequest(true);
        ListingResponse listingResponse = createListingResponse();

        String response = transformer.convertSearchHotelsResponse(request, listingResponse);

        assertNotNull(response);
        assertTrue(response.contains(ROOT_LEVEL_DEEPLINK_GLOBAL));
    }

    @Test
    public void testConvertSearchHotelsResponse_WithExclusiveHotels() {
        SearchHotelsRequest request = createSearchRequest(false);
        ListingResponse listingResponse = createListingResponseWithExclusiveHotels();

        String response = transformer.convertSearchHotelsResponse(request, listingResponse);

        System.out.println(response);
        assertNotNull(response);
    }

    private SearchHotelsRequest createSearchRequest(boolean isGlobal) {
        SearchHotelsRequest request = new SearchHotelsRequest();
        SearchHotelsCriteria criteria = new SearchHotelsCriteria();

        // Basic location and currency details
        criteria.setCurrency("INR");
        criteria.setLocationId("CITY123");
        criteria.setLocationType("CITY");
        criteria.setCountryCode("IN");
        criteria.setCityName("Test City");

        // Coordinates
        criteria.setLat(28.0);
        criteria.setLng(77.0);

        // Dates
        criteria.setCheckIn("2024-01-01");
        criteria.setCheckOut("2024-01-02");

        // Room stay candidates
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
        roomStayCandidate.setAdultCount(2);
        List<Integer> childAges = Arrays.asList(5, 8);
        roomStayCandidate.setChildAges(childAges);
        roomStayCandidates.add(roomStayCandidate);
        criteria.setRoomStayCandidates(roomStayCandidates);

        // Collection criteria
        CollectionCriteria collectionCriteria = new CollectionCriteria();
        collectionCriteria.setCollectionIds(Arrays.asList("COLL1", "COLL2"));
        collectionCriteria.setCollectionsCount("2");
        collectionCriteria.setCollectionRequired(true);
        collectionCriteria.setTrendingNow(true);
        collectionCriteria.setSuggestedForYouCards(true);
        collectionCriteria.setPropertyTypeCards(true);
        collectionCriteria.setStaticFilterCardsRequired(true);
        collectionCriteria.setDiscoverByDestinationCardsRequired(true);
        collectionCriteria.setInspiredCardsRequired(true);
        collectionCriteria.setOffbeatCitiesCardsRequired(true);
        collectionCriteria.setValueStayCardsRequired(true);
        collectionCriteria.setAthenaCategory("default");
        collectionCriteria.setLuxeCardRequired(true);
        collectionCriteria.setBannerListCardRequired(true);
        collectionCriteria.setFamilyCardRequired(true);
        collectionCriteria.setHostCardRequired(true);
        criteria.setCollectionCriteria(collectionCriteria);

        // Sort criteria
        SortCriteria sortCriteria = new SortCriteria();
        sortCriteria.setField("PRICE");
        sortCriteria.setOrder("ASC");

        // Global user info if needed
        if (isGlobal) {
            UserGlobalInfo globalInfo = new UserGlobalInfo();
            globalInfo.setEntityName("GLOBAL");
            criteria.setUserGlobalInfo(globalInfo);
        }

        request.setSearchCriteria(criteria);
        return request;
    }

    private ListingResponse createListingResponse() {
        ListingResponse response = new ListingResponse();

        // Set basic fields
        response.setLastPage(true);
        response.setLastHotelId("");
        response.setCurrency("INR");
        response.setTrackingMap(new HashMap<>());

        // Set location
        LocationDetails location = new LocationDetails();
        location.setId("CITY123");
        location.setType("CITY");
        location.setCityName("Test City");
        location.setCountryId("IN");
        location.setCountryName("India");
        response.setLocation(location);

        // Set personalized sections
        List<PersonalizedSectionDetails> sections = new ArrayList<>();
        PersonalizedSectionDetails section = new PersonalizedSectionDetails();
        section.setName("Test Section");
        section.setHotelCount(1);

        // Add hotel details
        List<HotelDetails> hotels = new ArrayList<>();
        HotelDetails hotel = new HotelDetails();
        Set<String> categories = new HashSet<>();
        categories.add("LUXURY");
        hotel.setCategories(categories);
        hotels.add(hotel);

        section.setHotels(hotels);
        sections.add(section);
        response.setPersonalizedSections(sections);

        return response;
    }

    private ListingResponse createListingResponseWithExclusiveHotels() {
        ListingResponse response = createListingResponse();

        PersonalizedSectionDetails exclusiveSection = new PersonalizedSectionDetails();
        exclusiveSection.setName("EXCLUSIVE_HOTELS");
        exclusiveSection.setHotelCount(1);
        exclusiveSection.setFilterToHotelMap(new HashMap<>());

        response.getPersonalizedSections().add(exclusiveSection);

        return response;
    }

    @Test
    public void testBuildDisplayFarePrice_NullHotelEntity() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        transformer.buildDisplayFarePrice(searchWrapperHotelEntity, null);
        assertNull(searchWrapperHotelEntity.getDisplayFare());
    }

    @Test
    public void testBuildDisplayFarePrice_NullRooms() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        HotelDetails hotelEntity = new HotelDetails();
        transformer.buildDisplayFarePrice(searchWrapperHotelEntity, hotelEntity);
        assertNull(searchWrapperHotelEntity.getDisplayFare());
    }

    @Test
    public void testBuildDisplayFarePrice_EmptyRooms() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        HotelDetails hotelEntity = new HotelDetails();
        hotelEntity.setRooms(new ArrayList<>());
        transformer.buildDisplayFarePrice(searchWrapperHotelEntity, hotelEntity);
        assertNull(searchWrapperHotelEntity.getDisplayFare());
    }

    @Test
    public void testBuildDisplayFarePrice_NullRatePlans() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        HotelDetails hotelEntity = new HotelDetails();
        List<RoomEntity> rooms = new ArrayList<>();
        RoomEntity room = new RoomEntity();
        rooms.add(room);
        hotelEntity.setRooms(rooms);
        transformer.buildDisplayFarePrice(searchWrapperHotelEntity, hotelEntity);
        assertNull(searchWrapperHotelEntity.getDisplayFare());
    }

    @Test
    public void testBuildDisplayFarePrice_EmptyRatePlans() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        HotelDetails hotelEntity = new HotelDetails();
        List<RoomEntity> rooms = new ArrayList<>();
        RoomEntity room = new RoomEntity();
        room.setRatePlans(new ArrayList<>());
        rooms.add(room);
        hotelEntity.setRooms(rooms);
        transformer.buildDisplayFarePrice(searchWrapperHotelEntity, hotelEntity);
        assertNull(searchWrapperHotelEntity.getDisplayFare());
    }

    @Test
    public void testBuildDisplayFarePrice_NullPrice() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        HotelDetails hotelEntity = createHotelEntityWithRatePlan(null);
        transformer.buildDisplayFarePrice(searchWrapperHotelEntity, hotelEntity);
        assertNull(searchWrapperHotelEntity.getDisplayFare());
    }

    @Test
    public void testBuildDisplayFarePrice_WithPrice() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        PriceDetail price = new PriceDetail();
        price.setDisplayPrice(1000.0);
        price.setSavingPerc(10.0);
        price.setBlackDiscount(100.0);
        price.setBasePrice(1100.0);
        price.setTotalRoomCount(2);

        HotelDetails hotelEntity = createHotelEntityWithRatePlan(price);
        transformer.buildDisplayFarePrice(searchWrapperHotelEntity, hotelEntity);

        assertNotNull(searchWrapperHotelEntity.getDisplayFare());
        assertNotNull(searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown());
        assertEquals(1000.0, searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getDisplayPrice(), 0.01);
        assertEquals(10.0, searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getSavingPerc(), 0.01);
        assertEquals(100.0, searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getBlackDiscount(), 0.01);
        assertEquals(1100.0, searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getNonDiscountedPrice(), 0.01);
        assertEquals(new Integer(2), searchWrapperHotelEntity.getDisplayFare().getTotalRoomCount());
    }

    @Test
    public void testBuildDisplayFarePrice_WithCoupons() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        PriceDetail price = new PriceDetail();
        price.setDisplayPrice(1000.0);

        List<PriceCouponInfo> coupons = new ArrayList<>();
        PriceCouponInfo coupon = new PriceCouponInfo();
        coupon.setCouponCode("TEST123");
        coupon.setDescription("Test Coupon");
        coupon.setDiscount(50.0);
        coupon.setSpecialPromoCoupon(true);
        coupon.setType("DISCOUNT");
        coupons.add(coupon);
        price.setApplicableCoupons(coupons);

        HotelDetails hotelEntity = createHotelEntityWithRatePlan(price);
        transformer.buildDisplayFarePrice(searchWrapperHotelEntity, hotelEntity);

        assertNotNull(searchWrapperHotelEntity.getDisplayFare());
        assertNotNull(searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo());
        assertEquals("TEST123", searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getCouponCode());
        assertEquals("Test Coupon", searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getDescription());
        assertEquals(50.0, searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getDiscountAmount(), 0.01);
        assertTrue(searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo().isSpecialPromoCoupon());
        assertEquals("DISCOUNT", searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getType());
    }

    @Test
    public void testBuildDisplayFarePrice_WithMealPlans() {
        SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
        HotelDetails hotelEntity = createHotelEntityWithRatePlan(new PriceDetail());

        List<MealPlan> mealPlans = new ArrayList<>();
        MealPlan mealPlan = new MealPlan();
        mealPlan.setCode("BB");
        mealPlan.setValue("Bed & Breakfast");
        mealPlans.add(mealPlan);
        hotelEntity.getRooms().get(0).getRatePlans().get(0).setMealPlans(mealPlans);

        transformer.buildDisplayFarePrice(searchWrapperHotelEntity, hotelEntity);

        assertNotNull(searchWrapperHotelEntity.getMealPlanIncluded());
        assertEquals("BB", searchWrapperHotelEntity.getMealPlanIncluded().getCode());
        assertEquals("Bed & Breakfast", searchWrapperHotelEntity.getMealPlanIncluded().getDesc());
    }

    private HotelDetails createHotelEntityWithRatePlan(PriceDetail price) {
        HotelDetails hotelEntity = new HotelDetails();
        List<RoomEntity> rooms = new ArrayList<>();
        RoomEntity room = new RoomEntity();
        List<ResponseRatePlan> ratePlans = new ArrayList<>();
        ResponseRatePlan ratePlan = new ResponseRatePlan();
        ratePlan.setPrice(price);
        ratePlans.add(ratePlan);
        room.setRatePlans(ratePlans);
        rooms.add(room);
        hotelEntity.setRooms(rooms);
        return hotelEntity;
    }

    @Test
    public void testGetMainImages_NullImageList() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        List<String> result = transformer.getMainImages(null, request);
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetMainImages_EmptyImageList() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        List<ImageDetails> imageList = new ArrayList<>();
        List<String> result = transformer.getMainImages(imageList, request);
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetMainImages_NullAdditionalProperties() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        List<ImageDetails> imageList = createSampleImageList();

        List<String> result = transformer.getMainImages(imageList, request);

        assertNotNull(result);
        assertEquals(3, result.size()); // Default imageCount is 1
        assertEquals("http://example.com/image1.jpg", result.get(0));
    }

    @Test
    public void testGetMainImages_WithImageCountAndNoSecureUrl() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        Map<String, String> additionalProperties = new HashMap<>();
        additionalProperties.put("imageCount", "2");
        request.setAdditionalProperties(additionalProperties);

        List<ImageDetails> imageList = createSampleImageList();

        List<String> result = transformer.getMainImages(imageList, request);

        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("http://example.com/image1.jpg", result.get(0));
        assertEquals("http://example.com/image2.jpg", result.get(1));
    }

    @Test
    public void testGetMainImages_WithSecureUrl() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        Map<String, String> additionalProperties = new HashMap<>();
        additionalProperties.put("imageCount", "2");
        additionalProperties.put("secureUrl", "https://secure");
        request.setAdditionalProperties(additionalProperties);

        List<ImageDetails> imageList = createSampleImageList();
        // Set SCION request flag
        request.setRequestDetails(new RequestDetails());
        request.getRequestDetails().setRequestor("SCION");

        List<String> result = transformer.getMainImages(imageList, request);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("https://secure:http://example.com/image1.jpg", result.get(0));
        assertEquals("https://secure:http://example.com/image2.jpg", result.get(1));
    }

    @Test
    public void testGetMainImages_RequestedMoreThanAvailable() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        Map<String, String> additionalProperties = new HashMap<>();
        additionalProperties.put("imageCount", "5"); // Request more images than available
        request.setAdditionalProperties(additionalProperties);

        List<ImageDetails> imageList = createSampleImageList(); // Creates 3 images
        request.setRequestDetails(new RequestDetails());
        request.getRequestDetails().setRequestor("SCION");

        List<String> result = transformer.getMainImages(imageList, request);

        assertNotNull(result);
        assertEquals(3, result.size()); // Should return all available images
    }

    @Test
    public void testGetMainImages_NonScionRequest() {
        SearchHotelsRequest request = new SearchHotelsRequest();
        Map<String, String> additionalProperties = new HashMap<>();
        additionalProperties.put("imageCount", "2");
        additionalProperties.put("secureUrl", "https://secure");
        request.setAdditionalProperties(additionalProperties);

        List<ImageDetails> imageList = createSampleImageList();
        request.setRequestDetails(new RequestDetails());
        request.getRequestDetails().setRequestor("TEST_REQUESTOR");

        List<String> result = transformer.getMainImages(imageList, request);

        assertNotNull(result);
        assertEquals(3, result.size()); // Should return all images without modification
        assertEquals("http://example.com/image1.jpg", result.get(0));
        assertEquals("http://example.com/image2.jpg", result.get(1));
        assertEquals("http://example.com/image3.jpg", result.get(2));
    }

    // Helper method to create sample image list
    private List<ImageDetails> createSampleImageList() {
        List<ImageDetails> imageList = new ArrayList<>();

        ImageDetails image1 = new ImageDetails();
        image1.setUrl("http://example.com/image1.jpg");
        imageList.add(image1);

        ImageDetails image2 = new ImageDetails();
        image2.setUrl("http://example.com/image2.jpg");
        imageList.add(image2);

        ImageDetails image3 = new ImageDetails();
        image3.setUrl("http://example.com/image3.jpg");
        imageList.add(image3);

        return imageList;
    }

    @Test
    public void testBuildReviewSummary_NullReviewDetails() {
        HotelDetails hotelEntity = new HotelDetails();
        ReviewSummary result = transformer.buildReviewSummary(hotelEntity);

        assertNotNull(result);
        assertNull(result.getOta());
        assertNotNull(result.getHotelRating());
    }

    @Test
    public void testBuildReviewSummary_WithReviewDetails() {
        HotelDetails hotelEntity = new HotelDetails();
        ListingReviewDetails reviewDetails = new ListingReviewDetails();
        reviewDetails.setOta("MMT");
        reviewDetails.setRating(4.5f);
        reviewDetails.setTotalReviewCount(100);
        reviewDetails.setTotalRatingCount(150);
        hotelEntity.setReviewDetails(reviewDetails);

        ReviewSummary result = transformer.buildReviewSummary(hotelEntity);

        assertNotNull(result);
        assertEquals("MMT", result.getOta());
        assertEquals(4.5f, result.getHotelRating(), 0.001);
        assertEquals(100, result.getReviewCount());
        assertEquals(150, result.getRatingCount());
    }

    @Test
    public void testBuildReviewSummary_NullHotelEntity() {
        ReviewSummary result = transformer.buildReviewSummary(null);

        assertNotNull(result);
        assertNull(result.getOta());
    }

    @Test
    public void testBuildFilterToHotelMap_NullInput() {
        Map<String, com. gommt. hotels. orchestrator. model. response. listing.ExclusiveFilterHotels<HotelDetails>> filterToHotelMap = null;
        ListingResponse listingResponse = new ListingResponse();
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();

        Map<String, com.mmt.hotels.model.response.searchwrapper.ExclusiveFilterHotels<SearchWrapperHotelEntity>> result =
            transformer.buildFilterToHotelMap(filterToHotelMap, listingResponse, searchHotelsRequest);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildFilterToHotelMap_EmptyInput() {
        Map<String, ExclusiveFilterHotels<HotelDetails>> filterToHotelMap = new HashMap<>();
        ListingResponse listingResponse = new ListingResponse();
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();

        Map<String, com.mmt.hotels.model.response.searchwrapper.ExclusiveFilterHotels<SearchWrapperHotelEntity>> result =
            transformer.buildFilterToHotelMap(filterToHotelMap, listingResponse, searchHotelsRequest);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildFilterToHotelMap_WithEmptyHotels() {
        Map<String, ExclusiveFilterHotels<HotelDetails>> filterToHotelMap = new HashMap<>();
        ExclusiveFilterHotels<HotelDetails> exclusiveFilterHotels = new ExclusiveFilterHotels<>();
        exclusiveFilterHotels.setHotels(new ArrayList<>());
        TabDetails tabDetails = new TabDetails();
        exclusiveFilterHotels.setTabDetails(tabDetails);
        filterToHotelMap.put("testFilter", exclusiveFilterHotels);

        ListingResponse listingResponse = new ListingResponse();
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();

        Map<String, com.mmt.hotels.model.response.searchwrapper.ExclusiveFilterHotels<SearchWrapperHotelEntity>> result =
            transformer.buildFilterToHotelMap(filterToHotelMap, listingResponse, searchHotelsRequest);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey("testFilter"));
        assertNotNull(result.get("testFilter").getTabDetails());
        assertTrue(CollectionUtils.isEmpty(result.get("testFilter").getHotels()));
    }

    @Test
    public void testBuildFilterToHotelMap_WithHotels() {
        // Setup input data
        Map<String, ExclusiveFilterHotels<HotelDetails>> filterToHotelMap = new HashMap<>();
        ExclusiveFilterHotels<HotelDetails> exclusiveFilterHotels = new ExclusiveFilterHotels<>();

        // Create sample hotel details
        List<HotelDetails> hotelList = new ArrayList<>();
        HotelDetails hotel = new HotelDetails();
        hotel.setId("TEST123");
        hotelList.add(hotel);
        exclusiveFilterHotels.setHotels(hotelList);

        // Create sample tab details
        TabDetails tabDetails = new TabDetails();
        tabDetails.setTitle("Test Tab");
        exclusiveFilterHotels.setTabDetails(tabDetails);

        filterToHotelMap.put("testFilter", exclusiveFilterHotels);

        // Setup listing response and request
        ListingResponse listingResponse = new ListingResponse();
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();

        // Execute method
        Map<String, com.mmt.hotels.model.response.searchwrapper.ExclusiveFilterHotels<SearchWrapperHotelEntity>> result =
            transformer.buildFilterToHotelMap(filterToHotelMap, listingResponse, searchHotelsRequest);

        // Verify results
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey("testFilter"));
        assertNotNull(result.get("testFilter").getTabDetails());
        assertFalse(CollectionUtils.isEmpty(result.get("testFilter").getHotels()));
        assertEquals(1, result.get("testFilter").getHotels().size());
    }

    @Test
    public void testBuildFilterToHotelMap_MultipleFilters() {
        // Setup input data
        Map<String, ExclusiveFilterHotels<HotelDetails>> filterToHotelMap = new HashMap<>();

        // Create first filter
        ExclusiveFilterHotels<HotelDetails> filter1 = new ExclusiveFilterHotels<>();
        List<HotelDetails> hotelList1 = new ArrayList<>();
        HotelDetails hotel1 = new HotelDetails();
        hotel1.setId("TEST123");
        hotelList1.add(hotel1);
        filter1.setHotels(hotelList1);
        TabDetails tabDetails1 = new TabDetails();
        tabDetails1.setTitle("Tab 1");
        filter1.setTabDetails(tabDetails1);
        filterToHotelMap.put("filter1", filter1);

        // Create second filter
        ExclusiveFilterHotels<HotelDetails> filter2 = new ExclusiveFilterHotels<>();
        List<HotelDetails> hotelList2 = new ArrayList<>();
        HotelDetails hotel2 = new HotelDetails();
        hotel2.setId("TEST456");
        hotelList2.add(hotel2);
        filter2.setHotels(hotelList2);
        TabDetails tabDetails2 = new TabDetails();
        tabDetails2.setTitle("Tab 2");
        filter2.setTabDetails(tabDetails2);
        filterToHotelMap.put("filter2", filter2);

        ListingResponse listingResponse = new ListingResponse();
        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();

        // Execute method
        Map<String, com.mmt.hotels.model.response.searchwrapper.ExclusiveFilterHotels<SearchWrapperHotelEntity>> result =
            transformer.buildFilterToHotelMap(filterToHotelMap, listingResponse, searchHotelsRequest);

        // Verify results
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey("filter1"));
        assertTrue(result.containsKey("filter2"));
        assertEquals(1, result.get("filter1").getHotels().size());
        assertEquals(1, result.get("filter2").getHotels().size());
        assertEquals("Tab 1", result.get("filter1").getTabDetails().getTitle());
        assertEquals("Tab 2", result.get("filter2").getTabDetails().getTitle());
    }

    @Test
    public void testConvertTabDetails_NullInput() {
        SearchHotelsRequest searchRequest = new SearchHotelsRequest();
        com.mmt.hotels.model.response.searchwrapper.TabDetails result = transformer.convertTabDetails(null, searchRequest);
        assertNull(result);
    }

    @Test
    public void testConvertTabDetails_FullDetails() {
        // Setup
        SearchHotelsRequest searchRequest = new SearchHotelsRequest();
        com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails sourceTabDetails =
            new com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails();

        sourceTabDetails.setId("tab123");
        sourceTabDetails.setTitle("Luxury Hotels");
        sourceTabDetails.setSelected(true);
        sourceTabDetails.setIconUrl("https://example.com/icon.png");
        sourceTabDetails.setFilterGroup("AMENITIES");
        sourceTabDetails.setFilterValue("POOL");

        // Execute
        com.mmt.hotels.model.response.searchwrapper.TabDetails result = transformer.convertTabDetails(sourceTabDetails, searchRequest);

        // Verify
        assertNotNull(result);
        assertEquals("tab123", result.getId());
        assertEquals("Luxury Hotels", result.getTitle());
        assertTrue(result.isSelected());
        assertEquals("https://example.com/icon.png", result.getIconUrl());
        assertEquals("AMENITIES", result.getFilterGroup());
        assertEquals("POOL", result.getFilterValue());
        assertNotNull(result.getDeeplink()); // Assuming deeplink is generated
    }

    @Test
    public void testConvertTabDetails_MinimalDetails() {
        // Setup
        SearchHotelsRequest searchRequest = new SearchHotelsRequest();
        com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails sourceTabDetails =
            new com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails();

        sourceTabDetails.setId("tab123");
        sourceTabDetails.setTitle("Budget Hotels");

        // Execute
        com.mmt.hotels.model.response.searchwrapper.TabDetails result = transformer.convertTabDetails(sourceTabDetails, searchRequest);

        // Verify
        assertNotNull(result);
        assertEquals("tab123", result.getId());
        assertEquals("Budget Hotels", result.getTitle());
        assertFalse(result.isSelected());
        assertNull(result.getIconUrl());
        assertNull(result.getFilterGroup());
        assertNull(result.getFilterValue());
        assertNotNull(result.getDeeplink());
    }

    @Test
    public void testConvertTabDetails_WithFilterGroupAndValue() {
        // Setup
        SearchHotelsRequest searchRequest = new SearchHotelsRequest();
        com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails sourceTabDetails =
            new com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails();

        sourceTabDetails.setId("tab123");
        sourceTabDetails.setFilterGroup("STAR_RATING");
        sourceTabDetails.setFilterValue("5_STAR");

        // Execute
        com.mmt.hotels.model.response.searchwrapper.TabDetails result = transformer.convertTabDetails(sourceTabDetails, searchRequest);

        // Verify
        assertNotNull(result);
        assertEquals("tab123", result.getId());
        assertEquals("STAR_RATING", result.getFilterGroup());
        assertEquals("5_STAR", result.getFilterValue());
        assertNotNull(result.getDeeplink());
    }

    @Test
    public void testConvertTabDetails_WithSearchRequest() {
        // Setup
        SearchHotelsRequest searchRequest = new SearchHotelsRequest();
        SearchHotelsCriteria criteria = new SearchHotelsCriteria();
        criteria.setCityName("Mumbai");
        criteria.setLocationId("1234");
        criteria.setCountryCode("IN");
        searchRequest.setSearchCriteria(criteria);

        com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails sourceTabDetails =
            new com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails();
        sourceTabDetails.setId("tab123");
        sourceTabDetails.setTitle("City Hotels");

        // Execute
        com.mmt.hotels.model.response.searchwrapper.TabDetails result = transformer.convertTabDetails(sourceTabDetails, searchRequest);

        // Verify
        assertNotNull(result);
        assertEquals("tab123", result.getId());
        assertEquals("City Hotels", result.getTitle());
        assertNotNull(result.getDeeplink());
        // Verify that deeplink contains search criteria information
        assertTrue(result.getDeeplink().contains("Mumbai"));
        assertTrue(result.getDeeplink().contains("1234"));
    }

    @Test
    public void testConvertTabDetails_EmptySearchRequest() {
        // Setup
        SearchHotelsRequest searchRequest = new SearchHotelsRequest();
        com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails sourceTabDetails =
            new com.gommt.hotels.orchestrator.model.request.crossSell.TabDetails();
        sourceTabDetails.setId("tab123");

        // Execute
        com.mmt.hotels.model.response.searchwrapper.TabDetails result = transformer.convertTabDetails(sourceTabDetails, searchRequest);

        // Verify
        assertNotNull(result);
        assertEquals("tab123", result.getId());
        assertNotNull(result.getDeeplink());
    }

    @Test
    public void testConvertAddress_WithValidAddress() {
        // Arrange
        com.gommt.hotels.orchestrator.model.response.content.Address sourceAddress = new com.gommt.hotels.orchestrator.model.response.content.Address();
        sourceAddress.setArea(Collections.singletonList("Bandra"));
        sourceAddress.setLine1("24, Turner Road");
        sourceAddress.setLine2("Near Linking Road");

        // Act
        com.mmt.hotels.model.response.staticdata.Address result = transformer.convertAddress(sourceAddress);

        // Assert
        assertNotNull(result);
        assertEquals(Collections.singletonList("Bandra"), result.getArea());
        assertEquals("24, Turner Road", result.getLine1());
        assertEquals("Near Linking Road", result.getLine2());
    }

    @Test
    public void testConvertAddress_WithNullAddress() {
        // Act
        com.mmt.hotels.model.response.staticdata.Address result = transformer.convertAddress(null);

        // Assert
        assertNull(result);
    }

    @Test
    public void testConvertAddress_WithEmptyAddress() {
        // Arrange
        com.gommt.hotels.orchestrator.model.response.content.Address sourceAddress = new com.gommt.hotels.orchestrator.model.response.content.Address();

        // Act
        com.mmt.hotels.model.response.staticdata.Address result = transformer.convertAddress(sourceAddress);

        // Assert
        assertNotNull(result);
        assertNull(result.getArea());
        assertNull(result.getLine1());
        assertNull(result.getLine2());
    }

    @Test
    public void testConvertAddress_WithPartialAddress() {
        // Arrange
        com.gommt.hotels.orchestrator.model.response.content.Address sourceAddress = new com.gommt.hotels.orchestrator.model.response.content.Address();
        sourceAddress.setArea(Collections.singletonList("Andheri East"));
        sourceAddress.setLine1("Near Metro Station");

        // Act
        com.mmt.hotels.model.response.staticdata.Address result = transformer.convertAddress(sourceAddress);

        // Assert
        assertNotNull(result);
        assertEquals(Collections.singletonList("Andheri East"), result.getArea());
        assertEquals("Near Metro Station", result.getLine1());
        assertNull(result.getLine2());
    }

    @Test
    public void testBuildFlyfishReviewSummary_WithValidReviewDetails() {
        // Arrange
        HotelDetails hotelEntity = new HotelDetails();
        ListingReviewDetails reviewDetails = new ListingReviewDetails();
        reviewDetails.setOta("MMT");
        reviewDetails.setRating(4.5f);
        reviewDetails.setTotalReviewCount(100);
        reviewDetails.setTotalRatingCount(150);
        hotelEntity.setReviewDetails(reviewDetails);

        // Act
        Map<com.mmt.hotels.model.request.flyfish.OTA, JsonNode> result = transformer.buildFlyfishReviewSummary(hotelEntity);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertFalse(result.containsKey(OTA.MMT));
    }

    @Test
    public void testBuildFlyfishReviewSummary_WithNullHotelEntity() {
        // Act
        Map<com.mmt.hotels.model.request.flyfish.OTA, JsonNode> result = transformer.buildFlyfishReviewSummary(null);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildFlyfishReviewSummary_WithNullReviewDetails() {
        // Arrange
        HotelDetails hotelEntity = new HotelDetails();
        hotelEntity.setReviewDetails(null);

        // Act
        Map<com.mmt.hotels.model.request.flyfish.OTA, JsonNode> result = transformer.buildFlyfishReviewSummary(hotelEntity);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildFlyfishReviewSummary_WithNullOta() {
        // Arrange
        HotelDetails hotelEntity = new HotelDetails();
        ListingReviewDetails reviewDetails = new ListingReviewDetails();
        reviewDetails.setOta(null);
        reviewDetails.setRating(4.5f);
        reviewDetails.setTotalReviewCount(100);
        reviewDetails.setTotalRatingCount(150);
        hotelEntity.setReviewDetails(reviewDetails);

        // Act
        Map<com.mmt.hotels.model.request.flyfish.OTA, JsonNode> result = transformer.buildFlyfishReviewSummary(hotelEntity);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }


    // ================================
    // Tests for appendDataToReviewDeepLink function
    // ================================

    @Test
    public void should_AppendAllParameters_When_AllParametersProvided() {
        // Given
        String deeplink = "https://example.com/review";
        String rscValue = "2e7e2e12e7e";
        boolean forwardBookingFlow = true;
        boolean maskedPropertyName = true;

        // When
        String result = ReflectionTestUtils.invokeMethod(transformer, "appendDataToReviewDeepLink", 
                deeplink, rscValue, forwardBookingFlow, maskedPropertyName, "", "");

        // Then
        assertNotNull(result);
        assertEquals("https://example.com/review&rsc=2e7e2e12e7e&forward=true&mpn=true", result);
    }

    @Test
    public void should_AppendParametersWithoutRsc_When_RscValueIsNull() {
        // Given
        String deeplink = "https://example.com/review";
        String rscValue = null;
        boolean forwardBookingFlow = true;
        boolean maskedPropertyName = false;

        // When
        String result = ReflectionTestUtils.invokeMethod(transformer, "appendDataToReviewDeepLink", 
                deeplink, rscValue, forwardBookingFlow, maskedPropertyName, "", "");

        // Then
        assertNotNull(result);
        assertEquals("https://example.com/review&forward=true&mpn=false", result);
    }

    @Test
    public void should_AppendParametersWithoutRsc_When_RscValueIsEmpty() {
        // Given
        String deeplink = "https://example.com/review";
        String rscValue = "";
        boolean forwardBookingFlow = false;
        boolean maskedPropertyName = true;

        // When
        String result = ReflectionTestUtils.invokeMethod(transformer, "appendDataToReviewDeepLink", 
                deeplink, rscValue, forwardBookingFlow, maskedPropertyName, "", "");

        // Then
        assertNotNull(result);
        assertEquals("https://example.com/review&forward=false&mpn=true", result);
    }

    @Test
    public void should_AppendParametersWithoutRsc_When_RscValueIsBlank() {
        // Given
        String deeplink = "https://example.com/review";
        String rscValue = "   ";
        boolean forwardBookingFlow = false;
        boolean maskedPropertyName = false;

        // When
        String result = ReflectionTestUtils.invokeMethod(transformer, "appendDataToReviewDeepLink", 
                deeplink, rscValue, forwardBookingFlow, maskedPropertyName, "", "");

        // Then
        assertNotNull(result);
    }

    @Test
    public void should_HandleForwardBookingFlowTrue_When_ForwardBookingFlowIsTrue() {
        // Given
        String deeplink = "https://example.com/review";
        String rscValue = "test123";
        boolean forwardBookingFlow = true;
        boolean maskedPropertyName = false;

        // When
        String result = ReflectionTestUtils.invokeMethod(transformer, "appendDataToReviewDeepLink", 
                deeplink, rscValue, forwardBookingFlow, maskedPropertyName, "", "");

        // Then
        assertNotNull(result);
        assertTrue(result.contains("&forward=true"));
        assertEquals("https://example.com/review&rsc=test123&forward=true&mpn=false", result);
    }

    @Test
    public void should_HandleForwardBookingFlowFalse_When_ForwardBookingFlowIsFalse() {
        // Given
        String deeplink = "https://example.com/review";
        String rscValue = "test456";
        boolean forwardBookingFlow = false;
        boolean maskedPropertyName = true;

        // When
        String result = ReflectionTestUtils.invokeMethod(transformer, "appendDataToReviewDeepLink", 
                deeplink, rscValue, forwardBookingFlow, maskedPropertyName, "", "");

        // Then
        assertNotNull(result);
        assertTrue(result.contains("&forward=false"));
        assertEquals("https://example.com/review&rsc=test456&forward=false&mpn=true", result);
    }

    @Test
    public void should_HandleMaskedPropertyNameTrue_When_MaskedPropertyNameIsTrue() {
        // Given
        String deeplink = "https://example.com/review";
        String rscValue = "rsc789";
        boolean forwardBookingFlow = true;
        boolean maskedPropertyName = true;

        // When
        String result = ReflectionTestUtils.invokeMethod(transformer, "appendDataToReviewDeepLink", 
                deeplink, rscValue, forwardBookingFlow, maskedPropertyName, "", "");

        // Then
        assertNotNull(result);
        assertTrue(result.contains("&mpn=true"));
        assertEquals("https://example.com/review&rsc=rsc789&forward=true&mpn=true", result);
    }

    @Test
    public void should_HandleMaskedPropertyNameFalse_When_MaskedPropertyNameIsFalse() {
        // Given
        String deeplink = "https://example.com/review";
        String rscValue = "rsc000";
        boolean forwardBookingFlow = false;
        boolean maskedPropertyName = false;

        // When
        String result = ReflectionTestUtils.invokeMethod(transformer, "appendDataToReviewDeepLink", 
                deeplink, rscValue, forwardBookingFlow, maskedPropertyName, "", "");

        // Then
        assertNotNull(result);
        assertTrue(result.contains("&mpn=false"));
        assertEquals("https://example.com/review&rsc=rsc000&forward=false&mpn=false", result);
    }

    @Test
    public void should_HandleEmptyDeeplink_When_DeeplinkIsEmpty() {
        // Given
        String deeplink = "";
        String rscValue = "test123";
        boolean forwardBookingFlow = true;
        boolean maskedPropertyName = false;

        // When
        String result = ReflectionTestUtils.invokeMethod(transformer, "appendDataToReviewDeepLink", 
                deeplink, rscValue, forwardBookingFlow, maskedPropertyName, "", "");

        // Then
        assertNotNull(result);
    }

    @Test
    public void should_HandleComplexRscValue_When_RscValueHasSpecialCharacters() {
        // Given
        String deeplink = "https://example.com/review";
        String rscValue = "2e7e2e12e7e_ABC-123";
        boolean forwardBookingFlow = true;
        boolean maskedPropertyName = true;

        // When
        String result = ReflectionTestUtils.invokeMethod(transformer, "appendDataToReviewDeepLink", 
                deeplink, rscValue, forwardBookingFlow, maskedPropertyName, "", "");

        // Then
        assertNotNull(result);
        assertEquals("https://example.com/review&rsc=2e7e2e12e7e_ABC-123&forward=true&mpn=true", result);
    }

    @Test
    public void should_HandleDeeplinkWithExistingParams_When_DeeplinkAlreadyHasParameters() {
        // Given
        String deeplink = "https://example.com/review?hotelId=123&checkIn=2024-01-01";
        String rscValue = "testRsc";
        boolean forwardBookingFlow = false;
        boolean maskedPropertyName = true;

        // When
        String result = ReflectionTestUtils.invokeMethod(transformer, "appendDataToReviewDeepLink", 
                deeplink, rscValue, forwardBookingFlow, maskedPropertyName, "", "");

        // Then
        assertNotNull(result);
        assertEquals("https://example.com/review?hotelId=123&checkIn=2024-01-01&rsc=testRsc&forward=false&mpn=true", result);
    }

    @Test
    public void should_HandleLongDeeplink_When_DeeplinkIsVeryLong() {
        // Given
        String deeplink = "https://example.com/review/very/long/path/with/multiple/segments/and/parameters?param1=value1&param2=value2&param3=value3";
        String rscValue = "longRscValue123456789";
        boolean forwardBookingFlow = true;
        boolean maskedPropertyName = false;

        // When
        String result = ReflectionTestUtils.invokeMethod(transformer, "appendDataToReviewDeepLink", 
                deeplink, rscValue, forwardBookingFlow, maskedPropertyName, "", "");

        // Then
        assertNotNull(result);
        assertTrue(result.startsWith(deeplink));
        assertTrue(result.contains("&rsc=longRscValue123456789"));
        assertTrue(result.contains("&forward=true"));
        assertTrue(result.contains("&mpn=false"));
    }

    @Test
    public void should_HandleAllFalseParameters_When_AllBooleanParametersAreFalse() {
        // Given
        String deeplink = "https://example.com/review";
        String rscValue = "falseTest";
        boolean forwardBookingFlow = false;
        boolean maskedPropertyName = false;

        // When
        String result = ReflectionTestUtils.invokeMethod(transformer, "appendDataToReviewDeepLink", 
                deeplink, rscValue, forwardBookingFlow, maskedPropertyName, "", "");

        // Then
        assertNotNull(result);
        assertEquals("https://example.com/review&rsc=falseTest&forward=false&mpn=false", result);
    }

    @Test
    public void should_HandleAllTrueParameters_When_AllBooleanParametersAreTrue() {
        // Given
        String deeplink = "https://example.com/review";
        String rscValue = "trueTest";
        boolean forwardBookingFlow = true;
        boolean maskedPropertyName = true;

        // When
        String result = ReflectionTestUtils.invokeMethod(transformer, "appendDataToReviewDeepLink", 
                deeplink, rscValue, forwardBookingFlow, maskedPropertyName, "", "");

        // Then
        assertNotNull(result);
        assertEquals("https://example.com/review&rsc=trueTest&forward=true&mpn=true", result);
    }

    @Test
    public void should_HandleMinimalInput_When_OnlyDeeplinkProvided() {
        // Given
        String deeplink = "https://example.com/review";
        String rscValue = null;
        boolean forwardBookingFlow = false;
        boolean maskedPropertyName = false;

        // When
        String result = ReflectionTestUtils.invokeMethod(transformer, "appendDataToReviewDeepLink", 
                deeplink, rscValue, forwardBookingFlow, maskedPropertyName, "", "");

        // Then
        assertNotNull(result);
        assertEquals("https://example.com/review&forward=false&mpn=false", result);
    }

    @Test
    public void should_HandleNullDeeplink_When_DeeplinkIsNull() {
        // Given
        String deeplink = null;
        String rscValue = "testRsc";
        boolean forwardBookingFlow = true;
        boolean maskedPropertyName = true;

        // When
        String result = ReflectionTestUtils.invokeMethod(transformer, "appendDataToReviewDeepLink", 
                deeplink, rscValue, forwardBookingFlow, maskedPropertyName, "", "");

        // Then
        assertNotNull(result);
    }

}
