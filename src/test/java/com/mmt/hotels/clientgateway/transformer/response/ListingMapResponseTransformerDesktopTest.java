package com.mmt.hotels.clientgateway.transformer.response;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.runners.MockitoJUnitRunner;

import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.transformer.response.desktop.ListingMapResponseTransformerDesktop;

@RunWith(MockitoJUnitRunner.class)
public class ListingMapResponseTransformerDesktopTest {

    @InjectMocks
    ListingMapResponseTransformerDesktop listingMapResponseTransformerDesktop;

    @Test
    public void addLocationPersuasionToHotelPersuasionsTest() {

        Hotel hotel = new Hotel();
        listingMapResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(hotel,null,null,null,null);
        Assert.assertNull(hotel.getHotelPersuasions());

        List<String> locationPersuasion = new ArrayList<>();
        locationPersuasion.add("Rajiv Chowk");
        locationPersuasion.add("10 km from Airport");
        hotel.setLocationPersuasion(locationPersuasion);
        listingMapResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(hotel,new ArrayList<>(),null,null,null);
        Assert.assertNull(hotel.getHotelPersuasions());

        listingMapResponseTransformerDesktop.addLocationPersuasionToHotelPersuasions(hotel,locationPersuasion,null,null,null);
        Assert.assertNotNull(hotel.getHotelPersuasions());

    }


}