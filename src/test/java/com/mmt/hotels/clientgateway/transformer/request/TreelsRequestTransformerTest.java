package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.FilterHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.request.matchmaker.CityTags;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.LOCATION;
import static com.mmt.hotels.clientgateway.constants.Constants.checkInAddition;
import static com.mmt.hotels.clientgateway.constants.Constants.checkOutAddition;
import static com.mmt.hotels.clientgateway.constants.Constants.defaultGuest;
import static com.mmt.hotels.clientgateway.constants.Constants.defaultRooms;

@RunWith(MockitoJUnitRunner.class)
public class TreelsRequestTransformerTest {

    @InjectMocks
    private TreelsRequestTransformer treelsRequestTransformer;

    @Mock
    private MetricAspect metricAspect;

    @Mock
    private Utility utility;

    @Mock
    private FilterHelper filterHelper;

    @Spy
    private CommonHelper commonHelper;

    @Test
    public void convertSearchRequestTest() {
        ListingSearchRequestV2 listingSearchRequestV2 = new ListingSearchRequestV2();
        listingSearchRequestV2.setSearchCriteria(new TreelsSearchCriteria());
        listingSearchRequestV2.getSearchCriteria().setCheckIn(LocalDate.now().toString());
        listingSearchRequestV2.getSearchCriteria().setCheckOut(LocalDate.now().plusDays(2).toString());
        listingSearchRequestV2.setDeviceDetails(new DeviceDetails());
        listingSearchRequestV2.setFilterCriteria(new ArrayList<>());
        listingSearchRequestV2.getFilterCriteria().add(new Filter());
        listingSearchRequestV2.getFilterCriteria().get(0).setFilterValue("test");
        listingSearchRequestV2.getFilterCriteria().get(0).setFilterGroup(com.mmt.hotels.clientgateway.response.filter.FilterGroup.TAGS);
        listingSearchRequestV2.getSearchCriteria().setRoomStayCandidates(new ArrayList<>());
        listingSearchRequestV2.getSearchCriteria().getRoomStayCandidates().add(new RoomStayCandidate());
        listingSearchRequestV2.setRequestDetails(new RequestDetails());
        Mockito.when(utility.isDistributeRoomStayCandidates(Mockito.any(), Mockito.any())).thenReturn(true);
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExtendedUser(new ExtendedUser());
        commonModifierResponse.setHydraResponse(new HydraResponse());
        commonModifierResponse.getHydraResponse().setHydraMatchedSegment(new HashSet<>());
        commonModifierResponse.getHydraResponse().getHydraMatchedSegment().add("test");
        SemanticSearchDetails semanticSearchDetails = new SemanticSearchDetails();
        semanticSearchDetails.setQueryText("some query text");
        semanticSearchDetails.setSemanticData("some semantic data");
        listingSearchRequestV2.getRequestDetails().setSemanticSearchDetails(semanticSearchDetails);
        SearchWrapperInputRequest hesReq = treelsRequestTransformer.convertSearchRequest(listingSearchRequestV2, commonModifierResponse);
        Assert.assertEquals(hesReq.getCheckin(), LocalDate.now().toString());
        Assert.assertEquals(hesReq.getCheckout(), LocalDate.now().plusDays(2).toString());
        Assert.assertNotNull(hesReq.getAppliedFilterMap().get(FilterGroup.TAGS));
    }

    @Test
    public void convertSearchRequestNPETest() {
        ListingSearchRequestV2 listingSearchRequestV2 = new ListingSearchRequestV2();
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        Assert.assertNotNull(treelsRequestTransformer.convertSearchRequest(listingSearchRequestV2, commonModifierResponse));
    }

    @Test
    public void convertSearchRequestLocationFilterTest() {
        ListingSearchRequestV2 listingSearchRequestV2 = new ListingSearchRequestV2();
        listingSearchRequestV2.setSearchCriteria(new TreelsSearchCriteria());
        listingSearchRequestV2.getSearchCriteria().setCheckIn(LocalDate.now().toString());
        listingSearchRequestV2.getSearchCriteria().setCheckOut(LocalDate.now().plusDays(2).toString());
        listingSearchRequestV2.setDeviceDetails(new DeviceDetails());
        listingSearchRequestV2.getSearchCriteria().setRoomStayCandidates(new ArrayList<>());
        listingSearchRequestV2.getSearchCriteria().getRoomStayCandidates().add(new RoomStayCandidate());
        listingSearchRequestV2.setRequestDetails(new RequestDetails());
        listingSearchRequestV2.setMatchMakerDetails(new MatchMakerRequest());
        listingSearchRequestV2.getMatchMakerDetails().setCityTags(new ArrayList<>());
        listingSearchRequestV2.getMatchMakerDetails().getCityTags().add(new CityTags());
        listingSearchRequestV2.getMatchMakerDetails().getCityTags().get(0).setFilterGroup(LOCATION);
        listingSearchRequestV2.getMatchMakerDetails().getCityTags().get(0).setFilterValue("CTDEL");
        SemanticSearchDetails semanticSearchDetails = new SemanticSearchDetails();
        semanticSearchDetails.setQueryText("some query text");
        semanticSearchDetails.setSemanticData("some semantic data");
        listingSearchRequestV2.getRequestDetails().setSemanticSearchDetails(semanticSearchDetails);
        Mockito.when(utility.isDistributeRoomStayCandidates(Mockito.any(), Mockito.any())).thenReturn(true);
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExtendedUser(new ExtendedUser());
        commonModifierResponse.setHydraResponse(new HydraResponse());
        commonModifierResponse.getHydraResponse().setHydraMatchedSegment(new HashSet<>());
        commonModifierResponse.getHydraResponse().getHydraMatchedSegment().add("test");
        Map<String, String> map = new HashMap<>();
        map.put("test", "test");
//        Mockito.when(filterHelper.getFilterMap(Mockito.any())).thenReturn(map);
        SearchWrapperInputRequest hesReq = treelsRequestTransformer.convertSearchRequest(listingSearchRequestV2, commonModifierResponse);
        Assert.assertNotNull(hesReq.getAppliedFilterMap().get(FilterGroup.LOCATION));
        Assert.assertEquals(hesReq.getAppliedFilterMap().get(FilterGroup.LOCATION).size(), 1);
    }

    @Test
    public void convertSearchRequestLocationFilterRemoveTest() {
        ListingSearchRequestV2 listingSearchRequestV2 = new ListingSearchRequestV2();
        listingSearchRequestV2.setSearchCriteria(new TreelsSearchCriteria());
        listingSearchRequestV2.getSearchCriteria().setCheckIn(LocalDate.now().toString());
        listingSearchRequestV2.getSearchCriteria().setCheckOut(LocalDate.now().plusDays(2).toString());
        listingSearchRequestV2.setDeviceDetails(new DeviceDetails());
        listingSearchRequestV2.getSearchCriteria().setRoomStayCandidates(new ArrayList<>());
        listingSearchRequestV2.getSearchCriteria().getRoomStayCandidates().add(new RoomStayCandidate());
        listingSearchRequestV2.setRequestDetails(new RequestDetails());
        listingSearchRequestV2.setFilterRemovedCriteria(new ArrayList<>());
        listingSearchRequestV2.getFilterRemovedCriteria().add(new Filter());
        listingSearchRequestV2.getFilterRemovedCriteria().get(0).setFilterValue("test");
        listingSearchRequestV2.getFilterRemovedCriteria().get(0).setFilterGroup(com.mmt.hotels.clientgateway.response.filter.FilterGroup.TAGS);
        SemanticSearchDetails semanticSearchDetails = new SemanticSearchDetails();
        semanticSearchDetails.setQueryText("some query text");
        semanticSearchDetails.setSemanticData("some semantic data");
        listingSearchRequestV2.getRequestDetails().setSemanticSearchDetails(semanticSearchDetails);
        Mockito.when(utility.isDistributeRoomStayCandidates(Mockito.any(), Mockito.any())).thenReturn(true);
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExtendedUser(new ExtendedUser());
        commonModifierResponse.setHydraResponse(new HydraResponse());
        commonModifierResponse.getHydraResponse().setHydraMatchedSegment(new HashSet<>());
        commonModifierResponse.getHydraResponse().getHydraMatchedSegment().add("test");
        Map<String, String> map = new HashMap<>();
        map.put("test", "test");
//        Mockito.when(filterHelper.getFilterMap(Mockito.any())).thenReturn(map);
        Assert.assertNotNull(treelsRequestTransformer.convertSearchRequest(listingSearchRequestV2, commonModifierResponse));
    }
}
