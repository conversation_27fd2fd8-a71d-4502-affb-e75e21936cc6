pool.name=MAIN


# Feature toggles for circuit breaker
circuit.breaker.mob-landing.enabled=true
circuit.breaker.filter-count.enabled=true
circuit.breaker.detail-api.enabled=false
circuit.breaker.listing-api.enabled=false

# Circuit Breaker configurations for MAIN pool (High Traffic)
resilience4j.circuitbreaker.instances.mob-landing.failure-rate-threshold=20
resilience4j.circuitbreaker.instances.mob-landing.timeout-duration=5s
resilience4j.circuitbreaker.instances.mob-landing.sliding-window-size=60
resilience4j.circuitbreaker.instances.mob-landing.minimum-number-of-calls=100
resilience4j.circuitbreaker.instances.mob-landing.wait-duration-in-open-state=60s
resilience4j.circuitbreaker.instances.mob-landing.permitted-number-of-calls-in-half-open-state=100
resilience4j.circuitbreaker.instances.mob-landing.slow-call-rate-threshold=50.0

# Circuit Breaker configurations for filter-count endpoint
resilience4j.circuitbreaker.instances.filter-count.failure-rate-threshold=20
resilience4j.circuitbreaker.instances.filter-count.timeout-duration=5s
resilience4j.circuitbreaker.instances.filter-count.sliding-window-size=60
resilience4j.circuitbreaker.instances.filter-count.minimum-number-of-calls=100
resilience4j.circuitbreaker.instances.filter-count.wait-duration-in-open-state=60s
resilience4j.circuitbreaker.instances.filter-count.permitted-number-of-calls-in-half-open-state=100
resilience4j.circuitbreaker.instances.filter-count.slow-call-rate-threshold=50.0

# Circuit breaker for other APIs (extensible)
#resilience4j.circuitbreaker.instances.detail-api.failure-rate-threshold=30
#resilience4j.circuitbreaker.instances.detail-api.timeout-duration=10s
#resilience4j.circuitbreaker.instances.detail-api.slow-call-rate-threshold=50.0
#resilience4j.circuitbreaker.instances.listing-api.failure-rate-threshold=35
#resilience4j.circuitbreaker.instances.listing-api.timeout-duration=12s
#resilience4j.circuitbreaker.instances.listing-api.slow-call-rate-threshold=50.0


http.connection.timeout.search.hotels=1000
http.connection.pool.size.search.hotels=110
search.hotels.conn.req.timeout=15000
search.hotels.so.timeout=15000
# rest connector configs for cross sell requests
http.connection.timeout.search.hotels.cross.sell=800
search.hotels.conn.req.timeout.cross.sell=800
search.hotels.so.timeout.cross.sell=800
search.hotels.conn.filter.applied.req.timeout=25000
search.hotels.filter.applied.so.timeout=25000
connection.request.timeout.filter.count=15000
http.connection.timeout.filter.count=1000
socket.timeout.filter.count=10000
http.connection.min.pool.size.filter.count=50
http.connection.max.pool.size.filter.count=80
#TODO tune the rest connector configs
connection.request.timeout.evaluate.filter.rank=15000
http.connection.timeout.evaluate.filter.rank=1000
socket.timeout.evaluate.filter.rank=10000
http.connection.min.pool.size.evaluate.filter.rank=50
http.connection.max.pool.size.evaluate.filter.rank=80

http.connection.timeout.carddata=1000
http.connection.pool.size.carddata=60
carddata.conn.req.timeout=3000
carddata.so.timeout=3000

http.connection.timeout.mob.landing=1000
http.connection.pool.size.mob.landing=50
mob.landing.conn.req.timeout=15000
mob.landing.so.timeout=15000

http.connection.timeout.search.treel=1000
http.connection.pool.size.search.treel=50
search.treel.conn.req.timeout=15000
search.treel.so.timeout=15000

http.connection.timeout.treel.filter=1000
http.connection.pool.size.treel.filter=50
treel.filter.conn.req.timeout=15000
treel.filter.so.timeout=15000

http.connection.timeout.listing.map=1000
http.connection.pool.size.listing.map=30
listing.map.conn.req.timeout=10000
listing.map.so.timeout=10000

http.connection.timeout.near.by=1000
http.connection.pool.size.near.by=40
near.by.conn.req.timeout=10000
near.by.so.timeout=10000

http.connection.timeout.search.rooms=1000
http.connection.pool.size.search.rooms=80
search.rooms.conn.req.timeout=10000
search.rooms.so.timeout=10000

spring.servlet.multipart.max-file-size=5MB
spring.servlet.multipart.max-request-size=40MB

http.connection.timeout.alternate.dates=1000
http.connection.pool.size.alternate.dates=80
alternate.dates.conn.req.timeout=10000
alternate.dates.so.timeout=10000

http.connection.timeout.static.rooms.detail=1000
http.connection.pool.size.static.rooms.detail=100
static.rooms.detail.conn.req.timeout=3000
static.rooms.detail.so.timeout=3000

program.18.conn.req.timeout=3000
http.connection.timeout.program.18=1000
program.18.detail.so.timeout=3000
http.connection.pool.size.program.18=100

ugc.submit.answers.conn.req.timeout=3000
http.connection.timeout.ugc.submit.answers=1000
ugc.submit.answers.detail.so.timeout=3000
http.connection.pool.size.ugc.submit.answers=100

ugc.image.upload.conn.req.timeout=3000
http.connection.timeout.ugc.image.upload=1000
ugc.image.upload.so.timeout=3000
http.connection.pool.size.ugc.image.upload=100

get.booking.details.conn.req.timeout=3000
http.connection.timeout.get.booking.details=1000
get.booking.details.so.timeout=3000
http.connection.pool.size.get.booking.details=100

http.connection.timeout.static.detail=1000
http.connection.pool.size.static.detail=100
static.detail.conn.req.timeout=10000
static.detail.so.timeout=10000

http.connection.timeout.updated.price=1000
http.connection.pool.size.updated.price=40
updated.price.conn.req.timeout=5000
updated.price.so.timeout=5000

http.connection.timeout.avail.rooms=1000
http.connection.pool.size.avail.rooms=60
avail.rooms.conn.req.timeout=10000
avail.rooms.so.timeout=10000

http.connection.timeout.gift.cards=1000
http.connection.pool.size.gift.cards=20
gift.cards.conn.req.timeout=20000
gift.cards.so.timeout=10000

http.connection.timeout.updated.price.occu.less=1000
http.connection.pool.size.updated.price.occu.less=30
updated.price.occu.less.conn.req.timeout=10000
updated.price.occu.less.so.timeout=10000

http.connection.timeout.pokus.experiment=500
http.connection.pool.size.pokus.experiment=100
pokus.experiment.conn.req.timeout=200
pokus.experiment.so.timeout=200

http.connection.timeout.user.service=500
http.connection.pool.size.user.service=100
user.service.conn.req.timeout=500
user.service.so.timeout=500

http.connection.timeout.user.first.time.state=500
http.connection.pool.size.user.first.time.state=50
user.first.time.state.conn.req.timeout=500
user.first.time.state.so.timeout=500

http.connection.timeout.last.booked.flight=500
http.connection.pool.size.last.booked.flight=50
last.booked.flight.conn.req.timeout=500
last.booked.flight.so.timeout=500

http.connection.timeout.bypass=1000
http.connection.pool.size.bypass=50
bypass.conn.req.timeout=1000
bypass.so.timeout=1000

http.connection.timeout.payment.checkout=1000
http.connection.pool.size.payment.checkout=50
payment.checkout.conn.req.timeout=10000
payment.checkout.so.timeout=250000

http.connection.timeout.offer.details=1000
http.connection.pool.size.offer.details=50
offer.details.conn.req.timeout=10000
offer.details.so.timeout=10000

http.connection.timeout.payment.checkout.mod.booking=10000
http.connection.pool.size.payment.checkout.mod.booking=10
payment.checkout.mod.booking.conn.req.timeout=10000
payment.checkout.mod.booking.so.timeout=120000


http.connection.timeout.fetch.collection=1000
http.connection.pool.size.fetch.collection=100
fetch.collection.conn.req.timeout=3000
fetch.collection.so.timeout=3000

http.connection.timeout.emiDetail=1000
http.connection.pool.size.emiDetail=80
emiDetail.conn.req.timeout=1200
emiDetail.so.timeout=1200

http.connection.timeout.totalPrice=1000
http.connection.pool.size.totalPrice=100
totalPrice.conn.req.timeout=5000
totalPrice.so.timeout=3000

http.connection.timeout.policies=500
policies.conn.req.timeout=500
policies.so.timeout=500
http.connection.pool.size.policies=20

http.connection.timeout.corporate.workflow.data=1000
http.connection.pool.size.corporate.workflow.data=100
corporate.workflow.data.conn.req.timeout=10000
corporate.workflow.data.so.timeout=10000

http.connection.timeout.update.approval=1000
http.connection.pool.size.update.approval=100
update.approval.conn.req.timeout=10000
update.approval.so.timeout=10000

http.connection.timeout.available.guest.house=1000
http.connection.pool.size.available.guest.house=100
available.guest.house.conn.req.timeout=10000
available.guest.house.so.timeout=10000

http.connection.timeout.set.approval=1000
http.connection.pool.size.set.approval=100
set.approval.conn.req.timeout=10000
set.approval.so.timeout=10000

fetch.locations.conn.req.timeout=1000
http.connection.timeout.fetch.locations=1000
fetch.locations.so.timeout=1000
http.connection.pool.size.fetch.locations=50

http.connection.timeout.ltlng=1000
ltlng.conn.req.timeout=5000
ltlng.so.timeout=5000
http.connection.pool.size.ltlng=30

http.connection.timeout.txn.data=1000
http.connection.pool.size.txn.data=50
txn.data.conn.req.timeout=10000
txn.data.so.timeout=10000

http.connection.timeout.affiliate=1000
http.connection.pool.size.affiliate=50
affiliate.conn.req.timeout=5000
affiliate.so.timeout=5000

http.connection.timeout.addons=500
addons.conn.req.timeout=1000
addons.so.timeout=1000
http.connection.pool.size.addons=30

http.connection.timeout.update.policy=500
update.policy.conn.req.timeout=5000
update.policy.so.timeout=5000
http.connection.pool.size.update.policy=25

polyglot.connection.timeout=5000
polyglot.connectionRequest.timeout=5000
polyglot.socket.timeout=5000
polyglot.connection.pool.size=50
polyglot.cache.cg.backup=classpath:polyglotCacheCGBackup.json
polyglot.mob.gen.cache.cg.backup=classpath:polyglotMobGenCacheCGBackup.json


paylater.connection.timeout=1000
paylater.connectionRequest.timeout=1000
paylater.socket.timeout=3500
paylater.connection.pool.size=10

request.callback.conn.req.timeout=1000
request.callback.so.timeout=1000
http.connection.pool.size.request.callback=30
http.connection.timeout.request.callback =500


commons.wishlist.hotels.connectionRequest.timeout=10000
commons.wishlist.hotels.connection.timeout=1000
commons.wishlist.hotels.socket.timeout=7000
commons.wishlist.hotels.connection.pool.size=40

commons.wishlist.reviews.connectionRequest.timeout=10000
commons.wishlist.reviews.connection.timeout=1000
commons.wishlist.reviews.socket.timeout=7000
commons.wishlist.reviews.connection.pool.size=40

platform.wishlist.reviews.connectionRequest.timeout=10000
platform.wishlist.reviews.connection.timeout=1000
platform.wishlist.reviews.socket.timeout=7000
platform.wishlist.reviews.connection.pool.size=40

calendar.availability.connectionRequest.timeout=5000
calendar.availability.connection.timeout=1000
calendar.availability.socket.timeout=10000
calendar.availability.connection.pool.size=50
calendar.availability.url=http://hotels-entity-service-detail.ecs.mmt/hotels-entity-detail/api/v2.0/calendarAvailability

# Host calling Config
host.calling.initiate.url=http://hotels-entity-service-detail.ecs.mmt/hotels-entity-detail/api/v2.0/hostCalling/initiate
http.connection.timeout.host.calling=1000
http.connection.pool.size.host.calling=50
host.calling.conn.req.timeout=5000
host.calling.so.timeout=5000

#Landing discovery Config
landing.discovery.conn.req.timeout=15000
http.connection.timeout.landing.discovery=1000
landing.discovery.so.timeout=15000
http.connection.pool.size.landing.discovery=110
landing.discovery.url=http://hotel-entity-service-listing.ecs.mmt/hotels-entity-listing/api/v2.0/landingDiscovery

#CityOverview Config
city.overview.conn.req.timeout=700
http.connection.timeout.city.overview=700
city.overview.so.timeout=200
http.connection.pool.size.city.overview=100
city.overview.url=http://hotel-entity-service-listing.ecs.mmt/hotels-entity-listing/api/v2.0/hotels/cityOverview

search.personalized.hotels.url=http://hotel-entity-service-listing.ecs.mmt/hotels-entity-listing/api/v2.0/listingPersonalization
search.hotels.url=http://hotel-entity-service-listing.ecs.mmt/hotels-entity-listing/api/v2.0/searchHotels
search.treels.url=http://hotel-entity-service-listing.ecs.mmt/hotels-entity-listing/api/v2.0/searchTreels
treels.filter.count.url=http://hotel-entity-service-listing.ecs.mmt/hotels-entity-listing/api/v2.0/treelsFilter
mob.landing.url=http://hotel-entity-service-listing.ecs.mmt/hotels-entity-listing/api/v2.0/hotels/landing
listing.map.hes.url=http://hotel-entity-service-listing.ecs.mmt/hotels-entity-listing/api/v2.0/listingMap
smart.filter.url=http://hotels-chatbot.ecs.mmt/smart-filter
filter.count.url=http://hotel-entity-service-listing.ecs.mmt/hotels-entity-listing/api/v2.0/filter
filter.count.log.url=http://hotel-entity-service-listing.ecs.mmt/hotels-entity-listing/api/v2.0/filterLogging
listing.map.url=http://hotels-mmt-clientbackend.ecs.mmt/clientbackend/entity/api/listingMap
search.rooms.url=http://hotels-entity-service-detail.ecs.mmt/hotels-entity-detail/api/v2.0/hotels/searchPrice
comparator.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/upsellHotels
alternate.dates.url=http://hotels-entity-service-detail.ecs.mmt/hotels-entity-detail/api/v2.0/hotels/alternateDates
static.rooms.url=http://hotels-entity-service-detail.ecs.mmt/hotels-entity-detail/api/v2.0/hotels/roominfo
updated.price.url=http://hotels-entity-service-detail.ecs.mmt/hotels-entity-detail/api/v2.0/hotels/getUpdatedDisplayPricing
avail.rooms.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/hotels/availPrice
claim.gift.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v1/giftCard/claim
updated.price.occu.less.url=http://hotels-entity-service-detail.ecs.mmt/hotels-entity-detail/api/v2.0/hotels/getUpdatedDisplayPricing
evaluate.filter.rank.order.url=http://persuasions-engine.ecs.mmt/v1.0/filter/evaluate-rule?correlationKey=%s

user.service.url=http://userservice.mmt.mmt/hotels/clientbackend/user/details
user.service.guest.url=http://userservice.mmt.mmt/ext/hotels/clientbackend/user/create/false?guest=true
user.service.update.details.url=http://userservice.mmt.mmt/hotels/clientbackend/user/update
nearby.hotels.url=http://hotel-entity-service-listing.ecs.mmt/hotels-entity-listing/api/v2.0/nearbyHotels

bank.offers.url=http://hotel-entity-service-listing.ecs.mmt/hotels-entity-listing/api/v2.0/hotels/bankOffers

platforms.image.upload.url=http://platform-ugc-orchestrator.ecs.mmt/ugc/uploadFile
submit.answers.url=http://platform-ugc-orchestrator.ecs.mmt/ugc/submitAnswers?clientVersion=%s
program18.url=http://platform-ugc-orchestrator.ecs.mmt/load/program?bookingId=%s&featureEntity=booking_id&lob=%s&metaSrc=%s&clientVersion=%s&deviceId=%s
get.booking.details.url=http://platform-ugc-orchestrator.ecs.mmt/ugc/pendingReviews

pokus.url=http://pokus.mmt.mmt/experiment/assignVariant
hydra.url=http://dpt-fsbrokerapi.ecs.mmt/v2/getFeatures
hydra.user.segment.url=http://dpt-fsbrokerapi.ecs.mmt/v2/getHydraSegments
fetch.collection.url=http://hotel-entity-service-listing.ecs.mmt/hotels-entity-listing/api/v2.0/fetchCollections
updated.emi.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/getUpdatedEmi
webapi.getPolicies.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/hotel/getPolicies?correlationKey=%s&txnKey=%s
validate.coupon.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/validateCoupon
place.lat.lng.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v4.0/hotels/suggest/getplacedetails?id=%s
lat.lng.city.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v4.0/hotels/suggest/getcitydetails?lat=%s&lng=%s
webapi.addons.get.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/hotels/getAddOns
txndata.get.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v3.0/txnData/get/{0}
meta.data.url=http://hotels-entity-service-detail.ecs.mmt/hotels-entity-detail/api/v2.0/city/%s/hotels/metadata
list.personalization.card.url=http://hotel-entity-service-listing.ecs.mmt/hotels-entity-listing/api/v2.0/hotels/listpersonalizedcards
affiliate.update.fee.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/hotels/affiliate/updateFee
affiliate.create.quote.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/hotels/affiliate/createQuote
affiliate.get.quote.data.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/hotels/affiliate/getQuoteDataMerged
hotel.image.url=http://hotels-entity-service-detail.ecs.mmt/hotels-entity-detail/api/v2.0/hotels/images
static.details.url=http://hotels-entity-service-detail.ecs.mmt/hotels-entity-detail/api/v2.0/hotel/staticDetails
city.data.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/city/%s?locationId=%s&locationType=%s
polyglot.url=http://hotels-flock-web.ecs.mmt/api/v1/polyglot/get_assets/?tags=HotelsCG&lang=eng,ara,hin&src=CG-MMT
polyglot.mobgen.url=http://hotels-flock-web.ecs.mmt/api/v1/polyglot/get_assets/?tags=HotelsMobGen&lang=eng,ara,hin&src=CG-MMT
city.guide.url=http://hotel-entity-service-listing.ecs.mmt/hotels-entity-listing/api/v2.0/cityGuide
hotels.hotstore.url=http://hotels-entity-service-detail.ecs.mmt/hotels-entity-detail/api/v2.0/hotels/hotstore?correlationKey=%s
fetch.locations.url=http://hotels-entity-service-detail.ecs.mmt/hotels-entity-detail/api/v2.0/hotels/fetchLocations

hotels.review.summary.url=http://hotels-entity-service-detail.ecs.mmt/hotels-entity-detail/api/v2.0/hotels/review-summary?correlationKey=%s
platform.review.summary.url=http://hotel-entity-service-listing.ecs.mmt/hotels-entity-listing/api/v2.0/hotels/ugc-summary?correlationKey=%s
ugc.category.url=http://hotels-entity-service-detail.ecs.mmt/hotels-entity-detail/api/v2.0/hotels/categorySummary
ugc.reviews.url=http://hotels-entity-service-detail.ecs.mmt/hotels-entity-detail/api/v2.0/hotels/ugc-reviews

kafka.broker.list=kafka-hotels.mmt.mmt:9092
krb.conf.filepath=/etc/kerberos/krb5.conf
jaas.conf.filepath=/etc/kerberos/jaas-cache.conf
enable.kafka.logging=true
threadpool.corepool.size.kafkaThreadPool=100
threadpool.maxpool.size.kafkaThreadPool=100
threadpool.queue.capacity.kafkaThreadPool=100
hotel.data.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/hotels/data

threadpool.corepool.size.userServiceThreadPool=100
threadpool.maxpool.size.userServiceThreadPool=100
threadpool.queue.capacity.userServiceThreadPool=100

threadpool.corepool.size.hydraServiceThreadPool=100
threadpool.maxpool.size.hydraServiceThreadPool=100
threadpool.queue.capacity.hydraServiceThreadPool=100



threadpool.corepool.size.detailServiceThreadPool=50
threadpool.maxpool.size.detailServiceThreadPool=100
threadpool.queue.capacity.detailServiceThreadPool=10


#TODO tune the pool configs
evaluate.filter.rank.order.thread.pool.core.pool=50
evaluate.filter.rank.order.thread.pool.max.pool=100
evaluate.filter.rank.order.thread.pool.queue=100

threadpool.corepool.size.pdt.logging=100
threadpool.maxpool.size.pdt.logging=100
threadpool.queue.capacity.pdt.logging=100

threadpool.corepool.size.listingThreadPool=150
threadpool.maxpool.size.listingThreadPool=150
threadpool.queue.capacity.listingThreadPool=100

thread.pool.keep.alive.seconds=1

payment.checkout.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/hotels/paymentCheckout

corporate.workflow.by.authode.url = http://corp-cb.mmt.mmt/admin/internal/v2/approvals

corporate.update.approval.url = http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/corporate/updateApproval/%s
corporate.update.approvals.url = http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/corporate/updateApprovals
corporate.workflow.info.url = http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/corporate/getApproval/%s
corporate.init.approval.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/corporate/initApproval
corporate.update.policy.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/corporate/updatePolicy
corporate.approvals.info.url = http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/corporate/getApprovals
paylater.eligibility.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/payLaterEligibility
corporate.get.availableguesthouses.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/corporate/availableGuestHouses
MOBILE.LOCATION.COUNT=15
MOBILE.FACILITY.COUNT=15

corp.segments=1135,1152
combo.title.meal.plan.code=AO,EP,BD,CP,SMAP,DMAP,TMAP,MAP,LMAP,LN,DN,AP,AI
cab.card.city.list=CTDUB,CTPATTA,CTBANGKO,CTPHUKE,CTSINGAP,CTBAL9d5bd6c3,CTKUALALU,CTMUANGKR,CTKATHM,CTHONGK,CTCOLO,CTLONDO,CTHOCHI,CTPARc0c94328,CTHANOI,CTABU,CTGUANG,CTISTAN,CTBAK00c5b339,CTTOKY,CTHICH,CTNEWY,CTLANGKA,CTAMSTER,CTMADfda474c6,CTPOKHA,CTKOSAM,CTLASVEG,CTSHANGH,CTPHIPH,CTDHAea6f012d,CTMECCA,CTALMAT,CTZRIC,CTJAKA,CTBARCELON,CTMUSC,CTMELB,CTMILAN,CTSYD,CTROMA,CTSEOU,CTTORON,CTSHAR,CTPHNO,CTDOH,CTFRANKFU,CTNAIR,CTOSAK,CTRIYA,CTLOSANG,CTPRAG,CTKYOTO,CTMEDCA57C76C,CTVIENNA,CTMAURIT,CTFOSH,CTSHENZ,CTLUCER,CTTBILI,CTGILI,CTKANDY,CTBERLI,CTTASHK,CTANCI,CTPENAN,CTSAN48bac4e4,CTINTE,CTPHQU,CTMEXICO,CTEDI,CTNEVS,CTCHIANGMA,CTHIA8c110c08,CTMUNICH,CTLISBO,CTMAKATI,CTCHICAG,CTVENIC,CTATHE,CTMAN02FE50CF,CTANTAL,CTFLORENC,CTBENTH,CTGENTIN,CTBRUSS,CTBUDAP,CTSIEMR,CTPEEa9058e80,CTORLAN,CTAUCKLA,CTKOPH,CTPETAL,CTGOLD,CTPAKKR,CTSANSAL,CTBEIJ,CTCITYOF,CTSEPAN,CTJEDD,CTRASALK,CTNEGOM,CTSALZ,CTHLON,CTSANTOR,CTNUW,CTGALLE,CTJOHO,CTOSLO,CTQUEENS,CTCANCUN,CTHUAH,CTHELSIN,CTCAIR,CTGENV,CTCITY,CTMANCH,CTDSS,CTSUFF,CTNICbda9f265,CTKLN,CTCOMMU,CTCHITW,CTCOPEN,CTQUSA,CTTROMS,CTTAIPE,CTWARSZ,CTROTTE,CTPASIG,CTCATALU,CTYAN58dd2969,CTLAPLA,CTDUBLI,CTNARIT,CTBATAM,CTATLANT,CTMANIL,CTLUMBI,CTDISTRIT,CTGLAS,CTVALE,CTKAM5E2B0445,CTKOYA,CTSTOC,CTXIAM,CTILAL,CTDAMM,CTMUANGCHI,CTSEV65f68f25,CTVANCO,CTMAH0B0B213B,CTADDI,CTAUSTIN,CTGRINDE,CTBIRMI,CTPASAY,CTKRAK,CTFIJ,CTJEJ5FDE0A7C,CTSOPA,CTNAPO,CTMIRIS,CTGREATERVA,CTCHRIS,CTBORACA,CTJANAK,CTIBIZ,CTHAMB,CTYIW,CTMAVO,CTKOTAO,CTSALER,CTNRN,CTBAGM,CTMYKONO,CTROTO,CTINNS,CTBRI,CTREYKJ,CTSANa8cbd434,CTNAGARKO,CTDIVISIONNO6,CTNEWC,CTKLANG,CTSTU,CTKOSAMET,CTFUJAI,CTANTIGU,CTWELIG,CTKEMPT,CTBASEL,CTCANB,CTAJM,CTCASABL,CTDALd657eb8f,CTADE,CTCHANGA,CTSIGIR,CTGIZA,CTZERMA,CTSAP82597fab,CTHIKK,CTBRIS,CTMELAKA,CTETI,CTAMMAN,CTKALUT,CTALKHO,CTLIM86e13e10,CTPHILA,CTQBL,CTREGIO,CTSURAB,CTNHAT,CTBABSH,CTBOLOG,CTWUP,CTNIAGAR,CTANTW,CTZHON,CTPANdd089a1b,CTCASCAI,CTVARE,CTSANTAFDEB,CTKOSHI,CTMOMBAS,CTBORDEA,CTYOKOH,CTSEI,CTNARAYANI,CTQUEZON,CTCOMd6e96dc7,CTPAYS,CTHIGHLAN,CTSANIL,CTELLA,CTCEB,CTKATANA,CTHIROS,CTUNAW,CTACCR,CTMUANGNONT,CTSHAHAL,CTSEAT,CTMANAG,CTDAMBU,CTPRASL,CTPER0737BE6D,CTMILTO,CTLUXO,CTBER00188457,CTMLAG,CTHOBA,CTNAGOY,CTMAPUT,CTVALDO,CTLIVERPO,CTCHITTA,CTEIN,CTBODRU,CTHAEU,CTDEHIW,CTNINGB,CTBINTA,CTKOTAT,CTPORb4020655,CTBORDJE,CTKOTAME,CTPARAA,CTLAG42B7735F,CTPUTR,CTHATY,CTKKOC,CTCAMERONH,CTLAUTER,CTKAZBE,CTDENV,CTCARD,CTJUN1cbd566a,CTNANJI,CTWESTL,CTSGRAV,CTOXFOR,CTSURRE,CTNEWTA,CTBHER,CTYEONS,CTMARRA,CTBASR,CTTOUL,CTBEIRU,CTNYARU,CTMUANGUD,CTRIODEJ,CTWELLIN,CTLYODF58AB63,CTLAUS,CTTHISS,CTWATTA,CTFETH,CTKOLAN,CTBATUM,CTASHSHARQ,CTVLAA,CTCHAAM,CTREUT,CTETHE,CTHAWAL,CTHUc2ad3819,CTHARAR,CTSAN119f4652,CTASWAN,CTHAKO,CTPISA,CTLEIC,CTDAKA,CTMUANGRAY,CTKASK,CTGTEB,CTKUWAI,CTBRUGE,CTWESTSU,CTULLE,CTYORaf238b64,CTNILF,CTLEED,CTDIVISIONN,CTAKKY,CTORT,CTKOTATI,CTMUANGSATUCTCTH,CTBOCAC,CTLUNcd5b27b7,CTSADA,CTKOTASE,CTSIRAC,CTSORRE,CTTAIC,CTHAVAN,CTHOAL,CTTAKUA,CTHWASE,CTTSELIN,CTJUFF,CTMUANGSUR,CTDISTRITOF,CTGENOA,CTESCH,CTLONGF,CTHAMAMA,CTUTRE,CTZANZ,CTSAPP,CTBANGLAM,CTNOTT,CTKON99b00672,CTKOTAKI,CTBUXO,CTESS3b2ec5ca,CTDONGG,CTLUXE,CTBERUW,CTCHONG,CTALAIN,CTBREME,CTISR,CTMELLOR,CTKANAZ,CTTUL01DAD607,CTPORTD,CTKADUW,CTALKHAWR,CTANKAY,CTNORc5ad8740,CTJINA,CTSIGT,CTSAURA,CTLAKEB,CTSEREM,CTBELGRAD,CTWUX,CTGOYAN,CTRHU,CTQINGD,CTTHANY,CTSTOLI,CTBER7152467e,CTFUJIKA,CTTAKUA,CTABUJA,CTPAKCHO,CTKUCHIN,CTSALVADO,CTBEKA,CTNAYP,CTTRIN,CTLAPUL,CTMUANGCHO,CTTANGE,CTOQD,CTDUNE,CTOEIR,CTJUNGB,CTWENZ,CTLCHN,CTYLD,CTLOMB,CTLJUBL,CTCOVE,CTKUAD,CTTRNB,CTVERO,CTKBANG,CTSUWO,CTHANGZ,CTCOX,CTBELF,CTHAMIL,CTESSO,CTMUANGSAM,CTHAMBA,CTQUANZ,CTBRIG,CTIZUMIS,CTMUANGKH,CTUNORGANIZEDIN,CTDHAU,CTBODENS,CTKOTAY,CTCHIBA,CTMUANGCHAN,CTFREIB,CTRIGA,CTMUNIC,CTGMU,CTPALERM,CTSTD,CTGRANAD,CTKAIKO,CTMUANGKA,CTMAESO,CTMARBELL,CTSOUSS,CTPHANTH,CTARRAYY,CTKARACHI,CTTEKAP,CTHURG,CTTHNHP,CTMUANGU,CTHAAR,CTKOTAB,CTMERS,CTVGAN,CTNAH38dc006c,CTKOBE,CTPET5A53875C,CTACORU,CTGIRONA,CTSTM,CTGOMBA,CTSANPAW,CTOFFE,CTWOLL,CTLADIG,CTHSI,CTTAIZH,CTATTIC,CTDIV6c7f5ada,CTTRIE,CTCAMBRI,CTMANGGA,CTQUB,CTMARSEI,CTHALT,CTSANKTG,CTSOLOT,CTBRAT,CTSAMARQ,CTTAOR,CTGDASK,CTOTOP,CTSRIM,CTSYL,CTDARMS,CTYANGO,CTWHIT,CTMASAIM,CTSALAL,CTWIE,CTASEE,CTCHENGDU,CTBRIGHT,CTJIAX,CTKAWASA,CTSHARM,CTCANNE,CTGUADALA,CTBOKSB,CTLUTO,CTGIEE,CTPOTS,CTLTec33d796,CTBARISA,CTRANGP,CTTIANJ,CTTAINAN,CTSOUTHAM,CTKENT,CTLUGA,CTTALLI,CTOOS,CTMSAM,CTCOMMUN,CTFREIS,CTASTUR,CTLEB,CTHAMPSHI,CTPUNTAC,CTBTR,CTTORIN,CTBCN,CTKIRU,CTTELF,CTNINHB,CTHAUTR,CTTEAN,CTWARE,CTCORK,CTKELA,CTKINON,CTBAR71487112,CTLOURE,CTAQA,CTDAHM,CTPHLC,CTKLE,CTMAL799c0b4f,CTZHUZ,CTMONTERRE,CTJAFF,CTZELENOV,CTZHE,CTLAG29e298a6,CTANGELE,CTLLORE,CTDRES,CTREYKJA,CTSETIU,CTMLADB,CTNEUC,CTVNHY,CTEME7bbda84c,CTSOHAR,CTCHESHI,CTMUANGP,CTSERE,CTMAINZ,CTESTEBAN,CTMAHARAG,CTKORAL,CTBAYRE,CTNIEDE,CTPALEM,CTSTAVA,CTNGORO,CTLEEU,CTAUGSB,CTTAOY,CTKILIFI,CTOBW,CTBRESC,CTHAUTES,CTTAB5A14EDDF,CTVILAFRA,CTUUS,CTFRS,CTMAIT,CTDAED,CTPOZN,CTHOC,CTFLA71656793,CTWANAK,CTMOHAMME,CTMETTM,CTJEP,CTCATANI,CTMANNH,CTJENA,CTGLEN,CTMABAL,CTCUMBR,CTSHOO,CTSNDE,CTFREDE,CTCHERA,CTGRO,CTROERM,CTVIZC,CTPRAE694B73E,CTSEONG,CTMUNT,CTNHO,CTVILN,CTKATHA,CTESTEP,CTCAPEW,CTCHAMO,CTDIVIS,CTALBUF,CTSOLL,CTGANDAK,CTDALI,CTDEPO,CTMAAST,CTGREATERG,CTMIR93b383b1,CTFUJIY,CTVALKE,CTALP,CTHOHH,CTLIND,CTPLUA,CTIPOH,CTALFARW,CTLOUL,CTCLU,CTABER,CTVIC07186251,CTALMER,CTLINCOLNS,CTTREVI,CTFUJIN,CTILLE,CTGTT,CTSAHAM,CTWAT,CTMOPAN,CTSCHAF,CTKOTABH,CTBUCKI,CTALWAK,CTBANGKR,CTCUNE,CTKUTAIS,CTSHIMOD,CTSANeef556f0,CTCRETE,CTKOTAMAL,CTALASI,CTCAUAY,CTPLAYAD,CTDUS,CTGASA,CTHAIN,CTALICA,CTSANTACRUZDETE,CTSOLIH,CTTAKUAT,CTARANY,CTMANDALA,CTENTE,CTIMBUL,CTVISP,CTPEKA,CTMANJUN,CTISHIG,CTSOUTHAEG,CTUTSU,CTCHISINA,CTBARUER,CTNARASH,CTTANGAL,CTMUANGPHIT,CTMACEI,CTKOTASET,CTBOURN,CTTANGER,CTHAARLE,CTHEI,CTCHANGZ,CTCASERT,CTWARW,CTROTE,CTRUDE,CTLASPE,CTGARMI,CTWAKEFI,CTFUKUO,CTVIK5EBA78E4,CTNARa64cd577,CTSANC2F7EAB6,CTFLAK,CTKISU,CTBAD1a0e956b,CTRABAT,CTBATHAN,CTKKOKU,CTKARLSR,CTALAJ,CTNANCH,CTKAYS,CTLIAOC,CTLUOY,CTBATU,CTNEWPOR,CTYORf45309a9,CTSIEN,CTSTIRL,CTBRASOV,CTBRAUNS,CTAGAD,CTNYARI,CTNAGASA,CTTAUP,CTNELSO,CTSAAN,CTTHUNA,CTFS69b81462,CTCOURCH,CTKUAN,CTFARN,CTARG,CTDIC,CTGUARU,CTTUT,CTOTTAW,CTNAIV,CTTROND,CTSHIZUO,CTNORTHAMPTONS,CTSANLUIST,CTASCHA,CTCHEF,CTRAMBU,CTLAPLA,CTXIAd5032ee8,CTMOD,CTKOSIC,CTHOR,CTSLE,CTSANANDRSC,CTBERGAM,CTJICA,CTAQT,CTYOR4abdb732,CTINNSB,CTEDEN,CTMAKAS,CTVNG,CTPLE,CTNASSAU,CTREGION,CTWRZ,CTFLORI,CTSAIY,CTLICH,CTFRASE,CTMAUS,CTOTAR,CTKESB,CTZUf3ee5ec4,CTWESTB,CTMOSKE,CTSHAOX,CTJINHU,CTSEINE,CTSAINTMIC,CTSETI,CTTIJUA,CTMOAL,CTNAMc90bf47b,CTSUKO,CTTRIES,CTREGGI,CTSQU,CTTAKAY,CTGALW,CTLANCA,CTSURF,CTPHA5ca594b1,CTCOBUR,CTMARES,CTBIDD,CTTELAVI,CTNUA,CTBUC3cbb8a25,CTKAf11301aa,CTESSE,CTHOUM,CTCALAM,CTRENFR,CTMUANGNAKH,CTERL,CTMAINTA,CTCOL,CTLABUA,CTMUAaeb6fc50,CTXUYN,CTTWIZ,CTSIDO,CTMAGE,CTZIB,CTZLN,CTUNL,CTMONTEGO,CTCURITI,CTSAFR,CTLOIRE,CTKITAK,CTMAAN,CTVILANOVAD,CTWRE,CTPUEBLA,CTPUERTOPRI,CTSTOKE,CTNPAL,CTSUIT,CTCAGAY,CTSHNA,CTSUMBATI,CTEHL,CTLAKSHM,CTKURUNE,CTKRISTIA,CTMERea486750,CTSILAO,CTAIGL,CTHULUL,CTLENZ,CTPRT,CTPADUA,CTTOKO,CTWARRIN,CTPUYD,CTDODOM,CTDORT,CTNEUU,CTBATANGAS,CTSIWe0fb617b,CTSHANT,CTEURO,CTBUSA,CTCUSC,CTLUDW,CTWILTS,CTKUAL,CTTHAP,CTORAD,CTMECHI,CTSAMAR,CTCHANGW,CTMATSUMO,CTMOROGO,CTMADAB,CTSANRAFAELC,CTODA,CTARUSH,CTGAZIE,CTBYKE,CTSHEF,CTALM0b115cd3,CTMONTP,CTMUANGNAK,CTCARTAGE,CTKNYS,CTGIMP,CTLEIPZ,CTBRED,CTREUN,CTPAI1dc9aafe,CTGRES,CTSANPEDROS,CTALUL,CTSHYM,CTPHUNP,CTNYK,CTBOJAN,CTCALW,CTHAPUT,CTLAML,CTNEVS,CTWADIM,CTPHRAN,CTSIB0d2dab21,CTBYR,CTDEVE,CTPAIH,CTMLH,CTLASP,CTCALVA,CTCITYO,CTWESE,CTSIXT,CTPUERTOGA,CTSANea273a2e,CTARNH,CTINBN,CTWOLV,CTBERC,CTNORDD,CTALQUA,CTPALUG,CTTSKA,CTHAM67a6a552,CTFRUTI,CTLOCAI,CTNAKUR,CTBALIK,CTKICI,CTSISA,CTRHN,CTYATIY,CTEMIRATE,CTNORDS,CTSELUKL,CTONN,CTWOLFS,CTNST,CTTORRES,CTASUNCI,CTMEX,CTHAIF,CTZHANGJ,CTOSS,CTNAMPU,CTALAHM,CTLEICE,CTYVE,CTCAG,CTPEL,CTHAUTEV,CTKAPUAS,CTCLA5ad68446,CTPAL4188ab9b,CTMRI,CTHUEL,CTWESTC,CTSVEIT,CTDAVAO,CTGUATEMA,CTLAHOR,CTHAUTSD,CTDORDR,CTCENTRALKO,CTCACAD,CTBOS,CTOSMA,CTTHASA,CTPFO,CTREM,CTSIERR,CTXIV,CTANYA,CTCEN898ebf23,CTWESTMORL,CTZUG,CTMUANGB,CTDAAN,CTPORTAD,CTASSUWA,CTALGHAR,CTGIAV,CTNAGca72c25d,CTSWIN,CTWUHA,CTBRATIS,CTSANMIGUELDEL,CTYNB,CTUMGU,CTSKA128f13cf,CTSHERT,CTNIDW,CTAOM,CTLAUNC,CTNEG3CECAB64,CTTASSL,CTTELAV,CTSOLNA,CTBORKE,CTLIBERI,CTNINHK,CTGENTO,CTCOF,CTBOUME,CTPAROSI,CTKATOW,CTLAN21212c0b,CTERDI,CTHSIN,CTAIN4204911f,CTSUMBAB,CTDON4cc15e7a,CTNPA,CTTHUa938e47d,CTCAMPB,CTJIE,CTKASHIW,CTJEMP,CTZIY,CTSUY,CTMALIND,CTPRAN,CTROSTO,CTTHES,CTUDE,CTPYRN,CTKASKA,CTCILE,CTDAMIET,CTKOICE,CTPLOV,CTSOPR,CTEMBI,CTAUSTUR,CTPANAD,CTRIMIN,CTSARAJ,CTSILHO,CTMENDRI,CTKRIST,CTESB,CTAUGS,CTNEU,CTNYAU,CTTAMPE,CTDONS,CTBALOV,CTGYEO,CTBINTU,CTBOD2730e4ef,CTCINQU,CTKARLS,CTHOL1dc45f16,CTCAPRI,CTSENDA,CTJEONJ,CTSAWI,CTKEEL,CTVENL,CTPICTO,CTPALMERS,CTMUANGNAKHON,CTNORF,CTOEG,CTSREE,CTBIEL,CTKAOHS,CTMERdbd4124f,CTLIMBUR,CTSYRA,CTTABAC,CTKOTAC,CTDUNDE,CTSHAMA,CTMISb773cd2f,CTGLO,CTSINTR,CTSTEI,CTCORNW,CTMANDALUY,CTLAG55ccd4b1,CTBILLU,CTWROC,CTSHAOY,CTSURA,CTTAURAN,CTTSUKU,CTBOHOL,CTKEMAR,CTKHULN,CTSRIJA,CTNORDW,CTSOUTHGL,CTCTE,CTKENTIN,CTTAKAOK,CTPETERBOR,CTMRD,CTPLAR,CTDEVON,CTGYR,CTHATEC3DEC76,CTALKMA,CTGRE5d239b1c,CTHABA,CTHAM,CTHEF,CTMRKIS,CTPORTSMOU,CTPAPH,CTTAIY,CTBLE098c7fb2,CTLOI,CTENSC,CTESCAZ,CTATAM,CTNONGKHA,CTTAMANO,CTMATOS,CTKESSE,CTISM,CTKULON,CTNIKa79cb9b8,CTKAMPOT,CTKHLONGY,CTCHIANGK,CTBAOT,CTAKUREY,CTARUS,CTBANYU,CTLINZ,CTULM5613fdda,CTQUAI,CTQBL,CTMOER,CTPERUG,CTMANCHE,CTMUANGNAKHO,CTATAQA,CTHSAV,CTDIV27fdc47e,CTMANAD,CTCHEMNI,CTEKU,CTYAMANA,CTSANDG,CTLILLE,CTRAVENN,CTWAIT,CTSHIRAKAWA,CTVINAD,CTPUERTOV,CTREMS,CTLESUN,CTCRDO,CTLNK,CTDANDO,CTBASSC,CTANTRIM,CTBEP,CTPORTOALEGRE,CTPOTH,CTNINHH,CTKUF,CTBNC,CTCORDO,CTDEVINU,CTMATERA,CTUPPLA,CTVALLET,CTALANY,CTPURWA,CTTUGUE,CTSASE,CTBOGR,CTFRANKF,CTTAPAC,CTMONZ,CTKFG,CTKAGOS,CTDONc1658724,CTBNHT,CTKUBAN,CTTAIF,CTMER7c95e958,CTSAHd2548873,CTARACH,CTHVO,CTCOZ,CTLINY,CTMEDW,CTKIRKE,CTMUR1d66456a,CTNELS,CTNUEVOL,CTMWEA,ANOMALY,CTMARBU,CTSNO,CTVNHLO,CTREYN,CTCAPEB,CTSANDN,CTWAZ,CTMUANGNA,CTKILIF,CTTANG,CTTAGAY,CTMATARA,CTSUNGA,CTSANTOTOM,CTBRATISL,CTFAR9bfa3ec0,CTDURH,CTMUANGR,CTWARRN,CTDbbe3fe82,CTAQTO,CTAKUR,CTMALAY,CTPORT,CTNEW,CTMORNIN,CTKOTD25C00F8,CTWIES,CTPASB,CTCONSTAN,CTBERG,CTCAML,CTROTTNE,CTSCHWA,CTWESTT,CTMUANGNON,CTILINA,CTCHAREN,CTDONCA,CTSTEENW,CTMENDER,CTMILD,CTFIN,CTKESTE,CTMANNAR,CTUTHUN,CTKALPIT,CTBATUP,CTTALG,CTTHIN,CTRAIY,CTMORIO,CTSOGN,CTRZE,CTBRAN,CTCHITO,CTCOROMAN,CTMAI9f188d61,CTMOTOB,CTREA5e202802,CTTOLf67fe007,CTCASTELLAM,CTMALAN,CTWAG,CTNEA,CTJAMBI,CTGULP,CTRHEINI,CTNANTON,CTNA354,CTMONTH,CTAUSTU,CTBEGAV,CTBORGA,CTSOMER,CTCOIM,CTBEL905b3b68,CTRMQ,CTBIAY,CTOSTUN,CTALPI,CTCARMA,CTKARLO,CTMUANGSI,CTMAHI,CTLAMEG,CTOKAYA,CTPIJ,CTSURSE,CTKISHIW,CTLECCO,CTTACL,CTXUZ,CTBERGAMA,CTBADA,CTALWU,CTLMH,CTAMBAG,CTFUJIE,CTHUALI,CTKINGSTON,CTBALATON,CTGOTE,CTLARN,CTMILAS,CTWADIR,CTWYO,CTYRE,CTLAEM,CTGLA,CTBADEN,CTCUA,CTDEVO,CTVARd456bd34,CTPIAS,CTSAN6350da1a,CTSHRO,CTOUC,CTWORCES,CTZWI,CTVESTUR,CTROVA,CTSOUTHTY,CTKUSIN,CTJOM,CTHELLN,CTMEDE,CTKOTASO,CTKALIB,CTMUANGPRA,CTHERMO,CTGEIR,CTLUL,CTOLB,CTOSL,CTINGO,CTWESTV,CTWHITEH,CTSTRAU,CTESSL,CTCENTRALOT,CTHALLES,CTKAJI,CTKOBU,CTBLU,CTENN,CTBENIDO,CTHIRO,CTMANGAU,CTJINI,CTRHEINK,CTWASS,CTNOBOR,CTFLEN,CTCIUDADR,CTROSES,CTYARRA,CTTATR,CTBRADFO,CTARUB,CTHELLA,CTTOR77d3b45e,CTEYJA,CTBANDUN,CTESKK,CTNAMD,CTVIENN,CTNISE,CTSONDR,CTZAR1d0a13a4,CTSTO,CTOUA,CTKGE,CTDARE,CTGYL,CTKLAE,CTMUANGCHU,CTTHD,CTSURb87ac7f5,CTMOJ,CTAGRI,CTBRIND,CTSUZH,CTTAJI,CTLAUG,CTBOUZAR,CTBANC,CTNYON,CTPETERBO,CTHUZH,CTADALA,CTBBL,CTBAHR,CTALESS,CTASCO,CTSALISB,CTTEKK,CTTOYA,CTHIME,CTDUMA,CTBAUNG,CTNISHIN,CTTAQA,CTKOBL,CTLOIREA,CTCHANGSH,CTKIEL,CTFIF,CTGRAZ,CTNILP,CTLAMBT,CTCORAN,CTHEY,CTCESS,CTBINH,CTBUAN,CTTRABZ,CTJINJ,CTAKI,CTMUHARR,CTFRT,CTHARST,CTKERR,CTLELY,CTDIVISIONNO11,CTKEB,CTKULAI,CTCALFE4B5ED0,CTBANTAK,CTGEB,CTMADRI
mypat_exclusive_rate_segmentId.list=1180,1190
##metric properties
metric.name=Hotels-ClientGateway-Errors-
metric.time.in.mins=2
aws.metric=DEPLOYMENT_VERSION

#gcc common headers for all downstream
gcc.downstream.common.headers=region,language,currency

approval.page.url=https://www.makemytrip.com/hotels/approval/?workflowId=

#persuasion on desktop stay type tool tip
desktop.tool.tip.persuasions = {"HOSTEL":{"imageUrl":"","toolTipHeading":"WHY_HOSTEL","data":["LIGHT_ON_YOUR_WALLET","CENTRALLY_LOCATED","GREAT_FOR_SOCIALIZING","YOUTHFUL_CONNECT"]},"VILLA":{"imageUrl":"","toolTipHeading":"WHY_VILLA","data":["VALUE_FOR_MONEY","IDEAL_FOR_GROUP_STAY","SUPER_SPACIOUS","COMPLETE_PRIVACY"]},"HOMESTAY":{"imageUrl":"","toolTipHeading":"WHY_HOMESTAY","data":["LIGHT_ON_YOUR_WALLET","HOMELY_COMFORTS","BEST_LOCAL_GUIDANCE","CENTRALLY_LOCATED"]},"COTTAGE":{"imageUrl":"","toolTipHeading":"WHY_COTTAGE","data":["IDEAL_FOR_GROUP_STAY","VALUE_FOR_MONEY","SUPER_SPACIOUS","ACCESS_TO_BEAUTIFUL_OUTDOOR_SPACES"]},"APARTMENT":{"imageUrl":"","toolTipHeading":"WHY_APARTMENT","data":["VALUE_FOR_MONEY","CENTRALLY_LOCATED","COMPLETE_ACCESS_TO_PROPERTY","SAVINGS_ON_LAUNDRY_AND_FOOD"]}}

amenities.altacco.icon=Kitchen,Kitchenette,Caretaker,Fireplace
group.booking.review.page.cards=BEST_PRICE_GUARANTEE_CARD,POST_BOOKING_CARD
group.booking.thank.you.page.cards=POST_BOOKING_CARD
star.host.icon.desktop=https://promos.makemytrip.com/altaccoimages/star_host_details_desktop.png
star.host.icon.app=https://promos.makemytrip.com/altaccoimages/star_host_details_new.png
mypartner.location.restricted.icon.url =https://imgak.mmtcdn.com/b2b/images/dt/deniedIcon.webp

#persuasions placeholders to be blocked for demand concentration
desktop.persuasion.placeholders.demand.concentration.tobe.blocked=PC_MIDDLE_5,PC_MIDDLE_9
apps.persuasion.placeholders.demand.concentration.tobe.blocked=PLACEHOLDER_CARD_M3,PLACEHOLDER_CARD_M6
active.languages=eng,hin,ara
mmt.value.stays.category.icon.url.desktop=https://promos.makemytrip.com/Hotels_product/Value_Stays/v2/logo/ValueStays-3.png
filter.conditions={"range":{"minValue":0,"maxValue":3000},"categoriesIncluded":["Premium Properties"],"categoriesExcluded":["MMT Value Stays"]}
suppressed.houseRules.list=2,47
hidden.gems.persuasion.style={"Desktop":{"hiddenGem":{"style":{"textColor":"#4A4A4A","styleClasses":["pc__peitho","pc__quote"]}},"hiddenGemIcon":{"iconurl":"https://promos.makemytrip.com/AltAcco/Hidden_gems_new.png","style":{"styleClasses":["pc__hiddenGem"]}},"homeStayTitle":{"style":{"styleClasses":["htlHighlt__title"]},"topLevelStyle":{"styleClasses":["htlHighlt"]}},"homeStaySubTitle":{"style":{"styleClasses":["htlHighlt__subTitle"]},"topLevelStyle":{"styleClasses":["htlHighlt"]}}},"Apps":{"hiddenGem":{"style":{"textColor":"#4A4A4A"}},"hiddenGemIcon":{"iconurl":"https://promos.makemytrip.com/AltAcco/Hidden_gems_new.png","style":{"iconHeight":24,"iconWidth":94}},"homeStayTitle":{"style":{"textColor":"#000000","fontType":"B"}},"homeStaySubTitle":{"style":{"textColor":"#4a4a4a"}}},"PWA":{"hiddenGem":{"style":{"textColor":"#4A4A4A"}},"hiddenGemIcon":{"iconurl":"https://promos.makemytrip.com/AltAcco/Hidden_gems_new.png","style":{"iconHeight":24,"iconWidth":94}},"homeStayTitle":{"style":{"textColor":"#0061aa","fontSize":"MEDIUM","fontType":"B"},"topLevelStyle":{"bgColor":"#e5f3ff","fontType":"B","bgUrl":"leftBlueLineBg"}},"homeStaySubTitle":{"style":{"textColor":"#4a4a4a"},"topLevelStyle":{"bgColor":"#e5f3ff","fontType":"B","bgUrl":"leftBlueLineBg"}}}}

family.friendly.tracking.text=family_friendly_property
pc.top.section.Allowed.Sections=PERSONALISED_PICKS_HOTELS,LISTING_MAP
pc.top.section.persuasion.desktop={"template":"MULTI_PERSUASION_H","data":[{"hasAction":false,"persuasionType":"HEADING","html":false,"style":{"styleClasses":["pc__locationPerNew"]},"id":"LOC_PERSUASION_1","text":"{HOTEL_TAG}"}],"placeholder":"SINGLE"}
pc.top.section.persuasion.apps={"data":[{"id":"LOC_PERSUASION_1","iconurl":"https://promos.makemytrip.com/Hotels_product/Persuasion_Icons/tips-icon.png","text":"{HOTEL_TAG}","hasAction":false,"style":{"textColor":"#a5572a","fontSize":"MID","fontType":"B","iconWidth": 16,"iconHeight": 16},"multiPersuasionPriority":0,"horizontal":false,"html":false}],"placeholder":"PLACEHOLDER_CARD_M6","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}
personalizedPicks.icon.url.tag={"PREFERRED_BY_COMPANY":"https://promos.makemytrip.com/mybiz/hotels/Preferred.png","LAST_BOOKED_HOTELS":"https://promos.makemytrip.com/mybiz/hotels/PrevBooked.png","RECENTLY_VIEWED_HOTELS":"https://promos.makemytrip.com/mybiz/hotels/RecViewed.png","BOOKED_BY_COMPANY":"https://promos.makemytrip.com/mybiz/hotels/BookedColl.png"}
personalizedPicks.style.class.tag.desktop={"PREFERRED_BY_COMPANY":"pc__companyPreferredPerNew","LAST_BOOKED_HOTELS":"pc__prevBookedPerNew","RECENTLY_VIEWED_HOTELS":"pc__recBookedPerNew","BOOKED_BY_COMPANY":"pc__bookedCollPerNew"}
personalizedPicks.color.tag.apps={"PREFERRED_BY_COMPANY":"#A5572A","LAST_BOOKED_HOTELS":"#007E7D","RECENTLY_VIEWED_HOTELS":"#4A4A4A","BOOKED_BY_COMPANY":"#4A4A4A"}
persuasion.place.holders.to.show={"SIMILAR_HOTELS":["PLACEHOLDER_IMAGE_LEFT_TOP","PLACEHOLDER_CARD_M4","PLACEHOLDER_CARD_M1","PLACEHOLDER_CARD_M2"],"SIMILAR_HOTELS_DT":["PC_MIDDLE_2","PC_MIDDLE_8"]}
placeholder.to.show.section.map={"PERSONALISED_PICKS_HOTELS":["PC_MIDDLE_8","PC_MIDDLE_2","PC_IMG_ANNOTATION","PC_TOP_SECTION","PLACEHOLDER_IMAGE_LEFT_TOP","PLACEHOLDER_CARD_M4","PLACEHOLDER_CARD_M1","PLACEHOLDER_CARD_M6","PLACEHOLDER_BOTTOM_BOX_M","PC_BOTTOM_BOX","PC_RIGHT_3"],"LISTING_MAP":["PC_MIDDLE_1","PC_MIDDLE_2","PC_MIDDLE_8","PC_TOP_LEFT","PC_TOP_SECTION","PLACEHOLDER_IMAGE_LEFT_TOP","PLACEHOLDER_CARD_M4","PLACEHOLDER_CARD_M1","PLACEHOLDER_CARD_M6"]}
section.bg.color.map={"PERSONALISED_PICKS_HOTELS":"#CEF1EB"}
mmt.ratings.count.exp.threshold=50
age.qualifying.code=10
food.dining.min.count.config=3

# for Last Minute Deal Filter Pill handling
app.version.android=9.1.6
app.version.ios=9.1.6

app.version.android.currency=9.5.2
app.version.ios.currency=9.1.9

consul.enable=true

food.menu.position.config=2
homestay.bedType.priority.order=King Bed,Queen Bed,Double Bed,Twin Bed,Single Bed,Bunk Bed,Standard Bed,Sofa Bed,Sofa Cum Bed,2 Seater Sofa,3 Seater Sofa,5 Seater Sofa,Mattress,Cot,Futon,Murphy,Tatami Mats
#Config for zone-bounds
shortstays.zone.bounds={"ZNDELA611626E":{"ne":{"lng":77.60,"lat":28.97},"sw":{"lng":76.96,"lat":28.38}}}
shortstays.direction.text.color=#0C58B4
#Config to get driving duration text based on drivingDuration time
#Key -> time frame in minutes, Value -> driving duration text
listing.driving.duration.buckets={"0-60":"LISTING_DRIVING_DURATION_BUCKET_1","60-90":"LISTING_DRIVING_DURATION_BUCKET_2","90-120":"LISTING_DRIVING_DURATION_BUCKET_3","120-150":"LISTING_DRIVING_DURATION_BUCKET_4","150-180":"LISTING_DRIVING_DURATION_BUCKET_5","180-240":"LISTING_DRIVING_DURATION_BUCKET_6","240-300":"LISTING_DRIVING_DURATION_BUCKET_7","300-360":"LISTING_DRIVING_DURATION_BUCKET_8","360":"LISTING_DRIVING_DURATION_BUCKET_9"}
#Config for negotiated rates for hotels.
listing.deep.link.url=https://www.makemytrip.com/hotels/hotel-listing?topHtlId={0}&hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&roomStayQualifier={5}&_uCurrency={6}&checkAvailability={7}
listing.deep.link.url.global=https://www.makemytrip.global/hotels/hotel-listing?topHtlId={0}&hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&roomStayQualifier={5}&_uCurrency={6}&checkAvailability={7}
negotiated.rates.delayed.confirmation.no.of.hours=4
special.fare.persuasion.style={"DESKTOP":{"PC_RIGHT_1_1":{"style":{"styleClasses":["specialFareTag","pushRight"]}},"PC_RIGHT_3":{"hover":{"style":{"styleClasses":["specialFareInfo-tooltip"]}},"style":{"styleClasses":["specialFareInfo"]}}},"APPS":{"PLACEHOLDER_BOTTOM_BOX_M":{"style":{"bgColor":"#FFF6E8","textColor":"#CF8100","fontType":"B","fontSize":"SMALL","iconHeight":16,"iconWidth":16},"iconurl":"https://promos.makemytrip.com/images/myBiz/hotels/Info_Icon.png", "topLevelStyle":{"gravity":"center"}},"PLACEHOLDER_PRICE_BOTTOM_M":{"topLevelStyle":{"bgGradient":{"start":"#EEAF4C","end":"#CE6112"}},"style":{"textColor":"#FFFFFF","fontType":"B","fontSize":"SMALL","maxLines":1}}}}
corp.one.on.one.segmentId=1135
negotiated.rate.icon.url=https://promos.makemytrip.com/images/myBiz/hotels/Info_Icon.png
negotiated.rate.icon.url.newApp=https://promos.makemytrip.com/mybiz/RTBinfoIcon2.png

#Persuasion Config for Flyer Exclusive rate Persuasion in Review Page
flyer.exclusive.rates.persuasion.apps={"data":[{"id":"62bd9677feb541192bd483a1","iconurl":"https://promos.makemytrip.com/Growth/Images/B2C/flyer_new_tag_listing.png","style":{"iconHeight":27,"iconWidth":139},"persuasionType":"FLYER_EXCLUSIVE_RATES_PERSUASION","hasAction":false,"html":false}],"placeholder":"PLACEHOLDER_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}
flyer.exclusive.rates.persuasion.DT={"data":[{"id":"62bd9677feb541192bd483a1","iconurl":"https://promos.makemytrip.com/Hotels_product/DT-Flyer/fyd-listing.png","style":{"styleClasses":["pc__flyerExclusiveTag"]},"persuasionType":"FLYER_EXCLUSIVE_RATES_PERSUASION","hasAction":false,"html":false}],"placeholder":"PC_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}
flyer.exclusive.rates.persuasion.pwa={"data":[{"id":"62bd9677feb541192bd483a1","iconurl":"https://promos.makemytrip.com/Growth/Images/B2C/flyer_new_tag_listing.png","style":{"styleClasses":["pc__flyerExclusiveTag"]},"persuasionType":"FLYER_EXCLUSIVE_RATES_PERSUASION","hasAction":false,"html":false}],"placeholder":"PLACEHOLDER_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}
#Persuasion Config for Flyer Deal GCC rate Persuasion in Review Page
flyer.deal.rates.persuasion.apps={"data":[{"iconurl":"https://promos.makemytrip.com/GCC/flyer/flystayreview.png","style":{"iconHeight":27,"iconWidth":139},"persuasionType":"FLYER_EXCLUSIVE_RATES_PERSUASION","hasAction":false,"html":false}],"placeholder":"PLACEHOLDER_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}
flyer.deal.rates.persuasion.DT={"data":[{"iconurl":"https://promos.makemytrip.com/GCC/flyer/flystayreview.png","style":{"styleClasses":["pc__flyerExclusiveTag"]},"persuasionType":"FLYER_EXCLUSIVE_RATES_PERSUASION","hasAction":false,"html":false}],"placeholder":"PC_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}
flyer.deal.rates.persuasion.pwa={"data":[{"iconurl":"https://promos.makemytrip.com/GCC/flyer/flystayreview.png","style":{"styleClasses":["pc__flyerExclusiveTag"]},"persuasionType":"FLYER_EXCLUSIVE_RATES_PERSUASION","hasAction":false,"html":false}],"placeholder":"PLACEHOLDER_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}

vistara.exclusive.rates.persuasion.apps={"data":[{"id":"62bd9677feb541192bd483a1","iconurl":"https://promos.makemytrip.com/Hotels_product/cross_sell/vistara-taj/Vistaraexcl-listing-mob.png","style":{"iconHeight":27,"iconWidth":139},"persuasionType":"VISTARA_EXCLUSIVE_RATES_PERSUASION","hasAction":false,"html":false}],"placeholder":"PLACEHOLDER_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}
vistara.exclusive.rates.persuasion.DT={"data":[{"id":"62bd9677feb541192bd483a1","iconurl":"https://promos.makemytrip.com/Hotels_product/cross_sell/vistara-taj/Vistaraexcl-listing-dt.png","style":{"styleClasses":["pc__flyerExclusiveTag"]},"persuasionType":"FLYER_EXCLUSIVE_RATES_PERSUASION","hasAction":false,"html":false}],"placeholder":"PC_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}
vistara.exclusive.rates.persuasion.pwa={"data":[{"id":"62bd9677feb541192bd483a1","iconurl":"https://promos.makemytrip.com/Hotels_product/cross_sell/vistara-taj/Vistaraexcl-listing-mob.png","style":{"styleClasses":["pc__flyerExclusiveTag"]},"persuasionType":"FLYER_EXCLUSIVE_RATES_PERSUASION","hasAction":false,"html":false}],"placeholder":"PLACEHOLDER_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}

sale.campaign.persuasion.apps = {"data":[{"id":"SALE_CAMPAIGN","style":{"iconHeight":22,"iconWidth":128,"styleClasses":["pc__flyerExclusiveTag"]},"persuasionType":"SALE_CAMPAIGN"}],"placeholder":"PLACEHOLDER_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}
sale.campaign.persuasion.DT = {"data":[{"id":"SALE_CAMPAIGN","style":{"styleClasses":["pc__flyerExclusiveTag"]},"persuasionType":"SALE_CAMPAIGN","hasAction":false,"html":false}],"placeholder":"PC_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}


#Persuasion Config for Bus Exclusive rate Persuasion in Review Page
bus.exclusive.rates.persuasion.apps={"data":[{"id":"62bd9677feb541192bd483a2","iconurl":"https://promos.makemytrip.com/Hotels_product/cross_sell/bus_listing_tag_3x.png","style":{"iconHeight":27,"iconWidth":139},"persuasionType":"FLYER_EXCLUSIVE_RATES_PERSUASION","hasAction":false,"html":false}],"placeholder":"PLACEHOLDER_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}
bus.exclusive.rates.persuasion.DT={"data":[{"id":"62bd9677feb541192bd483a2","iconurl":"https://promos.makemytrip.com/Hotels_product/cross_sell/DT/bus_listing_DT_3x.png","style":{"styleClasses":["pc__flyerExclusiveTag"]},"persuasionType":"FLYER_EXCLUSIVE_RATES_PERSUASION","hasAction":false,"html":false}],"placeholder":"PC_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}
bus.exclusive.rates.persuasion.pwa={"data":[{"id":"62bd9677feb541192bd483a2","iconurl":"https://promos.makemytrip.com/Hotels_product/cross_sell/DT/bus_listing_DT_3x.png","style":{"styleClasses":["pc__flyerExclusiveTag"]},"persuasionType":"FLYER_EXCLUSIVE_RATES_PERSUASION","hasAction":false,"html":false}],"placeholder":"PLACEHOLDER_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}


#Persuasion Config for Train Exclusive rate Persuasion in Review Page
train.exclusive.rates.persuasion.apps={"data":[{"id":"62bd9677feb541192bd483a3","iconurl":"https://promos.makemytrip.com/Hotels_product/cross_sell/rails_listing_3x.png","style":{"iconHeight":27,"iconWidth":139},"persuasionType":"FLYER_EXCLUSIVE_RATES_PERSUASION","hasAction":false,"html":false}],"placeholder":"PLACEHOLDER_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}
train.exclusive.rates.persuasion.DT={"data":[{"id":"62bd9677feb541192bd483a3","iconurl":"https://promos.makemytrip.com/Hotels_product/cross_sell/DT/rails_listing_DT_3x.png","style":{"styleClasses":["pc__flyerExclusiveTag"]},"persuasionType":"FLYER_EXCLUSIVE_RATES_PERSUASION","hasAction":false,"html":false}],"placeholder":"PC_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}
train.exclusive.rates.persuasion.pwa={"data":[{"id":"62bd9677feb541192bd483a3","iconurl":"https://promos.makemytrip.com/Hotels_product/cross_sell/DT/rails_listing_DT_3x.png","style":{"styleClasses":["pc__flyerExclusiveTag"]},"persuasionType":"FLYER_EXCLUSIVE_RATES_PERSUASION","hasAction":false,"html":false}],"placeholder":"PLACEHOLDER_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}

#Persuasion Config for BNPL Persuasion in Review Page
bnpl.review.page.persuasion.apps={"data":[{"id":"60058d3faf73b51d43e4af93","persuasionType":"BNPL_AT_0","text":"<b>Book Now Pay Later</b> option available","hasAction":false,"style":{"bgColor":"#E6FFF9"},"multiPersuasionPriority":0,"html":true}],"placeholder":"PLACEHOLDER_PRICE_BOTTOM","template":"DEAL_BOX_IMAGE_TEXT","templateType":"DEFAULT"}
addon.info.hotel.tag={"title":"RECOMMENDED","color":"#834CC5"}
addon.info.most.popular.tag={"title":"MOST POPULAR","color":"#007E7D","persuasionBgUrl":"https://promos.makemytrip.com/GCC/next_best/most_popular.png","horizontal":"8","vertical":"0","bgUrl":"https://promos.makemytrip.com/GCC/next_best/bg_new.png"}
exclude.hotel.tag.types=VALUE_STAYS,HIDDEN_GEM,MMT_LUXE

scarcity.price.bottom.persuasion.app={"data":[{"id":"3863","text":"This is a Steal Deal!","hasAction":false,"style":{"textColor":"#007E7D","fontType":"B","fontSize":"BASE"}},{"id":"3863","text":"The price per room per night for this property is just ?2,000.","hasAction":false,"style":{"fontSize":"BASE"}}],"style":{"horizontalSpace":12,"verticalSpace":12,"borderGradient":{"angle":"50","start":"#43E1A8","end":"#219393"}},"placeholder":"PLACEHOLDER_PRICE_BOTTOM","template":"MULTI_PERSUASION_V","templateType":"DEFAULT"}
coupons.review.page.persuasion.apps={"data":[{"id":"60058d3faf73b51d43e4af93","persuasionType":"DISCOUNT","text":"<font color=\\"#757575\\"><b>{PERSUASION_TEXT}</b></font>","hasAction":false,"style":{"borderColor":"#757575","cornerRadii":8},"multiPersuasionPriority":0,"html":true}],"placeholder":"PLACEHOLDER_CARD_M4","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}

#Persuasion for MMT black info, myCash discount apps
mmt.black.mycash.persuasion.apps={"data":[{"id":"3863","persuasionType":"BLACK","iconurl":"https://promos.makemytrip.com/Growth/Images/B2C/mmtblack_listing_icon.png","hasAction":false,"style":{"textColor":"#4a4a4a","fontSize":"SMALL","borderColor":"#BF6C40","iconWidth":16,"iconHeight":16},"multiPersuasionPriority":1,"persuasionKey":"PERS_BLACK_GEN_MOB_REVAMP_1","isHtml":true}],"placeholder":"PLACEHOLDER_BOTTOM_BOX_M","template":"DEAL_BOX_IMAGE_TEXT","templateType":"DEFAULT"} 
mmt.black.revamp.persuasion.apps={"data":[{"id":"3863","persuasionType":"BLACK","iconurl":"https://promos.makemytrip.com/Growth/Images/B2C/mmtblack_listing_icon.png","hasAction":false,"style":{"styleClasses":["blkDiscountAplied"],"iconWidth":20,"iconHeight":20},"multiPersuasionPriority":1,"persuasionKey":"PERS_BLACK_GEN_MOB_REVAMP_1","isHtml":true}],"style":{"verticalSpace":8,"horizontalSpace":8},"placeholder":"PLACEHOLDER_PRICE_BOTTOM","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}

#Persuasion for HotelBenefit info, Hotel_Credit and TAJ Gift Cards
hotelbenefit.persuasion.apps={"data":[{"id":"3863","persuasionType":"Gift_Card","text":"Your<b>GiftCard</b>willbesentviaWhatsAppandEmailadaybeforecheck-in","extraData":{"iconUrl":null,"style":{"iconWidth":41,"iconHeight":37}}}],"style":{"horizontalSpace":12,"verticalSpace":12,"borderColor":"#d8d8d8","borderSize":"1","cornerRadii":"16","textColor":"#4a4a4a"},"placeholder":"PLACEHOLDER_PRICE_BOTTOM_M1","template":"IMAGE_TEXT_H"}
gcc.meta.wallet.cashback.persuasion.apps={"data":[{"id":"62bd9677feb541192bd483a1","iconurl":"https://promos.makemytrip.com/GCC/Cashback/Wallet_cashback_review.png","style":{"iconHeight":27,"iconWidth":139},"persuasionType":"FLYER_EXCLUSIVE_RATES_PERSUASION","hasAction":false,"html":false}],"placeholder":"PLACEHOLDER_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}
gcc.meta.wallet.cashback.persuasion.DT={"data":[{"id":"62bd9677feb541192bd483a1","iconurl":"https://promos.makemytrip.com/GCC/Cashback/Wallet_cashback_review.png","style":{"styleClasses":["pc__flyerExclusiveTag"]},"persuasionType":"FLYER_EXCLUSIVE_RATES_PERSUASION","hasAction":false,"html":false}],"placeholder":"PC_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}
gcc.meta.wallet.cashback.persuasion.pwa={"data":[{"id":"62bd9677feb541192bd483a1","iconurl":"https://promos.makemytrip.com/GCC/Cashback/Wallet_cashback_review.png","style":{"styleClasses":["pc__flyerExclusiveTag"]},"persuasionType":"FLYER_EXCLUSIVE_RATES_PERSUASION","hasAction":false,"html":false}],"placeholder":"PLACEHOLDER_TOP_RIGHT","template":"IMAGE_TEXT_H","templateType":"DEFAULT"}

mmt.select.icon = https://promos.makemytrip.com/GCC/Select_logo/xhdpi/select_logo.png
#Config for Supplier funded deals
early.bird.icon.url= https://promos.makemytrip.com/Growth/Images/B2C/supplier_ebd.png
last.minute.icon.url= https://promos.makemytrip.com/Growth/Images/B2C/supplier_lmd.png
supplier.deals.bg.color= #EEFFFA
#Flyer Detail page persuasion Configs
flyer.persuasion.color.detail=#F4FAFF
flyer.persuasion.image.url.detail=https://promos.makemytrip.com/Hotels_product/cross_sell/FlyerExclusive-detail.png
flyer.persuasion.image.url.detail.dt=https://promos.makemytrip.com/Hotels_product/DT-Flyer/fyd-details.png

vistara.persuasion.image.url.detail=https://promos.makemytrip.com/Hotels_product/cross_sell/vistara-taj/Vistaraexcl-details-mob1.png
vistara.persuasion.image.url.detail.dt=https://promos.makemytrip.com/Hotels_product/cross_sell/vistara-taj/Vistaraexcl-details-dt.png
vistara.persuasion.color.detail=#FFFAE8

value.stays.icon.new.detail.page=https://promos.makemytrip.com/Hotels_product/Value_Stays/v2/logo/ValueStays-3.png
luxe.icon.new.detail.page.dt=https://promos.makemytrip.com/Hotels_product/Luxe/thumbnail_Luxe_Hotel_tiny.png
bus.persuasion.color.detail=#F4FAFF
bus.persuasion.image.url.detail=https://promos.makemytrip.com/Hotels_product/cross_sell/ic_bus_crossell_3x.png
bus.persuasion.image.url.detail.dt=https://promos.makemytrip.com/Hotels_product/cross_sell/DT/bus_detail_DT_3x.png

train.persuasion.color.detail=#F4FAFF
train.persuasion.image.url.detail=https://promos.makemytrip.com/Hotels_product/cross_sell/ic_train_crossell_3x.png
train.persuasion.image.url.detail.dt=https://promos.makemytrip.com/Hotels_product/cross_sell/DT/rails_detail_DT_3x.png

#Flyer GCC Detail Page persuasion Configs
flyer.gcc.persuasion.image.url.detail=https://promos.makemytrip.com/GCC/flyer/flystaycard.png
#Exclusive Deal
exclusive.deal.persuasion.img.height=25
exclusive.deal.persuasion.img.width=126
#BNPL active booking threshold
bnpl.active.booking.threshold=3
bnpl.apwindow.limit=2

#TCS Applicable web view link for review page
#todo: need to add url here
tcs.info.review.url=https://promos.makemytrip.com/tcs/tcs-mmt/index.html
offer.details.url=http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/hotels/offerDetails

#TCS Applicable web view link for review page
tcs.info.review.url.app=https://promos.makemytrip.com/tcs/tcs-mmt/index.html
tcs.info.review.url.dt=https://promos.makemytrip.com/tcs/tcs-mmt/tcs-dweb.html
old.persuasion.class.to.new={"pc__stayType":"pc__stayTypePerNew","pc__hotelCategory":"pc__hotelCategoryPerNew","pc__inclusion":"pc__inclusionPerNew","valueStays":"valueStaysPerNew","pc_priceTag":"pc_priceTagPerNew","pc__location":"pc__locationPerNew","htlHighlt":"htlHighltPerNew","htlHighlt__title":"htlHighlt__titlePerNew","pc__cashbackDeal":"pc__cashbackDealPerNew","pc__blackDeal":"pc__blackDealPerNew","pc__blackDeal--item":"pc__blackDeal--itemPerNew","pc__peitho":"pc__peithoPerNew","htlHighlt__subTitle":"htlHighlt__subTitle__persNew","pc__flyerExclusiveTag":"pc__flyerExclusiveTagPerNew","pc__dealTimer":"pc__dealTimerPersNew","pc__mmtExclusive":"pc__mmtExclusivePerNew"}





#Kids pricing data
 free.kids.inclusion.icon.url=https://promos.makemytrip.com/Hotels_product/Inclusions/Icons/Default_DefaultDot.png
red.cross.Icon=https://promos.makemytrip.com/Hotels_product/Inclusions/Icons/Default_DefaultDot.png
green.tick.icon=https://promos.makemytrip.com/Hotels_product/Inclusions/Icons/Inclusions_greentick.png
single.tick.url=https://promos.makemytrip.com/Hotels_product/misc/tick.png
extra.guest.free.child.color=#007E7D
default.search.room.url=https://promos.makemytrip.com/Growth/Images/B2C/Upgrade_Stay_Logo.png

#High Demand Persuasion
high.demand.persuasion.color=#EC2127
high.demand.background.color=#FCDADB
high.demand.title.color=#4F4F4F

international.hotels.default.price.bucket.config= {"INR":[[0,2500],[2500,5500],[5500,8500],[8500,11500],[11500,14500],[14500,15000],[15000,30000]]}
international.hotels.city.wise.price.bucket.config={"priceBucket1":["CTKUALALU","CTKATHM","CTPOKHA","CTBAK00c5b339","CTPHNO","CTSIEMR","CTYAN58dd2969","CTILAL","CTPHUE","CTMUANGRAY","CTTASSL","CTKATANA","CTNHAT","CTMELAKA","CTMUANGCHI","CTLTec33d796","CTKLANG","CTSIKH","CTHATY","CTGOMBA","CTMINS","CTMUANGCHAN","CTMUANGNAKHON","CTKARAW","CTPANdd089a1b","CTMIRIS","CTBACOLOD","CTBLACKP","CTLIVINGSTONE","CTMUANGNONT","CTNICOS","CTHOAL","CTANAL","CTMUANGCHO","CTKUAN","CTMUANGKH","CTHULUL","CTSALVADO","CTKHLON","CTPAI1dc9aafe","CTSEREM","CTDONGG","CTMUANGUD","CTCOX","CTKINON","CTMUANGSUR","CTBEKA","CTTAKUAT","CTMITTA","CTKUALAS","CTDHAU","CTJINA","CTMUANGKA","CTPHUNP","CTKUAL","CTALORG","CTMUANGSAMU","CTOLONG","CTBTR","CTMOSHI","CTCHIANGK","CTAGAD","CTMUANGR","CTMUANGP","CTSIDO","CTMANDALA","CTTATR","CTSANTODOMING","CTMANZDO","CTKOTABH","CTKAR26d85214","CTYANbccb4d19","CTTARRAGO","CTHOAL","CTFS69b81462","CTPAKXE","CTCAGAY","CTNUA","CTMECHI","CTKULAI","CTSANGK","CTPURM","CTKOTASET","CTMAKAS","CTMAGE","CTSHANT","CTPERL","CTMUANGSO","CTMUANGNAK","CTLCY","CTCIRN","CTTSU02192c8b","CTCALLAO","CTRCH","CTNPA","CTBANLUN","CTNGQ","CTMUANGRO","CTKULON","CTHILIR","CTDELI","CTBATO","CTBANY","CTAVEIR"],"priceBucket2":["CTPATTA","CTBANGKO","CTBAL9d5bd6c3","CTPHUKE","CTMUANGKR","CTHOCHI","CTHANOI","CTHICH","CTPENAN","CTSHAR","CTJAKA","CTMAKATI","CTANCI","CTCHIANGMA","CTHIA8c110c08","CTPETAL","CTSOPA","CTYIW","CTMANIL","CTSEPAN","CTJOHO","CTLUMBI","CTCEB","CTNEGOM","CTPARba71bdfc","CTQUEZON","CTCHITW","CTPASAY","CTKOTAT","CTNARAYANI","CTGIZA","CTPAKKR","CTCHANT","CTSURAB","CTFUJAI","CTKOTAO","CTJEONJ","CTSYL","CTSHAHAL","CTKOTAME","CTSALAL","CTKOTAB","CTLIM86e13e10","CTSISAT","CTIPOH","CTCAMERONH","CTSAURA","CTKOSHI","CTKOTAY","CTBHER","CTKOSAMET","CTKOTASE","CTMIR93b383b1","CTHUc2ad3819","CTANGELE","CTMUANGU","CTKUCHIN","CTPEKA","CTRUDAK","CTASEE","CTGANDAK","CTNINHB","CTKEM521f614d","CTKEB","CTLOUA","CTSIRAC","CTPALEM","CTOVERB","CTPHRAN","CTGOLFE","CTFOZ","CTSANLUISP","CTTANGER","CTLAML","CTSANIL","CTVNHY","CTMANGGA","CTMUANGPHIT","CTAMBAG","CTPEDROD","CTBATANGAS","CTMOAL","CTSARAJ","CTSHYM","CTKOTAMAL","CTGRONI","CTRANGP","CTMAUS","CTTHAMAI","CTMUANGSAM","CTKHULN","CTSELUK","CTFRAS","CTTIMIS","CTMUANGK","CTMUANGB","CTPODGORI","CTPEL","CTDOTE","CTBENGKAL","CTMUANGTR","CTKUTNH","CTKOTAC","CTRAMBU","CTBNHS","CTBUTUA","CTGUAYAQ","CTMUANGLOP","CTMUANGTRA","CTSANDAK","CTDAMN","CTJEM","CTKELA","CTMAESA","CTMARUD","CTMINU","CTMUANGNAN","CTMUANGNAR","CTPADAN","CTSAGAM","CTSHIJI","CTMBOU","CTBANN","CTBANYU","CTCAMR","CTDONSO","CTLABUA","CTMUANGMU","CTMUANGPHAY","CTMUANGT","CTRAJS","CTSIHE","CTSINTR","CTVILAN"],"priceBucket3":["CTDUB","CTCOLO","CTABU","CTAUCKLA","CTSANSAL","CTLANGKA","CTKOSAM","CTGUANG","CTDHAea6f012d","CTPHIPH","CTMUSC","CTDOH","CTCITY","CTMEXICO","CTMAN02FE50CF","CTGENTIN","CTMECCA","CTLOMB","CTALMAT","CTTAIPE","CTROTO","CTYERE","CTKANDY","CTGILI","CTSHENZ","CTTHIM","CTHUAH","CTFOSH","CTKOPH","CTRASALK","CTVALLET","CTNUW","CTCANCUN","CTNAGARKO","CTPORb4020655","CTAMMAN","CTPASIG","CTTASHK","CTAJM","CTMUNIC","CTDEHIW","CTADDI","CTSTOLI","CTTAUP","CTSAP82597fab","CTCRETE","CTHARAR","CTMANNH","CTHIKK","CTRIODEJ","CTMAPUT","CTGRANAD","CTANKAY","CTJEJ5FDE0A7C","CTSANIL","CTTSELIN","CTARNAV","CTAKKY","CTPARAA","CTJANAK","CTKKOC","CTFIJ","CTCHAAM","CTLAPUL","CTDRES","CTMABAL","CTBEIRU","CTSIGIR","CTOSC","CTJERUSALEM","CTTAIC","CTBAGM","CTNAGOY","CTHANGZ","CTDAKA","CTUNAW","CTBUSA","CTSAPP","CTHALLES","CTKON99b00672","CTSANTAFDEB","CTALM0b115cd3","CTBORACA","CTELLA","CTZANZ","CTKENT","CTMOMBAS","CTKFG","CTANDRS","CTHOC","CTSAK7e79c9c4","CTLOI","CTFUKUO","CTBIKE","CTHEI","CTKOTABO","CTWUX","CTSOLL","CTHAUTESP","CTARUSH","CTWATTA","CTBRASLI","CTSUIT","CTALEXANDRI","CTKLAE","CTCHIBA","CTWINDHOEK","CTDAVAO","CTJUNGB","CTEHL","CTSETI","CTSIB0d2dab21","CTNELSO","CTGOYAN","CTMERS","CTGUARU","CTBANGLAM","CTKARIY","CTCARTAGE","CTPORTD","CTSUZH","CTARRAYY","CTCITYO","CTLARN","CTWESTERNU","CTINGOM","CTSKOPJ","CTMAVO","CTKOTABE","CTDIEM","CTYRE","CTBINTU","CTHAMBA","CTTRABZ","CTNINGB","CTBUXO","CTVNG","CTWASHO","CTSAMARQ","CTBRA5a106cd4","CTBIELSK","CTPANEVO","CTPHA5ca594b1","CTCHANGZ","CTNARa64cd577","CTBLANT","CTWAITO","CTGOTE","CTSELUKL","CTKKOKU","CTNEUW","CTATSU","CTBANJARM","CTLIGE","CTSKA128f13cf","CTOQD","CTBOHOL","CTDEPO","CTHANW","CTKUTAIS","CTLIMEI","CTMEJI","CTPUERTOGA","CTSURA","CTKBANG","CTCURITI","CTBOGO","CTCRDO","CTKAWASA","CTCAMPOG","CTSALAMA","CTTANGSH","CTDODOM","CTFUJ4a903b3b","CTKIANGG","CTPAJdd5b7ef6","CTBARIL","CTCENTRALGR","CTLASPI","CTAJET","CTWADIR","CTXIV","CTBARDEJ","CTDAED","CTKHLO","CTMANAD","CTKOTAPA","CTBELf6a0fc0f","CTCILE","CTGYUMR","CTKOG41733ab5","CTMEDW","CTMUANGM","CTPUNO","CTSAFR","CTSIGNA","CTTAUNG","CTTHIBNH","CTCHIANGS","CTAQT","CTBNT","CTBINH","CTGERMIS","CTHUc2ad3819","CTMTHO","CTMUANGNAKHO","CTMUANGSU","CTNINHB","CTPUTAT","CTRIMIN","CTSIEM","CTSRIJA"],"priceBucket4":["CTISTAN","CTTOKY","CTHONGK","CTLASVEG","CTMELB","CTMADfda474c6","CTSHANGH","CTSEOU","CTPRAG","CTNAIR","CTATHE","CTPHQU","CTFRANKFU","CTNEVS","CTANTAL","CTOSAK","CTCITYOF","CTRIYA","CTBENTH","CTSTOC","CTWELLIN","CTVALDO","CTMANCH","CTCHRIS","CTQUEENS","CTFRANKFU","CTKYOTO","CTHLON","CTDIVISIONNO6","CTTBILI","CTWARSZ","CTBIRMI","CTBRI","CTJEDD","CTKEMPT","CTORLAN","CTSEI","CTNARIT","CTSTU","CTSEV65f68f25","CTCASABL","CTGALLE","CTLUSAK","CTYVE","CTPER0737BE6D","CTHAMad840fbb"],"priceBucket5":["CTSINGAP","CTLONDO","CTMALDI","CTPARc0c94328","CTVIENNA","CTMILAN","CTSYD","CTROMA","CTMAURIT","CTBUDAP","CTMUNICH","CTBERLI","CTBRUSS","CTFLORENC","CTINTE","CTVENIC","CTLISBO","CTS04b044d7","CTCOPEN","CTCHICAG","CTGOLD","CTDUBLI","CTCATALU","CTHAUTSD","CTHELSIN","CTTELAVI","CTSEIN","CTHAMB","CTVALE","CTANTW","CTDSS","CTVALDE","CTLYODF58AB63","CTGLAS","CTNAPO","CTKLN","CTSANMAT","CTCAIR","CTBEIJ"],"priceBucket6":["CTZRIC","CTAMSTER","CTNEWY","CTBARCELON","CTTORON","CTLUCER","CTEDI","CTGENV","CTLOSANG","CTHAARLE","CTGRINDE","CTIBIZ","CTSAN48bac4e4","CTSALZ","CTNICbda9f265","CTSANTOR","CTPEEa9058e80","CTOSLO","CTMIAMIB","CTCOMMU","CTNIAGAR","CTVANCO","CTGREATERVA","CTMYKONO","CTROTTE","CTMEDCA57C76C","CTDISTRICT","CTSAN7385424f","CTBER7152467e","CTORANGE","CTINNS","CTHIGHLAN","CTBER00188457","CTMIDDLESE","CTMIAE97DF277","CTSANDIEGOCA"]}
international.hotels.price.bucket.config={"INR":{"priceBucket1":[8000,2000,2000],"priceBucket2":[9000,2000,3000],"priceBucket3":[13000,3000,4000],"priceBucket4":[15000,3000,6000],"priceBucket5":[20000,4000,8000],"priceBucket6":[25000,5000,10000]}}
#MyPartner min limit of similar hotels to show horizontally
min.limit.similar.hotels=3

# contextual filters to be included for my partner
mypartner.popular.contextualfilters.applicable = [{"filterGroup":"DEALS","filterValue":"EARLY_BIRD"},{"filterGroup":"DEALS","filterValue":"LAST_MINUTE"},{"filterGroup":"CTA_RATES","filterValue":"CTA_RATES_AVAIL"},{"filterGroup":"MMT_OFFERING","filterValue":"Daily Dhamaka"},{"filterGroup":"MMT_OFFERING","filterValue":"Package Offer"},{"filterGroup":"BEST_POI_DISTANCE","filterValue":"Beachfront"},{"filterGroup":"HOTEL_CATEGORY","filterValue":"Beachfront"},{"filterGroup":"MEAL_PLAN_AVAIL","filterValue":"ALL_MEAL_AVAIL"},{"filterGroup":"MEAL_PLAN_AVAIL","filterValue":"TWO_MEAL_AVAIL"},{"filterGroup":"FREE_CANCELLATION_AVAIL","filterValue":"CANCELLATION_AVAIL"},{"filterGroup":"FREE_BREAKFAST_AVAIL","filterValue":"BREAKFAST_AVAIL"},{"filterGroup":"HOTEL_CATEGORY","filterValue":"myp_cat_a"},{"filterGroup":"HOUSE_RULES","filterValue":"Unmarried Couples Allowed"},{"filterGroup":"HOTEL_CATEGORY","filterValue":"Guest Friendly"},{"filterGroup":"HOTEL_CATEGORY","filterValue":"Early Check-in Available"},{"filterGroup":"HOTEL_CATEGORY","filterValue":"Indian food at the property"},{"filterGroup":"HOTEL_CATEGORY","filterValue":"Water Villa"},{"filterGroup":"HOTEL_CATEGORY","filterValue":"Free Transfers"},{"filterGroup":"HOTEL_CATEGORY","filterValue":"All-Inclusive Meals and Drinks"},{"filterGroup":"HOTEL_CATEGORY","filterValue":"luxury_hotels"},{"filterGroup":"HOTEL_CATEGORY","filterValue":"Floating Breakfast"},{"filterGroup":"HOTEL_CATEGORY","filterValue":"Free Theme Park Tickets"},{"filterGroup":"HOTEL_CATEGORY","filterValue":"Near Transit Points"}]

threshold.for.hotstore.filter.count.in.mypartner.popular.filter.section=5

scarcity.review.background = #FFEDD1
bank.coupon.generic.icon = https://promos.makemytrip.com/Growth/Images/B2C/generic_offer_icon_dh.png
enable.meta.trafficV2 = FLIGHTSTY,BUSESTY,TRAINSTY,flipkart,Tafi,phonePe,CROSSSELL,mmtamexrewards,opaque,sbiyono,crit,gdn_rt,Emlhotels,Sem,seo,FLYWHEEL_MYPARTNER,COSMOS,mmtonecard,mmtbajajfinserv,mmtposhvine,mmtsbicard
getRate.traffic.sources=SKYSCANNER,TRIVAGO,WEGO_DOM,GCCMETA_WE_GO,TRIPADV
super.package.icon.url=https://promos.makemytrip.com/Hotels_product/package/SuperPackageHotelDetail.png
super.package.icon.url.secondary=https://promos.makemytrip.com/Hotels_product/package/SuperPackageHotelDetail.png
listing.card.engine.url=http://hotel-entity-service-listing.ecs.mmt/hotels-entity-listing/api/v2.0/getCard
bedroom.count.filter.config:{"2":[1,2],"3":[1,2,3],"4":[1,2,3],"5":[2,3,4],"6":[2,3,4],"7":[2,3,4],"8":[2,3,4],"9":[3,4,5]}
dot.icon.url=https://promos.makemytrip.com/Hotels_product/Inclusions/Icons/Default_DefaultDot.png
luxe.package.icon=https://promos.makemytrip.com/Growth/Images/B2C/luxe_boundary.png
explore.more.image=https://promos.makemytrip.com/Growth/Images/B2C/Explore_More_Image.png
black.icon.details.page.url=https://promos.makemytrip.com/Growth/Images/B2C/MMTBLACK_BLACK_LOGO_DETAILS.png
bg.gradient.black.popup={"GOLD":{"start":"#FAF2E4","end":"#FFFFFF","angle":"0"},"PLATINUM":{"start":"#E7E7E7","end":"#FFFFFF","angle":"0"},"FALLBACK_BLACK_GRADIENT":{"start":"#757575","end":"#BDBDBD","angle":"0"}}
detail.page.fallback.icon.black=https://promos.makemytrip.com/Growth/Images/B2C/MMTBlack_Old_Details_Logo.png
black.revamp.fallback.bullet.icon=https://promos.makemytrip.com/Growth/Images/B2C/Fallback_Black_Bullet.png
black.v1.tier.mapping={"GOLD":"PREFERRED","PLATINUM":"EXCLUSIVE"}

# Mapping for experiment value for Chat GPT review summary on detail page, jira - [HTL-46185]
chat.gpt.review.summary.exp.map={"0":[],"1":["EXT"],"2":["MMT"],"3":["EXT","MMT"]}
callBack.data.update.url=http://htlwebapi.ecs.mmt/hotels-entity/api/cache/callbackdata

rtb.dayTime.persuasion.icon.url=https://promos.makemytrip.com/altaccoimages/RTBDayNew2.png
rtb.nightTime.persuasion.icon.url=https://promos.makemytrip.com/altaccoimages/RTBNightNew2.png

mypartner.gst.assured.icon.url=https://promos.makemytrip.com/images/gstAssuredTag.png
filter.new.icon.url=https://promos.makemytrip.com/images/image-1.png

inclusion.list=Breakfast,Lunch Or Dinner,Lunch,Dinner,meals

request.callback.count=2

############## UGC GRPC CONFIGURATION ################
ugc.service.content.schema=ugcContent.txt
cg.netty.max.thread.pool.size=60
cg.netty.thread.pool.size=60

threadpool.corepool.size.reviewServiceThreadPool=50
threadpool.maxpool.size.reviewServiceThreadPool=100
threadpool.queue.capacity.reviewServiceThreadPool=10
request.callback.green.icon=https://promos.makemytrip.com/GCC/MiscIcons/Verified2x.png

################ DA GRPC CONFIGURATION ################
discounting.aggregator.grpc.host=hotels-discounting-aggregator.ecs.mmt
discounting.aggregator.grpc.port=8443
discounting.aggregator.intl.default.timeout=21000
discounting.aggregator.dom.default.timeout=11000
discounting.aggregator.ssl.certificate.required=true
max.grpc.resp.size=25000000

emi.detail.default.timeout=10000

#Config for 360 degree view images
view.360.icon.url=https://promos.makemytrip.com/Hotels_product/Listing/3603x.png
view.360.persuasionIcon.url=https://promos.makemytrip.com/Hotels_product/Contextual%20Filter/Icons/ezgif.com-gif-to-webp.webp

#LongStayBenefits static content
los.icon.url.room=https://promos.makemytrip.com/Hotels_product/GI/los_icon_2x.png
los.icon.url.avail=https://promos.makemytrip.com/Hotels_product/GI/los_icon_2x.png
los.position.select.room=0
los.position.avail.room=0

#PWA Monetization
sponsored.hotel.icon.url=https://promos.makemytrip.com/Hotels_product/Monetization/Sponsored.png
app.install.deeplink=https://app.mmyt.co/Xm2V/appinstallhoteldefault

food.rating.thresold=70

retry.enabled.urls=/USER/DETAILS
user.service.post.retries=1
business.Identification.Affiliates.list=380189
business.Identification.Segments.list=1152

#Config for Call to book
call.to.book.iconUrl=https://promos.makemytrip.com/GCC/MiscIcons/CallIcon.png
call.to.book.title=Call for Special Rates
call.to.book.option=callToBook

flyer.Hydra.SegmentId.list.mmt.intl=r2015,r2016,r2017,r2018,r2023
#Config for TravelTip
travel.tip.conn.req.timeout=15000
http.connection.timeout.travel.tip=1000
travel.tip.so.timeout=15000
http.connection.pool.size.travel.tip=50
travel.tip.url=http://hotels-locus-web.ecs.mmt/locations/web/v1/get/travel-tips
api.key.get.travelTips=HOTELS-ORCH-8152051219151838

long.stay.gcc.nudge.iconUrl=https://promos.makemytrip.com/GCC/MiscIcons/green_offer_icon.png

#Thank-You screen onlyTodayDeal icon Url
onlytodaydeal.icon.url=https://promos.makemytrip.com/images/CDN_upload/Coin_animation.gif

gcc.lpg.card.payload={"index":1,"subType":"GCCCARD","id":"LPG","titleText":"LOWEST_PRICE_GUARANTEE_TITLE","subText":"LOWEST_PRICE_GUARANTEE_SUBTITLE_REVIEW_THANKYOU","iconURL":"https://promos.makemytrip.com/gcc/Badge_MMTExclusive_DT.png","bgColor":"#FDF7E9","cardAction":[{"title":"LOWEST_PRICE_GUARANTEE_CARDACTION_TITLE","webViewUrl":"https://promos.makemytrip.com/gcc-mmt-exclusive-lpg-terms.html"}],"templateId":"exclusive_v2"}
locality.filter.pill.position=1
bathroom.stay.info.icon=https://promos.makemytrip.com/images/CDN_upload/ic_bathrooms.png
bedroom.stay.info.icon=https://promos.makemytrip.com/images/CDN_upload/ic_rooms.png
kitchen.stay.info.icon=https://promos.makemytrip.com/images/CDN_upload/ic_kitchen.png
livingroom.stay.info.icon=https://promos.makemytrip.com/images/CDN_upload/livingroomicon.png

onlytodaydeal.persuasion.type=ONLY_TODAY_DEAL
black.persuasion.type=BLACK
los.persuasion.type=LONGSTAY
discount.persuasion.type=DISCOUNT
super.package.persuasion.type=SUPER_PACKAGE

use.new.listing.service=true
orch.search.hotels.url=http://hotels-orchestrator.ecs.mmt/hotels-orchestrator/api/v1.0/searchHotels
orch.static.details.url=http://hotels-orchestrator-detail.ecs.mmt/hotels-orchestrator-detail/api/v1.0/staticDetails
orch.search.rooms.url=http://hotels-orchestrator-detail.ecs.mmt/hotels-orchestrator-detail/api/v1.0/searchRooms
orch.traveller.summary.url=http://hotels-orchestrator-detail.ecs.mmt/hotels-orchestrator-detail/api/v1.0/travellerSummary
orch.traveller.reviews.url=http://hotels-orchestrator-detail.ecs.mmt/hotels-orchestrator-detail/api/v1.0/travellerReviews
orch.update.price.url=http://hotels-orchestrator-detail.ecs.mmt/hotels-orchestrator-detail/api/v1.0/updatePrice
orch.host.calling.url=http://hotels-orchestrator-detail.ecs.mmt/hotels-orchestrator-detail/api/v1.0/hostCalling/initiate
orch.calendar.availability.url=http://hotels-orchestrator-detail.ecs.mmt/hotels-orchestrator-detail/api/v1.0/calendarAvailability
hostImpression.title.tag.url=https://promos.makemytrip.com/Hotels_product/Contextual%20Filter/Icons/ezgif.com-gif-to-webp.webp

new.icon.url=https://promos.makemytrip.com/Hotels_product/Contextual%20Filter/Icons/ezgif.com-gif-to-webp.webp
brand.icon.url=https://promos.makemytrip.com/Growth/Images/B2C/Treels_Details_Logo.png

#URLS FOR DEEPLINKS
hotel.level.app.deeplink=mmyt://htl/detail/?topHtlId={0}&hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&roomStayQualifier={5}&_uCurrency={6}&checkAvailability={7}
hotel.level.sharing.url=https://app.mmyt.co/Xm2V/hotelShareScreenshot?hotelId=

root.level.sharing.url=https://app.mmyt.co/Xm2V/hotelListingShare?checkin={0}&checkout={1}&city={2}&country={3}&roomStayQualifier={4}&checkAvailability={5}
root.level.deeplink.url=https://www.makemytrip.com/hotels/hotel-listing/?checkin={0}&checkout={1}&city={2}&country={3}&roomStayQualifier={4}&checkAvailability={5}&_uCurrency={6}
root.level.deeplink.url.global=https://www.makemytrip.global/hotels/hotel-listing/?checkin={0}&checkout={1}&city={2}&country={3}&roomStayQualifier={4}&checkAvailability={5}&_uCurrency={6}
root.level.deeplink.url.happay=https://happay.makemytrip.com/hotels/hotel-listing/?checkin={0}&checkout={1}&city={2}&country={3}&roomStayQualifier={4}&checkAvailability={5}&_uCurrency={6}


detail.deep.link.url=https://www.makemytrip.com/hotels/hotel-details?hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&openDetail=true&currency={5}&roomStayQualifier={6}&locusId={7}&locusType={8}
detail.deep.link.url.global=https://www.makemytrip.global/hotels/hotel-details?hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&openDetail=true&currency={5}&roomStayQualifier={6}&locusId={7}&locusType={8}

search.rooms.deep.link.url=https://www.makemytrip.com/hotels/hotel-select-room?hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&_uCurrency={5}&roomStayQualifier={6}&locusId={7}&locusType={8}
search.rooms.deep.link.url.global=https://www.makemytrip.global/hotels/hotel-select-room?hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&_uCurrency={5}&roomStayQualifier={6}&locusId={7}&locusType={8}

category.details.Map={"MySafety - Safe and Hygienic Stays":{"data":["HYGENIC_ROOMS_TEXT","TRAINED_STAFF_TEXT","SANITIZED_INDOORS","SAFE_DINING_TEXT"],"iconUrl":"https://promos.makemytrip.com/COVID/safe.png","itemIconType":"","title":"MYSAFETY_TITLE_MAP"}}
deeplink.listing.url = mmyt://htl/listing/?checkin={0}&checkout={1}&city={2}&country={3}&roomStayQualifier={4}&checkAvailability={5}
listing.app.deep.link.url=mmyt://htl/listing/?checkin={0}&checkout={1}&city={2}&country={3}&roomStayQualifier={4}&checkAvailability={5}

groupBooking.deeplink.url=https://www.makemytrip.com/hotels/group-booking/?checkin={0}&checkout={1}&city={2}&country={3}&locusId={4}&locusType={5}&roomStayQualifier={6}&_uCurrency={7}&appVersion={8}&deviceId={9}&bookingDevice={10}&deviceType={11}&visitorId={12}&visitNumber={13}&funnelSource={14}&idContext={15}
groupBooking.deeplink.url.myPartner=https://mypartner.makemytrip.com/hotels/group-booking/?checkin={0}&checkout={1}&city={2}&country={3}&locusId={4}&locusType={5}&roomStayQualifier={6}&_uCurrency={7}&appVersion={8}&deviceId={9}&bookingDevice={10}&deviceType={11}&visitorId={12}&visitNumber={13}&funnelSource={14}&idContext={15}
groupBooking.deeplink.url.global=https://www.makemytrip.global/hotels/group-booking/?checkin={0}&checkout={1}&city={2}&country={3}&locusId={4}&locusType={5}&roomStayQualifier={6}&_uCurrency={7}&appVersion={8}&deviceId={9}&bookingDevice={10}&deviceType={11}&visitorId={12}&visitNumber={13}&funnelSource={14}&idContext={15}

reposition.index.comparator=1
deep.link.url.comparator=https://www.makemytrip.com/hotels/hotel-details?hotelId={0}&city={1}&country={2}&roomStayQualifier={3}&checkin={4}&checkout={5}&openDetail=true&altAcco={6}
deep.link.url.comparator.global=https://www.makemytrip.global/hotels/hotel-details?hotelId={0}&city={1}&country={2}&roomStayQualifier={3}&checkin={4}&checkout={5}&openDetail=true&altAcco={6}
deep.link.url.comparator.myPartner=https://mypartner.makemytrip.com/hotels/hotel-details?hotelId={0}&city={1}&country={2}&roomStayQualifier={3}&checkin={4}&checkout={5}&openDetail=true&altAcco={6}

# properties for my partner user details API
mypartner-core.user-details.url=http://b2b-mypartner-core-orch.ecs.mmt/orch/v1/hotels/user-details?uuid=%s
my.partner.user.details.conn.req.timeout=5000
http.connection.timeout.my.partner.user.details=5000
my.partner.user.details.so.timeout=1000
http.connection.pool.size.my.partner.user.details=20

# properties for my partner save traveller gst details API
mypartner-core.save-traveller-gst.url=http://b2b-mypartner-core-orch.ecs.mmt/orch/v1/save-traveller-gst
save.gst.details.conn.req.timeout=5000
http.connection.timeout.save.gst.details=5000
save.gst.details.so.timeout=1000
http.connection.pool.size.save.gst.details=20
food.dining.merge.sections=Cook,Meals,Meal Details

ugc.fetch.reviews.connectionRequest.timeout=5000
ugc.fetch.reviews.connection.timeout=2000
ugc.fetch.reviews.socket.timeout=1000
ugc.fetch.reviews.connection.pool.size=30

#Securing Cookies from client side access
server.servlet.session.cookie.secure=true
server.servlet.session.cookie.http-only=true

orch.static.details.conn.req.timeout=3000
orch.static.details.http.conn.timeout=3000
orch.static.details.so.timeout=3000
orch.static.details.conn.pool.size=50

orch.review.summary.conn.req.timeout=3000
orch.review.summary.http.conn.timeout=3000
orch.review.summary.so.timeout=3000
orch.review.summary.conn.pool.size=50

#Stop getting expose the server information
server.header.server=false
pixel.tracking.locations = CTDUB
detail.page.persuasion.order = {"GCC":["EXCLUSIVE","BLACK","SUPER_PACKAGE","DISCOUNT"],"B2C":["ONLY_TODAY_DEAL","BLACK","LONGSTAY","SUPER_PACKAGE","DISCOUNT"]}

#price drop persuasion Icon
price.drop.icon.url=https://go-assets.ibcdn.com/u/MMT/images/1741866982350-price_trend_icon.png
server.port=8081

#business rating icon
business.rating.icon.url=https://promos.makemytrip.com/mybiz/acquired/briefcase.png

header.icon.success = https://go-assets.ibcdn.com/u/MMT/images/1741612394416-state_success.png;
header.icon.failed = https://go-assets.ibcdn.com/u/MMT/images/1741612347197-state_failed.png;
header.icon.pending = https://go-assets.ibcdn.com/u/MMT/images/1741612369696-state_pending.png;
inclusion.icon.red.cross = https://go-assets.ibcdn.com/u/MMT/images/1741612225106-red_cross.png;
inclusion.icon.red.info = https://go-assets.ibcdn.com/u/MMT/images/1741612259429-red_info.png;
inclusion.icon.double.tick.green = https://go-assets.ibcdn.com/u/MMT/images/1741612282401-double_tick.png;
inclusion.icon.single.tick.grey = https://go-assets.ibcdn.com/u/MMT/images/1741612308485-single_grey_tick.png;

#Food and dining icon URLS
food.dining.icon.veg=https://gos3.ibcdn.com/ic_veg-1744345054.png
food.dining.icon.non.veg= https://gos3.ibcdn.com/ic_non_veg-1744345177.png

payment.plan.icon.url=https://promos.makemytrip.com/Hotels_product/group/ic_rupee_2x.png
pixel.base.url = https://ad.doubleclick.net/ddm/activity/src=5481501;type=ota-s0;cat=ota-d007;qty=1;u11=India;u18=Makemytrip;dc_lat=0;tag_for_child_directed_treatment=0;tfua=0;npa=0;gdpr=0;gdpr_consent=0;

#Price Graph
price.graph.text.icon = https://promos.makemytrip.com/Hotels_product/myra.png
price.graph.icon = https://go-assets.ibcdn.com/u/MMT/images/1747651926342-myra_price.png
price.graph.recommended.icon = https://go-assets.ibcdn.com/u/MMT/images/1746552419922-listing_price_decrease.gif

#Multi Room Stay Combo icon URLS
multi.room.stay.saving.combo.icon = https://promos.makemytrip.com/images/Group12448335601.png
multi.room.stay.room.change.icon = https://promos.makemytrip.com/images/RoomChange-1.png
multi.room.stay.checkin.icon = https://promos.makemytrip.com/images/Frame21472233384x.png
multi.room.stay.checkout.icon = https://promos.makemytrip.com/images/Frame21472233434x.png
multi.room.stay.room.change.filled.icon = https://promos.makemytrip.com/images/Frame21472233424x.png

#Config for section names that should have indexed hotel names
#Add comma separated list in the value For ex. - RECOMMENDED_HOTELS, RECENTLY_VIEWED
show.index.section.list = MOST_BOOKED_HOTELS

#Loved by Indians icon url
loved.by.indians.icon.url=https://promos.makemytrip.com/images/CDN_upload/Flag.png
staycation.getaway.persuasion.icon = https://promos.makemytrip.com/Hotels_product/Hotel_SR/iOS/DealBG.png

bhf.persuasion.intervention.config= {"bhfHighToInterventionMap":{"Details_Top_BHF":"T","Details_RR_BHF":"T","Details_Blocker_BHF":"F"},"bhfFlowToInterventionMap":{"Details_Top_BHF":"F","Details_RR_BHF":"F","Details_Blocker_BHF":"F"}}
bhf.persuasion.style.config= {"Details_Top_BHF":{"bgColor":"#FEF2EE","textColor":"#A9112F"},"Details_Blocker_BHF":{"bgColor":"#FEF2EE","textColor":"#A9112F","additionalTextColor":"#4A4A4A"},"Details_RR_BHF":{"bgColor":"#FEF2EE","textColor":"#A9112F"}}

# Review Deeplink URL configurations
booking.review.deeplink.url=https://www.makemytrip.com/hotels/hotel-review?hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&_uCurrency={5}&roomStayQualifier={6}&locusId={7}&locusType={8}&hotelName={9}&countryName={10}&cityName={11}&isEntireProperty={12}&funnelName={13}&roomCriteria={14}&searchType={15}&mtKey={16}&isCorporate={17}&rsc={18}&suppDetail={19}&payMode={20}&forward={21}&mpn={22}
booking.review.deeplink.url.global=https://www.makemytrip.global/hotels/hotel-review?hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&_uCurrency={5}&roomStayQualifier={6}&locusId={7}&locusType={8}&hotelName={9}&countryName={10}&cityName={11}&isEntireProperty={12}&funnelName={13}&roomCriteria={14}&searchType={15}&mtKey={16}&isCorporate={17}&rsc={18}&suppDetail={19}&payMode={20}&forward={21}&mpn={22}
booking.review.deeplink.url.mypartner=https://mypartner.makemytrip.com/hotels/hotel-review?hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&_uCurrency={5}&roomStayQualifier={6}&locusId={7}&locusType={8}&hotelName={9}&countryName={10}&cityName={11}&isEntireProperty={12}&funnelName={13}&roomCriteria={14}&searchType={15}&mtKey={16}&isCorporate={17}&rsc={18}&suppDetail={19}&payMode={20}&forward={21}&mpn={22}
gift.card.logo.url=https://gos3.ibcdn.com/gift_card-**********.png
gift.card.thank.you.url=https://go-assets.ibcdn.com/u/MMT/images/*************-thankyou_mmt.gif
gift.card.tnc.url=https://promos.makemytrip.com/mmtblack-program-terms.html
gift.card.default.icon=https://i.ibb.co/Y7Cjdhr5/Gift-Square-With-Bow-Streamline-Ultimate-1-3x.png

#Special Request V2 Bell Icon URL
special.request.v2.bell.icon.url = https://go-assets.ibcdn.com/u/MMT/images/*************-SplReqIcon.png

#Special Request V2 Banner Background URL
bank.offer.default.icon=https://promos.makemytrip.com/Hotels_product/special_request.png
special.request.v2.banner.background.url = https://go-assets.ibcdn.com/u/MMT/images/*************-SplReqBanner.png

# Location Persuasion Icon URLs (HTL-64116)
ugc.location.icon.url=https://promos.makemytrip.com/altaccoimages/ugc/ugc_location_1.webp
ai.summary.icon.url=https://gos3.ibcdn.com/ai_image-**********.png
bank.offer.saved.card.icon=https://gos3.ibcdn.com/thumbs_up-**********.png

