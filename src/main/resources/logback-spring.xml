<?xml version="1.0" encoding="UTF-8"?>
<configuration>

	<appender name="clientGatewayLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>/opt/logs/tomcat/hotels-clientgateway.log</file>
		<append>true</append>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>/opt/logs/tomcat/hotels-clientgateway.log-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
			<!-- Set the maximum file size to control size-based rollover -->
			<maxFileSize>250MB</maxFileSize>
			<!-- Define the TotalSizeCap for a maximum of 2GB -->
			<totalSizeCap>1GB</totalSizeCap>
			<!-- Set maxHistory to 4 -->
			<maxHistory>2</maxHistory>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>%date [client:%X{clientIP}] [%thread] %-5level %logger{36} - %msg [unique_request_id:%X{correlation}]%n</pattern>
		</encoder>
		<encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
			<layout class="com.mmt.hotels.clientgateway.util.logging.PatternMaskingLayout">
<!--				<maskPattern>(\w+\.?\w+@\w+[\.-]?\w+\.\w+)</maskPattern>-->
<!--				<maskPattern>[\"\'](\d{10}|\d{12})[\"\']</maskPattern>-->
<!--				<maskPattern>([A-Z]{5}[0-9]{4}[A-Z]{1})</maskPattern>-->
<!--				<maskPattern>\"firstName\"\s*:\s*\"(.*?)\"</maskPattern>-->
<!--				<maskPattern>\"lastName\"\s*:\s*\"(.*?)\"</maskPattern>-->
<!--				<maskPattern>\"middleName\"\s*:\s*\"(.*?)\"</maskPattern>-->
				<pattern>%date [client:%X{clientIP}] [%thread] %-5level %logger{36} - %msg [unique_request_id:%X{correlation}]%n</pattern>
			</layout>
		</encoder>
	</appender>

	<appender name="AsyncClientGatewayLog" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="clientGatewayLog" />
		<queueSize>50000</queueSize> <!-- Set the queue size -->
		<discardingThreshold>0</discardingThreshold> <!-- Discard events when the queue less than the value specified -->
		<neverBlock>false</neverBlock> <!-- Set neverBlock to true -->
		<maxFlushTime>100</maxFlushTime>
	</appender>
	
	<springProfile name="dev">
		<logger name="com.mmt.hotels" level="debug" additivity="false">
			<appender-ref ref="AsyncClientGatewayLog" />
		</logger>
	</springProfile>

	<springProfile name="!dev">
		<logger name="com.mmt.hotels" level="warn" additivity="false">
			<appender-ref ref="AsyncClientGatewayLog" />
		</logger>
	</springProfile>

</configuration>
