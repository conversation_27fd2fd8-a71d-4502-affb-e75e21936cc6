package com.mmt.hotels.clientgateway.grpcconnector;

import com.mmt.hotels.clientgateway.constants.Constants;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.netty.GrpcSslContexts;
import io.grpc.netty.NettyChannelBuilder;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.net.ssl.SSLException;
import java.io.File;

@Component
public class PricingEngineConnector {

    @Value("${discounting.aggregator.grpc.host}")
    private String grpcHost;

    @Value("${discounting.aggregator.grpc.port}")
    private int grpcPort;

    @Value("${discounting.aggregator.ssl.certificate.required}")
    private boolean pricingEngineCertificateRequired;

    @Value("${max.grpc.resp.size}")
    private int maxGrpcRespSize;

    @Autowired
    @Qualifier("nettyChannelExecutorCG")
    private TaskExecutor taskExecutor;

    public ManagedChannel channel=null;
    public ManagedChannel channelDesktop=null;
    public ManagedChannel channelIos=null;
    public ManagedChannel channelAndroid=null;
    public ManagedChannel channelPwa=null;

    private static final Logger LOGGER = LoggerFactory.getLogger(PricingEngineConnector.class);

    @PostConstruct
    public void init() throws SSLException {
        LOGGER.debug("Establishing connection with PE server");
        if(pricingEngineCertificateRequired){
            LOGGER.debug("SSL certificate reqd");
            NettyChannelBuilder channelBuilder = NettyChannelBuilder.forAddress(grpcHost, grpcPort);
            channelBuilder.sslContext(GrpcSslContexts.forClient().trustManager(new File("/opt/manthan/san-aws-ecs-mmt.crt")).build());
            channelBuilder.maxInboundMessageSize(maxGrpcRespSize);
            channelBuilder.executor(taskExecutor);
            channelAndroid = channelBuilder.build();
            channelIos = channelBuilder.build();
            channelDesktop = channelBuilder.build();
            channelPwa = channelBuilder.build();
        }else{
            channel  = ManagedChannelBuilder
                    .forAddress(grpcHost,grpcPort).usePlaintext()
                    .build();
            // Assigning all the channels to a single channel, it is only being used while testing in local, QA, dev servers and not used in prod.
            channelDesktop = channel;
            channelIos = channel;
            channelAndroid = channel;
            channelPwa = channel;
        }

    }

    public ManagedChannel getChannel(String deviceType){
        if(StringUtils.isBlank(deviceType))
            return channelDesktop;
        switch (deviceType.toUpperCase()) {
            case Constants.DEVICE_OS_ANDROID:
                return channelAndroid;
            case Constants.DEVICE_OS_IOS:
                return channelIos;
            case Constants.BKG_DEVICE_PWA:
                return channelPwa;
            default:
                return channelDesktop;
        }
    }
}
