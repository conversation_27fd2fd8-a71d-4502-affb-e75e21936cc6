package com.mmt.hotels.clientgateway.operations;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.interfaces.ListingStrategy;
import com.mmt.hotels.clientgateway.request.ListingSearchRequestV2;
import com.mmt.hotels.clientgateway.response.listing.TreelsListingResponse;
import com.mmt.hotels.clientgateway.restexecutors.TreelsListingExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.transformer.request.TreelsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.TreelsResponseTransformer;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.response.searchwrapper.TreelsListingResponseBO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.ControllerConstants.LISTING_SEARCH_TREELS;

@Service(Constants.TREELS)
@Lazy
public class TreelsListingOperation implements ListingStrategy {
    @Autowired
    private CommonHelper commonHelper;

    @Autowired
    CommonResponseTransformer commonResponseTransformer;

    @Autowired
    private Utility utility;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
    private SearchHotelsFactory searchHotelsFactory;

    @Autowired
    private TreelsListingExecutor treelsListingExecutor;

    @Autowired
    private TreelsRequestTransformer treelsRequestTransformer;

    @Autowired
    private TreelsResponseTransformer treelsResponseTransformer;

    private static final Logger logger = LoggerFactory.getLogger(TreelsListingOperation.class);

    @Override
    public TreelsListingResponse doListingOperation(ListingSearchRequestV2 listingSearchRequest, Map<String, String[]> parameterMap, Map<String,String> httpHeaderMap) throws ClientGatewayException {
        try {
            long startTime = System.currentTimeMillis();
            if(listingSearchRequest.getSearchCriteria()==null){
                logger.warn("searchCriteria is null in request, setting default search context");
                commonHelper.setDefaultSearchContext(listingSearchRequest);
            }
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(listingSearchRequest.getSearchCriteria(), listingSearchRequest, httpHeaderMap);
            if (listingSearchRequest.getSearchCriteria() != null) {
                utility.setPaginatedToMDC(listingSearchRequest.getSearchCriteria());
                utility.setLoggingParametersToMDC(listingSearchRequest.getSearchCriteria().getRoomStayCandidates(), listingSearchRequest.getSearchCriteria().getCheckIn(),
                        listingSearchRequest.getSearchCriteria().getCheckOut());
            }
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_SEARCH_COMMON_REQUEST_PROCESS, LISTING_SEARCH_TREELS, System.currentTimeMillis() - startTime);
            SearchWrapperInputRequest searchWrapperInputRequest = treelsRequestTransformer.convertSearchRequest(listingSearchRequest, commonModifierResponse); // we may have to write different request convertor

            TreelsListingResponseBO treelsListingResponseBO = (TreelsListingResponseBO) treelsListingExecutor.search(searchWrapperInputRequest, parameterMap, httpHeaderMap);
            return treelsResponseTransformer.convertTreelsResponse(treelsListingResponseBO, listingSearchRequest, searchWrapperInputRequest);
        } catch (Throwable e) {
            logger.error("error occurred in searchTreels: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

}
