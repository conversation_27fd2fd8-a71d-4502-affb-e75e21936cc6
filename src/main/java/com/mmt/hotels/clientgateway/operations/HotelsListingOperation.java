//package com.mmt.hotels.clientgateway.operations;
//
//import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
//import com.mmt.hotels.clientgateway.interfaces.ListingStrategy;
//import com.mmt.hotels.clientgateway.request.ListingSearchRequestV2;
//import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
//import com.mmt.hotels.clientgateway.request.TreelsListingSearchRequest;
//import com.mmt.hotels.clientgateway.response.listing.TreelsListingResponse;
//import com.mmt.hotels.clientgateway.response.searchHotels.SearchHotelsResponse;
//import com.mmt.hotels.clientgateway.service.ListingService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.Map;
//
//@Component
//public class HotelsListingOperation implements ListingStrategy {
//
//    @Autowired
//    private ListingService listingService;
//    @Override
//    public SearchHotelsResponse doListingOperation(ListingSearchRequestV2 searchHotelsRequest, Map<String, String[]> parameterMap, Map<String,String> httpHeaderMap) throws ClientGatewayException {
//        return listingService.searchHotels(searchHotelsRequest, parameterMap, httpHeaderMap);
//    }
//
//}
