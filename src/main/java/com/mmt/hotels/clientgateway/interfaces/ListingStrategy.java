package com.mmt.hotels.clientgateway.interfaces;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.request.ListingSearchRequestV2;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.listing.ListingResponse;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public interface ListingStrategy {
    public ListingResponse doListingOperation(ListingSearchRequestV2 listingSearchRequestV2, Map<String, String[]> parameterMap, Map<String,String> httpHeaderMap) throws ClientGatewayException;
}
