package com.mmt.hotels.clientgateway.context;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.interfaces.ListingStrategy;
import com.mmt.hotels.clientgateway.operations.TreelsListingOperation;
import com.mmt.hotels.clientgateway.request.ListingSearchRequestV2;
import com.mmt.hotels.clientgateway.response.listing.ListingResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ListingContext {

    private final Map<String, ListingStrategy> listingOperationsMap;

    public ListingContext(Map<String, ListingStrategy> listingOperations) {
        this.listingOperationsMap = listingOperations;
    }

    public ListingResponse executeStrategy(String listingType, ListingSearchRequestV2 listingSearchRequest, Map<String, String[]> parameterMap, Map<String,String> httpHeaderMap) throws ClientGatewayException {
        return listingOperationsMap.get(listingType).doListingOperation(listingSearchRequest, parameterMap, httpHeaderMap);
    }
}
