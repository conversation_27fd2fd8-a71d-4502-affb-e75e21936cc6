package com.mmt.hotels.clientgateway.exception;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.json.simple.JSONObject;
import org.springframework.http.HttpStatus;

@Data
@EqualsAndHashCode(callSuper = true)
public class ClientGatewayException extends Exception{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private DependencyLayer dependencyLayer;
	
	private ErrorType errorType;
	
	private String code;
	
	private String message;

	private String alternateMessage;
	
	private HttpStatus httpStatusCode;

	//we are adding metaData so that some additional data can be sent to controller advice to send an specific error
	private JSONObject metaData;
	
	public ClientGatewayException() {
		
	}
	
	public ClientGatewayException(DependencyLayer dependencyLayer, ErrorType errorType, String code, String message) {
		super();
		this.dependencyLayer = dependencyLayer;
		this.errorType = errorType;
		this.code = code;
		this.message = message;
	}

	public ClientGatewayException(DependencyLayer dependencyLayer, ErrorType errorType, String code, String message, String alternateMessage) {
		super();
		this.dependencyLayer = dependencyLayer;
		this.errorType = errorType;
		this.code = code;
		this.message = message;
		this.alternateMessage = alternateMessage;
	}

	public ClientGatewayException(DependencyLayer dependencyLayer, ErrorType errorType, String code, String message,String alternateMessage, JSONObject metaData) {
		super();
		this.dependencyLayer = dependencyLayer;
		this.errorType = errorType;
		this.code = code;
		this.message = message;
		this.alternateMessage = alternateMessage;
		this.metaData = metaData;
	}
}