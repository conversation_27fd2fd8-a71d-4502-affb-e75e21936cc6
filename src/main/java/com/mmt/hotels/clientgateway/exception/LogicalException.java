package com.mmt.hotels.clientgateway.exception;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;

public class LogicalException extends ClientGatewayException {
    /**
     *
     */
    private static final long serialVersionUID = 1L;

    public LogicalException(DependencyLayer dependencyLayer, ErrorType errorType, String code, String message) {
        super(dependencyLayer, errorType, code, message);
    }
    public LogicalException(DependencyLayer dependencyLayer, ErrorType errorType, String code, String message, String title) {
        super(dependencyLayer, errorType, code, message, title);
    }
}
