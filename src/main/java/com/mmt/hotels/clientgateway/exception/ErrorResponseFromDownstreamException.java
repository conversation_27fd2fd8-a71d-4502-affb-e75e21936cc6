package com.mmt.hotels.clientgateway.exception;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import org.json.simple.JSONObject;

public class ErrorResponseFromDownstreamException extends ClientGatewayException{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private static final String NO_CONTENT_RESPONSE_CODE = "204";
	
	private static final String NO_CONTENT_RESPONSE_MESSAGE = "No content in response";
	
	public ErrorResponseFromDownstreamException(DependencyLayer dependencyLayer, ErrorType errorType) {
		super(dependencyLayer, errorType, NO_CONTENT_RESPONSE_CODE, NO_CONTENT_RESPONSE_MESSAGE);
	}
	public ErrorResponseFromDownstreamException(DependencyLayer dependencyLayer, ErrorType errorType, String code, String message) {
		super(dependencyLayer, errorType, code, message);
	}
	public ErrorResponseFromDownstreamException(DependencyLayer dependencyLayer, ErrorType errorType, String code, String message,String alternateMessage, JSONObject metaData) {
		//Adding new constructor so as to support metaData
		super(dependencyLayer, errorType, code, message,alternateMessage, metaData);
	}
	public ErrorResponseFromDownstreamException(DependencyLayer dependencyLayer, ErrorType errorType, String code, String message, String alternateMessage) {
		super(dependencyLayer, errorType, code, message, alternateMessage);
	}
}
