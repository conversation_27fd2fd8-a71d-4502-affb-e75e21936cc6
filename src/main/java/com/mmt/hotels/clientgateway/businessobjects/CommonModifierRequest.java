package com.mmt.hotels.clientgateway.businessobjects;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class CommonModifierRequest {
    private String mmtAuth;
    private String region;
    private String bookingDevice;
    private String correlationKey;
    private String pageContext;
    private String subPageContext;
    private String channel;
    private String corpAuthCode;
    private String uuid;
    private String cityCode;
    private String locationId;
    private String locationType;
    private String idContext;
    private String appVersion;
    private String mcid;
    private String deviceId;
    private String visitorId;
    private String expData;
    private String expVariantKeys;
    private Integer apWindow;
    private Integer los;
    private Integer adultCount;
    private Integer childCount;
    private Integer roomCount;
    private String currency;
    private String countryCode;
    private String trafficSource;
    private String checkInDate;
    private Map<String, String> manthanExpDataMap;
    private Map<String, String> contentExpDataMap;
    private List<String> validExpList;
    private String variantKeys;
    private String userIntent;
    private int rnCount;
    private Integer bedRoomCount;
    private String requester;
    private String trafficAudience;
}
