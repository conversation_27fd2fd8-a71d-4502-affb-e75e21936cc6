package com.mmt.hotels.clientgateway.businessobjects;

import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;

@Data
public class FilterPage {
    private String title;
    private String page_id;
    private LinkedHashMap<String, FilterConfigCategory> filters;
    private LinkedHashMap<String, LinkedHashMap<String, List<String>>> filtersToShow;
    private List<String> categoriesToShowWithOrder;
    private boolean searchable;
    private String newTagIcon;
}
