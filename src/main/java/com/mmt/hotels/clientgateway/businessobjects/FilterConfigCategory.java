package com.mmt.hotels.clientgateway.businessobjects;

import lombok.Data;

import java.util.LinkedHashMap;

@Data
public class FilterConfigCategory {
    private LinkedHashMap<String, LinkedHashMap<String, FilterConfigDetail>> groups;
    private String title;
    private Boolean showMore;
    private Integer minItemsToShow;
    private Boolean visible;
    private String viewType;
    private String viewTypeV2;
    private boolean hideCount;
    private Boolean showCustomRange;
    private String customRangeTitle;
    private LinkedHashMap<String, String> condition;
    private String iconUrl;
    private Boolean showImageUrl;
    private String description;
    private Boolean singleSelection;
    private Boolean collapsed;
    private Boolean sortingRequired;
    private boolean removeZeroCountFilters;
    private int countThreshold;
    private Integer maxFilterListSize;
}
