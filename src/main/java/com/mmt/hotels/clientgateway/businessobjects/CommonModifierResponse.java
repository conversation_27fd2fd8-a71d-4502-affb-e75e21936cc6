package com.mmt.hotels.clientgateway.businessobjects;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.mmt.hotels.clientgateway.request.FilterRules;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.UserLocation;

import lombok.Data;

@Data
public class CommonModifierResponse {
	private ExtendedUser extendedUser;
	private HydraResponse hydraResponse;
	private List<FilterRules> filterRules;
	private String cdfContextId;
	private boolean categoryRequired;
	private int numberOfCoupons;
	private String domain;
	private boolean cityTaxExclusive;
	private boolean greenHornNewHotelFlag;
	private String mmtAuth;
	private String userCountry;
	private String region;
	private String language;
	private String deviceId;
	private String mobile;
	private String affiliateId;
    private String mcId;
    private int applicationId;
    private String expData;
	private LinkedHashMap<String, String> expDataMap;
	private Map<String, String> manthanExpDataMap;
	//HTL-41137 This field is used for personalised discounting by manthan
	private Map<String, String> contentExpDataMap;
	private UserLocation userLocation;
	private boolean reviewPriceRequest; // This flag return true in case of reviewPriceRequest API Flow is enable
	private boolean homestayV2Flow;
	private boolean isLiteResponse;
	private String originalTrafficSource;
	private String trafficSource;
}
