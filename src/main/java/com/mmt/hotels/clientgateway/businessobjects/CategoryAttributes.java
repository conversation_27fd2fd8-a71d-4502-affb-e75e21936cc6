package com.mmt.hotels.clientgateway.businessobjects;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CategoryAttributes {
    private boolean collapsed;
    private boolean sortingRequired;
    private boolean removeZeroCountFilters;
    private int countThreshold;
    private Integer maxFilterListSize;
}
