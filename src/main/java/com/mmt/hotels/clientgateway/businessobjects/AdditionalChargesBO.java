package com.mmt.hotels.clientgateway.businessobjects;

import com.mmt.hotels.model.response.pricing.AdditionalFees;
import com.mmt.hotels.model.response.prime.DoubleBlackValidateResponse;

import java.util.List;

public class AdditionalChargesBO {
    private String userCurrency;
    private String hotelierCurrency;
    private String propertyType;
    private List<AdditionalFees> additionalFees;
    private double conversionFactor;
    private double bookingAmount;
    private String cityCode;
    private String roomName;
    private String cityName;
    private String countryCode;
    private boolean recommendationFlow;

    private AdditionalChargesBO() {

    }

    public static class Builder {
        private AdditionalChargesBO additionalChargesBO;

        public Builder() {
            additionalChargesBO = new AdditionalChargesBO();
        }

        public Builder buildUserCurrency(String userCurrency) {
            this.additionalChargesBO.userCurrency = userCurrency;
            return this;
        }

        public Builder buildHotelierCurrency(String hotelierCurrency) {
            this.additionalChargesBO.hotelierCurrency = hotelierCurrency;
            return this;
        }

        public Builder buildPropertyType(String propertyType) {
            this.additionalChargesBO.propertyType = propertyType;
            return this;
        }

        public Builder buildConversionFactor(double conversionFactor) {
            this.additionalChargesBO.conversionFactor = conversionFactor;
            return this;
        }

        public Builder buildAdditionalFees(List<AdditionalFees> additionalFees) {
            this.additionalChargesBO.additionalFees = additionalFees;
            return this;
        }
        public Builder buildBookingAmount(double bookingAmount) {
            this.additionalChargesBO.bookingAmount = bookingAmount;
            return this;
        }

        public Builder buildCityCode(String cityCode) {
            this.additionalChargesBO.cityCode = cityCode;
            return this;
        }

        public Builder buildRoomName(String roomName) {
            this.additionalChargesBO.roomName = roomName;
            return this;
        }

        public Builder buildCityName(String cityName) {
            this.additionalChargesBO.cityName = cityName;
            return this;
        }

        public Builder buildCountryCode(String countryCode) {
            this.additionalChargesBO.countryCode = countryCode;
            return this;
        }

        public Builder buildRecommendationFlow(boolean recommendationFlow) {
            this.additionalChargesBO.recommendationFlow = recommendationFlow;
            return this;
        }

        public AdditionalChargesBO build() {
            return this.additionalChargesBO;
        }
    }

    public String getUserCurrency() {
        return userCurrency;
    }

    public String getHotelierCurrency() {
        return hotelierCurrency;
    }

    public List<AdditionalFees> getAdditionalFees() {
        return additionalFees;
    }

    public String getPropertyType() {
        return propertyType;
    }

    public double getConversionFactor() {
        return conversionFactor;
    }

    public double getBookingAmount() {
        return bookingAmount;
    }

    public String getCityCode() {
        return cityCode;
    }

    public String getRoomName() {
        return roomName;
    }

    public String getCityName() {
        return cityName;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public boolean isRecommendationFlow() {
        return recommendationFlow;
    }
}
