package com.mmt.hotels.clientgateway.businessobjects;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.response.filter.InfoTag;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FilterConfigDetail {

    private String title;
    private String subTitle;
    private boolean preApplied;
    private String imageUrl;
    private String infoText;
    private List<String> iconList;
    private String description; //added to show message along with toggle filter HTL-38235
    private LinkedHashMap<String, Object> condition; //added to check whether filter is to be added or not like siteDomain, funnelSource.
    private InfoTag infoTag;
}
