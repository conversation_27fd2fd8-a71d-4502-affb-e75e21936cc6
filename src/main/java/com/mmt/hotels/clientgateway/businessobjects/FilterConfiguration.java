package com.mmt.hotels.clientgateway.businessobjects;

import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;


@Data
public class FilterConfiguration {
    private LinkedHashMap<String,Integer> rankOrder;
    private LinkedHashMap<String, FilterConfigCategory> filters;
    private LinkedHashMap<String, List<String>> conditions;
    private LinkedHashMap<String, LinkedHashMap<String, List<String>>> filtersToShow;
    private String homestayBannerIconUrl;
    private LinkedHashMap<String, CategoryAttributes> categoryAttributes;
}
