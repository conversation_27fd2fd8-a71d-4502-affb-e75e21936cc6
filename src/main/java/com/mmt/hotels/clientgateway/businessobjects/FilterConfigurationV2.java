package com.mmt.hotels.clientgateway.businessobjects;

import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;

@Data
public class FilterConfigurationV2 {
    private LinkedHashMap<String,Integer> rankOrder;
    private LinkedHashMap<String,LinkedHashMap<String,Integer>> rankOrderV2;
    private LinkedHashMap<String, FilterPage> filterPages;
    private LinkedHashMap<String, List<String>> conditions;
    private LinkedHashMap<String, FilterPage> pagesToShow;
    private String homestayBannerIconUrl;
    private LinkedHashMap<String, CategoryAttributes> categoryAttributes;
}
