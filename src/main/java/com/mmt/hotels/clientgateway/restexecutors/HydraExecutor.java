package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.thirdparty.request.HydraRequest;
import com.mmt.hotels.clientgateway.thirdparty.request.HydraUserSegmentRequest;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraLastBookedFlightResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraUserSegmentResponse;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

@Component
public class HydraExecutor {
	
	private static final Logger logger = LoggerFactory.getLogger(HydraExecutor.class);
	
	@Autowired
	ObjectMapperUtil objectMapperUtil;

	@Autowired
	private RestConnectorUtil restConnectorUtil;

	@Autowired
	private CommonConfigHelper commonConfigHelper;
	 
	@Value("${hydra.url}")
	private String hydraUrl;
	
	@Value("${hydra.user.segment.url}")
	private String hydraUserSegmentUrl;

	private static final String REQUESTOR_HEADER = "X-HYDRA-REQUESTOR";
	
	private static final String REQUEST_ID_HEADER = "X-HYDRA-REQUEST-ID";
	
	private static final String APIKEY = "apikey";
	
	private static final String HOTELS_HYDRA_REQUESTOR = "clientbackend";
	

	public HydraLastBookedFlightResponse getHydraLastBookedFlightResponse(HydraRequest hydraRequest, String correlationKey) throws ClientGatewayException {
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Accept", "application/json");
		headerMap.put("Content-Type", "application/json");
		headerMap.put(REQUEST_ID_HEADER, correlationKey);
		headerMap.put(REQUESTOR_HEADER, HOTELS_HYDRA_REQUESTOR);
		headerMap.put(APIKEY, commonConfigHelper.getHydraAPIKey());
		headerMap.put("X-Consumer-name", HOTELS_HYDRA_REQUESTOR);
		String request = objectMapperUtil.getJsonFromObject(hydraRequest, DependencyLayer.CLIENTGATEWAY);
		String result = restConnectorUtil.performLastBookedFlightResponsePost(request, headerMap, hydraUrl);
		logger.warn("Last flight booked request: {} Headers: {} Result: {}", request, headerMap, result);

		/* When there is no response from DPT, and we receive the following message:
		 * "The server was not able to produce a timely response to your request.
		 *   Please try again in a short while!"
		 * To resolve this issue, added a check in the code to verify if the response contains opening curly braces before parsing the JSON string
		 * */
		if (StringUtils.isNotEmpty(result) && result.startsWith(Constants.OPENING_CURLY_BRACE)) {
			HydraLastBookedFlightResponse hydraResponse = objectMapperUtil.getObjectFromJson(result, HydraLastBookedFlightResponse.class, DependencyLayer.HYDRA);
			if (hydraResponse != null && MapUtils.isNotEmpty(hydraResponse.getError())) {
				for (String key : hydraResponse.getError().keySet()) {
					throw new ErrorResponseFromDownstreamException(DependencyLayer.HYDRA, ErrorType.DOWNSTREAM, key, hydraResponse.getError().get(key));
				}
			}
			return hydraResponse;
		}
		return null;
	}


	public HydraUserSegmentResponse getHydraMatchedSegment(HydraUserSegmentRequest hydraUserSegmentRequest, String correlationKey, String entityName) throws ClientGatewayException {
		if (hydraUserSegmentRequest == null) {
			return null;
		}

		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Accept", "application/json");
		headerMap.put("Content-Type", "application/json");
		headerMap.put(REQUEST_ID_HEADER, correlationKey);
		headerMap.put(REQUESTOR_HEADER, HOTELS_HYDRA_REQUESTOR);
		headerMap.put(APIKEY, commonConfigHelper.getHydraAPIKey());
		if (StringUtils.isNotEmpty(entityName)) headerMap.put(ENTITY_NAME, entityName);
		headerMap.put("X-Consumer-name", HOTELS_HYDRA_REQUESTOR);
		String request = objectMapperUtil.getJsonFromObject(hydraUserSegmentRequest, DependencyLayer.CLIENTGATEWAY);
		String result = restConnectorUtil.performHydraMatchedSegment(request, headerMap, hydraUserSegmentUrl);
		logger.debug("Hydra segment request: {} Headers: {} Result: {}", request, headerMap, result);

		/* When there is no response from DPT, and we receive the following message:
		 * "The server was not able to produce a timely response to your request.
		 *   Please try again in a short while!"
		 * To resolve this issue, added a check in the code to verify if the response contains opening curly braces before parsing the JSON string
		 * */
		if (StringUtils.isNotEmpty(result) && result.startsWith(Constants.OPENING_CURLY_BRACE)) {
			HydraUserSegmentResponse hydraResponse = objectMapperUtil.getObjectFromJson(result, HydraUserSegmentResponse.class, DependencyLayer.HYDRA);
			if (hydraResponse != null && MapUtils.isNotEmpty(hydraResponse.getError()) && hydraResponse.getData() == null) {
				for (String key : hydraResponse.getError().keySet()) {
					throw new ErrorResponseFromDownstreamException(DependencyLayer.HYDRA, ErrorType.DOWNSTREAM, key, hydraResponse.getError().get(key));
				}
			}
			return hydraResponse;
		}
		return null;
	}
}
