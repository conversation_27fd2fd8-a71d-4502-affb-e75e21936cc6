package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.CityOverviewHesRequest;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.pojo.matchmaker.WikiResponse;
import com.mmt.hotels.pojo.response.HotelListingMapResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class ListingMapExecutor {

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Value("${listing.map.hes.url}")
    private String listingMapUrl;

    @Value("${search.hotels.url}")
    private String searchHotelUrl;

    @Value("${city.overview.url}")
    private String cityOverviewUrl;
    
    private static final Logger logger = LoggerFactory.getLogger(ListingMapExecutor.class);

    public HotelListingMapResponse listingMap(SearchWrapperInputRequest listingMapRequestBody, Map<String, String> headers) throws ClientGatewayException {
        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
        	headerMap.put("mmt-auth", headers.get("mmt-auth"));
        String request = objectMapperUtil.getJsonFromObject(listingMapRequestBody, DependencyLayer.CLIENTGATEWAY);
        logger.debug("For URL : {} MODIFIEDREQUEST: {}", getListingMapUrl(listingMapRequestBody.isListingMapShortstays()), request);
        String result = restConnectorUtil.performListingMapPost(request, headerMap, getListingMapUrl(listingMapRequestBody.isListingMapShortstays()));
        logger.debug("RESPONSE: {}",result);
        HotelListingMapResponse hotelListingMapResponse = objectMapperUtil.getObjectFromJson(result, HotelListingMapResponse.class, DependencyLayer.ORCHESTRATOR);
        if (hotelListingMapResponse != null && hotelListingMapResponse.getResponseErrors() != null &&
        		CollectionUtils.isNotEmpty(hotelListingMapResponse.getResponseErrors().getErrorList())) {
        	throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
        			hotelListingMapResponse.getResponseErrors().getErrorList().get(0).getErrorCode(), 
        			hotelListingMapResponse.getResponseErrors().getErrorList().get(0).getErrorMessage());
        }
        return hotelListingMapResponse;
    }

    /**
     *
     * @param listingMapShortstays
     * @return URL specific to our search
     *
     * For Shortstays /listing-map we hit /searchHotels in HES instead of /listingMap
     */
    private String getListingMapUrl(boolean listingMapShortstays) {
        if (listingMapShortstays) {
            return searchHotelUrl;
        }
        return listingMapUrl;
    }

    //This Method is Used to fetch Response from cityOverview api in case of Specific IH cities controlled by Drools
    public WikiResponse fetchCityOverview(CityOverviewHesRequest cityOverviewHesRequest, Map<String, String[]> parameterMap, Map<String, String> headers) throws JsonParseException, ErrorResponseFromDownstreamException, RestConnectorException {
        WikiResponse wikiResponse = null;
        long startTime = new Date().getTime();
        logger.debug("City Overview Hes Request Start time: {}", startTime);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));

        String request = objectMapperUtil.getJsonFromObject(cityOverviewHesRequest, DependencyLayer.CLIENTGATEWAY);
        //cityOverviewUrl = RestURLHelper.getDestinationUrl(cityOverviewUrl);
        String relativeUrl = Utility.getcompleteURL(cityOverviewUrl, parameterMap, cityOverviewHesRequest.getCorrelationKey());
        logger.debug("CityOverview Hes Request: {}",request);
        logger.debug("CityOverview Hes request Url: {}", relativeUrl);
        String response = restConnectorUtil.performCityOverviewSearch(request, headerMap, relativeUrl);
        wikiResponse = objectMapperUtil.getObjectFromJson(response, WikiResponse.class, DependencyLayer.ORCHESTRATOR);
        if (wikiResponse != null && wikiResponse.getResponseError() != null
                && CollectionUtils.isNotEmpty(wikiResponse.getResponseError().getErrorList())) {
            throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, wikiResponse.getResponseError().getErrorList().get(0).getErrorCode(),
                    wikiResponse.getResponseError().getErrorList().get(0).getErrorMessage());
        }
        logger.debug("City Overview Hes Request Start time: {}", new Date().getTime() - startTime);
        return wikiResponse;
    }
}