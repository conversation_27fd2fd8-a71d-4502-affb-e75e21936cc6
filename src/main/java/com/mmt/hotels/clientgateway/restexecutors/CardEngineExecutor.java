package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

@Component
public class CardEngineExecutor {

    private static final Logger logger = LoggerFactory.getLogger(CardEngineExecutor.class);

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Value("${listing.card.engine.url}")
    private String listingCardEngineUrl;

    public String getCard(SearchWrapperInputRequest searchWrapperInputRequest) throws ClientGatewayException {
        long start = System.currentTimeMillis();
        try{
            Map<String, String> headerMap = new HashMap<String, String>();
            headerMap.put("Content-Type", "application/json");
            headerMap.put("Accept-Encoding", "gzip");
            headerMap.put("srcRequest", "ClientGateway");
            String requestStr = objectMapperUtil.getJsonFromObject(searchWrapperInputRequest, DependencyLayer.CLIENTGATEWAY);
            logger.debug("card request {} ", requestStr);
            String relativeUrl = Utility.getcompleteURL(listingCardEngineUrl, new HashMap<>(), searchWrapperInputRequest.getCorrelationKey());
            String result = restConnectorUtil.performCardEnginePost(requestStr, headerMap, relativeUrl);
            logger.debug("card Response {} ", result);
            return result;
        }finally {
            long timeTaken = System.currentTimeMillis() - start;
            logger.info("Time taken by getCard Service {} millis", timeTaken);
        }
    }
}
