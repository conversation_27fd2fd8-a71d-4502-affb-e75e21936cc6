package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.GstDetailsHelper;
import com.mmt.hotels.clientgateway.request.gstDetails.TravellerGstDetails;
import com.mmt.hotels.clientgateway.response.gstDetails.GstDetailsResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.MyPartnerUserDetailsResponse;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class MyPartnerCoreRestExecutor {

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    private GstDetailsHelper gstDetailsHelper;

    @Value("${mypartner-core.user-details.url}")
    private String userDetailsUrl;

    @Value("${mypartner-core.save-traveller-gst.url}")
    private String saveTravellergstUrl;


    public GstDetailsResponse<TravellerGstDetails> saveGstDetails(TravellerGstDetails travellerGstDetails, Map<String, String[]> parameterMap, Map<String, String> headerMap, String client) throws ClientGatewayException {
        String url = saveTravellergstUrl;

        String response = restConnectorUtil.performSaveGstDetailsPost(url, objectMapperUtil.getJsonFromObject(travellerGstDetails, DependencyLayer.CLIENTGATEWAY), headerMap);
        log.debug("Save GST details response: {}", response);

        return objectMapperUtil.getObjectFromJsonWithType(response, new TypeReference<GstDetailsResponse<TravellerGstDetails>>() {}, DependencyLayer.CLIENTGATEWAY);
    }

    public GstDetailsResponse<List<TravellerGstDetails>> getGstDetails(String uuid, Map<String, String[]> parameterMap, Map<String, String> headerMap, String client) throws ClientGatewayException {
        String url = String.format(userDetailsUrl, uuid);

        String response = restConnectorUtil.performUserDetailsGet(url, headerMap);
        log.debug("Response from MyPartner core API: {}", response);

        MyPartnerUserDetailsResponse myPartnerUserDetailsResponse = objectMapperUtil.getObjectFromJson(response, MyPartnerUserDetailsResponse.class, DependencyLayer.CLIENTGATEWAY);

        GstDetailsResponse<List<TravellerGstDetails>> gstDetailsResponse;
        if (!gstDetailsHelper.isGstDetailsResponseValid(myPartnerUserDetailsResponse)) {
            log.error("Gst details not received form MyPartner core API for uuid: {}", uuid);
            gstDetailsResponse = GstDetailsResponse.<List<TravellerGstDetails>>builder()
                    .success(false)
                    .error("Gst details not received form MyPartner core API")
                    .build();
        } else {
            gstDetailsResponse = GstDetailsResponse.<List<TravellerGstDetails>>builder()
                    .success(myPartnerUserDetailsResponse.isSuccess())
                    .error(myPartnerUserDetailsResponse.getError())
                    .data(myPartnerUserDetailsResponse.getData().getTravellerGstInfo().getGstDetails())
                    .build();
        }

        return gstDetailsResponse;
    }
}
