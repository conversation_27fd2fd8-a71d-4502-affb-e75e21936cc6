package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.emi.UpdateEmiDetailRequest;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class EmiDetailExecutor {

	@Autowired
	private RestConnectorUtil restConnectorUtil;
	
	@Autowired
	private ObjectMapperUtil objectMapperUtil;
	
	@Value("${updated.emi.url}")
	private String getUpdatedEmiUrl;
	
	private static final Logger logger = LoggerFactory.getLogger(EmiDetailExecutor.class);
	
	public String getUpdatedEmiDetails(UpdateEmiDetailRequest updateEmiDetailRequest, Map<String, String[]> parameterMap, Map<String,String> headers, String correlationKey) throws ClientGatewayException {
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Content-Type", "application/json");
		headerMap.put("Accept-Encoding", "gzip");
		headerMap.put("srcRequest", "ClientGateway");
		headerMap.put("region", MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()));
		headerMap.put("currency", MDC.get(MDCHelper.MDCKeys.CURRENCY.getStringValue()));
		headerMap.put("language", MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue()));
		if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
			headerMap.put("mmt-auth", headers.get("mmt-auth"));

		String request = objectMapperUtil.getJsonFromObject(updateEmiDetailRequest, DependencyLayer.CLIENTGATEWAY);
		String relativeUrl = Utility.getcompleteURL(getUpdatedEmiUrl, parameterMap, correlationKey);
		logger.debug(request);
		logger.debug(relativeUrl);

		return restConnectorUtil.performEmiDetailPost(request, headerMap, relativeUrl);
	}

	public RoomDetailsResponse getUpdatedEmi(UpdateEmiDetailRequest updateEmiDetailRequest, Map<String, String[]> parameterMap, Map<String, String> headers, String correlationKey) throws ClientGatewayException {
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Content-Type", "application/json");
		headerMap.put("Accept-Encoding", "gzip");
		headerMap.put("srcRequest", "ClientGateway");
		headerMap.put("region", MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()));
		headerMap.put("currency", MDC.get(MDCHelper.MDCKeys.CURRENCY.getStringValue()));
		headerMap.put("language", MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue()));
		if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
			headerMap.put("mmt-auth", headers.get("mmt-auth"));

		String request = objectMapperUtil.getJsonFromObject(updateEmiDetailRequest, DependencyLayer.CLIENTGATEWAY);
		String relativeUrl = Utility.getcompleteURL(getUpdatedEmiUrl, parameterMap, correlationKey);
		logger.debug(request);
		logger.debug(relativeUrl);

		String result = restConnectorUtil.performEmiDetailPost(request, headerMap, relativeUrl);
		logger.debug(result);

		RoomDetailsResponse roomDetailsResponse = objectMapperUtil.getObjectFromJson(result, RoomDetailsResponse.class, DependencyLayer.ORCHESTRATOR);
		if (roomDetailsResponse != null && roomDetailsResponse.getResponseErrors() != null &&
				CollectionUtils.isNotEmpty(roomDetailsResponse.getResponseErrors().getErrorList())) {
			throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
					roomDetailsResponse.getResponseErrors().getErrorList().get(0).getErrorCode(),
					roomDetailsResponse.getResponseErrors().getErrorList().get(0).getErrorMessage());
		}
		return roomDetailsResponse;
	}
}
