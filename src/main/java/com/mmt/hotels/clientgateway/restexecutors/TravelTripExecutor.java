package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.TravelTipRequest;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class TravelTripExecutor {

    @Autowired
    private ObjectMapperUtil objectMapperUtil;
    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Value("${travel.tip.url}")
    private String travelTipUrl;

    @Value("${api.key.get.travelTips}")
    private String apiKeyTravelTips;

    private static final Logger LOGGER = LoggerFactory.getLogger(TravelTripExecutor.class);

    public String fetchTravelTips(TravelTipRequest travelTipRequest, Map<String, String[]> paramMap, Map<String, String> headers) throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        headerMap.put("api-key", apiKeyTravelTips);

        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));
        String request = objectMapperUtil.getJsonFromObject(travelTipRequest, DependencyLayer.CLIENTGATEWAY);
        String relativeUrl = Utility.getcompleteURL(travelTipUrl, paramMap, travelTipRequest.getCorrelationKey());
        LOGGER.debug(request);
        LOGGER.debug(relativeUrl);
       String result = restConnectorUtil.performTravelTipPost(request, headerMap, relativeUrl);
        LOGGER.debug(result);
        return result;
    }

}
