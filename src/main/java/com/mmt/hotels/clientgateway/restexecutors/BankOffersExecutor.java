package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.request.MobLandingRequest;
import com.mmt.hotels.clientgateway.response.moblanding.MobLandingResponse;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.CalendarAvailabilityResponse;
import com.mmt.hotels.pojo.request.landing.BankOffersRequest;
import com.mmt.hotels.pojo.response.bankoffers.BankOffersResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class BankOffersExecutor {

    private static final Logger logger = LoggerFactory.getLogger(BankOffersExecutor.class);

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    MetricAspect metricAspect;

    @Value("${bank.offers.url}")
    private String bankOffersUrl;


    public BankOffersResponse bankOffers(
            BankOffersRequest bankOffersRequest, 
            String correlationKey,
            Map<String, String[]> parameterMap,
            Map<String, String> headers
    ) throws ClientGatewayException {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth"))) {
            headerMap.put("mmt-auth", headers.get("mmt-auth"));
        }
        String request = objectMapperUtil.getJsonFromObject(bankOffersRequest, DependencyLayer.CLIENTGATEWAY);
        String relativeUrl = Utility.getcompleteURL(bankOffersUrl, parameterMap, correlationKey);
        logger.debug(request);
        logger.debug(relativeUrl);
        String result = restConnectorUtil.performGetBankOffersPost(relativeUrl, request, headerMap);
        logger.debug(result);
        return objectMapperUtil.getObjectFromJson(result, BankOffersResponse.class, DependencyLayer.ORCHESTRATOR);
    }

}
