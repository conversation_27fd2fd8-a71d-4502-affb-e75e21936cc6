package com.mmt.hotels.clientgateway.restexecutors;

import com.gommt.hotels.orchestrator.detail.model.request.DetailRequest;
import com.gommt.hotels.orchestrator.detail.model.response.*;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.TravellerReviewResponse;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.TravellerReviewSummary;
import com.gommt.hotels.orchestrator.detail.model.state.RequestDetails;
import com.mmt.hotels.clientgateway.constants.HeaderConstants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.EMPTY_STRING;

@Component
public class OrchDetailExecutor {

    private static final Logger logger = LoggerFactory.getLogger(OrchDetailExecutor.class);

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Autowired
    MetricAspect metricAspect;

    @Value("${orch.search.rooms.url}")
    private String orchSearchRooms;

    @Value("${orch.host.calling.url}")
    private String orchHostCalling;

    @Value("${orch.static.details.url}")
    private String orchStaticDetailsUrl;

    @Value("${orch.update.price.url}")
    private String orchUpdatePrice;

    @Value("${orch.traveller.summary.url}")
    private String orchTravellerSummaryUrl;

    @Value("${orch.traveller.reviews.url}")
    private String orchTravellerReviewsUrl;

    @Value("${orch.calendar.availability.url}")
    private String calendarAvailabilityUrl;

    public HotelDetailsResponse searchRooms(DetailRequest detailRequest, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
        long startTime = new Date().getTime();
        try {
            // Convert the listing request to JSON
            String requestJson = objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY);

            // Build the complete URL
            RequestDetails requestDetails = detailRequest.getClientDetails() != null ? detailRequest.getClientDetails().getRequestDetails() : new RequestDetails();
            String requestUrl = Utility.getCompleteUrl(orchSearchRooms, buildParameterMap(requestDetails, parameterMap));

            // Log the request details
            logger.debug("Search rooms request body: {} URL: {}", requestJson, requestUrl);

            // Perform the orchestrator search rooms post-request
            String responseJson = restConnectorUtil.performOrchestratorSearchRoomsPost(requestJson, buildHeaderMap(headers), requestUrl);

            // Log the response details
            logger.debug("Search rooms response: {}", responseJson);

            // Convert the response JSON to ListingResponse object
            HotelDetailsResponse detailResponse = objectMapperUtil.getObjectFromJson(responseJson, HotelDetailsResponse.class, DependencyLayer.ORCHESTRATOR_NEW);

            // Check for errors in the response
            if (detailResponse != null && detailResponse.getError() != null) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR_NEW, ErrorType.DOWNSTREAM,
                        detailResponse.getError().getCode(), detailResponse.getError().getMessage(), detailResponse.getError().getDescription());
            }
            return detailResponse;
        } finally {
            // Add the time taken to the metric aspect
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR_NEW.name(), "orchSearchRooms", new Date().getTime() - startTime);
        }
    }

    public HostCallingResponse hostCalling(DetailRequest detailRequest, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
        long startTime = new Date().getTime();
        try {
            // Convert the listing request to JSON
            String requestJson = objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY);

            // Build the complete URL
            RequestDetails requestDetails = detailRequest.getClientDetails() != null ? detailRequest.getClientDetails().getRequestDetails() : new RequestDetails();
            String requestUrl = Utility.getCompleteUrl(orchHostCalling, buildParameterMap(requestDetails, parameterMap));

            // Log the request details
            logger.debug("Host Calling request body: {} URL: {}", requestJson, requestUrl);

            // Perform the orchestrator search rooms post-request
            String responseJson = restConnectorUtil.performOrchestratorHostCallingPost(requestJson, buildHeaderMap(headers), requestUrl);

            logger.debug("Host Calling response: {}", responseJson);

            // Convert the response JSON to ListingResponse object
            HostCallingResponse detailResponse = objectMapperUtil.getObjectFromJson(responseJson, HostCallingResponse.class, DependencyLayer.ORCHESTRATOR_NEW);

            // Check for errors in the response
            if (detailResponse != null && detailResponse.getError() != null) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR_NEW, ErrorType.DOWNSTREAM,
                        detailResponse.getError().getCode(), detailResponse.getError().getMessage(), detailResponse.getError().getDescription());
            }
            return detailResponse;
        } finally {
            // Add the time taken to the metric aspect
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR_NEW.name(), "orchHostCalling", new Date().getTime() - startTime);
        }
    }

    public HotelStaticContentResponse staticDetails(DetailRequest detailRequest, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
        long startTime = new Date().getTime();
        try {
            // Convert the listing request to JSON
            String requestJson = objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY);

            // Build the complete URL
            RequestDetails requestDetails = detailRequest.getClientDetails() != null ? detailRequest.getClientDetails().getRequestDetails() : new RequestDetails();
            String requestUrl = Utility.getCompleteUrl(orchStaticDetailsUrl, buildParameterMap(requestDetails, parameterMap));

            // Log the request details
            logger.debug("Static details request body: {} URL: {}", requestJson, requestUrl);

            // Perform the orchestrator search hotels post-request
            String responseJson = restConnectorUtil.performOrchestratorStaticDetailsPost(requestJson, buildHeaderMap(headers), requestUrl);

            logger.debug("Static details response: {}", responseJson);

            // Convert the response JSON to ListingResponse object
            HotelStaticContentResponse hotelStaticContentResponse = objectMapperUtil.getObjectFromJson(responseJson, HotelStaticContentResponse.class, DependencyLayer.ORCHESTRATOR_DETAIL);

            // Check for errors in the response
            if (hotelStaticContentResponse != null && hotelStaticContentResponse.getError() != null) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR_NEW, ErrorType.DOWNSTREAM,
                        hotelStaticContentResponse.getError().getCode(),
                        hotelStaticContentResponse.getError().getMessage(),
                        hotelStaticContentResponse.getError().getDescription());
            }

            return hotelStaticContentResponse;
        } finally {
            // Add the time taken to the metric aspect
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR_NEW.name(), "orchStaticDetails", new Date().getTime() - startTime);
        }
    }

    public UpdatePriceResponse updatePrice(DetailRequest detailRequest, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
        long startTime = new Date().getTime();
        try {
            // Null pointer check for detailRequest
            if (detailRequest == null) {
                logger.error("DetailRequest is null in updatePrice method");
                throw new ClientGatewayException(DependencyLayer.CLIENTGATEWAY, ErrorType.VALIDATION, "INVALID_REQUEST", "DetailRequest cannot be null");
            }

            // Convert the request to JSON
            RequestDetails requestDetails = detailRequest.getClientDetails() != null ? detailRequest.getClientDetails().getRequestDetails() : new RequestDetails();
            String requestUrl = Utility.getCompleteUrl(orchUpdatePrice, buildParameterMap(requestDetails, parameterMap));

            // Log the request details
            String requestJson = objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY);
            logger.debug("Update price request body: {} URL: {}", requestJson, requestUrl);

            // Perform the orchestrator update price post-request
            String responseJson = restConnectorUtil.performOrchestratorSearchRoomsPost(requestJson, buildHeaderMap(headers), requestUrl);

            logger.debug("Update price response: {}", responseJson);

            // Null pointer check for response
            if (responseJson == null || responseJson.trim().isEmpty()) {
                logger.error("Received null or empty response from orchestrator updatePrice API");
                throw new ClientGatewayException(DependencyLayer.ORCHESTRATOR_NEW, ErrorType.DOWNSTREAM, "EMPTY_RESPONSE", "Received null or empty response from orchestrator updatePrice API");
            }

            // Convert the response JSON to PriceBreakDownResponse object
            UpdatePriceResponse updatePriceResponse = objectMapperUtil.getObjectFromJson(responseJson, UpdatePriceResponse.class, DependencyLayer.ORCHESTRATOR_NEW);

            // Null pointer check for parsed response
            if (updatePriceResponse == null) {
                logger.error("Failed to parse response from orchestrator updatePrice API");
                throw new ClientGatewayException(DependencyLayer.ORCHESTRATOR_NEW, ErrorType.DOWNSTREAM, "PARSE_ERROR", "Failed to parse response from orchestrator updatePrice API");
            }

            if (updatePriceResponse.getError() != null) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR_NEW, ErrorType.DOWNSTREAM,
                        updatePriceResponse.getError().getCode(),
                        updatePriceResponse.getError().getMessage(),
                        updatePriceResponse.getError().getDescription());
            }

            return updatePriceResponse;
        } finally {
            // Add the time taken to the metric aspect
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR_NEW.name(), "orchUpdatePrice", new Date().getTime() - startTime);

        }
    }

    public TravellerReviewSummary travellerReviewSummary(DetailRequest detailRequest, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
        long startTime = new Date().getTime();
        try {

            // Convert the request to JSON
            RequestDetails requestDetails = detailRequest.getClientDetails() != null ? detailRequest.getClientDetails().getRequestDetails() : new RequestDetails();
            String requestUrl = Utility.getCompleteUrl(orchTravellerSummaryUrl, buildParameterMap(requestDetails, parameterMap));

            // Log the request details
            String requestJson = objectMapperUtil.getObjectToJSONIncludeNonEmptyNonNull(detailRequest, DependencyLayer.CLIENTGATEWAY);
            logger.debug("Traveller summary request body: {} URL: {}", requestJson, requestUrl);

            // Perform the orchestrator update price post-request
            String responseJson = restConnectorUtil.performOrchestratorReviewSummaryPost(requestJson, buildHeaderMap(headers), requestUrl);

            logger.debug("Traveller summary response: {}", responseJson);

            // Null pointer check for response
            if (responseJson == null || responseJson.trim().isEmpty()) {
                logger.error("Received null or empty response from orchestrator travellerSummary API");
                throw new ClientGatewayException(DependencyLayer.ORCHESTRATOR_NEW, ErrorType.DOWNSTREAM, "EMPTY_RESPONSE", "Received null or empty response from orchestrator travellerSummary API");
            }

            // Convert the response JSON to PriceBreakDownResponse object
            TravellerReviewSummary travellerReviewSummary = objectMapperUtil.getObjectFromJson(responseJson, TravellerReviewSummary.class, DependencyLayer.ORCHESTRATOR_NEW);

            // Null pointer check for parsed response
            if (travellerReviewSummary == null) {
                logger.error("Failed to parse response from orchestrator travellerSummary API");
                throw new ClientGatewayException(DependencyLayer.ORCHESTRATOR_NEW, ErrorType.DOWNSTREAM, "PARSE_ERROR", "Failed to parse response from orchestrator travellerSummary API");
            }

            if (travellerReviewSummary.getError() != null) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR_NEW, ErrorType.DOWNSTREAM,
                        travellerReviewSummary.getError().getCode(),
                        travellerReviewSummary.getError().getMessage(),
                        travellerReviewSummary.getError().getDescription());
            }

            return travellerReviewSummary;
        } finally {
            // Add the time taken to the metric aspect
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR_NEW.name(), "travellerSummary", new Date().getTime() - startTime);

        }
    }

    public TravellerReviewResponse travellerReviews(DetailRequest detailRequest, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
        long startTime = new Date().getTime();
        try {

            // Convert the request to JSON
            RequestDetails requestDetails = detailRequest.getClientDetails() != null ? detailRequest.getClientDetails().getRequestDetails() : new RequestDetails();
            String requestUrl = Utility.getCompleteUrl(orchTravellerReviewsUrl, buildParameterMap(requestDetails, parameterMap));

            // Log the request details
            String requestJson = objectMapperUtil.getObjectToJSONIncludeNonEmptyNonNull(detailRequest, DependencyLayer.CLIENTGATEWAY);
            logger.debug("Traveller reviews request body: {} URL: {}", requestJson, requestUrl);

            // Perform the orchestrator update price post-request
            String responseJson = restConnectorUtil.performOrchestratorReviewSummaryPost(requestJson, buildHeaderMap(headers), requestUrl);

            logger.debug("Traveller reviews response: {}", responseJson);

            // Null pointer check for response
            if (responseJson == null || responseJson.trim().isEmpty()) {
                logger.error("Received null or empty response from orchestrator travellerReviews API");
                throw new ClientGatewayException(DependencyLayer.ORCHESTRATOR_NEW, ErrorType.DOWNSTREAM, "EMPTY_RESPONSE", "Received null or empty response from orchestrator travellerReviews API");
            }

            // Convert the response JSON to PriceBreakDownResponse object
            TravellerReviewResponse reviewResponse = objectMapperUtil.getObjectFromJson(responseJson, TravellerReviewResponse.class, DependencyLayer.ORCHESTRATOR_NEW);

            // Null pointer check for parsed response
            if (reviewResponse == null) {
                logger.error("Failed to parse response from orchestrator travellerReviews API");
                throw new ClientGatewayException(DependencyLayer.ORCHESTRATOR_NEW, ErrorType.DOWNSTREAM, "PARSE_ERROR", "Failed to parse response from orchestrator travellerReviews API");
            }

            if (reviewResponse.getError() != null) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR_NEW, ErrorType.DOWNSTREAM,
                        reviewResponse.getError().getCode(),
                        reviewResponse.getError().getMessage(),
                        reviewResponse.getError().getDescription());
            }

            return reviewResponse;
        } finally {
            // Add the time taken to the metric aspect
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR_NEW.name(), "travellerReviews", new Date().getTime() - startTime);

        }
    }

    private Map<String, String> buildHeaderMap(Map<String, String> headers) {
        //Add Headers
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        if (MapUtils.isNotEmpty(headers) && StringUtils.isNotEmpty(headers.get(HeaderConstants.HIDDEN_PARAMS))) {
            headerMap.put(HeaderConstants.HIDDEN_PARAMS, headers.get(HeaderConstants.HIDDEN_PARAMS));
        }
        return headerMap;
    }

    public ConsolidatedCalendarAvailabilityResponse getCalendarAvailability(DetailRequest calendarAvailabilityRequest, String correlationKey, Map<String, String[]> parameterMap,
                                                                Map<String, String> headers) throws ClientGatewayException {
        long startTime = new Date().getTime();
        try {
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("Accept-Encoding", "gzip");
            headerMap.put("Content-Type", "application/json");
            if (StringUtils.isNotEmpty(headers.get("mmt-auth"))) {
                headerMap.put("mmt-auth", headers.get("mmt-auth"));
            }
            String request = objectMapperUtil.getJsonFromObject(calendarAvailabilityRequest, DependencyLayer.CLIENTGATEWAY);
            //calendarAvailabilityUrl = RestURLHelper.getDestinationUrl(calendarAvailabilityUrl);
            String relativeUrl = Utility.getcompleteURL(calendarAvailabilityUrl, parameterMap, correlationKey);
            logger.debug(request);
            logger.debug(relativeUrl);
            String result = restConnectorUtil.performCalendarAvailabilityPost(request, headerMap, relativeUrl);
            ConsolidatedCalendarAvailabilityResponse response = objectMapperUtil.getObjectFromJson(result, ConsolidatedCalendarAvailabilityResponse.class, DependencyLayer.ORCHESTRATOR);
            if (response != null && CollectionUtils.isNotEmpty(response.getErrorList())) {
                throw new ErrorResponseFromDownstreamException(
                        DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
                        response.getErrorList().get(0).getCode(),
                        response.getErrorList().get(0).getMessage()
                );
            }
            return response;
        } finally {
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "calendarAvailability", new Date().getTime() - startTime);
        }
    }

    private Map<String, String[]> buildParameterMap(RequestDetails requestDetails, Map<String, String[]> parameterMap) {
        String requestId = requestDetails.getRequestId();
        String region = requestDetails.getSiteDomain().getName().toUpperCase();
        String funnelSource = requestDetails.getFunnelSource()!=null ? requestDetails.getFunnelSource().name() : EMPTY_STRING;
        String journeyId = requestDetails.getJourneyId() != null ? requestDetails.getJourneyId() : "";
        String country = requestDetails.getCountry() != null ? requestDetails.getCountry().getName() : null;
        String trafficSource = requestDetails.getTrafficType().getName() != null ? requestDetails.getTrafficType().name() : "B2C";

        Map<String, String[]> updatedParameterMap = parameterMap != null ? new HashMap<>(parameterMap) : new HashMap<>();
        updatedParameterMap.put(HeaderConstants.COUNTRY, new String[]{country});
        updatedParameterMap.put(HeaderConstants.REQUEST_ID, new String[]{requestId});
        updatedParameterMap.put(HeaderConstants.REGION, new String[]{region});
        updatedParameterMap.put(HeaderConstants.FUNNEL, new String[]{funnelSource});
        updatedParameterMap.put(HeaderConstants.TRAFFIC_SOURCE, new String[]{trafficSource});
        updatedParameterMap.put(HeaderConstants.JOURNEY_ID, new String[]{journeyId});
        return updatedParameterMap;
    }

}
