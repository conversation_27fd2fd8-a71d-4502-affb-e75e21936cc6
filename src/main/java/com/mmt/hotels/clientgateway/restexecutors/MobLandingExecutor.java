package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonParseException;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.PermissionErrors;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.thirdparty.request.PartnerAffiliateDetails;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.pojo.request.landing.HotelLandingMobRequestBody;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

@Component
public class MobLandingExecutor {

    @Value("${mypartner-core.user-details.url}")
    private String ctaAffIdUrl;
    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Value("${mob.landing.url}")
    private String mobLandingUrl;
    
    @Value("${place.lat.lng.url}")
    private String placeToLatLngURL;
    
    @Value("${lat.lng.city.url}")
    private String latLngToCityCodeURL;

    @Value("${list.personalization.card.url}")
    private String listPersonalizedCardURL;

    @Autowired
    PricingEngineHelper pricingEngineHelper;
    
    private static final Logger LOGGER = LoggerFactory.getLogger(MobLandingExecutor.class);

    public String moblanding(HotelLandingMobRequestBody hotelLandingMobRequestBody, Map<String, String[]> paramMap, Map<String, String> headers) throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
                headerMap.put("mmt-auth", headers.get("mmt-auth"));
        String request = objectMapperUtil.getJsonFromObject(hotelLandingMobRequestBody, DependencyLayer.CLIENTGATEWAY);
        //mobLandingUrl = RestURLHelper.getDestinationUrl(mobLandingUrl);
        String relativeUrl = Utility.getcompleteURL(mobLandingUrl, paramMap, hotelLandingMobRequestBody.getCorrelationKey());
        relativeUrl = pricingEngineHelper.appendFilterServiceExpInUrl(relativeUrl, hotelLandingMobRequestBody.getHotelSearchRequest() != null ? hotelLandingMobRequestBody.getHotelSearchRequest().getExperimentData() : "");
        LOGGER.debug(request);
        LOGGER.debug(relativeUrl);
        String result = restConnectorUtil.performMobLandingPost(request, headerMap, relativeUrl);
        LOGGER.debug(result);
        return result;
    }
    
    public String getLatLngFromGooglePlaceId(String placeId,Double lat, Double lng) throws ClientGatewayException{

    	Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");

		String apiURL = null;
		if (StringUtils.isNotEmpty(placeId)) {
			try {
                //placeToLatLngURL = RestURLHelper.getDestinationUrl(placeToLatLngURL);
				apiURL = String.format(placeToLatLngURL,URLEncoder.encode(placeId, "UTF-8"));
			} catch (UnsupportedEncodingException e) {
				LOGGER.error("Exception in getLatLngFromGooglePlaceId", e);
				throw new ClientGatewayException(DependencyLayer.CLIENTGATEWAY, ErrorType.UNEXPECTED, "", e.getMessage());
			}
			LOGGER.warn("request param queryString = {} getCityDataFromLatLngOrPlaceID ", placeId);
		} else {
            //latLngToCityCodeURL = RestURLHelper.getDestinationUrl(latLngToCityCodeURL);
			apiURL = String.format(latLngToCityCodeURL, lat, lng);
			LOGGER.warn("request param queryString = {},{} getCityDataFromLatLngOrPlaceID ", lat, lng);
		}
		
		return restConnectorUtil.getLatLngFromGooglePlaceId(headerMap, apiURL);
    }

    public String listPersonalizedCards(HotelLandingMobRequestBody listPersonalizationRequest,  Map<String, String[]> paramMap, Map<String, String> headers) throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));
        String request = objectMapperUtil.getJsonFromObject(listPersonalizationRequest, DependencyLayer.CLIENTGATEWAY);
        LOGGER.debug(request);
        //listPersonalizedCardURL = RestURLHelper.getDestinationUrl(listPersonalizedCardURL);
        String relativeUrl = Utility.getcompleteURL(listPersonalizedCardURL, paramMap, listPersonalizationRequest.getCorrelationKey());
        LOGGER.debug(relativeUrl);
        return restConnectorUtil.performMobLandingPost(request, headerMap, relativeUrl);
    }

    private final ObjectMapper objectMapper;
    public MobLandingExecutor() {
        objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
    public PartnerAffiliateDetails fetchCTAMoAffId(String uuid, String bookingType, String pageContext) throws ClientGatewayException {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept", "application/json");

        String completeUrl = String.format(ctaAffIdUrl, uuid, bookingType, pageContext);
        LOGGER.info("Fetching PartnerAffiliateDetails from URL: {}", completeUrl);

        try {
            String result = restConnectorUtil.performGetAtCorporate(headerMap, completeUrl);

            if (result == null || result.trim().isEmpty()) {
                LOGGER.warn("Empty response from Permissions API for UUID: {}", uuid);
                throw new ClientGatewayException(
                        DependencyLayer.ORCHESTRATOR,
                        ErrorType.DOWNSTREAM,
                        PermissionErrors.PERMISSION_API_EMPTY_RESPONSE.getErrorCode(),
                        PermissionErrors.PERMISSION_API_EMPTY_RESPONSE.getErrorMsg()
                );
            }

            LOGGER.info("Received response from Permissions API: {}", result);
            return objectMapper.readValue(result, PartnerAffiliateDetails.class);

        } catch (JsonProcessingException e) {
            LOGGER.error("Error parsing Permissions API response", e);
            throw new ClientGatewayException(
                    DependencyLayer.ORCHESTRATOR,
                    ErrorType.DOWNSTREAM,
                    PermissionErrors.PERMISSION_API_PARSE_FAILED.getErrorCode(),
                    PermissionErrors.PERMISSION_API_PARSE_FAILED.getErrorMsg()
            );

        } catch (Exception e) {
            LOGGER.error("Unexpected error while calling Permissions API", e);
            throw new ClientGatewayException(
                    DependencyLayer.ORCHESTRATOR,
                    ErrorType.DOWNSTREAM,
                    "GENERIC_PERMISSION_API_FAILURE",
                    "Unexpected failure in Permissions API call"
            );
        }
    }

}
