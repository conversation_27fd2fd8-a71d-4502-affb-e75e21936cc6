package com.mmt.hotels.clientgateway.restexecutors;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.UgcError;
import com.mmt.hotels.clientgateway.exception.LogicalException;
import com.mmt.hotels.clientgateway.response.ugc.*;
import com.mmt.hotels.clientgateway.request.ugc.*;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.util.RestConnector;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.http.HttpEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartRequest;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

@Component
public class UgcExecutor {

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Autowired
    private RestConnector restConnector;

    @Value("${program18.url}")
    private String program18url;

    @Value("${submit.answers.url}")
    private String submitanswersurl;

    @Value("${platforms.image.upload.url}")
    private String platformsImageUploadUrl;

    @Value("${get.booking.details.url}")
    private String getBookingDetailsUrl;

    private static final Logger LOGGER = LoggerFactory.getLogger(UgcExecutor.class);
    public UgcResponse fetchProgram18(ClientLoadProgramRequest request, Map<String, String[]> parameterMap, Map<String, String> httpHeaders) throws RestConnectorException, JsonProcessingException, LogicalException {
        String bookingId = Constants.EMPTY_STRING;
        String lob = Constants.DOM_HOTEL;
        String metaSrc = Constants.EMPTY_STRING;
        String deviceId = Constants.EMPTY_STRING;

        if (request.getUgc() == null || request.getUgc().getBookingId() == null || request.getUgc().getBookingId().isEmpty()) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
            LocalDate thirtyDaysAgo = LocalDate.now().plusDays(6);
            GetBookingDetailsRequest getBookingDetailsRequest = new GetBookingDetailsRequest();
            if(request.getUgcQr()!=null) {
                if (request.getUgcQr().getMetaSrc() != null) {
                    metaSrc = request.getUgcQr().getMetaSrc();
                }
                else{
                    metaSrc = Constants.QR_CODE;
                }
                getBookingDetailsRequest.setUuid(request.getUgcQr().getUuid());
                if(request.getUgcQr().getHotelId()!=null && !request.getUgcQr().getHotelId().isEmpty()) {
                    getBookingDetailsRequest.setHotelId(request.getUgcQr().getHotelId());
                }
            }
            getBookingDetailsRequest.setDateKey(formatter.format(thirtyDaysAgo.atStartOfDay()));
            getBookingDetailsRequest.setProfileType(Constants.PROFILE_TYPE);
            getBookingDetailsRequest.setLob(Constants.DOM_HOTEL);
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, String> headers= new HashMap<>();
            restConnector.setUgcRequestHeaders(headers, Constants.HEADER_CONTENT_APPLICATION_JSON);
            String jsonRequest = objectMapper.writeValueAsString(getBookingDetailsRequest);
            String url = getBookingDetailsUrl;
            String result = restConnectorUtil.getBookingDetails(jsonRequest, headers, url);
            LOGGER.debug("QR code corresponding Response: {}", result);
            if (result==null || result.trim().equalsIgnoreCase((NO_DATA_FOUND_FOR_UUID + getBookingDetailsRequest.getUuid()).trim())) {
                throw new LogicalException(DependencyLayer.PFM, ErrorType.PFM, UgcError.BOOKING_DETAILS_NOT_FOUND_FOR_ACCOUNT.getErrorCode(), null);
            }
            GetBookingDetailsResponse responseData = objectMapper.readValue(result, GetBookingDetailsResponse.class);

            if(responseData.getErrors()!=null) {
                String errorCode = (responseData.getErrors().getErrorList()!=null && !responseData.getErrors().getErrorList().isEmpty())
                        ? responseData.getErrors().getErrorList().get(0).getCode():null;
                if(errorCode != null) {
                    throw new LogicalException(DependencyLayer.PFM, ErrorType.PFM,errorCode,null);
                } else {
                    throw new LogicalException(DependencyLayer.PFM, ErrorType.PFM,UgcError.GENERIC_ERROR.getErrorCode(),null);
                }
            }
            bookingId = responseData.getBookingId();
            if (bookingId == null || bookingId.isEmpty()) {
                throw new LogicalException(
                        DependencyLayer.PFM, ErrorType.PFM, UgcError.BOOKING_DETAILS_NOT_FOUND_FOR_ACCOUNT.getErrorCode(),null);
            }
        } else {
            bookingId = request.getUgc().getBookingId();
            if (request.getUgc() != null) {
                if (request.getUgc().getLob() != null){
                    lob = request.getUgc().getLob();
                }
                if (request.getUgc().getMetaSrc() != null) {
                    metaSrc = request.getUgc().getMetaSrc();
                }
            }
        }
        String clientVersion = "";
        if(MapUtils.isNotEmpty(parameterMap) && parameterMap.containsKey("versioncode")) {
            String[] versionArray = parameterMap.get("versioncode");
            clientVersion = (versionArray != null && versionArray.length > 0) ? versionArray[0] : null;
        }
        if(request.getDeviceDetails()!=null && request.getDeviceDetails().getDeviceId()!=null){
            deviceId = request.getDeviceDetails().getDeviceId();
        }

        String url = String.format(program18url, bookingId, lob, metaSrc, clientVersion, deviceId);
        Map<String, String> headers= new HashMap<>();
        headers.put(Constants.MMT_AUTH, httpHeaders.get(Constants.AUTH));
        if(httpHeaders.get(ENTITY_NAME) != null){
            headers.put(ENTITY_NAME, httpHeaders.get(ENTITY_NAME));
        }
        restConnector.setUgcRequestHeaders(headers, Constants.HEADER_CONTENT_APPLICATION_JSON);
        String result = restConnectorUtil.performProgram18Get(headers,url);
        LOGGER.debug("Program18 get request url: {}", url);
        LOGGER.debug("Program18 Response: {}", result);
        ObjectMapper mapper = new ObjectMapper();
        QuestionData responseData = mapper.readValue(result, QuestionData.class);
        if (responseData == null) {
            throw new LogicalException(DependencyLayer.PFM, ErrorType.PFM, UgcError.NO_PROGRAM_FOUND.getErrorCode(),null);
        }
        if(responseData.getErrors()!=null) {
            String errorCode = (responseData.getErrors().getErrorList()!=null && !responseData.getErrors().getErrorList().isEmpty())
                    ? responseData.getErrors().getErrorList().get(0).getCode():null;
            if(errorCode != null) {
                throw new LogicalException(DependencyLayer.PFM, ErrorType.PFM,errorCode,null);
            } else {
                throw new LogicalException(DependencyLayer.PFM, ErrorType.PFM,UgcError.GENERIC_ERROR.getErrorCode(),null);
            }
        }
        if (responseData.getError() != null && responseData.getError().getCode() != null && responseData.getError().getStatus() != null) {
            throw new LogicalException(DependencyLayer.PFM, ErrorType.PFM, UgcError.NO_PROGRAM_FOUND.getErrorCode(),null);
        }
        UgcResponse ugcResponse = new UgcResponse();
        ugcResponse.setQuestionData(responseData);
        return ugcResponse;
    }

    public UgcResponse submitAnswersToPlatforms(ClientSubmitApiRequest request, List<ImageUploadResult> imageUploadResults, Map<String, String[]> parameterMap) throws RestConnectorException, JsonProcessingException {
        String clientVersion = "";
        if(MapUtils.isNotEmpty(parameterMap) && parameterMap.containsKey("versioncode")) {
            String[] versionArray = parameterMap.get("versioncode");
            clientVersion = (versionArray != null && versionArray.length > 0) ? versionArray[0] : null;
        }
        String url = String.format(submitanswersurl,clientVersion);
        Map<String, String> headers= new HashMap<>();
        ObjectMapper mapper = new ObjectMapper();
        String submitAnswersRequestString = createSubmitAnswersRequest(request, imageUploadResults);
        restConnector.setUgcRequestHeaders(headers, Constants.HEADER_CONTENT_APPLICATION_JSON);
        String result = restConnectorUtil.submitAnswersPlatforms(submitAnswersRequestString,headers,url);
        UgcResponse responseData = mapper.readValue(result, UgcResponse.class);
        return responseData;
    }

    public List<ImageUploadResult> uploadImagesToPlatformsS3(MultipartRequest multipartRequest) throws RestConnectorException, IOException {
        String url = platformsImageUploadUrl;
        Map<String, String> headers = new HashMap<>();
        restConnector.setUgcRequestHeaders(headers, Constants.HEADER_CONTENT_MULTIPART_FORM_DATA);
        List<ImageUploadResult> imageUploadResults= new ArrayList<>();
        String result = "";
        Map<String, MultipartFile> multipartFileList = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> file : multipartFileList.entrySet()) {
            if (file.getValue().getContentType().toString().equalsIgnoreCase(Constants.HEADER_CONTENT_APPLICATION_JSON) || file.getValue().getContentType().toString().equalsIgnoreCase(Constants.HEADER_CONTENT_APPLICATION_JSON_UTF_8))
            {
                continue;
            }
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.addBinaryBody("file", file.getValue().getInputStream(), ContentType.MULTIPART_FORM_DATA, file.getValue().getOriginalFilename());
            HttpEntity multipart = builder.build();
            result = restConnectorUtil.uploadImagesToPlatform(multipart,headers,url);
            ObjectMapper mapper = new ObjectMapper();
            ImageUploadResult responseData = mapper.readValue(result, ImageUploadResult.class);
            imageUploadResults.add(responseData);
        }
        return imageUploadResults;
    }

    public String createSubmitAnswersRequest(ClientSubmitApiRequest request, List<ImageUploadResult> imageUploadResults) {
        try {
            SubmitRequest submitRequest = new SubmitRequest();
            String questionId = request.getQuestion().getQuestionId();

            submitRequest.setUgcId(request.getUgcId());
            submitRequest.setProgramId(request.getProgramId());
            submitRequest.setContentId(request.getHotelId());
            if(request.getUserPage()!=null) {
                submitRequest.setUserPage(request.getUserPage());
            }
            String questionType = "";
            boolean voiceInput = false;

            if(request.getMetaSrc()!=null) {
                submitRequest.setMetaSrc(request.getMetaSrc());
            }
            if(request.getQuestion()!=null && request.getQuestion().getQuestionType()!=null) {
                questionType = request.getQuestion().getQuestionType();
            }
            if(request.getQuestion()!=null && request.getQuestion().isVoiceInput()) {
                voiceInput = request.getQuestion().isVoiceInput();
            }

            Page page = new Page();
            List<Page> pages = new ArrayList<>();
            QuestionDataClient question = new QuestionDataClient();
            List<QuestionDataClient> questions = new ArrayList<>();
            question.setVoiceInput(voiceInput);

            switch (questionType) {
                case "IMAGE":
                    List<MediaDetails> mediaDetails = new ArrayList<>();
                    List<MediaDetails> alreadyUploadedMediaDetails = request.getQuestion().getMediaDetails();
                    if(alreadyUploadedMediaDetails!=null && !alreadyUploadedMediaDetails.isEmpty())
                    {
                        mediaDetails.addAll(alreadyUploadedMediaDetails);
                    }
                    for (ImageUploadResult imageUploadResult : imageUploadResults) {
                        MediaDetails mediaDetail = new MediaDetails();
                        mediaDetail.setMediaId(imageUploadResult.getFileList().get(0).getId());
                        mediaDetail.setMediaType(imageUploadResult.getFileList().get(0).getMediaType());
                        mediaDetail.setMediaUrl(imageUploadResult.getFileList().get(0).getUrl());
                        mediaDetails.add(mediaDetail);
                    }
                    question.setMediaDetails(mediaDetails);
                    break;
                case "RATING":
                    question.setRatingValue(request.getQuestion().getRating());
                    break;
                case "TEXT_AREA":
                    List<AdditionalDetail> additionalDetails = new ArrayList<>();
                    AdditionalDetail additionalDetail = new AdditionalDetail();
                    TextDetails contentText = new TextDetails();
                    TextDetails titleText = new TextDetails();
                    contentText.setText(request.getQuestion().getText());
                    titleText.setText(request.getQuestion().getTitle());
                    additionalDetail.setType(TITLE_TEXT);
                    additionalDetail.setTextDetails(titleText);
                    additionalDetails.add(additionalDetail);
                    question.setAdditionalDetails(additionalDetails);
                    question.setTextDetails(contentText);
                    break;
                    // handles all multichoice type of questions, including single select and multi select, emote
                default:
                    List<String> submittedAnswers = request.getQuestion().getSelected();
                    List<String> answers = new ArrayList<>();
                    for (String answer : submittedAnswers) {
                        answers.add(answer);
                    }
                    question.setSelectedOptionIds(answers);
            }
            if(request!=null && request.getQuestion()!=null && request.getQuestion().getIndexId()!=null) {
                question.setIndexId(request.getQuestion().getIndexId());
            }
            question.setQuestionId(questionId);
            page.setPageId(request.getPageId());
            questions.add(question);
            page.setQuestions(questions);
            pages.add(page);
            submitRequest.setPages(pages);
            if(request!=null && request.getUserPage()!=null) {
                submitRequest.setUserPage(request.getUserPage());
            }
            ObjectMapper mapper = new ObjectMapper();

            // Convert the object to JSON string
            String jsonString = mapper.writeValueAsString(submitRequest);
            return jsonString;
        } catch (Exception e) {
            LOGGER.error("Error in createSubmitAnswersRequest", e);
            return "";
        }
    }
}
