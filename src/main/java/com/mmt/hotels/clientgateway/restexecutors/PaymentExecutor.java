
package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.SpecifiedErrorsRequestor;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.util.MaskingUtil;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.request.payment.BeginCheckoutReqBody;
import com.mmt.hotels.model.request.payment.OfferDetailsRequest;
import com.mmt.hotels.model.response.errors.Error;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import com.mmt.hotels.model.response.payment.offerdetails.OfferDetailsResponse;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

@Component
public class PaymentExecutor {

    @Autowired
    ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Value("${payment.checkout.url}")
    private String paymentUrl;

    @Value("${offer.details.url}")
    private String offerDetailsUrl;

    @Autowired
    MetricAspect metricAspect;

    private static final Logger logger = LoggerFactory.getLogger(PaymentExecutor.class);

    public PaymentCheckoutResponse beginPaymentCheckout(BeginCheckoutReqBody paymentRequest, Map<String, String> httpHeaderMap) throws JsonParseException, ErrorResponseFromDownstreamException, RestConnectorException, ClientGatewayException {
        long startTime = new Date().getTime();
        try {
            httpHeaderMap.put("srcRequest", "ClientGateway");
            String request = objectMapperUtil.getJsonFromObject(paymentRequest, DependencyLayer.CLIENTGATEWAY);
            logger.warn("For paymentCheckout URL={} MODIFIEDREQUEST={} ", paymentUrl, MaskingUtil.maskSensitiveDataAndLog(request));
            String result = restConnectorUtil.performPaymentCheckoutPost(request, httpHeaderMap, paymentUrl);
            logger.warn("For paymentCheckout RESPONSE={} ", result);
            PaymentCheckoutResponse cgServerResponse = objectMapperUtil.getObjectFromJson(result, PaymentCheckoutResponse.class, DependencyLayer.ORCHESTRATOR);

            boolean isResponseValid = cgServerResponse != null && cgServerResponse.getResponseErrors() != null;
            boolean hasErrors = isResponseValid && CollectionUtils.isNotEmpty(cgServerResponse.getResponseErrors().getErrorList());
            boolean isCorpIdContext = Constants.CORP_ID_CONTEXT.equalsIgnoreCase(paymentRequest.getIdContext());
            boolean isSpecifiedError = isCorpIdContext && hasErrors && SpecifiedErrorsRequestor.resolve(cgServerResponse.getResponseErrors().getErrorList().get(0).getErrorCode()) != null;

            if (hasErrors && !isSpecifiedError) {
                Error firstError = cgServerResponse.getResponseErrors().getErrorList().get(0);
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, firstError.getErrorCode(), firstError.getErrorMessage());
            }
            return cgServerResponse;
        } finally {
            // Add the time taken to the metric aspect
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "hotels-entity/api/v2.0/hotels/paymentCheckout", new Date().getTime() - startTime);
        }
    }

    public PaymentCheckoutResponse beginPaymentCheckoutForModifyBooking(BeginCheckoutReqBody paymentRequest, Map<String,String> httpHeaderMap ) throws JsonParseException, ErrorResponseFromDownstreamException, RestConnectorException {
        httpHeaderMap.put("srcRequest", "ClientGateway");
        String request = objectMapperUtil.getJsonFromObject(paymentRequest, DependencyLayer.CLIENTGATEWAY);
        logger.warn("For paymentCheckout modified booking URL={} MODIFIEDREQUEST={} ", paymentUrl, MaskingUtil.maskSensitiveDataAndLog(request));
        String result = restConnectorUtil.performPaymentCheckoutPost(request, httpHeaderMap, paymentUrl);
        logger.warn("For paymentCheckout modified booking RESPONSE={} ", MaskingUtil.maskSensitiveDataAndLog(result));
        PaymentCheckoutResponse cgServerResponse = objectMapperUtil.getObjectFromJson(result, PaymentCheckoutResponse.class, DependencyLayer.ORCHESTRATOR);
        return  cgServerResponse;

    }

    public OfferDetailsResponse getOfferDetails(String requester, HydraResponse hydraResponse, ExtendedUser extendedUser, Map<String,String> httpHeaderMap, String bookingId) throws RestConnectorException, JsonParseException {
        OfferDetailsRequest offerDetailsRequest = OfferDetailsRequest.builder().hydraSegments(hydraResponse.getHydraMatchedSegment())
                .userProfile(extendedUser.getProfileId())
                .userType(extendedUser.getProfileType())
                .uuid(extendedUser.getUuid())
                .bookingId(bookingId)
                .requester(requester)
                .build();

        String request = objectMapperUtil.getJsonFromObject(offerDetailsRequest, DependencyLayer.CLIENTGATEWAY);
        //offerDetailsUrl = RestURLHelper.getDestinationUrl(offerDetailsUrl);
        String response = restConnectorUtil.performOfferDetailsPost(request, httpHeaderMap, offerDetailsUrl);
        return objectMapperUtil.getObjectFromJson(response, OfferDetailsResponse.class, DependencyLayer.ORCHESTRATOR);
    }


}