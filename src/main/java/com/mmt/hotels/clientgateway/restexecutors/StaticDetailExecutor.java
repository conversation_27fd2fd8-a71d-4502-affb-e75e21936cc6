package com.mmt.hotels.clientgateway.restexecutors;

import com.gommt.hotels.orchestrator.enums.Brand;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.request.PlatformUgcCategoryRequest;
import com.mmt.hotels.clientgateway.thirdparty.request.UgcReviewRequest;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.CityGuideRequest;
import com.mmt.hotels.model.response.flyfish.*;
import com.mmt.hotels.model.response.staticdata.CityGuideResponse;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.request.wishlist.FlyfishReviewRequestBody;
import com.mmt.hotels.pojo.request.wishlist.HotStoreHotelsRequestBody;
import com.mmt.hotels.pojo.response.detail.HotelDetailWrapperResponse;
import com.mmt.hotels.pojo.response.wishlist.FlyfishReviewWrapperResponse;
import com.mmt.hotels.pojo.response.wishlist.HotStoreHotelsWrapperResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

@Component
public class StaticDetailExecutor {
	
	@Autowired
	private ObjectMapperUtil objectMapperUtil;
	
	@Autowired
	private RestConnectorUtil restConnectorUtil;
	
	@Value("${static.details.url}")
	private String staticDetailUrl;

	@Value("${ugc.category.url}")
	private String ugcCategoryUrl;

	@Value("${city.guide.url}")
	private String cityGuideUrl;

	@Value("${hotels.hotstore.url}")
	private String hotelsFromHotStoreUrl;

	@Value("${platform.review.summary.url}")
	private String platformReviewSummaryUrl;

	@Value("${hotels.review.summary.url}")
	private String hotelsReviewSummaryUrl;

	@Value("${ugc.reviews.url}")
	private String ugcReviewUrl;

	@Autowired
	private MetricAspect metricAspect;

	@Autowired
	private PricingEngineHelper pricingEngineHelper;

	private static final Logger logger = LoggerFactory.getLogger(StaticDetailExecutor.class);

	public HotelDetailWrapperResponse getStaticDetail(HotelDetailsMobRequestBody hotelDetailsMobRequestBody, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
		String result = getStaticDetailsResponse(hotelDetailsMobRequestBody, parameterMap, headers);
        HotelDetailWrapperResponse hotelDetailWrapperResponse = objectMapperUtil.getObjectFromJson(result, HotelDetailWrapperResponse.class, DependencyLayer.ORCHESTRATOR);
        if (hotelDetailWrapperResponse != null && hotelDetailWrapperResponse.getResponseErrors() != null && CollectionUtils.isNotEmpty(hotelDetailWrapperResponse.getResponseErrors().getErrorList())) {
        	throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
        			hotelDetailWrapperResponse.getResponseErrors().getErrorList().get(0).getErrorCode(),
        			hotelDetailWrapperResponse.getResponseErrors().getErrorList().get(0).getErrorMessage());
        }
        return hotelDetailWrapperResponse;
	}

	public String getStaticDetailsResponse(HotelDetailsMobRequestBody hotelDetailsMobRequestBody, Map<String, String[]> parameterMap,Map<String, String> httpHeaderMap) throws ClientGatewayException {
		Map<String, String> headerMap = getHeaderMap(httpHeaderMap);
        String request = objectMapperUtil.getJsonFromObject(hotelDetailsMobRequestBody, DependencyLayer.CLIENTGATEWAY);
		//staticDetailUrl = RestURLHelper.getDestinationUrl(staticDetailUrl);
		String relativeUrl = Utility.getcompleteURL(staticDetailUrl, parameterMap, hotelDetailsMobRequestBody.getCorrelationKey());
		relativeUrl = pricingEngineHelper.appendPEEInUrl(relativeUrl, hotelDetailsMobRequestBody.getExperimentData());
		logger.debug(relativeUrl);
		logger.debug(request);

		return restConnectorUtil.performStaticDetailPost(request, headerMap, relativeUrl);
	}

	public UGCPlatformReviewSummaryDTO getUgcCategoryDetail(PlatformUgcCategoryRequest ugcCategoryRequestBody, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
		Map<String, String> headerMap = getHeaderMap(headers);
		String request = objectMapperUtil.getJsonFromObject(ugcCategoryRequestBody, DependencyLayer.CLIENTGATEWAY);
		//ugcCategoryUrl = RestURLHelper.getDestinationUrl(ugcCategoryUrl);
		String relativeUrl = Utility.getcompleteURL(ugcCategoryUrl, parameterMap, ugcCategoryRequestBody.getCorrelationKey());
		String result = restConnectorUtil.performStaticDetailPost(request, headerMap, relativeUrl);
		UGCPlatformReviewSummaryDTO ugcCategoryWrapperResponse = objectMapperUtil.getObjectFromJson(result, UGCPlatformReviewSummaryDTO.class, DependencyLayer.ORCHESTRATOR);
		String currency = DEFAULT_CUR_INR;
		if (headers.containsKey(USER_COUNTRY)) {
			currency = headers.get(USER_CURRENCY);
		}
		ugcCategoryWrapperResponse.setCurrency(currency);
		return ugcCategoryWrapperResponse;
	}

	public UgcReviewResponseData getReviewData(UgcReviewRequest request, Map<String, String[]> parameterMap) throws  ClientGatewayException {

		request.setClient(Brand.MMT.name());

		String requestString = objectMapperUtil.getJsonFromObject(request, DependencyLayer.CLIENTGATEWAY);
		logger.debug("Request for UGC Reviews: " + requestString);
		Map<String, String> headers = new HashMap<>();
		headers.put(HEADER_CONTENT_TYPE, "application/json");
		String relativeUrl = Utility.getcompleteURL(ugcReviewUrl, parameterMap, request.getCorrelationKey());
		String resp = restConnectorUtil.performFetchUgcReviewsPost(requestString, headers, relativeUrl);
		UgcReviewResponseData ugcReviewResponseData = objectMapperUtil.getObjectFromJson(resp, UgcReviewResponseData.class, DependencyLayer.CLIENTGATEWAY);

		return ugcReviewResponseData;
	}


	public CityGuideResponse getCityGuildeResponse(CityGuideRequest cityGuideRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
		Map<String, String> headerMap = getHeaderMap(httpHeaderMap);
		String request = objectMapperUtil.getJsonFromObject(cityGuideRequest, DependencyLayer.CLIENTGATEWAY);
		//cityGuideUrl = RestURLHelper.getDestinationUrl(cityGuideUrl);
		String relativeUrl = Utility.getcompleteURL(cityGuideUrl, parameterMap, cityGuideRequest.getCorrelationKey());
		logger.debug(relativeUrl);
		logger.debug(request);

		String response = restConnectorUtil.performStaticDetailPost(request, headerMap, relativeUrl);
		CityGuideResponse cityGuideResponse = objectMapperUtil.getObjectFromJson(response, CityGuideResponse.class, DependencyLayer.ORCHESTRATOR);
		String currency = DEFAULT_CUR_INR;
		if (httpHeaderMap.containsKey(USER_COUNTRY)) {
			currency = httpHeaderMap.get(USER_CURRENCY);
		}
		cityGuideResponse.setCurrency(currency);
		return cityGuideResponse;
	}


	private Map<String, String> getHeaderMap(Map<String, String> httpHeaderMap) {
		Map<String, String> headerMap = new HashMap<>();
		headerMap.put("Content-Type", "application/json");
		headerMap.put("Accept-Encoding", "gzip");
		headerMap.put("srcRequest", "ClientGateway");
		if (StringUtils.isNotEmpty(httpHeaderMap.get("mmt-auth")))
			headerMap.put("mmt-auth", httpHeaderMap.get("mmt-auth"));

		String akmaiHeader = httpHeaderMap.get(Constants.HEADER_AKAMAI);
		if (StringUtils.isEmpty(akmaiHeader))
			akmaiHeader = httpHeaderMap.get(Constants.HEADER_AKAMAI.toLowerCase());

		if(StringUtils.isNotEmpty(akmaiHeader)){
			headerMap.put(Constants.HEADER_AKAMAI.toLowerCase(),akmaiHeader);
		}
		return headerMap;
	}

	public HotStoreHotelsWrapperResponse getWishListedHotelsFromHotStore(HotStoreHotelsRequestBody hotStoreHotelsRequestBody, Map<String, String> httpHeaderMap)
			throws ClientGatewayException {
		long start = new Date().getTime();
		try {
			Map<String, String> headerMap = getHeaderMap(httpHeaderMap);
			String request = objectMapperUtil.getJsonFromObject(hotStoreHotelsRequestBody, DependencyLayer.CLIENTGATEWAY);
			//hotelsFromHotStoreUrl = RestURLHelper.getDestinationUrl(hotelsFromHotStoreUrl);
			String relativeUrl = String.format(hotelsFromHotStoreUrl, hotStoreHotelsRequestBody.getCorrelationKey());
			logger.debug(request);
			logger.debug(relativeUrl);
			String result = restConnectorUtil.performHotelsHotStorePost(request, headerMap, relativeUrl);
			HotStoreHotelsWrapperResponse hotStoreHotelsWrapperResponse = objectMapperUtil.getObjectFromJson(result, HotStoreHotelsWrapperResponse.class, DependencyLayer.ORCHESTRATOR);
			if (hotStoreHotelsWrapperResponse != null && hotStoreHotelsWrapperResponse.getResponseErrors() != null
					&& CollectionUtils.isNotEmpty(hotStoreHotelsWrapperResponse.getResponseErrors().getErrorList())) {
				throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
						hotStoreHotelsWrapperResponse.getResponseErrors().getErrorList().get(0).getErrorCode(),
						hotStoreHotelsWrapperResponse.getResponseErrors().getErrorList().get(0).getErrorMessage());
			}
			return hotStoreHotelsWrapperResponse;
		} finally {
			metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "getWishListedHotelsFromHotStore", new Date().getTime() - start);
		}
	}

	public FlyfishReviewWrapperResponse getFlyFishReviewSummary(FlyfishReviewRequestBody flyfishReviewRequestBody, Map<String, String> httpHeaderMap) throws JsonParseException {
		long start = new Date().getTime();
		FlyfishReviewWrapperResponse flyfishReviewWrapperResponse = new FlyfishReviewWrapperResponse();
		try {
			Map<String, String> headerMap = getHeaderMap(httpHeaderMap);
			String request = objectMapperUtil.getJsonFromObject(flyfishReviewRequestBody, DependencyLayer.CLIENTGATEWAY);
			//hotelsReviewSummaryUrl = RestURLHelper.getDestinationUrl(hotelsReviewSummaryUrl);
			String relativeUrl = String.format(hotelsReviewSummaryUrl, flyfishReviewRequestBody.getCorrelationKey());
			logger.debug(request);
			logger.debug(relativeUrl);
			String result = restConnectorUtil.performHotelsReviewSummaryPost(request, headerMap, relativeUrl);
			flyfishReviewWrapperResponse = objectMapperUtil.getObjectFromJson(result, FlyfishReviewWrapperResponse.class, DependencyLayer.ORCHESTRATOR);
			if (flyfishReviewWrapperResponse != null && flyfishReviewWrapperResponse.getResponseErrors() != null
					&& CollectionUtils.isNotEmpty(flyfishReviewWrapperResponse.getResponseErrors().getErrorList())) {
				logger.debug("Error while getting FlyFishReviewSummary {}", flyfishReviewWrapperResponse.getResponseErrors().getErrorList());
			}
		} catch (Exception e) {
			logger.error("Exception occurred while getting FlyFishReviewSummary for dateLess case ", e);
			if (flyfishReviewWrapperResponse.getResponseErrors() != null && CollectionUtils.isNotEmpty(flyfishReviewWrapperResponse.getResponseErrors().getErrorList())) {
				logger.error("Error while fetching FlyFishReviewSummary for dateLess case {}", flyfishReviewWrapperResponse.getResponseErrors().getErrorList());
			}
		} finally {
			metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "getFlyFishReviewSummary", new Date().getTime() - start);
		}
		return flyfishReviewWrapperResponse;
	}

	public UserReviewResponseForListing getPlatformsUgcSummary(UgcSummaryRequest ugcSummaryRequest, Map<String, String> httpHeaderMap, String ck){
		long start = new Date().getTime();
		UserReviewResponseForListing userReviewResponseForListing = new UserReviewResponseForListing();
		try{
			Map<String, String> headerMap = getHeaderMap(httpHeaderMap);
			String request = objectMapperUtil.getJsonFromObject(ugcSummaryRequest, DependencyLayer.CLIENTGATEWAY);
			String relativeUrl = String.format(platformReviewSummaryUrl, ck);
			String result = restConnectorUtil.performPlatformReviewSummaryPost(request, headerMap, relativeUrl);
			userReviewResponseForListing = objectMapperUtil.getObjectFromJson(result, UserReviewResponseForListing.class, DependencyLayer.ORCHESTRATOR);
		}
		catch(Exception e){
			logger.error("Exception occurred while getting UGCPlatformReviewSummary ", e);
		} finally {
			metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "getPlatformsUgcSummary", new Date().getTime() - start);
		}
		return userReviewResponseForListing;
	}

}
