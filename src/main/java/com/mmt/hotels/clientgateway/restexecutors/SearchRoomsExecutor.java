package com.mmt.hotels.clientgateway.restexecutors;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.RoomDataRequest;
import com.mmt.hotels.clientgateway.businessobjects.RoomDataResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.HotelsImagesSearchRequest;
import com.mmt.hotels.model.request.ImageCategoryEntityBO;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.upsell.UpsellHotelRequest;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import com.mmt.hotels.model.response.staticdata.HotelImage;
import com.mmt.hotels.model.response.staticdata.HotelsImageResponseEntity;
import com.mmt.model.HotelsRoomInfoResponseEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;

@Component
public class SearchRoomsExecutor {


	@Autowired
	@Qualifier("detailServiceThreadPool")
	private ThreadPoolTaskExecutor detailServiceThreadPool;

	@Autowired
	private ObjectMapperUtil objectMapperUtil;
	
	@Autowired
	private RestConnectorUtil restConnectorUtil;

	@Autowired
	private MetricAspect metricAspect;

	@Value("${search.rooms.url}")
	private String searchRoomsUrl;

	@Value("${static.rooms.url}")
	private String staticRoomsUrl;

	@Value("${alternate.dates.url}")
	private String alternateDatesPriceUrl;

	private static final Gson gson = new Gson();

	@Value("${comparator.url}")
	private String comparatorUrl;

	private static final Logger logger = LoggerFactory.getLogger(SearchRoomsExecutor.class);
	@Value("${hotel.image.url}")
	private String hotelImageUrl;

	@Value("${hotel.data.url}")
	private String hotelDataUrl;

	@Autowired
	PricingEngineHelper pricingEngineHelper;

	public Future<RoomDetailsResponse> getRoomPrices(PriceByHotelsRequestBody priceByHotelsRequestBody, Map<String, String[]> parameterMap, Map<String, String> headers, @Nullable CountDownLatch countDownLatchAsync) throws ClientGatewayException {
		Map<String, String> mdcMap = MDC.getCopyOfContextMap();
		return detailServiceThreadPool.submit(() -> {
			try{
				logger.warn("detailServiceThreadPool 2 : "+ detailServiceThreadPool.getActiveCount());
				if (mdcMap != null) {
					MDC.setContextMap(mdcMap);
				}
				Map<String, String> headerMap = new HashMap<String, String>();
				headerMap.put("Content-Type", "application/json");
				headerMap.put("Accept-Encoding", "gzip");
				headerMap.put("srcRequest", "ClientGateway");
				if (StringUtils.isNotEmpty(headers.get("x-hidden-params"))) {
					headerMap.put("x-hidden-params", headers.get("x-hidden-params"));
				}
				if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
					headerMap.put("mmt-auth", headers.get("mmt-auth"));
				String akmaiHeader = headers.get(Constants.HEADER_AKAMAI);
				if (StringUtils.isEmpty(akmaiHeader))
					akmaiHeader = headers.get(Constants.HEADER_AKAMAI.toLowerCase());

				if(StringUtils.isNotEmpty(akmaiHeader)){
					headerMap.put(Constants.HEADER_AKAMAI.toLowerCase(),akmaiHeader);
				}

				String request = objectMapperUtil.getJsonFromObject(priceByHotelsRequestBody, DependencyLayer.CLIENTGATEWAY);
				//searchRoomsUrl = RestURLHelper.getDestinationUrl(searchRoomsUrl);
				String relativeUrl = Utility.getcompleteURL(searchRoomsUrl, parameterMap, priceByHotelsRequestBody.getCorrelationKey());
				relativeUrl = pricingEngineHelper.appendPEEDInUrl(relativeUrl, priceByHotelsRequestBody.getExperimentData());
				relativeUrl = pricingEngineHelper.appendCountryInUrl(relativeUrl, priceByHotelsRequestBody.getCountryCode());
				relativeUrl = pricingEngineHelper.appendFunnelTraffic(relativeUrl, priceByHotelsRequestBody.getFunnelSource(), priceByHotelsRequestBody.getTrafficSource(), priceByHotelsRequestBody.getRequestIdentifier());


				logger.debug(request);
				logger.debug(relativeUrl);

				long startTime = new Date().getTime();
				String result = null;
				try {
					result = restConnectorUtil.performSearchRoomsPost(request, headerMap, relativeUrl);
				} catch (Exception ex) {
//					countDownLatchAsync.countDown();
					throw ex;
				} finally {
					logger.warn("detailServiceThreadPool 2 : END1 in "+ (new Date().getTime() - startTime));
					metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "searchRooms/searchPrice", new Date().getTime() - startTime);
				}

				RoomDetailsResponse roomDetailsResponse = objectMapperUtil.getObjectFromJson(result, RoomDetailsResponse.class, DependencyLayer.ORCHESTRATOR);
				if (roomDetailsResponse != null && roomDetailsResponse.getResponseErrors() != null &&
						CollectionUtils.isNotEmpty(roomDetailsResponse.getResponseErrors().getErrorList())) {
					logger.error("ErrorResponseFromDownstreamException exception thrown :- ERROR CODE: {} ERROR MESSAGE: {}", roomDetailsResponse.getResponseErrors().getErrorList().get(0).getErrorCode(),
							roomDetailsResponse.getResponseErrors().getErrorList().get(0).getErrorMessage());
//					countDownLatchAsync.countDown();
					logger.warn("detailServiceThreadPool 2 : END2 in "+ (new Date().getTime() - startTime));
					throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
							roomDetailsResponse.getResponseErrors().getErrorList().get(0).getErrorCode(),
							roomDetailsResponse.getResponseErrors().getErrorList().get(0).getErrorMessage(),
							roomDetailsResponse.getResponseErrors().getErrorList().get(0).getAlternateMessage());
				}
				if(countDownLatchAsync != null){
					countDownLatchAsync.countDown();
				}
				logger.warn("detailServiceThreadPool 2 : END3 in "+ (new Date().getTime() - startTime));
				return roomDetailsResponse;
			} catch (Exception ex) {
				if(countDownLatchAsync != null){
					countDownLatchAsync.countDown();
				}
				logger.error("getRoomPrices exception thrown :- ERROR MESSAGE: {}", ex.getMessage());
				throw ex;
			}
		});
	}

	public Future<HotelsRoomInfoResponseEntity> getRoomStaticDetails(String hotelId, int totalCandidate,String correlationKey, Map<String, String[]> parameterMap, Map<String, String> headers,@Nullable CountDownLatch countDownLatchAsync, String pageContext, String expData) throws ClientGatewayException {
		Map<String, String> mdcMap = MDC.getCopyOfContextMap();
		return detailServiceThreadPool.submit(() -> {
			try {
				logger.warn("detailServiceThreadPool 1 : "+detailServiceThreadPool.getActiveCount());
				if (mdcMap != null) {
					MDC.setContextMap(mdcMap);
				}
				Map<String, String> headerMap = new HashMap<String, String>();
				headerMap.put("Accept-Encoding", "gzip");
				headerMap.put("srcRequest", "ClientGateway");
				if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
					headerMap.put("mmt-auth", headers.get("mmt-auth"));
				Map<String, String> params = new HashMap<>();
				params.put("hotelIds", new JSONArray(Collections.singletonList(hotelId)).toString());
				params.put("totalCandidates", String.valueOf(totalCandidate));
				//staticRoomsUrl = RestURLHelper.getDestinationUrl(staticRoomsUrl);
				StringBuilder sb = new StringBuilder(staticRoomsUrl);
				sb.append("?");
				if (expData != null) {
					Type type = new TypeToken<Map<String, String>>() {
					}.getType();
					String expDataStr = expData.replaceAll("^\"|\"$", "");
					Map<String, String> expDataMap = gson.fromJson(expDataStr, type);
					if (expDataMap != null) {
						String isUgcV2 = expDataMap.get(Constants.UGCV2);
						if (isUgcV2 != null) {
							params.put(Constants.IS_UGC_V2, Constants.EXP_TRUE_VALUE.equalsIgnoreCase(isUgcV2) ? "true" : "false");
						}
					}
				}
				if (null != params) {
					for (Map.Entry<String, String> entry : params.entrySet()) {
						if (entry.getKey() != null && entry.getValue() != null) {
							sb.append("&");
							sb.append(entry.getKey());
							sb.append("=");
							try {
								sb.append(URLEncoder.encode(entry.getValue(), "UTF-8"));
							} catch (UnsupportedEncodingException e) {
								// TODO Auto-generated catch block
								e.printStackTrace();
							}
						}
					}
				}

				String relativeUrl = Utility.getcompleteURL(sb.toString(), parameterMap, correlationKey);
				relativeUrl = Utility.appendPageContextToURL(relativeUrl, pageContext);
				logger.debug(relativeUrl);

				long startTime = new Date().getTime();
				String result = null;
				try {
					result = restConnectorUtil.performStaticRoomDetailGet(headerMap, relativeUrl);
				} catch (Exception ex) {
//					countDownLatchAsync.countDown();
					throw ex;
				} finally {
					logger.warn("detailServiceThreadPool 1 : END1 in "+ (new Date().getTime() - startTime));
					metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "searchRooms/staticDetails", new Date().getTime() - startTime);
				}
				HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity = objectMapperUtil.getObjectFromJson(result, HotelsRoomInfoResponseEntity.class, DependencyLayer.ORCHESTRATOR);
				if (hotelsRoomInfoResponseEntity != null && hotelsRoomInfoResponseEntity.getResponseErrors() != null &&
						CollectionUtils.isNotEmpty(hotelsRoomInfoResponseEntity.getResponseErrors().getErrorList())) {
//					countDownLatchAsync.countDown();
					logger.warn("detailServiceThreadPool 1 : END2 in "+ (new Date().getTime() - startTime));
					throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
							hotelsRoomInfoResponseEntity.getResponseErrors().getErrorList().get(0).getErrorCode(),
							hotelsRoomInfoResponseEntity.getResponseErrors().getErrorList().get(0).getErrorMessage());
				}
				if(countDownLatchAsync != null){
					countDownLatchAsync.countDown();
				}
				logger.warn("detailServiceThreadPool 1 : END3 in "+ (new Date().getTime() - startTime));
				return hotelsRoomInfoResponseEntity;
			} catch (Exception ex) {
				if(countDownLatchAsync != null){
					countDownLatchAsync.countDown();
				}
				logger.error("getRoomStaticDetails exception thrown :- ERROR MESSAGE: {}", ex.getMessage());
				throw ex;
			}
		});
	}

	public String getRoomPricesOld(PriceByHotelsRequestBody priceByHotelsRequestBody, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Content-Type", "application/json");
		headerMap.put("Accept-Encoding", "gzip");
		headerMap.put("srcRequest", "ClientGateway");
		if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
			headerMap.put("mmt-auth", headers.get("mmt-auth"));

		String request = objectMapperUtil.getJsonFromObject(priceByHotelsRequestBody, DependencyLayer.CLIENTGATEWAY);
		//searchRoomsUrl = RestURLHelper.getDestinationUrl(searchRoomsUrl);
		String relativeUrl = Utility.getcompleteURL(searchRoomsUrl, parameterMap, priceByHotelsRequestBody.getCorrelationKey());
		relativeUrl = pricingEngineHelper.appendPEEDInUrl(relativeUrl, priceByHotelsRequestBody.getExperimentData());
		logger.debug(request);
		logger.debug(relativeUrl);

		String response= restConnectorUtil.performSearchRoomsPost(request, headerMap, relativeUrl);
		RoomDetailsResponse responseObject  =objectMapperUtil.getObjectFromJson(response, RoomDetailsResponse.class, DependencyLayer.ORCHESTRATOR);
		if(null!=responseObject){
			response = objectMapperUtil.getJsonFromObjectWithView(responseObject, DependencyLayer.ORCHESTRATOR, PIIView.class);
		}else{
			response=null;
		}
		return response;
	}

	public  <T> T getComparatorOld(UpsellHotelRequest upsellHotelRequest, Map<String, String[]> parameterMap, Map<String, String> headers, Class<T> type) throws ClientGatewayException {
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Content-Type", "application/json");
		headerMap.put("Accept-Encoding", "gzip");
		headerMap.put("srcRequest", "ClientGateway");
		if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
			headerMap.put("mmt-auth", headers.get("mmt-auth"));

		String request = objectMapperUtil.getJsonFromObject(upsellHotelRequest, DependencyLayer.CLIENTGATEWAY);
		//comparatorUrl = RestURLHelper.getDestinationUrl(comparatorUrl);
		String relativeUrl = Utility.getcompleteURL(comparatorUrl,parameterMap,upsellHotelRequest.getCorrelationKey());
		logger.debug(request);
		logger.debug(relativeUrl);

		String result =restConnectorUtil.performSearchRoomsPost(request, headerMap, relativeUrl);
		if(type != String.class)
			return objectMapperUtil.getObjectFromJson(result, type, DependencyLayer.ORCHESTRATOR);
		else
			return (T)result;
	}
	
	public String alternateDatesPriceOld(PriceByHotelsRequestBody priceByHotelsRequestBody, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Content-Type", "application/json");
		headerMap.put("Accept-Encoding", "gzip");
		headerMap.put("srcRequest", "ClientGateway");
		if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
			headerMap.put("mmt-auth", headers.get("mmt-auth"));

		//alternateDatesPriceUrl = RestURLHelper.getDestinationUrl(alternateDatesPriceUrl);
		String relativeUrl = Utility.getcompleteURL(alternateDatesPriceUrl, parameterMap, priceByHotelsRequestBody.getCorrelationKey());
		String request = objectMapperUtil.getJsonFromObject(priceByHotelsRequestBody, DependencyLayer.CLIENTGATEWAY);
		logger.debug(request);
		logger.debug(relativeUrl);

		return restConnectorUtil.performAlternateDatesPost(request, headerMap, relativeUrl);
	}

	public Future<HotelImage> getHotelImages(SearchRoomsRequest searchRoomsRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, CountDownLatch countDownLatchAsync, String pageContext) {
		Map<String, String> mdcMap = MDC.getCopyOfContextMap();
		return detailServiceThreadPool.submit(() -> {
			try{
				logger.warn("detailServiceThreadPool 5 : "+detailServiceThreadPool.getActiveCount());
				if (mdcMap != null) {
					MDC.setContextMap(mdcMap);
				}
				Map<String, String> params = new HashMap<>();
				params.put("hotelIds", new JSONArray(Collections.singletonList(searchRoomsRequest.getSearchCriteria().getHotelId())).toString());
				params.put("networkType", searchRoomsRequest.getDeviceDetails().getNetworkType());
				params.put("imageType", "[\"professional\"]");

				List<ImageCategoryEntityBO> imageCategoriesList = new ArrayList<>();
				ImageCategoryEntityBO imageCategory = new ImageCategoryEntityBO();
				imageCategory.setCount(100);
				imageCategory.setCategory("R");
				imageCategoriesList.add(imageCategory);

				params.put("imageCategory", new JSONArray(imageCategoriesList).toString());
				params.put("isThumbnailRequired", "true");
				params.put("countryCode", searchRoomsRequest.getSearchCriteria().getCountryCode());

				if (searchRoomsRequest.getExpData() != null) {
					Type type = new TypeToken<Map<String, String>>() {
					}.getType();
					String expData = searchRoomsRequest.getExpData().replaceAll("^\"|\"$", "");
					Map<String, String> expDataMap = gson.fromJson(expData, type);
					if (expDataMap != null) {
						String imgSeq = expDataMap.get("HIS");
						if (imgSeq != null) {
							params.put("imgSeq", imgSeq);
						}
						String isUgcV2 = expDataMap.get(Constants.UGCV2);
						if (isUgcV2 != null) {
							params.put(Constants.IS_UGC_V2, Constants.EXP_TRUE_VALUE.equalsIgnoreCase(isUgcV2) ? "true" : "false");
						}
					}
				}
				//hotelImageUrl = RestURLHelper.getDestinationUrl(hotelImageUrl);
				StringBuilder sb = new StringBuilder(hotelImageUrl);
				sb.append("?");
				if (null != params) {
					for (Map.Entry<String, String> entry : params.entrySet()) {
						if (entry.getKey() != null && entry.getValue() != null) {
							sb.append("&");
							sb.append(entry.getKey());
							sb.append("=");
							try {
								sb.append(URLEncoder.encode(entry.getValue(), "UTF-8"));
							} catch (UnsupportedEncodingException e) {
								// TODO Auto-generated catch block
								e.printStackTrace();
							}
						}
					}
				}
				String url = Utility.getcompleteURL(sb.toString(),parameterMap,searchRoomsRequest.getCorrelationKey());
				url = Utility.appendPageContextToURL(url, pageContext);
				long startTime = new Date().getTime();
				String result = null;
				try {
					result = restConnectorUtil.performStaticRoomDetailGet(httpHeaderMap, url);
				} catch (RestConnectorException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				} finally {
					metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "searchRooms/roomImages", new Date().getTime() - startTime);
				}
				try {
					HotelsImageResponseEntity response = objectMapperUtil.getObjectFromJson(result, HotelsImageResponseEntity.class, DependencyLayer.ORCHESTRATOR);

					if (CollectionUtils.isNotEmpty(response.getImages())) {
						countDownLatchAsync.countDown();
						return response.getImages().get(0);
					} else {
						countDownLatchAsync.countDown();
						return null;
					}
				} catch (JsonParseException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				countDownLatchAsync.countDown();
				logger.warn("detailServiceThreadPool 5 : END");
				return null;
			} catch (Exception ex) {
				countDownLatchAsync.countDown();
				logger.error("search slot getHotelImages exception thrown :- ERROR MESSAGE: {}", ex.getMessage());
				throw ex;
			}
		});
	}

	/*
		New POST API parallel to GET API made to send userContext and pokus details, to further send to hotstore for contextualisation of Images
	 */

	public Future<HotelImage> getHotelImages(HotelsImagesSearchRequest hotelsImagesSearchRequest, SearchRoomsRequest searchRoomsRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, CountDownLatch countDownLatchAsync, String pageContext) {
		Map<String, String> mdcMap = MDC.getCopyOfContextMap();
		return detailServiceThreadPool.submit(() -> {
			try{
				logger.warn("detailServiceThreadPool 3 : "+detailServiceThreadPool.getActiveCount());
				if (mdcMap != null) {
					MDC.setContextMap(mdcMap);
				}
				Map<String, String> headerMap = new HashMap<String, String>();
				headerMap.put("Content-Type", "application/json");
				headerMap.put("Accept-Encoding", "gzip");
				headerMap.put("srcRequest", "ClientGateway");
				if (StringUtils.isNotEmpty(httpHeaderMap.get("mmt-auth")))
					headerMap.put("mmt-auth", httpHeaderMap.get("mmt-auth"));

				hotelsImagesSearchRequest.setHotelIds(new JSONArray(Collections.singletonList(searchRoomsRequest.getSearchCriteria().getHotelId())).toString());
				hotelsImagesSearchRequest.setNetworkType(searchRoomsRequest.getDeviceDetails().getNetworkType());
				hotelsImagesSearchRequest.setImageType("[\"professional\"]");

				List<ImageCategoryEntityBO> imageCategoriesList = new ArrayList<>();
				ImageCategoryEntityBO imageCategory = new ImageCategoryEntityBO();
				imageCategory.setCount(100);
				imageCategory.setCategory("R");
				imageCategoriesList.add(imageCategory);
				hotelsImagesSearchRequest.setImageCategory(new JSONArray(imageCategoriesList).toString());
				hotelsImagesSearchRequest.setCorrelationKey(searchRoomsRequest.getCorrelationKey());
				hotelsImagesSearchRequest.setCountryCode(searchRoomsRequest.getSearchCriteria().getCountryCode());
				hotelsImagesSearchRequest.setThumbnailRequired(true);

				if (searchRoomsRequest.getExpData() != null) {
					Type type = new TypeToken<Map<String, String>>() {
					}.getType();
					String expData = searchRoomsRequest.getExpData().replaceAll("^\"|\"$", "");
					Map<String, String> expDataMap = gson.fromJson(expData, type);
					if (expDataMap != null) {
						String imgSeq = expDataMap.get("HIS");
						if (imgSeq != null) {
							hotelsImagesSearchRequest.setImgSeq(imgSeq);
						}
					}
				}

				hotelsImagesSearchRequest.setPageContext(pageContext);

				String request = objectMapperUtil.getJsonFromObject(hotelsImagesSearchRequest, DependencyLayer.CLIENTGATEWAY);
				//hotelImageUrl = RestURLHelper.getDestinationUrl(hotelImageUrl);
				String relativeUrl = Utility.getcompleteURL(hotelImageUrl, parameterMap, searchRoomsRequest.getCorrelationKey());

				long startTime = new Date().getTime();
				String result = null;
				try {
					result = restConnectorUtil.performStaticRoomDetailPost(request, headerMap, relativeUrl);
				} catch (RestConnectorException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				} finally {
					metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "searchRooms/roomImages", new Date().getTime() - startTime);
				}
				try {
					HotelsImageResponseEntity response = objectMapperUtil.getObjectFromJson(result, HotelsImageResponseEntity.class, DependencyLayer.ORCHESTRATOR);

					if (CollectionUtils.isNotEmpty(response.getImages())) {
						countDownLatchAsync.countDown();
						logger.warn("detailServiceThreadPool 3 : END1 in "+ (new Date().getTime()-startTime));
						return response.getImages().get(0);
					} else {
						countDownLatchAsync.countDown();
						logger.warn("detailServiceThreadPool 3 : END2  in "+ (new Date().getTime()-startTime));
						return null;
					}
				} catch (JsonParseException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				countDownLatchAsync.countDown();
				logger.warn("detailServiceThreadPool 3 : END3 in " +(new Date().getTime()-startTime));
				return null;
			} catch (Exception ex) {
				countDownLatchAsync.countDown();
				logger.error("detailServiceThreadPool 3 | Error in parsing getHotelImages: {}", ex.getMessage());
				throw ex;
			}
		});
	}

	public RoomDataResponse getRoomData(RoomDataRequest roomNameRequest, Map<String, String> httpHeaderMap, String correlationKey) throws ClientGatewayException {
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Content-Type", "application/json");
		headerMap.put("Accept-Encoding", "gzip");
		headerMap.put("srcRequest", "ClientGateway");
		if (StringUtils.isNotEmpty(httpHeaderMap.get("mmt-auth"))) {
			headerMap.put("mmt-auth", httpHeaderMap.get("mmt-auth"));
		}
		long startTime = new Date().getTime();
		//hotelDataUrl = RestURLHelper.getDestinationUrl(hotelDataUrl);
		String relativeUrl = Utility.getcompleteURL(hotelDataUrl, new HashMap<>(), roomNameRequest.getCorrelationKey());
		String request = objectMapperUtil.getJsonFromObject(roomNameRequest, DependencyLayer.CLIENTGATEWAY);
		RoomDataResponse response = null;
		try {
			String result = restConnectorUtil.performPostRoomData(request, headerMap, relativeUrl);
			response = objectMapperUtil.getObjectFromJson(result, RoomDataResponse.class, DependencyLayer.ORCHESTRATOR);
			return response;
		} catch (JsonParseException e) {
			logger.error("Error in parsing room data response: {}, correlationKey: {}", e.getMessage(), correlationKey);
		} catch (RestConnectorException e) {
			logger.error("Error in getting room data: {}, correlationKey: {}", e.getMessage(), correlationKey);
		} finally {
			metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "roomData", new Date().getTime() - startTime);
		}
		return response;
	}

}
