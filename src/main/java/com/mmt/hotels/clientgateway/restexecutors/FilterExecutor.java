package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.businessobjects.SmartFiltersRequest;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.request.filter.EvaluateFilterRankOrderRequest;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.FilterCountLoggingRequest;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

import static com.mmt.hotels.clientgateway.constants.ControllerConstants.LISTING_FILTER_COUNT;

@Component
public class FilterExecutor {


    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Value("${filter.count.url}")
    private String filterCountUrl;

    @Value("${evaluate.filter.rank.order.url}")
    private String evaluateFilterRankOrderUrl;

    @Autowired
    @Qualifier("evaluateFilterRankOrderThreadPool")
    private ThreadPoolTaskExecutor evaluateFilterRankOrderThreadPool;

    @Autowired
    PricingEngineHelper pricingEngineHelper;
    
    @Value("${filter.count.log.url}")
    private String filterCountLoggingUrl;

    @Value("${smart.filter.url}")
    private String smartFilterUrl;

    @Autowired
    private Utility utility;

    @Autowired
    private MetricAspect metricAspect;
    
    private static final Logger logger = LoggerFactory.getLogger(FilterExecutor.class);

    public <T> T filterCount(SearchWrapperInputRequest filterRequest, Map<String, String> headers, Class<T> type, Map<String, String[]> parameterMap) throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
        	headerMap.put("mmt-auth", headers.get("mmt-auth"));
        String request = objectMapperUtil.getJsonFromObject(filterRequest, DependencyLayer.CLIENTGATEWAY);

        if (parameterMap != null && !parameterMap.isEmpty()) {
            parameterMap = utility.addSrcReqToParameterMap(parameterMap);
        }
        logger.debug(request);
        String relativeUrl = pricingEngineHelper.appendFilterServiceExpInUrl(filterCountUrl, filterRequest.getExperimentData());
        relativeUrl = Utility.getcompleteURL(filterCountUrl, parameterMap, filterRequest.getCorrelationKey());
        String result = restConnectorUtil.performFilterCountPost(request, headerMap, relativeUrl);
        if(type != String.class)
            return objectMapperUtil.getObjectFromJson(result, type, DependencyLayer.ORCHESTRATOR);
        else
            return (T)result;
    }

    /**
     * Method to hit rule engine to evaluate the request and fetch the filter rank order configuration.
     * @param evaluateFilterRankOrderRequest Request object
     * @param correlationKey correlation key
     * @param headers header map to be sent in rest call.
     * @return Future object of filter rank order String.
     */
    public Future<String> evaluateFilterRankOrder(EvaluateFilterRankOrderRequest evaluateFilterRankOrderRequest, String correlationKey, Map<String, String> headers) {
        Map<String, String> mdcMap = MDC.getCopyOfContextMap();
        return evaluateFilterRankOrderThreadPool.submit(() -> {
            String response = null;
            try {
                if (mdcMap != null) {
                    MDC.setContextMap(mdcMap);
                }
                Map<String, String> headerMap = new HashMap<String, String>();
                headerMap.put("Content-Type", "application/json");
                headerMap.put("Accept-Encoding", "gzip");
                headerMap.put("srcRequest", "ClientGateway");
                if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
                    headerMap.put("mmt-auth", headers.get("mmt-auth"));
                String request = objectMapperUtil.getJsonFromObject(evaluateFilterRankOrderRequest, DependencyLayer.CLIENTGATEWAY);
                //evaluateFilterRankOrderUrl = RestURLHelper.getDestinationUrl(evaluateFilterRankOrderUrl);
                String relativeUrl = String.format(evaluateFilterRankOrderUrl, correlationKey);
                logger.debug(request);
                long startTime = System.currentTimeMillis();
                response = restConnectorUtil.evaluateFilterRankOrderPost(request, headerMap, relativeUrl);
                metricAspect.addToTimeInternalProcess(Constants.PROCESS_DOWNSTREAM_EVALUATE_FILTER_RANK_CALL, LISTING_FILTER_COUNT, System.currentTimeMillis() - startTime);
            } catch (Exception ex) {
                logger.warn("Error executing evaluateFilterRankOrder: ", ex.getMessage());
            }
            return response;
        });
    }

    public void filterCountLogging(FilterCountLoggingRequest filterCountLoggingRequest,
			Map<String, String> headers) throws ClientGatewayException {
		Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
        	headerMap.put("mmt-auth", headers.get("mmt-auth"));
        String request = objectMapperUtil.getJsonFromObject(filterCountLoggingRequest, DependencyLayer.CLIENTGATEWAY);
        logger.debug(request);
        //filterCountLoggingUrl = RestURLHelper.getDestinationUrl(filterCountLoggingUrl);
        restConnectorUtil.performFilterCountPost(request, headerMap, filterCountLoggingUrl);
		
	}

    public <T> T getSmartFilterTags(FilterCountRequest filterCountRequest, List<String> filterTitles, Map<String, String> headers, Class<T> type) throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));

        SmartFiltersRequest filterRequest = new SmartFiltersRequest();
        filterRequest.setFilters(filterTitles);
        filterRequest.setQueryText(filterCountRequest.getSearchCriteria().getSearchQueryText());
        String request = objectMapperUtil.getJsonFromObject(filterRequest, DependencyLayer.CHATBOT);
        String result = restConnectorUtil.performSmartFiltersPost(request, headerMap, smartFilterUrl);
        if (type != String.class)
            return objectMapperUtil.getObjectFromJson(result, type, DependencyLayer.CHATBOT);
        else
            return (T) result;
    }

}
