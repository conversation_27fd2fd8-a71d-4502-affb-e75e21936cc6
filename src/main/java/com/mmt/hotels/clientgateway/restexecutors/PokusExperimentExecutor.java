package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.thirdparty.request.PokusExperimentRequest;
import com.mmt.hotels.clientgateway.thirdparty.response.PokusExperimentResponse;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.PAGE_CONTEXT_REVIEW;
import static com.mmt.hotels.clientgateway.constants.Constants.USER_COUNTRY;
import static com.mmt.hotels.clientgateway.constants.Constants.ENTITY_NAME;

@Component
public class PokusExperimentExecutor {
	
	 @Autowired
	 private ObjectMapperUtil objectMapperUtil;

	 @Autowired
	 private RestConnectorUtil restConnectorUtil;
	 
	 @Value("${pokus.url}")
	 private String pokusUrl;

	@Autowired
	MetricAspect metricAspect;
	 
	 private static final Logger logger = LoggerFactory.getLogger(PokusExperimentExecutor.class);
	 
	 public PokusExperimentResponse getPokusExperimentResponse(PokusExperimentRequest pokusExperimentRequest, 
			 Map<String, String> headers, String mmtAuth) throws ClientGatewayException {
		 PokusExperimentResponse response=null;
		 long start = new Date().getTime();
		 try {
			 Map<String, String> headerMap = new HashMap<String, String>();
			 if (StringUtils.isNotEmpty(mmtAuth))
				 headerMap.put("mmtAuth", mmtAuth);
			 if (MapUtils.isNotEmpty(headers)) {
				 if (StringUtils.isNotBlank(headers.get("region")))
					 headerMap.put("region", headers.get("region"));
				 if (StringUtils.isNotBlank(headers.get(USER_COUNTRY)))
					 headerMap.put(USER_COUNTRY, headers.get(USER_COUNTRY));
				 if (StringUtils.isNotBlank(headers.get(ENTITY_NAME)))
					 headerMap.put(ENTITY_NAME, headers.get(ENTITY_NAME));
				 if (StringUtils.isNotBlank(headers.get("language")))
					 headerMap.put("language", headers.get("language"));
				 if (StringUtils.isNotBlank(headers.get("currency")))
					 headerMap.put("currency", headers.get("currency"));
			 }
			 headerMap.put("Accept-Encoding", "gzip");
			 String request = objectMapperUtil.getJsonFromObject(pokusExperimentRequest, DependencyLayer.CLIENTGATEWAY);
			 logger.debug(request);
			 String result = restConnectorUtil.performPokusExperimentPost(request, headerMap, pokusUrl);
			 response = objectMapperUtil.getObjectFromJson(result, PokusExperimentResponse.class,
					 DependencyLayer.POKUS);
			 if (pokusExperimentRequest != null && pokusExperimentRequest.getContext() != null && PAGE_CONTEXT_REVIEW.equalsIgnoreCase(pokusExperimentRequest.getContext().getPageName())) {
				 logger.warn("PokusExperiment request: {}, Experiment Variant Keys: {}", request, getVariantKeys(response));
			 }
			 if (response != null && response.getSuccess() != null && !response.getSuccess())
				 logger.error("Error in PokusExperiment response : {} " + result);
		 }finally {
			 metricAspect.addToTime(DependencyLayer.POKUS.name(), "experimentData", new Date().getTime() - start);
		 }
		 return response;
	 }

	private String getVariantKeys(PokusExperimentResponse pokusExperimentResponse) {
		if (pokusExperimentResponse != null && MapUtils.isNotEmpty(pokusExperimentResponse.getPerLobMap())
				&& pokusExperimentResponse.getPerLobMap().containsKey("HOTEL")
				&& StringUtils.isNotEmpty(pokusExperimentResponse.getPerLobMap().get("HOTEL").getVariantKey())) {
			return pokusExperimentResponse.getPerLobMap().get("HOTEL").getVariantKey();
		}
		return "";
	}
}
