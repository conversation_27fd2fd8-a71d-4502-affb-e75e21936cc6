package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.request.CreateQuoteRequest;
import com.mmt.hotels.clientgateway.request.UpdateAffiliateFeeRequest;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.affiliate.GetQuoteRequest;
import com.mmt.hotels.model.affiliate.GetQuoteResponse;
import com.mmt.hotels.model.affiliate.UpdateAffiliateFeeResponse;
import com.mmt.hotels.model.request.CreateQuoteRequestBody;
import com.mmt.hotels.model.request.UpdateAffiliateFeeReqBody;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

@Component
public class AffiliateExecutor {

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Value("${affiliate.update.fee.url}")
    private String updateFeeUrl;

    @Value("${affiliate.create.quote.url}")
    private String createQuoteUrl;

    @Value("${affiliate.get.quote.data.url}")
    private String getMergedQuoteDataUrl;

    private static final Logger logger = LoggerFactory.getLogger(AffiliateExecutor.class);

    public UpdateAffiliateFeeResponse getUpdatedAffiliateFeeResponse(UpdateAffiliateFeeRequest updateAffiliateFeeRequest, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException, UnsupportedEncodingException {

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcInterceptor", "update-fee");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));
        //updateFeeUrl = RestURLHelper.getDestinationUrl(updateFeeUrl);
        String url = Utility.getcompleteURL(updateFeeUrl, parameterMap, updateAffiliateFeeRequest.getCorrelationKey());
        String request = objectMapperUtil.getJsonFromObject(updateAffiliateFeeRequest, DependencyLayer.CLIENTGATEWAY);
        logger.debug("Update fee request :: {}", request);
        logger.debug("Update fee url :: {}", url);
        String result = restConnectorUtil.performAffiliateFeeUpdatePost(request, headerMap, url);
        logger.debug("Update fee response :: {}", result);
        UpdateAffiliateFeeResponse updateAffiliateFeeResponse = objectMapperUtil.getObjectFromJson(result, UpdateAffiliateFeeResponse.class, DependencyLayer.ORCHESTRATOR);

        if (updateAffiliateFeeResponse != null && updateAffiliateFeeResponse.getResponseErrors() != null
                && CollectionUtils.isNotEmpty(updateAffiliateFeeResponse.getResponseErrors().getErrorList())) {
            throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
                    updateAffiliateFeeResponse.getResponseErrors().getErrorList().get(0).getErrorCode(),
                    updateAffiliateFeeResponse.getResponseErrors().getErrorList().get(0).getErrorMessage());
        }
        return updateAffiliateFeeResponse;
    }

    public com.mmt.hotels.model.affiliate.CreateQuoteResponse getCreateQuoteResponse(CreateQuoteRequest createQuoteRequest, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException, UnsupportedEncodingException {

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcInterceptor", "update-fee");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));
        //createQuoteUrl = RestURLHelper.getDestinationUrl(createQuoteUrl);
        String url = Utility.getcompleteURL(createQuoteUrl, parameterMap, createQuoteRequest.getCorrelationKey());
        String request = objectMapperUtil.getJsonFromObject(createQuoteRequest, DependencyLayer.CLIENTGATEWAY);
        logger.debug("Create quote request :: {}", request);
        logger.debug("Create quote url :: {}", url);
        String result = restConnectorUtil.performAffiliateCreateQuotePost(request, headerMap, url);
        logger.debug("Create quote response :: {}", result);
        com.mmt.hotels.model.affiliate.CreateQuoteResponse createQuoteResponse = objectMapperUtil.getObjectFromJson(result, com.mmt.hotels.model.affiliate.CreateQuoteResponse.class, DependencyLayer.ORCHESTRATOR);

        if (createQuoteResponse != null && createQuoteResponse.getResponseErrors() != null
                && CollectionUtils.isNotEmpty(createQuoteResponse.getResponseErrors().getErrorList())) {
            throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
                    createQuoteResponse.getResponseErrors().getErrorList().get(0).getErrorCode(),
                    createQuoteResponse.getResponseErrors().getErrorList().get(0).getErrorMessage());
        }
        return createQuoteResponse;
    }

    public GetQuoteResponse getPersistedQuoteDataMerged(GetQuoteRequest getQuoteRequest, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException, UnsupportedEncodingException {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcInterceptor", "update-fee");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));
        //getMergedQuoteDataUrl = RestURLHelper.getDestinationUrl(getMergedQuoteDataUrl);
        String formattedUrl = MessageFormat.format(getMergedQuoteDataUrl, getQuoteRequest.getCorrelationKey());
        String url = Utility.getcompleteURL(formattedUrl, parameterMap, getQuoteRequest.getCorrelationKey());
        String request = objectMapperUtil.getJsonFromObject(getQuoteRequest, DependencyLayer.CLIENTGATEWAY);
        logger.debug("Get merged quote url :: {}", url);
        logger.warn("Get merged quote request :: {}", request);
        String result = restConnectorUtil.performGetQuoteDataMerged(request, headerMap, url);
        logger.warn("Get quote response :: {}", result);
        GetQuoteResponse getQuoteResponseHES = objectMapperUtil.getObjectFromJson(result, GetQuoteResponse.class, DependencyLayer.ORCHESTRATOR);

        if (getQuoteResponseHES != null && getQuoteResponseHES.getResponseErrors() != null
                && CollectionUtils.isNotEmpty(getQuoteResponseHES.getResponseErrors().getErrorList())) {
            throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
                    getQuoteResponseHES.getResponseErrors().getErrorList().get(0).getErrorCode(),
                    getQuoteResponseHES.getResponseErrors().getErrorList().get(0).getErrorMessage());
        }
        if (getQuoteResponseHES != null && getQuoteResponseHES.getAvailResponse() != null
                && getQuoteResponseHES.getAvailResponse().getResponseErrors() != null
                && CollectionUtils.isNotEmpty(getQuoteResponseHES.getAvailResponse().getResponseErrors().getErrorList())) {
            throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
                    getQuoteResponseHES.getAvailResponse().getResponseErrors().getErrorList().get(0).getErrorCode(),
                    getQuoteResponseHES.getAvailResponse().getResponseErrors().getErrorList().get(0).getErrorMessage());
        }
        return getQuoteResponseHES;
    }

	public String getUpdatedAffiliateFeeOldResponse(UpdateAffiliateFeeReqBody updateAffiliateFeeReqBody,
			Map<String, String[]> parameterMap, Map<String, String> headerMap, String correlationKey) throws ClientGatewayException, UnsupportedEncodingException{
		Map<String, String> headers = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));
        String url = Utility.getcompleteURL(updateFeeUrl, parameterMap, correlationKey);
        String request = objectMapperUtil.getJsonFromObject(updateAffiliateFeeReqBody, DependencyLayer.CLIENTGATEWAY);
        logger.debug("Update fee request :: {}", request);
        logger.debug("Update fee url :: {}", url);
        return restConnectorUtil.performAffiliateFeeUpdatePost(request, headerMap, url);
	}

	public String getCreateQuoteResponseOld(CreateQuoteRequestBody createQuoteRequestBody,
			Map<String, String[]> parameterMap, Map<String, String> headers, String correlationKey) throws ClientGatewayException, UnsupportedEncodingException{
		Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));
        //createQuoteUrl = RestURLHelper.getDestinationUrl(createQuoteUrl);
        String url = Utility.getcompleteURL(createQuoteUrl, parameterMap, correlationKey);
        String request = objectMapperUtil.getJsonFromObject(createQuoteRequestBody, DependencyLayer.CLIENTGATEWAY);
        logger.debug("Create quote request :: {}", request);
        logger.debug("Create quote url :: {}", url);
        return restConnectorUtil.performAffiliateCreateQuotePost(request, headerMap, url);
	}

}
