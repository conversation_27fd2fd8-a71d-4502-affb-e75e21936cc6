package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.pojo.request.detail.mob.HostCallingInitiateRequestBody;
import com.mmt.hotels.pojo.response.detail.HostCallingInitiateResponse;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.response.detail.HotelDetailWrapperResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class HostCallingExecutor {

    private static final Logger logger = LoggerFactory.getLogger(HostCallingExecutor.class);

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Autowired
    private MetricAspect metricAspect;

    @Value("${host.calling.initiate.url}")
    private String hostCallingInitiateUrl;

    public HostCallingInitiateResponse getEntityServiceResponse(HostCallingInitiateRequestBody hostCallingRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        String result = getHostCallingResponseFromEntityService(hostCallingRequest, parameterMap, httpHeaderMap);
        try {
            return objectMapperUtil.getObjectFromJson(result, HostCallingInitiateResponse.class, DependencyLayer.CLIENTGATEWAY);
        } catch (Exception e) {
            logger.error("Error parsing HES response to HostCallingInitiateResponse", e);
            throw new ClientGatewayException(DependencyLayer.CLIENTGATEWAY, 
                com.mmt.hotels.clientgateway.enums.ErrorType.MARSHALLING, 
                "500", 
                "Failed to parse entity service response");
        }
    }

    private String getHostCallingResponseFromEntityService(HostCallingInitiateRequestBody hostCallingRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {
            Map<String, String> headerMap = getHeaderMap(httpHeaderMap);
            String request = objectMapperUtil.getJsonFromObject(hostCallingRequest, DependencyLayer.CLIENTGATEWAY);
            String correlationKey = MDC.get("correlationKey") != null ? MDC.get("correlationKey") : "default";
            String relativeUrl = Utility.getcompleteURL(hostCallingInitiateUrl, parameterMap, correlationKey);
            
            logger.debug("HostCalling - Calling hotels-entity-service URL: {}", relativeUrl);
            logger.debug("HostCalling - Request: {}", request);

            return restConnectorUtil.performHostCallingPost(request, headerMap, relativeUrl);
        } catch (Exception e) {
            logger.error("Error calling hotels-entity-service for host calling", e);
            throw new ClientGatewayException(DependencyLayer.CLIENTGATEWAY, 
                com.mmt.hotels.clientgateway.enums.ErrorType.DOWNSTREAM, 
                "500", 
                "Failed to call entity service: " + e.getMessage());
        }
    }

    private Map<String, String> getHeaderMap(Map<String, String> httpHeaderMap) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        
        if (StringUtils.isNotEmpty(httpHeaderMap.get("mmt-auth"))) {
            headerMap.put("mmt-auth", httpHeaderMap.get("mmt-auth"));
        }

        String akmaiHeader = null;
        // Check for various case variations of akamai-user-ip header
        for (Map.Entry<String, String> entry : httpHeaderMap.entrySet()) {
            if ("akamai-user-ip".equalsIgnoreCase(entry.getKey())) {
                akmaiHeader = entry.getValue();
                break;
            }
        }

        if (StringUtils.isNotEmpty(akmaiHeader)) {
            headerMap.put("akamai-user-ip", akmaiHeader);
        }
        
        return headerMap;
    }
} 