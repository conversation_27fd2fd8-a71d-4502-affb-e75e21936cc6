package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.response.UserSessionData;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.CallBackDataRequest;
import com.mmt.hotels.model.response.searchwrapper.ListingPagePersonalizationResponsBO;
import com.mmt.hotels.model.response.txn.PersistanceMultiRoomResponseEntity;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

@Component
public class HESRestExecutor {

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Value("${txndata.get.url}")
    private String txnDataGetUrl;

    @Value("${callback.data.update.url}")
    private String callbackDataUrl;

    private static final Logger LOGGER = LoggerFactory.getLogger(HESRestExecutor.class);

    public PersistanceMultiRoomResponseEntity getTxnData(String correlationKey, Map<String, String[]> parameterMap, String txnKey) throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");

        String relativeUrl = Utility.getcompleteURL(MessageFormat.format(txnDataGetUrl, txnKey), parameterMap, correlationKey);
        LOGGER.debug(relativeUrl);

        String result = restConnectorUtil.performThankYouGet(headerMap, relativeUrl);
        PersistanceMultiRoomResponseEntity txnDataResponse = objectMapperUtil.getObjectFromJson(result, PersistanceMultiRoomResponseEntity.class, DependencyLayer.ORCHESTRATOR);

        if (txnDataResponse != null && txnDataResponse.getResponseErrors() != null
                && CollectionUtils.isNotEmpty(txnDataResponse.getResponseErrors().getErrorList())) {
        	LOGGER.error("Error from downstream in getTxnData " + txnDataResponse.getResponseErrors().getErrorList().get(0).getErrorCode());
            throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
                    txnDataResponse.getResponseErrors().getErrorList().get(0).getErrorCode(),
                    txnDataResponse.getResponseErrors().getErrorList().get(0).getErrorMessage());
        }
        return txnDataResponse;
    }

    public UserSessionData updateRequestCallBackData(String correlationKey, String uuid) throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");

        CallBackDataRequest callBackDataRequest = buildCallbackDataRequest(uuid);
        String request = objectMapperUtil.getJsonFromObject(callBackDataRequest, DependencyLayer.CLIENTGATEWAY);
        LOGGER.debug(callbackDataUrl);

        String result = restConnectorUtil.performUpdateRequestCallbackDataPost(request, headerMap, callbackDataUrl);
        UserSessionData userSessionData = objectMapperUtil.getObjectFromJson(result, UserSessionData.class, DependencyLayer.ORCHESTRATOR);

        if(userSessionData==null) {
            LOGGER.error("No userSessionData received from Hes");
        }

        if (userSessionData != null && userSessionData.getResponseErrors() != null
                && CollectionUtils.isNotEmpty(userSessionData.getResponseErrors().getErrorList())) {
            LOGGER.error("Error from downstream in updateRequestToCallBackData " + userSessionData.getResponseErrors().getErrorList().get(0).getErrorCode());
        }
        return userSessionData;
    }

    private CallBackDataRequest buildCallbackDataRequest(String uuid) {
        CallBackDataRequest callBackDataRequest = new CallBackDataRequest();
        callBackDataRequest.setUuid(uuid);
        return callBackDataRequest;
    }

}
