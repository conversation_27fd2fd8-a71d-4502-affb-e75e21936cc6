package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.HeaderConstants;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.thirdparty.request.PersonalDetails;
import com.mmt.hotels.clientgateway.thirdparty.request.UpdateUserDetailRequest;
import com.mmt.hotels.clientgateway.thirdparty.request.UpdateUserValueDetail;
import com.mmt.hotels.clientgateway.thirdparty.request.UserServiceRequestIdentifier;
import com.mmt.hotels.clientgateway.thirdparty.response.*;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.MaskingUtil;
import com.mmt.hotels.pojo.response.userservice.UserDetailsDTO;
import com.mmt.propertymanager.config.PropertyManager;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

@Component
public class UserServiceExecutor {

	@Value("${consul.enable}")
	private boolean consulFlag;

	@Autowired
	CommonConfigConsul commonConfigConsul;


	private static final Logger logger = LoggerFactory.getLogger(UserServiceExecutor.class);
	
	@Autowired
	ObjectMapperUtil objectMapperUtil;

	@Autowired
	private RestConnectorUtil restConnectorUtil;
	
	@Autowired
	private CommonConfigHelper commonConfigHelper;
	 
	@Value("${user.service.url}")
	private String userServiceUrl;

	@Value("${user.service.guest.url}")
	private String userServiceGuestUserUrl;

	@Value("${user.service.update.details.url}")
	private String userServiceUpdateDetailsUrl;
	
	private static final String AUTHORIZATION_KEY = "authorization";
	
	private static final String USER_IDENTIFIER_KEY = "user-identifier";
	
	private static final String SITE_DOMAIN="site-domain";
	
	private static final String REQUEST_TRACKER = "X-Request-Tracker";
	
	private static final String CHANNEL_FKPWA = "FKPWA";

	private List<String> userServiceServers;

	@Autowired
	private PropertyManager propertyManager;

	private String commonUserServiceAT;

	@Autowired
	MetricAspect metricAspect;


	@PostConstruct
	private void init() {

		if(consulFlag){
			commonUserServiceAT = "UkDTHwkONmybDBh";
			userServiceServers = commonConfigConsul.getUserServiceServers();
			logger.debug("Fetching values from commonConfig consul");
		}
		else {
			CommonConfig prop = propertyManager.getProperty("commonConfig", CommonConfig.class);
			prop.addPropertyChangeListener("userServiceServers", event -> {
				userServiceServers = prop.userServiceServers();
			});
			prop.addPropertyChangeListener("commonUserServiceAuthToken", event -> {
				commonUserServiceAT = prop.commonUserServiceAuthToken();
			});
			userServiceServers = prop.userServiceServers();
			commonUserServiceAT = prop.commonUserServiceAuthToken();
		}
	}

	public UserServiceResponse getUserServiceResponse(String mmtAuth,String emailId, String mobileNo,String channel,String correlationKey, String idContext,String siteDomain, String uuid, Map<String, String> httpHeaderMap) throws ClientGatewayException {
		long start = new Date().getTime();
		UserServiceResponse response=null;
		try {
			Map<String, String> headerMap = createUserServiceHeaderMap(mmtAuth, emailId, mobileNo, idContext, siteDomain, uuid, httpHeaderMap);
			headerMap.put(REQUEST_TRACKER, correlationKey);

			logger.warn("User service headers: {}", MaskingUtil.maskSensitiveDataAndLog(headerMap));

			String request = null;
			if (CHANNEL_FKPWA.equals(channel)) {
				request = commonConfigHelper.getUserAndTravellerInfoFKReqBody();
			} else {
				request = commonConfigHelper.getUserAndTravellerInfoReqBody();
			}
			boolean isAnyUserIdentifierPresent = CollectionUtils.isNotEmpty(Stream.of(mmtAuth, emailId, mobileNo, uuid).filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
			if(isAnyUserIdentifierPresent) {
				logger.debug("Hitting user service.");
				String result = restConnectorUtil.performUserServicePost(request, headerMap, userServiceUrl);
				logger.debug(result);
				response = objectMapperUtil.getObjectFromJson(result, UserServiceResponse.class,
						DependencyLayer.USERSERVICE);
			} else {
				//When all the user identifier fields are empty not calling the user service. Initializing the response object to bifurcate it from downstream response errors
				logger.debug("Not hitting user service since no user identifier present in the request.");
				response = new UserServiceResponse();
			}
			if (response == null)
				throw new ErrorResponseFromDownstreamException(DependencyLayer.USERSERVICE, ErrorType.DOWNSTREAM);

			// Check if user is authenticated.
			if (response.getResult() != null && response.getResult().getExtendedUser() != null) {
				logger.warn("User details retrieved for user with mmtAuth: {}", mmtAuth);

				if (Constants.PROFILE_CORPORATE.equalsIgnoreCase(response.getResult().getExtendedUser().getProfileType()))
					MDC.put(MDCHelper.MDCKeys.IDCONTEXT.getStringValue(), "CORP");
			} else {
				logger.error("User with mmtAuth: {} is not authenticated", mmtAuth);
			}
		}finally {
			metricAspect.addToTime(DependencyLayer.USERSERVICE.name(), "userDetails", new Date().getTime() - start);
		}
		 return response;
	}

	private Map<String, String> createUserServiceHeaderMap(String mmtAuth, String emailId, String mobileNo, String idContext,String siteDomain, String uuid, Map<String, String> httpHeaderMap) throws JsonParseException {
		String apiName = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Accept", "application/json; charset=UTF-8");
		headerMap.put("Content-Type", "application/json");
		UserServiceRequestIdentifier userIdentifier = new UserServiceRequestIdentifier();
		if (StringUtils.isNotBlank(mmtAuth)) {
			userIdentifier.setType("auth");
			userIdentifier.setValue(mmtAuth);
		} else if (StringUtils.isNotBlank(uuid)) {
			userIdentifier.setType("uuid");
			userIdentifier.setValue(uuid);
			if (httpHeaderMap.containsKey("idcontext") || OFFER_DETAILS_API.equalsIgnoreCase(apiName)) {
				userIdentifier.setProfileType(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(httpHeaderMap.get("idcontext")) ? "1" : "0");
			}
		} else if (StringUtils.isNotBlank(emailId)) {
			userIdentifier.setType("login-id");
			userIdentifier.setValue(emailId);
			userIdentifier.setProfileType(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext) ? "1" : "0");
		} else if (StringUtils.isNotBlank(mobileNo)) {
			userIdentifier.setType("login-id");
			userIdentifier.setValue(mobileNo);
			userIdentifier.setProfileType("0");
		}

		String userIdentifierKey  = objectMapperUtil.getJsonFromObject(userIdentifier, DependencyLayer.CLIENTGATEWAY);

        headerMap.put(AUTHORIZATION_KEY, commonConfigHelper.getCommonUserServiceAuthToken());
        headerMap.put(USER_IDENTIFIER_KEY, userIdentifierKey);
        headerMap.put(SITE_DOMAIN,StringUtils.isNotBlank(siteDomain)?siteDomain: "IN");
        if (MapUtils.isNotEmpty(httpHeaderMap)) {
			if (StringUtils.isNotBlank(httpHeaderMap.get("region")))
				headerMap.put("region", httpHeaderMap.get("region"));
			if (StringUtils.isNotBlank(httpHeaderMap.get(USER_COUNTRY)))
				headerMap.put(USER_COUNTRY, httpHeaderMap.get(USER_COUNTRY));
			if (StringUtils.isNotBlank(httpHeaderMap.get(ENTITY_NAME)))
				headerMap.put(ENTITY_NAME, httpHeaderMap.get(ENTITY_NAME));
			if (StringUtils.isNotBlank(httpHeaderMap.get("language")))
				headerMap.put("language", httpHeaderMap.get("language"));
			if (StringUtils.isNotBlank(httpHeaderMap.get("currency")))
				headerMap.put("currency", httpHeaderMap.get("currency"));
		}
		headerMap.put("org", "MMT");
		return headerMap;
	}

	public UserServiceResponse createGuestUser(String firstName, String lastName, String emailId, int retries, String correlationKey, String siteDomain, Map<String, String> headerMapRequest) throws ClientGatewayException {
		UserServiceResponse response = null;
		Map<String, String> headerMap = new HashMap<String, String>();

		headerMap.put(AUTHORIZATION_KEY, commonUserServiceAT);
		headerMap.put(SITE_DOMAIN,StringUtils.isNotBlank(siteDomain)? siteDomain:"IN");
		headerMap.put(REQUEST_TRACKER,StringUtils.isBlank(correlationKey) ? UUID.randomUUID().toString(): correlationKey);
		headerMap.put("Accept", "application/json; charset=UTF-8");
		headerMap.put("Content-Type", "application/json");
		headerMap.put("org", "MMT");
		if (MapUtils.isNotEmpty(headerMapRequest)) {
			if (StringUtils.isNotBlank(headerMapRequest.get("region")))
				headerMap.put("region", headerMapRequest.get("region"));
			if (StringUtils.isNotBlank(headerMapRequest.get(USER_COUNTRY)))
				headerMap.put(USER_COUNTRY, headerMapRequest.get(USER_COUNTRY));
			if (StringUtils.isNotBlank(headerMapRequest.get(ENTITY_NAME)))
				headerMap.put(ENTITY_NAME, headerMapRequest.get(ENTITY_NAME));
			if (StringUtils.isNotBlank(headerMapRequest.get("language")))
				headerMap.put("language", headerMapRequest.get("language"));
			if (StringUtils.isNotBlank(headerMapRequest.get("currency")))
				headerMap.put("currency", headerMapRequest.get("currency"));
		}

		CreateUserRequest userRequest = buildCreateUserRequest(firstName,lastName,emailId);
		
		String jsonReq = objectMapperUtil.getJsonFromObject(userRequest, DependencyLayer.CLIENTGATEWAY);
		logger.warn("Create user request {}", MaskingUtil.maskSensitiveDataAndLog(jsonReq));
		String result = restConnectorUtil.performUserServicePost(jsonReq,headerMap, userServiceGuestUserUrl);
		logger.warn("Create user response {}", MaskingUtil.maskSensitiveDataAndLog(result));
		response = objectMapperUtil.getObjectFromJson(result, UserServiceResponse.class,
				DependencyLayer.USERSERVICE);
		if (response == null )
			throw new ErrorResponseFromDownstreamException(DependencyLayer.USERSERVICE, ErrorType.DOWNSTREAM);

		return response;

	}

	//only nationality need to update so will update that only no other extra parameters should passed as it will impact user Account
	public void updateUserDetails(String nationality, String uuid, String siteDomain, String correlationKey, String idContext, Map<String, String> headers){

		try {
			Map<String, String> headerMap = new HashMap<String, String>();

			UserServiceRequestIdentifier userIdentifier = new UserServiceRequestIdentifier();
			if (StringUtils.isNotEmpty(uuid) && StringUtils.isNotEmpty(nationality)) {
				userIdentifier.setType(HeaderConstants.UUID);
				userIdentifier.setValue(uuid);
				userIdentifier.setProfileType(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext) ? ID_CONTEXT_BUSINESS : ID_CONTEXT_PERSONAL);

				String userIdentifierKey = objectMapperUtil.getJsonFromObject(userIdentifier, DependencyLayer.CLIENTGATEWAY);

				headerMap.put(USER_IDENTIFIER_KEY, userIdentifierKey);
				headerMap.put(AUTHORIZATION_KEY, commonUserServiceAT);
				headerMap.put(SITE_DOMAIN, StringUtils.isNotBlank(siteDomain) ? siteDomain : "IN");
				headerMap.put(REQUEST_TRACKER, StringUtils.isBlank(correlationKey) ? UUID.randomUUID().toString() : correlationKey);
				headerMap.put(HEADER_ACCEPT_TYPE, HEADER_CONTENT_APPLICATION_JSON_UTF_8);
				headerMap.put(HEADER_CONTENT_TYPE, HEADER_CONTENT_APPLICATION_JSON);
				headerMap.put(ORG, BRAND_MMT);
				if (headers.containsKey(ENTITY_NAME)) {
					headerMap.put(ENTITY_NAME, headers.get(ENTITY_NAME));
				}
				if (headers.containsKey(USER_COUNTRY)) {
					headerMap.put(USER_COUNTRY, headers.get(USER_COUNTRY));
				}
				UpdateUserDetailRequest userDetailsUpdateRequest = updateUserDetailsRequest(nationality);
				String jsonReq = objectMapperUtil.getJsonFromObject(userDetailsUpdateRequest, DependencyLayer.CLIENTGATEWAY);
				logger.debug("Update user request {}", jsonReq);
				String result = restConnectorUtil.performUserServicePost(jsonReq, headerMap, userServiceUpdateDetailsUrl);
				logger.debug("Update user response {}", result);
			}else{
				logger.warn("Either uuid is blank or nationality. So not updating user details.");
			}
		}catch (Exception e){
			logger.error("Error while updating details of users", e);
		}
	}

	private UpdateUserDetailRequest updateUserDetailsRequest(String nationality){
		UpdateUserDetailRequest userUpdateRequest = new UpdateUserDetailRequest();
		List<List<com.mmt.hotels.clientgateway.thirdparty.request.Query>> listOfQueryList = new ArrayList<>();
		List<com.mmt.hotels.clientgateway.thirdparty.request.Query> queryList = new ArrayList<>();
		listOfQueryList.add(queryList);
		userUpdateRequest.setQuery(listOfQueryList);
		com.mmt.hotels.clientgateway.thirdparty.request.Query query = new com.mmt.hotels.clientgateway.thirdparty.request.Query();
		queryList.add(query);

		query.setName(EXTENDED_USER);
		UpdateUserValueDetail value = new UpdateUserValueDetail();
		query.setValues(value);

		value.setPersonalDetails(new PersonalDetails());
		value.getPersonalDetails().setNationality(nationality);

		return userUpdateRequest;
	}


	private CreateUserRequest buildCreateUserRequest(String firstName, String lastName, String emailId){
		CreateUserRequest userRequest = new CreateUserRequest();
		List<List<Query>> listOfQueryList = new ArrayList<>();
		List<Query> queryList = new ArrayList<>();
		listOfQueryList.add(queryList);
		userRequest.setQuery(listOfQueryList);
		Query query = new Query();
		queryList.add(query);

		query.setName("extendedUser");
		CreateUserValueDetail value = new CreateUserValueDetail();
		query.setValues(value);
		value.setPersonalDetails(new PersonalUserDetail());
		value.getPersonalDetails().setName(new UserNameDetail());
		value.getPersonalDetails().getName().setFirstName(firstName);
		value.getPersonalDetails().getName().setLastName(lastName);

		LoginInfo loginInfo = new LoginInfo();
		loginInfo.setPassword("password");
		loginInfo.setPrimary(true);

		value.setLoginInfoList(new ArrayList<>());
		value.getLoginInfoList().add(loginInfo);

		value.setPrimaryEmailId(emailId);
		value.setProfileType("PERSONAL");

		return userRequest;
	}


	public UserDetailsDTO getUserDetails(String bookingDevice, Map<String, String> headers, String correlationKey,
			String siteDomain) {
		UserDetailsDTO userDetailsDTO = null;
		String authTocken = null;
		try {
			if ("ANDROID".equalsIgnoreCase(bookingDevice)) {
				String androidAuth = headers.get("backup_auth");
				if (!StringUtils.isEmpty(androidAuth) && androidAuth.indexOf("mmtAuth") > -1) {
					authTocken = androidAuth.substring(androidAuth.indexOf("mmtAuth") + 9, androidAuth.length() - 1);
				}

			} else {
				authTocken = headers.get("auth");
			}
			if (!StringUtils.isBlank(authTocken)) {
				UserServiceResponse userServiceResponse = getUserServiceResponse(authTocken, null, null, null, 0,
						correlationKey, null, siteDomain, headers);
				if (userServiceResponse != null && userServiceResponse.getResult() != null) {
					ExtendedUser extendedUser = userServiceResponse.getResult().getExtendedUser();
					if (extendedUser != null) {
						userDetailsDTO = parseUserServiceResponse(extendedUser);
					}
				}
			}
		} catch (Exception e) {
			logger.error("Exception while populating user details from auth", e);
		}
		return userDetailsDTO;
	}

	public UserServiceResponse getUserServiceResponse(String mmtAuth, String emailId, String mobileNo, String channel,
			int retries, String correlationKey, String idContext, String siteDomain, Map<String, String> httpHeaderMap)
			throws Exception {
		UserServiceResponse response = null;
		try {
			Map<String, String> headerMap = createUserServiceHeaderMap(mmtAuth, emailId, mobileNo, idContext,
					siteDomain,null, httpHeaderMap);
			headerMap.put(REQUEST_TRACKER,
					StringUtils.isBlank(correlationKey) ? UUID.randomUUID().toString() : correlationKey);
			String reqBody = null;
			if (CHANNEL_FKPWA.equals(channel)) {
				reqBody = commonConfigHelper.getCommonUserServiceFKRequestBody();
			} else {
				reqBody = commonConfigHelper.getCommonUserServiceRequestBody();
			}
			logger.debug(reqBody);
			String result = restConnectorUtil.performUserServicePost(reqBody, headerMap, userServiceUrl);
			logger.debug(result);
			response = objectMapperUtil.getObjectFromJson(result, UserServiceResponse.class,
					DependencyLayer.USERSERVICE);
			return response;
		} catch (Exception e) {
			logger.error("Exception while getting user service response", e);
		}
		return response;

	}
	
	public UserDetailsDTO parseUserServiceResponse(ExtendedUser extendedUser) {
		UserDetailsDTO userDetailsDTO = new UserDetailsDTO();
		parseUserDetails(extendedUser, userDetailsDTO);
		return userDetailsDTO;
	}
	
	private void parseUserDetails(ExtendedUser extendedUser, UserDetailsDTO userDetailsDTO) {
		if (null == extendedUser) {
			return;
		}
		if (extendedUser.getPersonalDetails() != null && extendedUser.getPersonalDetails().getName() != null) {
			userDetailsDTO.setFirstname(extendedUser.getPersonalDetails().getName().getFirstName());
			userDetailsDTO.setLastname(extendedUser.getPersonalDetails().getName().getLastName());
		}
		userDetailsDTO.setUuid(extendedUser.getUuid());
		if (StringUtils.isNotBlank(extendedUser.getProfileId())) {
			userDetailsDTO.setProfileId(extendedUser.getProfileId()); /* Corp ID */
		}
		userDetailsDTO.setProfileType(extendedUser.getProfileType());
		userDetailsDTO.setSubProfileType(extendedUser.getAffiliateId());
		if (CollectionUtils.isNotEmpty(extendedUser.getLoginInfoList())) {
			for (UserLoginInfo loginInfo : extendedUser.getLoginInfoList()) {
				if ("MOBILE".equalsIgnoreCase(loginInfo.getLoginType())) {
					userDetailsDTO.setMobile(loginInfo.getLoginId());
					userDetailsDTO.setVerified(loginInfo.isVerified());
				} else if ("EMAIL".equalsIgnoreCase(loginInfo.getLoginType())) {
					userDetailsDTO.setEmail(loginInfo.getLoginId());
				}
			}
		}
	}

}
