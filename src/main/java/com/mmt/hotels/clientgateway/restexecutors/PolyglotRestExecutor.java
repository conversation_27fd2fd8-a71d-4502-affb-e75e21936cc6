package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.model.polyglot.PolyglotTranslation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class PolyglotRestExecutor {

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Value("${polyglot.url}")
    private String polyglotUrl;

    @Value("${polyglot.mobgen.url}")
    private String polyglotMobGenUrl;

    private static final Logger LOGGER = LoggerFactory.getLogger(PolyglotRestExecutor.class);

    public PolyglotTranslation getPolyglotTranslation() throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");

        String result = restConnectorUtil.fetchTranslatedData(headerMap, polyglotUrl);
        PolyglotTranslation polyglotTranslation = objectMapperUtil.getObjectFromJsonWithIgnoreUnknown(result, PolyglotTranslation.class, DependencyLayer.POLYGLOT);
        return polyglotTranslation;
    }

    public PolyglotTranslation getMobGenPolyglotTranslation() throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");

        LOGGER.debug("Getting Mob gen data from polyglot");
        String result = restConnectorUtil.fetchTranslatedData(headerMap, polyglotMobGenUrl);
        PolyglotTranslation polyglotTranslation = objectMapperUtil.getObjectFromJsonWithIgnoreUnknown(result, PolyglotTranslation.class, DependencyLayer.POLYGLOT);
        return polyglotTranslation;
    }

}