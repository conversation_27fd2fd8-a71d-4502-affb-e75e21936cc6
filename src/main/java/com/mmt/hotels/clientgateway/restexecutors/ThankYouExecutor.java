package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.request.ThankYouRequest;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.txn.PersistanceMultiRoomResponseEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

@Component
public class ThankYouExecutor {

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Value("${txndata.get.url}")
    private String txnDataGetUrl;

    private static final Logger logger = LoggerFactory.getLogger(ThankYouExecutor.class);

    public PersistanceMultiRoomResponseEntity getThankYouReponse(ThankYouRequest thankYouRequest, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException, UnsupportedEncodingException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcInterceptor", "thankYou");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));
        String url = MessageFormat.format(txnDataGetUrl, thankYouRequest.getTxnKey());
        url = Utility.getcompleteURL(url, parameterMap, thankYouRequest.getCorrelationKey());

        String result = restConnectorUtil.performThankYouGet(headerMap, url);
        PersistanceMultiRoomResponseEntity txnDataResponse = objectMapperUtil.getObjectFromJson(result, PersistanceMultiRoomResponseEntity.class, DependencyLayer.ORCHESTRATOR);

        if (txnDataResponse != null && txnDataResponse.getResponseErrors() != null
                && CollectionUtils.isNotEmpty(txnDataResponse.getResponseErrors().getErrorList())) {
            throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
                    txnDataResponse.getResponseErrors().getErrorList().get(0).getErrorCode(),
                    txnDataResponse.getResponseErrors().getErrorList().get(0).getErrorMessage());
        }
        return txnDataResponse;
    }

}
