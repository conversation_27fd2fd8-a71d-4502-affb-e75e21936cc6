package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.FilterSearchMetaDataResponse;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.response.BaseResponse;
import com.mmt.hotels.model.response.searchwrapper.TreelsListingResponseBO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class TreelsListingExecutor {

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Value("${search.treels.url}")
    private String searchTreelsUrl;

    @Value("${treels.filter.count.url}")
    private String treelsFilterCountUrl;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Autowired
    MetricAspect metricAspect;

    private static final Logger logger = LoggerFactory.getLogger(TreelsListingExecutor.class);

    public BaseResponse search(
            SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {
        long start = new Date().getTime();
        try {
            Map<String, String> headerMap = Utility.getHeaderMap(headers);
            String request = objectMapperUtil.getJsonFromObject(searchWrapperInputRequest, DependencyLayer.CLIENTGATEWAY);
            //searchTreelsUrl = RestURLHelper.getDestinationUrl(searchTreelsUrl);
            String relativeUrl = Utility.getcompleteURL(searchTreelsUrl, parameterMap, searchWrapperInputRequest.getCorrelationKey());
            logger.debug("searchTreels request:",request);
            logger.debug("searchTreels url", relativeUrl);
            String result = restConnectorUtil.performSearchTreelsPost(request, headerMap, relativeUrl, MapUtils.isNotEmpty(searchWrapperInputRequest.getAppliedFilterMap()));
            TreelsListingResponseBO treelsListingResponseBO = objectMapperUtil.getObjectFromJson(result, TreelsListingResponseBO.class,
                    DependencyLayer.ORCHESTRATOR); // this will be changed later depending on structure of response
            if (treelsListingResponseBO != null && treelsListingResponseBO.getResponseErrors() != null
                    && CollectionUtils.isNotEmpty(treelsListingResponseBO.getResponseErrors().getErrorList())) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
                        treelsListingResponseBO.getResponseErrors().getErrorList().get(0).getErrorCode(),
                        treelsListingResponseBO.getResponseErrors().getErrorList().get(0).getErrorMessage());
            }
            return treelsListingResponseBO;
        } finally {
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "searchTreels", new Date().getTime() - start);
        }
    }

    public FilterSearchMetaDataResponse filterCount(SearchWrapperInputRequest treelsFilterRequest, Map<String, String> headers) throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));
        String request = objectMapperUtil.getJsonFromObject(treelsFilterRequest, DependencyLayer.CLIENTGATEWAY);
        logger.debug("Treels filter request to hes: {}", request);
        //treelsFilterCountUrl = RestURLHelper.getDestinationUrl(treelsFilterCountUrl);
        String result = restConnectorUtil.performTreelsFilterPost(request, headerMap, treelsFilterCountUrl);
        logger.debug("Treels filter response from hes: {}", result);
        return objectMapperUtil.getObjectFromJson(result, FilterSearchMetaDataResponse.class, DependencyLayer.ORCHESTRATOR);
    }
}
