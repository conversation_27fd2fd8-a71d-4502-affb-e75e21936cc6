package com.mmt.hotels.clientgateway.restexecutors;

import com.mmt.hotels.clientgateway.constants.ByPassUrls;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.request.ClaimGiftCardRequest;
import com.mmt.hotels.clientgateway.request.TotalPricingRequest;
import com.mmt.hotels.clientgateway.request.payLater.PayLaterEligibilityRequest;
import com.mmt.hotels.clientgateway.response.GiftCardsBottomSheetData;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.giftcard.GiftCardClaimResponse;
import com.mmt.hotels.model.request.FetchLocationsRequestBody;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.response.FetchLocationsResponseBody;
import com.mmt.hotels.model.response.PayLaterEligibilityResponse;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.pricing.TotalPricingResponse;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import com.mmt.model.HotelsRoomInfoResponseEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.LANGUAGE;
import static com.mmt.hotels.clientgateway.constants.Constants.ORG;
import static com.mmt.hotels.clientgateway.constants.Constants.OS;
import static com.mmt.hotels.clientgateway.constants.Constants.USER_AGENT;

@Component
public class AvailRoomsExecutor {

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Value("${avail.rooms.url}")
    private String availRoomsUrl;

    @Value("${claim.gift.url}")
    private String claimGiftCardUrl;
    
    @Value("${updated.price.occu.less.url}")
    private String updatedPriceOccuLessUrl;

    @Value("${paylater.eligibility.url}")
    private String payLaterEligibilityUrl;

    @Value("${fetch.locations.url}")
    private String fetchLocationsUrl;

    @Autowired
    private MetricAspect metricAspect;


    private static final Logger logger = LoggerFactory.getLogger(AvailRoomsExecutor.class);

    public RoomDetailsResponse availRooms(PriceByHotelsRequestBody availRoomsRequestBody, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
        	headerMap.put("mmt-auth", headers.get("mmt-auth"));
        String akmaiHeader = headers.get(Constants.HEADER_AKAMAI);
        if (StringUtils.isEmpty(akmaiHeader))
            akmaiHeader = headers.get(Constants.HEADER_AKAMAI.toLowerCase());

        if(StringUtils.isNotEmpty(akmaiHeader)){
            headerMap.put(Constants.HEADER_AKAMAI.toLowerCase(),akmaiHeader);
        }

        // for Quick-Checkout review page
        headerMap.put(OS,headers.get(OS));
        headerMap.put(ORG,headers.get(ORG));
        headerMap.put(LANGUAGE,headers.get(LANGUAGE));
        headerMap.put(USER_AGENT,headers.get(USER_AGENT));

        String request = objectMapperUtil.getJsonFromObject(availRoomsRequestBody, DependencyLayer.CLIENTGATEWAY);
        //availRoomsUrl = RestURLHelper.getDestinationUrl(availRoomsUrl);
        String relativeUrl = Utility.getcompleteURL(availRoomsUrl, parameterMap, availRoomsRequestBody.getCorrelationKey());
        logger.debug(request);
        logger.debug(relativeUrl);

        long startTime = new Date().getTime();
        String result = null;
        try {
            result = restConnectorUtil.performAvailRoomsPost(request, headerMap, relativeUrl);
        } catch (Exception ex) {
            throw ex;
        } finally {
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "availrooms/availPrice", new Date().getTime() - startTime);
        }

        logger.debug("Avail-room hes response :: {}", result);

        RoomDetailsResponse roomDetailsResponse = objectMapperUtil.getObjectFromJson(result, RoomDetailsResponse.class, DependencyLayer.ORCHESTRATOR);
        if (roomDetailsResponse != null &&  roomDetailsResponse.getResponseErrors() != null &&
                CollectionUtils.isNotEmpty(roomDetailsResponse.getResponseErrors().getErrorList() )) {
        	throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
        			roomDetailsResponse.getResponseErrors().getErrorList().get(0).getErrorCode(),
        			roomDetailsResponse.getResponseErrors().getErrorList().get(0).getErrorMessage());
        }
        return roomDetailsResponse;
    }

    public GiftCardClaimResponse claimGiftCard(ClaimGiftCardRequest claimGiftCardRequest, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));
        String akmaiHeader = headers.get(Constants.HEADER_AKAMAI);
        if (StringUtils.isEmpty(akmaiHeader))
            akmaiHeader = headers.get(Constants.HEADER_AKAMAI.toLowerCase());

        if(StringUtils.isNotEmpty(akmaiHeader)){
            headerMap.put(Constants.HEADER_AKAMAI.toLowerCase(),akmaiHeader);
        }

        headerMap.put(OS,headers.get(OS));
        headerMap.put(ORG,headers.get(ORG));
        headerMap.put(LANGUAGE,headers.get(LANGUAGE));
        headerMap.put(USER_AGENT,headers.get(USER_AGENT));

        String request = objectMapperUtil.getJsonFromObject(claimGiftCardRequest, DependencyLayer.CLIENTGATEWAY);
        logger.debug(request);
        logger.debug(claimGiftCardUrl);

        long startTime = new Date().getTime();
        String result = null;
        try {
            result = restConnectorUtil.performGiftCardPost(request, headerMap, claimGiftCardUrl);
        } catch (Exception ex) {
            ex.getMessage();
            throw ex;
        } finally {
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "giftCard", new Date().getTime() - startTime);
        }

        logger.debug("giftCard/claim hes response :: {}", result);

        return objectMapperUtil.getObjectFromJson(result, GiftCardClaimResponse.class, DependencyLayer.ORCHESTRATOR);
    }

    public String availRoomsOld(PriceByHotelsRequestBody availRoomsRequestBody, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));

        String request = objectMapperUtil.getJsonFromObject(availRoomsRequestBody, DependencyLayer.CLIENTGATEWAY);
        //availRoomsUrl = RestURLHelper.getDestinationUrl(availRoomsUrl);
        String relativeUrl = Utility.getcompleteURL(availRoomsUrl, parameterMap, availRoomsRequestBody.getCorrelationKey());
        logger.debug(request);
        logger.debug(relativeUrl);

        String response = restConnectorUtil.performAvailRoomsPost(request, headerMap, relativeUrl);
        RoomDetailsResponse responseObject  =objectMapperUtil.getObjectFromJson(response, RoomDetailsResponse.class, DependencyLayer.ORCHESTRATOR);
        if (null!=responseObject) {
            response = objectMapperUtil.getJsonFromObjectWithView(responseObject, DependencyLayer.ORCHESTRATOR, PIIView.External.class);
        } else {
            response = null;
        }
        return response;
    }
    
    public String updatedPriceOccuLessOld(PriceByHotelsRequestBody availRoomsRequestBody, Map<String, String[]> parameterMap, Map<String, String> headers) throws ClientGatewayException {

        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));

        String request = objectMapperUtil.getJsonFromObject(availRoomsRequestBody, DependencyLayer.CLIENTGATEWAY);
        String relativeUrl = Utility.getcompleteURL(updatedPriceOccuLessUrl, parameterMap, availRoomsRequestBody.getCorrelationKey());
        logger.debug(request);
        logger.debug(relativeUrl);

        return restConnectorUtil.performUpdatedPriceOccuLessPost(request, headerMap, relativeUrl);
    }

    public HotelsRoomInfoResponseEntity getRoomStaticDetails() {
        return null;
    }
    
	public TotalPricingResponse getTotalPricingDetails(TotalPricingRequest getTotalPriceRequest, Map<String, String[]> parameterMap, String correlationKey, Map<String,String> headers)
            throws ClientGatewayException {
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Content-Type", "application/json");
		headerMap.put("Accept-Encoding", "gzip");
		if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
			headerMap.put("mmt-auth", headers.get("mmt-auth"));

        String request = objectMapperUtil.getJsonFromObject(getTotalPriceRequest, DependencyLayer.CLIENTGATEWAY);
        String relativeUrl = Utility.getcompleteURL(ByPassUrls.DESTINATION_GET_TOTAL_PRICING_URL, parameterMap,correlationKey);
        logger.debug(request);
        logger.debug(relativeUrl);

        String result = restConnectorUtil.performTotalPricePost(request, headerMap, relativeUrl);
        TotalPricingResponse totalPricingRsp = objectMapperUtil.getObjectFromJson(result, TotalPricingResponse.class, DependencyLayer.ORCHESTRATOR);
        if (totalPricingRsp != null && totalPricingRsp.getResponseErrors() !=null &&
	    		CollectionUtils.isNotEmpty(totalPricingRsp.getResponseErrors().getErrorList())) {
	    	throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, 
	    			totalPricingRsp.getResponseErrors().getErrorList().get(0).getErrorCode(),
	    			totalPricingRsp.getResponseErrors().getErrorList().get(0).getErrorMessage());
	    }
		return totalPricingRsp;
	}

    public PayLaterEligibilityResponse fetchPayLaterEligibility(PayLaterEligibilityRequest payLaterEligibilityRequest, Map<String, String[]> parameterMap, String correlationKey, Map<String,String> headers)
            throws ClientGatewayException {
        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));

        String request = objectMapperUtil.getJsonFromObject(payLaterEligibilityRequest, DependencyLayer.CLIENTGATEWAY);
        String relativeUrl = Utility.getcompleteURL(payLaterEligibilityUrl, parameterMap,correlationKey);
        logger.debug(request);
        logger.debug(relativeUrl);

        String result = restConnectorUtil.checkPayLaterEligibilityPost(request, headerMap, relativeUrl);
        PayLaterEligibilityResponse resp = objectMapperUtil.getObjectFromJson(result, PayLaterEligibilityResponse.class, DependencyLayer.ORCHESTRATOR);
        if (resp != null && resp.getResponseErrors() !=null &&
                CollectionUtils.isNotEmpty(resp.getResponseErrors().getErrorList())) {
            throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
                    resp.getResponseErrors().getErrorList().get(0).getErrorCode(),
                    resp.getResponseErrors().getErrorList().get(0).getErrorMessage());
        }
        return resp;
    }

    public FetchLocationsResponseBody fetchLocations(FetchLocationsRequestBody fetchStateRequestHES, Map<String, String[]> parameterMap, String correlationKey, Map<String, String> httpHeaderMap) throws JsonParseException, RestConnectorException, ErrorResponseFromDownstreamException {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(Constants.HEADER_CONTENT_TYPE, Constants.HEADER_CONTENT_APPLICATION_JSON);
        headerMap.put(Constants.HEADER_ACCEPT_ENCODING, Constants.HEADER_GZIP);
        headerMap.put(Constants.LANGUAGE, httpHeaderMap.get(Constants.HEADER_LANGUAGE));

        String request = objectMapperUtil.getJsonFromObject(fetchStateRequestHES, DependencyLayer.CLIENTGATEWAY);
        //fetchLocationsUrl = RestURLHelper.getDestinationUrl(fetchLocationsUrl);
        String relativeUrl = Utility.getcompleteURL(fetchLocationsUrl, parameterMap, correlationKey);
        logger.debug("FetchLocations url and request : {} , {}", relativeUrl, request);

        String result = restConnectorUtil.fetchLocationsPost(request, headerMap, relativeUrl);
        logger.debug("FetchLocations response from hes : {}", result);
        FetchLocationsResponseBody resp = objectMapperUtil.getObjectFromJson(result, FetchLocationsResponseBody.class, DependencyLayer.ORCHESTRATOR);
        if (resp != null && resp.getResponseErrors() != null &&
                CollectionUtils.isNotEmpty(resp.getResponseErrors().getErrorList())) {
            throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
                    resp.getResponseErrors().getErrorList().get(0).getErrorCode(),
                    resp.getResponseErrors().getErrorList().get(0).getErrorMessage());
        }
        return resp;
    }
}
