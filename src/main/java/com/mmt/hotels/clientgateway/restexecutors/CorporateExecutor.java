
package com.mmt.hotels.clientgateway.restexecutors;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.request.UpdatePolicyRequest;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.response.corporate.GuestHouseResponse;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.model.response.corporate.*;
import com.mmt.hotels.clientgateway.response.corporate.WorkflowInfoResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.corporate.GetApprovalRequest;
import com.mmt.hotels.model.request.corporate.InitApprovalRequest;
import com.mmt.hotels.model.request.corporate.UpdateApprovalRequest;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

@Component
public class CorporateExecutor {

    @Autowired
    ObjectMapperUtil objectMapperUtil;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    @Autowired
    private SearchHotelsFactory searchHotelsFactory;

    @Value("${corporate.workflow.by.authode.url}")
    private String corporateWorkflowUrl;

    @Value("${corporate.update.approval.url}")
    private String corporateUpdateApprovalUrl;

    @Value("${corporate.update.approvals.url}")
    private String corporateUpdateApprovalsUrl;

    @Value("${corporate.workflow.info.url}")
    private String corporateGetWorkflowInfoUrl;

    @Value("${corporate.approvals.info.url}")
    private String corporateGetApprovalsInfoUrl;

    @Value("${corporate.init.approval.url}")
    private String corporateInitApprovalUrl;

    @Value("${corporate.update.policy.url}")
    private String corporateUpdatePolicyUrl;

    @Value("${corporate.get.availableguesthouses.url}")
    private String availableGuestHousesUrl;

    private static final Gson gson = new Gson();

    private static final Logger logger = LoggerFactory.getLogger(CorporateExecutor.class);

    public WorkflowInfoResponse getWorkflowInfoByAuthCode(String authCode, Map<String, String[]> parameterMap, String correlationKey) throws Exception{
        String result = null;
        WorkflowInfoResponse response = null;
        try {
            String[] authToken = new String[1];
            authToken[0] = URLEncoder.encode(authCode, "UTF-8");
            Map<String, String[]> map = new HashMap<>();
            map.putAll(parameterMap);
            if (MapUtils.isEmpty(map))
                map = new HashMap<>();
            map.put(Constants.auth_code, authToken);
            map.put(Constants.CORRELATIONKEY,new String[]{correlationKey});
            String completeUrl = getURlWithParam(corporateWorkflowUrl, map);
            long start = System.currentTimeMillis();
            logger.info("workflow url :{}" , completeUrl);

            result = restConnectorUtil.performCorporateWorkflowGet(new HashMap<>(), completeUrl);
            logger.warn("TimeTaken in fetching workflow info using authCode={}", start - System.currentTimeMillis());
            response = objectMapperUtil.getObjectFromJson(result, WorkflowInfoResponse.class,
                    DependencyLayer.CORPORATE);
        }catch(Exception e) {
            logger.error("Error occured in get corp workflow info using authcode, Exception : {} ", e);
            throw e;
        }
        return response;
    }

    private String getURlWithParam(String intialURL, Map<String, String[]> paramsMap) {
        StringBuilder sb = new StringBuilder(intialURL);
        int counter = 0;
        for (Map.Entry<String, String[]> entry : paramsMap.entrySet()) {
            if (counter > 0) {
                sb.append(Constants.AMP);
            } else {
                sb.append(Constants.QUE_MARK);
            }
            sb.append(entry.getKey());
            sb.append(Constants.EQUI);
            sb.append(entry.getValue()[0]);
            counter++;
        }
        return sb.toString();
    }

    public CGServerResponse getUpdateApprovalResponse(UpdateApprovalRequest updateApprovalBody, Map<String, String[]> parameterMap, String workflowId, Map<String, String> httpHeaderMap) throws ClientGatewayException, Exception {

        String request = objectMapperUtil.getJsonFromObject(updateApprovalBody, DependencyLayer.CLIENTGATEWAY);
        String url = Utility.getcompleteURL(String.format(corporateUpdateApprovalUrl, workflowId),parameterMap,updateApprovalBody.getCorrelationKey());
        logger.debug(url);
        logger.debug("Request for Update Approval :{}" , request);
        httpHeaderMap.put("srcRequest", "ClientGateway");
        String result = restConnectorUtil.performCorporateApprovalPost(request, httpHeaderMap, url);
        CGServerResponse response = objectMapperUtil.getObjectFromJson(result, CGServerResponse.class,
                DependencyLayer.ORCHESTRATOR);

        return response;
    }

    public CGServerResponse workflowInfoByAuthcode(GetApprovalRequest getApprovalRequest, Map<String, String[]> parameterMap, String correlationKey, String workflowId, Map<String, String> httpHeaderMap) throws Exception {

        String request = objectMapperUtil.getJsonFromObject(getApprovalRequest, DependencyLayer.CLIENTGATEWAY);
        String url = Utility.getcompleteURL(String.format(corporateGetWorkflowInfoUrl, workflowId),parameterMap,correlationKey);
        logger.debug(url);
        logger.debug("Request for Workflow Info :{}" , request);
        httpHeaderMap.put("srcRequest", "ClientGateway");
        String result = restConnectorUtil.performWorkflowInfoByAuthcodePost(request, httpHeaderMap, url);
        CGServerResponse response = objectMapperUtil.getObjectFromJson(result, CGServerResponse.class,
                DependencyLayer.ORCHESTRATOR);

        return response;
    }

    public GetApprovalsResponse getApprovalsInfo(GetApprovalRequest getApprovalRequest, Map<String, String[]> parameterMap, String correlationKey, Map<String, String> httpHeaderMap) throws Exception {

        String request = objectMapperUtil.getJsonFromObject(getApprovalRequest, DependencyLayer.CLIENTGATEWAY);
        String url = Utility.getcompleteURL(corporateGetApprovalsInfoUrl, parameterMap, correlationKey);
        logger.debug(url);
        logger.debug("Request for corp approvals Info :{}" , request);
        httpHeaderMap.put("srcRequest", "ClientGateway");
        String result = restConnectorUtil.performApprovalsInfoPost(request, httpHeaderMap, url);
        logger.debug("Response for corp approvals Info :{}", result);
        GetApprovalsResponse response = objectMapperUtil.getObjectFromJson(result, GetApprovalsResponse.class,
                DependencyLayer.ORCHESTRATOR);

        return response;
    }

	public CGServerResponse requestApproval(InitApprovalRequest approvalRequest, Map<String, String[]> parameterMap, String correlationKey, Map<String, String> httpHeaderMap) throws Exception {
		
        String request = objectMapperUtil.getJsonFromObject(approvalRequest, DependencyLayer.CLIENTGATEWAY);
        logger.debug("Request for Init Approval :{}" , request);
        httpHeaderMap.put("srcRequest", "ClientGateway");
        httpHeaderMap.put("content-type", "application/json");
        String url = Utility.getcompleteURL(corporateInitApprovalUrl,parameterMap,correlationKey);
        String result = restConnectorUtil.performCorporateInitApprovalPost(request, httpHeaderMap, url);
        logger.debug(url);
        logger.debug(request);
        CGServerResponse response = objectMapperUtil.getObjectFromJson(result, CGServerResponse.class,
                DependencyLayer.ORCHESTRATOR);
        
		return response;
	}

    public String updateCorpPolicy(CorpPolicyUpdateRequest corpPolicyUpdateRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap,
                                   String correlationKey) throws Exception{
        String request = objectMapperUtil.getJsonFromObject(corpPolicyUpdateRequest, DependencyLayer.CLIENTGATEWAY);
        logger.debug("Request for updateCorpPolicy :{}" , request);
        httpHeaderMap.put("srcRequest", "ClientGateway");
        httpHeaderMap.put("content-type", "application/json");
        String url = Utility.getcompleteURL(corporateUpdatePolicyUrl,parameterMap,correlationKey);
        return restConnectorUtil.performUpdatePolicyPost(request, httpHeaderMap, url);
    }

    public CorpPolicyUpdateResponse updatePolicy(UpdatePolicyRequest request, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, String client, String correlationKey) throws RestConnectorException, JsonParseException {
        httpHeaderMap.put("content-type", "application/json");
        httpHeaderMap.put("srcRequest", "ClientGateway");
        String url = Utility.getcompleteURL(corporateUpdatePolicyUrl, parameterMap, correlationKey);

        String requestString = objectMapperUtil.getJsonFromObject(request, DependencyLayer.CLIENTGATEWAY);
        logger.debug("Request for updateCorpPolicy :{}" , request);

       String resp = restConnectorUtil.performUpdatePolicyPost(requestString, httpHeaderMap, url);
        CorpPolicyUpdateResponse corpPolicyUpdateResponse = objectMapperUtil.getObjectFromJson(resp, CorpPolicyUpdateResponse.class,
                DependencyLayer.ORCHESTRATOR);

        return corpPolicyUpdateResponse;
    }

    public UpdateWorkflowResponse updateApproval(UpdateApprovalRequest updateApprovalRequestHES, String workflowId,
                                                 Map<String, String[]> parameterMap, String correlationKey,
                                                 Map<String, String> httpHeaderMap) throws Exception {
        String request = objectMapperUtil.getJsonFromObject(updateApprovalRequestHES, DependencyLayer.CLIENTGATEWAY);
        httpHeaderMap.put("srcRequest", "ClientGateway");
        httpHeaderMap.put("content-type", "application/json");
        String url = Utility.getcompleteURL(String.format(corporateUpdateApprovalUrl, workflowId), parameterMap,
                                            correlationKey);
        String result = restConnectorUtil.performCorporateInitApprovalPost(request, httpHeaderMap, url);
        logger.debug("URL update approval:: {}", url);
        logger.debug("Request update approval :{}", request);
        logger.debug("Response update approval :{}", result);
        UpdateWorkflowResponse response = objectMapperUtil.getObjectFromJson(result, UpdateWorkflowResponse.class,
                                                                             DependencyLayer.ORCHESTRATOR);
        return response;
    }

    public UpdateWorkflowResponse updateApprovals(UpdateApprovalRequest updateApprovalRequestHES,
                                                  Map<String, String[]> parameterMap, String correlationKey,
                                                  Map<String, String> httpHeaderMap) throws Exception {
        String request = objectMapperUtil.getJsonFromObject(updateApprovalRequestHES, DependencyLayer.CLIENTGATEWAY);
        httpHeaderMap.put("srcRequest", "ClientGateway");
        httpHeaderMap.put("content-type", "application/json");
        String url = Utility.getcompleteURL(corporateUpdateApprovalsUrl, parameterMap,
                correlationKey);
        String result = restConnectorUtil.performCorporateInitApprovalPost(request, httpHeaderMap, url);
        logger.debug("URL update approval: {}", url);
        logger.debug("Request update approval :{}", request);
        logger.debug("Response update approval :{}", result);
        UpdateWorkflowResponse response = objectMapperUtil.getObjectFromJson(result, UpdateWorkflowResponse.class,
                DependencyLayer.ORCHESTRATOR);
        return response;
    }
    public GuestHouseResponse getAvailableGuestHouses(String transactionKey, Map<String, String[]> parameterMap, String correlationKey, Map<String, String> httpHeaderMap) throws Exception {
        httpHeaderMap.put("srcRequest", "ClientGateway");
        httpHeaderMap.put("content-type", "application/json");
        String url = Utility.getcompleteURL(availableGuestHousesUrl, parameterMap,
                correlationKey, transactionKey);
        String result = restConnectorUtil.performAvailableGuestHouseGet(httpHeaderMap, url);

        AvailableGuestHouseResponse availableGuestHouseResponse = objectMapperUtil.getObjectFromJson(result, AvailableGuestHouseResponse.class, DependencyLayer.CORPORATE);

        GuestHouseResponse guestHouseResponse = buildGuestHouseResponse(availableGuestHouseResponse.getGuestHouseListingResponse(), false, null, null, false);
        guestHouseResponse.setCorrelationKey(transactionKey);
        return guestHouseResponse;
    }

    private GuestHouseResponse buildGuestHouseResponse(GuestHouseListingResponse guestHouseListingResponse, boolean isComparatorV2Required, String client, Map<String, String> expDataMap, boolean isLiteResponse){
        GuestHouseResponse guestHouseResponse = new GuestHouseResponse();
        guestHouseResponse.setTitle(guestHouseListingResponse.getTitle());
        guestHouseResponse.setCtaMap(guestHouseListingResponse.getCtaMap());
        if (null != guestHouseListingResponse.getHotelSearchResponse()) {
            if (CollectionUtils.isNotEmpty(guestHouseListingResponse.getHotelSearchResponse().getHotelList())) {
                guestHouseResponse.setPropertyList(searchHotelsFactory.getResponseService(client).buildPersonalizedHotels(guestHouseListingResponse.getHotelSearchResponse().getHotelList(),
                        expDataMap, null, null, null, null, null, null));
            }
        }
        return guestHouseResponse;
    }
}