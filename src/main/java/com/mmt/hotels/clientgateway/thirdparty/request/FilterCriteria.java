package com.mmt.hotels.clientgateway.thirdparty.request;

import com.mmt.hotels.model.request.flyfish.OTA;
import lombok.Data;

import java.util.List;
import java.util.Map;
@Data
public class FilterCriteria {
    private String ugcId;
    private String programId;
    private Map<String,String> lobData;
    private String sortBy;
    private String sortOrder;
    private List<String> imageTags;
    private List<String> videoTags;
    private List<String> reviewTags;
    private int limit;
    private String cohort;
    private int offset;
    private List<String> reviewIds;
    private List<String> ratingTags;
    private List<OTA> availableOTAS;
    private LobTag lobTags;
}
