package com.mmt.hotels.clientgateway.thirdparty.response;


import com.fasterxml.jackson.annotation.JsonInclude;

@<PERSON>sonInclude(JsonInclude.Include.NON_NULL)
public class Hover {

    private String tooltipType;
    private Object data;

    //New nodes added for Special Fare persuasion in case of negotiated rate hotels.
    private String headingText;

    private PersuasionStyle style;

    public String getTooltipType() {
        return tooltipType;
    }

    public void setTooltipType(String tooltipType) {
        this.tooltipType = tooltipType;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public String getHeadingText() {
        return headingText;
    }

    public void setHeadingText(String headingText) {
        this.headingText = headingText;
    }

    public PersuasionStyle getStyle() {
        return style;
    }

    public void setStyle(PersuasionStyle style) {
        this.style = style;
    }
}
