package com.mmt.hotels.clientgateway.thirdparty.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PersuasionStyle {
    List<String> styleClasses;
    String textColor;
    private String fontType;
    private String fontSize;
    private String borderColor;
    private String borderSize;
    private String bgColor;
    private String cornerRadii;
    private String bgUrl;
    private Integer iconWidth;
    private Integer iconHeight;
    private BgGradient bgGradient;
    private String gravity;
    private Integer maxLines;
    private String imageHeight;
    private String imageWidth;
    private Integer horizontalSpace;
    private Integer verticalSpace;
    private String imageUrl;
}
