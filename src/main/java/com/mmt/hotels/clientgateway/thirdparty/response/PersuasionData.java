package com.mmt.hotels.clientgateway.thirdparty.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.response.Inclusion;
import com.mmt.hotels.clientgateway.response.PersuasionTimer;
import com.mmt.hotels.model.persuasion.response.PersuasionTimerBO;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class PersuasionData {
    private String id;
    private String type;
    private String persuasionType;
    private String persuasionText;
    private String text;
    private String imageUrl;
    private boolean hasAction;
    private boolean html;
    private String iconurl;
    private PersuasionStyle style;
    private List<String> inclusions;
    private PersuasionTimer timer;
    private PersuasionStyle topLevelStyle; //New node added for Homestays Persuasion
    private String icontype;
    private Hover hover;
    private PersuasionTitle persuasionTitle;
    private String actionType;
    private Integer multiPersuasionPriority; //New node added for ShortStay Persuasion
    private Button button; //New node added for Special Fare persuasion in case of negotiated rate hotels.
}
