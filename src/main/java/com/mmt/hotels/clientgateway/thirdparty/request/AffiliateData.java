package com.mmt.hotels.clientgateway.thirdparty.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mmt.hotels.model.response.mypartner.MarkUpDetails;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AffiliateData {
    private String customerNo;
    private String affiliateid;
    private String account_id;
    private String customerName;
    private String agent_mobile_commid;
    private String agent_email_commid;
    private boolean pgCharges;
    private String ismmtprivliege;
    private String agencyMobileCommId;
    private String agencyEmailCommId;
    private MarkUpDetails markup;
    private String state;
    private Permissions permissions;
}
