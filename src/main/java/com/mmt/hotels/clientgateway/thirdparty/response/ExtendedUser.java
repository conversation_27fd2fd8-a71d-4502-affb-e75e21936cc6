package com.mmt.hotels.clientgateway.thirdparty.response;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import com.mmt.hotels.model.request.payment.AddressDetails;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class ExtendedUser {
	
	private List<UserLoginInfo> loginInfoList;

    private String primaryEmailId;
    private String uuid;
    private String profileType;
    private String affiliateId;
    private UserPersonalDetail personalDetails;
    private String profileId;
    private String accountId;
    private String corporateData;
    private List<UserAssociatedTraveller> associatedTravellers;
    private List<UserTravelDocument> travelDocuments;
    private List<AddressDetails> addressDetails;
    private List<AltVerifiedDetails> altVerifiedDetails;

}
