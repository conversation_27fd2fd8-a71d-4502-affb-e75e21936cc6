package com.mmt.hotels.clientgateway.thirdparty.request;

import com.mmt.hotels.clientgateway.request.BaseRequest;
import com.mmt.hotels.clientgateway.request.BaseSearchRequest;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.model.request.flyfish.OTA;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class UgcReviewRequest extends BaseRequest {
    private String expData;
    @NonNull
    private String hotelId;
    private String lob;
    private String client;
    private String roomCode;
    @NonNull
    private List<OTA> availableOTA;
    @NonNull
    private OTA nextOTA;
    private int start;
    public int limit;
    private String travelType;
    private String highLightedReview;
    private String subconcept;
    private List<String> subconcepts;
    @NonNull
    private DeviceDetails deviceDetails;
    private String sortCriteria;


    @Override
    public UgcReviewRequest clone() {
        try {
            return (UgcReviewRequest) super.clone();
        } catch (CloneNotSupportedException e) {
            ArrayList<String> subconcepts = new ArrayList<>();
            if(subconcept != null && !subconcept.isEmpty()){
                subconcepts.add(subconcept);
            }
            return new UgcReviewRequest(this.expData,this.hotelId, this.lob, this.client,this.roomCode,this.getAvailableOTA(),
                    this.getNextOTA(),this.start,this.limit,travelType,this.highLightedReview,this.subconcept,subconcepts,
                    this.getDeviceDetails(),this.getSortCriteria());
        }
    }
}
