package com.mmt.hotels.clientgateway.thirdparty.response;

import com.fasterxml.jackson.annotation.JsonInclude;

@<PERSON>sonInclude(JsonInclude.Include.NON_NULL)
public class BgGradient {
    private String angle;
    private String start;
    private String end;

    public String getAngle() {
        return angle;
    }

    public void setAngle(String angle) {
        this.angle = angle;
    }

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    public String getEnd() {
        return end;
    }

    public void setEnd(String end) {
        this.end = end;
    }
}
