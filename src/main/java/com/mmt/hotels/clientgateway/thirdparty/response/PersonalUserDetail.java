package com.mmt.hotels.clientgateway.thirdparty.response;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PersonalUserDetail {
    private UserNameDetail name;
    private String nationality;

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public UserNameDetail getName() {
        return name;
    }

    public void setName(UserNameDetail name) {
        this.name = name;
    }
}