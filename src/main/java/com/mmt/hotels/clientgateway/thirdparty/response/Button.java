package com.mmt.hotels.clientgateway.thirdparty.response;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class Button {
    private String iconUrl;
    private String actionType;
    private ButtonInfo info;

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public ButtonInfo getInfo() {
        return info;
    }

    public void setInfo(ButtonInfo info) {
        this.info = info;
    }
}
