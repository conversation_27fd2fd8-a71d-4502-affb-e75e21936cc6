package com.mmt.hotels.clientgateway.thirdparty.response;

import java.util.List;

public class CreateUserValueDetail {
    private List<LoginInfo> loginInfoList;
    private String primaryEmailId;
    private String profileType;
    private PersonalUserDetail personalDetails;

    public List<LoginInfo> getLoginInfoList() {
        return loginInfoList;
    }

    public void setLoginInfoList(List<LoginInfo> loginInfoList) {
        this.loginInfoList = loginInfoList;
    }

    public String getPrimaryEmailId() {
        return primaryEmailId;
    }

    public void setPrimaryEmailId(String primaryEmailId) {
        this.primaryEmailId = primaryEmailId;
    }

    public String getProfileType() {
        return profileType;
    }

    public void setProfileType(String profileType) {
        this.profileType = profileType;
    }

    public PersonalUserDetail getPersonalDetails() {
        return personalDetails;
    }

    public void setPersonalDetails(PersonalUserDetail personalDetails) {
        this.personalDetails = personalDetails;
    }
}