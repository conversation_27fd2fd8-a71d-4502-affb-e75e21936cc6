package com.mmt.hotels.clientgateway.thirdparty.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PersuasionObject {
    private String placeholder;
    private String template;
    private List<PersuasionData> data;
    private PersuasionStyle style; //New node added for Homestays Persuasion
    private String templateType;
    //New node added for Special Fare persuasion in case of negotiated rate hotels.
    private Hover hover;
    private ExtraDetails extraDetails;
}
