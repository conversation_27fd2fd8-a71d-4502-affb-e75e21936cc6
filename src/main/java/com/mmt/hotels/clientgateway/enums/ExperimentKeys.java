package com.mmt.hotels.clientgateway.enums;

public enum ExperimentKeys {
    BLACK_REVAMP("mmt.backend.hotel.default.default.default.blackRevamp"),
    APPLY_FILTER_TO_COMBO("mmt.backend.hotel.default.default.default.applyFilterToCombo"),

    EXP_SRRP("SRRP"), //client sends us this flag to identify if they need to show the new rate plan design at their end (to be in sync with backend) HTL-37514
    EXP_REORDER_INCLUSIONS("reorderInclusions"),
    EXP_RATE_PLAN_REDESIGN("ratePlanRedesign"),//this is a backend key to know if we need to show new rateplan design for HTL-37514
    MEAL_RACK_RATE("mealrackrate"),
    SHOW_RECOMMENDED_ROOMS("mmt.backend.hotel.default.detail.default.showRecommendedRooms"),
    SHOW_UPSELL_RECOMMENDATION("showUpsellRecommendation"),
    PRE_APPLIED_FILTERS_ENABLE("mmt.backend.hotel.default.default.default.preAppliedFiltersEnable"),
    PUSH_INDEPENDENT_ON_DIRECT_SEARCH("mmt.backend.hotel.default.default.default.pushIndependentOnDirectSearch"),
    ANCILLARY_DISPLAY_PRICE_VARIANT("mmt.backend.hotel.default.default.default.ancillaryDisplayPrice"),
    SPECIAL_REQUEST("SpecialRequest"),
    showstreetviewondetail("mmt.backend.hotel.default.detail.default.showstreetviewondetail"),
    carouselImageCount("carouselImageCount"),
    aboApplicable("aboApplicable"),
    SBPP("SBPP"), // this exp will let us know which price persuasion to show ex: 1: per room per night 2: per person
    recommendationV1("recommendationV1"),
    forexCard("mmt.backend.hotel.default.default.default.forexCard"),
    cabCard("mmt.backend.hotel.default.default.default.cabCard"),
    EXP_EXTRA_ADULT_CHILD_INCLUSION("mmt.backend.hotel.default.detail.default.myp_extra_adult_inclusion"),
    ALT_ACCO_PROPERTY_CONFIG_ENHANCEMENT("altAccoPropertyConfigEnhancement"),
    MMT_SPOTLIGHT("mmt.backend.hotel.default.default.default.mmtSpotlight_enableFilter"),
    SHOW_BOOK_AT_0_PERSUASION("bookAtZeroPersuasion"),
    EXP_OFFERS_UI_REDESIGN("newOffersUi"),
    BUSINESS_REVIEW("businessReview"),
    NEW_PROPERTY_OFFER("NewPropertyOffer"),
    FOOD_AND_DINING_ENHANCEMENT("foodAndDiningEnhancement"),
    REVIEW_OFFERS_CATEGORY("reviewOffersCategory"),
    PILGR_IMAGE_BED_INFO("mmt.backend.hotel.default.detail.default.pilgrimageBedInfo"),
    MYTRIP_URL_V2("mytripUrlV2"),
    IH_HOUSE_RULE_UI_REVAMP("ihHouseRuleUIRevamp"),
    MEAL_CLARITY("mealClarity"),
    AMENITIES_V2_ENABLED("amenitiesV2Enabled"),
    EXP_ORCHESTRATOR_DETAIL_V2("orchDetailRedesign"),
    FOOD_DINING_SECTION_RATING("foodDiningSectionRating"),
    LOCATION_SECTION_RATING("locationSectionRating"),
    AMENITY_SECTION_RATING("amenitySectionRating"),
    CHATBOT_HOOKS_EXP("mmt.backend.hotel.default.default.default.chatbot_hooks"),
    CHATBOT_ENABLED("mmt.backend.hotel.default.default.default.chatbotEnabled"),
    PRICE_VARIATION_V2("priceVariationV2"),
    ENABLE_HOST_CALLING("hostcallingaa"),
    FREE_STAY_FOR_KIDS_MMT_EXP_KEY("Freechildfilter"),
    SHOW_CONTEXTUAL_HOUSE_RULES("contextualhouserules"),
    SHOW_SMART_FILTER("smartFilter"),
    SHOWFILTERCOUNT("showFilterCount"),
    BLOCKCHEAPESTINFO("blockCheapestInfo"),
    ALTACCOSIMILARPROPERTY("altAccoSimilarPropertyOtherHost");


    private String key;

    ExperimentKeys(String key){
        this.key = key;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}
