package com.mmt.hotels.clientgateway.enums;

public enum ValidationErrors {
	
	EMPTY_TID("00", "no tid present in headers"),
	EMPTY_CLIENT("01", "no client in headers"),
	INVALID_REQUEST("02", "invalid request"),
	EMPTY_TRAVELLER_ID("03", "Traveller ID is empty, cant set policy"),
	AUTHCODE_NULL_IN_APPROVAL("12", "Authcode null/empty for corp approval"),
	WORKFLOW_ID_NULL("13", "WorkflowId null/empty for corp approvals"),
	WORKFLOW_ID_AND_AUTHCODE_NULL("14", "WorkflowId and AuthCode are null/empty for corp approvals"),
	INVALID_PAY_LATER_REQUEST("15","Invalid pay later request"),
	EMPTY_GSTN_DETAIL("16", "GST address information is not present and is mandatory as per govt rule");

	private String errorCode;
	private String errorMsg;
	
	private ValidationErrors(String errorCode, String errorMsg) {
		this.errorCode = errorCode;
		this.errorMsg = errorMsg;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}
}
