package com.mmt.hotels.clientgateway.enums;

import lombok.Getter;

@Getter
public enum DependencyLayer {
	
	CLIENTGATEWAY("00"),
	CLIENTS("01"),
	CLIENTBACKEND("02"),
	ORCHESTRATOR("03"),
	POKUS("04"),
	USERSERVICE("05"),
	HYDRA("06"),
	CORPORATE("07"),
	FLYFISH("08"),
	POLYGLOT("09"),
	DPT("10"),
	PFM("11"),
	ORCHESTRATOR_NEW("12"),
	ORCHESTRATOR_DETAIL("13"),
	CHATBOT("14");
	private String code;
	
	private DependencyLayer(String code) {
		this.code = code;
	}
}
