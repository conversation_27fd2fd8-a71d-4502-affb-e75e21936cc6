package com.mmt.hotels.clientgateway.enums;

public enum Persuasions {
	
	FREE_BREAKFAST("1","FREE_BREAKFAST", "PLACEHOLDER_APPS", "PLACEHOLDER_PWA", "PLACEHOLDER_DESKTOP"),
	FREE_BREAKFAST_AVAIL("2","FREE_BREAKFAST_AVAIL", "PLACEHOLDER_APPS", "PLACEHOLDER_PWA", "PLACEHOLDER_DESKTOP"),
	PAH("3","PAH","PLACEHOLDER_APPS", "PLACEHOLDER_PWA", "PLACEHOLDER_DESKTOP"),
	PAH_AVAIL("4","PAH_AVAIL", "PLACEHOLDER_APPS", "PLACEHOLDER_PWA", "PLACEHOLDER_DESKTOP"),
	FREE_CANCELLATION("5","FREE_CANCELLATION", "PLACEHOLDER_APPS", "PLACEHOLDER_PWA", "PLACEH<PERSON>DER_DESKTOP"),
	FREE_CANCELLATION_AVAIL("6","FREE_CANCELLATION_AVAIL", "PLACEHOLDER_APPS", "PLACEHOLDER_PWA", "PLACEHOLDER_DESKTOP"),
	HIDDEN_GEM_PERSUASION("7","HIDDEN_GEM_PERSUASION","PLACEHOLDER_CARD_M6","PLACEHOLDER_CARD_M6","PC_MIDDLE_9"),
	HIDDEN_GEM_ICON_PERSUASION("8","HIDDEN_GEM_ICON_PERSUASION","PLACEHOLDER_IMAGE_LEFT_TOP","PLACEHOLDER_IMAGE_LEFT_TOP","PC_IMG_ANNOTATION"),
	HOMESTAYS_TITLE_PERSUASION("8","HOMESTAYS_TITLE_PERSUASION","PLACEHOLDER_CARD_M2","PLACEHOLDER_CARD_M2","PC_MIDDLE_3"),
	HOMESTAYS_SUB_TITLE_PERSUASION("8","HOMESTAYS_SUB_TITLE_PERSUASION","PLACEHOLDER_CARD_M2","PLACEHOLDER_CARD_M2","PC_MIDDLE_3"),
	SPECIAL_FARE_PERSUASION("9", "SPECIAL_FARE_PERSUASION", "PLACEHOLDER_BOTTOM_BOX_M", "PLACEHOLDER_PWA", "PC_RIGHT_1_1");

	private String persuasionId;
	private String persuasionName;
	private String placeholderIdApps;
	private String placeholderIdPWA;
	private String placeholderIdDesktop;

	Persuasions(String persuasionId, String persuasionName, String placeholderIdApps, String placeholderIdPWA, String placeholderIdDesktop) {
		this.persuasionId = persuasionId;
		this.persuasionName = persuasionName;
		this.placeholderIdApps = placeholderIdApps;
		this.placeholderIdPWA = placeholderIdPWA;
		this.placeholderIdDesktop = placeholderIdDesktop;
	}

	public String getPersuasionId() {
		return persuasionId;
	}

	public void setPersuasionId(String persuasionId) {
		this.persuasionId = persuasionId;
	}

	public String getPersuasionName() {
		return persuasionName;
	}

	public void setPersuasionName(String persuasionName) {
		this.persuasionName = persuasionName;
	}

	public String getPlaceholderIdApps() {
		return placeholderIdApps;
	}

	public String getPlaceholderIdPWA() {
		return placeholderIdPWA;
	}

	public String getPlaceholderIdDesktop() {
		return placeholderIdDesktop;
	}
}
