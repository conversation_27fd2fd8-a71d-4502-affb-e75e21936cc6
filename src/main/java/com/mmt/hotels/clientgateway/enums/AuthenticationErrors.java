package com.mmt.hotels.clientgateway.enums;

public enum AuthenticationErrors {
	
	INVALID_CLIENT("00", "invalid client in headers"),
	UUID_NOT_FOUND("01","User not Authenticated"),
	CONNECTION_FAILURE("02","We could not authenticate the logged in user");
	
	private String errorCode;
	private String errorMsg;
	
	private AuthenticationErrors(String errorCode, String errorMsg) {
		this.errorCode = errorCode;
		this.errorMsg = errorMsg;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}
}
