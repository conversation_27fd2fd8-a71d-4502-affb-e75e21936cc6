package com.mmt.hotels.clientgateway.enums;

public enum RestErrors {

    UNEXPECTED_ERROR("00","Unexpected Error happened during processing %s API, Exception Message: %s"),
    INCORRECT_XML("01","Incorrect Request/Response xml %s API"),
    INCORRECT_JSON("02","Incorrect Request/Response json %s API"),
    CONNECTIVITY_ERROR("03","Error Connecting to %s API, Exception Message: %s"),
    CO_TIMEOUT_ERROR("04","Connection Timeout Expired while requesting to %s API"),
    EMPTY_RESPONSE("05","No data received from %s API"),
    THREAD_EXECUTION("06","There is some problem during processing threads for %s API"),
    INCORRECT_URL("07","Incorrect or malformed URL %s API"),
    INVALID_INPUT("08","Invalid Input %s API, Exception Message: %s"),
    SO_TIMEOUT_ERROR("09","Socket Timeout Expired while requesting to %s API"),
    HYSTRIX_FALLBACK_ERROR("10","Fall back method triggered by hystrix"),
    BELOWTHRESHOLD("11", "Dynamic data population threshold not met"),
    UPDATE_ATTEMPTS_EXHAUSTED("12", "Update attempts exhausted for txnData"),
    KEYNOTFOUND("13","Key %s not found from Polyglot"),
    GRPC_SERVER_ERROR("14","GRPC_SERVER_ERROR"),
    NO_ERROR_CODE("15","NO_ERROR_MESSAGE"),
    BMS_SAVE_PAN_ERROR("16", "Error saving pan card details with BMS API"),
    PIN_CODE_ERROR_HOTSTORE("17", "Not a valid PinCode"),
    NO_GSTIN_AVAILABLE_AUTOBOOK("18", "No Gstin available in autobook api");
    private String errorCode;
    private String errorMessage;

    RestErrors(String errorCode, String errorMessage){
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public String getErrorCode(){
        return this.errorCode;
    }

    public String getErrorMessage(){
        return this.errorMessage;
    }


}

