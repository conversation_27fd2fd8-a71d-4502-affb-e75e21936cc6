package com.mmt.hotels.clientgateway.enums;

import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;

public enum UgcError {
    GENERIC_ERROR("60604",ConstantsTranslation.PROGRAM_LOADING_ISSUE_MSG ,ConstantsTranslation.PROGRAM_LOADING_ISSUE_TITLE ),
    REVIEW_LINK_EXPIRED("REVIEW_LINK_EXPIRED",ConstantsTranslation.REVIEW_LINK_EXPIRED_MSG,ConstantsTranslation.REVIEW_LINK_EXPIRED_TITLE ), //expired review
    REVIEW_SUBMISSION_TIME_LIMIT_EXCEEDED("REVIEW_SUBMISSION_TIME_LIMIT_EXCEEDED",ConstantsTranslation.REVIEW_SUBMISSION_TIME_LIMIT_EXCEEDED_MSG,ConstantsTranslation.REVIEW_SUBMISSION_TIME_LIMIT_EXCEEDED_TITLE),
    REVIEW_ALREADY_EDITED("REVIEW_ALREADY_EDITED",ConstantsTranslation.REVIEW_SUBMISSION_TIME_LIMIT_EXCEEDED_MSG,ConstantsTranslation.REVIEW_SUBMISSION_TIME_LIMIT_EXCEEDED_TITLE),
    BOOKING_DETAILS_NOT_FOUND_FOR_ACCOUNT("BOOKING_DETAILS_NOT_FOUND_FOR_ACCOUNT",ConstantsTranslation.BOOKING_NOT_FOUND_MSG,ConstantsTranslation.BOOKING_NOT_FOUND_TITLE),
    CONTENT_NOT_FOUND("CONTENT_NOT_FOUND",ConstantsTranslation.PROGRAM_LOADING_ISSUE_MSG ,ConstantsTranslation.PROGRAM_LOADING_ISSUE_TITLE ),
    INTERNAL_SERVER_ERROR("INTERNAL_SERVER_ERROR",ConstantsTranslation.PROGRAM_LOADING_ISSUE_MSG,ConstantsTranslation.PROGRAM_LOADING_ISSUE_TITLE ),
    INVALID_REQUEST("INVALID_REQUEST",ConstantsTranslation.PROGRAM_LOADING_ISSUE_MSG,ConstantsTranslation.PROGRAM_LOADING_ISSUE_TITLE),
    NO_PROGRAM_FOUND("NO_PROGRAM_FOUND",ConstantsTranslation.PROGRAM_LOADING_ISSUE_MSG ,ConstantsTranslation.PROGRAM_LOADING_ISSUE_TITLE );


    private String errorCode;
    private String errorMsg;
    private String title;



    private UgcError(String errorCode, String message, String title){
        this.errorCode = errorCode;
        this.errorMsg = message;
        this.title = title;
    }

    public static UgcError resolve(String errorCode) {
        if (StringUtils.isBlank(errorCode))
            return null;
        Optional<UgcError> optional = Arrays.stream(UgcError.values()).filter(e->(e.getErrorCode().equalsIgnoreCase(errorCode))).findAny();
        return optional.isPresent() ? optional.get() : null;
    }
    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getTitle() {
        return title;
    }
    public void setTitle(String title) {
        this.title = title;
    }

}
