package com.mmt.hotels.clientgateway.enums;

public enum PermissionErrors {

    PERMISSIONS_NOT_FOUND("01", "Hotel permissions not found"),
    AFFILIATE_DETAILS_NULL("02", "PartnerAffiliateDetails is null"),
    PERMISSION_API_EMPTY_RESPONSE("03", "Empty response from Permissions API"),
    PERMISSION_API_PARSE_FAILED("04", "Failed to parse Permissions API response");

    private String errorCode;
    private String errorMsg;

    private PermissionErrors(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
