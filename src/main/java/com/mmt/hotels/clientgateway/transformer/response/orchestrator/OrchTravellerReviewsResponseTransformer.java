package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.gommt.hotels.orchestrator.detail.enums.ReviewCountType;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.CohortDetails;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.ImageDetail;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.ResponseToReview;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.StayDetails;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.TravellerDetails;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.TravellerReviewResponse;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.response.flyfish.ReviewResponse;
import com.mmt.hotels.model.response.flyfish.UGCReviewData;
import com.mmt.hotels.model.response.flyfish.UgcReview;
import com.mmt.hotels.model.response.flyfish.UgcReviewImage;
import com.mmt.hotels.model.response.flyfish.UgcReviewResponse;
import com.mmt.hotels.model.response.flyfish.UgcReviewResponseData;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class OrchTravellerReviewsResponseTransformer {

    public UgcReviewResponseData convertReviewsResponse(TravellerReviewResponse reviewResponse) {
        UgcReviewResponseData reviewResponseData = new UgcReviewResponseData();

        UgcReviewResponse ugcReviewResponse = new UgcReviewResponse();
        if (reviewResponse.getMoreReviewsAvailable() != null) {
            ugcReviewResponse.setMoreReviewsAvailable(reviewResponse.getMoreReviewsAvailable());
        }

        if (reviewResponse.getNextOTA() != null) ugcReviewResponse.setNextOTA(OTA.valueOf(reviewResponse.getNextOTA().name()));
        if (MapUtils.isNotEmpty(reviewResponse.getReviewCounts())) {
            ugcReviewResponse.setIndianReviewCount(reviewResponse.getReviewCounts().getOrDefault(ReviewCountType.DH, null));
            ugcReviewResponse.setMmtReviewCount(reviewResponse.getReviewCounts().getOrDefault(ReviewCountType.MMT, null));
            ugcReviewResponse.setExpReviewCount(reviewResponse.getReviewCounts().getOrDefault(ReviewCountType.EXP, null));
            ugcReviewResponse.setGiReviewCount(reviewResponse.getReviewCounts().getOrDefault(ReviewCountType.GI, null));
        }

        UgcReview reviews = new UgcReview();
        // Transform ReviewData from TravellerReviewResponse to the review list
        if (reviewResponse.getReviewData() != null && !reviewResponse.getReviewData().isEmpty()) {
            List<UGCReviewData> reviewList = transformReviewData(reviewResponse.getReviewData());
            reviews.setReviewList(reviewList);
        }
        if (reviewResponse.getCurrentOTA() != null) reviews.setOta(OTA.valueOf(reviewResponse.getCurrentOTA().name()));

        // Set the reviews in the response 
        ugcReviewResponse.setReviews(reviews);
        reviewResponseData.setResponse(ugcReviewResponse);
        return reviewResponseData;
    }

    /**
     * Transform List<ReviewData> from TravellerReviewResponse to List<UGCReviewData>
     * This method transforms review data from the orchestrator response to the format expected by UgcReview
     */
    private List<UGCReviewData> transformReviewData(List<com.gommt.hotels.orchestrator.detail.model.response.ugc.ReviewData> reviewDataList) {
        if (reviewDataList == null || reviewDataList.isEmpty()) {
            return new ArrayList<>();
        }

        List<UGCReviewData> transformedReviews = new ArrayList<>();
        
        // Transform each ReviewData to UGCReviewData
        for (com.gommt.hotels.orchestrator.detail.model.response.ugc.ReviewData reviewData : reviewDataList) {
            UGCReviewData ugcReviewData = new UGCReviewData();
            ugcReviewData.setUpvote(reviewData.getUpvoteCount());
            ugcReviewData.setUpvoted(reviewData.isShowUpvote());
            ugcReviewData.setCrawledData(reviewData.isCrawledData());
            if (reviewData.getRating() != null) ugcReviewData.setRating(reviewData.getRating());
            if (StringUtils.isNotBlank(reviewData.getReviewDate())) ugcReviewData.setPublishDate(reviewData.getReviewDate());
            if (StringUtils.isNotBlank(reviewData.getLogo())) ugcReviewData.setLogo(reviewData.getLogo());
            if (StringUtils.isNotBlank(reviewData.getReviewId())) ugcReviewData.setId(reviewData.getReviewId());
            if (StringUtils.isNotBlank(reviewData.getTitle())) ugcReviewData.setTitle(reviewData.getTitle());
            if (StringUtils.isNotBlank(reviewData.getReviewText())) ugcReviewData.setReviewText(reviewData.getReviewText());
            if (StringUtils.isNotBlank(reviewData.getBadgeUrl())) ugcReviewData.setBadgeUrl(reviewData.getBadgeUrl());
            if (StringUtils.isNotBlank(reviewData.getReviewsWrittenInfo())) ugcReviewData.setReviewsWrittenInfo(reviewData.getReviewsWrittenInfo());
            if (reviewData.getTravellerDetails() != null) buildTravellerDetails(ugcReviewData, reviewData.getTravellerDetails());
            if (reviewData.getStayDetails() != null) buildStayDetails(ugcReviewData, reviewData.getStayDetails());
            if (reviewData.getCohortDetails() != null) buildCohortDetails(ugcReviewData, reviewData.getCohortDetails());
            if (CollectionUtils.isNotEmpty(reviewData.getImageDetails())) buildImageDetails(ugcReviewData, reviewData.getImageDetails());
            if (CollectionUtils.isNotEmpty(reviewData.getTitleSpan())) ugcReviewData.setTitleSpan(reviewData.getTitleSpan());
            if (CollectionUtils.isNotEmpty(reviewData.getReviewSpan())) ugcReviewData.setReviewSpan(reviewData.getReviewSpan());
            if (CollectionUtils.isNotEmpty(reviewData.getReviewSpanList())) ugcReviewData.setReviewSpanList(reviewData.getReviewSpanList());
            if (CollectionUtils.isNotEmpty(reviewData.getResponseToReviews())) buildResponseToReviews(ugcReviewData, reviewData.getResponseToReviews());

            transformedReviews.add(ugcReviewData);
        }
        
        return transformedReviews;
    }

    private void buildCohortDetails(UGCReviewData ugcReviewData, CohortDetails cohortDetails) {
        if (StringUtils.isNotBlank(cohortDetails.getCohortType())) ugcReviewData.setCohortDetails(cohortDetails.getCohortType());
    }

    private void buildResponseToReviews(UGCReviewData ugcReviewData, List<ResponseToReview> responseToReviews) {
        List<ReviewResponse> responseToReview = new ArrayList<>();
        responseToReviews.forEach(review -> {
            ReviewResponse reviewResponse = new ReviewResponse();
            if (StringUtils.isNotBlank(review.getName())) reviewResponse.setName(review.getName());
            if (StringUtils.isNotBlank(review.getRepliedAgoText())) reviewResponse.setRepliedAgoText(review.getRepliedAgoText());
            if (StringUtils.isNotBlank(review.getResponseText())) reviewResponse.setResponseText(review.getResponseText());
            if (StringUtils.isNotBlank(review.getResponseDate())) reviewResponse.setResponseDate(review.getResponseDate());
            responseToReview.add(reviewResponse);
        });

        if (CollectionUtils.isNotEmpty(responseToReview)) {
            ugcReviewData.setResponseToReview(responseToReview);
        }
    }

    private void buildImageDetails(UGCReviewData ugcReviewData, List<ImageDetail> imageDetails) {

        List<UgcReviewImage> ugcReviewImageList = new ArrayList<>();
        imageDetails.forEach(imageDetail -> {
            UgcReviewImage ugcReviewImage = new UgcReviewImage();
            ugcReviewImage.setImgUrl(imageDetail.getUrl());
            if (CollectionUtils.isNotEmpty(imageDetail.getTags())) ugcReviewImage.setMmtTagList(imageDetail.getTags());
            ugcReviewImageList.add(ugcReviewImage);
        });

        if (CollectionUtils.isNotEmpty(imageDetails)) {
            ugcReviewData.setImages(ugcReviewImageList);
        }
    }

    private void buildStayDetails(UGCReviewData ugcReviewData, StayDetails stayDetails) {

//    private String stayDuration;
        if (StringUtils.isNotBlank(stayDetails.getStayDate())) ugcReviewData.setStayDate(stayDetails.getStayDate());
        if (StringUtils.isNotBlank(stayDetails.getStayDuration())) ugcReviewData.setStayDetails(stayDetails.getStayDuration());
        if (StringUtils.isNotBlank(stayDetails.getRoomType())) ugcReviewData.setRoomType(stayDetails.getRoomType());
    }

    private void buildTravellerDetails(UGCReviewData ugcReviewData, TravellerDetails travellerDetails) {
        if (StringUtils.isNotBlank(travellerDetails.getTravellerName()))
            ugcReviewData.setTravellerName(travellerDetails.getTravellerName());
        if (StringUtils.isNotBlank(travellerDetails.getTravellerImage()))
            ugcReviewData.setTravellerImage(travellerDetails.getTravellerImage());
        if (StringUtils.isNotBlank(travellerDetails.getTravelType()))
            ugcReviewData.setTravelType(travellerDetails.getTravelType());
        if (StringUtils.isNotBlank(travellerDetails.getLoyaltyIcon()))
            ugcReviewData.setLoyaltyIcon(travellerDetails.getLoyaltyIcon());
    }
}
