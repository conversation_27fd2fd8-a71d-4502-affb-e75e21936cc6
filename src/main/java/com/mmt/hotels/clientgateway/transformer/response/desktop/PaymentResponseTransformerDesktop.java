package com.mmt.hotels.clientgateway.transformer.response.desktop;

import com.mmt.hotels.clientgateway.constants.DuplicateBookingKey;
import com.mmt.hotels.clientgateway.response.ConsentData;
import com.mmt.hotels.clientgateway.response.PaymentResponse;
import com.mmt.hotels.clientgateway.transformer.response.PaymentResponseTransformer;
import com.mmt.hotels.model.request.payment.BeginCheckoutReqBody;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

@Component
public class PaymentResponseTransformerDesktop extends PaymentResponseTransformer {

    @Override
    public PaymentResponse processResponse(PaymentCheckoutResponse checkoutResponse, BeginCheckoutReqBody beginCheckoutReqBody){
        PaymentResponse paymentResponse =super.processResponse(checkoutResponse,beginCheckoutReqBody);
        if(checkoutResponse.getResponseErrors() != null && CollectionUtils.isNotEmpty(checkoutResponse.getResponseErrors().getErrorList()))
            return paymentResponse;
        else {
            paymentResponse.setFkToken(checkoutResponse.getPaymentParams() !=null && 
            		checkoutResponse.getPaymentParams().get("paymentPlatform") !=null ? checkoutResponse.getPaymentParams().get("paymentPlatform").toString() : null);
            paymentResponse.setThankYouUrl(checkoutResponse.getThankYouURL());
            paymentResponse.setCheckoutUrl(checkoutResponse.getPaymentParams() !=null && 
            		checkoutResponse.getPaymentParams().get("checkoutUrl") !=null ? checkoutResponse.getPaymentParams().get("checkoutUrl").toString() : null);

            return paymentResponse;
        }
    }

    @Override
    public void populateClientSpecificFields(ConsentData consentData) {
        consentData.setSubHeading(polyglotService.getTranslatedData(DuplicateBookingKey.DUPLICATE_BOOKING_SUB_HEADING.getValue()));
        consentData.setIconUrl(polyglotService.getTranslatedData(DuplicateBookingKey.DUPLICATE_BOOKING_ICON_URL_DESKTOP.getValue()));
    }
}
