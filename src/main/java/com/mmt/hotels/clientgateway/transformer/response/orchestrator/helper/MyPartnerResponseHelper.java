package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.common.DisplayItem;
import com.gommt.hotels.orchestrator.detail.model.response.mypartner.DisplayMeta;
import com.gommt.hotels.orchestrator.detail.model.response.mypartner.ErrorResponse;
import com.gommt.hotels.orchestrator.detail.model.response.mypartner.HeroCard;
import com.gommt.hotels.orchestrator.detail.model.response.mypartner.LoyaltyResponse;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.MarkUpDetails;
import com.mmt.hotels.clientgateway.response.MarkUpConfig;
import com.mmt.hotels.model.response.errors.LoyaltyError;
import com.mmt.hotels.model.response.persuasion.MyPartnerLoyaltyResponse;
import com.mmt.hotels.model.response.persuasion.TextIconUrl;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
public class MyPartnerResponseHelper {

    public MyPartnerLoyaltyResponse mapLoyaltyResponseToMyPartnerLoyaltyResponse(LoyaltyResponse loyaltyResponse) {
        if (loyaltyResponse == null) return null;
        MyPartnerLoyaltyResponse myPartnerLoyaltyResponse = new MyPartnerLoyaltyResponse();

        // Map basic fields
        myPartnerLoyaltyResponse.setTierNumber(loyaltyResponse.getTierNumber());
        myPartnerLoyaltyResponse.setLoyaltyEligible(loyaltyResponse.isLoyaltyEligible());
        myPartnerLoyaltyResponse.setWalletEarn(loyaltyResponse.getWalletEarn());
        myPartnerLoyaltyResponse.setTierName(loyaltyResponse.getTierName());
        myPartnerLoyaltyResponse.setStatus(loyaltyResponse.getStatus());

        // Map errors - convert from ErrorResponse to LoyaltyError
        // Note: LoyaltyError is not available in the expected package, so skipping error mapping
        if (loyaltyResponse.getErrors() != null && !loyaltyResponse.getErrors().isEmpty()) {
            List<LoyaltyError> loyaltyErrors = new ArrayList<>();
            for (ErrorResponse errorResponse : loyaltyResponse.getErrors()) {
                LoyaltyError loyaltyError = new LoyaltyError();
                loyaltyError.setType(errorResponse.getType());
                loyaltyError.setCode(errorResponse.getCode());
                loyaltyError.setMsg(errorResponse.getMsg());
                loyaltyErrors.add(loyaltyError);
            }
            myPartnerLoyaltyResponse.setErrors(loyaltyErrors);
        }

        // Map primary card
        if (loyaltyResponse.getPrimaryCard() != null) {
            myPartnerLoyaltyResponse.setPrimaryCard(mapHeroCardToMyPartnerHeroCard(loyaltyResponse.getPrimaryCard()));
        }

        // Map secondary cards
        if (loyaltyResponse.getSecondaryCards() != null && !loyaltyResponse.getSecondaryCards().isEmpty()) {
            List<com.mmt.hotels.model.response.persuasion.MyPartnerHeroCard> secondaryCards = new ArrayList<>();
            for (HeroCard heroCard : loyaltyResponse.getSecondaryCards()) {
                secondaryCards.add(mapHeroCardToMyPartnerHeroCard(heroCard));
            }
            myPartnerLoyaltyResponse.setSecondaryCards(secondaryCards);
        }

        return myPartnerLoyaltyResponse;
    }

    private com.mmt.hotels.model.response.persuasion.MyPartnerHeroCard mapHeroCardToMyPartnerHeroCard(HeroCard heroCard) {
        if (heroCard == null) return null;
        com.mmt.hotels.model.response.persuasion.MyPartnerHeroCard myPartnerHeroCard = new com.mmt.hotels.model.response.persuasion.MyPartnerHeroCard();

        // Map all available fields from HeroCard to MyPartnerHeroCard
        myPartnerHeroCard.setCardId(heroCard.getCardId());
        myPartnerHeroCard.setMessageTitle(heroCard.getMessageTitle());
        myPartnerHeroCard.setMessageText(heroCard.getMessageText());
        myPartnerHeroCard.setTierName(heroCard.getTierName());
        myPartnerHeroCard.setCta(heroCard.getCta());
        myPartnerHeroCard.setCtaLink(heroCard.getCtaLink());
        myPartnerHeroCard.setCtaActionType(heroCard.getCtaActionType());

        // Map DisplayMeta
        if (heroCard.getDisplayMeta() != null) {
            myPartnerHeroCard.setDisplayMeta(mapDisplayMetaToMyPartnerDisplayMeta(heroCard.getDisplayMeta()));
        }

        // Map textIconUrlList - assuming DisplayItem maps to some structure in MyPartnerHeroCard
        if (heroCard.getTextIconUrlList() != null && !heroCard.getTextIconUrlList().isEmpty()) {
            List<TextIconUrl> textIconUrlList = new ArrayList<>();
            for (DisplayItem displayItem : heroCard.getTextIconUrlList()) {
                TextIconUrl textIconUrl = new TextIconUrl();
                textIconUrl.setIconUrl(displayItem.getIconUrl());
                textIconUrl.setText(displayItem.getText());
                textIconUrlList.add(textIconUrl);
            }
            myPartnerHeroCard.setTextIconUrlList(textIconUrlList);
        }

        return myPartnerHeroCard;
    }

    private com.mmt.hotels.model.response.persuasion.DisplayMeta mapDisplayMetaToMyPartnerDisplayMeta(DisplayMeta displayMeta) {
        if (displayMeta == null) return null;
        com.mmt.hotels.model.response.persuasion.DisplayMeta myPartnerDisplayMeta = new com.mmt.hotels.model.response.persuasion.DisplayMeta();

        // Based on UnifiedCheckoutHelper usage, these fields exist in DisplayMeta:
        myPartnerDisplayMeta.setIconUrl(displayMeta.getIconUrl());
        myPartnerDisplayMeta.setIconText(displayMeta.getIconText());
        myPartnerDisplayMeta.setBgGradient(displayMeta.getBgGradient());
        myPartnerDisplayMeta.setHeaderTextColorBg(displayMeta.getHeaderTextColorBg());
        myPartnerDisplayMeta.setBannerBgImage(displayMeta.getBannerBgImage());
        myPartnerDisplayMeta.setBannerBgGradient(displayMeta.getBannerBgGradient());
        myPartnerDisplayMeta.setIconBgGradient(displayMeta.getIconBgGradient());

        return myPartnerDisplayMeta;
    }


    public MarkUpConfig buildMarkUpConfig(final MarkUpDetails markUpDetails, final MarkUpConfig markUpConfig) {
        if (Objects.nonNull(markUpDetails) && Objects.nonNull(markUpConfig) && markUpDetails.isMarkupEligible()) {
            final MarkUpConfig finalMarkUp = new MarkUpConfig();
            finalMarkUp.setText(markUpConfig.getText());
            finalMarkUp.setFlagValue(markUpConfig.isFlagValue());
            finalMarkUp.setHoverText(markUpConfig.getHoverText());
            if (markUpDetails.isEditable()) {
                finalMarkUp.setCtaUrl(markUpConfig.getCtaUrl());
                finalMarkUp.setCtaText(markUpConfig.getCtaText());
            }
            return finalMarkUp;
        }
        return null;
    }
}
