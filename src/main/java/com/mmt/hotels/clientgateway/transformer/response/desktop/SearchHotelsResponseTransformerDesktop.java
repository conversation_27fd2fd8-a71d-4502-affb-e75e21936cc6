package com.mmt.hotels.clientgateway.transformer.response.desktop;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.PropertyTextConfigConsul;
import com.mmt.hotels.clientgateway.enums.ActionType;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.PersuasionTemplate;
import com.mmt.hotels.clientgateway.enums.PersuasionType;
import com.mmt.hotels.clientgateway.enums.Persuasions;
import com.mmt.hotels.clientgateway.enums.TemplateType;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.PropertyTextConfig;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.response.searchHotels.BottomSheet;
import com.mmt.hotels.clientgateway.response.searchHotels.DesktopStylingClassesObj;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.HotelCard;
import com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard;
import com.mmt.hotels.clientgateway.response.searchHotels.SectionFeature;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.transformer.response.SearchHotelsResponseTransformer;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.listpersonalization.IndianessToolTip;
import com.mmt.hotels.model.response.listpersonalization.DevotenessToolTip;
import com.mmt.hotels.model.response.listpersonalization.LuxeToolTip;
import com.mmt.hotels.model.response.listpersonalization.MyBizAssuredToolTip;
import com.mmt.hotels.model.response.listpersonalization.MySafetyTooltip;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysTooltip;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.searchwrapper.PersonalizedResponse;
import com.mmt.hotels.model.response.searchwrapper.QuickBookInfo;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.hotels.pojo.listing.personalization.BGLinearGradient;
import com.mmt.hotels.pojo.response.CampaignPojo;
import com.mmt.hotels.pojo.response.DevotenessPersuasion;
import com.mmt.hotels.pojo.response.IndianessPersuasion;
import com.mmt.model.LocusData;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;

@Component
public class SearchHotelsResponseTransformerDesktop extends SearchHotelsResponseTransformer {

	private static final Logger LOGGER = LoggerFactory.getLogger(SearchHotelsResponseTransformerDesktop.class);

	private MySafetyTooltip mySafetyDataTooltips ;

	List<String> amenetiesWithUrl;

	@Value("${consul.enable}")
	private boolean consulFlag;

	@Autowired
	PropertyTextConfigConsul propertyTextConfigConsul;

	@Autowired
	CommonConfigConsul commonConfigConsul;

	@Autowired
	PropertyManager propManager;
	@Autowired
	private PolyglotHelper polyglotHelper;

	@Autowired
	private Utility utility;
	private ValueStaysTooltip valueStaysTooltipDom;
	private ValueStaysTooltip valueStaysTooltipIntl;
	private ValueStaysTooltip mmtMyPartnerTooltip;
	private Map<Integer, Set<String>> budgetHotelCityConfig;
	private LuxeToolTip luxeToolTipConfig;
	private MySafetyTooltip mysafetytooltip;
	private MyBizAssuredToolTip myBizAssuredTooltipDom;

	@Autowired
	PolyglotService polyglotService;

	@Autowired
	DateUtil dateUtil;

	@Value("${desktop.tool.tip.persuasions}")
	private String toolTipPersuasions;

	@Value("${mybiz.assured.url}")
	private String myBizAssuredUrl;

	@Value("${high.rated.url}")
	private String highRatedUrl;

	@Value("${gst.invoice.url}")
	private String gstInvoiceUrl;

	@Value("${bpg.url}")
	private String bpgUrl;

	@Value("${active.languages}")
	private String activeLanguages;

	private Map<String, Object> desktopToolTipPersuasionsMap;

	private Map<String, MySafetyTooltip> mySafetyToolTipTranslated;

	private MyBizStaticCard myBizStaticCard;

	@Autowired
	PersuasionUtil persuasionUtil;

	private static final String DEVICE_TYPE = "Desktop";

	@PostConstruct
	public void init() {

		if(consulFlag){
			mySafetyDataTooltips = propertyTextConfigConsul.getMySafetyTooltipKeys();
			mySafetyToolTipTranslated = createMySafetyTooltipTranslated();
			amenetiesWithUrl = propertyTextConfigConsul.getAmenetiesWithUrl();
			LOGGER.debug("Fetching values from propertyTextConfig consul");
		}
		else{
			PropertyTextConfig propertyTextConfig = propManager.getProperty("propertyTextConfig", PropertyTextConfig.class);
			mySafetyDataTooltips =   propertyTextConfig.mySafetyTooltipKeys();
			propertyTextConfig.addPropertyChangeListener("propertyTextConfig", event -> {
				mySafetyDataTooltips =  propertyTextConfig.mySafetyTooltipKeys();
			});
			mySafetyToolTipTranslated = createMySafetyTooltipTranslated();
			amenetiesWithUrl = propertyTextConfig.amenetiesWithUrl();
			propertyTextConfig.addPropertyChangeListener("propertyTextConfig", event -> {
				amenetiesWithUrl = propertyTextConfig.amenetiesWithUrl();

			});
		}

		if(consulFlag){
			valueStaysTooltipDom = commonConfigConsul.getMmtValueStaysTooltipDom();
			valueStaysTooltipIntl = commonConfigConsul.getMmtValueStaysTooltipIntl();
			mmtMyPartnerTooltip= commonConfigConsul.getMmtMyPartnerTooltip();
			luxeToolTipConfig = commonConfigConsul.getLuxeToolTip();
			myBizAssuredTooltipDom = commonConfigConsul.getMyBizAssuredTooltipDom();
			myBizStaticCard = commonConfigConsul.getMyBizStaticCard();
			missingSlotDetails = commonConfigConsul.getMissingSlotDetails();
			thresholdForSlashedAndDefaultHourPrice = commonConfigConsul.getThresholdForSlashedAndDefaultHourPrice();
			LOGGER.warn("missingSlotDetails : {}" , missingSlotDetails);
			LOGGER.debug("Fetching values from commonConfig consul");
		}
		else{
			CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
			valueStaysTooltipDom = commonConfig.mmtValueStaysTooltipDom();
			valueStaysTooltipIntl = commonConfig.mmtValueStaysTooltipIntl();
			luxeToolTipConfig = commonConfig.luxeToolTip();
			myBizAssuredTooltipDom = commonConfig.myBizAssuredTooltipDom();
			myBizStaticCard = commonConfig.myBizStaticCard();
			missingSlotDetails = commonConfig.missingSlotDetails();
			thresholdForSlashedAndDefaultHourPrice = commonConfig.thresholdForSlashedAndDefaultHourPrice();
			commonConfig.addPropertyChangeListener("missingSlotDetails", event -> missingSlotDetails = commonConfig.missingSlotDetails());
			commonConfig.addPropertyChangeListener("myBizStaticCard", event -> myBizStaticCard = commonConfig.myBizStaticCard());
			commonConfig.addPropertyChangeListener("mmtValueStaysTooltipDom", evt -> valueStaysTooltipDom = commonConfig.mmtValueStaysTooltipDom());
			commonConfig.addPropertyChangeListener("mmtValueStaysTooltipIntl", evt -> valueStaysTooltipIntl = commonConfig.mmtValueStaysTooltipIntl());
			commonConfig.addPropertyChangeListener("luxeToolTip", evt -> luxeToolTipConfig = commonConfig.luxeToolTip());
			commonConfig.addPropertyChangeListener("myBizAssuredTooltipDom", evt -> myBizAssuredTooltipDom = commonConfig.myBizAssuredTooltipDom());
			LOGGER.warn("missingSlotDetails : {}" , missingSlotDetails);
		}

		try {
			desktopToolTipPersuasionsMap = objectMapperUtil.getObjectFromJsonWithType(toolTipPersuasions, new TypeReference<Map<String, Object>>() {},
					DependencyLayer.CLIENTGATEWAY);
		} catch (JsonParseException e) {
			e.printStackTrace();
			LOGGER.error("error in creating desktopToolTipPersuasionsMap from string {} ", toolTipPersuasions);
		}
		specialFarePersuasionConfigMap = new Gson().fromJson(specialFarePersuasionConfig, new TypeToken<Map<String, Map<String, PersuasionData>>>() {
		}.getType());
	}


	@Autowired
	ObjectMapperUtil objectMapperUtil;

	@Override
	public void populateClientSpecificParameters() {
	}

	@Override
	public void addSeoTextPersuasion(Hotel hotel, SearchWrapperHotelEntity hotelEntity, boolean oddHotel, ListingSearchRequest searchHotelsRequest, String sectionName) {
		if (Utility.isSeoPersuasionAllowed(searchHotelsRequest, sectionName)) return;

		if (hotel.getHotelPersuasions() == null) {
			hotel.setHotelPersuasions(new HashMap<String, Object>());
		}
		boolean isPerNewEnabled = searchHotelsRequest!=null?utility.isExperimentOn(searchHotelsRequest.getExpDataMap(), EXP_PERNEW):false;
		PersuasionObject seoPersuasion = new PersuasionObject();
		seoPersuasion.setData(new ArrayList<>());
		seoPersuasion.setPlaceholder(Constants.SEO_TEXT_PERSUASION_PLACEHOLDER_ID);
		seoPersuasion.setPlaceholder("SINGLE");
        seoPersuasion.setTemplate("TEXT_OVERFLOW");

		PersuasionData seoPersuasionData = new PersuasionData();
		seoPersuasionData.setHasAction(true);
		seoPersuasionData.setHtml(true);
		seoPersuasionData.setStyle(new PersuasionStyle());
		List<String> styleClasses = new ArrayList<>();
		styleClasses.add("pc__seoText");
		if(isPerNewEnabled){
			styleClasses.add("pc__textOverflowPerNew");
		} else {
			styleClasses.add("pc__textOverflow");
		}
		seoPersuasionData.getStyle().setStyleClasses(styleClasses);
		seoPersuasionData.setPersuasionType(Constants.PERSUASION_TYPE_SEO);
		boolean isSeoDs = searchHotelsRequest.getFeatureFlags() !=null ? searchHotelsRequest.getFeatureFlags().isSeoDS():false;
		String seoCohort = searchHotelsRequest.getFeatureFlags() !=null ? searchHotelsRequest.getFeatureFlags().getSeoCohort() : null;
		seoPersuasionData.setText(getSeoPersuasionText(oddHotel, true, hotelEntity, isSeoDs, seoCohort));
		seoPersuasion.getData().add(seoPersuasionData);
		seoPersuasionData.setActionType("LISTING_MAP_POPUP");
		((Map<String, Object>) hotel.getHotelPersuasions())
				.put(Constants.SEO_TEXT_PERSUASION_PLACEHOLDER_ID, seoPersuasion);
	}

	@Override
	public HotelCard buildQuickBookCard(QuickBookInfo quickBookInfo) {
		HotelCard hotelBottomCard = new HotelCard();
		hotelBottomCard.setHeading(StringUtils.replace(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_TITLE),"{TITLE}", quickBookInfo.getTitleWithPrice()));
		hotelBottomCard.setSubHeading(StringUtils.replace(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_SUBTITLE),"{SUBTITLE}", quickBookInfo.getRoomPersuasion()));
		hotelBottomCard.setRoomSubHeading(StringUtils.replace(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_MODAL_SUBTITLE),"{SUBTITLE}", quickBookInfo.getRoomPersuasionWithSize()));
		hotelBottomCard.setShowCard(quickBookInfo.isShowQuickBookCard());
		hotelBottomCard.setCta(polyglotService.getTranslatedData(ConstantsTranslation.QUICK_BOOK_DESKTOP_CTA));
		BGLinearGradient bgLinearGradient = new BGLinearGradient();
		bgLinearGradient.setDirection("91.03deg");
		bgLinearGradient.setStart("#2D6F95 1.46%");
		bgLinearGradient.setEnd("#192B43 99.9%");
		hotelBottomCard.setBgLinearGradient(bgLinearGradient);
		DesktopStylingClassesObj classesObj = new DesktopStylingClassesObj();
		classesObj.setOuterClass("myBizQuickBook");
		classesObj.setCtaClass1("quick__book-btn");
		classesObj.setCtaClass2("double--arw");
		hotelBottomCard.setDesktopStylingClassesObj(classesObj);
		return hotelBottomCard;
	}

	@Override
	public String getMyBizDirectHotelDistanceText(String distanceText) {
		return StringUtils.replace(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_DIRECT_HOTEL_DESKTOP_DISTANCE_TEXT),"{DISTANCE_TEXT}", distanceText);
	}

	@Override
	public void addPersuasionHoverData(Hotel hotel, SearchWrapperHotelEntity hotelEntity, CancellationTimeline cancellationTimeline, DisplayFare displayFare) {
		try {
			if (null != hotel.getHotelPersuasions()) {
				JSONObject hotelPersuasions = new JSONObject(objectMapperUtil.getJsonFromObject(hotel.getHotelPersuasions(), DependencyLayer.CLIENTGATEWAY));
				for (String placeHolder : hotelPersuasions.keySet()) {
					JSONObject persuasion = hotelPersuasions.has(placeHolder) ? hotelPersuasions.getJSONObject(placeHolder) : null;
					if (null != persuasion && persuasion.has("data")) {
						JSONArray persuasionDataList = persuasion.getJSONArray("data");
						for (int i = 0; i < persuasionDataList.length(); i++) {
							JSONObject persuasionData = persuasionDataList.getJSONObject(i);
							if (persuasionData.has("hover")) {
								if (persuasionData.getJSONObject("hover").has("tooltipType")) {
									addToolTip(persuasionData.getJSONObject("hover"), hotelEntity, cancellationTimeline, displayFare,
											persuasionData.has("persuasionKey") ? persuasionData.getString("persuasionKey") : null);
									if (persuasionData != null && persuasionData.getJSONObject("hover") != null && StringUtils.isNotEmpty(persuasionData.getJSONObject("hover").getString("tooltipType"))
											&& TOOL_TIP_INDIANNESS.equalsIgnoreCase(persuasionData.getJSONObject("hover").getString("tooltipType").toUpperCase())) {
										hotel.setLovedByIndians(true);
									}
								}
								if (isWalletSurgePersuasionEnabled(persuasionData)) {
									persuasionData.getJSONObject("hover").put(PERSUASION_HEADING_KEY, hotelEntity.getWalletEntity().getWpmRule().getOutputDetails().getPersuasionText());
									persuasionData.getJSONObject(TIMER).put(EXPIRY, hotelEntity.getWalletEntity().getWpmRule().getExpireAt());
								}
							}
						}

					}

					if(null != persuasion && persuasion.has("hover")) {
						JSONObject topLevelHoverData = persuasion.getJSONObject("hover");
						updateTopLevelHover(topLevelHoverData, hotelEntity.getMpFareHoldStatus());
					}
				}
				hotel.setHotelPersuasions(hotelPersuasions.toMap());
			}
		}catch(Exception e){
			LOGGER.error("Error while updating hover data for desktop", e);
		}

	}

	private void addToolTip(JSONObject hoverData, SearchWrapperHotelEntity hotelEntity, CancellationTimeline cancellationTimeline, DisplayFare displayFare, String persuasionKey) {
		switch (hoverData.getString("tooltipType").toUpperCase()) {
			case Constants.TOOL_TIP_BNPL_AVAIL:
				hoverData.put("data", cancellationTimeline);
				break;
			case Constants.TOOL_TIP_FCZPN:
				hoverData.put("data", cancellationTimeline);
				break;
			case Constants.TOOL_TIP_FC:
				hoverData.put("data", createFreeCancellationTooltip(cancellationTimeline));
				break;
			case Constants.TOOL_TIP_OOP:
				if (null != displayFare && null != displayFare.getCorpMetaData() && null != displayFare.getCorpMetaData().getValidationPayload()
						&& CollectionUtils.isNotEmpty(displayFare.getCorpMetaData().getValidationPayload().getFailureReasons())) {
					hoverData.put("data", displayFare.getCorpMetaData().getValidationPayload().getFailureReasons());
				}
				break;
			case Constants.TOOL_TIP_SAFETY:
				hoverData.put("data", mySafetyToolTipTranslated.get(MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue())));

				break;
			case Constants.TOOL_TIP_VILLA_STAY:
				if (MapUtils.isNotEmpty(desktopToolTipPersuasionsMap) && desktopToolTipPersuasionsMap
						.containsKey(Constants.TOOL_TIP_VILLA_STAY)) {
					hoverData.put("data", fetchToolTipPersuasionData(Constants.TOOL_TIP_VILLA_STAY));
					hoverData.put("tooltipType", Constants.TOOL_TIP_TYPE_PROPERTY_BENEFITS);
				}
				break;
			case Constants.TOOL_TIP_HOSTEL_STAY:
				if (MapUtils.isNotEmpty(desktopToolTipPersuasionsMap) && desktopToolTipPersuasionsMap
						.containsKey(Constants.TOOL_TIP_HOSTEL_STAY)) {
					hoverData.put("data", fetchToolTipPersuasionData(Constants.TOOL_TIP_HOSTEL_STAY));
					hoverData.put("tooltipType", Constants.TOOL_TIP_TYPE_PROPERTY_BENEFITS);
				}
				break;
			case Constants.TOOL_TIP_APARTMENT_STAY:
				if (MapUtils.isNotEmpty(desktopToolTipPersuasionsMap) && desktopToolTipPersuasionsMap
						.containsKey(Constants.TOOL_TIP_APARTMENT_STAY)) {
					hoverData.put("data", fetchToolTipPersuasionData(Constants.TOOL_TIP_APARTMENT_STAY));
					hoverData.put("tooltipType", Constants.TOOL_TIP_TYPE_PROPERTY_BENEFITS);
				}
				break;
			case Constants.TOOL_TIP_COTTAGE_STAY:
				if (MapUtils.isNotEmpty(desktopToolTipPersuasionsMap) && desktopToolTipPersuasionsMap
						.containsKey(Constants.TOOL_TIP_COTTAGE_STAY)) {
					hoverData.put("data", fetchToolTipPersuasionData(Constants.TOOL_TIP_COTTAGE_STAY));
					hoverData.put("tooltipType", Constants.TOOL_TIP_TYPE_PROPERTY_BENEFITS);
				}
				break;
			case Constants.TOOL_TIP_HOMESTAY_STAY:
				if (MapUtils.isNotEmpty(desktopToolTipPersuasionsMap) && desktopToolTipPersuasionsMap
						.containsKey(Constants.TOOL_TIP_HOMESTAY_STAY)) {
					hoverData.put("data", fetchToolTipPersuasionData(Constants.TOOL_TIP_HOMESTAY_STAY));
					hoverData.put("tooltipType", Constants.TOOL_TIP_TYPE_PROPERTY_BENEFITS);
				}
				break;
			case Constants.TOOL_TIP_MMT_VALUE_STAY:
				if (hotelEntity.isBudgetHotel()) {
					hoverData.put("data", createValueStayToolTip(hotelEntity.getCountryCode()));
				}
				break;
			case Constants.TOOL_TIP_MYBIZ_ASSURED:
				hoverData.put("data", createMyBizAssuredToolTip());
				break;
			case Constants.TOOL_TIP_LUXE:
				hoverData.put("data", createLuxeToolTip());
				break;
			case TOOL_TIP_INDIANNESS:
				hoverData.put("data", createIndianessPersuasion(hotelEntity.getIndianessPersuasion()));
				break;
			case Constants.LOVED_BY_DEVOTEES:
				hoverData.put("data", createDevotenessPersuasion(hotelEntity.getDevotenessPersuasion()));
				break;
			case TOOL_TIP_GST_ASSURED:
				hoverData.put("data", mmtMyPartnerTooltip);
                break;
		}
	}

	public IndianessToolTip createIndianessPersuasion(IndianessPersuasion indianessPersuasion){
		IndianessToolTip indianessToolTip = new IndianessToolTip();
		if(indianessPersuasion == null || CollectionUtils.isEmpty(indianessPersuasion.getShortSummary()))
			return indianessToolTip;
		List<IndianessToolTip> data = new ArrayList<>();
		for(CampaignPojo campaignPojo : indianessPersuasion.getShortSummary()){
			IndianessToolTip indianessToolTip1 = new IndianessToolTip();
			indianessToolTip1.setTitleText(campaignPojo.getHeading());
			indianessToolTip1.setIconUrl(campaignPojo.getIconUrl());
			data.add(indianessToolTip1);
		}
		indianessToolTip.setData(data);
		indianessToolTip.setTitleText(polyglotService.getTranslatedData(INDIANESS_HOVER_TITLE));
		indianessToolTip.setSubText(polyglotService.getTranslatedData(INDIANESS_HOVER_SUBTITLE));
		return indianessToolTip;
	}

	public DevotenessToolTip createDevotenessPersuasion(DevotenessPersuasion devotenessPersuasion){
		 DevotenessToolTip  devotenessToolTip = new DevotenessToolTip();
		if( devotenessPersuasion == null || CollectionUtils.isEmpty(devotenessPersuasion.getShortSummary()))
			return devotenessToolTip;
		List<DevotenessToolTip> data = new ArrayList<>();
		for(CampaignPojo campaignPojo : devotenessPersuasion.getShortSummary()){
			DevotenessToolTip devotenessToolTip1 = new DevotenessToolTip();
			devotenessToolTip1.setTitleText(campaignPojo.getHeading());
			devotenessToolTip1.setIconUrl(campaignPojo.getIconUrl());
			data.add(devotenessToolTip1);
		}
		devotenessToolTip.setData(data);
		devotenessToolTip.setTitleText(polyglotService.getTranslatedData(DEVOTEE_HOVER_TITLE));
		devotenessToolTip.setSubText(polyglotService.getTranslatedData(DEVOTEE_HOVER_SUBTITLE));
		return devotenessToolTip;
	}


	public CancellationTimeline createFreeCancellationTooltip(CancellationTimeline cancellationTimeline){
		CancellationTimeline cancellationToolTip = new CancellationTimeline();
		if(cancellationTimeline != null) {
			cancellationToolTip.setCheckInDate(cancellationTimeline.getCheckInDate());
			cancellationToolTip.setCancellationDate(cancellationTimeline.getCancellationDate());
			cancellationToolTip.setSubTitle(cancellationTimeline.getSubTitle());
			cancellationToolTip.setFreeCancellationBenefits(cancellationTimeline.getFreeCancellationBenefits());
			cancellationToolTip.setFreeCancellationText(cancellationTimeline.getFreeCancellationText());
			cancellationToolTip.setTitle(cancellationTimeline.getTitle());
			cancellationToolTip.setBookingDate(cancellationTimeline.getBookingDate());
			cancellationToolTip.setFcTextForPersuasion(cancellationTimeline.getFcTextForPersuasion());
			cancellationToolTip.setCancellationPolicyTimelineList(cancellationTimeline.getCancellationPolicyTimelineList());
		}
		return cancellationToolTip;
	}

	public ValueStaysTooltip createValueStayToolTip(String countryCode){
		ValueStaysTooltip valueStaysTooltip;
		if (StringUtils.isBlank(countryCode) || "IN".equalsIgnoreCase(countryCode)) {
			valueStaysTooltip = SerializationUtils.clone(valueStaysTooltipDom);
		} else {
			valueStaysTooltip = SerializationUtils.clone(valueStaysTooltipIntl);
		}
		polyglotHelper.translateValueStaysTooltip(valueStaysTooltip);
		return valueStaysTooltip;
	}

	public MyBizAssuredToolTip createMyBizAssuredToolTip() {
		MyBizAssuredToolTip myBizAssuredToolTip;
		myBizAssuredToolTip = SerializationUtils.clone(myBizAssuredTooltipDom);
		polyglotHelper.translateMyBizAssuredTooltip(myBizAssuredToolTip);
		return myBizAssuredToolTip;
	}

	private LuxeToolTip createLuxeToolTip() {
	LuxeToolTip luxeToolTip;
	luxeToolTip = SerializationUtils.clone(luxeToolTipConfig);
	polyglotHelper.translateLuxeToolTip(luxeToolTip);
	return luxeToolTip;
	}


	private Map<String, MySafetyTooltip> createMySafetyTooltipTranslated() {
		Map<String, MySafetyTooltip> mySafetyTooltipTranslated = new HashMap<>();
		List<String> activeLanguagesList = Arrays.asList(activeLanguages.split(","));
		MySafetyTooltip mySafetyTooltipConfig = SerializationUtils.clone(mySafetyDataTooltips);

		for(String lang : activeLanguagesList){
			MySafetyTooltip mySafetyTooltip;
			mySafetyTooltip = polyglotHelper.translateMySafetyToolTip(mySafetyTooltipConfig,lang);
			mySafetyTooltipTranslated.put(lang,mySafetyTooltip);
		}
		return mySafetyTooltipTranslated;
	}

	private Map<String, Object> fetchToolTipPersuasionData(String stayType) {
		Map<String, Object> toolTipData = new HashMap<>();
		Map<String, Object> toolTipConfig = (Map<String, Object>) desktopToolTipPersuasionsMap.get(stayType);
		toolTipData.put("imageUrl", toolTipConfig.get("imageUrl"));
		toolTipData.put("toolTipHeading", polyglotService.getTranslatedData((String) toolTipConfig.get("toolTipHeading")));
		toolTipData.put("toolTipData", new ArrayList<String>());
		List<String> toolTipList = (List<String>) toolTipConfig.get("data");
		for (String toolTipKey : toolTipList) {
			((List<String>) toolTipData.get("toolTipData")).add(polyglotService.getTranslatedData(toolTipKey));
		}
		return toolTipData;
	}

	@Override
	public void addLocationPersuasionToHotelPersuasions(Hotel hotel, List<String> locationPersuasion, LinkedHashSet<String> facilities, ListingSearchRequest searchHotelsRequest, boolean enableAmenities, boolean sectionMyBizSimilarToDirectHtl, String dayUsePersuasionsText, TransportPoi nearestGroundTransportPoi, String drivingTimeText, LocusData locusData, boolean homestayV2Flow){
		boolean isGccListingPage = Utility.isGccOrKsa() && Utility.isListingPage(searchHotelsRequest);
		boolean isHNOrientation= searchHotelsRequest!=null?utility.isExperimentOn(searchHotelsRequest.getExpDataMap(), EXP_MYPARTNER_LISTING_HN):false;
		if(CollectionUtils.isNotEmpty(locationPersuasion)) {
			if (hotel.getHotelPersuasions() == null)
				hotel.setHotelPersuasions(new HashMap<String,Object>());
			PersuasionObject locPers = new PersuasionObject();
			PersuasionObject locPersGCC = new PersuasionObject();
			Set<String> alreadyAddedPersuasions = new HashSet<>();
			locPers.setData(new ArrayList<>());
			locPers.setTemplate("MULTI_PERSUASION_H");
			locPers.setPlaceholder("MULTI");

			int index = 1;
			PersuasionData locPersuasionData = new PersuasionData();
			locPersuasionData.setStyle(new PersuasionStyle());
			List<String> styleClasses = new ArrayList<>();
			styleClasses.add("pc__location");
			boolean isPerNewEnabled = searchHotelsRequest!=null?utility.isExperimentOn(searchHotelsRequest.getExpDataMap(), EXP_PERNEW):false;
			if(isHNOrientation){
				styleClasses.remove("pc__location");
				styleClasses.add("pc__location pc__location_ellipsis_2 darkGreyText");
			}
			else if(isPerNewEnabled){
				styleClasses.remove("pc__location");
				styleClasses.add("pc__locationPerNew");
			}
			locPersuasionData.getStyle().setStyleClasses(styleClasses);
			locPersuasionData.setHtml(true);
			locPersuasionData.setId("LOC_PERSUASION_" + index++);
			locPersuasionData.setPersuasionType("LOCATION");
			locPersuasionData.setHasAction(true);
			locPersuasionData.setActionType("DETAIL_PAGE_MAP");
			if(isGccListingPage) {
				styleClasses.add(STYLE_BLUE_TEXT);
				locPersuasionData.setActionType(ActionType.LISTING_MAP_POPUP.name());
			}

			locPers.getData().add(locPersuasionData);
			if (searchHotelsRequest != null && (searchHotelsRequest.getFeatureFlags() != null && searchHotelsRequest.getFeatureFlags().isOriginListingMap())) {
				if (locationPersuasion.size() > 0) {
                    locPersuasionData.setText("<span class='blueText" + (isHNOrientation ? " latoBold" : "") + "'>" + locationPersuasion.get(0) + "</span>");
                    alreadyAddedPersuasions.add(locationPersuasion.get(0));
                }
			} else if (isGccListingPage && locationPersuasion.size() > 0) {
				if(isPerNewEnabled){
					setTextForLocationPersuasion(locationPersuasion, locPersuasionData, isPerNewEnabled, alreadyAddedPersuasions,isHNOrientation);
				} else {
                    locPersuasionData.setText(locationPersuasion.get(0));
                    alreadyAddedPersuasions.add(locationPersuasion.get(0));
                }
			} else {
				setTextForLocationPersuasion(locationPersuasion, locPersuasionData, isPerNewEnabled, alreadyAddedPersuasions,isHNOrientation);

				// we need to block second element of data for sectionMyBizSimilarToDirectHtl
				if(locationPersuasion.size() > 2 && !sectionMyBizSimilarToDirectHtl && StringUtils.isNotBlank(locPersuasionData.getText())) {
					if(!alreadyAddedPersuasions.contains(locationPersuasion.get(2))) {
						if (isPerNewEnabled) {
							locPersuasionData.setText(locPersuasionData.getText() + "&nbsp;| " + "<span class='latoRegular'>" + locationPersuasion.get(2) + "</span>");
						} else {
							locPersuasionData.setText(locPersuasionData.getText() + "&nbsp;| " + locationPersuasion.get(2));
						}
						alreadyAddedPersuasions.add(locationPersuasion.get(2));
					}
				}

				//For Secondary Location Persuasion, if it is present, add it in the Location Persuasion
				if (locationPersuasion.size() > 3 && StringUtils.isNotBlank(locPersuasionData.getText())) {
					if(!alreadyAddedPersuasions.contains(locationPersuasion.get(3))) {
						if (isPerNewEnabled) {
							locPersuasionData.setText(locPersuasionData.getText() + "&nbsp;| " + "<span class='latoRegular'>" + locationPersuasion.get(3) + "</span>");
						} else {
							locPersuasionData.setText(locPersuasionData.getText() + "&nbsp;| " + locationPersuasion.get(3));
						}
						alreadyAddedPersuasions.add(locationPersuasion.get(3));
					}
				}
			}

			boolean isGroundTransportToBeAdded = !(locationPersuasion.size() > 2 && (locationPersuasion.get(2).contains(METRO) || locationPersuasion.get(2).contains(BUS))) && nearestGroundTransportPoi != null && CollectionUtils.isNotEmpty(nearestGroundTransportPoi.getPoiTags());
			if(isGccListingPage && locationPersuasion.size()>1) {
				//For GCC Listing page location POIs will be in new placeholder with template MULTI_PERSUASION_H
				locPersGCC.setData(new ArrayList<>());
				locPersGCC.setTemplate("MULTI_PERSUASION_H");
				locPersGCC.setPlaceholder("MULTI");
				for(String persuasion : locationPersuasion.subList(1,locationPersuasion.size())) {
					if(alreadyAddedPersuasions.contains(persuasion)) {
						continue;
					}
					PersuasionData locPersuasionDataGCC = new PersuasionData();
					locPersuasionDataGCC.setHasAction(false);
					locPersuasionDataGCC.setHtml(true);
					locPersuasionDataGCC.setId(LOCATION_PERSUASION_ID + index++);
					locPersuasionDataGCC.setPersuasionType(LOCATION);
					locPersuasionDataGCC.setText(persuasion);
					if(index <= locationPersuasion.size() || isGroundTransportToBeAdded) {
						locPersuasionDataGCC.setText(persuasion + COMMA); //Comma to be appended after every persuasion
					}
					locPersuasionDataGCC.setStyle(new PersuasionStyle());
					locPersuasionDataGCC.getStyle().setStyleClasses(new ArrayList<>(Arrays.asList(STYLE_LOCATION, STYLE_DARK_TEXT)));
					locPersGCC.getData().add(locPersuasionDataGCC);
					alreadyAddedPersuasions.add(persuasion);
				}
			}

			// Adding Ground transport Persuasion
			if(ObjectUtils.isNotEmpty(locPersGCC) && locPersGCC.getData() != null && locPersGCC.getData().size()>0 && isGroundTransportToBeAdded) {
				PersuasionData locPersuasionData3 = new PersuasionData();
				locPersuasionData3.setHasAction(false);
				locPersuasionData3.setHtml(true);
				locPersuasionData3.setId(LOCATION_PERSUASION_ID + index++);
				locPersuasionData3.setPersuasionType(LOCATION);
				locPersuasionData3.setStyle(new PersuasionStyle());
				if(nearestGroundTransportPoi.getPoiTags() != null && nearestGroundTransportPoi.getPoiTags().contains(METRO_TAG)) {
					locPersuasionData3.setText("<b>&nbsp;" + METRO_TEXT + "</b>");
				}
				else if(nearestGroundTransportPoi.getPoiTags() != null && (nearestGroundTransportPoi.getPoiTags().contains(BUS_TAG) || nearestGroundTransportPoi.getPoiTags().contains(BUS_TERMINAL_TAG))){
					locPersuasionData3.setText("<b>&nbsp;" + BUS_TEXT + "</b>");
				}
				locPersuasionData3.getStyle().setStyleClasses(new ArrayList<>(Arrays.asList(STYLE_LOCATION, STYLE_DARK_TEXT, STYLE_INLINE_BLOCK)));
				com.mmt.hotels.clientgateway.thirdparty.response.Hover hover = new com.mmt.hotels.clientgateway.thirdparty.response.Hover();
				hover.setData(nearestGroundTransportPoi);
				hover.setTooltipType(TOOL_TIP_GT);
				locPersuasionData3.setHover(hover);
				locPersGCC.getData().add(locPersuasionData3);
			}


			try {
				final String locationPersuasionKey = isHNOrientation ? Constants.LOCATION_PERSUAION_HN_PLACEHOLDER_ID : Constants.LOCATION_PERSUAION_PLACEHOLDER_ID;
				((Map<Object,Object>) hotel.getHotelPersuasions()).put(locationPersuasionKey, locPers);
				if(isGccListingPage) {
					((Map<Object, Object>) hotel.getHotelPersuasions()).put(Constants.LOCATION_PERSUAION_2_PLACEHOLDER_ID, locPersGCC);
				}
			} catch (ClassCastException e) {
				LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
			} catch (Exception e) {
				LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
			}
		}

		if (CollectionUtils.isNotEmpty(facilities) && enableAmenities) {
			if (hotel.getHotelPersuasions() == null)
				hotel.setHotelPersuasions(new HashMap<String,Object>());
			PersuasionObject amenityPers = new PersuasionObject();
			amenityPers.setData(new ArrayList<>());
			amenityPers.setTemplate("MULTI_PERSUASION_H");
			amenityPers.setPlaceholder("MULTI");

			int index = 1;
			Iterator<String> iter = facilities.iterator();
			while (iter.hasNext() && index <=3) {
				PersuasionData amenPersuasionData = new PersuasionData();
				amenPersuasionData.setHasAction(false);
				amenPersuasionData.setHtml(false);
				amenPersuasionData.setId("AMENITIES_" + index++);
				amenPersuasionData.setPersuasionType("AMENITIES");
				amenPersuasionData.setText(iter.next());
				String text = Utility.removeSpecialChar(amenPersuasionData.getText());
				if (StringUtils.isNotBlank(text) && amenetiesWithUrl.contains(text.toLowerCase())) {
					amenPersuasionData.setIconurl("https://promos.makemytrip.com/images/highlighted/" + text.toLowerCase() + ".png");
				} else {
					amenPersuasionData.setIcontype("singleGreyTickIcon");
				}
				amenPersuasionData.setStyle(new PersuasionStyle());
				List<String> styleClasses = new ArrayList<>();
				if(isHNOrientation){
					amenPersuasionData.setIcontype(null);
					amenPersuasionData.setIconurl(null);
					styleClasses.add("pc__hotelCategoryPerNew_hn");
				} else {
					styleClasses.add("pc__amenity");
				}
				amenPersuasionData.getStyle().setStyleClasses(styleClasses);
				amenityPers.getData().add(amenPersuasionData);
			}

			try {
				String amenityPersuasionKey = AMENITIES_PLACEHOLDER_ID;
				if (isHNOrientation) {
					amenityPersuasionKey = AMENITIES_PLACEHOLDER_ID_HN;
					utility.constraintLengthForAmenityPersuasions(amenityPers);
				}
				if (!((Map<Object,Object>) hotel.getHotelPersuasions()).containsKey(amenityPersuasionKey)) {
					((Map<Object,Object>) hotel.getHotelPersuasions()).put(amenityPersuasionKey,amenityPers);
				}
			} catch (ClassCastException e) {
				LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
			} catch (Exception e) {
				LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
			}
		}

		if(searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails()!=null && FUNNEL_DAYUSE.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource()) && StringUtils.isNotEmpty(dayUsePersuasionsText)) {
			if (hotel.getHotelPersuasions() == null)
				hotel.setHotelPersuasions(new HashMap<String,Object>());
			PersuasionObject amenityPers = new PersuasionObject();
			amenityPers.setData(new ArrayList<>());
			amenityPers.setTemplate(IMAGE_TEXT_H);
			amenityPers.setPlaceholder("SINGLE");


			PersuasionData amenPersuasionData = new PersuasionData();
			amenPersuasionData.setHasAction(false);
			amenPersuasionData.setHtml(false);
			amenPersuasionData.setId(DAYUSE_LOCAL_ID);
			amenPersuasionData.setPersuasionType(DAYUSE_LOCAL_ID);
			amenPersuasionData.setStyle(new PersuasionStyle());
			amenPersuasionData.getStyle().setTextColor("#000000");
			amenPersuasionData.setText(dayUsePersuasionsText);
			amenityPers.getData().add(amenPersuasionData);
			try {
				((Map<Object,Object>) hotel.getHotelPersuasions()).put(Constants.AMENITIES_PLACEHOLDER_ID_APP,amenityPers);
			} catch (ClassCastException e) {
				LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
			} catch (Exception e) {
				LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
			}
		}
	}
	 private void setTextForLocationPersuasion(List<String> locationPersuasion, PersuasionData locPersuasionData, boolean isPerNewEnabled, Set<String> alreadyAddedPersuasions, boolean isHNOrientation) {
		 if(alreadyAddedPersuasions == null) {
			 alreadyAddedPersuasions = new HashSet<>();
		 }

		 if (locationPersuasion.size() == 1) {
			 locPersuasionData.setText("<span class='blueText" + (isHNOrientation ? " latoBold" : "") + "'>" +locationPersuasion.get(0)+"</span>");
			 alreadyAddedPersuasions.add(locationPersuasion.get(0));
		 } else if (locationPersuasion.size() >= 2) {
			 locPersuasionData.setText("<span class='blueText" + (isHNOrientation ? " latoBold" : "") + "'>"+locationPersuasion.get(0)+"</span>");
			 alreadyAddedPersuasions.add(locationPersuasion.get(0));
			 if (StringUtils.isNotBlank(locPersuasionData.getText())) {
				 if (!alreadyAddedPersuasions.contains(locationPersuasion.get(1))) {
					 if (isPerNewEnabled) {
						 locPersuasionData.setText(locPersuasionData.getText() + "&nbsp;| " + "<span class='latoRegular'>" + locationPersuasion.get(1) + "</span>");
					 } else {
						 locPersuasionData.setText(locPersuasionData.getText() + "&nbsp;| " + locationPersuasion.get(1));
					 }
					 alreadyAddedPersuasions.add(locationPersuasion.get(1));
				 }
			 }
		 }
	 }

	@Override
	public  BottomSheet buildBottomSheet(PersonalizedResponse<SearchWrapperHotelEntity> perResponse) {
		BottomSheet bottomSheet = new BottomSheet();
		bottomSheet.setHeading(perResponse.getHeading());
		bottomSheet.setSubHeading(perResponse.getHeading());
		bottomSheet.setImgUrl(myBizAssuredUrl);
		bottomSheet.setCta(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_ASSURED_FILTER_CARD_CTA));
		bottomSheet.setCtaAction("");
		List<SectionFeature> sectionFeatureList = new ArrayList<>();
		sectionFeatureList.add(new SectionFeature(highRatedUrl, polyglotService.getTranslatedData(ConstantsTranslation.RATED_HIGH_BT), "grayDot", null, null));
		sectionFeatureList.add(new SectionFeature(gstInvoiceUrl, polyglotService.getTranslatedData(ConstantsTranslation.GST_INVOICE_ASSURANCE_TEXT), "grayDot", null, null));
		sectionFeatureList.add(new SectionFeature(bpgUrl, polyglotService.getTranslatedData(ConstantsTranslation.BPG_TEXT), "grayDot", null, null));
		bottomSheet.setSectionFeatures(sectionFeatureList);
		return bottomSheet;
	}

	public MyBizStaticCard buildStaticCard(String section, List<SearchWrapperHotelEntity> hotels) {
		MyBizStaticCard staticCard = null;
		if(CORPBUDGET_DIRECT_HOTEL.equalsIgnoreCase(section) && CollectionUtils.isNotEmpty(hotels) && !hotels.get(0).isCorpBudgetHotel() &&
				myBizStaticCard != null) {
			staticCard = SerializationUtils.clone(myBizStaticCard);
			staticCard.setActionUrl(hotels.get(0).getDetailDeeplinkUrl());
			translateStaticCard(staticCard);
		}
		return staticCard;
	}

	protected void translateStaticCard(MyBizStaticCard staticCard) {
		staticCard.setText(polyglotService.getTranslatedData(staticCard.getText()));
		staticCard.setSubtext(polyglotService.getTranslatedData(staticCard.getSubtext()));
		staticCard.setCtaText(polyglotService.getTranslatedData(staticCard.getCtaText()));
	}

	//To build location persuasion to Mob-Landing Cards
	public void addLocationPersuasionToHotelPersuasions(SearchWrapperHotelEntity hotelEntity) {
		if (hotelEntity != null && CollectionUtils.isNotEmpty(hotelEntity.getLocationPersuasion())) {
			List<String> locationPersuasion = hotelEntity.getLocationPersuasion();
			if (hotelEntity.getHotelPersuasions() == null)
				hotelEntity.setHotelPersuasions(new HashMap<String, Object>());
			PersuasionObject locPers = new PersuasionObject();
			locPers.setData(new ArrayList<>());
			locPers.setPlaceholder(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID);
			locPers.setTemplate("MULTI_PERSUASION_V");
			locPers.setPlaceholder("MULTI");

			int index = 1;
			PersuasionData locPersuasionData = new PersuasionData();
			locPersuasionData.setHasAction(false);
			locPersuasionData.setStyle(new PersuasionStyle());
			List<String> styleClasses = new ArrayList<>();
			styleClasses.add("pc__location");
			locPersuasionData.getStyle().setStyleClasses(styleClasses);
			locPersuasionData.setHtml(true);
			locPersuasionData.setId("LOC_PERSUASION_" + index++);
			locPersuasionData.setPersuasionType("LOCATION");

			locPers.getData().add(locPersuasionData);

			if (locationPersuasion.size() == 1) {
				locPersuasionData.setText(locationPersuasion.get(0));
			} else if (locationPersuasion.size() >= 2) {
				locPersuasionData.setText(locationPersuasion.get(0) + " | " + locationPersuasion.get(1));
			}

			try {
				((Map<Object, Object>) hotelEntity.getHotelPersuasions()).put(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID, locPers);
			} catch (ClassCastException e) {
				LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ", e.getMessage());
			} catch (Exception e) {
				LOGGER.error("Location Persuasion could not be added due to : {} ", e.getMessage());
			}
		}

	}

	/**
	 * This is an encapsulated method to build all the required persuasions for Hidden Gem card.
	 */
	@Override
	public void addPersuasionsForHiddenGemCard(SearchWrapperHotelEntity hotelEntity) {
		if (hotelEntity.getHotelPersuasions() == null)
			hotelEntity.setHotelPersuasions(new HashMap<String,Object>());
		addLocationPersuasionToHotelPersuasions(hotelEntity);
		buildHiddenGemPersuasion(hotelEntity);
		buildHiddenGemIconPersuasion(hotelEntity);
		buildHomeStaysPersuasion(hotelEntity);
	}

	/**
	 * Method to build Hidden Gem Persuasion, this method calls persuasionUtil to build Hidden Gem persuasion.
	 * If the util method return a non-empty Persuasion List, this method will add that persuasion in Hotel Persuasion object.
	 */
	public void buildHiddenGemPersuasion(SearchWrapperHotelEntity hotelEntity) {
		PersuasionObject hiddenGemPersuasion = persuasionUtil.buildHiddenGemPersuasion(hotelEntity, DEVICE_TYPE);
		if (hiddenGemPersuasion != null && hotelEntity.getHotelPersuasions() instanceof Map<?, ?>) {
			if(CollectionUtils.isNotEmpty(hiddenGemPersuasion.getData()))
				hiddenGemPersuasion.getData().get(0).setPersuasionType(PersuasionType.PEITHO.name());
			hiddenGemPersuasion.setTemplate(PersuasionTemplate.IMAGE_TEXT_H.name());
			hiddenGemPersuasion.setTemplateType(TemplateType.DEFAULT.name());
			hiddenGemPersuasion.setPlaceholder(Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdDesktop());
			MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdDesktop(), hiddenGemPersuasion);
		}
	}

	/**
	 * Method to build Hidden Gem Icon Persuasion, this method calls persuasionUtil to build Hidden Gem Icon persuasion.
	 * If the util method return a non-empty Persuasion List, this method will add that persuasion in Hotel Persuasion object.
	 */
	public void buildHiddenGemIconPersuasion(SearchWrapperHotelEntity hotelEntity) {
		PersuasionObject hiddenGemPersuasion = persuasionUtil.buildHiddenGemIconPersuasion(hotelEntity, DEVICE_TYPE);
		if (hiddenGemPersuasion != null && hotelEntity.getHotelPersuasions() instanceof Map<?, ?>) {
			if(CollectionUtils.isNotEmpty(hiddenGemPersuasion.getData()))
				hiddenGemPersuasion.getData().get(0).setPersuasionType(PersuasionType.HOTEL_CATEGORY.name());
			hiddenGemPersuasion.setTemplateType(TemplateType.DEFAULT.name());
			hiddenGemPersuasion.setPlaceholder(Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdDesktop());
			MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdDesktop(), hiddenGemPersuasion);
		}
	}

	/**
	 * Method to build Home Stays Persuasions, this method calls persuasionUtil to build Homestay persuasions.
	 * If the util method return a non-empty Persuasion List for homestay title, this method will add that persuasion in Hotel Persuasion object.
	 * And, if util method return a non-empty Persuasion List for homestay title and sub-title, this method will add both the persuasions in Hotel Persuasion object, on the same placeholder
	 */
	public void buildHomeStaysPersuasion(SearchWrapperHotelEntity hotelEntity) {
		PersuasionObject homeStaysTitlePersuasion = persuasionUtil.buildHomeStaysTitlePersuasion(hotelEntity, DEVICE_TYPE);
		if (homeStaysTitlePersuasion != null && CollectionUtils.isNotEmpty(homeStaysTitlePersuasion.getData()) && hotelEntity.getHotelPersuasions() instanceof Map<?, ?>) {
			homeStaysTitlePersuasion.setPlaceholder(Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdDesktop());
			MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdDesktop(), homeStaysTitlePersuasion);

			PersuasionObject homeStaysSubTitlePersuasion = persuasionUtil.buildHomeStaysSubTitlePersuasion(hotelEntity, DEVICE_TYPE);
			if (homeStaysSubTitlePersuasion != null && CollectionUtils.isNotEmpty(homeStaysSubTitlePersuasion.getData())) {
				homeStaysSubTitlePersuasion.setPlaceholder(Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdDesktop());
				homeStaysTitlePersuasion.getData().add(homeStaysSubTitlePersuasion.getData().get(0));
				MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdDesktop(), homeStaysTitlePersuasion);
			}
		}
	}

	/**
	 * HTL-40907: Add special fare persuasion to hotel's persuasion map for lowest negotiated rate plan hotels.
	 *
	 * @param hotelEntity
	 */
	@Override
	public void addSpecialFarePersuasion(SearchWrapperHotelEntity hotelEntity) {
		PersuasionObject specialFarePersuasion = getSpecialFarePersuasion(hotelEntity.getCorpAlias());

		if (hotelEntity.getHotelPersuasions() == null)
			hotelEntity.setHotelPersuasions(new HashMap<String, Object>());
		((Map<Object, Object>) hotelEntity.getHotelPersuasions()).put(Persuasions.SPECIAL_FARE_PERSUASION.getPlaceholderIdDesktop(), specialFarePersuasion);
	}

	private PersuasionObject getSpecialFarePersuasion(String corpAlias) {
		PersuasionObject specialFarePersuasion = new PersuasionObject();
		PersuasionData data = new PersuasionData();
		Map<String, PersuasionData> persuasionConfigMap = specialFarePersuasionConfigMap.get(CLIENT_DESKTOP);
		PersuasionData persuasionStyleConfig = MapUtils.isNotEmpty(persuasionConfigMap) ? persuasionConfigMap.get(PC_RIGHT_1_1_PLACEHOLDER) : new PersuasionData();

		String persuasionText = polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_FARE_TAG);
		corpAlias = corpAlias != null ? corpAlias : StringUtils.EMPTY;
        persuasionText = StringUtils.replace(persuasionText, "{CORP_ALIAS}", corpAlias);
		data.setText(persuasionText);
		specialFarePersuasion.setData(Collections.singletonList(data));
		PersuasionStyle persuasionStyle = new PersuasionStyle();
		BeanUtils.copyProperties(persuasionStyleConfig.getStyle() != null ? persuasionStyleConfig.getStyle() : new PersuasionStyle(), persuasionStyle);
		specialFarePersuasion.setStyle(persuasionStyle);
		specialFarePersuasion.setPlaceholder(PC_RIGHT_1_1_PLACEHOLDER);
		specialFarePersuasion.setTemplate(TEXT_IMAGE_H_TEMPLATE);
		specialFarePersuasion.setTemplateType(DEFAULT);
		return specialFarePersuasion;
	}

	@Override
	public void addBookingConfirmationPersuasion(SearchWrapperHotelEntity hotelEntity) {
		PersuasionObject bookingConfirmationPersuasion = new PersuasionObject();
		PersuasionData persuasionData = new PersuasionData();
		Map<String, PersuasionData> persuasionConfigMap = specialFarePersuasionConfigMap.get(CLIENT_DESKTOP);
		PersuasionData persuasionStyleConfig = MapUtils.isNotEmpty(persuasionConfigMap) ? persuasionConfigMap.get(PC_RIGHT_3_PLACEHOLDER) : new PersuasionData();

		persuasionData.setHtml(true);
		persuasionData.setIcontype("icInfoYellow");
		String text = polyglotService.getTranslatedData(ConstantsTranslation.BOOKING_CONFIRMATION_TEXT_LISTING);
		text = StringUtils.replace(text, NO_OF_HOURS_PLACEHOLDER, String.valueOf(noOfHoursForConfirmation));
		persuasionData.setText(text);
		bookingConfirmationPersuasion.setData(Collections.singletonList(persuasionData));

		com.mmt.hotels.clientgateway.thirdparty.response.Hover hover = new com.mmt.hotels.clientgateway.thirdparty.response.Hover();
		String headingText = polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_FARE_TITLE_TEXT);
		headingText = StringUtils.replace(headingText, NO_OF_HOURS_PLACEHOLDER, String.valueOf(noOfHoursForConfirmation));
		hover.setHeadingText(headingText);
		PersuasionStyle hoverStyle = new PersuasionStyle();
		BeanUtils.copyProperties((persuasionStyleConfig.getHover() != null && persuasionStyleConfig.getHover().getStyle() != null) ? persuasionStyleConfig.getHover().getStyle() : new PersuasionStyle(), hoverStyle);
		hover.setStyle(hoverStyle);
		bookingConfirmationPersuasion.setHover(hover);

		PersuasionStyle persuasionStyle = new PersuasionStyle();
		BeanUtils.copyProperties(persuasionStyleConfig.getStyle() != null ? persuasionStyleConfig.getStyle() : new PersuasionStyle(), persuasionStyle);
		bookingConfirmationPersuasion.setStyle(persuasionStyle);

		bookingConfirmationPersuasion.setPlaceholder(PC_RIGHT_3_PLACEHOLDER);
		bookingConfirmationPersuasion.setTemplate(IMAGE_TEXT_H);
		bookingConfirmationPersuasion.setTemplateType(DEFAULT);

		if (hotelEntity.getHotelPersuasions() == null)
			hotelEntity.setHotelPersuasions(new HashMap<String, Object>());
		((Map<Object, Object>) hotelEntity.getHotelPersuasions()).put(PC_RIGHT_3_PLACEHOLDER, bookingConfirmationPersuasion);
	}

	@Override
	public String buildBGColor(String sectionName, String orientation, String cardType) {
		return null;
	}
}
