package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.content.Media;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.PanoramicMedia360;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.ProfessionalMediaEntity;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.TravellerMediaEntity;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelImages;
import com.mmt.hotels.clientgateway.response.staticdetail.Image360.View360Image;
import com.mmt.hotels.clientgateway.response.staticdetail.MediaInfo;
import com.mmt.hotels.clientgateway.response.staticdetail.MediaV2;
import com.mmt.hotels.clientgateway.response.staticdetail.ProfessionalImages;
import com.mmt.hotels.clientgateway.response.staticdetail.TravellerImages;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.ReArchUtility;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.experience.HeroMedia;
import com.mmt.hotels.model.response.staticdata.Image360.GenericPosition;
import com.mmt.hotels.model.response.staticdata.Image360.Image360;
import com.mmt.hotels.model.response.staticdata.Image360.LinkHotspot;
import com.mmt.hotels.model.response.staticdata.Tag;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.CLIENT_DESKTOP;
import static com.mmt.hotels.clientgateway.constants.Constants.LISTING_TYPE_ENTIRE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.STATIC_ROOM_TAG;
import static java.lang.Math.min;

@Component
public class MediaResponseHelper {

    @Autowired
    private ReArchUtility utility;

    @Autowired
    private PolyglotService polyglotService;

    @Value("${view.360.icon.url}")
    private String view360IconUrl;

    @Value("${view.360.persuasionIcon.url}")
    private String view360PersuasionIconUrl;

    @Value("${detail.grid.image.limit}")
    private int detailGridImageLimit;

    @Value("${listing.media.limit.exp}")
    private int listingMediaLimitExp;

    public MediaV2 buildMedia(com.gommt.hotels.orchestrator.detail.model.response.content.Media src, boolean isChatBotEnable, boolean isImageExpEnable, boolean isLuxe, String client, String listingType, boolean isPremiumExperienceEnabled) {
        if (src == null) return null;
        MediaV2 mediaV2 = new MediaV2();
        // Traveller images (group by imgTag)
        mediaV2.setTraveller(setTravellerImages(src, isChatBotEnable));
        mediaV2.setHotel(setHotelImages(src, isChatBotEnable));
        mediaV2.setView360(set360Images(src, isLuxe, client));
        mediaV2.setGrid(setGridImages(src, isImageExpEnable, listingType));
        mediaV2.setHeroMedia(setHeroMedia(src,isPremiumExperienceEnabled));

        // Grid images (professionalMediaEntities)
        return mediaV2;
    }

    private List<HeroMedia> setHeroMedia(Media src, boolean isPremiumExperienceEnabled) {
        if (!isPremiumExperienceEnabled || src == null) {
            return null;
        }

        /* Hero Media */
        List<com.gommt.hotels.orchestrator.detail.model.response.content.experience.HeroMedia> orchestratorHeroMedia = src.getHeroMedia();

        if (CollectionUtils.isNotEmpty(orchestratorHeroMedia)) {
            // Transform orchestrator HeroMedia to client gateway HeroMedia
            List<HeroMedia> clientGatewayHeroMedia = new ArrayList<>(orchestratorHeroMedia.size());
            
            for (com.gommt.hotels.orchestrator.detail.model.response.content.experience.HeroMedia orchestratorMedia : orchestratorHeroMedia) {
                if (orchestratorMedia == null) continue;
                HeroMedia clientMedia = new HeroMedia();
                clientMedia.setMediaUrl(orchestratorMedia.getMediaUrl());
                clientMedia.setThumbnailUrl(orchestratorMedia.getThumbnailUrl());
                clientMedia.setMediaType(orchestratorMedia.getMediaType());
                clientMedia.setGifUrl(orchestratorMedia.getGifUrl());
                clientMedia.setTitle(orchestratorMedia.getTitle());
                clientMedia.setDescription(orchestratorMedia.getDescription());
                clientMedia.setCategory(orchestratorMedia.getCategory());
                
                clientGatewayHeroMedia.add(clientMedia);
            }
            
            return clientGatewayHeroMedia;
        }

        return null;
    }

    private ProfessionalImages setGridImages(com.gommt.hotels.orchestrator.detail.model.response.content.Media src, boolean isImageExpEnable, String listingType) {
        ProfessionalImages grid = new ProfessionalImages();
        if (src.getProfessionalMediaEntities() != null && src.getProfessionalMediaEntities().containsKey("H")) {
            List< com.gommt.hotels.orchestrator.detail.model.response.content.media.ProfessionalMediaEntity> professionalList = src.getProfessionalMediaEntities().get("H");
            if (professionalList != null && !professionalList.isEmpty()) {
                List<com.mmt.hotels.clientgateway.response.staticdetail.MediaInfo> mediaInfos = new ArrayList<>();
                int mediaLimit = isImageExpEnable && Utility.isAppRequest() ? listingMediaLimitExp : detailGridImageLimit;
                if(CollectionUtils.isNotEmpty(professionalList)) {
                    List<MediaInfo> professionalMediaInfos = buildProfessionalImagesFromContentResponse(src.getProfessionalMediaEntities(), listingType);
                    if (CollectionUtils.isNotEmpty(professionalMediaInfos)) {
                        int numOfImages = min(mediaLimit, professionalMediaInfos.size());
                        grid.setImages(professionalMediaInfos.subList(0, numOfImages));
                    }
                }
            }
        }
        return grid;

    }

    private View360Image set360Images(com.gommt.hotels.orchestrator.detail.model.response.content.Media src, boolean isLuxe, String client) {
        com.mmt.hotels.clientgateway.response.staticdetail.Image360.View360Image view360Image = null;
        if (MapUtils.isNotEmpty(src.getPanoramic360()) && src.getPanoramic360().containsKey("H")) {
            view360Image = new com.mmt.hotels.clientgateway.response.staticdetail.Image360.View360Image();
            List<com.gommt.hotels.orchestrator.detail.model.response.content.media.PanoramicMedia360> images = src.getPanoramic360().get("H");
            if (images != null && !images.isEmpty()) {
                // Convert PanoramicMedia360 to legacy Image360 format
                List<com.mmt.hotels.model.response.staticdata.Image360.Image360> image360List = images.stream()
                        .map(this::convertPanoramicToImage360)
                        .collect(Collectors.toList());
                view360Image.setImages(image360List);
                view360Image.setCtaIcon(view360IconUrl);
                view360Image.setPersuasionIcon(view360PersuasionIconUrl);
                view360Image.setCtaText(isLuxe && !CLIENT_DESKTOP.equalsIgnoreCase(client) ? polyglotService.getTranslatedData(ConstantsTranslation.VIEW_360_IMAGE) : polyglotService.getTranslatedData(ConstantsTranslation.VIEW_IMAGE));
            }
        }
        return view360Image;
    }

    /**
     * Convert OrchV2 PanoramicMedia360 to legacy Image360 format.
     */
    private Image360 convertPanoramicToImage360(PanoramicMedia360 panoramic) {
        Image360 image360 = new Image360();
        image360.setId(panoramic.getId());
        image360.setImageUrl(panoramic.getUrl());
        image360.setLinkHotspots(buildLinkHotspots(panoramic));
        image360.setLoadPosition(convertLoadPosition(panoramic));
        image360.setName(panoramic.getName());
        image360.setPanoramaImg(panoramic.getPanoramaImg());
        image360.setPreviewImg(panoramic.getPreviewUrl());
        image360.setRoomCode(panoramic.getRoomCode());
        image360.setSpaceType(panoramic.getSpaceType());
        image360.setThumbnail(panoramic.getThumbnailUrl());
        image360.setTiles(panoramic.getTiles());
        // Note: Some fields may not have exact setters, will be handled in future iterations
        return image360;
    }

    private List<LinkHotspot> buildLinkHotspots(PanoramicMedia360 panoramic) {
        List<LinkHotspot> linkHotspots = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(panoramic.getLinkHotspots())) {
            linkHotspots = panoramic.getLinkHotspots().stream()
                    .map(this::convertLinkHotspots)
                    .collect(Collectors.toList());
        }
        return linkHotspots;
    }

    private LinkHotspot convertLinkHotspots(PanoramicMedia360.LinkHotspot linkHotspot) {
        LinkHotspot hotspot = new LinkHotspot();
        hotspot.setId(linkHotspot.getId());
        hotspot.setMarkerPosition(convertMarkerPosition(linkHotspot.getMarkerPosition()));
        hotspot.setTargetLoadPosition(convertMarkerPosition(linkHotspot.getTargetLoadPosition()));
        return hotspot;
    }

    private GenericPosition convertMarkerPosition(PanoramicMedia360.GenericPosition markerPosition) {
        GenericPosition position = null;
        if (markerPosition != null) {
            position = new GenericPosition();
            position.setYaw(markerPosition.getYaw());
            position.setPitch(markerPosition.getPitch());
        }
        return position;
    }

    private static GenericPosition convertLoadPosition(PanoramicMedia360 panoramic) {
        GenericPosition loadPosition = null;
        if (panoramic.getLoadPosition() != null) {
            loadPosition = new GenericPosition();
            loadPosition.setPitch(panoramic.getLoadPosition().getPitch());
            loadPosition.setYaw(panoramic.getLoadPosition().getYaw());
        }
        return loadPosition;
    }

    private HotelImages setHotelImages(com.gommt.hotels.orchestrator.detail.model.response.content.Media src, boolean isChatBotEnable) {
        // ProfessionalV2 images (tagInfoList)
        HotelImages hotelImages = null;
        if (src.getTagInfoList() != null && src.getTagInfoList().containsKey("H")) {
            hotelImages = new HotelImages();
            List< com.gommt.hotels.orchestrator.detail.model.response.content.media.TagInfo> tagInfos = src.getTagInfoList().get("H");
            List<com.mmt.hotels.model.response.staticdata.Tag> hotelTags = new ArrayList<>();
            for ( com.gommt.hotels.orchestrator.detail.model.response.content.media.TagInfo tagInfo : tagInfos) {
                com.mmt.hotels.model.response.staticdata.Tag tag = new com.mmt.hotels.model.response.staticdata.Tag();
                tag.setName(tagInfo.getName());
                if (isChatBotEnable) {
                    tag.setChatBotHook(utility.buildChatbotHook(tag.getName()));
                }
                if ((Constants.Street_View.equalsIgnoreCase(tag.getName()))) {
                    tag.setTagIconUrl(view360PersuasionIconUrl);
                }
                List<com.mmt.hotels.model.response.staticdata.Subtag> subtagList = new ArrayList<>();
                if (tagInfo.getSubTagInfoList() != null) {
                    for ( com.gommt.hotels.orchestrator.detail.model.response.content.media.SubTagInfo subTagInfo : tagInfo.getSubTagInfoList()) {
                        com.mmt.hotels.model.response.staticdata.Subtag subtag = new com.mmt.hotels.model.response.staticdata.Subtag();
                        subtag.setName(subTagInfo.getName());
                        subtag.setAccess(subTagInfo.getAccess());
                        subtag.setAccessType(subTagInfo.getAccessType());
                        subtag.setSpaceId(subTagInfo.getSpaceId());
                        subtag.setText(subTagInfo.getText());
                        List<com.mmt.hotels.model.response.staticdata.ImageData> data = new ArrayList<>();
                        if (subTagInfo.getData() != null) {
                            for ( com.gommt.hotels.orchestrator.detail.model.response.content.media.RoomEntity img : subTagInfo.getData()) {
                                com.mmt.hotels.model.response.staticdata.ImageData d = new com.mmt.hotels.model.response.staticdata.ImageData();

                                // Basic fields from MediaEntity
                                d.setUrl(img.getUrl());
                                d.setTitle(img.getTitle());
                                d.setThumbnailURL(img.getThumbnailUrl());

                                // Set mediaType - either from source or default to IMAGE
                                d.setMediaType(img.getMediaType() != null ? img.getMediaType() : "IMAGE");

                                // Additional fields from MediaEntity
                                if (img.getDescription() != null) {
                                    d.setDescription(img.getDescription());
                                }
                                if (img.getPreviewUrl() != null) {
                                    d.setPreviewUrl(img.getPreviewUrl());
                                }

                                // Room-specific fields from RoomEntity
                                if (img.getRoomCode() != null) {
                                    d.setRoomCode(img.getRoomCode());
                                }
                                if (img.getRoomName() != null) {
                                    d.setRoomName(img.getRoomName());
                                }

                                // Note: Traveler-specific fields (travelerName, travellerImage, userReview,
                                // travelerRating, reviewCount, date) are not available in RoomEntity
                                // They are only available in TravellerMediaEntity

                                data.add(d);
                            }
                        }
                        subtag.setData(data);
                        subtagList.add(subtag);
                    }
                }
                tag.setSubtags(subtagList);
                hotelTags.add(tag);
            }
            hotelImages.setTags(hotelTags);
        }
        return hotelImages;
    }

    private TravellerImages setTravellerImages(com.gommt.hotels.orchestrator.detail.model.response.content.Media src, boolean isChatBotEnable) {
        List<com.mmt.hotels.model.response.staticdata.Tag> tags = null;
        TravellerImages travellerImages = null;
        if (src.getTraveller() != null && src.getTraveller().containsKey("H")) {
            travellerImages = new TravellerImages();
            List< com.gommt.hotels.orchestrator.detail.model.response.content.media.TravellerMediaEntity> travellerList = src.getTraveller().get("H");
            Map<String, List< TravellerMediaEntity>> spaceNameToImageEntityMap = new HashMap<>();
            for ( com.gommt.hotels.orchestrator.detail.model.response.content.media.TravellerMediaEntity entity : travellerList) {
                String key = entity.getImgTag();
                if (!spaceNameToImageEntityMap.containsKey(key)) {
                    spaceNameToImageEntityMap.put(key, new ArrayList<>());
                }
                spaceNameToImageEntityMap.get(key).add(entity);
            }
            // Convert to tags
            tags = new ArrayList<>();
            for (Map.Entry<String, List< com.gommt.hotels.orchestrator.detail.model.response.content.media.TravellerMediaEntity>> entry : spaceNameToImageEntityMap.entrySet()) {
                com.mmt.hotels.model.response.staticdata.Tag tag = new com.mmt.hotels.model.response.staticdata.Tag();
                tag.setName(entry.getKey());
                if (isChatBotEnable) {
                    tag.setChatBotHook(utility.buildChatbotHook(tag.getName()));
                }
                com.mmt.hotels.model.response.staticdata.Subtag subtag = new com.mmt.hotels.model.response.staticdata.Subtag();
                subtag.setName(entry.getKey());
                List<com.mmt.hotels.model.response.staticdata.ImageData> data = new ArrayList<>();
                for ( com.gommt.hotels.orchestrator.detail.model.response.content.media.TravellerMediaEntity travellerImageEntity : entry.getValue()) {
                    com.mmt.hotels.model.response.staticdata.ImageData imageData = new com.mmt.hotels.model.response.staticdata.ImageData();
                    imageData.setDate(travellerImageEntity.getDate());
                    imageData.setTravelerName(travellerImageEntity.getTravellerName());
                    imageData.setMediaType("IMAGE");
                    imageData.setUrl(travellerImageEntity.getUrl());
                    imageData.setTitle(travellerImageEntity.getTitle());
                    imageData.setThumbnailURL(travellerImageEntity.getThumbnailUrl());
                    imageData.setDescription(travellerImageEntity.getDescription());
                    data.add(imageData);
                }
                subtag.setData(data);
                List<com.mmt.hotels.model.response.staticdata.Subtag> subtagList = new ArrayList<>();
                subtagList.add(subtag);
                tag.setSubtags(subtagList);
                tags.add(tag);
            }
            // Sort tags if imageTagOrder is present
            LinkedHashSet<String> imageTagOrder = src.getProfessionalImageTagOrder();
            if (imageTagOrder != null && !imageTagOrder.isEmpty() && CollectionUtils.isNotEmpty(tags)) {
                sortTravellerTagsBasedImageTagsOrder(tags, new ArrayList<>(imageTagOrder));
            }
            travellerImages.setTags(tags);
        }
        return travellerImages;

    }

    // Add this method to support tag sorting in mediaV2 mapping
    private static void sortTravellerTagsBasedImageTagsOrder(List<com.mmt.hotels.model.response.staticdata.Tag> tags, List<String> imageTagsOrderList) {
        if (imageTagsOrderList == null || imageTagsOrderList.isEmpty() || tags == null || tags.isEmpty()) {
            return;
        }
        tags.sort(new Comparator<Tag>() {
            @Override
            public int compare(com.mmt.hotels.model.response.staticdata.Tag tag1, com.mmt.hotels.model.response.staticdata.Tag tag2) {
                // Keep the "Others" tag at the last
                if ("Others".equalsIgnoreCase(tag1.getName())) {
                    return 1;
                } else if ("Others".equalsIgnoreCase(tag2.getName())) {
                    return -1;
                }
                int index1 = imageTagsOrderList.indexOf(tag1.getName());
                int index2 = imageTagsOrderList.indexOf(tag2.getName());
                if (index2 == -1)
                    return -1;
                else if (index1 == -1)
                    return 1;
                else
                    return Integer.compare(index1, index2);
            }
        });
    }

    private List<MediaInfo> buildProfessionalImagesFromContentResponse(Map<String, List<ProfessionalMediaEntity>> hotelImage, String listingType){

        List<MediaInfo> professionalMediaInfos = new ArrayList<>();

        if (MapUtils.isNotEmpty(hotelImage)) {
            List<ProfessionalMediaEntity> professionalList = hotelImage.get("H");
            if (CollectionUtils.isNotEmpty(professionalList)) {
                for (ProfessionalMediaEntity professionalImageEntity : professionalList) {
                    MediaInfo mediaInfo = new MediaInfo();
                    mediaInfo.setFilterInfo(professionalImageEntity.getFilterInfo());
                    mediaInfo.setMediaType("IMAGE");
                    mediaInfo.setTags(professionalImageEntity.getSeekTags());
                    mediaInfo.setThumbnailURL(professionalImageEntity.getThumbnailUrl());
                    mediaInfo.setTitle(professionalImageEntity.getTitle());
                    mediaInfo.setUrl(professionalImageEntity.getUrl());
                    professionalMediaInfos.add(mediaInfo);
                }
            }
            professionalList = hotelImage.get("R");
            //HTL-41751 Not Merging RoomImages in GalleryImages when listingType is Entire Property
            //as same set of images were coming twice, once in gallery images and second time in room images.
            if (CollectionUtils.isNotEmpty(professionalList) && !LISTING_TYPE_ENTIRE.equalsIgnoreCase(listingType)) {
                for (ProfessionalMediaEntity professionalImageEntity : professionalList) {
                    MediaInfo mediaInfo = new MediaInfo();
                    mediaInfo.setMediaType("IMAGE");
                    mediaInfo.setTags(professionalImageEntity.getSeekTags());
                    String roomTag = polyglotService.getTranslatedData(STATIC_ROOM_TAG);
                    if (mediaInfo.getTags() != null) {
                        mediaInfo.getTags().add(roomTag);
                    } else {
                        List<String> tags = new ArrayList<String>();
                        tags.add(roomTag);
                        mediaInfo.setTags(tags);
                    }
                    mediaInfo.setFilterInfo(roomTag);
                    mediaInfo.setThumbnailURL(professionalImageEntity.getThumbnailUrl());
                    mediaInfo.setTitle(professionalImageEntity.getTitle());
                    mediaInfo.setUrl(professionalImageEntity.getUrl());
                    mediaInfo.setRoomCode(professionalImageEntity.getRoomCode());
                    professionalMediaInfos.add(mediaInfo);
                }
            }
        }

        return professionalMediaInfos;
    }
}
