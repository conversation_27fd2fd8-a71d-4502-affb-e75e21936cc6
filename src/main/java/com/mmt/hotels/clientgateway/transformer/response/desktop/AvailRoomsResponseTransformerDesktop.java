package com.mmt.hotels.clientgateway.transformer.response.desktop;

import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.DeviceConstant;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.Style;
import com.mmt.hotels.clientgateway.transformer.response.AvailRoomsResponseTransformer;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.SPECIAL_FARE_TAG_SMALL_STYLE;

@Component
public class AvailRoomsResponseTransformerDesktop extends AvailRoomsResponseTransformer {
    @Override
    public void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap) {
        persuasionUtil.buildLoyaltyCashbackPersuasions(coupon, persuasionMap);
    }

    @Override
    public PersuasionResponse buildSpecialFareTagPersuasion(String corpAlias) {
        PersuasionResponse specialFarePersuasion = new PersuasionResponse();
        Style style = new Style();
        style.setStyleClass(SPECIAL_FARE_TAG_SMALL_STYLE);
        specialFarePersuasion.setStyle(style);
        String title = polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_FARE_TAG);
        corpAlias = corpAlias != null ? corpAlias : StringUtils.EMPTY;
        title = StringUtils.replace(title, "{CORP_ALIAS}", corpAlias);
        specialFarePersuasion.setTitle(title);
        return specialFarePersuasion;
    }
}
