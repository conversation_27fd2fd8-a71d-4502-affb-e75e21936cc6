package com.mmt.hotels.clientgateway.transformer.response.desktop;


import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.response.ListingMapResponseTransformer;
import com.mmt.model.LocusData;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ListingMapResponseTransformerDesktop extends ListingMapResponseTransformer {

  private static final Logger LOGGER = LoggerFactory.getLogger(ListingMapResponseTransformerDesktop.class);

  @Override
  public void addLocationPersuasionToHotelPersuasions(Hotel hotel, List<String> locationPersuasion,String drivingTimeText,LocusData locusData,RequestDetails requestDetails) {
    if (CollectionUtils.isNotEmpty(locationPersuasion)) {
      if (hotel.getHotelPersuasions() == null) {
        hotel.setHotelPersuasions(new HashMap<String, Object>());
      }
      PersuasionObject locPers = new PersuasionObject();
      locPers.setData(new ArrayList<>());
      locPers.setPlaceholder(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID);
      locPers.setTemplate("IMAGE_TEXT_H");
      locPers.setPlaceholder("SINGLE");

      int index = 1;
      PersuasionData locPersuasionData = new PersuasionData();
      locPersuasionData.setHasAction(false);
      locPersuasionData.setHtml(true);
      locPersuasionData.setId("LOC_PERSUASION_" + index++);
      locPersuasionData.setPersuasionType("LOCATION");

      locPers.getData().add(locPersuasionData);
      if (locationPersuasion.size() > 0) {
        locPersuasionData.setText(locationPersuasion.get(0));
      }

      try {
        ((Map<Object, Object>) hotel.getHotelPersuasions())
            .put(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID, locPers);
      } catch (ClassCastException e) {
        LOGGER.error("Location Persuasion could not be added in Listing Map due to ClassCastException : {} ",
                     e.getMessage());
      } catch (Exception e) {
        LOGGER.error("Location Persuasion could not be added in Listing Map due to : {} ", e.getMessage());
      }
    }
  }
}
