package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.gommt.hotels.orchestrator.enums.BNPLVariant;
import com.gommt.hotels.orchestrator.model.response.da.CancellationTimeline;
import com.gommt.hotels.orchestrator.model.response.listing.CancellationPenalty;
import com.gommt.hotels.orchestrator.model.response.listing.HotelDetails;
import com.gommt.hotels.orchestrator.model.response.listing.ListingResponse;
import com.gommt.hotels.orchestrator.model.response.listing.PriceDetail;
import com.gommt.hotels.orchestrator.model.response.listing.RatePlanInclusions;
import com.gommt.hotels.orchestrator.model.response.listing.ResponseRateFlags;
import com.gommt.hotels.orchestrator.model.response.listing.ResponseRatePlan;
import com.gommt.hotels.orchestrator.model.response.listing.RoomEntity;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.constants.ControllerConstants;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicy;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicyType;
import com.mmt.hotels.clientgateway.response.BookedInclusion;
import com.mmt.hotels.clientgateway.response.IconType;
import com.mmt.hotels.clientgateway.response.Margin;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.PricingDetails;
import com.mmt.hotels.clientgateway.response.Style;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.listing.UpsellRateplanResponse;
import com.mmt.hotels.clientgateway.response.rooms.RoomTariff;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomRatePlan;
import com.mmt.hotels.clientgateway.response.rooms.Tariff;
import com.mmt.hotels.clientgateway.response.rooms.UpsellRateplans;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.util.Currency;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.mypartner.MarkUpDetails;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.HotelRates;
import com.mmt.hotels.model.response.pricing.PaymentMode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Type;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.AE;
import static com.mmt.hotels.clientgateway.constants.Constants.ANDROID;
import static com.mmt.hotels.clientgateway.constants.Constants.B2C;
import static com.mmt.hotels.clientgateway.constants.Constants.BG_URL;
import static com.mmt.hotels.clientgateway.constants.Constants.BREAKFAST;
import static com.mmt.hotels.clientgateway.constants.Constants.BUT_SEPARATOR;
import static com.mmt.hotels.clientgateway.constants.Constants.COLOR;
import static com.mmt.hotels.clientgateway.constants.Constants.DEFAULT_CUR_INR;
import static com.mmt.hotels.clientgateway.constants.Constants.DEVICE_IOS;
import static com.mmt.hotels.clientgateway.constants.Constants.DINNER;
import static com.mmt.hotels.clientgateway.constants.Constants.EP_MEAL_PLAN;
import static com.mmt.hotels.clientgateway.constants.Constants.EXP_BNPL_NEW_VARIANT;
import static com.mmt.hotels.clientgateway.constants.Constants.GEC;
import static com.mmt.hotels.clientgateway.constants.Constants.HORIZONTAL_MARGIN;
import static com.mmt.hotels.clientgateway.constants.Constants.LUNCH;
import static com.mmt.hotels.clientgateway.constants.Constants.MEAL;
import static com.mmt.hotels.clientgateway.constants.Constants.MEAL_UPSELL_CATEGORY;
import static com.mmt.hotels.clientgateway.constants.Constants.NEW_SELECT_ROOM_PAGE;
import static com.mmt.hotels.clientgateway.constants.Constants.OTHERS;
import static com.mmt.hotels.clientgateway.constants.Constants.PACKAGE_RATE;
import static com.mmt.hotels.clientgateway.constants.Constants.PERSUASION_BG_URL;
import static com.mmt.hotels.clientgateway.constants.Constants.POPULAR_PACKAGE;
import static com.mmt.hotels.clientgateway.constants.Constants.PRICE_TOP;
import static com.mmt.hotels.clientgateway.constants.Constants.Per_Night;
import static com.mmt.hotels.clientgateway.constants.Constants.SMALL;
import static com.mmt.hotels.clientgateway.constants.Constants.TEXT_WITH_BG_IMAGE;
import static com.mmt.hotels.clientgateway.constants.Constants.TITLE;
import static com.mmt.hotels.clientgateway.constants.Constants.USP;
import static com.mmt.hotels.clientgateway.constants.Constants.VERTICAL_MARGIN;
import static com.mmt.hotels.clientgateway.constants.Constants.ZPN;
import static com.mmt.hotels.clientgateway.constants.Constants.inclusionOrderList;
import static com.mmt.hotels.clientgateway.constants.Constants.per_night;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.BNPL_GCC_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.GIFT_CARD_TEXT;

@Component
public class OrchUpsellRatePlanResponseTransformer {

    @Autowired
    private Utility utility;

    @Autowired
    private DateUtil dateUtil;

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    @Value("${food.rating.thresold}")
    private int foodRatingThresold;

    @Value("${dot.icon.url}")
    private String dotIconUrl;

    @Value("${los.icon.url.room}")
    private String losIconUrl;

    @Value("${los.position.select.room}")
    private int losPositionSelectRoom;

    @Value("${red.cross.Icon}")
    private String redCrossIcon;

    @Value("${pah.without.cc.text}")
    private String pahWithoutCCText;

    @Value("${pah.with.cc.text}")
    private String pahWithCCText;

    @Value("${pah.gcc.text}")
    private String pahGccText;

    @Value("${free.kids.inclusion.icon.url}")
    private String freeChildInclusionIcon;

    @Autowired
    CommonResponseTransformer commonResponseTransformer;


    private Map<String, String> addOnInfoMostPopularTag;

    private Map<String, String> mealPlanMapPolyglot;

    private boolean mealplanFilterEnable;

    private Map<String, Map<String, Map<String, String>>> ratePlanNameMapRedesign;

    @Value("${red.cross.icon}")
    private String redCrossIconUrl;

    @Value("${green.tick.icon}")
    private String greenTickIcon;

    @Value("${searchrooms.rateplan.redesign}")
    private String ratePlanNameConfigRedesign;

    @Value("${searchrooms.rateplan.name.config}")
    private String ratePlanNameConfig;

    @Value("${addon.info.most.popular.tag}")
    private String addOnInfoMostPopularTagConfig;

    private Map<String, Map<String, Map<String, String>>> ratePlanNameMap;

    private int apLimitForInclusionIcons = 2;

    private static final Gson gson = new Gson();

    private static final Logger LOGGER = LoggerFactory.getLogger(OrchUpsellRatePlanResponseTransformer.class);

    @PostConstruct
    public void init() {
        mealplanFilterEnable = commonConfigConsul.isMealplanFilterEnable();
        mealPlanMapPolyglot = commonConfigConsul.getMealPlanMapPolyglot();
        addOnInfoMostPopularTag = gson.fromJson(addOnInfoMostPopularTagConfig, HashMap.class);
        apLimitForInclusionIcons = commonConfigConsul.getApLimitForInclusionIcons();
        ratePlanNameMapRedesign = gson.fromJson(ratePlanNameConfigRedesign, new TypeToken<Map<String, Map<String, Map<String, String>>>>() {
        }.getType());

        ratePlanNameMap = gson.fromJson(ratePlanNameConfig, new TypeToken<Map<String, Map<String, Map<String, String>>>>() {
        }.getType());
    }

    public UpsellRateplanResponse convertUpsellRatePlanResponse(ListingResponse listingResponse, SearchHotelsRequest searchHotelsRequest, CommonModifierResponse commonModifierResponse) {
        List<SelectRoomRatePlan> selectRoomRatePlans = new ArrayList<>();
        String baseroomCode = "";
        String roomName = "";
        String baseRatePlanCode = searchHotelsRequest.getSearchCriteria().getBaseRateplanCode();
        if (listingResponse == null || CollectionUtils.isEmpty(listingResponse.getPersonalizedSections()))
            return null;
        listingResponse.getPersonalizedSections().get(0).getHotels().get(0).getRooms().get(0).getRatePlans();
        String checkIn = searchHotelsRequest.getSearchCriteria().getCheckIn();
        String checkOut = searchHotelsRequest.getSearchCriteria().getCheckOut();
        int los = dateUtil.getDaysDiff(LocalDate.parse(checkIn), LocalDate.parse(checkOut));
        int ap = dateUtil.getDaysDiff(LocalDate.now(), LocalDate.parse(checkIn));
        for (HotelDetails searchWrapperHotelEntity : listingResponse.getPersonalizedSections().get(0).getHotels()) {
            List<ResponseRatePlan> ratePlans = new ArrayList<>();
            ResponseRatePlan baseRatePlan = null;
            for (RoomEntity roomEntity : searchWrapperHotelEntity.getRooms()) {
                for (ResponseRatePlan ratePlan : roomEntity.getRatePlans()) {
                    if (baseRatePlanCode != null && baseRatePlanCode.equalsIgnoreCase(ratePlan.getCode())) {
                        baseroomCode = roomEntity.getCode();
                        baseRatePlan = ratePlan;
                        continue;
                    } else {
                        roomName = ratePlan.getRoomName();
                    }
                    ratePlans.add(ratePlan);
                }
                boolean isBlockPAH = false;//listingResponse.getExpData() != null && listingResponse.getExpData().containsKey("blockPAH") && org.apache.commons.lang3.StringUtils.isNotBlank(listingResponse.getExpData().get("blockPAH")) && Boolean.parseBoolean(listingResponse.getExpData().get("blockPAH"));
                String askedCurrency = searchHotelsRequest.getSearchCriteria() != null ? searchHotelsRequest.getSearchCriteria().getCurrency() : "INR";
                selectRoomRatePlans = getUpsellRatePlans(searchWrapperHotelEntity.getListingType(),
                        searchHotelsRequest.getExpData(), askedCurrency, baseRatePlan != null ? baseRatePlan.getSellableType() : "", searchHotelsRequest.getRequestDetails().getFunnelSource(),
                        los, ap, isBlockPAH, commonModifierResponse, searchWrapperHotelEntity.isAltAcco(), ratePlans, searchWrapperHotelEntity.isHighSellingAltAcco());
            }
        }
        if (CollectionUtils.isEmpty(selectRoomRatePlans))
            return null;
        UpsellRateplanResponse upsellRateplanResponse = new UpsellRateplanResponse();
        UpsellRateplans upsellRateplans = new UpsellRateplans();
        upsellRateplans.setRoomCode(baseroomCode);
        upsellRateplans.setRatePlans(selectRoomRatePlans);
        upsellRateplans.setRoomName(roomName);
        upsellRateplanResponse.setUpsellRateplans(upsellRateplans);
        return upsellRateplanResponse;
    }

    private List<SelectRoomRatePlan> getUpsellRatePlans(String listingType, String expData, String askedCurrency,
                                                        String sellableType, String funnelSource, int days, int ap, boolean isBlockPAH,
                                                        CommonModifierResponse commonModifierResponse, boolean isAltAccoHotel,
                                                        List<ResponseRatePlan> ratePlansHes, boolean isHighSellingAltAcco) {
        List<SelectRoomRatePlan> ratePlans = new ArrayList<>();
        Map<String, List<ResponseRatePlan>> rpcRatePlanMap = new LinkedHashMap<>();
        boolean isBnplOneVariant = false;
        Map<String, String> experimentDataMap = utility.getExpDataMap(expData);
        if (MapUtils.isNotEmpty(experimentDataMap)) {
            isBnplOneVariant = experimentDataMap.containsKey(EXP_BNPL_NEW_VARIANT) && Boolean.parseBoolean(experimentDataMap.get(EXP_BNPL_NEW_VARIANT));
        }
        Comparator<ResponseRatePlan> compRatePlanPrice = Comparator.comparing(h -> h.getPrice().getDisplayPrice());
        List<ResponseRatePlan> sortedRatePlanList = ratePlansHes.stream().sorted(compRatePlanPrice).collect(Collectors.toList());
        for (ResponseRatePlan ratePlanHes : sortedRatePlanList) {
            String ratePlanCode = ratePlanHes.getCode();
            rpcRatePlanMap.computeIfAbsent(ratePlanCode, k -> new ArrayList<>());
            rpcRatePlanMap.get(ratePlanCode).add(ratePlanHes);
        }
        //need to add mostPopular tag
        int count = 0;
        for (String rpc : rpcRatePlanMap.keySet()) {
            SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
            ResponseRateFlags rateFlags = rpcRatePlanMap.get(rpc).get(0).getRateFlags() != null ? rpcRatePlanMap.get(rpc).get(0).getRateFlags() : new ResponseRateFlags();
            BNPLVariant bnplVariant = rateFlags.getBnplVariant();
            ratePlan.setRpc(rpc);
            ratePlan.setVendorRatePlanCode(rpcRatePlanMap.get(rpc).get(0).getRpcc());
            ratePlan.setPayMode(rpcRatePlanMap.get(rpc).get(0).getPaymentMode());
            ratePlan.setInclusionsList(transformInclusions(rpcRatePlanMap.get(rpc).get(0), mealPlanMapPolyglot, ap, isBlockPAH, expData, askedCurrency, true, null));

            ratePlan.setTariffs(getTariffs(rpcRatePlanMap.get(rpc), expData, askedCurrency, sellableType, days, funnelSource, isAltAccoHotel, isHighSellingAltAcco));


            ratePlan.setCancellationPolicy(transformCancellationPolicy(rpcRatePlanMap.get(rpc).get(0), false));
            ratePlan.setName(getRatePlanName(rpcRatePlanMap.get(rpc).get(0).getMealPlans(), ratePlan.getCancellationPolicy(), sellableType, listingType, expData));
            ratePlan.setSellableType(listingType);
            if (count == 0) {
                List<PersuasionResponse> persuasions = new ArrayList<>();
                PersuasionResponse persuasionResponse = new PersuasionResponse();
                persuasionResponse.setPlaceholderId(PRICE_TOP);
                persuasionResponse.setId(POPULAR_PACKAGE);
                persuasionResponse.setPersuasionText(addOnInfoMostPopularTag.get(TITLE));
                persuasionResponse.setTemplate(TEXT_WITH_BG_IMAGE);
                Style style = new Style();
                style.setTextColor(addOnInfoMostPopularTag.get(COLOR));
                style.setBgUrl(addOnInfoMostPopularTag.get(PERSUASION_BG_URL));
                Margin margin = new Margin();
                margin.setHorizontal(addOnInfoMostPopularTag.get(HORIZONTAL_MARGIN));
                margin.setVertical(addOnInfoMostPopularTag.get(VERTICAL_MARGIN));
                style.setMargin(margin);
                style.setFontSize(SMALL);
                persuasionResponse.setStyle(style);
                persuasions.add(persuasionResponse);
                ratePlan.setPersuasions(persuasions);
                ratePlan.setBgUrl(addOnInfoMostPopularTag.get(BG_URL));

            }
            count++;
            ratePlans.add(ratePlan);
        }

        return ratePlans;
    }


    public List<Tariff> getTariffs(List<ResponseRatePlan> list, String expData, String askedCurrency, String sellableType, int days, String funnelSource,
                                   boolean isAltAccoHotel, boolean isHighSellingAltAcco) {
        if (CollectionUtils.isEmpty(list))
            return null;
        List<Tariff> tariffList = new ArrayList<>();
        Type type = new com.google.gson.reflect.TypeToken<Map<String, String>>() {
        }.getType();
        expData = expData.replaceAll("^\"|\"$", "");
        for (ResponseRatePlan ratePlan : list) {
            Tariff tariff = new Tariff();
            tariff.setTariffCode(ratePlan.getCode());
            tariff.setOccupancydetails(new RoomTariff());
            if (CollectionUtils.isNotEmpty(ratePlan.getRoomTariffs())) {
                int adultCount = 0, roomCount = 0, childCount = 0;
                List<Integer> childrenAges = new ArrayList<>();
                for (com.gommt.hotels.orchestrator.model.response.listing.RoomTariff roomTariff : ratePlan.getRoomTariffs()) {
                    adultCount += roomTariff.getAdult();
                    roomCount += roomTariff.getRoomCount();
                    childCount += roomTariff.getChildren();
                    if (CollectionUtils.isNotEmpty(roomTariff.getChildrenAges())) {
                        childrenAges.addAll(roomTariff.getChildrenAges());
                    }
                }
                tariff.getOccupancydetails().setNumberOfAdults(adultCount);
                tariff.getOccupancydetails().setNumberOfChildren(childCount);
                if (CollectionUtils.isNotEmpty(childrenAges)) {
                    tariff.getOccupancydetails().setChildAges(childrenAges);
                }
                tariff.getOccupancydetails().setRoomCount(roomCount);
                tariff.setAvailCount(adultCount + childCount);
            }
            if (ratePlan.getRateFlags() != null && ratePlan.getRateFlags().getBnplVariant() != null) {
                tariff.setBnplApplicable(BNPLVariant.BNPL_NOT_APPLICABLE != ratePlan.getRateFlags().getBnplVariant());
            }

            tariff.setRoomTariffs(getRoomTariffs(ratePlan.getRoomTariffs()));
            tariff.setMtKey("DEFAULT");
            tariff.setPriceMap(ratePlan.getPrice() == null ? null : getPriceMap(ratePlan, expData, tariff.getOccupancydetails().getRoomCount(),
                    askedCurrency, sellableType, days, isAltAccoHotel, isHighSellingAltAcco));
            tariff.setDefaultPriceKey("DEFAULT");
            tariffList.add(tariff);
        }
        return tariffList;
    }

    private List<RoomTariff> getRoomTariffs(List<com.gommt.hotels.orchestrator.model.response.listing.RoomTariff> roomTariff) {
        if (CollectionUtils.isEmpty(roomTariff))
            return null;
        List<RoomTariff> roomTariffs = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.model.response.listing.RoomTariff room : roomTariff) {
            RoomTariff roomTariffCG = new RoomTariff();
            roomTariffCG.setNumberOfAdults(room.getAdult());
            roomTariffCG.setNumberOfChildren(room.getChildren());
            roomTariffCG.setDisplayPrice(room.getDisplayPrice());
            if (room.getChildren() > 0) {
                roomTariffCG.setChildAges(room.getChildrenAges());
            }
            roomTariffs.add(roomTariffCG);
        }
        return roomTariffs;
    }

    private void buildBaseFare(List<PricingDetails> pricingDetails, PriceDetail priceDetail) {
        if (priceDetail.getBasePrice() > 0.0d) {
            PricingDetails baseFare = new PricingDetails();
            baseFare.setAmount(priceDetail.getBasePrice());
            baseFare.setKey(Constants.BASE_FARE_KEY);
            baseFare.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.BASE_FARE_LABEL));
            baseFare.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
            pricingDetails.add(baseFare);
        }
    }

    private void buildPriceAfterDiscount(List<PricingDetails> pricingDetails, PriceDetail priceDetail) {
        PricingDetails priceAfterDiscount = new PricingDetails();
        priceAfterDiscount.setAmount(priceDetail.getDisplayPrice());
        priceAfterDiscount.setKey(Constants.PRICE_AFTER_DISCOUNT_KEY);
        priceAfterDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_AFTER_DISCOUNT_LABEL));
        priceAfterDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
        pricingDetails.add(priceAfterDiscount);
    }


    private void buildTotalDiscounts(List<PricingDetails> pricingDetails, PriceDetail priceDetail) {
        double totalDiscountAmount = 0.0d;
        List<PricingDetails> priceBreakup = new ArrayList<>();

        /* Build OfferDetailMap from Pricer breakup if available */

        if (priceDetail.getMmtDiscount() > 0.0d) {
            PricingDetails mmtDiscount = new PricingDetails();
            mmtDiscount.setAmount(priceDetail.getMmtDiscount());
            mmtDiscount.setKey(Constants.MMT_DISCOUNT_KEY);
            mmtDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.HOTELIER_DISCOUNT_LABEL));
            mmtDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
            priceBreakup.add(mmtDiscount);
            totalDiscountAmount += mmtDiscount.getAmount();
        }

        if (priceDetail.getBlackDiscount() > 0.0d) {
            PricingDetails blackDiscount = new PricingDetails();
            blackDiscount.setAmount(priceDetail.getBlackDiscount());
            blackDiscount.setKey(Constants.BLACK_DISCOUNT_KEY);
            // For GCC, MMT_SELECT Program runs hence picking Select Label in this case
            // For IN, MMT_BLACK Program runs hence picking Black label in this case
            blackDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.GCC_SELECT_DISCOUNT_LABEL));
            blackDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
            priceBreakup.add(blackDiscount);
            totalDiscountAmount += blackDiscount.getAmount();
        }

        if (priceDetail.getCouponDiscount() > 0.0d) {
            PricingDetails cdfDiscount = new PricingDetails();
            cdfDiscount.setAmount(priceDetail.getCouponDiscount());
            cdfDiscount.setKey(Constants.CDF_DISCOUNT_KEY);
            cdfDiscount.setLabel(priceDetail.getCouponCode() != null ? priceDetail.getCouponCode() : polyglotService.getTranslatedData(ConstantsTranslation.CDF_DISCOUNT_LABEL));
            cdfDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));

            if (priceDetail.getTaxBreakUp() != null && priceDetail.getTaxBreakUp().getServiceFee() > 0.0d && priceDetail.getCouponDiscount() > priceDetail.getTaxBreakUp().getServiceFee()) {
                List<PricingDetails> cdfCouponBreakup = new ArrayList<>();
                PricingDetails serviceFeeReversal = new PricingDetails();
                serviceFeeReversal.setKey(Constants.SERVICE_FEES_REVERSAL_KEY);
                serviceFeeReversal.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.SERVICE_FEES_REVERSAL_LABLE));
                serviceFeeReversal.setAmount(priceDetail.getTaxBreakUp().getServiceFee());
                serviceFeeReversal.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
                cdfCouponBreakup.add(serviceFeeReversal);
                PricingDetails effectiveCouponApplied = new PricingDetails();
                effectiveCouponApplied.setKey(Constants.EFFECTIVE_COUPON_APPLIED_KEY);
                effectiveCouponApplied.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.EFFECTIVE_COUPON_APPLIED_LABLE));
                effectiveCouponApplied.setAmount(priceDetail.getCouponDiscount() - priceDetail.getTaxBreakUp().getServiceFee());
                effectiveCouponApplied.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
                cdfCouponBreakup.add(effectiveCouponApplied);
                cdfDiscount.setBreakup(cdfCouponBreakup);
            }
            priceBreakup.add(cdfDiscount);
            totalDiscountAmount += cdfDiscount.getAmount();
        }

        if (totalDiscountAmount > 0.0d) {
            PricingDetails totalDiscount = new PricingDetails();
            totalDiscount.setAmount(totalDiscountAmount);
            totalDiscount.setKey(Constants.TOTAL_DISCOUNT_KEY);
            totalDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.TOTAL_DISCOUNT_LABEL));
            totalDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_DIFF));
            totalDiscount.setBreakup(priceBreakup);
            pricingDetails.add(totalDiscount);
        }
    }


    public List<PricingDetails> getPricingDetails(PriceDetail priceDetail, String countryCode, String expData) {
        if (priceDetail != null) {
            List<PricingDetails> pricingDetails = new ArrayList<>();
            buildBaseFare(pricingDetails, priceDetail);
            buildTotalDiscounts(pricingDetails, priceDetail);
            buildPriceAfterDiscount(pricingDetails, priceDetail);
            buildTaxesAndServiceFee(pricingDetails, priceDetail, countryCode, expData);
            buildTotalAmount(pricingDetails, priceDetail);

            return pricingDetails;
        }
        return null;
    }

    private void buildTotalAmount(List<PricingDetails> pricingDetails, PriceDetail priceDetail) {
        PricingDetails totalAmount = new PricingDetails();
        totalAmount.setAmount(priceDetail.getDisplayPrice());
        totalAmount.setKey(Constants.TOTAL_AMOUNT_KEY);
        totalAmount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
        totalAmount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.TOTAL_AMOUNT_LABEL));
        pricingDetails.add(totalAmount);
    }

    private void buildTaxesAndServiceFee(List<PricingDetails> pricingDetails, PriceDetail priceDetail, String countryCode, String expData) {
        double totalTaxesAndSrvcFee = 0.0d;
        if (priceDetail.getTaxBreakUp() != null) {
            if (priceDetail.getTaxBreakUp().getHotelTax() > 0.0d) {
                double hotelTaxAmount = priceDetail.getTaxBreakUp().getHotelTax();
                if (priceDetail.getTaxBreakUp().getHotelServiceCharge() > 0.0d) {
                    hotelTaxAmount -= priceDetail.getTaxBreakUp().getHotelServiceCharge();
                    PricingDetails serviceCharge = new PricingDetails();
                    serviceCharge.setAmount(priceDetail.getTaxBreakUp().getHotelServiceCharge());
                    serviceCharge.setKey(Constants.SERVICE_CHARGE_KEY);
                    serviceCharge.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.SERVICE_CHARGE_LABEL));
                    serviceCharge.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
                    totalTaxesAndSrvcFee += serviceCharge.getAmount();
                }
                PricingDetails hotelTax = new PricingDetails();
                hotelTax.setAmount(hotelTaxAmount);
                hotelTax.setKey(Constants.HOTEL_TAX_KEY);
                hotelTax.setLabel(Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode) ? polyglotService.getTranslatedData(ConstantsTranslation.GST_LABEL) : polyglotService.getTranslatedData(ConstantsTranslation.HOTEL_TAX_LABEL));
                hotelTax.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
                totalTaxesAndSrvcFee += hotelTax.getAmount();
            }
            if (priceDetail.getTaxBreakUp().getServiceFee() > 0.0d) {
                PricingDetails serviceFee = new PricingDetails();
                serviceFee.setAmount(priceDetail.getTaxBreakUp().getServiceFee());
                serviceFee.setKey(Constants.SERVICE_FEES_KEY);
                serviceFee.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.SERVICE_FEES_LABEL));
                serviceFee.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
                totalTaxesAndSrvcFee += serviceFee.getAmount();
            }
        }
        if (totalTaxesAndSrvcFee > 0.0d) {
            PricingDetails taxesAndSrvcFee = new PricingDetails();
            taxesAndSrvcFee.setAmount(totalTaxesAndSrvcFee);
            taxesAndSrvcFee.setKey(Constants.TAXES_KEY);
            //Excluded Charges text to be shown above taxes for GCC on review page
            if (Utility.isGCC() && utility.isExperimentOn(utility.getExpDataMap(expData), GEC)) {
                taxesAndSrvcFee.setSubLine(polyglotService.getTranslatedData(ConstantsTranslation.TAXES_EXCLUDED_TEXT_REVIEW_PAGE));
            }
            if (priceDetail.getTaxBreakUp().getHotelTax() > 0.0d || priceDetail.getTaxBreakUp().getServiceFee() > 0.0d) {
                taxesAndSrvcFee.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.TAXES_LISTING_DETAIL_LABEL));
            }
            taxesAndSrvcFee.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
            pricingDetails.add(taxesAndSrvcFee);
        }
    }


    public TotalPricing getTotalPricing(PriceDetail priceDetail, String countryCode, String expData, String currency) {
        TotalPricing totalPricing = new TotalPricing();
        if (priceDetail != null) {
            totalPricing.setDetails(getPricingDetails(priceDetail, countryCode, expData));
            if (CollectionUtils.isEmpty(totalPricing.getCoupons())) {
                totalPricing.setNoCouponText(polyglotService.getTranslatedData(ConstantsTranslation.NO_COUPON_AVAILABLE_TEXT));
            }

        }
        totalPricing.setCurrency(currency);
        return totalPricing;
    }


    public Map<String, TotalPricing> getPriceMap(ResponseRatePlan ratePlan, String expData, Integer roomCount, String askedCurrency, String sellableType, Integer nightCount,
                                                 boolean isAltAccoHotel,  boolean isHighSellingAltAcco) {
        PriceDetail priceDetail = ratePlan.getPrice();
        String priceDisplayMessage = (null == roomCount) ? null : commonResponseTransformer.getPriceDisplayMessage(expData, roomCount, sellableType,
                nightCount, false, isAltAccoHotel, isHighSellingAltAcco);
        Map<String, String> expDataMap = utility.getExpDataMap(expData);
        boolean isNewSelectRoomPage = utility.isExperimentTrue(expDataMap, NEW_SELECT_ROOM_PAGE);
        String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
        if (isNewSelectRoomPage && Per_Night.equalsIgnoreCase(priceDisplayMessage) && (client.equalsIgnoreCase(ANDROID) || client.equalsIgnoreCase(DEVICE_IOS))) {
            priceDisplayMessage = per_night;
        }
        Map<String, TotalPricing> priceMap = new HashMap<>();
        if (priceDetail.getCouponDiscount() > 0.0) {
            String couponCode = priceDetail.getCouponCode();
            priceMap.put(couponCode, getTotalPricing(priceDetail, "", expData, askedCurrency));
            priceMap.get(couponCode).setCouponAmount(priceDetail.getCouponDiscount());
            priceMap.get(couponCode).setPriceDisplayMsg(priceDisplayMessage);
            priceMap.get(couponCode).setPriceTaxMsg(commonResponseTransformer.getShowTaxMessage(expData, roomCount, priceDetail.getTotalTax(), askedCurrency));
        } else {
            priceMap.put("DEFAULT", getTotalPricing(priceDetail, "", expData, askedCurrency));
            priceMap.get("DEFAULT").setPricingKey("DEFAULT");
            priceMap.get("DEFAULT").setPriceDisplayMsg(priceDisplayMessage);
            priceMap.get("DEFAULT").setPriceTaxMsg(commonResponseTransformer.getShowTaxMessage(expData, roomCount, priceDetail.getTotalTax(), askedCurrency));
        }
        return priceMap;
    }


    public String getRatePlanName(List<com.gommt.hotels.orchestrator.model.response.listing.MealPlan> mealPlanList, BookedCancellationPolicy cancellationPolicy, String sellableType, String listingType, String expData) {

        String ratePlanName = StringUtils.EMPTY;

        String mealCode = CollectionUtils.isNotEmpty(mealPlanList) ? mealPlanList.get(0).getCode() : StringUtils.EMPTY;

        String cancellationPolicyString = Constants.CANCELLATION_TYPE_NR;
        if (cancellationPolicy != null && cancellationPolicy.getType() != null) {
            if (Constants.CANCELLATION_TYPE_FC.equalsIgnoreCase(cancellationPolicy.getType().name()))
                cancellationPolicyString = Constants.CANCELLATION_TYPE_FC;
            else if (Constants.CANCELLATION_TYPE_NR.equalsIgnoreCase(cancellationPolicy.getType().name()))
                cancellationPolicyString = Constants.CANCELLATION_TYPE_NR;
            else if (Constants.CANCELLATION_TYPE_FCZPN.equalsIgnoreCase(cancellationPolicy.getType().name()))
                cancellationPolicyString = Constants.CANCELLATION_TYPE_FCZPN;
        }

        sellableType = Constants.SELLABLE_ENTIRE_TYPE.equalsIgnoreCase(listingType) ? Constants.SELLABLE_ENTIRE_TYPE :
                StringUtils.isBlank(sellableType) ? (Constants.SELLABLE_ROOM_TYPE).toUpperCase() : sellableType.toUpperCase();

        Map<String, String> expDataMap = utility.getExpDataMap(expData);

        // this new variable is required because now we can have 2 values for ratePlanNameConfiguration and the relevant one will be added here
        Map<String, Map<String, Map<String, String>>> ratePlanNameMapTemp;
        // we have created a different configuration for name and it is only used when we get RATE_PLAN_REDESIGN as true form pokus
        if (utility.isRatePlanRedesign(expDataMap)) {
            ratePlanNameMapTemp = ratePlanNameMapRedesign;
        } else {
            ratePlanNameMapTemp = ratePlanNameMap;
        }
        if (MapUtils.isNotEmpty(ratePlanNameMapTemp)) {
            if (!ratePlanNameMapTemp.containsKey(mealCode)) {
                if (ratePlanNameMapTemp.get(Constants.DEFAULT).get(cancellationPolicyString).containsKey(sellableType))
                    ratePlanName = ratePlanNameMapTemp.get(Constants.DEFAULT).get(cancellationPolicyString).get(sellableType);
                else
                    ratePlanName = ratePlanNameMapTemp.get(Constants.DEFAULT).get(cancellationPolicyString).get(Constants.SELLABLE_ROOM_TYPE);
            } else {
                if (ratePlanNameMapTemp.get(mealCode).get(cancellationPolicyString).containsKey(sellableType))
                    ratePlanName = ratePlanNameMapTemp.get(mealCode).get(cancellationPolicyString).get(sellableType);
                else
                    ratePlanName = ratePlanNameMapTemp.get(mealCode).get(cancellationPolicyString).get(Constants.SELLABLE_ROOM_TYPE);
            }
        }
        LOGGER.debug(ratePlanName);
        return utility.getTranslationFromPolyglot(ratePlanName);
    }


    public BookedCancellationPolicy transformCancellationPolicy(ResponseRatePlan responseRatePlan, boolean bnplApplicable) {
        BookedCancellationPolicy bookedCancellationPolicy = new BookedCancellationPolicy();
        String partialRefundText = buildPartialRefundDateText(responseRatePlan.getCancellationTimeline());
        String freeCancellationText = responseRatePlan.getCancellationTimeline() != null ? responseRatePlan.getCancellationTimeline().getFreeCancellationText() : "";
        List<CancellationPenalty> cancelPenalty = responseRatePlan.getCancellationPolicy() != null ? responseRatePlan.getCancellationPolicy().getPenalties() : new ArrayList<>();
        boolean isFC = false;
        boolean isPC = false;
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(cancelPenalty) && null != cancelPenalty.get(0).getPenaltyType()) {
            if (CancelPenalty.CancellationType.FREE_CANCELLATON.getValue().equalsIgnoreCase(cancelPenalty.get(0).getPenaltyType())) {
                isFC = true;
            } else if (CancelPenalty.CancellationType.PARTIAL_REFUNDABLE.getValue().equalsIgnoreCase(cancelPenalty.get(0).getPenaltyType())) {
                isPC = true;
            }
        }
        bookedCancellationPolicy.setIconUrl((isFC || isPC) ? greenTickIcon : redCrossIconUrl);
        buildCancellationTexts(bnplApplicable, isFC, bookedCancellationPolicy, freeCancellationText, isPC, partialRefundText);
        return bookedCancellationPolicy;
    }

    private void buildCancellationTexts(boolean bnplApplicable, boolean isFC, BookedCancellationPolicy bookedCancellationPolicy, String freeCancellationText, boolean isPC, String partialRefundText) {
        if (isFC) {
            bookedCancellationPolicy.setIconType(IconType.DOUBLETICK);
            bookedCancellationPolicy.setText(freeCancellationText);
            bookedCancellationPolicy.setType(BookedCancellationPolicyType.FC);
            if (bnplApplicable) {
                bookedCancellationPolicy.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.CANCEL_POLICY_BNPL_SUBTEXT));
            }
        } else if (isPC) {
            bookedCancellationPolicy.setIconType(IconType.DOUBLETICK);
            bookedCancellationPolicy.setText(partialRefundText);
            bookedCancellationPolicy.setType(BookedCancellationPolicyType.FC);
        } else {
            bookedCancellationPolicy.setIconType(IconType.BIGCROSS);
            String amendablePolicyText = null;
            /* TODO Fix this
            if (CollectionUtils.isNotEmpty(cancelPenalty) && cancelPenalty.get(0) != null && cancelPenalty.get(0).getAmendmentPolicies() != null &&
                    DATE.equalsIgnoreCase(cancelPenalty.get(0).getAmendmentPolicies().getName())) {
                LOGGER.debug("PolicyName: {} and MetaData: {}", cancelPenalty.get(0).getAmendmentPolicies().getName(), cancelPenalty.get(0).getAmendmentPolicies().getMetaData());
                amendablePolicyText = utility.getAmendableTextForNRPolicy(cancelPenalty.get(0).getAmendmentPolicies(), controller);
            }
            if (StringUtils.isNotEmpty(amendablePolicyText)) {
                String text = polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_TEXT) + BUT_SEPARATOR + amendablePolicyText.toLowerCase();
                bookedCancellationPolicy.setText(text);
            } else */
            {
                bookedCancellationPolicy.setText(polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_TEXT));
            }
            bookedCancellationPolicy.setType(BookedCancellationPolicyType.NR);
        }
    }


    public String buildPartialRefundDateText(CancellationTimeline cancellationTimeline) {
        String partialRefundDateText = "";
        if (cancellationTimeline == null)
            return partialRefundDateText;

        String checkInDate = cancellationTimeline.getCheckInDate();
        String refundDate = "", refundDateTime = "";

        if (CollectionUtils.isNotEmpty(cancellationTimeline.getCancellationPolicyTimelineList()) && cancellationTimeline.getCancellationPolicyTimelineList().get(0) != null) {
            refundDate = cancellationTimeline.getCancellationPolicyTimelineList().get(0).getEndDate();
            refundDateTime = cancellationTimeline.getCancellationPolicyTimelineList().get(0).getEndDateTime();
        }

        boolean isCheckInAndRefundDateSame = false;
        if (StringUtils.isNotEmpty(checkInDate) && StringUtils.isNotEmpty(refundDate)) {
            isCheckInAndRefundDateSame = checkInDate.equalsIgnoreCase(refundDate);
        }

        if (isCheckInAndRefundDateSame) {
            partialRefundDateText = polyglotService.getTranslatedData(ConstantsTranslation.PARTIAL_REFUNDABLE_CHECKIN_TEXT);
        } else {
            String refundText = polyglotService.getTranslatedData(ConstantsTranslation.PARTIAL_REFUNDABLE_WITH_REFUNDDATE_TEXT);
            if (StringUtils.isNotEmpty(refundDate) && StringUtils.isNotEmpty(refundDateTime) && StringUtils.isNotEmpty(refundText)) {
                partialRefundDateText = MessageFormat.format(refundText, refundDate, refundDateTime);
            } else {
                partialRefundDateText = polyglotService.getTranslatedData(ConstantsTranslation.PARTIAL_REFUNDABLE_TEXT);
            }
        }

        return partialRefundDateText;
    }


    public List<BookedInclusion> transformInclusions(ResponseRatePlan ratePlanHES, Map<String, String> mealPlanMap,
                                                     int ap, boolean isBlockPah, String expData, String askedCurrency, boolean cfarBnplFlag, HotelRates hotelRates) {
        List<BookedInclusion> inclusions = new ArrayList<>();
        List<com.gommt.hotels.orchestrator.model.response.listing.MealPlan> mealPlan = ratePlanHES.getMealPlans();
        List<RatePlanInclusions> inclusionList = ratePlanHES.getInclusions();
        String supplierCode = ratePlanHES.getSupplierCode();
        Map<String, String> experimentDataMap = utility.getExpDataMap(expData);
        List<RatePlanInclusions> inclusionListCopy = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inclusionList)) {
            inclusionListCopy.addAll(inclusionList);
        }

        boolean isMealPlanPresent = false;
        int count = 0;
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(inclusionList)) {
            inclusionList.sort(Comparator.comparing(RatePlanInclusions::getCategory, Comparator.nullsLast(Comparator.naturalOrder())));
            for (RatePlanInclusions inclusion : inclusionList) {
                if (StringUtils.isEmpty(inclusion.getName()))
                    continue;
                count++;
                BookedInclusion bookedInclusion = new BookedInclusion();
                bookedInclusion.setText(inclusion.getCategory());
                bookedInclusion.setCode(inclusion.getCategory());
                bookedInclusion.setSubText(inclusion.getName());
                bookedInclusion.setCategory(inclusion.getCategory());
                if (StringUtils.isNotEmpty(inclusion.getCategory()) && (BREAKFAST.equalsIgnoreCase(inclusion.getCategory()) || LUNCH.equalsIgnoreCase(inclusion.getCategory()) || DINNER.equalsIgnoreCase(inclusion.getCategory()))) {
                    bookedInclusion.setCategory(MEAL);
                }
                if (StringUtils.isNotBlank(bookedInclusion.getText()) && bookedInclusion.getText().length() < 10 && !bookedInclusion.getText().equalsIgnoreCase(bookedInclusion.getSubText())) {
                    bookedInclusion.setText(bookedInclusion.getText() + " - " + bookedInclusion.getSubText());
                }
                bookedInclusion.setIconType(IconType.DEFAULT);
                bookedInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Persuasion_Icons/Dot.png");
                if (count < 2 && !isMealPlanPresent && CollectionUtils.isNotEmpty(mealPlan)) {
                    if (Constants.MEAL_PLAN_CODE_BREAKFAST.equalsIgnoreCase(mealPlan.get(0).getCode()))
                        isMealPlanPresent = true;
                    else if (!Constants.MEAL_PLAN_CODE_ROOM_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode()) &&
                            !Constants.MEAL_PLAN_CODE_BED_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode()) &&
                            !Constants.MEAL_PLAN_CODE_ACC_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode()))
                        isMealPlanPresent = true;
                    if (isMealPlanPresent) {
                        bookedInclusion.setInclusionCode(mealPlan.get(0).getCode());
                        bookedInclusion.setType("MEAL_PLAN");
                        if (StringUtils.isNotEmpty(bookedInclusion.getCategory()) && !bookedInclusion.getCategory().equalsIgnoreCase(MEAL_UPSELL_CATEGORY)) {
                            bookedInclusion.setCategory(MEAL);
                        }
                        bookedInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/meal.png");
                    }
                }
                if ("Packages".equalsIgnoreCase(inclusion.getCategory()) || "Packages1".equalsIgnoreCase(inclusion.getCategory())
                        || "Packages2".equalsIgnoreCase(inclusion.getCategory()) || "Packages3".equalsIgnoreCase(inclusion.getCategory())
                        || "MMTBLACK".equalsIgnoreCase(inclusion.getCategory())) {
                    bookedInclusion.setOnOffer(true);
                    //bookedInclusion.setIconUrl(inclusion.getImageURL()); //TODO Fix this
                    bookedInclusion.setCategory(USP);
                }
                if (!"Packages3".equalsIgnoreCase(inclusion.getCategory()) && StringUtils.isNotEmpty(inclusion.getCategory())) {
                    bookedInclusion.setBookable(true);
                }
                if ("MEAL_PLAN".equalsIgnoreCase(bookedInclusion.getType())) {
                    inclusions.add(bookedInclusion);
                } else {
                    inclusions.add(0, bookedInclusion);
                }
            }
        }

        buildNoMealInclusions(experimentDataMap, mealPlan, mealPlanMap, inclusions, isMealPlanPresent, supplierCode, ap);
        String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
        if (!Constants.PAS.equalsIgnoreCase(ratePlanHES.getPaymentMode())) {

            BookedInclusion pahInclusion = new BookedInclusion();
            if (AE.equalsIgnoreCase(region)) {
                pahInclusion.setCode(pahGccText);
                pahInclusion.setText(polyglotService.getTranslatedData(ConstantsTranslation.PAH_GCC_TEXT));
            }
            pahInclusion.setIconType(IconType.DEFAULT);
            pahInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Persuasion_Icons/Dot.png");
            pahInclusion.setInclusionCode(ratePlanHES.getPaymentMode());
            pahInclusion.setType("PAY_MODE");
            pahInclusion.setCategory(ZPN);
            inclusions.add(0, pahInclusion);
        }

        if (ratePlanHES.getCancellationPolicy() != null && CollectionUtils.isNotEmpty(ratePlanHES.getCancellationPolicy().getPenalties()) && ratePlanHES.getCancellationPolicy().getPenalties().get(0).getPenaltyType() != null) {
            String penaltyType = ratePlanHES.getCancellationPolicy().getPenalties().get(0).getPenaltyType();
            if (penaltyType.equalsIgnoreCase(CancelPenalty.CancellationType.FREE_CANCELLATON.getValue()) && !isBlockPah && cfarBnplFlag) {
                /* For FCZPN add an inclusion at start */
                BookedInclusion fczpnInlclusions = new BookedInclusion();
                setInclusionCodeAndText(fczpnInlclusions, region, askedCurrency);
                fczpnInlclusions.setType(Constants.CANCELLATION_TYPE_FCZPN);
                fczpnInlclusions.setIconType(IconType.DEFAULT);
                fczpnInlclusions.setIconUrl("https://promos.makemytrip.com/Hotels_product/Persuasion_Icons/Dot.png");
                fczpnInlclusions.setInclusionCode(Constants.CANCELLATION_TYPE_FCZPN);
                fczpnInlclusions.setCategory(ZPN);
                inclusions.add(0, fczpnInlclusions);
            }
        }

        //		We have added category for inclusions that are made so that we can order them in a specific order based on it
        if (utility.isRatePlanRedesign(experimentDataMap) || experimentDataMap.containsKey(ExperimentKeys.BLACK_REVAMP.getKey())) {
            inclusions = reorderInclusions(inclusions);
        }

        if (utility.isMealRackRate(experimentDataMap)) {
            inclusions = transformMealInclusions(inclusions, mealPlan, askedCurrency);
        }


        //inclusions.add(bookedInclusion);
        return inclusions;
    }

    public List<BookedInclusion> transformMealInclusions(List<BookedInclusion> inclusions, List<com.gommt.hotels.orchestrator.model.response.listing.MealPlan> mealPlan, String askedCurrency) {
        for (BookedInclusion inclusion : inclusions) {
            if (StringUtils.isNotEmpty(inclusion.getCode()) && StringUtils.isNotEmpty(inclusion.getCategory()) && inclusion.getCategory().equalsIgnoreCase(MEAL_UPSELL_CATEGORY)) {
                String amountValue = inclusion.getAmount();
                inclusion.setText(inclusion.getSubText());
                inclusion.setCode(inclusion.getSubText());
                inclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/meal.png");
                inclusion.setSubText(getMealUpsellSubtext(amountValue, mealPlan, askedCurrency));
            }
        }
        return inclusions;
    }

    public String getMealUpsellSubtext(String value, List<com.gommt.hotels.orchestrator.model.response.listing.MealPlan> mealPlan, String askedCurrency) {
        String mealUpsellSubtext = polyglotService.getTranslatedData(ConstantsTranslation.MEAL_UPSELL_SUBTEXT);
        String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : DEFAULT_CUR_INR).getCurrencySymbol();
        mealUpsellSubtext = mealUpsellSubtext.replace("{cur}", currencySymbol);
        if (CollectionUtils.isNotEmpty(mealPlan) && mealPlan.get(0) != null && !mealPlan.get(0).getCode().equalsIgnoreCase(EP_MEAL_PLAN)) {
            return String.format(mealUpsellSubtext, value);
        }
        return null;
    }

    private void buildNoMealInclusions(Map<String, String> experimentDataMap, List<com.gommt.hotels.orchestrator.model.response.listing.MealPlan> mealPlan, Map<String, String> mealPlanMap, List<BookedInclusion> inclusions, boolean isMealPlanPresent, String supplierCode, int ap) {
        // this piece of code was to be reused in for reordering of inclusion so created the function that can be called from anywhere it is needed
        if (!isMealPlanPresent && org.apache.commons.collections4.CollectionUtils.isNotEmpty(mealPlan) && MapUtils.isNotEmpty(mealPlanMap) && !(MapUtils.isNotEmpty(experimentDataMap)
                && Constants.TRUE.equalsIgnoreCase(experimentDataMap.get(Constants.NO_MEAL_INCLUSION_REMOVE)))) {
            if (Constants.MEAL_PLAN_CODE_ROOM_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode()) || Constants.MEAL_PLAN_CODE_BED_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode()) || Constants.MEAL_PLAN_CODE_ACC_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode())) {
                BookedInclusion noMeanInclusion = new BookedInclusion();
                noMeanInclusion.setText(polyglotService.getTranslatedData(mealPlanMap.get(mealPlan.get(0).getCode())));
                noMeanInclusion.setCode(noMeanInclusion.getText());
                if (ap < 2) {
                    noMeanInclusion.setIconType(IconType.DEFAULT);
                    noMeanInclusion.setIconUrl(dotIconUrl);
                } else {
                    noMeanInclusion.setIconType(IconType.CROSS);
                    noMeanInclusion.setIconUrl(redCrossIcon);
                }
                noMeanInclusion.setInclusionCode(mealPlan.get(0).getCode());
                noMeanInclusion.setCategory(MEAL);
                inclusions.add(0, noMeanInclusion);
            } else if (StringUtils.isNotBlank(supplierCode) && !Constants.SUPPLIER_INGO.equalsIgnoreCase(supplierCode)) {
                BookedInclusion noMeanInclusion = new BookedInclusion();
                noMeanInclusion.setText(mealPlanMap.containsKey(mealPlan.get(0).getCode()) ? polyglotService.getTranslatedData(mealPlanMap.get(mealPlan.get(0).getCode())) : mealPlan.get(0).getValue());
                noMeanInclusion.setCode(noMeanInclusion.getText());
                noMeanInclusion.setInclusionCode(mealPlan.get(0).getCode());
                noMeanInclusion.setType("MEAL_PLAN");
                noMeanInclusion.setCategory(MEAL);
                noMeanInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/meal.png");
                inclusions.add(0, noMeanInclusion);

            }
        }
    }

    private void setInclusionCodeAndText(BookedInclusion fczpnInlclusions, String region, String askedCurrency) {
        String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : DEFAULT_CUR_INR).getCurrencySymbol();
        String bnpText = polyglotService.getTranslatedData(BNPL_GCC_TEXT);
        bnpText = bnpText != null ? bnpText.replace("{cur}", currencySymbol) : "";
        fczpnInlclusions.setCode(bnpText);
        fczpnInlclusions.setText(bnpText);
    }

    private List<BookedInclusion> reorderInclusions(List<BookedInclusion> inclusions) {
//		this function orders the inculsion based on ratePlanCategoryList {kids -> zpn -> meal -> others, usp}
        List<BookedInclusion> newInclusions = new ArrayList<>();
        Map<String, List<BookedInclusion>> mp = new HashMap<>();
        for (String s : inclusionOrderList) {
            mp.put(s, new ArrayList<>());
        }
        inclusions.forEach(e -> {
            if (StringUtils.isNotEmpty(e.getCategory()) && mp.containsKey(e.getCategory())) {
                mp.get(e.getCategory()).add(e);
            } else {
                mp.get(OTHERS).add(e);
            }
        });
        for (String s : inclusionOrderList) {
            newInclusions.addAll(mp.get(s));
        }
        return newInclusions;
    }
}
