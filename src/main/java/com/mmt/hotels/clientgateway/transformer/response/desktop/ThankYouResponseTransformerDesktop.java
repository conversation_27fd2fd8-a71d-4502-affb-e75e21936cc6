package com.mmt.hotels.clientgateway.transformer.response.desktop;

import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.thankyou.ThankYouResponse;
import com.mmt.hotels.clientgateway.transformer.response.ThankYouResponseTransformer;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.UserGlobalInfo;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.txn.PersistanceMultiRoomResponseEntity;
import com.mmt.hotels.model.response.txn.PersistedMultiRoomData;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

@Component
public class ThankYouResponseTransformerDesktop extends ThankYouResponseTransformer {

    @Value("${hotelDetail.deeplink.url.desktop}")
    private String hotelDetailsRawDeepLink;

    @Value("${hotelDetail.deeplink.url.desktop.global}")
    private String hotelDetailsRawDeepLinkGlobal;

    @Value("${thankyou.mytrips.deeplink.desktop}")
    private String myTripsDeeplink;

    @Value("${thankyou.mytrips.deeplink.desktop.global}")
    private String myTripsDeeplinkGlobal;

    private static final Logger LOGGER = LoggerFactory.getLogger(AvailRoomsResponseTransformerDesktop.class);

    @Override
    public void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap) {
        PersuasionResponse persuasion = new PersuasionResponse();
        StringBuilder persuasionAppliedText=new StringBuilder();
        if(StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage()) ) {
            LOGGER.debug("loyalty_offer_message: {}", coupon.getLoyaltyOfferMessage());
            persuasionAppliedText.append(String.format(polyglotService.getTranslatedData(ConstantsTranslation.LOYALTY_OFFER_TEXT), coupon.getLoyaltyOfferMessage()));
        }
        else{
            LOGGER.debug("Promo_Cash_Amount: {}",coupon.getHybridDiscounts().get(CASHBACK_TO_WALLET));
            int cashbackDiscountAmtRounded = (int) Math.round(coupon.getHybridDiscounts().get(CASHBACK_TO_WALLET));
            persuasionAppliedText.append(String.format(polyglotService.getTranslatedData(ConstantsTranslation.CASHBACK_OFFER_TEXT),cashbackDiscountAmtRounded));
        }
        String iconType= StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage())? HERO_OFFER_PERSUASION_ICON_TYPE : CASHBACK_OFFER_PERSUASION_ICON_TYPE;
        persuasion.setPersuasionText(persuasionAppliedText.toString());
        persuasion.setHtml(true);
        persuasion.setIconType(iconType);
        persuasionMap.put (CASHBACK_HERO_OFFER_PERSUASION_NODE, persuasion);
    }

    @Override
    public ThankYouResponse convertThankYouResponse(PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity, FeatureFlags featureFlags, Map<String, String> parameterMap) throws ErrorResponseFromDownstreamException {
        return super.convertThankYouResponse(persistanceMultiRoomResponseEntity,featureFlags, parameterMap);
    }

    @Override
    protected String getMytripActionCorpUrl(String cardType) {
        return myTripsCardTypeToIconUrls.get(cardType).getIconUrlAndroidCorp();
    }

    @Override
    protected String getMytripActionB2CUrl(String cardType) {
        return myTripsCardTypeToIconUrls.get(cardType).getIconUrlAndroid();
    }

    @Override
    protected String getHotelDetailsRawDeepLinkUrl(PersistedMultiRoomData persistedData) {
        if (persistedData !=null
                && persistedData.getUserGlobalInfo() != null
                && GLOBAL_ENTITY.equalsIgnoreCase(persistedData.getUserGlobalInfo().getEntityName())) {
            return hotelDetailsRawDeepLinkGlobal;
        }
        return hotelDetailsRawDeepLink;
    }

    @Override
    protected String getMytripsRawDeepLinkUrl(UserGlobalInfo userGlobalInfo, Map<String, String> expData) {
        if (userGlobalInfo != null && GLOBAL_ENTITY.equalsIgnoreCase(userGlobalInfo.getEntityName())) {
            return myTripsDeeplinkGlobal;
        }
        return myTripsDeeplink;
    }

    @Override
    protected String getDigilockerCheckInDeepLinkUrl(UserGlobalInfo userGlobalInfo, String bookingId) {
        String rawDeeplink = getMytripsRawDeepLinkUrl(userGlobalInfo, null);
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put(BOOKING_ID, bookingId);
        queryParams.put(DIGILOCKER_DEEPLINK_ACT_PARAM, DIGILOCKER_DEEPLINK_ACT_VALUE);

        return Utility.appendQueryParamsInUrl(rawDeeplink, queryParams);
    }

    @Override
    protected boolean tildeRequiredInRSQ() {
        return false;
    }
}
