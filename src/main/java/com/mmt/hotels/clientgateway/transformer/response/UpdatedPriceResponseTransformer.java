package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.request.UpdatePriceRequest;
import com.mmt.hotels.clientgateway.request.UpdatedPriceRoomCriteria;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.pricing.PriceBreakDownResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class UpdatedPriceResponseTransformer {

    @Autowired
    private CommonResponseTransformer commonResponseTransformer;

    @Autowired
    private Utility utility;

    @Autowired
    private DateUtil dateUtil;

    public UpdatePriceResponse convertUpdatedPriceResponse(PriceBreakDownResponse updatedPriceResponseCB, UpdatePriceRequest updatePriceRequest, CommonModifierResponse commonModifierResponse) {
        boolean myPartner = Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
        String askedCurrency = updatePriceRequest.getSearchCriteria().getCurrency();
        List<UpdatedPriceRoomCriteria> updatedPriceRoomCriteria = updatePriceRequest.getSearchCriteria().getRoomCriteria();
        if (updatedPriceResponseCB == null)
            return null;
        String sellableType = CollectionUtils.isNotEmpty(updatedPriceRoomCriteria) ? updatedPriceRoomCriteria.get(0).getSellableType() :null;
        Integer roomCount = 1;
        if(CollectionUtils.isNotEmpty(updatedPriceRoomCriteria)){
            roomCount = 0;
            for(UpdatedPriceRoomCriteria rc : updatedPriceRoomCriteria){
                roomCount += rc.getRoomStayCandidates().size();
            }
        }
        boolean newPropertyOfferApplicable = commonModifierResponse!=null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) ? utility.isExperimentOn(commonModifierResponse.getExpDataMap(), ExperimentKeys.NEW_PROPERTY_OFFER.getKey()) : false;
        String checkIn = updatePriceRequest.getSearchCriteria().getCheckIn();
        String checkOut = updatePriceRequest.getSearchCriteria().getCheckOut();
        int los = dateUtil.getDaysDiff(LocalDate.parse(checkIn),LocalDate.parse(checkOut));
        UpdatePriceResponse updatePriceResponse = new UpdatePriceResponse();
        updatePriceResponse.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(updatedPriceResponseCB.getCorpMetaInfo(), utility.isTCSV2FlowEnabled(commonModifierResponse.getExpDataMap())));
        updatePriceResponse.setPriceMap(commonResponseTransformer.getPriceMap(updatedPriceResponseCB.getDisplayPriceBreakDown(),
                updatedPriceResponseCB.getDisplayPriceBreakDownList(),
                updatePriceRequest.getExpData(), roomCount, askedCurrency,
                sellableType, los, false, "",
                utility.buildToolTip(updatePriceRequest.getRequestDetails().getFunnelSource()),
                Utility.isGroupBookingFunnel(updatePriceRequest.getRequestDetails().getFunnelSource()),
                false,myPartner,updatedPriceResponseCB.isAltAcco(), updatedPriceResponseCB.getMarkUpDetails(),
                null, null,  newPropertyOfferApplicable, false));
        if (MapUtils.isNotEmpty(updatePriceResponse.getPriceMap())) {
        	updatePriceResponse.setDefaultPriceKey(getDefaultPriceKey(updatePriceResponse.getPriceMap()));
        }
        updatePriceResponse.setCurrency(updatedPriceResponseCB.getCurrency());
        return updatePriceResponse;

    }

	private String getDefaultPriceKey(Map<String, TotalPricing> priceMap) {
		for (String key: priceMap.keySet()) {
			return key;
		}
		return null;
	}
}
