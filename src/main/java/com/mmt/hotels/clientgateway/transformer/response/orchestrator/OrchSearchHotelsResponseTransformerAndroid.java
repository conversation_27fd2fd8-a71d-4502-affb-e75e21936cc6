package com.mmt.hotels.clientgateway.transformer.response.orchestrator;


import com.gommt.hotels.orchestrator.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.model.response.da.CancellationTimeline;
import com.gommt.hotels.orchestrator.model.response.listing.HotelDetails;
import com.gommt.hotels.orchestrator.model.response.listing.PersonalizedSectionDetails;
import com.gommt.hotels.orchestrator.model.response.listing.PriceDetail;
import com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.response.searchHotels.BottomSheet;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.HotelCard;
import com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.transformer.response.android.SearchHotelsResponseTransformerAndroid;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.hotels.pojo.listing.personalization.BGLinearGradient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.DAYUSE_LOCAL_ID;
import static com.mmt.hotels.clientgateway.constants.Constants.FUNNEL_DAYUSE;
import static com.mmt.hotels.clientgateway.constants.Constants.IMAGE_TEXT_H;
import static com.mmt.hotels.clientgateway.constants.Constants.ONE_CLICK;
import static com.mmt.hotels.clientgateway.constants.Constants.PLACEHOLDER_CARD_M1_TEXT_COLOR;
import static com.mmt.hotels.clientgateway.constants.Constants.SHORTSTAYS_FUNNEL;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;

@Component
public class OrchSearchHotelsResponseTransformerAndroid extends OrchSearchHotelsResponseTransformer {

    private static final Logger LOGGER = LoggerFactory.getLogger(SearchHotelsResponseTransformerAndroid.class);
    
    private MyBizStaticCard myBizStaticCard;
    
    @PostConstruct
    public void init() {
        super.init();
        myBizStaticCard = commonConfigConsul.getMyBizStaticCard();
    }

    @Override
    protected BottomSheet buildBottomSheet(PersonalizedSectionDetails perResponse) {
        return null;
    }

    @Override
    public void addPersuasionHoverData(Hotel hotel, HotelDetails hotelEntity, CancellationTimeline cancellationTimeline, PriceDetail displayFare, ListingSearchRequest listingSearchRequest) {
        hotel.setLovedByIndians(persuasionUtil.checkIfIndianessPersuasionExists(hotelEntity.getHotelPersuasions()));
    }

    @Override
    public void addLocationPersuasionToHotelPersuasions(Hotel hotel, List<String> locationPersuasion, LinkedHashSet<String> facilities, ListingSearchRequest searchHotelsRequest, boolean enableAmenities, boolean sectionMyBizSimilarToDirectHtl, String dayUsePersuasionsText, TransportPoi nearestGroundTransportPoi, String drivingTimeText, LocationDetails locusData, boolean homestayV2Flow) {
        if (CollectionUtils.isNotEmpty(locationPersuasion)) {
            if (hotel.getHotelPersuasions() == null)
                hotel.setHotelPersuasions(new HashMap<String, Object>());
            PersuasionObject locPers = new PersuasionObject();
            locPers.setData(new ArrayList<>());
            locPers.setTemplate(IMAGE_TEXT_H);
            locPers.setPlaceholder("SINGLE");

            int index = 1;
            PersuasionData locPersuasionData = new PersuasionData();
            locPersuasionData.setHasAction(false);
            locPersuasionData.setHtml(true);
            locPersuasionData.setId("LOC_PERSUASION_" + index++);
            locPersuasionData.setPersuasionType("LOCATION");
            PersuasionStyle style = new PersuasionStyle();
            if (homestayV2Flow) {
                style.setFontSize(Constants.BASE_FONT_SIZE);
                style.setTextColor(PLACEHOLDER_CARD_M1_TEXT_COLOR);
            }
            //[HTL-46707] Changed the max lines to 3 for IH hotels only
            Integer maxLines = hotel.getLocationDetail() != null && !Constants.DOM_COUNTRY.equalsIgnoreCase(hotel.getLocationDetail().getCountryId()) ? 3 : 2;
            style.setMaxLines(maxLines);
            locPersuasionData.setStyle(style);

            locPers.getData().add(locPersuasionData);
            if (locationPersuasion.size() == 1) {
                locPersuasionData.setText(locationPersuasion.get(0));
            } else if (locationPersuasion.size() >= 2) {
                locPersuasionData.setText(locationPersuasion.get(0) + " | " + locationPersuasion.get(1));
                if (locationPersuasion.size() > 2)
                    locPersuasionData.setText(locPersuasionData.getText() + " | " + locationPersuasion.get(2));
            }

            if (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null && SHORTSTAYS_FUNNEL.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource())) {
                if (drivingTimeText == null || locusData == null) {
                    LOGGER.warn("Location Persuasion could not be added for ShortStay Funnel because either driving_duration or city_name is empty");
                    return;
                }
                StringBuilder updatedLocationPersuasionText = new StringBuilder();
                String locText = polyglotService.getTranslatedData(ConstantsTranslation.SHORTSTAY_LOCATION_PERSUASION_PREFIX_TEXT_FONT);
                locText = locText.replace("{city_text}", locationPersuasion.get(0));
                updatedLocationPersuasionText.append(locText);
                updatedLocationPersuasionText.append(Constants.SPACE);
                String finalDrivingText = polyglotService.getTranslatedData(ConstantsTranslation.SHORTSTAY_LOCATION_PERSUASION_SUFFIX_TEXT_FONT);
                String drivingText = polyglotService.getTranslatedData(ConstantsTranslation.DRIVING_DURATION_ZONE_SHORTSTAY).replace("{duration}", drivingTimeText);
                drivingText = drivingText.replace("{city_name}", locusData.getCityName());
                finalDrivingText = finalDrivingText.replace("{driving_text}", drivingText);
                updatedLocationPersuasionText.append(finalDrivingText);
                locPersuasionData.setText(updatedLocationPersuasionText.toString());
            }

            try {
                ((Map<Object, Object>) hotel.getHotelPersuasions()).put(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP, locPers);
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added due to : {} ", e.getMessage());
            }

        }

        if (CollectionUtils.isNotEmpty(facilities) && !StringUtils.equals(hotel.getViewType(), ONE_CLICK) && enableAmenities) {
            if (hotel.getHotelPersuasions() == null)
                hotel.setHotelPersuasions(new HashMap<String, Object>());
            PersuasionObject amenityPers = new PersuasionObject();
            amenityPers.setData(new ArrayList<>());
            amenityPers.setTemplate(IMAGE_TEXT_H);
            amenityPers.setPlaceholder("SINGLE");


            PersuasionData amenPersuasionData = new PersuasionData();
            amenPersuasionData.setHasAction(false);
            amenPersuasionData.setHtml(false);
            amenPersuasionData.setId("AMENITIES");
            amenPersuasionData.setPersuasionType("AMENITIES");
            amenPersuasionData.setStyle(new PersuasionStyle());
            amenPersuasionData.getStyle().setTextColor("#000000");
            StringBuilder text = new StringBuilder();
            int index = 1;
            Iterator<String> iter = facilities.iterator();
            while (iter.hasNext() && index <= 3) {
                // if condition below is only to append a | for 2nd and 3rd amenity SWAt-9133734
                if (index != 1)
                    text.append(" | ");
                text.append(iter.next());
                index++;
            }
            amenPersuasionData.setText(text.toString());
            amenityPers.getData().add(amenPersuasionData);
            try {
                ((Map<Object, Object>) hotel.getHotelPersuasions()).put(Constants.AMENITIES_PLACEHOLDER_ID_APP, amenityPers);
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added due to : {} ", e.getMessage());
            }
        }

        if (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null && FUNNEL_DAYUSE.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource()) && StringUtils.isNotEmpty(dayUsePersuasionsText)) {
            if (hotel.getHotelPersuasions() == null)
                hotel.setHotelPersuasions(new HashMap<String, Object>());
            PersuasionObject amenityPers = new PersuasionObject();
            amenityPers.setData(new ArrayList<>());
            amenityPers.setTemplate(IMAGE_TEXT_H);
            amenityPers.setPlaceholder("SINGLE");


            PersuasionData amenPersuasionData = new PersuasionData();
            amenPersuasionData.setHasAction(false);
            amenPersuasionData.setHtml(false);
            amenPersuasionData.setId(DAYUSE_LOCAL_ID);
            amenPersuasionData.setPersuasionType(DAYUSE_LOCAL_ID);
            amenPersuasionData.setStyle(new PersuasionStyle());
            amenPersuasionData.getStyle().setTextColor("#000000");
            amenPersuasionData.setText(dayUsePersuasionsText);
            amenPersuasionData.setIcontype("b_dot");
            amenityPers.getData().add(amenPersuasionData);
            try {
                ((Map<Object, Object>) hotel.getHotelPersuasions()).put(Constants.AMENITIES_PLACEHOLDER_ID_APP, amenityPers);
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added due to : {} ", e.getMessage());
            }
        }

    }

    @Override
    protected String buildBGColor(String section, String orientation, String cardType) {
        return null;
    }

    @Override
    protected void addBookingConfirmationPersuasion(HotelDetails hotelEntity) {
        addBookingConfirmationPersuasionForMobile(hotelEntity);
    }

    @Override
    public MyBizStaticCard buildStaticCard(String section, List<HotelDetails> hotels, String detailDeepLinkUrl) {
        MyBizStaticCard staticCard = null;
        if(Constants.CORPBUDGET_DIRECT_HOTEL.equalsIgnoreCase(section) && CollectionUtils.isNotEmpty(hotels) && !hotels.get(0).isBudgetHotel() &&
                myBizStaticCard != null) {
            staticCard = SerializationUtils.clone(myBizStaticCard);
            staticCard.setActionUrl(detailDeepLinkUrl);
            translateStaticCard(staticCard);
        }
        return staticCard;
    }
    
    protected void translateStaticCard(MyBizStaticCard staticCard) {
        staticCard.setText(polyglotService.getTranslatedData(staticCard.getText()));
        staticCard.setSubtext(polyglotService.getTranslatedData(staticCard.getSubtext()));
        staticCard.setCtaText(polyglotService.getTranslatedData(staticCard.getCtaText()));
    }

    @Override
    public HotelCard buildQuickBookCard(QuickBookInfo quickBookInfo) {
        if(quickBookInfo == null) {
            return null;
        }
        HotelCard hotelBottomCard = new HotelCard();
        hotelBottomCard.setHeading(quickBookInfo.getTitleWithPrice());
        hotelBottomCard.setSubHeading(quickBookInfo.getRoomPersuasion());
        hotelBottomCard.setRoomSubHeading(quickBookInfo.getRoomPersuasionWithSize());
        hotelBottomCard.setShowCard(quickBookInfo.isShowQuickBookCard());
        hotelBottomCard.setCta(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW));
        BGLinearGradient bgLinearGradient = new BGLinearGradient();
        bgLinearGradient.setDirection("leftBottomToTopRight");
        bgLinearGradient.setStart("#2D6F95");
        bgLinearGradient.setEnd("#192B43");
        hotelBottomCard.setBgLinearGradient(bgLinearGradient);
        return hotelBottomCard;
    }
}
