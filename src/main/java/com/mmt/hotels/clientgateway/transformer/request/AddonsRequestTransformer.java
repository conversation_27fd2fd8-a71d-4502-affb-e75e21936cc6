package com.mmt.hotels.clientgateway.transformer.request;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.AvailRoomsSearchCriteria;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchAddonsCriteria;
import com.mmt.hotels.clientgateway.request.SearchAddonsRequest;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.RoomCriterion;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.request.TrafficSource;
import com.mmt.hotels.model.request.addon.GetAddonsRequest;

@Component
public class AddonsRequestTransformer {

	public GetAddonsRequest convertSearchAddonsRequest(SearchAddonsRequest searchAddonsRequest,CommonModifierResponse commonModifierResponse) {
		GetAddonsRequest getAddonsRequest = new GetAddonsRequest();

		populateDeviceDetails(getAddonsRequest,searchAddonsRequest.getDeviceDetails());

		populateSearchCriteria(getAddonsRequest, searchAddonsRequest.getSearchCriteria(), commonModifierResponse);

		populateRequestDetails(getAddonsRequest, searchAddonsRequest.getRequestDetails());

		getAddonsRequest.setExperimentData(searchAddonsRequest.getExpData());
		getAddonsRequest.setCdfContextId(commonModifierResponse.getCdfContextId());
		getAddonsRequest.setAffiliateId(commonModifierResponse.getAffiliateId());
		getAddonsRequest.setCorrelationKey(searchAddonsRequest.getCorrelationKey());
		if(commonModifierResponse.getExtendedUser() != null){
			getAddonsRequest.setUuid(commonModifierResponse.getExtendedUser().getUuid());
			getAddonsRequest.setProfileType(commonModifierResponse.getExtendedUser().getProfileType());

		}

		getAddonsRequest.setTotalPriceWithTax(searchAddonsRequest.getTotalPriceWithTax());
		getAddonsRequest.setTotalPriceWithoutTax(searchAddonsRequest.getTotalPriceWithoutTax());
		getAddonsRequest.setNetRateSelected(searchAddonsRequest.isNetRateSelected());
		return getAddonsRequest;
	}

	private void populateRequestDetails(GetAddonsRequest getAddonsRequest, RequestDetails requestDetails) {
		getAddonsRequest.setIdContext(requestDetails.getIdContext());
		getAddonsRequest.setVisitorId(requestDetails.getVisitorId());
		getAddonsRequest.setVisitNumber(requestDetails.getVisitNumber() != null?String.valueOf(requestDetails.getVisitNumber()): "");
		getAddonsRequest.setLoggedIn(requestDetails.isLoggedIn());
		getAddonsRequest.setPayMode(requestDetails.getPayMode());
        if(null != requestDetails.getTrafficSource()) {
        	getAddonsRequest.setTrafficSource(buildTrafficSource(requestDetails.getTrafficSource()));
        }
	}

    private TrafficSource buildTrafficSource(com.mmt.hotels.clientgateway.request.TrafficSource trafficSource) {
        TrafficSource trafficSourceCB = new TrafficSource();
        trafficSourceCB.setSource(trafficSource.getSource());
        trafficSourceCB.setType(trafficSource.getType());
        // Pass aud field to downstream APIs
        if (trafficSource.getAud() != null) {
            trafficSourceCB.setAud(trafficSource.getAud());
        }
        return trafficSourceCB;
    }

	private void populateSearchCriteria(GetAddonsRequest getAddonsRequest, SearchAddonsCriteria searchCriteria,
			CommonModifierResponse commonModifierResponse) {

		getAddonsRequest.setCheckin(searchCriteria.getCheckIn());
		getAddonsRequest.setCheckout(searchCriteria.getCheckOut());
		getAddonsRequest.setCityCode(searchCriteria.getCityCode());
		getAddonsRequest.setCountryCode(searchCriteria.getCountryCode());
		getAddonsRequest.setLocationId(searchCriteria.getLocationId());
		getAddonsRequest.setLocationType(searchCriteria.getLocationType());
		getAddonsRequest.setCurrency(searchCriteria.getCurrency());
		getAddonsRequest.setAuthToken(commonModifierResponse.getMmtAuth());

		getAddonsRequest.setRoomCriteria(buildRoomCriteria(searchCriteria.getRoomCriteria(), searchCriteria.getHotelId(), searchCriteria.getPricingKey()));

	}

    private List<RoomCriterion> buildRoomCriteria(List<AvailRoomsSearchCriteria> roomCriteria, String hotelId, String pricingKey) {
        if (CollectionUtils.isNotEmpty(roomCriteria)){
            List<RoomCriterion> roomCriterionList = new ArrayList<>();
            for (AvailRoomsSearchCriteria roomCriteriaCG : roomCriteria){
                RoomCriterion roomCriterion = new RoomCriterion();
                roomCriterion.setHotelId(hotelId);
                roomCriterion.setMtKey(roomCriteriaCG.getMtKey());
                roomCriterion.setPricingKey(pricingKey);
                roomCriterion.setRatePlanCode(roomCriteriaCG.getRatePlanCode());
                roomCriterion.setRoomCode(roomCriteriaCG.getRoomCode());
                roomCriterion.setRoomStayCandidates(buildRoomStayCandidates(roomCriteriaCG.getRoomStayCandidates()));
                roomCriterionList.add(roomCriterion);
            }
            return roomCriterionList;
        }
        return null;

    }

	private List<RoomStayCandidate> buildRoomStayCandidates(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        if(roomStayCandidates==null)
            return null;
        List<RoomStayCandidate> roomStayCandidateList = new ArrayList<>();
        for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidateCG : roomStayCandidates){
        	RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
            roomStayCandidate.setGuestCounts(buildGuestCounts(roomStayCandidateCG));
            roomStayCandidateList.add(roomStayCandidate);
        }
        return roomStayCandidateList;
    }

    private List<GuestCount> buildGuestCounts(
			com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidateCG) {
		List<GuestCount> guestCounts = new ArrayList<>();
		GuestCount guestCount = new GuestCount();
		guestCount.setAgeQualifyingCode("10");
		guestCount.setAges(roomStayCandidateCG.getChildAges());
		guestCount.setCount(String.valueOf(roomStayCandidateCG.getAdultCount()));
		guestCounts.add(guestCount);
		return guestCounts;
	}

	private void populateDeviceDetails(GetAddonsRequest getAddonsRequest, DeviceDetails deviceDetails) {
		getAddonsRequest.setAppVersion(deviceDetails.getAppVersion());
		getAddonsRequest.setBookingDevice(deviceDetails.getBookingDevice());
		getAddonsRequest.setDeviceId(deviceDetails.getDeviceId());
		getAddonsRequest.setDeviceType(deviceDetails.getDeviceType());
	}

}
