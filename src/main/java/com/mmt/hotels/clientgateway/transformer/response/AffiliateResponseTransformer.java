package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.affiliate.CreateQuoteResponse;
import com.mmt.hotels.clientgateway.response.affiliate.UpdatedAffiliateFeeResponse;
import com.mmt.hotels.model.affiliate.UpdateAffiliateFeeResponse;
import com.mmt.hotels.model.response.pricing.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AffiliateResponseTransformer {

    @Autowired
    private CommonResponseTransformer commonResponseTransformer;

    public UpdatedAffiliateFeeResponse convertAffiliateFeeUpdateResponse(UpdateAffiliateFeeResponse updateAffiliateFeeResponseHES) {
        UpdatedAffiliateFeeResponse updatedAffiliateFeeResponse = new UpdatedAffiliateFeeResponse();
        updatedAffiliateFeeResponse.setTotalpricing(getTotalPricing(updateAffiliateFeeResponseHES.getDisplayPriceBreakDown(), updateAffiliateFeeResponseHES.getCountryCode(), updateAffiliateFeeResponseHES.getPayMode(),null));
        updatedAffiliateFeeResponse.getTotalpricing().setAffiliateFeeDetails(commonResponseTransformer.buildAffiliateFeeDetails(updateAffiliateFeeResponseHES.getAffiliateFeeOptions()));
        return updatedAffiliateFeeResponse;
    }

    public CreateQuoteResponse convertAffiliateCreateQuoteResponse(com.mmt.hotels.model.affiliate.CreateQuoteResponse createQuoteResponseHES) {
        CreateQuoteResponse createQuoteResponseCG = new CreateQuoteResponse();
        createQuoteResponseCG.setId(createQuoteResponseHES.getId());
        return createQuoteResponseCG;
    }

    private com.mmt.hotels.clientgateway.response.TotalPricing getTotalPricing(DisplayPriceBreakDown displayPriceBrkDwn, String countryCode, String payMode, String expData) {
        com.mmt.hotels.clientgateway.response.TotalPricing totalPricing = new TotalPricing();
        if (displayPriceBrkDwn != null) {
            totalPricing.setDetails(commonResponseTransformer.getPricingDetails(displayPriceBrkDwn, countryCode, payMode, false, "", expData, false, false, null, false, false, (displayPriceBrkDwn.getCouponInfo()!=null?displayPriceBrkDwn.getCouponInfo().getExtraDiscountType(): Constants.EMPTY_STRING),false, true));
        }
        return totalPricing;
    }
}
