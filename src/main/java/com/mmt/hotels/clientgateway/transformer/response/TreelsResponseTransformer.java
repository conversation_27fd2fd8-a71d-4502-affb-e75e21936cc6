package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.ListingSearchRequestV2;
import com.mmt.hotels.clientgateway.response.listing.ExploreMoreData;
import com.mmt.hotels.clientgateway.response.listing.ListingProduct;
import com.mmt.hotels.clientgateway.response.listing.TreelsListingResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntityAbridged;
import com.mmt.hotels.model.response.searchwrapper.TreelsCTA;
import com.mmt.hotels.model.response.searchwrapper.TreelsListingResponseBO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component
public class TreelsResponseTransformer {

    @Autowired
    CommonResponseTransformer commonResponseTransformer;

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    private Utility utility;

    @Value("${explore.more.image}")
    private String exploreMoreImage;

    public TreelsListingResponse convertTreelsResponse(TreelsListingResponseBO treelsListingResponseBO, ListingSearchRequestV2 listingSearchRequest, SearchWrapperInputRequest searchWrapperInputRequest) {
        return Optional.ofNullable(treelsListingResponseBO)
                .map(TreelsListingResponseBO::getTreelsResponse)
                .map(response -> {
                    TreelsListingResponse treelsListingResponse = new TreelsListingResponse();
                    treelsListingResponse.setNoMoreProducts(response.isNoMoreProduct());
                    treelsListingResponse.setLastProductId(response.getLastProductId());
                    treelsListingResponse.setProducts(buildTreelsList(treelsListingResponseBO));
                    treelsListingResponse.setExpVariantKeys(response.getExpVariantKeys());
                    if(response.isNoMoreProduct()) {
                        treelsListingResponse.setExploreMoreData(buildExploreMoreData(listingSearchRequest, response.getExploreMoreCta(), searchWrapperInputRequest));
                        treelsListingResponse.setAllTreelsShown(!MapUtils.isNotEmpty(searchWrapperInputRequest.getAppliedFilterMap()));
                        treelsListingResponse.setFilterRemovedCriteria(buildFilterToRemove(searchWrapperInputRequest));
                    }
                    return treelsListingResponse;
                }).orElse(null);
    }

    private List<Filter> buildFilterToRemove(SearchWrapperInputRequest searchWrapperInputRequest) {
        if(searchWrapperInputRequest==null || MapUtils.isEmpty(searchWrapperInputRequest.getAppliedFilterMap())) {
            return null;
        }
        List<Filter> removalFilters = new ArrayList<>();
        for(Map.Entry entry : searchWrapperInputRequest.getAppliedFilterMap().entrySet()) {
            removalFilters.addAll((Collection<? extends Filter>) entry.getValue());
        }
        return removalFilters;
    }

    private ExploreMoreData buildExploreMoreData(ListingSearchRequestV2 listingSearchRequest, TreelsCTA cta, SearchWrapperInputRequest searchWrapperInputRequest) {
        boolean filterApplied = MapUtils.isNotEmpty(searchWrapperInputRequest.getAppliedFilterMap());
        ExploreMoreData exploreMoreData = null;
        if(!filterApplied) {
            exploreMoreData = new ExploreMoreData();
            exploreMoreData.setHeader(filterApplied ? polyglotService.getTranslatedData(ConstantsTranslation.FILTERED_TREEL_HEADING) : polyglotService.getTranslatedData(ConstantsTranslation.UNFILTERED_TREEL_HEADING));
            exploreMoreData.setTitle(filterApplied ? polyglotService.getTranslatedData(ConstantsTranslation.FILTERED_TREEL_TITLE) : polyglotService.getTranslatedData(ConstantsTranslation.UNFILTERED_TREEL_TITLE));
            exploreMoreData.setSubtitle(filterApplied ? polyglotService.getTranslatedData(ConstantsTranslation.FILTERED_TREEL_SUB_TITLE) : polyglotService.getTranslatedData(ConstantsTranslation.UNFILTERED_TREEL_SUB_TITLE));
            exploreMoreData.setImageUrl(exploreMoreImage);
            exploreMoreData.setCta(cta);
        }

        return exploreMoreData;
    }
    private List<ListingProduct> buildTreelsList(TreelsListingResponseBO treelsListingResponseBO) {
        if(treelsListingResponseBO!=null && treelsListingResponseBO.getTreelsResponse()!=null && CollectionUtils.isEmpty(treelsListingResponseBO.getTreelsResponse().getListingProducts())) {
            return null;
        }
        List<ListingProduct> treelsList = new ArrayList<>();
        for(com.mmt.hotels.model.response.searchwrapper.ListingProduct treelHes : treelsListingResponseBO.getTreelsResponse().getListingProducts()) {
            ListingProduct treelCG = ListingProduct.builder()
                    .id(treelHes.getId())
                    .mediaType(treelHes.getMediaType())
                    .url(treelHes.getMediaUrl())
                    .thumbnailUrl(treelHes.getThumbnailUrl())
                    .title(treelHes.getTitle())
                    .subtitle(treelHes.getSubtitle())
                    .headerIcon(treelHes.getHeaderIcon())
                    .description(treelHes.getDescription())
                    .cta(treelHes.getCta())
                    .wishlistCount(treelHes.getWishlishtCount()!=null?Double.valueOf(treelHes.getWishlishtCount())/10:null)
                    .isWishListed(treelHes.isWishListed())
                    .shareCount(treelHes.getShareCount()!=null?Double.valueOf(treelHes.getShareCount())/10:null)
                    .duration(treelHes.getDuration())
                    .shareUrl(treelHes.getShareUrl())
                    .associatedHotelIds(treelHes.getAssociatedHotelIds())
                    .hotels(buildHotelsList(treelHes.getHotels(), treelHes.isWishListed())).build();
            treelsList.add(treelCG);
        }
        return treelsList;
    }

    private List<Hotel> buildHotelsList(List<SearchWrapperHotelEntityAbridged> searchWrapperHotelEntityList, boolean isTreelWishlisted) {
        if(CollectionUtils.isEmpty(searchWrapperHotelEntityList)) {
            return null;
        }
        List<Hotel> hotelList = new ArrayList<>();
        for(SearchWrapperHotelEntityAbridged hotelEntityHes : searchWrapperHotelEntityList) {
            Hotel hotel = new Hotel();
            hotel.setId(hotelEntityHes.getId());
            hotel.setName(hotelEntityHes.getName());
            hotel.setStarRating(hotelEntityHes.getStarRating());
            hotel.setLocationPersuasion(hotelEntityHes.getLocationPersuasion());
            hotel.setReviewSummary(commonResponseTransformer.buildReviewSummary(hotelEntityHes.getReviewSummary(), true));
            hotel.setLocationDetail(utility.buildLocationDetail(hotelEntityHes.getCityCode(), hotelEntityHes.getCityName(), "", hotelEntityHes.getCountryCode(), hotelEntityHes.getCountryName()));
            hotel.setWishListed(isTreelWishlisted);
            hotelList.add(hotel);
        }
        return hotelList;
    }
}
