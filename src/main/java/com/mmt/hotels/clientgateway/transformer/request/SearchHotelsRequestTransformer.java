package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.ChatbotDetails;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.request.matchmaker.LatLngObject;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.LinkedHashMap;
import java.util.Set;
import java.util.Optional;

import static com.mmt.hotels.clientgateway.constants.Constants.APPLY_IN_POLICY_COR_FILTER_EXP;
import static com.mmt.hotels.clientgateway.constants.Constants.TRUE;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.LISTING_SEARCH_HOTELS;

@Component
public class SearchHotelsRequestTransformer  extends  BaseSearchRequestTransformer{

	@Value("${consul.enable}")
	private boolean consulFlag;

	@Autowired
	CommonConfigConsul commonConfigConsul;
	@Autowired
	private PropertyManager propertyManager;

	private List<Double> androidListingMapDensity;

	private List<Double> iosListingMapDensity;

	private List<Double> desktopListingMapDensity;

	private int starRatingMin;

	private int starRatingMax;

	private int corpBudgetPriceMin;

	private int corpBudgetPriceMax;

	@Autowired
	private CommonHelper commonHelper;

	private static final Logger logger = LoggerFactory.getLogger(SearchHotelsRequestTransformer.class);

	@Autowired
	private MetricAspect metricAspect;

	@PostConstruct
	public void init() {

		if(consulFlag){
			androidListingMapDensity = commonConfigConsul.getAndroidListingMapDensity();
			iosListingMapDensity = commonConfigConsul.getIosListingMapDensity();
			desktopListingMapDensity = commonConfigConsul.getDesktopListingMapDensity();
			starRatingMin = commonConfigConsul.getStarRatingMin();
			starRatingMax = commonConfigConsul.getStarRatingMax();
			corpBudgetPriceMin = commonConfigConsul.getCorpBudgetPriceMin();
			corpBudgetPriceMax = commonConfigConsul.getCorpBudgetPriceMax();
			logger.debug("Fetching values from commonConfig consul");

		}
		else {
			CommonConfig property = propertyManager.getProperty("commonConfig", CommonConfig.class);
			androidListingMapDensity = property.androidListingMapDensity();
			iosListingMapDensity = property.iosListingMapDensity();
			desktopListingMapDensity = property.desktopListingMapDensity();
			starRatingMin = property.starRatingMin();
			starRatingMax = property.starRatingMax();
			corpBudgetPriceMin = property.corpBudgetPriceMin();
			corpBudgetPriceMax = property.corpBudgetPriceMax();
		}
	}

	public SearchWrapperInputRequest convertSearchRequest(SearchHotelsRequest searchHotelsRequestGateway, CommonModifierResponse commonModifierResponse) {
		SearchWrapperInputRequest searchWrapperInputRequest = null;
		long start = System.currentTimeMillis();
		try {
			if(Utility.isCorpBudgetHotelFunnel(searchHotelsRequestGateway.getRequestDetails().getFunnelSource())){
				buildCorpBudgetFilterCriteria(searchHotelsRequestGateway);
			}
			searchWrapperInputRequest = super.convertSearchRequest(searchHotelsRequestGateway, commonModifierResponse);
			searchWrapperInputRequest.setLastProductId(searchHotelsRequestGateway.getLastProductId());
			if (searchHotelsRequestGateway.getLimit()!=null) {
				searchWrapperInputRequest.setLimit(Integer.valueOf(searchHotelsRequestGateway.getLimit()));
			}
			modifyMatchMakerDetailsIfDistanceFilterApplied(searchWrapperInputRequest, searchHotelsRequestGateway);
			buildMapDetails(searchWrapperInputRequest, searchHotelsRequestGateway.getMapDetails());
			modifyListingMapDensity(searchWrapperInputRequest);

			if (MapUtils.isNotEmpty(searchHotelsRequestGateway.getAdditionalProperties())) {
				searchWrapperInputRequest.setSecureURL(searchHotelsRequestGateway.getAdditionalProperties().get("secureUrl"));
				if (StringUtils.isNotBlank(searchHotelsRequestGateway.getAdditionalProperties().get("imageCount")))
					searchWrapperInputRequest.setImageCount(Integer.parseInt(searchHotelsRequestGateway.getAdditionalProperties().get("imageCount")));
			}

			/* NearByHotels Request */
			if (searchHotelsRequestGateway.getSearchCriteria().isNearBySearch()) {
				searchWrapperInputRequest.setCityCode("NEARBY");
				searchWrapperInputRequest.setCountryCode("NEARBY");
				if (null != searchHotelsRequestGateway.getNearbyFilter()) {
					searchWrapperInputRequest.setNearbyRequest(searchHotelsRequestGateway.getNearbyFilter());
					if (searchHotelsRequestGateway.getSearchCriteria() != null) {
						searchWrapperInputRequest.getNearbyRequest().setCountryCode(searchHotelsRequestGateway.getSearchCriteria().getCountryCode());
					}
				}
			}
			if(commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && TRUE.equals(commonModifierResponse.getExpDataMap().get(APPLY_IN_POLICY_COR_FILTER_EXP))) {
				searchWrapperInputRequest.setExcludeFilterMap(buildExcludeFilterMap(searchHotelsRequestGateway));
			}
			if (searchHotelsRequestGateway.getSearchCriteria().getMultiCurrencyInfo() != null) {
				searchWrapperInputRequest.setMultiCurrencyInfo(utility.buildMultiCurrencyInfoRequest(searchHotelsRequestGateway.getSearchCriteria().getMultiCurrencyInfo()));
			}
			if (searchHotelsRequestGateway.getSearchCriteria().getUserGlobalInfo() != null) {
				searchWrapperInputRequest.setUserGlobalInfo(utility.buildUserGlobalInfoHES(searchHotelsRequestGateway.getSearchCriteria().getUserGlobalInfo()));
			}
			if (searchHotelsRequestGateway.getRequestDetails() != null  && StringUtils.isNotEmpty(searchHotelsRequestGateway.getRequestDetails().getMyraMsgId())) {
				ChatbotDetails chatbotDetails = new ChatbotDetails();
				chatbotDetails.setMyraMsgId(searchHotelsRequestGateway.getRequestDetails().getMyraMsgId());
				searchWrapperInputRequest.setChatbotDetails(chatbotDetails);
			}

			searchWrapperInputRequest.setSubPageContext(searchHotelsRequestGateway.getRequestDetails().getSubPageContext());

		} finally {
			metricAspect.addToTimeInternalProcess(Constants.PROCESS_SEARCH_REQUEST_PROCESS, LISTING_SEARCH_HOTELS, System.currentTimeMillis() - start);
		}
		/* NearByHotels Request */

		return searchWrapperInputRequest;
	}

	private void modifyMatchMakerDetailsIfDistanceFilterApplied(SearchWrapperInputRequest searchWrapperInputRequest, SearchHotelsRequest searchHotelsRequestGateway) {
		if (searchHotelsRequestGateway == null || CollectionUtils.isEmpty(searchHotelsRequestGateway.getFilterCriteria())) {
			return;
		}

		Optional<Filter> distanceFilterOptional = searchHotelsRequestGateway.getFilterCriteria().stream()
				.filter(filter -> filter != null && FilterGroup.DRIVE_WALK_PROXIMITY_KEY_POI.name().equalsIgnoreCase(filter.getFilterGroup().name()))
				.findFirst();

		if (distanceFilterOptional.isPresent()) {
			Filter distanceFilter = distanceFilterOptional.get();
			if (StringUtils.isNotEmpty(distanceFilter.getFilterValue()) && distanceFilter.getFilterValue().contains(Constants.HASH_SEPARATOR)) {
				String[] splitValue = distanceFilter.getFilterValue().split(Constants.HASH_SEPARATOR);
				if (splitValue.length == 2 && splitValue[0].contains(Constants.UNDERSCORE)) {
					String[] poiIds = splitValue[0].split(Constants.UNDERSCORE);
					if (poiIds.length == 2 && StringUtils.isNotEmpty(poiIds[1])) {
						if (searchWrapperInputRequest.getMatchMakerRequest() == null) {
							searchWrapperInputRequest.setMatchMakerRequest(new MatchMakerRequest());
						}
						if (searchWrapperInputRequest.getMatchMakerRequest().getLatLng() == null) {
							searchWrapperInputRequest.getMatchMakerRequest().setLatLng(new ArrayList<>());
						}
						LatLngObject latLngObject = new LatLngObject();
						latLngObject.setPoiId(poiIds[1]);
						searchWrapperInputRequest.getMatchMakerRequest().getLatLng().add(latLngObject);
					}
				}
			}
		}
	}


	private void buildCorpBudgetFilterCriteria(SearchHotelsRequest searchHotelsRequestGateway) {
		if(searchHotelsRequestGateway.getFilterCriteria() == null)
			searchHotelsRequestGateway.setFilterCriteria(new ArrayList<>());
		for(int i=starRatingMin;i<=starRatingMax;i++){
			com.mmt.hotels.clientgateway.request.Filter filterCB = new  com.mmt.hotels.clientgateway.request.Filter();
			filterCB.setFilterGroup(com.mmt.hotels.clientgateway.response.filter.FilterGroup.STAR_RATING);
			filterCB.setFilterRange(null);
			filterCB.setFilterValue(String.valueOf(i));
			filterCB.setRangeFilter(false);
			searchHotelsRequestGateway.getFilterCriteria().add(filterCB);
		}
		com.mmt.hotels.clientgateway.request.Filter filterCB = new  com.mmt.hotels.clientgateway.request.Filter();
		filterCB.setFilterGroup(com.mmt.hotels.clientgateway.response.filter.FilterGroup.HOTEL_PRICE_BUCKET);
		com.mmt.hotels.clientgateway.request.FilterRange filterRange = new FilterRange();
		filterRange.setMinValue(corpBudgetPriceMin);
		filterRange.setMaxValue(corpBudgetPriceMax);
		filterCB.setFilterRange(filterRange);
		filterCB.setFilterValue(null);
		filterCB.setRangeFilter(true);
		searchHotelsRequestGateway.getFilterCriteria().add(filterCB);
	}

	private void buildMapDetails(SearchWrapperInputRequest searchWrapperInputRequest, MapDetails mapDetails) {
		if (mapDetails == null)
			return;
		searchWrapperInputRequest.setLatSegments(mapDetails.getLatSegments());
		searchWrapperInputRequest.setLongSegments(mapDetails.getLngSegments());
		com.mmt.hotels.model.request.LatLngBounds latLngBoundsCB = new com.mmt.hotels.model.request.LatLngBounds();
		LatLngBounds latLngBounds = mapDetails.getLatLngBounds();
		latLngBoundsCB.setNELat(latLngBounds.getNeLat());
		latLngBoundsCB.setNELng(latLngBounds.getNeLng());
		latLngBoundsCB.setRadius(mapDetails.getRadius());
		latLngBoundsCB.setSWLat(latLngBounds.getSwLat());
		latLngBoundsCB.setSWLng(latLngBounds.getSwLng());
		latLngBoundsCB.setRadius(mapDetails.getRadius());
		searchWrapperInputRequest.setLatLngBounds(latLngBoundsCB);
	}

	public void modifyListingMapDensity(SearchWrapperInputRequest searchWrapperInputRequest) {
		if (StringUtils.isBlank(searchWrapperInputRequest.getBookingDevice())
				|| searchWrapperInputRequest.getLatSegments() == null
				|| searchWrapperInputRequest.getLongSegments() == null)
			return;
		if (Constants.DEVICE_OS_ANDROID.equalsIgnoreCase(searchWrapperInputRequest.getBookingDevice())) {
				multiplyDensity(searchWrapperInputRequest, androidListingMapDensity);
		} else if (Constants.DEVICE_OS_IOS.equalsIgnoreCase(searchWrapperInputRequest.getBookingDevice())) {
			multiplyDensity(searchWrapperInputRequest, iosListingMapDensity);
		} else if (Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(searchWrapperInputRequest.getBookingDevice())) {
			multiplyDensity(searchWrapperInputRequest, desktopListingMapDensity);
		}
	}

	private void multiplyDensity(SearchWrapperInputRequest searchWrapperInputRequest, List<Double> listingMapDensity) {
		if(CollectionUtils.isEmpty(listingMapDensity) || listingMapDensity.size() != 2)
			return;
		searchWrapperInputRequest.setLatSegments((int) (searchWrapperInputRequest.getLatSegments() * listingMapDensity.get(0)));
		searchWrapperInputRequest.setLongSegments((int) (searchWrapperInputRequest.getLongSegments() * listingMapDensity.get(1)));
	}

	private Map<FilterGroup, Set<com.mmt.hotels.filter.Filter>> buildExcludeFilterMap(SearchHotelsRequest searchHotelsRequestGateway) {

		if (searchHotelsRequestGateway != null && CollectionUtils.isNotEmpty(searchHotelsRequestGateway.getFilterRemovedCriteria())) {
			Map<FilterGroup, Set<com.mmt.hotels.filter.Filter>> removeFilterMapCB = new LinkedHashMap<>();
			commonHelper.buildFiltersToRemove(removeFilterMapCB, searchHotelsRequestGateway.getFilterRemovedCriteria());
			return removeFilterMapCB;
		}
		return null;
	}
}