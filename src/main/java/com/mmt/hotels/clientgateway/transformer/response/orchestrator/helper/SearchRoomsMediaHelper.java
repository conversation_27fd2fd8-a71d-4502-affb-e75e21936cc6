package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.content.Media;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.PanoramicMedia360;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.ProfessionalMediaEntity;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.rooms.MediaData;
import com.mmt.hotels.clientgateway.response.staticdetail.Image360.View360Image;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.response.staticdata.Image360.GenericPosition;
import com.mmt.hotels.model.response.staticdata.Image360.Image360;
import com.mmt.hotels.model.response.staticdata.Image360.LinkHotspot;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class SearchRoomsMediaHelper {

    @Autowired
    private PolyglotService polyglotService;

    @Value("${view.360.icon.url}")
    private String view360IconUrl;

    /**
     * Extract room images from OrchV2 Media object (equivalent to legacy HotelImage logic).
     * Maps Media.professionalMediaEntities.get("R") to room image URLs by room code.
     */
    public List<String> extractRoomImagesFromMedia(Media media, String roomCode) {
        List<String> roomImages = new ArrayList<>();

        if (media != null && MapUtils.isNotEmpty(media.getProfessionalMediaEntities()) &&
                media.getProfessionalMediaEntities().containsKey("R")) {

            List<ProfessionalMediaEntity> professionalImages = media.getProfessionalMediaEntities().get("R");
            if (CollectionUtils.isNotEmpty(professionalImages)) {
                professionalImages.stream()
                        .filter(image -> roomCode.equals(image.getRoomCode()) && StringUtils.isNotBlank(image.getUrl()))
                        .forEach(image -> {
                            String imageUrl = image.getUrl().startsWith("http") ? image.getUrl() : "https:" + image.getUrl();
                            roomImages.add(imageUrl);
                        });
            }
        }

        return CollectionUtils.isNotEmpty(roomImages) ? roomImages : null;
    }

    /**
     * Extract 360 images from OrchV2 Media object (equivalent to legacy HotelImage.getImages360 logic).
     * Maps Media.panoramic360.get("R") to View360Image by room code.
     */
    public View360Image extract360ImagesFromMedia(Media media, String roomCode) {
        if (media != null && MapUtils.isNotEmpty(media.getPanoramic360()) &&
                media.getPanoramic360().containsKey("R")) {

            List<PanoramicMedia360> panoramic360List = media.getPanoramic360().get("R");
            if (CollectionUtils.isNotEmpty(panoramic360List)) {
                List<PanoramicMedia360> roomPanoramic360 = panoramic360List.stream()
                        .filter(image -> roomCode.equals(image.getRoomCode()))
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(roomPanoramic360)) {
                    View360Image view360Image = new View360Image();

                    // Convert PanoramicMedia360 to legacy Image360 format
                    List<Image360> image360List = roomPanoramic360.stream()
                            .map(this::convertPanoramicToImage360)
                            .collect(Collectors.toList());


                    view360Image.setImages(image360List);
                    view360Image.setCtaText(polyglotService.getTranslatedData(ConstantsTranslation.CTA_360_TEXT));
                    view360Image.setCtaIcon(view360IconUrl);

                    return view360Image;
                }
            }
        }

        return null;
    }

    /**
     * Convert OrchV2 PanoramicMedia360 to legacy Image360 format.
     */
    private Image360 convertPanoramicToImage360(PanoramicMedia360 panoramic) {
        Image360 image360 = new Image360();
        image360.setId(panoramic.getId());
        image360.setImageUrl(panoramic.getUrl());
        image360.setLinkHotspots(buildLinkHotspots(panoramic));
        image360.setLoadPosition(convertLoadPosition(panoramic));
        image360.setName(panoramic.getName());
        image360.setPanoramaImg(panoramic.getPanoramaImg());
        image360.setPreviewImg(panoramic.getPreviewUrl());
        image360.setRoomCode(panoramic.getRoomCode());
        image360.setSpaceType(panoramic.getSpaceType());
        image360.setThumbnail(panoramic.getThumbnailUrl());
        image360.setTiles(panoramic.getTiles());
        // Note: Some fields may not have exact setters, will be handled in future iterations
        return image360;
    }

    private List<LinkHotspot> buildLinkHotspots(PanoramicMedia360 panoramic) {
        List<LinkHotspot> linkHotspots = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(panoramic.getLinkHotspots())) {
            linkHotspots = panoramic.getLinkHotspots().stream()
                    .map(this::convertLinkHotspots)
                    .collect(Collectors.toList());
        }
        return linkHotspots;
    }

    private LinkHotspot convertLinkHotspots(PanoramicMedia360.LinkHotspot linkHotspot) {
        LinkHotspot hotspot = new LinkHotspot();
        hotspot.setId(linkHotspot.getId());
        hotspot.setMarkerPosition(convertMarkerPosition(linkHotspot.getMarkerPosition()));
        hotspot.setTargetLoadPosition(convertMarkerPosition(linkHotspot.getTargetLoadPosition()));
        return hotspot;
    }

    private GenericPosition convertMarkerPosition(PanoramicMedia360.GenericPosition markerPosition) {
        GenericPosition position = null;
        if (markerPosition != null) {
            position = new GenericPosition();
            position.setYaw(markerPosition.getYaw());
            position.setPitch(markerPosition.getPitch());
        }
        return position;
    }

    private static GenericPosition convertLoadPosition(PanoramicMedia360 panoramic) {
        GenericPosition loadPosition = null;
        if (panoramic.getLoadPosition() != null) {
            loadPosition = new GenericPosition();
            loadPosition.setPitch(panoramic.getLoadPosition().getPitch());
            loadPosition.setYaw(panoramic.getLoadPosition().getYaw());
        }
        return loadPosition;
    }

    /**
     * Populate media data from OrchV2 Media and room images (equivalent to legacy populateMedia logic).
     * Simplified version - OrchV2 doesn't have video support like legacy.
     */
    public List<MediaData> populateMedia(Map<String, RoomInfo> staticRoomInfoMap, List<String> roomImages, String roomCode) {
        List<MediaData> mediaDataList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(roomImages)) {
            for (String imageUrl : roomImages) {
                MediaData mediaData = new MediaData();
                mediaData.setUrl(imageUrl.startsWith("http") ? imageUrl : "https:" + imageUrl);
                mediaData.setMediaType(Constants.IMAGE_TYPE);
                mediaDataList.add(mediaData);
            }
        }

        return mediaDataList;
    }

}
