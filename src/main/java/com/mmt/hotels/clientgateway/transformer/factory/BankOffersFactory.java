package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.BankOffersRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.BankOffersRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.BankOffersRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.BankOffersRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.BankOffersRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.BankOffersResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.BankOffersResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.BankOffersResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.BankOffersResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.BankOffersResponseTransformerPWA;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class BankOffersFactory {

    @Autowired
    private BankOffersRequestTransformerPWA bankOffersRequestTransformerPWA;

    @Autowired
    private BankOffersRequestTransformerDesktop bankOffersRequestTransformerDesktop;

    @Autowired
    private BankOffersRequestTransformerIOS bankOffersRequestTransformerIOS;

    @Autowired
    private BankOffersRequestTransformerAndroid bankOffersRequestTransformerAndroid;

    @Autowired
    private BankOffersResponseTransformerPWA bankOffersResponseTransformerPWA;

    @Autowired
    private BankOffersResponseTransformerDesktop bankOffersResponseTransformerDesktop;

    @Autowired
    private BankOffersResponseTransformerIOS bankOffersResponseTransformerIOS;

    @Autowired
    private BankOffersResponseTransformerAndroid bankOffersResponseTransformerAndroid;

    public BankOffersRequestTransformer getRequestService(String client) {
        if (StringUtils.isEmpty(client))
            return bankOffersRequestTransformerDesktop;
        switch(client) {
            case "PWA":
            case "MSITE":
                return bankOffersRequestTransformerPWA;
            case "DESKTOP": return bankOffersRequestTransformerDesktop;
            case "ANDROID": return bankOffersRequestTransformerAndroid;
            case "IOS": return bankOffersRequestTransformerIOS;
        }
        return bankOffersRequestTransformerDesktop;
    }

    public BankOffersResponseTransformer getResponseService(String client) {
        if (StringUtils.isEmpty(client)) return bankOffersResponseTransformerDesktop;
        switch(client) {
            case "PWA":
            case "MSITE":
                return bankOffersResponseTransformerPWA;
            case "DESKTOP": return bankOffersResponseTransformerDesktop;
            case "ANDROID": return bankOffersResponseTransformerAndroid;
            case "IOS": return bankOffersResponseTransformerIOS;
        }
        return bankOffersResponseTransformerDesktop;
    }
    
}
