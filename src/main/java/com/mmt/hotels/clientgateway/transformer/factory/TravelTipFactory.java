package com.mmt.hotels.clientgateway.transformer.factory;



import com.mmt.hotels.clientgateway.transformer.request.TravelTipRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.TravelTipRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.TravelTipRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.TravelTipRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.TravelTipRequestTransformerPWA;

import com.mmt.hotels.clientgateway.transformer.response.TravelTipResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.TravelTipResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.TravelTipResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.TravelTipResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.TravelTipResponseTransformerPWA;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TravelTipFactory {

    @Autowired
    TravelTipResponseTransformerPWA travelTipResponseTransformerPWA;
    @Autowired
    TravelTipResponseTransformerDesktop travelTipResponseTransformerDesktop;
    @Autowired
    TravelTipResponseTransformerAndroid travelTipResponseTransformerAndroid;

    @Autowired
    TravelTipResponseTransformerIOS travelTipResponseTransformerIOS;

    @Autowired
    TravelTipRequestTransformerAndroid travelTipRequestTransformerAndroid;

    @Autowired
    TravelTipRequestTransformerIOS travelTipRequestTransformerIOS;

    @Autowired
    TravelTipRequestTransformerPWA travelTipRequestTransformerPWA;

    @Autowired
    TravelTipRequestTransformerDesktop travelTipRequestTransformerDesktop;


    public TravelTipRequestTransformer getRequestService(String client) {
        if (StringUtils.isEmpty(client))
            return travelTipRequestTransformerDesktop;
        switch(client) {
            case "PWA":
            case "MSITE":
                return travelTipRequestTransformerPWA;
            case "DESKTOP": return travelTipRequestTransformerDesktop;
            case "ANDROID": return travelTipRequestTransformerAndroid;
            case "IOS": return travelTipRequestTransformerIOS;
        }
        return travelTipRequestTransformerDesktop;
    }

    public TravelTipResponseTransformer getResponseService(String client) {
        if (StringUtils.isEmpty(client))
            return travelTipResponseTransformerDesktop;
        switch(client){
            case "PWA":
            case "MSITE":
                return travelTipResponseTransformerPWA;
            case "DESKTOP": return travelTipResponseTransformerDesktop;
            case "ANDROID": return travelTipResponseTransformerAndroid;
            case "IOS": return travelTipResponseTransformerIOS;
        }
        return travelTipResponseTransformerDesktop;
    }



}
