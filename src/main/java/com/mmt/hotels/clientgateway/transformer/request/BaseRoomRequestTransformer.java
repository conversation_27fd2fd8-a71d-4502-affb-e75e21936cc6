package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.helpers.FilterHelper;
import com.mmt.hotels.clientgateway.request.BaseSearchRequest;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.filter.FilterRange;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.ResponseFilterFlags;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.BEDROOM_COUNT;
import static com.mmt.hotels.clientgateway.constants.Constants.EXACT_ROOM_RECOMMENDATION;

public abstract class BaseRoomRequestTransformer {

    @Autowired
    private Utility utility;


	public ResponseFilterFlags buildResponseFilterFlags(PriceByHotelsRequestBody priceByHotelsRequestBody, BaseSearchRequest searchRoomsRequest, CommonModifierResponse commonModifierResponse) {

	    FeatureFlags featureFlags = searchRoomsRequest.getFeatureFlags();

        if (featureFlags == null)
            return null;
        ResponseFilterFlags responseFilterFlags = new ResponseFilterFlags();
        responseFilterFlags.setStaticData(featureFlags.isStaticData());
        responseFilterFlags.setFlyfishSummaryRequired(featureFlags.isReviewSummaryRequired());
        responseFilterFlags.setWalletRequired(featureFlags.isWalletRequired());
        responseFilterFlags.setShortlistRequired(featureFlags.isShortlistingRequired());
        responseFilterFlags.setPriceInfoReq(true);
        responseFilterFlags.setRoomLevelDetails(true);
        responseFilterFlags.setPriceInfoReq(true);
        responseFilterFlags.setBestCoupon(true);
        Optional.ofNullable(featureFlags.getMaskedPropertyName()).ifPresent(responseFilterFlags::setMaskedPropertyName);
        responseFilterFlags.setUpsellRequested(featureFlags.isShowUpsell());
        responseFilterFlags.setCityTaxExclusive(commonModifierResponse.isCityTaxExclusive());
        responseFilterFlags.setApplyAbsorption(featureFlags.isApplyAbsorption());
        responseFilterFlags.setAddOnRequired(featureFlags.isAddOnRequired());
        responseFilterFlags.setQuickReview(featureFlags.isQuickReview());
        responseFilterFlags.setHidePrice(featureFlags.isHidePrice());
        priceByHotelsRequestBody.setNumberOfAddons(featureFlags.getNoOfAddons());
        priceByHotelsRequestBody.setBestOffersLimit(featureFlags.getBestOffersLimit());
        if(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(searchRoomsRequest.getRequestDetails().getIdContext())) {
            responseFilterFlags.setNewCorp(true);
            responseFilterFlags.setPersuasionSeg(null);
            responseFilterFlags.setCityTaxExclusive(null);
        }
        responseFilterFlags.setDayUsePersuasion(featureFlags.isDayUsePersuasion());
        responseFilterFlags.setModifyBooking(featureFlags.isModifyBooking());
        if(null != searchRoomsRequest.getRequestDetails()
                && null != searchRoomsRequest.getRequestDetails().getTrafficSource()
                && null != searchRoomsRequest.getRequestDetails().getTrafficSource().getSource()
                && searchRoomsRequest.getRequestDetails().getTrafficSource().getSource().toUpperCase().startsWith(Constants.GOOGLEHOTELDFINDER)) {
            responseFilterFlags.setMetaFlow(true);
        }
        responseFilterFlags.setPropSearch(featureFlags.isPropSearch());
        if(StringUtils.isNotEmpty(featureFlags.getAltCheaperDates())){
            responseFilterFlags.setPropSearch(Boolean.TRUE);
        }
        return responseFilterFlags;
    }

    public void buildDeviceDetails(PriceByHotelsRequestBody priceByHotelsRequestBody,
                                    DeviceDetails deviceDetails) {
        priceByHotelsRequestBody.setAppVersion(deviceDetails.getAppVersion());
        priceByHotelsRequestBody.setBookingDevice(deviceDetails.getBookingDevice());
        priceByHotelsRequestBody.setLob(deviceDetails.getBookingDevice());
        priceByHotelsRequestBody.setDeviceId(deviceDetails.getDeviceId());
        priceByHotelsRequestBody.setDeviceType(deviceDetails.getDeviceType());
        priceByHotelsRequestBody.setDeviceName(deviceDetails.getDeviceName());
        priceByHotelsRequestBody.setSimSerialNo(deviceDetails.getSimSerialNo());
    }

    public void populateSearchCriteria(PriceByHotelsRequestBody priceByHotelsRequestBody, SearchCriteria searchCriteria, List<String> hotelIds, CommonModifierResponse commonModifierResponse) {
        priceByHotelsRequestBody.setHotelIds(hotelIds);
        priceByHotelsRequestBody.setCheckin(searchCriteria.getCheckIn());
        priceByHotelsRequestBody.setCheckout(searchCriteria.getCheckOut());
        priceByHotelsRequestBody.setCityCode(searchCriteria.getCityCode());
        priceByHotelsRequestBody.setCountryCode(searchCriteria.getCountryCode());
        priceByHotelsRequestBody.setLocationId(searchCriteria.getLocationId());
        priceByHotelsRequestBody.setLocationType(searchCriteria.getLocationType());
        if(searchCriteria.getUserSearchType()!=null) {
            priceByHotelsRequestBody.setUserSearchType(searchCriteria.getUserSearchType());
        }
        priceByHotelsRequestBody.setCurrency(searchCriteria.getCurrency()!=null ? searchCriteria.getCurrency().toUpperCase() : null);
        priceByHotelsRequestBody.setAuthToken(commonModifierResponse.getMmtAuth());
        priceByHotelsRequestBody.setMcid(commonModifierResponse.getMcId());
        priceByHotelsRequestBody.setPersonalCorpBooking(searchCriteria.isPersonalCorpBooking());
        utility.buildSlot(priceByHotelsRequestBody, searchCriteria);
    }

    /**
     * This method is to be used in Search-room and review page api only.
     * @param filters
     * @return
     */
    protected Map<FilterGroup, Set<Filter>> buildAppliedFilterMap(List<com.mmt.hotels.clientgateway.request.Filter> filters){
        Map<FilterGroup,Set<Filter>> appliedFilterMap = null;
        if(CollectionUtils.isNotEmpty(filters)){
            appliedFilterMap = new HashMap<>();
            for(com.mmt.hotels.clientgateway.request.Filter filter : filters){
                if(filter.getFilterGroup() != null) {
                    if (EXACT_ROOM_RECOMMENDATION.equalsIgnoreCase(filter.getFilterGroup().name()) || (BEDROOM_COUNT.equalsIgnoreCase(filter.getFilterGroup().name()))) {
                        continue;
                    }
                }
                FilterGroup filterGroup = FilterGroup.getFilterGroupFromFilterName(filter.getFilterGroup().name());
                Filter oldFilter = getOldFilterFromNewFilter(filter, filterGroup);
                if(appliedFilterMap.containsKey(filterGroup)){
                    appliedFilterMap.get(filterGroup).add(oldFilter);
                }else{
                    Set<Filter> filterSet = new HashSet<>();
                    filterSet.add(oldFilter);
                    appliedFilterMap.put(filterGroup,filterSet);
                }
            }
            //filterHelper.updateAppliedFilterMapDptCollections(appliedFilterMap, null, null);
        }
        return appliedFilterMap;
    }

    private Filter getOldFilterFromNewFilter(com.mmt.hotels.clientgateway.request.Filter filter, FilterGroup filterGroup) {
        Filter oldFilter = new Filter();
        oldFilter.setFilterGroup(filterGroup);
        oldFilter.setFilterValue(filter.getFilterValue());
        oldFilter.setRangeFilter(filter.isRangeFilter());
        if(null != filter.getFilterRange()) {
            FilterRange filterRange = new FilterRange();
            filterRange.setMaxValue(filter.getFilterRange().getMaxValue());
            filterRange.setMinValue(filter.getFilterRange().getMinValue());
            oldFilter.setFilterRange(filterRange);
        }
        return oldFilter;
    }
}
