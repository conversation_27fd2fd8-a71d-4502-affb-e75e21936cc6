package com.mmt.hotels.clientgateway.transformer.response.cardEngine;

import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.PersonalizedSection;
import com.mmt.hotels.clientgateway.response.searchHotels.SearchHotelsResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.ArrayList;
import java.util.List;


@Component
public class ListingCollectionCardEngineResponseTransformer extends CardEngineResponseTransformer {

    @Autowired
    PolyglotService polyglotService;

    private static final Logger logger = LoggerFactory.getLogger(ListingCollectionCardEngineResponseTransformer.class);

    @Override
    public String transformCardEngineResponse(String cardEngineResponse) {
        return super.transformCardEngineResponse(cardEngineResponse);
    }

    @Override
    public SearchHotelsResponse updateSearchHotelsResponse(SearchHotelsResponse searchHotelsResponse, String cardEngineResponse, SearchHotelsRequest searchHotelsRequest) {
        if (CollectionUtils.isNotEmpty(searchHotelsResponse.getPersonalizedSections())) {
            List<PersonalizedSection> mergedSectionList = new ArrayList<>();
            PersonalizedSection mergedSection = new PersonalizedSection();
            List<Hotel> mergedHotels = new ArrayList<>();
            int totalHotelCount = 0;

            List<PersonalizedSection> sections = searchHotelsResponse.getPersonalizedSections();
            for (PersonalizedSection section : sections) {
                if (CollectionUtils.isNotEmpty(section.getHotels())) {
                    for(Hotel hotel : section.getHotels()){
                        hotel.setSimilarHotelsRequired(false);
                    }
                    mergedHotels.addAll(section.getHotels());
                    totalHotelCount += section.getHotels().size();
                }

                int index = sections.indexOf(section);

                if (index == sections.size()-1) {
                    String heading = getPersonalizedSectionHeading(cardEngineResponse,searchHotelsResponse,searchHotelsRequest);
                    if(StringUtils.isNotEmpty(heading)){
                        mergedSection.setHeading(heading);
                    } else {
                        mergedSection.setHeading(section.getHeading());
                    }
                    mergedSection.setName(section.getName());
                    mergedSection.setShowIndex(section.getShowIndex());
                    mergedSection.setHeadingVisible(section.getHeadingVisible());
                    mergedSection.setSubHeading(section.getSubHeading());
                    mergedSection.setSectionFeatures(section.getSectionFeatures());
                    mergedSection.setMinHotelsToShow(section.getMinHotelsToShow());
                    mergedSection.setSeeMoreCTA(section.getSeeMoreCTA());
                    mergedSection.setHotelCardType(section.getHotelCardType());
                    mergedSection.setSectionBG(section.getSectionBG());
                    mergedSection.setToolTip(section.getToolTip());
                    mergedSection.setOrientation(section.getOrientation());
                    mergedSection.setShowMore(section.isShowMore());
                    mergedSection.setMinItemsToShow(section.getMinItemsToShow());
                    mergedSection.setCardInsertionAllowed(section.isCardInsertionAllowed());
                    mergedSection.setFilterInfo(section.getFilterInfo());
                    mergedSection.setBottomSheet(section.getBottomSheet());
                    mergedSection.setMyBizSimilarHotel(section.getMyBizSimilarHotel());
                    mergedSection.setStaticCard(section.getStaticCard());
                    mergedSection.setPremium(section.isPremium());
                    mergedSection.setTopHeaderText(section.getTopHeaderText());
                    mergedSection.setFooterDetails(section.getFooterDetails());
                }
            }

            mergedSection.setName("RECOMMENDED_HOTELS");
            mergedSection.setHotels(mergedHotels);
            mergedSection.setHotelCount(totalHotelCount);
            mergedSectionList.add(mergedSection);
            searchHotelsResponse.setPersonalizedSections(mergedSectionList);
        }

        return searchHotelsResponse;
    }

    private String getPersonalizedSectionHeading(String cardEngineResponse, SearchHotelsResponse searchHotelsResponse, SearchHotelsRequest searchHotelsRequest) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(cardEngineResponse);

            String filterValue = jsonNode
                    .path("cardData")
                    .path("data")
                    .path("subcard")
                    .path("items")
                    .get(0)
                    .path("filters")
                    .get(0)
                    .path("filterValue")
                    .asText();

            boolean isCollectionApplied = false;

            if (CollectionUtils.isNotEmpty(searchHotelsRequest.getFilterCriteria())) {
                for (Filter filter : searchHotelsRequest.getFilterCriteria()) {
                    if (filter.getFilterValue().equalsIgnoreCase(filterValue)) {
                        isCollectionApplied = true;
                        break;
                    }
                }
            }

            if (isCollectionApplied) {
                String title = jsonNode
                        .path("cardData")
                        .path("data")
                        .path("subcard")
                        .path("items")
                        .get(0)
                        .path("title")
                        .asText();
                String heading = polyglotService.getTranslatedData(ConstantsTranslation.LISTING_COLLECTION_SECTION_HEADING)
                        .replace("{collection}", title)
                        .replace("{city}", searchHotelsResponse.getLocationDetail().getName());

                return heading;
            }
        } catch (Exception ex){
            logger.error("Error while making heading for personalized section", ex);
        }
        return "";
    }
}
