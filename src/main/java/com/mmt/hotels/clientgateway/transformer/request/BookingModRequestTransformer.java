package com.mmt.hotels.clientgateway.transformer.request;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.PaymentHelper;
import com.mmt.hotels.clientgateway.request.modification.*;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.request.ExtraInfo;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.GuestRecommendationEnabledReqBody;
import com.mmt.hotels.model.request.MultiCurrencyInfo;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.ResponseFilterFlags;
import com.mmt.hotels.model.request.RoomCriterion;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.request.corporate.ApprovalInfo;
import com.mmt.hotels.model.request.corporate.InitApprovalRequest;
import com.mmt.hotels.model.request.payment.BeginCheckoutReqBody;
import com.mmt.hotels.model.request.payment.ErrorConfig;
import com.mmt.hotels.model.request.payment.Gender;
import com.mmt.hotels.model.request.payment.PaymentDetail;
import com.mmt.hotels.model.request.payment.TravelerDetail;
import com.mmt.hotels.model.request.payment.WorkflowData;
import com.mmt.hotels.model.request.payment.WorkflowRequest;
import com.mmt.scrambler.exception.ScramblerClientException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class BookingModRequestTransformer {

    @Autowired
    CommonHelper commonHelper;

    @Autowired
    PaymentHelper paymentHelper;

    private static final Logger logger = LoggerFactory.getLogger(BookingModRequestTransformer.class);

    public PriceByHotelsRequestBody convertPriceRequest(RatePreviewRequest request, CommonModifierResponse commonModifierResponse,String pageContext){
        PriceByHotelsRequestBody priceByHotelsRequestBody = new PriceByHotelsRequestBody();
        priceByHotelsRequestBody.setApplicationId(Constants.PAGE_CONTEXT_REVIEW.equalsIgnoreCase(pageContext) ? "310":"410");
        priceByHotelsRequestBody.setAmendType(request.getAmendType());
        priceByHotelsRequestBody.setAppVersion(request.getAppVersion());
        priceByHotelsRequestBody.setBookingDevice(request.getClient());
        priceByHotelsRequestBody.setLob(request.getClient());
        priceByHotelsRequestBody.setDeviceId(request.getDeviceId());
        priceByHotelsRequestBody.setOldBookingId(request.getBookingId());
        //priceByHotelsRequestBody.setDeviceType(request.getDeviceType());
        priceByHotelsRequestBody.setHotelIds(request.getHotelIds());
        if(Constants.PAGE_CONTEXT_REVIEW.equalsIgnoreCase(pageContext))
            priceByHotelsRequestBody.setRoomCriteria(buildRoomCriteria(request.getRoomCriteria(), request.getHotelIds().get(0)));
        if(Constants.PAGE_CONTEXT_DETAIL.equalsIgnoreCase(pageContext) && request.isNonRefundableButAmendable()){
            priceByHotelsRequestBody.setRoomCriteria(buildRoomCriteria(request.getRoomCriteria(), request.getHotelIds().get(0)));
        }
        priceByHotelsRequestBody.setRoomStayCandidates(buildRoomStayCandidates(request.getRoomCriteria().stream().map(a -> a.getRoomStayCandidates()).flatMap(List::stream).collect(Collectors.toList())));
        priceByHotelsRequestBody.setCheckin(request.getCheckin());
        priceByHotelsRequestBody.setCheckout(request.getCheckout());
        priceByHotelsRequestBody.setCityCode(request.getCityCode());
        priceByHotelsRequestBody.setCountryCode(request.getCountryCode());
        priceByHotelsRequestBody.setLocationId(request.getCityCode());
        priceByHotelsRequestBody.setLocationType("city");
        priceByHotelsRequestBody.setCurrency(request.getCurrency()!=null ? request.getCurrency().toUpperCase() : null);
        priceByHotelsRequestBody.setAuthToken(commonModifierResponse.getMmtAuth());
        priceByHotelsRequestBody.setFunnelSource("HOTELS");
        priceByHotelsRequestBody.setIdContext(request.getIdContext());
        priceByHotelsRequestBody.setCouponCount(commonModifierResponse.getNumberOfCoupons());
        priceByHotelsRequestBody.setPnr(CollectionUtils.isNotEmpty(request.getPnr()) ? request.getPnr() : null);
        priceByHotelsRequestBody.setSubVendorCode(StringUtils.isNotEmpty(request.getSubVendorCode()) ? request.getSubVendorCode() : null);
        priceByHotelsRequestBody.setSourcePnr(StringUtils.isNotEmpty(request.getSourcePnr()) ? request.getSourcePnr() : null);
        ResponseFilterFlags responseFilterFlags = new ResponseFilterFlags();
        priceByHotelsRequestBody.setResponseFilterFlags(responseFilterFlags);
        responseFilterFlags.setStaticData(true);
        responseFilterFlags.setWalletRequired(true);
        responseFilterFlags.setPriceInfoReq(true);
        responseFilterFlags.setRoomLevelDetails(true);
        responseFilterFlags.setPriceInfoReq(true);
        responseFilterFlags.setBestCoupon(true);
        responseFilterFlags.setBookingModification(true);
        responseFilterFlags.setCityTaxExclusive(commonModifierResponse.isCityTaxExclusive());
        if(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(request.getIdContext())) {
            responseFilterFlags.setNewCorp(true);
            responseFilterFlags.setPersuasionSeg(null);
            responseFilterFlags.setCityTaxExclusive(null);
        }

        priceByHotelsRequestBody.setGuestRecommendationEnabled(buildGuestRecommendationEnabled());
        priceByHotelsRequestBody.setMobile(commonModifierResponse.getMobile());
        priceByHotelsRequestBody.setExperimentData(buildExpData(commonModifierResponse.getExpData(), request));
        priceByHotelsRequestBody.setGuestRecommendationEnabled(buildGuestRecommendationEnabled());
        priceByHotelsRequestBody.setChannel("B2Cweb");
        priceByHotelsRequestBody.setPageContext(pageContext);
        priceByHotelsRequestBody.setRequestType("B2CAgent");
        priceByHotelsRequestBody.setDomain("B2C");
        priceByHotelsRequestBody.setOldBookingId(request.getBookingId());
        priceByHotelsRequestBody.setPayMode(request.getPayMode());

        priceByHotelsRequestBody.setCdfContextId(commonModifierResponse.getCdfContextId());
        priceByHotelsRequestBody.setAffiliateId(commonModifierResponse.getAffiliateId());
        priceByHotelsRequestBody.setCorrelationKey(MDC.get(MDCHelper.MDCKeys.CORRELATION.getStringValue()));
        if(commonModifierResponse.getExtendedUser() != null){
            priceByHotelsRequestBody.setUuid(commonModifierResponse.getExtendedUser().getUuid());
            priceByHotelsRequestBody.setProfileType(commonModifierResponse.getExtendedUser().getProfileType());
            priceByHotelsRequestBody.setCorpUserID(commonModifierResponse.getExtendedUser().getProfileId());
            priceByHotelsRequestBody.setSubProfileType(commonModifierResponse.getExtendedUser().getAffiliateId());

        }
        if (CollectionUtils.isNotEmpty(request.getTravellerEmailCommIds())) {
            List<TravelerDetail> travelerDetailsList = new ArrayList<>();
            for (String emailComm : request.getTravellerEmailCommIds()) {
                TravelerDetail travelerDetail = new TravelerDetail();
                travelerDetail.setEmailCommId(emailComm);
                travelerDetailsList.add(travelerDetail);

            }
            priceByHotelsRequestBody.setTravelerDetailsList(travelerDetailsList);
        }
        priceByHotelsRequestBody.setSiteDomain(MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()));
        priceByHotelsRequestBody.setLoggedIn(true);
        priceByHotelsRequestBody.setNonRefundableButAmendable(request.isNonRefundableButAmendable());
        if (commonModifierResponse.getHydraResponse() != null) {
            priceByHotelsRequestBody.setHydraSegments(commonModifierResponse.getHydraResponse().getHydraMatchedSegment());
            priceByHotelsRequestBody.setFlightBooker(commonModifierResponse.getHydraResponse().isFlightBooker());
        }

        if(Constants.PAGE_CONTEXT_REVIEW.equalsIgnoreCase(pageContext)) {
            priceByHotelsRequestBody.setExtraInfo(new ExtraInfo());
            priceByHotelsRequestBody.getExtraInfo().setSearchType(priceByHotelsRequestBody.getRoomCriteria().size() > 1 ? "R":"E");
            priceByHotelsRequestBody.setMtKey(request.getHashKey());

            if(CollectionUtils.isNotEmpty(priceByHotelsRequestBody.getRoomCriteria())){
                priceByHotelsRequestBody.getRoomCriteria().forEach(a-> a.setMtKey(request.getHashKey()));
            }
        }
        if (request.isMultiCurrencyEnabled() && request.getCurrencyInfo() != null) {
            priceByHotelsRequestBody.setCurrency(request.getCurrencyInfo().getUserCurrency());
            priceByHotelsRequestBody.setMultiCurrencyInfo(getMultiCurrencyInfoForBkgModification(request.getCurrencyInfo()));
        }

        return priceByHotelsRequestBody;
    }

    public MultiCurrencyInfo getMultiCurrencyInfoForBkgModification(CurrencyInfo currencyInfo) {
        MultiCurrencyInfo multiCurrencyInfo = new MultiCurrencyInfo();
        multiCurrencyInfo.setIsMultiCurrencyV2FlowEnabled(true);
        multiCurrencyInfo.setHotelierCurrency(currencyInfo.getHotelCurrency());
        multiCurrencyInfo.setRegionCurrency(currencyInfo.getBaseCurrency());
        multiCurrencyInfo.setUserCurrency(currencyInfo.getUserCurrency());
        //Old booking conversion factors
        multiCurrencyInfo.setRegionToUserCurrencyConvFactor(currencyInfo.getBaseToUserConversionRate());
        multiCurrencyInfo.setHotelierToRegionCurrencyConvFactor(currencyInfo.getHotelToBaseConversionRate());
        return multiCurrencyInfo;
    }

    public BeginCheckoutReqBody convertPaymentRequest(ProBookingRequest request ,Map<String, String> headers) throws ClientGatewayException, ScramblerClientException {
        BeginCheckoutReqBody beginCheckoutReqBody = new BeginCheckoutReqBody();
        beginCheckoutReqBody.setCorrelationKey(MDC.get(MDCHelper.MDCKeys.CORRELATION.getStringValue()));
        beginCheckoutReqBody.setAuthToken(request.getAuthToken());
        beginCheckoutReqBody.setCurrency(request.getCurrency());
        beginCheckoutReqBody.setTransactionKey(request.getHashKey());
        beginCheckoutReqBody.setSiteDomain(MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()));
        beginCheckoutReqBody.setDomainCountry(beginCheckoutReqBody.getSiteDomain());
        beginCheckoutReqBody.setDomainLanguage(MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue()));
        beginCheckoutReqBody.setPaymentDetail(new PaymentDetail());
        beginCheckoutReqBody.getPaymentDetail().setChannel(request.getPaymentDetail().getChannel());
        beginCheckoutReqBody.getPaymentDetail().setMode(request.getPaymentDetail().getMode());
        beginCheckoutReqBody.getPaymentDetail().setIsBNPL(request.getPaymentDetail().isBnpl());
        beginCheckoutReqBody.getPaymentDetail().setPartialPayment(request.getPaymentDetail().isPartialPayment());
        beginCheckoutReqBody.setModificationFlow(true);
        populateTravellerDetails(request.getTravellerDetailList(), request.getGstDetails());
        if(StringUtils.isNotBlank(request.getDetailPageUrl() )) {
            beginCheckoutReqBody.setErrorConfig(new ErrorConfig());
            beginCheckoutReqBody.getErrorConfig().setSessionTimeoutURL(request.getDetailPageUrl());
        }
        beginCheckoutReqBody.setTravelerDetailsList(populateTravellerDetails(request.getTravellerDetailList(), request.getGstDetails()));
        paymentHelper.modifyPaymentRequest(beginCheckoutReqBody, headers);
        beginCheckoutReqBody.setIdContext(
                commonHelper.updateIdContext(Constants.PROFILE_CORPORATE.equalsIgnoreCase(
                        beginCheckoutReqBody.getUserDetail().getProfileType())?"CORP":"B2C"   , request.getClient()));
        if (Constants.CORP_ID_CONTEXT.equalsIgnoreCase(beginCheckoutReqBody.getIdContext())){
            beginCheckoutReqBody.setWorkflowData(new WorkflowData());
            beginCheckoutReqBody.getWorkflowData().setInitiateApprovalWorkflowRequest(new WorkflowRequest());
            beginCheckoutReqBody.getWorkflowData().setWorkflowStatus(request.getWorkflowStatus());
        }
        if (request.isMultiCurrencyEnabled() && request.getCurrencyInfo() != null) {
            beginCheckoutReqBody.setCurrency(request.getCurrencyInfo().getUserCurrency());
            beginCheckoutReqBody.setMultiCurrencyInfo(getMultiCurrencyInfoForBkgModification(request.getCurrencyInfo()));
        }
        return  beginCheckoutReqBody;
    }

    public InitApprovalRequest convertRequestApproval(ProBookingRequest request ,Map<String, String> headers ){
        InitApprovalRequest hesInitApprovalRequest = new InitApprovalRequest();

        hesInitApprovalRequest.setTxnKey(request.getHashKey());
        hesInitApprovalRequest.setTravelerDetailsList(populateTravellerDetails(request.getTravellerDetailList(),request.getGstDetails()));
        hesInitApprovalRequest.setApprovalInfo(new ApprovalInfo());
        hesInitApprovalRequest.getApprovalInfo().setEmployeeComment(request.getModificationReason());
        hesInitApprovalRequest.getApprovalInfo().setWorkflowStatus(request.getWorkflowStatus());
        populateUserDetails( headers , hesInitApprovalRequest);
        return hesInitApprovalRequest;
    }

    private void populateUserDetails( Map<String, String> headers, InitApprovalRequest hesInitApprovalRequest) {

        try{
                String mmtAuth = commonHelper.getAuthToken(headers);
                hesInitApprovalRequest.setCorrelationKey(MDC.get(MDCHelper.MDCKeys.CORRELATION.getStringValue()));
                UserServiceResponse userServiceRsp = commonHelper.getUserDetails(mmtAuth, "","","", hesInitApprovalRequest.getCorrelationKey(), Constants.CORP_ID_CONTEXT, null,null,headers);
                if(userServiceRsp!=null && userServiceRsp.getResult()!=null
                        && userServiceRsp.getResult().getExtendedUser()!=null) {
                    ExtendedUser user = userServiceRsp.getResult().getExtendedUser();
                    hesInitApprovalRequest.setCorpUserId(user.getProfileId());
                    hesInitApprovalRequest.setUuid(user.getUuid());
                    hesInitApprovalRequest.setMmtAuth(mmtAuth);
                }

        }catch (Exception e){
            logger.error("Error occurred while populating user info in init approval request during booking modification flow : {}", e.getMessage(), e);
        }

    }

    private List<TravelerDetail> populateTravellerDetails(List<ModifiedTravellerDetail> reqTrvlrList, GSTDetails gstDetails) {
        List<TravelerDetail> trvlrList = new ArrayList<>();
        for (ModifiedTravellerDetail trvlr : reqTrvlrList) {
            TravelerDetail newTrvlr = new TravelerDetail();
            newTrvlr.setEmailID(trvlr.getEmail());
            newTrvlr.setFirstName(trvlr.getFirstName());
            newTrvlr.setIsdCode(trvlr.getIsdCode());
            newTrvlr.setLastName(trvlr.getLastName());
            newTrvlr.setMasterPax(trvlr.isMasterPax());
            newTrvlr.setMobileNo(trvlr.getMobileNo());
            newTrvlr.setRoomNo(trvlr.getRoomNo());
            String title =  trvlr.getSalutation() != null && StringUtils.isNotEmpty(trvlr.getSalutation().getSalutation()) ? trvlr.getSalutation().getSalutation() :
                    trvlr.getTitle() == 1 ? "Mr." : (trvlr.getTitle() == 2 ? "Ms." : "Mrs.");
            newTrvlr.setTitle(title);
            newTrvlr.setGender(trvlr.getTitle() == 1 ? Gender.MALE : Gender.FEMALE);
            if(null!=gstDetails) {
                newTrvlr.setGstinCompanyName(gstDetails.getGstCompanyName());
                newTrvlr.setGstinCompanyName(gstDetails.getGstCompanyAddress());
                newTrvlr.setRegisterGstinNum(gstDetails.getGstNo());
            }
            //missing pan and gst
            trvlrList.add(newTrvlr);
        }
        return  trvlrList;
    }

    private List<RoomCriterion> buildRoomCriteria(List<RateReviewSearchCriteria> reqRoomCriteria,String hotelId){
        List<RoomCriterion> rcList = new ArrayList<>();
        for(RateReviewSearchCriteria reqCriteria : reqRoomCriteria){
            RoomCriterion rc = new RoomCriterion();
            rc.setHotelId(hotelId);
            rc.setRoomStayCandidates(buildRoomStayCandidates(reqCriteria.getRoomStayCandidates()));
            rc.setRoomCode(reqCriteria.getRoomCode());
            rc.setRatePlanCode(reqCriteria.getRatePlanCode());
            rc.setSupplierCode(reqCriteria.getSupplierCode());
            rcList.add(rc);
        }
        return rcList;
    }

    private List<RoomStayCandidate> buildRoomStayCandidates(List<ModifiedRoomStayCandidate> roomStayCandidates) {

        if(roomStayCandidates==null)
            return null;

        List<RoomStayCandidate> roomStayCandidateList = new ArrayList<>();

        for (ModifiedRoomStayCandidate roomStayCandidateCG : roomStayCandidates){
            RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
            roomStayCandidate.setGuestCounts(buildGuestCounts(roomStayCandidateCG));
            roomStayCandidateList.add(roomStayCandidate);
        }

        return roomStayCandidateList;
    }

    private List<GuestCount> buildGuestCounts(
            ModifiedRoomStayCandidate roomStayCandidateCG) {
        List<GuestCount> guestCounts = new ArrayList<>();
        for(ModifiedGuestCount mcg: roomStayCandidateCG.getGuestCountList()) {
            GuestCount guestCount = new GuestCount();
            guestCount.setAgeQualifyingCode("10");
            guestCount.setAges(mcg.getChildAges());
            guestCount.setCount(String.valueOf(mcg.getAdultCount()));
            guestCounts.add(guestCount);
        }
        return guestCounts;
    }

    private GuestRecommendationEnabledReqBody buildGuestRecommendationEnabled() {
        GuestRecommendationEnabledReqBody guestRecommendationEnabledReqBody = new GuestRecommendationEnabledReqBody();
        guestRecommendationEnabledReqBody.setMaxRecommendations("1");
        guestRecommendationEnabledReqBody.setText("true");
        return guestRecommendationEnabledReqBody;
    }

    private String buildExpData(String expDataReq,RatePreviewRequest request){
        Map<String, String> existingExpData = new HashMap<>();
        if (!StringUtils.isEmpty(expDataReq)) {
            String experimentString = expDataReq.replaceAll("^\"|\"$", "");
            Type type = new TypeToken<Map<String, String>>() {
            }.getType();

            existingExpData = new Gson().fromJson(experimentString, type);
        }

        if (!existingExpData.containsKey("MRS"))
            existingExpData.put("MRS", Constants.CORP_ID_CONTEXT.equalsIgnoreCase(request.getIdContext()) ? "F":"T");
        if (!existingExpData.containsKey("BNPL"))
            existingExpData.put("BNPL","T");
        if (!existingExpData.containsKey("PDO"))
            existingExpData.put("PDO","TPT");
        if (!existingExpData.containsKey("BLACK"))
            existingExpData.put("BLACK","T");
        if (!existingExpData.containsKey("GCCBNPL"))
            existingExpData.put("GCCBNPL","T");
        if (!existingExpData.containsKey("PAH"))
            existingExpData.put("PAH","5");
        if (!existingExpData.containsKey("CHPC"))
            existingExpData.put("CHPC","T");
        if (!existingExpData.containsKey("WPAH"))
            existingExpData.put("WPAH","F");
        if (!existingExpData.containsKey("RCPN"))
            existingExpData.put("RCPN","T");
        if (!existingExpData.containsKey("NEWTY"))
            existingExpData.put("NEWTY","T");
        if(existingExpData.containsKey("apiExperiment"))
            existingExpData.put("APE",existingExpData.get("apiExperiment"));
        if(existingExpData.containsKey("htlDummyPanCardExperiment"))
            existingExpData.put("DPCR",existingExpData.get("htlDummyPanCardExperiment"));
        if (!existingExpData.containsKey("MCUR"))
            existingExpData.put("MCUR","T");
        if (!existingExpData.containsKey("B2BPAH"))
            existingExpData.put("B2BPAH","T");
        if(request.getBnplVariant() != null && request.getBnplVariant().equals(BNPLVariant.BNPL_AT_0.name())){
            existingExpData.put("disableBNPL", "false");
            existingExpData.put("bnplZeroVariant", "true");
            existingExpData.put("bnplNewVariant", "false");
            existingExpData.put("BNPL0", "T");
        }
        else if(request.getBnplVariant() != null && request.getBnplVariant().equals(BNPLVariant.BNPL_AT_1.name())){
            existingExpData.put("disableBNPL", "false");
            existingExpData.put("bnplZeroVariant", "false");
            existingExpData.put("bnplNewVariant", "true");
        }
        else if(request.getBnplVariant() == null || request.getBnplVariant().equals(BNPLVariant.BNPL_NOT_APPLICABLE.name())){
            existingExpData.put("bnplZeroVariant", "false");
            existingExpData.put("bnplNewVariant", "false");
        }

        return new Gson().toJson(existingExpData);
    }
}
