package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.request.filter.FilterAppliedData;
import com.mmt.hotels.clientgateway.request.filter.EvaluateContextDeviceDetails;
import com.mmt.hotels.clientgateway.request.filter.EvaluateContextRequestDetails;
import com.mmt.hotels.clientgateway.request.filter.EvaluateContextUserDetails;
import com.mmt.hotels.clientgateway.request.filter.EvaluateFilterRankOrderRequest;
import com.mmt.hotels.clientgateway.request.filter.Filter;
import com.mmt.hotels.clientgateway.request.filter.StayDetails;
import com.mmt.hotels.clientgateway.response.filter.ContextDetails;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.ResponseFilterFlags;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class FilterRequestTransformer extends BaseSearchRequestTransformer {

    public SearchWrapperInputRequest convertSearchRequest(FilterCountRequest filterCountRequest, CommonModifierResponse commonModifierResponse) {

        SearchWrapperInputRequest searchWrapperInputRequest =  super.convertSearchRequest(filterCountRequest, commonModifierResponse);

        if (filterCountRequest.getFeatureFlags() == null)
            filterCountRequest.setFeatureFlags(new FeatureFlags());
        buildResponseFilterFlags(searchWrapperInputRequest, filterCountRequest.getFeatureFlags());
        searchWrapperInputRequest.setContextDetails(buildContextDetails(filterCountRequest.getContextDetails()));
        setInitialCohortId(filterCountRequest, searchWrapperInputRequest);
        if (filterCountRequest.getSearchCriteria() != null && filterCountRequest.getSearchCriteria().getMultiCurrencyInfo() != null) {
            searchWrapperInputRequest.setMultiCurrencyInfo(utility.buildMultiCurrencyInfoRequest(filterCountRequest.getSearchCriteria().getMultiCurrencyInfo()));
        }
        if (filterCountRequest.getSearchCriteria().getUserGlobalInfo() != null) {
            searchWrapperInputRequest.setUserGlobalInfo(utility.buildUserGlobalInfoHES(filterCountRequest.getSearchCriteria().getUserGlobalInfo()));
        }
        return searchWrapperInputRequest;
    }

    /**
     * Set the initialCohortId if coming from the client
     */
    private void setInitialCohortId(FilterCountRequest filterCountRequest, SearchWrapperInputRequest searchWrapperInputRequest) {
        if (StringUtils.isNotBlank(filterCountRequest.getInitialCohortId())) {
            searchWrapperInputRequest.setInitialCohortId(filterCountRequest.getInitialCohortId());
        }
    }

    private com.mmt.hotels.filter.ContextDetails buildContextDetails(ContextDetails contextDetails) {
        if(contextDetails == null) {
            return null;
        }
        com.mmt.hotels.filter.ContextDetails contextDetailsHES = new com.mmt.hotels.filter.ContextDetails();
        contextDetailsHES.setContext(contextDetails.getContext());
        contextDetailsHES.setAltAccoIntent(contextDetails.isAltAccoIntent());
        return contextDetailsHES;
    }

    private ResponseFilterFlags buildResponseFilterFlags(SearchWrapperInputRequest searchWrapperInputRequest, FeatureFlags featureFlags) {
        ResponseFilterFlags responseFilterFlags = new ResponseFilterFlags();
        responseFilterFlags.setStaticData(featureFlags.isStaticData());
        responseFilterFlags.setFlyfishSummaryRequired(featureFlags.isReviewSummaryRequired());
        responseFilterFlags.setWalletRequired(featureFlags.isWalletRequired());
        responseFilterFlags.setShortlistRequired(featureFlags.isShortlistingRequired());
        Optional.ofNullable(featureFlags.getMaskedPropertyName()).ifPresent(responseFilterFlags::setMaskedPropertyName);
        searchWrapperInputRequest.setNumberOfAddons(featureFlags.getNoOfAddons());
        searchWrapperInputRequest.setNumberOfCoupons(featureFlags.getNoOfCoupons());
        searchWrapperInputRequest.setNoOfPersuasions(featureFlags.getNoOfPersuasions());
        searchWrapperInputRequest.setNumberOfSoldOuts(featureFlags.getNoOfSoldouts());
        return responseFilterFlags;
    }

    public EvaluateFilterRankOrderRequest buildEvaluateFilterRankOrderRequest(SearchWrapperInputRequest filterRequest) {
        List<Filter> appliedFilters = new ArrayList<>();
        if (MapUtils.isNotEmpty(filterRequest.getAppliedFilterMap())) {
            filterRequest.getAppliedFilterMap()
                    .forEach((filterGroup, filters) -> {
                        filters.forEach(filter -> appliedFilters.add(Filter.builder()
                                .filterValue(filter.getFilterValue()).filterGroup(filter.getFilterGroup().name()).build()));

                    });
        }
        int adultCount = 0;
        int childCount = 0;
        int roomCount = 0;
        if (CollectionUtils.isNotEmpty(filterRequest.getRoomStayCandidates())) {
            for (RoomStayCandidate roomStayCandidate : filterRequest.getRoomStayCandidates()) {
                if (CollectionUtils.isNotEmpty(roomStayCandidate.getGuestCounts())) {
                    for (GuestCount guest : roomStayCandidate.getGuestCounts()) {
                        adultCount = adultCount + Integer.parseInt(guest.getCount());
                        childCount = childCount + (CollectionUtils.isNotEmpty(guest.getAges()) ? guest.getAges().size() : 0);

                    }
                    roomCount = roomCount + 1;
                }
            }
        }
        return EvaluateFilterRankOrderRequest.builder()
                .userDetails(EvaluateContextUserDetails.builder()
                        .userHydraSegment(filterRequest.getUserSegments())
                        .profileType(filterRequest.getProfileType())
                        .subProfileType(filterRequest.getSubProfileType())
                        .loggedIn(filterRequest.getLoggedIn())
                        .build())
                .requestDetails(EvaluateContextRequestDetails.builder()
                        .funnelSource(filterRequest.getFunnelSource())
                        .idContext(filterRequest.getIdContext())
                        .pageContext(filterRequest.getPageContext())
                        .region(StringUtils.isEmpty(filterRequest.getSiteDomain()) ? "in" : filterRequest.getSiteDomain())
                        .trafficType(getTrafficType(filterRequest))
                        .trafficSource(filterRequest.getTrafficSource() != null ? filterRequest.getTrafficSource().getSource() : null)
                        .pokusExperiments(filterRequest.getExperimentData())
                        .appliedFilters(appliedFilters)
                        .countryCode(filterRequest.getCountryCode())
                        .build())
                .deviceDetails(EvaluateContextDeviceDetails.builder()
                        .deviceType(filterRequest.getBookingDevice())
                        .appVersion(filterRequest.getAppVersion())
                        .build())
                .stayDetails(StayDetails.builder()
                        .locationId(StringUtils.isNotEmpty(filterRequest.getLocationId()) ? filterRequest.getLocationId() : (StringUtils.isNotEmpty(filterRequest.getCityCode()) ? filterRequest.getCityCode() : null))
                        .locationType(filterRequest.getLocationType())
                        .checkIn(filterRequest.getCheckin())
                        .checkOut(filterRequest.getCheckout())
                        .adultCount(adultCount)
                        .childCount(childCount)
                        .roomCount(roomCount)
                        .build())
                .filterAppliedData(FilterAppliedData.builder()
                        .appliedFilters(appliedFilters)
                        .build())
                .build();
    }

    private String getTrafficType(SearchWrapperInputRequest filterRequest) {
        if (Constants.PREMIUM.equalsIgnoreCase(filterRequest.getReqContext())) {
            return Constants.PREMIUM;
        }
        return filterRequest.getTrafficSource() != null ? filterRequest.getTrafficSource().getType() : null;
    }


}
