package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gommt.hotels.orchestrator.detail.model.response.HotelStaticContentResponse;
import com.gommt.hotels.orchestrator.detail.model.response.common.DisplayItem;
import com.gommt.hotels.orchestrator.detail.model.response.content.HotelMetaData;
import com.gommt.hotels.orchestrator.detail.model.response.content.Media;
import com.gommt.hotels.orchestrator.detail.model.response.content.PlacesResponse;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.ProfessionalMediaEntity;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.TravellerMediaEntity;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.TreelsMediaEntity;
import com.gommt.hotels.orchestrator.detail.model.response.content.places.Category;
import com.gommt.hotels.orchestrator.detail.model.response.content.places.CategoryDatum;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.ArrangementInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo;
import com.gommt.hotels.orchestrator.detail.model.response.mypartner.MyPartnerMetaResponse;
import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.HotelPersuasionData;
import com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData;
import com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.PersonalizationCards;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.HostReviewSummary;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.RatingData;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.ReviewDescription;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.TopicRating;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.TravellerReviewSummary;
import com.gommt.hotels.orchestrator.model.response.listing.HotelDetails;
import com.gommt.hotels.orchestrator.model.response.listing.PersonalizedSectionDetails;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.MyPartnerConfigConsul;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.request.StaticDetailCriteria;
import com.mmt.hotels.clientgateway.request.StaticDetailRequest;
import com.mmt.hotels.clientgateway.request.UserGlobalInfo;
import com.mmt.hotels.clientgateway.response.ConceptSummary;
import com.mmt.hotels.clientgateway.response.LocationPersuasions;
import com.mmt.hotels.clientgateway.response.ManualPersuasion;
import com.mmt.hotels.clientgateway.response.PlacesResponseCG;
import com.mmt.hotels.clientgateway.response.ReviewObject;
import com.mmt.hotels.clientgateway.response.ReviewSummary;
import com.mmt.hotels.clientgateway.response.SeekTagDetails;
import com.mmt.hotels.clientgateway.response.TopicSummary;
import com.mmt.hotels.clientgateway.response.UGCRatingDataCG;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.moblanding.CardAction;
import com.mmt.hotels.clientgateway.response.moblanding.CardActionData;
import com.mmt.hotels.clientgateway.response.moblanding.CardCondition;
import com.mmt.hotels.clientgateway.response.moblanding.CardInfo;
import com.mmt.hotels.clientgateway.response.moblanding.CardPayloadData;
import com.mmt.hotels.clientgateway.response.moblanding.CardSheet;
import com.mmt.hotels.clientgateway.response.moblanding.CardSheetElem;
import com.mmt.hotels.clientgateway.response.moblanding.FloatingSheetData;
import com.mmt.hotels.clientgateway.response.moblanding.GenericCardPayloadDataCG;
import com.mmt.hotels.clientgateway.response.moblanding.Item;
import com.mmt.hotels.clientgateway.response.moblanding.ListPersonalizationResponse;
import com.mmt.hotels.clientgateway.response.moblanding.Meta;
import com.mmt.hotels.clientgateway.response.moblanding.RushDealTimerInfo;
import com.mmt.hotels.clientgateway.response.moblanding.SavedCardTracking;
import com.mmt.hotels.clientgateway.response.moblanding.Section;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.response.searchHotels.BorderGradient;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.staticdetail.ComparatorResponse;
import com.mmt.hotels.clientgateway.response.staticdetail.LiteResponseTravellerImage;
import com.mmt.hotels.clientgateway.response.staticdetail.MediaInfo;
import com.mmt.hotels.clientgateway.response.staticdetail.ReportCardPersuasion;
import com.mmt.hotels.clientgateway.response.staticdetail.StaticDetailResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.ChatBotPersuasionHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.HotelDetailHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.MediaResponseHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.MyPartnerResponseHelper;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.ReArchUtility;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.enums.ToggleAction;
import com.mmt.hotels.model.persuasion.response.BgGradient;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.request.upsell.Action;
import com.mmt.hotels.model.request.upsell.BannerInfo;
import com.mmt.hotels.model.response.listpersonalization.Filters;
import com.mmt.hotels.model.response.staticdata.Availability;
import com.mmt.hotels.model.response.staticdata.ImageData;
import com.mmt.hotels.model.response.staticdata.Responsibilities;
import com.mmt.hotels.model.response.staticdata.SpecialisedIn;
import com.mmt.hotels.model.response.staticdata.Staff;
import com.mmt.hotels.model.response.staticdata.StaffData;
import com.mmt.hotels.model.response.staticdata.StaffInfo;
import com.mmt.hotels.model.response.staticdata.Subtag;
import com.mmt.hotels.model.response.staticdata.Tag;
import com.mmt.hotels.model.response.staticdata.TreelGalleryData;
import com.mmt.hotels.model.response.staticdata.TreelMedia;
import com.mmt.hotels.pojo.HostSummary.HostImpressions;
import com.mmt.hotels.pojo.HostSummary.HostRatingInfo;
import com.mmt.hotels.pojo.HostSummary.UGCHostSummaryResponse;
import com.mmt.hotels.pojo.listing.personalization.BGLinearGradient;
import com.mmt.hotels.pojo.listing.personalization.IconTag;
import com.mmt.hotels.pojo.response.detail.ImageAuthor;
import com.mmt.hotels.pojo.response.detail.PoiImage;
import com.mmt.hotels.pojo.response.detail.placesapi.DirectionDetails;
import com.mmt.hotels.pojo.response.detail.placesapi.Location;
import com.mmt.model.SleepingArrangement;
import com.mmt.model.UGCRatingData;
import com.mmt.model.util.RatingDetail;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.BEDS;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.HOST_NO_RATING_TEXT;
import static com.mmt.hotels.clientgateway.enums.ExperimentKeys.CHATBOT_HOOKS_EXP;
import static com.mmt.hotels.clientgateway.enums.ExperimentKeys.LOCATION_SECTION_RATING;
import static com.mmt.hotels.clientgateway.util.DateUtil.MMDDYYYY;
import static com.mmt.hotels.clientgateway.util.Utility.isMyPartnerRequest;
import static java.lang.Math.min;


public abstract class OrchStaticDetailResponseTransformer extends HotelDetailHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrchStaticDetailResponseTransformer.class);
    private static final ObjectMapper MAPPER = new ObjectMapper();

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    private ReArchUtility utility;

    @Autowired
    private DateUtil dateUtil;

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private MyPartnerConfigConsul myPartnerConfigConsul;

    @Autowired
    MediaResponseHelper mediaResponseHelper;

    @Autowired
    ChatBotPersuasionHelper chatBotPersuasionHelper;

    @Autowired
    protected CommonResponseTransformer commonResponseTransformer;

    @Autowired
    private OrchTravellerSummaryResponseTransformer travellerSummaryResponseTransformer;

    @Autowired
    private MyPartnerResponseHelper myPartnerResponseHelper;

    @Autowired
    private OrchSearchHotelsResponseTransformerSCION orchSearchHotelsResponseTransformer;

    @Value("${hostImpression.title.tag.url}")
    private String hostImpressionTitleTagUrl;

    @Value("${new.icon.url}")
    private String newIconUrl;

    @Value("${sponsored.hotel.icon.url}")
    private String sponsoredHotelIconUrl;

    @Value("${deep.link.url.comparator}")
    protected String basicDetailDeeplink;

    @Value("${deep.link.url.comparator.global}")
    protected String basicDetailDeeplinkGlobal;

    @Value("${deep.link.url.comparator.myPartner}")
    protected String basicDetailDeeplinkMyPartner;

    @Value("${reposition.index.comparator}")
    protected int repositionIndex;

    @Value("${loved.by.indians.icon.url}")
    private String lovedByIndiansIconUrl;

    /**
     * Maps fields from HotelStaticContentResponse (Orchestrator) to StaticDetailResponse (ClientGateway).
     * This is a utility for use in the new re-arch flow.
     *
     * @param source The orchestrator response object (should be HotelStaticContentResponse)
     * @return The mapped StaticDetailResponse
     */
    public StaticDetailResponse convertStaticDetailResponse(StaticDetailRequest staticDetailRequest,
                                                            HotelStaticContentResponse source,
                                                            CommonModifierResponse commonModifierResponse) {
        if (source == null) return null;
        StaticDetailResponse target = new StaticDetailResponse();
        target.setWeaverResponse(buildWeaverResponse(source.getWeaverResponse()));
        Map<String, String> expDataMap = utility.getExpDataMap(staticDetailRequest.getExpData());
        StaticDetailCriteria staticDetailCriteria = staticDetailRequest.getSearchCriteria();

        boolean isGroupBookingFunnel = Utility.isGroupBookingFunnel(staticDetailRequest.getRequestDetails() != null ? staticDetailRequest.getRequestDetails().getFunnelSource() : null);
        boolean myPartnerReq = commonModifierResponse != null && commonModifierResponse.getExtendedUser() != null &&
                isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
        boolean isLiteResponse = staticDetailRequest.getFeatureFlags() != null && staticDetailRequest.getFeatureFlags().isLiteResponse();
        boolean isLuxe = source.getHotelMetaData() != null && source.getHotelMetaData().getPropertyDetails() != null && source.getHotelMetaData().getPropertyDetails().getCategories() != null && source.getHotelMetaData().getPropertyDetails().getCategories().contains(LUXURY_HOTELS);
        boolean isChatBotEnable = source.getHotelMetaData() != null && source.getHotelMetaData().getPropertyFlags() != null &&
                source.getHotelMetaData().getPropertyFlags().isChatbotEnabled() && utility.isExperimentOn(expDataMap, CHATBOT_HOOKS_EXP.getKey());
        boolean isImageExpEnable = MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(IMAGES_EXP_ENABLE) && TRUE.equalsIgnoreCase(expDataMap.get(IMAGES_EXP_ENABLE));
        String listingType = source.getHotelMetaData() != null && source.getHotelMetaData().getPropertyDetails() != null ?
                source.getHotelMetaData().getPropertyDetails().getListingType() : StringUtils.EMPTY;
        String countryCode = staticDetailCriteria != null && staticDetailCriteria.getCountryCode() != null ?
                staticDetailCriteria.getCountryCode() : StringUtils.EMPTY;
        DeviceDetails deviceDetails = staticDetailRequest.getDeviceDetails();
        String funnel = staticDetailRequest.getRequestDetails().getFunnelSource();
        String affiliateId = MapUtils.isNotEmpty(source.getClientProps()) &&
                source.getClientProps().containsKey("affiliateId") ? source.getClientProps().get("affiliateId").toString() : EMPTY_STRING;
        boolean isPremiumExperienceEnabled = utility.isExperimentOn(expDataMap, SUPER_PREMIUM_EXPERIENCE);


        String rscValue = staticDetailRequest.getSearchCriteria() != null ?
                utility.buildRscValue(staticDetailRequest.getSearchCriteria().getRoomStayCandidates()) : StringUtils.EMPTY;
        List<RoomStayCandidate> distributedRoomStayCandidateList = Collections.emptyList();
        if (utility.isDistributeRoomStayCandidates(staticDetailRequest.getSearchCriteria().getRoomStayCandidates(), staticDetailRequest.getExpDataMap())) {
            distributedRoomStayCandidateList = utility.buildRoomStayDistribution(staticDetailRequest.getSearchCriteria().getRoomStayCandidates(), staticDetailRequest.getExpDataMap());
        }

        // set ugc Summary
        target.setUgcSummary(travellerSummaryResponseTransformer.mapUgcSummary(source.getTravellerReviewSummary(), expDataMap, countryCode));

        // Map roomInfoMap
        if (source.getRoomInfoMap() != null) {
            Map<String, RoomDetails> roomDetailsMap = new HashMap<>();
            for (Map.Entry<String, RoomInfo> entry : source.getRoomInfoMap().entrySet()) {
                roomDetailsMap.put(entry.getKey(), mapRoomInfoToRoomDetails(entry.getValue(), source.getMedia()));
            }
            target.setRoomInfoMap(roomDetailsMap);
        }

        // Map media/images/mediaV2
        if (source.getMedia() != null && source.getHotelMetaData() != null) {
            target.setMediaV2(mediaResponseHelper.buildMedia(source.getMedia(), isChatBotEnable, isImageExpEnable, isLuxe, staticDetailRequest.getClient(), listingType,isPremiumExperienceEnabled));
        }


        if (isLiteResponse && CollectionUtils.isEmpty(staticDetailRequest.getUuids())) {
            if (target.getMediaV2() != null) {
                //set grid
                if (target.getMediaV2().getGrid() != null && CollectionUtils.isNotEmpty(target.getMediaV2().getGrid().getImages())) {
                    target.setHotelGridImages(target.getMediaV2().getGrid().getImages());
                }

                //set hotelImageListCount
                if (target.getMediaV2().getHotel() != null && CollectionUtils.isNotEmpty(target.getMediaV2().getHotel().getTags())) {
                    target.setHotelImageListCount(getMediaV2HotelMediaListCount(target.getMediaV2().getHotel().getTags()));
                }

                //set hotelImageByTravellerCount and hotelImageByTravellerL1
                if (target.getMediaV2().getTraveller() != null && CollectionUtils.isNotEmpty(target.getMediaV2().getTraveller().getTags())) {
                    List<LiteResponseTravellerImage> liteResponseTravellerImages = getMediaV2TravellerMediaList(target.getMediaV2().getTraveller().getTags());
                    if (CollectionUtils.isNotEmpty(liteResponseTravellerImages)) {
                        target.setHotelImageByTravellerCount(liteResponseTravellerImages.size());
                        target.setHotelImageByTravellerL1(liteResponseTravellerImages.subList(0, min(liteResponseTravellerImages.size(), 4)));
                    }
                }
            }
        }

        // Map hotelDetails
        if (source.getHotelMetaData() != null) {
            target.setHotelDetails(getHotelResult(staticDetailRequest, source.getHotelMetaData(),
                    expDataMap, staticDetailCriteria, commonModifierResponse, deviceDetails, isCorpIdContext(staticDetailRequest),
                    isGroupBookingFunnel, funnel, myPartnerReq, affiliateId));
        }

        if (source.getHotelMetaData() != null && source.getHotelMetaData().getHostingInfo() != null && source.getHotelMetaData().getHostingInfo().getStaffInfo() != null) {
            target.getHotelDetails().setStaffInfo(convertStaffInfo(source.getHotelMetaData().getHostingInfo().getStaffInfo()));
        }

        if (MapUtils.isNotEmpty(source.getComparatorResponse()) && null != staticDetailRequest.getRequiredApis() && staticDetailRequest.getRequiredApis().isComparatorV2Required()) {
            SearchHotelsRequest searchHotelsRequest = transformStaticDetailRequestToSearchHotelsRequest(staticDetailRequest);
            target.setHotelCompareResponse(buildHotelCompareResponseResponse(staticDetailRequest, searchHotelsRequest, source.getComparatorResponse(), expDataMap, isLiteResponse, commonModifierResponse, rscValue, myPartnerReq));
            if (source.getComparatorResponse().containsKey("CHAIN_HOTELS")) {
                target.setChainCompareResponse(buildChainComparatorResponse(staticDetailRequest, searchHotelsRequest, source.getComparatorResponse().get("CHAIN_HOTELS"), expDataMap, isLiteResponse, commonModifierResponse));
            }
        }

        if (source.getHostReviewSummary() != null) {
            target.setHostSummary(mapHostReviewSummary(source.getHostReviewSummary()));
        }

        if (source.getMedia() != null && source.getMedia().getTreelsImages() != null) {
            target.setTreelGalleryData(mapTreelsImagesToTreelGalleryData(source.getMedia().getTreelsImages()));
        }

        if (source.getPlacesResponse() != null) {
            target.setPlacesResponse(modifyPlacesResponse(source.getPlacesResponse(), expDataMap));
            target.setPlacesResponseV2(mapToPlacesResponseCG(source.getPlacesResponse(), expDataMap));
        }

        target.setDetailPersuasionCards(buildListPersonalizationResponse(source.getPersonalizationCards()));

        target.setUuids(source.getPendingRequestsUuids() != null ? source.getPendingRequestsUuids() : Collections.EMPTY_SET);
        target.setCompletedRequests(source.getCompletedRequestsUuids());
        if (source.getCompletedRequestsUuids() != null) {
            target.setCompletedRequests(mapCompletedRequests(source.getCompletedRequestsUuids()));
        }
        target.setExpVariantKeys(StringUtils.isNotBlank(staticDetailRequest.getExpVariantKeys()) ? staticDetailRequest.getExpVariantKeys() : null);


        // Map myPartnerMarkupConfig (stub, as MarkUpConfig is not available)
        MyPartnerMetaResponse myPartnerMeta = source.getMyPartnerMetaResponse();

        if (myPartnerMeta != null) {
            if (Objects.nonNull(myPartnerConfigConsul)) {
                target.setMyPartnerMarkupConfig(myPartnerResponseHelper.buildMarkUpConfig(myPartnerMeta.getMarkUpDetails(), myPartnerConfigConsul.getMarkUpConfig()));
            }

            target.setMyPartnerHero(myPartnerResponseHelper.mapLoyaltyResponseToMyPartnerLoyaltyResponse(myPartnerMeta.getLoyaltyResponse()));
        }

        if (null != source.getChatBotPersuasion()) {
            target.setChatBotPersuasions(chatBotPersuasionHelper.mapChatBotPersuasions(source.getChatBotPersuasion()));
        }

        if (Objects.nonNull(source.getHotelMetaData()) &&
                CollectionUtils.isNotEmpty(source.getHotelMetaData().getBhfPersuasions()) &&
                CollectionUtils.isNotEmpty(source.getHotelMetaData().getPropertyDetails().getCategories()) &&
                (source.getHotelMetaData().getPropertyDetails().getCategories().contains(HIGH_BHF) ||
                        source.getHotelMetaData().getPropertyDetails().getCategories().contains(LOW_BHF))) {

            target.setBhfPersuasions(utility.buildBhfPersuasions(source.getHotelMetaData().getBhfPersuasions(), source.getHotelMetaData().getPropertyDetails().getCategories()));
        }

        return target;
    }

    public abstract JsonNode buildWeaverResponse(JsonNode weaverResponse);

    public ListPersonalizationResponse buildListPersonalizationResponse(PersonalizationCards personalizationCards) {
        if (personalizationCards == null || CollectionUtils.isEmpty(personalizationCards.getCardData())) {
            return null;
        }

        ListPersonalizationResponse listPersonalizationResponseCG =
                new ListPersonalizationResponse();

        // === TOP-LEVEL MAPPINGS - Based on ExperienceCardBuilder fields ===
        listPersonalizationResponseCG.setExperimentId(personalizationCards.getExperimentId());
        listPersonalizationResponseCG.setTrackText(personalizationCards.getTrackText());

        // === CARD DATA MAPPING ===
        if (personalizationCards.getCardData() != null && !personalizationCards.getCardData().isEmpty()) {
            List<com.mmt.hotels.clientgateway.response.moblanding.CardData> cardDataList = new ArrayList<>();
            int sequence = 1; // Start sequence from 1

            for (CardData sourceCardData : personalizationCards.getCardData()) {
                com.mmt.hotels.clientgateway.response.moblanding.CardData targetCardData = mapCardDataFromOrchestrator(sourceCardData, sequence);
                if (targetCardData != null) {
                    cardDataList.add(targetCardData);
                    sequence++;
                }
            }
            listPersonalizationResponseCG.setCardData(cardDataList);
        } else {
            // Set card data to null when input is empty (as expected by tests)
            listPersonalizationResponseCG.setCardData(null);
        }

        // === META MAPPING ===
        Meta meta = new Meta();
        SavedCardTracking savedCardTracking =
                new SavedCardTracking();

        // TODO: These fields need external data source (user context, saved cards service, etc.)
        // savedCardTracking.setUserSavedCardCount(getUserSavedCardCount());
        // savedCardTracking.setCardInfo(getCardInfo());
        // savedCardTracking.setOfferApplicable(getOfferApplicable());

        meta.setSavedCardTracking(savedCardTracking);
        listPersonalizationResponseCG.setMeta(meta);

        return listPersonalizationResponseCG;
    }

    private Set<String> mapCompletedRequests(Set<String> completedRequests) {
        Set<String> result = new HashSet<>();
        for (String req : completedRequests) {
            if (req.contains(placeApiMap.getKey())) {
                result.add(placeApiMap.getValue());
                break;
            }
        }
        result.addAll(completedRequests);
        return result;
    }


    /**
     * Maps CardData from orchestrator schema to client gateway schema
     * All 12 distinct fields from ExperienceCardBuilder are mapped here
     */
    private com.mmt.hotels.clientgateway.response.moblanding.CardData mapCardDataFromOrchestrator(CardData sourceCardData, int sequence) {
        if (sourceCardData == null) {
            return null;
        }

        com.mmt.hotels.clientgateway.response.moblanding.CardData targetCardData = new com.mmt.hotels.clientgateway.response.moblanding.CardData();

        // Set sequence based on position in list
        targetCardData.setSequence(sequence);

        // Map cardInfo using proper schema classes
        targetCardData.setCardInfo(mapCardInfoFromOrchestrator(sourceCardData));

        return targetCardData;
    }

    /**
     * Maps CardInfo from orchestrator CardData to client gateway CardInfo
     * Maps all 12 distinct fields identified from ExperienceCardBuilder
     */
    private CardInfo mapCardInfoFromOrchestrator(CardData sourceCardData) {
        if (sourceCardData == null) {
            return null;
        }

        CardInfo cardInfo = new CardInfo();

        // === CORE FIELDS (1-4) from ExperienceCardBuilder ===
        cardInfo.setId(sourceCardData.getCardId());
        cardInfo.setSubType(sourceCardData.getCardSubType());
        cardInfo.setTitleText(sourceCardData.getTitleText());                    // Field 1: titleText
        cardInfo.setTemplateId(sourceCardData.getTemplateId());                  // Field 2: templateId
        cardInfo.setTemplateType(sourceCardData.getTemplateType());
        cardInfo.setPageContext(sourceCardData.getPageContext());
        cardInfo.setIconURL(sourceCardData.getIconUrl());                        // Field 3: iconUrl
        // Field 4: removeCard - stored in extraData since CardInfo doesn't have this field

        // === COMPLEX NESTED FIELDS (5-12) ===
        // Field 5-8: cardPayload and its nested genericCardData
        if (sourceCardData.getCardPayload() != null) {
            cardInfo.setCardPayload(mapCardPayloadData(sourceCardData.getCardPayload()));
        }

        // Field 9-10: data (nested list structure) - handled within cardPayload mapping
        // Field 11-12: cardAction.title and other cardAction fields
        if (sourceCardData.getCardAction() != null && !sourceCardData.getCardAction().isEmpty()) {
            cardInfo.setCardAction(mapCardActionList(sourceCardData.getCardAction()));
        }

        // === ADDITIONAL ORCHESTRATOR FIELDS ===
        // Map fields that exist in both schemas
        cardInfo.setHasAction(sourceCardData.isHasAction());
        cardInfo.setClaimed(sourceCardData.isHasClaimed());
        cardInfo.setSubText(sourceCardData.getSubText());
        cardInfo.setActionText(sourceCardData.getActionText());
        cardInfo.setHeading(sourceCardData.getHeading());
        cardInfo.setDescription(sourceCardData.getDesc());                      // desc -> description
        cardInfo.setBgImageURL(sourceCardData.getBgImageUrl());                 // bgImageUrl -> bgImageURL
        cardInfo.setBgColor(sourceCardData.getBgColor());
        cardInfo.setTextColor(sourceCardData.getTextColor());
        cardInfo.setBgGradient(sourceCardData.getBgGradient());
        cardInfo.setBorderColor(sourceCardData.getBorderColor());
        cardInfo.setMinItemsToShow(sourceCardData.getMinItemsToShow() > 0 ? sourceCardData.getMinItemsToShow() : null);
        cardInfo.setDealType(sourceCardData.getDealType());
        cardInfo.setHeaderUrl(sourceCardData.getHeaderUrl());
        cardInfo.setCouponCode(sourceCardData.getCouponCode());
        cardInfo.setTitleTextColor(sourceCardData.getTitleTextColor());
        cardInfo.setImageList(sourceCardData.getImageList());

        // Store fields not available in target schema in extraData
        if (cardInfo.getExtraData() == null) {
            cardInfo.setExtraData(new HashMap<>());
        }
        cardInfo.getExtraData().put("removeCard", String.valueOf(sourceCardData.isRemoveCard()));
        cardInfo.getExtraData().put("hasToggle", String.valueOf(sourceCardData.isHasToggle()));
        cardInfo.getExtraData().put("hasLocation", String.valueOf(sourceCardData.isHasLocation()));
        cardInfo.getExtraData().put("cardPosition", String.valueOf(sourceCardData.getCardPosition()));

        // === COMPLEX OBJECT MAPPINGS ===
        if (sourceCardData.getBgLinearGradient() != null) {
            cardInfo.setBgLinearGradient(mapBGLinearGradient(sourceCardData.getBgLinearGradient()));
        }

        if (sourceCardData.getBorderGradient() != null) {
            cardInfo.setBorderGradient(mapBorderGradient(sourceCardData.getBorderGradient()));
        }

        if (sourceCardData.getIconTag() != null) {
            cardInfo.setIconTags(mapIconTag(sourceCardData.getIconTag()));
        }

        if (sourceCardData.getToggleAction() != null) {
            cardInfo.setToggleAction(mapToggleAction(sourceCardData.getToggleAction()));
        }

        if (sourceCardData.getCardSheet() != null) {
            cardInfo.setCardSheet(mapCardSheet(sourceCardData.getCardSheet()));
        }

        if (sourceCardData.getFloatingSheetData() != null) {
            cardInfo.setFloatingSheetData(mapFloatingSheetData(sourceCardData.getFloatingSheetData()));
        }

        if (sourceCardData.getRushDealTimerInfo() != null) {
            cardInfo.setRushDealTimerInfo(mapRushDealTimerInfo(sourceCardData.getRushDealTimerInfo()));
        }

        if (sourceCardData.getCardCondition() != null) {
            cardInfo.setCardCondition(mapCardCondition(sourceCardData.getCardCondition()));
        }

        return cardInfo;
    }


    // ========== HELPER MAPPING METHODS ==========

    /**
     * Maps BGLinearGradient from orchestrator to client gateway
     */
    private BGLinearGradient mapBGLinearGradient(
            CardData.BGLinearGradient source) {
        if (source == null) return null;

        BGLinearGradient target =
                new BGLinearGradient();

        // Map color fields
        target.setStart(source.getStartColor());
        target.setEnd(source.getEndColor());
        target.setDirection(source.getDirection());

        // Handle angle - convert direction to angle if needed
        if (source.getDirection() != null) {
            target.setAngle(source.getDirection());
        }

        return target;
    }

    /**
     * Maps BorderGradient from orchestrator to client gateway
     */
    private BorderGradient mapBorderGradient(
            CardData.BorderGradient source) {
        if (source == null) return null;

        BorderGradient target =
                new BorderGradient();

        // Map color fields
        target.setStart(source.getStartColor());
        target.setEnd(source.getEndColor());
        target.setDirection(source.getDirection());

        // Map colors list if available
        if (source.getColors() != null) {
            target.setColor(source.getColors());
        }

        return target;
    }

    /**
     * Maps IconTag from orchestrator to client gateway
     */
    private IconTag mapIconTag(
            CardData.IconTag source) {
        if (source == null) return null;

        IconTag target =
                new IconTag();

        // Map text field
        target.setText(source.getText());

        // Map border color
        target.setBorderColor(source.getTextColor());

        // Create BgGradient if background color is available
        if (source.getBackgroundColor() != null) {
            BgGradient bgGradient =
                    new BgGradient();
            bgGradient.setStart(source.getBackgroundColor());
            bgGradient.setEnd(source.getBackgroundColor()); // Use same color for solid background
            target.setBgGradient(bgGradient);
        }

        return target;
    }

    /**
     * Maps Filters from orchestrator to client gateway
     */
    private Filters mapFilters(
            CardData.Filters source) {
        if (source == null) return null;

        Filters target =
                new Filters();

        // Note: Filter mapping between orchestrator and client gateway schemas
        // would require complex type conversion. Skipping for now as this is
        // an optional field and may not be required for current use case.

        return target;
    }

    /**
     * Maps CardActionData from orchestrator to client gateway
     */
    private CardActionData mapCardActionData(
            CardData.CardActionData source) {
        if (source == null) return null;

        CardActionData target =
                new CardActionData();

        target.setTitle(source.getTitle());

        // Map sections if available
        if (source.getSections() != null && !source.getSections().isEmpty()) {
            List<Section> sections = new ArrayList<>();
            for (CardData.CardActionData.Section srcSection : source.getSections()) {
                Section targetSection =
                        new Section();
                targetSection.setTitle(srcSection.getTitle());

                if (srcSection.getItems() != null && !srcSection.getItems().isEmpty()) {
                    List<Item> items = new ArrayList<>();
                    for (CardData.CardActionData.Section.Item srcItem : srcSection.getItems()) {
                        Item targetItem =
                                new Item();
                        targetItem.setText(srcItem.getText());
                        targetItem.setTextBoxTitle(srcItem.getTextBoxTitle());
                        // Note: iconUrl, value, and attributes are not available in target Item schema
                        items.add(targetItem);
                    }
                    targetSection.setItems(items);
                }
                sections.add(targetSection);
            }
            target.setSections(sections);
        }

        return target;
    }

    /**
     * Maps ToggleAction from orchestrator to client gateway
     */
    private ToggleAction mapToggleAction(
            CardData.ToggleAction source) {
        if (source == null) return null;

        // Map enum values
        switch (source) {
            case ON:
                return ToggleAction.ON;
            case OFF:
                return ToggleAction.OFF;
            default:
                return null;
        }
    }

    /**
     * Maps CardAction list from orchestrator to client gateway
     */
    private List<CardAction> mapCardActionList(
            List<CardData.CardAction> sourceList) {
        if (sourceList == null || sourceList.isEmpty()) return null;

        List<CardAction> targetList = new ArrayList<>();
        for (CardData.CardAction source : sourceList) {
            CardAction target = mapCardAction(source);
            if (target != null) {
                targetList.add(target);
            }
        }
        return targetList.isEmpty() ? null : targetList;
    }

    /**
     * Maps single CardAction from orchestrator to client gateway
     */
    private CardAction mapCardAction(
            CardData.CardAction source) {
        if (source == null) return null;

        CardAction target =
                new CardAction();

        // Map basic fields
        target.setTitle(source.getTitle());
        target.setWebViewUrl(source.getWebViewUrl());
        target.setDeeplinkUrl(source.getDeeplinkUrl());
        target.setIsLogin(source.getIsLogin());
        target.setCategories(source.getCategories());

        // Map filters if available
        if (source.getFilters() != null) {
            target.setFilters(mapFilters(source.getFilters()));
        }

        // Map action if available
        if (source.getAction() != null) {
            CardAction.MoreInfoAction moreInfoAction =
                    new CardAction.MoreInfoAction();
            moreInfoAction.setTitle(source.getAction().getTitle());
            moreInfoAction.setActionProp(source.getAction().getActionProp());
            target.setAction(moreInfoAction);
        }

        // Map data if available
        if (source.getData() != null) {
            target.setData(mapCardActionData(source.getData()));
        }

        return target;
    }

    /**
     * Maps CardPayloadResponse from orchestrator to client gateway CardPayloadData
     */
    private CardPayloadData mapCardPayloadData(
            CardData.CardPayloadResponse source) {
        if (source == null) return null;

        CardPayloadData target =
                new CardPayloadData();

        // Map genericCardData if available
        if (source.getGenericCardData() != null && !source.getGenericCardData().isEmpty()) {
            List<GenericCardPayloadDataCG> genericCardDataList = new ArrayList<>();
            for (CardData.GenericCardPayloadData srcData : source.getGenericCardData()) {
                GenericCardPayloadDataCG targetData =
                        new GenericCardPayloadDataCG();

                targetData.setId(srcData.getId());
                targetData.setText(srcData.getText());
                targetData.setTag(srcData.getTag());
                targetData.setTitleText(srcData.getTitleText());
                targetData.setSubText(srcData.getSubText());
                targetData.setImageUrl(srcData.getImageUrl());
                targetData.setActionUrl(srcData.getActionUrl());
                targetData.setIconUrl(srcData.getIconUrl());
                targetData.setGalleryView(srcData.isGalleryView() ? srcData.isGalleryView() : null);
                targetData.setItemIconType(srcData.getItemIconType());
                // Note: type, description, and metadata are not available in target schema

                // Handle nested data recursively if available
                if (srcData.getData() != null && !srcData.getData().isEmpty()) {
                    List<GenericCardPayloadDataCG> nestedDataList = new ArrayList<>();
                    for (CardData.GenericCardPayloadData nestedSrc : srcData.getData()) {
                        GenericCardPayloadDataCG nestedTarget =
                                new GenericCardPayloadDataCG();
                        nestedTarget.setId(nestedSrc.getId());
                        nestedTarget.setText(nestedSrc.getText());
                        nestedTarget.setTag(nestedSrc.getTag());
                        nestedTarget.setTitleText(nestedSrc.getTitleText());
                        nestedTarget.setSubText(nestedSrc.getSubText());
                        nestedTarget.setImageUrl(nestedSrc.getImageUrl());
                        nestedTarget.setActionUrl(nestedSrc.getActionUrl());
                        nestedTarget.setIconUrl(nestedSrc.getIconUrl());
                        nestedTarget.setItemIconType(nestedSrc.getItemIconType());
                        // Note: type, description, and metadata fields not available in target schema
                        nestedDataList.add(nestedTarget);
                    }
                    targetData.setData(nestedDataList);
                }

                genericCardDataList.add(targetData);
            }
            target.setGenericCardData(genericCardDataList);
        }

        return target;
    }

    /**
     * Maps CardSheet from orchestrator to client gateway
     */
    private CardSheet mapCardSheet(
            CardData.CardSheet source) {
        if (source == null) return null;

        CardSheet target =
                new CardSheet();

        // Map top sheet from title and content
        if (source.getTitle() != null || source.getContent() != null) {
            CardSheetElem topSheet =
                    new CardSheetElem();
            topSheet.setText(source.getTitle());
            topSheet.setSubText(source.getContent());
            target.setTopSheet(topSheet);
        }

        // Map bottom sheet from items if available
        if (source.getItems() != null && !source.getItems().isEmpty()) {
            CardSheetElem bottomSheet =
                    new CardSheetElem();

            // Convert items to infoList
            List<GenericCardPayloadDataCG> infoList = new ArrayList<>();
            for (CardData.CardSheet.SheetItem item : source.getItems()) {
                GenericCardPayloadDataCG info =
                        new GenericCardPayloadDataCG();
                info.setTitleText(item.getTitle());
                info.setSubText(item.getDescription());
                info.setIconUrl(item.getIconUrl());
                info.setActionUrl(item.getActionUrl());
                infoList.add(info);
            }
            bottomSheet.setInfoList(infoList);
            target.setBottomSheet(bottomSheet);
        }

        return target;
    }

    /**
     * Maps FloatingSheetData from orchestrator to client gateway
     */
    public FloatingSheetData mapFloatingSheetData(
            CardData.FloatingSheetData source) {
        if (source == null) return null;

        FloatingSheetData target =
                new FloatingSheetData();

        // Map basic fields
        target.setText(source.getTitle());
        target.setCurrentTimeStamp(String.valueOf(System.currentTimeMillis()));
        target.setFlotingActionName(source.getActionText());

        // Map action URL
        if (source.getActionUrl() != null) {
            // Store action URL in flotingActionName since that's the closest field
            target.setFlotingActionName(source.getActionText() != null ? source.getActionText() : source.getActionUrl());
        }

        // Set dismissible flag - store in extraData if needed
        // Note: dismissible field not available in target schema

        return target;
    }

    /**
     * Maps RushDealTimerInfo from orchestrator to client gateway
     */
    public RushDealTimerInfo mapRushDealTimerInfo(
            CardData.RushDealTimerInfo source) {
        if (source == null) return null;

        RushDealTimerInfo target =
                new RushDealTimerInfo();

        // Map urgency text to desc
        target.setDesc(source.getUrgencyText());

        // Map end time to validity timestamp
        if (source.getEndTime() > 0) {
            target.setValidityTimestamp(String.valueOf(source.getEndTime()));
        }

        // Note: bgGradient field exists in target but no equivalent in source
        // showTimer, timerText, timerFormat fields from source not available in target

        return target;
    }

    /**
     * Maps CardCondition from orchestrator to client gateway
     */
    public CardCondition mapCardCondition(
            CardData.CardCondition source) {
        if (source == null) return null;

        CardCondition target =
                new CardCondition();

        // Map checkIfFilterNotApplied if available
        if (source.getCheckIfFilterNotApplied() != null) {
            Filter filter = new Filter();

            // Convert FilterGroup enum from orchestrator to client gateway
            String filterGroupName = source.getCheckIfFilterNotApplied().getFilterGroup().name();
            FilterGroup cgFilterGroup =
                    FilterGroup.getFilterGroupFromFilterName(filterGroupName);
            if (cgFilterGroup != null) {
                filter.setFilterGroup(cgFilterGroup);
            }

            filter.setFilterValue(source.getCheckIfFilterNotApplied().getFilterValue());
            target.setCheckIfFilterNotApplied(filter);
        }

        // Note: Other fields from source (additionalConditions) don't have direct mappings
        // Target has many boolean fields (shouldBeLoggedInUser, etc.) not available in source

        return target;
    }

    public StaffInfo convertStaffInfo(com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffInfo staffInfo) {
        if (staffInfo == null ||
                ((staffInfo.getHost() == null || CollectionUtils.isEmpty(staffInfo.getHost().getData()))
                        && (staffInfo.getCook() == null || CollectionUtils.isEmpty(staffInfo.getCook().getData()))
                        && (staffInfo.getCaretaker() == null || CollectionUtils.isEmpty(staffInfo.getCaretaker().getData())))) {
            return null;
        }

        StaffInfo staffInfoCg = new StaffInfo();

        // Map basic fields
        staffInfoCg.setIsStarHost(staffInfo.getIsStarHost());
        staffInfoCg.setStarHostIconUrl(staffInfo.getStarHostIconUrl());
        staffInfoCg.setChatEnabled(staffInfo.getChatEnabled());
        staffInfoCg.setResponseTime(staffInfo.getResponseTime());
        staffInfoCg.setStarHostReasons(staffInfo.getStarHostReasons());

        // Map host Staff
        if (staffInfo.getHost() != null) {
            staffInfoCg.setHost(mapStaffToStaffCg(staffInfo.getHost()));
        }

        // Map caretaker Staff
        if (staffInfo.getCaretaker() != null) {
            staffInfoCg.setCaretaker(mapStaffToStaffCg(staffInfo.getCaretaker()));
        }

        // Map cook Staff
        if (staffInfo.getCook() != null) {
            staffInfoCg.setCook(mapStaffToStaffCg(staffInfo.getCook()));
        }

        return staffInfoCg;
    }

    public ReportCardPersuasion buildReportCardPersuasion(String popularText) {
        ReportCardPersuasion reportCardPersuasion = new ReportCardPersuasion();
        reportCardPersuasion.setText(popularText);
        reportCardPersuasion.setIconUrl("https://promos.makemytrip.com/appfest/2x/ic-popular2.png");
        return reportCardPersuasion;
    }

    public String buildCategoryIcon(HotelMetaData hotelMetaData, boolean isCorpIdContext, Map<String, String> expDataMap) {

        if (CollectionUtils.isNotEmpty(hotelMetaData.getPropertyDetails().getCategories())
                && hotelMetaData.getPropertyDetails().getCategories().contains(MMT_VALUE_STAYS) && !isCorpIdContext) {
            return "https://promos.makemytrip.com/Hotels_product/Value_Stays/v2/logo/ValueStays.png";
        }

        if (utility.hasHiddenGemExpEnabled(expDataMap) && CollectionUtils.isNotEmpty(hotelMetaData.getPropertyDetails().getCategories())
                && hotelMetaData.getPropertyDetails().getCategories().contains(HIDDEN_GEM)) {
            return "https://promos.makemytrip.com/appfest/2x/ic-popular2.png";
        }

        return null;

//        // if hotel is MyPartner GST Assured, set categoryIcon in response
//        if (hotelResult.isGstAssured()) {
//            finalResponse.getHotelResult().setCategoryIcon(myPartnerGstAssuredCategoryIcon);
//        }
    }


    /**
     * Maps Staff from orchestrator to client gateway Staff
     */
    private Staff mapStaffToStaffCg(
            com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.Staff staff) {
        if (staff == null) return null;

        Staff staffCg = new Staff();

        // Map header
        staffCg.setHeader(staff.getHeader());

        // Map staff data list - handle null or empty data gracefully
        List<StaffData> staffDataList = new ArrayList<>();
        if (staff.getData() != null && !staff.getData().isEmpty()) {
            for (com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffData staffData : staff.getData()) {
                StaffData mappedData = mapStaffDataToStaffDataCg(staffData);
                if (mappedData != null) {
                    staffDataList.add(mappedData);
                }
            }
        }
        staffCg.setData(staffDataList);

        return staffCg;
    }

    /**
     * Maps StaffData from orchestrator to client gateway StaffData
     */
    private StaffData mapStaffDataToStaffDataCg(
            com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffData staffData) {
        if (staffData == null) return null;

        StaffData staffDataCg = new StaffData();

        // Map all fields
        staffDataCg.setType(staffData.getType());
        staffDataCg.setHeading(staffData.getHeading());
        staffDataCg.setName(staffData.getName());
        staffDataCg.setGender(staffData.getGender());
        staffDataCg.setAge(staffData.getAge());
        staffDataCg.setProfilePicUrl(staffData.getProfilePicUrl());
        staffDataCg.setAbout(staffData.getAbout());

        // Map general info (SpecialisedIn list)
        if (staffData.getGeneralInfo() != null && !staffData.getGeneralInfo().isEmpty()) {
            List<SpecialisedIn> specialisedInList = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.SpecialisedIn specialisedIn : staffData.getGeneralInfo()) {
                specialisedInList.add(mapSpecialisedInToSpecialisedInCg(specialisedIn));
            }
            staffDataCg.setGeneralInfo(specialisedInList);
        }

        // Map availability
        if (staffData.getAvailability() != null) {
            staffDataCg.setAvailability(mapAvailabilityToAvailabilityCg(staffData.getAvailability()));
        }

        // Map responsibilities
        if (staffData.getResponsibilities() != null) {
            staffDataCg.setResponsibilities(mapResponsibilitiesToResponsibilitiesCg(staffData.getResponsibilities()));
        }

        return staffDataCg;
    }

    /**
     * Maps SpecialisedIn from orchestrator to client gateway SpecialisedIn
     */
    public SpecialisedIn mapSpecialisedInToSpecialisedInCg(
            com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.SpecialisedIn specialisedIn) {
        if (specialisedIn == null) return null;

        SpecialisedIn specialisedInCg =
                new SpecialisedIn();

        // Map text and iconUrl
        specialisedInCg.setText(specialisedIn.getText());
        specialisedInCg.setIconUrl(specialisedIn.getIconUrl());

        return specialisedInCg;
    }

    /**
     * Maps Availability from orchestrator to client gateway Availability
     */
    public Availability mapAvailabilityToAvailabilityCg(
            com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.Availability availability) {
        if (availability == null) return null;

        Availability availabilityCg =
                new Availability();

        // Map text and subText fields
        availabilityCg.setText(availability.getText());
        availabilityCg.setSubText(availability.getSubText());

        return availabilityCg;
    }

    /**
     * Maps Responsibilities from orchestrator to client gateway Responsibilities
     */
    public Responsibilities mapResponsibilitiesToResponsibilitiesCg(
            com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.Responsibilities responsibilities) {
        if (responsibilities == null) return null;

        Responsibilities responsibilitiesCg =
                new Responsibilities();

        // Map text
        responsibilitiesCg.setText(responsibilities.getText());

        // Map specialisedIn list
        if (responsibilities.getSpecialisedIn() != null && !responsibilities.getSpecialisedIn().isEmpty()) {
            List<SpecialisedIn> specialisedInList = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.SpecialisedIn specialisedIn : responsibilities.getSpecialisedIn()) {
                specialisedInList.add(mapSpecialisedInToSpecialisedInCg(specialisedIn));
            }
            responsibilitiesCg.setSpecialisedIn(specialisedInList);
        }

        return responsibilitiesCg;
    }

    public com.mmt.hotels.pojo.response.detail.PlacesResponse modifyPlacesResponse(PlacesResponse placesResponse, Map<String, String> expDataMap) {
        if (placesResponse != null && CollectionUtils.isNotEmpty(placesResponse.getCategories())) {
            for (Category category : placesResponse.getCategories()) {
                if (category != null && CollectionUtils.isNotEmpty(category.getCategoryData())) {
                    for (CategoryDatum categoryDatum : category.getCategoryData()) {
                        if (StringUtils.isNotEmpty(categoryDatum.getCategory())) {
                            categoryDatum.setTagLine(categoryDatum.getCategory());
                        }
                    }
                }
            }
        }
        com.mmt.hotels.pojo.response.detail.PlacesResponse placesResponseCg = new com.mmt.hotels.pojo.response.detail.PlacesResponse();
        if (placesResponse != null) {
            placesResponseCg.setCardType(placesResponse.getCardType());
            placesResponseCg.setCategories(mapCategories(placesResponse.getCategories()));
            placesResponseCg.setDirectionsToReach(mapDirectionDetails(placesResponse.getDirectionsToReach()));

            if (utility.isExperimentTrue(expDataMap, LOCATION_SECTION_RATING.getKey()))
                placesResponseCg.setLocRatingData(buildRatingData(placesResponse.getRatingData()));
            return placesResponseCg;
        }
        return null;
    }

    private List<com.mmt.hotels.pojo.response.detail.placesapi.Category> mapCategories(List<Category> sourceList) {
        if (sourceList == null) return null;
        List<com.mmt.hotels.pojo.response.detail.placesapi.Category> targetList = new ArrayList<>();
        for (Category src : sourceList) {
            com.mmt.hotels.pojo.response.detail.placesapi.Category tgt = new com.mmt.hotels.pojo.response.detail.placesapi.Category();
            tgt.setPriority(src.getPriority());
            tgt.setIconUrl(src.getIconUrl());
            tgt.setCategoryType(src.getCategoryType());
            targetList.add(tgt);
            tgt.setCategoryData(mapCategoryData(src.getCategoryData()));
        }
        return targetList;
    }

    private List<com.mmt.hotels.pojo.response.detail.placesapi.CategoryDatum> mapCategoryData(List<CategoryDatum> sourceList) {
        if (sourceList == null) return null;
        List<com.mmt.hotels.pojo.response.detail.placesapi.CategoryDatum> targetList = new ArrayList<>();
        for (CategoryDatum src : sourceList) {
            com.mmt.hotels.pojo.response.detail.placesapi.CategoryDatum tgt = new com.mmt.hotels.pojo.response.detail.placesapi.CategoryDatum();
            tgt.setPlaceId(src.getPlaceId());
            tgt.setPlaceName(src.getPlaceName());
            tgt.setLocation(mapLocation(src.getLocation()));
            tgt.setCategory(src.getCategory());
            tgt.setDistance(src.getDistance());
            tgt.setDistanceUnit(src.getDistanceUnit());
            tgt.setSeoUrl(src.getSeoUrl());
            tgt.setVoyId(src.getVoyId());
            tgt.setCtaUrl(src.getCtaUrl());
            tgt.setAddress(src.getAddress());
            tgt.setTagLine(src.getTagLine());
            tgt.setPerformanceTags(src.getPerformanceTags());
            tgt.setImageList(mapPoiImageList(src.getImageList()));
            targetList.add(tgt);
        }
        return targetList;
    }
    private List<com.mmt.hotels.clientgateway.response.CategoryDatum> mapCategoryDataCG(List<CategoryDatum> sourceList) {
        if (CollectionUtils.isEmpty(sourceList))
            return null;
        List<com.mmt.hotels.clientgateway.response.CategoryDatum> targetList = new ArrayList<>();
        for (CategoryDatum src : sourceList) {
            com.mmt.hotels.clientgateway.response.CategoryDatum tgt = new com.mmt.hotels.clientgateway.response.CategoryDatum();
            tgt.setPlaceId(src.getPlaceId());
            tgt.setPlaceName(src.getPlaceName());
            tgt.setLocation(mapLocationOrch(src.getLocation()));
            tgt.setCategory(src.getCategory());
            tgt.setDistance(src.getDistance());
            tgt.setDistanceUnit(src.getDistanceUnit());
            tgt.setSeoUrl(src.getSeoUrl());
            tgt.setVoyId(src.getVoyId());
            tgt.setCtaUrl(src.getCtaUrl());
            tgt.setAddress(src.getAddress());
            tgt.setTagLine(src.getTagLine());
            tgt.setPerformanceTags(src.getPerformanceTags());
            tgt.setImageList(mapPoiImageListOrch(src.getImageList()));
            targetList.add(tgt);
        }
        return targetList;
    }

    // Helper to map Location
    private Location mapLocation(com.gommt.hotels.orchestrator.detail.model.response.content.places.Location src) {
        if (src == null) return null;
        Location tgt = new Location();
        tgt.setLat(src.getLat());
        tgt.setLon(src.getLon());
        tgt.setType(src.getType());
        return tgt;
    }

    private Location mapLocationOrch(com.gommt.hotels.orchestrator.detail.model.response.content.places.Location src) {
        if (src == null) return null;
        Location tgt = new Location();
        tgt.setLat(src.getLat());
        tgt.setLon(src.getLon());
//        tgt.setType(src.getType());
        return tgt;
    }

    // Helper to map PoiImage list
    public List<PoiImage> mapPoiImageList(List<com.gommt.hotels.orchestrator.detail.model.response.content.places.PoiImage> srcList) {
        if (CollectionUtils.isEmpty(srcList)) return Collections.emptyList();
        List<PoiImage> tgtList = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.content.places.PoiImage src : srcList) {
            PoiImage tgt = new PoiImage();
            tgt.setUrl(src.getUrl());
            tgt.setThumbnail(src.isThumbnail());
            if (src.getAuthor() != null) {
                ImageAuthor imageAuthor = new ImageAuthor();
                imageAuthor.setName(src.getAuthor().getName());
                imageAuthor.setLink(src.getAuthor().getLink());
                tgt.setAuthor(imageAuthor);
            }
            tgtList.add(tgt);
        }
        return tgtList;
    }

    public List<PoiImage> mapPoiImageListOrch(List<com.gommt.hotels.orchestrator.detail.model.response.content.places.PoiImage> srcList) {
        if (CollectionUtils.isEmpty(srcList)) return null;
        List<PoiImage> tgtList = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.content.places.PoiImage src : srcList) {
            PoiImage tgt = new PoiImage();
            tgt.setUrl(src.getUrl());
            tgt.setThumbnail(src.isThumbnail());
            if (src.getAuthor() != null) {
                ImageAuthor imageAuthor = new ImageAuthor();
                imageAuthor.setName(src.getAuthor().getName());
                imageAuthor.setLink(src.getAuthor().getLink());
//                tgt.setAuthor(imageAuthor);
            }
            tgtList.add(tgt);
        }
        return tgtList;
    }

    private DirectionDetails mapDirectionDetails(com.gommt.hotels.orchestrator.detail.model.response.content.places.DirectionDetails src) {
        if (src == null) return null;
        DirectionDetails tgt = new DirectionDetails();
        tgt.setAddressDetail(src.getAddressDetail());
        tgt.setCtaText(src.getCtaText());
        return tgt;
    }

    private TreelGalleryData mapTreelsImagesToTreelGalleryData(List<TreelsMediaEntity> treelsImages) {
        if (treelsImages == null) return null;
        TreelGalleryData galleryData = new TreelGalleryData();
        List<TreelMedia> treelMediaList = new ArrayList<>();
        for (TreelsMediaEntity entity : treelsImages) {
            TreelMedia media = new TreelMedia();
            media.setDescription(entity.getDescription());
            media.setBrandIcon(entity.getBrandIcon());
            media.setMediaType(entity.getMediaType());
            media.setTitle(entity.getTitle());
            media.setSubtitle(entity.getSubtitle());
            media.setShareUrl(entity.getShareUrl());
            media.setThumbnailUrl(entity.getThumbnailUrl());
            media.setUrl(entity.getUrl());
            media.setShareCount(entity.getShareCount());
            media.setMediaBrand(entity.getMediaBrand());
            media.setDisplayablePropertyLayout(entity.isDisplayablePropertyLayout());
            treelMediaList.add(media);
        }
        galleryData.setTreelMedia(treelMediaList);
        galleryData.setIconUrl(newIconUrl);
        return galleryData;
    }

    public ComparatorResponse buildHotelCompareResponseResponse(StaticDetailRequest staticDetailRequest, SearchHotelsRequest searchHotelsRequest, Map<String, com.gommt.hotels.orchestrator.detail.model.response.ComparatorResponse> hotelCompareResponseMap,
                                                                 Map<String, String> expDataMap, boolean isLiteResponse, CommonModifierResponse commonModifierResponse, String rscValue, boolean myPartnerReq) {
        if (!hotelCompareResponseMap.containsKey("RECOMMENDED_HOTELS")) {
            return null;
        }

        ComparatorResponse comparatorResponse = new ComparatorResponse();
        com.gommt.hotels.orchestrator.detail.model.response.ComparatorResponse recommendedHotels = hotelCompareResponseMap.get("RECOMMENDED_HOTELS");
        comparatorResponse.setCtaMap(buildCTAMap(staticDetailRequest, commonModifierResponse,
                recommendedHotels, expDataMap, rscValue, myPartnerReq));

        HotelDetails hotelDetails = Optional.of(recommendedHotels)
                .map(com.gommt.hotels.orchestrator.detail.model.response.ComparatorResponse::getPersonalizedSections)
                .map(PersonalizedSectionDetails::getHotels)
                .filter(hotels -> !hotels.isEmpty())
                .map(hotels -> hotels.get(0))
                .orElse(new HotelDetails());
        comparatorResponse.setDeeplink(buildDetailDeepLink(staticDetailRequest, commonModifierResponse, expDataMap,
                staticDetailRequest.getSearchCriteria().getHotelId(), hotelDetails.getId(),
                hotelDetails.isAltAcco(), hotelDetails.getHotelCategory(), hotelDetails.isMaskedPropertyName(), rscValue, myPartnerReq));
        comparatorResponse.setTitle(recommendedHotels.getSectionTitle());
        comparatorResponse.setHotelDisplayMap(new HashMap<>());

        comparatorResponse.setShowCTA(recommendedHotels.getShowCta());
        if (recommendedHotels.getShowCta() != null && !recommendedHotels.getShowCta()) {
            comparatorResponse.setRepositionIndex(repositionIndex);
            comparatorResponse.setBannerInfo(buildBannerInfo());
        }

        com.gommt.hotels.orchestrator.detail.model.response.ComparatorResponse hotelList = hotelCompareResponseMap.get("RECOMMENDED_HOTELS");
        if (hotelList != null) {
            List<Hotel> finalList = getListingHotels(staticDetailRequest, searchHotelsRequest, hotelList, commonModifierResponse, expDataMap, isLiteResponse, false);
            if (CollectionUtils.isNotEmpty(finalList)) {
                comparatorResponse.setHotelList(finalList);
            }
        }

        if (hotelCompareResponseMap.containsKey("SPONSORED_HOTELS")) {
            com.gommt.hotels.orchestrator.detail.model.response.ComparatorResponse sponsoredHotelList = hotelCompareResponseMap.get("SPONSORED_HOTELS");
            List<Hotel> finalList = getListingHotels(staticDetailRequest, searchHotelsRequest, sponsoredHotelList, commonModifierResponse, expDataMap, isLiteResponse, true);
            if (CollectionUtils.isNotEmpty(finalList)) {
                comparatorResponse.setSponsoredIconUrl(sponsoredHotelIconUrl);
                comparatorResponse.setSponsoredHotelList(finalList);
            }
        }

        return comparatorResponse;

    }

    private BannerInfo buildBannerInfo() {
        BannerInfo bannerInfo = new BannerInfo();
        bannerInfo.setText(polyglotService.getTranslatedData(ConstantsTranslation.ABO_BANNER_INFO_TEXT));
        bannerInfo.setBackground("#FFEDD1");
        return bannerInfo;
    }

    public List<Hotel> getListingHotels(StaticDetailRequest staticDetailRequest, SearchHotelsRequest searchHotelsRequest,
                                        com.gommt.hotels.orchestrator.detail.model.response.ComparatorResponse comparatorResponse,
                                        CommonModifierResponse commonModifierResponse, Map<String, String> expDataMap, boolean isLiteResponse, boolean sponsoredHotel) {
        List<HotelDetails> hotels = new ArrayList<>();
        if(comparatorResponse.getPersonalizedSections() != null && CollectionUtils.isNotEmpty(comparatorResponse.getPersonalizedSections().getHotels())) {
            hotels = comparatorResponse.getPersonalizedSections().getHotels();
        }

        List<Hotel> transformedHotels = orchSearchHotelsResponseTransformer.buildPersonalizedHotels(
                hotels,
                expDataMap,
                searchHotelsRequest,
                comparatorResponse.getSectionTitle(), // section name
                commonModifierResponse, // commonModifierResponse
                null, // locusData
                null  // markUpDetails
        );
        if (isLiteResponse && CollectionUtils.isNotEmpty(transformedHotels)) {
            return liteHotelLists(transformedHotels, sponsoredHotel, true);
        }
        return transformedHotels;
    }

    /**
     * Transforms StaticDetailRequest to SearchHotelsRequest for use in buildPersonalizedHotels method
     *
     * @param staticDetailRequest The static detail request to transform
     * @return SearchHotelsRequest with mapped fields from StaticDetailRequest
     */
    private SearchHotelsRequest transformStaticDetailRequestToSearchHotelsRequest(StaticDetailRequest staticDetailRequest) {
        if (staticDetailRequest == null || staticDetailRequest.getSearchCriteria() == null) {
            return null;
        }

        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();

        // Map basic fields
        searchHotelsRequest.setCorrelationKey(staticDetailRequest.getCorrelationKey());
        searchHotelsRequest.setExpData(staticDetailRequest.getExpData());
        searchHotelsRequest.setExpVariantKeys(staticDetailRequest.getExpVariantKeys());
        searchHotelsRequest.setExpDataMap(staticDetailRequest.getExpDataMap());

        // Map device details
        if (staticDetailRequest.getDeviceDetails() != null) {
            searchHotelsRequest.setDeviceDetails(staticDetailRequest.getDeviceDetails());
        }

        // Map request details
        if (staticDetailRequest.getRequestDetails() != null) {
            searchHotelsRequest.setRequestDetails(staticDetailRequest.getRequestDetails());
        }

        // Map feature flags
        if (staticDetailRequest.getFeatureFlags() != null) {
            searchHotelsRequest.setFeatureFlags(staticDetailRequest.getFeatureFlags());
        }

        // Transform StaticDetailCriteria to SearchHotelsCriteria
        if (staticDetailRequest.getSearchCriteria() != null) {
            StaticDetailCriteria staticCriteria = staticDetailRequest.getSearchCriteria();
            SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();

            // Map basic search fields
            searchCriteria.setCheckIn(staticCriteria.getCheckIn());
            searchCriteria.setCheckOut(staticCriteria.getCheckOut());
            searchCriteria.setCityCode(staticCriteria.getCityCode());
            searchCriteria.setCountryCode(staticCriteria.getCountryCode());
            searchCriteria.setLocationId(staticCriteria.getLocationId());
            searchCriteria.setLocationType(staticCriteria.getLocationType());
            searchCriteria.setCurrency(staticCriteria.getCurrency());
            searchCriteria.setRoomStayCandidates(staticCriteria.getRoomStayCandidates());

            // Map user global info if available
            if (staticCriteria.getUserGlobalInfo() != null) {
                searchCriteria.setUserGlobalInfo(staticCriteria.getUserGlobalInfo());
            }

            // Map multi currency info if available
            if (staticCriteria.getMultiCurrencyInfo() != null) {
                searchCriteria.setMultiCurrencyInfo(staticCriteria.getMultiCurrencyInfo());
            }

            searchHotelsRequest.setSearchCriteria(searchCriteria);
        }

        // Map filter criteria
        if (staticDetailRequest.getFilterCriteria() != null) {
            searchHotelsRequest.setFilterCriteria(staticDetailRequest.getFilterCriteria());
        }

        return searchHotelsRequest;
    }

    private Map<String, Action> buildCTAMap(StaticDetailRequest staticDetailRequest, CommonModifierResponse commonModifierResponse,
                                            com.gommt.hotels.orchestrator.detail.model.response.ComparatorResponse comparatorResponse,
                                            Map<String, String> expDataMap, String rscValue, boolean myPartnerReq) {
        Map<String, Action> ctaMap = new HashMap<>();
        String hotelId = staticDetailRequest.getSearchCriteria().getHotelId();
        if (comparatorResponse.getPersonalizedSections() != null && CollectionUtils.isNotEmpty(comparatorResponse.getPersonalizedSections().getHotels())) {

            for (HotelDetails hotel : comparatorResponse.getPersonalizedSections().getHotels()) {
                Action action = new Action();
                String tempText = hotelId.equalsIgnoreCase(hotel.getId()) ? polyglotService.getTranslatedData(ConstantsTranslation.ACTION_CONTINUE)
                        : polyglotService.getTranslatedData(ConstantsTranslation.ACTION_VIEW_DETAILS);
                action.setUrl(buildDetailDeepLink(staticDetailRequest, commonModifierResponse, expDataMap, hotelId, hotel.getId(),
                        hotel.isAltAcco(), hotel.getHotelCategory(), hotel.isMaskedPropertyName(), rscValue, myPartnerReq));
                action.setText(tempText);
                ctaMap.put(hotel.getId(), action);
            }
        }
        return ctaMap;
    }

    private String buildDetailDeepLink(StaticDetailRequest staticDetailRequest, CommonModifierResponse commonModifierResponse,
                                       Map<String, String> expDataMap, String inputHotelId, String hotelId, boolean hotelAltAcco,
                                       String hotelCategory, boolean hotelMaskedPropertyName,
                                       String rscValue, boolean myPartnerReq) {

        StaticDetailCriteria staticDetailCriteria = staticDetailRequest.getSearchCriteria();
        UserGlobalInfo userGlobalInfo = staticDetailCriteria.getUserGlobalInfo();
        String requisitionID = staticDetailRequest.getRequestDetails().getRequisitionID();
        String myBizFLowIdentifier = staticDetailRequest.getRequestDetails().getMyBizFlowIdentifier();
        String deviceType = staticDetailRequest.getDeviceDetails().getDeviceType();
        String funnel = staticDetailRequest.getRequestDetails().getFunnelSource();
        String idContext = staticDetailRequest.getRequestDetails().getIdContext();
        String region = commonModifierResponse.getRegion();
        String cityCode = staticDetailCriteria.getCityCode();
        String countryCode = staticDetailCriteria.getCountryCode();
        FeatureFlags featureFlags = staticDetailRequest.getFeatureFlags();

        // Format check-in and check-out dates; if null, use an empty string
        String checkInDate = dateUtil.getDateFormatted(staticDetailCriteria.getCheckIn(), DateUtil.YYYY_MM_DD, MMDDYYYY);
        String checkOutDate = dateUtil.getDateFormatted(staticDetailCriteria.getCheckOut(), DateUtil.YYYY_MM_DD, MMDDYYYY);

        String deepLink = basicDetailDeeplink;
        if (myPartnerDeeplinkUpdateRequired(myPartnerReq, expDataMap, deviceType)) {
            deepLink = basicDetailDeeplinkMyPartner;
        } else if (userGlobalInfo != null && GLOBAL_ENTITY.equalsIgnoreCase(userGlobalInfo.getEntityName())) {
            deepLink = basicDetailDeeplinkGlobal;
            if (REGION_SA.equalsIgnoreCase(region)) {
                deepLink = deepLink.replace(WWW_SUBDOMAIN, SA_SUBDOMAIN);
            } else if (REGION_AE.equalsIgnoreCase(region) && REGION_AE.equalsIgnoreCase(userGlobalInfo.getUserCountry())) {
                deepLink = deepLink.replace(WWW_SUBDOMAIN, AE_SUBDOMAIN);
            }
        }

        String partialUrl = MessageFormat.format(deepLink, hotelId, cityCode, countryCode,
                rscValue, checkInDate, checkOutDate, hotelAltAcco);
        if (featureFlags != null && featureFlags.isLocus()) {
            partialUrl += AND_SEPARATOR + LOCUSID_URL_PARAM + PR_SEPARATOR + staticDetailCriteria.getLocationId();
            partialUrl += AND_SEPARATOR + LOCUSTYPE_URL_PARAM + PR_SEPARATOR + staticDetailCriteria.getLocationType();
        }
        if (StringUtils.isNotEmpty(region)) {
            partialUrl = partialUrl + AND_SEPARATOR + REGION_URL_PARAM + PR_SEPARATOR + region;
        }
        if (CORP_ID_CONTEXT.equalsIgnoreCase(idContext)) {
            partialUrl = partialUrl + AND_SEPARATOR + IS_CORPORATE + PR_SEPARATOR + true;
        }
        if (FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnel)) {
            partialUrl += AND_SEPARATOR + funnel.toLowerCase() + PR_SEPARATOR + "true";
        }
        if (StringUtils.isNotEmpty(rscValue)) {
            partialUrl += getQueryParameter(RSC, rscValue);
        }
        if (inputHotelId.equalsIgnoreCase(hotelId)) {
            partialUrl += AND_SEPARATOR + OPEN_ROOM_DEEP_LINK_URL;
        }

        if (StringUtils.isNotBlank(requisitionID) && StringUtils.isNotBlank(myBizFLowIdentifier)) {
            partialUrl += getQueryParameter(REQUISITION_ID, requisitionID);
            partialUrl += getQueryParameter(MYBIZ_FLOW_IDENTIFIER, myBizFLowIdentifier);
        }

        if (StringUtils.isNotBlank(hotelCategory)) {
            partialUrl += getQueryParameter(VIEW_TYPE, hotelCategory);
        }

        //Appending priceBy request param in the URL for GROUP funnel
        partialUrl += appendPriceByInDeepLink(funnel, expDataMap);
        partialUrl += "&similarHotel=true&topHtlId=" + hotelId;
        partialUrl += getQueryParameter(MASKED_PROPERTY_NAME, String.valueOf(hotelMaskedPropertyName));
        return partialUrl;
    }

    public boolean myPartnerDeeplinkUpdateRequired(boolean myPartnerReq, Map<String, String> expData, String bookingDevice) {
        return myPartnerReq && BKG_DEVICE_PWA.equalsIgnoreCase(bookingDevice) &&
                Optional.ofNullable(expData)
                        .map(data -> data.containsKey(MYPRT) && "T".equalsIgnoreCase(data.get(MYPRT)))
                        .orElse(false);
    }


    public String appendPriceByInDeepLink(String funnelSource, Map<String, String> expDataMap) {
        if (expDataMap.isEmpty() || funnelSource.isEmpty()) {
            return StringUtils.EMPTY;
        }

        String priceByQueryParam = "";
        if (FUNNEL_SOURCE_GROUP_BOOKING.equalsIgnoreCase(funnelSource) && StringUtils.isNotEmpty(expDataMap.get(PRICE_EXP))) {
            priceByQueryParam = AND_SEPARATOR + DEEPLINK_PRICE_BY_TEXT.toLowerCase() + PR_SEPARATOR + expDataMap.get(PRICE_EXP);
        }
        return priceByQueryParam;
    }

    public String getQueryParameter(String queryParam, String value) {
        return AND_SEPARATOR + queryParam + PR_SEPARATOR + value;
    }


    private ComparatorResponse buildChainComparatorResponse(StaticDetailRequest staticDetailRequest, SearchHotelsRequest searchHotelsRequest, com.gommt.hotels.orchestrator.detail.model.response.ComparatorResponse hotelCompareResponse,
                                                            Map<String, String> expDataMap,
                                                            boolean isLiteResponse, CommonModifierResponse commonModifierResponse) {

        ComparatorResponse comparatorResponse = new ComparatorResponse();
        if (hotelCompareResponse == null) {
            return comparatorResponse;
        }
        // Map basic fields if they exist
        // Since we don't know the exact structure of the orchestrator's ComparatorResponse,
        // we'll use reflection or handle this more defensively

        // TODO: Ask Lepsy :: How to corporate for both sponsored and recommended hotels
        Map<String, Action> ctaMapCg = new HashMap<>();
//        if(MapUtils.isNotEmpty(hotelList.getCtaItems())) {
//            for(Map.Entry<String, DisplayItem> entry : hotelList.getCtaItems().entrySet()) {
//                Action action = new Action();
//                action.setText(entry.getValue().getText());
//                action.setUrl(entry.getValue().getIconUrl());
//                ctaMapCg.put(entry.getKey(), action);
//            }
//        }

        // TODO: Ask Lepsy How to corporate for both sponsored and recommended hotels
        //comparatorResponse.setComparisonHeadings();
        comparatorResponse.setCtaMap(ctaMapCg);
        //comparatorResponse.setDeeplink(hotelCompareResponse.getDeeplink());
        //comparatorResponse.setHotelDisplayMap(hotelCompareResponse.);
        //comparatorResponse.setResponseErrors(hotelCompareResponse.getResponseErrors());
        //comparatorResponse.setTitle(hotelList.getSectionTitle());
        //comparatorResponse.setRepositionIndex(hotelCompareResponse.getRepositionIndex());
        //comparatorResponse.setShowCTA(hotelList.getShowCta());
        //comparatorResponse.setBannerInfo(hotelCompareResponse.getBannerInfo());
        // Handle hotel list for comparatorV2

        List<Hotel> finalList = getListingHotels(staticDetailRequest, searchHotelsRequest, hotelCompareResponse,
                commonModifierResponse, expDataMap, isLiteResponse, false);
        comparatorResponse.setHotelList(finalList);
        return comparatorResponse;
    }

    public List<Hotel> liteHotelLists(List<Hotel> hotelList, boolean isSponsoredHotels, boolean isChainInfoRequired) {
        List<Hotel> finalHotelList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(hotelList)) {
            for (Hotel hotel : hotelList) {
                Hotel hotelLite = new Hotel();
                hotelLite.setId(hotel.getId());
                hotelLite.setName(hotel.getName());
                hotelLite.setStarRating(hotel.getStarRating());
                hotelLite.setStarRatingType(hotel.getStarRatingType());
                hotelLite.setHighSellingAltAcco(hotel.getHighSellingAltAcco());
                hotelLite.setSoldOut(hotel.isSoldOut());
                hotelLite.setIsAltAcco(hotel.getIsAltAcco());
                hotelLite.setPriceDetail(hotel.getPriceDetail());
                hotelLite.setLocationDetail(hotel.getLocationDetail());
                hotelLite.setDetailDeeplinkUrl(hotel.getDetailDeeplinkUrl());
                hotelLite.setLocationPersuasion(hotel.getLocationPersuasion());
                hotelLite.setRatePersuasionText(hotel.getRatePersuasionText());
                hotelLite.setHotelPersuasions(hotel.getHotelPersuasions());
                if (CollectionUtils.isNotEmpty(hotel.getMedia())) {
                    for (MediaInfo liteMediaInfo : hotel.getMedia()) {
                        if ("IMAGE".equalsIgnoreCase(liteMediaInfo.getMediaType())) {
                            hotelLite.setMedia(Collections.singletonList(liteMediaInfo));
                            break;
                        }
                    }
                }
                if (hotel.getReviewSummary() != null) {
                    ReviewSummary liteReviewSummary = new ReviewSummary();
                    liteReviewSummary.setSource(hotel.getReviewSummary().getSource());
                    liteReviewSummary.setCumulativeRating(hotel.getReviewSummary().getCumulativeRating());
                    liteReviewSummary.setTotalRatingCount(hotel.getReviewSummary().getTotalRatingCount());
                    liteReviewSummary.setRatingText(hotel.getReviewSummary().getRatingText());
                    liteReviewSummary.setPreferredOTA(hotel.getReviewSummary().isPreferredOTA());
                    hotelLite.setReviewSummary(liteReviewSummary);
                }
                if (isSponsoredHotels || isChainInfoRequired) {
                    List<String> requiredPlaceHolders = Arrays.asList("PLACEHOLDER_CARD_M4", "PLACEHOLDER_IMAGE_LEFT_TOP");
                    if (isChainInfoRequired) {
                        requiredPlaceHolders = Arrays.asList("PLACEHOLDER_CARD_M2");
                    }
                    Map<String, Object> hotelPersuasions = (Map<String, Object>) hotel.getHotelPersuasions();
                    if (hotelPersuasions != null) {
                        Map<String, Object> liteHotelPersuasions = new LinkedHashMap<>();
                        for (String key : requiredPlaceHolders) {
                            if (hotelPersuasions.containsKey(key)) {
                                liteHotelPersuasions.put(key, hotelPersuasions.get(key));
                            }
                        }
                        if (MapUtils.isNotEmpty(liteHotelPersuasions)) {
                            hotelLite.setHotelPersuasions(liteHotelPersuasions);
                        }
                    }
                }
                finalHotelList.add(hotelLite);
            }
        }
        return finalHotelList;
    }

    private boolean isCorpIdContext(StaticDetailRequest staticDetailRequest) {
        if (staticDetailRequest != null && staticDetailRequest.getRequestDetails() != null
                && StringUtils.isNotBlank(staticDetailRequest.getRequestDetails().getIdContext())) {
            return CORP_ID_CONTEXT.equalsIgnoreCase(staticDetailRequest.getRequestDetails().getIdContext());
        }
        return false;
    }

    public List<LiteResponseTravellerImage> getMediaV2TravellerMediaList(List<Tag> tags) {
        List<LiteResponseTravellerImage> travellerImageList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(tags)) {
            for (Tag tag : tags) {
                if (CollectionUtils.isNotEmpty(tag.getSubtags())) {
                    for (Subtag subtag : tag.getSubtags()) {
                        if (subtag.getData() != null) {
                            for (ImageData item : subtag.getData()) {
                                if (StringUtils.isNotEmpty(item.getMediaType()) && "IMAGE".equalsIgnoreCase(item.getMediaType())) {
                                    LiteResponseTravellerImage liteResponseTravellerImage = new LiteResponseTravellerImage();
                                    liteResponseTravellerImage.setUrl(item.getUrl());
                                    liteResponseTravellerImage.setMediaType(item.getMediaType());
                                    liteResponseTravellerImage.setTitle(item.getTitle());
                                    liteResponseTravellerImage.setFilterInfo(subtag.getName());
                                    liteResponseTravellerImage.setSuperTag(tag.getName());
                                    liteResponseTravellerImage.setAccess(subtag.getAccess());
                                    liteResponseTravellerImage.setAccessType(subtag.getAccessType());
                                    liteResponseTravellerImage.setThumbnailURL(item.getThumbnailURL());
                                    liteResponseTravellerImage.setDate(item.getDate());
                                    liteResponseTravellerImage.setTravelerName(item.getTravelerName());
                                    liteResponseTravellerImage.setDescription(item.getDescription());
                                    liteResponseTravellerImage.setText(subtag.getText());
                                    travellerImageList.add(liteResponseTravellerImage);
                                }
                            }
                        }
                    }
                }
            }
        }
        return travellerImageList;
    }


    public int getMediaV2HotelMediaListCount(List<Tag> tags) {
        int hotelImageLength = 0;
        if (CollectionUtils.isNotEmpty(tags)) {
            for (Tag tag : tags) {
                if (CollectionUtils.isNotEmpty(tag.getSubtags())) {
                    for (Subtag subtag : tag.getSubtags()) {
                        if (subtag.getData() != null) {
                            for (ImageData item : subtag.getData()) {
                                if (StringUtils.isNotEmpty(item.getMediaType()) && !item.getMediaType().equalsIgnoreCase("VIDEO")) {
                                    hotelImageLength++;
                                }
                            }
                        }
                    }
                }
            }
        }
        return hotelImageLength;
    }

    private RoomDetails mapRoomInfoToRoomDetails(RoomInfo roomInfo, Media media) {
        if (roomInfo == null) return null;
        RoomDetails details = new RoomDetails();
        details.setRoomCode(roomInfo.getRoomCode());
        details.setRoomName(roomInfo.getRoomName());
        if(StringUtils.isNotEmpty(roomInfo.getRoomSize())) {
            details.setRoomSize(roomInfo.getRoomSize() + SPACE + roomInfo.getRoomSizeUnit());
        }
        details.setParentRoomCode(roomInfo.getParentRoomCode());
        details.setBedCount(roomInfo.getBedCount());
        details.setBedroomCount(roomInfo.getBedRoomCount());
        details.setMaxAdult(roomInfo.getMaxAdultCount());
        details.setMaxGuest(roomInfo.getMaxGuestCount());
        details.setMaxChild(roomInfo.getMaxChildCount());
        details.setRoomViewName(roomInfo.getRoomViewName());
        if (roomInfo.getRoomFlags() != null) {
            details.setMaster(roomInfo.getRoomFlags().isMasterRoom());
        }

        if (MapUtils.isNotEmpty(roomInfo.getRoomArrangementMap()) &&
                roomInfo.getRoomArrangementMap().containsKey(BEDS) &&
                CollectionUtils.isNotEmpty(roomInfo.getRoomArrangementMap().get(BEDS))) {
            List<SleepingArrangement> beds = new ArrayList<>();
            List<ArrangementInfo> bedsArrangementInfos = roomInfo.getRoomArrangementMap().get(BEDS);
            for (ArrangementInfo arrangementInfo : bedsArrangementInfos) {
                SleepingArrangement sleepingArrangement = new SleepingArrangement();
                sleepingArrangement.setType(arrangementInfo.getType());
                sleepingArrangement.setCount(arrangementInfo.getCount());
                beds.add(sleepingArrangement);
            }
            details.setBeds(beds);
        }

        // Map images from professionalMediaEntities by roomCode
        List<String> images = new ArrayList<>();
        if (media != null && MapUtils.isNotEmpty(media.getProfessionalMediaEntities())) {
            for (List<ProfessionalMediaEntity> entityList : media.getProfessionalMediaEntities().values()) {
                if (CollectionUtils.isNotEmpty(entityList)) {
                    for (ProfessionalMediaEntity entity : entityList) {
                        //TODO :: Ask Lepsy
                        if (roomInfo.getRoomCode() != null && roomInfo.getRoomCode().equals(entity.getRoomCode())) {
                            String url = entity.getUrl();
                            if (url != null && !url.startsWith("http")) {
                                url = "https:" + url;
                            }
                            images.add(url);
                        }
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(images))
            details.setImages(images);
        details.setAmenities(utility.buildAmenities(roomInfo.getAmenities(), roomInfo.getRoomInfoExtension() != null ? roomInfo.getRoomInfoExtension().getStarFacilities() : new ArrayList<>(), roomInfo.getHighlightedAmenities()));
        details.setHighlightedAmenities(utility.getHighlightedAmenities(roomInfo.getHighlightedAmenities()));

        return details;
    }

    /**
     * Maps HostReviewSummary (orchestrator) to UGCHostSummaryResponse (clientgateway)
     */
    private UGCHostSummaryResponse mapHostReviewSummary(HostReviewSummary hostReviewSummary) {
        UGCHostSummaryResponse response = new UGCHostSummaryResponse();
        if (hostReviewSummary.getRatingData() != null) {
            response.setHostRatingInfo(mapRatingDataToHostRatingInfo(hostReviewSummary.getRatingData()));
        }
        if (hostReviewSummary.getHostImpressionData() != null) {
            response.setHostImpressions(mapRatingDataToHostImpressionInfo(hostReviewSummary.getHostImpressionData()));
        }
        return response;
    }

    private HostImpressions mapRatingDataToHostImpressionInfo(RatingData ratingData) {
        if (ratingData == null) return null;
        HostImpressions hostImpressions = new HostImpressions();
        hostImpressions.setTitle(ratingData.getTitle());
        hostImpressions.setSubTitle(ratingData.getSubTitle());
        hostImpressions.setDescription(ratingData.getDescription());
        hostImpressions.setIconUrl(ratingData.getIconUrl());
        hostImpressions.setTitleTagUrl(hostImpressionTitleTagUrl);
        return hostImpressions;
    }

    private HostRatingInfo mapRatingDataToHostRatingInfo(RatingData ratingData) {
        if (ratingData == null) return null;
        HostRatingInfo hostRatingInfo = new HostRatingInfo();
        hostRatingInfo.setTitle(ratingData.getTitle());
        hostRatingInfo.setDescription(ratingData.getDescription());
        hostRatingInfo.setIconUrl(ratingData.getIconUrl());
        hostRatingInfo.setRating(ratingData.getRating());
        hostRatingInfo.setText(ratingData.getText());
        hostRatingInfo.setNoRatingText(polyglotService.getTranslatedData(HOST_NO_RATING_TEXT));
        return hostRatingInfo;
    }


    /**
     * Concrete method to remove icons from staff info, similar to StaticDetailResponseTransformer
     */
    public void removeIcon(com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffInfo staffInfo) {
        if (staffInfo == null) {
            return;
        }
        removeIcon(staffInfo.getHost());
        removeIcon(staffInfo.getCook());
        removeIcon(staffInfo.getCaretaker());
    }

    private UGCRatingData buildRatingData(RatingData ratingData) {
        if (ratingData == null) {
            return null;
        }

        UGCRatingData ugcRatingData = new UGCRatingData();

        // Map title
        if (ratingData.getTitle() != null) {
            ugcRatingData.setTitle(ratingData.getTitle());
        }

        // Map subtitle
        if (ratingData.getSubTitle() != null) {
            ugcRatingData.setSubTitle(ratingData.getSubTitle());
        }

        // Map show icon flag
        ugcRatingData.setShowIcon(ratingData.isShowIcon());

        // Map summary if available
        if (ratingData.getSummary() != null) {
            RatingDetail summaryDetail = new RatingDetail();
            if (ratingData.getSummary().getText() != null) {
                summaryDetail.setText(ratingData.getSummary().getText());
            }
            // Set icon URL from rating data if available
            if (ratingData.getSummary().getIconUrl() != null) {
                summaryDetail.setIconUrl(ratingData.getSummary().getIconUrl());
            }
            ugcRatingData.setSummary(summaryDetail);
        }

        // Map highlights if available
        if (CollectionUtils.isNotEmpty(ratingData.getHighlights())) {
            List<RatingDetail> highlights = new ArrayList<>();
            for (DisplayItem highlight : ratingData.getHighlights()) {
                RatingDetail highlightDetail = new RatingDetail();
                if (highlight.getText() != null) {
                    highlightDetail.setText(highlight.getText());
                }
                if (highlight.getIconUrl() != null) {
                    highlightDetail.setIconUrl(highlight.getIconUrl());
                }
                highlights.add(highlightDetail);
            }
            ugcRatingData.setHighlights(highlights);
        }

        return ugcRatingData;
    }

    private void removeIcon(com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.Staff staff) {
        if (staff == null) {
            return;
        }
        if (staff.getData() != null) {
            for (com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffData staffData : staff.getData()) {
                if (CollectionUtils.isNotEmpty(staffData.getGeneralInfo())) {
                    staffData.getGeneralInfo().forEach(x -> x.setIconUrl(null));
                }
            }
        }
    }

    public PlacesResponseCG mapToPlacesResponseCG(PlacesResponse placesResponse, Map<String, String> expDataMap) {
        if (placesResponse != null && CollectionUtils.isNotEmpty(placesResponse.getCategories())) {
            for (Category category : placesResponse.getCategories()) {
                if (category != null && CollectionUtils.isNotEmpty(category.getCategoryData())) {
                    for (CategoryDatum categoryDatum : category.getCategoryData()) {
                        if (StringUtils.isNotEmpty(categoryDatum.getCategory())) {
                            categoryDatum.setTagLine(categoryDatum.getCategory());
                        }
                    }
                }
            }
        }
        PlacesResponseCG placesResponseCG = new PlacesResponseCG();
        if (placesResponse != null) {
            placesResponseCG.setCardType(placesResponse.getCardType());
            placesResponseCG.setCategories(mapCategoriesCG(placesResponse.getCategories()));
            placesResponseCG.setDirectionsToReach(mapDirectionDetailsCG(placesResponse.getDirectionsToReach()));

            if (utility.isExperimentTrue(expDataMap, LOCATION_SECTION_RATING.getKey()))
                placesResponseCG.setLocRatingData(buildRatingDataCG(placesResponse.getRatingData()));
            return placesResponseCG;
        }
        return null;
    }

    private List<com.mmt.hotels.clientgateway.response.Category> mapCategoriesCG(List<Category> sourceList) {
        if (CollectionUtils.isEmpty(sourceList))
            return null;
        List<com.mmt.hotels.clientgateway.response.Category> targetList = new ArrayList<>();
        for (Category src : sourceList) {
            com.mmt.hotels.clientgateway.response.Category tgt = new com.mmt.hotels.clientgateway.response.Category();
            tgt.setPriority(src.getPriority());
            tgt.setIconUrl(src.getIconUrl());
            tgt.setCategoryType(src.getCategoryType());
            targetList.add(tgt);
            tgt.setCategoryData(mapCategoryDataCG(src.getCategoryData()));
        }
        return targetList;
    }

    private com.mmt.hotels.clientgateway.response.DirectionDetails mapDirectionDetailsCG(com.gommt.hotels.orchestrator.detail.model.response.content.places.DirectionDetails source) {
        if (source == null) return null;
        com.mmt.hotels.clientgateway.response.DirectionDetails target = new com.mmt.hotels.clientgateway.response.DirectionDetails();
        target.setAddressDetail(source.getAddressDetail());
        target.setCtaText(source.getCtaText());
        return target;
    }

    private UGCRatingDataCG buildRatingDataCG(RatingData ratingData) {
        if (ratingData == null) return null;
        
        UGCRatingDataCG ugcRatingDataCG = new UGCRatingDataCG();
        
        // Map basic fields from regular buildRatingData pattern
        if (ratingData.getTitle() != null) {
            ugcRatingDataCG.setTitle(ratingData.getTitle());
        }
        if (ratingData.getSubTitle() != null) {
            ugcRatingDataCG.setSubTitle(ratingData.getSubTitle());
        }
        ugcRatingDataCG.setShowIcon(ratingData.isShowIcon());
        
        // Map summary
        if (ratingData.getSummary() != null) {
            com.mmt.hotels.clientgateway.response.RatingDetail summaryDetail = new com.mmt.hotels.clientgateway.response.RatingDetail();
            summaryDetail.setText(ratingData.getSummary().getText());
            summaryDetail.setIconUrl(ratingData.getSummary().getIconUrl());
            ugcRatingDataCG.setSummary(summaryDetail);
        }
        
        if (CollectionUtils.isNotEmpty(ratingData.getHighlights())) {
            List<com.mmt.hotels.clientgateway.response.RatingDetail> highlights = new ArrayList<>();
            for (DisplayItem highlight : ratingData.getHighlights()) {
                com.mmt.hotels.clientgateway.response.RatingDetail highlightDetail = new com.mmt.hotels.clientgateway.response.RatingDetail();
                highlightDetail.setText(highlight.getText());
                if (highlight.getIconUrl() != null) {
                    highlightDetail.setIconUrl(highlight.getIconUrl());
                }
                highlights.add(highlightDetail);
            }
            ugcRatingDataCG.setHighlights(highlights);
        }

        if (MapUtils.isNotEmpty(ratingData.getPersuasionMap())) {
            List<LocationPersuasions> persuasionList = new ArrayList<>();
            
            Map<String, HotelPersuasionData> persuasionMap =
                ratingData.getPersuasionMap();
            
            if (persuasionMap.containsKey("UGC_LOCATION")) {
                HotelPersuasionData ugcWashroomData =
                    persuasionMap.get("UGC_LOCATION");
                
                if (ugcWashroomData != null && CollectionUtils.isNotEmpty(ugcWashroomData.getData())) {
                    ugcWashroomData.getData().stream()
                        .filter(Objects::nonNull)
                        .filter(persuasionValue -> StringUtils.isNotEmpty(persuasionValue.getText()))
                        .forEach(persuasionValue -> {
                            LocationPersuasions persuasions = new LocationPersuasions();
                            persuasions.setText(persuasionValue.getText());
                            persuasions.setPriority(String.valueOf(persuasionValue.getPriority()));
                            persuasionList.add(persuasions);
                        });
                }
            }
            
            if (CollectionUtils.isNotEmpty(persuasionList)) {
                ugcRatingDataCG.setPersuasionList(persuasionList);
            }
        }
        
        return ugcRatingDataCG;
    }



} 