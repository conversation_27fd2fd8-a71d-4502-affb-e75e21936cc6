package com.mmt.hotels.clientgateway.transformer.factory;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.transformer.request.HostCallingRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.HostCallingRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.HostCallingRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.HostCallingRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.HostCallingRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.HostCallingResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.HostCallingResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.HostCallingResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.HostCallingResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.HostCallingResponseTransformerPWA;

@Component
public class HostCallingFactory {
	
	@Autowired
	private HostCallingRequestTransformerPWA hostCallingRequestTransformerPWA;
	
	@Autowired
	private HostCallingResponseTransformerPWA hostCallingResponseTransformerPWA;
	
	@Autowired
	private HostCallingRequestTransformerDesktop hostCallingRequestTransformerDesktop;
	
	@Autowired
	private HostCallingResponseTransformerDesktop hostCallingResponseTransformerDesktop;
	
	@Autowired
	private HostCallingRequestTransformerAndroid hostCallingRequestTransformerAndroid;
	
	@Autowired
	private HostCallingResponseTransformerAndroid hostCallingResponseTransformerAndroid;
	
	@Autowired
	private HostCallingRequestTransformerIOS hostCallingRequestTransformerIOS;
	
	@Autowired
	private HostCallingResponseTransformerIOS hostCallingResponseTransformerIOS;
	
	public HostCallingRequestTransformer getRequestService(String client) {
		if (StringUtils.isEmpty(client))
			return hostCallingRequestTransformerDesktop;
		switch(client) {
			case "PWA":
			case "MSITE":
				return hostCallingRequestTransformerPWA;
			case "DESKTOP": return hostCallingRequestTransformerDesktop;
			case "ANDROID": return hostCallingRequestTransformerAndroid;
			case "IOS": return hostCallingRequestTransformerIOS;
		}
		return hostCallingRequestTransformerDesktop;
	}

	public HostCallingResponseTransformer getResponseService(String client) {
		if (StringUtils.isEmpty(client))
			return hostCallingResponseTransformerDesktop;
		switch(client) {
			case "PWA":
			case "MSITE":
				return hostCallingResponseTransformerPWA;
			case "DESKTOP": return hostCallingResponseTransformerDesktop;
			case "ANDROID": return hostCallingResponseTransformerAndroid;
			case "IOS": return hostCallingResponseTransformerIOS;
		}
		return hostCallingResponseTransformerDesktop;
	}
} 