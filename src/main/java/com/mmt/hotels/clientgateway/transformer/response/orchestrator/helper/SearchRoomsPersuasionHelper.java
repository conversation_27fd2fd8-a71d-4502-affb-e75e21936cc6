package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.fasterxml.jackson.core.type.TypeReference;
import com.gommt.hotels.orchestrator.detail.enums.RoomType;
import com.gommt.hotels.orchestrator.detail.model.request.prime.BlackInfo;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.gommt.hotels.orchestrator.detail.model.response.persuasion.BenefitType;
import com.gommt.hotels.orchestrator.detail.model.response.persuasion.DealBenefits;
import com.gommt.hotels.orchestrator.detail.model.response.persuasion.PackageDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.ExtraGuestDetail;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceCouponInfo;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.Rooms;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.response.BGGradient;
import com.mmt.hotels.clientgateway.response.BookedInclusion;
import com.mmt.hotels.clientgateway.response.Hover;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.PersuasionTimer;
import com.mmt.hotels.clientgateway.response.Style;
import com.mmt.hotels.clientgateway.response.rooms.BgStyle;
import com.mmt.hotels.clientgateway.response.rooms.ExtraGuestDetailPersuasion;
import com.mmt.hotels.clientgateway.response.rooms.PackageSelectRoomRatePlan;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomRatePlan;
import com.mmt.hotels.clientgateway.response.rooms.SpecialOfferCard;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionTitle;
import com.mmt.hotels.clientgateway.util.Currency;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.persuasion.response.BgGradient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.mmt.hotels.clientgateway.constants.Constants.BLACK_BENEFITS;
import static com.mmt.hotels.clientgateway.constants.Constants.BOOKING_CONFIRMATION_TEXT;
import static com.mmt.hotels.clientgateway.constants.Constants.CASHBACK_AMOUNT;
import static com.mmt.hotels.clientgateway.constants.Constants.CASHBACK_HERO_OFFER_PERSUASION_NODE;
import static com.mmt.hotels.clientgateway.constants.Constants.CASHBACK_OFFER_PERSUASION_ICON_TYPE;
import static com.mmt.hotels.clientgateway.constants.Constants.CF8100;
import static com.mmt.hotels.clientgateway.constants.Constants.COMBO_SAVING_AMOUNT;
import static com.mmt.hotels.clientgateway.constants.Constants.COMBO_SAVING_KEY;
import static com.mmt.hotels.clientgateway.constants.Constants.D0021B;
import static com.mmt.hotels.clientgateway.constants.Constants.DEFAULT_CUR_INR;
import static com.mmt.hotels.clientgateway.constants.Constants.DELAYED_CONFIRMATION;
import static com.mmt.hotels.clientgateway.constants.Constants.FFFFFF;
import static com.mmt.hotels.clientgateway.constants.Constants.HERO_OFFER_PERSUASION_ICON_TYPE;
import static com.mmt.hotels.clientgateway.constants.Constants.HOTEL_CLOUD;
import static com.mmt.hotels.clientgateway.constants.Constants.HOTEL_CLOUD_PERSUASION_ICON;
import static com.mmt.hotels.clientgateway.constants.Constants.IMAGE_TEXT_H;
import static com.mmt.hotels.clientgateway.constants.Constants.LONG_STAY_BENEFITS;
import static com.mmt.hotels.clientgateway.constants.Constants.MMT_EXCLUSIVE_BENEFITS;
import static com.mmt.hotels.clientgateway.constants.Constants.MMT_EXCLUSIVE_TYPE;
import static com.mmt.hotels.clientgateway.constants.Constants.MULTI_ROOM_STAY_CURRENCY_SYMBOL;
import static com.mmt.hotels.clientgateway.constants.Constants.MY_PARTNER_HERO_EFFECTIVE;
import static com.mmt.hotels.clientgateway.constants.Constants.NO_OF_HOURS_PLACEHOLDER;
import static com.mmt.hotels.clientgateway.constants.Constants.ONLY_TODAY_DEAL_BENEFITS;
import static com.mmt.hotels.clientgateway.constants.Constants.OVAL_PERSUASION_TEMPLATE;
import static com.mmt.hotels.clientgateway.constants.Constants.PACKAGE_RATE;
import static com.mmt.hotels.clientgateway.constants.Constants.PRICE_TOP;
import static com.mmt.hotels.clientgateway.constants.Constants.RIGHT_BOTTOM;
import static com.mmt.hotels.clientgateway.constants.Constants.SMALL;
import static com.mmt.hotels.clientgateway.constants.Constants.SPECIAL_FARE_TAG_LARGE;
import static com.mmt.hotels.clientgateway.constants.Constants.SPECIAL_FARE_TAG_LARGE_STYLE;
import static com.mmt.hotels.clientgateway.constants.Constants.SUPER_PACKAGE_CARD_TEXT;
import static com.mmt.hotels.clientgateway.constants.Constants.TEXT_WITH_BG_IMAGE;
import static com.mmt.hotels.clientgateway.constants.Constants.TIER_NAME;
import static com.mmt.hotels.clientgateway.constants.Constants.TOP_RIGHT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.DEAL_ICON_URL;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.DELAYED_CONFIRMATION_PERSUASION_MYB_NEW_DETAILS_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.DELAYED_CONFIRMATION_PERSUASION_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.EXTRA_GUEST_DEAL_TEXT_STYLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.HIGH_DEMAND_PERSUASION;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SPECIAL_FARE_TITLE_TEXT;

/**
 * Helper class for building persuasions for OrchV2 SearchRooms response
 * Currently handles rate plan level persuasions
 * TODO: Add search rooms response level persuasions when legacy dependencies are resolved
 */
@Component
public class SearchRoomsPersuasionHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(SearchRoomsPersuasionHelper.class);

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    private Utility utility;

    @Value("#{'${mypat_exclusive_rate_segmentId.list}'.split(',')}")
    private Set<String> mypatExclusiveRateSegmentIdList;

    @Value("${corp.one.on.one.segmentId}")
    private String corpPreferredRateSegmentId;

    // Configuration properties from CommonResponseTransformer
    @Value("${onlytodaydeal.persuasion.type}")
    private String onlyTodayDealPersuasionType;

    @Value("${black.persuasion.type}")
    private String blackPersuasionType;

    @Value("${los.persuasion.type}")
    private String losPersuasionType;

    @Value("${discount.persuasion.type}")
    private String discountPersuasionType;

    @Value("${super.package.persuasion.type}")
    private String superPackagePersuasionType;

    @Value("${detail.page.persuasion.order}")
    private String detailPagePersuasionOrder;

    @Value("${staycation.getaway.persuasion.icon}")
    private String staycationGetawayPersuasionIcon;

    Map<String, List<String>> detailPagePersuasionOrderMap;

    @Value("${super.package.icon.url}")
    private String superPackageIconUrl;

    @Value("${single.tick.url}")
    private String singleTickUrl;

    @Value("${high.demand.persuasion.color}")
    private String highDemandPersuasionColor;

    @Value("${extra.guest.free.child.color}")
    private String extraGuestFreeChildColor;

    @Value("${multi.room.stay.saving.combo.icon}")
    private String multiRoomStaySavingComboIcon;

    @Value("${luxe.package.icon}")
    private String luxePackageIconUrl;

    @Value("${negotiated.rates.delayed.confirmation.no.of.hours}")
    protected int noOfHoursForConfirmation;

    private PersuasionStyle onlyTodayDealTimerStyle;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    @Autowired
    private DateUtil dateUtil;

    @Autowired
    ObjectMapperUtil objectMapperUtil;

    // Constants from legacy transformer
    private static final String NEW_SELECT_ROOM_PAGE = "NEW_SELECT_ROOM_PAGE";
    private static final String RTB_EMAIL = "RTB_EMAIL";
    private static final String PERSUASION_NON_LUXE_PACKAGE_TEXT = "PERSUASION_NON_LUXE_PACKAGE_TEXT";
    private static final String PERSUASION_LUXE_PACKAGE_TEXT = "PERSUASION_LUXE_PACKAGE_TEXT";
    private static final String RTB_PRE_APPROVED_TEXT = "RTB_PRE_APPROVED_TEXT";
    private static final String PACKAGE_RATE_TEXT = "PACKAGE_RATE_TEXT";
    private static final String SPECIAL_FARE_TITLE_TEXT_NOT_RTB = "SPECIAL_FARE_TITLE_TEXT_NOT_RTB";
    private static final String CLIENT_DESKTOP = "DESKTOP";

    @PostConstruct
    void init() {
        onlyTodayDealTimerStyle = commonConfigConsul.getOneDayDealTimerStyleConfig();
        try {
            detailPagePersuasionOrderMap = objectMapperUtil.getObjectFromJsonWithType(detailPagePersuasionOrder, new TypeReference<Map<String, List<String>>>() {
            }, DependencyLayer.CLIENTGATEWAY);
        } catch (JsonParseException e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * Build rate plan specific persuasions for OrchV2 rate plan
     * Migrated from legacy SearchRoomsResponseTransformer.getRatePlanPersuasion method
     */
    public List<PersuasionResponse> getRatePlanPersuasion(SelectRoomRatePlan ratePlan, RatePlan ratePlanOrchV2,
            String funnelSource, CommonModifierResponse commonModifierResponse, boolean isLuxeHotel, String corpAlias) {

        List<PersuasionResponse> persuasions = null;
        String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());

        PersuasionResponse exclusiveRatePersuasion = getRateExclusivePersuasion(ratePlanOrchV2, commonModifierResponse);
        if (exclusiveRatePersuasion != null) {
            persuasions = new ArrayList<>();
            persuasions.add(exclusiveRatePersuasion);
        }
        // TODO: Confirmation policy persuasion - needs getConfirmationPolicy() method in OrchV2 RatePlan

        PersuasionResponse hotelCloudPersuasions = getHotelCloudPersuasions(ratePlanOrchV2);
        if (hotelCloudPersuasions != null) {
            if (persuasions == null) {
                persuasions = new ArrayList<>();
            }
            persuasions.add(hotelCloudPersuasions);
        }

        // Staycation deal persuasion
        PersuasionResponse staycationPersuasion = getStaycationPersuasion(ratePlanOrchV2, funnelSource, persuasions);
        if (staycationPersuasion != null) {
            if (persuasions == null) {
                persuasions = new ArrayList<>();
            }
            persuasions.add(staycationPersuasion);
        }

        // Package room persuasion (Occasion/super package)
        PersuasionResponse persuasionResponse = getOccasionPackagePersuasion(ratePlanOrchV2);
        if (persuasionResponse != null) {
            if (persuasions == null) {
                persuasions = new ArrayList<>();
            }
            persuasions.add(persuasionResponse);
        } else if (ratePlanOrchV2 != null && ratePlanOrchV2.getRatePlanFlags() != null &&
                ratePlanOrchV2.getRatePlanFlags().isPackageRatePlan() && setSuperPackagePersuasion(commonModifierResponse)) {
            if (persuasions == null) {
                persuasions = new ArrayList<>();
            }
            persuasions.add(getSuperPackagePersuasion());
        }

        // Package room persuasion (non-super package)
        if (ratePlanOrchV2 != null && ratePlanOrchV2.getRatePlanFlags() != null && ratePlanOrchV2.getRatePlanFlags().isPackageRatePlan() &&
                !setSuperPackagePersuasion(commonModifierResponse)) {
            if (persuasions == null) {
                persuasions = new ArrayList<>();
            }
            PersuasionResponse persuasion = getLuxPackagePersuasion(isLuxeHotel);
            persuasions.add(persuasion);
        }

        if (ratePlanOrchV2 != null && ratePlanOrchV2.getCorpMetaData() != null && !ratePlanOrchV2.getCorpMetaData().isWithinPolicy()) {
            if (persuasions == null) {
                persuasions = new ArrayList<>();
            }
            PersuasionResponse persuasion = getCorpPolicyPersuasion();
            persuasions.add(persuasion);
        }

        // BNPL persuasion
        if (commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && utility.showBookAtZeroPersuasion(commonModifierResponse.getExpDataMap()) &&
            ratePlanOrchV2 != null && ratePlanOrchV2.getBnplDetails() != null && !StringUtils.isEmpty(ratePlanOrchV2.getBnplDetails().getBnplPersuasionMsg())) {
            if (persuasions == null)
                persuasions = new ArrayList<>();
            PersuasionResponse bnplPersuasion = buildBnplPersuasion();
            persuasions.add(bnplPersuasion);
        }

        if (CLIENT_DESKTOP.equalsIgnoreCase(client) && ratePlanOrchV2 != null) {
            boolean rtbEmail = RTB_EMAIL.equalsIgnoreCase(ratePlanOrchV2.getRtbEmail());
            if (corpPreferredRateSegmentId != null && corpPreferredRateSegmentId.equals(ratePlanOrchV2.getSegmentId())) {
                PersuasionResponse specialFarePersuasion = buildSpecialFareTagWithInfoPersuasion(corpAlias, rtbEmail);
                if (persuasions == null) {
                    persuasions = new ArrayList<>();
                }
                CollectionUtils.addIgnoreNull(persuasions, specialFarePersuasion);
            }
            if (rtbEmail) {
                if (persuasions == null) {
                    persuasions = new ArrayList<>();
                }
                PersuasionResponse confirmationTextPersuasion = buildConfirmationTextPersuasion(true);
                CollectionUtils.addIgnoreNull(persuasions, confirmationTextPersuasion);
            }
        }

        // TODO: Add when rpBookingModel field is available in OrchV2 RatePlan
        // RTB confirmation text persuasion
        // if (ratePlanOrchV2 != null && RTB_EMAIL.equalsIgnoreCase(ratePlanOrchV2.getRpBookingModel())) {
        //     if (persuasions == null)
        //         persuasions = new ArrayList<>();
        //     PersuasionResponse persuasion = buildConfirmationTextPersuasion(corpAlias, false, false);
        //     if (persuasion != null) {
        //         persuasions.add(persuasion);
        //     }
        // }

        return persuasions;
    }

    private PersuasionResponse getHotelCloudPersuasions(RatePlan ratePlan) {
        PersuasionResponse persuasionResponse = null;
        if (ratePlan != null && ratePlan.getRatePlanFlags() != null && ratePlan.getRatePlanFlags().isHotelCloud()) {
            persuasionResponse = new PersuasionResponse();
            persuasionResponse.setId(HOTEL_CLOUD);
            persuasionResponse.setTemplate(IMAGE_TEXT_H);
            Style style = new Style();
            style.setIconHeight(20);
            style.setIconWidth(90);
            persuasionResponse.setStyle(style);
            persuasionResponse.setPlaceholderId(PRICE_TOP);
            persuasionResponse.setIconUrl(HOTEL_CLOUD_PERSUASION_ICON);
        }
        return persuasionResponse;
    }

    private PersuasionResponse getStaycationPersuasion(RatePlan ratePlanOrchV2, String funnelSource, List<PersuasionResponse> persuasions) {
        PersuasionResponse staycationPersuasion = null;
        if (ratePlanOrchV2 != null && ratePlanOrchV2.getRatePlanFlags() != null && ratePlanOrchV2.getRatePlanFlags().isStaycationDeal() &&
                "GETAWAY".equalsIgnoreCase(funnelSource)) {
            staycationPersuasion = new PersuasionResponse();
            staycationPersuasion.setPersuasionText(polyglotService.getTranslatedData(ConstantsTranslation.GETAWAY_DEAL_PERSUASION_TEXT));
            staycationPersuasion.setPlaceholderId("rightBottom");
            staycationPersuasion.setId("STAYCATION");
            staycationPersuasion.setTemplate(TEXT_WITH_BG_IMAGE);
            staycationPersuasion.setStyle(new Style());
            staycationPersuasion.getStyle().setTextColor("#4a4a4a");
            staycationPersuasion.getStyle().setFontSize(SMALL);
            staycationPersuasion.getStyle().setBgUrl(staycationGetawayPersuasionIcon);

        }
        return staycationPersuasion;
    }

    private PersuasionResponse getRateExclusivePersuasion(RatePlan ratePlanOrchV2, CommonModifierResponse commonModifierResponse) {
        PersuasionResponse exclusiveRatePersuasion = null;
        if (Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId()) &&
                mypatExclusiveRateSegmentIdList.contains(ratePlanOrchV2.getSegmentId())) {
            exclusiveRatePersuasion = new PersuasionResponse();
            exclusiveRatePersuasion.setPersuasionText("Partner Exclusive Rate");
            exclusiveRatePersuasion.setId("MY_PARTNER_SEGMENTS");
            exclusiveRatePersuasion.setPlaceholderId("PC_RIGHT_3");
            exclusiveRatePersuasion.setTemplate("PARTNER_PERSUASION");
            exclusiveRatePersuasion.setStyle(new Style());
            exclusiveRatePersuasion.getStyle().setTextColor("#fff");
            exclusiveRatePersuasion.getStyle().setBgGradient(new BGGradient());
            exclusiveRatePersuasion.getStyle().getBgGradient().setAngle("H");
            exclusiveRatePersuasion.getStyle().getBgGradient().setColor(Arrays.asList("#f5515f", "#9f0469"));
            exclusiveRatePersuasion.setSubText("Get the best rates possible");

        }
        return exclusiveRatePersuasion;
    }

    private PersuasionResponse getLuxPackagePersuasion(boolean isLuxeHotel) {
        String id = Constants.MMT_NON_LUXE_PACKAGE;
        String persuasionTextKey = PERSUASION_NON_LUXE_PACKAGE_TEXT;
        if (isLuxeHotel) {
            id = Constants.MMT_LUXE_PACKAGE;
            persuasionTextKey = PERSUASION_LUXE_PACKAGE_TEXT;
        }
        PersuasionResponse persuasion = new PersuasionResponse();
        persuasion.setPersuasionText(polyglotService.getTranslatedData(persuasionTextKey));
        persuasion.setPlaceholderId(RIGHT_BOTTOM);
        persuasion.setId(id);
        persuasion.setTemplate(TEXT_WITH_BG_IMAGE);
        persuasion.setStyle(new Style());
        persuasion.getStyle().setTextColor(CF8100);
        persuasion.getStyle().setFontSize(SMALL);
        persuasion.getStyle().setBgUrl(luxePackageIconUrl);
        return persuasion;
    }

    private PersuasionResponse getCorpPolicyPersuasion() {
        PersuasionResponse persuasion = new PersuasionResponse();
        persuasion.setPersuasionText(polyglotService.getTranslatedData(ConstantsTranslation.OUT_OF_POLICY_BOLD));
        persuasion.setPlaceholderId(TOP_RIGHT);
        persuasion.setHtml(true);
        persuasion.setId("OOP");
        persuasion.setTemplate(OVAL_PERSUASION_TEMPLATE);
        persuasion.setStyle(new Style());
        persuasion.getStyle().setTextColor(FFFFFF);
        persuasion.getStyle().setFontSize(SMALL);
        persuasion.getStyle().setBgColor(D0021B);
        return persuasion;
    }


    private PersuasionResponse getOccasionPackagePersuasion(RatePlan ratePlan) {
        if (ratePlan != null && ratePlan.getPackageDetails() != null) {
            PackageDetails packageDetails = ratePlan.getPackageDetails();
            PersuasionResponse persuasion = new PersuasionResponse();
            persuasion.setPlaceholderId(PRICE_TOP);
            persuasion.setStyle(new Style());
            persuasion.setId(Constants.MMT_OCCASION_PACKAGE);
            persuasion.setPersuasionText(packageDetails.getTagText());
            persuasion.setTemplate(Constants.TEXT_WITH_BG_IMAGE);
            persuasion.getStyle().setTextColor(packageDetails.getTagColor());
            persuasion.getStyle().setBgUrl(packageDetails.getTagImageUrl());
            persuasion.getStyle().setFontSize(SMALL);
            return persuasion;
        }
        return null;
    }

        /**
     * Check if super package persuasion should be set
     */
    private boolean setSuperPackagePersuasion(CommonModifierResponse commonModifierResponse) {
        return commonModifierResponse != null && commonModifierResponse.getExpDataMap() != null &&
               commonModifierResponse.getExpDataMap().containsKey(Constants.EXP_SPKG) &&
               Constants.EXP_TRUE_VALUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(Constants.EXP_SPKG));
    }

    /**
     * Build super package persuasion
     */
    private PersuasionResponse getSuperPackagePersuasion() {
        PersuasionResponse persuasion = new PersuasionResponse();
        persuasion.setId(Constants.MMT_SUPER_PACKAGE);
        persuasion.setPlaceholderId(Constants.PRICE_TOP);
        persuasion.setPersuasionText(Constants.SUPER_PACKAGE);
        persuasion.setTemplate(TEXT_WITH_BG_IMAGE);
        Style style = new Style();
        style.setTextColor(Constants.COLOR_A47313);
        style.setBgUrl("https://promos.makemytrip.com/Hotels_product/Details/Packages/gold-cut-border-empty.png");
        style.setFontSize("SMALL");
        persuasion.setStyle(style);
        return persuasion;
    }

    /**
     * Build BNPL persuasion for rate plan
     */
    private PersuasionResponse buildBnplPersuasion() {
        PersuasionResponse bnplPersuasion = new PersuasionResponse();
        bnplPersuasion.setPlaceholderId(Constants.PRICE_BOTTOM_PLACEHOLDER_ID);
        bnplPersuasion.setPersuasionText(polyglotService.getTranslatedData(Constants.BNPL_DETAIL_PERSUASION_TITLE));
        bnplPersuasion.setHtml(false);
        bnplPersuasion.setTemplate(Constants.IMAGE_TEXT_H);
        Style style = new Style();
        style.setTextColor("#FF018786");
        style.setFontSize("SMALL");
        style.setFontType("B");
        bnplPersuasion.setStyle(style);
        return bnplPersuasion;
    }

    /**
     * Builds search rooms level persuasions (response root level) based on hotel details
     * This maps to the legacy CommonResponseTransformer.buildPersuasions method
     */
    public List<PersuasionObject> buildSearchRoomsPersuasions(com.gommt.hotels.orchestrator.detail.model.response.HotelDetails hotelDetails, String deviceType) {

        try {

            List<PersuasionObject> persuasionsList = new ArrayList<>();
            PersuasionObject persuasionObject = new PersuasionObject();

            // Set template and placeholder based on device type
            if (CLIENT_DESKTOP.equalsIgnoreCase(deviceType)) {
                persuasionObject.setTemplate("IMAGE_TEXT_DEAL");
                persuasionObject.setPlaceholder("TOP_RIGHT");
            } else {
                persuasionObject.setTemplate("MULTI_PERSUASION_CAROUSEL");
                persuasionObject.setPlaceholder("PLACEHOLDER_CARD_M1");
            }

            // Set style
            PersuasionStyle style = new PersuasionStyle();
            style.setBorderColor("#e5e5e5");
            style.setBorderSize("1");
            style.setCornerRadii("8");
            persuasionObject.setStyle(style);

            // Build persuasions data
            List<com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData> data = new ArrayList<>();
            Map<String, com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData> persuasions =
                buildPersuasionsData(hotelDetails, deviceType);

            List<String> order = Utility.isGCC() ? detailPagePersuasionOrderMap.get("GCC") : detailPagePersuasionOrderMap.get("B2C");
            for (String persuasionType : order) {
                if (persuasions.containsKey(persuasionType)) {
                    data.add(persuasions.get(persuasionType));
                }
            }

            persuasionObject.setData(data);
            persuasionsList.add(persuasionObject);
            return CollectionUtils.isNotEmpty(data) ? persuasionsList : null;

        } catch (Exception e) {
            LOGGER.error("Error in building search rooms persuasions", e);
        }
        return null;
    }

    /**
     * Builds individual persuasions data from hotel details
     * Maps to legacy CommonResponseTransformer.buildPersuasionsData method
     */
    private Map<String, com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData> buildPersuasionsData(
            com.gommt.hotels.orchestrator.detail.model.response.HotelDetails hotelDetails, String deviceType) {

        Map<String, com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData> persuasions = new HashMap<>();
        PersuasionStyle persuasionChildStyle = createPersuasionChildStyle();

        if (!CLIENT_DESKTOP.equalsIgnoreCase(deviceType)) {
            if (CollectionUtils.isNotEmpty(hotelDetails.getDealBenefits())) {
                for (com.gommt.hotels.orchestrator.detail.model.response.persuasion.DealBenefits dealBenefit : hotelDetails.getDealBenefits()) {
                    if (dealBenefit != null && dealBenefit.getBenefitType() != null) {
                        switch (dealBenefit.getBenefitType().name()) {
                            case ONLY_TODAY_DEAL_BENEFITS:
                                buildOnlyTodayDealPersuasion(dealBenefit, persuasions, persuasionChildStyle);
                                break;
                            case MMT_EXCLUSIVE_BENEFITS:
                                buildMMTExclusivePersuasion(dealBenefit, persuasions, persuasionChildStyle);
                                break;
                            case BLACK_BENEFITS:
                                buildBlackInfoPersuasion(dealBenefit, persuasions, persuasionChildStyle);
                                break;
                            case LONG_STAY_BENEFITS:
                                buildLongStayBenefitsPersuasion(dealBenefit, persuasions, persuasionChildStyle);
                                break;
                        }
                    }
                }
            }
            buildOccasionPackagePersuasion(hotelDetails, persuasions, persuasionChildStyle);

            // Discount persuasion - TODO: Map from hotelDetails.getOffers() when available
            // Legacy: hotelRates.getAppliedOffers() -> OrchV2: hotelDetails.getOffers()
        }

        return persuasions;
    }


    private void buildOnlyTodayDealPersuasion(com.gommt.hotels.orchestrator.detail.model.response.persuasion.DealBenefits dealBenefit, Map<String, PersuasionData> persuasions, PersuasionStyle persuasionChildStyle) {
        PersuasionData dealPersuasion = new PersuasionData();
        dealPersuasion.setStyle(persuasionChildStyle);
        dealPersuasion.setType(onlyTodayDealPersuasionType);
        if (dealBenefit.getTitle() != null) {
            PersuasionTitle title = new PersuasionTitle();
            title.setText(dealBenefit.getTitle());
            PersuasionStyle titleStyle = new PersuasionStyle();
            titleStyle.setTextColor("#007E7D");
            title.setStyle(titleStyle);
            dealPersuasion.setPersuasionTitle(title);
        }
        if (dealBenefit.getSubTitle() != null) {
            dealPersuasion.setPersuasionText(dealBenefit.getSubTitle());
        }
        if (dealBenefit.getTimer() != null) {
            PersuasionTimer timer = new PersuasionTimer();
            timer.setExpiry(dealBenefit.getTimer().getExpiry());
            timer.setStyle(onlyTodayDealTimerStyle);
            dealPersuasion.setTimer(timer);
        }
        persuasions.put(onlyTodayDealPersuasionType, dealPersuasion);
    }

    private void buildMMTExclusivePersuasion(com.gommt.hotels.orchestrator.detail.model.response.persuasion.DealBenefits dealBenefit, Map<String, PersuasionData> persuasions, PersuasionStyle persuasionChildStyle) {
        if (CollectionUtils.isNotEmpty(dealBenefit.getInclusionsList())) {
            com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData persuasionData =
                    new com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData();

            persuasionData.setImageUrl(dealBenefit.getImageUrl());
            persuasionData.setIconurl(dealBenefit.getIconUrl());
            List<String> inclusions = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.da.Inclusion inclusion : dealBenefit.getInclusionsList()) {
                if (StringUtils.isNotEmpty(inclusion.getCode())) {
                    inclusions.add(inclusion.getCode());
                }
            }
            persuasionData.setInclusions(inclusions);
            persuasionData.setStyle(persuasionChildStyle);
            persuasionData.setType(MMT_EXCLUSIVE_TYPE);
            if (CollectionUtils.isNotEmpty(inclusions)) {
                persuasions.put(MMT_EXCLUSIVE_TYPE, persuasionData);
            }

        }
    }

    private PersuasionStyle createPersuasionChildStyle() {
        PersuasionStyle persuasionChildStyle = new PersuasionStyle();
        persuasionChildStyle.setTextColor("#757575");
        persuasionChildStyle.setIconHeight(6);
        persuasionChildStyle.setIconWidth(6);
        persuasionChildStyle.setImageHeight("24");
        persuasionChildStyle.setImageWidth("180");
        return persuasionChildStyle;
    }

    private void buildBlackInfoPersuasion(com.gommt.hotels.orchestrator.detail.model.response.persuasion.DealBenefits dealBenefit,
                                          Map<String, com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData> persuasions, PersuasionStyle persuasionChildStyle) {
        if (CollectionUtils.isNotEmpty(dealBenefit.getInclusionsList())) {
            com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData persuasionBlackInfoData =
                    new com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData();

            persuasionBlackInfoData.setIconurl(dealBenefit.getLoyaltyDetails().getIconUrl());
            persuasionBlackInfoData.setImageUrl(dealBenefit.getLoyaltyDetails().getTierHeaderUrl());

            // Build inclusions list
            List<String> inclusions = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.da.Inclusion inclusion : dealBenefit.getInclusionsList()) {
                if (StringUtils.isNotEmpty(inclusion.getCode())) {
                    inclusions.add(inclusion.getCode());
                }
            }
            persuasionBlackInfoData.setInclusions(inclusions);
            persuasionBlackInfoData.setStyle(persuasionChildStyle);
            persuasionBlackInfoData.setType(blackPersuasionType);

            if (CollectionUtils.isNotEmpty(inclusions)) {
                persuasions.put("BLACK", persuasionBlackInfoData);
            }
        }

    }

    private void buildLongStayBenefitsPersuasion(com.gommt.hotels.orchestrator.detail.model.response.persuasion.DealBenefits dealBenefit,
            Map<String, com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData> persuasions, PersuasionStyle persuasionChildStyle) {

        if (CollectionUtils.isNotEmpty(dealBenefit.getInclusionsList())) {
            com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData persuasionData = new com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData();

            persuasionData.setImageUrl(dealBenefit.getImageUrl());
            persuasionData.setIconurl(dealBenefit.getIconUrl());

            // Build inclusions list
            List<String> inclusions = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.da.Inclusion inclusion : dealBenefit.getInclusionsList()) {
                if (StringUtils.isNotEmpty(inclusion.getCode())) {
                    inclusions.add(inclusion.getCode());
                }
            }
            persuasionData.setInclusions(inclusions);
            persuasionData.setStyle(persuasionChildStyle);
            persuasionData.setType(losPersuasionType);

            if (CollectionUtils.isNotEmpty(inclusions)) {
                persuasions.put(losPersuasionType, persuasionData);
            }
        }
    }

    private void buildOccasionPackagePersuasion(
            com.gommt.hotels.orchestrator.detail.model.response.HotelDetails hotelDetails,
            Map<String, com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData> persuasions,
            PersuasionStyle persuasionChildStyle) {

        // Check for occasion package rooms
        if (CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {
            for (Rooms room : hotelDetails.getRooms()) {
                if ("OCCASION_PACKAGE".equalsIgnoreCase(room.getType()) && room.getPackageDetails() != null) {

                    com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData persuasionOccassionData =
                        new com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData();

                    List<String> inclusions = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(room.getRatePlans()) && CollectionUtils.isNotEmpty(room.getRatePlans().get(0).getInclusions())) {
                        room.getRatePlans().get(0).getInclusions().forEach(inclusion -> inclusions.add(inclusion.getCode()));
                    }

                    // Set data from package details
                    persuasionOccassionData.setImageUrl(room.getPackageDetails().getPersuasionImageUrl());
                    persuasionOccassionData.setIconurl(room.getPackageDetails().getPersuasionIconUrl());
                    persuasionOccassionData.setPersuasionText(room.getPackageDetails().getPersuasionText());
                    persuasionOccassionData.setInclusions(inclusions);
                    persuasionOccassionData.setStyle(persuasionChildStyle);
                    persuasionOccassionData.setType(superPackagePersuasionType);

                    if (CollectionUtils.isNotEmpty(inclusions)) {
                        persuasions.put("SUPER_PACKAGE", persuasionOccassionData);
                    }
                    return;
                }

                // Check for regular package rooms
                if (RoomType.SUPER_PACKAGE.name().equalsIgnoreCase(room.getType())) {// && room.getPackageDetails() != null) {

                    com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData persuasionPackageData =
                        new com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData();

                    List<String> inclusions = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(room.getRatePlans()) && CollectionUtils.isNotEmpty(room.getRatePlans().get(0).getInclusions())) {
                        room.getRatePlans().get(0).getInclusions().forEach(inclusion -> inclusions.add(inclusion.getCode()));
                    }

                    persuasionPackageData.setImageUrl("https://promos.makemytrip.com/Hotels_product/Details/Packages/Final/super-package.png"); //TODO Move to config
                    persuasionPackageData.setIconurl("https://promos.makemytrip.com/Hotels_product/Details/Packages/Final/super-package.png"); //TODO Move to config
                    //persuasionPackageData.setPersuasionText(room.getPackageDetails().getPersuasionText());
                    persuasionPackageData.setInclusions(inclusions);
                    persuasionPackageData.setStyle(persuasionChildStyle);
                    persuasionPackageData.setType(superPackagePersuasionType);

                    if (CollectionUtils.isNotEmpty(inclusions)) {
                        persuasions.put("SUPER_PACKAGE", persuasionPackageData);
                    }
                    return;
                }
            }
        }
    }

    public void setSuperPackageCard(SearchRoomsResponse searchRoomsResponse, String askedCurrency) {
        com.mmt.hotels.clientgateway.response.rooms.RoomFilterCard roomFilterCard = new com.mmt.hotels.clientgateway.response.rooms.RoomFilterCard();
        roomFilterCard.setTitle(buildSuperPackageCardText(searchRoomsResponse, askedCurrency));
        roomFilterCard.setCtaText(polyglotService.getTranslatedData(ConstantsTranslation.VIEW_ALL_PACKAGES));
        roomFilterCard.setBgStyle(getBgStyle(FFFFFF, Constants.ffeaa7, Constants.DIAGONAL_START));
        roomFilterCard.setIconUrl(superPackageIconUrl);
        roomFilterCard.setFilterCode(new ArrayList<>());
        roomFilterCard.getFilterCode().add(PACKAGE_RATE);
        searchRoomsResponse.setRoomFilterCard(roomFilterCard);
    }

    private String buildSuperPackageCardText(SearchRoomsResponse searchRoomsResponse, String askedCurrency) {
        String superPackageCardText = polyglotService.getTranslatedData(ConstantsTranslation.ENJOY_PACKAGE_BENEFITS);
        String slashedPrice = "";
        String priceBenfits = "";
        if(searchRoomsResponse!=null && searchRoomsResponse.getPackageRooms()!=null && CollectionUtils.isNotEmpty(searchRoomsResponse.getPackageRooms()) && CollectionUtils.isNotEmpty(searchRoomsResponse.getPackageRooms().get(0).getRatePlans()) && searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0) instanceof PackageSelectRoomRatePlan) {
            PackageSelectRoomRatePlan packageSelectRoomRatePlan = (PackageSelectRoomRatePlan) searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0);
            if(packageSelectRoomRatePlan!=null && packageSelectRoomRatePlan.getPackageInclusionDetails()!=null) {
                slashedPrice = String.valueOf((int)Double.parseDouble(packageSelectRoomRatePlan.getPackageInclusionDetails().getPackageBenefitsSlashedPrice()));
                priceBenfits = String.valueOf((int)Double.parseDouble(packageSelectRoomRatePlan.getPackageInclusionDetails().getPackageBenefitsPrice()));
            }
        }
        if(searchRoomsResponse!=null && CollectionUtils.isNotEmpty(searchRoomsResponse.getPackageRooms()) && CollectionUtils.isNotEmpty(searchRoomsResponse.getPackageRooms().get(0).getRatePlans()) && CollectionUtils.isNotEmpty(searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0).getInclusionsList())) {
            List<BookedInclusion> inclusionList = searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0).getInclusionsList();
            for(BookedInclusion inclusion:inclusionList) {
                if(StringUtils.isEmpty(inclusion.getType()) && StringUtils.isNotEmpty(slashedPrice) && StringUtils.isNotEmpty(priceBenfits)) {
                    String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : DEFAULT_CUR_INR).getCurrencySymbol();
                    superPackageCardText = polyglotService.getTranslatedData(SUPER_PACKAGE_CARD_TEXT);
                    superPackageCardText = superPackageCardText.replace("{incText}", inclusion.getCode());
                    superPackageCardText = superPackageCardText.replace("{1}", slashedPrice);
                    superPackageCardText = superPackageCardText.replace("{2}", priceBenfits);
                    superPackageCardText = superPackageCardText.replace("{cur}", currencySymbol);
                    break;
                }
            }
        }
        return superPackageCardText;
    }

    public ExtraGuestDetailPersuasion buildExtraGuestDetailPersuasion(boolean isHighDemandPersuasonEnable, List<Rooms> rooms, boolean isAltAccoHotel) {
        ExtraGuestDetailPersuasion extraGuestDetailPersuasion;
        //High Demand persuasion has highest priority
        if (isHighDemandPersuasonEnable) {
            extraGuestDetailPersuasion = new ExtraGuestDetailPersuasion();
            String persuasionText = polyglotService.getTranslatedData(HIGH_DEMAND_PERSUASION);
            extraGuestDetailPersuasion.setText("<font color=\"" + highDemandPersuasionColor + "\">" + persuasionText + "</font>");
            return extraGuestDetailPersuasion;
        } else if (!isAltAccoHotel && CollectionUtils.isNotEmpty(rooms) && CollectionUtils.isNotEmpty(rooms.get(0).getRatePlans())) {
            ExtraGuestDetail extraGuestDetail = rooms.get(0).getRatePlans().get(0).getExtraGuestDetail();
            if (StringUtils.isNotEmpty(rooms.get(0).getRatePlans().get(0).getFreeChildText())) {
                return getExtraGuestDetailFreeChildText(rooms.get(0).getRatePlans().get(0).getFreeChildText());
            }
            if (extraGuestDetail != null && StringUtils.isNotEmpty(extraGuestDetail.getHotelDetailExtraBedText())) {
                extraGuestDetailPersuasion = new ExtraGuestDetailPersuasion();
                extraGuestDetailPersuasion.setIconUrl(polyglotService.getTranslatedData(DEAL_ICON_URL));
                String translatedText = polyglotService.getTranslatedData(EXTRA_GUEST_DEAL_TEXT_STYLE);
                if (StringUtils.isNotEmpty(translatedText)) {
                    translatedText = MessageFormat.format(translatedText, extraGuestDetail.getHotelDetailExtraBedText());
                    extraGuestDetailPersuasion.setText(translatedText);
                    return extraGuestDetailPersuasion;
                }
            }

        }
        return null;
    }

    private ExtraGuestDetailPersuasion getExtraGuestDetailFreeChildText(String freeChildText) {
        ExtraGuestDetailPersuasion extraGuestDetailPersuasion = null;
        if (StringUtils.isNotEmpty(freeChildText)) {
            extraGuestDetailPersuasion = new ExtraGuestDetailPersuasion();
            extraGuestDetailPersuasion.setIconUrl(singleTickUrl);
            extraGuestDetailPersuasion.setText("<font color=\"" + extraGuestFreeChildColor + "\">" + freeChildText + "</font>");
        }
        return extraGuestDetailPersuasion;
    }


    private BgStyle getBgStyle(String start, String end, String direction) {
        BgStyle bgStyle = new BgStyle();
        bgStyle.setStart(start);
        bgStyle.setEnd(end);
        bgStyle.setDirection(direction);
        return bgStyle;
    }

    public Map<String, PersuasionResponse> buildRatePlanPersuasionsMap(RatePlan ratePlan, CommonModifierResponse commonModifierResponse, List<DealBenefits> dealBenefits) {
        BlackInfo blackInfo = null;
        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();
        boolean isMyPartnerRequest = (commonModifierResponse != null) && (commonModifierResponse.getExtendedUser() != null) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
        if(isMyPartnerRequest) {
            if (CollectionUtils.isNotEmpty(dealBenefits)) {
                DealBenefits loyaltydealBenefits = dealBenefits.stream().filter(dealBenefit -> BenefitType.LOYALTY_HERO_BENEFITS.equals(dealBenefit.getBenefitType()))
                        .findFirst().orElse(null);
                if (loyaltydealBenefits != null && loyaltydealBenefits.getLoyaltyDetails() != null) {
                    blackInfo = loyaltydealBenefits.getLoyaltyDetails();
                }
            }

            PersuasionResponse fareHoldPersuasion = getFareHoldPersuasion(ratePlan);
            if (fareHoldPersuasion != null) {
                persuasionMap.put(Constants.BOOK_NOW_PERSUASION_KEY, fareHoldPersuasion);
            }
            PersuasionResponse myPartnerCashbackPersuasion = null;
            myPartnerCashbackPersuasion = getMyPartnerCashbackPersuasion(ratePlan, blackInfo);
            if (myPartnerCashbackPersuasion != null) {
                persuasionMap.put(CASHBACK_HERO_OFFER_PERSUASION_NODE, myPartnerCashbackPersuasion);
            }
        }
        return persuasionMap;
    }

    private PersuasionResponse getMyPartnerCashbackPersuasion(RatePlan ratePlan, BlackInfo blackInfo) {
        PriceDetail priceDetail = ratePlan.getPrice();
        PersuasionResponse myPartnerCashbackPersuasion = null;
        if (priceDetail != null && (priceDetail.getDiscount() != null || blackInfo != null)) {
            int myPartnerCashback = priceDetail.getCashbackDetails() != null ? (int) priceDetail.getCashbackDetails().getLoyalty() : 0;
            PriceCouponInfo coupon = StringUtils.isNotEmpty(priceDetail.getCouponCode()) ? priceDetail.getApplicableCoupons()
                    .stream()
                    .filter(applicableCoupon -> priceDetail.getCouponCode().equalsIgnoreCase(applicableCoupon.getCouponCode()))
                    .findFirst()
                    .orElse(null) : null;
            //If manthan is sending rewardbonus in mmtVals node, coupon will be set as CTW->Cashback Amount(Cashback offer persuasion condition)
            if (coupon != null) {
                boolean isCashbackAmtAvailable = MapUtils.isNotEmpty(coupon.getDiscountBreakup()) && coupon.getDiscountBreakup().containsKey("CTW");
                if (StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage()) || isCashbackAmtAvailable || myPartnerCashback > 0) {
                    myPartnerCashbackPersuasion = buildLoyaltyCashbackPersuasions(coupon, myPartnerCashback, blackInfo);
                }
            } else if (myPartnerCashback > 0) {
                myPartnerCashbackPersuasion = buildLoyaltyCashbackPersuasions(coupon, myPartnerCashback, blackInfo);
            }
        }
        return myPartnerCashbackPersuasion;
    }

    private PersuasionResponse getFareHoldPersuasion(RatePlan ratePlan) {
        PersuasionResponse fareHoldPersuasion = null;
        if (ratePlan != null && ratePlan.getBnplDetails() != null && ratePlan.getBnplDetails().isBnplApplicable()) {
            int bookingValue = (int) (ratePlan.getBnplDetails().getBookingAmount());
            fareHoldPersuasion = new PersuasionResponse();
            fareHoldPersuasion.setTitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_TITLE),
                    String.valueOf(bookingValue)));
            Hover hover = new Hover();
            if (ratePlan.getBnplDetails().getExpiry() != null) {
                long expiry = ratePlan.getBnplDetails().getExpiry();
                hover.setTitleText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_TITLE),
                        dateUtil.convertEpochToDateTime(expiry, DateUtil.DD_MMM_hh_mm_a)));
            }
            hover.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_SUB_TITLE));
            fareHoldPersuasion.setHover(hover);
        }
        return fareHoldPersuasion;
    }


    private PersuasionResponse buildLoyaltyCashbackPersuasions(PriceCouponInfo coupon, int myPartnerCashback, BlackInfo loyaltyInfo) {
        PersuasionResponse persuasion = null;
        String persuasionAppliedText = polyglotService.getTranslatedData(ConstantsTranslation.MYPARTNER_TIER_OFFER_TEXT);

        if (myPartnerCashback > 0 && StringUtils.isNotEmpty(persuasionAppliedText) && loyaltyInfo.getTierName() != null) {
            persuasion = new PersuasionResponse();
            persuasionAppliedText = persuasionAppliedText.replace(TIER_NAME, loyaltyInfo.getTierName()).replace(CASHBACK_AMOUNT, Integer.toString(myPartnerCashback));
            persuasion.setPersuasionText(persuasionAppliedText);
            buildTierPersuasion(loyaltyInfo, persuasion);
            String iconType = coupon != null && StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage()) ? HERO_OFFER_PERSUASION_ICON_TYPE : CASHBACK_OFFER_PERSUASION_ICON_TYPE;
            persuasion.setIconType(iconType);
            persuasion.setTemplate(MY_PARTNER_HERO_EFFECTIVE);
        } else if (coupon != null) {
            persuasion = buildLoyaltyCashbackPersuasions(coupon);
        }
        return persuasion;
    }

    public static void buildTierPersuasion(BlackInfo loyaltyInfo, PersuasionResponse persuasion) {
        if (loyaltyInfo == null) {
            return;
        }
        persuasion.setHtml(true);
        persuasion.setIconUrl(loyaltyInfo.getTierHeaderUrl());
        Style style = new Style();
        style.setTextColor(loyaltyInfo.getTextColor());
        BGGradient bgGradient = new BGGradient();
        bgGradient.setColor(loyaltyInfo.getBgGradient());
        style.setBgGradient(bgGradient);
        persuasion.setStyle(style);
    }

    public PersuasionResponse buildLoyaltyCashbackPersuasions(PriceCouponInfo coupon) {
        PersuasionResponse persuasion = null;
        StringBuilder persuasionAppliedText = new StringBuilder();
        if (StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage())) {
            persuasionAppliedText.append(String.format(polyglotService.getTranslatedData(ConstantsTranslation.LOYALTY_OFFER_TEXT), coupon.getLoyaltyOfferMessage()));
        } else if (MapUtils.isNotEmpty(coupon.getDiscountBreakup()) && coupon.getDiscountBreakup().containsKey("CTW")) {
            int cashbackDiscountAmtRounded = (int) Math.round(coupon.getDiscountBreakup().get("CTW"));
            persuasionAppliedText.append(String.format(polyglotService.getTranslatedData(ConstantsTranslation.CASHBACK_OFFER_TEXT), cashbackDiscountAmtRounded));
        }
        if(StringUtils.isNotEmpty(persuasionAppliedText)) {
            persuasion = new PersuasionResponse();
            String iconType = StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage()) ? HERO_OFFER_PERSUASION_ICON_TYPE : CASHBACK_OFFER_PERSUASION_ICON_TYPE;
            persuasion.setPersuasionText(persuasionAppliedText.toString());
            persuasion.setHtml(true);
            persuasion.setIconType(iconType);
        }
        return persuasion;
    }

    public void buildLosComboSavingPersuasion(double comboSaving, Map<String,PersuasionResponse> persuasionMap, String currency) {
        if(comboSaving > 0) {
            PersuasionResponse comboSavingPersuasion = new PersuasionResponse();
            String formattedAmount = utility.convertNumericValueToCommaSeparatedString((int) comboSaving, Locale.ENGLISH);
            String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(currency) ? currency : "INR").getCurrencySymbol();
            String title = polyglotService.getTranslatedData(ConstantsTranslation.COMBO_SAVING_TEXT).replace(COMBO_SAVING_AMOUNT, formattedAmount).replace(MULTI_ROOM_STAY_CURRENCY_SYMBOL, currencySymbol);
            String displayText = polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_COMBO_OFFER_TEXT).replace(COMBO_SAVING_AMOUNT, formattedAmount).replace(MULTI_ROOM_STAY_CURRENCY_SYMBOL, currencySymbol);
            comboSavingPersuasion.setHtml(true);
            comboSavingPersuasion.setTitle(title);
            comboSavingPersuasion.setDisplayText(displayText);
            comboSavingPersuasion.setIconUrl(multiRoomStaySavingComboIcon);
            persuasionMap.put(COMBO_SAVING_KEY, comboSavingPersuasion);
        }
    }

    public SpecialOfferCard buildSpecialOfferCard(HotelDetails hotelDetails) {
        if (hotelDetails == null) {
            return null;
        }
        PackageDetails packageDetails = null;
        if (CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {
            for (Rooms room : hotelDetails.getRooms()) {
                if ("OCCASION_PACKAGE".equalsIgnoreCase(room.getType()) && room.getPackageDetails() != null) {
                    packageDetails = room.getPackageDetails();
                    break;
                }
            }
        }
        if (packageDetails != null && StringUtils.isNotEmpty(packageDetails.getHeaderImageUrl()) && StringUtils.isNotEmpty(packageDetails.getSpecialCardImageUrl())) {
            SpecialOfferCard specialOfferCard = new SpecialOfferCard();
            specialOfferCard.setImageUrl(packageDetails.getSpecialCardImageUrl());
            specialOfferCard.setHeaderImage(packageDetails.getHeaderImageUrl());
            specialOfferCard.setText(packageDetails.getText());
            if (packageDetails.getBgGradient() != null) {
                BgGradient bgGradient = new BgGradient();
                bgGradient.setStart(packageDetails.getBgGradient().getStart());
                bgGradient.setCenter(packageDetails.getBgGradient().getCenter());
                bgGradient.setEnd(packageDetails.getBgGradient().getEnd());
                bgGradient.setDirection(packageDetails.getBgGradient().getDirection());
                specialOfferCard.setBgGradient(bgGradient);
                specialOfferCard.setBgGradient(bgGradient);
            }
            specialOfferCard.setType(packageDetails.getType());
            return specialOfferCard;
        }
        return null;
    }

    public PersuasionResponse buildDelayedConfirmationPersuasion(String corpAlias, boolean isMyBizNewDetailsPage) {
        PersuasionResponse delayedConfirmationPersuasion = new PersuasionResponse();
        delayedConfirmationPersuasion.setId(DELAYED_CONFIRMATION);
        String title = isMyBizNewDetailsPage ? polyglotService.getTranslatedData(DELAYED_CONFIRMATION_PERSUASION_MYB_NEW_DETAILS_TITLE) : polyglotService.getTranslatedData(DELAYED_CONFIRMATION_PERSUASION_TITLE);
        corpAlias = corpAlias != null ? corpAlias : StringUtils.EMPTY;
        title = StringUtils.replace(title, "{CORP_ALIAS}", corpAlias);
        title = StringUtils.replace(title, NO_OF_HOURS_PLACEHOLDER, String.valueOf(noOfHoursForConfirmation));
        delayedConfirmationPersuasion.setTitle(title);
        Style style = new Style();
        if (isMyBizNewDetailsPage) {
            style.setBgColor(Constants.DELAYED_CONFIRMATION_MYB_NEW_DETAILS_BG_COLOR);
        } else {
            style.setBgColor(Constants.DELAYED_CONFIRMATION_BG_COLOR);
        }
        delayedConfirmationPersuasion.setStyle(style);
        return delayedConfirmationPersuasion;
    }


    public PersuasionResponse buildSpecialFareTagWithInfoPersuasion(String corpAlias, boolean rtbEmail) {
        PersuasionResponse specialFarePersuasion = new PersuasionResponse();
        Style style = new Style();
        style.setStyleClass(SPECIAL_FARE_TAG_LARGE_STYLE);
        specialFarePersuasion.setStyle(style);
        String title = polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_FARE_TAG);
        corpAlias = corpAlias != null ? corpAlias : StringUtils.EMPTY;
        title = StringUtils.replace(title, "{CORP_ALIAS}", corpAlias);
        specialFarePersuasion.setTitle(title);
        specialFarePersuasion.setId(SPECIAL_FARE_TAG_LARGE);
        specialFarePersuasion.setIconType("infoIconLarge");
        Hover hover = new Hover();
        String titleText = polyglotService.getTranslatedData(SPECIAL_FARE_TITLE_TEXT_NOT_RTB);
        if (rtbEmail) {
            polyglotService.getTranslatedData(SPECIAL_FARE_TITLE_TEXT);
            titleText = StringUtils.replace(titleText, NO_OF_HOURS_PLACEHOLDER, String.valueOf(noOfHoursForConfirmation));
        }
        hover.setTitleText(titleText);
        specialFarePersuasion.setHover(hover);
        return specialFarePersuasion;
    }

    public PersuasionResponse buildConfirmationTextPersuasion(boolean isMyBizNewDetailsPage) {
        PersuasionResponse confirmationTextPersuasion = new PersuasionResponse();
        confirmationTextPersuasion.setId(BOOKING_CONFIRMATION_TEXT);
        String persuasionText = isMyBizNewDetailsPage ? polyglotService.getTranslatedData(ConstantsTranslation.BOOKING_CONFIRMATION_TEXT_NEW_DETAILS_DESKTOP) : polyglotService.getTranslatedData(ConstantsTranslation.BOOKING_CONFIRMATION_TEXT_DESKTOP);
        persuasionText = StringUtils.replace(persuasionText, NO_OF_HOURS_PLACEHOLDER, String.valueOf(noOfHoursForConfirmation));
        confirmationTextPersuasion.setPersuasionText(persuasionText);
        return confirmationTextPersuasion;
    }


}