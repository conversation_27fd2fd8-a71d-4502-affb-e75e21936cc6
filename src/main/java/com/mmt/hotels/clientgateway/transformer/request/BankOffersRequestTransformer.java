package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.BankOffersRequestCG;
import com.mmt.hotels.clientgateway.transformer.factory.MobLandingFactory;
import com.mmt.hotels.pojo.request.landing.BankOffersRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public abstract class BankOffersRequestTransformer {

    @Autowired
    MobLandingFactory mobLandingFactory;

    public BankOffersRequest convertBankOffersRequest(BankOffersRequestCG request, CommonModifierResponse commonModifierResponse) {
        BankOffersRequest bankOffersRequest = new BankOffersRequest();
        mobLandingFactory.getRequestService(request.getClient()).convertMobLandingRequest(bankOffersRequest, request, commonModifierResponse);
        bankOffersRequest.setDeltaDays(request.getDeltaDays());
        return bankOffersRequest;
    }

}
