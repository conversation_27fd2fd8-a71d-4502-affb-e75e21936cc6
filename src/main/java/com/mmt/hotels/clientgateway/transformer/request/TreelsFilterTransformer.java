package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.ListingSearchRequestV2;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.mmt.hotels.clientgateway.constants.ControllerConstants.LISTING_SEARCH_HOTELS;

@Component
public class TreelsFilterTransformer extends BaseSearchRequestTransformer {

    @Autowired
    private MetricAspect metricAspect;

    public SearchWrapperInputRequest convertSearchRequest(
            ListingSearchRequestV2 listingSearchRequest, CommonModifierResponse commonModifierResponse) {
        SearchWrapperInputRequest searchWrapperInputRequest = new SearchWrapperInputRequest();
        long start = System.currentTimeMillis();
        try {
            searchWrapperInputRequest.setCorrelationKey(listingSearchRequest.getCorrelationKey());
            buildSearchCriteria(searchWrapperInputRequest, listingSearchRequest.getSearchCriteria(), listingSearchRequest.getFilterCriteria(), listingSearchRequest.getRequestDetails(), listingSearchRequest.getExpDataMap());
            searchWrapperInputRequest.setExperimentData(listingSearchRequest.getExpData());
            searchWrapperInputRequest.setValidExpList(listingSearchRequest.getValidExpList());
            searchWrapperInputRequest.setVariantKeys(listingSearchRequest.getVariantKeys());
            searchWrapperInputRequest.setRequestIdentifier(utility.buildRequestIdentifier(listingSearchRequest.getRequestDetails()));
            if (CollectionUtils.isNotEmpty(listingSearchRequest.getFilterRemovedCriteria())) {
                searchWrapperInputRequest.setBuildTreelsExploreMoreData(true);
            }
            if(listingSearchRequest!=null && listingSearchRequest.getRequestDetails()!=null) {
                searchWrapperInputRequest.setPageContext(listingSearchRequest.getRequestDetails().getPageContext());
                searchWrapperInputRequest.setSiteDomain(listingSearchRequest.getRequestDetails().getSiteDomain());
            }
            searchWrapperInputRequest.setMcid(commonModifierResponse.getMcId());
            searchWrapperInputRequest.setMmtAuth(commonModifierResponse.getMmtAuth());
            searchWrapperInputRequest.setAuthToken(commonModifierResponse.getMmtAuth());
            searchWrapperInputRequest.setUserId(commonModifierResponse.getMmtAuth());
            searchWrapperInputRequest.setDomain(commonModifierResponse.getDomain());
            searchWrapperInputRequest.setUserLocation(commonModifierResponse.getUserLocation());
            if (commonModifierResponse.getExtendedUser() != null) {
                searchWrapperInputRequest.setUUID(commonModifierResponse.getExtendedUser().getUuid());
                searchWrapperInputRequest.setProfileType(commonModifierResponse.getExtendedUser().getProfileType());
                searchWrapperInputRequest.setSubProfileType(commonModifierResponse.getExtendedUser().getAffiliateId());
                searchWrapperInputRequest.setCorpUserID(commonModifierResponse.getExtendedUser().getProfileId());
            }
            if (listingSearchRequest.getLimit() != null) {
                searchWrapperInputRequest.setLimit(Integer.valueOf(listingSearchRequest.getLimit()));
            }
        } finally {
            // TODO: why is this logged in metric?
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_SEARCH_REQUEST_PROCESS, LISTING_SEARCH_HOTELS, System.currentTimeMillis() - start);
        }
        /* NearByHotels Request */

        return searchWrapperInputRequest;
    }
}
