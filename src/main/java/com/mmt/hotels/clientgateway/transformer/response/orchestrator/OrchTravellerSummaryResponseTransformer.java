package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.HotelPersuasionData;
import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.PersuasionValue;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.ReviewDescription;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.SortingCriteria;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.SubConcept;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.TopicRating;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.TravellerReviewSummary;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.ReviewCategoryConstants;
import com.mmt.hotels.clientgateway.response.UGCSummary;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.ReArchUtility;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.request.flyfish.ReviewSummaryFeedback;
import com.mmt.hotels.model.request.flyfish.ReviewSummaryListItem;
import com.mmt.hotels.model.request.flyfish.SeekTagDetailsDTO;
import com.mmt.hotels.model.request.flyfish.SeekTagTopicSummaryDTO;
import com.mmt.hotels.model.response.flyfish.CountryWiseReviewCount;
import com.mmt.hotels.model.response.flyfish.CountryWiseReviewData;
import com.mmt.hotels.model.response.flyfish.ManualPersuasion;
import com.mmt.hotels.model.response.flyfish.PlatformTopicRatings;
import com.mmt.hotels.model.response.flyfish.RatingHighlight;
import com.mmt.hotels.model.response.flyfish.ReviewDescriptionDTO;
import com.mmt.hotels.model.response.flyfish.ReviewSortingCriterionListDTO;
import com.mmt.hotels.model.response.flyfish.ReviewSummaryDisclaimer;
import com.mmt.hotels.model.response.flyfish.SubConceptDTO;
import com.mmt.hotels.model.response.flyfish.UGCPlatformReviewSummaryDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.COMBINED_OTA;
import static com.mmt.hotels.clientgateway.constants.Constants.COUNTRY_WISE_REVIEW_TEXT_COLOR;
import static com.mmt.hotels.clientgateway.constants.Constants.NATIONALITY;
import static com.mmt.hotels.clientgateway.constants.Constants.NATIONALITY_MAP;
import static com.mmt.hotels.clientgateway.constants.Constants.REVIEW_COUNT;
import static com.mmt.hotels.clientgateway.constants.Constants.REVIEW_RATING_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.COUNTRY_WISE_REVIEW_CTA_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.COUNTRY_WISE_REVIEW_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.REVIEW_SUMMARY_BATHROOM_REVIEW;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.REVIEW_SUMMARY_FEEDBACK_LABEL;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.REVIEW_SUMMARY_FEEDBACK_TOAST;

@Component
public class OrchTravellerSummaryResponseTransformer {

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    private ReArchUtility utility;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    @Value("${business.rating.icon.url}")
    private String businessRatingIconUrl;

    public UGCPlatformReviewSummaryDTO convertSummaryResponse(TravellerReviewSummary reviewSummary) {
        UGCSummary ugcSummary = mapUgcSummary(reviewSummary, new HashMap<>(), reviewSummary.getCountryCode());
        return ugcSummary != null ? ugcSummary.getData() :  null;
    }

    public UGCSummary mapUgcSummary(TravellerReviewSummary summary, Map<String, String> expDataMap, String countryCode) {
        UGCSummary ugcSummary = buildUgcReviewSummary(summary);
        if (ugcSummary == null) {
            return null;
        }

        UGCPlatformReviewSummaryDTO data = ugcSummary.getData();
        boolean combineOTASupported = utility.isExperimentOn(expDataMap, COMBINED_OTA);
        if (data != null) {
            if (!Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode)) {
                data.setRatingBreakup(null);
            }
            if (!combineOTASupported && summary.getSource() != null &&
                    (OTA.BKG.name().equalsIgnoreCase(summary.getSource().name()) ||
                            OTA.MMT_BKG.name().equalsIgnoreCase(summary.getSource().name()) ||
                            OTA.MMT_EXP.name().equalsIgnoreCase(summary.getSource().name()))) {
                data.setSource(OTA.MMT);
            }
            if (summary.getSelectedCategory() != null && summary.getSelectedCategory().equals(ReviewCategoryConstants.BUSINESS.name())) {
                data.setSelectedCategoryIcon(businessRatingIconUrl);
            }

            data.setCountryWiseReviewData(buildCountryWiseData(data));
        }
        return ugcSummary;
    }

    public CountryWiseReviewData buildCountryWiseData(UGCPlatformReviewSummaryDTO summary) {
        if (summary.getCountryWiseReviewCount() != null && StringUtils.isNotBlank(summary.getCountryWiseReviewCount().getCountry())) {
            String nationality = NATIONALITY_MAP.get(summary.getCountryWiseReviewCount().getCountry());

            if (StringUtils.isNotBlank(nationality)) {
                int reviewCount = summary.getCountryWiseReviewCount().getReviewCount();
                String reviewText = polyglotService.getTranslatedData(COUNTRY_WISE_REVIEW_TEXT);
                String reviewCtaText = polyglotService.getTranslatedData(COUNTRY_WISE_REVIEW_CTA_TEXT);

                if (StringUtils.isBlank(reviewText)) {
                    return null;
                }

                reviewText = reviewText.replace(REVIEW_COUNT, Integer.toString(reviewCount)).replace(NATIONALITY, nationality);
                if (StringUtils.isNotBlank(reviewCtaText)) {
                    reviewCtaText = reviewCtaText.replace(REVIEW_COUNT, Integer.toString(reviewCount)).replace(NATIONALITY, nationality);
                }

                CountryWiseReviewData countryWiseReviewData = new CountryWiseReviewData();
                countryWiseReviewData.setText(reviewText);
                countryWiseReviewData.setColor(COUNTRY_WISE_REVIEW_TEXT_COLOR);
                countryWiseReviewData.setCtaText((reviewCtaText));
                return countryWiseReviewData;
            }
        }
        return null;
    }

    public UGCSummary buildUgcReviewSummary(com.gommt.hotels.orchestrator.detail.model.response.ugc.TravellerReviewSummary summary) {
        if (summary == null) {
            return null;
        }

        UGCSummary ugcSummary = new UGCSummary();

        // 1. Set card title - using a default title for now
        ugcSummary.setCardTitle(REVIEW_RATING_TITLE);

        // 3. Populate UGCPlatformReviewSummaryDTO
        UGCPlatformReviewSummaryDTO platformData = new UGCPlatformReviewSummaryDTO();

        // Map source OTA
        if (summary.getSource() != null) {
            try {
                platformData.setSource(OTA.valueOf(summary.getSource().name()));
            } catch (IllegalArgumentException e) {
                // Set default or skip if enum doesn't match
            }
        }

        platformData.setTravelTypeList(summary.getTravelTypeList());

        // Map available OTAs
        if (summary.getAvailableOTAs() != null && !summary.getAvailableOTAs().isEmpty()) {
            List<OTA> availableOtaList = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.enums.OTA orchestratorOta : summary.getAvailableOTAs()) {
                try {
                    OTA clientOta = OTA.valueOf(orchestratorOta.name());
                    availableOtaList.add(clientOta);
                } catch (IllegalArgumentException e) {
                    // Skip if OTA enum value doesn't exist
                }
            }
            platformData.setAvailableOTAs(availableOtaList);
        }

        // Map basic fields
        platformData.setShowUpvote(summary.isShowUpvote());
        platformData.setCrawledData(summary.isCrawledData());
        platformData.setDisableLowRating(summary.isDisableLowRating());
        platformData.setChatGPTSummaryExists(summary.isChatGPTSummaryExists());
        platformData.setPreferredOTA(summary.isPreferredOTA());
        if (summary.isNewListing()) platformData.setIsNewListing(summary.isNewListing());

        // Map review and rating counts
        platformData.setReviewCount(summary.getReviewCount());
        platformData.setRatingCount(summary.getRatingCount());
        platformData.setTotalReviewsCount(summary.getTotalReviewCount());
        platformData.setTotalRatingCount(summary.getTotalRatingCount());

        List<ReviewSortingCriterionListDTO> sortingCriterionListDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(summary.getSortingCriteriaList())) {
            for (SortingCriteria sortingCriteria : summary.getSortingCriteriaList()) {
                ReviewSortingCriterionListDTO reviewSortingCriterionListDTO = new ReviewSortingCriterionListDTO();
                reviewSortingCriterionListDTO.setCriteriaType(sortingCriteria.getCriteriaType());
                reviewSortingCriterionListDTO.setDisplayText(sortingCriteria.getDisplayText());
                sortingCriterionListDTOList.add(reviewSortingCriterionListDTO);
            }
        }

        platformData.setSortingCriterionList(sortingCriterionListDTOList);

        // Map MMT review count
        if (summary.getMmtReviewCount() > 0) {
            platformData.setMmtReviewCount(summary.getMmtReviewCount());
        }

        // Map rating data
        platformData.setCumulativeRating(summary.getCumulativeRating());
        platformData.setRatingText(summary.getRatingText());
        platformData.setRecentRatings(summary.getRecentRatings() != null ?
                summary.getRecentRatings().stream().map(Float::intValue).collect(Collectors.toList()) : null);

        // Map rated text and icon
        platformData.setRatedText(summary.getRatedText());
        platformData.setRatedIcon(summary.getRatedIcon());

        // Map high rated topics
        platformData.setHighRatedTopic(summary.getHighRatedTopic());

        // Map best review title
        platformData.setBestReviewTitle(summary.getBestReviewTitle());

        // Map breakup maps
        platformData.setRatingBreakup(summary.getRatingBreakup());
        platformData.setReviewBreakup(summary.getReviewBreakup());

        // Map travel types and selected category
        platformData.setTravelTypes(summary.getTravelTypes());
        platformData.setSelectedCategory(summary.getSelectedCategory());

        // Map sorting criterion
        platformData.setSortingCriterion(summary.getSortingCriterion());

        // Map best reviews
        if (summary.getBestReviews() != null && !summary.getBestReviews().isEmpty()) {
            List<ReviewDescriptionDTO> bestReviewDTOs = new ArrayList<>();
            for (ReviewDescription review : summary.getBestReviews()) {
                ReviewDescriptionDTO reviewDTO = new ReviewDescriptionDTO();
                reviewDTO.setId(review.getId());
                reviewDTO.setTitle(review.getTitle());
                reviewDTO.setReviewText(review.getReviewText());
                reviewDTO.setRating(review.getRating());
                reviewDTO.setTravellerName(review.getTravellerName());
                reviewDTO.setPublishDate(review.getPublishDate());
                reviewDTO.setCheckinDate(review.getCheckinDate());
                reviewDTO.setCheckoutDate(review.getCheckoutDate());
                reviewDTO.setUpvoted(review.isUpvote());
                reviewDTO.setLogo(review.getLogo());
                reviewDTO.setTravelType(review.getTravelType());
                // Note: TravelType is not available in orchestrator ReviewDescription
                bestReviewDTOs.add(reviewDTO);
            }
            platformData.setBest(bestReviewDTOs);
        }

        // Map topic ratings to hotel rating summary
        if (CollectionUtils.isNotEmpty(summary.getTopicRatings())) {
            List<PlatformTopicRatings> platformTopicRatings = new ArrayList<>();
            for (TopicRating topicRating : summary.getTopicRatings()) {
                PlatformTopicRatings platformRating = new PlatformTopicRatings();
                platformRating.setConcept(topicRating.getConcept());
                platformRating.setDisplayText(topicRating.getDisplayText() != null ?
                        topicRating.getDisplayText() : topicRating.getTitle());
                platformRating.setValue(topicRating.getValue() != 0 ?
                        topicRating.getValue() : topicRating.getRating());
                platformRating.setShow(topicRating.isShow());
                platformRating.setReviewCount(topicRating.getReviewCount());
                platformRating.setHeroTag(topicRating.isHeroTag());

                platformTopicRatings.add(platformRating);
            }
            platformData.setHotelRatingSummary(platformTopicRatings);
        }

        // Map sub concepts
        if (summary.getSubConcepts() != null && !summary.getSubConcepts().isEmpty()) {
            List<SubConceptDTO> subConceptDTOs = new ArrayList<>();
            for (SubConcept subConcept : summary.getSubConcepts()) {
                SubConceptDTO subConceptDTO = new SubConceptDTO();
                subConceptDTO.setSubConcept(subConcept.getSubConcept());
                subConceptDTO.setSentiment(subConcept.getSentiment());
                subConceptDTO.setRelatedReviewCount(subConcept.getRelatedReviewCount());
                subConceptDTO.setPriorityScore(subConcept.getPriorityScore());
                subConceptDTO.setDisplayText(subConcept.getDisplayText());
                // Set source and tagType from orchestrator if available
                if (subConcept.getSource() != null) {
                    subConceptDTO.setSource(subConcept.getSource());
                }
                if (subConcept.getTagType() != null) {
                    subConceptDTO.setTagType(subConcept.getTagType());
                }
                subConceptDTOs.add(subConceptDTO);
            }
            platformData.setSubConcepts(subConceptDTOs);
        }

        // Map manual persuasion
        if (summary.getManualPersuasion() != null) {
            ManualPersuasion manualPersuasion = new ManualPersuasion();
            manualPersuasion.setIconUrl(summary.getManualPersuasion().getIconUrl());
            manualPersuasion.setText(summary.getManualPersuasion().getText());
            platformData.setManualPersuasion(manualPersuasion);
        }

        // Map rating highlight
        if (summary.getRatingHighlight() != null && summary.getRatingHighlight().getRating() != null && summary.getRatingHighlight().getRating() != 0) {
            RatingHighlight ratingHighlight = new RatingHighlight();
            ratingHighlight.setRating(summary.getRatingHighlight().getRating());
            ratingHighlight.setTitle(summary.getRatingHighlight().getTitle());
            ratingHighlight.setText(summary.getRatingHighlight().getText());
            platformData.setRatingHighlight(ratingHighlight);
        }

        // Map disclaimer
        if (summary.getDisclaimer() != null) {
            ReviewSummaryDisclaimer disclaimer = new ReviewSummaryDisclaimer();
            disclaimer.setFull(summary.getDisclaimer().getFull());
            disclaimer.setSmall(summary.getDisclaimer().getSmall());
            platformData.setDisclaimer(disclaimer);
        }

        // Map seek tag details
        if (summary.getSeekTagDetails() != null) {
            SeekTagDetailsDTO seekTagDetails = new SeekTagDetailsDTO();
            seekTagDetails.setSeekTagsTitle(summary.getSeekTagDetails().getTitle());
            seekTagDetails.setSeekTagsSubtitle(summary.getSeekTagDetails().getSubtitle());
            seekTagDetails.setSeekTagIcon(summary.getSeekTagDetails().getIcon());
            seekTagDetails.setSeekTagSummary(summary.getSeekTagDetails().getSummary());
            seekTagDetails.setSeekTagSpans(summary.getSeekTagDetails().getSpans());

            if (summary.getSeekTagDetails().getMaxSeekTagCount() != null) {
                seekTagDetails.setMaxSeekTagCount(summary.getSeekTagDetails().getMaxSeekTagCount());
            }
            if (summary.getSeekTagDetails().getDefaultSeekTagCount() != null) {
                seekTagDetails.setDefaultSeekTagCount(summary.getSeekTagDetails().getDefaultSeekTagCount());
            }

            // Map topic summary list
            if (summary.getSeekTagDetails().getTopicSummary() != null &&
                    !summary.getSeekTagDetails().getTopicSummary().isEmpty()) {
                List<SeekTagTopicSummaryDTO> topicSummaryList = new ArrayList<>();
                for (com.gommt.hotels.orchestrator.detail.model.response.ugc.SeekTagDetails.SeekTagTopicSummary topicSummary :
                        summary.getSeekTagDetails().getTopicSummary()) {
                    SeekTagTopicSummaryDTO mappedSummary =
                            new SeekTagTopicSummaryDTO();
                    mappedSummary.setConcept(topicSummary.getConcept());
                    mappedSummary.setSummary(topicSummary.getSummary());
                    topicSummaryList.add(mappedSummary);
                }
                seekTagDetails.setSeekTagTopicSummary(topicSummaryList);
            }

            if(summary.getPersuasionMap() != null && summary.getPersuasionMap().get("UGC_WASHROOM") != null){
                HotelPersuasionData washroomData = summary.getPersuasionMap().get("UGC_WASHROOM");
                List<PersuasionValue> value = washroomData.getData();
                if(CollectionUtils.isNotEmpty(value)){

                    // Review Summary List
                    List<String> values = value.stream()
                            .map(PersuasionValue::getText)
                            .collect(Collectors.toList());
                    String reviewSummaryText = polyglotService.getTranslatedData(REVIEW_SUMMARY_BATHROOM_REVIEW);
                    if(reviewSummaryText != null){
                        reviewSummaryText = reviewSummaryText.replace("{summary}",String.join(", ", values));
                    }

                    // Review Summary Persuasion IDs
                    List<String> persuasionIDs = value.stream()
                            .map(PersuasionValue::getId)
                            .collect(Collectors.toList());
                    String trackingStr = String.join("_", persuasionIDs);

                    ReviewSummaryListItem summaryListItem = new ReviewSummaryListItem();
                    summaryListItem.setSummary(reviewSummaryText);
                    summaryListItem.setType("UGC_WASHROOM");
                    summaryListItem.setImageTag(getReviewSummaryMediaTag());
                    summaryListItem.setTrackingStr(trackingStr);

                    ArrayList<ReviewSummaryListItem> list = new ArrayList<>();
                    list.add(summaryListItem);
                    seekTagDetails.setReviewsSummaryList(list);

                    // Feedback
                    ReviewSummaryFeedback feedback = new ReviewSummaryFeedback();
                    feedback.setLabel(polyglotService.getTranslatedData(REVIEW_SUMMARY_FEEDBACK_LABEL));
                    feedback.setToast(polyglotService.getTranslatedData(REVIEW_SUMMARY_FEEDBACK_TOAST));
                    seekTagDetails.setFeedback(feedback);

                }
            }

            platformData.setSeekTagDetails(seekTagDetails);
        }

        if (summary.getCountryWiseReviewData() != null) {
            CountryWiseReviewCount countryWiseReviewCount = new CountryWiseReviewCount();
            countryWiseReviewCount.setCountry(summary.getCountryWiseReviewData().getCountry());
            countryWiseReviewCount.setReviewCount(summary.getCountryWiseReviewData().getReviewCount());
            platformData.setCountryWiseReviewCount(countryWiseReviewCount);
        }

        // Set the populated platform data
        ugcSummary.setData(platformData);

        return ugcSummary;
    }

    public String getReviewSummaryMediaTag() {
        String tag = commonConfigConsul.getBathroomReviewSummaryMediaTag();
        if (StringUtils.isEmpty(tag)) {
            tag = "Washroom";
        }
        return tag;
    }
}
