package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.fasterxml.jackson.databind.JsonNode;
import com.gommt.hotels.orchestrator.detail.model.response.content.HotelMetaData;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.staticdata.StaffInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.HIDDEN_GEM;
import static com.mmt.hotels.clientgateway.constants.Constants.LUXE_ICON_APPS;
import static com.mmt.hotels.clientgateway.constants.Constants.MMT_VALUE_STAYS;

@Component
public class OrchStaticDetailsResponseTransformerAndroid extends OrchStaticDetailResponseTransformer {

    @Value("${star.host.icon.app}")
    private String starHostIconApp;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    @Autowired
    PolyglotService polyglotService;

    @Autowired
    private Utility utility;

    Map<String, String> cardTitleMap;

    private static final Logger LOGGER = LoggerFactory.getLogger(OrchStaticDetailsResponseTransformerAndroid.class);

    @PostConstruct
    public void init() {
        try {
            cardTitleMap = commonConfigConsul.getCardTitleMap();
            LOGGER.debug("Fetching values from commonConfig consul");
        } catch (Exception ex) {
            LOGGER.error("error in fetching commonConfig pms properties", ex);
        }
    }

    @Override
    public Map<String, String> buildCardTitleMap() {
        Map<String, String> cardTitleMapAndroid = null;
        try {
            if (MapUtils.isNotEmpty(cardTitleMap)) {
                cardTitleMapAndroid = cardTitleMap.entrySet().stream().collect(Collectors.toMap(e -> e.getKey(), e -> polyglotService.getTranslatedData(e.getValue())));
            }
        } catch (Exception e) {
            LOGGER.error("Error while creating cardTitleMap for android", e);
        }
        return cardTitleMapAndroid;
    }

    @Override
    public void addTitleData(HotelResult hotelResult, String countryCode, boolean isNewDetailPageDesktop) {}

    @Override
    public String getLuxeIcon() {
        return LUXE_ICON_APPS;
    }

    @Override
    public StaffInfo convertStaffInfo(com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffInfo staffInfo) {
        // Always return a StaffInfo object, even if some data is null
        if (staffInfo == null) {
            return null;
        }

        // Set star host icon if needed before calling parent
        if (BooleanUtils.isTrue(staffInfo.getIsStarHost())) {
            staffInfo.setStarHostIconUrl(starHostIconApp);
        }
        removeIcon(staffInfo);

        // Create a new StaffInfo object regardless of null fields
        com.mmt.hotels.model.response.staticdata.StaffInfo staffInfoCg = new com.mmt.hotels.model.response.staticdata.StaffInfo();

        // Map basic fields
        staffInfoCg.setIsStarHost(staffInfo.getIsStarHost());
        staffInfoCg.setStarHostIconUrl(staffInfo.getStarHostIconUrl());
        staffInfoCg.setChatEnabled(staffInfo.getChatEnabled());
        staffInfoCg.setResponseTime(staffInfo.getResponseTime());
        staffInfoCg.setStarHostReasons(staffInfo.getStarHostReasons());

        // Map host Staff if available
        if (staffInfo.getHost() != null) {
            // Create a simplified mapping since the parent method is too strict
            com.mmt.hotels.model.response.staticdata.Staff hostStaff = new com.mmt.hotels.model.response.staticdata.Staff();
            hostStaff.setHeader(staffInfo.getHost().getHeader());
            if (staffInfo.getHost().getData() != null && !staffInfo.getHost().getData().isEmpty()) {
                // Map basic staff data without full validation
                List<com.mmt.hotels.model.response.staticdata.StaffData> staffDataList = new ArrayList<>();
                for (com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffData staffData : staffInfo.getHost().getData()) {
                    if (staffData != null) {
                        com.mmt.hotels.model.response.staticdata.StaffData staffDataCg = new com.mmt.hotels.model.response.staticdata.StaffData();
                        staffDataCg.setType(staffData.getType());
                        staffDataCg.setHeading(staffData.getHeading());
                        staffDataCg.setName(staffData.getName());
                        staffDataCg.setGender(staffData.getGender());
                        staffDataCg.setAge(staffData.getAge());
                        staffDataCg.setProfilePicUrl(staffData.getProfilePicUrl());
                        staffDataCg.setAbout(staffData.getAbout());
                        staffDataList.add(staffDataCg);
                    }
                }
                hostStaff.setData(staffDataList);
            }
            staffInfoCg.setHost(hostStaff);
        }

        // Map caretaker Staff if available
        if (staffInfo.getCaretaker() != null) {
            com.mmt.hotels.model.response.staticdata.Staff caretakerStaff = new com.mmt.hotels.model.response.staticdata.Staff();
            caretakerStaff.setHeader(staffInfo.getCaretaker().getHeader());
            if (staffInfo.getCaretaker().getData() != null && !staffInfo.getCaretaker().getData().isEmpty()) {
                List<com.mmt.hotels.model.response.staticdata.StaffData> staffDataList = new ArrayList<>();
                for (com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffData staffData : staffInfo.getCaretaker().getData()) {
                    if (staffData != null) {
                        com.mmt.hotels.model.response.staticdata.StaffData staffDataCg = new com.mmt.hotels.model.response.staticdata.StaffData();
                        staffDataCg.setType(staffData.getType());
                        staffDataCg.setHeading(staffData.getHeading());
                        staffDataCg.setName(staffData.getName());
                        staffDataCg.setGender(staffData.getGender());
                        staffDataCg.setAge(staffData.getAge());
                        staffDataCg.setProfilePicUrl(staffData.getProfilePicUrl());
                        staffDataCg.setAbout(staffData.getAbout());
                        staffDataList.add(staffDataCg);
                    }
                }
                caretakerStaff.setData(staffDataList);
            }
            staffInfoCg.setCaretaker(caretakerStaff);
        }

        // Map cook Staff if available
        if (staffInfo.getCook() != null) {
            com.mmt.hotels.model.response.staticdata.Staff cookStaff = new com.mmt.hotels.model.response.staticdata.Staff();
            cookStaff.setHeader(staffInfo.getCook().getHeader());
            if (staffInfo.getCook().getData() != null && !staffInfo.getCook().getData().isEmpty()) {
                List<com.mmt.hotels.model.response.staticdata.StaffData> staffDataList = new ArrayList<>();
                for (com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffData staffData : staffInfo.getCook().getData()) {
                    if (staffData != null) {
                        com.mmt.hotels.model.response.staticdata.StaffData staffDataCg = new com.mmt.hotels.model.response.staticdata.StaffData();
                        staffDataCg.setType(staffData.getType());
                        staffDataCg.setHeading(staffData.getHeading());
                        staffDataCg.setName(staffData.getName());
                        staffDataCg.setGender(staffData.getGender());
                        staffDataCg.setAge(staffData.getAge());
                        staffDataCg.setProfilePicUrl(staffData.getProfilePicUrl());
                        staffDataCg.setAbout(staffData.getAbout());
                        staffDataList.add(staffDataCg);
                    }
                }
                cookStaff.setData(staffDataList);
            }
            staffInfoCg.setCook(cookStaff);
        }

        return staffInfoCg;
    }

    public String buildCategoryIcon(HotelMetaData hotelMetaData, boolean isCorpIdContext, Map<String, String> expDataMap) {
        // Check for null PropertyDetails to avoid NPE
        if (hotelMetaData == null || hotelMetaData.getPropertyDetails() == null) {
            return null;
        }

        if (CollectionUtils.isNotEmpty(hotelMetaData.getPropertyDetails().getCategories())
                && hotelMetaData.getPropertyDetails().getCategories().contains(MMT_VALUE_STAYS) && !isCorpIdContext) {
            return "https://promos.makemytrip.com/Growth/Images/B2C/mmt_vs_details.png";
        }

        if (utility.hasHiddenGemExpEnabled(expDataMap) && CollectionUtils.isNotEmpty(hotelMetaData.getPropertyDetails().getCategories())
                && hotelMetaData.getPropertyDetails().getCategories().contains(HIDDEN_GEM)) {
            return "https://promos.makemytrip.com/Growth/Images/B2C/mmt_hg_details.png";
        }

        return null;

//        // if hotel is MyPartner GST Assured, set categoryIcon in response
//        if (hotelResult.isGstAssured()) {
//            finalResponse.getHotelResult().setCategoryIcon(myPartnerGstAssuredCategoryIcon);
//        }
    }

    @Override
    public JsonNode buildWeaverResponse(JsonNode weaverResponse) {
        utility.updateWeaverResponse(weaverResponse);
        return weaverResponse;
    }

}
