package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.Rooms;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.FlexiCancelStaticDetail;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomBanner;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.FLEXI_CANCEL;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.AMENITY_1;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.AMENITY_2;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.AMENITY_3;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FLEXI_CANCEL_DETAIL_BANNER_DESC;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FLEXI_CANCEL_DETAIL_BANNER_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FLEXI_CANCEL_LEAN_MORE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.ROOM_NAME;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SELECT_ROOM_1_AMENITIES_BANNER;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SELECT_ROOM_2_AMENITIES_BANNER;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SELECT_ROOM_3_AMENITIES_BANNER;

@Component
public class SearchRoomsBannerHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(SearchRoomsBannerHelper.class);

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    private FlexiCancelStaticDetail flexiCancelStaticDetail;


    @PostConstruct
    public void init() {
        flexiCancelStaticDetail = commonConfigConsul.getFlexiCancelStaticDetail();
    }


    // Legacy buildBanner method and helper methods removed
    // These were causing compilation errors due to non-existent classes (Banner, Room, FlexiCancelDetails) in OrchV2
    // The correct OrchV2 implementation is buildBanner(SearchRoomsResponse, HotelDetails)

    public SelectRoomBanner buildBanner(SearchRoomsResponse searchRoomsResponse, HotelDetails hotelDetails) {
        if (hotelDetails != null && searchRoomsResponse != null) {
            SelectRoomBanner banner = new SelectRoomBanner();
            boolean hasFlexiCancel = false;
            String flexiCancelBannerTitle = null;

            // Check for FlexiCancel in recommended combos first (from hotelDetails.roomCombos)
            if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos()) &&
                    CollectionUtils.isNotEmpty(hotelDetails.getRoomCombos())) {
                hasFlexiCancel = isHasFlexiCancelInRoomCombos(hotelDetails.getRoomCombos());
                flexiCancelBannerTitle = getFlexiCancelBannerTitleFromRoomCombos(hotelDetails.getRoomCombos());
            }

            // Check for FlexiCancel in exact rooms (from hotelDetails.rooms)
            if (CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms()) &&
                    CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {

                if (!hasFlexiCancel) {
                    hasFlexiCancel = isHasFlexiCancelInOrchV2Rooms(hotelDetails.getRooms());
                    flexiCancelBannerTitle = getFlexiCancelBannerTitleFromOrchV2Rooms(hotelDetails.getRooms());
                }

                // Build FlexiCancel banner if available
                if (hasFlexiCancel && StringUtils.isNotBlank(flexiCancelBannerTitle)) {
                    makeBanner(banner);
                    banner.setDescription(flexiCancelBannerTitle);
                    return banner;
                }

                // Check for room amenities banner (fallback) from hotelDetails.rooms - In old flow this code is never executing, since selected amenities never come.
                /*
                String amenityBannerText = buildAmenityBannerFromRooms(hotelDetails.getRooms(),
                        searchRoomsResponse.getExactRooms().get(0).getRoomCode());
                if (StringUtils.isNotBlank(amenityBannerText)) {
                    banner.setTitle(amenityBannerText);
                    banner.setRedirectType(Constants.SELECT_ROOM_BANNER_TYPE);
                    banner.setIconUrl(Constants.SELECT_ROOM_BANNER_ICON_URL);
                    banner.setBgColor(Constants.SELECT_ROOM_BANNER_BG_COLOR);
                    return banner;
                }*/
            }
            // Check for occupancy rooms (from hotelDetails.rooms)
            else if (CollectionUtils.isNotEmpty(searchRoomsResponse.getOccupancyRooms()) &&
                    CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {

                String amenityBannerText = buildAmenityBannerFromRooms(hotelDetails.getRooms(),
                        searchRoomsResponse.getOccupancyRooms().get(0).getRoomCode());
                if (StringUtils.isNotBlank(amenityBannerText)) {
                    banner.setTitle(amenityBannerText);
                    banner.setRedirectType(Constants.SELECT_ROOM_BANNER_TYPE);
                    banner.setIconUrl(Constants.SELECT_ROOM_BANNER_ICON_URL);
                    banner.setBgColor(Constants.SELECT_ROOM_BANNER_BG_COLOR);
                    return banner;
                }
            }
            // Check for recommended combos (from hotelDetails.roomCombos)
            else if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos()) &&
                    CollectionUtils.isNotEmpty(hotelDetails.getRoomCombos())) {

                if (hasFlexiCancel && StringUtils.isNotBlank(flexiCancelBannerTitle)) {
                    makeBanner(banner);
                    banner.setDescription(flexiCancelBannerTitle);
                    return banner;
                }

                // Check for room amenities banner from room combos - In old flow this code is never executing, since selected amenities never come.
                /*String amenityBannerText = buildAmenityBannerFromRoomCombos(hotelDetails.getRoomCombos(),
                        searchRoomsResponse.getRecommendedCombos().get(0).getRooms().get(0).getRoomCode());
                if (StringUtils.isNotBlank(amenityBannerText)) {
                    banner.setTitle(amenityBannerText);
                    banner.setRedirectType(Constants.SELECT_ROOM_BANNER_TYPE);
                    banner.setIconUrl(Constants.SELECT_ROOM_BANNER_ICON_URL);
                    banner.setBgColor(Constants.SELECT_ROOM_BANNER_BG_COLOR);
                    return banner;
                }*/
            }
        }
        return null;
    }

    private boolean isHasFlexiCancelInOrchV2Rooms(List<Rooms> rooms) {
        return rooms.stream()
                .filter(Objects::nonNull)
                .filter(room -> room.getRatePlans() != null)
                .flatMap(room -> room.getRatePlans().stream())
                .anyMatch(this::hasFlexiCancelInOrchV2RatePlan);
    }

    private String getFlexiCancelBannerTitleFromOrchV2Rooms(List<Rooms> rooms) {
        return rooms.stream()
                .filter(Objects::nonNull)
                .filter(room -> room.getRatePlans() != null)
                .flatMap(room -> room.getRatePlans().stream())
                .filter(this::hasFlexiCancelInOrchV2RatePlan)
                .map(this::getFlexiCancelTitleFromOrchV2RatePlan)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    /**
     * Check if an OrchV2 rate plan has FlexiCancel available (for hotel rooms).
     * In OrchV2, FlexiCancel information is in ratePlan.getAddOns().getAddOnDetails()
     */
    private boolean hasFlexiCancelInOrchV2RatePlan(RatePlan ratePlan) {
        if (ratePlan == null || ratePlan.getAddOns() == null ||
                ratePlan.getAddOns().getAddOnDetails() == null) {
            return false;
        }

        return ratePlan.getAddOns().getAddOnDetails().stream()
                .filter(Objects::nonNull)
                .anyMatch(addOnDetail -> FLEXI_CANCEL.equals(addOnDetail.getType()) &&
                        addOnDetail.getRemoved() != null &&
                        StringUtils.isNotBlank(addOnDetail.getRemoved().getTitle()));
    }

    /**
     * Get FlexiCancel title from OrchV2 rate plan for banner display (for hotel rooms).
     * In OrchV2, the title is in addOns.addOnDetails[type="FLEXI_CANCEL"].getRemoved().getTitle()
     */
    private String getFlexiCancelTitleFromOrchV2RatePlan(RatePlan ratePlan) {
        if (!hasFlexiCancelInOrchV2RatePlan(ratePlan)) {
            return null;
        }

        return ratePlan.getAddOns().getAddOnDetails().stream()
                .filter(Objects::nonNull)
                .filter(addOnDetail -> FLEXI_CANCEL.equals(addOnDetail.getType()) &&
                        addOnDetail.getRemoved() != null &&
                        StringUtils.isNotBlank(addOnDetail.getRemoved().getTitle()))
                .map(addOnDetail -> addOnDetail.getRemoved().getTitle())
                .findFirst()
                .orElse(null);
    }

    private void makeBanner(SelectRoomBanner banner) {
        banner.setType(FLEXI_CANCEL);
        banner.setTitle(polyglotService.getTranslatedData(FLEXI_CANCEL_DETAIL_BANNER_TITLE));
        banner.setDescription(polyglotService.getTranslatedData(FLEXI_CANCEL_DETAIL_BANNER_DESC));
        banner.setIconUrl(flexiCancelStaticDetail != null ? flexiCancelStaticDetail.getIconUrl() : "");
        banner.setRedirectLink(flexiCancelStaticDetail != null ? flexiCancelStaticDetail.getRedirectUrl() : "");
        banner.setBgImageUrl(flexiCancelStaticDetail != null ? flexiCancelStaticDetail.getBackgroundImage() : "");
        banner.setActionText(polyglotService.getTranslatedData(FLEXI_CANCEL_LEAN_MORE));
    }


    private boolean isHasFlexiCancelInRoomCombos(List<RoomCombo> roomCombos) {
        return roomCombos.stream()
                .filter(Objects::nonNull)
                .filter(combo -> combo.getRooms() != null)
                .flatMap(combo -> combo.getRooms().stream())
                .filter(Objects::nonNull)
                .filter(room -> room.getRatePlans() != null)
                .flatMap(room -> room.getRatePlans().stream())
                .anyMatch(this::hasFlexiCancelInOrchV2RatePlan);
    }

    private String getFlexiCancelBannerTitleFromRoomCombos(List<RoomCombo> roomCombos) {
        return roomCombos.stream()
                .filter(Objects::nonNull)
                .filter(combo -> combo.getRooms() != null)
                .flatMap(combo -> combo.getRooms().stream())
                .filter(Objects::nonNull)
                .filter(room -> room.getRatePlans() != null)
                .flatMap(room -> room.getRatePlans().stream())
                .filter(this::hasFlexiCancelInOrchV2RatePlan)
                .map(this::getFlexiCancelTitleFromOrchV2RatePlan)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    private String buildAmenityBannerFromRooms(List<Rooms> rooms, String excludeRoomCode) {
        if (CollectionUtils.isNotEmpty(rooms)) {
            Optional<Rooms> maxAmenityRoom = rooms.stream()
                    .filter(Objects::nonNull)
                    .filter(room -> room.getRoomInfo() != null &&
                            CollectionUtils.isNotEmpty(room.getRoomInfo().getAmenities()) &&
                            room.getCode() != null &&
                            !room.getCode().equalsIgnoreCase(excludeRoomCode))
                    .max(Comparator.comparingInt(room -> room.getRoomInfo().getAmenities().size()));

            if (maxAmenityRoom.isPresent()) {
                List<String> selectedAmenities = maxAmenityRoom.get().getRoomInfo().getAmenities().stream()
                        .filter(Objects::nonNull)
                        .map(AmenityGroup::getName)
                        .filter(StringUtils::isNotBlank)
                        .limit(3)
                        .collect(Collectors.toList());

                return buildAmenityBannerText(selectedAmenities, maxAmenityRoom.get().getRoomInfo().getRoomName());
            }
        }
        return null;
    }

    private String buildAmenityBannerFromRoomCombos(List<RoomCombo> roomCombos, String excludeRoomCode) {
        if (CollectionUtils.isNotEmpty(roomCombos)) {
            Optional<Rooms> maxAmenityRoom = roomCombos.stream()
                    .filter(Objects::nonNull)
                    .filter(combo -> combo.getRooms() != null)
                    .flatMap(combo -> combo.getRooms().stream())
                    .filter(Objects::nonNull)
                    .filter(room -> room.getRoomInfo() != null &&
                            CollectionUtils.isNotEmpty(room.getRoomInfo().getAmenities()) &&
                            room.getCode() != null &&
                            !room.getCode().equalsIgnoreCase(excludeRoomCode))
                    .max(Comparator.comparingInt(room -> room.getRoomInfo().getAmenities().size()));

            if (maxAmenityRoom.isPresent()) {
                List<String> selectedAmenities = maxAmenityRoom.get().getRoomInfo().getAmenities().stream()
                        .filter(Objects::nonNull)
                        .map(AmenityGroup::getName)
                        .filter(StringUtils::isNotBlank)
                        .limit(3)
                        .collect(Collectors.toList());

                return buildAmenityBannerText(selectedAmenities, maxAmenityRoom.get().getRoomInfo().getRoomName());
            }
        }
        return null;
    }

    private String buildAmenityBannerText(List<String> selectedAmenities, String roomName) {
        if (CollectionUtils.isEmpty(selectedAmenities)) {
            return null;
        }
        
        try {
            String text = StringUtils.EMPTY;
            int amenityCount = selectedAmenities.size();
            String finalRoomName = StringUtils.isNotBlank(roomName) ? roomName : "Room";

            if (amenityCount == 1) {
                text = polyglotService.getTranslatedData(SELECT_ROOM_1_AMENITIES_BANNER)
                        .replace(ROOM_NAME, finalRoomName)
                        .replace(AMENITY_1, selectedAmenities.get(0));
            } else if (amenityCount == 2) {
                text = polyglotService.getTranslatedData(SELECT_ROOM_2_AMENITIES_BANNER)
                        .replace(ROOM_NAME, finalRoomName)
                        .replace(AMENITY_1, selectedAmenities.get(0))
                        .replace(AMENITY_2, selectedAmenities.get(1));
            } else if (amenityCount >= 3) {
                text = polyglotService.getTranslatedData(SELECT_ROOM_3_AMENITIES_BANNER)
                        .replace(ROOM_NAME, finalRoomName)
                        .replace(AMENITY_1, selectedAmenities.get(0))
                        .replace(AMENITY_2, selectedAmenities.get(1))
                        .replace(AMENITY_3, selectedAmenities.get(2));
            }

            return StringUtils.isNotBlank(text) ? text : null;
        } catch (Exception e) {
            LOGGER.error("Error building amenity banner text", e);
            return null;
        }
    }
} 