package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchSearchSlotsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchSearchSlotsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchSearchSlotsResponseTransformerIOS;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SearchSlotsFactory {

    @Autowired
    private OrchSearchSearchSlotsResponseTransformerAndroid orchSearchRoomsResponseTransformerAndroid;

    @Autowired
    private OrchSearchSearchSlotsResponseTransformerIOS orchSearchRoomsResponseTransformerIOS;

    public OrchSearchSearchSlotsResponseTransformer getOrchResponseService(String client) {
        if (StringUtils.isEmpty(client))
            return orchSearchRoomsResponseTransformerAndroid;
        switch (client) {
            case "PWA":
            case "MSITE":
            case "ANDROID":
                return orchSearchRoomsResponseTransformerAndroid;
            case "IOS":
                return orchSearchRoomsResponseTransformerIOS;
        }
        return orchSearchRoomsResponseTransformerAndroid;
    }
}
