package com.mmt.hotels.clientgateway.transformer.response.ios;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.RatePlanFilter;
import com.mmt.hotels.clientgateway.response.rooms.LoginPersuasion;
import com.mmt.hotels.clientgateway.response.wrapper.ShowCheapest;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.HeroTierDetails;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.transformer.response.SearchRoomsResponseTransformer;

import java.util.List;
import java.util.Map;

@Component
public class SearchRoomsResponseTransformerIOS extends SearchRoomsResponseTransformer{

    @Override
    protected PersuasionObject createTopRatedPersuasion(boolean isNewDetailsPageDesktop) {
        return createTopRatedPersuasionForMoblie();
    }
    @Override
    protected LoginPersuasion buildLoginPersuasion() {
        return null;
    }

    @Override
    protected void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap) {

    }

    @Override
    protected void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap, int myPartnerCashback, HeroTierDetails heroTierDetails) {

    }

    @Override
    protected ShowCheapest buildCheapestRatePlan(List<RatePlanFilter> ratePlanFilter) {
        return null;
    }

    @Override
    public PersuasionResponse buildDelayedConfirmationPersuasion(String corpAlias, boolean isMyBizNewDetailsPage) {
        return null;
    }

    @Override
    public PersuasionResponse buildSpecialFareTagPersuasion(String corpAlias) {
        return null;
    }

    @Override
    public PersuasionResponse buildSpecialFareTagWithInfoPersuasion(String corpAlias,boolean isNewSelectRoomPage) {
        return buildSpecialFarePersuasionForMobile(corpAlias,isNewSelectRoomPage);
    }

    @Override
    public PersuasionResponse buildConfirmationTextPersuasion(String corpAlias,boolean isNewSelectRoomPage, boolean isMyBizNewDetailsPage) {
        return buildBookingConfirmationPersuasionForMobile(corpAlias,isNewSelectRoomPage);
    }

    public String getHtml(){
        return Constants.APPS_INCLUSION_HTML;
    }
}
