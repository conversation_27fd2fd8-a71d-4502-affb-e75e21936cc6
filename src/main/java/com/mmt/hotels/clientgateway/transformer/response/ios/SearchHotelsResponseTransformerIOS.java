package com.mmt.hotels.clientgateway.transformer.response.ios;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.Persuasions;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.searchHotels.*;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.transformer.response.SearchHotelsResponseTransformer;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.searchwrapper.ListingPagePersonalizationResponsBO;
import com.mmt.hotels.model.response.searchwrapper.PersonalizedResponse;
import com.mmt.hotels.model.response.searchwrapper.QuickBookInfo;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.propertymanager.config.PropertyManager;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.model.LocusData;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.transformer.response.android.SearchHotelsResponseTransformerAndroid.getQuickBookCardForApps;

@Component
public class SearchHotelsResponseTransformerIOS extends SearchHotelsResponseTransformer {


	@Value("${consul.enable}")
	private boolean consulFlag;
	@Autowired
	CommonConfigConsul commonConfigConsul;

	public static final String IMAGE_TEXT_H1 = "IMAGE_TEXT_H";
	private static final Logger LOGGER = LoggerFactory.getLogger(SearchHotelsResponseTransformerIOS.class);

	@Value("${mybiz.bottom.sheet.image}")
	private String myBizBottomSheetImage;

	@Value("${high.rated.url}")
	private String highRatedUrl;

	@Value("${gst.invoice.url}")
	private String gstInvoiceUrl;

	@Value("${bpg.url}")
	private String bpgUrl;

	@Autowired
	PolyglotService polyglotService;

	@Autowired
	PropertyManager propManager;

	private MyBizStaticCard myBizStaticCard;

	@Autowired
	PersuasionUtil persuasionUtil;
	private static final String DEVICE_TYPE = "Apps";

	@PostConstruct
	public void init() {
		if(consulFlag){
			myBizStaticCard = commonConfigConsul.getMyBizStaticCard();
			missingSlotDetails = commonConfigConsul.getMissingSlotDetails();
			LOGGER.warn("missingSlotDetails : {}" , missingSlotDetails);
			thresholdForSlashedAndDefaultHourPrice = commonConfigConsul.getThresholdForSlashedAndDefaultHourPrice();
			LOGGER.debug("Fetching values from commonConfig consul");
		}
		else{
			CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
			myBizStaticCard = commonConfig.myBizStaticCard();
			missingSlotDetails = commonConfig.missingSlotDetails();
			commonConfig.addPropertyChangeListener("myBizStaticCard", event -> myBizStaticCard = commonConfig.myBizStaticCard());
			commonConfig.addPropertyChangeListener("missingSlotDetails", event -> missingSlotDetails = commonConfig.missingSlotDetails());
			LOGGER.warn("missingSlotDetails : {}" , missingSlotDetails);
			thresholdForSlashedAndDefaultHourPrice = commonConfig.thresholdForSlashedAndDefaultHourPrice();
		}
		listingDrivingDurationBucketMap = gson.fromJson(listingDrivingDurationBucket, HashMap.class);
		specialFarePersuasionConfigMap = new Gson().fromJson(specialFarePersuasionConfig, new TypeToken<Map<String, Map<String, PersuasionData>>>() {
		}.getType());
	}

	@Override
	public SearchHotelsResponse convertSearchHotelsResponse(ListingPagePersonalizationResponsBO listingPageResponseBO, SearchHotelsRequest searchHotelsRequest, CommonModifierResponse commonModifierResponse) {
		SearchHotelsResponse searchHotelsResponse = super.convertSearchHotelsResponse(listingPageResponseBO,searchHotelsRequest,commonModifierResponse);
		if(searchHotelsResponse != null){
			searchHotelsResponse.setExpData(null);
		}
		return searchHotelsResponse;
	}

	@Override
	public void populateClientSpecificParameters() {
	}

	@Override
	public void addSeoTextPersuasion(Hotel hotel, SearchWrapperHotelEntity hotelEntity, boolean odd, ListingSearchRequest searchHotelsRequest, String sectionName) {

	}

	@Override
	public HotelCard buildQuickBookCard(QuickBookInfo quickBookInfo) {
		return getQuickBookCardForApps(quickBookInfo, polyglotService);
	}

	@Override
	public String getMyBizDirectHotelDistanceText(String distanceText) {
		return StringUtils.replace(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_DIRECT_HOTEL_APPS_DISTANCE_TEXT),"{DISTANCE_TEXT}", distanceText);
	}

	@Override
	public MyBizStaticCard buildStaticCard(String section, List<SearchWrapperHotelEntity> hotels) {
		MyBizStaticCard staticCard = null;
		if(CORPBUDGET_DIRECT_HOTEL.equalsIgnoreCase(section) && CollectionUtils.isNotEmpty(hotels) && !hotels.get(0).isCorpBudgetHotel() &&
				myBizStaticCard != null) {
			staticCard = SerializationUtils.clone(myBizStaticCard);
			staticCard.setActionUrl(hotels.get(0).getDetailDeeplinkUrl());
			translateStaticCard(staticCard);
		}
		return staticCard;
	}

	protected void translateStaticCard(MyBizStaticCard staticCard) {
		staticCard.setText(polyglotService.getTranslatedData(staticCard.getText()));
		staticCard.setSubtext(polyglotService.getTranslatedData(staticCard.getSubtext()));
		staticCard.setCtaText(polyglotService.getTranslatedData(staticCard.getCtaText()));
	}

	@Override
	public void addLocationPersuasionToHotelPersuasions(Hotel hotel, List<String> locationPersuasion, LinkedHashSet<String> facilities, ListingSearchRequest searchHotelsRequest, boolean enableAmenities, boolean sectionMyBizSimilarToDirectHtl, String dayUsePersuasionsText, TransportPoi nearestGroundTransportPoi, String drivingTimeText, LocusData locusData, boolean homestayV2Flow){
		if(CollectionUtils.isNotEmpty(locationPersuasion)) {
			if (hotel.getHotelPersuasions() == null)
				hotel.setHotelPersuasions(new HashMap<String,Object>());
			PersuasionObject locPers = new PersuasionObject();
			locPers.setData(new ArrayList<>());
			locPers.setTemplate(IMAGE_TEXT_H);
			locPers.setPlaceholder("SINGLE");

			int index = 1;
			PersuasionData locPersuasionData = new PersuasionData();
			locPersuasionData.setHasAction(false);
			locPersuasionData.setHtml(true);
			locPersuasionData.setId("LOC_PERSUASION_" + index++);
			locPersuasionData.setPersuasionType("LOCATION");

			PersuasionStyle style = new PersuasionStyle();
			//[HTL-46707] Changed the max lines to 3 for IH hotels only
			if (homestayV2Flow) {
				style.setFontSize(Constants.BASE_FONT_SIZE);
				style.setTextColor(PLACEHOLDER_CARD_M1_TEXT_COLOR);
			}
			if (hotel.getLocationDetail() != null && !Constants.DOM_COUNTRY.equalsIgnoreCase(hotel.getLocationDetail().getCountryId())) {
				style.setMaxLines(3);
			} else {
				style.setMaxLines(2);
			}
			locPersuasionData.setStyle(style);

			locPers.getData().add(locPersuasionData);
			if(locationPersuasion.size() == 1 ) {
				locPersuasionData.setText(locationPersuasion.get(0));
			}else if (locationPersuasion.size() >= 2){
				locPersuasionData.setText(locationPersuasion.get(0) + " | " + locationPersuasion.get(1));
				//For Secondary Location Persuasion, if it is present, add it in the Location Persuasion
				if (locationPersuasion.size() > 2)
					locPersuasionData.setText(locPersuasionData.getText() + " | " + locationPersuasion.get(2));
			}
			
			if(searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails()!=null && SHORTSTAYS_FUNNEL.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource())) {
				if(drivingTimeText == null || locusData == null) {
					LOGGER.warn("Location Persuasion could not be added for ShortStay Funnel because either driving_duration or city_name is empty");
					return;
				}
				StringBuilder updatedLocationPersuasionText = new StringBuilder();
				String locText = polyglotService.getTranslatedData(ConstantsTranslation.SHORTSTAY_LOCATION_PERSUASION_PREFIX_TEXT_FONT);
				locText = locText.replace("{city_text}", locationPersuasion.get(0));
				updatedLocationPersuasionText.append(locText);
				updatedLocationPersuasionText.append(Constants.SPACE);
				String finalDrivingText = polyglotService.getTranslatedData(ConstantsTranslation.SHORTSTAY_LOCATION_PERSUASION_SUFFIX_TEXT_FONT);
				String drivingText = polyglotService.getTranslatedData(ConstantsTranslation.DRIVING_DURATION_ZONE_SHORTSTAY).replace("{duration}", drivingTimeText);
				drivingText = drivingText.replace("{city_name}", locusData.getLocusName());
				finalDrivingText = finalDrivingText.replace("{driving_text}", drivingText);
				updatedLocationPersuasionText.append(finalDrivingText);
				locPersuasionData.setText(updatedLocationPersuasionText.toString());
			}

			try {
				((Map<Object,Object>) hotel.getHotelPersuasions()).put(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP,locPers);
			} catch (ClassCastException e) {
				LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
			} catch (Exception e) {
				LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
			}

		}

		if(CollectionUtils.isNotEmpty(facilities) && !StringUtils.equals(hotel.getViewType(), ONE_CLICK) && enableAmenities) {
			if (hotel.getHotelPersuasions() == null)
				hotel.setHotelPersuasions(new HashMap<String,Object>());
			PersuasionObject amenityPers = new PersuasionObject();
			amenityPers.setData(new ArrayList<>());
			amenityPers.setTemplate(IMAGE_TEXT_H);
			amenityPers.setPlaceholder("SINGLE");


			PersuasionData amenPersuasionData = new PersuasionData();
			amenPersuasionData.setHasAction(false);
			amenPersuasionData.setHtml(false);
			amenPersuasionData.setId("AMENITIES");
			amenPersuasionData.setPersuasionType("AMENITIES");
			amenPersuasionData.setStyle(new PersuasionStyle());
			amenPersuasionData.getStyle().setTextColor("#000000");
			StringBuilder text = new StringBuilder();
			int index = 1;
			Iterator<String> iter = facilities.iterator();
			while(iter.hasNext() && index <=3){

				text.append( iter.next());
				if(index < 3 && index != facilities.size())
					text.append(" | ");
				index++;
			}
			amenPersuasionData.setText(text.toString());
			amenityPers.getData().add(amenPersuasionData);


			try {
				((Map<Object,Object>) hotel.getHotelPersuasions()).put(Constants.AMENITIES_PLACEHOLDER_ID_APP,amenityPers);
			} catch (ClassCastException e) {
				LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
			} catch (Exception e) {
				LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
			}
		}

		if(searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails()!=null && FUNNEL_DAYUSE.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource()) && StringUtils.isNotEmpty(dayUsePersuasionsText)) {
			if (hotel.getHotelPersuasions() == null)
				hotel.setHotelPersuasions(new HashMap<String,Object>());
			PersuasionObject amenityPers = new PersuasionObject();
			amenityPers.setData(new ArrayList<>());
			amenityPers.setTemplate(IMAGE_TEXT_H1);
			amenityPers.setPlaceholder("SINGLE");


			PersuasionData amenPersuasionData = new PersuasionData();
			amenPersuasionData.setHasAction(false);
			amenPersuasionData.setHtml(false);
			amenPersuasionData.setId(DAYUSE_LOCAL_ID);
			amenPersuasionData.setPersuasionType(DAYUSE_LOCAL_ID);
			amenPersuasionData.setStyle(new PersuasionStyle());
			amenPersuasionData.getStyle().setTextColor("#000000");
			amenPersuasionData.setText(dayUsePersuasionsText);
			amenPersuasionData.setIcontype("b_dot");
			amenityPers.getData().add(amenPersuasionData);
			try {
				((Map<Object,Object>) hotel.getHotelPersuasions()).put(Constants.AMENITIES_PLACEHOLDER_ID_APP,amenityPers);
			} catch (ClassCastException e) {
				LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
			} catch (Exception e) {
				LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
			}
		}
	}

	@Override
	public void addPersuasionHoverData(Hotel hotel, SearchWrapperHotelEntity hotelEntity, CancellationTimeline cancellationTimeline, DisplayFare displayFare) {
		hotel.setLovedByIndians(persuasionUtil.checkIfIndianessPersuasionExists(hotelEntity.getHotelPersuasions()));
	}
	@Override
	public BottomSheet buildBottomSheet(PersonalizedResponse<SearchWrapperHotelEntity> perResponse) {
		BottomSheet bottomSheet = new BottomSheet();
		bottomSheet.setHeading(polyglotService.getTranslatedData(ConstantsTranslation.CORP_BOTTOM_SHEET_HEADING_MOBILE));
		bottomSheet.setSubHeading(polyglotService.getTranslatedData(ConstantsTranslation.CORP_BOTTOM_SHEET_SUBHEADING_MOBILE));
		bottomSheet.setImgUrl(myBizBottomSheetImage);
		bottomSheet.setCta(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_ASSURED_FILTER_CARD_CTA));
		bottomSheet.setCtaAction("");
		List<SectionFeature> sectionFeatureList = new ArrayList<>();
		sectionFeatureList.add(new SectionFeature(highRatedUrl, polyglotService.getTranslatedData(ConstantsTranslation.RATED_HIGH_BT_MOBILE), "grayDot", null, null));
		sectionFeatureList.add(new SectionFeature(gstInvoiceUrl, polyglotService.getTranslatedData(ConstantsTranslation.GST_INVOICE_ASSURANCE_TEXT), "grayDot", null, null));
		sectionFeatureList.add(new SectionFeature(bpgUrl, polyglotService.getTranslatedData(ConstantsTranslation.BPG_TEXT), "grayDot", null, null));
		bottomSheet.setSectionFeatures(sectionFeatureList);
		return bottomSheet;
	}

	//To build location persuasion to Mob-Landing Cards
	public void addLocationPersuasionToHotelPersuasions(SearchWrapperHotelEntity hotelEntity) {
		if (hotelEntity != null && CollectionUtils.isNotEmpty(hotelEntity.getLocationPersuasion())) {
			List<String> locationPersuasion = hotelEntity.getLocationPersuasion();
			if (hotelEntity.getHotelPersuasions() == null)
				hotelEntity.setHotelPersuasions(new HashMap<String, Object>());
			PersuasionObject locPers = new PersuasionObject();
			locPers.setData(new ArrayList<>());
			locPers.setPlaceholder(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP);
			locPers.setTemplate("IMAGE_TEXT_H");
			locPers.setPlaceholder("SINGLE");

			int index = 1;
			PersuasionData locPersuasionData = new PersuasionData();
			locPersuasionData.setHasAction(false);
			locPersuasionData.setHtml(true);
			locPersuasionData.setId("LOC_PERSUASION_" + index++);
			locPersuasionData.setPersuasionType("LOCATION");
			PersuasionStyle style = new PersuasionStyle();
			style.setMaxLines(2);
			locPersuasionData.setStyle(style);

			locPers.getData().add(locPersuasionData);
			if (locationPersuasion.size() == 1) {
				locPersuasionData.setText(locationPersuasion.get(0));
			} else if (locationPersuasion.size() >= 2) {
				locPersuasionData.setText(locationPersuasion.get(0) + " | " + locationPersuasion.get(1));
			}
			try {
				((Map<Object, Object>) hotelEntity.getHotelPersuasions()).put(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP, locPers);
			} catch (ClassCastException e) {
				LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ", e.getMessage());
			} catch (Exception e) {
				LOGGER.error("Location Persuasion could not be added due to : {} ", e.getMessage());
			}

		}
	}

	/**
	 * This is an encapsulated method to build all the required persuasions for Hidden Gem card.
	 */
	@Override
	public void addPersuasionsForHiddenGemCard(SearchWrapperHotelEntity hotelEntity) {
		if (hotelEntity.getHotelPersuasions() == null)
			hotelEntity.setHotelPersuasions(new HashMap<String,Object>());
		addLocationPersuasionToHotelPersuasions(hotelEntity);
		buildHiddenGemPersuasion(hotelEntity);
		buildHiddenGemIconPersuasion(hotelEntity);
		buildHomeStaysPersuasion(hotelEntity);
	}

	/**
	 * Method to build Hidden Gem Persuasion, this method calls persuasionUtil to build Hidden Gem persuasion.
	 * If the util method return a non-empty Persuasion List, this method will add that persuasion in Hotel Persuasion object.
	 */
	public void buildHiddenGemPersuasion(SearchWrapperHotelEntity hotelEntity) {
		PersuasionObject hiddenGemPersuasion = persuasionUtil.buildHiddenGemPersuasion(hotelEntity, DEVICE_TYPE);
		if (hiddenGemPersuasion != null && hotelEntity.getHotelPersuasions() instanceof Map<?, ?>) {
			hiddenGemPersuasion.setPlaceholder(Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdApps());
			MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdApps(), hiddenGemPersuasion);
		}
	}

	/**
	 * Method to build Hidden Gem Icon Persuasion, this method calls persuasionUtil to build Hidden Gem Icon persuasion.
	 * If the util method return a non-empty Persuasion List, this method will add that persuasion in Hotel Persuasion object.
	 */
	public void buildHiddenGemIconPersuasion(SearchWrapperHotelEntity hotelEntity) {
		PersuasionObject hiddenGemPersuasion = persuasionUtil.buildHiddenGemIconPersuasion(hotelEntity, DEVICE_TYPE);
		if (hiddenGemPersuasion != null && hotelEntity.getHotelPersuasions() instanceof Map<?, ?>) {
			hiddenGemPersuasion.setPlaceholder(Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdApps());
			MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdApps(), hiddenGemPersuasion);
		}
	}

	/**
	 * Method to build Home Stays Persuasions, this method calls persuasionUtil to build Homestay persuasions.
	 * If the util method return a non-empty Persuasion List for homestay title, this method will add that persuasion in Hotel Persuasion object.
	 * And, if util method return a non-empty Persuasion List for homestay title and sub-title, this method will add both the persuasions in Hotel Persuasion object, on the same placeholder
	 */
	public void buildHomeStaysPersuasion(SearchWrapperHotelEntity hotelEntity) {
		PersuasionObject homeStaysTitlePersuasion = persuasionUtil.buildHomeStaysTitlePersuasion(hotelEntity, DEVICE_TYPE);
		if (homeStaysTitlePersuasion != null && CollectionUtils.isNotEmpty(homeStaysTitlePersuasion.getData()) && hotelEntity.getHotelPersuasions() instanceof Map<?, ?>) {
			homeStaysTitlePersuasion.setPlaceholder(Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdApps());
			MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdApps(), homeStaysTitlePersuasion);

			PersuasionObject homeStaysSubTitlePersuasion = persuasionUtil.buildHomeStaysSubTitlePersuasion(hotelEntity, DEVICE_TYPE);
			if (homeStaysSubTitlePersuasion != null && CollectionUtils.isNotEmpty(homeStaysSubTitlePersuasion.getData())) {
				homeStaysSubTitlePersuasion.setPlaceholder(Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdApps());
				homeStaysTitlePersuasion.getData().add(homeStaysSubTitlePersuasion.getData().get(0));
				MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdApps(), homeStaysTitlePersuasion);
			}
		}
	}

	/**
	 * HTL-40907: Add special fare persuasion to hotel's persuasion map for lowest negotiated rate plan hotels.
	 *
	 * @param hotelEntity
	 */
	@Override
	public void addSpecialFarePersuasion(SearchWrapperHotelEntity hotelEntity) {
		addSpecialFareTagPersuasionForMobile(hotelEntity);
	}

	/**
	 * HTL-40907: Add booking confirmation persuasion to hotel's persuasion map for lowest negotiated rate plan hotels.
	 *
	 * @param hotelEntity
	 */
	@Override
	public void addBookingConfirmationPersuasion(SearchWrapperHotelEntity hotelEntity) {
		addBookingConfirmationPersuasionForMobile(hotelEntity);
	}

	@Override
	public String buildBGColor(String sectionName, String orientation, String cardType) {
		return null;
	}

}
