package com.mmt.hotels.clientgateway.transformer.response.android;

import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.transformer.response.AvailRoomsResponseTransformer;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import org.springframework.stereotype.Component;
import java.util.Map;


@Component
public class AvailRoomsResponseTransformerAndroid extends AvailRoomsResponseTransformer {

    @Override
    public void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap) {

    }

    @Override
    public PersuasionResponse buildSpecialFareTagPersuasion(String corpAlias) {
        return null;
    }
}
