package com.mmt.hotels.clientgateway.transformer.response;

import com.gommt.hotels.orchestrator.detail.model.response.HostCallingResponse;
import com.gommt.hotels.orchestrator.detail.model.response.common.ErrorResponse;
import com.mmt.hotels.clientgateway.response.hostcalling.HostCallingInitiateResponse;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import com.mmt.hotels.pojo.response.ErrorEntity;
import org.apache.commons.collections.CollectionUtils;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.CHAIN_NAME_PLACEHOLDER;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.MISSED_CALL_MESSAGE_CONSTANT;

@Component
public class OrchHostCallingResponseTransformer {

    private static final Logger logger = LoggerFactory.getLogger(OrchHostCallingResponseTransformer.class);

    @Autowired
    private PolyglotService polyglotService;

    /**
     * Transforms HES HostCallingInitiateResponse to ClientGateway HostCallingInitiateResponse
     * Similar to convertStaticDetailResponse in StaticDetailResponseTransformer
     *
     * @param hesResponse HES response
     * @param client Client type (PWA, ANDROID, IOS, DESKTOP)
     * @param requestId Request ID from original request for fallback scenarios
     * @return ClientGateway response
     */
    public HostCallingInitiateResponse convertHostCallingResponse(
            HostCallingResponse hesResponse,
            String client,
            String requestId) {

        if (hesResponse == null) {
            logger.warn("HES HostCalling response is null for requestId: {}", requestId);
            return createErrorResponse(requestId, "No response received from entity service");
        }

        try {
            HostCallingInitiateResponse cgResponse = new HostCallingInitiateResponse();

            // Map core response fields - FIX: Add null safety for all hesResponse field access
            cgResponse.setStatus(hesResponse.getStatus() != null ? hesResponse.getStatus() : "error");
            cgResponse.setRequestId(hesResponse.getRequestId() != null ? hesResponse.getRequestId() : requestId);

            // Map host calling specific fields - FIX: Add null safety for all fields
            cgResponse.setChainName(hesResponse.getChainName());
            cgResponse.setAvailableNow(hesResponse.isAvailableNow()); // boolean primitive is safe
            cgResponse.setStartTime(hesResponse.getStartTime());
            cgResponse.setEndTime(hesResponse.getEndTime());
            cgResponse.setMaskedNumber(hesResponse.getMaskedNumber());

            // Map error handling fields using conversion methods
            cgResponse.setResponseErrors(convertToResponseErrors(hesResponse.getErrorList()));
            cgResponse.setErrorEntity(convertToErrorEntity(hesResponse.getError()));

            // Note: missedCallMessage will be set by the service layer after polyglot call

            // Add missed call message using polyglot service - FIX: Add null safety for chain name
            String chainName = cgResponse.getChainName();
            if (StringUtils.isNotEmpty(chainName)) {
                String missedCallMessage = getMissedCallMessage(chainName);
                cgResponse.setMissedCallMessage(missedCallMessage);
            }

            logger.debug("Successfully transformed HES response to CG response for client: {}, requestId: {}", client, requestId);
            return cgResponse;

        } catch (Exception e) {
            logger.error("Error transforming HES response to CG response for client: {}, requestId: {}", client, requestId, e);
            return createErrorResponse(requestId, "Failed to process host calling response");
        }
    }

    /**
     * Gets missed call message using polyglot service
     * Similar to the original implementation
     */
    private String getMissedCallMessage(String chainName) {
        try {
            // FIX: Add null safety for polyglotService
            if (polyglotService == null) {
                logger.warn("PolyglotService is null, using fallback message");
                return "Host calling is currently unavailable. Please try again during operational hours.";
            }

            // Get the base message from polyglot
            String baseMessage = polyglotService.getTranslatedData(MISSED_CALL_MESSAGE_CONSTANT);

            // FIX: Add null safety for baseMessage and chainName
            if (StringUtils.isNotEmpty(baseMessage) && StringUtils.isNotEmpty(chainName)) {
                // Replace the chainName placeholder with actual chain name
                return baseMessage.replace(CHAIN_NAME_PLACEHOLDER, chainName);
            }

            // FIX: Return safe default if baseMessage is null/empty
            return StringUtils.isNotEmpty(baseMessage) ? baseMessage : 
                   "Host calling is currently unavailable. Please try again during operational hours.";
                   
        } catch (Exception e) {
            logger.error("Error getting missed call message for chainName: {}", chainName, e);
            return "Host calling is currently unavailable. Please try again during operational hours.";
        }
    }

    /**
     * Converts orchestrator ErrorResponse list to ClientGateway ResponseErrors
     */
    private ResponseErrors convertToResponseErrors(List<ErrorResponse> errorList) {
        if (CollectionUtils.isEmpty(errorList)) {
            return null;
        }

        List<com.mmt.hotels.model.response.errors.Error> cgErrorList = new ArrayList<>();
        for (ErrorResponse orchestratorError : errorList) {
            if (orchestratorError != null) {
                // FIX: Add null safety for error code and message
                String errorCode = orchestratorError.getCode() != null ? orchestratorError.getCode() : "UNKNOWN_ERROR";
                String errorMessage = orchestratorError.getMessage() != null ? orchestratorError.getMessage() : "Unknown error occurred";
                
                com.mmt.hotels.model.response.errors.Error cgError =
                    new com.mmt.hotels.model.response.errors.Error.Builder()
                        .buildErrorCode(errorCode, errorMessage)
                        .build();
                cgErrorList.add(cgError);
            }
        }

        // FIX: Check if cgErrorList is empty after processing
        if (cgErrorList.isEmpty()) {
            return null;
        }

        return new ResponseErrors.Builder()
            .buildErrorList(cgErrorList)
            .build();
    }

    /**
     * Converts orchestrator ErrorResponse to ClientGateway ErrorEntity
     */
    private ErrorEntity convertToErrorEntity(ErrorResponse error) {
        if (error == null) {
            return null;
        }

        ErrorEntity errorEntity = new ErrorEntity();
        // FIX: Add null safety for error code and message
        errorEntity.setErrorCode(error.getCode() != null ? error.getCode() : "UNKNOWN_ERROR");
        errorEntity.setMsg(error.getMessage() != null ? error.getMessage() : "Unknown error occurred");
        return errorEntity;
    }

    /**
     * Creates an error response when transformation fails or HES response is null
     *
     * @param requestId Request ID for tracking
     * @param errorMessage Error message to set
     * @return Error response
     */
    private HostCallingInitiateResponse createErrorResponse(String requestId, String errorMessage) {
        HostCallingInitiateResponse errorResponse = new HostCallingInitiateResponse();
        errorResponse.setStatus("error");
        // FIX: Add null safety for requestId
        errorResponse.setRequestId(requestId != null ? requestId : "unknown");
        errorResponse.setAvailableNow(false);

        logger.warn("Created error response for requestId: {} with message: {}", requestId, errorMessage);
        return errorResponse;
    }
}
