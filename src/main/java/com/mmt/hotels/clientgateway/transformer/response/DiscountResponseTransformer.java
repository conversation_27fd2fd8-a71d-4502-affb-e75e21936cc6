package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.BNPLDisabledReason;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.helpers.FareHoldHelper;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.Currency;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.response.MpFareHoldStatus;
import com.mmt.hotels.model.response.discount.ValidCouponResult;
import com.mmt.hotels.model.response.discount.ValidateCouponResponse;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.model.PriceFooterDetail;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.*;


@Component
public class DiscountResponseTransformer{

	@Autowired
	private CommonResponseTransformer commonResponseTransformer;

	@Autowired
	private PolyglotService polyglotService;
	@Autowired
	private PersuasionUtil persuasionUtil;
	@Autowired
	private PricingEngineHelper pricingEngineHelper;
	@Autowired
	private FareHoldHelper fareHoldHelper;

	@Value("${bnpl.active.booking.threshold}")
	private int bnplActiveBookingThreshold;

	@Autowired
	Utility utility;



	public ValidateCouponResponseBody convertValidateCouponResponse(ValidateCouponResponse validateCouponResponse, String expData, String countryCode, boolean showBnplCard) {

		ValidateCouponResponseBody response = new ValidateCouponResponseBody();
		TotalPricing totalPricing = new TotalPricing();
		Map<String, String> expDataMap = utility.getExpDataMap(expData);

		boolean enableThemification = validateCouponResponse.getThemifiedDetails() != null && validateCouponResponse.getThemifiedDetails().isThemificationEnabled();
		if (validateCouponResponse.getDisplayPriceBreakDown() != null) {
			List<PricingDetails> pricingDetails = commonResponseTransformer.getPricingDetails(validateCouponResponse.getDisplayPriceBreakDown(), countryCode, validateCouponResponse.getValidCouponResult().getPayMode(),
					validateCouponResponse.getValidCouponResult().getCorpMetaInfo() != null,
					commonResponseTransformer.getCorporateSegmentId(validateCouponResponse.getRoomTypes()), expData, false,
					validateCouponResponse.getValidCouponResult() != null && validateCouponResponse.getValidCouponResult().isMyPartnerMoveToTdsTaxStructure(), validateCouponResponse.getPriceBreakupText(), validateCouponResponse.getDisplayPriceBreakDown().isCbrAvailable(), true,validateCouponResponse.getValidCouponResult()!=null?validateCouponResponse.getValidCouponResult().getExtraDiscountType(): EMPTY_STRING, validateCouponResponse.isMetaTraffic(), true);
			totalPricing.setDetails(pricingDetails);
			int ancillaryVariant = MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(ExperimentKeys.ANCILLARY_DISPLAY_PRICE_VARIANT) ? Integer.parseInt(expDataMap.get(ExperimentKeys.ANCILLARY_DISPLAY_PRICE_VARIANT.getKey())) : 2;

			boolean showReviewOffersCategory = utility.showReviewOffersCategory(expDataMap);
			List<Coupon> coupons = commonResponseTransformer.getCouponDetails(validateCouponResponse.getDisplayPriceBreakDown(),utility.isExperimentTrue(expDataMap, ihCashbackSectionExp), ancillaryVariant, showReviewOffersCategory);

			totalPricing.setCoupons(coupons);
			totalPricing.setPinCodeMandatory(validateCouponResponse.getDisplayPriceBreakDown().isPinCodeMandatory());
			
//			EMIDetail emiDetail = commonResponseTransformer.getEmiDetails(validateCouponResponse.getDisplayPriceBreakDown());
//			totalPricing.setEmiBankDetails(emiDetail);
			totalPricing.setPricingKey(validateCouponResponse.getDisplayPriceBreakDown().getPricingKey());

			String pahText = polyglotService.getTranslatedData(ConstantsTranslation.PAY_AT_HOTEL_POLICY_TEXT_GENERIC);
			if (validateCouponResponse.getValidCouponResult() != null) {
				totalPricing.setPartnerDetails(commonResponseTransformer.getPartnerDetails(totalPricing.getDetails(), pricingEngineHelper.getMarkUpForHotels(validateCouponResponse.getValidCouponResult().getMarkUpDetails(), validateCouponResponse.getDisplayPriceBreakDown().getDisplayPrice())));
				if (validateCouponResponse.getValidCouponResult().getPayMode() != null)
					Utility.updatePayAtHotelText(totalPricing, validateCouponResponse.getValidCouponResult().getPayMode(), pahText, countryCode);
				if (validateCouponResponse.getValidCouponResult().getCorpMetaInfo() != null)
					response.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(validateCouponResponse.getValidCouponResult().getCorpMetaInfo(), validateCouponResponse.getValidCouponResult().isTcsV2FlowEnabled()));
			}
		}

		BNPLVariant bnplVariant = null;
		boolean isBnplOneVariant = false;  // HTL-42803: TO-DO remove boolean isBnplOneVariant node once BNPLVariant Enum changes are completely live.
		Boolean isPeakDate = validateCouponResponse.getDisplayPriceBreakDown() != null && validateCouponResponse.getDisplayPriceBreakDown().getPeakDate() != null && validateCouponResponse.getDisplayPriceBreakDown().getPeakDate();
		if (validateCouponResponse.getValidCouponResult() != null) {
			isBnplOneVariant = validateCouponResponse.getValidCouponResult().isBnplNewVariant();
			bnplVariant = validateCouponResponse.getValidCouponResult().getBnplVariant();
			if(bnplVariant == null || bnplVariant.equals(BNPLVariant.BNPL_NOT_APPLICABLE)) {
				bnplVariant = validateCouponResponse.getBnplVariant();
			}
			totalPricing.setCurrency(validateCouponResponse.getValidCouponResult().getCurrency());
			commonResponseTransformer.buildPaymentInfoTextForAlternateCurrency(totalPricing, countryCode, validateCouponResponse.getValidCouponResult().getPayMode(), validateCouponResponse.getValidCouponResult().getCurrency());
			totalPricing.setAffiliateFeeDetails(commonResponseTransformer.buildAffiliateFeeDetails(validateCouponResponse.getValidCouponResult().getAffiliateFeeOptions()));
			commonResponseTransformer.updateTotalAmountInHotelierCurrency(totalPricing.getDetails(), validateCouponResponse.getValidCouponResult().getPayMode(),
					validateCouponResponse.getValidCouponResult().getCurrency(), validateCouponResponse.getValidCouponResult().getHotelierCurrency(),
					validateCouponResponse.getValidCouponResult().getHotelierCurrencyConvFactor());
			Map<String, Object> nonBnplAppliedCouponDetailsMap = commonResponseTransformer.fetchNonBnplAppliedCouponDetails(totalPricing);
			boolean bnplDisabledDueToNonBnplCouponApplied = (boolean) nonBnplAppliedCouponDetailsMap.get(Constants.BNPL_DISABLED_DUE_TO_NON_BNPL_COUPON_APPLIED);
			String nonBnplCouponAppliedCode = (String) nonBnplAppliedCouponDetailsMap.get(Constants.NON_BNPL_COUPON_APPLIED_CODE);
			boolean insuranceAddonSelected = validateCouponResponse.getDisplayPriceBreakDown() != null && MapUtils.isNotEmpty(validateCouponResponse.getDisplayPriceBreakDown().getInsuranceBreakupMap());
			Integer activeBnplBookingCount = validateCouponResponse.isUserLevelBnplDisabled() ? validateCouponResponse.getActiveBnplBookingCount() : null;
			BNPLDisabledReason bnplDisabledReason = null;
			if (validateCouponResponse.isShowDisabledBnplDetails()) {
				bnplDisabledReason = commonResponseTransformer.getBNPLDisabledReason(validateCouponResponse.isUserLevelBnplDisabled(), bnplDisabledDueToNonBnplCouponApplied, false);
			}
			int bnplAllowedCount = bnplActiveBookingThreshold;
			if (validateCouponResponse.getBnplAllowedCount() != null && validateCouponResponse.getBnplAllowedCount() > 0) {
				bnplAllowedCount = validateCouponResponse.getBnplAllowedCount();
			}
			if (bnplDisabledReason != null && bnplVariant != null && !bnplVariant.equals(BNPLVariant.BNPL_NOT_APPLICABLE)) {
				response.setBnplDetails(commonResponseTransformer.buildBNPLDetailsForDisabledBnpl(bnplDisabledReason, nonBnplCouponAppliedCode, bnplVariant, activeBnplBookingCount, bnplAllowedCount));
			} else {
				response.setBnplDetails(commonResponseTransformer.buildBNPLDetails(validateCouponResponse.getValidCouponResult().isBnplApplicable(),
						validateCouponResponse.getValidCouponResult().getBnplPersuasionMsg(), validateCouponResponse.getValidCouponResult().getBnplPolicyText(),
						validateCouponResponse.getValidCouponResult().getBnplNewVariantText(), validateCouponResponse.getValidCouponResult().getBnplNewVariantSubText(),
						validateCouponResponse.getValidCouponResult().isOriginalBNPL(), showBnplCard, bnplVariant, validateCouponResponse.getValidCouponResult().getBnplFinalPrice()));
				if(validateCouponResponse.getValidCouponResult()!=null && validateCouponResponse.getValidCouponResult().getFullPayment()!=null) {
					response.setFullPayment(utility.buildFullPayment(validateCouponResponse.getValidCouponResult().getFullPayment()));
				}
			}
			final MpFareHoldStatus mpFareHoldStatus = fareHoldHelper.getMpFareHoldStatus(validateCouponResponse.getRoomTypes());
			if(mpFareHoldStatus!=null && mpFareHoldStatus.getExpiry()!=null) {
				response.setBookNowDetails(fareHoldHelper.getBookNowDetails(mpFareHoldStatus, bnplDisabledDueToNonBnplCouponApplied, nonBnplCouponAppliedCode));
			}

			response.setCancellationTimeline(commonResponseTransformer.buildCancellationTimeline(validateCouponResponse.getValidCouponResult().getCancellationTimeline(), null));
			response.setCancellationPolicyTimeline(commonResponseTransformer.buildCancellationPolicyTimeline(validateCouponResponse.getValidCouponResult().getCancellationTimeline(), enableThemification, null));
			response.setPaymentPlan(commonResponseTransformer.buildPaymentPlan(validateCouponResponse.getPaymentPlan()));
			response.setStatusMessage(validateCouponResponse.getValidCouponResult().getSuccessApplyMessage());
			if(validateCouponResponse.getNoOfNightStays()!=null && validateCouponResponse.getRoomCount() != null && validateCouponResponse.getAdultCount()!=null && StringUtils.isNotEmpty(validateCouponResponse.getSbppExpValue())) {
				Double displayAmount = utility.getTotalAmount(totalPricing);
				String currency = totalPricing!=null ? Currency.getCurrencyEnum(totalPricing.getCurrency()).getCurrencySymbol() : HINDI_RUPEE;
				totalPricing.setPricePersuasions(utility.buildHomestayPersuasion(true, validateCouponResponse.getSbppExpValue(), validateCouponResponse.getRoomCount(), validateCouponResponse.getAdultCount(), displayAmount, validateCouponResponse.getNoOfNightStays(), currency)); // here we will set price Persuasion for homestay
			}
		}

		if (validateCouponResponse.getMsmeCorpCard() != null) {
			response.setMsmeCorpCard(validateCouponResponse.getMsmeCorpCard());
		}

		response.setRateplanlist(commonResponseTransformer.buildRateplanList(validateCouponResponse.getRoomTypes(), response.getBnplDetails(), isBnplOneVariant, bnplVariant, isPeakDate, enableThemification));
		if (response.getCorpApprovalInfo() != null && response.getCorpApprovalInfo().isWalletQuickPayAllowed() && validateCouponResponse.getDisplayPriceBreakDown() != null) {
			response.setMyBizQuickPayConfig(commonResponseTransformer.buildMyBizQuickPayConfig(validateCouponResponse.getDisplayPriceBreakDown(),
					validateCouponResponse.getValidCouponResult() != null ? validateCouponResponse.getValidCouponResult().getCurrency() : null));
		}
		if (enableThemification) {
			String payMode = validateCouponResponse.getValidCouponResult() != null ? validateCouponResponse.getValidCouponResult().getPayMode() : EMPTY_STRING;
			if (validateCouponResponse.getThemifiedDetails().getPriceFooter() != null) {
				PriceFooterDetail priceFooterDetail = validateCouponResponse.getThemifiedDetails().getPriceFooter();
				totalPricing.setPriceFooter(commonResponseTransformer.buildPriceFooter(priceFooterDetail.isTaxIncluded(), priceFooterDetail.getLos(), priceFooterDetail.getRoomCount()));
			}
			if (response.getBnplDetails() != null && response.getBnplDetails().isBnplApplicable()) {
				response.getBnplDetails().setPriceFooter(commonResponseTransformer.buildBNPLPriceFooter());
			}
			commonResponseTransformer.updatePriceFooterForPAHOnly(payMode, totalPricing);
		}
		if (validateCouponResponse.getDisplayPriceBreakDown() != null && validateCouponResponse.getDisplayPriceBreakDown().getBlackInfo() != null && (DEVICE_OS_ANDROID.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) || DEVICE_IOS.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())))) {
			commonResponseTransformer.setPricePersuasionBlackInfo(validateCouponResponse.getDisplayPriceBreakDown().getBlackInfo(), totalPricing);
		}
		if (validateCouponResponse.getDisplayPriceBreakDown() != null && validateCouponResponse.getDisplayPriceBreakDown().getHotelBenefitInfo() != null && (DEVICE_OS_ANDROID.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) || DEVICE_IOS.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())))) {
			commonResponseTransformer.setPricePersuasionHotelBenefitInfo(validateCouponResponse.getDisplayPriceBreakDown().getHotelBenefitInfo(), totalPricing);
		}
		if(validateCouponResponse.getValidCouponResult() != null && validateCouponResponse.getValidCouponResult().getHeroTierUpgradeDetails() != null) {
			totalPricing.setHeroTierUpgradeDetails(validateCouponResponse.getValidCouponResult().getHeroTierUpgradeDetails());
		}
		if(validateCouponResponse.getValidCouponResult() != null && validateCouponResponse.getValidCouponResult().getMyPartnerCashbackDetails() != null) {
			totalPricing.setMyPartnerCashbackDetails(validateCouponResponse.getValidCouponResult().getMyPartnerCashbackDetails());
			PricingDetails effectivePriceDetails = commonResponseTransformer.buildEffectivePrice(validateCouponResponse.getDisplayPriceBreakDown());
			if(effectivePriceDetails != null && totalPricing != null && totalPricing.getDetails() != null) {
				totalPricing.getDetails().add(effectivePriceDetails);
			}
		}



		response.setTotalPricing(totalPricing);
		if (validateCouponResponse.getDisplayPriceBreakDown() != null && validateCouponResponse.getValidCouponResult() != null
				&& CollectionUtils.isNotEmpty(validateCouponResponse.getDisplayPriceBreakDown().getNoCostEmiDetailsList())) {
			ValidCouponResult validCouponResult = validateCouponResponse.getValidCouponResult();

			com.mmt.hotels.model.response.pricing.FullPayment fullPayment = commonResponseTransformer.buildNoCostEmiDetailAndUpdateFullPayment(validateCouponResponse.getDisplayPriceBreakDown().getNoCostEmiDetailsList(),
					 response.getTotalPricing(), response.getFullPayment(), response.getBnplDetails());

			//Update the fullPayment Node for Apps, now having no-cost emi messaging
			if (fullPayment != null)
				response.setFullPayment(utility.buildFullPayment(fullPayment));

		}

		// Set the BNPL unavailable message when BNPL is removed due to addition of coupon for which BNPL is not applicable
		if (StringUtils.isNotBlank(validateCouponResponse.getBnplUnavailableMsg())) {
			response.getTotalPricing().setBnplUnavailableMsg(null);
		}
		// HTL-39856 ExpressCheckoutDetail for myPartner to be sent to client if user is eligible to pay entire amount from wallet
		if (validateCouponResponse != null && validateCouponResponse.getValidCouponResult() != null &&
				validateCouponResponse.getValidCouponResult().getExpressCheckoutDetail() != null && totalPricing != null) {
			totalPricing.setExpressCheckoutDetail(validateCouponResponse.getValidCouponResult().getExpressCheckoutDetail());
		}
		//build cashback offer persuasion/Hero offer persuasion for review page myPartner funnel
		boolean isMyPartnerRequest = FUNNEL_SOURCE_MYPARTNER.equalsIgnoreCase(MDC.get(FUNNEL_SOURCE));
		if (isMyPartnerRequest) {
			if (validateCouponResponse.getDisplayPriceBreakDown() != null && validateCouponResponse.getDisplayPriceBreakDown().getCouponInfo() != null) {
				BestCoupon coupon = validateCouponResponse.getDisplayPriceBreakDown().getCouponInfo();
				//If manthan is sending rewardbonus in mmtVals node, coupon will be set as CTW->Cashback Amount(Cashback offer persuasion condition)
				boolean isCashbackAmtAvailable = org.apache.commons.collections.MapUtils.isNotEmpty(coupon.getHybridDiscounts()) && coupon.getHybridDiscounts().containsKey("CTW");
				if (StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage()) || isCashbackAmtAvailable) {
					response.setHotelPersuasions(new HashMap<String, PersuasionResponse>());
					persuasionUtil.buildLoyaltyCashbackPersuasions(coupon, response.getHotelPersuasions());
				}
			}
		}
		response.setAckId(validateCouponResponse.getAckId());
		if (validateCouponResponse.getFlexiDetailBottomSheet() != null) {
			response.setFlexiDetailBottomSheet(buildFlexiDetailBottomSheet(validateCouponResponse.getFlexiDetailBottomSheet()));
		}
		if (CollectionUtils.isNotEmpty(validateCouponResponse.getUpdatedUpsellOptions())) {
			if (CollectionUtils.isEmpty(response.getUpdatedUpsellOptions()))	{
				response.setUpdatedUpsellOptions(new ArrayList<>());
			}
			for (com.mmt.hotels.model.response.txn.UpdatedUpsellOptions updatedUpsellOptions : validateCouponResponse.getUpdatedUpsellOptions()) {
				response.getUpdatedUpsellOptions().add(buildUpdatedUpsellOptions(updatedUpsellOptions));
			}
		}
		return response;

	}

	private UpdatedUpsellOptions buildUpdatedUpsellOptions(com.mmt.hotels.model.response.txn.UpdatedUpsellOptions updatedUpsellOptions) {
		UpdatedUpsellOptions updatedUpsellOptionsResponse = new UpdatedUpsellOptions();
		updatedUpsellOptionsResponse.setAddOnType(updatedUpsellOptions.getAddOnType());
		updatedUpsellOptionsResponse.setRatePlanCode(updatedUpsellOptions.getRatePlanCode());
		updatedUpsellOptionsResponse.setRoomCode(updatedUpsellOptions.getRoomCode());
		updatedUpsellOptionsResponse.setSuccessDisplayText(updatedUpsellOptions.getSuccessDisplayText());
		return updatedUpsellOptionsResponse;
	}

	private FlexiDetailBottomSheet buildFlexiDetailBottomSheet(com.mmt.hotels.model.response.pricing.FlexiDetailBottomSheet flexiDetailBottomSheet) {
		FlexiDetailBottomSheet flexiDetailBottomSheetResponse = new FlexiDetailBottomSheet();
		flexiDetailBottomSheetResponse.setTitleText(flexiDetailBottomSheet.getTitleText());
		flexiDetailBottomSheetResponse.setSelected(utility.getSelected(flexiDetailBottomSheet.getSelected()));
		flexiDetailBottomSheetResponse.setUnselected(utility.getSelected(flexiDetailBottomSheet.getUnselected()));
		return flexiDetailBottomSheetResponse;
	}

}
