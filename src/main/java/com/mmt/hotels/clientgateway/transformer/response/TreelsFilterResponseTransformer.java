package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfiguration;
import com.mmt.hotels.clientgateway.businessobjects.FilterPillConfigurationWrapper;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.request.TreelsFilterCountRequest;
import com.mmt.hotels.clientgateway.response.filter.Filter;
import com.mmt.hotels.clientgateway.response.filter.FilterCategory;
import com.mmt.hotels.clientgateway.response.filter.FilterResponse;
import com.mmt.hotels.clientgateway.response.listing.ExploreMoreData;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.service.TreelsFilterService;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.FilterSearchMetaDataResponse;
import com.mmt.hotels.model.response.searchwrapper.TreelsCTA;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import static com.mmt.hotels.clientgateway.constants.Constants.FLEX;
import static com.mmt.hotels.clientgateway.constants.Constants.TILE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FIND_INSPIRATION;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.POPULAR_LOCATION;

@Component
public class TreelsFilterResponseTransformer {

    @Value("${explore.more.image}")
    private String exploreMoreImage;

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    private CommonConfigHelper commonConfigHelper;

    private static final Logger logger = LoggerFactory.getLogger(TreelsFilterResponseTransformer.class);

    public FilterResponse convertFilterResponse(FilterSearchMetaDataResponse filterResponseHES, TreelsFilterCountRequest filterRequest) {
        if(filterResponseHES ==null || MapUtils.isEmpty(filterResponseHES.getFilterDataMap())) {
            return null;
        }
        FilterResponse filterResponse = new FilterResponse();
        List<FilterCategory> filterCategoryList = new ArrayList<>();
        filterResponseHES.getFilterDataMap().entrySet().stream().forEach(entry -> {
            if(filterRequest!=null && CollectionUtils.isNotEmpty(filterRequest.getFilterRemovedCriteria())) {
                logger.warn("Filter removed criteria is not empty");
                filterRequest.getFilterRemovedCriteria().forEach(excludedFilter -> {
                    if (entry.getKey().name().equalsIgnoreCase(excludedFilter.getFilterGroup().name())) {
                        FilterCategory filterCategory = buildFilterCategory(entry.getKey(), entry.getValue(), filterRequest.getFilterRemovedCriteria());
                        if(!filterCategoryList.contains(filterCategory)) {
                            filterCategoryList.add(filterCategory);
                        }
                    }
                });
            } else {
                logger.warn("Filter removed criteria is empty");
                filterCategoryList.add(buildFilterCategory(entry.getKey(), entry.getValue(), filterRequest.getFilterRemovedCriteria()));
            }
        });

        filterResponse.setFilterList(filterCategoryList);
        if(CollectionUtils.isNotEmpty(filterRequest.getFilterRemovedCriteria())) {
            filterResponse.setExploreMoreData(buildExploreMoreData(filterResponseHES.getCta(), filterRequest.getFilterRemovedCriteria()));
            filterResponse.setAllTreelsShown(false);
        }
        return filterResponse;
    }

    private ExploreMoreData buildExploreMoreData(TreelsCTA cta, List<com.mmt.hotels.clientgateway.request.Filter> removalFilters) {
        StringBuilder st = new StringBuilder();
        for(int i=0; i<removalFilters.size(); i++) {
            if(i==0) {
                st.append(removalFilters.get(i).getTitle());
            } else if(i == removalFilters.size()-1) {
                st.append(Constants.SPACE).append(Constants.AND_STRING).append(Constants.SPACE).append(removalFilters.get(i).getTitle());
            } else {
                st.append(Constants.COMMA).append(Constants.SPACE).append(removalFilters.get(i).getTitle());
            }
        }
        ExploreMoreData exploreMoreData = new ExploreMoreData();
        exploreMoreData.setHeader(polyglotService.getTranslatedData(ConstantsTranslation.FILTERED_TREEL_HEADING));
        exploreMoreData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.FILTERED_TREEL_TITLE).replace("{0}", st.toString()));
        exploreMoreData.setSubtitle(polyglotService.getTranslatedData(ConstantsTranslation.FILTERED_TREEL_SUB_TITLE));
        exploreMoreData.setImageUrl(exploreMoreImage);
        exploreMoreData.setCta(cta);
        return exploreMoreData;
    }

    private FilterCategory buildFilterCategory(FilterGroup key, List<com.mmt.hotels.filter.Filter> value, List<com.mmt.hotels.clientgateway.request.Filter> exclusionFilters) {
        FilterCategory filterCategoryCG = new FilterCategory();
        filterCategoryCG.setCategoryName(key.name());
        filterCategoryCG.setVisible(true);
        filterCategoryCG.setFilters(CollectionUtils.isEmpty(exclusionFilters)?buildFiltersList(key, value):buildFiltersWithExclusions(key, value, exclusionFilters));
        filterCategoryCG.setViewType(key.name().equalsIgnoreCase(FilterGroup.LOCATION.name())?TILE:FLEX); // this needs to be changed later when more filters are added
        filterCategoryCG.setTitle(key.name().equalsIgnoreCase(FilterGroup.LOCATION.name())?polyglotService.getTranslatedData(POPULAR_LOCATION):polyglotService.getTranslatedData(FIND_INSPIRATION));
        return filterCategoryCG;
    }

    private List<Filter> buildFiltersWithExclusions(FilterGroup key, List<com.mmt.hotels.filter.Filter> value, List<com.mmt.hotels.clientgateway.request.Filter> exclusionFilters) {
        logger.warn("Building filters only for filters which were excluded previously");
        List<Filter> filterList = buildFiltersList(key, value);
        Integer treelFilterSize = commonConfigHelper.getTreelsFilterSize();
        int sizeOfFilter = treelFilterSize!=null?treelFilterSize:6;
        int sizeOfExclusionfilters = exclusionFilters.size()!=0?exclusionFilters.size():1;
        exclusionFilters.forEach(excludedFilter -> {
            filterList.removeIf(filter -> filter.getFilterValue().equalsIgnoreCase(excludedFilter.getFilterValue()));
        });
        if(filterList.size() > sizeOfFilter/sizeOfExclusionfilters) {
            return filterList.subList(0, sizeOfFilter/sizeOfExclusionfilters);
        }
        return filterList;
    }

    private List<Filter> buildFiltersList(FilterGroup key, List<com.mmt.hotels.filter.Filter> value) {
        logger.warn("Building filters list for all filters received from HES");
        List<Filter> filterList = new ArrayList<>();
        if(CollectionUtils.isEmpty(value)) {
            return filterList;
        }
        value.forEach(filter -> {
            if(filter!=null) {
                Filter filterCG = new Filter();
                filterCG.setFilterValue(filter.getFilterValue());
                filterCG.setFilterGroup(key.name());
                filterCG.setTitle(filter.getTitle());
                filterCG.setImageUrl(filter.getIconUrl());
                filterCG.setMatchmakerType(filter.getLocusType());
                filterCG.setLocusId(filter.getLocusId());
                filterCG.setLocusType(filter.getLocusType());
                filterList.add(filterCG);
            }
        });
        return filterList;
    }

}
