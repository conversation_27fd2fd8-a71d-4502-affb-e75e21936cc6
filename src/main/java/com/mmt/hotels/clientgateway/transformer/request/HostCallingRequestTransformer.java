package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.hostcalling.HostCallingInitiateRequestBody;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.util.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Abstract transformer class for converting ClientGateway HostCallingInitiateRequestBody 
 * to HES HostCallingInitiateRequestBody
 * Following the exact same pattern as StaticDetailRequestTransformer
 */
public abstract class HostCallingRequestTransformer {

    private static final Logger LOGGER = LoggerFactory.getLogger(HostCallingRequestTransformer.class);

    @Autowired
    Utility utility;

    /**
     * Convert ClientGateway HostCallingInitiateRequestBody to HES HostCallingInitiateRequestBody
     * Following the exact same pattern as StaticDetailRequestTransformer.convertStaticDetailRequest()
     */
    public com.mmt.hotels.pojo.request.detail.mob.HostCallingInitiateRequestBody convertHostCallingRequest(
            HostCallingInitiateRequestBody hostCallingRequest, 
            CommonModifierResponse commonModifierResponse) {
        
        com.mmt.hotels.pojo.request.detail.mob.HostCallingInitiateRequestBody hesRequest = 
            new com.mmt.hotels.pojo.request.detail.mob.HostCallingInitiateRequestBody();

        
        // Build components using helper methods - exact same pattern as StaticDetailRequestTransformer
        buildDeviceDetails(hesRequest, hostCallingRequest.getDeviceDetails());
        buildSearchCriteria(hesRequest, hostCallingRequest);
        RequestDetails requestDetails = hostCallingRequest.getRequestDetails();
        buildRequestDetails(hesRequest, requestDetails);
        
        // Set fields from CommonModifierResponse - following StaticDetailRequestTransformer pattern
        if(commonModifierResponse.getExtendedUser() != null){
            hesRequest.setUuid(commonModifierResponse.getExtendedUser().getUuid());
            hesRequest.setProfileType(commonModifierResponse.getExtendedUser().getProfileType());			
            hesRequest.setSubProfileType(commonModifierResponse.getExtendedUser().getAffiliateId());
            hesRequest.setAgencyUUID(Utility.fetchAgencyUUIDFromCorp(commonModifierResponse.getExtendedUser().getCorporateData()));
        }
        hesRequest.setExperimentData(hostCallingRequest.getExpData());
        hesRequest.setSiteDomain(requestDetails.getSiteDomain());
        hesRequest.setApplicationId(commonModifierResponse.getApplicationId());
        hesRequest.setDomain(Constants.B2C);
        if(commonModifierResponse.getHydraResponse() != null){
            hesRequest.setHydraSegments(commonModifierResponse.getHydraResponse().getHydraMatchedSegment());			
        }
        hesRequest.setMobile(commonModifierResponse.getMobile());
        hesRequest.setMcid(commonModifierResponse.getMcId());
        hesRequest.setPageContext(Constants.PAGE_CONTEXT_DETAIL);
        hesRequest.setRequestType("B2CAgent");
        hesRequest.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());
        hesRequest.setBusinessIdentificationEnableFromUserService(utility.isBusinessIdentificationEnableFromUserService(commonModifierResponse.getExtendedUser()));
        hesRequest.setUserLocation(commonModifierResponse.getUserLocation());
        
        return hesRequest;
    }
    
    /**
     * Build device details - following StaticDetailRequestTransformer.buildDeviceDetails pattern
     */
    private void buildDeviceDetails(com.mmt.hotels.pojo.request.detail.mob.HostCallingInitiateRequestBody hesRequest, 
                                   DeviceDetails deviceDetails) {
        if (deviceDetails != null) {
            hesRequest.setAppVersion(deviceDetails.getAppVersion());
            hesRequest.setBookingDevice(deviceDetails.getBookingDevice());
            hesRequest.setDeviceId(deviceDetails.getDeviceId());
            hesRequest.setDeviceType(deviceDetails.getDeviceType());
            hesRequest.setNetworkType(deviceDetails.getNetworkType());
            hesRequest.setDeviceName(deviceDetails.getDeviceName());
        }
    }

    /**
     * Build search criteria - following StaticDetailRequestTransformer.buildSearchCriteria pattern
     */
    private void buildSearchCriteria(com.mmt.hotels.pojo.request.detail.mob.HostCallingInitiateRequestBody hesRequest,
                                    HostCallingInitiateRequestBody hostCallingInitiateRequestBody) {
        if (hostCallingInitiateRequestBody.getSearchCriteria() != null) {
            hesRequest.setHotelId(hostCallingInitiateRequestBody.getSearchCriteria().getHotelId());
            hesRequest.setCheckin(hostCallingInitiateRequestBody.getSearchCriteria().getCheckIn());
            hesRequest.setCheckout(hostCallingInitiateRequestBody.getSearchCriteria().getCheckOut());
            hesRequest.setCountryCode(hostCallingInitiateRequestBody.getSearchCriteria().getCountryCode());
            hesRequest.setCityCode(hostCallingInitiateRequestBody.getSearchCriteria().getCityCode());
            hesRequest.setLocationId(hostCallingInitiateRequestBody.getSearchCriteria().getLocationId());
            hesRequest.setLocationType(hostCallingInitiateRequestBody.getSearchCriteria().getLocationType());
            hesRequest.setCurrency(hostCallingInitiateRequestBody.getSearchCriteria().getCurrency());
            hesRequest.setRoomStayCandidates(buildRoomStayCandidates(hostCallingInitiateRequestBody.getSearchCriteria().getRoomStayCandidates()));
        }
    }

    /**
     * Build request details - following StaticDetailRequestTransformer.buildRequestDetails pattern
     */
    private void buildRequestDetails(com.mmt.hotels.pojo.request.detail.mob.HostCallingInitiateRequestBody hesRequest,
                                    RequestDetails requestDetails) {
        if (requestDetails != null) {
            hesRequest.setVisitorId(requestDetails.getVisitorId());
            hesRequest.setIdContext(requestDetails.getIdContext());
            hesRequest.setVisitNumber(String.valueOf(requestDetails.getVisitNumber()));
            hesRequest.setLoggedIn(requestDetails.isLoggedIn());
            hesRequest.setFunnelSource(requestDetails.getFunnelSource());
            hesRequest.setJourneyId(requestDetails.getJourneyId());
        }
    }
    
    /**
     * Build room stay candidates - following StaticDetailRequestTransformer.buildRoomStayCandidates pattern
     */
    private java.util.List<com.mmt.hotels.model.request.RoomStayCandidate> buildRoomStayCandidates(
            java.util.List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        if (roomStayCandidates == null) return null;
        
        java.util.List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidateList = new java.util.ArrayList<>();

        for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate : roomStayCandidates){
            com.mmt.hotels.model.request.RoomStayCandidate roomStayCandidateCB = new com.mmt.hotels.model.request.RoomStayCandidate();
            roomStayCandidateCB.setGuestCounts(buildGuestCounts(roomStayCandidate));
            roomStayCandidateList.add(roomStayCandidateCB);
        }
        return roomStayCandidateList;
    }
    
    /**
     * Build guest counts - following StaticDetailRequestTransformer.buildGuestCounts pattern
     */
    private java.util.List<com.mmt.hotels.model.request.GuestCount> buildGuestCounts(
            com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate) {
        java.util.List<com.mmt.hotels.model.request.GuestCount> guestCounts = new java.util.ArrayList<>();
        com.mmt.hotels.model.request.GuestCount guestCount = new com.mmt.hotels.model.request.GuestCount();
        guestCount.setAgeQualifyingCode("10");
        guestCount.setAges(roomStayCandidate.getChildAges());
        guestCount.setCount(String.valueOf(roomStayCandidate.getAdultCount()));
        guestCounts.add(guestCount);
        return guestCounts;
    }
} 