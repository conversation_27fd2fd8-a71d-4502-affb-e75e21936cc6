package com.mmt.hotels.clientgateway.transformer.response;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.util.Utility;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.response.Address;
import com.mmt.hotels.clientgateway.response.Coupon;
import com.mmt.hotels.clientgateway.response.EMIDetail;
import com.mmt.hotels.clientgateway.response.LocationDetail;
import com.mmt.hotels.clientgateway.response.PriceDetail;
import com.mmt.hotels.clientgateway.response.listingmap.ListingMapResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.SoldOutInfoCG;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.model.response.emi.Emi;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.pricing.DisplayPriceBreakDown;
import com.mmt.hotels.model.response.searchwrapper.SoldOutInfo;
import com.mmt.hotels.pojo.response.HotelListingMapResponse;
import com.mmt.hotels.pojo.response.ListingHotelMapEntity;
import com.mmt.model.LocusData;

import static com.mmt.hotels.clientgateway.constants.Constants.COMBINED_OTA;
import static com.mmt.hotels.clientgateway.constants.Constants.USER_CURRENCY;

@Component
public class ListingMapResponseTransformer {

    @Autowired
    CommonResponseTransformer commonResponseTransformer;

    @Autowired
    ShortstaysListingMapResponseTransformer shortstaysResponseTransformer;
    
	@Autowired
	private PolyglotService polyglotService;
	
	@Autowired
    PersuasionUtil persuasionUtil;

    @Autowired
    private Utility utility;

    private static final Logger LOGGER = LoggerFactory.getLogger(ListingMapResponseTransformer.class);
    

    public ListingMapResponse convertListingMapResponse(HotelListingMapResponse listingMapResponse, Map<String, String> expDataMap, DeviceDetails deviceDetails, RequestDetails requestDetails, CommonModifierResponse commonModifierResponse) {
        if (listingMapResponse == null) {
            return null;
        }

        ListingMapResponse listingMapResponseCG = new ListingMapResponse();
        listingMapResponseCG.setHotelCount(listingMapResponse.getTotalHotelCounts());
        listingMapResponseCG.setHotelCountInCity(listingMapResponse.getHotelCountInCity());
        listingMapResponseCG.setHotelCountInViewPort(listingMapResponse.getHotelCountInViewPort());
        listingMapResponseCG.setNoMoreHotels(listingMapResponse.isNoMoreAvailableHotels());
        listingMapResponseCG.setLocationDetail(buildLocationDetail(listingMapResponse));
        listingMapResponseCG.setCityLocationDetail(buildLocationDetail(listingMapResponse));
        listingMapResponseCG.setHotels(buildHotelList(listingMapResponse.getHotelList(), listingMapResponse.getCountryCode(),listingMapResponse.getLocusData(), expDataMap,deviceDetails,requestDetails, commonModifierResponse));
        listingMapResponseCG.setPois(commonResponseTransformer.getPois(listingMapResponse.getPoiList()));

        //central city details and associated city details are transformed for SHORTSTAYS funnel
        listingMapResponseCG.setCentralCityGeoDetails(shortstaysResponseTransformer.buildCentralCityGeoDetails(listingMapResponse.getShortstaysZoneResult()));
        listingMapResponseCG.setAssociatedCitiesGeoConfig(shortstaysResponseTransformer.buildAssociatedCitiesGeoConfig(listingMapResponse.getShortstaysZoneResult()));
        if(listingMapResponse.getLuckyUserContext() != null){
            listingMapResponseCG.setLuckyUserContext(listingMapResponse.getLuckyUserContext());
        }
        listingMapResponseCG.setCurrency(listingMapResponse.getCurrency());
        return listingMapResponseCG;
    }


    private List<Hotel> buildHotelList(List<ListingHotelMapEntity> hotelList, String countryCode, LocusData locusData, Map<String, String> expDataMap,DeviceDetails deviceDetails, RequestDetails requestDetails, CommonModifierResponse commonModifierResponse) {

        if (hotelList == null) {
            return null;
        }

        List<Hotel> hotelListCG  = new ArrayList<>();

        for (ListingHotelMapEntity hotelCB: hotelList){
            Hotel hotelCG  = new Hotel();

            hotelCG.setId(hotelCB.getId());
            hotelCG.setName(hotelCB.getName());
            hotelCG.setMaskedPropertyName(hotelCB.isMaskedPropertyName());
            hotelCG.setPropertyType(hotelCB.getPropertyType());
            hotelCG.setPropertyLabel(hotelCB.getPropertyLabel());
            hotelCG.setStayType(hotelCB.getStayType());
            hotelCG.setLocationDetail(buildLocationInfo(hotelCB));
            hotelCG.setStarRating(hotelCB.getStarRating());
            hotelCG.setAddress(buildAddress(hotelCB.getAddress()));
            hotelCG.setGeoLocation(commonResponseTransformer.buildGeoLocation(hotelCB.getGeoLocation()));
            hotelCG.setPriceDetail(buildPriceDetail(hotelCB.getDisplayFare(), commonResponseTransformer.enableSaveValue(expDataMap)));
            // user rating
            //On Old apps combined OTAs like MMT_BKG not supported, hence changing such OTA to MMT for old apps using exp COMBINED_OTA
            boolean combinedOTASupported = utility.isExperimentOn(expDataMap, COMBINED_OTA);
            if (MapUtils.isNotEmpty(expDataMap) && Constants.TRUE.equalsIgnoreCase(expDataMap.get(Constants.UNIFIED_USER_RATING))) {
                hotelCG.setReviewSummary(commonResponseTransformer.buildReviewSummary(hotelCB.getReviewSummary(), combinedOTASupported));
            } else {
                hotelCG.setReviewSummary(commonResponseTransformer.buildReviewSummary(countryCode, hotelCB.getFlyfishReviewSummary(), combinedOTASupported));
            }

            hotelCG.setSoldOut(hotelCB.getIsSoldOut() != null ? hotelCB.getSoldOut() : false);
            hotelCG.setSoldOutInfo(buildSoldOutInfo(hotelCB.getSoldOutInfo()));
            hotelCG.setMedia(commonResponseTransformer.buildMedia(hotelCB.getMainImages(),null, expDataMap, false));
            hotelCG.setLocationPersuasion(hotelCB.getLocationPersuasion());
            hotelCG.setHotelPersuasions(hotelCB.getHotelPersuasions());
            if(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(requestDetails.getIdContext())) {
                commonResponseTransformer.buildTopSectionPersuasion(hotelCG, hotelCB.getTag(), Constants.LISTING_MAP, deviceDetails != null ? deviceDetails.getBookingDevice() : null);
                commonResponseTransformer.removePlaceHolderPersuasionsForSection(hotelCG, Constants.LISTING_MAP);
            }
            String drivingTimeText = "";
            if(requestDetails!=null && Constants.SHORTSTAYS_FUNNEL.equalsIgnoreCase(requestDetails.getFunnelSource())
					&& commonResponseTransformer.isEligibleForNearbyFlow(deviceDetails)){
				drivingTimeText = commonResponseTransformer.buildDrivingTimeText(hotelCB.getDrivingTime());
				persuasionUtil.addShortStayPeithoPersuasionToHotelPersuasion(hotelCG,hotelCB.getUspShortStayValue() ,hotelCB.getLocationPersuasion());
			}
            addLocationPersuasionToHotelPersuasions(hotelCG, hotelCB.getLocationPersuasion(),drivingTimeText,locusData,requestDetails);
            hotelCG.setCategories(hotelCB.getCategories());
            if(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(requestDetails.getIdContext())) {
                hotelCG.setMyBizAssured(org.apache.commons.collections.CollectionUtils.isNotEmpty(hotelCB.getCategories()) && hotelCB.getCategories().contains(Constants.MYBIZ_ASSURED));
            }
            if(hotelCB.getDisplayFare()!=null) {
                hotelCG.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(hotelCB.getDisplayFare().getCorpMetaData(), utility.isTCSV2FlowEnabled(commonModifierResponse!=null?commonModifierResponse.getExpDataMap():null)));
            }
            hotelCG.setFreeCancellationText(hotelCB.getFreeCancellationText());
            hotelCG.setMmtHotelCategory(hotelCB.getMmtHotelCategory());
            hotelCG.setIsAltAcco(hotelCB.isAltAcco());
            hotelCG.setTotalRoomCount((hotelCB.getDisplayFare() != null && hotelCB.getDisplayFare().getTotalRoomCount() != null) ? hotelCB.getDisplayFare().getTotalRoomCount() : null);
            hotelListCG.add(hotelCG);
        }
        return hotelListCG;
    }

    private PriceDetail buildPriceDetail(DisplayFare displayFare, boolean enableSaveValue) {

        if (displayFare == null || displayFare.getDisplayPriceBreakDown() == null) {
            return null;
        }

        DisplayPriceBreakDown displayPriceBreakDown = displayFare.getDisplayPriceBreakDown();
        PriceDetail priceDetail = new PriceDetail();
        priceDetail.setDisplayPrice(displayFare.getDisplayPriceBreakDown().getDisplayPrice());
        priceDetail.setNonDiscountedPrice(displayFare.getDisplayPriceBreakDown().getNonDiscountedPrice());
        priceDetail.setCoupon(buildCoupon(displayPriceBreakDown.getCouponInfo()));
        priceDetail.setEmiDetails(buildEmiDetails(displayPriceBreakDown.getEmiDetails()));
        priceDetail.setTotalTax(displayPriceBreakDown.getTotalTax());
        if(enableSaveValue) {
            priceDetail.setTotalSaving(displayPriceBreakDown.getTotalSaving());
            priceDetail.setSavingPerc(displayPriceBreakDown.getSavingPerc());
        }
        priceDetail.setPrice(displayPriceBreakDown.getNonDiscountedPrice() - (displayPriceBreakDown.isTaxIncluded() ?
                                                                              displayPriceBreakDown.getTotalTax() : 0));
        priceDetail.setPriceWithTax(
            displayPriceBreakDown.getNonDiscountedPrice() + (displayPriceBreakDown.isTaxIncluded() ? 0 :
                                                             displayPriceBreakDown.getTotalTax()));
        priceDetail.setDiscountedPrice(
            displayPriceBreakDown.getDisplayPrice() - (displayPriceBreakDown.isTaxIncluded() ?
                                                       displayPriceBreakDown.getTotalTax() : 0));
        priceDetail.setDiscountedPriceWithTax(
            displayPriceBreakDown.getDisplayPrice() + (displayPriceBreakDown.isTaxIncluded() ? 0 :
                                                       displayPriceBreakDown.getTotalTax()));
        priceDetail.setPricingKey(displayPriceBreakDown.getPricingKey());

        return priceDetail;
    }

    private Coupon buildCoupon(BestCoupon couponInfo) {
        if (couponInfo == null) {
            return null;
        }
        Coupon coupon = new Coupon();
        coupon.setDescription(couponInfo.getDescription());
        coupon.setCode(couponInfo.getCouponCode());
        coupon.setType(couponInfo.getType());
        coupon.setSpecialPromo(couponInfo.isSpecialPromoCoupon());
        return coupon;
    }

    private EMIDetail buildEmiDetails(Emi emiInfo){
        if (emiInfo == null) {
            return null;
        }
        EMIDetail emiDetail = new EMIDetail();
        emiDetail.setAmount((double) emiInfo.getEmiAmount());
        emiDetail.setType(emiInfo.getEmiType());
        emiDetail.setBankName(emiInfo.getBankName());
        emiDetail.setTenure(emiInfo.getTenure());
        emiDetail.setTotalCost(emiInfo.getTotalCost());
        emiDetail.setTotalInterest(emiInfo.getTotalInterest());
        return emiDetail;
    }

    private SoldOutInfoCG buildSoldOutInfo(SoldOutInfo soldOutInfo){
        if (soldOutInfo == null)
            return null;
        SoldOutInfoCG soldOutInfoCG = new SoldOutInfoCG();
        soldOutInfoCG.setSoldOutText(soldOutInfo.getSoldOutText());
        soldOutInfoCG.setSoldOutSubText(soldOutInfo.getSoldOutSubText());
        soldOutInfoCG.setSoldOutReason(soldOutInfo.getSoldOutReason());
        soldOutInfoCG.setSoldOutType(soldOutInfo.getSoldOutType());
        return soldOutInfoCG;
    }

    private Address buildAddress(com.mmt.hotels.model.response.staticdata.Address address) {

        if (address == null)
            return null;

        Address addressCG  = new Address();
        if (CollectionUtils.isNotEmpty(address.getArea())) {
            addressCG.setArea(address.getArea().get(0));
        }
        addressCG.setLine1(address.getLine1());
        addressCG.setLine2(address.getLine2());

        return addressCG;
    }

    private LocationDetail buildLocationInfo(ListingHotelMapEntity hotelCB) {
        if (hotelCB == null) {
            return null;
        }
        LocationDetail locationDetail = new LocationDetail(hotelCB.getCityCode(), hotelCB.getCityName(),
                                                           Constants.TYPE_CITY,
                                                           hotelCB.getCountryCode(), hotelCB.getCountryName());
        return locationDetail;
    }

    private LocationDetail buildLocationDetail(HotelListingMapResponse listingMapResponse) {

        LocationDetail locationDetail = new LocationDetail();
        if (listingMapResponse.getLocusData() != null) {
            locationDetail.setId(listingMapResponse.getLocusData().getLocusId());
            locationDetail.setType(listingMapResponse.getLocusData().getLocusType());
            locationDetail.setName(listingMapResponse.getLocusData().getLocusName());
        } else {
            locationDetail.setId(listingMapResponse.getCityCode());
            locationDetail.setName(listingMapResponse.getCityName());
            locationDetail.setType("city");
        }
        locationDetail.setCountryName(listingMapResponse.getCountryName());
        locationDetail.setCountryId(listingMapResponse.getCountryCode());

        return locationDetail;
    }

    public void addLocationPersuasionToHotelPersuasions(Hotel hotel, List<String> locationPersuasion,String drivingTimeText,LocusData locusData,RequestDetails requestDetails) {
        if (CollectionUtils.isNotEmpty(locationPersuasion)) {
            if (hotel.getHotelPersuasions() == null) {
                hotel.setHotelPersuasions(new HashMap<String, Object>());
            }
            PersuasionObject locPers = new PersuasionObject();
            locPers.setData(new ArrayList<>());
            locPers.setPlaceholder(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP);
            locPers.setTemplate("IMAGE_TEXT_H");
            locPers.setPlaceholder("SINGLE");

            int index = 1;
            PersuasionData locPersuasionData = new PersuasionData();
            locPersuasionData.setHasAction(false);
            locPersuasionData.setHtml(true);
            locPersuasionData.setId("LOC_PERSUASION_" + index++);
            locPersuasionData.setPersuasionType("LOCATION");
            PersuasionStyle style = new PersuasionStyle();
            style.setMaxLines(2);
            locPersuasionData.setStyle(style);

            locPers.getData().add(locPersuasionData);
            if (locationPersuasion.size() == 1) {
                locPersuasionData.setText(locationPersuasion.get(0));
            } else if (locationPersuasion.size() >= 2) {
                locPersuasionData.setText(locationPersuasion.get(0) + " | " + locationPersuasion.get(1));
            }
            
            if(requestDetails!=null && Constants.SHORTSTAYS_FUNNEL.equalsIgnoreCase(requestDetails.getFunnelSource())) {
            	if(drivingTimeText == null || locusData == null) {
            		LOGGER.warn("Location Persuasion could not be added for ShortStay Funnel because either driving_duration or city_name is empty");
            		return;
            	}
            	StringBuilder updatedLocationPersuasionText = new StringBuilder();
				String locText = polyglotService.getTranslatedData(ConstantsTranslation.SHORTSTAY_LOCATION_PERSUASION_PREFIX_TEXT_LISTING_MAP);
				locText = locText.replace("{city_text}", locationPersuasion.get(0));
				updatedLocationPersuasionText.append(locText);
				updatedLocationPersuasionText.append(Constants.SPACE);
                String finalDrivingText = polyglotService.getTranslatedData(ConstantsTranslation.SHORTSTAY_LOCATION_PERSUASION_SUFFIX__LISTING_MAP);
                String drivingText = polyglotService.getTranslatedData(ConstantsTranslation.DRIVING_DURATION_ZONE_SHORTSTAY).replace("{duration}", drivingTimeText);
				drivingText = drivingText.replace("{city_name}", locusData.getLocusName());
				finalDrivingText = finalDrivingText.replace("{driving_text}", drivingText);
				updatedLocationPersuasionText.append(finalDrivingText);
				locPersuasionData.setText(updatedLocationPersuasionText.toString());
			}

            try {
                ((Map<Object, Object>) hotel.getHotelPersuasions())
                    .put(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP, locPers);
            } catch (ClassCastException e) {
                LOGGER.error("Location Persuasion could not be added in Listing Map due to ClassCastException : {} ", e.getMessage());
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added in Listing Map due to : {} ", e.getMessage());
            }

        }
    }
}
