package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchTravellerReviewsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchTravellerSummaryResponseTransformer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class OrchTravellerReviewSummaryFactory {

    @Autowired
    private OrchTravellerSummaryResponseTransformer summaryResponseTransformer;

    @Autowired
    private OrchTravellerReviewsResponseTransformer reviewsResponseTransformer;

    public OrchTravellerSummaryResponseTransformer getOrchSummaryResponseService(String client) {
        return summaryResponseTransformer;
//        If required in the future, uncomment below transformer and use these
//        if (StringUtils.isEmpty(client))
//            return summaryResponseTransformer;
//        switch (client) {
//            case "PWA":
//                return summaryResponseTransformerPWA;
//            case "DESKTOP":
//                return summaryResponseTransformerDesktop;
//            case "ANDROID":
//                return summaryResponseTransformerAndroid;
//            case "IOS":
//                return summaryResponseTransformerIOS;
//        }
    }


    public OrchTravellerReviewsResponseTransformer getOrchReviewsResponseService(String client) {
        return reviewsResponseTransformer;
//        If required in the future, uncomment below transformer and use these
//        if (StringUtils.isEmpty(client))
//            return reviewsResponseTransformer;
//        switch (client) {
//            case "PWA":
//                return reviewsResponseTransformerPWA;
//            case "DESKTOP":
//                return reviewsResponseTransformerDesktop;
//            case "ANDROID":
//                return reviewsResponseTransformerAndroid;
//            case "IOS":
//                return reviewsResponseTransformerIOS;
//        }
    }
}
