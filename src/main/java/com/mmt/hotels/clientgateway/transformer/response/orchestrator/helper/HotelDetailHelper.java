package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.enums.RatingCategory;
import com.gommt.hotels.orchestrator.detail.model.response.common.DisplayItem;
import com.gommt.hotels.orchestrator.detail.model.response.content.FrequentlyAskedQuestion;
import com.gommt.hotels.orchestrator.detail.model.response.content.HotelMetaData;
import com.gommt.hotels.orchestrator.detail.model.response.content.LocationInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.PropertyChain;
import com.gommt.hotels.orchestrator.detail.model.response.content.PropertyChainDetails;
import com.gommt.hotels.orchestrator.detail.model.response.content.RulesAndPolicies;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup;
import com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CategoryInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.meals.MealRules;
import com.gommt.hotels.orchestrator.detail.model.response.content.meals.MealRulesData;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.RatingData;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.StaticDetailCriteria;
import com.mmt.hotels.clientgateway.request.StaticDetailRequest;
import com.mmt.hotels.clientgateway.request.UserGlobalInfo;
import com.mmt.hotels.clientgateway.response.Address;
import com.mmt.hotels.clientgateway.response.LocationDetail;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.Style;
import com.mmt.hotels.clientgateway.response.moblanding.CardAction;
import com.mmt.hotels.clientgateway.response.moblanding.CardActionData;
import com.mmt.hotels.clientgateway.response.moblanding.Item;
import com.mmt.hotels.clientgateway.response.moblanding.Section;
import com.mmt.hotels.clientgateway.response.moblanding.SupportDetails;
import com.mmt.hotels.clientgateway.response.rooms.SharedInfo;
import com.mmt.hotels.clientgateway.response.rooms.Space;
import com.mmt.hotels.clientgateway.response.rooms.SpaceData;
import com.mmt.hotels.clientgateway.response.searchHotels.CalendarCriteria;
import com.mmt.hotels.clientgateway.response.staticdetail.CategoryTag;
import com.mmt.hotels.clientgateway.response.staticdetail.ChildExtraBedPolicy;
import com.mmt.hotels.clientgateway.response.staticdetail.CommonRules;
import com.mmt.hotels.clientgateway.response.staticdetail.DiningRuleItem;
import com.mmt.hotels.clientgateway.response.staticdetail.ExtraBedRules;
import com.mmt.hotels.clientgateway.response.staticdetail.FaqData;
import com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningRule;
import com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningV2;
import com.mmt.hotels.clientgateway.response.staticdetail.GovtPolicies;
import com.mmt.hotels.clientgateway.response.staticdetail.HomeStayAwardDetails;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.response.staticdetail.HouseRules;
import com.mmt.hotels.clientgateway.response.staticdetail.HouseRulesV2;
import com.mmt.hotels.clientgateway.response.staticdetail.MealType;
import com.mmt.hotels.clientgateway.response.staticdetail.PolicyRules;
import com.mmt.hotels.clientgateway.response.staticdetail.PropertyChainCG;
import com.mmt.hotels.clientgateway.response.staticdetail.PropertyChainDetailCG;
import com.mmt.hotels.clientgateway.response.staticdetail.PropertyHighlightCG;
import com.mmt.hotels.clientgateway.response.staticdetail.PropertyHighlightDetailCG;
import com.mmt.hotels.clientgateway.response.staticdetail.ReportCardPersuasion;
import com.mmt.hotels.clientgateway.response.staticdetail.Restaurant;
import com.mmt.hotels.clientgateway.response.staticdetail.Rule;
import com.mmt.hotels.clientgateway.response.staticdetail.StreetViewInfo;
import com.mmt.hotels.clientgateway.response.staticdetail.SummaryItem;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.ReArchUtility;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.matchmaker.LatLngObject;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.model.request.matchmaker.Tags;
import com.mmt.hotels.clientgateway.response.staticdetail.ExperiencesCardData;
import com.mmt.hotels.model.response.searchwrapper.MmtHotelCategory;
import com.mmt.hotels.model.response.staticdata.CallingInfo;
import com.mmt.hotels.model.response.staticdata.ContextRules;
import com.mmt.hotels.model.response.staticdata.Datum;
import com.mmt.hotels.model.response.staticdata.Faqs;
import com.mmt.hotels.model.response.staticdata.HostInfoV2;
import com.mmt.hotels.model.response.staticdata.InfoData;
import com.mmt.hotels.model.response.staticdata.LatLong;
import com.mmt.hotels.model.response.staticdata.StaffInfo;
import com.mmt.hotels.pojo.FoodAndDining.FoodAndDiningEnums;
import com.mmt.model.UGCRatingData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import javax.annotation.Nullable;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.BEACHFRONT_CATEGORY_USP_DETAILS_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.EXPERIENCES_CARD_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.EXPERIENCES_CARD_TITLE_PREMIUM;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.EXPERIENCES_CARD_CTA;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.EXTRA_BED_POLICY;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FAQ_EXTRA_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FAQ_HINT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FAQ_ITEM_COUNT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FAQ_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FOOD_AND_DINING;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.LBI_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.RESTRICTIONS;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SINGLE_ENTIRE_PROPERTY_LAYOUT_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.STREET_VIEW_SUBTITLE_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.STREET_VIEW_TITLE_TEXT;
import static com.mmt.hotels.clientgateway.enums.ExperimentKeys.AMENITY_SECTION_RATING;
import static com.mmt.hotels.clientgateway.enums.ExperimentKeys.CHATBOT_ENABLED;
import static com.mmt.hotels.clientgateway.enums.ExperimentKeys.CHATBOT_HOOKS_EXP;
import static com.mmt.hotels.clientgateway.enums.ExperimentKeys.FOOD_DINING_SECTION_RATING;
import static com.mmt.hotels.clientgateway.util.DateUtil.MMDDYYYY;

public abstract class HotelDetailHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(HotelDetailHelper.class);

    @Autowired
    private ReArchUtility utility;

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private CommonResponseTransformer commonResponseTransformer;

    @Autowired
    private DateUtil dateUtil;

    @Value("#{'${suppressed.houseRules.list}'.split(',')}")
    private List<Integer> supressedHouseRulesList;

    @Value("${value.stay.icon}")
    private String iconUrl;

    @Value("${value.stay.icon.gcc}")
    private String iconUrlGcc;

    @Value("${mypartner.gst.assured.icon.url}")
    private String iconUrlGSTAssured;

    @Value("${food.dining.min.count.config}")
    private int foodDiningMinCountConfig;

    @Value("${loved.by.indians.icon.url}")
    private String lovedByIndiansIconUrl;

    @Value("${food.menu.position.config}")
    private int foodMenuPosition;

    @Value("#{'${food.dining.merge.sections}'.split(',')}")
    private List<String> foodDiningMergeSections;

    @Value("${luxe.icon.new.detail.page.dt}")
    private String luxeIconNewDetailPageDt;

    @Value("${groupBooking.deeplink.url}")
    private String groupBookingDeepLinkIndia;

    @Value("${groupBooking.deeplink.url.global}")
    private String groupBookingDeepLinkGlobal;

    @Value("${groupBooking.deeplink.url.myPartner}")
    private String groupBookingDeepLinkMyPartner;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    /**
     * Platform-specific abstract methods that concrete implementations must provide
     */
    protected abstract Map<String, String> buildCardTitleMap();

    protected abstract void addTitleData(HotelResult hotelResult, String countryCode, boolean isNewDetailPageDesktop);

    protected abstract String getLuxeIcon();

    public abstract StaffInfo convertStaffInfo(com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffInfo staffInfo);

    public abstract ReportCardPersuasion buildReportCardPersuasion(String popularText);

    public abstract String buildCategoryIcon(HotelMetaData hotelMetaData, boolean isCorpIdContext, Map<String, String> expDataMap);

    public HotelResult getHotelResult(StaticDetailRequest staticDetailRequest, HotelMetaData hotelMetaData,
                                      Map<String, String> expDataMap, StaticDetailCriteria staticDetailCriteria,
                                      CommonModifierResponse commonModifierResponse, DeviceDetails deviceDetails,
                                      boolean isCorpIdContext, boolean isGroupBookingFunnel, String funnel,
                                      boolean myPartnerReq, String affiliateId) {
        if (null == hotelMetaData) {
            return null;
        }

        boolean isLiteResponse = staticDetailRequest.getFeatureFlags() != null && staticDetailRequest.getFeatureFlags().isLiteResponse();
        boolean isNewDetailPageTrue = MapUtils.isNotEmpty(expDataMap) && utility.isExperimentTrue(expDataMap, NEW_DETAIL_PAGE);
        boolean isDisableHostChat = Utility.isGCC() && MapUtils.isNotEmpty(expDataMap) && utility.isExperimentTrue(expDataMap, DISABLE_HOST_CHAT);
        boolean isNewDetailPageDesktop = MapUtils.isNotEmpty(expDataMap) && utility.isExperimentOn(expDataMap, NEW_DETAIL_PAGE_DESKTOP_EXP);
        boolean isCallToBookV2Applicable = MapUtils.isNotEmpty(expDataMap) && utility.isExperimentValid(expDataMap, callToBook, 4);
        boolean enableHostCalling = MapUtils.isNotEmpty(expDataMap) && utility.isExperimentTrue(expDataMap, ENABLE_HOST_CALLING);


        boolean dhCall = true;
        if (hotelMetaData.getLocationInfo() != null && StringUtils.isNotEmpty(hotelMetaData.getLocationInfo().getCountryCode())) {
            dhCall = DOM_COUNTRY.equalsIgnoreCase(hotelMetaData.getLocationInfo().getCountryCode());
        }

        HotelResult hotelResult = new HotelResult();
        if (hotelMetaData.getPropertyDetails() != null && hotelMetaData.getPropertyDetails().getPropertyChain() != null && MapUtils.isNotEmpty(hotelMetaData.getPropertyDetails().getPropertyChain().getSummary())) {
            hotelResult.setPropertyChain(getPropertyChain(hotelMetaData.getPropertyDetails().getPropertyChain()));
        }
        if (hotelMetaData.getPropertyDetails() != null && hotelMetaData.getPropertyDetails().getPropertyHighlights() != null && CollectionUtils.isNotEmpty(hotelMetaData.getPropertyDetails().getPropertyHighlights().getDetails())) {
            PropertyHighlightCG propertyHighlightCG = getPropertyHighLights(hotelMetaData.getPropertyDetails().getPropertyHighlights());
            hotelResult.setPropertyHighlights(propertyHighlightCG);
        }
        hotelResult.setGroupBookingHotel(hotelMetaData.getPropertyFlags() != null && hotelMetaData.getPropertyFlags().isGroupBookingAllowed());
        if (hotelMetaData.getLocationInfo() != null) {
            Address address = new Address();
            List<String> addressLines = hotelMetaData.getLocationInfo().getAddressLines();
            if (addressLines != null && !addressLines.isEmpty()) {
                address.setLine1(addressLines.get(0));
                if (addressLines.size() > 1) {
                    address.setLine2(addressLines.get(1));
                }
            }
            hotelResult.setAddress(address);
            if (hotelMetaData.getLocationInfo().getStreetViewInfo() != null) {
                StreetViewInfo streetViewInfo = new StreetViewInfo();
                LatLong latLong = new LatLong();
                latLong.setLat(hotelMetaData.getLocationInfo().getStreetViewInfo().getLatitude());
                latLong.setLng(hotelMetaData.getLocationInfo().getStreetViewInfo().getLongitude());
                streetViewInfo.setLatLong(latLong);
                streetViewInfo.setPanoId(hotelMetaData.getLocationInfo().getStreetViewInfo().getPanoId());
                hotelResult.setStreetViewInfo(streetViewInfo);

                if (utility.isExperimentOn(expDataMap, ExperimentKeys.showstreetviewondetail.getKey())) {
                    modifyStreetViewInfo(hotelResult.getStreetViewInfo());
                }
            }
        }

        if (hotelMetaData.getLocationInfo() != null && StringUtils.isNotEmpty(hotelMetaData.getLocationInfo().getPrimaryArea())) {
            hotelResult.setPrimaryArea(hotelMetaData.getLocationInfo().getPrimaryArea());
        }
        hotelResult.setAltAcco(hotelMetaData.getPropertyDetails() != null && hotelMetaData.getPropertyFlags() != null && hotelMetaData.getPropertyFlags().isAltAcco());
        if (StringUtils.isNotEmpty(hotelMetaData.getPropertyDetails().getIngoHotelId())) {
            hotelResult.setIngoId(hotelMetaData.getPropertyDetails().getIngoHotelId());
        }
        if (hotelMetaData.getPropertyDetails() != null && hotelMetaData.getPropertyDetails().getCategories() != null) {
            hotelResult.setCategories(new ArrayList<>(hotelMetaData.getPropertyDetails().getCategories()));
        }

        if (hotelMetaData.getCheckInOutInfo() != null) {
            hotelResult.setCheckinTime(StringUtils.isNotEmpty(hotelMetaData.getCheckInOutInfo().getCheckInTimeRange()) ?
                    hotelMetaData.getCheckInOutInfo().getCheckInTimeRange() : hotelMetaData.getCheckInOutInfo().getCheckInTime());
            hotelResult.setCheckoutTime(StringUtils.isNotEmpty(hotelMetaData.getCheckInOutInfo().getCheckOutTimeRange()) ?
                    hotelMetaData.getCheckInOutInfo().getCheckOutTimeRange() : hotelMetaData.getCheckInOutInfo().getCheckOutTime());
            hotelResult.setCheckInTimeInRange(hotelMetaData.getCheckInOutInfo().isCheckInTimeInRange());
            hotelResult.setCheckOutTimeInRange(hotelMetaData.getCheckInOutInfo().isCheckOutTimeInRange());
        }


        hotelResult.setHotelIcon(hotelMetaData.getPropertyDetails().getHotelIcon());
        hotelResult.setId(hotelMetaData.getPropertyDetails().getId());
        hotelResult.setLat(hotelMetaData.getLocationInfo().getLatitude());
        hotelResult.setLng(hotelMetaData.getLocationInfo().getLongitude());
        hotelResult.setLocationDetail(buildLocationDetail(hotelMetaData.getLocationInfo().getLocationId(), hotelMetaData.getLocationInfo().getLocationName(),
                hotelMetaData.getLocationInfo().getCountryCode(), hotelMetaData.getLocationInfo().getCountryName()));
        hotelResult.setLongDesc(hotelMetaData.getPropertyDetails().getLongDescription());
        hotelResult.setName(hotelMetaData.getPropertyDetails().getName());
        hotelResult.setMaskedPropertyName(hotelMetaData.getPropertyFlags() != null && hotelMetaData.getPropertyFlags().isMaskedPropertyName());
        hotelResult.setPinCode(hotelMetaData.getLocationInfo().getPinCode());
        hotelResult.setPropertyType(hotelMetaData.getPropertyDetails().getPropertyType());
        hotelResult.setPropertyLabel(hotelMetaData.getPropertyDetails().getPropertyLabel());
        hotelResult.setShortDesc(hotelMetaData.getPropertyDetails().getShortDescription());
        hotelResult.setStarRating(hotelMetaData.getPropertyDetails().getStarRating());
        hotelResult.setStarRatingType(hotelMetaData.getPropertyDetails().getStarRatingType());
        hotelResult.setHighSellingAltAcco(hotelMetaData.getPropertyFlags() != null && hotelMetaData.getPropertyFlags().isHighSellingAltAcco());
        hotelResult.setStayType(hotelMetaData.getPropertyDetails().getStayType());
        hotelResult.setExperiences(
        createExperiencesCardData(hotelMetaData.getPremiumExperiences(), hotelMetaData.getPropertyDetails().getCategories(), hotelResult, expDataMap)
        );

//        if (hotelMetaData.getHostingInfo() != null && hotelMetaData.getHostingInfo().getHostInfo() != null) {
//            hotelResult.setHostInfo(buildHostInfo(hotelMetaData.getHostingInfo().getHostInfo(), isDisableHostChat));
//        }

        if (StringUtils.isNotEmpty(hotelMetaData.getPropertyDetails().getPopularText())) {
            hotelResult.setReportCardPersuasion(buildReportCardPersuasion(hotelMetaData.getPropertyDetails().getPopularText()));
        }

        hotelResult.setPopularType(hotelMetaData.getPropertyDetails().getPopularType());
        if (!hotelMetaData.getPropertyFlags().isActiveButOffline()) {
            if (hotelMetaData.getHostingInfo() != null && hotelMetaData.getHostingInfo().getHostInfo() != null &&
                    StringUtils.isNotEmpty(hotelMetaData.getHostingInfo().getHostInfo().getHostType())) {
                hotelResult.setHostInfoV2(buildHostInfoV2(hotelMetaData.getHostingInfo().getHostInfo(), hotelMetaData.getRatingDataMap(), isDisableHostChat, enableHostCalling));
            }
        }

        if (hotelMetaData.getHostingInfo() != null && hotelMetaData.getHostingInfo().getStaffInfo() != null) {
            hotelResult.setStaffInfo(convertStaffInfo(hotelMetaData.getHostingInfo().getStaffInfo()));
        }
        if (MapUtils.isNotEmpty(hotelMetaData.getRatingDataMap()) &&
                hotelMetaData.getRatingDataMap().containsKey(RatingCategory.AMENITIES) &&
                utility.isExperimentTrue(expDataMap, AMENITY_SECTION_RATING.getKey())) {
            hotelResult.setAmenitiesRatingData(utility.buildRatingData(hotelMetaData.getRatingDataMap().get(RatingCategory.AMENITIES), new UGCRatingData()));
        }

        if(hotelMetaData.getAmenitiesInfo().isLovedByIndianAmenities()){
            PersuasionResponse amenitiesPersuasion = buildLbiPersuasion();
            hotelResult.setAmenitiesPersuasion(amenitiesPersuasion);
        }
        int amenityRatingCardPos = hotelMetaData!=null && hotelMetaData.getAmenitiesInfo()!=null ? hotelMetaData.getAmenitiesInfo().getRatingCardPosition() : -1;
        boolean isAmenitiesV2Enabled = utility.isAmenitiesV2Enabled(expDataMap);
        hotelResult.setAmenities(utility.getAmenities(hotelMetaData.getAmenitiesInfo().getAmenities(), isAmenitiesV2Enabled, amenityRatingCardPos));
//        hotelResult.setAmenities(utility.getAmenities(hotelMetaData.getAmenitiesInfo().getAmenities(), isAmenitiesV2Enabled, hotelMetaData.getAmenitiesRatingCardPos()));
        // the lines below will be set and sent to the client only if it is a case of Available but sold out
        //
        //TODO :: Ask Lepsy
        //hotelResult.setListingDeeplinkUrl(hotelMetaData.getPropertyDetails().getListingDeeplinkUrl());

        hotelResult.setFlexibleCheckinInfo(mapFlexibleCheckinInfo(hotelMetaData.getCheckInOutInfo() != null ? hotelMetaData.getCheckInOutInfo().getFlexibleCheckinInfo() : null));
        
        // Build chatbot info in CG layer instead of using hotelMetaData.getChatbotInfo()
        boolean isTravelPlexEnabled = MapUtils.isNotEmpty(expDataMap) && utility.isExperimentOn(expDataMap, TRAVEL_PLEX_ENABLED);
        boolean isHotelsChatbotEnabled = utility.isExperimentOn(expDataMap, CHATBOT_ENABLED.getKey()) || utility.isExperimentOn(expDataMap, CHATBOT_HOOKS_EXP.getKey());
        if (isHotelsChatbotEnabled)
            hotelResult.setChatbotInfo(buildChatbotInfo(hotelResult, hotelMetaData.getLocationInfo(), staticDetailRequest, staticDetailCriteria, commonModifierResponse, expDataMap, affiliateId, isTravelPlexEnabled));
        
        utility.addHooksData(hotelResult.getChatbotInfo(), utility.isExperimentOn(expDataMap, CHATBOT_HOOKS_EXP.getKey()), isTravelPlexEnabled);
        if (isGroupBookingFunnel) {
            hotelResult.setHighlightedAmenities(hotelMetaData.getAmenitiesInfo()
                    .getHighlightedAmenities().stream()
                    .map(AmenityGroup::getName)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList()));
        } else {
            hotelResult.setHighlightedAmenities(utility.getHighlightedAmenities(hotelMetaData.getAmenitiesInfo().getHighlightedAmenities()));
            hotelResult.setHighlightedAmenitiesV2(utility.getHighlightedAmenitiesV2(hotelMetaData.getAmenitiesInfo().getHighlightedAmenities()));
        }

        hotelResult.setShowCallToBook(hotelMetaData.getPropertyDetails() != null && hotelMetaData.getPropertyFlags() != null && hotelMetaData.getPropertyFlags().isShowCallToBook());
        if (utility.isExperimentOn(expDataMap, EXP_PLV2) && MapUtils.isNotEmpty(hotelMetaData.getAltAccoRoomInfo())) {
            Set<Space> sharedSpacesList = new HashSet<>();
            Set<Space> privateSpacesList = new HashSet<>();
            AtomicReference<SharedInfo> sharedInfo = new AtomicReference<>();
            hotelMetaData.getAltAccoRoomInfo().forEach((key, roomInfo) -> {
                if (roomInfo != null && CollectionUtils.isNotEmpty(roomInfo.getSpaces())) {
                    roomInfo.getSpaces().forEach(spaceData -> {
                        if (spaceData.getType() == com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData.Type.SHARED) {
                            sharedInfo.set(utility.buildSharedInfo(spaceData.getDisplayItem()));
                            utility.getSpaceDataV2(spaceData, false, sharedSpacesList);
                        } else {
                            utility.getSpaceDataV2(spaceData, true, privateSpacesList);
                        }
                    });
                }
            });
            if (CollectionUtils.isNotEmpty(privateSpacesList)) {
                hotelResult.setPrivateSpacesV2(new SpaceData());
                hotelResult.getPrivateSpacesV2().setSpaces(new ArrayList<>(privateSpacesList));
            }
            if (CollectionUtils.isNotEmpty(sharedSpacesList)) {
                hotelResult.setSharedSpacesV2(new SpaceData());
                hotelResult.getSharedSpacesV2().setSharedInfo(sharedInfo.get());
                hotelResult.getSharedSpacesV2().setSpaces(new ArrayList<>(sharedSpacesList));
            }
            hotelResult.setPropertyLayoutTitleText(MessageFormat.format(polyglotService.getTranslatedData(SINGLE_ENTIRE_PROPERTY_LAYOUT_TEXT), hotelMetaData.getPropertyDetails().getPropertyType()));
        }
        hotelResult.setIsABO(hotelMetaData.getPropertyFlags().isActiveButOffline());
        if (hotelMetaData.getPropertyFlags().isActiveButOffline() && !Utility.isGccOrKsa() && !myPartnerReq) {
            hotelResult.setSupportDetails(buildSupportDetailsForABO());
        }
        if (hotelMetaData.getPropertyFlags().isShowCallToBook()) {
            String device = (staticDetailRequest != null && staticDetailRequest.getDeviceDetails() != null && StringUtils.isNotEmpty(staticDetailRequest.getDeviceDetails().getBookingDevice())) ? staticDetailRequest.getDeviceDetails().getBookingDevice() : EMPTY_STRING;
            if (hotelMetaData.getPropertyFlags().isActiveButOffline() && utility.isExperimentTrue(expDataMap, ExperimentKeys.aboApplicable.name())) {
                hotelResult.setRequestCallbackData(utility.buildRequestToCallBackDataForB2C(PAGE_CONTEXT_DETAIL));
            } else if (!hotelMetaData.getPropertyFlags().isActiveButOffline() && isCallToBookV2Applicable) {
                hotelResult.setRequestCallbackData(utility.buildRequestToCallBackDataV2(PAGE_CONTEXT_DETAIL));
            } else if (!hotelMetaData.getPropertyFlags().isActiveButOffline() && !isCallToBookV2Applicable) {
                hotelResult.setRequestCallbackData(utility.buildRequestToCallBackData(PAGE_CONTEXT_DETAIL, hotelResult.getName(), device));
            }
        }
        if (isNewDetailPageTrue && CollectionUtils.isNotEmpty(hotelResult.getHighlightedAmenities()) && (Constants.ANDROID.equalsIgnoreCase(deviceDetails.getBookingDevice())
                || Constants.DEVICE_IOS.equalsIgnoreCase(deviceDetails.getBookingDevice()))) {
            if (isGroupBookingFunnel) {
                limitAmenitiesCount(hotelResult.getHighlightedAmenities(), hotelMetaData.getPropertyFlags().isServiceApartment(), hotelMetaData.getPropertyFlags().isAltAcco());
            } else {
                limitAmenitiesCount(hotelResult.getHighlightedAmenities(), hotelMetaData.getPropertyFlags().isServiceApartment(), hotelMetaData.getPropertyFlags().isAltAcco());
                limitAmenitiesCount(hotelResult.getHighlightedAmenitiesV2(), hotelMetaData.getPropertyFlags().isServiceApartment(), hotelMetaData.getPropertyFlags().isAltAcco());
            }
        }

        hotelResult.setHouseRules(buildHouseRules(hotelMetaData));
        hotelResult.setHouseRulesV2(buildHouseRulesV2(hotelResult.getHouseRules(), hotelMetaData.getRulesAndPolicies().getFoodAndDiningRules(),
                hotelMetaData.getPropertyDetails().getSpokenLanguages(), hotelMetaData.getRulesAndPolicies(),
                hotelMetaData.getRulesAndPolicies().getDepositPolicy(), !dhCall, expDataMap));

        if (isLiteResponse) {
            hotelResult.setHouseRules(null);
        }

        boolean foodAndDiningV2 = Boolean.parseBoolean(expDataMap.get(FoodAndDiningV2));
        boolean foodDiningRevamp = !utility.isExperimentValid(expDataMap, Constants.FOOD_DINING_REVAMP, 0);
        boolean foodAndDiningEnhancement = utility.isFoodAndDiningEnhancement(expDataMap);
        boolean isLuxe = hotelMetaData.getPropertyDetails() != null && hotelMetaData.getPropertyDetails().getCategories() != null && hotelMetaData.getPropertyDetails().getCategories().contains(LUXURY_HOTELS);
        boolean isMealDetailsPresent = hotelMetaData.getRulesAndPolicies()!=null && hotelMetaData.getRulesAndPolicies().getMealRules()!=null
                && hotelMetaData.getRulesAndPolicies().getMealRules().isHasMealsDetails();
//        hotelMetaData.getRulesAndPolicies().getFoodAndDiningRules()

        if (hotelMetaData.getRulesAndPolicies().getMealRules() != null) {
            RatingData foodRatingData = MapUtils.isNotEmpty(hotelMetaData.getRatingDataMap()) && hotelMetaData.getRatingDataMap().containsKey(RatingCategory.FOOD) ?
                    hotelMetaData.getRatingDataMap().get(RatingCategory.FOOD) : null;
            hotelResult.setFoodDiningV2(buildFoodDiningV2(
                    hotelMetaData.getRulesAndPolicies().getMealRules(),
                    foodRatingData, deviceDetails, hotelMetaData.getPropertyFlags().isAltAcco(),
                    dhCall, foodAndDiningEnhancement, isMealDetailsPresent));
        } else if (CollectionUtils.isNotEmpty(hotelMetaData.getRulesAndPolicies().getFoodAndDiningRules())) {
            hotelResult.setFoodDining(buildFoodDining(hotelMetaData.getRulesAndPolicies().getFoodAndDiningRules(), expDataMap, staticDetailCriteria, deviceDetails, foodAndDiningV2, foodDiningRevamp, MapUtils.isNotEmpty(hotelMetaData.getRatingDataMap()) ? hotelMetaData.getRatingDataMap().getOrDefault(RatingCategory.FOOD, new RatingData()) : new RatingData(), hotelMetaData.getPropertyFlags().isAltAcco(), dhCall, foodAndDiningEnhancement));
        }

        if (FUNNEL_SOURCE_DAYUSE.equalsIgnoreCase(funnel))
            suppressFewHouseRules(hotelResult.getHouseRulesV2());

        hotelResult.setFaqData(buildFaqData(hotelMetaData.getFrequentlyAskedQuestions()));

        if (StringUtils.isNotBlank(hotelMetaData.getPropertyDetails().getStayType()) && StringUtils.startsWith(hotelMetaData.getPropertyDetails().getStayType().toUpperCase(), "ENTIRE")) {
            hotelResult.setEntireProperty(true);
        }
        //TODO :: Ask Lepsy
        //hotelResult.setTotalGuestCount(hotelMetaData.getPropertyDetails().getTotalGuestCount());
        hotelResult.setSharingUrl(hotelMetaData.getPropertyDetails().getSharingUrl());
        //TODO :: Ask Lepsy
//        if (StringUtils.isNotBlank(hotelMetaData.getPropertyDetails().getDetailDeeplinkUrl())) {
//            hotelResult.setDetailDeeplinkUrl(hotelMetaData.getPropertyDetails().getDetailDeeplinkUrl());
//        }
        hotelResult.setLocationPersuasion(hotelMetaData.getLocationInfo().getLocationPersuasion());
        hotelResult.setIngoId(hotelMetaData.getPropertyDetails().getIngoHotelId());
        hotelResult.setLocationPersuasion(hotelMetaData.getLocationInfo().getLocationPersuasion());
        hotelResult.setContextType(hotelMetaData.getPropertyDetails().getContext());
        hotelResult.setContext(hotelMetaData.getPropertyDetails().getContext());

        //TODO :: Ask Lepsy
        //hotelResult.setAlternateDatesAvailable(hotelMetaData.getPropertyFlags().isAlternateDatesAvailable());
        if (!isCorpIdContext && CollectionUtils.isNotEmpty(hotelResult.getCategories()) && hotelResult.getCategories().contains(Constants.MMT_VALUE_STAYS)) {
            hotelResult.setTitleIcon(Utility.isGccOrKsa() ? iconUrlGcc : iconUrl);
            addTitleData(hotelResult, hotelMetaData.getLocationInfo().getCountryCode(), isNewDetailPageDesktop);
        }

        if (myPartnerReq && hotelMetaData.getGstInfo() != null && hotelMetaData.getGstInfo().isGstAssured()) {
            CategoryTag categoryTag = new CategoryTag();
            categoryTag.setIconUrl(iconUrlGSTAssured);
            categoryTag.setStyle(new Style());
            categoryTag.getStyle().setIconHeight(29);
            categoryTag.getStyle().setIconWidth(134);
            categoryTag.setTooltip(commonConfigConsul.getMmtMyPartnerTooltip());
            hotelResult.setCategoryTag(categoryTag);
        } else if (isLuxe) {
            if (isNewDetailPageTrue && (Constants.ANDROID.equalsIgnoreCase(deviceDetails.getBookingDevice())
                    || Constants.DEVICE_IOS.equalsIgnoreCase(deviceDetails.getBookingDevice()))) {
                hotelResult.setLuxeIcon(LUXE_ICON_NEW_APP);
            } else {
                hotelResult.setLuxeIcon(getLuxeIcon());
            }
            if (isNewDetailPageDesktop) {
                CategoryTag categoryTag = new CategoryTag();
                categoryTag.setIconUrl(luxeIconNewDetailPageDt);
                Style style = new Style();
                style.setIconHeight(23);
                style.setIconWidth(68);
                categoryTag.setStyle(style);
                hotelResult.setCategoryTag(categoryTag);
            }
        }

        hotelResult.setMmtHotelCategory(buildMmtHotelCategory(hotelMetaData.getPropertyDetails().getCategories()));
        if (StringUtils.isNotEmpty(hotelMetaData.getPropertyDetails().getUspHotelText()))
            hotelResult.setMmtHotelText(hotelMetaData.getPropertyDetails().getUspHotelText());

        hotelResult.setLongStayAmenities(Collections.emptyList());
        if (StringUtils.isNotEmpty(hotelMetaData.getAmenitiesInfo().getHighlightedAmenityTag())) {
            hotelResult.setHighlightedAmenitiesTag(hotelMetaData.getAmenitiesInfo().getHighlightedAmenityTag());
        }
        hotelResult.setCardTitleMap(buildCardTitleMap());

        if (hotelMetaData.getMediaContent() != null) {
            if (StringUtils.isNotEmpty(hotelMetaData.getMediaContent().getHeroImage())) hotelResult.setHeroImage(hotelMetaData.getMediaContent().getHeroImage());
            if (StringUtils.isNotEmpty(hotelMetaData.getMediaContent().getHeroVideoUrl())) hotelResult.setHeroVideoUrl(hotelMetaData.getMediaContent().getHeroVideoUrl());
        }

        if (hotelMetaData.getPropertyFlags().isGroupBookingAllowed() && (hotelResult.getHostInfo() == null || !hotelResult.getHostInfo().isChatEnabled())) {
            hotelResult.setGroupBookingQueryEnabled(hotelMetaData.getPropertyFlags().isGroupBookingAllowed());
            hotelResult.setGroupBookingWebUrl(prepareDeepLinkUrlForGroupBooking(staticDetailRequest, staticDetailCriteria, deviceDetails, myPartnerReq));
        }
        hotelResult.setWishListed(hotelMetaData.getPropertyFlags().isWishListed());
        hotelResult.setCalendarCriteria(buildCalendarCriteria(hotelMetaData.getCalendarCriteria()));
        hotelResult.setCategoryIcon(buildCategoryIcon(hotelMetaData, isCorpIdContext, expDataMap));
        hotelResult.setIsABSO(hotelMetaData.getPropertyFlags().isActiveButSoldOut());
        hotelResult.setIsRTB(hotelMetaData.getPropertyFlags().isRequestToBook());
        hotelResult.setIsMLOS(hotelMetaData.getCalendarCriteria() != null);
        hotelResult.setHomeStayAwardDetails(buildHomeStayAwardDetails(hotelMetaData.getPropertyDetails().getPropertyAwardText(), hotelMetaData.getPropertyFlags().isAltAcco(), deviceDetails));
        if (hotelMetaData.getRulesAndPolicies() != null && CollectionUtils.isNotEmpty(hotelMetaData.getRulesAndPolicies().getGovtPolicies())) {
            hotelResult.setGovtPolicies(buildGovtPolies(hotelMetaData.getRulesAndPolicies().getGovtPolicies()));
        }

        if (StringUtils.isNotEmpty(hotelMetaData.getPropertyDetails().getCategoryUsp()) && !isLiteResponse) {
            hotelResult.setCategoryUspDetailsText(polyglotService.getTranslatedData(BEACHFRONT_CATEGORY_USP_DETAILS_TEXT));
        }
        hotelResult.setHotelCategories(utility.concatenateWithSeparator(PIPE, hotelResult.getCategories()));
        return hotelResult;


        //NOT USED
        //hotelResult.setPropertyUnavailableImg(hotelMetaData.getPropertyDetails().getPropertyUnavailableImg());
        //hotelResult.setPropertyRulesTitle(hotelMetaData.getPropertyDetails().getPropertyRulesTitle());
        //hotelResult.setShowTimeRangeUi(hotelMetaData.getCheckInOutInfo() != null && hotelMetaData.getCheckInOutInfo().isShowTimeRangeUi());
    }

    private HomeStayAwardDetails buildHomeStayAwardDetails(String titleText, boolean isAltAcco, DeviceDetails deviceDetails) {
        HomeStayAwardDetails homeStayAwardDetails = null;
        if (isAltAcco && CLIENT_DESKTOP.equalsIgnoreCase(deviceDetails.getBookingDevice()) &&
                StringUtils.isNotEmpty(titleText) && StringUtils.isNotEmpty(commonConfigConsul.getHomeStayAwardUrl())) {
            homeStayAwardDetails = new HomeStayAwardDetails();
            homeStayAwardDetails.setTitleText(titleText);
            homeStayAwardDetails.setImageUrl(commonConfigConsul.getHomeStayAwardUrl());
        }
        return homeStayAwardDetails;
    }

    private FoodDiningV2 buildFoodDiningV2(
            MealRules mealRules,
            RatingData foodRatingData,
            DeviceDetails deviceDetails,
            boolean isAltAcco,
            boolean isDhCall,
            boolean foodAndDiningEnhancement,
            boolean isMealDetailsPresent) {
        // Return null if foodDiningV2HES is null or if both diningInfo and summary are null or empty
        if (mealRules == null ||
                (CollectionUtils.isEmpty(mealRules.getMealRulesData()) &&
                        CollectionUtils.isEmpty(mealRules.getDisplayItems()))) {
            return null;
        }

        FoodDiningV2 foodDiningV2CG = new FoodDiningV2();
        UGCRatingData ugcRatingData  = new UGCRatingData();

        // FIX: Add null safety for polyglotService
        if (polyglotService != null) {
            foodDiningV2CG.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_CARD_TITLE));
        }

        foodDiningV2CG.setMealDetailsPresent(isMealDetailsPresent);

        List<FoodDiningRule> allRulesList = new ArrayList<>();
        List<SummaryItem> summaryList = new ArrayList<>();
        List<Restaurant> restaurants = new ArrayList<>();

        if (mealRules.getMealRulesData() != null && CollectionUtils.isNotEmpty(mealRules.getMealRulesData())) {
            for (MealRulesData mealRule : mealRules.getMealRulesData()) {
                // FIX: Add null safety for mealRule and its category
                if (mealRule == null || mealRule.getCategory() == null) {
                    continue;
                }

                if (mealRule.getCategory().equalsIgnoreCase(FoodAndDiningEnums.FoodMenu.getName())
                        || mealRule.getCategory().equalsIgnoreCase(FoodAndDiningEnums.Cook.getName())) {
                    continue;
                }

                FoodDiningRule diningRuleCG = new FoodDiningRule();

                if (FoodAndDiningEnums.Kitchen.getName().equalsIgnoreCase(mealRule.getCategory())) {
                    diningRuleCG.setCategory(KITCHEN_CATEGORY_REVAMP);
                } else {
                    diningRuleCG.setCategory(mealRule.getCategory());
                }

                // FIX: Add null safety for mealRule fields
                diningRuleCG.setId(mealRule.getId() != null ? mealRule.getId() : "");
                diningRuleCG.setHeading(mealRule.getHeading() != null ? mealRule.getHeading() : "");
                diningRuleCG.setDescription(mealRule.getDescription() != null ? mealRule.getDescription() : "");
                diningRuleCG.setImages(mealRule.getGallery()!=null && mealRule.getGallery().getUrls() != null ? mealRule.getGallery().getUrls() : new ArrayList<>());
                diningRuleCG.setShowInL2Page(mealRule.isShowInL2Page());
                diningRuleCG.setShowInDetailHome(mealRule.isShowInL1Page());
                diningRuleCG.setShowArrowInDetailHome(true);

                List<DiningRuleItem> ruleItems = new ArrayList<>();

                if (mealRule.getRules() != null && CollectionUtils.isNotEmpty(mealRule.getRules())) {
                    for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.Rule ruleHES : mealRule.getRules()) {
                        DiningRuleItem ruleItem = buildFoodDiningV2RuleItem(ruleHES);
                        if (ruleItem != null) {
                            ruleItems.add(ruleItem);
                        }
                    }
                }

                diningRuleCG.setRules(CollectionUtils.isNotEmpty(ruleItems) ? ruleItems : null);

                if (mealRule.getMealInfo() != null) {
                    if(foodRatingData != null){
                        // FIX: Add null safety for mealRule.getId()
                        String mergeId = mealRule.getId() != null ? mealRule.getId() : "";
                        ugcRatingData.setMergeId(mergeId);
                    }
                    // FIX: Add null safety for commonResponseTransformer
                    if (commonResponseTransformer != null) {
                        commonResponseTransformer.transformAndUpdateMealDetails(diningRuleCG, mealRule.getMealInfo());
                    }
                }

                if(FoodAndDiningEnums.Restaurant.getName().equalsIgnoreCase(mealRule.getCategory())){
                    if (!isAltAcco && foodAndDiningEnhancement) {
                        foodDiningV2CG.setTitle(RESTAURANTS_TITLE);
                    }
                    if(Utility.isBookingDeviceDesktop(deviceDetails)){
                        diningRuleCG.setShowInDetailHome(false);
                    }
                    if (isDhCall && mealRule.getGallery()!=null && CollectionUtils.isNotEmpty(mealRule.getGallery().getUrls())) {
                        diningRuleCG.setShowArrowInDetailHome(false);
                        // FIX: Add null safety for gallery URL access
                        String firstImageUrl = mealRule.getGallery().getUrls().get(0);
                        if (firstImageUrl != null) {
                            // FIX: Add null safety for restaurant constructor parameters
                            String heading = mealRule.getHeading() != null ? mealRule.getHeading() : "";
                            String id = mealRule.getId() != null ? mealRule.getId() : "";
                            restaurants.add(new Restaurant(heading, firstImageUrl, id));
                        }
                    }
                }

                allRulesList.add(diningRuleCG);
            }
        }

        if(foodRatingData != null){
            foodDiningV2CG.setRatingData(utility.buildRatingData(foodRatingData, ugcRatingData));
        }

        // Map summary items from foodDiningV2HES to foodDiningV2CG
        if(mealRules.getDisplayItems() != null && CollectionUtils.isNotEmpty(mealRules.getDisplayItems())) {
            for(DisplayItem summaryItemHES : mealRules.getDisplayItems()) {
                // FIX: Add null safety for display item
                if (summaryItemHES != null) {
                    SummaryItem summaryItemCG = new SummaryItem();
                    if(StringUtils.isNotEmpty(summaryItemHES.getText())) {
                        summaryItemCG.setText(summaryItemHES.getText());
                    }
                    if(StringUtils.isNotEmpty(summaryItemHES.getIconUrl())) {
                        summaryItemCG.setIconUrl(summaryItemHES.getIconUrl());
                    }
                    summaryList.add(summaryItemCG);
                }
            }
            foodDiningV2CG.setSummary(summaryList);
        }

        foodDiningV2CG.setRestaurants(CollectionUtils.isNotEmpty(restaurants) ? restaurants : null);
        foodDiningV2CG.setAllRules(CollectionUtils.isNotEmpty(allRulesList) ? allRulesList : null);
        foodDiningV2CG.setFeedbackData(buildFoodDiningFeedbackData());

        return foodDiningV2CG;
    }

    private PersuasionResponse buildLbiPersuasion() {
        PersuasionResponse persuasionResponse = new PersuasionResponse();
        
        // FIX: Add null safety for polyglotService
        if (polyglotService == null) {
            return null;
        }
        
        String title = polyglotService.getTranslatedData(LBI_TITLE);
        if(title == null || StringUtils.isEmpty(title)) {
            return null;
        };
        persuasionResponse.setTitle(title);
        // FIX: Add null safety for lovedByIndiansIconUrl
        persuasionResponse.setIconUrl(lovedByIndiansIconUrl != null ? lovedByIndiansIconUrl : "");
        return persuasionResponse;
    }

    private CardAction buildFoodDiningFeedbackData() {
        CardAction cardAction = new CardAction();
        
        // FIX: Add null safety for polyglotService and provide safe defaults
        if (polyglotService != null) {
            cardAction.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_FEEDBACK_CARD_TITLE));
        }

        CardActionData cardActionData = new CardActionData();
        if (polyglotService != null) {
            cardActionData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_FEEDBACK_SHEET_TITLE));
        }

        // Create sections list
        List<Section> sections = new ArrayList<>();
        Section section = new Section();
        if (polyglotService != null) {
            section.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_FEEDBACK_SHEET_DESCRIPTION));
        }

        // Create items list
        List<Item> items = new ArrayList<>();

        // First item - "The list was very long"
        Item item1 = new Item();
        if (polyglotService != null) {
            item1.setText(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_FEEDBACK_SHEET_REASON_ONE));
        }
        item1.setType(FEEDBACK_ITEM_TYPE_TEXT);
        items.add(item1);

        // Second item - "Did not find the amenity I was looking for"
        Item item2 = new Item();
        if (polyglotService != null) {
            item2.setText(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_FEEDBACK_SHEET_REASON_TWO));
        }
        item2.setType(FEEDBACK_ITEM_TYPE_TEXT);
        items.add(item2);

        // Third item - "My reason is not listed here" with textBox
        Item item3 = new Item();
        if (polyglotService != null) {
            item3.setText(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_FEEDBACK_SHEET_REASON_THREE));
            item3.setTextBoxTitle(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_FEEDBACK_SHEET_TEXT_BOX_TITLE));
        }
        item3.setType(FEEDBACK_ITEM_TYPE_TEXTBOX);
        items.add(item3);

        section.setItems(items);
        sections.add(section);
        cardActionData.setSections(sections);

        cardAction.setData(cardActionData);
        return cardAction;
    }

    private DiningRuleItem buildFoodDiningV2RuleItem(com.gommt.hotels.orchestrator.detail.model.response.content.houserules.Rule ruleHES) {
        if (ruleHES == null) {
            return null;
        }

        DiningRuleItem diningRuleItem = new DiningRuleItem();

        // FIX: Add null safety for basic field access
        diningRuleItem.setTitleText(ruleHES.getTemplateText() != null ? ruleHES.getTemplateText() : ""); // titleText -> templatetext
        diningRuleItem.setText(ruleHES.getText() != null ? ruleHES.getText() : ""); // ruleHES.getText() ->
        diningRuleItem.setIconUrl(ruleHES.getIconUrl() != null ? ruleHES.getIconUrl() : "");

        // Convert HES MealTypes to CG MealTypes
        if (CollectionUtils.isNotEmpty(ruleHES.getDisplayItems())) { // ruleHES.getMealTypes() ->
            List<MealType> cgMealTypes = new ArrayList<>();
            for (DisplayItem hesMealType : ruleHES.getDisplayItems()) {
                if (hesMealType != null) {
                    MealType cgMealType = new MealType();
                    // FIX: Add null safety for text and icon URL
                    cgMealType.setName(hesMealType.getText() != null ? hesMealType.getText() : ""); //getName
                    cgMealType.setIcon(hesMealType.getIconUrl() != null ? hesMealType.getIconUrl() : ""); // getIcon
                    cgMealTypes.add(cgMealType);
                }
            }
            diningRuleItem.setMealTypes(CollectionUtils.isNotEmpty(cgMealTypes) ? cgMealTypes : null);
        }

        // Convert infoData to additionalInfo if present
        if (ruleHES.getRuleTableInfo() != null && CollectionUtils.isNotEmpty(ruleHES.getRuleTableInfo().getInfoList())) { // getInfoData.getData
            List<String> additionalInfo = new ArrayList<>();
            for (RuleInfo dataItem : ruleHES.getRuleTableInfo().getInfoList()) {
                if (dataItem != null) {
                    String key = dataItem.getKey();
                    // FIX: Add null safety for getValue() and list access
                    List<String> values = dataItem.getValue();
                    if (values != null && !values.isEmpty()) {
                        String value = values.get(0);
                        if (StringUtils.isNotEmpty(key) && StringUtils.isNotEmpty(value)) {
                            additionalInfo.add(key + ": " + value);
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(additionalInfo)) {
                diningRuleItem.setAdditionalInfo(additionalInfo);
                // Wrap text with bold HTML tags when additionalInfo is present
                if (StringUtils.isNotEmpty(diningRuleItem.getText())) {
                    diningRuleItem.setText("<b>" + diningRuleItem.getText() + "</b>");
                }
            }
        }

        return diningRuleItem;
    }

    public String buildMmtHotelCategory(Set<String> categories) {
        if (CollectionUtils.isEmpty(categories)) {
            return null;
        }
        if (categories.contains(BUDGET_CONTEXT)) {
            return MmtHotelCategory.BUDGET.toString();
        } else if (categories.contains(LUXURY_HOTELS)) {
            return MmtHotelCategory.LUXE.toString();
        } else if (categories.contains(PREMIUM_CONTEXT)) {
            return MmtHotelCategory.PREMIUM.toString();
        }
        return null;
    }

    private void suppressFewHouseRules(HouseRulesV2 houseRulesV2) {
        if (null == houseRulesV2 || CollectionUtils.isEmpty(houseRulesV2.getAllRules())) return;
        List<CommonRules> resultCommonRulesList = houseRulesV2.getAllRules().stream().filter(rule -> !supressedHouseRulesList.contains(rule.getCategoryId()) && (null == rule.getId() || !rule.getId().equalsIgnoreCase(EXTRA_BED_POLICY_TO_BE_REMOVED))).collect(Collectors.toList());
        houseRulesV2.setAllRules(resultCommonRulesList);
    }

    private HouseRulesV2 buildHouseRulesV2(HouseRules houseRules, List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules> foodAndDiningRule,
                                           List<String> spokenLanguages, RulesAndPolicies rulesAndPolicies,
                                           CategoryInfo depositPolicy, boolean isInternationalHotel, Map<String, String> expDataMap) {
        if (houseRules == null) {
            return null;
        }

        HouseRulesV2 houseRulesV2 = new HouseRulesV2();
        boolean showcontextualhouserules =  utility.isExperimentTrue(expDataMap, ExperimentKeys.SHOW_CONTEXTUAL_HOUSE_RULES.getKey());
        if (showcontextualhouserules) {
            if (rulesAndPolicies != null && StringUtils.isNotEmpty(rulesAndPolicies.getHighlightMustReadRule().getHighlightedTitleDetail().getTitle())) {
                houseRulesV2.setHighlightMustReadRules(getHighlightMustReadRule(rulesAndPolicies.getHighlightMustReadRule()));
            }
        }else {
            houseRulesV2.setContextRules(houseRules.getContextRules());
        }


        boolean isIhHouseRuleUiV2Enabled = isInternationalHotel && utility.isExperimentTrue(expDataMap, ExperimentKeys.IH_HOUSE_RULE_UI_REVAMP.getKey());

        List<CommonRules> allRules = new ArrayList<>();
        if (houseRules.getMustReadRules() != null) {
            allRules.add(convertMustReadRule(houseRules.getMustReadRules()));
        }

        if (CollectionUtils.isNotEmpty(houseRules.getCommonRules())) {
            for (CommonRules commonRules : houseRules.getCommonRules()) {
                if (Constants.GUEST_PROFILE.equalsIgnoreCase(commonRules.getId()) || Constants.SAFETY_AND_HYGIENE.equalsIgnoreCase(commonRules.getId())) {
                    commonRules.setShowInDetailHome(true);
                }
                allRules.add(commonRules);
            }
        }

        List<Rule> rules = new ArrayList<>();
        List<ChildExtraBedPolicy> extraBedPolicy = houseRules.getExtraBedPolicyList();
        if (CollectionUtils.isNotEmpty(extraBedPolicy)) {
            for (ChildExtraBedPolicy childExtraBedPolicy : extraBedPolicy) {
                rules.add(new Rule(childExtraBedPolicy.getPolicyInfo()));
                if (CollectionUtils.isNotEmpty(childExtraBedPolicy.getPolicyRules())) {
                    for (PolicyRules policyRules : childExtraBedPolicy.getPolicyRules()) {
                        if (CollectionUtils.isNotEmpty(policyRules.getExtraBedTerms())) {
                            for (ExtraBedRules extraBedRules : policyRules.getExtraBedTerms()) {
                                if (extraBedRules.getValue() != null) {
                                    rules.add(new Rule(extraBedRules.getValue()));
                                }
                            }
                        }
                    }
                }
            }
            CommonRules commonRules = new CommonRules();
            commonRules.setCategory(polyglotService.getTranslatedData(EXTRA_BED_POLICY));
            commonRules.setId(EXTRA_BED_POLICY.toLowerCase());
            commonRules.setRules(rules);
            allRules.add(commonRules);
        }

        if (rulesAndPolicies.getHouseRules() != null && CollectionUtils.isNotEmpty(rulesAndPolicies.getHouseRules().getCategoryInfos())) {
            for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CategoryInfo categoryInfo : rulesAndPolicies.getHouseRules().getCategoryInfos()) {
                if (Constants.BREAKFAST_CHARGES.equalsIgnoreCase(categoryInfo.getId())
                        || Constants.EXTRA_BED_POLICY.equalsIgnoreCase(categoryInfo.getId())
                        || Constants.LANGUAGES_SPOKEN.equalsIgnoreCase(categoryInfo.getId())) {
                    CommonRules categoryInfoRule = buildCategoryCommonRules(categoryInfo);
                    if (categoryInfoRule != null) {
                        allRules.add(categoryInfoRule);
                    }
                }
            }
        }

        if (depositPolicy != null && depositPolicy.getRuleTableInfo() != null &&
                CollectionUtils.isNotEmpty(depositPolicy.getRuleTableInfo().getInfoList())) {
            com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo ruleTableInfoCG = ReArchUtility.buildRuleTableInfo(depositPolicy.getRuleTableInfo());
            if (ruleTableInfoCG != null) {
                CommonRules commonRule = new CommonRules();
                commonRule.setId(depositPolicy.getId());
                commonRule.setCategory(depositPolicy.getName());
                commonRule.setCategoryName(depositPolicy.getName());
                commonRule.setCategoryDesc(depositPolicy.getDesc());
                commonRule.setRuleDesc(depositPolicy.getRuleInfo());
                commonRule.setRuleTableInfo(ruleTableInfoCG);
                commonRule.setRules(buildCategoryRules(depositPolicy.getRuleDesc()));
                commonRule.setShowInDetailHome(true);
                commonRule.setCategoryHeading(depositPolicy.getHeading());
                allRules.add(commonRule);
            }
        }

        houseRulesV2.setAllRules(allRules);

        //For isIhHouseRuleUiV2Enabled, languages data comes as separate category item in HES response
        if (CollectionUtils.isNotEmpty(spokenLanguages) && !isIhHouseRuleUiV2Enabled) {
            houseRulesV2.setLanguage(prepareSpokenLanguagesString(spokenLanguages));
            houseRulesV2.setLanguageHeader(polyglotService.getTranslatedData("SPOKEN_LANGUAGE_HEADER"));
            // Spoken languages is a part of house rules on detail page after release that contains pokus EXTRA_BNB_EXP_KEY
            if (CollectionUtils.isNotEmpty(allRules) && utility.isExperimentTrue(expDataMap, EXTRA_BNB_EXP_KEY)) {
                CommonRules firstRule = allRules.stream()
                        .filter(rule -> rule.isShowInDetailHome() && rule.isExpandRules())
                        .findFirst()
                        .orElse(null);
                if (firstRule != null) {
                    Rule rule = new Rule();
                    rule.setHeaderTitle(houseRulesV2.getLanguageHeader());
                    rule.setSubTitle(houseRulesV2.getLanguage());
                    if (CollectionUtils.isNotEmpty(firstRule.getRules()))
                        firstRule.getRules().add(rule);
                    else
                        firstRule.setRules(Collections.singletonList(rule));
                }
            }
        }
        return houseRulesV2;
    }

   private com.mmt.hotels.clientgateway.response.staticdetail.HighlightMustReadRule  getHighlightMustReadRule(com.gommt.hotels.orchestrator.detail.model.response.content.HighlightMustReadRule highlightMustReadRule){
        if(highlightMustReadRule == null){
            return null;
        }
        com.mmt.hotels.clientgateway.response.staticdetail.HighlightMustReadRule highlightMustReadRule1 = new com.mmt.hotels.clientgateway.response.staticdetail.HighlightMustReadRule();
        highlightMustReadRule1.setHighlightedTitle(highlightMustReadRule.getHighlightedTitleDetail().getTitle());
        highlightMustReadRule1.setHighlightedIcon(highlightMustReadRule.getHighlightedTitleDetail().getIconUrl());
        if (CollectionUtils.isNotEmpty(highlightMustReadRule.getHighlightedMustReadRules())){
            AtomicBoolean isPositiveResponse = new AtomicBoolean(true);
            List<String> highlightedRules = new ArrayList<>();
            highlightMustReadRule.getHighlightedMustReadRules().forEach( highlightedRule->{
                if (StringUtils.isNotEmpty(highlightedRule.getKey())) {
                    highlightedRules.add(highlightedRule.getKey());
                    if (CollectionUtils.isNotEmpty(highlightedRule.getValue()) && highlightedRule.getValue().get(0).equalsIgnoreCase("no")){
                        isPositiveResponse.set(false);
                    }
                }
            });
            String highLightedText = "";
            if (highlightedRules.size() == 1){
                highLightedText = highlightedRules.get(0) + ".";
            }else {
                highLightedText = String.join(". ", highlightedRules);
            }
            if (StringUtils.isNotEmpty(highLightedText)){
                highlightMustReadRule1.setRuleText(highLightedText);
            }

            if (isPositiveResponse.get()) {
                highlightMustReadRule1.setBackground("#E6FFF9");
            }else {
                highlightMustReadRule1.setBackground("#FFEDD1");
            }
        }
        return highlightMustReadRule1;

    }


    private CommonRules buildCategoryCommonRules(com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CategoryInfo categoryInfo) {
        if (categoryInfo != null) {
            boolean isLanguagesSpokenCategory = LANGUAGES_SPOKEN.equalsIgnoreCase(categoryInfo.getId());
            com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo ruleTableInfoCG = ReArchUtility.buildRuleTableInfo(categoryInfo.getRuleTableInfo());
            if ((ruleTableInfoCG == null || ruleTableInfoCG.getInfoList().isEmpty()) && !isLanguagesSpokenCategory) {
                return null;
            }
            CommonRules commonRules = new CommonRules();
            commonRules.setRuleDesc(categoryInfo.getRuleInfo());
            commonRules.setRuleTableInfo(ruleTableInfoCG);
            commonRules.setCategory(categoryInfo.getName());
            commonRules.setCategoryDesc(categoryInfo.getDesc());
            commonRules.setCategoryName(categoryInfo.getName());
            commonRules.setId(categoryInfo.getId());
            commonRules.setRules(buildCategoryRules(categoryInfo.getRuleDesc()));
            commonRules.setCategoryHeading(categoryInfo.getHeading());
            commonRules.setShowInDetailHome(true);
            commonRules.setShowArrowInDetailHome(!isLanguagesSpokenCategory);
            commonRules.setShowInL2Page(!isLanguagesSpokenCategory);
            return commonRules;
        }
        return null;
    }

    private CommonRules convertMustReadRule(CommonRules mustReadRules) {
        if (CollectionUtils.isNotEmpty(mustReadRules.getRulesList())) {
            List<Rule> rules = mustReadRules.getRulesList().stream().map(Rule::new).collect(Collectors.toList());
            mustReadRules.setCategory(polyglotService.getTranslatedData(ConstantsTranslation.RESTRICTIONS));
            mustReadRules.setId(RESTRICTIONS.toLowerCase());
            mustReadRules.setShowInDetailHome(true);
            mustReadRules.setExpandRules(true);
            mustReadRules.setRules(rules);
            mustReadRules.setRulesList(null);
        }
        return mustReadRules;
    }

    private List<Rule> buildCategoryRules(List<String> rulesList) {
        List<Rule> rules = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rulesList)) {
            for (String ruleDesc : rulesList) {
                Rule rule = new Rule();
                rule.setText(ruleDesc);
                rules.add(rule);
            }
        }
        return rules;
    }

    private String prepareSpokenLanguagesString(List<String> spokenLanguages) {
        String spokenLangString = null;
        if (CollectionUtils.isNotEmpty(spokenLanguages)) {
            StringBuilder languageBuilder = new StringBuilder();
            if (spokenLanguages.size() == 1) {
                languageBuilder.append(spokenLanguages.get(0).trim());
            } else {
                for (int i = 0; i < spokenLanguages.size(); i++) {
                    if (i == spokenLanguages.size() - 1) {
                        languageBuilder.append(AND_STRING);
                        languageBuilder.append(SPACE);
                        languageBuilder.append(spokenLanguages.get(i).trim());
                        continue;
                    }
                    if (i == spokenLanguages.size() - 2) {
                        languageBuilder.append(spokenLanguages.get(i).trim());
                        languageBuilder.append(SPACE);
                        continue;
                    }
                    languageBuilder.append(spokenLanguages.get(i).trim());
                    languageBuilder.append(COMMA);
                    languageBuilder.append(SPACE);
                }
            }
            spokenLangString = languageBuilder.toString();
        }
        return spokenLangString;
    }

    private HouseRules buildHouseRules(HotelMetaData hotelMetaData) {
        HouseRules houseRulesCG = new HouseRules();
        RulesAndPolicies rulesAndPolicies = hotelMetaData.getRulesAndPolicies();
        com.gommt.hotels.orchestrator.detail.model.response.content.houserules.HouseRules houseRules = rulesAndPolicies.getHouseRules() != null ?
                rulesAndPolicies.getHouseRules() : new com.gommt.hotels.orchestrator.detail.model.response.content.houserules.HouseRules();

        if (CollectionUtils.isNotEmpty(houseRules.getChildExtraBedPolicies())) {
            houseRulesCG.setChildExtraBedPolicy(buildChildExtraBedPolicy(houseRules.getChildExtraBedPolicies().get(0)));
            houseRulesCG.setExtraBedPolicyList(buildExtraBedPolicyList(houseRules.getChildExtraBedPolicies()));
        }
        houseRulesCG.setMustReadRules(buildMustReadRules(rulesAndPolicies.getMustReadRules()));
        houseRulesCG.setContextRules(buildContextRules(hotelMetaData.getPropertyDetails().getCategories(), hotelMetaData.getLocationInfo().getCountryCode()));
        houseRulesCG.setCommonRules(buildCommonRules(houseRules.getCommonRules()));
        //houseRulesCG.setOtherInfo(buildCommonRules(houseRules.getOtherInfo()));
        return houseRulesCG;
    }

    private ContextRules buildContextRules(Set<String> categories, String countryCode) {
        if (CollectionUtils.isEmpty(categories) ||
                !categories.contains(COUPLE_FRIENDLY) ||
                !DOM_COUNTRY.equalsIgnoreCase(countryCode)) {
            return null;
        }

        ContextRules contextRules = new ContextRules();
        contextRules.setCategory(polyglotService.getTranslatedData(ConstantsTranslation.COUPLE_FRIENDLY_TITLE));
        contextRules.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.COUPLE_FRIENDLY_RULE_TITLE));
        contextRules.setDesc(polyglotService.getTranslatedData(ConstantsTranslation.COUPLE_FRIENDLY_RULE_DESC));
        contextRules.setTag(polyglotService.getTranslatedData(ConstantsTranslation.COUPLE_FRIENDLY_TITLE));
        contextRules.setRuleIcon("https://promos.makemytrip.com/Hotels_product/Details/Couplefriendly2x.png");
        return contextRules;
    }


    private FaqData buildFaqData(List<FrequentlyAskedQuestion> frequentlyAskedQuestions) {
        FaqData faqData = null;
        if (frequentlyAskedQuestions != null && !frequentlyAskedQuestions.isEmpty()) {
            faqData = new FaqData();
            List<Faqs> faqsList = new ArrayList<>();

            for (FrequentlyAskedQuestion frequentlyAskedQuestion : frequentlyAskedQuestions) {
                Faqs faqs = new Faqs();
                if (frequentlyAskedQuestion.getQuestion() != null) {
                    faqs.setQuestion(frequentlyAskedQuestion.getQuestion());
                }
                if (frequentlyAskedQuestion.getAnswer() != null) {
                    faqs.setAnswer(frequentlyAskedQuestion.getAnswer());
                }
                faqsList.add(faqs);
            }

            faqData.setFaqs(faqsList);
            faqData.setTitle(FAQ_TITLE);
            faqData.setHint(FAQ_HINT);
            faqData.setItemCountForCard(Integer.valueOf(FAQ_ITEM_COUNT));
            faqData.setExtraItemText(FAQ_EXTRA_TEXT.replace("%d", "" + frequentlyAskedQuestions.size()));
        }
        return faqData;
    }

    private CommonRules buildMustReadRules(List<String> mustReadRules) {
        if (CollectionUtils.isEmpty(mustReadRules)) {
            return null;
        }
        CommonRules mustReadRulesCG = new CommonRules();
        mustReadRulesCG.setCategory("must read");
        mustReadRulesCG.setRulesList(mustReadRules);
        return mustReadRulesCG;
    }

    private List<CommonRules> buildCommonRules(List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules> commonRules) {
        List<CommonRules> commonRulesCG = null;
        if (CollectionUtils.isNotEmpty(commonRules)) {
            commonRulesCG = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules commonRule : commonRules) {
                CommonRules commonRuleCG = new CommonRules();
                commonRuleCG.setCategory(commonRule.getCategory());
                if (StringUtils.isNotBlank(commonRule.getCategoryId())) {
                    commonRuleCG.setCategoryId(Integer.valueOf(commonRule.getCategoryId()));
                }
                commonRuleCG.setId(commonRule.getId());
                commonRuleCG.setRules(buildRules(commonRule.getRules()));
                commonRuleCG.setHostCatHeading(commonRule.getHeading());
                commonRuleCG.setShowInHost(commonRule.isShowInHost());
                commonRuleCG.setShowInDetailHome(commonRule.isShowInL1Page());
                commonRuleCG.setExpandRules(commonRule.isExpandable());
                if (commonRule.getGallery() != null) {
                    commonRuleCG.setImages(commonRule.getGallery().getUrls());
                    commonRuleCG.setShowInL2Page(commonRule.isShowInL2Page());
                }
                commonRulesCG.add(commonRuleCG);
            }
        }
        return commonRulesCG;
    }

    private List<Rule> buildRules(List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.Rule> rules) {
        List<Rule> rulesCG = null;
        if (CollectionUtils.isNotEmpty(rules)) {
            rulesCG = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.Rule rule : rules) {
                Rule ruleCG = new Rule();
                ruleCG.setDisplay(rule.isDisplay());
                ruleCG.setDisplayRank(rule.getDisplayRank());
                ruleCG.setSentiment(rule.getSentiment());
                ruleCG.setTemplateText(rule.getTemplateText());
                ruleCG.setText(rule.getText());
//TODO Pending
//                ruleCG.setIconUrl(utility.getUrlFromConfig(rule.ge));
//                ruleCG.setInfoData(rule.getRuleTableInfo());
                rulesCG.add(ruleCG);
            }
        }
        return rulesCG;
    }

    private List<ChildExtraBedPolicy> buildExtraBedPolicyList(List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.ChildExtraBedPolicy> extraBedPolicyList) {
        List<ChildExtraBedPolicy> listCG = null;
        if (CollectionUtils.isNotEmpty(extraBedPolicyList)){
            listCG = new ArrayList<>();
            for(com.gommt.hotels.orchestrator.detail.model.response.content.houserules.ChildExtraBedPolicy policy : extraBedPolicyList){
                listCG.add(buildChildExtraBedPolicy(policy));
            }
        }
        return listCG;
    }


    private ChildExtraBedPolicy buildChildExtraBedPolicy(com.gommt.hotels.orchestrator.detail.model.response.content.houserules.ChildExtraBedPolicy childExtraBedPolicy) {
        if (null == childExtraBedPolicy) {
            return null;
        }
        ChildExtraBedPolicy childExtraBedPolicyCG = new ChildExtraBedPolicy();
        childExtraBedPolicyCG.setId(childExtraBedPolicy.getId());
        childExtraBedPolicyCG.setLabel(childExtraBedPolicy.getLabel());
        childExtraBedPolicyCG.setPaid(childExtraBedPolicy.getPaid());
        childExtraBedPolicyCG.setPolicyInfo(childExtraBedPolicy.getPolicyInfo());
        childExtraBedPolicyCG.setPolicyRules(buildPolicyRules(childExtraBedPolicy.getPolicyRules()));
        return childExtraBedPolicyCG;
    }

    private List<PolicyRules> buildPolicyRules(List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.PolicyRules> policyRules) {
        List<PolicyRules> policyRulesCG = null;
        if (CollectionUtils.isNotEmpty(policyRules)) {
            policyRulesCG = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.PolicyRules policyRule : policyRules) {
                PolicyRules PolicyRulesCG = new PolicyRules();
                PolicyRulesCG.setAgeGroup(policyRule.getAgeGroup());
                PolicyRulesCG.setExtraBedTerms(buildExtraBedTerms(policyRule.getExtraBedTerms()));
                policyRulesCG.add(PolicyRulesCG);
            }
        }
        return policyRulesCG;
    }

    private Set<ExtraBedRules> buildExtraBedTerms(Set<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.ExtraBedRules> extraBedTerms) {
        Set<ExtraBedRules> extraBedRulesCG = null;
        if (CollectionUtils.isNotEmpty(extraBedTerms)) {
            extraBedRulesCG = new HashSet<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.ExtraBedRules extraBedTerm : extraBedTerms) {
                ExtraBedRules extraBedRuleCG = new ExtraBedRules();
                extraBedRuleCG.setLabel(extraBedTerm.getLabel());
                extraBedRuleCG.setValue(extraBedTerm.getValue());
                extraBedRulesCG.add(extraBedRuleCG);
            }
        }
        return extraBedRulesCG;
    }
    /**
     * Builds HostInfoV2 from orchestrator's HostInfo with complete field mapping
     *
     * @param hostInfo          The orchestrator's HostInfo object
     * @param ratingDataMap
     * @param isDisableHostChat Flag to disable host chat functionality
     * @return Fully populated HostInfoV2 object
     */
    private HostInfoV2 buildHostInfoV2(com.gommt.hotels.orchestrator.detail.model.response.content.HostInfo hostInfo, Map<RatingCategory, RatingData> ratingDataMap, boolean isDisableHostChat, boolean enableHostCalling) {
        if (hostInfo == null || StringUtils.isEmpty(hostInfo.getName())) {
            return null;
        }

        HostInfoV2 hostInfoV2 = new HostInfoV2();

        // Direct field mappings
        hostInfoV2.setName(hostInfo.getName());
        hostInfoV2.setAbout(checkIfPresentElseNull(hostInfo.getAbout()));
        hostInfoV2.setHostType(checkIfPresentElseNull(hostInfo.getHostType()));
        hostInfoV2.setTimeSinceHostingOnMmt(checkIfPresentElseNull(hostInfo.getTimeSinceHostingOnMmt()));
        hostInfoV2.setHostImage(checkIfPresentElseNull(hostInfo.getHostImage()));
        hostInfoV2.setResponseTime(checkIfPresentElseNull(hostInfo.getResponseTime()));
        hostInfoV2.setResponseRate(checkIfPresentElseNull(hostInfo.getResponseRate()));
        hostInfoV2.setLanguage(checkIfPresentElseNull(hostInfo.getLanguage()));
        hostInfoV2.setCaretakerAvailability(checkIfPresentElseNull(hostInfo.getCaretakerAvailability()));
        hostInfoV2.setCaretakerResponsibilities(checkIfPresentElseNull(hostInfo.getCaretakerResponsibilities()));
        hostInfoV2.setHostedPropertyText(checkIfPresentElseNull(hostInfo.getHostedPropertyText()));
        hostInfoV2.setHostedPropertyIcon(checkIfPresentElseNull(hostInfo.getHostedPropertyIcon()));
        hostInfoV2.setHostStayText(checkIfPresentElseNull(hostInfo.getHostStayText()));
        hostInfoV2.setHostStayIcon(checkIfPresentElseNull(hostInfo.getHostStayIcon()));

        // Chat enabled logic with disable flag
        hostInfoV2.setChatEnabled(!isDisableHostChat && hostInfo.isChatEnabled());
        if (enableHostCalling) {
            hostInfoV2.setCallingInfo(buildCallingInfo(hostInfo.getCallingInfo()));
        }

        // Convert RatingData to UGCRatingData if present
        if (MapUtils.isNotEmpty(ratingDataMap) && ratingDataMap.containsKey(RatingCategory.CARETAKER)) {
            hostInfoV2.setCareTakerRating(utility.buildRatingData(ratingDataMap.get(RatingCategory.CARETAKER), new UGCRatingData()));
        }

        return hostInfoV2;
    }

    private CallingInfo buildCallingInfo(com.gommt.hotels.orchestrator.detail.model.response.content.CallingInfo callingInfo) {
        if (callingInfo == null || StringUtils.isEmpty(callingInfo.getChainName())) {
            return null;
        }
        CallingInfo callingInfoCG = new CallingInfo();
        callingInfoCG.setChainName(callingInfo.getChainName());
        callingInfoCG.setEndTime(checkIfPresentElseNull(callingInfo.getEndTime()));
        callingInfoCG.setStartTime(checkIfPresentElseNull(callingInfo.getStartTime()));
        return callingInfoCG;
    }

    private String checkIfPresentElseNull(String inputString) {
        return StringUtils.isEmpty(inputString) ? null : inputString;
    }

    private List<String> checkIfPresentElseNull(List<String> inputList) {
        return CollectionUtils.isEmpty(inputList) ? null : inputList;
    }

    private List<GovtPolicies> buildGovtPolies(List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules> commonRules) {
        List<GovtPolicies> govtPolicies = new ArrayList<>();
        commonRules.forEach(govtPolicyFromHES -> {
            if (govtPolicyFromHES != null) {
                GovtPolicies govPolicy = new GovtPolicies();
                govPolicy.setNidhiId(govtPolicyFromHES.getId());
                if (CollectionUtils.isNotEmpty((govtPolicyFromHES.getRules()))) {
                    com.gommt.hotels.orchestrator.detail.model.response.content.houserules.Rule rule = govtPolicyFromHES.getRules().get(0);
                    govPolicy.setLogoUrl(rule.getIconUrl());
                    govPolicy.setValidTill(rule.getText());
                }
                govPolicy.setTitle(govtPolicyFromHES.getTitle());
                govPolicy.setSubTitle(govtPolicyFromHES.getSubTitle());

                govtPolicies.add(govPolicy);
            }
        });
        return govtPolicies;

    }

    private CalendarCriteria buildCalendarCriteria(com.gommt.hotels.orchestrator.detail.model.response.content.CalendarCriteria calendarCriteriaHES) {
        if (calendarCriteriaHES == null)
            return null;
        CalendarCriteria calendarCriteriaCG = new CalendarCriteria();
        calendarCriteriaCG.setAdvanceDays(calendarCriteriaHES.getDisplayDuration());
        calendarCriteriaCG.setAvailable(calendarCriteriaHES.isEnabled());
        calendarCriteriaCG.setMaxDate(calendarCriteriaHES.getEndDate());
        calendarCriteriaCG.setMlos(calendarCriteriaHES.getMinimumStayLength());
        return calendarCriteriaCG;
    }

    private LocationDetail buildLocationDetail(String cityCode, String cityName, String cityCtyCode, String country) {
        LocationDetail locationDetail = new LocationDetail();
        locationDetail.setId(cityCode);
        locationDetail.setName(cityName);
        locationDetail.setType("city");
        locationDetail.setCountryId(cityCtyCode);
        locationDetail.setCountryName(country);
        return locationDetail;
    }


    private PropertyChainCG getPropertyChain(PropertyChain propertyChain) {
        PropertyChainCG propertyChainCG = new PropertyChainCG();
        if (propertyChain.getDetails() != null)
            propertyChainCG.setDetails(getPropertyDetails(propertyChain.getDetails()));
        propertyChainCG.setSummary(propertyChain.getSummary());
        propertyChainCG.setLogo(propertyChain.getLogo());
        return propertyChainCG;
    }

    private PropertyChainDetailCG getPropertyDetails(PropertyChainDetails details) {
        PropertyChainDetailCG propertyChainDetailCG = new PropertyChainDetailCG();
        propertyChainDetailCG.setTitle(details.getTitle());
        propertyChainDetailCG.setDesc(details.getDesc());
        return propertyChainDetailCG;
    }


    private PropertyHighlightCG getPropertyHighLights(com.gommt.hotels.orchestrator.detail.model.response.content.PropertyHighlights propertyHighlights) {
        PropertyHighlightCG propertyHighlightCG = new PropertyHighlightCG();
        if (propertyHighlights.getDetails() != null)
            propertyHighlightCG.setDetails(getDetails(propertyHighlights.getDetails()));
        propertyHighlightCG.setTitle(propertyHighlights.getTitle());
        return propertyHighlightCG;
    }

//    private ReportCardPersuasion buildReportCardPersuasion(@NonNull PersuasionData reportCardPersuasionBO) {
//        //TODO: peitho is to be integrated for details page
//        ReportCardPersuasion reportCardPersuasion = new ReportCardPersuasion();
//        reportCardPersuasion.setIconUrl(reportCardPersuasionBO.getIconurl());
//        reportCardPersuasion.setText(reportCardPersuasionBO.getText());
//        return reportCardPersuasion;
//    }

    private List<PropertyHighlightDetailCG> getDetails(List<com.gommt.hotels.orchestrator.detail.model.response.content.PropertyHighlightDetails> details) {
        List<PropertyHighlightDetailCG> highlightDetailCGList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(details)) {
            for (com.gommt.hotels.orchestrator.detail.model.response.content.PropertyHighlightDetails detail : details) {
                PropertyHighlightDetailCG propertyHighlightDetailCG = new PropertyHighlightDetailCG();
                propertyHighlightDetailCG.setDesc(detail.getDesc());
                propertyHighlightDetailCG.setTitle(detail.getTitle());
                propertyHighlightDetailCG.setIcon(detail.getIcon());
                highlightDetailCGList.add(propertyHighlightDetailCG);
            }
        }
        return highlightDetailCGList;
    }

    private void modifyStreetViewInfo(StreetViewInfo streetViewInfo) {
        if (Objects.nonNull(commonConfigConsul)) {
            streetViewInfo.setIconurl(commonConfigConsul.getStreetViewIconUrl());
        }
        streetViewInfo.setTitle(polyglotService.getTranslatedData(STREET_VIEW_TITLE_TEXT));
        streetViewInfo.setSubTitle(polyglotService.getTranslatedData(STREET_VIEW_SUBTITLE_TEXT));
    }

    private <T> void limitAmenitiesCount(List<T> highLightedAmenities, boolean serviceApartment, boolean isAltAcco) {
        if (CollectionUtils.isEmpty(highLightedAmenities)) {
            return;
        }
        int total = highLightedAmenities.size();
        int amenitiesToShow = SIX;
        if (total <= amenitiesToShow) {
            return;
        } else {
            int extra = total - amenitiesToShow;
            if (extra < 0) {
                int q = total / amenitiesToShow;
                extra = total - (q * amenitiesToShow);
            }
            for (int i = 0; i < extra; i++) {
                highLightedAmenities.remove((highLightedAmenities.size() - 1));
            }
        }
    }


    private HouseRulesV2 buildFoodDining(List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules> foodDiningRule,
                                         Map<String, String> expDataMap, StaticDetailCriteria staticDetailRequest, DeviceDetails deviceDetails, boolean foodAndDiningV2,
                                         boolean foodDiningRevamp, RatingData foodRatingData, boolean isAltAcco, boolean isDhCall,
                                         boolean foodAndDiningEnhancement) {
        HouseRulesV2 foodDining = new HouseRulesV2();
        foodDining.setTitle(foodDiningRevamp ? polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_CARD_TITLE) : polyglotService.getTranslatedData(FOOD_AND_DINING));
        foodDining.setTag(true);
        if (foodRatingData != null && utility.isExperimentTrue(expDataMap, FOOD_DINING_SECTION_RATING.getKey())) {
            foodDining.setRatingData(utility.buildRatingData(foodRatingData, new UGCRatingData()));
        }

        List<CommonRules> allRulesList = new ArrayList<>();
        List<String> summaryList = new ArrayList<>();
        List<SummaryItem> summaryListV2 = new ArrayList<>();
        List<Restaurant> restaurants = new ArrayList<>();
        Map<String, List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules>> sectionToRuleMap = buildSectionToRuleMap(foodDiningRule);

        if (MapUtils.isNotEmpty(sectionToRuleMap)) {
            for (Map.Entry<String, List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules>> sectionToRuleEntry : sectionToRuleMap.entrySet()) {
                if (CollectionUtils.isNotEmpty(sectionToRuleEntry.getValue())) {
                    for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules commonRule : sectionToRuleEntry.getValue()) {
                        if (sectionToRuleEntry.getKey().equalsIgnoreCase(FoodAndDiningEnums.FoodMenu.getName())) {
                            continue;
                        }
                        //Ignoring Cook Section
                        if (foodDiningRevamp && sectionToRuleEntry.getKey().equalsIgnoreCase(FoodAndDiningEnums.Cook.getName())) {
                            if (Objects.nonNull(commonRule) && StringUtils.isNotEmpty(commonRule.getSummaryText())) {
                                summaryList.add(commonRule.getSummaryText());
                            }
                            continue;
                        }
                        CommonRules commonRules = new CommonRules();
                        if (foodDiningRevamp && Objects.nonNull(commonRule) &&
                                FoodAndDiningEnums.Kitchen.getName().equalsIgnoreCase(commonRule.getCategory())) {
                            commonRules.setCategory(KITCHEN_CATEGORY_REVAMP);
                        } else {
                            commonRules.setCategory(commonRule.getCategory());
                        }
                        commonRules.setDescription(commonRule.getHeading());

                        if (foodDiningMergeSections.contains(commonRules.getCategory()) || FoodAndDiningEnums.IndianFoodOptions.getName().equalsIgnoreCase(commonRule.getCategory())) {
                            commonRules.setMergeId(FND_MERGE_ID_STRING);
                        }

                        if (commonRule.getCategory().equalsIgnoreCase(FoodAndDiningEnums.Meals.getName()) && sectionToRuleMap.containsKey(FoodAndDiningEnums.FoodMenu.getName())) {
                            commonRules.setRules(buildMealRuleList(sectionToRuleMap, deviceDetails));
                        } else if (CollectionUtils.isNotEmpty(commonRule.getRules())) {
                            commonRules.setRules(buildRuleList(commonRule));
                        }

                        if (!(foodAndDiningV2 & ADDITIONAL_INFORMATION.equalsIgnoreCase(commonRule.getCategory()))) {
                            commonRules.setShowInDetailHome(true);
                        }
                        if (commonRule.getGallery() != null) {
                            commonRules.setImages(commonRule.getGallery().getUrls());
                        }
                        if (FoodAndDiningEnums.IndianFoodOptions.getName().equalsIgnoreCase(commonRule.getCategory()) ||
                                FoodAndDiningEnums.FoodAndDiningPropertyRules.getName().equalsIgnoreCase(commonRule.getCategory())) {
                            commonRules.setId(commonRule.getId());
                            commonRules.setShowInDetailHome(commonRule.isShowInL1Page());
                            commonRules.setShowInL2Page(commonRule.isShowInL2Page());
                        }

                        if (FoodAndDiningEnums.Restaurant.getName().equalsIgnoreCase(commonRule.getCategory())) {
                            if (!isAltAcco && foodAndDiningEnhancement) {
                                foodDining.setTitle(RESTAURANTS_TITLE);
                            }
                            commonRules.setId(commonRule.getId());
                            if (!foodAndDiningEnhancement)
                                commonRules.setMergeId(commonRule.getMergeId());
                            commonRules.setShowInDetailHome(commonRule.isShowInL1Page());
                            if (Utility.isBookingDeviceDesktop(deviceDetails)) {
                                commonRules.setShowInDetailHome(false);
                            }
                            if (isDhCall && CollectionUtils.isNotEmpty(commonRule.getGallery().getUrls())) {
                                commonRules.setShowArrowInDetailHome(false);
                                restaurants.add(new Restaurant(commonRule.getHeading(), commonRule.getGallery().getUrls().get(0), commonRule.getId()));
                            }
                            if (StringUtils.isNotEmpty(commonRule.getUspText())) {
                                commonRules.setUspText(commonRule.getUspText());
                            }
                        }
                        allRulesList.add(commonRules);
                        if (!ADDITIONAL_INFORMATION.equalsIgnoreCase(commonRule.getCategory())
                                && !Utility.isBookingDeviceDesktop(deviceDetails)) {
                            if (FoodAndDiningEnums.IndianFoodOptions.getName().equalsIgnoreCase(commonRule.getCategory())) {
                                if (CollectionUtils.isNotEmpty(commonRule.getRules())) {
                                    for (Rule rule : commonRules.getRules()) {

                                        if (StringUtils.isNotEmpty(rule.getText())) {
                                            summaryList.add(rule.getText());
                                        }
                                        if (StringUtils.isNotEmpty(rule.getText())) {
                                            summaryListV2.add(new SummaryItem(rule.getText(), rule.getIconUrl()));
                                        }
                                    }
                                }
                            } else if (foodAndDiningV2 && StringUtils.isNotEmpty(commonRule.getSummaryText()) && !FoodAndDiningEnums.FoodAndDiningPropertyRules.getName().equalsIgnoreCase(commonRule.getCategory())) {
                                summaryList.add(commonRule.getSummaryText());
                                if (StringUtils.isNotEmpty(commonRule.getSummaryText())) {
                                    summaryListV2.add(new SummaryItem(commonRule.getSummaryText(), null));
                                }
                            } else if (StringUtils.isNotEmpty(commonRule.getHeading()) &&
                                    !Constants.MEALS_AND_COOK_DETAILS.equalsIgnoreCase(commonRule.getCategory()) &&
                                    !FoodAndDiningEnums.Restaurant.getName().equalsIgnoreCase(commonRule.getCategory()) && !FoodAndDiningEnums.FoodAndDiningPropertyRules.getName().equalsIgnoreCase(commonRule.getCategory())) {
                                summaryList.add(commonRule.getHeading());
                                if (StringUtils.isNotEmpty(commonRule.getHeading())) {
                                    summaryListV2.add(new SummaryItem(commonRule.getHeading(), null));
                                }
                            }
                        }
                    }
                }
            }
        }
        foodDining.setRestaurants(CollectionUtils.isNotEmpty(restaurants) ? restaurants : null);

        foodDining.setAllRules(CollectionUtils.isNotEmpty(allRulesList) ? allRulesList : null);

        //if there is only one section then put ruleText from ruleList into summary list
        if (CollectionUtils.isNotEmpty(foodDiningRule) && foodDiningRule.size() == 1 && ADDITIONAL_INFORMATION.equalsIgnoreCase(foodDiningRule.get(0).getCategory())) {
            handleOneFoodAndDiningSection(foodDiningRule.get(0), summaryList, allRulesList);
        }

        // these are L1 overview pointers
        if (CollectionUtils.isNotEmpty(summaryList)) {
            // if childContext (child count > 0) is there in request, add node "Special meal for Kids is available on request"
            if (MapUtils.isNotEmpty(sectionToRuleMap) && sectionToRuleMap.containsKey(FoodAndDiningEnums.Cook.getName())) {
                Integer childCount = utility.getTotalChildrenFromRequest(staticDetailRequest.getRoomStayCandidates());
                if (childCount != null && childCount > 0) {
                    String mealForKids = polyglotService.getTranslatedData(ConstantsTranslation.MEAL_FOR_KIDS);
                    if (StringUtils.isNotEmpty(mealForKids) && !Constants.NULL_STRING.equalsIgnoreCase(mealForKids)) {
                        summaryList.add(mealForKids);
                    }
                }
            }
            foodDining.setSummary(summaryList);
            foodDining.setSummaryV2(summaryListV2);
        }
        return foodDining;
    }

    private Map<String, List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules>> buildSectionToRuleMap(List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules> foodDiningRule) {
        Map<String, List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules>> sectionToRuleMap = new LinkedHashMap<>();

        if (CollectionUtils.isNotEmpty(foodDiningRule)) {
            for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules commonRule : foodDiningRule) {
                if (StringUtils.isNotEmpty(commonRule.getCategory())) {
                    if (!sectionToRuleMap.containsKey(commonRule.getCategory())) {
                        sectionToRuleMap.put(commonRule.getCategory(), new ArrayList<>());
                    }
                    List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules> rulesList = sectionToRuleMap.get(commonRule.getCategory());
                    rulesList.add(commonRule);
                    sectionToRuleMap.put(commonRule.getCategory(), rulesList);
                }
            }
        }
        return sectionToRuleMap;
    }

    private List<Rule> buildMealRuleList(Map<String, List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules>> sectionToCommonRuleMap, DeviceDetails deviceDetails) {
        Rule foodMenuRule = null;
        if (sectionToCommonRuleMap.containsKey(FoodAndDiningEnums.FoodMenu.getName())
                && CollectionUtils.isNotEmpty(sectionToCommonRuleMap.get(FoodAndDiningEnums.FoodMenu.getName()).get(0).getRules())
                && null != sectionToCommonRuleMap.get(FoodAndDiningEnums.FoodMenu.getName()).get(0).getRules().get(0)) {
            com.gommt.hotels.orchestrator.detail.model.response.content.houserules.Rule foodAndDiningMenuRule = sectionToCommonRuleMap.get(FoodAndDiningEnums.FoodMenu.getName()).get(0).getRules().get(0);
            foodMenuRule = new Rule();
            foodMenuRule.setText(foodAndDiningMenuRule.getText());
            foodMenuRule.setImages(foodAndDiningMenuRule.getImages());
            foodMenuRule.setImageCategory(foodAndDiningMenuRule.getImageCategory());
            //TODO :: Ask Lepsy
//            foodMenuRule.setInfoData(foodAndDiningMenuRule.getInfoData());
            foodMenuRule.setIconUrl(utility.getUrlFromConfig(foodAndDiningMenuRule.getIconUrl()));
        }

        List<Rule> ruleList = buildRuleList(sectionToCommonRuleMap.get(FoodAndDiningEnums.Meals.getName()).get(0));
        if (deviceDetails != null && Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(deviceDetails.getBookingDevice())) {
            if (foodMenuRule != null && sectionToCommonRuleMap.get(FoodAndDiningEnums.Meals.getName()).get(0).getRules().size() > foodMenuPosition) {
                ruleList.add(foodMenuPosition, foodMenuRule);
            }
        } else {
            ruleList.add(foodMenuRule);
        }
        return ruleList;
    }

    private List<Rule> buildRuleList(com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules commonRule) {
        List<Rule> ruleList = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.Rule rules : commonRule.getRules()) {
            Rule rule = new Rule();
            rule.setText(rules.getText());
            rule.setIconUrl(utility.getUrlFromConfig(rules.getIconUrl()));
            rule.setInfoData(buildInfoData(rules.getRuleTableInfo()));
            ruleList.add(rule);
        }
        return ruleList;
    }

    private InfoData buildInfoData(com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleTableInfo ruleTableInfo) {
        if (ruleTableInfo != null) {
            InfoData infoData = new InfoData();
            infoData.setTitle(ruleTableInfo.getKeyTitle());
            ArrayList<Datum> datumArrayList = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleInfo ruleInfo : ruleTableInfo.getInfoList()) {
                Datum datum = new Datum();
                datum.setKey(ruleInfo.getKey());
                if(CollectionUtils.isNotEmpty(ruleInfo.getValue())) {
                    datum.setValue(ruleInfo.getValue().get(0));
                }
                datumArrayList.add(datum);
            }
            if(CollectionUtils.isNotEmpty(datumArrayList)) {
                infoData.setData(datumArrayList);
                return infoData;
            }
        }
        return null;
    }

    private void handleOneFoodAndDiningSection(com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules commonRule, List<String> summaryList, List<CommonRules> allRulesList) {
        summaryList.clear();
        if (CollectionUtils.isNotEmpty(commonRule.getRules())) {
            int count = 0;
            for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.Rule rule : commonRule.getRules()) {
                if (StringUtils.isEmpty(rule.getText())) {
                    continue;
                }

                count++;
                summaryList.add(rule.getText());
                if (count == foodDiningMinCountConfig) {
                    break;
                }
            }
            allRulesList.clear();
        }
    }

    private com.mmt.hotels.model.response.staticdata.FlexibleCheckinInfo mapFlexibleCheckinInfo(
            com.gommt.hotels.orchestrator.detail.model.response.content.flexiblecheckin.FlexibleCheckinInfo flexibleCheckinInfo) {
        if (flexibleCheckinInfo == null) {
            return null;
        }

        com.mmt.hotels.model.response.staticdata.FlexibleCheckinInfo mappedInfo =
                new com.mmt.hotels.model.response.staticdata.FlexibleCheckinInfo();

        // Map title
        if (flexibleCheckinInfo.getTitle() != null) {
            mappedInfo.setTitle(flexibleCheckinInfo.getTitle());
        }

        // Map subtitle
        if (flexibleCheckinInfo.getSubTitle() != null) {
            mappedInfo.setSubTitle(flexibleCheckinInfo.getSubTitle());
        }

        // Map default slot message
        if (flexibleCheckinInfo.getDefaultSlotMsg() != null) {
            mappedInfo.setDefaultSlotMsg(flexibleCheckinInfo.getDefaultSlotMsg());
        }

        // Map time slots
        if (flexibleCheckinInfo.getTimeSlots() != null && !flexibleCheckinInfo.getTimeSlots().isEmpty()) {
            List<com.mmt.hotels.model.response.staticdata.TimeSlot> mappedTimeSlots = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.content.flexiblecheckin.TimeSlot timeSlot : flexibleCheckinInfo.getTimeSlots()) {
                com.mmt.hotels.model.response.staticdata.TimeSlot mappedSlot = new com.mmt.hotels.model.response.staticdata.TimeSlot();
                if (timeSlot.getId() != null) {
                    mappedSlot.setId(timeSlot.getId());
                }
                if (timeSlot.getValue() != null) {
                    mappedSlot.setValue(timeSlot.getValue());
                }
                mappedTimeSlots.add(mappedSlot);
            }
            mappedInfo.setTimeSlots(mappedTimeSlots);
        }

        // Map tag URL
        if (flexibleCheckinInfo.getTagUrl() != null) {
            mappedInfo.setTagUrl(flexibleCheckinInfo.getTagUrl());
        }

        // Map subtitle default
        if (flexibleCheckinInfo.getSubTitleDefault() != null) {
            mappedInfo.setSubTitleDefault(flexibleCheckinInfo.getSubTitleDefault());
        }

        // Map subtitle slot selected
        if (flexibleCheckinInfo.getSubTitleSlotSelected() != null) {
            mappedInfo.setSubTitleSlotSelected(flexibleCheckinInfo.getSubTitleSlotSelected());
        }

        // Map tag info
        if (flexibleCheckinInfo.getTagInfo() != null) {
            com.mmt.hotels.model.response.staticdata.TagInfo mappedTagInfo = new com.mmt.hotels.model.response.staticdata.TagInfo();

            if (flexibleCheckinInfo.getTagInfo().getText() != null) {
                mappedTagInfo.setText(flexibleCheckinInfo.getTagInfo().getText());
            }

            if (flexibleCheckinInfo.getTagInfo().getTextColor() != null) {
                mappedTagInfo.setTextColor(flexibleCheckinInfo.getTagInfo().getTextColor());
            }

            // Map BgGradient if it exists
            if (flexibleCheckinInfo.getTagInfo().getBgGradient() != null) {
                com.mmt.hotels.model.persuasion.response.BgGradient mappedBgGradient = new com.mmt.hotels.model.persuasion.response.BgGradient();
                // Map BgGradient fields - assuming similar structure
                // You may need to adjust this based on the actual BgGradient structure
                mappedTagInfo.setBgGradient(mappedBgGradient);
            }

            mappedInfo.setTagInfo(mappedTagInfo);
        }

        // Map bottom sheet info
        if (flexibleCheckinInfo.getBottomSheetInfo() != null) {
            com.mmt.hotels.model.response.staticdata.FlexiCheckinBottomSheet mappedBottomSheet =
                    new com.mmt.hotels.model.response.staticdata.FlexiCheckinBottomSheet();

            if (flexibleCheckinInfo.getBottomSheetInfo().getTitle() != null) {
                mappedBottomSheet.setTitle(flexibleCheckinInfo.getBottomSheetInfo().getTitle());
            }

            if (flexibleCheckinInfo.getBottomSheetInfo().getDescription() != null) {
                mappedBottomSheet.setDescription(flexibleCheckinInfo.getBottomSheetInfo().getDescription());
            }

            // Map time slots in bottom sheet
            if (flexibleCheckinInfo.getBottomSheetInfo().getTimeSlots() != null &&
                    !flexibleCheckinInfo.getBottomSheetInfo().getTimeSlots().isEmpty()) {
                List<com.mmt.hotels.model.response.staticdata.TimeSlot> mappedBottomSheetTimeSlots = new ArrayList<>();
                for (com.gommt.hotels.orchestrator.detail.model.response.content.flexiblecheckin.TimeSlot timeSlot :
                        flexibleCheckinInfo.getBottomSheetInfo().getTimeSlots()) {
                    com.mmt.hotels.model.response.staticdata.TimeSlot mappedSlot = new com.mmt.hotels.model.response.staticdata.TimeSlot();
                    if (timeSlot.getId() != null) {
                        mappedSlot.setId(timeSlot.getId());
                    }
                    if (timeSlot.getValue() != null) {
                        mappedSlot.setValue(timeSlot.getValue());
                    }
                    mappedBottomSheetTimeSlots.add(mappedSlot);
                }
                mappedBottomSheet.setTimeSlots(mappedBottomSheetTimeSlots);
            }

            mappedInfo.setBottomSheetInfo(mappedBottomSheet);
        }

        return mappedInfo;
    }


    private SupportDetails buildSupportDetailsForABO() {
        SupportDetails supportDetails = new SupportDetails();
        supportDetails.setOptions(new ArrayList<>());
        supportDetails.getOptions().add(callToBookOption);
        return supportDetails;
    }

    public String buildRoomStayCandidateFromSearchWrapper(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        StringBuilder builder = new StringBuilder();
        if (CollectionUtils.isEmpty(roomStayCandidates)) {
            return builder.toString();
        }
        for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate : roomStayCandidates) {
            int adultCount = roomStayCandidate.getAdultCount();
            int childCount = roomStayCandidate.getChildAges() != null ? roomStayCandidate.getChildAges().size() : 0;
            builder.append(adultCount);
            builder.append(Constants.RSQ_SPLITTER);
            builder.append(childCount);
            builder.append(Constants.RSQ_SPLITTER);
            if (CollectionUtils.isNotEmpty(roomStayCandidate.getChildAges())) {
                for (int age : roomStayCandidate.getChildAges()) {
                    if (age >= 0 && age <= 12) {
                        builder.append(age);
                        builder.append(Constants.RSQ_SPLITTER);
                    }
                }
            }
        }
        return builder.toString();
    }

    public static String getEncodedUrl(String url) {
        String encodedString = "";
        try {
            if (StringUtils.isNotBlank(url))
                encodedString = URLEncoder.encode(url, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
        } catch (Exception e) {
            LOGGER.warn("Error while encoding url: {}", url);
        }
        return encodedString;
    }

    public String prepareDeepLinkUrlForGroupBooking(StaticDetailRequest staticDetailRequest, StaticDetailCriteria staticDetailCriteria, DeviceDetails deviceDetails, boolean myPartnerRequest) {
        String deeplink = null;
        try {
            // Extract parameters from StaticDetailRequest and StaticDetailCriteria
            String idContext = staticDetailRequest.getRequestDetails() != null ? staticDetailRequest.getRequestDetails().getIdContext() : "";
            String funnelSource = staticDetailRequest.getRequestDetails() != null ? staticDetailRequest.getRequestDetails().getFunnelSource() : "";
            Integer visitNumber = staticDetailRequest.getRequestDetails() != null ? staticDetailRequest.getRequestDetails().getVisitNumber() : 0;
            String visitorId = staticDetailRequest.getRequestDetails() != null ? staticDetailRequest.getRequestDetails().getVisitorId() : "";
            String deviceType = deviceDetails != null ? deviceDetails.getDeviceType() : "";
            String bookingDevice = deviceDetails != null ? deviceDetails.getBookingDevice() : "";
            String deviceId = deviceDetails != null ? deviceDetails.getDeviceId() : "";
            String appVersion = deviceDetails != null ? deviceDetails.getAppVersion() : "";

            // Build room stay parameters
            String roomStayParam = buildRoomStayCandidateFromSearchWrapper(staticDetailCriteria.getRoomStayCandidates());

            // Extract dates
            String checkin = staticDetailCriteria.getCheckIn() != null ?
                    dateUtil.getDateFormatted(staticDetailCriteria.getCheckIn(), DateUtil.YYYY_MM_DD, MMDDYYYY) : "";
            String checkout = staticDetailCriteria.getCheckOut() != null ?
                    dateUtil.getDateFormatted(staticDetailCriteria.getCheckOut(), DateUtil.YYYY_MM_DD, MMDDYYYY) : "";

            String cityName = staticDetailCriteria.getCityName();
            String cityId = staticDetailCriteria.getLocationId();
            String country = staticDetailCriteria.getCountryCode() != null ? staticDetailCriteria.getCountryCode() : "IN";
            String locationType = staticDetailCriteria.getLocationType();
            String currency = StringUtils.isNotBlank(staticDetailCriteria.getCurrency()) ? staticDetailCriteria.getCurrency() : DEFAULT_CUR_INR;

            deeplink = MessageFormat.format(getGroupBookingDeepLink(staticDetailRequest, staticDetailCriteria, myPartnerRequest),
                    checkin, checkout, cityId, country, cityId, locationType, roomStayParam, currency,
                    appVersion, deviceId, bookingDevice, deviceType, visitorId, visitNumber, funnelSource, idContext);

            if (StringUtils.isNotBlank(cityName)) {
                deeplink = deeplink + Constants.AND_SEPARATOR + Constants.SEARCHTEXT_URL_PARAM + Constants.PR_SEPARATOR + getEncodedUrl(cityName);
            }

            if (StringUtils.isNotBlank(funnelSource)) {
                deeplink += getQueryParameter(FUNNEL_NAME, funnelSource);
            }
        } catch (Exception e) {
            LOGGER.error("Exception occurred while preparing deeplink for group booking : {}", e.getMessage());
        }
        return deeplink;
    }

    public String getQueryParameter(String queryParam, String value) {
        return Constants.AND_SEPARATOR + queryParam + Constants.PR_SEPARATOR + value;
    }

    private String getGroupBookingDeepLink(StaticDetailRequest staticDetailRequest, StaticDetailCriteria staticDetailCriteria, boolean myPartnerRequest) {
        // Check for MyPartner
        if (myPartnerRequest) {
            return groupBookingDeepLinkMyPartner;
        }
        
        // Check for Global entity
//        if (staticDetailCriteria.getUserGlobalInfo() != null && GLOBAL_ENTITY.equalsIgnoreCase(staticDetailCriteria.getUserGlobalInfo().getEntityName())) {
//            if (REGION_SA.equalsIgnoreCase(staticDetailCriteria.getSiteDomain())) {
//                return groupBookingDeepLinkGlobal.replace(WWW_SUBDOMAIN, SA_SUBDOMAIN);
//            } else if (REGION_AE.equalsIgnoreCase(staticDetailCriteria.getSiteDomain()) &&
//                       REGION_AE.equalsIgnoreCase(staticDetailCriteria.getUserGlobalInfo().getUserCountry())) {
//                return groupBookingDeepLinkGlobal.replace(WWW_SUBDOMAIN, AE_SUBDOMAIN);
//            }
//            return groupBookingDeepLinkGlobal;
//        }
        return groupBookingDeepLinkIndia;
    }

    /**
     * Build chatbot info URL with affiliate ID - adapted from HES-Detail HotelUtil.getChatbotInfoUrl
     */
    private com.mmt.hotels.model.response.staticdata.ChatbotInfo buildChatbotInfo(HotelResult hotelResult,
                                                                                  LocationInfo locationInfo, StaticDetailRequest staticDetailRequest,
                                                                                  StaticDetailCriteria staticDetailCriteria,
                                                                                  CommonModifierResponse commonModifierResponse, Map<String, String> expDataMap, String affiliateId,
                                                                                  boolean isTravelPlexEnabled) {
        com.mmt.hotels.model.response.staticdata.ChatbotInfo chatbotInfo = new com.mmt.hotels.model.response.staticdata.ChatbotInfo();
        
        // Base URLs from HES-Detail implementation
        String hotelsChatbotUrl = "https://myra.makemytrip.com/chat-expert?lob=HOTELS&lobCategory=dh&project=hotels_dh_chatbot&page={0}&hotelID={1}&hotelName={2}&checkin={3}&checkout={4}&city={5}&country={6}&roomStayCandidates={7}&affiliateId={8}&funnelSource={9}&userCountry={10}&region={11}&locationType={12}&mmPoiTag={13}&mmAreaTag={14}";
        String hotelsChatbotUrlGlobal = "https://myra.makemytrip.global/chat-expert?lob=HOTELS&lobCategory=dh&project=hotels_dh_chatbot&page={0}&hotelID={1}&hotelName={2}&checkin={3}&checkout={4}&city={5}&country={6}&roomStayCandidates={7}&affiliateId={8}&funnelSource={9}&userCountry={10}&region={11}&locationType={12}&mmPoiTag={13}&mmAreaTag={14}";
        String hotelsChatbotIconUrl = "https://imgak.mmtcdn.com/flights/assets/media/mobile/listing/3X/myra_new_gif.webp";
        
        // Extract parameters from requests
        String hotelName = StringUtils.isNotEmpty(hotelResult.getName()) ? 
                java.util.Base64.getEncoder().encodeToString(hotelResult.getName().getBytes()) : EMPTY_STRING;
        String locationType = staticDetailCriteria.getLocationType();
        String locationId = StringUtils.isNotEmpty(locationInfo.getLocationId()) ? locationInfo.getLocationId() : staticDetailCriteria.getLocationId();
        String countryCode = StringUtils.isNotEmpty(locationInfo.getCountryCode()) ? locationInfo.getCountryCode() : staticDetailCriteria.getCountryCode();
        String region = Utility.isGccOrKsa() ? "AE" : "IN";

        // Extract dates
        String checkin = staticDetailCriteria.getCheckIn() != null ?
                dateUtil.getDateFormatted(staticDetailCriteria.getCheckIn(), DateUtil.YYYY_MM_DD, MMDDYYYY) : "";
        String checkout = staticDetailCriteria.getCheckOut() != null ?
                dateUtil.getDateFormatted(staticDetailCriteria.getCheckOut(), DateUtil.YYYY_MM_DD, MMDDYYYY) : "";
        
        String pageContext = staticDetailRequest.getRequestDetails().getPageContext();
        String funnelSource = staticDetailRequest.getRequestDetails().getFunnelSource();
        String roomStayCandidates = staticDetailRequest.getSearchCriteria() != null ?
                utility.buildRscValue(staticDetailRequest.getSearchCriteria().getRoomStayCandidates()) : "";

        
        // User country - can be enhanced based on available request data
        String userCountry = commonModifierResponse.getUserCountry();
        
        // Area and POI tags - simplified for CG implementation
        String mmPoiTag = buildMmtPoiTag(staticDetailRequest.getMatchMakerDetails());
        String mmAreaTag = buildAreaTags(staticDetailRequest.getMatchMakerDetails());
        
        // Determine which URL to use based on global entity
        String chatBotDeeplinkBaseUrl = hotelsChatbotUrl;
        UserGlobalInfo userGlobalInfo = staticDetailCriteria.getUserGlobalInfo();
        boolean isGlobalEntity = userGlobalInfo != null && GLOBAL_ENTITY.equalsIgnoreCase(userGlobalInfo.getEntityName());
        
        if (isGlobalEntity) {
            chatBotDeeplinkBaseUrl = hotelsChatbotUrlGlobal;
        }
        
        // Build final URL using MessageFormat
        String finalChatbotUrl = MessageFormat.format(chatBotDeeplinkBaseUrl, 
                pageContext, hotelResult.getId(), hotelName, checkin, checkout,
                locationId, countryCode, roomStayCandidates, 
                StringUtils.isNotEmpty(affiliateId) ? affiliateId : EMPTY_STRING,
                funnelSource, userCountry, region, locationType, mmPoiTag, mmAreaTag);
        
        chatbotInfo.setChatBotUrl(finalChatbotUrl);
        chatbotInfo.setIconUrl(hotelsChatbotIconUrl);
        chatbotInfo.setType("CHATBOT");
        
        // Build tooltip data
        com.mmt.hotels.model.response.staticdata.TooltipData tooltipData = new com.mmt.hotels.model.response.staticdata.TooltipData();
        tooltipData.setText(polyglotService.getTranslatedData("HOTELS_CHATBOT_TOOLTIP_TEXT"));
        tooltipData.setTimer(15000); // 15 seconds as in HES-Detail
        chatbotInfo.setTooltipData(tooltipData);
        
        // Build LOB metadata JSON
        chatbotInfo.setLobMetaData(buildLobMetaDataJson(hotelResult, staticDetailRequest, staticDetailCriteria, affiliateId, checkin, checkout, roomStayCandidates, locationId, countryCode, isGlobalEntity));
        
        // Apply travel plex and consul configurations
        return utility.buildChatbotInfoStaticDetail(chatbotInfo, isTravelPlexEnabled);
    }

    private String buildAreaTags(MatchMakerRequest matchMakerRequest) {
        StringBuilder mmAreaTagBuilder = new StringBuilder();
        if (matchMakerRequest != null && CollectionUtils.isNotEmpty(matchMakerRequest.getSelectedTags())) {
            for (Tags areaTag : matchMakerRequest.getSelectedTags()) {
                if (mmAreaTagBuilder.length() > 0) {
                    mmAreaTagBuilder.append("~");
                }
                mmAreaTagBuilder.append(StringUtils.isNotEmpty(areaTag.getTagDescription()) ? areaTag.getTagDescription().replace(" ", SPACE_UNICODE) : "")
                        .append(PIPE_UNICODE).append(areaTag.getTagAreaId());
            }
        }
        return mmAreaTagBuilder.toString();
    }

    private String buildMmtPoiTag(MatchMakerRequest matchMakerRequest) {
        StringBuilder mmPoiTagBuilder = new StringBuilder();
        if (matchMakerRequest != null && CollectionUtils.isNotEmpty(matchMakerRequest.getLatLng())) {
            for (LatLngObject poiLatLng : matchMakerRequest.getLatLng()) {
                if (mmPoiTagBuilder.length() > 0) {
                    mmPoiTagBuilder.append("~");
                }
                mmPoiTagBuilder.append("POI")
                        .append(PIPE_UNICODE).append(org.apache.commons.lang.StringUtils.isNotEmpty(poiLatLng.getName()) ? poiLatLng.getName().replace(" ", SPACE_UNICODE) :"")
                        .append(PIPE_UNICODE).append(poiLatLng.getPoiId())
                        .append(PIPE_UNICODE).append(poiLatLng.getLatitude())
                        .append(PIPE_UNICODE).append(poiLatLng.getLongitude());
            }
        }
        return mmPoiTagBuilder.toString();
    }

    @Nullable
    private ExperiencesCardData createExperiencesCardData(
            com.gommt.hotels.orchestrator.detail.model.response.content.experience.PremiumExperiences premiumExperiences, 
            Set<String> categories, 
            HotelResult hotelResult, 
            Map<String, String> expDataMap) {
        
        boolean isPremiumExperienceEnabled = utility.isExperimentOn(expDataMap, SUPER_PREMIUM_EXPERIENCE);
        if (!isPremiumExperienceEnabled || premiumExperiences == null || 
            CollectionUtils.isEmpty(premiumExperiences.getExperienceCategoryDetails())) {
            return null;
        }
        
        // Transform orchestrator's ExperienceCategoryDetails to entity service's ExperienceCategoryDetails
        List<com.mmt.hotels.model.response.experience.ExperienceCategoryDetails> transformedExperiences = 
            transformExperienceCategoryDetails(premiumExperiences.getExperienceCategoryDetails());
        
        ExperiencesCardData cardData = new ExperiencesCardData();

        String base = hotelResult.getDetailDeeplinkUrl();
        if (StringUtils.isNotBlank(base)) {
            Map<String, String> params = new HashMap<>();
            params.put("subPage", Constants.EXPERIENCES_SUBPAGE);
            cardData.setDeeplink(ReArchUtility.appendQueryParamsInUrl(base, params));
        }

        // Build mmtHotelCategory from categories set
        String category = StringUtils.defaultIfEmpty(buildMmtHotelCategory(categories), "");
        if (category.equalsIgnoreCase(Constants.HOTEL_CATEGORY_LUXE) || category.equalsIgnoreCase(Constants.HOTEL_CATEGORY_PREMIUM)) {
            cardData.setTitle(polyglotService.getTranslatedData(EXPERIENCES_CARD_TITLE_PREMIUM));
        } else {
            cardData.setTitle(polyglotService.getTranslatedData(EXPERIENCES_CARD_TITLE));
        }
        cardData.setCtaText(polyglotService.getTranslatedData(EXPERIENCES_CARD_CTA));
        cardData.setExperienceDetails(transformedExperiences);
        
        return cardData;
    }
    
    private List<com.mmt.hotels.model.response.experience.ExperienceCategoryDetails> transformExperienceCategoryDetails(
            List<com.gommt.hotels.orchestrator.detail.model.response.content.experience.ExperienceCategoryDetails> orchExperiences) {
        
        if (CollectionUtils.isEmpty(orchExperiences)) {
            return new ArrayList<>();
        }
        
        List<com.mmt.hotels.model.response.experience.ExperienceCategoryDetails> result = new ArrayList<>(orchExperiences.size());
        
        for (com.gommt.hotels.orchestrator.detail.model.response.content.experience.ExperienceCategoryDetails orchCategory : orchExperiences) {
            if (orchCategory == null) continue;
            com.mmt.hotels.model.response.experience.ExperienceCategoryDetails category = new com.mmt.hotels.model.response.experience.ExperienceCategoryDetails();
            category.setCategoryName(orchCategory.getCategoryName());
            
            if (CollectionUtils.isNotEmpty(orchCategory.getExperiences())) {
                List<com.mmt.hotels.model.response.experience.Experience> transformedExperiences = new ArrayList<>();
                
                for (com.gommt.hotels.orchestrator.detail.model.response.content.experience.Experience orchExp : orchCategory.getExperiences()) {
                    com.mmt.hotels.model.response.experience.Experience exp = new com.mmt.hotels.model.response.experience.Experience();
                    exp.setMediaUrl(orchExp.getMediaUrl());
                    exp.setMediaType(orchExp.getMediaType());
                    exp.setTitle(orchExp.getTitle());
                    exp.setThumbnailUrl(orchExp.getThumbnailUrl());
                    exp.setGifUrl(orchExp.getGifUrl());
                    exp.setShortDescription(orchExp.getShortDescription());
                    exp.setPackageType(orchExp.getPackageType());
                    
                    // Transform ExperienceDetails if present
                    if (orchExp.getDetails() != null) {
                        com.mmt.hotels.model.response.experience.ExperienceDetails details = new com.mmt.hotels.model.response.experience.ExperienceDetails();
                        
                        // Transform Media list if present
                        if (CollectionUtils.isNotEmpty(orchExp.getDetails().getMedia())) {
                            List<com.mmt.hotels.model.response.experience.Media> transformedMedia = new ArrayList<>();
                            for (com.gommt.hotels.orchestrator.detail.model.response.content.experience.Media orchMedia : orchExp.getDetails().getMedia()) {
                                com.mmt.hotels.model.response.experience.Media media = new com.mmt.hotels.model.response.experience.Media();
                                media.setMediaUrl(orchMedia.getMediaUrl());
                                media.setMediaType(orchMedia.getMediaType());
                                media.setThumbnailUrl(orchMedia.getThumbnailUrl());
                                media.setGifUrl(orchMedia.getGifUrl());
                                media.setTitle(orchMedia.getTitle());
                                media.setDescription(orchMedia.getDescription());
                                media.setCategory(orchMedia.getCategory());
                                media.setSequenceId(orchMedia.getSequenceId());
                                media.setDurationInSeconds(orchMedia.getDurationInSeconds());
                                transformedMedia.add(media);
                            }
                            details.setMedia(transformedMedia);
                        }
                        
                        // Set detailed description
                        details.setDetailedDescription(orchExp.getDetails().getDetailedDescription());
                        
                        // Transform Metadata list if present
                        if (CollectionUtils.isNotEmpty(orchExp.getDetails().getMetadata())) {
                            List<com.mmt.hotels.model.response.experience.ExperienceMetadata> transformedMetadata = new ArrayList<>();
                            for (com.gommt.hotels.orchestrator.detail.model.response.content.experience.ExperienceMetadata orchMetadata : orchExp.getDetails().getMetadata()) {
                                com.mmt.hotels.model.response.experience.ExperienceMetadata metadata = new com.mmt.hotels.model.response.experience.ExperienceMetadata();
                                metadata.setName(orchMetadata.getName());
                                metadata.setValue(orchMetadata.getValue());
                                transformedMetadata.add(metadata);
                            }
                            details.setMetadata(transformedMetadata);
                        }
                        
                        exp.setDetails(details);
                    }
                    
                    transformedExperiences.add(exp);
                }
                
                category.setExperiences(transformedExperiences);
            }
            
            result.add(category);
        }
        
        return result;
    }
    
    /**
     * Build LOB metadata JSON - adapted from HES-Detail HotelUtil.buildLobMetaDataJson
     */
    private String buildLobMetaDataJson(HotelResult hotelResult,
                                        StaticDetailRequest staticDetailRequestBody,
                                        StaticDetailCriteria staticDetailRequest,
                                        String affiliateId, String checkin, String checkout, String roomStayCandidate,
                                        String locationId, String countryCode, boolean isGlobalEntity) {
        Map<String, Object> lobMetaDataMap = new HashMap<>();
        
        // Build metadata map
        lobMetaDataMap.put("hotelID", hotelResult.getId());
        lobMetaDataMap.put("hotelName", hotelResult.getName());
        lobMetaDataMap.put("checkin", checkin);
        lobMetaDataMap.put("checkout", checkout);
        lobMetaDataMap.put("city", locationId);
        lobMetaDataMap.put("lob", "HOTELS");
        lobMetaDataMap.put("country", countryCode);
        lobMetaDataMap.put("roomStayCandidates", roomStayCandidate);
        lobMetaDataMap.put("affiliateId", StringUtils.isNotEmpty(affiliateId) ? affiliateId : EMPTY_STRING);
        lobMetaDataMap.put("funnelSource", staticDetailRequestBody != null && staticDetailRequestBody.getRequestDetails() != null ?
                staticDetailRequestBody.getRequestDetails().getFunnelSource() : EMPTY_STRING);
        lobMetaDataMap.put("userCountry", EMPTY_STRING); // Can be enhanced based on available data
        lobMetaDataMap.put("region", Utility.isGccOrKsa() ? "AE" : "IN");
        lobMetaDataMap.put("locationType", staticDetailRequest != null ? staticDetailRequest.getLocationType() : EMPTY_STRING);
        lobMetaDataMap.put("mmPoiTag", EMPTY_STRING); // Simplified for CG implementation
        lobMetaDataMap.put("mmAreaTag", EMPTY_STRING); // Simplified for CG implementation
        lobMetaDataMap.put("currency", staticDetailRequest != null ? staticDetailRequest.getCurrency() : EMPTY_STRING);
        lobMetaDataMap.put("page", staticDetailRequestBody != null && staticDetailRequestBody.getRequestDetails() != null ?
                staticDetailRequestBody.getRequestDetails().getPageContext() : "DETAIL");
        lobMetaDataMap.put("idContext", staticDetailRequestBody != null && staticDetailRequestBody.getRequestDetails() != null ?
                staticDetailRequestBody.getRequestDetails().getIdContext() : EMPTY_STRING);
        
        try {
            // Remove null and empty values
            lobMetaDataMap.values().removeIf(value -> value == null || (value instanceof String && ((String) value).isEmpty()));
            return objectMapperUtil.getJsonFromObject(lobMetaDataMap, DependencyLayer.ORCHESTRATOR_DETAIL);
        } catch (Exception e) {
            LOGGER.error("Error while parsing lobMetaDataMap to JSON: {}", e.getMessage());
        }
        return lobMetaDataMap.toString();
    }
}
