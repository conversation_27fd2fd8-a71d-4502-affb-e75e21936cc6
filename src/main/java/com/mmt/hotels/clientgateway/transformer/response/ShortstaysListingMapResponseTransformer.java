package com.mmt.hotels.clientgateway.transformer.response;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.moblanding.LatLong;
import com.mmt.hotels.clientgateway.response.moblanding.LatLongObject;
import com.mmt.hotels.clientgateway.response.shortstays.CityGeoConfig;
import com.mmt.hotels.clientgateway.response.shortstays.CityLocationDetails;
import com.mmt.hotels.clientgateway.response.shortstays.StyleConfig;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.response.shortstays.CityMapDetails;
import com.mmt.hotels.model.response.shortstays.ShortstaysZoneResult;
import com.mmt.hotels.model.response.staticdata.BbCentre;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 *
 * This transforms ShortstaysResult received from HES and transforms it as per requirement
 *
 */
@Component
public class ShortstaysListingMapResponseTransformer {

    @Autowired
    PolyglotService polyglotService;

    @Value("${shortstays.zone.bounds}")
    private String zoneBoundConfig;

    @Value("${shortstays.direction.text.color}")
    private String directionTextColor;

    private Map<String, LatLong> zoneBoundMap;

    private final Gson gson = new Gson();

    @PostConstruct
    public void init() {

        zoneBoundMap = gson.fromJson(zoneBoundConfig, new TypeToken<Map<String, LatLong>>() {
        }.getType());

    }


    /**
     *
     * @param shortstaysZoneResult
     * @return list of cityGeoConfig
     * transforms HES response to CG response
     * updates displayName and styleConfig for associatedCities
     *
     */
    public List<CityGeoConfig> buildAssociatedCitiesGeoConfig(ShortstaysZoneResult shortstaysZoneResult) {
        List<CityGeoConfig> associatedCitiesGeoConfig = null;
        if (shortstaysZoneResult != null) {
            associatedCitiesGeoConfig = new ArrayList<>();
            for (CityMapDetails cityMapDetails : shortstaysZoneResult.getAssociatedCityDetails()) {
                CityGeoConfig cityGeoConfig = new CityGeoConfig();
                cityGeoConfig.setDisplayName(polyglotService.getTranslatedData(ConstantsTranslation.SHORTSTAYS_TOWARDS) + cityMapDetails.getName());
                cityGeoConfig.setStyleConfig(buildAssociatedCityStyleConfig(cityMapDetails.getDirection()));
                cityGeoConfig.setDirection(cityMapDetails.getDirection());
                associatedCitiesGeoConfig.add(cityGeoConfig);

            }

        }
        return associatedCitiesGeoConfig;
    }

    /**
     *
     * @param direction
     * @return style config for a particular direction
     * method created as going forward iconURL will also be updated here
     *
     */
    private StyleConfig buildAssociatedCityStyleConfig(String direction) {
        StyleConfig styleConfig = null;
        if (StringUtils.isNotEmpty(direction)) {
            styleConfig = new StyleConfig();
            styleConfig.setTextColor(directionTextColor);
        }
        return styleConfig;

    }

    /**
     *
     * @param shortstaysZoneResult
     * @return centralCityGeoConfig
     *
     * transforms HES centralCityDetails to CG response
     * update displayName for central city
     *
     */
    public CityGeoConfig buildCentralCityGeoDetails(ShortstaysZoneResult shortstaysZoneResult) {
        CityGeoConfig cityGeoConfig = null;
        if (shortstaysZoneResult != null) {
            cityGeoConfig = new CityGeoConfig();
            cityGeoConfig.setCityLocationDetails(buildCityLocationDetails(shortstaysZoneResult.getCentralCityDetails(), shortstaysZoneResult.getZoneId()));
            if (shortstaysZoneResult.getCentralCityDetails() != null) {
                cityGeoConfig.setDisplayName(shortstaysZoneResult.getCentralCityDetails().getName());
            }

        }
        return cityGeoConfig;
    }

    /**
     *
     * @param centralCityDetailsHES
     * @param zoneId
     * @return cityLocationDetails
     *
     * Transform cityLocationDetails HES to CG required response
     * picks bounds from config/properties
     *
     */
    private CityLocationDetails buildCityLocationDetails(CityMapDetails centralCityDetailsHES, String zoneId) {
        CityLocationDetails cityLocationDetails = null;
        if (centralCityDetailsHES != null) {
            cityLocationDetails = new CityLocationDetails();
            cityLocationDetails.setCentre(buildCentre(centralCityDetailsHES.getCentre()));
            cityLocationDetails.setLocusId(centralCityDetailsHES.getId());
            cityLocationDetails.setLocusType(Constants.TYPE_CITY);
            cityLocationDetails.setBounds(buildZoneBounds(zoneId));

        }
        return cityLocationDetails;
    }

    /**
     * @param zoneId
     * @return bounds for a particular zone from config
     *
     */

    private LatLong buildZoneBounds(String zoneId) {
        return StringUtils.isNotEmpty(zoneId) && zoneBoundMap.containsKey(zoneId) ? zoneBoundMap.get(zoneId) : null;
    }


    /**
     *
     * @param centre
     * @return LatLongObject
     *
     * creates CG specific LatLongObject from centre object
     *
     */
    private LatLongObject buildCentre(BbCentre centre) {
        LatLongObject latLongObject = null;
        if (centre != null) {
            latLongObject = new LatLongObject();
            if (CollectionUtils.isNotEmpty(centre.getCoordinates())) {
                latLongObject.setLng(centre.getCoordinates().get(0));
                latLongObject.setLat(centre.getCoordinates().get(1));
            }
        }
        return latLongObject;
    }

}
