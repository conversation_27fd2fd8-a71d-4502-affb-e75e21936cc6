package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.Facility;
import com.mmt.hotels.clientgateway.response.FacilityGroup;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.util.Tuple;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import static com.mmt.hotels.clientgateway.constants.Constants.POPULAR_WITH_GUESTS;

@Component
public class RoomAmentiesHelper {

    @Autowired
    PolyglotService polyglotService;

    /**
     * Build amenities from Orch RoomInfo - adapted from CommonResponseTransformer.buildAmenities
     * This method transforms Orch AmenityGroup structure to ClientGateway FacilityGroup structure
     */
    public List<FacilityGroup> buildAmenities(RoomInfo orchRoomInfo, boolean amendRoomHighlights) {
        if (orchRoomInfo == null || CollectionUtils.isEmpty(orchRoomInfo.getAmenities())) {
            return null;
        }

        List<FacilityGroup> amenitiesCGList = new ArrayList<>();
        Set<String> starAmenitySet = new HashSet<>();

        // Build star amenity set from highlighted amenities (equivalent to starFacilities in legacy)
        if (CollectionUtils.isNotEmpty(orchRoomInfo.getHighlightedAmenities())) {
            for (AmenityGroup amenityGroup : orchRoomInfo.getHighlightedAmenities()) {
                if (CollectionUtils.isNotEmpty(amenityGroup.getAmenities())) {
                    for (Amenity amenity : amenityGroup.getAmenities()) {
                        starAmenitySet.add(amenity.getName());
                    }
                }
            }
        }

        List<Facility> starFacilityCGs = new ArrayList<>();
        Tuple<Boolean, AmenityGroup> isPopularWithGuestsAvailable = null;

        if (amendRoomHighlights) {
            isPopularWithGuestsAvailable = isPopularWithGuestsAvailableOrch(orchRoomInfo.getHighlightedAmenities());
        }

        // Process main amenities from Orch RoomInfo
        for (AmenityGroup orchAmenityGroup : orchRoomInfo.getAmenities()) {
            FacilityGroup facilityGroup = new FacilityGroup();

            // Skip POPULAR_WITH_GUESTS if not available and amendRoomHighlights is true
            if (POPULAR_WITH_GUESTS.equalsIgnoreCase(orchAmenityGroup.getName()) &&
                    isPopularWithGuestsAvailable != null &&
                    isPopularWithGuestsAvailable.getX() != null &&
                    Boolean.FALSE.equals(isPopularWithGuestsAvailable.getX()) &&
                    amendRoomHighlights) {
                continue;
            }

            facilityGroup.setName(orchAmenityGroup.getName());
            List<Facility> facilityCGs = new ArrayList<>();

            // Determine which amenities to process (highlighted vs regular)
            List<com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity> amenitiesToProcess;
            if (isPopularWithGuestsAvailable != null &&
                    isPopularWithGuestsAvailable.getY() != null &&
                    amendRoomHighlights &&
                    POPULAR_WITH_GUESTS.equalsIgnoreCase(orchAmenityGroup.getName())) {
                amenitiesToProcess = isPopularWithGuestsAvailable.getY().getAmenities();
            } else {
                amenitiesToProcess = orchAmenityGroup.getAmenities();
            }

            for (com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity orchAmenity : amenitiesToProcess) {
                Facility facilityCG = new Facility();
                facilityCG.setAttributeName(orchAmenity.getAttributeName());
                facilityCG.setCategoryName(orchAmenity.getCategoryName());
                facilityCG.setDisplayType(orchAmenity.getDisplayType());
                facilityCG.setHighlightedName(orchAmenity.getHighlightedName());
                facilityCG.setName(orchAmenity.getName());
                facilityCG.setSequence(orchAmenity.getSequence());
                facilityCG.setTags(orchAmenity.getTags());
                facilityCG.setType(orchAmenity.getType());
                // Note: ClientGateway Facility doesn't have iconUrl and rating fields
                // These are available in Orch but not in legacy ClientGateway schema

                // Build child attributes if present
                if (CollectionUtils.isNotEmpty(orchAmenity.getChildAttributes())) {
                    List<com.mmt.hotels.clientgateway.response.AttributesFacility> childAttributesCG =
                            buildChildAttributesCgFromOrch(orchAmenity.getChildAttributes());
                    facilityCG.setChildAttributes(CollectionUtils.isNotEmpty(childAttributesCG) ? childAttributesCG : null);
                }

                // Check if this amenity should be in star facilities
                if (starAmenitySet.contains(orchAmenity.getName())) {
                    starAmenitySet.remove(orchAmenity.getName());
                    starFacilityCGs.add(facilityCG);
                } else if (facilityCG.getName() != null) {
                    facilityCGs.add(facilityCG);
                }
            }

            if (CollectionUtils.isNotEmpty(facilityCGs)) {
                facilityGroup.setFacilities(facilityCGs);
                amenitiesCGList.add(facilityGroup);
            }
        }

        // Process remaining star facilities from highlighted amenities
        if (CollectionUtils.isNotEmpty(orchRoomInfo.getHighlightedAmenities()) && !starAmenitySet.isEmpty()) {
            for (com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup orchAmenityGroup : orchRoomInfo.getHighlightedAmenities()) {
                for (com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity orchAmenity : orchAmenityGroup.getAmenities()) {
                    if (!starAmenitySet.contains(orchAmenity.getName())) {
                        continue;
                    }

                    Facility facilityCG = new Facility();
                    facilityCG.setAttributeName(orchAmenity.getAttributeName());
                    facilityCG.setCategoryName(orchAmenity.getCategoryName());
                    facilityCG.setDisplayType(orchAmenity.getDisplayType());
                    facilityCG.setHighlightedName(orchAmenity.getHighlightedName());
                    facilityCG.setName(orchAmenity.getName());
                    facilityCG.setSequence(orchAmenity.getSequence());
                    facilityCG.setTags(orchAmenity.getTags());
                    facilityCG.setType(orchAmenity.getType());
                    // Note: ClientGateway Facility doesn't have iconUrl and rating fields

                    if (CollectionUtils.isNotEmpty(orchAmenity.getChildAttributes())) {
                        List<com.mmt.hotels.clientgateway.response.AttributesFacility> childAttributesCG =
                                buildChildAttributesCgFromOrch(orchAmenity.getChildAttributes());
                        facilityCG.setChildAttributes(CollectionUtils.isNotEmpty(childAttributesCG) ? childAttributesCG : null);
                    }
                    starFacilityCGs.add(facilityCG);
                }
            }
        }

        // Add star facilities section if present
        if (CollectionUtils.isNotEmpty(starFacilityCGs)) {
            starFacilityCGs.sort(Comparator.comparing(
                    starFacilityCG -> starFacilityCG.getSequence() == null ? Integer.MAX_VALUE : starFacilityCG.getSequence()));

            FacilityGroup starFacilityGroup = new FacilityGroup();
            starFacilityGroup.setName(polyglotService.getTranslatedData(ConstantsTranslation.STAR_FACILITIES));
            starFacilityGroup.setType(Constants.BOLD_TYPE);
            starFacilityGroup.setFacilities(starFacilityCGs);
            amenitiesCGList.add(0, starFacilityGroup); // Add at the beginning
        }

        return amenitiesCGList;
    }

    /**
     * Build highlighted amenities from Orch RoomInfo - adapted from CommonResponseTransformer.buildHighlightedAmenities
     */
    public List<FacilityGroup> buildHighlightedAmenities(RoomInfo orchRoomInfo) {
        if (orchRoomInfo == null || CollectionUtils.isEmpty(orchRoomInfo.getHighlightedAmenities())) {
            return null;
        }

        List<FacilityGroup> highlightedAmenitiesCGList = new ArrayList<>();

        for (com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup orchAmenityGroup : orchRoomInfo.getHighlightedAmenities()) {
            FacilityGroup facilityGroup = new FacilityGroup();
            facilityGroup.setName(orchAmenityGroup.getName());

            List<Facility> facilityCGs = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity orchAmenity : orchAmenityGroup.getAmenities()) {
                Facility facilityCG = new Facility();
                facilityCG.setAttributeName(orchAmenity.getAttributeName());
                facilityCG.setCategoryName(orchAmenity.getCategoryName());
                facilityCG.setDisplayType(orchAmenity.getDisplayType());
                facilityCG.setHighlightedName(orchAmenity.getHighlightedName());
                facilityCG.setName(orchAmenity.getName());
                facilityCG.setSequence(orchAmenity.getSequence());
                facilityCG.setTags(orchAmenity.getTags());
                facilityCG.setType(orchAmenity.getType());
                // Note: ClientGateway Facility doesn't have iconUrl and rating fields

                if (CollectionUtils.isNotEmpty(orchAmenity.getChildAttributes())) {
                    List<com.mmt.hotels.clientgateway.response.AttributesFacility> childAttributesCG =
                            buildChildAttributesCgFromOrch(orchAmenity.getChildAttributes());
                    facilityCG.setChildAttributes(CollectionUtils.isNotEmpty(childAttributesCG) ? childAttributesCG : null);
                }
                facilityCGs.add(facilityCG);
            }

            if (CollectionUtils.isNotEmpty(facilityCGs)) {
                facilityGroup.setFacilities(facilityCGs);
                highlightedAmenitiesCGList.add(facilityGroup);
            }
        }

        return highlightedAmenitiesCGList;
    }

    /**
     * Check if POPULAR_WITH_GUESTS is available in highlighted amenities - Orch version
     */
    private Tuple<Boolean, com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup>
    isPopularWithGuestsAvailableOrch(List<com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup> highlightedAmenities) {
        if (CollectionUtils.isNotEmpty(highlightedAmenities)) {
            for (com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup amenityGroup : highlightedAmenities) {
                if (StringUtils.equalsIgnoreCase(amenityGroup.getName(), POPULAR_WITH_GUESTS) &&
                        CollectionUtils.isNotEmpty(amenityGroup.getAmenities())) {
                    return new Tuple<>(true, amenityGroup);
                }
            }
        }
        return new Tuple<>(false, null);
    }

    /**
     * Build child attributes from Orch AmenityAttribute to ClientGateway AttributesFacility
     */
    private List<com.mmt.hotels.clientgateway.response.AttributesFacility>
    buildChildAttributesCgFromOrch(List<com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityAttribute> childAttributesOrch) {
        if (CollectionUtils.isEmpty(childAttributesOrch)) {
            return null;
        }

        List<com.mmt.hotels.clientgateway.response.AttributesFacility> childAttributesCG = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityAttribute orchAttribute : childAttributesOrch) {
            com.mmt.hotels.clientgateway.response.AttributesFacility attributeCG =
                    new com.mmt.hotels.clientgateway.response.AttributesFacility();
            // Use BeanUtils to copy properties from Orch to ClientGateway
            // Orch AmenityAttribute has 'name' field, ClientGateway AttributesFacility will copy available fields
            BeanUtils.copyProperties(orchAttribute, attributeCG);
            childAttributesCG.add(attributeCG);
        }
        return childAttributesCG;
    }

    public List<String> buildFacilityHighlights(List<AmenityGroup> highlightedAmenities) {
        LinkedHashSet<String> highlightedFacilityNames = new LinkedHashSet<>();
        if (CollectionUtils.isNotEmpty(highlightedAmenities)) {
            for (AmenityGroup amenityGroup : highlightedAmenities) {
                highlightedFacilityNames.add(amenityGroup.getName());
            }
        }
        return CollectionUtils.isNotEmpty(highlightedFacilityNames) ? new ArrayList<>(highlightedFacilityNames) : null;
    }
}
