package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.da.Inclusion;
import com.mmt.hotels.clientgateway.response.BookedInclusion;
import com.mmt.hotels.clientgateway.response.IconType;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Helper class for handling inclusion logic at ClientGateway layer.
 * Responsible for:
 * - UI/UX concerns and client-specific formatting
 * - Text concatenation and formatting
 * - Icon URL assignment and visual elements
 * - Experiment-based reordering and A/B testing
 * - Region-specific localization
 */
@Component
public class InclusionHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(InclusionHelper.class);

    @Autowired
    private PolyglotService polyglotService;

    // Constants
    private static final String MEAL_ICON_URL = "https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/meal.png";

    // Categories for ordering
    private static final String MEAL_CATEGORY = "MEAL";
    private static final String ZPN_CATEGORY = "ZPN";
    private static final String USP_CATEGORY = "USP";
    private static final String BLACK_CATEGORY = "BLACK";

    @Value("${dot.icon.url}")
    private String dotIconUrl;

    @Value("${free.kids.inclusion.icon.url}")
    private String freeChildInclusionIcon;

    @Value("${pah.gcc.text}")
    private String pahGccText;

    /**
     * Transform OrchV2 inclusions to ClientGateway BookedInclusions
     */
    public List<BookedInclusion> transformInclusions(List<Inclusion> orchInclusions, Map<String, String> experimentDataMap, String region) {
        List<BookedInclusion> bookedInclusions = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orchInclusions)) {
            for (Inclusion inclusion : orchInclusions) {
                bookedInclusions.add(transformSingleInclusion(inclusion, experimentDataMap, region));
            }
        }
        // Note: Reordering is now handled at OrchV2 layer, not at CG layer
        return bookedInclusions;
    }

    /**
     * Transform single inclusion with formatting logic
     */
    private BookedInclusion transformSingleInclusion(Inclusion inclusion, Map<String, String> experimentDataMap, String region) {
        BookedInclusion bookedInclusion = new BookedInclusion();

        // Basic field mapping
        bookedInclusion.setCode(inclusion.getCode());
        bookedInclusion.setAmount(inclusion.getAmount());
        bookedInclusion.setCategory(inclusion.getCategory());
        bookedInclusion.setSegmentIdentifier(inclusion.getSegmentIdentifier());
        bookedInclusion.setType(inclusion.getType());
        
        // Set default values for ClientGateway-specific fields
        bookedInclusion.setInclusionCode(inclusion.getCode()); // Use code as inclusion code

        // Apply text formatting logic
        applyTextLogic(bookedInclusion, inclusion);

        // Apply icon assignment logic
        applyIconLogic(bookedInclusion, inclusion, experimentDataMap);

        // Apply region-specific logic
        applyRegionSpecificLogic(bookedInclusion, inclusion, region);

        return bookedInclusion;
    }

    /**
     * Apply text logic - concatenation, formatting, localization
     */
    private void applyTextLogic(BookedInclusion bookedInclusion, Inclusion inclusion) {
        // Set basic text fields
        bookedInclusion.setText(inclusion.getCode());
        
        // Use value field as the text, with fallback to type if value is not available
        if (StringUtils.isNotBlank(inclusion.getValue())) {
            bookedInclusion.setSubText(inclusion.getValue());
        } else if (StringUtils.isNotBlank(inclusion.getType())) {
            bookedInclusion.setSubText(inclusion.getType());
        } else {
            bookedInclusion.setSubText(inclusion.getCode());
        }

        // Rule: Concatenate text and subtext if text is short and different
        if (StringUtils.isNotBlank(bookedInclusion.getText()) && bookedInclusion.getText().length() < 10 &&
            !bookedInclusion.getText().equalsIgnoreCase(bookedInclusion.getSubText())) {
            bookedInclusion.setText(bookedInclusion.getText() + " - " + bookedInclusion.getSubText());
        }

        // Apply localization for specific inclusion types
        applyLocalizationLogic(bookedInclusion, inclusion);
    }

    /**
     * Apply localization logic
     */
    private void applyLocalizationLogic(BookedInclusion bookedInclusion, Inclusion inclusion) {
        // Localize specific inclusion codes
        if ("PAH_WITHOUT_CC_TEXT".equals(inclusion.getCode())) {
            bookedInclusion.setText(polyglotService.getTranslatedData("PAH_WITHOUT_CC_TEXT"));
        } else if ("PAH_WITH_CC_TEXT".equals(inclusion.getCode())) {
            bookedInclusion.setText(polyglotService.getTranslatedData("PAH_WITH_CC_TEXT"));
        } else if ("PAH_GCC_TEXT".equals(inclusion.getCode())) {
            bookedInclusion.setText(polyglotService.getTranslatedData("PAH_GCC_TEXT"));
        } else if ("FCZPN_TEXT".equals(inclusion.getCode())) {
            bookedInclusion.setText(polyglotService.getTranslatedData("FCZPN_TEXT"));
        } else if ("LOS_BENEFIT_TEXT".equals(inclusion.getCode())) {
            bookedInclusion.setText(polyglotService.getTranslatedData("LOS_BENEFIT_TEXT"));
        }
    }

    /**
     * Apply icon logic based on category, type, and experiments
     */
    private void applyIconLogic(BookedInclusion bookedInclusion, Inclusion inclusion, Map<String, String> experimentDataMap) {
        // Default icon settings
        bookedInclusion.setIconType(IconType.DEFAULT);
        bookedInclusion.setIconUrl(dotIconUrl);

        // Apply category-specific icon logic
        if ("MEAL_PLAN".equals(inclusion.getType()) || MEAL_CATEGORY.equals(inclusion.getCategory())) {
            bookedInclusion.setIconUrl(MEAL_ICON_URL);
        }

        // Apply package-specific icon logic
        if (USP_CATEGORY.equals(inclusion.getCategory()) && StringUtils.isNotEmpty(inclusion.getIconUrl())) {
            bookedInclusion.setIconUrl(inclusion.getIconUrl());
        }

        // Apply black segment icon logic for experiments
        if (BLACK_CATEGORY.equalsIgnoreCase(inclusion.getSegmentIdentifier()) && isBlackRevampExperiment(experimentDataMap)) {
            bookedInclusion.setIconUrl(inclusion.getIconUrl());
        }

        // Apply free child specific icon
        if ("FREE_CHILD".equals(inclusion.getType())) {
            bookedInclusion.setIconUrl(freeChildInclusionIcon);
        }
    }

    /**
     * Apply region-specific logic (GCC/KSA customizations)
     */
    private void applyRegionSpecificLogic(BookedInclusion bookedInclusion, Inclusion inclusion, String region) {
        // Apply GCC/KSA specific customizations
        if (isGccOrKsaRegion(region)) {
            // Use specific PAH text for GCC region
            if ("PAH_WITHOUT_CC_TEXT".equals(inclusion.getCode()) || "PAH_WITH_CC_TEXT".equals(inclusion.getCode())) {
                bookedInclusion.setText(pahGccText);
            }
            // Additional GCC/KSA specific logic can be added here
        }
    }

    /**
     * Check if black revamp experiment is active (needed for icon logic)
     */
    private boolean isBlackRevampExperiment(Map<String, String> experimentDataMap) {
        return experimentDataMap != null && "true".equals(experimentDataMap.get("blackRevamp"));
    }

    /**
     * Check if region is GCC or KSA
     */
    private boolean isGccOrKsaRegion(String region) {
        return "GCC".equalsIgnoreCase(region) || "KSA".equalsIgnoreCase(region);
    }
} 