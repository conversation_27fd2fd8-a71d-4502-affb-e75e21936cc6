package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.util.Utility;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.request.ValidateCouponRequest;
import com.mmt.hotels.model.request.ValidateCouponRequestBody;

@Component
public class DiscountRequestTransformer {

	@Autowired
	private Utility utility;

	public ValidateCouponRequestBody convertValidateCouponRequest(ValidateCouponRequest validateCouponRequest) {
		ValidateCouponRequestBody req = new ValidateCouponRequestBody();
		req.setTxnKey(validateCouponRequest.getTxnKey());
		req.setCouponCode(validateCouponRequest.getCouponCode());
		req.setRemoveCoupon(validateCouponRequest.isRemoveCoupon());
		req.setCorrelationKey(validateCouponRequest.getCorrelationKey());
		req.setQuoteId(validateCouponRequest.getQuoteId());
		req.setUserLocation(validateCouponRequest.getUserLocation());
		req.setRequestIdentifier(utility.buildRequestIdentifier(validateCouponRequest.getRequestDetails()));
		req.setQuickCheckoutApplicable(validateCouponRequest.isQuickCheckoutApplicable());
		req.setCountryCode(validateCouponRequest.getCountryCode());
		return req;
	}

}
