package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.controller.ListingController;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.request.ListingSearchRequestV2;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.request.TreelsSearchCriteria;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.enums.SectionsType;
import com.mmt.hotels.model.request.ResponseFilterFlags;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.request.matchmaker.CityTags;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.model.request.payment.TravelerDetail;
import com.mmt.scrambler.ScramblerClient;
import com.mmt.scrambler.exception.ScramblerClientException;
import com.mmt.scrambler.utils.HashType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.BEDROOM_COUNT;
import static com.mmt.hotels.clientgateway.constants.Constants.EXACT_ROOM_RECOMMENDATION;
import static com.mmt.hotels.clientgateway.constants.Constants.LOCATION;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.LISTING_SEARCH_HOTELS;

@Component
public class TreelsRequestTransformer extends BaseSearchRequestTransformer{

    private static final Logger LOGGER = LoggerFactory.getLogger(TreelsRequestTransformer.class);

    @Autowired
    private CommonHelper commonHelper;


    @Autowired
    private MetricAspect metricAspect;
    public SearchWrapperInputRequest convertSearchRequest(
            ListingSearchRequestV2 listingSearchRequest, CommonModifierResponse commonModifierResponse) {
        SearchWrapperInputRequest searchWrapperInputRequest = new SearchWrapperInputRequest();
        long start = System.currentTimeMillis();
        try {
            searchWrapperInputRequest.setCorrelationKey(listingSearchRequest.getCorrelationKey());
            buildDeviceDetails(searchWrapperInputRequest, listingSearchRequest.getDeviceDetails());
            buildSearchCriteria(searchWrapperInputRequest, listingSearchRequest.getSearchCriteria(),listingSearchRequest.getFilterCriteria(),listingSearchRequest.getRequestDetails(), listingSearchRequest.getExpDataMap());
            buildRequestDetails(searchWrapperInputRequest, listingSearchRequest.getRequestDetails(), commonModifierResponse,listingSearchRequest.getDeviceDetails());
            searchWrapperInputRequest.setExperimentData(listingSearchRequest.getExpData());
            searchWrapperInputRequest.setValidExpList(listingSearchRequest.getValidExpList());
            searchWrapperInputRequest.setVariantKeys(listingSearchRequest.getVariantKeys());
            searchWrapperInputRequest.setRequestIdentifier(utility.buildRequestIdentifier(listingSearchRequest.getRequestDetails()));
            Set<String> dptInlineAppliedCategories = new HashSet<>();
            searchWrapperInputRequest.setExcludeFilterMap(buildExcludeFilterMap(listingSearchRequest));
            if(MapUtils.isEmpty(searchWrapperInputRequest.getExcludeFilterMap())) {
                searchWrapperInputRequest.setAppliedFilterMap(buildAppliedFilterMap(listingSearchRequest, dptInlineAppliedCategories));
            }
//            searchWrapperInputRequest.setAppliedBatchKeys(buildAppliedBatchKeys(listingSearchRequest.getAppliedBatchKeys()));
//            updateFiltersToRemove(listingSearchRequest, searchWrapperInputRequest);
//            updateFilterGroupsToRemove(listingSearchRequest, searchWrapperInputRequest);
            searchWrapperInputRequest.setAdvancedFiltering(true);

            if (listingSearchRequest.getFeatureFlags() == null)
                listingSearchRequest.setFeatureFlags(new FeatureFlags());
            searchWrapperInputRequest.setResponseFilterFlags(buildResponseFilterFlags(searchWrapperInputRequest, listingSearchRequest, commonModifierResponse));
            searchWrapperInputRequest.setMatchMakerRequest(listingSearchRequest.getMatchMakerDetails());
            if(listingSearchRequest.getRequestDetails()!=null) {
                searchWrapperInputRequest.setChannel(StringUtils.isEmpty(listingSearchRequest.getRequestDetails().getChannel()) ? "B2C":listingSearchRequest.getRequestDetails().getChannel());
                searchWrapperInputRequest.setPageContext(listingSearchRequest.getRequestDetails().getPageContext());
                searchWrapperInputRequest.setSiteDomain(listingSearchRequest.getRequestDetails().getSiteDomain());
            }
            searchWrapperInputRequest.setMobile(commonModifierResponse.getMobile());
            searchWrapperInputRequest.setAffiliateId(commonModifierResponse.getAffiliateId());
            searchWrapperInputRequest.setApplicationId(String.valueOf(commonModifierResponse.getApplicationId()));
            if(listingSearchRequest!=null && listingSearchRequest.getRequestDetails()!=null) {
                searchWrapperInputRequest.setPageContext(listingSearchRequest.getRequestDetails().getPageContext());
            }
            searchWrapperInputRequest.setMcid(commonModifierResponse.getMcId());
            searchWrapperInputRequest.setMmtAuth(commonModifierResponse.getMmtAuth());
            searchWrapperInputRequest.setAuthToken(commonModifierResponse.getMmtAuth());
            searchWrapperInputRequest.setUserId(commonModifierResponse.getMmtAuth());
            searchWrapperInputRequest.setDomain(commonModifierResponse.getDomain());
            searchWrapperInputRequest.setUserLocation(commonModifierResponse.getUserLocation());
            if (commonModifierResponse.getExtendedUser() != null) {
                searchWrapperInputRequest.setUUID(commonModifierResponse.getExtendedUser().getUuid());
                searchWrapperInputRequest.setProfileType(commonModifierResponse.getExtendedUser().getProfileType());
                searchWrapperInputRequest.setSubProfileType(commonModifierResponse.getExtendedUser().getAffiliateId());
                searchWrapperInputRequest.setCorpUserID(commonModifierResponse.getExtendedUser().getProfileId());
            }
            if (commonModifierResponse.getHydraResponse() != null) {
                if (CollectionUtils.isNotEmpty(commonModifierResponse.getHydraResponse().getHydraMatchedSegment())) {
                    String[] segmentList = new String[commonModifierResponse.getHydraResponse().getHydraMatchedSegment()
                            .size()];
                    int i = 0;
                    for (String segment : commonModifierResponse.getHydraResponse().getHydraMatchedSegment()) {
                        segmentList[i] = segment;
                        i++;
                    }
                    searchWrapperInputRequest.setUserSegments(segmentList);
                }
                searchWrapperInputRequest.setFlightBooker(commonModifierResponse.getHydraResponse().isFlightBooker());
            }
            searchWrapperInputRequest.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());
            searchWrapperInputRequest.setLastProductId(listingSearchRequest.getLastProductId());
            if(listingSearchRequest.getLimit()!=null) {
                searchWrapperInputRequest.setLimit(Integer.valueOf(listingSearchRequest.getLimit()));
            }
        } finally {
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_SEARCH_REQUEST_PROCESS, LISTING_SEARCH_HOTELS, System.currentTimeMillis() - start);
        }
        /* NearByHotels Request */

        return searchWrapperInputRequest;
    }

    public void buildSearchCriteria(SearchWrapperInputRequest searchWrapperInputRequest,
                                    TreelsSearchCriteria searchCriteria, List<com.mmt.hotels.clientgateway.request.Filter> filterCriteria, RequestDetails requestDetails, Map<String, String> expData) {
        if(requestDetails != null && CollectionUtils.isNotEmpty(filterCriteria) && Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(requestDetails.getFunnelSource())){
            Optional<com.mmt.hotels.clientgateway.request.Filter> bedRoomCountfilter = filterCriteria.stream().filter(f -> com.mmt.hotels.clientgateway.response.filter.FilterGroup.BEDROOM_COUNT.name().equalsIgnoreCase(f.getFilterGroup().name())).findFirst();
            if(bedRoomCountfilter.isPresent()){
                utility.modifyRoomStayCandidateRequestForHomestayFunnel(bedRoomCountfilter.get(),searchCriteria);
            }
        }
        if(searchCriteria==null) {
            return;
        }
        searchWrapperInputRequest.setHotelIdList(searchCriteria.getHotelIds());
        searchWrapperInputRequest.setCheckin(searchCriteria.getCheckIn());
        searchWrapperInputRequest.setTreelId(searchCriteria.getTreelId());
        searchWrapperInputRequest.setCheckout(searchCriteria.getCheckOut());
        searchWrapperInputRequest.setCountryCode(searchCriteria.getCountryCode());
        searchWrapperInputRequest.setCityCode(searchCriteria.getCityCode());
        searchWrapperInputRequest.setLocationId(searchCriteria.getLocationId());
        searchWrapperInputRequest.setLocationType(searchCriteria.getLocationType());
        searchWrapperInputRequest.setCurrency(searchCriteria.getCurrency());
        searchWrapperInputRequest.setLimit(searchCriteria.getLimit());
        searchWrapperInputRequest.setLastFetchedHotelId(searchCriteria.getLastHotelId());
        searchWrapperInputRequest.setLastFetchedWindowInfo(searchCriteria.getLastFetchedWindowInfo());
        searchWrapperInputRequest.setLastFetchedHotelCategory(searchCriteria.getLastHotelCategory());
        searchWrapperInputRequest.setParentLocationId(searchCriteria.getParentLocationId());
        searchWrapperInputRequest.setParentLocationType(searchCriteria.getParentLocationType());
        if(searchCriteria.getLat() != null && searchCriteria.getLat() != 0.0d)
            searchWrapperInputRequest.setLatitude(searchCriteria.getLat());
        if(searchCriteria.getLng() != null && searchCriteria.getLng() != 0.0d)
            searchWrapperInputRequest.setLongitude(searchCriteria.getLng());
        searchWrapperInputRequest.setRoomStayCandidates(buildRoomStayCandidates(searchCriteria.getRoomStayCandidates()));
        // we will put condition here to execute the logic for the new apps
        if (searchCriteria != null && utility.isDistributeRoomStayCandidates(searchCriteria.getRoomStayCandidates(), expData)) {
            searchWrapperInputRequest.setAppendRscInDeepLink(true);
            searchWrapperInputRequest.setRscValueForDeepLink(utility.buildRscValue(searchCriteria.getRoomStayCandidates()));
            searchWrapperInputRequest.setRoomStayCandidates(utility.buildRoomStayDistribution(searchCriteria.getRoomStayCandidates(), expData));
        }
        searchWrapperInputRequest.setTripType(searchCriteria.getTripType());
        if (StringUtils.isNotBlank(searchCriteria.getSectionsType())) {
            searchWrapperInputRequest.setSectionsType(SectionsType.valueOf(searchCriteria.getSectionsType()));
        }
        searchWrapperInputRequest.setPersonalizedSearch(searchCriteria.isPersonalizedSearch());
        searchWrapperInputRequest.setBoostProperty(searchCriteria.getBoostProperty());
    }

    public Map<FilterGroup, Set<Filter>> buildAppliedFilterMap(ListingSearchRequestV2 searchRequestGateway, Set<String> dptInlineAppliedCategories) {
        Map<FilterGroup, Set<Filter>> appliedFilterMapCB = new LinkedHashMap<>();
        if (searchRequestGateway != null && CollectionUtils.isNotEmpty(searchRequestGateway.getFilterCriteria())) {
            updateAppliedFilterMap(appliedFilterMapCB, searchRequestGateway.getFilterCriteria(), searchRequestGateway.getMatchMakerDetails(), dptInlineAppliedCategories);
        }
        if(searchRequestGateway.getMatchMakerDetails()!=null && CollectionUtils.isNotEmpty(searchRequestGateway.getMatchMakerDetails().getCityTags())) {
            addTreelsLocationFilters(appliedFilterMapCB, searchRequestGateway.getMatchMakerDetails().getCityTags());
        }
        return appliedFilterMapCB;
    }

    private void addTreelsLocationFilters(Map<FilterGroup, Set<Filter>> appliedFilterMapCB, List<CityTags> treelsLocationFiltersList) {
        Set<Filter> locationFiltersList = new HashSet<>();
        for(CityTags treelsLocationFilter : treelsLocationFiltersList) {
            Filter filter = new Filter();
            filter.setFilterGroup(FilterGroup.getFilterGroupFromFilterName(treelsLocationFilter.getFilterGroup()));
            filter.setFilterValue(treelsLocationFilter.getFilterValue());
            filter.setMatchmakerType(treelsLocationFilter.getMatchmakerType());
            filter.setTitle(treelsLocationFilter.getTitle());
            locationFiltersList.add(filter);
        }
        appliedFilterMapCB.put(FilterGroup.getFilterGroupFromFilterName(LOCATION), locationFiltersList);
    }

    public Map<FilterGroup, Set<Filter>> buildExcludeFilterMap(ListingSearchRequestV2 searchRequestGateway) {

        if (searchRequestGateway != null && CollectionUtils.isNotEmpty(searchRequestGateway.getFilterRemovedCriteria())) {
            Map<FilterGroup, Set<Filter>> removeFilterMapCB = new LinkedHashMap<>();
            commonHelper.buildFiltersToRemove(removeFilterMapCB, searchRequestGateway.getFilterRemovedCriteria());
            return removeFilterMapCB;
        }
        return null;
    }

    private void updateAppliedFilterMap(Map<FilterGroup, Set<Filter>> appliedFilterMap,
                                        List<com.mmt.hotels.clientgateway.request.Filter> filterCriteria, MatchMakerRequest matchMakerDetails, Set<String> dptInlineAppliedCategories) {
        if (CollectionUtils.isEmpty(filterCriteria)) {
            return;
        }
        if (appliedFilterMap == null) {
            appliedFilterMap = new HashMap<>();
        }

        com.mmt.hotels.clientgateway.request.Filter lastFilterAppliedCG = filterCriteria.get(filterCriteria.size()-1);
        lastFilterAppliedCG.setLastApplied(true);
        for (com.mmt.hotels.clientgateway.request.Filter filterCG : filterCriteria) {
            if(filterCG.getFilterGroup() != null) {
                if (EXACT_ROOM_RECOMMENDATION.equalsIgnoreCase(filterCG.getFilterGroup().name()) || (BEDROOM_COUNT.equalsIgnoreCase(filterCG.getFilterGroup().name()))) {
                    continue;
                }
            }
            com.mmt.hotels.filter.FilterGroup filterGroup = com.mmt.hotels.filter.FilterGroup.getFilterGroupFromFilterName(
                    filterCG.getFilterGroup().name());
            if (filterGroup == null) {
                LOGGER.error("No filter group found in HES for CG filter group::{}", filterCG.getFilterGroup().name());
                continue;
            }
            if(filterCG.getFilterGroup().name().equalsIgnoreCase(Constants.VILLA_AND_APPT)) {
                filterGroup = com.mmt.hotels.filter.FilterGroup
                        .getFilterGroupFromFilterName(Constants.ALT_ACCO_PROPERTY);
                filterCG.setFilterValue(Constants.AltAcco);
            }
            com.mmt.hotels.filter.Filter filterCB = new com.mmt.hotels.filter.Filter();
            filterCB.setFilterGroup(filterGroup);
//            filterCB.setFilterRange(buildFilterRange(filterCG.getFilterRange()));
            filterCB.setFilterValue(filterCG.getFilterValue());
            filterCB.setTitle(filterCG.getTitle());
            filterCB.setRangeFilter(filterCG.isRangeFilter());
            filterCB.setLastApplied(filterCG.isLastApplied());
            appliedFilterMap.putIfAbsent(filterGroup, new HashSet<>());
            appliedFilterMap.get(filterGroup).add(filterCB);
        }
        filterHelper.updateAppliedFilterMapDptCollections(appliedFilterMap, matchMakerDetails, dptInlineAppliedCategories);

    }

//    public void updateFiltersToRemove(ListingSearchRequestV2 searchHotelsRequestGateway, SearchWrapperInputRequest searchWrapperInputRequest) {
//        if (CollectionUtils.isNotEmpty(searchHotelsRequestGateway.getFiltersToRemove())){
//            List<Filter> filtersToRemove = new ArrayList<>();
//            for (com.mmt.hotels.clientgateway.request.Filter filter : searchHotelsRequestGateway.getFiltersToRemove()){
//                Filter filtertoremove = new Filter();
//                filtertoremove.setFilterValue(filter.getFilterValue());
//                filtertoremove.setFilterGroup(FilterGroup.getFilterGroupFromFilterName(filter.getFilterGroup().name()));
//                filtertoremove.setRangeFilter(filter.isRangeFilter());
//                filtertoremove.setFilterRange(buildFilterRange(filter.getFilterRange()));
//                filtersToRemove.add(filtertoremove);
//            }
//            searchWrapperInputRequest.setFiltersToRemove(filtersToRemove);
//        }
//    }

//    public void updateFilterGroupsToRemove(ListingSearchRequestV2 searchHotelsRequestGateway, SearchWrapperInputRequest searchWrapperInputRequest) {
//        if(CollectionUtils.isNotEmpty(searchHotelsRequestGateway.getFilterGroupsToRemove())){
//            List<FilterGroup> filterGroupsToRemove = new ArrayList<>();
//            searchHotelsRequestGateway.getFilterGroupsToRemove()
//                    .forEach(fg -> filterGroupsToRemove.add(FilterGroup.getFilterGroupFromFilterName(fg.name())));
//            searchWrapperInputRequest.setFilterGroupsToRemove(filterGroupsToRemove);
//        }
//    }

    public ResponseFilterFlags buildResponseFilterFlags(SearchWrapperInputRequest searchWrapperInputRequest, ListingSearchRequestV2 searchHotelsRequestGateway, CommonModifierResponse commonModifierResponse) {
        FeatureFlags featureFlags = searchHotelsRequestGateway.getFeatureFlags();
        ResponseFilterFlags responseFilterFlags = new ResponseFilterFlags();
        responseFilterFlags.setStaticData(featureFlags.isStaticData());
        responseFilterFlags.setFlyfishSummaryRequired(featureFlags.isReviewSummaryRequired());
        responseFilterFlags.setWalletRequired(featureFlags.isWalletRequired());
        responseFilterFlags.setShortlistRequired(featureFlags.isShortlistingRequired());
        responseFilterFlags.setCheckAvailibility(featureFlags.isCheckAvailability());
        responseFilterFlags.setBestCoupon(featureFlags.isCoupon());
        responseFilterFlags.setApplyAbsorption(featureFlags.isApplyAbsorption());
        //responseFilterFlags.setDealOfTheDayRequired(featureFlags.isDealOfTheDayRequired());
        //Removing flag dependency fix for SWAT-4039659
        responseFilterFlags.setDealOfTheDayRequired(true);
        responseFilterFlags.setPoisRequiredOnMap(featureFlags.isPoisRequiredOnMap());
        responseFilterFlags.setExtraAltAccoPropertiesRequired(featureFlags.isExtraAltAccoRequired());
        responseFilterFlags.setPersuasionsEngineHit(featureFlags.isPersuasionsEngineHit());
        Optional.ofNullable(featureFlags.getMaskedPropertyName()).ifPresent(responseFilterFlags::setMaskedPropertyName);
        responseFilterFlags.setSelectiveHotels(featureFlags.isSelectiveHotels());
        searchWrapperInputRequest.setNumberOfAddons(featureFlags.getNoOfAddons());
        searchWrapperInputRequest.setNumberOfCoupons(featureFlags.getNoOfCoupons());
        searchWrapperInputRequest.setNoOfPersuasions(featureFlags.getNoOfPersuasions());
        searchWrapperInputRequest.setNumberOfSoldOuts(featureFlags.getNoOfSoldouts());
        responseFilterFlags.setCityTaxExclusive(commonModifierResponse.isCityTaxExclusive());
        responseFilterFlags.setSimilarHotels(featureFlags.isSimilarHotel());
        responseFilterFlags.setComparator(featureFlags.isComparator());
        responseFilterFlags.setOriginListingMap(featureFlags.isOriginListingMap());
        responseFilterFlags.setMostBooked(featureFlags.isMostBooked());
        responseFilterFlags.setDetailMap(featureFlags.isDetailMap());
        responseFilterFlags.setPersuasionRequired(featureFlags.isPersuasionsRequired());
        responseFilterFlags.setTopCard(featureFlags.isTopCard());
        responseFilterFlags.setCardRequired(featureFlags.isCardRequired());
        responseFilterFlags.setFilters(featureFlags.isFilters());
        return responseFilterFlags;
    }


}
