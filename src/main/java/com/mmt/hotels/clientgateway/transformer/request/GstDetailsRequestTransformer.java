package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.request.gstDetails.SaveGstDetailsRequest;
import com.mmt.hotels.clientgateway.request.gstDetails.TravellerGstDetails;
import org.springframework.stereotype.Component;

@Component
public class GstDetailsRequestTransformer extends BaseRoomRequestTransformer {

    public TravellerGstDetails convertToTravellerGstDetails(SaveGstDetailsRequest saveGstDetailsRequest, CommonModifierResponse commonModifierResponse) {
        return TravellerGstDetails.builder()
                .uuid(commonModifierResponse.getExtendedUser().getUuid())
                .gstOwner(saveGstDetailsRequest.getGstOwner())
                .gstNumber(saveGstDetailsRequest.getGstNumber())
                .billingAddress(saveGstDetailsRequest.getBillingAddress())
                .companyName(saveGstDetailsRequest.getCompanyName())
                .state(saveGstDetailsRequest.getState())
                .pinCode(saveGstDetailsRequest.getPinCode())
                .isDefault(saveGstDetailsRequest.getIsDefault())
                .build();
    }
}
