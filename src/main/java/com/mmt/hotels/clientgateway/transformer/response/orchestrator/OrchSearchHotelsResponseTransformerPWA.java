package com.mmt.hotels.clientgateway.transformer.response.orchestrator;


import com.gommt.hotels.orchestrator.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.model.response.da.CancellationTimeline;
import com.gommt.hotels.orchestrator.model.response.listing.HotelDetails;
import com.gommt.hotels.orchestrator.model.response.listing.PersonalizedSectionDetails;
import com.gommt.hotels.orchestrator.model.response.listing.PriceDetail;
import com.gommt.hotels.orchestrator.model.response.listing.QuickBookInfo;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.response.searchHotels.BottomSheet;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.HotelCard;
import com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.hotels.pojo.listing.personalization.BGLinearGradient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.DAYUSE_LOCAL_ID;
import static com.mmt.hotels.clientgateway.constants.Constants.FUNNEL_DAYUSE;
import static com.mmt.hotels.clientgateway.constants.Constants.IMAGE_TEXT_H;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.BOOK_NOW;

@Component
public class OrchSearchHotelsResponseTransformerPWA extends OrchSearchHotelsResponseTransformer {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrchSearchHotelsResponseTransformerPWA.class);
    
    private MyBizStaticCard myBizStaticCard;
    
    @PostConstruct
    public void init() {
        super.init();
        myBizStaticCard = commonConfigConsul.getMyBizStaticCard();
    }

    @Override
    protected BottomSheet buildBottomSheet(PersonalizedSectionDetails perResponse) {
        return null;
    }

    @Override
    public void addPersuasionHoverData(Hotel hotel, HotelDetails hotelEntity, CancellationTimeline cancellationTimeline, PriceDetail displayFare, ListingSearchRequest listingSearchRequest) {

    }

    @Override
    public void addLocationPersuasionToHotelPersuasions(Hotel hotel, List<String> locationPersuasion, LinkedHashSet<String> facilities, ListingSearchRequest searchHotelsRequest, boolean enableAmenities, boolean sectionMyBizSimilarToDirectHtl, String dayUsePersuasionsText, TransportPoi nearestGroundTransportPoi, String drivingTimeText, LocationDetails locusData, boolean homestayV2Flow) {
        if(CollectionUtils.isNotEmpty(locationPersuasion)) {
            if (hotel.getHotelPersuasions() == null)
                hotel.setHotelPersuasions(new HashMap<String,Object>());
            PersuasionObject locPers = new PersuasionObject();
            locPers.setData(new ArrayList<>());
            locPers.setTemplate(IMAGE_TEXT_H);
            locPers.setPlaceholder("SINGLE");

            int index = 1;
            PersuasionData locPersuasionData = new PersuasionData();
            locPersuasionData.setHasAction(false);
            locPersuasionData.setHtml(true);
            locPersuasionData.setId("LOC_PERSUASION_" + index++);
            locPersuasionData.setPersuasionType("LOCATION");
            PersuasionStyle style = new PersuasionStyle();
            //[HTL-46707] Changed the max lines to 3 for IH hotels only
            if (hotel.getLocationDetail() != null && !Constants.DOM_COUNTRY.equalsIgnoreCase(hotel.getLocationDetail().getCountryId())) {
                style.setMaxLines(3);
            } else {
                style.setMaxLines(2);
            }
            locPersuasionData.setStyle(style);

            locPers.getData().add(locPersuasionData);
            if(locationPersuasion.size() == 1 ) {
                locPersuasionData.setText(locationPersuasion.get(0));
            }else if(StringUtils.isEmpty(locationPersuasion.get(0))){
                locPersuasionData.setText(locationPersuasion.get(1));
            }
            else if (locationPersuasion.size() >= 2){
                locPersuasionData.setText(locationPersuasion.get(0) + " | " + locationPersuasion.get(1));
                //For Secondary Location Persuasion, if it is present, add it in the Location Persuasion
                if (locationPersuasion.size() > 2)
                    locPersuasionData.setText(locPersuasionData.getText() + " | " + locationPersuasion.get(2));
            }

            try {
                ((Map<Object,Object>) hotel.getHotelPersuasions()).put(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP,locPers);
            } catch (ClassCastException e) {
                LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
            }

        }

        if(CollectionUtils.isNotEmpty(facilities) && enableAmenities) {
            if (hotel.getHotelPersuasions() == null)
                hotel.setHotelPersuasions(new HashMap<String,Object>());
            PersuasionObject amenityPers = new PersuasionObject();
            amenityPers.setData(new ArrayList<>());
            amenityPers.setTemplate(IMAGE_TEXT_H);
            amenityPers.setPlaceholder("SINGLE");


            PersuasionData amenPersuasionData = new PersuasionData();
            amenPersuasionData.setHasAction(false);
            amenPersuasionData.setHtml(false);
            amenPersuasionData.setId("AMENITIES");
            amenPersuasionData.setPersuasionType("AMENITIES");
            amenPersuasionData.setStyle(new PersuasionStyle());
            amenPersuasionData.getStyle().setTextColor("#000000");
            StringBuilder text = new StringBuilder();
            int index = 1;
            int facilitiesSize = facilities.size();
            Iterator<String> iter = facilities.iterator();
            while(iter.hasNext() && index <=3){

                text.append( iter.next());
                if(index < 3 && facilitiesSize > 1)
                    text.append(" | ");
                index++;
                --facilitiesSize;
            }
            amenPersuasionData.setText(text.toString());
            amenityPers.getData().add(amenPersuasionData);


            try {
                ((Map<Object,Object>) hotel.getHotelPersuasions()).put(Constants.AMENITIES_PLACEHOLDER_ID_APP,amenityPers);
            } catch (ClassCastException e) {
                LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
            }
        }

        if(searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails()!=null && FUNNEL_DAYUSE.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource()) && org.apache.commons.lang3.StringUtils.isNotEmpty(dayUsePersuasionsText)) {
            if (hotel.getHotelPersuasions() == null)
                hotel.setHotelPersuasions(new HashMap<String,Object>());
            PersuasionObject amenityPers = new PersuasionObject();
            amenityPers.setData(new ArrayList<>());
            amenityPers.setTemplate(IMAGE_TEXT_H);
            amenityPers.setPlaceholder("SINGLE");


            PersuasionData amenPersuasionData = new PersuasionData();
            amenPersuasionData.setHasAction(false);
            amenPersuasionData.setHtml(false);
            amenPersuasionData.setId(DAYUSE_LOCAL_ID);
            amenPersuasionData.setPersuasionType(DAYUSE_LOCAL_ID);
            amenPersuasionData.setStyle(new PersuasionStyle());
            amenPersuasionData.getStyle().setTextColor("#000000");
            amenPersuasionData.setText(dayUsePersuasionsText);
            amenPersuasionData.setIcontype("b_dot");
            amenityPers.getData().add(amenPersuasionData);
            try {
                ((Map<Object,Object>) hotel.getHotelPersuasions()).put(Constants.AMENITIES_PLACEHOLDER_ID_APP,amenityPers);
            } catch (ClassCastException e) {
                LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
            }
        }

    }

    @Override
    protected String buildBGColor(String section, String orientation, String cardType) {
        return null;
    }

    @Override
    protected void addBookingConfirmationPersuasion(HotelDetails hotelEntity) {

    }

    @Override
    public MyBizStaticCard buildStaticCard(String section, List<HotelDetails> hotels, String detailDeepLinkUrl) {
        MyBizStaticCard staticCard = null;
        if(Constants.CORPBUDGET_DIRECT_HOTEL.equalsIgnoreCase(section) && CollectionUtils.isNotEmpty(hotels) && !hotels.get(0).isBudgetHotel() &&
                myBizStaticCard != null) {
            staticCard = SerializationUtils.clone(myBizStaticCard);
            staticCard.setActionUrl(detailDeepLinkUrl);
            translateStaticCard(staticCard);
        }
        return staticCard;
    }
    
    protected void translateStaticCard(MyBizStaticCard staticCard) {
        staticCard.setText(polyglotService.getTranslatedData(staticCard.getText()));
        staticCard.setSubtext(polyglotService.getTranslatedData(staticCard.getSubtext()));
        staticCard.setCtaText(polyglotService.getTranslatedData(staticCard.getCtaText()));
    }

    @Override
    public HotelCard buildQuickBookCard(QuickBookInfo quickBookInfo) {
        if(quickBookInfo==null) {
            return null;
        }
        HotelCard hotelBottomCard = new HotelCard();
        hotelBottomCard.setHeading(quickBookInfo.getTitleWithPrice());
        hotelBottomCard.setSubHeading(quickBookInfo.getRoomPersuasion());
        hotelBottomCard.setRoomSubHeading(quickBookInfo.getRoomPersuasionWithSize());
        hotelBottomCard.setShowCard(quickBookInfo.isShowQuickBookCard());
        hotelBottomCard.setCta(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW));
        BGLinearGradient bgLinearGradient = new BGLinearGradient();
        bgLinearGradient.setDirection("leftBottomToTopRight");
        bgLinearGradient.setStart("#2D6F95");
        bgLinearGradient.setEnd("#192B43");
        hotelBottomCard.setBgLinearGradient(bgLinearGradient);
        return hotelBottomCard;
    }
}
