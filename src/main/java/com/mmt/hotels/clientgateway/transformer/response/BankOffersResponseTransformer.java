package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.response.BankOfferCG;
import com.mmt.hotels.clientgateway.response.BankOffersResponseCG;
import com.mmt.hotels.pojo.response.bankoffers.BankOffer;
import com.mmt.hotels.pojo.response.bankoffers.BankOffersResponse;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
public abstract class BankOffersResponseTransformer {

    public BankOffersResponseCG convertBankOffersResponse(BankOffersResponse response) {
        BankOffersResponseCG bankOffersResponseCG = new BankOffersResponseCG();
        if (response != null && CollectionUtils.isNotEmpty(response.getBankOffersData())) {
            bankOffersResponseCG.setBankOffersData(response.getBankOffersData().stream().map(this::convertBankOffersResponse).collect(Collectors.toList()));
        }
        if (response != null && response.getResponseErrors() != null && CollectionUtils.isNotEmpty(response.getResponseErrors().getErrorList())) {
            bankOffersResponseCG.setError(response.getResponseErrors().getErrorList().get(0));
        }
        return bankOffersResponseCG;
    }

    private BankOfferCG convertBankOffersResponse(BankOffer response) {
        BankOfferCG bankOfferCG = new BankOfferCG();
        bankOfferCG.setIconUrl(response.getIconUrl());
        bankOfferCG.setPromoCode(response.getPromoCode());
        bankOfferCG.setText(response.getText());
        bankOfferCG.setSubText(response.getSubText());
        return bankOfferCG;
    }

}
