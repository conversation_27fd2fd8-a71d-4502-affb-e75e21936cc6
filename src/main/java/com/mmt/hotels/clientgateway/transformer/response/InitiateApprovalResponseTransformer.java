package com.mmt.hotels.clientgateway.transformer.response;

import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.response.ConsentData;
import com.mmt.hotels.clientgateway.response.InitApprovalResponse;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.corporate.DuplicateBookingDetails;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;

import static com.mmt.hotels.clientgateway.constants.Constants.GBF;
import static com.mmt.hotels.clientgateway.constants.Constants.OSBA;
import static com.mmt.hotels.clientgateway.constants.Constants.SUCCESS;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.POLICY_MISMATCH;

@Component
public class InitiateApprovalResponseTransformer {

    @Autowired
    PolyglotService polyglotService;
    @Autowired
    Utility utility;

    public InitApprovalResponse processResponse(CGServerResponse initAppResponseHES){

        InitApprovalResponse initApprovalResponse = new InitApprovalResponse();
        if(initAppResponseHES.getResponseErrors() != null && CollectionUtils.isNotEmpty(initAppResponseHES.getResponseErrors().getErrorList())){
            Error error  = new Error(initAppResponseHES.getResponseErrors().getErrorList().get(0).getErrorCode(), initAppResponseHES.getResponseErrors().getErrorList().get(0).getErrorMessage());
            initApprovalResponse.setError(error);
        }else if (initAppResponseHES.getAdditionalProperties().get("error") != null){
            Error error  = new Error((String) initAppResponseHES.getAdditionalProperties().get("status").toString(), (String) initAppResponseHES.getAdditionalProperties().get("message"));
            initApprovalResponse.setError(error);
        }else {
            initApprovalResponse = new Gson().fromJson(new Gson().toJson(initAppResponseHES.getAdditionalProperties()), InitApprovalResponse.class);
            populateConsentData(initApprovalResponse, initAppResponseHES);
            if(initApprovalResponse != null && !SUCCESS.equalsIgnoreCase(initApprovalResponse.getStatus()) &&
                    StringUtils.isNotEmpty(initApprovalResponse.getResponseCode()) && StringUtils.isNotEmpty(initApprovalResponse.getMessage())) {
                Error error;
                if (initApprovalResponse.getResponseCode().equalsIgnoreCase(OSBA) || initApprovalResponse.getResponseCode().equalsIgnoreCase(GBF)) {
                    error = new Error(initApprovalResponse.getResponseCode(), initApprovalResponse.getMessage(), null, polyglotService.getTranslatedData(POLICY_MISMATCH));
                }
                else{
                    error = new Error(initApprovalResponse.getResponseCode(), initApprovalResponse.getMessage());
                }
                initApprovalResponse.setError(error);
            }
        }

        return  initApprovalResponse;
    }

    public void populateConsentData(InitApprovalResponse initApprovalResponse, CGServerResponse initAppResponseHES) {
        if(MapUtils.isNotEmpty(initAppResponseHES.getAdditionalProperties()) && initAppResponseHES.getAdditionalProperties().containsKey("duplicateBookingDetails")){
            DuplicateBookingDetails duplicateBookingDetails = new Gson().fromJson(new Gson().toJson(initAppResponseHES.getAdditionalProperties().get("duplicateBookingDetails"), LinkedHashMap.class), DuplicateBookingDetails.class);
            ConsentData consentData = new ConsentData();
            consentData.setDataList(utility.populateDateList(duplicateBookingDetails));
            utility.populateCommonFields(consentData);
            populateClientSpecificFields(consentData);
            initApprovalResponse.setConsentData(consentData);
            initApprovalResponse.setStatus(SUCCESS);
        }
    }

    public void populateClientSpecificFields(ConsentData consentData) {
        //To be implemented by client specific classes
    }
}
