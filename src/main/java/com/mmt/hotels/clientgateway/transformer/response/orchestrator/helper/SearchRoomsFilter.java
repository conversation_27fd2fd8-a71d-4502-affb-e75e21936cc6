package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.response.RatePlanFilter;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomRatePlan;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.mmt.hotels.clientgateway.constants.Constants.BOOK_NOW_AT_0;
import static com.mmt.hotels.clientgateway.constants.Constants.BOOK_NOW_AT_1;
import static com.mmt.hotels.clientgateway.constants.Constants.CONTRACTED_FARE;
import static com.mmt.hotels.clientgateway.constants.Constants.EXP_SPKG;
import static com.mmt.hotels.clientgateway.constants.Constants.HOTEL_CLOUD;
import static com.mmt.hotels.clientgateway.constants.Constants.HOTEL_CLOUD_RATE_SEGMENT;
import static com.mmt.hotels.clientgateway.constants.Constants.HOTEL_CLOUD_TITLE;
import static com.mmt.hotels.clientgateway.constants.Constants.ONE_ON_ONE_RATE_SEGMENT;
import static com.mmt.hotels.clientgateway.constants.Constants.PACKAGE_RATE;
import static com.mmt.hotels.clientgateway.constants.Constants.PAGE_CONTEXT_DETAIL;
import static com.mmt.hotels.clientgateway.constants.Constants.RTB_EMAIL;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FILTER_LUXE_PACKAGE_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FILTER_NON_LUXE_PACKAGE_TEXT;

/**
 * Helper class for SearchRooms filter functionality.
 * Migrated from legacy SearchRoomsResponseTransformer to work with OrchV2 flow.
 */
@Component
public class SearchRoomsFilter {

    @Autowired
    private CommonConfigConsul commonConfigConsul;

    @Autowired
    private PolyglotService polyglotService;

    @Value("#{'${mypat_exclusive_rate_segmentId.list}'.split(',')}")
    private Set<String> mypatExclusiveRateSegmentIdList;

    @Value("${corp.one.on.one.segmentId}")
    private String corpPreferredRateSegmentId;

    // Configuration fields loaded from CommonConfigConsul
    private boolean mealplanFilterEnable;
    private boolean partnerExclusiveFilterEnable;
    private Map<String, String> mealPlanMapPolyglot;

    @PostConstruct
    public void init() {
        // Load configuration from CommonConfigConsul - using default values for now
        this.mealplanFilterEnable = true;
        this.partnerExclusiveFilterEnable = true;
        this.mealPlanMapPolyglot = new HashMap<>();
    }

    /**
     * Get filter codes for a specific rate plan.
     * Migrated from legacy SearchRoomsResponseTransformer.getFilterCodes() method.
     * Updated to work with OrchV2 model (com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan).
     */
    public List<String> getFilterCodes(com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan ratePlan, boolean isBlockPAH, int ap,
                                       CommonModifierResponse commonModifierResponse, boolean isLuxeHotel, String roomName) {
        List<String> filterCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ratePlan.getMealPlans()) && !(Constants.MEAL_PLAN_CODE_ROOM_ONLY.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
                || Constants.MEAL_PLAN_CODE_BED_ONLY.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode()) || Constants.MEAL_PLAN_CODE_ACC_ONLY.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode()))) {
            filterCodes.add(Constants.FREE_BREAKFAST);
        }
        if (mealplanFilterEnable && CollectionUtils.isNotEmpty(ratePlan.getMealPlans())
                && (Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH_OR_DINNER.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
                || Constants.MEAL_PLAN_CODE_BREAKFAST_DINNER.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
                || Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode()))) {
            filterCodes.add(Constants.TWO_MEAL_AVAIL);
        }
        if (mealplanFilterEnable && CollectionUtils.isNotEmpty(ratePlan.getMealPlans())
                && (Constants.MEAL_PLAN_CODE_ALL_MEALS_AI.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
                || Constants.MEAL_PLAN_CODE_ALL_MEALS.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode()))) {
            filterCodes.add(Constants.ALL_MEAL_AVAIL);
        }

        boolean freeCancellation =  ratePlan.getCancellationPolicy() != null && CollectionUtils.isNotEmpty(ratePlan.getCancellationPolicy().getPenalties()) && CancelPenalty.CancellationType.FREE_CANCELLATON.name().equalsIgnoreCase(ratePlan.getCancellationPolicy().getPenalties().get(0).getType());
        if (freeCancellation) {
            filterCodes.add(Constants.FREE_CANCELLATION);
        }
        if (freeCancellation && ratePlan.getBnplDetails() != null && ratePlan.getBnplDetails().isEligibleForHoldBooking() && ratePlan.getBnplDetails().getExpiry() != null && ratePlan.getBnplDetails().getBookingAmount() == 0f) {
            filterCodes.add(BOOK_NOW_AT_0);
        }

        if (freeCancellation && ratePlan.getBnplDetails() != null && ratePlan.getBnplDetails().isEligibleForHoldBooking() && ratePlan.getBnplDetails().getExpiry() != null && ratePlan.getBnplDetails().getBookingAmount() == 1f) {
            filterCodes.add(BOOK_NOW_AT_1);
        }
        if (Utility.isMyBizRequest() && StringUtils.isNotEmpty(ratePlan.getSegmentId()) && ratePlan.getSegmentId().equals(ONE_ON_ONE_RATE_SEGMENT)) {
            filterCodes.add(CONTRACTED_FARE);
        }
        if (ratePlan.getRatePlanFlags() != null && ratePlan.getRatePlanFlags().isHotelCloud()) {
            filterCodes.add(HOTEL_CLOUD);
        }
        if (!(Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId()))) {
            if (null != ratePlan.getPaymentMode()
                    && !ratePlan.getPaymentMode().getMappedPayMode().equalsIgnoreCase(com.mmt.hotels.model.response.pricing.PaymentMode.PAS.getMappedPayMode())) {
                filterCodes.add(isBlockPAH && ap < 5 ? "FCZPN" : "PAH");
            }
            if (ratePlan.getBnplDetails() != null && ratePlan.getBnplDetails().isBnplApplicable()) {
                filterCodes.add("FCZPN");
            }
            if (CollectionUtils.isNotEmpty(ratePlan.getInclusions()) && ratePlan.getInclusions().stream().anyMatch(a -> ("Packages".equalsIgnoreCase(a.getCategory()) || "Packages1".equalsIgnoreCase(a.getCategory())
                    || "Packages2".equalsIgnoreCase(a.getCategory()) || "Packages3".equalsIgnoreCase(a.getCategory())
                    || "MMTBLACK".equalsIgnoreCase(a.getCategory())))) {
                filterCodes.add("SPECIALDEALS");
            }

            if (ratePlan.getRatePlanFlags().isStaycationDeal()) {
                filterCodes.add("STAYCATION");
            }
        }

        if (ratePlan.getRatePlanFlags().isPackageRatePlan()) {
            filterCodes.add(PACKAGE_RATE);
        }

        if (!RTB_EMAIL.equalsIgnoreCase(ratePlan.getRtbEmail())) {
            filterCodes.add(Constants.INSTANT_BOOKING);
        }

        if (partnerExclusiveFilterEnable && mypatExclusiveRateSegmentIdList.contains(ratePlan.getSegmentId()))
            filterCodes.add("CTA_RATES_AVAIL");

        if (StringUtils.isNotEmpty(roomName) && (roomName.contains("Suite") || roomName.contains("suite"))) {
            filterCodes.add("SUITE");
        }
        return filterCodes;
    }


    public List<RatePlanFilter> getFilters(
            List<RoomDetails> exactRooms, List<RoomDetails> occupancyRooms,
            List<Filter> filterCriteria, String funnelSource, int ap, boolean isBlockPAH,
            String bnplVariant, CommonModifierResponse commonModifierResponse, boolean isLuxeHotel,
            boolean negotiatedRateFlag, boolean applyFilterToCombo, boolean hideSpecificFilters,
            String pageContext) {

        // Convert String bnplVariant to BNPLVariant enum
        BNPLVariant bnplVariantEnum = null;
        if (StringUtils.isNotEmpty(bnplVariant)) {
            try {
                bnplVariantEnum = BNPLVariant.valueOf(bnplVariant);
            } catch (IllegalArgumentException e) {
                // Default to BNPL_NOT_APPLICABLE if conversion fails
                bnplVariantEnum = BNPLVariant.BNPL_NOT_APPLICABLE;
            }
        } else {
            bnplVariantEnum = BNPLVariant.BNPL_NOT_APPLICABLE;
        }

        return getFilters(exactRooms, occupancyRooms, filterCriteria, funnelSource,
                ap, isBlockPAH, bnplVariantEnum, commonModifierResponse,
                isLuxeHotel, negotiatedRateFlag, applyFilterToCombo,
                hideSpecificFilters, pageContext);
    }

    /**
     * Build complete filter list for search rooms response.
     * Migrated from legacy SearchRoomsResponseTransformer.getFilters() method.
     */
    private List<RatePlanFilter> getFilters(List<RoomDetails> exactRooms, List<RoomDetails> occupancyRooms,
                                           List<Filter> filterCriteria, String funnelSource, int ap, boolean isblockPAH,
                                           BNPLVariant bnplVariant, CommonModifierResponse commonModifierResponse, boolean isLuxeHotel, boolean negotiatedRateFlag, boolean applyFilterToCombo, boolean hideSpecificFilters, String pageContext) {
        if (CollectionUtils.isEmpty(exactRooms) && CollectionUtils.isEmpty(occupancyRooms)) {
            return new ArrayList<>();
        }
        List<RoomDetails> rooms = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(exactRooms)) {
            rooms.addAll(exactRooms);
        }
        if (CollectionUtils.isNotEmpty(occupancyRooms)) {
            rooms.addAll(occupancyRooms);
        }

        List<RatePlanFilter> ratePlanFilters = new ArrayList<>();
        boolean pahFilter = false, freeCancellationFilter = false, freeBreakfastFilter = false, fczpnFilter = false, specialDeals = false, staycation = false;
        boolean twomealsFilter = false, allmealsFilter = false, partnerExclusiveFilter = false, packageRoomFilter = false, nonLuxePackageRoomFilter = false;
        boolean contractedFareFilter = false;
        boolean hotelCloudFilter = false;
        boolean mpBookNowFilterFor0 = false;
        boolean mpBookNowFilterFor1 = false;
        boolean instantBookingFilter = false;
        boolean myPartner = Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
        String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
        for (RoomDetails roomDetails : rooms) {
            for (SelectRoomRatePlan ratePlan : roomDetails.getRatePlans()) {
                if (CollectionUtils.isNotEmpty(ratePlan.getFilterCode())) {
                    if (ratePlan.getFilterCode().contains("FREE_CANCELLATION") && !hideSpecificFilters) {
                        freeCancellationFilter = true;
                    }
                    if (ratePlan.getFilterCode().contains(BOOK_NOW_AT_0) && !hideSpecificFilters) {
                        mpBookNowFilterFor0 = true;
                    }
                    if (ratePlan.getFilterCode().contains(BOOK_NOW_AT_1) && !hideSpecificFilters) {
                        mpBookNowFilterFor1 = true;
                    }
                    if (ratePlan.getFilterCode().contains("FREE_BREAKFAST"))
                        freeBreakfastFilter = true;
                    if (!myPartner) {
                        if (ratePlan.getFilterCode().contains("PAH")) {
                            if (isblockPAH && ap < 5) {
                                fczpnFilter = true;
                            } else {
                                pahFilter = true;
                            }
                        }
                        if (ratePlan.getFilterCode().contains("FCZPN") && !hideSpecificFilters)
                            fczpnFilter = true;
                        if (ratePlan.getFilterCode().contains("SPECIALDEALS"))
                            specialDeals = true;
                        if (ratePlan.getFilterCode().contains("STAYCATION"))
                            staycation = true;
                    }
                    if (ratePlan.getFilterCode().contains("TWO_MEAL_AVAIL"))
                        twomealsFilter = true;
                    if (ratePlan.getFilterCode().contains("ALL_MEAL_AVAIL"))
                        allmealsFilter = true;
                    if (ratePlan.getFilterCode().contains(PACKAGE_RATE))
                        packageRoomFilter = true;
                    if (ratePlan.getFilterCode().contains("CTA_RATES_AVAIL"))
                        partnerExclusiveFilter = true;
                    if (negotiatedRateFlag && ratePlan.getFilterCode().contains(Constants.INSTANT_BOOKING))
                        instantBookingFilter = true;
                    if (Utility.isMyBizRequest()
                            && PAGE_CONTEXT_DETAIL.equalsIgnoreCase(pageContext)
                            && ratePlan.getFilterCode().contains(CONTRACTED_FARE))
                        contractedFareFilter = true;

                    if (Utility.isMyBizRequest()
                            && PAGE_CONTEXT_DETAIL.equalsIgnoreCase(pageContext)
                            && ratePlan.getFilterCode().contains(HOTEL_CLOUD))
                        hotelCloudFilter = true;
                }
            }
        }
        if (partnerExclusiveFilter) {
            boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "CTA_RATES_AVAIL".equalsIgnoreCase(a.getFilterValue()));
            RatePlanFilter ratePlanFilter = new RatePlanFilter("Partner Exclusive Rate", "CTA_RATES_AVAIL", "", selected);
            ratePlanFilters.add(ratePlanFilter);
        }

        if (freeCancellationFilter) {
            boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "CANCELLATION_AVAIL".equalsIgnoreCase(a.getFilterValue()));
            RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(ConstantsTranslation.FREE_CANCELLATION_FILTER), "FREE_CANCELLATION", "", selected);
            ratePlanFilters.add(ratePlanFilter);
        }
        if (mpBookNowFilterFor0) {
            boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "CANCELLATION_AVAIL".equalsIgnoreCase(a.getFilterValue()));
            RatePlanFilter mpBookNowRatePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(BOOK_NOW_AT_0), BOOK_NOW_AT_0, "", selected);
            ratePlanFilters.add(mpBookNowRatePlanFilter);
        }
        if (mpBookNowFilterFor1) {
            boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "CANCELLATION_AVAIL".equalsIgnoreCase(a.getFilterValue()));
            RatePlanFilter mpBookNowRatePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(BOOK_NOW_AT_1), BOOK_NOW_AT_1, "", selected);
            ratePlanFilters.add(mpBookNowRatePlanFilter);
        }
        if (freeBreakfastFilter) {
            boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "BREAKFAST_AVAIL".equalsIgnoreCase(a.getFilterValue()) || "BREAKFAST".equalsIgnoreCase(a.getFilterValue()));
            RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(ConstantsTranslation.BREAKFAST_INCLUDED_FILTER), "FREE_BREAKFAST", "", selected);
            ratePlanFilters.add(ratePlanFilter);
        }
        if (twomealsFilter) {
            boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "TWO_MEAL_AVAIL".equalsIgnoreCase(a.getFilterValue()));
            RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData((myPartner ? "BUSINESS_TWO_MEAL_AVAIL_TITLE_MYPARTNER" : "BREAKFAST_LUNCH_DINNER_INCLUDED")), "TWO_MEAL_AVAIL", "", selected);
            ratePlanFilters.add(ratePlanFilter);
        }
        if (allmealsFilter) {
            boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "ALL_MEAL_AVAIL".equalsIgnoreCase(a.getFilterValue()));
            RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData((myPartner ? "BUSINESS_ALL_MEAL_AVAIL_TITLE_MYPARTNER" : "ALL_MEALS_INCLUDED")), "ALL_MEAL_AVAIL", "", selected);
            ratePlanFilters.add(ratePlanFilter);
        }
        if (!myPartner) {
            if (pahFilter) {
                boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "PAH".equalsIgnoreCase(a.getFilterValue()) || "PAH_AVAIL".equalsIgnoreCase(a.getFilterValue()));
                RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(ConstantsTranslation.PAY_AT_HOTEL_FILTER), "PAH", "", selected);
                ratePlanFilters.add(ratePlanFilter);
            }
            if (fczpnFilter) {
                boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "PAY_LATER".equalsIgnoreCase(a.getFilterValue()));
                RatePlanFilter ratePlanFilter;
                if (isblockPAH) {
                    ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_FILTER), "FCZPN", "", selected);
                } else {
                    //GCC -> Free Cancellation Zero Payment Now
                    if (Utility.isRegionGccOrKsa(region)) {
                        ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("FCZPN_FILTER"), "FCZPN", "", selected);
                    }
                    //BNPL_AT_1 -> Free Cancellation - Book @ ₹ 1
                    else if (BNPLVariant.BNPL_AT_1.equals(bnplVariant)) {
                        ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(ConstantsTranslation.FCZPN_FILTER_BNPL_NEW_VARIANT), "FCZPN", "", selected);
                    }
                    //BNPL_AT_0 -> Book with 0 Payment
                    else if (BNPLVariant.BNPL_AT_0.equals(bnplVariant)) {
                        ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(ConstantsTranslation.FCZPN_FILTER_BNPL_ZERO_VARIANT), "FCZPN", "", selected);
                    } else {
                        ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(ConstantsTranslation.FCZPN_FILTER), "FCZPN", "", selected);
                    }
                }
                ratePlanFilters.add(ratePlanFilter);
            }
            if (specialDeals) {
                RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_DEALS_FILTER), "SPECIALDEALS", "", false);
                ratePlanFilters.add(ratePlanFilter);
            }

            if (staycation && "GETAWAY".equalsIgnoreCase(funnelSource)) {
                boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "STAYCATION_DEALS".equalsIgnoreCase(a.getFilterValue()));
                RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(ConstantsTranslation.GETAWAY_DEALS_FILTER), "STAYCATION", "", selected);
                ratePlanFilters.add(ratePlanFilter);
            }
        }
//Remove MMT PACKAGE and MMT LUXE PACKAGE node value and set to PACKAGE_RATE, confirm with the client LUXE_PACKAGE and NON_LUXE_PACKAGE not required
        if (packageRoomFilter) {
            boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(
                    a -> Constants.PACKAGE_RATE.equalsIgnoreCase(a.getFilterValue()));
            RatePlanFilter ratePlanFilter = null;
            if (isLuxeHotel) {
                ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(FILTER_LUXE_PACKAGE_TEXT),
                        Constants.PACKAGE_RATE, "", selected);
            } else {
                ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(FILTER_NON_LUXE_PACKAGE_TEXT),
                        Constants.PACKAGE_RATE, "", selected);
            }
            if (setSuperPackagePersuasion(commonModifierResponse)) {
                ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(Constants.SUPER_PACKAGES_TEXT),
                        Constants.PACKAGE_RATE, "", selected);
            }
            ratePlanFilter.setOrder(1);
            ratePlanFilters.add(ratePlanFilter);
        }

        // Add INSTANT BOOKING rate plan filter negotiated rate hotel flow.
        if (instantBookingFilter) {
            boolean instantBookingSelected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> Constants.INSTANT_BOOKING.equalsIgnoreCase(a.getFilterValue()));
            RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(ConstantsTranslation.INSTANT_BOOKING), Constants.INSTANT_BOOKING, "", instantBookingSelected);
            ratePlanFilters.add(ratePlanFilter);
        }
        if (contractedFareFilter) {
            boolean selected = false;
            RatePlanFilter ratePlanFilter = new RatePlanFilter("Contracted Fare", CONTRACTED_FARE, "", selected);
            ratePlanFilters.add(ratePlanFilter);
        }
        if (hotelCloudFilter) {
            boolean selected = false;
            RatePlanFilter ratePlanFilter = new RatePlanFilter(HOTEL_CLOUD_TITLE, HOTEL_CLOUD, "", selected);
            ratePlanFilters.add(ratePlanFilter);
        }

        if (CollectionUtils.isEmpty(ratePlanFilters))
            return new ArrayList<>();

        for (RatePlanFilter filter : ratePlanFilters) {
            if (!applyFilterToCombo && CollectionUtils.isNotEmpty(occupancyRooms)) {
                filter.setSelected(false);
            }
        }
        if (filterCriteria != null && filterCriteria.stream().anyMatch(a -> "SUITE_ROOM".equalsIgnoreCase(a.getFilterValue()))) {
            RatePlanFilter ratePlanFilter = new RatePlanFilter("Suite Room", "SUITE", "", true);
            ratePlanFilters.add(ratePlanFilter);
        }
        ratePlanFilters.sort(Comparator.comparingInt(RatePlanFilter::getOrder));
        return ratePlanFilters;
    }

    /**
     * Helper method to check if super package persuasion should be set.
     * Migrated from legacy SearchRoomsResponseTransformer.
     */
    private boolean setSuperPackagePersuasion(CommonModifierResponse commonModifierResponse) {
        return commonModifierResponse!=null && commonModifierResponse.getExpDataMap()!=null && commonModifierResponse.getExpDataMap().containsKey(EXP_SPKG) && Constants.EXP_TRUE_VALUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(EXP_SPKG));
    }
}