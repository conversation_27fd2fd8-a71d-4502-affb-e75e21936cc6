package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.request.UserGlobalInfo;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.moblanding.SupportDetails;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.RoomStayCandidate;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.mmt.hotels.clientgateway.constants.Constants.AE_SUBDOMAIN;
import static com.mmt.hotels.clientgateway.constants.Constants.CHECK_IN_TIME;
import static com.mmt.hotels.clientgateway.constants.Constants.CITY_NAME;
import static com.mmt.hotels.clientgateway.constants.Constants.COUNTRY_NAME;
import static com.mmt.hotels.clientgateway.constants.Constants.DDMMYYYY;
import static com.mmt.hotels.clientgateway.constants.Constants.DEFAULT_CUR_INR;
import static com.mmt.hotels.clientgateway.constants.Constants.EMPTY_STRING;
import static com.mmt.hotels.clientgateway.constants.Constants.ENTIRE_PROPERTY;
import static com.mmt.hotels.clientgateway.constants.Constants.FUNNEL_NAME;
import static com.mmt.hotels.clientgateway.constants.Constants.GLOBAL_ENTITY;
import static com.mmt.hotels.clientgateway.constants.Constants.HOTEL;
import static com.mmt.hotels.clientgateway.constants.Constants.HOTEL_NAME;
import static com.mmt.hotels.clientgateway.constants.Constants.MPN;
import static com.mmt.hotels.clientgateway.constants.Constants.REGION_AE;
import static com.mmt.hotels.clientgateway.constants.Constants.REGION_SA;
import static com.mmt.hotels.clientgateway.constants.Constants.REGION_URL_PARAM;
import static com.mmt.hotels.clientgateway.constants.Constants.RSC;
import static com.mmt.hotels.clientgateway.constants.Constants.SA_SUBDOMAIN;
import static com.mmt.hotels.clientgateway.constants.Constants.TYPE_CITY;
import static com.mmt.hotels.clientgateway.constants.Constants.WWW_SUBDOMAIN;
import static com.mmt.hotels.clientgateway.util.DateUtil.MMDDYYYY;

@Component
public class DeepLinkHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(DeepLinkHelper.class);

    @Autowired
    private DateUtil dateUtil;

    @Autowired
    private Utility utility;

    @Value("${detail.deep.link.url}")
    protected String basicDetailDeeplink;

    @Value("${detail.deep.link.url.global}")
    protected String basicDetailDeeplinkGlobal;

    @Value("${search.rooms.deep.link.url}")
    protected String searchRoomsDeeplink;

    @Value("${search.rooms.deep.link.url.global}")
    protected String searchRoomsDeeplinkGlobal;

    public String buildDetailDeepLinkUrl(HotelDetails hotelDetails, SearchRoomsRequest searchRoomsRequest) {
        return buildDetailDeepLinkUrl(hotelDetails, searchRoomsRequest, getBasicDeepLink(searchRoomsRequest, basicDetailDeeplink, basicDetailDeeplinkGlobal), false);
    }

    public String buildSearchRoomsDeepLinkUrl(HotelDetails hotelDetails, SearchRoomsRequest searchRoomsRequest) {
        return buildDetailDeepLinkUrl(hotelDetails, searchRoomsRequest, getBasicDeepLink(searchRoomsRequest, searchRoomsDeeplink, searchRoomsDeeplinkGlobal), true);
    }

    private String getBasicDeepLink(SearchRoomsRequest searchRoomsRequest, String basicDeeplink, String globalDeepLink) {
        UserGlobalInfo userGlobalInfo = searchRoomsRequest.getSearchCriteria().getUserGlobalInfo();
        if (userGlobalInfo != null && GLOBAL_ENTITY.equalsIgnoreCase(userGlobalInfo.getEntityName())) {
            if (REGION_SA.equalsIgnoreCase(searchRoomsRequest.getRequestDetails().getSiteDomain())) {
                return globalDeepLink.replace(WWW_SUBDOMAIN, SA_SUBDOMAIN);
            } else if (REGION_AE.equalsIgnoreCase(searchRoomsRequest.getRequestDetails().getSiteDomain())) {
                return globalDeepLink.replace(WWW_SUBDOMAIN, AE_SUBDOMAIN);
            }
        }
        return basicDeeplink;
    }

    public String buildDetailDeepLinkUrl(HotelDetails hotelDetails, SearchRoomsRequest searchRoomsRequest, String deepLink, boolean deepLinkWithPropertyDetails) {

        String rscValue;
        List<RoomStayCandidate> distributedRoomStayCandidateList;
        if (utility.isDistributeRoomStayCandidates(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates(), searchRoomsRequest.getExpDataMap())) {
            rscValue = utility.buildRscValue(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates());
            distributedRoomStayCandidateList = utility.buildRoomStayDistribution(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates(), searchRoomsRequest.getExpDataMap());
        } else {
            distributedRoomStayCandidateList = Collections.emptyList();
            rscValue = EMPTY_STRING;
        }

        String currency = Optional.ofNullable(searchRoomsRequest.getSearchCriteria()).map(SearchCriteria::getCurrency).orElse(EMPTY_STRING);

        // Handle check-in and check-out dates; use empty strings if unavailable
        String checkIn = searchRoomsRequest.getSearchCriteria().getCheckIn() != null
                ? dateUtil.getDateFormatted(searchRoomsRequest.getSearchCriteria().getCheckIn(), DateUtil.YYYY_MM_DD, MMDDYYYY) : "";
        String checkOut = searchRoomsRequest.getSearchCriteria().getCheckOut() != null
                ? dateUtil.getDateFormatted(searchRoomsRequest.getSearchCriteria().getCheckOut(), DateUtil.YYYY_MM_DD, MMDDYYYY) : "";
        String city = Optional.ofNullable(searchRoomsRequest.getSearchCriteria()).map(SearchCriteria::getLocationId).orElse(EMPTY_STRING);
        String country = Optional.ofNullable(searchRoomsRequest.getSearchCriteria()).map(SearchCriteria::getCountryCode).orElse(EMPTY_STRING);
        String locusId = Optional.ofNullable(searchRoomsRequest.getSearchCriteria()).map(SearchCriteria::getLocationId).orElse(Optional.ofNullable(hotelDetails.getLocation()).map(LocationDetails::getId).orElse(EMPTY_STRING));
        String locusType = Optional.ofNullable(searchRoomsRequest.getSearchCriteria()).map(SearchCriteria::getLocationType).orElse(Optional.ofNullable(hotelDetails.getLocation()).map(LocationDetails::getType).orElse(EMPTY_STRING));
        String roomStayParam = "";
        if (CollectionUtils.isNotEmpty(distributedRoomStayCandidateList)) {
            roomStayParam = buildRoomStayCandidateFromSearchWrapperS(distributedRoomStayCandidateList);
        } else {
            roomStayParam = buildRoomStayCandidateFromSearchWrapper(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates());
        }

        /*
        Sample URLS
            https://www.makemytrip.com/hotels/hotel-details?
             hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&openDetail=true&currency={5}

        https://www.makemytrip.com/hotels/hotel-details?
        * hotelId=202001031606575931&checkin=01162025&checkout=01172025&country=IN&city=RGNCR
        * &openDetail=true&currency=INR&roomStayQualifier=4e1e7e3e1e12e&locusId=RGNCR&
        * locusType=region&filterData=STAR_RATING%7C4&region=in&viewType=BUDGET&
        * funnelName=HOTELS&rsc=2e7e2e12e7e
        *    */

        String format = MessageFormat.format(deepLink, hotelDetails.getId(),
                checkIn, checkOut, country, city, currency, roomStayParam, locusId, locusType);

        //Append Filters
        format = appendFiltersDataToDeepLink(searchRoomsRequest.getFilterCriteria(), format);

        format += deepLinkWithPropertyDetails ? getDeeplinkWithEntireProperty(hotelDetails) : "";

        // Append region from MDC if available, fallback to default region
        String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
        if (StringUtils.isNotEmpty(region)) {
            format += Constants.AND_SEPARATOR + REGION_URL_PARAM + Constants.PR_SEPARATOR + region.toLowerCase();
        } else {
            format += Constants.AND_SEPARATOR + REGION_URL_PARAM + Constants.PR_SEPARATOR + Constants.WALLET_REGION_IND;
        }

        if (StringUtils.isNotEmpty(hotelDetails.getHotelCategory())) {
            format += getQueryParameter(Constants.VIEW_TYPE, hotelDetails.getHotelCategory());
        }

        // Append funnel source if available
        if (searchRoomsRequest.getRequestDetails() != null && StringUtils.isNotBlank(searchRoomsRequest.getRequestDetails().getFunnelSource())) {
            format += getQueryParameter(FUNNEL_NAME, searchRoomsRequest.getRequestDetails().getFunnelSource());
        }

        // Append RSC value if available
        if (StringUtils.isNotEmpty(rscValue)) {
            format += getQueryParameter(RSC, rscValue);
        }

        //AddCheckinTime
        if (searchRoomsRequest.getSearchCriteria() != null && searchRoomsRequest.getSearchCriteria().getSlot() != null && searchRoomsRequest.getSearchCriteria().getSlot().getTimeSlot() != null) {
            format += getQueryParameter(CHECK_IN_TIME, String.valueOf(searchRoomsRequest.getSearchCriteria().getSlot().getTimeSlot()));
        }

        format += getQueryParameter(MPN, String.valueOf(hotelDetails.getHotelRateFlags().isMaskedPropertyName()));

        return format;
    }

    private String getDeeplinkWithEntireProperty(HotelDetails hotelDetails) {
        String partialDeepLink = EMPTY_STRING;
        if (org.apache.commons.lang.StringUtils.isNotBlank(hotelDetails.getName())) {
            partialDeepLink += getQueryParameter(HOTEL_NAME, getEncodedUrl(hotelDetails.getName()));
        }
        if (hotelDetails.getLocation() != null && org.apache.commons.lang.StringUtils.isNotBlank(hotelDetails.getLocation().getCountryName())) {
            partialDeepLink += getQueryParameter(COUNTRY_NAME, hotelDetails.getLocation().getCountryName());
        }
        if (hotelDetails.getLocation() != null && org.apache.commons.lang.StringUtils.isNotBlank(hotelDetails.getLocation().getCityName())) {
            partialDeepLink += getQueryParameter(CITY_NAME, hotelDetails.getLocation().getCityName());
        }
        if (org.apache.commons.lang.StringUtils.isNotBlank(hotelDetails.getPropertyType())) {
            partialDeepLink += getQueryParameter(ENTIRE_PROPERTY, String.valueOf(org.apache.commons.lang3.StringUtils.startsWith(hotelDetails.getPropertyType(), "Entire")));
        }
        return partialDeepLink;
    }

    public String appendFiltersDataToDeepLink(List<Filter> filterCriteria, String deepLink) {
        if (CollectionUtils.isEmpty(filterCriteria)) {
            return deepLink; // Return the original deepLink if no filterCriteria is present
        }

        StringBuilder filters = new StringBuilder();
        for (Filter filter : filterCriteria) {
            if (filter.getFilterGroup() == null) {
                continue; // Skip filters with no FilterGroup
            }

            // Append the filter group name and the pipe splitter
            filters.append(filter.getFilterGroup().name()).append(Constants.PIPE_SPLITTER);

            // Check if the filter group is related to price
            if (filter.getFilterGroup().name().equals(FilterGroup.HOTEL_PRICE.name())
                    || filter.getFilterGroup().name().equals(FilterGroup.HOTEL_PRICE_BUCKET.name())) {

                // Initialize min and max values for price filters
                int minValue = Integer.MAX_VALUE;
                int maxValue = Integer.MIN_VALUE;

                // Update min and max values if a filter range is present
                if (filter.getFilterRange() != null) {
                    minValue = filter.getFilterRange().getMinValue();
                    maxValue = filter.getFilterRange().getMaxValue();
                }

                // If valid min and max values are found, append them
                if (minValue != Integer.MAX_VALUE && maxValue != Integer.MIN_VALUE) {
                    filters.append(minValue)
                            .append(Constants.HYPHEN)
                            .append(maxValue)
                            .append(Constants.COMMA);
                }
            } else {
                // Append the filter value if it exists
                if (StringUtils.isNotEmpty(filter.getFilterValue())) {
                    filters.append(filter.getFilterValue()).append(Constants.COMMA);
                }
            }

            // Remove the last comma and append the group splitter
            if (filters.length() > 0 && filters.charAt(filters.length() - 1) == Constants.COMMA.charAt(0)) {
                filters.deleteCharAt(filters.length() - 1); // Remove trailing comma
            }
            filters.append(Constants.DEEPLINK_FILTER_GROUP_SPLITTER); // Append filter group splitter
        }

        // Remove the last filter group splitter if present
        if (filters.length() > 0 && filters.charAt(filters.length() - 1) == Constants.DEEPLINK_FILTER_GROUP_SPLITTER.charAt(0)) {
            filters.deleteCharAt(filters.length() - 1); // Remove trailing group splitter
        }

        // Append filters to the deep link if available
        if (StringUtils.isNotBlank(filters.toString())) {
            return deepLink + Constants.AND_SEPARATOR + Constants.DEEPLINK_FILTER_DATA + Constants.PR_SEPARATOR
                    + getEncodedUrl(filters.toString());
        }

        // Return the original deepLink if no valid filters were appended
        return deepLink;
    }

    public static String getEncodedUrl(String url) {
        String encodedString = "";
        try {
            if (StringUtils.isNotBlank(url))
                encodedString = URLEncoder.encode(url, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
        } catch (Exception e) {
            LOGGER.warn("Error while encoding url: {}", url);
        }
        return encodedString;
    }

    public String getQueryParameter(String queryParam, String value) {
        return Constants.AND_SEPARATOR + queryParam + Constants.PR_SEPARATOR + value;
    }

    public String buildRoomStayCandidateFromSearchWrapperS(@Nullable List<RoomStayCandidate> roomStayCandidates) {
        StringBuilder builder = new StringBuilder();
        if (CollectionUtils.isEmpty(roomStayCandidates)) {
            return builder.toString();
        }

        for (RoomStayCandidate roomStayCandidate : roomStayCandidates) {
            for (GuestCount guestCount : roomStayCandidate.getGuestCounts()) {
                if (guestCount == null)
                    continue;
                int adultCount = Integer.parseInt(guestCount.getCount());
                int childCount = 0;
                if (CollectionUtils.isNotEmpty(guestCount.getAges()))
                    childCount = guestCount.getAges().size();
                builder.append(adultCount);
                builder.append(Constants.RSQ_SPLITTER);
                builder.append(childCount);
                builder.append(Constants.RSQ_SPLITTER);
                if (CollectionUtils.isNotEmpty(guestCount.getAges())) {
                    for (int age : guestCount.getAges()) {
                        if (age >= 0 && age <= 12) {
                            builder.append(age);
                            builder.append(Constants.RSQ_SPLITTER);
                        }
                    }
                }
            }
        }
        return builder.toString();
    }

    public String buildRoomStayCandidateFromSearchWrapper(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        StringBuilder builder = new StringBuilder();
        if (CollectionUtils.isEmpty(roomStayCandidates)) {
            return builder.toString();
        }
        for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate : roomStayCandidates) {
            int adultCount = roomStayCandidate.getAdultCount();
            int childCount = roomStayCandidate.getChildAges() != null ? roomStayCandidate.getChildAges().size() : 0;
            builder.append(adultCount);
            builder.append(Constants.RSQ_SPLITTER);
            builder.append(childCount);
            builder.append(Constants.RSQ_SPLITTER);
            if (CollectionUtils.isNotEmpty(roomStayCandidate.getChildAges())) {
                for (int age : roomStayCandidate.getChildAges()) {
                    if (age >= 0 && age <= 12) {
                        builder.append(age);
                        builder.append(Constants.RSQ_SPLITTER);
                    }
                }
            }
        }
        return builder.toString();
    }

    public String buildFormUrlForDetail(SearchRoomsRequest searchRoomsRequest, SupportDetails supportDetails, String propertyType, String hotelName) {
        String formUrl = null;
        if (searchRoomsRequest != null) {
            String checkin = searchRoomsRequest.getSearchCriteria() != null ? dateUtil.getDateFormatted(searchRoomsRequest.getSearchCriteria().getCheckIn(), DateUtil.YYYY_MM_DD, DDMMYYYY) : EMPTY_STRING;
            String checkout = searchRoomsRequest.getSearchCriteria() != null ? dateUtil.getDateFormatted(searchRoomsRequest.getSearchCriteria().getCheckOut(), DateUtil.YYYY_MM_DD, DDMMYYYY) : EMPTY_STRING;
            String city = searchRoomsRequest.getSearchCriteria() != null ? searchRoomsRequest.getSearchCriteria().getCityCode() : EMPTY_STRING;
            String country = searchRoomsRequest.getSearchCriteria() != null ? searchRoomsRequest.getSearchCriteria().getCountryCode() : "IN";
            String locusId = searchRoomsRequest.getSearchCriteria() != null ? searchRoomsRequest.getSearchCriteria().getLocationId() : EMPTY_STRING;
            String locusType = searchRoomsRequest.getSearchCriteria() != null ? searchRoomsRequest.getSearchCriteria().getLocationType() : TYPE_CITY;
            String rsc = searchRoomsRequest.getSearchCriteria() != null ? utility.buildRscValue(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates()) : EMPTY_STRING;
            String _uCurrency = searchRoomsRequest.getSearchCriteria() != null ? searchRoomsRequest.getSearchCriteria().getCurrency() : DEFAULT_CUR_INR;
            String appVersion = searchRoomsRequest.getDeviceDetails() != null ? searchRoomsRequest.getDeviceDetails().getAppVersion() : EMPTY_STRING;
            String deviceId = searchRoomsRequest.getDeviceDetails() != null ? searchRoomsRequest.getDeviceDetails().getDeviceId() : EMPTY_STRING;
            String bookingDevice = searchRoomsRequest.getDeviceDetails() != null ? searchRoomsRequest.getDeviceDetails().getBookingDevice() : EMPTY_STRING;
            String deviceType = searchRoomsRequest.getDeviceDetails() != null ? searchRoomsRequest.getDeviceDetails().getDeviceType() : EMPTY_STRING;
            String visitorId = searchRoomsRequest.getRequestDetails() != null ? searchRoomsRequest.getRequestDetails().getVisitorId() : EMPTY_STRING;
            String visitNumber = String.valueOf(searchRoomsRequest.getRequestDetails() != null && searchRoomsRequest.getRequestDetails().getVisitNumber() != null ? searchRoomsRequest.getRequestDetails().getVisitNumber() : 1);
            String funnelSource = searchRoomsRequest.getRequestDetails() != null ? searchRoomsRequest.getRequestDetails().getFunnelSource() : EMPTY_STRING;
            String idContext = searchRoomsRequest.getRequestDetails() != null ? searchRoomsRequest.getRequestDetails().getIdContext() : EMPTY_STRING;
            String funnelName = searchRoomsRequest.getRequestDetails() != null ? searchRoomsRequest.getRequestDetails().getFunnelSource() : EMPTY_STRING;
            propertyType = StringUtils.isNotEmpty(propertyType) ? propertyType : HOTEL;
            hotelName = hotelName.replace(' ', '+');
            formUrl = MessageFormat.format(supportDetails.getFormUrl(), checkin, checkout, city, country, locusId, locusType, rsc, _uCurrency, appVersion, deviceId, bookingDevice, deviceType, visitorId, visitNumber, funnelSource, idContext, funnelName, propertyType, hotelName);
        }
        return formUrl;
    }
}
