package com.mmt.hotels.clientgateway.transformer.factory;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.*;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.FilterCategory;
import com.mmt.hotels.clientgateway.consul.FilterConfigConsul;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.helpers.FilterHelper;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.FilterConfig;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.transformer.request.FilterRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.FilterResponseTransformer;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.dpt.UserCohort;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.mmt.hotels.clientgateway.constants.Constants.CLIENT_DESKTOP;
import static com.mmt.hotels.clientgateway.constants.Constants.CLIENT_PWA;

@Component
public class FilterFactory {

    @Value("${consul.enable}")
    private boolean consulFlag;
    @Autowired
    FilterConfigConsul filterConfigConsul;

    @Autowired
    FilterResponseTransformer filterResponseTransformer;

    @Autowired
    FilterRequestTransformer filterRequestTransformer;

    @Autowired
    FilterHelper filterHelper;

    @Autowired
    PropertyManager propertyManager;

    private final Gson gson = new Gson();

    @Autowired
    PolyglotHelper polyglotHelper;
    private String baseFilterSettings;
    private String baseFilterSettingsV2;
    private String appsDomFilterSettingsV2;
    private String appsIntlFilterSettingsV2;
    private String appsHomestayFilterSettingsV2;
    private String appsCorpIntlFilterSettingsV2;
    private String appsCorpDomFilterSettingsV2;
    private String dayUseFilterSettingsV2;
    private String gccFilterSettingsV2;
    private String premiumFunnelFilterSettingsV2;
    private String desktopAltBookingFilterSettingV2;

    private String desktopDomHotelsFilterSettingV2;
    private String desktopDomHomestayFilterSettingV2;
    private String desktopIntlHomestayFilterSettingV2;
    private String desktopIntlHotelsFilterSettingV2;
    private String desktopGCCFilterSettingV2;
    private String desktopMyPartnerFilterSettingsV2;
    private String desktopCorpDomFilterSettingV2;
    private String desktopCorpIntlFilterSettingV2;

    private String pwaDomHotelsFilterSettingV2;
    private String pwaDomHomestayFilterSettingV2;
    private String pwaIntlHomestayFilterSettingV2;
    private String pwaIntlHotelsFilterSettingV2;
    private String pwaGCCFilterSettingV2;
    private String pwaMyPartnerFilterSettingsV2;
    private String seoDomFilterSettingV2;
    private String seoIntlFilterSettingV2;
    private String metaIntlFilterSettingV2;
    private String metaDomFilterSettingV2;
    private String semIntlFilterSettingV2;
    private String semDomFilterSettingV2;

    private String pwaMyPartnerFilterSettings;
    private String desktopMyPartnerFilterSettings;
    private String desktopGCCFilterSetting;
    private String androidGCCFilterSetting;
    private String iosGCCFilterSetting;
    private String pwaGCCFilterSetting;
    private String seoIntlFilterSetting;
    private String seoDomFilterSetting;
    private String metaIntlFilterSetting;
    private String metaDomFilterSetting;
    private String semIntlFilterSetting;
    private String semDomFilterSetting;
    private String phonePeFilterSetting;
    private String desktopIntlHotelsFilterSetting;
    private String pwaIntlHotelsFilterSetting;
    private String appsIntlHotelsFilterSetting;
    private String desktopDomHotelsFilterSetting;
    private String pwaDomHotelsFilterSetting;
    private String appsDomHotelsFilterSetting;
    private String desktopIntlHomestayFilterSetting;
    private String pwaIntlHomestayFilterSetting;
    private String appsIntlHomestayFilterSetting;
    private String desktopDomHomestayFilterSetting;
    private String pwaDomHomestayFilterSetting;
    private String appsDomHomestayFilterSetting;
    private String dayuseFilterSetting;
    private String desktopCorpIntlFilterSetting;
    private String appsCorpIntlFilterSetting;
    private String pwaCorpIntlFilterSetting;
    private String desktopCorpDomFilterSetting;
    private String appsCorpDomFilterSetting;
    private String pwaCorpDomFilterSetting;
    private String desktopAltBookingFilterSetting;
    private Map<String, List<Filter>> compositeFilterMapping;


    /**
     * below categories will be added to base filter settings for FILTER_PILL_EXP HTL-38235
     **/
    @Value("${filter.meal.preferences.category}")
    private String mealPrefConfig;

    @Value("${filter.toggle.category}")
    private String toggleFilterConfig;

    @Value("${filter.homestay.toggle.category}")
    private String toggleFilterConfigHomestay;

    FilterConfigCategory mealPrefConfigCategory;

    FilterConfigCategory toggleConfigCategory;

    private static final Logger logger = LoggerFactory.getLogger(FilterFactory.class);



    @PostConstruct
    public void init() {

        if (consulFlag){
            baseFilterSettings = filterConfigConsul.getBaseFilterSettings();
            baseFilterSettingsV2 = filterConfigConsul.getBaseFilterSettingsV2();
            appsDomFilterSettingsV2 = filterConfigConsul.getAppsDomFilterSettingsV2();
            appsIntlFilterSettingsV2 = filterConfigConsul.getAppsIntlFilterSettingsV2();
            appsHomestayFilterSettingsV2 = filterConfigConsul.getAppsHomestayFilterSettingsV2();
            appsCorpIntlFilterSettingsV2 = filterConfigConsul.getAppsCorpIntlFilterSettingsV2();
            appsCorpDomFilterSettingsV2 = filterConfigConsul.getAppsCorpDomFilterSettingsV2();
            dayUseFilterSettingsV2 = filterConfigConsul.getDayUseFilterSettingsV2();
            gccFilterSettingsV2 = filterConfigConsul.getGccFilterSettingsV2();
            premiumFunnelFilterSettingsV2 = filterConfigConsul.getPremiumFunnelFilterSettingsV2();
            desktopAltBookingFilterSettingV2 = filterConfigConsul.getDesktopAltBookingFilterSettingV2();

            desktopDomHotelsFilterSettingV2 = filterConfigConsul.getDesktopDomHotelsFilterSettingV2();
            desktopDomHomestayFilterSettingV2 = filterConfigConsul.getDesktopDomHomestayFilterSettingV2();
            desktopIntlHomestayFilterSettingV2 = filterConfigConsul.getDesktopIntlHomestayFilterSettingV2();
            desktopIntlHotelsFilterSettingV2 = filterConfigConsul.getDesktopIntlHotelsFilterSettingV2();
            desktopGCCFilterSettingV2 = filterConfigConsul.getDesktopGCCFilterSettingV2();
            desktopMyPartnerFilterSettingsV2 = filterConfigConsul.getDesktopMyPartnerFilterSettingsV2();
            desktopCorpDomFilterSettingV2 = filterConfigConsul.getDesktopCorpDomFilterSettingV2();
            desktopCorpIntlFilterSettingV2 = filterConfigConsul.getDesktopCorpIntlFilterSettingV2();

            pwaDomHotelsFilterSettingV2 = filterConfigConsul.getPwaDomHotelsFilterSettingV2();
            pwaDomHomestayFilterSettingV2 = filterConfigConsul.getPwaDomHomestayFilterSettingV2();
            pwaIntlHomestayFilterSettingV2 = filterConfigConsul.getPwaIntlHomestayFilterSettingV2();
            pwaIntlHotelsFilterSettingV2 = filterConfigConsul.getPwaIntlHotelsFilterSettingV2();
            pwaGCCFilterSettingV2 = filterConfigConsul.getPwaGCCFilterSettingV2();
            pwaMyPartnerFilterSettingsV2 = filterConfigConsul.getPwaMyPartnerFilterSettingsV2();

            seoDomFilterSettingV2 = filterConfigConsul.getSeoDomFilterSettingV2();
            seoIntlFilterSettingV2 = filterConfigConsul.getSeoIntlFilterSettingV2();
            metaIntlFilterSettingV2 = filterConfigConsul.getMetaIntlFilterSettingV2();
            metaDomFilterSettingV2 = filterConfigConsul.getMetaDomFilterSettingV2();
            semIntlFilterSettingV2 = filterConfigConsul.getSemIntlFilterSettingV2();
            semDomFilterSettingV2 = filterConfigConsul.getSemDomFilterSettingV2();

            compositeFilterMapping = filterConfigConsul.getCompositeFilterConfig();
            pwaMyPartnerFilterSettings = filterConfigConsul.getPwaMyPartnerFilterSettings();
            desktopMyPartnerFilterSettings = filterConfigConsul.getDesktopMyPartnerFilterSettings();

            desktopGCCFilterSetting = filterConfigConsul.getDesktopGCCFilterSetting();
            androidGCCFilterSetting = filterConfigConsul.getAndroidGCCFilterSetting();
            iosGCCFilterSetting = filterConfigConsul.getIosGCCFilterSetting();
            pwaGCCFilterSetting = filterConfigConsul.getPwaGCCFilterSetting();
            seoIntlFilterSetting = filterConfigConsul.getSeoIntlFilterSetting();
            seoDomFilterSetting = filterConfigConsul.getSeoDomFilterSetting();
            metaIntlFilterSetting = filterConfigConsul.getMetaIntlFilterSetting();
            metaDomFilterSetting = filterConfigConsul.getMetaDomFilterSetting();
            semIntlFilterSetting = filterConfigConsul.getSemIntlFilterSetting();
            semDomFilterSetting = filterConfigConsul.getSemDomFilterSetting();
            phonePeFilterSetting = filterConfigConsul.getPhonePeFilterSetting();
            desktopIntlHotelsFilterSetting = filterConfigConsul.getDesktopIntlHotelsFilterSetting();
            appsIntlHotelsFilterSetting = filterConfigConsul.getAppsIntlHotelsFilterSetting();
            pwaIntlHotelsFilterSetting = filterConfigConsul.getPwaIntlHotelsFilterSetting();
            desktopDomHotelsFilterSetting = filterConfigConsul.getDesktopDomHotelsFilterSetting();
            appsDomHotelsFilterSetting = filterConfigConsul.getAppsDomHotelsFilterSetting();
            pwaDomHotelsFilterSetting = filterConfigConsul.getPwaDomHotelsFilterSetting();
            desktopIntlHomestayFilterSetting = filterConfigConsul.getDesktopIntlHomestayFilterSetting();
            appsIntlHomestayFilterSetting = filterConfigConsul.getAppsIntlHomestayFilterSetting();
            pwaIntlHomestayFilterSetting = filterConfigConsul.getPwaIntlHomestayFilterSetting();
            desktopDomHomestayFilterSetting = filterConfigConsul.getDesktopDomHomestayFilterSetting();
            appsDomHomestayFilterSetting = filterConfigConsul.getAppsDomHomestayFilterSetting();
            pwaDomHomestayFilterSetting = filterConfigConsul.getPwaDomHomestayFilterSetting();
            dayuseFilterSetting = filterConfigConsul.getDayuseFilterSetting();
            desktopCorpIntlFilterSetting = filterConfigConsul.getDesktopCorpIntlFilterSetting();
            appsCorpIntlFilterSetting = filterConfigConsul.getAppsCorpIntlFilterSetting();
            pwaCorpIntlFilterSetting = filterConfigConsul.getPwaCorpIntlFilterSetting();
            desktopCorpDomFilterSetting = filterConfigConsul.getDesktopCorpDomFilterSetting();
            appsCorpDomFilterSetting = filterConfigConsul.getAppsCorpDomFilterSetting();
            pwaCorpDomFilterSetting = filterConfigConsul.getPwaCorpDomFilterSetting();
            desktopAltBookingFilterSetting = filterConfigConsul.getDesktopAltBookingFilterSetting();
            logger.debug("Fetching values from commonConfig consul");
        }
        else{
            logger.warn("Fetching values from property manager (PMS)");
            FilterConfig filterConfig = propertyManager.getProperty("cgFilterConfig", FilterConfig.class);
            baseFilterSettings = filterConfig.baseFilterSettings();
            compositeFilterMapping = filterConfig.compositeFilterConfig();

            // myPartner property filter settings
            pwaMyPartnerFilterSettings = filterConfig.pwaMyPartnerFilterSettings();
            desktopMyPartnerFilterSettings = filterConfig.desktopMyPartnerFilterSettings();

            //GCC filter setting
            desktopGCCFilterSetting = filterConfig.desktopGCCFilterSetting();
            androidGCCFilterSetting = filterConfig.androidGCCFilterSetting();
            iosGCCFilterSetting = filterConfig.iosGCCFilterSetting();
            pwaGCCFilterSetting = filterConfig.pwaGCCFilterSetting();
            //Seo filter settings
            seoIntlFilterSetting = filterConfig.seoIntlFilterSetting();
            seoDomFilterSetting = filterConfig.seoDomFilterSetting();
            //Meta filter setting
            metaIntlFilterSetting = filterConfig.metaIntlFilterSetting();
            metaDomFilterSetting = filterConfig.metaDomFilterSetting();
            //SEM filter setting
            semIntlFilterSetting = filterConfig.semIntlFilterSetting();
            semDomFilterSetting = filterConfig.semDomFilterSetting();
            //PhonePe filter Setting
            phonePeFilterSetting = filterConfig.phonePeFilterSetting();
            // hotels filter setting
            desktopIntlHotelsFilterSetting = filterConfig.desktopIntlHotelsFilterSetting();
            appsIntlHotelsFilterSetting = filterConfig.appsIntlHotelsFilterSetting();
            pwaIntlHotelsFilterSetting = filterConfig.pwaIntlHotelsFilterSetting();
            desktopDomHotelsFilterSetting = filterConfig.desktopDomHotelsFilterSetting();
            appsDomHotelsFilterSetting = filterConfig.appsDomHotelsFilterSetting();
            pwaDomHotelsFilterSetting = filterConfig.pwaDomHotelsFilterSetting();
            // homestay filter setting
            desktopIntlHomestayFilterSetting = filterConfig.desktopIntlHomestayFilterSetting();
            appsIntlHomestayFilterSetting = filterConfig.appsIntlHomestayFilterSetting();
            pwaIntlHomestayFilterSetting = filterConfig.pwaIntlHomestayFilterSetting();
            desktopDomHomestayFilterSetting = filterConfig.desktopDomHomestayFilterSetting();
            appsDomHomestayFilterSetting = filterConfig.appsDomHomestayFilterSetting();
            pwaDomHomestayFilterSetting = filterConfig.pwaDomHomestayFilterSetting();
            //dayUse filter setting
            dayuseFilterSetting = filterConfig.dayuseFilterSetting();
            // Corp filter setting
            desktopCorpIntlFilterSetting = filterConfig.desktopCorpIntlFilterSetting();
            appsCorpIntlFilterSetting = filterConfig.appsCorpIntlFilterSetting();
            pwaCorpIntlFilterSetting = filterConfig.pwaCorpIntlFilterSetting();
            desktopCorpDomFilterSetting = filterConfig.desktopCorpDomFilterSetting();
            appsCorpDomFilterSetting = filterConfig.appsCorpDomFilterSetting();
            pwaCorpDomFilterSetting = filterConfig.pwaCorpDomFilterSetting();

            desktopDomHotelsFilterSettingV2 = filterConfigConsul.getDesktopDomHotelsFilterSettingV2();
            desktopDomHomestayFilterSettingV2 = filterConfigConsul.getDesktopDomHomestayFilterSettingV2();
            desktopIntlHomestayFilterSettingV2 = filterConfigConsul.getDesktopIntlHomestayFilterSettingV2();
            desktopIntlHotelsFilterSettingV2 = filterConfigConsul.getDesktopIntlHotelsFilterSettingV2();
            desktopGCCFilterSettingV2 = filterConfigConsul.getDesktopGCCFilterSettingV2();
            desktopMyPartnerFilterSettingsV2 = filterConfigConsul.getDesktopMyPartnerFilterSettingsV2();
            desktopCorpDomFilterSettingV2 = filterConfigConsul.getDesktopCorpDomFilterSettingV2();
            desktopCorpIntlFilterSettingV2 = filterConfigConsul.getDesktopCorpIntlFilterSettingV2();

            pwaDomHotelsFilterSettingV2 = filterConfigConsul.getPwaDomHotelsFilterSettingV2();
            pwaDomHomestayFilterSettingV2 = filterConfigConsul.getPwaDomHomestayFilterSettingV2();
            pwaIntlHomestayFilterSettingV2 = filterConfigConsul.getPwaIntlHomestayFilterSettingV2();
            pwaIntlHotelsFilterSettingV2 = filterConfigConsul.getPwaIntlHotelsFilterSettingV2();
            pwaGCCFilterSettingV2 = filterConfigConsul.getPwaGCCFilterSettingV2();
            pwaMyPartnerFilterSettingsV2 = filterConfigConsul.getPwaMyPartnerFilterSettingsV2();

            seoDomFilterSettingV2 = filterConfigConsul.getSeoDomFilterSettingV2();
            seoIntlFilterSettingV2 = filterConfigConsul.getSeoIntlFilterSettingV2();
            metaIntlFilterSettingV2 = filterConfigConsul.getMetaIntlFilterSettingV2();
            metaDomFilterSettingV2 = filterConfigConsul.getMetaDomFilterSettingV2();
            semIntlFilterSettingV2 = filterConfigConsul.getSemIntlFilterSettingV2();
            semDomFilterSettingV2 = filterConfigConsul.getSemDomFilterSettingV2();

            filterConfig.addPropertyChangeListener("baseFilterSettings", event -> {
                baseFilterSettings = filterConfig.baseFilterSettings();
            });

            // myPartner property change listeners
            filterConfig.addPropertyChangeListener("pwaMyPartnerFilterSettings", event -> {
                pwaMyPartnerFilterSettings = filterConfig.pwaMyPartnerFilterSettings();
            });
            filterConfig.addPropertyChangeListener("desktopMyPartnerFilterSettings", event -> {
                desktopMyPartnerFilterSettings = filterConfig.desktopMyPartnerFilterSettings();
            });

            filterConfig.addPropertyChangeListener("compositeFilterMapping", event -> {
                compositeFilterMapping = filterConfig.compositeFilterConfig();
            });

            filterConfig.addPropertyChangeListener("desktopGCCFilterSetting", event -> {
                desktopGCCFilterSetting = filterConfig.desktopGCCFilterSetting();
            });
            filterConfig.addPropertyChangeListener("androidGCCFilterSetting", event -> {
                androidGCCFilterSetting = filterConfig.androidGCCFilterSetting();
            });
            filterConfig.addPropertyChangeListener("iosGCCFilterSetting", event -> {
                iosGCCFilterSetting = filterConfig.iosGCCFilterSetting();
            });
            filterConfig.addPropertyChangeListener("pwaGCCFilterSetting", event -> {
                pwaGCCFilterSetting = filterConfig.pwaGCCFilterSetting();
            });

            filterConfig.addPropertyChangeListener("seoIntlFilterSetting", event -> {
                seoIntlFilterSetting = filterConfig.seoIntlFilterSetting();
            });
            filterConfig.addPropertyChangeListener("seoDomFilterSetting", event -> {
                seoDomFilterSetting = filterConfig.seoDomFilterSetting();
            });
            filterConfig.addPropertyChangeListener("metaIntlFilterSetting", event -> {
                metaIntlFilterSetting = filterConfig.pwaGCCFilterSetting();
            });
            filterConfig.addPropertyChangeListener("metaDomFilterSetting", event -> {
                metaDomFilterSetting = filterConfig.metaDomFilterSetting();
            });

            filterConfig.addPropertyChangeListener("semIntlFilterSetting", event -> {
                semIntlFilterSetting = filterConfig.semIntlFilterSetting();
            });
            filterConfig.addPropertyChangeListener("semDomFilterSetting", event -> {
                semDomFilterSetting = filterConfig.semDomFilterSetting();
            });

            filterConfig.addPropertyChangeListener("phonePeFilterSetting", event -> {
                phonePeFilterSetting = filterConfig.phonePeFilterSetting();
            });

            filterConfig.addPropertyChangeListener("desktopIntlHotelsFilterSetting", event -> {
                desktopIntlHotelsFilterSetting = filterConfig.desktopIntlHotelsFilterSetting();
            });
            filterConfig.addPropertyChangeListener("appsIntlHotelsFilterSetting", event -> {
                appsIntlHotelsFilterSetting = filterConfig.appsIntlHotelsFilterSetting();
            });
            filterConfig.addPropertyChangeListener("pwaIntlHotelsFilterSetting", event -> {
                pwaIntlHotelsFilterSetting = filterConfig.pwaIntlHotelsFilterSetting();
            });
            filterConfig.addPropertyChangeListener("desktopDomHotelsFilterSetting", event -> {
                desktopDomHotelsFilterSetting = filterConfig.desktopDomHotelsFilterSetting();
            });
            filterConfig.addPropertyChangeListener("appsDomHotelsFilterSetting", event -> {
                appsDomHotelsFilterSetting = filterConfig.appsDomHotelsFilterSetting();
            });
            filterConfig.addPropertyChangeListener("pwaDomHotelsFilterSetting", event -> {
                pwaDomHotelsFilterSetting = filterConfig.pwaDomHotelsFilterSetting();
            });

            filterConfig.addPropertyChangeListener("desktopIntlHomestayFilterSetting", event -> {
                desktopIntlHomestayFilterSetting = filterConfig.desktopIntlHomestayFilterSetting();
            });
            filterConfig.addPropertyChangeListener("appsIntlHomestayFilterSetting", event -> {
                appsIntlHomestayFilterSetting = filterConfig.appsIntlHomestayFilterSetting();
            });
            filterConfig.addPropertyChangeListener("pwaIntlHomestayFilterSetting", event -> {
                pwaIntlHomestayFilterSetting = filterConfig.pwaIntlHomestayFilterSetting();
            });
            filterConfig.addPropertyChangeListener("desktopDomHomestayFilterSetting", event -> {
                desktopDomHomestayFilterSetting = filterConfig.desktopDomHomestayFilterSetting();
            });
            filterConfig.addPropertyChangeListener("appsDomHomestayFilterSetting", event -> {
                appsDomHomestayFilterSetting = filterConfig.appsDomHomestayFilterSetting();
            });
            filterConfig.addPropertyChangeListener("pwaDomHomestayFilterSetting", event -> {
                pwaDomHomestayFilterSetting = filterConfig.pwaDomHomestayFilterSetting();
            });

            filterConfig.addPropertyChangeListener("dayuseFilterSetting", event -> {
                dayuseFilterSetting = filterConfig.dayuseFilterSetting();
            });

            filterConfig.addPropertyChangeListener("desktopCorpIntlFilterSetting", event -> {
                desktopCorpIntlFilterSetting = filterConfig.desktopCorpIntlFilterSetting();
            });
            filterConfig.addPropertyChangeListener("appsCorpIntlFilterSetting", event -> {
                appsCorpIntlFilterSetting = filterConfig.appsCorpIntlFilterSetting();
            });
            filterConfig.addPropertyChangeListener("pwaCorpIntlFilterSetting", event -> {
                pwaCorpIntlFilterSetting = filterConfig.pwaCorpIntlFilterSetting();
            });
            filterConfig.addPropertyChangeListener("desktopCorpDomFilterSetting", event -> {
                desktopCorpDomFilterSetting = filterConfig.desktopCorpDomFilterSetting();
            });
            filterConfig.addPropertyChangeListener("appsCorpDomFilterSetting", event -> {
                appsCorpDomFilterSetting = filterConfig.appsCorpDomFilterSetting();
            });
            filterConfig.addPropertyChangeListener("pwaCorpDomFilterSetting", event -> {
                pwaCorpDomFilterSetting = filterConfig.pwaCorpDomFilterSetting();
            });
        }


    }

    public FilterRequestTransformer getRequestService(String client) {
        if (StringUtils.isEmpty(client))
            return filterRequestTransformer;
        switch (client.toUpperCase()) {
            case "PWA":
                return filterRequestTransformer;
            case "DESKTOP":
                return filterRequestTransformer;
            case "ANDROID":
                return filterRequestTransformer;
            case "IOS":
                return filterRequestTransformer;
        }
        return filterRequestTransformer;
    }

    public FilterResponseTransformer getResponseService(String client) {
        return filterResponseTransformer;
    }

    /*
    * myPartner change log :
    *   commonModifierResponse which contains profileType and affiliateId[subProfileType] is sent as another request parameter
    *   The values will be used to read myPartner filter configs
    *   We are modifying the current getFilterConfiguration,
    *   since the change affects only the test cases [test cases are modified as well to mock this object] and getFilterConfiguration is
    *   not used in any other service or  any functional part of the code base
    * */
    public FilterConfiguration getFilterConfiguration(String client, String idContext, String funnelSource, CommonModifierResponse commonModifierResponse, String locationName, FilterCountRequest filterRequest, String filterSettingsFromRuleEngine){

        FilterConfiguration fConfig = null;
        fConfig = gson.fromJson(baseFilterSettings, new TypeToken<FilterConfiguration>() {
        }.getType());

        String trafficSource = null;
        if(filterRequest!=null && filterRequest.getRequestDetails()!=null && filterRequest.getRequestDetails().getTrafficSource()!=null){
            trafficSource = filterRequest.getRequestDetails().getTrafficSource().getSource();
        }
        String siteDomain = null;
        if(filterRequest!=null && filterRequest.getRequestDetails()!=null && StringUtils.isNotEmpty(filterRequest.getRequestDetails().getSiteDomain())){
            siteDomain = filterRequest.getRequestDetails().getSiteDomain();
        }
        String countryCode = null;
        if(filterRequest!=null && filterRequest.getSearchCriteria()!=null && StringUtils.isNotEmpty(filterRequest.getSearchCriteria().getCountryCode())){
            countryCode = filterRequest.getSearchCriteria().getCountryCode();
        }

        if(fConfig!=null && MapUtils.isNotEmpty(fConfig.getFilters())) {
            LinkedHashMap<String, FilterConfigCategory> filtersConfigMap = fConfig.getFilters();
            if (MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && !Constants.EXP_TRUE_VALUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(Constants.FILTER_PILL_EXP))) {
//            null check
                if (filtersConfigMap.containsKey(Constants.TOGGLE_FILTERS_CATEGORY)) {
                    fConfig.getFilters().remove(Constants.TOGGLE_FILTERS_CATEGORY);
                }
                if (filtersConfigMap.containsKey(Constants.MEAL_PREFERENCE_CATEGORY)) {
                    fConfig.getFilters().remove(Constants.MEAL_PREFERENCE_CATEGORY);
                }
            } else if(Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource) && filtersConfigMap.get(Constants.TOGGLE_FILTERS_CATEGORY)!=null && MapUtils.isNotEmpty(filtersConfigMap.get(Constants.TOGGLE_FILTERS_CATEGORY).getGroups())) {
                for(Map.Entry<String, LinkedHashMap<String, FilterConfigDetail>> groupEntry: fConfig.getFilters().get(Constants.TOGGLE_FILTERS_CATEGORY).getGroups().entrySet()) {
                    for (Map.Entry<String, FilterConfigDetail> entry : groupEntry.getValue().entrySet()) {
                        entry.getValue().setDescription(null);
                    }
                }
            }
            if (MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && !Constants.TRUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(Constants.EXP_CITY_COLLECTION_ENABLE))) {
                if (filtersConfigMap.containsKey(Constants.DPT_COLLECTIONS)) {
                    fConfig.getFilters().remove(Constants.DPT_COLLECTIONS);
                }
            }
        }

        FilterConfiguration baseFilterSettingsModified = polyglotHelper.translateFilterConfig(fConfig,funnelSource);

        // Append location name for the title of DRIVING_DURATION_HR
        if ( baseFilterSettingsModified != null && MapUtils.isNotEmpty(baseFilterSettingsModified.getFilters()) &&
                baseFilterSettingsModified.getFilters().containsKey(Constants.DRIVING_DURATION_HR)){
            String title = baseFilterSettingsModified.getFilters().get(Constants.DRIVING_DURATION_HR).getTitle();
            title = StringUtils.isEmpty(title) ? Constants.EMPTY_STRING : title + Constants.SPACE + locationName;
            baseFilterSettingsModified.getFilters().get(Constants.DRIVING_DURATION_HR).setTitle(title);
        }

        String baseFilterModified = gson.toJson(baseFilterSettingsModified);

        /*
         * myPartner change log :
         *   Since myPartner checks work completely on different request parameters [profile/subProfileType] instead of idContext
         *   we will not be using the switch case and these cases will entirely be moved to a block where the b2c flow is true
         * */
        FilterConfiguration filterConfiguration = null;
        if (StringUtils.isNotEmpty(filterSettingsFromRuleEngine)) {
            filterConfiguration = filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(filterSettingsFromRuleEngine, funnelSource), idContext);
        } else if (Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) &&
                Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId())) {
            /*
             * myPartner change log :
             *   We won't be switching on idContext, and just on the client under the myPartner isolated context
             * */
            switch (client){
                case "PWA":
                    filterConfiguration = filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(pwaMyPartnerFilterSettings,funnelSource), idContext);
                    break;
                case "DESKTOP":
                    filterConfiguration =  filterHelper.getFilterConfig(baseFilterModified,modifiedFilterWithPolyglotData(desktopMyPartnerFilterSettings,funnelSource), idContext);
                    break;
                default :
                    filterConfiguration =  filterHelper.getFilterConfig(baseFilterModified, idContext);
            }
        }else if (filterRequest != null && filterRequest.getAlternateBookingInfo() != null && filterRequest.getAlternateBookingInfo().isAlternateBooking()) {
            return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(desktopAltBookingFilterSetting, funnelSource), idContext);
        } else if (Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext)) {
            filterConfiguration = getCorpFilterConfiguration(client, countryCode, baseFilterModified, funnelSource, idContext);
        } else if (Constants.TRAFFIC_SOURCE_SEO.equalsIgnoreCase(trafficSource)) {
            // for GCC
            filterConfiguration = getSeoFilterConfiguration(client, countryCode, baseFilterModified, funnelSource, idContext);
        } else if (Constants.TRAFFIC_SOURCE_META.equalsIgnoreCase(trafficSource)) {
            filterConfiguration = getMetaFilterConfiguration(client, countryCode, baseFilterModified, funnelSource, idContext);
        } else if (Constants.TRAFFIC_SOURCE_SEM.equalsIgnoreCase(trafficSource)) {
            // for GCC
            filterConfiguration = getSemFilterConfiguration(client, countryCode, baseFilterModified, funnelSource, idContext);
        } else if (Constants.TRAFFIC_SOURCE_PHONEPE.equalsIgnoreCase(trafficSource)) {
            return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(phonePeFilterSetting, funnelSource), idContext);
        } else if (Utility.isRegionGccOrKsa(siteDomain)) {
            return getGccFilterConfiguration(client, countryCode, baseFilterModified, funnelSource, idContext);
        } else if (Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource)) {
            filterConfiguration = getHomestayFilterConfiguration(client, countryCode, baseFilterModified, funnelSource, idContext);
        } else if (Constants.FUNNEL_SOURCE_DAYUSE.equalsIgnoreCase(funnelSource)) {
            filterConfiguration = filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(dayuseFilterSetting, funnelSource), idContext);
        } else{
            filterConfiguration = getHotelFilterConfiguration(client, countryCode, baseFilterModified, funnelSource, idContext);
        }
        modifyFilterConfigFromExperiments(filterConfiguration,commonModifierResponse.getExpDataMap());
        return filterConfiguration;
    }

    // This function is deprecated. Any pokus check should be implemented at downstream.
    @Deprecated
    public void modifyFilterConfigFromExperiments(FilterConfiguration filterConfiguration, LinkedHashMap<String, String> expDataMap) {
        //if filterConfiguration is non-null and expDataMap is non-null and does not contain the key ExperimentKeys.<experiment_name>.getKey() or its value is false
        // remove the filter group from the filterConfiguration
        if (filterConfiguration != null && MapUtils.isNotEmpty(filterConfiguration.getFilters()) && MapUtils.isNotEmpty(expDataMap)) {
            LinkedHashMap<String, FilterConfigCategory> filters = filterConfiguration.getFilters();
            if(!Constants.TRUE.equalsIgnoreCase(expDataMap.getOrDefault(ExperimentKeys.MMT_SPOTLIGHT.getKey(),Constants.FALSE))) {
                if (filters.getOrDefault(Constants.TOGGLE_FILTERS_CATEGORY, null) != null && filters.get(Constants.TOGGLE_FILTERS_CATEGORY).getGroups().getOrDefault(FilterGroup.SPOTLIGHT.name(), null) != null) {
                    filters.get(Constants.TOGGLE_FILTERS_CATEGORY).getGroups().remove(FilterGroup.SPOTLIGHT.name());
                }
            }
            if(!Constants.TRUE.equalsIgnoreCase(expDataMap.getOrDefault(ExperimentKeys.BUSINESS_REVIEW.getKey(),Constants.FALSE))) {
                if (filters.getOrDefault(FilterCategory.USER_RATING_MMT_BRAND, null) != null) {
                    filters.get(FilterCategory.USER_RATING_MMT_BRAND).getGroups().remove(FilterGroup.UGC_BUSINESS_RATING.name());
                }
            }
            if(!(Constants.TRUE.equalsIgnoreCase(expDataMap.get(ExperimentKeys.FREE_STAY_FOR_KIDS_MMT_EXP_KEY.getKey())))){
                if (filters.getOrDefault(Constants.FILTER_POPULAR, null) != null && filters.get(Constants.FILTER_POPULAR).getGroups().containsKey(FilterGroup.FREE_STAY_FOR_KIDS_AVAIL.name())) {
                    filters.get(Constants.FILTER_POPULAR).getGroups().remove(FilterGroup.FREE_STAY_FOR_KIDS_AVAIL.name());
                }
            }
        }
    }

    // This function is deprecated. Any pokus check should be implemented at downstream.
    @Deprecated
    public void modifyFilterConfigFromExperiments(FilterConfigurationV2 filterConfiguration, LinkedHashMap<String, String> expDataMap) {
        //if filterConfiguration is non-null and expDataMap is non-null and does not contain the key ExperimentKeys.<experiment_name>.getKey() or its value is false
        // remove the filter group from the filterConfiguration
        if (filterConfiguration != null && MapUtils.isNotEmpty(filterConfiguration.getFilterPages()) && MapUtils.isNotEmpty(expDataMap)) {
            for (Map.Entry<String, FilterPage> filterConfigEntry : filterConfiguration.getFilterPages().entrySet()) {
                LinkedHashMap<String, FilterConfigCategory> filters = filterConfigEntry.getValue().getFilters();
                if(!Constants.TRUE.equalsIgnoreCase(expDataMap.getOrDefault(ExperimentKeys.MMT_SPOTLIGHT.getKey(),Constants.FALSE))) {
                    if (filters.getOrDefault(Constants.TOGGLE_FILTERS_CATEGORY, null) != null && filters.get(Constants.TOGGLE_FILTERS_CATEGORY).getGroups().getOrDefault(FilterGroup.SPOTLIGHT.name(), null) != null) {
                        filters.get(Constants.TOGGLE_FILTERS_CATEGORY).getGroups().remove(FilterGroup.SPOTLIGHT.name());
                    }
                }
                if(!Constants.TRUE.equalsIgnoreCase(expDataMap.getOrDefault(ExperimentKeys.BUSINESS_REVIEW.getKey(),Constants.FALSE))) {
                    if (filters.getOrDefault(FilterCategory.USER_RATING_MMT_BRAND, null) != null) {
                        filters.get(FilterCategory.USER_RATING_MMT_BRAND).getGroups().remove(FilterGroup.UGC_BUSINESS_RATING.name());
                    }
                }
                if(!(Constants.TRUE.equalsIgnoreCase(expDataMap.get(ExperimentKeys.FREE_STAY_FOR_KIDS_MMT_EXP_KEY.getKey())))){
                    if (filters.getOrDefault(Constants.FILTER_POPULAR, null) != null && filters.get(Constants.FILTER_POPULAR).getGroups().containsKey(FilterGroup.FREE_STAY_FOR_KIDS_AVAIL.name())) {
                        filters.get(Constants.FILTER_POPULAR).getGroups().remove(FilterGroup.FREE_STAY_FOR_KIDS_AVAIL.name());
                    }
                }
                if(!Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.FILTER_PILL_EXP))){
                    filterConfigEntry.getValue().getFilters().remove(Constants.TOGGLE_FILTERS_CATEGORY);
                    filterConfigEntry.getValue().getFilters().remove(Constants.MEAL_PREFERENCE_CATEGORY);
                }
                if (!Constants.TRUE.equalsIgnoreCase(expDataMap.get(Constants.EXP_CITY_COLLECTION_ENABLE))) {
                    filterConfigEntry.getValue().getFilters().remove(Constants.DPT_COLLECTIONS);
                }
            }
        }
    }

    @Deprecated
    public void modifyFilterConfig(FilterConfigurationV2 filterConfiguration, String funnelSource) {
        //if filterConfiguration is non-null and expDataMap is non-null and does not contain the key ExperimentKeys.<experiment_name>.getKey() or its value is false
        // remove the filter group from the filterConfiguration
        if (filterConfiguration != null && MapUtils.isNotEmpty(filterConfiguration.getFilterPages())) {
            for (Map.Entry<String, FilterPage> filterConfigEntry : filterConfiguration.getFilterPages().entrySet()) {
                LinkedHashMap<String, FilterConfigCategory> filters = filterConfigEntry.getValue().getFilters();
                if(Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource) && filters.get(Constants.TOGGLE_FILTERS_CATEGORY)!=null && MapUtils.isNotEmpty(filters.get(Constants.TOGGLE_FILTERS_CATEGORY).getGroups())) {
                    for(Map.Entry<String, LinkedHashMap<String, FilterConfigDetail>> groupEntry: filters.get(Constants.TOGGLE_FILTERS_CATEGORY).getGroups().entrySet()) {
                        for (Map.Entry<String, FilterConfigDetail> entry : groupEntry.getValue().entrySet()) {
                            entry.getValue().setDescription(null);
                        }
                    }
                }
            }
        }
    }

    public FilterConfigurationV2 getFilterConfigurationV2(CommonModifierResponse commonModifierResponse, FilterCountRequest filterRequest, UserCohort userCohort) {
        FilterConfigurationV2 baseFilterConfig = null;

        if(filterRequest == null || filterRequest.getRequestDetails() == null)
            return null;

        String client = filterRequest.getClient();
        String idContext = filterRequest.getRequestDetails().getIdContext();
        String funnelSource = filterRequest.getRequestDetails().getFunnelSource();
        String trafficSource = null;
        if(filterRequest.getRequestDetails().getTrafficSource() != null){
            trafficSource = filterRequest.getRequestDetails().getTrafficSource().getSource();
        }

        try {
            baseFilterConfig = gson.fromJson(baseFilterSettingsV2, new TypeToken<FilterConfigurationV2>() {
            }.getType());
        } catch (Exception e) {
            logger.error("Error in parsing baseFilterSettingsV2", e);
        }

        String siteDomain = null;
        if(filterRequest.getRequestDetails()!=null && StringUtils.isNotEmpty(filterRequest.getRequestDetails().getSiteDomain())){
            siteDomain = filterRequest.getRequestDetails().getSiteDomain();
        }
        String countryCode = null;
        if(filterRequest.getSearchCriteria()!=null && StringUtils.isNotEmpty(filterRequest.getSearchCriteria().getCountryCode())){
            countryCode = filterRequest.getSearchCriteria().getCountryCode();
        }

        FilterConfigurationV2 modifiedBaseFilterConfig = polyglotHelper.translateFilterPageData(baseFilterConfig,funnelSource);
        String cohort = getUserCohortTag(userCohort);

        boolean isDtPwa = (client.equalsIgnoreCase(CLIENT_DESKTOP) || client.equalsIgnoreCase(CLIENT_PWA));

        if (Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) &&
                Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId())) {
            // Use Case : My Partner (We won't be switching on idContext, and just on the client under the myPartner isolated context)

            if (client.equalsIgnoreCase(CLIENT_DESKTOP)) {
                return filterHelper.getFilterConfigV2(modifiedBaseFilterConfig, modifiedFilterConfigWithPolyglotDataV2(desktopMyPartnerFilterSettingsV2, funnelSource), idContext, cohort, client);
            }else if (client.equalsIgnoreCase(CLIENT_PWA)) {
                return filterHelper.getFilterConfigV2(modifiedBaseFilterConfig, modifiedFilterConfigWithPolyglotDataV2(pwaMyPartnerFilterSettingsV2, funnelSource), idContext, cohort, client);
            }else{
                return filterHelper.getFilterConfigV2(modifiedBaseFilterConfig, null, idContext, cohort, client);
            }

        } else if (!isDtPwa && filterRequest.getRequestDetails() != null && filterRequest.getRequestDetails().isPremium()) {
            // Use Case : Premium
            return filterHelper.getFilterConfigV2(modifiedBaseFilterConfig, modifiedFilterConfigWithPolyglotDataV2(premiumFunnelFilterSettingsV2, funnelSource), idContext, cohort, client);
        } else if (filterRequest.getAlternateBookingInfo() != null && filterRequest.getAlternateBookingInfo().isAlternateBooking()) {
            // Use Case : Alt Booking
            return filterHelper.getFilterConfigV2(modifiedBaseFilterConfig, modifiedFilterConfigWithPolyglotDataV2(desktopAltBookingFilterSettingV2, funnelSource), idContext, cohort, client);
        } else if (Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext)) {
            // Use Case : Corp
            return getCorpFilterConfigurationV2(client, countryCode, modifiedBaseFilterConfig, funnelSource, idContext, cohort);
        }else if (Constants.TRAFFIC_SOURCE_SEO.equalsIgnoreCase(trafficSource) && isDtPwa) {
            // for META
            return getSeoFilterConfigurationV2(client, countryCode, modifiedBaseFilterConfig, funnelSource, idContext, cohort);
        } else if (Constants.TRAFFIC_SOURCE_META.equalsIgnoreCase(trafficSource) && isDtPwa) {
            return getMetaFilterConfigurationV2(client, countryCode, modifiedBaseFilterConfig, funnelSource, idContext, cohort);
        } else if (Constants.TRAFFIC_SOURCE_SEM.equalsIgnoreCase(trafficSource) && isDtPwa) {
            // for SEM
            return getSemFilterConfigurationV2(client, countryCode, modifiedBaseFilterConfig, funnelSource, idContext, cohort);
        }  else if (Utility.isRegionGccOrKsa(siteDomain)) {
            // Use Case : GCC / KSA
            return getGccFilterConfigurationV2(client, modifiedBaseFilterConfig, funnelSource, idContext, cohort);
        }else if (Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource)) {
            // Use Case : Homestay
            return getHomestayFilterConfigurationV2(client, countryCode, modifiedBaseFilterConfig, funnelSource, idContext, cohort);
        } else if (Constants.FUNNEL_SOURCE_DAYUSE.equalsIgnoreCase(funnelSource) && !isDtPwa) {
            // Use Case : DayUse
            return filterHelper.getFilterConfigV2(modifiedBaseFilterConfig, modifiedFilterConfigWithPolyglotDataV2(dayUseFilterSettingsV2,funnelSource), idContext, cohort, client);
        }else{
            // Use Case : Hotels
            return getHotelFilterConfigurationV2(client, countryCode, modifiedBaseFilterConfig, funnelSource, idContext, cohort);
        }
    }

    /**
     * userCohort tag = {searchContext}_{userSegment}_{cityGroup}
     * */
    private String getUserCohortTag(UserCohort userCohort) {
        if (userCohort != null) {
            String searchContext = userCohort.getSearchContext();
            String userSegment = userCohort.getUserSegment();
            String cityGroup = userCohort.getCityGroup();

            StringBuilder sb = new StringBuilder();
            if (StringUtils.isNotEmpty(searchContext)) {
                sb.append(searchContext.toLowerCase());
            }
            if (StringUtils.isNotEmpty(userSegment)) {
                if (sb.length() > 0) sb.append("_");
                sb.append(userSegment.toLowerCase());
            }
            if (StringUtils.isNotEmpty(cityGroup)) {
                if (sb.length() > 0) sb.append("_");
                sb.append(cityGroup.toLowerCase());
            }
            if (sb.length() > 0) {
                return sb.toString();
            }
        }
        return "default";
    }

    private FilterConfiguration getCorpFilterConfiguration(String client, String countryCode, String baseFilterModified, String funnelSource, String idContext){
        String key = client.toUpperCase()+Constants.UNDERSCORE+Constants.CORP_ID_CONTEXT+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.DESKTOP_CORP_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(desktopCorpIntlFilterSetting,funnelSource), idContext);
            case Constants.ANDROID_CORP_INTL:
            case Constants.IOS_CORP_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(appsCorpIntlFilterSetting,funnelSource), idContext);
            case Constants.PWA_CORP_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(pwaCorpIntlFilterSetting,funnelSource), idContext);
            case Constants.DESKTOP_CORP_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(desktopCorpDomFilterSetting,funnelSource), idContext);
            case Constants.ANDROID_CORP_DOM:
            case Constants.IOS_CORP_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(appsCorpDomFilterSetting,funnelSource), idContext);
            case Constants.PWA_CORP_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(pwaCorpDomFilterSetting,funnelSource), idContext);
            default:
                return  filterHelper.getFilterConfig(baseFilterModified, idContext);
        }
    }

    private FilterConfigurationV2 getGccFilterConfigurationV2(String client, FilterConfigurationV2 baseFilterModified, String funnelSource, String idContext, String cohort) {
        String key = client.toUpperCase()+Constants.UNDERSCORE+"GCC";
        switch (key) {
            case Constants.DESKTOP_GCC:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(desktopGCCFilterSettingV2,funnelSource), idContext, cohort, client);
            case Constants.PWA_GCC:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(pwaGCCFilterSettingV2,funnelSource), idContext, cohort, client);
            default:
                return  filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(gccFilterSettingsV2,funnelSource), idContext, cohort, client);
        }
    }


    private FilterConfigurationV2 getCorpFilterConfigurationV2(String client, String countryCode, FilterConfigurationV2 baseFilterModified, String funnelSource, String idContext, String cohort){
        String key = client.toUpperCase()+Constants.UNDERSCORE+Constants.CORP_ID_CONTEXT+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.ANDROID_CORP_INTL:
            case Constants.IOS_CORP_INTL:
            case Constants.PWA_CORP_INTL:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(appsCorpIntlFilterSettingsV2,funnelSource), idContext, cohort, client);
            case Constants.ANDROID_CORP_DOM:
            case Constants.IOS_CORP_DOM:
            case Constants.PWA_CORP_DOM:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(appsCorpDomFilterSettingsV2,funnelSource), idContext, cohort, client);
            case Constants.DESKTOP_CORP_DOM:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(desktopCorpDomFilterSettingV2,funnelSource), idContext, cohort, client);
            case Constants.DESKTOP_CORP_INTL:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(desktopCorpIntlFilterSettingV2,funnelSource), idContext, cohort, client);
            default:
                return  filterHelper.getFilterConfigV2(baseFilterModified, null, idContext, cohort, client);
        }
    }

    private FilterConfigurationV2 getSeoFilterConfigurationV2(String client, String countryCode, FilterConfigurationV2 baseFilterModified, String funnelSource, String idContext, String cohort){
        String key = Constants.TRAFFIC_SOURCE_SEO.toUpperCase()+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.SEO_INTL:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(seoIntlFilterSettingV2,funnelSource), idContext, cohort, client);
            case Constants.SEO_DOM:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(seoDomFilterSettingV2,funnelSource), idContext, cohort, client);
            default:
                return  filterHelper.getFilterConfigV2(baseFilterModified, null,idContext, cohort, client);
        }
    }

    private FilterConfigurationV2 getMetaFilterConfigurationV2(String client, String countryCode, FilterConfigurationV2 baseFilterModified, String funnelSource, String idContext, String cohort) {
        String key = Constants.TRAFFIC_SOURCE_META.toUpperCase()+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.META_INTL:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(metaIntlFilterSettingV2,funnelSource), idContext, cohort, client);
            case Constants.META_DOM:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(metaDomFilterSettingV2,funnelSource), idContext, cohort, client);
            default:
                return  filterHelper.getFilterConfigV2(baseFilterModified, null, idContext, cohort, client);
        }
    }

    private FilterConfigurationV2 getSemFilterConfigurationV2(String client, String countryCode, FilterConfigurationV2 baseFilterModified, String funnelSource, String idContext, String cohort) {
        String key = Constants.TRAFFIC_SOURCE_SEM.toUpperCase()+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.SEM_INTL:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(semIntlFilterSettingV2,funnelSource), idContext, cohort, client);
            case Constants.SEM_DOM:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(semDomFilterSettingV2,funnelSource), idContext, cohort, client);
            default:
                return filterHelper.getFilterConfigV2(baseFilterModified, null, idContext, cohort, client);
        }
    }

    private FilterConfiguration getSeoFilterConfiguration(String client, String countryCode, String baseFilterModified, String funnelSource, String idContext) {
        String key = Constants.TRAFFIC_SOURCE_SEO.toUpperCase()+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.SEO_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(seoIntlFilterSetting,funnelSource), idContext);
            case Constants.SEO_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(seoDomFilterSetting,funnelSource), idContext);
            default:
                return  filterHelper.getFilterConfig(baseFilterModified, idContext);
        }
    }

    private FilterConfiguration getMetaFilterConfiguration(String client, String countryCode, String baseFilterModified, String funnelSource, String idContext) {
        String key = Constants.TRAFFIC_SOURCE_META.toUpperCase()+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.META_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(metaIntlFilterSetting,funnelSource), idContext);
            case Constants.META_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(metaDomFilterSetting,funnelSource), idContext);
            default:
                return  filterHelper.getFilterConfig(baseFilterModified, idContext);
        }
    }

    private FilterConfiguration getSemFilterConfiguration(String client, String countryCode, String baseFilterModified, String funnelSource, String idContext) {
        String key = Constants.TRAFFIC_SOURCE_SEM.toUpperCase()+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.SEM_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(semIntlFilterSetting,funnelSource), idContext);
            case Constants.SEM_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(semDomFilterSetting,funnelSource), idContext);
            default:
                return filterHelper.getFilterConfig(baseFilterModified, idContext);
        }
    }

    private FilterConfiguration getGccFilterConfiguration(String client, String countryCode, String baseFilterModified, String funnelSource, String idContext) {
        String key = client.toUpperCase()+Constants.UNDERSCORE+"GCC";
        switch (key) {
            case Constants.DESKTOP_GCC:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(desktopGCCFilterSetting,funnelSource), idContext);
            case Constants.ANDROID_GCC:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(androidGCCFilterSetting,funnelSource), idContext);
            case Constants.IOS_GCC:
                return filterHelper.getFilterConfig(baseFilterModified,modifiedFilterWithPolyglotData(iosGCCFilterSetting,funnelSource), idContext);
            case Constants.PWA_GCC:
                return filterHelper.getFilterConfig(baseFilterModified,modifiedFilterWithPolyglotData(pwaGCCFilterSetting,funnelSource), idContext);
            default:
                return  filterHelper.getFilterConfig(baseFilterModified, idContext);
        }
    }

    private FilterConfiguration getHotelFilterConfiguration(String client, String countryCode, String baseFilterModified, String funnelSource, String idContext) {
        String key = client.toUpperCase()+Constants.UNDERSCORE+Constants.FUNNEL_SOURCE_HOTELS+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.DESKTOP_HOTELS_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(desktopIntlHotelsFilterSetting,funnelSource), idContext);
            case Constants.ANDROID_HOTELS_INTL:
            case Constants.IOS_HOTELS_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(appsIntlHotelsFilterSetting,funnelSource), idContext);
            case Constants.PWA_HOTELS_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(pwaIntlHotelsFilterSetting,funnelSource), idContext);
            case Constants.DESKTOP_HOTELS_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(desktopDomHotelsFilterSetting,funnelSource), idContext);
            case Constants.ANDROID_HOTELS_DOM:
            case Constants.IOS_HOTELS_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(appsDomHotelsFilterSetting,funnelSource), idContext);
            case Constants.PWA_HOTELS_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(pwaDomHotelsFilterSetting,funnelSource), idContext);
            default:
                return  filterHelper.getFilterConfig(baseFilterModified, idContext);
        }
    }

    private FilterConfigurationV2 getHotelFilterConfigurationV2(String client, String countryCode, FilterConfigurationV2 baseFilterModified, String funnelSource, String idContext, String cohort) {
        String key = client.toUpperCase()+Constants.UNDERSCORE+Constants.FUNNEL_SOURCE_HOTELS+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.ANDROID_HOTELS_INTL:
            case Constants.IOS_HOTELS_INTL:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(appsIntlFilterSettingsV2,funnelSource), idContext, cohort, client);
            case Constants.ANDROID_HOTELS_DOM:
            case Constants.IOS_HOTELS_DOM:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(appsDomFilterSettingsV2,funnelSource), idContext, cohort, client);
            case Constants.DESKTOP_HOTELS_DOM:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(desktopDomHotelsFilterSettingV2,funnelSource), idContext, cohort, client);
            case Constants.DESKTOP_HOTELS_INTL:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(desktopIntlHotelsFilterSettingV2,funnelSource), idContext, cohort, client);
            case Constants.PWA_HOTELS_DOM:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(pwaDomHotelsFilterSettingV2,funnelSource), idContext, cohort, client);
            case Constants.PWA_HOTELS_INTL:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(pwaIntlHotelsFilterSettingV2,funnelSource), idContext, cohort, client);
            default:
                return  filterHelper.getFilterConfigV2(baseFilterModified, null, idContext, cohort, client);
        }
    }

    private FilterConfigurationV2 getHomestayFilterConfigurationV2(String client, String countryCode, FilterConfigurationV2 baseFilterModified, String funnelSource, String idContext, String cohort) {
        String key = client.toUpperCase()+Constants.UNDERSCORE+Constants.FUNNEL_SOURCE_HOTELS+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.ANDROID_HOTELS_INTL:
            case Constants.IOS_HOTELS_INTL:
            case Constants.ANDROID_HOTELS_DOM:
            case Constants.IOS_HOTELS_DOM:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(appsHomestayFilterSettingsV2,funnelSource), idContext, cohort, client);
            case Constants.DESKTOP_HOTELS_DOM:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(desktopDomHomestayFilterSettingV2,funnelSource), idContext, cohort, client);
            case Constants.DESKTOP_HOTELS_INTL:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(desktopIntlHomestayFilterSettingV2,funnelSource), idContext, cohort, client);
            case Constants.PWA_HOTELS_DOM:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(pwaDomHomestayFilterSettingV2,funnelSource), idContext, cohort, client);
            case Constants.PWA_HOTELS_INTL:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(pwaIntlHomestayFilterSettingV2,funnelSource), idContext, cohort, client);
            default:
                return  filterHelper.getFilterConfigV2(baseFilterModified, null, idContext, cohort, client);
        }
    }

    private FilterConfiguration getHomestayFilterConfiguration(String client, String countryCode, String baseFilterModified, String funnelSource, String idContext) {
        String key = client.toUpperCase()+Constants.UNDERSCORE+Constants.FUNNEL_SOURCE_HOMESTAY+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.DESKTOP_HOMESTAY_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(desktopIntlHomestayFilterSetting,funnelSource), idContext);
            case Constants.ANDROID_HOMESTAY_INTL:
            case Constants.IOS_HOMESTAY_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(appsIntlHomestayFilterSetting,funnelSource), idContext);
            case Constants.PWA_HOMESTAY_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(pwaIntlHomestayFilterSetting,funnelSource), idContext);
            case Constants.DESKTOP_HOMESTAY_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(desktopDomHomestayFilterSetting,funnelSource), idContext);
            case Constants.ANDROID_HOMESTAY_DOM:
            case Constants.IOS_HOMESTAY_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(appsDomHomestayFilterSetting,funnelSource), idContext);
            case Constants.PWA_HOMESTAY_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(pwaDomHomestayFilterSetting,funnelSource), idContext);
            default:
                return  filterHelper.getFilterConfig(baseFilterModified, idContext);
        }
    }

    /**
     * this method adds meal_preferences and toggle_filters category to base filter settings HTL-38235
     **/
    private void modifyFilterConfigForPillsExperiment(String funnelSource, FilterConfiguration fConfig) {
        if (StringUtils.isNotBlank(mealPrefConfig) && StringUtils.isNotBlank(toggleFilterConfigHomestay) && StringUtils.isNotBlank(toggleFilterConfig)) {
            mealPrefConfigCategory = gson.fromJson(mealPrefConfig, new TypeToken<FilterConfigCategory>() {
            }.getType());

            if (Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource)) {
                toggleConfigCategory = gson.fromJson(toggleFilterConfigHomestay, new TypeToken<FilterConfigCategory>() {
                }.getType());
            } else {
                toggleConfigCategory = gson.fromJson(toggleFilterConfig, new TypeToken<FilterConfigCategory>() {
                }.getType());
            }
            if (fConfig != null && fConfig.getFilters() != null) {
                fConfig.getFilters().put(Constants.MEAL_PREFERENCE_CATEGORY, mealPrefConfigCategory);
                fConfig.getFilters().put(Constants.TOGGLE_FILTERS_CATEGORY, toggleConfigCategory);
            }
        }
    }

    public String modifiedFilterWithPolyglotData(String filter,String funnelSource){
        FilterConfiguration fConfig = null;
        fConfig = gson.fromJson(filter, new TypeToken<FilterConfiguration>() {
        }.getType());

        polyglotHelper.translateFilterConfig(fConfig,funnelSource);
        String modifiedFilter = gson.toJson(fConfig);
        return modifiedFilter;
    }

    public FilterConfigurationV2 modifiedFilterConfigWithPolyglotDataV2(String filter,String funnelSource){
        FilterConfigurationV2 fConfig = null;

        try {
            fConfig = gson.fromJson(filter, new TypeToken<FilterConfigurationV2>() {
            }.getType());
        } catch (Exception e) {
            logger.error("Error in parsing filter", e);
        }
        polyglotHelper.translateFilterPageData(fConfig,funnelSource);
        return fConfig;
    }

    public Map<String, List<Filter>> getCompositeFilterConfig() {
        return compositeFilterMapping;
    }

}
