package com.mmt.hotels.clientgateway.transformer.response.android;

import com.mmt.hotels.clientgateway.constants.DuplicateBookingKey;
import com.mmt.hotels.clientgateway.response.ConsentData;
import com.mmt.hotels.clientgateway.response.InitApprovalResponse;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.InitiateApprovalResponseTransformer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class InitiateApprovalResponseTransformerAndroid extends InitiateApprovalResponseTransformer {

    @Autowired
    private PolyglotService polyglotService;
    @Override
    public InitApprovalResponse processResponse(CGServerResponse initAppResponseHES) {
        return super.processResponse(initAppResponseHES);
    }
    @Override
    public void populateClientSpecificFields(ConsentData consentData) {
        consentData.setIconUrl(polyglotService.getTranslatedData(DuplicateBookingKey.DUPLICATE_BOOKING_ICON_URL_MOBILE.name()));
    }
}
