package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.fasterxml.jackson.core.type.TypeReference;
import com.gommt.hotels.orchestrator.model.objects.FetchedWindow;
import com.gommt.hotels.orchestrator.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.model.response.content.shortstays.ShortStaysZoneResult;
import com.gommt.hotels.orchestrator.model.response.da.CancellationTimeline;
import com.gommt.hotels.orchestrator.model.response.listing.*;
import com.gommt.hotels.orchestrator.model.response.content.PropertyChainInfo;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ibm.icu.text.NumberFormat;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.enums.Persuasions;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.Coupon;
import com.mmt.hotels.clientgateway.response.GroupPrice;
import com.mmt.hotels.clientgateway.response.LocationDetail;
import com.mmt.hotels.clientgateway.response.PixelUrlConfig;
import com.mmt.hotels.clientgateway.response.dayuse.Slot;
import com.mmt.hotels.clientgateway.response.dayuse.SlotDetail;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.listingmap.ListingMapResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.*;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.*;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.ShortstaysListingMapResponseTransformer;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.StaticPrice;
import com.mmt.hotels.model.enums.SectionsType;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.request.matchmaker.InputHotel;
import com.mmt.hotels.model.request.matchmaker.LatLngObject;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.model.request.matchmaker.Tags;
import com.mmt.hotels.model.response.dayuse.MissingSlotDetail;

import com.mmt.hotels.model.response.shortstays.ShortstaysZoneResult;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.hotels.orchestrator.models.mypartner.MarkUp;
import com.mmt.hotels.orchestrator.models.mypartner.MarkUpDetails;
import com.mmt.hotels.util.PromotionalOfferType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.GROUP_PRICE_TEXT_ONE_NIGHT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SAVING_PERC_TEXT;
import static com.mmt.hotels.clientgateway.util.DateUtil.MMDDYYYY;
import static com.mmt.hotels.clientgateway.util.Utility.*;
import static com.mmt.hotels.model.response.mypartner.MarkUpType.PERCENTAGE;

@Component
@SuppressWarnings("unchecked")
abstract public class OrchSearchHotelsResponseTransformer {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrchSearchHotelsResponseTransformer.class);

    @Autowired
    CommonResponseTransformer commonResponseTransformer;

    @Autowired
    Utility utility;

    @Autowired
    PolyglotService polyglotService;

    @Value("${family.friendly.tracking.text}")
    private String familyFriendlyTracking;

    @Autowired
    private DateUtil dateUtil;

    @Autowired
    CommonHelper commonHelper;

    @Value("${mybiz.assured.url}")
    private String myBizAssuredUrl;

    @Value("${listing.myBiz.Assured.tooltip.iconType}")
    private String myBizToolTipIconType;

    @Value("${bank.coupon.generic.icon}")
    private String genericBankIcon;

    @Value("${filter.conditions}")
    private String filterConditionsConfig;

    @Value("${persuasion.place.holders.to.show}")
    private String placeHoldersToShowConfig;

    @Value("${min.limit.similar.hotels}")
    private int minSimilarHotels;

    @Value("${listing.driving.duration.buckets}")
    protected String listingDrivingDurationBucket;

    @Value("${special.fare.persuasion.style}")
    protected String specialFarePersuasionConfig;

    @Value("${negotiated.rates.delayed.confirmation.no.of.hours}")
    protected int noOfHoursForConfirmation;

    @Autowired
    ObjectMapperUtil objectMapperUtil;

    @Autowired
    PersuasionUtil persuasionUtil;

    @Autowired
    PricingEngineHelper pricingEngineHelper;

    @Autowired
    MobConfigHelper mobConfigHelper;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    @Value("${hotel.level.app.deeplink}")
    protected String hotelLevelAppDeepLink;

    @Value("${hotel.level.sharing.url}")
    protected String hotelLevelSharingUrl;

    @Value("${root.level.sharing.url}")
    protected String rootLevelSharingUrl;

    @Value("${root.level.deeplink.url}")
    protected String rootLevelDeeplink;

    @Value("${root.level.deeplink.url.global}")
    protected String rootLevelDeeplinkGlobal;

    @Value("${detail.deep.link.url}")
    protected String basicDetailDeeplink;

    @Value("${detail.deep.link.url.global}")
    protected String basicDetailDeeplinkGlobal;

    @Value("#{'${pixel.tracking.locations}'.split(',')}")
    private Set<String> pixelTrackingLocations;

    @Value("${listing.deep.link.url}")
    protected String listinglDeeplink;

    @Value("${listing.app.deep.link.url}")
    protected String listingApplDeeplink;

    @Value("${listing.deep.link.url.global}")
    protected String listinglDeeplinkGlobal;

    @Autowired
    ShortstaysListingMapResponseTransformer shortstaysResponseTransformer;

    protected Map<String, Map<String, PersuasionData>> specialFarePersuasionConfigMap;
    protected Map<String, String> listingDrivingDurationBucketMap;

    public MissingSlotDetail missingSlotDetails = null;

    public int thresholdForSlashedAndDefaultHourPrice = 0;

    protected static Gson gson = new Gson();

    @PostConstruct
    public void init() {
        missingSlotDetails = commonConfigConsul.getMissingSlotDetails();
        thresholdForSlashedAndDefaultHourPrice = commonConfigConsul.getThresholdForSlashedAndDefaultHourPrice();

        listingDrivingDurationBucketMap = gson.fromJson(listingDrivingDurationBucket, HashMap.class);
        specialFarePersuasionConfigMap = gson.fromJson(specialFarePersuasionConfig, new TypeToken<Map<String, Map<String, PersuasionData>>>() {
        }.getType());
    }

    protected abstract BottomSheet buildBottomSheet(PersonalizedSectionDetails perResponse);

    public abstract void addPersuasionHoverData(Hotel hotel, HotelDetails hotelEntity, CancellationTimeline cancellationTimeline, PriceDetail displayFare, ListingSearchRequest listingSearchRequest);

    public abstract void addLocationPersuasionToHotelPersuasions(Hotel hotel, List<String> locationPersuasion, LinkedHashSet<String> facilities, ListingSearchRequest searchHotelsRequest, boolean enableAmenities, boolean sectionMyBizSimilarToDirectHtl, String dayUsePersuasionsText, TransportPoi nearestGroundTransportPoi, String drivingTimeText, LocationDetails locusData, boolean homestayV2Flow);

    protected abstract String buildBGColor(String section, String orientation, String cardType);

    protected abstract void addBookingConfirmationPersuasion(HotelDetails hotelEntity);

    public abstract MyBizStaticCard buildStaticCard(String section, List<HotelDetails> hotels, String detailDeepLinkUrl);

    public abstract HotelCard buildQuickBookCard(QuickBookInfo quickBookInfo);

    public SearchHotelsResponse convertSearchHotelsResponse(ListingResponse listingResponse, SearchHotelsRequest searchHotelsRequest, CommonModifierResponse commonModifierResponse) {

        SearchHotelsResponse searchHotelsResponse = new SearchHotelsResponse();
        //TODO - Check for multi currency
        searchHotelsResponse.setCurrency(searchHotelsRequest.getSearchCriteria().getCurrency());
        searchHotelsResponse.setHotelCount(listingResponse.getHotelCount());
        searchHotelsResponse.setLastFetchedHotelCategory(listingResponse.getLastFetchedHotelCategory());
        searchHotelsResponse.setLastHotelCategory(listingResponse.getLastFetchedHotelCategory());
        if (searchHotelsRequest.getExpData() != null) {
            LinkedHashMap<String, String> expDataMap = commonModifierResponse.getExpDataMap();
            if (expDataMap.containsKey("nearbyFixes") && Boolean.parseBoolean(expDataMap.get("nearbyFixes")) || expDataMap.containsKey("emptyShopSolution") && Boolean.parseBoolean(expDataMap.get("emptyShopSolution"))) {
                searchHotelsResponse.setLastFetchedHotelCategory(listingResponse.getLastFetchedHotelCategory());
            }
        }

        if (SectionsType.FILTER_REMOVAL.name().equalsIgnoreCase(searchHotelsResponse.getLastFetchedHotelCategory())) {
            LOGGER.warn("FILTER_REMOVAL category found in lastFetchedHotelCategory");
            if (CollectionUtils.isNotEmpty(searchHotelsRequest.getFilterCriteria())) {
                List<Filter> filters = searchHotelsRequest.getFilterCriteria().stream().filter(e -> e.getFilterGroup().equals(FilterGroup.STAR_RATING)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(filters)) {
                    searchHotelsResponse.setFilterRemovedCriteria(filters);
                }
            }
        }

        searchHotelsResponse.setLastHotelId(listingResponse.getLastHotelId());
        searchHotelsResponse.setLastFetchedWindowInfo(listingResponse.getLastFetchedWindowInfo());
        searchHotelsResponse.setRankingHotelCount(String.valueOf(listingResponse.getHotelCount())); //TODO Fix this
        FetchedWindow fetchedWindow = buildFetchedWindow(listingResponse.getLastFetchedWindowInfo());
        searchHotelsResponse.setLastHotelIndex(fetchedWindow != null ? String.valueOf(fetchedWindow.getEndIndex()) : "");
        if (listingResponse.getLocation() != null) {
            LocationDetails locationDetails = listingResponse.getLocation();
            searchHotelsResponse.setLocationDetail(new LocationDetail(locationDetails.getId(), locationDetails.getCityName(), locationDetails.getType(), locationDetails.getCountryId(), locationDetails.getCountryName()));
            LocationDetails cityLocationDetails = getCityLocationDetail(listingResponse);
            searchHotelsResponse.setCityLocationDetail(new LocationDetail(cityLocationDetails.getCityId(), cityLocationDetails.getCityName(), "city", null, null));
        }

        if (searchHotelsRequest.getRequestDetails() != null && SHORTSTAYS_FUNNEL.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource()) && commonResponseTransformer.isEligibleForNearbyFlow(searchHotelsRequest.getDeviceDetails())) {
            if (CollectionUtils.isNotEmpty(listingResponse.getPersonalizedSections())) {
                String displayName = listingResponse.getPersonalizedSections().get(0).getHeading();
                if (searchHotelsResponse.getLocationDetail() != null && displayName != null) {
                    searchHotelsResponse.getLocationDetail().setDisplayName(displayName.replace("Showing ", ""));
                }
            }
        }

        //TODO: Test the logic
        boolean noMoreHotels = (listingResponse.getLastPage() != null && listingResponse.getLastPage()) || StringUtils.isEmpty(listingResponse.getLastHotelId());
        searchHotelsResponse.setNoMoreHotels(noMoreHotels);
        String idContext = searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getIdContext() : null;
        searchHotelsResponse.setPersonalizedSections(buildPersonalizedSections(listingResponse.getPersonalizedSections(), utility.getExpDataMap(searchHotelsRequest.getExpData()), idContext, searchHotelsRequest, commonModifierResponse, listingResponse.getLocation(), listingResponse.getMarkUpDetails()));
        searchHotelsResponse.setSortCriteria(buildSortCriteria(listingResponse.getSortCriteria()));
        searchHotelsResponse.setExpData(buildExpData(commonModifierResponse));
        searchHotelsResponse.setExpVariantKeys(StringUtils.isNotBlank(searchHotelsRequest.getExpVariantKeys()) ? searchHotelsRequest.getExpVariantKeys() : null);

        searchHotelsResponse.setHydraSegments(getHydraSegments(commonModifierResponse));
        if (fetchedWindow != null && !"RANKING".equalsIgnoreCase(fetchedWindow.getSectionSequenceUsedToBuildBitset())) {
            searchHotelsResponse.setSectionsType(fetchedWindow.getSectionSequenceUsedToBuildBitset());
        }
        if (StringUtils.isNotBlank(listingResponse.getSharingUrl())) {
            searchHotelsResponse.setSharingUrl(listingResponse.getSharingUrl());
        }

        searchHotelsResponse.setHotelCount(listingResponse.getHotelCount());
        searchHotelsResponse.setHotelCountInCity(0);
        String luckyUserStatus = listingResponse.getLuckyUserContext()!=null && StringUtils.isNotEmpty(listingResponse.getLuckyUserContext().getLuckyUserStatus())? listingResponse.getLuckyUserContext().getLuckyUserStatus(): "";
        searchHotelsResponse.setLuckyUserContext(utility.logLuckyUserData(commonModifierResponse, luckyUserStatus, "LISTING"));
        if(listingResponse.isGuestHouseAvailable()) {
            searchHotelsResponse.setGuestHouseAvailable(listingResponse.isGuestHouseAvailable());
        }
        String listingSharingUrl = prepareListingSharingUrl(searchHotelsRequest,rootLevelSharingUrl ,true, true, null);
        listingSharingUrl += getRscValue(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest.getExpDataMap());
        searchHotelsResponse.setSharingUrl(listingSharingUrl);
        String recentDeeplink = rootLevelDeeplink;
        if (searchHotelsRequest.getSearchCriteria().getUserGlobalInfo() != null && "GLOBAL".equalsIgnoreCase(searchHotelsRequest.getSearchCriteria().getUserGlobalInfo().getEntityName())) {
            recentDeeplink = rootLevelDeeplinkGlobal;
        }
        searchHotelsResponse.setRecentDeepLink(prepareRecentDeeplink(searchHotelsRequest,recentDeeplink));
        searchHotelsResponse.setPremiumAvailable(listingResponse.isPremiumAvailable());

        Map<String, String> trackingMap = listingResponse.getTrackingMap();
        if(commonModifierResponse != null && (REGION_AE).equalsIgnoreCase(commonModifierResponse.getRegion())) {
            String businessOwnerID = commonResponseTransformer.getEvarBasedOnCountryAndRegion(commonModifierResponse.getUserCountry(), searchHotelsRequest.getSearchCriteria().getCountryCode());
            if (StringUtils.isNotEmpty(businessOwnerID)) {
                if (MapUtils.isEmpty(trackingMap)) {
                    trackingMap = new HashMap<>();
                }
                if (trackingMap.containsKey(EVAR_126)) {
                    trackingMap.put(EVAR_126, trackingMap.get(EVAR_126).toString() + "|" + businessOwnerID);
                } else {
                    trackingMap.put(EVAR_126, businessOwnerID);
                }
            }
        }
        searchHotelsResponse.setTrackingMap(commonResponseTransformer.buildTrackingMap(trackingMap));
        //searchHotelsResponse.setCurrency(listingResponse.getCurrency());
        //TODO - Not found in Android contract
        /*
            searchHotelsResponse.setUsradid(listingResponse.getUsradid());
            searchHotelsResponse.setUserLoyaltyStatus(listingResponse.get());
            if (CollectionUtils.isNotEmpty(listingResponse.getPaxDetailsList())) {
                searchHotelsResponse.setPaxDetails(listingResponse.getPaxDetailsList());
            }
            if (StringUtils.isNotBlank(listingResponse.getListingDeepLinkWithoutFilters())) {
                //set listingDeepLinkWithoutFilters as recentDeepLink to pass to the client
                searchHotelsResponse.setRecentDeepLink(listingResponse.getListingDeepLinkWithoutFilters());
            }
        */

        if (CollectionUtils.isNotEmpty(listingResponse.getPaxDetails())) {
            searchHotelsResponse.setPaxDetails(buildPaxDetails(listingResponse.getPaxDetails()));
        }

        //TODO - myPartner/myBiz case
        /*
            searchHotelsResponse.setUserFareHold(listingResponse.isUserFareHold());
            if (commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && TRUE.equals(commonModifierResponse.getExpDataMap().get(APPLY_IN_POLICY_COR_FILTER_EXP)) && listingResponse.isInPolicyFilterRemoved()) {
                List<Filter> filters = searchHotelsRequest.getFilterCriteria().stream().filter(e -> e.getFilterGroup().equals(FilterGroup.IN_POLICY)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(filters)) {
                    searchHotelsResponse.setFilterRemovedCriteria(filters);
                    LOGGER.warn("Added IN_POLICY filter to filterRemovedCriteria" + searchHotelsResponse.getFilterRemovedCriteria());
            }
        */
        searchHotelsResponse.setUserFareHold(listingResponse.isUserFareHold());
        searchHotelsResponse.setUserLoyaltyStatus(listingResponse.getUserLoyaltyStatus());

        PixelUrlConfig pixelUrlData = createPixelUrlData(listingResponse, commonModifierResponse, searchHotelsRequest);
        if(pixelUrlData != null){
            searchHotelsResponse.setPixelUrl(commonResponseTransformer.buildPixelUrl(pixelUrlData));
        }

        if(listingResponse.getPartnerModificationUrl() !=null){
            searchHotelsResponse.setPartnerModificationUrl(listingResponse.getPartnerModificationUrl());
        }

        return searchHotelsResponse;
    }

    private PixelUrlConfig createPixelUrlData(ListingResponse listingResponse, CommonModifierResponse commonModifierResponse, SearchHotelsRequest searchHotelsRequest) {
        if(Utility.isGccOrKsa() || Utility.isMyBizRequest() || !utility.isB2CFunnel() || listingResponse == null || listingResponse.getLocation() == null || listingResponse.getLocation().getId() == null || !pixelTrackingLocations.contains(listingResponse.getLocation().getId())){
            return null;
        }
        SearchHotelsCriteria searchHotelsCriteria = (searchHotelsRequest != null && searchHotelsRequest.getSearchCriteria() != null) ? searchHotelsRequest.getSearchCriteria() : null;
        return new PixelUrlConfig(listingResponse.getCurrency(), commonModifierResponse != null ? commonModifierResponse.getLanguage() : null, listingResponse.getLocation().getCityName(), null, null, searchHotelsCriteria != null ? searchHotelsCriteria.getCheckIn() : null, searchHotelsCriteria != null ? searchHotelsCriteria.getCheckOut() : null, searchHotelsCriteria != null && searchHotelsCriteria.getRoomStayCandidates() != null && !searchHotelsCriteria.getRoomStayCandidates().isEmpty() ? getGuestCountForPixelUrl(searchHotelsCriteria.getRoomStayCandidates().get(0)) : null, commonModifierResponse != null ? commonModifierResponse.getUserCountry() : null, commonModifierResponse != null ? commonModifierResponse.getDeviceId() : null, searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getJourneyId() : null);
    }

    private LocationDetails getCityLocationDetail(ListingResponse listingResponse) {
        for (PersonalizedSectionDetails personalizedSectionDetails : listingResponse.getPersonalizedSections()) {
            for (HotelDetails hotel : personalizedSectionDetails.getHotels()) {
                return hotel.getLocation();
            }
        }
        return new LocationDetails();
    }

    private int getGuestCountForPixelUrl(com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate) {
        int adultCount = roomStayCandidate.getAdultCount() != null ?  roomStayCandidate.getAdultCount() : 0;
        int childCount = roomStayCandidate.getChildAges() != null ? roomStayCandidate.getChildAges().size() : 0;
        return adultCount + childCount;
    }

    private List<String> getHydraSegments(CommonModifierResponse commonModifierResponse) {
        List<String> segments = null;
        if (commonModifierResponse != null && commonModifierResponse.getHydraResponse() != null && CollectionUtils.isNotEmpty(commonModifierResponse.getHydraResponse().getHydraMatchedSegment())) {
            segments = new ArrayList<>(commonModifierResponse.getHydraResponse().getHydraMatchedSegment());
        }
        return segments;
    }

    private Map<String, String> buildExpData(CommonModifierResponse commonModifierResponse) {
        return commonModifierResponse != null ? commonModifierResponse.getExpDataMap() : new HashMap<>();
    }

    private SortCriteria buildSortCriteria(com.gommt.hotels.orchestrator.model.objects.SortCriteria criteria) {
        return criteria == null ? null : new SortCriteria(criteria.getField(), criteria.getOrder());
    }


    private List<PersonalizedSection> buildPersonalizedSections(List<PersonalizedSectionDetails> list, Map<String, String> expDataMap, String idContext, SearchHotelsRequest searchHotelsRequest, CommonModifierResponse commonModifierResponse, LocationDetails locusData, MarkUpDetails markUpDetails) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<PersonalizedSection> personalizedSections = new ArrayList<>();
        list.forEach(perResponse -> {
            PersonalizedSection personalizedSection = new PersonalizedSection();
            personalizedSection.setName(perResponse.getName());

            personalizedSection.setShowIndex(commonResponseTransformer.canShowIndex(perResponse.getName(), expDataMap));

            if (checkIfHorizontalSection(perResponse, commonModifierResponse) && null != perResponse.getHeading())
                personalizedSection.setHeading(HG_HORIZONTAL_HEADING);
            else
                personalizedSection.setHeading(perResponse.getHeading());

            //TODO - myPartner/MyBiz case
            /*
                if (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null && Utility.isCorpBudgetHotelFunnel(searchHotelsRequest.getRequestDetails().getFunnelSource())) {
                    personalizedSection.setStaticCard(buildStaticCard(perResponse.getName(), perResponse.getHotels()));
                }

                if (inPolicyFilterRemoved) {
                    if (MYBIZ_RECOMMENDED_INTL_SECTION.equalsIgnoreCase(personalizedSection.getName()))
                        personalizedSection.setHeading(polyglotService.getTranslatedData(IN_POLICY_FILTER_REMOVED_HEADING_MYBIZ_ASSURED));
                    else
                        personalizedSection.setHeading(polyglotService.getTranslatedData(IN_POLICY_FILTER_REMOVED_HEADING));
                }
             */

            //ShortStay funnel will not have a subheading
            if (searchHotelsRequest.getRequestDetails() != null && (SHORTSTAYS_FUNNEL.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource()))
                    && commonResponseTransformer.isEligibleForNearbyFlow(searchHotelsRequest.getDeviceDetails())) {
                personalizedSection.setSubHeading(null);
            } else {
                personalizedSection.setSubHeading(perResponse.getSubHeading());
            }

            if (searchHotelsRequest.getRequestDetails() != null && isGroupBookingFunnel(searchHotelsRequest.getRequestDetails().getFunnelSource())) {
                personalizedSection.setHeadingVisible(perResponse.isHeadingVisible());
            }
            if (StringUtils.isNotBlank(personalizedSection.getName()) && Constants.NOT_MYBIZ_ASSURED_SHOWN.equalsIgnoreCase(personalizedSection.getName())) {
                overridePersonalizedSectionHeadingForDirectHotelSearch(searchHotelsRequest, personalizedSection);
                buildFilterCardForMyBizAndNonMyBizProperties(perResponse, personalizedSection);
            }
            if (StringUtils.isNotBlank(personalizedSection.getName()) && personalizedSection.getName().equalsIgnoreCase(Constants.MY_BIZ_ASSURED_SECTION) && DOM_COUNTRY.equalsIgnoreCase(searchHotelsRequest.getSearchCriteria().getCountryCode())) {
                personalizedSection.setToolTip(buildMyBizAssuredToolTip());
            }

            String orientation = buildOrientation(perResponse, expDataMap);
            personalizedSection.setHotels(buildPersonalizedHotels(perResponse.getHotels(), expDataMap, searchHotelsRequest, personalizedSection.getName(), commonModifierResponse, locusData, markUpDetails));
            if (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null && Utility.isCorpBudgetHotelFunnel(searchHotelsRequest.getRequestDetails().getFunnelSource())) {
                String detailDeepLinkUrl = personalizedSection.getHotels().get(0).getDetailDeeplinkUrl();
                personalizedSection.setStaticCard(buildStaticCard(perResponse.getName(), perResponse.getHotels(), detailDeepLinkUrl));
            }
            personalizedSection.setHotelCount(perResponse.getHotelCount());
            personalizedSection.setOrientation(orientation);
            personalizedSection.setFooterDetails(perResponse.getFooterDetails());
            personalizedSection.setTopHeaderText(perResponse.getTopHeaderText());
            personalizedSection.setPremium(perResponse.isPremium());

            if (perResponse.getMinHotelToShow() > 0)
                personalizedSection.setMinHotelsToShow(perResponse.getMinHotelToShow());

            //Adding hotelCardType, seeMoreCTA, minHotelsToShow node here based newListingUi exp
            if (StringUtils.isNotEmpty(perResponse.getHotelCardType()))
                personalizedSection.setHotelCardType(perResponse.getHotelCardType().toLowerCase());
            if (perResponse.getMinHotelToShow() != 0) {
                personalizedSection.setMinHotelsToShow(perResponse.getMinHotelToShow());
            }

            //TODO - Copy logic from Orchestrator and ADD it here
            /*
            if (StringUtils.isNotEmpty(perResponse.getSeeMoreCTA())) {
                personalizedSection.setSeeMoreCTA(perResponse.getSeeMoreCTA());
            }
             */
            personalizedSection.setShowMore(false);
            personalizedSection.setCardInsertionAllowed(getCardInsertionAllowedValue(perResponse, idContext));


            if (StringUtils.isNotBlank(perResponse.getName())) {
                // In case number of items to be shown for Recently Booked and Recently Clicked section names is to be set as custom value from pms
                if (commonModifierResponse.getExtendedUser() != null && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId())
                        && mobConfigHelper.getMyPartnerSectionListCount().containsKey(perResponse.getName())) {
                    personalizedSection.setMinItemsToShow(mobConfigHelper.getMyPartnerSectionListCount().get(perResponse.getName()));
                } else if (mobConfigHelper.getCorpSectionListCount().containsKey(perResponse.getName())) {
                    personalizedSection.setMinItemsToShow(mobConfigHelper.getCorpSectionListCount().getOrDefault(perResponse.getName(), 5));
                }
                if (CollectionUtils.isNotEmpty(perResponse.getHotels()) && personalizedSection.getMinItemsToShow() != null && perResponse.getHotels().size() > personalizedSection.getMinItemsToShow()
                        && (mobConfigHelper.getCorpSectionListCount().containsKey(perResponse.getName()) || mobConfigHelper.getMyPartnerSectionListCount().containsKey(perResponse.getName()))
                        && !HORIZONTAL.equalsIgnoreCase(personalizedSection.getOrientation()))
                    personalizedSection.setShowMore(true);
            }
            personalizedSection.setSectionBG(buildBGColor(perResponse.getName(), personalizedSection.getOrientation(), personalizedSection.getHotelCardType()));

            updateHotelCardType(searchHotelsRequest, commonModifierResponse, perResponse, personalizedSection);
            personalizedSections.add(personalizedSection);
        });

        //TODO - myPartner/MyBiz case
        /*
        personalizedSection.setMyBizSimilarHotel(perResponse.getMyBizSimilarToDirectHotel());
        if (perResponse.isMyBizAssuredRecommended()) {
            personalizedSection.setSectionFeatures(getMybizSimilarHotelsFeatures());
        }*/

        return personalizedSections;
    }


    /**
     * @param searchHotelsRequest can be null when called from buildComparatorResponse in StaticDetailResponseTransformer
     *                            adding null check to searchHotelsRequest is mandatory to avoid NPE
     * @param sectionName         Hotels section name
     * @throws NullPointerException if null checks are not added for searchHotelsRequest
     */
    public List<Hotel> buildPersonalizedHotels(List<HotelDetails> hotelList, Map<String, String> expDataMap, SearchHotelsRequest searchHotelsRequest, String sectionName, CommonModifierResponse commonModifierResponse, LocationDetails locusData, MarkUpDetails markUpDetails) {
        if (CollectionUtils.isEmpty(hotelList)) {
            return null;
        }

        List<Hotel> hotels = new ArrayList<>();
        String currency = searchHotelsRequest.getSearchCriteria() != null ? searchHotelsRequest.getSearchCriteria().getCurrency() : DEFAULT_CUR_INR;
        boolean isMyPartnerRequest = (commonModifierResponse != null) && (commonModifierResponse.getExtendedUser() != null) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
        boolean isCallToBookV2Applicable = MapUtils.isNotEmpty(expDataMap) && utility.isExperimentValid(expDataMap, callToBook, 4);
        String idContext = searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getIdContext() : null;
        boolean isDomRequest = DOM_COUNTRY.equalsIgnoreCase(searchHotelsRequest.getSearchCriteria().getCountryCode());
        String funnelSource = searchHotelsRequest.getRequestDetails() != null && StringUtils.isNotBlank(searchHotelsRequest.getRequestDetails().getFunnelSource()) ? searchHotelsRequest.getRequestDetails().getFunnelSource() : "";
        final boolean[] odd = {true};
        long startTime = new Date().getTime();

        //RSC VALUE FOR DEEP-LINKS
        String rscValue;
        List<RoomStayCandidate> distributedRoomStayCandidateList;
        if (utility.isDistributeRoomStayCandidates(searchHotelsRequest.getSearchCriteria().getRoomStayCandidates(), searchHotelsRequest.getExpDataMap())) {
            rscValue = utility.buildRscValue(searchHotelsRequest.getSearchCriteria().getRoomStayCandidates());
            distributedRoomStayCandidateList = utility.buildRoomStayDistribution(searchHotelsRequest.getSearchCriteria().getRoomStayCandidates(), searchHotelsRequest.getExpDataMap());
        } else {
            distributedRoomStayCandidateList = Collections.emptyList();
            rscValue = EMPTY_STRING;
        }

        boolean isDirectSearch = isDirectSearch(searchHotelsRequest);
        hotelList.forEach(hotelEntity -> {
            try {
                Hotel hotel = new Hotel();
                hotel.setGroupBookingHotel(hotelEntity.isGroupBookingHotel());
                hotel.setGroupBookingPrice(hotelEntity.isGroupBookingPrice());
                hotel.setStaticPrice(buildStaticPriceABO(hotelEntity.getStaticPrice()));
                hotel.setIsABO(hotelEntity.getOmnitureFlags().isAbo());
                hotel.setShowCallToBook(hotelEntity.isShowCallToBook());
                hotel.setLocationPersuasion(hotelEntity.getLocationPersuasions());
                hotel.setTrackingMap(hotelEntity.getTrackingMap());
                hotel.setShowSimilarChainProperty(isDirectSearch && isDomRequest && hotelEntity.isAltAcco() && hotelEntity.getPropertyInfo()!=null && (StringUtils.isNotEmpty(hotelEntity.getPropertyInfo().getChainId()) || StringUtils.isNotEmpty(String.valueOf(hotelEntity.getPropertyInfo().getHostId()))));
                setChainAndHostPropertiesHeaders(hotel, hotelEntity, locusData);
                hotel.setPropertyInfo(buildPropertyInfo(hotelEntity.getPropertyInfo()));
                hotel.setView360IconUrl(hotelEntity.getThreeSixtyViewIconUrl());
                hotel.setHighSellingAltAcco(hotelEntity.isHighSellingAltAcco());
                hotel.setStarRatingType(hotelEntity.getStarRatingType());
                hotel.setSpotlightApplicable(hotelEntity.isSpotlightApplicable());
                ResponseRatePlan responseRatePlan = Optional.ofNullable(hotelEntity.getRooms())
                        .flatMap(rooms -> rooms.stream().findFirst())
                        .flatMap(room -> Optional.ofNullable(room.getRatePlans()).flatMap(ratePlans -> ratePlans.stream().findFirst()))
                        .orElse(null);

                PriceDetail priceDetail = Optional.ofNullable(responseRatePlan)
                        .map(ResponseRatePlan::getPrice)
                        .orElse(null);

                if (hotelEntity.isShowCallToBook()) {
                    String device = (searchHotelsRequest.getDeviceDetails() != null && StringUtils.isNotEmpty(searchHotelsRequest.getDeviceDetails().getBookingDevice())) ? searchHotelsRequest.getDeviceDetails().getBookingDevice() : EMPTY_STRING;
                    if (hotelEntity.getOmnitureFlags().isAbo() && utility.isExperimentTrue(expDataMap, ExperimentKeys.aboApplicable.name())) {
                        hotel.setRequestCallbackData(utility.buildRequestToCallBackDataForB2C(PAGE_CONTEXT_LISTING));
                    } else if (!hotelEntity.getOmnitureFlags().isAbo()) {
                        if (isCallToBookV2Applicable) {
                            hotel.setRequestCallbackData(utility.buildRequestToCallBackDataV2(PAGE_CONTEXT_LISTING));
                        } else {
                            hotel.setRequestCallbackData(utility.buildRequestToCallBackData(PAGE_CONTEXT_LISTING, hotelEntity.getName(), device));
                        }
                    }
                }

                hotel.setDetailDeeplinkUrl(hotelEntity.getDetailDeeplinkUrl());
                hotel.setWishListed(hotelEntity.getOmnitureFlags().isWishlisted());
                boolean setPriceDetailsBasisABO = !hotelEntity.getOmnitureFlags().isAbo() || !B2C.equalsIgnoreCase(idContext) || isMyPartnerRequest || isGccOrKsa();
                if (!utility.isExperimentTrue(expDataMap, ExperimentKeys.aboApplicable.name())) {
                    setPriceDetailsBasisABO = true;
                }
                hotel.setTotalRoomCount(hotelEntity.getTotalRoomCount());
                hotel.setId(hotelEntity.getId());
                hotel.setName(hotelEntity.getName());
                hotel.setMaskedPropertyName(hotelEntity.isMaskedPropertyName());
                hotel.setPropertyType(hotelEntity.getPropertyType());
                hotel.setPropertyLabel(hotelEntity.getPropertyLabel());
                hotel.setStayType(hotelEntity.getStayType());
                hotel.setStarRating(hotelEntity.getStarRating());
                hotel.setIsAltAcco(hotelEntity.isAltAcco());
                if (!utility.isExperimentTrue(expDataMap, ExperimentKeys.aboApplicable.name())) {
                    hotel.setSoldOut(hotelEntity.getSoldOutInfo() != null && StringUtils.isNotEmpty(hotelEntity.getSoldOutInfo().getType()));
                    hotel.setSoldOutInfo(buildSoldOutInfo(hotelEntity.getSoldOutInfo()));
                } else if (hotel.getIsABO() == null || !hotel.getIsABO()) {
                    hotel.setSoldOut(hotelEntity.getSoldOutInfo() != null && StringUtils.isNotEmpty(hotelEntity.getSoldOutInfo().getType()));
                    hotel.setSoldOutInfo(buildSoldOutInfo(hotelEntity.getSoldOutInfo()));
                } else if (!B2C.equalsIgnoreCase(idContext) || isMyPartnerRequest || isGccOrKsa()) {
                    hotel.setSoldOut(hotelEntity.getSoldOutInfo() != null && StringUtils.isNotEmpty(hotelEntity.getSoldOutInfo().getType()));
                    hotel.setSoldOutInfo(buildSoldOutInfo(hotelEntity.getSoldOutInfo()));
                }
                hotel.setGeoLocation(commonResponseTransformer.buildGeoLocation(hotelEntity.getLocation().getGeo()));
                hotel.setAppDeeplink(hotelEntity.getAppDeeplink());
                hotel.setMedia(commonResponseTransformer.buildMedia(hotelEntity.getMedia(), expDataMap));
                hotel.setTotalImageCount(hotelEntity.getMedia() != null && CollectionUtils.isNotEmpty(hotelEntity.getMedia().getImages()) ? hotelEntity.getMedia().getImages().size() : 0);
                hotel.setTravellerImageCount(0);
                hotel.setIsRTB(hotelEntity.getOmnitureFlags().isRtb());
                hotel.setIsABSO(hotelEntity.getOmnitureFlags().isAbso());
                hotel.setIsMLOS(hotelEntity.getCalendarCriteria() != null);
                UpsellRatePlanInfo upsellRateplanInfo = Optional.ofNullable(responseRatePlan)
                        .map(ResponseRatePlan::getUpsellRateplanInfo)
                        .orElse(null);
                hotel.setUpsellRateplanInfo(utility.isExperimentOn(expDataMap, EXP_GBRP) ? commonResponseTransformer.buildUpsellRateplanInfo(upsellRateplanInfo, currency) : null);
                GroupPrice groupPrice = new GroupPrice();
                //A new groupPrice node under priceDetail will be enabled if GRPN: T and is a group booking funnel on listing page
                boolean enableNewGroupDesign = searchHotelsRequest.getExpData() != null && utility.isExperimentOn(expDataMap, GRPN_GROUP_BOOKING_EXCLUSIVE_TAX_EXP) && isGroupBookingFunnel(funnelSource) && !isMyPartnerRequest;

                List<String> groupPriceSavingText = null;
                if (enableNewGroupDesign) {
                    //A new groupPrice node under priceDetail will be displayed
                    groupPriceForGRPN(searchHotelsRequest, priceDetail, hotelEntity, groupPrice);
                } else {
                    //Existing flow- groupPriceText and SavingsText under priceDetail will be displayed after calculations
                    groupPriceSavingText = getGroupPriceAndSavingText(hotelEntity, priceDetail, searchHotelsRequest, expDataMap);
                }
                //TODO - DAYUSE Case:
                if (FUNNEL_DAYUSE.equalsIgnoreCase(funnelSource)) {
                    hotel.setSlotDetail(buildSlotDetails(hotelEntity, searchHotelsRequest));
                }

                /* START - Build Price Detail */
                if ((FUNNEL_DAYUSE.equalsIgnoreCase(funnelSource) && hotelEntity.getRooms() != null)) {
                    //PRICE DETAILS ::
                     responseRatePlan = Optional.ofNullable(hotelEntity.getRooms())
                            .flatMap(rooms -> rooms.stream().findFirst())
                            .flatMap(room -> Optional.ofNullable(
                                            room.getRatePlans())
                                    .flatMap(ratePlans -> ratePlans.stream()
                                            .filter(ratePlan -> ratePlan.getSlot() == null || ratePlan.getSlot().getDuration() == 0)
                                            .findFirst()))
                            .orElse(null);

                     priceDetail = Optional.ofNullable(responseRatePlan)
                            .map(ResponseRatePlan::getPrice)
                            .orElse(null);

                    if (setPriceDetailsBasisABO && shouldSetPriceDetailsForDayUse(hotel.getSlotDetail(), priceDetail)) {
                        hotel.setPriceDetail(buildPriceDetailForDayUse(priceDetail));
                    }
                }  else {
//					String idContext = searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getIdContext() : null;
                    if (setPriceDetailsBasisABO && responseRatePlan != null) {
//						TODO: check if we can block the creation of displayPriceBreakDownList on hes only currently if we do that there are multiple null pointers
                        hotel.setPriceDetail(buildPriceDetail(responseRatePlan,
                                commonResponseTransformer.enableSaveValue(expDataMap),
                                commonResponseTransformer.enableDiscount(priceDetail),
                                (searchHotelsRequest.getRequestDetails() != null && searchHotelsRequest.getRequestDetails().isMetaInfo()),
                                groupPriceSavingText, idContext, enableNewGroupDesign, groupPrice, funnelSource));
                    }
                }


                hotel.setHotelPersuasions(hotelEntity.getHotelPersuasions());
                if (utility.isExperimentOn(expDataMap, EXP_PERNEW)) {
                    long start = System.currentTimeMillis();
                    persuasionUtil.updatePersuasionsForDesktopAndPwa((Map<String, Map<String, Object>>) hotel.getHotelPersuasions());
                    LOGGER.debug("Time Taken to update persuasion for Desktop/PWA is: {} ", System.currentTimeMillis() - start);
                }
                //On Old apps combined OTAs like MMT_BKG not supported, hence changing such OTA to MMT for old apps using exp COMBINED_OTA
                boolean combinedOTASupported = utility.isExperimentOn(expDataMap, COMBINED_OTA);
                hotel.setReviewSummary(commonResponseTransformer.buildReviewSummary(hotelEntity.getReviewDetails(), combinedOTASupported));
                LocationDetails locationDetails = hotelEntity.getLocation();
                hotel.setLocationDetail(new LocationDetail(locationDetails.getId(), locationDetails.getCityName(), locationDetails.getType(), locationDetails.getCountryId(), locationDetails.getCountryName()));
                hotel.setCategories(hotelEntity.getCategories());

                hotel.setTrackingInfo(hotelEntity.getTrackingInfo());
                hotel.setCalendarCriteria(buildCalendarCriteria(hotelEntity.getCalendarCriteria()));
                hotel.setHotelType(hotelEntity.getHotelType());

                boolean sectionMyBizSimilarToDirectHtl = MYBIZ_SIMILAR_TO_DIRECT_HOTEL.equalsIgnoreCase(sectionName);
                String drivingTimeText = "";
                if (searchHotelsRequest.getRequestDetails() != null && SHORTSTAYS_FUNNEL.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource())) {
//                        && commonResponseTransformer.isEligibleForNearbyFlow(searchHotelsRequest.getDeviceDetails())) {
                    drivingTimeText = commonResponseTransformer.buildDrivingTimeText(hotelEntity.getDrivingTime());
                    persuasionUtil.addShortStayPeithoPersuasionToHotelPersuasion(hotel, hotelEntity.getUspShortStayValue(), hotelEntity.getLocationPersuasions());
                }
                String dayUsePersuasionText = ""; //TODO - hotelEntity.getDayUsePersuasionsText();
                TransportPoi nearestGroundPoi = null; //TODO - hotelEntity.getNearestGroundTransportPoi();

                addLocationPersuasionToHotelPersuasions(hotel, hotelEntity.getLocationPersuasions(), hotelEntity.getFacilityHighlights(), searchHotelsRequest, commonResponseTransformer.enableAmenitiesPersuasion(expDataMap, searchHotelsRequest.getRequestDetails().getFunnelSource(), isMyPartnerRequest), sectionMyBizSimilarToDirectHtl, dayUsePersuasionText, nearestGroundPoi, drivingTimeText, locusData, commonModifierResponse != null && commonModifierResponse.isHomestayV2Flow());
                addPersuasionHoverData(hotel, hotelEntity, responseRatePlan != null ? responseRatePlan.getCancellationTimeline() : null, priceDetail, searchHotelsRequest);
                hotel.setHeroImage(hotelEntity.getHeroImage());
                if (StringUtils.isNotEmpty(hotelEntity.getSeoUrl())) {
                    hotel.setSeoUrl(hotelEntity.getSeoUrl());
                }

                // Based on this feature flag need to suppress few persuasions at specific placeholders configured at PMS and provided by client
                //At PMS level maintain map of key and List or PlaceHolders to suppress to make it generic if in future we want to suppress different placeholder for different flow we can use the same map
                if (searchHotelsRequest.getFeatureFlags() != null && searchHotelsRequest.getFeatureFlags().isPersuasionSuppression()) {
                    Map<String, List<String>> placeholdersToShowMap = null;
                    List<String> placeholdersToShow = null;
                    try {
                        //Not Using PMS config here, as it will be deprecated
                        placeholdersToShowMap = objectMapperUtil.getObjectFromJsonWithType(placeHoldersToShowConfig, new TypeReference<Map<String, List<String>>>() {
                        }, DependencyLayer.CLIENTGATEWAY);
                    } catch (JsonParseException e) {
                        LOGGER.debug("Json Parse exception while parsing placeholdersToShowMap : ", e);
                    }
                    if (MapUtils.isNotEmpty(placeholdersToShowMap)) {
                        placeholdersToShow = placeholdersToShowMap.get(SIMILAR_HOTELS);
                    }
                    LOGGER.debug("Placeholder we want to show for PersuasionSuppression {}", placeholdersToShow);
                    if (CollectionUtils.isNotEmpty(placeholdersToShow)) {
                        Map<Object, Object> hotelPersuasions = (Map<Object, Object>) hotel.getHotelPersuasions();
                        List<String> placeholdersToBeBlocked = new ArrayList<>();
                        for (Object key : hotelPersuasions.keySet()) {
                            if (!placeholdersToShow.contains(key)) {
                                placeholdersToBeBlocked.add(key.toString());
                            }
                        }
                        LOGGER.debug("Placeholder we have to block from Persuasion for PersuasionSuppression {}", placeholdersToBeBlocked);
                        for (String placeholderToBeBlocked : placeholdersToBeBlocked) {
                            if (MapUtils.isNotEmpty(hotelPersuasions)) {
                                hotelPersuasions.remove(placeholderToBeBlocked);
                            }
                        }
                    }
                }


                //TODO - MyPartner/MyBiz case
                    /*
                    String bookingDevice = (searchHotelsRequest != null && searchHotelsRequest.getDeviceDetails() != null) ? searchHotelsRequest.getDeviceDetails().getBookingDevice() : null;
                    if (MYBIZ_SIMILAR_TO_DIRECT_HOTEL.equalsIgnoreCase(sectionName) && (DEVICE_IOS.equalsIgnoreCase(bookingDevice) || DEVICE_OS_ANDROID.equalsIgnoreCase(bookingDevice) || DEVICE_OS_DESKTOP.equalsIgnoreCase(bookingDevice))) {
                        List<String> placeholdersToBeBlocked = DEVICE_OS_DESKTOP.equalsIgnoreCase(bookingDevice) ? desktopPersPlaceholdersToBeBlockedDemandConc : appsPersPlaceholdersToBeBlockedDemandConc;
                        Map<Object, Object> hotelPersuasions = (Map<Object, Object>) hotel.getHotelPersuasions();
                        for (String placeholderToBeBlocked : placeholdersToBeBlocked) {
                            if (MapUtils.isNotEmpty(hotelPersuasions)) {
                                hotelPersuasions.remove(placeholderToBeBlocked);
                            }
                        }

                    }*/
                // Mypartner Mob-landing
                //commonResponseTransformer.buildSelectiveHotelPersuasions(hotel, hotelEntity, priceDetail);
                if (checkIfHorizontalSection(sectionName, hotelList.size(), searchHotelsRequest.getDeviceDetails())) {
                    //TODO Add Tag
                    commonResponseTransformer.buildTopSectionPersuasion(hotel, hotelEntity.getTag(), sectionName, searchHotelsRequest.getDeviceDetails() != null ? searchHotelsRequest.getDeviceDetails().getBookingDevice() : null);
                    commonResponseTransformer.removePlaceHolderPersuasionsForSection(hotel, sectionName);
                }

                if(commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap())
                        && utility.similarPropertyOtherHost(commonModifierResponse.getExpDataMap())){
                    hotel.setSimilarHotelsRequired(hotelEntity.isShowSimilarProperty());
                }
                else{
                    // Based on this node client will hit search-hotel api similarHotel flow, and we set this flag based on some conditions provide by Product and configured at PMS to change them dynamically
                    //HTL-39243 Suppress SimilarHotels true for Direct Searched Property
                    if (commonHelper.checkValidHotel(searchHotelsRequest, hotelEntity)) {
                        try {
                            if (searchHotelsRequest.getRequestDetails() != null
                                    && searchHotelsRequest.getSearchCriteria() != null
                                    && searchHotelsRequest.getRequestDetails().getIdContext() != null
                                    && searchHotelsRequest.getRequestDetails().getFunnelSource() != null
                                    && searchHotelsRequest.getSearchCriteria().getLocationType() != null
                                    && (B2C.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getIdContext())
                                    || CORP_ID_CONTEXT.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getIdContext()))
                                    && FUNNEL_SOURCE_HOTELS.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource())
                                    && !(Constants.ZONE).equalsIgnoreCase(searchHotelsRequest.getSearchCriteria().getLocationType()))
                                hotel.setSimilarHotelsRequired(getSimilarHotelRequired(hotelEntity, priceDetail));
                        } catch (JsonParseException ex) {
                            LOGGER.error("An exception occurred while building hotel from HotelEntity", ex);
                        }
                    }
                }


                if (Objects.nonNull(hotel.getPriceDetail())) {
                    hotel.setPartnerDetails(getPartnerDetails(hotel.getPriceDetail(), getMarkUpForHotels(markUpDetails, hotel.getPriceDetail().getDiscountedPriceWithTax())));
                } else {
                    hotel.setPartnerDetails(commonResponseTransformer.getPartnerDetails(hotel.getPriceDetail(), 0.0d));
                }
                // TODO - Used only in athena call
                // hotel.setDeepLink(hotelEntity.getDesktopDeeplink());
                //TODO - Comparator case
                //hotel.setCrossSellTag(hotelEntity.getCrossSellTag());
                hotel.setHotelBottomCard(buildHotelBottomCard(hotelEntity.getQuickBookInfo()));
                //TODO - myPartner/MyBiz case
                    /*
                        hotel.setHotelBottomCard(buildHotelBottomCard(hotelEntity.getQuickBookInfo()));
                        hotel.setHotelTopCard(buildHotelTopCard(hotelEntity.getMyBizSimilarToDirectObj()));
                        hotel.setReviewDeeplinkUrl(hotelEntity.getReviewDeeplinkUrl());
                        // TODO: Check - Adding Markup for MyPartner
                         if (Objects.nonNull(hotel.getPriceDetail()))
                        hotel.setPartnerDetails(getPartnerDetails(hotel.getPriceDetail(), pricingEngineHelper.getMarkUpForHotels(markUpDetails, hotel.getPriceDetail().getDiscountedPriceWithTax())));
                        hotel.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(hotelEntity.getDisplayFare() != null ? hotelEntity.getDisplayFare().getCorpMetaData() : null, utility.isTCSV2FlowEnabled(expDataMap)));
                        //Add booking confirmation persuasion for negotiated rate hotels.Negotiated rates are the one-on-one rates that are directly negotiated between the hotel and the organization.
                        if (VERTICAL.equalsIgnoreCase(orientation) && RTB_EMAIL.equalsIgnoreCase(hotelEntity.getLowestRateRpBookingModel())) {
                            addBookingConfirmationPersuasion(hotelEntity);
                        }
                        // Add special fare persuasion for negotiated rate hotels.
                        if (corpPreferredRateSegmentId != null && corpPreferredRateSegmentId.equals(hotelEntity.getLowestRateSegmentId())) {
                            addSpecialFarePersuasion(hotelEntity);
                        }
                     */

                hotel.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(hotelEntity.getCorpMetaInfo() != null ? hotelEntity.getCorpMetaInfo() : null, utility.isTCSV2FlowEnabled(expDataMap)));
                hotel.setMmtHotelCategory(hotelEntity.getHotelCategory());
                //TODO - Not found in Android contract
                    /*
                        hotel.setMaskedPrice(false);
                        hotel.setIsGroupBookingForSimilar(false);
                        hotel.setAlternateDates(false);
                        hotel.setMultiRoomRecommendation(false);
                        hotel.setMaskedPrice(hotelEntity.isMaskedPrice());
                        hotel.setIsGroupBookingForSimilar(hotelEntity.isGroupBookingForSimilar()); //Not in Use - Only Test Cases
                        hotel.setServiceApartment(hotelEntity.isServiceApartment());
                        hotel.setFromCity(hotelEntity.getFromCity());
                        hotel.setDistance(hotelEntity.getDistance());
                        hotel.setDistanceUnit(hotelEntity.getDistanceUnit());
                        hotel.setFreeCancellationText(hotelEntity.getFreeCancellationText());
                        hotel.setAlternateDates(hotelEntity.isAlternateDatesAvailable());
                        hotel.setMultiRoomRecommendation(hotelEntity.isRecommendedMultiRoom()); //Not in Use
                        hotel.setSharingUrl(hotelEntity.getSharingUrl());
                        hotel.setShortList(hotelEntity.isShortList());
                        hotel.setSponsored(hotelEntity.isSponsored());
                        hotel.setNewType(hotelEntity.isNewType()); //"NEW" is the tag type which will be sent by singularity for reference (HTL-37120)
                        hotel.setPoiTag(hotelEntity.getPoiTag());
                        hotel.setExtraMeals(hotelEntity.getExtraMeals());
                        hotel.setViewType(hotelEntity.getViewType()); //Not in Use
                        hotel.setRatePersuasionText(hotelEntity.getRatePersuasionText());
                        hotel.setLastBookedInfo(hotelEntity.getLastBookedInfo()); //Not in Use
                        hotel.setSearchRoomDeeplinkUrl(hotelEntity.getSearchRoomDeeplinkUrl()); //Not in Use
                        hotel.setHeroVideoUrl(hotelEntity.getHeroVideoUrl());
                        hotel.setMmtHotelCategory(hotelEntity.getMmtHotelCategory());
                        hotel.setStoryViewDescription(hotelEntity.getLongTailStoryPersuasions());
                        hotel.setPropertyHighlightText(hotelEntity.getLongTailPropertyCardPersuasions());
                        if (hotelEntity.getCollectionCardPersuasion() != null) {
                            hotel.setCollectionCardPersuasion(new CollectionCardPersuasion());
                            hotel.getCollectionCardPersuasion().setText(hotelEntity.getCollectionCardPersuasion().getText());
                            hotel.getCollectionCardPersuasion().setIconUrl(hotelEntity.getCollectionCardPersuasion().getIconUrl());
                        }
                    */


                odd[0] = !odd[0];
                //SET SHARING URLS :
                hotel.setSharingUrl(prepareHotelSharingUrl(hotel.getId(), searchHotelsRequest, rscValue, distributedRoomStayCandidateList,hotel.isMaskedPropertyName()));
                String detailUrl = getDetailDeeplink(searchHotelsRequest);
                hotel.setAppDeeplink(buildHotelLevelAppDeepLink(searchHotelsRequest, hotel, rscValue, distributedRoomStayCandidateList,null, locusData, detailUrl));
                hotel.setDetailDeeplinkUrl(buildDetailDeepLinkUrl(hotel, hotelEntity, searchHotelsRequest, rscValue, distributedRoomStayCandidateList));
                if(hotelEntity.getQuickBookInfo()!=null && StringUtils.isNotEmpty(hotelEntity.getQuickBookInfo().getReviewDeeplinkUrl())) {
                    boolean forwordBookingEnabled = searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails()!=null && searchHotelsRequest.getRequestDetails().isForwardBookingFlow();
                    String myBizFlowIdentifier = searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails()!=null ? searchHotelsRequest.getRequestDetails().getMyBizFlowIdentifier() : EMPTY_STRING;
                    String requisitonId = searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails()!=null ? searchHotelsRequest.getRequestDetails().getRequisitionID() : EMPTY_STRING;
                    hotel.setReviewDeeplinkUrl(appendDataToReviewDeepLink(hotelEntity.getQuickBookInfo().getReviewDeeplinkUrl(), rscValue, forwordBookingEnabled, hotelEntity.isMaskedPropertyName(), myBizFlowIdentifier, requisitonId));
                }
                hotels.add(hotel);
            } catch (Exception ex) {
                LOGGER.error("An exception occurred while building hotel from HotelEntity", ex);
            }
        });
        LOGGER.warn("Hotel List preparation time : {}", new Date().getTime() - startTime);
        return hotels;
    }

    private String appendDataToReviewDeepLink(String deeplink, String rscValue, boolean forwardBookingFlow, boolean maskedPropertyName, String myBizFlowIdentifier, String requisitionId) {
        if(StringUtils.isEmpty(deeplink)) {
            return EMPTY_STRING;
        }
        StringBuilder reviewDeepLinkBuilder = new StringBuilder(deeplink);

        if(StringUtils.isNotEmpty(rscValue)) {
            reviewDeepLinkBuilder.append(AND_SEPARATOR)
                    .append(RSC)
                    .append(PR_SEPARATOR)
                    .append(rscValue);
        }
        if(StringUtils.isNotEmpty(requisitionId)) {
            reviewDeepLinkBuilder.append(AND_SEPARATOR)
                    .append("requisitionID")
                    .append(PR_SEPARATOR)
                    .append(requisitionId);
        }
        if(StringUtils.isNotEmpty(myBizFlowIdentifier)) {
            reviewDeepLinkBuilder.append(AND_SEPARATOR)
                    .append("myBizFlowIdentifier")
                    .append(PR_SEPARATOR)
                    .append(myBizFlowIdentifier);
        }
        reviewDeepLinkBuilder.append(AND_SEPARATOR)
                .append("forward")
                .append(PR_SEPARATOR)
                .append(forwardBookingFlow);
        reviewDeepLinkBuilder.append(AND_SEPARATOR)
                .append(MPN)
                .append(PR_SEPARATOR)
                .append(maskedPropertyName);

        return reviewDeepLinkBuilder.toString();

    }

    public com.mmt.hotels.clientgateway.response.PriceDetail getPartnerDetails(final com.mmt.hotels.clientgateway.response.PriceDetail priceDetail, final double markUp) {
        final com.mmt.hotels.clientgateway.response.PriceDetail priceWithMarkUp = new com.mmt.hotels.clientgateway.response.PriceDetail();
        if (Objects.nonNull(priceDetail.getPrice()) && Objects.nonNull(priceDetail.getPriceWithTax()) && Objects.nonNull(priceDetail.getDiscountedPrice()) && Objects.nonNull(priceDetail.getDiscountedPriceWithTax())) {
            priceWithMarkUp.setPrice(priceDetail.getPrice() + markUp);
            priceWithMarkUp.setPriceWithTax(priceDetail.getPriceWithTax() + markUp);
            priceWithMarkUp.setDiscountedPrice(priceDetail.getDiscountedPrice() + markUp);
            priceWithMarkUp.setDiscountedPriceWithTax(priceDetail.getDiscountedPriceWithTax() + markUp);
        }
        return priceWithMarkUp;
    }

    private HotelCard buildHotelBottomCard(QuickBookInfo quickBookInfo) {
        if (quickBookInfo != null) {
            return buildQuickBookCard(quickBookInfo);
        }
        return null;
    }

    protected com.mmt.hotels.clientgateway.response.PriceDetail buildPriceDetailForDayUse(PriceDetail input) {
        if (input == null) {
            return null;
        }
        com.mmt.hotels.clientgateway.response.PriceDetail priceDetail = new com.mmt.hotels.clientgateway.response.PriceDetail();
        priceDetail.setPrice(input.getDisplayPrice());
        priceDetail.setPriceWithTax(input.getDisplayPrice());
        priceDetail.setDiscountedPrice(input.getDisplayPrice());
        priceDetail.setDiscountedPriceWithTax(input.getDisplayPrice() + input.getTotalTax());
        priceDetail.setTotalTax(input.getTotalTax());
        priceDetail.setPricingKey("DEFAULT");

        return priceDetail;
    }

    public double getMarkUpForHotels(final MarkUpDetails markUpDetails, final Double finalPriceWithoutTax) {
        final boolean isDomestic = Constants.DOMESTIC.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()));
        if (Objects.nonNull(finalPriceWithoutTax) && finalPriceWithoutTax > 0 && markUpDetails != null &&
                markUpDetails.isMarkupEligible() && markUpDetails.getMarkupMap() != null) {
            final MarkUp markUp = isDomestic ? markUpDetails.getMarkupMap().get("DH") : markUpDetails.getMarkupMap().get("IH");
            if (Objects.nonNull(markUp))
                return PERCENTAGE.equals(markUp.getType()) ? (markUp.getValue() * finalPriceWithoutTax * .01) : markUp.getValue();
        }
        return 0;
    }


    // HTL-37846 => dont send price detail or slashed price for day use if it is less than hourly prices 3h/6h/9h
    public boolean  shouldSetPriceDetailsForDayUse(List<SlotDetail> slotDetails, PriceDetail input) {
        if (CollectionUtils.isNotEmpty(slotDetails) && input != null) {
            for (SlotDetail slotDetail : slotDetails) {

                double fullNightPrice = input.getDisplayPrice() + input.getTotalTax();
                if (slotDetail != null && !isSlashedPriceGreaterThanHourlyPrice(slotDetail,fullNightPrice)) {
                    return false;
                }
            }
        }
        return true;
    }

    private boolean isSlashedPriceGreaterThanHourlyPrice(SlotDetail slotDetail, double fullNightPrice) {
        if(slotDetail != null && slotDetail.getPriceDetail() == null)
            return true;
        return (slotDetail.getPriceDetail().getDiscountedPriceWithTax() < fullNightPrice);
    }


    private List<SlotDetail> buildSlotDetails(HotelDetails hotelEntity, SearchHotelsRequest searchHotelsRequest) {
        if (hotelEntity == null || CollectionUtils.isEmpty(hotelEntity.getRooms())) {
            return null;
        }
        List<SlotDetail> slotDetailList = new ArrayList<>();
        List<RoomEntity> roomTypeDetailsList = hotelEntity.getRooms();
        Set<Integer> slotDetailCount = new HashSet<>();
        Slot slot = null;
        SlotDetail slotDetail = null;
        String slotTime = null;

        // If a particular select is requested return that slot only
        if (searchHotelsRequest != null && searchHotelsRequest.getSearchCriteria() != null && searchHotelsRequest.getSearchCriteria().getSlot() != null
                && searchHotelsRequest.getSearchCriteria().getSlot().getDuration() != null && searchHotelsRequest.getSearchCriteria().getSlot().getDuration() > 0) {
            Integer requestedSlotDuration = searchHotelsRequest.getSearchCriteria().getSlot().getDuration();
            for (RoomEntity roomTypeDetails : roomTypeDetailsList) {
                List<ResponseRatePlan> ratePlans = roomTypeDetails.getRatePlans();
                for(ResponseRatePlan ratePlan:ratePlans){
                    if (ratePlan.getSlot() != null && ratePlan.getSlot().getDuration()>0
                            && requestedSlotDuration == ratePlan.getSlot().getDuration()) {
                        buildSlotDetailList(hotelEntity, searchHotelsRequest, slotDetailList, slotDetailCount, ratePlan);
                    }
                }
            }
            return slotDetailList;
        }

        for (RoomEntity roomTypeDetails : roomTypeDetailsList) {
            List<ResponseRatePlan> ratePlans = roomTypeDetails.getRatePlans();
            if (CollectionUtils.isNotEmpty(ratePlans)) {
                for (ResponseRatePlan ratePlan : ratePlans) {
                    if (ratePlan.getSlot() != null && ratePlan.getSlot().getDuration() > 0) {
                        slotTime = buildSlotDetailList(hotelEntity, searchHotelsRequest, slotDetailList, slotDetailCount, ratePlan);
                    }
                }
            }
        }

        if (!slotDetailCount.isEmpty() && slotDetailCount.size() < 3) {
            Iterator<Integer> itr = missingSlotDetails != null && missingSlotDetails.getDuration() != null ? missingSlotDetails.getDuration().iterator() : null;
            while (itr != null && itr.hasNext()) {
                Integer value = itr.next();
                if (!slotDetailCount.contains(value)) {
                    slotDetail = new SlotDetail();
                    slot = new Slot();
                    slot.setDuration(value);
                    com.mmt.hotels.model.response.dayuse.Slot tempSlot = new com.mmt.hotels.model.response.dayuse.Slot();
                    tempSlot.setDuration(slot.getDuration());
                    tempSlot.setTimeSlot(slotTime);
                    slot.setTimeSlot(utility.calculateTimeSlot_Meridiem(tempSlot));
                    slotDetail.setSlot(slot);
                    slotDetailList.add(slotDetail);
                }
            }
        }
        return slotDetailList;
    }


    private String buildSlotDetailList(HotelDetails hotelEntity, ListingSearchRequest searchHotelsRequest, List<SlotDetail> slotDetailList,
                                       Set<Integer> slotDetailCount, ResponseRatePlan ratePlan) {
        Slot slot;
        SlotDetail slotDetail;
        String slotTime;
        slotDetail = new SlotDetail();


        String idContext = searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getIdContext() : null;
        slotDetail.setPriceDetail(buildPriceDetail(ratePlan,
                commonResponseTransformer.enableSaveValue(searchHotelsRequest.getExpDataMap()),
                commonResponseTransformer.enableDiscount(ratePlan.getPrice()  != null ? ratePlan.getPrice()  : null),
                (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null && searchHotelsRequest.getRequestDetails().isMetaInfo()),
                Collections.emptyList(),idContext,false,null,searchHotelsRequest.getRequestDetails().getFunnelSource()));

        slot = new Slot();
        slot.setDuration(ratePlan.getSlot().getDuration());
        slotDetailCount.add(slot.getDuration());
        slotTime = ratePlan.getSlot().getTimeSlot();
        slot.setTimeSlot(utility.calculateTimeSlot_Meridiem(slotTime, slot.getDuration()));
        slotDetail.setSlot(slot);
        slotDetailList.add(slotDetail);
        return slotTime;
    }

    private Map<String, String> buildTrackingMap(TrackingVariables trackingVariables) {
        Map<String, String> trackingMap = new HashMap<>();
        if(trackingVariables!=null && trackingVariables.isFamilyFriendly()) {
            trackingMap.put(m_c54_tracking_key, familyFriendlyTracking);
        }
        return trackingMap;
    }

    private StaticPrice buildStaticPriceABO(com.gommt.hotels.orchestrator.model.response.listing.StaticPrice staticPrice) {
        StaticPrice staticPriceCG = null;
        if (staticPrice != null && StringUtils.isNotEmpty(staticPrice.getPriceDisplayMsg())) {
            staticPriceCG = new StaticPrice();
            staticPriceCG.setPriceTitle(staticPrice.getPriceTitle());
            staticPriceCG.setPriceDisplayMsg(staticPrice.getPriceDisplayMsg());
            staticPriceCG.setPriceLabel(staticPrice.getPriceLabel());
        }
        return staticPriceCG;
    }

    private com.mmt.hotels.model.response.searchwrapper.PropertyChainInfo buildPropertyInfo(PropertyChainInfo propertyChainInfo) {
        if(propertyChainInfo == null) {
            return null;
        }
        com.mmt.hotels.model.response.searchwrapper.PropertyChainInfo propertyInfo = new com.mmt.hotels.model.response.searchwrapper.PropertyChainInfo();
        propertyInfo.setChainId(propertyChainInfo.getChainId());
        propertyInfo.setHostId(propertyChainInfo.getHostId());
        return propertyInfo;
    }

    public void updateHotelCardType(SearchHotelsRequest searchHotelsRequest, CommonModifierResponse commonModifierResponse, PersonalizedSectionDetails perResponse, PersonalizedSection personalizedSection) {
        String experimentKeyValue = commonModifierResponse.getExpDataMap() != null
                ? commonModifierResponse.getExpDataMap().get(ExperimentKeys.PUSH_INDEPENDENT_ON_DIRECT_SEARCH.getKey()) : null;
        boolean isIndependentProperty = checkIndependentPropertyAttributeInSearchHotelsRequest(searchHotelsRequest);

        if (EXPERIMENT_KEY_VALUE_B.equals(experimentKeyValue) && DIRECT_HOTEL.equals(personalizedSection.getName()) && isIndependentProperty) {
            personalizedSection.setHotelCardType(COMPACT_V1_HOTEL_CARD_TYPE);
        } else if (EXPERIMENT_KEY_VALUE_C.equals(experimentKeyValue) && isIndependentProperty && SIMILAR_HOTELS.equals(personalizedSection.getName())) {
            personalizedSection.setHotelCardType(COMPACT_V2_HOTEL_CARD_TYPE);
            personalizedSection.setOrientation(HORIZONTAL);
            personalizedSection.setSectionBG(GRADIENT_END);
        } else if (EXPERIMENT_KEY_VALUE_C.equals(experimentKeyValue) && DIRECT_HOTEL.equals(personalizedSection.getName()) && isIndependentProperty) {
            personalizedSection.setHotelCardType(COMPACT_V1_HOTEL_CARD_TYPE);
        } else {
            if (StringUtils.isNotEmpty(perResponse.getHotelCardType())) {
                personalizedSection.setHotelCardType(perResponse.getHotelCardType().toLowerCase());
            }
        }
    }

    private boolean checkIndependentPropertyAttributeInSearchHotelsRequest(SearchHotelsRequest searchHotelsRequest) {
        List<String> attributeValues = fetchAttributeValuesFromSearchHotelsRequest(searchHotelsRequest);
        return attributeValues.contains(ATTRIBUTE_INDEPENDENT_PROPERTY);
    }

    private List<String> fetchAttributeValuesFromSearchHotelsRequest(SearchHotelsRequest searchHotelsRequest) {
        List<String> attributeValues = new ArrayList<>();
        if (searchHotelsRequest != null && searchHotelsRequest.getMatchMakerDetails() != null && searchHotelsRequest.getMatchMakerDetails().getHotels() != null) {
            for (InputHotel inputHotel : searchHotelsRequest.getMatchMakerDetails().getHotels()) {
                attributeValues.add(inputHotel.getAttribute());
            }
        }
        return attributeValues;
    }

    private boolean getCardInsertionAllowedValue(PersonalizedSectionDetails perResponse, String idContext) {
        return !HORIZONTAL.equalsIgnoreCase(perResponse.getOrientation()) && (!StringUtils.isNotBlank(perResponse.getName()) || ((!StringUtils.isNotBlank(idContext) ||
                !LAST_BOOKED_HOTELS.equalsIgnoreCase(perResponse.getName()) || !B2C.equalsIgnoreCase(idContext))));
    }

    private boolean getSimilarHotelRequired(HotelDetails hotelEntity, PriceDetail priceDetail) throws JsonParseException {


        //For chains and hosts
        if(hotelEntity!= null && hotelEntity.isAltAcco() && hotelEntity.getPropertyInfo()!=null && (StringUtils.isNotEmpty(hotelEntity.getPropertyInfo().getChainId()) || StringUtils.isNotEmpty(String.valueOf(hotelEntity.getPropertyInfo().getHostId())))){
            return true;
        }

        FilterConditions filterConditions = objectMapperUtil.getObjectFromJsonWithType(filterConditionsConfig, new TypeReference<FilterConditions>() {
                },
                DependencyLayer.CLIENTGATEWAY);
        double price = -1;

        if (priceDetail != null) {
            price = priceDetail.getDisplayPrice();
        }

        //if hotel category is in includeCategoryList then only we return true
        boolean isIncludeCategory = false;
        if (filterConditions != null && CollectionUtils.isNotEmpty(filterConditions.getCategoriesIncluded()) && CollectionUtils.isNotEmpty(hotelEntity.getCategories())) {
            for (String category : hotelEntity.getCategories()) {
                if (filterConditions.getCategoriesIncluded().contains(category)) {
                    isIncludeCategory = true;
                    break;
                }
            }
        }

        if (!isIncludeCategory && filterConditions != null && filterConditions.getRange() != null &&
                (price < filterConditions.getRange().getMinValue() || price > filterConditions.getRange().getMaxValue())) {
            return false;
        }

        //if hotel category is not in excludedCategoryList then only we return true
        boolean isExcludedCategory = false;
        if (filterConditions != null && CollectionUtils.isNotEmpty(filterConditions.getCategoriesExcluded()) && CollectionUtils.isNotEmpty(hotelEntity.getCategories())) {
            for (String category : hotelEntity.getCategories()) {
                if (filterConditions.getCategoriesExcluded().contains(category)) {
                    isExcludedCategory = true;
                    break;
                }
            }
        }
        return !isExcludedCategory || isIncludeCategory;
    }

    public com.mmt.hotels.clientgateway.response.PriceDetail buildPriceDetail(ResponseRatePlan responseRatePlan, boolean enableSaveValue, boolean enableDiscount,
                                                                               boolean metaInfo, List<String> groupPriceAndSavingText, String idContext,
                                                                               boolean enableNewGroupDesign, GroupPrice groupPriceNewDesign, String funnelSource) {
        PriceDetail priceDetailOrch = responseRatePlan.getPrice();
        if (priceDetailOrch == null) {
            return null;
        }
        String lowestRateSegmentId = ""; //responseRatePlan.getLowestRateSegmentId();
        com.mmt.hotels.clientgateway.response.PriceDetail priceDetail = new com.mmt.hotels.clientgateway.response.PriceDetail();
        if (CollectionUtils.isNotEmpty(groupPriceAndSavingText)) {
            if(isGroupBookingFunnel(funnelSource) && !groupPriceAndSavingText.isEmpty()) {
                priceDetail.setGroupPriceText(groupPriceAndSavingText.get(0));
            }
            if (groupPriceAndSavingText.size() > 1) {
                priceDetail.setSavingsText(groupPriceAndSavingText.get(1));
            }
        }
        //groupPrice node is displayed only when GRPN:T and is a group booking funnel
        if (enableNewGroupDesign && null != groupPriceNewDesign) {
            priceDetail.setGroupPrice(groupPriceNewDesign);
        }
        priceDetail.setCoupon(buildCoupon(priceDetailOrch));
        priceDetail.setTotalTax(priceDetailOrch.getTotalTax());
        double saving = priceDetailOrch.getTotalDiscount();
        if (enableSaveValue) {
            priceDetail.setTotalSaving(saving);
            priceDetail.setSavingPerc(priceDetailOrch.getSavingPerc());
        }

        priceDetail.setPrice(priceDetailOrch.getBasePrice());
        priceDetail.setPriceWithTax(priceDetailOrch.getBasePrice() + priceDetailOrch.getTotalTax());
        priceDetail.setDiscountedPrice(priceDetailOrch.getDisplayPrice() - (priceDetailOrch.isTaxIncluded() ? priceDetailOrch.getTotalTax() : 0));
        priceDetail.setDiscountedPriceWithTax(priceDetailOrch.getDisplayPrice() + (priceDetailOrch.isTaxIncluded() ? 0 : priceDetailOrch.getTotalTax()));
        priceDetail.setTotalTax(priceDetailOrch.getTotalTax());
        priceDetail.setPricingKey("DEFAULT");
        /*
         * myPartner change log :
         * 	This value is added in the schema repo.
         * 	We've not versioned this, since there is no changes to existing keys, this is a new addition only used for
         * 	myPartner affilaiteID
         * 	Conditionally it will be set - value > 100 and > 5% of the discounted Price [to be closed on the product side]
         * */
//		if(enableDiscount){
//			priceDetail.setMyPartnerDiscount(priceDetailOrch.getTotalSaving());
//		}

        if (enableDiscount && StringUtils.equalsIgnoreCase(idContext, CORP_ID_CONTEXT)) {
            priceDetail.setTotalDiscount(saving);
        }

        if (metaInfo) {
            Map<String, String> metaMap = new HashMap<>();
            metaMap.put(SERVICE_CHARGE_KEY, String.valueOf(priceDetailOrch.getTaxBreakUp().getHotelServiceCharge()));
            metaMap.put(HOTEL_TAX_KEY, String.valueOf(priceDetailOrch.getTaxBreakUp().getHotelTax()));
            metaMap.put(SERVICE_FEES_KEY, String.valueOf(priceDetailOrch.getTaxBreakUp().getServiceFee()));
            //metaMap.put(AFFILIATE_FEES_KEY, String.valueOf(priceDetailOrch.getAffiliateFee()));
            //metaMap.put(WALLET_KEY, String.valueOf(priceDetailOrch.getWallet()));

            //TODO -
            //metaMap.put(MMT_DISCOUNT_KEY, String.valueOf(priceDetailOrch.getMMTDiscount()));
            //metaMap.put(BLACK_DISCOUNT_KEY, String.valueOf(priceDetailOrch.getBlackDiscount()));

            metaMap.put(CDF_DISCOUNT_KEY, String.valueOf(priceDetailOrch.getCouponDiscount()));

            if (priceDetailOrch.getApplicableCoupons() != null) {
                List<PriceCouponInfo> list = priceDetailOrch.getApplicableCoupons()
                        .stream()
                        .filter(entry -> entry.getDiscount() > 0.0d)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(list)) {
                    for (PriceCouponInfo couponInfo : list) {
                        if (!couponInfo.getType().equalsIgnoreCase(PromotionalOfferType.MIXED.getName())) {
                            metaMap.put(couponInfo.getCouponCode(), String.valueOf(couponInfo.getDiscount()));
                        }
                    }
                }
            }
            metaMap.put(LOWEST_RATE_SEGMENT_KEY, lowestRateSegmentId);
            priceDetail.setMetaInfo(metaMap);
        }

        return priceDetail;
    }

    private Coupon buildCoupon(PriceDetail priceDetail) {
        List<PriceCouponInfo> couponInfoList = priceDetail.getApplicableCoupons();
        PriceCouponInfo couponInfo = CollectionUtils.isNotEmpty(couponInfoList) ? couponInfoList.get(0) : null;
        Coupon coupon = null;
        if (couponInfo == null && priceDetail.getCouponDiscount() > 0.0d) {
            coupon = new Coupon();
            coupon.setCode(priceDetail.getCouponCode());
            coupon.setCouponAmount(priceDetail.getCouponDiscount());
            //TODO: Fix - coupon.setPromoIcon(StringUtils.isNotEmpty(couponInfo.getPromoIconLink()) ? couponInfo.getPromoIconLink() : genericBankIcon);
            coupon.setPromoIcon(genericBankIcon);
            PriceCouponInfo bestCoupon = getBestCoupon(priceDetail.getApplicableCoupons(), priceDetail.getCouponCode());
            if (bestCoupon != null) {
                coupon.setDescription(bestCoupon.getDescription());
                coupon.setSpecialPromo(bestCoupon.isSpecialPromoCoupon());
                coupon.setType(bestCoupon.getType());

            }
        } else if (couponInfo != null) {
            coupon = getCoupon(couponInfo);
        }
        return coupon;
    }

    private PriceCouponInfo getBestCoupon(List<PriceCouponInfo> applicableCoupons, String couponCode) {
        return CollectionUtils.isNotEmpty(applicableCoupons) ? applicableCoupons.stream().filter(coupon -> coupon.getCouponCode().equalsIgnoreCase(couponCode)).findFirst().orElse(null) : null;
    }

    private Coupon getCoupon(PriceCouponInfo couponInfo) {
        Coupon coupon = new Coupon();
        coupon.setDescription(couponInfo.getDescription());
        coupon.setCode(couponInfo.getCouponCode());
        coupon.setType(couponInfo.getType());
        coupon.setCouponAmount(couponInfo.getDiscount());
        coupon.setSpecialPromo(couponInfo.isSpecialPromoCoupon());
        //TODO: Fix - coupon.setPromoIcon(StringUtils.isNotEmpty(couponInfo.getPromoIconLink()) ? couponInfo.getPromoIconLink() : genericBankIcon);
        coupon.setPromoIcon(genericBankIcon);
        return coupon;
    }

    public List<String> getGroupPriceAndSavingText(HotelDetails searchWrapperHotelEntity, PriceDetail priceDetail, ListingSearchRequest listingSearchRequest, Map<String, String> expDataMap) {
        String currency = (listingSearchRequest != null && listingSearchRequest.getSearchCriteria() != null) ? listingSearchRequest.getSearchCriteria().getCurrency() : "INR";
        if (priceDetail == null) {
            return null;
        }

        List<String> groupPriceAndSavingText = new ArrayList<>();
        boolean isPerNewEnabled = utility.isExperimentOn(expDataMap, EXP_PERNEW);
        long totalPrice = (long) (priceDetail.getTotalAmount());
        long savingPerc = (long) (priceDetail.getSavingPerc());
        int roomCount = searchWrapperHotelEntity.getTotalRoomCount();
        String checkIn = null;
        String checkOut = null;
        if (listingSearchRequest != null && listingSearchRequest.getSearchCriteria() != null) {
            checkOut = listingSearchRequest.getSearchCriteria().getCheckOut();
            checkIn = listingSearchRequest.getSearchCriteria().getCheckIn();
        }
        int nightCount = 0;
        if (StringUtils.isNotBlank(checkIn) && StringUtils.isNotBlank(checkOut)) {
            nightCount = dateUtil.getDaysDiff(LocalDate.parse(checkIn), LocalDate.parse(checkOut));
        }
        if (StringUtils.equalsIgnoreCase(searchWrapperHotelEntity.getStayType(), "ENTIRE") && (searchWrapperHotelEntity.getTotalRoomCount() == 1)) {
            NumberFormat numberFormat = com.ibm.icu.text.NumberFormat.getInstance(new Locale("en", "IN"));
            groupPriceAndSavingText.add(polyglotService.getTranslatedData(Utility.getGroupPriceTextOrSavingPercKey(GROUP_PRICE_TEXT_ONE_NIGHT, listingSearchRequest != null ? listingSearchRequest.getClient() : "") + (isPerNewEnabled ? (UNDERSCORE + EXP_PERNEW) : ""))
                    .replace(ConstantsTranslation.AMOUNT.toUpperCase(), numberFormat.format(totalPrice))
                    .replace("{NIGHT_COUNT}", String.valueOf(nightCount))
                    .replace("{ROOM_COUNT}", String.valueOf(roomCount)));
        } else {
            NumberFormat numberFormat = com.ibm.icu.text.NumberFormat.getInstance(new Locale("en", "IN"));
            groupPriceAndSavingText.add(commonResponseTransformer.getGroupPriceText(roomCount, nightCount, numberFormat.format(totalPrice), listingSearchRequest != null ? listingSearchRequest.getClient() : "", isPerNewEnabled));
        }


        String savingPercText = null;
        if (savingPerc != 0.0 && searchWrapperHotelEntity.isGroupBookingPrice()) {
            savingPercText = polyglotService.getTranslatedData(Utility.getGroupPriceTextOrSavingPercKey(SAVING_PERC_TEXT, listingSearchRequest != null ? listingSearchRequest.getClient() : "") + (isPerNewEnabled ? (UNDERSCORE + EXP_PERNEW) : "")).replace(SAVING_PERC, String.valueOf(savingPerc));
        }
        groupPriceAndSavingText.add(savingPercText);

        if (StringUtils.isNotEmpty(currency) && !currency.equalsIgnoreCase("INR")) {
            List<String> updatedGroupPriceAndSavingText = new ArrayList<>();
            for (String entry : groupPriceAndSavingText) {
                if (StringUtils.isNotEmpty(entry)) {
                    String currencySymbol = currency + " ";
                    updatedGroupPriceAndSavingText.add(entry.replaceAll("₹", currencySymbol));
                } else {
                    updatedGroupPriceAndSavingText.add(entry);
                }
            }
            return updatedGroupPriceAndSavingText;
        }

        return groupPriceAndSavingText;
    }


    private void groupPriceForGRPN(ListingSearchRequest searchHotelsRequest, PriceDetail priceDetail, HotelDetails hotelEntity, GroupPrice groupPrice) {
        if (groupPrice == null || priceDetail == null) {
            return;
        }

        long totalPrice = (long) (priceDetail.getDisplayPrice());
        String savingPercText = getSavingPercTextForGroupBooking(searchHotelsRequest, priceDetail, hotelEntity);
        String priceDisplayMessage = createPriceDisplayMsgAsPerRoomsNights(searchHotelsRequest, hotelEntity);
        groupPrice.setPriceLabel(polyglotService.getTranslatedData(ConstantsTranslation.GROUP_PRICE_LABEL));
        groupPrice.setSavingsText(savingPercText);
        groupPrice.setPriceDisplayMsg(priceDisplayMessage);
        groupPrice.setTaxMsg(polyglotService.getTranslatedData(ConstantsTranslation.TAX_MSG));
        //totalPrice will be including taxes
        groupPrice.setPrice(totalPrice);
    }

    private String getSavingPercTextForGroupBooking(ListingSearchRequest searchHotelsRequest, PriceDetail priceDetail, HotelDetails hotelEntity) {
        long savingPerc = (long) (priceDetail.getSavingPerc());
        String savingPercText = null;
        if (savingPerc != 0.0 && hotelEntity.isGroupBookingPrice()) {
            savingPercText = polyglotService.getTranslatedData(Utility.getGroupPriceTextOrSavingPercKey(SAVING_PERC_TEXT, searchHotelsRequest != null ? searchHotelsRequest.getClient() : "")).replace(SAVING_PERC, String.valueOf(savingPerc));
        }
        return savingPercText;
    }

    private String createPriceDisplayMsgAsPerRoomsNights(ListingSearchRequest searchHotelsRequest, HotelDetails hotelEntity) {
        int nightCount = 0;
        int roomCount = hotelEntity.getTotalRoomCount();
        String checkIn = null;
        String checkOut = null;
        if (searchHotelsRequest != null && searchHotelsRequest.getSearchCriteria() != null) {
            checkOut = searchHotelsRequest.getSearchCriteria().getCheckOut();
            checkIn = searchHotelsRequest.getSearchCriteria().getCheckIn();
        }
        if (StringUtils.isNotBlank(checkIn) && StringUtils.isNotBlank(checkOut)) {
            nightCount = utility.getLOS(checkIn, checkOut);
        }
        String groupPriceDisplayText = StringUtils.EMPTY;
        if (hotelEntity.isAltAcco() && searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null &&
                FUNNEL_SOURCE_GROUP_BOOKING.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource())) {
            groupPriceDisplayText = commonResponseTransformer.getGroupPriceTextForGRPNTForAltAcco(nightCount);
        } else {
            groupPriceDisplayText = commonResponseTransformer.getGroupPriceTextForGRPNT(roomCount, nightCount);
        }
        return groupPriceDisplayText;
    }

    private CalendarCriteria buildCalendarCriteria(com.gommt.hotels.orchestrator.model.response.content.CalendarCriteria calendarCriteriaHES) {
        if (calendarCriteriaHES == null)
            return null;
        CalendarCriteria calendarCriteriaCG = new CalendarCriteria();
        calendarCriteriaCG.setAdvanceDays(calendarCriteriaHES.getAdvanceDays());
        calendarCriteriaCG.setAvailable(calendarCriteriaHES.isAvailable());
        calendarCriteriaCG.setMaxDate(calendarCriteriaHES.getMaxDate());
        calendarCriteriaCG.setMlos(calendarCriteriaHES.getMlos());
        return calendarCriteriaCG;
    }

    private SoldOutInfoCG buildSoldOutInfo(SoldOutInfo soldOutInfo) {
        if (soldOutInfo == null)
            return null;
        SoldOutInfoCG soldOutInfoCG = new SoldOutInfoCG();
        soldOutInfoCG.setSoldOutText(soldOutInfo.getSoldOutText());
        soldOutInfoCG.setSoldOutSubText(soldOutInfo.getSoldOutSubText());
        soldOutInfoCG.setSoldOutReason(soldOutInfo.getSoldOutReason());
        soldOutInfoCG.setSoldOutType(soldOutInfo.getType());
        return soldOutInfoCG;
    }

    private String buildOrientation(PersonalizedSectionDetails personalizedResponse, Map<String, String> expDataMap) {
        return utility.isExperimentOn(expDataMap, EXP_MYPARTNER_LISTING_HN) ? Constants.HN : personalizedResponse.getOrientation();
    }

    // TODO - MyPartner/MyBiz case
    /*private boolean checkIfExclusiveDeal(final PersonalizedSectionDetails personalizedResponse, final CommonModifierResponse commonModifierResponse) {
        return utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), MYPARTNER_EXCLUSIVE_DEAL)
                && utility.isMyPartner(commonModifierResponse)
                && RECOMMENDED_HOTELS_SECTION.equalsIgnoreCase(personalizedResponse.getName())
                && personalizedResponse.getHotels() != null && personalizedResponse.getHotels().stream()
                .filter(hotel -> hotel.getHotelPersuasions() != null)
                .flatMap(hotel -> ((Map<String, Map<String, Object>>) hotel.getHotelPersuasions()).entrySet().stream())
                .filter(persuasion -> persuasion.getKey().equalsIgnoreCase(PC_IMG_ANNOTATION_PLACEHOLDER)
                        && persuasion.getValue().get("data") != null
                        && !((List<Map<String, Object>>) persuasion.getValue().get("data")).isEmpty())
                .map(k -> ((List<Map<String, Object>>) k.getValue().get("data")).get(0).get("iconurl"))
                .anyMatch(iconUrl -> EXCLUSIVE_DEAL_IMAGE_URL.equalsIgnoreCase((String) iconUrl));
    }*/

    private boolean checkIfHorizontalSection(final PersonalizedSectionDetails personalizedResponse, final CommonModifierResponse commonModifierResponse) {
        if (utility.isMyPartner(commonModifierResponse)
                && SIMILAR_HOTELS.equalsIgnoreCase(personalizedResponse.getName())) {
            final String pokus = commonModifierResponse.getExpDataMap().get(MYPARTNER_HIGH_GRASP_HOTELS);
            return pokus != null && !MYPARTNER_HG_NOT_SHOW.equalsIgnoreCase(pokus);
        }
        return false;
    }

    private boolean checkIfHorizontalSection(String sectionName, int hotelSize, DeviceDetails deviceDetails) {
        String deviceType = deviceDetails != null ? deviceDetails.getBookingDevice() : null;
        if (PERSONALISED_PICKS_HOTELS.equalsIgnoreCase(sectionName)) {

            return (Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(deviceType) && hotelSize >= minSimilarHotels) ||
                    (DEVICE_OS_ANDROID.equalsIgnoreCase(deviceType) || Constants.DEVICE_OS_IOS.equalsIgnoreCase(deviceType));
        }
        return false;
    }

    private void overridePersonalizedSectionHeadingForDirectHotelSearch(SearchHotelsRequest searchHotelsRequest, PersonalizedSection personalizedSection) {
        MatchMakerRequest matchMakerDetails = null;
        String funnelSource = null;
        if (searchHotelsRequest != null) {
            if (searchHotelsRequest.getMatchMakerDetails() != null) {
                matchMakerDetails = searchHotelsRequest.getMatchMakerDetails();
            }
            if (searchHotelsRequest.getRequestDetails() != null && searchHotelsRequest.getRequestDetails().getFunnelSource() != null) {
                funnelSource = searchHotelsRequest.getRequestDetails().getFunnelSource();
            }
        }
        if (matchMakerDetails != null && CollectionUtils.isNotEmpty(matchMakerDetails.getHotels())
                && null != matchMakerDetails.getHotels().get(0) && null != matchMakerDetails.getHotels().get(0).getHotelId()) {
            if (FUNNEL_SOURCE_CORPBUDGET.equalsIgnoreCase(funnelSource)) {
                personalizedSection.setHeading(polyglotService.getTranslatedData(Constants.CORP_BUDGET_PROPERTIES_TEXT));
            } else {
                personalizedSection.setHeading(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_RECOMMENDED_PROPERTIES_NEAR_THIS_PROPERTY));
            }
        }
    }

    private ToolTip buildMyBizAssuredToolTip() {
        ToolTip toolTip = new ToolTip();
        toolTip.setHeading(polyglotService.getTranslatedData(ConstantsTranslation.WHY_MYBIZ_ASSURED_TEXT));
        toolTip.setIconType(myBizToolTipIconType);
        toolTip.setData(new ArrayList<>());
        toolTip.getData().add(polyglotService.getTranslatedData(ConstantsTranslation.RATED_HIGH_BT));
        toolTip.getData().add(polyglotService.getTranslatedData(ConstantsTranslation.GST_INVOICE_ASSURANCE_TEXT));
        toolTip.getData().add(polyglotService.getTranslatedData(ConstantsTranslation.BPG_TEXT));
        return toolTip;
    }

    private void buildFilterCardForMyBizAndNonMyBizProperties(PersonalizedSectionDetails perResponse, PersonalizedSection personalizedSection) {
        personalizedSection.setFilterInfo(buildFilterInfo());
        personalizedSection.setBottomSheet(buildBottomSheet(perResponse));
    }

    private SectionFeature buildFilterInfo() {
        SectionFeature filterInfo = new SectionFeature();
        filterInfo.setIconUrl(myBizAssuredUrl);
        filterInfo.setText(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_ASSURED_FILTER_CARD_TEXT));
        filterInfo.setIconType("checkbox");
        filterInfo.setActionTitle(polyglotService.getTranslatedData(ConstantsTranslation.KNOW_MORE));
        List<Filter> filterCriteria = new ArrayList<>();
        Filter filter = new Filter();
        filter.setFilterGroup(FilterGroup.HOTEL_CATEGORY);
        filter.setFilterValue(Constants.MyBiz_Assured);
        filter.setFilterRange(null);
        filter.setRangeFilter(false);
        filterCriteria.add(filter);
        filterInfo.setFilterCriteria(filterCriteria);
        return filterInfo;
    }

    protected String getSeoPersuasionText(boolean oddHotel, boolean viewOnMap, HotelDetails hotelEntity, boolean seoDS, String seoCohort) {
        //TODO Complete for SEO case
        return null;
    }

    public boolean isWalletSurgePersuasionEnabled(JSONObject persuasionData) {
        return persuasionData.has(PERSUASION_KEY)
                && WALLET_SURGE_PERSUASION_KEY.equalsIgnoreCase(persuasionData.optString(PERSUASION_KEY))
                && persuasionData.has(TIMER)
                && persuasionData.getJSONObject(TIMER).has(EXPIRY)
                && persuasionData.getJSONObject(TIMER).get(EXPIRY) instanceof Integer
                && (Integer) persuasionData.getJSONObject(TIMER).get(EXPIRY) == 1;
    }

    protected void addBookingConfirmationPersuasionForMobile(HotelDetails hotelEntity) {
        PersuasionObject specialFarePersuasion = new PersuasionObject();
        PersuasionData persuasionData = new PersuasionData();
        Map<String, PersuasionData> persuasionConfigMap = specialFarePersuasionConfigMap.get(Constants.CLIENT_APPS);
        PersuasionData persuasionStyleConfig = MapUtils.isNotEmpty(persuasionConfigMap) ? persuasionConfigMap.get(Persuasions.SPECIAL_FARE_PERSUASION.getPlaceholderIdApps()) : new PersuasionData();
        persuasionData.setPersuasionType(DISCOUNTS);

        String text = polyglotService.getTranslatedData(ConstantsTranslation.BOOKING_CONFIRMATION_TEXT_LISTING_MOBILE);
        text = StringUtils.replace(text, NO_OF_HOURS_PLACEHOLDER, String.valueOf(noOfHoursForConfirmation));
        persuasionData.setText(text);

        persuasionData.setHasAction(false);
        Button button = new Button();
        button.setActionType(BOTTOMSHEET);
        ButtonInfo buttonInfo = new ButtonInfo();
        String header = polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_FARE_TAG_MOBILE);
        //TODO - myPartner/myBiz case
        /*hotelEntity.setCorpAlias(hotelEntity.getCorpAlias() != null ? hotelEntity.getCorpAlias() : SPECIAL);
        header = StringUtils.replace(header, "{CORP_ALIAS}", hotelEntity.getCorpAlias());*/
        buttonInfo.setHeader(header);
        String buttonInfoText = polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_FARE_TEXT_MOBILE);
        buttonInfoText = StringUtils.replace(buttonInfoText, NO_OF_HOURS_PLACEHOLDER, String.valueOf(noOfHoursForConfirmation));
        buttonInfo.setText(buttonInfoText);
        button.setInfo(buttonInfo);
        persuasionData.setButton(button);

        PersuasionStyle style = new PersuasionStyle();
        BeanUtils.copyProperties(persuasionStyleConfig.getStyle() != null ? persuasionStyleConfig.getStyle() : new PersuasionStyle(), style);
        persuasionData.setStyle(style);

        persuasionData.setHtml(true);
        persuasionData.setIconurl(persuasionStyleConfig.getIconurl());

        specialFarePersuasion.setData(Collections.singletonList(persuasionData));
        specialFarePersuasion.setPlaceholder(Persuasions.SPECIAL_FARE_PERSUASION.getPlaceholderIdApps());
        specialFarePersuasion.setTemplate(DEAL_BOX_IMAGE_TEXT_TEMPLATE);
        specialFarePersuasion.setTemplateType(DEFAULT);

        PersuasionStyle topLevelStyle = new PersuasionStyle();
        BeanUtils.copyProperties(persuasionStyleConfig.getTopLevelStyle() != null ? persuasionStyleConfig.getTopLevelStyle() : new PersuasionStyle(), topLevelStyle);
        specialFarePersuasion.setStyle(topLevelStyle);

        if (hotelEntity.getHotelPersuasions() == null)
            hotelEntity.setHotelPersuasions(new HashMap<>());
        ((Map<Object, Object>) hotelEntity.getHotelPersuasions()).put(Persuasions.SPECIAL_FARE_PERSUASION.getPlaceholderIdApps(), specialFarePersuasion);
    }

    private FetchedWindow buildFetchedWindow(String lastFetchedWindowInfo) {
        if (StringUtils.isNotEmpty(lastFetchedWindowInfo)) {
            List<String> lastFetchedWindowList = Arrays.stream(lastFetchedWindowInfo.split(HASH_SEPARATOR)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(lastFetchedWindowList) && lastFetchedWindowList.size() > 3) {
                String sectionType = getSectionType(lastFetchedWindowList.get(3));
                return FetchedWindow.builder()
                        .soldOutBits(lastFetchedWindowList.get(0))
                        .startIndex(Integer.valueOf(lastFetchedWindowList.get(1)))
                        .endIndex(Integer.valueOf(lastFetchedWindowList.get(2)))
                        .nearByCalled(!"RANKING".equalsIgnoreCase(sectionType))
                        .sectionSequenceUsedToBuildBitset(sectionType).build();
            }
        }
        return null;
    }

    private String getSectionType(String sectionValue) {
        sectionValue = StringUtils.isNotEmpty(sectionValue) ? sectionValue.toUpperCase() : "RANKING";
        switch (sectionValue) {
            case "TRUE":
                return "NEARBY";
            case "FALSE":
                return "RANKING";
            default:
                return sectionValue;
        }
    }

    private String appendMmAreaTag(Tags areaObj) {
        // this function is to create the value that is to be used in mmAreaTag url parameter
        return new StringBuilder(areaObj.getTagDescription().replaceAll("\\s", "%20")).append(PIPE_UNICODE).append(areaObj.getTagAreaId()).toString();
    }

    private String appendMmPoiTag(LatLngObject poiObj) {
        // this function is to create the value that is to be used in mmPoiTag url parameter
        // LPOI should be appended to the deepLink in case of GPOI's case and POI in case of normal location poi which is identified by the presence of poiId node
        String mmPoiTagValue = new StringBuilder(poiObj.getPoiId() != null ? "POI" : "LPOI")
                .append(PIPE_UNICODE)
                .append(poiObj.getName().replaceAll("\\s", SPACE_UNICODE))
                .append(PIPE_UNICODE)
                .append(poiObj.getPoiId() != null ? poiObj.getPoiId() : poiObj.getPlaceId()).toString();

        if (poiObj.getLatitude() > 0.0 && poiObj.getLongitude() > 0.0) {
            mmPoiTagValue = new StringBuilder(mmPoiTagValue)
                    .append(PIPE_UNICODE)
                    .append(poiObj.getLatitude())
                    .append(PIPE_UNICODE)
                    .append(poiObj.getLongitude()).toString();
        }
        return mmPoiTagValue;
    }

    public String appendAreaOrPoiUrlParameter(String deepLink, SearchHotelsRequest inputBO) {
        //this function appends the required url parameters to create a recent search Listing deeplink
        if (inputBO.getMatchMakerDetails() != null && CollectionUtils.isNotEmpty(inputBO.getMatchMakerDetails().getHotels())) {
            //Adding topHtlId in recent search deepLink in case of direct hotel search
            deepLink = deepLink + "&topHtlId=" + inputBO.getMatchMakerDetails().getHotels().get(0).getHotelId();

            deepLink += getQueryParameter(MPN, String.valueOf(inputBO.getFeatureFlags().getMaskedPropertyName()!=null?inputBO.getFeatureFlags().getMaskedPropertyName():false));
        }else{
            if(inputBO.getMatchMakerDetails()!=null && inputBO.getMatchMakerDetails().getSelectedTags()!=null){
                for(Tags areaObj : inputBO.getMatchMakerDetails().getSelectedTags()){
                    if(areaObj.isPrimary() && areaObj.getTagDescription()!=null && areaObj.getTagAreaId()!=null){
                        // adding mmAreaTag in deepLink in case when a selected tag is Primary
                        deepLink = new StringBuilder(deepLink).append(Constants.AMP).append(Constants.MM_AREA_TAG).append(Constants.EQUI).append(appendMmAreaTag(areaObj)).toString();
                        break;
                    }
                }
            }
            if (inputBO.getMatchMakerDetails() != null && inputBO.getMatchMakerDetails().getLatLng() != null) {
                for (LatLngObject poiObj : inputBO.getMatchMakerDetails().getLatLng()) {
                    if (poiObj.isPrimary() && (poiObj.getPoiId() != null || poiObj.getPlaceId() != null)) {
                        // adding mmPoiTag in deepLink in case when a lat/long is Primary
                        deepLink = new StringBuilder(deepLink).append(Constants.AMP).append(Constants.MM_POI_TAG).append(Constants.EQUI).append(appendMmPoiTag(poiObj)).toString();
                        break;
                    }
                }
            }
        }
        return deepLink;
    }

    public String prepareRecentDeeplink(SearchHotelsRequest searchHotelsRequest, String deeplink) {
        String listingSharingUrl = prepareListingSharingUrl(searchHotelsRequest, deeplink, false, false, null);
        listingSharingUrl = appendAreaOrPoiUrlParameter(listingSharingUrl, searchHotelsRequest);
        listingSharingUrl += getRscValue(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest.getExpDataMap());
        return modifyDeeplinkDomain(listingSharingUrl, searchHotelsRequest);
    }

    // Deeplink added as method param to make method dynamic
    public String prepareListingSharingUrl(SearchHotelsRequest searchHotelsRequest, String deeplink, boolean appendFilter, boolean checkAvailability, LocationDetails locationDetails) {

        // Handle potential nulls in search criteria and room stay candidates
        SearchHotelsCriteria searchCriteria = searchHotelsRequest.getSearchCriteria();
        String roomStayParam = "";
        if (searchCriteria != null && searchCriteria.getRoomStayCandidates() != null) {
            List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidatesHES = utility.buildRoomStayDistribution(searchCriteria.getRoomStayCandidates(), searchHotelsRequest.getExpDataMap());
            roomStayParam = buildRoomStayCandidateFromSearchWrapperS(roomStayCandidatesHES);
        }

        // Prepare basic sharing URL
        String deepLink = prepareBasicSharingUrl(
                roomStayParam,
                searchCriteria != null ? searchCriteria.getCheckIn() : "",
                searchCriteria != null ? searchCriteria.getCheckOut() : "",
                searchCriteria != null && searchCriteria.getCityName()!=null ? searchCriteria.getCityName() : locationDetails != null ? locationDetails.getCityName() : "",
                searchCriteria != null && searchCriteria.getLocationId() != null? searchCriteria.getLocationId() : locationDetails != null ? locationDetails.getId() : "",
                searchCriteria != null && searchCriteria.getCountryCode() != null? searchCriteria.getCountryCode() : locationDetails != null ? locationDetails.getCountryId() : "",
                deeplink,
                searchCriteria != null &&  searchCriteria.getLocationType() != null ? searchCriteria.getLocationType() : locationDetails != null ? locationDetails.getType() : "",
                searchCriteria != null ? searchCriteria.getCurrency() : "",
                searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getSiteDomain() : "",
                searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getPageContext() : "",
                searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getFunnelSource() : "",
                searchCriteria != null ? searchCriteria.getParentLocationId() : "",
                searchCriteria != null ? searchCriteria.getParentLocationType() : "",
                checkAvailability
        );

        // Append filter data to the deepLink if needed
        if (appendFilter && CollectionUtils.isNotEmpty(searchHotelsRequest.getFilterCriteria())) {
            deepLink = appendFiltersDataToDeepLink(searchHotelsRequest.getFilterCriteria(), deepLink);
        }

        // Return the final deepLink
        return deepLink;
    }

    protected String getRscValue(SearchHotelsCriteria searchCriteria, Map<String, String> expData) {
        // Handle RSC value if room stay candidates are distributed
        if (searchCriteria != null && utility.isDistributeRoomStayCandidates(searchCriteria.getRoomStayCandidates(), expData)) {
            String rscValue = utility.buildRscValue(searchCriteria.getRoomStayCandidates());
            if (StringUtils.isNotEmpty(rscValue)) {
                return getQueryParameter(RSC, rscValue);
            }
        }
        return "";
    }



    public String prepareHotelSharingUrl(String hotelId, SearchHotelsRequest searchHotelsRequest, String rscValue, List<RoomStayCandidate> distributedRoomStayCandidateList, boolean isMaskedPropertyName) {
        // Handle nulls for mandatory fields by setting empty strings for null values
        hotelId = hotelId == null ? "" : hotelId;

        if (searchHotelsRequest == null || searchHotelsRequest.getSearchCriteria() == null) {
            // Return a URL with empty values if searchHotelsRequest or its criteria is null
            return hotelLevelSharingUrl + hotelId;
        }

        SearchHotelsCriteria searchCriteria = searchHotelsRequest.getSearchCriteria();
        RequestDetails requestDetails = searchHotelsRequest.getRequestDetails();

        String idContext = searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null
                ? searchHotelsRequest.getRequestDetails().getIdContext() : "";

        // Format check-in and check-out dates; if null, use an empty string
        String checkInDate = dateUtil.getDateFormatted(searchCriteria.getCheckIn(), DateUtil.YYYY_MM_DD, MMDDYYYY);
        String checkOutDate = dateUtil.getDateFormatted(searchCriteria.getCheckOut(), DateUtil.YYYY_MM_DD, MMDDYYYY);

        checkInDate = checkInDate == null ? "" : checkInDate;
        checkOutDate = checkOutDate == null ? "" : checkOutDate;

        String roomStayParam = "";
        if (CollectionUtils.isNotEmpty(distributedRoomStayCandidateList)) {
            roomStayParam = buildRoomStayCandidateFromSearchWrapperS(distributedRoomStayCandidateList);
        } else {
            roomStayParam = buildRoomStayCandidateFromSearchWrapper(searchHotelsRequest.getSearchCriteria().getRoomStayCandidates());
        }

        // Using StringBuilder to efficiently build the sharing URL
        StringBuilder sharingUrlBuilder = new StringBuilder();
        sharingUrlBuilder.append(hotelLevelSharingUrl).append(hotelId)
                .append(AMP).append(CITY).append(EQUI).append(safeAppend(searchCriteria.getLocationId()))
                .append(AMP).append(COUNTRY).append(EQUI).append(safeAppend(searchCriteria.getCountryCode()))
                .append(AMP).append(ROOM_STAY_QUAL).append(EQUI).append(safeAppend(roomStayParam))
                .append(AMP).append(CHECK_IN).append(EQUI).append(checkInDate)
                .append(AMP).append(CHECK_OUT).append(EQUI).append(checkOutDate)
                .append(AMP).append(COMPONENT).append(EQUI).append(APP_SHARE_HOT_DETAILS);

        // Append optional parameters with empty string handling
        sharingUrlBuilder.append(AMP).append(LOCUS_ID).append(EQUI).append(safeAppend(searchCriteria.getLocationId()))
                .append(AMP).append(LOCUS_LOCATION_TYPE).append(EQUI).append(safeAppend(searchCriteria.getLocationType()));

        if (requestDetails != null) {
            sharingUrlBuilder.append(AMP).append(REGION_URL_PARAM).append(EQUI).append(safeAppend(requestDetails.getSiteDomain()))
                    .append(AMP).append(FUNNEL_NAME).append(EQUI).append(safeAppend(requestDetails.getFunnelSource()));
        }

        // Add RSC value if not empty, otherwise append an empty value
        sharingUrlBuilder.append(appendPriceByInDeepLink(requestDetails, searchHotelsRequest.getExpDataMap()));
        sharingUrlBuilder.append(AMP).append(RSC).append(EQUI).append(safeAppend(rscValue));

        //AddCheckinTime
        if(searchCriteria.getSlot()!=null && searchCriteria.getSlot().getTimeSlot()!=null){
            sharingUrlBuilder.append(AMP).append(CHECK_IN_TIME).append(EQUI).append(safeAppend(String.valueOf(searchCriteria.getSlot().getTimeSlot())));
        }
        sharingUrlBuilder.append(AMP).append(MPN).append(EQUI).append(isMaskedPropertyName);
        if(CORP_ID_CONTEXT.equalsIgnoreCase(idContext)) {
            sharingUrlBuilder.append(AMP).append(IS_CORPORATE).append(EQUI).append(true);
        }
        // Return the final URL
        return sharingUrlBuilder.toString();
    }

    public String appendPriceByInDeepLink(RequestDetails requestDetails, Map<String, String> expData) {
        String priceByQueryParam = "";
        if (requestDetails != null && isGroupBookingFunnel(requestDetails.getFunnelSource()) && expData != null && StringUtils.isNotEmpty(expData.get("PDO"))) {
            priceByQueryParam = Constants.AND_SEPARATOR + Constants.DEEPLINK_PRICE_BY_TEXT.toLowerCase() + Constants.PR_SEPARATOR + expData.get("PDO");
        }
        return priceByQueryParam;
    }

    private String safeAppend(String value) {
        // Return empty string if value is null, otherwise return the value
        return value == null ? "" : value;
    }

    protected String prepareBasicSharingUrl(String roomStayParam, String checkin, String checkout, String cityName, String cityCode,
                                          String countryCode, String rawDeepLink, String locationType, String currency, String siteDomain,
                                          String pageContext, String funnelSource, String parentLocationId, String parentLocationType, boolean checkAvailability) {

        checkin = dateUtil.getDateFormatted(checkin, DateUtil.YYYY_MM_DD, MMDDYYYY);
        checkout = dateUtil.getDateFormatted(checkout, DateUtil.YYYY_MM_DD, MMDDYYYY);
        String deepLink = MessageFormat.format(rawDeepLink, checkin, checkout, cityCode, countryCode, roomStayParam, checkAvailability, currency);

        if (cityName != null) {
            deepLink = deepLink + Constants.AND_SEPARATOR + Constants.SEARCHTEXT_URL_PARAM + Constants.PR_SEPARATOR + getEncodedUrl(cityName);
        }

        deepLink = deepLink + Constants.AND_SEPARATOR + Constants.LOCUSID_URL_PARAM + Constants.PR_SEPARATOR + cityCode;
        deepLink = deepLink + Constants.AND_SEPARATOR + Constants.LOCUSTYPE_URL_PARAM + Constants.PR_SEPARATOR + locationType;

        if (StringUtils.isNotEmpty(siteDomain)) {
            deepLink = deepLink + Constants.AND_SEPARATOR + REGION_URL_PARAM + Constants.PR_SEPARATOR + siteDomain.toLowerCase();
        }
        if (FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource)) {
            deepLink += Constants.AND_SEPARATOR + Constants.HOMESTAY_URL_PARAM + Constants.PR_SEPARATOR + "true";
        }
        if (Constants.STAYCATION.equalsIgnoreCase(pageContext))
            deepLink += Constants.AND_SEPARATOR + Constants.STAYCATION_FILTER_URL_PARAM + Constants.PR_SEPARATOR + "true";

        if (StringUtils.isNotBlank(funnelSource)) {
            deepLink += getQueryParameter(FUNNEL_NAME, funnelSource);
        }
        if (StringUtils.isNotEmpty(parentLocationId) && StringUtils.isNotEmpty(parentLocationType)) {
            deepLink = deepLink + Constants.AND_SEPARATOR + PARENT_LOCATION_ID + Constants.PR_SEPARATOR + parentLocationId;
            deepLink = deepLink + Constants.AND_SEPARATOR + PARENT_LOCATION_type + Constants.PR_SEPARATOR + parentLocationType;
        }

        return deepLink;
    }

    public String getQueryParameter(String queryParam, String value) {
        return Constants.AND_SEPARATOR + queryParam + Constants.PR_SEPARATOR + value;
    }



    private String buildDetailDeepLinkUrl(Hotel hotel, HotelDetails hotelEntity, SearchHotelsRequest searchHotelsRequest, String rscValue, List<RoomStayCandidate> distributedRoomStayCandidateList) {
        String currency = Optional.ofNullable(searchHotelsRequest.getSearchCriteria()).map(SearchCriteria::getCurrency).orElse(EMPTY_STRING);

        // Handle check-in and check-out dates; use empty strings if unavailable
        String checkIn = searchHotelsRequest.getSearchCriteria().getCheckIn() != null
                ? dateUtil.getDateFormatted(searchHotelsRequest.getSearchCriteria().getCheckIn(), DateUtil.YYYY_MM_DD, MMDDYYYY) : "";
        String checkOut = searchHotelsRequest.getSearchCriteria().getCheckOut() != null
                ? dateUtil.getDateFormatted(searchHotelsRequest.getSearchCriteria().getCheckOut(), DateUtil.YYYY_MM_DD, MMDDYYYY) : "";
        String city = Optional.ofNullable(searchHotelsRequest.getSearchCriteria()).map(SearchCriteria::getLocationId).orElse(EMPTY_STRING);
        String country = Optional.ofNullable(searchHotelsRequest.getSearchCriteria()).map(SearchCriteria::getCountryCode).orElse(EMPTY_STRING);
        String locusId = Optional.ofNullable(searchHotelsRequest.getSearchCriteria()).map(SearchCriteria::getLocationId).orElse(Optional.ofNullable(hotel.getLocationDetail()).map(LocationDetail::getId).orElse(EMPTY_STRING));
        String locusType = Optional.ofNullable(searchHotelsRequest.getSearchCriteria()).map(SearchCriteria::getLocationType).orElse(Optional.ofNullable(hotel.getLocationDetail()).map(LocationDetail::getType).orElse(EMPTY_STRING));
        String idContext = Optional.ofNullable(searchHotelsRequest.getRequestDetails()).map(RequestDetails::getIdContext).orElse(EMPTY_STRING);
        String roomStayParam = "";
        if (CollectionUtils.isNotEmpty(distributedRoomStayCandidateList)) {
            roomStayParam = buildRoomStayCandidateFromSearchWrapperS(distributedRoomStayCandidateList);
        } else {
            roomStayParam = buildRoomStayCandidateFromSearchWrapper(searchHotelsRequest.getSearchCriteria().getRoomStayCandidates());
        }

        /*
        Sample URLS
            https://www.makemytrip.com/hotels/hotel-details?
             hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&openDetail=true&currency={5}

        https://www.makemytrip.com/hotels/hotel-details?
        * hotelId=202001031606575931&checkin=01162025&checkout=01172025&country=IN&city=RGNCR
        * &openDetail=true&currency=INR&roomStayQualifier=4e1e7e3e1e12e&locusId=RGNCR&
        * locusType=region&filterData=STAR_RATING%7C4&region=in&viewType=BUDGET&
        * funnelName=HOTELS&rsc=2e7e2e12e7e
        *    */
        String detailDeeplink = basicDetailDeeplink;
        if (searchHotelsRequest.getSearchCriteria().getUserGlobalInfo() != null && "GLOBAL".equalsIgnoreCase(searchHotelsRequest.getSearchCriteria().getUserGlobalInfo().getEntityName())) {
            detailDeeplink = basicDetailDeeplinkGlobal;
        }
        String format = MessageFormat.format(detailDeeplink, hotel.getId(),
                checkIn, checkOut, country, city, currency,roomStayParam,locusId,locusType);

        //Append Filters
        format = appendFiltersDataToDeepLink(searchHotelsRequest.getFilterCriteria(), format);

        // Append region from MDC if available, fallback to default region
        String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
        if (StringUtils.isNotEmpty(region)) {
            format += Constants.AND_SEPARATOR + REGION_URL_PARAM + Constants.PR_SEPARATOR + region.toLowerCase();
        } else {
            format += Constants.AND_SEPARATOR + REGION_URL_PARAM + Constants.PR_SEPARATOR + Constants.WALLET_REGION_IND;
        }

        if (StringUtils.isNotEmpty(hotelEntity.getHotelCategory())) {
            format += getQueryParameter(Constants.VIEW_TYPE, hotelEntity.getHotelCategory());
        }

        // Append funnel source if available
        if (searchHotelsRequest.getRequestDetails() != null && StringUtils.isNotBlank(searchHotelsRequest.getRequestDetails().getFunnelSource())) {
            format += getQueryParameter(FUNNEL_NAME, searchHotelsRequest.getRequestDetails().getFunnelSource());
        }

        // Append RSC value if available
        if (StringUtils.isNotEmpty(rscValue)) {
            format += getQueryParameter(RSC, rscValue);
        }

        //AddCheckinTime
        if(searchHotelsRequest.getSearchCriteria()!=null && searchHotelsRequest.getSearchCriteria().getSlot()!=null && searchHotelsRequest.getSearchCriteria().getSlot().getTimeSlot()!=null){
            format+= getQueryParameter(CHECK_IN_TIME,String.valueOf(searchHotelsRequest.getSearchCriteria().getSlot().getTimeSlot()));
        }

        format += getQueryParameter(MPN,String.valueOf(hotelEntity.isMaskedPropertyName()));
        if(CORP_ID_CONTEXT.equalsIgnoreCase(idContext)) {
            format += getQueryParameter(IS_CORPORATE, "true");
        }

        // Append oldBookingId if AlternateBookingInfo is present with alternateBooking true
        if (searchHotelsRequest != null &&
            searchHotelsRequest.getAlternateBookingInfo() != null &&
            searchHotelsRequest.getAlternateBookingInfo().isAlternateBooking() &&
            StringUtils.isNotEmpty(searchHotelsRequest.getAlternateBookingInfo().getOldBookingId())) {
            format += getQueryParameter("oldBookingId", searchHotelsRequest.getAlternateBookingInfo().getOldBookingId());
        }

        return modifyDeeplinkDomain(format, searchHotelsRequest);
    }

    public String buildRoomStayCandidateFromSearchWrapper(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        StringBuilder builder = new StringBuilder();
        if (CollectionUtils.isEmpty(roomStayCandidates)) {
            return builder.toString();
        }
        for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate : roomStayCandidates) {
            int adultCount = roomStayCandidate.getAdultCount();
            int childCount = roomStayCandidate.getChildAges() != null ? roomStayCandidate.getChildAges().size() : 0;
            builder.append(adultCount);
            builder.append(Constants.RSQ_SPLITTER);
            builder.append(childCount);
            builder.append(Constants.RSQ_SPLITTER);
            if (CollectionUtils.isNotEmpty(roomStayCandidate.getChildAges())) {
                for (int age : roomStayCandidate.getChildAges()) {
                    if (age >= 0 && age <= 12) {
                        builder.append(age);
                        builder.append(Constants.RSQ_SPLITTER);
                    }
                }
            }
        }
        return builder.toString();
    }

    public String buildRoomStayCandidateFromSearchWrapperS(@Nullable List<RoomStayCandidate> roomStayCandidates) {
        StringBuilder builder = new StringBuilder();
        if (CollectionUtils.isEmpty(roomStayCandidates)) {
            return builder.toString();
        }

        for (RoomStayCandidate roomStayCandidate : roomStayCandidates) {
            for (GuestCount guestCount : roomStayCandidate.getGuestCounts()) {
                if (guestCount == null)
                    continue;
                int adultCount = Integer.parseInt(guestCount.getCount());
                int childCount = 0;
                if (CollectionUtils.isNotEmpty(guestCount.getAges()))
                    childCount = guestCount.getAges().size();
                builder.append(adultCount);
                builder.append(Constants.RSQ_SPLITTER);
                builder.append(childCount);
                builder.append(Constants.RSQ_SPLITTER);
                if (CollectionUtils.isNotEmpty(guestCount.getAges())) {
                    for (int age : guestCount.getAges()) {
                        if (age >= 0 && age <= 12) {
                            builder.append(age);
                            builder.append(Constants.RSQ_SPLITTER);
                        }
                    }
                }
            }
        }
        return builder.toString();
    }

    public static String getEncodedUrl(String url) {
        String encodedString = "";
        try {
            if (StringUtils.isNotBlank(url))
                encodedString = URLEncoder.encode(url, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
        } catch (Exception e) {
            LOGGER.warn("Error while encoding url: {}", url);
        }
        return encodedString;
    }

    protected String getAppDeeplinkCrossSell(SearchHotelsRequest searchHotelsRequest) {
        String deepLink = listingApplDeeplink == null ? "" : listingApplDeeplink;
        if (isScionRequest(searchHotelsRequest) && isDeskTopRequest(searchHotelsRequest)) {
            deepLink = listinglDeeplink;
            if (searchHotelsRequest.getSearchCriteria().getUserGlobalInfo() != null && "GLOBAL".equalsIgnoreCase(searchHotelsRequest.getSearchCriteria().getUserGlobalInfo().getEntityName())) {
                deepLink = listinglDeeplinkGlobal;
                if (searchHotelsRequest.getRequestDetails() != null && searchHotelsRequest.getRequestDetails().getSiteDomain() != null && REGION_SA.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getSiteDomain())) {
                    deepLink = deepLink.replace(WWW_SUBDOMAIN, SA_SUBDOMAIN);
                }
            }
        }
        return deepLink;
    }

    protected String getDetailDeeplink(SearchHotelsRequest searchHotelsRequest) {
        String deepLink = hotelLevelAppDeepLink == null ? "" : hotelLevelAppDeepLink;
        if (isScionRequest(searchHotelsRequest) && isDeskTopRequest(searchHotelsRequest)) {
            deepLink = basicDetailDeeplink;
            if (searchHotelsRequest.getSearchCriteria().getUserGlobalInfo() != null && "GLOBAL".equalsIgnoreCase(searchHotelsRequest.getSearchCriteria().getUserGlobalInfo().getEntityName())) {
                deepLink = basicDetailDeeplinkGlobal;
                if (searchHotelsRequest.getRequestDetails() != null && searchHotelsRequest.getRequestDetails().getSiteDomain() != null && REGION_SA.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getSiteDomain())) {
                    deepLink = deepLink.replace(WWW_SUBDOMAIN, SA_SUBDOMAIN);
                }
            }
        }
        return deepLink;
    }


    public String buildHotelLevelAppDeepLink(SearchHotelsRequest searchHotelsRequest, Hotel hotelEntity, String rscValue, List<RoomStayCandidate> distributedRoomStayCandidateList, HotelDetails hotelDetails, LocationDetails locusData, String url) {
        // Initialize detail URL and handle potential nulls
        if (hotelEntity == null && hotelDetails !=null) {
            hotelEntity = new Hotel();
            LocationDetails locationDetails = hotelDetails.getLocation();
            if(locationDetails != null) {
                LocationDetail locationDetail = new LocationDetail(locationDetails.getId(), locationDetails.getCityName(), locationDetails.getType(), locationDetails.getCountryId(), locationDetails.getCountryName());
                hotelEntity.setId(hotelDetails.getId());
                hotelEntity.setLocationDetail(locationDetail);
                hotelEntity.setMaskedPropertyName(hotelDetails.isMaskedPropertyName());
            }
        }

        // Safely extract the room stay parameters from the search request
        SearchHotelsCriteria searchCriteria = searchHotelsRequest != null && searchHotelsRequest.getSearchCriteria() != null
                ? searchHotelsRequest.getSearchCriteria() : new SearchHotelsCriteria();
        String idContext = searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null
                ? searchHotelsRequest.getRequestDetails().getIdContext() : "";

        String roomStayParam = "";
        if (CollectionUtils.isNotEmpty(distributedRoomStayCandidateList)) {
            roomStayParam = buildRoomStayCandidateFromSearchWrapperS(distributedRoomStayCandidateList);
        } else {
            roomStayParam = buildRoomStayCandidateFromSearchWrapper(searchCriteria.getRoomStayCandidates());
        }
        // Handle check-in and check-out dates; use empty strings if unavailable
        String checkin = searchCriteria.getCheckIn() != null
                ? dateUtil.getDateFormatted(searchCriteria.getCheckIn(), DateUtil.YYYY_MM_DD, MMDDYYYY)
                : "";
        String checkout = searchCriteria.getCheckOut() != null
                ? dateUtil.getDateFormatted(searchCriteria.getCheckOut(), DateUtil.YYYY_MM_DD, MMDDYYYY)
                : "";

        // Default values for missing parameters
        String checkAvailability = isScionRequest(searchHotelsRequest)?"false":"true";
        String cityCode = searchCriteria.getCityCode() == null ? searchCriteria.getLocationId() : searchCriteria.getCityCode();
        if(StringUtils.isEmpty(cityCode) && locusData != null && StringUtils.isNotEmpty(locusData.getId())){
            cityCode = locusData.getId();
        }

        // Determine country code, fallback on search criteria country code if hotel location is missing
        String countryCode = hotelEntity != null && hotelEntity.getLocationDetail() != null
                && StringUtils.isNotEmpty(hotelEntity.getLocationDetail().getCountryId())
                ? hotelEntity.getLocationDetail().getCountryId()
                : searchCriteria.getCountryCode() == null ? "" : searchCriteria.getCountryCode();

        // Format the initial partial deep link
        String partialDeepLink = MessageFormat.format(url, "{0}",
                checkin,
                checkout,
                countryCode, cityCode, roomStayParam, searchCriteria.getCurrency() == null ? "" : searchCriteria.getCurrency(),
                checkAvailability);

        // Append location ID and location type if available
        if (hotelEntity != null && hotelEntity.getLocationDetail() !=null && hotelEntity.getLocationDetail().getType() != null && hotelEntity.getLocationDetail().getId() != null ) {
            partialDeepLink += Constants.AND_SEPARATOR + LOCUS_ID + Constants.PR_SEPARATOR + hotelEntity.getLocationDetail().getId();
            partialDeepLink += Constants.AND_SEPARATOR + LOCUS_LOCATION_TYPE + Constants.PR_SEPARATOR + hotelEntity.getLocationDetail().getType();
        }


        if (StringUtils.isNotBlank(searchCriteria.getLocationId()) && StringUtils.isNotBlank(searchCriteria.getLocationType())) {
            partialDeepLink += Constants.AND_SEPARATOR + LOCUS_ID + Constants.PR_SEPARATOR + searchCriteria.getLocationId();
            partialDeepLink += Constants.AND_SEPARATOR + LOCUS_LOCATION_TYPE + Constants.PR_SEPARATOR + searchCriteria.getLocationType();
        }

        if(StringUtils.isBlank(searchCriteria.getLocationId()) && locusData != null && StringUtils.isNotEmpty(locusData.getId())) {
            partialDeepLink += Constants.AND_SEPARATOR + LOCUS_ID + Constants.PR_SEPARATOR + locusData.getId();
            partialDeepLink += Constants.AND_SEPARATOR + LOCUS_LOCATION_TYPE + Constants.PR_SEPARATOR + locusData.getType();
        }

        // Format and finalize the deep link with the hotel ID
        String completeDeepLink = MessageFormat.format(partialDeepLink, hotelEntity != null ? hotelEntity.getId() : "");

        // Append filter criteria if available
        if (searchHotelsRequest != null && searchHotelsRequest.getFilterCriteria() != null) {
            completeDeepLink = appendFiltersDataToDeepLink(searchHotelsRequest.getFilterCriteria(), completeDeepLink);
        }

        // Append region from MDC if available, fallback to default region
        String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
        if (StringUtils.isNotEmpty(region)) {
            completeDeepLink += Constants.AND_SEPARATOR + REGION_URL_PARAM + Constants.PR_SEPARATOR + region.toLowerCase();
        } else {
            completeDeepLink += Constants.AND_SEPARATOR + REGION_URL_PARAM + Constants.PR_SEPARATOR + Constants.WALLET_REGION_IND;
        }

        // Append funnel source if available
        if (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null
                && StringUtils.isNotBlank(searchHotelsRequest.getRequestDetails().getFunnelSource())) {
            completeDeepLink += getQueryParameter(FUNNEL_NAME, searchHotelsRequest.getRequestDetails().getFunnelSource());
        }

        // Append RSC value if available
        if (StringUtils.isNotEmpty(rscValue)) {
            completeDeepLink += getQueryParameter(RSC, rscValue);
        }

        if (isScionRequest(searchHotelsRequest) && searchHotelsRequest.getRequestDetails() != null && searchHotelsRequest.getRequestDetails().getFunnelSource() != null
        && FUNNEL_SOURCE_SHORTSTAYS.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource())&& org.apache.commons.lang.StringUtils.isNotBlank(hotelEntity.getLocationDetail().getId())) {
            completeDeepLink = completeDeepLink + Constants.AND_SEPARATOR + Constants.SEARCHTEXT_URL_PARAM + Constants.PR_SEPARATOR + hotelEntity.getLocationDetail().getId();
        }
        completeDeepLink += getQueryParameter(MPN, String.valueOf(hotelEntity.isMaskedPropertyName()));
        if(CORP_ID_CONTEXT.equalsIgnoreCase(idContext)) {
            completeDeepLink += getQueryParameter(IS_CORPORATE, "true");
        }
        return completeDeepLink;
    }

    public boolean isScionRequest(SearchHotelsRequest searchHotelsRequest) {
        return searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null &&
                (Constants.REQUESTOR_SCION.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getRequestor()) || TRAFFIC_SOURCE_CROSSSELL.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getRequestor()));
    }

    public boolean isDeskTopRequest(SearchHotelsRequest searchHotelsRequest) {
        return searchHotelsRequest != null && searchHotelsRequest.getDeviceDetails() != null &&
                (Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(searchHotelsRequest.getDeviceDetails().getDeviceType()) || DEVICE_OS_PWA.equalsIgnoreCase(searchHotelsRequest.getDeviceDetails().getDeviceType()));
    }

    public String appendFiltersDataToDeepLink(List<Filter> filterCriteria, String deepLink) {
        if (CollectionUtils.isEmpty(filterCriteria)) {
            return deepLink; // Return the original deepLink if no filterCriteria is present
        }

        StringBuilder filters = new StringBuilder();
        for (Filter filter : filterCriteria) {
            if (filter.getFilterGroup() == null) {
                continue; // Skip filters with no FilterGroup
            }

            // Append the filter group name and the pipe splitter
            filters.append(filter.getFilterGroup().name()).append(Constants.PIPE_SPLITTER);

            // Check if the filter group is related to price
            if (filter.getFilterGroup().name().equals(FilterGroup.HOTEL_PRICE.name())
                    || filter.getFilterGroup().name().equals(FilterGroup.HOTEL_PRICE_BUCKET.name())) {

                // Initialize min and max values for price filters
                int minValue = Integer.MAX_VALUE;
                int maxValue = Integer.MIN_VALUE;

                // Update min and max values if a filter range is present
                if (filter.getFilterRange() != null) {
                    minValue = filter.getFilterRange().getMinValue();
                    maxValue = filter.getFilterRange().getMaxValue();
                }

                // If valid min and max values are found, append them
                if (minValue != Integer.MAX_VALUE && maxValue != Integer.MIN_VALUE) {
                    filters.append(minValue)
                            .append(Constants.HYPHEN)
                            .append(maxValue)
                            .append(Constants.COMMA);
                }
            } else {
                // Append the filter value if it exists
                if (StringUtils.isNotEmpty(filter.getFilterValue())) {
                    filters.append(filter.getFilterValue()).append(Constants.COMMA);
                }
            }

            // Remove the last comma and append the group splitter
            if (filters.length() > 0 && filters.charAt(filters.length() - 1) == Constants.COMMA.charAt(0)) {
                filters.deleteCharAt(filters.length() - 1); // Remove trailing comma
            }
            filters.append(Constants.DEEPLINK_FILTER_GROUP_SPLITTER); // Append filter group splitter
        }

        // Remove the last filter group splitter if present
        if (filters.length() > 0 && filters.charAt(filters.length() - 1) == Constants.DEEPLINK_FILTER_GROUP_SPLITTER.charAt(0)) {
            filters.deleteCharAt(filters.length() - 1); // Remove trailing group splitter
        }

        // Append filters to the deep link if available
        if (StringUtils.isNotBlank(filters.toString())) {
            return deepLink + Constants.AND_SEPARATOR + Constants.DEEPLINK_FILTER_DATA + Constants.PR_SEPARATOR
                    + getEncodedUrl(filters.toString());
        }

        // Return the original deepLink if no valid filters were appended
        return deepLink;
    }

    public ListingMapResponse convertListingMapResponse(ListingResponse listingResponse, SearchHotelsRequest searchHotelsRequest, CommonModifierResponse commonModifierResponse) {
        ListingMapResponse listingMapResponse = new ListingMapResponse();
        Map<String, String> expDataMap = commonModifierResponse.getExpDataMap();

        listingMapResponse.setHotelCount(listingResponse.getHotelCount());
        if (listingResponse.getLocation() != null) {
            LocationDetails locationDetails = listingResponse.getLocation();
            LocationDetail locationDetail = new LocationDetail(locationDetails.getId(), locationDetails.getCityName(), locationDetails.getType(), locationDetails.getCountryId(), locationDetails.getCountryName());
            listingMapResponse.setLocationDetail(locationDetail);
            listingMapResponse.setCityLocationDetail(locationDetail);
        }

        if (CollectionUtils.isNotEmpty(listingResponse.getPersonalizedSections())) {
            for (PersonalizedSectionDetails personalizedSection : listingResponse.getPersonalizedSections()) {
                List<Hotel> hotels = buildPersonalizedHotels(personalizedSection.getHotels(), expDataMap, searchHotelsRequest, personalizedSection.getName(), commonModifierResponse, listingResponse.getLocation(), listingResponse.getMarkUpDetails());
                listingMapResponse.setHotels(hotels);
            }
        }

        ShortStaysZoneResult shortStaysZoneResult = listingResponse.getShortStaysZoneResult();
        if (shortStaysZoneResult != null) {
            try {
                String shortStaysZoneString = objectMapperUtil.getJsonFromObject(shortStaysZoneResult, DependencyLayer.CLIENTGATEWAY);
                ShortstaysZoneResult shortstaysZoneResult = objectMapperUtil.getObjectFromJson(shortStaysZoneString, ShortstaysZoneResult.class, DependencyLayer.CLIENTGATEWAY);
                listingMapResponse.setCentralCityGeoDetails(shortstaysResponseTransformer.buildCentralCityGeoDetails(shortstaysZoneResult));
                listingMapResponse.setAssociatedCitiesGeoConfig(shortstaysResponseTransformer.buildAssociatedCitiesGeoConfig(shortstaysZoneResult));

            } catch (Exception e) {
                LOGGER.error("Error while converting ShortStaysZoneResult to JSON", e);
            }
        }

        return listingMapResponse;
    }

    /**
     * Modifies the deeplink domain based on region and userCountry parameters.
     * Uses proper URI parsing to handle domain replacement safely.
     */
    public String modifyDeeplinkDomain(String deeplink, SearchHotelsRequest searchHotelsRequest) {
        try {
            if (StringUtils.isEmpty(deeplink) || StringUtils.isEmpty(searchHotelsRequest.getSearchCriteria().getCountryCode())) {
                return deeplink;
            }
            String region = searchHotelsRequest.getSearchCriteria().getCountryCode();
            // Handle URLs that start with @ symbol
            String processedDeeplink = deeplink.startsWith("@") ? deeplink.substring(1) : deeplink;
            // Parse the URL into URI components
            java.net.URI uri = new java.net.URI(processedDeeplink);
            // Determine the new domain based on region and userCountry (case insensitive)
            String regionUpper = StringUtils.isNotEmpty(region) ? region.toUpperCase() : "";
            String newHost = "makemytrip.com";
            switch (regionUpper) {
                case REGION_SA:
                    newHost = "sa.makemytrip.global";
                    break;
                case Constants.AE:
                    if (StringUtils.isEmpty(searchHotelsRequest.getUserLocation().getCountry())) {
                        return deeplink;
                    } else {
                        String userCountry = searchHotelsRequest.getUserLocation().getCountry();
                        String userCountryUpper = StringUtils.isNotEmpty(userCountry) ? userCountry.toUpperCase() : "";
                        if (Constants.AE.equals(userCountryUpper)) {
                            newHost = "ae.makemytrip.global";
                        } else {
                            newHost = "makemytrip.global";
                        }
                    }
                    break;
                default:
                    // No change needed, return original deeplink
                    return deeplink;
            }

            // Create new URI with modified host
            java.net.URI modifiedUri = new java.net.URI(
                uri.getScheme(),
                uri.getUserInfo(),
                newHost,
                uri.getPort(),
                uri.getPath(),
                uri.getQuery(),
                uri.getFragment()
            );
            return modifiedUri.toString();
        } catch (Exception e) {
            LOGGER.error("Error while modifying deeplink domain for region: deeplink: {}", deeplink, e);
            // Return original deeplink in case of any error
            return deeplink;
        }
    }

    /**
     * Transforms orchestrator PaxDetails to clientgateway ItineraryPaxDetails.
     * This method converts passenger details from the orchestrator model to the client gateway model.
     * 
     * @param orchestratorPaxDetailsList List of PaxDetails from orchestrator response
     * @return List of ItineraryPaxDetails for clientgateway response
     */
    private List<com.mmt.hotels.model.response.corporate.ItineraryPaxDetails> buildPaxDetails(List<com.gommt.hotels.orchestrator.model.response.listing.PaxDetails> orchestratorPaxDetailsList) {
        if (CollectionUtils.isEmpty(orchestratorPaxDetailsList)) {
            return new ArrayList<>();
        }
        
        List<com.mmt.hotels.model.response.corporate.ItineraryPaxDetails> clientGatewayPaxDetailsList = new ArrayList<>();
        
        for (com.gommt.hotels.orchestrator.model.response.listing.PaxDetails orchestratorPax : orchestratorPaxDetailsList) {
            com.mmt.hotels.model.response.corporate.ItineraryPaxDetails clientGatewayPax = new com.mmt.hotels.model.response.corporate.ItineraryPaxDetails();
            
            // Map basic passenger information
            clientGatewayPax.setFirstName(orchestratorPax.getFirstName());
            clientGatewayPax.setLastName(orchestratorPax.getLastName());
            clientGatewayPax.setEmail(orchestratorPax.getEmail());
            clientGatewayPax.setPhoneNumber(orchestratorPax.getPhoneNumber());
            clientGatewayPax.setPrimaryPax(orchestratorPax.isPrimaryPax());
            clientGatewayPax.setGender(orchestratorPax.getGender());
            
            // Set default values for fields not available in orchestrator model
            clientGatewayPax.setIsdCode(null); // Not available in orchestrator model
            
            clientGatewayPaxDetailsList.add(clientGatewayPax);
        }
        
        return clientGatewayPaxDetailsList;
    }

    /**
     * Determines if the current request is a direct search based on comprehensive request context.
     * A direct search is identified when either:
     * 1. SearchCriteria contains specific hotel IDs, or
     * 2. MatchMakerRequest contains hotel information
     * 
     * @param searchHotelsRequest the search request to analyze, can be null
     * @return true if this is a direct search, false otherwise (null-safe with false as default)
     */
    private boolean isDirectSearch(SearchHotelsRequest searchHotelsRequest) {
        if (searchHotelsRequest == null) {
            return false;
        }
        
        // Check if search criteria contains hotel IDs (primary direct search indicator)
        if (searchHotelsRequest.getSearchCriteria() != null && 
            CollectionUtils.isNotEmpty(searchHotelsRequest.getSearchCriteria().getHotelIds())) {
            return true;
        }
        
        // Check if matchmaker details contain hotel information (secondary direct search indicator)
        if (searchHotelsRequest.getMatchMakerDetails() != null && 
            CollectionUtils.isNotEmpty(searchHotelsRequest.getMatchMakerDetails().getHotels())) {
            
            // Ensure the first hotel entry has a valid hotel ID
            return searchHotelsRequest.getMatchMakerDetails().getHotels().get(0) != null && 
                   StringUtils.isNotEmpty(searchHotelsRequest.getMatchMakerDetails().getHotels().get(0).getHotelId());
        }
        
        return false;
    }

    /**
     * Sets chain and host properties headers for a hotel based on property information and location data.
     * Handles both chain properties and host properties with appropriate localized headings.
     * 
     * @param hotel the hotel object to update, can be null
     * @param hotelEntity the hotel entity containing property information, can be null
     * @param locusData the location data containing city information, can be null
     */
    private void setChainAndHostPropertiesHeaders(Hotel hotel, HotelDetails hotelEntity, LocationDetails locusData) {
        if (hotel == null || hotelEntity == null || locusData == null) {
            return;
        }
        
        // Set chain properties header if chain information is available
        if (hotelEntity.getPropertyInfo() != null && 
            StringUtils.isNotEmpty(locusData.getCityName()) && 
            StringUtils.isNotEmpty(hotelEntity.getHotelChainName())) {
            
            String chainSectionHeading = polyglotService.getTranslatedData(ConstantsTranslation.CHAIN_PROPERTIES_HEADER);
            chainSectionHeading = chainSectionHeading.replace("{chain}", hotelEntity.getHotelChainName()); //TODO: Uncomment this line when chain name starts flowing from new hes
            chainSectionHeading = chainSectionHeading.replace("{city}", locusData.getCityName());
            hotel.setChainPropertiesHeader(chainSectionHeading);
        }
        
        // Set host properties header if host information is available
        if (hotelEntity.getPropertyInfo() != null && 
            StringUtils.isNotEmpty(locusData.getCityName()) && 
            StringUtils.isNotEmpty(hotelEntity.getHostName())) {
            
            String hostSectionHeading = polyglotService.getTranslatedData(ConstantsTranslation.HOST_PROPERTIES_HEADER);
            hostSectionHeading = hostSectionHeading.replace("{host}", hotelEntity.getHostName());
            hostSectionHeading = hostSectionHeading.replace("{city}", locusData.getCityName());
            hotel.setChainPropertiesHeader(hostSectionHeading);
        }
    }
}