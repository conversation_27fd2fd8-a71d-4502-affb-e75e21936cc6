package com.mmt.hotels.clientgateway.transformer.factory;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.FilterPillConfigurationWrapper;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.response.filter.FilterPill;
import com.mmt.hotels.clientgateway.response.filter.SortList;
import com.mmt.hotels.clientgateway.response.searchHotels.SortCriteria;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.FilterPillConfig;
import com.mmt.hotels.clientgateway.transformer.response.FilterResponseTransformer;
import com.mmt.hotels.model.request.AccessPoint;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.SORT_CRITERIA_ACCESS_POINT_FIELD_VALUE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SORT_CRITERIA_ACCESS_POINT_TITLE;

@Component
public class FilterPillFactory {

    @Value("${filter.pill.config.B2C}")
    private String pillConfigB2C;

    @Value("${filter.pill.config.myBiz}")
    private String pillConfigMyBiz;

    @Value("${filter.pill.config.getaway}")
    private String pillConfigGetaway;

    @Value("${filter.pill.config.luxe}")
    private String pillConfigLuxe;

    @Value("${filter.pill.config.shortstays}")
    private String pillConfigShortStays;

    @Value("${filter.pill.config.premium}")
    private String pillConfigPremium;

    @Value("${sort.pill.config}")
    private String sortPillData;

    @Value("${sort.pill.config.sortCriteriaCheapestAndBestReviewed}")
    private String sortCriteriaCheapestAndBestReviewedJson;

    @Value("${sort.pill.config.sortCriteriaMostAndBestReviewed}")
    private String sortCriteriaMostAndBestReviewedJson;

    @Value("${sort.pill.config.sortCriteriaHeroPoiDrivingDistance}")
    private String sortCriteriaHeroPoiDrivingDistanceJson;

    @Value("${sort.pill.config.sortCriteriaBeachCityPoiDrivingDistance}")
    private String sortCriteriaBeachCityPoiDrivingDistanceJson;

    @Value("${sort.pill.config.sortedByDrivingDurationShortstay}")
    private String sortCriteriaDrivingDurationShortstayJson;

    @Value("${filter.pill.config.homestayV2.B2C}")
    private String homestayV2ConfigB2C;

    private FilterPillConfig filterPillsB2C;

    private FilterPillConfig filterPillsMyBiz;

    private FilterPillConfig filterPillsGetaway;

    private FilterPillConfig filterPillsLuxe;

    private FilterPillConfig filterPillsShortStays;

    private FilterPillConfig filterPillsPremium;

    private SortList sortList;

    private SortCriteria sortCriteriaCheapestAndBestReviewed;

    private SortCriteria sortCriteriaMostAndBestReviewed;

    private SortCriteria sortCriteriaHeroPoiDrivingDistance;

    private SortCriteria sortCriteriaBeachCityPoiDrivingDistance;

    private SortCriteria sortCriteriaDrivingDurationShortstay;

    private FilterPillConfig homestayV2PillsB2C;

    private final Gson gson = new Gson();

    @Autowired
    PolyglotService polyglotService;


    @PostConstruct
    public void init() {
        filterPillsB2C = gson.fromJson(pillConfigB2C, new TypeToken<FilterPillConfig>() {
        }.getType());

        filterPillsMyBiz = gson.fromJson(pillConfigMyBiz, new TypeToken<FilterPillConfig>() {
        }.getType());

        filterPillsGetaway = gson.fromJson(pillConfigGetaway, new TypeToken<FilterPillConfig>() {
        }.getType());

        filterPillsLuxe = gson.fromJson(pillConfigLuxe, new TypeToken<FilterPillConfig>() {
        }.getType());

        filterPillsPremium = gson.fromJson(pillConfigPremium, new TypeToken<FilterPillConfig>() {
        }.getType());

        sortList = gson.fromJson(sortPillData, new TypeToken<SortList>() {
        }.getType());

        sortCriteriaCheapestAndBestReviewed = gson.fromJson(sortCriteriaCheapestAndBestReviewedJson, new TypeToken<SortCriteria>() {
        }.getType());

        sortCriteriaMostAndBestReviewed = gson.fromJson(sortCriteriaMostAndBestReviewedJson, new TypeToken<SortCriteria>() {
        }.getType());

        sortCriteriaHeroPoiDrivingDistance = gson.fromJson(sortCriteriaHeroPoiDrivingDistanceJson, new TypeToken<SortCriteria>() {
        }.getType());

        sortCriteriaBeachCityPoiDrivingDistance = gson.fromJson(sortCriteriaBeachCityPoiDrivingDistanceJson, new TypeToken<SortCriteria>() {
        }.getType());

        sortCriteriaDrivingDurationShortstay = gson.fromJson(sortCriteriaDrivingDurationShortstayJson, new TypeToken<SortCriteria>() {
        }.getType());

        filterPillsShortStays = gson.fromJson(pillConfigShortStays, new TypeToken<FilterPillConfig>(){
        }.getType());

        homestayV2PillsB2C = gson.fromJson(homestayV2ConfigB2C, new TypeToken<FilterPillConfig>() {
        }.getType());


    }


    /**
     * returns wrapper for filter pill configuration
     **/

    public FilterPillConfigurationWrapper getFilterPillConfiguration(
            String funnelSource,
            String locationType,
            String idContext,
            boolean isSortCheapestBestReviewedApplicable,
            boolean isSortMostAndBestReviewedApplicable,
            String cityHeroPoiName,
            boolean isBeachCityPoiPresent,
            boolean isSaveForCompanyAvailable,
            boolean isPremiumFunnel,
            Map<String, AccessPoint> accessPoints
    ) {

        FilterPillConfigurationWrapper filterPillConfigurationWrapper = new FilterPillConfigurationWrapper();
        filterPillConfigurationWrapper.setFilterPillConfig(getFilterPillConfig(funnelSource, locationType,idContext,isSaveForCompanyAvailable,isPremiumFunnel));
        filterPillConfigurationWrapper.setSortList(getSortListPill(isSortCheapestBestReviewedApplicable, isSortMostAndBestReviewedApplicable, cityHeroPoiName, isBeachCityPoiPresent, funnelSource, accessPoints));

        return filterPillConfigurationWrapper;

    }

    /**
     * this method returns pill config as per search and funnel
     **/
    private FilterPillConfig getFilterPillConfig(String funnelSource, String locationType,String idContext, boolean isSaveForCompanyAvailable, boolean isPremiumFunnel) {

        //Create filter pill config for MyBiz
        if(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext)) {
            FilterPillConfig corpFilterPillConfig =  copyFilterPillConfigProp(filterPillsMyBiz);
            if(MapUtils.isNotEmpty(corpFilterPillConfig.getDynamicFilterPills()) && corpFilterPillConfig.getDynamicFilterPills().keySet().contains(Constants.SAVED_FOR_COMPANY_PILL_ID) && !isSaveForCompanyAvailable) {
                corpFilterPillConfig.getDynamicFilterPills().remove(Constants.SAVED_FOR_COMPANY_PILL_ID);
            }

            return corpFilterPillConfig;
        }
        // Create FilterPillConfig for premium funnel
        if (isPremiumFunnel) {
            return copyFilterPillConfigProp(filterPillsPremium);
        }
        // Create FilterPillConfig for locationType LUXE
        if (Constants.LUXE_LOCATION_TYPE.equalsIgnoreCase(locationType)) {
            return copyFilterPillConfigProp(filterPillsLuxe);
        }
        // Create FilterPillConfig for funnelSource GETAWAY
        if (Constants.FUNNEL_SOURCE_GETAWAY.equalsIgnoreCase(funnelSource)) {
            return copyFilterPillConfigProp(filterPillsGetaway);
        }
        // Create FilterPillConfig for funnelSource SHORTSTAYS
        if (Constants.FUNNEL_SOURCE_SHORTSTAYS.equalsIgnoreCase(funnelSource)) {
            return copyFilterPillConfigProp(filterPillsShortStays);
        }
        // Create FilterPillConfig for B2C
        return copyFilterPillConfigProp(filterPillsB2C);

    }


    /**
     * this method creates copy of filter pill config and returns to ensure original config is not modified
     **/
    public FilterPillConfig copyFilterPillConfigProp(FilterPillConfig filterPillConfig) {
        if (filterPillConfig != null) {
            FilterPillConfig newFilterPillConfig = new FilterPillConfig();

            // Populate sticky filter pills
            if (MapUtils.isNotEmpty(filterPillConfig.getStickyFilterPills())) {
                newFilterPillConfig.setStickyFilterPills(getCopyOfFilterPillMap(filterPillConfig.getStickyFilterPills()));
            }

            // Populate default dynamic filter pills
            if (MapUtils.isNotEmpty(filterPillConfig.getDynamicFilterPills())) {
                newFilterPillConfig.setDynamicFilterPills(getCopyOfFilterPillMap(filterPillConfig.getDynamicFilterPills()));
            }

            // Populate DPT dynamic filter pills
            if(MapUtils.isNotEmpty(filterPillConfig.getDynamicFilterPillsDPT())){
                newFilterPillConfig.setDynamicFilterPillsDPT(getCopyOfFilterPillMap(filterPillConfig.getDynamicFilterPillsDPT()));
            }

            // Populate homestayV2 filter pills
            if (homestayV2PillsB2C!=null && MapUtils.isNotEmpty(homestayV2PillsB2C.getHomestayV2FilterPills())) {
                newFilterPillConfig.setHomestayV2FilterPills(getCopyOfFilterPillMap(homestayV2PillsB2C.getHomestayV2FilterPills()));
            }

            return newFilterPillConfig;
        }
        return null;
    }

    /**
     * Method to create a copy of filter pill map
     */
    private Map<String, FilterPill> getCopyOfFilterPillMap(Map<String, FilterPill> filterPillMap) {
        Map<String, FilterPill> newDynamicFilterPills = null;
        if (MapUtils.isNotEmpty(filterPillMap)) {
            newDynamicFilterPills = new HashMap<>();
            for (String pill : filterPillMap.keySet()) {
                FilterPill filterPill = new FilterPill();
                if (filterPillMap.get(pill) != null) {
                    List<String> filterCategories = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(filterPillMap.get(pill).getCategories())){
                        filterCategories.addAll(filterPillMap.get(pill).getCategories());
                    }
                    BeanUtils.copyProperties(filterPillMap.get(pill), filterPill);
                    filterPill.setCategories(filterCategories);
                }
                newDynamicFilterPills.put(pill, filterPill);
            }
        }
        return newDynamicFilterPills;
    }

    /**
     * this method creates copy of sort list pill and returns it to ensure config is not changed
     **/
    private SortList getSortListPill(boolean isSortCheapestBestReviewedApplicable, boolean isSortMostAndBestReviewedApplicable, String cityHeroPoiName, boolean isBeachCityPoiPresent, String funnelSource, Map<String, AccessPoint> accessPoints) {
        Logger logger = LoggerFactory.getLogger(FilterResponseTransformer.class);

        if (sortList != null) {
            SortList sortListConfig = buildSortList(sortList);
            if(isSortCheapestBestReviewedApplicable){
                SortCriteria newSortCriteriaCheapestAndBestReviewed = new SortCriteria();
                BeanUtils.copyProperties(sortCriteriaCheapestAndBestReviewed, newSortCriteriaCheapestAndBestReviewed);
                sortListConfig.getSortCriteria().add(newSortCriteriaCheapestAndBestReviewed);
            }

            if(isSortMostAndBestReviewedApplicable){
                SortCriteria newSortCriteriaMostAndBestReviewed = new SortCriteria();
                BeanUtils.copyProperties(sortCriteriaMostAndBestReviewed, newSortCriteriaMostAndBestReviewed);
                sortListConfig.getSortCriteria().add(newSortCriteriaMostAndBestReviewed);
            }

            if(StringUtils.isNotEmpty(cityHeroPoiName)) {
                SortCriteria newSortCriteriaHeroPoiDrivingDistance = new SortCriteria();
                BeanUtils.copyProperties(sortCriteriaHeroPoiDrivingDistance, newSortCriteriaHeroPoiDrivingDistance);
                sortListConfig.getSortCriteria().add(newSortCriteriaHeroPoiDrivingDistance);
            }

            if(isBeachCityPoiPresent) {
                SortCriteria newSortCriteriaBeachCityPoiDrivingDistance = new SortCriteria();
                BeanUtils.copyProperties(sortCriteriaBeachCityPoiDrivingDistance, newSortCriteriaBeachCityPoiDrivingDistance);
                sortListConfig.getSortCriteria().add(newSortCriteriaBeachCityPoiDrivingDistance);
            }

            if (Constants.FUNNEL_SOURCE_SHORTSTAYS.equalsIgnoreCase(funnelSource)) {
                SortCriteria newSortCriteriaDrivingDurationShortstays = new SortCriteria();
                BeanUtils.copyProperties(sortCriteriaDrivingDurationShortstay, newSortCriteriaDrivingDurationShortstays);
                sortListConfig.getSortCriteria().add(newSortCriteriaDrivingDurationShortstays);
            }

            // Add access point based sort criteria if accessPoints are available
            if (MapUtils.isNotEmpty(accessPoints)) {
                for (Map.Entry<String, AccessPoint> entry : accessPoints.entrySet()) {
                    AccessPoint accessPoint = entry.getValue();
                    if (accessPoint != null && StringUtils.isNotEmpty(accessPoint.getAccessPointName()) 
                        && StringUtils.isNotEmpty(accessPoint.getPoi())) {
                        String title = polyglotService.getTranslatedData(SORT_CRITERIA_ACCESS_POINT_TITLE).replace("{poi_name}",accessPoint.getAccessPointName());
                        SortCriteria accessPointSortCriteria = getSortCriteria(accessPoint, title);
                        sortListConfig.getSortCriteria().add(accessPointSortCriteria);
                    }
                }
            }

            return sortListConfig;
        }
        return null;
    }

    private static SortCriteria getSortCriteria(AccessPoint accessPoint, String title) {
        SortCriteria accessPointSortCriteria = new SortCriteria();
        accessPointSortCriteria.setOrder("asc");

        accessPointSortCriteria.setTitle(title);
        accessPointSortCriteria.setPillText(title);
        accessPointSortCriteria.setAccessPoint(true);

        String fieldValue = SORT_CRITERIA_ACCESS_POINT_FIELD_VALUE.replace("{poi_id}",accessPoint.getPoi());
        accessPointSortCriteria.setField(fieldValue);

        return accessPointSortCriteria;
    }

    private SortList buildSortList(SortList sortListConfig){
        SortList sortList = new SortList();
        sortList.setTitle(sortListConfig.getTitle());
        List<SortCriteria> sortCriteriaList = new ArrayList<>();
        for (SortCriteria sortCriteria : sortListConfig.getSortCriteria()) {
            SortCriteria newSortCriteria = new SortCriteria();
            BeanUtils.copyProperties(sortCriteria, newSortCriteria);
            sortCriteriaList.add(newSortCriteria);
        }
        sortList.setSortCriteria(sortCriteriaList);
        return sortList;
    }

}
