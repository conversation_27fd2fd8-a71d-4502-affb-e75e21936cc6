package com.mmt.hotels.clientgateway.transformer.response.ios;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.thankyou.ThankYouResponse;
import com.mmt.hotels.clientgateway.transformer.response.ThankYouResponseTransformer;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.UserGlobalInfo;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.txn.BookingMetaInfo;
import com.mmt.hotels.model.response.txn.PersistanceMultiRoomResponseEntity;
import com.mmt.hotels.model.response.txn.PersistedMultiRoomData;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Component
public class ThankYouResponseTransformerIOS extends ThankYouResponseTransformer {

    @Value("${thankyou.mytrips.deeplink}")
    private String myTripsDeeplink;

    @Value("${thankyou.mytrips.deeplink.v2}")
    private String myTripsDeeplinkV2;

    @Value("${hotelDetail.deeplink.url}")
    private String hotelDetailsRawDeepLink;

    @Override
    protected void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap) {

    }

    @Override
    public ThankYouResponse convertThankYouResponse(PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity, FeatureFlags featureFlags, Map<String, String> parameterMap) throws ErrorResponseFromDownstreamException {
        ThankYouResponse thankYouResponse = super.convertThankYouResponse(persistanceMultiRoomResponseEntity,featureFlags, parameterMap);
        if(thankYouResponse != null){
            thankYouResponse.setExperimentData(null);
        }
        return thankYouResponse;
    }

    @Override
    protected String getMytripActionCorpUrl(String cardType) {
        return myTripsCardTypeToIconUrls.get(cardType).getIconUrlIosCorp();
    }

    @Override
    protected String getMytripActionB2CUrl(String cardType) {
        return myTripsCardTypeToIconUrls.get(cardType).getIconUrlIos();
    }

    @Override
    protected String getHotelDetailsRawDeepLinkUrl(PersistedMultiRoomData persistedData) {
        return hotelDetailsRawDeepLink;
    }

    @Override
    protected String getMytripsRawDeepLinkUrl(UserGlobalInfo userGlobalInfo, Map<String, String> expData) {
        if (expData != null && TRUE.equalsIgnoreCase(expData.get(ExperimentKeys.MYTRIP_URL_V2))) {
            return myTripsDeeplinkV2;
        }
        return myTripsDeeplink;
    }

    @Override
    protected String getDigilockerCheckInDeepLinkUrl(UserGlobalInfo userGlobalInfo, String bookingId) {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put(BOOKING_ID, bookingId);
        queryParams.put(DIGILOCKER_DEEPLINK_ACT_PARAM, DIGILOCKER_DEEPLINK_ACT_VALUE);
        queryParams.put(MY_TRIPS_PAGE_NAME_PARAM, MY_TRIPS_HOTEL_DETAIL_PAGE_PARAM);

        return Utility.appendQueryParamsInUrl(myTripsDeeplinkV2, queryParams);
    }

    @Override
    protected boolean tildeRequiredInRSQ() {
        return true;
    }

    @Override
    protected String getMyTripsDeepLink(String myTripsDeeplink, BookingMetaInfo bookingMetaInfo) {
        HashMap<String, String> params = new HashMap<>();
        params.put(KEY_PAGE, HOTEL_BOOKING_DETAIL);
        if (null != bookingMetaInfo && StringUtils.hasText(bookingMetaInfo.getBookingId())) {
            params.put(BOOKING_ID, bookingMetaInfo.getBookingId());
        }
        if (bookingMetaInfo != null) {
            boolean isCorp = CORP_ID_CONTEXT.equalsIgnoreCase(bookingMetaInfo.getRequestorIdContext());
            params.put(IS_CORPORATE, String.valueOf(isCorp));
        }
        return Utility.appendQueryParamsInUrl(myTripsDeeplink, params);
    }
}
