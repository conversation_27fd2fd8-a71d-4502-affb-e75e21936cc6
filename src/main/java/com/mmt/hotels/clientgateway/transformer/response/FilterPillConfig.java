package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.response.filter.FilterPill;

import java.util.Map;

public class FilterPillConfig {

    // Map to store the filter pills, which needs to be flown in every case
    private Map<String, FilterPill> stickyFilterPills;

    // Map to store the filter pills, which will be overridden by the filter pills received from DPT
    private Map<String, FilterPill> dynamicFilterPills;


    private Map<String, FilterPill> dynamicFilterPillsDPT;

    private Map<String, FilterPill> homestayV2FilterPills;

    public Map<String, FilterPill> getHomestayV2FilterPills() {
        return homestayV2FilterPills;
    }

    public void setHomestayV2FilterPills(Map<String, FilterPill> homestayV2FilterPills) {
        this.homestayV2FilterPills = homestayV2FilterPills;
    }

    public Map<String, FilterPill> getStickyFilterPills() {
        return stickyFilterPills;
    }

    public void setStickyFilterPills(Map<String, FilterPill> stickyFilterPills) {
        this.stickyFilterPills = stickyFilterPills;
    }

    public Map<String, FilterPill> getDynamicFilterPills() {
        return dynamicFilterPills;
    }

    public void setDynamicFilterPills(Map<String, FilterPill> dynamicFilterPills) {
        this.dynamicFilterPills = dynamicFilterPills;
    }

    public Map<String, FilterPill> getDynamicFilterPillsDPT() {
        return dynamicFilterPillsDPT;
    }

    public void setDynamicFilterPillsDPT(Map<String, FilterPill> dynamicFilterPillsDPT) {
        this.dynamicFilterPillsDPT = dynamicFilterPillsDPT;
    }

}