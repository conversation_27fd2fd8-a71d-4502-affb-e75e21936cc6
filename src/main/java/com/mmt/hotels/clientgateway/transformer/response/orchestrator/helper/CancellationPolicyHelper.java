package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.common.BNPLDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.CancelRules;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.CancelRulesDesc;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.CancellationPenalty;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.CancellationPolicy;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.CancellationPolicyTimelineDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.CancellationTimelineDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.FreeCancellationBenefitDetails;
import com.gommt.hotels.orchestrator.model.response.da.AmendmentPolicies;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicy;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicyType;
import com.mmt.hotels.clientgateway.response.CancellationTimeline;
import com.mmt.hotels.clientgateway.response.FCBenefit;
import com.mmt.hotels.clientgateway.response.IconType;
import com.mmt.hotels.clientgateway.response.rooms.CancellationPolicyTimeline;
import com.mmt.hotels.clientgateway.response.rooms.CancellationTimelineV2;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.response.pricing.CancelRulesDescription;
import com.mmt.hotels.model.response.pricing.CancellationRules;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.mmt.hotels.clientgateway.constants.Constants.ANDROID;
import static com.mmt.hotels.clientgateway.constants.Constants.DEVICE_IOS;

/**
 * Helper class for transforming OrchV2 CancellationPolicy directly to CG response format
 * without going through legacy model transformation.
 */
@Component
public class CancellationPolicyHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(CancellationPolicyHelper.class);

    @Autowired
    private PolyglotService polyglotService;

    @Value("${red.cross.icon}")
    private String redCrossIconUrl;

    @Value("${green.tick.icon}")
    private String greenTickIcon;

    @Value("${inclusion.icon.red.cross}")
    private String inclusionIconRedCross;

    @Value("${inclusion.icon.double.tick.green}")
    private String inclusionIconDoubleTickGreen;

    // FCBenefit type constants
    private static final String TYPE_FCZPN = "FCZPN";
    private static final String BNPL_DISABLED = "BNPL_DISABLED";
    private static final String DATE = "DATE";
    private static final int AP_LIMIT_FOR_INCLUSION_ICONS = 2;

    /**
     * Transform OrchV2 CancellationPolicy directly to BookedCancellationPolicy
     * with complete logic including icons, amendment policies, and advance purchase handling.
     * Assumes DETAIL_SEARCH_ROOMS controller context.
     */
    public BookedCancellationPolicy transformCancellationPolicy(CancellationPolicy policy, Integer advancePurchase) {

        if (policy == null) {
            return null;
        }

        BookedCancellationPolicy bookedCancellationPolicy = new BookedCancellationPolicy();
        String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
        boolean isCancelRulesRequired = true;

        // Determine cancellation type from OrchV2 policy
        String cancellationPolicyType = getCancellationPolicyType(policy);
        boolean isFC = Constants.CANCELLATION_TYPE_FC.equalsIgnoreCase(cancellationPolicyType);
        boolean isPC = Constants.CANCELLATION_TYPE_FCZPN.equalsIgnoreCase(cancellationPolicyType);

        // Set icon URLs and types based on cancellation type
        bookedCancellationPolicy.setIconUrl((isFC || isPC) ? greenTickIcon : redCrossIconUrl);
        bookedCancellationPolicy.setIconUrlV2((isFC || isPC) ? inclusionIconDoubleTickGreen : inclusionIconRedCross);

        String cancelPolicyText = "";
        if (isFC) {
            // Free Cancellation logic
            bookedCancellationPolicy.setIconType(IconType.DOUBLETICK);
            bookedCancellationPolicy.setType(BookedCancellationPolicyType.FC);

            // Set text from penalties if available
            if (CollectionUtils.isNotEmpty(policy.getPenalties())) {
                CancellationPenalty firstPenalty = policy.getPenalties().get(0);
                cancelPolicyText = StringUtils.isNotBlank(firstPenalty.getFreeCancellationText()) ? firstPenalty.getFreeCancellationText() : getFreeCancellationText();
            } else {
                cancelPolicyText = getFreeCancellationText();
            }

            // TODO: Add BNPL and MpFareHoldStatus logic when available in OrchV2

        } else if (isPC) {
            // Partial Refundable logic
            bookedCancellationPolicy.setIconType(IconType.DOUBLETICK);
            bookedCancellationPolicy.setType(BookedCancellationPolicyType.FC);

            // TODO: Add partial refund text building logic when available
            cancelPolicyText = polyglotService.getTranslatedData(ConstantsTranslation.PARTIAL_REFUNDABLE_TEXT);

        } else {
            // Non-Refundable logic
            // Set icon type based on advance purchase for DETAIL_SEARCH_ROOMS controller
            if (advancePurchase != null && advancePurchase < AP_LIMIT_FOR_INCLUSION_ICONS) {
                bookedCancellationPolicy.setIconType(IconType.DEFAULT);
            } else {
                bookedCancellationPolicy.setIconType(IconType.BIGCROSS);
            }

            bookedCancellationPolicy.setType(BookedCancellationPolicyType.NR);

            // Handle amendment policies for NR
            String amendablePolicyText = null;
            AmendmentPolicies amendmentPolicies = getAmendmentPoliciesFromPolicy(policy);

            if (amendmentPolicies != null && DATE.equalsIgnoreCase(amendmentPolicies.getName())) {
                LOGGER.debug("PolicyName: {} and MetaData: {}", amendmentPolicies.getName(), amendmentPolicies.getMetaData());
                amendablePolicyText = getAmendableTextForNRPolicy(amendmentPolicies);
            }

            // Set text based on amendment policies for DETAIL_SEARCH_ROOMS
            String nonRefundableText = polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_TEXT);

            if (StringUtils.isNotEmpty(amendablePolicyText)) {
                isCancelRulesRequired = false;
                cancelPolicyText = nonRefundableText + Constants.BUT_SEPARATOR + amendablePolicyText.toLowerCase();
            } else {
                cancelPolicyText = nonRefundableText;
            }

            // Set sub text
            if (StringUtils.isNotEmpty(amendablePolicyText)) {
                bookedCancellationPolicy.setSubText(getAmendableSubtext(amendablePolicyText));
            } else {
                bookedCancellationPolicy.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_SUBTEXT));
            }
        }

        // Set cancel rules if required and available
        if (isCancelRulesRequired && CollectionUtils.isNotEmpty(policy.getPenalties())) {
            CancellationPenalty firstPenalty = policy.getPenalties().get(0);
            bookedCancellationPolicy.setCancelRules(transformCancelRules(firstPenalty.getCancelRules()));
        }

        if (ANDROID.equalsIgnoreCase(client) || DEVICE_IOS.equalsIgnoreCase(client)) {
            cancelPolicyText = cancelPolicyText.replace("Free Cancellation", "<font color=\"#007E7D\">Free Cancellation</font>");
        }
        bookedCancellationPolicy.setText(cancelPolicyText);
        return bookedCancellationPolicy;
    }

    /**
     * Extract amendment policies from OrchV2 policy
     */
    private AmendmentPolicies getAmendmentPoliciesFromPolicy(CancellationPolicy policy) {
        
        if (CollectionUtils.isNotEmpty(policy.getPenalties())) {
            CancellationPenalty firstPenalty = policy.getPenalties().get(0);
            // TODO: Check if OrchV2 CancellationPenalty has amendment policies field
            // For now, return null as the field might not be available in OrchV2 schema
        }
        return null;
    }

    /**
     * Build amendable text for NR policy
     */
    private String getAmendableTextForNRPolicy(AmendmentPolicies amendmentPolicies) {
        try {
            String text = polyglotService.getTranslatedData(ConstantsTranslation.AMENDABLE_DETAIL_TEXT);
            text = text.replace("{amendType}", amendmentPolicies.getName());
            String durationText = amendmentPolicies.getMetaData() + " hrs";
            text = StringUtils.isNotEmpty(durationText) ? text.replace("{duration}", durationText) : null;
            return text;
        } catch (Exception ex) {
            LOGGER.warn("Error in processing amendablePolicy meta-info: {}", ex.getMessage());
        }
        return null;
    }

    /**
     * Build amendable subtext
     */
    private String getAmendableSubtext(String amendablePolicyText) {
        return "<font color=\"#4A4A4A\">• " + polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_SUBTEXT)
                + "<br/>• " + amendablePolicyText + "</font>";
    }

    /**
     * Transform OrchV2 CancelRules to legacy CancellationRules format
     */
    private List<CancellationRules> transformCancelRules(List<CancelRules> orchCancelRules) {
        if (CollectionUtils.isEmpty(orchCancelRules)) {
            return null;
        }

        List<CancellationRules> cancellationRulesList = new ArrayList<>();
        for (CancelRules orchRule : orchCancelRules) {
            CancellationRules cancellationRules = new CancellationRules();
            cancellationRules.setText(orchRule.getText());
            
            // Transform descText from OrchV2 CancelRulesDesc to legacy CancelRulesDescription
            if (CollectionUtils.isNotEmpty(orchRule.getDescText())) {
                List<CancelRulesDescription> legacyDescText = new ArrayList<>();
                for (CancelRulesDesc orchDesc : orchRule.getDescText()) {
                    CancelRulesDescription cancelRulesDescription = new CancelRulesDescription();
                    cancelRulesDescription.setFeeText(orchDesc.getFeeText());
                    cancelRulesDescription.setDateText(orchDesc.getDateText());
                    legacyDescText.add(cancelRulesDescription);
                }
                cancellationRules.setDescText(legacyDescText);
            }
            cancellationRulesList.add(cancellationRules);
        }
        return cancellationRulesList;
    }

    /**
     * Get free cancellation text with fallback
     */
    private String getFreeCancellationText() {
        try {
            // Try to get translated text, fallback to default if not available
            String translatedText = polyglotService.getTranslatedData("FREE_CANCELLATION_TEXT");
            if (StringUtils.isNotBlank(translatedText) && !"FREE_CANCELLATION_TEXT".equals(translatedText)) {
                return translatedText;
            }
        } catch (Exception e) {
            // Ignore translation errors and use fallback
        }
        return "Free Cancellation";
    }

    /**
     * Build CancellationTimeline directly from orchestrator v2 CancellationTimelineDetails
     */
    public CancellationTimeline buildCancellationTimelineFromOrchV2(CancellationTimelineDetails orchTimelineDetails, BNPLDetails bnplDetails) {

        if (orchTimelineDetails == null) {
            return null;
        }

        CancellationTimeline cancellationTimeline = new CancellationTimeline();

        // Map all fields directly from orchestrator v2 to CG response
        cancellationTimeline.setBookingDate(orchTimelineDetails.getBookingDate());
        cancellationTimeline.setCancellationDate(orchTimelineDetails.getCancellationDate());
        cancellationTimeline.setCancellationDateTime(orchTimelineDetails.getCancellationDateTime());
        cancellationTimeline.setCardChargeDate(orchTimelineDetails.getCardChargeDate());
        cancellationTimeline.setCardChargeDateTime(orchTimelineDetails.getCardChargeDateTime());
        cancellationTimeline.setDateFormat(orchTimelineDetails.getDateFormat());
        cancellationTimeline.setCardChargeText(orchTimelineDetails.getCardChargeText());
        cancellationTimeline.setBookingAmountText(orchTimelineDetails.getBookingAmountText());
        cancellationTimeline.setCheckInDate(orchTimelineDetails.getCheckInDate());
        cancellationTimeline.setCheckInDateTime(orchTimelineDetails.getCheckInDateTime());
        cancellationTimeline.setFreeCancellationText(orchTimelineDetails.getFreeCancellationText());
        cancellationTimeline.setSubTitle(orchTimelineDetails.getSubTitle());
        cancellationTimeline.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.RATEPLAN_CANCELLATION_POLICY));

        // Transform free cancellation benefits
        if (CollectionUtils.isNotEmpty(orchTimelineDetails.getFreeCancellationBenefits())) {
            List<FCBenefit> fcBenefits = new ArrayList<>();
            boolean allSingleTickIconFCBenefits = true;
            for (FreeCancellationBenefitDetails orchBenefit : orchTimelineDetails.getFreeCancellationBenefits()) {
                FCBenefit benefit = new FCBenefit();
                benefit.setText(orchBenefit.getText());
                // Map type to IconType based on the logic from CommonResponseTransformer
                if (orchBenefit.getType() != null) {
                    if (orchBenefit.getType().equalsIgnoreCase(BNPL_DISABLED)) {
                        allSingleTickIconFCBenefits = false;
                        benefit.setIconType(IconType.DEFAULT);
                    } else if (orchBenefit.getType().equalsIgnoreCase(TYPE_FCZPN)) {
                        allSingleTickIconFCBenefits = false;
                        benefit.setIconType(IconType.DOUBLETICK);
                    } else {
                        benefit.setIconType(IconType.SINGLETICK);
                    }
                } else {
                    benefit.setIconType(IconType.SINGLETICK);
                }
                fcBenefits.add(benefit);
            }
            cancellationTimeline.setFreeCancellationBenefits(fcBenefits);

            if (bnplDetails != null && bnplDetails.getBnplVariant() != null && BNPLVariant.BNPL_AT_0.name().equals(bnplDetails.getBnplVariant().name())) {
                FCBenefit benefitCG = new FCBenefit();
                benefitCG.setText(polyglotService.getTranslatedData(Constants.PAY_ON_MMT_ONLY_INSTRUCTION));

                if (allSingleTickIconFCBenefits) {
                    benefitCG.setIconType(IconType.SINGLETICK);
                } else {
                    benefitCG.setIconType(IconType.DOUBLETICK);
                }
                fcBenefits.add(benefitCG);
            }
        }

        return cancellationTimeline;
    }

    /**
     * Build CancellationPolicyTimeline directly from orchestrator v2 CancellationTimelineDetails
     */
    public CancellationPolicyTimeline buildCancellationPolicyTimelineFromOrchV2(
            CancellationTimelineDetails orchTimelineDetails, boolean enableThemification) {

        if (orchTimelineDetails == null || CollectionUtils.isEmpty(orchTimelineDetails.getCancellationPolicyTimelineList())) {
            return null;
        }

        CancellationPolicyTimeline cancellationPolicyTimeline = new CancellationPolicyTimeline();

        // Set basic fields
        cancellationPolicyTimeline.setCancellationDate(orchTimelineDetails.getCancellationDate());
        cancellationPolicyTimeline.setCancellationDateTime(orchTimelineDetails.getCancellationDateTime());
        cancellationPolicyTimeline.setDateFormat(orchTimelineDetails.getDateFormat());
        cancellationPolicyTimeline.setBookingAmountText(orchTimelineDetails.getBookingAmountText());
        cancellationPolicyTimeline.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.RATEPLAN_CANCELLATION_POLICY));

        // Set timeline based on themification
        if (enableThemification) {
            cancellationPolicyTimeline.setTimelinesV2(buildCancellationTimelineV2List(orchTimelineDetails.getCancellationPolicyTimelineList()));
            cancellationPolicyTimeline.setHeaderLeft(polyglotService.getTranslatedData(ConstantsTranslation.RATEPLAN_CANCELLATION_POLICY));
            cancellationPolicyTimeline.setHeaderRight(polyglotService.getTranslatedData(ConstantsTranslation.APPLICABLE_REFUND_TEXT));
        } else {
            cancellationPolicyTimeline.setTimeline(buildCancellationTimelineList(orchTimelineDetails.getCancellationPolicyTimelineList()));
        }

        // Below check is placed to ensure card charge date is not overlapped due to partial refundable plans
        if (CollectionUtils.isNotEmpty(cancellationPolicyTimeline.getTimeline()) &&
                cancellationPolicyTimeline.getTimeline().size() < 3) {
            cancellationPolicyTimeline.setCardChargeDate(orchTimelineDetails.getCardChargeDate());
            cancellationPolicyTimeline.setCardChargeDateTime(orchTimelineDetails.getCardChargeDateTime());
            cancellationPolicyTimeline.setCardChargeText(orchTimelineDetails.getCardChargeText());
        }

        // Transform free cancellation benefits
        if (CollectionUtils.isNotEmpty(orchTimelineDetails.getFreeCancellationBenefits())) {
            List<FCBenefit> fcBenefits = new ArrayList<>();
            for (FreeCancellationBenefitDetails orchBenefit : orchTimelineDetails.getFreeCancellationBenefits()) {
                FCBenefit benefit = new FCBenefit();
                benefit.setText(orchBenefit.getText());
                // Map type to IconType based on the logic from CommonResponseTransformer
                if (orchBenefit.getType() != null) {
                    if (orchBenefit.getType().equalsIgnoreCase(BNPL_DISABLED)) {
                        benefit.setIconType(IconType.DEFAULT);
                    } else if (orchBenefit.getType().equalsIgnoreCase(TYPE_FCZPN)) {
                        benefit.setIconType(IconType.DOUBLETICK);
                    } else {
                        benefit.setIconType(IconType.SINGLETICK);
                    }
                } else {
                    benefit.setIconType(IconType.SINGLETICK);
                }
                fcBenefits.add(benefit);
            }
            cancellationPolicyTimeline.setFreeCancellationBenefits(fcBenefits);
        }

        return cancellationPolicyTimeline;
    }

    private List<com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline> buildCancellationTimelineList(
            List<CancellationPolicyTimelineDetails> orchPolicyTimelineList) {
        if (CollectionUtils.isEmpty(orchPolicyTimelineList)) {
            return null;
        }

        List<com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline> timelineList = new ArrayList<>();
        for (CancellationPolicyTimelineDetails orchTimeline : orchPolicyTimelineList) {
            com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline timeline =
                    new com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline();

            timeline.setRefundable(orchTimeline.isRefundable());
            timeline.setText(orchTimeline.getText());
            timeline.setStartDate(orchTimeline.getStartDate());
            timeline.setEndDate(orchTimeline.getEndDate());
            timeline.setEndDateTime(orchTimeline.getEndDateTime());

            // TODO: Add flexi cancellation details transformation when available

            timelineList.add(timeline);
        }
        return timelineList;
    }

    private List<CancellationTimelineV2> buildCancellationTimelineV2List(List<CancellationPolicyTimelineDetails> orchPolicyTimelineList) {
        if (CollectionUtils.isEmpty(orchPolicyTimelineList)) {
            return null;
        }

        List<CancellationTimelineV2> timelineList = new ArrayList<>();
        for (CancellationPolicyTimelineDetails orchTimeline : orchPolicyTimelineList) {
            CancellationTimelineV2 timeline = new CancellationTimelineV2();

            timeline.setStartDateText(orchTimeline.getStartDate());
            timeline.setStartDateSubText(orchTimeline.getStartDateTime());
            timeline.setEndDateText(orchTimeline.getEndDate());
            timeline.setEndDateSubText(orchTimeline.getEndDateTime());
            timeline.setType(orchTimeline.getType());
            timeline.setRefundText(orchTimeline.getRefundText());

            // TODO: Add flexi cancellation details transformation when available

            timelineList.add(timeline);
        }
        return timelineList;
    }

    public String getCancellationPolicyType(CancellationPolicy cancellationPolicy) {
        String penaltyType = CollectionUtils.isNotEmpty(cancellationPolicy.getPenalties()) ? cancellationPolicy.getPenalties().get(0).getType() : "";
        String cancellationType = Constants.CANCELLATION_TYPE_NR;
        if (StringUtils.isNotBlank(penaltyType)) {
            switch (penaltyType.toUpperCase()) {
                case "FC":
                case "FREE_CANCELLATON":
                case "FREE_CANCELLATION":
                    cancellationType = Constants.CANCELLATION_TYPE_FC;
                    break;
                case "PR":
                case "PARTIAL_REFUNDABLE":
                    cancellationType = Constants.CANCELLATION_TYPE_FCZPN;
                    break;
                case "NR":
                case "NON_REFUNDABLE":
                default:
                    cancellationType = Constants.CANCELLATION_TYPE_NR;
                    break;
            }
        }
        return cancellationType;
    }
}