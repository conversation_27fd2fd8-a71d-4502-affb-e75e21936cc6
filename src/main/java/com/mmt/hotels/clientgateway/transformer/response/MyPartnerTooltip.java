package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
public class MyPartnerTooltip {
    private String titleText;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String subText;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String iconUrl;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<MyPartnerTooltip> data;
}
