package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.EMIDetail;
import com.mmt.hotels.emi.detail.Coupon;
import com.mmt.hotels.emi.detail.Coupons;
import com.mmt.hotels.emi.detail.EmiDetailRequest;
import com.mmt.hotels.emi.detail.GuestDetails;
import com.mmt.hotels.model.request.ExtraInfo;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.RoomCriterion;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.request.emi.UpdateEmiDetailRequest;
import com.mmt.hotels.model.response.emi.EmiDetailsRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class EMIRequestTransformer extends BaseEMIRequestTransformer {

    public UpdateEmiDetailRequest convertEmiRequest(UpdatedEmiRequest updatedEmiRequest, CommonModifierResponse commonModifierResponse) {
        UpdateEmiDetailRequest updateEmiDetailRequest = new UpdateEmiDetailRequest();
        super.buildDeviceDetails(updateEmiDetailRequest, updatedEmiRequest.getDeviceDetails());
        buildSearchCriteria(updateEmiDetailRequest, updatedEmiRequest.getSearchCriteria(), commonModifierResponse);
        buildRequestDetails(updateEmiDetailRequest, updatedEmiRequest.getRequestDetails());
        updateEmiDetailRequest.setEmiDetails(buildEmiDetail(updatedEmiRequest.getEmiDetail()));
        updateEmiDetailRequest.setExperimentData(updatedEmiRequest.getExpData());
        updateEmiDetailRequest.setSrcClient(updatedEmiRequest.getClient());
        return updateEmiDetailRequest;
    }

    private void buildSearchCriteria(UpdateEmiDetailRequest updateEmiDetailRequest,
                                     UpdatedEMISearchCriteria searchCriteria, CommonModifierResponse commonModifierResponse) {
        updateEmiDetailRequest.setHotelId(searchCriteria.getHotelId());
        super.populateSearchCriteria(updateEmiDetailRequest, searchCriteria);
        updateEmiDetailRequest.setRoomCriteria(buildRoomCriteria(searchCriteria.getRoomCriteria()));
        updateEmiDetailRequest.setRoomStayCandidates(buildRoomStayCandidates(searchCriteria.getRoomStayCandidates()));
        ExtraInfo extraInfo = new ExtraInfo();
        extraInfo.setSearchType(searchCriteria.getSearchType());
        updateEmiDetailRequest.setExtraInfo(extraInfo);
    }

    private List<RoomStayCandidate> buildRoomStayCandidates(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {

        if (roomStayCandidates == null)
            return null;

        List<RoomStayCandidate> roomStayCandidateList = new ArrayList<>();

        for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidateCG : roomStayCandidates) {
            RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
            roomStayCandidate.setGuestCounts(buildGuestCounts(roomStayCandidateCG));
            roomStayCandidateList.add(roomStayCandidate);
        }

        return roomStayCandidateList;
    }

    private List<GuestCount> buildGuestCounts(com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidateCG) {


        List<GuestCount> guestCounts = new ArrayList<>();
        GuestCount guestCount = new GuestCount();
        guestCount.setAgeQualifyingCode("10");
        guestCount.setAges(roomStayCandidateCG.getChildAges());
        guestCount.setCount(String.valueOf(roomStayCandidateCG.getAdultCount()));
        guestCounts.add(guestCount);
        return guestCounts;

    }

    private List<RoomCriterion> buildRoomCriteria(List<UpdatedEMIRoomCriteria> roomCriteria) {

        if (CollectionUtils.isNotEmpty(roomCriteria)) {
            List<RoomCriterion> roomCriterionList = new ArrayList<>();

            for (UpdatedEMIRoomCriteria roomCriteriaCG : roomCriteria) {
                RoomCriterion roomCriterion = new RoomCriterion();
                roomCriterion.setPricingKey(roomCriteriaCG.getPricingKey());
                roomCriterion.setRatePlanCode(roomCriteriaCG.getRatePlanCode());
                roomCriterion.setRoomCode(roomCriteriaCG.getRoomCode());
                List<String> pricingKeys = new ArrayList<>();
                pricingKeys.add(roomCriteriaCG.getPricingKey());
                roomCriterion.setPricingKeys(pricingKeys);
                roomCriterion.setPaymentMode(roomCriteriaCG.getPaymentMode());
                roomCriterionList.add(roomCriterion);
            }
            return roomCriterionList ;
        }

        return null;

    }

    private void buildRequestDetails(UpdateEmiDetailRequest updateEmiDetailRequest, RequestDetails requestDetails) {

        updateEmiDetailRequest.setIdContext(requestDetails.getIdContext());
        updateEmiDetailRequest.setChannel(requestDetails.getChannel());
        updateEmiDetailRequest.setPaymentChannel(requestDetails.getChannel());
        updateEmiDetailRequest.setPageContext(requestDetails.getPageContext());
        updateEmiDetailRequest.setVisitNumber(requestDetails.getVisitNumber() != null ?
                String.valueOf(requestDetails.getVisitNumber()) : "");
        updateEmiDetailRequest.setLoggedIn(requestDetails.isLoggedIn());
        updateEmiDetailRequest.setSiteDomain(requestDetails.getSiteDomain());
        updateEmiDetailRequest.setCouponCount(requestDetails.getCouponCount() != null ? requestDetails.getCouponCount() : 0);
    }

    private EmiDetailsRequest buildEmiDetail(EMIDetail emiDetail) {

        if (emiDetail == null)
            return null;

        EmiDetailsRequest emiDetailCB = new EmiDetailsRequest();
        emiDetailCB.setBankId(emiDetail.getBankId());
        emiDetailCB.setBankName(emiDetail.getBankName());
        emiDetailCB.setPayOption(emiDetail.getPayOption());
        emiDetailCB.setTenure(emiDetail.getTenure());
        return emiDetailCB;

    }

    public EmiDetailRequest buildEmiDetailRequest(EMIDetailRequest emiDetailsRequest) {
        EmiDetailRequest.Builder emiDetailRequest = EmiDetailRequest.newBuilder()
                .addAllPricingKeys(emiDetailsRequest.getPricingKeys())
                .setCheckin(emiDetailsRequest.getCheckInDate())
                .setCheckout(emiDetailsRequest.getCheckOutDate())
                .setCorrelationKey(emiDetailsRequest.getCorrelationKey())
                .setFunnelSource(emiDetailsRequest.getRequestDetails().getFunnelSource())
                .setSelectedPricingKey(emiDetailsRequest.getSelectedPricingKey())
                .setFetchEmiPlansFromPayAdvice(Constants.COUPON_GREEN_STRIP.equalsIgnoreCase(emiDetailsRequest.getRequestDetails().getFlowIdentifier()))
                .setBrand(StringUtils.isNotEmpty(emiDetailsRequest.getBrand()) ? emiDetailsRequest.getBrand() : Constants.EMPTY_STRING)
                .setExperimentData(StringUtils.isNotEmpty(emiDetailsRequest.getExpData()) ? emiDetailsRequest.getExpData() : Constants.EMPTY_STRING)
                .setCountryCode(StringUtils.isNotEmpty(emiDetailsRequest.getCountryCode()) ? emiDetailsRequest.getCountryCode() : Constants.EMPTY_STRING)
                .setPageContext(StringUtils.isNotEmpty(emiDetailsRequest.getRequestDetails().getPageContext()) ? emiDetailsRequest.getRequestDetails().getPageContext() : Constants.PAGE_CONTEXT_DETAIL);

        if (CollectionUtils.isNotEmpty(emiDetailsRequest.getRoomStayCandidates())) {
            List<GuestDetails> guestDetails = emiDetailsRequest.getRoomStayCandidates().stream()
                    .map(roomStayCandidateCG -> {
                        GuestDetails.Builder guestDetailsBuilder = GuestDetails.newBuilder();
                        guestDetailsBuilder.setAdultCount(roomStayCandidateCG.getAdultCount());

                        if (CollectionUtils.isNotEmpty(roomStayCandidateCG.getChildAges())) {
                            guestDetailsBuilder.addAllChildAges(roomStayCandidateCG.getChildAges());
                        }
                        return guestDetailsBuilder.build();
                    })
                    .collect(Collectors.toList());

            com.mmt.hotels.emi.detail.RoomStayCandidate.Builder roomStayCandidate = com.mmt.hotels.emi.detail.RoomStayCandidate.newBuilder();
            roomStayCandidate.addAllGuestDetails(guestDetails);
            emiDetailRequest.setRoomStayCandidate(roomStayCandidate.build());
        }

        if (emiDetailsRequest.getDeviceDetails() != null && StringUtils.isNotEmpty(emiDetailsRequest.getDeviceDetails().getBookingDevice())) {
            com.mmt.hotels.emi.detail.DeviceDetails deviceDetails = com.mmt.hotels.emi.detail.DeviceDetails.newBuilder()
                    .setBookingDevice(emiDetailsRequest.getDeviceDetails().getBookingDevice()).build();
            emiDetailRequest.setDeviceDetails(deviceDetails);
        }

        if (CollectionUtils.isNotEmpty(emiDetailsRequest.getCoupons())) {
            List<Coupon> couponList = emiDetailsRequest.getCoupons().stream()
                    .filter(com.mmt.hotels.clientgateway.response.Coupon::isNoCostEmiApplicable)
                    .filter(coupon -> StringUtils.isNotEmpty(coupon.getBankName()))
                    .map(coupon -> Coupon.newBuilder()
                            .setCouponCode(coupon.getCode())
                            .setCouponDiscount(coupon.getCouponAmount())
                            .setBankName(coupon.getBankName())
                            .build())
                    .collect(Collectors.toList());

            Coupons coupons = Coupons.newBuilder().addAllCouponList(couponList).build();
            emiDetailRequest.setCoupons(coupons);
        }

        return emiDetailRequest.build();
    }
}
