package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.gommt.hotels.orchestrator.detail.model.response.content.StayDetails;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.ArrangementInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfoExtension;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Tag;
import com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.ComboType;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.Rooms;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.RatePlanFilter;
import com.mmt.hotels.clientgateway.response.rooms.MediaData;
import com.mmt.hotels.clientgateway.response.rooms.PropertyLayoutInfo;
import com.mmt.hotels.clientgateway.response.rooms.RecommendedCombo;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.response.rooms.RoomHighlight;
import com.mmt.hotels.clientgateway.response.rooms.RoomLayoutDetails;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.SharedInfo;
import com.mmt.hotels.clientgateway.response.rooms.SleepingArrangementDetails;
import com.mmt.hotels.clientgateway.response.rooms.SleepingArrangementRoomInfo;
import com.mmt.hotels.clientgateway.response.rooms.Space;
import com.mmt.hotels.clientgateway.response.rooms.SpaceData;
import com.mmt.hotels.clientgateway.response.rooms.StayDetail;
import com.mmt.hotels.clientgateway.response.rooms.StayInfo;
import com.mmt.hotels.clientgateway.response.rooms.StayTypeInfo;
import com.mmt.hotels.clientgateway.response.rooms.Tariff;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.flyfish.RoomSummary;
import com.mmt.hotels.model.response.flyfish.TagData;
import com.mmt.hotels.model.response.pricing.ExtraGuestDetail;
import com.mmt.hotels.util.Tuple;
import com.mmt.model.SleepingArrangement;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.BATHROOM;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.BEDROOM_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.HOSTEL_LAYOUT_TEXT_ZERO_BEDROOM_COUNT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.HOSTEL_ROOMS_AVAILABLE_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.KITCHENETTE_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.LIVING_ROOM_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.MULTIPLE_ENTIRE_PROPERTY_GENERIC_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.MULTIPLE_HETERO_PROPERTY_LAYOUT_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.ROOMS_SELLABLE_LABEL;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SINGLE_BEDROOM_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SINGLE_ENTIRE_PROPERTY_GENERIC_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SINGLE_ENTIRE_PROPERTY_ZERO_BEDROOM_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SPACE_OCCUPANCY_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SPACE_SINGLE_OCCUPANCY_TEXT;

@Component
public class RoomInfoHelper {

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    private Utility utility;

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Value("${bedroom.stay.info.icon}")
    private String bedroomStayInfoIcon;

    @Value("${bathroom.stay.info.icon}")
    private String bathroomInfoIcon;

    @Value("${black.revamp.fallback.bullet.icon}")
    private String stayInfoIcon;

    @Value("${kitchen.stay.info.icon}")
    private String kitchenStayInfoIcon;

    @Value("${livingroom.stay.info.icon}")
    private String livingroomStayInfoIcon;

    private Map<String, StayTypeInfo> actionInfoMap = new HashMap<>();

    /**
     * Extract sleeping arrangements from OrchV2 RoomInfo
     */
    public static List<SleepingArrangement> getSleepingArrangements(RoomInfo roomInfo) {
        List<SleepingArrangement> beds = null;
        if (roomInfo != null && MapUtils.isNotEmpty(roomInfo.getRoomArrangementMap()) && CollectionUtils.isNotEmpty(roomInfo.getRoomArrangementMap().get("BEDS"))) {
            beds = new ArrayList<>();
            for (ArrangementInfo arrangementInfo : roomInfo.getRoomArrangementMap().get("BEDS")) {
                SleepingArrangement bed = new SleepingArrangement();
                bed.setType(arrangementInfo.getType());
                bed.setCount(arrangementInfo.getCount());
                beds.add(bed);
            }
        }
        return beds;
    }

    /**
     * Transform OrchV2 RoomInfo to room highlights - Complete implementation adapted from legacy getRoomHighlights
     */
    public List<RoomHighlight> transformRoomHighlights(RoomInfo orchRoomInfo, ExtraGuestDetail extraGuestDetail, 
                                                       boolean altAccoHotel, boolean isOHSExpEnable, 
                                                       List<AmenityGroup> roomAmenities, String countryCode,
                                                       boolean amendRoomHighlights, boolean pilgrimageBedInfoEnable) {
        List<RoomHighlight> roomHighlights = new ArrayList<>();
        
        if (orchRoomInfo == null) {
            return roomHighlights;
        }

        // Room size and room view logic (only if not OHS experience) - following legacy pattern
        if (!isOHSExpEnable) {
            // Room size from OrchV2 RoomInfo directly
            if (StringUtils.isNotBlank(orchRoomInfo.getRoomSize())) {
                RoomHighlight roomHighlight = new RoomHighlight();
                roomHighlight.setIconUrl(IMAGE_URL_ROOM_SIZE);
                roomHighlight.setText(buildRoomSizeTextFromOrchV2(orchRoomInfo));
                roomHighlight.setDescription(roomHighlight.getText());
                roomHighlights.add(roomHighlight);
            }

            // Room view name from OrchV2 RoomInfo directly
            if (StringUtils.isNotBlank(orchRoomInfo.getRoomViewName())) {
                RoomHighlight roomHighlight = new RoomHighlight();
                roomHighlight.setIconUrl(IMAGE_URL_ROOM_NAME);
                roomHighlight.setText(orchRoomInfo.getRoomViewName());
                roomHighlight.setDescription(orchRoomInfo.getRoomViewName());
                roomHighlights.add(roomHighlight);
            }
        }

        // Process bed and bathroom info from OrchV2 spaces data
        // Note: OrchV2 doesn't have external vendor bedroom info, so we skip that logic
        // and directly process bathroom and bed info from spaces

        // Bed info from OrchV2 spaces data
        addBedHighlightsFromOrchV2(orchRoomInfo, countryCode, roomHighlights, extraGuestDetail, altAccoHotel, pilgrimageBedInfoEnable);

        // Bathroom info from OrchV2 spaces data
        addBathroomHighlightsFromOrchV2(orchRoomInfo, roomHighlights);

        // Highlighted facilities logic (for amendment rooms) - adapted for OrchV2
        String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
        if (amendRoomHighlights && CollectionUtils.isNotEmpty(orchRoomInfo.getHighlightedAmenities())) {
            // Use highlighted amenities from OrchV2 RoomInfo instead of facilities
            com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity lowestSequenceAmenity = null;
            for (com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup amenityGroup : orchRoomInfo.getHighlightedAmenities()) {
                List<com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity> amenities = amenityGroup.getAmenities();
                if (CollectionUtils.isNotEmpty(amenities)) {
                    for (com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity amenity : amenities) {
                        if (lowestSequenceAmenity == null || amenity.getSequence() < lowestSequenceAmenity.getSequence()) {
                            lowestSequenceAmenity = amenity;
                        }
                    }
                }
            }
            if (lowestSequenceAmenity != null) {
                RoomHighlight roomHighlight = buildRoomHighlightsBasicOfRoomAmenities(lowestSequenceAmenity.getName());
                roomHighlight.setIconUrl("https://promos.makemytrip.com/images/CDN_upload/blacktick_with_circle.png");
                roomHighlights.add(roomHighlight);
            }
        }

        // Room amenities logic (for OHS experience)
        if (isOHSExpEnable && !amendRoomHighlights && !CLIENT_DESKTOP.equalsIgnoreCase(client) && 
            CollectionUtils.isNotEmpty(roomAmenities)) {
            int amenitiesToShow = 2;
            for (AmenityGroup facilityGroup : roomAmenities) {
                List<Amenity> facilities = facilityGroup.getAmenities();
                if (CollectionUtils.isNotEmpty(facilities)) {
                    for (Amenity facility : facilities) {
                        if (amenitiesToShow == 0) {
                            break;
                        }
                        roomHighlights.add(buildRoomHighlightsBasicOfRoomAmenities(facility.getName()));
                        amenitiesToShow--;
                    }
                }
                if (amenitiesToShow == 0) {
                    break;
                }
            }
        }

        return roomHighlights;
    }

    /**
     * Build room size text from OrchV2 RoomInfo (adapted from legacy buildRoomSizeText)
     */
    private String buildRoomSizeTextFromOrchV2(RoomInfo orchRoomInfo) {
        String roomSizeText = null;
        if (orchRoomInfo != null && StringUtils.isNotBlank(orchRoomInfo.getRoomSize()) && 
            StringUtils.isNotBlank(orchRoomInfo.getRoomSizeUnit())) {
            roomSizeText = orchRoomInfo.getRoomSize() + SPACE + orchRoomInfo.getRoomSizeUnit();
            if (SQUARE_FEET_V2.equalsIgnoreCase(orchRoomInfo.getRoomSizeUnit())) {
                try {
                    double roomSize = Double.parseDouble(orchRoomInfo.getRoomSize());
                    roomSize = roomSize * SQUARE_FEET_TO_SQUARE_METER_CONVERSION_FACTOR;
                    long roundedRoomSize = Math.round(roomSize); // Round off to the nearest integer
                    String roomSizeInMeterSquare = String.valueOf(roundedRoomSize);
                    roomSizeText = roomSizeText + SPACE + AMENITIES_OPEN_BRACE + roomSizeInMeterSquare + SPACE + SQUARE_METER + AMENITIES_CLOSING_BRACE;
                } catch (Exception e) {
                    // Log error but continue with basic room size text
                    roomSizeText = orchRoomInfo.getRoomSize() + SPACE + orchRoomInfo.getRoomSizeUnit();
                }
            }
        }
        return roomSizeText;
    }

    /**
     * Add bathroom highlights from OrchV2 roomArrangementMap (equivalent to legacy roomInfo.getBathrooms())
     */
    private void addBathroomHighlightsFromOrchV2(RoomInfo orchRoomInfo, List<RoomHighlight> roomHighlights) {
        // Extract bathroom info from OrchV2 roomArrangementMap using "BATHROOM" key
        if (orchRoomInfo.getRoomArrangementMap() != null && 
            CollectionUtils.isNotEmpty(orchRoomInfo.getRoomArrangementMap().get("BATHROOM"))) {
            
            List<ArrangementInfo> bathrooms = orchRoomInfo.getRoomArrangementMap().get("BATHROOM");
            ArrangementInfo bathroomArrangement = bathrooms.get(0); // Follow legacy pattern - get first bathroom
            
            RoomHighlight roomHighlight = new RoomHighlight();
            roomHighlight.setIconUrl(IMAGE_URL_BATHROOM_TYPE);
            String bedTypeText;
            if (bathroomArrangement.getCount() > 1) {
                bedTypeText = bathroomArrangement.getCount() + SPACE + polyglotService.getTranslatedData(ConstantsTranslation.ROOM_DETAILS_BATHROOMS_TEXT);
            } else {
                bedTypeText = bathroomArrangement.getCount() + SPACE + polyglotService.getTranslatedData(ConstantsTranslation.ROOM_DETAILS_BATHROOM_TEXT);
            }
            roomHighlight.setText(bedTypeText);
            roomHighlight.setDescription(bedTypeText);
            roomHighlights.add(roomHighlight);
        }
    }

    /**
     * Add bed highlights from OrchV2 roomArrangementMap (equivalent to legacy roomInfo.getBeds())
     */
    private void addBedHighlightsFromOrchV2(RoomInfo orchRoomInfo, String countryCode, List<RoomHighlight> roomHighlights,
                                            ExtraGuestDetail extraGuestDetail, boolean altAccoHotel,
                                            boolean pilgrimageBedInfoEnable) {
        // Extract bed info from OrchV2 roomArrangementMap using "BEDS" and "ALTERNATE_BEDS" keys
        if (orchRoomInfo.getRoomArrangementMap() != null) {
            List<ArrangementInfo> beds = orchRoomInfo.getRoomArrangementMap().get("BEDS");
            List<ArrangementInfo> alternateBeds = orchRoomInfo.getRoomArrangementMap().get("ALTERNATE_BEDS");
            List<ArrangementInfo> externalVendorBedRooms = orchRoomInfo.getRoomArrangementMap().get("BEDROOM_INFO");
            if (!DOM_COUNTRY.equalsIgnoreCase(countryCode) && CollectionUtils.isNotEmpty(externalVendorBedRooms)) {
                externalVendorBedRooms.stream()
                        .filter(bedRoomInfo -> StringUtils.isNotBlank(bedRoomInfo.getDescription()))
                        .forEach(bedRoomInfo -> {
                            String bedTypeText = null;
                            if (StringUtils.isNotEmpty(bedRoomInfo.getName())) {
                                bedTypeText = bedRoomInfo.getName() + Constants.SPACE + Constants.HYPEN + Constants.SPACE + bedRoomInfo.getDescription();
                            } else {
                                bedTypeText = bedRoomInfo.getDescription();
                            }
                            RoomHighlight roomHighlight = new RoomHighlight();
                            if (Constants.BATHROOMS.equalsIgnoreCase(bedRoomInfo.getName())) {
                                roomHighlight.setIconUrl(Constants.IMAGE_URL_BATHROOM_TYPE);
                            } else {
                                roomHighlight.setIconUrl(Constants.IMAGE_URL_ROOM_TYPE);
                            }
                            roomHighlight.setText(bedTypeText);
                            roomHighlights.add(roomHighlight);
                        });
            } else if (pilgrimageBedInfoEnable) {
                // Pilgrimage bed info logic - with alternate beds (following legacy pattern exactly)
                if (CollectionUtils.isNotEmpty(beds) || CollectionUtils.isNotEmpty(alternateBeds)) {
                    RoomHighlight roomHighlight = new RoomHighlight();
                    roomHighlight.setIconUrl(IMAGE_URL_ROOM_TYPE);
                    List<String> bedTypeList = new ArrayList<>();
                    List<String> alternateBedTypeList = new ArrayList<>();

                    if (CollectionUtils.isNotEmpty(beds)) {
                        beds.forEach(bedType -> {
                            String bedTypeText = bedType.getCount() + SPACE + bedType.getType();
                            bedTypeList.add(bedTypeText);
                        });
                    }
                    if (CollectionUtils.isNotEmpty(alternateBeds)) {
                        alternateBeds.forEach(bedType -> {
                            String bedTypeText = bedType.getCount() + SPACE + bedType.getType();
                            alternateBedTypeList.add(bedTypeText);
                        });
                    }
                    String bedTypeListString = "";
                    if (!bedTypeList.isEmpty()) {
                        bedTypeListString = String.join(COMMA_SPACE, bedTypeList);
                        if (!alternateBedTypeList.isEmpty()) {
                            bedTypeListString = bedTypeListString + SPACE + polyglotService.getTranslatedData(ConstantsTranslation.ROOM_DETAILS_ALTERNATE_BED_TYPE_OR_TEXT) + SPACE + String.join(COMMA_SPACE, alternateBedTypeList);
                        }
                    } else {
                        bedTypeListString = String.join(COMMA_SPACE, alternateBedTypeList);
                    }

                    roomHighlight.setText(bedTypeListString);
                    roomHighlight.setDescription(bedTypeListString);
                    if (extraGuestDetail != null && StringUtils.isNotEmpty(extraGuestDetail.getRoomSelectionExtraBedText()) && !altAccoHotel) {
                        roomHighlight.setSubText(extraGuestDetail.getRoomSelectionExtraBedText());
                    }
                    roomHighlights.add(roomHighlight);
                }
            } else {
                // Standard bed info logic (following legacy pattern exactly)
                if (CollectionUtils.isNotEmpty(beds)) {
                    RoomHighlight roomHighlight = new RoomHighlight();
                    roomHighlight.setIconUrl(IMAGE_URL_ROOM_TYPE);
                    List<String> bedTypeList = new ArrayList<>();
                    beds.forEach(bedType -> {
                        String bedTypeText;
                        if (bedType.getCount() > 1) {
                            bedTypeText = bedType.getCount() + SPACE_X_SPACE + bedType.getType();
                        } else {
                            bedTypeText = bedType.getType();
                        }
                        bedTypeList.add(bedTypeText);
                    });
                    roomHighlight.setText(String.join(COMMA_SPACE, bedTypeList));
                    roomHighlight.setDescription(String.join(COMMA_SPACE, bedTypeList));
                    if (extraGuestDetail != null && StringUtils.isNotEmpty(extraGuestDetail.getRoomSelectionExtraBedText()) && !altAccoHotel) {
                        roomHighlight.setSubText(extraGuestDetail.getRoomSelectionExtraBedText());
                    }
                    roomHighlights.add(roomHighlight);
                }
            }
        }
    }

    /**
     * Build basic room highlight for amenities
     */
    private RoomHighlight buildRoomHighlightsBasicOfRoomAmenities(String facilityName) {
        RoomHighlight roomHighlight = new RoomHighlight();
        roomHighlight.setText(facilityName);
        roomHighlight.setDescription(facilityName);
        return roomHighlight;
    }

    /**
     * Transform OrchV2 SpaceData to CG SpaceData (V1)
     */
    public SpaceData getSpaceData(com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData orchSpaceData, 
                                   CommonModifierResponse commonModifierResponse) {
        if (orchSpaceData == null) {
            return null;
        }
        
        SpaceData cgSpaceData = new SpaceData();
        int extraBedCount = 0, totalBaseOccupancy = 0, totalMaxOccupancy = 0;

        cgSpaceData.setDescriptive(orchSpaceData.getDescriptive());
        cgSpaceData.setSharedInfo(buildSharedInfo(orchSpaceData.getDisplayItem()));

        List<Space> spaceList = new ArrayList<>();

        // Build CG spaces
        if (CollectionUtils.isNotEmpty(orchSpaceData.getSpaces())) {
            for (com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space orchSpace : orchSpaceData.getSpaces()) {
                Space cgSpace = new Space();
                cgSpace.setAreaText(orchSpace.getAreaText());
                cgSpace.setDescriptionText(orchSpace.getDescriptionText());
                cgSpace.setName(orchSpace.getName());
                String subText = orchSpace.getSubText();
                cgSpace.setSpaceId(orchSpace.getSpaceId());
                cgSpace.setSpaceType(orchSpace.getSpaceType());

                if (orchSpace.getSpaceType() != null &&
                    (orchSpace.getSpaceType().equalsIgnoreCase(BEDROOM) ||
                     orchSpace.getSpaceType().equalsIgnoreCase(LIVING_ROOM))) {

                    int finalOccupancy = orchSpace.getFinalOccupancy();
                    int occupancy = Math.max(finalOccupancy, orchSpace.getBaseOccupancy());
                    if (occupancy > 0) {
                        subText = (occupancy > 1) ?
                            polyglotService.getTranslatedData(ConstantsTranslation.SPACE_OCCUPANCY_TEXT) :
                            polyglotService.getTranslatedData(ConstantsTranslation.SPACE_SINGLE_OCCUPANCY_TEXT);
                    } else {
                        subText = null;
                    }
                    if (subText != null) {
                        subText = subText.replace(OCCUPANCY_PARAMETER, String.valueOf(occupancy));
                    }
                    totalBaseOccupancy += orchSpace.getBaseOccupancy();
                    totalMaxOccupancy += orchSpace.getMaxOccupancy();
                    extraBedCount += Math.max(0, finalOccupancy - orchSpace.getBaseOccupancy());
                }

                cgSpace.setSubText(subText);
                cgSpace.setOpenCardText(orchSpace.getOpenCardText());

                // Convert media from RoomEntity to MediaData
                if (CollectionUtils.isNotEmpty(orchSpace.getMedia())) {
                    List<MediaData> mediaDataList = new ArrayList<>();
                    for (com.gommt.hotels.orchestrator.detail.model.response.content.media.RoomEntity roomEntity : orchSpace.getMedia()) {
                        MediaData cgMediaData = new MediaData();
                        cgMediaData.setMediaType(roomEntity.getMediaType());
                        cgMediaData.setUrl(roomEntity.getUrl());
                        mediaDataList.add(cgMediaData);
                    }
                    cgSpace.setMedia(mediaDataList);
                }
                spaceList.add(cgSpace);
            }
        }
        cgSpaceData.setSpaces(spaceList);

        cgSpaceData.setBaseGuests(totalBaseOccupancy);
        cgSpaceData.setExtraBeds(extraBedCount);
        cgSpaceData.setMaxGuests(totalMaxOccupancy);
        return cgSpaceData;
    }

    /**
     * Transform OrchV2 SpaceData to CG SpaceData (V2)
     */
    public SpaceData getSpaceDataV2(com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData orchSpaceData, 
                                     boolean isPrivateSpace) {
        if (orchSpaceData == null) {
            return null;
        }
        
        SpaceData cgSpaceData = new SpaceData();
        int extraBedCount = 0, totalBaseOccupancy = 0, totalMaxOccupancy = 0;

        cgSpaceData.setDescriptive(orchSpaceData.getDescriptive());
        cgSpaceData.setSharedInfo(buildSharedInfo(orchSpaceData.getDisplayItem()));

        List<Space> spaceList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orchSpaceData.getSpaces())) {
            for (com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space orchSpace : orchSpaceData.getSpaces()) {
                Space cgSpace = new Space();
                cgSpace.setName(orchSpace.getName());
                cgSpace.setSpaceId(orchSpace.getSpaceId());
                cgSpace.setSpaceType(orchSpace.getSpaceType());
                cgSpace.setAreaText(orchSpace.getAreaText());
                cgSpace.setSpaceInclusions(buildSpaceInclusion(orchSpace));

                if (isPrivateSpace) {
                    cgSpace.setAreaText(null);
                } else {
                    cgSpace.setDescriptionText(orchSpace.getDescriptionText());
                    String subText = orchSpace.getSubText();
                    if (orchSpace.getSpaceType() != null &&
                        (orchSpace.getSpaceType().equalsIgnoreCase(BEDROOM) ||
                         orchSpace.getSpaceType().equalsIgnoreCase(LIVING_ROOM))) {

                        int finalOccupancy = orchSpace.getFinalOccupancy();
                        int occupancy = Math.max(finalOccupancy, orchSpace.getBaseOccupancy());
                        if (occupancy > 0) {
                            subText = (occupancy > 1) ?
                                polyglotService.getTranslatedData(ConstantsTranslation.SPACE_OCCUPANCY_TEXT) :
                                polyglotService.getTranslatedData(ConstantsTranslation.SPACE_SINGLE_OCCUPANCY_TEXT);
                        } else {
                            subText = null;
                        }
                        if (subText != null) {
                            subText = subText.replace(OCCUPANCY_PARAMETER, String.valueOf(occupancy));
                        }
                    }
                    cgSpace.setSubText(subText);
                    //cgSpace.setOpenCardText(orchSpace.getOpenCardText());
                }

                // Convert media from RoomEntity to MediaData
                if (CollectionUtils.isNotEmpty(orchSpace.getMedia())) {
                    List<MediaData> mediaDataList = new ArrayList<>();
                    for (com.gommt.hotels.orchestrator.detail.model.response.content.media.RoomEntity roomEntity : orchSpace.getMedia()) {
                        MediaData cgMediaData = new MediaData();
                        cgMediaData.setMediaType(roomEntity.getMediaType());
                        cgMediaData.setUrl(roomEntity.getUrl());
                        mediaDataList.add(cgMediaData);
                    }
                    cgSpace.setMedia(mediaDataList);
                }
                spaceList.add(cgSpace);
            }
        }
        cgSpaceData.setSpaces(spaceList);

        cgSpaceData.setBaseGuests(totalBaseOccupancy);
        cgSpaceData.setExtraBeds(extraBedCount);
        cgSpaceData.setMaxGuests(totalMaxOccupancy);
        return cgSpaceData;
    }

    /**
     * Build SharedInfo from OrchV2 DisplayItem
     */
    private SharedInfo buildSharedInfo(com.gommt.hotels.orchestrator.detail.model.response.common.DisplayItem displayItem) {
        if (displayItem == null) {
            return null;
        }
        SharedInfo sharedInfo = new SharedInfo();
        sharedInfo.setIconUrl(displayItem.getIconUrl());
        sharedInfo.setInfoText(displayItem.getText());
        return sharedInfo;
    }

    /**
     * Build open card text from OrchV2 Space
     */
    private String buildOpenCardText(com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space orchSpace, Map<String, String> expDataMap) {
        if (expDataMap != null && expDataMap.get("plcnew") != null && StringUtils.equalsIgnoreCase(expDataMap.get("plcnew"), "true")) {
            StringBuilder openCardText = new StringBuilder(orchSpace.getDescriptionText());
            if (StringUtils.isNotBlank(orchSpace.getOpenCardText())) {
                openCardText.append(BREAK_AMENITIES_BOLD).append(orchSpace.getOpenCardText());
            }
            return openCardText.toString();
        }

        return orchSpace.getOpenCardText();
    }

    /**
     * Build space inclusions from OrchV2 Space
     */
    private List<String> buildSpaceInclusion(com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space orchSpace) {
        StringBuilder responseString = new StringBuilder();
        if (orchSpace != null) {
            if (orchSpace.getSleepingDetails() != null) {
                if (CollectionUtils.isNotEmpty(orchSpace.getSleepingDetails().getBedInfo())) {
                    for (com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.ArrangementInfo bedInfo : orchSpace.getSleepingDetails().getBedInfo()) {
                        if (StringUtils.isNotEmpty(responseString.toString())) {
                            responseString.append(SPACE).append(AMP).append(SPACE).append(buildBedType(bedInfo));
                        } else {
                            responseString.append(buildBedType(bedInfo));
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(orchSpace.getSleepingDetails().getExtraBedInfo())) {
                    for (com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.ArrangementInfo bedInfo : orchSpace.getSleepingDetails().getExtraBedInfo()) {
                        responseString.append(COMMA_SPACE).append(EXTRA).append(SPACE).append(buildBedType(bedInfo)).append(SPACE).append(AVAILABLE.toLowerCase());
                    }
                }
            }
            if (StringUtils.isNotEmpty(responseString.toString()) && StringUtils.isNotEmpty(orchSpace.getDescriptionText())) {
                responseString.append(PIPE_SEPARATOR);
            }
            responseString.append(orchSpace.getDescriptionText());
        }
        return StringUtils.isNotEmpty(responseString.toString()) ? 
            Arrays.asList(responseString.toString().split(PIPE_SEPARATOR_WITH_BACKSLASH)) : null;
    }

    /**
     * Build bed type text from ArrangementInfo
     */
    private String buildBedType(com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.ArrangementInfo bedInfo) {
        if (bedInfo != null && StringUtils.isNotEmpty(bedInfo.getType())) {
            if (bedInfo.getCount() > 1) {
                return bedInfo.getCount() + SPACE + bedInfo.getType() + PLURAL_STRING;
            } else if (bedInfo.getCount() == 1) {
                return bedInfo.getCount() + SPACE + bedInfo.getType();
            } else {
                return bedInfo.getType();
            }
        }
        return null;
    }

    /**
     * Build complete room info object from OrchV2 data
     */
    public SleepingArrangementRoomInfo buildRoomInfo(HotelDetails hotelDetails, SearchRoomsResponse searchRoomsResponse,
                                                     String countryCode, boolean isOHSExpEnable, boolean isNewDetailPageTrue,
                                                     boolean isIHAAOrch, Map<String, String> expDataMap) {
        if (hotelDetails == null) {
            return null;
        }

        SleepingArrangementRoomInfo roomInfo = new SleepingArrangementRoomInfo();

        // Build stay details
        String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
        boolean serviceApartment = hotelDetails.getHotelRateFlags() != null && hotelDetails.getHotelRateFlags().isServiceApartment();
        if (hotelDetails.getStayDetails() != null) {
            buildStayDetails(roomInfo, hotelDetails.getStayDetails());
        }

        if (roomInfo.getStayDetail() != null) {
            StayTypeInfo stayTypeInfo = buildStayTypeInfo(hotelDetails.getSellableUnit(), hotelDetails.getPropertyType(), roomInfo.getTitle(), serviceApartment);
            roomInfo.getStayDetail().setStayTypeInfo(stayTypeInfo);
        }

        // Set basic room info properties
        if (roomInfo.getStayDetail() == null) {
            roomInfo.setStayDetail(new StayDetail());
        }

        // Set guest room key-value based on OrchV2 data
        updateGuestRoomDetails(hotelDetails, countryCode, expDataMap, roomInfo);

        // Get additional data needed for room info
        String freeChildText = getFreeChildTextFromHotelDetails(hotelDetails);
        Pair<Boolean, Boolean> bedAndRoomPresent = new ImmutablePair<>(false,false);
        if (isOHSExpEnable) {
            bedAndRoomPresent = addSellableLabelFromSellableType(searchRoomsResponse);
            updateSearchRoomsResponseFilters(searchRoomsResponse, bedAndRoomPresent);
        }

        // Build sleep info text based on stay details
        if (roomInfo.getStayDetail() != null) {
            buildSleepInfoText(roomInfo.getStayDetail(), isOHSExpEnable, hotelDetails.getAdditionalDetails().getSellableCombo(),
                    bedAndRoomPresent, freeChildText, isNewDetailPageTrue);
        }

        // Set propertyInfoText from room size
        roomInfo.setPropertyInfoText("");

        if (isIHAAOrch && hotelDetails.getHotelRateFlags().isAltAcco() && !PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelDetails.getPropertyType())) {
            buildSleepingArrangementFromBedroomInfo(searchRoomsResponse, hotelDetails, roomInfo, countryCode);
            if (Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(client)) {
                roomInfo.setSizeDesc(getRoomSizeWithUnit(hotelDetails));
            }
        }

        setRoomDescription(searchRoomsResponse, countryCode, hotelDetails, isIHAAOrch, roomInfo);

        return roomInfo;
    }

    private void buildStayDetails(SleepingArrangementRoomInfo roomInfo, StayDetails stayDetails) {
        StayDetail stayDetail = new StayDetail();
        stayDetail.setBedRoom(stayDetails.getRoomCount());
        stayDetail.setBed(stayDetails.getBedCount());
        stayDetail.setMaxCapacity(stayDetails.getMaxCapacity());
        stayDetail.setMaxGuests(stayDetails.getGuestCount());
        stayDetail.setBaseGuests(stayDetails.getGuestCount());
        stayDetail.setBedInfoText(stayDetails.getBedInfoText());
        stayDetail.setBedInfoMap(stayDetails.getBedInfoMap());
        stayDetail.setExtraBeds(stayDetails.getExtraBeds());
        stayDetail.setBathroom(stayDetails.getBathroomCount());
        stayDetail.setShowBathroomCount(stayDetails.isShowBathroomCount());
        roomInfo.setStayDetail(stayDetail);
        roomInfo.setTitle(stayDetails.getStayTypeText());
        roomInfo.setBedInfoText(stayDetail.getBedInfoText());
    }


    // Copy from V1 transformer
    private void setRoomDescription(SearchRoomsResponse searchRoomsResponse, String countryCode, HotelDetails hotelDetails, boolean isIHAltAccoNodesExp, SleepingArrangementRoomInfo roomInfo) {
        if (!DOM_COUNTRY.equalsIgnoreCase(countryCode) && isIHAltAccoNodesExp) {
            List<StayInfo> stayDetailList = buildStayInfoList(hotelDetails, searchRoomsResponse);
            if (CollectionUtils.isNotEmpty(stayDetailList)) {
                roomInfo.getStayDetail().setStayInfoList(stayDetailList);
            }
            String roomInfoDescription = null;
            if (roomInfo.getStayDetail() != null && CollectionUtils.isNotEmpty(roomInfo.getStayDetail().getStayInfoList())) {
                if (hotelDetails.getRoomCount() > 1) {
                    if (CollectionUtils.isEmpty(hotelDetails.getRoomCombos())) {
                        roomInfoDescription = MessageFormat.format(polyglotService.getTranslatedData(MULTIPLE_ENTIRE_PROPERTY_GENERIC_TEXT), hotelDetails.getRoomCount());
                    } else if (CollectionUtils.isNotEmpty(hotelDetails.getRoomCombos()) && hotelDetails.getRoomCombos().stream()
                            .filter(combo -> combo.getComboType() == ComboType.RECOMMENDED_ROOM).count() == 1) {
                        roomInfoDescription = MessageFormat.format(polyglotService.getTranslatedData(MULTIPLE_ENTIRE_PROPERTY_GENERIC_TEXT), hotelDetails.getRoomCount());
                    } else {
                        roomInfoDescription = MessageFormat.format(polyglotService.getTranslatedData(MULTIPLE_HETERO_PROPERTY_LAYOUT_TEXT), hotelDetails.getRoomCount(), hotelDetails.getPropertyType());
                    }
                } else {
                    roomInfoDescription = polyglotService.getTranslatedData(SINGLE_ENTIRE_PROPERTY_GENERIC_TEXT);
                }
                if ((roomInfo.getStayDetail() != null && Objects.isNull(roomInfo.getStayDetail().getBedRoom())) ||
                        (roomInfo.getStayDetail() != null && Objects.nonNull(roomInfo.getStayDetail().getBedRoom()) &&
                                roomInfo.getStayDetail().getBedRoom() == 0)) {
                    if (PROPERTY_TYPE_APPARTMENT.equalsIgnoreCase(hotelDetails.getPropertyType())) {
                        roomInfoDescription = polyglotService.getTranslatedData(SINGLE_ENTIRE_PROPERTY_ZERO_BEDROOM_TEXT);
                    }
                }
                if (PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelDetails.getPropertyType())) {
                    if ((searchRoomsResponse.getExactRooms() != null && !searchRoomsResponse.getExactRooms().isEmpty()) || (
                            CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos()) && searchRoomsResponse.getRecommendedCombos().get(0) != null
                                    && CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos().get(0).getRooms()) && searchRoomsResponse.getRecommendedCombos().get(0).getRooms().size() == 1)) {
                        roomInfoDescription = polyglotService.getTranslatedData(HOSTEL_LAYOUT_TEXT_ZERO_BEDROOM_COUNT);
                        if ((roomInfo.getStayDetail() != null && CollectionUtils.isNotEmpty(roomInfo.getStayDetail().getStayInfoList())
                                && roomInfo.getStayDetail().getStayInfoList().size() == 1 &&
                                PRIVATE_ROOMS_AVAILABLE.equalsIgnoreCase(roomInfo.getStayDetail().getStayInfoList().get(0).getInfoText()))) {
                            roomInfoDescription = null;
                        }
                    } else {
                        roomInfoDescription = null;
                    }
                }
                roomInfo.setDescription(roomInfoDescription);
            }
        } else {
            roomInfo.setDescription(isIHAltAccoNodesExp && LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelDetails.getListingType()) && (hotelDetails.getRoomCount() > 1) ? polyglotService.getTranslatedData(ROOM_INFO_SUBTITLE) : null);
        }
    }


    /**
     * Update guest room details from OrchV2 data
     */
    public void updateGuestRoomDetails(HotelDetails hotelDetails, String countryCode, Map<String, String> expDataMap, SleepingArrangementRoomInfo roomInfo) {
        if (CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {
            Rooms firstRoom = hotelDetails.getRooms().get(0);
            if (firstRoom != null && CollectionUtils.isNotEmpty(firstRoom.getRatePlans())) {
                RatePlan firstRatePlan = firstRoom.getRatePlans().get(0);
                if (firstRatePlan != null && firstRatePlan.getAvailDetail() != null && firstRatePlan.getAvailDetail().getOccupancyDetails() != null) {
                    OccupancyDetails occupancyDetails = firstRatePlan.getAvailDetail().getOccupancyDetails();
                    Map<String, Integer> roomBedCount = new HashMap<>();
                    roomBedCount.put(Constants.SELLABLE_ROOM_TYPE, occupancyDetails != null ? occupancyDetails.getNumberOfRooms() : 1);
                    // Note: bedCount is not available in OrchV2 occupancy details, using default value
                    roomBedCount.put(Constants.SELLABLE_BED_TYPE, occupancyDetails != null ? occupancyDetails.getBedCount() : 0);

                    Tuple<String, String> guestRoomKeyValue = utility.getGuestRoomKeyValue(roomBedCount, hotelDetails.getPropertyType(), countryCode, hotelDetails.getHotelRateFlags().isHighSellingAltAcco(),
                            hotelDetails.getHotelRateFlags().isAltAcco(), null, hotelDetails.getListingType(), false, 0, expDataMap, false
                    );
                    roomInfo.setGuestRoomKey(guestRoomKeyValue.getX());
                    roomInfo.setGuestRoomValue(guestRoomKeyValue.getY());
                }
            }
        }
    }

    public void buildStayDetails(RoomDetails roomDetails) {
        int roomCount = 1;
        if (CollectionUtils.isNotEmpty(roomDetails.getRatePlans()) && CollectionUtils.isNotEmpty(roomDetails.getRatePlans().get(0).getTariffs())) {
            Tariff tariff = roomDetails.getRatePlans().get(0).getTariffs().get(0);
            if (tariff.getOccupancydetails() != null && tariff.getOccupancydetails().getRoomCount() != null) {
                roomCount = tariff.getOccupancydetails().getRoomCount();
            }
        }
        StayDetail stayDetail = new StayDetail();
        if (roomDetails.getBedCount() != null) {
            stayDetail.setBed(roomCount * roomDetails.getBedCount());
        }
        if (roomDetails.getMaxGuest() != null) {
            stayDetail.setMaxGuests(roomCount * roomDetails.getMaxGuest());
        }
        if (roomDetails.getBedroomCount() != null) {
            stayDetail.setBedRoom(roomCount * roomDetails.getBedroomCount());
        }
        if (roomDetails.getExtraBedCount() != null) {
            stayDetail.setExtraBeds(roomCount * roomDetails.getExtraBedCount());
        }
        if (roomDetails.getBaseGuest() != null) {
            stayDetail.setBaseGuests(roomCount * roomDetails.getBaseGuest());
        }
        if (roomDetails.getBathroomCount() != null) {
            stayDetail.setBathroom(roomCount * roomDetails.getBathroomCount());
        }
        if (roomDetails.getMaxGuest() != null) {
            stayDetail.setMaxCapacity(roomCount * roomDetails.getMaxGuest());
        }
        roomDetails.setStayDetail(stayDetail);
    }

    /**
     * Build sleeping arrangement from bedroom info - Copy from V1 transformer
     */
    public void buildSleepingArrangementFromBedroomInfo(SearchRoomsResponse searchRoomsResponse, HotelDetails hotelDetails,
                                                        SleepingArrangementRoomInfo roomInfo, String countryCode) {
        // Simplified implementation for OrchV2
        if (roomInfo.getStayDetail() == null) {
            roomInfo.setStayDetail(new StayDetail());
        }

        if (!DOM_COUNTRY.equalsIgnoreCase(countryCode) && hotelDetails != null && searchRoomsResponse != null && CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {
            if (searchRoomsResponse.getExactRooms() != null && !searchRoomsResponse.getExactRooms().isEmpty()) {
                List<RoomDetails> roomDetailsList = searchRoomsResponse.getExactRooms();
                RoomDetails roomDetails = roomDetailsList.get(0);
                buildSleepingArrangementDetails(hotelDetails, roomInfo, roomDetails);
            } else if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos()) && searchRoomsResponse.getRecommendedCombos().get(0) != null && CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos().get(0).getRooms()) && searchRoomsResponse.getRecommendedCombos().get(0).getRooms().size() == 1) {
                List<RecommendedCombo> recommendedCombos = searchRoomsResponse.getRecommendedCombos();
                RecommendedCombo recommendedCombo = recommendedCombos.get(0);
                List<RoomDetails> roomDetailsList = recommendedCombo.getRooms();
                RoomDetails roomDetails = roomDetailsList.get(0);
                buildSleepingArrangementDetails(hotelDetails, roomInfo, roomDetails);
            }
        }
    }

    private void buildSleepingArrangementDetails(HotelDetails hotelDetails, SleepingArrangementRoomInfo roomInfo, RoomDetails roomDetails) {
        if (roomDetails != null && roomDetails.getPrivateSpaces() == null && roomDetails.getPrivateSpacesV2() == null && hotelDetails.getRooms().get(0).getRoomInfo() != null) {
            SleepingArrangementDetails sleepingArrangementDetails = null;
            RoomInfo orchRoomInfo = hotelDetails.getRooms().get(0).getRoomInfo();
            if (orchRoomInfo != null && orchRoomInfo.getBedRoomCount() > 1) {
                List<RoomLayoutDetails> roomLayoutDetailsList = getRoomLayoutDetailsExternalVendor(orchRoomInfo);
                if (CollectionUtils.isNotEmpty(roomLayoutDetailsList)) {
                    sleepingArrangementDetails = new SleepingArrangementDetails();
                    sleepingArrangementDetails.setHeaderText("Property Layout");
                    sleepingArrangementDetails.setRoomLayoutDetailsList(roomLayoutDetailsList);
                }
            }
            roomInfo.setSleepingArrangement(sleepingArrangementDetails);
        }
    }

    private List<RoomLayoutDetails> getRoomLayoutDetailsExternalVendor(RoomInfo roomInfos) {
        if (roomInfos == null || MapUtils.isEmpty(roomInfos.getRoomArrangementMap()) || !roomInfos.getRoomArrangementMap().containsKey("BEDROOM_INFO")) {
            return null;
        }
        List<RoomLayoutDetails> roomLayoutDetailsList = new ArrayList<>();
        for (ArrangementInfo externalVendorBedRoomInfo : roomInfos.getRoomArrangementMap().get("BEDROOM_INFO")) {
            RoomLayoutDetails roomLayoutDetails = new RoomLayoutDetails();
            roomLayoutDetails.setTitle(StringUtils.isNotEmpty(externalVendorBedRoomInfo.getName()) ? externalVendorBedRoomInfo.getName() : polyglotService.getTranslatedData(SINGLE_BEDROOM_TITLE));
            if (roomInfos.getBedRoomCount() == 1 && BEDROOM_1.equalsIgnoreCase(externalVendorBedRoomInfo.getName())) {
                roomLayoutDetails.setTitle(polyglotService.getTranslatedData(SINGLE_BEDROOM_TITLE));
            }
            roomLayoutDetails.setSubTitle(externalVendorBedRoomInfo.getDescription());
            if (Constants.BATHROOMS.equalsIgnoreCase(externalVendorBedRoomInfo.getName())) {
                roomLayoutDetails.setIconUrl(Constants.IMAGE_URL_BATHROOM_TYPE);
            } else {
                roomLayoutDetails.setIconUrl(IMAGE_URL_DOUBLE_BED);
            }
            if (SINGLE_SOFA_BED.equalsIgnoreCase(externalVendorBedRoomInfo.getDescription())) {
                roomLayoutDetails.setTitle("Common Area");
                roomLayoutDetails.setIconUrl(IMAGE_URL_SOFA_BED);
            }
            roomLayoutDetailsList.add(roomLayoutDetails);
        }
        if (CollectionUtils.isNotEmpty(roomLayoutDetailsList)) {
            if (roomLayoutDetailsList.size() == 1 && !Constants.BATHROOMS.equalsIgnoreCase(roomLayoutDetailsList.get(0).getTitle())) {
                roomLayoutDetailsList.get(0).setTitle(polyglotService.getTranslatedData(SINGLE_BEDROOM_TITLE));
            } else if (roomLayoutDetailsList.size() == 2 && Constants.BATHROOMS.equalsIgnoreCase(roomLayoutDetailsList.get(1).getTitle())) {
                roomLayoutDetailsList.get(0).setTitle(polyglotService.getTranslatedData(SINGLE_BEDROOM_TITLE));
            }
        }
        return roomLayoutDetailsList;
    }

    /**
     * Get room size with unit (simplified for OrchV2)
     */
    public String getRoomSizeWithUnit(HotelDetails hotelDetails) {
        // First try to get room info from rooms
        RoomInfo roomInfo = Optional.ofNullable(hotelDetails.getRooms())
                .filter(rooms -> !rooms.isEmpty())
                .flatMap(rooms -> rooms.stream()
                        .filter(room -> room != null && room.getRoomInfo() != null)
                        .map(Rooms::getRoomInfo)
                        .findFirst())
                .orElse(null);

        // If not found in rooms, try room combos
        if (roomInfo == null) {
            roomInfo = Optional.ofNullable(hotelDetails.getRoomCombos())
                    .filter(roomCombos -> !roomCombos.isEmpty())
                    .flatMap(roomCombos -> roomCombos.stream()
                            .filter(roomCombo -> roomCombo != null && roomCombo.getRooms() != null && !roomCombo.getRooms().isEmpty())
                            .flatMap(roomCombo -> roomCombo.getRooms().stream())
                            .filter(room -> room != null && room.getRoomInfo() != null)
                            .map(Rooms::getRoomInfo)
                            .findFirst())
                    .orElse(null);
        }

        // Return room size with unit if available
        if (roomInfo != null && roomInfo.getRoomSize() != null && roomInfo.getRoomSizeUnit() != null) {
            return roomInfo.getRoomSize() + " " + roomInfo.getRoomSizeUnit();
        }
        return "";
    }

    /**
     * Get free child text from hotel details
     */
    public String getFreeChildTextFromHotelDetails(HotelDetails hotelDetails) {
        if (CollectionUtils.isNotEmpty(hotelDetails.getRoomCombos())) {
            for (RoomCombo roomCombo : hotelDetails.getRoomCombos()) {
                if (StringUtils.isNotEmpty(roomCombo.getFreeChildText())) {
                    return roomCombo.getFreeChildText();
                }
            }
        }
        return getFreeChildText(hotelDetails.getRooms());
    }

    private String getFreeChildText(List<Rooms> rooms) {
        return Optional.ofNullable(rooms)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(roomList -> roomList.stream()
                        .filter(room -> CollectionUtils.isNotEmpty(room.getRatePlans()))
                        .flatMap(room -> room.getRatePlans().stream())
                        .map(RatePlan::getFreeChildText)
                        .filter(StringUtils::isNotEmpty)
                        .findFirst())
                .orElse(null);
    }

    /**
     * Add sellable label from sellable type
     */
    public Pair<Boolean, Boolean> addSellableLabelFromSellableType(SearchRoomsResponse searchRoomsResponse) {
        if (searchRoomsResponse == null) {
            return new ImmutablePair<>(false, false);
        }

        boolean bedAvailable = false, roomAvailable = false;
        Pair<Boolean, Boolean> isBedAndRoomAvailable = null;
        if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos())) {
            for (RecommendedCombo recommendedCombo : searchRoomsResponse.getRecommendedCombos()) {
                if (recommendedCombo != null) {
                    isBedAndRoomAvailable = addSellableLabelAndRoomTypeFilterCode(recommendedCombo.getRooms());
                    bedAvailable = bedAvailable || isBedAndRoomAvailable.getKey();
                    roomAvailable = roomAvailable || isBedAndRoomAvailable.getValue();
                }
            }
        }
        isBedAndRoomAvailable = addSellableLabelAndRoomTypeFilterCode(searchRoomsResponse.getOccupancyRooms());
        bedAvailable = bedAvailable || isBedAndRoomAvailable.getKey();
        roomAvailable = roomAvailable || isBedAndRoomAvailable.getValue();
        isBedAndRoomAvailable = addSellableLabelAndRoomTypeFilterCode(searchRoomsResponse.getExactRooms());
        bedAvailable = bedAvailable || isBedAndRoomAvailable.getKey();
        roomAvailable = roomAvailable || isBedAndRoomAvailable.getValue();
        return new ImmutablePair<>(bedAvailable, roomAvailable);
    }

    /***
     * This function add SellableLabel and RoomTypeFilterCode for each roomCode
     * @param rooms
     * @return boolean true/false basis of {sellableTypeBed,sellableTypeRoom} is available for list of roomDetails.
     */
    private Pair<Boolean, Boolean> addSellableLabelAndRoomTypeFilterCode(List<RoomDetails> rooms) {
        boolean sellableTypeBed = false, sellableTypeRoom = false;
        if (CollectionUtils.isNotEmpty(rooms))
            for (RoomDetails room : rooms) {
                List<String> filterCode = new ArrayList<>();
                String sellableType = (CollectionUtils.isNotEmpty(room.getRatePlans()) ? room.getRatePlans().get(0).getSellableType() : null);
                if (Constants.SELLABLE_BED_TYPE.equalsIgnoreCase(sellableType)) {
                    room.setSellableLabel(polyglotService.getTranslatedData(ConstantsTranslation.BEDS_SELLABLE_LABEL));
                    filterCode.add(BEDS_SELLABLE_TYPE_FILTER_CODE);
                    sellableTypeBed = true;
                } else if (Constants.SELLABLE_ROOM_TYPE.equalsIgnoreCase(sellableType)) {
                    room.setSellableLabel(polyglotService.getTranslatedData(ConstantsTranslation.ROOMS_SELLABLE_LABEL));
                    filterCode.add(ROOMS_SELLABLE_TYPE_FILTER_CODE);
                    sellableTypeRoom = true;
                }
                room.setFilterCode(filterCode);
            }
        return new ImmutablePair<>(sellableTypeBed, sellableTypeRoom);
    }

    /**
     * Build sleep info text from stay details
     */
    public void buildSleepInfoText(StayDetail stayDetail, boolean isOHSExpEnable, int sellableCombo,
                                   Pair<Boolean, Boolean> bedAndRoomPresent,
                                   String freeChildText, boolean isNewDetailPageTrue) {
        String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
        if (StringUtils.isNotBlank(client) && stayDetail != null && stayDetail.getMaxGuests() != null && stayDetail.getMaxGuests() > 0) {
            String polyglotSleepsText = stayDetail.getMaxGuests() == 1 ? polyglotService.getTranslatedData(ConstantsTranslation.SPACE_SINGLE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(ConstantsTranslation.SPACE_OCCUPANCY_TEXT);
            if (StringUtils.isNotBlank(polyglotSleepsText)) {
                String sleepsText = polyglotSleepsText.replace(Constants.OCCUPANCY_PARAMETER, String.valueOf(stayDetail.getMaxGuests()));
                stayDetail.setSleepInfoText(sleepsText);

                int maxCapacity = stayDetail.getMaxCapacity() != null ? stayDetail.getMaxCapacity() : 0;
                int maxGuests = stayDetail.getMaxGuests() != null ? stayDetail.getMaxGuests() : 0;
                if ((maxCapacity > maxGuests) || StringUtils.isNotEmpty(freeChildText)) {
                    int extraGuests = maxCapacity - maxGuests;
                    String polyglotExtraGuestsText = extraGuests == 1 ? polyglotService.getTranslatedData(ConstantsTranslation.SPACE_SINGLE_OCCUPANCY_EXTRA_GUESTS) : polyglotService.getTranslatedData(ConstantsTranslation.SPACE_OCCUPANCY_EXTRA_GUESTS);
                    if (StringUtils.isNotBlank(polyglotSleepsText)) {
                        String extraGuestsText = StringUtils.isNotEmpty(freeChildText) ? freeChildText : polyglotExtraGuestsText.replace(Constants.EXTRA_PARAMETER, String.valueOf(extraGuests));
                        if (Constants.CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
                            stayDetail.setSleepInfoText(sleepsText);
                            stayDetail.setAdditionalSleepInfoText(extraGuestsText);
                        } else {
                            if (isNewDetailPageTrue && (Constants.ANDROID.equalsIgnoreCase(client) || Constants.DEVICE_IOS.equalsIgnoreCase(client) || Constants.DEVICE_OS_PWA.equalsIgnoreCase(client))) {
                                stayDetail.setSleepInfoText(sleepsText + Constants.COMMA + Constants.SPACE + extraGuestsText);
                            } else {
                                stayDetail.setSleepInfoText(Constants.OPEN_BOLD_TAG + sleepsText + Constants.CLOSE_BOLD_TAG + Constants.SPACE + Constants.BULLET_HTML + Constants.SPACE + extraGuestsText);
                            }
                        }
                    }
                }
                if (isOHSExpEnable) {
                    if (sellableCombo == 1) {
                        // sellableType bed available in lowestRoomTypeCode, So Suggest private rooms
                        stayDetail.setBedInfoText(null);
                        if (Constants.CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
                            stayDetail.setSleepInfoText(null);
                            stayDetail.setAdditionalSleepInfoText(null);
                            if (bedAndRoomPresent.getRight()) {
                                stayDetail.setBedAndRoomAvailabilityText(polyglotService.getTranslatedData(ConstantsTranslation.HOSTEL_ROOMS_AVAILABLE_TEXT));
                            }
                        } else {
                            if (bedAndRoomPresent.getRight()) {
                                stayDetail.setSleepInfoText(polyglotService.getTranslatedData(ConstantsTranslation.HOSTEL_ROOMS_AVAILABLE_TEXT));
                            } else {
                                stayDetail.setSleepInfoText(null);
                            }
                        }
                    } else if (sellableCombo == 2) {
                        // sellableType bedRoom available in lowestRoomTypeCode so suggest shared dorm
                        if (Constants.CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
                            if (bedAndRoomPresent.getLeft()) {
                                stayDetail.setBedAndRoomAvailabilityText(polyglotService.getTranslatedData(ConstantsTranslation.HOSTEL_BEDS_AVAILABLE_TEXT));
                            }
                        } else {
                            stayDetail.setBedInfoText(sleepsText);
                            if (bedAndRoomPresent.getLeft()) {
                                stayDetail.setSleepInfoText(polyglotService.getTranslatedData(ConstantsTranslation.HOSTEL_BEDS_AVAILABLE_TEXT));
                            } else {
                                stayDetail.setSleepInfoText(null);
                            }
                        }
                    } else {
                        stayDetail.setSleepInfoText(null);
                        stayDetail.setAdditionalSleepInfoText(null);
                        stayDetail.setBedInfoText(null);
                    }
                }
            }
        }
    }

    public PropertyLayoutInfo buildPropertyLayoutInfo(Rooms room) {
       if (room.getPropertyLayout() != null && CollectionUtils.isNotEmpty(room.getPropertyLayout().getUnits())) {
           PropertyLayoutInfo propertyLayoutInfo = new PropertyLayoutInfo();
           propertyLayoutInfo.setUnits(room.getPropertyLayout().getUnits());
           return propertyLayoutInfo;
       }
       return null;
    }

    /**
     * Build stay info list -  Copy from V1 transformer
     */
    public List<StayInfo> buildStayInfoList(HotelDetails hotelDetails, SearchRoomsResponse searchRoomsResponse) {
        if (hotelDetails != null && searchRoomsResponse != null) {
            String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
            boolean isRequestFromApps = Constants.ANDROID.equalsIgnoreCase(client) || Constants.DEVICE_IOS.equalsIgnoreCase(client);

            List<StayInfo> stayInfoList = new ArrayList<>();
            // Cases where exact match was found for eg. 1 room 2 adults
            if (CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms()) && searchRoomsResponse.getExactRooms().get(0) != null) {
                RoomDetails roomDetails = searchRoomsResponse.getExactRooms().get(0);
                StayDetail stayDetail = roomDetails != null ? roomDetails.getStayDetail() : null;
                if (stayDetail != null) {
                    RoomInfo roomInfos = CollectionUtils.isNotEmpty(hotelDetails.getRooms()) ? hotelDetails.getRooms().get(0).getRoomInfo() : null;
                    if(isRequestFromApps) {
                        if (roomDetails.getBedroomCount() != null && roomDetails.getBedroomCount() > 0 && !PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelDetails.getPropertyType())) {
                            StayInfo stayInfo = null;
                            if (roomDetails.getBedroomCount() == 1) {
                                stayInfo = new StayInfo();
                                String stayInfoText = "";
                                String polyglotSleepsText = "";
                                stayInfoText = "1" + SPACE + polyglotService.getTranslatedData(SINGLE_BEDROOM_TITLE);
                                if (roomDetails.getStayDetail() != null && roomDetails.getMaxGuest() != null && roomDetails.getMaxGuest() > 0) {
                                    polyglotSleepsText = roomDetails.getMaxGuest() == 1 ? polyglotService.getTranslatedData(SPACE_SINGLE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(SPACE_OCCUPANCY_TEXT);
                                    polyglotSleepsText = polyglotSleepsText.replace(OCCUPANCY_PARAMETER, String.valueOf(roomDetails.getMaxGuest()));
                                }

                                String bedNamesText = getBedNamesFromExternalVendor(roomInfos);

                                if (StringUtils.isNotEmpty(bedNamesText)) {
                                    stayInfoText += HYPHEN_SPACE + bedNamesText;
                                }

                                if (StringUtils.isNotEmpty(polyglotSleepsText)) {
                                    if (StringUtils.isNotEmpty(bedNamesText)) {
                                        stayInfoText += COMMA_SPACE + polyglotSleepsText;
                                    } else {
                                        stayInfoText += HYPHEN_SPACE + polyglotSleepsText;
                                    }
                                }
                                stayInfo.setInfoText(stayInfoText);
                                stayInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bedroomStayInfoIcon : stayInfoIcon);
                            } else if (roomDetails.getBedroomCount() > 1) {
                                stayInfo = new StayInfo();
                                String stayInfoText = "";
                                String polyglotSleepsText = "";
                                stayInfoText = roomDetails.getBedroomCount() + SPACE + polyglotService.getTranslatedData(BEDROOM_TITLE);
                                String bedCountText = getBedCountInfoFromRoomInfo(roomInfos);
                                if (roomDetails.getStayDetail() != null && roomDetails.getMaxGuest() != null && roomDetails.getMaxGuest() > 0) {
                                    polyglotSleepsText = roomDetails.getMaxGuest() == 1 ? polyglotService.getTranslatedData(SPACE_SINGLE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(SPACE_OCCUPANCY_TEXT);
                                    polyglotSleepsText = polyglotSleepsText.replace(OCCUPANCY_PARAMETER, String.valueOf(roomDetails.getMaxGuest()));
                                }

                                if (StringUtils.isNotEmpty(bedCountText)) {
                                    stayInfoText += HYPHEN_SPACE + bedCountText;
                                }

                                if (StringUtils.isNotEmpty(polyglotSleepsText)) {
                                    if (StringUtils.isNotEmpty(bedCountText)) {
                                        stayInfoText += COMMA_SPACE + polyglotSleepsText;
                                    } else {
                                        stayInfoText += HYPHEN_SPACE + polyglotSleepsText;
                                    }
                                }
                                stayInfo.setInfoText(stayInfoText);
                                stayInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bedroomStayInfoIcon : stayInfoIcon);
                            }
                            if (stayInfo != null) {
                                stayInfoList.add(stayInfo);
                            }
                        } else {
                            String bedNamesText= "";
                            if(!PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelDetails.getPropertyType())) {
                                bedNamesText = getBedNamesFromExternalVendor(roomInfos);
                            }
                            String polyglotSleepsText = "";

                            if (roomDetails.getStayDetail() != null && roomDetails.getMaxGuest() != null && roomDetails.getMaxGuest() > 0) {
                                polyglotSleepsText = roomDetails.getMaxGuest() == 1 ? polyglotService.getTranslatedData(SPACE_SINGLE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(SPACE_OCCUPANCY_TEXT);
                                polyglotSleepsText = polyglotSleepsText.replace(OCCUPANCY_PARAMETER, String.valueOf(roomDetails.getMaxGuest()));
                            }
                            StayInfo stayInfo = new StayInfo();
                            String stayInfoText = "";
                            if (StringUtils.isNotEmpty(bedNamesText)) {
                                stayInfoText += bedNamesText;
                            }
                            if (StringUtils.isNotEmpty(polyglotSleepsText)) {
                                if (StringUtils.isNotEmpty(bedNamesText)) {
                                    stayInfoText += COMMA_SPACE + polyglotSleepsText;
                                } else {
                                    stayInfoText += polyglotSleepsText;
                                }
                            }
                            if (StringUtils.isNotEmpty(stayInfoText)) {
                                stayInfo.setInfoText(stayInfoText);
                                stayInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bedroomStayInfoIcon : stayInfoIcon);
                            }
                            stayInfoList.add(stayInfo);
                        }
                    }
                    int bathroomCount = 0;
                    if (roomDetails.getBathroomCount() != null && roomDetails.getBathroomCount() > 0) {
                        bathroomCount = roomDetails.getBathroomCount();
                    } else {
                        bathroomCount = getBathroomCountFromRoomInfo(roomInfos);
                    }
                    if (bathroomCount > 0 && !PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelDetails.getPropertyType())) {
                        StayInfo bathRoomInfo = new StayInfo();
                        String stayInfoText = "";
                        if (bathroomCount == 1) {
                            stayInfoText = bathroomCount + SPACE + Constants.BATHROOM;
                        } else {
                            stayInfoText = bathroomCount + SPACE + Constants.BATHROOMS;
                        }
                        bathRoomInfo.setInfoText(stayInfoText);
                        bathRoomInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bathroomInfoIcon : stayInfoIcon);
                        stayInfoList.add(bathRoomInfo);
                    }
                    addKitchenRoomInfoOrLivingRoomInfo(roomInfos, roomDetails, client, stayInfoList);
                    if (Constants.PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelDetails.getPropertyType()) && hotelDetails.getAdditionalDetails() != null &&
                            hotelDetails.getAdditionalDetails().getSellableCombo() == 1) {
                        StayInfo stayInfo = new StayInfo();
                        stayInfo.setInfoText(polyglotService.getTranslatedData(HOSTEL_ROOMS_AVAILABLE_TEXT));
                        stayInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bedroomStayInfoIcon : stayInfoIcon);
                        stayInfoList.add(stayInfo);
                    }

                }

            } else if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos())) {
                //Homogenous Combo (same type of rooms size == 1)
                if (searchRoomsResponse.getRecommendedCombos().get(0).getRooms().size() == 1) {
                    RoomDetails roomDetails = searchRoomsResponse.getRecommendedCombos().get(0).getRooms().get(0);
                    RoomInfo roomInfos = CollectionUtils.isNotEmpty(hotelDetails.getRoomCombos()) && CollectionUtils.isNotEmpty(hotelDetails.getRoomCombos().get(0).getRooms()) ?
                            hotelDetails.getRoomCombos().get(0).getRooms().get(0).getRoomInfo() : null;
                    StayDetail stayDetail = roomDetails != null ? roomDetails.getStayDetail() : null;
                    if (stayDetail != null) {
                        if (isRequestFromApps) {
                            if (roomDetails.getBedroomCount() != null && roomDetails.getBedroomCount() > 0 && !PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelDetails.getPropertyType())) {
                                StayInfo stayInfo = null;
                                if (roomDetails.getBedroomCount() == 1) {
                                    stayInfo = new StayInfo();
                                    String stayInfoText = "";
                                    String polyglotSleepsText = "";
                                    stayInfoText = "1" + SPACE + polyglotService.getTranslatedData(SINGLE_BEDROOM_TITLE);
                                    String bedNamesText = getBedNamesFromExternalVendor(roomInfos);
                                    if (roomDetails.getStayDetail() != null && roomDetails.getMaxGuest() != null && roomDetails.getMaxGuest() > 0) {
                                        polyglotSleepsText = roomDetails.getMaxGuest() == 1 ? polyglotService.getTranslatedData(SPACE_SINGLE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(SPACE_OCCUPANCY_TEXT);
                                        polyglotSleepsText = polyglotSleepsText.replace(OCCUPANCY_PARAMETER, String.valueOf(roomDetails.getMaxGuest()));
                                    }
                                    if (StringUtils.isNotEmpty(bedNamesText)) {
                                        stayInfoText += HYPHEN_SPACE + bedNamesText;
                                    }

                                    if (StringUtils.isNotEmpty(polyglotSleepsText)) {
                                        if (StringUtils.isNotEmpty(bedNamesText)) {
                                            stayInfoText += COMMA_SPACE + polyglotSleepsText;
                                        } else {
                                            stayInfoText += HYPHEN_SPACE + polyglotSleepsText;
                                        }
                                    }
                                    stayInfo.setInfoText(stayInfoText);
                                    stayInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bedroomStayInfoIcon : stayInfoIcon);
                                } else if (roomDetails.getBedroomCount() > 1) {
                                    stayInfo = new StayInfo();
                                    String stayInfoText = "";
                                    String polyglotSleepsText = "";
                                    stayInfoText = roomDetails.getBedroomCount() + SPACE + polyglotService.getTranslatedData(BEDROOM_TITLE);
                                    String bedCountText = getBedCountInfoFromRoomInfo(roomInfos);
                                    if (roomDetails.getStayDetail() != null && roomDetails.getMaxGuest() != null && roomDetails.getMaxGuest() > 0) {
                                        polyglotSleepsText = roomDetails.getMaxGuest() == 1 ? polyglotService.getTranslatedData(SPACE_SINGLE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(SPACE_OCCUPANCY_TEXT);
                                        polyglotSleepsText = polyglotSleepsText.replace(OCCUPANCY_PARAMETER, String.valueOf(roomDetails.getMaxGuest()));
                                    }
                                    if (StringUtils.isNotEmpty(bedCountText)) {
                                        stayInfoText += HYPHEN_SPACE + bedCountText;
                                    }
                                    if (StringUtils.isNotEmpty(polyglotSleepsText)) {
                                        if (StringUtils.isNotEmpty(bedCountText)) {
                                            stayInfoText += COMMA_SPACE + polyglotSleepsText;
                                        } else {
                                            stayInfoText += HYPHEN_SPACE + polyglotSleepsText;
                                        }
                                    }
                                    stayInfo.setInfoText(stayInfoText);
                                    stayInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bedroomStayInfoIcon : stayInfoIcon);
                                }
                                if (stayInfo != null) {
                                    stayInfoList.add(stayInfo);
                                }
                            } else {
                                String bedNamesText= "";
                                if(!PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelDetails.getPropertyType())) {
                                    bedNamesText = getBedNamesFromExternalVendor(roomInfos);
                                }
                                String polyglotSleepsText = "";

                                if (roomDetails.getStayDetail() != null && roomDetails.getMaxGuest() != null && roomDetails.getMaxGuest() > 0) {
                                    polyglotSleepsText = roomDetails.getMaxGuest() == 1 ? polyglotService.getTranslatedData(SPACE_SINGLE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(SPACE_OCCUPANCY_TEXT);
                                    polyglotSleepsText = polyglotSleepsText.replace(OCCUPANCY_PARAMETER, String.valueOf(roomDetails.getMaxGuest()));
                                }
                                StayInfo stayInfo = new StayInfo();
                                String stayInfoText = "";
                                if (StringUtils.isNotEmpty(bedNamesText)) {
                                    stayInfoText += bedNamesText;
                                }
                                if (StringUtils.isNotEmpty(polyglotSleepsText)) {
                                    if (StringUtils.isNotEmpty(bedNamesText)) {
                                        stayInfoText += COMMA_SPACE + polyglotSleepsText;
                                    } else {
                                        stayInfoText += polyglotSleepsText;
                                    }
                                }
                                if (StringUtils.isNotEmpty(stayInfoText)) {
                                    stayInfo.setInfoText(stayInfoText);
                                    stayInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bedroomStayInfoIcon : stayInfoIcon);
                                }
                                stayInfoList.add(stayInfo);
                            }
                        }
                        int bathroomCount = 0;
                        if (roomDetails.getBathroomCount() != null && roomDetails.getBathroomCount() > 0) {
                            bathroomCount = roomDetails.getBathroomCount();
                        } else {
                            bathroomCount = getBathroomCountFromRoomInfo(roomInfos);
                        }
                        if (bathroomCount > 0 && !PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelDetails.getPropertyType())) {
                            StayInfo bathRoomInfo = new StayInfo();
                            String stayInfoText = "";
                            if (bathroomCount == 1) {
                                stayInfoText = bathroomCount + SPACE + Constants.BATHROOM;
                            } else {
                                stayInfoText = bathroomCount + SPACE + Constants.BATHROOMS;
                            }
                            bathRoomInfo.setInfoText(stayInfoText);
                            bathRoomInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bathroomInfoIcon : stayInfoIcon);
                            stayInfoList.add(bathRoomInfo);
                        }
                        addKitchenRoomInfoOrLivingRoomInfo(roomInfos, roomDetails, client, stayInfoList);
                        if (Constants.PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelDetails.getPropertyType()) && hotelDetails.getAdditionalDetails() != null && hotelDetails.getAdditionalDetails().getSellableCombo() == 1) {
                            StayInfo stayInfo = new StayInfo();
                            stayInfo.setInfoText(polyglotService.getTranslatedData(HOSTEL_ROOMS_AVAILABLE_TEXT));
                            stayInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bedroomStayInfoIcon : stayInfoIcon);
                            stayInfoList.add(stayInfo);

                        }
                    }
                }
                //Heterogeneous Combo (different type of rooms size > 1)
                else {
                    stayInfoList = new ArrayList<>();
                    if (!Constants.CLIENT_DESKTOP.equalsIgnoreCase(client)) {
                        for (RoomDetails roomDetails : searchRoomsResponse.getRecommendedCombos().get(0).getRooms()) {
                            StayInfo stayInfo = new StayInfo();
                            Integer roomCount = getPropertyCountFromRoomTariff(roomDetails);
                            stayInfo.setInfoText(roomCount != null && roomCount > 1 ? roomCount + SPACE_X_SPACE + roomDetails.getRoomName() : roomDetails.getRoomName());
                            stayInfo.setIconUrl(stayInfoIcon);
                            stayInfoList.add(stayInfo);
                        }
                    }

                }
            }

            return stayInfoList;
        }
        return null;
    }

    private void addKitchenRoomInfoOrLivingRoomInfo(RoomInfo roomInfo, RoomDetails roomDetails, String client, List<StayInfo> stayInfoList) {
        boolean isKitchenPresent = checkIfKitchenPresentAmenities(roomInfo);
        String	livingRoomTitle = polyglotService.getTranslatedData(LIVING_ROOM_TITLE);
        if (isKitchenPresent) {
            String	kitchenRoomTitle = polyglotService.getTranslatedData(KITCHENETTE_TITLE);
            StayInfo kitchenInfo = new StayInfo();
            kitchenInfo.setInfoText(kitchenRoomTitle);
            kitchenInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? kitchenStayInfoIcon : stayInfoIcon);
            stayInfoList.add(kitchenInfo);
        }
        if (roomInfo != null && roomInfo.getRoomFlags() != null && roomInfo.getRoomFlags().isLivingRoomPresent()) {
            StayInfo livingRoom = new StayInfo();
            livingRoom.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? livingroomStayInfoIcon : stayInfoIcon);
            livingRoom.setInfoText(livingRoomTitle);
            stayInfoList.add(livingRoom);
        }
    }

    private boolean checkIfKitchenPresentAmenities(RoomInfo roomInfo) {
        if (roomInfo != null && CollectionUtils.isNotEmpty(roomInfo.getAmenities())) {
            for (AmenityGroup facilityWithGrp : roomInfo.getAmenities()) {
                if (facilityWithGrp != null && StringUtils.isNotEmpty(facilityWithGrp.getName()) && facilityWithGrp.getName().toLowerCase().contains("kitchen")) {
                    return true;
                }
            }
        }
        return false;
    }

    private int getBathroomCountFromRoomInfo(RoomInfo roomInfos) {
        if (roomInfos != null && MapUtils.isNotEmpty(roomInfos.getRoomArrangementMap()) && CollectionUtils.isNotEmpty(roomInfos.getRoomArrangementMap().get(BATHROOM))) {
            for (ArrangementInfo sleepingArrangement : roomInfos.getRoomArrangementMap().get(BATHROOM)) {
                return sleepingArrangement.getCount();
            }
        }
       return 0;
    }

    private String getBedCountInfoFromRoomInfo(RoomInfo roomInfos) {
        String bedCountText = "";
        if (roomInfos != null && MapUtils.isNotEmpty(roomInfos.getRoomArrangementMap()) && CollectionUtils.isNotEmpty(roomInfos.getRoomArrangementMap().get(BEDS))) {
            int bedCount = 0;
            for (ArrangementInfo sleepingArrangement : roomInfos.getRoomArrangementMap().get(BEDS)) {
                bedCount += sleepingArrangement.getCount();
            }
            if (bedCount > 0) {
                bedCountText = bedCount + SPACE + (bedCount > 1 ? BEDS.toLowerCase() : BED.toLowerCase());
            }

        }
        return bedCountText;
    }


    private String getBedNamesFromExternalVendor(RoomInfo roomInfos) {
        String bedNamesText = "";
        if (roomInfos != null && MapUtils.isNotEmpty(roomInfos.getRoomArrangementMap()) && roomInfos.getRoomArrangementMap().containsKey("BEDROOM_INFO")) {
            List<ArrangementInfo> externalVendorBedRoomInfoList = roomInfos.getRoomArrangementMap().get("BEDROOM_INFO");
            if (CollectionUtils.isNotEmpty(externalVendorBedRoomInfoList)) {
                for (ArrangementInfo externalVendorBedRoomInfo : externalVendorBedRoomInfoList) {
                    if (externalVendorBedRoomInfo != null && StringUtils.isNotEmpty(externalVendorBedRoomInfo.getDescription()) &&
                            externalVendorBedRoomInfo.getDescription().toLowerCase().contains("bed")) {
                        if (StringUtils.isNotBlank(bedNamesText)) {
                            bedNamesText += COMMA_SPACE;
                        }
                        bedNamesText += externalVendorBedRoomInfo.getDescription();
                    }
                }
            }
        }
        return bedNamesText;
    }

    private Integer getPropertyCountFromRoomTariff(RoomDetails roomDetails) {
        Integer roomCount = 0;
        if (roomDetails != null && CollectionUtils.isNotEmpty(roomDetails.getRatePlans()) && CollectionUtils.isNotEmpty(roomDetails.getRatePlans().get(0).getTariffs())) {
            Tariff tariff = roomDetails.getRatePlans().get(0).getTariffs().get(0);
            if (tariff.getOccupancydetails() != null && tariff.getOccupancydetails().getRoomCount() != null) {
                roomCount = tariff.getOccupancydetails().getRoomCount();
            }
        }
        return roomCount;
    }

    /**
     * Build stay type info based on sellable unit and property type
     */
    public StayTypeInfo buildStayTypeInfo(String propertySellableUnit, String propertyType, String title, boolean serviceApartment) {
        StayTypeInfo stayTypeInfo = null;
        
        if (MapUtils.isEmpty(actionInfoMap) || StringUtils.isEmpty(propertySellableUnit)) {
            return stayTypeInfo;
        }
        
        if (SELLABLE_UNIT_ENTIRE.equalsIgnoreCase(propertySellableUnit)) {
            if (StringUtils.isNotEmpty(propertyType) && PROPERTY_TYPE_APARTMENT.contains(propertyType.toLowerCase()) && serviceApartment) {
                StayTypeInfo configStayTypeInfo = actionInfoMap.get(APARTMENT_SELLABLE_UNIT_ENTIRE_CONSUL_KEY);
                if (configStayTypeInfo != null) {
                    stayTypeInfo = configStayTypeInfo;
                }
            } else {
                StayTypeInfo configStayTypeInfo = actionInfoMap.get(SELLABLE_UNIT_ENTIRE_CONSUL_KEY);
                if (configStayTypeInfo != null) {
                    stayTypeInfo = configStayTypeInfo;
                    stayTypeInfo.setTitle(MessageFormat.format(PROPERTY_STAY_TYPE_TITLE_ENTIRE_V1, propertyType));
                }
            }
        } else if (SELLABLE_UNIT_ROOM.equalsIgnoreCase(propertySellableUnit) && !PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(propertyType)) {
            if (StringUtils.isNotEmpty(propertyType) && PROPERTY_TYPE_APARTMENT.contains(propertyType.toLowerCase()) && serviceApartment) {
                StayTypeInfo configStayTypeInfo = actionInfoMap.get(APARTMENT_SELLABLE_UNIT_ROOM_CONSUL_KEY);
                if (configStayTypeInfo != null) {
                    stayTypeInfo = configStayTypeInfo;
                }
            } else {
                StayTypeInfo configStayTypeInfo = actionInfoMap.get(SELLABLE_UNIT_ROOM_CONSUL_KEY);
                if (configStayTypeInfo != null) {
                    stayTypeInfo = configStayTypeInfo;
                    stayTypeInfo.setTitle(MessageFormat.format(PROPERTY_STAY_TYPE_TITLE_ROOM_V1, utility.buildStringTypeWithVowel(propertyType)));
                }
            }
        }
        return stayTypeInfo;
    }

    private void updateSearchRoomsResponseFilters(SearchRoomsResponse searchRoomsResponse, Pair<Boolean, Boolean> bedAndRoomPresent) {
        if (bedAndRoomPresent.getKey() && bedAndRoomPresent.getValue()) {
            // It means both sellableType "bed" & "room" is Present then create filter for private rooms only
            RatePlanFilter privateRooms = new RatePlanFilter(polyglotService.getTranslatedData(ROOMS_SELLABLE_LABEL), ROOMS_SELLABLE_TYPE_FILTER_CODE, null, false);
            if (CollectionUtils.isEmpty(searchRoomsResponse.getFilters())) {
                searchRoomsResponse.setFilters(new ArrayList<>());
            }
            searchRoomsResponse.getFilters().add(0, privateRooms);
        }
    }

    /**
     * Initialize actionInfoMap from configuration
     */
    public void initializeActionInfoMap(Map<String, StayTypeInfo> configActionInfoMap) {
        if (configActionInfoMap != null) {
            this.actionInfoMap = configActionInfoMap;
        }
    }

    public RoomSummary buildRoomSummary(RoomInfoExtension roomInfoExtension) {
        RoomSummary roomSummary = null;
        if (roomInfoExtension != null && roomInfoExtension.getRoomSummary() != null) {
            roomSummary = new RoomSummary();
            roomSummary.setTopRated(roomInfoExtension.getRoomSummary().isTopRated());
            roomSummary.setRatingCount(roomInfoExtension.getRoomSummary().getRatingCount());
            roomSummary.setReviewCount(roomInfoExtension.getRoomSummary().getReviewCount());
            roomSummary.setDisableLowRating(roomInfoExtension.getRoomSummary().isDisableLowRating());
            roomSummary.setTagData(buildTagData(roomInfoExtension.getRoomSummary().getTagData()));
        }
        return roomSummary;
    }

    private List<TagData> buildTagData(List<Tag> tagData) {
        if (CollectionUtils.isNotEmpty(tagData)) {
            List<TagData> tagDataList = new ArrayList<>();
            tagData.forEach(tag -> tagDataList.add(convertTag(tag)));
            return tagDataList;
        }
        return null;
    }

    private TagData convertTag(Tag tag) {
        TagData data = new TagData();
        data.setName(tag.getName());
        data.setId(tag.getId());
        data.setTagType(tag.getTagType());
        data.setSentiment(tag.getSentiment());
        return data;
    }
}