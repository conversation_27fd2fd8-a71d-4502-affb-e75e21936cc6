package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.rooms.LoginPersuasion;
import com.mmt.hotels.clientgateway.response.rooms.RecommendedCombo;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static com.mmt.hotels.clientgateway.constants.Constants.SPACE_X_SPACE;

@Component
public class OrchSearchRoomsResponseTransformerPWA extends OrchSearchRoomsResponseTransformer {
    private static final Logger LOGGER = LoggerFactory.getLogger(OrchSearchRoomsResponseTransformerPWA.class);

    @Override
    protected LoginPersuasion buildLoginPersuasion() {
        return null;
    }

    @Override
    public PersuasionObject createTopRatedPersuasion(boolean isNewDetailsPageDesktop) {
        // TODO: Add to Peitho
        return null;
    }

    @Override
    public PersuasionResponse buildDelayedConfirmationPersuasion(String corpAlias, boolean isMyBizNewDetailsPage) {
        return null;
    }

    @Override
    public PersuasionResponse buildSpecialFareTagPersuasion(String corpAlias) {
        return null;
    }

    @Override
    public PersuasionResponse buildSpecialFareTagWithInfoPersuasion(String corpAlias, boolean isNewSelectRoomPage) {
        return null;
    }

    @Override
    public PersuasionResponse buildConfirmationTextPersuasion(String corpAlias, boolean isNewSelectRoomPage, boolean isMyBizNewDetailsPage) {
        return null;
    }

    public String getHtml() {
        return Constants.DT_INCLUSION_HTML;
    }

    protected void buildGroupBookingComboText(RoomDetails roomDetails, RecommendedCombo recommendedCombo,
                                              boolean baseCombo, OccupancyDetails occupancyDetails) {

        if (baseCombo && recommendedCombo != null && roomDetails.getBaseRoom() != null &&
                roomDetails.getBaseRoom() && occupancyDetails != null) {
            int roomCount = occupancyDetails.getNumberOfRooms();
            String comboDisplayText = roomCount + SPACE_X_SPACE + roomDetails.getRoomName();
            roomDetails.setDisplayName(comboDisplayText);
            recommendedCombo.setBaseComboText(comboDisplayText);
        }
    }
}
