package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.helpers.FilterHelper;
import com.mmt.hotels.clientgateway.request.CityGuideRequest;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.wishlist.WishListedHotelsDetailCriteria;
import com.mmt.hotels.clientgateway.request.wishlist.WishListedHotelsDetailRequest;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.request.TrafficSource;
import com.mmt.hotels.model.request.*;
import com.mmt.hotels.model.request.flyfish.FlyFishSummaryRequest;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.request.flyfish.SubConceptFilterDTO;
import com.mmt.hotels.model.request.flyfish.SummaryFilterCriteriaDTO;
import com.mmt.hotels.model.response.flyfish.UgcSummaryRequest;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.request.detail.mob.RequiredApis;
import com.mmt.hotels.pojo.request.wishlist.FlyfishReviewRequestBody;
import com.mmt.hotels.pojo.request.wishlist.HotStoreHotelsRequestBody;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.BEDROOM_COUNT;
import static com.mmt.hotels.clientgateway.constants.Constants.EXACT_ROOM_RECOMMENDATION;

public abstract class StaticDetailRequestTransformer {

	@Autowired
	Utility utility;

	@Autowired
	FilterHelper filterHelper;

	public HotelDetailsMobRequestBody convertStaticDetailRequest(StaticDetailRequest staticDetailRequest, CommonModifierResponse commonModifierResponse) {
		HotelDetailsMobRequestBody hotelDetailsMobRequestBody = new HotelDetailsMobRequestBody();

		hotelDetailsMobRequestBody.setUuids(staticDetailRequest.getUuids());
		hotelDetailsMobRequestBody.setCorrelationKey(staticDetailRequest.getCorrelationKey());
		buildDeviceDetails(hotelDetailsMobRequestBody, staticDetailRequest.getDeviceDetails());
        buildSearchCriteria(hotelDetailsMobRequestBody, staticDetailRequest.getSearchCriteria());
		RequestDetails requestDetails = staticDetailRequest.getRequestDetails();
		buildRequestDetails(hotelDetailsMobRequestBody, requestDetails);
        hotelDetailsMobRequestBody.setAppliedFilterMap(buildAppliedFilterMap(staticDetailRequest.getFilterCriteria()));

        buildImageDetails(hotelDetailsMobRequestBody, staticDetailRequest.getImageDetails());
        hotelDetailsMobRequestBody.setResponseFilterFlags(buildResponseFilterFlags(hotelDetailsMobRequestBody, staticDetailRequest.getFeatureFlags(), commonModifierResponse.isCityTaxExclusive()));
        hotelDetailsMobRequestBody.setExperimentData(staticDetailRequest.getExpData());
        hotelDetailsMobRequestBody.setFlyfishSummaryRequest(buildReviewDetails(staticDetailRequest.getReviewDetails(), staticDetailRequest.getSearchCriteria().getCountryCode()));
        hotelDetailsMobRequestBody.setRequiredApis(getRequiredApis(staticDetailRequest.getRequiredApis()));

        if(commonModifierResponse.getExtendedUser() != null){
			hotelDetailsMobRequestBody.setUuid(commonModifierResponse.getExtendedUser().getUuid());
			hotelDetailsMobRequestBody.setProfileType(commonModifierResponse.getExtendedUser().getProfileType());
			hotelDetailsMobRequestBody.setSubProfileType(commonModifierResponse.getExtendedUser().getAffiliateId());
			hotelDetailsMobRequestBody.setAgencyUUID(Utility.fetchAgencyUUIDFromCorp(commonModifierResponse.getExtendedUser().getCorporateData()));
        }
		hotelDetailsMobRequestBody.setSiteDomain(requestDetails.getSiteDomain());
		hotelDetailsMobRequestBody.setAffiliateId(commonModifierResponse.getAffiliateId());
		hotelDetailsMobRequestBody.setApplicationId(commonModifierResponse.getApplicationId());
		hotelDetailsMobRequestBody.setDomain(Constants.B2C);
		if(commonModifierResponse.getHydraResponse() != null){
			hotelDetailsMobRequestBody.setHydraSegments(commonModifierResponse.getHydraResponse().getHydraMatchedSegment());
		}
		hotelDetailsMobRequestBody.setSeoCohort(staticDetailRequest.getFeatureFlags().getSeoCohort());
		hotelDetailsMobRequestBody.setMobile(commonModifierResponse.getMobile());
		hotelDetailsMobRequestBody.setMcid(commonModifierResponse.getMcId());
		hotelDetailsMobRequestBody.setDetailedAmenities(true);
		hotelDetailsMobRequestBody.setPageContext("DETAIL");
		hotelDetailsMobRequestBody.setRequestType("B2CAgent");
		hotelDetailsMobRequestBody.setGuestRecommendEnabled(buildGuestRecommendation());
		hotelDetailsMobRequestBody.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());
		if (requestDetails.getRequisitionID() != null && requestDetails.getMyBizFlowIdentifier() != null) {
			hotelDetailsMobRequestBody.setRequisitionID(requestDetails.getRequisitionID());
			hotelDetailsMobRequestBody.setMyBizFlowIdentifier(requestDetails.getMyBizFlowIdentifier());
		}
		hotelDetailsMobRequestBody.setBusinessIdentificationEnableFromUserService(utility.isBusinessIdentificationEnableFromUserService(commonModifierResponse.getExtendedUser()));
		hotelDetailsMobRequestBody.setUserLocation(commonModifierResponse.getUserLocation());
		if (staticDetailRequest.getSearchCriteria() != null && staticDetailRequest.getSearchCriteria().getMultiCurrencyInfo() != null) {
			hotelDetailsMobRequestBody.setMultiCurrencyInfo(utility.buildMultiCurrencyInfoRequest(staticDetailRequest.getSearchCriteria().getMultiCurrencyInfo()));
		}
		if (staticDetailRequest.getSearchCriteria().getUserGlobalInfo() != null) {
			hotelDetailsMobRequestBody.setUserGlobalInfo(utility.buildUserGlobalInfoHES(staticDetailRequest.getSearchCriteria().getUserGlobalInfo()));
		}
        return hotelDetailsMobRequestBody;
	}

	public HotStoreHotelsRequestBody getHotStoreHotelsRequest(WishListedHotelsDetailRequest wishListedHotelsDetailRequest, CommonModifierResponse commonModifierResponse) {
		HotStoreHotelsRequestBody hotStoreHotelsRequestBody = new HotStoreHotelsRequestBody();
		hotStoreHotelsRequestBody.setCorrelationKey(wishListedHotelsDetailRequest.getCorrelationKey());
		buildDeviceDetails(hotStoreHotelsRequestBody, wishListedHotelsDetailRequest.getDeviceDetails());
		buildSearchCriteria(hotStoreHotelsRequestBody, wishListedHotelsDetailRequest.getSearchCriteria());
		buildImageDetails(hotStoreHotelsRequestBody, wishListedHotelsDetailRequest.getImageDetails());
		if (commonModifierResponse.getExtendedUser() != null) {
			hotStoreHotelsRequestBody.setUuid(commonModifierResponse.getExtendedUser().getUuid());
			hotStoreHotelsRequestBody.setProfileType(commonModifierResponse.getExtendedUser().getProfileType());
			hotStoreHotelsRequestBody.setSubProfileType(commonModifierResponse.getExtendedUser().getAffiliateId());
		}
		return hotStoreHotelsRequestBody;
	}

	private void buildDeviceDetails(HotStoreHotelsRequestBody hotStoreHotelsRequestBody, DeviceDetails deviceDetails) {
		if (deviceDetails != null) {
			hotStoreHotelsRequestBody.setAppVersion(deviceDetails.getAppVersion());
			hotStoreHotelsRequestBody.setBookingDevice(deviceDetails.getBookingDevice());
			hotStoreHotelsRequestBody.setDeviceId(deviceDetails.getDeviceId());
			hotStoreHotelsRequestBody.setDeviceType(deviceDetails.getDeviceType());
			hotStoreHotelsRequestBody.setNetworkType(deviceDetails.getNetworkType());
		}
	}

	private void buildSearchCriteria(HotStoreHotelsRequestBody hotStoreHotelsRequestBody, WishListedHotelsDetailCriteria wishListedHotelsDetailCriteria) {
		if (wishListedHotelsDetailCriteria != null) {
			if (CollectionUtils.isNotEmpty(wishListedHotelsDetailCriteria.getHotelIds())) {
				hotStoreHotelsRequestBody.setHotelIds(wishListedHotelsDetailCriteria.getHotelIds().toString());
			}
			if (StringUtils.isNotBlank(wishListedHotelsDetailCriteria.getLocationId()) && StringUtils.isNotBlank(wishListedHotelsDetailCriteria.getLocationType())) {
				hotStoreHotelsRequestBody.setCityId(wishListedHotelsDetailCriteria.getLocationId());
				hotStoreHotelsRequestBody.setLocationId(wishListedHotelsDetailCriteria.getLocationId());
				hotStoreHotelsRequestBody.setLocationType(wishListedHotelsDetailCriteria.getLocationType());
			} else if (StringUtils.isNotBlank(wishListedHotelsDetailCriteria.getCityCode())) {
				hotStoreHotelsRequestBody.setCityId(wishListedHotelsDetailCriteria.getCityCode());
			}
		}
	}

	private void buildImageDetails(HotStoreHotelsRequestBody hotStoreHotelsRequestBody, ImageDetails imageDetails) {
		if (imageDetails == null)
			return;
		hotStoreHotelsRequestBody.setImageType(imageDetails.getTypes());
		hotStoreHotelsRequestBody.setImageCategory(buildImageCategory(imageDetails.getCategories()));
	}

	public FlyfishReviewRequestBody getFlyfishReviewRequest(WishListedHotelsDetailRequest wishListedHotelsDetailRequest, CommonModifierResponse commonModifierResponse) {
		FlyfishReviewRequestBody flyfishReviewRequestBody = new FlyfishReviewRequestBody();
		flyfishReviewRequestBody.setCorrelationKey(wishListedHotelsDetailRequest.getCorrelationKey());
		buildDeviceDetails(flyfishReviewRequestBody, wishListedHotelsDetailRequest.getDeviceDetails());
		buildSearchCriteria(flyfishReviewRequestBody, wishListedHotelsDetailRequest.getSearchCriteria());
		buildImageDetails(flyfishReviewRequestBody, wishListedHotelsDetailRequest.getImageDetails());
		flyfishReviewRequestBody.setFlyfishSummaryRequest(buildReviewDetails(wishListedHotelsDetailRequest.getReviewDetails(), wishListedHotelsDetailRequest.getSearchCriteria().getCountryCode()));
		if (commonModifierResponse.getExtendedUser() != null) {
			flyfishReviewRequestBody.setUuid(commonModifierResponse.getExtendedUser().getUuid());
			flyfishReviewRequestBody.setProfileType(commonModifierResponse.getExtendedUser().getProfileType());
			flyfishReviewRequestBody.setSubProfileType(commonModifierResponse.getExtendedUser().getAffiliateId());
		}
		return flyfishReviewRequestBody;
	}

	public UgcSummaryRequest getUgcSummaryRequest(WishListedHotelsDetailRequest wishListedHotelsDetailRequest, CommonModifierResponse commonModifierResponse) {
		UgcSummaryRequest ugcSummaryRequest = new UgcSummaryRequest();
		ugcSummaryRequest.setHotelIds(wishListedHotelsDetailRequest.getSearchCriteria().getHotelIds());
		return ugcSummaryRequest;
	}

	private void buildDeviceDetails(FlyfishReviewRequestBody flyfishReviewRequestBody, DeviceDetails deviceDetails) {
		if (deviceDetails != null) {
			flyfishReviewRequestBody.setAppVersion(deviceDetails.getAppVersion());
			flyfishReviewRequestBody.setBookingDevice(deviceDetails.getBookingDevice());
			flyfishReviewRequestBody.setDeviceId(deviceDetails.getDeviceId());
			flyfishReviewRequestBody.setDeviceType(deviceDetails.getDeviceType());
			flyfishReviewRequestBody.setNetworkType(deviceDetails.getNetworkType());
		}
	}

	private void buildSearchCriteria(FlyfishReviewRequestBody flyfishReviewRequestBody, WishListedHotelsDetailCriteria wishListedHotelsDetailCriteria) {
		if (wishListedHotelsDetailCriteria != null) {
			if (CollectionUtils.isNotEmpty(wishListedHotelsDetailCriteria.getHotelIds())) {
				flyfishReviewRequestBody.setHotelIds(wishListedHotelsDetailCriteria.getHotelIds().toString());
			}
			if (StringUtils.isNotBlank(wishListedHotelsDetailCriteria.getLocationId()) && StringUtils.isNotBlank(wishListedHotelsDetailCriteria.getLocationType())) {
				flyfishReviewRequestBody.setCityCode(wishListedHotelsDetailCriteria.getLocationId());
				flyfishReviewRequestBody.setLocationId(wishListedHotelsDetailCriteria.getLocationId());
				flyfishReviewRequestBody.setLocationType(wishListedHotelsDetailCriteria.getLocationType());
			} else if (StringUtils.isNotBlank(wishListedHotelsDetailCriteria.getCityCode())) {
				flyfishReviewRequestBody.setCityCode(wishListedHotelsDetailCriteria.getCityCode());
			}
			flyfishReviewRequestBody.setRoomStayCandidates(buildRoomStayCandidates(wishListedHotelsDetailCriteria.getRoomStayCandidates()));
		}
	}

	private void buildImageDetails(FlyfishReviewRequestBody flyfishReviewRequestBody, ImageDetails imageDetails) {
		if (imageDetails == null)
			return;
		flyfishReviewRequestBody.setImageType(imageDetails.getTypes());
		flyfishReviewRequestBody.setImageCategory(buildImageCategory(imageDetails.getCategories()));
	}

    private GuestRecommendationEnabledReqBody buildGuestRecommendation() {
        GuestRecommendationEnabledReqBody guestRecommendationEnabledReqBody = new GuestRecommendationEnabledReqBody();
        guestRecommendationEnabledReqBody.setMaxRecommendations("1");
        guestRecommendationEnabledReqBody.setText("true");
        return guestRecommendationEnabledReqBody;
	}

	private RequiredApis getRequiredApis(com.mmt.hotels.clientgateway.request.RequiredApis requiredApis) {
		if (requiredApis == null)
			return null;
		RequiredApis requiredAPIsCB = new RequiredApis();
		requiredAPIsCB.setComparatorRequired(requiredApis.isComparatorRequired() || requiredApis.isComparatorV2Required());
		requiredAPIsCB.setFlyfishSummaryRequired(requiredApis.isReviewSummaryRequired());
		requiredAPIsCB.setLuxuryRequired(requiredApis.isLuxuryRequired());
		requiredAPIsCB.setPersuasionsRequired(requiredApis.isPersuasionsRequired());
		requiredAPIsCB.setPlacesRequired(requiredApis.isPlacesRequired());
		requiredAPIsCB.setRoomInfoRequired(requiredApis.isRoomInfoRequired());
		requiredAPIsCB.setTopReviewsRequired(requiredApis.isTopReviewsRequired());
		requiredAPIsCB.setWeaverResponseRequired(requiredApis.isWeaverResponseRequired());
		requiredAPIsCB.setDetailPersuasionCardsRequired(requiredApis.isDetailPersuasionCardsRequired());
		requiredAPIsCB.setMyPartnerHeroLoyalty(requiredApis.isMyPartnerHeroLoyalty());
		return requiredAPIsCB;
	}

	private FlyFishSummaryRequest buildReviewDetails(ReviewDetails reviewDetails, String countryCode) {
		if (reviewDetails == null)
			return null;
		FlyFishSummaryRequest flyfishSummaryRequest = new FlyFishSummaryRequest();
		SummaryFilterCriteriaDTO summaryFilterCriteriaDTO = new SummaryFilterCriteriaDTO();
		SubConceptFilterDTO subConceptFilterDTO = new SubConceptFilterDTO();
		List<OTA> otas = new ArrayList<>();
		for (OTA ota: OTA.values()) {
			if (reviewDetails.getOtas().contains(ota.name()))
				otas.add(ota);
		}

		if (!(Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode) || otas.contains(OTA.EXP))) {
			otas.add(OTA.EXP);
		}
		subConceptFilterDTO.setTagTypes(reviewDetails.getTagTypes());
		summaryFilterCriteriaDTO.setSubConcept(subConceptFilterDTO);
		summaryFilterCriteriaDTO.setOtas(otas);
		flyfishSummaryRequest.setFilter(summaryFilterCriteriaDTO);
		return flyfishSummaryRequest;
	}

	private void buildImageDetails(HotelDetailsMobRequestBody hotelDetailsMobRequestBody, ImageDetails imageDetails) {
		if (imageDetails == null)
    		return;
		hotelDetailsMobRequestBody.setImageType(imageDetails.getTypes());
		hotelDetailsMobRequestBody.setImageCategory(buildImageCategory(imageDetails.getCategories()));

	}

	private List<ImageCategoryEntityBO> buildImageCategory(List<ImageCategory> imageCategories) {

        List<ImageCategoryEntityBO> imageCategoryList = new ArrayList<>();

        for (ImageCategory imageCategory : imageCategories){
        	ImageCategoryEntityBO imageCategoryCB = new ImageCategoryEntityBO();
        	imageCategoryCB.setCategory(imageCategory.getType());
            imageCategoryCB.setCount(imageCategory.getCount());
            imageCategoryCB.setHeight(imageCategory.getHeight());
            imageCategoryCB.setWidth(imageCategory.getWidth());
            imageCategoryCB.setOutputFormat(imageCategory.getImageFormat());
            imageCategoryList.add(imageCategoryCB);
        }
        return imageCategoryList;
    }

	private Map<FilterGroup, Set<Filter>> buildAppliedFilterMap(
			List<com.mmt.hotels.clientgateway.request.Filter> filterCriteria) {
		if (CollectionUtils.isNotEmpty(filterCriteria)) {
            Map<FilterGroup, Set<Filter>> appliefFilterMapCB = new HashMap<>();
            for (com.mmt.hotels.clientgateway.request.Filter filterCG : filterCriteria) {
				if(filterCG.getFilterGroup() != null) {
					if (EXACT_ROOM_RECOMMENDATION.equalsIgnoreCase(filterCG.getFilterGroup().name()) || (BEDROOM_COUNT.equalsIgnoreCase(filterCG.getFilterGroup().name()))) {
						continue;
					}
				}
                com.mmt.hotels.filter.Filter filterCB = new com.mmt.hotels.filter.Filter();
                com.mmt.hotels.filter.FilterGroup filterGroup = com.mmt.hotels.filter.FilterGroup.
                		getFilterGroupFromFilterName(filterCG.getFilterGroup().name());
                filterCB.setFilterGroup(filterGroup);
                filterCB.setFilterRange(buildFilterRange(filterCG.getFilterRange()));
                filterCB.setFilterValue(filterCG.getFilterValue());
                filterCB.setRangeFilter(filterCG.isRangeFilter());

                if (appliefFilterMapCB.get(filterGroup) == null) {
                    Set<Filter> filterSet = new HashSet<>();
                    filterSet.add(filterCB);
                    appliefFilterMapCB.put(filterGroup, filterSet);

                } else {
                    Set<Filter> filterSet = appliefFilterMapCB.get(filterGroup);
                    filterSet.add(filterCB);
                    appliefFilterMapCB.put(filterGroup, filterSet);
                }
            }
			filterHelper.updateAppliedFilterMapDptCollections(appliefFilterMapCB, null, null);
            return appliefFilterMapCB;
        }
        return null;
	}

	 private com.mmt.hotels.filter.FilterRange buildFilterRange(FilterRange filterRange) {

		 if (filterRange == null)
			 return null;

		 com.mmt.hotels.filter.FilterRange filterRangeCB = new com.mmt.hotels.filter.FilterRange();
		 filterRangeCB.setMaxValue(filterRange.getMaxValue());
		 filterRangeCB.setMinValue(filterRange.getMinValue());

		 return filterRangeCB;
	 }

	private ResponseFilterFlags buildResponseFilterFlags(HotelDetailsMobRequestBody hotelDetailsMobRequestBody,
			FeatureFlags featureFlags, boolean isCityTaxExclusive) {
		ResponseFilterFlags responseFilterFlags = new ResponseFilterFlags();
		responseFilterFlags.setStaticData(featureFlags.isStaticData());
		responseFilterFlags.setFlyfishSummaryRequired(featureFlags.isReviewSummaryRequired());
		responseFilterFlags.setWalletRequired(featureFlags.isWalletRequired());
		responseFilterFlags.setStaticData(featureFlags.isStaticData());
		responseFilterFlags.setShortlistRequired(featureFlags.isShortlistingRequired());
		responseFilterFlags.setUnmodifiedAmenities(featureFlags.isUnmodifiedAmenities());
		responseFilterFlags.setPersuasionsEngineHit(featureFlags.isPersuasionsEngineHit());
		Optional.ofNullable(featureFlags.getMaskedPropertyName()).ifPresent(responseFilterFlags::setMaskedPropertyName);
		//hotelDetailsMobRequestBody.setNumberOfAddons(featureFlags.getNoOfAddons());
		//hotelDetailsMobRequestBody.setNumberOfCoupons(featureFlags.getNoOfCoupons());
		//hotelDetailsMobRequestBody.setNoOfPersuasions(featureFlags.getNoOfPersuasions());
		//hotelDetailsMobRequestBody.setNumberOfSoldOuts(featureFlags.getNoOfSoldouts());
		if(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(hotelDetailsMobRequestBody.getIdContext())) {
			responseFilterFlags.setNewCorp(true);
			responseFilterFlags.setPersuasionSeg(null);
			responseFilterFlags.setCityTaxExclusive(null);
		}else{
			responseFilterFlags.setCityTaxExclusive(isCityTaxExclusive);
		}
		responseFilterFlags.setPriceInfoReq(true);
		responseFilterFlags.setCheckAvailibility(true);
		responseFilterFlags.setBestCoupon(true);
		responseFilterFlags.setLocus(featureFlags.isLocus());
		responseFilterFlags.setLiteResponse(featureFlags.isLiteResponse());
		return responseFilterFlags;
	}

	private void buildDeviceDetails(HotelDetailsMobRequestBody hotelDetailsMobRequestBody, DeviceDetails deviceDetails) {
		hotelDetailsMobRequestBody.setAppVersion(deviceDetails.getAppVersion());
		hotelDetailsMobRequestBody.setBookingDevice(deviceDetails.getBookingDevice());
		hotelDetailsMobRequestBody.setDeviceId(deviceDetails.getDeviceId());
		hotelDetailsMobRequestBody.setDeviceType(deviceDetails.getDeviceType());
		hotelDetailsMobRequestBody.setNetworkType(deviceDetails.getNetworkType());
		hotelDetailsMobRequestBody.setDeviceName(deviceDetails.getDeviceName());
    }

    private void buildSearchCriteria(HotelDetailsMobRequestBody hotelDetailsMobRequestBody,
                                     StaticDetailCriteria staticDetailCriteria) {
    	hotelDetailsMobRequestBody.setHotelId(staticDetailCriteria.getHotelId());
    	hotelDetailsMobRequestBody.setCheckin(staticDetailCriteria.getCheckIn());
    	hotelDetailsMobRequestBody.setCheckout(staticDetailCriteria.getCheckOut());
    	hotelDetailsMobRequestBody.setCountryCode(staticDetailCriteria.getCountryCode());
    	hotelDetailsMobRequestBody.setCityCode(staticDetailCriteria.getCityCode());
    	hotelDetailsMobRequestBody.setLocationId(staticDetailCriteria.getLocationId());
    	hotelDetailsMobRequestBody.setLocationType(staticDetailCriteria.getLocationType());
    	hotelDetailsMobRequestBody.setCurrency(staticDetailCriteria.getCurrency());
        hotelDetailsMobRequestBody.setLatitude(staticDetailCriteria.getLat());
        hotelDetailsMobRequestBody.setLongitude(staticDetailCriteria.getLng());
        hotelDetailsMobRequestBody.setRoomStayCandidates(buildRoomStayCandidates(staticDetailCriteria.getRoomStayCandidates()));
		if (staticDetailCriteria != null && utility.isDistributeRoomStayCandidates(staticDetailCriteria.getRoomStayCandidates(), hotelDetailsMobRequestBody.getExpDataMap())) {
			hotelDetailsMobRequestBody.setAppendRscInDeeplink(true);
			hotelDetailsMobRequestBody.setRscValueForDeepLink(utility.buildRscValue(staticDetailCriteria.getRoomStayCandidates()));
			hotelDetailsMobRequestBody.setRoomStayCandidates(utility.buildRoomStayDistribution(staticDetailCriteria.getRoomStayCandidates(), hotelDetailsMobRequestBody.getExpDataMap()));
		}
        hotelDetailsMobRequestBody.setComparatorHotelsList(staticDetailCriteria.getComparatorHotelIds());
        hotelDetailsMobRequestBody.setTripType(staticDetailCriteria.getTripType());
		utility.buildSlot(hotelDetailsMobRequestBody, staticDetailCriteria);
    }

    private void buildRequestDetails(HotelDetailsMobRequestBody hotelDetailsMobRequestBody,
                                     RequestDetails requestDetails) {
    	hotelDetailsMobRequestBody.setVisitorId(requestDetails.getVisitorId());
    	hotelDetailsMobRequestBody.setIdContext(requestDetails.getIdContext());
    	hotelDetailsMobRequestBody.setNotifCoupon(requestDetails.getNotifCoupon());
    	hotelDetailsMobRequestBody.setVisitNumber(String.valueOf(requestDetails.getVisitNumber()));
    	hotelDetailsMobRequestBody.setTrafficSource(buildTrafficSource(requestDetails.getTrafficSource()));
    	hotelDetailsMobRequestBody.setLoggedIn(requestDetails.isLoggedIn());
		hotelDetailsMobRequestBody.setFunnelSource(requestDetails.getFunnelSource());
		hotelDetailsMobRequestBody.setJourneyId(requestDetails.getJourneyId());
    }

    private List<RoomStayCandidate> buildRoomStayCandidates(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {

        List<RoomStayCandidate> roomStayCandidateList = new ArrayList<>();

        for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate : roomStayCandidates){
        	RoomStayCandidate roomStayCandidateCB = new RoomStayCandidate();
            roomStayCandidateCB.setGuestCounts(buildGuestCounts(roomStayCandidate));
            roomStayCandidateList.add(roomStayCandidateCB);
        }
        return roomStayCandidateList;
    }

    private List<GuestCount> buildGuestCounts(
            com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate) {
        List<GuestCount> guestCounts = new ArrayList<>();
        GuestCount guestCount = new GuestCount();
        guestCount.setAgeQualifyingCode("10");
        guestCount.setAges(roomStayCandidate.getChildAges());
        guestCount.setCount(String.valueOf(roomStayCandidate.getAdultCount()));
        guestCounts.add(guestCount);
        return guestCounts;
    }

    private TrafficSource buildTrafficSource(com.mmt.hotels.clientgateway.request.TrafficSource trafficSource) {
    	if(null == trafficSource){
    		return null;
    	}

    	TrafficSource trafficSourceCB = new TrafficSource();
        trafficSourceCB.setSource(trafficSource.getSource());
        trafficSourceCB.setType(trafficSource.getType());
        // Pass aud field to downstream APIs
        if (trafficSource.getAud() != null) {
            trafficSourceCB.setAud(trafficSource.getAud());
        }
        return trafficSourceCB;
    }

	public HotelDetailsMobRequestBody convertStaticDetailRequest(SearchRoomsRequest searchRequest,
			CommonModifierResponse commonModifierResponse, HotelDetailsMobRequestBody hotelDetailsMobRequestBody) {

		hotelDetailsMobRequestBody.setCorrelationKey(searchRequest.getCorrelationKey());
		hotelDetailsMobRequestBody.setCurrency(searchRequest.getSearchCriteria().getCurrency());
		hotelDetailsMobRequestBody.setLoggedIn(searchRequest.getRequestDetails().isLoggedIn());

		hotelDetailsMobRequestBody.setApplicationId(commonModifierResponse.getApplicationId());
		hotelDetailsMobRequestBody.setDomain(commonModifierResponse.getDomain());
		hotelDetailsMobRequestBody.setMcid(commonModifierResponse.getMcId());
		hotelDetailsMobRequestBody.setCdfContextId(commonModifierResponse.getCdfContextId());

        if(commonModifierResponse.getExtendedUser() != null){
			hotelDetailsMobRequestBody.setUuid(commonModifierResponse.getExtendedUser().getUuid());
			hotelDetailsMobRequestBody.setProfileType(commonModifierResponse.getExtendedUser().getProfileType());
			hotelDetailsMobRequestBody.setSubProfileType(commonModifierResponse.getExtendedUser().getAffiliateId());
        }

		if(commonModifierResponse.getHydraResponse() != null){
			hotelDetailsMobRequestBody.setHydraSegments(commonModifierResponse.getHydraResponse().getHydraMatchedSegment());
			hotelDetailsMobRequestBody.setFirstTimeUserState(commonModifierResponse.getHydraResponse().isFirstUserState() ?  1 : 0);
		}
		if (searchRequest.getSearchCriteria() != null && searchRequest.getSearchCriteria().getMultiCurrencyInfo() != null) {
			hotelDetailsMobRequestBody.setMultiCurrencyInfo(utility.buildMultiCurrencyInfoRequest(searchRequest.getSearchCriteria().getMultiCurrencyInfo()));
		}
		if (searchRequest.getSearchCriteria().getUserGlobalInfo() != null) {
			hotelDetailsMobRequestBody.setUserGlobalInfo(utility.buildUserGlobalInfoHES(searchRequest.getSearchCriteria().getUserGlobalInfo()));
		}


		return hotelDetailsMobRequestBody;
	}

	public com.mmt.hotels.model.request.CityGuideRequest convertCityGuideRequest(CityGuideRequest inputRequest, String correlationKey) {
		com.mmt.hotels.model.request.CityGuideRequest request = new com.mmt.hotels.model.request.CityGuideRequest();
		request.setCorrelationKey(correlationKey);
		if (null != inputRequest.getSearchCriteria()) {
			request.setCheckin(inputRequest.getSearchCriteria().getCheckIn());
			request.setCheckout(inputRequest.getSearchCriteria().getCheckOut());
			request.setCityCode(inputRequest.getSearchCriteria().getCityCode());
			request.setLocationId(inputRequest.getSearchCriteria().getLocationId());
			request.setLocationType(inputRequest.getSearchCriteria().getLocationType());
			request.setCurrency(inputRequest.getSearchCriteria().getCurrency());
			request.setCountryCode(inputRequest.getSearchCriteria().getCountryCode());
			if(CollectionUtils.isNotEmpty(inputRequest.getSearchCriteria().getRoomStayCandidates())) {
				request.setRoomStayCandidates(buildRoomStayCandidates(inputRequest.getSearchCriteria().getRoomStayCandidates()));
			}
		}
		return request;
	}
}
