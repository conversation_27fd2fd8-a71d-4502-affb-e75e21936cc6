package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchHotelsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchHotelsResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchHotelsResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchHotelsResponseTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchUpsellRatePlanResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchHotelsResponseTransformerSCION;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchHotelsResponseTransformer;

import com.mmt.hotels.clientgateway.transformer.response.android.SearchHotelsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.SearchHotelsResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.SearchHotelsResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.SearchHotelsResponseTransformerPWA;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.transformer.request.SearchHotelsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.SearchHotelsRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.SearchHotelsRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.SearchHotelsRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.SearchHotelsRequestTransformerPWA;

@Component
public class SearchHotelsFactory {
	
	@Autowired
	private SearchHotelsRequestTransformerPWA searchHotelsRequestTransformerPWA;

	@Autowired
	private SearchHotelsResponseTransformerPWA searchHotelsResponseTransformerPWA;
	
	@Autowired
	private SearchHotelsRequestTransformerDesktop searchHotelsRequestTransformerDesktop;

	@Autowired
	private SearchHotelsResponseTransformerDesktop searchHotelsResponseTransformerDesktop;
	
	@Autowired
	private SearchHotelsRequestTransformerAndroid searchHotelsRequestTransformerAndroid;

	@Autowired
	private SearchHotelsResponseTransformerAndroid searchHotelsResponseTransformerAndroid;
	
	@Autowired
	private SearchHotelsRequestTransformerIOS searchHotelsRequestTransformerIOS;

	@Autowired
	private SearchHotelsResponseTransformerIOS searchHotelsResponseTransformerIOS;

	@Autowired
	OrchSearchHotelsResponseTransformerDesktop orchSearchHotelsResponseTransformerDesktop;

	@Autowired
	OrchSearchHotelsResponseTransformerPWA orchSearchHotelsResponseTransformerPWA;

	@Autowired
	OrchSearchHotelsResponseTransformerIOS orchSearchHotelsResponseTransformerIOS;

	@Autowired
	OrchSearchHotelsResponseTransformerAndroid orchSearchHotelsResponseTransformerAndroid;

	@Autowired
	OrchUpsellRatePlanResponseTransformer orchUpsellRatePlanResponseTransformer;

	@Autowired
	OrchSearchHotelsResponseTransformerSCION orchSearchHotelsResponseTransformerSCION;
	
	public SearchHotelsRequestTransformer  getRequestService(String client) {
		if (StringUtils.isEmpty(client))
			return searchHotelsRequestTransformerDesktop;
		switch(client) {
			case "PWA":
			case "MSITE":
				return searchHotelsRequestTransformerPWA;
			case "DESKTOP": return searchHotelsRequestTransformerDesktop;
			case "ANDROID": return searchHotelsRequestTransformerAndroid;
			case "IOS": return searchHotelsRequestTransformerIOS;
		}
		return searchHotelsRequestTransformerDesktop;
	}

	public com.mmt.hotels.clientgateway.transformer.response.SearchHotelsResponseTransformer getResponseService(String client) {
		if (StringUtils.isEmpty(client))
			return searchHotelsResponseTransformerDesktop;
		switch(client){
			case "PWA":
			case "MSITE":
				return searchHotelsResponseTransformerPWA;
			case "DESKTOP": return searchHotelsResponseTransformerDesktop;
			case "ANDROID": return searchHotelsResponseTransformerAndroid;
			case "IOS": return searchHotelsResponseTransformerIOS;
		}
		return searchHotelsResponseTransformerDesktop;
	}

	public OrchSearchHotelsResponseTransformer getSearchHotelsResponseService(String client) {
		if (StringUtils.isEmpty(client))
			return orchSearchHotelsResponseTransformerDesktop;
		switch (client) {
			case "PWA":
			case "MSITE":
				return orchSearchHotelsResponseTransformerPWA;
			case "DESKTOP":
				return orchSearchHotelsResponseTransformerDesktop;
			case "ANDROID":
				return orchSearchHotelsResponseTransformerAndroid;
			case "IOS":
				return orchSearchHotelsResponseTransformerIOS;
		}
		return orchSearchHotelsResponseTransformerDesktop;
	}

	public OrchUpsellRatePlanResponseTransformer getUpsellRatePlanResponseService(String client) {

		return orchUpsellRatePlanResponseTransformer;
	}

	public OrchSearchHotelsResponseTransformerSCION getSearchHotelsScionTransformer(String client) {

		return orchSearchHotelsResponseTransformerSCION;
	}
}
