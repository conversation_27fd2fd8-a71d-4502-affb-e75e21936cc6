package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailsResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailsResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailsResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchStaticDetailsResponseTransformerPWA;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class OrchStaticDetailFactory {

    @Autowired
    private OrchStaticDetailsResponseTransformerAndroid orchStaticDetailsResponseTransformerAndroid;

    @Autowired
    private OrchStaticDetailsResponseTransformerPWA orchStaticDetailsResponseTransformerPwa;

    @Autowired
    private OrchStaticDetailsResponseTransformerIOS orchStaticDetailsResponseTransformerIOS;

    @Autowired
    private OrchStaticDetailsResponseTransformerDesktop orchStaticDetailsResponseTransformerDesktop;


    public OrchStaticDetailResponseTransformer getResponseService(String client) {
        if (StringUtils.isEmpty(client))
            return orchStaticDetailsResponseTransformerDesktop;
        switch(client) {
            case "PWA":
            case "MSITE":
                return  orchStaticDetailsResponseTransformerPwa;
            case "DESKTOP": return orchStaticDetailsResponseTransformerDesktop;
            case "ANDROID": return orchStaticDetailsResponseTransformerAndroid;
            case "IOS": return orchStaticDetailsResponseTransformerIOS;
        }
        return orchStaticDetailsResponseTransformerDesktop;
    }
}
