package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.fasterxml.jackson.databind.JsonNode;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.request.StaticDetailRequest;
import com.mmt.hotels.clientgateway.response.Style;
import com.mmt.hotels.clientgateway.response.staticdetail.CategoryTag;
import com.mmt.hotels.clientgateway.response.staticdetail.ComparatorResponse;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.response.staticdetail.StaticDetailResponse;
import com.mmt.hotels.clientgateway.response.staticdetail.TitleData;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.ReArchUtility;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysTooltip;
import com.mmt.hotels.model.response.staticdata.StaffInfo;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.LUXE_ICON_DESKTOP;
import static com.mmt.hotels.clientgateway.constants.Constants.VALUE_STAYS_TOOL_TIP_NEW_DETAIL_PAGE_DT;

@Component
public class OrchStaticDetailsResponseTransformerDesktop extends OrchStaticDetailResponseTransformer {

    private static final Logger logger = LoggerFactory.getLogger(OrchStaticDetailsResponseTransformerDesktop.class);

    @Value("${consul.enable}")
    private boolean consulFlag;

    @Autowired
    private ReArchUtility utility;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    @Autowired
    private PropertyManager propertyManager;

    @Autowired
    private PolyglotHelper polyglotHelper;

    @Autowired
    private PolyglotService polyglotService;

    private ValueStaysTooltip valueStaysTooltipDom;
    private ValueStaysTooltip valueStaysTooltipIntl;

    @Value("${value.stays.title.icon}")
    private String valueStatysTitleIcon;

    @Value("${value.stays.icon.new.detail.page}")
    private String valueStaysIconNewDetailPageDt;

    @Value("${value.stays.title.icon.gcc}")
    private String valueStatysTitleIconGcc;

    @Value("${star.host.icon.desktop}")
    private String starHostIconDesktop;

    @Value("${mmt.value.stays.category.icon.url.desktop}")
    private String mmtValueStaysCategoryIconUrlDesktop;

    @PostConstruct
    public void init() {
        try {
            if (consulFlag) {
                valueStaysTooltipDom = commonConfigConsul.getMmtValueStaysTooltipDom();
                valueStaysTooltipIntl = commonConfigConsul.getMmtValueStaysTooltipIntl();
                logger.debug("Fetching values from commonConfig consul");
            } else {
                CommonConfig commonConfig = propertyManager.getProperty("commonConfig", CommonConfig.class);
                valueStaysTooltipDom = commonConfig.mmtValueStaysTooltipDom();
                valueStaysTooltipIntl = commonConfig.mmtValueStaysTooltipIntl();
                commonConfig.addPropertyChangeListener("mmtValueStaysTooltipDom", 
                    evt -> valueStaysTooltipDom = commonConfig.mmtValueStaysTooltipDom());
                commonConfig.addPropertyChangeListener("mmtValueStaysTooltipIntl", 
                    evt -> valueStaysTooltipIntl = commonConfig.mmtValueStaysTooltipIntl());
            }
        } catch (Exception ex) {
            logger.error("error in fetching commonConfig pms properties", ex);
        }
    }

    @Override
    public Map<String, String> buildCardTitleMap() {
        return null;
    }

    @Override
    public void addTitleData(HotelResult hotelResult, String countryCode, boolean isNewDetailPageDesktop) {
        if (hotelResult == null) {
            return;
        }
        TitleData titleData = new TitleData();
        titleData.setTitleIcon(Utility.isGccOrKsa() ? valueStatysTitleIconGcc : valueStatysTitleIcon);
        ValueStaysTooltip valueStaysTooltip;
        if (StringUtils.isBlank(countryCode) || "IN".equalsIgnoreCase(countryCode)) {
            valueStaysTooltip = SerializationUtils.clone(valueStaysTooltipDom);
        } else {
            valueStaysTooltip = SerializationUtils.clone(valueStaysTooltipIntl);
        }
        polyglotHelper.translateValueStaysTooltip(valueStaysTooltip);
        titleData.setTooltip(valueStaysTooltip);
        if (isNewDetailPageDesktop) {
            CategoryTag categoryTag = new CategoryTag();
            categoryTag.setIconUrl(valueStaysIconNewDetailPageDt);
            Style style = new Style();
            style.setIconHeight(29);
            style.setIconWidth(134);
            categoryTag.setStyle(style);
            if (valueStaysTooltip != null) {
                valueStaysTooltip.setTooltipType(VALUE_STAYS_TOOL_TIP_NEW_DETAIL_PAGE_DT);
                categoryTag.setTooltip(valueStaysTooltip);
            }
            hotelResult.setCategoryTag(categoryTag);
        }
        hotelResult.setTitleData(titleData);
    }

    @Override
    public String getLuxeIcon() {
        return LUXE_ICON_DESKTOP;
    }

    @Override
    public StaffInfo convertStaffInfo(com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffInfo staffInfo) {
        if (staffInfo != null && BooleanUtils.isTrue(staffInfo.getIsStarHost())) {
            staffInfo.setStarHostIconUrl(starHostIconDesktop);
        }
        StaffInfo staffInfoCg = super.convertStaffInfo(staffInfo);
        return staffInfoCg;
    }

    /**
     * Convert HotelStaticContentResponse to StaticDetailResponse with desktop-specific modifications
     * This method is adapted for orchestrator pattern using HotelStaticContentResponse
     */
    public StaticDetailResponse convertStaticDetailResponse(com.gommt.hotels.orchestrator.detail.model.response.HotelStaticContentResponse hotelStaticContentResponse, 
                                                           String client, 
                                                           StaticDetailRequest staticDetailRequest, 
                                                           CommonModifierResponse commonModifierResponse) {
        // Use the parent's main mapping method
        StaticDetailResponse staticDetailResponse = super.convertStaticDetailResponse(
                staticDetailRequest, hotelStaticContentResponse, commonModifierResponse);
        
        // Apply desktop-specific modifications
        if (staticDetailResponse != null && staticDetailResponse.getHotelDetails() != null
                && StringUtils.isNotBlank(staticDetailResponse.getHotelDetails().getCategoryIcon())
                && StringUtils.isNotBlank(mmtValueStaysCategoryIconUrlDesktop) 
                && isNotAHiddenGemIcon(staticDetailResponse)
                && isNotGSTAssuredIcon(hotelStaticContentResponse)) {
            staticDetailResponse.getHotelDetails().setCategoryIcon(mmtValueStaysCategoryIconUrlDesktop);
        }
        return staticDetailResponse;
    }

    /**
     * If a property is a Hidden_Gem, Hidden Gem Icon need to be shown
     */
    private boolean isNotAHiddenGemIcon(StaticDetailResponse staticDetailResponse) {
        if (staticDetailResponse != null && staticDetailResponse.getHotelDetails() != null 
                && staticDetailResponse.getHotelDetails().getCategories() != null) {
            return !staticDetailResponse.getHotelDetails().getCategories().contains(Constants.HIDDEN_GEM);
        }
        return true;
    }

    private boolean isNotGSTAssuredIcon(com.gommt.hotels.orchestrator.detail.model.response.HotelStaticContentResponse hotelStaticContentResponse) {
        if (null != hotelStaticContentResponse && null != hotelStaticContentResponse.getHotelMetaData() 
                && null != hotelStaticContentResponse.getHotelMetaData().getGstInfo()) {
            return !hotelStaticContentResponse.getHotelMetaData().getGstInfo().isGstAssured();
        }
        return true;
    }

    @Override
    public JsonNode buildWeaverResponse(JsonNode weaverResponse) {
        return weaverResponse;
    }

    /**
     * Override buildHotelCompareResponseResponse for desktop to build hotel display map with headings
     */
    @Override
    public ComparatorResponse buildHotelCompareResponseResponse(StaticDetailRequest staticDetailRequest,
                                                                  SearchHotelsRequest searchHotelsRequest, 
                                                                  Map<String, com.gommt.hotels.orchestrator.detail.model.response.ComparatorResponse> hotelCompareResponseMap,
                                                                  Map<String, String> expDataMap, 
                                                                  boolean isLiteResponse, 
                                                                  CommonModifierResponse commonModifierResponse, 
                                                                  String rscValue, 
                                                                  boolean myPartnerReq) {
        // Call the parent method to get the base comparator response
        ComparatorResponse comparatorResponse = super.buildHotelCompareResponseResponse(staticDetailRequest, searchHotelsRequest, 
                                                                                       hotelCompareResponseMap, expDataMap, 
                                                                                       isLiteResponse, commonModifierResponse, 
                                                                                       rscValue, myPartnerReq);
        
        if (comparatorResponse != null) {
            // Build a hotel display map for desktop
            utility.buildHotelDisplayMap(comparatorResponse, hotelCompareResponseMap.get("RECOMMENDED_HOTELS"), searchHotelsRequest);
        }
        
        return comparatorResponse;
    }
}
