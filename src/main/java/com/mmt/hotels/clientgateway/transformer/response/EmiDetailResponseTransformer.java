package com.mmt.hotels.clientgateway.transformer.response;

import com.ibm.icu.text.MessageFormat;
import com.ibm.icu.text.NumberFormat;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.response.emi.*;
import com.mmt.hotels.clientgateway.response.searchHotels.BgGradient;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.Currency;
import com.mmt.hotels.emi.detail.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

@Component
public class EmiDetailResponseTransformer {

    @Autowired
    private CommonConfigConsul commonConfigConsul;

    @Autowired
    PolyglotService polyglotService;

    BgGradient noCostEmiIconConfig;
    NumberFormat numberFormatter;

    private static final Logger logger = LoggerFactory.getLogger(EmiDetailResponseTransformer.class);

    @PostConstruct
    void init() {
        try {
            noCostEmiIconConfig = commonConfigConsul.getNoCostEmiIconConfig();

            // Format the totalCost with commas and without decimals
            numberFormatter = NumberFormat.getNumberInstance(new Locale("en", "IN"));
            numberFormatter.setMaximumFractionDigits(0); // No decimals
            numberFormatter.setMinimumFractionDigits(0); // Ensure no trailing zeros
        } catch (Exception e) {
            logger.error("Error in Getting Properties File: ", e);
        }
    }

    public EMIDetailResponse convertEmiDetailResponse(EmiDetailResponse emiDetailResponse) {
        EMIDetailResponse emiDetailResponseCG = new EMIDetailResponse();

        if (emiDetailResponse == null || Constants.FAILURE.equalsIgnoreCase(emiDetailResponse.getStatus()) ||
                emiDetailResponse.hasErrorResponse() && CollectionUtils.isNotEmpty(emiDetailResponse.getErrorResponse().getErrorListList()) ||
                MapUtils.isEmpty(emiDetailResponse.getBanksMap())) {
            emiDetailResponseCG.setStatus(Constants.FAILURE);
            return emiDetailResponseCG;
        }

        // Setting no cost EMI tag details
        if (emiDetailResponse.getNoCostEmiAvailable()) {
            EmiTagDetails emiTagDetails = new EmiTagDetails();
            emiTagDetails.setText("NO COST EMI");
            emiTagDetails.setBgGradient(noCostEmiIconConfig);
            Map<String, EmiTagDetails> emiTags = new HashMap<>();
            emiTags.put(EmiPlanType.NO_COST.name(), emiTagDetails);
            emiDetailResponseCG.setEmiTags(emiTags);
        }

        // Setting header details
        EmiPageHeader emiPageHeader = new EmiPageHeader();
        emiPageHeader.setPageTitle(polyglotService.getTranslatedData(ConstantsTranslation.EMI_L1_PAGE_TITLE));
        emiPageHeader.setSubPageTitle(polyglotService.getTranslatedData(ConstantsTranslation.EMI_L1_PAGE_SUB_TITLE));
        emiPageHeader.setFare(Currency.INR.getCurrencySymbol() + numberFormatter.format(emiDetailResponse.getTotalPayableAmount()));
        emiDetailResponseCG.setHeader(emiPageHeader);

        if (MapUtils.isNotEmpty(emiDetailResponse.getBanksMap())) {
            List<PaymentOption> paymentOptionList = new ArrayList<>();

            if (emiDetailResponse.getBanksMap().containsKey("ccBanks")) {
                paymentOptionList.add(processBankDetails(emiDetailResponse.getBanksMap().get("ccBanks"), "Credit Card", "Credit Card EMI Plans"));
            }

            if (emiDetailResponse.getBanksMap().containsKey("dcBanks")) {
                paymentOptionList.add(processBankDetails(emiDetailResponse.getBanksMap().get("dcBanks"), "Debit Card", "Debit Card EMI Plans"));
            }

            emiDetailResponseCG.setPaymentOptions(paymentOptionList);
        }

        return emiDetailResponseCG;
    }

    private PaymentOption processBankDetails(BankDetailsMap bankDetailsMap, String title, String heading) {
        PaymentOption paymentOption = new PaymentOption();
        paymentOption.setTitle(title);
        paymentOption.setHeading(heading);

        if (bankDetailsMap != null && MapUtils.isNotEmpty(bankDetailsMap.getBankDetailMapMap())) {
            List<EmiBankDetails> emiBankDetailsList = new ArrayList<>();

            for (Map.Entry<String, BankDetailsList> entry : bankDetailsMap.getBankDetailMapMap().entrySet()) {
                if (entry.getValue() != null && CollectionUtils.isNotEmpty(entry.getValue().getBankDetailsList())) {
                    List<BankDetails> bankDetails = entry.getValue().getBankDetailsList();
                    processBankDetail(bankDetails, emiBankDetailsList, entry.getKey());
                }
            }

            paymentOption.setBankList(emiBankDetailsList);
        }

        return paymentOption;
    }

    private void processBankDetail(List<BankDetails> bankDetails, List<EmiBankDetails> emiBankDetailsList, String key) {
        if (CollectionUtils.isEmpty(bankDetails)) {
            return;
        }

        for (BankDetails bankDetail : bankDetails) {
            EmiBankDetails emiBankDetails = new EmiBankDetails();
            emiBankDetails.setBankLogo(bankDetail.getBankLogo());
            emiBankDetails.setName(bankDetail.getName());

            if (bankDetail.getBestEmiType().equalsIgnoreCase("No Cost Emi")) {
                emiBankDetails.setTagType(EmiPlanType.NO_COST.name());
            }

            if (!Constants.DEFAULT.equalsIgnoreCase(key) && StringUtils.isNotEmpty(bankDetail.getCouponCode()) && bankDetail.getCouponDiscount() > 0) {
                emiBankDetails.setDescription(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.NO_COST_EMI_COUPON_CODE_AND_DISCOUNT_TEXT), bankDetail.getCouponDiscount(), bankDetail.getCouponCode()));
            }

            if (CollectionUtils.isNotEmpty(bankDetail.getEmiPlanDetailsList())) {
                emiBankDetails.setEmiDetails(processEmiPlanDetails(bankDetail.getEmiPlanDetailsList(), bankDetail.getName(), emiBankDetails));
            }
            emiBankDetailsList.add(emiBankDetails);
        }
    }

    private EmiPlanDetails processEmiPlanDetails(List<EMIPlanDetails> emiPlanDetailsList, String bankName, EmiBankDetails emiBankDetails) {
        EmiPlanDetails emiPlanDetails = new EmiPlanDetails();
        List<EmiPlan> emiList = new ArrayList<>();
        double lowestNoCostAmount = 0.0;
        double lowestEmiAmount = 0.0;

        for (EMIPlanDetails planDetails : emiPlanDetailsList) {
            EmiPlan emiPlan = new EmiPlan();
            emiPlan.setTotalCost(Currency.INR.getCurrencySymbol() + numberFormatter.format(planDetails.getTotalCost()));
            emiPlan.setTagType(planDetails.getEmiType());

            long emiAmount = Math.round(planDetails.getEmiAmount());
            StringBuilder emiPlanTypeBuilder = new StringBuilder()
                    .append(planDetails.getTenure())
                    .append(Constants.SPACE)
                    .append("months")
                    .append(Constants.SPACE)
                    .append(" x ")
                    .append(Currency.INR.getCurrencySymbol()).append(numberFormatter.format(emiAmount));
            emiPlan.setPlanType(emiPlanTypeBuilder.toString());

            if (planDetails.getAnnualInterest() > 0 && planDetails.getTotalInterest() > 0) {
                StringBuilder emiMessageBuilder = new StringBuilder("Including")
                        .append(Constants.SPACE)
                        .append(Currency.INR.getCurrencySymbol())
                        .append(numberFormatter.format(planDetails.getTotalInterest()))
                        .append(Constants.SPACE)
                        .append("interest")
                        .append(Constants.SPACE)
                        .append("@")
                        .append(planDetails.getAnnualInterest())
                        .append("%");
                emiPlan.setMessage(emiMessageBuilder.toString());
            }

            if (EmiPlanType.NO_COST.name().equalsIgnoreCase(planDetails.getEmiType())) {
                lowestNoCostAmount = planDetails.getEmiAmount();
            } else {
                lowestEmiAmount = planDetails.getEmiAmount();
            }
            emiList.add(emiPlan);
        }

        emiPlanDetails.setEmis(emiList);
        emiPlanDetails.setEmiTitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.EMI_L2_PAGE_TITLE), bankName));
        double finalLowestEmiAmount = lowestNoCostAmount > 0 ? lowestNoCostAmount : lowestEmiAmount;
        emiBankDetails.setAlertMessage(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.EMI_L1_ALERT_TEXT), numberFormatter.format(finalLowestEmiAmount)));

        return emiPlanDetails;
    }
}
