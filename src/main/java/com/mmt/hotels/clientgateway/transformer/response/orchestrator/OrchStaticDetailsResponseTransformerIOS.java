package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.fasterxml.jackson.databind.JsonNode;
import com.gommt.hotels.orchestrator.detail.model.response.content.HotelMetaData;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.staticdata.StaffInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.HIDDEN_GEM;
import static com.mmt.hotels.clientgateway.constants.Constants.LUXE_ICON_APPS;
import static com.mmt.hotels.clientgateway.constants.Constants.MMT_VALUE_STAYS;

@Component
public class OrchStaticDetailsResponseTransformerIOS extends OrchStaticDetailResponseTransformer {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrchStaticDetailsResponseTransformerIOS.class);


    @Value("${star.host.icon.app}")
    private String starHostIconApp;

    @Autowired
    private Utility utility;

    @Override
    public Map<String, String> buildCardTitleMap() {
        return null;
    }

    @Override
    public void addTitleData(HotelResult hotelResult, String countryCode, boolean isNewDetailPageDesktop) {}

    @Override
    public String getLuxeIcon() {
        return LUXE_ICON_APPS;
    }

    @Override
    public JsonNode buildWeaverResponse(JsonNode weaverResponse) {
        utility.updateWeaverResponse(weaverResponse);
        return weaverResponse;
    }

    @Override
    public StaffInfo convertStaffInfo(com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffInfo staffInfo) {
        if(staffInfo != null && BooleanUtils.isTrue(staffInfo.getIsStarHost())){
            staffInfo.setStarHostIconUrl(starHostIconApp);
        }
        removeIcon(staffInfo);
        return super.convertStaffInfo(staffInfo);
    }

    @Override
    public String buildCategoryIcon(HotelMetaData hotelMetaData, boolean isCorpIdContext, Map<String, String> expDataMap) {

        if (CollectionUtils.isNotEmpty(hotelMetaData.getPropertyDetails().getCategories())
                && hotelMetaData.getPropertyDetails().getCategories().contains(MMT_VALUE_STAYS) && !isCorpIdContext) {
            return "https://promos.makemytrip.com/Growth/Images/B2C/mmt_vs_details.png";
        }

        if (utility.hasHiddenGemExpEnabled(expDataMap) && CollectionUtils.isNotEmpty(hotelMetaData.getPropertyDetails().getCategories())
                && hotelMetaData.getPropertyDetails().getCategories().contains(HIDDEN_GEM)) {
            return "https://promos.makemytrip.com/Growth/Images/B2C/mmt_hg_details.png";
        }

        return null;
    }
}
