package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.HotelPersuasionData;
import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.Hover;
import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.PersuasionValue;
import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.Style;
import com.mmt.hotels.model.persuasion.response.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ChatBotPersuasionHelper {


    private static final Logger LOGGER = LoggerFactory.getLogger(ChatBotPersuasionHelper.class);


    public Map<String, Persuasion> mapChatBotPersuasions(Map<String, HotelPersuasionData> chatBotPersuasion) {
        if (chatBotPersuasion == null) return null;
        Map<String, Persuasion> persuasionMap = new HashMap<>();
        for (Map.Entry<String, HotelPersuasionData> entry : chatBotPersuasion.entrySet()) {
            try {
                Persuasion persuasion = new Persuasion();
                HotelPersuasionData data = entry.getValue();
                if (data != null) {
                    // Map simple fields
                    persuasion.setPlaceholder(data.getPlaceholder());
                    persuasion.setTemplate(data.getTemplate());
                    persuasion.setTemplateType(data.getTemplateType());
                    persuasion.setTopLevelText(data.getTopLevelText());
                    persuasion.setSeparator(data.getSeparator());

                    // Map data field
                    if (data.getData() != null && !data.getData().isEmpty()) {
                        List<com.mmt.hotels.model.persuasion.response.PersuasionData> persuasionDataList = new ArrayList<>();
                        for (PersuasionValue value : data.getData()) {
                            // Use the new method to map PersuasionValue to PersuasionData
                            com.mmt.hotels.model.persuasion.response.PersuasionData persuasionData =
                                    mapPersuasionValueToPersuasionData(value);
                            if (persuasionData != null) {
                                persuasionDataList.add(persuasionData);
                            }
                        }
                        persuasion.setData(persuasionDataList);
                    }

                    // Map style (top-level style) using the new method
                    persuasion.setStyle(mapStyleToStyleResponseBO(data.getStyle()));

                    // Map hover using the new method
                    persuasion.setHover(mapHoverToHoverResponseBO(data.getHover()));

                    // Map extraDetails using the new method
                    persuasion.setExtraDetails(mapExtraDetailsToExtraDetails(data.getExtraDetails()));
                }
                persuasionMap.put(entry.getKey(), persuasion);
            } catch (Exception e) {
                LOGGER.error("Error mapping chatbot persuasion for key: " + entry.getKey(), e);
            }
        }
        return persuasionMap;
    }


    /**
     * Maps Style from orchestrator to StyleResponseBO
     * @param orchestratorStyle The orchestrator Style object
     * @return StyleResponseBO with all available fields mapped
     */
    private StyleResponseBO mapStyleToStyleResponseBO(Style orchestratorStyle) {
        if (orchestratorStyle == null) return null;

        StyleResponseBO styleResponseBO = new StyleResponseBO();

        // Map basic string fields
        styleResponseBO.setTextColor(orchestratorStyle.getTextColor());
        styleResponseBO.setBgColor(orchestratorStyle.getBgColor());
        styleResponseBO.setSecondaryBgColor(orchestratorStyle.getSecondaryBgColor());
        styleResponseBO.setFontSize(orchestratorStyle.getFontSize());
        styleResponseBO.setBorderColor(orchestratorStyle.getBorderColor());
        styleResponseBO.setFontType(orchestratorStyle.getFontType());
        styleResponseBO.setBgUrl(orchestratorStyle.getBgUrl());
        styleResponseBO.setCornerRadii(orchestratorStyle.getCornerRadii());
        styleResponseBO.setSeparatorType(orchestratorStyle.getSeparatorType());
        styleResponseBO.setImageUrl(orchestratorStyle.getImageUrl());
        styleResponseBO.setGravity(orchestratorStyle.getGravity());
        styleResponseBO.setTitleColor(orchestratorStyle.getTitleColor());
        styleResponseBO.setTitle(orchestratorStyle.getTitle());

        // Map integer fields
        styleResponseBO.setBorderSize(orchestratorStyle.getBorderSize());
        styleResponseBO.setIconWidth(orchestratorStyle.getIconWidth());
        styleResponseBO.setIconHeight(orchestratorStyle.getIconHeight());
        styleResponseBO.setHorizontalSpace(orchestratorStyle.getHorizontalSpace());
        styleResponseBO.setVerticalSpace(orchestratorStyle.getVerticalSpace());
        styleResponseBO.setMaxLines(orchestratorStyle.getMaxLines());
        styleResponseBO.setMaxCount(orchestratorStyle.getMaxCount());
        styleResponseBO.setImageWidth(orchestratorStyle.getImageWidth());
        styleResponseBO.setImageHeight(orchestratorStyle.getImageHeight());

        // Map list fields
        styleResponseBO.setStyleClasses(orchestratorStyle.getStyleClasses());
        styleResponseBO.setCorners(orchestratorStyle.getCorners());
        styleResponseBO.setBorderStyle(orchestratorStyle.getBorderStyle());

        // Map bgGradient
        if (orchestratorStyle.getBgGradient() != null) {
            BgGradient bgGradient = new BgGradient();
            bgGradient.setAngle(orchestratorStyle.getBgGradient().getAngle());
            bgGradient.setStart(orchestratorStyle.getBgGradient().getStart());
            bgGradient.setEnd(orchestratorStyle.getBgGradient().getEnd());
            styleResponseBO.setBgGradient(bgGradient);
        }

        // Map textGradient
        if (orchestratorStyle.getTextGradient() != null) {
            BgGradient textGradient = new BgGradient();
            textGradient.setAngle(orchestratorStyle.getTextGradient().getAngle());
            textGradient.setStart(orchestratorStyle.getTextGradient().getStart());
            textGradient.setEnd(orchestratorStyle.getTextGradient().getEnd());
            styleResponseBO.setTextGradient(textGradient);
        }

        // Map borderGradient
        if (orchestratorStyle.getBorderGradient() != null) {
            com.mmt.hotels.model.response.mmtprime.BorderGradient borderGradient = 
                new com.mmt.hotels.model.response.mmtprime.BorderGradient();
            borderGradient.setAngle(orchestratorStyle.getBorderGradient().getAngle());
            borderGradient.setStart(orchestratorStyle.getBorderGradient().getStart());
            borderGradient.setEnd(orchestratorStyle.getBorderGradient().getEnd());
            borderGradient.setDirection(orchestratorStyle.getBorderGradient().getDirection());
            // Note: BgGradient has 'center' field but BorderGradient has 'color' field
            // We'll skip mapping color field for now as it's not available in BgGradient
            styleResponseBO.setBorderGradient(borderGradient);
        }

        return styleResponseBO;
    }

    /**
     * Maps Hover from orchestrator to HoverResponseBO
     * @param orchestratorHover The orchestrator Hover object
     * @return HoverResponseBO with all available fields mapped
     */
    private HoverResponseBO mapHoverToHoverResponseBO(Hover orchestratorHover) {
        if (orchestratorHover == null) return null;

        HoverResponseBO hoverResponseBO = new HoverResponseBO();

        // Map simple string fields
        hoverResponseBO.setHeadingText(orchestratorHover.getHeadingText());
        hoverResponseBO.setTooltipType(orchestratorHover.getTooltipType());
        hoverResponseBO.setCtaText(orchestratorHover.getCtaText());
        hoverResponseBO.setCtaColor(orchestratorHover.getCtaColor());

        // Map integer fields
        hoverResponseBO.setOpenHoverThreshold(orchestratorHover.getOpenHoverThreshold());

        // Map style using the existing method
        hoverResponseBO.setStyle(mapStyleToStyleResponseBO(orchestratorHover.getStyle()));

        // Map headingStyle using the existing method
        hoverResponseBO.setHeadingStyle(mapStyleToStyleResponseBO(orchestratorHover.getHeadingStyle()));

        // Map data field (it's an Object, so direct mapping)
        hoverResponseBO.setData(orchestratorHover.getData());

        return hoverResponseBO;
    }

    /**
     * Maps ExtraDetails from orchestrator to ExtraDetails
     * @param orchestratorExtraDetails The orchestrator ExtraDetails object
     * @return ExtraDetails with all available fields mapped
     */
    private com.mmt.hotels.model.persuasion.response.ExtraDetails mapExtraDetailsToExtraDetails(
            com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.ExtraDetails orchestratorExtraDetails) {
        if (orchestratorExtraDetails == null) return null;

        com.mmt.hotels.model.persuasion.response.ExtraDetails extraDetails =
                new com.mmt.hotels.model.persuasion.response.ExtraDetails();

        // Map all fields
        extraDetails.setIconUrl(orchestratorExtraDetails.getIconUrl());
        extraDetails.setActionType(orchestratorExtraDetails.getActionType());
        extraDetails.setTitle(orchestratorExtraDetails.getTitle());
        extraDetails.setStyle(mapStyleToStyleResponseBO(orchestratorExtraDetails.getStyle()));

        return extraDetails;
    }

    /**
     * Maps PersuasionValue from orchestrator to PersuasionData
     * @param persuasionValue The orchestrator PersuasionValue object
     * @return PersuasionData with all available fields mapped
     */
    private com.mmt.hotels.model.persuasion.response.PersuasionData mapPersuasionValueToPersuasionData(
            PersuasionValue persuasionValue) {
        if (persuasionValue == null) return null;

        com.mmt.hotels.model.persuasion.response.PersuasionData persuasionData =
                new com.mmt.hotels.model.persuasion.response.PersuasionData();

        // Map string fields
        persuasionData.setId(persuasionValue.getId());
        persuasionData.setText(persuasionValue.getText());
        persuasionData.setSubtext(persuasionValue.getSubtext());
        persuasionData.setPersuasionType(persuasionValue.getPersuasionType());
        persuasionData.setIconurl(persuasionValue.getIconurl());
        persuasionData.setImageUrl(persuasionValue.getImageUrl());
        persuasionData.setActionurl(persuasionValue.getActionurl());
        persuasionData.setIcontype(persuasionValue.getIcontype());
        persuasionData.setSeparator(persuasionValue.getSeparator());
        persuasionData.setActionType(persuasionValue.getActionType());
        persuasionData.setPersuasionKey(persuasionValue.getPersuasionKey());
        persuasionData.setOuterLevelPersuasionTextKey(persuasionValue.getOuterLevelPersuasionTextKey());
        persuasionData.setPersuasionTemplate(persuasionValue.getPersuasionTemplate());
        persuasionData.setPersuasionText(persuasionValue.getPersuasionText());

        // Map boolean fields
        persuasionData.setHasAction(persuasionValue.isHasAction());
        persuasionData.setHtml(persuasionValue.isHtml());
        persuasionData.setHorizontal(persuasionValue.isHorizontal());

        // Map integer fields
        if (persuasionValue.getMultiPersuasionCount() != null) {
            persuasionData.setMultiPersuasionCount(persuasionValue.getMultiPersuasionCount());
        }
        if (persuasionValue.getMultiPersuasionPriority() != null) {
            persuasionData.setMultiPersuasionPriority(persuasionValue.getMultiPersuasionPriority());
        }

        // Map style using the existing method
        persuasionData.setStyle(mapStyleToStyleResponseBO(persuasionValue.getStyle()));

        // Map subtextStyle using the existing method
        persuasionData.setSubtextStyle(mapStyleToStyleResponseBO(persuasionValue.getSubtextStyle()));

        // Map hover using the existing method
        persuasionData.setHover(mapHoverToHoverResponseBO(persuasionValue.getHover()));

        // Map button if available
        if (persuasionValue.getButton() != null) {
            com.mmt.hotels.model.persuasion.response.PersuasionButtonBO buttonBO =
                    new com.mmt.hotels.model.persuasion.response.PersuasionButtonBO();
            // Map button fields
            buttonBO.setText(persuasionValue.getButton().getText());
            buttonBO.setActionUrl(persuasionValue.getButton().getActionUrl());
            buttonBO.setHasAction(persuasionValue.getButton().isHasAction());
            buttonBO.setActionType(persuasionValue.getButton().getActionType());
            buttonBO.setStyle(mapStyleToStyleResponseBO(persuasionValue.getButton().getStyle()));
            buttonBO.setHover(mapHoverToHoverResponseBO(persuasionValue.getButton().getHover()));
            persuasionData.setButton(buttonBO);
        }

        // Map timer if available
        if (persuasionValue.getTimer() != null) {
            com.mmt.hotels.model.persuasion.response.PersuasionTimerBO timerBO =
                    new com.mmt.hotels.model.persuasion.response.PersuasionTimerBO();
            // Map all timer fields from TImer to PersuasionTimerBO
            timerBO.setExpiry(persuasionValue.getTimer().getExpiry());
            timerBO.setExpiryFormat(persuasionValue.getTimer().getExpiryFormat());
            timerBO.setStyle(mapStyleToStyleResponseBO(persuasionValue.getTimer().getStyle()));
            timerBO.setHover(mapHoverToHoverResponseBO(persuasionValue.getTimer().getHover()));
            persuasionData.setTimer(timerBO);
        }

        // Map inclusions list
        if (persuasionValue.getInclusions() != null) {
            persuasionData.setInclusions(new ArrayList<>(persuasionValue.getInclusions()));
        }

        // Map persuasionTitle if available
        if (persuasionValue.getPersuasionTitle() != null) {
            com.mmt.hotels.model.persuasion.response.PersuasionTitle persuasionTitle =
                    new com.mmt.hotels.model.persuasion.response.PersuasionTitle();
            // Map persuasionTitle fields - assuming similar structure
            persuasionTitle.setText(persuasionValue.getPersuasionTitle().getText());
            persuasionTitle.setStyle(mapStyleToStyleResponseBO(persuasionValue.getPersuasionTitle().getStyle()));
            // TODO: Map other persuasionTitle fields if available
            persuasionData.setPersuasionTitle(persuasionTitle);
        }

        // Map extraData using the new method
        persuasionData.setExtraData(mapExtraDetailsToExtraDetails(persuasionValue.getExtraData()));

        // Map childPersuasions recursively
        if (persuasionValue.getChildPersuasions() != null && !persuasionValue.getChildPersuasions().isEmpty()) {
            List<PersuasionData> childPersuasions = new ArrayList<>();
            for (PersuasionValue childValue :
                    persuasionValue.getChildPersuasions()) {
                com.mmt.hotels.model.persuasion.response.PersuasionData childData =
                        mapPersuasionValueToPersuasionData(childValue);
                if (childData != null) {
                    childPersuasions.add(childData);
                }
            }
            persuasionData.setChildPersuasions(childPersuasions);
        }

        return persuasionData;
    }
}
