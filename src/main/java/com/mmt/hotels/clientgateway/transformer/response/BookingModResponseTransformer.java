package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.modification.ModifiedGuestCount;
import com.mmt.hotels.clientgateway.request.modification.ModifiedRoomStayCandidate;
import com.mmt.hotels.clientgateway.request.modification.RatePreviewRequest;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.response.modification.*;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.corporate.InitApprovalRequest;
import com.mmt.hotels.model.request.payment.BeginCheckoutReqBody;
import com.mmt.hotels.model.response.corporate.CorpMetaInfo;
import com.mmt.hotels.model.response.modification.CancelPreviewResponse;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import com.mmt.hotels.model.response.pricing.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.kafka.common.metrics.stats.Rate;
import org.codehaus.groovy.util.StringUtil;
import org.codehaus.plexus.util.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class BookingModResponseTransformer {

    @Value("${approval.page.url}")
    private String approvalPageUrl;

    @Autowired
    private Utility utility;

    public RatePreviewResponse convertPriceResponse(RoomDetailsResponse roomDetailsResponse, RatePreviewRequest ratePreviewRequest, String pageContext, CommonModifierResponse commonModifierResponse ){
       RatePreviewResponse response = new RatePreviewResponse();
        if (roomDetailsResponse.getResponseErrors() != null && CollectionUtils.isNotEmpty(roomDetailsResponse.getResponseErrors().getErrorList())) {
            response.setSuccess(false);
            response.setErrorCode(roomDetailsResponse.getResponseErrors().getErrorList().get(0).getErrorCode());
            response.setErrorMessage(roomDetailsResponse.getResponseErrors().getErrorList().get(0).getErrorMessage());
            return  response;
        }

        response.setSuccess(true);
        RateData rateData = buildRateData(roomDetailsResponse,pageContext,commonModifierResponse);
        if(rateData != null) {
            rateData.setOldBookingId(ratePreviewRequest.getBookingId());
            response.setData(rateData);
        }
       return response;
    }

    public ProBookingResponse convertPaymentRsponse(PaymentCheckoutResponse paymentCheckoutResponse,String txnkey){
        ProBookingResponse proBookingResponse = new ProBookingResponse();
        if(paymentCheckoutResponse != null && paymentCheckoutResponse.getResponseErrors() == null) {
            ProBookingData proBookingData = new ProBookingData();
            proBookingData.setBookingId(paymentCheckoutResponse.getBookingID());
            proBookingData.setCorrelationKey(paymentCheckoutResponse.getCorrelationKey());
          proBookingData.setCurrency(paymentCheckoutResponse.getCurrency());
          proBookingData.setAltCurrencySelected(paymentCheckoutResponse.isAlternateCurrencySelected());
          if(MapUtils.isNotEmpty(paymentCheckoutResponse.getPaymentParams()) && checkIfCheckoutDataIsNotNull(paymentCheckoutResponse.getPaymentParams())) {
              proBookingData.setState(ProBookingData.State.PAYMENT);
              proBookingData.setRedirectUrl(null!=paymentCheckoutResponse.getPaymentParams().get("checkoutUrl")?paymentCheckoutResponse.getPaymentParams().get("checkoutUrl").toString():null);
              proBookingData.setStateId(null!=paymentCheckoutResponse.getPaymentParams().get("checkoutId")?paymentCheckoutResponse.getPaymentParams().get("checkoutId").toString():null);
          }else{
              proBookingData.setState(ProBookingData.State.THANKYOU);
              proBookingData.setRedirectUrl(paymentCheckoutResponse.getThankYouURL());
              proBookingData.setStateId(txnkey);
          }
          proBookingData.setTotalAmount(StringUtils.isNotBlank(paymentCheckoutResponse.getTotalAmount()) ? Double.parseDouble(paymentCheckoutResponse.getTotalAmount()) : 0.0);
          proBookingData.setTxnId(txnkey);
            proBookingResponse.setData(proBookingData);
            proBookingResponse.setSuccess(true);
        }else {
            proBookingResponse.setErrorCode(paymentCheckoutResponse != null && paymentCheckoutResponse.getResponseErrors()!= null
                    && CollectionUtils.isNotEmpty(paymentCheckoutResponse.getResponseErrors().getErrorList()) ?
                    paymentCheckoutResponse.getResponseErrors().getErrorList().get(0).getErrorCode():"200000");
            proBookingResponse.setErrorMessage(paymentCheckoutResponse != null && paymentCheckoutResponse.getResponseErrors()!= null
                    && CollectionUtils.isNotEmpty(paymentCheckoutResponse.getResponseErrors().getErrorList()) ?
                    paymentCheckoutResponse.getResponseErrors().getErrorList().get(0).getErrorMessage():"Some Unexpected Error happened");
            proBookingResponse.setSuccess(false);
        }

        return  proBookingResponse;

    }

    private boolean checkIfCheckoutDataIsNotNull(Map<String, Object> paymentParams) {
        if(paymentParams.containsKey("checkoutUrl")){
            if(paymentParams.get("checkoutUrl") == null)
                return false;
            else
                return paymentParams.containsKey("checkoutId") ? (paymentParams.get("checkoutId") == null ? false : true) : false;
        }
        else
            return false;
    }

    public ProBookingResponse convertRequestApprovalResponse(CGServerResponse cgServerResponse,String txnkey){
        ProBookingResponse proBookingResponse = new ProBookingResponse();
        ProBookingData proBookingData = new ProBookingData();

        proBookingData.setCorrelationKey(MDC.get(MDCHelper.MDCKeys.CORRELATION.getStringValue()));
      ;
        if(cgServerResponse.getResponseErrors()== null && cgServerResponse.getAdditionalProperties().containsKey("workflowId")) {
            proBookingResponse.setData(proBookingData);
            proBookingResponse.setSuccess(true);
            proBookingData.setState(ProBookingData.State.APPROVAL);
            proBookingData.setStateId(cgServerResponse.getAdditionalProperties().get("workflowId").toString());
            proBookingData.setRedirectUrl(approvalPageUrl + proBookingData.getStateId());
        }else{
            proBookingResponse.setErrorCode(cgServerResponse != null && cgServerResponse.getResponseErrors()!= null
                    && CollectionUtils.isNotEmpty(cgServerResponse.getResponseErrors().getErrorList()) ?
                    cgServerResponse.getResponseErrors().getErrorList().get(0).getErrorCode():"200000");
            proBookingResponse.setErrorMessage(cgServerResponse != null && cgServerResponse.getResponseErrors()!= null
                    && CollectionUtils.isNotEmpty(cgServerResponse.getResponseErrors().getErrorList()) ?
                    cgServerResponse.getResponseErrors().getErrorList().get(0).getErrorMessage():"Some Unexpected Error happened");
            proBookingResponse.setSuccess(false);
        }
        proBookingData.setTxnId(txnkey);
        return proBookingResponse;

    }

    private ModifiedAbsorptionDetails buildAbsorptionDetails(List<AlertInfo> alertInfoList, boolean isCouponValidated, boolean isSameRoom, boolean isSamePayMode){
       ModifiedAbsorptionDetails absorptionDetails =  new ModifiedAbsorptionDetails();
       if(CollectionUtils.isNotEmpty(alertInfoList)) {
           for(AlertInfo alertInfo : alertInfoList) {
               if(alertInfo.getMismatchType() == AlertInfo.AlertType.CANCELLATION)
                    absorptionDetails.setCancelPolicyChanged(true);
               if(alertInfo.getMismatchType() == AlertInfo.AlertType.MEALPLAN)
                    absorptionDetails.setMealPlanChanged(true);
               if(alertInfo.getMismatchType() == AlertInfo.AlertType.PRICE)
                    absorptionDetails.setPriceChanged(true);
           }
       }
        absorptionDetails.setSamePaymentMode(isSamePayMode);
        absorptionDetails.setSameRoomAvailable(isSameRoom);
        absorptionDetails.setValidateCouponSuccess(isCouponValidated);
       return absorptionDetails;
    }

    private ModifiedCorpPolicyDetails buildCorpPolicyDetails(CorpMetaInfo corpMetaInfo){
        ModifiedCorpPolicyDetails modifiedCorpData = new ModifiedCorpPolicyDetails();
        modifiedCorpData.setApprovalRequired(corpMetaInfo.getValidationPayload().isApprovalRequired());
        modifiedCorpData.setBlockOopBooking(corpMetaInfo.getValidationPayload().getBlockOopBooking()> 0);
        modifiedCorpData.setWithinPolicy(corpMetaInfo.getValidationPayload().isWithinPolicy());
        //modifiedCorpData.setDateChangeNotAllowed(corpMetaInfo.getValidationPayload().isDateChangeAllowed());
        modifiedCorpData.setBlockSkipApproval(corpMetaInfo.getValidationPayload().getBlockSkipApproval() > 0);
        return modifiedCorpData;
    }

    private double getFinalPrice(DisplayPriceBreakDown dpBrkDown,String pageContext,CommonModifierResponse commonModifierResponse){
        double finalPrice = 0;
        double wallet = Double.NaN;
        if (dpBrkDown != null) {
            finalPrice = dpBrkDown.getDisplayPrice();
            wallet = dpBrkDown.getWallet();
        }
        if (commonModifierResponse != null && utility.isMyPartner(commonModifierResponse) && !Double.isNaN(wallet)) {
            finalPrice += wallet;
        }
        return finalPrice;
    }

    private  void buildRoomDetails(RateData rateData , RoomTypeDetails roomTypeDetails, String pageContext,CommonModifierResponse commonModifierResponse){
        if(roomTypeDetails != null) {
            boolean isPricingSet = false;
            boolean isOccSet = false;
            if(roomTypeDetails.getTotalDisplayFare()!= null){
                isPricingSet = true;
                if(roomTypeDetails.getTotalDisplayFare().getCorpMetaData() != null)
                    rateData.setCorpPolicyDetails(buildCorpPolicyDetails(roomTypeDetails.getTotalDisplayFare().getCorpMetaData()));
                rateData.setFinalPayAmount(getFinalPrice(roomTypeDetails.getTotalDisplayFare().getDisplayPriceBreakDown(), pageContext,commonModifierResponse));
                if(roomTypeDetails.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo() != null) {
                    rateData.setPromoCode(roomTypeDetails.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getCouponCode());
                    rateData.setPromoText(roomTypeDetails.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getDescription());
                }
            }
            if(roomTypeDetails.getOccupancyDetails() != null){
                isOccSet = true;
                rateData.setAdultCount(roomTypeDetails.getOccupancyDetails().getAdult());
                rateData.setChildCount(roomTypeDetails.getOccupancyDetails().getChild());
                rateData.setTotalRooms(roomTypeDetails.getOccupancyDetails().getNumOfRooms());
            }

            rateData.setRoomDetails(new ArrayList<>());
            for(Map.Entry<String,RoomType> roomTypeEntry : roomTypeDetails.getRoomType().entrySet()) {


                for(Map.Entry<String,RatePlan> ratePlanEntry : roomTypeEntry.getValue().getRatePlanList().entrySet()) {
                    String roomCode = roomTypeEntry.getKey();
                    String rateplanCode = ratePlanEntry.getKey();
                    RatePlan ratePlan = ratePlanEntry.getValue();
                    ModifiedRoomDetails roomDetails = new ModifiedRoomDetails();
                    rateData.getRoomDetails().add(roomDetails);
                    if(Constants.PAGE_CONTEXT_DETAIL.equalsIgnoreCase(pageContext))
                        rateData.setHashKey(ratePlan.getMtKey());
                    rateData.setNewPaymentMode(ratePlan.getPaymentDetails().getPaymentMode().getMappedPayMode());
                    if(!isPricingSet){
                        isPricingSet = true;
                        if(ratePlan.getCorpMetaData() != null)
                             rateData.setCorpPolicyDetails(buildCorpPolicyDetails(ratePlan.getCorpMetaData()));
                        rateData.setFinalPayAmount(getFinalPrice(ratePlan.getDisplayFare().getDisplayPriceBreakDown(), pageContext,commonModifierResponse));
                        if(ratePlan.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo() != null) {
                            rateData.setPromoCode(ratePlan.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getCouponCode());
                            rateData.setPromoText(ratePlan.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getDescription());
                        }
                    }
                    if(!isOccSet){
                        isOccSet = true;
                        rateData.setAdultCount(ratePlan.getAvailDetails().getOccupancyDetails().getAdult());
                        rateData.setChildCount(ratePlan.getAvailDetails().getOccupancyDetails().getChild());
                        rateData.setTotalRooms(ratePlan.getAvailDetails().getOccupancyDetails().getNumOfRooms());
                    }
                    roomDetails.setRoomCode(roomCode);
                    roomDetails.setRoomStayCandidates(buildRSC(ratePlan.getRoomTariff()));
                    roomDetails.setCancellationTillDate(ratePlan.getCancelPenaltyList().get(0).getTillDate());
                    if(CollectionUtils.isNotEmpty(ratePlan.getInclusions()))
                        roomDetails.setInclusions(ratePlan.getInclusions().stream().map(Inclusion::getCode).collect(Collectors.toList()));
                    roomDetails.setRoomImageUrl(CollectionUtils.isNotEmpty(roomTypeEntry.getValue().getRoomImages()) ? roomTypeEntry.getValue().getRoomImages().get(0): null);
                    roomDetails.setCancelPenaltyDesc(ratePlan.getCancelPenaltyList().get(0).getPenaltyDescription().getDescription());
                    roomDetails.setMealPlanCode(ratePlan.getMealPlans().get(0).getCode());
                    roomDetails.setMealPlanValue(ratePlan.getMealPlans().get(0).getValue());
                    roomDetails.setRateplanCode(rateplanCode);
                    roomDetails.setRoomTypeName(roomTypeEntry.getValue().getRoomTypeName());
                    rateData.setFCtoNR(!(ratePlan.getCancelPenaltyList().get(0).getCancellationType() != null &&
                            ratePlan.getCancelPenaltyList().get(0).getCancellationType() ==  CancelPenalty.CancellationType.FREE_CANCELLATON));

                    if (ratePlan.getBnplVariant() != null && (ratePlan.getBnplVariant().equals(BNPLVariant.BNPL_AT_0) || ratePlan.getBnplVariant().equals(BNPLVariant.BNPL_AT_1))){
                        rateData.setBnplVariant(ratePlan.getBnplVariant());
                        CancellationTimeline cancellationTimeline = ratePlan.getCancellationTimeline();
                        rateData.setPaymentChargeDate(cancellationTimeline.getCardChargeDateLong());
                    }
                    else
                        rateData.setBnplVariant(BNPLVariant.BNPL_NOT_APPLICABLE);
                }
            }
        }
    }

    private List<ModifiedRoomStayCandidate> buildRSC(List<RoomTariff> roomTariffList){
        List<ModifiedRoomStayCandidate> modRscList = new ArrayList<>();
        for(RoomTariff roomTariff: roomTariffList){
            ModifiedRoomStayCandidate modRsc = new ModifiedRoomStayCandidate();
            modRsc.setGuestCountList(new ArrayList<>());
            ModifiedGuestCount modGC = new ModifiedGuestCount();
            modRsc.getGuestCountList().add(modGC);
            modGC.setAdultCount(roomTariff.getNumberOfAdults());
            modGC.setChildAges(roomTariff.getChildAges());
            modRscList.add(modRsc);
        }

        return modRscList;
    }

    private RateData buildRateData(RoomDetailsResponse roomDetailsResponse,String pageContext,CommonModifierResponse commonModifierResponse){
        RateData rateData = null;
        if(CollectionUtils.isNotEmpty(roomDetailsResponse.getHotelRates())) {
            HotelRates hotelRates = roomDetailsResponse.getHotelRates().get(0);
            rateData = new RateData();
            rateData.setAbsorptionDetails(buildAbsorptionDetails(hotelRates.getAlerts(), roomDetailsResponse.getHotelRates().get(0).isCouponValidated(),roomDetailsResponse.getHotelRates().get(0).isSameRoomModification(),roomDetailsResponse.getHotelRates().get(0).isSamePayModeModification()));
            rateData.setPenaltyDetails(buildPenaltyDetails(hotelRates.getCancelPreviewResponse()));
            if(hotelRates.getCancelPreviewResponse() != null) {
                rateData.setNonRefundable(hotelRates.getCancelPreviewResponse().isNonRefundable());
            }
            if(Constants.PAGE_CONTEXT_REVIEW.equalsIgnoreCase(pageContext))
                rateData.setHashKey(roomDetailsResponse.getTxnKey());
            if(hotelRates.getRoomTypeDetails() != null)
                 buildRoomDetails(rateData,hotelRates.getRoomTypeDetails(),pageContext,commonModifierResponse);
            else if (hotelRates.getRecommendedRoomTypeDetails() != null)
                buildRoomDetails(rateData,hotelRates.getRecommendedRoomTypeDetails(),pageContext,commonModifierResponse);
            else if (CollectionUtils.isNotEmpty(hotelRates.getOtherRecommendedRooms()))
                buildRoomDetails(rateData,hotelRates.getOtherRecommendedRooms().get(0),pageContext,commonModifierResponse);
        }
        return rateData;
    }

    private ModificationPenaltyDetails buildPenaltyDetails(CancelPreviewResponse prevResp ){
        ModificationPenaltyDetails modCanPenalty = null ;
        if(prevResp != null ) {
            modCanPenalty = new ModificationPenaltyDetails();
            modCanPenalty.setAddOnMarkup(prevResp.getAddOnMarkup());
            modCanPenalty.setAffiliateFee(prevResp.getAffiliateFee());
            modCanPenalty.setFlatPenalty(prevResp.getFlatPenalty());
            modCanPenalty.setCommission(prevResp.getCommission());
            modCanPenalty.setBaseFare(prevResp.getBaseFare());
            modCanPenalty.setCustomerCgst(prevResp.getCustomerCgst());
            modCanPenalty.setCustomerIgst(prevResp.getCustomerIgst());
            modCanPenalty.setCustomerSgst(prevResp.getCustomerSgst());
            modCanPenalty.setCustomerWaiverAmount(prevResp.getCustomerWaiverAmount());
            modCanPenalty.setEcommerceCgstTax(prevResp.getEcommerceCgstTax());
            modCanPenalty.setEcommerceSgstTax(prevResp.getEcommerceSgstTax());
            modCanPenalty.setHotelierServiceCharge(prevResp.getHotelierServiceCharge());
            modCanPenalty.setHotelWaiverAmount(prevResp.getHotelWaiverAmount());
            modCanPenalty.setMmtDiscount(prevResp.getMmtDiscount());
            modCanPenalty.setProfit(prevResp.getProfit());
            modCanPenalty.setProfitCgst(prevResp.getProfitCgst());
            modCanPenalty.setProfitSgst(prevResp.getProfitSgst());
            modCanPenalty.setPromoMarkup(prevResp.getPromoMarkup());
            modCanPenalty.setTax(prevResp.getTax());
            modCanPenalty.setTotalCancellationAmount(prevResp.getTotalCancellationAmount());
            modCanPenalty.setVendorCgst(prevResp.getVendorCgst());
            modCanPenalty.setVendorIgst(prevResp.getVendorIgst());
            modCanPenalty.setVendorSgst(prevResp.getVendorSgst());
            if(prevResp.getModificationRule() != null) {
                modCanPenalty.setNrToFcChanged(prevResp.getModificationRule().isNrToFcChange());
                if(prevResp.getModificationRule().getPenaltyOldBooking()!= null)
                    modCanPenalty.setPenaltyOldBooking(prevResp.getModificationRule().getPenaltyOldBooking().toString());
            }
            modCanPenalty.setPgCharges(prevResp.getPgCharges());
            modCanPenalty.setPenaltyPercent(prevResp.getPenaltyPercent());
        }

        return modCanPenalty;
    }
}























