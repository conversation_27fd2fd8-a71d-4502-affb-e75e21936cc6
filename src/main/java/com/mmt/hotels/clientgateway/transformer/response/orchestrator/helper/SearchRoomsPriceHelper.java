package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.enums.MarkUpType;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.gommt.hotels.orchestrator.detail.model.response.common.MarkUp;
import com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.LinkedRate;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.MarkUpDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.NoCostEmiDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceCouponInfo;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail;
import com.ibm.icu.text.NumberFormat;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.response.Coupon;
import com.mmt.hotels.clientgateway.response.CouponPersuasion;
import com.mmt.hotels.clientgateway.response.EMIPlanDetail;
import com.mmt.hotels.clientgateway.response.PricingDetails;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.emi.EmiConfigDetails;
import com.mmt.hotels.clientgateway.response.emi.EmiPlanType;
import com.mmt.hotels.clientgateway.response.emi.EmiTagDetails;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.BgGradient;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.util.Currency;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.pricing.DiscountPersuasionInfo;
import com.mmt.hotels.model.response.searchwrapper.MmtHotelCategory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.mmt.hotels.clientgateway.constants.Constants.ANDROID;
import static com.mmt.hotels.clientgateway.constants.Constants.B2C;
import static com.mmt.hotels.clientgateway.constants.Constants.BUDGET_CONTEXT;
import static com.mmt.hotels.clientgateway.constants.Constants.DEVICE_IOS;
import static com.mmt.hotels.clientgateway.constants.Constants.EXTRA_DISCOUNT_TYPE_FIRST_FIVE_BOOKING;
import static com.mmt.hotels.clientgateway.constants.Constants.GEC;
import static com.mmt.hotels.clientgateway.constants.Constants.GROUP_FUNNEL_ENHANCEMENT_EXP;
import static com.mmt.hotels.clientgateway.constants.Constants.LINKEDRATE_FCNR;
import static com.mmt.hotels.clientgateway.constants.Constants.LUXURY_HOTELS;
import static com.mmt.hotels.clientgateway.constants.Constants.NEW_SELECT_ROOM_PAGE;
import static com.mmt.hotels.clientgateway.constants.Constants.PREMIUM_CONTEXT;
import static com.mmt.hotels.clientgateway.constants.Constants.Per_Night;
import static com.mmt.hotels.clientgateway.constants.Constants.ihCashbackSectionExp;
import static com.mmt.hotels.clientgateway.constants.Constants.per_night;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.GIFT_CARD_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.LINKED_RATE_PLAN_BOTTOMSHEET_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.LINKED_RATE_PLAN_DISCOUNT_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.LINKED_RATE_PLAN_ORIGINAL_PRICE_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.NO_COST_EMI_TAG;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SAVING_PERC_TEXT;

@Component
public class SearchRoomsPriceHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(SearchRoomsPriceHelper.class);

    @Autowired
    CommonResponseTransformer commonResponseTransformer;

    @Autowired
    PolyglotService polyglotService;

    @Autowired
    PricingEngineHelper pricingEngineHelper;

    @Autowired
    Utility utility;

    @Value("${bank.coupon.generic.icon}")
    private String genericBankIcon;

    @Value("#{'${corp.segments}'.split(',')}")
    private Set<String> corpSegments;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    private BgGradient noCostEmiIconConfig;

    private NumberFormat numberFormatter;

    @PostConstruct
    void init() {
        noCostEmiIconConfig = commonConfigConsul.getNoCostEmiIconConfig();
        numberFormatter = NumberFormat.getNumberInstance(new Locale("en", "IN"));
        numberFormatter.setMaximumFractionDigits(0); // No decimals
        numberFormatter.setMinimumFractionDigits(0); // Ensure no trailing zeros
    }

    /**
     * New method to build price map from orchestrator v2 PriceDetail
     * Works directly with PriceDetail without converting to DisplayPriceBreakDown
     */
    public Map<String, TotalPricing> getPriceMap(PriceDetail priceDetail,
                                                 String expData, OccupancyDetails occupancyDetails, String askedCurrency,
                                                 String sellableType, Integer nightCount,
                                                 boolean isCorp, String segmentId, boolean buildToolTip, boolean groupBookingFunnel,
                                                 boolean groupBookingPrice, boolean ismyPartnerRequest, boolean isAltAccoHotel,
                                                 final MarkUpDetails markUpDetails, NoCostEmiDetails noCostEmiDetailForRootLevel,
                                                 List<LinkedRate> linkedRates, boolean isNewPropertyOfferApplicable, boolean isHighSellingAltAcco) {
        if (priceDetail == null) {
            return null;
        }

        Integer roomCount = occupancyDetails != null && occupancyDetails.getNumberOfRooms() > 0 ? occupancyDetails.getNumberOfRooms() : null;

        String priceDisplayMessage = (null == roomCount) ? polyglotService.getTranslatedData(ConstantsTranslation.PER_NIGHT_TEXT) : commonResponseTransformer.getPriceDisplayMessage(expData, roomCount, sellableType, nightCount,
                groupBookingFunnel, isAltAccoHotel, isHighSellingAltAcco);
        Map<String, String> expDataMap = utility.getExpDataMap(expData);
        boolean isNewSelectRoomPage = utility.isExperimentTrue(expDataMap, NEW_SELECT_ROOM_PAGE);
        String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
        String country = MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue());
        if (isNewSelectRoomPage && Per_Night.equalsIgnoreCase(priceDisplayMessage) && (client.equalsIgnoreCase(ANDROID) || client.equalsIgnoreCase(DEVICE_IOS))) {
            priceDisplayMessage = per_night;
        }
        Map<String, TotalPricing> priceMap = new HashMap<>();
        String priceMapKey;
        boolean groupFunnelEnhancement = utility.isExperimentTrue(expDataMap, GROUP_FUNNEL_ENHANCEMENT_EXP);

        // Build price map directly from orchestrator v2 PriceDetail
        if (StringUtils.isNotBlank(priceDetail.getCouponCode())) {
            priceMapKey = priceDetail.getCouponCode();
        } else {
            priceMapKey = "DEFAULT";
        }

        // Build TotalPricing directly from PriceDetail
        TotalPricing totalPricing = buildTotalPricingFromPriceDetail(priceDetail, isCorp, segmentId, expDataMap, groupBookingFunnel, markUpDetails, askedCurrency, isNewPropertyOfferApplicable, priceDetail.getCouponCode());
        if (occupancyDetails != null && StringUtils.isNotEmpty(occupancyDetails.getPricingKey())) {
            totalPricing.setPricingKey(occupancyDetails.getPricingKey());
        }

        // Set coupon description and amount if available
        if (StringUtils.isNotBlank(priceDetail.getCouponCode()) && CollectionUtils.isNotEmpty(priceDetail.getApplicableCoupons())) {
            com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceCouponInfo appliedCoupon = priceDetail.getApplicableCoupons().stream()
                    .filter(coupon -> priceDetail.getCouponCode().equals(coupon.getCouponCode()))
                    .findFirst()
                    .orElse(null);
            if (appliedCoupon != null) {
                totalPricing.setCouponDesc(appliedCoupon.getDescription());
                totalPricing.setCouponAmount(appliedCoupon.getDiscount());
                totalPricing.setEmiPlanDetail(buildEmiPlanDetails(appliedCoupon.getNoCostEmiDetails()));
                buildNoCostEmiDetailsForRootLevel(appliedCoupon.getNoCostEmiDetails(), noCostEmiDetailForRootLevel);
            }
        }

        // Set price display message
        totalPricing.setPriceDisplayMsg(priceDisplayMessage);

        // Set tax message
        totalPricing.setPriceTaxMsg(commonResponseTransformer.getShowTaxMessage(expData, roomCount, priceDetail.getTotalTax(), askedCurrency));


        // Build coupon persuasion if available
        if (totalPricing.getCouponPersuasion() == null && priceDetail.getExtraDiscount() != null && priceDetail.getDiscount() != null) {
            String extraDiscountType = "FBPHostNMMT".equalsIgnoreCase(priceDetail.getExtraDiscount().getType()) ?
                    EXTRA_DISCOUNT_TYPE_FIRST_FIVE_BOOKING : priceDetail.getExtraDiscount().getType();
            DiscountPersuasionInfo discountPersuasionInfo = new DiscountPersuasionInfo();
            discountPersuasionInfo.setDiscount(priceDetail.getExtraDiscount().getDiscount());
            discountPersuasionInfo.setBookingCount(priceDetail.getExtraDiscount().getBookingCount());
            discountPersuasionInfo.setType(priceDetail.getExtraDiscount().getType());

            CouponPersuasion couponPersuasion = commonResponseTransformer.buildCouponPersuasion(extraDiscountType, priceDetail.getDiscount().getCoupon(), discountPersuasionInfo, isNewPropertyOfferApplicable);
            if (couponPersuasion != null) {
                totalPricing.setCouponPersuasion(couponPersuasion);
            }
        }

        // Build price tooltip if required
        if (buildToolTip) {
            totalPricing.setPriceToolTip(buildPriceToolTipFromPriceDetail(priceDetail, nightCount, askedCurrency));
        }

        // Set group price and savings text
        long savingPerc = (long) calculateSavingPercentage(priceDetail);
        long displayPrice = (long) priceDetail.getDisplayPrice();
        setGroupPriceAndSavingsText(totalPricing, roomCount, nightCount, savingPerc, displayPrice, groupBookingFunnel, groupBookingPrice, ismyPartnerRequest, groupFunnelEnhancement);

        priceMap.put(priceMapKey, totalPricing);


        // Build linked rates persuasions
        buildLinkedRatesPersuasionsFromPriceDetail(priceMapKey, priceMap, priceDetail, askedCurrency, linkedRates);

        // Process applicable coupons (similar to displayPriceBreakDownList)
        if (CollectionUtils.isNotEmpty(priceDetail.getApplicableCoupons())) {
            for (com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceCouponInfo couponInfo : priceDetail.getApplicableCoupons()) {
                if (couponInfo == null || StringUtils.isBlank(couponInfo.getCouponCode()) ||
                        couponInfo.getCouponCode().equals(priceDetail.getCouponCode())) {
                    continue; // Skip if already processed as main coupon
                }

                // Build TotalPricing for this coupon using same priceDetail but different coupon
                TotalPricing couponTotalPricing = buildTotalPricingFromPriceDetail(priceDetail, isCorp, segmentId, expDataMap,
                        groupBookingFunnel, markUpDetails, askedCurrency, isNewPropertyOfferApplicable, couponInfo.getCouponCode());
                if (occupancyDetails != null && StringUtils.isNotEmpty(occupancyDetails.getPricingKey())) {
                    couponTotalPricing.setPricingKey(occupancyDetails.getPricingKey());
                }

                // Override with coupon specific values
                couponTotalPricing.setCouponDesc(couponInfo.getDescription());
                couponTotalPricing.setCouponAmount(couponInfo.getDiscount());
                couponTotalPricing.setPricingKey(couponInfo.getCouponCode());
                couponTotalPricing.setPriceDisplayMsg(priceDisplayMessage);
                couponTotalPricing.setPriceTaxMsg(commonResponseTransformer.getShowTaxMessage(expData, roomCount, priceDetail.getTotalTax(), askedCurrency));


                // Build coupon persuasion for this coupon
                if (priceDetail.getExtraDiscount() != null) {
                    String extraDiscountType = "FBPHostNMMT".equalsIgnoreCase(priceDetail.getExtraDiscount().getType()) ?
                            EXTRA_DISCOUNT_TYPE_FIRST_FIVE_BOOKING : priceDetail.getExtraDiscount().getType();
                    DiscountPersuasionInfo discountPersuasionInfo = new DiscountPersuasionInfo();
                    discountPersuasionInfo.setDiscount(priceDetail.getExtraDiscount().getDiscount());
                    discountPersuasionInfo.setBookingCount(priceDetail.getExtraDiscount().getBookingCount());
                    discountPersuasionInfo.setType(priceDetail.getExtraDiscount().getType());

                    CouponPersuasion couponPersuasion = commonResponseTransformer.buildCouponPersuasion(extraDiscountType, couponInfo.getDiscount(), discountPersuasionInfo, isNewPropertyOfferApplicable);
                    if (couponPersuasion != null) {
                        couponTotalPricing.setCouponPersuasion(couponPersuasion);
                    }
                }
                couponTotalPricing.setEmiPlanDetail(buildEmiPlanDetails(couponInfo.getNoCostEmiDetails()));
                buildNoCostEmiDetailsForRootLevel(couponInfo.getNoCostEmiDetails(), noCostEmiDetailForRootLevel);

                priceMap.put(couponInfo.getCouponCode(), couponTotalPricing);
                setGroupPriceAndSavingsText(couponTotalPricing, roomCount, nightCount, savingPerc, displayPrice, groupBookingFunnel, groupBookingPrice, ismyPartnerRequest, groupFunnelEnhancement);
                buildLinkedRatesPersuasionsFromPriceDetail(couponInfo.getCouponCode(), priceMap, priceDetail, askedCurrency, linkedRates);
            }
        }

        return priceMap;
    }


    /**
     * Build price tooltip from OrchV2 PriceDetail - equivalent to buildPriceToolTip for DisplayPriceBreakDown
     */
    public String buildPriceToolTipFromPriceDetail(com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail, Integer nightCount, String askedCurrency) {
        if (priceDetail == null || nightCount == null || nightCount <= 0 || priceDetail.getDiscount().getLongStay() <= 0.0d) {
            return null;
        }

        double totalAmount = (priceDetail.getDisplayPrice() + (priceDetail.isTaxIncluded() ? 0 : priceDetail.getTotalTax())) * priceDetail.getPricingDivisor();
        totalAmount = Utility.round(totalAmount, 0);

        return (polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TOOL_TIP))
                .replace(ConstantsTranslation.DAYS, nightCount.toString())
                .replace(ConstantsTranslation.AMOUNT, String.valueOf(totalAmount))
                .replace(ConstantsTranslation.CURRENCY, askedCurrency);
    }

    /**
     * Build linked rates persuasions from OrchV2 PriceDetail - equivalent to buildLinkedRatesPersuasions for DisplayPriceBreakDown
     */
    private void buildLinkedRatesPersuasionsFromPriceDetail(String priceMapKey, Map<String, TotalPricing> priceMap,
                                                            com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail, String askedCurrency, List<LinkedRate> linkedRates) {
        try {
            if (linkedRates == null || linkedRates.isEmpty()) {
                return;
            }

            for (LinkedRate linkedRate : linkedRates) {
                com.gommt.hotels.orchestrator.detail.model.response.pricing.LinkedRatePriceCalculations linkedRatePriceCalculations;

                if (LINKEDRATE_FCNR.equalsIgnoreCase(linkedRate.getType())) {
                    if (MapUtils.isNotEmpty(priceDetail.getLinkedRatePriceCalculationsMap())) {
                        if (priceDetail.getLinkedRatePriceCalculationsMap().containsKey(linkedRate.getPricingKey())) {
                            linkedRatePriceCalculations = priceDetail.getLinkedRatePriceCalculationsMap().get(linkedRate.getPricingKey());
                        } else {
                            continue;
                        }
                        if (Objects.nonNull(linkedRatePriceCalculations)) {
                            String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : "INR").getCurrencySymbol();
                            String discountedPrice = utility.convertNumericValueToCommaSeparatedString(linkedRatePriceCalculations.getDisplayPriceDifference(), Locale.ENGLISH);
                            String parentOriginalPrice = utility.convertNumericValueToCommaSeparatedString(linkedRatePriceCalculations.getParentOriginalPrice(), Locale.ENGLISH);
                            TotalPricing totalPricing = priceMap.get(priceMapKey);
                            totalPricing.setLinkedRPBottomSheetTitle(polyglotService.getTranslatedData(LINKED_RATE_PLAN_BOTTOMSHEET_TITLE).replace("{discount}", discountedPrice).replace("{currency}", currencySymbol));
                            totalPricing.setLinkedRPDiscountMsg(polyglotService.getTranslatedData(LINKED_RATE_PLAN_DISCOUNT_TEXT).replace("{discount}", discountedPrice).replace("{currency}", currencySymbol));
                            totalPricing.setLinkedRPOriginalPriceMsg(polyglotService.getTranslatedData(LINKED_RATE_PLAN_ORIGINAL_PRICE_TEXT).replace("{parentOriginalPrice}", parentOriginalPrice).replace("{currency}", currencySymbol));
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error in building linked rate persuasions from PriceDetail", e);
        }
    }

    private void buildBaseFare(com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail, List<PricingDetails> pricingDetailsList) {
        if (priceDetail.getBasePrice() > 0.0d) {
            PricingDetails baseFare = new PricingDetails();
            baseFare.setAmount(priceDetail.getBasePrice());
            baseFare.setKey(Constants.BASE_FARE_KEY);
            baseFare.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.BASE_FARE_LABEL));
            baseFare.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
            pricingDetailsList.add(baseFare);
        }
    }

    private void buildTotalDiscounts(com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail, List<PricingDetails> pricingDetailsList, PriceCouponInfo priceCouponInfo,
                                     String segmentId, boolean corp) {
        double totalDiscountAmount = 0.0d;
        List<PricingDetails> priceBreakup = new ArrayList<>();
        if (priceDetail.getDiscount().getSupplier() > 0.0) {
            PricingDetails supplierDiscount = new PricingDetails();
            supplierDiscount.setAmount(priceDetail.getDiscount().getSupplier());
            supplierDiscount.setLabel(polyglotService.getTranslatedData(priceDetail.getDiscount().getSupplierDiscountType().concat("_DISC_LABEL")));
            supplierDiscount.setKey(priceDetail.getDiscount().getSupplierDiscountType());
            supplierDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
            priceBreakup.add(supplierDiscount);
            totalDiscountAmount += supplierDiscount.getAmount();
        }

        /* Build OfferDetailMap from Pricer breakup if available */
        if (priceDetail.getDiscount() != null && priceDetail.getDiscount().getMmt() > 0.0d) {
            PricingDetails mmtDiscount = new PricingDetails();
            mmtDiscount.setAmount(priceDetail.getDiscount().getMmt());
            mmtDiscount.setKey(Constants.MMT_DISCOUNT_KEY);
            mmtDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.HOTELIER_DISCOUNT_LABEL));
            if (corp && corpSegments.contains(segmentId)) {
                mmtDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.HOTELIER_DISCOUNT_LABEL_CORP));
            }
            mmtDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
            priceBreakup.add(mmtDiscount);
            totalDiscountAmount += mmtDiscount.getAmount();
        }

        if (priceDetail.getDiscount() != null && priceDetail.getDiscount().getBlack() > 0.0d) {
            PricingDetails blackDiscount = new PricingDetails();
            blackDiscount.setAmount(priceDetail.getDiscount().getBlack());
            blackDiscount.setKey(Constants.BLACK_DISCOUNT_KEY);
            // For GCC, MMT_SELECT Program runs hence picking Select Label in this case
            // For IN, MMT_BLACK Program runs hence picking Black label in this case
            blackDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.GCC_SELECT_DISCOUNT_LABEL));
            blackDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
            priceBreakup.add(blackDiscount);
            totalDiscountAmount += blackDiscount.getAmount();
        }

        if (priceDetail.getDiscount() != null && priceDetail.getDiscount().getLongStay() > 0.0d) {
            PricingDetails blackDiscount = new PricingDetails();
            blackDiscount.setAmount(priceDetail.getDiscount().getLongStay());
            blackDiscount.setKey(Constants.LONGSTAY_BENEFITS);
            blackDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.LOS_DISCOUNT_LABEL));
            blackDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
            priceBreakup.add(blackDiscount);
            totalDiscountAmount += blackDiscount.getAmount();
        }

        if (priceCouponInfo != null) {
            PricingDetails cdfDiscount = new PricingDetails();
            cdfDiscount.setAmount(priceCouponInfo.getDiscount());
            cdfDiscount.setKey(Constants.CDF_DISCOUNT_KEY);
            cdfDiscount.setLabel(priceCouponInfo.getCouponCode() != null ? priceCouponInfo.getCouponCode() : polyglotService.getTranslatedData(ConstantsTranslation.CDF_DISCOUNT_LABEL));
            cdfDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));

            if (priceDetail.getTaxBreakUp() != null && priceDetail.getTaxBreakUp().getServiceFee() > 0.0d && priceCouponInfo.getDiscount() > priceDetail.getTaxBreakUp().getServiceFee()) {
                List<PricingDetails> cdfCouponBreakup = new ArrayList<>();
                PricingDetails serviceFeeReversal = new PricingDetails();
                serviceFeeReversal.setKey(Constants.SERVICE_FEES_REVERSAL_KEY);
                serviceFeeReversal.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.SERVICE_FEES_REVERSAL_LABLE));
                serviceFeeReversal.setAmount(priceDetail.getTaxBreakUp().getServiceFee());
                serviceFeeReversal.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
                cdfCouponBreakup.add(serviceFeeReversal);
                PricingDetails effectiveCouponApplied = new PricingDetails();
                effectiveCouponApplied.setKey(Constants.EFFECTIVE_COUPON_APPLIED_KEY);
                effectiveCouponApplied.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.EFFECTIVE_COUPON_APPLIED_LABLE));
                effectiveCouponApplied.setAmount(priceCouponInfo.getDiscount() - priceDetail.getTaxBreakUp().getServiceFee());
                effectiveCouponApplied.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
                cdfCouponBreakup.add(effectiveCouponApplied);
                cdfDiscount.setBreakup(cdfCouponBreakup);
            }
            priceBreakup.add(cdfDiscount);
            totalDiscountAmount += cdfDiscount.getAmount();
        }

        if (totalDiscountAmount > 0.0d) {
            PricingDetails totalDiscount = new PricingDetails();
            totalDiscount.setAmount(totalDiscountAmount);
            totalDiscount.setKey(Constants.TOTAL_DISCOUNT_KEY);
            totalDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.TOTAL_DISCOUNT_LABEL));
            totalDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_DIFF));
            totalDiscount.setBreakup(priceBreakup);
            pricingDetailsList.add(totalDiscount);
        }
    }

    private void buildTotalAmount(com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail, List<PricingDetails> pricingDetailsList, PriceCouponInfo couponInfo) {
        PricingDetails totalAmount = new PricingDetails();
        totalAmount.setAmount(priceDetail.getDisplayPrice() + priceDetail.getDiscount().getCoupon() - (couponInfo != null ? couponInfo.getDiscount() : 0.0));
        totalAmount.setKey(Constants.TOTAL_AMOUNT_KEY);
        totalAmount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
        totalAmount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.TOTAL_AMOUNT_LABEL));
        pricingDetailsList.add(totalAmount);
    }

    private void buildPriceAfterDiscount(com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail, List<PricingDetails> pricingDetailsList, PriceCouponInfo couponInfo) {
        if (priceDetail.getTotalDiscount() > 0.0d || (couponInfo != null && couponInfo.getDiscount() > 0.0d)) {
            PricingDetails priceAfterDiscount = new PricingDetails();
            priceAfterDiscount.setAmount(priceDetail.getDisplayPrice() + priceDetail.getDiscount().getCoupon() - (couponInfo != null ? couponInfo.getDiscount() : 0.0d) - (priceDetail.isTaxIncluded() ? priceDetail.getTotalTax() : 0d));
            priceAfterDiscount.setKey(Constants.PRICE_AFTER_DISCOUNT_KEY);
            priceAfterDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_AFTER_DISCOUNT_LABEL));
            priceAfterDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
            pricingDetailsList.add(priceAfterDiscount);
        }
    }

    private void buildTaxesAndServiceFee(com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail, List<PricingDetails> pricingDetailsList, Map<String, String> expDataMap) {
        String countryCode = MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue());
        List<PricingDetails> priceBreakup = new ArrayList<>();
        double totalTaxesAndSrvcFee = 0.0d;
        if (priceDetail.getTaxBreakUp() != null) {
            double hotelTaxAmount = priceDetail.getTaxBreakUp().getHotelTax();
            if (priceDetail.getTaxBreakUp().getHotelServiceCharge() > 0.0d) {
                hotelTaxAmount -= priceDetail.getTaxBreakUp().getHotelServiceCharge();
                PricingDetails serviceCharge = new PricingDetails();
                serviceCharge.setAmount(priceDetail.getTaxBreakUp().getHotelServiceCharge());
                serviceCharge.setKey(Constants.SERVICE_CHARGE_KEY);
                serviceCharge.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.SERVICE_CHARGE_LABEL));
                serviceCharge.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
                priceBreakup.add(serviceCharge);
                totalTaxesAndSrvcFee += serviceCharge.getAmount();
            }
            if (priceDetail.getTaxBreakUp().getHotelTax() > 0.0d) {
                PricingDetails hotelTax = new PricingDetails();
                hotelTax.setAmount(hotelTaxAmount);
                hotelTax.setKey(Constants.HOTEL_TAX_KEY);
                hotelTax.setLabel(Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode) ? polyglotService.getTranslatedData(ConstantsTranslation.GST_LABEL) : polyglotService.getTranslatedData(ConstantsTranslation.HOTEL_TAX_LABEL));
                hotelTax.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
                priceBreakup.add(hotelTax);
                totalTaxesAndSrvcFee += hotelTax.getAmount();
            }
            if (priceDetail.getTaxBreakUp().getServiceFee() > 0.0d) {
                PricingDetails serviceFee = new PricingDetails();
                serviceFee.setAmount(priceDetail.getTaxBreakUp().getServiceFee());
                serviceFee.setKey(Constants.SERVICE_FEES_KEY);
                serviceFee.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.SERVICE_FEES_LABEL));
                serviceFee.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
                priceBreakup.add(serviceFee);
                totalTaxesAndSrvcFee += serviceFee.getAmount();
            }
        }
        if (totalTaxesAndSrvcFee > 0.0d) {
            PricingDetails taxesAndSrvcFee = new PricingDetails();
            taxesAndSrvcFee.setAmount(totalTaxesAndSrvcFee);
            taxesAndSrvcFee.setKey(Constants.TAXES_KEY);
            //Excluded Charges text to be shown above taxes for GCC on review page
            if (Utility.isGCC() && utility.isExperimentOn(expDataMap, GEC)) {
                taxesAndSrvcFee.setSubLine(polyglotService.getTranslatedData(ConstantsTranslation.TAXES_EXCLUDED_TEXT_REVIEW_PAGE));
            }
            if (priceDetail.getTaxBreakUp().getHotelTax() > 0.0d || priceDetail.getTaxBreakUp().getServiceFee() > 0.0d) {
                taxesAndSrvcFee.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.TAXES_LISTING_DETAIL_LABEL));
            }
            taxesAndSrvcFee.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
            taxesAndSrvcFee.setBreakup(priceBreakup);
            pricingDetailsList.add(taxesAndSrvcFee);
        }
    }

    /**
     * Build TotalPricing directly from orchestrator v2 PriceDetail
     */
    private TotalPricing buildTotalPricingFromPriceDetail(com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail,
                                                          boolean isCorp, String segmentId, Map<String, String> expDataMap,
                                                          boolean groupBookingFunnel, final MarkUpDetails markUpDetails,
                                                          String currency, boolean isNewPropertyOfferApplicable, String couponCode) {
        TotalPricing totalPricing = new TotalPricing();
        boolean isIhCashbackSectionEnable = utility.isExperimentTrue(expDataMap, ihCashbackSectionExp);
        int ancillaryVariant = MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(ExperimentKeys.ANCILLARY_DISPLAY_PRICE_VARIANT.getKey()) ? Integer.parseInt(expDataMap.get(ExperimentKeys.ANCILLARY_DISPLAY_PRICE_VARIANT.getKey())) : 0;
        boolean showReviewOffersCategory = utility.showReviewOffersCategory(expDataMap);
        boolean myPartnerMoveToTdsTaxStructure = utility.isExperimentTrue(expDataMap, Constants.MY_PARTNER_MOVE_TO_TDS_TAX_STRUCTURE);

        // Build pricing details list directly from PriceDetail
        List<PricingDetails> pricingDetailsList = new ArrayList<>();
        // Add base fare if available
        if (priceDetail.getBasePrice() > 0.0d) {
            buildBaseFare(priceDetail, pricingDetailsList);
        }

        // Add discount if available
        PriceCouponInfo priceCouponInfo = CollectionUtils.isNotEmpty(priceDetail.getApplicableCoupons()) && StringUtils.isNotEmpty(couponCode) ?
                priceDetail.getApplicableCoupons().stream().filter(coupon -> couponCode.equalsIgnoreCase(coupon.getCouponCode())).findFirst().orElse(null) : null;
        if (!myPartnerMoveToTdsTaxStructure) {
            buildTotalDiscounts(priceDetail, pricingDetailsList, priceCouponInfo, segmentId, isCorp);
            buildPriceAfterDiscount(priceDetail, pricingDetailsList, priceCouponInfo);
        }

        buildTaxesAndServiceFee(priceDetail, pricingDetailsList, expDataMap);
        buildTotalAmount(priceDetail, pricingDetailsList, priceCouponInfo);
        totalPricing.setDetails(pricingDetailsList);

        // Set currency (TotalPricing only has setCurrency, not setCurrencyCode/Symbol)
        if (StringUtils.isNotBlank(currency)) {
            totalPricing.setCurrency(currency);
        }

        // Apply markup if myPartner request and markUpDetails available
        if (markUpDetails != null && priceDetail.getDisplayPrice() > 0.0d) {
            totalPricing.setPartnerDetails(commonResponseTransformer.getPartnerDetails(totalPricing.getDetails(), getMarkUpForHotels(markUpDetails, priceDetail.getDisplayPrice())));
        }

        // Build and set coupons list from PriceDetail
        if (CollectionUtils.isNotEmpty(priceDetail.getApplicableCoupons())) {
            List<Coupon> coupons = buildCouponsFromPriceDetail(priceDetail, isIhCashbackSectionEnable, ancillaryVariant, showReviewOffersCategory, couponCode);
            totalPricing.setCoupons(coupons);
        }

        // Set no coupon text if no coupons available
        if (CollectionUtils.isEmpty(totalPricing.getCoupons())) {
            totalPricing.setNoCouponText(polyglotService.getTranslatedData(ConstantsTranslation.NO_COUPON_AVAILABLE_TEXT));
        }

        // Set coupon amount if available
        if (priceDetail.getDiscount().getCoupon() > 0.0d) {
            totalPricing.setCouponAmount(priceDetail.getDiscount().getCoupon());
        }

        // Set coupon description if coupon code is available
        if (StringUtils.isNotBlank(priceDetail.getCouponCode()) && CollectionUtils.isNotEmpty(priceDetail.getApplicableCoupons())) {
            com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceCouponInfo appliedCoupon = priceDetail.getApplicableCoupons().stream()
                    .filter(coupon -> priceDetail.getCouponCode().equals(coupon.getCouponCode()))
                    .findFirst()
                    .orElse(null);
            if (appliedCoupon != null) {
                totalPricing.setCouponDesc(appliedCoupon.getDescription());
                totalPricing.setCouponAmount(appliedCoupon.getDiscount());
            }
        }

        // Set coupon subtext for B2C clients (similar to legacy method)
        if (B2C.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue())) && !Utility.isGccOrKsa()) {
            totalPricing.setCouponSubtext(polyglotService.getTranslatedData(GIFT_CARD_TEXT));
        }

        // Set EMI plan details from PriceDetail
		/*if (priceDetail.getEmiDetails() != null) {
			EMIPlanDetail emiPlanDetail = buildEmiPlanDetailsFromPriceDetail(priceDetail.getEmiDetails());
			if (emiPlanDetail != null) {
				totalPricing.setEmiPlanDetail(emiPlanDetail);
			}
		}*/

        // Handle coupon persuasion if discount persuasion info is available
        if (priceDetail.getDiscount().getCoupon() > 0.0d) {
            // Build coupon persuasion based on available info
            DiscountPersuasionInfo discountPersuasionInfo = new DiscountPersuasionInfo();
            String extraDiscountType = "";
            if (priceDetail.getExtraDiscount() != null) {
                discountPersuasionInfo.setDiscount(priceDetail.getExtraDiscount().getDiscount());
                discountPersuasionInfo.setBookingCount(priceDetail.getExtraDiscount().getBookingCount());
                discountPersuasionInfo.setType(priceDetail.getExtraDiscount().getType());
                extraDiscountType = "FBPHostNMMT".equalsIgnoreCase(priceDetail.getExtraDiscount().getType()) ? EXTRA_DISCOUNT_TYPE_FIRST_FIVE_BOOKING : priceDetail.getExtraDiscount().getType();
            }

            CouponPersuasion couponPersuasion = commonResponseTransformer.buildCouponPersuasion(extraDiscountType, priceDetail.getDiscount().getCoupon(), discountPersuasionInfo, isNewPropertyOfferApplicable);
            if (couponPersuasion != null) {
                totalPricing.setCouponPersuasion(couponPersuasion);
            }
        }

        // Set pricing key
        totalPricing.setPricingKey(priceDetail.getPricingKey());
        return totalPricing;
    }

    public double getMarkUpForHotels(final MarkUpDetails markUpDetails, final Double finalPriceWithoutTax) {
        final boolean isDomestic = Constants.DOMESTIC.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()));
        if (Objects.nonNull(finalPriceWithoutTax) && finalPriceWithoutTax > 0 && markUpDetails != null && markUpDetails.isMarkupEligible() && markUpDetails.getMarkupMap() != null) {
            final MarkUp markUp = isDomestic ? markUpDetails.getMarkupMap().get("DH") : markUpDetails.getMarkupMap().get("IH");
            if (Objects.nonNull(markUp))
                return MarkUpType.PERCENTAGE.equals(markUp.getType()) ? (markUp.getValue() * finalPriceWithoutTax * .01) : markUp.getValue();
        }
        return 0;
    }

    /**
     * Calculate saving percentage from PriceDetail
     */
    private double calculateSavingPercentage(com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail) {
        if (priceDetail.getBasePrice() > 0.0d && priceDetail.getTotalDiscount() > 0.0d) {
            return (priceDetail.getTotalDiscount() / priceDetail.getBasePrice()) * 100;
        }
        return 0.0;
    }

    /**
     * Build coupons list from OrchV2 PriceDetail
     */
    private List<Coupon> buildCouponsFromPriceDetail(com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail priceDetail,
                                                     boolean isIhCashbackSectionEnable, int ancillaryVariant, boolean showReviewOffersCategory, String couponCode) {
        List<Coupon> coupons = null;
        if (CollectionUtils.isNotEmpty(priceDetail.getApplicableCoupons())) {
            for (com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceCouponInfo couponInfo : priceDetail.getApplicableCoupons()) {
                if (couponCode.equalsIgnoreCase(couponInfo.getCouponCode())) {
                    Coupon coupon = new Coupon();
                    coupon.setCode(couponInfo.getCouponCode());
                    coupon.setDescription(couponInfo.getDescription());
                    coupon.setCouponAmount(couponInfo.getDiscount());
                    coupon.setBnplAllowed(couponInfo.isBnplAllowed());
                    coupon.setTncUrl(couponInfo.getTncUrl());
                    coupon.setNoCostEmiApplicable(couponInfo.isNoCostEmiApplicable());
                    coupon.setAutoApplicable(priceDetail.getCouponCode().equalsIgnoreCase(couponInfo.getCouponCode()));
                    //coupon.setCouponType(couponInfo.getType());
                    coupon.setPromoIcon(StringUtils.isNotBlank(couponInfo.getPromoIconLink()) ? couponInfo.getPromoIconLink() : genericBankIcon);
                    if (CollectionUtils.isEmpty(coupons)) {
                        coupons = new ArrayList<>();
                    }
                    coupon.setBankOffer(showReviewOffersCategory && couponInfo.isBankOffer());
                    coupons.add(coupon);
                }
            }
        }

        return coupons;
    }

    private void setGroupPriceAndSavingsText(TotalPricing totalPricing, Integer roomCount, Integer nightCount, long savingPerc, long displayPrice, boolean groupBookingFunnel, boolean groupBookingPrice, boolean ismyPartnerRequest, boolean groupFunnelEnhancement) {
        if (groupBookingFunnel && totalPricing != null) {
            numberFormatter = NumberFormat.getNumberInstance(new Locale("en", "IN"));
            String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
            //the check has been put inCase of search-roomapi not to add displayPrice in groupPriceText
            if (!groupFunnelEnhancement) {
                if (utility.isDetailPageAPI(controller) && ismyPartnerRequest) {
                    totalPricing.setGroupPriceText(commonResponseTransformer.getGroupPriceText(roomCount, nightCount));
                } else {
                    totalPricing.setGroupPriceText(commonResponseTransformer.getGroupPriceText(roomCount, nightCount, numberFormatter.format(displayPrice), "", false));
                }
            }
            if (savingPerc != 0.0 && groupBookingPrice) {
                totalPricing.setSavingsText(polyglotService.getTranslatedData(SAVING_PERC_TEXT)
                        .replace("{PERCENTAGE}", String.valueOf(savingPerc)));
            }
        }
    }

    public EMIPlanDetail buildEmiPlanDetails(NoCostEmiDetails noCostEmiDetails) {
        if (noCostEmiDetails == null || noCostEmiDetails.getEmiAmount() == 0) {
            return null;
        }

        // Format the totalCost with commas and without decimals
        EMIPlanDetail emiPlanDetail = new EMIPlanDetail();
        emiPlanDetail.setEmiTagType(EmiPlanType.NO_COST.name());
        String emiPlanDescBuilder = "from " +
                Currency.INR.getCurrencySymbol() +
                numberFormatter.format(noCostEmiDetails.getEmiAmount()) +
                "/month";
        emiPlanDetail.setEmiPlanDesc(emiPlanDescBuilder);
        if (noCostEmiDetails.getCouponTiedEmiAmount() > 0) {
            // No cost EMI starting @ {currency} {amount}
            String emiTagDetail = polyglotService.getTranslatedData(NO_COST_EMI_TAG)
                    .replace("{currency}", Currency.INR.getCurrencySymbol())
                    .replace("{amount}", numberFormatter.format(noCostEmiDetails.getCouponTiedEmiAmount()));
            emiPlanDetail.setEmiTagDetail(emiTagDetail);
        }
        return emiPlanDetail;
    }

    private void buildNoCostEmiDetailsForRootLevel(NoCostEmiDetails noCostEmiDetails, NoCostEmiDetails noCostEmiDetailForRootLevel) {
        // noCostEmiDetailForRootLevel is initialized to new NoCostEmiDetails() in the calling method
        // so its emiAmount is 0.0 by default
        if (noCostEmiDetailForRootLevel != null && noCostEmiDetails != null && (noCostEmiDetailForRootLevel.getEmiAmount() == 0 || noCostEmiDetailForRootLevel.getEmiAmount() > noCostEmiDetails.getEmiAmount())) {
            noCostEmiDetailForRootLevel.setEmiAmount(noCostEmiDetails.getEmiAmount());
        }
    }

    public void buildEmiConfig(SearchRoomsResponse searchRoomsResponse, HotelDetails hotelDetails) {
        if (hotelDetails.getHotelRateFlags() != null && hotelDetails.getHotelRateFlags().isEmiPlanAvailable()) {
            EmiTagDetails emiTagDetails = new EmiTagDetails();
            emiTagDetails.setText("NO COST EMI");
            emiTagDetails.setBgGradient(noCostEmiIconConfig);
            Map<String, EmiTagDetails> emiTagDetailsMap = new HashMap<>();
            emiTagDetailsMap.put("NO_COST", emiTagDetails);
            EmiConfigDetails emiConfigDetails = new EmiConfigDetails();
            emiConfigDetails.setCtaText(polyglotService.getTranslatedData(ConstantsTranslation.VIEW_PLANS_CTA_TEXT));
            emiConfigDetails.setEmiTags(emiTagDetailsMap);
            searchRoomsResponse.setEmiConfig(emiConfigDetails);
        }
    }


    public boolean isLuxOrPremCategory(Set<String> categories){
        String hotelCategory = buildMmtHotelCategory(categories);
        return MmtHotelCategory.LUXE.toString().equalsIgnoreCase(hotelCategory) || MmtHotelCategory.PREMIUM.toString().equalsIgnoreCase(hotelCategory);
    }

    private String buildMmtHotelCategory(Set<String> categories) {
        if (CollectionUtils.isEmpty(categories)) {
            return null;
        }
        if (categories.contains(BUDGET_CONTEXT)) {
            return MmtHotelCategory.BUDGET.toString();
        } else if (categories.contains(LUXURY_HOTELS)) {
            return MmtHotelCategory.LUXE.toString();
        } else if (categories.contains(PREMIUM_CONTEXT)) {
            return MmtHotelCategory.PREMIUM.toString();
        }
        return null;
    }
}
