package com.mmt.hotels.clientgateway.transformer.response;


import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.DuplicateBookingKey;
import com.mmt.hotels.clientgateway.enums.SpecifiedErrorsRequestor;
import com.mmt.hotels.clientgateway.helpers.ErrorHelper;
import com.mmt.hotels.clientgateway.helpers.ForwardBookingFlowHelper;
import com.mmt.hotels.clientgateway.response.ConsentData;
import com.mmt.hotels.clientgateway.response.PaymentResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.payment.BeginCheckoutReqBody;
import com.mmt.hotels.model.response.corporate.DuplicateBookingDetails;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PaymentResponseTransformer {

    @Autowired
    protected PolyglotService polyglotService;

    @Autowired
    ErrorHelper errorHelper;

    @Autowired
    private ForwardBookingFlowHelper forwardBookingFlowHelper;

    @Autowired
    private Utility utility;

    private static final Logger logger = LoggerFactory.getLogger(PaymentResponseTransformer.class);

    public PaymentResponse processResponse(PaymentCheckoutResponse checkoutResponse, BeginCheckoutReqBody beginCheckoutReqBody){
        String txnkey = beginCheckoutReqBody.getTransactionKey();
        PaymentResponse paymentResponse = new PaymentResponse();
        paymentResponse.setCorrelationKey(checkoutResponse.getCorrelationKey());
        if(checkoutResponse.getResponseErrors() != null && CollectionUtils.isNotEmpty(checkoutResponse.getResponseErrors().getErrorList())){
            String errorCode = checkoutResponse.getResponseErrors().getErrorList().get(0).getErrorCode();
            SpecifiedErrorsRequestor specificError = SpecifiedErrorsRequestor.resolve(errorCode);
            if(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(beginCheckoutReqBody.getIdContext()) && specificError!=null){
                //will execute only if payment-checkout is called from corp and the error code matches to specified errors
                Error error = new Error(specificError.getErrorCode(), errorHelper.getSubtitleForError(specificError.getSubTitle()), null, errorHelper.getTitleForError(specificError.getTitle()));
                error = forwardBookingFlowHelper.setForwardBookingFlowDataToErrorForPaymentPage(error, checkoutResponse);
                logger.warn("Error: {}", error);
                paymentResponse.setError(error);
            }else{
                String errorTitle = null;
                if(MapUtils.isNotEmpty(checkoutResponse.getResponseErrors().getErrorList().get(0).getErrorAdditionalInfo())) {
                    errorTitle = checkoutResponse.getResponseErrors().getErrorList().get(0).getErrorAdditionalInfo().getOrDefault("ERROR_TITLE",null);
                }
                Error error  = new Error(checkoutResponse.getResponseErrors().getErrorList().get(0).getErrorCode(), checkoutResponse.getResponseErrors().getErrorList().get(0).getErrorMessage(),null,errorTitle);
                error = forwardBookingFlowHelper.setForwardBookingFlowDataToErrorForPaymentPage(error, checkoutResponse);
                paymentResponse.setError(error);
            }
        } else if(null != checkoutResponse.getDuplicateBookingDetails() && null != beginCheckoutReqBody.getRequestIdentifier() && null != beginCheckoutReqBody.getRequestIdentifier().getCheckDuplicateBooking() && beginCheckoutReqBody.getRequestIdentifier().getCheckDuplicateBooking()) {
            populateConsentData(paymentResponse, checkoutResponse);
            paymentResponse.setTransactionKey(txnkey);
            paymentResponse.setCorrelationKey(checkoutResponse.getCorrelationKey());
            paymentResponse.setPaymentRespMessage(checkoutResponse.getPaymentRespMessage());
        }else {
            paymentResponse.setBookingID(checkoutResponse.getBookingID());
            paymentResponse.setCheckoutId(checkoutResponse.getPaymentParams() != null 
            		&& checkoutResponse.getPaymentParams().get("checkoutId") != null ? checkoutResponse.getPaymentParams().get("checkoutId").toString() : null);
            paymentResponse.setPaymentRespMessage(checkoutResponse.getPaymentRespMessage());
            paymentResponse.setTransactionKey(txnkey);

            if (checkoutResponse.isAlternateCurrencySelected()) {
                paymentResponse.setTotalAmount(checkoutResponse.getDisplayPriceAlternateCurrency());
                paymentResponse.setCurrency(checkoutResponse.getAlternateCurrencyCode());
            } else {
                paymentResponse.setTotalAmount(checkoutResponse.getTotalAmount());
                paymentResponse.setCurrency(checkoutResponse.getCurrency());
            }

            // For BNPL flow at clients to redirect to Thankyou/Payment page
            if (checkoutResponse.getRedirect() != null) {
                paymentResponse.setRedirect(checkoutResponse.getRedirect().name());
            }
        }

        return  paymentResponse;
    }

    public void populateConsentData(PaymentResponse paymentResponse, PaymentCheckoutResponse checkoutResponseHES) {
        if(null != checkoutResponseHES.getDuplicateBookingDetails()){
            DuplicateBookingDetails duplicateBookingDetails = checkoutResponseHES.getDuplicateBookingDetails();
            ConsentData consentData = new ConsentData();
            consentData.setDataList(utility.populateDateList(duplicateBookingDetails));
            populateClientSpecificFields(consentData);
            utility.populateCommonFields(consentData);
            paymentResponse.setConsentData(consentData);
        }
    }

    public void populateClientSpecificFields(ConsentData consentData) {
        consentData.setIconUrl(polyglotService.getTranslatedData(DuplicateBookingKey.DUPLICATE_BOOKING_ICON_URL_MOBILE.getValue()));
    }
}
