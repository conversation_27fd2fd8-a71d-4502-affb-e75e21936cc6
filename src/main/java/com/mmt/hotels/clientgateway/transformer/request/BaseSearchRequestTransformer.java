package com.mmt.hotels.clientgateway.transformer.request;

import com.gommt.hotels.orchestrator.enums.SubpageContext;
import com.gommt.hotels.orchestrator.model.request.listing.FilterDetails;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.helpers.FilterHelper;
import com.mmt.hotels.clientgateway.request.BatchKey;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.request.FilterRange;
import com.mmt.hotels.clientgateway.request.ImageCategory;
import com.mmt.hotels.clientgateway.request.ImageDetails;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.request.MatchMakerDetails;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.ReviewDetails;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.request.SelectedTag;
import com.mmt.hotels.clientgateway.transformer.factory.FilterFactory;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.enums.SectionsType;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.GuestRecommendationEnabledReqBody;
import com.mmt.hotels.model.request.ImageCategoryEntityBO;
import com.mmt.hotels.model.request.MultiCityFilter;
import com.mmt.hotels.model.request.ResponseFilterFlags;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.request.flyfish.FlyFishSummaryRequest;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.request.flyfish.SubConceptFilterDTO;
import com.mmt.hotels.model.request.flyfish.SummaryFilterCriteriaDTO;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.model.request.matchmaker.Tags;
import com.mmt.hotels.model.request.payment.TravelerDetail;
import com.mmt.hotels.pojo.request.SemanticSearchDetails;
import com.mmt.scrambler.ScramblerClient;
import com.mmt.scrambler.exception.ScramblerClientException;
import com.mmt.scrambler.utils.HashType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

public abstract  class BaseSearchRequestTransformer {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseSearchRequestTransformer.class);

  @Autowired
  private FilterFactory filterFactory;

    @Autowired
    FilterHelper filterHelper;

  @Autowired
  Utility utility;

    public SearchWrapperInputRequest convertSearchRequest(ListingSearchRequest searchHotelsRequestGateway, CommonModifierResponse commonModifierResponse) {

        SearchWrapperInputRequest searchWrapperInputRequest = new SearchWrapperInputRequest();
        searchWrapperInputRequest.setCorrelationKey(searchHotelsRequestGateway.getCorrelationKey());
        buildDeviceDetails(searchWrapperInputRequest, searchHotelsRequestGateway.getDeviceDetails());
        buildSearchCriteria(searchWrapperInputRequest, searchHotelsRequestGateway.getSearchCriteria(),searchHotelsRequestGateway.getFilterCriteria(),searchHotelsRequestGateway.getRequestDetails(), searchHotelsRequestGateway.getExpDataMap());
        buildRequestDetails(searchWrapperInputRequest, searchHotelsRequestGateway.getRequestDetails(), commonModifierResponse, searchHotelsRequestGateway.getDeviceDetails());
        buildImageDetails(searchWrapperInputRequest, searchHotelsRequestGateway.getImageDetails());
        searchWrapperInputRequest.setCardId(searchHotelsRequestGateway.getCardId());
        searchWrapperInputRequest.setExperimentData(searchHotelsRequestGateway.getExpData());
        searchWrapperInputRequest.setValidExpList(searchHotelsRequestGateway.getValidExpList());
        searchWrapperInputRequest.setVariantKeys(searchHotelsRequestGateway.getVariantKeys());
        searchWrapperInputRequest.setRequestIdentifier(utility.buildRequestIdentifier(searchHotelsRequestGateway.getRequestDetails()));
        searchWrapperInputRequest.setSortCriteria(buildSortCriteria(searchHotelsRequestGateway.getSortCriteria()));
        Set<String> dptInlineAppliedCategories = new HashSet<>();
        searchWrapperInputRequest.setAppliedFilterMap(buildAppliedFilterMap(searchHotelsRequestGateway, dptInlineAppliedCategories,commonModifierResponse!=null?commonModifierResponse.getExpDataMap():null));
        searchWrapperInputRequest.setDptInlineAppliedCollections(dptInlineAppliedCategories);
        searchWrapperInputRequest.setAppliedBatchKeys(buildAppliedBatchKeys(searchHotelsRequestGateway.getAppliedBatchKeys()));
        // Hotstore is providing the static filter with filterGroup "ALT_ACCO_PROPERTY" and filterValue "AltAcco", so we do not have to apply composite filters
        // updateCompositeFilters(searchHotelsRequestGateway.getFilterCriteria(), searchWrapperInputRequest.getAppliedFilterMap());
        updateFiltersToRemove(searchHotelsRequestGateway, searchWrapperInputRequest);
        updateFilterGroupsToRemove(searchHotelsRequestGateway, searchWrapperInputRequest);
        searchWrapperInputRequest.setAdvancedFiltering(true);

        if (searchHotelsRequestGateway.getFeatureFlags() == null)
     	   searchHotelsRequestGateway.setFeatureFlags(new FeatureFlags());
        searchWrapperInputRequest.setResponseFilterFlags(buildResponseFilterFlags(searchWrapperInputRequest, searchHotelsRequestGateway, commonModifierResponse));
        searchWrapperInputRequest.setFlyfishSummaryRequest(buildReviewDetails(searchHotelsRequestGateway.getReviewDetails(), searchHotelsRequestGateway.getSearchCriteria().getCountryCode()));
        searchWrapperInputRequest.setMatchMakerRequest(searchHotelsRequestGateway.getMatchMakerDetails());
        searchWrapperInputRequest.setLastPeekedOnMapHotelIds(searchHotelsRequestGateway.getLastPeekedOnMapHotelIds());
        searchWrapperInputRequest.setGuestRecommendationEnabled(buildGuestRecommendationEnabled());
        if ("MOBILE".equalsIgnoreCase(searchHotelsRequestGateway.getDeviceDetails().getDeviceType()))
            searchWrapperInputRequest.setCancellationPolicyRulesReq("yes");
        else
            searchWrapperInputRequest.setCancellationPolicyRulesReq("no");
        searchWrapperInputRequest.setRequestType("B2CAgent");
        searchWrapperInputRequest.setSeoCohort(searchHotelsRequestGateway.getFeatureFlags().getSeoCohort());
        searchWrapperInputRequest.setChannel(StringUtils.isEmpty(searchHotelsRequestGateway.getRequestDetails().getChannel()) ? "B2C":searchHotelsRequestGateway.getRequestDetails().getChannel());
        searchWrapperInputRequest.setPaymentChannel(searchHotelsRequestGateway.getRequestDetails().getChannel());
        searchWrapperInputRequest.setMobile(commonModifierResponse.getMobile());
        searchWrapperInputRequest.setAffiliateId(commonModifierResponse.getAffiliateId());
        searchWrapperInputRequest.setApplicationId(String.valueOf(commonModifierResponse.getApplicationId()));
        searchWrapperInputRequest.setPageContext(searchHotelsRequestGateway.getRequestDetails().getPageContext());
        searchWrapperInputRequest.setSubPageContext(searchHotelsRequestGateway.getRequestDetails().getSubPageContext());
        searchWrapperInputRequest.setMcid(commonModifierResponse.getMcId());
        searchWrapperInputRequest.setMmtAuth(commonModifierResponse.getMmtAuth());
        searchWrapperInputRequest.setAuthToken(commonModifierResponse.getMmtAuth());
        searchWrapperInputRequest.setUserId(commonModifierResponse.getMmtAuth());
        searchWrapperInputRequest.setSiteDomain(searchHotelsRequestGateway.getRequestDetails().getSiteDomain());
        searchWrapperInputRequest.setCdfContextId(commonModifierResponse.getCdfContextId());
        searchWrapperInputRequest.setDomain(commonModifierResponse.getDomain());
        searchWrapperInputRequest.setGreenHornNewHotelFlag(commonModifierResponse.isGreenHornNewHotelFlag());
        searchWrapperInputRequest.setUserLocation(commonModifierResponse.getUserLocation());
        if (commonModifierResponse.getExtendedUser() != null) {
            searchWrapperInputRequest.setUUID(commonModifierResponse.getExtendedUser().getUuid());
            searchWrapperInputRequest.setProfileType(commonModifierResponse.getExtendedUser().getProfileType());
            searchWrapperInputRequest.setSubProfileType(commonModifierResponse.getExtendedUser().getAffiliateId());
            searchWrapperInputRequest.setAgencyUUID(Utility.fetchAgencyUUIDFromCorp(commonModifierResponse.getExtendedUser().getCorporateData()));
            /*if (Constants.PROFILE_CORPORATE.equalsIgnoreCase(commonModifierResponse.getExtendedUser().getProfileType()))
                searchWrapperInputRequest.setChannel("B2Bweb");
            */
            searchWrapperInputRequest.setCorpUserID(commonModifierResponse.getExtendedUser().getProfileId());
        }
        if (commonModifierResponse.getHydraResponse() != null) {
        	if (CollectionUtils.isNotEmpty(commonModifierResponse.getHydraResponse().getHydraMatchedSegment())) {
				String[] segmentList = new String[commonModifierResponse.getHydraResponse().getHydraMatchedSegment()
						.size()];
				int i = 0;
				for (String segment : commonModifierResponse.getHydraResponse().getHydraMatchedSegment()) {
					segmentList[i] = segment;
					i++;
				}
				searchWrapperInputRequest.setUserSegments(segmentList);
			}
			searchWrapperInputRequest.setFlightBooker(commonModifierResponse.getHydraResponse().isFlightBooker());
        }
        if (searchHotelsRequestGateway.getSearchCriteria().isNearBySearch() && searchHotelsRequestGateway.getMultiCityFilter()!=null) {
            searchWrapperInputRequest.setMultiCityFilter(builldMultiFilter(searchHotelsRequestGateway.getMultiCityFilter()));
        }
        searchWrapperInputRequest.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
        searchWrapperInputRequest.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());
        searchWrapperInputRequest.setRoomPreferenceEnabled(utility.checkIfFilterValueExistsInAppliedFilterMap(searchHotelsRequestGateway.getFilterCriteria()));
        searchWrapperInputRequest.setExperimentData(utility.updateExpDataForBedRoomCountFilter(searchHotelsRequestGateway.getFilterCriteria(), searchWrapperInputRequest.getExperimentData()));
        searchWrapperInputRequest.setReqContext(searchHotelsRequestGateway.getRequestDetails().isPremium() ? Constants.PREMIUM : Constants.DEFAULT);
        searchWrapperInputRequest.setSelectedTabId(searchHotelsRequestGateway.getSelectedTabId());
        searchWrapperInputRequest.setRoomRecommendationToggle(utility.getRoomRecommndationToggleValue(searchHotelsRequestGateway.getFilterCriteria()));
        // Setting flag in the baseSearchRequest for the Business Identification
        if(commonModifierResponse.getExtendedUser() != null){
            searchWrapperInputRequest.setBusinessIdentificationEnableFromUserService(utility.isBusinessIdentificationEnableFromUserService(commonModifierResponse.getExtendedUser()));
        }
        if (searchHotelsRequestGateway.getSearchCriteria() != null && CollectionUtils.isNotEmpty(searchHotelsRequestGateway.getSearchCriteria().getRoomStayCandidates())) {
            searchWrapperInputRequest.setRscValueForDeepLink(utility.buildRscValue(searchHotelsRequestGateway.getSearchCriteria().getRoomStayCandidates()));
        }
        return searchWrapperInputRequest;
    }

    private List<com.mmt.hotels.filter.BatchKey> buildAppliedBatchKeys(List<BatchKey> appliedBatchKeys) {
        if(CollectionUtils.isEmpty(appliedBatchKeys)){
            return null;
        }
        List<com.mmt.hotels.filter.BatchKey> appliedBatchKeysRequest = new ArrayList<>();
        appliedBatchKeys.forEach(appliedBatchKey -> appliedBatchKeysRequest.add(createBatchKey(appliedBatchKey)));
        return appliedBatchKeysRequest;
    }

    private com.mmt.hotels.filter.BatchKey createBatchKey(BatchKey appliedBatchKey) {
        com.mmt.hotels.filter.BatchKey appliedBatch = new com.mmt.hotels.filter.BatchKey();
        appliedBatch.setStaticBatch(appliedBatchKey.getStaticBatch());
        appliedBatch.setFilterValue(appliedBatchKey.getFilterValue());
        return appliedBatch;
    }

//    private void updateCompositeFilters(List<com.mmt.hotels.clientgateway.request.Filter> filterCriteria,
//                                      Map<FilterGroup, Set<Filter>> appliedFilterMap) {
//    if (CollectionUtils.isEmpty(filterCriteria)) {
//      return;
//    }
//    Map<String, List<com.mmt.hotels.clientgateway.request.Filter>> compositeFilterMapping = filterFactory
//                                                                                                .getCompositeFilterConfig();
//    if (MapUtils.isEmpty(compositeFilterMapping)) {
//      return;
//    }
//    for (com.mmt.hotels.clientgateway.request.Filter filterCG : filterCriteria) {
//      if (compositeFilterMapping.containsKey(filterCG.getFilterGroup().name())) {
//        updateAppliedFilterMap(appliedFilterMap, compositeFilterMapping.get(filterCG.getFilterGroup().name()));
//      }
//    }
//  }

  private void updateFilterGroupsToRemove(ListingSearchRequest searchHotelsRequestGateway, SearchWrapperInputRequest searchWrapperInputRequest) {
        if(CollectionUtils.isNotEmpty(searchHotelsRequestGateway.getFilterGroupsToRemove())){
            List<FilterGroup> filterGroupsToRemove = new ArrayList<>();
            searchHotelsRequestGateway.getFilterGroupsToRemove()
                    .forEach(fg -> filterGroupsToRemove.add(FilterGroup.getFilterGroupFromFilterName(fg.name())));
            searchWrapperInputRequest.setFilterGroupsToRemove(filterGroupsToRemove);
        }
    }

    private void updateFiltersToRemove(ListingSearchRequest searchHotelsRequestGateway, SearchWrapperInputRequest searchWrapperInputRequest) {
        if (CollectionUtils.isNotEmpty(searchHotelsRequestGateway.getFiltersToRemove())){
            List<Filter> filtersToRemove = new ArrayList<>();
            for (com.mmt.hotels.clientgateway.request.Filter filter : searchHotelsRequestGateway.getFiltersToRemove()){
                Filter filtertoremove = new Filter();
                filtertoremove.setFilterValue(filter.getFilterValue());
                filtertoremove.setFilterGroup(FilterGroup.getFilterGroupFromFilterName(filter.getFilterGroup().name()));
                filtertoremove.setRangeFilter(filter.isRangeFilter());
                filtertoremove.setFilterRange(buildFilterRange(filter.getFilterRange()));
                filtersToRemove.add(filtertoremove);
            }
            searchWrapperInputRequest.setFiltersToRemove(filtersToRemove);
        }
    }

    // This needs to be evaluated for more flags
   	private void buildMatchMakerDetails(SearchWrapperInputRequest searchWrapperInputRequest,
   			MatchMakerDetails matchmakerDetails) {
   		if (matchmakerDetails == null)
   			return;
   		MatchMakerRequest matchMakerRequest = new MatchMakerRequest();

   		if (CollectionUtils.isNotEmpty(matchmakerDetails.getSelectedTags())) {
   			List<Tags> tags = new ArrayList<>();
   			for (SelectedTag tag: matchmakerDetails.getSelectedTags()) {
   				Tags matchMakerTag = new Tags();
   				matchMakerTag.setTagAreaId(tag.getTagAreaId());
   				matchMakerTag.setTagDescription(tag.getTagDescription());
   				tags.add(matchMakerTag);
   			}
   			matchMakerRequest.setSelectedTags(tags);
   		}
   		searchWrapperInputRequest.setMatchMakerRequest(matchMakerRequest);
   	}

   	private void buildImageDetails(SearchWrapperInputRequest searchWrapperInputRequest, ImageDetails imageDetails) {
    	if (imageDetails == null)
    		return;
    	searchWrapperInputRequest.setImageType(imageDetails.getTypes());
    	searchWrapperInputRequest.setImageCategory(buildImageCategory(imageDetails.getCategories()));
	}

    private List<ImageCategoryEntityBO> buildImageCategory(List<ImageCategory> imageCategories) {

        List<ImageCategoryEntityBO> imageCategoryList = new ArrayList<>();

        for (ImageCategory imageCategory : imageCategories){
        	ImageCategoryEntityBO imageCategoryCB = new ImageCategoryEntityBO();
        	imageCategoryCB.setCategory(imageCategory.getType());
            imageCategoryCB.setCount(imageCategory.getCount());
            imageCategoryCB.setHeight(imageCategory.getHeight());
            imageCategoryCB.setOutputFormat(imageCategory.getImageFormat());
            imageCategoryCB.setWidth(imageCategory.getWidth());
            imageCategoryList.add(imageCategoryCB);
        }
        return imageCategoryList;
    }

    private ResponseFilterFlags buildResponseFilterFlags(SearchWrapperInputRequest searchWrapperInputRequest, ListingSearchRequest searchHotelsRequestGateway, CommonModifierResponse commonModifierResponse) {
    	FeatureFlags featureFlags = searchHotelsRequestGateway.getFeatureFlags();
		ResponseFilterFlags responseFilterFlags = new ResponseFilterFlags();
		responseFilterFlags.setStaticData(featureFlags.isStaticData());
		responseFilterFlags.setFlyfishSummaryRequired(featureFlags.isReviewSummaryRequired());
		responseFilterFlags.setWalletRequired(featureFlags.isWalletRequired());
		responseFilterFlags.setShortlistRequired(featureFlags.isShortlistingRequired());
		responseFilterFlags.setCheckAvailibility(featureFlags.isCheckAvailability());
		responseFilterFlags.setBestCoupon(featureFlags.isCoupon());
		responseFilterFlags.setApplyAbsorption(featureFlags.isApplyAbsorption());
		//responseFilterFlags.setDealOfTheDayRequired(featureFlags.isDealOfTheDayRequired());
        //Removing flag dependency fix for SWAT-4039659
        responseFilterFlags.setDealOfTheDayRequired(true);
        responseFilterFlags.setPoisRequiredOnMap(featureFlags.isPoisRequiredOnMap());
        responseFilterFlags.setExtraAltAccoPropertiesRequired(featureFlags.isExtraAltAccoRequired());
        responseFilterFlags.setPersuasionsEngineHit(featureFlags.isPersuasionsEngineHit());
        Optional.ofNullable(featureFlags.getMaskedPropertyName()).ifPresent(responseFilterFlags::setMaskedPropertyName);
        responseFilterFlags.setSelectiveHotels(featureFlags.isSelectiveHotels());
        searchWrapperInputRequest.setNumberOfAddons(featureFlags.getNoOfAddons());
        searchWrapperInputRequest.setNumberOfCoupons(featureFlags.getNoOfCoupons());
        searchWrapperInputRequest.setNoOfPersuasions(featureFlags.getNoOfPersuasions());
        searchWrapperInputRequest.setNumberOfSoldOuts(featureFlags.getNoOfSoldouts());
		responseFilterFlags.setCityTaxExclusive(commonModifierResponse.isCityTaxExclusive());
		responseFilterFlags.setSimilarHotels(featureFlags.isSimilarHotel());
		responseFilterFlags.setComparator(featureFlags.isComparator());
		responseFilterFlags.setOriginListingMap(featureFlags.isOriginListingMap());
		responseFilterFlags.setMostBooked(featureFlags.isMostBooked());
        responseFilterFlags.setDetailMap(featureFlags.isDetailMap());
        responseFilterFlags.setPersuasionRequired(featureFlags.isPersuasionsRequired());
        responseFilterFlags.setFlashDealClaimed(featureFlags.isFlashDealClaimed());
		if(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(searchHotelsRequestGateway.getRequestDetails().getIdContext())) {
			responseFilterFlags.setNewCorp(true);
			responseFilterFlags.setPersuasionSeg(null);
			responseFilterFlags.setCityTaxExclusive(null);
		}
    responseFilterFlags.setTopCard(featureFlags.isTopCard());
    responseFilterFlags.setCardRequired(featureFlags.isCardRequired());
    responseFilterFlags.setFilters(featureFlags.isFilters());
    responseFilterFlags.setHomestayV2Flow(commonModifierResponse.isHomestayV2Flow());
    responseFilterFlags.setOrientation(featureFlags.getOrientation());
    responseFilterFlags.setUpsellRateplanRequired(featureFlags.isUpsellRateplanRequired());
    responseFilterFlags.setPremiumThemesCardRequired(featureFlags.isPremiumThemesCardRequired());

        return responseFilterFlags;
	}



	private FlyFishSummaryRequest buildReviewDetails(ReviewDetails reviewDetails, String countryCode) {
		if (reviewDetails == null)
			return null;
		FlyFishSummaryRequest flyfishSummaryRequest = new FlyFishSummaryRequest();
		SummaryFilterCriteriaDTO summaryFilterCriteriaDTO = new SummaryFilterCriteriaDTO();
		SubConceptFilterDTO subConceptFilterDTO = new SubConceptFilterDTO();
		List<OTA> otas = new ArrayList<>();
		if (reviewDetails.getOtas()!=null) {
            for (OTA ota: OTA.values()) {
                if (reviewDetails.getOtas().contains(ota.name()))
                    otas.add(ota);
            }
        }

        if (!(Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode) || otas.contains(OTA.EXP))) {
            otas.add(OTA.EXP);
        }

        subConceptFilterDTO.setTagTypes(reviewDetails.getTagTypes());
		summaryFilterCriteriaDTO.setSubConcept(subConceptFilterDTO);
		summaryFilterCriteriaDTO.setOtas(otas);
		flyfishSummaryRequest.setFilter(summaryFilterCriteriaDTO);
		return flyfishSummaryRequest;
	}

  private void updateAppliedFilterMap(Map<FilterGroup, Set<Filter>> appliedFilterMap,
                                      List<com.mmt.hotels.clientgateway.request.Filter> filterCriteria, MatchMakerRequest matchMakerDetails, Set<String> dptInlineAppliedCategories) {
    if (CollectionUtils.isEmpty(filterCriteria)) {
      return;
    }
    if (appliedFilterMap == null) {
      appliedFilterMap = new HashMap<>();
    }

    com.mmt.hotels.clientgateway.request.Filter lastFilterAppliedCG = filterCriteria.get(filterCriteria.size()-1);
    lastFilterAppliedCG.setLastApplied(true);
    for (com.mmt.hotels.clientgateway.request.Filter filterCG : filterCriteria) {
        if(filterCG.getFilterGroup() != null) {
            if (EXACT_ROOM_RECOMMENDATION.equalsIgnoreCase(filterCG.getFilterGroup().name()) || (BEDROOM_COUNT.equalsIgnoreCase(filterCG.getFilterGroup().name()))) {
                continue;
            }
        }
      com.mmt.hotels.filter.FilterGroup filterGroup = com.mmt.hotels.filter.FilterGroup.getFilterGroupFromFilterName(
          filterCG.getFilterGroup().name());
      if (filterGroup == null) {
        LOGGER.error("No filter group found in HES for CG filter group::{}", filterCG.getFilterGroup().name());
        continue;
      }
      if(filterCG.getFilterGroup().name().equalsIgnoreCase(Constants.VILLA_AND_APPT)) {
          filterGroup = com.mmt.hotels.filter.FilterGroup
                  .getFilterGroupFromFilterName(Constants.ALT_ACCO_PROPERTY);
          filterCG.setFilterValue(Constants.AltAcco);
      }
      com.mmt.hotels.filter.Filter filterCB = new com.mmt.hotels.filter.Filter();
      filterCB.setFilterGroup(filterGroup);
      filterCB.setFilterRange(buildFilterRange(filterCG.getFilterRange()));
      filterCB.setFilterValue(filterCG.getFilterValue());
      filterCB.setRangeFilter(filterCG.isRangeFilter());
      filterCB.setLastApplied(filterCG.isLastApplied());
      appliedFilterMap.putIfAbsent(filterGroup, new HashSet<>());
      appliedFilterMap.get(filterGroup).add(filterCB);
    }
      filterHelper.updateAppliedFilterMapDptCollections(appliedFilterMap, matchMakerDetails, dptInlineAppliedCategories);

  }

    public Map<FilterGroup, Set<Filter>> buildAppliedFilterMap(ListingSearchRequest searchRequestGateway, Set<String> dptInlineAppliedCategories, LinkedHashMap<String, String> expDataMap) {

        // On the basis of the locationId/cityCode we are adding preAppliedFilter in the filterCriteria in the client request
        if (searchRequestGateway != null) {
            List<com.mmt.hotels.clientgateway.request.Filter> preAppliedFiltersForLocationId = utility.getPreAppliedFiltersForLocationId(searchRequestGateway.getSearchCriteria(), expDataMap);
            if (CollectionUtils.isNotEmpty(preAppliedFiltersForLocationId)) {
                if (CollectionUtils.isEmpty(searchRequestGateway.getFilterCriteria())) {
                    searchRequestGateway.setFilterCriteria(new ArrayList<>());
                }
                if (CollectionUtils.isNotEmpty(preAppliedFiltersForLocationId)) {
                    searchRequestGateway.getFilterCriteria().addAll(preAppliedFiltersForLocationId);
                }
            }
        }
        if (searchRequestGateway != null && CollectionUtils.isNotEmpty(searchRequestGateway.getFilterCriteria())) {

            if(searchRequestGateway.getRequestDetails() != null && searchRequestGateway.getRequestDetails().getTrafficSource() != null &&
                    searchRequestGateway.getRequestDetails().getTrafficSource().getSource() != null ) {

                //for vistara traffic source flyer deal filter coming from flights, we have to replace it with vistara deal filter
                if(TRAFFIC_SOURCE_VISTARA_FLIGHTS.equalsIgnoreCase(searchRequestGateway.getRequestDetails().getTrafficSource().getSource())){
                    if(searchRequestGateway.getFilterCriteria().get(0) != null && FilterGroup.HOTEL_CATEGORY.name().equals(searchRequestGateway.getFilterCriteria().get(0).getFilterGroup().name())){
                        searchRequestGateway.getFilterCriteria().get(0).setFilterValue(VISTARA_FILTER_VALUE);
                    }
                }

                if(TRAFFIC_SOURCE_FLIGHTS_THANKYOU_PAGE.equalsIgnoreCase(searchRequestGateway.getRequestDetails().getTrafficSource().getSource())){
                    utility.addFilterInAppliedFilterList(com.mmt.hotels.clientgateway.response.filter.FilterGroup.STAR_RATING,STAR_RATING_4,-1,searchRequestGateway.getFilterCriteria());
                    utility.addFilterInAppliedFilterList(com.mmt.hotels.clientgateway.response.filter.FilterGroup.STAR_RATING,STAR_RATING_5,-1,searchRequestGateway.getFilterCriteria());
                }
            }
            Map<FilterGroup, Set<Filter>> appliedFilterMapCB = new LinkedHashMap<>();
            updateAppliedFilterMap(appliedFilterMapCB, searchRequestGateway.getFilterCriteria(), searchRequestGateway.getMatchMakerDetails(), dptInlineAppliedCategories);

            if (searchRequestGateway.getRequestDetails() != null && (SubpageContext.fromValue(searchRequestGateway.getRequestDetails().getSubPageContext()) == SubpageContext.LISTING_LMD)
                    && !appliedFilterMapCB.containsKey("DEALS")) {
                Set<Filter> filterSet = new HashSet<Filter>();
                Filter filterLmd = new Filter();
                filterLmd.setFilterGroup(FilterGroup.DEALS);
                filterLmd.setFilterValue("LAST_MINUTE");
                filterSet.add(filterLmd);

                appliedFilterMapCB.put(FilterGroup.DEALS, filterSet);
            }
            return appliedFilterMapCB;
        }

        Map<FilterGroup, Set<Filter>> appliedFilterMapCB = null;
        if (searchRequestGateway != null && searchRequestGateway.getRequestDetails() != null && (SubpageContext.fromValue(searchRequestGateway.getRequestDetails().getSubPageContext()) == SubpageContext.LISTING_LMD)) {
            appliedFilterMapCB = new LinkedHashMap<>();
            Set<Filter> filterSet = new HashSet<>();
            Filter filterLmd = new Filter();
            filterLmd.setFilterGroup(FilterGroup.DEALS);
            filterLmd.setFilterValue("LAST_MINUTE");
            filterSet.add(filterLmd);

            appliedFilterMapCB.put(FilterGroup.DEALS, filterSet);
        }

        return appliedFilterMapCB;
    }

    private com.mmt.hotels.filter.FilterRange buildFilterRange(FilterRange filterRange) {
        if (filterRange == null)
            return null;

        com.mmt.hotels.filter.FilterRange filterRangeCB = new com.mmt.hotels.filter.FilterRange();
        filterRangeCB.setMaxValue(filterRange.getMaxValue());
        filterRangeCB.setMinValue(filterRange.getMinValue());

        return filterRangeCB;
    }

    public void buildDeviceDetails(SearchWrapperInputRequest searchWrapperInputRequest, DeviceDetails deviceDetails) {
        if (deviceDetails == null) {
            return;
        }
        searchWrapperInputRequest.setAppVersion(deviceDetails.getAppVersion());
        searchWrapperInputRequest.setBookingDevice(deviceDetails.getBookingDevice());
        searchWrapperInputRequest.setDeviceId(deviceDetails.getDeviceId());
        searchWrapperInputRequest.setDeviceType(deviceDetails.getDeviceType());
        searchWrapperInputRequest.setNetworkType(deviceDetails.getNetworkType());
        searchWrapperInputRequest.setDeviceName(deviceDetails.getDeviceName());
    }

    public void buildSearchCriteria(SearchWrapperInputRequest searchWrapperInputRequest,
                                     SearchHotelsCriteria searchCriteria, List<com.mmt.hotels.clientgateway.request.Filter> filterCriteria, RequestDetails requestDetails, Map<String, String> expData) {
        if(requestDetails != null && CollectionUtils.isNotEmpty(filterCriteria) && Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(requestDetails.getFunnelSource())){
            Optional<com.mmt.hotels.clientgateway.request.Filter> bedRoomCountfilter = filterCriteria.stream().filter(f -> com.mmt.hotels.clientgateway.response.filter.FilterGroup.BEDROOM_COUNT.name().equalsIgnoreCase(f.getFilterGroup().name())).findFirst();
            if(bedRoomCountfilter.isPresent()){
                utility.modifyRoomStayCandidateRequestForHomestayFunnel(bedRoomCountfilter.get(),searchCriteria);
            }
        }
        if(searchCriteria==null) {
            return;
        }
        searchWrapperInputRequest.setWishListedSearch(searchCriteria.isWishListedSearch());
        searchWrapperInputRequest.setHotelIdList(searchCriteria.getHotelIds());
        searchWrapperInputRequest.setCheckin(searchCriteria.getCheckIn());
        searchWrapperInputRequest.setCheckout(searchCriteria.getCheckOut());
        searchWrapperInputRequest.setCountryCode(searchCriteria.getCountryCode());
        searchWrapperInputRequest.setCityCode(searchCriteria.getCityCode());
        searchWrapperInputRequest.setLocationId(searchCriteria.getLocationId());
        searchWrapperInputRequest.setLocationType(searchCriteria.getLocationType());
        searchWrapperInputRequest.setCurrency(searchCriteria.getCurrency());
        searchWrapperInputRequest.setLimit(searchCriteria.getLimit());
        searchWrapperInputRequest.setLastFetchedHotelId(searchCriteria.getLastHotelId());
        searchWrapperInputRequest.setLastFetchedWindowInfo(searchCriteria.getLastFetchedWindowInfo());
        searchWrapperInputRequest.setGuestHouseAvailable(searchCriteria.getGuestHouseAvailable());
        searchWrapperInputRequest.setLastFetchedHotelCategory(searchCriteria.getLastHotelCategory());
        searchWrapperInputRequest.setGuestHouseAvailable(searchCriteria.getGuestHouseAvailable());
        searchWrapperInputRequest.setParentLocationId(searchCriteria.getParentLocationId());
        searchWrapperInputRequest.setParentLocationType(searchCriteria.getParentLocationType());
        searchWrapperInputRequest.setBaseRateplanCode(searchCriteria.getBaseRateplanCode());
        if(searchCriteria.getUserSearchType()!=null) {
            searchWrapperInputRequest.setUserSearchType(searchCriteria.getUserSearchType());
        }
        if(searchCriteria.getLat() != null && searchCriteria.getLat() != 0.0d)
            searchWrapperInputRequest.setLatitude(searchCriteria.getLat());
        if(searchCriteria.getLng() != null && searchCriteria.getLng() != 0.0d)
            searchWrapperInputRequest.setLongitude(searchCriteria.getLng());
        searchWrapperInputRequest.setRoomStayCandidates(buildRoomStayCandidates(searchCriteria.getRoomStayCandidates()));
        // we will put condition here to execute the logic for the new apps
        if (searchCriteria != null && utility.isDistributeRoomStayCandidates(searchCriteria.getRoomStayCandidates(), expData)) {
            searchWrapperInputRequest.setAppendRscInDeepLink(true);
            searchWrapperInputRequest.setRscValueForDeepLink(utility.buildRscValue(searchCriteria.getRoomStayCandidates()));
            searchWrapperInputRequest.setRoomStayCandidates(utility.buildRoomStayDistribution(searchCriteria.getRoomStayCandidates(), expData));
        }
        if(null != searchCriteria.getCollectionCriteria()) {
            searchWrapperInputRequest.setCollectionIds(searchCriteria.getCollectionCriteria().getCollectionIds());
            searchWrapperInputRequest.setCollectionsCount(searchCriteria.getCollectionCriteria().getCollectionsCount());
            searchWrapperInputRequest.setCollectionRequired(searchCriteria.getCollectionCriteria().isCollectionRequired());
            searchWrapperInputRequest.setTrendingNow(searchCriteria.getCollectionCriteria().isTrendingNow());
            searchWrapperInputRequest.setSuggestedForYouCardRequired(searchCriteria.getCollectionCriteria().isSuggestedForYouCards());
            searchWrapperInputRequest.setPropertyTypeCardRequired(searchCriteria.getCollectionCriteria().isPropertyTypeCards());
            searchWrapperInputRequest.setStaticFilterCardsRequired(searchCriteria.getCollectionCriteria().isStaticFilterCardsRequired());
            searchWrapperInputRequest.setDiscoverByDestinationCardsRequired(searchCriteria.getCollectionCriteria().isDiscoverByDestinationCardsRequired());
            searchWrapperInputRequest.setInspiredCardsRequired(searchCriteria.getCollectionCriteria().isInspiredCardsRequired());
            searchWrapperInputRequest.setOffbeatCitiesCardsRequired(searchCriteria.getCollectionCriteria().isOffbeatCitiesCardsRequired());
            searchWrapperInputRequest.setValueStayCardsRequired(searchCriteria.getCollectionCriteria().isValueStayCardsRequired());
            if(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(requestDetails.getIdContext())) {
                searchWrapperInputRequest.setAthenaCategory("myBiz");
            } else {
                searchWrapperInputRequest.setAthenaCategory(searchCriteria.getCollectionCriteria().getAthenaCategory());
            }
            searchWrapperInputRequest.setLuxeCardRequired(searchCriteria.getCollectionCriteria().isLuxeCardRequired());
            searchWrapperInputRequest.setBannerListCardRequired(searchCriteria.getCollectionCriteria().isBannerListCardRequired());
            searchWrapperInputRequest.setFamilyCardRequired(searchCriteria.getCollectionCriteria().isFamilyCardRequired());
            searchWrapperInputRequest.setHostCardRequired(searchCriteria.getCollectionCriteria().isHostCardRequired());
        }
        if (searchCriteria.isPersonalizedSearch() && CollectionUtils.isNotEmpty(searchCriteria.getTravellerEmailID())) {
            if (StringUtils.isNotBlank(searchCriteria.getTravellerEmailID().get(0))) {
                TravelerDetail travelerDetail = new TravelerDetail();
                try {
                    ScramblerClient sc = ScramblerClient.getInstance();
                    String commEmail = sc.encode(searchCriteria.getTravellerEmailID().get(0), HashType.F);
                    travelerDetail.setEmailCommId(commEmail);
                    searchWrapperInputRequest.setTravelerDetailsList(Collections.singletonList(travelerDetail));
                } catch (ScramblerClientException e){
                    LOGGER.warn("Error while encrypting email",e);
                }
            }
        }
        searchWrapperInputRequest.setBookingForGuestUser(searchCriteria.isBookingForGuest());
        searchWrapperInputRequest.setTripType(searchCriteria.getTripType());
        if (StringUtils.isNotBlank(searchCriteria.getSectionsType())) {
            searchWrapperInputRequest.setSectionsType(SectionsType.valueOf(searchCriteria.getSectionsType()));
        }
        searchWrapperInputRequest.setPersonalCorpBooking(searchCriteria.isPersonalCorpBooking());
        searchWrapperInputRequest.setPersonalizedSearch(searchCriteria.isPersonalizedSearch());
        utility.buildSlot(searchWrapperInputRequest,searchCriteria);
        searchWrapperInputRequest.setRmDHS(searchCriteria.getRmDHS());
        searchWrapperInputRequest.setBoostProperty(searchCriteria.getBoostProperty());
    }

    public void buildRequestDetails(SearchWrapperInputRequest searchWrapperInputRequest,
                                     RequestDetails requestDetails, CommonModifierResponse commonModifierResponse, DeviceDetails deviceDetails) {
        if (commonModifierResponse.getUserLocation() != null) {
            searchWrapperInputRequest.setSrCon(commonModifierResponse.getUserLocation().getCountry());
            searchWrapperInputRequest.setSrCty(commonModifierResponse.getUserLocation().getCity());
            searchWrapperInputRequest.setSrState(commonModifierResponse.getUserLocation().getState());
        }
        if(requestDetails!=null) {
            searchWrapperInputRequest.setVisitorId(requestDetails.getVisitorId());
            searchWrapperInputRequest.setFunnelSource(requestDetails.getFunnelSource());
            searchWrapperInputRequest.setIdContext(requestDetails.getIdContext());
            searchWrapperInputRequest.setSrLat(requestDetails.getSrLat());
            searchWrapperInputRequest.setSrLng(requestDetails.getSrLng());
            searchWrapperInputRequest.setNotifCoupon(requestDetails.getNotifCoupon());
            searchWrapperInputRequest.setVisitNumber(String.valueOf(requestDetails.getVisitNumber()));
            searchWrapperInputRequest.setTrafficSource(buildTrafficSource(requestDetails.getTrafficSource(), deviceDetails, commonModifierResponse));
            searchWrapperInputRequest.setLoggedIn(requestDetails.isLoggedIn());
            searchWrapperInputRequest.setRequester(requestDetails.getRequestor());
            searchWrapperInputRequest.setSeoCorp(requestDetails.isSeoCorp());
            searchWrapperInputRequest.setWishCode(requestDetails.getWishCode());
            searchWrapperInputRequest.setRequisitionID(requestDetails.getRequisitionID());
            searchWrapperInputRequest.setOldWorkflowId(requestDetails.getOldWorkflowId());
            searchWrapperInputRequest.setForwardBookingFlow(requestDetails.isForwardBookingFlow());
            searchWrapperInputRequest.setMyBizFlowIdentifier(requestDetails.getMyBizFlowIdentifier());
            searchWrapperInputRequest.setRequestIdentifier(utility.buildRequestIdentifier(requestDetails));
            if (requestDetails.getSemanticSearchDetails() != null) {
                SemanticSearchDetails sematicSearchDetails = new SemanticSearchDetails();
                sematicSearchDetails.setQueryText(requestDetails.getSemanticSearchDetails().getQueryText());
                sematicSearchDetails.setSemanticData(requestDetails.getSemanticSearchDetails().getSemanticData());
                searchWrapperInputRequest.setSemanticSearchDetails(sematicSearchDetails);
            }
        }
    }

    private com.mmt.hotels.model.request.TrafficSource buildTrafficSource(com.mmt.hotels.clientgateway.request.TrafficSource trafficSource, DeviceDetails deviceDetails, CommonModifierResponse commonModifierResponse) {
        if (trafficSource == null)
            return null;
        com.mmt.hotels.model.request.TrafficSource trafficSourceCB = new com.mmt.hotels.model.request.TrafficSource();
        // Use original traffic source from CommonModifierResponse if available, otherwise use the current source
        String originalSource = commonModifierResponse != null && StringUtils.isNotEmpty(commonModifierResponse.getOriginalTrafficSource())
            ? commonModifierResponse.getOriginalTrafficSource()
            : trafficSource.getSource();
        trafficSourceCB.setOriginalTrafficSource(originalSource);
        trafficSourceCB.setSource(trafficSource.getSource());
        trafficSourceCB.setType(trafficSource.getType());
        // Pass aud field to downstream APIs
        if (trafficSource.getAud() != null) {
            trafficSourceCB.setAud(trafficSource.getAud());
        }
        return trafficSourceCB;
    }

    public List<com.mmt.hotels.model.request.RoomStayCandidate> buildRoomStayCandidates(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        if (CollectionUtils.isEmpty(roomStayCandidates))
            return null;
        List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidateList = new ArrayList<>();

        for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate : roomStayCandidates){
            com.mmt.hotels.model.request.RoomStayCandidate roomStayCandidateCB = new com.mmt.hotels.model.request.RoomStayCandidate();
            roomStayCandidateCB.setGuestCounts(buildGuestCounts(roomStayCandidate));
            roomStayCandidateList.add(roomStayCandidateCB);
        }
        return roomStayCandidateList;
    }

    private List<GuestCount> buildGuestCounts(
            com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate) {
        List<GuestCount> guestCounts = new ArrayList<>();
        GuestCount guestCount = new GuestCount();
        guestCount.setAgeQualifyingCode("10");
        guestCount.setAges(roomStayCandidate.getChildAges());
        guestCount.setCount(String.valueOf(roomStayCandidate.getAdultCount()));
        guestCounts.add(guestCount);
        return guestCounts;
    }

    public com.mmt.hotels.model.request.SortCriteria buildSortCriteria(com.mmt.hotels.clientgateway.request.SortCriteria sortCriteria) {
        if (sortCriteria == null)
            return null;
        com.mmt.hotels.model.request.SortCriteria sortCriteriaCB = new com.mmt.hotels.model.request.SortCriteria();
        sortCriteriaCB.setOrder(sortCriteria.getOrder());
        sortCriteriaCB.setField(sortCriteria.getField());
        return sortCriteriaCB;
    }

    private GuestRecommendationEnabledReqBody buildGuestRecommendationEnabled() {
        GuestRecommendationEnabledReqBody guestRecommendationEnabledReqBody = new GuestRecommendationEnabledReqBody();
        guestRecommendationEnabledReqBody.setMaxRecommendations("1");
        guestRecommendationEnabledReqBody.setText("true");
        return guestRecommendationEnabledReqBody;
    }

    private MultiCityFilter builldMultiFilter(com.mmt.hotels.clientgateway.request.MultiCityFilter multiCityFilter){
        MultiCityFilter multiCityFilterHES = new MultiCityFilter();
        multiCityFilterHES.setLatitude(multiCityFilter.getLatitude());
        multiCityFilterHES.setLongitude(multiCityFilter.getLongitude());
        multiCityFilterHES.setRadius(multiCityFilter.getRadius());
        return multiCityFilterHES;
    }
}
