package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.Style;
import com.mmt.hotels.clientgateway.response.rooms.LoginPersuasion;
import com.mmt.hotels.clientgateway.response.rooms.RecommendedCombo;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.SearchRoomsPersuasionHelper;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import static com.mmt.hotels.clientgateway.constants.Constants.DT_INCLUSION_HTML;
import static com.mmt.hotels.clientgateway.constants.Constants.SPACE_X_SPACE;
import static com.mmt.hotels.clientgateway.constants.Constants.SPECIAL_FARE_TAG_SMALL;
import static com.mmt.hotels.clientgateway.constants.Constants.SPECIAL_FARE_TAG_SMALL_STYLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.LOGIN_PERSUASION_SUBTEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.LOGIN_PERSUASION_SUBTEXT_GCC;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.LOGIN_PERSUASION_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.LOGIN_PERSUASION_TEXT_GCC;

@Component
public class OrchSearchRoomsResponseTransformerDesktop extends OrchSearchRoomsResponseTransformer {
    private static final Logger LOGGER = LoggerFactory.getLogger(OrchSearchRoomsResponseTransformerDesktop.class);
    private final SearchRoomsPersuasionHelper searchRoomsPersuasionHelper;

    public OrchSearchRoomsResponseTransformerDesktop(SearchRoomsPersuasionHelper searchRoomsPersuasionHelper) {
        super();
        this.searchRoomsPersuasionHelper = searchRoomsPersuasionHelper;
    }

    @Override
    protected PersuasionObject createTopRatedPersuasion(boolean isNewDetailsPageDesktop) {
        // TODO: Add to Peitho
        return null;
    }

    @Override
    public LoginPersuasion buildLoginPersuasion(){
        String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
        LoginPersuasion loginPersuasion = new LoginPersuasion();
        if (Utility.isRegionGccOrKsa(region)) {
            loginPersuasion.setLoginPersuasionText(polyglotService.getTranslatedData(LOGIN_PERSUASION_TEXT_GCC));
            loginPersuasion.setLoginPersuasionSubText(polyglotService.getTranslatedData(LOGIN_PERSUASION_SUBTEXT_GCC));
        }else {
            loginPersuasion.setLoginPersuasionText(polyglotService.getTranslatedData(LOGIN_PERSUASION_TEXT));
            loginPersuasion.setLoginPersuasionSubText(polyglotService.getTranslatedData(LOGIN_PERSUASION_SUBTEXT));
        }
        return loginPersuasion;
    }

    /**
     * HTL-40907: Build delayed confirmation persuasion for negotiated rate hotels.
     * Negotiated rates are the one-on-one rates that are directly negotiated between the corporate/organization and the hotel.
     *
     * @param corpAlias             Organisation alias name.
     * @param isMyBizNewDetailsPage
     * @return delayed confirmation persuasion.
     */
    @Override
    public PersuasionResponse buildDelayedConfirmationPersuasion(String corpAlias, boolean isMyBizNewDetailsPage) {
        return searchRoomsPersuasionHelper.buildDelayedConfirmationPersuasion(corpAlias, isMyBizNewDetailsPage);
    }

    public String getHtml(){
        return  DT_INCLUSION_HTML;
    }

    /**
     * HTL-40907: Build special fare tag persuasion for negotiated rate hotels.
     * Negotiated rates are the one-on-one rates that are directly negotiated between the corporate/organization and the hotel.
     *
     * @param corpAlias Organization alias name.
     * @return special fare persuasion.
     */
    @Override
    public PersuasionResponse buildSpecialFareTagPersuasion(String corpAlias) {
        PersuasionResponse specialFarePersuasion = new PersuasionResponse();
        Style style = new Style();
        style.setStyleClass(SPECIAL_FARE_TAG_SMALL_STYLE);
        specialFarePersuasion.setStyle(style);
        String title = polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_FARE_TAG);
        corpAlias = corpAlias != null ? corpAlias : StringUtils.EMPTY;
        title = StringUtils.replace(title, "{CORP_ALIAS}", corpAlias);
        specialFarePersuasion.setTitle(title);
        specialFarePersuasion.setId(SPECIAL_FARE_TAG_SMALL);
        return specialFarePersuasion;
    }

    /**
     * HTL-40907: Build special fare tag with info icon persuasion for negotiated rate hotels.
     * Negotiated rates are the one-on-one rates that are directly negotiated between the corporate/organization and the hotel.
     *
     * @param corpAlias Organization alias name.
     * @return special fare persuasion.
     */
    @Override
    public PersuasionResponse buildSpecialFareTagWithInfoPersuasion(String corpAlias, boolean isNewSelectRoomPage) {
        // Implemented in SearchRoomsPersuasionHelper class, this method can be removed.
        return null;
    }

    /**
     * HTL-40907: Build delayed booking confirmation persuasion for negotiated rate hotels.
     * Negotiated rates are the one-on-one rates that are directly negotiated between the corporate/organization and the hotel.
     *
     * @param corpAlias Organization alias name.
     * @return confirmation text persuasion.
     */
    @Override
    public PersuasionResponse buildConfirmationTextPersuasion(String corpAlias,boolean isNewSelectRoomPage, boolean isMyBizNewDetailsPage) {
        // Implemented in SearchRoomsPersuasionHelper class, this method can be removed.
        return null;
    }

    protected void buildGroupBookingComboText(RoomDetails roomDetails, RecommendedCombo recommendedCombo,
                                              boolean baseCombo, OccupancyDetails occupancyDetails) {

        if (recommendedCombo != null && roomDetails.getBaseRoom() != null && roomDetails.getBaseRoom() && occupancyDetails != null) {
            int roomCount = occupancyDetails.getNumberOfRooms();
            String comboDisplayText = roomCount + SPACE_X_SPACE + roomDetails.getRoomName();
            roomDetails.setDisplayName(comboDisplayText);
            recommendedCombo.setBaseComboText(comboDisplayText);
        }
    }
}
