package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.core.type.TypeReference;
import com.gommt.hotels.orchestrator.enums.SubpageContext;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.*;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.FilterConfigConsul;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.enums.LocationType;
import com.mmt.hotels.clientgateway.helpers.FilterHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.FilterConfig;
import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.response.filter.*;
import com.mmt.hotels.clientgateway.response.filter.FilterPage;
import com.mmt.hotels.clientgateway.response.searchHotels.SortCriteria;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.Currency;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.FilterSearchMetaDataResponse;
import com.mmt.hotels.model.request.dpt.UserCohort;
import com.mmt.hotels.model.response.dpt.ContextualFilterResponse;
import com.mmt.hotels.model.response.dpt.InlineFilterDetails;
import com.mmt.hotels.orchestrator.enums.SubPageContext;
import com.mmt.hotels.util.PromotionalOfferType;
import com.mmt.hotels.util.Tuple;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Type;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.filterGroupsToTranslate;

@Component
public class FilterResponseTransformer {

    private static final Logger logger = LoggerFactory.getLogger(FilterResponseTransformer.class);

    @Value("${consul.enable}")
    private boolean consulFlag;

    @Value("${dpt.filters.max.items.mp}")
    private int maxDPTFiltersForMP;

    @Value("${locality.filter.pill.position}")
    private int localityFilterPillPosition;

    @Value("${filter.new.icon.url}")
    private String newIconUrl;

    @Value("#{'${filter.pill.order.mybizz}'.split(',')}")
    private List<String> pillConfigOrderMyBiz;

    @Value("${smart.filter.pill.icon}")
    private String smartFilterPillIconURL;

    @Value("${smart.filter.sheet.config}")
    private String smartFilterSheetData;

    private int supplierDealsFilterCountLimit;
    @Autowired
    FilterConfigConsul filterConfigConsul;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    @Autowired
    FilterHelper filterHelper;

    @Autowired
    PropertyManager propertyManager;
    @Autowired
    ObjectMapperUtil objectMapperUtil;
    private Map<String, List<Integer>> defaultPriceHistConfig;

    @Autowired
    private DateUtil dateUtil;

    @Autowired
    PolyglotService polyglotService;

    @Autowired
    private CommonResponseTransformer commonResponseTransformer;

    private Map<String, List<Integer>> defaultPriceHistConfigCorp;
    private Map<Integer, Set<String>> budgetHotelCityConfig;
    private Map<String, Map<String, AmenityCategory>> amenitiesCategoryConfig;
    private Map<String, Map<String, AmenityCategory>> amenitiesCategoryConfigPolyGlot;
    private FilterPricingOption pricingOptionConfigGCC;
    private List<String> roiCityList;

    @Value("${filter.view.modification.config}")
    private String filterViewTypeModificationConfig;

    @Value("${filter.categories.remove.config}")
    private String filterCategoriesRemoveConfig;

    private Map<String, Map<String,String>> filterViewTypeModificationMap;

    private Map<String, List<String>> filterCategoriesRemoveMap;

    @Value("${filter.min.items.limit}")
    private int filterMinItemsLimit;

    @Value("${filter.min.items.show}")
    private int filterMinItemsToShow;

    @Value("${filters.dynamic.pills.min}")
    private int minDynamicFilterPills;

    @Value("${filters.dynamic.pills.max}")
    private int maxDynamicFilterPills;

    @Value("${filter.categories.dpt.list.limit}")
    private String dptFiltersListLimit;

    @Value("#{'${amenities.altacco.icon}'.split(',')}")
    private List<String> altAccoAmenities;

    @Value("${international.hotels.default.price.bucket.config}")
    private String defaultPriceConfigIH;

    @Value("${international.hotels.city.wise.price.bucket.config}")
    private String cityWisePriceConfigIH;

    @Value("${international.hotels.price.bucket.config}")
    private String cityWisePriceBucketsConfigIH;

    @Value("${bnpl.apwindow.limit}")
    private int bnplApWindowLimit;

    private Map<String, List<List<Integer>>> defaultPriceConfigIHMap;

    private Map<String, HashSet<String>> cityWisePriceConfigMapIH;

    private Map<String, Map<String, List<Integer>>> cityWisePriceBucketsConfigMapIH;

    @Value("${mypartner.popular.contextualfilters.applicable}")
    private String mpContextualFiltersApplicableStr;

    List<Filter> mpContextualFiltersApplicable;

    private Map<String, Integer> dptFiltersListLimitMap;

    @Value("${bedroom.count.filter.config}")
    private String bedRoomCountFilterConfig;

    @Value("${distance.filter.sorting.modes.priority}")
    private String distanceSortingModesPriority;

    @Value("${threshold.for.hotstore.filter.count.in.mypartner.popular.filter.section}")
    private int k_thresholdForHotstoreFilterCountInMyPartnerPopularFilterSection;

    private static Map<String, Integer> distanceSortingModesPriorityMap;

    private Map<String, List<Integer>> bedRoomCountFilerListMap;

    @Autowired
    private Utility utility;

    @PostConstruct
    public void init() {

        if(consulFlag){

            try {

                bedRoomCountFilerListMap = objectMapperUtil.getObjectFromJsonWithType(bedRoomCountFilterConfig, new TypeReference<Map<String, List<Integer>>>() {
                }, DependencyLayer.CLIENTGATEWAY);

                defaultPriceHistConfig = objectMapperUtil.getObjectFromJsonWithType(filterConfigConsul.getDefaultPriceHistogram(),
                        new TypeReference<Map<String, List<Integer>>>() {
                        }, DependencyLayer.CLIENTGATEWAY);
                defaultPriceHistConfigCorp = objectMapperUtil.getObjectFromJsonWithType(filterConfigConsul.getDefaultPriceHistogramCorp(),
                        new TypeReference<Map<String, List<Integer>>>() {
                        }, DependencyLayer.CLIENTGATEWAY);
                amenitiesCategoryConfig = objectMapperUtil.getObjectFromJsonWithType(filterConfigConsul.getAmenitiesCategoryConfig(),
                        new TypeReference<Map<String, Map<String, AmenityCategory>>>() {
                        }, DependencyLayer.CLIENTGATEWAY);
                amenitiesCategoryConfigPolyGlot = objectMapperUtil.getObjectFromJsonWithType(filterConfigConsul.getAmenitiesCategoryConfigPolyGlot(),
                        new TypeReference<Map<String, Map<String, AmenityCategory>>>() {
                        }, DependencyLayer.CLIENTGATEWAY);

                pricingOptionConfigGCC = filterConfigConsul.getPricingOption();
                filterViewTypeModificationMap = objectMapperUtil.getObjectFromJsonWithType(filterViewTypeModificationConfig, new TypeReference<Map<String, Map<String, String>>>() {
                }, DependencyLayer.CLIENTGATEWAY);

                filterCategoriesRemoveMap = objectMapperUtil.getObjectFromJsonWithType(filterCategoriesRemoveConfig, new TypeReference<Map<String, List<String>>>() {
                }, DependencyLayer.CLIENTGATEWAY);

                defaultPriceConfigIHMap = objectMapperUtil.getObjectFromJsonWithType(filterConfigConsul.getIntlDefaultPriceBucketConfig(), new TypeReference<Map<String, List<List<Integer>>>>() {
                }, DependencyLayer.CLIENTGATEWAY);

                cityWisePriceConfigMapIH = objectMapperUtil.getObjectFromJsonWithType(filterConfigConsul.getIntlCityWisePriceBucketConfig(), new TypeReference<Map<String, HashSet<String>>>() {
                }, DependencyLayer.CLIENTGATEWAY);

                cityWisePriceBucketsConfigMapIH = objectMapperUtil.getObjectFromJsonWithType(filterConfigConsul.getIntlPriceBucketConfig(), new TypeReference<Map<String, Map<String, List<Integer>>>>() {
                }, DependencyLayer.CLIENTGATEWAY);

                dptFiltersListLimitMap = objectMapperUtil.getObjectFromJsonWithType(dptFiltersListLimit, new TypeReference<Map<String, Integer>>() {
                }, DependencyLayer.CLIENTGATEWAY);

                logger.debug("Fetching values from cgFilterConfig consul");

            } catch (Exception ex) {
                logger.error("Error in fetching filter config ,:{}", ex.getMessage(), ex);
            }


            budgetHotelCityConfig = commonConfigConsul.getBudgetHotelCityConfig();
            supplierDealsFilterCountLimit = commonConfigConsul.getSupplierDealsFilterCountLimit();
            roiCityList = commonConfigConsul.getRoiCityList();
        }
        else{
            FilterConfig filterConfig = propertyManager.getProperty("cgFilterConfig", FilterConfig.class);
            try {

                defaultPriceHistConfig = objectMapperUtil.getObjectFromJsonWithType(filterConfig.defaultPriceHistogram(),
                        new TypeReference<Map<String, List<Integer>>>() {
                        }, DependencyLayer.CLIENTGATEWAY);
                defaultPriceHistConfigCorp = objectMapperUtil.getObjectFromJsonWithType(filterConfig.defaultPriceHistogramCorp(),
                        new TypeReference<Map<String, List<Integer>>>() {
                        }, DependencyLayer.CLIENTGATEWAY);
                amenitiesCategoryConfig = objectMapperUtil.getObjectFromJsonWithType(filterConfig.amenitiesCategoryConfig(),
                        new TypeReference<Map<String, Map<String, AmenityCategory>>>() {
                        }, DependencyLayer.CLIENTGATEWAY);
                amenitiesCategoryConfigPolyGlot = objectMapperUtil.getObjectFromJsonWithType(filterConfig.amenitiesCategoryConfigPolyGlot(),
                        new TypeReference<Map<String, Map<String, AmenityCategory>>>() {
                        }, DependencyLayer.CLIENTGATEWAY);

                pricingOptionConfigGCC = filterConfig.pricingOption();
                filterViewTypeModificationMap = objectMapperUtil.getObjectFromJsonWithType(filterViewTypeModificationConfig, new TypeReference<Map<String, Map<String, String>>>() {
                }, DependencyLayer.CLIENTGATEWAY);

                filterCategoriesRemoveMap = objectMapperUtil.getObjectFromJsonWithType(filterCategoriesRemoveConfig, new TypeReference<Map<String, List<String>>>() {
                }, DependencyLayer.CLIENTGATEWAY);

                defaultPriceConfigIHMap = objectMapperUtil.getObjectFromJsonWithType(defaultPriceConfigIH,new TypeReference<Map<String, List<List<Integer>>>>() {
                }, DependencyLayer.CLIENTGATEWAY);

                cityWisePriceConfigMapIH = objectMapperUtil.getObjectFromJsonWithType(cityWisePriceConfigIH,new TypeReference<Map<String, HashSet<String>>>() {
                }, DependencyLayer.CLIENTGATEWAY);

                cityWisePriceBucketsConfigMapIH = objectMapperUtil.getObjectFromJsonWithType(cityWisePriceBucketsConfigIH,new TypeReference<Map<String, Map<String, List<Integer>>>>() {
                }, DependencyLayer.CLIENTGATEWAY);

                bedRoomCountFilerListMap = objectMapperUtil.getObjectFromJsonWithType(bedRoomCountFilterConfig, new TypeReference<Map<String, List<Integer>>>() {
                }, DependencyLayer.CLIENTGATEWAY);

            } catch (Exception ex) {
                logger.error("Error in fetching filter config ,:{}", ex.getMessage(), ex);
            }

            filterConfig.addPropertyChangeListener("defaultPriceHistogram", event -> {
                try {
                    defaultPriceHistConfig = objectMapperUtil.getObjectFromJsonWithType(filterConfig.defaultPriceHistogram(),
                            new TypeReference<Map<String, List<Integer>>>() {
                            }, DependencyLayer.CLIENTGATEWAY);
                } catch (Exception ex) {
                    logger.error("Error in updating filter config ,:{}", ex.getMessage(), ex);
                }
            });

            filterConfig.addPropertyChangeListener("defaultPriceHistogramCorp", event -> {
                try {
                    defaultPriceHistConfigCorp = objectMapperUtil.getObjectFromJsonWithType(filterConfig.defaultPriceHistogramCorp(),
                            new TypeReference<Map<String, List<Integer>>>() {
                            }, DependencyLayer.CLIENTGATEWAY);
                } catch (Exception ex) {
                    logger.error("Error in updating filter config corp,:{}", ex.getMessage(), ex);
                }
            });

            CommonConfig commonConfig = propertyManager.getProperty("commonConfig", CommonConfig.class);
            budgetHotelCityConfig = commonConfig.budgetHotelCityConfig();
            roiCityList = commonConfig.defaultRoiCityList();
            commonConfig.addPropertyChangeListener("budgetHotelCityConfig", evt -> budgetHotelCityConfig = commonConfig.budgetHotelCityConfig());


            filterConfig.addPropertyChangeListener("amenitiesCategoryConfig", event -> {
                try {
                    amenitiesCategoryConfig = objectMapperUtil.getObjectFromJsonWithType(filterConfig.amenitiesCategoryConfig(),
                            new TypeReference<Map<String, Map<String, AmenityCategory>>>() {
                            }, DependencyLayer.CLIENTGATEWAY);
                } catch (Exception ex) {
                    logger.error("Error in updating amenities category config config corp,:{}", ex.getMessage(), ex);
                }
            });

            filterConfig.addPropertyChangeListener("amenitiesCategoryConfigPolyGlot", event -> {
                try {
                    amenitiesCategoryConfigPolyGlot = objectMapperUtil.getObjectFromJsonWithType(filterConfig.amenitiesCategoryConfigPolyGlot(),
                            new TypeReference<Map<String, Map<String, AmenityCategory>>>() {
                            }, DependencyLayer.CLIENTGATEWAY);
                } catch (Exception ex) {
                    logger.error("Error in updating amenities category config config corp,:{}", ex.getMessage(), ex);
                }
            });

        }

        try {
            mpContextualFiltersApplicable = objectMapperUtil.getObjectFromJsonWithType(mpContextualFiltersApplicableStr,
                    new TypeReference<List<Filter>>() {
                    }, DependencyLayer.CLIENTGATEWAY);
            distanceSortingModesPriorityMap = objectMapperUtil.getObjectFromJsonWithType(distanceSortingModesPriority,
                    new TypeReference<Map<String, Integer>>() {
                    }, DependencyLayer.CLIENTGATEWAY);
        } catch (Exception ex) {
            logger.error("Error in parsing contextual filter applicable for my partner config :{}", ex.getMessage(), ex);
        }

    }

    private void translateFilterValues(FilterSearchMetaDataResponse filterResponseHES) {
        if (filterResponseHES == null || filterResponseHES.getFilterDataMap() == null) {
            return;
        }
        // Translate filter group titles
        for (FilterGroup filterGroup : filterGroupsToTranslate) {
            if (filterResponseHES.getFilterDataMap().containsKey(filterGroup)) {
                List<com.mmt.hotels.filter.Filter> filters = filterResponseHES.getFilterDataMap().get(filterGroup);
                if (CollectionUtils.isNotEmpty(filters)) {
                    for (com.mmt.hotels.filter.Filter filter : filters) {
                        if (StringUtils.isNotBlank(filter.getTitle())) {
                            String translatedTitle = polyglotService.getTranslatedData(filter.getTitle());
                            filter.setTitle(StringUtils.isNotBlank(translatedTitle) ? translatedTitle : filter.getTitle());
                        }
                    }
                }
            }
        }
    }

    private String getTranslatedFilterTitle(com.mmt.hotels.filter.Filter filter) {
        if (filter == null || filter.getFilterGroup() == null) {
            return filter != null ? filter.getTitle() : null;
        }

        if (filterGroupsToTranslate.contains(filter.getFilterGroup())) {
            if (StringUtils.isNotBlank(filter.getTitle())) {
                String translatedTitle = polyglotService.getTranslatedData(filter.getTitle());
                String finalTitle = StringUtils.isNotBlank(translatedTitle) ? translatedTitle : filter.getTitle();
                return finalTitle;
            }
        }

        return filter.getTitle();
    }

    public FilterResponse convertFilterResponse(FilterSearchMetaDataResponse filterResponseHES, FilterConfiguration filterConfig, FilterCountRequest filterRequest, LinkedHashMap<String, String> expDataMap, CommonModifierResponse commonModifierResponse, FilterPillConfigurationWrapper filterPillConfigurationWrapper) {
        if (filterConfig == null || MapUtils.isEmpty(filterConfig.getFilters())) {
            return null;
        }

        // Translate filter values before proceeding
        translateFilterValues(filterResponseHES);

        int maxFilterSize = filterConfig !=null && MapUtils.isNotEmpty(filterConfig.getRankOrder()) ? filterConfig.getRankOrder().values().stream().max(Integer::compareTo).get() : -1;
        boolean isDynamicFilterOrdering = filterConfig != null && MapUtils.isNotEmpty(filterConfig.getCategoryAttributes());
        List<FilterCategory> categories = addFilterCategories(filterResponseHES, filterRequest, expDataMap, commonModifierResponse, filterConfig.getFilters(), maxFilterSize, filterConfig.getRankOrder(), isDynamicFilterOrdering, false);

        return convertFilterResponse(categories, filterResponseHES, filterConfig.getHomestayBannerIconUrl(), filterRequest, expDataMap, commonModifierResponse, filterPillConfigurationWrapper);
    }

    public FilterResponse convertDTPWAFilterResponse(FilterSearchMetaDataResponse filterResponseHES, FilterConfigurationV2 filterConfig, FilterCountRequest filterRequest, LinkedHashMap<String, String> expDataMap, CommonModifierResponse commonModifierResponse, FilterPillConfigurationWrapper filterPillConfigurationWrapper) {
        if (filterConfig == null || MapUtils.isEmpty(filterConfig.getFilterPages())) {
            return null;
        }

        // Translate filter values before proceeding
        translateFilterValues(filterResponseHES);

        List<FilterCategory> categories = new ArrayList<>();

        com.mmt.hotels.clientgateway.businessobjects.FilterPage filterPage = filterConfig.getFilterPages().get("DT_PWA");
        if(filterPage != null && filterPage.getFilters() != null){
            boolean isDynamicFilterOrdering = MapUtils.isNotEmpty(filterConfig.getCategoryAttributes());
            categories = addFilterCategories(filterResponseHES, filterRequest, expDataMap, commonModifierResponse, filterPage.getFilters(), -1, null, isDynamicFilterOrdering, true);
        }

        return convertFilterResponse(categories, filterResponseHES, filterConfig.getHomestayBannerIconUrl(), filterRequest, expDataMap, commonModifierResponse, filterPillConfigurationWrapper);
    }

    public FilterResponse convertFilterResponse(List<FilterCategory> categories, FilterSearchMetaDataResponse filterResponseHES, String homestayBannerIconUrl, FilterCountRequest filterRequest, LinkedHashMap<String, String> expDataMap, CommonModifierResponse commonModifierResponse, FilterPillConfigurationWrapper filterPillConfigurationWrapper){
        FilterResponse filterResponse = new FilterResponse();
        filterResponse.setCurrency(filterResponseHES.getCurrency());
        filterResponse.setTotalCount(filterResponseHES.getTotalHotelsCount());
        filterResponse.setFilteredCount(filterResponseHES.getTotalHotelsCountAfterFilterApplication());
        filterResponse.setMinPrice(filterResponseHES.getMinPrice());
        filterResponse.setLimited(filterResponseHES.isLimitedResponse());
        filterResponse.setFilterList(categories);
        filterResponse.setMatchmakerFilterList(buildMatchmakerSuggestedFilterList(filterResponseHES.getMatchmakerFilterList()));
        filterResponse.setContextDetails(buildContextDetails(filterResponseHES.getContextDetails()));
        filterResponse.setPricingOption(buildPricingOption(filterRequest, pricingOptionConfigGCC));
        filterResponse.setBannerIconUrl(homestayBannerIconUrl);
        filterResponse.setHideFilterCount(!canShowFilterCount(filterRequest.getSearchCriteria().getLocationId(), expDataMap));

        if (filterRequest != null) {
            filterResponse.setPreAppliedFilters(utility.getPreAppliedFiltersForLocationId(filterRequest.getSearchCriteria(), expDataMap));
        }
        /** will be configured as per experiment **/
        boolean isMyPartnerRequest = (commonModifierResponse != null) && (commonModifierResponse.getExtendedUser() != null) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
        if (MapUtils.isNotEmpty(expDataMap) && EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(FILTER_PILL_EXP)) && !isMyPartnerRequest && filterPillConfigurationWrapper != null) {

            populateCategoryFromDPTFilters(filterResponse, filterResponseHES, filterRequest, expDataMap, FILTER_PREVIOUSLY_USED, isMyPartnerRequest);

            if (MapUtils.isNotEmpty(expDataMap) && EXP_TRUE_1.equalsIgnoreCase(expDataMap.get(HTL_POPULAR_ENABLED_EXP))) {
                populateCategoryFromDPTFilters(filterResponse, filterResponseHES, filterRequest, expDataMap, FILTER_POPULAR, isMyPartnerRequest);
            }

            /***
             // If htlPillsEnabled = 1(true), Whole DPT response will be used for filter response modification
             else if (EXP_TRUE_1.equalsIgnoreCase(expDataMap.get(HTL_PILLS_ENABLED_EXP))) {
             updateFilterListWithDptResponse(filterResponse, filterResponseHES, filterRequest, expDataMap);
             }**/

            String funnelSource = filterRequest != null && filterRequest.getRequestDetails() != null && StringUtils.isNotEmpty(filterRequest.getRequestDetails().getFunnelSource()) ? filterRequest.getRequestDetails().getFunnelSource() : StringUtils.EMPTY;
            boolean isCorp = false;
            if (filterRequest != null && filterRequest.getRequestDetails() != null && StringUtils.isNotEmpty(filterRequest.getRequestDetails().getIdContext())) {
                isCorp = CORP_ID_CONTEXT.equalsIgnoreCase(filterRequest.getRequestDetails().getIdContext());
            }
            filterResponse.setFilterPills(
                    buildFilterPills(filterResponse, filterResponseHES, filterPillConfigurationWrapper.getFilterPillConfig(), expDataMap, funnelSource, filterRequest, isCorp)
            );

            if (MapUtils.isNotEmpty(filterCategoriesRemoveMap) && filterCategoriesRemoveMap.containsKey(filterRequest.getClient())) {
                removeFilterCategoriesFromResponse(filterCategoriesRemoveMap.get(filterRequest.getClient().toUpperCase()), filterResponse);
            }

            translateSortPillData(filterPillConfigurationWrapper.getSortList(), filterResponseHES.getCityHeroPoiName());
            filterResponse.setSortList(filterPillConfigurationWrapper.getSortList());
            filterResponse.setCtaText(buildFilterCtaText(filterResponseHES.getTotalHotelsCountAfterFilterApplication(), filterRequest, expDataMap));
            if (MapUtils.isNotEmpty(filterViewTypeModificationMap) && filterViewTypeModificationMap.containsKey(filterRequest.getClient())) {
                modifyFilterCategoryViewType(filterViewTypeModificationMap.get(filterRequest.getClient().toUpperCase()), filterResponse);
            }
//            modifyShowImageUrlMergePropertyType(filterResponse.getFilterList());
            setCurrentCohortId(filterResponse, filterResponseHES.getDptContextualFilterResponse());

        }

        if (Utility.isNotGCCDayUse(filterRequest)) {
            if (CollectionUtils.isEmpty(filterResponse.getFilterList())) {
                filterResponse.setFilterList(new ArrayList<>());
            }
            Optional<FilterCategory> optionalFilterCategory = filterResponse.getFilterList().stream().filter(f -> FILTER_POPULAR.equalsIgnoreCase(f.getCategoryName())).findFirst();
            boolean isGcc = filterRequest.getRequestDetails() != null && Utility.isRegionGccOrKsa(filterRequest.getRequestDetails().getSiteDomain());
            if (optionalFilterCategory.isPresent()) {
                FilterCategory fg = optionalFilterCategory.get();
                if (fg.isVisible() && filterRequest.getSearchCriteria()!=null) {
                    if (CollectionUtils.isEmpty(fg.getFilters())) {
                        fg.setFilters(new ArrayList<>());
                    }
                    Tuple<Boolean,Boolean> showSupplierFilters = showSupplierFilters(filterRequest.getSearchCriteria().getCheckIn(),filterResponseHES, isGcc);
                    if (showSupplierFilters.getX()!=null && showSupplierFilters.getX()) {
                        Filter earlyBirdFilter = new Filter();
                        earlyBirdFilter.setFilterGroup(com.mmt.hotels.clientgateway.response.filter.FilterGroup.DEALS.name());
                        earlyBirdFilter.setFilterValue("EARLY_BIRD");
                        earlyBirdFilter.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.EARLY_BIRD_FILTER_TITLE));
                        earlyBirdFilter.setCount(null);
                        if (isGcc) {
                            int count = getSupplierFiltersCount(filterResponseHES, "EARLY_BIRD");
                            earlyBirdFilter.setCount(count);
                        }
                        fg.getFilters().add(0, earlyBirdFilter);
                    }
                    if (showSupplierFilters.getY()!=null && showSupplierFilters.getY()) {
                        Filter lastMinuteFilter = new Filter();
                        lastMinuteFilter.setFilterGroup(com.mmt.hotels.clientgateway.response.filter.FilterGroup.DEALS.name());
                        lastMinuteFilter.setFilterValue("LAST_MINUTE");
                        lastMinuteFilter.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.LAST_MINUTE_FILTER_TITLE));
                        lastMinuteFilter.setCount(null);
                        if (isGcc) {
                            int count = getSupplierFiltersCount(filterResponseHES, "LAST_MINUTE");
                            lastMinuteFilter.setCount(count);
                        }
                        fg.getFilters().add(0, lastMinuteFilter);
                    }
                }

                if (filterResponseHES.getFilterDataMap().containsKey(FilterGroup.RUSH_DEALS)) {
                    String rushDealFilterValue = "RUSH_DEALS";
                    Optional<com.mmt.hotels.filter.Filter> rushDeal = filterResponseHES.getFilterDataMap().get(FilterGroup.RUSH_DEALS).stream().filter(f-> rushDealFilterValue.equalsIgnoreCase(f.getFilterValue())).findFirst();
                    if (rushDeal.isPresent() && rushDeal.get().getCount()!=null && rushDeal.get().getCount() > supplierDealsFilterCountLimit) {
                        Filter rushDealFilter = new Filter();
                        rushDealFilter.setFilterGroup(com.mmt.hotels.clientgateway.response.filter.FilterGroup.RUSH_DEALS.name());
                        rushDealFilter.setFilterValue(rushDealFilterValue);
                        rushDealFilter.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.MMT_RUSH_DEALS_TITLE));
                        rushDealFilter.setCount(rushDeal.get().getCount());
                        fg.getFilters().add(0, rushDealFilter);
                    }
                }
                if(isMyPartnerRequest && utility.isExperimentTrue(expDataMap, Constants.MP_SUGGESTED_FILTER_ENABLED)  && fg.getFilters() != null) {
                    List<Filter> dptAppendedFilters = appendDPTContextualFiltersForMyPartner(fg.getFilters(), filterResponseHES, fg.getCategoryName());
                    if (CollectionUtils.isNotEmpty(dptAppendedFilters)) {
                        // Calculate optimal insertion index to ensure DPT filters are always visible
                        int optimalInsertIndex = calculateOptimalDPTInsertionIndex(fg, dptAppendedFilters.size());
                        fg.getFilters().addAll(optimalInsertIndex, dptAppendedFilters);
                    }
                }
                if (isGcc && filterRequest.getDeviceDetails() != null && utility.checkAppVersion(filterRequest.getDeviceDetails().getBookingDevice(), filterRequest.getDeviceDetails().getAppVersion())) {
                    String subPageContext = filterRequest.getRequestDetails() != null && filterRequest.getRequestDetails().getSubPageContext() != null ? filterRequest.getRequestDetails().getSubPageContext() : EMPTY_STRING;
                    modifyFilterPillsGCCOffers(filterResponse, subPageContext);
                }
                if(MapUtils.isNotEmpty(expDataMap) && TRUE.equalsIgnoreCase(expDataMap.get(RUSH_DEALS_MMT_EXP_KEY)))
                    addRushDealFilterPill(filterResponse);

                if(MapUtils.isNotEmpty(expDataMap) && TRUE.equalsIgnoreCase(expDataMap.get(ExperimentKeys.FREE_STAY_FOR_KIDS_MMT_EXP_KEY.getKey())))
                    addFreeKidStayFilterPill(filterResponse);
            }


        }

        modifyFilterPills(filterResponse, filterResponseHES);

        if(MapUtils.isNotEmpty(expDataMap) && TRUE.equalsIgnoreCase(expDataMap.get(ExperimentKeys.EXP_OFFERS_UI_REDESIGN.getKey()))) {
            addOffersFilterPillIfApplicable(filterResponse, false);
        }

        // Ensure smart filter pill is always added at sequence 3 before returning response
        addSmartFilterPill(filterResponse, expDataMap, filterRequest);

        sortChainInfoByName(filterResponse);
        return filterResponse;
    }

    public FilterResponse convertFilterResponseV2(FilterSearchMetaDataResponse filterResponseHES, FilterConfigurationV2 filterConfig, FilterCountRequest filterRequest, LinkedHashMap<String, String> expDataMap, CommonModifierResponse commonModifierResponse, FilterPillConfigurationWrapper filterPillConfigurationWrapper) {
        if (filterConfig == null || MapUtils.isEmpty(filterConfig.getFilterPages())) {
            return null;
        }

        // Translate filter values before proceeding
        translateFilterValues(filterResponseHES);

        FilterResponse filterResponse = new FilterResponse();
        filterResponse.setCurrency(filterResponseHES.getCurrency());
        filterResponse.setTotalCount(filterResponseHES.getTotalHotelsCount());
        filterResponse.setFilteredCount(filterResponseHES.getTotalHotelsCountAfterFilterApplication());
        filterResponse.setMinPrice(filterResponseHES.getMinPrice());
        filterResponse.setLimited(filterResponseHES.isLimitedResponse());
        filterResponse.setFilterListV2(addFilterPages(filterResponseHES, filterConfig, filterRequest, expDataMap, commonModifierResponse));
        filterResponse.setMatchmakerFilterList(buildMatchmakerSuggestedFilterList(filterResponseHES.getMatchmakerFilterList()));
        filterResponse.setContextDetails(buildContextDetails(filterResponseHES.getContextDetails()));
        filterResponse.setPricingOption(buildPricingOption(filterRequest, pricingOptionConfigGCC));
        filterResponse.setBannerIconUrl(filterConfig.getHomestayBannerIconUrl());
        filterResponse.setHideFilterCount(!canShowFilterCount(filterRequest.getSearchCriteria().getLocationId(), expDataMap));
        if (filterRequest != null) {
            filterResponse.setPreAppliedFilters(utility.getPreAppliedFiltersForLocationId(filterRequest.getSearchCriteria(), expDataMap));
        }
        /** will be configured as per experiment **/
        boolean isMyPartnerRequest = (commonModifierResponse != null) && (commonModifierResponse.getExtendedUser() != null) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
        if (MapUtils.isNotEmpty(expDataMap) && EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(FILTER_PILL_EXP)) && !isMyPartnerRequest && filterPillConfigurationWrapper != null) {

            populateCategoryFromDPTFiltersV2(filterResponse, filterResponseHES, filterRequest, expDataMap, FILTER_PREVIOUSLY_USED, isMyPartnerRequest);

            // uncomment below lines if dpt filter support is need to be enabled for filter v2 ui
//            if (MapUtils.isNotEmpty(expDataMap) && EXP_TRUE_1.equalsIgnoreCase(expDataMap.get(HTL_POPULAR_ENABLED_EXP))) {
//                populateCategoryFromDPTFiltersV2(filterResponse, filterResponseHES, filterRequest, expDataMap, FILTER_POPULAR, isMyPartnerRequest);
//            }

            /***
             // If htlPillsEnabled = 1(true), Whole DPT response will be used for filter response modification
             else if (EXP_TRUE_1.equalsIgnoreCase(expDataMap.get(HTL_PILLS_ENABLED_EXP))) {
             updateFilterListWithDptResponse(filterResponse, filterResponseHES, filterRequest, expDataMap);
             }**/

            String funnelSource = filterRequest != null && filterRequest.getRequestDetails() != null && StringUtils.isNotEmpty(filterRequest.getRequestDetails().getFunnelSource()) ? filterRequest.getRequestDetails().getFunnelSource() : StringUtils.EMPTY;

            filterResponse.setFilterPills(
                    buildFilterPills(filterResponse, filterResponseHES, filterPillConfigurationWrapper.getFilterPillConfig(), expDataMap, funnelSource, filterRequest, CORP_ID_CONTEXT.equalsIgnoreCase(filterRequest.getRequestDetails().getIdContext()))
            );

            if (MapUtils.isNotEmpty(filterCategoriesRemoveMap) && filterCategoriesRemoveMap.containsKey(filterRequest.getClient())) {
                removeFilterCategoriesFromResponse(filterCategoriesRemoveMap.get(filterRequest.getClient().toUpperCase()), filterResponse);
            }

            translateSortPillData(filterPillConfigurationWrapper.getSortList(), filterResponseHES.getCityHeroPoiName());
            filterResponse.setSortList(filterPillConfigurationWrapper.getSortList());
            filterResponse.setCtaText(buildFilterCtaText(filterResponseHES.getTotalHotelsCountAfterFilterApplication(), filterRequest, expDataMap));
            if (MapUtils.isNotEmpty(filterViewTypeModificationMap) && filterViewTypeModificationMap.containsKey(filterRequest.getClient())) {
                modifyFilterCategoryViewType(filterViewTypeModificationMap.get(filterRequest.getClient().toUpperCase()), filterResponse);
            }
//            modifyShowImageUrlMergePropertyType(filterResponse.getFilterList());
            setCurrentCohortId(filterResponse, filterResponseHES.getDptContextualFilterResponse());

            if(MapUtils.isNotEmpty(expDataMap) && TRUE.equalsIgnoreCase(expDataMap.get(ExperimentKeys.EXP_OFFERS_UI_REDESIGN.getKey()))) {
                addOffersFilterPillIfApplicable(filterResponse, true);
            }
        }

        if (Utility.isNotGCCDayUse(filterRequest)) {
            FilterPage page = filterResponse.getFilterListV2().stream().filter(f -> f.getPageId().equalsIgnoreCase(PAGE_SUGGESTED_FOR_YOU)).findFirst().orElse(null);
            Optional<FilterCategory> optionalFilterCategory = Optional.empty();
            if (page != null) {
                optionalFilterCategory = page.getCategories().stream().filter(f -> FILTER_POPULAR.equalsIgnoreCase(f.getCategoryName())).findFirst();
            }
            boolean isGcc = filterRequest.getRequestDetails() != null && Constants.AE.equalsIgnoreCase(filterRequest.getRequestDetails().getSiteDomain());
            if (optionalFilterCategory.isPresent()) {
                if (isGcc && filterRequest.getDeviceDetails() != null && utility.checkAppVersion(filterRequest.getDeviceDetails().getBookingDevice(), filterRequest.getDeviceDetails().getAppVersion())) {
                    String subPageContext = filterRequest.getRequestDetails() != null && filterRequest.getRequestDetails().getSubPageContext() != null ? filterRequest.getRequestDetails().getSubPageContext() : EMPTY_STRING;
                    modifyFilterPillsGCCOffers(filterResponse, subPageContext);
                }
                if(MapUtils.isNotEmpty(expDataMap) && TRUE.equalsIgnoreCase(expDataMap.get(RUSH_DEALS_MMT_EXP_KEY)))
                    addRushDealFilterPill(filterResponse);

                if(MapUtils.isNotEmpty(expDataMap) && TRUE.equalsIgnoreCase(expDataMap.get(ExperimentKeys.FREE_STAY_FOR_KIDS_MMT_EXP_KEY.getKey())))
                    addFreeKidStayFilterPill(filterResponse);
            }


        }
        modifyFilterPills(filterResponse, filterResponseHES);

        // Ensure smart filter pill is always added at sequence 3 before returning response
        addSmartFilterPill(filterResponse, expDataMap, filterRequest);

        sortChainInfoByName(filterResponse);
        return filterResponse;
    }

    private void modifyFilterPills(FilterResponse filterResponse, FilterSearchMetaDataResponse filterResponseHES) {
        FilterPill filterPill = filterResponseHES.getFilterPills() != null ? extractFilterPillFromHES(filterResponseHES) : null;

        if (filterPill == null) {
            return;
        }

        int insertPosition = findInsertPosition(filterResponse.getFilterPills());

        if (insertPosition != -1 && insertPosition <= filterResponse.getFilterPills().size()) {
            filterResponse.getFilterPills().add(insertPosition, filterPill);
        }
    }

    private FilterPill extractFilterPillFromHES(FilterSearchMetaDataResponse filterResponseHES) {
        com.mmt.hotels.filter.FilterPill ihPropertyFilterPill = filterResponseHES.getFilterPills().get(IH_PROPERTY_FILTER_PILL);
        if (ihPropertyFilterPill != null && ihPropertyFilterPill.getType().equals(FILTER) && CollectionUtils.isNotEmpty(ihPropertyFilterPill.getFilters())) {
            Filter ihPropertyFilter = getIHPropertyFilter(ihPropertyFilterPill.getFilters().get(0));
            FilterPill filterPill = new FilterPill();
            filterPill.setId(IH_PROPERTY_FILTER_PILL_ID);
            filterPill.setTitle(ihPropertyFilter.getTitle());
            filterPill.setType(FILTER);
            filterPill.setCategories(new ArrayList<>());
            filterPill.setPillFilter(ihPropertyFilter);
            filterPill.setPillType(null);
            return filterPill;
        }
        return null;
    }

    private Filter getIHPropertyFilter(com.mmt.hotels.filter.Filter filter) {
        Filter ihPropertyFilter = new Filter();
        ihPropertyFilter.setFilterGroup(filter.getFilterGroup().name());
        ihPropertyFilter.setFilterValue(filter.getFilterValue());
        ihPropertyFilter.setCount(filter.getCount());
        ihPropertyFilter.setTitle(getTranslatedFilterTitle(filter));
        ihPropertyFilter.setFilterExists(filter.getFilterExist());
        ihPropertyFilter.setQuantityFilter(filter.isQuantityFilter());
        ihPropertyFilter.setRangeFilter(filter.isRangeFilter());
        return ihPropertyFilter;
    }

    private int findInsertPosition(List<FilterPill> filterPills) {
        int rushDealsIndex = findFilterPillIndex(filterPills, RUSH_DEAL_FILTER_PILL_ID);
        if (rushDealsIndex != -1) {
            return rushDealsIndex + 1;
        }

        int lastMinDealIndex = findFilterPillIndex(filterPills, LAST_MIN_DEAL_FILTER_PILL_ID);
        if(lastMinDealIndex != -1){
            return lastMinDealIndex + 1;
        }

        int earlyBirdDealIndex = findFilterPillIndex(filterPills, EARLY_BIRD_DEAL_FILTER_PILL_ID);
        if(earlyBirdDealIndex != -1){
            return earlyBirdDealIndex + 1;
        }

        int allFiltersIndex = findFilterPillIndex(filterPills, ALL_FILTERS_FILTER_PILL_ID);
        if (allFiltersIndex != -1) {
            return allFiltersIndex + 1;
        }

        return 0;
    }

    private int findFilterPillIndex(List<FilterPill> filterPills, String pillId) {
        if (filterPills == null || pillId == null) {
            return -1;
        }

        return IntStream.range(0, filterPills.size())
                .filter(i -> {
                    FilterPill pill = filterPills.get(i);
                    return pill != null && pillId.equals(pill.getId());
                })
                .findFirst()
                .orElse(-1);
    }

    private void modifyFilterPillsGCCOffers(FilterResponse filterResponse, String subPageContext) {
        if (filterResponse != null && CollectionUtils.isNotEmpty(filterResponse.getFilterPills())) {
            List<FilterCategory> filterList = getPopularFilterCategoryForGCC(filterResponse);
            if (filterList == null) {
                return;
            }

            boolean lastMinuteFilterPresent = false;
            boolean earlyBirdFilterPresent = false;
            Filter lastMinuteDeal = null;
            Filter earlyBirdDeal = null;
            for (FilterCategory filterCategory : filterList) {
                List<Filter> filters = filterCategory.getFilters();
                if (CollectionUtils.isNotEmpty(filters)) {
                    for (Filter filter : filters) {
                        if ("LAST_MINUTE".equalsIgnoreCase(filter.getFilterValue()) && filter.getCount() >= supplierDealsFilterCountLimit) {
                            lastMinuteFilterPresent = true;
                            lastMinuteDeal = filter;
                        }
                        if ("EARLY_BIRD".equalsIgnoreCase(filter.getFilterValue()) && filter.getCount() >= supplierDealsFilterCountLimit) {
                            earlyBirdFilterPresent = true;
                            earlyBirdDeal = filter;
                        }
                    }
                }
            }
            FilterPill filterPill = null;
            if (lastMinuteFilterPresent && !subPageContext.equalsIgnoreCase(SubpageContext.LISTING_LMD.getName())) {
                filterPill = new FilterPill();
                filterPill.setId(LAST_MIN_DEAL_FILTER_PILL_ID);
                filterPill.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.LAST_MIN_DEAL_TITLE));
                filterPill.setType(FILTER);
                filterPill.setSequence(2);
                filterPill.setCategories(new ArrayList<>());
                filterPill.setPillFilter(lastMinuteDeal);
                filterPill.setPillType(null);
                filterPill.setIcon("https://promos.makemytrip.com/GCC/MiscIcons/Lastmindeal%201.gif");
            } else if (earlyBirdFilterPresent) {
                filterPill = new FilterPill();
                filterPill.setId(EARLY_BIRD_DEAL_FILTER_PILL_ID);
                filterPill.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.EARLY_BIRD_DEAL_TITLE));
                filterPill.setType(FILTER);
                filterPill.setSequence(2);
                filterPill.setCategories(new ArrayList<>());
                filterPill.setPillFilter(earlyBirdDeal);
                filterPill.setPillType(null);
                filterPill.setIcon("https://promos.makemytrip.com/GCC/MiscIcons/Lastmindeal%201.gif");
            }
            if (filterPill != null) {
                filterResponse.getFilterPills().add(2, filterPill);
            }
        }
    }

    private void addRushDealFilterPill(FilterResponse filterResponse) {
        if (filterResponse != null && CollectionUtils.isNotEmpty(filterResponse.getFilterPills())) {
            List<FilterCategory> filterList = getPopularFilterCategoryForGCC(filterResponse);
            if (filterList == null) {
                return;
            }
            boolean rushDealsFilterPresent = false;
            Filter rushDeal = null;
            for (FilterCategory filterCategory : filterList) {
                List<Filter> filters = filterCategory.getFilters();
                if (CollectionUtils.isNotEmpty(filters)) {
                    for (Filter filter : filters) {
                        if ("RUSH_DEALS".equalsIgnoreCase(filter.getFilterValue()) && filter.getCount() != null && filter.getCount() > 0) {
                            rushDealsFilterPresent = true;
                            rushDeal = filter;
                        }
                    }
                }
            }
            FilterPill filterPill = null;
            if (rushDealsFilterPresent) {
                filterPill = new FilterPill();
                filterPill.setId(RUSH_DEAL_FILTER_PILL_ID);
                filterPill.setTitle(polyglotService.getTranslatedData(RUSH_DEAL_MMT_FILTER_TITLE_TEXT));
                filterPill.setType(FILTER);
                filterPill.setSequence(2);
                filterPill.setCategories(new ArrayList<>());
                filterPill.setPillFilter(rushDeal);
                filterPill.setPillType(null);
                filterPill.setIcon("https://promos.makemytrip.com/images/CDN_upload/timern.gif");
            }

            if (filterPill != null) {
                filterResponse.getFilterPills().add(2, filterPill);
            }
        }
    }

    private void addFreeKidStayFilterPill(FilterResponse filterResponse) {
        if (filterResponse != null && CollectionUtils.isNotEmpty(filterResponse.getFilterPills())) {
            int FILTER_PILL_INDEX = 2;
            List<FilterCategory> filterList = getPopularFilterCategoryForGCC(filterResponse);
            if (filterList == null) {
                return;
            }
            boolean freeStayForKid = false;
            Filter freeStayForKidFilter = null;
            for (FilterCategory filterCategory : filterList) {
                List<Filter> filters = filterCategory.getFilters();
                if (CollectionUtils.isNotEmpty(filters)) {
                    for (Filter filter : filters) {
                        if (FREE_STAY_FOR_KIDS_FILTER_VALUE.equalsIgnoreCase(filter.getFilterValue()) && filter.getCount() != null && filter.getCount() > 0) {
                            freeStayForKid = true;
                            freeStayForKidFilter = filter;
                        }
                    }
                }
            }
            FilterPill filterPill = null;
            if (freeStayForKid) {
                filterPill = new FilterPill();
                filterPill.setId(FREE_KID_STAY_FILTER_PILL_ID);
                filterPill.setTitle(polyglotService.getTranslatedData(FREE_KIDS_FILTER_TITLE));
                filterPill.setType(FILTER);
                filterPill.setSequence(2);
                filterPill.setCategories(new ArrayList<>());
                filterPill.setPillFilter(freeStayForKidFilter);
            }

            if (filterPill != null) {
                filterResponse.getFilterPills().add(FILTER_PILL_INDEX, filterPill);
            }
        }
    }

    public List<FilterCategory> getPopularFilterCategoryForGCC(FilterResponse filterResponse) {
        List<FilterCategory> filterList = null;
        if (CollectionUtils.isNotEmpty(filterResponse.getFilterList())) {
            filterList = filterResponse.getFilterList();
        }
        else if (filterResponse.getFilterListV2() != null) {
            FilterPage page = filterResponse.getFilterListV2().stream().filter(f -> f.getPageId().equalsIgnoreCase(FILTER_DEALS)).findFirst().orElse(null);
            if (page == null || page.getCategories() == null) {
                return null;
            }
            filterList = page.getCategories();
        }
        return filterList;
    }

    private void addOffersFilterPillIfApplicable(FilterResponse filterResponse, boolean isV2) {
        if (filterResponse == null ||
                CollectionUtils.isEmpty(filterResponse.getFilterPills()) ||
                (!isV2 && CollectionUtils.isEmpty(filterResponse.getFilterList())) ||
                (isV2 && CollectionUtils.isEmpty(filterResponse.getFilterListV2()))) {
            return;
        }
        String categoryName = null;
        String title = null;
        int total = 0;

        if (isV2) {
            FilterPage offersFilterCategory = filterResponse.getFilterListV2().stream()
                    .filter(filterCategory -> OFFERS_FILTER_CATEGORY.equalsIgnoreCase(filterCategory.getPageId()))
                    .findFirst()
                    .orElse(null);
            if (offersFilterCategory == null || CollectionUtils.isEmpty(offersFilterCategory.getCategories())) {
                return;
            }
            total = offersFilterCategory.getCategories().stream()
                    .filter(filter -> filter.getFilters() != null && CollectionUtils.isNotEmpty(filter.getFilters()))
                    .flatMap(filter -> filter.getFilters().stream())
                    .filter(f -> f.getCount() != null)
                    .mapToInt(f -> 1)
                    .sum();
            categoryName = offersFilterCategory.getPageId();
            title = offersFilterCategory.getTitle();
        } else {
            FilterCategory offersFilterCategory = filterResponse.getFilterList().stream()
                    .filter(filterCategory -> OFFERS_FILTER_CATEGORY.equalsIgnoreCase(filterCategory.getCategoryName()))
                    .findFirst()
                    .orElse(null);
            if (offersFilterCategory == null || CollectionUtils.isEmpty(offersFilterCategory.getFilters())) {
                return;
            }
            total = (int) offersFilterCategory.getFilters().stream()
                    .filter(filter -> filter.getCount() != null && filter.getCount() != 0)
                    .count();
            categoryName = offersFilterCategory.getCategoryName();
            title = offersFilterCategory.getTitle();
        }

        if (total > 0) {
            FilterPill pill = new FilterPill();
            pill.setId(title);
            pill.setTitle(title);
            ArrayList<String> categories = new ArrayList<>();
            categories.add(categoryName);
            pill.setCategories(categories);

            int insertIndex = (total > 1)
                    ? Math.min(1, filterResponse.getFilterPills().size())
                    : filterResponse.getFilterPills().size();
            filterResponse.getFilterPills().add(insertIndex, pill);
        }
    }


    private int getSupplierFiltersCount(FilterSearchMetaDataResponse filterResponseHES, String dealType) {
        if (filterResponseHES.getFilterDataMap().containsKey(FilterGroup.DEALS)) {
            if ("EARLY_BIRD".equalsIgnoreCase(dealType)) {
                Optional<com.mmt.hotels.filter.Filter> lastMinute = filterResponseHES.getFilterDataMap().get(FilterGroup.DEALS).stream()
                        .filter(f -> PromotionalOfferType.EARLY_BIRD.getName().equalsIgnoreCase(f.getFilterValue())).findFirst();
                if (lastMinute.isPresent() && lastMinute.get().getCount() != null) {
                    return lastMinute.get().getCount();
                }
            }
            if ("LAST_MINUTE".equalsIgnoreCase(dealType)) {
                Optional<com.mmt.hotels.filter.Filter> lastMinute = filterResponseHES.getFilterDataMap().get(FilterGroup.DEALS).stream()
                        .filter(f -> PromotionalOfferType.LAST_MINUTE.getName().equalsIgnoreCase(f.getFilterValue())).findFirst();
                if (lastMinute.isPresent() && lastMinute.get().getCount() != null) {
                    return lastMinute.get().getCount();
                }
            }
        }
        return 0;
    }

    private void sortChainInfoByName(FilterResponse filterResponse) {
        if (filterResponse != null && CollectionUtils.isNotEmpty(filterResponse.getFilterList())) {
            filterResponse.getFilterList().forEach(filterCategory -> {
                if (BRAND_FILTER.equalsIgnoreCase(filterCategory.getCategoryName()) && CollectionUtils.isNotEmpty(filterCategory.getFilters())) {
                    filterCategory.getFilters().removeIf(filter -> filter.getTitle() == null);
                    filterCategory.getFilters().sort(Comparator.comparing(Filter::getTitle));
                }
            });
        }
    }

    /**
     * @param filterResponse
     * @param filterResponseHES
     * @param filterCountRequest
     * @param expDataMap
     * @param filterCategoryName
     *
     * This method populates filter groups and values from primaryFilterValueMap received from DPT in filter list
     * Method is generic and accepts category_name which will be present as key in primaryFilterValueMap
     * Previously used filter is added at index 0 for every case
     * For other filter categories, filter list is replaced with DPT filters
     *
     */

    private boolean populateCategoryFromDPTFilters(FilterResponse filterResponse, FilterSearchMetaDataResponse filterResponseHES,
                                                   FilterCountRequest filterCountRequest, LinkedHashMap<String, String> expDataMap,
                                                   String filterCategoryName, boolean isMyPartnerRequest) {
        if (filterResponseHES != null && filterResponseHES.getDptContextualFilterResponse() != null
                && filterResponse != null && CollectionUtils.isNotEmpty(filterResponse.getFilterList())) {

            ContextualFilterResponse dptContextualFilterResponse = filterResponseHES.getDptContextualFilterResponse();

            //Check if filter category is present in primary filter value map in DPT response
            if (MapUtils.isNotEmpty(dptContextualFilterResponse.getPrimaryFilterValueMap()) && dptContextualFilterResponse.getPrimaryFilterValueMap().containsKey(filterCategoryName)) {
                FilterConfigCategory filterConfigCategory = new FilterConfigCategory();
                FilterCategory filterCategory = new FilterCategory();
                transformDptFilterToCgFilter(filterResponseHES.getFilterDataMap(), filterCategory, dptContextualFilterResponse.getPrimaryFilterValueMap().get(filterCategoryName));
                filterConfigCategory.setTitle(filterCategoryName);
                String filterCategoryText = updateCategoryTitle(filterCategoryName, filterConfigCategory, filterCountRequest, filterResponseHES, expDataMap, isMyPartnerRequest);
                filterCategory.setTitle(StringUtils.isNotBlank(filterCategoryText) ? filterCategoryText : filterCategoryName);
                filterCategory.setCategoryName(filterCategoryName);
                filterCategory.setVisible(true);

                //If filters from DPT are not empty then set previously used filters at index 0 and rest filter categories at their respective positions
                if (CollectionUtils.isNotEmpty(filterCategory.getFilters())) {
                    //Adding Previously used filter category at index 0 else set filters at their respective positions
                    if (FILTER_PREVIOUSLY_USED.equalsIgnoreCase(filterCategoryName)) {
                        filterResponse.getFilterList().add(0, filterCategory);
                    } else {
                        int index = IntStream.range(0, filterResponse.getFilterList().size())
                                .filter(i -> filterResponse.getFilterList().get(i) != null && filterCategoryName.equalsIgnoreCase(filterResponse.getFilterList().get(i).getCategoryName()))
                                .findFirst()
                                .orElse(-1);
                        if (index != -1) {
                            filterResponse.getFilterList().set(index, filterCategory);
                            if (dptFiltersListLimitMap.containsKey(filterCategoryName)) {
                                filterCategory.setFilters(filterCategory.getFilters().subList(0, Math.min(dptFiltersListLimitMap.get(filterCategoryName), filterCategory.getFilters().size())));
                            }
                        }
                    }
                    //Update min items to be shown in filter lis basis config
                    updateMinItemsToShowAndShowMore(filterCategory);
                    return true;
                }

            }
        }
        return false;
    }

    private boolean populateCategoryFromDPTFiltersV2(FilterResponse filterResponse, FilterSearchMetaDataResponse filterResponseHES,
                                                   FilterCountRequest filterCountRequest, LinkedHashMap<String, String> expDataMap,
                                                   String filterCategoryName, boolean isMyPartnerRequest) {
        if (filterResponseHES != null && filterResponseHES.getDptContextualFilterResponse() != null
                && filterResponse != null && CollectionUtils.isNotEmpty(filterResponse.getFilterListV2())) {

            ContextualFilterResponse dptContextualFilterResponse = filterResponseHES.getDptContextualFilterResponse();

            //Check if filter category is present in primary filter value map in DPT response
            if (MapUtils.isNotEmpty(dptContextualFilterResponse.getPrimaryFilterValueMap()) && dptContextualFilterResponse.getPrimaryFilterValueMap().containsKey(filterCategoryName)) {
                FilterConfigCategory filterConfigCategory = new FilterConfigCategory();
                FilterCategory filterCategory = new FilterCategory();
                transformDptFilterToCgFilter(filterResponseHES.getFilterDataMap(), filterCategory, dptContextualFilterResponse.getPrimaryFilterValueMap().get(filterCategoryName));
                filterConfigCategory.setTitle(filterCategoryName);
                String filterCategoryText = updateCategoryTitle(filterCategoryName, filterConfigCategory, filterCountRequest, filterResponseHES, expDataMap, isMyPartnerRequest);
                filterCategory.setTitle(StringUtils.isNotBlank(filterCategoryText) ? filterCategoryText : filterCategoryName);
                filterCategory.setCategoryName(filterCategoryName);
                filterCategory.setVisible(true);
                filterCategory.setViewTypeV2(VIEW_TYPE_CHECKBOX);

                //If filters from DPT are not empty then set previously used filters at index 0 and rest filter categories at their respective positions
                if (CollectionUtils.isNotEmpty(filterCategory.getFilters())) {
                    //Adding Previously used filter category at index 0 else set filters at their respective positions
                    if (FILTER_PREVIOUSLY_USED.equalsIgnoreCase(filterCategoryName)) {
                        for (FilterPage page : filterResponse.getFilterListV2()) {
                            if (page.getPageId().equalsIgnoreCase(PAGE_SUGGESTED_FOR_YOU)) {
                                page.getCategories().add(0, filterCategory);
                            }
                        }
                    } else {
                        for (FilterPage page : filterResponse.getFilterListV2()) {
                            if (CollectionUtils.isEmpty(page.getCategories())) {
                                continue;
                            }
                            int index = IntStream.range(0, page.getCategories().size())
                                    .filter(i -> page.getCategories().get(i) != null && filterCategoryName.equalsIgnoreCase(page.getCategories().get(i).getCategoryName()))
                                    .findFirst()
                                    .orElse(-1);
                            if (index != -1) {
                                page.getCategories().set(index, filterCategory);
                                if (dptFiltersListLimitMap.containsKey(filterCategoryName)) {
                                    filterCategory.setFilters(filterCategory.getFilters().subList(0, Math.min(dptFiltersListLimitMap.get(filterCategoryName), filterCategory.getFilters().size())));
                                }
                                break;
                            }
                        }
                    }
                    //Update min items to be shown in filter lis basis config
                    updateMinItemsToShowAndShowMore(filterCategory);
                    return true;
                }

            }
        }
        return false;
    }

    /**
     * Method to add only previously used section from DPT response to the filter response
     */
    private void populatePreviouslyUsedFilters(FilterResponse filterResponse, FilterSearchMetaDataResponse filterResponseHES,
                                               FilterCountRequest filterRequest, LinkedHashMap<String, String> expDataMap) {
        if (filterResponseHES != null && filterResponseHES.getDptContextualFilterResponse() != null
                && CollectionUtils.isNotEmpty(filterResponseHES.getDptContextualFilterResponse().getFilterCategories())
                && filterResponse != null && CollectionUtils.isNotEmpty(filterResponse.getFilterList())) {
            ContextualFilterResponse dptContextualFilterResponse = filterResponseHES.getDptContextualFilterResponse();
            String previouslyUsedFilterCategory = FILTER_PREVIOUSLY_USED;
            if (CollectionUtils.isNotEmpty(dptContextualFilterResponse.getFilterCategories()) && dptContextualFilterResponse.getFilterCategories().contains(previouslyUsedFilterCategory)
                    && MapUtils.isNotEmpty(dptContextualFilterResponse.getPrimaryFilterValueMap()) && dptContextualFilterResponse.getPrimaryFilterValueMap().containsKey(previouslyUsedFilterCategory)) {
                FilterConfigCategory filterConfigCategory = new FilterConfigCategory();
                FilterCategory filterCategory = new FilterCategory();
                transformDptFilterToCgFilter(filterResponseHES.getFilterDataMap(), filterCategory, dptContextualFilterResponse.getPrimaryFilterValueMap().get(previouslyUsedFilterCategory));
                filterConfigCategory.setTitle(previouslyUsedFilterCategory);
                filterCategory.setCategoryName(previouslyUsedFilterCategory);
                previouslyUsedFilterCategory = updateCategoryTitle(previouslyUsedFilterCategory, filterConfigCategory, filterRequest, filterResponseHES, expDataMap, false);
                filterCategory.setTitle(previouslyUsedFilterCategory);
                filterCategory.setVisible(true);
                if (CollectionUtils.isNotEmpty(filterCategory.getFilters())) {
                    //Adding Previously used filter category on top position
                    filterResponse.getFilterList().add(0, filterCategory);
                }
                updateMinItemsToShowAndShowMore(filterCategory);
            }
        }
    }

    /**
     * Set the currentCohortId calculated based on the current searchCriteria, filter
     * and other required factors used to calculate the userCohortId
     */
    private void setCurrentCohortId(FilterResponse filterResponse, ContextualFilterResponse dptContextualFilterResponse) {
        if (dptContextualFilterResponse != null && dptContextualFilterResponse.getCurrentCohortId() != null) {
            filterResponse.setCurrentCohortId(dptContextualFilterResponse.getCurrentCohortId());
        }
    }

    /**
     * This method will take the intersection of FilterResponse#filterList and ContextualFilterResponse#filterCategories (received from DPT)
     * based on FilterCategory#categoryName and retain the final filters to be shown in FilterResponse#filterList
     */
    private void updateFilterListWithDptResponse(FilterResponse filterResponse, FilterSearchMetaDataResponse filterResponseHES,
                                                 FilterCountRequest filterRequest, LinkedHashMap<String, String> expDataMap) {
        if (filterResponseHES != null && filterResponseHES.getDptContextualFilterResponse() != null
                && CollectionUtils.isNotEmpty(filterResponseHES.getDptContextualFilterResponse().getFilterCategories())
                && filterResponse != null && CollectionUtils.isNotEmpty(filterResponse.getFilterList())) {

            ContextualFilterResponse dptContextualFilterResponse = filterResponseHES.getDptContextualFilterResponse();

            Map<FilterGroup, List<com.mmt.hotels.filter.Filter>> filterDataMap = filterResponseHES.getFilterDataMap();

            LinkedHashMap<String, List<com.mmt.hotels.filter.Filter>> primaryFilterValueMapDpt = dptContextualFilterResponse.getPrimaryFilterValueMap();

            if (CollectionUtils.isNotEmpty(dptContextualFilterResponse.getFilterCategories())) {
                List<FilterCategory> filterCategoriesBasedOnDpt = dptContextualFilterResponse.getFilterCategories().stream()
                        .map(filterCategoryDpt -> {
                                    // If filterCategory received from DPT, have corresponding filters in primaryFilterValueMapDpt then,
                                    // take those and add them to final response
                                    if (MapUtils.isNotEmpty(primaryFilterValueMapDpt) && primaryFilterValueMapDpt.containsKey(filterCategoryDpt)) {
                                        FilterConfigCategory filterConfigCategory = new FilterConfigCategory();
                                        FilterCategory filterCategory = new FilterCategory();
                                        filterConfigCategory.setTitle(filterCategoryDpt);
                                        transformDptFilterToCgFilter(filterDataMap, filterCategory, primaryFilterValueMapDpt.get(filterCategoryDpt));
                                        filterCategory.setCategoryName(filterCategoryDpt);
                                        filterCategoryDpt = updateCategoryTitle(filterCategoryDpt, filterConfigCategory, filterRequest, filterResponseHES, expDataMap, false);
                                        filterCategory.setTitle(filterCategoryDpt);
                                        return Collections.singletonList(filterCategory);
                                    }
                                    // Else check that category, in all categories prepared earlier and add them to final filter response
                                    else {
                                        String finalFilterCategoryDpt = filterCategoryDpt;
                                        return filterResponse.getFilterList().stream()
                                                .filter(filterCategoryHES -> StringUtils.equalsIgnoreCase(filterCategoryHES.getCategoryName(), finalFilterCategoryDpt))
                                                .collect(Collectors.toList());
                                    }
                                }
                        ).flatMap(Collection::stream).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(filterCategoriesBasedOnDpt)) {
                    filterResponse.setFilterList(filterCategoriesBasedOnDpt);
                }
            }
        }
    }

    /**
     * Method to translate and populate the filters coming from DPT with count greater than 0
     */
    private void transformDptFilterToCgFilter(Map<FilterGroup, List<com.mmt.hotels.filter.Filter>> filterDataMap, FilterCategory filterCategory,
                                              List<com.mmt.hotels.filter.Filter> filtersDPT) {
        if (CollectionUtils.isNotEmpty(filtersDPT) && MapUtils.isNotEmpty(filterDataMap) && filterCategory != null) {
            for (com.mmt.hotels.filter.Filter filterFromDpt : filtersDPT) {
                // Get the filter response coming from HES
                List<com.mmt.hotels.filter.Filter> filtersHES = filterDataMap.get(filterFromDpt.getFilterGroup());
                if (CollectionUtils.isNotEmpty(filtersHES)) {
                    // Get the HES filter with exactly same filterGroup and filterValue to that of DPT filter
                    filtersHES.stream()
                            .filter(filterFromHes -> (StringUtils.equalsIgnoreCase(filterFromHes.getFilterValue(), filterFromDpt.getFilterValue()) && StringUtils.isNotEmpty(filterFromDpt.getTitle())))
                            .findFirst().ifPresent(existingFilter -> {
                                filterFromDpt.setCount(existingFilter.getCount());
                                filterFromDpt.setTitle(polyglotService.getTranslatedData(filterFromDpt.getTitle()));
                            });

                    // If count is greater than 0 set, transform the filter to CG format and save it to the CG response
                    if (filterFromDpt.getCount() != null && filterFromDpt.getCount() > 0) {
                        if (CollectionUtils.isEmpty(filterCategory.getFilters())) {
                            filterCategory.setFilters(new ArrayList<>());
                        }
                        filterCategory.getFilters().add(commonResponseTransformer.buildFilterCG(filterFromDpt));
                    }
                }
            }
        }
    }

    /**
     * This method modifies the showImageUrl boolean to false for filter context experiment MERGE_PROPERTY_TYPE
     **/
    private void modifyShowImageUrlMergePropertyType(List<FilterCategory> filterList) {
        if (CollectionUtils.isNotEmpty(filterList)) {
            for (FilterCategory filterCategory : filterList) {
                if (filterCategory != null && MERGE_PROPERTY_TYPE.equalsIgnoreCase(filterCategory.getCategoryName())) {
                    filterCategory.setShowImageUrl(false);
                    break;
                }
            }
        }
    }

    /**
     * This method removes certain filter categories from filter response as per config
     **/
    private void removeFilterCategoriesFromResponse(List<String> filterCategoriesToRemove, FilterResponse filterResponse) {
        if (filterResponse != null && CollectionUtils.isNotEmpty(filterCategoriesToRemove) && CollectionUtils.isNotEmpty(filterResponse.getFilterList())) {
            List<FilterCategory> categories = new ArrayList<>();
            for (FilterCategory filterCategory : filterResponse.getFilterList()) {
                if (filterCategoriesToRemove.contains(filterCategory.getCategoryName())) {
                    categories.add(filterCategory);
                }
            }
            filterResponse.getFilterList().removeAll(categories);
        }
    }

    /**
     * This method modifies the view-type of filter category as per config, config is map with category name and corresponding target view type
     **/
    private void modifyFilterCategoryViewType(Map<String, String> viewTypeModificationMap, FilterResponse filterResponse) {

        if (filterResponse != null && MapUtils.isNotEmpty(viewTypeModificationMap) && CollectionUtils.isNotEmpty(filterResponse.getFilterList())) {
            for (FilterCategory filterCategory : filterResponse.getFilterList()) {
                if (StringUtils.isNotEmpty(filterCategory.getCategoryName()) && viewTypeModificationMap.containsKey(filterCategory.getCategoryName())) {
                    filterCategory.setViewType(viewTypeModificationMap.get(filterCategory.getCategoryName()));
                }
            }
        }

    }


    /**
     * this method returns a string having count of properties after filter application
     * eg: VIEW 45 PROPERTIES
     **/
    private String buildFilterCtaText(int totalHotelsCountAfterFilterApplication, FilterCountRequest filterRequest, LinkedHashMap<String, String> expDataMap) {

        String ctaText = polyglotService.getTranslatedData(ConstantsTranslation.FILTER_CTA_TEXT);

        if (StringUtils.isNotEmpty(ctaText)) {
            if (filterRequest != null && canShowFilterCount(filterRequest.getSearchCriteria().getLocationId(), expDataMap) && (CollectionUtils.isNotEmpty(filterRequest.getFilterCriteria()) || (filterRequest.getMatchMakerDetails() != null && (CollectionUtils.isNotEmpty(filterRequest.getMatchMakerDetails().getSelectedTags()) || CollectionUtils.isNotEmpty(filterRequest.getMatchMakerDetails().getLatLng())))))
            {
                ctaText = (totalHotelsCountAfterFilterApplication >= 0) ? ctaText.replace("{filtered_hotel_count}", String.valueOf(totalHotelsCountAfterFilterApplication)) : ctaText.replace("{filtered_hotel_count}", EMPTY_STRING);

            } else {
                ctaText = ctaText.replace("{filtered_hotel_count}", EMPTY_STRING);
            }
        }

        return ctaText;

    }

    private boolean canShowFilterCount(String locationId, LinkedHashMap<String, String> expDataMap) {
        boolean showFiltercountExpEnabled = utility.isExperimentOn(expDataMap, ExperimentKeys.SHOWFILTERCOUNT.getKey());
        if(locationId == null){
            return true;
        }
        if(showFiltercountExpEnabled && CollectionUtils.isNotEmpty(roiCityList) && roiCityList.contains(locationId)){
            return false;
        }
        return true;
    }

    /** this method calls polyglot to translate te keys present in the SORT filter pill **/
    private void translateSortPillData(SortList sortList, String cityHeroPoiName) {
        if (sortList != null) {
            if (StringUtils.isNotBlank(sortList.getTitle())) {
                sortList.setTitle(polyglotService.getTranslatedData(sortList.getTitle()));
            }
            if (CollectionUtils.isNotEmpty(sortList.getSortCriteria())) {
                for (SortCriteria sortCriteria : sortList.getSortCriteria()) {
                    try {
                        if(!sortCriteria.isAccessPoint()){
                            sortCriteria.setTitle(Constants.HERO_POI_DRIVING_DISTANCE_TITLE.equalsIgnoreCase(sortCriteria.getTitle()) ? polyglotService.getTranslatedData(sortCriteria.getTitle()).replace("{city_hero_poi_name}", cityHeroPoiName) : polyglotService.getTranslatedData(sortCriteria.getTitle()));
                            sortCriteria.setPillText(Constants.HERO_POI_DRIVING_DISTANCE_TEXT.equalsIgnoreCase(sortCriteria.getPillText()) ? polyglotService.getTranslatedData(sortCriteria.getPillText()).replace("{city_hero_poi_name}", cityHeroPoiName) : polyglotService.getTranslatedData(sortCriteria.getPillText()));
                        }
                    }
                    catch(Exception ex){
                        logger.error("Exception occurred for the following cityHeroPoiName :{} , sortCriteria :{}", cityHeroPoiName, sortCriteria, ex);
                    }
                }
            }
        }
    }

    /** This method uses filterList in filterResponse to build filter pills **/
    private List<FilterPill> buildFilterPills(FilterResponse filterResponse, FilterSearchMetaDataResponse filterResponseHES,
                                              FilterPillConfig filterPillConfig, LinkedHashMap<String, String> expDataMap, String funnelSource,
                                              FilterCountRequest filterCountRequest, boolean isCorp) {

        // Check if locality filter can be shown based on location type : HTL-62290 for multi city searches, pill needs to be removed
        // For MultiCity Area and Poi where location type itself is 'area' or 'poi', Location filters are not supported
        boolean canShowLocalityFilter = true;
        if (filterCountRequest != null && filterCountRequest.getSearchCriteria() != null && 
            StringUtils.isNotBlank(filterCountRequest.getSearchCriteria().getLocationType())) {
            String locationType = filterCountRequest.getSearchCriteria().getLocationType();
            canShowLocalityFilter = !(LocationType.poi.name().equalsIgnoreCase(locationType) || LocationType.area.name().equalsIgnoreCase(locationType));
        }

        ContextualFilterResponse dptContextualFilterResponse = filterResponseHES.getDptContextualFilterResponse();
        List<FilterPill> stickyFilterPillList = new ArrayList<>();
        List<FilterPill> dynamicFilterPillList = new ArrayList<>();

        /* List containing all the filter categories present in filterList. Eg: PRICE, POPULAR, STAR_CATEGORY */
        List<String> filterCategories =
                filterCountRequest.getExpDataMap() != null &&
                        utility.isExperimentOn(filterCountRequest.getExpDataMap(), Constants.FILTER_SCREEN_V4) ?
                        getCategoryNamesFromFilterResponseV2(filterResponse) : getCategoryNamesFromFilterResponse(filterResponse);

        if (filterPillConfig != null) {

            // Populate sticky filter pills
            if (MapUtils.isNotEmpty(filterPillConfig.getStickyFilterPills())) {
                populateFilterPillsFromConfig(stickyFilterPillList,filterCategories,filterPillConfig.getStickyFilterPills(), filterResponseHES);
                stickyFilterPillList = sortFilterPillsBasisSequence(stickyFilterPillList);
            }
            // Populate homestayV2 filter pills after sticky filter in case experiment is enabled
            if(TRUE.equalsIgnoreCase(expDataMap.get(HOMESTAY_V2_FILTERS_PILL_EXP)) && MapUtils.isNotEmpty(filterPillConfig.getHomestayV2FilterPills())){
                populateFilterPillsFromConfig(stickyFilterPillList,filterCategories,filterPillConfig.getHomestayV2FilterPills(), filterResponseHES);
            }

            // Have to add that dynamic preAppliedFilterPill in case we have applied it to the user
            if (CollectionUtils.isNotEmpty(filterResponse.getPreAppliedFilters()))
                stickyFilterPillList.addAll(buildFilterPillListFromPreAppliedFilters(filterResponse.getPreAppliedFilters(), filterResponseHES));

            // Populate dynamic filter pills from DPT basis experiment htlPillsEnabled: 0/1
            if (MapUtils.isNotEmpty(expDataMap) && EXP_TRUE_1.equalsIgnoreCase(expDataMap.get(HTL_PILLS_ENABLED_EXP))) {
                if (MapUtils.isNotEmpty(filterPillConfig.getDynamicFilterPillsDPT()) &&
                        dptContextualFilterResponse != null && CollectionUtils.isNotEmpty(dptContextualFilterResponse.getPrimaryFilterCategories())) {

                    logger.warn("filterPillConfig.getDynamicFilterPills() -> {}, dptContextualFilterResponse.getPrimaryFilterCategories() -> {}",
                            filterPillConfig.getDynamicFilterPillsDPT(), dptContextualFilterResponse.getPrimaryFilterCategories());

                    LinkedHashMap<String, FilterPill> orderedFilterPillConfigDPT = getFilterPillOrderedMap(dptContextualFilterResponse.getPrimaryFilterCategories(), filterPillConfig.getDynamicFilterPillsDPT());

                    if (orderedFilterPillConfigDPT.keySet().size() >= minDynamicFilterPills) {
                        populateFilterPillsFromConfig(dynamicFilterPillList, filterCategories, orderedFilterPillConfigDPT, filterResponseHES);
                    }

                    logger.warn("filterPillConfig.getDynamicFilterPills() -> {}", orderedFilterPillConfigDPT.keySet());

                }
            }

            // If dynamic filter pills are not populated from DPT or size of pills list is less than minDynamicFilterPills threshold
            if (dynamicFilterPillList.size() < minDynamicFilterPills) {
                dynamicFilterPillList.clear();
                populateFilterPillsFromConfig(dynamicFilterPillList, filterCategories, filterPillConfig.getDynamicFilterPills(), filterResponseHES);
                dynamicFilterPillList = sortFilterPillsBasisSequence(dynamicFilterPillList);

                logger.warn("Populating default dynamic filter pills -> {}", filterPillConfig.getDynamicFilterPills());

            } else {
                // restricting the length of dynamic filter pills list to "maxDynamicFilterPills" -> 4 currently
                dynamicFilterPillList = dynamicFilterPillList.subList(0, Integer.min(dynamicFilterPillList.size(), maxDynamicFilterPills));

                // Add Locality pill for location recommendation
                if (canShowLocalityFilter && (MapUtils.isNotEmpty(expDataMap) && (TRUE.equalsIgnoreCase(expDataMap.get(Constants.ENABLE_LOCATION_RECOMMENDATION_EXP)) || (Utility.isPWARequest() && Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.FILTER_PILL_EXP))))
                        && MapUtils.isNotEmpty(filterPillConfig.getDynamicFilterPills()))) {
                    FilterPill localityPill = Optional.ofNullable(filterPillConfig.getDynamicFilterPills().get(LOCALITY_GROUP)).orElse(null);

                    FilterPill translatedFilterPill = buildTranslatedFilterPill(filterCategories, localityPill, filterResponseHES);
                    if(translatedFilterPill!=null && localityFilterPillPosition < dynamicFilterPillList.size())
                        dynamicFilterPillList.add(localityFilterPillPosition, translatedFilterPill);
                    }

                SubPageContext subPageContext = SubPageContext.resolve(filterCountRequest.getRequestDetails().getSubPageContext());
                //Add DPT Collection Pill at last if collection filters are present
                if (TRUE.equalsIgnoreCase(expDataMap.get(EXP_CITY_COLLECTION_ENABLE)) && MapUtils.isNotEmpty(filterResponseHES.getFilterDataMap()) &&
                        CollectionUtils.isNotEmpty(filterResponseHES.getFilterDataMap().get(FilterGroup.DPT_COLLECTIONS))
                && !SubPageContext.LISTING_COLLECTION.getSubPageContext().equalsIgnoreCase(subPageContext.getSubPageContext())) {
                    logger.debug("Adding DPT Collections Pill");
                    buildDptInlineCollectionPill(dynamicFilterPillList, funnelSource, filterResponse);
                }
            }
        }

        // Remove locality pills from both lists if canShowLocalityFilter is false
        if (!canShowLocalityFilter) {
            stickyFilterPillList.removeIf(pill -> LOCALITY_GROUP.equalsIgnoreCase(pill.getId()));
            dynamicFilterPillList.removeIf(pill -> LOCALITY_GROUP.equalsIgnoreCase(pill.getId()));
        }



        Stream<FilterPill> combinedStream = Stream.concat(stickyFilterPillList.stream(), dynamicFilterPillList.stream());
        return isCorp ? combinedStream.sorted(Comparator.comparingInt(pill -> {int index = pillConfigOrderMyBiz.indexOf(pill.getId());return index == -1 ? Integer.MAX_VALUE : index;})).collect(Collectors.toList()) : combinedStream.collect(Collectors.toList());
    }

    private void buildDptInlineCollectionPill(List<FilterPill> dynamicFilterPillList, String funnelSource, FilterResponse filterResponse) {
        boolean collectionFiltersPresent = false;

        if (CollectionUtils.isNotEmpty(filterResponse.getFilterList())) {
            for (FilterCategory filterCategory : filterResponse.getFilterList()) {
                if (Objects.equals(filterCategory.getCategoryName(), DPT_COLLECTIONS)) {
                    collectionFiltersPresent = true;
                    break;
                }
            }
        } else if (CollectionUtils.isNotEmpty(filterResponse.getFilterListV2())) {
            for (FilterPage filterCategory : filterResponse.getFilterListV2()) {
                if (Objects.equals(filterCategory.getPageId(), PAGE_ID_COLLECTIONS) && CollectionUtils.isNotEmpty(filterCategory.getCategories())) {
                    collectionFiltersPresent = true;
                    break;
                }
            }
        }

        if (collectionFiltersPresent && CollectionUtils.isNotEmpty(dynamicFilterPillList)) {
            FilterPill filterPill = new FilterPill();
            filterPill.setCategories(Arrays.asList(FilterGroup.DPT_COLLECTIONS.name()));
            filterPill.setId(FilterGroup.DPT_COLLECTIONS.name());
            if(FUNNEL_SOURCE_SHORTSTAYS.equalsIgnoreCase(funnelSource)) {
                filterPill.setType("pill");
            }
            filterPill.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.COLLECTIONS_TITLE));
            dynamicFilterPillList.add(0, filterPill);
        }
    }

    /**
     *
     * @param filterPillList
     *
     * This method sorts the filter pills as per the sequence mentioned in the filter pill config
     */
    private static List<FilterPill> sortFilterPillsBasisSequence(List<FilterPill> filterPillList) {
        if (CollectionUtils.isNotEmpty(filterPillList)) {
            filterPillList = filterPillList.stream().sorted(Comparator.comparingInt(FilterPill::getSequence)).collect(Collectors.toList());
        }
        return filterPillList;
    }


    /**
     * @param primaryFilterCategories
     * @param filterPillConfig
     * This method creates the ordered map config for filter pills as per the primary filter categories order shared by DPT
     */
    private LinkedHashMap<String, FilterPill> getFilterPillOrderedMap(LinkedHashSet<String> primaryFilterCategories, Map<String, FilterPill> filterPillConfig) {

        LinkedHashMap<String, FilterPill> filterPillMapOrdered = null;

        if (CollectionUtils.isNotEmpty(primaryFilterCategories) && filterPillConfig != null) {
            filterPillMapOrdered = new LinkedHashMap<>();
            // "PRICE_BUCKET" filterCategory removed if both "PRICE" and "PRICE_BUCKET" filterCategory is present in primaryFilterCategories list
            if (primaryFilterCategories.contains(Constants.FILTER_HOTEL_PRICE) && primaryFilterCategories.contains(FILTER_HOTEL_PRICE_BUCKET)) {
                primaryFilterCategories.remove(Constants.FILTER_HOTEL_PRICE_BUCKET);
            }
            // "USER_RATING" removed if both "USER_RATING" and "USER_RATING_MMT_BRAND" is present in primaryFilterCategories
            if (primaryFilterCategories.contains(FilterGroup.USER_RATING.name()) && primaryFilterCategories.contains(FilterGroup.USER_RATING_MMT_BRAND.name())) {
                primaryFilterCategories.remove(FilterGroup.USER_RATING.name());
            }

            for (String filterCategory : primaryFilterCategories) {
                if (filterPillConfig.containsKey(filterCategory)) {
                    filterPillMapOrdered.put(filterCategory, filterPillConfig.get(filterCategory));
                }
            }
        }
        return filterPillMapOrdered;
    }

    /**
     *
     * @param filterPillList
     * @param filterCategories
     * @param filterPillMap
     *
     * This method translates filter pill config and adds filter categories basis availability in filter list response
     *
     */

    public void populateFilterPillsFromConfig(List<FilterPill> filterPillList, List<String> filterCategories, Map<String, FilterPill> filterPillMap, FilterSearchMetaDataResponse filterResponseHES) {
        if (MapUtils.isNotEmpty(filterPillMap) && filterPillList != null) {
            for (Map.Entry<String, FilterPill> entry : filterPillMap.entrySet()) {
                FilterPill translatedFilterPill = buildTranslatedFilterPill(filterCategories, entry.getValue(), filterResponseHES);
                if (translatedFilterPill != null) {
                    filterPillList.add(translatedFilterPill);
                }
            }
        }

    }

    public List<FilterPill> buildFilterPillListFromPreAppliedFilters(List<com.mmt.hotels.clientgateway.request.Filter> preAppliedFilters, FilterSearchMetaDataResponse filterResponseHES) {
        List<FilterPill> filterPillList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(preAppliedFilters)) {
            for (com.mmt.hotels.clientgateway.request.Filter filterCGReq : preAppliedFilters) {
                if(filterCGReq==null){
                    continue;
                }
                Filter filterCGResp = new Filter();
                filterCGResp.setTitle(filterCGReq.getTitle());
                filterCGResp.setFilterGroup(filterCGReq.getFilterGroup().toString());
                filterCGResp.setFilterValue(filterCGReq.getFilterValue());
                com.mmt.hotels.filter.Filter filterFromHES = getFilterForFilterGroupAndValueFromHESResponse(filterResponseHES, filterCGResp);
                if (filterFromHES != null) {
                    // adding count and quantityFilter detail in filter
                    filterCGResp.setCount(filterFromHES.getCount());
                    filterCGResp.setQuantityFilter(filterFromHES.isQuantityFilter());
                }
                filterPillList.add(buildFilterPillFromFilter(filterCGResp));
            }
        }
        return filterPillList;
    }

    private FilterPill buildFilterPillFromFilter(Filter filter) {
        FilterPill filterPill = new FilterPill();
        filterPill.setTitle(filter.getTitle());
        filterPill.setPillFilter(filter);
        filterPill.setId(filter.getFilterValue());
        filterPill.setType(Constants.FILTER);
        return filterPill;
    }

    /**
     * method to get all category names from the filterList in filterResponse
     * Eg. PRICE, PRICE_BUCKET, POPULAR, STAR_CATEGORY
     **/
    private List<String> getCategoryNamesFromFilterResponse(FilterResponse filterResponse) {
        List<String> filterCategories = new ArrayList<>();

        if (filterResponse != null && filterResponse.getFilterList() != null) {
            for (FilterCategory filterCategory : filterResponse.getFilterList()) {
                if (CollectionUtils.isNotEmpty(filterCategory.getFilters())) {
                    filterCategories.add(filterCategory.getCategoryName());
                }
            }
        }
        return filterCategories;

    }

    private List<String> getCategoryNamesFromFilterResponseV2(FilterResponse filterResponse) {
        List<String> filterCategories = new ArrayList<>();

        if (filterResponse != null && filterResponse.getFilterListV2() != null) {
            for (FilterPage page : filterResponse.getFilterListV2()) {
                for (FilterCategory filterCategory : page.getCategories()) {
                    if (CollectionUtils.isNotEmpty(filterCategory.getFilters())) {
                        filterCategories.add(filterCategory.getCategoryName());
                    }
                }
            }
        }
        return filterCategories;

    }

    /**
     * This method builds a pill from a pill configuration. A new {@link FilterPill} will be created and the values are populated from the filterPill configuration and HES response. There can be two types of pills. <br><br>
     * 1. Either it could be a pill that represents a section of filters of different categories OR <br>
     * 2. A direct filter that can be applied by simply clicking on it. A direct filter will have its filterGroup, filterValue and other parameters. <br><br>
     * These two types are represented by a key named "type" with value "filter" or "pill"
     * @param filterCategories A list of categories that is applicable taken from HES response after passing several conditions.
     * @param filterPillFromConfig Configuration data of a pill fetched using pill name.
     * @param filterResponseHES Response from HES
     * @return type of {@link FilterPill} that
     */
    private FilterPill buildTranslatedFilterPill(List<String> filterCategories, FilterPill filterPillFromConfig, FilterSearchMetaDataResponse filterResponseHES) {
        if (filterPillFromConfig == null || !isCategoriesInPillConfigPresentInHESCategoryList(filterCategories, filterPillFromConfig,filterResponseHES)) return null;
        FilterPill translatedFilterPill = new FilterPill();
        translatedFilterPill.setId(filterPillFromConfig.getId());
        translatedFilterPill.setTitle(polyglotService.getTranslatedData(filterPillFromConfig.getTitle()));
        translatedFilterPill.setType(filterPillFromConfig.getType());
        translatedFilterPill.setSequence(filterPillFromConfig.getSequence());
        translatedFilterPill.setCategories(buildCategories(filterPillFromConfig));
        translatedFilterPill.setPillFilter(buildPillFilter(filterResponseHES, filterPillFromConfig));
        translatedFilterPill.setPillType(filterPillFromConfig.getPillType());
        translatedFilterPill.setIcon(filterPillFromConfig.getIcon());
        return translatedFilterPill;
    }

    /**
     * This function checks if the categories of a pill in its config is present in the HES category list applicable for the request.
     * This HES category list is created for a particular request by evaluating the pms configuration and other conditions.
     * @param filterCategories List of categories taken from HES response applicable for this request.
     * @param filterPillFromConfig Pill Configuration for a particular pill name
     * @return type of {@link Boolean} shows whether this pill should be added in response or not.
     */
    private boolean isCategoriesInPillConfigPresentInHESCategoryList(List<String> filterCategories, FilterPill filterPillFromConfig,FilterSearchMetaDataResponse filterResponseHES){
        //If its matchMaker filter no ned t check further
        if(isMatchMakerFilter(filterResponseHES.isSavedForCompanyDataAvailable(),filterPillFromConfig.getId()))return true;
        // If there is no categories in configuration, no need to check in list
        if (CollectionUtils.isEmpty(filterPillFromConfig.getCategories())) return true;
        // There are categories in configuration but the HESCategoryList is empty
        if (CollectionUtils.isEmpty(filterCategories)) return false;
        // Retain only the categories present in HES category list
        filterPillFromConfig.getCategories().retainAll(filterCategories);
        // If the category list in config becomes empty after retaining, return false, otherwise return true.
        return !CollectionUtils.isEmpty(filterPillFromConfig.getCategories());
    }

    private boolean isMatchMakerFilter(boolean isSavedForCompany, String filterPillId) {

        if(isSavedForCompany && SAVED_FOR_COMPANY_PILL_ID.equalsIgnoreCase(filterPillId))
            return true;

        return false;
    }

    /**
     * For a direct filter, categories is not needed. Thus, this method will return null in case of direct filter and will
     * return the categories otherwise
     * @param filterPillFromConfig Pill configuration
     * @return type of {@link List} returns category list
     */
    private List<String> buildCategories(FilterPill filterPillFromConfig){
        if (TYPE_FILTER.equalsIgnoreCase(filterPillFromConfig.getType())) return null;
        return filterPillFromConfig.getCategories();
    }

    /**
     * This method will create the direct filter that can be applied when simply clicked. This is not a section with filter
     * categories. If it's not a direct filter, null will be the return value.
     * @param filterResponseHES Response from HES
     * @param filterPillFromConfig Pill Configuration
     * @return A direct filter
     */
    private Filter buildPillFilter(FilterSearchMetaDataResponse filterResponseHES, FilterPill filterPillFromConfig){
        if (!Constants.TYPE_FILTER.equalsIgnoreCase(filterPillFromConfig.getType())) return  null;
        Filter pillFilter = new Filter();
        pillFilter.setFilterGroup(filterPillFromConfig.getPillFilter().getFilterGroup());
        pillFilter.setFilterValue(filterPillFromConfig.getPillFilter().getFilterValue());
        pillFilter.setTitle(polyglotService.getTranslatedData(filterPillFromConfig.getPillFilter().getTitle()));
        pillFilter.setQuantityFilter(filterPillFromConfig.getPillFilter().isQuantityFilter());
        com.mmt.hotels.filter.Filter filterFromHES = getFilterForFilterGroupAndValueFromHESResponse(filterResponseHES, filterPillFromConfig.getPillFilter());
        if (null == filterFromHES ) return pillFilter;
        pillFilter.setCount(filterFromHES.getCount());
        pillFilter.setQuantityFilter(filterFromHES.isQuantityFilter());
        return pillFilter;
    }

    /**
     * This method check if a particular filter is present in HES response using the FilterGroup and FilterValue params.
     * If It's present, that filter is sent back
     * @param filterResponseHES Response from HES
     * @param pillFilter pillFilter key from pill configuration containing FilterGroup and FilterValue params.
     * @return filter that matches the condition.
     */
    private com.mmt.hotels.filter.Filter getFilterForFilterGroupAndValueFromHESResponse(FilterSearchMetaDataResponse filterResponseHES, Filter pillFilter){
        FilterGroup filterGroupInConfig = FilterGroup.getFilterGroupFromFilterName(pillFilter.getFilterGroup());
        List<com.mmt.hotels.filter.Filter> filtersOfFilterGroupFromHES = filterResponseHES.getFilterDataMap().get(filterGroupInConfig);
        // get the filter for the corresponding filter value
        filtersOfFilterGroupFromHES = filtersOfFilterGroupFromHES.stream().filter(f -> Objects.requireNonNull(f.getFilterValue()).equalsIgnoreCase(pillFilter.getFilterValue())).collect(Collectors.toList());
        return CollectionUtils.isEmpty(filtersOfFilterGroupFromHES) ? null : filtersOfFilterGroupFromHES.get(0);
    }


    public FilterResponse convertBatchFilterResponse(FilterSearchMetaDataResponse filterResponseHES) {
        if (filterResponseHES == null) {
            return null;
        }
        FilterResponse filterResponse = new FilterResponse();
        filterResponse.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.BATCH_COLLECTIONS_TITLE));
        filterResponse.setTotalCount(filterResponseHES.getTotalHotelsCount());
        filterResponse.setBatchFiltersList(buildBatchFilterList(filterResponseHES.getBatchFilters()));
        filterResponse.setFilteredCount(filterResponseHES.getTotalHotelsCountAfterFilterApplication());
        filterResponse.setContextDetails(buildContextDetails(filterResponseHES.getContextDetails()));
        return filterResponse;
    }

    public FilterPricingOption buildPricingOption(FilterCountRequest filterRequest, FilterPricingOption pricingOption){

        if(pricingOption == null)
            return null;

        String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
        String checkIn = filterRequest.getSearchCriteria().getCheckIn();
        String checkOut = filterRequest.getSearchCriteria().getCheckOut();
        int los = dateUtil.getDaysDiff(LocalDate.parse(checkIn),LocalDate.parse(checkOut));

        if(Utility.isRegionGccOrKsa(region) && los>1) {

            FilterPricingOption pricingOptionNew = new FilterPricingOption();
            pricingOptionNew.setHeader(polyglotService.getTranslatedData(pricingOption.getHeader()));
            List<PricingOptionFilter> filterListNew = new ArrayList<PricingOptionFilter>();
            List<PricingOptionFilter> filterList = pricingOption.getFilters();

            for(PricingOptionFilter filter : filterList) {
                PricingOptionFilter newFilter = new PricingOptionFilter();
                newFilter.setTitle(polyglotService.getTranslatedData(filter.getTitle()));
                newFilter.setExpValue(filter.getExpValue());

                filterListNew.add(newFilter);
            }
            pricingOptionNew.setFilters(filterListNew);
            return pricingOptionNew;
        }

        return null;
    }
    private ContextDetails buildContextDetails(com.mmt.hotels.filter.ContextDetails contextDetails) {
        if(contextDetails == null){
            return null;
        }
        ContextDetails contextDetailsCG = new ContextDetails();
        contextDetailsCG.setContext(contextDetails.getContext());
        contextDetailsCG.setAltAccoIntent(contextDetails.isAltAccoIntent());
        return contextDetailsCG;
    }

    private List<Filter> buildBatchFilterList(List<com.mmt.hotels.filter.Filter> batchFilters) {
        if(CollectionUtils.isEmpty(batchFilters)){
            return null;
        }
        List<Filter> batchFiltersCG = new ArrayList<>();
        batchFilters.forEach(batchFilter -> batchFiltersCG.add(buildBatchFilter(batchFilter)));
        return batchFiltersCG;
    }

    private Filter buildBatchFilter(com.mmt.hotels.filter.Filter batchFilter) {
        Filter batchCG = new Filter();
        batchCG.setFilterValue(batchFilter.getFilterValue());
        batchCG.setCount(batchFilter.getCount());
        batchCG.setIconUrl(batchFilter.getIconUrl());
        batchCG.setTitle(batchFilter.getTitle());
        batchCG.setSubTitle(batchFilter.getSubTitle());
        batchCG.setStaticBatch(batchFilter.getStaticBatch());
        return batchCG;
    }

    private List<Filter> buildMatchmakerSuggestedFilterList(List<com.mmt.hotels.filter.Filter> matchmakerFilterList) {
        if(CollectionUtils.isEmpty(matchmakerFilterList)){
            return null;
        }
        List<Filter> matchmakerFilters = new ArrayList<>();
        matchmakerFilterList.forEach(matchmakerFilterHes -> matchmakerFilters.add(commonResponseTransformer.buildFilterCG(matchmakerFilterHes)));
        return matchmakerFilters;
    }

    public List<FilterPage> addFilterPages(FilterSearchMetaDataResponse filterResponseHES, FilterConfigurationV2 filterConfig, FilterCountRequest filterRequest, LinkedHashMap<String, String> expDataMap, CommonModifierResponse commonModifierResponse) {
        List<FilterPage> filterPages = new ArrayList<>();

        int maxFilterSize = MapUtils.isNotEmpty(filterConfig.getRankOrder()) ? filterConfig.getRankOrder().values().stream().max(Integer::compareTo).get() : -1;
        FilterPage[] filterPageArr = new FilterPage[maxFilterSize];

        String idContext = (filterRequest.getRequestDetails() != null && StringUtils.isNotBlank(filterRequest.getRequestDetails().getIdContext())) ? filterRequest.getRequestDetails().getIdContext().toUpperCase() : "B2C";
        String funnelSource = (filterRequest.getRequestDetails() != null && StringUtils.isNotBlank(filterRequest.getRequestDetails().getFunnelSource())) ? filterRequest.getRequestDetails().getFunnelSource().toUpperCase() : "HOTELS";
        String locationType= (filterRequest.getSearchCriteria()!=null && StringUtils.isNotBlank(filterRequest.getSearchCriteria().getLocationType())) ? filterRequest.getSearchCriteria().getLocationType() : "";

        for (Map.Entry<String, com.mmt.hotels.clientgateway.businessobjects.FilterPage> pageEntry: filterConfig.getFilterPages().entrySet()) {
            com.mmt.hotels.clientgateway.businessobjects.FilterPage pageConfig = pageEntry.getValue();

            // [HTL-62290] for multi city searches [area, poi as locationType], Locality filters needs to be removed
            if (LOCATION.equalsIgnoreCase(pageConfig.getPage_id()) && (LocationType.poi.name().equalsIgnoreCase(locationType) || LocationType.area.name().equalsIgnoreCase(locationType))) {
                continue;
            }

            FilterPage filterPage = new FilterPage();
            filterPage.setTitle(pageConfig.getTitle());
            filterPage.setPageId(pageConfig.getPage_id());
            filterPage.setSearchable(pageConfig.isSearchable());
            filterPage.setNewTagIcon(pageConfig.getNewTagIcon());

            List<FilterCategory> categories = new ArrayList<>();
            for (Map.Entry<String, FilterConfigCategory> categoryEntry : pageConfig.getFilters().entrySet()) {
                FilterCategory category = new FilterCategory();
                FilterConfigCategory fConfigCat = categoryEntry.getValue();
                category.setCategoryName(categoryEntry.getKey());

                boolean isMyPartnerRequest = commonModifierResponse != null && commonModifierResponse.getExtendedUser() != null && StringUtils.isNotEmpty(commonModifierResponse.getExtendedUser().getAffiliateId()) && commonModifierResponse.getExtendedUser().getAffiliateId().equalsIgnoreCase(MYPARTNER);
                boolean isMobile = Constants.ANDROID.equalsIgnoreCase(filterRequest.getClient()) || Constants.DEVICE_IOS.equalsIgnoreCase(filterRequest.getClient());
                boolean isAltAccoFilterEnabled = expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER) != null && Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER));
                boolean isCorp = CORP_ID_CONTEXT.equalsIgnoreCase(idContext);
                String title = null;
                if (!Constants.FILTER_HOTEL_PRICE_BUCKET.equalsIgnoreCase(categoryEntry.getKey()) || !isMobile || (!isCorp && !isAltAccoFilterEnabled)) {
                    title = updateCategoryTitle(categoryEntry.getKey(), fConfigCat, filterRequest, filterResponseHES, expDataMap, isMyPartnerRequest);
                }

                if (title != null && !title.equalsIgnoreCase(NULL_STRING))
                    category.setTitle(title);

                category.setShowImageUrl(Boolean.TRUE.equals(fConfigCat.getShowImageUrl()));
                category.setVisible(Boolean.TRUE.equals(fConfigCat.getVisible()));
                category.setCustomRangeTitle(fConfigCat.getCustomRangeTitle());
                category.setDescription(fConfigCat.getDescription());
                category.setSingleSelection(Boolean.TRUE.equals(fConfigCat.getSingleSelection()));
                category.setCollapsed(Boolean.TRUE.equals(fConfigCat.getCollapsed()));
                category.setIconUrl(fConfigCat.getIconUrl());
                category.setMinItemsToShow(fConfigCat.getMinItemsToShow());
                category.setViewType(fConfigCat.getViewType());
                category.setViewTypeV2(fConfigCat.getViewTypeV2());
                category.setHideCount(fConfigCat.isHideCount() || !canShowFilterCount(filterRequest.getSearchCriteria().getLocationId(), expDataMap));

                boolean isDynamicFilterOrdering = filterConfig != null && MapUtils.isNotEmpty(filterConfig.getCategoryAttributes());

                List<Filter> filtersList = buildFilterList(categoryEntry.getValue(), filterResponseHES, filterRequest, expDataMap, categoryEntry.getKey(), idContext, commonModifierResponse, category, pageConfig.getFilters(), isDynamicFilterOrdering);
                if (filtersList != null && PAGE_SUGGESTED_FOR_YOU.equalsIgnoreCase(pageConfig.getPage_id())) {
                    for (Filter filter : filtersList) {
                        filter.setImageUrl(null);
                    }
                }

                if (AMENITIES.equalsIgnoreCase(pageConfig.getPage_id())) {
                    List<FilterCollection> filterCollections = buildFilterCollection(filtersList, funnelSource);
                    List<Filter> otherFilters = new ArrayList<>();
                    for (int i = 0; i < filterCollections.size(); i++) {
                        if (category.getCategoryName().equalsIgnoreCase(SIGNATURE_AMENITIES)) {
                            if (i == 0) {
                                category.setFilters(filterCollections.get(0).getFilters());
                            }
                        } else if (category.getCategoryName().equalsIgnoreCase(AMENITIES)) {
                            if (i > 0) {
                                otherFilters.addAll(filterCollections.get(i).getFilters());
                                category.setFilters(otherFilters);
                            }
                        }
                    }
                } else {
                    category.setFilters(filtersList);
                }

                if (FILTER_POPULAR.equalsIgnoreCase(category.getCategoryName()) || FILTER_DEALS.equalsIgnoreCase(category.getCategoryName())) {
                    boolean isGcc = filterRequest.getRequestDetails() != null && Constants.AE.equalsIgnoreCase(filterRequest.getRequestDetails().getSiteDomain());

                    Tuple<Boolean, Boolean> showSupplierFilters = showSupplierFilters(filterRequest.getSearchCriteria().getCheckIn(), filterResponseHES, isGcc);
                    if (showSupplierFilters.getX() != null && showSupplierFilters.getX()) {
                        Filter earlyBirdFilter = new Filter();
                        earlyBirdFilter.setFilterGroup(com.mmt.hotels.clientgateway.response.filter.FilterGroup.DEALS.name());
                        earlyBirdFilter.setFilterValue(EARLY_BIRD);
                        earlyBirdFilter.setTitle(polyglotService.getTranslatedData(Constants.EARLY_BIRD_FILTER_TITLE));
                        earlyBirdFilter.setCount(null);
                        InfoTag tag = new InfoTag();
                        earlyBirdFilter.setInfoTag(tag);
                        if (isGcc) {
                            int count = getSupplierFiltersCount(filterResponseHES, EARLY_BIRD);
                            earlyBirdFilter.setCount(count);
                        }
                        category.getFilters().add(0, earlyBirdFilter);
                    }
                    if (showSupplierFilters.getY() != null && showSupplierFilters.getY()) {
                        Filter lastMinuteFilter = new Filter();
                        lastMinuteFilter.setFilterGroup(com.mmt.hotels.clientgateway.response.filter.FilterGroup.DEALS.name());
                        lastMinuteFilter.setFilterValue(LAST_MINUTE);
                        lastMinuteFilter.setTitle(polyglotService.getTranslatedData(Constants.LAST_MINUTE_FILTER_TITLE));
                        lastMinuteFilter.setCount(null);
                        InfoTag tag = new InfoTag();
                        lastMinuteFilter.setInfoTag(tag);
                        if (isGcc) {
                            int count = getSupplierFiltersCount(filterResponseHES, Constants.LAST_MINUTE);
                            lastMinuteFilter.setCount(count);
                        }
                        category.getFilters().add(0, lastMinuteFilter);
                    }
                    if (filterResponseHES.getFilterDataMap().containsKey(FilterGroup.HOTELS_USP)) {
                        for (com.mmt.hotels.filter.Filter filter : filterResponseHES.getFilterDataMap().get(FilterGroup.HOTELS_USP)) {
                            if (!isGcc && filter.getFilterValue() != null && filter.getFilterValue().equalsIgnoreCase(LOVED_BY_INDIANS_TITLE)) {
                                Filter uspFilter = new Filter();
                                uspFilter.setFilterGroup(filter.getFilterGroup().name());
                                uspFilter.setFilterValue(filter.getFilterValue());
                                uspFilter.setTitle(LOVED_BY_INDIANS_TITLE);
                                uspFilter.setCount(filter.getCount());
                                category.getFilters().add(0, uspFilter);
                            }
                        }
                    }
                }

                if (category.getCategoryName().equalsIgnoreCase(LOCALITY_GROUP) || (category.getFilters() != null && !category.getFilters().isEmpty())) {
                    categories.add(category);
                }
            }
            filterPage.setCategories(categories);
            if(filterPage.getPageId().equalsIgnoreCase(LOCATION) || !filterPage.getCategories().isEmpty()) {
                int index = filterConfig.getRankOrder().getOrDefault(pageEntry.getKey(), -1);
                if (index > 0 && filterPageArr.length > 0) {
                    filterPageArr[index - 1] = filterPage;
                }
            }
        }
        if (filterPageArr != null) {
            filterPages = new ArrayList<>(Arrays.asList(filterPageArr));
        }
        filterPages = filterPages.stream().filter(Objects::nonNull).collect(Collectors.toList());
        return filterPages;
    }

    private List<FilterCategory> addFilterCategories(
            FilterSearchMetaDataResponse filterResponseHES,
            FilterCountRequest filterRequest,
            LinkedHashMap<String,String>expDataMap,
            CommonModifierResponse commonModifierResponse,
            LinkedHashMap<String, FilterConfigCategory> categories,
            int maxFilterSize,
            LinkedHashMap<String,Integer> rankOrder,
            boolean isDynamicFilterOrdering,
            boolean isFilterConfigV2
    ){

        List<FilterCategory> filterCategoryList = new ArrayList<>();
        List<FilterCategory> nonRankedFilterCategoryList = new ArrayList<>();
        FilterCategory[] fCategoryRankedArr = null;
        if(maxFilterSize > 0)
            fCategoryRankedArr = new FilterCategory[maxFilterSize];

        String funnelSource = (filterRequest.getRequestDetails() != null && StringUtils.isNotBlank(filterRequest.getRequestDetails().getFunnelSource())) ? filterRequest.getRequestDetails().getFunnelSource().toUpperCase() : "HOTELS";
        String idContext = (filterRequest.getRequestDetails() != null && StringUtils.isNotBlank(filterRequest.getRequestDetails().getIdContext())) ? filterRequest.getRequestDetails().getIdContext().toUpperCase() : "B2C";
        String siteDomain = (filterRequest.getRequestDetails() != null && StringUtils.isNotBlank(filterRequest.getRequestDetails().getSiteDomain())) ? filterRequest.getRequestDetails().getSiteDomain() : "IN";
        String locationType= (filterRequest.getSearchCriteria()!=null && StringUtils.isNotBlank(filterRequest.getSearchCriteria().getLocationType())) ? filterRequest.getSearchCriteria().getLocationType() : "";
        String trafficSource = filterRequest.getRequestDetails()!=null?(filterRequest.getRequestDetails().getTrafficSource()!=null?filterRequest.getRequestDetails().getTrafficSource().getSource():""):"";
        boolean isMyPartnerRequest = commonModifierResponse != null && commonModifierResponse.getExtendedUser() != null && StringUtils.isNotEmpty(commonModifierResponse.getExtendedUser().getAffiliateId()) && commonModifierResponse.getExtendedUser().getAffiliateId().equalsIgnoreCase(MYPARTNER);
        for(Map.Entry<String, FilterConfigCategory> entry: categories.entrySet()){
            FilterConfigCategory fConfigCat = entry.getValue();
            if ((LOCALITY_GROUP.equalsIgnoreCase(entry.getKey()) && (MapUtils.isEmpty(expDataMap) || FALSE.equalsIgnoreCase(expDataMap.get(Constants.COUNTRY_PAGE_FILTER))))
                    || FILTER_HOTEL_PRICE_BUCKET.equalsIgnoreCase(entry.getKey())
                    || PRICE_CATEGORY_NAME.equalsIgnoreCase(entry.getKey())
                    || FILTER_ROOMS_AND_BEDS.equalsIgnoreCase(entry.getKey())) {
                /* Location/Price/BedCount should be removed for country search in non-seo case. */
                if (COUNTRY.equalsIgnoreCase(locationType) && (filterRequest.getRequestDetails() == null
                        || filterRequest.getRequestDetails().getTrafficSource() == null
                        || !TRAFFIC_SOURCE_SEO.equalsIgnoreCase(filterRequest.getRequestDetails().getTrafficSource().getSource()))) {
                    continue;
                }
            }
            // [HTL-62290] for multi city searches [area, poi as locationType], Locality filters needs to be removed
            if (LOCALITY_GROUP.equalsIgnoreCase(entry.getKey()) && (LocationType.poi.name().equalsIgnoreCase(locationType) || LocationType.area.name().equalsIgnoreCase(locationType))) {
                continue;
            }
            if(TRANSIT_POIS.equalsIgnoreCase(entry.getKey()) && (StringUtils.isEmpty(trafficSource) || !fConfigCat.getCondition().get(TRAFFIC_SOURCE).equalsIgnoreCase(trafficSource))){
                continue;
            }
            if (PROPERTY_TYPE.equalsIgnoreCase(entry.getKey()) && MapUtils.isNotEmpty(expDataMap) && TRUE.equalsIgnoreCase(expDataMap.get(Constants.ENABLE_MERGED_PROPERTY_TYPE)))
                continue;
            if (MERGE_PROPERTY_TYPE.equalsIgnoreCase(entry.getKey()) && MapUtils.isNotEmpty(expDataMap) && !TRUE.equalsIgnoreCase(expDataMap.get(Constants.ENABLE_MERGED_PROPERTY_TYPE)))
                continue;
            if (entry.getKey().equalsIgnoreCase("LUX") && MapUtils.isNotEmpty(expDataMap) && ((expDataMap.containsKey("enableLuxeFilter") && !expDataMap.get("enableLuxeFilter").equalsIgnoreCase("true")) || (!expDataMap.containsKey("enableLuxeFilter")))){
                continue;
            }
            if ("FLEXIBLE_CHECKIN".equalsIgnoreCase(entry.getKey()) && !utility.isExperimentTrue(expDataMap, "flexible_checkin"))
                continue;

            if(COUNTRY.equalsIgnoreCase(locationType) && FilterGroup.DRIVE_WALK_PROXIMITY_KEY_POI.name().equalsIgnoreCase(entry.getKey())){
                continue;
            }

            // [HTL-44036] if unifiedUserRating pokus is true, we have to pick new unified userRating filters
            if (entry.getKey().equalsIgnoreCase(FilterGroup.USER_RATING.name()) && MapUtils.isNotEmpty(expDataMap) && Constants.TRUE.equalsIgnoreCase(expDataMap.get(Constants.UNIFIED_USER_RATING))) {
                continue;
            } else if (entry.getKey().equalsIgnoreCase(FilterGroup.USER_RATING_MMT_BRAND.name()) && MapUtils.isNotEmpty(expDataMap) && Constants.FALSE.equalsIgnoreCase(expDataMap.get(Constants.UNIFIED_USER_RATING))) {
                continue;
            }

            if(MapUtils.isNotEmpty(fConfigCat.getCondition())) {
                if (fConfigCat.getCondition().containsKey(Constants.FILTER_COND_FUNNEL) && StringUtils.isNotBlank(fConfigCat.getCondition().get(Constants.FILTER_COND_FUNNEL)) &&
                        !fConfigCat.getCondition().get(Constants.FILTER_COND_FUNNEL).contains(funnelSource))
                    continue;

                String country = Utility.isDomOrIntl(filterRequest.getSearchCriteria());
                if (fConfigCat.getCondition().containsKey(Constants.COUNTRY) && !country.equalsIgnoreCase(fConfigCat.getCondition().get(Constants.COUNTRY))) {
                    continue;
                }

                if (entry.getKey().equalsIgnoreCase("STAR_CATEGORY") &&
                        (funnelSource.equalsIgnoreCase("HOMESTAY") || funnelSource.equalsIgnoreCase(Constants.FUNNEL_SOURCE_HOMESTAY_NEW) )&&
                        filterRequest.getSearchCriteria().getCountryCode().equalsIgnoreCase(Constants.DOM_COUNTRY))
                    continue;

                if (fConfigCat.getCondition().containsKey(Constants.SITE_DOMAIN) && !siteDomain.equalsIgnoreCase(fConfigCat.getCondition().get(Constants.SITE_DOMAIN))) {
                    continue;
                }

                if(fConfigCat.getCondition().containsKey(Constants.IS_LUXE_ONLY_FILTER)){
                    if(StringUtils.isBlank(fConfigCat.getCondition().get(Constants.IS_LUXE_ONLY_FILTER)) || ("YES".equalsIgnoreCase(fConfigCat.getCondition().get(Constants.IS_LUXE_ONLY_FILTER)) &&  !Constants.LUXE_LOCATION_TYPE.equalsIgnoreCase(locationType)))
                        continue;
                } else {
                    if (Constants.LUXE_LOCATION_TYPE.equalsIgnoreCase(locationType))
                        continue;
                }

            } else {
                if (Constants.LUXE_LOCATION_TYPE.equalsIgnoreCase(locationType))
                    continue;
            }

            // For Desktop, hotel_price_bucket filter is removed for B2C and international hotels based on originListingMap featureFlag
            if (Constants.CLIENT_DESKTOP.equalsIgnoreCase(filterRequest.getClient())) {
                if (isRemovePriceBucketFilter(filterRequest, idContext)) {
                    if (Constants.FILTER_HOTEL_PRICE_BUCKET.equalsIgnoreCase(entry.getKey())) {
                        continue;
                    }
                } else {
                    if (Constants.FILTER_HOTEL_PRICE.equalsIgnoreCase(entry.getKey())) {
                        continue;
                    }
                }
            }

            if (Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource)) {
                if (expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER) != null && Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER))) {
                    if (FilterGroup.PROPERTY_CATEGORY.name().equalsIgnoreCase(entry.getKey())) {
                        continue;
                    }
                } else {
                    if (FilterGroup.PROPERTY_TYPE.name().equalsIgnoreCase(entry.getKey())){
                        continue;
                    }
                }
            }

            if((expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER) == null || !Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER)))
                    && Constants.FILTER_SPACE.equalsIgnoreCase(entry.getKey()) && MapUtils.isNotEmpty(fConfigCat.getGroups())){
                fConfigCat.getGroups().remove(FilterGroup.BOOKING.name());
            }
            if (FILTER_POPULAR.equalsIgnoreCase(entry.getKey()) &&
                    fConfigCat.getGroups().containsKey(FilterGroup.PAY_LATER.name())) {
                if (isRemoveBNPLFilter(filterRequest)) {
                    fConfigCat.getGroups().remove(FilterGroup.PAY_LATER.name());
                }
                else{
                    if (Utility.isGccOrKsa()) {
                        setTitleForGccBnplFilter(fConfigCat);
                    }
                }
            }

            if((expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER) == null || !Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER)))
                    && (FilterGroup.HOUSE_RULES.name().equalsIgnoreCase(entry.getKey()) || Constants.FILTER_ROOMS_AND_BEDS.equalsIgnoreCase(entry.getKey()))){
                continue;
            }

            if(entry.getKey().equalsIgnoreCase(Constants.LOCALITY_GROUP) && filterRequest.getFeatureFlags()!=null && !filterRequest.getFeatureFlags().isFilterRanking()){
                continue;
            }
            FilterCategory fRespCategory = new FilterCategory();
            fRespCategory.setCategoryName(entry.getKey());
            String title;
            boolean isMobile = Constants.ANDROID.equalsIgnoreCase(filterRequest.getClient()) || Constants.DEVICE_IOS.equalsIgnoreCase(filterRequest.getClient());
            boolean isAltAccoFilterEnabled = expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER) != null && Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER));
            boolean isCorp = CORP_ID_CONTEXT.equalsIgnoreCase(idContext);
            if ( Constants.FILTER_HOTEL_PRICE_BUCKET.equalsIgnoreCase(entry.getKey()) && isMobile && (isCorp || isAltAccoFilterEnabled)){
                title = null;
            } else{
                title = updateCategoryTitle(entry.getKey(), fConfigCat, filterRequest, filterResponseHES, expDataMap, isMyPartnerRequest);
            }
            fRespCategory.setTitle(title);

            fRespCategory.setShowMore(Boolean.TRUE.equals(fConfigCat.getShowMore()));
            fRespCategory.setMinItemsToShow(fConfigCat.getMinItemsToShow());
            fRespCategory.setShowImageUrl(Boolean.TRUE.equals(fConfigCat.getShowImageUrl()));
            fRespCategory.setViewType(fConfigCat.getViewType());
            fRespCategory.setShowCustomRange(Boolean.TRUE.equals(fConfigCat.getShowCustomRange()));
            fRespCategory.setCustomRangeTitle(fConfigCat.getCustomRangeTitle());
            fRespCategory.setVisible(Boolean.TRUE.equals(fConfigCat.getVisible()));
            fRespCategory.setCollapsed(Boolean.TRUE.equals(fConfigCat.getCollapsed()));
            if (Constants.CLIENT_DESKTOP.equalsIgnoreCase(filterRequest.getClient())) {
                fRespCategory.setIconUrl(fConfigCat.getIconUrl());
            }

            if (!Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource)) {
                fRespCategory.setDescription(fConfigCat.getDescription());
            }

            // [HTL-59441] setting the Description for the filter if key is DPT_COLLECTIONS and POKUS CCL is on
            if(entry.getKey().equalsIgnoreCase(FilterGroup.DPT_COLLECTIONS.name())&& MapUtils.isNotEmpty(expDataMap) && EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(CL_POKUS))){
                if(filterResponseHES != null && filterResponseHES.getDptContextualFilterResponse()!=null && filterResponseHES.getDptContextualFilterResponse().getCityCollectionData()!=null){
                    fRespCategory.setDescription(filterResponseHES.getDptContextualFilterResponse().getCityCollectionData().getCityDescription());
                    fRespCategory.setVisible(false);
                }
            }

            //Exp HAFC is enabled now for PWA also for making Dynamic Price Buckets, HAFC also modifies the Amenities Category response which is not required, added PWA client check for this.

            if (expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER) != null && Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.EXP_HOTEL_ALT_ACCO_FILTER)) && FilterGroup.AMENITIES.name().equalsIgnoreCase(entry.getKey())) {
                List<Filter> filterList = buildFilterList(entry.getValue(), filterResponseHES, filterRequest, expDataMap, entry.getKey(), idContext, commonModifierResponse, fRespCategory, categories, isDynamicFilterOrdering);
                fRespCategory.setFilterCollection(buildFilterCollection(filterList, funnelSource));
                fRespCategory.setSearchable(true);
                fRespCategory.setViewType(Constants.VIEW_TYPE_FLEX);
            } else {
                fRespCategory.setFilters(buildFilterList(entry.getValue(), filterResponseHES, filterRequest, expDataMap, entry.getKey(), idContext, commonModifierResponse, fRespCategory, categories, isDynamicFilterOrdering));
            }
            //dont update minItems if myPartner and category is popular when HFC is true
            boolean updateMinItems= !isMyPartnerRequest || (isMyPartnerRequest && !entry.getKey().equalsIgnoreCase(POPULAR));
            //show more filter experiment HTL-38235
            if (updateMinItems && expDataMap.get(FILTER_PILL_EXP) != null && EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(FILTER_PILL_EXP))) {
                updateMinItemsToShowAndShowMore(fRespCategory);
            }

            fRespCategory.setSingleSelection(Boolean.TRUE.equals(entry.getValue().getSingleSelection()));
            if ((entry.getKey().equalsIgnoreCase(Constants.LOCALITY_GROUP)) || CollectionUtils.isNotEmpty(fRespCategory.getFilters()) || CollectionUtils.isNotEmpty(fRespCategory.getFilterCollection())) {
                if(rankOrder != null){
                    int index = rankOrder.getOrDefault(entry.getKey(), -1);
                    if (fCategoryRankedArr != null && index > 0) {
                        fCategoryRankedArr[index - 1] = fRespCategory;
                    } else {
                        nonRankedFilterCategoryList.add(fRespCategory);
                    }
                }else{
                    nonRankedFilterCategoryList.add(fRespCategory);
                }

            }
            if (entry.getKey().equalsIgnoreCase(FILTER_HOTEL_PRICE_BUCKET) && isMyPartnerRequest){
                updatePriceFilterTitleForTPTMyPartner(filterRequest, fRespCategory);
            }

        }

        if (fCategoryRankedArr != null){
            filterCategoryList = new ArrayList<>(Arrays.asList(fCategoryRankedArr));
            // In case of country page, locality filter is moved to top so that the users can easily filter the hotels by
            // locality in a country before removing null filters
            if (COUNTRY.equalsIgnoreCase(locationType))
                moveLocalityFilterToTop(filterCategoryList, rankOrder);
            // remove null filters
            filterCategoryList = filterCategoryList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }else if(isFilterConfigV2){
            filterCategoryList = new ArrayList<>(nonRankedFilterCategoryList);
            // In case of country page, locality filter is moved to top so that the users can easily filter the hotels by
            // locality in a country before removing null filters
            if (COUNTRY.equalsIgnoreCase(locationType))
                moveLocalityFilterToTop(filterCategoryList);
            // remove null filters
            filterCategoryList = filterCategoryList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
        return filterCategoryList;
    }

    private void setTitleForGccBnplFilter(FilterConfigCategory fConfigCat) {
        Map<String, FilterConfigDetail> payLaterGroup = null;
        if(fConfigCat != null && fConfigCat.getGroups() != null && fConfigCat.getGroups().containsKey(FilterGroup.PAY_LATER.name())) {
             payLaterGroup = fConfigCat.getGroups().get(FilterGroup.PAY_LATER.name());
        }
        if(MapUtils.isNotEmpty(payLaterGroup) && payLaterGroup.values().iterator().next() != null ){
            payLaterGroup.entrySet().iterator().next().getValue().setTitle(polyglotService.getTranslatedData(ConstantsTranslation.GCC_BNPL_FILTER_TITLE));
            logger.debug("Title for BNPL filter is updated for GCC");
        }
    }

    private void updatePriceFilterTitleForTPTMyPartner(FilterCountRequest filterRequest, FilterCategory fRespCategory){
        String currency = filterRequest.getSearchCriteria().getCurrency().toUpperCase();
        for (int index = 0; index < fRespCategory.getFilters().size(); index++){
            Filter filter = fRespCategory.getFilters().get(index);
            filter.setTitle(buildPriceTitleForMyPartner(currency, index, filter.getFilterRange().getMinValue(), filter.getFilterRange().getMaxValue(), fRespCategory.getFilters().size()));
        }
    }

    private String buildPriceTitleForMyPartner(String currency, int sequence, int minValue, int maxValue, int bucketSize){
        if (sequence == bucketSize - 1)
            return Currency.getCurrencyEnum(currency).getCurrencySymbol() + SPACE + Utility.convertToLakhOrCrore(minValue) + "+";
        else
            return Currency.getCurrencyEnum(currency).getCurrencySymbol() + SPACE + Utility.convertToLakhOrCrore(minValue) + " - " + Currency.getCurrencyEnum(currency).getCurrencySymbol() + " " + Utility.convertToLakhOrCrore(maxValue);
    }

    /**
     *
     * @param filterRequest
     * @return Decide whether to remove BNPL filter or not
     * This is done basis AP window checks and BNPL peak dates check
     * Show BNPL filter when AP > 2
     * For Peak Dates show BNPL when AP > BNPL + 2
     */
    private boolean isRemoveBNPLFilter(FilterCountRequest filterRequest) {
        if (filterRequest != null && filterRequest.getSearchCriteria() != null
                && StringUtils.isNotEmpty(filterRequest.getSearchCriteria().getCheckIn()) &&
                MapUtils.isNotEmpty(filterRequest.getExpDataMap()) &&
                Constants.EXP_TRUE_VALUE.equalsIgnoreCase(filterRequest.getExpDataMap().get(EXP_BNPL))) {
            int apWindow = dateUtil.getDaysDiff(LocalDate.now(), LocalDate.parse(filterRequest.getSearchCriteria().getCheckIn()));
            if (MapUtils.isNotEmpty(filterRequest.getExpDataMap()) && TRUE.equalsIgnoreCase(filterRequest.getExpDataMap().get(EXP_BNPL_PEAK_DATE))) {
                int bnplPeakDays = Integer.parseInt(filterRequest.getExpDataMap().get(EXP_BNPL_PEAK_DAYS));
                return bnplPeakDays + bnplApWindowLimit > apWindow;
            }
            return apWindow < bnplApWindowLimit;
        }
        return false;
    }

    /**
     * Move the LOCALITY filter to the first position if its present in the ranked list
     * @param rankedFilterList a list of {@link FilterCategory}  ranked in order
     * @param rankOrder rankOrder taken from filter configuration on PMS
     */
    private void moveLocalityFilterToTop(List<FilterCategory> rankedFilterList, LinkedHashMap<String,Integer> rankOrder) {
        if(CollectionUtils.isEmpty(rankedFilterList)) return;
        int localityIndex = rankOrder != null && rankOrder.containsKey(LOCALITY_GROUP) ? rankOrder.get(LOCALITY_GROUP) : -1;
        if (localityIndex > 0) {
            FilterCategory localityFilterCategory = rankedFilterList.remove(localityIndex - 1);
            rankedFilterList.add(0, localityFilterCategory);
        }
    }

    /**
     * Move the LOCALITY filter to the first position if its present in the ranked list
     * @param rankedFilterList a list of {@link FilterCategory}  ranked in order
     */
    private void moveLocalityFilterToTop(List<FilterCategory> rankedFilterList) {
        if(CollectionUtils.isEmpty(rankedFilterList)) return;
        // int localityIndex = rankedFilterList.indexOf(it.getCategoryName().equals(LOCALITY_GROUP))
        int localityIndex = IntStream.range(0, rankedFilterList.size())
                .filter(i -> LOCALITY_GROUP.equals(rankedFilterList.get(i).getCategoryName()))
                .findFirst()
                .orElse(-1);
        if (localityIndex > 0) {
            FilterCategory localityFilterCategory = rankedFilterList.remove(localityIndex);
            rankedFilterList.add(0, localityFilterCategory);
        }
    }

    /**
     * HTL-38235
     * If size of filter values is greater than filterMinItemsLimit then :
     * -> showMore will be set as true
     * -> minItemsToShow will be filterMinItemsToShow
     * Eg. FilterList size = 10
     * filterMinItemsLimit = 7
     * filterMinItemsToShow = 5
     * 5 filters will be shown with 5 in show more
     **/
    private void updateMinItemsToShowAndShowMore(FilterCategory fRespCategory) {
        if (fRespCategory != null && CollectionUtils.isNotEmpty(fRespCategory.getFilters())) {
            if (fRespCategory.getFilters().size() > filterMinItemsLimit) {
                fRespCategory.setShowMore(true);
                fRespCategory.setMinItemsToShow(filterMinItemsToShow);
            } else {
                fRespCategory.setShowMore(false);
                fRespCategory.setMinItemsToShow(null);
            }
        }
    }

    private Map<String, String> getExpDataMap(String expDataReq) {

        Map<String, String> existingExpData = new HashMap<>();
        if (!StringUtils.isEmpty(expDataReq)) {
            String experimentString = expDataReq.replaceAll("^\"|\"$", "");
            Type type = new TypeToken<Map<String, String>>() {
            }.getType();
            //TODO
            existingExpData = new Gson().fromJson(experimentString, type);
        }
        return existingExpData;
    }

    public String updateCategoryTitle(String categoryName, FilterConfigCategory fConfigCat, FilterCountRequest filterCountRequest, FilterSearchMetaDataResponse filterRespHES, LinkedHashMap<String,String>expDataMap, boolean isMyPartnerRequest) {
        String contextualExpValue = (MapUtils.isNotEmpty(expDataMap)?expDataMap.get(Constants.EXP_CONTEXTUAL_FILTER):EMPTY_STRING);
        if(filterCountRequest != null && filterCountRequest.getDeviceDetails() != null && (ANDROID.equalsIgnoreCase(filterCountRequest.getDeviceDetails().getBookingDevice()) || DEVICE_IOS.equalsIgnoreCase(filterCountRequest.getDeviceDetails().getBookingDevice()))){
            contextualExpValue = EXP_CONTEXTUAL_FILTER_ENABLE_VALUE;
        }
        String categoryTitle = fConfigCat.getTitle();
        switch (categoryName) {
            case Constants.FILTER_HOTEL_PRICE:
                categoryTitle = filterHelper.fetchPriceTitle(expDataMap);
                break;
            case Constants.FILTER_HOTEL_PRICE_BUCKET:
                if(filterCountRequest.getRequestDetails() != null && StringUtils.isNotEmpty(filterCountRequest.getClient()) &&
                        Arrays.asList(DEVICE_OS_DESKTOP, DEVICE_OS_PWA).contains(filterCountRequest.getClient().toLowerCase()) &&
                        (CORP_ID_CONTEXT.equalsIgnoreCase(filterCountRequest.getRequestDetails().getIdContext()) || isMyPartnerRequest || Utility.isGroupBookingFunnel(filterCountRequest.getRequestDetails().getFunnelSource()))) {
                    categoryTitle = filterHelper.fetchPriceTitle(expDataMap);
                }
                break;
            case Constants.FILTER_POPULAR:
                if(StringUtils.isNotBlank(contextualExpValue) &&
                        (!StringUtils.equalsIgnoreCase(contextualExpValue, EXP_CONTEXTUAL_FILTER_DISABLE_VALUE)
                                && !StringUtils.equalsIgnoreCase(contextualExpValue, EXP_CONTEXTUAL_FILTER_MYPARTNER_VALUE))) {
                    categoryTitle = polyglotService.getTranslatedData(ConstantsTranslation.CONTEXTUAL_POPULAR_FILTER_TITLE);
                    if(StringUtils.isEmpty(filterRespHES.getLocationName())) {
                        categoryTitle = categoryTitle.replace("{filter_text}", EMPTY_STRING);
                    }
                }
                // in {place_name}
                String titleShown = polyglotService.getTranslatedData(ConstantsTranslation.FILTER_POPULAR_TITLE_IN);
                if(StringUtils.isNotEmpty(filterRespHES.getLocationName())) {
                    String appendText = (Constants.DOM_COUNTRY.equalsIgnoreCase(filterCountRequest.getSearchCriteria().getCountryCode()) ? titleShown.replace("{place_name}", filterRespHES.getLocationName()) : (Constants.DEFAULT_SITE_DOMAIN.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()))) ? polyglotService.getTranslatedData(ConstantsTranslation.AMONG_INDIANS) : titleShown.replace("{place_name}", filterRespHES.getLocationName()));
                    categoryTitle = categoryTitle.replace("{filter_text}", appendText);
                }
                break;
            case Constants.FILTER_PRICE_MANUAL:
                categoryTitle = filterRespHES.getFilterDataMap().containsKey(FilterGroup.HOTEL_PRICE_BUCKET) ? polyglotService.getTranslatedData(ConstantsTranslation.OR_ENTER_RANGE) : polyglotService.getTranslatedData(ConstantsTranslation.ENTER_PRICE_RANGE);
                break;
            case Constants.PRICE_CHECKBOX:
                categoryTitle = categoryTitle.replace("max_budget_price", ""+ getMaxBudgetPriceForCity(filterCountRequest));
                break;
            case Constants.FILTER_PREVIOUSLY_USED:
                categoryTitle = polyglotService.getTranslatedData(ConstantsTranslation.PREVIOUSLY_USED_FILTER_TITLE);
            default:

        }
        return categoryTitle;

    }

    public int getMaxBudgetPriceForCity(FilterCountRequest filterCountRequest) {
        int defaultMaxBudget = 3000;
        if(filterCountRequest == null || filterCountRequest.getSearchCriteria() == null){
            return defaultMaxBudget;
        }
        String cityCode = filterCountRequest.getSearchCriteria().getCityCode();
        if(org.apache.commons.collections4.MapUtils.isNotEmpty(budgetHotelCityConfig)){
            AtomicReference<Integer> value = new AtomicReference<>(defaultMaxBudget);
            budgetHotelCityConfig.forEach((x,y) -> {
                if(y.contains(cityCode)){
                    value.set(x);
                }
            });
            return value.get();
        }
        return defaultMaxBudget;
    }

    private List<FilterCollection> buildFilterCollection(List<Filter> filterList, String funnelSource){
        List<FilterCollection> filterCollectionList = new ArrayList<>();
        String funnelKey = Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource) ? Constants.FUNNEL_SOURCE_HOMESTAY : Constants.FUNNEL_SOURCE_HOTELS;

        if(MapUtils.isEmpty(amenitiesCategoryConfigPolyGlot) || MapUtils.isEmpty(amenitiesCategoryConfigPolyGlot.get(funnelKey))){
            return filterCollectionList;
        }

        Map<String, AmenityCategory> amenityCategoryMap = amenitiesCategoryConfigPolyGlot.get(funnelKey);

        for(Map.Entry<String, AmenityCategory> amenityCategoryEntry: amenityCategoryMap.entrySet()){
            FilterCollection filterCollection = new FilterCollection();
            filterCollection.setCollectionName(polyglotService.getTranslatedData(amenityCategoryEntry.getValue().getTitle()));
            List<Filter> finalFilters = new ArrayList<>();
            for (Filter filter: filterList){
                if(amenityCategoryEntry.getValue().getAmenities().contains(filter.getFilterValue())){
                    if(Constants.GUESTS_LOVE.equalsIgnoreCase(amenityCategoryEntry.getKey()))
                        filter.setIconType(Constants.ICON_TYPE_LIGHTNING);
                    finalFilters.add(filter);
                }
            }
            if (CollectionUtils.isNotEmpty(finalFilters)) {
                filterCollection.setFilters(finalFilters);
                filterCollectionList.add(filterCollection);
            }
        }
        return filterCollectionList;
    }

    private List<Filter> buildFilterList(
            FilterConfigCategory fConfigCat,
            FilterSearchMetaDataResponse filterResponseHES,
            FilterCountRequest filterCountRequest,
            Map<String, String> expDataMap,
            String filterConfigKey,
            String idContext,
            CommonModifierResponse commonModifierResponse,
            FilterCategory fRespCategory,
            LinkedHashMap<String, FilterConfigCategory> categories,
            boolean isDynamicFilterOrdering
    ) {

        if (MapUtils.isEmpty(fConfigCat.getGroups()))
            return null;

        List<Filter> filterList = new ArrayList<>();
        boolean isMyPartner = Utility.isMyPartnerRequest(commonModifierResponse);
        for (Map.Entry<String, LinkedHashMap<String, FilterConfigDetail>> entry : fConfigCat.getGroups().entrySet()) {
            try {
                if (Constants.FILTER_PRICE_MANUAL.equalsIgnoreCase(entry.getKey())) {
                    Filter filter = new Filter();
                    filter.setFilterGroup(Constants.FILTER_PRICE_MANUAL);
                    filterList.add(filter);
                    return filterList;
                } else if (MapUtils.isNotEmpty(filterResponseHES.getFilterDataMap())
                        && (FilterGroup.DRIVE_WALK_PROXIMITY_KEY_POI.name().equalsIgnoreCase(entry.getKey())
                        || FilterGroup.DRIVE_WALK_PROXIMITY_POI.name().equalsIgnoreCase(entry.getKey())
                        || FilterGroup.DISTANCE_CITY_CENTRE.name().equalsIgnoreCase(entry.getKey()))) {
                    filterList.addAll(getDriveWalkProximityFilter(entry.getKey(), filterResponseHES.getFilterDataMap(), fRespCategory));
                } else if(FilterGroup.BED.name().equalsIgnoreCase(entry.getKey()) || FilterGroup.BEDROOM.name().equalsIgnoreCase(entry.getKey())){
                    filterList.add(getRoomOrBedFilter(entry.getKey(), entry.getValue().get(entry.getKey())));
                } else if (MapUtils.isNotEmpty(filterResponseHES.getFilterCategoryMap()) && CollectionUtils.isNotEmpty(filterResponseHES.getFilterCategoryMap().get(filterConfigKey)) && !myPartnerContextualFilters(expDataMap)) {
                    filterList.addAll(buildFilter(filterResponseHES.getFilterCategoryMap().get(filterConfigKey)));
                    break;
                } else if (null != FilterGroup.getFilterGroupFromFilterName(entry.getKey()) && filterResponseHES.getFilterDataMap().get(FilterGroup.valueOf(entry.getKey())) != null) {
                    if (MapUtils.isNotEmpty(entry.getValue())) {
                        List<Filter> filters = addSelectiveFilter(filterResponseHES, entry.getKey(), entry.getValue(), categories, idContext, filterConfigKey, filterCountRequest, filterList.size());
                        if (CollectionUtils.isNotEmpty(filters)) {
                            boolean isMyPartnerRequest = (commonModifierResponse!=null) && (commonModifierResponse.getExtendedUser()!=null) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
                            if(FilterGroup.HOTELS_USP.name().equals(entry.getKey()) && !utility.isExperimentOn(expDataMap,ICV2) && !isMyPartnerRequest && !StringUtils.equalsIgnoreCase(Constants.CORP_ID_CONTEXT, idContext)){
                                break;
                            } else{
                                filterList.addAll(filters);
                            }
                        }
                    } else {
                        List<Filter> filters = addAllFilterInGroup(filterResponseHES, entry.getKey(), categories, idContext, filterConfigKey, filterCountRequest.getFilterCriteria(),filterCountRequest);
                        if (CollectionUtils.isNotEmpty(filters))
                            filterList.addAll(filters);
                    }
                } else if (null != FilterGroup.getFilterGroupFromFilterName(entry.getKey()) && FilterGroup.valueOf(entry.getKey()) == FilterGroup.HOTEL_PRICE) {
                    filterList.addAll(createDefaultPriceHistogramBuckets(filterCountRequest.getSearchCriteria()));
                }
                else if (null != FilterGroup.getFilterGroupFromFilterName(entry.getKey()) && FilterGroup.valueOf(entry.getKey()) == FilterGroup.HOTEL_PRICE_BUCKET) {
                        filterList.addAll(createDefaultPriceHistogramCorpBuckets(filterCountRequest.getSearchCriteria()));
                }
                else if(filterResponseHES.isOrgPreferredFilter() && null != FilterGroup.getFilterGroupFromFilterName(entry.getKey()) && FilterGroup.valueOf(entry.getKey()) == FilterGroup.HOTELS_SECTION) {
                    filterList.add(createPreferredByCompanyFilter());
                }

                else if (utility.isExperimentOn(expDataMap, EXP_EBCF) && utility.isExperimentTrue(expDataMap,PRICER_V2) && com.mmt.hotels.clientgateway.response.filter.FilterGroup.BEDROOM_COUNT.name().equalsIgnoreCase(entry.getKey()) && filterCountRequest != null && filterCountRequest.getRequestDetails() != null &&
                        FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(filterCountRequest.getRequestDetails().getFunnelSource()) && filterCountRequest.getSearchCriteria() != null && CollectionUtils.isNotEmpty(filterCountRequest.getSearchCriteria().getRoomStayCandidates())){
                    if(!(MapUtils.isNotEmpty(expDataMap) && utility.isExperimentOn(expDataMap, BEDROOM_COUNT_AVAILABLE)
                    && Objects.nonNull(filterCountRequest.getSearchCriteria().getRoomStayCandidates().get(0)) && Objects.nonNull(filterCountRequest.getSearchCriteria().getRoomStayCandidates().get(0).getRooms()))) {
                        int adults = Objects.nonNull(filterCountRequest.getSearchCriteria().getRoomStayCandidates().get(0).getAdultCount()) ? filterCountRequest.getSearchCriteria().getRoomStayCandidates().get(0).getAdultCount() : 0;
                        int children = Objects.nonNull(filterCountRequest.getSearchCriteria().getRoomStayCandidates().get(0).getChildAges()) ? filterCountRequest.getSearchCriteria().getRoomStayCandidates().get(0).getChildAges().size() : 0;
                        filterList.addAll(createDefaultBedRoomCountFilter(entry.getKey(), adults, children ,filterCountRequest.getExpDataMap()));
                    }
                }
                if (isMyPartner && null != FilterGroup.getFilterGroupFromFilterName(entry.getKey()) && FilterGroup.valueOf(entry.getKey()) == FilterGroup.GSTN_ASSURED) {
                    filterList.stream()
                            .filter(filter -> FilterGroup.GSTN_ASSURED.name().equals(filter.getFilterGroup()) && Constants.MYPARTNER_ASSURED_FILTER_VALUE.equals(filter.getFilterValue()))
                            .findFirst()
                            .ifPresent(filter -> {
                                filter.setTooltip(commonConfigConsul.getMmtMyPartnerTooltip());
                                filter.setIconUrl(newIconUrl);
                                fRespCategory.setIconUrl(newIconUrl);
                            });
                }
                if (isMyPartner && entry.getKey() != null && null != FilterGroup.getFilterGroupFromFilterName(entry.getKey()) && FilterGroup.valueOf(entry.getKey()) == FilterGroup.FREE_STAY_FOR_KIDS_AVAIL) {
                    filterList.stream()
                            .filter(filter -> FilterGroup.FREE_STAY_FOR_KIDS_AVAIL.name().equals(filter.getFilterGroup()) && Constants.FREE_STAY_FOR_KIDS_FILTER_VALUE.equals(filter.getFilterValue()))
                            .findFirst()
                            .ifPresent(filter -> {
                                filter.setIconUrl(newIconUrl);
                                fRespCategory.setIconUrl(newIconUrl);
                            });
                }

            } catch (Exception ex) {
                logger.error("Error in configuring filter for :{} , exception message:{}", entry.getKey(), ex.getMessage(), ex);
            }
        }

        appendContextualFiltersForMyPartner(filterList, expDataMap, filterResponseHES, filterConfigKey, filterCountRequest);
        boolean myPartnerRequest = Objects.nonNull(commonModifierResponse) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser());

        if(isMyPartner && FILTER_POPULAR.equalsIgnoreCase(filterConfigKey) && MapUtils.isNotEmpty(filterResponseHES.getFilterCategoryMap()) && commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap())
                && commonModifierResponse.getExpDataMap().containsKey(Constants.EXP_MYPARTNER_HOTSTORE_FILTERS)
                && TRUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(Constants.EXP_MYPARTNER_HOTSTORE_FILTERS))) {
            List<Filter> hotstoreFilters = buildFilter(filterResponseHES.getFilterCategoryMap().get(filterConfigKey));
            appendTopKHotstoreFilters(filterList, hotstoreFilters);
        }

        // Remove duplicate filters based on filterValue, keeping the one with higher count
        filterList = removeDuplicateFilters(filterList);

        filterList = filterList.stream().sorted(Comparator.comparingInt(Filter::getSequence)).collect(Collectors.toList());
        if (isRepositionFiltersWithCountZero(commonModifierResponse)) {
            appendFilterWithCountZeroAtLast(filterList, myPartnerRequest);
        }

        if (PROPERTY_TYPE.equalsIgnoreCase(filterConfigKey) || MERGE_PROPERTY_TYPE.equalsIgnoreCase(filterConfigKey)) {
            if (FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(filterCountRequest.getRequestDetails().getFunnelSource()))
                filterList = filterList.stream().filter(filter -> filter.getCount() > 0).collect(Collectors.toList());
            if (!isDynamicFilterOrdering) {
                sortByPropertyCount(filterList);
            }
        }

        //TODO Add comments
        filterList = fConfigCat.isRemoveZeroCountFilters() ? filterList.stream().filter(filter -> (filter.getCount() != null && filter.getCount() > 0) || (filter.getCount() == null)).collect(Collectors.toList()) : filterList;
        filterList = fConfigCat.getMaxFilterListSize() != null ? filterList.stream().limit(fConfigCat.getMaxFilterListSize()).collect(Collectors.toList()) : filterList;
        filterList = fConfigCat.getCountThreshold() > 0 ? filterList.stream()
                .filter(filter -> FilterGroup.LOCATION.name().equalsIgnoreCase(filter.getFilterGroup()) || (filter.getCount() != null && filter.getCount() > fConfigCat.getCountThreshold()))
                .collect(Collectors.toList()) : filterList;
        if (Boolean.TRUE.equals(fConfigCat.getSortingRequired())) {
            sortByPropertyCount(filterList);
        }

        return filterList;

    }


    private void appendTopKHotstoreFilters(List<Filter> filterList, List<Filter> hotstoreFilters) {
        if (CollectionUtils.isNotEmpty(hotstoreFilters)) {
            if(CollectionUtils.isEmpty(filterList)){
                filterList = new ArrayList<>();
            }
            int count = 0;
            for (Filter filter : hotstoreFilters) {
                if (count >= k_thresholdForHotstoreFilterCountInMyPartnerPopularFilterSection) {
                    break;
                }
                if(filter == null)
                    continue;

                if((StringUtils.isNotEmpty(filter.getFilterGroup()) && filter.getFilterGroup().contains(PAY_AT_HOTEL)) || (StringUtils.isNotEmpty(filter.getFilterValue()) && filter.getFilterValue().contains(PAH)) || (StringUtils.isNotEmpty(filter.getFilterValue()) && filter.getFilterValue().contains(MMT_VALUE_STAYS)) || (StringUtils.isNotEmpty(filter.getFilterGroup()) && filter.getFilterGroup().contains(STAR_RATING))){
                    continue;
                }

                if (filterList.stream().noneMatch(f -> f.getFilterValue().equals(filter.getFilterValue()))) {
                    filterList.add(filter);
                    count++;
                }
            }
        }
    }

    public List<Filter> getDriveWalkProximityFilter(String filterGroupString, Map<FilterGroup, List<com.mmt.hotels.filter.Filter>> filterDataMap, FilterCategory fRespCategory) {
        FilterGroup filterGroup = FilterGroup.getFilterGroupFromFilterName(filterGroupString);
        List<Filter> filterListCG = new ArrayList<>();
        if (MapUtils.isNotEmpty(filterDataMap) && filterDataMap.containsKey(filterGroup)) {
            String filterCategoryTitle = null;

            List<com.mmt.hotels.filter.Filter> sortedFilterList = sortDistanceCategoryFilter(filterDataMap.get(filterGroup));

            for (com.mmt.hotels.filter.Filter filter : sortedFilterList) {
                Filter filterCG = new Filter();
                filterCG.setFilterGroup(filterGroupString);
                filterCG.setFilterValue(filter.getFilterValue());
                filterCG.setTitle(filter.getTitle());
                filterCG.setCount(filter.getCount());
                filterListCG.add(filterCG);

                if (filterCategoryTitle == null && StringUtils.isNotEmpty(filter.getAreaPoiName())) {
                    filterCategoryTitle = filter.getAreaPoiName();
                }
            }

            if (StringUtils.isNotEmpty(filterCategoryTitle)) {
                fRespCategory.setTitle(MessageFormat.format(fRespCategory.getTitle(), filterCategoryTitle));
            }
        }
        return filterListCG;
    }

    /**
     * This method sorts a list of filters based on their distance and mode.
     * The filters are first checked for null values and then further filtered based on certain conditions.
     * The conditions include checks on the filter value, the split value, the distance, and the mode.
     * After the filters have passed these conditions, they are sorted.
     * The sorting logic is as follows:
     * - If the modes of two filters are equal, the filters are sorted based on their distance.
     * - If the mode of the first filter is "wd", it is placed before the second filter.
     * - Otherwise, the second filter is placed before the first.
     * - first filter (wd) is walking distance and second filter (dd) is driving distance.
     *
     * @param filterList the list of filters to be sorted.
     * @return a sorted list of filters.
     */
    public static List<com.mmt.hotels.filter.Filter> sortDistanceCategoryFilter(List<com.mmt.hotels.filter.Filter> filterList) {
        // Return an empty list if the input list is null or empty
        if (filterList == null || filterList.isEmpty() || distanceSortingModesPriorityMap == null) {
            return Collections.emptyList();
        }

        return filterList.stream()
                // Filter out null filters
                .filter(Objects::nonNull)
                // Further filter the list based on certain conditions
                .filter(filter -> {
                    String filterValue = filter.getFilterValue();
                    // Check if the filter value is empty or does not contain "#"
                    if (StringUtils.isEmpty(filterValue) || !filterValue.contains(Constants.HASH_SEPARATOR)) {
                        return false;
                    }

                    String[] splitValue = filterValue.split(Constants.HASH_SEPARATOR);
                    // Check if the split value has at least two elements
                    if (splitValue.length < 2) {
                        return false;
                    }

                    int distance = extractDistance(splitValue[1]);
                    // Check if the distance is the maximum integer value
                    if (distance == Integer.MAX_VALUE) {
                        return false;
                    }

                    String mode = extractMode(splitValue[1]);
                    // Check if the mode is null
                    return mode != null;
                })
                // Sort the filters
                .sorted((filter1, filter2) -> {
                    String filterValue1 = filter1.getFilterValue();
                    String filterValue2 = filter2.getFilterValue();

                    String[] splitValue1 = filterValue1.split(Constants.HASH_SEPARATOR);
                    String[] splitValue2 = filterValue2.split(Constants.HASH_SEPARATOR);

                    int distance1 = extractDistance(splitValue1[1]);
                    int distance2 = extractDistance(splitValue2[1]);

                    String mode1 = extractMode(splitValue1[1]);
                    String mode2 = extractMode(splitValue2[1]);

                    // Compare modes based on priorities
                    int modeComparison = Integer.compare(distanceSortingModesPriorityMap.getOrDefault(mode1, Integer.MAX_VALUE), distanceSortingModesPriorityMap.getOrDefault(mode2, Integer.MAX_VALUE));
                    if (modeComparison != 0) {
                        return modeComparison;
                    }

                    // If the modes are equal, sort based on distance
                    return Integer.compare(distance1, distance2);
                })
                // Collect the sorted filters into a list
                .collect(Collectors.toList());
    }

    private static int extractDistance(String filterValue) {
        try {
            if (filterValue.contains(Constants.HYPEN)) {
                String distanceStr = filterValue.substring(filterValue.lastIndexOf(Constants.HYPEN) + 1);
                if (StringUtils.isEmpty(distanceStr))
                    return Integer.MAX_VALUE;
                return Integer.parseInt(distanceStr);
            }
        } catch (Exception ex) {
            // Do Nothing
        }
        return Integer.MAX_VALUE;
    }

    private static String extractMode(String filterValue) {
        if (filterValue.contains(Constants.UNDERSCORE))
            return filterValue.substring(0, filterValue.lastIndexOf(Constants.UNDERSCORE));
        return null;
    }

    private boolean isOneBedroomOnlyFilterAvailable(int guests) {
        return guests == Integer.parseInt(GUESTS_COUNT_THREE) || guests == Integer.parseInt(GUESTS_COUNT_FOUR);
    }

    private List<Filter> createDefaultBedRoomCountFilter(String filterGroup, Integer adults, Integer children, Map<String, String> expDataMap){
        List<Filter> bedRoomFilter = new ArrayList<>();
        Integer guests = adults + children;
        if(guests < 2) {
            return bedRoomFilter;
        }
        // adult/2 - 2, adult/2 - 1, adult/2, adult/2 + 1, adult/2 + 2 -> new logic
        List<Integer> filterList = new ArrayList<>();
        if (Math.ceil(guests/2 - 2) > 0) {
            filterList.add((int) Math.ceil(guests/2 - 2));
        }
        if (Math.ceil(guests/2 - 1) > 0) {
            filterList.add((int) Math.ceil(guests/2 - 1));
        }
        if (Math.ceil(guests/2) > 0) {
            filterList.add((int) Math.ceil(guests/2));
        }
        if (Math.ceil(guests/2 + 1) > 0) {
            filterList.add((int) Math.ceil(guests/2 + 1));
        }
        if (Math.ceil(guests/2 + 2) > 0) {
            filterList.add((int) Math.ceil(guests/2 + 2));
        }
        for(Integer bedRoomCount : filterList){
            Filter filter = new Filter();
            filter.setFilterGroup(filterGroup);
            filter.setRangeFilter(false);
            if(bedRoomCount > adults) continue;
            if(bedRoomCount == 1){
                if (!isOneBedroomOnlyFilterAvailable(guests)) {
                    continue;
                }
                filter.setTitle(String.valueOf(bedRoomCount) + SPACE + polyglotService.getTranslatedData(ConstantsTranslation.SINGLE_BEDROOM_TITLE) + SPACE + ONLY);
            } else{
                filter.setTitle(String.valueOf(bedRoomCount) + SPACE + polyglotService.getTranslatedData(ConstantsTranslation.BEDROOM_TITLE));
            }
            filter.setFilterValue(String.valueOf(bedRoomCount));
            filter.setQuantityFilter(false);
            bedRoomFilter.add(filter);
        }
        return bedRoomFilter;
    }

    private Filter createPreferredByCompanyFilter() {

        Filter preferredByCompanyFilter = new Filter();
        preferredByCompanyFilter.setFilterGroup(FilterGroup.HOTELS_SECTION.name());
        preferredByCompanyFilter.setFilterValue("ORG_PREFERRED_HOTELS");
        preferredByCompanyFilter.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.ORG_PREFERRED_HOTELS_TITLE));
        return preferredByCompanyFilter;
    }

    /**
     * This method adds the contextual filters along with popular filters if the following conditions are met,
     * a) contextual filterGroup-filterValue combo is not already present in popular filters
     * b) CRF pokus value is 'F'
     * c) contextual filters are available
     * d) contextual filter is present in the list of applicable contextual filters
     * @param filterList list of already existing filters
     * @param expDataMap experiment data contains CRF
     * @param filterResponseHES filterCount response from HES
     * @param filterConfigKey represents the filterCategory
     */
    private void appendContextualFiltersForMyPartner(List<Filter> filterList, Map<String, String> expDataMap, FilterSearchMetaDataResponse filterResponseHES, String filterConfigKey, FilterCountRequest filterCountRequest) {
        Set<String> alreadyPresentFilters = createUniqueKeySetForFilters(filterList);
        Set<String> contextualFiltersToInclude = createUniqueKeySetForFilters(mpContextualFiltersApplicable);
        if (!shouldAddContextualFilters(expDataMap, filterResponseHES, filterConfigKey)) return;
        List<Filter> contextualFilters = buildFilter(filterResponseHES.getFilterCategoryMap().get(filterConfigKey));
        for (Filter filter: contextualFilters){
            String filterKey = filter.getFilterGroup() + HYPEN + filter.getFilterValue();
            if(alreadyPresentFilters.contains(filterKey) || !contextualFiltersToInclude.contains(filterKey)) continue;

            if (isUnmarriedCouplesFilter(filter) && getTotalAdultCount(filterCountRequest) == 1) {
                continue;
            }
            filterList.add(filter);
        }
    }

    private int getTotalAdultCount(FilterCountRequest filterCountRequest) {
        if (filterCountRequest == null ||
                filterCountRequest.getSearchCriteria() == null ||
                CollectionUtils.isEmpty(filterCountRequest.getSearchCriteria().getRoomStayCandidates())) {
            return 0;
        }

        return utility.getTotalAdultsFromRequest(filterCountRequest.getSearchCriteria().getRoomStayCandidates());
    }

    private boolean isUnmarriedCouplesFilter(Filter filter) {
        return HOUSE_RULES.equalsIgnoreCase(filter.getFilterGroup()) &&
                UNMARRIED_COUPLES_ALLOWED.equalsIgnoreCase(filter.getFilterValue());
    }

    private List<Filter> appendDPTContextualFiltersForMyPartner(List<Filter> filterList, FilterSearchMetaDataResponse filterResponseHES, String filterConfigKey) {
        List<Filter> dptAppendedFilterList = new ArrayList<>();
        if (filterResponseHES.getDptContextualFilterResponse() != null && MapUtils.isNotEmpty(filterResponseHES.getDptContextualFilterResponse().getPrimaryFilterValueMap())
                && filterResponseHES.getDptContextualFilterResponse().getPrimaryFilterValueMap().containsKey(filterConfigKey)) {
            Set<String> alreadyPresentFilters = createUniqueKeySetForFilters(filterList);
            List<com.mmt.hotels.filter.Filter> dptFiltersFromHES = filterResponseHES.getDptContextualFilterResponse().getPrimaryFilterValueMap().get(filterConfigKey);
            dptFiltersFromHES.forEach(filter -> filter.setTranslationRequired(true));
            List<Filter> dptFilters = buildFilter(dptFiltersFromHES);
            int filtersAdded = 0;
            int dptSequence = filterList.size();
            for (Filter dptFilter : dptFilters) {
                if (filtersAdded >= maxDPTFiltersForMP)
                    break;
                String filterKey = dptFilter.getFilterGroup() + HYPEN + dptFilter.getFilterValue();
                if (!alreadyPresentFilters.contains(filterKey)) {
                    dptFilter.setSequence(dptSequence++);
                    dptAppendedFilterList.add(dptFilter);
                    filtersAdded++;
                }
            }
            logger.debug("{} DPT Filters added in Popular Filter", filtersAdded);
        }
        return dptAppendedFilterList;
    }
    /**
     * @param filterList list of filters
     * @return a set of strings in the format filterGroup-filterValue by iterating over filterList
     */
    private Set<String> createUniqueKeySetForFilters(List<Filter> filterList){
        if (CollectionUtils.isEmpty(filterList)) return new HashSet<>();
        return filterList.stream()
                .map(filter -> filter.getFilterGroup() + HYPEN + filter.getFilterValue())
                .collect(Collectors.toSet());
    }

    /**
     * ContextualFilters to be added if the following conditions are met
     * a) expData contains CRF key and its value is 'F' (this only comes for my partner)
     * b) contextual Filters are coming from HES response
     * @param expDataMap a map of experiments
     * @param filterResponseHES response from HES
     * @param filterConfigKey represents current filter category
     * @return a boolean representing whether contextualFilters should be added or not
     */
    private boolean shouldAddContextualFilters(Map<String, String> expDataMap, FilterSearchMetaDataResponse filterResponseHES, String filterConfigKey){
        return myPartnerContextualFilters(expDataMap) &&
                MapUtils.isNotEmpty(filterResponseHES.getFilterCategoryMap()) &&
                CollectionUtils.isNotEmpty(filterResponseHES.getFilterCategoryMap().get(filterConfigKey));
    }

    /**
     * @param expData a map of experiments
     * @return true if CRF experiment is 'F'
     */

    private boolean myPartnerContextualFilters(Map<String, String> expData){
        return MapUtils.isNotEmpty(expData) &&
                expData.containsKey(Constants.EXP_CONTEXTUAL_FILTER) &&
                expData.get(EXP_CONTEXTUAL_FILTER).equalsIgnoreCase(EXP_CONTEXTUAL_FILTER_MYPARTNER_VALUE);
    }

    private boolean isRepositionFiltersWithCountZero(CommonModifierResponse commonModifierResponse){
//        this function helps us to identify if we have to resposition the filters with count 0 at last HTL-37652
        return (DEVICE_OS_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) || DEVICE_OS_PWA.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) && commonModifierResponse!=null && commonModifierResponse.getExtendedUser()!=null &&
                Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
    }

    private void appendFilterWithCountZeroAtLast(List<Filter> filterList, boolean isMyPartnerRequest){
//        this function extracts the filters that has count 0 in filter list and then append them at last
        if(CollectionUtils.isNotEmpty(filterList)) {
            List<Filter> filtersWithCountZero = new ArrayList<>();
            filterList.forEach(e -> {
                if(isMyPartnerRequest)
                    if (StringUtils.isNotEmpty(e.getFilterGroup()) && !HOTEL_PRICE.equalsIgnoreCase(e.getFilterGroup()) && !FILTER_PRICE_BUCKET_HES.equalsIgnoreCase(e.getFilterGroup()) && !CTA_RATES.equalsIgnoreCase(e.getFilterGroup()) && (e.getCount() == null || e.getCount() == 0)) {
                        filtersWithCountZero.add(e);
                    }
                else
                    if (StringUtils.isNotEmpty(e.getFilterGroup()) && !HOTEL_PRICE.equalsIgnoreCase(e.getFilterGroup()) && !FILTER_PRICE_BUCKET_HES.equalsIgnoreCase(e.getFilterGroup()) && e.getCount() != null && e.getCount() == 0) {
                        filtersWithCountZero.add(e);
                    }
            });
            filtersWithCountZero.forEach(e -> {
                filterList.remove(e);
                filterList.add(e);
            });
        }
    }

    private void sortByPropertyCount(List<Filter> filterList) {
        try
        {
            if(CollectionUtils.isNotEmpty(filterList))
                filterList.sort(Comparator.comparing(Filter::getCount).reversed());
        }
        catch (Exception ex)
        {
            logger.error("Error while sorting filterList on the basis of PropertyCount",ex);
        }
    }

    /**
     * Removes duplicate filters from the list based on filterValue.
     * When duplicates are found, keeps the filter with the higher count.
     * Maintains the original order of filters and includes filters without a filterValue.
     *
     * @param filterList the list of filters to deduplicate
     * @return a new list with duplicates removed
     */
    private List<Filter> removeDuplicateFilters(List<Filter> filterList) {
        if (CollectionUtils.isEmpty(filterList)) {
            return filterList;
        }
        List<Filter> result = new ArrayList<>();
        Map<String, Integer> uniqueFiltersMap = new HashMap<>();

        for (Filter filter : filterList) {
            String filterValue = filter.getFilterValue();
            if (StringUtils.isNotEmpty(filterValue)) {
                Integer currentCount = filter.getCount() != null ? filter.getCount() : 0;

                if (uniqueFiltersMap.containsKey(filterValue)) {
                    Integer existingCount = uniqueFiltersMap.get(filterValue) != null ? uniqueFiltersMap.get(filterValue) : 0;

                    if (currentCount > existingCount) {
                        // Remove existing filter with same filterValue from result
                        result.removeIf(f -> filterValue.equals(f.getFilterValue()));

                        uniqueFiltersMap.put(filterValue, currentCount);
                        result.add(filter);
                    }
                } else {
                    uniqueFiltersMap.put(filterValue, currentCount);
                    result.add(filter);
                }
            } else {
                result.add(filter);
            }
        }

        return result;
    }

    public List<Filter> buildFilter(List<com.mmt.hotels.filter.Filter> filters) {
        List<Filter> filterList = new ArrayList<>();
        filters.forEach(filterHes -> {
          filterList.add(commonResponseTransformer.buildFilterCG(filterHes));
        });
        return filterList;
    }

    private Filter getRoomOrBedFilter(String filterGroup, FilterConfigDetail filterConfigDetail) {
        Filter filter = new Filter();
        filter.setQuantityFilter(true);
        filter.setTitle(filterConfigDetail.getTitle());
        filter.setFilterGroup(filterGroup);
        filter.setFilterValue("0");
        filter.setIconList(filterConfigDetail.getIconList());
        filter.setImageUrl(filterConfigDetail.getImageUrl());
        return filter;
    }

    private List<Filter> createDefaultPriceHistogramCorpBuckets(SearchHotelsCriteria searchHotelsCriteria) {
        List<Filter> filterList = new ArrayList<>();
        String currency = StringUtils.isNotBlank(searchHotelsCriteria.getCurrency()) ? searchHotelsCriteria.getCurrency().toUpperCase() : searchHotelsCriteria.getCurrency();
        if (StringUtils.isNotEmpty(searchHotelsCriteria.getCountryCode()) && !DOM_COUNTRY.equalsIgnoreCase(searchHotelsCriteria.getCountryCode())) {
            buildIntlStaticFilters(searchHotelsCriteria, currency, filterList, FilterGroup.HOTEL_PRICE_BUCKET.name());
        } else if (defaultPriceHistConfigCorp.containsKey(currency)) {
            List<Integer> config = defaultPriceHistConfigCorp.get(currency);
            int sequence = 0;
            for (int i = 0; i < config.get(0) + config.get(1); i += config.get(1)) {
                int max = (i + config.get(1)) > config.get(0) ? config.get(0) * 7 : (i + config.get(1));
                Filter filter = buildPriceFilter(FilterGroup.HOTEL_PRICE_BUCKET.name(), sequence++, i, max, currency, config.get(0), 0);
                filterList.add(filter);
            }
        }

        return filterList;
    }

    public List<Filter> createDefaultPriceHistogramBuckets(SearchHotelsCriteria searchHotelsCriteria) {
        List<Filter> filterList = new ArrayList<>();
        String currency = StringUtils.isNotBlank(searchHotelsCriteria.getCurrency()) ? searchHotelsCriteria.getCurrency().toUpperCase() : searchHotelsCriteria.getCurrency();
        if (StringUtils.isNotEmpty(searchHotelsCriteria.getCountryCode()) && !DOM_COUNTRY.equalsIgnoreCase(searchHotelsCriteria.getCountryCode())) {
            buildIntlStaticFilters(searchHotelsCriteria, currency, filterList, FilterGroup.HOTEL_PRICE.name());
        } else if (defaultPriceHistConfig.containsKey(currency)) {
            List<Integer> config = defaultPriceHistConfig.get(currency);
            int sequence = 0;
            for (int i = 0; i < config.get(0); i += config.get(1)) {
                int max = (i + config.get(1)) > config.get(0) ? config.get(0) : (i + config.get(1));
                Filter filter = buildPriceFilter(FilterGroup.HOTEL_PRICE.name(), sequence++, i, max, currency, config.get(0), 0);
                filterList.add(filter);
            }
        }

        return filterList;
    }

    /* For IH Properties, there is different price buckets for Different cities for which separate config is added.
            Price Config [9000,2000,3000] => In This Config first Index is Upper Limit, second index is the price bucket gap of price buckets after 1st bucket,
             and last index is the starting limit of first bucket
            i = 0  {minRange = i,maxRange = i+config[2]} => {0 - 3k} and i will be increased by i+config[1] if i is not 0 othwerise i+config[2] => 3000
            i == 3000 {minRange = i,maxRange = i+config[1]} => {3k - 5k} and value of i will become (i+config[1]) = 5K
            i == 2 {minRange = i,maxRange = i+config[1]} => {5k - 7k} and value of i will become (i+config[1]) = 7K
            i == 3 {minRange = i,manRange = i+config[1]} => {7k - 9k} and value of i will become (i+config[1]) = 9K
            if minRange == config[0] then -> {minRange = i,maxRange = config[0]*7} => {9k - 63k}
            The loop will break after i becomes greater than the uppper limit which is config[0]
         */
    private void buildIntlStaticFilters(SearchHotelsCriteria searchHotelsCriteria, String currency, List<Filter> filterList, String filterGroup) {
        String priceBucket = StringUtils.isNotEmpty(searchHotelsCriteria.getLocationId()) ? findPriceBucketIfPresent(searchHotelsCriteria.getLocationId()) : StringUtils.isNotEmpty(searchHotelsCriteria.getCityCode()) ? findPriceBucketIfPresent(searchHotelsCriteria.getCityCode()) : StringUtils.EMPTY;
        if (StringUtils.isNotEmpty(priceBucket) && cityWisePriceBucketsConfigMapIH.containsKey(currency)) {
            List<Integer> config = cityWisePriceBucketsConfigMapIH.get(currency).get(priceBucket);
            int sequence = 0;
            for (int i = 0; i <= config.get(0); ) {
                Filter filter = buildPriceFilter(filterGroup, sequence++, i, getMaxPrice(config, i), currency, config.get(0), 0);
                i += i == 0 ? config.get(2) : config.get(1);
                filterList.add(filter);
            }
        } else if (defaultPriceConfigIHMap.containsKey(currency)) {
            int sequence = 0;
            List<List<Integer>> list = defaultPriceConfigIHMap.get(currency);
            for (List<Integer> bucket : list) {
                Filter filter = buildPriceFilter(filterGroup, sequence++, bucket.get(0), bucket.get(1), currency, 0, list.size());
                filterList.add(filter);
            }
            /*For Last Index creating filter separately as this filter cannot be created by loop,
             setting minValue from second index of last config and maxValue as second index X7 */
            Filter newFilter = new Filter();
            if (list.size() > 0) {
                List<Integer> bucket = list.get(list.size() - 1);
                newFilter.setFilterGroup(filterGroup);
                newFilter.setRangeFilter(true);
                newFilter.setSequence(sequence);
                newFilter.setFilterRange(new FilterRange());
                newFilter.getFilterRange().setMinValue(bucket.get(1));
                newFilter.getFilterRange().setMaxValue(bucket.get(1) * 7);
                newFilter.setTitle(MessageFormat.format(ABOVE_TEXT, Currency.getCurrencyEnum(currency).getCurrencySymbol(), newFilter.getFilterRange().getMinValue()));
                filterList.add(newFilter);
            }
        }
    }

    //This method returns the static priceBucket valid for the searchedCity
    private String findPriceBucketIfPresent(String cityCode) {
        for (Map.Entry<String, HashSet<String>> entry : cityWisePriceConfigMapIH.entrySet()) {
            if (entry.getValue().contains(cityCode)) {
                return entry.getKey();
            }
        }
        return StringUtils.EMPTY;
    }

    private Filter buildPriceFilter(String filterGroup, int sequence, int minValue, int maxValue, String currency, int maxBucketPrice, int bucketSize) {
        Filter filter = new Filter();
        filter.setFilterGroup(filterGroup);
        filter.setRangeFilter(true);
        filter.setSequence(sequence);
        filter.setFilterRange(new FilterRange());
        filter.getFilterRange().setMinValue(minValue);
        filter.getFilterRange().setMaxValue(maxValue);
        if (minValue == 0)
            filter.setTitle(MessageFormat.format(UNDER_TEXT, Currency.getCurrencyEnum(currency).getCurrencySymbol(), filter.getFilterRange().getMaxValue()));
        else if ((minValue >= maxBucketPrice && bucketSize == 0) || (sequence == bucketSize))
            filter.setTitle(MessageFormat.format(ABOVE_TEXT, Currency.getCurrencyEnum(currency).getCurrencySymbol(), filter.getFilterRange().getMinValue()));
        else
            filter.setTitle(Currency.getCurrencyEnum(currency).getCurrencySymbol() + SPACE + filter.getFilterRange().getMinValue() + " - " + Currency.getCurrencyEnum(currency).getCurrencySymbol() + " " + filter.getFilterRange().getMaxValue());

        return filter;
    }

    private Integer getMaxPrice(List<Integer> config, int index) {
        if (index == 0) {
            return index + config.get(2) > config.get(0) ? config.get(0) * 7 : index + config.get(2);
        } else {
            return index + config.get(1) > config.get(0) ? config.get(0) * 7 : index + config.get(1);
        }
    }

    private List<Filter> addAllFilterInGroup(
            FilterSearchMetaDataResponse filterResponseHES,
            String groupKey,
            LinkedHashMap<String, FilterConfigCategory> categories,
            String idContext,
            String filterConfigKey,
            List<com.mmt.hotels.clientgateway.request.Filter> filterCriteria,
            FilterCountRequest filterCountRequest
    ) {
        List<Filter> filterList = null;
        if (filterResponseHES.getFilterDataMap().containsKey(FilterGroup.valueOf(groupKey))) {
            filterList = new ArrayList<>();
            int position = 0;
            for (com.mmt.hotels.filter.Filter filterHES : filterResponseHES.getFilterDataMap().get(FilterGroup.valueOf(groupKey))) {
                try {
                    Filter filter = createFilter(null, filterHES, idContext, filterConfigKey, filterCriteria);

                    if (filterResponseHES != null &&
                            filterResponseHES.getDptContextualFilterResponse() != null &&
                            filterResponseHES.getDptContextualFilterResponse().getInlineCollections() != null &&
                            filterCountRequest != null &&
                            filterCountRequest.getExpDataMap() != null &&
                             EXP_TRUE_VALUE.equalsIgnoreCase(filterCountRequest.getExpDataMap().get(CL_POKUS))) {

                        List<InlineFilterDetails> entry = filterResponseHES.getDptContextualFilterResponse().getInlineCollections();

                        for (InlineFilterDetails details : entry) {
                            if (details != null && details.getCategory().equals(filterHES.getFilterValue())) {
                                String roundImageUrl = details.getRoundImgUrl();
                                filter.setImageUrl(roundImageUrl);
                                break;
                            }
                        }
                    }

                    if (filter != null) {
                        filter.setSequence(position++);
                        filterList.add(filter);
                    }
                    if (CollectionUtils.isNotEmpty(filterHES.getSubGroup())) {
                        filter.setSubFilterCategory(createFilterSubGroup(filterHES.getSubGroup(), filterResponseHES, categories, idContext, filterConfigKey, filterCriteria));
                    }
                } catch (Exception ex) {
                    logger.error("Error in configuring filter for :{} , exception message:{}", groupKey, ex.getMessage(), ex);
                }
            }
        }
        return filterList;
    }

    private List<Filter> addSelectiveFilter(
            FilterSearchMetaDataResponse filterResponseHES,
            String groupKey,
            LinkedHashMap<String, FilterConfigDetail> filterConfigMap,
            LinkedHashMap<String, FilterConfigCategory> categories,
            String idContext,
            String filterConfigKey,
            FilterCountRequest filterCountRequest,
            int existingFilterListSize
    ) {
        List<Filter> filterList = new ArrayList<>();
        for (Map.Entry<String, FilterConfigDetail> fEntry : filterConfigMap.entrySet()) {
            try {
                //Not adding filters in response based upon country INTL/DOM
                if (fEntry != null && fEntry.getValue() != null && MapUtils.isNotEmpty(fEntry.getValue().getCondition())) {
                    Map<String, Object> condition = fEntry.getValue().getCondition();
                    if (condition.containsKey(Constants.COUNTRY)) {
                        String country = Utility.isDomOrIntl(filterCountRequest.getSearchCriteria());
                        if (!country.equalsIgnoreCase(String.valueOf(condition.get(Constants.COUNTRY)))) {
                            continue;
                        }
                    }
                }

                Optional<com.mmt.hotels.filter.Filter> f;
                String[] filterString = fEntry.getKey().split(":");
                String filterVal = filterString[0];
                int position;
                if (filterString.length > 1)
                    position = Integer.parseInt(filterString[1]);
                else
                    position = existingFilterListSize + filterList.size();
                if (filterVal.contains(Constants.FILTER_PREFERRED)) {
                    f = filterResponseHES.getFilterDataMap().get(FilterGroup.valueOf(groupKey)).stream().filter(a -> a.getFilterValue().contains(filterVal)).findFirst();
                }
                if(BOOK_NOW_AT_0.equalsIgnoreCase(filterVal) &&
                        (filterResponseHES.getMpFareHoldStatus()==null || !filterResponseHES.getMpFareHoldStatus().isHoldEligible() ||
                                filterResponseHES.getMpFareHoldStatus().getBookingAmount()!=0 ))
                    continue;
                if(BOOK_NOW_AT_1.equalsIgnoreCase(filterVal) &&
                        (filterResponseHES.getMpFareHoldStatus()==null || !filterResponseHES.getMpFareHoldStatus().isHoldEligible() ||
                                filterResponseHES.getMpFareHoldStatus().getBookingAmount()!=1 ))
                    continue;

                    /* Group Deals filter should not come for funnels other than GROUP */
                else if (filterCountRequest.getRequestDetails() != null && !StringUtils.equalsIgnoreCase(filterCountRequest.getRequestDetails().getFunnelSource(), FUNNEL_SOURCE_GROUP_BOOKING) && StringUtils.equalsIgnoreCase(filterVal, GROUP_DEALS)) {
                    continue;
                } else {
                    f = filterResponseHES.getFilterDataMap().get(FilterGroup.valueOf(groupKey)).stream().filter(a -> filterVal.equalsIgnoreCase(a.getFilterValue())).findFirst();
                }

                if (f.isPresent()) {
                    Filter filter = createFilter(fEntry.getValue(), f.get(), idContext, filterConfigKey, filterCountRequest.getFilterCriteria());
                    if (filter != null) {
                        filter.setSequence(position);
                        filterList.add(filter);
                    }
                    if (CollectionUtils.isNotEmpty(f.get().getSubGroup())) {
                        filter.setSubFilterCategory(createFilterSubGroup(f.get().getSubGroup(), filterResponseHES, categories, idContext, filterConfigKey, filterCountRequest.getFilterCriteria()));
                    }
                }

            } catch (Exception ex) {
                logger.error("Error in configuring filter for :{} , exception message:{}", fEntry.getKey(), ex.getMessage(), ex);
            }
        }
        return filterList;
    }

    public FilterCategory createFilterSubGroup(List<FilterGroup> filterHESSubGroupList, FilterSearchMetaDataResponse filterResponseHES, LinkedHashMap<String, FilterConfigCategory> categories, String idContext, String filterConfigKey, List<com.mmt.hotels.clientgateway.request.Filter> filterCriteria) {
        FilterCategory filterCategory = null;
        if (CollectionUtils.isNotEmpty(filterHESSubGroupList) && filterResponseHES != null) {

            for (com.mmt.hotels.filter.FilterGroup filterHESSubGroup : filterHESSubGroupList) {
                List<com.mmt.hotels.filter.Filter> filterHESSubList = filterResponseHES.getFilterDataMap().get(filterHESSubGroup);
                Optional<FilterConfigCategory> filterConfigCategory = categories.values().stream().filter(a -> a.getGroups().containsKey(filterHESSubGroup.name())).findFirst();

                if (CollectionUtils.isNotEmpty(filterHESSubList) && filterConfigCategory.isPresent()) {
                    filterCategory = new FilterCategory();
                    List<Filter> subFilterList = new ArrayList<>();
                    for (com.mmt.hotels.filter.Filter filterHESSub : filterHESSubList) {
                        FilterConfigDetail filterConfigDetail = filterConfigCategory.get().getGroups().get(filterHESSubGroup.name()).get(filterHESSub.getFilterValue());
                        Filter filter = createFilter(filterConfigDetail, filterHESSub, idContext, filterConfigKey, filterCriteria);
                        if (filter != null) {
                            subFilterList.add(filter);
                        }
                    }

                    filterCategory.setViewType(filterConfigCategory.get().getViewType());
                    filterCategory.setShowCustomRange(Boolean.TRUE.equals(filterConfigCategory.get().getShowCustomRange()));
                    filterCategory.setCustomRangeTitle(filterConfigCategory.get().getCustomRangeTitle());
                    filterCategory.setVisible(Boolean.TRUE.equals(filterConfigCategory.get().getVisible()));
                    filterCategory.setFilters(subFilterList);
                    filterCategory.setTitle(filterConfigCategory.get().getTitle());
                    filterCategory.setMinItemsToShow(filterConfigCategory.get().getMinItemsToShow());
                    filterCategory.setShowImageUrl(Boolean.TRUE.equals(filterConfigCategory.get().getShowImageUrl()));
                    filterCategory.setShowMore(Boolean.TRUE.equals(filterConfigCategory.get().getShowMore()));
                    filterCategory.setIconUrl(filterConfigCategory.get().getIconUrl());
                }

            }
        }
        return filterCategory;
    }

    public Filter createFilter(FilterConfigDetail filterConfigDetail, com.mmt.hotels.filter.Filter filterHES, String idContext, String filterConfigKey,
                               List<com.mmt.hotels.clientgateway.request.Filter> filterCriteria) {
        // for GCC funnel, if filter count is 0 and appliedFilter is null then don't need to send those filters to client
        if (Utility.isGccOrKsa() && filterHES != null && filterHES.getCount() != null && filterHES.getCount() == 0 && CollectionUtils.isEmpty(filterCriteria)) {
            return null;
        }
        Filter filter = new Filter();
        if (filterConfigDetail != null) {
            filter.setSubTitle(filterConfigDetail.getSubTitle());
            filter.setTitle(filterConfigDetail.getTitle());
            filter.setDescription(filterConfigDetail.getDescription());
            filter.setImageUrl(filterConfigDetail.getImageUrl());
            filter.setIconList(filterConfigDetail.getIconList());
            filter.setInfoText(filterConfigDetail.getInfoText());
            filter.setInfoTag(filterConfigDetail.getInfoTag());
        } else if (filterHES != null) {
            filter.setTitle(filterHES.getTitle());

        }
        if (filterHES!=null) {
            if (filterHES.getCount()!=null && !isHotelCloudForCorp(filterHES, idContext))
                filter.setCount(filterHES.getCount());
            filter.setFilterGroup(filterHES.getFilterGroup().name());
            filter.setFilterValue(filterHES.getFilterValue());
            if (filterHES.getTooltip() != null) {
                filter.setTooltip(filterHES.getTooltip());
            }
            if (StringUtils.isNotBlank(filterHES.getSubTitle())) {
                filter.setSubTitle(filterHES.getSubTitle());
            }
            if(StringUtils.isBlank(filter.getTitle()))
                filter.setTitle(filterHES.getTitle());
            if(StringUtils.isBlank(filter.getTitle()))
                filter.setTitle(filterHES.getFilterValue());
            // client doesnt need unnecessary false values. so made wrapper Boolean and setting null for false
            filter.setRangeFilter(filterHES.isRangeFilter() ? filterHES.isRangeFilter() : null);
            if (filterHES.isRangeFilter()) {
                FilterRange fRange = new FilterRange();
                fRange.setMinValue(filterHES.getFilterRange().getMinValue());
                fRange.setMaxValue(filterHES.getFilterRange().getMaxValue());
                filter.setFilterRange(fRange);
            }
            filter.setSuggestedFilters(commonResponseTransformer.buildSuggestedFilters(filterHES.getSuggestedFilters()));
        }
        if (filter.getFilterGroup().equalsIgnoreCase(FilterGroup.AMENITIES.name()) && altAccoAmenities.contains(filter.getFilterValue()) && !StringUtils.equalsIgnoreCase(Constants.CORP_ID_CONTEXT, idContext)
                && !filterConfigKey.equalsIgnoreCase(FILTER_SPACE)) {
            filter.setIconList(new ArrayList<>());
            filter.getIconList().add(Constants.HOMESTAY_ICON);
        }
        return filter;
    }

    // For Desktop, price_bucket_filter is remove for B2C and international hotel if originListingMap flag is true from client.
    private boolean isRemovePriceBucketFilter(FilterCountRequest filterCountRequest, String idContext) {
        return filterCountRequest != null && Constants.B2C.equalsIgnoreCase(idContext) && filterCountRequest.getSearchCriteria() != null && !Constants.DOM_COUNTRY.equalsIgnoreCase(filterCountRequest.getSearchCriteria().getCountryCode()) && filterCountRequest.getFeatureFlags() != null && filterCountRequest.getFeatureFlags().isOriginListingMap();
    }
    private boolean isHotelCloudForCorp( com.mmt.hotels.filter.Filter filterHES, String idContext) {
        return FilterGroup.HOTELCLOUD.equals(filterHES.getFilterGroup()) && idContext.equals(CORP_ID_CONTEXT);
    }

    private Tuple<Boolean,Boolean> showSupplierFilters(String checkin, FilterSearchMetaDataResponse filterSearchMetaDataResponse, boolean isGcc) {
        boolean doNotShowEarlyBird = false;
        boolean doNotShowLastMinute = false;
        if (filterSearchMetaDataResponse.getFilterDataMap().containsKey(FilterGroup.DEALS)) {
            Optional<com.mmt.hotels.filter.Filter> lastMinute = filterSearchMetaDataResponse.getFilterDataMap().get(FilterGroup.DEALS).stream().filter(f-> PromotionalOfferType.LAST_MINUTE.getName().equalsIgnoreCase(f.getFilterValue())).findFirst();
            if (lastMinute.isPresent() && lastMinute.get().getCount()!=null && lastMinute.get().getCount()==0) {
                doNotShowLastMinute = true;
            }
            if (isGcc && lastMinute.isPresent() && lastMinute.get().getCount() != null && lastMinute.get().getCount() <= supplierDealsFilterCountLimit) {
                doNotShowLastMinute = true;
            }
            Optional<com.mmt.hotels.filter.Filter> earlyBird = filterSearchMetaDataResponse.getFilterDataMap().get(FilterGroup.DEALS).stream().filter(f-> PromotionalOfferType.EARLY_BIRD.getName().equalsIgnoreCase(f.getFilterValue())).findFirst();
            if (earlyBird.isPresent() && earlyBird.get().getCount()!=null && earlyBird.get().getCount()==0) {
                doNotShowEarlyBird = true;
            }
            if (isGcc && earlyBird.isPresent() && earlyBird.get().getCount() != null && earlyBird.get().getCount() <= supplierDealsFilterCountLimit) {
                doNotShowEarlyBird = true;
            }
        }
        boolean showEarlyBird = false, showLastMinute = false;
        if (StringUtils.isNotBlank(checkin)) {
            int ap = dateUtil.getDaysDiff(LocalDate.now(), LocalDate.parse(checkin));
            showLastMinute = ap <= 3 && !doNotShowLastMinute;
            showEarlyBird = ap >= 7 && !doNotShowEarlyBird;
        }
        logger.debug("doNotShowEarlyBird={}, doNotShowLastMinute={}, showEarlyBird={}, showLastMinute={}",doNotShowEarlyBird,doNotShowLastMinute,showEarlyBird,showLastMinute);
        return new Tuple<>(showEarlyBird,showLastMinute);
    }

    private int calculateOptimalDPTInsertionIndex(FilterCategory fg, int dptFiltersCount) {
        int currentFilterCount = fg.getFilters().size();

        // Get effective minItemsToShow value
        int effectiveMinItemsToShow = (fg.getMinItemsToShow() != null && fg.getMinItemsToShow() > 0)
            ? fg.getMinItemsToShow()
            : filterMinItemsToShow;

        // Calculate insertion index to ensure DPT filters are within visible range
        int maxAllowedStartIndex = Math.max(0, effectiveMinItemsToShow - dptFiltersCount);

        return Math.min(maxAllowedStartIndex, currentFilterCount);
    }



    /**
     * Ensures smart filter pill is always added at sequence 3 position before returning response
     * This is the final method that guarantees smart filter pill appears in ALL cases when experiment is enabled
     */
    private void addSmartFilterPill(FilterResponse filterResponse, LinkedHashMap<String, String> expDataMap, FilterCountRequest filterRequest) {
        // Check if experiment is enabled
        boolean isExperimentEnabled = MapUtils.isNotEmpty(expDataMap) && TRUE.equalsIgnoreCase(expDataMap.get(ExperimentKeys.SHOW_SMART_FILTER.getKey()));

        // Check if device is Android or iOS
        String device = (filterRequest.getDeviceDetails()!=null) ? filterRequest.getDeviceDetails().getBookingDevice() : StringUtils.EMPTY;
        boolean isMobileDevice = Constants.DEVICE_IOS.equalsIgnoreCase(device) || ANDROID.equalsIgnoreCase(device);

        // Check funnel source
        String funnelSource = "";
        if(filterRequest.getRequestDetails() != null && filterRequest.getRequestDetails().getFunnelSource() != null){
            funnelSource = filterRequest.getRequestDetails().getFunnelSource();
        }
        boolean isHotelFunnel = funnelSource.equalsIgnoreCase(FUNNEL_SOURCE_HOTELS);

        // Only proceed if experiment is enabled and device is mobile
        if (!isExperimentEnabled || !isMobileDevice || !isHotelFunnel) {
            return;
        }

        if (filterResponse == null || CollectionUtils.isEmpty(filterResponse.getFilterPills())) {
            return;
        }

        List<FilterPill> filterPills = filterResponse.getFilterPills();

        // Check if smart filter pill already exists
        FilterPill existingSmartPill = null;
        int existingIndex = -1;

        for (int i = 0; i < filterPills.size(); i++) {
            FilterPill pill = filterPills.get(i);
            if (Constants.SMART_FILTERS_PILL_ID.equals(pill.getId()) || SMART_FILTERS_PILL_ID.equals(pill.getId())) {
                existingSmartPill = filterPills.get(i);
                existingIndex = i;
                break;
            }
        }

        // If smart filter pill doesn't exist, create it
        if (existingSmartPill == null) {
            existingSmartPill = new FilterPill();
            existingSmartPill.setId(Constants.SMART_FILTERS_PILL_ID);
            existingSmartPill.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.SMART_FILTER_PILL_TEXT));
        } else {
            // Remove from current position
            filterPills.remove(existingIndex);
        }

        // Always set sequence to 3
        existingSmartPill.setSequence(3);
        try {
            existingSmartPill.setIcon(objectMapperUtil.getObjectFromJsonWithType(smartFilterPillIconURL,  new TypeReference<String>() {}, DependencyLayer.CLIENTGATEWAY));
            existingSmartPill.setSheetData(objectMapperUtil.getObjectFromJson(smartFilterSheetData, FilterPillSheetData.class, DependencyLayer.CLIENTGATEWAY));
        } catch (Exception e) {
            logger.error("Error in parsing Filter Pill Sheet Data ,:{}", e.getMessage(), e);
        }

        // Find the correct position for sequence 3 (after SORT and FILTERS)
        int targetPosition = findTargetPositionForPill(filterPills);

        // Insert at the target position
        filterPills.add(targetPosition, existingSmartPill);
    }

    /**
     * Finds the correct position to insert smart filter pill at sequence 3
     * Should be after SORT (sequence 1) and FILTERS (sequence 2)
     */
    private int findTargetPositionForPill(List<FilterPill> filterPills) {
        int position = 0;

        for (int i = 0; i < filterPills.size(); i++) {
            FilterPill pill = filterPills.get(i);

            // If current pill has sequence >= 3, insert before it
            if (pill.getSequence() >= 3) {
                return i;
            }

            // If it's FILTERS pill, insert after it
            if (ALL_FILTERS_FILTER_PILL_ID.equals(pill.getId())) {
                position = i + 1;
            }
            // If it's SORT pill and we haven't found FILTERS yet, position after SORT
            else if ("SORT".equals(pill.getId()) && position == 0) {
                position = i + 1;
            }
        }

        return position;
    }
}
