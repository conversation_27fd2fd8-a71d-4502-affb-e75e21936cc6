package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.response.hostcalling.HostCallingInitiateResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Abstract transformer class for converting Hotels-Entity-Service HostCalling responses to ClientGateway responses
 * This follows the same pattern as StaticDetailResponseTransformer
 */
public abstract class HostCallingResponseTransformer {

    private static final Logger logger = LoggerFactory.getLogger(HostCallingResponseTransformer.class);

    /**
     * Transforms HES HostCallingInitiateResponse to ClientGateway HostCallingInitiateResponse
     * Similar to convertStaticDetailResponse in StaticDetailResponseTransformer
     * 
     * @param hesResponse HES response
     * @param client Client type (PWA, ANDROID, IOS, DESKTOP)
     * @param requestId Request ID from original request for fallback scenarios
     * @return ClientGateway response
     */
    public HostCallingInitiateResponse convertHostCallingResponse(
            com.mmt.hotels.pojo.response.detail.HostCallingInitiateResponse hesResponse, 
            String client,
            String requestId) {
        
        if (hesResponse == null) {
            logger.warn("HES HostCalling response is null for requestId: {}", requestId);
            return createErrorResponse(requestId, "No response received from entity service");
        }

        try {
            HostCallingInitiateResponse cgResponse = new HostCallingInitiateResponse();

            // Map core response fields
            cgResponse.setStatus(hesResponse.getStatus());
            cgResponse.setRequestId(hesResponse.getRequestId());
            
            // Map host calling specific fields
            cgResponse.setChainName(hesResponse.getChainName());
            cgResponse.setAvailableNow(hesResponse.getAvailableNow());
            cgResponse.setStartTime(hesResponse.getStartTime());
            cgResponse.setEndTime(hesResponse.getEndTime());
            cgResponse.setMaskedNumber(hesResponse.getMaskedNumber());
            
            // Map error handling fields
            cgResponse.setResponseErrors(hesResponse.getResponseErrors());
            cgResponse.setErrorEntity(hesResponse.getErrorEntity());
            
            // Note: missedCallMessage will be set by the service layer after polyglot call
            
            logger.debug("Successfully transformed HES response to CG response for client: {}, requestId: {}", client, requestId);
            return cgResponse;

        } catch (Exception e) {
            logger.error("Error transforming HES response to CG response for client: {}, requestId: {}", client, requestId, e);
            return createErrorResponse(requestId, "Failed to process host calling response");
        }
    }

    /**
     * Creates an error response when transformation fails or HES response is null
     * 
     * @param requestId Request ID for tracking
     * @param errorMessage Error message to set
     * @return Error response
     */
    private HostCallingInitiateResponse createErrorResponse(String requestId, String errorMessage) {
        HostCallingInitiateResponse errorResponse = new HostCallingInitiateResponse();
        errorResponse.setStatus("error");
        errorResponse.setRequestId(requestId);
        errorResponse.setAvailableNow(false);
        
        logger.warn("Created error response for requestId: {} with message: {}", requestId, errorMessage);
        return errorResponse;
    }
} 