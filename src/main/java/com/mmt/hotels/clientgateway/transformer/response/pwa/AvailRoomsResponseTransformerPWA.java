package com.mmt.hotels.clientgateway.transformer.response.pwa;

import com.mmt.hotels.clientgateway.enums.DeviceConstant;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.transformer.response.AvailRoomsResponseTransformer;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class AvailRoomsResponseTransformerPWA extends AvailRoomsResponseTransformer {

    @Override
    protected void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap) {
        persuasionUtil.buildLoyaltyCashbackPersuasions(coupon,persuasionMap);
    }

    @Override
    public PersuasionResponse buildSpecialFareTagPersuasion(String corpAlias) {
        return null;
    }
}
