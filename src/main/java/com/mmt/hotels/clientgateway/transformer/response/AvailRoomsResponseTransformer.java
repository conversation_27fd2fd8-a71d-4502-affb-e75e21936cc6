package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mmt.hotels.clientgateway.businessobjects.AdditionalChargesBO;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.businessobjects.RequestInputBO;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.MyPartnerConfigConsul;
import com.mmt.hotels.clientgateway.enums.BNPLDisabledReason;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.DurationType;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.helpers.FareHoldHelper;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.AvailPriceCriteria;
import com.mmt.hotels.clientgateway.request.AvailRoomsRequest;
import com.mmt.hotels.clientgateway.request.AvailRoomsSearchCriteria;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.TotalPricingRequest;
import com.mmt.hotels.clientgateway.request.payment.SpecialRequestCategory;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.FlexiDetailBottomSheet;
import com.mmt.hotels.clientgateway.response.PricingDetails;
import com.mmt.hotels.clientgateway.response.SpecialRequestV2;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.availrooms.Alert;
import com.mmt.hotels.clientgateway.response.availrooms.CorpData;
import com.mmt.hotels.clientgateway.response.availrooms.DayUseInfo;
import com.mmt.hotels.clientgateway.response.availrooms.FeatureFlags;
import com.mmt.hotels.clientgateway.response.availrooms.FlexiCancellationDetails;
import com.mmt.hotels.clientgateway.response.availrooms.GstInfo;
import com.mmt.hotels.clientgateway.response.availrooms.HotelTag;
import com.mmt.hotels.clientgateway.response.availrooms.PanInfo;
import com.mmt.hotels.clientgateway.response.availrooms.PayLaterCard;
import com.mmt.hotels.clientgateway.response.availrooms.PayLaterSuccessCard;
import com.mmt.hotels.clientgateway.response.availrooms.TCClauseDetails;
import com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions;
import com.mmt.hotels.clientgateway.response.corporate.CorpRateTags;
import com.mmt.hotels.clientgateway.response.corporate.CorpRecentlyBooked;
import com.mmt.hotels.clientgateway.response.corporate.CorpTravellerInfo;
import com.mmt.hotels.clientgateway.response.corporate.RecentTravellerItem;
import com.mmt.hotels.clientgateway.response.moblanding.CardData;
import com.mmt.hotels.clientgateway.response.moblanding.CardInfo;
import com.mmt.hotels.clientgateway.response.moblanding.CardPayloadData;
import com.mmt.hotels.clientgateway.response.moblanding.SupportDetails;
import com.mmt.hotels.clientgateway.response.payLater.PayLaterEligibilityResponse;
import com.mmt.hotels.clientgateway.response.rooms.MmtExclusive;
import com.mmt.hotels.clientgateway.response.rooms.OccupancyDetail;
import com.mmt.hotels.clientgateway.response.rooms.RatePlan;
import com.mmt.hotels.clientgateway.response.rooms.RoomTariff;
import com.mmt.hotels.clientgateway.response.rooms.TripDetailsCard;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.response.wrapper.AppInstallStrip;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.BgGradient;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.util.Currency;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.enums.LuckyUserContext;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.BlackBenefits;
import com.mmt.hotels.model.persuasion.response.Persuasion;
import com.mmt.hotels.model.request.RoomCriterion;
import com.mmt.hotels.model.request.addon.LOB;
import com.mmt.hotels.model.response.FetchLocationsResponseBody;
import com.mmt.hotels.model.response.MpFareHoldStatus;
import com.mmt.hotels.model.response.corporate.CorpMetaInfo;
import com.mmt.hotels.model.response.corporate.CorpTravellerDetails;
import com.mmt.hotels.model.response.corporate.RecentTravellerDetails;
import com.mmt.hotels.model.response.emi.NoCostEmiDetails;
import com.mmt.hotels.model.response.persuasion.HotelTagType;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.FullPayment;
import com.mmt.hotels.model.response.staticdata.ChatbotInfo;
import com.mmt.hotels.model.response.staticdata.HighlightMustReadRule;
import com.mmt.hotels.model.response.staticdata.TooltipData;
import com.mmt.hotels.pojo.listing.personalization.BGLinearGradient;
import com.mmt.hotels.model.request.payment.TravelerDetail;
import com.mmt.hotels.util.Tuple;
import com.mmt.model.HotelsRoomInfoResponseEntity;
import com.mmt.model.PriceFooterDetail;
import com.mmt.model.RoomInfo;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.REVIEW_AVAIL_ROOMS;
import static com.mmt.hotels.clientgateway.enums.ExperimentKeys.SHOW_CONTEXTUAL_HOUSE_RULES;
import static com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer.sanitiseHeroCardData;
import static com.mmt.hotels.clientgateway.util.Utility.isGccOrKsa;
import static com.mmt.hotels.model.response.persuasion.HotelTagType.ONLY_FOR_TODAY;

@Component
public abstract class AvailRoomsResponseTransformer {


	@Value("${consul.enable}")
	private boolean consulFlag;
	@Autowired
	CommonConfigConsul commonConfigConsul;

	@Value("${thankyou.max.inclusions}")
    private int maxInclusionsThankyou;

	@Value("#{'${corp.segments}'.split(',')}")
	private Set<String> corpSegments;

	@Value("#{'${group.booking.review.page.cards}'.split(',')}")
	private List<String> groupBookingCardKeys;

	@Value("#{'${flights.booker.hydraSegments}'.split(',')}")
	private Set<String> flightsBookerHydraSegment;

	@Value("${trip.money.dark.logo.apps}")
	private String tripMoneyDarkLogoApps;

	@Value("${trip.money.dark.logo.web}")
	private String tripMoneyDarkLogoWeb;

	@Value("${bnpl.active.booking.threshold}")
	private int bnplActiveBookingThreshold;

	@Value("${trip.money.white.logo.apps}")
	private String tripMoneyWhiteLogoApps;

	@Value("${trip.money.white.logo.web}")
	private String tripMoneyWhiteLogoWeb;

	@Value("${trip.money.icon.apps}")
	private String tripMoneyIconApps;

	@Value("${trip.money.icon.web}")
	private String tripMoneyIconWeb;

	@Value("${tcs.info.review.url.app}")
	private String tcsInfoCardWebUrlApp;

	@Value("${tcs.info.review.url.dt}")
	private String tcsInfoCardWebUrlDT;

	@Value("${bank.coupon.generic.icon}")
	private String genericBankIcon;

	@Value("${app.install.deeplink}")
	private String appInstallDeeplink;

	@Value("${rtb.dayTime.persuasion.icon.url}")
	private String rtbDayTimePersuasionUrl;

	@Value("${rtb.nightTime.persuasion.icon.url}")
	private String rtbNightTimePersuasionUrl;


	@Value("${mypartner.gst.assured.icon.url}")
	private String iconUrlGSTAssured;

	@Value("${special.request.v2.bell.icon.url}")
	private String specialRequestV2BellIconUrl;

	@Value("${special.request.v2.banner.background.url}")
	private String specialRequestV2BannerBackgroundUrl;

	@Value("${bank.offer.default.icon}")
	private String bankOfferDefaultIcon;

	@Value("${bank.offer.saved.card.icon}")
	private String bankOfferSavedCardIcon;

	private Map<String, String> rtbTimeLineMap = new HashMap<>();

	@Autowired
	private CommonResponseTransformer commonResponseTransformer;

	@Autowired
	private PricingEngineHelper pricingEngineHelper;

	private Map<String,String> mealPlanMapPolyglot;
	private Map<String,Map<String,List<String>>> supplierToRateSegmentMapping;
	private List<String> translateEnabledSupplierCodes = new ArrayList<>();

	@Autowired
	PropertyManager propManager;

    @Autowired
    private Utility utility;

    @Autowired
    private DateUtil dateUtil;

    @Autowired
    protected PolyglotService polyglotService;

    @Autowired
    protected PersuasionUtil persuasionUtil;

    @Autowired
    MetricAspect metricAspect;
	@Autowired
	private FareHoldHelper fareHoldHelper;

	@Value("#{'${exclude.hotel.tag.types}'.split(',')}")
	private List<String> excludeHotelTagTypes;

    Map<String, String> rtbCardConfigs;

	@Value("${super.package.icon.url}")
	private String superPackageIconUrl;

    private boolean enablePanCardCheck;

    List<String> sameDayRoomNames;

	double payLaterCardLimit;

	@Value("${dot.icon.url}")
	private String dotIconUrl;

	@Value("${free.kids.inclusion.icon.url}")
	private String freeChildInclusionIcon;

	@Value("${los.position.avail.room}")
	private int losPositionAvailRoom;

	private Map<String, CardData> reviewPageCards;

	private static final Logger LOGGER = LoggerFactory.getLogger(AvailRoomsResponseTransformer.class);

	protected abstract void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap);

	protected abstract PersuasionResponse buildSpecialFareTagPersuasion(String corpAlias);

	@Autowired
	private ObjectMapperUtil objectMapperUtil;

	@Autowired
	MyPartnerConfigConsul myPartnerConfigConsul;

	@Autowired
	private MobConfigHelper mobConfigHelper;

	private MarkUpConfig markUpConfig;

	private ChatbotInfo chatbotConsulInfoV2;// used for Travelplex

	private boolean showSaleIcon;

	@PostConstruct
	public  void init(){

		if (consulFlag) {
			markUpConfig = myPartnerConfigConsul.getMarkUpConfig();
			mealPlanMapPolyglot = commonConfigConsul.getMealPlanMapPolyglot();
			rtbCardConfigs = commonConfigConsul.getRtbCardConfigs();
			enablePanCardCheck = commonConfigConsul.isEnablePanCardCheck();
			sameDayRoomNames = commonConfigConsul.getSameDayRoomNames();
			supplierToRateSegmentMapping = commonConfigConsul.getSupplierToRateSegmentMapping();
			translateEnabledSupplierCodes = commonConfigConsul.getTranslateEnabledSupplierCodes();
			payLaterCardLimit = commonConfigConsul.getPayLaterCardLimit();
			reviewPageCards = commonConfigConsul.getReviewPageCards();
			showSaleIcon = commonConfigConsul.isShowSaleIcon();
			chatbotConsulInfoV2 = commonConfigConsul.getChatbotInfoV2();
			try {
				rtbTimeLineMap = objectMapperUtil.getObjectFromJsonWithType(commonConfigConsul.getRtbTimeLine(), new TypeReference<Map<String, String>>() {
						}, DependencyLayer.CLIENTGATEWAY);
			} catch (JsonParseException e) {
				LOGGER.warn("Error fetching rtbTimeLine from commonConfig Consul");
			}
			LOGGER.debug("Fetching values from commonConfig consul");
		} else {
			CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
			mealPlanMapPolyglot = commonConfig.mealPlanMapPolyglot();
			commonConfig.addPropertyChangeListener("mealPlanMapPolyglot", evt -> mealPlanMapPolyglot = commonConfig.mealPlanMapPolyglot());
			rtbCardConfigs = commonConfig.rtbCardConfigs();
			commonConfig.addPropertyChangeListener("rtbCardConfigs", evt -> rtbCardConfigs = commonConfig.rtbCardConfigs());
			enablePanCardCheck = commonConfig.enablePanCardCheck();
			commonConfig.addPropertyChangeListener("enablePanCardCheck", evt -> enablePanCardCheck = commonConfig.enablePanCardCheck());
			sameDayRoomNames = commonConfig.sameDayRoomNames();
			commonConfig.addPropertyChangeListener("sameDayRoomNames", evt -> sameDayRoomNames = commonConfig.sameDayRoomNames());
			supplierToRateSegmentMapping = commonConfig.supplierToRateSegmentMapping();
			commonConfig.addPropertyChangeListener("supplierToRateSegmentMapping", event -> supplierToRateSegmentMapping = commonConfig.supplierToRateSegmentMapping());
			payLaterCardLimit = commonConfig.payLaterCardLimit();
			commonConfig.addPropertyChangeListener("payLaterCardLimit", evt -> payLaterCardLimit = commonConfig.payLaterCardLimit());
			reviewPageCards = commonConfig.reviewPageCards();
			commonConfig.addPropertyChangeListener("reviewPageCards", evt -> reviewPageCards = commonConfig.reviewPageCards());
		}
	}

	public AvailRoomsResponse convertAvailRoomsResponse(AvailRoomsRequest availRoomsRequest,RoomDetailsResponse roomDetailsResponse, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity,
														String siteDomain, String checkIn, String checkOut, String expData, String expVariantKeys, boolean allInclusions,
														String funnelSource, CommonModifierResponse commonModifierResponse, boolean showBnplCard, String deviceType) {
		AvailRoomsResponse availRoomsResponse = new AvailRoomsResponse();
		Map<String, String > expDataMap = utility.getExpDataMap(expData);
		boolean enableThemification = utility.isExperimentTrue(expDataMap, THEMIFICATION_ENABLED);
		boolean showContextualhouserules = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) ? utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), SHOW_CONTEXTUAL_HOUSE_RULES.getKey()) : false;
		boolean roomScarcityCallout = utility.isExperimentTrue(expDataMap, ROOM_SCARCITY_CALLOUT);
		boolean showReviewNRInclusion = utility.isExperimentTrue(expDataMap, SHOW_REVIEW_NR_INCLUSION);
		boolean isRTBCTrue = utility.isExperimentOn(expDataMap, EXP_RTBC);
		availRoomsResponse.setCurrency(roomDetailsResponse.getCurrency());
		boolean blackRevamp = utility.isExperimentTrue(expDataMap, ExperimentKeys.BLACK_REVAMP.getKey());
		boolean enableHostCalling = MapUtils.isNotEmpty(expDataMap) && utility.isExperimentTrue(expDataMap, ENABLE_HOST_CALLING);
		boolean isCallToBookV2Applicable = MapUtils.isNotEmpty(expDataMap) && utility.isExperimentValid(expDataMap, callToBook,4);
		boolean groupFunnelEnhancement = utility.isExperimentTrue(expDataMap, Constants.GROUP_FUNNEL_ENHANCEMENT_EXP);
		int maxCouponsToShowLimit = (MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(maxCouponCountExp))?Integer.parseInt(expDataMap.get(maxCouponCountExp)):0;
		boolean newPropertyOfferApplicable = commonModifierResponse!=null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) ? utility.isExperimentOn(commonModifierResponse.getExpDataMap(), ExperimentKeys.NEW_PROPERTY_OFFER.getKey()) : false;
		boolean ihCashbackSectionEnable =  utility.isExperimentTrue(expDataMap, ihCashbackSectionExp);
		long startTime = System.currentTimeMillis();
		try {
			Integer ap = StringUtils.isNotBlank(checkIn) ? dateUtil.getDaysDiff(LocalDate.now(), LocalDate.parse(checkIn)) : null;
			int los = StringUtils.isNotEmpty(checkIn) && StringUtils.isNotEmpty(checkOut) ? dateUtil.getDaysDiff(LocalDate.parse(checkIn), LocalDate.parse(checkOut)) : 1;
			HotelRates hotelRates = roomDetailsResponse.getHotelRates().get(0);
			if (roomDetailsResponse != null && CollectionUtils.isNotEmpty(roomDetailsResponse.getHotelRates())) {
				Map<String, Integer> roomBedCount = new HashMap<>();
				boolean showTransfersFeeTxt  = false;
				roomBedCount.put(Constants.SELLABLE_ROOM_TYPE, 0);
				roomBedCount.put(Constants.SELLABLE_BED_TYPE, 0);
				if (Objects.nonNull(myPartnerConfigConsul) && Objects.nonNull(hotelRates)) {
					availRoomsResponse.setMyPartnerMarkupConfig(pricingEngineHelper.buildMarkUpConfig(hotelRates.getMarkUpDetails(), myPartnerConfigConsul.getMarkUpConfig()));
				}
				availRoomsResponse.setFeatureFlags(getFeatureFlags(hotelRates, expDataMap, roomDetailsResponse.getCorpData() != null ? roomDetailsResponse.getCorpData() : null));
				if(roomDetailsResponse.getPermissions() !=null && roomDetailsResponse.getPermissions().getHotels() != null) {
					availRoomsResponse.setHotelPermissions(utility.buildHotelPermissions(roomDetailsResponse.getPermissions().getHotels()));
				}
				if (hotelRates.getGiftCardData() != null && hotelRates.getGiftCardData().getGcBottomSheetData() != null) {
					availRoomsResponse.setGiftCardData(utility.convertGiftCardsData(hotelRates.getGiftCardData(), true));
				}
				// Added pokus based mypartner exclusive deal tag
				boolean isHighValueCall = MapUtils.isNotEmpty(expDataMap) ? utility.isExperimentTrue(expDataMap, HVC) : false;
				int ancillaryVariant = MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(ExperimentKeys.ANCILLARY_DISPLAY_PRICE_VARIANT.getKey()) ? Integer.parseInt(expDataMap.get(ExperimentKeys.ANCILLARY_DISPLAY_PRICE_VARIANT.getKey())) : 0;
				boolean isPCCExpEnabled = utility.isExperimentOn(commonModifierResponse != null ? commonModifierResponse.getExpDataMap() : null, PROPERTY_CONFIG_CALLOUT_ENABLED);

				availRoomsResponse.setHotelInfo(buildHotelInfo(availRoomsRequest,hotelRates, checkIn, funnelSource, enableThemification, utility.isExperimentTrue(expDataMap, MYPARTNER_EXCLUSIVE_DEAL) && utility.isMyPartner(commonModifierResponse),groupFunnelEnhancement, isPCCExpEnabled));
				availRoomsResponse.setScarcityInfo(hotelRates.getScarcityInfo());
				availRoomsResponse.setUpsellOptions(buildUpsellOptions(hotelRates));
				availRoomsResponse.setCancellationTimeline(hotelRates.getRoomTypeDetails() != null ?
						commonResponseTransformer.buildCancellationTimeline(hotelRates.getRoomTypeDetails().getCancellationTimeline(), null) : null);
				availRoomsResponse.setCancellationPolicyTimeline(hotelRates.getRoomTypeDetails() != null ?
						commonResponseTransformer.buildCancellationPolicyTimeline(hotelRates.getRoomTypeDetails().getCancellationTimeline(), enableThemification, null) : null);
				availRoomsResponse.setPaymentPlan(commonResponseTransformer.buildPaymentPlan(hotelRates.getRoomTypeDetails().getPaymentPlan()));
				availRoomsResponse.setTcsWidgetInfo(hotelRates.getTcsWidgetInfo());
				availRoomsResponse.setTotalpricing(commonResponseTransformer.getTotalPricing(hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown(), hotelRates.getCountryCode(),
						availRoomsResponse.getFeatureFlags().getPayMode(), isCorp(availRoomsResponse, hotelRates), commonResponseTransformer.getCorporateSegmentId(hotelRates.getRoomTypeDetails()),
						expData, Utility.isGroupBookingFunnel(funnelSource), commonResponseTransformer.getPriceBreakupTextFromInsuranceAddon(hotelRates.getAddOns()), hotelRates.isCbrAvailable(), hotelRates.getMarkUpDetails(),roomDetailsResponse.isMetaTraffic(), hotelRates.getHotelBenefitInfo(), hotelRates.getCurrencyCode(), newPropertyOfferApplicable));
				String pahText = polyglotService.getTranslatedData(ConstantsTranslation.PAY_AT_HOTEL_POLICY_TEXT_GENERIC);
				if (availRoomsResponse.getFeatureFlags() != null
						&& availRoomsResponse.getFeatureFlags().getPayMode() != null
						&& availRoomsRequest != null && availRoomsRequest.getSearchCriteria() != null
				)
					Utility.updatePayAtHotelText(
							availRoomsResponse.getTotalpricing(),
							availRoomsResponse.getFeatureFlags().getPayMode(),
							pahText,
							availRoomsRequest.getSearchCriteria().getCountryCode()
					);
				updateHotelierCurrencyPricingDetails(availRoomsResponse.getTotalpricing(), hotelRates.getRoomTypeDetails(), hotelRates.getRoomTypeDetails().getTotalDisplayFare().getConversionFactor(), hotelRates.getCurrencyCode(), availRoomsResponse.getFeatureFlags().getPayMode());
				boolean showReviewOffersCategory = utility.showReviewOffersCategory(expDataMap);
				populateOtherCoupons(hotelRates.getRoomTypeDetails(), availRoomsResponse, CLIENT_DESKTOP.equalsIgnoreCase(deviceType), ihCashbackSectionEnable, hotelRates.getCabPrice(), ancillaryVariant, showReviewOffersCategory);
				if(ihCashbackSectionEnable) {
					populateBenefitDeals(hotelRates.getRoomTypeDetails(), availRoomsResponse, CLIENT_DESKTOP.equalsIgnoreCase(deviceType), roomDetailsResponse.getHydraSegments(), hotelRates.getCabPrice(), ancillaryVariant, showReviewOffersCategory); // forex benefit deal coupons
				}
				if (hotelRates.getFoodRatingData() != null) {
					availRoomsResponse.setFoodRatingData(hotelRates.getFoodRatingData());
				}
				if (MapUtils.isNotEmpty(hotelRates.getMealDetailsMap())) {
					availRoomsResponse.setMealsMap(commonResponseTransformer.transformMealsMap(hotelRates.getMealDetailsMap()));
				}
				if (hotelRates.isDigiLockerEnabled()) {
					availRoomsResponse.setDigilockerOptInInfo(buildDigilockerOptInInfo());
				}
				availRoomsResponse.getTotalpricing().setCurrency(hotelRates.getCurrencyCode());
				availRoomsResponse.getTotalpricing().setMaxCouponsToShow(maxCouponsToShowLimit);

				if (hotelRates.getRoomTypeDetails() != null
						&& hotelRates.getRoomTypeDetails().getTotalDisplayFare() != null
						&& StringUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getTotalDisplayFare().getCommonAdditionalReviewPersuasion())) {
					availRoomsResponse.getTotalpricing().setCommonCouponPersuasionInfo(buildCommonCouponPersuasionInfo(hotelRates.getRoomTypeDetails().getTotalDisplayFare().getCommonAdditionalReviewPersuasion()));
				}

				commonResponseTransformer.buildPaymentInfoTextForAlternateCurrency(availRoomsResponse.getTotalpricing(), hotelRates.getCountryCode(), availRoomsResponse.getFeatureFlags().getPayMode(), hotelRates.getCurrencyCode());
				BNPLVariant bnplVariant = hotelRates.getRoomTypeDetails().getTotalDisplayFare().getBnplVariant();
				if(bnplVariant == null || bnplVariant.equals(BNPLVariant.BNPL_NOT_APPLICABLE)) {
					if (commonModifierResponse != null && commonModifierResponse.getExpDataMap() != null && commonModifierResponse.getExpDataMap().get(Constants.EXP_BNPL_ZERO_VARIANT) != null && Constants.TRUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(EXP_BNPL_ZERO_VARIANT))) {
						bnplVariant = BNPLVariant.BNPL_AT_0;
					}
					else if (commonModifierResponse != null && commonModifierResponse.getExpDataMap() != null && commonModifierResponse.getExpDataMap().get(Constants.EXP_BNPL_NEW_VARIANT) != null && Constants.TRUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(Constants.EXP_BNPL_NEW_VARIANT))){
						bnplVariant = BNPLVariant.BNPL_AT_1;
					}
					else {
						bnplVariant = BNPLVariant.BNPL_NOT_APPLICABLE;
					}
				}

				if (roomDetailsResponse != null && roomDetailsResponse.getCorpTravellerDetails() != null) {
					availRoomsResponse.setCorpTravellerDetails(getCorpTravellerDetails(roomDetailsResponse.getCorpTravellerDetails()));
				}

				if (roomDetailsResponse != null && roomDetailsResponse.getTravellerDetails() != null) {
					availRoomsResponse.setTravellers(roomDetailsResponse.getTravellerDetails());
				}

				Map<String, Object> nonBnplAppliedCouponDetailsMap = commonResponseTransformer.fetchNonBnplAppliedCouponDetails(availRoomsResponse.getTotalpricing());
				boolean bnplDisabledDueToNonBnplCouponApplied = (boolean) nonBnplAppliedCouponDetailsMap.get(Constants.BNPL_DISABLED_DUE_TO_NON_BNPL_COUPON_APPLIED);
				String nonBnplCouponAppliedCode = (String) nonBnplAppliedCouponDetailsMap.get(Constants.NON_BNPL_COUPON_APPLIED_CODE);
				Integer activeBnplBookingCount = roomDetailsResponse.isUserLevelBnplDisabled() ? roomDetailsResponse.getActiveBnplBookingCount() : null;
				ExtendedUser extendedUser = null;

				if (Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser())) {
					extendedUser = commonModifierResponse.getExtendedUser();
				}
				BNPLDisabledReason bnplDisabledReason = null;
				if (Utility.isNotGCCMyPartnerMyBiz(commonResponseTransformer, extendedUser) && commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && Boolean.parseBoolean(commonModifierResponse.getExpDataMap().get(Constants.SHOW_DISABLED_BNPL_DETAILS))) {
					bnplDisabledReason = commonResponseTransformer.getBNPLDisabledReason(roomDetailsResponse.isUserLevelBnplDisabled(), bnplDisabledDueToNonBnplCouponApplied, false);
				}
				String cancellationPolicyType = getCancellationPolicyType(hotelRates.getRoomTypeDetails());
				int bnplAllowedCount = bnplActiveBookingThreshold;
				if (roomDetailsResponse.getBnplAllowedCount() != null && roomDetailsResponse.getBnplAllowedCount() > 0) {
					bnplAllowedCount = roomDetailsResponse.getBnplAllowedCount();
				}
				if (hotelRates.getIsBNPLAvailable() && !CANCELLATION_TYPE_NR.equals(cancellationPolicyType) && bnplDisabledReason != null && !BNPLVariant.BNPL_NOT_APPLICABLE.equals(bnplVariant)) {
					availRoomsResponse.setBnplDetails(commonResponseTransformer.buildBNPLDetailsForDisabledBnpl(bnplDisabledReason, nonBnplCouponAppliedCode, bnplVariant, activeBnplBookingCount, bnplAllowedCount));
				} else {
					availRoomsResponse.setBnplDetails(getBnplDetails(hotelRates.getRoomTypeDetails(), showBnplCard));
				}

				if(hotelRates!=null && hotelRates.getRoomTypeDetails()!=null && hotelRates.getRoomTypeDetails().getFullPayment()!=null) {
					availRoomsResponse.setFullPayment(utility.buildFullPayment(hotelRates.getRoomTypeDetails().getFullPayment()));
				}

				String propertyType = hotelRates.getPropertyTypeMerged()!=null?hotelRates.getPropertyTypeMerged():hotelRates.getPropertyType();
				availRoomsResponse.setRateplanlist(getRatePlanDetails(
						hotelRates.getRoomTypeDetails(), availRoomsResponse.getBnplDetails(), roomBedCount, ap,
						hotelRates.isUserGCCAndMmtExclusive(), expDataMap,hotelRates.isAltAcco(),
						LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelRates.getListingType()),
						propertyType, hotelRates));
				availRoomsResponse.setAddons(commonResponseTransformer.getAddons(hotelRates.getAddOns()));
				availRoomsResponse.setSpecialrequests(buildSpecialRequests(hotelRates.getSpecialRequestAvailable(), enableThemification));
				availRoomsResponse.setSpecialRequestV2(buildSpecialRequestsV2(hotelRates.getSpecialRequestAvailableV2()));
				availRoomsResponse.setShowSpecialRequestsV2(MapUtils.isNotEmpty(expDataMap) && utility.isExperimentOn(expDataMap, ExperimentKeys.SPECIAL_REQUEST.getKey()));
				availRoomsResponse.setGstInfo(getGstInfo(hotelRates.getGstin(), hotelRates.getCountryCode(), siteDomain, hotelRates.getGstStateCode(), hotelRates.getRoomTypeDetails()));
				availRoomsResponse.setTcsInfo(getTcsInfoCard(commonModifierResponse, siteDomain, hotelRates.getCountryCode(), hotelRates.isPanCardRequired(), hotelRates.getUserEligiblePayMode()));
				availRoomsResponse.setPanInfo(getPanInfo(hotelRates.isPanCardRequired(), hotelRates.isPnAvlbl()));
//				availRoomsResponse.setEmiDetails(commonResponseTransformer.getEmiAbridgeDetails(hotelRates.getEmiDetails()));
				availRoomsResponse.setDoubleBlackInfo(commonResponseTransformer.getDoubleBlackInfo(hotelRates.getDoubleBlackInfo()));
				availRoomsResponse.setMsmeCorpCard(hotelRates.getMsmeCorpCard());
				availRoomsResponse.setBlackInfo(commonResponseTransformer.buildBlackInfo(hotelRates.getBlackInfo()));
				availRoomsResponse.setTxnKey(roomDetailsResponse.getTxnKey());
				availRoomsResponse.setShowExternalChainMembership(roomDetailsResponse.isShowExternalChainMembership());
				availRoomsResponse.setSafetyPersuasionMap(commonResponseTransformer.buildSafetyPersuasionList(availRoomsResponse.getHotelInfo().getCategories()));
				availRoomsResponse.getTotalpricing().setAffiliateFeeDetails(commonResponseTransformer.buildAffiliateFeeDetails(hotelRates.getRoomTypeDetails().getTotalDisplayFare().getAffiliateFeeOptions()));
				buildPropertyRules(availRoomsResponse, hotelRates,showContextualhouserules);
				availRoomsResponse.setIntlRoamingInfo(hotelRates.getIntlRoamingInfo());
				if(roomDetailsResponse.getMyPartnerAgentDetails() != null && roomDetailsResponse.getMyPartnerAgentDetails().getPincode() != null){
					availRoomsResponse.setMyPartnerAgentDetails(roomDetailsResponse.getMyPartnerAgentDetails());
				}
				//flag to decide whether to show transfer fee text or not
				if (MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(Constants.TRANSFERS_FEE_TEXT_KEY)) {
					showTransfersFeeTxt = Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.TRANSFERS_FEE_TEXT_KEY));
				}
				buildAdditionalCharges(availRoomsResponse, hotelRates, showTransfersFeeTxt, expDataMap);
				//AlertTypeVariant - AlertMessage will be updated basis Pokus experiment key alert_type_variant.
				boolean alertTypeVariant = utility.isExperimentTrue(expDataMap, ALERT_TYPE_VARIANT);
				boolean pricerV2Exp = utility.isExperimentTrue(expDataMap, PRICER_V2);
				// In case of serving cachedPrice to clients, Will suppress all the alerts
				if(!hotelRates.isServedCachedPriceResponse()) {
					buildAlerts(availRoomsResponse, hotelRates, alertTypeVariant, isRTBCTrue, pricerV2Exp);
				}
				updateCampaingAlert(availRoomsResponse, hotelRates);
				if ("android".equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) || "ios".equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
					availRoomsResponse.getHotelInfo().setApplicableHotelCategoryData(commonResponseTransformer.buildHotelCategoryDataMap(availRoomsResponse.getHotelInfo().getCategories(), enableThemification));
				} else {
					availRoomsResponse.getHotelInfo().setApplicableHotelCategoryDataWeb(commonResponseTransformer.buildHotelCategoryDataWeb(availRoomsResponse.getHotelInfo().getCategories()));
				}
				availRoomsResponse.getHotelInfo().setShowCallToBook(hotelRates.isShowCallToBook());
				if(hotelRates.getPopularType()!=null) {
					availRoomsResponse.getHotelInfo().setPopularType(hotelRates.getPopularType());
				}
				if(hotelRates.isShowCallToBook()) {
					String device = (availRoomsRequest!=null && availRoomsRequest.getDeviceDetails()!=null && StringUtils.isNotEmpty(availRoomsRequest.getDeviceDetails().getBookingDevice()))?availRoomsRequest.getDeviceDetails().getBookingDevice(): EMPTY_STRING;
					if(isCallToBookV2Applicable){
						availRoomsResponse.getHotelInfo().setRequestCallbackData(utility.buildRequestToCallBackDataV2(PAGE_CONTEXT_REVIEW));
					}else{
						availRoomsResponse.getHotelInfo().setRequestCallbackData(utility.buildRequestToCallBackData(PAGE_CONTEXT_REVIEW, hotelRates.getName(), device));
					}
				}
				if(roomDetailsResponse.isHighDemandPersuasonEnable()){
					commonResponseTransformer.buildHighDemandHotelTag(availRoomsResponse);
				}else if (roomScarcityCallout) {
					commonResponseTransformer.buildScarcityHotelTag(hotelRates, availRoomsResponse);
				}
				updateInclusions(availRoomsResponse, allInclusions, expData, expDataMap);
				if(availRoomsRequest != null && availRoomsRequest.getRequestDetails() != null && StringUtils.isNotEmpty(availRoomsRequest.getRequestDetails().getIdContext()) &&
						availRoomsRequest.getRequestDetails().getIdContext().equalsIgnoreCase(CORP_ID_CONTEXT)) {
					utility.buildRoomNameCollapseDesc(availRoomsResponse);
				}
				utility.updateNRCancellationPolicyInclusion(showReviewNRInclusion, cancellationPolicyType, availRoomsResponse);
				buildCorpApprovalInfo(availRoomsResponse, hotelRates, utility.isTCSV2FlowEnabled(expDataMap));
				buildApprovingManagers(availRoomsResponse, hotelRates);
				if (availRoomsResponse != null && availRoomsResponse.getCorpApprovalInfo() != null && availRoomsResponse.getCorpApprovalInfo().isWalletQuickPayAllowed()
						&& hotelRates != null && hotelRates.getRoomTypeDetails() != null && hotelRates.getRoomTypeDetails().getTotalDisplayFare() != null
						&& hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown() != null) {
					availRoomsResponse.setMyBizQuickPayConfig(commonResponseTransformer.buildMyBizQuickPayConfig(hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown(), hotelRates.getCurrencyCode()));
				}
				int totalNumOfRooms = 0;
				if (availRoomsResponse != null && availRoomsResponse.getRateplanlist() != null) {
					totalNumOfRooms = availRoomsResponse.getRateplanlist().stream()
							.filter(Objects::nonNull) // Check for null ratePlan
							.filter(ratePlan -> ratePlan.getOccupancydetails() != null)
							.filter(ratePlan -> ratePlan.getOccupancydetails().getNumOfRooms() != null)
							.mapToInt(ratePlan -> ratePlan.getOccupancydetails().getNumOfRooms())
							.sum();
				}
				LinkedHashMap<String, String> commonModifierExpDataMap = new LinkedHashMap<>();
				if(commonModifierResponse != null){
					commonModifierExpDataMap = commonModifierResponse.getExpDataMap();
				}
				if(roomBedCount.get(SELLABLE_ROOM_TYPE) != null && roomBedCount.get(SELLABLE_ROOM_TYPE) > 0){
					int uniqueCheckinCheckoutCombinationsCount = getUniqueCheckinCheckoutCombinationsCount(hotelRates);
					int updatedTotalRooms = uniqueCheckinCheckoutCombinationsCount > 0 ? roomBedCount.get(SELLABLE_ROOM_TYPE) / uniqueCheckinCheckoutCombinationsCount : roomBedCount.get(SELLABLE_ROOM_TYPE);
					roomBedCount.put(SELLABLE_ROOM_TYPE, updatedTotalRooms);
				}
				Tuple<String, String> guestKeyValueTuple = utility.getGuestRoomKeyValue(roomBedCount, hotelRates.getPropertyLabel(), hotelRates.getCountryCode(), hotelRates.isHighSellingAltAcco(), hotelRates.isAltAcco(), availRoomsResponse.getHotelInfo().getBedInfoText(), availRoomsResponse.getHotelInfo().getListingType(), roomDetailsResponse.isServiceApartment(), totalNumOfRooms, commonModifierExpDataMap, true);
				availRoomsResponse.getHotelInfo().setGuestRoomKey(guestKeyValueTuple.getX());
				String countryCode = hotelRates.getCountryCode();
				boolean isOHSExpEnable = utility.isOHSExpEnable(
						hotelRates.getPropertyType(),
						commonModifierResponse != null ? commonModifierResponse.getExpDataMap() : null
				);
				boolean isIHAltAccoNodesExp = utility.isExperimentTrue(
						commonModifierResponse != null ? commonModifierResponse.getExpDataMap() : null,
						"IHAAOrch"
				);
				if (hotelRates.isAltAcco()) {
					availRoomsResponse.getHotelInfo().setGuestRoomValue(
							buildGuestRoomValueForAltAcco(isOHSExpEnable, hotelRates, countryCode, isIHAltAccoNodesExp, isPCCExpEnabled)
					);
				} else {
					availRoomsResponse.getHotelInfo().setGuestRoomValue(
							totalNumOfRooms > 1 && hotelRates.isEntireProperty() ? String.valueOf(totalNumOfRooms) + " " + guestKeyValueTuple.getY() : guestKeyValueTuple.getY()
					);
				}
				if (isRTBCTrue && hotelRates.isRequestToBook()) {
					availRoomsResponse.setRtbCard(buildRtbCard(utility.isRTBDayNightEnabled(expDataMap)));
				} else if (hotelRates.isRequestToBook() && !hotelRates.isRtbPreApproved()) {
					TripDetailsCard tripDetailsCard = new TripDetailsCard();
					tripDetailsCard.setActionText(polyglotService.getTranslatedData(ConstantsTranslation.RTB_TRIP_CARD_ACTION_TEXT));
					tripDetailsCard.setHeaderSubText(polyglotService.getTranslatedData(ConstantsTranslation.RTB_TRIP_CARD_HEADER_SUB_TEXT));
					tripDetailsCard.setHeaderText(polyglotService.getTranslatedData(ConstantsTranslation.RTB_TRIP_CARD_HEADER_TEXT));
					tripDetailsCard.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.RTB_TRIP_CARD_TITLE));
					List<String> texts = new ArrayList<>();
					texts.add(polyglotService.getTranslatedData(ConstantsTranslation.RTB_TRIP_CARD_TEXT1));
					texts.add(polyglotService.getTranslatedData(ConstantsTranslation.RTB_TRIP_CARD_TEXT2));
					tripDetailsCard.setTexts(texts);
					tripDetailsCard.setActionUrl(rtbCardConfigs.get("rtbTripDetailsActionUrl"));
					availRoomsResponse.setTripDetailsCard(tripDetailsCard);
				}
				availRoomsResponse.setHydraSegments(roomDetailsResponse.getHydraSegments());
				if (availRoomsResponse.getHotelInfo() != null) {
					availRoomsResponse.getHotelInfo().setFlexibleCheckinInfo(roomDetailsResponse.getFlexibleCheckinInfo());
				}

				boolean isTravelPlexEnabled = MapUtils.isNotEmpty(expDataMap) && utility.isExperimentOn(expDataMap, TRAVEL_PLEX_ENABLED);
				setChatBotInfo(availRoomsResponse, roomDetailsResponse.getChatbotInfo(), isTravelPlexEnabled);
				availRoomsResponse.setTcClauseDetails(buiildTCClauseDetails(hotelRates.getRoomTypeDetails()));
				if(roomDetailsResponse.getCorpData() != null){
					availRoomsResponse.setCorpData(buildCorpData(roomDetailsResponse.getCorpData().isCorpCapturePersonalBookingGstn()));
				}
				if(null!=availRoomsResponse.getFeatureFlags() && null!=availRoomsResponse.getFeatureFlags().getPayLaterCard() && availRoomsResponse.getFeatureFlags().getPayLaterCard()){
					availRoomsResponse.setPayLaterCard(buildPayLaterCard());
				}
				if(roomDetailsResponse.getHotelRates().get(0).getUserLoyaltyStatus()!=null)
					availRoomsResponse.setUserLoyaltyStatus(roomDetailsResponse.getHotelRates().get(0).getUserLoyaltyStatus());
				availRoomsResponse.setMyPartnerHero(roomDetailsResponse.getMyPartnerHero());
				if(roomDetailsResponse.getHotelRates().get(0) != null && roomDetailsResponse.getHotelRates().get(0).getRoomTypeDetails() !=null && roomDetailsResponse.getHotelRates().get(0).getRoomTypeDetails().getTotalDisplayFare().getMyPartnerCashbackDetails()!= null){
					availRoomsResponse.getTotalpricing().setMyPartnerCashbackDetails(roomDetailsResponse.getHotelRates().get(0).getRoomTypeDetails().getTotalDisplayFare().getMyPartnerCashbackDetails());
					PricingDetails effectivePriceDetails = commonResponseTransformer.buildEffectivePrice(hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown());
					if(effectivePriceDetails != null && availRoomsResponse.getTotalpricing() != null && availRoomsResponse.getTotalpricing().getDetails() != null) {
						availRoomsResponse.getTotalpricing().getDetails().add(effectivePriceDetails);
					}
				}
				if(roomDetailsResponse.getHotelRates().get(0) != null && roomDetailsResponse.getHotelRates().get(0).getRoomTypeDetails() != null && availRoomsResponse.getMyPartnerHero() != null) {
					sanitiseHeroCardData(roomDetailsResponse.getHotelRates().get(0).getRoomTypeDetails(), availRoomsResponse.getMyPartnerHero());
				}
				if(CollectionUtils.isNotEmpty(roomDetailsResponse.getPaxDetailsList())){
					availRoomsResponse.setPaxDetails(roomDetailsResponse.getPaxDetailsList());
				}
				availRoomsResponse.setCardData(buildReviewPageCardData(funnelSource, hotelRates,commonModifierResponse,roomDetailsResponse.getAffiliateId()));
				final MpFareHoldStatus mpFareHoldStatus = fareHoldHelper.getMpFareHoldStatus(hotelRates);
				if(mpFareHoldStatus!=null && mpFareHoldStatus.getExpiry()!=null) {
					availRoomsResponse.setBookNowDetails(fareHoldHelper.getBookNowDetails(mpFareHoldStatus, bnplDisabledDueToNonBnplCouponApplied, nonBnplCouponAppliedCode));
				}
                availRoomsResponse.setHotelPersuasions(buildHotelPersuasions(hotelRates, mpFareHoldStatus!=null && mpFareHoldStatus.getExpiry() != null, roomDetailsResponse.isAgentGSTAssured(), commonModifierResponse, roomDetailsResponse.getCorpAlias()));

                /** HTL-40907: Set Instant Fare Information  for negotiated rate hotels flow. Negotiated rates are the one-on-one rates that are directly negotiated between
                 * the corporate/organization and the hotel. This value is obtained from pricer to orchestrator.
                 */
                if (hotelRates.isNegotiatedRateFlag()) {
                    availRoomsResponse.setInstantFareInfo(getInstantFareInfo(hotelRates.getDetailDeeplinkUrl()));
                }
				String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
				//Adding Flyer/bus/train Exclusive rate persuasion in Review page response
				if (isTrainExclusiveRateAvailableInRatePlan(hotelRates.getRoomTypeDetails())) {
					Map<String, Persuasion> persuasionMap = persuasionUtil.buildTrainExclusiveRatesPersuasionForReviewPage(client);
					availRoomsResponse.getTotalpricing().setPricePersuasions(persuasionMap);
				}
				if (isBusExclusiveRateAvailableInRatePlan(hotelRates.getRoomTypeDetails())) {
					Map<String, Persuasion> persuasionMap = persuasionUtil.buildBusExclusiveRatesPersuasionForReviewPage(client);
					availRoomsResponse.getTotalpricing().setPricePersuasions(persuasionMap);
				}
				if (isFlyerExclusiveRateAvailableInRatePlan(hotelRates.getRoomTypeDetails())) {
					if (utility.isMyPartner(commonModifierResponse)) {
						persuasionUtil.buildHotelPersuasionOfExclusiveDealForReviewAndThankyou(availRoomsResponse.getHotelInfo());
					} else {
						Map<String, Persuasion> persuasionMap = persuasionUtil.buildFlyerExclusiveRatesPersuasionForReviewPage(client);
						availRoomsResponse.getTotalpricing().setPricePersuasions(persuasionMap);
					}
				}
				if(roomDetailsResponse.isVistaraDealAvailable()) {
					Map<String, Persuasion> persuasionMap = persuasionUtil.buildVistaraExclusiveRatesPersuasionForReviewPage(client);
					availRoomsResponse.getTotalpricing().setPricePersuasions(persuasionMap);
				}
				// create the bottom_price persuasion for homestay only for entire property
				Double displayAmount = utility.getTotalAmount(availRoomsResponse.getTotalpricing()); // null check
				int roomCount = availRoomsResponse.getHotelInfo()!=null ? availRoomsResponse.getHotelInfo().getBedroomCount(): 1;
				AtomicInteger totalAdults = new AtomicInteger();
				Optional.ofNullable(availRoomsRequest)
						.map(AvailRoomsRequest::getSearchCriteria)
						.map(AvailPriceCriteria::getRoomCriteria)
						.ifPresent(roomCriteriaList -> roomCriteriaList.forEach(roomCriterion ->
								Optional.ofNullable(roomCriterion.getRoomStayCandidates())
										.ifPresent(roomStayCandidates -> roomStayCandidates.forEach(roomStayCandidate ->
												totalAdults.addAndGet(Optional.ofNullable(roomStayCandidate.getAdultCount()).orElse(0))
										))
						));
				int noOfNightStays = 1;
				String searchedCurrency = HINDI_RUPEE;
				if(availRoomsRequest!=null && availRoomsRequest.getSearchCriteria()!=null && StringUtils.isNotEmpty(availRoomsRequest.getSearchCriteria().getCurrency())) {
					searchedCurrency = Currency.getCurrencyEnum(availRoomsRequest.getSearchCriteria().getCurrency()).getCurrencySymbol();
				}

				if (StringUtils.isNotEmpty(checkIn) && StringUtils.isNotEmpty(checkOut)) {
					try {
						SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
						noOfNightStays = dateUtil.getNoOfNights(formatter.parse(checkIn), formatter.parse(checkOut), funnelSource);
					} catch (Exception e) {
						LOGGER.warn("Error while parsing checkIn and checkOut dates");
					}
				}
				if(totalAdults!=null && MapUtils.isNotEmpty(expDataMap) && availRoomsResponse.getHotelInfo() != null && LISTING_TYPE_ENTIRE.equalsIgnoreCase(availRoomsResponse.getHotelInfo().getListingType())) {
					availRoomsResponse.getTotalpricing().setPricePersuasions(utility.buildHomestayPersuasion(hotelRates.isAltAcco(), expDataMap.get("SBPP"), roomCount, totalAdults.get(), displayAmount, noOfNightStays, searchedCurrency));
				}

				if (hotelRates.getBlackInfo() != null && (ANDROID.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) || DEVICE_IOS.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) ||
						(blackRevamp && (DEVICE_OS_PWA.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) || DEVICE_OS_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())))))) {
					Persuasion blackPersuasion = persuasionUtil.buildBlackPersuasionForReviewPage(hotelRates.getBlackInfo(), blackRevamp);
					if (blackPersuasion != null) {
						if (availRoomsResponse.getTotalpricing().getPricePersuasions() != null) {
							availRoomsResponse.getTotalpricing().getPricePersuasions().put(PLACEHOLDER_PRICE_BOTTOM, blackPersuasion);
						} else {
							availRoomsResponse.getTotalpricing().setPricePersuasions(new HashMap<>());
							availRoomsResponse.getTotalpricing().getPricePersuasions().put(PLACEHOLDER_PRICE_BOTTOM, blackPersuasion);
						}
					}
				}
// mmt4044, building hotel benefit persuassion in the pricepersuassion
				if (hotelRates.getHotelBenefitInfo() != null && (ANDROID.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) || DEVICE_IOS.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) ||
						 DEVICE_OS_PWA.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) || DEVICE_OS_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())))) {
					Persuasion benefitPersuasion = persuasionUtil.buildHotelBenefitPersuasionForReviewPage(hotelRates.getHotelBenefitInfo());
					if (benefitPersuasion != null) {
						if (availRoomsResponse.getTotalpricing().getPricePersuasions() != null) {
							availRoomsResponse.getTotalpricing().getPricePersuasions().put(PLACEHOLDER_PRICE_BOTTOM_M1, benefitPersuasion);
						} else {
							availRoomsResponse.getTotalpricing().setPricePersuasions(new HashMap<>());
							availRoomsResponse.getTotalpricing().getPricePersuasions().put(PLACEHOLDER_PRICE_BOTTOM_M1, benefitPersuasion);
						}
					}
				}

				if (hotelRates.getRoomTypeDetails() != null && hotelRates.getRoomTypeDetails().getTotalDisplayFare() != null
						&& hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown() != null)  {
					List<NoCostEmiDetails> noCostEmiDetailsList = hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getNoCostEmiDetailsList();
					setNoCostEmiDetails(noCostEmiDetailsList, hotelRates.getRoomTypeDetails().getCancellationTimeline(), availRoomsResponse);
				}

				//Black Upgrade Info
				if (blackRevamp && hotelRates.getBlackInfo() != null && hotelRates.getUpgradeInfo() != null) {
					BlackBenefits blackBenefits = getBlackBenefits(hotelRates);
					availRoomsResponse.setRateplansUpgrade(commonResponseTransformer.prepareUpgradeInfo(hotelRates, availRoomsResponse.getTotalpricing(), los, blackBenefits));
					availRoomsResponse.getRateplanlist().forEach(ratePlan -> ratePlan.setTagInfo(commonResponseTransformer.prepareTagInfo(blackBenefits)));
				}

				//[HTL-49652] Changes for sale persuasion on price top on review page
				buildSaleCampaignPersuasion(availRoomsResponse, hotelRates, client);

				String trafficType = "";
				if(isGccOrKsa() && TRAFFIC_TYPE_CMP.equalsIgnoreCase(trafficType)){
					Map<String, Persuasion> persuasionMap = persuasionUtil.buildGccMetaWalletCashbackPersuasionForReviewPage(client);
					availRoomsResponse.getTotalpricing().setPricePersuasions(persuasionMap);
				}
				if (enableThemification) {
					String payMode = availRoomsResponse != null && availRoomsResponse.getFeatureFlags() != null ? availRoomsResponse.getFeatureFlags().getPayMode() : EMPTY_STRING;
					if (availRoomsResponse.getBnplDetails() != null && availRoomsResponse.getBnplDetails().isBnplApplicable()) {
						availRoomsResponse.getBnplDetails().setPriceFooter(commonResponseTransformer.buildBNPLPriceFooter());
					}
					if (availRoomsResponse.getHotelInfo() != null && hotelRates.getRoomTypeDetails() != null && hotelRates.getRoomTypeDetails().getTotalDisplayFare() != null) {
						availRoomsResponse.getTotalpricing().setPriceFooter(commonResponseTransformer.buildPriceFooter(hotelRates.getRoomTypeDetails().getTotalDisplayFare().isTaxExcluded(),
								utility.getLengthOfStay(checkIn, checkOut), hotelRates.getRoomTypeDetails().getTotalDisplayFare().getTotalRoomCount()));
					}
					commonResponseTransformer.updatePriceFooterForPAHOnly(payMode, availRoomsResponse.getTotalpricing());
					boolean taxIncluded = hotelRates.getRoomTypeDetails() != null && hotelRates.getRoomTypeDetails().getTotalDisplayFare() != null ? hotelRates.getRoomTypeDetails().getTotalDisplayFare().isTaxExcluded() : true;
					availRoomsResponse.setAddOnInfo(commonResponseTransformer.getAddonInfo(taxIncluded, roomDetailsResponse.getFoodRating(),siteDomain, expDataMap, availRoomsResponse.getUpsellOptions(), hotelRates.getRoomTypeDetails().getSelectedAddOnState()));
				}
				//Setting quick checkout flag
				availRoomsResponse.setQuickCheckoutApplicable(roomDetailsResponse.isQuickCheckoutApplicable());

				if(roomDetailsResponse.getLuckyUserContext() != null){
					availRoomsResponse.setLuckyUserContext(LuckyUserContext.LUCKY.equals(roomDetailsResponse.getLuckyUserContext()) ? PRIVILEGED_USER : LuckyUserContext.LUCKY_UNLUCKY.equals(roomDetailsResponse.getLuckyUserContext()) ? CURSED_USER : UNFORTUNATE_USER);
				}
				// In case of serving cachedPrice to clients, Will provide flag priceChangeWindowHitReq = true, So that client hit "priceChangeWindow" endPoint
				HighDemand highDemand = new HighDemand();
				highDemand.setAvailRoomsV2HitRequired(hotelRates.isServedCachedPriceResponse());
				if (hotelRates.isServedCachedPriceResponse()) {
					if (CollectionUtils.isNotEmpty(hotelRates.getAlerts())) {
						//calculate the priceDiff between the cache and non-cache price
						Optional<AlertInfo> priceAlert = hotelRates.getAlerts().stream()
								.filter(al -> AlertInfo.AlertType.PRICE.equals(al.getMismatchType())).findAny();
						if (priceAlert.isPresent()) {
							Double amount = Utility.round(getAlertAmount(priceAlert.get()), 0);
							highDemand.setAmountDiff(amount.intValue() + SPACE + hotelRates.getCurrencyCode());
						}
					}
				}
				availRoomsResponse.setHighDemand(highDemand);

				String luckyUserContextWithExp = utility.logLuckyUserData(commonModifierResponse, availRoomsResponse.getLuckyUserContext(), "avail-rooms");
				availRoomsResponse.setLuckyUserContext(luckyUserContextWithExp);
				availRoomsResponse.setAckId(hotelRates.getAckId());
				if(isHighValueCall && availRoomsResponse.getHotelInfo()!=null){
					int totalTicketValue = 0;
					if(availRoomsResponse.getTotalpricing()!=null){
						totalTicketValue = utility.getTotalTicketValue(availRoomsResponse.getTotalpricing(),false);
					}
					boolean aboApplicable = MapUtils.isNotEmpty(expDataMap) ? utility.isExperimentTrue(expDataMap, ExperimentKeys.aboApplicable.name()) : false;
					availRoomsResponse.getHotelInfo().setSupportDetails(utility.buildSupportDetails(totalTicketValue,PAGE_CONTEXT_REVIEW,
							utility.getHighValueCallFunnelType(propertyType,funnelSource,availRoomsRequest!=null && availRoomsRequest.getSearchCriteria()!=null?
									availRoomsRequest.getSearchCriteria().getCountryCode():DOM_COUNTRY), aboApplicable));
					if(availRoomsResponse.getHotelInfo().getSupportDetails()!=null){
						String hotelName = StringUtils.isNotEmpty(hotelRates.getName()) ? hotelRates.getName() : EMPTY_STRING;
						availRoomsResponse.getHotelInfo().getSupportDetails().setFormUrl(buildFormUrlForReview(availRoomsRequest,availRoomsResponse.getHotelInfo().getSupportDetails(),propertyType,hotelName));
					}
				}
				if (hotelRates.getFlexiDetailBottomSheet() != null) {
					availRoomsResponse.setFlexiDetailBottomSheet(buildFlexiDetailBottomSheet(hotelRates.getFlexiDetailBottomSheet()));
				}
				if (hotelRates.getRoomTypeDetails().getSelectedAddOnState() != null) {
					availRoomsResponse.setSelectedAddOnState(com.mmt.hotels.clientgateway.request.AddOnState.valueOf(hotelRates.getRoomTypeDetails().getSelectedAddOnState().name()));
				}
				availRoomsResponse.setTrackingText(hotelRates.getTrackingText());
            }
			// HTL-39856 ExpressCheckoutDetail for myPartner to be sent to client if user is eligible to pay entire amount from wallet
			if (roomDetailsResponse != null && CollectionUtils.isNotEmpty(roomDetailsResponse.getHotelRates()) &&
                    roomDetailsResponse.getHotelRates().get(0).getExpressCheckoutDetail() != null &&
                    availRoomsResponse != null && availRoomsResponse.getTotalpricing() != null) {
                availRoomsResponse.getTotalpricing().setExpressCheckoutDetail(roomDetailsResponse.getHotelRates().get(0).getExpressCheckoutDetail());
            }
			availRoomsResponse.setExpVariantKeys(StringUtils.isNotBlank(expVariantKeys) ? expVariantKeys : null);

			if (roomDetailsResponse != null && CollectionUtils.isNotEmpty(roomDetailsResponse.getHotelRates()) &&
					roomDetailsResponse.getHotelRates().get(0).getSubscriptionCardData() != null) {
				availRoomsResponse.setSmeSubscriptionData(roomDetailsResponse.getHotelRates().get(0).getSubscriptionCardData());
			}
			if(availRoomsRequest != null && availRoomsRequest.getDeviceDetails() != null &&
			   (ANDROID.equalsIgnoreCase(availRoomsRequest.getDeviceDetails().getBookingDevice()) || 
			    DEVICE_IOS.equalsIgnoreCase(availRoomsRequest.getDeviceDetails().getBookingDevice()))){
				if(enableHostCalling && hotelRates.getHostInfoV2() != null && hotelRates.getHostInfoV2().getCallingInfo() != null){
					availRoomsResponse.setHostInfoV2(hotelRates.getHostInfoV2());
				}
			}
			availRoomsResponse.setAppInstallStrip(buildAppInstallStrip(availRoomsRequest));
			availRoomsResponse.setHotelCloudData(roomDetailsResponse.getHotelCloudData());
			availRoomsResponse.setCardsMap(buildCardsMap(roomDetailsResponse,availRoomsRequest,siteDomain,expDataMap,hotelRates.getCabsDeepLinkUrl()));
			Map<String, String> trackingMap = roomDetailsResponse.getTrackingMap();
			if(commonModifierResponse != null && (REGION_AE).equalsIgnoreCase(commonModifierResponse.getRegion()) && hotelRates != null) {
				String businessOwnerID = commonResponseTransformer.getEvarBasedOnCountryAndRegion(commonModifierResponse.getUserCountry(), hotelRates.getCountryCode());
				if (StringUtils.isNotEmpty(businessOwnerID)) {
					if (MapUtils.isEmpty(trackingMap)) {
						trackingMap = new HashMap<>();
					}
					if (trackingMap.containsKey(EVAR_126)) {
						trackingMap.put(EVAR_126, trackingMap.get(EVAR_126).toString() + "|" + businessOwnerID);
					} else {
						trackingMap.put(EVAR_126, businessOwnerID);
					}
				}
			}
			availRoomsResponse.setTrackingMap(commonResponseTransformer.buildTrackingMap(trackingMap));
			if(roomDetailsResponse.getHotelRates() != null && roomDetailsResponse.getHotelRates().get(0) != null) {
				MultiRoomStayDetails multiRoomStayDetails = new MultiRoomStayDetails();
				MultiRoomStayDetails.PopupData popupData = commonResponseTransformer.buildMultiRoomStayDetailsPopupData(hotelRates.getRoomTypeDetails());
				if(popupData != null) {
					multiRoomStayDetails.setPopupData(popupData);
					availRoomsResponse.setMultiRoomStayDetails(multiRoomStayDetails);
				}
			}
        } finally {
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_REVIEW_RESPONSE_PROCESS, REVIEW_AVAIL_ROOMS, System.currentTimeMillis() - startTime);
        }
        return availRoomsResponse;
    }

	private CommonCouponPersuasionInfo buildCommonCouponPersuasionInfo(String commonAdditionalReviewPersuasion) {
		CommonCouponPersuasionInfo commonCouponPersuasionInfo = new CommonCouponPersuasionInfo();
		commonCouponPersuasionInfo.setTitle(commonAdditionalReviewPersuasion);
		commonCouponPersuasionInfo.setIconUrl(bankOfferDefaultIcon);
		commonCouponPersuasionInfo.setBorderColor("#D8D8D8");

		BGLinearGradient bgGradient = new BGLinearGradient();
		bgGradient.setStart("#FFF4F4");
		bgGradient.setCenter("#FFFFFF");
		bgGradient.setEnd("#FFFFFF");
		bgGradient.setDirection("horizontal");
		commonCouponPersuasionInfo.setBgGradient(bgGradient);

		return commonCouponPersuasionInfo;
	}

	private int getUniqueCheckinCheckoutCombinationsCount(HotelRates hotelRates) {
		if (hotelRates == null || hotelRates.getRoomTypeDetails() == null ||
			hotelRates.getRoomTypeDetails().getRoomType() == null) {
			return 0;
		}

		Set<String> uniqueCombinations = new HashSet<>();
		// Iterate through all room types
		for (com.mmt.hotels.model.response.pricing.RoomType roomType : hotelRates.getRoomTypeDetails().getRoomType().values()) {
			if (roomType != null && roomType.getRatePlanList() != null) {
				// Iterate through all rate plans in this room type
				for (com.mmt.hotels.model.response.pricing.RatePlan ratePlan : roomType.getRatePlanList().values()) {
					if (ratePlan != null) {
						String checkin = ratePlan.getCheckin();
						String checkout = ratePlan.getCheckout();

						// Only consider valid (non-null and non-empty) dates
						if (StringUtils.isNotEmpty(checkin) && StringUtils.isNotEmpty(checkout)) {
							String combination = checkin + "|" + checkout;
							uniqueCombinations.add(combination);
						}
					}
				}
			}
		}
		return uniqueCombinations.size();
	}

    private String buildGuestRoomValueForAltAcco(boolean isOHSExpEnable, HotelRates hotelRates, String countryCode, boolean isIHAltAccoNodesExp, boolean isPCCExpEnabled) {
        if(isPCCExpEnabled && DOM_COUNTRY.equalsIgnoreCase(countryCode)) {
            String title = hotelRates.getStayTypeWithSizeBed();
            if ((!LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelRates.getListingType()) || ROOM_VALUE.equalsIgnoreCase(hotelRates.getSellableUnit())) && hotelRates.getRoomCount() > 1) {
                int roomCount = hotelRates.getRoomCount();
                title = roomCount + SPACE + title;
            }
            return title;
        } else {
            if (isOHSExpEnable && DOM_COUNTRY.equalsIgnoreCase(countryCode)) {
                return polyglotService.getTranslatedData(HOSTEL_TITLE);
            } else {
                String title;
                if (StringUtils.isNotEmpty(hotelRates.getStayTypeWithSizeBed()))
                    title = hotelRates.getStayTypeWithSizeBed();
                else
                    title = hotelRates.getStayTypeWithSize();

                // For countryCode india, if ListingType is Entire or room and RoomCount is greater than 1
                if (DOM_COUNTRY.equalsIgnoreCase(countryCode) && isIHAltAccoNodesExp
                        && hotelRates.getRoomCount() > 1 && hotelRates.isAltAcco()) {
                    if (LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelRates.getListingType()) && hotelRates.getRoomCount() > 1 && !title.endsWith(PLURAL_STRING)) {
                        title += PLURAL_STRING;
                    }
                    int roomCount = hotelRates.getRoomCount();
                    title = roomCount + SPACE + title;
                    return title;
                } else if (LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelRates.getListingType()) && isIHAltAccoNodesExp
                        && (hotelRates.getRoomCount() > 1) && !ROOM_VALUE.equalsIgnoreCase(hotelRates.getSellableUnit())) {
                    if (hotelRates.getRoomCount() > 1 && !title.endsWith(PLURAL_STRING)) {
                        title += PLURAL_STRING;
                    }
                    int roomCount = hotelRates.getRoomCount();
                    title = roomCount + SPACE + title;
                    return title;
                }
                return title;
            }
        }
    }



	private void setChatBotInfo(AvailRoomsResponse availRoomsResponse, ChatbotInfo chatbotInfo, boolean isTravelPlexEnabled) {
		if ( chatbotInfo == null || availRoomsResponse == null) {
			return;
		}
		availRoomsResponse.setChatbotInfo(chatbotInfo);

		if(!isTravelPlexEnabled) {
			return;
		}
		chatbotInfo.setLobMetaData(chatbotInfo.getLobMetaData());

		if (chatbotConsulInfoV2 == null) {
			return;
		}
		String iconUrl = chatbotConsulInfoV2.getIconUrl();
		if (StringUtils.isNotEmpty(iconUrl)) {
			chatbotInfo.setIconUrl(iconUrl);
		}
		TooltipData tooltipData = chatbotConsulInfoV2.getTooltipData();
		if (tooltipData != null) {
			chatbotInfo.setTooltipData(tooltipData);
		}
		String lottieIconUrl = chatbotConsulInfoV2.getLottieIconUrl();
		if(StringUtils.isNotEmpty(lottieIconUrl)) {
			chatbotInfo.setLottieIconUrl(lottieIconUrl);
		}
	}

	public Map<String, CardInfo> buildCardsMap(RoomDetailsResponse roomDetailsResponse, AvailRoomsRequest availRoomsRequest, String siteDomain, Map<String, String> expDataMap, String cabsDeepLinkUrl) {
		Map<String, CardInfo> cardsMap = new HashMap<>();
		try {
			//building lpg card
			Map<String, CardInfo> lpgCard = mobConfigHelper.convertCardDataToCardInfoMap(roomDetailsResponse.getCardDataMap());
			if (MapUtils.isNotEmpty(lpgCard)) {
				cardsMap.putAll(lpgCard);
			}
			if(availRoomsRequest != null) {
				boolean bookingDeviceDesktop = Utility.isBookingDeviceDesktop(availRoomsRequest.getDeviceDetails());
				String countryCode = availRoomsRequest.getSearchCriteria() != null ? availRoomsRequest.getSearchCriteria().getCountryCode() : "IN";
				String cityCode = availRoomsRequest.getSearchCriteria() != null ? availRoomsRequest.getSearchCriteria().getCityCode() : EMPTY_STRING;
				String idContext = availRoomsRequest.getRequestDetails() != null ? availRoomsRequest.getRequestDetails().getIdContext() : null;

				//building forex and cab card
				if (utility.isB2CFunnel(idContext) && utility.isIHFunnel(countryCode, siteDomain)
						&& (utility.isExperimentOn(expDataMap, ExperimentKeys.forexCard.getKey()) || utility.isExperimentOn(expDataMap, ExperimentKeys.cabCard.getKey()))) {
					Map<String, CardInfo> forexAndCabCard = buildForexAndCabCard(expDataMap, cityCode, cabsDeepLinkUrl, bookingDeviceDesktop);
					if (MapUtils.isNotEmpty(forexAndCabCard)) {
						cardsMap.putAll(forexAndCabCard);
					}
				}
			}
		}
		catch (Exception e) {
			LOGGER.error("Error while building lpg, forex and cab cards map", e);
		}
		return cardsMap;
	}

	public Map<String, CardInfo> buildForexAndCabCard(Map<String, String> expDataMap, String cityCode, String cabsDeepLink, boolean bookingDeviceDesktop) {
		Map<String, CardInfo> forexAndCabcardInfoMap = new HashMap<>();
		CardInfo forexAndCabCard = new CardInfo();
		CardPayloadData cardPayloadData = commonResponseTransformer.buildForexAndCabCardPayload(expDataMap, cityCode, cabsDeepLink, bookingDeviceDesktop, PAGE_CONTEXT_REVIEW);
		if(CollectionUtils.isEmpty(cardPayloadData.getGenericCardData())){
			return forexAndCabcardInfoMap;
		}
		forexAndCabCard.setCardPayload(cardPayloadData);
		forexAndCabCard.setTitleText(polyglotService.getTranslatedData(FOREX_CAB_CARD_TITLE_REVIEW_PAGE));
		forexAndCabCard.setSubText(polyglotService.getTranslatedData(FOREX_CAB_CARD_SUBTEXT_REVIEW_PAGE));
		forexAndCabCard.setTemplateId(FOREX_CAB_CARD_TEMPLATE_ID);
//		forexAndCabCard.setCardsData(commonResponseTransformer.buildForexAndCabCardsData(expDataMap,cityCode,cabsDeepLink));
		forexAndCabcardInfoMap.put(FOREX_CAB_CARD_ID,forexAndCabCard);
		return forexAndCabcardInfoMap;
	}

	private AppInstallStrip buildAppInstallStrip(AvailRoomsRequest availRoomsRequest){
		String idContext = availRoomsRequest != null && availRoomsRequest.getRequestDetails() != null ? availRoomsRequest.getRequestDetails().getIdContext() : null;
		String bookingDevice = availRoomsRequest != null && availRoomsRequest.getDeviceDetails() != null ? availRoomsRequest.getDeviceDetails().getBookingDevice() : null;
		if(StringUtils.isNotEmpty(idContext) && B2C.equalsIgnoreCase(idContext) && StringUtils.isNotEmpty(bookingDevice)
				&& DEVICE_OS_PWA.equalsIgnoreCase(bookingDevice) && !isGccOrKsa()){
			AppInstallStrip appInstallStrip = new AppInstallStrip();
			appInstallStrip.setText(polyglotService.getTranslatedData(ConstantsTranslation.APP_INSTALL_TEXT));
			appInstallStrip.setButtonText(polyglotService.getTranslatedData(ConstantsTranslation.APP_INSTALL_BUTTON_TEXT));
			appInstallStrip.setDeeplink(appInstallDeeplink);
			return appInstallStrip;
		}
		return null;
	}

	private FlexiDetailBottomSheet buildFlexiDetailBottomSheet(com.mmt.hotels.model.response.pricing.FlexiDetailBottomSheet flexiDetailBottomSheet) {
		FlexiDetailBottomSheet flexiDetailBottomSheetResponse = new FlexiDetailBottomSheet();
		flexiDetailBottomSheetResponse.setTitleText(flexiDetailBottomSheet.getTitleText());
		flexiDetailBottomSheetResponse.setSelected(utility.getSelected(flexiDetailBottomSheet.getSelected()));
		flexiDetailBottomSheetResponse.setUnselected(utility.getSelected(flexiDetailBottomSheet.getUnselected()));
		return flexiDetailBottomSheetResponse;
	}
	private BlackBenefits getBlackBenefits(HotelRates hotelRates) {
        BlackBenefits blackBenefits = null;
        if (hotelRates.getRoomTypeDetails() != null && MapUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getRoomType())) {
            RoomType roomType = hotelRates.getRoomTypeDetails().getRoomType().values().stream().findFirst().orElse(new RoomType());
            if (MapUtils.isNotEmpty(roomType.getRatePlanList())) {
                blackBenefits = roomType.getRatePlanList().values().stream().findFirst().orElse(new com.mmt.hotels.model.response.pricing.RatePlan()).getBlackBenefits();
            }
        }
        return blackBenefits;
    }

	private void buildSaleCampaignPersuasion(AvailRoomsResponse availRoomsResponse, HotelRates hotelRates, String client) {
		if (utility.isB2CFunnel() && showSaleIcon) {
			Optional.ofNullable(availRoomsResponse.getTotalpricing())
					.ifPresent(totalPricing -> {
						if (totalPricing.getPricePersuasions() == null) {
							totalPricing.setPricePersuasions(new HashMap<>());
						}
						Map<String, Persuasion> persuasionMap = new HashMap<>();
						if (hotelRates.getCampaignPojo() != null && StringUtils.isNotEmpty(hotelRates.getCampaignPojo().getIconUrl())) {
							persuasionMap = persuasionUtil.buildSaleCampaignPersuasionForReviewPage(hotelRates.getCampaignPojo().getIconUrl(), client);
						}
						totalPricing.getPricePersuasions().putAll(persuasionMap);
					});
		}
	}

	private void setNoCostEmiDetails(List<NoCostEmiDetails> noCostEmiDetailsList, CancellationTimeline cancellationTimeline, AvailRoomsResponse availRoomsResponse) {
		String couponCode = null;
		boolean noCostEmiApplicableOnCoupon = false;
		if (availRoomsResponse.getTotalpricing() != null && CollectionUtils.isNotEmpty(availRoomsResponse.getTotalpricing().getCoupons())) {
			for (Coupon coupon : availRoomsResponse.getTotalpricing().getCoupons()) {
				if (coupon.isAutoApplicable()) {
					couponCode = coupon.getCode();
					noCostEmiApplicableOnCoupon = coupon.isNoCostEmiApplicable();
					break;
				}
			}
		}

		commonResponseTransformer.buildNoCostEmiDetailAndUpdateFullPayment(noCostEmiDetailsList, availRoomsResponse.getTotalpricing(), availRoomsResponse.getFullPayment(), availRoomsResponse.getBnplDetails());
	}

	private RtbCard buildRtbCard(boolean rtbDayNightEnabled){
		RtbCard rtbCard = new RtbCard();
		rtbCard.setTitle(polyglotService.getTranslatedData(RTB_TITLE_TEXT));
		//TO DO : Remove subTitle after apps new version rollOut is ~90%
		rtbCard.setSubTitle(polyglotService.getTranslatedData(RTB_SUB_TITLE_TEXT));
		rtbCard.setBnplText(polyglotService.getTranslatedData(RTB_BNPL_TEXT));
		rtbCard.setCheckBoxText(polyglotService.getTranslatedData(RTB_CHECKBOX_TEXT));
		rtbCard.setCheckBoxErrorText(polyglotService.getTranslatedData(RTB_CHECKBOX_ERROR_TEXT));
		rtbCard.setRtbInfoList(Arrays.asList(polyglotService.getTranslatedData(RTB_DT_SUBTITLE_WITH_SEPARATOR).split(HASH_SEPARATOR)));
		try {
			if(!rtbDayNightEnabled){
				return rtbCard;
			}
			String durationTime = dateUtil.findDurationTimeSlot(rtbTimeLineMap);
			if(EARLY_MORNING_TIME.equalsIgnoreCase(durationTime)){
				rtbCard.setPersuasionIcon(rtbDayTimePersuasionUrl);
				rtbCard.setType(DurationType.DAY.name());
				rtbCard.setPersuasionText(polyglotService.getTranslatedData(EARLY_MORNING_TIME_TEXT));
			} else if(DAY_TIME.equalsIgnoreCase(durationTime)){
				rtbCard.setPersuasionIcon(rtbDayTimePersuasionUrl);
				rtbCard.setType(DurationType.DAY.name());
				rtbCard.setPersuasionText(polyglotService.getTranslatedData(DAY_TIME_TEXT));
			} else if(EARLY_NIGHT_TIME.equalsIgnoreCase(durationTime)){
				rtbCard.setPersuasionIcon(rtbDayTimePersuasionUrl);
				rtbCard.setType(DurationType.DAY.name());
				rtbCard.setPersuasionText(polyglotService.getTranslatedData(EARLY_NIGHT_TIME_TEXT));
			} else if(LATE_NIGHT_TIME.equalsIgnoreCase(durationTime)){
				rtbCard.setPersuasionIcon(rtbNightTimePersuasionUrl);
				rtbCard.setType(DurationType.NIGHT.name());
				rtbCard.setPersuasionText(polyglotService.getTranslatedData(LATE_NIGHT_TIME_TEXT));
			}
		} catch (ParseException e) {
			LOGGER.error("Error while building RTB DayTime persuasions: {}",e.getMessage());
			throw new RuntimeException(e);
		}
		return rtbCard;
	}

	// this function returns warningText coming from myPartner for bookNow
	private String getWarningTextForBookNowDetails(HotelRates hotelRates) {
		RoomType roomType = hotelRates.getRoomTypeDetails().getRoomType().values().stream().findFirst().orElse(null);
		if (roomType == null) {
			return EMPTY_STRING;
		}

		com.mmt.hotels.model.response.pricing.RatePlan ratePlan = roomType.getRatePlanList().values().stream().findFirst().orElse(null);
		if (ratePlan == null || ratePlan.getMpFareHoldStatus() == null) {
			return EMPTY_STRING;
		}

		return ratePlan.getMpFareHoldStatus().getHoldWarningText();
	}

	/**
	 * This method will return the TCS card data basis country is international and region is India
	 *
	 * @param commonModifierResponse
	 * @param domain
	 * @param countryCode
	 * @param panCardRequired
	 * @param userEligiblePayMode
	 * @return
	 */
	private TcsInfo getTcsInfoCard(CommonModifierResponse commonModifierResponse, String domain, String countryCode, boolean panCardRequired, PaymentMode userEligiblePayMode) {
		TcsInfo tcsInfo = null;
		String tcsApplicableInfoTextReview = polyglotService.getTranslatedData(ConstantsTranslation.TCS_APPLICABLE_TEXT_IH);
		if (StringUtils.isNotEmpty(tcsApplicableInfoTextReview) && utility.isIHMyPartnerOrB2CFunnel(commonModifierResponse, domain, countryCode)
				&& panCardRequired && userEligiblePayMode == PaymentMode.PAS) {
			tcsInfo = new TcsInfo();
			tcsInfo.setMessage(tcsApplicableInfoTextReview);
			String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
			tcsInfo.setWebUrl(StringUtils.isNotEmpty(client) && client.equalsIgnoreCase(CLIENT_DESKTOP) ? tcsInfoCardWebUrlDT : tcsInfoCardWebUrlApp);
			tcsInfo.setCta(polyglotService.getTranslatedData(ConstantsTranslation.TCS_APPLICABLE_CTA_IH));
		}
		return tcsInfo;
	}

	private CorpTravellerInfo getCorpTravellerDetails(CorpTravellerDetails travellerDetailsHES) {
		CorpTravellerInfo corpTravellerDetails = new CorpTravellerInfo();

		corpTravellerDetails.setAllowedToInviteUser(travellerDetailsHES.isAllowedToInviteUser());
		corpTravellerDetails.setEditTravellerFlowEnabled(travellerDetailsHES.isEditTravellerFlowEnabled());

		if (CollectionUtils.isNotEmpty(travellerDetailsHES.getRecentlyBookedTravellers())) {
			CorpRecentlyBooked corpRecentlyBooked = new CorpRecentlyBooked();
			corpRecentlyBooked.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.RECENTLY_ADDED_GUESTS_TITLE));
			List<RecentTravellerItem> travellers = getRecentTravellerItems(travellerDetailsHES);

			corpRecentlyBooked.setTravellers(travellers);
			corpTravellerDetails.setRecentlyBooked(corpRecentlyBooked);
		}

		return corpTravellerDetails;
	}

	private List<RecentTravellerItem> getRecentTravellerItems(CorpTravellerDetails travellerDetailsHES) {
		List<RecentTravellerItem> travellers = new ArrayList<>();

		if (CollectionUtils.isEmpty(travellerDetailsHES.getRecentlyBookedTravellers())) {
			return travellers;
		}

		for(RecentTravellerDetails travellerHES : travellerDetailsHES.getRecentlyBookedTravellers()) {
			RecentTravellerItem travellerItem = new RecentTravellerItem();

			travellerItem.setEmail(travellerHES.getEmail());
			travellerItem.setFirstName(travellerHES.getFirstName());
			travellerItem.setLastName(travellerHES.getLastName());
			travellerItem.setGender(travellerHES.getGender());
			travellerItem.setMobileNumber(travellerHES.getMobileNumber());

			travellers.add(travellerItem);
		}
		return travellers;
	}

	//This method checks if isExclusiveFlyerRateAvailable true or not in rateplan
    private boolean isFlyerExclusiveRateAvailableInRatePlan(RoomTypeDetails roomTypeDetails) {
        if (roomTypeDetails != null) {
            return roomTypeDetails.getRoomType().values().stream().findFirst().get().
                    getRatePlanList().values().stream().findFirst().get().isExclusiveFlyerRateAvailable();
        }
        return false;
    }

	//This method checks if isBusExclusiveRatePlan true or not in rateplan
	private boolean isBusExclusiveRateAvailableInRatePlan(RoomTypeDetails roomTypeDetails) {
		if (roomTypeDetails != null && roomTypeDetails.getRoomType() != null) {
			return roomTypeDetails.getRoomType().values().stream().findFirst().get().
					getRatePlanList().values().stream().findFirst().get().isBusExclusiveRateAvailable();
		}
		return false;
	}

	//This method checks if isTrainExclusiveRatePlan true or not in rateplan
	private boolean isTrainExclusiveRateAvailableInRatePlan(RoomTypeDetails roomTypeDetails) {
		if (roomTypeDetails != null && roomTypeDetails.getRoomType() != null) {
			return roomTypeDetails.getRoomType().values().stream().findFirst().get().
					getRatePlanList().values().stream().findFirst().get().isTrainExclusiveRateAvailable();
		}
		return false;
	}

    private Map<String, PersuasionResponse> buildHotelPersuasions(HotelRates hotelRates, boolean isMpFareHoldEligible, boolean isAgentGSTAssured, CommonModifierResponse commonModifierResponse, String corpAlias) {

        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();
        if (isMpFareHoldEligible) {
            int bookingValue = (int) (
                    hotelRates.getRoomTypeDetails().getRoomType().values().stream().findFirst().get().
                            getRatePlanList().values().stream().findFirst().get().getMpFareHoldStatus().getBookingAmount());

            //handles all null check to evaluate isMpFareHoldEligible
			Long expiry = hotelRates.getRoomTypeDetails().getRoomType().values().stream().findFirst().get().
					getRatePlanList().values().stream().findFirst().get().getMpFareHoldStatus().getExpiry();

			PersuasionResponse fareHoldPersuasion = new PersuasionResponse();
			fareHoldPersuasion.setTitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_TITLE),
					String.valueOf(bookingValue)));
			Hover hover = new Hover();
			if(expiry!=null) {
				hover.setTitleText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_TITLE),
						dateUtil.convertEpochToDateTime(expiry, dateUtil.DD_MMM_hh_mm_a)));
			}
			hover.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_SUB_TITLE));
			fareHoldPersuasion.setHover(hover);
			persuasionMap.put(Constants.BOOK_NOW_PERSUASION_KEY, fareHoldPersuasion);
		}
		//build cashback offer persuasion/Hero offer persuasion for review page myPartner funnel
		boolean isMyPartnerRequest = (commonModifierResponse!=null) && (commonModifierResponse.getExtendedUser()!=null) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
		if(isMyPartnerRequest) {
			if (null != hotelRates.getRoomTypeDetails() && null != hotelRates.getRoomTypeDetails().getTotalDisplayFare() && null !=  hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown() && null != hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo()) {
				BestCoupon coupon = hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo();
				//If manthan is sending rewardbonus in mmtVals node, coupon will be set as CTW->Cashback Amount(Cashback offer persuasion condition)
				LOGGER.debug("Manthan HybridDiscounts {}",coupon.getHybridDiscounts());
				boolean isCashbackAmtAvailable= org.apache.commons.collections.MapUtils.isNotEmpty(coupon.getHybridDiscounts()) && coupon.getHybridDiscounts().containsKey("CTW");
				if (StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage()) || isCashbackAmtAvailable) {
					buildLoyaltyCashbackPersuasions(coupon,persuasionMap);
				}
			}
			if(isAgentGSTAssured){
				PersuasionResponse gstAssuredPersuasion = new PersuasionResponse();
				gstAssuredPersuasion.setIconUrl(iconUrlGSTAssured);
				gstAssuredPersuasion.setStyle(new Style());
				gstAssuredPersuasion.getStyle().setIconHeight(29);
				gstAssuredPersuasion.getStyle().setIconWidth(134);
				gstAssuredPersuasion.setTooltip(commonConfigConsul.getMmtMyPartnerTooltip());
					persuasionMap.put(GST_ASSURED_PERSUASION_KEY, gstAssuredPersuasion);
			}
		}

		/* HTL-40907: Add special fare persuasion if negotiated rate hotel.
		 */
		if (hotelRates.isCorpSpecialFare()) {
			PersuasionResponse specialFarePersuasion = buildSpecialFareTagPersuasion(corpAlias);
			if (specialFarePersuasion != null) {
				persuasionMap.put(SPECIAL_FARE_TAG_TOP, specialFarePersuasion);
			}
		}
		return persuasionMap;
	}
	private PayLaterCard buildPayLaterCard() {
		PayLaterCard payLaterCard = new PayLaterCard();
		payLaterCard.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_TITLE));
		payLaterCard.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_SUBTITLE));
		payLaterCard.setCtaText(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_CTA_TEXT));
		if (Constants.CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
			payLaterCard.setLogo(tripMoneyWhiteLogoWeb);
			payLaterCard.setTitleIcon(tripMoneyIconWeb);
		}else{
			payLaterCard.setLogo(tripMoneyWhiteLogoApps);
			payLaterCard.setTitleIcon(tripMoneyIconApps);
		}
		return payLaterCard;
	}

	private List<CardData> buildReviewPageCardData(String funnelSource, HotelRates hotelRates, CommonModifierResponse commonModifierResponse, String affiliateId) {
		if (MapUtils.isNotEmpty(reviewPageCards)) {
			List<CardData> reviewCards = new ArrayList<>();
			addGroupBookingCards(funnelSource, reviewCards, hotelRates, commonModifierResponse);
			List<CardData> businessIdentificationCards = commonResponseTransformer.buildBusinessIdentificationCards(hotelRates, affiliateId);
			if(CollectionUtils.isNotEmpty(businessIdentificationCards)){
				reviewCards.addAll(businessIdentificationCards);
			}
			return reviewCards;
		}
		return null;
	}

	/**
	 * * This method is used to make Group booking Cards In this one POST_BOOKING_CARD is not build in the case of MyPartner and Group Funnel
	 * @param funnelSource
	 * @param reviewCards
	 * @param hotelRates
	 * @param commonModifierResponse
	 */
	private void addGroupBookingCards(String funnelSource, List<CardData> reviewCards, HotelRates hotelRates,CommonModifierResponse commonModifierResponse ){
		boolean isMyPartnerRequest = (commonModifierResponse!=null) && (commonModifierResponse.getExtendedUser()!=null) && utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
		if (utility.isGroupBookingFunnel(funnelSource) && hotelRates != null && hotelRates.isGroupBookingHotel()) {
			groupBookingCardKeys.forEach(groupBookingCardKey -> {
				if(reviewPageCards.get(groupBookingCardKey) != null && isMyPartnerRequest  && !Constants.POST_BOOKING_CARD.equalsIgnoreCase(groupBookingCardKey)){
						reviewCards.add(reviewPageCards.get(groupBookingCardKey));
					}
				else if(reviewPageCards.get(groupBookingCardKey) != null && !isMyPartnerRequest) {
					reviewCards.add(reviewPageCards.get(groupBookingCardKey));
				}
			});
		}
	}


	private TCClauseDetails buiildTCClauseDetails(RoomTypeDetails roomTypeDetails)
	{
		SupplierDetails supplierDetails = getSupplierDetails(roomTypeDetails);
		String segmentId = getSegmentId(roomTypeDetails);
		String supplierCode = supplierDetails.getSupplierCode();

		if(supplierToRateSegmentMapping!=null && supplierToRateSegmentMapping.get(Constants.TC_CLAUSE_KEY)!=null &&
				supplierToRateSegmentMapping.get(Constants.TC_CLAUSE_KEY).get(supplierCode)!=null
				&& (supplierToRateSegmentMapping.get(Constants.TC_CLAUSE_KEY).get(supplierCode).isEmpty()
				|| supplierToRateSegmentMapping.get(Constants.TC_CLAUSE_KEY).get(supplierCode).contains(segmentId))) {

			TCClauseDetails tcclauseDetails = new TCClauseDetails();
			tcclauseDetails.setClause(polyglotService.getTranslatedData(ConstantsTranslation.TC_CLAUSE_TEXT));
			tcclauseDetails.setHeading(polyglotService.getTranslatedData(ConstantsTranslation.TC_HEADING_TEXT));
			tcclauseDetails.setSubHeading(polyglotService.getTranslatedData(ConstantsTranslation.TC_SUBHEADING_TEXT));

			return tcclauseDetails;
		}

		return null;
	}

	private void updateCampaingAlert(AvailRoomsResponse availRoomsResponse, HotelRates hotelRates) {
		boolean setCampaignAlert = true;
		String campaignText = null;
		for(RatePlan ratePlan : availRoomsResponse.getRateplanlist()){
			String roomCode = ratePlan.getRoomCode();
			String ratePlanCode = ratePlan.getCode();
			if(null != ratePlan.getCancellationPolicy() && BookedCancellationPolicyType.FC == ratePlan.getCancellationPolicy().getType()) {
				campaignText = hotelRates.getRoomTypeDetails().getRoomType().get(roomCode).getRatePlanList().get(ratePlanCode).getCampaingText();
			}else{
				setCampaignAlert = false;
				break;
			}
		}
		if(setCampaignAlert && StringUtils.isNotBlank(campaignText)){
			Alert alert = new Alert();
			alert.setText(campaignText);
			alert.setType(AlertType.FREE_CANC_CAMPAIGN);
			availRoomsResponse.setCampaignAlert(alert);
		}
	}


	private boolean isCorp(AvailRoomsResponse availRoomsResponse, HotelRates hotelRates){
		return availRoomsResponse!=null && hotelRates.getRoomTypeDetails()!=null && hotelRates.getRoomTypeDetails().getTotalDisplayFare()!=null && hotelRates.getRoomTypeDetails().getTotalDisplayFare().getCorpMetaData()!=null;
	}

	private void updateInclusions(AvailRoomsResponse availRoomsResponse , boolean isAllInclusion, String expData, Map<String, String> expDataMap){
		if(availRoomsResponse != null && CollectionUtils.isNotEmpty(availRoomsResponse.getRateplanlist()) && !isAllInclusion) {
			for(RatePlan ratePlan : availRoomsResponse.getRateplanlist()) {
				BookedInclusion losInclusion = buildLosInclusion(ratePlan);
				List<BookedInclusion> inclusions = ratePlan.getInclusionsList();
				if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(inclusions) && inclusions.size() > maxInclusionsThankyou) {
					inclusions.sort(Comparator.comparing(BookedInclusion::getSegmentIdentifier, Comparator.nullsLast(Comparator.naturalOrder())));
					inclusions = inclusions.subList(0, maxInclusionsThankyou);
					ratePlan.setInclusionsList(inclusions);
				}
				if (StringUtils.isNotBlank(ratePlan.getMealCode())) {
					/* This will duplicate strings with mealPlan code : but added on Product's request (HTL-29300) */
					BookedInclusion bookedInclusion = new BookedInclusion();
					bookedInclusion.setText(utility.getRatePlanName(getMealPlanList(ratePlan.getMealCode()),ratePlan.getCancellationPolicy(),ratePlan.getSellableType(),availRoomsResponse.getHotelInfo().getListingType(),expData));
					bookedInclusion.setCode(bookedInclusion.getText());
					bookedInclusion.setIconType(IconType.DEFAULT);
					if (CollectionUtils.isEmpty(ratePlan.getInclusionsList())) {
						ratePlan.setInclusionsList(new ArrayList<>());
					}
					if (ratePlan.getInclusionsList()
							.stream()
							.filter(inclusion -> StringUtils.isNotEmpty(inclusion.getCode()))
							.anyMatch(inclusion -> inclusion.getCode().equalsIgnoreCase(bookedInclusion.getCode()))) {
						continue;
					}
					bookedInclusion.setIconUrl(utility.isReorderInclusions(expDataMap)?dotIconUrl:freeChildInclusionIcon);
					ratePlan.getInclusionsList().add(0,bookedInclusion);
				}
				if(losInclusion != null && inclusions.size() > losPositionAvailRoom)
					ratePlan.getInclusionsList().add(losPositionAvailRoom, losInclusion);
				else if(losInclusion != null)
					ratePlan.getInclusionsList().add(losInclusion);

				// bring extra adult child inclusion to first position in inclusion list
				Optional<BookedInclusion> extraAdultChildInclusion = ratePlan.getInclusionsList().stream()
						.filter(inclusion -> Constants.EXTRA_ADULT_CHILD.equals(inclusion.getCode()))
						.findFirst();
				extraAdultChildInclusion.ifPresent(inclusion -> {
					ratePlan.getInclusionsList().remove(inclusion);

					Optional.ofNullable(commonConfigConsul.getExtraAdultChildInclusionConfig())
							.ifPresent(config -> {
								String inclusionText = polyglotService.getTranslatedData(ConstantsTranslation.INCLUDED_FOR);

								if (StringUtils.isBlank(inclusionText)) {
									inclusionText = StringUtils.EMPTY;
								}
								inclusion.setText(inclusionText);
								inclusion.setIconUrl(config.getIconUrl());
								inclusion.setStyleClasses(config.getStyleClasses());
							});

					ratePlan.getInclusionsList().add(0, inclusion);
				});
			}
		}
	}

	private BookedInclusion buildLosInclusion(RatePlan ratePlan){
		BookedInclusion losInclusion = null;
		if(ratePlan != null){
			List<BookedInclusion> inclusions = new ArrayList<>();
			//Discount Inclusion
			if(ratePlan.getLosDiscountInclusion() != null){
				losInclusion = ratePlan.getLosDiscountInclusion();
				String translatedText = polyglotService.getTranslatedData(ConstantsTranslation.LONG_STAY_BENEFITS_HEADING);
				String text = StringUtils.isNotEmpty(translatedText) && StringUtils.isNotEmpty(losInclusion.getText()) ? (translatedText + SPACE + losInclusion.getText() + SPACE) : losInclusion.getText();
				losInclusion.setText(text);
				losInclusion.setCode(losInclusion.getText());
			}
			//Benefits Inclusions
			if(CollectionUtils.isNotEmpty(ratePlan.getInclusionsList())){
				for(BookedInclusion inclusion : ratePlan.getInclusionsList()){
					if(LONGSTAY.equalsIgnoreCase(inclusion.getInclusionCode())){
						if(losInclusion == null){
							losInclusion = inclusion;
							String translatedText = polyglotService.getTranslatedData(ConstantsTranslation.LONG_STAY_BENEFITS_HEADING);
							String text = StringUtils.isNotEmpty(translatedText) && StringUtils.isNotEmpty(losInclusion.getText()) ? (translatedText + SPACE + losInclusion.getText() + SPACE) : losInclusion.getText();
							losInclusion.setText(text);
							losInclusion.setCode(losInclusion.getText());
						}else{
							losInclusion.setText(StringUtils.isNotEmpty(losInclusion.getText()) ?  losInclusion.getText() + COMMA_SPACE + inclusion.getText() : inclusion.getText());
							losInclusion.setCode(StringUtils.isNotEmpty(losInclusion.getCode()) ?  losInclusion.getCode() + COMMA_SPACE + inclusion.getCode() : inclusion.getCode());
						}
						continue;
					}
					inclusions.add(inclusion);
				}
				ratePlan.setInclusionsList(inclusions);
			}
		}
		return losInclusion;
	}

	private void buildApprovingManagers(AvailRoomsResponse availRoomsResponse, HotelRates hotelRates) {

		if (availRoomsResponse!=null && hotelRates.getApprovingManagers()!=null && CollectionUtils.isNotEmpty(hotelRates.getApprovingManagers())){
			availRoomsResponse.setApprovingManagers(commonResponseTransformer.buildManagers(hotelRates.getApprovingManagers()));
		}
	}

	private void buildCorpApprovalInfo(AvailRoomsResponse availRoomsResponse, HotelRates hotelRates, boolean tcsV2FlowEnabled) {

		if (availRoomsResponse!=null && hotelRates.getRoomTypeDetails()!=null && hotelRates.getRoomTypeDetails().getTotalDisplayFare()!=null && hotelRates.getRoomTypeDetails().getTotalDisplayFare().getCorpMetaData()!=null){
			availRoomsResponse.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(hotelRates.getRoomTypeDetails().getTotalDisplayFare().getCorpMetaData(), tcsV2FlowEnabled));
		}
	}

	public List<Coupon> populateForexCoupons(RoomTypeDetails roomTypeDetails, boolean isDeviceDesktop, double cabPrice, int ancillaryVariant, boolean showReviewOfferCategory){

		List<Coupon> forexDealCoupons = new ArrayList<>();
		if (roomTypeDetails != null && roomTypeDetails.getTotalDisplayFare() != null &&
					CollectionUtils.isNotEmpty(roomTypeDetails.getTotalDisplayFare().getForexCoupons())){
				List<BestCoupon> forexCoupons = roomTypeDetails.getTotalDisplayFare().getForexCoupons();
				if (CollectionUtils.isNotEmpty(forexCoupons)) {
					forexCoupons.stream().forEach(coupon -> {
						Coupon forexCoupon = new Coupon();
						forexCoupon.setCode(coupon.getCouponCode());
						setBankOffer(forexCoupon,coupon.isBankOffer(), showReviewOfferCategory);
						forexCoupon.setDescription(coupon.getDescription());
						forexCoupon.setTncUrl(coupon.getTncUrl());
						forexCoupon.setPromoIcon(coupon.getPromoIconLink());
						forexCoupon.setBnplAllowed(coupon.getBnplAllowed() != null ? coupon.getBnplAllowed() : false);
						forexCoupon.setAutoApplicable(coupon.isAutoApplicable());
						forexCoupon.setDisabled(coupon.isDisabled());
						utility.forexCouponsNodeAddition(coupon, forexCoupon, isDeviceDesktop, cabPrice, ancillaryVariant);
						forexDealCoupons.add(forexCoupon);
					});
				}
			}
		return forexDealCoupons;
	}

	public void populateBenefitDeals(RoomTypeDetails roomTypeDetails, AvailRoomsResponse availRoomsResponse, boolean isDeviceDesktop, List<String> hydraSegments, double cabPrice, int ancillaryVariant, boolean showReviewOfferCategory){

		if(roomTypeDetails == null || availRoomsResponse == null)
			return;
		List<Coupon> forexDealCoupons = populateForexCoupons(roomTypeDetails, isDeviceDesktop, cabPrice, ancillaryVariant, showReviewOfferCategory);
		if(CollectionUtils.isNotEmpty(forexDealCoupons)){
			if (availRoomsResponse.getTotalpricing() != null) {
				BenefitDeals benefitDeals = new BenefitDeals();
				benefitDeals.setDealCoupons(forexDealCoupons);
				if(CollectionUtils.isNotEmpty(hydraSegments) && CollectionUtils.isNotEmpty(flightsBookerHydraSegment) &&
						utility.hasCommonElements(hydraSegments, flightsBookerHydraSegment)){
					benefitDeals.setTitle(polyglotService.getTranslatedData(FLIGHT_BOOKING_REASON));
					benefitDeals.setSubTitle(polyglotService.getTranslatedData(EXCLUSIVE_BENEFITS_TITLE));
				}else {
					benefitDeals.setTitle(polyglotService.getTranslatedData(Constants.MMT_EXCLUSIVE_BENEFITS));
					benefitDeals.setSubTitle(polyglotService.getTranslatedData(SPECIAL_BENEFITS_TITLE));
				}

				benefitDeals.setBgLinearGradient(utility.buildBgLinearGradientforForex());
				ConfirmationPopup confirmationPopup = new ConfirmationPopup();
				confirmationPopup.setTitle(polyglotService.getTranslatedData(DEAL_TITLE));
				confirmationPopup.setSubtitle(polyglotService.getTranslatedData(FOREX_DEAL_POPUP_SUBTITLE));
				confirmationPopup.setPrimaryCtaText(CONTINUE);
				confirmationPopup.setSecondaryCtaText(CANCEL);
				benefitDeals.setConfirmationPopup(confirmationPopup);
                benefitDeals.setSubTextBgGradient(utility.buildSubTextBgGradientforForex());
				availRoomsResponse.getTotalpricing().setBenefitDeals(benefitDeals);
			}
		}
	}

	private void populateOtherCoupons(RoomTypeDetails roomTypeDetails, AvailRoomsResponse availRoomsResponse, boolean isDeviceDesktop, boolean ihCashbackSectionEnable, double cabPrice, int ancillaryVariant, boolean showReviewOfferCategory) {
		String payMode = getRatePlanPayMode(roomTypeDetails);
		if (availRoomsResponse.getTotalpricing() != null) {
			if (roomTypeDetails.getTotalDisplayFare() != null &&
					MapUtils.isNotEmpty(roomTypeDetails.getTotalDisplayFare().getOtherCouponByPaymode()) &&
					roomTypeDetails.getTotalDisplayFare().getOtherCouponByPaymode().containsKey(payMode)) {
				List<BestCoupon> otherCoupons = roomTypeDetails.getTotalDisplayFare().getOtherCouponByPaymode().get(payMode);
				if (CollectionUtils.isNotEmpty(otherCoupons)) {
					otherCoupons.stream().forEach(coupon -> {
						if(coupon.getForexCouponDetails() == null || !ihCashbackSectionEnable) {
							Coupon otherCoupon = new Coupon();
							otherCoupon.setCode(coupon.getCouponCode());
							setBankOffer(otherCoupon,coupon.isBankOffer(), showReviewOfferCategory);
							otherCoupon.setCouponAmount(coupon.getDiscountAmount() != null ? coupon.getDiscountAmount() : 0.0);
							otherCoupon.setDescription(coupon.getDescription());
							otherCoupon.setBnplAllowed(coupon.getBnplAllowed() != null ? coupon.getBnplAllowed() : false);
							otherCoupon.setTncUrl(coupon.getTncUrl());
							otherCoupon.setAutoApplicable(coupon.isAutoApplicable());
                            otherCoupon.setNoCostEmiApplicable(coupon.isNoCostEmiApplicable());
							otherCoupon.setAdditionalReviewPersuasion(coupon.getAdditionalReviewPersuasion());
							if (StringUtils.isNotEmpty(coupon.getSavedCardPersuasion())) {
								otherCoupon.setSavedCardPersuasion(buildSavedCardPersuasion(coupon.getSavedCardPersuasion()));
							}
                            otherCoupon.setDisabled(coupon.isDisabled());
							otherCoupon.setPromoIcon(StringUtils.isNotEmpty(coupon.getPromoIconLink()) ? coupon.getPromoIconLink() : genericBankIcon);
							if (StringUtils.isNotEmpty(coupon.getBankName())) otherCoupon.setBankName(coupon.getBankName());
							if (coupon.isRecommendedCard()) {
								otherCoupon.setCouponPersuasions(persuasionUtil.buildCouponPersuasionsForReviewPage(polyglotService.getTranslatedData(REVIEW_COUPON_PERSUASION_TITLE)));
							}
							if(coupon.getForexCouponDetails() != null){
								utility.forexCouponsNodeAddition(coupon, otherCoupon, isDeviceDesktop,cabPrice, ancillaryVariant);
							}
							if (CollectionUtils.isEmpty(availRoomsResponse.getTotalpricing().getCoupons()))
								availRoomsResponse.getTotalpricing().setCoupons(new ArrayList<Coupon>());
							availRoomsResponse.getTotalpricing().getCoupons().add(otherCoupon);
						}
					});
				}
				if (CollectionUtils.isNotEmpty(availRoomsResponse.getTotalpricing().getCoupons())) {
					availRoomsResponse.getTotalpricing().setNoCouponText(null);
				}
			}
    	}
    }

	private SavedCardPersuasion buildSavedCardPersuasion(String savedCardPersuasion) {
		SavedCardPersuasion persuasion = new SavedCardPersuasion();
		persuasion.setPersuasion(savedCardPersuasion);
		persuasion.setIconUrl(bankOfferSavedCardIcon);
		return persuasion;
	}

	private HotelResult buildHotelInfo(AvailRoomsRequest availRoomsRequest,HotelRates hotelRates, String checkIn, String funnelSource, boolean enableThemification, final boolean isMyPartner,boolean groupFunnelEnhancement, boolean isPCCExpEnabled) {
		HotelResult hotelInfo = new HotelResult();
		hotelInfo.setHotelIcon(hotelRates.getHotelIcon());
		hotelInfo.setAltAcco(hotelRates.isAltAcco());
		hotelInfo.setName(hotelRates.getName());
		hotelInfo.setMaskedPropertyName(hotelRates.isMaskedPropertyName());
		hotelInfo.setStarRating(hotelRates.getStarRating());
		hotelInfo.setStarRatingType(hotelRates.getStarRatingType());
		hotelInfo.setHighSellingAltAcco(hotelRates.isHighSellingAltAcco());
		hotelInfo.setPropertyType(hotelRates.getPropertyTypeMerged()!=null?hotelRates.getPropertyTypeMerged():hotelRates.getPropertyType());
		if(!hotelRates.isHighSellingAltAcco()) {
			hotelInfo.setPropertyLabel(hotelRates.getPropertyLabel());
		}
		hotelInfo.setLat(hotelRates.getLat());
		hotelInfo.setLng(hotelRates.getLng());
		hotelInfo.setCityName(hotelRates.getCityName());

		AtomicInteger roomCount = new AtomicInteger();
		AtomicBoolean setRoomCount = new AtomicBoolean(true);

		Optional.ofNullable(availRoomsRequest)
				.map(AvailRoomsRequest::getSearchCriteria)
				.map(AvailPriceCriteria::getRoomCriteria)
				.ifPresent(roomCriteriaList -> roomCriteriaList.forEach(roomCriterion -> {
					String roomCodeForCount = roomCriterion.getRoomCode();
					if (StringUtils.isNotEmpty(roomCodeForCount) && hotelRates.getRoomInfo() != null && hotelRates.getRoomInfo().containsKey(roomCodeForCount)) {
						if(StringUtils.isNotEmpty(roomCodeForCount) && hotelRates.getRoomInfo()!=null && hotelRates.getRoomInfo().containsKey(roomCodeForCount) && hotelRates.getRoomInfo().get(roomCodeForCount).getBedRoomCount()!=null) {
							String bedRoomCountStr = hotelRates.getRoomInfo().get(roomCodeForCount).getBedRoomCount();
							if("0".equalsIgnoreCase(bedRoomCountStr)){
								setRoomCount.set(false);
							}
							if (StringUtils.isNotEmpty(bedRoomCountStr)) {
								int roomOfSameType = roomCriterion.getRoomStayCandidates().size();
								roomCount.addAndGet(Integer.parseInt(bedRoomCountStr)*roomOfSameType);
							}
						}
					}
				}));
		if(setRoomCount!=null && setRoomCount.get() && roomCount!=null) {
			hotelInfo.setBedroomCount(roomCount.get());
		}
		//BedInfoText, It is added to summarize the bed types and count for property Layout
		Map<String, RoomInfo> roomInfoMap = hotelRates.getRoomInfo();
		List<RoomInfo> roomInfoList = MapUtils.isNotEmpty(roomInfoMap) ? roomInfoMap.values().stream()
				.filter(roomInfo -> StringUtils.isNotBlank(roomInfo.getBedInfoText())).collect(Collectors.toList()) : new ArrayList<>();
		if(CollectionUtils.isNotEmpty(roomInfoList) && roomInfoList.get(0).getBedRoomCount()!=null) {
			String s = (roomInfoList.get(0).getBedRoomCount().equals("1")) ? roomInfoList.get(0).getBedRoomCount() + " "+polyglotService.getTranslatedData(ConstantsTranslation.BEDROOM) : roomInfoList.get(0).getBedRoomCount() + " "+polyglotService.getTranslatedData(ConstantsTranslation.BEDROOMS);
			hotelInfo.setBedInfoText(s + " | " + roomInfoList.get(0).getBedInfoText());
		}
		else if(CollectionUtils.isNotEmpty(roomInfoList)){
			hotelInfo.setBedInfoText(roomInfoList.get(0).getBedInfoText());
		}
		hotelInfo.setCountryName(hotelRates.getCountryName());
		hotelInfo.setAddress(getAddress(hotelRates.getAddressLines()));
		hotelInfo.setCheckinTime(StringUtils.isNotEmpty(hotelRates.getCheckInTimeRange())?hotelRates.getCheckInTimeRange():hotelRates.getCheckInTime());
		hotelInfo.setCheckoutTime(StringUtils.isNotEmpty(hotelRates.getCheckOutTimeRange())?hotelRates.getCheckOutTimeRange():hotelRates.getCheckOutTime());
		hotelInfo.setShowTimeRangeUi(hotelRates.isShowTimeRangeUi());
		hotelInfo.setListingType(hotelRates.getListingType());
		hotelInfo.setHotelId(hotelRates.getId());
		hotelInfo.setShowCallToBook(hotelRates.isShowCallToBook());

		if(isPCCExpEnabled && hotelRates.isAltAcco() && hotelRates.getRoomCount()>1 && DOM_COUNTRY.equalsIgnoreCase(hotelRates.getCountryCode()) && LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelRates.getListingType()) && !ROOM_VALUE.equalsIgnoreCase(hotelRates.getSellableUnit())){
			String propertyCountText = hotelRates.getRoomCount() + SPACE + UNITS_OF;
			hotelInfo.setPropertyCountText(propertyCountText);
		}
		hotelInfo.setHotelCategories(utility.concatenateWithSeparator(PIPE, hotelRates.getCategories()));
		hotelInfo.setCategories(commonResponseTransformer.getHotelCategories(hotelRates.getCategories(), hotelRates.isAltAcco(), isMyPartner));
		commonResponseTransformer.updateHotelCategories(enableThemification, hotelInfo.getCategories(), hotelRates.getCategories());
		if (MapUtils.isNotEmpty(hotelRates.getCategoryDetails())) {
			hotelInfo.setCategoryDetails(hotelRates.getCategoryDetails());
		}
		hotelInfo.setHotelTags(buildHotelTags(hotelRates.getHotelTags(), enableThemification));
		String idContext = MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue());
		//Hidden Gem will have high priority than Value Stays [HTL-38253]
		if (!enableThemification && hotelRates.isBudgetHotel() && StringUtils.isNotBlank(idContext) && !Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext) && !(CollectionUtils.isNotEmpty(hotelRates.getCategories()) && hotelRates.getCategories().contains(HIDDEN_GEM))) {
			hotelInfo.setHotelTags(commonResponseTransformer.buildValueStaysHotelTag(Constants.VALUE_STAY_TAG_TITLE_REVIEW, hotelInfo.getHotelTags()));
		}
		hotelInfo.setMmtHotelCategory(hotelRates.getMmtHotelCategory());
		hotelInfo.setDayUseInfo(buildDayUseInfo(hotelRates));
		if (hotelInfo.getDayUseInfo()!=null) {
			hotelInfo.setCheckinTime(hotelInfo.getDayUseInfo().getCheckinDate());
			hotelInfo.setCheckoutTime(hotelInfo.getDayUseInfo().getCheckoutDate());
			hotelInfo.getDayUseInfo().setCheckinDate(checkIn);
			hotelInfo.getDayUseInfo().setCheckoutDate(checkIn);
		}
		if( hotelRates!= null && StringUtils.isNotEmpty(hotelRates.getIngoAltaccoTracking())){
			hotelInfo.setSupplierType(hotelRates.getIngoAltaccoTracking());
		}
		hotelInfo.setQuickBookSubTitle(hotelRates.getQuickBookSubtitle());
		hotelInfo.setEntireProperty(hotelRates.isEntireProperty());
		setGroupBookingParamsInHotelInfo(hotelRates, funnelSource, hotelInfo,groupFunnelEnhancement);
		if(StringUtils.isNotBlank(hotelRates.getBedInfoText()))
			hotelInfo.setBedInfoText(hotelRates.getBedInfoText());
		hotelInfo.setSpecialFare(hotelRates.isCorpSpecialFare());
		hotelInfo.setChildOccupancyMsg(hotelRates.getRoomTypeDetails() != null ? hotelRates.getRoomTypeDetails().getFreeChildText() : null);
		return hotelInfo;
	}

	private String buildFormUrlForReview(AvailRoomsRequest availRoomsRequest, SupportDetails supportDetails, String propertyType,String hotelName){
		String formUrl = null;
		try{
			if(availRoomsRequest!=null){
				String checkin = availRoomsRequest.getSearchCriteria() != null ? dateUtil.getDateFormatted(availRoomsRequest.getSearchCriteria().getCheckIn(),DateUtil.YYYY_MM_DD,DDMMYYYY) : EMPTY_STRING;
				String checkout = availRoomsRequest.getSearchCriteria() != null ? dateUtil.getDateFormatted(availRoomsRequest.getSearchCriteria().getCheckOut(),DateUtil.YYYY_MM_DD,DDMMYYYY) : EMPTY_STRING;
				String city = availRoomsRequest.getSearchCriteria()!=null ? availRoomsRequest.getSearchCriteria().getCityCode() : EMPTY_STRING;
				String country = availRoomsRequest.getSearchCriteria()!= null ? availRoomsRequest.getSearchCriteria().getCountryCode(): "IN";
				String locusId = availRoomsRequest.getSearchCriteria() != null ? availRoomsRequest.getSearchCriteria().getLocationId() : EMPTY_STRING;
				String locusType = availRoomsRequest.getSearchCriteria() != null ? availRoomsRequest.getSearchCriteria().getLocationType() : TYPE_CITY;
				String rsc = availRoomsRequest.getSearchCriteria() != null ? utility.buildRscValue(availRoomsRequest.getSearchCriteria().getRoomStayCandidates()):EMPTY_STRING;
				String _uCurrency = availRoomsRequest.getSearchCriteria() != null ? availRoomsRequest.getSearchCriteria().getCurrency() : DEFAULT_CUR_INR;
				String appVersion=availRoomsRequest.getDeviceDetails() != null ? availRoomsRequest.getDeviceDetails().getAppVersion() : EMPTY_STRING;
				String deviceId=availRoomsRequest.getDeviceDetails() != null ? availRoomsRequest.getDeviceDetails().getDeviceId() : EMPTY_STRING;
				String bookingDevice=availRoomsRequest.getDeviceDetails() != null ? availRoomsRequest.getDeviceDetails().getBookingDevice() : EMPTY_STRING;
				String deviceType=availRoomsRequest.getDeviceDetails() != null ? availRoomsRequest.getDeviceDetails().getDeviceType() : EMPTY_STRING;
				String visitorId=availRoomsRequest.getRequestDetails() != null ? availRoomsRequest.getRequestDetails().getVisitorId() : EMPTY_STRING;
				String  visitNumber= String.valueOf(availRoomsRequest.getRequestDetails() != null ? availRoomsRequest.getRequestDetails().getVisitNumber() : 1);
				String funnelSource=availRoomsRequest.getRequestDetails() != null ? availRoomsRequest.getRequestDetails().getFunnelSource() : EMPTY_STRING;
				String idContext=availRoomsRequest.getRequestDetails() != null ? availRoomsRequest.getRequestDetails().getIdContext() : EMPTY_STRING;
				String funnelName = availRoomsRequest.getRequestDetails() != null ? availRoomsRequest.getRequestDetails().getFunnelSource() : EMPTY_STRING;
				propertyType = StringUtils.isNotEmpty(propertyType) ? propertyType : HOTEL;
				hotelName = hotelName.replace(' ', '+');
				formUrl = MessageFormat.format(supportDetails.getFormUrl(),checkin, checkout,city, country,locusId,locusType,rsc,_uCurrency ,appVersion,deviceId,bookingDevice,deviceType,visitorId,visitNumber,funnelSource,idContext,funnelName,propertyType,hotelName);
			}
		}catch(Exception e){
			LOGGER.error("Exception occured while preparing formUrl for supportDetail for review page : "+e.getMessage(),e);
		}
		return formUrl;
	}

	private void setGroupBookingParamsInHotelInfo(HotelRates hotelRates, String funnelSource, HotelResult hotelInfo, boolean groupFunnelEnhancement) {
		if (utility.isGroupBookingFunnel(funnelSource)) {
			hotelInfo.setGroupBookingHotel(hotelRates.isGroupBookingHotel());
			if(!groupFunnelEnhancement)
				hotelInfo.setGroupBookingPrice(hotelRates.isGroupBookingPrice());
			else
				hotelInfo.setGroupBookingPrice(false); // Explicit marking this as false, In case of experiment is groupFunnelEnhancement enabled
			hotelInfo.setMaskedPrice(hotelRates.isMaskedPrice());
			hotelInfo.setRoomText(buildRoomText(hotelRates.getRoomTypeDetails()));
		}
	}

	private String buildRoomText(RoomTypeDetails roomTypeDetails) {
		if (roomTypeDetails != null && roomTypeDetails.getTotalDisplayFare() != null && MapUtils.isNotEmpty(roomTypeDetails.getRoomType()) &&
				roomTypeDetails.getRoomType().entrySet().stream().anyMatch((roomTypeDetailsEntry -> roomTypeDetailsEntry.getValue().isBaseRoom()))) {
			String baseRoomName = roomTypeDetails.getRoomType().entrySet().stream().filter(roomTypeDetailsEntry -> roomTypeDetailsEntry.getValue().isBaseRoom()).findFirst().get().getValue().getRoomTypeName();
			return roomTypeDetails.getTotalDisplayFare().getTotalRoomCount() + SPACE_X_SPACE + baseRoomName;
		}
		return null;
	}

	private Map<String, HotelTag> buildHotelTags(Map<String, com.mmt.hotels.model.response.persuasion.HotelTag> hotelTags, boolean enableThemification) {
		if (MapUtils.isEmpty(hotelTags)) {
			return null;
		}

		Map<String, HotelTag> hotelTagMap = new HashMap<>();

		for (Map.Entry<String, com.mmt.hotels.model.response.persuasion.HotelTag> hotelTag : hotelTags.entrySet()) {
			hotelTagMap.put(hotelTag.getKey(), buildHotelTag(hotelTag.getKey(), hotelTag.getValue(), enableThemification));
		}

		return hotelTagMap;
	}

	private HotelTag buildHotelTag(String placeholder,com.mmt.hotels.model.response.persuasion.HotelTag responseHotelTag, boolean enableThemification) {
		if (responseHotelTag == null || (enableThemification && responseHotelTag.type != null && excludeHotelTagTypes.contains(responseHotelTag.type.getValue()))) {
			return null;
		}
		HotelTag hotelTag = new HotelTag();
		hotelTag.setTitle(responseHotelTag.title);
		if (!enableThemification && !PLACEHOLDER_PC_BELOW_HOTEL.equalsIgnoreCase(placeholder)) {
			hotelTag.setIcon(responseHotelTag.icon);
		}

		hotelTag.setIconUrl(responseHotelTag.iconUrl);

		if(responseHotelTag.background != null){
			hotelTag.setBackground(responseHotelTag.background);
		} else {
			hotelTag.setBackground(getTopTagBackground(responseHotelTag.type));
		}

		if(responseHotelTag.titleColor != null) {
			hotelTag.setTitleColor(responseHotelTag.titleColor);
		} else {
			hotelTag.setTitleColor(getTopTagTitleColor(responseHotelTag.type));
		}

		if(responseHotelTag.type != null) {
			hotelTag.setType(responseHotelTag.type.getValue());
		}
		if(responseHotelTag.getTimer() != null && responseHotelTag.getTimer().getExpiry() != null) {
			PersuasionTimer timer = new PersuasionTimer();
			timer.setExpiry(responseHotelTag.getTimer().getExpiry());
			timer.setStyle(getTimerStyle(responseHotelTag.type));
			hotelTag.setTimer(timer);
		}
		if(responseHotelTag.getStickyText() != null){
			hotelTag.setStickyText(responseHotelTag.getStickyText());
			if(responseHotelTag.type == ONLY_FOR_TODAY){
				hotelTag.setIsSticky(true);
			} else {
				hotelTag.setIsSticky(false);
			}
		}

		return hotelTag;
	}

	@Nullable
    public String getTopTagTitleColor(HotelTagType type) {
		if(type == ONLY_FOR_TODAY){
			return "#4A4A4A";
		}
		return null;
	}

	@Nullable
    public String getTopTagBackground(HotelTagType type) {
		if(type == ONLY_FOR_TODAY){
			return "#E6FFF9";
		}
		return null;
	}

	@Nullable
    protected PersuasionStyle getTimerStyle(HotelTagType type) {
		if(type == ONLY_FOR_TODAY){
			return commonConfigConsul.getOneDayDealTimerStyleConfig();
		}
        return null;
    }
	private BNPLDetails getBnplDetails(RoomTypeDetails roomTypeDetails, boolean showBnplCard) {
		if (roomTypeDetails.getTotalDisplayFare() != null) {
			boolean hotelOriginalBNPL = roomTypeDetails.getTotalDisplayFare().isOriginalBNPL();
            boolean isBNPL = roomTypeDetails.getTotalDisplayFare().getIsBNPLApplicable();
            BNPLVariant bnplVariant = roomTypeDetails.getTotalDisplayFare().getBnplVariant();
            String bnplPerMsg = StringUtils.isNotBlank(roomTypeDetails.getBnplPersuasionMsg()) ? roomTypeDetails.getBnplPersuasionMsg() : roomTypeDetails.getTotalDisplayFare().getBnplPersuasionMsg();
			String bnplPolicyText = StringUtils.isNotBlank(roomTypeDetails.getBnplPolicyText()) ? roomTypeDetails.getBnplPolicyText() : roomTypeDetails.getTotalDisplayFare().getBnplPolicyText();
			String bnplText = StringUtils.isNotBlank(roomTypeDetails.getBnplNewVariantText()) ? roomTypeDetails.getBnplNewVariantText() : null;
			String bnplSubText = StringUtils.isNotBlank(roomTypeDetails.getBnplNewVariantSubText()) ? roomTypeDetails.getBnplNewVariantSubText() : null;
			Double bnplFinalPrice = roomTypeDetails.getBnplFinalPrice();
            return commonResponseTransformer.buildBNPLDetails(isBNPL, bnplPerMsg, bnplPolicyText, bnplText, bnplSubText, hotelOriginalBNPL, showBnplCard, bnplVariant, bnplFinalPrice);
		}
		return null;
	}

	private Address getAddress(List<String> addressLines) {
    	if(CollectionUtils.isNotEmpty(addressLines)) {
    		Address address = new Address();
    		address.setLine1(addressLines.get(0));
    		address.setLine2(addressLines.size() > 1 ? addressLines.get(1) : null);
    		return address;
    	}
    	return null;
    }

    private void buildPropertyRules(AvailRoomsResponse availRoomsResponse, HotelRates hotelRates,boolean showHighLightedRules) {
    	RequestInputBO inputBo = new RequestInputBO.Builder()
				.buildCountryCode(hotelRates.getCountryCode())
				.buildPropertyType(hotelRates.getPropertyTypeMerged()!=null?hotelRates.getPropertyTypeMerged():hotelRates.getPropertyType())
				.buildHouseRules(hotelRates.getHouseRules())
				.buildDepositPolicy(hotelRates.getDepositPolicy())
				.buildSpokenLanguage(hotelRates.getSpokenLanguages())
				.buildMustReadRules(hotelRates.getMustReadRules())
				.buildPah(Utility.isPahOnlyPaymode(availRoomsResponse.getFeatureFlags().getPayMode()))
				.buildPahWithCC(Utility.isPahWithCCPaymode(availRoomsResponse.getFeatureFlags().getPayMode()))
    			.buildCancellationPolicyType(getCancellationPolicyType(hotelRates.getRoomTypeDetails()))
    			.buildCancellationDate(getCancellationDate(hotelRates.getRoomTypeDetails()))
    			.buildSupplierCode(getSupplierCode(hotelRates))
    			.buildCheckinPolicy(getCheckinPolicy(hotelRates.getRoomTypeDetails()))
				.buildHighlightMustReadRule(getHighlightMustReadRule(hotelRates.getHighlightMustReadRule(),showHighLightedRules))
				.buildConfirmationPolicy(commonResponseTransformer.getConfirmationPolicy(hotelRates.getRoomTypeDetails()))
				.buildNotices(hotelRates.getNotices())
    			.build();

		availRoomsResponse.setPropertyRules(commonResponseTransformer.getImportantInfoSection(inputBo));
    }

    private CancelPenalty getCancelPenalty(RoomTypeDetails roomTypeDetails) {
    	CancelPenalty mostRestrictedCancelPenalty = null;
    	for(Map.Entry<String,RoomType> roomType: roomTypeDetails.getRoomType().entrySet()){
			for(Map.Entry<String, com.mmt.hotels.model.response.pricing.RatePlan> ratePlan : roomType.getValue().getRatePlanList().entrySet()){
				if(mostRestrictedCancelPenalty == null) {
					mostRestrictedCancelPenalty = ratePlan.getValue().getCancelPenaltyList().get(0);
					if(Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(mostRestrictedCancelPenalty.getMostRestrictive())) {
						return mostRestrictedCancelPenalty;
					}
				}
				Optional<CancelPenalty> cancelPenalty = ratePlan.getValue().getCancelPenaltyList().stream().filter(penalty -> Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(penalty.getMostRestrictive())).findFirst();
				if(cancelPenalty.isPresent()) {
					mostRestrictedCancelPenalty = cancelPenalty.get();
					return mostRestrictedCancelPenalty;
				}
			}
    	}
    	return mostRestrictedCancelPenalty;
    }

    private RatePolicy getCheckinPolicy(RoomTypeDetails roomTypeDetails) {
    	RatePolicy checkInPolicy = null;
    	for(Map.Entry<String,RoomType> roomType: roomTypeDetails.getRoomType().entrySet()){
			for(Map.Entry<String, com.mmt.hotels.model.response.pricing.RatePlan> ratePlan : roomType.getValue().getRatePlanList().entrySet()){
				RatePolicy ratePolicy = ratePlan.getValue().getCheckinPolicy();
				if(checkInPolicy == null && ratePolicy !=null) {
					checkInPolicy = ratePolicy;
					if(Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(checkInPolicy.getMostRestrictive())) {
						return checkInPolicy;
					}
				}
				else if(checkInPolicy !=null && Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(ratePolicy.getMostRestrictive())) {
					checkInPolicy = ratePolicy;
					return checkInPolicy;
				}
			}
    	}
    	return checkInPolicy;
    }



	private void buildAdditionalCharges(AvailRoomsResponse availRoomsResponse, HotelRates hotelRates,
										boolean showTransfersFeeTxt, Map<String, String> expDataMap) {
		SupplierDetails supplierDetails = getSupplierDetails(hotelRates.getRoomTypeDetails());
		String roomName = null;
		for (String roomCode: hotelRates.getRoomTypeDetails().getRoomType().keySet()) {
			roomName = hotelRates.getRoomTypeDetails().getRoomType().get(roomCode).getRoomTypeName();
			break;
		}
		AdditionalChargesBO additionalChargesBO = new AdditionalChargesBO.Builder()
				.buildUserCurrency(hotelRates.getCurrencyCode())
				.buildHotelierCurrency(supplierDetails != null ? supplierDetails.getHotelierCurrencyCode() : "INR")
				.buildPropertyType(hotelRates.getPropertyTypeMerged()!=null?hotelRates.getPropertyTypeMerged():hotelRates.getPropertyType())
				.buildAdditionalFees(hotelRates.getMandatoryCharges())
				.buildConversionFactor(hotelRates.getRoomTypeDetails().getTotalDisplayFare().getConversionFactor())
				.buildBookingAmount(hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getDisplayPrice())
				.buildCityCode(hotelRates.getCityCode())
				.buildCountryCode(hotelRates.getCountryCode())
				.buildCityName(hotelRates.getCityName())
				.buildRoomName(roomName)
				.buildRecommendationFlow(hotelRates.getRoomTypeDetails().getRoomType().size() > 1)
				.build();
		availRoomsResponse.setAdditionalFees(commonResponseTransformer.buildAdditionalCharges(
				additionalChargesBO, showTransfersFeeTxt, hotelRates.getListingType(),
				PAGE_CONTEXT_REVIEW, expDataMap));
	}

	private void buildAlerts(AvailRoomsResponse availRoomsResponse, HotelRates hotelRates, boolean alertTypeVariant ,boolean isRtbcTrue, boolean pricerV2) {

		List<Alert> hotelLevelAlerts = new ArrayList<>();
		if(hotelRates.isRequestToBook() && !isRtbcTrue){
			if(hotelRates.isPreApprovalExpired()){
				Alert alert = new Alert();
				alert.setType(AlertType.RTB_CHANGE);
				alert.setText(polyglotService.getTranslatedData(ConstantsTranslation.RTB_CHANGE_ALERT_TEXT));
				hotelLevelAlerts.add(alert);
			}else if(hotelRates.isRTBRatePlanPreApproved()) {
				Alert alert = new Alert();
				alert.setType(AlertType.RTB_PRE_APPROVED);
				alert.setText(polyglotService.getTranslatedData(ConstantsTranslation.RTB_PRE_APPROVED_TEXT));
				hotelLevelAlerts.add(alert);
			}else if(!hotelRates.isRtbPreApproved()) {
				Alert alert = new Alert();
				alert.setType(AlertType.RTB);
				alert.setText(polyglotService.getTranslatedData(ConstantsTranslation.RTB_ALERT_TEXT));
				hotelLevelAlerts.add(alert);

			}
		}
		RatePolicy checkinPolicy = getCheckinPolicy(hotelRates.getRoomTypeDetails());
		if (null != checkinPolicy && StringUtils.isNotBlank(checkinPolicy.getShortDescription())) {
			Alert alert = new Alert();
			alert.setType(AlertType.CHECKIN_POLICY);
			alert.setText(checkinPolicy.getShortDescription());
			hotelLevelAlerts.add(alert);
		}

		RatePolicy confirmationPolicy = commonResponseTransformer.getConfirmationPolicy(hotelRates.getRoomTypeDetails());
		if (null != confirmationPolicy && StringUtils.isNotBlank(confirmationPolicy.getShortDescription())) {
			Alert alert = new Alert();
			alert.setType(AlertType.CONFIRMATION_POLICY);
			alert.setText(polyglotService.getTranslatedData(ConstantsTranslation.CTRIP_NON_INSTANT_TEXT));
			hotelLevelAlerts.add(alert);
		}

		if (CollectionUtils.isNotEmpty(hotelRates.getAlerts())) {
			//build price alert
			Optional<AlertInfo> priceAlert = hotelRates.getAlerts().stream()
					.filter(al -> AlertInfo.AlertType.PRICE.equals(al.getMismatchType())).findAny();
			//HTL-41292 For IH need to Change Alert Message if we get any alertType other than PRICE from pricer.
			boolean isIHNonPriceAlert = priceAlert.isPresent() && hotelRates.getAlerts().size()>1 && !DOM_COUNTRY.equalsIgnoreCase(hotelRates.getCountryCode()); //true if AlertType from pricer response list contains PRICE and any other AlertType also
			String propertyType = hotelRates.getPropertyTypeMerged()!=null?hotelRates.getPropertyTypeMerged():hotelRates.getPropertyType();
			if (priceAlert.isPresent()) {
                Alert alert = new Alert();
                AlertInfo alertInfo = priceAlert.get();
                Double amount = Utility.round(getAlertAmount(alertInfo), 0);

                // Get currency symbol based on user selected currency.
                String currencyType = availRoomsResponse.getTotalpricing().getCurrency();
                String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(currencyType) ? currencyType : DEFAULT_CUR_INR).getCurrencySymbol();
                if (priceIncreaseAlert(alertInfo)) {
                    alert.setType(AlertType.PRICE_INCREASE);
                    if (isIHNonPriceAlert && alertTypeVariant) {
                        alert.setText(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_CHANGE_ALERT_TEXT));
                        alert.setSubText(Utility.getTextBasedUponCurrency(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_CHANGE_INCREASE_ALERT_SUB_TEXT), currencySymbol, amount));
                    } else if (!alertTypeVariant) {
                        alert.setText(Utility.getTextBasedUponCurrency(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_CHANGE_INCREASE_ALERT_TEXT_OLD_VERSION), currencySymbol, amount));
                        alert.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_CHANGE_DECREASE_OLD_VERSION_ALERT_SUB_TEXT));
                    } else {
						String alertText = polyglotService.getTranslatedData(ConstantsTranslation.PRICE_INCREASE_ALERT_TEXT);
						if(StringUtils.isNotEmpty(alertText)){
							alertText = MessageFormat.format(alertText, propertyType, amount);
							alertText = alertText.replace("<cur>",currencySymbol);
						}
                        alert.setText(alertText);
                        alert.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_INCREASE_ALERT_SUB_TEXT));
                    }
				} else {
					alert.setType(AlertType.PRICE_DECREASE);
                    if (isIHNonPriceAlert && alertTypeVariant) {
                        alert.setText(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_CHANGE_ALERT_TEXT));
                        alert.setSubText(Utility.getTextBasedUponCurrency(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_CHANGE_DECREASE_ALERT_SUB_TEXT), currencySymbol, amount));
                    } else if (!alertTypeVariant) {
                        alert.setText(Utility.getTextBasedUponCurrency(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_CHANGE_DECREASE_ALERT_TEXT_OLD_VERSION), currencySymbol, amount));
                        alert.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_CHANGE_DECREASE_OLD_VERSION_ALERT_SUB_TEXT));
                    } else {
						String alertText = polyglotService.getTranslatedData(ConstantsTranslation.PRICE_DECREASE_ALERT_TEXT);
						if(StringUtils.isNotEmpty(alertText)){
							alertText = MessageFormat.format(alertText, amount);
							alertText = alertText.replace("<cur>",currencySymbol);
						}
                        alert.setText(alertText);
                        alert.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_DECREASE_ALERT_SUB_TEXT));
                    }
				}
				//adding Alert Reasons list parsed from Pricer Mismatch error messages to client for tracking purpose.
				alert.setReasons(hotelRates.getAlerts().stream().map(AlertInfo::getMismatchType).collect(Collectors.toList()));
				alert.setCurrency(hotelRates.getCurrencyCode());
				alert.setAmount(amount);
				hotelLevelAlerts.add(alert);
			}

			//build rate plan specific alerts - cancelpolicy and meal plan
			for (RatePlan ratePlan : availRoomsResponse.getRateplanlist()) {
				List<AlertInfo> ratePlanAlerts = hotelRates.getAlerts().stream()
						.filter(al -> (pricerV2 || ratePlan.getCode().equals(al.getRpcc())) && !AlertInfo.AlertType.PRICE.equals(al.getMismatchType()))
						.collect(Collectors.toList());
				for (AlertInfo alertInfo : ratePlanAlerts) {
					Alert alert = new Alert();
					if (AlertInfo.AlertType.CANCELLATION.equals(alertInfo.getMismatchType())) {
						alert.setType(AlertType.CANCEL_POLICY);
						alert.setText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.CANCEL_POLICY_CHANGE_ALERT_TEXT), propertyType.toLowerCase()));
					} else if(AlertInfo.AlertType.MEALPLAN.equals(alertInfo.getMismatchType())){
						alert.setType(AlertType.MEAL_PLAN);
						alert.setText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.MEAL_PLAN_CHANGE_ALERT_TEXT), propertyType.toLowerCase()));
					}
					if (CollectionUtils.isEmpty(ratePlan.getAlerts()))
						ratePlan.setAlerts(new ArrayList<>());
					ratePlan.getAlerts().add(alert);
				}
			}
		}
		availRoomsResponse.setAlerts(hotelLevelAlerts);
	}

	private Double getAlertAmount(AlertInfo alertInfo) {
		return Math.abs(Double.valueOf(alertInfo.getActualValue()) - Double.valueOf(alertInfo.getExpectedValue()));
	}

	private boolean priceIncreaseAlert(AlertInfo alertInfo) {
		return Double.valueOf(alertInfo.getActualValue()) > Double.valueOf(alertInfo.getExpectedValue());
	}

	private String getSupplierCode(HotelRates hotelRates) {
    	SupplierDetails supplierDetails = getSupplierDetails(hotelRates.getRoomTypeDetails());
		if(supplierDetails !=null) {
			return supplierDetails.getSupplierCode();
		}
    	return null;
    }


	private HighlightMustReadRule getHighlightMustReadRule(HighlightMustReadRule highlightMustReadRule,boolean showHighLightedRules) {
		if (highlightMustReadRule != null && highlightMustReadRule.getHighlightedTitle()!=null&& showHighLightedRules){
			return highlightMustReadRule;
		}else {
			return  null;
		}


	}

    private String getCancellationPolicyType(RoomTypeDetails roomTypeDetails) {
    	CancelPenalty cancelPenalty = getCancelPenalty(roomTypeDetails);
    	if(cancelPenalty !=null && cancelPenalty.getCancellationType() !=null) {
			if (CancelPenalty.CancellationType.FREE_CANCELLATON.equals(cancelPenalty.getCancellationType()))
				return BookedCancellationPolicyType.FC.name();
			else
				return BookedCancellationPolicyType.NR.name();
		}
    	else
    		return BookedCancellationPolicyType.NR.name();


    }

    private String getCancellationDate(RoomTypeDetails roomTypeDetails) {
    	CancelPenalty cancelPenalty = getCancelPenalty(roomTypeDetails);
    	if(cancelPenalty !=null) {
    		return cancelPenalty.getTillDate();
    	}
    	return null;
    }

    private void updateHotelierCurrencyPricingDetails(TotalPricing totalpricing, RoomTypeDetails roomTypeDetails, double convFactor, String userCurrency, String payMode) {
    	// update only if paymode is PAH
    	if(Utility.isPahOnlyPaymode(payMode)) {
			SupplierDetails supplierDetails = getSupplierDetails(roomTypeDetails);
			if(supplierDetails !=null) {
				commonResponseTransformer.updateTotalAmountInHotelierCurrency(totalpricing.getDetails(), payMode, userCurrency, supplierDetails.getHotelierCurrencyCode(), convFactor);
			}
    	}
    }

	private String getSegmentId(RoomTypeDetails roomTypeDetails)
	{
		RoomType roomType = null;
		String segmentId = null;
		for (String roomCode: roomTypeDetails.getRoomType().keySet()) {
			roomType = roomTypeDetails.getRoomType().get(roomCode);
			break;
		}

		if(roomType !=null) {
			Map<String, com.mmt.hotels.model.response.pricing.RatePlan> ratePlanMap = roomType.getRatePlanList();
			for (String code: ratePlanMap.keySet()) {
				com.mmt.hotels.model.response.pricing.RatePlan ratePlanCB = ratePlanMap.get(code);
				segmentId = ratePlanCB.getSegmentId();
				break;
			}
		}

		return segmentId;
	}
    private SupplierDetails getSupplierDetails(RoomTypeDetails roomTypeDetails) {
    	RoomType roomType = null;
    	SupplierDetails supplierDetails = null;
		for (String roomCode: roomTypeDetails.getRoomType().keySet()) {
			roomType = roomTypeDetails.getRoomType().get(roomCode);
			break;
		}
		if(roomType !=null) {
			Map<String, com.mmt.hotels.model.response.pricing.RatePlan> ratePlanMap = roomType.getRatePlanList();
			for (String code: ratePlanMap.keySet()) {
				com.mmt.hotels.model.response.pricing.RatePlan ratePlanCB = ratePlanMap.get(code);
				supplierDetails = ratePlanCB.getSupplierDetails();
				break;
			}
		}
		return supplierDetails;
    }

	private com.mmt.hotels.clientgateway.request.payment.SpecialRequest buildSpecialRequests(SpecialRequest spclReq, boolean enableThemification) {
    	if(spclReq !=null) {
    		com.mmt.hotels.clientgateway.request.payment.SpecialRequest specialRequest = new com.mmt.hotels.clientgateway.request.payment.SpecialRequest();
    		specialRequest.setDisclaimer(spclReq.getDisclaimer());
    		if(CollectionUtils.isNotEmpty(spclReq.getCategories())) {
    			List<SpecialRequestCategory> spclCatList = new ArrayList<>();
    			for (com.mmt.hotels.model.response.pricing.SpecialRequestCategory reqCategory : spclReq.getCategories()) {
    				SpecialRequestCategory spclCat = new SpecialRequestCategory();
    				spclCat.setCode(reqCategory.getCode());
    				spclCat.setName(reqCategory.getName());
    				spclCat.setType(reqCategory.getType());
					if (enableThemification && ANY_OTHER_SPECIAL_REQUEST_CODE.equalsIgnoreCase(reqCategory.getCode())) {
						spclCat.setPlaceholder(reqCategory.getPlaceholder());
					}
    				if(CollectionUtils.isNotEmpty(reqCategory.getSubCategories()))
    					buildSpecialReqSubCategories(reqCategory.getSubCategories(), spclCat);
    				spclCatList.add(spclCat);
				}
    			specialRequest.setCategories(spclCatList);
    		}
    		return specialRequest;
    	}
    	return null;
    }

	private SpecialRequestV2 buildSpecialRequestsV2(List<com.mmt.hotels.model.response.pricing.SpecialRequestV2> spclReq) {
		if(CollectionUtils.isNotEmpty(spclReq)) {
			SpecialRequestV2 specialRequest = new SpecialRequestV2();
			specialRequest.setCardData(buildSpecialRequestV2CardData());
			specialRequest.setFormData(buildSpecialRequestV2FormData(spclReq));
			return specialRequest;
		}
		return null;
	}

	private SpecialRequestV2CardData buildSpecialRequestV2CardData() {
		SpecialRequestV2CardData cardData = new SpecialRequestV2CardData();
		cardData.setDefaultState(buildSpecialRequestV2DefaultCard());
		cardData.setSelectedState(buildSpecialRequestV2SelectedCard());
		return cardData;
	}

	private SpecialRequestV2Form buildSpecialRequestV2FormData(List<com.mmt.hotels.model.response.pricing.SpecialRequestV2> spclReq){
		SpecialRequestV2Form formData = new SpecialRequestV2Form();
		formData.setBannerData(buildSpecialRequestV2FormBannerData());
		formData.setTemplates(buildSpecialRequestV2Templates(spclReq));
		return formData;
	}

	private List<SpecialRequestV2FormTemplates> buildSpecialRequestV2Templates(List<com.mmt.hotels.model.response.pricing.SpecialRequestV2> spclReq) {
		List<SpecialRequestV2FormTemplates> templates = new ArrayList<>();
		for (com.mmt.hotels.model.response.pricing.SpecialRequestV2 reqCategory : spclReq) {
			if(reqCategory.getSubCategoryTypes() != null) {
				SpecialRequestV2FormTemplates template = new SpecialRequestV2FormTemplates();
				template.setTemplateId(reqCategory.getTemplateId());
				template.setTemplateName(reqCategory.getTemplateName());
				template.setSelectionType(reqCategory.getSelectionType().name());
				template.setSubCategoryTypes(buildSpecialRequestV2TemplateSubcategoryTypes(reqCategory.getSubCategoryTypes()));
				templates.add(template);
			} else {
				if (reqCategory.getTemplateId().equalsIgnoreCase(SPECIAL_REQ_V2_OTHER_REQUEST_TEMPLATE_ID)) {
					SpecialRequestV2FormTemplates template = new SpecialRequestV2FormTemplates();
					template.setTemplateId(reqCategory.getTemplateId());
					template.setTemplateName(reqCategory.getTemplateName());
					template.setSelectionType(reqCategory.getSelectionType().name());
					templates.add(template);
				}
			}
		}
		return templates;
	}

	private List<SpecialRequestV2SubcategoryType> buildSpecialRequestV2TemplateSubcategoryTypes(List<TemplateSubtype> subCategoryTypes) {
		List<SpecialRequestV2SubcategoryType> categorySubTypes = new ArrayList<>();
		for(TemplateSubtype categorySubtype: subCategoryTypes) {
			SpecialRequestV2SubcategoryType subcategoryType = new SpecialRequestV2SubcategoryType();
			subcategoryType.setTemplateSubTypeId(categorySubtype.getTemplateSubTypeId());
			subcategoryType.setDisplayName(categorySubtype.getDisplayName());
			subcategoryType.setSubCategories(buildSpecialRequestV2CategorySubtypeSubcategories(categorySubtype.getStartRange(), categorySubtype.getEndRange()));
			categorySubTypes.add(subcategoryType);
		}
		return categorySubTypes;
	}

	private List<SpecialRequestV2SubcategoryTypeInfo> buildSpecialRequestV2CategorySubtypeSubcategories(String startRange, String endRange) {
		List<SpecialRequestV2SubcategoryTypeInfo> subcategoryTypeInfos = new ArrayList<>();
		if(StringUtils.isNotEmpty(startRange) && StringUtils.isNotEmpty(endRange)) {
			subcategoryTypeInfos.add(buildDateRangeDropdownSubcategoryInfo(startRange, endRange));
		}
		subcategoryTypeInfos.add(buildUserCommentSubcategoryInfo());
		return subcategoryTypeInfos;
	}

	private SpecialRequestV2SubcategoryTypeInfo buildDateRangeDropdownSubcategoryInfo(String startRange, String endRange) {
		SpecialRequestV2SubcategoryTypeInfo dateRangeDropdownSubcategoryInfo = new SpecialRequestV2SubcategoryTypeInfo();
		dateRangeDropdownSubcategoryInfo.setType(SELECTION_TYPE_DROP_DOWN);
		dateRangeDropdownSubcategoryInfo.setValues(buildDateRangeValues(startRange, endRange));
		dateRangeDropdownSubcategoryInfo.setValuesMap(buildValueMap(dateRangeDropdownSubcategoryInfo.getValues()));
		return dateRangeDropdownSubcategoryInfo;
	}

	private String[] buildDateRangeValues(String startRange, String endRange) {
		List<String> timeValues = new ArrayList<>();

		try {
			// Parse start and end hours from the time strings (assuming format "HH:mm")
			int startHour = Integer.parseInt(startRange.split(":")[0]);
			int endHour = Integer.parseInt(endRange.split(":")[0]);

			// Generate time values from start to end hour
			for (int hour = startHour; hour <= endHour; hour++) {
				String timeString = convertTo12HourFormat(hour);
				timeValues.add(timeString);
			}

		} catch (NumberFormatException e) {
			LOGGER.error("Error parsing time range: startRange={}, endRange={}", startRange, endRange, e);
			return null;
		}

		return timeValues.toArray(new String[0]);
	}

	private String convertTo12HourFormat(int hour24) {
		if (hour24 == 0) {
			return "12:00 AM";
		} else if (hour24 < 12) {
			return String.format("%02d:00 AM", hour24);
		} else if (hour24 == 12) {
			return "12:00 PM";
		} else {
			return String.format("%02d:00 PM", hour24 - 12);
		}
	}

	private SpecialRequestV2SubcategoryTypeInfo buildUserCommentSubcategoryInfo() {
		SpecialRequestV2SubcategoryTypeInfo userCommentSubcategoryInfo = new SpecialRequestV2SubcategoryTypeInfo();
		userCommentSubcategoryInfo.setType(SELECTION_TYPE_INPUT_BOX);
		userCommentSubcategoryInfo.setPlaceholder(SPECIAL_REQ_V2_INPUT_BOX_PLACEHOLDER_TEXT);
		return userCommentSubcategoryInfo;
	}

	private SpecialRequestV2FormBannerData buildSpecialRequestV2FormBannerData() {
		SpecialRequestV2FormBannerData bannerData = new SpecialRequestV2FormBannerData();
		bannerData.setIconUrl(specialRequestV2BellIconUrl);
		bannerData.setDesc(polyglotService.getTranslatedData(SPECIAL_REQUEST_V2_BANNER_DESC));
		bannerData.setBackgroundImageUrl(specialRequestV2BannerBackgroundUrl);
		return bannerData;
	}

	private SpecialRequestV2CardType buildSpecialRequestV2DefaultCard() {
		SpecialRequestV2CardType defaultCard = new SpecialRequestV2CardType();
		defaultCard.setTitle(polyglotService.getTranslatedData(SPECIAL_REQUEST_V2_DEFAULT_CARD_TITLE));
		defaultCard.setDesc(polyglotService.getTranslatedData(SPECIAL_REQUEST_V2_DEFAULT_CARD_DESC));
		defaultCard.setCtaText(polyglotService.getTranslatedData(SPECIAL_REQUEST_V2_DEFAULT_CARD_CTA_TEXT));
		defaultCard.setIconURL(specialRequestV2BellIconUrl);
		return defaultCard;
	}

	private SpecialRequestV2CardType buildSpecialRequestV2SelectedCard() {
		SpecialRequestV2CardType selectedCard = new SpecialRequestV2CardType();
		selectedCard.setTitle(polyglotService.getTranslatedData(SPECIAL_REQUEST_V2_SELECTED_CARD_TITLE));
		selectedCard.setDesc(polyglotService.getTranslatedData(SPECIAL_REQUEST_V2_SELECTED_CARD_DESC));
		selectedCard.setCtaText(polyglotService.getTranslatedData(SPECIAL_REQUEST_V2_SELECTED_CARD_CTA_TEXT));
		return selectedCard;
	}


    private void buildSpecialReqSubCategories(List<com.mmt.hotels.model.response.pricing.SpecialRequestCategory> subCategories, SpecialRequestCategory spclCat){
    	List<SpecialRequestCategory> spclSubCategories = new ArrayList<>();
    	for (com.mmt.hotels.model.response.pricing.SpecialRequestCategory subCat : subCategories) {
    		SpecialRequestCategory spclSubCat = new SpecialRequestCategory();
    		if(Constants.SPCL_CAT_TYPE_LABEL.equalsIgnoreCase(subCat.getType())) {
    			spclCat.setLabel(subCat.getName());
    		}else {
    			spclSubCat.setCode(subCat.getCode());
    			spclSubCat.setName(subCat.getName());
    			spclSubCat.setType(subCat.getType());
    			spclSubCat.setValues(subCat.getValues());
    			spclSubCat.setValuesMap(buildValueMap(subCat.getValues()));
    			spclSubCategories.add(spclSubCat);
    		}
		}
    	spclCat.setSubCategories(spclSubCategories);
    }

	private Map<String, String> buildValueMap(String[] values) {
		if (ArrayUtils.isEmpty(values)) {
			return null;
		}
		Map<String, String> valuesMap = new LinkedHashMap<>();
		for (String value : values) {
			if ("ARA".equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue())) && (StringUtils.containsIgnoreCase(value, "AM") || StringUtils.containsIgnoreCase(value, "PM"))) {
				String[] time = value.split("\\s");
				String translatedValue = polyglotService.getTranslatedData(time[1]) + " " + time[0];
				valuesMap.put(value, translatedValue);
			} else {
				valuesMap.put(value, value);
			}
		}
		return valuesMap;
	}

    private PanInfo getPanInfo(boolean panRequired, boolean panAvailable) {
    	PanInfo panInfo = new PanInfo();
		if(enablePanCardCheck){
			panInfo.setPanCardRequired(panRequired);
		}else {
			panInfo.setPanCardRequired(false);
		}
    	panInfo.setPanAvailable(panAvailable);
    	return panInfo;
    }

	private GstInfo getGstInfo(String gstIn, String countryCode, String siteDomain, String gstStateCode, RoomTypeDetails roomTypeDetails) {
		SupplierDetails supplierDetails = null;
		if(roomTypeDetails != null) {
			supplierDetails = getSupplierDetails(roomTypeDetails);
		}
		GstInfo gstInfo = null;
//		if ("AE".equalsIgnoreCase(siteDomain)) {
//			if(supplierDetails != null && StringUtils.isNotBlank(supplierDetails.getSupplierCode()) && Constants.SUPPLIER_INGO.equalsIgnoreCase(supplierDetails.getSupplierCode())) {
//				gstInfo = new GstInfo();
//				if (StringUtils.isNotBlank(gstStateCode)) {
//					gstInfo.setGstStateCode(gstStateCode);
//				}
//				if (countryCode.equalsIgnoreCase("UNI")) {
//					gstInfo.setText(polyglotService.getTranslatedData(ConstantsTranslation.TIN_NO_NOT_AVAILABLE_TEXT));
//				} else {
//					return null;
//				}
//				return gstInfo;
//			}
//		}
		if (Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode) && !Utility.isRegionGccOrKsa(siteDomain)) {
			gstInfo = new GstInfo();
			if (StringUtils.isNotBlank(gstStateCode)) {
				gstInfo.setGstStateCode(gstStateCode);
			}
			if (StringUtils.isNotBlank(gstIn)) {
				gstInfo.setGstin(gstIn);
			} else {
				gstInfo.setText(polyglotService.getTranslatedData(ConstantsTranslation.GSTN_NOT_AVAILABLE_TEXT));
			}
			gstInfo.setClaimText(polyglotService.getTranslatedData(ConstantsTranslation.GSTIN_CLAIM_TEXT));
			return gstInfo;
		}
		return null;
	}

    private FeatureFlags getFeatureFlags(HotelRates hotelRates, Map<String, String> expDataMap, com.mmt.model.CorpData corpData) {
    	FeatureFlags featureFlags = new FeatureFlags();
    	featureFlags.setPahWalletApplicable(hotelRates.isPahWalletApplicable());
    	featureFlags.setSoldOut(hotelRates.getSoldOut()!=null? hotelRates.getSoldOut(): false);
    	featureFlags.setShowFCBanner(hotelRates.isShowFcBanner());
    	featureFlags.setBlackEligible(hotelRates.isBlackEligible());
    	featureFlags.setPayMode(getRatePlanPayMode(hotelRates.getRoomTypeDetails()));
    	featureFlags.setForeignTravel(hotelRates.isForeignTravel());
    	featureFlags.setLeadPassengerMandatoryPerRoom(hotelRates.isLeadPassengerMandatoryPerRoom());
		featureFlags.setRequestToBook(hotelRates.isRequestToBook());
		featureFlags.setRtbPreApproved(hotelRates.isRtbPreApproved());
		featureFlags.setRtbAutoCharge(hotelRates.isRtbAutoCharge());
    	if(hotelRates.getUserWalletDetails() !=null) {
    		if((hotelRates.getUserWalletDetails().getRealAmount() !=null && hotelRates.getUserWalletDetails().getRealAmount() > 0.0d) ||
    				hotelRates.getUserWalletDetails().getPlusAmount() !=null && hotelRates.getUserWalletDetails().getPlusAmount() > 0.0d) {
    			featureFlags.setRealWalletAvailable(true);
    		}
    	}
		boolean paylaterPriceValidated = null!=hotelRates.getRoomTypeDetails()
				&& null!=hotelRates.getRoomTypeDetails().getTotalDisplayFare()
				&& null!=hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown()
				&& payLaterCardLimit >= hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getDisplayPrice();
		featureFlags.setPayLaterCard(paylaterPriceValidated && MapUtils.isNotEmpty(expDataMap) && Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.PAY_LATER_CARD_EXP)));
		featureFlags.setMyPartnerMoveToTdsTaxStructure(MapUtils.isNotEmpty(expDataMap) && TRUE.equalsIgnoreCase(expDataMap.get(MY_PARTNER_MOVE_TO_TDS_TAX_STRUCTURE)));
		featureFlags.setShowScrolldown(hotelRates.isShowScrolldown());
		if(corpData != null){
			featureFlags.setCaptureAllPaxDetailsHotel(corpData.isCaptureAllPaxDetailsHotel());
		}
    	return featureFlags;
    }

    public TotalPriceResponse convertTotalPricingResponse(TotalPricingResponse totalPricingResponseOld, TotalPricingRequest getTotalPriceRequest, String countryCode){
    	TotalPriceResponse totalPriceResponse = new TotalPriceResponse();
    	totalPriceResponse.setTotalPricing(getTotalAmountDetails(totalPricingResponseOld, countryCode, getTotalPriceRequest.getExpData()));
		totalPriceResponse.setCurrency(totalPricingResponseOld.getCurrency());
		if(totalPricingResponseOld!=null && StringUtils.isNotEmpty(totalPricingResponseOld.getSbppExpValue())) {
			if( totalPricingResponseOld.getNoOfNightStays()!=null && totalPricingResponseOld.getAdultCount() != null && totalPricingResponseOld.getRoomCount()!=null && StringUtils.isNotEmpty(totalPricingResponseOld.getSbppExpValue())) {
				Double displayAmount = utility.getTotalAmount(totalPriceResponse.getTotalPricing());
				String currency = totalPricingResponseOld.getPriceBreakdown() != null && StringUtils.isNotEmpty(totalPricingResponseOld.getPriceBreakdown().getCurrency()) ? Currency.getCurrencyEnum(totalPricingResponseOld.getPriceBreakdown().getCurrency()).getCurrencySymbol() : HINDI_RUPEE;
				totalPriceResponse.getTotalPricing().setPricePersuasions(utility.buildHomestayPersuasion(true, totalPricingResponseOld.getSbppExpValue(), totalPricingResponseOld.getRoomCount(), totalPricingResponseOld.getAdultCount(), displayAmount, totalPricingResponseOld.getNoOfNightStays(), currency)); // here we will set price Persuasion for homestay
			}
		}
    	totalPriceResponse.setAddon(getAddOnPricing(totalPricingResponseOld, countryCode, getTotalPriceRequest.getExpData()));
		boolean enableThemification = totalPricingResponseOld != null && totalPricingResponseOld.getThemifiedDetails() != null && totalPricingResponseOld.getThemifiedDetails().isThemificationEnabled();
    	if(totalPricingResponseOld.getPriceBreakdown() !=null) {
    		com.mmt.hotels.model.response.pricing.TotalPricing priceBreakdown = totalPricingResponseOld.getPriceBreakdown();
    		totalPriceResponse.setCancellationTimeline(commonResponseTransformer.buildCancellationTimeline(priceBreakdown.getCancellationTimeline(), null));
			totalPriceResponse.setCancellationPolicyTimeline(commonResponseTransformer.buildCancellationPolicyTimeline(priceBreakdown.getCancellationTimeline(), enableThemification, null));
			boolean showBnplFlags = Utility.isShowBnplCard(getTotalPriceRequest.getFeatureFlags());
			BNPLVariant bnplVariant = priceBreakdown.getBnplVariant();
			if(bnplVariant == null || bnplVariant.equals(BNPLVariant.BNPL_NOT_APPLICABLE)) {
				bnplVariant = totalPricingResponseOld.getBnplVariant();
			}
			Map<String, Object> nonBnplAppliedCouponDetailsMap = commonResponseTransformer.fetchNonBnplAppliedCouponDetails(totalPriceResponse.getTotalPricing());
			boolean bnplDisabledDueToNonBnplCouponApplied = (boolean) nonBnplAppliedCouponDetailsMap.get(Constants.BNPL_DISABLED_DUE_TO_NON_BNPL_COUPON_APPLIED);
			String nonBnplCouponAppliedCode = (String) nonBnplAppliedCouponDetailsMap.get(Constants.NON_BNPL_COUPON_APPLIED_CODE);
			boolean insuranceAddonSelected = priceBreakdown.getLobWisePricing() != null && priceBreakdown.getLobWisePricing().get(LOB.INSURANCE) != null;
			Integer activeBnplBookingCount = totalPricingResponseOld.isUserLevelBnplDisabled() ? totalPricingResponseOld.getActiveBnplBookingCount() : null;
			BNPLDisabledReason bnplDisabledReason = null;

			if (totalPricingResponseOld.isShowDisabledBnplDetails()) {
				bnplDisabledReason = commonResponseTransformer.getBNPLDisabledReason(totalPricingResponseOld.isUserLevelBnplDisabled(), bnplDisabledDueToNonBnplCouponApplied, false);
			}
			int bnplAllowedCount = bnplActiveBookingThreshold;
			if (totalPricingResponseOld.getBnplAllowedCount() != null && totalPricingResponseOld.getBnplAllowedCount() > 0) {
				bnplAllowedCount = totalPricingResponseOld.getBnplAllowedCount();
			}
			if(priceBreakdown.isInsuranceStartDateBeforeBnplChargeDate() && totalPricingResponseOld.isShowDisabledBnplDetails()){
				BNPLDetails bnplDetails = new BNPLDetails();
				bnplDetails.setBnplApplicable(false);
				bnplDetails.setBnplNewVariantText(priceBreakdown.getBnplNewVariantText());
				bnplDetails.setBnplNewVariantSubText(priceBreakdown.getBnplNewVariantSubText());
				totalPriceResponse.setBnplDetails(bnplDetails);
			} else if (bnplDisabledReason != null && bnplVariant != null && !BNPLVariant.BNPL_NOT_APPLICABLE.equals(bnplVariant)) {
				totalPriceResponse.setBnplDetails(commonResponseTransformer.buildBNPLDetailsForDisabledBnpl(bnplDisabledReason, nonBnplCouponAppliedCode, bnplVariant, activeBnplBookingCount, bnplAllowedCount));
			} else {
				totalPriceResponse.setBnplDetails(commonResponseTransformer.buildBNPLDetails(priceBreakdown.isBnplApplicable(), priceBreakdown.getBnplPersuasionMsg(),
						priceBreakdown.getBnplPolicyText(), priceBreakdown.getBnplNewVariantText(), priceBreakdown.getBnplNewVariantSubText(), priceBreakdown.isOriginalBNPL(), showBnplFlags, bnplVariant, priceBreakdown.getBnplFinalPrice()));
				if(priceBreakdown.getFullPayment()!=null) {
					totalPriceResponse.setFullPayment(utility.buildFullPayment(priceBreakdown.getFullPayment()));
				}
			}

			if (totalPricingResponseOld.getPriceBreakdown().getCorpMetaData() != null) {
				totalPriceResponse.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(totalPricingResponseOld.getPriceBreakdown().getCorpMetaData(), utility.isTcsV2FlowEnabled(getTotalPriceRequest.getExpData())));
			}
			totalPriceResponse.setRateplanlist(commonResponseTransformer.buildRateplanList(priceBreakdown.getRoomTypes(), totalPriceResponse.getBnplDetails(), priceBreakdown.isBnplNewVariant(), bnplVariant, priceBreakdown.getPeakDate(), enableThemification));
			totalPriceResponse.setPaymentPlan(commonResponseTransformer.buildPaymentPlan(totalPricingResponseOld.getPaymentPlan()));
		}

		// Set the BNPL unavailable message when BNPL is removed due to addition of insurance addon
		if (totalPriceResponse.getTotalPricing() != null && totalPricingResponseOld.getPriceBreakdown() != null
				&& StringUtils.isNotBlank(totalPricingResponseOld.getPriceBreakdown().getBnplUnavailableMsg())) {
			totalPriceResponse.getTotalPricing().setBnplUnavailableMsg(null);
		}

		//HTL-39856 ExpressCheckoutDetail node sent to client for myPartner Express checkout on review page
		if (totalPricingResponseOld != null && totalPricingResponseOld.getPriceBreakdown() != null && totalPricingResponseOld.getPriceBreakdown().getExpressCheckoutDetail() != null) {
			totalPriceResponse.getTotalPricing().setExpressCheckoutDetail(totalPricingResponseOld.getPriceBreakdown().getExpressCheckoutDetail());
		}

		if (enableThemification) {
			String payMode = totalPricingResponseOld.getPriceBreakdown() != null ? totalPricingResponseOld.getPriceBreakdown().getPayMode() : EMPTY_STRING;
			if (totalPricingResponseOld.getThemifiedDetails().getPriceFooter() != null) {
				PriceFooterDetail priceFooterDetail = totalPricingResponseOld.getThemifiedDetails().getPriceFooter();
				totalPriceResponse.getTotalPricing().setPriceFooter(commonResponseTransformer.buildPriceFooter(priceFooterDetail.isTaxIncluded(), priceFooterDetail.getLos(), priceFooterDetail.getRoomCount()));
			}
			if (totalPriceResponse.getBnplDetails() != null && totalPriceResponse.getBnplDetails().isBnplApplicable()) {
				totalPriceResponse.getBnplDetails().setPriceFooter(commonResponseTransformer.buildBNPLPriceFooter());
			}
			commonResponseTransformer.updatePriceFooterForPAHOnly(payMode, totalPriceResponse.getTotalPricing());
		}

		if (totalPricingResponseOld.getFlexiDetailBottomSheet() != null) {
			totalPriceResponse.setFlexiDetailBottomSheet(buildFlexiDetailBottomSheet(totalPricingResponseOld.getFlexiDetailBottomSheet()));
		}

		if (CollectionUtils.isNotEmpty(totalPricingResponseOld.getUpdatedUpsellOptions())) {
			if (CollectionUtils.isEmpty(totalPriceResponse.getUpdatedUpsellOptions()))	{
				totalPriceResponse.setUpdatedUpsellOptions(new ArrayList<>());
			}
			for (com.mmt.hotels.model.response.txn.UpdatedUpsellOptions updatedUpsellOptions : totalPricingResponseOld.getUpdatedUpsellOptions()) {
				totalPriceResponse.getUpdatedUpsellOptions().add(buildUpdatedUpsellOptions(updatedUpsellOptions));
			}
		}

		/* Build no cost emi details from display price breakdown */
		buildNoCostEmiDetailsForTotalPricing(totalPricingResponseOld.getPriceBreakdown(), totalPriceResponse);


		return totalPriceResponse;
    }

	private UpdatedUpsellOptions buildUpdatedUpsellOptions(com.mmt.hotels.model.response.txn.UpdatedUpsellOptions updatedUpsellOptions) {
		UpdatedUpsellOptions updatedUpsellOptionsResponse = new UpdatedUpsellOptions();
		updatedUpsellOptionsResponse.setAddOnType(updatedUpsellOptions.getAddOnType());
		updatedUpsellOptionsResponse.setRatePlanCode(updatedUpsellOptions.getRatePlanCode());
		updatedUpsellOptionsResponse.setRoomCode(updatedUpsellOptions.getRoomCode());
		updatedUpsellOptionsResponse.setSuccessDisplayText(updatedUpsellOptions.getSuccessDisplayText());
		return updatedUpsellOptionsResponse;
	}

	private TotalPricing getTotalAmountDetails(TotalPricingResponse totalPricingResponseOld, String countryCode, String expData){
    	if(totalPricingResponseOld !=null && totalPricingResponseOld.getPriceBreakdown() !=null) {
    		TotalPricing totalPrice = null;
			double flexiCancalCharges = 0d;
    		if(MapUtils.isNotEmpty(totalPricingResponseOld.getPriceBreakdown().getLobWisePricing()) &&
    				totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().containsKey(LOB.HOTEL)) {
				totalPrice = commonResponseTransformer.getTotalPricing(totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().get(LOB.HOTEL), countryCode,
						totalPricingResponseOld.getPriceBreakdown().getPayMode(),
						false, "", expData, false, totalPricingResponseOld.getPriceBreakdown().getPriceBreakupText(),
						totalPricingResponseOld.getPriceBreakdown().isCbrAvailable(), null, totalPricingResponseOld.isMetaTraffic(),totalPricingResponseOld.getHotelBenefitInfo(), totalPricingResponseOld.getPriceBreakdown().getCurrency(), true);
				DisplayPriceBreakDown dPBrkDwn = totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().get(LOB.HOTEL) ;
				if (dPBrkDwn != null && dPBrkDwn.getFlexiCancellationCharges() > 0d) {
					flexiCancalCharges = dPBrkDwn.getFlexiCancellationCharges();
				}
			}
    		if(totalPrice !=null && CollectionUtils.isNotEmpty(totalPrice.getDetails())) {
    			List<PricingDetails> priceDetails = totalPrice.getDetails();
				if(MapUtils.isNotEmpty(totalPricingResponseOld.getPriceBreakdown().getLobWisePricing()) &&
						totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().containsKey(LOB.ADDON)) {
					commonResponseTransformer.buildCharityAddonBreakUp(priceDetails, totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().get(LOB.ADDON));
				}
				Map<String, PricingDetails> pricingDetailsMap = getPricingDetailKeyToPricingDetailObjectMap(priceDetails);
    			updateTotalAmountIncludingAddon(pricingDetailsMap, totalPricingResponseOld.getPriceBreakdown().getDisplayPrice(), flexiCancalCharges);
				commonResponseTransformer.buildPaymentInfoTextForAlternateCurrency(totalPrice, countryCode, totalPricingResponseOld.getPriceBreakdown().getPayMode(), totalPricingResponseOld.getPriceBreakdown().getCurrency());
    			if (totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().get(LOB.INSURANCE)!=null) {
					commonResponseTransformer.buildInsuranceBreakup(priceDetails, totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().get(LOB.INSURANCE), totalPricingResponseOld.getPriceBreakdown().getPriceBreakupText());
				}
				if (totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().get(LOB.MBIZ_SUBSCRIPTION)!=null) {
					commonResponseTransformer.buildSMESubscriptionBreakup(priceDetails, totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().get(LOB.MBIZ_SUBSCRIPTION));
				}
    			totalPrice.setDetails(priceDetails);
				totalPrice.setPinCodeMandatory(totalPricingResponseOld.getPriceBreakdown().isPinCodeMandatory());

    			commonResponseTransformer.updateTotalAmountInHotelierCurrency(priceDetails
    					,totalPricingResponseOld.getPriceBreakdown().getPayMode(), totalPricingResponseOld.getPriceBreakdown().getCurrency()
    					, totalPricingResponseOld.getPriceBreakdown().getHotelierCurrency(), totalPricingResponseOld.getPriceBreakdown().getHotelierCurrencyConvFactor());
    		}
			String pahText = polyglotService.getTranslatedData(ConstantsTranslation.PAY_AT_HOTEL_POLICY_TEXT_GENERIC);

			if (totalPricingResponseOld.getPriceBreakdown().getPayMode() != null)
				Utility.updatePayAtHotelText(totalPrice, totalPricingResponseOld.getPriceBreakdown().getPayMode(), pahText, countryCode);
    		return totalPrice;
    	}
    	return null;
    }

	private void buildNoCostEmiDetailsForTotalPricing(com.mmt.hotels.model.response.pricing.TotalPricing priceBreakdown, TotalPriceResponse totalPriceResponse) {
		if (priceBreakdown != null && MapUtils.isNotEmpty(priceBreakdown.getLobWisePricing())
				&& priceBreakdown.getLobWisePricing().get(LOB.HOTEL) != null
				&& CollectionUtils.isNotEmpty(priceBreakdown.getLobWisePricing().get(LOB.HOTEL).getNoCostEmiDetailsList())) {
			DisplayPriceBreakDown displayPriceBreakDown = priceBreakdown.getLobWisePricing().get(LOB.HOTEL);
			BestCoupon coupon = displayPriceBreakDown.getCouponInfo();

			FullPayment fullPayment = commonResponseTransformer.buildNoCostEmiDetailAndUpdateFullPayment(displayPriceBreakDown.getNoCostEmiDetailsList(),
					totalPriceResponse.getTotalPricing(), totalPriceResponse.getFullPayment(), totalPriceResponse.getBnplDetails());

			//Update the fullPayment Node for Apps, now having no-cost emi messaging
			if (fullPayment != null) {
				totalPriceResponse.setFullPayment(utility.buildFullPayment(fullPayment));
			}
		}
	}

	private void updateTotalAmountIncludingAddon(Map<String, PricingDetails> priceDetails, double totalAmount, double flexiCancalCharges) {
		if (MapUtils.isNotEmpty(priceDetails) && priceDetails.containsKey(Constants.TOTAL_AMOUNT_KEY)) {
			if (priceDetails.containsKey(Constants.TCS_AMOUNT)) {
				priceDetails.get(Constants.TOTAL_AMOUNT_KEY).setAmount(totalAmount + priceDetails.get(Constants.TCS_AMOUNT).getAmount());
			} else {
				priceDetails.get(Constants.TOTAL_AMOUNT_KEY).setAmount(totalAmount + flexiCancalCharges);
			}
		}
	}

	private Map<String, PricingDetails> getPricingDetailKeyToPricingDetailObjectMap(List<PricingDetails> priceDetails) {
		if (CollectionUtils.isNotEmpty(priceDetails)) {
			return priceDetails.stream().collect(Collectors.toMap(PricingDetails::getKey, priceDetail -> priceDetail));
		}
		return null;
	}

    private List<PricingDetails> getAddOnPricing(TotalPricingResponse totalPricingResponseOld, String countryCode, String expData){
    	if(totalPricingResponseOld !=null && totalPricingResponseOld.getPriceBreakdown() !=null &&
    			MapUtils.isNotEmpty(totalPricingResponseOld.getPriceBreakdown().getLobWisePricing())) {
    		List<PricingDetails> details = new ArrayList<>();
    		for (Map.Entry<LOB, DisplayPriceBreakDown> entry : totalPricingResponseOld.getPriceBreakdown().getLobWisePricing().entrySet()) {
    			if (!LOB.HOTEL.name().equalsIgnoreCase(entry.getKey().name()) && !LOB.INSURANCE.name().equalsIgnoreCase(entry.getKey().name())
				&& !LOB.MBIZ_SUBSCRIPTION.name().equalsIgnoreCase(entry.getKey().name())) {
					details.addAll(commonResponseTransformer.getPricingDetails(entry.getValue(), countryCode, null, false, "", expData, false, utility.isExperimentTrue(utility.getExpDataMap(expData), MY_PARTNER_MOVE_TO_TDS_TAX_STRUCTURE), totalPricingResponseOld.getPriceBreakdown().getPriceBreakupText(), false,false, (entry.getValue()!=null && entry.getValue().getCouponInfo()!=null?entry.getValue().getCouponInfo().getExtraDiscountType():Constants.EMPTY_STRING), totalPricingResponseOld.isMetaTraffic(), true));
    			}
			}
    		return details;
    	}
    	return null;
    }

    private List<RatePlan> getRatePlanDetails(RoomTypeDetails roomTypeDetails, BNPLDetails bnplDetails,
											  Map<String, Integer> roomBedCount, Integer ap, boolean isUserGccAndMmtExclusive,
											  Map<String, String> expDataMap, boolean isAltAcco, boolean listingTypeEntire,
											  String propertyType, HotelRates hotelRates) {
        List<RatePlan> ratePlansList = new ArrayList<>();

		boolean bnplApplicable = (bnplDetails != null) && bnplDetails.isBnplApplicable();
        RatePolicy confirmationPolicy = commonResponseTransformer.getConfirmationPolicy(roomTypeDetails);
        String confirmationPolicyType = (confirmationPolicy != null) ? confirmationPolicy.getValue() : null;
        String baseRoomCode = null;
        if (roomTypeDetails.getRoomType().entrySet().stream().anyMatch((roomTypeDetailsEntry -> roomTypeDetailsEntry.getValue().isBaseRoom()))) {
            baseRoomCode = roomTypeDetails.getRoomType().entrySet().stream().filter(roomTypeDetailsEntry -> roomTypeDetailsEntry.getValue().isBaseRoom()).findFirst().get().getValue().getRoomTypeCode();
        }
        for (String roomCode : roomTypeDetails.getRoomType().keySet()) {
            RoomType roomType = roomTypeDetails.getRoomType().get(roomCode);
			String roomCategoryText = listingTypeEntire ? (propertyType!=null?propertyType:SELLABLE_ROOM_TYPE) : (roomType.getSellableType()!=null?roomType.getSellableType():SELLABLE_ROOM_TYPE);
			ratePlansList.addAll(getRatePlansList(roomType.getRatePlanList(), roomType.getRoomTypeName(), roomCode, bnplApplicable, confirmationPolicyType, roomType.getSellableType(), roomBedCount, ap, isUserGccAndMmtExclusive, expDataMap, StringUtils.equalsIgnoreCase(roomCode, baseRoomCode), isAltAcco, roomCategoryText,hotelRates, roomTypeDetails.getOccassionDetails()));
        }
        return ratePlansList;
    }

    private List<RatePlan> getRatePlansList(Map<String, com.mmt.hotels.model.response.pricing.RatePlan> ratePlanList,
											String roomName, String roomCode, boolean bnplApplicable,
											String confirmationPolicyType, String sellableType, Map<String, Integer> roomBedCount,
											Integer ap, boolean isUserGccAndMmtExclusive, Map<String, String> expDataMap,
											boolean baseRoom, boolean isAltAcco, String roomCategoryText,
											HotelRates hotelRates, OccassionDetails occassionDetails) {
        List<RatePlan> ratePlans = new ArrayList<>();
		boolean enableThemification = utility.isExperimentTrue(expDataMap, THEMIFICATION_ENABLED);
		int totalRooms = hotelRates.getRoomTypeDetails().getRoomType().size();
        for (String code : ratePlanList.keySet()) {
            RatePlan ratePlan = new RatePlan();
            com.mmt.hotels.model.response.pricing.RatePlan ratePlanCB = ratePlanList.get(code);
			ratePlan.setRoomName(roomName);
			ratePlan.setType(ratePlanCB.getRatePlanType());
			ratePlan.setCode(code);
			ratePlan.setRoomCode(roomCode);
			if(StringUtils.isNotEmpty(ratePlanCB.getMealDetailsCode())) {
				ratePlan.setMealDetailCode(ratePlanCB.getMealDetailsCode());
			}
			ratePlan.setVendorRatePlanCode(ratePlanCB.getRpcc());
			ratePlan.setSegmentId(ratePlanCB.getSegmentId());
			ratePlan.setDescription(ratePlanCB.getRatePlanDesc());
			ratePlan.setSuppliercode(ratePlanCB.getSupplierDetails() != null ? ratePlanCB.getSupplierDetails().getSupplierCode() : null);
			if(ratePlanCB.getCheckin() != null && StringUtils.isNotEmpty(ratePlanCB.getCheckin()) && ratePlanCB.getCheckout() != null && StringUtils.isNotEmpty(ratePlanCB.getCheckout())) {
				ratePlan.setCheckin(ratePlanCB.getCheckin());
				ratePlan.setCheckout(ratePlanCB.getCheckout());
			}
			
			// Set canTranslate flag based on supplier code configuration
			ratePlan.setCanTranslate(Utility.getCanTranslateFlag(translateEnabledSupplierCodes, ratePlan.getSuppliercode()));
			
			ratePlan.setCancellationTimeline(commonResponseTransformer.buildCancellationTimeline(ratePlanCB.getCancellationTimeline(), null));
			ratePlan.setCancellationPolicyTimeline(commonResponseTransformer.buildCancellationPolicyTimeline(ratePlanCB.getCancellationTimeline(), enableThemification, null));
			ratePlan.setRoomCategoryText(roomCategoryText);

			BNPLVariant bnplVariant = ratePlanCB.getBnplVariant();
			// HTL-42803:  TO-DO remove boolean isBnplOneVariant node once BNPLVariant Enum changes are completely live.
			boolean isBnplOneVariant = false;
			Boolean isPeakDate = null;
			if (MapUtils.isNotEmpty(expDataMap)) {
				isBnplOneVariant = expDataMap.containsKey(EXP_BNPL_NEW_VARIANT) && Boolean.parseBoolean(expDataMap.get(EXP_BNPL_NEW_VARIANT));
				isPeakDate = expDataMap.containsKey(EXP_BNPL_PEAK_DATE) && Boolean.parseBoolean(expDataMap.get(EXP_BNPL_PEAK_DATE));
			}
			String policyDateTime = null;
			if (ratePlanCB.getCancellationTimeline() != null) {
				policyDateTime = utility.concatenateDateAndTime(ratePlanCB.getCancellationTimeline().getCardChargeDate(), ratePlanCB.getCancellationTimeline().getCardChargeDateTime());
			}
			List<Integer> childAges = null;
			if (ratePlanCB.getAvailDetails() != null && ratePlanCB.getAvailDetails().getOccupancyDetails() != null) {
				childAges = ratePlanCB.getAvailDetails().getOccupancyDetails().getChildAges();
			}
			String freeChildText = ratePlanCB.getFreeChildCount() > 0 && StringUtils.isNotEmpty(ratePlanCB.getFreeChildText()) ? ratePlanCB.getFreeChildText() : null;
			String partialRefundText = utility.buildPartialRefundDateText(ratePlanCB.getCancellationTimeline());
			ratePlan.setCancellationPolicy(utility.transformCancellationPolicy(ratePlanCB.getCancelPenaltyList(), bnplApplicable , isBnplOneVariant, bnplVariant, confirmationPolicyType, polyglotService.getTranslatedData(ConstantsTranslation.CANCELLATION_POLICY_NR_NON_INSTANT_TEXT), ap, Optional.ofNullable(ratePlanCB.getMpFareHoldStatus()), partialRefundText, false));
			utility.updateCancellationPolicyText(utility.isFreeCancellation(ratePlanCB.getCancelPenaltyList()) && enableThemification, ratePlan);
			commonResponseTransformer.updateCancellationPolicyFCSubtext(ratePlan.getCancellationPolicy(), isPeakDate, policyDateTime, enableThemification, ratePlanCB.getCancellationTimeline()!=null?ratePlanCB.getCancellationTimeline().getPricerBnplMessage():null);
			commonResponseTransformer.updateCancelPolicyDescription(ratePlan, ratePlanCB.getCancelPenaltyList());
			boolean showExtraAdultChildInclusion = null != hotelRates && Boolean.TRUE.equals(hotelRates.getIsExtraAdultChild());
			ratePlan.setInclusionsList(utility.transformInclusions(
					ratePlanCB.getMealPlans(), ratePlanCB.getInclusions(), mealPlanMapPolyglot,
					ratePlanCB.getSupplierDetails() != null ? ratePlanCB.getSupplierDetails().getSupplierCode() : "",
					ap, ratePlanCB.getExtraGuestDetail(), expDataMap, isAltAcco, freeChildText,
					hotelRates.getHotelChainCode(), hotelRates.getHtlAttributes(), childAges, hotelRates.getCountryCode(),
					ratePlanCB.isMealAvailableAtProperty(), IconType.CROSS, false, showExtraAdultChildInclusion));
			ratePlan.setLosDiscountInclusion(utility.buildLosDiscountInclusion(ratePlanCB.getLosDiscountInclusion()));
			ratePlan.setExperienceInclusions(utility.createExperienceInclusionCard(ratePlanCB.getInclusions(), expDataMap));
			if (CollectionUtils.isNotEmpty(ratePlanCB.getMealPlans())) {
				ratePlan.setMealCode(ratePlanCB.getMealPlans().get(0).getCode());
			}
			ratePlan.setOccupancydetails(getOccupancyDetails(ratePlanCB.getAvailDetails()));
			ratePlan.setRoomTariff(getRoomTariffInfo(ratePlanCB.getRoomTariff(), roomBedCount, sellableType));
			ratePlan.setSellableType(sellableType);
			if(Objects.nonNull(occassionDetails) && StringUtils.isNotEmpty(occassionDetails.getPersuasionImageUrl())) {
				ratePlan.setHighlightImage(occassionDetails.getPersuasionImageUrl());
				ratePlan.setType(occassionDetails.getOccassionType());
			} else  if(ratePlanCB.getPackageRoomRatePlan()) {
				ratePlan.setHighlightImage(superPackageIconUrl);
			}

			// update alerts later
			ratePlan.setCorpRateTags(buildCorpRateTags(ratePlanCB.getCorpMetaData()));

			ratePlan.setRatePlanPersuasions(buildRatePlanPersuasion(ratePlanCB));
			if(ratePlanCB.getCheckout() != null && totalRooms>1) {
				PersuasionResponse persuasionResponse = commonResponseTransformer.buildRoomChangePersuasion(dateUtil.getDateFormatted(ratePlanCB.getCheckout(), DateUtil.YYYY_MM_DD, DateUtil.MMDDYYYY), totalRooms);
				if(ratePlan.getRatePlanPersuasions() != null){
					ratePlan.getRatePlanPersuasions().put(ROOM_CHANGE_DATE_KEY, persuasionResponse);
				} else {
					Map<String, PersuasionResponse> ratePlanPersuasions = new HashMap<>();
					ratePlanPersuasions.put(ROOM_CHANGE_DATE_KEY, persuasionResponse);
					ratePlan.setRatePlanPersuasions(ratePlanPersuasions);
				}
			}
			//Creating Node For MmtExclusive detail Page
			if(isUserGccAndMmtExclusive) {
				Map<String, MmtExclusive> card = new HashMap<>();
				card.put(Constants.GCC_EXCLUSIVE, utility.buildMmtExclusiveNode(ratePlanCB.getInclusions(), expDataMap));
				if(MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey("gcclpg")
						&& !expDataMap.get("gcclpg").equals("2") && !expDataMap.get("gcclpg").equals("3")){
					ratePlan.setCards(card);
				}
			}
				ratePlans.add(ratePlan);
			}

		if(baseRoom && CollectionUtils.isNotEmpty(ratePlans)) {
			ratePlans.get(0).setBasePlan(true);
		}
        return ratePlans;
    }

	private Map<String, PersuasionResponse> buildRatePlanPersuasion(com.mmt.hotels.model.response.pricing.RatePlan ratePlanCB)
	{

		Map<String, PersuasionResponse> ratePlanPersuasions = new HashMap<>();
		if(ratePlanCB!=null && ratePlanCB.getSupplierDetails()!=null && supplierToRateSegmentMapping!=null && supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY)!=null &&
				supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).get(ratePlanCB.getSupplierDetails().getSupplierCode())!=null
				&& (supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).get(ratePlanCB.getSupplierDetails().getSupplierCode()).isEmpty()
		|| supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).get(ratePlanCB.getSupplierDetails().getSupplierCode()).contains(ratePlanCB.getSegmentId()))) {

			ratePlanPersuasions.put(Constants.MYPARTNER_EXPEDIA_PKG_RATE,buildExpediaPackageRatePersuasion());
		}
		return ratePlanPersuasions.isEmpty() ? null:ratePlanPersuasions;
	}

	private PersuasionResponse buildExpediaPackageRatePersuasion(){

		PersuasionResponse persuasion = new PersuasionResponse();
		persuasion.setPersuasionText(polyglotService.getTranslatedData(ConstantsTranslation.PACKAGE_RATE_TEXT));
		persuasion.setId(Constants.MYPARTNER_EXPEDIA_PKG_RATE);
		return persuasion;
	}

	/**
	 * Sets the canTranslate flag based on supplier code configuration for AvailRoomsResponseTransformer
	 */
	private void setCanTranslateFlag(com.mmt.hotels.clientgateway.response.rooms.RatePlan ratePlan) {
		if (ratePlan == null) {
			return;
		}
		
		String supplierCode = ratePlan.getSuppliercode();
		if (CollectionUtils.isNotEmpty(translateEnabledSupplierCodes) && 
			StringUtils.isNotEmpty(supplierCode) &&
			translateEnabledSupplierCodes.contains(supplierCode)) {
			ratePlan.setCanTranslate(true);
		} else {
			ratePlan.setCanTranslate(false);
		}
	}

	private List<CorpRateTags> buildCorpRateTags(CorpMetaInfo corpMetaData) {
		if (corpMetaData!=null && CollectionUtils.isNotEmpty(corpMetaData.getTags())){
			List<CorpRateTags> corpRateTagsList = commonResponseTransformer.buildTags(corpMetaData.getTags());
			return corpRateTagsList;
		}
		return null;
	}


    private String getRatePlanPayMode(RoomTypeDetails roomTypeDetails) {
    	for (Map.Entry<String, RoomType> entry : roomTypeDetails.getRoomType().entrySet()) {
			List<com.mmt.hotels.model.response.pricing.RatePlan> ratePlanlist = entry.getValue().getRatePlanList().entrySet().stream().map(ratePlan -> ratePlan.getValue()).collect(Collectors.toList());
			if(CollectionUtils.isNotEmpty(ratePlanlist) && ratePlanlist.get(0).getPaymentDetails() !=null &&
					ratePlanlist.get(0).getPaymentDetails().getPaymentMode() !=null) {
				return ratePlanlist.get(0).getPaymentDetails().getPaymentMode().name();
			}
		}
    	return null;
    }

    private boolean childAgesSame(List<Integer> currentTariffChildAges, List<Integer> nextTariffChildAges) {
    	if(CollectionUtils.isEmpty(currentTariffChildAges) && CollectionUtils.isEmpty(nextTariffChildAges))
    		return true;
    	if(CollectionUtils.isEmpty(currentTariffChildAges) || CollectionUtils.isEmpty(nextTariffChildAges))
    		return false;
    	if(currentTariffChildAges.size() == nextTariffChildAges.size()) {
			Collections.sort(currentTariffChildAges);
			Collections.sort(nextTariffChildAges);
			int index = 0;
			for (; index < currentTariffChildAges.size(); index++) {
				if (!currentTariffChildAges.get(index).equals(nextTariffChildAges.get(index)))
					return false;
			}
			return index == currentTariffChildAges.size();
		}
    	return false;
    }

    private boolean isSameRoomTariff(com.mmt.hotels.model.response.pricing.RoomTariff currentTariff,
    		com.mmt.hotels.model.response.pricing.RoomTariff nextTariff) {
    	if(currentTariff !=null && nextTariff !=null) {
			return currentTariff.getNumberOfAdults() == nextTariff.getNumberOfAdults() &&
					currentTariff.getNumberOfChildren() == nextTariff.getNumberOfChildren() &&
					childAgesSame(currentTariff.getChildAges(), nextTariff.getChildAges());
		}
    	return false;
    }

    private List<RoomTariff> getRoomTariffInfo(List<com.mmt.hotels.model.response.pricing.RoomTariff> roomTariffs, Map<String, Integer> roomBedCount, String sellableType){
    	if(CollectionUtils.isNotEmpty(roomTariffs)) {
    		List<RoomTariff> roomTariffList = new ArrayList<>();
    		List<Integer> removeIndexes = new ArrayList<>();
    		for (com.mmt.hotels.model.response.pricing.RoomTariff roomTariff : roomTariffs) {
    			int index = roomTariffs.indexOf(roomTariff);
    			if(!removeIndexes.contains(index)) {
        			RoomTariff tariff = new RoomTariff();
        			tariff.setNumberOfAdults(roomTariff.getNumberOfAdults());
        			tariff.setNumberOfChildren(roomTariff.getNumberOfChildren());
        			tariff.setChildAges(roomTariff.getChildAges());
        			tariff.setRoomCount(1);
        			int nextIndex = index + 1;
    				while(nextIndex < roomTariffs.size() && !removeIndexes.contains(nextIndex)) {
    					com.mmt.hotels.model.response.pricing.RoomTariff nextRoomTariff = roomTariffs.get(nextIndex);
    					if(isSameRoomTariff(roomTariff, nextRoomTariff)) {
    						removeIndexes.add(nextIndex);
    						tariff.setRoomCount(tariff.getRoomCount() + 1);
    					}
    					nextIndex++;
    				}
					int count = tariff.getRoomCount();
					if(Constants.SELLABLE_BED_TYPE.equalsIgnoreCase(sellableType)){
						roomBedCount.put(Constants.SELLABLE_BED_TYPE, roomBedCount.get(Constants.SELLABLE_BED_TYPE)+count);
					}else{
						roomBedCount.put(Constants.SELLABLE_ROOM_TYPE, roomBedCount.get(Constants.SELLABLE_ROOM_TYPE)+count);
					}
    				roomTariffList.add(tariff);
    			}
			}
    		return roomTariffList;
    	}
    	return null;
    }

    private OccupancyDetail getOccupancyDetails(AvailDetails availDetails) {
        if (availDetails == null || availDetails.getOccupancyDetails() == null)
            return null;
        OccupancyDetail occupancyDetail = new OccupancyDetail();
        occupancyDetail.setNumOfRooms(availDetails.getNumOfRooms());
        occupancyDetail.setAdult(availDetails.getOccupancyDetails().getAdult());
        occupancyDetail.setChild(availDetails.getOccupancyDetails().getChild());
        return occupancyDetail;
    }

	private List<MealPlan> getMealPlanList(String mealCode) {
		if (StringUtils.isBlank(mealCode))
			return null;
		MealPlan mealPlan = new MealPlan();
		mealPlan.setCode(mealCode);
		return Collections.singletonList(mealPlan);
	}

	private List<UpsellOptions> buildUpsellOptions(HotelRates hotelRates) {
		if (hotelRates!=null && hotelRates.getRoomTypeDetails()!=null && MapUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getRoomType())) {
			Optional<RoomType> roomTypeEntry = hotelRates.getRoomTypeDetails().getRoomType().values().stream().findFirst();
			List<com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions> upsellOptionsList = new ArrayList<>();
			roomTypeEntry.ifPresent(roomType -> {
				List<com.mmt.hotels.model.response.pricing.RatePlan> list = roomType.getRatePlanList().values().stream().filter(e->e.getUpsellOptions()!=null).collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(list)) {
					list.stream().forEach(ratePlan -> {
						upsellOptionsList.addAll(makeUpsellOptions(ratePlan.getUpsellOptions()));
					});
				}
			});
			if (CollectionUtils.isNotEmpty(upsellOptionsList)) {
				reorderUpsellOptions(upsellOptionsList);
				return upsellOptionsList;
			}
		}
		return null;
	}

	private void reorderUpsellOptions(List<UpsellOptions> upsellOptionsList) {
		if (CollectionUtils.isNotEmpty(upsellOptionsList)) {
			Map<String, List<UpsellOptions>> groupedOptions = new LinkedHashMap<>();

			for (UpsellOptions option : upsellOptionsList) {
				if(option != null && StringUtils.isNotEmpty(option.getAddOnType())) {
					groupedOptions
							.computeIfAbsent(option.getAddOnType(), k -> new ArrayList<>())
							.add(option);
				}
			}

			upsellOptionsList.clear();
			for (List<UpsellOptions> options : groupedOptions.values()) {
				upsellOptionsList.addAll(options);
			}
		}
	}

	private List<com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions> makeUpsellOptions(List<com.mmt.hotels.model.response.pricing.UpsellOptions> list) {
		List<com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions> upsellOptionsList = new ArrayList<>();
		list.forEach(u -> {
			UpsellOptions upsellOptionsCG = new UpsellOptions();
			upsellOptionsCG.setAddOnType(u.getAddOnType());
			upsellOptionsCG.setUpsellType(u.getUpsellType());
			upsellOptionsCG.setFlexiCancellationDetails(buildFlexiCancellationDetails(u.getFlexiCancellationDetails()));
			upsellOptionsCG.setMtKey(u.getMtKey());
			upsellOptionsCG.setRoomCode(u.getRoomCode());
			upsellOptionsCG.setRatePlanCode(u.getRatePlanCode());
			upsellOptionsCG.setDisplayText(u.getDisplayText());
			upsellOptionsCG.setSuccessDisplayText(u.getSuccessDisplayText());
			upsellOptionsCG.setFailureDisplayText(u.getFailureDisplayText());
			upsellOptionsCG.setDisplaySubText(u.getDisplaySubText());
			//upsellOptionsCG.setDescriptionText(u.getDescriptionText());
			upsellOptionsCG.setRoomCriteria(buildRoomCriteria(u.getRoomCriteria()));
			upsellOptionsCG.setTrackingStr(u.getTrackingStr());
			upsellOptionsList.add(upsellOptionsCG);
		});
		return upsellOptionsList;
	}

	private FlexiCancellationDetails buildFlexiCancellationDetails(com.mmt.hotels.model.response.pricing.FlexiCancellationDetails flexiCancellationDetails) {
		FlexiCancellationDetails flexiCancellationDetailsResponse = null;
		if (flexiCancellationDetails != null) {
			flexiCancellationDetailsResponse = new FlexiCancellationDetails();
			BeanUtils.copyProperties(flexiCancellationDetails, flexiCancellationDetailsResponse);
		}
		return flexiCancellationDetailsResponse;
	}

	private List<AvailRoomsSearchCriteria> buildRoomCriteria(List<RoomCriterion> roomCriteria) {
		List<AvailRoomsSearchCriteria> roomsSearchCriteria = null;
		if (CollectionUtils.isNotEmpty(roomCriteria)) {
			roomsSearchCriteria = new ArrayList<>();
			for (RoomCriterion roomCriterionHES : roomCriteria) {
				AvailRoomsSearchCriteria criteria = new AvailRoomsSearchCriteria();
				criteria.setRatePlanCode(roomCriterionHES.getRatePlanCode());
				criteria.setRoomCode(roomCriterionHES.getRoomCode());
				criteria.setMtKey(roomCriterionHES.getMtKey());
				criteria.setRoomStayCandidates(getRoomStayCandidate(roomCriterionHES.getRoomStayCandidates()));
				roomsSearchCriteria.add(criteria);
			}
		}
		return roomsSearchCriteria;
	}

	private List<RoomStayCandidate> getRoomStayCandidate(List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidateHES) {
		List<RoomStayCandidate> roomStayCandidateList = null;
		if (CollectionUtils.isNotEmpty(roomStayCandidateHES)) {
			roomStayCandidateList = new ArrayList<>();
			for (com.mmt.hotels.model.request.RoomStayCandidate roomStayCandidateIter : roomStayCandidateHES) {
				RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
				roomStayCandidate.setAdultCount(roomStayCandidateIter.getAdultCount());
				roomStayCandidate.setChildAges(roomStayCandidateIter.getChildAges());
				roomStayCandidateList.add(roomStayCandidate);
			}
		}
		return roomStayCandidateList;
	}

	private DayUseInfo buildDayUseInfo(HotelRates hotelRates) {
		boolean allRoomsDayUse = true;
		String roomName = StringUtils.EMPTY;
		if (hotelRates.getRoomTypeDetails()!=null && MapUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getRoomType())) {
			for (RoomType roomType : hotelRates.getRoomTypeDetails().getRoomType().values()) {
				boolean isRoomDayUse = false;
				if (CollectionUtils.isNotEmpty(sameDayRoomNames) && StringUtils.isNotEmpty(roomType.getRoomTypeName())) {
					roomName = roomType.getRoomTypeName();
					isRoomDayUse = sameDayRoomNames.stream()
							.anyMatch(e -> roomType.getRoomTypeName().toLowerCase().contains(e.toLowerCase()));
				}
				allRoomsDayUse = allRoomsDayUse && isRoomDayUse;
			}
		}
		if (allRoomsDayUse) {
			DayUseInfo dayUseInfo = new DayUseInfo();
			dayUseInfo.setAlertText(polyglotService.getTranslatedData(ConstantsTranslation.DAY_USE_ROOM_ALERT_TEXT));
			dayUseInfo.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.DAY_USE_ROOM_LABEL));
			if (StringUtils.isNotBlank(roomName)) {
				Tuple<String,String> timmings = Utility.getCheckinAndCheckoutForDailyUse(roomName);
				if (timmings!=null) {
					dayUseInfo.setCheckinDate(timmings.getX());
					dayUseInfo.setCheckoutDate(timmings.getY());
				}
			}
			if (StringUtils.isBlank(dayUseInfo.getCheckinDate())||StringUtils.isBlank(dayUseInfo.getCheckoutDate())) {
				dayUseInfo.setCheckoutDate(polyglotService.getTranslatedData(ConstantsTranslation.DAY_USE));
				dayUseInfo.setCheckinDate(polyglotService.getTranslatedData(ConstantsTranslation.DAY_USE));
			}
			return dayUseInfo;
		}
		return null;
	}

	private CorpData buildCorpData(boolean corpCapturePersonalBookingGstn) {
		CorpData corpData = new CorpData();
		corpData.setCorpCapturePersonalBookingGstn(corpCapturePersonalBookingGstn);
		return corpData;
	}

	public PayLaterEligibilityResponse convertPayLaterEligibilityResponse(String client, com.mmt.hotels.model.response.PayLaterEligibilityResponse hesResponse,boolean isMemoize) {
		PayLaterEligibilityResponse response = new PayLaterEligibilityResponse();
		PayLaterCard card = new PayLaterCard();
		if(isMemoize && hesResponse!=null && !hesResponse.isEligible())
		{
			response.setEligible(hesResponse.isEligible());
			response.setPayLaterCard(buildPayLaterCard());
			response.setCheckEligibility(true);
			return response;
		}
		String titleIcon = tripMoneyIconApps;
		String failureLogo = tripMoneyWhiteLogoApps;
		String successLogo = tripMoneyDarkLogoApps;
		if(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client)){
			titleIcon = tripMoneyIconWeb;
			failureLogo = tripMoneyWhiteLogoWeb;
			successLogo = tripMoneyDarkLogoWeb;
		}
		if (hesResponse.isEligible()) {
			card.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_SUCCESS_TITLE));
			card.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_SUCCESS_SUBTITLE));
			card.setDesc(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_DESCRIPTION));
			card.setSuccessCard(buildSuccessCard(Utility.round(hesResponse.getAmount(),0), successLogo));
		} else {
			card.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_FAILURE_TITLE));
			card.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_FAILURE_SUBTITLE));
			card.setTitleIcon(titleIcon);
			card.setLogo(failureLogo);
		}
		response.setEligible(hesResponse.isEligible());
		response.setPayLaterCard(card);
		response.setCurrency(hesResponse.getCurrency());
		return response;
	}

	private PayLaterSuccessCard buildSuccessCard(double amount, String successLogo) {
		PayLaterSuccessCard card = new PayLaterSuccessCard();
		card.setAmount(amount);
		card.setCurrency(Constants.DEFAULT_CUR_INR);
		card.setLogo(successLogo);
		card.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_SUCCESS_CARD_TITLE));
		return card;
	}

	/**
	 * Build instant fare information required to be shown in review page in case of negotiated rate hotels flow.
	 *
	 * @param deepLink Details deeplink.
	 * @return {@link InstantFareInfo} object.
	 */
	private InstantFareInfo getInstantFareInfo(String deepLink) {
		InstantFareInfo instantFareInfo = new InstantFareInfo();
		instantFareInfo.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.INSTANT_BOOKING_CONFIRMATION));
		Cta cta = new Cta();
		if (ANDROID.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) || DEVICE_IOS.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
			cta.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.CHANGE_ROOM_MOBILE));
		} else {
			cta.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.CHANGE_ROOM_DESKTOP));
		}
		cta.setDeeplink(deepLink);
		instantFareInfo.setCta(cta);
		return instantFareInfo;
	}

	public FetchLocationsResponse convertFetchLocationsResponse(FetchLocationsResponseBody hesResponse) {
		if(hesResponse == null)
			return null;
		FetchLocationsResponse response = new FetchLocationsResponse();
		response.setRequestId(hesResponse.getCorrelationKey());
		List<LocationDetail> locations = hesResponse.getResult().stream().map(result -> {
			LocationDetail locationDetail = new LocationDetail();
			locationDetail.setName(result.getName());
			locationDetail.setId(result.getLocId());
			locationDetail.setPostalCode(result.getPostalCode());
			return locationDetail;
		}).collect(Collectors.toList());
		response.setLocations(locations);
		return response;
	}

	private DigilockerOptInInfo buildDigilockerOptInInfo() {
		DigilockerOptInInfo digilockerOptInInfo = new DigilockerOptInInfo();
		digilockerOptInInfo.setText(polyglotService.getTranslatedData(ConstantsTranslation.DIGILOCKER_OPT_IN_TEXT));
		digilockerOptInInfo.setBgColor(Constants.EAF5FF);
		return digilockerOptInInfo;
	}

	protected void setBankOffer(Coupon coupon, boolean bankOffer, boolean showReviewOfferCategory) {
		coupon.setBankOffer(bankOffer);
	}
}