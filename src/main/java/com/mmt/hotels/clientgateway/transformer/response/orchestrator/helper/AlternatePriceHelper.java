package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.response.DateRange;
import com.mmt.hotels.clientgateway.response.PriceCardDetail;
import com.mmt.hotels.clientgateway.response.SubTextData;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static com.mmt.hotels.clientgateway.constants.Constants.CHEAPER_BY;
import static com.mmt.hotels.clientgateway.constants.Constants.CHEAPEST_PRICE;
import static com.mmt.hotels.clientgateway.constants.Constants.EXPENSIVE_BY;
import static com.mmt.hotels.clientgateway.constants.Constants.SELECTED_PRICE;

@Component
public class AlternatePriceHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(AlternatePriceHelper.class);

    @Autowired
    DateUtil dateUtil;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    public void buildAlternatePriceCard(com.gommt.hotels.orchestrator.detail.model.response.HotelDetails hotelDetails,
                                         SearchRoomsResponse searchRoomsResponse,
                                         SearchRoomsRequest searchRoomsRequest) {
        com.mmt.hotels.clientgateway.response.AlternatePriceCard alternatePriceCard = new com.mmt.hotels.clientgateway.response.AlternatePriceCard();
        try {
            SearchRoomsCriteria searchRoomsCriteria = searchRoomsRequest.getSearchCriteria();

            // Set configuration-based properties (similar to old transformer)
            alternatePriceCard.setHeading(commonConfigConsul.getPriceWidgetHeadline());
            alternatePriceCard.setSubheading(commonConfigConsul.getPriceWidgetSubHeadline());
            alternatePriceCard.setNewFeatureTag(commonConfigConsul.getPriceWidgetNewFeatureTag());
            List<PriceCardDetail> priceCardDetails = new ArrayList<>();
            alternatePriceCard.setData(priceCardDetails);

            // Find the selected price card for delta calculations
            com.gommt.hotels.orchestrator.detail.model.response.pricing.AlternatePriceCard priceCardSelected = hotelDetails.getAlternateDatePriceDetails()
                    .stream()
                    .filter(com.gommt.hotels.orchestrator.detail.model.response.pricing.AlternatePriceCard::isSelected)
                    .findFirst()
                    .orElse(null);

            for (com.gommt.hotels.orchestrator.detail.model.response.pricing.AlternatePriceCard priceCard : hotelDetails.getAlternateDatePriceDetails()) {
                PriceCardDetail priceCardDetail = new PriceCardDetail();

                // Set formatted text using dateUtil (similar to old transformer)
                priceCardDetail.setText(dateUtil.concatDate(priceCard.getCheckIn(), priceCard.getCheckOut()));
                priceCardDetail.setPrice(priceCard.getPrice());
                priceCardDetail.setCheaper(priceCard.isCheaper());
                priceCardDetail.setSelected(priceCard.isSelected());
                priceCardDetail.setCurrency(priceCard.getCurrency());

                // Calculate weekend, next day, and same day of week flags (similar to old transformer)
                int los = dateUtil.getDaysDiff(searchRoomsRequest.getSearchCriteria().getCheckIn(), searchRoomsRequest.getSearchCriteria().getCheckOut());
                boolean isWeekend = isWeekendRateAvailable(priceCard, searchRoomsCriteria, los);
                boolean isNextDay = isNextDayRateAvailable(priceCard, searchRoomsCriteria, los);
                boolean isSameDayOfWeek = isSearchDateDaysIsSameAsAlternateDateDays(priceCard, searchRoomsCriteria);

                priceCardDetail.setComingWeekend(isWeekend);
                priceCardDetail.setNextDay(isNextDay);
                if (priceCard.isSelected()) {
                    priceCardDetail.setSameDayOfWeek(false);
                } else {
                    priceCardDetail.setSameDayOfWeek(isSameDayOfWeek);
                }

                // Set date range
                DateRange dateRange = new DateRange();
                dateRange.setCheckIn(priceCard.getCheckIn());
                dateRange.setCheckOut(priceCard.getCheckOut());
                priceCardDetail.setDateRange(dateRange);

                // Build SubTextData with delta calculations (similar to old transformer)
                SubTextData subTextData = new SubTextData();
                double delta = priceCardSelected != null ? priceCard.getDelta() / priceCardSelected.getPrice() : 1.0;
                if (priceCard.getDelta() == 0) {
                    priceCardDetail.setSamePrice(true);
                } else {
                    priceCardDetail.setSamePrice(false);
                }
                if (priceCard.getDelta() > 0 && delta > 0.03) {
                    subTextData.setAmount(priceCard.getDelta());
                    subTextData.setText(priceCard.isCheaper() ? CHEAPER_BY : EXPENSIVE_BY);
                }
                if (priceCard.isSelected()) {
                    subTextData.setText(priceCard.isCheaper() ? CHEAPEST_PRICE : SELECTED_PRICE);
                    priceCardDetail.setHoverText(commonConfigConsul.getPriceWidgetHoverHtmlForSelected());
                } else {
                    priceCardDetail.setHoverText(commonConfigConsul.getPriceWidgetHoverHtml());
                }
                priceCardDetail.setSubTextData(subTextData);
                priceCardDetails.add(priceCardDetail);
            }
            searchRoomsResponse.setAlternatePriceCard(alternatePriceCard);
        } catch (Exception exp) {
            LOGGER.error("error while building alternate price card", exp);
        }
    }

    // Helper methods adapted from old transformer for OrchV2 data structures
    private boolean isSearchDateDaysIsSameAsAlternateDateDays(com.gommt.hotels.orchestrator.detail.model.response.pricing.AlternatePriceCard priceCard, SearchRoomsCriteria searchRoomsCriteria) {
        String checkInAlternateDay = dateUtil.dayOfWeek(priceCard.getCheckIn());
        String checkOutAlternateDay = dateUtil.dayOfWeek(priceCard.getCheckOut());

        String checkInSearchedDay = dateUtil.dayOfWeek(searchRoomsCriteria.getCheckIn());
        String checkOutSearchedDay = dateUtil.dayOfWeek(searchRoomsCriteria.getCheckOut());

        return dateUtil.isSameDay(checkInAlternateDay, checkInSearchedDay) && dateUtil.isSameDay(checkOutAlternateDay, checkOutSearchedDay);
    }

    private boolean isWeekendRateAvailable(com.gommt.hotels.orchestrator.detail.model.response.pricing.AlternatePriceCard priceCard, SearchRoomsCriteria searchRoomsCriteria, int los) {
        LocalDate checkInDate = dateUtil.getLocalDate(priceCard.getCheckIn(), DateUtil.YYYY_MM_DD);
        LocalDate checkOutDate = dateUtil.getLocalDate(priceCard.getCheckOut(), DateUtil.YYYY_MM_DD);
        LocalDate searchCheckInDate = dateUtil.getLocalDate(searchRoomsCriteria.getCheckIn(), DateUtil.YYYY_MM_DD);

        return los == 1 && checkInDate != null && searchCheckInDate != null && checkOutDate != null
                && checkInDate.isAfter(searchCheckInDate) && checkInDate.getDayOfWeek() == DayOfWeek.SATURDAY && checkOutDate.getDayOfWeek() == DayOfWeek.SUNDAY;
    }

    private boolean isNextDayRateAvailable(com.gommt.hotels.orchestrator.detail.model.response.pricing.AlternatePriceCard priceCard, SearchRoomsCriteria searchRoomsCriteria, int los) {
        LocalDate checkInSearchedDate = dateUtil.getLocalDate(searchRoomsCriteria.getCheckIn(), DateUtil.YYYY_MM_DD);
        LocalDate checkInAlternateDate = dateUtil.getLocalDate(priceCard.getCheckIn(), DateUtil.YYYY_MM_DD);
        
        if (checkInSearchedDate == null || checkInAlternateDate == null) {
            return false;
        }
        
        LocalDate added = checkInSearchedDate.plusDays(1);
        return los == 1 && checkInAlternateDate.equals(added);
    }
}
