package com.mmt.hotels.clientgateway.transformer.response.cardEngine;

import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.searchHotels.SearchHotelsResponse;
import org.springframework.stereotype.Component;

@Component
public class CardEngineResponseTransformer {

    public String transformCardEngineResponse(String cardEngineResponse){
        return cardEngineResponse;
    }

    public SearchHotelsResponse updateSearchHotelsResponse(SearchHotelsResponse searchHotelsResponse, String cardEngineResponse, SearchHotelsRequest searchHotelsRequest){
        return searchHotelsResponse;
    }
}
