package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.response.Action;
import com.mmt.hotels.clientgateway.response.filter.Filter;
import com.mmt.hotels.clientgateway.response.OverviewCard;
import com.mmt.hotels.clientgateway.response.SummaryCard;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.response.CardsData;
import com.mmt.hotels.clientgateway.response.TravelTipResponse;
import com.mmt.hotels.clientgateway.response.TravelTipWrapperResponse;

import java.util.ArrayList;
import java.util.List;

public abstract class TravelTipResponseTransformer {

    public TravelTipResponse convertTravelTipResponse(TravelTipWrapperResponse travelTipWrapperResponse, String client) {
        TravelTipResponse travelTipResponse = new TravelTipResponse();

        if (travelTipWrapperResponse != null && travelTipWrapperResponse.getName() != null) {
            travelTipResponse.setHeader(travelTipWrapperResponse.getName());
        }
        travelTipResponse.setSubHeader(Constants.TRAVEL_TIPS_SUB_HEADER);

        if (travelTipWrapperResponse.getBgImg() != null) {
            travelTipResponse.setBgImg(travelTipWrapperResponse.getBgImg());
        }
        if (travelTipWrapperResponse.getCardsData() != null) {
            CardsData cardsData = new CardsData();
            cardsData.setOverviewCards(convertOverviewCards(travelTipWrapperResponse.getCardsData().getOverviewCards()));
            cardsData.setSummaryCards(convertSummaryCards(travelTipWrapperResponse.getCardsData().getSummaryCards()));
            travelTipResponse.setCardsData(cardsData);
        }

        return travelTipResponse;
    }

    public List<OverviewCard> convertOverviewCards(List<OverviewCard> overviewCardsFromWrapper) {
        List<OverviewCard> overviewCards = new ArrayList<>();
        if (overviewCardsFromWrapper != null) {
            for (OverviewCard overviewCardFromWrapper : overviewCardsFromWrapper) {
                OverviewCard overviewCard = new OverviewCard();
                overviewCard.setIconUrl(overviewCardFromWrapper.getIconUrl());
                overviewCard.setTitle(overviewCardFromWrapper.getTitle());
                overviewCard.setSubtitle(overviewCardFromWrapper.getSubtitle());
                overviewCards.add(overviewCard);
            }
        }
        return overviewCards;
    }

    public List<SummaryCard> convertSummaryCards(List<SummaryCard> summaryCardsFromWrapper) {
        List<SummaryCard> summaryCards = new ArrayList<>();
        if (summaryCardsFromWrapper != null) {
            for (SummaryCard summaryCardFromWrapper : summaryCardsFromWrapper) {
                SummaryCard summaryCard = new SummaryCard();
                summaryCard.setTitle(summaryCardFromWrapper.getTitle());
                summaryCard.setHighlights(summaryCardFromWrapper.getHighlights());
                summaryCard.setActions(convertActions(summaryCardFromWrapper.getActions()));
                summaryCards.add(summaryCard);
            }
        }
        return summaryCards;
    }

    public List<Action> convertActions(List<Action> actionsFromWrapper) {
        List<Action> actions = new ArrayList<>();
        if (actionsFromWrapper != null) {
            for (Action actionFromWrapper : actionsFromWrapper) {
                if (actionFromWrapper.getPersuasionText() != null && actionFromWrapper.getFilters() != null) {
                    Action action = new Action();
                    action.setText(actionFromWrapper.getPersuasionText());
                    action.setFilters(convertFilters(actionFromWrapper.getFilters()));
                    actions.add(action);
                }
            }
        }
        return actions;
    }

    public List<Filter> convertFilters(List<Filter> filtersFromWrapper) {
        List<Filter> filters = new ArrayList<>();
        if (filtersFromWrapper == null) {
            return filters;
        }
        for (Filter filterFromWrapper : filtersFromWrapper) {
            if (Constants.FILTER_CATEGORY_TYPE.equals(filterFromWrapper.getType()) && filterFromWrapper.getSub_filter_category()!= null) {
                filters.addAll(createFiltersForSubCategories(filterFromWrapper));
            } else {
                filters.add(createFilter(filterFromWrapper));
            }
        }
        return filters;
    }

    private List<Filter> createFiltersForSubCategories(Filter filterFromWrapper) {
        List<Filter> filters = new ArrayList<>();
        for (String subFilterCategory : filterFromWrapper.getSub_filter_category()) {
            Filter filter = createFilter(filterFromWrapper);
            filter.setFilterValue(subFilterCategory);
            filters.add(filter);
        }
        return filters;
    }

    private Filter createFilter(Filter filterFromWrapper) {
        Filter filter = new Filter();
        filter.setTitle(filterFromWrapper.getTitle());
        filter.setFilterGroup(filterFromWrapper.getFilter_category() != null ? filterFromWrapper.getFilter_category() : Constants.LOCATION_FILTER_GROUP);
        filter.setFilterValue(filterFromWrapper.getId());
        if(filterFromWrapper.getType() != null && !Constants.FILTER_CATEGORY_TYPE.equals(filterFromWrapper.getType())) {
            filter.setMatchmakerType(filterFromWrapper.getType());
        }
        return filter;
    }
}