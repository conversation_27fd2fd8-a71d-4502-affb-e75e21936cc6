package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.gommt.hotels.orchestrator.detail.enums.PriceVariationType;
import com.gommt.hotels.orchestrator.detail.enums.RatingCategory;
import com.gommt.hotels.orchestrator.detail.enums.RoomType;
import com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetails;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetailsResponse;
import com.gommt.hotels.orchestrator.detail.model.response.content.Media;
import com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CategoryInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.houserules.HouseRules;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo;
import com.gommt.hotels.orchestrator.detail.model.response.corp.CorpMetaInfo;
import com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails;
import com.gommt.hotels.orchestrator.detail.model.response.da.PriceVariation;
import com.gommt.hotels.orchestrator.detail.model.response.personalizedcards.CardData;
import com.gommt.hotels.orchestrator.detail.model.response.persuasion.BenefitType;
import com.gommt.hotels.orchestrator.detail.model.response.persuasion.Benefits;
import com.gommt.hotels.orchestrator.detail.model.response.persuasion.PackageDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.AllInclusiveData;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.ComboType;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.HotelRateFlags;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.MarkUpDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.MealPlan;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.NoCostEmiDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.PaymentPlan;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.RoomCombo;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.Rooms;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.AdditionalChargesBO;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.constants.TrafficSourceConstants;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.LinkedRatePlanStyle;
import com.mmt.hotels.clientgateway.consul.properties.ExtraAdultChildInclusionConfig;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.response.AdditionalMandatoryCharges;
import com.mmt.hotels.clientgateway.response.BlackInfo;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicyType;
import com.mmt.hotels.clientgateway.response.BookedInclusion;
import com.mmt.hotels.clientgateway.response.CalendarAvailabilityResponse;
import com.mmt.hotels.clientgateway.response.CalendarBO;
import com.mmt.hotels.clientgateway.response.Card;
import com.mmt.hotels.clientgateway.response.Cta;
import com.mmt.hotels.clientgateway.response.DateRange;
import com.mmt.hotels.clientgateway.response.FiltersData;
import com.mmt.hotels.clientgateway.response.IconType;
import com.mmt.hotels.clientgateway.response.InstantFareInfo;
import com.mmt.hotels.clientgateway.response.OfferDetail;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.PriceCardDetail;
import com.mmt.hotels.clientgateway.response.PricingDetails;
import com.mmt.hotels.clientgateway.response.ResponseContextDetail;
import com.mmt.hotels.clientgateway.response.Style;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.TrailingCtaBottomSheet;
import com.mmt.hotels.clientgateway.response.availrooms.FeatureFlags;
import com.mmt.hotels.clientgateway.response.corporate.CorpApprovalInfo;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUsePersuasion;
import com.mmt.hotels.clientgateway.response.emi.CouponCardConfig;
import com.mmt.hotels.clientgateway.response.moblanding.CardAction;
import com.mmt.hotels.clientgateway.response.moblanding.CardInfo;
import com.mmt.hotels.clientgateway.response.moblanding.SupportDetails;
import com.mmt.hotels.clientgateway.response.rooms.AddOnDetails;
import com.mmt.hotels.clientgateway.response.rooms.BgStyle;
import com.mmt.hotels.clientgateway.response.rooms.ExtraBedPolicy;
import com.mmt.hotels.clientgateway.response.rooms.FilterDetailsClient;
import com.mmt.hotels.clientgateway.response.rooms.LoginPersuasion;
import com.mmt.hotels.clientgateway.response.rooms.PackageRoomDetails;
import com.mmt.hotels.clientgateway.response.rooms.PolicyDetails;
import com.mmt.hotels.clientgateway.response.rooms.PriceGraphInfo;
import com.mmt.hotels.clientgateway.response.rooms.RecommendedCombo;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.response.rooms.RoomTariff;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomRatePlan;
import com.mmt.hotels.clientgateway.response.rooms.SleepingArrangementRoomInfo;
import com.mmt.hotels.clientgateway.response.rooms.StayTypeInfo;
import com.mmt.hotels.clientgateway.response.rooms.Tariff;
import com.mmt.hotels.clientgateway.response.rooms.TariffViewType;
import com.mmt.hotels.clientgateway.response.searchHotels.BgGradient;
import com.mmt.hotels.clientgateway.response.searchHotels.BorderGradient;
import com.mmt.hotels.clientgateway.response.searchHotels.SectionFeature;
import com.mmt.hotels.clientgateway.response.staticdetail.AllInclusiveCard;
import com.mmt.hotels.clientgateway.response.staticdetail.ExperienceInclusions;
import com.mmt.hotels.clientgateway.response.staticdetail.PrimaryOffer;
import com.mmt.hotels.clientgateway.response.wrapper.LongStayBenefits;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.SearchRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.AddOnHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.AlternatePriceHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.CancellationPolicyHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.DeepLinkHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.RoomAmentiesHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.RoomInfoHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.SearchRoomsBannerHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.SearchRoomsFilter;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.SearchRoomsMediaHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.SearchRoomsPersuasionHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.SearchRoomsPriceHelper;
import com.mmt.hotels.clientgateway.util.Currency;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.DayUseUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.ReArchUtility;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.dayuse.MissingSlotDetail;
import com.mmt.hotels.model.response.persuasion.HotelCloudData;
import com.mmt.hotels.model.response.pricing.AdditionalFees;
import com.mmt.hotels.model.response.pricing.AdditionalFeesPrice;
import com.mmt.hotels.model.response.pricing.FlexiCancelAddOnDetails;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.SupplierDetails;
import com.mmt.hotels.model.response.staticdata.AllInclusiveInclusion;
import com.mmt.hotels.model.response.staticdata.RuleInfo;
import com.mmt.hotels.model.response.staticdata.RuleTableInfo;
import com.mmt.hotels.pojo.listing.personalization.BGLinearGradient;
import com.mmt.model.UGCRatingData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.COUPON_CARD_CONFIG_SUBHEADING;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.CP_MEAL_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.DEFAULT_PROPERTY_LAYOUT_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.HOTEL_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.INSTANT_BOOKING;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.INSTANT_BOOKING_FARES;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.INSTANT_FARE_INFO_HEADER;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.INSTANT_FARE_INFO_SUBHEADER;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.INSTANT_FARE_INFO_TITLE_MOBILE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.KNOW_MORE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.MULTIPLE_ENTIRE_PROPERTY_LAYOUT_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.PAYMENT_PLAN_PENALTY_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.PRICE_GRAPH_DROP_SUBTITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.PRICE_GRAPH_DROP_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.PRICE_GRAPH_ICON_SUBTITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.PRICE_GRAPH_ICON_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.PRICE_GRAPH_SURGE_SUBTITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.PRICE_GRAPH_SURGE_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.PRICE_GRAPH_TYPICAL_SUBTITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.PRICE_GRAPH_TYPICAL_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.PROCEED_BOOKING_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.RESORT_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.ROOM_SELLABLE_TYPE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SINGLE_ENTIRE_PROPERTY_LAYOUT_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.STAY_SELLABLE_TYPE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SUPPLIER;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.DETAIL_SEARCH_ROOMS;
import static com.mmt.hotels.clientgateway.enums.ExperimentKeys.APPLY_FILTER_TO_COMBO;

@Component
public abstract class OrchSearchRoomsResponseTransformer {
    // Package room constants
    private static final String DIAGONAL_BOTTOM = "diagonalBottom";

    private static final Logger LOGGER = LoggerFactory.getLogger(SearchRoomsResponseTransformer.class);

    @Autowired
    private CommonResponseTransformer commonResponseTransformer;

    @Autowired
    private SearchRoomsPriceHelper searchRoomsPriceHelper;

    @Autowired
    protected PersuasionUtil persuasionUtil;

    @Autowired
    private Utility utility;

    @Autowired
    private ReArchUtility reArchUtility;

    @Autowired
    private SearchRoomsFilter searchRoomsFilter;

    @Autowired
    private CancellationPolicyHelper cancellationPolicyHelper;

    @Autowired
    private SearchRoomsPersuasionHelper searchRoomsPersuasionHelper;

    @Autowired
    private DateUtil dateUtil;

    @Autowired
    private DayUseUtil dayUseUtil;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    @Autowired
    DeepLinkHelper deepLinkHelper;

    @Value("${pah.without.cc.text}")
    private String pahWithoutCCText;

    @Value("${pah.with.cc.text}")
    private String pahWithCCText;

    @Value("${pah.gcc.text}")
    private String pahGccText;

    @Value("${super.package.icon.url}")
    private String superPackageIconUrl;

    @Value("${super.package.icon.url.secondary}")
    private String superPackageIconUrlSecondary;

    @Value("${default.search.room.url}")
    private String defaultSearchRoomUrl;

    @Value("${red.cross.Icon}")
    private String redCrossIcon;

    @Value("${free.kids.inclusion.icon.url}")
    private String freeChildInclusionIcon;

    @Value("${negotiated.rates.delayed.confirmation.no.of.hours}")
    protected int noOfHoursForConfirmation;


    @Value("${corp.one.on.one.segmentId}")
    private String corpPreferredRateSegmentId;

    @Value("${negotiated.rate.icon.url}")
    private String negotiatedRateIconUrl;

    @Value("${negotiated.rate.icon.url.newApp}")
    private String negotiatedRateIconUrlNewApp;

    @Value("${flyer.persuasion.color.detail}")
    private String flyerPersuasionColorDetail;

    @Value("${vistara.persuasion.color.detail}")
    private String vistaraPersuasionColorDetail;

    @Value("${bus.persuasion.color.detail}")
    private String busPersuasionColorDetail;

    @Value("${train.persuasion.color.detail}")
    private String trainPersuasionColorDetail;

    @Value("${flyer.persuasion.image.url.detail}")
    private String flyerPersuasionImageUrlDetail;

    @Value("${vistara.persuasion.image.url.detail}")
    private String vistaraPersuasionImageUrlDetail;

    @Value("${vistara.persuasion.image.url.detail.dt}")
    private String vistaraPersuasionImageUrlDetailDT;

    @Value("${bus.persuasion.image.url.detail}")
    private String busPersuasionImageUrlDetail;

    @Value("${train.persuasion.image.url.detail}")
    private String trainPersuasionImageUrlDetail;

    @Value("${flyer.gcc.persuasion.image.url.detail}")
    private String flyerGccPersuasionImageUrlDetail;

    @Value("${flyer.persuasion.image.url.detail.dt}")
    private String flyerPersuasionImageUrlDetailDT;

    @Value("${bus.persuasion.image.url.detail.dt}")
    private String busPersuasionImageUrlDetailDT;

    @Value("${train.persuasion.image.url.detail.dt}")
    private String trainPersuasionImageUrlDetailDT;

    @Value("${early.bird.icon.url}")
    private String earlyBirdIconUrl;

    @Value("${payment.plan.icon.url}")
    private String paymentPlanIcon;

    @Value("${last.minute.icon.url}")
    private String lastMinuteIconUrl;

    private String noCostEmiIconUrl;
    private BgGradient noCostEmiIconConfig;

    @Value("${supplier.deals.bg.color}")
    private String supplierBgColor;

    @Value("#{'${combo.title.meal.plan.code}'.split(',')}")
    private List<String> mealPlanCodeList;

    @Value("${single.tick.url}")
    private String singleTickUrl;

    @Value("${long.stay.gcc.nudge.iconUrl}")
    private String longStayGccNudgeIconUrl;

    @Value("${extra.guest.free.child.color}")
    private String extraGuestFreeChildColor;

    @Value("${high.demand.persuasion.color}")
    private String highDemandPersuasionColor;

    @Value("${dot.icon.url}")
    private String dotIconUrl;

    @Value("${black.icon.details.page.url}")
    private String blackIconDetailsPage;

    @Value("${addon.info.most.popular.tag}")
    private String addOnInfoMostPopularTagConfig;

    @Value("${food.rating.thresold}")
    private int foodRatingThresold;

    @Value("${call.to.book.iconUrl}")
    private String callToBookIconUrl;

    @Value("${call.to.book.title}")
    private String callToBookTitle;

    @Value("${call.to.book.option}")
    private String callToBookOption;

    //TODO need to update this
    @Value("${black.revamp.fallback.bullet.icon}")
    private String stayInfoIcon;

    @Value("${los.icon.url.room}")
    private String losIconUrl;

    @Value("${los.position.select.room}")
    private int losPositionSelectRoom;

    @Value("${bathroom.stay.info.icon}")
    private String bathroomInfoIcon;

    @Value("${kitchen.stay.info.icon}")
    private String kitchenStayInfoIcon;

    @Value("${livingroom.stay.info.icon}")
    private String livingroomStayInfoIcon;

    @Value("${bedroom.stay.info.icon}")
    private String bedroomStayInfoIcon;

    @Value("${price.graph.text.icon}")
    private String priceGraphTextIcon;

    @Value("${price.graph.icon}")
    private String priceGraphIcon;

    @Value("${price.graph.recommended.icon}")
    private String priceGraphRecommendedIcon;

    private List<String> translateEnabledSupplierCodes = new ArrayList<>();

    private int apLimitForInclusionIcons = 2;

    private int losFosGCCNudgePersuasion;

    private int ratePlanMoreOptionsLimit = 1;
    private Map<String, Map<String, List<String>>> supplierToRateSegmentMapping;

    @Autowired
    protected PolyglotService polyglotService;

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Value("${searchrooms.rateplan.name.config}")
    private String ratePlanNameConfigProperty;

    @Value("${searchrooms.rateplan.redesign}")
    private String ratePlanNameConfigRedesign;

    @Autowired
    private SearchRoomsMediaHelper searchRoomsMediaHelper;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private AlternatePriceHelper alternatePriceHelper;

    private static final Gson gson = new Gson();

    private Map<String, Map<String, Map<String, Integer>>> ratePlanDisplayLogic;
    Map<String, String> rtbCardConfigs;

    String mandatoryChargesAlert;

    AllInclusiveCard allInclusiveCard;

    private MissingSlotDetail missingSlotDetails = null;

    private Map<String, DayUsePersuasion> dayUseFunnelPersuasions = null;

    private int thresholdForSlashedAndDefaultHourPrice = 0;
    private String recommendedRoomPropertiesConfig;
    private Map<String, Map<String, String>> recommendedRoomPropertiesMap;

    @Autowired
    private MobConfigHelper mobConfigHelper;

    private Map<String, StayTypeInfo> actionInfoMap = new HashMap<>();

    private Map<String, String> addOnInfoMostPopularTag;

    private LinkedRatePlanStyle linkedRatePlanStyle;

    private ExtraAdultChildInclusionConfig extraAdultChildInclusionConfig;

    @Autowired
    private AddOnHelper addOnHelper;

    @Autowired
    private RoomInfoHelper roomInfoHelper;

    @Autowired
    private RoomAmentiesHelper roomAmentiesHelper;

    @Autowired
    private SearchRoomsBannerHelper searchRoomsBannerHelper;

    @PostConstruct
    public void init() {
        thresholdForSlashedAndDefaultHourPrice = commonConfigConsul.getThresholdForSlashedAndDefaultHourPrice();
        recommendedRoomPropertiesConfig = commonConfigConsul.getRecommendedRoomProperties();
        ratePlanMoreOptionsLimit = commonConfigConsul.getRatePlanMoreOptionsLimit();
        ratePlanDisplayLogic = commonConfigConsul.getRatePlanDisplayLogic();
        apLimitForInclusionIcons = commonConfigConsul.getApLimitForInclusionIcons();
        rtbCardConfigs = commonConfigConsul.getRtbCardConfigs();
        mandatoryChargesAlert = commonConfigConsul.getMandatoryChargesAlert();
        allInclusiveCard = commonConfigConsul.getAllInclusiveCard();
        supplierToRateSegmentMapping = commonConfigConsul.getSupplierToRateSegmentMapping();
        //Added missing slot and persuasion details of dayUse detail page
        missingSlotDetails = commonConfigConsul.getMissingSlotDetails();
        dayUseFunnelPersuasions = commonConfigConsul.getDayUseFunnelPersuasions();
        noCostEmiIconUrl = commonConfigConsul.getNoCostEmiIconUrl();
        noCostEmiIconConfig = commonConfigConsul.getNoCostEmiIconConfig();
        addOnInfoMostPopularTag = gson.fromJson(addOnInfoMostPopularTagConfig, HashMap.class);
        losFosGCCNudgePersuasion = commonConfigConsul.getLosFosGCCNudgePersuasion();
        linkedRatePlanStyle = commonConfigConsul.getLinkedRatePlanStyle();
        extraAdultChildInclusionConfig = commonConfigConsul.getExtraAdultChildInclusionConfig();
        Map<String, JsonNode> configJsonNodeMapEnglish = new HashMap<>();
        configJsonNodeMapEnglish = mobConfigHelper.populateConfigNodeMap(configJsonNodeMapEnglish, null, "eng");
        translateEnabledSupplierCodes = commonConfigConsul.getTranslateEnabledSupplierCodes();
        try {
            JsonNode actionInfoJsonNode = configJsonNodeMapEnglish.get("A").get(CONSUL_NODE_ACTION_INFO);
            actionInfoMap = objectMapperUtil.getObjectFromJsonNode(actionInfoJsonNode, new TypeReference<Map<String, StayTypeInfo>>() {
            });
            recommendedRoomPropertiesMap = objectMapperUtil.getObjectFromJsonWithType(recommendedRoomPropertiesConfig,  new TypeReference<Map<String,Map<String, String>>>() {
            }, DependencyLayer.CLIENTGATEWAY);
        } catch (Exception ex) {
            // Swallow the exception
        }

        // Initialize actionInfoMap in RoomInfoHelper
        roomInfoHelper.initializeActionInfoMap(actionInfoMap);
    }

    public SearchRoomsResponse convertSearchRoomsResponse(SearchRoomsRequest searchRoomsRequest, HotelDetailsResponse hotelDetailsResponse, String expData,
                                                          List<RoomStayCandidate> roomStayCandidates, SearchCriteria searchRoomsCriteria,
                                                          List<Filter> filterCriteria, String expVariantKeys, RequestDetails requestDetails,
                                                          CommonModifierResponse commonModifierResponse) {

        SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();

        // Extract room count from search criteria for priceDisplayMessage
        // This ensures priceDisplayMessage shows correct room count (e.g., "Per night for 3 Rooms")
        Integer searchRoomCount = null;
        if (CollectionUtils.isNotEmpty(roomStayCandidates)) {
            searchRoomCount = roomStayCandidates.size();
        }

        // Initialize experiment flags
        boolean isRTBCTrue = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && utility.isExperimentOn(commonModifierResponse.getExpDataMap(), EXP_RTBC);
        boolean isNewDetailPageTrue = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), NEW_DETAIL_PAGE);
        boolean isNewBlackDeal = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && utility.isExperimentOn(commonModifierResponse.getExpDataMap(), NEW_BLACK_DEAL);
        boolean blackRevamp = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), ExperimentKeys.BLACK_REVAMP.getKey());
        boolean isCallToBookV2Applicable = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && utility.isExperimentValid(commonModifierResponse.getExpDataMap(), callToBook, 4);
        int ancillaryVariant = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap())
                && commonModifierResponse.getExpDataMap().containsKey(ExperimentKeys.ANCILLARY_DISPLAY_PRICE_VARIANT.getKey())
                ? Integer.parseInt(commonModifierResponse.getExpDataMap().get(ExperimentKeys.ANCILLARY_DISPLAY_PRICE_VARIANT.getKey())) : 0;
        Set<String> hydraSegments = commonModifierResponse != null && commonModifierResponse.getHydraResponse() != null
                ? commonModifierResponse.getHydraResponse().getHydraMatchedSegment() : new HashSet<>();
        boolean isLiteResponse = searchRoomsRequest != null && searchRoomsRequest.getFeatureFlags() != null
                && searchRoomsRequest.getFeatureFlags().isLiteResponse();
        Map<String, String> expDataMap = commonModifierResponse != null ? commonModifierResponse.getExpDataMap() : new HashMap<>();

        // Check if we have valid hotel details
        if (hotelDetailsResponse == null || hotelDetailsResponse.getHotelDetails() == null) {
            return searchRoomsResponse;
        }

        HotelDetails hotelDetails = hotelDetailsResponse.getHotelDetails();
        String corpAlias = hotelDetails.getCorpMetaInfo() != null ? hotelDetails.getCorpMetaInfo().getAlias() : "";


        // Set currency
        searchRoomsResponse.setCurrency(hotelDetails.getCurrencyCode());

        if (commonModifierResponse != null) {
            commonModifierResponse.setLiteResponse(isLiteResponse);
        }

        long startTime = System.currentTimeMillis();
        try {
            String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
            String askedCurrency = StringUtils.isNotEmpty(searchRoomsCriteria.getCurrency()) ? searchRoomsCriteria.getCurrency() : "INR";

            // Check if we have rooms or room combos
            if (CollectionUtils.isEmpty(hotelDetails.getRooms()) && CollectionUtils.isEmpty(hotelDetails.getRoomCombos())) {
                return searchRoomsResponse;
            }

            // Build alternate price card if available
            if (CollectionUtils.isNotEmpty(hotelDetails.getAlternateDatePriceDetails())) {
                alternatePriceHelper.buildAlternatePriceCard(hotelDetails, searchRoomsResponse, searchRoomsRequest);
            }

            // Implement price graph info if priceVariation data is available
            if (hotelDetails.getPriceVariation() != null) {
                searchRoomsResponse.setPriceGraphInfo(getPriceGraphInfo(hotelDetails.getPriceVariation(), askedCurrency, expDataMap));
            }
            // Map basic hotel information
            boolean isLuxeHotel = utility.isLuxeHotel(hotelDetails.getCategories());
            boolean isAltAccoHotel = hotelDetails.getHotelRateFlags().isAltAcco();
            boolean isHighSellingAltAcco = hotelDetails.getHotelRateFlags().isHighSellingAltAcco();
            String propertyType = hotelDetails.getPropertyType();
            String deviceType = searchRoomsRequest != null && searchRoomsRequest.getDeviceDetails() != null
                    ? searchRoomsRequest.getDeviceDetails().getDeviceType() : EMPTY_STRING;
            String countryCode = searchRoomsCriteria.getCountryCode();
            String checkIn = searchRoomsCriteria.getCheckIn();
            String checkOut = searchRoomsCriteria.getCheckOut();
            int los = dateUtil.getDaysDiff(LocalDate.parse(checkIn), LocalDate.parse(checkOut));
            int ap = dateUtil.getDaysDiff(LocalDate.now(), LocalDate.parse(checkIn));
            boolean isBlockPAH = expDataMap.containsKey("blockPAH") && StringUtils.isNotBlank(expDataMap.get("blockPAH")) && Boolean.parseBoolean(expDataMap.get("blockPAH"));
            boolean isOHSExpEnable = false;
            if (expDataMap instanceof LinkedHashMap) {
                isOHSExpEnable = utility.isOHSExpEnable(propertyType, (LinkedHashMap<String, String>) expDataMap);
            } else if (MapUtils.isNotEmpty(expDataMap)) {
                // Convert Map to LinkedHashMap if needed
                LinkedHashMap<String, String> linkedExpDataMap = new LinkedHashMap<>(expDataMap);
                isOHSExpEnable = utility.isOHSExpEnable(propertyType, linkedExpDataMap);
            }
            boolean isIHAAOrch = utility.isExperimentTrue(expDataMap, "IHAAOrch");
            boolean showOccasionPackagesPlan = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), SHOW_OCC_PACKAGE);
            boolean showMandatoryChargesDH = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), SHOW_MANDATORY_CHARGES_DH);
            String siteDomain = requestDetails.getSiteDomain();
            String selectedRoomCode = searchRoomsRequest != null && searchRoomsRequest.getSearchCriteria() != null
                    && searchRoomsRequest.getSearchCriteria().getSelectedRatePlan() != null
                    ? searchRoomsRequest.getSearchCriteria().getSelectedRatePlan().getRoomCode() : "";
            String selectedRateplanCode = searchRoomsRequest != null && searchRoomsRequest.getSearchCriteria() != null
                    && searchRoomsRequest.getSearchCriteria().getSelectedRatePlan() != null
                    ? searchRoomsRequest.getSearchCriteria().getSelectedRatePlan().getRatePlanCode() : "";

            // Map offers and persuasions
            if (showOccasionPackagesPlan) {
                // Build search rooms level persuasions using helper
                searchRoomsResponse.setPersuasions(searchRoomsPersuasionHelper.buildSearchRoomsPersuasions(hotelDetails, deviceType));
                searchRoomsResponse.setSpecialOfferCard(searchRoomsPersuasionHelper.buildSpecialOfferCard(hotelDetails));
            } else {
                // Extract and set long stay benefits and persuasion from dealBenefits
                if (CollectionUtils.isNotEmpty(hotelDetails.getDealBenefits())) {
                    // Extract long stay benefits
                    searchRoomsResponse.setLongStayBenefits(extractLongStayBenefits(hotelDetails.getDealBenefits(), BenefitType.LONG_STAY_BENEFITS));
                }
                searchRoomsResponse.setOffers(transformOffers(hotelDetails.getOffers()));
                //searchRoomsResponse.setAppliedOffers(new ArrayList<>());
                searchRoomsResponse.setBlackInfo(extractAndBuildBlackInfo(hotelDetails.getDealBenefits()));
            }
            // Extract long stay persuasion
            searchRoomsResponse.setLongStayPersuasion(extractLongStayBenefits(hotelDetails.getDealBenefits(), BenefitType.LON_STAY_PERSUASIONS));

            searchRoomsResponse.setSpotlightApplicable(hotelDetails.getHotelRateFlags().isSpotlightApplicable());

            if (MapUtils.isNotEmpty(hotelDetails.getMealInfos())) {
                searchRoomsResponse.setMealsMap(commonResponseTransformer.transformMealsMapFromOrch(hotelDetails.getMealInfos()));
            }

            if (MapUtils.isNotEmpty(hotelDetails.getRatingDataMap()) && hotelDetails.getRatingDataMap().containsKey(RatingCategory.FOOD)) {
                searchRoomsResponse.setFoodRatingData(reArchUtility.buildRatingData(hotelDetails.getRatingDataMap().get(RatingCategory.FOOD), new UGCRatingData()));
            }

            // Map tracking info

            if (hotelDetails.getAdditionalDetails().getTotalAvailabilityCount() != 0) {
                searchRoomsResponse.setTotalAvailabilityCount(hotelDetails.getAdditionalDetails().getTotalAvailabilityCount());
            }

            Map<String, String> trackingMap = new HashMap<>();
            searchRoomsResponse.setTrackingMap(commonResponseTransformer.buildTrackingMap(trackingMap));
            searchRoomsResponse.setTrackingText(hotelDetails.getTrackingInfo().getTrackingText());

            //searchRoomsResponse.setMsmeCorpCard(null); // TODO: Map msmeCorpCard from hotelDetails - B2B implementation

            // searchRoomsResponse.setHydraSegments(null); //Not being used at client end

            searchRoomsResponse.setExpVariantKeys(StringUtils.isNotBlank(expVariantKeys) ? expVariantKeys : null);
            searchRoomsResponse.setUserLoyaltyStatus(hotelDetailsResponse.getUserLoyaltyStatus());

            // Set all inclusive inclusions based on experiment and flags
            if (utility.isExperimentOn(expDataMap, Constants.ALL_INCLUSIVE_PLAN_EXPERIMENT) && hotelDetails.getHotelRateFlags().isAnyRateAllInclusive()) {
                // Extract all inclusive inclusions from hotelDetails
                searchRoomsResponse.setAllInclusiveInclusions(extractAllInclusiveInclusions(hotelDetails));
                // Also build AllInclusiveCard
                searchRoomsResponse.setAllInclusiveCard(buildAllInclusiveCard(hotelDetails));
            }

            // Transform rooms
            Map<String, String> ratePlanCodeAndNameMap = new HashMap<>();
            boolean packageRoomPresent = false;

            // Handle single rooms vs room combos
            if (CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {
                // Filter out package rooms from regular rooms
                List<Rooms> regularRooms = hotelDetails.getRooms().stream()
                        .filter(room -> RoomType.EXACT.name().equalsIgnoreCase(room.getType()))
                        .collect(Collectors.toList());

                // Transform individual rooms to exact rooms
                List<RoomDetails> exactRooms = transformRoomsToRoomDetails(
                        regularRooms, hotelDetails, expData,
                        askedCurrency, requestDetails.getFunnelSource(), los, ap, isBlockPAH,
                        commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel,
                        isOHSExpEnable, propertyType,
                        countryCode, siteDomain, selectedRoomCode, selectedRateplanCode,
                        isHighSellingAltAcco
                );
                searchRoomsResponse.setExactRooms(exactRooms);

                // TODO: Set extra guest detail persuasion
                // searchRoomsResponse.setExtraGuestDetailPersuasion(buildExtraGuestDetailPersuasion(...));
            }

            // Set occupancy rooms (legacy: hotelRates.getOccupencyLessRoomTypeDetails -> OrchV2: hotelDetails.getRoomCombos with ComboType.OCCUPANCY_ROOM)
            // This should be set regardless of whether we have individual rooms or room combos, matching legacy behavior
            if (CollectionUtils.isNotEmpty(hotelDetails.getRoomCombos())) {
                List<RoomCombo> occupancyRoomCombos = hotelDetails.getRoomCombos().stream()
                        .filter(combo -> combo.getComboType() == ComboType.OCCUPANCY_ROOM)
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(occupancyRoomCombos)) {
                    // Occupancy rooms are essentially single room combos transformed to RoomDetails
                    List<RoomDetails> occupancyRooms = transformOccupancyRoomCombos(
                            occupancyRoomCombos, hotelDetails, expData,
                            askedCurrency, requestDetails.getFunnelSource(), los, ap, isBlockPAH,
                            commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel,
                            isOHSExpEnable, propertyType, countryCode, siteDomain, selectedRoomCode,
                            selectedRateplanCode, isHighSellingAltAcco
                    );
                    searchRoomsResponse.setOccupancyRooms(occupancyRooms);
                }
            }

            // Set recommended combos (legacy: hotelRates.getRecommendedRoomTypeDetails + hotelRates.getOtherRecommendedRooms)
            // This should be set regardless of whether we have individual rooms or room combos, matching legacy behavior
            if (CollectionUtils.isNotEmpty(hotelDetails.getRoomCombos())) {
                List<RoomCombo> recommendedRoomCombos = hotelDetails.getRoomCombos().stream()
                        .filter(combo -> combo.getComboType() == ComboType.RECOMMENDED_ROOM || combo.getComboType() == ComboType.OTHER_RECOMMENDED_ROOM)
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(recommendedRoomCombos)) {
                    List<RecommendedCombo> recommendedCombos = transformRoomCombos(
                            recommendedRoomCombos, hotelDetails.getMedia(), hotelDetails, expData, askedCurrency,
                            requestDetails.getFunnelSource(), los, ap, isBlockPAH, commonModifierResponse,
                            ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel, isOHSExpEnable,
                            propertyType, countryCode, siteDomain, selectedRoomCode, selectedRateplanCode, isHighSellingAltAcco, searchRoomCount
                    );
                    searchRoomsResponse.setRecommendedCombos(recommendedCombos);
                }

                List<RoomCombo> losRoomCombos = hotelDetails.getRoomCombos().stream()
                        .filter(combo -> combo.getComboType() == ComboType.LOS_COMBO)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(losRoomCombos)) {
                    List<RecommendedCombo> losCombos = transformRoomCombos(
                            losRoomCombos, hotelDetails.getMedia(), hotelDetails, expData, askedCurrency,
                            requestDetails.getFunnelSource(), los, ap, isBlockPAH, commonModifierResponse,
                            ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel, isOHSExpEnable,
                            propertyType, countryCode, siteDomain, selectedRoomCode, selectedRateplanCode, isHighSellingAltAcco, searchRoomCount
                    );
                    searchRoomsResponse.setMultiRoomStayCombos(losCombos);
                }
            }

            // Handle recommended rooms logic (outside if/else-if block as per legacy code)
            // Legacy: hotelRates.getRecommendedRooms[] -> OrchV2: hotelDetails.getRoomCombos with ComboType.RECOMMENDED_ROOM
            if (commonModifierResponse != null && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), ExperimentKeys.SHOW_RECOMMENDED_ROOMS.getKey())) {
                // Handle occasion package room details first (legacy: hotelRates.getOccassionPackageRoomDetails)
                // In OrchV2: Single entry in hotelDetails.getRooms[] with type "OCCASION_PACKAGE"
                List<Rooms> occasionPackageRooms = hotelDetails.getRooms().stream()
                        .filter(room -> RoomType.OCCASION_PACKAGE.name().equalsIgnoreCase(room.getType()))
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(occasionPackageRooms)) {
                    List<RoomDetails> occasionPackageRoomDetails = transformRoomsToRoomDetails(
                            occasionPackageRooms, hotelDetails, expData,
                            askedCurrency, requestDetails.getFunnelSource(), los, ap, isBlockPAH,
                            commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel,
                            isOHSExpEnable, propertyType,
                            countryCode, siteDomain, selectedRoomCode, selectedRateplanCode,
                            isHighSellingAltAcco
                    );
                    searchRoomsResponse.setRecommendedRooms(occasionPackageRoomDetails);
                } else {
                    // Handle package room details (legacy: hotelRates.getPackageRoomDetails)
                    // In OrchV2: Single entry in hotelDetails.getRooms[] with type "PACKAGE"
                    List<Rooms> packageRooms = hotelDetails.getRooms().stream()
                            .filter(room -> RoomType.SUPER_PACKAGE.name().equalsIgnoreCase(room.getType()))
                            .collect(Collectors.toList());

                    if (CollectionUtils.isNotEmpty(packageRooms)) {
                        List<RoomDetails> packageRoomDetails = transformRoomsToRoomDetails(
                                packageRooms, hotelDetails, expData,
                                askedCurrency, requestDetails.getFunnelSource(), los, ap, isBlockPAH,
                                commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel,
                                isOHSExpEnable, propertyType,
                                countryCode, siteDomain, selectedRoomCode, selectedRateplanCode,
                                isHighSellingAltAcco
                        );
                        searchRoomsResponse.setRecommendedRooms(packageRoomDetails);
                        packageRoomPresent = true;
                    }
                }

                // Handle upsell recommendation logic from legacy (lines 688-715)
                if (utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), ExperimentKeys.SHOW_UPSELL_RECOMMENDATION.getKey())) {
                    List<RoomDetails> additionalRecommendedRooms = new ArrayList<>();

                    if (!utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), ExperimentKeys.recommendationV1.getKey())) {
                        // Handle meal upsell rooms (legacy: hotelRates.getMealUpsellRoomDetails)
                        // In OrchV2: Single entry in hotelDetails.getRooms[] with type "MEAL_UPSELL"
                        List<Rooms> mealUpsellRooms = hotelDetails.getRooms().stream()
                                .filter(room -> RoomType.MEAL_UPGRADE.name().equalsIgnoreCase(room.getType()))
                                .collect(Collectors.toList());

                        if (CollectionUtils.isNotEmpty(mealUpsellRooms)) {
                            List<RoomDetails> mealUpsellRoomDetails = transformRoomsToRoomDetails(
                                    mealUpsellRooms, hotelDetails, expData,
                                    askedCurrency, requestDetails.getFunnelSource(), los, ap, isBlockPAH,
                                    commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel,
                                    isOHSExpEnable, propertyType,
                                    countryCode, siteDomain, selectedRoomCode, selectedRateplanCode,
                                    isHighSellingAltAcco
                            );
                            additionalRecommendedRooms.addAll(mealUpsellRoomDetails);
                        }
                    } else {
                        // Handle new recommendation algorithm (legacy: hotelRates.getRecommendedRooms[])
                        // In OrchV2: Multiple entries in hotelDetails.getRooms[] with type "RECOMMENDED"
                        List<Rooms> recommendedRooms = hotelDetails.getRooms().stream()
                                .filter(room -> RoomType.MMT_RECOMMEND.name().equalsIgnoreCase(room.getType()))
                                .collect(Collectors.toList());

                        if (CollectionUtils.isNotEmpty(recommendedRooms)) {
                            List<RoomDetails> recommendedRoomDetails = transformRoomsToRoomDetails(
                                    recommendedRooms, hotelDetails, expData,
                                    askedCurrency, requestDetails.getFunnelSource(), los, ap, isBlockPAH,
                                    commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel,
                                    isOHSExpEnable, propertyType,
                                    countryCode, siteDomain, selectedRoomCode, selectedRateplanCode,
                                    isHighSellingAltAcco
                            );
                            additionalRecommendedRooms.addAll(recommendedRoomDetails);
                        }
                    }

                    // Set filter details to null for first recommended room if present
                    if (CollectionUtils.isNotEmpty(additionalRecommendedRooms) &&
                            CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedRooms()) &&
                            searchRoomsResponse.getRecommendedRooms().get(0) != null) {
                        searchRoomsResponse.getRecommendedRooms().get(0).setFilterDetails(null);
                    }

                    // Initialize recommended rooms list if empty
                    if (CollectionUtils.isEmpty(searchRoomsResponse.getRecommendedRooms())) {
                        searchRoomsResponse.setRecommendedRooms(new ArrayList<>());
                    }

                    // Add additional recommended rooms
                    if (CollectionUtils.isNotEmpty(additionalRecommendedRooms)) {
                        searchRoomsResponse.getRecommendedRooms().addAll(additionalRecommendedRooms);
                    }
                }
            }

            // Handle package rooms separately (outside SHOW_RECOMMENDED_ROOMS experiment)
            // Legacy: hotelRates.getPackageRoomDetails -> OrchV2: Single entry in hotelDetails.getRooms[] with type "PACKAGE"
            if (commonModifierResponse == null || !utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), ExperimentKeys.SHOW_RECOMMENDED_ROOMS.getKey())) {
                List<Rooms> packageRooms = hotelDetails.getRooms().stream()
                        .filter(room -> RoomType.SUPER_PACKAGE.name().equalsIgnoreCase(room.getType()))
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(packageRooms)) {
                    List<RoomDetails> packageRoomDetails = transformRoomsToRoomDetails(
                            packageRooms, hotelDetails, expData,
                            askedCurrency, requestDetails.getFunnelSource(), los, ap, isBlockPAH,
                            commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel,
                            isOHSExpEnable, propertyType,
                            countryCode, siteDomain, selectedRoomCode, selectedRateplanCode,
                            isHighSellingAltAcco
                    );
                    searchRoomsResponse.setPackageRooms(packageRoomDetails);
                    // TODO: Update package inclusion base rate plan name when method is available
                    // updatePackageInclusionBaseRatePlanName(ratePlanCodeAndNameMap, searchRoomsResponse.getPackageRooms());
                }
            }

            // Set property layout title text
            if (Constants.LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelDetails.getListingType())) {
                if (hotelDetails.getRoomCount() > 1) {
                    searchRoomsResponse.setPropertyLayoutTitleText(
                            MessageFormat.format(polyglotService.getTranslatedData(MULTIPLE_ENTIRE_PROPERTY_LAYOUT_TEXT),
                                    hotelDetails.getRoomCount(), propertyType, propertyType)
                    );
                } else {
                    searchRoomsResponse.setPropertyLayoutTitleText(
                            MessageFormat.format(polyglotService.getTranslatedData(SINGLE_ENTIRE_PROPERTY_LAYOUT_TEXT), propertyType)
                    );
                }
            } else {
                // TODO: Check if room type count > 1 to set property layout title text
                 if (getRoomTypeCount(hotelDetails) > 1) {
                     searchRoomsResponse.setPropertyLayoutTitleText(polyglotService.getTranslatedData(DEFAULT_PROPERTY_LAYOUT_TEXT));
                 }
            }

            // Set additional fields
            searchRoomsResponse.setAddons(null); // TODO: Transform AddOns - hotelDetails doesn't have addons field

            // Initialize missing variables for filters
            String bnplVariant = hotelDetails.getBnplVariant() != null ? hotelDetails.getBnplVariant().name() : "";
            boolean applyFilterToCombo = expDataMap.containsKey(APPLY_FILTER_TO_COMBO.getKey()) && Boolean.parseBoolean(expDataMap.get(APPLY_FILTER_TO_COMBO.getKey()));

            boolean hideSpecificFilters = hotelDetails.getTrackingInfo() != null && StringUtils.isNotEmpty(hotelDetails.getTrackingInfo().getTrackingText()) && hotelDetails.getTrackingInfo().getTrackingText().contains("FLXI_AVL");
            boolean negotiatedRateFlag = hotelDetails.getHotelRateFlags().isNegotiatedRateFlag();

            searchRoomsResponse.setFilters(searchRoomsFilter.getFilters(searchRoomsResponse.getExactRooms(), searchRoomsResponse.getOccupancyRooms(),
                    filterCriteria, requestDetails.getFunnelSource(), ap, isBlockPAH,
                    bnplVariant, commonModifierResponse, isLuxeHotel, negotiatedRateFlag,
                    applyFilterToCombo, hideSpecificFilters, requestDetails.getPageContext()));

            // Build feature flags
            searchRoomsResponse.setFeatureFlags(getFeatureFlags(hotelDetails, searchRoomsResponse.getExactRooms(), (commonModifierResponse != null ? commonModifierResponse.getExpDataMap() : null)));

            // Build context details
            searchRoomsResponse.setContextDetails(getContextDetails(hotelDetails));

            // EMI config based on rate plan data
            searchRoomsPriceHelper.buildEmiConfig(searchRoomsResponse, hotelDetails);

            // coupon card config
            if (searchRoomsRequest != null && searchRoomsRequest.getSearchCriteria() != null && CollectionUtils.isNotEmpty(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates())) {
                searchRoomsResponse.setCouponCardConfig(buildCouponCardConfig(searchRoomsResponse, searchRoomsRequest.getSearchCriteria().getRoomStayCandidates().get(0).getAdultCount()));
            }

            // TODO: Implement impInfo (important info) for combo rooms
            // searchRoomsResponse.setImpInfo(getImpInfo(recommendedCombos, roomStayCandidates));

            // Set super package card if package rooms are present
            packageRoomPresent = packageRoomPresent || isPackageRoomPresentInCombos(hotelDetails.getRoomCombos());
            if (CollectionUtils.isNotEmpty(searchRoomsResponse.getPackageRooms()) || packageRoomPresent) {
                searchRoomsPersuasionHelper.setSuperPackageCard(searchRoomsResponse, askedCurrency);
            }
            searchRoomsResponse.setExtraGuestDetailPersuasion(searchRoomsPersuasionHelper.buildExtraGuestDetailPersuasion(false, hotelDetails.getRooms(), isAltAccoHotel));

            // Build property sellable type
            searchRoomsResponse.setPropertySellableType(buildPropertySellableType(hotelDetails));
            if (SELLABLE_ROOM_TYPE.equalsIgnoreCase(searchRoomsResponse.getPropertySellableType())) {
                searchRoomsResponse.setPropertySellableText(polyglotService.getTranslatedData(ROOM_SELLABLE_TYPE));
            } else {
                searchRoomsResponse.setPropertySellableText(polyglotService.getTranslatedData(STAY_SELLABLE_TYPE));
            }

            // Set selected rate plan code
            if (hotelDetails.getAdditionalDetails() != null) {
                searchRoomsResponse.setSelectedRatePlanCode(hotelDetails.getAdditionalDetails().getSelectedRatePlanCode());
            }

            // TODO: Map spaceIdToSleepingInfoArrMap data to rooms
            // mappingSpaceIdToEachRoomCode(searchRoomsResponse, spaceIdToSleepingInfoArrMap);

            // Build additional fees
            if (CollectionUtils.isNotEmpty(hotelDetails.getMandatoryCharges()) && (showMandatoryChargesDH || !DH_COUNTRY_CODE.equalsIgnoreCase(hotelDetails.getCurrencyCode()))) {
                searchRoomsResponse.setAdditionalFees(transformAdditionalFees(hotelDetails, commonModifierResponse, expDataMap));
            }

            // Handle space data transformation - Shared Spaces at Response Level
            // Condition: Check if space details are required and available
            if (CollectionUtils.isNotEmpty(hotelDetails.getRooms()) &&
                    hotelDetails.getRooms().get(0).getRoomInfo() != null &&
                    CollectionUtils.isNotEmpty(hotelDetails.getRooms().get(0).getRoomInfo().getSpaces()) &&
                    !isHighSellingAltAcco && hotelDetails.getRooms().get(0).getRoomInfo().getRoomFlags().isSpaceDetailsRequired()) {

                // Get shared spaces from the first room's room info (following legacy pattern)
                com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData sharedSpaceData =
                        hotelDetails.getRooms().get(0).getRoomInfo().getSpaces().stream()
                                .filter(space -> space.getType() == com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData.Type.SHARED)
                                .findFirst()
                                .orElse(null);

                if (sharedSpaceData != null) {
                    // Use experiment EXP_PLV2 to decide between V1 and V2 (following legacy pattern)
                    if (commonModifierResponse != null && utility.isExperimentOn(commonModifierResponse.getExpDataMap(), EXP_PLV2)) {
                        searchRoomsResponse.setSharedSpacesV2(roomInfoHelper.getSpaceDataV2(sharedSpaceData, false));
                    } else {
                        searchRoomsResponse.setSharedSpaces(roomInfoHelper.getSpaceData(sharedSpaceData, commonModifierResponse));
                    }
                }
            }

            if (!requestDetails.isLoggedIn()) {
                searchRoomsResponse.setLoginPersuasion(buildLoginPersuasion());
            }

            // Build hotel details for non-desktop clients
            String detailDeepLink = deepLinkHelper.buildDetailDeepLinkUrl(hotelDetails, searchRoomsRequest);
            searchRoomsResponse.setDetailDeeplinkUrl(detailDeepLink);
            searchRoomsResponse.setSearchRoomDeeplinkUrl(deepLinkHelper.buildSearchRoomsDeepLinkUrl(hotelDetails, searchRoomsRequest));
            if (!Constants.CLIENT_DESKTOP.equalsIgnoreCase(client)) {
                searchRoomsResponse.setHotelDetails(buildHotelDetails(hotelDetails, requestDetails.getFunnelSource(), searchRoomsRequest.getSearchCriteria(), detailDeepLink));
            }
            // Build banner for non-desktop and non-corp contexts
            if (!DEVICE_OS_DESKTOP.equalsIgnoreCase(client) && !CORP_ID_CONTEXT.equalsIgnoreCase(requestDetails.getIdContext())) {
                searchRoomsResponse.setBanner(searchRoomsBannerHelper.buildBanner(searchRoomsResponse, hotelDetails));
            }

             if (hotelDetails.getHotelRateFlags().isNegotiatedRateFlag()) {
                 buildInstantFareInfo(corpAlias, searchRoomsResponse, askedCurrency);
            }

            // TODO: Set addOnErrorMessage if present
            // searchRoomsResponse.setAddOnErrorMessage(hotelDetails.getAddOnErrorMessage());

            // Build room info
            SleepingArrangementRoomInfo roomInfo = roomInfoHelper.buildRoomInfo(hotelDetails, searchRoomsResponse,
                    searchRoomsCriteria.getCountryCode(), isOHSExpEnable, isNewDetailPageTrue, isIHAAOrch, expDataMap);
            searchRoomsResponse.setRoomInfo(roomInfo);

            boolean isHighValueCall = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), HVC);
            if (isHighValueCall) {
                updateSupportDetails(searchRoomsRequest, searchRoomsCriteria, requestDetails, commonModifierResponse, isHighValueCall, searchRoomsResponse, hotelDetails, isCallToBookV2Applicable);
            }
            if (isCallToBookV2Applicable && hotelDetails.getHotelRateFlags().isShowCallToBook() && Utility.isGccOrKsa()) {
                updateSupportDetails(searchRoomsResponse);
            }

            // TODO: Set RTB persuasion card
            if (!isRTBCTrue) {
                // searchRoomsResponse.setRtbPersuasionCard(buildRtbPersuasionCard(hotelDetails));
            }

           searchRoomsResponse.setPaymentCard(buildPaymentCard(requestDetails, hotelDetails));

            // TODO: Set primary offer based on various deal types
            // - Check vistaraDealAvailable
            // - Check exclusiveFlyerRateAvailable
            // - Check busExclusiveRateAvailable
            // - Check trainExclusiveRateAvailable
            // - Check campaignPojo for sale campaigns
            // - Check supplier deals
            // - Check no cost EMI
            if (hotelDetails.getPrimaryOffer() != null) {
                PrimaryOffer primaryOffer = getPrimaryOffer(hotelDetails.getPrimaryOffer());
                searchRoomsResponse.setPrimaryOffer(primaryOffer);
            }


            if (utility.isExperimentOn(expDataMap, Constants.ALL_INCLUSIVE_PLAN_EXPERIMENT) && hotelDetails.getHotelRateFlags().isAnyRateAllInclusive()) {
                searchRoomsResponse.setAllInclusiveInclusions(buildAllInclusiveBenefits(hotelDetails.getAllInclusiveData()));
                searchRoomsResponse.setAllInclusiveCard(buildAllInclusiveCard(hotelDetails));
            }

            // TODO: Set extra bed policy from house rules
            searchRoomsResponse.setExtraBedPolicy(buildExtraBedPolicy(hotelDetails.getHouseRules()));

            // TODO: Handle alternate dates persuasion
            // commonResponseTransformer.buildAltDatesPersuasionAndBottomsheet(...)

            // TODO: Set luckyUserContext
            // searchRoomsResponse.setLuckyUserContext(luckyUserContext);

            // TODO: Set hotel cloud data
            // searchRoomsResponse.setHotelCloudData(hotelCloudData);

            // TODO: Set business identification card data
            // searchRoomsResponse.setCardData(cardData);

            searchRoomsResponse.setCardsMap(buildCardMap(hotelDetails.getCardData()));

        } finally {
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_RESPONSE_PROCESS, DETAIL_SEARCH_ROOMS, System.currentTimeMillis() - startTime);
        }

        return searchRoomsResponse;
    }

    private void updateSupportDetails(SearchRoomsRequest searchRoomsRequest, SearchCriteria searchRoomsCriteria, RequestDetails requestDetails, CommonModifierResponse commonModifierResponse, boolean isHighValueCall, SearchRoomsResponse searchRoomsResponse, HotelDetails hotelDetails, boolean isCallToBookV2Applicable) {
        String defaultPriceKey;
        int totalTicketValue = 0;
        if (CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms())) {
            if (searchRoomsResponse.getExactRooms().get(0) != null && CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms().get(0).getRatePlans())
                    && searchRoomsResponse.getExactRooms().get(0).getRatePlans().get(0) != null && CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms().get(0).getRatePlans().get(0).getTariffs())
                    && searchRoomsResponse.getExactRooms().get(0).getRatePlans().get(0).getTariffs().get(0) != null) {
                defaultPriceKey = searchRoomsResponse.getExactRooms().get(0).getRatePlans().get(0).getTariffs().get(0).getDefaultPriceKey();
                Map<String, TotalPricing> pricingMap = searchRoomsResponse.getExactRooms().get(0).getRatePlans().get(0).getTariffs().get(0).getPriceMap();
                totalTicketValue = calculateTotalTicketValue(defaultPriceKey, pricingMap);
            }
        } else if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos())) {
            if (searchRoomsResponse.getRecommendedCombos().get(0) != null && searchRoomsResponse.getRecommendedCombos().get(0).getComboTariff() != null) {
                defaultPriceKey = searchRoomsResponse.getRecommendedCombos().get(0).getComboTariff().getDefaultPriceKey();
                Map<String, TotalPricing> pricingMap = searchRoomsResponse.getRecommendedCombos().get(0).getComboTariff().getPriceMap();
                totalTicketValue = calculateTotalTicketValue(defaultPriceKey, pricingMap);
            }
        }
        boolean aboApplicable = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && utility.isExperimentOn(commonModifierResponse.getExpDataMap(), ExperimentKeys.aboApplicable.name());
        searchRoomsResponse.setSupportDetails(utility.buildSupportDetails(totalTicketValue, PAGE_CONTEXT_DETAIL,
                utility.getHighValueCallFunnelType(hotelDetails.getPropertyType(), requestDetails.getFunnelSource(), searchRoomsCriteria.getCountryCode()), aboApplicable));
        if (searchRoomsResponse.getSupportDetails() != null) {
            searchRoomsResponse.setRequestCallbackData(utility.buildRequestToCallBackDataForHighValue(PAGE_CONTEXT_DETAIL, "", "", isCallToBookV2Applicable));
        }
        if (searchRoomsResponse.getSupportDetails() != null) {
            String hotelName = StringUtils.isNotEmpty(hotelDetails.getName()) ? hotelDetails.getName() : EMPTY_STRING;
            searchRoomsResponse.getSupportDetails().setFormUrl(deepLinkHelper.buildFormUrlForDetail(searchRoomsRequest, searchRoomsResponse.getSupportDetails(), hotelDetails.getPropertyType(), hotelName));
        }

    }

    private int calculateTotalTicketValue(String defaultPriceKey,Map<String, TotalPricing> pricingMap){
        int totalTicketValue = 0;
        for (Map.Entry<String, TotalPricing> entry : pricingMap.entrySet()) {
            String key = entry.getKey();
            TotalPricing totalPricing = entry.getValue();
            if(key.equals(defaultPriceKey)){
                totalTicketValue = utility.getTotalTicketValue(totalPricing,true);
            }
        }
        return totalTicketValue;
    }

    private Card buildPaymentCard(RequestDetails requestDetails, HotelDetails hotelDetails) {
        if (requestDetails != null && Utility.isGroupBookingFunnel(requestDetails.getFunnelSource()) && CollectionUtils.isNotEmpty(hotelDetails.getRoomCombos())) {
            RoomCombo roomCombo = hotelDetails.getRoomCombos().stream()
                    .filter(combo -> ComboType.RECOMMENDED_ROOM.equals(combo.getComboType()))
                    .findFirst().orElse(null);
            if (roomCombo != null && roomCombo.getPaymentPlan() != null && StringUtils.isNotEmpty(roomCombo.getPaymentPlan().getPaymentCardText())) {
                Card paymentCard = new Card();
                paymentCard.setTitle(roomCombo.getPaymentPlan().getPaymentCardText());
                paymentCard.setCta(polyglotService.getTranslatedData(KNOW_MORE));
                paymentCard.setIconUrl(paymentPlanIcon);
                return paymentCard;
            }
        }
        return null;
    }


    public com.mmt.hotels.clientgateway.response.rooms.PaymentPlan buildPaymentPlan(PaymentPlan paymentPlan) {
        if (paymentPlan != null && paymentPlan.getAmount() != 0.0) {
            com.mmt.hotels.clientgateway.response.rooms.PaymentPlan paymentPlanCG = new com.mmt.hotels.clientgateway.response.rooms.PaymentPlan();
            paymentPlanCG.setText(paymentPlan.getText());
            paymentPlanCG.setAmount(paymentPlan.getAmount());
            if (CollectionUtils.isNotEmpty(paymentPlan.getPaymentPolicy())) {
                AtomicInteger index = new AtomicInteger(1);
                paymentPlanCG.setPaymentPolicy(new ArrayList<>());
                paymentPlan.getPaymentPolicy().forEach(policy -> {
                    com.mmt.hotels.clientgateway.response.rooms.PaymentPlan policyCG = new com.mmt.hotels.clientgateway.response.rooms.PaymentPlan();
                    policyCG.setSequence(index.getAndIncrement());
                    policyCG.setText(policy.getText());
                    policyCG.setAmount(policy.getAmount());
                    policyCG.setPaymentDate(buildPaymentDate(policy.getPaymentDateText()));
                    paymentPlanCG.getPaymentPolicy().add(policyCG);
                });
                paymentPlanCG.setPenaltyText(polyglotService.getTranslatedData(PAYMENT_PLAN_PENALTY_TEXT));
            }
            return paymentPlanCG;
        }
        return null;
    }

    private String buildPaymentDate(String paymentDate) {
        if (paymentDate != null && paymentDate.split(COMMA).length > 1) {
            return paymentDate.split(COMMA)[0];
        }
        return null;
    }


    private Map<String, CardInfo> buildCardMap(Map<String, CardData> cardData) {
        Map<String, CardInfo> cardInfoMap = new HashMap<>();
        if (MapUtils.isNotEmpty(cardData)) {
            cardData.forEach((key, data) -> {
                CardInfo cgCardInfo = new CardInfo();
                //cgCardInfo.setIndex(data.getIndex());
                cgCardInfo.setSubType(data.getCardSubType());
                cgCardInfo.setId(data.getCardId());
                cgCardInfo.setTitleText(data.getTitleText());
                cgCardInfo.setSubText(data.getSubText());
                cgCardInfo.setIconURL(data.getIconUrl());
                cgCardInfo.setBgColor(data.getBgColor());
                cgCardInfo.setCardAction(convertCardAction(data.getCardAction()));
                cgCardInfo.setTemplateId(data.getTemplateId());
                cardInfoMap.put(key, cgCardInfo);
            });
        }
        return cardInfoMap;
    }

    private List<CardAction> convertCardAction(List<CardData.CardAction> cardActions) {
        return CollectionUtils.isNotEmpty(cardActions) ? cardActions.stream().map(action -> {
                    CardAction cgCardAction = new CardAction();
                    cgCardAction.setTitle(action.getTitle());
                    cgCardAction.setWebViewUrl(action.getWebViewUrl());
                    return cgCardAction;
                }).collect(Collectors.toList()) : null;
    }


    private ExtraBedPolicy buildExtraBedPolicy(HouseRules houseRules) {
        ExtraBedPolicy extraBedPolicy = null;
        if (houseRules != null && CollectionUtils.isNotEmpty(houseRules.getCategoryInfos())) {
            for (CategoryInfo categoryInfo : houseRules.getCategoryInfos()) {
                if ("EXTRA_BED_POLICY".equals(categoryInfo.getId())) {
                    RuleTableInfo ruleTableInfo = getParsedRuleTableInfo(transformRuleTableInfo(categoryInfo.getRuleTableInfo()));
                    if (ruleTableInfo == null) {
                        return null;
                    }
                    extraBedPolicy = new ExtraBedPolicy();
                    extraBedPolicy.setDesc(categoryInfo.getDesc());
                    extraBedPolicy.setTitle(categoryInfo.getHeading());
                    extraBedPolicy.setRules(categoryInfo.getRuleDesc());
                    extraBedPolicy.setRuleTableInfo(ruleTableInfo);
                }
            }
        }
        return extraBedPolicy;
    }

    private RuleTableInfo transformRuleTableInfo(com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleTableInfo inputRuleTableInfo) {
        RuleTableInfo ruleTableInfo = null;
        if (inputRuleTableInfo != null) {
            ruleTableInfo = new RuleTableInfo();
            ruleTableInfo.setKeyTitle(inputRuleTableInfo.getKeyTitle());
            ruleTableInfo.setValueTitle(inputRuleTableInfo.getValueTitle());
            List<RuleInfo> infoList = new ArrayList<>();
            inputRuleTableInfo.getInfoList().forEach(info -> {
                RuleInfo ruleInfo = new RuleInfo();
                ruleInfo.setKey(info.getKey());
                ruleInfo.setValue(info.getValue());
                infoList.add(ruleInfo);
            });
            ruleTableInfo.setInfoList(infoList);
        }
        return ruleTableInfo;
    }

    public RuleTableInfo getParsedRuleTableInfo(RuleTableInfo ruleTableInfo) {
        if (ruleTableInfo == null || CollectionUtils.isEmpty(ruleTableInfo.getInfoList())) {
            return null;
        }
        // keeping only non-null key value pairs from infoList
        List<RuleInfo> ruleInfoList = ruleTableInfo.getInfoList().stream()
                .filter(ruleInfo -> normalizeStringValues(ruleInfo.getKey()) != null && CollectionUtils.isNotEmpty(normalizeStringValues(ruleInfo.getValue())))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ruleInfoList)) {
            return null;
        }
        RuleTableInfo ruleTableInfoResponse = new RuleTableInfo();
        ruleTableInfoResponse.setKeyTitle(StringUtils.isBlank(ruleTableInfo.getKeyTitle()) ? EMPTY_STRING : ruleTableInfo.getKeyTitle());
        ruleTableInfoResponse.setValueTitle(StringUtils.isBlank(ruleTableInfo.getValueTitle()) ? EMPTY_STRING : ruleTableInfo.getValueTitle());
        ruleTableInfoResponse.setInfoList(ruleInfoList);
        return ruleTableInfoResponse;
    }

    public List<String> normalizeStringValues(List<String> values) {
        return values.stream().map(Utility::normalizeStringValues)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public String normalizeStringValues(String value) {
        if (StringUtils.isBlank(value) || NULL_STRING.equalsIgnoreCase(value)) {
            return null;
        }
        return value;
    }


    private List<AllInclusiveInclusion> buildAllInclusiveBenefits(AllInclusiveData allInclusiveData) {
        List<AllInclusiveInclusion> allInclusiveInclusions = null;
        if (CollectionUtils.isNotEmpty(allInclusiveData.getAllInclusiveBenefits())) {
            allInclusiveInclusions = new ArrayList<>();
            for (Benefits benefits : allInclusiveData.getAllInclusiveBenefits()) {
                AllInclusiveInclusion allInclusiveInclusion = new AllInclusiveInclusion();
                allInclusiveInclusion.setCode(benefits.getCode());
                allInclusiveInclusion.setBulletTexts(benefits.getBulletTexts());
                allInclusiveInclusion.setIconUrl(benefits.getIconUrl());
                allInclusiveInclusion.setImageUrl(benefits.getImageUrl());
                allInclusiveInclusions.add(allInclusiveInclusion);
            }
        }
        return allInclusiveInclusions;
    }

    public CalendarAvailabilityResponse convertCalendarAvailabilityResponse(ConsolidatedCalendarAvailabilityResponse calendarAvailabilityResponseHES, String currency) {
        CalendarAvailabilityResponse calendarAvailabilityResponseCG = new CalendarAvailabilityResponse();
        Map<String, CalendarBO> dates = new LinkedHashMap<>();
        if(MapUtils.isNotEmpty(calendarAvailabilityResponseHES.getDates())) {
            calendarAvailabilityResponseHES.getDates().forEach((date, calendarBOHES) -> {
                if(calendarBOHES!=null) {
                    CalendarBO calendarBO = new CalendarBO();
                    calendarBO.setStatus(calendarBOHES.getStatus()!=null ? calendarBOHES.getStatus().name() : EMPTY_STRING);
                    calendarBO.setPrice(calendarBOHES.getPrice());
                    calendarBO.setPriceColor(reArchUtility.getPriceColorForPriceDrop(calendarBOHES.getPriceVariationType()));
                    dates.put(date, calendarBO);
                }
            });
        }
        calendarAvailabilityResponseCG.setDates(dates);
        calendarAvailabilityResponseCG.setCurrency(currency);
        return calendarAvailabilityResponseCG;
    }

    protected AllInclusiveCard buildAllInclusiveCard(HotelDetails hotelDetails) {
        AllInclusiveCard card = new AllInclusiveCard();
        card.setData(allInclusiveCard.getData());
        card.setDesc(allInclusiveCard.getDesc());
        card.setTitle(allInclusiveCard.getTitle());
        card.setPersuasionText(allInclusiveCard.getPersuasionText());
        card.setImageUrl(allInclusiveCard.getImageUrl());
        card.setRatePlanCode(hotelDetails.getAllInclusiveData().getAllInclusiveRpc());
        card.setRoomCode(hotelDetails.getAllInclusiveData().getAllInclusiveRoomCode());
        if (!hotelDetails.getHotelRateFlags().isLowestRateAllInclusive()) {
            card.setAmount(hotelDetails.getAllInclusiveData().getAmount());
        }

        return card;
    }

    private void updateSupportDetails(SearchRoomsResponse searchRoomsResponse) {
        SupportDetails supportDetails = searchRoomsResponse.getSupportDetails() != null ? searchRoomsResponse.getSupportDetails() : new SupportDetails();
        supportDetails.setIconUrl(callToBookIconUrl);
        supportDetails.setTitle(callToBookTitle);
        List<String> options = CollectionUtils.isNotEmpty(supportDetails.getOptions()) ? supportDetails.getOptions() : new ArrayList<>();
        options.add(callToBookOption);
        supportDetails.setOptions(options);
        searchRoomsResponse.setSupportDetails(supportDetails);
    }

    /**
     * Build instant fare details if any of the rates is a negotiated rate and set instantFareInfo field of searchRoomsResponse.
     *
     * @param corpAlias             Organisation alias name.
     * @param searchRoomsResponse   {@link SearchRoomsResponse} object.
     * @param currency              User selected currency.
     */
    private void buildInstantFareInfo(String corpAlias, SearchRoomsResponse searchRoomsResponse, String currency) {
        InstantFareInfo instantFareInfo = new InstantFareInfo();

        String title;

        if (CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
            title = polyglotService.getTranslatedData(ConstantsTranslation.INSTANT_FARE_INFO_MYB_NEW_DETAILS_TITLE);
        } else {
            title = polyglotService.getTranslatedData(INSTANT_FARE_INFO_TITLE_MOBILE);
        }
        corpAlias = corpAlias != null ? corpAlias : StringUtils.EMPTY;
        title = StringUtils.replace(title, "{CORP_ALIAS}", corpAlias);
        title = StringUtils.replace(title, "{NO_OF_HOURS}", String.valueOf(noOfHoursForConfirmation));
        instantFareInfo.setTitle(title);

        instantFareInfo.setHeader(polyglotService.getTranslatedData(INSTANT_FARE_INFO_HEADER));

        String subHeader = polyglotService.getTranslatedData(INSTANT_FARE_INFO_SUBHEADER);
        // Get currency symbol based on user selected currency.
        String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(currency) ? currency : DEFAULT_CUR_INR).getCurrencySymbol();
        subHeader = StringUtils.replace(subHeader, "{CURRENCY_SYMBOL}", currencySymbol);
        instantFareInfo.setSubheader(subHeader);

        Cta cta = new Cta();
        cta.setTitle(polyglotService.getTranslatedData(INSTANT_BOOKING_FARES));
        cta.setProceedText(polyglotService.getTranslatedData(PROCEED_BOOKING_TEXT));
        List<FiltersData> filtersDataList = new ArrayList<>();
        addFiltersData(filtersDataList, polyglotService.getTranslatedData(INSTANT_BOOKING), Constants.INSTANT_BOOKING);
        cta.setFiltersData(filtersDataList);
        instantFareInfo.setCta(cta);

        searchRoomsResponse.setSpecialFareInfo(instantFareInfo);
    }

    /**
     * Build filtersData from title, code and add it to {@code filtersDataList}.
     *
     * @param filtersDataList Collection of filtersData.
     * @param title           title for filtersData.
     * @param code            code for filtersData.
     */
    private void addFiltersData(List<FiltersData> filtersDataList, String title, String code) {
        FiltersData filtersData = new FiltersData();
        filtersData.setTitle(title);
        filtersData.setCode(code);
        filtersDataList.add(filtersData);
    }


    private ResponseContextDetail getContextDetails(HotelDetails hotelDetails) {
        ResponseContextDetail responseContextDetail = new ResponseContextDetail();
        responseContextDetail.setCurrency(hotelDetails.getCurrencyCode());
        responseContextDetail.setMmtHotelCategory(hotelDetails.getHotelCategory());
        return responseContextDetail;
    }

    private FeatureFlags getFeatureFlags(HotelDetails hotelDetails, List<RoomDetails> rooms, LinkedHashMap<String, String> expDataMap) {
        if (hotelDetails == null)
            return null;
        HotelRateFlags hotelRateFlags = hotelDetails.getHotelRateFlags();
        FeatureFlags featureFlags = new FeatureFlags();
        featureFlags.setBestPriceGuaranteed(hotelRateFlags.isBestPriceGuaranteed());
        featureFlags.setBnpl(hotelRateFlags.isBnplAvailable());
        featureFlags.setBnplBaseAmount(hotelDetails.getAdditionalDetails().getBnplBaseAmount());
        featureFlags.setFirstTimeUser(hotelRateFlags.isFirstTimeUser());
        featureFlags.setFreeCancellation(hotelRateFlags.isFreeCancellationAvailable());
        featureFlags.setPahAvailable(hotelRateFlags.isPahAvailable());
        featureFlags.setPahTariffAvailable(hotelRateFlags.isPahAvailable());
        if (Constants.DEVICE_OS_PWA.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
            featureFlags.setPwaDetailSelectMerge(hotelRateFlags.isDetailSelectMerge());
        }
        featureFlags.setPahWalletApplicable(hotelRateFlags.isPahWalletApplicable());
        featureFlags.setRequestToBook(hotelRateFlags.isRequestToBook());
        featureFlags.setRtbPreApproved(hotelRateFlags.isRtbPreApproved());
        featureFlags.setRtbAutoCharge(hotelRateFlags.isRtbAutoCharge());
        Set<String> payModes = new HashSet<>();
        if (CollectionUtils.isNotEmpty(rooms)) {
            for (RoomDetails roomDetail : rooms) {
                if (CollectionUtils.isNotEmpty(roomDetail.getRatePlans())) {
                    for (SelectRoomRatePlan ratePlan : roomDetail.getRatePlans()) {
                        payModes.add(ratePlan.getPayMode());
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(payModes))
                featureFlags.setPayModes(payModes);
        }
        if (!utility.isExperimentTrue(expDataMap, Constants.GROUP_FUNNEL_ENHANCEMENT_EXP))
            featureFlags.setGroupBookingPrice(hotelRateFlags.isGroupBookingPrice());
        else
            featureFlags.setGroupBookingPrice(false); // Explicit marking this as false, In case of experiment is groupFunnelEnhancement enabled
        featureFlags.setMaskedPrice(hotelRateFlags.isMaskedPrice());
        featureFlags.setOptimisedSelection(utility.isOHSExpEnable(hotelDetails.getPropertyType(), expDataMap));
        featureFlags.setMyPartnerMoveToTdsTaxStructure(MapUtils.isNotEmpty(expDataMap) && TRUE.equalsIgnoreCase(expDataMap.get(MY_PARTNER_MOVE_TO_TDS_TAX_STRUCTURE)));
        /*
        //TODO Add for mypartner
        if(hotelDetails.getAdditionalDetails().getBnplBaseAmount()!=null) {
            featureFlags.setHotelFareHold(true);
        }*/
        return featureFlags;
    }

    private int getRoomTypeCount(HotelDetails hotelDetails) {
        return hotelDetails.getRooms() != null && CollectionUtils.isNotEmpty(hotelDetails.getRooms()) ? hotelDetails.getRooms().size() : 0;
    }

    private String buildPropertySellableType(HotelDetails hotelDetails) {
        String propertySellableType = "Stay";
        if (isPropertyHotelOrResort(hotelDetails)) {
            propertySellableType = "Room";
        }
        return propertySellableType;
    }

    private boolean isPropertyHotelOrResort(HotelDetails hotelDetails) {
        String titleHotel = polyglotService.getTranslatedData(HOTEL_TITLE);
        String titleResort = polyglotService.getTranslatedData(RESORT_TITLE);
        titleHotel = StringUtils.isNotEmpty(titleHotel) ? titleHotel : "hotel";
        titleResort = StringUtils.isNotEmpty(titleResort) ? titleResort : "resort";
        return titleHotel.equalsIgnoreCase(hotelDetails.getPropertyType()) || titleResort.equalsIgnoreCase(hotelDetails.getPropertyType());
    }

    private PrimaryOffer getPrimaryOffer(com.gommt.hotels.orchestrator.detail.model.response.persuasion.PrimaryOffer orchPrimaryOffer) {
        return "CAMPAIGN".equalsIgnoreCase(orchPrimaryOffer.getType()) ? getPrimaryOfferForSaleCampaign(orchPrimaryOffer) : setPrimaryOfferFieldsForSupplier(orchPrimaryOffer);
    }

    private PrimaryOffer setPrimaryOfferFieldsForSupplier(com.gommt.hotels.orchestrator.detail.model.response.persuasion.PrimaryOffer orchPrimaryOffer) {
        PrimaryOffer primaryOffer = new PrimaryOffer();
        Style style = new Style();
        style.setBgColor(supplierBgColor);
        primaryOffer.setStyle(style);
        primaryOffer.setDescription(orchPrimaryOffer.getDesc());
        //primaryOffer.setExpiry(orchPrimaryOffer.getExpiry());
        primaryOffer.setType(polyglotService.getTranslatedData(SUPPLIER));
        primaryOffer.setIconUrl(EARLY_BIRD.equalsIgnoreCase(orchPrimaryOffer.getType()) ? earlyBirdIconUrl : lastMinuteIconUrl);
        return primaryOffer;
    }


    private PrimaryOffer getPrimaryOfferForSaleCampaign(com.gommt.hotels.orchestrator.detail.model.response.persuasion.PrimaryOffer orchPrimaryOffer) {
        PrimaryOffer primaryOffer = new PrimaryOffer();
        primaryOffer.setDescription(orchPrimaryOffer.getDesc());
        primaryOffer.setIconUrl(orchPrimaryOffer.getIconUrl());
        primaryOffer.setType(SALE_CAMPAIGN);
        Style style = new Style();
        style.setBgColor(orchPrimaryOffer.getHeadingColor());
        primaryOffer.setStyle(style);
        return primaryOffer;
    }

    private void buildAlternatePriceCard(com.gommt.hotels.orchestrator.detail.model.response.HotelDetails hotelDetails,
                                         SearchRoomsResponse searchRoomsResponse,
                                         SearchRoomsRequest searchRoomsRequest) {
        if (CollectionUtils.isEmpty(hotelDetails.getAlternateDatePriceDetails())) {
            return;
        }
        // Use the clientgateway AlternatePriceCard instead of orchestrator one
        com.mmt.hotels.clientgateway.response.AlternatePriceCard priceCard = new com.mmt.hotels.clientgateway.response.AlternatePriceCard();
        List<PriceCardDetail> priceDetails = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.pricing.AlternatePriceCard orchestratorCard :
                hotelDetails.getAlternateDatePriceDetails()) {
            PriceCardDetail priceDetail = new PriceCardDetail();
            // AlternatePriceCard has checkIn/checkOut and price fields based on the search results
            DateRange dateRange = new DateRange();
            dateRange.setCheckIn(orchestratorCard.getCheckIn());
            dateRange.setCheckOut(orchestratorCard.getCheckOut());
            priceDetail.setDateRange(dateRange);

            priceDetail.setPrice(orchestratorCard.getPrice());
            priceDetail.setCurrency(orchestratorCard.getCurrency());
            priceDetail.setCheaper(orchestratorCard.isCheaper());
            priceDetail.setSelected(orchestratorCard.isSelected());

            priceDetails.add(priceDetail);
        }
        priceCard.setData(priceDetails);
        priceCard.setHeading(polyglotService.getTranslatedData("ALTERNATE_DATES_TITLE"));
        searchRoomsResponse.setAlternatePriceCard(priceCard);
    }

    private List<OfferDetail> transformOffers(List<com.gommt.hotels.orchestrator.detail.model.response.common.RangePrice> offers) {
        if (CollectionUtils.isEmpty(offers)) {
            return null;
        }

        List<OfferDetail> offerDetails = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.common.RangePrice offer : offers) {
            OfferDetail offerDetail = new OfferDetail();
            offerDetail.setLongText(offer.getLongText());
            offerDetail.setOfferType(offer.getOfferType());
            offerDetail.setPriority(offer.getPriority());
            offerDetail.setShortText(offer.getShortText());
            offerDetail.setTncLink(offer.getTncLink());
            offerDetail.setIconUrl(offer.getIconUrl());
            offerDetails.add(offerDetail);
        }
        return offerDetails;
    }

    private Map<String, AddOnDetails> transformAddOns(com.gommt.hotels.orchestrator.detail.model.response.pricing.AddOns addOns) {
        // Delegate to AddOnHelper for complete addOns transformation
        return addOnHelper.transformAddOns(addOns);
    }

    private List<RoomDetails> transformRoomsToRoomDetails(List<Rooms> rooms,
                                                          HotelDetails hotelDetails, String expData,
                                                          String askedCurrency, String funnelSource, int los, int ap,
                                                          boolean isBlockPAH, CommonModifierResponse commonModifierResponse,
                                                          Map<String, String> ratePlanCodeAndNameMap, boolean isLuxeHotel,
                                                          boolean isAltAccoHotel, boolean isOHSExpEnable,
                                                          String propertyType,
                                                          String countryCode, String siteDomain, String selectedRoomCode,
                                                          String selectedRateplanCode, boolean isHighSellingAltAcco) {
        List<RoomDetails> roomDetailsList = new ArrayList<>();
        boolean isNewDetailPageDesktop = commonModifierResponse != null && utility.isExperimentOn(commonModifierResponse.getExpDataMap(), NEW_DETAIL_PAGE_DESKTOP_EXP);
        boolean amendRoomHighlights = utility.showHighlightsForRoomAmenities(countryCode, funnelSource);
        boolean  pilgrimageBedInfoEnable = utility.isExperimentOn(utility.getExpDataMap(expData), ExperimentKeys.PILGR_IMAGE_BED_INFO.getKey());
        String corpAlias = hotelDetails.getCorpMetaInfo() != null ? hotelDetails.getCorpMetaInfo().getAlias() : "";
        if (CollectionUtils.isEmpty(rooms)) {
            return roomDetailsList;
        }

        for (Rooms room : rooms) {
            String sellableType = SELLABLE_UNIT_ROOM;
            // Determine room type flags based on OrchV2 data
            boolean isPackageRoom = isSuperPackageRoom(room) || isRecommendedRoom(room) || isOccasionRoom(room);
            RoomInfo roomInfo = room.getRoomInfo();

            // Create appropriate RoomDetails type based on flags (following legacy pattern)
            RoomDetails roomDetails = new RoomDetails();
            if (isPackageRoom) {
                roomDetails = new PackageRoomDetails();
                ((PackageRoomDetails) roomDetails).setType(room.getType());
                if (isSuperPackageRoom(room)) {
                    setFilterDetails((PackageRoomDetails) roomDetails);
                }
            }
            // TODO: Add meal plan logic similar to legacy

            roomDetails.setRoomCode(room.getCode());
            roomDetails.setRoomName(room.getName());
            roomDetails.setBaseRoom(room.isBaseRoom());
            roomDetails.setRoomCategoryText(LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelDetails.getListingType())
                    ? (hotelDetails.getPropertyType() != null ? hotelDetails.getPropertyType() : SELLABLE_ROOM_TYPE)
                    : (roomInfo != null && roomInfo.getSellableType() != null ? roomInfo.getSellableType() : SELLABLE_ROOM_TYPE));

            // Set room images from OrchV2 Media (equivalent to legacy HotelImage logic)
            List<String> roomImages = searchRoomsMediaHelper.extractRoomImagesFromMedia(hotelDetails.getMedia(), room.getCode());
            roomDetails.setImages(searchRoomsMediaHelper.extractRoomImagesFromMedia(hotelDetails.getMedia(), room.getCode()));

            // Set 360 images from OrchV2 Media (equivalent to legacy HotelImage.getImages360 logic)
            roomDetails.setView360(searchRoomsMediaHelper.extract360ImagesFromMedia(hotelDetails.getMedia(), room.getCode()));

            // Set media data from OrchV2 Media and room images (simplified - no video support in OrchV2)
            roomDetails.setMedia(searchRoomsMediaHelper.populateMedia(null, roomImages, room.getCode()));


            if (roomInfo != null) {
                sellableType = StringUtils.isNotEmpty(roomInfo.getSellableType()) ? roomInfo.getSellableType() : sellableType;
                // Set room highlights - adapted for OrchV2 (some parameters not available)
                roomDetails.setRoomHighlights(roomInfoHelper.transformRoomHighlights(roomInfo, null, hotelDetails.getHotelRateFlags().isAltAcco(), isOHSExpEnable, roomInfo.getAmenities(), countryCode, amendRoomHighlights, pilgrimageBedInfoEnable));
                roomDetails.setRoomSize(StringUtils.isNotEmpty(roomInfo.getRoomSize()) ? roomInfo.getRoomSize() : null);
                roomDetails.setRoomInfoText(buildRoomInfoText(room, roomInfo.getRoomSize(), askedCurrency));
                roomDetails.setMaxGuest(roomInfo.getMaxGuestCount());
                roomDetails.setMaxGuestCount(roomInfo.getMaxGuestCount());
                roomDetails.setMaxAdult(roomInfo.getMaxAdultCount());
                roomDetails.setMaxAdultCount(roomInfo.getMaxAdultCount());
                roomDetails.setMaxChild(roomInfo.getMaxChildCount());
                roomDetails.setMaxChildCount(roomInfo.getMaxChildCount());
                roomDetails.setExtraBedCount(roomInfo.getExtraBedCount());
                roomDetails.setBathroomCount(roomInfo.getRoomInfoExtension() != null ? roomInfo.getRoomInfoExtension().getBathroomCount() : 0);
                roomDetails.setBedCount(roomInfo.getBedCount());
                roomDetails.setBedroomCount(roomInfo.getBedRoomCount());
                roomDetails.setMaster(roomInfo.getRoomFlags().isMasterRoom());
                roomDetails.setParentRoomCode(roomInfo.getParentRoomCode());
                roomDetails.setBeds(RoomInfoHelper.getSleepingArrangements(roomInfo));
                roomDetails.setRoomViewName(roomInfo.getRoomViewName());
                roomDetails.setDescription(roomInfo.getDesc());

                // Handle private space data transformation - Room Level
                // Condition: Check if space details are required and not high selling alt acco
                if (CollectionUtils.isNotEmpty(roomInfo.getSpaces()) && !isHighSellingAltAcco && roomInfo.getRoomFlags().isSpaceDetailsRequired()) {

                    // Get private spaces from room info (following legacy pattern)
                    com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData privateSpaceData =
                            roomInfo.getSpaces().stream()
                                    .filter(space -> space.getType() == com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData.Type.PRIVATE)
                                    .findFirst()
                                    .orElse(null);

                    if (privateSpaceData != null) {
                        // Use experiment EXP_PLV2 to decide between V1 and V2 (following legacy pattern)
                        if (commonModifierResponse != null && utility.isExperimentOn(commonModifierResponse.getExpDataMap(), EXP_PLV2)) {
                            roomDetails.setPrivateSpacesV2(roomInfoHelper.getSpaceDataV2(privateSpaceData, true));
                        } else {
                            roomDetails.setPrivateSpaces(roomInfoHelper.getSpaceData(privateSpaceData, commonModifierResponse));
                        }
                    }
                }
                roomDetails.setAmenities(roomAmentiesHelper.buildAmenities(roomInfo, false));
                //roomDetails.setHighlightedAmenities(roomAmentiesHelper.buildFacilityHighlights(roomInfo.getHighlightedAmenities()));
                roomDetails.setBaseGuest(roomDetails.getPrivateSpaces() != null ? roomDetails.getPrivateSpaces().getBaseGuests() : 0);
                roomDetails.setRoomSummary(roomInfoHelper.buildRoomSummary(roomInfo.getRoomInfoExtension()));
                roomDetails.setPropertyLayoutInfo(roomInfoHelper.buildPropertyLayoutInfo(room));
            }

            // Transform rate plans
            if (CollectionUtils.isNotEmpty(room.getRatePlans())) {
                List<SelectRoomRatePlan> ratePlans = transformRatePlans(room.getRatePlans(), sellableType, expData, room, hotelDetails,
                        askedCurrency, funnelSource, los, ap, isBlockPAH, commonModifierResponse,
                        ratePlanCodeAndNameMap, isLuxeHotel);
                addSpecialFarePersuasion(ratePlans, corpAlias);
                roomDetails.setRatePlans(ratePlans);
            }
            roomDetails.setClientViewType(getTariffViewType(roomDetails, hotelDetails.getStarRating(), roomDetailsList.isEmpty(), commonModifierResponse.getExpDataMap()));
            roomDetails.setRoomPersuasions(room.getPersuasions());
            roomInfoHelper.buildStayDetails(roomDetails);
            if(anyPackageRateAvailable(room)) {
                roomDetails.setHighlightImage(superPackageIconUrlSecondary);
            }
            // Set package room specific information (following legacy pattern)
            if (isPackageRoom) {
                setPackageRoomSpecificInfo((PackageRoomDetails) roomDetails, room, setSuperPackagePersuasion(commonModifierResponse), askedCurrency, room.getSubType(), isNewDetailPageDesktop);
            }

            roomDetailsList.add(roomDetails);
        }

        return roomDetailsList;
    }

    private void addSpecialFarePersuasion(List<SelectRoomRatePlan> ratePlans, String corpAlias) {
        if (CollectionUtils.isNotEmpty(ratePlans)) {
            SelectRoomRatePlan ratePlan = ratePlans.get(0);
            if (ratePlan.isSpecialFare()) {
                List<PersuasionResponse> persuasions = ratePlan.getPersuasions();
                if (persuasions == null) {
                    persuasions = new ArrayList<>();
                }
                PersuasionResponse delayedConfirmationPersuasion = buildDelayedConfirmationPersuasion(corpAlias, true);
                CollectionUtils.addIgnoreNull(persuasions, delayedConfirmationPersuasion);
            }

            if (corpPreferredRateSegmentId != null && corpPreferredRateSegmentId.equals(ratePlan.getSegmentId())) {
                List<PersuasionResponse> persuasions = ratePlan.getPersuasions();
                if (persuasions == null) {
                    persuasions = new ArrayList<>();
                }
                PersuasionResponse specialFarePersuasion = buildSpecialFareTagPersuasion(corpAlias);
                CollectionUtils.addIgnoreNull(persuasions, specialFarePersuasion);
            }
        }
    }

    private boolean anyPackageRateAvailable(Rooms room) {
        return CollectionUtils.isNotEmpty(room.getRatePlans()) && room.getRatePlans().stream().anyMatch(ratePlan -> ratePlan.getRatePlanFlags().isPackageRatePlan());
    }

    private String buildRoomInfoText(Rooms rooms, String roomSize, String askedCurrency) {
        String roomInfoText = null;
        if (StringUtils.isNotEmpty(roomSize) && CollectionUtils.isNotEmpty(rooms.getRatePlans()) && rooms.getRatePlans().get(0).getPrice() != null) {
            int minPrice = (int) rooms.getRatePlans().get(0).getPrice().getDisplayPrice();
            if (minPrice != Integer.MAX_VALUE) {
                DecimalFormat decimalFormat = new DecimalFormat("#,###");
                String minPriceString = decimalFormat.format(minPrice);
                String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : DEFAULT_CUR_INR).getCurrencySymbol();
                roomInfoText = STARTS_AT + SPACE + currencySymbol + SPACE + minPriceString + PIPE_SEPARATOR + roomSize + SPACE + SQUARE_FEET;
            }
        }
        return roomInfoText;
    }


    //Copied from legacy code
    private TariffViewType getTariffViewType(RoomDetails roomDetails, Integer starRating, boolean isFirstRoom, Map<String, String> expDataMap) {
        int totalRatePlans = roomDetails.getRatePlans().size();

        TariffViewType clientViewType = new TariffViewType();
        clientViewType.setTotalTariffs(roomDetails.getRatePlans().size());
        clientViewType.setBaseTariffText(polyglotService.getTranslatedData(ConstantsTranslation.STARTING_PRICE_AT));
        if (totalRatePlans == 1) {
            clientViewType.setInitialVisible(1);
            return clientViewType;
        }

        /* Search-Rooms UAT JIRA HTL-29300 */
        if (MapUtils.isNotEmpty(ratePlanDisplayLogic) && MapUtils.isNotEmpty(expDataMap)) {
            Map<String, Integer> map;
            if (("true").equalsIgnoreCase(expDataMap.get("Premium_SR")) && starRating != null && starRating >= 4) {
                if (("true").equalsIgnoreCase(expDataMap.get("Room_Count_SR"))) {
                    /* Premium_SR = true && Room_Count_SR = true */
                    map = ratePlanDisplayLogic.get("PREMIUM").get(getKeyAccordingToCount(ratePlanDisplayLogic.get("PREMIUM"), totalRatePlans));
                } else {
                    /* Premium_SR = true && Room_Count_SR = false */
                    map = ratePlanDisplayLogic.get("PREMIUM").get("DEFAULT");
                }
                if (isFirstRoom) {
                    clientViewType.setInitialVisible(map.get("FIRST_ROOM_RPC_COUNT"));
                } else {
                    clientViewType.setInitialVisible(map.get("OTHER_ROOM_RPC_COUNT"));
                }
            } else if (("false").equalsIgnoreCase(expDataMap.get("Premium_SR")) || (("true").equalsIgnoreCase(expDataMap.get("Premium_SR")) && starRating != null && starRating < 4)) {
                if (("true").equalsIgnoreCase(expDataMap.get("Room_Count_SR"))) {
                    /* Premium_SR = false && Room_Count_SR = true */
                    map = ratePlanDisplayLogic.get("BUDGET").get(getKeyAccordingToCount(ratePlanDisplayLogic.get("BUDGET"), totalRatePlans));
                } else {
                    /* Premium_SR = false && Room_Count_SR = false */
                    map = ratePlanDisplayLogic.get("BUDGET").get("DEFAULT");
                }
                if (isFirstRoom) {
                    clientViewType.setInitialVisible(map.get("FIRST_ROOM_RPC_COUNT"));
                } else {
                    clientViewType.setInitialVisible(map.get("OTHER_ROOM_RPC_COUNT"));
                }
            } else {
                clientViewType.setInitialVisible(ratePlanMoreOptionsLimit);
            }
        } else {
            clientViewType.setInitialVisible(ratePlanMoreOptionsLimit);
        }
        /* Search-Rooms UAT JIRA HTL-29300 */

        /* Check and correct initialVisibleCount for case when : (initialVisibleCount > TotalTariffsCount) */
        if (clientViewType.getInitialVisible() > clientViewType.getTotalTariffs())
            clientViewType.setInitialVisible(clientViewType.getTotalTariffs());

        return clientViewType;
    }

    private String getKeyAccordingToCount(Map<String, Map<String, Integer>> map, int roomCount) {
        String key = "DEFAULT";
        if (map.containsKey(String.valueOf(roomCount))) {
            key = String.valueOf(roomCount);
        } else {
            List<String> list = new ArrayList<>(map.keySet());
            List<Integer> arr = list.stream().filter(NumberUtils::isCreatable).map(Integer::valueOf).sorted(Comparator.comparingInt(Integer::intValue)).collect(Collectors.toList());
            Optional<Integer> k = arr.stream().filter(i -> i > roomCount).findFirst();
            if (k.isPresent()) {
                key = String.valueOf(k.get());
            } else if (CollectionUtils.isNotEmpty(arr) && (arr.get(arr.size() - 1) < roomCount)) {
                key = String.valueOf(arr.get(arr.size() - 1));
            }
        }
        return key;
    }

    private List<SelectRoomRatePlan> transformRatePlans(List<RatePlan> ratePlans, String sellableType, String expData, Rooms room, HotelDetails hotelDetails,
                                                        String askedCurrency, String funnelSource, int los, int ap,
                                                        boolean isBlockPAH, CommonModifierResponse commonModifierResponse,
                                                        Map<String, String> ratePlanCodeAndNameMap, boolean isLuxeHotel) {
        List<SelectRoomRatePlan> selectRatePlans = new ArrayList<>();

        Map<String, String> experimentDataMap = utility.getExpDataMap(expData);
        boolean isPremiumExperienceApplicable = utility.isExperimentOn(experimentDataMap, SUPER_PREMIUM_EXPERIENCE) &&  hotelDetails!=null && searchRoomsPriceHelper.isLuxOrPremCategory(hotelDetails.getCategories());

        // Simple transformation: OrchV2 has already done RPC grouping, so just transform each rate plan
        for (RatePlan ratePlan : ratePlans) {
            SelectRoomRatePlan selectRatePlan = new SelectRoomRatePlan();

            // Basic rate plan information
            selectRatePlan.setSupplierCode(ratePlan.getTrackingInfo().getSupplierCode());
            selectRatePlan.setRpc(ratePlan.getCode()); // OrchV2 has already set this as RPC key
            selectRatePlan.setVendorRatePlanCode(ratePlan.getRpcc());
            selectRatePlan.setName(buildRatePlanName(ratePlan, sellableType, hotelDetails.getListingType(), expData));
            selectRatePlan.setFilterCode(new ArrayList<>());
            // TODO: setDescription not available in SelectRoomRatePlan
            // selectRatePlan.setDescription(ratePlan.getDescription());

            // Store rate plan name in map
            ratePlanCodeAndNameMap.put(ratePlan.getCode(), selectRatePlan.getName());

            // Set payment mode
            selectRatePlan.setPayMode(ratePlan.getPaymentMode() != null ? ratePlan.getPaymentMode().name() : null);
            selectRatePlan.setMealDetailCode(ratePlan.getMealConfigCode());
            selectRatePlan.setRatePlanType(ratePlan.getRateType());
            selectRatePlan.setSellableType(sellableType);
            selectRatePlan.setSellableText(SELLABLE_ROOM_TYPE);

            // Transform inclusions with system-generated inclusions
            selectRatePlan.setInclusionsList(transformInclusions(ratePlan.getInclusions(), ratePlan, experimentDataMap, ap, isBlockPAH,isPremiumExperienceApplicable));

            // Set experience inclusions for premium experience
            ExperienceInclusions expIncl = createExperienceInclusionCardForOrchV2(ratePlan.getInclusions(),experimentDataMap,isPremiumExperienceApplicable);
            if (expIncl != null) {
                selectRatePlan.setExperienceInclusions(expIncl);
            }

            // Transform cancellation policy
            if (ratePlan.getCancellationPolicy() != null) {
                selectRatePlan.setCancellationPolicy(cancellationPolicyHelper.transformCancellationPolicy(ratePlan.getCancellationPolicy(), ap));
                // Set cancellation timelines directly from orchestrator v2
                if (ratePlan.getCancellationPolicy().getCancellationTimeline() != null) {
                    selectRatePlan.setCancellationTimeline(cancellationPolicyHelper.buildCancellationTimelineFromOrchV2(ratePlan.getCancellationPolicy().getCancellationTimeline(), ratePlan.getBnplDetails()));
                    boolean enableThemification = commonModifierResponse != null && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), THEMIFICATION_ENABLED);
                    selectRatePlan.setCancellationPolicyTimeline(cancellationPolicyHelper.buildCancellationPolicyTimelineFromOrchV2(ratePlan.getCancellationPolicy().getCancellationTimeline(), enableThemification));
                }
            }

            // Create multiple tariffs from pricePerOccupancy data (OrchV2 has grouped the rate plans)
            List<Tariff> tariffs = buildTariffs(ratePlan, room, askedCurrency, expData, sellableType, los, funnelSource, commonModifierResponse, hotelDetails);
            selectRatePlan.setTariffs(tariffs);

            // Set review deeplink URL with mpn parameter
            if (StringUtils.isNotEmpty(ratePlan.getReviewDeeplinkUrl())) {
                selectRatePlan.setReviewDeeplinkUrl(ratePlan.getReviewDeeplinkUrl());
                if (!ratePlan.getReviewDeeplinkUrl().contains("mpn")) {
                    String reviewDeeplinkUrl = ratePlan.getReviewDeeplinkUrl();
                    boolean maskedPropertyName = hotelDetails.getHotelRateFlags() != null && hotelDetails.getHotelRateFlags().isMaskedPropertyName();
                    selectRatePlan.setReviewDeeplinkUrl(reviewDeeplinkUrl.concat(Constants.AND_SEPARATOR + "mpn" + Constants.PR_SEPARATOR + maskedPropertyName));
                }
            }

            if (ratePlan.getPrice() != null && ratePlan.getPrice().getDiscount() != null && ratePlan.getPrice().getDiscount().getHotel() > 0
                    && StringUtils.isNotEmpty(ratePlan.getPrice().getDiscount().getHCPEncrypted())) {
                selectRatePlan.setHCP(ratePlan.getPrice().getDiscount().getHCPEncrypted());
            }
            selectRatePlan.setAdditionalFees(transformAdditionalFees(hotelDetails, ratePlan, commonModifierResponse, commonModifierResponse.getExpDataMap(), room.getName()));

            // Transform addOns from OrchV2 to CG format (matching legacy logic)
            if (ratePlan.getAddOns() != null) {
                Map<String, AddOnDetails> addOnDetailsMap = transformAddOns(ratePlan.getAddOns());

                // Convert Map<String, AddOnDetails> to List<com.mmt.hotels.model.response.addon.AddOnNode>
                // (matching the format expected by commonResponseTransformer.getAddons)
                List<com.mmt.hotels.model.response.addon.AddOnNode> addOnNodeList = new ArrayList<>();
                for (Map.Entry<String, AddOnDetails> entry : addOnDetailsMap.entrySet()) {
                    com.mmt.hotels.model.response.addon.AddOnNode addOnNode = new com.mmt.hotels.model.response.addon.AddOnNode();
                    addOnNode.setAddOnType(entry.getKey());
                    // Convert AddOnDetails to the format expected by AddOnNode
                    BeanUtils.copyProperties(entry.getValue(), addOnNode);
                    addOnNodeList.add(addOnNode);
                }

                // Use CommonResponseTransformer to convert to final CG response format (matching legacy logic)
                List<com.mmt.hotels.clientgateway.request.payment.AddOnNode> cgAddOnNodeList = commonResponseTransformer.getAddons(addOnNodeList);
                selectRatePlan.setAddons(cgAddOnNodeList);

            }
            selectRatePlan.setSpecialFare(RTB_EMAIL.equalsIgnoreCase(ratePlan.getRtbEmail()));

            // Set persuasions
            selectRatePlan.setPersuasions(searchRoomsPersuasionHelper.getRatePlanPersuasion(selectRatePlan, ratePlan, funnelSource, commonModifierResponse, isLuxeHotel, null));
            selectRatePlan.setRatePlanPersuasionsMap(searchRoomsPersuasionHelper.buildRatePlanPersuasionsMap(ratePlan, commonModifierResponse, null));
            selectRatePlan.setFilterCode(getFilterCodes(ratePlan, isBlockPAH, ap, commonModifierResponse, room.getName()));
            selectRatePlan.setSegmentId(ratePlan.getSegmentId());
            selectRatePlan.setPackageRateAvailable(ratePlan.getRatePlanFlags().isPackageRatePlan());
            selectRatePlan.setAllInclusiveRate(ratePlan.getRatePlanFlags().isAllInclusiveRate());
            selectRatePlan.setCorpApprovalInfo(buildCorpApprovalInfo(ratePlan.getCorpMetaData(), utility.isTcsV2FlowEnabled(expData)));
            Utility.setCanTranslateFlag(selectRatePlan, translateEnabledSupplierCodes, ratePlan.getTrackingInfo().getSupplierCode());
            setRatePlanDetailsCta(ratePlan, selectRatePlan);
            selectRatePlan.setHotelCloudData(buildHotelCloudDataForDetail(ratePlan.getRatePlanFlags() != null && ratePlan.getRatePlanFlags().isHotelCloud()));
            selectRatePlans.add(selectRatePlan);
        }

        return selectRatePlans;
    }

    private HotelCloudData buildHotelCloudDataForDetail(boolean hotelCloud) {
        return hotelCloud ? HotelCloudData.builder()
                .persuasionIcon(HOTEL_CLOUD_DESKTOP_PERSUASION_ICON)
                .persuasionText(polyglotService.getTranslatedData(HOTEL_CLOUD_TITLE_TEXT))
                .build() : null;
    }

    private void setRatePlanDetailsCta(RatePlan ratePlanOrch, SelectRoomRatePlan ratePlan) {
        ratePlan.setShowRatePlanDetailsCta(Objects.isNull(ratePlanOrch) || !ratePlanOrch.getRatePlanFlags().isMealAvailableAtProperty() || Objects.isNull(ratePlan.getCancellationPolicy())
                || ratePlan.getCancellationPolicy().getType() != BookedCancellationPolicyType.NR);
    }


    private List<Tariff> buildTariffs(RatePlan ratePlan, Rooms room, String askedCurrency, String expData, String sellableType,
                                      int los, String funnelSource, CommonModifierResponse commonModifierResponse, HotelDetails hotelDetails) {
        boolean isAltAccoHotel = hotelDetails.getHotelRateFlags().isAltAcco();
        boolean isHighSellingAltAcco = hotelDetails.getHotelRateFlags().isHighSellingAltAcco();
        List<Tariff> tariffs = new ArrayList<>();
        boolean groupBookingPrice = false; // TODO: Get from request context
        boolean myPartner = commonModifierResponse != null && commonModifierResponse.getExtendedUser() != null &&
                Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(),
                        commonModifierResponse.getExtendedUser().getAffiliateId());
        MarkUpDetails markUpDetails = hotelDetails.getMarkUpDetails();
        boolean newPropertyOfferApplicable = commonModifierResponse != null &&
                utility.isExperimentOn(commonModifierResponse.getExpDataMap(), ExperimentKeys.NEW_PROPERTY_OFFER.getKey());

        NoCostEmiDetails noCostEmiDetailForRootLevel = new NoCostEmiDetails();

        if (ratePlan.getPrice() != null && CollectionUtils.isNotEmpty(ratePlan.getPrice().getPricePerOccupancy())) {
            // Check if this is an occupancy room (similar to OrchV2 logic)
            boolean isOccupancyRoom = isOccupancyRoomType(room);
            if (isOccupancyRoom) {
                // OCCUPANCY ROOMS: Create one tariff for each occupancy detail (current logic - CORRECT)
                for (com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails occupancyDetail : ratePlan.getPrice().getPricePerOccupancy()) {
                    List<OccupancyDetails> roomOccupancyDetail = new ArrayList();
                    roomOccupancyDetail.add(occupancyDetail);
                    Tariff tariff = getTariff(ratePlan, askedCurrency, expData, sellableType, los, funnelSource, hotelDetails, occupancyDetail, roomOccupancyDetail,
                            groupBookingPrice, myPartner, markUpDetails, noCostEmiDetailForRootLevel, newPropertyOfferApplicable);
                    tariffs.add(tariff);
                }
            } else {
                // NON-OCCUPANCY ROOMS (Exact, Recommended, Package): Create single tariff with multiple RoomTariff objects
                Tariff tariff = getTariff(ratePlan, askedCurrency, expData, sellableType, los, funnelSource, hotelDetails, ratePlan.getAvailDetail().getOccupancyDetails(), ratePlan.getPrice().getPricePerOccupancy(), groupBookingPrice, myPartner, markUpDetails, noCostEmiDetailForRootLevel, newPropertyOfferApplicable);
                tariffs.add(tariff);
            }
        }

        return tariffs;
    }

    private Tariff getTariff(RatePlan ratePlan, String askedCurrency, String expData, String sellableType, int los, String funnelSource, HotelDetails hotelDetails,
                             OccupancyDetails occupancyDetail, List<OccupancyDetails> roomOccupancyDetails, boolean groupBookingPrice, boolean myPartner,
                             MarkUpDetails markUpDetails, NoCostEmiDetails noCostEmiDetailForRootLevel, boolean newPropertyOfferApplicable) {
        Tariff tariff = new Tariff();

        // Set basic tariff properties
        tariff.setTariffCode(StringUtils.isNotEmpty(occupancyDetail.getRatePlanCode()) ? occupancyDetail.getRatePlanCode() : ratePlan.getCode());
        tariff.setMtKey(StringUtils.isNotEmpty(occupancyDetail.getRatePlanCode()) ? occupancyDetail.getRatePlanCode() : ratePlan.getCode());

        // Set occupancy details from the current occupancy detail
        RoomTariff occupancyDetails = new RoomTariff();
        occupancyDetails.setNumberOfAdults(occupancyDetail.getAdult());
        occupancyDetails.setNumberOfChildren(occupancyDetail.getChild());
        occupancyDetails.setRoomCount(occupancyDetail.getNumberOfRooms());

        // Set child ages if children present
        if (occupancyDetail.getChild() > 0 && CollectionUtils.isNotEmpty(occupancyDetail.getChildAges())) {
            occupancyDetails.setChildAges(occupancyDetail.getChildAges());
        }
        tariff.setOccupancydetails(occupancyDetails);

        // Set availability count
        if (ratePlan.getAvailDetail() != null) {
            tariff.setAvailCount(ratePlan.getAvailDetail().getCount());
        }

        // Set BNPL fields
        if (ratePlan.getBnplDetails() != null) {
            tariff.setBnplApplicable(ratePlan.getBnplDetails().isBnplApplicable());
            tariff.setBnplPersuasionMsg(ratePlan.getBnplDetails().getBnplPersuasionMsg());
        }


        // Create multiple RoomTariff objects from all occupancy details
        List<RoomTariff> roomTariffs = new ArrayList<>();
        for (OccupancyDetails roomOccupancy : roomOccupancyDetails) {
            RoomTariff roomTariff = new RoomTariff();
            roomTariffs.add(roomTariff);
            roomTariff.setNumberOfAdults(roomOccupancy.getAdult());
            roomTariff.setNumberOfChildren(roomOccupancy.getChild());
            roomTariff.setChildAges(CollectionUtils.isNotEmpty(roomOccupancy.getChildAges()) ? roomOccupancy.getChildAges() : null);
            roomTariff.setRoomCount(roomOccupancy.getNumberOfRooms() > 0 ? roomOccupancy.getNumberOfRooms() : null);
            roomTariff.setDisplayPrice(roomOccupancy.getAmount());
        }
        tariff.setRoomTariffs(roomTariffs);

        // Use existing CommonResponseTransformer.getPriceMap method
        if (ratePlan.getPrice() != null) {
            boolean isCorp = ratePlan.getCorpMetaData() != null;
            String segmentId = ratePlan.getSegmentId() != null ? ratePlan.getSegmentId() : "";

            boolean isAltAccoHotel = hotelDetails.getHotelRateFlags().isAltAcco();
            boolean isHighSellingAltAcco = hotelDetails.getHotelRateFlags().isHighSellingAltAcco();
            Map<String, TotalPricing> priceMap = searchRoomsPriceHelper.getPriceMap(ratePlan.getPrice(), expData, occupancyDetail, askedCurrency, sellableType, los,  // Default searchRoomCount for individual rooms
                    isCorp, segmentId, utility.buildToolTip(funnelSource), Utility.isGroupBookingFunnel(funnelSource), groupBookingPrice, myPartner,
                    isAltAccoHotel, markUpDetails, noCostEmiDetailForRootLevel, ratePlan.getLinkedRates(), newPropertyOfferApplicable, isHighSellingAltAcco);
            tariff.setPriceMap(priceMap);

            // Set EMI plan details
            tariff.setEmiPlanDetail(searchRoomsPriceHelper.buildEmiPlanDetails(noCostEmiDetailForRootLevel));

            // Set default price key based on coupon code or default
            if (StringUtils.isNotBlank(ratePlan.getPrice().getCouponCode())) {
                tariff.setDefaultPriceKey(ratePlan.getPrice().getCouponCode());
            } else {
                tariff.setDefaultPriceKey("DEFAULT");
            }
        }
        return tariff;
    }

    /**
     * Check if the room type represents occupancy-based matching (similar to OrchV2 logic)
     */
    private boolean isOccupancyRoomType(Rooms room) {
        // In CG layer, we need to identify occupancy rooms
        return ComboType.OCCUPANCY_ROOM.name().equalsIgnoreCase(room.getType());
    }

    public List<String> getFilterCodes(RatePlan ratePlan, boolean isBlockPAH, int ap,
                                       CommonModifierResponse commonModifierResponse, String roomName) {
        return searchRoomsFilter.getFilterCodes(ratePlan, isBlockPAH, ap, commonModifierResponse, false, roomName);
    }

    private BorderGradient getBorderGradient(RoomType roomType) {
        BorderGradient borderGradient = null;
        if (RoomType.OCCASION_PACKAGE.equals(roomType)) {
            borderGradient = new BorderGradient();
            borderGradient.setStart("#d8d8d8");
            borderGradient.setEnd("#d8d8d8");
            borderGradient.setColor(new ArrayList<>());
            borderGradient.setDirection("horizontal");
        } else if (RoomType.SUPER_PACKAGE.equals(roomType)) {
            borderGradient = new BorderGradient();
            borderGradient.setStart("#8c671c");
            borderGradient.setEnd("#8c671c");
            borderGradient.setColor(Arrays.asList("#d3aa53", "#ffefcf", "#d3aa53"));
            borderGradient.setDirection("horizontal");
        }
        return borderGradient;
    }


    private String buildPackageBenefitsText(PackageDetails packageInclusionDetails, boolean isNewDetailPageDesktop, String askedCurrency, String occasionType, String mealPlanCode, Rooms packageRoom) {
        if (isRecommendedRoom(packageRoom)) {
            return buildRecommendationText(mealPlanCode, packageRoom.getDesc());
        }
        String packageBenefitsText = polyglotService.getTranslatedData(ConstantsTranslation.PACKAGE_BENEFITS_TEXT);
        if (isNewDetailPageDesktop) {
            packageBenefitsText = polyglotService.getTranslatedData(ConstantsTranslation.PACKAGE_BENEFITS_TEXT_NEW_PAGE_DT);
        }
        if (!StringUtils.isEmpty(occasionType)) {
            String occasionText = polyglotService.getTranslatedData(occasionType + UNDERSCORE + ConstantsTranslation.OCCASION_PACKAGE_BENEFITS_TEXT);
            if (StringUtils.isNotEmpty(occasionText)) {
                return occasionText;
            }
        }
        if (packageInclusionDetails != null && StringUtils.isNotEmpty(packageInclusionDetails.getPackageBenefitsSlashedPrice()) && StringUtils.isNotEmpty(packageInclusionDetails.getBenefitsPrice())) {
            String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : DEFAULT_CUR_INR).getCurrencySymbol();
            packageBenefitsText = packageBenefitsText.replace("{1}", String.valueOf((int) Double.parseDouble(packageInclusionDetails.getPackageBenefitsSlashedPrice())));
            packageBenefitsText = packageBenefitsText.replace("{2}", String.valueOf((int) Double.parseDouble(packageInclusionDetails.getBenefitsPrice())));
            packageBenefitsText = packageBenefitsText.replace("{cur}", currencySymbol);
            return packageBenefitsText;
        }
        return polyglotService.getTranslatedData(ConstantsTranslation.ENJOY_PACKAGE_BENEFITS);
    }


    private List<RecommendedCombo> transformRoomCombos(List<RoomCombo> roomCombos,
                                                       Media media, HotelDetails hotelDetails, String expData, String askedCurrency,
                                                       String funnelSource, int los, int ap, boolean isBlockPAH, CommonModifierResponse commonModifierResponse,
                                                       Map<String, String> ratePlanCodeAndNameMap, boolean isLuxeHotel, boolean isAltAccoHotel, boolean isOHSExpEnable,
                                                       String propertyType, String countryCode, String siteDomain, String selectedRoomCode, String selectedRateplanCode, boolean isHighSellingAltAcco, int searchRoomCount) {
        if (CollectionUtils.isEmpty(roomCombos)) {
            return new ArrayList<>();
        }

        List<RecommendedCombo> recommendedCombos = new ArrayList<>();

        // Common variables
        boolean myPartner = Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) &&
                Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
        boolean newPropertyOfferApplicable = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) &&
                utility.isExperimentOn(commonModifierResponse.getExpDataMap(), ExperimentKeys.NEW_PROPERTY_OFFER.getKey());
        NoCostEmiDetails noCostEmiDetailForRootLevel = new NoCostEmiDetails();
        double baseComboFare = 0.0;

        /* Case 1 : Make Combo from RecommendedRoomTypeDetails */
        // Find the primary recommended combo (comboType = RECOMMENDED_ROOM)
        List<RoomCombo> recommendedRoomCombos = roomCombos.stream()
                .filter(combo -> ComboType.RECOMMENDED_ROOM.equals(combo.getComboType()) || ComboType.LOS_COMBO.equals(combo.getComboType())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(recommendedRoomCombos)) {
            for (RoomCombo recommendedRoomCombo : recommendedRoomCombos) {
                // Transform rooms in the combo to RoomDetails
                List<RoomDetails> roomDetailsList = transformRoomsToRoomDetails(
                        recommendedRoomCombo.getRooms(), hotelDetails,
                        expData, askedCurrency, funnelSource, los, ap, isBlockPAH,
                        commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel,
                        isAltAccoHotel, isOHSExpEnable, propertyType, countryCode,
                        siteDomain, selectedRoomCode, selectedRateplanCode, isHighSellingAltAcco
                );

                // Get sellable type from first room's first rate plan
                String sellableType = Constants.SELLABLE_ROOM_TYPE;
                if (CollectionUtils.isNotEmpty(roomDetailsList) && CollectionUtils.isNotEmpty(roomDetailsList.get(0).getRatePlans())) {
                    sellableType = roomDetailsList.get(0).getRatePlans().get(0).getSellableType();
                }

                // Get staycation deal flag from first room
                boolean staycationDeal = false;
                if (CollectionUtils.isNotEmpty(recommendedRoomCombo.getRooms())) {
                    staycationDeal = recommendedRoomCombo.getRooms().get(0).getStaycationDeal();
                }

                // Build the recommended combo
                RecommendedCombo recommendedCombo = buildBasicRecommendedCombo(roomDetailsList, utility.getComboName(recommendedRoomCombo.getComboMealPlan()),
                        staycationDeal, true, // This is the base combo
                        funnelSource, recommendedRoomCombo.getOccupancyDetails());

                // Initialize combo tariff
                if (recommendedCombo.getComboTariff() == null) {
                    recommendedCombo.setComboTariff(new Tariff());
                }

                recommendedCombo.setCorpApprovalInfo(buildCorpApprovalInfo(recommendedRoomCombo.getCorpMetaData(), utility.isTcsV2FlowEnabled(expData)));
                recommendedCombo.setPaymentPlan(buildPaymentPlan(recommendedRoomCombo.getPaymentPlan()));


                // Build price map from combo pricing
                if (recommendedRoomCombo.getPriceDetail() != null) {
                    PriceDetail priceDetail = recommendedRoomCombo.getPriceDetail();
                    // Get occupancy details
                    OccupancyDetails occupancyDetails = recommendedRoomCombo.getOccupancyDetails();
                    if (occupancyDetails == null) {
                        occupancyDetails = CollectionUtils.isNotEmpty(priceDetail.getPricePerOccupancy()) ?
                                priceDetail.getPricePerOccupancy().get(0) : null;
                    }


                    recommendedCombo.getComboTariff().setPriceMap(searchRoomsPriceHelper.getPriceMap(priceDetail, expData, recommendedRoomCombo.getOccupancyDetails(), askedCurrency, sellableType, los, false, "",
                            utility.buildToolTip(funnelSource), Utility.isGroupBookingFunnel(funnelSource),
                            false, // groupBookingPrice - TODO: check from hotelDetails
                            myPartner, isAltAccoHotel, null, // markUpDetails
                            noCostEmiDetailForRootLevel, null, newPropertyOfferApplicable, isHighSellingAltAcco));

                    recommendedCombo.getComboTariff().setEmiPlanDetail(searchRoomsPriceHelper.buildEmiPlanDetails(noCostEmiDetailForRootLevel));

                    // Set default price key
                    String defaultPriceKey = "DEFAULT";
                    if (StringUtils.isNotBlank(recommendedRoomCombo.getPriceDetail().getCouponCode())) {
                        defaultPriceKey = recommendedRoomCombo.getPriceDetail().getCouponCode();
                    }
                    recommendedCombo.getComboTariff().setDefaultPriceKey(defaultPriceKey);

                    // Set occupancy details in tariff
                    if (occupancyDetails != null) {
                        RoomTariff roomTariff = new RoomTariff();
                        roomTariff.setNumberOfAdults(occupancyDetails.getAdult());
                        roomTariff.setNumberOfChildren(occupancyDetails.getChild());
                        if (CollectionUtils.isNotEmpty(occupancyDetails.getChildAges())) {
                            roomTariff.setChildAges(occupancyDetails.getChildAges());
                        }
                        roomTariff.setRoomCount(occupancyDetails.getNumberOfRooms());
                        recommendedCombo.getComboTariff().setOccupancydetails(roomTariff);
                    }
                    if (priceDetail.getDiscount() != null) {
                        Map<String, PersuasionResponse> persuasionMap = new HashMap<>();
                        searchRoomsPersuasionHelper.buildLosComboSavingPersuasion(priceDetail.getDiscount().getComboSaving(), persuasionMap, hotelDetails.getCurrencyCode());
                        recommendedCombo.setComboOfferPersuasions(persuasionMap);
                    }
                }

                // Set payment plan if available
                // TODO: paymentPlan not available in RoomCombo

                // Group booking specific logic
                if (Utility.isGroupBookingFunnel(funnelSource)) {
                    recommendedCombo.setBaseCombo(true);
                    if (recommendedRoomCombo.getPriceDetail() != null && recommendedRoomCombo.getPriceDetail().getDisplayPrice() > 0.0d) {
                        baseComboFare = recommendedRoomCombo.getPriceDetail().getDisplayPrice();
                    }
                    recommendedCombo.setComboText("<b>" + recommendedCombo.getComboName() + "</b>");
                }

                // Free stay for X children - child occupancy message
                // TODO: freeChildCount and freeChildText not available in RoomCombo

                // Flexi cancel add-on details
                // TODO: flexiCancelRoomDetail not available in RoomCombo

                recommendedCombos.add(recommendedCombo);

            }
        }

        /* Case 2 : Make Combo from OtherRecommendedRooms */
        // Find all other recommended combos (comboType = OTHER_RECOMMENDED_ROOM)
        List<RoomCombo> otherRecommendedCombos = roomCombos.stream()
                .filter(combo -> ComboType.OTHER_RECOMMENDED_ROOM.equals(combo.getComboType()))
                .collect(Collectors.toList());

        for (RoomCombo otherRecommendedCombo : otherRecommendedCombos) {
            // Transform rooms in the combo to RoomDetails
            List<RoomDetails> roomDetailsList = transformRoomsToRoomDetails(
                    otherRecommendedCombo.getRooms(), hotelDetails,
                    expData, askedCurrency, funnelSource, los, ap, isBlockPAH,
                    commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel,
                    isAltAccoHotel, isOHSExpEnable, propertyType, countryCode,
                    siteDomain, selectedRoomCode, selectedRateplanCode, isHighSellingAltAcco
            );

            // Get sellable type from first room's first rate plan
            String sellableType = Constants.SELLABLE_ROOM_TYPE;
            if (CollectionUtils.isNotEmpty(roomDetailsList) && CollectionUtils.isNotEmpty(roomDetailsList.get(0).getRatePlans())) {
                sellableType = roomDetailsList.get(0).getRatePlans().get(0).getSellableType();
            }

            // Get staycation deal flag from first room
            boolean staycationDeal = false;
            if (CollectionUtils.isNotEmpty(otherRecommendedCombo.getRooms())) {
                staycationDeal = otherRecommendedCombo.getRooms().get(0).getStaycationDeal();
            }

            // Build the recommended combo
            RecommendedCombo recommendedCombo = buildBasicRecommendedCombo(roomDetailsList, utility.getComboName(otherRecommendedCombo.getComboMealPlan()),
                    staycationDeal,
                    false, // This is not the base combo
                    funnelSource, otherRecommendedCombo.getOccupancyDetails());

            // Initialize combo tariff
            if (recommendedCombo.getComboTariff() == null) {
                recommendedCombo.setComboTariff(new Tariff());
            }

            recommendedCombo.setCorpApprovalInfo(buildCorpApprovalInfo(otherRecommendedCombo.getCorpMetaData(), utility.isTcsV2FlowEnabled(expData)));
            recommendedCombo.setPaymentPlan(buildPaymentPlan(otherRecommendedCombo.getPaymentPlan()));


            // Build price map from combo pricing
            if (otherRecommendedCombo.getPriceDetail() != null) {
                PriceDetail priceDetail = otherRecommendedCombo.getPriceDetail();
                // Get occupancy details
                OccupancyDetails occupancyDetails = CollectionUtils.isNotEmpty(priceDetail.getPricePerOccupancy()) ?
                        priceDetail.getPricePerOccupancy().get(0) : null;

                recommendedCombo.getComboTariff().setPriceMap(searchRoomsPriceHelper.getPriceMap(priceDetail, expData, otherRecommendedCombo.getOccupancyDetails(), askedCurrency, sellableType, los, false, "",
                        utility.buildToolTip(funnelSource), Utility.isGroupBookingFunnel(funnelSource),
                        false, // groupBookingPrice - TODO: check from hotelDetails
                        myPartner, isAltAccoHotel, null, // markUpDetails
                        noCostEmiDetailForRootLevel, null, newPropertyOfferApplicable, isHighSellingAltAcco));

                recommendedCombo.getComboTariff().setEmiPlanDetail(searchRoomsPriceHelper.buildEmiPlanDetails(noCostEmiDetailForRootLevel));

                // Set default price key
                String defaultPriceKey = "DEFAULT";
                if (StringUtils.isNotBlank(otherRecommendedCombo.getPriceDetail().getCouponCode())) {
                    defaultPriceKey = otherRecommendedCombo.getPriceDetail().getCouponCode();
                }
                recommendedCombo.getComboTariff().setDefaultPriceKey(defaultPriceKey);

                // Set occupancy details in tariff
                if (occupancyDetails != null) {
                    RoomTariff roomTariff = new RoomTariff();
                    roomTariff.setNumberOfAdults(occupancyDetails.getAdult());
                    roomTariff.setNumberOfChildren(occupancyDetails.getChild());
                    if (occupancyDetails.getChild() > 0) {
                        // TODO: Handle child ages when available in RoomCombo
                    }
                    roomTariff.setRoomCount(occupancyDetails.getNumberOfRooms());
                    recommendedCombo.getComboTariff().setOccupancydetails(roomTariff);
                }
            }

            // Group booking specific logic
            if (Utility.isGroupBookingFunnel(funnelSource)) {
                if (otherRecommendedCombo.getPriceDetail() != null && otherRecommendedCombo.getPriceDetail().getDisplayPrice() > 0.0d) {
                    double otherRecommendationDisplayPrice = otherRecommendedCombo.getPriceDetail().getDisplayPrice();
                    long differenceInPriceFromBaseCombo = (long) (otherRecommendationDisplayPrice - baseComboFare);
                    if (differenceInPriceFromBaseCombo != 0) {
                        recommendedCombo.setComboText(getComboText(otherRecommendedCombo.getComboMealPlan(), differenceInPriceFromBaseCombo));
                    }
                }
            }

            // Free stay for X children - child occupancy message
            // TODO: freeChildCount and freeChildText not available in RoomCombo

            // Flexi cancel add-on details
            // TODO: flexiCancelRoomDetail not available in RoomCombo

            recommendedCombos.add(recommendedCombo);
        }

        /* Case 3 : Make recommended combos out of the exact matched Rooms */
        // Use hotelDetails.getRooms() for exact matches instead of creating a new combo type
        if (CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {
            // Transform rooms to RoomDetails
            List<RoomDetails> roomDetailsList = transformRoomsToRoomDetails(
                    hotelDetails.getRooms(), hotelDetails,
                    expData, askedCurrency, funnelSource, los, ap, isBlockPAH,
                    commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel,
                    isAltAccoHotel, isOHSExpEnable, propertyType, countryCode,
                    siteDomain, selectedRoomCode, selectedRateplanCode, isHighSellingAltAcco
            );

            // Get sellable type from first room's first rate plan
            String sellableType = Constants.SELLABLE_ROOM_TYPE;
            if (CollectionUtils.isNotEmpty(roomDetailsList) && CollectionUtils.isNotEmpty(roomDetailsList.get(0).getRatePlans())) {
                sellableType = roomDetailsList.get(0).getRatePlans().get(0).getSellableType();
            }

            // Get staycation deal flag from first room
            boolean staycationDeal = false;
            if (CollectionUtils.isNotEmpty(hotelDetails.getRooms())) {
                staycationDeal = hotelDetails.getRooms().get(0).getStaycationDeal();
            }

            // Build the recommended combo
            RecommendedCombo recommendedCombo = buildBasicRecommendedCombo(roomDetailsList, null, // No meal plan for exact matches
                    staycationDeal,
                    false, // This is not the base combo
                    funnelSource, null // No occupancy details for exact matches
            );

            // Initialize combo tariff
            if (recommendedCombo.getComboTariff() == null) {
                recommendedCombo.setComboTariff(new Tariff());
            }

            // Build price map from room pricing using rate plans
            if (CollectionUtils.isNotEmpty(roomDetailsList)) {
                RoomDetails firstRoom = roomDetailsList.get(0);
                if (CollectionUtils.isNotEmpty(firstRoom.getRatePlans())) {
                    SelectRoomRatePlan firstRatePlan = firstRoom.getRatePlans().get(0);
                    if (firstRatePlan.getTariffs() != null && !firstRatePlan.getTariffs().isEmpty()) {
                        Tariff firstTariff = firstRatePlan.getTariffs().get(0);
                        recommendedCombo.getComboTariff().setPriceMap(firstTariff.getPriceMap());
                        recommendedCombo.getComboTariff().setEmiPlanDetail(firstTariff.getEmiPlanDetail());
                        recommendedCombo.getComboTariff().setDefaultPriceKey(firstTariff.getDefaultPriceKey());

                        // Set occupancy details in tariff
                        if (firstTariff.getOccupancydetails() != null) {
                            recommendedCombo.getComboTariff().setOccupancydetails(firstTariff.getOccupancydetails());
                        }
                    }
                }
            }

            // Group booking specific logic
            if (Utility.isGroupBookingFunnel(funnelSource)) {
                if (CollectionUtils.isNotEmpty(roomDetailsList) && CollectionUtils.isNotEmpty(roomDetailsList.get(0).getRatePlans())) {
                    SelectRoomRatePlan firstRatePlan = roomDetailsList.get(0).getRatePlans().get(0);
                    if (firstRatePlan.getTariffs() != null && !firstRatePlan.getTariffs().isEmpty() &&
                            firstRatePlan.getTariffs().get(0).getPriceMap() != null) {
                        Map<String, TotalPricing> priceMap = firstRatePlan.getTariffs().get(0).getPriceMap();
                        TotalPricing defaultPricing = priceMap.get("DEFAULT");
                        if (defaultPricing != null && defaultPricing.getDetails() != null) {
                            // Find the total amount pricing detail
                            Optional<PricingDetails> totalAmount = defaultPricing.getDetails().stream()
                                    .filter(detail -> Constants.TOTAL_AMOUNT_KEY.equals(detail.getKey()))
                                    .findFirst();
                            if (totalAmount.isPresent()) {
                                double amount = totalAmount.get().getAmount();
                                if (amount > 0) {
                                    long differenceInPriceFromBaseCombo = (long) (amount - baseComboFare);
                                    if (differenceInPriceFromBaseCombo != 0) {
                                        recommendedCombo.setComboText(getComboText(null, differenceInPriceFromBaseCombo));
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Free stay for X children - child occupancy message
            // TODO: freeChildCount and freeChildText not available in RoomCombo

            // Flexi cancel add-on details
            // TODO: flexiCancelRoomDetail not available in RoomCombo

            recommendedCombos.add(recommendedCombo);
        }

        return recommendedCombos;
    }

    public CorpApprovalInfo buildCorpApprovalInfo(CorpMetaInfo corpMetaData, boolean tcsV2FlowEnabled) {
        if (corpMetaData == null) {
            return null;
        }
        CorpApprovalInfo corpMetaInfo = new CorpApprovalInfo();
        corpMetaInfo.setApprovalRequired(corpMetaData.isApprovalRequired());
        corpMetaInfo.setBlockOopBooking(corpMetaData.getBlockOopBooking());
        corpMetaInfo.setBlockSkipApproval(corpMetaData.getBlockSkipApproval());
        corpMetaInfo.setWithinPolicy(corpMetaData.isWithinPolicy());
        if(CollectionUtils.isNotEmpty(corpMetaData.getFailureReasons())) {
            corpMetaInfo.setFailureReasons(corpMetaData.getFailureReasons());
        }
        if(StringUtils.isNotEmpty(corpMetaData.getApprovalType())) {
            corpMetaInfo.setApprovalType(corpMetaData.getApprovalType());
        }

        if (corpMetaData.isQuickCheckout()) {
            // sending quickCheckout flag from corp to client
            corpMetaInfo.setWalletQuickPayAllowed(corpMetaData.isQuickCheckout() && !tcsV2FlowEnabled);
        }
        return corpMetaInfo;
    }

    // Helper method to get meal plan from rooms
    private String getMealPlanFromRooms(List<Rooms> rooms) {
        if (CollectionUtils.isEmpty(rooms)) {
            return null;
        }

        // Try to get meal plan from first room's first rate plan
        for (Rooms room : rooms) {
            if (CollectionUtils.isNotEmpty(room.getRatePlans())) {
                for (RatePlan ratePlan : room.getRatePlans()) {
                    if (CollectionUtils.isNotEmpty(ratePlan.getMealPlans())) {
                        return ratePlan.getMealPlans().get(0).getCode();
                    }
                }
            }
        }
        return null;
    }

    // Helper method to check if any room has staycation deal
    private boolean isStaycationDeal(List<Rooms> rooms) {
        if (CollectionUtils.isEmpty(rooms)) {
            return false;
        }

        // TODO: Check if staycation deal flag is available in orchestrator v2
        // For now return false as the field doesn't exist in v2 RatePlan
        return false;
    }

    // Helper method to get occupancy details from rooms
    private OccupancyDetails getOccupancyDetailsFromRooms(List<Rooms> rooms) {
        if (CollectionUtils.isEmpty(rooms)) {
            return null;
        }

        // Try to get occupancy from first room's first rate plan's price
        for (Rooms room : rooms) {
            if (CollectionUtils.isNotEmpty(room.getRatePlans())) {
                for (RatePlan ratePlan : room.getRatePlans()) {
                    if (ratePlan.getPrice() != null &&
                            CollectionUtils.isNotEmpty(ratePlan.getPrice().getPricePerOccupancy())) {
                        return ratePlan.getPrice().getPricePerOccupancy().get(0);
                    }
                }
            }
        }
        return null;
    }

    // Helper method to get combo price from rooms
    private PriceDetail getComboPriceFromRooms(List<Rooms> rooms) {
        if (CollectionUtils.isEmpty(rooms)) {
            return null;
        }

        // Get price from first room's first rate plan
        for (Rooms room : rooms) {
            if (CollectionUtils.isNotEmpty(room.getRatePlans())) {
                for (RatePlan ratePlan : room.getRatePlans()) {
                    if (ratePlan.getPrice() != null) {
                        return ratePlan.getPrice();
                    }
                }
            }
        }
        return null;
    }

    private RecommendedCombo buildBasicRecommendedCombo(List<RoomDetails> roomDetailsList, String comboName,
                                                        boolean isStaycationDeal, boolean baseCombo,
                                                        String funnelSource, OccupancyDetails occupancyDetails) {
        RecommendedCombo recommendedCombo = new RecommendedCombo();
        recommendedCombo.setComboId(UUID.randomUUID().toString());
        recommendedCombo.setComboName(comboName);
        recommendedCombo.setRooms(roomDetailsList);
        recommendedCombo.setStaycationDeal(isStaycationDeal);

        // Set room persuasions from first room
        if (CollectionUtils.isNotEmpty(roomDetailsList) && roomDetailsList.get(0).getRoomPersuasions() != null) {
            recommendedCombo.setRoomPersuasions(roomDetailsList.get(0).getRoomPersuasions());
        }

        // Build combo title based on rooms
        String comboTitle = buildComboTitle(recommendedCombo, funnelSource);
        recommendedCombo.setComboTitle(comboTitle);

        // Handle group booking specific text
        if (Utility.isGroupBookingFunnel(funnelSource) && occupancyDetails != null) {
            for (RoomDetails roomDetails : roomDetailsList) {
                buildGroupBookingComboText(roomDetails, recommendedCombo, baseCombo, occupancyDetails);
            }
        }

        return recommendedCombo;
    }


    private String buildComboTitle(RecommendedCombo recommendedCombo, String funnelSource) {
        // Variables for combo title generation
        boolean isSameCancellationPolicy = true;
        String cancellationType = null;
        int totalTariffs = 0;
        Set<String> mealInclusionCodeList = new HashSet<>();
        String roomCategoryText = Constants.SELLABLE_ROOM_TYPE;
        String mealTypeCodeText = "";

        // Analyze rooms to build combo title
        for (RoomDetails roomDetails : recommendedCombo.getRooms()) {
            if (CollectionUtils.isNotEmpty(roomDetails.getRatePlans())) {
                SelectRoomRatePlan firstRatePlan = roomDetails.getRatePlans().get(0);

                // Get cancellation type from first rate plan
                if (cancellationType == null && firstRatePlan.getCancellationPolicy() != null) {
                    cancellationType = firstRatePlan.getCancellationPolicy().getType().name();
                }

                // Check if all rate plans have same cancellation policy
                for (SelectRoomRatePlan ratePlan : roomDetails.getRatePlans()) {
                    if (ratePlan.getCancellationPolicy() != null) {
                        isSameCancellationPolicy = isSameCancellationPolicy && cancellationType != null && cancellationType.equalsIgnoreCase(ratePlan.getCancellationPolicy().getType().name());
                    }

                    // Count tariffs
                    if (CollectionUtils.isNotEmpty(ratePlan.getTariffs())) {
                        totalTariffs += ratePlan.getTariffs().stream()
                                .mapToInt(tariff -> CollectionUtils.isNotEmpty(tariff.getRoomTariffs()) ? tariff.getRoomTariffs().size() : 0)
                                .sum();
                    }

                    // Collect meal inclusions
                    if (CollectionUtils.isNotEmpty(ratePlan.getInclusionsList())) {
                        for (BookedInclusion inclusion : ratePlan.getInclusionsList()) {
                            if (inclusion != null && "MEAL".equalsIgnoreCase(inclusion.getCategory()) &&
                                    StringUtils.isNotEmpty(inclusion.getInclusionCode())) {
                                mealInclusionCodeList.add(inclusion.getInclusionCode());
                            }
                        }
                    }

                    // Extract meal type from rate plan name
                    if (StringUtils.isNotEmpty(ratePlan.getName()) && StringUtils.isEmpty(mealTypeCodeText)) {
                        String[] arr = ratePlan.getName().split("\\|");
                        if (arr.length > 1) {
                            mealTypeCodeText = arr[1];
                        }
                        if (StringUtils.isEmpty(mealTypeCodeText)) {
                            arr = ratePlan.getName().split(WITH);
                            if (arr.length > 1) {
                                mealTypeCodeText = arr[1];
                            }
                        }
                    }
                }

                // Get sellable type
                if (StringUtils.isNotEmpty(roomDetails.getRoomCategoryText())) {
                    roomCategoryText = roomDetails.getRoomCategoryText();
                }
            }
        }

        // Build combo title
        return getComboTitle(isSameCancellationPolicy, cancellationType, totalTariffs,
                mealInclusionCodeList, "", roomCategoryText, mealTypeCodeText);
    }

    private String getComboTitle(boolean isSameCancellationPolicyInAllRatePlans, String cancellationType,
                                 int totalTariffs, Set<String> mealInclusionCodeList, String mealTypeCode,
                                 String roomCategoryText, String mealTypeCodeText) {
        try {
            // Creation of Polyglot key
            String comboTitle = "ROOMS_COMBO";
            if (totalTariffs == 1) {
                comboTitle = "SINGLE_" + comboTitle;
            } else {
                comboTitle = "PLURAL_" + comboTitle;
            }

            comboTitle += (isSameCancellationPolicyInAllRatePlans &&
                    "FC".equalsIgnoreCase(cancellationType)) ? "_FC" : "_NR";

            String roomTextFromPolyglot = polyglotService.getTranslatedData(comboTitle);
            comboTitle = roomTextFromPolyglot != null ? roomTextFromPolyglot : "";
            comboTitle = comboTitle.replace("{total_tarrifs}", String.valueOf(totalTariffs));
            comboTitle = comboTitle.replace("{sellable_type}", StringUtils.capitalize(roomCategoryText));

            mealTypeCode = mealInclusionCodeList.size() == 1 ?
                    mealInclusionCodeList.stream().findFirst().get() : "";
            mealTypeCode = (CollectionUtils.isNotEmpty(mealPlanCodeList) &&
                    mealPlanCodeList.contains(mealTypeCode)) ?
                    "COMBO_TITLE_" + mealTypeCode : mealTypeCode;

            String mealTextFromPolyglot = polyglotService.getTranslatedData(mealTypeCode);
            comboTitle += (StringUtils.isNotEmpty(mealTextFromPolyglot)) ?
                    " | " + mealTextFromPolyglot : "";

            String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
            if (!Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(client)) {
                comboTitle += (StringUtils.isNotEmpty(mealTypeCodeText)) ?
                        " | " + mealTypeCodeText : "";
            }

            return comboTitle;
        } catch (Exception e) {
            LOGGER.warn("Error occurred in building Combo Title", e);
            return "Combo";
        }
    }

    private Map<String, AddOnDetails> buildAddOnDetails(Map<String, FlexiCancelAddOnDetails> flexiCancelRoomDetail,
                                                        String expData, Integer roomCount, int nightCount,
                                                        String sellableType, boolean groupBookingFunnel,
                                                        boolean isAltAccoHotel, boolean isHighSellingAltAcco) {
        Map<String, AddOnDetails> addOnDetailsMap = null;

        if (flexiCancelRoomDetail.containsKey("FLEXI_CANCEL") &&
                flexiCancelRoomDetail.get("FLEXI_CANCEL") != null) {

            String priceDisplayMessage = (null == roomCount) ? null :
                    commonResponseTransformer.getPriceDisplayMessage(expData, roomCount, sellableType,
                            nightCount, groupBookingFunnel, isAltAccoHotel, isHighSellingAltAcco);

            addOnDetailsMap = new HashMap<>();
            AddOnDetails addOnDetails = new AddOnDetails();
            BeanUtils.copyProperties(flexiCancelRoomDetail.get("FLEXI_CANCEL"), addOnDetails);

            PolicyDetails appliedPolicyDetails = new PolicyDetails();
            BeanUtils.copyProperties(flexiCancelRoomDetail.get("FLEXI_CANCEL").getApplied(), appliedPolicyDetails);

            String appliedText = polyglotService.getTranslatedData(ConstantsTranslation.FLEXI_CANCEL_DETAIL_SELECTED_PER_NIGHT_TEXT);
            if (StringUtils.isNotEmpty(appliedText)) {
                appliedText = MessageFormat.format(appliedText, priceDisplayMessage);
                appliedPolicyDetails.setPriceDescription(appliedText);
            }
            addOnDetails.setApplied(appliedPolicyDetails);

            PolicyDetails removePolicyDetails = new PolicyDetails();
            BeanUtils.copyProperties(flexiCancelRoomDetail.get("FLEXI_CANCEL").getRemoved(), removePolicyDetails);
            removePolicyDetails.setPriceDescription(priceDisplayMessage);
            addOnDetails.setRemoved(removePolicyDetails);

            addOnDetailsMap.put("FLEXI_CANCEL", addOnDetails);
        }

        return addOnDetailsMap;
    }


    /**
     * Transform occupancy room combos from orchestrator v2 response to RoomDetails list.
     * Occupancy room combos are essentially single room combos with less restrictive occupancy requirements.
     * Each combo contains a list of rooms, and we transform them to individual RoomDetails.
     */
    private List<RoomDetails> transformOccupancyRoomCombos(List<RoomCombo> occupancyRoomCombos,
                                                           HotelDetails hotelDetails,
                                                           String expData, String askedCurrency,
                                                           String funnelSource, int los, int ap,
                                                           boolean isBlockPAH,
                                                           CommonModifierResponse commonModifierResponse,
                                                           Map<String, String> ratePlanCodeAndNameMap,
                                                           boolean isLuxeHotel, boolean isAltAccoHotel,
                                                           boolean isOHSExpEnable, String propertyType, String countryCode,
                                                           String siteDomain, String selectedRoomCode,
                                                           String selectedRateplanCode,
                                                           boolean isHighSellingAltAcco) {
        List<RoomDetails> occupancyRoomDetails = new ArrayList<>();

        if (CollectionUtils.isEmpty(occupancyRoomCombos)) {
            return occupancyRoomDetails;
        }

        // Each occupancy room combo should contain rooms with less restrictive occupancy
        for (RoomCombo roomCombo : occupancyRoomCombos) {
            if (CollectionUtils.isNotEmpty(roomCombo.getRooms())) {
                // Transform each room in the combo to RoomDetails
                List<RoomDetails> transformedRooms = transformRoomsToRoomDetails(
                        roomCombo.getRooms(), hotelDetails, expData,
                        askedCurrency, funnelSource, los, ap, isBlockPAH, commonModifierResponse,
                        ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel, isOHSExpEnable,
                        propertyType, countryCode, siteDomain,
                        selectedRoomCode, selectedRateplanCode, isHighSellingAltAcco
                );
                occupancyRoomDetails.addAll(transformedRooms);
            }
        }

        return occupancyRoomDetails;
    }

    private List<BookedInclusion> transformInclusions(List<com.gommt.hotels.orchestrator.detail.model.response.da.Inclusion> inclusions,
                                                      RatePlan ratePlan, Map<String, String> experimentDataMap, int ap, boolean isBlockPAH, boolean isPremiumExperienceApplicable) {
        String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());

        List<BookedInclusion> bookedInclusions = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inclusions)) {
            for (com.gommt.hotels.orchestrator.detail.model.response.da.Inclusion inclusion : inclusions) {
                if (inclusion == null) continue; // guard against null items
                // Skip experience inclusions as they are handled separately
                if (isPremiumExperienceApplicable && Constants.INCLUSION_TYPE_EXPERIENCES.equalsIgnoreCase(inclusion.getType())) {
                    continue;
                }

                BookedInclusion bookedInclusion = new BookedInclusion();
                bookedInclusion.setCode(inclusion.getCode());
                bookedInclusion.setText(StringUtils.isNotEmpty(inclusion.getText()) ? inclusion.getText() : inclusion.getCode());
                bookedInclusion.setSubText(inclusion.getValue());
                bookedInclusion.setAmount(inclusion.getAmount());
                bookedInclusion.setCategory(inclusion.getCategory());
                bookedInclusion.setIconUrl(inclusion.getIconUrl());
                bookedInclusion.setType(inclusion.getType());
                try {
                    if (StringUtils.isNotEmpty(inclusion.getIconType())) {
                        bookedInclusion.setIconType(IconType.valueOf(inclusion.getIconType()));
                    }
                } catch (IllegalArgumentException ignored) { /* ignore invalid enum */ }

                if (CollectionUtils.isNotEmpty(inclusion.getStyleClasses())) {
                    bookedInclusion.setStyleClasses(inclusion.getStyleClasses());
                }
                bookedInclusion.setTrailingCtaType(inclusion.getTrailingCtaType());
                bookedInclusion.setTrailingCtaText(inclusion.getTrailingCtaText());
                bookedInclusion.setTrailingCtaBottomSheet(buildTrailingCtaBottomSheet(inclusion.getTrailingCtaBottomSheet()));
                bookedInclusions.add(bookedInclusion);
            }
        }
        return bookedInclusions;//inclusionHelper.transformInclusions(inclusions, experimentDataMap, region);
    }

    private TrailingCtaBottomSheet buildTrailingCtaBottomSheet(com.gommt.hotels.orchestrator.detail.model.response.persuasion.TrailingCtaBottomSheet trailingCtaBottomSheet) {
        if (trailingCtaBottomSheet == null)
            return null;
        TrailingCtaBottomSheet bottomSheet = new TrailingCtaBottomSheet();
        bottomSheet.setHeading(trailingCtaBottomSheet.getHeading());
        bottomSheet.setSubHeading(trailingCtaBottomSheet.getSubHeading());
        List<SectionFeature> features = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.persuasion.SectionFeature sectionFeature : trailingCtaBottomSheet.getSectionFeatures()) {
            SectionFeature feature = new SectionFeature();
            feature.setText(sectionFeature.getText());
            feature.setIconUrl(sectionFeature.getIconUrl());
            features.add(feature);
        }
        bottomSheet.setSectionFeatures(features);
        return bottomSheet;
    }

    private String buildRatePlanName(RatePlan ratePlan, String sellableType, String listingType, String expData) {
        // Implement the same logic as utility.getRatePlanName but for OrchV2 models directly
        String ratePlanName = StringUtils.EMPTY;
        // Get meal code from OrchV2 meal plans
        String mealCode = StringUtils.EMPTY;
        if (CollectionUtils.isNotEmpty(ratePlan.getMealPlans()) && ratePlan.getMealPlans().get(0) != null) {
            mealCode = ratePlan.getMealPlans().get(0).getCode();
        }

        // Get cancellation policy type from OrchV2 cancellation policy
        String cancellationPolicyString = cancellationPolicyHelper.getCancellationPolicyType(ratePlan.getCancellationPolicy());

        // Adjust sellable type based on listing type
        if (Constants.SELLABLE_ENTIRE_TYPE.equalsIgnoreCase(listingType)) {
            sellableType = Constants.SELLABLE_ENTIRE_TYPE;
        } else {
            sellableType = StringUtils.isBlank(sellableType) ?
                    Constants.SELLABLE_ROOM_TYPE.toUpperCase() : sellableType.toUpperCase();
        }

        // Get experiment data map
        Map<String, String> expDataMap = utility.getExpDataMap(expData);

        // Get the appropriate rate plan name configuration using the same logic as utility.getRatePlanName
        ratePlanName = getRatePlanNameFromConfig(mealCode, cancellationPolicyString, sellableType, expDataMap);

        LOGGER.debug("Built rate plan name: {}", ratePlanName);
        return utility.getTranslationFromPolyglot(ratePlanName);
    }

    /**
     * Get rate plan name from configuration - implements the same logic as utility.getRatePlanName
     * but works with OrchV2 data directly without creating legacy objects
     */
    private String getRatePlanNameFromConfig(String mealCode, String cancellationPolicyString, String sellableType, Map<String, String> expDataMap) {

        String ratePlanName = StringUtils.EMPTY;

        try {
            // Get rate plan name configuration from @Value properties - same as utility class
            String ratePlanNameConfig;
            if (utility.isRatePlanRedesign(expDataMap)) {
                ratePlanNameConfig = ratePlanNameConfigRedesign;
            } else {
                ratePlanNameConfig = ratePlanNameConfigProperty;
            }

            if (StringUtils.isNotBlank(ratePlanNameConfig)) {
                // Parse the configuration JSON using Gson - same as utility class
                Gson gson = new Gson();
                Map<String, Map<String, Map<String, String>>> ratePlanNameMap = gson.fromJson(ratePlanNameConfig, new TypeToken<Map<String, Map<String, Map<String, String>>>>() {
                }.getType());

                if (MapUtils.isNotEmpty(ratePlanNameMap)) {
                    // Apply the exact same logic as utility.getRatePlanName
                    if (!ratePlanNameMap.containsKey(mealCode)) {
                        if (ratePlanNameMap.get(Constants.DEFAULT).get(cancellationPolicyString).containsKey(sellableType)) {
                            ratePlanName = ratePlanNameMap.get(Constants.DEFAULT).get(cancellationPolicyString).get(sellableType);
                        } else {
                            ratePlanName = ratePlanNameMap.get(Constants.DEFAULT).get(cancellationPolicyString).get(Constants.SELLABLE_ROOM_TYPE);
                        }
                    } else {
                        if (ratePlanNameMap.get(mealCode).get(cancellationPolicyString).containsKey(sellableType)) {
                            ratePlanName = ratePlanNameMap.get(mealCode).get(cancellationPolicyString).get(sellableType);
                        } else {
                            ratePlanName = ratePlanNameMap.get(mealCode).get(cancellationPolicyString).get(Constants.SELLABLE_ROOM_TYPE);
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error getting rate plan name from config", e);
        }

        return ratePlanName;
    }

    private AdditionalMandatoryCharges transformAdditionalFees(HotelDetails hotelDetails, RatePlan ratePlan, CommonModifierResponse commonModifierResponse, Map<String, String> expDataMap, String roomName) {
        AdditionalMandatoryCharges additionalCharges = null;
        // Check if trafficSource is "flywheel", if not return null
        if (commonModifierResponse ==null || commonModifierResponse.getTrafficSource() == null || !TrafficSourceConstants.TRAFFIC_SOURCE_FLYWHEEL.equalsIgnoreCase(commonModifierResponse.getTrafficSource())) {
            return null;
        }

        // Extract supplier details and conversion factor from the first available rate plan
        SupplierDetails supplierDetails = null;
        double conversionFactor = 1.0;

        if (ratePlan.getPrice() != null
                && ratePlan.getPrice().getCurrencyConvertor() > 0) {
            // Map orchestrator supplier details to old model
            supplierDetails = new SupplierDetails();
            //TODO add SupplierDetails in Orchestrator response
            //supplierDetails.setSupplierCode(ratePlan.getSupplier().getCode());
            //supplierDetails.setHotelierCurrencyCode(ratePlan.getSupplier().getCurrencyCode());

            conversionFactor = ratePlan.getPrice().getCurrencyConvertor();
        }

        // Check if transfers fee text should be shown
        boolean showTransfersFeeTxt = false;
        if (MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && commonModifierResponse.getExpDataMap().containsKey(Constants.TRANSFERS_FEE_TEXT_KEY)) {
            showTransfersFeeTxt = Constants.EXP_TRUE_VALUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(Constants.TRANSFERS_FEE_TEXT_KEY));
        }

        // Transform orchestrator AdditionalFees to old model AdditionalFees
        List<com.mmt.hotels.model.response.pricing.AdditionalFees> oldAdditionalFees = transformMandatoryCharges(hotelDetails.getMandatoryCharges());

        // Build AdditionalChargesBO object
        AdditionalChargesBO additionalChargesBO = new AdditionalChargesBO.Builder()
                .buildUserCurrency(hotelDetails.getCurrencyCode()) //TODO read from user currency
                .buildHotelierCurrency(hotelDetails.getCurrencyCode())
                .buildPropertyType(hotelDetails.getPropertyType())
                .buildAdditionalFees(oldAdditionalFees)
                .buildConversionFactor(conversionFactor)
                .buildCityCode(hotelDetails.getLocation() != null && hotelDetails.getLocation().getCityId() != null ? hotelDetails.getLocation().getCityId() : null)
                .buildCountryCode(hotelDetails.getLocation() != null ? hotelDetails.getLocation().getCountryId() : null)
                .buildRoomName(roomName)
                .buildCityName(hotelDetails.getLocation() != null && hotelDetails.getLocation().getCityName() != null ? hotelDetails.getLocation().getCityName() : null)
                .buildRecommendationFlow(hotelDetails.getRoomCombos() != null && !hotelDetails.getRoomCombos().isEmpty())
                .build();

        return commonResponseTransformer.buildAdditionalCharges(additionalChargesBO, showTransfersFeeTxt, hotelDetails.getListingType(), PAGE_CONTEXT_DETAIL, expDataMap);
    }

    private AdditionalMandatoryCharges transformAdditionalFees(HotelDetails hotelDetails, CommonModifierResponse commonModifierResponse, Map<String, String> expDataMap) {
        AdditionalMandatoryCharges additionalCharges = null;

        // Extract room type information from orchestrator model
        Map<String, Rooms> roomsMap = null;
        if (hotelDetails.getRooms() != null && !hotelDetails.getRooms().isEmpty()) {
            roomsMap = hotelDetails.getRooms().stream()
                    .collect(Collectors.toMap(Rooms::getCode, Function.identity(), (existing, replacement) -> existing));
        }

        // Extract supplier details and conversion factor from the first available rate plan
        SupplierDetails supplierDetails = null;
        double conversionFactor = 1.0;
        String roomName = null;

        if (MapUtils.isNotEmpty(roomsMap)) {
            for (Map.Entry<String, Rooms> roomEntry : roomsMap.entrySet()) {
                Rooms room = roomEntry.getValue();
                if (room.getRatePlans() != null && !room.getRatePlans().isEmpty()) {
                    for (RatePlan ratePlan : room.getRatePlans()) {
                        if (ratePlan.getPrice() != null
                                && ratePlan.getPrice().getCurrencyConvertor() > 0) {
                            // Map orchestrator supplier details to old model
                            supplierDetails = new SupplierDetails();
                            //TODO add SupplierDetails in Orchestrator response
                            //supplierDetails.setSupplierCode(ratePlan.getSupplier().getCode());
                            //supplierDetails.setHotelierCurrencyCode(ratePlan.getSupplier().getCurrencyCode());

                            conversionFactor = ratePlan.getPrice().getCurrencyConvertor();
                            roomName = room.getName();
                            break;
                        }
                    }
                    //if (supplierDetails != null) break;
                }
            }
        }

        // Check if transfers fee text should be shown
        boolean showTransfersFeeTxt = false;
        if (commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap())
                && commonModifierResponse.getExpDataMap().containsKey(Constants.TRANSFERS_FEE_TEXT_KEY)) {
            showTransfersFeeTxt = Constants.EXP_TRUE_VALUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(Constants.TRANSFERS_FEE_TEXT_KEY));
        }

        // Transform orchestrator AdditionalFees to old model AdditionalFees
        List<com.mmt.hotels.model.response.pricing.AdditionalFees> oldAdditionalFees = transformMandatoryCharges(hotelDetails.getMandatoryCharges());

        // Build AdditionalChargesBO object
        AdditionalChargesBO additionalChargesBO = new AdditionalChargesBO.Builder()
                .buildUserCurrency(hotelDetails.getCurrencyCode()) //TODO read from user currency
                .buildHotelierCurrency(hotelDetails.getCurrencyCode())
                .buildPropertyType(hotelDetails.getPropertyType())
                .buildAdditionalFees(oldAdditionalFees)
                .buildConversionFactor(conversionFactor)
                .buildCityCode(hotelDetails.getLocation() != null && hotelDetails.getLocation().getCityId() != null ? hotelDetails.getLocation().getCityId() : null)
                .buildCountryCode(hotelDetails.getLocation() != null ? hotelDetails.getLocation().getCountryId() : null)
                .buildRoomName(roomName)
                .buildCityName(hotelDetails.getLocation() != null && hotelDetails.getLocation().getCityName() != null ? hotelDetails.getLocation().getCityName() : null)
                .buildRecommendationFlow(hotelDetails.getRoomCombos() != null && !hotelDetails.getRoomCombos().isEmpty())
                .build();

        return commonResponseTransformer.buildAdditionalCharges(additionalChargesBO, showTransfersFeeTxt, hotelDetails.getListingType(), PAGE_CONTEXT_DETAIL, expDataMap);
    }

    /**
     * Transform orchestrator AdditionalFees to old model AdditionalFees
     */
    private List<AdditionalFees> transformMandatoryCharges(
            List<com.gommt.hotels.orchestrator.detail.model.response.pricing.AdditionalFees> orchestratorFees) {

        if (CollectionUtils.isEmpty(orchestratorFees)) {
            return new ArrayList<>();
        }

        List<AdditionalFees> additionalFeesList = new ArrayList<>();

        for (com.gommt.hotels.orchestrator.detail.model.response.pricing.AdditionalFees orchFee : orchestratorFees) {
            AdditionalFees additionalFees = new AdditionalFees();

            // Map ALL basic fields from orchestrator model
            additionalFees.setAmount(orchFee.getAmount());
            additionalFees.setCategory(orchFee.getCategory());
            additionalFees.setLeafCategory(orchFee.getCategory());
            additionalFees.setDescription(orchFee.getDescription());
            additionalFees.setMandatory(orchFee.isMandatory());
            additionalFees.setCurrency(orchFee.getCurrency());

            // Map name if available
            if (StringUtils.isNotBlank(orchFee.getName())) {
                additionalFees.setName(orchFee.getName());
            }

            // Map asked currency amount - this is the display amount in user's currency
            if (orchFee.getAskedCurrencyAmount() > 0) {
                additionalFees.setAskedCurrencyAmount(orchFee.getAskedCurrencyAmount());
            }

            // Map total counts
            additionalFees.setTotalAdults(orchFee.getTotalAdults());
            additionalFees.setTotalChild(orchFee.getTotalChild());
            additionalFees.setTotalRooms(orchFee.getTotalRooms());
            additionalFees.setApplicableDaysCount(orchFee.getApplicableDaysCount());

            // Map property types
            if (CollectionUtils.isNotEmpty(orchFee.getPropertyType())) {
                additionalFees.setPropertyType(new ArrayList<>(orchFee.getPropertyType()));
            }

            if (CollectionUtils.isNotEmpty(orchFee.getPropertySubType())) {
                additionalFees.setPropertySubType(new ArrayList<>(orchFee.getPropertySubType()));
            }

            // Map price breakdown if available
            if (orchFee.getPrice() != null) {
                AdditionalFeesPrice additionalFeesPrice = new AdditionalFeesPrice();

                // Map all price fields
                additionalFeesPrice.setDefaultPrice(orchFee.getPrice().getDefaultPrice());
                additionalFeesPrice.setPerStayRoom(orchFee.getPrice().getPerStayRoom());
                additionalFeesPrice.setPerStayAdult(orchFee.getPrice().getPerStayAdult());
                additionalFeesPrice.setPerStayChild(orchFee.getPrice().getPerStayChild());
                additionalFeesPrice.setPerStayInfant(orchFee.getPrice().getPerStayInfant());
                additionalFeesPrice.setPerNightRoom(orchFee.getPrice().getPerNightRoom());
                additionalFeesPrice.setPerNightAdult(orchFee.getPrice().getPerNightAdult());
                additionalFeesPrice.setPerNightChild(orchFee.getPrice().getPerNightChild());
                additionalFeesPrice.setPerNightInfant(orchFee.getPrice().getPerNightInfant());

                // Set the price breakdown on the AdditionalFees object
                // Note: If setPriceBreakdown doesn't exist, we may need to set individual price fields
                // or store it in a different way based on the old model structure
                additionalFees.setPrice(additionalFeesPrice);
            }

            additionalFeesList.add(additionalFees);
        }

        return additionalFeesList;
    }

    private BgStyle getBgStyle(String start, String end, String direction) {
        BgStyle bgStyle = new BgStyle();
        bgStyle.setStart(start);
        bgStyle.setEnd(end);
        bgStyle.setDirection(direction);
        return bgStyle;
    }

    private com.mmt.hotels.clientgateway.response.rooms.HotelDetails buildHotelDetails(com.gommt.hotels.orchestrator.detail.model.response.HotelDetails hotelDetails, String funnelSource,
                                                                                       SearchRoomsCriteria searchRoomsCriteria, String detailDeepLinkUrl) {
        if (hotelDetails != null) {
            com.mmt.hotels.clientgateway.response.rooms.HotelDetails cgHotelDetails = new com.mmt.hotels.clientgateway.response.rooms.HotelDetails();
            cgHotelDetails.setHotelName(hotelDetails.getName());
            cgHotelDetails.setHotelIcon(hotelDetails.getIcon());
            cgHotelDetails.setHotelType(searchRoomsCriteria != null && StringUtils.isNotEmpty(searchRoomsCriteria.getHotelType()) ? searchRoomsCriteria.getHotelType() : hotelDetails.getPropertyType());
            if (detailDeepLinkUrl != null) {
                String[] parts = detailDeepLinkUrl.split(Constants.URL_PARAM_BASE_SPLITTER);
                if (parts.length == 2) {
                    String base = parts[0];
                    String params = parts[1];

                    detailDeepLinkUrl = base + Constants.QUE_MARK + Stream.of(params.split(Constants.AMP))
                            .map(p -> p.split(Constants.EQUI))
                            .filter(p -> !p[0].equals(Constants.CHECK_AVAILBILITY_PARAM))
                            .map(p -> String.join(Constants.EQUI, p))
                            .collect(Collectors.joining(Constants.AMP));

                    if (Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource)) {
                        detailDeepLinkUrl += Constants.AMP + Constants.FUNNEL_SOURCE_HOMESTAY.toLowerCase() + Constants.EQUI + "true";
                    }
                }
            }

            cgHotelDetails.setUrl(detailDeepLinkUrl);
            return cgHotelDetails;
        }
        return null;
    }

    // All RoomInfo-related methods have been moved to RoomInfoHelper

    // Abstract methods that need to be implemented by subclasses

    protected abstract void buildGroupBookingComboText(RoomDetails roomDetails, RecommendedCombo recommendedCombo, boolean baseCombo, OccupancyDetails occupancyDetails);

    protected abstract LoginPersuasion buildLoginPersuasion();

    protected abstract PersuasionObject createTopRatedPersuasion(boolean isNewDetailsPageDesktop);

    protected abstract PersuasionResponse buildDelayedConfirmationPersuasion(String corpAlias, boolean isMyBizNewDetailsPage);

    protected abstract PersuasionResponse buildSpecialFareTagPersuasion(String corpAlias);

    protected abstract PersuasionResponse buildSpecialFareTagWithInfoPersuasion(String corpAlias, boolean isNewSelectRoomPage);

    protected abstract PersuasionResponse buildConfirmationTextPersuasion(String corpAlias, boolean isNewSelectRoomPage, boolean isMyBizNewDetailsPage);

    private ExperienceInclusions createExperienceInclusionCardForOrchV2(
            List<com.gommt.hotels.orchestrator.detail.model.response.da.Inclusion> orchInclusions,Map<String, String> expDataMap,boolean isPremiumExperienceApplicable) {

        if (!isPremiumExperienceApplicable) {
            return null;
        }

        if (CollectionUtils.isEmpty(orchInclusions)) {
            return null;
        }

        // Convert orchestrator v2 inclusions to pricing inclusions for experience type only
        List<com.mmt.hotels.model.response.pricing.Inclusion> pricingInclusions = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.da.Inclusion orchInclusion : orchInclusions) {
            if (Constants.INCLUSION_TYPE_EXPERIENCES.equalsIgnoreCase(orchInclusion.getType())) {
                com.mmt.hotels.model.response.pricing.Inclusion pricingInclusion = new com.mmt.hotels.model.response.pricing.Inclusion();
                pricingInclusion.setCode(orchInclusion.getCode());
                pricingInclusion.setValue(orchInclusion.getValue());
                pricingInclusion.setAmount(orchInclusion.getAmount());
                pricingInclusion.setIconUrl(orchInclusion.getIconUrl());
                pricingInclusion.setImageURL(orchInclusion.getImageURL());
                pricingInclusion.setCategory(orchInclusion.getCategory());
                pricingInclusion.setType(orchInclusion.getType());
                pricingInclusions.add(pricingInclusion);
            }
        }

        if (CollectionUtils.isEmpty(pricingInclusions)) {
            return null;
        }

        return utility.createExperienceInclusionCard(pricingInclusions, expDataMap);
    }

    //TODO: Mypartner
    //protected abstract void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap);

    //protected abstract void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap, int myPartnerCashback, HeroTierDetails heroTierDetails);

    // Abstract method from parent class
    public abstract String getHtml();

    // ========================= START: IMPLEMENTED METHODS FROM PARENT =========================
    // These methods have been migrated from the parent transformer with adaptations for orchestrator v2

    /**
     * Build price graph information based on price variation data.
     * Note: Orchestrator v2 has limited fields compared to old model:
     * - Missing: amount, alternateDates, heading, description
     * - Type is String instead of enum
     */
    public PriceGraphInfo getPriceGraphInfo(PriceVariation priceVariationInfo, String askedCurrency, Map<String, String> experimentDataMap) {
        if (priceVariationInfo != null && StringUtils.isNotBlank(priceVariationInfo.getType())
                && !PriceVariationType.UNAVAILABLE.name().equalsIgnoreCase(priceVariationInfo.getType())) {

            PriceGraphInfo info = new PriceGraphInfo();
            String type = priceVariationInfo.getType();
            String insightType = priceVariationInfo.getInsightType();
            if (insightType != null && !insightType.trim().isEmpty()) {
                type += "_" + insightType;
            }
            info.setType(type);

            // Convert string type to PriceVariationType enum
            PriceVariationType priceVariationType = priceVariationInfo.getVariationType() != null ? priceVariationInfo.getVariationType() : PriceVariationType.UNAVAILABLE;

            // Use utility method to get background gradient
            BGLinearGradient bgGradient = getBgLinearGradientForPriceVariationType(priceVariationType);
            info.setBgGradient(bgGradient);

            // Determine title and subtitle keys based on price variation type
            String titleKey, subtitleKey;
            switch (priceVariationType) {
                case DROP:
                    titleKey = PRICE_GRAPH_DROP_TITLE;
                    subtitleKey = PRICE_GRAPH_DROP_SUBTITLE;
                    // TODO: AlternateDates not available in orchestrator v2
                    // info.setAlternateDates(buildAlternateDates(...));
                    break;
                case SURGE:
                    titleKey = PRICE_GRAPH_SURGE_TITLE;
                    subtitleKey = PRICE_GRAPH_SURGE_SUBTITLE;
                    break;
                default: // TYPICAL or default
                    titleKey = PRICE_GRAPH_TYPICAL_TITLE;
                    subtitleKey = PRICE_GRAPH_TYPICAL_SUBTITLE;
                    break;
            }

            if (utility.isPriceVariationV2Enabled(experimentDataMap)) {
                // V2 implementation
                info.setIconUrl(priceGraphIcon);
                info.setIconTitle(polyglotService.getTranslatedData(PRICE_GRAPH_ICON_TITLE));
                info.setIconSubTitle(polyglotService.getTranslatedData(PRICE_GRAPH_ICON_SUBTITLE));
            } else {
                // V1 implementation  
                info.setIconUrl(priceGraphTextIcon);
            }

            // Set heading and description using the determined keys
            info.setHeading(getDefaultPriceGraphHeading(titleKey, priceVariationInfo, askedCurrency));
            info.setDescription(getDefaultPriceGraphDescription(subtitleKey, priceVariationInfo, askedCurrency));

            return info;
        }
        return null;
    }

    public BGLinearGradient getBgLinearGradientForPriceVariationType(PriceVariationType priceVariationType) {
        switch (priceVariationType) {
            case DROP:
                return utility.buildBgLinearGradientForPriceDrop();
            case SURGE:
                return utility.buildBgLinearGradientForPriceSurge();
            default:
                return utility.buildBgLinearGradientForPriceTypical();
        }
    }

    /**
     * Helper method to build default heading text when not available in orchestrator v2.
     * Note: Amount field is not available in orchestrator v2 PriceVariation
     */
    private String getDefaultPriceGraphHeading(String priceGraphPolyglotKey,
                                               com.gommt.hotels.orchestrator.detail.model.response.da.PriceVariation priceVariationInfo,
                                               String askedCurrency) {
        String currencySymbol = Currency.getCurrencyEnum(
                StringUtils.isNotBlank(askedCurrency) ? askedCurrency : "INR").getCurrencySymbol();
        String priceGraphStr = polyglotService.getTranslatedData(priceGraphPolyglotKey);
        priceGraphStr = priceGraphStr.replace("{currency}", currencySymbol);
        priceGraphStr = priceGraphStr.replace("{amount}", priceVariationInfo.getPercentage() + "%");
        priceGraphStr = priceGraphStr.replace("{percentage}", String.valueOf(priceVariationInfo.getPercentage()));
        priceGraphStr = priceGraphStr.replace("{duration}", String.valueOf(priceVariationInfo.getDurationDays()));
        return priceGraphStr;
    }

    /**
     * Helper method to build default description text when not available in orchestrator v2.
     */
    private String getDefaultPriceGraphDescription(String priceGraphPolyglotKey,
                                                   com.gommt.hotels.orchestrator.detail.model.response.da.PriceVariation priceVariationInfo,
                                                   String askedCurrency) {
        // Similar to heading but for description
        return getDefaultPriceGraphHeading(priceGraphPolyglotKey, priceVariationInfo, askedCurrency);
    }



    private CouponCardConfig buildCouponCardConfig(SearchRoomsResponse searchRoomsResponse, Integer paxCount) {
        CouponCardConfig couponCardConfig = new CouponCardConfig();
        couponCardConfig.setHeader(polyglotService.getTranslatedData(ConstantsTranslation.DETAIL_COUPON_CARD_HEADER));
        couponCardConfig.setSubHeader(polyglotService.getTranslatedData(COUPON_CARD_CONFIG_SUBHEADING).replace("{paxCount}", String.valueOf(paxCount)));
        return couponCardConfig;
    }

    /**
     * Extract long stay benefits/persuasion from dealBenefits list in orchestrator v2 response.
     *
     * @param dealBenefits List of deal benefits from orchestrator v2 response
     * @param benefitType  The type to filter by (LONG_STAY_BENEFITS or LON_STAY_PERSUASIONS)
     * @return Transformed LongStayBenefits object ready for CG response
     */
    private LongStayBenefits extractLongStayBenefits(List<com.gommt.hotels.orchestrator.detail.model.response.persuasion.DealBenefits> dealBenefits,
                                                     com.gommt.hotels.orchestrator.detail.model.response.persuasion.BenefitType benefitType) {
        if (CollectionUtils.isEmpty(dealBenefits)) {
            return null;
        }

        // Find the DealBenefits with the specified type
        com.gommt.hotels.orchestrator.detail.model.response.persuasion.DealBenefits longStayDeal = dealBenefits.stream()
                .filter(deal -> deal != null && benefitType.equals(deal.getBenefitType()))
                .findFirst()
                .orElse(null);

        if (longStayDeal == null) {
            return null;
        }

        // Transform directly to CG LongStayBenefits format
        LongStayBenefits longStayBenefitsCG = new LongStayBenefits();
        longStayBenefitsCG.setTitle(longStayDeal.getTitle());
        longStayBenefitsCG.setSubTitle(longStayDeal.getSubTitle());
        longStayBenefitsCG.setTitleColor(longStayDeal.getTitleColor());
        longStayBenefitsCG.setCardId(longStayDeal.getCardId());

        // Transform inclusions
        if (CollectionUtils.isNotEmpty(longStayDeal.getInclusionsList())) {
            List<Inclusion> inclusionsList = longStayDeal.getInclusionsList().stream()
                    .map(this::transformDealInclusionToLongStayInclusion)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            longStayBenefitsCG.setInclusionsList(inclusionsList);
        }

        // Transform border gradient
        if (longStayDeal.getBorderGradient() != null) {
            BorderGradient bg = new BorderGradient();
            bg.setAngle(longStayDeal.getBorderGradient().getAngle());
            bg.setEnd(longStayDeal.getBorderGradient().getEnd());
            bg.setStart(longStayDeal.getBorderGradient().getStart());
            // Direction might not be available in CG BorderGradient
            longStayBenefitsCG.setBorderGradient(bg);
        }

        return longStayBenefitsCG;
    }

    /**
     * Transform orchestrator v2 Inclusion to LongStayBenefits Inclusion format
     */
    private Inclusion transformDealInclusionToLongStayInclusion(com.gommt.hotels.orchestrator.detail.model.response.da.Inclusion orchInclusion) {
        if (orchInclusion == null) {
            return null;
        }

        Inclusion inclusion = new Inclusion();
        inclusion.setValue(orchInclusion.getCode());
        inclusion.setCode(orchInclusion.getCode()); // Using value as code for long stay benefits
        inclusion.setInclusionType(orchInclusion.getInclusionType());
        inclusion.setId(orchInclusion.getId());
        inclusion.setIconUrl(orchInclusion.getIconUrl());

        return inclusion;
    }

    /**
     * Extract black benefits from dealBenefits list in orchestrator v2 response and build BlackInfo.
     *
     * @param dealBenefits List of deal benefits from orchestrator v2 response
     * @return Transformed BlackInfo object ready for CG response
     */
    private BlackInfo extractAndBuildBlackInfo(List<com.gommt.hotels.orchestrator.detail.model.response.persuasion.DealBenefits> dealBenefits) {
        if (CollectionUtils.isEmpty(dealBenefits)) {
            return null;
        }
        // Find the DealBenefits with BLACK_BENEFITS type
        com.gommt.hotels.orchestrator.detail.model.response.persuasion.DealBenefits blackDeal = dealBenefits.stream()
                .filter(deal -> deal != null && BenefitType.BLACK_BENEFITS.equals(deal.getBenefitType()))
                .findFirst()
                .orElse(null);

        if (blackDeal == null || blackDeal.getLoyaltyDetails() == null) {
            return null;
        }

        // Directly create CG BlackInfo from orchestrator v2 loyalty details
        BlackInfo blackInfo = new BlackInfo();
        com.gommt.hotels.orchestrator.detail.model.request.prime.BlackInfo loyaltyDetails = blackDeal.getLoyaltyDetails();

        // Map basic fields from loyalty details
        blackInfo.setTierName(loyaltyDetails.getTierName());
        blackInfo.setTierNumber(loyaltyDetails.getTierNumber());
        blackInfo.setIconUrl(loyaltyDetails.getIconUrl());
        blackInfo.setMsg(blackDeal.getSubTitle()); // Using subtitle from deal benefits
        blackInfo.setBorderColor(loyaltyDetails.getBorderColour());
        blackInfo.setCtaUrl(loyaltyDetails.getCtaLink());
        blackInfo.setCtaText(loyaltyDetails.getCta());
        blackInfo.setCurrencyIcon(loyaltyDetails.getCurrencyIcon());
        blackInfo.setTitle(blackDeal.getTitle()); // Using title from deal benefits
        blackInfo.setCardId(blackDeal.getCardId());
        blackInfo.setBgImageUrl(loyaltyDetails.getBgImageUrl());
        blackInfo.setCampaignEndTime(loyaltyDetails.getCampaignEndTime());
        blackInfo.setMmtSelectPrivilige(loyaltyDetails.isMmtSelectPrivilige());
        blackInfo.setTitleImageUrl(loyaltyDetails.getTitleImageUrl());

        // Map inclusions if available
        if (CollectionUtils.isNotEmpty(blackDeal.getInclusionsList())) {
            List<Inclusion> inclusionsList = blackDeal.getInclusionsList().stream()
                    .map(this::transformDealInclusionToBlackInclusion)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            blackInfo.setInclusionsList(inclusionsList);
        }
        // Map border gradient if available
        if (blackDeal.getBorderGradient() != null) {
            BorderGradient bg = new BorderGradient();
            bg.setAngle(blackDeal.getBorderGradient().getAngle());
            bg.setEnd(blackDeal.getBorderGradient().getEnd());
            bg.setStart(blackDeal.getBorderGradient().getStart());
            // Direction might not be available in CG BorderGradient
            blackInfo.setBorderGradient(bg);
        }

        return blackInfo;
    }

    /**
     * Transform orchestrator v2 Inclusion to CG Inclusion format for Black Benefits
     */
    private Inclusion transformDealInclusionToBlackInclusion(com.gommt.hotels.orchestrator.detail.model.response.da.Inclusion orchInclusion) {
        if (orchInclusion == null) {
            return null;
        }

        Inclusion inclusion = new Inclusion();
        inclusion.setValue(orchInclusion.getValue());
        inclusion.setCode(orchInclusion.getValue()); // Using value as code
        inclusion.setInclusionType(orchInclusion.getInclusionType());
        inclusion.setId(orchInclusion.getId());
        inclusion.setImageURL(orchInclusion.getIconUrl());

        return inclusion;
    }

    /**
     * Extract all inclusive inclusions from hotelDetails in orchestrator v2 response.
     *
     * @param hotelDetails Hotel details from orchestrator v2 response
     * @return List of AllInclusiveInclusion objects for CG response (using raw List since exact type is not imported)
     */
    private List<AllInclusiveInclusion> extractAllInclusiveInclusions(HotelDetails hotelDetails) {
        if (hotelDetails.getAllInclusiveData() == null || CollectionUtils.isEmpty(hotelDetails.getAllInclusiveData().getAllInclusiveBenefits())) {
            return null;
        }
        List<AllInclusiveInclusion> allInclusiveInclusions = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.persuasion.Benefits benefit : hotelDetails.getAllInclusiveData().getAllInclusiveBenefits()) {
            AllInclusiveInclusion inclusion = new AllInclusiveInclusion();
            inclusion.setCode(benefit.getCode());
            inclusion.setIconUrl(benefit.getIconUrl());
            inclusion.setImageUrl(benefit.getImageUrl());
            inclusion.setBulletTexts(benefit.getBulletTexts());

            allInclusiveInclusions.add(inclusion);
        }
        return allInclusiveInclusions;
    }

    // ========================= END: IMPLEMENTED METHODS FROM PARENT ========================="

    private String getComboText(String mealPlan, long priceDifference) {
        StringBuilder comboText = new StringBuilder();
        if (StringUtils.isNotBlank(mealPlan)) {
            comboText.append(mealPlan);
        }
        if (priceDifference != 0) {
            if (comboText.length() > 0) {
                comboText.append(" | ");
            }
            if (priceDifference > 0) {
                comboText.append("+");
            }
            comboText.append(priceDifference);
        }
        return comboText.toString();
    }

    /**
     * Helper method to check if package rooms are present in room combos
     */
    private boolean isPackageRoomPresentInCombos(List<RoomCombo> roomCombos) {
        if (CollectionUtils.isEmpty(roomCombos)) {
            return false;
        }

        return roomCombos.stream()
                .anyMatch(combo -> combo.getComboType() == ComboType.PACKAGE_ROOM ||
                        combo.getComboType() == ComboType.OCCASION_PACKAGE_ROOM);
    }

    private void setPackageRoomSpecificInfo(PackageRoomDetails roomDetails, Rooms packageRoom, boolean setSuperPackagePersuasion, String askedCurrency, String occasionType, boolean isNewDetailPageDesktop) {
        if (packageRoom == null) {
            return;
        }
        RoomType roomType = RoomType.fromValue(packageRoom.getType());

        // Map OrchV2 PackageDetails to legacy PackageRoomType fields
        if (packageRoom.getPackageDetails() != null) {
            PackageDetails packageDetails = packageRoom.getPackageDetails();

            // Map available fields from OrchV2 PackageDetails to legacy fields
            if (StringUtils.isNotEmpty(packageDetails.getType())) {
                roomDetails.setType(packageDetails.getType());
            }

            if (StringUtils.isNotEmpty(packageDetails.getSpecialCardText())) {
                roomDetails.setCtaText(packageDetails.getSpecialCardText());
            }

            if (StringUtils.isNotEmpty(packageDetails.getPersuasionText())) {
                roomDetails.setRecommendText(packageDetails.getPersuasionText());
            }

            if (StringUtils.isNotEmpty(packageDetails.getText())) {
                roomDetails.setTitle(packageDetails.getText());
            }

            // Set description from available text fields
            if (StringUtils.isNotEmpty(packageDetails.getPersuasionText())) {
                roomDetails.setDescriptionText(packageDetails.getPersuasionText());
            }

            String mealPlanCode = getMealUpgradeType(packageRoom);
            roomDetails.setPackageBenefits(buildPackageBenefitsText(packageDetails, isNewDetailPageDesktop, askedCurrency, occasionType, mealPlanCode, packageRoom));
            roomDetails.setBorderGradient(getBorderGradient(roomType));
            roomDetails.setBgStyle(getBgStyleForPackage(roomType, packageDetails.getBgGradient()));
            //TODO Extract a method
            if (isSuperPackageRoom(packageRoom)) {
                roomDetails.setIconUrl("https://promos.makemytrip.com/Hotels_product/package/SuperPackageHotelDetail.png");
            } else if (isRecommendedRoom(packageRoom)) {
                roomDetails.setIconUrl(buildMealUpgradeIcon(mealPlanCode));
            } else if (StringUtils.isNotEmpty(packageDetails.getHeaderImageUrl())) {
                roomDetails.setIconUrl(packageDetails.getHeaderImageUrl());
            }
            roomDetails.setPackageImageUrl(packageDetails.getPackagingImageUrl());
        } else {
            // Fallback to default values when PackageDetails is not available
            // Use polyglot service for translated text similar to legacy implementation
            roomDetails.setTitle(polyglotService.getTranslatedData("UNLOCKED_TITLE"));
            roomDetails.setRecommendText(polyglotService.getTranslatedData("RECOMMEND_TEXT"));
            roomDetails.setCtaText(polyglotService.getTranslatedData("RECOMMENDED_CTA_TEXT"));
            roomDetails.setType("SUPER_PACKAGE");
        }

        // Set common package room properties
        roomDetails.setShowGreatValuePackage(!setSuperPackagePersuasion);

        // Set filter details for package rooms
        //setFilterDetails(roomDetails);
    }

    private String getMealUpgradeType(Rooms roomTypeDetails) {
        if (roomTypeDetails != null && CollectionUtils.isNotEmpty(roomTypeDetails.getRatePlans())) {
            for (RatePlan ratePlan : roomTypeDetails.getRatePlans()) {
                if (CollectionUtils.isNotEmpty(ratePlan.getMealPlans())) {
                    for (MealPlan mealPlan : ratePlan.getMealPlans()) {
                        return mealPlan.getCode();
                    }
                }
            }
        }
        return EMPTY_STRING;
    }

    private String buildMealUpgradeIcon(String mealPlanCode) {
        if (StringUtils.isNotBlank(mealPlanCode) && recommendedRoomPropertiesMap.containsKey(mealPlanCode) && recommendedRoomPropertiesMap.get(mealPlanCode).containsKey("iconUrl")) {
            return recommendedRoomPropertiesMap.get(mealPlanCode).get("iconUrl");
        }
        return defaultSearchRoomUrl;
    }

    private String buildRecommendationText(String mealPlanCode, String packageCommunication) {
        if (StringUtils.isNotEmpty(packageCommunication)) {
            return packageCommunication;
        }
        if (StringUtils.isNotBlank(mealPlanCode) && recommendedRoomPropertiesMap.containsKey(mealPlanCode) && recommendedRoomPropertiesMap.get(mealPlanCode).containsKey("benefitText")) {
            return polyglotService.getTranslatedData(recommendedRoomPropertiesMap.get(mealPlanCode).get("benefitText"));
        }
        return polyglotService.getTranslatedData(CP_MEAL_TEXT);
    }

    private BgStyle getBgStyleForPackage(RoomType roomType, com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.BgGradient gradient) {
        BgStyle bgStyle = new BgStyle();
        if (gradient != null) {
            bgStyle.setStart(gradient.getStart());
            bgStyle.setCenter(gradient.getCenter());
            bgStyle.setEnd(gradient.getEnd());
            bgStyle.setDirection(DIAGONAL_BOTTOM);
            bgStyle.setAngle(OCCASSION_PACKAGE_ANGLE);
        } else if (RoomType.SUPER_PACKAGE.equals(roomType)) {
            bgStyle = getBgStyle(Constants.FFFFFF, Constants.ffeaa7, DIAGONAL_BOTTOM);
        } else if (RoomType.MMT_RECOMMEND.equals(roomType)) {
            bgStyle = getBgStyle(Constants.FFFFFF, Constants.D3E7FF, DIAGONAL_BOTTOM);
        }
        return bgStyle;
    }

    private void setFilterDetails(PackageRoomDetails packageGenerated) {
        FilterDetailsClient filterDetails = new FilterDetailsClient();
        filterDetails.setTitle(SHOW_ALL_PACKAGES);
        filterDetails.setFilterCode(new ArrayList<>());
        filterDetails.getFilterCode().add(SHOW_ALL_PACKAGES);
        packageGenerated.setFilterDetails(filterDetails);
    }

    private boolean setSuperPackagePersuasion(CommonModifierResponse commonModifierResponse) {
        return commonModifierResponse != null && commonModifierResponse.getExpDataMap() != null &&
                commonModifierResponse.getExpDataMap().containsKey(Constants.EXP_SPKG) &&
                Constants.EXP_TRUE_VALUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(Constants.EXP_SPKG));
    }

    /**
     * Determines if a room is a package room based on OrchV2 data.
     * Following legacy pattern: check for package attributes in room.
     */
    private boolean isSuperPackageRoom(Rooms room) {
        RoomType roomTypeEnum = RoomType.fromValue(room.getType());
        return RoomType.SUPER_PACKAGE.equals(roomTypeEnum);
    }

    /**
     * Determines if a room is a recommended room based on OrchV2 data.
     * Following legacy pattern: check for meal upsell or recommendation attributes.
     */
    private boolean isRecommendedRoom(Rooms room) {
        RoomType roomTypeEnum = RoomType.fromValue(room.getType());
        return RoomType.MMT_RECOMMEND.equals(roomTypeEnum) || RoomType.MEAL_UPGRADE.equals(roomTypeEnum);
    }

    /**
     * Determines if a room is an occasion room based on OrchV2 data.
     * Following legacy pattern: check for occasion type.
     */
    private boolean isOccasionRoom(Rooms room) {
        RoomType roomTypeEnum = RoomType.fromValue(room.getType());
        return RoomType.OCCASION_PACKAGE.equals(roomTypeEnum);
    }

}
