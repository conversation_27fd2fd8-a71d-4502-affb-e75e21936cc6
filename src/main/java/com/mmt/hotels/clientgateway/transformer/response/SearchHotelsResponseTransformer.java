package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ibm.icu.text.NumberFormat;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.enums.Persuasions;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;

import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.Hover;
import com.mmt.hotels.clientgateway.response.dayuse.Slot;
import com.mmt.hotels.clientgateway.response.dayuse.SlotDetail;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.searchHotels.BottomSheet;
import com.mmt.hotels.clientgateway.response.searchHotels.CalendarCriteria;
import com.mmt.hotels.clientgateway.response.searchHotels.CollectionCardPersuasion;
import com.mmt.hotels.clientgateway.response.searchHotels.CrossSellData;
import com.mmt.hotels.clientgateway.response.searchHotels.Facility;
import com.mmt.hotels.clientgateway.response.searchHotels.FilterConditions;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.HotelCard;
import com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard;
import com.mmt.hotels.clientgateway.response.searchHotels.PersonalizedSection;
import com.mmt.hotels.clientgateway.response.searchHotels.SearchHotelsResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.SectionFeature;
import com.mmt.hotels.clientgateway.response.searchHotels.SoldOutInfoCG;
import com.mmt.hotels.clientgateway.response.searchHotels.SortCriteria;
import com.mmt.hotels.clientgateway.response.searchHotels.ToolTip;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.*;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.util.CrossSellUtil;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.DayUseUtil;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.enums.LuckyUserContext;
import com.mmt.hotels.model.enums.SectionsType;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.request.matchmaker.InputHotel;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.model.response.MpFareHoldStatus;
import com.mmt.hotels.model.response.dayuse.MissingSlotDetail;
import com.mmt.hotels.model.response.emi.Emi;
import com.mmt.hotels.model.response.flyfish.ConceptSummaryDTO;
import com.mmt.hotels.model.response.flyfish.TravellerRatingSummaryDTO;
import com.mmt.hotels.model.response.mypartner.MarkUpDetails;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.pricing.DisplayPriceBreakDown;
import com.mmt.hotels.model.response.pricing.RoomTypeDetails;
import com.mmt.hotels.model.response.searchwrapper.ListingPagePersonalizationResponsBO;
import com.mmt.hotels.model.response.searchwrapper.MyBizSimilarToDirectObj;
import com.mmt.hotels.model.response.searchwrapper.PersonalizedResponse;
import com.mmt.hotels.model.response.searchwrapper.QuickBookInfo;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.searchwrapper.SoldOutInfo;
import com.mmt.hotels.model.response.searchwrapper.TrackingVariables;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.hotels.pojo.response.detail.FeaturedAmenity;
import com.mmt.hotels.util.PromotionalOfferType;
import com.mmt.model.LocusData;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.GROUP_PRICE_TEXT_ONE_NIGHT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SAVING_PERC_TEXT;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.LISTING_SEARCH_HOTELS;
import static com.mmt.hotels.clientgateway.util.Utility.*;


@Component
public abstract class SearchHotelsResponseTransformer {


	@Value("${consul.enable}")
	private boolean consulFlag;
	@Value("${view.360.icon.url}")
	private String view360IconUrl;

	@Autowired
	CommonConfigConsul commonConfigConsul;
	@Autowired
	CommonResponseTransformer commonResponseTransformer;
	@Autowired
	CrossSellUtil crossSellUtil;
	@Autowired
	PricingEngineHelper pricingEngineHelper;

	@Autowired
	private DateUtil dateUtil;

	@Autowired
	private DayUseUtil dayUseUtil;

	@Value("${listing.myBiz.Assured.tooltip.iconType}")
	private String myBizToolTipIconType;

	@Value("${mybiz.assured.url}")
	private String myBizAssuredUrl;

	@Value("${high.rated.url}")
	private String highRatedUrl;

	@Value("${gst.invoice.url}")
	private String gstInvoiceUrl;

	@Value("${bpg.url}")
	private String bpgUrl;
	@Value("${min.limit.similar.hotels}")
	private int minSimilarHotels;

	@Value("${section.bg.color.map}")
	private String sectionBgColorMap;

	@Value("${desktop.persuasion.placeholders.demand.concentration.tobe.blocked}")
	private List<String> desktopPersPlaceholdersToBeBlockedDemandConc;

	@Value("${apps.persuasion.placeholders.demand.concentration.tobe.blocked}")
	private List<String> appsPersPlaceholdersToBeBlockedDemandConc;

	@Value("${listing.driving.duration.buckets}")
	protected String listingDrivingDurationBucket;

	@Value("#{'${pixel.tracking.locations}'.split(',')}")
	private Set<String> pixelTrackingLocations;

	protected Map<String, String> listingDrivingDurationBucketMap;

	@Autowired
	private PolyglotService polyglotService;

	@Autowired
	private MobConfigHelper mobConfigHelper;

	@Autowired
	protected ObjectMapperUtil objectMapperUtil;

	@Autowired
	private MetricAspect metricAspect;

	@Autowired
	private PropertyManager propManager;

	@Autowired
	private Utility utility;

	@Value("${persuasion.place.holders.to.show}")
	private String placeHoldersToShowConfig;

	@Value("${filter.conditions}")
	private String filterConditionsConfig;

	@Value("${family.friendly.tracking.text}")
	private String familyFriendlyTracking;

	@Value("${negotiated.rates.delayed.confirmation.no.of.hours}")
	protected int noOfHoursForConfirmation;

	@Value("${special.fare.persuasion.style}")
	protected String specialFarePersuasionConfig;

	@Value("${corp.one.on.one.segmentId}")
	private String corpPreferredRateSegmentId;

	@Value("${bank.coupon.generic.icon}")
	private String genericBankIcon;

	protected Map<String, Map<String,PersuasionData>> specialFarePersuasionConfigMap;

	@Autowired
	private CommonHelper commonHelper;

	@Autowired
    PersuasionUtil persuasionUtil;

	public MissingSlotDetail missingSlotDetails = null;

	public int thresholdForSlashedAndDefaultHourPrice = 0;

	private static final Logger LOGGER = LoggerFactory.getLogger(SearchHotelsResponseTransformer.class);

	protected static Gson gson = new Gson();

	@PostConstruct
	public void init() {
		if(consulFlag){
			missingSlotDetails = commonConfigConsul.getMissingSlotDetails();
			LOGGER.warn("missingSlotDetails : {}" , missingSlotDetails);
			thresholdForSlashedAndDefaultHourPrice = commonConfigConsul.getThresholdForSlashedAndDefaultHourPrice();
			LOGGER.debug("Fetching values from commonConfig consul");
		}
		else{
			CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
			missingSlotDetails = commonConfig.missingSlotDetails();
			LOGGER.warn("missingSlotDetails : {}" , missingSlotDetails);
			thresholdForSlashedAndDefaultHourPrice = commonConfig.thresholdForSlashedAndDefaultHourPrice();
		}
		listingDrivingDurationBucketMap = gson.fromJson(listingDrivingDurationBucket, HashMap.class);
		specialFarePersuasionConfigMap = gson.fromJson(specialFarePersuasionConfig, new TypeToken<Map<String, Map<String, PersuasionData>>>() {
		}.getType());
	}
	public SearchHotelsResponse convertSearchHotelsResponse(ListingPagePersonalizationResponsBO listingPageResponseBO, SearchHotelsRequest searchHotelsRequest, CommonModifierResponse commonModifierResponse) {
		long startTime = System.currentTimeMillis();
		SearchHotelsResponse searchHotelsResponse= new SearchHotelsResponse();
		try {
			searchHotelsResponse.setCurrency(listingPageResponseBO.getCurrency());
			searchHotelsResponse.setHotelCount(listingPageResponseBO.getTotalHotelCounts());
			searchHotelsResponse.setHotelCountInCity(listingPageResponseBO.getHotelCountInCity());
			searchHotelsResponse.setLastHotelCategory(listingPageResponseBO.getLastFetchedHotelCategory());
			searchHotelsResponse.setCanShowFilterRemovalCard(listingPageResponseBO.isCanShowFilterRemovalCard());
			if (listingPageResponseBO.getExpData()!=null) {
				if (listingPageResponseBO.getExpData().containsKey("nearbyFixes") && Boolean.parseBoolean(listingPageResponseBO.getExpData().get("nearbyFixes"))
					|| listingPageResponseBO.getExpData().containsKey("emptyShopSolution") && Boolean.parseBoolean(listingPageResponseBO.getExpData().get("emptyShopSolution"))) {
					searchHotelsResponse.setLastFetchedHotelCategory(listingPageResponseBO.getLastFetchedHotelCategory());
				}
			}
			if (SectionsType.FILTER_REMOVAL.name().equalsIgnoreCase(searchHotelsResponse.getLastFetchedHotelCategory())) {
				LOGGER.warn("FILTER_REMOVAL category found in lastFetchedHotelCategory");
				if (searchHotelsRequest!=null && CollectionUtils.isNotEmpty(searchHotelsRequest.getFilterCriteria())) {
					List<Filter> filters = searchHotelsRequest.getFilterCriteria().stream().filter(e->e.getFilterGroup().equals(FilterGroup.STAR_RATING)).collect(Collectors.toList());
					if (CollectionUtils.isNotEmpty(filters)) {
						searchHotelsResponse.setFilterRemovedCriteria(filters);
					}
				}
			}

			if(commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && TRUE.equals(commonModifierResponse.getExpDataMap().get(APPLY_IN_POLICY_COR_FILTER_EXP)) && listingPageResponseBO.isInPolicyFilterRemoved()){
				List<Filter> filters = searchHotelsRequest.getFilterCriteria().stream().filter(e->e.getFilterGroup().equals(FilterGroup.IN_POLICY)).collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(filters)) {
					searchHotelsResponse.setFilterRemovedCriteria(filters);
					LOGGER.warn("Added IN_POLICY filter to filterRemovedCriteria" + searchHotelsResponse.getFilterRemovedCriteria());
				}
			}
			searchHotelsResponse.setLastHotelId(listingPageResponseBO.getLastFetchedHotelId());
			searchHotelsResponse.setLastFetchedWindowInfo(listingPageResponseBO.getLastFetchedWindowInfo());
			searchHotelsResponse.setRankingHotelCount(listingPageResponseBO.getRankingHotelCount());
			searchHotelsResponse.setLastHotelIndex(listingPageResponseBO.getLastHotelIndex());
			if (listingPageResponseBO.getLocusData() != null) {
				searchHotelsResponse.setLocationDetail(buildLocationDetail(listingPageResponseBO.getLocusData().getLocusId(), listingPageResponseBO.getLocusData().getLocusName(),
						listingPageResponseBO.getLocusData().getLocusType(), listingPageResponseBO.getCountryCode(), listingPageResponseBO.getCountryName()));
			}
			searchHotelsResponse.setCityLocationDetail(buildLocationDetail(listingPageResponseBO.getCityCode(), listingPageResponseBO.getCityName(),
					"city", null, null));
			if(searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails()!=null && Constants.SHORTSTAYS_FUNNEL.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource())
					&& commonResponseTransformer.isEligibleForNearbyFlow(searchHotelsRequest.getDeviceDetails())) {
				if(CollectionUtils.isNotEmpty(listingPageResponseBO.getPersonalizedResponse())) {
					String displayName = listingPageResponseBO.getPersonalizedResponse().get(0).getHeading();
					if(searchHotelsResponse.getLocationDetail()!=null && displayName!=null) {
						searchHotelsResponse.getLocationDetail().setDisplayName(displayName.replace("Showing ",""));
					}
				}
			}
			searchHotelsResponse.setNoMoreHotels(listingPageResponseBO.isNoMoreAvailableHotels());
			String idContext = searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getIdContext() : null;
			searchHotelsResponse.setPersonalizedSections(buildPersonalizedSections(listingPageResponseBO.getPersonalizedResponse(), utility.getExpDataMap(searchHotelsRequest.getExpData()), idContext, searchHotelsRequest,commonModifierResponse, listingPageResponseBO.getLocusData(), listingPageResponseBO.getMarkUpDetails(), listingPageResponseBO.isInPolicyFilterRemoved()));
			searchHotelsResponse.setSortCriteria(buildSortCriteria(listingPageResponseBO.getSortCriteria()));
			searchHotelsResponse.setExpData(listingPageResponseBO.getExpData());
			searchHotelsResponse.setExpVariantKeys(StringUtils.isNotBlank(searchHotelsRequest.getExpVariantKeys()) ? searchHotelsRequest.getExpVariantKeys() : null);
			searchHotelsResponse.setUsradid(listingPageResponseBO.getUsradid());
			searchHotelsResponse.setHydraSegments(listingPageResponseBO.getHydraSegments());
			searchHotelsResponse.setUserLoyaltyStatus(listingPageResponseBO.getUserLoyaltyStatus());
			searchHotelsResponse.setUserFareHold(listingPageResponseBO.isUserFareHold());
			if(listingPageResponseBO.getPremiumAvailable() != null){
				searchHotelsResponse.setPremiumAvailable(listingPageResponseBO.getPremiumAvailable());
			}
			searchHotelsResponse.setGuestHouseAvailable(listingPageResponseBO.getGuestHouseAvailable());
			if(listingPageResponseBO.getPremiumAvailable() != null){
				searchHotelsResponse.setPremiumAvailable(listingPageResponseBO.getPremiumAvailable());
			}
			if (listingPageResponseBO.getSectionsType() != null) {
				searchHotelsResponse.setSectionsType(listingPageResponseBO.getSectionsType().getValue());
			}
			if(StringUtils.isNotBlank(listingPageResponseBO.getSharingUrl())){
				searchHotelsResponse.setSharingUrl(listingPageResponseBO.getSharingUrl());
			}
			if(StringUtils.isNotBlank(listingPageResponseBO.getListingDeepLinkWithoutFilters())){
//				set listingDeepLinkWithoutFilters as recentDeepLink to pass to the client
				searchHotelsResponse.setRecentDeepLink(listingPageResponseBO.getListingDeepLinkWithoutFilters());
			}
			if(CollectionUtils.isNotEmpty(listingPageResponseBO.getPaxDetailsList())){
				searchHotelsResponse.setPaxDetails(listingPageResponseBO.getPaxDetailsList());
			}
			boolean isBuildCallToBookData = false;

			if(listingPageResponseBO.getLuckyUserContext() != null){
				searchHotelsResponse.setLuckyUserContext(LuckyUserContext.LUCKY.equals(listingPageResponseBO.getLuckyUserContext()) ? PRIVILEGED_USER : LuckyUserContext.LUCKY_UNLUCKY.equals(listingPageResponseBO.getLuckyUserContext()) ? CURSED_USER : UNFORTUNATE_USER);
			}
			if (StringUtils.isNotEmpty(listingPageResponseBO.getTrackingText())) {
				searchHotelsResponse.setTrackingText(listingPageResponseBO.getTrackingText());
			}

			String luckyUserContextWithExp = utility.logLuckyUserData(commonModifierResponse, searchHotelsResponse.getLuckyUserContext(), "search-hotels");
			searchHotelsResponse.setLuckyUserContext(luckyUserContextWithExp);

			if (crossSellUtil.isCrossSellRequest(searchHotelsRequest)) {
				searchHotelsResponse.setCrossSellData(crossSellUtil.getCrossSellData(false, searchHotelsRequest.getDeviceDetails().getBookingDevice(), listingPageResponseBO.getCityName(), searchHotelsRequest, new HashMap<>(),commonModifierResponse));
			}

			// passing additional discount node to BUSES & TRAINS funnel
			if(searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null && searchHotelsRequest.getRequestDetails().getTrafficSource() != null
					&& (TRAFFIC_SOURCE_BUSES_THANKYOU_PAGE.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getTrafficSource().getSource())
					|| TRAFFIC_SOURCE_TRAINS_THANKYOU_PAGE.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getTrafficSource().getSource()))
					&& listingPageResponseBO != null &&listingPageResponseBO.getCrossSellData() != null){
				CrossSellData crossSellData = new CrossSellData();
				crossSellData.setDiscountPercent(listingPageResponseBO.getCrossSellData().getDiscountPercent());
				crossSellData.setCouponCode(listingPageResponseBO.getCrossSellData().getCouponCode());
				searchHotelsResponse.setCrossSellData(crossSellData);
			}
			if (searchHotelsRequest != null) {
				// If we have applied preAppliedFilter on the search request, then we need to show the request callback data
				searchHotelsResponse.setPreAppliedFilters(utility.getPreAppliedFiltersForLocationId(searchHotelsRequest.getSearchCriteria(), commonModifierResponse != null ? commonModifierResponse.getExpDataMap() : null));
			}

			Map<String, String> trackingMap = listingPageResponseBO.getTrackingMap();
			if(commonModifierResponse != null && (REGION_AE).equalsIgnoreCase(commonModifierResponse.getRegion())) {
				String businessOwnerID = commonResponseTransformer.getEvarBasedOnCountryAndRegion(commonModifierResponse.getUserCountry() , listingPageResponseBO.getCountryCode());
				if (StringUtils.isNotEmpty(businessOwnerID)) {
					if (MapUtils.isEmpty(trackingMap)) {
						trackingMap = new HashMap<>();
					}
					if (trackingMap.containsKey(EVAR_126)) {
						trackingMap.put(EVAR_126, trackingMap.get(EVAR_126).toString() + "|" + businessOwnerID);
					} else {
						trackingMap.put(EVAR_126, businessOwnerID);
					}
				}
			}
			searchHotelsResponse.setTrackingMap(commonResponseTransformer.buildTrackingMap(trackingMap));

			PixelUrlConfig pixelUrlData = createPixelDataProvider(listingPageResponseBO, commonModifierResponse, searchHotelsRequest);
			if (pixelUrlData != null) {
				searchHotelsResponse.setPixelUrl(commonResponseTransformer.buildPixelUrl(pixelUrlData));
			}
		} finally {
			metricAspect.addToTimeInternalProcess(PROCESS_SEARCH_RESPONSE_PROCESS, LISTING_SEARCH_HOTELS, System.currentTimeMillis() - startTime);
		}
		return searchHotelsResponse;
	}

	private PixelUrlConfig createPixelDataProvider(ListingPagePersonalizationResponsBO listingPageResponseBO, CommonModifierResponse commonModifierResponse, SearchHotelsRequest searchHotelsRequest) {
		if(utility.isGccOrKsa() || utility.isMyBizRequest() || !utility.isB2CFunnel() || listingPageResponseBO == null || listingPageResponseBO.getLocusData() == null || listingPageResponseBO.getLocusData().getLocusId() == null || !pixelTrackingLocations.contains(listingPageResponseBO.getLocusData().getLocusId())){
			return null;
		}
		SearchHotelsCriteria searchHotelsCriteria = (searchHotelsRequest != null && searchHotelsRequest.getSearchCriteria() != null) ? searchHotelsRequest.getSearchCriteria() : null;
		return new PixelUrlConfig(listingPageResponseBO.getCurrency(), commonModifierResponse != null ? commonModifierResponse.getLanguage() : null, listingPageResponseBO.getLocusData().getLocusName(), null, null, searchHotelsCriteria != null ? searchHotelsCriteria.getCheckIn() : null, searchHotelsCriteria != null ? searchHotelsCriteria.getCheckOut() : null, searchHotelsCriteria != null && searchHotelsCriteria.getRoomStayCandidates() != null && !searchHotelsCriteria.getRoomStayCandidates().isEmpty() ? getGuestCountForPixelUrl(searchHotelsCriteria.getRoomStayCandidates().get(0)) : null, commonModifierResponse != null ? commonModifierResponse.getUserCountry() : null, commonModifierResponse != null ? commonModifierResponse.getDeviceId() : null, searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getJourneyId() : null);
	}

	private int getGuestCountForPixelUrl(RoomStayCandidate roomStayCandidate) {
		int adultCount = roomStayCandidate.getAdultCount() != null ? roomStayCandidate.getAdultCount() : 0;
		int childCount = roomStayCandidate.getChildAges() != null ? roomStayCandidate.getChildAges().size() : 0;
		return adultCount + childCount;
	}

	public LocationDetail buildLocationDetail(String id, String name, String type, String countryId, String countryName) {
		LocationDetail locationDetail = new LocationDetail(id, name, type, countryId, countryName);
		return locationDetail;
	}

	private List<PersonalizedSection> buildPersonalizedSections(List<PersonalizedResponse<SearchWrapperHotelEntity>> list, Map<String, String> expDataMap, String idContext, SearchHotelsRequest searchHotelsRequest, CommonModifierResponse commonModifierResponse, LocusData locusData, final MarkUpDetails markUpDetails, boolean inPolicyFilterRemoved) {
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		List<PersonalizedSection> personalizedSections = new ArrayList<>();
		list.forEach(perResponse -> {
			PersonalizedSection personalizedSection = new PersonalizedSection();
			personalizedSection.setName(perResponse.getSection());

			personalizedSection.setShowIndex(commonResponseTransformer.canShowIndex(perResponse.getSection(), expDataMap));
			
			if (checkIfExclusiveDeal(perResponse, commonModifierResponse))
				personalizedSection.setHeading(EXCLUSIVE_DEAL_HEADING);
			else if (checkIfHorizontalSection(perResponse, commonModifierResponse) && null != perResponse.getHeading())
				personalizedSection.setHeading(HG_HORIZONTAL_HEADING);
			else
				personalizedSection.setHeading(perResponse.getHeading());
			if (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null && Utility.isCorpBudgetHotelFunnel(searchHotelsRequest.getRequestDetails().getFunnelSource())){
				personalizedSection.setStaticCard(buildStaticCard(perResponse.getSection(), perResponse.getHotels()));
			}
			//ShortStay funnel will not have a subheading
			if (searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails()!=null && (Constants.SHORTSTAYS_FUNNEL.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource()))
					&& commonResponseTransformer.isEligibleForNearbyFlow(searchHotelsRequest.getDeviceDetails())) {
				personalizedSection.setSubHeading(null);
			} else {
				personalizedSection.setSubHeading(perResponse.getSubHeading());
			}

			if(inPolicyFilterRemoved){
				if(MYBIZ_RECOMMENDED_INTL_SECTION.equalsIgnoreCase(personalizedSection.getName()))
					personalizedSection.setHeading(polyglotService.getTranslatedData(IN_POLICY_FILTER_REMOVED_HEADING_MYBIZ_ASSURED));
				else
					personalizedSection.setHeading(polyglotService.getTranslatedData(IN_POLICY_FILTER_REMOVED_HEADING));
			}

			if (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null && isGroupBookingFunnel(searchHotelsRequest.getRequestDetails().getFunnelSource())) {
				personalizedSection.setHeadingVisible(perResponse.isHeadingVisible());
			}
			if (StringUtils.isNotBlank(personalizedSection.getName()) && Constants.NOT_MYBIZ_ASSURED_SHOWN.equalsIgnoreCase(personalizedSection.getName())) {
				overridePersonalizedSectionHeadingForDirectHotelSearch(searchHotelsRequest, personalizedSection);
				buildFilterCardForMyBizAndNonMyBizProperties(perResponse, personalizedSection);
			}
			if (StringUtils.isNotBlank(personalizedSection.getName()) && personalizedSection.getName().equalsIgnoreCase(Constants.MY_BIZ_ASSURED_SECTION) && Constants.DOM_COUNTRY.equalsIgnoreCase(searchHotelsRequest.getSearchCriteria().getCountryCode())) {
				personalizedSection.setToolTip(buildMyBizAssuredToolTip());
			}
			if (perResponse.isMyBizAssuredRecommended()) {
				personalizedSection.setSectionFeatures(getMybizSimilarHotelsFeatures());
			}
			String orientation= buildOrientation(perResponse,expDataMap);
			personalizedSection.setHotels(buildPersonalizedHotels(perResponse.getHotels(),expDataMap, searchHotelsRequest, personalizedSection.getName(),commonModifierResponse,locusData, markUpDetails, orientation));
			personalizedSection.setHotelCount(perResponse.getCount());
			personalizedSection.setOrientation(orientation);
			//Adding hotelCardType, seeMoreCTA, minHotelsToShow node here based newListingUi exp
			if(StringUtils.isNotEmpty(perResponse.getHotelCardType()))
				personalizedSection.setHotelCardType(perResponse.getHotelCardType().toLowerCase());
			if (perResponse.getMinCardCount() != null) {
				personalizedSection.setMinHotelsToShow(Integer.valueOf(perResponse.getMinCardCount()));
			}
			if (StringUtils.isNotEmpty(perResponse.getSeeMoreCTA())) {
				personalizedSection.setSeeMoreCTA(perResponse.getSeeMoreCTA());
			}
			personalizedSection.setShowMore(false);
			personalizedSection.setCardInsertionAllowed(getCardInsertionAllowedValue(perResponse, idContext));
			personalizedSection.setMyBizSimilarHotel(perResponse.getMyBizSimilarToDirectHotel());
			if(StringUtils.isNotBlank(perResponse.getSection())){
				// In case number of items to be shown for Recently Booked and Recently Clicked section names is to be set as custom value from pms
				if (commonModifierResponse.getExtendedUser() != null && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(),commonModifierResponse.getExtendedUser().getAffiliateId())
						&& mobConfigHelper.getMyPartnerSectionListCount().containsKey(perResponse.getSection())) {
					personalizedSection.setMinItemsToShow(mobConfigHelper.getMyPartnerSectionListCount().get(perResponse.getSection()));
				} else if (mobConfigHelper.getCorpSectionListCount().containsKey(perResponse.getSection())) {
					personalizedSection.setMinItemsToShow(mobConfigHelper.getCorpSectionListCount().getOrDefault(perResponse.getSection(), 5));
				}
				if (CollectionUtils.isNotEmpty(perResponse.getHotels())
						&& personalizedSection.getMinItemsToShow() != null && perResponse.getHotels().size() > personalizedSection.getMinItemsToShow()
						&& ( mobConfigHelper.getCorpSectionListCount().containsKey(perResponse.getSection())
						|| mobConfigHelper.getMyPartnerSectionListCount().containsKey(perResponse.getSection()))
						&& !HORIZONTAL.equalsIgnoreCase(personalizedSection.getOrientation()))
					personalizedSection.setShowMore(true);
			}
			personalizedSection.setSectionBG(buildBGColor(perResponse.getSection(), personalizedSection.getOrientation(),personalizedSection.getHotelCardType()));
			personalizedSection.setFooterDetails(perResponse.getFooterDetails());
			personalizedSection.setTopHeaderText(perResponse.getTopHeaderText());
			personalizedSection.setPremium(perResponse.isPremium());
			updateHotelCardType(searchHotelsRequest, commonModifierResponse, perResponse, personalizedSection);
			personalizedSections.add(personalizedSection);
		});
		return personalizedSections;
	}

	protected abstract String buildBGColor(String section, String orientation, String cardType);

	private void overridePersonalizedSectionHeadingForDirectHotelSearch(SearchHotelsRequest searchHotelsRequest, PersonalizedSection personalizedSection) {
		MatchMakerRequest matchMakerDetails = null;
		String funnelSource = null;
		if(searchHotelsRequest != null){
			if(searchHotelsRequest.getMatchMakerDetails() != null){
				matchMakerDetails = searchHotelsRequest.getMatchMakerDetails();
			}
			if(searchHotelsRequest.getRequestDetails() != null && searchHotelsRequest.getRequestDetails().getFunnelSource() != null){
				funnelSource = searchHotelsRequest.getRequestDetails().getFunnelSource();
			}
		}
		if (matchMakerDetails != null && CollectionUtils.isNotEmpty(matchMakerDetails.getHotels())
				&& null != matchMakerDetails.getHotels().get(0) && null != matchMakerDetails.getHotels().get(0).getHotelId()) {
			if(funnelSource != null && FUNNEL_SOURCE_CORPBUDGET.equalsIgnoreCase(funnelSource)){
				personalizedSection.setHeading(polyglotService.getTranslatedData(Constants.CORP_BUDGET_PROPERTIES_TEXT));
			} else{
				personalizedSection.setHeading(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_RECOMMENDED_PROPERTIES_NEAR_THIS_PROPERTY));
			}
		}
	}

	private void buildFilterCardForMyBizAndNonMyBizProperties(PersonalizedResponse<SearchWrapperHotelEntity> perResponse,
			PersonalizedSection personalizedSection) {
		personalizedSection.setFilterInfo(buildFilterInfo());
		personalizedSection.setBottomSheet(buildBottomSheet(perResponse));
	}

	protected abstract BottomSheet buildBottomSheet(PersonalizedResponse<SearchWrapperHotelEntity> perResponse);

	private SectionFeature buildFilterInfo() {
		SectionFeature filterInfo = new SectionFeature();
		filterInfo.setIconUrl(myBizAssuredUrl);
		filterInfo.setText(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_ASSURED_FILTER_CARD_TEXT));
		filterInfo.setIconType("checkbox");
		filterInfo.setActionTitle(polyglotService.getTranslatedData(ConstantsTranslation.KNOW_MORE));
		List<Filter> filterCriteria = new ArrayList<>();
		Filter filter = new Filter();
		filter.setFilterGroup(FilterGroup.HOTEL_CATEGORY);
		filter.setFilterValue(Constants.MyBiz_Assured);
		filter.setFilterRange(null);
		filter.setRangeFilter(false);
		filterCriteria.add(filter);
		filterInfo.setFilterCriteria(filterCriteria);
		return filterInfo;
	}

	private boolean getCardInsertionAllowedValue(PersonalizedResponse<SearchWrapperHotelEntity> perResponse, String idContext) {
		return !perResponse.isHorizontal() && (!StringUtils.isNotBlank(perResponse.getSection()) || ((!StringUtils.isNotBlank(idContext) ||
				!LAST_BOOKED_HOTELS.equalsIgnoreCase(perResponse.getSection()) || !B2C.equalsIgnoreCase(idContext))));
	}

	private String buildOrientation(PersonalizedResponse<SearchWrapperHotelEntity> personalizedResponse, Map<String, String> expDataMap) {
		if (utility.isExperimentOn(expDataMap, EXP_MYPARTNER_LISTING_HN))
			return Constants.HN;
		else if (personalizedResponse.isHorizontal())
			return Constants.HORIZONTAL;
		return Constants.VERTICAL;
	}

	private Map<String, String> buildTrackingMap(TrackingVariables trackingVariables) {
		Map<String, String> trackingMap = new HashMap<>();
		if (trackingVariables != null && trackingVariables.isFamilyFriendly()) {
			trackingMap.put(m_c54_tracking_key, familyFriendlyTracking);
		}
		return trackingMap;
	}


	/**
	 * @param searchHotelsRequest can be null when called from buildComparatorResponse in StaticDetailResponseTransformer
	 *                            adding null check to searchHotelsRequest is mandatory to avoid NPE
	 * @param sectionName         Hotels section name
	 * @throws NullPointerException if null checks are not added for searchHotelsRequest
	 */
	public List<Hotel> buildPersonalizedHotels(List<SearchWrapperHotelEntity> hotelList, Map<String, String> expDataMap, ListingSearchRequest searchHotelsRequest, String sectionName,CommonModifierResponse commonModifierResponse,LocusData locusData, final MarkUpDetails markUpDetails, final String orientation) {
		if (CollectionUtils.isEmpty(hotelList)) {
			return null;
		}
		try {
			String currency = searchHotelsRequest != null && searchHotelsRequest.getSearchCriteria() != null ? searchHotelsRequest.getSearchCriteria().getCurrency() : DEFAULT_CUR_INR;
			String siteDomain = searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getSiteDomain() : StringUtils.EMPTY;
			boolean isImageExpEnable = utility.isImageExperimentEnable(expDataMap);
			boolean isDomRequest = searchHotelsRequest != null && searchHotelsRequest.getSearchCriteria() != null && DOM_COUNTRY.equalsIgnoreCase(searchHotelsRequest.getSearchCriteria().getCountryCode());
			boolean isMyPartnerRequest = (commonModifierResponse!=null) && (commonModifierResponse.getExtendedUser()!=null) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
			boolean isCallToBookV2Applicable = MapUtils.isNotEmpty(expDataMap) ? utility.isExperimentValid(expDataMap, callToBook,4) : false;
			String idContext = searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getIdContext() : null;
			List<Hotel> hotels = new ArrayList<>();
			String funnelSource;
			if (searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails()!=null && StringUtils.isNotBlank(searchHotelsRequest.getRequestDetails().getFunnelSource())) {
				funnelSource = searchHotelsRequest.getRequestDetails().getFunnelSource();
			} else {
				funnelSource = "";
			}
			final boolean[] odd = {true};
			long startTime = new Date().getTime();
			if(searchHotelsRequest!=null) {
				searchHotelsRequest.setExpDataMap(expDataMap);
			}
			hotelList.forEach(hotelEntity -> {
				try {
					Hotel hotel = new Hotel();
					hotel.setGroupBookingHotel(hotelEntity.isGroupBookingHotel());
					hotel.setGroupBookingPrice(hotelEntity.isGroupBookingPrice());
					if(hotelEntity.isActiveButOffline() && B2C.equalsIgnoreCase(idContext) && !isMyPartnerRequest && !isGccOrKsa()) {
						hotel.setStaticPrice(hotelEntity.getStaticPrice());
					}
					hotel.setIsABO(hotelEntity.isActiveButOffline());
					hotel.setMaskedPrice(hotelEntity.isMaskedPrice());
					hotel.setIsGroupBookingForSimilar(hotelEntity.isGroupBookingForSimilar());
					hotel.setId(hotelEntity.getId());
					hotel.setName(hotelEntity.getName());
					hotel.setMaskedPropertyName(hotelEntity.isMaskedPropertyName());
					hotel.setPropertyType(hotelEntity.getPropertyType());
					hotel.setPropertyLabel(hotelEntity.getPropertyLabel());
					hotel.setStayType(hotelEntity.getStayType());
					hotel.setHighSellingAltAcco(hotelEntity.isHighSellingAltAcco());
					hotel.setSpotlightApplicable(hotelEntity.isSpotlightApplicable());
					hotel.setServiceApartment(hotelEntity.isServiceApartment());
					hotel.setStarRatingType(hotelEntity.getStarRatingType());
					hotel.setStarRating(hotelEntity.getStarRating());
					hotel.setFromCity(hotelEntity.getFromCity());
					hotel.setDistance(hotelEntity.getDistance());
					hotel.setCrossSellTag(hotelEntity.getCrossSellTag());
					hotel.setShowCallToBook(hotelEntity.isShowCallToBook());
					hotel.setShowSimilarChainProperty(isDomRequest && hotelEntity.isAltAcco() && hotelEntity.getPropertyInfo()!=null && StringUtils.isNotEmpty(hotelEntity.getPropertyInfo().getChainId()));
					if(hotelEntity.getPropertyInfo()!=null && StringUtils.isNotEmpty(hotelEntity.getHotelChainName()) && Objects.nonNull(locusData) && StringUtils.isNotEmpty(locusData.getLocusName())) {
						String chainSectionHeading = polyglotService.getTranslatedData(ConstantsTranslation.CHAIN_PROPERTIES_HEADER);
						chainSectionHeading = chainSectionHeading.replace("{chain}", hotelEntity.getHotelChainName());
						chainSectionHeading = chainSectionHeading.replace("{city}", locusData.getLocusName());
						hotel.setChainPropertiesHeader(chainSectionHeading);
					}
					hotel.setPropertyInfo(hotelEntity.getPropertyInfo());
					if(hotelEntity.getPopularType() != null) {
						hotel.setPopularType(hotelEntity.getPopularType());
					}

					if(hotelEntity.getTrackingVariables()!=null) {
						hotel.setTrackingMap(buildTrackingMap(hotelEntity.getTrackingVariables()));
					}
					hotel.setDistanceUnit(hotelEntity.getDistanceUnit());
					hotel.setFreeCancellationText(hotelEntity.getFreeCancellationText());
					if(!utility.isExperimentTrue(expDataMap, ExperimentKeys.aboApplicable.name())) {
						hotel.setSoldOut(hotelEntity.getIsSoldOut() != null ? hotelEntity.getIsSoldOut() : (hotelEntity.getDisplayFare() == null));
						hotel.setSoldOutInfo(buildSoldOutInfo(hotelEntity.getSoldOutInfo()));
					} else if(hotel.getIsABO()==null || !hotel.getIsABO()) {
						hotel.setSoldOut(hotelEntity.getIsSoldOut() != null ? hotelEntity.getIsSoldOut() : (hotelEntity.getDisplayFare() == null));
						hotel.setSoldOutInfo(buildSoldOutInfo(hotelEntity.getSoldOutInfo()));
					} else if (!B2C.equalsIgnoreCase(idContext) || isMyPartnerRequest || isGccOrKsa()) {
						hotel.setSoldOut(hotelEntity.getIsSoldOut() != null ? hotelEntity.getIsSoldOut() : (hotelEntity.getDisplayFare() == null));
						hotel.setSoldOutInfo(buildSoldOutInfo(hotelEntity.getSoldOutInfo()));
					}
					hotel.setAlternateDates(hotelEntity.isAlternateDatesAvailable());
					hotel.setMultiRoomRecommendation(hotelEntity.isRecommendedMultiRoom());
					hotel.setIsAltAcco(hotelEntity.isAltAcco());
					hotel.setMtKey(hotelEntity.getMtkey());
					hotel.setGeoLocation(commonResponseTransformer.buildGeoLocation(hotelEntity.getGeoLocation()));
					hotel.setDeepLink(hotelEntity.getDesktopDeeplink());
					hotel.setAppDeeplink(hotelEntity.getAppDeeplink());
					hotel.setSharingUrl(hotelEntity.getSharingUrl());
					hotel.setLocationPersuasion(hotelEntity.getLocationPersuasion());
					hotel.setMedia(commonResponseTransformer.buildMedia(hotelEntity.getMainImages(), hotelEntity.getHotelVideos(), expDataMap, isImageExpEnable));
					if(hotelEntity.isHas360Image()){
						hotel.setView360IconUrl(view360IconUrl);
					}
					hotel.setTotalImageCount(CollectionUtils.isNotEmpty(hotelEntity.getMainImages()) ? hotelEntity.getMainImages().size() : 0);
					hotel.setTravellerImageCount(hotelEntity.getTravellerImageCount());
					if(hotelEntity.isShowCallToBook()) {
						String device = (searchHotelsRequest!=null && searchHotelsRequest.getDeviceDetails()!=null && StringUtils.isNotEmpty(searchHotelsRequest.getDeviceDetails().getBookingDevice()))?searchHotelsRequest.getDeviceDetails().getBookingDevice(): EMPTY_STRING;
						if(hotelEntity.isActiveButOffline() && utility.isExperimentTrue(expDataMap, ExperimentKeys.aboApplicable.name())) {
							hotel.setRequestCallbackData(utility.buildRequestToCallBackDataForB2C(PAGE_CONTEXT_LISTING));
						} else if(!hotelEntity.isActiveButOffline() && isCallToBookV2Applicable){
							hotel.setRequestCallbackData(utility.buildRequestToCallBackDataV2(PAGE_CONTEXT_LISTING));
						}else if(!hotelEntity.isActiveButOffline() && !isCallToBookV2Applicable){
							hotel.setRequestCallbackData(utility.buildRequestToCallBackData(PAGE_CONTEXT_LISTING, hotelEntity.getName(), device));
						}
					}
					hotel.setIsRTB(hotelEntity.isRTB());
					hotel.setIsABSO(hotelEntity.isABSO());
					hotel.setIsMLOS(hotelEntity.getCalendarCriteria() != null);
					hotel.setUpsellRateplanInfo(utility.isExperimentOn(expDataMap, EXP_GBRP) ? commonResponseTransformer.buildUpsellRateplanInfo(hotelEntity.getUpsellRateplanInfo(),currency) : null);
					GroupPrice groupPrice = new GroupPrice();
					//A new groupPrice node under priceDetail will be enabled if GRPN: T and is a group booking funnel on listing page
					boolean enableNewGroupDesign = searchHotelsRequest != null && searchHotelsRequest.getExpData() != null && utility.isExperimentOn(expDataMap, GRPN_GROUP_BOOKING_EXCLUSIVE_TAX_EXP) && isGroupBookingFunnel(funnelSource) && !isMyPartnerRequest;
					List<String> groupPriceSavingText = null;
					if (enableNewGroupDesign) {
						//A new groupPrice node under priceDetail will be displayed
						groupPriceForGRPN(searchHotelsRequest, hotelEntity, groupPrice);
					} else {
						//Existing flow- groupPriceText and SavingsText under priceDetail will be displayed after calculations
						groupPriceSavingText = getGroupPriceAndSavingText(hotelEntity, searchHotelsRequest, expDataMap);
					}
					if (FUNNEL_DAYUSE.equalsIgnoreCase(funnelSource)) {
						hotel.setSlotDetail(buildSlotDetails(hotelEntity, expDataMap, searchHotelsRequest));
					}
					boolean setPriceDetailsBasisABO = !hotelEntity.isActiveButOffline() || !B2C.equalsIgnoreCase(idContext) || isMyPartnerRequest || isGccOrKsa();
					if(!utility.isExperimentTrue(expDataMap, ExperimentKeys.aboApplicable.name())){
						setPriceDetailsBasisABO = true;
					}
				/* START - Build Price Detail */
				if ((FUNNEL_DAYUSE.equalsIgnoreCase(funnelSource) && hotelEntity.getDisplayFare() != null)) {
					if (setPriceDetailsBasisABO && !hotelEntity.getDisplayFare().isSlotRate() && dayUseUtil.shouldSetPriceDetailsForDayUse(hotel.getSlotDetail(), hotelEntity.getDisplayFare()) && dayUseUtil.isXPercentRulePassed(hotel.getSlotDetail(), hotelEntity.getDisplayFare(), thresholdForSlashedAndDefaultHourPrice)) {
						hotel.setPriceDetail(buildPriceDetailForDayUse(hotelEntity.getDisplayFare()));
					}
				} else {
//					String idContext = searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getIdContext() : null;
					if(setPriceDetailsBasisABO) {
//						TODO: check if we can block the creation of displayPriceBreakDownList on hes only currently if we do that there are multiple null pointers
						hotel.setPriceDetail(buildPriceDetail(hotelEntity.getDisplayFare() != null ? hotelEntity.getDisplayFare().getDisplayPriceBreakDown() : null,
								commonResponseTransformer.enableSaveValue(expDataMap),
								commonResponseTransformer.enableDiscount(hotelEntity.getDisplayFare() != null ? hotelEntity.getDisplayFare().getDisplayPriceBreakDown() : null),
								(searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null && searchHotelsRequest.getRequestDetails().isMetaInfo()),
								hotelEntity.getLowestRateSegmentId(), groupPriceSavingText, idContext, enableNewGroupDesign, groupPrice));
					}
				}
					// Adding Markup for MyPartner
					if (Objects.nonNull(hotel.getPriceDetail()))
						hotel.setPartnerDetails(getPartnerDetails(hotel.getPriceDetail(), pricingEngineHelper.getMarkUpForHotels(markUpDetails, hotel.getPriceDetail().getDiscountedPriceWithTax())));
				/* END -  Build Price Detail */

					//updateAppDeeplinkForNoSlot(hotel, funnelSource);
					hotel.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(hotelEntity.getDisplayFare() != null ? hotelEntity.getDisplayFare().getCorpMetaData() : null, utility.isTCSV2FlowEnabled(expDataMap)));
				/* Add booking confirmation persuasion for negotiated rate hotels.
				   Negotiated rates are the one-on-one rates that are directly negotiated between the hotel and the organization.
				*/
					if (VERTICAL.equalsIgnoreCase(orientation) && RTB_EMAIL.equalsIgnoreCase(hotelEntity.getLowestRateRpBookingModel())) {
						addBookingConfirmationPersuasion(hotelEntity);
					}

					/* Add special fare persuasion for negotiated rate hotels.
					 */
					if (corpPreferredRateSegmentId != null && corpPreferredRateSegmentId.equals(hotelEntity.getLowestRateSegmentId())) {
						addSpecialFarePersuasion(hotelEntity);
					}

					hotel.setHotelPersuasions(hotelEntity.getHotelPersuasions());
					if (utility.isExperimentOn(expDataMap, EXP_PERNEW)) {
						long start = System.currentTimeMillis();
						persuasionUtil.updatePersuasionsForDesktopAndPwa((Map<String, Map<String, Object>>) hotel.getHotelPersuasions());
						LOGGER.debug("Time Taken to update persuasion for Desktop/PWA is: {} ", System.currentTimeMillis() - start);
					}
					//On Old apps combined OTAs like MMT_BKG not supported, hence changing such OTA to MMT for old apps using exp COMBINED_OTA
					boolean combinedOTASupported = utility.isExperimentOn(expDataMap, COMBINED_OTA);
					if (MapUtils.isNotEmpty(expDataMap) && (Constants.TRUE.equalsIgnoreCase(expDataMap.get(Constants.UNIFIED_USER_RATING)) || Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.UGCV2)))) {
						hotel.setReviewSummary(commonResponseTransformer.buildReviewSummary(hotelEntity.getReviewSummary(), combinedOTASupported));
					} else {
						hotel.setReviewSummary(commonResponseTransformer.buildReviewSummary(hotelEntity.getCountryCode(), hotelEntity.getFlyfishReviewSummary(), combinedOTASupported));
					}
					hotel.setLocationDetail(buildLocationDetail(hotelEntity.getCityCode(), hotelEntity.getCityName(), "city", hotelEntity.getCountryCode(), hotelEntity.getCountryName()));
					hotel.setCategories(hotelEntity.getCategories());
					hotel.setShortList(hotelEntity.isShortList());
					hotel.setTrackingInfo(hotelEntity.getTrackingInfo());
					hotel.setSponsored(hotelEntity.isSponsored());
					hotel.setNewType(hotelEntity.isNewType()); //"NEW" is the tag type which will be sent by singularity for reference (HTL-37120)
					hotel.setPoiTag(hotelEntity.getPoiTag());
					hotel.setTotalRoomCount((hotelEntity.getDisplayFare() != null && hotelEntity.getDisplayFare().getTotalRoomCount() != null) ? hotelEntity.getDisplayFare().getTotalRoomCount() : null);
					hotel.setExtraMeals(hotelEntity.getExtraMeals());
					hotel.setViewType(hotelEntity.getViewType());
					hotel.setRatePersuasionText(hotelEntity.getRatePersuasionText());
					hotel.setCalendarCriteria(buildCalendarCriteria(hotelEntity.getCalendarCriteria()));
					hotel.setHotelType(hotelEntity.getHotelType());
					boolean sectionMyBizSimilarToDirectHtl = MYBIZ_SIMILAR_TO_DIRECT_HOTEL.equalsIgnoreCase(sectionName);
					String drivingTimeText = "";
					if (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null && SHORTSTAYS_FUNNEL.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource())
							&& commonResponseTransformer.isEligibleForNearbyFlow(searchHotelsRequest.getDeviceDetails())) {
						drivingTimeText = commonResponseTransformer.buildDrivingTimeText(hotelEntity.getDrivingTime());
						persuasionUtil.addShortStayPeithoPersuasionToHotelPersuasion(hotel, hotelEntity.getUspShortStayValue(), hotelEntity.getLocationPersuasion());
					}
					if (searchHotelsRequest != null)
						addLocationPersuasionToHotelPersuasions(hotel, hotelEntity.getLocationPersuasion(), hotelEntity.getFacilityHighlights(), searchHotelsRequest, commonResponseTransformer.enableAmenitiesPersuasion(expDataMap, searchHotelsRequest.getRequestDetails().getFunnelSource(), isMyPartnerRequest), sectionMyBizSimilarToDirectHtl, hotelEntity.getDayUsePersuasionsText(), hotelEntity.getNearestGroundTransportPoi(), drivingTimeText, locusData,commonModifierResponse!=null && commonModifierResponse.isHomestayV2Flow());
					else
						addLocationPersuasionToHotelPersuasions(hotel, hotelEntity.getLocationPersuasion(), hotelEntity.getFacilityHighlights(), null, commonResponseTransformer.enableAmenitiesPersuasion(expDataMap, "HOTELS", isMyPartnerRequest), sectionMyBizSimilarToDirectHtl, hotelEntity.getDayUsePersuasionsText(), hotelEntity.getNearestGroundTransportPoi(), drivingTimeText, locusData,commonModifierResponse!=null && commonModifierResponse.isHomestayV2Flow());
					addPersuasionHoverData(hotel, hotelEntity, hotelEntity.getCancellationTimeline(), hotelEntity.getDisplayFare());
					addSeoTextPersuasion(hotel, hotelEntity, odd[0], searchHotelsRequest, sectionName);
					hotel.setLastBookedInfo(hotelEntity.getLastBookedInfo());
					hotel.setHotelBottomCard(buildHotelBottomCard(hotelEntity.getQuickBookInfo()));
					hotel.setHotelTopCard(buildHotelTopCard(hotelEntity.getMyBizSimilarToDirectObj()));
					hotel.setReviewDeeplinkUrl(hotelEntity.getReviewDeeplinkUrl());
					if(StringUtils.isNotEmpty(hotel.getReviewDeeplinkUrl()) && !hotel.getReviewDeeplinkUrl().contains("mpn"))
					{
						String reviewDeeplinkUrl = hotel.getReviewDeeplinkUrl();
						hotel.setReviewDeeplinkUrl(reviewDeeplinkUrl.concat(Constants.AND_SEPARATOR + "mpn" + Constants.PR_SEPARATOR + hotel.isMaskedPropertyName()));
					}
					hotel.setSearchRoomDeeplinkUrl(hotelEntity.getSearchRoomDeeplinkUrl());
					
					// Set detail deeplink URL and append oldBookingId if AlternateBookingInfo is present with alternateBooking true
					String detailDeeplinkUrl = hotelEntity.getDetailDeeplinkUrl();
					if (searchHotelsRequest != null && 
						searchHotelsRequest.getAlternateBookingInfo() != null && 
						searchHotelsRequest.getAlternateBookingInfo().isAlternateBooking() &&
						StringUtils.isNotEmpty(searchHotelsRequest.getAlternateBookingInfo().getOldBookingId())) {
						
						if (StringUtils.isNotEmpty(detailDeeplinkUrl)) {
							String separator = detailDeeplinkUrl.contains("?") ? Constants.AND_SEPARATOR : "?";
							detailDeeplinkUrl += separator + "oldBookingId" + Constants.PR_SEPARATOR + searchHotelsRequest.getAlternateBookingInfo().getOldBookingId();
						}
					}
					hotel.setDetailDeeplinkUrl(detailDeeplinkUrl);
					hotel.setHeroImage(hotelEntity.getHeroImage());
					hotel.setHeroVideoUrl(hotelEntity.getHeroVideoUrl());
					if (StringUtils.isNotEmpty(hotelEntity.getSeoUrl())) {
						hotel.setSeoUrl(hotelEntity.getSeoUrl());
					}

					if (hotelEntity.getCollectionCardPersuasion() != null) {
						hotel.setCollectionCardPersuasion(new CollectionCardPersuasion());
						hotel.getCollectionCardPersuasion().setText(hotelEntity.getCollectionCardPersuasion().getText());
						hotel.getCollectionCardPersuasion().setIconUrl(hotelEntity.getCollectionCardPersuasion().getIconUrl());
					}
					hotel.setMmtHotelCategory(hotelEntity.getMmtHotelCategory());
					hotel.setWishListed(hotelEntity.isWishListed());
					hotel.setStoryViewDescription(hotelEntity.getLongTailStoryPersuasions());
					hotel.setPropertyHighlightText(hotelEntity.getLongTailPropertyCardPersuasions());

					// Based on this feature flag need to suppress few persuasions at specific placeholders configured at PMS and provided by client
					//At PMS level maintain map of key and List or PlaceHolders to suppress to make it generic if in future we want to suppress different placeholder for different flow we can use the same map
					if (searchHotelsRequest != null && searchHotelsRequest.getFeatureFlags() != null && searchHotelsRequest.getFeatureFlags().isPersuasionSuppression()) {
						Map<String, List<String>> placeholdersToShowMap = null;
						List<String> placeholdersToShow = null;
						try {
							//Not Using PMS config here, as it will be deprecated
							placeholdersToShowMap = objectMapperUtil.getObjectFromJsonWithType(placeHoldersToShowConfig, new TypeReference<Map<String, List<String>>>() {},
									DependencyLayer.CLIENTGATEWAY);
						} catch (JsonParseException e) {
							e.printStackTrace();
						}
						if (MapUtils.isNotEmpty(placeholdersToShowMap)) {
							placeholdersToShow = placeholdersToShowMap.get(SIMILAR_HOTELS);
							if(searchHotelsRequest!=null && isBookingDeviceDesktop(searchHotelsRequest.getDeviceDetails())) {
								placeholdersToShow = placeholdersToShowMap.get(SIMILAR_HOTELS_DT);
							}
						}
						LOGGER.debug("Placeholder we want to show for PersuasionSuppression {}", placeholdersToShow);
						if (CollectionUtils.isNotEmpty(placeholdersToShow)) {
							Map<Object, Object> hotelPersuasions = (Map<Object, Object>) hotel.getHotelPersuasions();
							List<String> placeholdersToBeBlocked = new ArrayList<>();
							for (Object key : hotelPersuasions.keySet()) {
								if (!placeholdersToShow.contains(key)) {
									placeholdersToBeBlocked.add(key.toString());
								}
							}
							LOGGER.debug("Placeholder we have to block from Persuasion for PersuasionSuppression {}", placeholdersToBeBlocked);
							for (String placeholderToBeBlocked : placeholdersToBeBlocked) {
								if (MapUtils.isNotEmpty(hotelPersuasions)) {
									hotelPersuasions.remove(placeholderToBeBlocked);
								}
							}
						}
					}


					String bookingDevice = (searchHotelsRequest != null && searchHotelsRequest.getDeviceDetails() != null) ? searchHotelsRequest.getDeviceDetails().getBookingDevice() : null;
					if (MYBIZ_SIMILAR_TO_DIRECT_HOTEL.equalsIgnoreCase(sectionName) && (DEVICE_IOS.equalsIgnoreCase(bookingDevice)
							|| DEVICE_OS_ANDROID.equalsIgnoreCase(bookingDevice) || DEVICE_OS_DESKTOP.equalsIgnoreCase(bookingDevice))) {
						List<String> placeholdersToBeBlocked = DEVICE_OS_DESKTOP.equalsIgnoreCase(bookingDevice) ? desktopPersPlaceholdersToBeBlockedDemandConc : appsPersPlaceholdersToBeBlockedDemandConc;
						Map<Object, Object> hotelPersuasions = (Map<Object, Object>) hotel.getHotelPersuasions();
						for (String placeholderToBeBlocked : placeholdersToBeBlocked) {
							if (MapUtils.isNotEmpty(hotelPersuasions)) {
								hotelPersuasions.remove(placeholderToBeBlocked);
							}
						}

					}
					commonResponseTransformer.buildSelectiveHotelPersuasions(hotel, hotelEntity);
					if (searchHotelsRequest!=null && checkIfHorizontalSection(sectionName, hotelList.size(), searchHotelsRequest.getDeviceDetails())) {
						commonResponseTransformer.buildTopSectionPersuasion(hotel, hotelEntity.getTag(), sectionName, searchHotelsRequest.getDeviceDetails() != null ? searchHotelsRequest.getDeviceDetails().getBookingDevice() : null);
						commonResponseTransformer.removePlaceHolderPersuasionsForSection(hotel, sectionName);
					}
					// Based on this node client will hit search-hotel api similarHotel flow, and we set this flag based on some conditions provide by Product and configured at PMS to change them dynamically
					//HTL-39243 Supress SimilarHotels true for Direct Searched Property
					if (commonHelper.checkValidHotel(searchHotelsRequest, hotelEntity)) {
						try {
							if (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null
									&& searchHotelsRequest.getSearchCriteria() != null
									&& searchHotelsRequest.getRequestDetails().getIdContext() != null
									&& searchHotelsRequest.getRequestDetails().getFunnelSource() != null
									&& searchHotelsRequest.getSearchCriteria().getLocationType() != null
									&& (B2C.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getIdContext())
									|| CORP_ID_CONTEXT.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getIdContext()))
									&& FUNNEL_SOURCE_HOTELS.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource())
									&& !(Constants.ZONE).equalsIgnoreCase(searchHotelsRequest.getSearchCriteria().getLocationType()))
								hotel.setSimilarHotelsRequired(getSimilarHotelRequired(hotelEntity));
						} catch (JsonParseException e) {
							e.printStackTrace();
						}
					}

					odd[0] = !odd[0];
					hotels.add(hotel);
				} catch(Exception ex) {
					LOGGER.error("An exception occured while building hotel from HotelEntity",ex);
				}
			});
			LOGGER.warn("Hotel List preparation time : {}", new Date().getTime() - startTime);
			return hotels;
		} catch (Exception e) {
			LOGGER.error("An exception occurred in buildPersonalizedHotels ", e);
			return null;
		}
	}

	private PriceDetail getPartnerDetails(final PriceDetail priceDetail, final double markUp) {
		final PriceDetail priceWithMarkUp = new PriceDetail();
		if (Objects.nonNull(priceDetail.getPrice()) && Objects.nonNull(priceDetail.getPriceWithTax()) && Objects.nonNull(priceDetail.getDiscountedPrice()) && Objects.nonNull(priceDetail.getDiscountedPriceWithTax())) {
			priceWithMarkUp.setPrice(priceDetail.getPrice() + markUp);
			priceWithMarkUp.setPriceWithTax(priceDetail.getPriceWithTax() + markUp);
			priceWithMarkUp.setDiscountedPrice(priceDetail.getDiscountedPrice() + markUp);
			priceWithMarkUp.setDiscountedPriceWithTax(priceDetail.getDiscountedPriceWithTax() + markUp);
		}
		return priceWithMarkUp;
	}
	private boolean getSimilarHotelRequired(SearchWrapperHotelEntity hotelEntity) throws JsonParseException {
		FilterConditions filterConditions = objectMapperUtil.getObjectFromJsonWithType(filterConditionsConfig,new TypeReference<FilterConditions>() {},
				DependencyLayer.CLIENTGATEWAY);
		double price = -1;
		if(hotelEntity.getDisplayFare()!=null && hotelEntity.getDisplayFare().getDisplayPriceBreakDown()!=null) {
			price = (hotelEntity.getDisplayFare().getDisplayPriceBreakDown().getDisplayPrice() - (hotelEntity.getDisplayFare().getDisplayPriceBreakDown().isTaxIncluded() ? hotelEntity.getDisplayFare().getDisplayPriceBreakDown().getTotalTax() : 0));
		}

		//if hotel category is in includeCategoryList then only we return true
		boolean isIncludeCategory = false;
		if (filterConditions != null && CollectionUtils.isNotEmpty(filterConditions.getCategoriesIncluded()) && CollectionUtils.isNotEmpty(hotelEntity.getCategories())) {
			for (String category : hotelEntity.getCategories()) {
				if (filterConditions.getCategoriesIncluded().contains(category)) {
					isIncludeCategory = true;
					break;
				}
			}
		}

		if (!isIncludeCategory && filterConditions != null && filterConditions.getRange() != null &&
				(price < filterConditions.getRange().getMinValue() || price > filterConditions.getRange().getMaxValue())) {
			return false;
		}

		//if hotel category is not in excludedCategoryList then only we return true
		boolean isExcludedCategory = false;
		if(filterConditions != null && CollectionUtils.isNotEmpty(filterConditions.getCategoriesExcluded()) && CollectionUtils.isNotEmpty(hotelEntity.getCategories())) {
			for (String category : hotelEntity.getCategories()) {
				if (filterConditions.getCategoriesExcluded().contains(category)) {
					isExcludedCategory = true;
					break;
				}
			}
		}
		return !isExcludedCategory || isIncludeCategory;
	}


	public void buildHiddenGemPersuasions(List<SearchWrapperHotelEntity> hotelList) {
		hotelList.forEach(this::addPersuasionsForHiddenGemCard);
	}

	public abstract void addPersuasionsForHiddenGemCard(SearchWrapperHotelEntity hotelEntity);

	private void updateAppDeeplinkForNoSlot(Hotel hotel, String funnelSource) {
		if (CollectionUtils.isEmpty(hotel.getSlotDetail()) && hotel.getPriceDetail() != null && FUNNEL_DAYUSE.equalsIgnoreCase(funnelSource)) {
			if (StringUtils.isNotBlank(hotel.getAppDeeplink()) && hotel.getAppDeeplink().contains(FUNNEL_DAYUSE)) {
				String updatedDeeplink = hotel.getAppDeeplink().replace(FUNNEL_DAYUSE, FUNNEL_SOURCE_HOTELS);
				hotel.getPriceDetail().setHotelSearchDeeplink(null);
			}
		}
	}

	protected PriceDetail buildPriceDetailForDayUse(DisplayFare displayFare) {
		if (displayFare == null) {
			return null;
		}
		PriceDetail priceDetail = new PriceDetail();
		priceDetail.setTotalTax(displayFare.getTax() != null ? displayFare.getTax().getValue() : 0);
		if (displayFare.getSlashedPrice() != null) {
			priceDetail.setPrice(displayFare.getSlashedPrice().getSellingPriceNoTax());
			priceDetail.setPriceWithTax(displayFare.getSlashedPrice().getSellingPriceWithTax());
			priceDetail.setDiscountedPrice(displayFare.getSlashedPrice().getSellingPriceNoTax());
			priceDetail.setDiscountedPriceWithTax(displayFare.getSlashedPrice().getSellingPriceWithTax());
		}
		return priceDetail;
	}
	public List<SlotDetail> buildSlotDetails(SearchWrapperHotelEntity hotelEntity, Map<String, String> expDataMap, ListingSearchRequest searchHotelsRequest) {
		if(hotelEntity == null || CollectionUtils.isEmpty(hotelEntity.getRecommendedRoomTypeDetails())){
			return null;
		}
		List<SlotDetail> slotDetailList = new ArrayList<>();
		List<RoomTypeDetails> roomTypeDetailsList = hotelEntity.getRecommendedRoomTypeDetails();
		Set<Integer> slotDetailCount = new HashSet<>();
		Slot slot = null;
		SlotDetail slotDetail = null;
		String slotTime = null;

		// If a particular select is requested return that slot only
		if (searchHotelsRequest != null && searchHotelsRequest.getSearchCriteria() != null && searchHotelsRequest.getSearchCriteria().getSlot() != null
				&& searchHotelsRequest.getSearchCriteria().getSlot().getDuration() != null && searchHotelsRequest.getSearchCriteria().getSlot().getDuration() > 0) {
			Integer requestedSlotDuration = searchHotelsRequest.getSearchCriteria().getSlot().getDuration();
			for (RoomTypeDetails roomTypeDetails : roomTypeDetailsList) {
				if (roomTypeDetails.getSlot() != null && roomTypeDetails.getSlot().getDuration() != null
						&& Objects.equals(requestedSlotDuration, roomTypeDetails.getSlot().getDuration())) {
					buildSlotDetailList(hotelEntity, expDataMap, searchHotelsRequest, slotDetailList, slotDetailCount, roomTypeDetails);
				}
			}
			return slotDetailList;
		}

		for(RoomTypeDetails roomTypeDetails: roomTypeDetailsList){
			if(roomTypeDetails.getSlot() != null && roomTypeDetails.getSlot().getDuration() != null) {
				slotTime = buildSlotDetailList(hotelEntity, expDataMap, searchHotelsRequest, slotDetailList, slotDetailCount, roomTypeDetails);
			}
		}

		if (!slotDetailCount.isEmpty() && slotDetailCount.size() < 3) {
			Iterator<Integer> itr = missingSlotDetails!=null && missingSlotDetails.getDuration()!=null ? missingSlotDetails.getDuration().iterator() : null;
			while (itr!=null && itr.hasNext()) {
				Integer value = itr.next();
				if(!slotDetailCount.contains(value)){
					slotDetail = new SlotDetail();
					slot = new Slot();
					slot.setDuration(value);
					com.mmt.hotels.model.response.dayuse.Slot tempSlot = new com.mmt.hotels.model.response.dayuse.Slot();
					tempSlot.setDuration(slot.getDuration());
					tempSlot.setTimeSlot(slotTime);
					slot.setTimeSlot(utility.calculateTimeSlot_Meridiem(tempSlot));
					slotDetail.setSlot(slot);
					slotDetailList.add(slotDetail);
				}
			}
		}
		return slotDetailList;
	}

	private String buildSlotDetailList(SearchWrapperHotelEntity hotelEntity, Map<String, String> expDataMap, ListingSearchRequest searchHotelsRequest, List<SlotDetail> slotDetailList,
									   Set<Integer> slotDetailCount, RoomTypeDetails roomTypeDetails) {
		Slot slot;
		SlotDetail slotDetail;
		String slotTime;
		slotDetail = new SlotDetail();
		slotDetail.setPriceDetail(buildPriceDetail(roomTypeDetails.getTotalDisplayFare().getDisplayPriceBreakDown() != null ? roomTypeDetails.getTotalDisplayFare().getDisplayPriceBreakDown() : null,
				commonResponseTransformer.enableSaveValue(expDataMap),
			commonResponseTransformer.enableDiscount(hotelEntity.getDisplayFare() != null ? hotelEntity.getDisplayFare().getDisplayPriceBreakDown() : null),
			(searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null && searchHotelsRequest.getRequestDetails().isMetaInfo()),
			hotelEntity.getLowestRateSegmentId(), Collections.emptyList(), null,false,null));
		slot = new Slot();
		slot.setDuration(roomTypeDetails.getSlot().getDuration());
		slotDetailCount.add(slot.getDuration());
		slotTime = roomTypeDetails.getSlot().getTimeSlot();
		slot.setTimeSlot(utility.calculateTimeSlot_Meridiem(roomTypeDetails.getSlot()));
		slotDetail.setSlot(slot);
		slotDetailList.add(slotDetail);
		return slotTime;
	}

	public List<String> getGroupPriceAndSavingText(SearchWrapperHotelEntity searchWrapperHotelEntity, ListingSearchRequest listingSearchRequest, Map<String, String> expDataMap) {
		String currency = (listingSearchRequest != null && listingSearchRequest.getSearchCriteria() != null) ? listingSearchRequest.getSearchCriteria().getCurrency() : "INR";
		List<String> groupPriceAndSavingText = new ArrayList<>();
		boolean isPerNewEnabled = utility.isExperimentOn(expDataMap, EXP_PERNEW);
		long displayPrice = (long) ((searchWrapperHotelEntity.getDisplayFare() != null && searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown() != null) ? searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getTotalAmount() : 0.0d);
		long savingPerc = (long) ((searchWrapperHotelEntity.getDisplayFare() != null && searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown() != null) ? searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getSavingPerc() : 0.0d);
		int roomCount = searchWrapperHotelEntity.getRoomCount();
		String checkIn = null;
		String checkOut = null;
		if (listingSearchRequest != null && listingSearchRequest.getSearchCriteria() != null) {
			checkOut = listingSearchRequest.getSearchCriteria().getCheckOut();
			checkIn = listingSearchRequest.getSearchCriteria().getCheckIn();
		}
		int nightCount = 0;
		if (StringUtils.isNotBlank(checkIn) && StringUtils.isNotBlank(checkOut)) {
			nightCount = dateUtil.getDaysDiff(LocalDate.parse(checkIn), LocalDate.parse(checkOut));
		}
		if (StringUtils.equalsIgnoreCase(searchWrapperHotelEntity.getStayType(), "ENTIRE") && (searchWrapperHotelEntity.getDisplayFare() != null && searchWrapperHotelEntity.getDisplayFare().getTotalRoomCount() == 1)) {
			NumberFormat numberFormat = com.ibm.icu.text.NumberFormat.getInstance(new Locale("en","IN"));
			groupPriceAndSavingText.add(polyglotService.getTranslatedData(Utility.getGroupPriceTextOrSavingPercKey(GROUP_PRICE_TEXT_ONE_NIGHT, listingSearchRequest != null ? listingSearchRequest.getClient() : "")+ (isPerNewEnabled?(UNDERSCORE + EXP_PERNEW):""))
					.replace(ConstantsTranslation.AMOUNT.toUpperCase(), numberFormat.format(displayPrice))
					.replace("{NIGHT_COUNT}", String.valueOf(nightCount))
					.replace("{ROOM_COUNT}", String.valueOf(roomCount)));
		} else {
			NumberFormat numberFormat = com.ibm.icu.text.NumberFormat.getInstance(new Locale("en","IN"));
			groupPriceAndSavingText.add(commonResponseTransformer.getGroupPriceText(roomCount, nightCount, numberFormat.format(displayPrice), listingSearchRequest != null ? listingSearchRequest.getClient() : "", isPerNewEnabled));
		}


		String savingPercText = null;
		if (savingPerc != 0.0 && searchWrapperHotelEntity.isGroupBookingPrice()) {
			savingPercText = polyglotService.getTranslatedData(Utility.getGroupPriceTextOrSavingPercKey(SAVING_PERC_TEXT, listingSearchRequest != null ? listingSearchRequest.getClient() : "") + (isPerNewEnabled?(UNDERSCORE + EXP_PERNEW):"")).replace(SAVING_PERC, String.valueOf(savingPerc));
		}
		groupPriceAndSavingText.add(savingPercText);

		if (StringUtils.isNotEmpty(currency) && !currency.equalsIgnoreCase("INR")) {
			List<String> updatedGroupPriceAndSavingText = new ArrayList<>();
			for (String entry : groupPriceAndSavingText) {
				if (StringUtils.isNotEmpty(entry)) {
					String currencySymbol = currency + " ";
					updatedGroupPriceAndSavingText.add(entry.replaceAll("₹", currencySymbol));
				} else {
					updatedGroupPriceAndSavingText.add(entry);
				}
			}
			return updatedGroupPriceAndSavingText;
		}
		return groupPriceAndSavingText;
	}

	private HotelCard buildHotelTopCard(MyBizSimilarToDirectObj myBizSimilarToDirectObj) {
		if (myBizSimilarToDirectObj == null) {
			return null;
		}
		HotelCard hotelTopCard = new HotelCard();
		hotelTopCard.setHeading(getMyBizDirectHotelDistanceText(myBizSimilarToDirectObj.getDistance()));
		hotelTopCard.setSubHeading(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_DIRECT_HOTEL_SUBHEADING));
		hotelTopCard.setTag(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_DIRECT_HOTEL_TAG));
		if (CollectionUtils.isNotEmpty(myBizSimilarToDirectObj.getAmenities())) {
			List<Facility> amenities = new ArrayList<>();
			myBizSimilarToDirectObj.getAmenities().forEach(myBizAmenity -> amenities.add( getFacility(myBizAmenity)));
			hotelTopCard.setAmenities(amenities);
		}
		return hotelTopCard;
	}

	private Facility getFacility(FeaturedAmenity myBizAmenity) {
		Facility facility = new Facility();
		facility.setIconUrl(myBizAmenity.getIconUrl());
		facility.setName(myBizAmenity.getName());
		return facility;
	}

	private HotelCard buildHotelBottomCard(QuickBookInfo quickBookInfo) {
		if (quickBookInfo != null) {
			return buildQuickBookCard(quickBookInfo);
		}
		return null;
	}

	private PriceDetail buildPriceDetail(DisplayPriceBreakDown displayPriceBreakDown,
										 boolean enableSaveValue, boolean enableDiscount, boolean metaInfo, String lowestRateSegmentId, List<String> groupPriceAndSavingText, String idContext, boolean enableNewGroupDesign,GroupPrice groupPriceNewDesign) {
		if (displayPriceBreakDown == null) {
			return null;
		}
		PriceDetail priceDetail = new PriceDetail();
		if (CollectionUtils.isNotEmpty(groupPriceAndSavingText)) {
			priceDetail.setGroupPriceText(groupPriceAndSavingText.get(0));
			priceDetail.setSavingsText(groupPriceAndSavingText.get(1));
		}
		//groupPrice node is displayed only when GRPN:T and is a group booking funnel
		if(enableNewGroupDesign && null!=groupPriceNewDesign)
		{
		priceDetail.setGroupPrice(groupPriceNewDesign);
		}
		priceDetail.setCoupon(buildCoupon(displayPriceBreakDown.getCouponInfo()));
		priceDetail.setEmiDetails(buildEmiDetails(displayPriceBreakDown.getEmiDetails()));
		priceDetail.setTotalTax(displayPriceBreakDown.getTotalTax());
		if(enableSaveValue) {
			priceDetail.setTotalSaving(displayPriceBreakDown.getTotalSaving());
			priceDetail.setSavingPerc(displayPriceBreakDown.getSavingPerc());
		}
		priceDetail.setPrice(displayPriceBreakDown.getNonDiscountedPrice()-(displayPriceBreakDown.isTaxIncluded()?displayPriceBreakDown.getTotalTax():0));
		priceDetail.setPriceWithTax(displayPriceBreakDown.getNonDiscountedPrice()+(displayPriceBreakDown.isTaxIncluded()?0:displayPriceBreakDown.getTotalTax()));
		priceDetail.setDiscountedPrice(displayPriceBreakDown.getDisplayPrice()-(displayPriceBreakDown.isTaxIncluded()?displayPriceBreakDown.getTotalTax():0));
		priceDetail.setDiscountedPriceWithTax(displayPriceBreakDown.getDisplayPrice()+(displayPriceBreakDown.isTaxIncluded()?0:displayPriceBreakDown.getTotalTax()));
		priceDetail.setPricingKey(displayPriceBreakDown.getPricingKey());
		/*
		* myPartner change log :
		* 	This value is added in the schema repo.
		* 	We've not versioned this, since there is no changes to existing keys, this is a new addition only used for
		* 	myPartner affilaiteID
		* 	Conditionally it will be set - value > 100 and > 5% of the discounted Price [to be closed on the product side]
		* */
//		if(enableDiscount){
//			priceDetail.setMyPartnerDiscount(displayPriceBreakDown.getTotalSaving());
//		}

		if(enableDiscount && StringUtils.equalsIgnoreCase(idContext, CORP_ID_CONTEXT)){
			priceDetail.setTotalDiscount(displayPriceBreakDown.getTotalSaving());
		}

		if (metaInfo) {
			Map<String, String> metaMap = new HashMap<String, String>();
			metaMap.put(SERVICE_CHARGE_KEY, String.valueOf(displayPriceBreakDown.getHotelServiceCharge()));
			metaMap.put(HOTEL_TAX_KEY, String.valueOf(displayPriceBreakDown.getHotelTax()));
			metaMap.put(SERVICE_FEES_KEY, String.valueOf(displayPriceBreakDown.getMmtServiceCharge()));
			metaMap.put(AFFILIATE_FEES_KEY, String.valueOf(displayPriceBreakDown.getAffiliateFee()));
			metaMap.put(WALLET_KEY, String.valueOf(displayPriceBreakDown.getWallet()));
			metaMap.put(MMT_DISCOUNT_KEY, String.valueOf(displayPriceBreakDown.getMmtDiscount()));
			metaMap.put(BLACK_DISCOUNT_KEY, String.valueOf(displayPriceBreakDown.getBlackDiscount()));
			metaMap.put(CDF_DISCOUNT_KEY, String.valueOf(displayPriceBreakDown.getCdfDiscount()));
			if (displayPriceBreakDown.getOfferDiscountBreakup() != null) {
				List<Map.Entry<PromotionalOfferType, Double>> list = displayPriceBreakDown.getOfferDiscountBreakup().entrySet()
						.stream()
						.filter(entry -> entry.getValue() != null && entry.getValue() > 0.0d)
						.collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(list)) {
					for (Map.Entry<PromotionalOfferType, Double> entry : list) {
						if (entry.getKey() != PromotionalOfferType.MIXED) {
							PricingDetails pd = new PricingDetails();
							metaMap.put(entry.getKey().getName(), String.valueOf(entry.getValue()));
						}
					}
				}
			}
			metaMap.put(LOWEST_RATE_SEGMENT_KEY, lowestRateSegmentId);
			priceDetail.setMetaInfo(metaMap);
		}

		return priceDetail;
	}

	private EMIDetail buildEmiDetails(Emi emiInfo){
		if (emiInfo == null) {
			return null;
		}
		EMIDetail emiDetail = new EMIDetail();
		emiDetail.setAmount((double) emiInfo.getEmiAmount());
		emiDetail.setType(emiInfo.getEmiType());
		emiDetail.setBankName(emiInfo.getBankName());
		emiDetail.setTenure(emiInfo.getTenure());
		emiDetail.setTotalCost(emiInfo.getTotalCost());
		emiDetail.setTotalInterest(emiInfo.getTotalInterest());
		return emiDetail;
	}

	private Coupon buildCoupon(BestCoupon couponInfo) {
		if (couponInfo == null) {
			return null;
		}
		Coupon coupon = new Coupon();
		coupon.setDescription(couponInfo.getDescription());
		coupon.setBankOffer(couponInfo.isBankOffer());
		coupon.setCode(couponInfo.getCouponCode());
		coupon.setType(couponInfo.getType());
		coupon.setCouponAmount(couponInfo.getDiscountAmount());
		coupon.setSpecialPromo(couponInfo.isSpecialPromoCoupon());
		coupon.setPromoIcon(StringUtils.isNotEmpty(couponInfo.getPromoIconLink()) ? couponInfo.getPromoIconLink() : genericBankIcon);
		return coupon;
	}

	private SortCriteria buildSortCriteria(com.mmt.hotels.model.request.SortCriteria criteria) {
		if (criteria == null) {
			return null;
		}
		SortCriteria sortCriteria = new SortCriteria(criteria.getField(), criteria.getOrder());
		return sortCriteria;
	}

	private ToolTip buildMyBizAssuredToolTip(){
		ToolTip toolTip = new ToolTip();
		toolTip.setHeading(polyglotService.getTranslatedData(ConstantsTranslation.WHY_MYBIZ_ASSURED_TEXT));
		toolTip.setIconType(myBizToolTipIconType);
		toolTip.setData(new ArrayList<>());
		toolTip.getData().add(polyglotService.getTranslatedData(ConstantsTranslation.RATED_HIGH_BT));
		toolTip.getData().add(polyglotService.getTranslatedData(ConstantsTranslation.GST_INVOICE_ASSURANCE_TEXT));
		toolTip.getData().add(polyglotService.getTranslatedData(ConstantsTranslation.BPG_TEXT));
		return toolTip;
	}

	private List<SectionFeature> getMybizSimilarHotelsFeatures(){
		List<SectionFeature> sectionFeatureList = new ArrayList<>();
		sectionFeatureList.add(new SectionFeature(highRatedUrl,polyglotService.getTranslatedData(ConstantsTranslation.RATED_HIGH_BT), null, null, null));
		sectionFeatureList.add(new SectionFeature(gstInvoiceUrl,polyglotService.getTranslatedData(ConstantsTranslation.GST_INVOICE_ASSURANCE_TEXT), null, null, null));
		sectionFeatureList.add(new SectionFeature(bpgUrl,polyglotService.getTranslatedData(ConstantsTranslation.BPG_TEXT), null, null, null));
		return sectionFeatureList;
	}

	private SoldOutInfoCG buildSoldOutInfo(SoldOutInfo soldOutInfo){
		if (soldOutInfo == null)
			return null;
		SoldOutInfoCG soldOutInfoCG = new SoldOutInfoCG();
		soldOutInfoCG.setSoldOutText(soldOutInfo.getSoldOutText());
		soldOutInfoCG.setSoldOutSubText(soldOutInfo.getSoldOutSubText());
		soldOutInfoCG.setSoldOutReason(soldOutInfo.getSoldOutReason());
		soldOutInfoCG.setSoldOutType(soldOutInfo.getSoldOutType());
		return soldOutInfoCG;
	}
	public abstract void addLocationPersuasionToHotelPersuasions(Hotel hotel, List<String> locationPersuasion, LinkedHashSet<String> facilities, ListingSearchRequest searchHotelsRequest, boolean enableAmenities, boolean sectionMyBizSimilarToDirectHtl, String dayUsePersuasionsText, TransportPoi nearestGroundTransportPoi, String drivingTimeText, LocusData locusData, boolean homestayV2Flow);
	public abstract void addPersuasionHoverData(Hotel hotel, SearchWrapperHotelEntity hotelEntity, CancellationTimeline cancellationTimeline, DisplayFare displayFare);

	public abstract void populateClientSpecificParameters();

	public abstract void addSeoTextPersuasion(Hotel hotel, SearchWrapperHotelEntity hotelEntity, boolean odd, ListingSearchRequest searchHotelsRequest, String sectionName);

	public abstract HotelCard buildQuickBookCard(QuickBookInfo quickBookInfo);

	public abstract String getMyBizDirectHotelDistanceText(String distanceText);

	public abstract MyBizStaticCard buildStaticCard(String section, List<SearchWrapperHotelEntity> hotels);

	public abstract void addSpecialFarePersuasion(SearchWrapperHotelEntity hotelEntity);

	public abstract void addBookingConfirmationPersuasion(SearchWrapperHotelEntity hotelEntity);

	protected String getSeoPersuasionText(boolean oddHotel, boolean viewOnMap, SearchWrapperHotelEntity hotelEntity, boolean seoDS, String seoCohort) {
		boolean starRating = hotelEntity.getStarRating() != null && hotelEntity.getStarRating() != 0;
		boolean addressLine1 = hotelEntity.getAddress() != null && StringUtils.isNotBlank(hotelEntity.getAddress().getLine1());
		boolean addressLine2 = hotelEntity.getAddress() != null && StringUtils.isNotBlank(hotelEntity.getAddress().getLine2());
		boolean cityName = StringUtils.isNotBlank(hotelEntity.getCityName());
		boolean propertyType = StringUtils.isNotBlank(hotelEntity.getPropertyType());
		boolean userRating = isUserRatingPresent(hotelEntity);
		boolean hotelSummary = isHotelSummaryPresent(hotelEntity);
		boolean mobiusInclusion = CollectionUtils.isNotEmpty(hotelEntity.getMobiusInclusions());
		boolean sanitisedAmenityName = CollectionUtils.isNotEmpty(hotelEntity.getFacilityHighlights());
		float userRatingValue = 0.0f;
		if (userRating) {
			if ("IN".equalsIgnoreCase(hotelEntity.getCountryCode())) {
				userRatingValue = hotelEntity.getFlyfishReviewSummary().get(OTA.MMT).get("cumulativeRating").floatValue();
			} else {
				userRatingValue = hotelEntity.getFlyfishReviewSummary().get(OTA.TA).get("cumulativeRating").floatValue();
			}
		}
		String hotelSummaryValue = "";
		if (hotelSummary) {
			hotelSummaryValue = fetchHotelSummary(hotelEntity);
		}
		String sanitisedAmenitiesValue = "";
		if (sanitisedAmenityName) {
			sanitisedAmenitiesValue = fetchSanitisedAmenities(hotelEntity);
		}

		String persuasionText;
		if (seoDS && StringUtils.isNotEmpty(hotelEntity.getShortDescSeo())) {
			String cohortReviewSummary = buildCohortReviewSummary(hotelEntity);
			String shortDescSeo = buildShortDescSeo(hotelEntity.getShortDescSeo(), viewOnMap);

			StringBuilder persuasionTextSB = new StringBuilder();
			persuasionTextSB.append(cohortReviewSummary).append(shortDescSeo);
			persuasionText = persuasionTextSB.toString();
		}
		else {
			if (oddHotel) {
				persuasionText = buildOddHotelSeoText(hotelEntity, starRating, addressLine1, addressLine2, cityName, propertyType, userRating, hotelSummary, mobiusInclusion, sanitisedAmenityName, userRatingValue, hotelSummaryValue, sanitisedAmenitiesValue, viewOnMap);
			} else {
				persuasionText = buildEvenHotelSeoText(hotelEntity, starRating, addressLine1, addressLine2, cityName, propertyType, hotelSummary, mobiusInclusion, sanitisedAmenityName, userRatingValue, hotelSummaryValue, sanitisedAmenitiesValue, viewOnMap);
			}
		}
		return persuasionText;
	}

	private String buildCohortReviewSummary(SearchWrapperHotelEntity hotelEntity) {
		String summary = StringUtils.EMPTY;
		if (StringUtils.isNotEmpty(hotelEntity.getCohortReviewSummary())) {
			String cohortReviewSummary = hotelEntity.getCohortReviewSummary();
			StringBuilder cohortReviewSummarySB = new StringBuilder();
			summary = cohortReviewSummarySB.append("<span>").append(cohortReviewSummary).append("</span>").toString();
		}
		return summary;
	}

	private String buildShortDescSeo(String shortDescSeo, boolean viewOnMap) {
		StringBuilder summary = new StringBuilder();
		summary.append("<span>").append(shortDescSeo);

		if (viewOnMap) {
			summary.append("<span class=\"blueText\"><b>View On Map</b></span>");
		}

		summary.append("</span>");
		return summary.toString();
	}

	private String buildEvenHotelSeoText(SearchWrapperHotelEntity hotelEntity, boolean starRating, boolean addressLine1, boolean addressLine2, boolean cityName, boolean propertyType, boolean hotelSummary, boolean mobiusInclusion, boolean sanitisedAmenityName, float userRatingValue, String hotelSummaryValue, String sanitisedAmenitiesValue, boolean viewOnMap) {
		StringBuilder persuasionText = new StringBuilder();
		if (addressLine1 && addressLine2 && propertyType) {
			if (viewOnMap) {
				persuasionText.append(String.format(
						"<span>Location of the %s is %s <span class=\"blueText\"><b>View On Map</b></span> (%s). </span>",
						hotelEntity.getPropertyType(), hotelEntity.getAddress().getLine2(),
						hotelEntity.getAddress().getLine1()));
			} else {
				persuasionText.append(String.format(
						"<span>Location of the %s is %s (%s). </span>",
						hotelEntity.getPropertyType(), hotelEntity.getAddress().getLine2(),
						hotelEntity.getAddress().getLine1()));
			}
		}
		if (starRating && addressLine2 && cityName && propertyType && hotelSummary) {
			persuasionText.append(String.format("<span>It’s a %s Star %s with overall Rating %.1f Out of 5 where %s. </span>",
					hotelEntity.getStarRating(), hotelEntity.getPropertyType(), userRatingValue,
					hotelSummaryValue));
		}
		if (sanitisedAmenityName && propertyType) {
			persuasionText.append(String.format("<span>Top Facilities of this %s are %s. </span>",
					hotelEntity.getPropertyType(), sanitisedAmenitiesValue));
		}
		if (mobiusInclusion) {
			persuasionText.append(String.format("<span>%s. </span>", hotelEntity.getMobiusInclusions()));
		}
		return persuasionText.toString();
	}

	private String buildOddHotelSeoText(SearchWrapperHotelEntity hotelEntity, boolean starRating, boolean addressLine1, boolean addressLine2, boolean cityName, boolean propertyType, boolean userRating, boolean hotelSummary, boolean mobiusInclusion, boolean sanitisedAmenityName, float userRatingValue, String hotelSummaryValue, String sanitisedAmenitiesValue, boolean viewOnMap) {
		StringBuilder persuasionText = new StringBuilder();
		if (starRating && addressLine2 && cityName && propertyType) {
			persuasionText.append(String.format("<span>This %d Star %s in %s is located in %s. </span>",
					hotelEntity.getStarRating(), hotelEntity.getPropertyType(),
					hotelEntity.getCityName(), hotelEntity.getAddress().getLine2()));
		}
		if (addressLine1) {
			if (viewOnMap) {
				persuasionText.append(String.format(
						"<span>Full Address of property is %s <span class=\"blueText\"><b>View On Map</b></span> </span>",
						hotelEntity.getAddress().getLine1()));
			} else {
				persuasionText.append(String.format(
						"<span>Location of the %s is %s (%s). </span>",
						hotelEntity.getPropertyType(), hotelEntity.getAddress().getLine2(),
						hotelEntity.getAddress().getLine1()));
			}
		}
		if (propertyType && userRating && hotelSummary) {
			persuasionText.append(String.format("<span>This %s have %.1f Out of 5 Rating where %s. </span>",
					hotelEntity.getPropertyType(), userRatingValue, hotelSummaryValue));
		}
		if (mobiusInclusion) {
			persuasionText.append(String.format("<span>%s. </span>", hotelEntity.getMobiusInclusions()));
		}
		if (sanitisedAmenityName) {
			persuasionText.append(String.format("<span>Key amenities of this property are %s. </span>", sanitisedAmenitiesValue));
		}
		return persuasionText.toString();
	}

	protected String fetchSanitisedAmenities(SearchWrapperHotelEntity hotelEntity) {
		int maxLength = Math.min(hotelEntity.getFacilityHighlights().size(), 3);
		int len = 0;
		StringBuilder sanitizedAminitiesText = new StringBuilder();
		for (String facilityHighlight : hotelEntity.getFacilityHighlights()) {
			if (len == maxLength) {
				break;
			}
			sanitizedAminitiesText.append(facilityHighlight);
			if (len != maxLength - 1) {
				sanitizedAminitiesText.append(" & ");
			}
			len++;
		}
		return sanitizedAminitiesText.toString();
	}

	protected String fetchHotelSummary(SearchWrapperHotelEntity hotelEntity) {
		JsonNode travellerRatingSummary = null;
		if ("IN".equalsIgnoreCase(hotelEntity.getCountryCode())) {
			travellerRatingSummary = hotelEntity.getFlyfishReviewSummary().get(OTA.MMT).get("travellerRatingSummary");
		} else {
			travellerRatingSummary = hotelEntity.getFlyfishReviewSummary().get(OTA.TA).get("travellerRatingSummary");
		}
		TravellerRatingSummaryDTO summaryDTO = objectMapperUtil.getObjectFromJsonNode(travellerRatingSummary,
				new TypeReference<TravellerRatingSummaryDTO>() {
				});
		if (summaryDTO == null || CollectionUtils.isEmpty(summaryDTO.getHotelSummary())) {
			return "";
		}
		StringBuilder hotelSummaryText = new StringBuilder();
		int maxLength = Math.min(summaryDTO.getHotelSummary().size(), 3);
		int len = 0;
		for (ConceptSummaryDTO conceptSummaryDTO : summaryDTO.getHotelSummary()) {
			if (len == maxLength) {
				break;
			}
			hotelSummaryText.append(
					String.format("<span> %.1f/5 for %s", conceptSummaryDTO.getValue(), conceptSummaryDTO.getConcept()));
			if (len != maxLength - 1) {
				hotelSummaryText.append(" &");
			}
			hotelSummaryText.append(" </span>");
			len++;
		}
		return hotelSummaryText.toString();
	}

	protected boolean isHotelSummaryPresent(SearchWrapperHotelEntity hotelEntity) {
		if (hotelEntity.getFlyfishReviewSummary() == null) {
			return false;
		}
		if ("IN".equalsIgnoreCase(hotelEntity.getCountryCode()) &&
				(hotelEntity.getFlyfishReviewSummary().get(OTA.MMT) == null ||
						hotelEntity.getFlyfishReviewSummary().get(OTA.MMT).get("travellerRatingSummary") == null ||
						hotelEntity.getFlyfishReviewSummary().get(OTA.MMT).get("travellerRatingSummary")
								.get("hotelSummary") == null)) {
			return false;
		}
		if (!"IN".equalsIgnoreCase(hotelEntity.getCountryCode()) &&
				(hotelEntity.getFlyfishReviewSummary().get(OTA.TA) == null ||
						hotelEntity.getFlyfishReviewSummary().get(OTA.TA).get("travellerRatingSummary") == null ||
						hotelEntity.getFlyfishReviewSummary().get(OTA.TA).get("travellerRatingSummary").get("hotelSummary") == null)) {
			return false;
		}
		return true;
	}

	protected boolean isUserRatingPresent(SearchWrapperHotelEntity hotelEntity) {
		if (hotelEntity.getFlyfishReviewSummary() == null) {
			return false;
		}
		if ("IN".equalsIgnoreCase(hotelEntity.getCountryCode()) &&
				(hotelEntity.getFlyfishReviewSummary().get(OTA.MMT) == null ||
						hotelEntity.getFlyfishReviewSummary().get(OTA.MMT).get("cumulativeRating") == null ||
						Float.compare(hotelEntity.getFlyfishReviewSummary().get(OTA.MMT).get("cumulativeRating").floatValue(),
								0.0f) == 0)) {
			return false;
		}
		if (!"IN".equalsIgnoreCase(hotelEntity.getCountryCode()) &&
				(hotelEntity.getFlyfishReviewSummary().get(OTA.TA) == null ||
						hotelEntity.getFlyfishReviewSummary().get(OTA.TA).get("cumulativeRating") == null ||
						Float.compare(hotelEntity.getFlyfishReviewSummary().get(OTA.TA).get("cumulativeRating").floatValue(),
								0.0f) == 0)) {
			return false;
		}
		return true;
	}

	private CalendarCriteria buildCalendarCriteria(com.mmt.hotels.pojo.CalendarAvailability.CalendarCriteria calendarCriteriaHES) {
		if(calendarCriteriaHES == null)
			return null;
		CalendarCriteria calendarCriteriaCG = new CalendarCriteria();
		calendarCriteriaCG.setAdvanceDays(calendarCriteriaHES.getAdvanceDays());
		calendarCriteriaCG.setAvailable(calendarCriteriaHES.isAvailable());
		calendarCriteriaCG.setMaxDate(calendarCriteriaHES.getMaxDate());
		calendarCriteriaCG.setMlos(calendarCriteriaHES.getMlos());
		return calendarCriteriaCG;
	}

	/**
<<<<<<< HEAD
	 * groupPrice node having (priceLabel,savingText,priceDisplayMsg,taxMsg & price) will be added under priceDetail node
	 * if GRPN:T(When PDO:PRN is sent and new format required on listing) exp is send by client and is a group booking funnel
	 *
	 *
	 * @param searchHotelsRequest search Hotels request received from client
	 * @param hotelEntity         Details of each Hotel on listing page
	 * @param groupPrice          New groupPrice node added for groupBooking new design
	 */
	private void groupPriceForGRPN(ListingSearchRequest searchHotelsRequest, SearchWrapperHotelEntity hotelEntity, GroupPrice groupPrice) {
		long totalPrice = (long) ((hotelEntity.getDisplayFare() != null && hotelEntity.getDisplayFare().getDisplayPriceBreakDown() != null) ? hotelEntity.getDisplayFare().getDisplayPriceBreakDown().getTotalAmount() : 0.0d);
		String savingPercText = getSavingPercTextForGroupBooking(searchHotelsRequest, hotelEntity);
		String priceDisplayMessage= createPriceDisplayMsgAsPerRoomsNights(searchHotelsRequest, hotelEntity);
		groupPrice.setPriceLabel(polyglotService.getTranslatedData(ConstantsTranslation.GROUP_PRICE_LABEL));
		groupPrice.setSavingsText(savingPercText);
		groupPrice.setPriceDisplayMsg(priceDisplayMessage);
		groupPrice.setTaxMsg(polyglotService.getTranslatedData(ConstantsTranslation.TAX_MSG));
		//totalPrice will be including taxes
		groupPrice.setPrice(totalPrice);
	}

	/**
	 *
	 * Text like 2 nights, 6 rooms will be created on the basis of roomCount and Night count
	 * calculated from checkin & checkout
	 *
	 * @param searchHotelsRequest search Hotels request received from client
	 * @param hotelEntity         Details of each Hotel on listing page
	 */
	private String createPriceDisplayMsgAsPerRoomsNights(ListingSearchRequest searchHotelsRequest, SearchWrapperHotelEntity hotelEntity) {
		int nightCount = 0;
		int roomCount = hotelEntity.getRoomCount();
		String checkIn = null;
		String checkOut = null;
		if (searchHotelsRequest != null && searchHotelsRequest.getSearchCriteria() != null) {
			checkOut = searchHotelsRequest.getSearchCriteria().getCheckOut();
			checkIn = searchHotelsRequest.getSearchCriteria().getCheckIn();
		}
		if (StringUtils.isNotBlank(checkIn) && StringUtils.isNotBlank(checkOut)) {
			nightCount = utility.getLOS(checkIn,checkOut);
		}
		String groupPriceDisplayText=StringUtils.EMPTY;
		if(hotelEntity.isAltAcco() && searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null &&
				FUNNEL_SOURCE_GROUP_BOOKING.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource())){
			groupPriceDisplayText = commonResponseTransformer.getGroupPriceTextForGRPNTForAltAcco(nightCount);
		}else {
			groupPriceDisplayText = commonResponseTransformer.getGroupPriceTextForGRPNT(roomCount, nightCount);
		}
		return groupPriceDisplayText;
	}

	/**
	 *
	 * Saving percentage text will be created from DisplayFare->DisplayPriceBreakDown->SavingPerc node
	 *
	 * @param searchHotelsRequest search Hotels request received from client
	 * @param hotelEntity         Details of each Hotel on listing page
	 */
	private String getSavingPercTextForGroupBooking(ListingSearchRequest searchHotelsRequest, SearchWrapperHotelEntity hotelEntity) {
		long savingPerc = (long) ((hotelEntity.getDisplayFare() != null && hotelEntity.getDisplayFare().getDisplayPriceBreakDown() != null) ? hotelEntity.getDisplayFare().getDisplayPriceBreakDown().getSavingPerc() : 0.0d);
		String savingPercText = null;
		if (savingPerc != 0.0 && hotelEntity.isGroupBookingPrice()) {
			savingPercText = polyglotService.getTranslatedData(Utility.getGroupPriceTextOrSavingPercKey(SAVING_PERC_TEXT, searchHotelsRequest != null ? searchHotelsRequest.getClient() : "")).replace(SAVING_PERC, String.valueOf(savingPerc));
		}
		return savingPercText;
	}
	/**
	 * HTL-40907: Add booking confirmation persuasion to hotel's persuasion map for lowest negotiated rate plan hotels for mobile.
	 *
	 * @param hotelEntity
	 */
	protected void addBookingConfirmationPersuasionForMobile(SearchWrapperHotelEntity hotelEntity) {
		PersuasionObject specialFarePersuasion = new PersuasionObject();
		PersuasionData persuasionData = new PersuasionData();
		Map<String, PersuasionData> persuasionConfigMap = specialFarePersuasionConfigMap.get(Constants.CLIENT_APPS);
		PersuasionData persuasionStyleConfig = MapUtils.isNotEmpty(persuasionConfigMap) ? persuasionConfigMap.get(Persuasions.SPECIAL_FARE_PERSUASION.getPlaceholderIdApps()) : new PersuasionData();

		persuasionData.setPersuasionType(DISCOUNTS);

		String text = polyglotService.getTranslatedData(ConstantsTranslation.BOOKING_CONFIRMATION_TEXT_LISTING_MOBILE);
		text = StringUtils.replace(text, NO_OF_HOURS_PLACEHOLDER, String.valueOf(noOfHoursForConfirmation));
		persuasionData.setText(text);

		persuasionData.setHasAction(false);

		Button button = new Button();
		button.setActionType(BOTTOMSHEET);
		ButtonInfo buttonInfo = new ButtonInfo();
		String header = polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_FARE_TAG_MOBILE);
		hotelEntity.setCorpAlias(hotelEntity.getCorpAlias() != null ? hotelEntity.getCorpAlias() : SPECIAL);
        header = StringUtils.replace(header, "{CORP_ALIAS}", hotelEntity.getCorpAlias());
		buttonInfo.setHeader(header);
		String buttonInfoText = polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_FARE_TEXT_MOBILE);
		buttonInfoText = StringUtils.replace(buttonInfoText, NO_OF_HOURS_PLACEHOLDER, String.valueOf(noOfHoursForConfirmation));
		buttonInfo.setText(buttonInfoText);
		button.setInfo(buttonInfo);
		persuasionData.setButton(button);

		PersuasionStyle style = new PersuasionStyle();
		BeanUtils.copyProperties(persuasionStyleConfig.getStyle() != null ? persuasionStyleConfig.getStyle() : new PersuasionStyle(), style);
		persuasionData.setStyle(style);

		persuasionData.setHtml(true);
		persuasionData.setIconurl(persuasionStyleConfig.getIconurl());

		specialFarePersuasion.setData(Collections.singletonList(persuasionData));
		specialFarePersuasion.setPlaceholder(Persuasions.SPECIAL_FARE_PERSUASION.getPlaceholderIdApps());
		specialFarePersuasion.setTemplate(DEAL_BOX_IMAGE_TEXT_TEMPLATE);
		specialFarePersuasion.setTemplateType(DEFAULT);

		PersuasionStyle topLevelStyle = new PersuasionStyle();
		BeanUtils.copyProperties(persuasionStyleConfig.getTopLevelStyle() != null ? persuasionStyleConfig.getTopLevelStyle() : new PersuasionStyle(), topLevelStyle);
		specialFarePersuasion.setStyle(topLevelStyle);

		if (hotelEntity.getHotelPersuasions() == null)
			hotelEntity.setHotelPersuasions(new HashMap<>());
		((Map<Object, Object>) hotelEntity.getHotelPersuasions()).put(Persuasions.SPECIAL_FARE_PERSUASION.getPlaceholderIdApps(), specialFarePersuasion);
	}

	/**
	 * Build company special fare tag persuasion for negotiated rates and it hotel's persuasions.
	 *
	 * @param hotelEntity
	 */
	protected void addSpecialFareTagPersuasionForMobile(SearchWrapperHotelEntity hotelEntity) {
		PersuasionObject specialFareTagPersuasion = new PersuasionObject();
		PersuasionData persuasionData = new PersuasionData();
		Map<String, PersuasionData> persuasionConfigMap = specialFarePersuasionConfigMap.get(Constants.CLIENT_APPS);
		PersuasionData persuasionStyleConfig = MapUtils.isNotEmpty(persuasionConfigMap) ? persuasionConfigMap.get(PLACEHOLDER_PRICE_BOTTOM_M) : new PersuasionData();

		persuasionData.setPersuasionType(CANCEL_TYPE);

		String text = polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_FARE_TAG_MOBILE);
        hotelEntity.setCorpAlias(hotelEntity.getCorpAlias() != null ? hotelEntity.getCorpAlias() : SPECIAL);
        text = StringUtils.replace(text, "{CORP_ALIAS}", hotelEntity.getCorpAlias());
		persuasionData.setText(text);

		PersuasionStyle style = new PersuasionStyle();
		BeanUtils.copyProperties(persuasionStyleConfig.getStyle() != null ? persuasionStyleConfig.getStyle() : new PersuasionStyle(), style);
		persuasionData.setStyle(style);

		persuasionData.setHasAction(false);
		persuasionData.setHtml(true);

		specialFareTagPersuasion.setData(Collections.singletonList(persuasionData));
		specialFareTagPersuasion.setPlaceholder(PLACEHOLDER_PRICE_BOTTOM_M);
		specialFareTagPersuasion.setTemplate(MULTI_PERSUASION_V_TEMPLATE);
		specialFareTagPersuasion.setTemplateType(DEFAULT);

		PersuasionStyle topLevelStyle = new PersuasionStyle();
		BeanUtils.copyProperties(persuasionStyleConfig.getTopLevelStyle() != null ? persuasionStyleConfig.getTopLevelStyle() : new PersuasionStyle(), topLevelStyle);
		specialFareTagPersuasion.setStyle(topLevelStyle);

		if (hotelEntity.getHotelPersuasions() == null)
			hotelEntity.setHotelPersuasions(new HashMap<>());
		((Map<Object, Object>) hotelEntity.getHotelPersuasions()).put(PLACEHOLDER_PRICE_BOTTOM_M, specialFareTagPersuasion);
	}

	private boolean checkIfExclusiveDeal(final PersonalizedResponse<SearchWrapperHotelEntity> personalizedResponse, final CommonModifierResponse commonModifierResponse) {
		return utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), MYPARTNER_EXCLUSIVE_DEAL)
				&& utility.isMyPartner(commonModifierResponse)
				&& RECOMMENDED_HOTELS_SECTION.equalsIgnoreCase(personalizedResponse.getSection())
				&& personalizedResponse.getHotels() != null && personalizedResponse.getHotels().stream()
				.filter(hotel -> hotel.getHotelPersuasions() != null)
				.flatMap(hotel -> ((Map<String, Map<String, Object>>) hotel.getHotelPersuasions()).entrySet().stream())
				.filter(persuasion -> persuasion.getKey().equalsIgnoreCase(PC_IMG_ANNOTATION_PLACEHOLDER)
						&& persuasion.getValue().get("data") != null
						&& !((List<Map<String, Object>>) persuasion.getValue().get("data")).isEmpty())
				.map(k -> ((List<Map<String, Object>>) k.getValue().get("data")).get(0).get("iconurl"))
				.anyMatch(iconUrl -> EXCLUSIVE_DEAL_IMAGE_URL.equalsIgnoreCase((String) iconUrl));
	}

	private boolean checkIfHorizontalSection(final PersonalizedResponse<SearchWrapperHotelEntity> personalizedResponse, final CommonModifierResponse commonModifierResponse) {
		if (utility.isMyPartner(commonModifierResponse)
				&& SIMILAR_HOTELS.equalsIgnoreCase(personalizedResponse.getSection())) {
			final String pokus = commonModifierResponse.getExpDataMap().get(MYPARTNER_HIGH_GRASP_HOTELS);
			return pokus != null && !MYPARTNER_HG_NOT_SHOW.equalsIgnoreCase(pokus);
		}
		return false;
	}

	private boolean checkIfHorizontalSection(String sectionName,int hotelSize, DeviceDetails deviceDetails) {

		String deviceType = deviceDetails!=null?deviceDetails.getBookingDevice():null;
		if(PERSONALISED_PICKS_HOTELS.equalsIgnoreCase(sectionName)) {

			return (Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(deviceType) && hotelSize >= minSimilarHotels) ||
					(DEVICE_OS_ANDROID.equalsIgnoreCase(deviceType) || Constants.DEVICE_OS_IOS.equalsIgnoreCase(deviceType));
		}
		return false;
	}

//	public String getBgColorFromSection(String sectionName) {
//
//		try {
//			Map<String, String> sectionToBgColorMap = gson.fromJson(sectionBgColorMap, new TypeToken<HashMap>() {
//			}.getType());
//			return sectionToBgColorMap.getOrDefault(sectionName,null);
//		} catch (Exception ex) {
//			LOGGER.error("Exception occured in building sectionToBgColorMap ", ex);
//		}
//
//		return null;
//	}
	protected void updateTopLevelHover(JSONObject topLevelHoverData, MpFareHoldStatus mpFareHoldStatus) {
		if (topLevelHoverData == null || mpFareHoldStatus == null) {
			return;
		}
		String tooltipType = topLevelHoverData.optString(TOOL_TIP_TYPE);
		if (MP_FARE_HOLD.equalsIgnoreCase(tooltipType)) {
			Hover hover = new Hover();
			Long expiry = mpFareHoldStatus.getExpiry();
			if (expiry != null) {
				hover.setTitleText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_TITLE),
						dateUtil.convertEpochToDateTime(expiry, dateUtil.DD_MMM_hh_mm_a)));
			} else {
				hover.setTitleText(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_GENERIC_TITLE));
			}
			hover.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_SUB_TITLE));

			topLevelHoverData.put(DATA, hover);
			topLevelHoverData.put(LOG_HOVER_KEY, LOG_HOVER_VALUE);
			topLevelHoverData.put(TOOL_TIP_TYPE, TITLE_SUBTITLE_TOOLTIP);
		}
	}

	public boolean isWalletSurgePersuasionEnabled(JSONObject persuasionData) {
		return persuasionData.has(PERSUASION_KEY)
				&& WALLET_SURGE_PERSUASION_KEY.equalsIgnoreCase(persuasionData.optString(PERSUASION_KEY))
				&& persuasionData.has(TIMER)
				&& persuasionData.getJSONObject(TIMER).has(EXPIRY)
				&& persuasionData.getJSONObject(TIMER).get(EXPIRY) instanceof Integer
				&& (Integer) persuasionData.getJSONObject(TIMER).get(EXPIRY) == 1;
	}

	public void updateHotelCardType(SearchHotelsRequest searchHotelsRequest, CommonModifierResponse commonModifierResponse, PersonalizedResponse perResponse, PersonalizedSection personalizedSection) {
		String experimentKeyValue = commonModifierResponse.getExpDataMap() != null
				? commonModifierResponse.getExpDataMap().get(ExperimentKeys.PUSH_INDEPENDENT_ON_DIRECT_SEARCH.getKey())
				: null;
		boolean isIndependentProperty = checkIndependentPropertyAttributeInSearchHotelsRequest(searchHotelsRequest);

		if (EXPERIMENT_KEY_VALUE_B.equals(experimentKeyValue) && DIRECT_HOTEL.equals(personalizedSection.getName())&& isIndependentProperty) {
			personalizedSection.setHotelCardType(COMPACT_V1_HOTEL_CARD_TYPE);
		} else if (EXPERIMENT_KEY_VALUE_C.equals(experimentKeyValue) && isIndependentProperty && SIMILAR_HOTELS.equals(personalizedSection.getName())) {
			personalizedSection.setHotelCardType(COMPACT_V2_HOTEL_CARD_TYPE);
			personalizedSection.setOrientation(HORIZONTAL);
			personalizedSection.setSectionBG(GRADIENT_END);
		} else if (EXPERIMENT_KEY_VALUE_C.equals(experimentKeyValue) && DIRECT_HOTEL.equals(personalizedSection.getName()) && isIndependentProperty) {
			personalizedSection.setHotelCardType(COMPACT_V1_HOTEL_CARD_TYPE);
		} else {
			if (StringUtils.isNotEmpty(perResponse.getHotelCardType())) {
				personalizedSection.setHotelCardType(perResponse.getHotelCardType().toLowerCase());
			}
		}
	}
	private List<String> fetchAttributeValuesFromSearchHotelsRequest(SearchHotelsRequest searchHotelsRequest) {
		List<String> attributeValues = new ArrayList<>();
		if (searchHotelsRequest != null && searchHotelsRequest.getMatchMakerDetails() != null && searchHotelsRequest.getMatchMakerDetails().getHotels() != null) {
			for (InputHotel inputHotel : searchHotelsRequest.getMatchMakerDetails().getHotels()) {
				attributeValues.add(inputHotel.getAttribute());
			}
		}
		return attributeValues;
	}

	private boolean checkIndependentPropertyAttributeInSearchHotelsRequest(SearchHotelsRequest searchHotelsRequest) {
		List<String> attributeValues = fetchAttributeValuesFromSearchHotelsRequest(searchHotelsRequest);
		return attributeValues.contains(ATTRIBUTE_INDEPENDENT_PROPERTY);
	}

}
