package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.rooms.LoginPersuasion;
import com.mmt.hotels.clientgateway.response.rooms.RecommendedCombo;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.HeroTierDetails;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.SPACE_X_SPACE;

@Component
public class OrchSearchRoomsResponseTransformerIOS extends OrchSearchRoomsResponseTransformer {

    @Override
    protected PersuasionObject createTopRatedPersuasion(boolean isNewDetailsPageDesktop) {
        // TODO: Add to Peitho
        return null;
    }

    @Override
    protected LoginPersuasion buildLoginPersuasion() {
        return null;
    }

    @Override
    public PersuasionResponse buildDelayedConfirmationPersuasion(String corpAlias, boolean isMyBizNewDetailsPage) {
        return null;
    }

    @Override
    public PersuasionResponse buildSpecialFareTagPersuasion(String corpAlias) {
        return null;
    }

    @Override
    public PersuasionResponse buildSpecialFareTagWithInfoPersuasion(String corpAlias, boolean isNewSelectRoomPage) {
        return buildSpecialFarePersuasionForMobile(corpAlias, isNewSelectRoomPage);
    }

    @Override
    public PersuasionResponse buildConfirmationTextPersuasion(String corpAlias, boolean isNewSelectRoomPage, 
                                                              boolean isMyBizNewDetailsPage) {
        return buildBookingConfirmationPersuasionForMobile(corpAlias, isNewSelectRoomPage);
    }

    @Override
    public String getHtml() {
        return Constants.APPS_INCLUSION_HTML;
    }

    /**
     * Builds special fare persuasion for mobile platforms
     */
    protected PersuasionResponse buildSpecialFarePersuasionForMobile(String corpAlias, boolean isNewSelectRoomPage) {
        // TODO: Implement mobile specific special fare persuasion
        return null;
    }

    /**
     * Builds booking confirmation persuasion for mobile platforms
     */
    protected PersuasionResponse buildBookingConfirmationPersuasionForMobile(String corpAlias, boolean isNewSelectRoomPage) {
        // TODO: Implement mobile specific booking confirmation persuasion
        return null;
    }

    protected void buildGroupBookingComboText(RoomDetails roomDetails, RecommendedCombo recommendedCombo,
                                              boolean baseCombo, OccupancyDetails occupancyDetails) {

        if (baseCombo && recommendedCombo != null && roomDetails.getBaseRoom() != null &&
                roomDetails.getBaseRoom() && occupancyDetails != null) {
            int roomCount = occupancyDetails.getNumberOfRooms();
            String comboDisplayText = roomCount + SPACE_X_SPACE + roomDetails.getRoomName();
            roomDetails.setDisplayName(comboDisplayText);
            recommendedCombo.setBaseComboText(comboDisplayText);
        }
    }
} 