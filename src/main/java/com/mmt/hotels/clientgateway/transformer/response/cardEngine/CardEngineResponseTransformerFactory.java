package com.mmt.hotels.clientgateway.transformer.response.cardEngine;

import com.mmt.hotels.orchestrator.enums.SubPageContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CardEngineResponseTransformerFactory {

    @Autowired
    CardEngineResponseTransformer cardEngineResponseTransformer;

    @Autowired
    ListingCollectionCardEngineResponseTransformer listingCollectionCardEngineResponseTransformer;

    public CardEngineResponseTransformer getResponseService(String subPageContext){
        SubPageContext subPageContextEnum = SubPageContext.resolve(subPageContext);
        switch (subPageContextEnum) {
            case LISTING_COLLECTION:
                return listingCollectionCardEngineResponseTransformer;
            default:
                return cardEngineResponseTransformer;
        }
    }
}
