package com.mmt.hotels.clientgateway.transformer.response.pwa;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.PersuasionType;
import com.mmt.hotels.clientgateway.enums.Persuasions;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.TemplateType;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.response.Hover;
import com.mmt.hotels.clientgateway.response.searchHotels.*;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.transformer.response.SearchHotelsResponseTransformer;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.MpFareHoldStatus;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.DisplayFare;
import com.mmt.hotels.model.response.searchwrapper.PersonalizedResponse;
import com.mmt.hotels.model.response.searchwrapper.QuickBookInfo;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.propertymanager.config.PropertyManager;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.model.LocusData;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.text.MessageFormat;
import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

@Component
public class SearchHotelsResponseTransformerPWA extends SearchHotelsResponseTransformer {

	private static final Logger LOGGER = LoggerFactory.getLogger(SearchHotelsResponseTransformerPWA.class);

	@Override
	public void populateClientSpecificParameters() {
	}

	@Value("${consul.enable}")
	private boolean consulFlag;

	@Autowired
	CommonConfigConsul commonConfigConsul;

	@Value("${mybiz.bottom.sheet.image}")
	private String myBizBottomSheetImage;

	@Value("${high.rated.url}")
	private String highRatedUrl;

	@Value("${gst.invoice.url}")
	private String gstInvoiceUrl;

	@Value("${bpg.url}")
	private String bpgUrl;

	@Autowired
	PolyglotService polyglotService;

	@Autowired
	PropertyManager propManager;

	@Autowired
	DateUtil dateUtil;

	private MyBizStaticCard myBizStaticCard;

	@Autowired
	private CommonHelper commonHelper;

	@Autowired
	PersuasionUtil persuasionUtil;

	private static final String DEVICE_TYPE = "PWA";

	@PostConstruct
	public void init() {
		if(consulFlag){
			myBizStaticCard = commonConfigConsul.getMyBizStaticCard();
			missingSlotDetails = commonConfigConsul.getMissingSlotDetails();
			LOGGER.warn("missingSlotDetails : {}" , missingSlotDetails);
			thresholdForSlashedAndDefaultHourPrice = commonConfigConsul.getThresholdForSlashedAndDefaultHourPrice();
			LOGGER.debug("Fetching values from commonConfig consul");
		}
		else{
			CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
			myBizStaticCard = commonConfig.myBizStaticCard();
			missingSlotDetails = commonConfig.missingSlotDetails();
			commonConfig.addPropertyChangeListener("myBizStaticCard", event -> myBizStaticCard = commonConfig.myBizStaticCard());
			commonConfig.addPropertyChangeListener("missingSlotDetails", event -> missingSlotDetails = commonConfig.missingSlotDetails());
			LOGGER.warn("missingSlotDetails : {}" , missingSlotDetails);
			thresholdForSlashedAndDefaultHourPrice = commonConfig.thresholdForSlashedAndDefaultHourPrice();
		}
		specialFarePersuasionConfigMap = new Gson().fromJson(specialFarePersuasionConfig, new TypeToken<Map<String, Map<String, PersuasionData>>>() {
		}.getType());
	}

	@Override
	public void addSeoTextPersuasion(Hotel hotel, SearchWrapperHotelEntity hotelEntity, boolean odd, ListingSearchRequest searchHotelsRequest, String sectionName) {
		if (Utility.isSeoPersuasionAllowed(searchHotelsRequest, sectionName)) return;

		if (hotel.getHotelPersuasions() == null) {
			hotel.setHotelPersuasions(new HashMap<String, Object>());
		}
		PersuasionObject seoPersuasion = new PersuasionObject();
		seoPersuasion.setData(new ArrayList<>());
		seoPersuasion.setPlaceholder(Constants.PLACEHOLDER_CARD_SEO);
		seoPersuasion.setPlaceholder("SINGLE");
		seoPersuasion.setTemplate("SEO_PERSUASION");
		PersuasionData seoPersuasionData = new PersuasionData();
		seoPersuasionData.setHtml(true);
		seoPersuasionData.setPersuasionType(Constants.PERSUASION_TYPE_SEO);
		boolean isSeoDs = searchHotelsRequest.getFeatureFlags() !=null ? searchHotelsRequest.getFeatureFlags().isSeoDS():false;
		String seoCohort = searchHotelsRequest.getFeatureFlags() !=null ? searchHotelsRequest.getFeatureFlags().getSeoCohort() : null;
		seoPersuasionData.setText(getSeoPersuasionText(odd, false, hotelEntity, isSeoDs, seoCohort));
		seoPersuasion.getData().add(seoPersuasionData);
		try {
			((Map<Object, Object>) hotel.getHotelPersuasions()).put(Constants.PLACEHOLDER_CARD_SEO, seoPersuasion);
		} catch (Exception e) {
			LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ", e.getMessage());
		}
	}

	@Override
	public HotelCard buildQuickBookCard(QuickBookInfo quickBookInfo) {
		return null;
	}

	@Override
	public String getMyBizDirectHotelDistanceText(String distanceText) {
		return org.apache.commons.lang3.StringUtils.replace(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_DIRECT_HOTEL_APPS_DISTANCE_TEXT),"{DISTANCE_TEXT}", distanceText);
	}

	@Override
	public MyBizStaticCard buildStaticCard(String section, List<SearchWrapperHotelEntity> hotels) {
		MyBizStaticCard staticCard = null;
		if(CORPBUDGET_DIRECT_HOTEL.equalsIgnoreCase(section) && CollectionUtils.isNotEmpty(hotels) && !hotels.get(0).isCorpBudgetHotel() &&
				myBizStaticCard != null) {
			staticCard = SerializationUtils.clone(myBizStaticCard);
			staticCard.setActionUrl(hotels.get(0).getDetailDeeplinkUrl());
			translateStaticCard(staticCard);
		}
		return staticCard;
	}

	protected void translateStaticCard(MyBizStaticCard staticCard) {
		staticCard.setText(polyglotService.getTranslatedData(staticCard.getText()));
		staticCard.setSubtext(polyglotService.getTranslatedData(staticCard.getSubtext()));
		staticCard.setCtaText(polyglotService.getTranslatedData(staticCard.getCtaText()));
	}

	@Override
	public void addLocationPersuasionToHotelPersuasions(Hotel hotel, List<String> locationPersuasion, LinkedHashSet<String> facilities, ListingSearchRequest searchHotelsRequest, boolean enableAmenities, boolean sectionMyBizSimilarToDirectHtl, String dayUsePersuasionsText, TransportPoi nearestGroundTransportPoi, String drivingTimeText, LocusData locusData, boolean homestayV2Flow){
		if(CollectionUtils.isNotEmpty(locationPersuasion)) {
			if (hotel.getHotelPersuasions() == null)
				hotel.setHotelPersuasions(new HashMap<String,Object>());
			PersuasionObject locPers = new PersuasionObject();
			locPers.setData(new ArrayList<>());
			locPers.setTemplate(IMAGE_TEXT_H);
			locPers.setPlaceholder("SINGLE");

			int index = 1;
			PersuasionData locPersuasionData = new PersuasionData();
			locPersuasionData.setHasAction(false);
			locPersuasionData.setHtml(true);
			locPersuasionData.setId("LOC_PERSUASION_" + index++);
			locPersuasionData.setPersuasionType("LOCATION");
			PersuasionStyle style = new PersuasionStyle();
			//[HTL-46707] Changed the max lines to 3 for IH hotels only
			if (hotel.getLocationDetail() != null && !Constants.DOM_COUNTRY.equalsIgnoreCase(hotel.getLocationDetail().getCountryId())) {
				style.setMaxLines(3);
			} else {
				style.setMaxLines(2);
			}
			locPersuasionData.setStyle(style);

			locPers.getData().add(locPersuasionData);
			if(locationPersuasion.size() == 1 ) {
				locPersuasionData.setText(locationPersuasion.get(0));
			}else if(StringUtils.isEmpty(locationPersuasion.get(0))){
				locPersuasionData.setText(locationPersuasion.get(1));
			}
			else if (locationPersuasion.size() >= 2){
				locPersuasionData.setText(locationPersuasion.get(0) + " | " + locationPersuasion.get(1));
				//For Secondary Location Persuasion, if it is present, add it in the Location Persuasion
				if (locationPersuasion.size() > 2)
					locPersuasionData.setText(locPersuasionData.getText() + " | " + locationPersuasion.get(2));
			}

			try {
				((Map<Object,Object>) hotel.getHotelPersuasions()).put(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP,locPers);
			} catch (ClassCastException e) {
				LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
			} catch (Exception e) {
				LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
			}

		}

		if(CollectionUtils.isNotEmpty(facilities) && enableAmenities) {
			if (hotel.getHotelPersuasions() == null)
				hotel.setHotelPersuasions(new HashMap<String,Object>());
			PersuasionObject amenityPers = new PersuasionObject();
			amenityPers.setData(new ArrayList<>());
			amenityPers.setTemplate(IMAGE_TEXT_H);
			amenityPers.setPlaceholder("SINGLE");


			PersuasionData amenPersuasionData = new PersuasionData();
			amenPersuasionData.setHasAction(false);
			amenPersuasionData.setHtml(false);
			amenPersuasionData.setId("AMENITIES");
			amenPersuasionData.setPersuasionType("AMENITIES");
			amenPersuasionData.setStyle(new PersuasionStyle());
			amenPersuasionData.getStyle().setTextColor("#000000");
			StringBuilder text = new StringBuilder();
			int index = 1;
			int facilitiesSize = facilities.size();
			Iterator<String> iter = facilities.iterator();
			while(iter.hasNext() && index <=3){

				text.append( iter.next());
				if(index < 3 && facilitiesSize > 1)
					text.append(" | ");
				index++;
				 --facilitiesSize;
			}
			amenPersuasionData.setText(text.toString());
			amenityPers.getData().add(amenPersuasionData);


			try {
				((Map<Object,Object>) hotel.getHotelPersuasions()).put(Constants.AMENITIES_PLACEHOLDER_ID_APP,amenityPers);
			} catch (ClassCastException e) {
				LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
			} catch (Exception e) {
				LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
			}
		}

		if(searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails()!=null && FUNNEL_DAYUSE.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource()) && org.apache.commons.lang3.StringUtils.isNotEmpty(dayUsePersuasionsText)) {
			if (hotel.getHotelPersuasions() == null)
				hotel.setHotelPersuasions(new HashMap<String,Object>());
			PersuasionObject amenityPers = new PersuasionObject();
			amenityPers.setData(new ArrayList<>());
			amenityPers.setTemplate(IMAGE_TEXT_H);
			amenityPers.setPlaceholder("SINGLE");


			PersuasionData amenPersuasionData = new PersuasionData();
			amenPersuasionData.setHasAction(false);
			amenPersuasionData.setHtml(false);
			amenPersuasionData.setId(DAYUSE_LOCAL_ID);
			amenPersuasionData.setPersuasionType(DAYUSE_LOCAL_ID);
			amenPersuasionData.setStyle(new PersuasionStyle());
			amenPersuasionData.getStyle().setTextColor("#000000");
			amenPersuasionData.setText(dayUsePersuasionsText);
			amenPersuasionData.setIcontype("b_dot");
			amenityPers.getData().add(amenPersuasionData);
			try {
				((Map<Object,Object>) hotel.getHotelPersuasions()).put(Constants.AMENITIES_PLACEHOLDER_ID_APP,amenityPers);
			} catch (ClassCastException e) {
				LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ",e.getMessage());
			} catch (Exception e) {
				LOGGER.error("Location Persuasion could not be added due to : {} ",e.getMessage());
			}
		}

	}

	@Override
	public void addPersuasionHoverData(Hotel hotel, SearchWrapperHotelEntity hotelEntity, CancellationTimeline cancellationTimeline, DisplayFare displayFare) {

		try {
			if (null != hotel.getHotelPersuasions()) {
				JSONObject hotelPersuasions = new JSONObject(objectMapperUtil.getJsonFromObject(hotel.getHotelPersuasions(), DependencyLayer.CLIENTGATEWAY));
				for (String placeHolder : hotelPersuasions.keySet()) {
					JSONObject persuasion = hotelPersuasions.has(placeHolder) ? hotelPersuasions.getJSONObject(placeHolder) : null;
					if(null != persuasion && persuasion.has("hover")) {
						JSONObject topLevelHoverData = persuasion.getJSONObject("hover");
						updateTopLevelHover(topLevelHoverData,hotelEntity.getMpFareHoldStatus());
					}
					if (null != persuasion && persuasion.has("data")) {
						JSONArray persuasionDataList = persuasion.getJSONArray("data");
						if(persuasionDataList != null) {
							for (int i = 0; i < persuasionDataList.length(); i++) {
								JSONObject persuasionData = persuasionDataList.getJSONObject(i);
								if (persuasionData != null && persuasionData.has(PERSUASION_KEY) && persuasionData.get(PERSUASION_KEY) != null &&
										LOVED_BY_INDIANS.equalsIgnoreCase((String) persuasionData.get(PERSUASION_KEY))) {
									hotel.setLovedByIndians(true);
								}
								if (persuasionData.has("hover") && isWalletSurgePersuasionEnabled(persuasionData)) {
									persuasionData.getJSONObject("hover").put(PERSUASION_HEADING_KEY, hotelEntity.getWalletEntity().getWpmRule().getOutputDetails().getPersuasionText());
									persuasionData.getJSONObject(TIMER).put(EXPIRY, hotelEntity.getWalletEntity().getWpmRule().getExpireAt());
								}
							}
						}

					}

				}
				hotel.setHotelPersuasions(hotelPersuasions.toMap());
			}
		}catch(Exception e){
			LOGGER.error("Error while updating hover data for desktop", e);
		}
	}

	@Override
	public BottomSheet buildBottomSheet(PersonalizedResponse<SearchWrapperHotelEntity> perResponse) {
		BottomSheet bottomSheet = new BottomSheet();
		bottomSheet.setHeading(polyglotService.getTranslatedData(ConstantsTranslation.CORP_BOTTOM_SHEET_HEADING_MOBILE));
		bottomSheet.setSubHeading(polyglotService.getTranslatedData(ConstantsTranslation.CORP_BOTTOM_SHEET_SUBHEADING_MOBILE));
		bottomSheet.setImgUrl(myBizBottomSheetImage);
		bottomSheet.setCta(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_ASSURED_FILTER_CARD_CTA));
		bottomSheet.setCtaAction("");
		List<SectionFeature> sectionFeatureList = new ArrayList<>();
		sectionFeatureList.add(new SectionFeature(highRatedUrl, polyglotService.getTranslatedData(ConstantsTranslation.RATED_HIGH_BT_MOBILE), "grayDot", null, null));
		sectionFeatureList.add(new SectionFeature(gstInvoiceUrl, polyglotService.getTranslatedData(ConstantsTranslation.GST_INVOICE_ASSURANCE_TEXT), "grayDot", null, null));
		sectionFeatureList.add(new SectionFeature(bpgUrl, polyglotService.getTranslatedData(ConstantsTranslation.BPG_TEXT), "grayDot", null, null));
		bottomSheet.setSectionFeatures(sectionFeatureList);
		return bottomSheet;
	}

	//To build location persuasion to Mob-Landing Cards
	public void addLocationPersuasionToHotelPersuasions(SearchWrapperHotelEntity hotelEntity) {
		if (hotelEntity != null && CollectionUtils.isNotEmpty(hotelEntity.getLocationPersuasion())) {
			List<String> locationPersuasion = hotelEntity.getLocationPersuasion();
			if (hotelEntity.getHotelPersuasions() == null)
				hotelEntity.setHotelPersuasions(new HashMap<String, Object>());
			PersuasionObject locPers = new PersuasionObject();
			locPers.setData(new ArrayList<>());
			locPers.setPlaceholder(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP);
			locPers.setTemplate("IMAGE_TEXT_H");
			locPers.setPlaceholder("SINGLE");

			int index = 1;
			PersuasionData locPersuasionData = new PersuasionData();
			locPersuasionData.setHasAction(false);
			locPersuasionData.setHtml(true);
			locPersuasionData.setId("LOC_PERSUASION_" + index++);
			locPersuasionData.setPersuasionType("LOCATION");
			PersuasionStyle style = new PersuasionStyle();
			style.setMaxLines(2);
			locPersuasionData.setStyle(style);

			locPers.getData().add(locPersuasionData);
			if (locationPersuasion.size() == 1) {
				locPersuasionData.setText(locationPersuasion.get(0));
			} else if (StringUtils.isEmpty(locationPersuasion.get(0))) {
				locPersuasionData.setText(locationPersuasion.get(1));
			} else if (locationPersuasion.size() >= 2) {
				locPersuasionData.setText(locationPersuasion.get(0) + " | " + locationPersuasion.get(1));
			}

			try {
				((Map<Object, Object>) hotelEntity.getHotelPersuasions()).put(Constants.LOCATION_PERSUAION_PLACEHOLDER_ID_APP, locPers);
			} catch (ClassCastException e) {
				LOGGER.error("Location Persuasion could not be added due to ClassCastException : {} ", e.getMessage());
			} catch (Exception e) {
				LOGGER.error("Location Persuasion could not be added due to : {} ", e.getMessage());
			}
		}
	}

	/**
	 * This is an encapsulated method to build all the required persuasions for Hidden Gem card.
	 */
	@Override
	public void addPersuasionsForHiddenGemCard(SearchWrapperHotelEntity hotelEntity) {
		if (hotelEntity.getHotelPersuasions() == null)
			hotelEntity.setHotelPersuasions(new HashMap<String,Object>());
		addLocationPersuasionToHotelPersuasions(hotelEntity);
		buildHiddenGemPersuasion(hotelEntity);
		buildHiddenGemIconPersuasion(hotelEntity);
		buildHomeStaysPersuasion(hotelEntity);
	}

	/**
	 * Method to build Hidden Gem Persuasion, this method calls persuasionUtil to build Hidden Gem persuasion.
	 * If the util method return a non-empty Persuasion List, this method will add that persuasion in Hotel Persuasion object.
	 */
	public void buildHiddenGemPersuasion(SearchWrapperHotelEntity hotelEntity) {
		PersuasionObject hiddenGemPersuasion = persuasionUtil.buildHiddenGemPersuasion(hotelEntity, DEVICE_TYPE);
		if (hiddenGemPersuasion != null && hotelEntity.getHotelPersuasions() instanceof Map<?, ?>) {
			if(CollectionUtils.isNotEmpty(hiddenGemPersuasion.getData()))
				hiddenGemPersuasion.getData().get(0).setPersuasionType(PersuasionType.HOTEL_CATEGORY.name());
			hiddenGemPersuasion.setTemplateType(TemplateType.DEFAULT.name());
			hiddenGemPersuasion.setPlaceholder(Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdPWA());
			MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HIDDEN_GEM_PERSUASION.getPlaceholderIdPWA(), hiddenGemPersuasion);
		}
	}

	/**
	 * Method to build Hidden Gem Icon Persuasion, this method calls persuasionUtil to build Hidden Gem Icon persuasion.
	 * If the util method return a non-empty Persuasion List, this method will add that persuasion in Hotel Persuasion object.
	 */
	public void buildHiddenGemIconPersuasion(SearchWrapperHotelEntity hotelEntity) {
		PersuasionObject hiddenGemPersuasion = persuasionUtil.buildHiddenGemIconPersuasion(hotelEntity, DEVICE_TYPE);
		if (hiddenGemPersuasion != null && hotelEntity.getHotelPersuasions() instanceof Map<?, ?>) {
			if(CollectionUtils.isNotEmpty(hiddenGemPersuasion.getData()))
				hiddenGemPersuasion.getData().get(0).setPersuasionType(PersuasionType.HOTEL_CATEGORY.name());
			hiddenGemPersuasion.setTemplateType(TemplateType.DEFAULT.name());
			hiddenGemPersuasion.setPlaceholder(Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdPWA());
			MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HIDDEN_GEM_ICON_PERSUASION.getPlaceholderIdPWA(), hiddenGemPersuasion);
		}
	}

	/**
	 * Method to build Home Stays Persuasions, this method calls persuasionUtil to build Homestay persuasions.
	 * If the util method return a non-empty Persuasion List for homestay title, this method will add that persuasion in Hotel Persuasion object.
	 * And, if util method return a non-empty Persuasion List for homestay title and sub-title, this method will add both the persuasions in Hotel Persuasion object, on the same placeholder
	 */
	public void buildHomeStaysPersuasion(SearchWrapperHotelEntity hotelEntity) {
		PersuasionObject homeStaysTitlePersuasion = persuasionUtil.buildHomeStaysTitlePersuasion(hotelEntity, DEVICE_TYPE);
		if (homeStaysTitlePersuasion != null && CollectionUtils.isNotEmpty(homeStaysTitlePersuasion.getData()) && hotelEntity.getHotelPersuasions() instanceof Map<?, ?>) {
			homeStaysTitlePersuasion.setPlaceholder(Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdPWA());
			MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HOMESTAYS_TITLE_PERSUASION.getPlaceholderIdPWA(), homeStaysTitlePersuasion);

			PersuasionObject homeStaysSubTitlePersuasion = persuasionUtil.buildHomeStaysSubTitlePersuasion(hotelEntity, DEVICE_TYPE);
			if (homeStaysSubTitlePersuasion != null && CollectionUtils.isNotEmpty(homeStaysSubTitlePersuasion.getData())) {
				homeStaysSubTitlePersuasion.setPlaceholder(Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdPWA());
				homeStaysTitlePersuasion.getData().add(homeStaysSubTitlePersuasion.getData().get(0));
				MapUtils.safeAddToMap((Map<?, ?>) hotelEntity.getHotelPersuasions(), Persuasions.HOMESTAYS_SUB_TITLE_PERSUASION.getPlaceholderIdPWA(), homeStaysTitlePersuasion);
			}
		}
	}

	@Override
	public void addSpecialFarePersuasion(SearchWrapperHotelEntity hotelEntity) {

	}

	@Override
	public void addBookingConfirmationPersuasion(SearchWrapperHotelEntity hotelEntity) {

	}
	@Override
	public String buildBGColor(String sectionName, String orientation, String cardType) {
		return null;
	}
}
