package com.mmt.hotels.clientgateway.consul;
import com.mmt.hotels.model.response.listpersonalization.MySafetyTooltip;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


@RefreshScope
@Configuration
@Component
@ConfigurationProperties(prefix = "property-text-config")
public class PropertyTextConfigConsul {
    private Map<String, Object> mySafetyDataTooltips;

    private List<String> amenetiesWithUrl;

    private MySafetyTooltip mySafetyTooltipKeys;


    public Map<String, Object> getMySafetyDataTooltips() {
        return mySafetyDataTooltips;
    }

    public void setMySafetyDataTooltips(Map<String, Object> mySafetyDataTooltips) {
        this.mySafetyDataTooltips = mySafetyDataTooltips;
    }

    public List<String> getAmenetiesWithUrl() {
        return amenetiesWithUrl;
    }

    public void setAmenetiesWithUrl(List<String> amenetiesWithUrl) {
        this.amenetiesWithUrl = amenetiesWithUrl;
    }

    public MySafetyTooltip getMySafetyTooltipKeys() {
        return mySafetyTooltipKeys;
    }

    public void setMySafetyTooltipKeys(MySafetyTooltip mySafetyTooltipKeys) {
        this.mySafetyTooltipKeys = mySafetyTooltipKeys;
    }
}
