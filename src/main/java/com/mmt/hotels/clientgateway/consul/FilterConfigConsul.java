package com.mmt.hotels.clientgateway.consul;

import com.mmt.hotels.clientgateway.businessobjects.AmenityCategory;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfiguration;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.response.filter.FilterPricingOption;
import com.mmt.propertymanager.util.JsonConvertor;
import org.aeonbits.owner.Config;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


@RefreshScope
@Configuration
@Component
@ConfigurationProperties(prefix = "cg-filter-config-polyglot")
public class FilterConfigConsul {

    private String baseFilterSettings="{}";
    private String baseFilterSettingsV2="{}";
    private String appsDomFilterSettingsV2="{}";
    private String appsIntlFilterSettingsV2="{}";
    private String appsHomestayFilterSettingsV2="{}";
    private String appsCorpIntlFilterSettingsV2="{}";
    private String appsCorpDomFilterSettingsV2="{}";
    private String dayUseFilterSettingsV2="{}";
    private String gccFilterSettingsV2="{}";
    private String premiumFunnelFilterSettingsV2="{}";
    private String desktopAltBookingFilterSettingV2="{}";

    private String desktopDomHotelsFilterSettingV2="{}";
    private String desktopDomHomestayFilterSettingV2="{}";
    private String desktopIntlHomestayFilterSettingV2="{}";
    private String desktopIntlHotelsFilterSettingV2="{}";
    private String desktopGCCFilterSettingV2="{}";
    private String desktopMyPartnerFilterSettingsV2="{}";
    private String desktopCorpDomFilterSettingV2="{}";
    private String desktopCorpIntlFilterSettingV2="{}";

    private String  pwaDomHotelsFilterSettingV2="{}";
    private String  pwaDomHomestayFilterSettingV2="{}";
    private String  pwaIntlHomestayFilterSettingV2="{}";
    private String  pwaIntlHotelsFilterSettingV2="{}";
    private String  pwaGCCFilterSettingV2="{}";
    private String  pwaMyPartnerFilterSettingsV2="{}";

    private String  seoDomFilterSettingV2="{}";
    private String  seoIntlFilterSettingV2="{}";
    private String  metaIntlFilterSettingV2="{}";
    private String  metaDomFilterSettingV2="{}";
    private String  semIntlFilterSettingV2="{}";
    private String  semDomFilterSettingV2="{}";

    private String defaultPriceHistogram="{}";


    private String defaultPriceHistogramCorp="{}";


    private String pwaMyPartnerFilterSettings="{}";


    private String desktopMyPartnerFilterSettings="{}";


    private Map<String, List<Filter>> compositeFilterConfig;


    private String amenitiesCategoryConfig="{}";


    private String amenitiesCategoryConfigPolyGlot = "{}";

    private String desktopGCCFilterSetting                 ="{}";
    private String androidGCCFilterSetting                 ="{}";
    private String iosGCCFilterSetting                     ="{}";
    private String pwaGCCFilterSetting                     ="{}";
    private String seoIntlFilterSetting                    ="{}";
    private String seoDomFilterSetting                     ="{}";
    private String metaIntlFilterSetting                   ="{}";
    private String metaDomFilterSetting                    ="{}";
    private String semIntlFilterSetting                    ="{}";
    private String semDomFilterSetting                     ="{}";
    private String phonePeFilterSetting                    ="{}";
    private String desktopIntlHotelsFilterSetting          ="{}";
    private String appsIntlHotelsFilterSetting             ="{}";
    private String pwaIntlHotelsFilterSetting              ="{}";
    private String desktopDomHotelsFilterSetting           ="{}";
    private String appsDomHotelsFilterSetting              ="{}";
    private String pwaDomHotelsFilterSetting               ="{}";
    private String desktopIntlHomestayFilterSetting        ="{}";
    private String appsIntlHomestayFilterSetting           ="{}";
    private String pwaIntlHomestayFilterSetting            ="{}";
    private String desktopDomHomestayFilterSetting         ="{}";
    private String appsDomHomestayFilterSetting            ="{}";
    private String pwaDomHomestayFilterSetting             ="{}";
    private String dayuseFilterSetting                     ="{}";
    private String desktopCorpIntlFilterSetting            ="{}";
    private String appsCorpIntlFilterSetting               ="{}";
    private String pwaCorpIntlFilterSetting                ="{}";
    private String desktopCorpDomFilterSetting             ="{}";
    private String appsCorpDomFilterSetting                ="{}";
    private String pwaCorpDomFilterSetting                 ="{}";
    private String desktopAltBookingFilterSetting           ="{}";


    private FilterPricingOption pricingOption;

    private String intlDefaultPriceBucketConfig;

    private String intlCityWisePriceBucketConfig;

    private String intlPriceBucketConfig;

    public String getIntlDefaultPriceBucketConfig() {
        return intlDefaultPriceBucketConfig;
    }

    public void setIntlDefaultPriceBucketConfig(String intlDefaultPriceBucketConfig) {
        this.intlDefaultPriceBucketConfig = intlDefaultPriceBucketConfig;
    }

    public String getIntlCityWisePriceBucketConfig() {
        return intlCityWisePriceBucketConfig;
    }

    public void setIntlCityWisePriceBucketConfig(String intlCityWisePriceBucketConfig) {
        this.intlCityWisePriceBucketConfig = intlCityWisePriceBucketConfig;
    }

    public String getIntlPriceBucketConfig() {
        return intlPriceBucketConfig;
    }

    public void setIntlPriceBucketConfig(String intlPriceBucketConfig) {
        this.intlPriceBucketConfig = intlPriceBucketConfig;
    }

    public String getBaseFilterSettings() {
        return baseFilterSettings;
    }

    public void setBaseFilterSettings(String baseFilterSettings) {
        this.baseFilterSettings = baseFilterSettings;
    }

    public String getBaseFilterSettingsV2() {
        return baseFilterSettingsV2;
    }

    public String getAppsDomFilterSettingsV2() {
        return appsDomFilterSettingsV2;
    }

    public void setAppsDomFilterSettingsV2(String appsDomFilterSettingsV2) {
        this.appsDomFilterSettingsV2 = appsDomFilterSettingsV2;
    }

    public String getAppsIntlFilterSettingsV2() {
        return appsIntlFilterSettingsV2;
    }

    public void setAppsIntlFilterSettingsV2(String appsIntlFilterSettingsV2) {
        this.appsIntlFilterSettingsV2 = appsIntlFilterSettingsV2;
    }

    public void setBaseFilterSettingsV2(String baseFilterSettingsV2) {
        this.baseFilterSettingsV2 = baseFilterSettingsV2;
    }

    public String getAppsHomestayFilterSettingsV2() {
        return appsHomestayFilterSettingsV2;
    }

    public void setAppsHomestayFilterSettingsV2(String appsHomestayFilterSettingsV2) {
        this.appsHomestayFilterSettingsV2 = appsHomestayFilterSettingsV2;
    }

    public String getAppsCorpIntlFilterSettingsV2() {
        return appsCorpIntlFilterSettingsV2;
    }

    public void setAppsCorpIntlFilterSettingsV2(String appsCorpIntlFilterSettingsV2) {
        this.appsCorpIntlFilterSettingsV2 = appsCorpIntlFilterSettingsV2;
    }

    public String getAppsCorpDomFilterSettingsV2() {
        return appsCorpDomFilterSettingsV2;
    }

    public void setAppsCorpDomFilterSettingsV2(String appsCorpDomFilterSettingsV2) {
        this.appsCorpDomFilterSettingsV2 = appsCorpDomFilterSettingsV2;
    }

    public String getDayUseFilterSettingsV2() {
        return dayUseFilterSettingsV2;
    }

    public void setDayUseFilterSettingsV2(String dayUseFilterSettingsV2) {
        this.dayUseFilterSettingsV2 = dayUseFilterSettingsV2;
    }

    public String getGccFilterSettingsV2() {
        return gccFilterSettingsV2;
    }

    public String getPremiumFunnelFilterSettingsV2() {
        return premiumFunnelFilterSettingsV2;
    }

    public void setPremiumFunnelFilterSettingsV2(String premiumFunnelFilterSettingsV2) {
        this.premiumFunnelFilterSettingsV2 = premiumFunnelFilterSettingsV2;
    }

    public String getDesktopAltBookingFilterSettingV2() {
        return desktopAltBookingFilterSettingV2;
    }

    public void setDesktopAltBookingFilterSettingV2(String desktopAltBookingFilterSettingV2) {
        this.desktopAltBookingFilterSettingV2 = desktopAltBookingFilterSettingV2;
    }

    public void setGccFilterSettingsV2(String gccFilterSettingsV2) {
        this.gccFilterSettingsV2 = gccFilterSettingsV2;
    }

    public String getDefaultPriceHistogram() {
        return defaultPriceHistogram;
    }

    public void setDefaultPriceHistogram(String defaultPriceHistogram) {
        this.defaultPriceHistogram = defaultPriceHistogram;
    }

    public String getDefaultPriceHistogramCorp() {
        return defaultPriceHistogramCorp;
    }

    public void setDefaultPriceHistogramCorp(String defaultPriceHistogramCorp) {
        this.defaultPriceHistogramCorp = defaultPriceHistogramCorp;
    }

    public String getPwaMyPartnerFilterSettings() {
        return pwaMyPartnerFilterSettings;
    }

    public void setPwaMyPartnerFilterSettings(String pwaMyPartnerFilterSettings) {
        this.pwaMyPartnerFilterSettings = pwaMyPartnerFilterSettings;
    }

    public String getDesktopMyPartnerFilterSettings() {
        return desktopMyPartnerFilterSettings;
    }

    public void setDesktopMyPartnerFilterSettings(String desktopMyPartnerFilterSettings) {
        this.desktopMyPartnerFilterSettings = desktopMyPartnerFilterSettings;
    }

    public Map<String, List<Filter>> getCompositeFilterConfig() {
        return compositeFilterConfig;
    }

    public void setCompositeFilterConfig(Map<String, List<Filter>> compositeFilterConfig) {
        this.compositeFilterConfig = compositeFilterConfig;
    }

    public String getAmenitiesCategoryConfig() {
        return amenitiesCategoryConfig;
    }

    public void setAmenitiesCategoryConfig(String amenitiesCategoryConfig) {
        this.amenitiesCategoryConfig = amenitiesCategoryConfig;
    }

    public String getAmenitiesCategoryConfigPolyGlot() {
        return amenitiesCategoryConfigPolyGlot;
    }

    public void setAmenitiesCategoryConfigPolyGlot(String amenitiesCategoryConfigPolyGlot) {
        this.amenitiesCategoryConfigPolyGlot = amenitiesCategoryConfigPolyGlot;
    }

    public FilterPricingOption getPricingOption() {
        return pricingOption;
    }

    public void setPricingOption(FilterPricingOption pricingOption) {
        this.pricingOption = pricingOption;
    }

    public String getDesktopGCCFilterSetting() {
        return desktopGCCFilterSetting;
    }

    public void setDesktopGCCFilterSetting(String desktopGCCFilterSetting) {
        this.desktopGCCFilterSetting = desktopGCCFilterSetting;
    }

    public String getAndroidGCCFilterSetting() {
        return androidGCCFilterSetting;
    }

    public void setAndroidGCCFilterSetting(String androidGCCFilterSetting) {
        this.androidGCCFilterSetting = androidGCCFilterSetting;
    }

    public String getIosGCCFilterSetting() {
        return iosGCCFilterSetting;
    }

    public void setIosGCCFilterSetting(String iosGCCFilterSetting) {
        this.iosGCCFilterSetting = iosGCCFilterSetting;
    }

    public String getPwaGCCFilterSetting() {
        return pwaGCCFilterSetting;
    }

    public void setPwaGCCFilterSetting(String pwaGCCFilterSetting) {
        this.pwaGCCFilterSetting = pwaGCCFilterSetting;
    }

    public String getSeoIntlFilterSetting() {
        return seoIntlFilterSetting;
    }

    public void setSeoIntlFilterSetting(String seoIntlFilterSetting) {
        this.seoIntlFilterSetting = seoIntlFilterSetting;
    }

    public String getSeoDomFilterSetting() {
        return seoDomFilterSetting;
    }

    public void setSeoDomFilterSetting(String seoDomFilterSetting) {
        this.seoDomFilterSetting = seoDomFilterSetting;
    }

    public String getMetaIntlFilterSetting() {
        return metaIntlFilterSetting;
    }

    public void setMetaIntlFilterSetting(String metaIntlFilterSetting) {
        this.metaIntlFilterSetting = metaIntlFilterSetting;
    }

    public String getMetaDomFilterSetting() {
        return metaDomFilterSetting;
    }

    public void setMetaDomFilterSetting(String metaDomFilterSetting) {
        this.metaDomFilterSetting = metaDomFilterSetting;
    }

    public String getSemIntlFilterSetting() {
        return semIntlFilterSetting;
    }

    public void setSemIntlFilterSetting(String semIntlFilterSetting) {
        this.semIntlFilterSetting = semIntlFilterSetting;
    }

    public String getSemDomFilterSetting() {
        return semDomFilterSetting;
    }

    public void setSemDomFilterSetting(String semDomFilterSetting) {
        this.semDomFilterSetting = semDomFilterSetting;
    }

    public String getPhonePeFilterSetting() {
        return phonePeFilterSetting;
    }

    public void setPhonePeFilterSetting(String phonePeFilterSetting) {
        this.phonePeFilterSetting = phonePeFilterSetting;
    }

    public String getDesktopIntlHotelsFilterSetting() {
        return desktopIntlHotelsFilterSetting;
    }

    public void setDesktopIntlHotelsFilterSetting(String desktopIntlHotelsFilterSetting) {
        this.desktopIntlHotelsFilterSetting = desktopIntlHotelsFilterSetting;
    }

    public String getAppsIntlHotelsFilterSetting() {
        return appsIntlHotelsFilterSetting;
    }

    public void setAppsIntlHotelsFilterSetting(String appsIntlHotelsFilterSetting) {
        this.appsIntlHotelsFilterSetting = appsIntlHotelsFilterSetting;
    }

    public String getPwaIntlHotelsFilterSetting() {
        return pwaIntlHotelsFilterSetting;
    }

    public void setPwaIntlHotelsFilterSetting(String pwaIntlHotelsFilterSetting) {
        this.pwaIntlHotelsFilterSetting = pwaIntlHotelsFilterSetting;
    }

    public String getDesktopDomHotelsFilterSetting() {
        return desktopDomHotelsFilterSetting;
    }

    public void setDesktopDomHotelsFilterSetting(String desktopDomHotelsFilterSetting) {
        this.desktopDomHotelsFilterSetting = desktopDomHotelsFilterSetting;
    }

    public String getAppsDomHotelsFilterSetting() {
        return appsDomHotelsFilterSetting;
    }

    public void setAppsDomHotelsFilterSetting(String appsDomHotelsFilterSetting) {
        this.appsDomHotelsFilterSetting = appsDomHotelsFilterSetting;
    }

    public String getPwaDomHotelsFilterSetting() {
        return pwaDomHotelsFilterSetting;
    }

    public void setPwaDomHotelsFilterSetting(String pwaDomHotelsFilterSetting) {
        this.pwaDomHotelsFilterSetting = pwaDomHotelsFilterSetting;
    }

    public String getDesktopIntlHomestayFilterSetting() {
        return desktopIntlHomestayFilterSetting;
    }

    public void setDesktopIntlHomestayFilterSetting(String desktopIntlHomestayFilterSetting) {
        this.desktopIntlHomestayFilterSetting = desktopIntlHomestayFilterSetting;
    }

    public String getAppsIntlHomestayFilterSetting() {
        return appsIntlHomestayFilterSetting;
    }

    public void setAppsIntlHomestayFilterSetting(String appsIntlHomestayFilterSetting) {
        this.appsIntlHomestayFilterSetting = appsIntlHomestayFilterSetting;
    }

    public String getPwaIntlHomestayFilterSetting() {
        return pwaIntlHomestayFilterSetting;
    }

    public void setPwaIntlHomestayFilterSetting(String pwaIntlHomestayFilterSetting) {
        this.pwaIntlHomestayFilterSetting = pwaIntlHomestayFilterSetting;
    }

    public String getDesktopDomHomestayFilterSetting() {
        return desktopDomHomestayFilterSetting;
    }

    public void setDesktopDomHomestayFilterSetting(String desktopDomHomestayFilterSetting) {
        this.desktopDomHomestayFilterSetting = desktopDomHomestayFilterSetting;
    }

    public String getAppsDomHomestayFilterSetting() {
        return appsDomHomestayFilterSetting;
    }

    public void setAppsDomHomestayFilterSetting(String appsDomHomestayFilterSetting) {
        this.appsDomHomestayFilterSetting = appsDomHomestayFilterSetting;
    }

    public String getPwaDomHomestayFilterSetting() {
        return pwaDomHomestayFilterSetting;
    }

    public void setPwaDomHomestayFilterSetting(String pwaDomHomestayFilterSetting) {
        this.pwaDomHomestayFilterSetting = pwaDomHomestayFilterSetting;
    }

    public String getDayuseFilterSetting() {
        return dayuseFilterSetting;
    }

    public void setDayuseFilterSetting(String dayuseFilterSetting) {
        this.dayuseFilterSetting = dayuseFilterSetting;
    }

    public String getDesktopCorpIntlFilterSetting() {
        return desktopCorpIntlFilterSetting;
    }

    public void setDesktopCorpIntlFilterSetting(String desktopCorpIntlFilterSetting) {
        this.desktopCorpIntlFilterSetting = desktopCorpIntlFilterSetting;
    }

    public String getAppsCorpIntlFilterSetting() {
        return appsCorpIntlFilterSetting;
    }

    public void setAppsCorpIntlFilterSetting(String appsCorpIntlFilterSetting) {
        this.appsCorpIntlFilterSetting = appsCorpIntlFilterSetting;
    }

    public String getPwaCorpIntlFilterSetting() {
        return pwaCorpIntlFilterSetting;
    }

    public void setPwaCorpIntlFilterSetting(String pwaCorpIntlFilterSetting) {
        this.pwaCorpIntlFilterSetting = pwaCorpIntlFilterSetting;
    }

    public String getDesktopCorpDomFilterSetting() {
        return desktopCorpDomFilterSetting;
    }

    public void setDesktopCorpDomFilterSetting(String desktopCorpDomFilterSetting) {
        this.desktopCorpDomFilterSetting = desktopCorpDomFilterSetting;
    }

    public String getAppsCorpDomFilterSetting() {
        return appsCorpDomFilterSetting;
    }

    public void setAppsCorpDomFilterSetting(String appsCorpDomFilterSetting) {
        this.appsCorpDomFilterSetting = appsCorpDomFilterSetting;
    }

    public String getPwaCorpDomFilterSetting() {
        return pwaCorpDomFilterSetting;
    }

    public void setPwaCorpDomFilterSetting(String pwaCorpDomFilterSetting) {
        this.pwaCorpDomFilterSetting = pwaCorpDomFilterSetting;
    }

    public String getDesktopAltBookingFilterSetting() {
        return desktopAltBookingFilterSetting;
    }

    public void setDesktopAltBookingFilterSetting(String desktopAltBookingFilterSetting) {
        this.desktopAltBookingFilterSetting = desktopAltBookingFilterSetting;
    }

    public String getDesktopDomHotelsFilterSettingV2() {
        return desktopDomHotelsFilterSettingV2;
    }

    public void setDesktopDomHotelsFilterSettingV2(String desktopDomHotelsFilterSettingV2) {
        this.desktopDomHotelsFilterSettingV2 = desktopDomHotelsFilterSettingV2;
    }

    public String getDesktopDomHomestayFilterSettingV2() {
        return desktopDomHomestayFilterSettingV2;
    }

    public void setDesktopDomHomestayFilterSettingV2(String desktopDomHomestayFilterSettingV2) {
        this.desktopDomHomestayFilterSettingV2 = desktopDomHomestayFilterSettingV2;
    }

    public String getDesktopIntlHomestayFilterSettingV2() {
        return desktopIntlHomestayFilterSettingV2;
    }

    public void setDesktopIntlHomestayFilterSettingV2(String desktopIntlHomestayFilterSettingV2) {
        this.desktopIntlHomestayFilterSettingV2 = desktopIntlHomestayFilterSettingV2;
    }

    public String getDesktopIntlHotelsFilterSettingV2() {
        return desktopIntlHotelsFilterSettingV2;
    }

    public void setDesktopIntlHotelsFilterSettingV2(String desktopIntlHotelsFilterSettingV2) {
        this.desktopIntlHotelsFilterSettingV2 = desktopIntlHotelsFilterSettingV2;
    }

    public String getDesktopGCCFilterSettingV2() {
        return desktopGCCFilterSettingV2;
    }

    public void setDesktopGCCFilterSettingV2(String desktopGCCFilterSettingV2) {
        this.desktopGCCFilterSettingV2 = desktopGCCFilterSettingV2;
    }

    public String getDesktopMyPartnerFilterSettingsV2() {
        return desktopMyPartnerFilterSettingsV2;
    }

    public void setDesktopMyPartnerFilterSettingsV2(String desktopMyPartnerFilterSettingsV2) {
        this.desktopMyPartnerFilterSettingsV2 = desktopMyPartnerFilterSettingsV2;
    }

    public String getDesktopCorpDomFilterSettingV2() {
        return desktopCorpDomFilterSettingV2;
    }

    public void setDesktopCorpDomFilterSettingV2(String desktopCorpDomFilterSettingV2) {
        this.desktopCorpDomFilterSettingV2 = desktopCorpDomFilterSettingV2;
    }

    public String getDesktopCorpIntlFilterSettingV2() {
        return desktopCorpIntlFilterSettingV2;
    }

    public void setDesktopCorpIntlFilterSettingV2(String desktopCorpIntlFilterSettingV2) {
        this.desktopCorpIntlFilterSettingV2 = desktopCorpIntlFilterSettingV2;
    }

    public String getPwaDomHotelsFilterSettingV2() {
        return pwaDomHotelsFilterSettingV2;
    }

    public void setPwaDomHotelsFilterSettingV2(String pwaDomHotelsFilterSettingV2) {
        this.pwaDomHotelsFilterSettingV2 = pwaDomHotelsFilterSettingV2;
    }

    public String getPwaDomHomestayFilterSettingV2() {
        return pwaDomHomestayFilterSettingV2;
    }

    public void setPwaDomHomestayFilterSettingV2(String pwaDomHomestayFilterSettingV2) {
        this.pwaDomHomestayFilterSettingV2 = pwaDomHomestayFilterSettingV2;
    }

    public String getPwaIntlHomestayFilterSettingV2() {
        return pwaIntlHomestayFilterSettingV2;
    }

    public void setPwaIntlHomestayFilterSettingV2(String pwaIntlHomestayFilterSettingV2) {
        this.pwaIntlHomestayFilterSettingV2 = pwaIntlHomestayFilterSettingV2;
    }

    public String getPwaIntlHotelsFilterSettingV2() {
        return pwaIntlHotelsFilterSettingV2;
    }

    public void setPwaIntlHotelsFilterSettingV2(String pwaIntlHotelsFilterSettingV2) {
        this.pwaIntlHotelsFilterSettingV2 = pwaIntlHotelsFilterSettingV2;
    }

    public String getPwaGCCFilterSettingV2() {
        return pwaGCCFilterSettingV2;
    }

    public void setPwaGCCFilterSettingV2(String pwaGCCFilterSettingV2) {
        this.pwaGCCFilterSettingV2 = pwaGCCFilterSettingV2;
    }

    public String getPwaMyPartnerFilterSettingsV2() {
        return pwaMyPartnerFilterSettingsV2;
    }

    public void setPwaMyPartnerFilterSettingsV2(String pwaMyPartnerFilterSettingsV2) {
        this.pwaMyPartnerFilterSettingsV2 = pwaMyPartnerFilterSettingsV2;
    }

    public String getSeoDomFilterSettingV2() {
        return seoDomFilterSettingV2;
    }

    public void setSeoDomFilterSettingV2(String seoDomFilterSettingV2) {
        this.seoDomFilterSettingV2 = seoDomFilterSettingV2;
    }

    public String getSeoIntlFilterSettingV2() {
        return seoIntlFilterSettingV2;
    }

    public void setSeoIntlFilterSettingV2(String seoIntlFilterSettingV2) {
        this.seoIntlFilterSettingV2 = seoIntlFilterSettingV2;
    }

    public String getMetaIntlFilterSettingV2() {
        return metaIntlFilterSettingV2;
    }

    public void setMetaIntlFilterSettingV2(String metaIntlFilterSettingV2) {
        this.metaIntlFilterSettingV2 = metaIntlFilterSettingV2;
    }

    public String getMetaDomFilterSettingV2() {
        return metaDomFilterSettingV2;
    }

    public void setMetaDomFilterSettingV2(String metaDomFilterSettingV2) {
        this.metaDomFilterSettingV2 = metaDomFilterSettingV2;
    }

    public String getSemIntlFilterSettingV2() {
        return semIntlFilterSettingV2;
    }

    public void setSemIntlFilterSettingV2(String semIntlFilterSettingV2) {
        this.semIntlFilterSettingV2 = semIntlFilterSettingV2;
    }

    public String getSemDomFilterSettingV2() {
        return semDomFilterSettingV2;
    }

    public void setSemDomFilterSettingV2(String semDomFilterSettingV2) {
        this.semDomFilterSettingV2 = semDomFilterSettingV2;
    }
}
