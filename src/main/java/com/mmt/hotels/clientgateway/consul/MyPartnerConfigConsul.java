package com.mmt.hotels.clientgateway.consul;

import com.mmt.hotels.clientgateway.response.MarkUpConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.Map;


@RefreshScope
@Configuration
@Component
@ConfigurationProperties(prefix = "my-part-config")
public class MyPartnerConfigConsul {
    private Map<String,Double> discountParameters;
    private MarkUpConfig markUpConfig;

    public MarkUpConfig getMarkUpConfig() {
        return markUpConfig;
    }

    public void setMarkUpConfig(final MarkUpConfig markUpConfig) {
        this.markUpConfig = markUpConfig;
    }

    public Map<String, Double> getDiscountParameters() {
        return discountParameters;
    }

    public void setDiscountParameters(Map<String, Double> discountParameters) {
        this.discountParameters = discountParameters;
    }
}
