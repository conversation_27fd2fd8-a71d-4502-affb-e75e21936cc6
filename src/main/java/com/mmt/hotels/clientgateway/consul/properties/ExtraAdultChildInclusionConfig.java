package com.mmt.hotels.clientgateway.consul.properties;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExtraAdultChildInclusionConfig {
    private boolean enabled;
    private boolean dhSupplierCodeCheckEnabled;
    private boolean ihSupplierCodeCheckEnabled;
    private String iconUrl;
    private List<String> styleClasses;
    private List<String> enabledDHSupplierCodeList;
    private List<String> enabledIHSupplierCodeList;
    private List<String> disabledMealPlanList;
}
