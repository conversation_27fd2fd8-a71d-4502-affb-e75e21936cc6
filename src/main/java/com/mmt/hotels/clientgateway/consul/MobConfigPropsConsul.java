package com.mmt.hotels.clientgateway.consul;

import com.mmt.hotels.clientgateway.consul.consulHelper.HighValueCallSupportDetails;
import com.mmt.hotels.clientgateway.pms.ApiProperties;
import com.mmt.hotels.clientgateway.pms.SourceRegionSpecificDataConfig;
import com.mmt.hotels.clientgateway.response.searchHotels.FilterConditions;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


@RefreshScope
@Configuration
@Component
@ConfigurationProperties(prefix = "mob-config-props")
public class MobConfigPropsConsul {
    public String version;

    public String configJson;

    public int guavaCacheDetailResponsesCapacity=100;

    public int hotelInfoCacheResponseCapacity=200;

    public int guavaCacheExpiryMinutes=2;


    public int guavaCacheLandingResponsesCapacity=100;

    public int guavaLandingCacheExpiryMinutes=2;

    public List<Integer> landingApiTimeOuts;

    Map<String, Integer> seekConceptPriorities;

    Map<String, Integer> seekSubConceptPriorities;

    int latchWaitTimeInMillSec=500;

    public int collectionApiTimeOut=100;

    public String versionA;
    public String configJsonA;
    public String versionB;
    public String configJsonB;
    public String versionC;
    public String configJsonC;
    public String versionD;
    public String configJsonD;
    private HighValueCallSupportDetails supportDetails;

    private Map<String, SourceRegionSpecificDataConfig> SourceRegionSpecificDataMapping;

    private Map<String, List<String>> domainBasedPersuasionsFiltering;

    public List<Integer> detailApiTimeOuts;

    private FilterConditions filterConditions;

    private Map<String, List<String>> persuasionPlaceHoldersToShow;

//    @Config.Key("DETAIL.API.PROPERTIES.MAP")
    public Map<String, ApiProperties> detailApiSettings;

//    @Config.Key("LANDING.API.PROPERTIES.MAP")
    public Map<String, ApiProperties> landingApiSettings;

    private Map<String,Integer> corpSectionListCount;

    private Map<String,Integer> myPartnerSectionListCount;

    public Map<String, Integer> getMyPartnerSectionListCount() {
        return myPartnerSectionListCount;
    }

    public void setMyPartnerSectionListCount(Map<String, Integer> myPartnerSectionListCount) {
        this.myPartnerSectionListCount = myPartnerSectionListCount;
    }

    public HighValueCallSupportDetails getSupportDetails() {
        return supportDetails;
    }

    public void setSupportDetails(HighValueCallSupportDetails supportDetails) {
        this.supportDetails = supportDetails;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getConfigJson() {
        return configJson;
    }

    public void setConfigJson(String configJson) {
        this.configJson = configJson;
    }

    public int getGuavaCacheDetailResponsesCapacity() {
        return guavaCacheDetailResponsesCapacity;
    }

    public void setGuavaCacheDetailResponsesCapacity(int guavaCacheDetailResponsesCapacity) {
        this.guavaCacheDetailResponsesCapacity = guavaCacheDetailResponsesCapacity;
    }

    public int getHotelInfoCacheResponseCapacity() {
        return hotelInfoCacheResponseCapacity;
    }

    public void setHotelInfoCacheResponseCapacity(int hotelInfoCacheResponseCapacity) {
        this.hotelInfoCacheResponseCapacity = hotelInfoCacheResponseCapacity;
    }

    public int getGuavaCacheExpiryMinutes() {
        return guavaCacheExpiryMinutes;
    }

    public void setGuavaCacheExpiryMinutes(int guavaCacheExpiryMinutes) {
        this.guavaCacheExpiryMinutes = guavaCacheExpiryMinutes;
    }

    public int getGuavaCacheLandingResponsesCapacity() {
        return guavaCacheLandingResponsesCapacity;
    }

    public void setGuavaCacheLandingResponsesCapacity(int guavaCacheLandingResponsesCapacity) {
        this.guavaCacheLandingResponsesCapacity = guavaCacheLandingResponsesCapacity;
    }

    public int getGuavaLandingCacheExpiryMinutes() {
        return guavaLandingCacheExpiryMinutes;
    }

    public void setGuavaLandingCacheExpiryMinutes(int guavaLandingCacheExpiryMinutes) {
        this.guavaLandingCacheExpiryMinutes = guavaLandingCacheExpiryMinutes;
    }

    public List<Integer> getLandingApiTimeOuts() {
        return landingApiTimeOuts;
    }

    public void setLandingApiTimeOuts(List<Integer> landingApiTimeOuts) {
        this.landingApiTimeOuts = landingApiTimeOuts;
    }

    public Map<String, Integer> getSeekConceptPriorities() {
        return seekConceptPriorities;
    }

    public void setSeekConceptPriorities(Map<String, Integer> seekConceptPriorities) {
        this.seekConceptPriorities = seekConceptPriorities;
    }

    public Map<String, Integer> getSeekSubConceptPriorities() {
        return seekSubConceptPriorities;
    }

    public void setSeekSubConceptPriorities(Map<String, Integer> seekSubConceptPriorities) {
        this.seekSubConceptPriorities = seekSubConceptPriorities;
    }

    public int getLatchWaitTimeInMillSec() {
        return latchWaitTimeInMillSec;
    }

    public void setLatchWaitTimeInMillSec(int latchWaitTimeInMillSec) {
        this.latchWaitTimeInMillSec = latchWaitTimeInMillSec;
    }

    public int getCollectionApiTimeOut() {
        return collectionApiTimeOut;
    }

    public void setCollectionApiTimeOut(int collectionApiTimeOut) {
        this.collectionApiTimeOut = collectionApiTimeOut;
    }

    public String getVersionA() {
        return versionA;
    }

    public void setVersionA(String versionA) {
        this.versionA = versionA;
    }

    public String getConfigJsonA() {
        return configJsonA;
    }

    public void setConfigJsonA(String configJsonA) {
        this.configJsonA = configJsonA;
    }

    public String getVersionB() {
        return versionB;
    }

    public void setVersionB(String versionB) {
        this.versionB = versionB;
    }

    public String getConfigJsonB() {
        return configJsonB;
    }

    public void setConfigJsonB(String configJsonB) {
        this.configJsonB = configJsonB;
    }

    public String getVersionC() {
        return versionC;
    }

    public void setVersionC(String versionC) {
        this.versionC = versionC;
    }

    public String getConfigJsonC() {
        return configJsonC;
    }

    public void setConfigJsonC(String configJsonC) {
        this.configJsonC = configJsonC;
    }

    public String getVersionD() {
        return versionD;
    }

    public void setVersionD(String versionD) {
        this.versionD = versionD;
    }

    public String getConfigJsonD() {
        return configJsonD;
    }

    public void setConfigJsonD(String configJsonD) {
        this.configJsonD = configJsonD;
    }

    public Map<String, SourceRegionSpecificDataConfig> getSourceRegionSpecificDataMapping() {
        return SourceRegionSpecificDataMapping;
    }

    public void setSourceRegionSpecificDataMapping(Map<String, SourceRegionSpecificDataConfig> sourceRegionSpecificDataMapping) {
        SourceRegionSpecificDataMapping = sourceRegionSpecificDataMapping;
    }

    public Map<String, List<String>> getDomainBasedPersuasionsFiltering() {
        return domainBasedPersuasionsFiltering;
    }

    public void setDomainBasedPersuasionsFiltering(Map<String, List<String>> domainBasedPersuasionsFiltering) {
        this.domainBasedPersuasionsFiltering = domainBasedPersuasionsFiltering;
    }

    public List<Integer> getDetailApiTimeOuts() {
        return detailApiTimeOuts;
    }

    public void setDetailApiTimeOuts(List<Integer> detailApiTimeOuts) {
        this.detailApiTimeOuts = detailApiTimeOuts;
    }

    public FilterConditions getFilterConditions() {
        return filterConditions;
    }

    public void setFilterConditions(FilterConditions filterConditions) {
        this.filterConditions = filterConditions;
    }

    public Map<String, List<String>> getPersuasionPlaceHoldersToShow() {
        return persuasionPlaceHoldersToShow;
    }

    public void setPersuasionPlaceHoldersToShow(Map<String, List<String>> persuasionPlaceHoldersToShow) {
        this.persuasionPlaceHoldersToShow = persuasionPlaceHoldersToShow;
    }

    public Map<String, ApiProperties> getDetailApiSettings() {
        return detailApiSettings;
    }

    public void setDetailApiSettings(Map<String, ApiProperties> detailApiSettings) {
        this.detailApiSettings = detailApiSettings;
    }

    public Map<String, ApiProperties> getLandingApiSettings() {
        return landingApiSettings;
    }

    public void setLandingApiSettings(Map<String, ApiProperties> landingApiSettings) {
        this.landingApiSettings = landingApiSettings;
    }

    public Map<String, Integer> getCorpSectionListCount() {
        return corpSectionListCount;
    }

    public void setCorpSectionListCount(Map<String, Integer> corpSectionListCount) {
        this.corpSectionListCount = corpSectionListCount;
    }
}
