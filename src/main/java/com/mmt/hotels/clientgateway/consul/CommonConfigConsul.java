package com.mmt.hotels.clientgateway.consul;


import com.mmt.hotels.clientgateway.businessobjects.FilterDetail;
import com.mmt.hotels.clientgateway.businessobjects.IHGInclusionConfig;
import com.mmt.hotels.clientgateway.consul.properties.ExtraAdultChildInclusionConfig;
import com.mmt.hotels.clientgateway.pms.SourceRegionSpecificDataConfig;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.response.HotelCategoryData;
import com.mmt.hotels.clientgateway.response.HotelCategoryDataWeb;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUsePersuasion;
import com.mmt.hotels.clientgateway.response.moblanding.CardData;
import com.mmt.hotels.clientgateway.response.moblanding.CardInfo;
import com.mmt.hotels.clientgateway.response.searchHotels.BgGradient;
import com.mmt.hotels.clientgateway.response.searchHotels.BottomSheet;
import com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard;
import com.mmt.hotels.clientgateway.response.staticdetail.AllInclusiveCard;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.model.response.dayuse.MissingSlotDetail;
import com.mmt.hotels.model.response.listpersonalization.LuxeToolTip;
import com.mmt.hotels.model.response.listpersonalization.MyBizAssuredToolTip;
import com.mmt.hotels.model.response.listpersonalization.MySafetyTooltip;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysTooltip;
import com.mmt.hotels.model.response.staticdata.ChatbotInfo;
import com.mmt.hotels.model.response.staticdata.HooksData;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.*;

@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "common-exp")
@Component
@Data
public class CommonConfigConsul {

    private ExtraAdultChildInclusionConfig extraAdultChildInclusionConfig;

    private ValueStaysTooltip mmtMyPartnerTooltip;

    private String persuasionConfigPWAString="{\"\":{}}";

    private String persuasionConfigDesktopString="{\"\":{}}";

    private String persuasionConfigAndroidString="{\"\":{}}";

    private String persuasionConfigIOSString="{\"\":{}}";

    private boolean disableUserServiceOtpValidation;

    private String defaultSearchContext;

    private Integer treelsFilterSize;

    private String commonUserServiceQuery;

    private String commonUserServiceFKQuery;

    private String userAndTravellerInfoReqBody;

    private String userAndTravellerInfoFKReqBody;

    private Map<String, Set<String>> groupToSegmentsMapping;

    private String ctaAffiliateId="312380";

    private Map<String,String> mmtTerminalPointAffiliateMapping;

    private Map<String, List<String>> mapHotelAndFlightCity;

    private List<String> defaultSeqTopDomCities;

    private List<String> defaultSeqTopIntlCities;

    private Set<String> hydraAllSegmentsList;

    private int pastFlightBookingDays=30;

    private boolean skipFlightCityCheck;

    private int getUserDetailsRetries=0;

    private int createGuestUserRetries=0;

    private int scarcityThreshold =5;

    private int supplierDealsFilterCountLimit = 7;

    private int serviceFeeGstPercentage = 18;

    private String noCostEmiIconUrl = "https://promos.makemytrip.com/Growth/Images/B2C/NoCostEMIOffer.png";

    private Map<String, String> reviewPagePersuasion;

    private boolean enableUUID=false;

    private List<String> userServiceServers= Arrays.asList("userservice.mmt.mmt");

    private String filterRulesForCombinationFilter;

    float greenHornExpPer=5;

    private Map<String,String> trafficSourceCDFDomainMapping;

    private List<String> taxExclusiveCountryList;

    private List<String> roiCityList;

    private List<String> ebableInboundExpDeviceList;

    private String hydraAPIKey="070f27324406fa20796cfaa18fb703c3cc57b0b6";

    private Map<String, String> srcConCurrMapping;

    private boolean disableSrcDesIntlExp;

    private Long thankYouPageExpiry=21600000L;

    @Value("1.7,1.7")
    private List<Double> androidListingMapDensity;

    @Value("1.7,1.7")
    private List<Double> iosListingMapDensity;

    @Value("1,1")
    private List<Double> desktopListingMapDensity;

    private List<String> hotelCategoryPriority;

    private List<String> altAccoCategoryPriority;

    private List<String> safetyCategories;

    private PersuasionStyle oneDayDealTimerStyleConfig;

    private Map<String,String> categoryKeyToTextMap;
    private Map<String,String> mandatoryChargesDisclaimerMap;
    private Map<String,String> mandatoryChargesTransfersShortDescMap;
    private Set<String> mandatoryChargesCityTaxCategories;
    private Set<String> mandatoryChargesServiceCategories;
    private Set<String> compulsoryChargesTaxTypes;

    private Map<String, CardData> thankYouCards;

    private Map<String, CardData> reviewPageCards;

    private List<CardData> businessIdentificationCards;

    private List<CardData> businessIdentificationNonRegisteredUserCard;

    private boolean showSaleIcon = false;

    private int ratePlanMoreOptionsLimit=1;

    private Map<String, String> mealPlanMapPolyglot;
    private String mySafetyData;

    private int apLimitForInclusionIcons=2;

    private int losFosGCCNudgePersuasion;

    private String requestToCallBackData;
    private Map<String, BottomSheet> requestToCallBackDataV2;
    private Map<String, BottomSheet> requestToCallBackDataB2C;
    private Map<String, BottomSheet> requestToCallBackDataConfigForHighValue;

    private String recommendedRoomProperties;

    private String requestToCallHotelIdData;

    private boolean mealplanFilterEnable=false;

    private boolean partnerExclusiveFilterEnable=false;

    private ValueStaysTooltip mmtValueStaysTooltipDom;

    private MyBizAssuredToolTip myBizAssuredTooltipDom;

    private ValueStaysTooltip mmtValueStaysTooltipIntl;

    private String mobgenJsonBO;

    private String mobgenStringsBO;

    private Map<String, HotelCategoryDataWeb> hotelCategoryDataWebMapNew;

    private Map<String, Map<String, HotelCategoryData>> hotelCategoryDataMap;
    private Map<String, Map<String, HotelCategoryData>> hotelCategoryDataMapV2;

    private Map<String, String> rtbCardConfigs;

    private boolean enablePanCardCheck=false;

    private LuxeToolTip luxeToolTip;

    private MySafetyTooltip mysafetytooltip;

    private Map<String, String> cardTitleMap;

    private String mandatoryChargesAlert;

    private AllInclusiveCard allInclusiveCard;

    List<String> sameDayRoomNames= Arrays.asList("");

    private Map<String,Map<String,List<String>>> supplierToRateSegmentMapping;

    private double payLaterCardLimit=7000.0;

    int thresholdForSlashedAndDefaultHourPrice=0;

    private Map<String, DayUsePersuasion> dayUseFunnelPersuasions;

    private Map<String, FilterDetail> landingFilterConditions;

    private MyBizStaticCard myBizStaticCard;

    private int starRatingMin=3;

    private int starRatingMax=5;

    private int corpBudgetPriceMin=0;

    private int corpBudgetPriceMax=2000;

    private boolean calendarRearch=false;

    private MissingSlotDetail missingSlotDetails;

    private Map<String,Map<String,String>> defaultSeqMapping;

    private Map<String,Map<String,String>> defaultCorpSequencing;

    private Map<String, SourceRegionSpecificDataConfig> sourceRegionSpecificDataMapping;

    private Map<String, Map<String, String>> collectionsCountMapping;

    private Map<Integer, Set<String>> budgetHotelCityConfig;

    private Map<String,Map<String,Map<String,Integer>>> ratePlanDisplayLogic;

    private Map<String, List<String>> chatGptReviewSummaryExp;

    private int hydraRetryCount=3;

    private Map<String, Map<String, Set<String>>> overridePokusConfig;

    private Map<String, String> crossSellDataMap;

    private int deltaPercentageForAltDates=5;

    private String priceWidgetHeadline;

    private String priceWidgetSubHeadline;

    private String priceWidgetNewFeatureTag;

    private String priceWidgetHoverHtml;

    private String priceWidgetHoverHtmlForSelected;

    private CardInfo lgpWelcomeCardData;

    private Map<String, String> concludedExperiments;

    private List<String> translateEnabledSupplierCodes = new ArrayList<>();

    private boolean ugcSummaryOrchEnabled = false;

    private boolean ugcReviewsOrchEnabled = false;

    private String homeStayAwardUrl = "https://promos.makemytrip.com/altaccoimages/IHFA_DT_Details.png";

    public Map<String, String> getConcludedExperiments() {
        return concludedExperiments;
    }

    public void setConcludedExperiments(Map<String, String> concludedExperiments) {
        this.concludedExperiments = concludedExperiments;
    }

    public String getPriceWidgetHeadline() {
        return priceWidgetHeadline;
    }

    public void setPriceWidgetHeadline(String priceWidgetHeadline) {
        this.priceWidgetHeadline = priceWidgetHeadline;
    }

    public String getPriceWidgetSubHeadline() {
        return priceWidgetSubHeadline;
    }

    public void setPriceWidgetSubHeadline(String priceWidgetSubHeadline) {
        this.priceWidgetSubHeadline = priceWidgetSubHeadline;
    }

    public String getPriceWidgetNewFeatureTag() {
        return priceWidgetNewFeatureTag;
    }

    public void setPriceWidgetNewFeatureTag(String priceWidgetNewFeatureTag) {
        this.priceWidgetNewFeatureTag = priceWidgetNewFeatureTag;
    }

    public String getPriceWidgetHoverHtml() {
        return priceWidgetHoverHtml;
    }

    public void setPriceWidgetHoverHtml(String priceWidgetHoverHtml) {
        this.priceWidgetHoverHtml = priceWidgetHoverHtml;
    }

    public String getPriceWidgetHoverHtmlForSelected() {
        return priceWidgetHoverHtmlForSelected;
    }

    public void setPriceWidgetHoverHtmlForSelected(String priceWidgetHoverHtmlForSelected) {
        this.priceWidgetHoverHtmlForSelected = priceWidgetHoverHtmlForSelected;
    }

    public List<CardData> getBusinessIdentificationCards() {
        return businessIdentificationCards;
    }

    public void setBusinessIdentificationCards(List<CardData> businessIdentificationCards) {
        this.businessIdentificationCards = businessIdentificationCards;
    }


    private String genericRestErrorMessage;
    private String bathroomReviewSummaryMediaTag;

    private String rewardIconUrl = "https://promos.makemytrip.com/Hotels_product/UGC/UGC-reward.png";
    private int reviewLevel1MaxAmount = 1000;
    private int reviewLevel1MaxPer = 5;
    private int reviewLevel1Count = 2;

    private int reviewLevel2MaxAmount = 1200;
    private int reviewLevel2MaxPer = 6;
    private int reviewLevel2Count = 9;

    private int reviewLevel3MaxAmount = 1400;
    private int reviewLevel3MaxPer = 7;
    private int reviewLevel3Count = 1;
    private FlexiCancelStaticDetail flexiCancelStaticDetail;
    private LinkedRatePlanStyle linkedRatePlanStyle;
    private String streetViewIconUrl;

    private BgGradient noCostEmiIconConfig;

    private Map<String, CardData> ihCashBackConfig;
    private IHGInclusionConfig ihgInclusionConfig;
    private ChatbotInfo chatbotInfo;
    private ChatbotInfo chatbotInfoV2;
    private Map<String, HooksData> chatbotInfoMediaV2;

    public Map<String, BottomSheet> getRequestToCallBackDataV2() {
        return requestToCallBackDataV2;
    }

    public void setRequestToCallBackDataV2(Map<String, BottomSheet> requestToCallBackDataV2) {
        this.requestToCallBackDataV2 = requestToCallBackDataV2;
    }

    public FlexiCancelStaticDetail getFlexiCancelStaticDetail() { return flexiCancelStaticDetail; }

    public void setFlexiCancelStaticDetail(FlexiCancelStaticDetail flexiCancelStaticDetail) {
        this.flexiCancelStaticDetail = flexiCancelStaticDetail;
    }

    public Map<String, List<String>> getChatGptReviewSummaryExp() {
        return chatGptReviewSummaryExp;
    }

    private Map<String, List<Filter>> preAppliedFiltersCityWise;

    public Map<String, List<Filter>> getPreAppliedFiltersCityWise() {
        return preAppliedFiltersCityWise;
    }

    public void setPreAppliedFiltersCityWise(Map<String, List<Filter>> preAppliedFiltersCityWise) {
        this.preAppliedFiltersCityWise = preAppliedFiltersCityWise;
    }

    public void setChatGptReviewSummaryExp(Map<String, List<String>> chatGptReviewSummaryExp) {
        this.chatGptReviewSummaryExp = chatGptReviewSummaryExp;
    }
    private String rtbTimeLine;

    public String getRtbTimeLine() {
        return rtbTimeLine;
    }

    public void setRtbTimeLine(String rtbTimeLine) {
        this.rtbTimeLine = rtbTimeLine;
    }

    public String getPersuasionConfigPWAString() {
        return persuasionConfigPWAString;
    }

    public void setPersuasionConfigPWAString(String persuasionConfigPWAString) {
        this.persuasionConfigPWAString = persuasionConfigPWAString;
    }

    public String getPersuasionConfigDesktopString() {
        return persuasionConfigDesktopString;
    }

    public void setPersuasionConfigDesktopString(String persuasionConfigDesktopString) {
        this.persuasionConfigDesktopString = persuasionConfigDesktopString;
    }

    public String getPersuasionConfigAndroidString() {
        return persuasionConfigAndroidString;
    }

    public void setPersuasionConfigAndroidString(String persuasionConfigAndroidString) {
        this.persuasionConfigAndroidString = persuasionConfigAndroidString;
    }

    public String getPersuasionConfigIOSString() {
        return persuasionConfigIOSString;
    }

    public void setPersuasionConfigIOSString(String persuasionConfigIOSString) {
        this.persuasionConfigIOSString = persuasionConfigIOSString;
    }

    public boolean isDisableUserServiceOtpValidation() {
        return disableUserServiceOtpValidation;
    }

    public void setDisableUserServiceOtpValidation(boolean disableUserServiceOtpValidation) {
        this.disableUserServiceOtpValidation = disableUserServiceOtpValidation;
    }


    public String getCommonUserServiceQuery() {
        return commonUserServiceQuery;
    }

    public void setCommonUserServiceQuery(String commonUserServiceQuery) {
        this.commonUserServiceQuery = commonUserServiceQuery;
    }

    public String getCommonUserServiceFKQuery() {
        return commonUserServiceFKQuery;
    }

    public void setCommonUserServiceFKQuery(String commonUserServiceFKQuery) {
        this.commonUserServiceFKQuery = commonUserServiceFKQuery;
    }

    public String getUserAndTravellerInfoReqBody() {
        return userAndTravellerInfoReqBody;
    }

    public void setUserAndTravellerInfoReqBody(String userAndTravellerInfoReqBody) {
        this.userAndTravellerInfoReqBody = userAndTravellerInfoReqBody;
    }

    public String getUserAndTravellerInfoFKReqBody() {
        return userAndTravellerInfoFKReqBody;
    }

    public void setUserAndTravellerInfoFKReqBody(String userAndTravellerInfoFKReqBody) {
        this.userAndTravellerInfoFKReqBody = userAndTravellerInfoFKReqBody;
    }

    public Map<String, Set<String>> getGroupToSegmentsMapping() {
        return groupToSegmentsMapping;
    }

    public void setGroupToSegmentsMapping(Map<String, Set<String>> groupToSegmentsMapping) {
        this.groupToSegmentsMapping = groupToSegmentsMapping;
    }

    public String getCtaAffiliateId() {
        return ctaAffiliateId;
    }

    public void setCtaAffiliateId(String ctaAffiliateId) {
        this.ctaAffiliateId = ctaAffiliateId;
    }

    public Map<String, String> getMmtTerminalPointAffiliateMapping() {
        return mmtTerminalPointAffiliateMapping;
    }

    public void setMmtTerminalPointAffiliateMapping(Map<String, String> mmtTerminalPointAffiliateMapping) {
        this.mmtTerminalPointAffiliateMapping = mmtTerminalPointAffiliateMapping;
    }

    public Map<String, List<String>> getMapHotelAndFlightCity() {
        return mapHotelAndFlightCity;
    }

    public void setMapHotelAndFlightCity(Map<String, List<String>> mapHotelAndFlightCity) {
        this.mapHotelAndFlightCity = mapHotelAndFlightCity;
    }

    public List<String> getDefaultSeqTopDomCities() {
        return defaultSeqTopDomCities;
    }

    public void setDefaultSeqTopDomCities(List<String> defaultSeqTopDomCities) {
        this.defaultSeqTopDomCities = defaultSeqTopDomCities;
    }

    public List<String> getDefaultSeqTopIntlCities() {
        return defaultSeqTopIntlCities;
    }

    public void setDefaultSeqTopIntlCities(List<String> defaultSeqTopIntlCities) {
        this.defaultSeqTopIntlCities = defaultSeqTopIntlCities;
    }

    public Set<String> getHydraAllSegmentsList() {
        return hydraAllSegmentsList;
    }

    public void setHydraAllSegmentsList(Set<String> hydraAllSegmentsList) {
        this.hydraAllSegmentsList = hydraAllSegmentsList;
    }

    public int getPastFlightBookingDays() {
        return pastFlightBookingDays;
    }

    public void setPastFlightBookingDays(int pastFlightBookingDays) {
        this.pastFlightBookingDays = pastFlightBookingDays;
    }

    public boolean isSkipFlightCityCheck() {
        return skipFlightCityCheck;
    }

    public void setSkipFlightCityCheck(boolean skipFlightCityCheck) {
        this.skipFlightCityCheck = skipFlightCityCheck;
    }

    public int getGetUserDetailsRetries() {
        return getUserDetailsRetries;
    }

    public void setGetUserDetailsRetries(int getUserDetailsRetries) {
        this.getUserDetailsRetries = getUserDetailsRetries;
    }

    public int getCreateGuestUserRetries() {
        return createGuestUserRetries;
    }

    public void setCreateGuestUserRetries(int createGuestUserRetries) {
        this.createGuestUserRetries = createGuestUserRetries;
    }

    public boolean isEnableUUID() {
        return enableUUID;
    }

    public void setEnableUUID(boolean enableUUID) {
        this.enableUUID = enableUUID;
    }

    public List<String> getUserServiceServers() {
        return userServiceServers;
    }

    public void setUserServiceServers(List<String> userServiceServers) {
        this.userServiceServers = userServiceServers;
    }

    public String getFilterRulesForCombinationFilter() {
        return filterRulesForCombinationFilter;
    }

    public void setFilterRulesForCombinationFilter(String filterRulesForCombinationFilter) {
        this.filterRulesForCombinationFilter = filterRulesForCombinationFilter;
    }

    public float getGreenHornExpPer() {
        return greenHornExpPer;
    }

    public void setGreenHornExpPer(float greenHornExpPer) {
        this.greenHornExpPer = greenHornExpPer;
    }

    public Map<String, String> getTrafficSourceCDFDomainMapping() {
        return trafficSourceCDFDomainMapping;
    }

    public void setTrafficSourceCDFDomainMapping(Map<String, String> trafficSourceCDFDomainMapping) {
        this.trafficSourceCDFDomainMapping = trafficSourceCDFDomainMapping;
    }

    public List<String> getTaxExclusiveCountryList() {
        return taxExclusiveCountryList;
    }

    public void setTaxExclusiveCountryList(List<String> taxExclusiveCountryList) {
        this.taxExclusiveCountryList = taxExclusiveCountryList;
    }

    public List<String> getRoiCityList() {
        return roiCityList;
    }

    public void setRoiCityList(List<String> roiCityList) {
        this.roiCityList = roiCityList;
    }

    public MissingSlotDetail getMissingSlotDetails() {
        return missingSlotDetails;
    }

    public void setMissingSlotDetails(MissingSlotDetail missingSlotDetails) {
        this.missingSlotDetails = missingSlotDetails;
    }

    public Map<String, Map<String, String>> getDefaultSeqMapping() {
        return defaultSeqMapping;
    }

    public void setDefaultSeqMapping(Map<String, Map<String, String>> defaultSeqMapping) {
        this.defaultSeqMapping = defaultSeqMapping;
    }

    public Map<String, Map<String, String>> getDefaultCorpSequencing() {
        return defaultCorpSequencing;
    }

    public void setDefaultCorpSequencing(Map<String, Map<String, String>> defaultCorpSequencing) {
        this.defaultCorpSequencing = defaultCorpSequencing;
    }

    public Map<String, SourceRegionSpecificDataConfig> getSourceRegionSpecificDataMapping() {
        return sourceRegionSpecificDataMapping;
    }

    public void setSourceRegionSpecificDataMapping(Map<String, SourceRegionSpecificDataConfig> sourceRegionSpecificDataMapping) {
        this.sourceRegionSpecificDataMapping = sourceRegionSpecificDataMapping;
    }

    public Map<String, Map<String, String>> getCollectionsCountMapping() {
        return collectionsCountMapping;
    }

    public void setCollectionsCountMapping(Map<String, Map<String, String>> collectionsCountMapping) {
        this.collectionsCountMapping = collectionsCountMapping;
    }

    public Map<Integer, Set<String>> getBudgetHotelCityConfig() {
        return budgetHotelCityConfig;
    }

    public void setBudgetHotelCityConfig(Map<Integer, Set<String>> budgetHotelCityConfig) {
        this.budgetHotelCityConfig = budgetHotelCityConfig;
    }

    public Map<String, Map<String, Map<String, Integer>>> getRatePlanDisplayLogic() {
        return ratePlanDisplayLogic;
    }

    public void setRatePlanDisplayLogic(Map<String, Map<String, Map<String, Integer>>> ratePlanDisplayLogic) {
        this.ratePlanDisplayLogic = ratePlanDisplayLogic;
    }

    public String getHydraAPIKey() {
        return hydraAPIKey;
    }

    public void setHydraAPIKey(String hydraAPIKey) {
        this.hydraAPIKey = hydraAPIKey;
    }

    public Map<String, String> getSrcConCurrMapping() {
        return srcConCurrMapping;
    }

    public void setSrcConCurrMapping(Map<String, String> srcConCurrMapping) {
        this.srcConCurrMapping = srcConCurrMapping;
    }

    public List<String> getEbableInboundExpDeviceList() {
        return ebableInboundExpDeviceList;
    }

    public void setEbableInboundExpDeviceList(List<String> ebableInboundExpDeviceList) {
        this.ebableInboundExpDeviceList = ebableInboundExpDeviceList;
    }

    public boolean isDisableSrcDesIntlExp() {
        return disableSrcDesIntlExp;
    }

    public void setDisableSrcDesIntlExp(boolean disableSrcDesIntlExp) {
        this.disableSrcDesIntlExp = disableSrcDesIntlExp;
    }

    public Long getThankYouPageExpiry() {
        return thankYouPageExpiry;
    }

    public void setThankYouPageExpiry(Long thankYouPageExpiry) {
        this.thankYouPageExpiry = thankYouPageExpiry;
    }

    public List<Double> getAndroidListingMapDensity() {
        return androidListingMapDensity;
    }

    public void setAndroidListingMapDensity(List<Double> androidListingMapDensity) {
        this.androidListingMapDensity = androidListingMapDensity;
    }

    public List<Double> getIosListingMapDensity() {
        return iosListingMapDensity;
    }

    public void setIosListingMapDensity(List<Double> iosListingMapDensity) {
        this.iosListingMapDensity = iosListingMapDensity;
    }

    public List<Double> getDesktopListingMapDensity() {
        return desktopListingMapDensity;
    }

    public void setDesktopListingMapDensity(List<Double> desktopListingMapDensity) {
        this.desktopListingMapDensity = desktopListingMapDensity;
    }

    public List<String> getHotelCategoryPriority() {
        return hotelCategoryPriority;
    }

    public void setHotelCategoryPriority(List<String> hotelCategoryPriority) {
        this.hotelCategoryPriority = hotelCategoryPriority;
    }

    public List<String> getAltAccoCategoryPriority() {
        return altAccoCategoryPriority;
    }

    public void setAltAccoCategoryPriority(List<String> altAccoCategoryPriority) {
        this.altAccoCategoryPriority = altAccoCategoryPriority;
    }

    public List<String> getSafetyCategories() {
        return safetyCategories;
    }

    public void setSafetyCategories(List<String> safetyCategories) {
        this.safetyCategories = safetyCategories;
    }

    public Map<String, String> getCategoryKeyToTextMap() {
        return categoryKeyToTextMap;
    }

    public void setCategoryKeyToTextMap(Map<String, String> categoryKeyToTextMap) {
        this.categoryKeyToTextMap = categoryKeyToTextMap;
    }

    public Map<String, String> getMandatoryChargesDisclaimerMap() {
        return mandatoryChargesDisclaimerMap;
    }

    public void setMandatoryChargesDisclaimerMap(Map<String, String> mandatoryChargesDisclaimerMap) {
        this.mandatoryChargesDisclaimerMap = mandatoryChargesDisclaimerMap;
    }

    public Set<String> getCompulsoryChargesTaxTypes() {
        return compulsoryChargesTaxTypes;
    }

    public void setCompulsoryChargesTaxTypes(Set<String> compulsoryChargesTaxTypes) {
        this.compulsoryChargesTaxTypes = compulsoryChargesTaxTypes;
    }

    public Map<String, CardData> getThankYouCards() {
        return thankYouCards;
    }

    public void setThankYouCards(Map<String, CardData> thankYouCards) {
        this.thankYouCards = thankYouCards;
    }

    public Map<String, CardData> getReviewPageCards() {
        return reviewPageCards;
    }

    public void setReviewPageCards(Map<String, CardData> reviewPageCards) {
        this.reviewPageCards = reviewPageCards;
    }

    public int getRatePlanMoreOptionsLimit() {
        return ratePlanMoreOptionsLimit;
    }

    public void setRatePlanMoreOptionsLimit(int ratePlanMoreOptionsLimit) {
        this.ratePlanMoreOptionsLimit = ratePlanMoreOptionsLimit;
    }


    public Map<String, String> getMealPlanMapPolyglot() {
        return mealPlanMapPolyglot;
    }

    public void setMealPlanMapPolyglot(Map<String, String> mealPlanMapPolyglot) {
        this.mealPlanMapPolyglot = mealPlanMapPolyglot;
    }

    public String getMySafetyData() {
        return mySafetyData;
    }

    public void setMySafetyData(String mySafetyData) {
        this.mySafetyData = mySafetyData;
    }

    public int getApLimitForInclusionIcons() {
        return apLimitForInclusionIcons;
    }

    public void setApLimitForInclusionIcons(int apLimitForInclusionIcons) {
        this.apLimitForInclusionIcons = apLimitForInclusionIcons;
    }

    public int getLosFosGCCNudgePersuasion() {
        return losFosGCCNudgePersuasion;
    }

    public void setLosFosGCCNudgePersuasion(int losFosGCCNudgePersuasion) {
        this.losFosGCCNudgePersuasion = losFosGCCNudgePersuasion;
    }

    public boolean isMealplanFilterEnable() {
        return mealplanFilterEnable;
    }

    public void setMealplanFilterEnable(boolean mealplanFilterEnable) {
        this.mealplanFilterEnable = mealplanFilterEnable;
    }

    public boolean isPartnerExclusiveFilterEnable() {
        return partnerExclusiveFilterEnable;
    }

    public void setPartnerExclusiveFilterEnable(boolean partnerExclusiveFilterEnable) {
        this.partnerExclusiveFilterEnable = partnerExclusiveFilterEnable;
    }

    public ValueStaysTooltip getMmtValueStaysTooltipDom() {
        return mmtValueStaysTooltipDom;
    }

    public void setMmtValueStaysTooltipDom(ValueStaysTooltip mmtValueStaysTooltipDom) {
        this.mmtValueStaysTooltipDom = mmtValueStaysTooltipDom;
    }

    public MyBizAssuredToolTip getMyBizAssuredTooltipDom() {
        return myBizAssuredTooltipDom;
    }

    public void setMyBizAssuredTooltipDom(MyBizAssuredToolTip myBizAssuredTooltipDom) {
        this.myBizAssuredTooltipDom = myBizAssuredTooltipDom;
    }

    public ValueStaysTooltip getMmtValueStaysTooltipIntl() {
        return mmtValueStaysTooltipIntl;
    }

    public void setMmtValueStaysTooltipIntl(ValueStaysTooltip mmtValueStaysTooltipIntl) {
        this.mmtValueStaysTooltipIntl = mmtValueStaysTooltipIntl;
    }


    public String getMobgenJsonBO() {
        return mobgenJsonBO;
    }

    public void setMobgenJsonBO(String mobgenJsonBO) {
        this.mobgenJsonBO = mobgenJsonBO;
    }

    public String getMobgenStringsBO() {
        return mobgenStringsBO;
    }

    public void setMobgenStringsBO(String mobgenStringsBO) {
        this.mobgenStringsBO = mobgenStringsBO;
    }

    public Map<String, HotelCategoryDataWeb> getHotelCategoryDataWebMapNew() {
        return hotelCategoryDataWebMapNew;
    }

    public void setHotelCategoryDataWebMapNew(Map<String, HotelCategoryDataWeb> hotelCategoryDataWebMapNew) {
        this.hotelCategoryDataWebMapNew = hotelCategoryDataWebMapNew;
    }

    public Map<String, Map<String, HotelCategoryData>> getHotelCategoryDataMap() {
        return hotelCategoryDataMap;
    }

    public void setHotelCategoryDataMap(Map<String, Map<String, HotelCategoryData>> hotelCategoryDataMap) {
        this.hotelCategoryDataMap = hotelCategoryDataMap;
    }

    public Map<String, String> getRtbCardConfigs() {
        return rtbCardConfigs;
    }

    public void setRtbCardConfigs(Map<String, String> rtbCardConfigs) {
        this.rtbCardConfigs = rtbCardConfigs;
    }

    public boolean isEnablePanCardCheck() {
        return enablePanCardCheck;
    }

    public void setEnablePanCardCheck(boolean enablePanCardCheck) {
        this.enablePanCardCheck = enablePanCardCheck;
    }

    public LuxeToolTip getLuxeToolTip() {
        return luxeToolTip;
    }

    public void setLuxeToolTip(LuxeToolTip luxeToolTip) {
        this.luxeToolTip = luxeToolTip;
    }

    public MySafetyTooltip getMysafetytooltip() {
        return mysafetytooltip;
    }

    public void setMysafetytooltip(MySafetyTooltip mysafetytooltip) {
        this.mysafetytooltip = mysafetytooltip;
    }

    public Map<String, String> getCardTitleMap() {
        return cardTitleMap;
    }

    public void setCardTitleMap(Map<String, String> cardTitleMap) {
        this.cardTitleMap = cardTitleMap;
    }

    public String getMandatoryChargesAlert() {
        return mandatoryChargesAlert;
    }

    public void setMandatoryChargesAlert(String mandatoryChargesAlert) {
        this.mandatoryChargesAlert = mandatoryChargesAlert;
    }

    public AllInclusiveCard getAllInclusiveCard() {
        return allInclusiveCard;
    }

    public void setAllInclusiveCard(AllInclusiveCard allInclusiveCard) {
        this.allInclusiveCard = allInclusiveCard;
    }

    public List<String> getSameDayRoomNames() {
        return sameDayRoomNames;
    }

    public void setSameDayRoomNames(List<String> sameDayRoomNames) {
        this.sameDayRoomNames = sameDayRoomNames;
    }

    public Map<String, Map<String, List<String>>> getSupplierToRateSegmentMapping() {
        return supplierToRateSegmentMapping;
    }

    public void setSupplierToRateSegmentMapping(Map<String, Map<String, List<String>>> supplierToRateSegmentMapping) {
        this.supplierToRateSegmentMapping = supplierToRateSegmentMapping;
    }

    public double getPayLaterCardLimit() {
        return payLaterCardLimit;
    }

    public void setPayLaterCardLimit(double payLaterCardLimit) {
        this.payLaterCardLimit = payLaterCardLimit;
    }

    public int getThresholdForSlashedAndDefaultHourPrice() {
        return thresholdForSlashedAndDefaultHourPrice;
    }

    public void setThresholdForSlashedAndDefaultHourPrice(int thresholdForSlashedAndDefaultHourPrice) {
        this.thresholdForSlashedAndDefaultHourPrice = thresholdForSlashedAndDefaultHourPrice;
    }


    public Map<String, DayUsePersuasion> getDayUseFunnelPersuasions() {
        return dayUseFunnelPersuasions;
    }

    public void setDayUseFunnelPersuasions(Map<String, DayUsePersuasion> dayUseFunnelPersuasions) {
        this.dayUseFunnelPersuasions = dayUseFunnelPersuasions;
    }

    public Map<String, FilterDetail> getLandingFilterConditions() {
        return landingFilterConditions;
    }

    public void setLandingFilterConditions(Map<String, FilterDetail> landingFilterConditions) {
        this.landingFilterConditions = landingFilterConditions;
    }

    public MyBizStaticCard getMyBizStaticCard() {
        return myBizStaticCard;
    }

    public void setMyBizStaticCard(MyBizStaticCard myBizStaticCard) {
        this.myBizStaticCard = myBizStaticCard;
    }

    public int getStarRatingMin() {
        return starRatingMin;
    }

    public void setStarRatingMin(int starRatingMin) {
        this.starRatingMin = starRatingMin;
    }

    public int getStarRatingMax() {
        return starRatingMax;
    }

    public void setStarRatingMax(int starRatingMax) {
        this.starRatingMax = starRatingMax;
    }

    public int getCorpBudgetPriceMin() {
        return corpBudgetPriceMin;
    }

    public void setCorpBudgetPriceMin(int corpBudgetPriceMin) {
        this.corpBudgetPriceMin = corpBudgetPriceMin;
    }

    public int getCorpBudgetPriceMax() {
        return corpBudgetPriceMax;
    }

    public void setCorpBudgetPriceMax(int corpBudgetPriceMax) {
        this.corpBudgetPriceMax = corpBudgetPriceMax;
    }

    public Map<String, Map<String, HotelCategoryData>> getHotelCategoryDataMapV2() {
        return hotelCategoryDataMapV2;
    }

    public void setHotelCategoryDataMapV2(Map<String, Map<String, HotelCategoryData>> hotelCategoryDataMapV2) {
        this.hotelCategoryDataMapV2 = hotelCategoryDataMapV2;
    }

    public int getHydraRetryCount() {
        return hydraRetryCount;
    }

    public void setHydraRetryCount(int hydraRetryCount) {
        this.hydraRetryCount = hydraRetryCount;
    }

    public int getScarcityThreshold() {
        return scarcityThreshold;
    }

    public String getDefaultSearchContext() {
        return defaultSearchContext;
    }

    public void setDefaultSearchContext(String defaultSearchContext) {
        this.defaultSearchContext = defaultSearchContext;
    }

    public Integer getTreelsFilterSize() {
        return treelsFilterSize;
    }

    public void setTreelsFilterSize(Integer treelsFilterSize) {
        this.treelsFilterSize = treelsFilterSize;
    }

    public void setScarcityThreshold(int scarcityThreshold) {
        this.scarcityThreshold = scarcityThreshold;
    }

    public Map<String, String> getCrossSellDataMap() {
        return crossSellDataMap;
    }

    public void setCrossSellDataMap(Map<String, String> crossSellDataMap) {
        this.crossSellDataMap = crossSellDataMap;
    }

    public Map<String, Map<String, Set<String>>> getOverridePokusConfig() {
        return overridePokusConfig;
    }

    public void setOverridePokusConfig(Map<String, Map<String, Set<String>>> overridePokusConfig) {
        this.overridePokusConfig = overridePokusConfig;
    }

    public String getNoCostEmiIconUrl() {
        return noCostEmiIconUrl;
    }

    public void setNoCostEmiIconUrl(String noCostEmiIconUrl) {
        this.noCostEmiIconUrl = noCostEmiIconUrl;
    }

    public boolean isShowSaleIcon() {
        return showSaleIcon;
    }

    public void setShowSaleIcon(boolean showSaleIcon) {
        this.showSaleIcon = showSaleIcon;
    }

    public Map<String, String> getReviewPagePersuasion() {
        return reviewPagePersuasion;
    }

    public void setReviewPagePersuasion(Map<String, String> reviewPagePersuasion) {
        this.reviewPagePersuasion = reviewPagePersuasion;
    }

    public String getRequestToCallBackData() {
        return requestToCallBackData;
    }

    public void setRequestToCallBackData(String requestToCallBackData) {
        this.requestToCallBackData = requestToCallBackData;
    }

    public String getRequestToCallHotelIdData() {
        return requestToCallHotelIdData;
    }

    public void setRequestToCallHotelIdData(String requestToCallHotelIdData) {
        this.requestToCallHotelIdData = requestToCallHotelIdData;
    }

    public String getRecommendedRoomProperties() {
        return recommendedRoomProperties;
    }

    public void setRecommendedRoomProperties(String recommendedRoomProperties) {
        this.recommendedRoomProperties = recommendedRoomProperties;
    }

    public int getSupplierDealsFilterCountLimit() {
        return supplierDealsFilterCountLimit;
    }

    public void setSupplierDealsFilterCountLimit(int supplierDealsFilterCountLimit) {
        this.supplierDealsFilterCountLimit = supplierDealsFilterCountLimit;
    }

    public String getGenericRestErrorMessage() {
        return genericRestErrorMessage;
    }

    public void setGenericRestErrorMessage(String genericRestErrorMessage) {
        this.genericRestErrorMessage = genericRestErrorMessage;
    }

    public String getBathroomReviewSummaryMediaTag() {
        return bathroomReviewSummaryMediaTag;
    }

    public void setBathroomReviewSummaryMediaTag(String bathroomReviewSummaryMediaTag) {
        this.bathroomReviewSummaryMediaTag = bathroomReviewSummaryMediaTag;
    }

    public String getRewardIconUrl() {
        return rewardIconUrl;
    }

    public void setRewardIconUrl(String rewardIconUrl) {
        this.rewardIconUrl = rewardIconUrl;
    }

    public int getReviewLevel1MaxAmount() {
        return reviewLevel1MaxAmount;
    }

    public void setReviewLevel1MaxAmount(int reviewLevel1MaxAmount) {
        this.reviewLevel1MaxAmount = reviewLevel1MaxAmount;
    }

    public int getReviewLevel1MaxPer() {
        return reviewLevel1MaxPer;
    }

    public void setReviewLevel1MaxPer(int reviewLevel1MaxPer) {
        this.reviewLevel1MaxPer = reviewLevel1MaxPer;
    }

    public int getReviewLevel1Count() {
        return reviewLevel1Count;
    }

    public void setReviewLevel1Count(int reviewLevel1Count) {
        this.reviewLevel1Count = reviewLevel1Count;
    }

    public int getReviewLevel2MaxAmount() {
        return reviewLevel2MaxAmount;
    }

    public void setReviewLevel2MaxAmount(int reviewLevel2MaxAmount) {
        this.reviewLevel2MaxAmount = reviewLevel2MaxAmount;
    }

    public int getReviewLevel2MaxPer() {
        return reviewLevel2MaxPer;
    }

    public void setReviewLevel2MaxPer(int reviewLevel2MaxPer) {
        this.reviewLevel2MaxPer = reviewLevel2MaxPer;
    }

    public int getReviewLevel2Count() {
        return reviewLevel2Count;
    }

    public void setReviewLevel2Count(int reviewLevel2Count) {
        this.reviewLevel2Count = reviewLevel2Count;
    }

    public int getReviewLevel3MaxAmount() {
        return reviewLevel3MaxAmount;
    }

    public void setReviewLevel3MaxAmount(int reviewLevel3MaxAmount) {
        this.reviewLevel3MaxAmount = reviewLevel3MaxAmount;
    }

    public int getReviewLevel3MaxPer() {
        return reviewLevel3MaxPer;
    }

    public void setReviewLevel3MaxPer(int reviewLevel3MaxPer) {
        this.reviewLevel3MaxPer = reviewLevel3MaxPer;
    }

    public int getReviewLevel3Count() {
        return reviewLevel3Count;
    }

    public void setReviewLevel3Count(int reviewLevel3Count) {
        this.reviewLevel3Count = reviewLevel3Count;
    }

    public Map<String, BottomSheet> getRequestToCallBackDataB2C() {
        return requestToCallBackDataB2C;
    }

    public void setRequestToCallBackDataB2C(Map<String, BottomSheet> requestToCallBackDataB2C) {
        this.requestToCallBackDataB2C = requestToCallBackDataB2C;
    }

    public Map<String, BottomSheet> getRequestToCallBackDataConfigForHighValue() {
        return requestToCallBackDataConfigForHighValue;
    }

    public void setRequestToCallBackDataConfigForHighValue(Map<String, BottomSheet> requestToCallBackDataConfigForHighValue) {
        this.requestToCallBackDataConfigForHighValue = requestToCallBackDataConfigForHighValue;
    }

    public BgGradient getNoCostEmiIconConfig() {
        return noCostEmiIconConfig;
    }

    public void setNoCostEmiIconConfig(BgGradient noCostEmiIconConfig) {
        this.noCostEmiIconConfig = noCostEmiIconConfig;
    }

    public PersuasionStyle getOneDayDealTimerStyleConfig() {
        return oneDayDealTimerStyleConfig;
    }

    public void setOneDayDealTimerStyleConfig(PersuasionStyle oneDayDealTimerStyleConfig) {
        this.oneDayDealTimerStyleConfig = oneDayDealTimerStyleConfig;
    }

    public List<String> getTranslateEnabledSupplierCodes() {
        return translateEnabledSupplierCodes;
    }

    public void setTranslateEnabledSupplierCodes(List<String> translateEnabledSupplierCodes) {
        this.translateEnabledSupplierCodes = translateEnabledSupplierCodes;
    }

    public boolean isUgcSummaryOrchEnabled() {
        return ugcSummaryOrchEnabled;
    }

    public void setUgcSummaryOrchEnabled(boolean ugcSummaryOrchEnabled) {
        this.ugcSummaryOrchEnabled = ugcSummaryOrchEnabled;
    }

    public boolean isUgcReviewsOrchEnabled() {
        return ugcReviewsOrchEnabled;
    }

    public void setUgcReviewsOrchEnabled(boolean ugcReviewsOrchEnabled) {
        this.ugcReviewsOrchEnabled = ugcReviewsOrchEnabled;
    }

    public String getHomeStayAwardUrl() {
        return homeStayAwardUrl;
    }

    public void setHomeStayAwardUrl(String homeStayAwardUrl) {
        this.homeStayAwardUrl = homeStayAwardUrl;
    }
}
