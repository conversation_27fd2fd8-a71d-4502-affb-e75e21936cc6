package com.mmt.hotels.clientgateway.pms;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.PostConstruct;

import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.request.FilterRules;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.propertymanager.config.PropertyManager;

@Component
public class CommonConfigHelper {


	@Value("${consul.enable}")
	private boolean consulFlag;

	@Autowired
	CommonConfigConsul commonConfigConsul;


	@Autowired
	private PropertyManager propManager;
	
	@Autowired
	private ObjectMapperUtil objectMapperUtil;
	
	private static final Logger logger = LoggerFactory.getLogger(CommonConfigHelper.class);
	
	private String persuasionConfigPWAString;

	private String defaultSearchContextConfig;

	private Integer treelsFilterSize;

	private String requestToCallBackDataConfig;

	private String persuasionConfigDesktopString;
	
	private String persuasionConfigAndroidString;
	private int hydraRetryCount;

	private String persuasionConfigIOSString;
	
	private Map<String, PersuasionConfig> persuasionConfigPWA;
	
	private Map<String,PersuasionConfig> persuasionConfigDesktop;
	
	private Map<String,PersuasionConfig> persuasionConfigAndroid;
	
	private Map<String,PersuasionConfig> persuasionConfigIOS;
	
	private String commonUserServiceRequestBody;
	
	private String commonUserServiceFKRequestBody;

	private String userAndTravellerInfoReqBody;

	private String userAndTravellerInfoFKReqBody;

	private String commonUserServiceAT;
	
	private Map<String,Set<String>> segmentGroupsMapping;
	
	private String ctaAffiliateId;
	
	private Map<String, String> terminalAffiliateMap;
	
	private Map<String, List<String>> mapHotelAndFlightCity;
	
	private Set<String> hydraAllSegmentsList;
	
	private Map<String, Map<String, String>> defaultSeqMapping;
	
    private Map<String, Map<String, String>> defaultCorpSeqMapping;
    
    private List<String> defaultSeqTopIntlCitiesList;
    
	private List<String> defaultSeqTopDomCitiesList;
	
	private int pastFlightBookingDays;
	
	private boolean skipFlightCityCheck;
	
	private List<FilterRules> filterRulesObj;

	public Map<String, Integer> getDefaultSearchContext() {
		return defaultSearchContext;
	}

	public void setDefaultSearchContext(Map<String, Integer> defaultSearchContext) {
		this.defaultSearchContext = defaultSearchContext;
	}

	private Map<String, Integer> defaultSearchContext;

	private float greenHornExpPer;
	
	private Map<String,String> cdfDomainMapping;
	
	private List<String> conList;

	private Map<String, Map<String, String>> collectionCountMapping;
	
	private String hydraAPIKey;
	
	private Map<String, String> currCityMap;
	
	private List<String> ebableInboundExpDeviceList;
	
	private boolean disableSrcDesIntlExp;

	private boolean mealplanFilterEnable;

	private boolean partnerExclusiveFilterEnable;

	private Map<String, String> crossSellDataMap;

	private Map<String, Set<String>> convFeePokusConfig;

	private Map<String, Map<String, Set<String>>> overridePokusConfig;

	private String genericRestErrorMessage;
	
	@PostConstruct
	public void init() {
		
		try {

			if(consulFlag){
				commonUserServiceAT = "UkDTHwkONmybDBh";
				persuasionConfigPWAString = commonConfigConsul.getPersuasionConfigPWAString();
				defaultSearchContextConfig = commonConfigConsul.getDefaultSearchContext();
				treelsFilterSize = commonConfigConsul.getTreelsFilterSize();
				requestToCallBackDataConfig = commonConfigConsul.getRequestToCallBackData();
				persuasionConfigDesktopString = commonConfigConsul.getPersuasionConfigDesktopString();
				persuasionConfigAndroidString = commonConfigConsul.getPersuasionConfigAndroidString();
				hydraRetryCount = commonConfigConsul.getHydraRetryCount();
				persuasionConfigIOSString = commonConfigConsul.getPersuasionConfigIOSString();
				commonUserServiceRequestBody = commonConfigConsul.getCommonUserServiceQuery();
				commonUserServiceFKRequestBody = commonConfigConsul.getCommonUserServiceFKQuery();
				userAndTravellerInfoReqBody = commonConfigConsul.getUserAndTravellerInfoReqBody();
				userAndTravellerInfoFKReqBody = commonConfigConsul.getUserAndTravellerInfoFKReqBody();
				populateSegmentGroupsMap(commonConfigConsul.getGroupToSegmentsMapping());
				ctaAffiliateId = commonConfigConsul.getCtaAffiliateId();
				terminalAffiliateMap = commonConfigConsul.getMmtTerminalPointAffiliateMapping();
				mapHotelAndFlightCity = commonConfigConsul.getMapHotelAndFlightCity();
				hydraAllSegmentsList = commonConfigConsul.getHydraAllSegmentsList();
				defaultSeqMapping = commonConfigConsul.getDefaultSeqMapping();
				defaultCorpSeqMapping = commonConfigConsul.getDefaultCorpSequencing();
				defaultSeqTopIntlCitiesList = commonConfigConsul.getDefaultSeqTopIntlCities();
				defaultSeqTopDomCitiesList = commonConfigConsul.getDefaultSeqTopDomCities();
				pastFlightBookingDays = commonConfigConsul.getPastFlightBookingDays();
				skipFlightCityCheck = commonConfigConsul.isSkipFlightCityCheck();
				String filterRuleString=commonConfigConsul.getFilterRulesForCombinationFilter();
				greenHornExpPer = commonConfigConsul.getGreenHornExpPer();
				cdfDomainMapping=commonConfigConsul.getTrafficSourceCDFDomainMapping();
				conList = commonConfigConsul.getTaxExclusiveCountryList();
				collectionCountMapping = commonConfigConsul.getCollectionsCountMapping();
				hydraAPIKey = commonConfigConsul.getHydraAPIKey();
				currCityMap = commonConfigConsul.getSrcConCurrMapping();
				ebableInboundExpDeviceList = commonConfigConsul.getEbableInboundExpDeviceList();
				disableSrcDesIntlExp = commonConfigConsul.isDisableSrcDesIntlExp();
				mealplanFilterEnable = commonConfigConsul.isMealplanFilterEnable();
				partnerExclusiveFilterEnable = commonConfigConsul.isPartnerExclusiveFilterEnable();
				crossSellDataMap = commonConfigConsul.getCrossSellDataMap();
				overridePokusConfig = commonConfigConsul.getOverridePokusConfig();
				genericRestErrorMessage = commonConfigConsul.getGenericRestErrorMessage();

				logger.debug("Fetching values from commonConfig consul");

				persuasionConfigPWA = objectMapperUtil.getObjectFromJsonWithType(persuasionConfigPWAString,
						new TypeReference<Map<String,PersuasionConfig>>() {
						}, DependencyLayer.CLIENTGATEWAY);

				persuasionConfigDesktop = objectMapperUtil.getObjectFromJsonWithType(persuasionConfigDesktopString,
						new TypeReference<Map<String,PersuasionConfig>>() {
						}, DependencyLayer.CLIENTGATEWAY);

				persuasionConfigAndroid = objectMapperUtil.getObjectFromJsonWithType(persuasionConfigAndroidString,
						new TypeReference<Map<String,PersuasionConfig>>() {
						}, DependencyLayer.CLIENTGATEWAY);

				persuasionConfigIOS = objectMapperUtil.getObjectFromJsonWithType(persuasionConfigIOSString,
						new TypeReference<Map<String,PersuasionConfig>>() {
						}, DependencyLayer.CLIENTGATEWAY);
				filterRulesObj= objectMapperUtil.getObjectFromJsonWithType(filterRuleString,
						new TypeReference<List<FilterRules>>() {
						}, DependencyLayer.CLIENTGATEWAY);

				defaultSearchContext = objectMapperUtil.getObjectFromJsonWithType(defaultSearchContextConfig,
						new TypeReference<Map<String,Integer>>() {
						}, DependencyLayer.CLIENTGATEWAY);
			}
			else {
				CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
				persuasionConfigPWAString = commonConfig.persuasionConfigPWAString();
				persuasionConfigDesktopString = commonConfig.persuasionConfigDesktopString();
				persuasionConfigAndroidString = commonConfig.persuasionConfigAndroidString();
				persuasionConfigIOSString = commonConfig.persuasionConfigIOSString();
				commonUserServiceRequestBody = commonConfig.commonUserServiceQuery();
				commonUserServiceFKRequestBody = commonConfig.commonUserServiceFKQuery();
				userAndTravellerInfoReqBody = commonConfig.userAndTravellerInfoReqBody();
				userAndTravellerInfoFKReqBody = commonConfig.userAndTravellerInfoFKReqBody();
				commonUserServiceAT = commonConfig.commonUserServiceAuthToken();
				populateSegmentGroupsMap(commonConfig.groupToSegmentsMapping());
				ctaAffiliateId = commonConfig.ctaAffiliateId();
				terminalAffiliateMap = commonConfig.mmtTerminalPointAffiliateMapping();
				mapHotelAndFlightCity = commonConfig.mapHotelAndFlightCity();
				hydraAllSegmentsList = commonConfig.hydraAllSegmentsList();
				defaultSeqMapping = commonConfig.defaultSeqMapping();
				defaultCorpSeqMapping = commonConfig.defaultCorpSequencing();
				defaultSeqTopIntlCitiesList = commonConfig.defaultSeqTopIntlCities();
				defaultSeqTopDomCitiesList = commonConfig.defaultSeqTopDomCities();
				pastFlightBookingDays = commonConfig.pastFlightBookingDays();
				skipFlightCityCheck = commonConfig.skipFlightCityCheck();
				String filterRuleString=commonConfig.filterRulesForCombinationFilter();
				greenHornExpPer = commonConfig.greenHornExpPer();
				cdfDomainMapping=commonConfig.trafficSourceCDFDomainMapping();
				conList = commonConfig.taxExcConList();
				collectionCountMapping = commonConfig.collectionsCountMapping();
				hydraAPIKey = commonConfig.hydraAPIKey();
				currCityMap = commonConfig.srcConCurrMapping();
				ebableInboundExpDeviceList = commonConfig.ebableInboundExpDeviceList();
				disableSrcDesIntlExp = commonConfig.disableSrcDesIntlExp();
				mealplanFilterEnable = commonConfig.mealplanFilterEnable();
				partnerExclusiveFilterEnable = commonConfig.partnerExclusiveFilterEnable();

//			commonConfig.addPropertyChangeListener("persuasionConfigPWAString", event -> {
//				persuasionConfigPWAString = commonConfig.persuasionConfigPWAString();
//			});
//
//			commonConfig.addPropertyChangeListener("persuasionConfigDesktopString", event -> {
//				persuasionConfigDesktopString = commonConfig.persuasionConfigDesktopString();
//			});
//
//			commonConfig.addPropertyChangeListener("persuasionConfigAndroidString", event -> {
//				persuasionConfigAndroidString = commonConfig.persuasionConfigAndroidString();
//			});
//
//			commonConfig.addPropertyChangeListener("persuasionConfigIOSString", event -> {
//				persuasionConfigIOSString = commonConfig.persuasionConfigIOSString();
//			});
//
//			commonConfig.addPropertyChangeListener("sourceRegionSpecificDataMapping", event -> {
//				sourceRegionSpecificDataMapping = commonConfig.sourceRegionSpecificDataMapping();
//			});
//
//			commonConfig.addPropertyChangeListener("hydraAllSegmentsList", event -> {
//				hydraAllSegmentsList = commonConfig.hydraAllSegmentsList();
//			});

				persuasionConfigPWA = objectMapperUtil.getObjectFromJsonWithType(persuasionConfigPWAString,
						new TypeReference<Map<String,PersuasionConfig>>() {
						}, DependencyLayer.CLIENTGATEWAY);

				persuasionConfigDesktop = objectMapperUtil.getObjectFromJsonWithType(persuasionConfigDesktopString,
						new TypeReference<Map<String,PersuasionConfig>>() {
						}, DependencyLayer.CLIENTGATEWAY);

				persuasionConfigAndroid = objectMapperUtil.getObjectFromJsonWithType(persuasionConfigAndroidString,
						new TypeReference<Map<String,PersuasionConfig>>() {
						}, DependencyLayer.CLIENTGATEWAY);

				persuasionConfigIOS = objectMapperUtil.getObjectFromJsonWithType(persuasionConfigIOSString,
						new TypeReference<Map<String,PersuasionConfig>>() {
						}, DependencyLayer.CLIENTGATEWAY);
				filterRulesObj= objectMapperUtil.getObjectFromJsonWithType(filterRuleString,
						new TypeReference<List<FilterRules>>() {
						}, DependencyLayer.CLIENTGATEWAY);

			}

		} catch (JsonParseException e) {
			logger.error("Error Ocurred in initialising config: ", e);
		}
	}

	private void populateSegmentGroupsMap(Map<String, Set<String>> groupToSegmentsMapping) {
		Map<String, Set<String>> tempMap = new HashMap<>();
		for(Map.Entry<String,Set<String>> entry : groupToSegmentsMapping.entrySet()){
			String group = entry.getKey();
			Set<String> segments =  entry.getValue();			
			for(String hydraSegment : segments){
				Set<String> groups = tempMap.get(hydraSegment);
				if(groups == null){
					groups = new HashSet<>();
				}
				groups.add(group);
				tempMap.put(hydraSegment, groups);
			}
		}
		
		segmentGroupsMapping = tempMap;
	}

	public Map<String,PersuasionConfig> getPersuasionConfigs(String bookingDevice) {
		switch(bookingDevice) {
			case "PWA": return persuasionConfigPWA;
			case "DESKTOP": return persuasionConfigDesktop;
			case "ANDROID": return persuasionConfigAndroid;
			case "IOS": return persuasionConfigIOS;
			default: return null;
		}
	}
	
	public String getCommonUserServiceRequestBody() {
		return commonUserServiceRequestBody;
	}
	
	public String getCommonUserServiceFKRequestBody() {
		return commonUserServiceFKRequestBody;
	}

	public String getUserAndTravellerInfoReqBody() {
		return userAndTravellerInfoReqBody;
	}

	public String getUserAndTravellerInfoFKReqBody() {
		return userAndTravellerInfoFKReqBody;
	}

	public String getCommonUserServiceAuthToken() {
		return commonUserServiceAT;
	}
	
	public Map<String, Set<String>> getSegmentGroupsMapping() {
		return segmentGroupsMapping;
	}
	
	public Set<String> getHydraAllSegmentsList() {
		return hydraAllSegmentsList;
	}
	
	public Map<String, Map<String, String>> getDefaultSeqMapping() {
		return defaultSeqMapping;
	}
	
	public Map<String, Map<String, String>> getDefaultCorpSeqMapping() {
		return defaultCorpSeqMapping;
	}
	
	public List<String> getDefaultSeqTopIntlCitiesList() {
		return defaultSeqTopIntlCitiesList;
	}
	
	public List<String> getDefaultSeqTopDomCitiesList() {
		return defaultSeqTopDomCitiesList;
	}
	
	public String getCtaAffiliateId() {
		return ctaAffiliateId;
	}
	
	public Map<String, String> getTerminalAffiliateMap() {
		return terminalAffiliateMap;
	}
	
	public Map<String, List<String>> getMapHotelAndFlightCityt() {
		return mapHotelAndFlightCity;
	}
	
	public int getPastFlightBookingDays() {
		return pastFlightBookingDays;
	}
	
	public boolean isSkipFlightCityCheck() {
		return skipFlightCityCheck;
	}

	public List<FilterRules> getFilterRuleObject() {
		return filterRulesObj;
	}
	
	public float getGreenHornExpPer() {
		return greenHornExpPer;
	}
	
	public Map<String, String> getCdfDomainMapping() {
		return cdfDomainMapping;
	}
	
	public List<String> getConList() {
		return conList;
	}

	public Map<String, Map<String, String>> getCollectionCountMapping() {
		return collectionCountMapping;
	}
	
	public String getHydraAPIKey() {
		return hydraAPIKey;
	}

	public Map<String, String> getCurrCityMap() {
		return currCityMap;
	}

	public int getHydraRetryCount() {
		return hydraRetryCount;
	}

	public void setHydraRetryCount(int hydraRetryCount) {
		this.hydraRetryCount = hydraRetryCount;
	}

	public List<String> getEbableInboundExpDeviceList() {
		return ebableInboundExpDeviceList;
	}

	public boolean isDisableSrcDesIntlExp() {
		return disableSrcDesIntlExp;
	}

	public Integer getTreelsFilterSize() {
		return treelsFilterSize;
	}

	public void setTreelsFilterSize(Integer treelsFilterSize) {
		this.treelsFilterSize = treelsFilterSize;
	}

	public Map<String, String> getCrossSellDataMap() {
		return crossSellDataMap;
	}

	public void setCrossSellDataMap(Map<String, String> crossSellDataMap) {
		this.crossSellDataMap = crossSellDataMap;
	}


	public Map<String, Map<String, Set<String>>> getOverridePokusConfig() {
		return overridePokusConfig;
	}

	public void setOverridePokusConfig(Map<String, Map<String, Set<String>>> overridePokusConfig) {
		this.overridePokusConfig = overridePokusConfig;
	}
}
