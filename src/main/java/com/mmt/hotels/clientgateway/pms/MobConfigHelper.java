package com.mmt.hotels.clientgateway.pms;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.MobConfigPropsConsul;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.response.MobgenJsonBO;
import com.mmt.hotels.clientgateway.response.MobgenStringsBO;
import com.mmt.hotels.clientgateway.response.moblanding.CardAction;
import com.mmt.hotels.clientgateway.response.moblanding.CardInfo;
import com.mmt.hotels.clientgateway.response.searchHotels.FilterConditions;
import com.mmt.hotels.clientgateway.restexecutors.PolyglotRestExecutor;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.response.cbresponse.ErrorResponse;
import com.mmt.hotels.clientgateway.response.moblanding.GenericErrorEntity;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.util.ClientBackendUtility;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.propertymanager.config.PropertyManager;
import com.fasterxml.jackson.databind.JsonNode;

@Component
public class MobConfigHelper {

    @Value("${consul.enable}")
    private boolean consulFlag;

    @Autowired
    MobConfigPropsConsul mobConfigPropsConsul;

    @Autowired
    CommonConfigConsul commonConfigConsul;

	@Autowired
    private PropertyManager propManager;
	
	@Autowired
	private CommonHelper commonHelper;
	
	@Autowired
	private ObjectMapperUtil objectMapperUtil;

    @Autowired
    PolyglotRestExecutor polyglotRestExecutor;

    @Autowired
    CommonResponseTransformer commonResponseTransformer;

    @Autowired
    CacheManager cacheManager;

    @Autowired
    PolyglotHelper polyglotHelper;

    private MobConfigProps mobConfigProps;

    private Map<String, JsonNode> configJsonNodeMap;

    private List<String> mobConfigPropsVariants = Arrays.asList("A", "B", "C", "D", "default");

    private JsonNode mobgenJsonNodeEnglish;
    private JsonNode mobgenJsonNodeHindi;
    private JsonNode mobgenJsonNodeArabic;

    private Map<String, SourceRegionSpecificDataConfig> sourceRegionSpecificDataMapping;

	private Map<String,Integer> corpSectionListCount;
    private Map<String,Integer> myPartnerSectionListCount;

	private static final Logger logger = LoggerFactory.getLogger(MobConfigHelper.class);
	
    public enum HtlMobConfigError {
        PMS_VERSION_ERROR, PMS_CONFIG_READ_ERROR
    }

    private String mobgenJsonBO;
    private Map<String, Map<String, MobgenJsonBO>> mobgenJsonBOMapEnglish = null;
    private Map<String, Map<String, MobgenJsonBO>> mobgenJsonBOMapHindi = null;
    private Map<String, Map<String, MobgenJsonBO>> mobgenJsonBOMapArabic = null;

    private String mobgenStringsBO;
    private Map<String, MobgenStringsBO> mobgenStringsBOMapEnglish = null;
    private Map<String, MobgenStringsBO> mobgenStringsBOMapHindi = null;
    private Map<String, MobgenStringsBO> mobgenStringsBOMapArabic = null;

    JsonNode configNodePolyglotEnglish;
    JsonNode configNodePolyglotHindi;
    JsonNode configNodePolyglotArabic;

    private Map<String, JsonNode> configJsonNodeMapEnglish;
    private Map<String, JsonNode> configJsonNodeMapHindi;
    private Map<String, JsonNode> configJsonNodeMapArabic;

	@PostConstruct
	public void init() {
		try {
            if(consulFlag){

                mobgenJsonBO = commonConfigConsul.getMobgenJsonBO();
                populateMobgenJsonBOMap(mobgenJsonBO);

                mobgenStringsBO = commonConfigConsul.getMobgenStringsBO();
                populateMobgenStringsBOMap(mobgenStringsBO);

                sourceRegionSpecificDataMapping = mobConfigPropsConsul.getSourceRegionSpecificDataMapping();
                corpSectionListCount = mobConfigPropsConsul.getCorpSectionListCount();
                myPartnerSectionListCount=mobConfigPropsConsul.getMyPartnerSectionListCount();
                logger.debug("Fetching values from mobConfigProps consul");

                populateMobgenJsonNodeAndMergeMobgenStringNode();
                Cache cache = cacheManager.getCache(Constants.MOBGEN_CACHE);
                configJsonNodeMapHindi = new HashMap<>();
                configJsonNodeMapEnglish = new HashMap<>();
                configJsonNodeMapArabic = new HashMap<>();
                populatePolyglotLanguageMapsToConfigNodes(cache);
                populateConfigJsonNodeMaps();
            }
            else{
                CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);

                mobgenJsonBO = commonConfig.mobgenJsonBO();
                populateMobgenJsonBOMap(mobgenJsonBO);

                mobgenStringsBO = commonConfig.mobgenStringsBO();
                populateMobgenStringsBOMap(mobgenStringsBO);

                mobConfigProps = propManager.getProperty("mobConfigProps", MobConfigProps.class);
                sourceRegionSpecificDataMapping = mobConfigProps.SourceRegionSpecificDataMapping();
                corpSectionListCount = mobConfigProps.corpSectionListCount();
                myPartnerSectionListCount=mobConfigProps.myPartnerSectionListCount();
                populateMobgenJsonNodeAndMergeMobgenStringNode();
                Cache cache = cacheManager.getCache(Constants.MOBGEN_CACHE);
                configJsonNodeMapHindi = new HashMap<>();
                configJsonNodeMapEnglish = new HashMap<>();
                configJsonNodeMapArabic = new HashMap<>();
                populatePolyglotLanguageMapsToConfigNodes(cache);
                populateConfigJsonNodeMaps();
            }
		}catch (Exception e) {
			logger.error("Error Ocurred in initialising mob config: ", e);
		}
	}

    private void populateMobgenJsonBOMap(String mobgenJsonBO) {
        try {
            mobgenJsonBOMapHindi = objectMapperUtil.getObjectFromJsonWithType(mobgenJsonBO,
                    new TypeReference<Map<String, Map<String, MobgenJsonBO>>>() {
                    }, DependencyLayer.CLIENTGATEWAY);
            polyglotHelper.translateMobgenJsonBO(mobgenJsonBOMapHindi, "hin");

            mobgenJsonBOMapEnglish = objectMapperUtil.getObjectFromJsonWithType(mobgenJsonBO,
                    new TypeReference<Map<String, Map<String, MobgenJsonBO>>>() {
                    }, DependencyLayer.CLIENTGATEWAY);
            polyglotHelper.translateMobgenJsonBO(mobgenJsonBOMapEnglish, "eng");

            mobgenJsonBOMapArabic = objectMapperUtil.getObjectFromJsonWithType(mobgenJsonBO,
                    new TypeReference<Map<String, Map<String, MobgenJsonBO>>>() {
                    }, DependencyLayer.CLIENTGATEWAY);
            polyglotHelper.translateMobgenJsonBO(mobgenJsonBOMapArabic, "ara");
        } catch (Exception e) {
            logger.warn("Error in buildMobgenJsonBO");
        }
    }

    private void populateMobgenStringsBOMap(String mobgenStringsBO) {
        try {
            mobgenStringsBOMapEnglish = objectMapperUtil.getObjectFromJsonWithType(mobgenStringsBO,
                    new TypeReference<Map<String, MobgenStringsBO>>() {
                    }, DependencyLayer.CLIENTGATEWAY);
            polyglotHelper.translateMobgenStringsBO(mobgenStringsBOMapEnglish, "eng");

            mobgenStringsBOMapHindi = objectMapperUtil.getObjectFromJsonWithType(mobgenStringsBO,
                    new TypeReference<Map<String, MobgenStringsBO>>() {
                    }, DependencyLayer.CLIENTGATEWAY);
            polyglotHelper.translateMobgenStringsBO(mobgenStringsBOMapHindi, "hin");

            mobgenStringsBOMapArabic = objectMapperUtil.getObjectFromJsonWithType(mobgenStringsBO,
                    new TypeReference<Map<String, MobgenStringsBO>>() {
                    }, DependencyLayer.CLIENTGATEWAY);
            polyglotHelper.translateMobgenStringsBO(mobgenStringsBOMapArabic, "ara");
        } catch (Exception e) {
            logger.warn("Error in buildMobgenStringBO");
        }
    }

    public Map<String, Map<String, MobgenJsonBO>> getMobgenJsonBO(String lang) {
        if(lang.equalsIgnoreCase("hin"))
            return mobgenJsonBOMapHindi;
        else if(lang.equalsIgnoreCase("ara"))
            return mobgenJsonBOMapArabic;
        return mobgenJsonBOMapEnglish;
    }

    public Map<String, MobgenStringsBO> getMobgenStringsBO(String lang) {
        if(lang.equalsIgnoreCase("hin"))
            return mobgenStringsBOMapHindi;
        else if(lang.equalsIgnoreCase("ara"))
            return mobgenStringsBOMapArabic;
        return mobgenStringsBOMapEnglish;
    }

    public void populatePolyglotLanguageMapsToConfigNodes(Cache cache) {
        ObjectMapper mapper = new ObjectMapper();
        configNodePolyglotEnglish = populateLanguageNode(cache, "eng", configNodePolyglotEnglish, mapper);
        configNodePolyglotHindi = populateLanguageNode(cache, "hin", configNodePolyglotHindi, mapper);
        configNodePolyglotArabic = populateLanguageNode(cache, "ara", configNodePolyglotArabic, mapper);
    }

    public JsonNode populateLanguageNode(Cache cache, String lang, JsonNode configNodePolyglot, ObjectMapper mapper) {
        Map<String, String> polyMap = new HashMap<>();

        if (cache.get(lang) != null) {
            polyMap = cache.get(lang, Map.class);
        }

        configNodePolyglot = mapper.valueToTree(polyMap);
        configNodePolyglot = mergeMobgenJsonNode(configNodePolyglot, lang);
        return configNodePolyglot;
    }

    public JsonNode mergeMobgenJsonNode(JsonNode configNodePolyglot, String lang) {
        if (configNodePolyglot != null && getMobgenJsonNodeMergedWithMobgenStringNode(lang) != null) {
            merge(configNodePolyglot, getMobgenJsonNodeMergedWithMobgenStringNode(lang));
        } else if (getMobgenJsonNodeMergedWithMobgenStringNode(lang) != null) {
            configNodePolyglot = getMobgenJsonNodeMergedWithMobgenStringNode(lang);
        }
        return configNodePolyglot;
    }

    public void populateConfigJsonNodeMaps() {
        configJsonNodeMapHindi = populateConfigNodeMap(configJsonNodeMapHindi, configNodePolyglotHindi, "hin");
        configJsonNodeMapEnglish = populateConfigNodeMap(configJsonNodeMapEnglish, configNodePolyglotEnglish, "eng");
        configJsonNodeMapArabic = populateConfigNodeMap(configJsonNodeMapArabic, configNodePolyglotArabic, "ara");
    }

    public JsonNode getConfigJsonForLangVariant(String lang, String variant) {
        Map<String, JsonNode> configJsonNodeMap;
        if (lang.equalsIgnoreCase("hin")) {
            configJsonNodeMap = configJsonNodeMapHindi;
        }
        else if (lang.equalsIgnoreCase("ara")){
            configJsonNodeMap = configJsonNodeMapArabic;
        }
        else{
            configJsonNodeMap = configJsonNodeMapEnglish;
        }

        JsonNode configNode = configJsonNodeMap.containsKey(variant) ? configJsonNodeMap.get(variant) : configJsonNodeMap.get("default");
        return configNode;
    }

	public Map<String, Integer> getCorpSectionListCount() {
        return corpSectionListCount;
    }
    public Map<String, Integer> getMyPartnerSectionListCount() {
        return myPartnerSectionListCount;
    }

	public Map<String, SourceRegionSpecificDataConfig> getSourceRegionSpecificDataMapping() {
		return sourceRegionSpecificDataMapping;
	}

    private String getCurrentVersionOfConfig(String variant){
        if(consulFlag){
            switch (variant) {
                case "A":
                    return mobConfigPropsConsul.getVersionA();
                case "B":
                    return mobConfigPropsConsul.getVersionB();
                case "C":
                    return mobConfigPropsConsul.getVersionC();
                case "D":
                    return mobConfigPropsConsul.getVersionD();
                default:
                    return mobConfigPropsConsul.getVersion();
            }
        }
        else{
            MobConfigProps mobConfigProps  = propManager.getProperty("mobConfigProps", MobConfigProps.class);
            switch (variant) {
                case "A":
                    return mobConfigProps.versionA();
                case "B":
                    return mobConfigProps.versionB();
                case "C":
                    return mobConfigProps.versionC();
                case "D":
                    return mobConfigProps.versionD();
                default:
                    return mobConfigProps.version();
            }
        }
    }
    
    private boolean isVersionChanged(String versionOnClient,String variant){
        String pmsJsonVersion = getCurrentVersionOfConfig(variant);
        double pmsVersion;
        double clientVersion;

        if(StringUtils.isBlank(versionOnClient) || StringUtils.isBlank(pmsJsonVersion)){
            return true;
        }
        try {
            pmsVersion = Double.valueOf(pmsJsonVersion);
            clientVersion = Double.valueOf(versionOnClient);
        } catch (NumberFormatException e){
        	logger.error("Number Format Exception in isVersionChanged() in HotelMobileConfigHelper", e);
            return true;
        }
        return Double.compare(pmsVersion, clientVersion) > 0;
    }
    
	private String getConfigJson(String variant) {

        if(consulFlag){
            switch (variant) {
                case "A":
                    return mobConfigPropsConsul.getConfigJsonA();
                case "B":
                    return mobConfigPropsConsul.getConfigJsonB();
                case "C":
                    return mobConfigPropsConsul.getConfigJsonC();
                case "D":
                    return mobConfigPropsConsul.getConfigJsonD();
                default:
                    return mobConfigPropsConsul.getConfigJson();
            }
        }
        else {
            MobConfigProps mobGeneralConfigProps = propManager.getProperty("mobConfigProps", MobConfigProps.class);
            switch (variant) {
                case "A":
                    return mobGeneralConfigProps.configJsonA();
                case "B":
                    return mobGeneralConfigProps.configJsonB();
                case "C":
                    return mobGeneralConfigProps.configJsonC();
                case "D":
                    return mobGeneralConfigProps.configJsonD();
                default:
                    return mobGeneralConfigProps.configJson();
            }
        }


	}

    public JsonNode getConfigJsonNode(String variant, JsonNode configNodePolyglot, String lang) throws JsonParseException {
        String configJson = getConfigJson(variant);
        if(configJson == null)
            return null;
        JsonNode configNode = objectMapperUtil.getObjectFromJson(configJson, JsonNode.class, DependencyLayer.CLIENTGATEWAY);
        /**
         * Polyglot dependency will not be resolved by merge function as merge function will add the nodes at base level.
         * This function is called when we want to replace nodes at deeper level of json explicitly.
         */
        polyglotHelper.translateConfigNodesFromPolyglot(configNode, lang);
        if (configNode!=null && configNodePolyglot!=null){
            merge(configNode, configNodePolyglot);
        }
        return configNode;
    }

    private HotelMobConfig getBaseHotelMobileConfig(String version,String variant){
        return new HotelMobConfig.Builder()
                .success(true)
                .isNewConfig(false)
                .responseErrors(null)
                .version(version)
                .variant(StringUtils.isEmpty(variant) ? null : variant)
                .build();
    }

    public Map<String, JsonNode> populateConfigNodeMap(Map<String, JsonNode> configJsonNodeMap, JsonNode configNodePolyglot, String lang){
        for(String variant: mobConfigPropsVariants){
            try {
                configJsonNodeMap.put(variant, getConfigJsonNode(variant, configNodePolyglot, lang));
            } catch (JsonParseException e) {
                logger.error("Exception {}", e.getMessage());
            }
        }
        return configJsonNodeMap;
    }

    private HotelMobConfig getHotelMobileConfigFromPMS(String variant) throws JsonParseException {
        String pmsVersion = getCurrentVersionOfConfig(variant);
        if (StringUtils.isBlank(pmsVersion)) {
        	return getErrorMobileConfig(HtlMobConfigError.PMS_VERSION_ERROR);
        }

        String lang = MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue());
        JsonNode configJsonNodeOfVarAndLang = getConfigJsonForLangVariant(lang, variant);

        if (configJsonNodeOfVarAndLang == null) {
            return getErrorMobileConfig(HtlMobConfigError.PMS_CONFIG_READ_ERROR);
        }

        HotelMobConfig hotelMobileConfig = getBaseHotelMobileConfig(pmsVersion,variant);

        hotelMobileConfig.setConfigJson(configJsonNodeOfVarAndLang);

        hotelMobileConfig.setIsNewConfig(true);

        return hotelMobileConfig;
    }

    private JsonNode getConfigJsonForVariant(String variant) {
        if(configJsonNodeMap.containsKey(variant))
            return configJsonNodeMap.get(variant);
        return configJsonNodeMap.get("default");
    }

    public void populateMobgenJsonNodeAndMergeMobgenStringNode(){
        ObjectMapper mapper = new ObjectMapper();
        Map<String, Map<String, MobgenJsonBO>> mobgenJsonMapEnglish = getMobgenJsonBO("eng");
        mobgenJsonNodeEnglish = mapper.valueToTree(mobgenJsonMapEnglish);
        Map<String, MobgenStringsBO> mobgenStringsMapEnglish = getMobgenStringsBO("eng");
        JsonNode mobgenStringNodeEnglish = mapper.valueToTree(mobgenStringsMapEnglish);
        merge(mobgenJsonNodeEnglish, mobgenStringNodeEnglish);

        Map<String, Map<String, MobgenJsonBO>> mobgenJsonMapHindi = getMobgenJsonBO("hin");
        mobgenJsonNodeHindi = mapper.valueToTree(mobgenJsonMapHindi);
        Map<String, MobgenStringsBO> mobgenStringsMapHindi = getMobgenStringsBO("hin");
        JsonNode mobgenStringNodeHindi = mapper.valueToTree(mobgenStringsMapHindi);
        merge(mobgenJsonNodeHindi, mobgenStringNodeHindi);

        Map<String, Map<String, MobgenJsonBO>> mobgenJsonMapArabic = getMobgenJsonBO("ara");
        mobgenJsonNodeArabic = mapper.valueToTree(mobgenJsonMapArabic);
        Map<String, MobgenStringsBO> mobgenStringsMapArabic = getMobgenStringsBO("ara");
        JsonNode mobgenStringNodeArabic = mapper.valueToTree(mobgenStringsMapArabic);
        merge(mobgenJsonNodeArabic, mobgenStringNodeArabic);
    }

    public JsonNode getMobgenJsonNodeMergedWithMobgenStringNode(String lang){
        if(lang.equalsIgnoreCase("hin"))
            return mobgenJsonNodeHindi;
        else if(lang.equalsIgnoreCase("ara"))
            return mobgenJsonNodeArabic;
        return mobgenJsonNodeEnglish;
    }

    public static JsonNode merge(JsonNode mainNode, JsonNode updateNode) {

        Iterator<String> fieldNames = updateNode.fieldNames();
        while (fieldNames.hasNext()) {

            String fieldName = fieldNames.next();
            JsonNode jsonNode = mainNode.get(fieldName);

                if (mainNode instanceof ObjectNode) {
                    // Overwrite field
                    JsonNode value = updateNode.get(fieldName);
                    ((ObjectNode) mainNode).put(fieldName, value);
                }


        }

        return mainNode;
    }

    
    public String getHotelMobConfigAsString(String versionOnClient,String variant, Map<String,String> httpHeaderMap) throws JsonParseException{
        HotelMobConfig configResponse;
        String responseString = null;
        variant = validateAndUpdateTheVariant(variant);

        if(isVersionChanged(versionOnClient,variant)){
            configResponse = getHotelMobileConfigFromPMS(variant);
        }else{
            configResponse = getBaseHotelMobileConfig(getCurrentVersionOfConfig(variant),variant);
        }
        
        getSrcRegionSpecificData(configResponse, httpHeaderMap);
        try{
            responseString = objectMapperUtil.getJsonFromObject(configResponse, DependencyLayer.CLIENTGATEWAY);
        } catch(JsonParseException e){
             responseString = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
             logger.error(e.toString());
        }
    	return responseString;
    }

    public String validateAndUpdateTheVariant(String variant){
        if (variant == null){
            return  "";
        }
        if(variant.equalsIgnoreCase("A") || variant.equalsIgnoreCase("default")|| variant.equalsIgnoreCase("B") || variant.equalsIgnoreCase("C") || variant.equalsIgnoreCase("D"))
        {
            return  variant;
        }else {
           return  "";
        }
    }

    /**
     * Get source region specific data like currency, domain, language etc
     * @param configResponse, httpHeaderMap
     * @return
     */
    public void getSrcRegionSpecificData(HotelMobConfig configResponse, Map<String,String> httpHeaderMap) {
   	 try {
         Map<String, String> countryAndCity = commonHelper.getCountryAndCityCodeFromHeader(httpHeaderMap);
         String countryCode = countryAndCity.get(Constants.HEADER_COUNTRY_CODE);

            if(consulFlag){
                if(StringUtils.isNotBlank(countryCode) && mobConfigPropsConsul != null &&
                        MapUtils.isNotEmpty(mobConfigPropsConsul.getSourceRegionSpecificDataMapping())) {
                    SourceRegionSpecificDataConfig regionData = new SourceRegionSpecificDataConfig();
                    logger.debug("Fetching values from mobConfigProps consul");
                    if(mobConfigPropsConsul.getSourceRegionSpecificDataMapping().containsKey(countryCode.toUpperCase())) {
                        regionData = mobConfigPropsConsul.getSourceRegionSpecificDataMapping().get(countryCode);
                    }else {
                        regionData = mobConfigPropsConsul.getSourceRegionSpecificDataMapping().get(Constants.DEFAULT_DOMAIN);
                    }
                    configResponse.setCurrency(regionData.getCurrency());
                    configResponse.setSiteDomain(regionData.getSiteDomain());
                    configResponse.setLanguage(regionData.getLanguage());
                }else {
                    configResponse.setCurrency(Constants.DEFAULT_CUR_INR);
                    configResponse.setSiteDomain(Constants.DEFAULT_SITE_DOMAIN);
                    configResponse.setLanguage(Constants.ENGLISH);
                }
            }
            else{
                if (StringUtils.isNotBlank(countryCode) && mobConfigProps != null &&
                        MapUtils.isNotEmpty(mobConfigProps.SourceRegionSpecificDataMapping())) {
                    SourceRegionSpecificDataConfig regionData = new SourceRegionSpecificDataConfig();
                    if (mobConfigProps.SourceRegionSpecificDataMapping().containsKey(countryCode.toUpperCase())) {
                        regionData = mobConfigProps.SourceRegionSpecificDataMapping().get(countryCode);
                    } else {
                        regionData = mobConfigProps.SourceRegionSpecificDataMapping().get(Constants.DEFAULT_DOMAIN);
                    }
                    configResponse.setCurrency(regionData.getCurrency());
                    configResponse.setSiteDomain(regionData.getSiteDomain());
                    configResponse.setLanguage(regionData.getLanguage());
                }else {
                    configResponse.setCurrency(Constants.DEFAULT_CUR_INR);
                    configResponse.setSiteDomain(Constants.DEFAULT_SITE_DOMAIN);
                    configResponse.setLanguage(Constants.ENGLISH);
                }
            }
   	 }catch(Exception e) {
		 	logger.error("Exception occured in getting Country specific data", e);
   	 }
    }
    
    /**
     * Function to get error responses for HotelMobileConfig
     * @param errorCode the error code
     * @return the hotel mobile config with error response
     */
    private HotelMobConfig getErrorMobileConfig(HtlMobConfigError errorCode){
    	HotelMobConfig errorMobileConfig = new HotelMobConfig.Builder()
                .isNewConfig(false)
                .success(false)
                .version(Constants.EMPTY_STRING)
                .build();

        ErrorResponse errorResponse = new ErrorResponse();
        GenericErrorEntity genericErrorEntity = new GenericErrorEntity();
        errorResponse.setErrorList(new ArrayList<>());
        errorResponse.getErrorList().add(genericErrorEntity);
        errorMobileConfig.setResponseErrors(errorResponse);
        //genericErrorEntity.setErrorAdditionalInfo("Please check Girgit for prop values immediately");

        switch (errorCode) {
            case PMS_VERSION_ERROR:
                genericErrorEntity.setErrorCode("4400");
                genericErrorEntity.setErrorMessage("Version in PMS is null or empty after read.");
                break;
            case PMS_CONFIG_READ_ERROR:
                genericErrorEntity.setErrorCode("4401");
                genericErrorEntity.setErrorMessage("Config in PMS is null or empty after read.");
                break;
            default:
                genericErrorEntity.setErrorCode("4402");
                genericErrorEntity.setErrorMessage("Something went wrong while trying to access PMS");
                break;
        }

        logger.error("Error in trying to read hotel config from PMS with error code " + genericErrorEntity.getErrorCode());
        return errorMobileConfig;
    }

    public FilterConditions getFilterConditions() {
        try {
            if(consulFlag){
                return mobConfigPropsConsul.getFilterConditions();
            }
            else{
                return mobConfigProps.filterConditions();
            }
        } catch(Exception e) {
            logger.error("Error Ocurred in getFilterConditions: ", e);
        }
        return null;
    }

    public Map<String, List<String>> getPersuasionPlaceHoldersToShow() {
        try {
            if(consulFlag){
                return mobConfigPropsConsul.getPersuasionPlaceHoldersToShow();
            }
            else{
                return mobConfigProps.persuasionPlaceHoldersToShow();
            }
        } catch(Exception e) {
            logger.error("Error Ocurred in getPersuasionPlaceHoldersToShow: ", e);
        }
        return null;
    }

    public Map<String, CardInfo> convertCardDataToCardInfoMap(Map<String,com.mmt.hotels.pojo.listing.personalization.CardData> hesCardDataMap) {
        Map<String, CardInfo> cardInfoMap = new HashMap<>();
        if (MapUtils.isNotEmpty(hesCardDataMap)) {
            hesCardDataMap.forEach((key, hesCardData) -> {
                CardInfo cgCardInfo = new CardInfo();
                cgCardInfo.setIndex(hesCardData.getIndex());
                cgCardInfo.setSubType(hesCardData.getCardSubType());
                cgCardInfo.setId(hesCardData.getCardId());
                cgCardInfo.setTitleText(hesCardData.getTitleText());
                cgCardInfo.setSubText(hesCardData.getSubText());
                cgCardInfo.setIconURL(hesCardData.getIconUrl());
                cgCardInfo.setBgColor(hesCardData.getBgColor());
                cgCardInfo.setCardAction(convertCardAction(hesCardData.getCardAction()));
                cgCardInfo.setTemplateId(hesCardData.getTemplateId());
                cardInfoMap.put(key, cgCardInfo);
            });
        }
        return cardInfoMap;
    }

    private List<CardAction> convertCardAction(List<com.mmt.hotels.pojo.listing.personalization.CardAction> hesCardAction) {
        if (CollectionUtils.isNotEmpty(hesCardAction)) {
            return hesCardAction.stream().map(action -> {
                CardAction cgCardAction = new CardAction();
                cgCardAction.setTitle(action.getTitle());
                cgCardAction.setWebViewUrl(action.getWebViewUrl());
                return cgCardAction;
            }).collect(Collectors.toList());
        }
        return null;
    }

}
