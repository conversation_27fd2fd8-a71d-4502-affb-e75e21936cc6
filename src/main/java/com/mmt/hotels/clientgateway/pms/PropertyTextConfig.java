package com.mmt.hotels.clientgateway.pms;

import com.mmt.hotels.model.response.listpersonalization.MySafetyTooltip;
import com.mmt.propertymanager.config.PropertyQualifier;
import com.mmt.propertymanager.util.JsonConvertor;
import org.aeonbits.owner.Config;
import org.aeonbits.owner.Mutable;
import org.aeonbits.owner.Reloadable;

import java.util.List;
import java.util.Map;

@Config.LoadPolicy(Config.LoadType.MERGE)
@Config.Sources({PMSUrls.GIRGIT_HOST + PMSUrls.GIRGIT_PROPERTY_TEXT_URL})
@PropertyQualifier("propertyTextConfig")
public interface PropertyTextConfig extends Reloadable, Mutable {

    @DefaultValue("{}")
    @ConverterClass(JsonConvertor.class)
    Map<String, Object> mySafetyDataTooltips();

    @DefaultValue("")
    @Separator(",")
    List<String> amenetiesWithUrl();

    @DefaultValue("{}")
    @ConverterClass(JsonConvertor.class)
    MySafetyTooltip mySafetyTooltipKeys();
}
