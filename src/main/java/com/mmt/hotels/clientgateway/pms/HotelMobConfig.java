package com.mmt.hotels.clientgateway.pms;

import com.fasterxml.jackson.databind.JsonNode;
import com.mmt.hotels.clientgateway.response.cbresponse.ErrorResponse;

public class HotelMobConfig {

    private boolean success;

    private String version;
    
    private String variant;

    private boolean isNewConfig;

    private JsonNode configJson;
    
    private String currency;
    
    private String siteDomain;
    
    private String language;

    private ErrorResponse responseErrors;

    private HotelMobConfig(Builder builder) {
        setSuccess(builder.success);
        setVersion(builder.version);
        setVariant(builder.variant);
        setIsNewConfig(builder.isNewConfig);
        setConfigJson(builder.configJson);
        setResponseErrors(builder.responseErrors);
    }

    public boolean getSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public boolean getIsNewConfig() {
        return isNewConfig;
    }

    public void setIsNewConfig(boolean newConfig) {
        isNewConfig = newConfig;
    }

    public JsonNode getConfigJson() {
        return configJson;
    }

    public void setConfigJson(JsonNode configJson) {
        this.configJson = configJson;
    }
    
    public String getVariant() {
		return variant;
	}

	public void setVariant(String variant) {
		this.variant = variant;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getSiteDomain() {
		return siteDomain;
	}

	public void setSiteDomain(String siteDomain) {
		this.siteDomain = siteDomain;
	}

	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	public ErrorResponse getResponseErrors() {
		return responseErrors;
	}

	public void setResponseErrors(ErrorResponse responseErrors) {
		this.responseErrors = responseErrors;
	}

	public static final class Builder {
        private boolean success;
        private String version;
        private String variant;
        private boolean isNewConfig;
        private JsonNode configJson;
        private ErrorResponse responseErrors;

        public Builder() {
        }

        public Builder success(boolean val) {
            success = val;
            return this;
        }

        public Builder version(String val) {
            version = val;
            return this;
        }

        public Builder isNewConfig(boolean val) {
            isNewConfig = val;
            return this;
        }

        public Builder configJson(JsonNode val) {
            configJson = val;
            return this;
        }

        public Builder variant(String val) {
        	variant = val;
            return this;
        }
        
        public Builder responseErrors(ErrorResponse val) {
            responseErrors = val;
            return this;
        }
        
        public HotelMobConfig build() {
            return new HotelMobConfig(this);
        }
    }
}
