package com.mmt.hotels.clientgateway.pms;

import com.mmt.hotels.clientgateway.businessobjects.FilterDetail;
import com.mmt.hotels.clientgateway.response.HotelCategoryData;
import com.mmt.hotels.clientgateway.response.HotelCategoryDataWeb;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUsePersuasion;
import com.mmt.hotels.clientgateway.response.moblanding.CardData;
import com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard;
import com.mmt.hotels.clientgateway.response.staticdetail.AllInclusiveCard;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.model.response.dayuse.MissingSlotDetail;
import com.mmt.hotels.model.response.listpersonalization.LuxeToolTip;
import com.mmt.hotels.model.response.listpersonalization.MyBizAssuredToolTip;
import com.mmt.hotels.model.response.listpersonalization.MySafetyTooltip;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysTooltip;
import com.mmt.propertymanager.config.PropertyQualifier;
import com.mmt.propertymanager.util.JsonConvertor;
import org.aeonbits.owner.Config.LoadPolicy;
import org.aeonbits.owner.Config.LoadType;
import org.aeonbits.owner.Config.Sources;
import org.aeonbits.owner.Mutable;
import org.aeonbits.owner.Reloadable;

import java.util.List;
import java.util.Map;
import java.util.Set;

@LoadPolicy(LoadType.MERGE)
@Sources({ PMSUrls.GIRGIT_HOST + PMSUrls.COMMON_CONFIG_URL })
@PropertyQualifier("commonConfig")
public interface CommonConfig extends Reloadable, Mutable{

	@DefaultValue("{\"\":{}}")
    String persuasionConfigPWAString();
	
	@DefaultValue("{\"\":{}}")
	String persuasionConfigDesktopString();
	
	@DefaultValue("{\"\":{}}")
	String persuasionConfigAndroidString();
	
	@DefaultValue("{\"\":{}}")
	String persuasionConfigIOSString();

    boolean disableUserServiceOtpValidation();

    @ConverterClass(JsonConvertor.class)
	@DefaultValue("{\"US\":{\"currency\":\"USD\",\"siteDomain\":\"US\",\"language\":\"ENGLISH\"},\"AE\":{\"currency\":\"AED\",\"siteDomain\":\"AE\",\"language\":\"ENGLISH\"},\"DEFAULT\":{\"currency\":\"INR\",\"siteDomain\":\"IN\",\"language\":\"ENGLISH\"}}")
	Map<String, SourceRegionSpecificDataConfig> sourceRegionSpecificDataMapping();
	
	String commonUserServiceQuery();
	
    String commonUserServiceFKQuery();
    
    String commonUserServiceAuthToken();

    String userAndTravellerInfoReqBody();

    String userAndTravellerInfoFKReqBody();
    
    @ConverterClass(JsonConvertor.class)
    Map<String,Set<String>> groupToSegmentsMapping();
    
    @DefaultValue("312380")
	String ctaAffiliateId();
    
    @ConverterClass(JsonConvertor.class)
    Map<String,String> mmtTerminalPointAffiliateMapping();
    
    @ConverterClass(JsonConvertor.class)
    Map<String,List<String>> mapHotelAndFlightCity();
    
    @DefaultValue("")
    @Separator(",")
    List<String> defaultSeqTopDomCities();
   
    @DefaultValue("")
    @Separator(",")
    List<String> defaultSeqTopIntlCities();

    @DefaultValue("")
    @Separator(",")
    List<String> defaultRoiCityList();
	
	@Separator(",")
    Set<String> hydraAllSegmentsList();
	
	@DefaultValue("{\"DESKTOP\":{\"DOM\":\"hsq19\",\"DOM_TOP100\":\"hsq19\",\"INTL\":\"hsq01\",\"INTL_TOP100\":\"hsq19\"},\"MSITE\":{\"DOM\":\"hsq119\",\"DOM_TOP100\":\"hsq119\",\"INTL\":\"hsq101\",\"INTL_TOP100\":\"hsq119\"},\"ANDROID\":{\"DOM\":\"hsq119\",\"DOM_TOP100\":\"hsq119\",\"INTL\":\"hsq101\",\"INTL_TOP100\":\"hsq119\"},\"IOS\":{\"DOM\":\"hsq69\",\"DOM_TOP100\":\"hsq69\",\"INTL\":\"hsq51\",\"INTL_TOP100\":\"hsq69\"}}")
    @ConverterClass(JsonConvertor.class)
    Map<String,Map<String,String>> defaultSeqMapping();

    @DefaultValue("{\"DESKTOP\":{\"DOM\":\"sign-hsq130\",\"DOM_TOP100\":\"sign-hsq130\",\"INTL\":\"sign-hsq130\",\"INTL_TOP100\":\"sign-hsq130\"},\"MSITE\":{\"DOM\":\"sign-hsq130\",\"DOM_TOP100\":\"sign-hsq130\",\"INTL\":\"sign-hsq130\",\"INTL_TOP100\":\"sign-hsq130\"},\"ANDROID\":{\"DOM\":\"sign-hsq130\",\"DOM_TOP100\":\"sign-hsq130\",\"INTL\":\"sign-hsq130\",\"INTL_TOP100\":\"sign-hsq130\"},\"IOS\":{\"DOM\":\"sign-hsq130\",\"DOM_TOP100\":\"sign-hsq130\",\"INTL\":\"sign-hsq130\",\"INTL_TOP100\":\"sign-hsq130\"}}")
    @ConverterClass(JsonConvertor.class)
    Map<String,Map<String,String>> defaultCorpSequencing();
    
    @DefaultValue("30")
    int pastFlightBookingDays();
    
    boolean skipFlightCityCheck();

    @DefaultValue("0")
    int getUserDetailsRetries();

    @DefaultValue("0")
    int createGuestUserRetries();

    @DefaultValue("false")
    boolean enableUUID();

    @DefaultValue("userservice.mmt.mmt")
    @Separator(",")
    List<String> userServiceServers();

    String filterRulesForCombinationFilter();

	@DefaultValue("5")
	float greenHornExpPer();
	
	@ConverterClass(JsonConvertor.class)
	Map<String,String> trafficSourceCDFDomainMapping();
	
	@Separator(",")
	@Key("taxExclusiveCountryList")
    List<String> taxExcConList();

    @DefaultValue("{\"DESKTOP\":{\"LISTING\":\"1\",\"ALTACCOLANDING\":\"4\",\"HOMEPAGE\":\"1\"},\"MSITE\":{\"LISTING\":\"1\",\"ALTACCOLANDING\":\"4\",\"HOMEPAGE\":\"1\"},\"ANDROID\":{\"LISTING\":\"1\",\"ALTACCOLANDING\":\"4\",\"HOMEPAGE\":\"1\"},\"IOS\":{\"LISTING\":\"1\",\"ALTACCOLANDING\":\"4\",\"HOMEPAGE\":\"1\"}}")
    @ConverterClass(JsonConvertor.class)
    Map<String, Map<String, String>> collectionsCountMapping();

    @DefaultValue("070f27324406fa20796cfaa18fb703c3cc57b0b6")
    String hydraAPIKey();
    
	@ConverterClass(JsonConvertor.class)
    Map<String, String> srcConCurrMapping();
	
	@Separator(",")
	@Key("ebableInboundExpDeviceList")
	List<String> ebableInboundExpDeviceList();

	boolean disableSrcDesIntlExp();

    @DefaultValue("21600000")
    Long thankYouPageExpiry();

    @DefaultValue("1.7,1.7")
    @Separator(",")
    List<Double> androidListingMapDensity();

    @DefaultValue("1.7,1.7")
    @Separator(",")
    List<Double> iosListingMapDensity();

    @DefaultValue("1,1")
    @Separator(",")
    List<Double> desktopListingMapDensity();

    @Separator(",")
    List<String> hotelCategoryPriority();

    @Separator(",")
    List<String> altAccoCategoryPriority();

    @Separator(",")
    List<String> safetyCategories();

    @ConverterClass(JsonConvertor.class)
    Map<String,String> categoryKeyToTextMap();

    @ConverterClass(JsonConvertor.class)
    Map<String, CardData> thankYouCards();

    @ConverterClass(JsonConvertor.class)
    Map<String, CardData> reviewPageCards();

    @DefaultValue("1")
    int ratePlanMoreOptionsLimit();

    @ConverterClass(JsonConvertor.class)
    @DefaultValue("{\"PREMIUM\":{\"DEFAULT\":{\"FIRST_ROOM_RPC_COUNT\":100,\"OTHER_ROOM_RPC_COUNT\":2},\"3\":{\"FIRST_ROOM_RPC_COUNT\":100,\"OTHER_ROOM_RPC_COUNT\":100},\"5\":{\"FIRST_ROOM_RPC_COUNT\":100,\"OTHER_ROOM_RPC_COUNT\":3},\"10\":{\"FIRST_ROOM_RPC_COUNT\":100,\"OTHER_ROOM_RPC_COUNT\":1},\"100\":{\"FIRST_ROOM_RPC_COUNT\":100,\"OTHER_ROOM_RPC_COUNT\":1}},\"BUDGET\":{\"DEFAULT\":{\"FIRST_ROOM_RPC_COUNT\":100,\"OTHER_ROOM_RPC_COUNT\":2},\"3\":{\"FIRST_ROOM_RPC_COUNT\":100,\"OTHER_ROOM_RPC_COUNT\":100},\"5\":{\"FIRST_ROOM_RPC_COUNT\":100,\"OTHER_ROOM_RPC_COUNT\":3},\"10\":{\"FIRST_ROOM_RPC_COUNT\":100,\"OTHER_ROOM_RPC_COUNT\":1},\"100\":{\"FIRST_ROOM_RPC_COUNT\":100,\"OTHER_ROOM_RPC_COUNT\":1}}}")
    Map<String,Map<String,Map<String,Integer>>> ratePlanDisplayLogic();

    @ConverterClass(JsonConvertor.class)
    Map<String, String> mealPlanMapPolyglot();

    String mySafetyData();

    @DefaultValue("2")
    int apLimitForInclusionIcons();

    @DefaultValue("false")
    boolean mealplanFilterEnable();

    @DefaultValue("false")
    boolean partnerExclusiveFilterEnable();

    @DefaultValue("{}")
    @ConverterClass(JsonConvertor.class)
    ValueStaysTooltip mmtValueStaysTooltipDom();

    @DefaultValue("{}")
    @ConverterClass(JsonConvertor.class)
    MyBizAssuredToolTip myBizAssuredTooltipDom();

    @DefaultValue("{}")
    @ConverterClass(JsonConvertor.class)
    ValueStaysTooltip mmtValueStaysTooltipIntl();

    @DefaultValue("{\"3000\":[\"CTDEL\"],\"4000\":[],\"5000\":[]}")
    @ConverterClass(JsonConvertor.class)
    Map<Integer, Set<String>> budgetHotelCityConfig();

    @DefaultValue("{\"exclusiveImageUrl\":\"https://promos.makemytrip.com/gcc/Badge_MMTExclusive_DT.png\",\"exclusiveIconUrl\":\"https://promos.makemytrip.com/Hotels_product/Persuasion_Icons/Dot.png\",\"selectImageUrl\":\"https://promos.makemytrip.com/GCC/MiscIcons/MMTSELECT_new.png\",\"selectIconUrl\":\"https://promos.makemytrip.com/GCC/MiscIcons/MMTSelectdot.png\"}")
    @ConverterClass(JsonConvertor.class)
    Map<String, String> gccImages();

    String mobgenJsonBO();

    String mobgenStringsBO();

    @ConverterClass(JsonConvertor.class)
    Map<String, HotelCategoryDataWeb> hotelCategoryDataWebMapNew();

    @ConverterClass(JsonConvertor.class)
    Map<String, Map<String, HotelCategoryData>> hotelCategoryDataMap();
    @ConverterClass(JsonConvertor.class)
    Map<String, Map<String, HotelCategoryData>> hotelCategoryDataMapV2();

    @ConverterClass(JsonConvertor.class)
    PersuasionStyle onlyTodayDealTimerStyle();

    @DefaultValue("{}")
    @ConverterClass(JsonConvertor.class)
    Map<String, String> rtbCardConfigs();

    @DefaultValue("false")
    boolean enablePanCardCheck();

    @DefaultValue("{}")
    @ConverterClass(JsonConvertor.class)
    LuxeToolTip luxeToolTip();

    @DefaultValue("{}")
    @ConverterClass(JsonConvertor.class)
    MySafetyTooltip mysafetytooltip();

    @DefaultValue("{}")
    @ConverterClass(JsonConvertor.class)
    Map<String, String> cardTitleMap();

    String mandatoryChargesAlert();

    @DefaultValue("{}")
    @ConverterClass(JsonConvertor.class)
    AllInclusiveCard allInclusiveCard();

    @DefaultValue("")
    @Separator(",")
    List<String> sameDayRoomNames();

    @ConverterClass(JsonConvertor.class)
    Map<String,Map<String,List<String>>> supplierToRateSegmentMapping();

    @DefaultValue("7000.0")
    double payLaterCardLimit();

    @DefaultValue("0")
    int thresholdForSlashedAndDefaultHourPrice();

    @DefaultValue("{\"duration\":[3,6,9]}")
    @ConverterClass(JsonConvertor.class)
    MissingSlotDetail missingSlotDetails();

    @DefaultValue("{}")
    @ConverterClass(JsonConvertor.class)
    Map<String, DayUsePersuasion> dayUseFunnelPersuasions();

    @DefaultValue("{}")
    @ConverterClass(JsonConvertor.class)
    Map<String, FilterDetail> landingFilterConditions();

    @DefaultValue("{}")
    @ConverterClass(JsonConvertor.class)
    MyBizStaticCard myBizStaticCard();

    @DefaultValue("3")
    int starRatingMin();

    @DefaultValue("5")
    int starRatingMax();

    @DefaultValue("0")
    int corpBudgetPriceMin();

    @DefaultValue("2000")
    int corpBudgetPriceMax();
}
