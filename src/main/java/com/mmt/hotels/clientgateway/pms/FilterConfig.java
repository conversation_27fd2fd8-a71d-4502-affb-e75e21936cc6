package com.mmt.hotels.clientgateway.pms;

import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.response.filter.FilterPricingOption;
import com.mmt.propertymanager.config.PropertyQualifier;
import com.mmt.propertymanager.util.JsonConvertor;
import org.aeonbits.owner.Config;
import org.aeonbits.owner.Mutable;
import org.aeonbits.owner.Reloadable;

import java.util.List;
import java.util.Map;

@Config.LoadPolicy(Config.LoadType.MERGE)
@Config.Sources({PMSUrls.GIRGIT_HOST + PMSUrls.GIRGIT_FILTER_URL})
@PropertyQualifier("cgFilterConfig")
public interface FilterConfig extends Reloadable, Mutable {


    @DefaultValue("{}")
    String baseFilterSettings();

    @DefaultValue("{}")
    String baseFilterSettingsV2();

    @DefaultValue("{}")
    String appsDomFilterSettingsV2();

    @DefaultValue("{}")
    String defaultPriceHistogram();

    @DefaultValue("{}")
    String defaultPriceHistogramCorp();

    @DefaultValue("{}")
    String pwaMyPartnerFilterSettings();

    @DefaultValue("{}")
    String desktopMyPartnerFilterSettings();

    @DefaultValue("{}")
    String desktopGCCFilterSetting();
    @DefaultValue("{}")
    String androidGCCFilterSetting();
    @DefaultValue("{}")
    String pwaGCCFilterSetting();
    @DefaultValue("{}")
    String seoIntlFilterSetting();
    @DefaultValue("{}")
    String seoDomFilterSetting();
    @DefaultValue("{}")
    String metaIntlFilterSetting();
    @DefaultValue("{}")
    String metaDomFilterSetting();
    @DefaultValue("{}")
    String semIntlFilterSetting();
    @DefaultValue("{}")
    String semDomFilterSetting();
    @DefaultValue("{}")
    String phonePeFilterSetting();
    @DefaultValue(("{}"))
    String desktopIntlHotelsFilterSetting();
    @DefaultValue(("{}"))
    String appsIntlHotelsFilterSetting();
    @DefaultValue(("{}"))
    String pwaIntlHotelsFilterSetting();
    @DefaultValue(("{}"))
    String desktopDomHotelsFilterSetting();
    @DefaultValue(("{}"))
    String appsDomHotelsFilterSetting();
    @DefaultValue(("{}"))
    String pwaDomHotelsFilterSetting();
    @DefaultValue(("{}"))
    String desktopIntlHomestayFilterSetting();
    @DefaultValue(("{}"))
    String appsIntlHomestayFilterSetting();
    @DefaultValue(("{}"))
    String pwaIntlHomestayFilterSetting();
    @DefaultValue(("{}"))
    String desktopDomHomestayFilterSetting();
    @DefaultValue(("{}"))
    String appsDomHomestayFilterSetting();
    @DefaultValue(("{}"))
    String pwaDomHomestayFilterSetting();
    @DefaultValue(("{}"))
    String dayuseFilterSetting();
    @DefaultValue(("{}"))
    String desktopCorpIntlFilterSetting();
    @DefaultValue("{}")
    String appsCorpIntlFilterSetting();
    @DefaultValue("{}")
    String pwaCorpIntlFilterSetting();
    @DefaultValue("{}")
    String desktopCorpDomFilterSetting();
    @DefaultValue("{}")
    String appsCorpDomFilterSetting();
    @DefaultValue("{}")
    String pwaCorpDomFilterSetting();
    @DefaultValue(("{}"))
    String iosGCCFilterSetting();

    @DefaultValue("{}")
    @ConverterClass(JsonConvertor.class)
    Map<String, List<Filter>> compositeFilterConfig();

    @DefaultValue("{}")
    String amenitiesCategoryConfig();

    @DefaultValue("{}")
    String amenitiesCategoryConfigPolyGlot();

    @ConverterClass(JsonConvertor.class)
    FilterPricingOption pricingOption();
}
