package com.mmt.hotels.clientgateway.constants;

public class GraphQlConstants {

    public static final String UGC_REVIEW_QUERY = "query getContentById ($client:String,$contentId: String, $lob: String,$org: String, $deviceOs:String,$uuid:String,$filter: Filter) { getContentById (client:$client,contentId: $contentId, lob: $lob,org: $org, deviceOs: $deviceOs, uuid:$uuid,filter: $filter) { reviewCount ugcContentData{ugcId lobData userInfo{ uuid customerName } cohort rating{ ratingTag value } imageInfo{ baseUrl tags{ tag } transCodeDeviceOsImg{ deviceOs } } title{ text tags{ tag matchingInfo{ matchingText tagMatchingOffsetResponse{ start end } } }} ugcContentAdditionalInfo{ logo } textInfo { text tags{ tag matchingInfo{ matchingText tagMatchingOffsetResponse{ start end } } } } replyDetails{ replyText repliedBy replyTime } reviewDate } }}";

}
