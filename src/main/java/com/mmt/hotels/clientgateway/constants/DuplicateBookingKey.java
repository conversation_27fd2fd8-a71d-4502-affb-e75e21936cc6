package com.mmt.hotels.clientgateway.constants;

public enum DuplicateBooking<PERSON><PERSON> {
    DUPLICATE_BOOKING_HEADING(""),
    DUPLICATE_BOOKING_SUB_HEADING(""),
    DUPLICATE_BOOKING_TITLE(""),
    DUPLICATE_BOOKING_DESCRIPTION(""),
    DUPLICATE_BOOKING_ICON_URL_MOBILE(""),
    DUPLICATE_BOOKING_ICON_URL_DESKTOP(""),
    DUPLICATE_BOOKING_TITLE_COLOR("#eb2026"),
    DUPLICATE_BOOKING_BG_LINEAR_GRADIENT("bgLinearGradient"),
    DUPLICATE_BOOKING_BG_LINEAR_GRADIENT_START("#FDF3EB"),
    DUPLICATE_BOOKING_BG_LINEAR_GRADIENT_END("#FFFFFF"),
    DUPLICATE_BOOKING_BG_LINEAR_GRADIENT_DIRECTION("vertical"),
    DUPLICATE_BOOKING_BG_LINEAR_GRADIENT_CENTER("#FFFFFF"),
    DUPLICATE_BOOKING_STROKE_COLOR("#eb2026"),
    DUPLICATE_BOOKING_PRIMARY_TRAVELLER_NAME("Traveller Name"),
    DUPLICATE_BOOKING_BOOKING_ID("Booking ID"),
    DUPLICATE_BOOKING_HOTEL_NAME("Hotel Name"),
    DUPLICATE_BOOKING_CHECK_IN_DATE("Check-in"),
    DUPLICATE_BOOKING_CHECK_OUT_DATE("Check-out"),
    DUPLICATE_BOOKING_ROOM_NAME("Room"),
    DUPLICATE_BOOKING_WORKFLOW_ID("Workflow Id"),
    DUPLICATE_BOOKING_EMAIL("Email ID");

    private String value;

    public String getValue() {
        return value;
    }

    DuplicateBookingKey(String value){
        this.value = value;
    }

}