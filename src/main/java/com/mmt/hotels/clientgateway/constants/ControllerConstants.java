package com.mmt.hotels.clientgateway.constants;

public class ControllerConstants {

    private ControllerConstants(){
    }

    public static String LISTING_SEARCH_HOTELS = "cg/search-hotels";
    public static String LISTING_SEARCH_TREELS = "cg/search-treels";
    public static String WISHLISTED_HOTELS = "cg/wishListed-hotels";
    public static String LISTING_MOB_LANDING = "cg/mob-landing";
    public static String LISTING_FILTER_COUNT = "cg/filter-count/";
    public static String TREELS_FILTER_COUNT = "cg/treels-filter-count/";
    public static String TREELS_LISTING_FILTER_COUNT = "cg/treels-filter-count/";
    public static String LISTING_BATCH_FILTER = "cg/batch-filters/";
    public static String LISTING_MAP = "cg/listing-map/";
    public static String CITY_OVERVIEW = "cg/city-overview/";
    public static String FETCH_UPSELL_RATEPLAN = "cg/fetch-upsell-rateplan";

    public static String REVIEW_AVAIL_ROOMS = "cg/avail-rooms/";
    public static String GIFT_CARD_CLAIM = "cg/giftcard/claim/";
    public static String FETCH_EMI_DETAILS = "cg/fetch-emi-details/";
    public static String REVIEW_VAILDATE_COUPON = "cg/validatecoupon";
    public static String REVIEW_SEARCH_ADD_ONS = "cg/search-addons/";
    public static String REVIEW_TOTAL_PRICING = "cg/totalpricing";
    public static String REVIEW_GET_POLICIES = "cg/getpolicies";
    public static String REVIEW_ROOM_INFO = "cg/room-info";
    public static String REVIEW_ROOM_INFOS = "cg/room-infos";

    public static String DETAIL_SEARCH_ROOMS = "cg/search-rooms/";
    public static String DETAIL_SEARCH_SLOTS = "cg/search-slots/";
    public static String DETAIL_STATIC_DETAIL = "cg/staticdetail";

    public static String DETAIL_UGC_SUMMARY = "cg/ugc-summary";
    public static String DETAIL_UGC_REVIEWS = "cg/ugc-reviews";
    public static String DETAIL_UGC_UPVOTE = "cg/ugc-upvote";
    public static String WISHLISTED_STATIC_DETAILS = "cg/wishListed-static-detail";
    public static String DETAIL_UPDATE_PRICE = "cg/updateprice";
    public static String DETAIL_UPDATED_EMI_DETAILS = "cg/updated-emi-details/";
    public static String HOST_CALLING_INITIATE = "cg/hostCalling/initiate";

    public static String REBOOK_DETAIL = "cg/rebook/avail";
    public static String REBOOK_REVIEW = "cg/rebook/avail-validate";
    public static String REBOOK_PAYMENT = "cg/rebook/create-provisional-booking";

    public static final String COMMON_QUERY_PARAMS = "/{client}/{version}";

    public static String SRC_CLIENT = "srcClient";

    public static String FETCH_COLLECTIONS = "cg/fetchCollections";

    public static String GROUP_BOOKING = "cg/groupBooking";

    public static String GET_APPROVALS = "cg/get-approvals";
    public static String PAY_LATER_ELIGIBILITY="cg/pay-later-eligibility";

    public static String FETCH_LOCATIONS = "cg/fetch-locations";

    public static String CALENDAR_AVAILABILITY = "cg/calendar-availability";

    public static String CG_UGCQUESTIONS = "cg/ugc-question";

    public static String CG_UGCSUBMIT = "cg/ugc-submit";

    public static String TRAVEL_INSIGHT = "cg/travel-insights";

    public static final String SAVE_TRAVELLER_GST_DETAILS = "cg/save-traveller-gst";

    public static final String GET_TRAVELLER_GST_DETAILS = "cg/traveller-gst";

    public static String GET_BANK_OFFERS = "cg/bankOffers";

    public static String LISTING_DEEPLINK_GENERATION = "cg/gen-redirect-url";

}
