package com.mmt.hotels.clientgateway.constants;

import com.mmt.hotels.filter.FilterGroup;

import java.util.Arrays;
import java.util.List;

public class ConstantsTranslation {

    public static final String PRICE_PN_TITLE = "price_pn_title";
    public static final String PRICE_PNT_TITLE = "price_pnt_title";
    public static final String PRICE_PRN_TITLE = "price_prn_title";
    public static final String PRICE_PRNT_TITLE = "price_prnt_title";
    public static final String PRICE_TP_TITLE = "price_tp_title";
    public static final String PRICE_TPT_TITLE = "price_tpt_title";
    public static final String RECOMMENDED_HOTELS_HEADING = "recommended_hotels_heading";
    public static final String RECOMMENDED_HOTELS_HEADING_NEW = "recommended_hotels_heading_new";
    public static final String GETAWAYS_RECOMMENDED_HEADING = "NEARBY_GETAWAYS_HEADING";
    public static final String GIFT_CARD_TEXT = "GIFT_CARD_TEXT";
    public static final String MOST_BOOKED_HEADING = "MOST_BOOKED_HEADING";
    public static final String TITLE_MISS = "TITLE_MISS";
    public static final String TITLE_MS = "TITLE_MS";
    public static final String TITLE_MRS = "TITLE_MRS";

    // avail rooms pricing constants
    public static final String PRICE_TYPE_SUM = "price_type_sum";
    public static final String PRICE_TYPE_DIFF = "price_type_diff";
    public static final String TOTAL_AMOUNT_LABEL = "total_amount_label";
    public static final String AMOUNT_YOU_PAYING_NOW_LABEL = "amount_you_paying_now_label";
    public static final String AMOUNT_YOU_PAYING_AT_HOTEL_LABEL = "amount_you_paying_at_hotel_label";
    public static final String FLEXI_CANCEL_CHARGES_LABEL = "flexi_cancel_charges_label";
    public static final String NO_CARD_REQUIRED_SUBLINE = "no_card_required_subline";
    public static final String BASE_FARE_LABEL = "base_fare_label";
    public static final String BASE_FARE_WITH_TAX_LABEL = "base_fare_with_tax_label";
    public static final String TCS_AMOUNT_LABEL = "tcs_amount_label";
    public static final String INSURANCE_LABEL = "insurance_label";
    public static final String SME_LABEL = "sme_label";
    public static final String SME_BREAKUP_LABEL = "sme_breakup_label";

    public static final String BNPL0_BOOKINGS_THRESHOLD_REACHED_TEXT = "BNPL0_BOOKINGS_THRESHOLD_REACHED_TEXT";
    public static final String BNPL_BOOKINGS_THRESHOLD_REACHED_TEXT = "BNPL_BOOKINGS_THRESHOLD_REACHED_TEXT";
    public static final String BNPL_BOOKINGS_THRESHOLD_REACHED_SUBTEXT = "BNPL_BOOKINGS_THRESHOLD_REACHED_SUBTEXT";

    public static final String BNPL_SINGLE_BOOKING_THRESHOLD_SUBTEXT = "BNPL_SINGLE_BOOKING_THRESHOLD_SUBTEXT";
    public static final String BNPL_MULTIPLE_BOOKING_THRESHOLD_SUBTEXT = "BNPL_MULTIPLE_BOOKING_THRESHOLD_SUBTEXT";

    public static final String NON_BNPL0_COUPON_APPLIED_TEXT = "NON_BNPL0_COUPON_APPLIED_TEXT";
    public static final String NON_BNPL_COUPON_APPLIED_TEXT = "NON_BNPL_COUPON_APPLIED_TEXT";
    public static final String NON_BNPL_COUPON_APPLIED_SUBTEXT = "NON_BNPL_COUPON_APPLIED_SUBTEXT";

    public static final String INSURANCE_BNPL1_TEXT = "INSURANCE_BNPL_1_TEXT";

    public static final String INSURANCE_BNPL0_TEXT = "INSURANCE_BNPL_0_TEXT";

    public static final String INSURANCE_BNPL_SUBTEXT = "INSURANCE_BNPL_SUBTEXT";

    public static final String BEDROOMS = "BEDROOMS";

    public static final String BEDROOM = "BEDROOM";
    public static final String TOTAL_DISCOUNT_LABEL = "total_discount_label";
    public static final String PARTNER_COMMISSION_LABEL = "partner_commission_label";
    public static final String HOTELIER_DISCOUNT_LABEL = "hotelier_discount_label";
    public static final String META_DISCOUNT_LABEL = "META_DISCOUNT_LABEL";
    public static final String CDF_DISCOUNT_LABEL = "cdf_discount_label";
    public static final String CDF_DISCOUNT_LABEL_MY_PARTNER = "cdf_discount_label_my_partner";
    public static final String BLACK_DISCOUNT_LABEL = "BLACK_DISCOUNT_LABEL";
    public static final String GCC_SELECT_DISCOUNT_LABEL = "GCC_SELECT_DISCOUNT_LABEL";
    public static final String LOS_DISCOUNT_LABEL = "LOS_DISCOUNT_LABEL";
    public static final String WALLET_LABEL = "wallet_label";
    public static final String PROMO_CASH = "promo_cash";
    public static final String PROMO_CASH_MY_PARTNER = "promo_cash_mypartner";
    public static final String PRICE_AFTER_DISCOUNT_LABEL = "price_after_discount_label";
    public static final String HOTEL_DISCOUNT_LABEL = "HOTEL_DISCOUNT_LABEL";
    public static final String HOTEL_FUNDED_DISCOUNT_LABEL = "hotel_funded_discount_label";
    public static final String TDS_LABEL = "tds_label";
    public static final String TAXES_LABEL = "taxes_label";
    public static final String GST_LABEL = "gst_label";
    public static final String HOTEL_TAX_LABEL = "hotel_tax_label";
    public static final String SERVICE_FEES_LABEL = "service_fees_label";

    public static final String SERVICE_FEES_GST_LABEL = "service_fees_gst_label";
    public static final String SERVICE_CHARGE_LABEL = "service_charge_label";
    public static final String AFFILIATE_FEES_LABEL = "affiliate_fees_label";
    public static final String SERVICE_FEES_REVERSAL_LABLE = "service_fees_reversal_lable";
    public static final String EFFECTIVE_COUPON_APPLIED_LABLE = "effective_coupon_applied_lable";

    public static final String CAT_STATIC_POLICY = "cat_static_policy";
    public static final String CAT_HOUSE_RULES = "cat_house_rules";
    public static final String SUBCAT_COMMON_RULES = "subcat_common_rules";
    public static final String SUBCAT_EXTRA_BED = "subcat_extra_bed";
    public static final String CAT_MUST_READ = "cat_must_read";
    public static final String TITLE_MUST_READ = "title_must_read";
    /*
    thankyou constants
     */

    public static final String AMOUNT_BREAKUP_DEDUCTED = "amount_breakup_deducted";
    public static final String AMOUNT_BREAKUP_PENDING = "amount_breakup_pending";
    public static final String AMOUNT_BREAKUP_TOTAL = "amount_breakup_total";

    public static final String NON_REFUNDABLE_TEXT = "non_refundable_text";
    public static final String REFUNDABLE_TEXT = "refundable_text";

    public static final String NON_REFUNDABLE_TEXT_DATE_CHANGE = "NON_REFUNDABLE_TEXT_DATE_CHANGE";
    public static final String NON_REFUNDABLE_TEXT_V2 = "non_refundable_text_V2";
    public static final String NON_REFUNDABLE_TEXT_DATE_CHANGE_V2 = "NON_REFUNDABLE_TEXT_DATE_CHANGE_V2";
    public  static final String PARTIAL_REFUNDABLE_TEXT = "PARTIAL_REFUNDABLE_TEXT";

    public  static final String PARTIAL_REFUNDABLE_CHECKIN_TEXT = "PARTIAL_REFUNDABLE_CHECKIN_TEXT";

    public  static final String PARTIAL_REFUNDABLE_WITH_REFUNDDATE_TEXT = "PARTIAL_REFUNDABLE_WITH_REFUNDDATE_TEXT";
    public static final String NON_REFUNDABLE_SUBTEXT = "non_refundable_subtext";
    public static final String FREE_CANCELL_BNPL_SUBTEXT = "free_cancell_bnpl_subtext";
    public static final String CANCEL_POLICY_BNPL_SUBTEXT = "CANCEL_POLICY_BNPL_SUBTEXT";
    public static final String CANCEL_POLICY_BNPL_SUBTEXT_NEW_VARIANT = "CANCEL_POLICY_BNPL_SUBTEXT_NEW_VARIANT";
    public static final String GIFT_CARD_CLAIM_NOW = "GIFT_CARD_CLAIM_NOW";
    public static final String GIFT_CARD_BOTTOMSHEET_CTA = "GIFT_CARD_BOTTOMSHEET_CTA";
    public static final String GC_TNC_TITLE = "GC_TNC_TITLE";
    public static final String GC_TNC_SUBTITLE = "GC_TNC_SUBTITLE";
    public static final String GC_TITLE_CLAIMED = "GC_TITLE_CLAIMED";
    public static final String GC_TITLE_UNCLAIMED = "GC_TITLE_UNCLAIMED";
    public static final String BOOK_NOW_MODAL_OKAY_GOT_IT = "BOOK_NOW_MODAL_OKAY_GOT_IT";

    // for bnpl 0
    public static final String CANCEL_POLICY_BNPL_SUBTEXT_ZERO_VARIANT = "CANCEL_POLICY_BNPL_SUBTEXT_ZERO_VARIANT";

    public static final String UNRATED_SR = "unrated_sr";
    public static final String KID_FRIENDLY = "kid_friendly";
    public static final String COUPLE_FRIENDLY = "couple_friendly";
    public static final String STAYCATION = "staycation";
    public static final String GREATVALUE = "greatvalue";
    public static final String MyBiz_Assured = "MyBiz_Assured";
    public static final String MMT_Assured = "MMT_Assured";

    public static final String ADDITIONAL_FEE_SUBTEXT_ROOMS = "additional_fee_subtext_rooms";
    public static final String ADDITIONAL_FEE_SUBTEXT_ROOM = "additional_fee_subtext_room";
    public static final String ADDITIONAL_FEE_SUBTEXT_ADULT = "additional_fee_subtext_adult";
    public static final String ADDITIONAL_FEE_SUBTEXT_ADULTS = "additional_fee_subtext_adults";
    public static final String ADDITIONAL_FEE_SUBTEXT_CHILD = "additional_fee_subtext_child";
    public static final String ADDITIONAL_FEE_SUBTEXT_CHILDREN = "additional_fee_subtext_children";
    public static final String ADDITIONAL_FEE_SUBTEXT_NIGHT = "additional_fee_subtext_night";
    public static final String ADDITIONAL_FEE_SUBTEXT_NIGHTS = "additional_fee_subtext_nights";
    public static final String PG_CHARGES_TEXT = "PG_CHARGES_TEXT";
    public static final String ADDITIONAL_FEE_TRANSFERS_MSG = "additional_fee_transfers_msg";
    public static final String ADDITIONAL_FEE_TRANSFERS_HTML = "additional_fee_transfers_html";

    //Avail rooms properties.
    public static final String GSTN_NOT_AVAILABLE_TEXT = "availrooms_gstn_not_available_text";
    public static final String GSTIN_CLAIM_TEXT = "GSTIN_CLAIM_TEXT";
    public static final String TIN_NO_NOT_AVAILABLE_TEXT = "TIN_NO_NOT_AVAILABLE_TEXT";
    public static final String NO_MEALS_INCLUDED_TEXT = "no_meals_included_text";
    public static final String PRICE_INCREASE_ALERT_TEXT = "alert_price_increase_textV2";
    public static final String PRICE_INCREASE_ALERT_SUB_TEXT = "alert_price_increase_subtext";
    public static final String PRICE_DECREASE_ALERT_TEXT = "alert_price_decrease_textV2";
    public static final String PRICE_DECREASE_ALERT_SUB_TEXT = "alert_price_decrease_subtext";
    public static final String CANCEL_POLICY_CHANGE_ALERT_TEXT = "alert_cancel_policy_change_text";
    public static final String MEAL_PLAN_CHANGE_ALERT_TEXT = "alert_mean_plan_change_text";
    public static final String CTRIP_NON_INSTANT_TEXT = "alert_confirmation_policy_text";
    public static final String CANCELLATION_POLICY_NR_NON_INSTANT_TEXT = "cancellation_policy_nr_non_instant_text";
    public static final String PRICE_CHANGE_ALERT_TEXT = "alert_price_change_text";
    public static final String PRICE_CHANGE_INCREASE_ALERT_SUB_TEXT = "alert_price_change_increase_subtext_based_upon_currency";
    public static final String PRICE_CHANGE_DECREASE_ALERT_SUB_TEXT = "alert_price_change_decrease_subtext_based_upon_currency";
    public static final String PRICE_CHANGE_DECREASE_OLD_VERSION_ALERT_SUB_TEXT = "alert_price_change_old_version_subtext";
    public static final String PRICE_CHANGE_INCREASE_ALERT_TEXT_OLD_VERSION = "alert_price_change_increase_old_version_text_based_upon_currency";
    public static final String PRICE_CHANGE_DECREASE_ALERT_TEXT_OLD_VERSION = "alert_price_change_decrease_old_version_text_based_upon_currency";
    //thank you
    public static final String PENDING_AMOUNT_BNPL_TEXT = "thankyou_remaining_amount_bnpl_text";
    public static final String PENDING_AMOUNT_BNPL_NEW_VARIANT_TEXT = "thankyou_remaining_amount_bnpl_new_variant_text_v2";
    public static final String PENDING_AMOUNT_BNPL_MANDATE_TEXT = "thankyou_bnpl_mandate_text";
    public static final String PENDING_AMOUNT_PAH_DEFAULT_TEXT = "thankyou_remaining_amount_pah_default_text";
    public static final String PENDING_AMOUNT_PAH_TEXT = "thankyou_remaining_amount_pah_text";
    public static final String FAILED_BOOKING_REFUND_DURATION = "thankyou.failed.booking.refund.duration";
    public static final String PENDING_BOOKING_CONFIRMATION_TIME = "thankyou.booking.pending.corfirmation.time";

    public static final String PENDING_BOOKING_CONFIRMATION_TIME_NEGOTIATED_RATE = "THANKYOU_BOOKING_PENDING_CONFIRMATION_TIME_NEGOTIATED_RATE";
    public static final String PENDING_CTATEXT = "thankyou_pending_booking_cta_text";
    public static final String BNPL_PAYMENT_BREAKUP_LABEL = "thankyou_bnpl_payment_persuasion_label";
    public static final String PAH_PAYMENT_BREAKUP_LABEL = "thankyou_pah_payment_persuasion_label";
    public static final String SBPP_VALUE_1_PERS_TEXT = "SBPP_VALUE_1_PERS_TEXT";
    public static final String SBPP_VALUE_2_PERS_TEXT = "SBPP_VALUE_2_PERS_TEXT";

    // common transfer
    public static final String NO_COUPON_AVAILABLE_TEXT = "no_coupon_available_text";
    public static final String EARLY_CHECKIN_TITLE = "early_checkin_title";
    public static final String MANDATORY_CHARGES_SECTION_TITLE = "mandatory_charges_section_title";
    public static final String MANDATORY_CHARGES_SECTION_DESC = "mandatory_charges_section_desc";
    public static final String MANDATORY_CHARGES_SECTION_DESC_DH = "MANDATORY_CHARGES_SECTION_DESC_DH";
    public static final String MANDATORY_CHARGES_DETAIL_PAGE_DISCLAIMER = "mandatory_charges_detail_page_disclaimer";
    public static final String MANDATORY_CHARGES_BREAKUP_SUBTITLE = "mandatory_charges_section_breakup_subtitle";
    public static final String MANDATORY_CHARGES_DEFAULT_PRICE_TEXT = "mandatory_charges_section_default_price_text";
    public static final String INTL_PAH_NON_REFUNDABLE_TEXT = "intl_pah_nr_policy_text";
    public static final String INTL_PAH_FREE_CANCELLATION_TEXT = "intl_pah_fc_policy_text";
    public static final String INTL_PAH_FC_NON_INGO_POLICY_TEXT = "intl_pah_fc_non_ingo_policy_text";
    public static final String INTL_NR_SUPPLIER_SPECIFIC_TEXT = "intl_nr_supplier_specific_text";
    public static final String PROPERTY_RULES_DISCLAIMER_TEXT = "property_rules_disclaimer_text";
    public static final String SPECIAL_REQUEST_TEXT = "selected_special_request_text";
    public static final String SPECIAL_REQUEST_SUBTEXT = "selected_special_request_sub_text";

    public static final String HOTELIER_DISCOUNT_LABEL_CORP = "HOTELIER_DISCOUNT_LABEL_CORP";
    public static final String PRICE_TOOL_TIP = "PRICE_TOOL_TIP";
    public static final String DAYS = "{days}";
    public static final String AMOUNT = "{amount}";
    public static final String CURRENCY = "{currency}";

    public static final String GUESTS = "GUESTS";
    public static final String GUESTS_AND_ROOMS = "GUESTS_AND_ROOMS";
    public static final String GUESTS_AND_BEDS = "GUESTS_AND_BEDS";
    public static final String LONGSTAY_HIGHLIGHTED_TEXT = "LONGSTAY_HIGHLIGHTED_TEXT";
    public static final String ZERO_PAYMENT_NOW_WITH_CC = "ZERO_PAYMENT_NOW_WITH_CC";
    public static final String BNPL_NEW_VARIANT_TEXT = "BNPL_NEW_VARIANT_TEXTV2";

    //Book with 0 Payment
    public static final String BNPL_ZERO_VARIANT_TEXT = "BNPL_ZERO_VARIANT_TEXTV2";


    //RTB related texts
    public static final String RTB_CARD_TITLE = "RTB_CARD_TITLE";
    public static final String RTB_CARD_TEXT = "RTB_CARD_TEXT";
    public static final String RTB_TRIP_CARD_ACTION_TEXT = "RTB_TRIP_CARD_ACTION_TEXT";
    public static final String RTB_TRIP_CARD_HEADER_SUB_TEXT = "RTB_TRIP_CARD_HEADER_SUB_TEXT";
    public static final String RTB_TRIP_CARD_HEADER_TEXT = "RTB_TRIP_CARD_HEADER_TEXT";
    public static final String RTB_TRIP_CARD_TEXT1 = "RTB_TRIP_CARD_TEXT1";
    public static final String RTB_TRIP_CARD_TEXT2 = "RTB_TRIP_CARD_TEXT2";
    public static final String RTB_TRIP_CARD_TITLE = "RTB_TRIP_CARD_TITLE";
    public static final String RTB_CHAT_CARD_TITLE = "RTB_CHAT_CARD_TITLE";
    public static final String RTB_CHAT_CARD_SUBTITLE = "RTB_CHAT_CARD_SUBTITLE";
    public static final String RTB_CHAT_CARD_ACTION_TEXT = "RTB_CHAT_CARD_ACTION_TEXT";
    public static final String RTB_ALERT_TEXT = "RTB_ALERT_TEXT";
    public static final String RTB_CHANGE_ALERT_TEXT = "RTB_CHANGE_ALERT_TEXT";
    public static final String STAR_FACILITIES = "STAR_FACILITIES";
    public static final String THANkYOU_INSURANCE_SUBTEXT = "THANKYOU_INSURANCE_CARD_SUB_TEXT";
    public static final String THANkYOU_INSURANCE_SUBTEXT_BNPL = "THANKYOU_INSURANCE_SUBTEXT_BNPL";
    public static final String TOP_RATED = "TOP_RATED";
    public static final String RTB_TITLE = "RTB_TITLE";
    public static final String RTB_CARD_DESC = "RTB_CARD_DESC";
    public static final String RTB_PRE_APPROVED_TEXT = "RTB_PRE_APPROVED_TEXT";
    public static final String RTB_TEXT = "RTB_TEXT";
    public static final String COMPLETE_YOUR_BOOKING = "COMPLETE_YOUR_BOOKING";

    public static final String KNOW_MORE = "KNOW_MORE";

    public static final String FILTER_LUXE_PACKAGE_TEXT = "FILTER_LUXE_PACKAGE_TEXT";
    public static final String FILTER_NON_LUXE_PACKAGE_TEXT = "FILTER_NON_LUXE_PACKAGE_TEXT";
    public static final String PERSUASION_LUXE_PACKAGE_TEXT = "PERSUASION_LUXE_PACKAGE_TEXT";
    public static final String PERSUASION_NON_LUXE_PACKAGE_TEXT = "PERSUASION_NON_LUXE_PACKAGE_TEXT";

    //FAQ Properties
    public static final String FAQ_TITLE = "Frequently Asked Questions";
    public static final String FAQ_HINT = "Search";
    public static final String FAQ_ITEM_COUNT = "3";
    public static final String FAQ_EXTRA_TEXT = "All %d answered questions";

    public static final String SELECT_ROOM_1_AMENITIES_BANNER = "SELECT_ROOM_1_AMENITIES_BANNER";
    public static final String SELECT_ROOM_2_AMENITIES_BANNER = "SELECT_ROOM_2_AMENITIES_BANNER";
    public static final String SELECT_ROOM_3_AMENITIES_BANNER = "SELECT_ROOM_3_AMENITIES_BANNER";
    public static final String ROOM_NAME = "{ROOM_NAME}";
    public static final String AMENITY_1 = "{AMENITY_1}";
    public static final String AMENITY_2 = "{AMENITY_2}";
    public static final String AMENITY_3 = "{AMENITY_3}";

    public static final String DAY_USE = "DAY_USE";
    public static final String DAY_USE_ROOM_ALERT_TEXT = "DAY_USE_ROOM_ALERT_TEXT";
    public static final String DAY_USE_ROOM_LABEL = "DAY_USE_ROOM_LABEL";

    public static final String CORP_BOTTOM_SHEET_HEADING_MOBILE = "CORP_BOTTOM_SHEET_HEADING_MOBILE";
    public static final String CORP_BOTTOM_SHEET_SUBHEADING_MOBILE = "CORP_BOTTOM_SHEET_SUBHEADING_MOBILE";
    public static final String RATED_HIGH_BT_MOBILE = "RATED_HIGH_BT_MOBILE";
    public static final String MYBIZ_ASSURED_FILTER_CARD_CTA = "MYBIZ_ASSURED_FILTER_CARD_CTA";
    public static final String GST_INVOICE_ASSURANCE_TEXT = "GST_INVOICE_ASSURANCE_TEXT";
    public static final String BPG_TEXT = "BPG_TEXT";
    public static final String RATED_HIGH_BT = "RATED_HIGH_BT";
    public static final String HOMESTAY_DISPLAY_TEXT = "HOMESTAY_DISPLAY_TEXT";
    public static final String CONTEXTUAL_POPULAR_FILTER_TITLE = "CONTEXTUAL_POPULAR_FILTER_TITLE";
    public static final String BATCH_COLLECTIONS_TITLE = "BATCH_COLLECTIONS_TITLE";
    public static final String RESTRICTIONS = "RESTRICTIONS";
    public static final String EXTRA_BED_POLICY = "EXTRA_BED_POLICY";
    public static final String FOOD_AND_DINING = "FOOD_AND_DINING";
    public static final String ROOM_DETAILS_EXTRA_GUEST_INFO_HEADING = "ROOM_DETAILS_EXTRA_GUEST_INFO_HEADING";
    public static final String DEAL_ICON_URL = "DEAL_ICON_URL";
    public static final String EXTRA_GUEST_DEAL_TEXT_STYLE = "EXTRA_GUEST_DEAL_TEXT_STYLE";
    public static final String HIGH_DEMAND_PERSUASION = "HIGH_DEMAND_PERSUASION";
    public static final String LONG_STAY_GCC_NUDGE_PERSUASION = "LONG_STAY_GCC_NUDGE_PERSUASION";
    public static final String BOOK_NOW = "BOOK_NOW";
    public static final String QUICK_BOOK_DESKTOP_CTA = "QUICK_BOOK_DESKTOP_CTA";
    public static final String QUICK_BOOK_DESKTOP_TITLE = "QUICK_BOOK_DESKTOP_TITLE";
    public static final String QUICK_BOOK_DESKTOP_SUBTITLE = "QUICK_BOOK_DESKTOP_SUBTITLE";
    public static final String QUICK_BOOK_DESKTOP_MODAL_SUBTITLE = "QUICK_BOOK_DESKTOP_MODAL_SUBTITLE";

    public static final String LOCAL_CURRENCY_TEXT = "LOCAL_CURRENCY_TEXT";
    public static final String PAY_AT_HOTEL_POLICY_TEXT = "PAY_AT_HOTEL_POLICY_TEXT";
    public static final String PAY_AT_HOTEL_POLICY_TEXT_GENERIC = "PAY_AT_HOTEL_POLICY_TEXT_GENERIC";
    public static final String LOGIN_PERSUASION_TEXT = "LOGIN_PERSUASION_TEXT";
    public static final String LOGIN_PERSUASION_SUBTEXT = "LOGIN_PERSUASION_SUBTEXT";
    public static final String LOGIN_PERSUASION_TEXT_GCC = "LOGIN_PERSUASION_TEXT_GCC";
    public static final String LOGIN_PERSUASION_SUBTEXT_GCC = "LOGIN_PERSUASION_SUBTEXT_GCC";

    public static final String SPACE_OCCUPANCY_TEXT = "SPACE_OCCUPANCY_TEXT";
    public static final String SPACE_SINGLE_OCCUPANCY_TEXT = "SPACE_SINGLE_OCCUPANCY_TEXT";

    public static final String PAN_CARD_DETAILS_TITLE = "PAN_CARD_DETAILS_TITLE";
    public static final String PAN_CARD_DETAILS_DESCRIPTION = "PAN_CARD_DETAILS_DESCRIPTION";

    public static final String MYBIZ_DIRECT_HOTEL_APPS_DISTANCE_TEXT = "MYBIZ_DIRECT_HOTEL_APPS_DISTANCE_TEXT";
    public static final String MYBIZ_DIRECT_HOTEL_DESKTOP_DISTANCE_TEXT = "MYBIZ_DIRECT_HOTEL_DESKTOP_DISTANCE_TEXT";
    public static final String MYBIZ_DIRECT_HOTEL_TAG = "MYBIZ_DIRECT_HOTEL_TAG";
    public static final String MYBIZ_DIRECT_HOTEL_SUBHEADING = "MYBIZ_DIRECT_HOTEL_SUBHEADING";
    public static final String CHAIN_PROPERTIES_HEADER = "CHAIN_PROPERTIES_HEADER";
    public static final String HOST_PROPERTIES_HEADER = "HOST_PROPERTIES_HEADER";

    public static final String BNPL_GCC_TEXT = "BNPL_GCC_TEXTV2";
    public static final String PAH_GCC_TEXT = "PAH_GCC_TEXT";
    public static final String PAH_WITHOUT_CC_TEXT = "PAH_WITHOUT_CC_TEXT";
    public static final String PAH_WITH_CC_TEXT = "PAH_WITH_CC_TEXT";
    public static final String PACKAGE_RATE_TEXT = "PACKAGE_RATE_TEXT";
    public static final String TC_CLAUSE_TEXT = "TC_CLAUSE_TEXT";
    public static final String TC_HEADING_TEXT = "TC_HEADING_TEXT";
    public static final String TC_SUBHEADING_TEXT = "TC_SUBHEADING_TEXT";

    //    TripMoney paylater texts
    public static final String PAY_LATER_TITLE = "PAY_LATER_TITLE";
    public static final String PAY_LATER_SUBTITLE = "PAY_LATER_SUBTITLE";
    public static final String PAY_LATER_CTA_TEXT = "PAY_LATER_CTA_TEXT";
    public static final String PAY_LATER_SUCCESS_TITLE = "PAY_LATER_SUCCESS_TITLE";
    public static final String PAY_LATER_FAILURE_TITLE = "PAY_LATER_FAILURE_TITLE";
    public static final String PAY_LATER_SUCCESS_SUBTITLE = "PAY_LATER_SUCCESS_SUBTITLE";
    public static final String PAY_LATER_FAILURE_SUBTITLE = "PAY_LATER_FAILURE_SUBTITLE";
    public static final String PAY_LATER_SUCCESS_CARD_TITLE = "PAY_LATER_SUCCESS_CARD_TITLE";
    public static final String PAY_LATER_DESCRIPTION = "PAY_LATER_DESCRIPTION";


    public static final String MMT_EXCLUSIVE = "MMT_EXCLUSIVE";
    public static final String LANDING_GB_MSG = "landingGBMsg";
    public static final String TIMESLOT_AM_AM = "TIMESLOT_AM_AM";
    public static final String TIMESLOT_AM_PM = "TIMESLOT_AM_PM";
    public static final String TIMESLOT_PM_AM = "TIMESLOT_PM_AM";
    public static final String TIMESLOT_PM_PM = "TIMESLOT_PM_PM";
    public static final String STAY_TIME_HOURS = "HOURS";

    public static final String DAYUSE_PER_NIGHT = "DAYUSE_PER_NIGHT";

    public static final String DAYUSE_PER_NIGHT_TAX = "DAYUSE_PER_NIGHT_TAX";

    /*Group Booking Constants*/
    public static final String ADD_BREAKFAST = "ADD_BREAKFAST";
    public static final String ADD_BREAKFAST_LUNCH_AND_DINNER = "ADD_BREAKFAST_LUNCH_AND_DINNER";
    public static final String ADD_BREAKFAST_LUNCH_OR_DINNER = "ADD_BREAKFAST_LUNCH_OR_DINNER";
    public static final String ADD_BREAKFAST_AND_LUNCH = "ADD_BREAKFAST_AND_LUNCH";
    public static final String ADD_BREAKFAST_AND_DINNER = "ADD_BREAKFAST_AND_DINNER";
    public static final String GROUP_PRICE_TEXT = "GROUP_PRICE_TEXT";
    public static final String GROUP_PRICE_TEXT_ONE_NIGHT = "GROUP_PRICE_TEXT_ONE_NIGHT";
    public static final String GROUP_PRICE_TEXT_ONE_ROOM = "GROUP_PRICE_TEXT_ONE_ROOM";
    public static final String GROUP_PRICE_TEXT_ONE_NIGHT_ONE_ROOM = "GROUP_PRICE_TEXT_ONE_NIGHT_ONE_ROOM";

    public static final String GROUP_PRICE_TEXT_WITH_TAX = "GROUP_PRICE_TEXT_WITH_TAX";
    public static final String GROUP_PRICE_TEXT_ONE_NIGHT_WITH_TAX = "GROUP_PRICE_TEXT_ONE_NIGHT_WITH_TAX";
    public static final String GROUP_PRICE_TEXT_ONE_ROOM_WITH_TAX = "GROUP_PRICE_TEXT_ONE_ROOM_WITH_TAX";
    public static final String GROUP_PRICE_TEXT_ONE_NIGHT_ONE_ROOM_WITH_TAX = "GROUP_PRICE_TEXT_ONE_NIGHT_ONE_ROOM_WITH_TAX";

    public static final String SAVING_PERC_TEXT = "SAVING_PERC_TEXT";
    public static final String PAYMENT_PLAN_PENALTY_TEXT = "PAYMENT_PLAN_PENALTY_TEXT";
    public static final String BEDS_SELLABLE_LABEL = "BEDS_SELLABLE_LABEL";
    public static final String ROOMS_SELLABLE_LABEL = "ROOMS_SELLABLE_LABEL";
    public static final String HOSTEL_BEDS_AVAILABLE_TEXT = "HOSTEL_BEDS_AVAILABLE_TEXT";
    public static final String HOSTEL_ROOMS_AVAILABLE_TEXT = "HOSTEL_ROOMS_AVAILABLE_TEXT";

    public static final String MYBIZ_QUICKPAY_TITLE = "MYBIZ_QUICKPAY_TITLE";

    public static final String MYBIZ_QUICKPAY_SUBTITLE = "MYBIZ_QUICKPAY_SUBTITLE";

    public static final String MYBIZ_QUICKPAY_TEXT_CURRENCY = "MYBIZ_QUICKPAY_TEXT_CURRENCY";

    public static final String MYBIZ_QUICKPAY_CTA_DESKTOP = "MYBIZ_QUICKPAY_CTA_DESKTOP";

    public static final String MYBIZ_QUICKPAY_CTA_APPS = "MYBIZ_QUICKPAY_CTA_APPS";

    public static final String STATIC_ROOM_TAG = "STATIC_ROOM_TAG";

    public static final String CHECKOUT_ERROR_PRICE_CHANGE_CODE_TITLE_KEY = "CHECKOUT_ERROR_PRICE_CHANGE_CODE_TITLE_KEY";

    public static final String CHECKOUT_ERROR_PRICE_CHANGE_CODE_SUBTITLE_KEY = "CHECKOUT_ERROR_PRICE_CHANGE_CODE_SUBTITLE_KEY";

    public static final String CHECKOUT_ERROR_SOLD_OUT_CODE_TITLE_KEY = "CHECKOUT_ERROR_SOLD_OUT_CODE_TITLE_KEY";

    public static final String CHECKOUT_ERROR_SOLD_OUT_CODE_SUBTITLE_KEY = "CHECKOUT_ERROR_SOLD_OUT_CODE_SUBTITLE_KEY";

    public static final String CORP_APPROVAL_REQUEST_EXPIRY_CODE_TITLE = "CORP_APPROVAL_REQUEST_EXPIRY_CODE_TITLE";

    public static final String CORP_APPROVAL_REQUEST_EXPIRY_CODE_SUBTITLE = "CORP_APPROVAL_REQUEST_EXPIRY_CODE_SUBTITLE";

    public static final String CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_TITLE = "CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_TITLE";

    public static final String CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_SUBTITLE = "CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_SUBTITLE";

    public static final String APP_CHECKOUT_ERROR_PRICE_CHANGE_CODE_TITLE_KEY = "APP_CHECKOUT_ERROR_PRICE_CHANGE_CODE_TITLE_KEY";

    public static final String APP_CHECKOUT_ERROR_PRICE_CHANGE_CODE_SUBTITLE_KEY = "APP_CHECKOUT_ERROR_PRICE_CHANGE_CODE_SUBTITLE_KEY";

    public static final String APP_CHECKOUT_ERROR_SOLD_OUT_CODE_TITLE_KEY = "APP_CHECKOUT_ERROR_SOLD_OUT_CODE_TITLE_KEY";

    public static final String APP_CHECKOUT_ERROR_SOLD_OUT_CODE_SUBTITLE_KEY = "APP_CHECKOUT_ERROR_SOLD_OUT_CODE_SUBTITLE_KEY";

    public static final String APP_CORP_APPROVAL_REQUEST_EXPIRY_CODE_TITLE = "APP_CORP_APPROVAL_REQUEST_EXPIRY_CODE_TITLE";

    public static final String APP_CORP_APPROVAL_REQUEST_EXPIRY_CODE_SUBTITLE = "APP_CORP_APPROVAL_REQUEST_EXPIRY_CODE_SUBTITLE";

    public static final String APP_CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_TITLE = "APP_CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_TITLE";

    public static final String APP_CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_SUBTITLE = "APP_CORP_ROOM_NOT_AVAILABLE_ERROR_CODE_SUBTITLE";

    public static final String GENERIC_ERROR_TITLE = "GENERIC_ERROR_TITLE";

    public static final String GENERIC_ERROR_SUBTITLE = "GENERIC_ERROR_SUBTITLE";

    public static final String CORP_TXT_KEY_EXPIRED_CODE_TITLE = "CORP_TXT_KEY_EXPIRED_CODE_TITLE";

    public static final String CORP_TXT_KEY_EXPIRED_CODE_SUBTITLE = "CORP_TXT_KEY_EXPIRED_CODE_SUBTITLE";


    public static final String APP_CORP_TXT_KEY_EXPIRED_CODE_TITLE = "APP_CORP_TXT_KEY_EXPIRED_CODE_TITLE";

    public static final String APP_CORP_TXT_KEY_EXPIRED_CODE_SUBTITLE = "APP_CORP_TXT_KEY_EXPIRED_CODE_SUBTITLE";

    public static final String BOOK_NOW_PERSUASION_HOVER_TITLE = "BOOK_NOW_PERSUASION_HOVER_TITLE";
    public static final String BOOK_NOW_PERSUASION_HOVER_GENERIC_TITLE = "BOOK_NOW_PERSUASION_HOVER_GENERIC_TITLE";
    public static final String BOOK_NOW_PERSUASION_HOVER_SUB_TITLE = "BOOK_NOW_PERSUASION_HOVER_SUB_TITLE";
    public static final String BOOK_NOW_PERSUASION_TITLE = "BOOK_NOW_PERSUASION_TITLE";
    public static final String BOOK_NOW_THANK_YOU_PAYMENT_HEADING = "BOOK_NOW_THANK_YOU_PAYMENT_HEADING";
    public static final String BOOK_NOW_THANK_YOU_HEADING = "BOOK_NOW_THANK_YOU_HEADING";
    public static final String BOOK_NOW_THANK_YOU_SUB_HEADING = "BOOK_NOW_THANK_YOU_SUB_HEADING";
    public static final String BOOK_NOW_THANK_YOU_CTA_TEXT = "BOOK_NOW_THANK_YOU_CTA_TEXT";
    public static final String FILTER_CTA_TEXT = "FILTER_CTA_TEXT";

    public static final String TAXES_EXCLUDED_TEXT_REVIEW_PAGE = "TAXES_EXCLUDED_TEXT_REVIEW_PAGE";

    public static final String LOYALTY_OFFER_TEXT = "LOYALTY_OFFER_TEXT";

    public static final String CASHBACK_OFFER_TEXT = "CASHBACK_OFFER_TEXT";

    public static final String CASHBACK_OFFER_TEXT_PWA = "CASHBACK_OFFER_TEXT_PWA";
    public static final String BEACHFRONT_CATEGORY_USP_DETAILS_TEXT = "BEACHFRONT_CATEGORY_USP_DETAILS_TEXT";

    public static final String SPACE_OCCUPANCY_EXTRA_GUESTS = "SPACE_OCCUPANCY_EXTRA_GUESTS";
    public static final String SPACE_SINGLE_OCCUPANCY_EXTRA_GUESTS = "SPACE_SINGLE_OCCUPANCY_EXTRA_GUESTS";

    public static final String BATHROOM = "BATHROOM";
    public static final String BATHROOMS = "BATHROOMS";
    public static final String KITCHENETTE_TITLE = "KITCHENETTE_TITLE";
    public static final String BED = "BED";
    public static final String BEDS = "BEDS";
    public static final String PAYMENT_INR_MSG = "PAYMENT_INR_MSG";

    //New Listing page design changes as per PDO: PRNT send by client
    public static final String GROUP_PRICE_TEXT_ONE_NIGHT_ONE_ROOM_NEW_DESIGN = "GROUP_PRICE_TEXT_ONE_NIGHT_ONE_ROOM_NEW_DESIGN";
    public static final String GROUP_PRICE_TEXT_NEW_DESIGN = "GROUP_PRICE_TEXT_NEW_DESIGN";
    public static final String GROUP_PRICE_TEXT_ONE_NIGHT_NEW_DESIGN = "GROUP_PRICE_TEXT_ONE_NIGHT_NEW_DESIGN";
    public static final String GROUP_PRICE_TEXT_ONE_ROOM_NEW_DESIGN = "GROUP_PRICE_TEXT_ONE_ROOM_NEW_DESIGN";
    public static final String GROUP_PRICE_LABEL = "PRICE_LABEL";
    public static final String TAX_MSG = "TAX_MSG";

    public static final String GROUP_PRICE_TEXT_ONE_NIGHT_ALT_ACCO = "GROUP_PRICE_TEXT_ONE_NIGHT_ALT_ACCO";
    public static final String GROUP_PRICE_TEXT_MULTI_NIGHT_ALT_ACCO = "GROUP_PRICE_TEXT_MULTI_NIGHT_ALT_ACCO";

    public static final String SHORTSTAYS_TOWARDS = "SHORTSTAYS_TOWARDS_TEXT";
    public static final String DRIVING_DURATION_ZONE_SHORTSTAY = "DRIVING_DURATION_ZONE_SHORTSTAY";
    public static final String SHORTSTAY_LOCATION_PERSUASION_PREFIX_TEXT_FONT = "SHORTSTAY_LOCATION_PERSUASION_PREFIX_TEXT";
    public static final String SHORTSTAY_LOCATION_PERSUASION_SUFFIX_TEXT_FONT = "SHORTSTAY_LOCATION_PERSUASION_SUFFIX_TEXT";
    public static final String SHORTSTAY_LOCATION_PERSUASION_PREFIX_TEXT_LISTING_MAP = "SHORTSTAY_LOCATION_PERSUASION_PREFIX_TEXT_LISTING_MAP";

    public static final String SHORTSTAY_LOCATION_PERSUASION_SUFFIX__LISTING_MAP = "SHORTSTAY_LOCATION_PERSUASION_SUFFIX_LISTING_MAP";

    // HTL-40907: Translation constants for negotiated rates for hotels.
    public static final String INSTANT_FARE_INFO_TITLE = "INSTANT_FARE_INFO_TITLE";

    public static final String INSTANT_FARE_INFO_MYB_NEW_DETAILS_TITLE = "INSTANT_FARE_INFO_MYB_NEW_DETAILS_TITLE";

    public static final String INSTANT_FARE_INFO_TITLE_MOBILE = "INSTANT_FARE_INFO_TITLE_MOBILE";

    public static final String INSTANT_FARE_INFO_HEADER = "INSTANT_FARE_INFO_HEADER";

    public static final String INSTANT_FARE_INFO_SUBHEADER = "INSTANT_FARE_INFO_SUBHEADER";

    public static final String INSTANT_BOOKING_FARES = "INSTANT_BOOKING_FARES";

    public static final String INSTANT_BOOKING_CONFIRMATION = "INSTANT_BOOKING_CONFIRMATION";

    public static final String CHANGE_ROOM_MOBILE = "CHANGE_ROOM";

    public static final String CHANGE_ROOM_DESKTOP = "CHANGE_ROOM_DESKTOP";

    public static final String INSTANT_BOOKING = "INSTANT_BOOKING";

    public static final String PENDING_CARD_INFO_TITLE = "PENDING_CARD_INFO_TITLE";

    public static final String PENDING_CARD_INFO_SUBTITLE = "PENDING_CARD_INFO_SUBTITLE";

    public static final String PENDING_CARD_INFO_SUBTITLE_MOBILE = "PENDING_CARD_INFO_SUBTITLE_MOBILE";

    public static final String DELAYED_CONFIRMATION_PERSUASION_TITLE = "DELAYED_CONFIRMATION_PERSUASION_TITLE";

    public static final String DELAYED_CONFIRMATION_PERSUASION_MYB_NEW_DETAILS_TITLE = "DELAYED_CONFIRMATION_PERSUASION_MYB_NEW_DETAILS_TITLE";

    public static final String SPECIAL_FARE_TAG = "SPECIAL_FARE_TAG";

    public static final String SPECIAL_FARE_TAG_MOBILE = "SPECIAL_FARE_TAG_MOBILE";

    public static final String SPECIAL_FARE_TITLE_TEXT = "SPECIAL_FARE_TITLE_TEXT";

    public static final String SPECIAL_FARE_TITLE_TEXT_NOT_RTB = "SPECIAL_FARE_TITLE_TEXT_NOT_RTB";

    public static final String BOOKING_CONFIRMATION_TEXT_DESKTOP = "BOOKING_CONFIRMATION_TEXT_DESKTOP";

    public static final String BOOKING_CONFIRMATION_TEXT_NEW_DETAILS_DESKTOP = "BOOKING_CONFIRMATION_TEXT_NEW_DETAILS_DESKTOP";

    public static final String BOOKING_CONFIRMATION_TEXT_MOBILE = "BOOKING_CONFIRMATION_TEXT_MOBILE";

    public static final String BOOKING_CONFIRMATION_TEXT_LISTING = "BOOKING_CONFIRMATION_TEXT_LISTING";

    public static final String BOOKING_CONFIRMATION_TEXT_LISTING_MOBILE = "BOOKING_CONFIRMATION_TEXT_LISTING_MOBILE";

    public static final String PROCEED_BOOKING_TEXT = "PROCEED_BOOKING_TEXT";

    public static final String SPECIAL_FARE_TEXT_MOBILE = "SPECIAL_FARE_TEXT_MOBILE";

    public static final String SPECIAL_FARE_SUBTEXT_MOBILE = "SPECIAL_FARE_SUBTEXT_MOBILE";

    //Food & Dining Constants
    public static final String MEAL_FOR_KIDS = "MEAL_FOR_KIDS";   //Kid's meal can be prepared on request

    public static final String FLYER_DESCRIPTION = "FLYER_DESCRIPTION";
    public static final String BUS_DESCRIPTION = "BUS_DESCRIPTION";
    public static final String TRAIN_DESCRIPTION = "TRAIN_DESCRIPTION";
    public static final String PRICE_DROP_PERSUASION_PLAIN = "PRICE_DROP_PERSUASION_PLAIN";
    public static final String PRICE_DROP_PERSUASION_PERCENTAGE = "PRICE_DROP_PERSUASION_PERCENTAGE";


    //Supplier deals constants
    public static final String EARLY_BIRD_DEAL_LABEL = "EARLY_BIRD_DEAL_LABEL";

    public static final String LAST_MINUTE_PRICE_LABEL = "LAST_MINUTE_PRICE_LABEL";

    public static final String LAST_MINUTE_PRICE_KEY = "LAST_MINUTE_PRICE_KEY";

    public static final String EARLY_BIRD_DEAL_KEY = "EARLY_BIRD_DEAL_KEY";

    public static final String SUPPLIER = "SUPPLIER";

    public static final String PRIMARY_OFFER_DESCRIPTION = "PRIMARY_OFFER_DESCRIPTION";
    public static final String PRIMARY_OFFER_DESCRIPTION_LAST_MINUTE = "PRIMARY_OFFER_DESCRIPTION_LAST_MINUTE";

    public static final String CANCEL_POLICY_BNPL_PEAK_SUBTEXT = "CANCEL_POLICY_BNPL_PEAK_SUBTEXT_V2";
    public static final String CANCEL_POLICY_BNPL_NONPEAK_SUBTEXT = "CANCEL_POLICY_BNPL_NONPEAK_SUBTEXT_V2";

    public static final String TCS_APPLICABLE_TEXT_IH = "TCS_TEXT_IH";
    public static final String TCS_APPLICABLE_CTA_IH = "TCS_CTA_IH";
    public static final String INCLUSIVE_OF_TAXES_AND_FEES = "INCLUSIVE_OF_TAXES_AND_FEES";
    public static final String EXCLUSIVE_OF_TAXES_AND_FEES = "EXCLUSIVE_OF_TAXES_AND_FEES";
    public static final String PRICE_FOOTER_CTA_TEXT = "PRICE_FOOTER_CTA_TEXT";
    public static final String PRICE_FOOTER_AMOUNT_TEXT = "PRICE_FOOTER_AMOUNT_TEXT";

    public static final String PRICE_FOOTER_CTA_TEXT_GCC = "PRICE_FOOTER_CTA_TEXT_GCC";
    public static final String PRICE_FOOTER_AMOUNT_TEXT_GCC = "PRICE_FOOTER_AMOUNT_TEXT_GCC";
    public static final String PRICE_FOOTER_TEXT = "PRICE_FOOTER_TEXT";
    public static final String PRICE_FOOTER_SUBTEXT = "PRICE_FOOTER_SUBTEXT";
    public static final String ADD_ONS = "ADD_ONS";
    public static final String INCLUSIVE_OF_TAXES = "INCLUSIVE_OF_TAXES";
    public static final String EXCLUSIVE_OF_TAXES = "EXCLUSIVE_OF_TAXES";
    public static final String FREE_KID_CODE_TEXT = "FREE_KID_CODE_TEXT";

    public static final String SPOKEN_LANGUAGE_HEADER = "SPOKEN_LANGUAGE_HEADER";
    public static final String RATEPLAN_CANCELLATION_POLICY = "RATEPLAN_CANCELLATION_POLICY";
    public static final String APPLICABLE_REFUND_TEXT = "APPLICABLE_REFUND";
    public static final String RTB_BNPL_0_THANK_YOU_TEXT = "RTB_BNPL_0_THANK_YOU_TEXT";

    public static final String HOSTEL_TITLE = "HOSTEL_TITLE";
    public static final String REVIEW_SCARCITY_TITLE = "REVIEW_SCARCITY_TITLE";
    public static final String REVIEW_COUPON_PERSUASION_TITLE = "REVIEW_COUPON_PERSUASION_TITLE";

    public static final String PACKAGE_BENEFITS_TEXT = "PACKAGE_BENEFITS_TEXT_V2";
    public static final String OCCASION_PACKAGE_BENEFITS_TEXT = "OCCASION_PACKAGE_BENEFITS_TEXT";
    public static final String PACKAGE_BENEFITS_TEXT_NEW_PAGE_DT = "PACKAGE_BENEFITS_TEXT_NEW_PAGE_DT_V2";
    public static final String ENJOY_PACKAGE_BENEFITS = "ENJOY_PACKAGE_BENEFITS";
    public static final String BEDROOM_TITLE = "BEDROOM_TITLE";
    public static final String SINGLE_BEDROOM_TITLE = "SINGLE_BEDROOM_TITLE";
    public static final String DEFAULT_PROPERTY_LAYOUT_TEXT = "DEFAULT_PROPERTY_LAYOUT_TEXT";
    public static final String SINGLE_ENTIRE_PROPERTY_LAYOUT_TEXT = "SINGLE_ENTIRE_PROPERTY_LAYOUT_TEXT";
    public static final String SINGLE_ENTIRE_PROPERTY_GENERIC_TEXT = "SINGLE_ENTIRE_PROPERTY_GENERIC_TEXT";
    public static final String SINGLE_ENTIRE_PROPERTY_ZERO_BEDROOM_TEXT = "SINGLE_ENTIRE_PROPERTY_ZERO_BEDROOM_TEXT";
    public static final String MULTIPLE_ENTIRE_PROPERTY_LAYOUT_TEXT = "MULTIPLE_ENTIRE_PROPERTY_LAYOUT_TEXT";
    public static final String MULTIPLE_ENTIRE_PROPERTY_GENERIC_TEXT = "MULTIPLE_ENTIRE_PROPERTY_GENERIC_TEXT";
    public static final String HOSTEL_LAYOUT_TEXT = "HOSTEL_LAYOUT_TEXT";
    public static final String HOSTEL_LAYOUT_TEXT_ZERO_BEDROOM_COUNT = "HOSTEL_LAYOUT_TEXT_ZERO_BEDROOM_COUNT";
    public static final String MULTIPLE_HETERO_PROPERTY_LAYOUT_TEXT = "MULTIPLE_HETERO_PROPERTY_LAYOUT_TEXT";
    public static final String FLIGHT_BOOKING_REASON = "FLIGHT_BOOKING_REASON";
    public static final String EXCLUSIVE_BENEFITS_TITLE = "EXCLUSIVE_BENEFITS_TITLE";

    public static final String MMT_EXCLUSIVE_BENEFITS = "MMT_EXCLUSIVE_BENEFITS";
    public static final String SPECIAL_BENEFITS_TITLE = "SPECIAL_BENEFITS_TITLE";
    public static final String DEAL_TITLE = "DEAL_TITLE";
    public static final String FOREX_DEAL_POPUP_SUBTITLE = "FOREX_DEAL_POPUP_SUBTITLE";
    public static final String FOREX_DEAL_TITLE_THANKU_PAGE = "FOREX_DEAL_TITLE_THANKU_PAGE";
    public static final String FOREX_DEAL_TITLE_REVIEW_PAGE_DT = "FOREX_DEAL_TITLE_REVIEW_PAGE_DT";

    public static final String FOREX_CAB_CARD_TITLE_REVIEW_PAGE = "FOREX_CAB_CARD_TITLE_REVIEW_PAGE";//"You have unlocked <b>FREE</b> addons!"
    public static final String FOREX_CAB_CARD_SUBTEXT_REVIEW_PAGE = "FOREX_CAB_CARD_SUBTEXT_REVIEW_PAGE";//"Details will be shared after your booking is confirmed."
    public static final String FOREX_CAB_CARD_TITLE_THANKYOU_PAGE = "FOREX_CAB_CARD_TITLE_THANKYOU_PAGE";//"FREE ADD-ONS UNLOCKED!"
    public static final String CAB_CARD_TITLE="CAB_CARD_TITLE";//"300 instant discount on International Airport Cabs"
    public static final String CAB_CARD_SUBTEXT="CAB_CARD_SUBTEXT";//"Guaranteed pickup from airport to hotel."
    public static final String FOREX_CARD_TITLE="FOREX_CARD_TITLE";//"1200 Cashback on Forex Card and currency."
    public static final String FOREX_CARD_SUBTEXT="FOREX_CARD_SUBTEXT";//"Get best rates at zero forex markup."
    public static final String FOREX_CAB_CARD_CARDACTION_MORE="FOREX_CAB_CARD_CARDACTION_MORE";//"More"
    public static final String FOREX_CAB_CARD_CARDACTION_CLAIM="FOREX_CAB_CARD_CARDACTION_CLAIM";//"Claim"
    public static final String GIFT_CARD_CARDACTION_CLAIM_TITLE="GIFT_CARD_CARDACTION_CLAIM_TITLE";//"Claim"
    public static final String GIFT_CARD_CARDACTION_CLAIM_SUBTITLE="GIFT_CARD_CARDACTION_CLAIM_SUBTITLE";//"Claim"

    public static final String FOREX_OFFER_TNC_TEXT = "FOREX_OFFER_TNC_TEXT";
    public static final String EARLY_MORNING_TIME_TEXT = "EARLY_MORNING_TIME_TEXT";
    public static final String DAY_TIME_TEXT = "DAY_TIME_TEXT";
    public static final String EARLY_NIGHT_TIME_TEXT = "EARLY_NIGHT_TIME_TEXT";
    public static final String LATE_NIGHT_TIME_TEXT = "LATE_NIGHT_TIME_TEXT";
    public static final String INDIANESS_HOVER_TITLE = "INDIANESS_HOVER_TITLE";
    public static final String INDIANESS_HOVER_SUBTITLE = "INDIANESS_HOVER_SUBTITLE";
    public static final String DEVOTEE_HOVER_TITLE = "DEVOTEE_HOVER_TITLE";
    public static final String DEVOTEE_HOVER_SUBTITLE = "DEVOTEE_HOVER_SUBTITLE";
    public static final String FILTERED_TREEL_HEADING = "FILTERED_TREEL_HEADING";
    public static final String UNFILTERED_TREEL_HEADING = "UNFILTERED_TREEL_HEADING";
    public static final String FILTERED_TREEL_TITLE = "FILTERED_TREEL_TITLE";
    public static final String UNFILTERED_TREEL_TITLE = "UNFILTERED_TREEL_TITLE";
    public static final String FILTERED_TREEL_SUB_TITLE = "FILTERED_TREEL_SUB_TITLE";
    public static final String UNFILTERED_TREEL_SUB_TITLE = "UNFILTERED_TREEL_SUB_TITLE";
    public static final String POPULAR_LOCATION = "POPULAR_LOCATION";
    public static final String FIND_INSPIRATION = "FIND_INSPIRATION";

    public static final String HOTEL_TITLE = "HOTEL_TITLE";
    public static final String RESORT_TITLE = "RESORT_TITLE";
    public static final String ROOM_SELLABLE_TYPE = "ROOM_SELLABLE_TYPE";
    public static final String STAY_SELLABLE_TYPE = "STAY_SELLABLE_TYPE";
    public static final String BED_SELLABLE_TYPE = "BED_SELLABLE_TYPE";
    public static final String MEAL_UPSELL_SUBTEXT = "MEAL_UPSELL_SUBTEXTV2";
    public static final String DONATION_PRICE_LABLE = "DONATION_PRICE_LABLE";

    public static final String NO_COST_EMI_AUTO_APPLIED_TITLE_TEXT = "NO_COST_EMI_AUTO_APPLIED_TITLE_TEXT";
    public static final String NO_COST_EMI_FULL_PAYMENT_SUB_TITLE_TEXT = "NO_COST_EMI_FULL_PAYMENT_SUB_TITLE_TEXT";
    public static final String NO_COST_EMI_PAYMENT_TITLE_TEXT = "NO_COST_EMI_PAYMENT_TITLE_TEXT";
    public static final String NO_COST_EMI_PAYMENT_SUB_TITLE_TEXT = "NO_COST_EMI_PAYMENT_SUB_TITLE_TEXT";
    public static final String NO_COST_EMI_TITLE = "NO_COST_EMI_TITLE";
    public static final String NO_COST_EMI_DETAIL_PAGE_TEXT = "NO_COST_EMI_DETAIL_PAGE_TEXT";
    public static final String NO_COST_EMI = "NO_COST_EMI";
    public static final String PAY_FULL_AMOUNT_NOW_TITLE_TEXT = "PAY_FULL_AMOUNT_NOW_TITLE_TEXT";
    public static final String PAY_FULL_AMOUNT_NOW_SUB_TITLE_TEXT = "PAY_FULL_AMOUNT_NOW_SUB_TITLE_TEXT";
    public static final String VIEW_PLANS_CTA_TEXT = "VIEW_PLANS_CTA_TEXT";
    public static final String PER_NIGHT_TEXT = "PER_NIGHT_TEXT";
    public static final String ROOM_UPGRADED_TAG_TITLE = "ROOM_UPGRADED_TAG_TITLE";
    public static final String UPGRADE_RATE_PLAN_POPUP_TITLE = "UPGRADE_RATE_PLAN_POPUP_TITLE";
    public static final String UPGRADE_RATE_PLAN_POPUP_TITLE_BOTH = "UPGRADE_RATE_PLAN_POPUP_TITLE_BOTH";
    public static final String UPGRADE_RATE_PLAN_POPUP_TITLE_MEAL = "UPGRADE_RATE_PLAN_POPUP_TITLE_MEAL";
    public static final String UPGRADE_RATE_PLAN_POPUP_TITLE_GCC = "UPGRADE_RATE_PLAN_POPUP_TITLE_GCC";
    public static final String UPGRADE_RATE_PLAN_POPUP_TITLE_BOTH_GCC = "UPGRADE_RATE_PLAN_POPUP_TITLE_BOTH_GCC";
    public static final String UPGRADE_RATE_PLAN_POPUP_TITLE_MEAL_GCC = "UPGRADE_RATE_PLAN_POPUP_TITLE_MEAL_GCC";
    public static final String UPGRADE_RATE_PLAN_POPUP_TITLE_ROOM = "UPGRADE_RATE_PLAN_POPUP_TITLE_ROOM";
    public static final String UPGRADE_RATE_PLAN_POPUP_DISCLAIMER = "UPGRADE_RATE_PLAN_POPUP_DISCLAIMER";
    public static final String UPGRADE_RATE_PLAN_ORIGINAL_PRICE = "UPGRADE_RATE_PLAN_ORIGINAL_PRICE";
    public static final String SELECTED_TITLE = "SELECTED_TITLE";
    public static final String UPGRADED_TITLE = "UPGRADED_TITLE";
    public static final String UPGRADED_TITLE_MEAL_AND_ROOM = "UPGRADED_TITLE_MEAL_AND_ROOM";
    public static final String UPGRADED_TITLE_MEAL = "UPGRADED_TITLE_MEAL";
    public static final String UPGRADED_TITLE_ROOM = "UPGRADED_TITLE_ROOM";
    public static final String MEAL_UPSELL_DESC_TEXT = "MEAL_UPSELL_DESC_TEXT";
    public static final String POLICY_MISMATCH = "POLICY_MISMATCH_IDENTIFIED";
    public static final String VISTARA_DESCRIPTION = "VISTARA_DESCRIPTION";
    public static final String VISTARA_TEXT = "VISTARA";
    public static final String FIRST_FIVE_BOOKING_HEADING = "FIRST_FIVE_BOOKING_HEADING"; // Only For Desktop
    public static final String FIRST_FIVE_BOOKING_DESC_APPS = "FIRST_FIVE_BOOKING_DESC_APPS";
    public static final String FIRST_FIVE_BOOKING_DESC_DESKTOP = "FIRST_FIVE_BOOKING_DESC_DESKTOP";
    public static final String PROPERTY_DISCOUNT_PERSUASION_DESC_APPS = "PROPERTY_DISCOUNT_PERSUASION_DESC_APPS";
    public static final String PROPERTY_DISCOUNT_PERSUASION_DESC_DESKTOP = "PROPERTY_DISCOUNT_PERSUASION_DESC_DESKTOP";
    public static final String NEXT_BEST_RATEPLAN_GCC_TITLE_TEXT = "NEXT_BEST_RATEPLAN_GCC_TITLE_TEXTV2";
    public static final String SEE_ALL_CTA_TITLE = "SEE_ALL_CTA_TITLE";
    public static final String LISTING_BOTTOMSHEET_CALLBACK_INFOTEXT = "LISTING_BOTTOMSHEET_CALLBACK_INFOTEXT";
    public static final String CTA_360_TEXT = "CTA_360_TEXT";
    public static final String VIEW_IMAGE = "VIEW_IMAGE";
    public static final String VIEW_360_IMAGE = "VIEW_360_IMAGE";
    public static final String AMENDABLE_DETAIL_TEXT = "AMENDABLE_DETAIL_TEXT";
    public static final String AMENDABLE_REVIEW_TEXT = "AMENDABLE_REVIEW_TEXT";
    public static final String AMENDABLE_TY_TEXT = "AMENDABLE_TY_TEXT";

    public static final String GCC_BNPL_FILTER_TITLE = "GCC_BNPL_FILTER_TITLE";

    public static final String CALL_TO_BOOK_REQUEST_SUBMITTED = "CALL_TO_BOOK_REQUEST_SUBMITTED";
    public static final String CALL_TO_BOOK_REQUEST_SUBMITTED_DESKTOP = "CALL_TO_BOOK_REQUEST_SUBMITTED_DESKTOP";
    public static final String CALL_TO_BOOK_REQUEST_LIMIT_EXCEEDED = "CALL_TO_BOOK_REQUEST_LIMIT_EXCEEDED";
    public static final String AP_MEAL_TEXT = "AP_MEAL_TEXT";
    public static final String CP_MEAL_TEXT = "CP_MEAL_TEXT";
    public static final String MAP_MEAL_TEXT = "MAP_MEAL_TEXT";

    // Guidelines constants
    public static final String REVIEW_TEXT_TITLE = "REVIEW_TEXT_TITLE";
    public static final String REVIEW_IMAGE_TITLE = "REVIEW_IMAGE_TITLE";
    public static final String BASE_TITLE = "BASE_TITLE";

    // OnBoardingData constants
    public static final String REVIEW_TITLE = "REVIEW_TITLE";
    public static final String REVIEW_SUBTITLE = "REVIEW_SUBTITLE";
    public static final String REVIEW_EDIT_TITLE = "REVIEW_EDIT_TITLE";
    public static final String REVIEW_EDIT_TEXT = "REVIEW_EDIT_TEXT";
    public static final String REVIEW_TEXT = "REVIEW_TEXT";
    public static final String REVIEW_DESKTOP_TEXT = "REVIEW_DESKTOP_TEXT";

    // Configs constants
    public static final String REVIEW_ESTIMATED_TIME = "REVIEW_ESTIMATED_TIME";
    public static final String REVIEW_EXIT_REVIEW_STRING = "REVIEW_EXIT_REVIEW_STRING";
    public static final String REVIEW_EDIT_EXIT_REVIEW_STRING = "REVIEW_EDIT_EXIT_REVIEW_STRING";

    public static final String REVIEW_NONINCENTIVE_TITLE = "REVIEW_NONINCENTIVE_TITLE";
    public static final String REVIEW_NONINCENTIVE_TEXT = "REVIEW_NONINCENTIVE_TEXT";
    public static final String REVIEW_DESKTOP_NONINCENTIVE_TEXT = "REVIEW_DESKTOP_NONINCENTIVE_TEXT";
    public static final String REVIEW_EXIT_REVIEW_NONINCENTIVE_STRING = "REVIEW_EXIT_REVIEW_NONINCENTIVE_STRING";
    public static final String REVIEW_DISCAMOUNT_TITLE = "REVIEW_DISCAMOUNT_TITLE";
    public static final String REVIEW_DISCAMOUNT_TEXT = "REVIEW_DISCAMOUNT_TEXT";
    public static final String REVIEW_DESKTOP_DISCAMOUNT_TEXT = "REVIEW_DESKTOP_DISCAMOUNT_TEXT";
    public static final String REVIEW_EXIT_REVIEW_DISCAMOUNT_STRING = "REVIEW_EXIT_REVIEW_DISCAMOUNT_STRING";


    public static final String IN_PROGRESS_REVIEW = "IN_PROGRESS";
    public static final String REVIEW_COMPLETE = "REVIEW_COMPLETE";
    public static final String SEGMENT_ID = "SEGMENT_ID";
    public static final String LOCUS_TYPE = "LOCUS_TYPE";
    public static final String FLEXI_CANCEL_DETAIL_BANNER_TITLE = "FLEXI_CANCEL_DETAIL_BANNER_TITLE";
    public static final String FLEXI_CANCEL_DETAIL_BANNER_DESC = "FLEXI_CANCEL_DETAIL_BANNER_DESC";
    public static final String FLEXI_CANCEL_LEAN_MORE = "FLEXI_CANCEL_LEAN_MORE";
    public static final String FLEXI_CANCEL_THANKYOU_CANCELLATION_POLICY_TEXT = "FLEXI_CANCEL_THANKYOU_CANCELLATION_POLICY_TEXT";
    public static final String TAX_AND_SERVICE_FEE = "TAX_AND_SERVICE_FEE";
    public static final String WISHLIST_DETAIL_TAX_AND_FEE = "WISHLIST_DETAIL_TAX_AND_FEE";
    public static final String VIEW_ALL_PACKAGES = "VIEW_ALL_PACKAGES";
    public static final String ABO_BANNER_INFO_TEXT = "ABO_BANNER_INFO_TEXT";

    public static final String NO_COST_EMI_COUPON_CODE_AND_DISCOUNT_TEXT = "NO_COST_EMI_COUPON_CODE_AND_DISCOUNT_TEXT";
    public static final String EMI_L2_PAGE_TITLE = "EMI_L2_PAGE_TITLE";
    public static final String EMI_L1_ALERT_TEXT = "EMI_L1_ALERT_TEXT";
    public static final String EMI_L1_PAGE_TITLE = "EMI_L1_PAGE_TITLE";
    public static final String EMI_L1_PAGE_SUB_TITLE = "EMI_L1_PAGE_SUB_TITLE";
    public static final String EMI_L1_FARE_DESC = "EMI_L1_FARE_DESC";
    public static final String LONG_STAY_BENEFITS_HEADING = "LONG_STAY_BENEFITS_HEADING";
    public static final String PLUS_MORE = "PLUS_MORE";
    public static final String MORE_DETAILS_TEXT = "MORE_DETAILS_TEXT";
    public static final String FLYER_EXCLUSIVE_DEAL_TEXT = "FLYER_EXCLUSIVE_DEAL_TEXT";
    public static final String STREET_VIEW_TITLE_TEXT = "STREET_VIEW_TITLE_TEXT";
    public static final String STREET_VIEW_SUBTITLE_TEXT = "STREET_VIEW_SUBTITLE_TEXT";
    public static final String APP_INSTALL_TEXT = "APP_INSTALL_TEXT";
    public static final String CAB_CARD_DISCOUNT_SUB_TEXT = "CAB_CARD_DISCOUNT_SUB_TEXT";
    public static final String FOREX_CARD_DISCOUNT_SUB_TEXT = "FOREX_CARD_DISCOUNT_SUB_TEXT";
    public static final String FOREX_CARD_CASHBACK_SUB_TEXT = "FOREX_CARD_CASHBACK_SUB_TEXT";
    public static final String APP_INSTALL_BUTTON_TEXT = "APP_INSTALL_BUTTON_TEXT";

    public static final String FLEXIBLE_ROOMS_FILTER_VALUE = "FLEXIBLE_ROOMS_FILTER_VALUE";
    public static final String FLEXIBLE_ROOMS_FILTER_TITLE = "FLEXIBLE_ROOMS_FILTER_TITLE";

    public static final String EXACT_ROOM_RECOMMENDATION_TITLE_TEXT_SINGLE = "EXACT_ROOM_RECOMMENDATION_TITLE_TEXT_SINGLE";

    public static final String EXACT_ROOM_RECOMMENDATION_TITLE_TEXT_MULTI = "EXACT_ROOM_RECOMMENDATION_TITLE_TEXT_MULTI";

    public static final String EXACT_ROOM_LANDING_TEXT = "EXACT_ROOM_LANDING_TEXT";

    public static final String FLEXIBLE_ROOM_LANDING_TEXT = "FLEXIBLE_ROOM_LANDING_TEXT";

    public static final String APP_INSTALL_DEEPLINK = "APP_INSTALL_DEEPLINK";
    public static final String LOWEST_PRICE_GUARANTEE_TITLE = "LOWEST_PRICE_GUARANTEE_TITLE";//Lowest Price Guarantee
    public static final String MMT_EXCLUSIVE_INCLUSIONS_1 = "MMT_EXCLUSIVE_INCLUSIONS_1";
    public static final String MMT_EXCLUSIVE_INCLUSIONS_2 = "MMT_EXCLUSIVE_INCLUSIONS_2";
    // ugc question error key polyglot constants
    public static final String BOOKING_NOT_FOUND_MSG = "BOOKING_NOT_FOUND_MSG";
    public static final String BOOKING_NOT_FOUND_TITLE = "BOOKING_NOT_FOUND_TITLE";
    public static final String PROGRAM_LOADING_ISSUE_MSG = "PROGRAM_LOADING_ISSUE_MSG";
    public static final String PROGRAM_LOADING_ISSUE_TITLE = "PROGRAM_LOADING_ISSUE_TITLE";
    public static final String NO_DATA_FOUND_FOR_UUID = "NO_DATA_FOUND_FOR_UUID_MSG";
    public static final String NO_DATA_FOUND_FOR_UUID_TITLE = "NO_DATA_FOUND_FOR_UUID_TITLE";

    public static final String REVIEW_LINK_EXPIRED_MSG = "REVIEW_LINK_EXPIRED_MSG";
    public static final String REVIEW_LINK_EXPIRED_TITLE = "REVIEW_LINK_EXPIRED_TITLE";
    public static final String REVIEW_SUBMISSION_TIME_LIMIT_EXCEEDED_MSG = "REVIEW_SUBMISSION_TIME_LIMIT_EXCEEDED_MSG";
    public static final String REVIEW_SUBMISSION_TIME_LIMIT_EXCEEDED_TITLE = "REVIEW_SUBMISSION_TIME_LIMIT_EXCEEDED_TITLE";
    // cta text
    public static final String CTA_TEXT = "CTA_TEXT";
    public static final String CTA_DEEPLINK_URL = "CTA_DEEPLINK_URL";

    public static final String QUESTION_GENERIC_ERROR_MSG = "QUESTION_GENERIC_ERROR_MSG";

    public static final String QUESTION_GENERIC_ERROR_TITLE = "QUESTION_GENERIC_ERROR_TITLE";
    public static final String GREEN_TAX_SHORT_DESC = "GREEN_TAX_SHORT_DESC";
    public static final String GALA_MEALS_SHORT_DESC = "GALA_MEALS_SHORT_DESC";
    public static final String MANDATORY_CHARGES_CITY_TAX_SHORT_DESC = "MANDATORY_CHARGES_CITY_TAX_SHORT_DESC";
    public static final String MANDATORY_CHARGES_SERVICE_FEE_SHORT_DESC = "MANDATORY_CHARGES_SERVICE_FEE_SHORT_DESC";
    public static final String MANDATORY_CHARGES_AMOUNT_DESC = "MANDATORY_CHARGES_AMOUNT_DESC";
    public static final String MANDATORY_CHARGES_ROOM_DESC = "MANDATORY_CHARGES_ROOM_DESC";
    public static final String MANDATORY_CHARGES_ROOM_DESC_RECOMMENDATION = "MANDATORY_CHARGES_ROOM_DESC_RECOMMENDATION";
    public static final String WELCOME_OFFER_CARDACTION_TITLE = "WELCOME_OFFER_CARDACTION_TITLE";

    public static final String ALT_DATE_BOTTOMSHEET_TITLE = "ALT_DATE_BOTTOMSHEET_TITLE";//Save upto <font color='#007e7d'><b>{currency} {delta}</b></font> per night by switching your dates!
    public static final String ALT_DATE_BOTTOMSHEET_DATA_TEXT = "ALT_DATE_BOTTOMSHEET_DATA_TEXT";//{dateRange}
    public static final String ALT_DATE_BOTTOMSHEET_DATA_PRICE_SUFFIX = "ALT_DATE_BOTTOMSHEET_DATA_PRICE_SUFFIX";//per night
    public static final String ALT_DATE_PERSUASIONS_DATA_TEXT = "ALT_DATE_PERSUASIONS_DATA_TEXT";//"Save {currency} {delta} by switching dates! Book this hotel for {currency} {price} from <b><font color='#008cff'>{dateRange}</font></b>"
    public static final String ALT_DATE_BOTTOMSHEET_DATA_SUB_TEXT = "ALT_DATE_BOTTOMSHEET_DATA_SUB_TEXT";//"You save {currency} {delta}";
    public static final String AVOID_HASSLE_COLLECTING_GST_INVOICES = "AVOID_HASSLE_COLLECTING_GST_INVOICES";
    public static final String HOTEL_CLOUD_GST_DESCRIPTION = "HOTEL_CLOUD_GST_DESCRIPTION";
    public static final String POWERED_BY_HOTEL_CLOUD = "POWERED_BY_HOTEL_CLOUD";

    public static final String LINKED_RATE_PLAN_BOTTOMSHEET_TITLE = "LINKED_RATE_PLAN_BOTTOMSHEET_TITLE";
    public static final String LINKED_RATE_PLAN_DISCOUNT_TEXT = "LINKED_RATE_PLAN_DISCOUNT_TEXT";
    public static final String LINKED_RATE_PLAN_ORIGINAL_PRICE_TEXT = "LINKED_RATE_PLAN_ORIGINAL_PRICE_TEXT";
    public static final String LINKED_RATE_PLAN_TITLE = "LINKED_RATE_PLAN_TITLE";
    public static final String LINKED_RATE_PLAN_DESCRIPTION = "LINKED_RATE_PLAN_DESCRIPTION";
    public static final String LINKED_RATE_PLAN_CTATEXT = "LINKED_RATE_PLAN_CTATEXT";
    public static final String LINKED_RATE_PLAN_BOTTOM_SHEET_FOOTER_TEXT = "LINKED_RATE_PLAN_BOTTOM_SHEET_FOOTER_TEXT" ;
    public static final String LISTING_COLLECTION_SECTION_HEADING = "LISTING_COLLECTION_SECTION_HEADING";
    public static final String LIVING_ROOM_TITLE = "LIVING_ROOM_TITLE";

    public static final String INCLUDED_FOR = "INCLUDED_FOR";

    public static final String CROSS_SELL_BENEFIT_COUNT_TEXT_SINGULAR = "CROSS_SELL_BENEFIT_COUNT_TEXT_SINGULAR";
    public static final String CROSS_SELL_BENEFIT_COUNT_TEXT = "CROSS_SELL_BENEFIT_COUNT_TEXT";

    public static final String LAST_MIN_DEAL_TITLE = "LAST_MIN_DEAL_TITLE";
    public static final String EARLY_BIRD_DEAL_TITLE = "EARLY_BIRD_DEAL_TITLE";
    public static final String COLLECTIONS_TITLE = "COLLECTIONS_TITLE";

    public static final String WITH_B_L_D = "WITH_B_L_D";
    public static final String WITH_B_AND_LD = "WITH_B_AND_LD";
    public static final String WITH_BREAKFAST_AND_DINNER = "WITH_BREAKFAST_AND_DINNER";
    public static final String WITH_BREAKFAST_AND_LUNCH = "WITH_BREAKFAST_AND_LUNCH";
    public static final String WITH_BREAKFAST_TEXT = "WITH_BREAKFAST_TEXT";
    public static final String ROOM_ONLY_TEXT = "ROOM_ONLY_TEXT";
    public static final String BED_ONLY = "BED_ONLY";
    public static final String ACCOMODATION_ONLY = "ACCOMODATION_ONLY";
    public static final String MEAL_INCLUDED_TEXT = "MEAL_INCLUDED_TEXT";

    public static final String PRICE_FILTER_TITLE = "PRICE_FILTER_TITLE";

    public static final String REVIEW_LEVEL_ESTIMATED_TIME = "REVIEW_LEVEL_%d_ESTIMATED_TIME";
    public static final String REVIEW_LEVEL_TITLE = "REVIEW_LEVEL_%d_TITLE";
    public static final String REVIEW_LEVEL_DESCRIPTION = "REVIEW_LEVEL_%d_DESCRIPTION";
    public static final String REVIEW_LEVEL_EDIT_TEXT_DESC = "REVIEW_LEVEL_%d_EDIT_TEXT_DESC";

    public static final String DESKTOP_POPULAR_GROUPS_PAH_AVAIL_TITLE = "DESKTOP_POPULAR_GROUPS_PAH_AVAIL_TITLE";

    public static final String EARLY_BIRD_FILTER_TITLE = "EARLY_BIRD_FILTER_TITLE";
    public static final String LAST_MINUTE_FILTER_TITLE = "LAST_MINUTE_FILTER_TITLE";
    public static final String MMT_RUSH_DEALS_TITLE = "MMT_RUSH_DEALS_TITLE";
    public static final String FILTER_POPULAR_TITLE_IN = "FILTER_POPULAR_TITLE_IN";
    public static final String PREVIOUSLY_USED_FILTER_TITLE = "PREVIOUSLY_USED_FILTER_TITLE";
    public static final String AMONG_INDIANS = "AMONG_INDIANS";
    public static final String ENTER_PRICE_RANGE = "ENTER_PRICE_RANGE";
    public static final String OR_ENTER_RANGE = "OR_ENTER_RANGE";
    public static final String ORG_PREFERRED_HOTELS_TITLE = "ORG_PREFERRED_HOTELS_TITLE";
    public static final String MYBIZ_RECOMMENDED_PROPERTIES_NEAR_THIS_PROPERTY = "MYBIZ_RECOMMENDED_PROPERTIES_NEAR_THIS_PROPERTY";
    public static final String MYBIZ_ASSURED_FILTER_CARD_TEXT = "MYBIZ_ASSURED_FILTER_CARD_TEXT";
    public static final String WHY_MYBIZ_ASSURED_TEXT = "WHY_MYBIZ_ASSURED_TEXT";

    public static final String TAXES_LISTING_DETAIL_LABEL = "TAXES_LISTING_DETAIL_LABEL";
    public static final String GROUP_PER_ROOM_PER_NIGHT = "GROUP_PER_ROOM_PER_NIGHT";
    public static final String PER_ROOM_PER_NIGHT = "PER_ROOM_PER_NIGHT";
    public static final String PER_NIGHT_FOR_NUM_ROOMS = "PER_NIGHT_FOR_NUM_ROOMS";
    public static final String PER_NIGHT_WITH_TAX = "PER_NIGHT_WITH_TAX";
    public static final String FOR_NUM_ROOMS_PER_NIGHT_WITH_TAX = "FOR_NUM_ROOMS_PER_NIGHT_WITH_TAX";
    public static final String PER_NIGHT_FOR_NUM_ROOMS_TEXT = "PER_NIGHT_FOR_NUM_ROOMS_TEXT";
    public static final String TOTAL_PRICE_TEXT = "TOTAL_PRICE_TEXT";
    public static final String FOR_NUM_NIGHTS = "FOR_NUM_NIGHTS";
    public static final String FOR_NUM_NIGHTS_NUM_ROOMS = "FOR_NUM_NIGHTS_NUM_ROOMS";

    public static final String FLEXI_CANCEL_DETAIL_SELECTED_PER_NIGHT_TEXT = "FLEXI_CANCEL_DETAIL_SELECTED_PER_NIGHT_TEXT";
    public static final String FREE_CANCELLATION_FILTER = "FREE_CANCELLATION_FILTER";
    public static final String SMART_FILTER_PILL_TEXT = "SMART_FILTER_PILL_TEXT";

    public static final String PAY_AT_HOTEL_FILTER = "PAY_AT_HOTEL_FILTER";
    public static final String PAY_LATER_FILTER = "PAY_LATER_FILTER";
    public static final String FCZPN_FILTER = "FCZPN_FILTER";
    public static final String FCZPN_FILTER_BNPL_NEW_VARIANT = "FCZPN_FILTER_BNPL_NEW_VARIANT";
    public static final String FCZPN_FILTER_BNPL_ZERO_VARIANT = "FCZPN_FILTER_BNPL_ZERO_VARIANT";
    public static final String SPECIAL_DEALS_FILTER = "SPECIAL_DEALS_FILTER";
    public static final String GETAWAY_DEALS_FILTER = "GETAWAY_DEALS_FILTER";
    public static final String BREAKFAST_INCLUDED_FILTER = "BREAKFAST_INCLUDED_FILTER";
    public static final String STARTING_PRICE_AT = "STARTING_PRICE_AT";
    public static final String OUT_OF_POLICY_BOLD = "OUT_OF_POLICY_BOLD";
    public static final String GETAWAY_DEAL_PERSUASION_TEXT = "GETAWAY_DEAL_PERSUASION_TEXT";

    public static final String ENTIRE = "ENTIRE";
    public static final String BED_TEXT = "BED_TEXT";
    public static final String BEDS_TEXT = "BEDS_TEXT";
    public static final String ROOM_TEXT = "ROOM_TEXT";
    public static final String ROOMS_TEXT = "ROOMS_TEXT";


    public static final String IMPORTANT_TITLE = "IMPORTANT_TITLE";
    public static final String ROOM_MISMATCH_TEXT = "ROOM_MISMATCH_TEXT";
    public static final String OCCUPANCY_MISMATCH_TEXT = "OCCUPANCY_MISMATCH_TEXT";


    public static final String DETAIL_COUPON_CARD_HEADER = "DETAIL_COUPON_CARD_HEADER";

    public static final String COUPON_CARD_CONFIG_SUBHEADING = "COUPON_CARD_CONFIG_SUBHEADING";
    //mypartner location based
    public static final String PERMISSION_HEADER_TEXT = "PERMISSION_HEADER_TEXT";
    public static final String PERMISSION_BOOKING_SUBHEADER_TEXT = "PERMISSION_BOOKING_SUBHEADER_TEXT";
    public static final String PERMISSION_BNPL_SUBHEADER_TEXT = "PERMISSION_BNPL_SUBHEADER_TEXT";
    public static final String PERMISSION_SEARCH_SUBHEADER_TEXT = "PERMISSION_SEARCH_SUBHEADER_TEXT";


    public static final String BUSINESS_RATING_HOVER_TEXT = "BUSINESS_RATING_HOVER_TEXT";
    public static final String COLLECTION_LISTING_COMPARE_CARD_TITLE = "COLLECTION_LISTING_COMPARE_CARD_TITLE";
    public static final String MORE_COLLECTION_PROPERTIES = "MORE_COLLECTION_PROPERTIES";

    public static final String NO_COST_EMI_TAG = "NO_COST_EMI_TAG";

    public static final String HEADER_TITLE_SUCCESS = "HEADER_TITLE_SUCCESS";
    public static final String HEADER_SUBTITLE_SUCCESS = "HEADER_SUBTITLE_SUCCESS";

    public static final String HEADER_TITLE_FAILED = "HEADER_TITLE_FAILED";
    public static final String HEADER_SUBTITLE_FAILED_BNPL = "HEADER_SUBTITLE_FAILED_BNPL";
    public static final String HEADER_SUBTITLE_FAILED_PAID = "HEADER_SUBTITLE_FAILED_PAID";

    public static final String HEADER_TITLE_PENDING_BNPL_RTB = "HEADER_TITLE_PENDING_BNPL_RTB";
    public static final String HEADER_TITLE_PENDING_BNPL = "HEADER_TITLE_PENDING_BNPL";
    public static final String HEADER_TITLE_PENDING_PAID_RTB = "HEADER_TITLE_PENDING_PAID_RTB";
    public static final String HEADER_TITLE_PENDING_PAID = "HEADER_TITLE_PENDING_PAID";
    public static final String HEADER_SUBTITLE_PENDING_RTB = "HEADER_SUBTITLE_PENDING_RTB";
    public static final String HEADER_SUBTITLE_PENDING = "HEADER_SUBTITLE_PENDING";

    public static final String BNPL_PAID_AMOUNT_BREAKUP_TITLE = "BNPL_PAID_AMOUNT_BREAKUP_TITLE";
    public static final String COUPLE_FRIENDLY_TITLE = "COUPLE_FRIENDLY";
    public static final String COUPLE_FRIENDLY_RULE_TITLE = "COUPLE_FRIENDLY_RULE_TITLE";
    public static final String COUPLE_FRIENDLY_RULE_DESC = "COUPLE_FRIENDLY_RULE_DESC";
    public static final String THANK_YOU_GIFT_CARD_TITLE = "THANK_YOU_GIFT_CARD_TITLE";

    public static final String PENDING_AMOUNT_BNPL_MANDATE_TEXT_V2 = "PENDING_AMOUNT_BNPL_MANDATE_TEXT_V2";
    public static final String PENDING_AMOUNT_BNPL_NEW_VARIANT_TEXT_V2 = "PENDING_AMOUNT_BNPL_NEW_VARIANT_TEXT_V2";
    public static final String PENDING_AMOUNT_BNPL_TEXT_V2 = "PENDING_AMOUNT_BNPL_TEXT_V2";
    public static final String PENDING_AMOUNT_PAH_DEFAULT_TEXT_V2 = "PENDING_AMOUNT_PAH_DEFAULT_TEXT_V2";
    public static final String PENDING_AMOUNT_PAH_TEXT_V2 = "PENDING_AMOUNT_PAH_TEXT_V2";
    public static final String PG_CHARGES_TEXT_V2 = "PG_CHARGES_TEXT_V2";

    public static final String LABEL_PRIMARY_GUEST = "LABEL_PRIMARY_GUEST";
    public static final String LABEL_Booking_ID = "LABEL_Booking_ID";
    public static final String LABEL_PNR = "LABEL_PNR";
    public static final String LABEL_TOTAL_PRICE = "LABEL_TOTAL_PRICE";
    public static final String LABEL_AMOUNT_PAID = "LABEL_AMOUNT_PAID";
    public static final String LABEL_GUEST_AND_ROOMS = "LABEL_GUEST_AND_ROOMS";
    public static final String LABEL_ADULT_COUNT = "LABEL_ADULT_COUNT";
    public static final String LABEL_ADULT_COUNT_PLURAL = "LABEL_ADULT_COUNT_PLURAL";
    public static final String LABEL_CHILD_COUNT = "LABEL_CHILD_COUNT";
    public static final String LABEL_CHILD_COUNT_PLURAL = "LABEL_CHILD_COUNT_PLURAL";
    public static final String THANK_YOU_CHARITY_TITLE = "THANK_YOU_CHARITY_TITLE";
    public static final String THANK_YOU_CHARITY_SUB_TITLE = "THANK_YOU_CHARITY_SUB_TITLE";
    public static final String THANK_YOU_PRIMARY_CTA_TITLE = "THANK_YOU_PRIMARY_CTA_TITLE";
    public static final String COUNTRY_WISE_REVIEW_TEXT = "COUNTRY_WISE_REVIEW_TEXT";
    public static final String COUNTRY_WISE_REVIEW_CTA_TEXT = "COUNTRY_WISE_REVIEW_CTA_TEXT";

    public static final String RECENTLY_ADDED_GUESTS_TITLE = "RECENTLY_ADDED_GUESTS_TITLE";
    public static final String IMPORTANT_INFORMATION_SUBTITLE_IH = "IMPORTANT_INFORMATION_SUBTITLE_IH";

    //FoodDiningV2 Strings
    public static final String FOOD_DINING_CARD_TITLE = "FOOD_DINING_CARD_TITLE";
    public static final String FOOD_DINING_FEEDBACK_CARD_TITLE = "FOOD_DINING_FEEDBACK_CARD_TITLE";
    public static final String FOOD_DINING_FEEDBACK_SHEET_TITLE = "FOOD_DINING_FEEDBACK_SHEET_TITLE";
    public static final String FOOD_DINING_FEEDBACK_SHEET_DESCRIPTION = "FOOD_DINING_FEEDBACK_SHEET_DESCRIPTION";
    public static final String FOOD_DINING_FEEDBACK_SHEET_TEXT_BOX_TITLE = "FOOD_DINING_FEEDBACK_SHEET_TEXT_BOX_TITLE";
    public static final String FOOD_DINING_FEEDBACK_SHEET_REASON_ONE = "FOOD_DINING_FEEDBACK_SHEET_REASON_ONE";
    public static final String FOOD_DINING_FEEDBACK_SHEET_REASON_TWO = "FOOD_DINING_FEEDBACK_SHEET_REASON_TWO";
    public static final String FOOD_DINING_FEEDBACK_SHEET_REASON_THREE = "FOOD_DINING_FEEDBACK_SHEET_REASON_THREE";
    public static final String FOOD_DINING_MULTI_OPTION_TITLE = "FOOD_DINING_MULTI_OPTION_TITLE";
    public static final String FOOD_DINING_FIXED_MENU_TITLE = "FOOD_DINING_FIXED_MENU_TITLE";
    public static final String FOOD_DINING_COOK_OPTION_TITLE = "FOOD_DINING_COOK_OPTION_TITLE";
    public static final String FOOD_DINING_MULTI_OPTION_INFO_TEXT = "FOOD_DINING_MULTI_OPTION_INFO_TEXT";
    public static final String FOOD_DINING_CARD_MEALS_OFFERED = "FOOD_DINING_CARD_MEALS_OFFERED";
    public static final String FOOD_DINING_CARD_CUISINES = "FOOD_DINING_CARD_CUISINES";
    public static final String FOOD_DINING_CARD_CHARGES = "FOOD_DINING_CARD_CHARGES";
    public static final String FOOD_DINING_INCLUDED_TEXT = "FOOD_DINING_INCLUDED_TEXT";
    public static final String FOOD_DINING_EXCLUDED_TEXT = "FOOD_DINING_EXCLUDED_TEXT";
    public static final String FOOD_DINING_SHEET_TITLE_MEAL_DETAILS = "FOOD_DINING_SHEET_TITLE_MEAL_DETAILS";
    public static final String FOOD_DINING_SHEET_TITLE_MEALS_AVAILABLE = "FOOD_DINING_SHEET_TITLE_MEALS_AVAILABLE";

    // filter groups to consider for translation based on filter title
    public static final List<FilterGroup> filterGroupsToTranslate = Arrays.asList(
            FilterGroup.AMENITIES,
            FilterGroup.ROOM_AMENITIES,
            FilterGroup.PROPERTY_TYPE,
            FilterGroup.MERGE_PROPERTY_TYPE,
            FilterGroup.HOUSE_RULES,
            FilterGroup.ROOM_VIEWS
    );
    public static final String ROOM_DETAILS_ALTERNATE_BED_TYPE_OR_TEXT = "ROOM_DETAILS_ALTERNATE_BED_TYPE_OR_TEXT";
    public static final String ROOM_DETAILS_BATHROOM_TEXT = "ROOM_DETAILS_BATHROOM_TEXT";
    public static final String ROOM_DETAILS_BATHROOMS_TEXT = "ROOM_DETAILS_BATHROOMS_TEXT";

    public static final String FOREX_CAB_CARD_TITLE_THANKYOU_PAGE_V2 = "FOREX_CAB_CARD_TITLE_THANKYOU_PAGE_V2";//"FREE ADD-ONS UNLOCKED!"
    public static final String MYPARTNER_TIER_OFFER_TEXT = "MYPARTNER_TIER_OFFER_TEXT";

    //DROP
    public static final String PRICE_GRAPH_DROP_TITLE = "price_graph_drop_title";
    public static final String PRICE_GRAPH_DROP_SUBTITLE = "price_graph_drop_subtitle";
    //SURGE
    public static final String PRICE_GRAPH_SURGE_TITLE = "price_graph_surge_title";
    public static final String PRICE_GRAPH_SURGE_SUBTITLE = "price_graph_surge_subtitle";
    //TYPICAL
    public static final String PRICE_GRAPH_TYPICAL_TITLE = "price_graph_typical_title";
    public static final String PRICE_GRAPH_TYPICAL_SUBTITLE = "price_graph_typical_subtitle";

    public static final String PRICE_GRAPH_ICON_TITLE = "price_graph_icon_title";
    public static final String PRICE_GRAPH_ICON_SUBTITLE = "price_graph_icon_subtitle";
    public static final String EFFECTIVE_PRICE_LABEL = "effective_price_label";

    public static final String MISSED_CALL_MESSAGE_CONSTANT = "HOST_CALLING_MISSED_CALL_MESSAGE";
    public static final String CHAIN_NAME_PLACEHOLDER = "{chainName}";
    public static final String HOST_CALLING_DOWNSTREAM_ERROR_MESSAGE = "HOST_CALLING_DOWNSTREAM_ERROR_MESSAGE";
    public static final String HOST_CALLING_MOBILE_MISSING_ERROR_MESSAGE = "HOST_CALLING_MOBILE_MISSING_ERROR_MESSAGE";
    public static final String SORT_CRITERIA_ACCESS_POINT_TITLE = "SORT_CRITERIA_ACCESS_POINT_TITLE";
    public static final String LBI_TITLE = "LBI_TITLE"; // loved by indians persuasion title

    public static final String ACTION_VIEW_DETAILS = "ACTION_VIEW_DETAILS";
    public static final String ACTION_CONTINUE = "ACTION_CONTINUE";
    public static final String HOST_NO_RATING_TEXT = "HOST_NO_RATING_TEXT";

    // Multi Stay Room Change related translation keys
    public static final String ROOM_CHANGE_TEXT = "ROOM_CHANGE_TEXT";
    public static final String STAY_REMINDER_TEXT = "STAY_REMINDER_TEXT";
    public static final String STAY_REMINDER_TEXT_LONG = "STAY_REMINDER_TEXT_LONG";
    public static final String ROOM_CHANGE_CTA_TEXT = "ROOM_CHANGE_CTA_TEXT";
    public static final String LONG_STAY_DISCLAIMER_TEXT = "LONG_STAY_DISCLAIMER_TEXT";
    public static final String COMBO_SAVING_TEXT = "COMBO_SAVING_TEXT";
    public static final String SPECIAL_COMBO_OFFER_TEXT = "SPECIAL_COMBO_OFFER_TEXT";
    public static final String MULTI_ROOM_STAY_TITLE = "MULTI_ROOM_STAY_TITLE";
    public static final String MULTI_ROOM_STAY_DETAILS_TITLE = "MULTI_ROOM_STAY_DETAILS_TITLE";

    public static final String MMT_VALUESTAY_TITLE = "MMT_VALUESTAY_TITLE";
    public static final String FREE_CANCELLATION = "FREE_CANCELLATION";
    public static final String FREE_CANCELLATION_HIGHER_PRICE = "FREE_CANCELLATION_HIGHER_PRICE";
    public static final String FREE_BREAKFAST_AT_HIGHER_PRICE = "FREE_BREAKFAST_AT_HIGHER_PRICE";
    public static final String FREE_WIFI = "FREE_WIFI";

    public static final String HOST_INFO = "HOST_INFO";
    public static final String CARETAKER_INFO = "CARETAKER_INFO";
    public static final String SPECIAL_REQUEST_V2_DEFAULT_CARD_TITLE = "SPECIAL_REQUEST_V2_DEFAULT_CARD_TITLE";
    public static final String SPECIAL_REQUEST_V2_DEFAULT_CARD_DESC = "SPECIAL_REQUEST_V2_DEFAULT_CARD_DESC";
    public static final String SPECIAL_REQUEST_V2_DEFAULT_CARD_CTA_TEXT = "SPECIAL_REQUEST_V2_DEFAULT_CARD_CTA_TEXT";
    public static final String BHF_BLOCKER_HEADING = "BHF_BLOCKER_HEADING";
    public static final String BHF_BLOCKER_LEFT_CTA = "BHF_BLOCKER_LEFT_CTA";
    public static final String BHF_BLOCKER_RIGHT_CTA = "BHF_BLOCKER_RIGHT_CTA";
    public static final String BHF_BLOCKER_ADDITIONAL_TEXT = "BHF_BLOCKER_ADDITIONAL_TEXT";

    public static final String EXPERIENCES_CARD_TITLE = "EXPERIENCES_CARD_TITLE";
    public static final String EXPERIENCES_CARD_TITLE_PREMIUM = "EXPERIENCES_CARD_TITLE_PREMIUM";

    public static final String REVIEW_SUMMARY_BATHROOM_REVIEW = "REVIEW_SUMMARY_BATHROOM_REVIEW";
    public static final String REVIEW_SUMMARY_FEEDBACK_LABEL = "REVIEW_SUMMARY_FEEDBACK_LABEL";
    public static final String REVIEW_SUMMARY_FEEDBACK_TOAST = "REVIEW_SUMMARY_FEEDBACK_TOAST";


    public static final String HOST_INFO_TITLE_DESKTOP = "HOST_INFO_TITLE_DESKTOP";
    public static final String HOST_INFO_SUB_TITLE_DESKTOP = "HOST_INFO_SUB_TITLE_DESKTOP";
    public static final String CARETAKER_INFO_TITLE_DESKTOP = "CARETAKER_INFO_TITLE_DESKTOP";
    public static final String CARETAKER_INFO_SUB_TITLE_DESKTOP = "CARETAKER_INFO_SUB_TITLE_DESKTOP";
    public static final String SPECIAL_REQUEST_V2_SELECTED_CARD_TITLE = "SPECIAL_REQUEST_V2_SELECTED_CARD_TITLE";
    public static final String SPECIAL_REQUEST_V2_SELECTED_CARD_DESC = "SPECIAL_REQUEST_V2_SELECTED_CARD_DESC";
    public static final String SPECIAL_REQUEST_V2_SELECTED_CARD_CTA_TEXT = "SPECIAL_REQUEST_V2_SELECTED_CARD_CTA_TEXT";

    //DigiLocker Check in Strings
    public static final String DIGILOCKER_OPT_IN_TEXT = "DIGILOCKER_OPT_IN_TEXT";
    public static final String DIGILOCKER_CARD_TITLE = "DIGILOCKER_CARD_TITLE";
    public static final String DIGILOCKER_CARD_CTA = "DIGILOCKER_CARD_CTA";

    public static final String SPECIAL_REQUEST_V2_BANNER_DESC = "SPECIAL_REQUEST_V2_BANNER_DESC";
    public static final String EXPERIENCES_CARD_CTA = "EXPERIENCES_CARD_CTA";

    public static final String EXPERIENCES_INCLUSION_TITLE = "EXPERIENCES_INCLUSION_TITLE";
    public static final String SECURITY_DEPOSIT_SERVICE_FEE_SHORT_DESC = "SECURITY_DEPOSIT_SERVICE_FEE_SHORT_DESC";
    public static final String CLEANING_FEE_SERVICE_FEE_SHORT_DESC = "CLEANING_FEE_SERVICE_FEE_SHORT_DESC";


}
