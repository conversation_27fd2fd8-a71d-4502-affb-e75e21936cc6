package com.mmt.hotels.clientgateway.constants;

public class ByPassUrls {
	
	public static final String SOURCE_OTP_GENERATE_URL = "/entity/api/otp/generate";
	public static final String SOURCE_VALIDATE_COUPON_URL = "/entity/api/validateCoupon";
	public static final String SOURCE_OTP_VALIDATE_URL = "/entity/api/otp/validate";
	public static final String SOURCE_GET_TOTAL_PRICING_URL = "/entity/api/hotels/getTotalPricing";
	public static final String SOURCE_GET_FLYFISH_UPVOTE_DOWNVOTE_URL = "/entity/api/review/upvote";
	public static final String SOURCE_GET_FLYFISH_CATEGORY_LIST = "/entity/api/hotels/{hotelId}/summary/category";
	public static final String SOURCE_GET_FLYFISH_REVIEWS = "/entity/api/hotel/{hotelId}/flyfishReviews";
	public static final String SOURCE_ALT_ACCO_BENEFITS = "/entity/api/hotel/altAccoBenifits";
	public static final String SOURCE_EMI_DETAILS = "/bypass/hotels-entity/api/v2.0/emidetails";
	public static final String SOURCE_STATIC_POLICIES = "/entity/api/hotel/{hotelId}/staticPolicies";
	public static final String SOURCE_GET_PMS_CONFIG="/bypass/hotels-entity/api/v2.0/getPmsConfig";

	public static final String DESTINATION_VALIDATE_COUPON_URL = "http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/validateCoupon";
	public static final String DESTINATION_OTP_GENERATE_URL = "http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/otp/generate";
	public static final String DESTINATION_OTP_VALIDATE_URL = "http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/otp/validate";
	public static final String DESTINATION_GET_TOTAL_PRICING_URL = "http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/hotels/getTotalPricing";
	public static final String DESTINATION_FLYFISH_REVIEWS_UPVOTE_DOWNVOTE_API_URL = "http://hotels-fishmonger.ecs.mmt/fish-monger/api/v1.0/review/upvote";
	public static final String DESTINATION_UGC_REVIEWS_UPVOTE_DOWNVOTE_API_URL = "http://platform-ugc-orchestrator.ecs.mmt/ugc/userAction";
	public static final String DESTINATION_FLYFISH_REVIEWS_CATEGORY_LIST_API_URL= "http://hotels-fishmonger.ecs.mmt/fish-monger/api/v1.0/hotels/%s/summary/category?client=%s&corelationKey=%s&contextType=%s";
	public static final String DESTINATION_FLYFISH_REVIEWS_API_URL= "http://hotels-fishmonger.ecs.mmt/fish-monger/api/v1.0/hotels/%s/reviews?client=%s&corelationKey=%s&contextType=%s";
	public static final String DESTINATION_ALT_ACCO_BENEFITS_API_URL= "http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/altaccodata/benifits?correlationKey=%s&countryCode=%s&deviceType=%s&cityCode=%s&region=%s";

	public static final String DESTINATION_EMI_DETAILS_URL= "http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/emidetails";
	public static final String DESTINATION_STATICPOLICY_URL = "http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/hotel/{hotelId}/staticPolicies";
	public static final String DESTINATION_GET_PMS_CONFIG= "http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/getPmsConfig";

}
