package com.mmt.hotels.clientgateway.configuration;

import org.springframework.stereotype.Component;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import javax.servlet.http.Cookie;

import java.io.IOException;

@Component
public class SecurityHeadersFilter implements Filter{
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // This method can be used for initialization, but not needed here
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;


        // Wrap the original HttpServletResponse with a custom wrapper
        HttpServletResponseWrapper responseWrapper = new HttpServletResponseWrapper(httpServletResponse) {
            @Override
            public void setHeader(String name, String value) {
                // Exclude "Server" header
                if (!"Server".equalsIgnoreCase(name)) {
                    super.setHeader(name, value);
                }
            }

            @Override
            public void addHeader(String name, String value) {
                // Exclude "Server" header
                if (!"Server".equalsIgnoreCase(name)) {
                    super.addHeader(name, value);
                }
            }

            @Override
            public void addCookie(Cookie cookie) {
                // Set HTTP-only and Secure flags on the cookie
                cookie.setHttpOnly(true);
                cookie.setSecure(true);
                super.addCookie(cookie);
            }
        };

        // Set the X-Frame-Options header to 'SAMEORIGIN' or 'DENY'
        responseWrapper.setHeader("X-Frame-Options", "SAMEORIGIN");

        // Set the X-Content-Type-Options header to nosniff
        responseWrapper.setHeader("X-Content-Type-Options", "nosniff");

        // Removed the header below since it's now being added via the Apache server (see clientgateway.conf file)
        // Set the Content-Security-Policy (CSP) header
//        String csp = "default-src 'self'; script-src 'self' 'unsafe-inline';";
//        responseWrapper.setHeader("Content-Security-Policy", csp);

        // Continue the request-response chain
        chain.doFilter(request, response);
    }

    @Override
    public void destroy() {
        // Clean up resources if necessary
    }
}
