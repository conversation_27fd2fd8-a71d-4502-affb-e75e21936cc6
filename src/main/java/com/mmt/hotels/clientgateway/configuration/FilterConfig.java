package com.mmt.hotels.clientgateway.configuration;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FilterConfig {

    @Bean
    public FilterRegistrationBean<SecurityHeadersFilter> xFrameOptionsHeaderFilter() {
        FilterRegistrationBean<SecurityHeadersFilter> registrationBean = new FilterRegistrationBean<>();

        // Register the filter
        registrationBean.setFilter(new SecurityHeadersFilter());

        // Apply the filter to all URL patterns
        registrationBean.addUrlPatterns("/*");  // Apply to all URLs, change this to apply only to specific URLs

        // Optional: Set the order of filter execution (optional, 1 is the highest priority)
        registrationBean.setOrder(1);

        return registrationBean;
    }

}
