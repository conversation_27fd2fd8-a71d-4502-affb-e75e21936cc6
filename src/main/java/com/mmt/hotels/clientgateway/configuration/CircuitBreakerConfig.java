package com.mmt.hotels.clientgateway.configuration;

import com.mmt.hotels.clientgateway.util.CircuitBreakerMetricAspect;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.time.Duration;

/**
 * Base Circuit Breaker Configuration
 * Provides common circuit breaker settings and factory methods
 */
@Configuration
public class CircuitBreakerConfig {

    private static final Logger logger = LoggerFactory.getLogger(CircuitBreakerConfig.class);

    @Autowired(required = false)
    @Lazy
    private CircuitBreakerMetricAspect circuitBreakerMetricAspect;

    // Mob Landing Circuit Breaker Properties
    @Value("${resilience4j.circuitbreaker.instances.mob-landing.failure-rate-threshold:30}")
    private float mobLandingFailureRateThreshold;

    @Value("${resilience4j.circuitbreaker.instances.mob-landing.timeout-duration:8s}")
    private String mobLandingTimeoutDuration;

    @Value("${resilience4j.circuitbreaker.instances.mob-landing.sliding-window-size:100}")
    private int mobLandingSlidingWindowSize;

    @Value("${resilience4j.circuitbreaker.instances.mob-landing.minimum-number-of-calls:20}")
    private int mobLandingMinimumNumberOfCalls;

    @Value("${resilience4j.circuitbreaker.instances.mob-landing.wait-duration-in-open-state:60s}")
    private String mobLandingWaitDurationInOpenState;

    @Value("${resilience4j.circuitbreaker.instances.mob-landing.permitted-number-of-calls-in-half-open-state:10}")
    private int mobLandingPermittedNumberOfCallsInHalfOpenState;

    @Value("${resilience4j.circuitbreaker.instances.mob-landing.slow-call-rate-threshold:50.0}")
    private float mobLandingSlowCallRateThreshold;

    // Detail API Circuit Breaker Properties
    @Value("${resilience4j.circuitbreaker.instances.detail-api.failure-rate-threshold:25}")
    private float detailApiFailureRateThreshold;

    @Value("${resilience4j.circuitbreaker.instances.detail-api.timeout-duration:10s}")
    private String detailApiTimeoutDuration;

    @Value("${resilience4j.circuitbreaker.instances.detail-api.slow-call-rate-threshold:50.0}")
    private float detailApiSlowCallRateThreshold;

    // Listing API Circuit Breaker Properties
    @Value("${resilience4j.circuitbreaker.instances.listing-api.failure-rate-threshold:35}")
    private float listingApiFailureRateThreshold;

    @Value("${resilience4j.circuitbreaker.instances.listing-api.timeout-duration:12s}")
    private String listingApiTimeoutDuration;

    @Value("${resilience4j.circuitbreaker.instances.listing-api.slow-call-rate-threshold:50.0}")
    private float listingApiSlowCallRateThreshold;

    // Feature toggles
    @Value("${circuit.breaker.mob-landing.enabled:true}")
    private boolean mobLandingCircuitBreakerEnabled;

    @Value("${circuit.breaker.detail-api.enabled:false}")
    private boolean detailApiCircuitBreakerEnabled;

    @Value("${circuit.breaker.listing-api.enabled:false}")
    private boolean listingApiCircuitBreakerEnabled;



    /**
     * Creates the Circuit Breaker Registry with pool-specific configurations
     */
    @Bean
    public CircuitBreakerRegistry circuitBreakerRegistry() {
        logger.warn("Initializing Circuit Breaker Registry");
        
        CircuitBreakerRegistry registry = CircuitBreakerRegistry.ofDefaults();
        
        // Register mob-landing circuit breaker if enabled
        if (mobLandingCircuitBreakerEnabled) {
            registry.circuitBreaker("mob-landing", createMobLandingConfig());
            logger.warn("Mob Landing Circuit Breaker registered with failure rate threshold: {}%",
                       mobLandingFailureRateThreshold);
        }
        
        // Register detail-api circuit breaker if enabled
//        if (detailApiCircuitBreakerEnabled) {
//            registry.circuitBreaker("detail-api", createDetailApiConfig());
//            logger.warn("Detail API Circuit Breaker registered with failure rate threshold: {}%",
//                       detailApiFailureRateThreshold);
//        }
//
//        // Register listing-api circuit breaker if enabled
//        if (listingApiCircuitBreakerEnabled) {
//            registry.circuitBreaker("listing-api", createListingApiConfig());
//            logger.warn("Listing API Circuit Breaker registered with failure rate threshold: {}%",
//                       listingApiFailureRateThreshold);
//        }
        
        return registry;
    }

    /**
     * Creates mob-landing specific circuit breaker configuration
     */
    private io.github.resilience4j.circuitbreaker.CircuitBreakerConfig createMobLandingConfig() {
        return io.github.resilience4j.circuitbreaker.CircuitBreakerConfig.custom()
                .failureRateThreshold(mobLandingFailureRateThreshold)
                .slowCallRateThreshold(mobLandingSlowCallRateThreshold)
                .slowCallDurationThreshold(Duration.parse("PT" + mobLandingTimeoutDuration.toUpperCase()))
                .slidingWindowSize(mobLandingSlidingWindowSize)
                .slidingWindowType(io.github.resilience4j.circuitbreaker.CircuitBreakerConfig.SlidingWindowType.TIME_BASED)
                .minimumNumberOfCalls(mobLandingMinimumNumberOfCalls)
                .waitDurationInOpenState(Duration.parse("PT" + mobLandingWaitDurationInOpenState.toUpperCase()))
                .permittedNumberOfCallsInHalfOpenState(mobLandingPermittedNumberOfCallsInHalfOpenState)
                .automaticTransitionFromOpenToHalfOpenEnabled(true)
                .build();
    }

    /**
     * Creates detail-api specific circuit breaker configuration
     */
    private io.github.resilience4j.circuitbreaker.CircuitBreakerConfig createDetailApiConfig() {
        return io.github.resilience4j.circuitbreaker.CircuitBreakerConfig.custom()
                .failureRateThreshold(detailApiFailureRateThreshold)
                .slowCallRateThreshold(detailApiSlowCallRateThreshold)
                .slowCallDurationThreshold(Duration.parse("PT" + detailApiTimeoutDuration.toUpperCase()))
                .slidingWindowSize(50)
                .minimumNumberOfCalls(10)
                .waitDurationInOpenState(Duration.ofSeconds(60))
                .permittedNumberOfCallsInHalfOpenState(5)
                .automaticTransitionFromOpenToHalfOpenEnabled(true)
                .build();
    }

    /**
     * Creates listing-api specific circuit breaker configuration
     */
    private io.github.resilience4j.circuitbreaker.CircuitBreakerConfig createListingApiConfig() {
        return io.github.resilience4j.circuitbreaker.CircuitBreakerConfig.custom()
                .failureRateThreshold(listingApiFailureRateThreshold)
                .slowCallRateThreshold(listingApiSlowCallRateThreshold)
                .slowCallDurationThreshold(Duration.parse("PT" + listingApiTimeoutDuration.toUpperCase()))
                .slidingWindowSize(50)
                .minimumNumberOfCalls(10)
                .waitDurationInOpenState(Duration.ofSeconds(60))
                .permittedNumberOfCallsInHalfOpenState(5)
                .automaticTransitionFromOpenToHalfOpenEnabled(true)
                .build();
    }

    /**
     * Provides mob-landing circuit breaker bean
     */
    @Bean("mobLandingCircuitBreaker")
    public CircuitBreaker mobLandingCircuitBreaker(CircuitBreakerRegistry registry) {
        if (mobLandingCircuitBreakerEnabled) {
            CircuitBreaker circuitBreaker = registry.circuitBreaker("mob-landing");

            // Add event listeners for monitoring
            circuitBreaker.getEventPublisher()
                    .onStateTransition(event -> {
                        logger.warn("Mob Landing Circuit Breaker state transition from {} to {}",
                                   event.getStateTransition().getFromState(),
                                   event.getStateTransition().getToState());

                        // Log metrics if aspect is available
                        if (circuitBreakerMetricAspect != null) {
                            circuitBreakerMetricAspect.logCircuitBreakerStateChange(
                                "mob-landing",
                                event.getStateTransition().getFromState(),
                                event.getStateTransition().getToState()
                            );
                        }
                    });

            circuitBreaker.getEventPublisher()
                    .onFailureRateExceeded(event -> {
                        logger.warn("Mob Landing Circuit Breaker failure rate exceeded: {}%",
                                   event.getFailureRate());

                        // Log metrics if aspect is available
                        if (circuitBreakerMetricAspect != null) {
                            circuitBreakerMetricAspect.logCircuitBreakerFailureRateExceeded(
                                "mob-landing",
                                event.getFailureRate()
                            );
                        }
                    });

            return circuitBreaker;
        }
        return null;
    }

    // Getters for configuration values (for use in other components)
    public boolean isMobLandingCircuitBreakerEnabled() {
        return mobLandingCircuitBreakerEnabled;
    }

}
