package com.mmt.hotels.clientgateway.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class ThreadPoolConfig {
	
	@Value("${threadpool.corepool.size.kafkaThreadPool}")
	private int kafkaThreadPoolCorePool;
	@Value("${threadpool.maxpool.size.kafkaThreadPool}")
	private int kafkaThreadPoolMaxPool;
	@Value("${threadpool.queue.capacity.kafkaThreadPool}")
	private int kafkaThreadPoolQueue;
	@Value("${threadpool.corepool.size.userServiceThreadPool}")
	private int userServiceThreadPoolCorePool;
	@Value("${threadpool.maxpool.size.userServiceThreadPool}")
	private int userServiceThreadPoolMaxPool;
	@Value("${threadpool.queue.capacity.userServiceThreadPool}")
	private int userServiceThreadPoolQueue;
	@Value("${threadpool.corepool.size.hydraServiceThreadPool}")
	private int hydraServiceThreadPoolCorePool;
	@Value("${threadpool.maxpool.size.hydraServiceThreadPool}")
	private int hydraServiceThreadPoolMaxPool;
	@Value("${threadpool.queue.capacity.hydraServiceThreadPool}")
	private int hydraServiceThreadPoolQueue;
	@Value("${threadpool.corepool.size.detailServiceThreadPool}")
	private int detailServiceThreadPoolCorePool;
	@Value("${threadpool.maxpool.size.detailServiceThreadPool}")
	private int detailServiceThreadPoolMaxPool;
	@Value("${threadpool.queue.capacity.detailServiceThreadPool}")
	private int detailServiceThreadPoolQueue;

	@Value("${thread.pool.keep.alive.seconds}")
	private int keepAliveSeconds;
	
	// PDT Kafka Logging for contextual filter thread pool properties
	@Value("${threadpool.corepool.size.pdt.logging}")
	private int pdtKafkaLoggingCorePoolSize;
	@Value("${threadpool.maxpool.size.pdt.logging}")
	private int pdtKafkaLoggingMaxPoolSize;
	@Value("${threadpool.queue.capacity.pdt.logging}")
	private int pdtKafkaLoggingQueueCapacity;

	@Value("${evaluate.filter.rank.order.thread.pool.core.pool}")
	private int evaluateFilterRankOrderThreadPoolCorePool;
	@Value("${evaluate.filter.rank.order.thread.pool.max.pool}")
	private int evaluateFilterRankOrderThreadPoolMaxPool;
	@Value("${evaluate.filter.rank.order.thread.pool.queue}")
	private int evaluateFilterRankOrderThreadPoolQueue;

	// Listing API thread pool properties

	@Value("${threadpool.corepool.size.listingThreadPool}")
	private int listingThreadPoolCorePool;
	@Value("${threadpool.maxpool.size.listingThreadPool}")
	private int listingThreadPoolMaxPool;
	@Value("${threadpool.queue.capacity.listingThreadPool}")
	private int listingThreadPoolQueue;

	@Value("${threadpool.corepool.size.reviewServiceThreadPool}")
	private int reviewServiceThreadPoolCorePool;

	@Value("${threadpool.maxpool.size.reviewServiceThreadPool}")
	private int reviewServiceThreadPoolMaxPool;

	@Value("${threadpool.queue.capacity.reviewServiceThreadPool}")
	private int reviewServiceThreadPoolQueue;

	@Bean(name = "kafkaThreadPool")
	public ThreadPoolTaskExecutor kafkaThreadPool() {
		return createExecutor(kafkaThreadPoolCorePool, kafkaThreadPoolMaxPool,
				kafkaThreadPoolQueue, "kafkaThreadPool", keepAliveSeconds);
	}
	
	@Bean(name = "userServiceThreadPool")
	public ThreadPoolTaskExecutor userServiceThreadPool() {
    	return createExecutor(userServiceThreadPoolCorePool, userServiceThreadPoolMaxPool,
				userServiceThreadPoolQueue, "userServiceThreadPool", keepAliveSeconds);
	}
	
	@Bean(name = "hydraServiceThreadPool")
	public ThreadPoolTaskExecutor hydraServiceThreadPool() {
    	return createExecutor(hydraServiceThreadPoolCorePool, hydraServiceThreadPoolMaxPool,
    			hydraServiceThreadPoolQueue, "hydraServiceThreadPool", keepAliveSeconds);
	}

	@Bean(name = "detailServiceThreadPool")
	public ThreadPoolTaskExecutor detailServiceThreadPool() {
    	return createExecutor(detailServiceThreadPoolCorePool, detailServiceThreadPoolMaxPool,
				detailServiceThreadPoolQueue, "detailServiceThreadPool", keepAliveSeconds);
	}

	@Bean(name = "reviewServiceThreadPool")
	public ThreadPoolTaskExecutor reviewServiceThreadPool() {
		return createExecutor(reviewServiceThreadPoolCorePool, reviewServiceThreadPoolMaxPool,
				reviewServiceThreadPoolQueue, "reviewServiceThreadPool", keepAliveSeconds);
	}
	
	@Bean(name = "pdtLoggingThreadPool")
	public ThreadPoolTaskExecutor pdtLoggingThreadPool() {
		return createExecutor(pdtKafkaLoggingCorePoolSize, pdtKafkaLoggingMaxPoolSize,
				pdtKafkaLoggingQueueCapacity, "pdtLoggingThreadPool", keepAliveSeconds);
	}

	@Bean(name = "evaluateFilterRankOrderThreadPool")
	public ThreadPoolTaskExecutor evaluateFilterRankOrderThreadPool() {
		return createExecutor(evaluateFilterRankOrderThreadPoolCorePool, evaluateFilterRankOrderThreadPoolMaxPool,
				evaluateFilterRankOrderThreadPoolQueue, "evaluateFilterRankOrderThreadPool", keepAliveSeconds);
	}

	@Bean(name = "listingThreadPool")
	public ThreadPoolTaskExecutor listingThreadPool() {
		return createExecutor(listingThreadPoolCorePool, listingThreadPoolMaxPool,
				listingThreadPoolQueue, "listingThreadPool", keepAliveSeconds);
	}

    private ThreadPoolTaskExecutor createExecutor(int corePoolSize,int maxPoolSize,int queueCapacity,
			String threadNamePrefix,int keepAliveSeconds) {
		ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();
		pool.setCorePoolSize(corePoolSize);
		pool.setMaxPoolSize(maxPoolSize);
		pool.setQueueCapacity(queueCapacity);
		pool.setThreadNamePrefix(threadNamePrefix);
		pool.setKeepAliveSeconds(keepAliveSeconds);
		pool.setWaitForTasksToCompleteOnShutdown(false);
		pool.afterPropertiesSet();
		return pool;
	}
}
