package com.mmt.hotels.clientgateway.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

@Component
public class GrpcConnectorConfig {

    @Value("${cg.netty.max.thread.pool.size}")
    private int maxThreadPoolSizeCg;

    @Value("${cg.netty.thread.pool.size}")
    private int threadPoolSizeCg;

    @Bean(name = "nettyChannelExecutorCG")
    public TaskExecutor executorManagedChannelHsc() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadPoolSizeCg);
        executor.setMaxPoolSize(maxThreadPoolSizeCg);
        executor.setThreadNamePrefix("managed-channel-pool");
        executor.initialize();
        return executor;
    }
}
