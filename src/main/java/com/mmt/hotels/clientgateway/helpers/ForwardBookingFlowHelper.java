package com.mmt.hotels.clientgateway.helpers;

import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONObject;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.mmt.hotels.clientgateway.constants.Constants.FORWARD_BOOKING_CTA;
import static com.mmt.hotels.clientgateway.constants.Constants.FORWARD_BOOKING_DEEPLINK;
import static com.mmt.hotels.clientgateway.constants.Constants.ICON_URL_KEY;

@Component
public class ForwardBookingFlowHelper {

	public void setForwardBookingFlowDataToMetaData(com.mmt.hotels.model.response.corporate.GetApprovalsResponse approvalsResponse, JSONObject metaData) {
		if (approvalsResponse == null)
			return;

		metaData.put(FORWARD_BOOKING_DEEPLINK, approvalsResponse.getDeepLink());
		metaData.put(FORWARD_BOOKING_CTA, approvalsResponse.getCtaText());
		metaData.put(ICON_URL_KEY, approvalsResponse.getIconUrl());
	}

	public Error setForwardBookingFlowDataToErrorForApprovalPage(Error error, JSONObject metaData) {
		if (metaData != null) {
			String deepLink = (String) metaData.get(FORWARD_BOOKING_DEEPLINK);
			String ctaText = (String) metaData.get(FORWARD_BOOKING_CTA);
			String iconUrl = (String) metaData.get(ICON_URL_KEY);
			if (StringUtils.isNotEmpty(deepLink)) {
				Map<String, String> additionalInfo = new HashMap<>();
				additionalInfo.put(FORWARD_BOOKING_DEEPLINK, deepLink);
				additionalInfo.put(FORWARD_BOOKING_CTA, ctaText);
				additionalInfo.put(ICON_URL_KEY, iconUrl);
				return new Error(error.getCode(), error.getMessage(), error.getAlternateMessage(), error.getErrorTitle(), additionalInfo);
			}
		}
		return error;
	}

	public Error setForwardBookingFlowDataToErrorForPaymentPage(Error error, PaymentCheckoutResponse checkoutResponse) {

		if (!Utility.isMyBizRequest() || checkoutResponse == null)
			return error;

		Map<String, String> additionalErrorInfo = Optional.ofNullable(checkoutResponse.getResponseErrors())
				.map(ResponseErrors::getErrorList)
				.flatMap(errorList -> errorList.stream().findFirst())
				.map(com.mmt.hotels.model.response.errors.Error::getErrorAdditionalInfo).orElse(null);

		if (additionalErrorInfo != null && additionalErrorInfo.containsKey(FORWARD_BOOKING_DEEPLINK)) {
			return new Error(error.getCode(), error.getMessage(), error.getAlternateMessage(), error.getErrorTitle(), additionalErrorInfo);
		}
		return error;
	}
}
