package com.mmt.hotels.clientgateway.helpers;

import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.*;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.LogicalException;
import com.mmt.hotels.clientgateway.exception.OTPAuthenticationException;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.request.OfferDetailsRequest;
import com.mmt.hotels.clientgateway.restexecutors.UserServiceExecutor;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.HeadersUtil;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.payment.BeginCheckoutReqBody;
import com.mmt.hotels.model.request.payment.PaxType;
import com.mmt.hotels.model.request.payment.TravelerDetail;
import com.mmt.hotels.model.request.payment.UserDetail;
import com.mmt.hotels.model.request.payment.AddressDetails;
import com.mmt.propertymanager.config.PropertyManager;
import com.mmt.scrambler.ScramblerClient;
import com.mmt.scrambler.exception.ScramblerClientException;
import com.mmt.scrambler.utils.HashType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.HeaderConstants.MMT_AUTH;
import static com.mmt.hotels.clientgateway.constants.HeaderConstants.REGION;
import static com.mmt.hotels.clientgateway.constants.HeaderConstants.UUID;

@Component
public class PaymentHelper {


    @Value("${consul.enable}")
    private boolean consulFlag;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    @Autowired
    CommonHelper commonHelper;

    @Autowired
    UserServiceExecutor userService;

    @Autowired
    private PropertyManager propertyManager;

    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
    private PolyglotService polyglotService;

    private static final Logger logger = LoggerFactory.getLogger(PaymentHelper.class);

    private int createGuestUserRetries;

    @PostConstruct
    public void init() {
        try {

            if(consulFlag){
                createGuestUserRetries = commonConfigConsul.getCreateGuestUserRetries();
                logger.debug("Fetching values from commonConfig consul");
            }
            else {
                CommonConfig prop = propertyManager.getProperty("commonConfig", CommonConfig.class);
                prop.addPropertyChangeListener("createGuestUserRetries", event -> {
                    createGuestUserRetries = prop.createGuestUserRetries();
                });
                createGuestUserRetries = prop.createGuestUserRetries();
            }
        } catch (Exception e) {
            logger.error("Error Occure in PaymentCheckoutService ", e);
        }

    }

    public  BeginCheckoutReqBody modifyPaymentRequest(BeginCheckoutReqBody paymentRequest, HttpServletRequest httpServletRequest) throws ScramblerClientException, ClientGatewayException {
        Map<String, String> headerMap = HeadersUtil.getHeadersFromServletRequest(httpServletRequest);
        return modifyPaymentRequest(paymentRequest, headerMap);
    }

    public  BeginCheckoutReqBody modifyPaymentRequest(BeginCheckoutReqBody paymentRequest, Map<String, String> headerMap) throws ScramblerClientException, ClientGatewayException{
        modifyTravelerDetails(paymentRequest);
        sanitizeTimeOutURL(paymentRequest);
        if (StringUtils.isBlank(paymentRequest.getAuthToken())) {
            String authToken = commonHelper.getAuthToken(headerMap);
            paymentRequest.setAuthToken(authToken);
        }

        populateAndCheckUserData(paymentRequest, headerMap);
        if (!checkUserOTPValidated(paymentRequest)) {
            throw  new OTPAuthenticationException(DependencyLayer.CLIENTGATEWAY, ErrorType.AUTHENTICATION, "3999","This user required OTP Validation");
        }

        return  paymentRequest;
    }

    public void modifyTravelerDetails(BeginCheckoutReqBody paymentRequest) throws ScramblerClientException, LogicalException {
        if (paymentRequest.getTravelerDetail() != null && CollectionUtils.isEmpty(paymentRequest.getTravelerDetailsList())) {
            if (CollectionUtils.isEmpty(paymentRequest.getTravelerDetailsList())) {
                List<TravelerDetail> travelerDetailsList = new ArrayList<>();
                paymentRequest.getTravelerDetail().setMasterPax(true);
                paymentRequest.getTravelerDetail().setPaxType(PaxType.ADULT);
                com.mmt.hotels.model.request.payment.Gender gender = getGenderFromTitle(paymentRequest.getTravelerDetail().getTitle());
                paymentRequest.getTravelerDetail().setGender(gender);
                sanitizeTravellerDetail(paymentRequest.getTravelerDetail());
                travelerDetailsList.add(paymentRequest.getTravelerDetail());
                paymentRequest.setTravelerDetailsList(travelerDetailsList);
            }
        }else{
            for(TravelerDetail trvlr : paymentRequest.getTravelerDetailsList())
                sanitizeTravellerDetail(trvlr);
        }

        if (!CollectionUtils.isEmpty(paymentRequest.getTravelerDetailsList())
                && !paymentRequest.getTravelerDetailsList().get(0).isMasterPax()){
            // make sure master pax is always first item in the list
            Collections.sort(paymentRequest.getTravelerDetailsList(),
                    Comparator.comparing(TravelerDetail::isMasterPax).reversed());
        }


    }

    private void sanitizeTravellerDetail(TravelerDetail travelerDetail) throws ScramblerClientException, LogicalException {

        if (travelerDetail == null)
            return;
        travelerDetail.setEmailID(commonHelper.sanitizeInput(travelerDetail.getEmailID()));
        travelerDetail.setFirstName(commonHelper.sanitizeInput(travelerDetail.getFirstName()));
        travelerDetail.setLastName(commonHelper.sanitizeInput(travelerDetail.getLastName()));
        travelerDetail.setMobileNo(commonHelper.sanitizeInput(travelerDetail.getMobileNo()));
        travelerDetail.setIsdCode(commonHelper.sanitizeInput(travelerDetail.getIsdCode()));
        travelerDetail.setRegisterGstinNum(commonHelper.sanitizeInput(travelerDetail.getRegisterGstinNum()));
        travelerDetail.setGstinCompanyAddress(commonHelper.sanitizeInput(travelerDetail.getGstinCompanyAddress()));
        travelerDetail.setGstinCompanyName(commonHelper.sanitizeInput(travelerDetail.getGstinCompanyName()));
        travelerDetail.setTitle(commonHelper.sanitizeInput(travelerDetail.getTitle()));
        travelerDetail.setPurposeOfTravel(commonHelper.sanitizeInput(travelerDetail.getPurposeOfTravel()));
        ScramblerClient scramblerClient = ScramblerClient.getInstance();
        if(StringUtils.isNotBlank(travelerDetail.getEmailID()))
            travelerDetail.setEmailCommId(scramblerClient.encode(travelerDetail.getEmailID(), HashType.F));
        if(StringUtils.isNotBlank(travelerDetail.getMobileNo())){
            travelerDetail.setPhoneCommId(getPhoneCommId(travelerDetail.getIsdCode(), travelerDetail.getMobileNo()));
        }

    }

    public void sanitizeTimeOutURL(BeginCheckoutReqBody paymentRequestClient) {
        if (paymentRequestClient == null || paymentRequestClient.getErrorConfig() == null
                || StringUtils.isEmpty(paymentRequestClient.getErrorConfig().getSessionTimeoutURL()))
            return;
        paymentRequestClient.getErrorConfig().setSessionTimeoutURL(commonHelper.sanitizeInput( paymentRequestClient.getErrorConfig().getSessionTimeoutURL()));
    }

    //note
    public com.mmt.hotels.model.request.payment.Gender getGenderFromTitle(String title) {
        com.mmt.hotels.model.request.payment.Gender gender = com.mmt.hotels.model.request.payment.Gender.MALE;
        if (title != null) {
            String ttl = title.trim().replace(".", "");
            if (ttl.equalsIgnoreCase(Constants.TITLE_MRS) || ttl.equalsIgnoreCase(Constants.TITLE_MS)
                    || ttl.equalsIgnoreCase(Constants.TITLE_MISS)) {
                gender = com.mmt.hotels.model.request.payment.Gender.FEMALE;
            }
        }
        return gender;

    }

    public UserServiceResponse populateAndCheckUserData(BeginCheckoutReqBody paymentRequestClient, Map<String, String> headerMap) throws ClientGatewayException,ScramblerClientException {
        UserServiceResponse userServiceResponse = getUserServiceResponse(paymentRequestClient,headerMap);
        if (userServiceResponse != null && userServiceResponse.getResult() != null && userServiceResponse.getResult().getExtendedUser() != null) {
            populateUUID(paymentRequestClient, userServiceResponse);
            if(Utility.isGccOrKsa() && userServiceResponse.getResult().getExtendedUser().getPersonalDetails() != null
            && StringUtils.isEmpty(userServiceResponse.getResult().getExtendedUser().getPersonalDetails().getNationality())){
                if(paymentRequestClient != null && CollectionUtils.isNotEmpty(paymentRequestClient.getTravelerDetailsList())
                && paymentRequestClient.getTravelerDetailsList().get(0) != null && StringUtils.isNotEmpty(paymentRequestClient.getTravelerDetailsList().get(0).getNationality())){
                    userService.updateUserDetails(paymentRequestClient.getTravelerDetailsList().get(0).getNationality()
                            ,userServiceResponse.getResult().getExtendedUser().getUuid()
                            ,paymentRequestClient.getSiteDomain()
                            ,paymentRequestClient.getCorrelationKey(),paymentRequestClient.getIdContext(), headerMap);
                }
            }
        }else{
            createGuestUser(paymentRequestClient, headerMap);
        }

        // check if the user service response has gstn details. If yes, then ignore the gstn details sent from client and take from user service response.
        populateAddressDetailsFromUserService(paymentRequestClient, userServiceResponse);
        // GST address state is mandatory to make a booking according to govt rules.
        // The implementation is currently only applicable for B2C and Non GCC countries.
        /*if (!Utility.isGCC() && Constants.B2C.equalsIgnoreCase(paymentRequestClient.getIdContext())
                && (paymentRequestClient.getAddressDetails() == null || StringUtils.isEmpty(paymentRequestClient.getAddressDetails().getState()))){
            throw new ClientGatewayException(DependencyLayer.CLIENTGATEWAY, ErrorType.VALIDATION, ValidationErrors.EMPTY_GSTN_DETAIL.getErrorCode(), ValidationErrors.EMPTY_GSTN_DETAIL.getErrorMsg());
        }*/

        if(paymentRequestClient.getUserDetail() == null || StringUtils.isBlank(paymentRequestClient.getUserDetail().getUuid()))
            throw new ClientGatewayException(DependencyLayer.USERSERVICE, ErrorType.AUTHENTICATION, AuthenticationErrors.UUID_NOT_FOUND.getErrorCode(), AuthenticationErrors.UUID_NOT_FOUND.getErrorMsg());

        return userServiceResponse;
    }

    /**
     * This method overrides the address details populated from the payment-checkout client request body if user service response has the data.
     *
     * @param paymentRequestClient is the request body for HES
     * @param userServiceResponse response received from user service downstream
     */
    private void populateAddressDetailsFromUserService(BeginCheckoutReqBody paymentRequestClient, UserServiceResponse userServiceResponse){
        if (userServiceResponse == null
                || userServiceResponse.getResult() == null
                || userServiceResponse.getResult().getExtendedUser() == null
                || CollectionUtils.isEmpty(userServiceResponse.getResult().getExtendedUser().getAddressDetails())) return;

        AddressDetails addressDetails = userServiceResponse.getResult().getExtendedUser().getAddressDetails().get(0);
        paymentRequestClient.setAddressDetails(addressDetails);
    }

    public UserServiceResponse getUserServiceResponse(BeginCheckoutReqBody paymentRequestClient, Map<String, String> headerMap) throws ClientGatewayException {
        UserServiceResponse userServiceResponse = null;
        String userEmailId = paymentRequestClient.getUserDetail() != null ?  paymentRequestClient.getUserDetail().getEmailID() :paymentRequestClient.getTravelerDetailsList().get(0).getEmailID() ;
        String mobNo = paymentRequestClient.getUserDetail() != null ? paymentRequestClient.getUserDetail().getMobileNo() :
                ( StringUtils.isNotBlank(paymentRequestClient.getTravelerDetailsList().get(0).getIsdCode()) ? paymentRequestClient.getTravelerDetailsList().get(0).getIsdCode() : "91" ) + paymentRequestClient.getTravelerDetailsList().get(0).getMobileNo();

        if (!StringUtils.isEmpty(paymentRequestClient.getAuthToken()) || !StringUtils.isEmpty(userEmailId)) {
            try {
                userServiceResponse = userService.getUserServiceResponse(paymentRequestClient.getAuthToken(), userEmailId, mobNo,
                        paymentRequestClient.getPaymentDetail().getChannel(), paymentRequestClient.getCorrelationKey(), paymentRequestClient.getIdContext(), paymentRequestClient.getSiteDomain(), null,headerMap);
            } catch (Exception ex) {
                logger.debug("Error occured while fetching uuid from user service  :{}, corr :{} ", ex.getMessage(), paymentRequestClient.getCorrelationKey(), ex);
                ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(ex);
                metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
                throw exceptionHandlerResponse.getClientGatewayException();
            }
        }
        return userServiceResponse;
    }

    public boolean checkUserOTPValidated(BeginCheckoutReqBody paymentRequestClient)  {
        try {
            if (consulFlag) {
                if (commonConfigConsul.isDisableUserServiceOtpValidation()) {
                    return true;
                }
            }
            else{
                if (propertyManager.getProperty("commonConfig", CommonConfig.class)
                        .disableUserServiceOtpValidation()) {
                    return true;
                }
            }

            if (paymentRequestClient.isRequiredOtpValidation()
                    && !StringUtils.isEmpty(paymentRequestClient.getAuthToken())) {
                //the mobile no in userdetail is one that is populated from USER SERVICE
                boolean isUserOptpValidated = (StringUtils.isNotBlank(paymentRequestClient.getUserDetail().getMobileNo()) &&
                    paymentRequestClient.getUserDetail().getMobileNo().contains(paymentRequestClient.getTravelerDetailsList().get(0).getMobileNo()));
                if (!isUserOptpValidated) {
                    logger.error("UserOptValidationFailed for correlationKey={} ,mmtAuth={}",
                            paymentRequestClient.getCorrelationKey(), paymentRequestClient.getAuthToken());
                }
                return isUserOptpValidated;
            }
        } catch (Exception ex) {
            logger.error("Error Occure in isUserOtpValidated= ", ex);
        }
        return true;

    }

    public void populateUUID(BeginCheckoutReqBody paymentRequestClient, UserServiceResponse userServiceResponse) throws ScramblerClientException, LogicalException {
        String correlationKey = paymentRequestClient.getCorrelationKey();
        try {
                if(paymentRequestClient.getUserDetail() == null)
                    paymentRequestClient.setUserDetail(new UserDetail());
                paymentRequestClient.getUserDetail().setUuid(userServiceResponse.getResult().getExtendedUser().getUuid());
                paymentRequestClient.getUserDetail().setProfileType(userServiceResponse.getResult().getExtendedUser().getProfileType());
                paymentRequestClient.getUserDetail().setSubProfileType(userServiceResponse.getResult().getExtendedUser().getAffiliateId());
                paymentRequestClient.setCorpUserId(userServiceResponse.getResult().getExtendedUser().getProfileId());

                if(Constants.PROFILE_CORPORATE.equalsIgnoreCase(paymentRequestClient.getUserDetail().getProfileType()))
                    paymentRequestClient.setIdContext(Constants.CORP_ID_CONTEXT);

                if (CollectionUtils.isNotEmpty(userServiceResponse.getResult().getExtendedUser().getLoginInfoList())) {
                    userServiceResponse.getResult().getExtendedUser().getLoginInfoList().forEach(loginInfo -> {
                        if ("MOBILE".equals(loginInfo.getLoginType())) {
                            paymentRequestClient.getUserDetail().setMobileNo(loginInfo.getLoginId());
                            paymentRequestClient.getUserDetail().setVerified(loginInfo.isVerified() || paymentRequestClient.getUserDetail().isVerified());
                        }else if ("EMAIL".equals(loginInfo.getLoginType()) ){
                            paymentRequestClient.getUserDetail().setEmailID(loginInfo.getLoginId());
                            paymentRequestClient.getUserDetail().setVerified(loginInfo.isVerified() || paymentRequestClient.getUserDetail().isVerified());
                        }else if(Constants.CHANNEL_FKPWA.equals(paymentRequestClient.getPaymentDetail().getChannel()) ) {
                            if(Constants.LOGIN_INFO_TYPE_FK_IDNTY.equals(loginInfo.getLoginType())) {
                                paymentRequestClient.setFk_token(loginInfo.getLoginId());
                            }
                        }

                    });
                }

                if(userServiceResponse.getResult().getExtendedUser().getPersonalDetails() != null){
                   paymentRequestClient.getUserDetail().setFirstName(userServiceResponse.getResult().getExtendedUser().getPersonalDetails().getName().getFirstName());
                    paymentRequestClient.getUserDetail().setLastName(userServiceResponse.getResult().getExtendedUser().getPersonalDetails().getName().getLastName());
                }
                if(StringUtils.isNotBlank(paymentRequestClient.getUserDetail().getEmailID())) {
                    ScramblerClient sc = ScramblerClient.getInstance();
                    paymentRequestClient.getUserDetail().setEmailCommId(sc.encode(paymentRequestClient.getUserDetail().getEmailID(), HashType.F));
                }
               paymentRequestClient.getUserDetail().setPhoneCommId(getPhoneCommId(null,paymentRequestClient.getUserDetail().getMobileNo()));

        }catch(Exception ex){
            logger.error("Error occurred in populating uuid, corr: {}",correlationKey, ex);
            throw  ex;

        }
    }

    public  void createGuestUser(BeginCheckoutReqBody paymentRequestClient, Map<String, String> headerMap) throws ScramblerClientException,ClientGatewayException{
        if (paymentRequestClient.getUserDetail() == null || StringUtils.isBlank(paymentRequestClient.getUserDetail().getUuid())) {
            UserServiceResponse guestUserResponse = null;
            try {
                guestUserResponse= userService.createGuestUser(paymentRequestClient.getTravelerDetailsList().get(0).getFirstName(), paymentRequestClient.getTravelerDetailsList().get(0).getLastName(),
                        paymentRequestClient.getTravelerDetailsList().get(0).getEmailID(),createGuestUserRetries, paymentRequestClient.getCorrelationKey(),paymentRequestClient.getSiteDomain(), headerMap);
                if (guestUserResponse != null && guestUserResponse.getResult() != null && guestUserResponse.getResult().getData() != null) {
                    paymentRequestClient.setUserDetail(new UserDetail());
                    paymentRequestClient.getUserDetail().setFirstName(paymentRequestClient.getTravelerDetailsList().get(0).getFirstName());
                    paymentRequestClient.getUserDetail().setFirstName(paymentRequestClient.getTravelerDetailsList().get(0).getLastName());
                    paymentRequestClient.getUserDetail().setEmailID(paymentRequestClient.getTravelerDetailsList().get(0).getEmailID());
                    paymentRequestClient.getUserDetail().setMobileNo(paymentRequestClient.getTravelerDetailsList().get(0).getMobileNo());
                    paymentRequestClient.getUserDetail().setUuid(guestUserResponse.getResult().getData().getUuid());
                    paymentRequestClient.getUserDetail().setProfileType("PERSONAL");
                    if(guestUserResponse.getResult().getExtendedUser()!= null)
                         paymentRequestClient.getUserDetail().setSubProfileType(guestUserResponse.getResult().getExtendedUser().getAffiliateId());
                    ScramblerClient sc = ScramblerClient.getInstance();
                    paymentRequestClient.getUserDetail().setEmailCommId(sc.encode(paymentRequestClient.getUserDetail().getEmailID(), HashType.F));
                    paymentRequestClient.getUserDetail().setPhoneCommId(getPhoneCommId(null,paymentRequestClient.getUserDetail().getMobileNo()));
                }

            }catch (Exception ex){
                logger.error("Error occured while guest user sign up :{} ,corr :{} ", ex.getMessage(), paymentRequestClient.getCorrelationKey(), ex);
                throw ex;
            }

        }
    }

    public String getPhoneCommId(String isd, String mobile) throws ScramblerClientException, LogicalException {
        Integer isdCode = StringUtils.isNotBlank(isd) ? Integer.parseInt(isd):91;
        String commid = null;
        if (StringUtils.isNotBlank(mobile)) {
            ScramblerClient scramblerClient = ScramblerClient.getInstance();
            /**
            Commenting this code as we are getting isd code and mobile from client
            User cannot enter mobile number without isd code . Hence, no need of the below commented code.
            */
//            try{
//                PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();
//                Phonenumber.PhoneNumber phoneNumber = phoneNumberUtil.parse("+" + mobile, "");
//                if (phoneNumberUtil.isValidNumber(phoneNumber)) {
//                    isdCode = phoneNumber.getCountryCode();
//                    mobile = Long.toString(phoneNumber.getNationalNumber());
//                }
//            }catch (Exception ex){
//                logger.info("Phone number from client is without country code code :{} " , ex.getMessage());
//            }
            try {
                commid = scramblerClient.encode(mobile, isdCode, HashType.N);
            }catch (ScramblerClientException e) {
                throw new LogicalException(DependencyLayer.CLIENTGATEWAY, ErrorType.LOGICAL,
                        LogicalErrors.UUID_SCRAMBLER.getErrorCode(), LogicalErrors.UUID_SCRAMBLER.getErrorMsg());
            }
        }

        return  commid;
    }



    /**
     * This method is used to get user service response
     * @param offerDetailsRequest offerDetailsRequest containing correlationKey
     * @param headerMap headerMap containing uuid, authToken, region
     * @return UserServiceResponse
     */
    public UserServiceResponse getUserServiceResponseFromUUID(OfferDetailsRequest offerDetailsRequest, Map<String, String> headerMap) {
        UserServiceResponse userServiceResponse = null;
        String authToken = headerMap.get(MMT_AUTH);
        String uuid = headerMap.get(UUID);
        String region = headerMap.get(REGION);
        if (StringUtils.isNotEmpty(authToken) || StringUtils.isNotEmpty(uuid)) {
            try {
                userServiceResponse = userService.getUserServiceResponse(authToken, null, null, null, offerDetailsRequest.getCorrelationKey(), null, region, uuid, headerMap);
            } catch (Exception ex) {
                logger.debug("Error occured while fetching uuid from user service  :{}, corr :{} ", ex.getMessage(), offerDetailsRequest.getCorrelationKey(), ex);
                ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(ex);
                metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
                //throw exceptionHandlerResponse.getClientGatewayException();
            }
        }
        return userServiceResponse;
    }



}
