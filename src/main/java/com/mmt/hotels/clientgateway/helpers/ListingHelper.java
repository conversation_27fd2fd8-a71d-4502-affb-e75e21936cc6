package com.mmt.hotels.clientgateway.helpers;

import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.enums.UnexpectedErrors;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.Margin;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.Style;
import com.mmt.hotels.clientgateway.response.listing.UpsellRateplanResponse;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomRatePlan;
import com.mmt.hotels.clientgateway.response.rooms.UpsellRateplans;
import com.mmt.hotels.clientgateway.response.searchHotels.FetchCollection;
import com.mmt.hotels.clientgateway.service.ListingService;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.factory.SearchRoomsFactory;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.DPTExperimentDetails;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.request.FilterSearchMetaDataResponse;
import com.mmt.hotels.model.response.mypartner.MarkUpDetails;
import com.mmt.hotels.model.response.pricing.RatePlan;
import com.mmt.hotels.model.response.searchwrapper.ListingPagePersonalizationResponsBO;
import com.mmt.hotels.model.response.searchwrapper.PersonalizedResponse;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperResponseBO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.EXP_BNPL_NEW_VARIANT;
import static com.mmt.hotels.clientgateway.constants.Constants.PRICE_TOP;
import static com.mmt.hotels.clientgateway.constants.Constants.POPULAR_PACKAGE;
import static com.mmt.hotels.clientgateway.constants.Constants.TEXT_WITH_BG_IMAGE;
import static com.mmt.hotels.clientgateway.constants.Constants.TITLE;
import static com.mmt.hotels.clientgateway.constants.Constants.COLOR;
import static com.mmt.hotels.clientgateway.constants.Constants.PERSUASION_BG_URL;
import static com.mmt.hotels.clientgateway.constants.Constants.HORIZONTAL_MARGIN;
import static com.mmt.hotels.clientgateway.constants.Constants.VERTICAL_MARGIN;
import static com.mmt.hotels.clientgateway.constants.Constants.SMALL;
import static com.mmt.hotels.clientgateway.constants.Constants.BG_URL;

@Component
@PropertySource("classpath:defaultSequenceId.properties")
public class ListingHelper {

	@Autowired
	CommonConfigConsul commonConfigConsul;
	
	@Autowired
	private CommonConfigHelper commonConfigHelper;
    
    @Autowired
	private Environment env;

	@Value("${pool.name}")
	private String poolName;
    
    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
	private PolyglotService polyglotService;

	@Autowired
	private DateUtil dateUtil;

	@Autowired
	private Utility utility;

	@Autowired
	private SearchRoomsFactory searchRoomsFactory;

	private Map<String, String> mealPlanMapPolyglot;

	@Value("${addon.info.most.popular.tag}")
	private String addOnInfoMostPopularTagConfig;

	private Map<String, String> addOnInfoMostPopularTag;

	private static final Logger logger = LoggerFactory.getLogger(ListingService.class);

	private static Gson gson = new Gson();

	@PostConstruct
	public void init() {
		try {
			mealPlanMapPolyglot = commonConfigConsul.getMealPlanMapPolyglot();
			addOnInfoMostPopularTag = gson.fromJson(addOnInfoMostPopularTagConfig,HashMap.class);
			logger.debug("Fetching values from commonConfig consul");
		} catch (Exception e){
			logger.error("Error while fetching values from commonConfig consul");
		}
	}

	public ListingPagePersonalizationResponsBO convertSearchHotelsToPersonalizedHotels(
			SearchWrapperResponseBO<SearchWrapperHotelEntity> searchWrapperResponseBO, SearchHotelsRequest searchHotelsRequest) {
		if (searchWrapperResponseBO == null)
			return null;
		ListingPagePersonalizationResponsBO listingPagePersonalizationResponsBO = new ListingPagePersonalizationResponsBO();
		listingPagePersonalizationResponsBO.setBlackEligibilityDays(searchWrapperResponseBO.getBlackEligibilityDays());
		listingPagePersonalizationResponsBO.setBlackEligible(searchWrapperResponseBO.isBlackEligible());
		listingPagePersonalizationResponsBO.setHotelCountInCity(searchWrapperResponseBO.getHotelCountInCity());
		listingPagePersonalizationResponsBO.setBrinDetail(searchWrapperResponseBO.getBrinDetail());
		listingPagePersonalizationResponsBO.setCityCode(searchWrapperResponseBO.getCityCode());
		listingPagePersonalizationResponsBO.setTrackingMap(searchWrapperResponseBO.getTrackingMap());
		listingPagePersonalizationResponsBO.setCityName(searchWrapperResponseBO.getCityName());
		listingPagePersonalizationResponsBO.setCorrelationKey(searchWrapperResponseBO.getCorrelationKey());
		listingPagePersonalizationResponsBO.setCountryCode(searchWrapperResponseBO.getCountryCode());
		listingPagePersonalizationResponsBO.setCountryName(searchWrapperResponseBO.getCountryName());
		listingPagePersonalizationResponsBO.setCurrency(searchWrapperResponseBO.getCurrency());
		listingPagePersonalizationResponsBO.setExpData(searchWrapperResponseBO.getExpData());
		listingPagePersonalizationResponsBO.setFailureReason(searchWrapperResponseBO.getFailureReason());
		listingPagePersonalizationResponsBO.setFirstTimeUser(searchWrapperResponseBO.isFirstTimeUser());
		listingPagePersonalizationResponsBO.setIncrementalDiscount(searchWrapperResponseBO.getIncrementalDiscount());
		listingPagePersonalizationResponsBO.setLastFetchedHotelCategory(searchWrapperResponseBO.getLastFetchedHotelCategory());
		listingPagePersonalizationResponsBO.setLastFetchedHotelId(searchWrapperResponseBO.getLastFetchedHotelId());
		listingPagePersonalizationResponsBO.setLastFetchedWindowInfo(searchWrapperResponseBO.getLastFetchedWindowInfo());
		listingPagePersonalizationResponsBO.setRankingHotelCount(searchWrapperResponseBO.getRankingHotelCount());
		listingPagePersonalizationResponsBO.setLastHotelIndex(searchWrapperResponseBO.getLastHotelIndex());
		listingPagePersonalizationResponsBO.setMatchMakerQuestions(searchWrapperResponseBO.getMatchMakerQuestions());
		listingPagePersonalizationResponsBO.setMatchMakerResponse(searchWrapperResponseBO.isMatchMakerResponse());
		listingPagePersonalizationResponsBO.setNoMoreAvailableHotels(searchWrapperResponseBO.isNoMoreAvailableHotels());
		listingPagePersonalizationResponsBO.setPersonalizedResponse(buildPersonalizedResponse(searchWrapperResponseBO,searchHotelsRequest.getRequestDetails(), searchHotelsRequest.getFeatureFlags(), searchHotelsRequest));
		listingPagePersonalizationResponsBO.setResponseErrors(searchWrapperResponseBO.getResponseErrors());
		listingPagePersonalizationResponsBO.setShowFcBanner(searchWrapperResponseBO.getShowFcBanner());
		listingPagePersonalizationResponsBO.setSortCriteria(searchWrapperResponseBO.getSortCriteria());
		listingPagePersonalizationResponsBO.setStaticHotelCounts(searchWrapperResponseBO.getStaticHotelCounts());
		listingPagePersonalizationResponsBO.setSuppressPas(searchWrapperResponseBO.isSuppressPas());
		listingPagePersonalizationResponsBO.setTotalHotelCounts(searchWrapperResponseBO.getTotalHotelCounts());
		listingPagePersonalizationResponsBO.setWarningMessage(searchWrapperResponseBO.getWarningMessage());
		listingPagePersonalizationResponsBO.setLocusData(searchWrapperResponseBO.getLocusData());
		listingPagePersonalizationResponsBO.setUsradid(searchWrapperResponseBO.getUsradid());
		listingPagePersonalizationResponsBO.setHydraSegments(searchWrapperResponseBO.getHydraSegments());
		listingPagePersonalizationResponsBO.setSectionsType(searchWrapperResponseBO.getSectionsType());
		listingPagePersonalizationResponsBO.setSharingUrl(searchWrapperResponseBO.getSharingUrl());
		return listingPagePersonalizationResponsBO;
	}

	private List<PersonalizedResponse<SearchWrapperHotelEntity>> buildPersonalizedResponse(SearchWrapperResponseBO<SearchWrapperHotelEntity> searchWrapperResponseBO, RequestDetails requestDetails, FeatureFlags featureFlags, SearchHotelsRequest searchHotelsRequest) {

		List<PersonalizedResponse<SearchWrapperHotelEntity>> personalizedResponses= new ArrayList<>();

		if (CollectionUtils.isNotEmpty(searchWrapperResponseBO.getHotelList())) {
			PersonalizedResponse<SearchWrapperHotelEntity> personalizedResponse = new PersonalizedResponse<SearchWrapperHotelEntity>();
			personalizedResponse.setSection(Constants.RECOMMENDED_HOTELS_SECTION);
			if ("GETAWAY".equalsIgnoreCase(requestDetails.getFunnelSource())) {
				personalizedResponse.setHeading(polyglotService.getTranslatedData(ConstantsTranslation.GETAWAYS_RECOMMENDED_HEADING));
			}else if (featureFlags!= null && featureFlags.isMostBooked()) {
				personalizedResponse.setHeading(polyglotService.getTranslatedData(ConstantsTranslation.MOST_BOOKED_HEADING));
			}else {
				String heading = polyglotService.getTranslatedData(ConstantsTranslation.RECOMMENDED_HOTELS_HEADING_NEW);
				heading = updateHeading(heading, searchHotelsRequest, searchWrapperResponseBO.getCityName());
				personalizedResponse.setHeading(heading);
			}
			personalizedResponse.setHorizontal(false);
			personalizedResponse.setCount(searchWrapperResponseBO.getHotelList().size());
			personalizedResponse.setHotels(searchWrapperResponseBO.getHotelList());
			//in case of SimilarHotels are true we will get this card from HES and need to give it to client always. HTL-37224
			if(StringUtils.isNotBlank(searchWrapperResponseBO.getHotelCardType())){
				personalizedResponse.setHotelCardType(searchWrapperResponseBO.getHotelCardType());
			}
			personalizedResponses.add(personalizedResponse);
		}
		if (CollectionUtils.isNotEmpty(searchWrapperResponseBO.getNearbyHotelList())) {
			PersonalizedResponse<SearchWrapperHotelEntity> personalizedResponse = new PersonalizedResponse<SearchWrapperHotelEntity>();
			personalizedResponse.setSection(Constants.NEARBY_HOTELS_SECTION);
			personalizedResponse.setHeading(searchWrapperResponseBO.getNearbyHeading());
			personalizedResponse.setSubHeading(searchWrapperResponseBO.getNearbySubHeading());
			personalizedResponse.setHorizontal(false);
			personalizedResponse.setCount(searchWrapperResponseBO.getNearbyHotelList().size());
			personalizedResponse.setHotels(searchWrapperResponseBO.getNearbyHotelList());
			personalizedResponses.add(personalizedResponse);
		}
		if (MapUtils.isNotEmpty(searchWrapperResponseBO.getSectionWiseHotelList())) {
			searchWrapperResponseBO.getSectionWiseHotelList().forEach((key, value) -> {
				if (CollectionUtils.isNotEmpty(value)) {
					PersonalizedResponse<SearchWrapperHotelEntity> personalizedResponse = new PersonalizedResponse<SearchWrapperHotelEntity>();
					personalizedResponse.setSection(key.name());
					if (MapUtils.isNotEmpty(searchWrapperResponseBO.getSectionWiseMetaData()) && searchWrapperResponseBO.getSectionWiseMetaData().containsKey(key)) {
						personalizedResponse.setHeading(searchWrapperResponseBO.getSectionWiseMetaData().get(key).getHeading());
						personalizedResponse.setSubHeading(searchWrapperResponseBO.getSectionWiseMetaData().get(key).getSubHeading());
					}
					personalizedResponse.setHorizontal(false);
					personalizedResponse.setCount(value.size());
					personalizedResponse.setHotels(value);
					personalizedResponses.add(personalizedResponse);
				}
			});
		}
		/* DEPRECATED FEATURE
		if (CollectionUtils.isNotEmpty(searchWrapperResponseBO.getOtherAltAccoHotelList())) {
			PersonalizedResponse<SearchWrapperHotelEntity> personalizedResponse = new PersonalizedResponse<SearchWrapperHotelEntity>();
			personalizedResponse.setSection(Constants.OTHER_ALTACCO_HOTELS_SECTION);
			personalizedResponse.setHeading(searchWrapperResponseBO.getOtherAltAccoHeading());
			personalizedResponse.setHorizontal(false);
			personalizedResponse.setCount(searchWrapperResponseBO.getOtherAltAccoHotelList().size());
			personalizedResponse.setHotels(searchWrapperResponseBO.getOtherAltAccoHotelList());
			personalizedResponses.add(personalizedResponse);
		}
		if (CollectionUtils.isNotEmpty(searchWrapperResponseBO.getNonAltAccoHotelList())) {
			PersonalizedResponse<SearchWrapperHotelEntity> personalizedResponse = new PersonalizedResponse<SearchWrapperHotelEntity>();
			personalizedResponse.setSection(Constants.NON_ALTACCO_HOTELS_SECTION);
			personalizedResponse.setHeading(searchWrapperResponseBO.getNonAltAccoHeading());
			personalizedResponse.setHorizontal(false);
			personalizedResponse.setCount(searchWrapperResponseBO.getNonAltAccoHotelList().size());
			personalizedResponse.setHotels(searchWrapperResponseBO.getNonAltAccoHotelList());
			personalizedResponses.add(personalizedResponse);
		} */
		if (CollectionUtils.isEmpty(personalizedResponses))
			return null;
		return personalizedResponses;
	}

	private String updateHeading(String heading, SearchHotelsRequest searchHotelsRequest, String cityName){
		//Recommend_Hotels section's heading is to be updated to Properties in {City/Area}
		if (searchHotelsRequest.getMatchMakerDetails() != null && CollectionUtils.isNotEmpty(searchHotelsRequest.getMatchMakerDetails().getSelectedTags())) {
			heading = heading.replace("{AREA}", searchHotelsRequest.getMatchMakerDetails().getSelectedTags().get(0).getTagDescription());
		} else if (StringUtils.isNotBlank(cityName)) {
			heading = heading.replace("{AREA}", cityName);
		} else {
			heading = polyglotService.getTranslatedData(ConstantsTranslation.RECOMMENDED_HOTELS_HEADING);
		}
		return heading;
	}

	public void updateCollectionCounts(ListingSearchRequest searchHotelsRequest){
		if (MapUtils.isNotEmpty(commonConfigHelper.getCollectionCountMapping())
				&& StringUtils.isNotBlank(searchHotelsRequest.getDeviceDetails().getBookingDevice())
				&& commonConfigHelper.getCollectionCountMapping().containsKey(searchHotelsRequest.getDeviceDetails().getBookingDevice().toUpperCase())) {
			Map<String, String> pageContextToCountMap = commonConfigHelper.getCollectionCountMapping().get(searchHotelsRequest.getDeviceDetails().getBookingDevice().toUpperCase());
			if (MapUtils.isNotEmpty(pageContextToCountMap) && StringUtils.isNotBlank(searchHotelsRequest.getRequestDetails().getPageContext())
					&& pageContextToCountMap.containsKey(searchHotelsRequest.getRequestDetails().getPageContext().toUpperCase())
					&& searchHotelsRequest.getSearchCriteria().getCollectionCriteria() != null)
				searchHotelsRequest.getSearchCriteria().getCollectionCriteria().setCollectionsCount(pageContextToCountMap.get(searchHotelsRequest.getRequestDetails().getPageContext().toUpperCase()));
		}
	}

	public void sortBasedOnPriority(List<FetchCollection> fetchCollectionList) {
		Collections.sort(fetchCollectionList, new Comparator<FetchCollection>() {
			@Override
			public int compare(FetchCollection coll1, FetchCollection coll2) {
				if(coll1==null || coll2==null || coll1.getPriority()==null || coll2.getPriority()==null)
					return 0;
				return Integer.valueOf(coll1.getPriority()) - Integer.valueOf(coll2.getPriority());
			}
		});
	}

	public void populateDPTExperimentDetailsList(List<DPTExperimentDetails> dptExperimentDetailsList, FilterSearchMetaDataResponse filterResponseHES) {
		if (dptExperimentDetailsList != null && filterResponseHES != null && filterResponseHES.getDptContextualFilterResponse() != null && CollectionUtils.isNotEmpty(filterResponseHES.getDptContextualFilterResponse().getExpDetails())) {
			filterResponseHES.getDptContextualFilterResponse().getExpDetails().stream().map(experimentDetail -> {
				DPTExperimentDetails dptExperimentDetails = null;
				if (experimentDetail != null) {
					dptExperimentDetails = new DPTExperimentDetails();
					dptExperimentDetails.setExperimentId(experimentDetail.getExperimentId());
					dptExperimentDetails.setExperimentVersion(experimentDetail.getVersionId());
					dptExperimentDetails.setVariantId(experimentDetail.getVariantId());
					dptExperimentDetailsList.add(dptExperimentDetails);
				}
				return dptExperimentDetails;
			}).collect(Collectors.toList());

		}
	}

	public UpsellRateplanResponse convertFetchUpsellRateplanresponse(ListingPagePersonalizationResponsBO searchWrapperResponseBO ,SearchHotelsRequest fetchUpsellRateplanRequest, CommonModifierResponse commonModifierResponse) {
		List<SelectRoomRatePlan> selectRoomRatePlans = new ArrayList<>();
		String baseroomCode = "";
		String baseRoomName = "";
		if (searchWrapperResponseBO == null || CollectionUtils.isEmpty(searchWrapperResponseBO.getPersonalizedResponse()))
			return null;
		for(SearchWrapperHotelEntity searchWrapperHotelEntity : searchWrapperResponseBO.getPersonalizedResponse().get(0).getHotels()){
			baseroomCode = searchWrapperHotelEntity.getLowestRoomCode();
			baseRoomName = searchWrapperHotelEntity.getLowestRoomType();
			String checkIn = fetchUpsellRateplanRequest.getSearchCriteria().getCheckIn();
			String checkOut = fetchUpsellRateplanRequest.getSearchCriteria().getCheckOut();
			int los = dateUtil.getDaysDiff(LocalDate.parse(checkIn), LocalDate.parse(checkOut));
			int ap = dateUtil.getDaysDiff(LocalDate.now(), LocalDate.parse(checkIn));
			boolean isBlockPAH = searchWrapperResponseBO.getExpData() != null && searchWrapperResponseBO.getExpData().containsKey("blockPAH")
					&& org.apache.commons.lang3.StringUtils.isNotBlank(searchWrapperResponseBO.getExpData().get("blockPAH")) && Boolean.parseBoolean(searchWrapperResponseBO.getExpData().get("blockPAH"));

			String askedCurrency = fetchUpsellRateplanRequest.getSearchCriteria() != null ? fetchUpsellRateplanRequest.getSearchCriteria().getCurrency() : "INR";
			selectRoomRatePlans = getUpsellRatePlans(searchWrapperHotelEntity.getListingType(),
					fetchUpsellRateplanRequest.getExpData(), true, askedCurrency, searchWrapperHotelEntity.getSellableType(), fetchUpsellRateplanRequest.getRequestDetails().getFunnelSource(),
					los, ap , isBlockPAH,commonModifierResponse, searchWrapperHotelEntity.isAltAcco(), new MarkUpDetails(), 0.0, fetchUpsellRateplanRequest.getDeviceDetails().getBookingDevice(), searchWrapperHotelEntity.getUpsellRateplanList());
		}

		if (CollectionUtils.isEmpty(selectRoomRatePlans))
			return null;
		UpsellRateplanResponse upsellRateplanResponse = new UpsellRateplanResponse();
		UpsellRateplans upsellRateplans = new UpsellRateplans();
		upsellRateplans.setRoomCode(baseroomCode);
		upsellRateplans.setRatePlans(selectRoomRatePlans);
		upsellRateplans.setRoomName(baseRoomName);
		upsellRateplanResponse.setUpsellRateplans(upsellRateplans);
		return upsellRateplanResponse;
	}

	private List<SelectRoomRatePlan> getUpsellRatePlans(String listingType, String expData, boolean ratePlanGroup, String askedCurrency,
												  String sellableType, String funnelSource, int days, int ap, boolean isBlockPAH,
												  CommonModifierResponse commonModifierResponse, boolean isAltAccoHotel, final MarkUpDetails markUpDetails, Double foodRating, String client, Map<String, RatePlan> ratePlansHes) {
		List<SelectRoomRatePlan> ratePlans = new ArrayList<>();
		Map<String, List<com.mmt.hotels.model.response.pricing.RatePlan>> rpcRatePlanMap = new LinkedHashMap<>();
		boolean myPartner = Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
		boolean isBnplOneVariant = false;
		Map<String, String> experimentDataMap = utility.getExpDataMap(expData);
		if (MapUtils.isNotEmpty(experimentDataMap)) {
			isBnplOneVariant = experimentDataMap.containsKey(EXP_BNPL_NEW_VARIANT) && Boolean.parseBoolean(experimentDataMap.get(EXP_BNPL_NEW_VARIANT));
		}
		Comparator<RatePlan> compRatePlanPrice = (h1, h2) -> Double.valueOf(h1.getDisplayFare().getDisplayPriceBreakDown().getDisplayPrice()).compareTo(Double.valueOf(h2.getDisplayFare().getDisplayPriceBreakDown().getDisplayPrice()));
		List<RatePlan> sortedRatePlanList = ratePlansHes.values().stream().sorted(compRatePlanPrice).collect(Collectors.toList());
		for (RatePlan ratePlanHes : sortedRatePlanList) {
			String ratePlanCode = ratePlanHes.getRatePlanCode();
			ratePlanHes.setRatePlanCode(ratePlanCode);
			if (ratePlanGroup) {
				rpcRatePlanMap.computeIfAbsent(ratePlanCode, k -> new ArrayList<>());
				rpcRatePlanMap.get(ratePlanCode).add(ratePlanHes);
			}else {
				rpcRatePlanMap.computeIfAbsent(ratePlanHes.getRpcc(), k -> new ArrayList<>());
				rpcRatePlanMap.get(ratePlanHes.getRpcc()).add(ratePlanHes);
			}
		}
		//need to add mostPopular tag
		int count = 0;
		boolean newPropertyOfferApplicable = commonModifierResponse!=null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) ? utility.isExperimentOn(commonModifierResponse.getExpDataMap(), ExperimentKeys.NEW_PROPERTY_OFFER.getKey()) : false;

		for (String rpc : rpcRatePlanMap.keySet()) {
			SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
			BNPLVariant bnplVariant = rpcRatePlanMap.get(rpc).get(0).getBnplVariant();
			ratePlan.setRpc(rpc);
			ratePlan.setVendorRatePlanCode(rpcRatePlanMap.get(rpc).get(0).getRpcc());
			ratePlan.setPayMode(rpcRatePlanMap.get(rpc).get(0).getPaymentDetails().getPaymentMode().name());
			ratePlan.setSupplierCode(rpcRatePlanMap.get(rpc).get(0).getSupplierDetails().getSupplierCode());
			ratePlan.setFilterCode(searchRoomsFactory.getResponseService(client).getFilterCodes(rpcRatePlanMap.get(rpc).get(0),isBlockPAH,ap,commonModifierResponse,false, null));
			ratePlan.setInclusionsList(searchRoomsFactory.getResponseService(client).transformInclusions(
					rpcRatePlanMap.get(rpc).get(0), mealPlanMapPolyglot, ap, isBlockPAH, expData, null,
					askedCurrency, true, null));
			ratePlan.setTariffs(searchRoomsFactory.getResponseService(client).getTariffs(rpcRatePlanMap.get(rpc), expData, askedCurrency, sellableType, days, funnelSource, false,
					myPartner, isAltAccoHotel, markUpDetails, newPropertyOfferApplicable, false));
			String partialRefundText = utility.buildPartialRefundDateText(rpcRatePlanMap.get(rpc).get(0).getCancellationTimeline());
			ratePlan.setCancellationPolicy(utility.transformCancellationPolicy(rpcRatePlanMap.get(rpc).get(0).getCancelPenaltyList(), false, isBnplOneVariant, bnplVariant, null, polyglotService.getTranslatedData(ConstantsTranslation.CANCELLATION_POLICY_NR_NON_INSTANT_TEXT), null, Optional.ofNullable(rpcRatePlanMap.get(rpc).get(0).getMpFareHoldStatus()), partialRefundText, false));
			ratePlan.setName(utility.getRatePlanName(rpcRatePlanMap.get(rpc).get(0).getMealPlans(), ratePlan.getCancellationPolicy(), sellableType, listingType, expData));
			ratePlan.setSellableType(listingType);
			if(rpcRatePlanMap.get(rpc).get(0).isMostPopularRateplan()){
				List<PersuasionResponse> persuasions = new ArrayList<>();
				PersuasionResponse persuasionResponse = new PersuasionResponse();
				persuasionResponse.setPlaceholderId(PRICE_TOP);
				persuasionResponse.setId(POPULAR_PACKAGE);
				persuasionResponse.setPersuasionText(addOnInfoMostPopularTag.get(TITLE));
				persuasionResponse.setTemplate(TEXT_WITH_BG_IMAGE);
				Style style = new Style();
				style.setTextColor(addOnInfoMostPopularTag.get(COLOR));
				style.setBgUrl(addOnInfoMostPopularTag.get(PERSUASION_BG_URL));
				Margin margin = new Margin();
				margin.setHorizontal(addOnInfoMostPopularTag.get(HORIZONTAL_MARGIN));
				margin.setVertical(addOnInfoMostPopularTag.get(VERTICAL_MARGIN));
				style.setMargin(margin);
				style.setFontSize(SMALL);
				persuasionResponse.setStyle(style);
				persuasions.add(persuasionResponse);
				ratePlan.setPersuasions(persuasions);
				ratePlan.setBgUrl(addOnInfoMostPopularTag.get(BG_URL));
			}
			count++;
			ratePlans.add(ratePlan);
		}

		return ratePlans;
	}


}
