package com.mmt.hotels.clientgateway.helpers;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mmt.hotels.clientgateway.businessobjects.*;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.thankyou.MyTripCard;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.response.listpersonalization.LuxeToolTip;
import com.mmt.hotels.model.response.listpersonalization.MyBizAssuredToolTip;
import com.mmt.hotels.model.response.listpersonalization.MySafetyTooltip;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysTooltip;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class PolyglotHelper {

    @Autowired
    PolyglotService polyglotService;


    public FilterConfiguration translateFilterConfig(FilterConfiguration fConfig,String funnelSource) {

        if (fConfig == null){
            return null;
        }

        if (MapUtils.isNotEmpty(fConfig.getFilters())){

            for (Map.Entry<String, FilterConfigCategory> entry : fConfig.getFilters().entrySet()) {

                FilterConfigCategory filterConfigCategory = entry.getValue();
                filterConfigCategory.setCustomRangeTitle(polyglotService.getTranslatedData(filterConfigCategory.getCustomRangeTitle(),funnelSource));
                filterConfigCategory.setTitle(polyglotService.getTranslatedData(filterConfigCategory.getTitle(),funnelSource));
                filterConfigCategory.setDescription(polyglotService.getTranslatedData(filterConfigCategory.getDescription(),funnelSource));

                if (MapUtils.isNotEmpty(filterConfigCategory.getGroups())){

                    for (Map.Entry<String, LinkedHashMap<String, FilterConfigDetail>> group : filterConfigCategory.getGroups().entrySet()){

                        LinkedHashMap<String, FilterConfigDetail> filterConfigDetailLinkedHashMap = group.getValue();

                        if (MapUtils.isNotEmpty(filterConfigDetailLinkedHashMap)){

                            for (Map.Entry<String, FilterConfigDetail> filterConfigDetailEntry : filterConfigDetailLinkedHashMap.entrySet()){

                                FilterConfigDetail filterConfigDetail = filterConfigDetailEntry.getValue();
                                if (filterConfigDetail != null){

                                    filterConfigDetail.setTitle(polyglotService.getTranslatedData(filterConfigDetail.getTitle(),funnelSource));
                                    filterConfigDetail.setSubTitle(polyglotService.getTranslatedData(filterConfigDetail.getSubTitle(),funnelSource));
                                    if(StringUtils.isNotBlank(filterConfigDetail.getInfoText())){
                                        filterConfigDetail.setInfoText(polyglotService.getTranslatedData(filterConfigDetail.getInfoText(),funnelSource));
                                    }
                                    if(StringUtils.isNotBlank(filterConfigDetail.getDescription())){
                                        filterConfigDetail.setDescription(polyglotService.getTranslatedData(filterConfigDetail.getDescription()));
                                    }
                                }
                            }
                        }
                    }
                }
            }


        }

        return fConfig;

    }

    public FilterConfigurationV2 translateFilterPageData(FilterConfigurationV2 fConfig, String funnelSource) {

        if (fConfig == null){
            return null;
        }

        LinkedHashMap<String, FilterPage> pagesConfig = fConfig.getFilterPages() == null ? fConfig.getPagesToShow() : fConfig.getFilterPages();

        if (MapUtils.isEmpty(pagesConfig)){
            return fConfig;
        }
        for (Map.Entry<String, FilterPage> pageEntry : pagesConfig.entrySet()) {
            if (pageEntry.getValue() == null || MapUtils.isEmpty(pageEntry.getValue().getFilters())){
                continue;
            }

            for (Map.Entry<String, FilterConfigCategory> entry : pageEntry.getValue().getFilters().entrySet()) {

                if (entry.getValue() == null){
                    continue;
                }
                FilterConfigCategory filterConfigCategory = entry.getValue();
                filterConfigCategory.setCustomRangeTitle(polyglotService.getTranslatedData(filterConfigCategory.getCustomRangeTitle(),funnelSource));
                filterConfigCategory.setTitle(polyglotService.getTranslatedData(filterConfigCategory.getTitle(),funnelSource));
                filterConfigCategory.setDescription(polyglotService.getTranslatedData(filterConfigCategory.getDescription(),funnelSource));

                if (MapUtils.isEmpty(filterConfigCategory.getGroups())){
                    continue;
                }
                for (Map.Entry<String, LinkedHashMap<String, FilterConfigDetail>> group : filterConfigCategory.getGroups().entrySet()){

                    LinkedHashMap<String, FilterConfigDetail> filterConfigDetailLinkedHashMap = group.getValue();

                    if (MapUtils.isEmpty(filterConfigDetailLinkedHashMap)){
                        continue;
                    }
                    for (Map.Entry<String, FilterConfigDetail> filterConfigDetailEntry : filterConfigDetailLinkedHashMap.entrySet()){

                        FilterConfigDetail filterConfigDetail = filterConfigDetailEntry.getValue();
                        if (filterConfigDetail == null){
                            continue;
                        }
                        filterConfigDetail.setTitle(polyglotService.getTranslatedData(filterConfigDetail.getTitle(),funnelSource));
                        filterConfigDetail.setSubTitle(polyglotService.getTranslatedData(filterConfigDetail.getSubTitle(),funnelSource));
                        if(StringUtils.isNotBlank(filterConfigDetail.getInfoText())){
                            filterConfigDetail.setInfoText(polyglotService.getTranslatedData(filterConfigDetail.getInfoText(),funnelSource));
                        }
                        if(StringUtils.isNotBlank(filterConfigDetail.getDescription())){
                            filterConfigDetail.setDescription(polyglotService.getTranslatedData(filterConfigDetail.getDescription()));
                        }
                    }
                }
            }
        }

        return fConfig;

    }

    public void translateMyTripsCards(Map<String, MyTripCard> myTripsCardTypeToCardDetailsModified,
                                      Map<String, String> myTripsCardTypeToCardTextReplacer) {

        if (MapUtils.isNotEmpty(myTripsCardTypeToCardDetailsModified)){


            for (Map.Entry<String, MyTripCard> element : myTripsCardTypeToCardDetailsModified.entrySet()){

                MyTripCard myTripCard = element.getValue();
                if (myTripsCardTypeToCardTextReplacer != null && myTripsCardTypeToCardTextReplacer.containsKey(myTripCard.getText())) {
                    myTripCard.setText(myTripsCardTypeToCardTextReplacer.get(myTripCard.getText()));
                }
                myTripCard.setText(polyglotService.getTranslatedData(myTripCard.getText()));

            }
        }
    }

    public void translatePersuasionMap(Map<String, Map<String, PersuasionResponse>> persuasionResponseMap) {

        if (MapUtils.isNotEmpty(persuasionResponseMap)){

            for (Map.Entry<String, Map<String, PersuasionResponse>> mySafetyData : persuasionResponseMap.entrySet()){

                Map<String, PersuasionResponse> safetyMap = mySafetyData.getValue();

                if (MapUtils.isNotEmpty(safetyMap)){

                    for (Map.Entry<String, PersuasionResponse> entry : safetyMap.entrySet()){

                        PersuasionResponse persuasion = entry.getValue();
                        persuasion.setTitle(polyglotService.getTranslatedData(persuasion.getTitle()));
                        persuasion.setSubText(polyglotService.getTranslatedData(persuasion.getSubText()));
                        persuasion.setDisplayText(polyglotService.getTranslatedData(persuasion.getDisplayText()));
                        persuasion.setDisplaySubText(polyglotService.getTranslatedData(persuasion.getDisplaySubText()));
                        persuasion.setId("MYSAFETY_PERSUASION");
                        persuasion.setPlaceholderId("MYSAFETY_DATA");
                    }
                }
            }
        }
    }

    public void translateHotelCategoryData(Map<String, HotelCategoryDataWeb> hotelCategoryDataWebMap) {
        if (MapUtils.isNotEmpty(hotelCategoryDataWebMap)){
            for (Map.Entry<String, HotelCategoryDataWeb> entry : hotelCategoryDataWebMap.entrySet()){
                HotelCategoryDataWeb hotelCategoryDataWeb = entry.getValue();
                hotelCategoryDataWeb.setTitle(polyglotService.getTranslatedData(hotelCategoryDataWeb.getTitle()));
            }
        }
    }

    public void translateValueStaysTooltip(ValueStaysTooltip tooltip) {
        if (tooltip == null) {
            return;
        }
        tooltip.setTitleText(polyglotService.getTranslatedData(tooltip.getTitleText()));
        tooltip.setSubText(polyglotService.getTranslatedData(tooltip.getSubText()));
        tooltip.setFooterText(polyglotService.getTranslatedData(tooltip.getFooterText()));
        if (CollectionUtils.isNotEmpty(tooltip.getData())) {
            tooltip.getData().forEach(t -> t.setTitleText(polyglotService.getTranslatedData(t.getTitleText())));
        }
    }

    public void translateMyBizAssuredTooltip(MyBizAssuredToolTip myBizAssuredToolTip) {
        if (myBizAssuredToolTip == null) {
            return;
        }
        myBizAssuredToolTip.setTitleText(polyglotService.getTranslatedData(myBizAssuredToolTip.getTitleText()));
        myBizAssuredToolTip.setSubText(polyglotService.getTranslatedData(myBizAssuredToolTip.getSubText()));
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(myBizAssuredToolTip.getData())) {
            myBizAssuredToolTip.getData().forEach(t -> t.setTitleText(polyglotService.getTranslatedData(t.getTitleText())));
        }
    }

    public void translateLuxeToolTip(LuxeToolTip toolTip)
    {
        if(toolTip == null) {
            return;
        }
        toolTip.setTitleText(polyglotService.getTranslatedData(toolTip.getTitleText()));
        toolTip.setSubText(polyglotService.getTranslatedData(toolTip.getSubText()));
        if (CollectionUtils.isNotEmpty(toolTip.getData())) {
            toolTip.getData().forEach(t -> t.setTitleText(polyglotService.getTranslatedData(t.getTitleText())));
        }
    }


    public MySafetyTooltip translateMySafetyToolTip(MySafetyTooltip toolTip, String lang)
    {
        if(toolTip == null) {
            return null;
        }
        MySafetyTooltip mySafetyTooltipTranslated = new MySafetyTooltip();
        mySafetyTooltipTranslated.setTitle(polyglotService.getTranslatedDataInLang(toolTip.getTitle(),lang));
        if (ArrayUtils.isNotEmpty(toolTip.getData())) {
            List<String> translatedString = Arrays.stream(toolTip.getData()).map(str -> polyglotService.getTranslatedDataInLang(str,lang)).collect(Collectors.toList());
            String[] strings = translatedString.toArray(new String[toolTip.getData().length]);
            mySafetyTooltipTranslated.setData(strings);
        }
        mySafetyTooltipTranslated.setIconUrl(toolTip.getIconUrl());
        return mySafetyTooltipTranslated;
    }

    public void translateMobgenJsonBO(Map<String, Map<String, MobgenJsonBO>> mobgenJsonBOMap, String lang) {

        if (MapUtils.isNotEmpty(mobgenJsonBOMap)) {

            for (Map.Entry<String, Map<String, MobgenJsonBO>> entry1 :
                    mobgenJsonBOMap.entrySet()) {
                for (Map.Entry<String, MobgenJsonBO> entry2 : entry1.getValue().entrySet()) {
                    MobgenJsonBO mobgenJsonBO = entry2.getValue();
                    mobgenJsonBO.setTitle(polyglotService.getTranslatedDataForLang(mobgenJsonBO.getTitle(), lang));
                    mobgenJsonBO.setNegativeBtnText(polyglotService.getTranslatedDataForLang(mobgenJsonBO.getNegativeBtnText(), lang));
                    mobgenJsonBO.setPositiveBtnText(polyglotService.getTranslatedDataForLang(mobgenJsonBO.getPositiveBtnText(), lang));
                    mobgenJsonBO.setMsgToOverride(polyglotService.getTranslatedDataForLang(mobgenJsonBO.getMsgToOverride(), lang));
                    mobgenJsonBO.setQuickBook(polyglotService.getTranslatedDataForLang(mobgenJsonBO.getQuickBook(), lang));
                    mobgenJsonBO.setTariffLevel(polyglotService.getTranslatedDataForLang(mobgenJsonBO.getTariffLevel(), lang));
                }

            }
        }

    }


    public void translateMobgenStringsBO(Map<String, MobgenStringsBO> mobgenStringsBOMap, String lang) {

        if(MapUtils.isNotEmpty(mobgenStringsBOMap)) {

            for(Map.Entry<String, MobgenStringsBO> entry : mobgenStringsBOMap.entrySet()) {

                MobgenStringsBO mobgenStringsBO = entry.getValue();
                mobgenStringsBO.setCardMessageFreeFailedBooking(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getCardMessageFreeFailedBooking(), lang));
                mobgenStringsBO.setCardMessageFreeNonRefundablePendingBooking(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getCardMessageFreeNonRefundablePendingBooking(), lang));
                mobgenStringsBO.setCardMessageFreePendingBooking(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getCardMessageFreePendingBooking(), lang));
                mobgenStringsBO.setCardMessageFreeRefundablePendingBooking(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getCardMessageFreeRefundablePendingBooking(), lang));
                mobgenStringsBO.setCardMessagePaidFailedBooking(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getCardMessagePaidFailedBooking(), lang));
                mobgenStringsBO.setCardMessagePaidNonRefundablePendingBooking(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getCardMessagePaidNonRefundablePendingBooking(), lang));
                mobgenStringsBO.setCardMessagePaidPendingBooking(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getCardMessagePaidPendingBooking(), lang));
                mobgenStringsBO.setCardMessagePaidRefundablePendingBooking(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getCardMessagePaidRefundablePendingBooking(), lang));
                mobgenStringsBO.setCardMessageTrackFailedBookingRefund(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getCardMessageTrackFailedBookingRefund(), lang));
                mobgenStringsBO.setDonationText(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getDonationText(), lang));
                mobgenStringsBO.setHeaderDescFreeFailedBooking(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getHeaderDescFreeFailedBooking(), lang));
                mobgenStringsBO.setHeaderDescPaidFailedBooking(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getHeaderDescPaidFailedBooking(), lang));
                mobgenStringsBO.setHeaderDescPendingBooking(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getHeaderDescPendingBooking(), lang));
                mobgenStringsBO.setHeaderDescSuccessBooking(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getHeaderDescSuccessBooking(), lang));
                mobgenStringsBO.setHeaderTitleFailedBooking(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getHeaderTitleFailedBooking(), lang));
                mobgenStringsBO.setHeaderTitleFreePendingBooking(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getHeaderTitleFreePendingBooking(), lang));
                mobgenStringsBO.setHeaderTitlePaidPendingBooking(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getHeaderTitlePaidPendingBooking(), lang));
                mobgenStringsBO.setHeaderTitleSuccessBooking(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getHeaderTitleSuccessBooking(), lang));
                mobgenStringsBO.setMessagePropertyRulesCard(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getMessagePropertyRulesCard(), lang));
                mobgenStringsBO.setMessagePropertyRulesCardMyBizz(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getMessagePropertyRulesCardMyBizz(), lang));
                mobgenStringsBO.setMessageRoomBreakUpCard(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getMessageRoomBreakUpCard(), lang));
                mobgenStringsBO.setMessageRoomBreakUpCardMyBizz(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getMessageRoomBreakUpCardMyBizz(), lang));
                mobgenStringsBO.setMessageSuccessBookingAmountBreak(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getMessageSuccessBookingAmountBreak(), lang));
                mobgenStringsBO.setMessageSuccessBookingAmountBreakMyBizz(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getMessageSuccessBookingAmountBreakMyBizz(), lang));
                mobgenStringsBO.setGst(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getGst(), lang));
                mobgenStringsBO.setPrice(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getPrice(), lang));
                mobgenStringsBO.setRating(polyglotService.getTranslatedDataForLang(mobgenStringsBO.getRating(), lang));
            }
        }
    }

    public void translateHotelCategoryDataMap(Map<String, HotelCategoryData> hotelCategoryDataMapNode) {
        for(Map.Entry<String, HotelCategoryData> entry : hotelCategoryDataMapNode.entrySet()) {
            HotelCategoryData hotelCategoryData = entry.getValue();
            hotelCategoryData.setDisplayText(polyglotService.getTranslatedData(hotelCategoryData.getDisplayText()));
            hotelCategoryData.setTitle(polyglotService.getTranslatedData(hotelCategoryData.getTitle()));

        }
    }

    public void translateHotelCategoryDataWebMapNew(HotelCategoryDataWeb hotelCategoryDataWeb) {
        hotelCategoryDataWeb.setTitle(polyglotService.getTranslatedData(hotelCategoryDataWeb.getTitle()));
    }

    /**
     * Function to replace nodes from Polyglot. For Specific nodes which are nor present at base level.
     *This problems comes due to non-flat hierarchy of nodes
     */
    public void translateConfigNodesFromPolyglot(JsonNode node, String lang) {
        if(node!=null && node.get(Constants.GROUP_BOOKING_TEXT)!=null && node.get(Constants.GROUP_BOOKING_TEXT).get(ConstantsTranslation.LANDING_GB_MSG)!=null &&
                StringUtils.isNotEmpty(node.get(Constants.GROUP_BOOKING_TEXT).get(ConstantsTranslation.LANDING_GB_MSG).textValue())) {
            ((ObjectNode)node.get(Constants.GROUP_BOOKING_TEXT)).put(ConstantsTranslation.LANDING_GB_MSG, polyglotService.getTranslatedDataForLang(ConstantsTranslation.LANDING_GB_MSG, lang));
        }

        //Fetching data from poly to show pop up on client side for Book now flow MyPartner
        //Fetching every translation/polyglot key from PMS itself
        String polyglotKey;
        if(node!=null && node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG)!=null) {
            polyglotKey = node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG).get(Constants.BOOK_NOW_MODAL_HEADING).textValue();
            ((ObjectNode)node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG)).put(Constants.BOOK_NOW_MODAL_HEADING,polyglotService.getTranslatedDataForLang(polyglotKey, lang));

            polyglotKey = node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG).get(Constants.BOOK_NOW_MODAL_SUB_HEADING).textValue();
            ((ObjectNode)node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG)).put(Constants.BOOK_NOW_MODAL_SUB_HEADING,polyglotService.getTranslatedDataForLang(polyglotKey, lang));

            polyglotKey = node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG).get(Constants.BOOK_NOW_MODAL_DESCRIPTION).textValue();
            ((ObjectNode)node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG)).put(Constants.BOOK_NOW_MODAL_DESCRIPTION,polyglotService.getTranslatedDataForLang(polyglotKey, lang));

            polyglotKey = node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG).get(Constants.BOOK_NOW_MODAL_OKAY_GOT_IT).textValue();
            ((ObjectNode)node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG)).put(Constants.BOOK_NOW_MODAL_OKAY_GOT_IT,polyglotService.getTranslatedDataForLang(polyglotKey, lang));

            Iterator<JsonNode> benefitNodes = node.get(Constants.BOOK_NOW_MODAL_DATA_CONFIG).get(Constants.BOOK_NOW_MODAL_BENEFITS).elements();
            List<JsonNode> updatedNodes = new ArrayList<>();
            while(benefitNodes.hasNext()) {
                JsonNode benefitNode = benefitNodes.next();
                polyglotKey = benefitNode.get(Constants.BOOK_NOW_TEXT_KEY).textValue();
                ((ObjectNode)benefitNode).put(Constants.BOOK_NOW_TEXT_KEY,polyglotService.getTranslatedDataForLang(polyglotKey, lang));
                updatedNodes.add(benefitNode);
            }
        }
    }
}
