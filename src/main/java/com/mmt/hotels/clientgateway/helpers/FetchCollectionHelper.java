package com.mmt.hotels.clientgateway.helpers;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.FilterDetail;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.request.FetchCollectionRequest;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import org.apache.commons.collections.MapUtils;
import org.codehaus.plexus.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class FetchCollectionHelper {

    @Autowired
    PolyglotService polyglotService;

    public boolean shouldAddFilters(String filters, FetchCollectionRequest fetchCollectionRequest, Map<String, FilterDetail> landingFilterConditions) {
        if (fetchCollectionRequest != null && fetchCollectionRequest.getRequestDetails() != null) {
            FilterDetail filterDetail = landingFilterConditions.get(filters);
            return isFilterCondition(filterDetail, fetchCollectionRequest.getRequestDetails());
        }
        return false;
    }

    private boolean isFilterCondition(FilterDetail filterDetail, RequestDetails requestDetails) {
        return filterDetail.getFunnelSource().contains(requestDetails.getFunnelSource()) &&
                filterDetail.getPageContext().contains(requestDetails.getPageContext());
    }

    //[HTL-42801] Added title on bottom-up sheet on listing field based upon purpose selected by user on landing page.
    public String getTitleForBottomFilterSheet(FetchCollectionRequest fetchCollectionRequest, Map<String, String> purposeStayFilterTitleMap) {
        String tripType = fetchCollectionRequest.getSearchCriteria().getTripType();
        if (StringUtils.isNotEmpty(tripType) && MapUtils.isNotEmpty(purposeStayFilterTitleMap)
                && purposeStayFilterTitleMap.containsKey(tripType) && StringUtils.isNotEmpty(purposeStayFilterTitleMap.get(tripType))) {
            String purposeFilterTitle = polyglotService.getTranslatedData(purposeStayFilterTitleMap.get(tripType));
            if (StringUtils.isNotEmpty(purposeFilterTitle) && !Constants.NULL_STRING.equalsIgnoreCase(purposeFilterTitle)) {
                return purposeFilterTitle;
            }
        }
        return null;
    }
}
