package com.mmt.hotels.clientgateway.helpers;

import com.gommt.hotels.orchestrator.enums.Country;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.response.MarkUpConfig;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.RequestIdentifier;
import com.mmt.hotels.model.request.TrafficSource;
import com.mmt.hotels.model.response.mypartner.MarkUp;
import com.mmt.hotels.model.response.mypartner.MarkUpDetails;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

import static com.mmt.hotels.model.response.mypartner.MarkUpType.PERCENTAGE;

@Component
public class PricingEngineHelper {
    private static final Logger logger = LoggerFactory.getLogger(PricingEngineHelper.class);

    @Autowired
    Utility utility;

    private String appendKeyAndValue(String relativeURL, String key, String value) {
        if (StringUtils.isNotBlank(relativeURL)) {
            if (StringUtils.contains(relativeURL, '?')) {
                relativeURL = relativeURL + Constants.AMP;
            } else {
                relativeURL = relativeURL + Constants.QUESTION;
            }
            relativeURL = relativeURL + key.toLowerCase() + Constants.EQUI + value.toUpperCase();
        }
        return relativeURL;
    }

    /**
     * Helper function to add PEE in the api url
     * @param relativeURL
     * @param experimentData
     */
    public String appendPEEInUrl(String relativeURL, String experimentData) {
        Map<String, String> experimentDataMap = utility.getExpDataMap(experimentData);
        String value = "F";
        if (MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey(Constants.PEE)) {
            value = experimentDataMap.get(Constants.PEE);
        }
        return appendKeyAndValue(relativeURL, Constants.PEE, value);
    }

    /**
     * Helper function to add PEED in the api url
     * @param relativeURL
     * @param experimentData
     */
    public String appendPEEDInUrl(String relativeURL, String experimentData) {
        Map<String, String> experimentDataMap = utility.getExpDataMap(experimentData);
        String value = "F";
        if (MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey(Constants.PEED)) {
            value = experimentDataMap.get(Constants.PEED);
        }
        return appendKeyAndValue(relativeURL, Constants.PEED, value);
    }

    public String appendFilterServiceExpInUrl(String relativeURL, String experimentData) {
        Map<String, String> experimentDataMap = utility.getExpDataMap(experimentData);
        String value = "", expValueIH = "";
        if (MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey(Constants.newFilterService)) {
            value = experimentDataMap.get(Constants.newFilterService);
        }
        if (MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey(Constants.newFilterServiceIH)) {
            expValueIH = experimentDataMap.get(Constants.newFilterServiceIH);
        }
        expValueIH = "true";
        relativeURL = appendKeyAndValue(relativeURL, Constants.newFilterService, value);
        return appendKeyAndValue(relativeURL, Constants.newFilterServiceIH, expValueIH);
    }

    public String appendPricerV2InUrl(String relativeUrl, String experimentData) {
        Map<String, String> experimentDataMap = utility.getExpDataMap(experimentData);
        String value = "false";
        if (MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey(Constants.PRICER_V2)) {
            value = experimentDataMap.get(Constants.PRICER_V2);
        }
        return appendKeyAndValue(relativeUrl, Constants.PRICER_V2, value);
    }

    public String appendFunnelTraffic(String relativeUrl, String funnelSource, TrafficSource trafficSource, RequestIdentifier requestIdentifier) {
        String traffic = trafficSource != null && StringUtils.isNotEmpty(trafficSource.getType()) ? trafficSource.getType() : "B2C";
        relativeUrl = appendKeyAndValue(relativeUrl, Constants.FUNNEL_SOURCE, StringUtils.isNotEmpty(funnelSource) ? funnelSource : "");
        relativeUrl = appendKeyAndValue(relativeUrl, Constants.TRAFFIC_SOURCE, traffic);
        if(null == requestIdentifier) {
            logger.warn("appendFunnelTraffic : requestIdentifier null");
        }
        relativeUrl = appendKeyAndValue(relativeUrl, Constants.JOURNEY_ID, (requestIdentifier!=null && StringUtils.isNotEmpty(requestIdentifier.getJourneyId())) ? requestIdentifier.getJourneyId() : "");
        return relativeUrl;
    }

    public String appendCountryInUrl(String relativeUrl, String countryCode) {
        String value = StringUtils.isNotEmpty(countryCode) && !Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode) ? Country.IH.getName() : Country.DH.getName();
        return appendKeyAndValue(relativeUrl, Constants.COUNTRY, value);
    }

    public double getMarkUpForHotels(final MarkUpDetails markUpDetails, final Double finalPriceWithoutTax){
        final boolean isDomestic= Constants.DOMESTIC.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()));
        if(Objects.nonNull(finalPriceWithoutTax) && finalPriceWithoutTax >0 && markUpDetails!=null && markUpDetails.isMarkupEligible()&& markUpDetails.getMarkupMap()!=null){
            final MarkUp markUp=isDomestic?markUpDetails.getMarkupMap().get("DH"):markUpDetails.getMarkupMap().get("IH");
            if(Objects.nonNull(markUp))
                return PERCENTAGE.equals(markUp.getType())?(markUp.getValue()*finalPriceWithoutTax*.01):markUp.getValue();
        }
        return 0;
    }

    public MarkUpConfig buildMarkUpConfig(final MarkUpDetails markUpDetails, final MarkUpConfig markUpConfig) {
        if (Objects.nonNull(markUpDetails) && Objects.nonNull(markUpConfig) && markUpDetails.isMarkupEligible()) {
            final MarkUpConfig finalMarkUp = new MarkUpConfig();
            finalMarkUp.setText(markUpConfig.getText());
            finalMarkUp.setFlagValue(markUpConfig.isFlagValue());
            finalMarkUp.setHoverText(markUpConfig.getHoverText());
            if (markUpDetails.isEditable()) {
                finalMarkUp.setCtaUrl(markUpConfig.getCtaUrl());
                finalMarkUp.setCtaText(markUpConfig.getCtaText());
            }
            return finalMarkUp;
        }
        return null;
    }
}
