package com.mmt.hotels.clientgateway.helpers;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.UUIDException;
import com.mmt.hotels.clientgateway.response.cbresponse.ErrorResponse;
import com.mmt.hotels.clientgateway.response.moblanding.GenericErrorEntity;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.ValidationErrors;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.pojo.response.userservice.UserDetailsDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class CorporateHelper {


    @Autowired
    CommonHelper commonHelper;

    @Autowired
    MetricAspect metricAspect;

    private static final Logger logger = LoggerFactory.getLogger(CorporateHelper.class);


    public com.mmt.hotels.clientgateway.response.cbresponse.ErrorResponse generateInvalidApprovalErrorRsp() {
        com.mmt.hotels.clientgateway.response.cbresponse.ErrorResponse responseErrors = new ErrorResponse();
        com.mmt.hotels.clientgateway.response.moblanding.GenericErrorEntity genericErrorEntity = new GenericErrorEntity();
        genericErrorEntity.setErrorCode(ValidationErrors.AUTHCODE_NULL_IN_APPROVAL.getErrorCode());
        genericErrorEntity.setErrorMessage(ValidationErrors.AUTHCODE_NULL_IN_APPROVAL.getErrorMsg());
        responseErrors.setErrorList(Stream.of(genericErrorEntity).collect(Collectors.toList()));
        return responseErrors;
    }


    public UserDetailsDTO getCorpUserIdForMyBizUser(String bookingDevice, Map<String, String> httpHeaderMap,
                                                    String correlationKey, String emailId, String siteDomain) throws ClientGatewayException, UUIDException {
        UserDetailsDTO userDetailsDTO = null;
        String mmtAuth = commonHelper.getAuthToken(httpHeaderMap);
        if (StringUtils.isNotBlank(emailId) || !StringUtils.isBlank(mmtAuth)) {

            UserServiceResponse userServiceResponse = null;

            userServiceResponse = commonHelper.getUserDetails(mmtAuth, emailId, null ,null, correlationKey, Constants.CORP_ID_CONTEXT,siteDomain, null, httpHeaderMap);

            ExtendedUser extendedUser = null;

            if (userServiceResponse != null && userServiceResponse.getResult() != null)
                extendedUser =  userServiceResponse.getResult().getExtendedUser();

            if (extendedUser == null || !Constants.PROFILE_CORPORATE.equalsIgnoreCase(extendedUser.getProfileType())
                    || StringUtils.isBlank(extendedUser.getProfileId())) {
                if (StringUtils.isEmpty(emailId)) {
                    logger.error("Error while fetching corp user Id");
                    throw new UUIDException("Unauthorized or non biz user");
                }
            }

            try {
                userDetailsDTO = parseCorporateUserServiceResponse(extendedUser);
                userDetailsDTO.setMmtAuth(mmtAuth);
            }catch(Exception ex) {
                logger.error("Error while parsing user detail DTO", ex);
                throw ex;
            }
        }
        return userDetailsDTO;
    }


    public UserDetailsDTO parseCorporateUserServiceResponse(ExtendedUser extendedUser) {
            UserDetailsDTO userDetailsDTO = new UserDetailsDTO();
            parseCorporateUser(extendedUser, userDetailsDTO);
            return userDetailsDTO;
    }

     private void parseCorporateUser(ExtendedUser extendedUser, UserDetailsDTO userDetailsDTO) {
            if (extendedUser != null && StringUtils.isNotBlank(extendedUser.getProfileId()))
                userDetailsDTO.setProfileId(extendedUser.getProfileId());
            if (extendedUser != null && StringUtils.isNotBlank(extendedUser.getUuid()))
                userDetailsDTO.setUuid(extendedUser.getUuid());
            if(extendedUser != null && StringUtils.isNotBlank(extendedUser.getProfileType()))
                userDetailsDTO.setProfileType(extendedUser.getProfileType());
            if(extendedUser!= null && StringUtils.isNotBlank(extendedUser.getAffiliateId()))
                userDetailsDTO.setSubProfileType(extendedUser.getAffiliateId());
     }


    public ErrorResponse getErrorResponseFromCBError(CBError error) {
        ErrorResponse errorResponse = new ErrorResponse();
        errorResponse.setErrorList(Arrays.asList(setGenricErrorEntity(error)));
        return errorResponse;
    }


    public  com.mmt.hotels.clientgateway.response.moblanding.GenericErrorEntity setGenricErrorEntity(CBError erroEnum) {
        com.mmt.hotels.clientgateway.response.moblanding.GenericErrorEntity genericErrorEntity = null;

        try {

            genericErrorEntity = new com.mmt.hotels.clientgateway.response.moblanding.GenericErrorEntity();
            genericErrorEntity.setErrorCode(erroEnum.getCode());
            genericErrorEntity.setErrorMessage(erroEnum.getDescription());
            metricAspect.addToCounter(DependencyLayer.CLIENTGATEWAY, ErrorType.UNEXPECTED, erroEnum.getCode());

        } catch (Exception e) {
            logger.error("Error by setGenricErrorEntity error ", e);
        }
        return genericErrorEntity;

    }




}
