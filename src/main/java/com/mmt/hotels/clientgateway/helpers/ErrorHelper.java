package com.mmt.hotels.clientgateway.helpers;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.ErrorCodesCorp;
import com.mmt.hotels.clientgateway.enums.SpecifiedErrorsApprover;
import com.mmt.hotels.clientgateway.enums.SpecifiedErrorsRequestor;
import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.request.payment.ApprovalAction;
import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.mmt.hotels.clientgateway.constants.Constants.USER_ROLE_CONSTANT;
import static com.mmt.hotels.clientgateway.constants.Constants.CORP_APPROVER_PENDING_SUBTITLE_KEY_EXPIRED;
import static com.mmt.hotels.clientgateway.constants.Constants.CORP_APPROVER_PENDING_TITLE_KEY_EXPIRED;
import static com.mmt.hotels.clientgateway.constants.Constants.CORP_REQUESTER_PENDING_SUBTITLE_KEY_EXPIRED;
import static com.mmt.hotels.clientgateway.constants.Constants.CORP_REQUESTER_PENDING_TITLE_KEY_EXPIRED;
import static com.mmt.hotels.clientgateway.constants.Constants.CORP_REQUESTER_REJECTED_SUBTITLE_KEY_EXPIRED;
import static com.mmt.hotels.clientgateway.constants.Constants.CORP_REQUESTER_REJECTED_TITLE_KEY_EXPIRED;
import static com.mmt.hotels.clientgateway.constants.Constants.APPROVER;
@Component
public class ErrorHelper {

    @Autowired
    protected PolyglotService polyglotService;

    public String getSubtitleForError(String subtitle){
        // translation Constants is received in subtitle
        String translatedSubtitle = polyglotService.getTranslatedData(subtitle);
        return StringUtils.isNotEmpty(translatedSubtitle) && !Constants.NULL_STRING.equalsIgnoreCase(translatedSubtitle)?translatedSubtitle:polyglotService.getTranslatedData(ConstantsTranslation.GENERIC_ERROR_SUBTITLE);
    }

    public String getTitleForError(String title){
        // translation Constants is received in title
        String translatedTitle = polyglotService.getTranslatedData(title);
        return StringUtils.isNotEmpty(translatedTitle) && !Constants.NULL_STRING.equalsIgnoreCase(translatedTitle)?translatedTitle:polyglotService.getTranslatedData(ConstantsTranslation.GENERIC_ERROR_TITLE);
    }

    public boolean sendErrorWithTitle(String errorCode, String controller){
        return Constants.CORP_ID_CONTEXT.equalsIgnoreCase(controller) && checkIferrorCodeMatches(errorCode);
    }

    private boolean checkIferrorCodeMatches(String errorcode){
        //add errorcode here to expand the functionality for other errors
        return ErrorCodesCorp.resolve(errorcode);
    }

    public Error getErrorWithTitle(String controller, String errorCode, Error error, JSONObject metaData){
        //this function creates the new error for approver/requestor else we return the usual error
        if(Constants.GET_APPROVAL_ENDPOINT.equalsIgnoreCase(controller)){
            String userRole = (String)metaData.get(USER_ROLE_CONSTANT);
            String approvalStatus = (String)metaData.get(Constants.APPROVAL_STATUS);
            if(APPROVER.equalsIgnoreCase(userRole)){
                SpecifiedErrorsApprover specificError = SpecifiedErrorsApprover.resolve(errorCode);
                if(ApprovalAction.PENDING.getValue().equalsIgnoreCase(approvalStatus)) {
                    return specificError != null ? new Error(error.getCode(), getSubtitleForError(CORP_APPROVER_PENDING_SUBTITLE_KEY_EXPIRED), null, getSubtitleForError(CORP_APPROVER_PENDING_TITLE_KEY_EXPIRED)) : error;
                }
                return specificError!=null ? new Error(error.getCode(), getSubtitleForError(specificError.getSubTitle()), null, getSubtitleForError(specificError.getTitle())): error;
            }else {
                SpecifiedErrorsRequestor specificError = SpecifiedErrorsRequestor.resolve(errorCode);
                if(ApprovalAction.PENDING.getValue().equalsIgnoreCase(approvalStatus)) {
                    return specificError != null ? new Error(error.getCode(), getSubtitleForError(CORP_REQUESTER_PENDING_SUBTITLE_KEY_EXPIRED), null, getSubtitleForError(CORP_REQUESTER_PENDING_TITLE_KEY_EXPIRED)) : error;
                }
                else if(ApprovalAction.REJECTED.getValue().equalsIgnoreCase(approvalStatus)) {
                    return specificError != null ? new Error(error.getCode(), getSubtitleForError(CORP_REQUESTER_REJECTED_SUBTITLE_KEY_EXPIRED), null, getSubtitleForError(CORP_REQUESTER_REJECTED_TITLE_KEY_EXPIRED)) : error;
                }
                return specificError!=null ? new Error(error.getCode(), getSubtitleForError(specificError.getSubTitle()), null, getSubtitleForError(specificError.getTitle())): error;
            }
        }else{
            return error;
        }
    }
}
