package com.mmt.hotels.clientgateway.helpers;

import com.mmt.hotels.clientgateway.thirdparty.response.MyPartnerUserDetailsResponse;
import org.springframework.stereotype.Component;

@Component
public class GstDetailsHelper {

    public boolean isGstDetailsResponseValid(MyPartnerUserDetailsResponse myPartnerUserDetailsResponse) {
        return null != myPartnerUserDetailsResponse &&
                null != myPartnerUserDetailsResponse.getData() &&
                null != myPartnerUserDetailsResponse.getData().getTravellerGstInfo() &&
                null != myPartnerUserDetailsResponse.getData().getTravellerGstInfo().getGstDetails();
    }
}
