package com.mmt.hotels.clientgateway.helpers;

import com.mmt.hotels.clientgateway.response.BookNowDetails;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.response.MpFareHoldStatus;
import com.mmt.hotels.model.response.pricing.HotelRates;
import com.mmt.hotels.model.response.pricing.RoomType;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

import static com.mmt.hotels.clientgateway.constants.Constants.EMPTY_STRING;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.NON_BNPL_COUPON_APPLIED_SUBTEXT;
import static java.lang.Math.min;

//Helper to provide BNPL Pay mode information to client
@Component
public class FareHoldHelper {
    @Autowired
    protected PolyglotService polyglotService;
    private static final Logger LOGGER = LoggerFactory.getLogger(FareHoldHelper.class);

    // Extracts FareHold status from hotelRates to build Pay Mode information for Review Page
    public MpFareHoldStatus getMpFareHoldStatus(final HotelRates hotelRates) {
        if (hotelRates != null && hotelRates.getRoomTypeDetails() != null && MapUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getRoomType())) {
            return getMpFareHoldStatus(hotelRates.getRoomTypeDetails().getRoomType().values());
        }
        return null;
    }

    // Extracts FareHold status from roomTypes to build Pay Mode information for Validate request
    public MpFareHoldStatus getMpFareHoldStatus(final Collection<RoomType> roomTypes) {
        MpFareHoldStatus mpFareHoldStatus = new MpFareHoldStatus();
        mpFareHoldStatus.setHoldWarningText(EMPTY_STRING);
        for (RoomType roomtype : roomTypes) {
            if (MapUtils.isNotEmpty(roomtype.getRatePlanList())) {
                for (com.mmt.hotels.model.response.pricing.RatePlan ratePlan : roomtype.getRatePlanList().values()) {
                    if (ratePlan.getMpFareHoldStatus() != null && ratePlan.getMpFareHoldStatus().getExpiry() != null && ratePlan.getMpFareHoldStatus().isHoldEligible()) {
                        if (mpFareHoldStatus.getExpiry() == null) {
                            //If any ratePlan has null value for Expiry we should return null as BookNow@0/1 flow is not eligible
                            mpFareHoldStatus.setExpiry(ratePlan.getMpFareHoldStatus().getExpiry());
                            mpFareHoldStatus.setBookingAmount(ratePlan.getMpFareHoldStatus().getBookingAmount());
                            mpFareHoldStatus.setHoldWarningText(ratePlan.getMpFareHoldStatus().getHoldWarningText());
                            mpFareHoldStatus.setHoldEligible(ratePlan.getMpFareHoldStatus().isHoldEligible());
                            mpFareHoldStatus.setExpiryTime(ratePlan.getMpFareHoldStatus().getExpiryTime());
                        } else {
                            LOGGER.debug("More than one expiry found checking minimum for MPFareHoldStatus : {}", ratePlan.getMpFareHoldStatus());
                            //Get minValue(restrictive Value) (minDate/ minEpoch) to return
                            if (ratePlan.getMpFareHoldStatus().getExpiry() < mpFareHoldStatus.getExpiry()) {
                                mpFareHoldStatus.setExpiry(ratePlan.getMpFareHoldStatus().getExpiry());
                                mpFareHoldStatus.setExpiryTime(ratePlan.getMpFareHoldStatus().getExpiryTime());
                            }
                        }
                    } else {
                        LOGGER.warn("One of the rateplan is not eligible for BNPL for MPFareHoldStatus : {}", ratePlan.getMpFareHoldStatus());
                        //If one of rate-plan is non-refundable return null as BookNow@0/1 flow is not eligible
                        return null;
                    }
                }
            }
        }
        LOGGER.debug("MPFareHoldStatus : {}", mpFareHoldStatus);
        return mpFareHoldStatus;
    }

    // Provided node to build BNPL Pay mode on review page
    public BookNowDetails getBookNowDetails(final MpFareHoldStatus mpFareHoldStatus, final boolean bnplDisabled, final String bnplDisabledCoupon) {

        BookNowDetails details = new BookNowDetails();
        details.setBookNowAmount((int) mpFareHoldStatus.getBookingAmount());
        details.setExpiry(mpFareHoldStatus.getExpiry());
        details.setExpiryTime(mpFareHoldStatus.getExpiryTime());
        details.setWarningText(mpFareHoldStatus.getHoldWarningText());
        // Disable BNPL Pay-Mode in case Manthan coupon restricts BNPL
        if (bnplDisabled) {
            details.setDisabled(bnplDisabled);
            details.setSubText(polyglotService.getTranslatedData(NON_BNPL_COUPON_APPLIED_SUBTEXT).replace("{coupon_code}", String.valueOf(bnplDisabledCoupon)));
        }
        LOGGER.debug("BookNowDetails : {} using {} coupon", details, bnplDisabledCoupon);
        return details;
    }

}
