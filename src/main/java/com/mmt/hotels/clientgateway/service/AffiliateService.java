package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.PaymentHelper;
import com.mmt.hotels.clientgateway.request.CreateQuoteRequest;
import com.mmt.hotels.clientgateway.request.GetQuoteRequest;
import com.mmt.hotels.clientgateway.request.UpdateAffiliateFeeRequest;
import com.mmt.hotels.clientgateway.response.AvailRoomsResponse;
import com.mmt.hotels.clientgateway.response.ValidateCouponResponseBody;
import com.mmt.hotels.clientgateway.response.affiliate.UpdatedAffiliateFeeResponse;
import com.mmt.hotels.clientgateway.response.rooms.RatePlan;
import com.mmt.hotels.clientgateway.restexecutors.AffiliateExecutor;
import com.mmt.hotels.clientgateway.restexecutors.UserServiceExecutor;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.transformer.factory.AffiliateFactory;
import com.mmt.hotels.clientgateway.transformer.factory.AvailRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.factory.DiscountServiceFactory;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.affiliate.CreateQuoteResponse;
import com.mmt.hotels.model.affiliate.GetQuoteResponse;
import com.mmt.hotels.model.affiliate.UpdateAffiliateFeeResponse;
import com.mmt.hotels.model.request.CreateQuoteRequestBody;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.UpdateAffiliateFeeReqBody;
import com.mmt.hotels.model.request.payment.*;
import com.mmt.hotels.model.response.GetQuoteResponseBody;
import com.mmt.hotels.model.response.discount.ValidateCouponResponse;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.pojo.response.userservice.UserDetailsDTO;
import com.mmt.model.HotelsRoomInfoResponseEntity;
import com.mmt.scrambler.ScramblerClient;
import com.mmt.scrambler.exception.ScramblerClientException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

@Component
public class AffiliateService {

    @Autowired
    private AffiliateExecutor affiliateExecutor;

    @Autowired
    private AffiliateFactory affiliateFactory;

    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
    private CommonHelper commonHelper;

    @Autowired
    private PaymentHelper payHelper;

    @Autowired
    private AvailRoomsFactory availRoomsFactory;

    @Autowired
    private DiscountServiceFactory discountServiceFactory;

    @Autowired
    private CommonResponseTransformer commonResponseTransformer;
    
    @Autowired
    private UserServiceExecutor userServiceExecutor;

    private static final Logger logger = LoggerFactory.getLogger(AffiliateService.class);

    public UpdatedAffiliateFeeResponse getUpdateAffiliateFeeResponse(UpdateAffiliateFeeRequest updateAffiliateFeeRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {

            UpdateAffiliateFeeResponse updateAffiliateFeeResponse = affiliateExecutor.getUpdatedAffiliateFeeResponse(updateAffiliateFeeRequest, parameterMap, httpHeaderMap);
            return affiliateFactory.getResponseService(updateAffiliateFeeRequest.getClient()).convertAffiliateFeeUpdateResponse(updateAffiliateFeeResponse);

        } catch (Throwable e) {
            logAndThrowException(e, "error occured in fetch update-fee: ");
        }
        return null;
    }

    public com.mmt.hotels.clientgateway.response.affiliate.CreateQuoteResponse createQuote(CreateQuoteRequest createQuoteRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {
            populateUserDetails(createQuoteRequest, httpHeaderMap);
            CreateQuoteResponse createQuoteResponseHES = affiliateExecutor.getCreateQuoteResponse(createQuoteRequest, parameterMap, httpHeaderMap);
            return affiliateFactory.getResponseService(createQuoteRequest.getClient()).convertAffiliateCreateQuoteResponse(createQuoteResponseHES);
        } catch (Throwable e) {
            logAndThrowException(e, "error occured in fetch create-quote: ");
        }
        return null;
    }

    public AvailRoomsResponse getQuote(GetQuoteRequest getQuoteRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {
            GetQuoteResponse getQuoteResponseHES = affiliateExecutor.getPersistedQuoteDataMerged(getHESMergedQuoteDataRequest(getQuoteRequest), parameterMap, httpHeaderMap);

            RoomDetailsResponse roomDetailsResponse = getQuoteResponseHES.getAvailResponse();
            ValidateCouponResponse validateCouponResponseHES = getQuoteResponseHES.getValidateCouponResponse();
            AvailRoomsResponse availRoomsResponseCG = availRoomsFactory.getResponseService(getQuoteRequest.getClient()).convertAvailRoomsResponse(null,roomDetailsResponse, new HotelsRoomInfoResponseEntity(),
                    Constants.DEFAULT_SITE_DOMAIN, StringUtils.EMPTY, null, null, null, false, "", null, false,StringUtils.EMPTY);
            ValidateCouponResponseBody validateCouponResponseCG = null;

            if (null != validateCouponResponseHES && null == validateCouponResponseHES.getResponseErrors()) {
                validateCouponResponseCG = discountServiceFactory.getResponseService(getQuoteRequest.getClient()).convertValidateCouponResponse(validateCouponResponseHES, null, availRoomsResponseCG.getHotelInfo().getCountryCode(), false);
            }
            updateGetQuoteResponse(availRoomsResponseCG, validateCouponResponseCG, getQuoteResponseHES);
            if (CollectionUtils.isNotEmpty(getQuoteResponseHES.getRoomStayCandidates())) {
                availRoomsResponseCG.setRoomStayCandidates(getQuoteResponseHES.getRoomStayCandidates());
            }
            String currency = DEFAULT_CUR_INR;
            if (httpHeaderMap.containsKey(USER_COUNTRY)) {
                currency = httpHeaderMap.get(USER_CURRENCY);
            }
            availRoomsResponseCG.setCurrency(currency);
            return availRoomsResponseCG;
        } catch (Throwable e) {
            logAndThrowException(e, "error occured in fetch get-quote: ");
        }
        return null;
    }

    private void logAndThrowException(Throwable e, String errMsg) throws ClientGatewayException {
        logger.error(errMsg, e);

        ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
        metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
        throw exceptionHandlerResponse.getClientGatewayException();
    }

    private com.mmt.hotels.model.affiliate.GetQuoteRequest getHESMergedQuoteDataRequest(GetQuoteRequest getQuoteRequestCG) {
        com.mmt.hotels.model.affiliate.GetQuoteRequest getQuoteRequestHES = new com.mmt.hotels.model.affiliate.GetQuoteRequest();
        getQuoteRequestHES.setQuoteId(getQuoteRequestCG.getQuoteId());
        getQuoteRequestHES.setCorrelationKey(getQuoteRequestCG.getCorrelationKey());
        return getQuoteRequestHES;
    }

    private void updateGetQuoteResponse(AvailRoomsResponse availRoomsResponse, ValidateCouponResponseBody validateCouponResponse, GetQuoteResponse getQuoteResponseHES) {
        availRoomsResponse.setTravellers(getQuoteResponseHES.getTravelerDetailsList());
        if (getQuoteResponseHES.getAvailResponse() != null && null != getQuoteResponseHES.getAvailResponse().getHotelRates().get(0).getSpecialRequestAvailable()
                && getQuoteResponseHES.getSpecialRequest() != null)
            availRoomsResponse.setSelectedSpecialRequests(commonResponseTransformer.buildSelctedSpecialRequests(getQuoteResponseHES.getAvailResponse().getHotelRates().get(0).getSpecialRequestAvailable(), getQuoteResponseHES.getSpecialRequest()));
        if (null != availRoomsResponse.getTotalpricing()) {
            availRoomsResponse.getTotalpricing().setAffiliateFeeDetails(null);
        }
        availRoomsResponse.setSpecialrequests(null);
        if (availRoomsResponse.getHotelInfo() != null) {
            availRoomsResponse.getHotelInfo().setCheckinDate(getQuoteResponseHES.getCheckinDate());
            availRoomsResponse.getHotelInfo().setCheckoutDate(getQuoteResponseHES.getCheckoutDate());
            availRoomsResponse.getHotelInfo().setCountryCode(getQuoteResponseHES.getCountryCode());
            availRoomsResponse.getHotelInfo().setSearchType(getQuoteResponseHES.getSearchType());
        }

        if (null == validateCouponResponse && null != availRoomsResponse && null != availRoomsResponse.getTotalpricing()) {
            availRoomsResponse.getTotalpricing().setCoupons(null);
            return;
        }
        availRoomsResponse.setTotalpricing(validateCouponResponse.getTotalPricing());
        if (null != availRoomsResponse.getTotalpricing()) {
            availRoomsResponse.getTotalpricing().setAffiliateFeeDetails(null);
        }
        availRoomsResponse.setBnplDetails(validateCouponResponse.getBnplDetails());
        availRoomsResponse.setEmiDetails(validateCouponResponse.getEmiDetails());
        if (CollectionUtils.isNotEmpty(availRoomsResponse.getRateplanlist())) {
            for (RatePlan ratePlanAvail : availRoomsResponse.getRateplanlist())
                ratePlanAvail.setCancellationTimeline(validateCouponResponse.getCancellationTimeline());
        }
    }

    public void populateUserDetails(CreateQuoteRequest createQuoteRequest, Map<String, String> headerMap) throws ClientGatewayException, ScramblerClientException {
        BeginCheckoutReqBody paymentRequestClient = new BeginCheckoutReqBody();
        paymentRequestClient.setTravelerDetailsList(createQuoteRequest.getTravelerDetailsList());
        paymentRequestClient.setPaymentDetail(new PaymentDetail());
        paymentRequestClient.setAuthToken(commonHelper.getAuthToken(headerMap));
        UserServiceResponse userServiceResponse = payHelper.getUserServiceResponse(paymentRequestClient, headerMap);
        if (userServiceResponse != null && userServiceResponse.getResult() != null && userServiceResponse.getResult().getExtendedUser() != null) {
            payHelper.populateUUID(paymentRequestClient, userServiceResponse);
        }
        if (null != paymentRequestClient.getUserDetail())
            createQuoteRequest.setUserDetail(paymentRequestClient.getUserDetail());
    }

	public String getUpdateAffiliateFeeResponseOld(UpdateAffiliateFeeReqBody updateAffiliateFeeReqBody,
			Map<String, String[]> parameterMap, Map<String, String> headerMap, String correlationKey) throws UnsupportedEncodingException, ClientGatewayException {
		return affiliateExecutor.getUpdatedAffiliateFeeOldResponse(updateAffiliateFeeReqBody, parameterMap, headerMap, correlationKey);
	}

	public String createQuoteOld(CreateQuoteRequestBody createQuoteRequestBody, Map<String, String[]> parameterMap,
			Map<String, String> headers, String correlationKey) throws UnsupportedEncodingException, ClientGatewayException {
		UserDetailsDTO userDetailsDTO = userServiceExecutor.getUserDetails(createQuoteRequestBody.getBookingDevice(),headers,correlationKey,createQuoteRequestBody.getSiteDomain());
		populateUserDetailsOld(createQuoteRequestBody,userDetailsDTO);
		return affiliateExecutor.getCreateQuoteResponseOld(createQuoteRequestBody, parameterMap, headers, correlationKey);
	}

	private void populateUserDetailsOld(CreateQuoteRequestBody createQuoteRequestBody, UserDetailsDTO userDetailsDTO) {
		try {
			UserDetail userDetail = null;
			if (userDetailsDTO != null) {
				userDetail = new UserDetail();
				userDetail.setEmailID(userDetailsDTO.getEmail());
				userDetail.setMobileNo(userDetailsDTO.getMobile());
				userDetail.setFirstName(userDetailsDTO.getFirstname());
				userDetail.setLastName(userDetailsDTO.getLastname());
				userDetail.setUuid(userDetailsDTO.getUuid());
				userDetail.setProfileType(userDetailsDTO.getProfileType());
				userDetail.setSubProfileType(userDetailsDTO.getSubProfileType());
				userDetail.setVerified(userDetailsDTO.isVerified());
				ScramblerClient scramblerClient = ScramblerClient.getInstance();
				Utility.populateCommIdsInUserDetails(userDetail, null, scramblerClient);
			}
			createQuoteRequestBody.setUserDetail(userDetail);
			}
			catch(Exception ex){
				logger.error("Error in createQuote : Error occurred in scrambling: " , ex);
		}
		
	}

	public GetQuoteResponseBody getQuoteOld(com.mmt.hotels.model.affiliate.GetQuoteRequest getQuoteRequestBody, Map<String, String[]> parameterMap,
			Map<String, String> headers, String correlationKey) throws UnsupportedEncodingException, ClientGatewayException {
		GetQuoteResponseBody response = new GetQuoteResponseBody();
		GetQuoteResponse getQuoteResponseHES = affiliateExecutor.getPersistedQuoteDataMerged(getQuoteRequestBody, parameterMap, headers);
		if (getQuoteResponseHES != null) {
			response.setValidateCouponResponse(getQuoteResponseHES.getValidateCouponResponse());
			response.setHotelRates(getQuoteResponseHES.getAvailResponse().getHotelRates());
			response.setCorrelationKey(getQuoteResponseHES.getAvailResponse().getCorrelationKey());
			response.setTxnKey(getQuoteResponseHES.getAvailResponse().getTxnKey());
			response.setStaticDetails(getQuoteResponseHES.getStaticDetails());
			if(CollectionUtils.isNotEmpty(getQuoteResponseHES.getTravelerDetailsList())){
				response.setTravelerDetailsList(prepareTravelerDetails(getQuoteResponseHES.getTravelerDetailsList()));				
			}
			
			//TODO
			populateParametersFromAvailRequest(response, null);
		}
		return response;
	}
	
	private void populateParametersFromAvailRequest(GetQuoteResponseBody response, PriceByHotelsRequestBody priceByHotelsRequestBody) {
		if(priceByHotelsRequestBody != null){
			response.setCheckin(priceByHotelsRequestBody.getCheckin());
			response.setCheckout(priceByHotelsRequestBody.getCheckout());
			response.setCityCode(priceByHotelsRequestBody.getCityCode());
			response.setCountryCode(priceByHotelsRequestBody.getCountryCode());
			response.setCurrency(priceByHotelsRequestBody.getCurrency());
			response.setRoomCriteria(priceByHotelsRequestBody.getRoomCriteria());
			response.setRoomStayCandidates(priceByHotelsRequestBody.getRoomStayCandidates());
		}
	}

	private List<TravelerDetail> prepareTravelerDetails(List<TravelerDetail> travelerDetailsList) {
		List<TravelerDetail> travelers = new ArrayList<>();
		for(TravelerDetail travelerInfo : travelerDetailsList){
			TravelerDetail travelerDetail = new TravelerDetail();
			travelerDetail.setTitle(travelerInfo.getTitle());
			travelerDetail.setFirstName(travelerInfo.getFirstName());
			travelerDetail.setLastName(travelerInfo.getLastName());
			travelerDetail.setGender(travelerInfo.getGender() != null ? Gender.valueOf(travelerInfo.getGender().name()) : null);
			travelerDetail.setPaxType(travelerInfo.getPaxType() != null ? PaxType.valueOf(travelerInfo.getPaxType().name()) : null);
			travelerDetail.setEmailID(travelerInfo.getEmailID());
			travelerDetail.setMobileNo(travelerInfo.getMobileNo());
			travelerDetail.setIsdCode(travelerInfo.getIsdCode());
			travelerDetail.setPanCard(travelerInfo.getPanCard());
			travelerDetail.setRegisterGstinNum(travelerInfo.getRegisterGstinNum());
			travelerDetail.setGstinCompanyAddress(travelerInfo.getGstinCompanyAddress());
			travelerInfo.setGstinCompanyName(travelerInfo.getGstinCompanyName());
			
			travelers.add(travelerDetail);
		}
		return travelers;
	}
	
	
	
	

}
