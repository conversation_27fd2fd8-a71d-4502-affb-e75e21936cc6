package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.MarshallingErrors;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.request.TreelsFilterCountRequest;
import com.mmt.hotels.clientgateway.response.filter.FilterResponse;
import com.mmt.hotels.clientgateway.restexecutors.FilterExecutor;
import com.mmt.hotels.clientgateway.restexecutors.TreelsListingExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.FilterFactory;
import com.mmt.hotels.clientgateway.transformer.request.TreelsFilterTransformer;
import com.mmt.hotels.clientgateway.transformer.request.TreelsRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.TreelsFilterResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.TreelsResponseTransformer;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.filter.DPTExperimentDetails;
import com.mmt.hotels.model.request.FilterSearchMetaDataResponse;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.ControllerConstants.LISTING_FILTER_COUNT;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.TREELS_FILTER_COUNT;

@Component
public class TreelsFilterService extends ListingService{

    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
    private CommonHelper commonHelper;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    private FilterFactory filterFactory;

    @Autowired
    private FilterExecutor filterExecutor;

    @Autowired
    private TreelsListingExecutor treelsListingExecutor;

    @Autowired
    private TreelsFilterTransformer treelsFilterTransformer;

    @Autowired
    private TreelsFilterResponseTransformer treelsFilterResponseTransformer;

    private static final Logger logger = LoggerFactory.getLogger(TreelsFilterService.class);

    public FilterResponse filterCount(TreelsFilterCountRequest treelsFilterRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        FilterResponse filterResponse = null;
        try {
            long startTime = System.currentTimeMillis();
            if(treelsFilterRequest.getSearchCriteria()==null){
                logger.warn("searchCriteria is null in treels filterCount");
                commonHelper.setDefaultSearchContext(treelsFilterRequest);
            }
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(treelsFilterRequest.getSearchCriteria(), treelsFilterRequest, httpHeaderMap);

            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DOWNSTREAM_TREELS_FILTER_HES_CALL, TREELS_FILTER_COUNT, System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();
            SearchWrapperInputRequest treelsFilterRequestHES =
                    treelsFilterTransformer
                            .convertSearchRequest(treelsFilterRequest, commonModifierResponse);

            // Execute treels filter count with circuit breaker protection
            FilterSearchMetaDataResponse treelsFilterResponseHES = circuitBreakerService.executeFilterCountWithCircuitBreaker(
                () -> {
                    try {
                        return treelsListingExecutor.filterCount(treelsFilterRequestHES, httpHeaderMap);
                    } catch (ClientGatewayException e) {
                        throw new RuntimeException("error in treels-filter-count api call",e);
                    }
                },
                httpHeaderMap
            );

            if (treelsFilterResponseHES != null && treelsFilterResponseHES.getResponseErrors() != null && CollectionUtils.isNotEmpty(treelsFilterResponseHES.getResponseErrors().getErrorList())) {
                logger.warn("Received error from downstream in treelsFilterCount:");
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, treelsFilterResponseHES.getResponseErrors().getErrorList().get(0).getErrorCode(),
                        treelsFilterResponseHES.getResponseErrors().getErrorList().get(0).getErrorMessage());
            } else if (treelsFilterResponseHES!=null && MapUtils.isEmpty(treelsFilterResponseHES.getFilterDataMap())) {
                logger.warn("Received empty filter response from downstream in treelsFilterCount:");
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.MARSHALLING, MarshallingErrors.NO_DATA_FOUND.getErrorCode(), MarshallingErrors.NO_DATA_FOUND.getErrorMsg());
            }
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DOWNSTREAM_TREELS_FILTER_HES_CALL, TREELS_FILTER_COUNT, System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();
            filterResponse = treelsFilterResponseTransformer.convertFilterResponse(treelsFilterResponseHES,treelsFilterRequest);
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DOWNSTREAM_TREELS_FILTER_HES_CALL, TREELS_FILTER_COUNT, System.currentTimeMillis() - startTime);
        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in treelsFilterCount: " + e.getMessage(), e);
                logger.debug("error occurred in treelsFilterCount: " + e.getMessage(), e);
            } else
                logger.error("error occurred in treelsFilterCount: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
        return filterResponse;
    }
}
