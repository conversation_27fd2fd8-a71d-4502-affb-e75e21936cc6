package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.ValidationErrors;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.exception.GenericException;
import com.mmt.hotels.clientgateway.exception.ValidationException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.CorporateHelper;
import com.mmt.hotels.clientgateway.helpers.ErrorHelper;
import com.mmt.hotels.clientgateway.helpers.ForwardBookingFlowHelper;
import com.mmt.hotels.clientgateway.request.UpdatePolicyRequest;
import com.mmt.hotels.clientgateway.response.AvailRoomsResponse;
import com.mmt.hotels.clientgateway.response.InitApprovalResponse;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.response.corporate.*;
import com.mmt.hotels.clientgateway.restexecutors.CorporateExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.AvailRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.factory.InitiateApprovalFactory;
import com.mmt.hotels.clientgateway.transformer.factory.UpdateApprovalFactory;
import com.mmt.hotels.clientgateway.transformer.factory.UpdatePolicyFactory;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.corporate.GetApprovalRequest;
import com.mmt.hotels.model.request.corporate.InitApprovalRequest;
import com.mmt.hotels.model.request.corporate.UpdateApprovalRequest;
import com.mmt.hotels.model.response.corporate.CorpPolicyUpdateRequest;
import com.mmt.hotels.model.response.corporate.CorpPolicyUpdateResponse;
import com.mmt.hotels.model.response.corporate.UpdateWorkflowResponse;
import com.mmt.hotels.model.response.errors.Error;
import com.mmt.hotels.model.response.pricing.HotelRates;
import com.mmt.hotels.pojo.response.userservice.UserDetailsDTO;
import com.mmt.model.HotelsRoomInfoResponseEntity;
import com.mmt.scrambler.ScramblerClient;
import com.mmt.scrambler.utils.HashType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.mmt.hotels.clientgateway.constants.Constants.USER_ROLE_CONSTANT;
import static com.mmt.hotels.clientgateway.constants.Constants.APPROVER;
import static com.mmt.hotels.clientgateway.constants.Constants.APPROVAL_STATUS;

@Component
public class CorporateService {

    @Autowired
    CorporateExecutor corporateExecutor;

    @Autowired
    CorporateHelper corporateHelper;

    @Autowired
    private ForwardBookingFlowHelper forwardBookingFlowHelper;

    @Autowired
    ErrorHelper errorHelper;

    @Autowired
    CommonHelper commonHelper;

    @Autowired
    UpdatePolicyFactory updatePolicyFactory;

    @Autowired
    InitiateApprovalFactory initiateApprovalFactory;

    @Autowired
    private UpdateApprovalFactory updateApprovalFactory;

    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
    private AvailRoomsFactory availRoomsFactory;

    @Autowired
    MetricAspect metricAspect;

    @Autowired
    private CommonResponseTransformer commonResponseTransformer;

    @Autowired
    protected PolyglotService polyglotService;

    private static final Logger logger = LoggerFactory.getLogger(CorporateService.class);


    @Deprecated
    public CGServerResponse updateApprovalByAuthcode(UpdateApprovalRequest updateApprovalBody, Map<String, String[]> parameterMap, String authCode, Map<String,String> httpHeaderMap, String correlationKey) throws Exception {

        CGServerResponse serverResponse = null;
        UserDetailsDTO userDetailsDTO = null;
        try {

            if(StringUtils.isBlank(correlationKey)) {
                correlationKey = UUID.randomUUID().toString();
            }

            String workFlow_Id = null;
            String emailId = null;

            WorkflowInfoResponse workflowInfo = corporateExecutor.getWorkflowInfoByAuthCode(authCode,parameterMap,correlationKey);
            if(workflowInfo != null && workflowInfo.getApproverInfo() != null
                    && StringUtils.isNotBlank(workflowInfo.getApproverInfo().getEmailId())) {
                emailId = workflowInfo.getApproverInfo().getEmailId();
                workFlow_Id = workflowInfo.getWorkflowId();
            }

            String deviceType = updateApprovalBody.getDeviceDetails() != null && updateApprovalBody.getDeviceDetails().getOsType() != null
                    ? updateApprovalBody.getDeviceDetails().getOsType().getValue().toUpperCase() : "";

            userDetailsDTO = corporateHelper.getCorpUserIdForMyBizUser(deviceType,
                    httpHeaderMap, correlationKey, emailId,updateApprovalBody.getSiteDomain());


            updateApprovalBody.setCorrelationKey(correlationKey);

            if (StringUtils.isBlank(userDetailsDTO.getUuid()) && null != workflowInfo && null != workflowInfo.getApproverInfo()
                    && StringUtils.isNotBlank(workflowInfo.getApproverInfo().getApproverEmailCommId())) {
                updateApprovalBody.setEmailCommId(workflowInfo.getApproverInfo().getApproverEmailCommId());
            } else {
                updateApprovalBody.setApproverUuid(userDetailsDTO.getUuid());
            }

            serverResponse = corporateExecutor.getUpdateApprovalResponse(updateApprovalBody, parameterMap, workFlow_Id, httpHeaderMap);
            if (serverResponse == null)
                throw new GenericException("Empty response from webapi for update corporate approval API");

        }catch(Exception ex) {

            if (ex instanceof GenericException){
                throw new GenericException("Employee not found for corp user Id " + userDetailsDTO.getProfileId());

            }else{
                logger.error("Error occured in update corp approval, Exception : {} ", ex);
                throw ex;
            }

        }
        return serverResponse;
    }

    @Deprecated
    public CGServerResponse updateApproval(UpdateApprovalRequest updateApprovalBody, Map<String, String[]> parameterMap, String workflowId, Map<String,String> httpHeaderMap, String correlationKey) throws Exception {

        CGServerResponse serverResponse = null;
        UserDetailsDTO userDetailsDTO = null;
        try {
            String deviceType = updateApprovalBody.getDeviceDetails() != null && updateApprovalBody.getDeviceDetails().getOsType() != null
                    ? updateApprovalBody.getDeviceDetails().getOsType().getValue().toUpperCase() : "";
            userDetailsDTO = corporateHelper.getCorpUserIdForMyBizUser(deviceType,
                    httpHeaderMap, correlationKey, null,updateApprovalBody.getSiteDomain());
            updateApprovalBody.setCorrelationKey(correlationKey);
            updateApprovalBody.setApproverUuid(userDetailsDTO.getUuid());
            serverResponse = corporateExecutor.getUpdateApprovalResponse(updateApprovalBody, parameterMap, workflowId, httpHeaderMap);
            if (serverResponse == null)
                throw new GenericException("Empty response from webapi for update corporate approval API");

        }catch(Exception ex) {

            if (ex instanceof GenericException){
                throw new GenericException("Employee not found for corp user Id " + userDetailsDTO.getProfileId());

            }else{
                logger.error("Error occured in update corp approval, Exception : {} ", ex);
                throw ex;
            }

        }
        return serverResponse;
    }



    public CGServerResponse workflowInfoByAuthcode(String authCode, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, String correlationKey, String siteDomain, String srcClient) throws Exception {

    CGServerResponse serverResponse = null;

    try {
        if(StringUtils.isBlank(correlationKey)) {
            correlationKey = UUID.randomUUID().toString();
        }

        String workFlow_Id = null;
        String emailId = null;

        WorkflowInfoResponse workflowInfo = corporateExecutor.getWorkflowInfoByAuthCode(authCode,parameterMap,correlationKey);
        if(workflowInfo != null && workflowInfo.getApproverInfo() != null
                && StringUtils.isNotBlank(workflowInfo.getApproverInfo().getEmailId())) {
            emailId = workflowInfo.getApproverInfo().getEmailId();
            workFlow_Id = workflowInfo.getWorkflowId();
        }
        httpHeaderMap.put("mmt-auth", null);
        httpHeaderMap.put("backup_auth", null);
        UserDetailsDTO userDetailsDTO = corporateHelper.getCorpUserIdForMyBizUser(srcClient,
                httpHeaderMap, correlationKey, emailId, siteDomain);


        GetApprovalRequest getApprovalRequest = new GetApprovalRequest();

        if (StringUtils.isBlank(userDetailsDTO.getUuid()) && null != workflowInfo && null != workflowInfo.getApproverInfo()
                && StringUtils.isNotBlank(workflowInfo.getApproverInfo().getApproverEmailCommId())) {
            getApprovalRequest.setEmailCommId(workflowInfo.getApproverInfo().getApproverEmailCommId());
        } else {
            getApprovalRequest.setUuid(userDetailsDTO.getUuid());
        }
        getApprovalRequest.setProfileType(userDetailsDTO.getProfileType());
        getApprovalRequest.setSrcClient(srcClient);


        serverResponse = corporateExecutor.workflowInfoByAuthcode(getApprovalRequest, parameterMap, correlationKey, workFlow_Id, httpHeaderMap);

        if (serverResponse == null)
            throw new GenericException("Empty response from webapi for get workflow info API");

    }catch(Exception ex) {
        logger.error("Error occured in get corp approval workflow info, Exception : {} ", ex);
        throw ex;
    }
        return serverResponse;


    }


    public GetApprovalsResponse approvalsInfo(String workflowId, String authCode, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, String correlationKey, String siteDomain, String srcClient) throws ClientGatewayException {

        try {
            GetApprovalsResponse finalApprovalsResponse = new GetApprovalsResponse();

            long startTime = new Date().getTime();
            try {
                if(StringUtils.isBlank(correlationKey)) {
                    correlationKey = UUID.randomUUID().toString();
                }


                String emailId = null;
                com.mmt.hotels.model.response.corporate.GetApprovalsResponse approvalsResponse = null;
                if (StringUtils.isNotBlank(workflowId)) {
                    UserDetailsDTO userDetailsDTO = corporateHelper.getCorpUserIdForMyBizUser(srcClient,
                            httpHeaderMap,
                            correlationKey, emailId,
                            siteDomain);

                    GetApprovalRequest getApprovalRequest = new GetApprovalRequest();

                    getApprovalRequest.setUuid(userDetailsDTO.getUuid());
                    getApprovalRequest.setProfileType(userDetailsDTO.getProfileType());

                    getApprovalRequest.setSrcClient(srcClient);
                    approvalsResponse = corporateExecutor.getApprovalsInfo(getApprovalRequest, parameterMap,
                                                correlationKey, httpHeaderMap);
                    if (approvalsResponse.getResponseErrors() != null && CollectionUtils.isNotEmpty(
                        approvalsResponse.getResponseErrors().getErrorList())) {
                        Error error = approvalsResponse.getResponseErrors().getErrorList().get(0);
                        JSONObject metaData = new JSONObject();
                        if (approvalsResponse != null && approvalsResponse.getApprovalDetails() != null) {
                            if(APPROVER.equalsIgnoreCase(approvalsResponse.getApprovalDetails().getRole()))
                                metaData.put(USER_ROLE_CONSTANT, approvalsResponse.getApprovalDetails().getRole());
                            if(approvalsResponse.getApprovalDetails() != null && approvalsResponse.getApprovalDetails().getApprovalAction() != null)
                                metaData.put(APPROVAL_STATUS, approvalsResponse.getApprovalDetails().getApprovalAction().getValue());
                        }

                        forwardBookingFlowHelper.setForwardBookingFlowDataToMetaData(approvalsResponse, metaData);

                        throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR,
                                ErrorType.DOWNSTREAM,
                                error.getErrorCode(), error.getErrorMessage(), null, metaData);
                    }
                } else if (StringUtils.isNotBlank(authCode)) {
                    WorkflowInfoResponse workflowInfo = corporateExecutor
                                                            .getWorkflowInfoByAuthCode(authCode, parameterMap,
                                                                                       correlationKey);
                    if (workflowInfo != null && workflowInfo.getApproverInfo() != null
                        && StringUtils.isNotBlank(workflowInfo.getApproverInfo().getEmailId())) {
                        emailId = workflowInfo.getApproverInfo().getEmailId();
                    }
                    httpHeaderMap.put("mmt-auth", null);
                    httpHeaderMap.put("backup_auth", null);

                    GetApprovalRequest getApprovalRequest = new GetApprovalRequest();

                    String mmtAuth = commonHelper.getAuthToken(httpHeaderMap);

                    // HTL-43883 in case of authCode, we are skipping user service call as we will not get email or mmtAuth.
                    if ((StringUtils.isNotBlank(emailId) || StringUtils.isNotBlank(mmtAuth)) && StringUtils.isNotBlank(authCode)) {
                        UserDetailsDTO userDetailsDTO = corporateHelper.getCorpUserIdForMyBizUser(srcClient,
                                httpHeaderMap,
                                correlationKey, emailId,
                                siteDomain);
                        getApprovalRequest.setUuid(userDetailsDTO.getUuid());
                        getApprovalRequest.setProfileType(userDetailsDTO.getProfileType());
                    }

                    getApprovalRequest.setSrcClient(srcClient);
                    approvalsResponse = corporateExecutor.getApprovalsInfo(getApprovalRequest, parameterMap,
                                                                           correlationKey, httpHeaderMap);
                    if (approvalsResponse.getResponseErrors() != null && CollectionUtils.isNotEmpty(
                        approvalsResponse.getResponseErrors().getErrorList())) {
                        Error error = approvalsResponse.getResponseErrors().getErrorList().get(0);
                        JSONObject metaData = new JSONObject();
                        if(approvalsResponse!=null && approvalsResponse.getApprovalDetails()!=null && "APPROVER".equalsIgnoreCase(approvalsResponse.getApprovalDetails().getRole())){
                            metaData.put(USER_ROLE_CONSTANT, approvalsResponse.getApprovalDetails().getRole());
                        }
                        throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR,
                                ErrorType.DOWNSTREAM,
                                error.getErrorCode(), error.getErrorMessage(), null, metaData);
                    }
                } else {
                    throw new ValidationException(DependencyLayer.CLIENTGATEWAY, ErrorType.VALIDATION,
                                                  ValidationErrors.WORKFLOW_ID_AND_AUTHCODE_NULL.getErrorCode(),
                                                  ValidationErrors.WORKFLOW_ID_AND_AUTHCODE_NULL.getErrorMsg());
                }
                AvailRoomsResponse availRoomsResponse =  availRoomsFactory.getResponseService(srcClient)
                        .convertAvailRoomsResponse(null, approvalsResponse.getRoomDetailsResponse(), new HotelsRoomInfoResponseEntity(), siteDomain,
                                approvalsResponse.getPriceByHotelsRequestBody().getCheckin(), null, approvalsResponse.getPriceByHotelsRequestBody().getExperimentData(),
                                "", false, "", null, false,StringUtils.EMPTY);
                updateCorpInfoForQuickCheckout(approvalsResponse, availRoomsResponse, finalApprovalsResponse);



                populateAvailResponse(availRoomsResponse, finalApprovalsResponse);
                finalApprovalsResponse.setCanApprove(approvalsResponse.isCurrentApprover());
                finalApprovalsResponse.setApprovalDetails(approvalsResponse.getApprovalDetails());
                finalApprovalsResponse.setSearchContext(getSearchContext(approvalsResponse.getPriceByHotelsRequestBody()));
                finalApprovalsResponse.setExternalChainMembershipID(approvalsResponse.getExternalChainMembershipID());
                if (approvalsResponse.getPriceByHotelsRequestBody() != null && StringUtils.isNotEmpty(approvalsResponse.getPriceByHotelsRequestBody().getCurrency())) {
                    finalApprovalsResponse.setCurrency(approvalsResponse.getPriceByHotelsRequestBody().getCurrency());
                }
                return finalApprovalsResponse;
            }catch(Exception ex) {
                throw ex;
            }
            finally {
                metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(),"cg/get-approvals/", new Date().getTime() - startTime);
            }
        }catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occured in corp approvals info: " + e.getMessage());
                logger.debug("error occured in corp approvals info: " + e.getMessage(), e);
            }else
                logger.error("error occured in corp approvals info: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }



    private void populateAvailResponse(AvailRoomsResponse availRoomsResponse, GetApprovalsResponse getApprovalsResponse){
        getApprovalsResponse.setTripDetailsCard(availRoomsResponse.getTripDetailsCard());
        getApprovalsResponse.setHotelInfo(availRoomsResponse.getHotelInfo());
        getApprovalsResponse.setAddons(availRoomsResponse.getAddons());
        getApprovalsResponse.setTotalpricing(availRoomsResponse.getTotalpricing());
        getApprovalsResponse.setRateplanlist(availRoomsResponse.getRateplanlist());
        getApprovalsResponse.setSpecialrequests(availRoomsResponse.getSpecialrequests());
        getApprovalsResponse.setSpecialRequestV2(availRoomsResponse.getSpecialRequestV2());
        getApprovalsResponse.setShowSpecialRequestsV2(availRoomsResponse.isShowSpecialRequestsV2());
        getApprovalsResponse.setPropertyRules(availRoomsResponse.getPropertyRules());
        getApprovalsResponse.setPanInfo(availRoomsResponse.getPanInfo());
        getApprovalsResponse.setEmiDetails(availRoomsResponse.getEmiDetails());
        getApprovalsResponse.setDoubleBlackInfo(availRoomsResponse.getDoubleBlackInfo());
        getApprovalsResponse.setFeatureFlags(availRoomsResponse.getFeatureFlags());
        getApprovalsResponse.setBnplDetails(availRoomsResponse.getBnplDetails());
        getApprovalsResponse.setGstInfo(availRoomsResponse.getGstInfo());
        getApprovalsResponse.setTxnKey(availRoomsResponse.getTxnKey());
        getApprovalsResponse.setAdditionalFees(availRoomsResponse.getAdditionalFees());
        getApprovalsResponse.setAlerts(availRoomsResponse.getAlerts());
        getApprovalsResponse.setCampaignAlert(availRoomsResponse.getCampaignAlert());
        getApprovalsResponse.setBlackInfo(availRoomsResponse.getBlackInfo());
        getApprovalsResponse.setTravellers(availRoomsResponse.getTravellers());
        getApprovalsResponse.setSelectedSpecialRequests(availRoomsResponse.getSelectedSpecialRequests());
        getApprovalsResponse.setCorpApprovalInfo(availRoomsResponse.getCorpApprovalInfo());
        getApprovalsResponse.setCancellationTimeline(availRoomsResponse.getCancellationTimeline());
        getApprovalsResponse.setRoomStayCandidates(availRoomsResponse.getRoomStayCandidates());
        getApprovalsResponse.setSafetyPersuasionList(availRoomsResponse.getSafetyPersuasionList());
        getApprovalsResponse.setSafetyPersuasionMap(availRoomsResponse.getSafetyPersuasionMap());
        getApprovalsResponse.setHotelPersuasions(availRoomsResponse.getHotelPersuasions());
    }

    private PriceByHotelsRequestBody getSearchContext(PriceByHotelsRequestBody requestBody){
        PriceByHotelsRequestBody searchContext = new PriceByHotelsRequestBody();
        searchContext.setCheckin(requestBody.getCheckin());
        searchContext.setCheckout(requestBody.getCheckout());
        searchContext.setExtraInfo(requestBody.getExtraInfo());
        searchContext.setCountryCode(requestBody.getCountryCode());
        searchContext.setCurrency(requestBody.getCurrency());
        return searchContext;
    }

    public CGServerResponse getWorkflowInfo(String workflowId, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, String correlationKey, String siteDomain, String srcClient) throws Exception {
        CGServerResponse serverResponse ;
        try {
            if(StringUtils.isBlank(correlationKey)) {
                correlationKey = UUID.randomUUID().toString();
            }
            UserDetailsDTO userDetailsDTO = corporateHelper.getCorpUserIdForMyBizUser(srcClient,
                    httpHeaderMap, correlationKey, null, siteDomain);

            GetApprovalRequest getApprovalRequest = new GetApprovalRequest();
            getApprovalRequest.setUuid(userDetailsDTO.getUuid());
            getApprovalRequest.setProfileType(userDetailsDTO.getProfileType());
            getApprovalRequest.setSrcClient(srcClient);
            serverResponse = corporateExecutor.workflowInfoByAuthcode(getApprovalRequest, parameterMap, correlationKey, workflowId, httpHeaderMap);
            if (serverResponse == null)
                throw new GenericException("Empty response from webapi for get workflow info API");
        }catch(Exception ex) {
            logger.error("Error occured in get corp approval workflow info, Exception : {} ", ex);
            throw ex;
        }
        return serverResponse;
    }


    public CGServerResponse requestApproval(InitApprovalRequest approvalRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap,
			String correlationKey) throws Exception {

		CGServerResponse serverResponse = null;
		
        UserDetailsDTO userDetailsDTO = corporateHelper.getCorpUserIdForMyBizUser(Utility.getDeviceInfo(approvalRequest.getDeviceDetails()),
                httpHeaderMap, correlationKey, null, approvalRequest.getSiteDomain());
        
        approvalRequest.setCorpUserId(userDetailsDTO.getProfileId());
        approvalRequest.setCorrelationKey(correlationKey);
        approvalRequest.setUuid(userDetailsDTO.getUuid());
        approvalRequest.setMmtAuth(userDetailsDTO.getMmtAuth());

        serverResponse =  corporateExecutor.requestApproval(approvalRequest,parameterMap,correlationKey,httpHeaderMap);
        
        if(null == serverResponse){
        	throw new GenericException("Empty response from webapi for requestApproval API");
        }

		return serverResponse;
	}
    public String updateCorpPolicy(CorpPolicyUpdateRequest request, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap,
                                   String correlationKey) throws Exception {

        UserDetailsDTO userDetailsDTO = corporateHelper.getCorpUserIdForMyBizUser(request.getBookingDevice(),
                httpHeaderMap, correlationKey, null, request.getSiteDomain());

        request.setBookerUuid(userDetailsDTO.getUuid());
        request.setProfileType(userDetailsDTO.getProfileType());
        request.setProfileId(userDetailsDTO.getProfileId());

        ScramblerClient scramblerClient = ScramblerClient.getInstance();
        if(CollectionUtils.isNotEmpty(request.getTravellerEmailId())){
            List<String> commEmails=new ArrayList<>();
            for(String email:request.getTravellerEmailId()){
                String commEmail=scramblerClient.encode(email, HashType.F);
                commEmails.add(commEmail);
            }
            request.setTravellerEmailId(commEmails);
        }

        String response  = corporateExecutor.updateCorpPolicy(request,parameterMap,httpHeaderMap,correlationKey);

        if(null == response){
            throw new GenericException("Empty response from webapi for updateCorpPolicy API");
        }

        return response;
    }


    public UpdatePolicyResponse updatePolicy(UpdatePolicyRequest request,  Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, String client, String correlationKey) throws ClientGatewayException {

        try{
            UpdatePolicyRequest updatePolicyRequest = updatePolicyFactory.getRequestService(client).convertUpdatePolicyRequest(request, httpHeaderMap, correlationKey);
            CorpPolicyUpdateResponse responseHES = corporateExecutor.updatePolicy(updatePolicyRequest, parameterMap, httpHeaderMap, client, correlationKey);
            return updatePolicyFactory.getResponseService(client).convertUpdatePolicyResponse(responseHES);
        }catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occured in fetch updatePolicy: " + e.getMessage());
                logger.debug("error occured in fetch updatePolicy: " + e.getMessage(), e);
            }else
                logger.error("error occured in fetch updatePolicy: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }

    }

    public InitApprovalResponse initiateApproval(com.mmt.hotels.clientgateway.request.InitApprovalRequest request,  Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, String client, String correlationKey) throws ClientGatewayException {

        try {
            InitApprovalRequest initApprovalRequestHES = initiateApprovalFactory.getRequestService(client).convertInitApprovalRequest(request, httpHeaderMap, correlationKey);
            CGServerResponse responseHES = corporateExecutor.requestApproval(initApprovalRequestHES, parameterMap, correlationKey, httpHeaderMap);
            InitApprovalResponse initApprovalResponse = initiateApprovalFactory.getResponseService(client).processResponse(responseHES);

            return initApprovalResponse;
        } catch (Throwable e) {
            logger.error("error occured in init approval: " + e.getMessage(), e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public UpdateApprovalResponse updateApprovalUsingWorkflowId(com.mmt.hotels.clientgateway.request.UpdateApprovalRequest
                                                                        updateApprovalRequest,
                                                                Map<String, String[]> parameterMap, String workflowId,
                                                                Map<String, String> httpHeaderMap, String correlationKey,
                                                                String client)
            throws ClientGatewayException {
        try {
            UpdateApprovalRequest updateApprovalRequestHES = updateApprovalFactory.getRequestService(client)
                    .convertUpdateApprovalRequest(
                            updateApprovalRequest, client,
                            correlationKey, httpHeaderMap);
            UpdateWorkflowResponse responseHES = corporateExecutor
                    .updateApproval(updateApprovalRequestHES, workflowId, parameterMap,
                            correlationKey, httpHeaderMap);
            UpdateApprovalResponse updateApprovalResponse = updateApprovalFactory.getResponseService(client)
                    .convertUpdateApprovalResponse(
                            responseHES);
            return updateApprovalResponse;
        } catch (Exception e) {
            logger.error("Exception occurred in updateApproval workflow API::{}, {} ", e.getMessage(), e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public UpdateApprovalResponse updateApprovalUsingAuthCode(com.mmt.hotels.clientgateway.request.UpdateApprovalRequest
                                                                      updateApprovalRequest,
                                                              Map<String, String[]> parameterMap, String authCode,
                                                              Map<String, String> httpHeaderMap, String correlationKey,
                                                              String client)
            throws ClientGatewayException {
        try {
            String workFlowId = null;
            WorkflowInfoResponse workflowInfo = corporateExecutor
                    .getWorkflowInfoByAuthCode(authCode, parameterMap, correlationKey);
            if (workflowInfo != null) {
                workFlowId = workflowInfo.getWorkflowId();
            }
            return updateApprovalUsingWorkflowId(updateApprovalRequest, parameterMap, workFlowId, httpHeaderMap,
                    correlationKey, client);
        } catch (Exception e) {
            logger.error("Exception occurred in updateApproval using authCode API::{}, {} ", e.getMessage(), e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public UpdateApprovalResponse updateApprovals(com.mmt.hotels.clientgateway.request.UpdateApprovalRequest updateApprovalRequest,
                                                  Map<String, String[]> parameterMap,
                                                  Map<String, String> httpHeaderMap,
                                                  String authCode,
                                                  String workflowId,
                                                  String correlationKey,
                                                  String client) throws ClientGatewayException {
        try {
            UpdateApprovalRequest updateApprovalRequestHES = updateApprovalFactory.getRequestService(client)
                                            .convertUpdateApprovalRequest(updateApprovalRequest, client, correlationKey, httpHeaderMap);
            if(StringUtils.isNotBlank(authCode)) {
                WorkflowInfoResponse workflowInfo = corporateExecutor.getWorkflowInfoByAuthCode(authCode, parameterMap, correlationKey);
                if (workflowInfo != null && workflowInfo.getApproverInfo() != null
                        && StringUtils.isNotBlank(workflowInfo.getApproverInfo().getEmailId())) {
                    UserDetailsDTO userDetailsDTO = corporateHelper.getCorpUserIdForMyBizUser(null,
                            httpHeaderMap,
                            correlationKey, workflowInfo.getApproverInfo().getEmailId(),
                            null);
                    updateApprovalRequestHES.setApproverUuid(userDetailsDTO.getUuid());
                    updateApprovalRequestHES.setEmailCommId(workflowInfo.getApproverInfo().getApproverEmailCommId());
                }

            }
            UpdateWorkflowResponse responseHES = corporateExecutor
                                            .updateApprovals(updateApprovalRequestHES, parameterMap, correlationKey, httpHeaderMap);
            UpdateApprovalResponse updateApprovalResponse = updateApprovalFactory.getResponseService(client)
                                            .convertUpdateApprovalResponse(responseHES);
            return updateApprovalResponse;
        } catch (Exception e) {
            logger.error("Exception occurred in updateApproval workflow API::{}, {} ", e.getMessage(), e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    private void updateCorpInfoForQuickCheckout(com.mmt.hotels.model.response.corporate.GetApprovalsResponse approvalsResponse, AvailRoomsResponse availRoomsResponse, GetApprovalsResponse finalApprovalsResponse) {
        if (approvalsResponse != null) {
            if (availRoomsResponse != null && availRoomsResponse.getCorpApprovalInfo() != null) {
                availRoomsResponse.getCorpApprovalInfo().setWalletQuickPayAllowed(approvalsResponse.isQuickCheckout() && !approvalsResponse.isTcsV2FlowEnabled());
            }
            if (approvalsResponse.isQuickCheckout() && approvalsResponse.getRoomDetailsResponse() != null && CollectionUtils.isNotEmpty(approvalsResponse.getRoomDetailsResponse().getHotelRates())) {
                HotelRates hotelRates = approvalsResponse.getRoomDetailsResponse().getHotelRates().get(0);
                if (hotelRates != null && hotelRates.getRoomTypeDetails() != null && hotelRates.getRoomTypeDetails().getTotalDisplayFare() != null
                        && hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown() != null) {
                    finalApprovalsResponse.setMyBizQuickPayConfig(commonResponseTransformer.buildMyBizQuickPayConfig(hotelRates.getRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown(), hotelRates.getCurrencyCode()));
                }

            }

        }

    }

    public GuestHouseResponse getAvailableGuestHouses(String transactionKey, Map<String, String[]> parameterMap, String correlationKey, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {
            return corporateExecutor.getAvailableGuestHouses(transactionKey, parameterMap, correlationKey, httpHeaderMap);
        } catch (Exception e) {
            logger.error("Exception occurred in getAvailableGuestHouses API: {} | ", e.getMessage(), e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }
}
