package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.BankOffersRequestCG;
import com.mmt.hotels.clientgateway.response.BankOffersResponseCG;
import com.mmt.hotels.clientgateway.restexecutors.BankOffersExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.BankOffersFactory;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.pojo.request.landing.BankOffersRequest;
import com.mmt.hotels.pojo.response.bankoffers.BankOffersResponse;
import org.mockito.Mock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.Date;
import java.util.Map;

@Component
public class BankOffersService {

    @Autowired
    private BankOffersFactory bankOffersFactory;

    @Autowired
    private CommonHelper commonHelper;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    BankOffersExecutor bankOffersExecutor;

    private static final Logger logger = LoggerFactory.getLogger(BankOffersService.class);

    public BankOffersResponseCG bankOffers(BankOffersRequestCG bankOffersRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        long startTime = new Date().getTime();
        try {
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(bankOffersRequest.getSearchCriteria(), bankOffersRequest, httpHeaderMap);
            BankOffersRequest request = bankOffersFactory.getRequestService(bankOffersRequest.getClient()).convertBankOffersRequest(bankOffersRequest, commonModifierResponse);
            startTime = new Date().getTime();
            BankOffersResponse response = bankOffersExecutor.bankOffers(request, bankOffersRequest.getCorrelationKey(), parameterMap, httpHeaderMap);
            return bankOffersFactory.getResponseService(bankOffersRequest.getClient()).convertBankOffersResponse(response);
        } catch (Exception ex) {
            logger.error("Exception occurred in bankOffers service:", ex);
            throw ex;
        }  finally {
            metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "bankOffers", new Date().getTime() - startTime);
        }
    }

}
