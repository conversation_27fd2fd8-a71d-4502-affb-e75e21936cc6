package com.mmt.hotels.clientgateway.service;

import com.gommt.hotels.orchestrator.detail.enums.Brand;
import com.gommt.hotels.orchestrator.detail.enums.Country;
import com.gommt.hotels.orchestrator.detail.enums.Currency;
import com.gommt.hotels.orchestrator.detail.enums.DeviceType;
import com.gommt.hotels.orchestrator.detail.enums.Funnel;
import com.gommt.hotels.orchestrator.detail.enums.IdContext;
import com.gommt.hotels.orchestrator.detail.enums.Language;
import com.gommt.hotels.orchestrator.detail.enums.OTA;
import com.gommt.hotels.orchestrator.detail.enums.PageContext;
import com.gommt.hotels.orchestrator.detail.enums.ProfileType;
import com.gommt.hotels.orchestrator.detail.enums.Region;
import com.gommt.hotels.orchestrator.detail.enums.SiteDomain;
import com.gommt.hotels.orchestrator.detail.enums.SubProfileType;
import com.gommt.hotels.orchestrator.detail.enums.TrafficSource;
import com.gommt.hotels.orchestrator.detail.enums.TrafficType;
import com.gommt.hotels.orchestrator.detail.enums.filter.FilterGroup;
import com.gommt.hotels.orchestrator.detail.model.objects.BookingDevice;
import com.gommt.hotels.orchestrator.detail.model.objects.MultiCurrencyInfo;
import com.gommt.hotels.orchestrator.detail.model.objects.RoomCriteria;
import com.gommt.hotels.orchestrator.detail.model.objects.SortCriteria;
import com.gommt.hotels.orchestrator.detail.model.request.DetailRequest;
import com.gommt.hotels.orchestrator.detail.model.request.common.ClientDetails;
import com.gommt.hotels.orchestrator.detail.model.request.common.FilterDetails;
import com.gommt.hotels.orchestrator.detail.model.request.common.GeoLocationDetails;
import com.gommt.hotels.orchestrator.detail.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.detail.model.request.common.RangeDetails;
import com.gommt.hotels.orchestrator.detail.model.request.ugc.AggregationTag;
import com.gommt.hotels.orchestrator.detail.model.request.ugc.CohortData;
import com.gommt.hotels.orchestrator.detail.model.request.ugc.TravellerReviewsRequest;
import com.gommt.hotels.orchestrator.detail.model.response.ConsolidatedCalendarAvailabilityResponse;
import com.gommt.hotels.orchestrator.detail.model.response.HotelDetailsResponse;
import com.gommt.hotels.orchestrator.detail.model.response.HotelStaticContentResponse;
import com.gommt.hotels.orchestrator.detail.model.response.UpdatePriceResponse;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.TravellerReviewResponse;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.TravellerReviewSummary;
import com.gommt.hotels.orchestrator.detail.model.state.ChatbotDetails;
import com.gommt.hotels.orchestrator.detail.model.state.RequiredSections;
import com.gommt.hotels.orchestrator.detail.model.state.UserDetails;
import com.gommt.hotels.orchestrator.detail.model.state.UserSessionData;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.FilterHelper;
import com.mmt.hotels.clientgateway.helpers.OrchAlternatePriceHelper;
import com.mmt.hotels.clientgateway.request.CalendarAvailabilityRequest;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.ImageCategory;
import com.mmt.hotels.clientgateway.request.PlatformUgcCategoryRequest;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.RequiredApis;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.request.StaticDetailCriteria;
import com.mmt.hotels.clientgateway.request.UpdatePriceRequest;
import com.mmt.hotels.clientgateway.request.UserGlobalInfo;
import com.mmt.hotels.clientgateway.request.dayuse.DayUseRoomsRequest;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.staticdetail.StaticDetailResponse;
import com.mmt.hotels.clientgateway.restexecutors.OrchDetailExecutor;
import com.mmt.hotels.clientgateway.thirdparty.request.UgcReviewRequest;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.transformer.factory.OrchStaticDetailFactory;
import com.mmt.hotels.clientgateway.transformer.factory.OrchTravellerReviewSummaryFactory;
import com.mmt.hotels.clientgateway.transformer.factory.OrchUpdatedPriceFactory;
import com.mmt.hotels.clientgateway.transformer.factory.SearchRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.factory.SearchSlotsFactory;
import com.mmt.hotels.clientgateway.transformer.factory.UpdatedPriceFactory;
import com.mmt.hotels.clientgateway.transformer.request.OrchUpdatedPriceRequestTransformer;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.UserLocation;
import com.mmt.hotels.model.response.flyfish.UGCPlatformReviewSummaryDTO;
import com.mmt.hotels.model.response.flyfish.UgcReviewResponseData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.BEDROOM_COUNT;
import static com.mmt.hotels.clientgateway.constants.Constants.CORP_ID_CONTEXT;
import static com.mmt.hotels.clientgateway.constants.Constants.EXACT_ROOM_RECOMMENDATION;
import static com.mmt.hotels.clientgateway.constants.Constants.EXACT_ROOM_VALUE;
import static com.mmt.hotels.clientgateway.constants.Constants.FILTER_GROUP_SPLITTER_DPT;
import static com.mmt.hotels.clientgateway.constants.Constants.FILTER_GROUP_VALUE_SPLITTER_DPT;
import static com.mmt.hotels.clientgateway.constants.Constants.FILTER_VALUE_SPLITTER_DPT;
import static com.mmt.hotels.clientgateway.constants.Constants.MATCHMAKER_AREA;
import static com.mmt.hotels.clientgateway.constants.Constants.MATCHMAKER_POI;
import static com.mmt.hotels.clientgateway.constants.Constants.OS;
import static com.mmt.hotels.clientgateway.constants.Constants.USER_CURRENCY;
import static com.mmt.hotels.clientgateway.constants.Constants.VISITOR_ID;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.DETAIL_SEARCH_ROOMS;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.DETAIL_STATIC_DETAIL;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.DETAIL_UGC_REVIEWS;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.DETAIL_UGC_SUMMARY;
import static com.mmt.hotels.clientgateway.helpers.FilterHelper.updateAppliedFilterMapForLocationDptFilters;

@Component
public class OrchDetailService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrchDetailService.class);

    @Autowired
    Utility utility;

    @Autowired
    FilterHelper filterHelper;

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private OrchAlternatePriceHelper orchAlternatePriceHelper;

    @Value("${orch.calendar.availability.url}")
    private String alternateDatesPriceUrl;

    @Autowired
    private RestConnectorUtil restConnectorUtil;

    
    @Autowired
    OrchUpdatedPriceRequestTransformer orchUpdatedPriceRequestTransformer;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    OrchStaticDetailFactory orchStaticDetailFactory;

    @Autowired
    OrchTravellerReviewSummaryFactory orchTravellerReviewSummaryFactory;

    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
    private OrchDetailExecutor orchDetailExecutor;

    @Autowired
    private SearchRoomsFactory searchRoomsFactory;

    @Autowired
    private SearchSlotsFactory searchSlotsFactory;

    @Autowired
    private UpdatedPriceFactory updatedPriceFactory;

    @Autowired
    private OrchUpdatedPriceFactory orchUpdatedPriceFactory;

    public SearchRoomsResponse searchRooms(SearchRoomsRequest searchRoomsRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, CommonModifierResponse commonModifierResponse) throws ClientGatewayException {
        try {
            LOGGER.warn("Orchestrator search rooms v2 flow");
            long startTime = System.currentTimeMillis();

            if (searchRoomsRequest.getSearchCriteria() != null) {
                utility.setLoggingParametersToMDC(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates(), searchRoomsRequest.getSearchCriteria().getCheckIn(),
                        searchRoomsRequest.getSearchCriteria().getCheckOut());
            }
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_COMMON_REQUEST_PROCESS, DETAIL_SEARCH_ROOMS, System.currentTimeMillis() - startTime);


            DetailRequest detailRequest = buildSearchRoomsRequest(searchRoomsRequest, commonModifierResponse);

            HotelDetailsResponse response = orchDetailExecutor.searchRooms(detailRequest, parameterMap, httpHeaderMap);
            //RESPONSE TIME
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_RESPONSE_PROCESS, DETAIL_SEARCH_ROOMS, System.currentTimeMillis() - startTime);
            return searchRoomsFactory.getOrchResponseService(searchRoomsRequest.getClient()).convertSearchRoomsResponse(searchRoomsRequest, response, searchRoomsRequest.getExpData(),
                    searchRoomsRequest.getSearchCriteria().getRoomStayCandidates(), searchRoomsRequest.getSearchCriteria(), searchRoomsRequest.getFilterCriteria(),
                    searchRoomsRequest.getExpVariantKeys(), searchRoomsRequest.getRequestDetails(), commonModifierResponse);

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                LOGGER.error("error occurred in searchRooms: {}", e.getMessage());
                LOGGER.debug("error occurred in searchRooms: {}", e.getMessage(), e);
            } else
                LOGGER.error("error occurred in searchRooms: {}", e.getMessage(), e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }

    }

    public com.mmt.hotels.clientgateway.response.CalendarAvailabilityResponse calendarAvailability(CalendarAvailabilityRequest calendarAvailabilityRequest, String correlationKey, Map<String, String[]> parameterMap,
                                                                                                   Map<String, String> headers, CommonModifierResponse commonModifierResponse) throws ClientGatewayException {
        try {
            LOGGER.warn("Orchestrator search rooms v2 flow");
            long startTime = System.currentTimeMillis();

            if (calendarAvailabilityRequest.getSearchCriteria() != null) {
                utility.setLoggingParametersToMDC(calendarAvailabilityRequest.getSearchCriteria().getRoomStayCandidates(), calendarAvailabilityRequest.getSearchCriteria().getCheckIn(),
                        calendarAvailabilityRequest.getSearchCriteria().getCheckOut());
            }
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_COMMON_REQUEST_PROCESS, DETAIL_SEARCH_ROOMS, System.currentTimeMillis() - startTime);


            DetailRequest detailRequest = buildCalendarAvailabilityRequest(calendarAvailabilityRequest, commonModifierResponse);

            ConsolidatedCalendarAvailabilityResponse response = orchDetailExecutor.getCalendarAvailability(detailRequest, correlationKey, parameterMap, headers);
            //RESPONSE TIME
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_RESPONSE_PROCESS, DETAIL_SEARCH_ROOMS, System.currentTimeMillis() - startTime);
            return searchRoomsFactory.getOrchResponseService(calendarAvailabilityRequest.getClient()).convertCalendarAvailabilityResponse(response, calendarAvailabilityRequest.getSearchCriteria().getCurrency());

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                LOGGER.error("error occurred in searchRooms: {}", e.getMessage());
                LOGGER.debug("error occurred in searchRooms: {}", e.getMessage(), e);
            } else
                LOGGER.error("error occurred in searchRooms: {}", e.getMessage(), e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public String alternateDatesPrice(PriceByHotelsRequestBody priceByHotelsRequestBody, Map<String, String[]> parameterMap, Map<String, String> headers, CommonModifierResponse commonModifierResponse) throws ClientGatewayException {
        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));

        DetailRequest detailRequest = orchAlternatePriceHelper.buildAlternatePriceRequest(priceByHotelsRequestBody, parameterMap, headers, commonModifierResponse);

        //alternateDatesPriceUrl = RestURLHelper.getDestinationUrl(alternateDatesPriceUrl);
        String relativeUrl = Utility.getcompleteURL(alternateDatesPriceUrl, parameterMap, priceByHotelsRequestBody.getCorrelationKey());
        String request = objectMapperUtil.getJsonFromObject(detailRequest, DependencyLayer.CLIENTGATEWAY);
        LOGGER.debug(request);
        LOGGER.debug(relativeUrl);

        return restConnectorUtil.performAlternateDatesPost(request, headerMap, relativeUrl);
    }

    public StaticDetailResponse staticDetails(com.mmt.hotels.clientgateway.request.StaticDetailRequest staticDetailRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, CommonModifierResponse commonModifierResponse) throws ClientGatewayException {
        StaticDetailResponse staticDetailResponse;
        try {
            long startTime = System.currentTimeMillis();

            if (staticDetailRequest.getSearchCriteria() != null) {
                utility.setLoggingParametersToMDC(staticDetailRequest.getSearchCriteria().getRoomStayCandidates(), staticDetailRequest.getSearchCriteria().getCheckIn(),
                        staticDetailRequest.getSearchCriteria().getCheckOut());
            }

            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_COMMON_REQUEST_PROCESS, DETAIL_STATIC_DETAIL, System.currentTimeMillis() - startTime);
            DetailRequest detailRequest = buildStaticDetailRequest(staticDetailRequest, commonModifierResponse);
            HotelStaticContentResponse hotelStaticContentResponse = orchDetailExecutor.staticDetails(detailRequest, parameterMap, httpHeaderMap);
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_RESPONSE_PROCESS, DETAIL_STATIC_DETAIL, System.currentTimeMillis() - startTime);
            staticDetailResponse = orchStaticDetailFactory.getResponseService(staticDetailRequest.getClient()).convertStaticDetailResponse(staticDetailRequest, hotelStaticContentResponse, commonModifierResponse);

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                LOGGER.error("error occurred in staticDetails: {}", e.getMessage());
                LOGGER.debug("error occurred in staticDetails: {}", e.getMessage(), e);
            } else {
                LOGGER.error("error occurred in staticDetails: {}", e.getMessage(), e);

            }
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
        return staticDetailResponse;
    }

    public UGCPlatformReviewSummaryDTO ugcSummary(PlatformUgcCategoryRequest request, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        UGCPlatformReviewSummaryDTO summaryResponse;
        try {
            long startTime = System.currentTimeMillis();

            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_COMMON_REQUEST_PROCESS, DETAIL_UGC_SUMMARY, System.currentTimeMillis() - startTime);
            DetailRequest detailRequest = buildTravellerSummaryRequest(request, httpHeaderMap);
            TravellerReviewSummary reviewSummary = orchDetailExecutor.travellerReviewSummary(detailRequest, parameterMap, httpHeaderMap);
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_RESPONSE_PROCESS, DETAIL_UGC_SUMMARY, System.currentTimeMillis() - startTime);
            summaryResponse = orchTravellerReviewSummaryFactory.getOrchSummaryResponseService(request.getClient()).convertSummaryResponse(reviewSummary);
        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                LOGGER.error("error occurred in travellerSummary orchestrator flow: {}", e.getMessage());
                LOGGER.debug("error occurred in travellerSummary orchestrator flow: {}", e.getMessage(), e);
            } else {
                LOGGER.error("error occurred in travellerSummary orchestrator flow: {}", e.getMessage(), e);

            }
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
        return summaryResponse;
    }

    public UgcReviewResponseData ugcReviews(UgcReviewRequest request, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        UgcReviewResponseData reviewResponseData;
        try {
            long startTime = System.currentTimeMillis();

            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_COMMON_REQUEST_PROCESS, DETAIL_UGC_REVIEWS, System.currentTimeMillis() - startTime);
            DetailRequest detailRequest = buildTravellerReviewRequest(request, httpHeaderMap);
            TravellerReviewResponse reviewResponse = orchDetailExecutor.travellerReviews(detailRequest, parameterMap, httpHeaderMap);
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_RESPONSE_PROCESS, DETAIL_UGC_REVIEWS, System.currentTimeMillis() - startTime);
            reviewResponseData = orchTravellerReviewSummaryFactory.getOrchReviewsResponseService(request.getClient()).convertReviewsResponse(reviewResponse);
        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                LOGGER.error("error occurred in travellerReviews orchestrator flow: {}", e.getMessage());
                LOGGER.debug("error occurred in travellerReviews orchestrator flow: {}", e.getMessage(), e);
            } else {
                LOGGER.error("error occurred in travellerReviews orchestrator flow: {}", e.getMessage(), e);

            }
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            return null;
        }
        return reviewResponseData;
    }

    public com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse updatePrice(UpdatePriceRequest updatePriceRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, CommonModifierResponse commonModifierResponse) throws ClientGatewayException {
        try {
            long startTime = System.currentTimeMillis();

            LOGGER.info("Processing updatePrice request through orchestrator flow");

            // Build orchestrator-specific request for update price
            DetailRequest detailRequest = orchUpdatedPriceRequestTransformer.buildUpdatePriceRequest(updatePriceRequest, commonModifierResponse);

            // Execute orchestrator call for update price
            UpdatePriceResponse response = orchDetailExecutor.updatePrice(detailRequest, parameterMap, httpHeaderMap);

            //RESPONSE TIME
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_RESPONSE_PROCESS, "detail_update_price", System.currentTimeMillis() - startTime);

            // Use the orchestrator factory to convert the response
            return orchUpdatedPriceFactory.getOrchResponseService(updatePriceRequest.getClient()).convertUpdatePriceResponse(updatePriceRequest, response, updatePriceRequest.getExpData(), updatePriceRequest.getRequestDetails(), commonModifierResponse);

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                LOGGER.error("error occurred in updatePrice orchestrator flow: {}", e.getMessage());
                LOGGER.debug("error occurred in updatePrice orchestrator flow: {}", e.getMessage(), e);
            } else
                LOGGER.error("error occurred in updatePrice orchestrator flow: {}", e.getMessage(), e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public DetailRequest buildStaticDetailRequest(com.mmt.hotels.clientgateway.request.StaticDetailRequest staticDetailRequest, com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse commonModifierResponse) {
        if (staticDetailRequest == null) {
            LOGGER.error("StaticDetailRequest is null");
            throw new IllegalArgumentException("StaticDetailRequest cannot be null");
        }

        if (commonModifierResponse == null) {
            LOGGER.error("CommonModifierResponse is null");
            throw new IllegalArgumentException("CommonModifierResponse cannot be null");
        }

        DetailRequest detailRequest = new DetailRequest();

        // Extract search criteria safely
        StaticDetailCriteria searchCriteria = Optional.ofNullable(staticDetailRequest.getSearchCriteria())
                .orElseThrow(() -> new IllegalArgumentException("StaticDetailCriteria cannot be null"));

        // 1. Set hotelId and basic search criteria
        detailRequest.setHotelId(searchCriteria.getHotelId());
        detailRequest.setPrimaryHotelIds(searchCriteria.getComparatorHotelIds());
        detailRequest.setCheckIn(searchCriteria.getCheckIn());
        detailRequest.setCheckOut(searchCriteria.getCheckOut());
        detailRequest.setUuids(staticDetailRequest.getUuids());

        // 2. Build location details - using specific criteria
        detailRequest.setLocation(buildLocationDetails(searchCriteria));

        // 3. Build room criteria (Room Stay Candidates) - using specific data
        detailRequest.setRooms(buildRoomDetails(searchCriteria.getRoomStayCandidates(), staticDetailRequest.getExpDataMap()));

        // 4. Set slotDetails - using specific method
        detailRequest.setSlotDetails(buildSlotDetails(searchCriteria.getSlot()));

        // 5. Build client details - using specific request data
        detailRequest.setClientDetails(buildClientDetails(staticDetailRequest.getRequestDetails(), staticDetailRequest.getDeviceDetails(),
                staticDetailRequest.getFeatureFlags(), staticDetailRequest.getFilterCriteria(), searchCriteria, staticDetailRequest.getExpDataMap(), staticDetailRequest.getRequiredApis(), commonModifierResponse));

        // 6. Build filters - using specific criteria
        detailRequest.setFilters(buildFilterDetails(staticDetailRequest.getFilterCriteria(), staticDetailRequest.getMatchMakerDetails()));

        // 7. Build image details - using specific details
        detailRequest.setImageDetails(buildImageDetails(staticDetailRequest.getImageDetails()));

        detailRequest.setExperimentData(commonModifierResponse.getExpData());
        detailRequest.setExpVariantKeys(staticDetailRequest.getExpVariantKeys());
        detailRequest.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
        detailRequest.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());

        detailRequest.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());
        detailRequest.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());

        LOGGER.info("Successfully built StaticDetailRequest for hotelId: {}", searchCriteria.getHotelId());
        return detailRequest;
    }


    public DetailRequest buildSearchRoomsRequest(SearchRoomsRequest cgRequest, CommonModifierResponse commonModifierResponse) {
        if (cgRequest == null) {
            LOGGER.error("ListingSearchRequest is null");
            throw new IllegalArgumentException("ListingSearchRequest cannot be null");
        }

        if (commonModifierResponse == null) {
            LOGGER.error("CommonModifierResponse is null");
            throw new IllegalArgumentException("CommonModifierResponse cannot be null");
        }

        DetailRequest orchRequest = new DetailRequest();

        // Extract search criteria safely
        SearchRoomsCriteria searchCriteria = Optional.ofNullable(cgRequest.getSearchCriteria())
                .orElseThrow(() -> new IllegalArgumentException("SearchRoomsCriteria cannot be null"));

        // Build location details using specific criteria
        orchRequest.setHotelId(searchCriteria.getHotelId());
        orchRequest.setLocation(buildLocationDetails(searchCriteria));

        // Set check-in, check-out, and other parameters
        orchRequest.setCheckIn(searchCriteria.getCheckIn());
        orchRequest.setCheckOut(searchCriteria.getCheckOut());

        orchRequest.setSlotDetails(buildSlotDetails(searchCriteria.getSlot()));

        // Build room criteria (Room Stay Candidates) using specific data
        orchRequest.setRooms(buildRoomDetails(searchCriteria.getRoomStayCandidates(), cgRequest.getExpDataMap()));

        // Build dummy required api section
        RequiredApis requiredApis = buildRequiredApiMap(cgRequest.getDeviceDetails());

        // Build client details using specific request data
        orchRequest.setClientDetails(buildClientDetails(cgRequest.getRequestDetails(), cgRequest.getDeviceDetails(),
                cgRequest.getFeatureFlags(), cgRequest.getFilterCriteria(), searchCriteria, cgRequest.getExpDataMap(), requiredApis, commonModifierResponse));

        // Build image details using specific details
        orchRequest.setImageDetails(buildImageDetails(cgRequest.getImageDetails()));

        // Set experiment data and additional fields
        orchRequest.setExperimentData(cgRequest.getExpData());
        orchRequest.setExpVariantKeys(cgRequest.getExpVariantKeys());
        orchRequest.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
        orchRequest.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());
        orchRequest.setUserSearchType(searchCriteria.getUserSearchType());

        //orchRequest.setValidExpList(cgRequest.getValidExpList());
        orchRequest.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
        orchRequest.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());
        LOGGER.info("Successfully built ListingRequest");
        return orchRequest;
    }

    public DetailRequest buildCalendarAvailabilityRequest(SearchRoomsRequest cgRequest, CommonModifierResponse commonModifierResponse) {
        if (cgRequest == null) {
            LOGGER.error("ListingSearchRequest is null");
            throw new IllegalArgumentException("ListingSearchRequest cannot be null");
        }

        DetailRequest orchRequest = new DetailRequest();

        // Extract search criteria safely
        SearchRoomsCriteria searchCriteria = Optional.ofNullable(cgRequest.getSearchCriteria())
                .orElseThrow(() -> new IllegalArgumentException("SearchRoomsCriteria cannot be null"));

        // Build location details using specific criteria
        orchRequest.setHotelId(searchCriteria.getHotelId());
        orchRequest.setLocation(buildLocationDetails(searchCriteria));

        // Set check-in, check-out, and other parameters
        orchRequest.setCheckIn(searchCriteria.getCheckIn());
        orchRequest.setCheckOut(searchCriteria.getCheckOut());

        orchRequest.setSlotDetails(buildSlotDetails(searchCriteria.getSlot()));

        // Build room criteria (Room Stay Candidates) using specific data
        orchRequest.setRooms(buildRoomDetails(searchCriteria.getRoomStayCandidates(), cgRequest.getExpDataMap()));

        // Build dummy required api section
        RequiredApis requiredApis = buildRequiredApiMap(cgRequest.getDeviceDetails());

        // Build client details using specific request data
        orchRequest.setClientDetails(buildClientDetails(cgRequest.getRequestDetails(), cgRequest.getDeviceDetails(),
                cgRequest.getFeatureFlags(), cgRequest.getFilterCriteria(), searchCriteria, cgRequest.getExpDataMap(), requiredApis, commonModifierResponse));

        // Build image details using specific details
        orchRequest.setImageDetails(buildImageDetails(cgRequest.getImageDetails()));

        // Set experiment data and additional fields
        orchRequest.setExperimentData(cgRequest.getExpData());
        orchRequest.setExpVariantKeys(cgRequest.getExpVariantKeys());
//        orchRequest.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
//        orchRequest.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());
        orchRequest.setUserSearchType(searchCriteria.getUserSearchType());
        orchRequest.setSourceApi("calendar");
        //orchRequest.setValidExpList(cgRequest.getValidExpList());
//        orchRequest.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
//        orchRequest.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());
        LOGGER.info("Successfully built ListingRequest");
        return orchRequest;
    }


    private RequiredApis buildRequiredApiMap(DeviceDetails deviceDetails) {
        RequiredApis requiredApis = new RequiredApis();
        requiredApis.setReviewSummaryRequired(true);
        requiredApis.setRoomInfoRequired(true);
        requiredApis.setPlacesRequired(true);
        requiredApis.setMyPartnerHeroLoyalty(true);
        return requiredApis;
    }

    private com.gommt.hotels.orchestrator.detail.model.state.ImageDetails buildImageDetails(com.mmt.hotels.clientgateway.request.ImageDetails imageDetailsRequest) {
        com.gommt.hotels.orchestrator.detail.model.state.ImageDetails imageDetails = new com.gommt.hotels.orchestrator.detail.model.state.ImageDetails();

        if (imageDetailsRequest == null) {
            return imageDetails;
        }

        // Safely retrieve and set categories, defaulting to an empty list if null
        List<ImageCategory> categories = Optional.ofNullable(imageDetailsRequest.getCategories())
                .orElse(Collections.emptyList());
        imageDetails.setCategories(buildImageCategory(categories));

        // Safely set types, defaulting to null if null (matching OrchListingService line 773)
        List<String> types = Optional.ofNullable(imageDetailsRequest.getTypes()).orElse(null);
        imageDetails.setTypes(types);

        return imageDetails;
    }

    private List<com.gommt.hotels.orchestrator.detail.model.objects.ImageCategory> buildImageCategory(List<ImageCategory> inputCategories) {
        // CRITICAL BUG FIX: Initialize list outside the loop (matching OrchListingService lines 776-787)
        List<com.gommt.hotels.orchestrator.detail.model.objects.ImageCategory> imageCategoryList = new ArrayList<>();
        for (ImageCategory imageCategory : inputCategories) {
            com.gommt.hotels.orchestrator.detail.model.objects.ImageCategory result = new com.gommt.hotels.orchestrator.detail.model.objects.ImageCategory();
            result.setType(imageCategory.getType());
            result.setCount(imageCategory.getCount());
            result.setHeight(imageCategory.getHeight());
            result.setImageFormat(imageCategory.getImageFormat());
            result.setWidth(imageCategory.getWidth());
            imageCategoryList.add(result);
        }
        return imageCategoryList;
    }

    private List<FilterDetails> buildFilterDetails(List<Filter> filterCriteriaList, com.mmt.hotels.model.request.matchmaker.MatchMakerRequest matchMakerDetails) {
        if (filterCriteriaList == null) {
            filterCriteriaList = Collections.emptyList();
        }

        Map<String, FilterDetails> filterMap = new HashMap<>();

        // Iterate through each filter criterion and build filter details
        filterCriteriaList.forEach(filterCriteria -> {
            if (filterCriteria != null) {
                String key = filterCriteria.getFilterGroup().name();
                if (EXACT_ROOM_RECOMMENDATION.equalsIgnoreCase(key) || (BEDROOM_COUNT.equalsIgnoreCase(key))) {
                    return;
                }
                FilterDetails filterDetails;
                if (filterMap.containsKey(key)) {
                    filterDetails = filterMap.get(key);
                    updateFilterDetails(filterCriteria, filterDetails);
                } else {
                    filterDetails = initializeFilterDetails(filterCriteria);
                    filterMap.put(key, filterDetails);
                }
            }
        });

        if (matchMakerDetails != null) {
            updateAppliedFilterMapDptCollections(filterMap, matchMakerDetails);
        }

        List<FilterDetails> filterDetailsList = new ArrayList<>(filterMap.values());
        LOGGER.info("Successfully built FilterDetails for {} filters.", filterDetailsList.size());
        return filterDetailsList;
    }


    public void updateAppliedFilterMapDptCollections(Map<String, FilterDetails> appliedFilterMapCB,
                                                     com.mmt.hotels.model.request.matchmaker.MatchMakerRequest matchMakerDetails) {

        if (MapUtils.isNotEmpty(appliedFilterMapCB)) {
            boolean dptFilterConfigFromDpt = false;
            if (appliedFilterMapCB.containsKey(com.mmt.hotels.filter.FilterGroup.DPT_COLLECTIONS.name())) {
                FilterDetails collectionFilterValue = appliedFilterMapCB.get(com.mmt.hotels.filter.FilterGroup.DPT_COLLECTIONS.name());
                for (String filterValue : collectionFilterValue.getValues()) {
                    if (filterValue.contains(FILTER_GROUP_SPLITTER_DPT)) {
                        dptFilterConfigFromDpt = true;
                        String[] collectionFilterMap = filterValue.split(FILTER_GROUP_SPLITTER_DPT);
                        if (collectionFilterMap.length > 0) {
                            for (int i = 1; i < collectionFilterMap.length; i++) {
                                String[] filters = collectionFilterMap[i].split(FILTER_GROUP_VALUE_SPLITTER_DPT);
                                FilterDetails dptFilters = new FilterDetails();
                                dptFilters.setValues(new HashSet<>());
                                if (filters.length > 0 && !MATCHMAKER_AREA.equalsIgnoreCase(filters[0]) && !MATCHMAKER_POI.equalsIgnoreCase(filters[0])) {
                                    updateAppliedFilterMapForNonLocationDptFilters(appliedFilterMapCB, filters, dptFilters);
                                } else {
                                    updateAppliedFilterMapForLocationDptFilters(matchMakerDetails, filters);
                                }

                            }
                        }
                    }
                }
                if (dptFilterConfigFromDpt) {
                    appliedFilterMapCB.remove(com.mmt.hotels.filter.FilterGroup.DPT_COLLECTIONS.name());
                }

            }
            if (appliedFilterMapCB.containsKey(FilterGroup.DPT_PROP_COLLECTIONS.name())) {
                FilterDetails collectionFilterValue = appliedFilterMapCB.get(FilterGroup.DPT_PROP_COLLECTIONS.name());
                FilterDetails appliedPropFilters = new FilterDetails();
                appliedPropFilters.setValues(new HashSet<>());
                for (String filter : collectionFilterValue.getValues()) {
                    if (StringUtils.isNotEmpty(filter) && filter.contains(FILTER_GROUP_SPLITTER_DPT)) {
                        dptFilterConfigFromDpt = true;
                        String[] filterValue = filter.split(FILTER_GROUP_SPLITTER_DPT);
                        if (filterValue.length > 1) {
                            appliedPropFilters.getValues().add(filterValue[1]);
                        }
                    }
                }
                if (dptFilterConfigFromDpt) {
                    appliedFilterMapCB.remove(FilterGroup.DPT_PROP_COLLECTIONS.name());
                }
                appliedFilterMapCB.putIfAbsent(FilterGroup.DPT_PROP_COLLECTIONS.name(), appliedPropFilters);
            }
        }
    }

    private void updateAppliedFilterMapForNonLocationDptFilters(Map<String, FilterDetails> appliedFilterMapCB, String[] filters, FilterDetails dptFilters) {
        if (filters != null && filters.length > 1) {
            String[] filterValueList = filters[1].split(FILTER_VALUE_SPLITTER_DPT);
            FilterGroup filterGroup = FilterGroup.getFilterGroupFromFilterName(filters[0]);
            for (String s : filterValueList) {
                dptFilters.setGroup(filterGroup.name());
                if (FilterGroup.HOTEL_PRICE_BUCKET.name().equalsIgnoreCase(filters[0])) {
                    String[] priceMinMax = s.split(Constants.HYPEN);
                    if (priceMinMax.length == 2) {
                        RangeDetails rangeDetails = new RangeDetails();
                        rangeDetails.setMax(Integer.parseInt(priceMinMax[1]));
                        rangeDetails.setMin(Integer.parseInt(priceMinMax[0]));
                        if (dptFilters.getRange() == null) {
                            dptFilters.setRange(new ArrayList<>());
                        }
                        dptFilters.getRange().add(rangeDetails);
                    }
                } else {
                    dptFilters.getValues().add(s);
                }
            }
            if (filterGroup != null && appliedFilterMapCB.containsKey(filterGroup.name())) {
                if (FilterGroup.HOTEL_PRICE_BUCKET.name().equalsIgnoreCase(filterGroup.name())) {
                    appliedFilterMapCB.get(filterGroup.name()).getRange().addAll(dptFilters.getRange());
                } else {
                    appliedFilterMapCB.get(filterGroup.name()).getValues().addAll(dptFilters.getValues());
                }
            } else if (filterGroup != null) {
                appliedFilterMapCB.putIfAbsent(filterGroup.name(), dptFilters);
            }
        }
    }

    // Method to initialize a new FilterDetails object
    private FilterDetails initializeFilterDetails(Filter filterCriteria) {
        FilterDetails filterDetails = new FilterDetails();
        filterDetails.setGroup(filterCriteria.getFilterGroup().name());

        Set<String> filterValues = new HashSet<>();
        Optional.ofNullable(filterCriteria.getFilterValue()).ifPresent(filterValues::add);
        filterDetails.setValues(filterValues);

        if (filterCriteria.getFilterRange() != null) {
            RangeDetails rangeDetails = RangeDetails.builder()
                    .min(Optional.ofNullable(filterCriteria.getFilterRange().getMinValue()).orElse(0))
                    .max(Optional.ofNullable(filterCriteria.getFilterRange().getMaxValue()).orElse(0))
                    .build();
            filterDetails.setRange(new ArrayList<>());
            filterDetails.getRange().add(rangeDetails);
        } else {
            filterDetails.setRange(new ArrayList<>());
        }
        return filterDetails;
    }

    // Method to update an existing FilterDetails object
    private void updateFilterDetails(Filter filterCriteria, FilterDetails filterDetails) {
        Set<String> filterValues = filterDetails.getValues();
        Optional.ofNullable(filterCriteria.getFilterValue()).ifPresent(filterValues::add);
        filterDetails.setValues(filterValues);

        if (filterCriteria.getFilterRange() != null) {
            RangeDetails rangeDetails = RangeDetails.builder()
                    .min(Optional.ofNullable(filterCriteria.getFilterRange().getMinValue()).orElse(0))
                    .max(Optional.ofNullable(filterCriteria.getFilterRange().getMaxValue()).orElse(0))
                    .build();
            if (CollectionUtils.isEmpty(filterDetails.getRange())) {
                filterDetails.setRange(new ArrayList<>());
            }
            filterDetails.getRange().add(rangeDetails);
        }
    }


    private ClientDetails buildClientDetails(RequestDetails requestDetails,
                                             DeviceDetails deviceDetails, FeatureFlags featureFlags, List<Filter> filterCriteria, SearchCriteria searchCriteria,
                                             Map<String, String> expDataMap, RequiredApis requiredApis, CommonModifierResponse commonModifierResponse) {
        ClientDetails clientDetails = new ClientDetails();
        clientDetails.setFeatureFlags(buildFeatureFlags(requestDetails, deviceDetails, requiredApis, featureFlags, filterCriteria, expDataMap, commonModifierResponse));
        clientDetails.setRequestDetails(buildRequestDetails(requestDetails, deviceDetails, searchCriteria, featureFlags, commonModifierResponse));
        clientDetails.setUserDetails(buildUserDetails(requestDetails, commonModifierResponse));
        clientDetails.setTripType(searchCriteria.getTripType());
        String visitorId = Optional.ofNullable(requestDetails)
                .map(com.mmt.hotels.clientgateway.request.RequestDetails::getVisitorId)
                .orElse("");

        clientDetails.setVisitorId(visitorId);
        clientDetails.setMcId(commonModifierResponse.getMcId());
        clientDetails.setChatbotDetails(buildChatbotDetails(requestDetails));
        return clientDetails;
    }

    private ChatbotDetails buildChatbotDetails(com.mmt.hotels.clientgateway.request.RequestDetails requestDetails) {
        if (requestDetails == null || StringUtils.isEmpty(requestDetails.getMyraMsgId())) {
            return null;
        }
        ChatbotDetails chatbotDetails = new ChatbotDetails();
        chatbotDetails.setMyraMsgId(requestDetails.getMyraMsgId());
        return chatbotDetails;
    }

    private UserDetails buildUserDetails(com.mmt.hotels.clientgateway.request.RequestDetails requestDetails, CommonModifierResponse commonModifierResponse) {
        UserDetails userDetails = new UserDetails();

        // Safely set the location details using LocationDetails builder
        LocationDetails locationDetails = LocationDetails.builder()
                .cityName(Optional.ofNullable(commonModifierResponse.getUserLocation())
                        .map(UserLocation::getCity)
                        .orElse(""))  // Default to empty string if city is null
                .cityId(Optional.ofNullable(commonModifierResponse.getUserLocation())
                        .map(UserLocation::getCity)
                        .orElse(""))  // Default to empty string if cityId is null
                .countryId(Optional.ofNullable(commonModifierResponse.getUserLocation())
                        .map(UserLocation::getCountry)
                        .orElse(""))  // Default to empty string if countryId is null
                .stateId(Optional.ofNullable(commonModifierResponse.getUserLocation())
                        .map(UserLocation::getState)
                        .orElse(""))  // Default to empty string if stateId is null
                .build();
        userDetails.setLocation(locationDetails);

        // Safely set mmtAuth, uuid, profileType, and subProfileType with default values if null
        userDetails.setMmtAuth(Optional.ofNullable(commonModifierResponse.getMmtAuth()).orElse(""));
        userDetails.setUuid(Optional.ofNullable(commonModifierResponse.getExtendedUser()).map(ExtendedUser::getUuid).orElse(""));

        UserSessionData userSessionData = new UserSessionData();
        // FIXED BUSINESS LOGIC: Use getSessionId() instead of getSessionId() (matching OrchListingService line 1127)
        userSessionData.setSessionId(Optional.ofNullable(requestDetails)
                .map(com.mmt.hotels.clientgateway.request.RequestDetails::getSessionId)
                .orElse(""));

        userSessionData.setUuid(userDetails.getUuid());
        userDetails.setSessionData(userSessionData);

        // Safely map profileType and subProfileType, defaulting to UNKNOWN or empty if null
        userDetails.setProfileType(Optional.ofNullable(commonModifierResponse.getExtendedUser())
                .map(ExtendedUser::getProfileType)
                .map(ProfileType::valueOf)
                .orElse(null));  // Default to UNKNOWN if profileType is null

        userDetails.setSubProfileType(Optional.ofNullable(commonModifierResponse.getExtendedUser())
                .map(ExtendedUser::getAffiliateId)
                .map(SubProfileType::fromValue)
                .orElse(SubProfileType.DEFAULT));
        // Default to DEFAULT if subProfileType is null

        // Safely set loggedIn, defaulting to false if null
        userDetails.setLoggedIn(Optional.ofNullable(requestDetails)
                .map(com.mmt.hotels.clientgateway.request.RequestDetails::isLoggedIn)
                .orElse(false));

        // Safely set user segments
        List<String> userSegmentsList = new ArrayList<>(Optional.ofNullable(commonModifierResponse.getHydraResponse())
                .map(HydraResponse::getHydraMatchedSegment)
                .orElse(Collections.emptySet()));
        userDetails.setUserSegments(userSegmentsList);
        LOGGER.info("Successfully built UserDetails for UUID: {}", userDetails.getUuid());
        return userDetails;
    }

    private com.gommt.hotels.orchestrator.detail.model.state.RequestDetails buildRequestDetails(com.mmt.hotels.clientgateway.request.RequestDetails requestDetails, DeviceDetails deviceDetails, SearchCriteria searchCriteria, FeatureFlags featureFlags, CommonModifierResponse commonModifierResponse) {
        com.gommt.hotels.orchestrator.detail.model.state.RequestDetails orchRequestDetails = new com.gommt.hotels.orchestrator.detail.model.state.RequestDetails();

        // Safely retrieve BookingDevice from deviceDetails, defaulting to an empty device
        orchRequestDetails.setBookingDevice(buildBookingDevice(Optional.ofNullable(deviceDetails).orElse(new DeviceDetails())));

        // Set currency using original business logic from OrchListingService
        String currency = searchCriteria != null && StringUtils.isNotEmpty(searchCriteria.getCurrency()) ?
                searchCriteria.getCurrency().toUpperCase() : "INR";
        orchRequestDetails.setCurrency(Currency.valueOf(currency));

        // Set MultiCurrency info using original business logic
        orchRequestDetails.setMultiCurrencyInfo(buildMultiCurrencyInfo(searchCriteria != null ? searchCriteria.getMultiCurrencyInfo() : null));

        // Set UserGlobalInfo if available
        if (searchCriteria != null && searchCriteria.getUserGlobalInfo() != null) {
            orchRequestDetails.setUserGlobalInfo(buildUserGlobalInfo(searchCriteria.getUserGlobalInfo()));
        }

        // Set additional fields from the original business logic
        orchRequestDetails.setAffiliateId(Optional.ofNullable(commonModifierResponse.getAffiliateId()).orElse(""));
        orchRequestDetails.setApplicationId(String.valueOf(Optional.of(commonModifierResponse.getApplicationId()).orElse(0)));
        orchRequestDetails.setRequestType("B2CAgent"); // Hardcoded as per current implementation
        orchRequestDetails.setCdfContextId(Optional.ofNullable(commonModifierResponse.getCdfContextId()).orElse(""));
        orchRequestDetails.setReqContext(requestDetails != null && requestDetails.isPremium() ? Constants.PREMIUM : Constants.DEFAULT);

        // TODO: Set semantic search details if available - needs correct import resolution

        // Set B2B attributes and SEO fields using original business logic from OrchListingService
        orchRequestDetails.setB2bAttributes(Collections.emptyMap());

        // Set SEO cohort and template using original business logic from OrchListingService (lines 1018-1019)
        orchRequestDetails.setSeoCohort(Optional.ofNullable(featureFlags).map(FeatureFlags::getSeoCohort).orElse(""));
        orchRequestDetails.setSeoDescTemplate("defaultSeoDescTemplate");  // Hardcoded value as per original

        // MISSING BUSINESS LOGIC: Set ContentExpDataMap and ManthanExpDataMap (from OrchListingService lines 1012-1013)
        // Note: These were commented out in OrchListingService but are set in the method signature
        // They should be set here to match the pattern used elsewhere in the method
        orchRequestDetails.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());
        orchRequestDetails.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
        requestDetails.setRequestId(StringUtils.isNotEmpty(Optional.ofNullable(requestDetails.getRequestId()).orElse("")) ? requestDetails.getRequestId() : UUID.randomUUID().toString());
        requestDetails.setJourneyId(Optional.ofNullable(requestDetails.getJourneyId()).orElse(""));
        Funnel funnel = Optional.ofNullable(requestDetails.getFunnelSource())
                .map(Funnel::fromValue)
                .orElse(Funnel.HOTELS);
        requestDetails.setFunnelSource(funnel.getName());  // Default to a "Hotels" value for Funnel

        // Set language from MDC
        Language language = Optional.ofNullable(MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue()))
                .map(String::toLowerCase)
                .map(Language::fromValue)
                .orElse(Language.ENGLISH);
        orchRequestDetails.setLanguage(language);

        // Set region from MDC
        Region region = Optional.ofNullable(MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()))
                .map(String::toLowerCase)
                .map(Region::fromValue)
                .orElse(Region.IN);
        orchRequestDetails.setRegion(region);

        // Set country using original business logic from OrchListingService (line 991)
        boolean dhCall = Constants.DOM_COUNTRY.equalsIgnoreCase(searchCriteria != null ? searchCriteria.getCountryCode() : "");
        orchRequestDetails.setCountry(!dhCall ? Country.IH : Country.DH);

        // Set the original traffic source
        orchRequestDetails.setOriginalTrafficSource(Optional.ofNullable(commonModifierResponse.getOriginalTrafficSource())
                .orElse(TrafficSource.DEFAULT.getName()));

        // Set application-specific fields
        orchRequestDetails.setRequestType("B2CAgent");
        orchRequestDetails.setApplicationId(String.valueOf(Optional.of(commonModifierResponse.getApplicationId()).orElse(0)));
        orchRequestDetails.setAffiliateId(Optional.ofNullable(commonModifierResponse.getAffiliateId()).orElse(""));
        orchRequestDetails.setCdfContextId(Optional.ofNullable(commonModifierResponse.getCdfContextId()).orElse(""));

        // Set additional maps
        orchRequestDetails.setSessionData(Collections.emptyMap());
        orchRequestDetails.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
        orchRequestDetails.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());

        // Basic request details

        orchRequestDetails.setRequestor(requestDetails.getRequestor());
        orchRequestDetails.setRequestId(Optional.ofNullable(requestDetails.getRequestId()).orElse(UUID.randomUUID().toString()));
        orchRequestDetails.setJourneyId(Optional.ofNullable(requestDetails.getJourneyId()).orElse(""));
        orchRequestDetails.setVisitorId(Optional.ofNullable(requestDetails.getVisitorId()).orElse(""));
        orchRequestDetails.setChannel(Optional.ofNullable(requestDetails.getChannel()).orElse(""));
        orchRequestDetails.setSessionId(Optional.ofNullable(requestDetails.getSessionId()).orElse(""));

        // Set funnel source
        orchRequestDetails.setFunnelSource(Optional.ofNullable(requestDetails.getFunnelSource())
                .map(Funnel::fromValue)
                .orElse(Funnel.HOTELS));

        // Set page context
        orchRequestDetails.setPageContext(Optional.ofNullable(requestDetails.getPageContext())
                .map(PageContext::fromValue)
                .orElse(PageContext.LISTING));

        // Set brand
        orchRequestDetails.setBrand(Optional.ofNullable(requestDetails.getBrand())
                .map(Brand::valueOf)
                .orElse(Brand.MMT));

        // Set ID context
        orchRequestDetails.setIdContext(Optional.ofNullable(requestDetails.getIdContext())
                .map(IdContext::valueOf)
                .orElse(IdContext.B2C));

        // Set site domain
        orchRequestDetails.setSiteDomain(Optional.ofNullable(requestDetails.getSiteDomain())
                .map(String::toUpperCase)
                .map(SiteDomain::valueOf)
                .orElse(SiteDomain.IN));

        // Set traffic source and type
        if (requestDetails.getTrafficSource() != null) {
            orchRequestDetails.setTrafficSource(requestDetails.getTrafficSource().getSource());
            orchRequestDetails.setTrafficFlowType(requestDetails.getTrafficSource().getFlowType());
        }

        // Traffic Source and Type - Safely set from requestDetails
        if (StringUtils.isNotEmpty(requestDetails.getSiteDomain()) && Utility.isRegionGccOrKsa(requestDetails.getSiteDomain())) {
            orchRequestDetails.setTrafficType(TrafficType.GCC);
        } else if (utility.isMyPartner(commonModifierResponse)) {
            orchRequestDetails.setTrafficType(TrafficType.MYPARTNER);
        } else if (CORP_ID_CONTEXT.equalsIgnoreCase(requestDetails.getIdContext())) {
            orchRequestDetails.setTrafficType(TrafficType.B2B);
        } else {
            orchRequestDetails.setTrafficType(Optional.ofNullable(requestDetails.getTrafficSource())
                    .map(com.mmt.hotels.clientgateway.request.TrafficSource::getType)
                    .map(TrafficType::fromValue)
                    .orElse(TrafficType.B2C));  // Default to B2C if not present
        }

        // Set semantic search details if available
        if (requestDetails.getSemanticSearchDetails() != null) {
            com.gommt.hotels.orchestrator.detail.model.request.matchmaker.SemanticSearchDetails semanticSearchDetails =
                    new com.gommt.hotels.orchestrator.detail.model.request.matchmaker.SemanticSearchDetails();
            semanticSearchDetails.setQueryText(requestDetails.getSemanticSearchDetails().getQueryText());
            semanticSearchDetails.setSemanticData(requestDetails.getSemanticSearchDetails().getSemanticData());
            orchRequestDetails.setSemanticSearchDetails(semanticSearchDetails);
        }

        // Set request context
        orchRequestDetails.setReqContext(requestDetails.isPremium() ? Constants.PREMIUM : Constants.DEFAULT);


        LOGGER.info("Successfully built RequestDetails with requestId: {}", orchRequestDetails.getRequestId());
        return orchRequestDetails;
    }

    public static com.gommt.hotels.orchestrator.detail.model.objects.UserGlobalInfo buildUserGlobalInfo(UserGlobalInfo userGlobalInfo) {
        com.gommt.hotels.orchestrator.detail.model.objects.UserGlobalInfo userGlobalInfoOrch = new com.gommt.hotels.orchestrator.detail.model.objects.UserGlobalInfo();
        userGlobalInfoOrch.setUserCountry(userGlobalInfo.getUserCountry());
        userGlobalInfoOrch.setEntityName(userGlobalInfo.getEntityName());
        return userGlobalInfoOrch;
    }

    private MultiCurrencyInfo buildMultiCurrencyInfo(com.mmt.hotels.clientgateway.request.MultiCurrencyInfo multiCurrencyInfo) {
        if (multiCurrencyInfo == null)
            return null;
        return MultiCurrencyInfo.builder()
                .userCurrency(multiCurrencyInfo.getUserCurrency())
                .regionCurrency(multiCurrencyInfo.getRegionCurrency())
                .build();
    }

    private BookingDevice buildBookingDevice(DeviceDetails deviceDetails) {
        if (deviceDetails == null) {
            LOGGER.warn("DeviceDetails is null, returning an empty BookingDevice.");
            return BookingDevice.builder().build();  // Return an empty BookingDevice if deviceDetails is null
        }

        BookingDevice.BookingDeviceBuilder bookingDeviceBuilder = BookingDevice.builder();

        // Safely set device fields, defaulting to empty strings or enums if values are null
        bookingDeviceBuilder.deviceId(Optional.ofNullable(deviceDetails.getDeviceId()).orElse(""));
        bookingDeviceBuilder.deviceName(Optional.ofNullable(deviceDetails.getDeviceName()).orElse(""));
        bookingDeviceBuilder.deviceType(Optional.ofNullable(deviceDetails.getBookingDevice()).map(DeviceType::fromValue).orElse(DeviceType.DESKTOP));
        bookingDeviceBuilder.appVersion(Optional.ofNullable(deviceDetails.getAppVersion()).orElse(""));
        bookingDeviceBuilder.networkType(Optional.ofNullable(deviceDetails.getNetworkType()).orElse("").toUpperCase());
        LOGGER.info("Successfully built BookingDevice with deviceId: {}", deviceDetails.getDeviceId());
        return bookingDeviceBuilder.build();
    }

    private com.gommt.hotels.orchestrator.detail.model.state.FeatureFlags buildFeatureFlags(RequestDetails requestDetails, DeviceDetails deviceDetails,
                                                                                            RequiredApis requiredApis, FeatureFlags featureFlags, List<Filter> filterCriteria, Map<String, String> expDataMap,
                                                                                            CommonModifierResponse commonModifierResponse) {
        final FeatureFlags inputFeatureFlags = Optional.ofNullable(featureFlags).orElse(new FeatureFlags());
        com.gommt.hotels.orchestrator.detail.model.state.FeatureFlags result = new com.gommt.hotels.orchestrator.detail.model.state.FeatureFlags();

        // Safely set FlightBooker, defaulting to false
        result.setFlightBooker(Optional.ofNullable(commonModifierResponse.getHydraResponse())
                .map(HydraResponse::isFlightBooker)
                .orElse(false));
        result.setRoomLevelDetails(true);

        // Safely set WalletRequired, defaulting to false
        result.setWalletRequired(Optional.of(inputFeatureFlags).map(FeatureFlags::isWalletRequired).orElse(false));

        // Safely set CityTaxExclusive based on condition
        result.setCityTaxExclusive(Optional.of(commonModifierResponse).map(CommonModifierResponse::isCityTaxExclusive).orElse(false));

        // Override CityTaxExclusive if CORP_ID_CONTEXT matches
        result.setCityTaxExclusive(!CORP_ID_CONTEXT.equalsIgnoreCase(
                Optional.ofNullable(requestDetails)
                        .map(com.mmt.hotels.clientgateway.request.RequestDetails::getIdContext)
                        .orElse("")) && result.isCityTaxExclusive());

        // Safely set BestCoupon, defaulting to false
        result.setBestCoupon(Optional.of(inputFeatureFlags).map(FeatureFlags::isCoupon).orElse(false));

        // Safely set ComparatorHotelRequest, defaulting to false
        result.setComparatorHotelRequest(Optional.of(inputFeatureFlags).map(FeatureFlags::isComparator).orElse(false));

        // Check RoomPreferenceEnabled from applied filters, defaulting to false - using original business logic
        result.setRoomPreferenceEnabled(utility.checkIfFilterValueExistsInAppliedFilterMap(filterCriteria));

        // IMPLEMENTED BUSINESS LOGIC: EXACT_ROOM_VALUE logic (from OrchListingService lines 849-851)
        if (expDataMap != null && EXACT_ROOM_VALUE.equalsIgnoreCase(expDataMap.get("roomCountDefault"))) {
            result.setRoomPreferenceEnabled(!result.isRoomPreferenceEnabled());
        }

        result.setCollectionRequest(false);

        // Hardcoded value for AdvancedFiltering
        result.setAdvancedFiltering(Boolean.TRUE);

        result.setSeoDS(inputFeatureFlags.isSeoDS());

        // Safely set CheckAvailability, defaulting to false
        result.setCheckAvailability(Optional.of(inputFeatureFlags).map(FeatureFlags::isCheckAvailability).orElse(false));

        // Safely set HomeStayV2Flow, defaulting to false
        result.setHomeStayV2Flow(Optional.of(commonModifierResponse).map(CommonModifierResponse::isHomestayV2Flow).orElse(false));

        // Safely set Orientation, defaulting to null if not present
        result.setOrientation(Optional.of(inputFeatureFlags).map(FeatureFlags::getOrientation).orElse(null));
        result.setDayUsePersuasion(Optional.of(inputFeatureFlags).map(FeatureFlags::isDayUsePersuasion).orElse(false));

        // Safely set SimilarHotels, defaulting to false
        result.setSimilarHotels(Optional.of(inputFeatureFlags).map(FeatureFlags::isSimilarHotel).orElse(false));
        result.setMaskedPropertyName(Optional.of(inputFeatureFlags).map(FeatureFlags::getMaskedPropertyName).orElse(false));
        result.setPropSearch(Optional.of(inputFeatureFlags).map(FeatureFlags::isPropSearch).orElse(false));
        result.setRequiredSections(buildRequiredSections(requiredApis));

        // TODO: These feature flags were not found on CG, defaulting to false
        result.setAllSoldOutRequired(false);
        result.setSoldOutInfoRequired(false);
        result.setListingMapShortStays(false);
        result.setBookingModification(false);
        result.setAddHCPToHotelDiscount(false);
        result.setEnableSabre(false);
        result.setBnplExtended(false);
        result.setBlockEmi(false);
        result.setSoldOut(false);
        result.setScarcityFlow(false);
        result.setUnmodifiedAmenities(false);
        result.setSameCityFlightItineraryAvailable(false);
        result.setLocus(false);
        result.setBlackUser(false);
        result.setMmtPrime(false);
        result.setDoubleBlackUser(false);
        result.setLimitedFilterCall(false);
        result.setPremiumThemesCardRequired(inputFeatureFlags.isPremiumThemesCardRequired());
        result.setUpsellRateplanRequired(Optional.of(inputFeatureFlags).map(FeatureFlags::isUpsellRateplanRequired).orElse(false));
        LOGGER.info("Successfully built FeatureFlags.");
        return result;
    }

    private RequiredSections buildRequiredSections(RequiredApis requiredApis) {
        if (requiredApis == null) {
            return null;
        }
        RequiredSections requiredSections = new RequiredSections();
        requiredSections.setComparator(requiredApis.isComparatorV2Required());
        requiredSections.setPlaces(requiredApis.isPlacesRequired());
        requiredSections.setWeaver(requiredApis.isWeaverResponseRequired());
        requiredSections.setReviewSummary(requiredApis.isReviewSummaryRequired());
        requiredSections.setRoomInfo(requiredApis.isRoomInfoRequired());
        requiredSections.setMyPartnerHeroLoyalty(requiredApis.isMyPartnerHeroLoyalty());
        requiredSections.setPersonalizedCards(requiredApis.isDetailPersuasionCardsRequired());
        return requiredSections;
    }

    private List<com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails> buildRoomDetails(List<RoomStayCandidate> roomStayCandidates, Map<String, String> expDataMap) {
        if (roomStayCandidates == null || roomStayCandidates.isEmpty()) {
            LOGGER.warn("RoomStayCandidates is null or empty, returning an empty RoomDetails list.");
            return Collections.emptyList();
        }

        // Handle distribution logic for SearchRoomsRequest (when expDataMap is provided)
        if (expDataMap != null && utility.isDistributeRoomStayCandidates(roomStayCandidates, expDataMap)) {
            List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidatesHES = utility.buildRoomStayDistribution(roomStayCandidates, expDataMap);
            return roomStayCandidatesHES.stream().map(roomStayCandidate -> {
                com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails roomDetails = new com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails();
                int adultCount = 0;
                List<Integer> childrenAges = new ArrayList<>();
                for (com.mmt.hotels.model.request.GuestCount guestCount : roomStayCandidate.getGuestCounts()) {
                    adultCount = adultCount + Integer.parseInt(guestCount.getCount());
                    if (CollectionUtils.isNotEmpty(guestCount.getAges())) {
                        childrenAges.addAll(guestCount.getAges());
                    }
                }
                roomDetails.setAdults(adultCount);
                roomDetails.setChildrenAges(childrenAges);
                return roomDetails;
            }).collect(Collectors.toList());
        }

        List<com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails> roomDetailsList = new ArrayList<>();

        // Iterate over room stay candidates and build room details
        for (RoomStayCandidate roomStayCandidate : roomStayCandidates) {
            if (roomStayCandidate != null) {
                com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails roomDetails = new com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails();
                roomDetails.setAdults(roomStayCandidate.getAdultCount());
                roomDetails.setChildrenAges(roomStayCandidate.getChildAges());
                roomDetailsList.add(roomDetails);
            } else {
                LOGGER.warn("Encountered a null RoomStayCandidate, skipping.");
            }
        }

        LOGGER.info("Successfully built RoomDetails for {} rooms.", roomDetailsList.size());
        return roomDetailsList;
    }


    private com.gommt.hotels.orchestrator.detail.model.request.common.SlotDetails buildSlotDetails(com.mmt.hotels.clientgateway.request.dayuse.Slot slot) {
        com.gommt.hotels.orchestrator.detail.model.request.common.SlotDetails slotDetails = new
                com.gommt.hotels.orchestrator.detail.model.request.common.SlotDetails();
        if (slot == null) {
            return slotDetails;
        }
        slotDetails.setTimeSlot(slot.getTimeSlot() != null ? slot.getTimeSlot() : 0);
        slotDetails.setDuration(slot.getDuration() != null ? slot.getDuration() : 0);
        return slotDetails;
    }

    private LocationDetails buildLocationDetails(SearchCriteria searchCriteria) {
        if (searchCriteria == null) {
            LOGGER.warn("SearchCriteria is null while building LocationDetails, returning empty LocationDetails.");
            return new LocationDetails();
        }

        LocationDetails locationDetails = new LocationDetails();
        GeoLocationDetails geoLocationDetails = new GeoLocationDetails();

        // Use the base SearchCriteria fields directly since both SearchRoomsCriteria and StaticDetailCriteria extend it
        locationDetails.setId(Optional.ofNullable(searchCriteria.getLocationId()).orElse(""));
        locationDetails.setType(Optional.ofNullable(searchCriteria.getLocationType()).orElse(""));
        locationDetails.setCityId(Optional.ofNullable(searchCriteria.getCityCode()).orElse(""));
        locationDetails.setCityName(Optional.ofNullable(searchCriteria.getCityName()).orElse(""));
        locationDetails.setCountryId(Optional.ofNullable(searchCriteria.getCountryCode()).orElse(""));

        geoLocationDetails.setLatitude(searchCriteria.getLat() != null ? String.valueOf(searchCriteria.getLat()) : "");
        geoLocationDetails.setLongitude(searchCriteria.getLng() != null ? String.valueOf(searchCriteria.getLng()) : "");

        locationDetails.setGeo(geoLocationDetails);
        return locationDetails;
    }

    public DayUseRoomsResponse searchSlots(DayUseRoomsRequest dayUseRoomsRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, CommonModifierResponse commonModifierResponse) throws ClientGatewayException {
        try {
            LOGGER.warn("Orchestrator search rooms v2 flow");
            long startTime = System.currentTimeMillis();

            if (dayUseRoomsRequest.getSearchCriteria() != null) {
                utility.setLoggingParametersToMDC(dayUseRoomsRequest.getSearchCriteria().getRoomStayCandidates(), dayUseRoomsRequest.getSearchCriteria().getCheckIn(),
                        dayUseRoomsRequest.getSearchCriteria().getCheckOut());
            }
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_COMMON_REQUEST_PROCESS, DETAIL_SEARCH_ROOMS, System.currentTimeMillis() - startTime);
            DetailRequest detailRequest = buildSearchSlotsRequest(dayUseRoomsRequest, commonModifierResponse);
            HotelDetailsResponse response = orchDetailExecutor.searchRooms(detailRequest, parameterMap, httpHeaderMap);
            //RESPONSE TIME
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_RESPONSE_PROCESS, DETAIL_SEARCH_ROOMS, System.currentTimeMillis() - startTime);
            return searchSlotsFactory.getOrchResponseService(dayUseRoomsRequest.getClient()).convertSearchSlotsResponse(dayUseRoomsRequest, response, commonModifierResponse);

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                LOGGER.error("error occurred in searchRooms: {}", e.getMessage());
                LOGGER.debug("error occurred in searchRooms: {}", e.getMessage(), e);
            } else
                LOGGER.error("error occurred in searchRooms: {}", e.getMessage(), e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }

    }

    public DetailRequest buildSearchSlotsRequest(DayUseRoomsRequest cgRequest, CommonModifierResponse commonModifierResponse) {
        if (cgRequest == null) {
            LOGGER.error("DayUseRoomsRequest is null");
            throw new IllegalArgumentException("DayUseRoomsRequest cannot be null");
        }

        if (commonModifierResponse == null) {
            LOGGER.error("CommonModifierResponse is null");
            throw new IllegalArgumentException("CommonModifierResponse cannot be null");
        }

        DetailRequest orchRequest = new DetailRequest();

        // Build location details - DayUseRoomsRequest has getSearchCriteria() method
        orchRequest.setHotelId(cgRequest.getSearchCriteria().getHotelId());
        orchRequest.setLocation(buildLocationDetails(cgRequest.getSearchCriteria()));

        // Extract search criteria safely
        SearchRoomsCriteria searchCriteria = Optional.ofNullable(cgRequest.getSearchCriteria())
                .orElseThrow(() -> new IllegalArgumentException("SearchRoomsCriteria cannot be null"));

        // Set check-in, check-out, and other parameters
        orchRequest.setCheckIn(searchCriteria.getCheckIn());
        orchRequest.setCheckOut(searchCriteria.getCheckOut());

        orchRequest.setSlotDetails(buildSlotDetails(searchCriteria.getSlot()));

        // Build room details - DayUseRoomsRequest has getSearchCriteria().getRoomStayCandidates()
        orchRequest.setRooms(buildRoomDetails(searchCriteria.getRoomStayCandidates(), cgRequest.getExpDataMap()));

        // Build dummy required api section
        RequiredApis requiredApis = buildRequiredApiMap(cgRequest.getDeviceDetails());

        // Build client details - DayUseRoomsRequest has getRequestDetails() method
        orchRequest.setClientDetails(buildClientDetails(cgRequest.getRequestDetails(), cgRequest.getDeviceDetails(),
                cgRequest.getFeatureFlags(), cgRequest.getFilterCriteria(), searchCriteria, cgRequest.getExpDataMap(), requiredApis, commonModifierResponse));

        // Build filter details - can reuse existing method
        orchRequest.setFilters(buildFilterDetails(cgRequest.getFilterCriteria(), cgRequest.getMatchMakerDetails()));

        // Build image details
        com.gommt.hotels.orchestrator.detail.model.state.ImageDetails imageDetails = buildImageDetails(cgRequest.getImageDetails());
        com.gommt.hotels.orchestrator.detail.model.objects.ImageCategory category = null;
        if (CollectionUtils.isNotEmpty(imageDetails.getCategories())) {
            category = imageDetails.getCategories().stream().filter(imageCategory -> "R".equalsIgnoreCase(imageCategory.getType())).findAny().orElse(null);
        } else {
            imageDetails.setCategories(new ArrayList<>());
        }
        if (category == null) {
            //As per legacy code, should be coming directly from client
            category = new com.gommt.hotels.orchestrator.detail.model.objects.ImageCategory();
            category.setType("R");
            category.setCount(100);
            imageDetails.getCategories().add(category);
        }
        orchRequest.setImageDetails(imageDetails);

        // Set experiment data and additional fields
        orchRequest.setExperimentData(cgRequest.getExpData());
        orchRequest.setExpVariantKeys(cgRequest.getExpVariantKeys());
        orchRequest.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
        orchRequest.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());

        LOGGER.info("Successfully built DetailRequest for searchSlots");
        return orchRequest;
    }

    private DetailRequest buildTravellerSummaryRequest(PlatformUgcCategoryRequest summaryRequest, Map<String, String> httpHeaderMap) {

        if (summaryRequest == null || StringUtils.isEmpty(summaryRequest.getHotelId())) {
            LOGGER.error("Ugc Summary request is null or HotelId is empty.");
            throw new IllegalArgumentException("Ugc Summary request or Hotel Id can't be null or empty");
        }

        DetailRequest detailRequest = new DetailRequest();

        // 1. Set hotelId
        detailRequest.setHotelId(summaryRequest.getHotelId());

        // 2. Build client details - using specific request data
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(MapUtils.isNotEmpty(httpHeaderMap) && httpHeaderMap.containsKey(OS) ? httpHeaderMap.get(OS) : "");
        detailRequest.setClientDetails(buildClientDetails(summaryRequest.getCorrelationKey(), deviceDetails, httpHeaderMap));

        if (StringUtils.isNotEmpty(summaryRequest.getTravelType())) {
            TravellerReviewsRequest travellerReviewsRequest = TravellerReviewsRequest.builder().cohort(Collections.singletonList(buildCohortData(summaryRequest.getTravelType(), new ArrayList<>()))).build();
            detailRequest.getClientDetails().getRequestDetails().setTravellerReviewsRequest(travellerReviewsRequest);
        }

        // 3. Set dummy values for NotEmpty/NonNullable fields
        detailRequest.setCheckIn(LocalDate.now().toString());
        detailRequest.setCheckOut(LocalDate.now().plusDays(1).toString());

        LOGGER.info("Successfully built TravellerSummaryRequest for hotelId: {}", summaryRequest.getHotelId());
        return detailRequest;
    }

    private DetailRequest buildTravellerReviewRequest(UgcReviewRequest reviewRequest, Map<String, String> httpHeaderMap) {

        if (reviewRequest == null || StringUtils.isEmpty(reviewRequest.getHotelId())) {
            LOGGER.error("Ugc review request is null or HotelId is empty.");
            throw new IllegalArgumentException("Ugc review request or Hotel Id can't be null or empty");
        }

        DetailRequest detailRequest = new DetailRequest();

        // 1. Set hotelId
        detailRequest.setHotelId(reviewRequest.getHotelId());

        // 2. Build client details - using specific request data
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice(MapUtils.isNotEmpty(httpHeaderMap) && httpHeaderMap.containsKey(OS) ? httpHeaderMap.get(OS) : "");
        detailRequest.setClientDetails(buildClientDetails(reviewRequest.getCorrelationKey(), deviceDetails, httpHeaderMap));

        // 3. Update the roomCode in roomCriteria list
        if (StringUtils.isEmpty(reviewRequest.getRoomCode())) {
            if (CollectionUtils.isEmpty(detailRequest.getClientDetails().getRequestDetails().getRoomCriteria())) {
                detailRequest.getClientDetails().getRequestDetails().setRoomCriteria(new ArrayList<>());
            }

            RoomCriteria roomCriteria = new RoomCriteria();
            roomCriteria.setRoomCode(reviewRequest.getRoomCode());
            detailRequest.getClientDetails().getRequestDetails().getRoomCriteria().add(roomCriteria);
        }

        // 4. build reviews related fields
        detailRequest.getClientDetails().getRequestDetails().setTravellerReviewsRequest(buildTravellerReviewsRequest(reviewRequest));

        // 5. Set dummy values for NotEmpty/NonNullable fields
        detailRequest.setCheckIn(LocalDate.now().toString());
        detailRequest.setCheckOut(LocalDate.now().plusDays(1).toString());

        LOGGER.info("Successfully built TravellerReviewsRequest for hotelId: {}", reviewRequest.getHotelId());
        return detailRequest;
    }

    private ClientDetails buildClientDetails(String requestId, DeviceDetails deviceDetails, Map<String, String> httpHeaderMap) {
        ClientDetails clientDetails = new ClientDetails();
        clientDetails.setFeatureFlags(new com.gommt.hotels.orchestrator.detail.model.state.FeatureFlags());
        clientDetails.setUserDetails(new UserDetails());
        clientDetails.setRequestDetails(buildRequestDetails(requestId, deviceDetails, httpHeaderMap));
        return clientDetails;
    }

    private com.gommt.hotels.orchestrator.detail.model.state.RequestDetails buildRequestDetails(String requestId, DeviceDetails deviceDetails, Map<String, String> httpHeaderMap) {
        com.gommt.hotels.orchestrator.detail.model.state.RequestDetails requestDetails = new com.gommt.hotels.orchestrator.detail.model.state.RequestDetails();

        requestDetails.setBrand(Brand.MMT);
        requestDetails.setFunnelSource(Funnel.ALL);
        requestDetails.setLanguage(Language.ENGLISH);
        requestDetails.setSiteDomain(SiteDomain.IN);
        requestDetails.setIdContext(IdContext.B2C);
        requestDetails.setRequestType("B2CAgent");
        requestDetails.setTrafficType(TrafficType.ALL);
        requestDetails.setPageContext(PageContext.DETAIL);
        requestDetails.setBookingDevice(buildBookingDevice(deviceDetails));
        requestDetails.setRequestId(StringUtils.isNotEmpty(requestId) ? requestId : UUID.randomUUID().toString());

        String currency = MapUtils.isNotEmpty(httpHeaderMap) && httpHeaderMap.containsKey(USER_CURRENCY) ?
                httpHeaderMap.get(USER_CURRENCY).toUpperCase() : "INR";
        requestDetails.setCurrency(Currency.valueOf(currency));

        String journeyId = MapUtils.isNotEmpty(httpHeaderMap) && httpHeaderMap.containsKey(VISITOR_ID) ?
                httpHeaderMap.get(VISITOR_ID).toUpperCase() : "";
        requestDetails.setJourneyId(journeyId);

        return requestDetails;
    }

    private TravellerReviewsRequest buildTravellerReviewsRequest(UgcReviewRequest reviewRequest) {
        TravellerReviewsRequest travellerReviewsRequest = new TravellerReviewsRequest();

        // update cohort details
        List<String> tags = StringUtils.isNotEmpty(reviewRequest.getSubconcept()) ? Collections.singletonList(reviewRequest.getSubconcept()) : new ArrayList<>();
        travellerReviewsRequest.setCohort(Collections.singletonList(buildCohortData(reviewRequest.getTravelType(), tags)));

        // update highlight reviews
        if (StringUtils.isNotEmpty(reviewRequest.getHighLightedReview())) {
            travellerReviewsRequest.setReviewIds(Collections.singletonList(reviewRequest.getHighLightedReview()));
        }

        if (StringUtils.isNotEmpty(reviewRequest.getSortCriteria())) {
            travellerReviewsRequest.setSortCriteria(new SortCriteria());
            travellerReviewsRequest.getSortCriteria().setField(reviewRequest.getSortCriteria());
        }

        travellerReviewsRequest.setLimit(reviewRequest.getLimit());
        travellerReviewsRequest.setStart(reviewRequest.getStart());
        travellerReviewsRequest.setNextOTA(OTA.valueOf(reviewRequest.getNextOTA().name()));
        travellerReviewsRequest.setAvailableOTA(reviewRequest.getAvailableOTA().stream().map(ota -> OTA.valueOf(ota.name())).collect(Collectors.toList()));

        return travellerReviewsRequest;
    }

    private CohortData buildCohortData(String cohortName, List<String> tags) {
        CohortData cohortData = new CohortData();
        cohortData.setCohortName(cohortName);

        if (CollectionUtils.isNotEmpty(tags)) {
            cohortData.setAggregationTags(tags.stream().map(tag -> new AggregationTag(tag, Collections.emptyList())).collect(Collectors.toList()));
        }

        return cohortData;
    }
}
