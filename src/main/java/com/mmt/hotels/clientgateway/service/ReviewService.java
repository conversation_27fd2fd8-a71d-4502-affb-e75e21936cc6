package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.ValidationErrors;
import com.mmt.hotels.clientgateway.exception.AuthenticationException;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.payLater.PayLaterEligibilityRequest;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.payLater.PayLaterEligibilityResponse;
import com.mmt.hotels.clientgateway.restexecutors.AvailRoomsExecutor;
import com.mmt.hotels.clientgateway.restexecutors.SearchRoomsExecutor;
import com.mmt.hotels.clientgateway.restexecutors.UserServiceExecutor;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.transformer.factory.AvailRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.request.OldToNewerRequestTransformer;
import com.mmt.hotels.clientgateway.util.ClientBackendUtility;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.MaskingUtil;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.giftcard.GiftCardClaimData;
import com.mmt.hotels.model.giftcard.GiftCardClaimResponse;
import com.mmt.hotels.model.request.FetchLocationsRequestBody;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.response.FetchLocationsResponseBody;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.pricing.TotalPricingResponse;
import com.mmt.model.HotelsRoomInfoResponseEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.ControllerConstants.REVIEW_AVAIL_ROOMS;


@Component
public class ReviewService {

    @Autowired
    private AvailRoomsFactory availRoomsFactory;

    @Autowired
    private AvailRoomsExecutor availRoomsExecutor;
    
    @Autowired
    private SearchRoomsExecutor searchRoomsExecutor;

    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    private OldToNewerRequestTransformer oldToNewerRequestTransformer;

    @Autowired
    CommonHelper commonHelper;

    @Autowired
    UserServiceExecutor userServiceExecutor;
    
    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private Utility utility;

    private static DateUtil dateUtil = new DateUtil();

    @Autowired
    @Qualifier("reviewServiceThreadPool")
    private ThreadPoolTaskExecutor reviewServiceThreadPool;

    private static final Logger logger = LoggerFactory.getLogger(ReviewService.class);


    public AvailRoomsResponse availRooms(AvailRoomsRequest availRoomsRequest,  Map<String,String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {
            String request = objectMapperUtil.getJsonFromObject(availRoomsRequest, DependencyLayer.CLIENTGATEWAY);
            logger.warn("Client Request for avail rooms:- {}", MaskingUtil.maskSensitiveDataAndLog(request));
            long startTime = System.currentTimeMillis();
            int totalCandidates=0;
            SearchCriteria searchCriteria = buildRoomStayCandidatesUsingRoomCriteria(availRoomsRequest);
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchCriteria, availRoomsRequest, httpHeaderMap);
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_REVIEW_COMMON_REQUEST_PROCESS, REVIEW_AVAIL_ROOMS, System.currentTimeMillis() - startTime);
            PriceByHotelsRequestBody priceByHotelsRequestBody = availRoomsFactory.
                    getRequestService(availRoomsRequest.getClient()).convertAvailRoomsRequest(availRoomsRequest, commonModifierResponse);

            startTime = new Date().getTime();
            try {
                parameterMap = utility.addFunnelSourceToParameterMap(priceByHotelsRequestBody, parameterMap);
                RoomDetailsResponse roomDetailsResponse = availRoomsExecutor.availRooms(priceByHotelsRequestBody, parameterMap, httpHeaderMap);
                Future<HotelsRoomInfoResponseEntity> hotelsRoomInfoResponseEntityFuture = null;
                if(availRoomsRequest.getFeatureFlags() !=null && availRoomsRequest.getFeatureFlags().isRoomInfoRequired()) {
                    hotelsRoomInfoResponseEntityFuture = searchRoomsExecutor.getRoomStaticDetails(availRoomsRequest.getSearchCriteria().getHotelId(),totalCandidates, availRoomsRequest.getCorrelationKey(), parameterMap , httpHeaderMap, null, Constants.PAGE_CONTEXT_REVIEW, availRoomsRequest.getExpData());
                }

                String siteDomain = (availRoomsRequest.getRequestDetails() != null) ? availRoomsRequest.getRequestDetails().getSiteDomain() : Constants.DEFAULT_SITE_DOMAIN;
                String checkIn = (availRoomsRequest.getSearchCriteria()!=null) ? availRoomsRequest.getSearchCriteria().getCheckIn() : StringUtils.EMPTY;
                String checkOut = (availRoomsRequest.getSearchCriteria()!=null) ? availRoomsRequest.getSearchCriteria().getCheckOut() : StringUtils.EMPTY;
                String funnelSource = (availRoomsRequest.getRequestDetails()!=null) ? availRoomsRequest.getRequestDetails().getFunnelSource() : StringUtils.EMPTY;
                String deviceType = (availRoomsRequest.getDeviceDetails()!=null) ? availRoomsRequest.getDeviceDetails().getDeviceType() : StringUtils.EMPTY;
                boolean allInclusions = availRoomsRequest.getFeatureFlags()!=null && availRoomsRequest.getFeatureFlags().isAllInclusions();
                boolean showBnplCard = Utility.isShowBnplCard(availRoomsRequest.getFeatureFlags());
                HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity = hotelsRoomInfoResponseEntityFuture != null ? hotelsRoomInfoResponseEntityFuture.get() : new HotelsRoomInfoResponseEntity();
                return availRoomsFactory.getResponseService(availRoomsRequest.getClient())
                        .convertAvailRoomsResponse(availRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, siteDomain, checkIn, checkOut, availRoomsRequest.getExpData(), availRoomsRequest.getExpVariantKeys(), allInclusions, funnelSource,commonModifierResponse, showBnplCard,deviceType);
            } catch (Exception ex) {
                throw ex;
            } finally {
                metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "availRoomsTotal", new Date().getTime() - startTime);
            }

        }catch (Throwable e) {
        	if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occured in fetch availRooms: " + e.getMessage());
        		logger.debug("error occured in fetch availRooms: " + e.getMessage(), e);
        	}else 
        		logger.error("error occured in fetch availRooms: " + e.getMessage(), e);
        	
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public GiftCardClaimResponse claimGiftCard(ClaimGiftCardRequest giftCardRequest, Map<String,String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {
            String request = objectMapperUtil.getJsonFromObject(giftCardRequest, DependencyLayer.CLIENTGATEWAY);
            logger.warn("Client Request for claim gift card:- {}", MaskingUtil.maskSensitiveDataAndLog(request));
            long startTime = System.currentTimeMillis();
            int totalCandidates = 0;

            startTime = new Date().getTime();
            try {
                return availRoomsExecutor.claimGiftCard(giftCardRequest, parameterMap, httpHeaderMap);
            } catch (Exception ex) {
                return null;
            } finally {
                metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "giftCard/claim", new Date().getTime() - startTime);
            }

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occured in fetch giftCard/claim: " + e.getMessage());
                logger.debug("error occured in fetch giftCard/claim: " + e.getMessage(), e);
            } else
                logger.error("error occured in fetch giftCard/claim: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    private SearchCriteria buildRoomStayCandidatesUsingRoomCriteria(AvailRoomsRequest availRoomsRequest) {
        if(availRoomsRequest.getSearchCriteria() != null && CollectionUtils.isNotEmpty(availRoomsRequest.getSearchCriteria().getRoomCriteria())) {
            SearchCriteria searchCriteria = availRoomsRequest.getSearchCriteria();
            searchCriteria.setRoomStayCandidates(availRoomsRequest.getSearchCriteria().
                    getRoomCriteria().stream().map(AvailRoomsSearchCriteria::getRoomStayCandidates).flatMap(List::stream).collect(Collectors.toList()));
            return searchCriteria;
        }
        return null;
    }


    public String getAvailPriceOld(PriceByHotelsRequestBody priceByHotelsRequestBody, Map<String,String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try{
            AvailRoomsRequest availRoomsRequest = oldToNewerRequestTransformer.updateAvailRoomsRequest(priceByHotelsRequestBody);
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(availRoomsRequest.getSearchCriteria(), availRoomsRequest, httpHeaderMap);
            PriceByHotelsRequestBody priceByHotelsRequestBodyModified = availRoomsFactory
                    .getRequestService(availRoomsRequest.getClient())
                    .convertAvailRoomsRequest(availRoomsRequest, commonModifierResponse);
            return availRoomsExecutor.availRoomsOld(priceByHotelsRequestBodyModified, parameterMap, httpHeaderMap);
        }catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
        	else if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occured in fetch availPriceOld: " + e.getMessage());
        		logger.debug("error occured in fetch availPriceOld: " + e.getMessage(), e);
        	}else 
        		logger.error("error occured in fetch availPriceOld: " + e.getMessage(), e);
        	
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }
    
    public TotalPriceResponse getTotalPrice(TotalPricingRequest getTotalPriceRequest, Map<String,String[]> parameterMap, String correlationKey, Map<String, String> httpHeaderMap, String client) throws ClientGatewayException {
    	TotalPriceResponse totalPriceResponse;
    	try {
        	String request = objectMapperUtil.getJsonFromObject(getTotalPriceRequest, DependencyLayer.CLIENTGATEWAY);
            logger.warn("Client Request for Total Pricing:- " + request);
    		TotalPricingResponse totalPricingResponseOld = availRoomsExecutor.getTotalPricingDetails(getTotalPriceRequest, parameterMap, correlationKey, httpHeaderMap);
    		totalPriceResponse =  availRoomsFactory.getResponseService(client).convertTotalPricingResponse(totalPricingResponseOld, getTotalPriceRequest, getTotalPriceRequest.getCountryCode());
        }catch (Throwable e) {
        	if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occured in fetch totalPrice: " + e.getMessage());
        		logger.debug("error occured in fetch totalPrice: " + e.getMessage(), e);
        	}else 
        		logger.error("error occured in fetch totalPrice: " + e.getMessage(), e);
        	
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
        return totalPriceResponse;
    }
    
    public String getUpdatedPriceOccuLessOld(PriceByHotelsRequestBody priceByHotelsRequestBody, Map<String,String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try{
            AvailRoomsRequest availRoomsRequest = oldToNewerRequestTransformer.updateAvailRoomsRequest(priceByHotelsRequestBody);
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(availRoomsRequest.getSearchCriteria(), availRoomsRequest, httpHeaderMap);
            PriceByHotelsRequestBody priceByHotelsRequestBodyModified = availRoomsFactory
                    .getRequestService(availRoomsRequest.getClient())
                    .convertAvailRoomsRequest(availRoomsRequest, commonModifierResponse);
            return availRoomsExecutor.updatedPriceOccuLessOld(priceByHotelsRequestBodyModified, parameterMap, httpHeaderMap);
        }catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
        	else if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occured in fetch updatedPriceOccupancyLessOld: " + e.getMessage());
        		logger.debug("error occured in fetch updatedPriceOccupancyLessOld: " + e.getMessage(), e);
        	}else 
        		logger.error("error occured in fetch updatedPriceOccupancyLessOld: " + e.getMessage(), e);
        	
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public PayLaterEligibilityResponse fetchPayLaterEligibility(PayLaterEligibilityRequest request,String correlationKey, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, String client) throws ClientGatewayException {
        try {
            String mobile = null;
            String mmtAuth = commonHelper.getMMTAuth(httpHeaderMap, client);
            httpHeaderMap.put("mmt-auth",mmtAuth);
            UserServiceResponse userServiceResponse = userServiceExecutor.getUserServiceResponse(mmtAuth, null, null, null, correlationKey, "IN", null, null, httpHeaderMap);
            if (null != userServiceResponse
                    && null != userServiceResponse.getResult()
                    && null != userServiceResponse.getResult().getExtendedUser()
                    && null != userServiceResponse.getResult().getExtendedUser().getLoginInfoList()) {
                mobile = userServiceResponse.getResult().getExtendedUser().getLoginInfoList().stream().filter(e -> "MOBILE".equalsIgnoreCase(e.getLoginType())).findFirst().get().getLoginId();
            }
            if (StringUtils.isNotEmpty(mobile) && StringUtils.isNotEmpty(request.getTxnKey())) {
                request.setMobileNumber(mobile);
                com.mmt.hotels.model.response.PayLaterEligibilityResponse response = availRoomsExecutor.fetchPayLaterEligibility(request, parameterMap, correlationKey, httpHeaderMap);
                return availRoomsFactory.getResponseService(client).convertPayLaterEligibilityResponse(client, response,request.isMemoize());
            } else {
                throw new ClientGatewayException(DependencyLayer.CLIENTGATEWAY, ErrorType.VALIDATION, ValidationErrors.INVALID_PAY_LATER_REQUEST.getErrorCode(), ValidationErrors.INVALID_PAY_LATER_REQUEST.getErrorMsg());
            }
        } catch (Throwable e) {
            logger.error("error occured in paylater eligibility: ", e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public FetchLocationsResponse fetchLocations(FetchLocationsRequest request, String correlationKey, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, String client) throws ClientGatewayException {
        try {
            FetchLocationsRequestBody fetchLocationsRequestHES = availRoomsFactory
                    .getRequestService(client)
                    .convertFetchLocationsRequest(request);

            FetchLocationsResponseBody fetchStatesResponseHES = availRoomsExecutor.fetchLocations(fetchLocationsRequestHES, parameterMap, correlationKey, httpHeaderMap);

            return availRoomsFactory.getResponseService(client).
                    convertFetchLocationsResponse(fetchStatesResponseHES);
        } catch (ErrorResponseFromDownstreamException e) {
            logger.error("error occurred from downstream in fetchLocation: {}", e.getMessage(), e);
            ExceptionHandlerResponse exceptionHandlerResponse = handleExceptionForFetchLocations(e);
            throw exceptionHandlerResponse.getClientGatewayException();
        } catch (Exception e) {
            logger.error("error occurred in fetchLocation: {}", e.getMessage(), e);
            ExceptionHandlerResponse exceptionHandlerResponse = handleExceptionForFetchLocations(e);
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    private ExceptionHandlerResponse handleExceptionForFetchLocations(Exception e) {
        ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
        metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
        return exceptionHandlerResponse;
    }

}
