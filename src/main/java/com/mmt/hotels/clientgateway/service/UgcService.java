package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.UgcError;
import com.mmt.hotels.clientgateway.exception.LogicalException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.request.ugc.ClientLoadProgramRequest;
import com.mmt.hotels.clientgateway.request.ugc.ClientSubmitApiRequest;
import com.mmt.hotels.clientgateway.response.ugc.ClientUgcResponse;
import com.mmt.hotels.clientgateway.response.ugc.Configs;
import com.mmt.hotels.clientgateway.response.ugc.ImageUploadResult;
import com.mmt.hotels.clientgateway.response.ugc.Level;
import com.mmt.hotels.clientgateway.response.ugc.LevelConfig;
import com.mmt.hotels.clientgateway.response.ugc.QuestionData;
import com.mmt.hotels.clientgateway.response.ugc.QuestionDetails;
import com.mmt.hotels.clientgateway.response.ugc.ResponseData;
import com.mmt.hotels.clientgateway.response.ugc.ThankyouResponse;
import com.mmt.hotels.clientgateway.response.ugc.UgcResponse;
import com.mmt.hotels.clientgateway.response.ugc.FeMsgs;
import com.mmt.hotels.clientgateway.restexecutors.UgcExecutor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartRequest;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.mmt.hotels.clientgateway.constants.Constants.*;


@Component
public class UgcService {

    @Autowired
    private UgcExecutor ugcExecutor;

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    private String rewardIconUrl;
    private int reviewLevel1MaxAmount;
    private int reviewLevel1MaxPer;
    private int reviewLevel1Count;

    private int reviewLevel2MaxAmount;
    private int reviewLevel2MaxPer;
    private int reviewLevel2Count;

    private int reviewLevel3MaxAmount;
    private int reviewLevel3MaxPer;
    private int reviewLevel3Count;

    private static final Logger LOGGER = LoggerFactory.getLogger(UgcService.class);

    @PostConstruct
    public void init() {
        rewardIconUrl = commonConfigConsul.getRewardIconUrl();
        reviewLevel1Count = commonConfigConsul.getReviewLevel1Count();
        reviewLevel1MaxPer = commonConfigConsul.getReviewLevel1MaxPer();
        reviewLevel1MaxAmount = commonConfigConsul.getReviewLevel1MaxAmount();

        reviewLevel2Count = commonConfigConsul.getReviewLevel2Count();
        reviewLevel2MaxPer = commonConfigConsul.getReviewLevel2MaxPer();
        reviewLevel2MaxAmount = commonConfigConsul.getReviewLevel2MaxAmount();

        reviewLevel3Count = commonConfigConsul.getReviewLevel3Count();
        reviewLevel3MaxPer = commonConfigConsul.getReviewLevel3MaxPer();
        reviewLevel3MaxAmount = commonConfigConsul.getReviewLevel3MaxAmount();
    }

    public ClientUgcResponse fetchProgram18Question(ClientLoadProgramRequest request, Map<String, String[]> parameterMap, String correlationKey, Map<String, String> httpHeaders) {
        try {
            LOGGER.info("Fetching program18question answer for request: {}, {}", correlationKey, request);
            UgcResponse result = ugcExecutor.fetchProgram18(request, parameterMap, httpHeaders);
            LOGGER.info("Received response for program18question for request: {}, {}", correlationKey, result);
            ClientUgcResponse ugcResponse = new ClientUgcResponse();
            String currency = DEFAULT_CUR_INR;
            if (httpHeaders.containsKey(USER_CURRENCY)) {
                currency = httpHeaders.get(USER_CURRENCY);
            }
            UgcResponseClientUgcResponseMapper(result, ugcResponse, currency);
            ugcResponse.setCurrency(currency);
            return ugcResponse;
        } catch (Exception e) {
            LOGGER.error("Error in fetchProgram18Question for: {}", correlationKey, e);
            return getErrorResponseForClient(e);
        }
    }
    /**
     * Generates a ClientUgcResponse object containing error details based on the provided exception.
     *
     * @param e the exception that occurred
     * @return a ClientUgcResponse object populated with error details
     */
    public ClientUgcResponse getErrorResponseForClient(Exception e) {
        ClientUgcResponse ugcResponse = new ClientUgcResponse();
        ugcResponse.setSuccess(false);
        FeMsgs feMsgs = new FeMsgs();
        FeMsgs.ErrMsg1 errMsg1 = new FeMsgs.ErrMsg1();
        FeMsgs.Cta cta = new FeMsgs.Cta();

        String errorCode;

        if (e instanceof LogicalException) {
            LogicalException logicalException = (LogicalException) e;
            errorCode = logicalException.getCode();
        } else {
            errorCode = UgcError.GENERIC_ERROR.getErrorCode();
        }

        UgcError ugcError = UgcError.resolve(errorCode);
        if (ugcError == null) {
            ugcError = UgcError.GENERIC_ERROR;
        }

        cta.setText(polyglotService.getTranslatedData(ConstantsTranslation.CTA_TEXT));
        cta.setDeeplink(polyglotService.getTranslatedData(ConstantsTranslation.CTA_DEEPLINK_URL));

        errMsg1.setCode(ugcError.getErrorCode());
        errMsg1.setTitle(polyglotService.getTranslatedData(ugcError.getTitle()));
        errMsg1.setMsg(polyglotService.getTranslatedData(ugcError.getErrorMsg()));
        errMsg1.setCta(cta);
        feMsgs.setErrMsg1(errMsg1);
        ugcResponse.setFeMsgs(feMsgs);
        return ugcResponse;
    }

    public ClientUgcResponse submitAnswers(ClientSubmitApiRequest request, MultipartRequest multipartRequest, Map<String, String[]> parameterMap,
                                           String correlationKey, Map<String, String> httpHeaders) {
        try {
            LOGGER.info("Submitting answer for request: {}, {}", request, correlationKey);
            List<ImageUploadResult> imageUploadResults = uploadImageToPlatformsS3(multipartRequest);
            UgcResponse result = ugcExecutor.submitAnswersToPlatforms(request, imageUploadResults, parameterMap);
            LOGGER.info("Received response for submit answer for request: {}, {}", correlationKey, result);
            ClientUgcResponse clientUgcResponse = new ClientUgcResponse();
            String currency = DEFAULT_CUR_INR;
            if (httpHeaders.containsKey(USER_COUNTRY)) {
                currency = httpHeaders.get(USER_CURRENCY);
            }
            UgcResponseClientUgcResponseMapper(result, clientUgcResponse, currency);
            return clientUgcResponse;
        } catch (Exception e) {
            LOGGER.error("Error in submitAnswers for: {} ", correlationKey, e);
            return null;
        }
    }

    public List<ImageUploadResult> uploadImageToPlatformsS3(MultipartRequest multipartRequest) throws RestConnectorException, IOException {
        return ugcExecutor.uploadImagesToPlatformsS3(multipartRequest);
    }

    public void UgcResponseClientUgcResponseMapper(UgcResponse ugcResponse, ClientUgcResponse clientUgcResponse, String currency) {
        try {
            if (ugcResponse == null || ugcResponse.getQuestionData() == null) {
                clientUgcResponse.setSuccess(false);
                return;
            }
            QuestionData questionData = ugcResponse.getQuestionData();
            ResponseData data = new ResponseData();
            Configs configs = new Configs();

            if(ugcResponse.getQuestionData().getLobData()!=null && ugcResponse.getQuestionData().getLobData().getBookingId()!=null)
                data.setBookingid(ugcResponse.getQuestionData().getLobData().getBookingId());

            // Set guidelines
            List<String> reviewTextList = IntStream.rangeClosed(1, 4)
                    .sequential()
                    .mapToObj(i -> "TEXT_REVIEW_TEXT_" + i)
                    .map(polyglotService::getTranslatedData)
                    .collect(Collectors.toList());

            configs.getGuidelines().getTextReview().setTitle(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_TEXT_TITLE));
            configs.getGuidelines().getTextReview().setText(reviewTextList);

            List<String> imageReviewList = IntStream.rangeClosed(1, 3)
                    .sequential()
                    .mapToObj(i -> "IMAGE_REVIEW_TEXT_" + i)
                    .map(polyglotService::getTranslatedData)
                    .collect(Collectors.toList());

            configs.getGuidelines().getImageReview().setTitle(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_IMAGE_TITLE));
            configs.getGuidelines().getImageReview().setText(imageReviewList);

            List<String> baseTextList = IntStream.rangeClosed(1, 3)
                    .sequential()
                    .mapToObj(i -> "BASE_TEXT_" + i)
                    .map(polyglotService::getTranslatedData)
                    .collect(Collectors.toList());
            configs.getGuidelines().getBase().setTitle(polyglotService.getTranslatedData(ConstantsTranslation.BASE_TITLE));
            configs.getGuidelines().getBase().setText(baseTextList);

            // Set onboarding data
            configs.getOnBoardingData().setIcon(rewardIconUrl);

            // Set other properties
            configs.getOnBoardingData().setHtlImage(questionData.getLobData().getHotelImage());
            configs.setEstimatedTime(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_ESTIMATED_TIME));

            data.setHotelName(questionData.getLobData().getHotelName());
            data.setLocusId(questionData.getLobData().getCityCode());
            data.setLocusType(polyglotService.getTranslatedData(ConstantsTranslation.LOCUS_TYPE));
            data.setEndCityName(null);
            data.setSegmentId(polyglotService.getTranslatedData(ConstantsTranslation.SEGMENT_ID));
            data.setUgcId(questionData.getUgcId());
            data.setNumberOfPages(questionData.getNumberOfPages());

            List<QuestionDetails> submittedQuestions = new ArrayList<>();
            if (questionData.getSubmittedQuestions() != null) {
                for (QuestionData questionDataMap : questionData.getSubmittedQuestions().values()) {
                    submittedQuestions.add(convertQuestionDataToQuestionDetails(questionDataMap));
                }
            }
            data.setSubmittedQuestions(submittedQuestions);

            if (StringUtils.isNotEmpty(questionData.getProgramId())) {
                data.setProgramId(questionData.getProgramId());
            }
            if (StringUtils.isNotEmpty(questionData.getContentId())) {
                data.setContentId(questionData.getContentId());
            }

            int totalQuesCount = 0;
            if (questionData.getLevelConfig() != null && questionData.getNumberOfLevels() > 0) {
                totalQuesCount = fetchDynamicLevelData(questionData, data, null);
            }

            String hotelName = questionData.getLobData() != null && questionData.getLobData().getHotelName() != null ? questionData.getLobData().getHotelName() : StringUtils.EMPTY;
            if (CollectionUtils.isNotEmpty(submittedQuestions) && totalQuesCount == submittedQuestions.size()) {
                configs.getOnBoardingData().setTitle(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_EDIT_TITLE));
                configs.getOnBoardingData().setText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_EDIT_TEXT), hotelName));
                configs.getOnBoardingData().setDesktopText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_EDIT_TEXT), hotelName));
                configs.setExitReviewString(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_EDIT_EXIT_REVIEW_STRING));
            } else {
                Map<String, LevelConfig> levelConfigMap = questionData.getLevelConfig();
                String lastLevel = String.valueOf(questionData.getNumberOfLevels());
                int maxPerc = 0;
                int maxAmount = 0;
                if (levelConfigMap.get(lastLevel) != null) {
                    maxPerc = levelConfigMap.get(lastLevel).getMaxPerc() != null ? levelConfigMap.get(lastLevel).getMaxPerc().intValue() : 0;
                    maxAmount = levelConfigMap.get(lastLevel).getMaxAmount() != null ? levelConfigMap.get(lastLevel).getMaxAmount().intValue() : 0;
                }

                if (maxPerc == 0 && maxAmount == 0) {
                    //Non incentive - both null
                    configs.getOnBoardingData().setTitle(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_NONINCENTIVE_TITLE));
                    configs.getOnBoardingData().setText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_NONINCENTIVE_TEXT), hotelName));
                    configs.getOnBoardingData().setDesktopText(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_DESKTOP_NONINCENTIVE_TEXT));
                    configs.setExitReviewString(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_EXIT_REVIEW_NONINCENTIVE_STRING));
                } else if (maxPerc == 0) {
                    //Oyo - percentage null
                    configs.getOnBoardingData().setTitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_DISCAMOUNT_TITLE), maxAmount));
                    configs.getOnBoardingData().setText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_DISCAMOUNT_TEXT), hotelName, maxAmount));
                    configs.getOnBoardingData().setDesktopText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_DESKTOP_DISCAMOUNT_TEXT), maxAmount));
                    configs.setExitReviewString(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_EXIT_REVIEW_DISCAMOUNT_STRING));
                } else {
                    //Incentive - percentage & amount available
                    configs.getOnBoardingData().setTitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_TITLE), maxPerc));
                    configs.getOnBoardingData().setText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_TEXT), hotelName, maxPerc, maxAmount));
                    configs.getOnBoardingData().setDesktopText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_DESKTOP_TEXT), maxPerc, maxAmount));
                    configs.setExitReviewString(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_EXIT_REVIEW_STRING));
                }
                data.setMaxAmount(maxAmount);
                data.setMaxPercent(maxPerc);
            }
            data.setConfigs(configs);

            if (questionData.getReviewTerminationMessage() != null) {
                data.setNextQuestion(null);
                data.setState(polyglotService.getTranslatedData(ConstantsTranslation.REVIEW_COMPLETE));
                clientUgcResponse.setData(data);
                clientUgcResponse.setSuccess(true);
                return;
            }

            data.setNextQuestion(convertQuestionDataToQuestionDetails(questionData));
            data.setState(polyglotService.getTranslatedData(ConstantsTranslation.IN_PROGRESS_REVIEW));
            if(CollectionUtils.isNotEmpty(questionData.getLevelPageIds())) {
                data.setCurLevelPageCount(questionData.getLevelPageIds().size());
            }
            clientUgcResponse.setData(data);
            clientUgcResponse.setSuccess(true);
            clientUgcResponse.setCurrency(currency);
        } catch (Exception e) {
            clientUgcResponse.setSuccess(false);
            LOGGER.error("Error in UgcResponseClientUgcResponseMapper", e);
        }
    }


    public QuestionDetails convertQuestionDataToQuestionDetails(QuestionData questionData) {
        try {
            QuestionDetails questionDetails = new QuestionDetails();
            if (questionData.getQuestions() != null && CollectionUtils.isNotEmpty(questionData.getQuestions())) {
                questionDetails.setDynamicQuestionType(questionData.getQuestions().get(0).getAnswerType());
                questionDetails.setOptionsInfo(questionData.getQuestions().get(0).getOptionsInfo());
                questionDetails.setId(String.valueOf(questionData.getQuestions().get(0).getQuestionId()));
                questionDetails.setQuestionDescription(questionData.getQuestions().get(0).getQuestionTitle());
                questionDetails.setQuestionSubtitle(questionData.getQuestions().get(0).getQuestionSubtitle());
                questionDetails.setPageId(questionData.getPageId());
                questionDetails.setUserPage(questionData.getUserPage());
                questionDetails.setPreviousPageId(questionData.getPreviousPageId());
                questionDetails.setAdditionalProperties(questionData.getQuestions().get(0).getAdditionalProperties());
                questionDetails.setAnswerProvided(questionData.getQuestions().get(0).getAnswerProvided());
                questionDetails.setAnswerHelpText(questionData.getQuestions().get(0).getAnswerHelpText());
                questionDetails.setAnswerTitle(questionData.getQuestions().get(0).getAnswerTitle());
                if (questionData.getLevelConfig() != null) {
                    fetchDynamicLevelData(questionData, null, questionDetails);
                }
                questionDetails.setLevel(questionData.getQuestions().get(0).getLevel());
                if(questionData.getQuestions().get(0).getValidators() != null)
                    questionDetails.setMandatory(questionData.getQuestions().get(0).getValidators().isMandatory());
                questionDetails.setValidators(questionData.getQuestions().get(0).getValidators());
            }
            return questionDetails;
        } catch (Exception e) {
            LOGGER.error("Error in UgcResponseClientUgcResponseMapper", e);
            return null;
        }
    }

    private int fetchDynamicLevelData(QuestionData questionData, ResponseData responseData, QuestionDetails questionDetails) {
        int totalQuesCount = 0;
        Map<String, LevelConfig> platformsLevelConfig = questionData.getLevelConfig();
        List<Level> levels = new ArrayList<>();

        for (int i = 1; i <= questionData.getNumberOfLevels(); i++) {
            LevelConfig config = platformsLevelConfig.get(String.valueOf(i));
            if (config != null) {
                Level level = new Level();
                level.setMaxAmount(config.getMaxAmount() != null ? config.getMaxAmount().intValue() : 0);
                level.setMaxPercent(config.getMaxPerc() != null ? config.getMaxPerc().intValue() : 0);
                level.setLevelHeader(config.getLevelText());
                level.setCount(config.getLevelTotalQuestions());
                totalQuesCount += level.getCount();
                level.setEstimatedTime(polyglotService.getTranslatedData(String.format(ConstantsTranslation.REVIEW_LEVEL_ESTIMATED_TIME, i)));
                ThankyouResponse response = new ThankyouResponse();
                int levelIndex = (i == questionData.getNumberOfLevels()) ? 3 : i;
                response.setTitle(polyglotService.getTranslatedData(String.format(ConstantsTranslation.REVIEW_LEVEL_TITLE, levelIndex)));
                response.setDescription(polyglotService.getTranslatedData(String.format(ConstantsTranslation.REVIEW_LEVEL_DESCRIPTION, levelIndex)));
                response.setEditTextDesc(polyglotService.getTranslatedData(String.format(ConstantsTranslation.REVIEW_LEVEL_EDIT_TEXT_DESC, levelIndex)));
                level.setThankyouResponse(response);
                levels.add(level);
            }
        }

        if (responseData != null) responseData.setLevelconfig(levels);
        if (questionDetails != null) questionDetails.setLevelconfig(levels);

        return totalQuesCount;
    }
}
