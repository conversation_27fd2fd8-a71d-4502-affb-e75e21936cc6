package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.configuration.CircuitBreakerConfig;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.CircuitBreakerMetricAspect;
import com.mmt.hotels.clientgateway.response.moblanding.MobLandingResponse;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.function.Supplier;

/**
 * Generic Circuit Breaker Service Wrapper
 * Provides circuit breaker functionality for all API calls with fallback strategies
 */
@Service
public class CircuitBreakerService {

    private static final Logger logger = LoggerFactory.getLogger(CircuitBreakerService.class);

    @Autowired
    private CircuitBreakerRegistry circuitBreakerRegistry;

    @Autowired
    private CircuitBreakerConfig circuitBreakerConfig;

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired(required = false)
    private CircuitBreakerMetricAspect circuitBreakerMetricAspect;

    /**
     * Executes mob landing API call with circuit breaker protection
     */
    public MobLandingResponse executeMobLandingWithCircuitBreaker(
            Supplier<MobLandingResponse> apiCall,
            Map<String, String> headers) throws ClientGatewayException {

        if (!circuitBreakerConfig.isMobLandingCircuitBreakerEnabled()) {
            logger.debug("Mob Landing Circuit Breaker is disabled, executing direct API call");
            return apiCall.get();
        }

        CircuitBreaker circuitBreaker = null;
        try {
            circuitBreaker = circuitBreakerRegistry.circuitBreaker("mob-landing");
        } catch (Exception e) {
            logger.warn("Failed to get circuit breaker, falling back to direct API call: {}", e.getMessage());
            return apiCall.get();
        }

        logger.debug("Executing Mob Landing API call with circuit breaker");

        Callable<MobLandingResponse> decoratedCall = CircuitBreaker.decorateCallable(circuitBreaker, () -> {
            long startTime = System.currentTimeMillis();
            try {
                MobLandingResponse response = apiCall.get();

                // Log successful call metrics
                long executionTime = System.currentTimeMillis() - startTime;
                if (circuitBreakerMetricAspect != null) {
                    circuitBreakerMetricAspect.logCircuitBreakerCallTime("mob-landing", executionTime, true);
                }

                return response;
            } catch (Exception e) {
                // Log failed call metrics
                long executionTime = System.currentTimeMillis() - startTime;
                if (circuitBreakerMetricAspect != null) {
                    circuitBreakerMetricAspect.logCircuitBreakerCallTime("mob-landing", executionTime, false);
                }

                logger.error("Error in Mob Landing API call", e);
                throw e;
            }
        });

        try {
            return decoratedCall.call();
        } catch (Exception e) {
            logger.warn("Circuit breaker triggered for Mob Landing API, attempting fallback");

            // Log fallback execution
            if (circuitBreakerMetricAspect != null) {
                circuitBreakerMetricAspect.logCircuitBreakerFallbackExecution("mob-landing", "DEFAULT_RESPONSE");
            }

            return getMobLandingFallbackResponse(headers);
        }
    }

    /**
     * Executes filter count API call with circuit breaker protection
     */
    public <T> T executeFilterCountWithCircuitBreaker(
            Supplier<T> apiCall,
            Map<String, String> headers) throws ClientGatewayException {

        if (!circuitBreakerConfig.isFilterCountCircuitBreakerEnabled()) {
            logger.debug("Filter Count Circuit Breaker is disabled, executing direct API call");
            return apiCall.get();
        }

        CircuitBreaker circuitBreaker = null;
        try {
            circuitBreaker = circuitBreakerRegistry.circuitBreaker("filter-count");
        } catch (Exception e) {
            logger.warn("Failed to get filter-count circuit breaker, falling back to direct API call: {}", e.getMessage());
            return apiCall.get();
        }

        logger.debug("Executing Filter Count API call with circuit breaker");

        Callable<T> decoratedCall = CircuitBreaker.decorateCallable(circuitBreaker, () -> {
            long startTime = System.currentTimeMillis();
            try {
                T response = apiCall.get();

                // Log successful call metrics
                long executionTime = System.currentTimeMillis() - startTime;
                if (circuitBreakerMetricAspect != null) {
                    circuitBreakerMetricAspect.logCircuitBreakerCallTime("filter-count", executionTime, true);
                }

                return response;
            } catch (Exception e) {
                // Log failed call metrics
                long executionTime = System.currentTimeMillis() - startTime;
                if (circuitBreakerMetricAspect != null) {
                    circuitBreakerMetricAspect.logCircuitBreakerCallTime("filter-count", executionTime, false);
                }

                logger.error("Error in Filter Count API call", e);
                throw e;
            }
        });

        try {
            return decoratedCall.call();
        } catch (Exception e) {
            logger.warn("Circuit breaker triggered for Filter Count API, throwing 503 Service Temporarily Unavailable");

            // Log fallback execution
            if (circuitBreakerMetricAspect != null) {
                circuitBreakerMetricAspect.logCircuitBreakerFallbackExecution("filter-count", "503_SERVICE_UNAVAILABLE");
            }

            // Throw 503 Service Temporarily Unavailable exception
            throw getFilterCountFallbackException();
        }
    }

    /**
     * Executes generic API call with circuit breaker protection
     */
    public <T> T executeWithCircuitBreaker(
            String circuitBreakerName,
            Supplier<T> apiCall,
            Supplier<T> fallbackCall) throws ClientGatewayException {

        CircuitBreaker circuitBreaker = circuitBreakerRegistry.circuitBreaker(circuitBreakerName);
        if (circuitBreaker == null) {
            logger.debug("Circuit breaker {} not found or disabled, executing direct API call", circuitBreakerName);
            return apiCall.get();
        }

        logger.debug("Executing API call with circuit breaker: {}", circuitBreakerName);

        Callable<T> decoratedCall = CircuitBreaker.decorateCallable(circuitBreaker, () -> {
            try {
                return apiCall.get();
            } catch (Exception e) {
                logger.error("Error in API call for circuit breaker: {}", circuitBreakerName, e);
                throw e;
            }
        });

        try {
            return decoratedCall.call();
        } catch (Exception e) {
            logger.warn("Circuit breaker {} triggered, attempting fallback", circuitBreakerName);
            return fallbackCall.get();
        }
    }

    /**
     * Provides fallback response for mob landing API
     */
    private MobLandingResponse getMobLandingFallbackResponse(Map<String, String> headers) {
        // Return default fallback response
        logger.info("Returning default fallback response for Mob Landing API");
        return createDefaultMobLandingResponse();
    }

    /**
     * Provides fallback exception for filter count API
     */
    private ClientGatewayException getFilterCountFallbackException() {
        logger.warn("Creating 503 Service Temporarily Unavailable exception for Filter Count API");

        ClientGatewayException exception = new ClientGatewayException(
            DependencyLayer.ORCHESTRATOR,
            ErrorType.CONNECTIVITY,
            "503",
            "Service temporarily unavailable"
        );
        exception.setHttpStatusCode(HttpStatus.SERVICE_UNAVAILABLE);

        return exception;
    }

    /**
     * Creates a default fallback response for mob landing
     */
    private MobLandingResponse createDefaultMobLandingResponse() {
        MobLandingResponse fallbackResponse = new MobLandingResponse();

        // Set basic fallback data
        // Note: This should be customized based on the actual MobLandingResponse structure
        // and business requirements for fallback scenarios

        logger.debug("Created default fallback response for Mob Landing API");

        return fallbackResponse;
    }

    /**
     * Gets circuit breaker state for monitoring
     */
    public String getCircuitBreakerState(String circuitBreakerName) {
        CircuitBreaker circuitBreaker = circuitBreakerRegistry.circuitBreaker(circuitBreakerName);
        if (circuitBreaker != null) {
            return circuitBreaker.getState().toString();
        }
        return "NOT_CONFIGURED";
    }

    /**
     * Gets circuit breaker metrics for monitoring
     */
    public Map<String, Object> getCircuitBreakerMetrics(String circuitBreakerName) {
        CircuitBreaker circuitBreaker = circuitBreakerRegistry.circuitBreaker(circuitBreakerName);
        if (circuitBreaker != null) {
            try {
                CircuitBreaker.Metrics metrics = circuitBreaker.getMetrics();
                Map<String, Object> metricsMap = new HashMap<>();
                metricsMap.put("state", circuitBreaker.getState().toString());

                // Safely get metrics with fallback values
                try {
                    metricsMap.put("failureRate", metrics.getFailureRate());
                } catch (Exception e) {
                    metricsMap.put("failureRate", "N/A");
                }

                // Use methods available in Resilience4j 1.7.1
                try {
                    // getNumberOfBufferedCalls() is available in 1.7.1
                    metricsMap.put("numberOfBufferedCalls", metrics.getNumberOfBufferedCalls());
                } catch (Exception e) {
                    metricsMap.put("numberOfBufferedCalls", "N/A");
                }

                try {
                    // getNumberOfFailedCalls() is available in 1.7.1
                    metricsMap.put("numberOfFailedCalls", metrics.getNumberOfFailedCalls());
                } catch (Exception e) {
                    metricsMap.put("numberOfFailedCalls", "N/A");
                }

                try {
                    // getNumberOfSuccessfulCalls() is available in 1.7.1
                    metricsMap.put("numberOfSuccessfulCalls", metrics.getNumberOfSuccessfulCalls());
                } catch (Exception e) {
                    metricsMap.put("numberOfSuccessfulCalls", "N/A");
                }

                try {
                    // getNumberOfSlowCalls() is available in 1.7.1
                    metricsMap.put("numberOfSlowCalls", metrics.getNumberOfSlowCalls());
                } catch (Exception e) {
                    metricsMap.put("numberOfSlowCalls", "N/A");
                }

                try {
                    // getSlowCallRate() is available in 1.7.1
                    metricsMap.put("slowCallRate", metrics.getSlowCallRate());
                } catch (Exception e) {
                    metricsMap.put("slowCallRate", "N/A");
                }

                return metricsMap;
            } catch (Exception e) {
                logger.error("Error getting metrics for circuit breaker: {}", circuitBreakerName, e);
                Map<String, Object> errorMap = new HashMap<>();
                errorMap.put("error", "Error retrieving metrics: " + e.getMessage());
                return errorMap;
            }
        }
        Map<String, Object> errorMap = new HashMap<>();
        errorMap.put("error", "Circuit breaker not found: " + circuitBreakerName);
        return errorMap;
    }
}
