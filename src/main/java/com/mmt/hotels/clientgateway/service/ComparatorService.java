package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.exception.AuthenticationException;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.restexecutors.SearchRoomsExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.SearchRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.request.OldToNewerRequestTransformer;
import com.mmt.hotels.clientgateway.util.ClientBackendUtility;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.ResponseFilterFlags;
import com.mmt.hotels.model.request.upsell.UpsellHotelDetailRequest;
import com.mmt.hotels.model.request.upsell.UpsellHotelDetailResponse;
import com.mmt.hotels.model.request.upsell.UpsellHotelRequest;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperResponseBO;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.response.ComparatorHotel;
import com.mmt.hotels.pojo.response.ComparatorHotels;
import com.mmt.hotels.pojo.response.ComparatorPrices;
import com.mmt.hotels.pojo.response.HotelComparatorResponse;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class ComparatorService {

    @Autowired
    private OldToNewerRequestTransformer oldToNewerRequestTransformer;

    @Autowired
    private CommonHelper commonHelper;

    @Autowired
    private SearchRoomsFactory searchRoomsFactory;

    @Autowired
    private SearchRoomsExecutor searchRoomsExecutor;

    @Autowired
    private MetricErrorLogger metricErrorLogger;

    private static final Logger logger = LoggerFactory.getLogger(DetailService.class);

    public HotelComparatorResponse comparatorOld(HotelDetailsMobRequestBody request, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, String type) throws ClientGatewayException {
        try {
            SearchRoomsRequest searchRequest = oldToNewerRequestTransformer.updateSearchRoomRequest(request);
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchRequest.getSearchCriteria(), searchRequest, httpHeaderMap);
            PriceByHotelsRequestBody priceByHotelsRequestBodyModified = searchRoomsFactory.
                    getRequestService(searchRequest.getClient()).convertSearchRoomsRequest(searchRequest, commonModifierResponse);
            UpsellHotelDetailResponse detailResponse = searchRoomsExecutor.getComparatorOld(convertSearchRoomsRequest(priceByHotelsRequestBodyModified, request, type), parameterMap, httpHeaderMap, UpsellHotelDetailResponse.class);
            HotelComparatorResponse hotelComparatorResponse = null;
            if (detailResponse.getResponseErrors() != null) {
                hotelComparatorResponse = new HotelComparatorResponse();
                hotelComparatorResponse.setResponseErrors(detailResponse.getResponseErrors());
                return hotelComparatorResponse;
            } else {
                return parseComparatorResponse(detailResponse);
            }
        } catch (Throwable e) {
             if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occured in comparatorServiceOld: " + e.getMessage());
            } else
                logger.error("error occured in comparatorServiceOld: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    private UpsellHotelRequest convertSearchRoomsRequest(PriceByHotelsRequestBody comparatorRequest, HotelDetailsMobRequestBody hotelDetailsMobRequestBody, String type) {
        UpsellHotelRequest upsellHotelRequest = new UpsellHotelRequest();
        if (comparatorRequest != null) {
            upsellHotelRequest.setMmtAuth(comparatorRequest.getAuthToken());
            upsellHotelRequest.setRequestType(comparatorRequest.getRequestType());
            upsellHotelRequest.setIdContext(comparatorRequest.getIdContext());
            upsellHotelRequest.setChannel(comparatorRequest.getChannel());
            upsellHotelRequest.setToken(comparatorRequest.getToken());
            upsellHotelRequest.setDeviceId(comparatorRequest.getDeviceId());
            upsellHotelRequest.setDeviceType(comparatorRequest.getDeviceType());
            upsellHotelRequest.setAppVersion(comparatorRequest.getAppVersion());
            upsellHotelRequest.setAffiliateId(comparatorRequest.getAffiliateId());
            upsellHotelRequest.setApplicationId(comparatorRequest.getApplicationId());
            upsellHotelRequest.setExpiryRequired(comparatorRequest.isExpiryRequired());
            upsellHotelRequest.setPageContext(comparatorRequest.getPageContext());
            upsellHotelRequest.setCdfContextId(comparatorRequest.getCdfContextId());
            upsellHotelRequest.setCityCode(comparatorRequest.getCityCode());
            upsellHotelRequest.setCountryCode(comparatorRequest.getCountryCode());
            upsellHotelRequest.setHotelIds(comparatorRequest.getHotelIds());
            upsellHotelRequest.setBookingDevice(comparatorRequest.getBookingDevice());
            upsellHotelRequest.setCheckin(comparatorRequest.getCheckin());
            upsellHotelRequest.setCheckout(comparatorRequest.getCheckout());
            upsellHotelRequest.setLoggedIn(comparatorRequest.isLoggedIn());
            upsellHotelRequest.setRoomStayCandidates(comparatorRequest.getRoomStayCandidates());
            upsellHotelRequest.setResponseFilterFlags(comparatorRequest.getResponseFilterFlags());
            upsellHotelRequest.setNumberOfCoupons(comparatorRequest.getCouponCount());
            upsellHotelRequest.setCurrency(comparatorRequest.getCurrency());
            upsellHotelRequest.setGuestRecommendationEnabled(comparatorRequest.getGuestRecommendationEnabled());
            upsellHotelRequest.setVisitorId(comparatorRequest.getVisitorId());
            upsellHotelRequest.setVisitNumber(comparatorRequest.getVisitNumber());
            upsellHotelRequest.setDomain(comparatorRequest.getDomain());
            upsellHotelRequest.setLob(comparatorRequest.getLob());
            upsellHotelRequest.setLatitude(comparatorRequest.getSrLat());
            upsellHotelRequest.setLongitude(comparatorRequest.getSrLng());
            upsellHotelRequest.setExperimentData(comparatorRequest.getExperimentData());
            upsellHotelRequest.setCorrelationKey(comparatorRequest.getCorrelationKey());
            upsellHotelRequest.setFirstTimeUser(comparatorRequest.isFirstTimeUser());
            upsellHotelRequest.setSrLat(comparatorRequest.getSrLat());
            upsellHotelRequest.setSrLng(comparatorRequest.getSrLng());
            upsellHotelRequest.setAuthToken(comparatorRequest.getAuthToken());
            upsellHotelRequest.setUUID(comparatorRequest.getUuid());
            upsellHotelRequest.setProfileType(comparatorRequest.getProfileType());
            upsellHotelRequest.setSubProfileType(comparatorRequest.getSubProfileType());
            upsellHotelRequest.setEbizDetails(comparatorRequest.getEbizDetails());
            upsellHotelRequest.setLocationId(comparatorRequest.getLocationId());
            upsellHotelRequest.setLocationType(comparatorRequest.getLocationType());
            upsellHotelRequest.setNotifCoupon(comparatorRequest.getNotifCoupon());
            upsellHotelRequest.setHotelIdList(comparatorRequest.getHotelIds());
            upsellHotelRequest.setHydraSegments(comparatorRequest.getHydraSegments());
            upsellHotelRequest.setSortCriteria(hotelDetailsMobRequestBody.getSortCriteria());
            upsellHotelRequest.setAppliedFilterMap(comparatorRequest.getAppliedFilterMap());
            upsellHotelRequest.setSelectTags(hotelDetailsMobRequestBody.getSelectedTags());
            UpsellHotelDetailRequest hotelDetail = new UpsellHotelDetailRequest();
            hotelDetail.setHotelId(hotelDetailsMobRequestBody.getHotelId());
            upsellHotelRequest.setHotel(hotelDetail);
            upsellHotelRequest.setLimit(3);
            upsellHotelRequest.setNumberOfSoldOuts(1);
            upsellHotelRequest.setComparatorHotelsList(hotelDetailsMobRequestBody.getComparatorHotelsList());
            upsellHotelRequest.setImageCategory(hotelDetailsMobRequestBody.getImageCategory());
            upsellHotelRequest.setImageType(hotelDetailsMobRequestBody.getImageType());
            if (CollectionUtils.isNotEmpty(hotelDetailsMobRequestBody.getComparatorHotelsList())) {
                upsellHotelRequest.setLimit(Integer.MAX_VALUE);
                upsellHotelRequest.setNumberOfSoldOuts(Integer.MAX_VALUE - hotelDetailsMobRequestBody.getComparatorHotelsList().size());
            }
            //sold out get
            if(null==upsellHotelRequest.getResponseFilterFlags()){
                upsellHotelRequest.setResponseFilterFlags(new ResponseFilterFlags());
            }
            upsellHotelRequest.getResponseFilterFlags().setSoldOutInfoReq(false);
            //this is default set values
            upsellHotelRequest.setNumberOfCoupons(1);
            upsellHotelRequest.setCancellationPolicyRulesReq("no");
            upsellHotelRequest.setUpsellReqType(type);
            upsellHotelRequest.setMcid(comparatorRequest.getMcid());
            upsellHotelRequest.setTravelerDetailsList(comparatorRequest.getTravelerDetailsList());
        }
        return upsellHotelRequest;
    }

    private HotelComparatorResponse parseComparatorResponse(
            UpsellHotelDetailResponse detailResponse) {
        HotelComparatorResponse hotelComparatorResponse = new HotelComparatorResponse();
        hotelComparatorResponse.setHotelDisplayMap(detailResponse.getHotelDisplayMap());
        hotelComparatorResponse.setComparisonHeadings(detailResponse.getComparisonHeadings());
        hotelComparatorResponse.setCtaMap(detailResponse.getCtaMap());
        hotelComparatorResponse.setResponseErrors(detailResponse.getResponseErrors());
        hotelComparatorResponse.setTitle(detailResponse.getTitle());
        ComparatorHotels hotelSearchResponse = parseComparatorHotels(detailResponse.getHotelSearchResponse());
        hotelComparatorResponse.setHotelSearchResponse(hotelSearchResponse);
        return hotelComparatorResponse;
    }

    private ComparatorHotels parseComparatorHotels(SearchWrapperResponseBO<SearchWrapperHotelEntity> hotelSearchResponse) {
        ComparatorHotels comparatorHotels = new ComparatorHotels();
        List<ComparatorHotel> hotelList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(hotelSearchResponse.getHotelList())) {
            for (SearchWrapperHotelEntity searchWrapperHotelEntity : hotelSearchResponse.getHotelList()) {
                ComparatorHotel comparatorHotel = new ComparatorHotel();
                comparatorHotel.setPrices(parseComparatorPrices(searchWrapperHotelEntity));
                comparatorHotel.setFlyfishReviewSummary(searchWrapperHotelEntity.getFlyfishReviewSummary());
                comparatorHotel.setId(searchWrapperHotelEntity.getId());
                if (CollectionUtils.isNotEmpty(searchWrapperHotelEntity.getMainImages())) {
                    List<String> images = new ArrayList<>();
                    images.add(searchWrapperHotelEntity.getMainImages().get(0));
                    comparatorHotel.setMainImages(images);
                }
                comparatorHotel.setName(searchWrapperHotelEntity.getName());
                comparatorHotel.setMaskedPropertyName(searchWrapperHotelEntity.isMaskedPropertyName());
                comparatorHotel.setSoldOut(searchWrapperHotelEntity.getIsSoldOut());
                comparatorHotel.setStarRating(searchWrapperHotelEntity.getStarRating());
                comparatorHotel.setCrossSellTag(searchWrapperHotelEntity.getCrossSellTag());
                comparatorHotel.setAddress(searchWrapperHotelEntity.getAddress());
                comparatorHotel.setMmtHotelCategory(searchWrapperHotelEntity.getMmtHotelCategory());
                hotelList.add(comparatorHotel);
            }
        }
        comparatorHotels.setCountryCode(hotelSearchResponse.getCountryCode());
        comparatorHotels.setHotelList(hotelList);
        comparatorHotels.setCurrency(hotelSearchResponse.getCurrency());
        return comparatorHotels;
    }

    private ComparatorPrices parseComparatorPrices(SearchWrapperHotelEntity searchWrapperHotelEntity) {
        ComparatorPrices comparatorPrices = new ComparatorPrices();
        if (searchWrapperHotelEntity != null && searchWrapperHotelEntity.getDisplayFare() != null
                && searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown() != null) {
            comparatorPrices.setDisplayPrice(searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getDisplayPrice());
            comparatorPrices.setNonDiscountedPrice(searchWrapperHotelEntity.getDisplayFare().getDisplayPriceBreakDown().getNonDiscountedPrice());
            comparatorPrices.setPriceDifferenceWithPivot(searchWrapperHotelEntity.getPriceDifferenceWithPivot());
        }
        return comparatorPrices;
    }
}
