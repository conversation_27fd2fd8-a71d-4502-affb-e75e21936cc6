package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.deeplink.DeepLinkGenerationRequest;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.response.deeplink.DeepLinkGenerationResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;

import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.exception.ValidationException;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.ValidationErrors;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.OrchSearchHotelsResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;

import java.util.Map;
import org.slf4j.MDC;

@Service
public class DeepLinkGenerationService {

    private static final Logger logger = LoggerFactory.getLogger(DeepLinkGenerationService.class);

    @Autowired
    private Utility utility;

    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
    private SearchHotelsFactory searchHotelsFactory;

    @Value("${root.level.deeplink.url}")
    private String rootLevelDeeplinkUrl;
    

    @Value("${root.level.deeplink.url.happay}")
    private String rootLevelDeeplinkHappayUrl;




    public DeepLinkGenerationResponse generateDeepLink(DeepLinkGenerationRequest request) throws ClientGatewayException {
        try {
            validateDeepLinkRequest(request);
            
            String deeplink = buildListingDeepLink(request);
            
            DeepLinkGenerationResponse response = new DeepLinkGenerationResponse();
            response.setData(deeplink);
            return response;
            
        } catch (Exception e) {
            logger.error("Error generating listing deeplink", e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    private String getTemplateUrlForRequest(DeepLinkGenerationRequest request) {
        if(request.getRequestDetails().getMyBizFlowIdentifier() != null && Constants.HAPPAY.equalsIgnoreCase(request.getRequestDetails().getMyBizFlowIdentifier())){
            return rootLevelDeeplinkHappayUrl;
        }
        return rootLevelDeeplinkUrl;
    }

    private String buildListingDeepLink(DeepLinkGenerationRequest request) {
        String deeplink = getTemplateUrlForRequest(request);
        // Get the appropriate transformer based on client type
        String clientType = getClientTypeFromRequest(request);
        OrchSearchHotelsResponseTransformer transformer = searchHotelsFactory.getSearchHotelsResponseService(clientType);
        
        // Cast DeepLinkGenerationRequest to SearchHotelsRequest since it extends it
        SearchHotelsRequest searchRequest = (SearchHotelsRequest) request;
        String listingSharingUrl = transformer.prepareListingSharingUrl(searchRequest, deeplink, false, false, null);
        listingSharingUrl = transformer.appendAreaOrPoiUrlParameter(listingSharingUrl, searchRequest);
        listingSharingUrl += getRscValue(request.getSearchCriteria(), request.getExpDataMap());
        if(request.getRequestDetails().getMyBizFlowIdentifier() != null){
            listingSharingUrl += getQueryParameter(Constants.MYBIZ_FLOW_IDENTIFIER, request.getRequestDetails().getMyBizFlowIdentifier());
        }
        if(request.getRequestDetails().getRequisitionID() != null){
            listingSharingUrl += getQueryParameter(Constants.REQUISITION_ID, request.getRequestDetails().getRequisitionID());
        }

        return listingSharingUrl;
    }



    private String getClientTypeFromRequest(DeepLinkGenerationRequest request) {
        if (request != null && request.getDeviceDetails() != null) {
            String deviceType = request.getDeviceDetails().getDeviceType();
            if (StringUtils.isNotEmpty(deviceType)) {
                return deviceType.toUpperCase();
            }
        }
        // Default to DESKTOP if no device type is specified
        return "DESKTOP";
    }

    private String getRscValue(SearchHotelsCriteria searchCriteria, Map<String, String> expData) {
        // Handle RSC value if room stay candidates are distributed
        if (searchCriteria != null && utility.isDistributeRoomStayCandidates(searchCriteria.getRoomStayCandidates(), expData)) {
            String rscValue = utility.buildRscValue(searchCriteria.getRoomStayCandidates());
            if (StringUtils.isNotEmpty(rscValue)) {
                return getQueryParameter(Constants.RSC, rscValue);
            }
        }
        return "";
    }

    private String getQueryParameter(String key, String value) {
        return Constants.AMP + key + Constants.EQUI + value;
    }

    private void validateDeepLinkRequest(DeepLinkGenerationRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException(DependencyLayer.CLIENTGATEWAY, ErrorType.VALIDATION,
                    ValidationErrors.INVALID_REQUEST.getErrorCode(), 
                    "DeepLink request cannot be null");
        }
        
        SearchHotelsCriteria criteria = request.getSearchCriteria();
        if (criteria == null) {
            throw new ValidationException(DependencyLayer.CLIENTGATEWAY, ErrorType.VALIDATION,
                    ValidationErrors.INVALID_REQUEST.getErrorCode(), 
                    "Search criteria is required for deep link generation");
        }

        // Validate only truly required fields - handle optional fields gracefully
        if (StringUtils.isEmpty(criteria.getCheckIn())) {
            throw new ValidationException(DependencyLayer.CLIENTGATEWAY, ErrorType.VALIDATION,
                    ValidationErrors.INVALID_REQUEST.getErrorCode(), 
                    "Check-in date is required for deep link generation");
        }

        if (StringUtils.isEmpty(criteria.getCheckOut())) {
            throw new ValidationException(DependencyLayer.CLIENTGATEWAY, ErrorType.VALIDATION,
                    ValidationErrors.INVALID_REQUEST.getErrorCode(), 
                    "Check-out date is required for deep link generation");
        }

        // Validate room stay candidates
        if (CollectionUtils.isEmpty(criteria.getRoomStayCandidates())) {
            throw new ValidationException(DependencyLayer.CLIENTGATEWAY, ErrorType.VALIDATION,
                    ValidationErrors.INVALID_REQUEST.getErrorCode(), 
                    "At least one room stay candidate is required for deep link generation");
        }

        // Validate at least one adult per room
        for (RoomStayCandidate room : criteria.getRoomStayCandidates()) {
            if (room.getAdultCount() == null || room.getAdultCount() < 1) {
                throw new ValidationException(DependencyLayer.CLIENTGATEWAY, ErrorType.VALIDATION,
                        ValidationErrors.INVALID_REQUEST.getErrorCode(), 
                        "Each room must have at least 1 adult for deep link generation");
            }
        }
    }
}