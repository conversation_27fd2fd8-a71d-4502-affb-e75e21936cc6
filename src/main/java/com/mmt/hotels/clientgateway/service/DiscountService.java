package com.mmt.hotels.clientgateway.service;

import java.util.HashMap;
import java.util.Map;

import com.mmt.hotels.clientgateway.util.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.ValidateCouponRequest;
import com.mmt.hotels.clientgateway.response.ValidateCouponResponseBody;
import com.mmt.hotels.clientgateway.restexecutors.DiscountServiceExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.DiscountServiceFactory;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.model.request.UserLocation;
import com.mmt.hotels.model.request.ValidateCouponRequestBody;
import com.mmt.hotels.model.response.discount.ValidateCouponResponse;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

@Component
public class DiscountService {
	
    private static final Logger LOGGER = LoggerFactory.getLogger(DiscountService.class);
    
	@Autowired
	private DiscountServiceFactory discountServiceFactory;
	
	@Autowired
	private DiscountServiceExecutor discountServiceExecutor;
	
    @Autowired
    private MetricErrorLogger metricErrorLogger;
    
	@Autowired
	private ObjectMapperUtil objectMapperUtil;
	
	@Autowired
	private CommonHelper commonHelper;

	
	public ValidateCouponResponseBody validateCoupon(ValidateCouponRequest validateCouponRequest,  Map<String, String[]> parameterMap,
			Map<String, String> httpHeaderMap, String client) throws ClientGatewayException{
		
		ValidateCouponResponseBody response;
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put(OS,httpHeaderMap.get(OS));
		headerMap.put(ORG,httpHeaderMap.get(ORG));
		headerMap.put(LANGUAGE,httpHeaderMap.get(LANGUAGE));
		headerMap.put(USER_AGENT,httpHeaderMap.get(USER_AGENT));
		
		try {
        	String request = objectMapperUtil.getJsonFromObject(validateCouponRequest, DependencyLayer.CLIENTGATEWAY);
        	LOGGER.warn("Client Request for Validate Coupon:- " + request);
			ValidateCouponRequestBody req  = discountServiceFactory.getRequestService(client).convertValidateCouponRequest(validateCouponRequest);
			/*
			 * HTL-41137 If user location details like city,state and country
		     * is empty in the request,set the value from Akamai Headers.
			 */
			if(req.getUserLocation()==null) {
				UserLocation userLocation = commonHelper.buildUserLocationFromHeader(httpHeaderMap);
				req.setUserLocation(userLocation);
			}
			ValidateCouponResponse validateCouponResponse = discountServiceExecutor.getValidateCouponResponse(req,parameterMap,headerMap);

			boolean showBnplCard = Utility.isShowBnplCard(validateCouponRequest.getFeatureFlags());
			response = discountServiceFactory.getResponseService(client).convertValidateCouponResponse(validateCouponResponse, validateCouponRequest.getExpData(), validateCouponRequest.getCountryCode(), showBnplCard);
			response.setCurrency(validateCouponResponse.getCurrency());
		} catch (Exception e) {
        	if (e instanceof ErrorResponseFromDownstreamException) {
        		LOGGER.error("error occurred in validateCoupon: " + e.getMessage());
        		LOGGER.debug("error occurred in validateCoupon: " + e.getMessage(), e);
        	}else 
        		LOGGER.error("error occurred in validateCoupon: " + e.getMessage(), e);
        	
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
		}
		
		return response;
	}

}
