package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.ValidationErrors;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.exception.ValidationException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.modification.ProBookingRequest;
import com.mmt.hotels.clientgateway.request.modification.RatePreviewRequest;
import com.mmt.hotels.clientgateway.response.cbresponse.CGServerResponse;
import com.mmt.hotels.clientgateway.response.modification.ProBookingResponse;
import com.mmt.hotels.clientgateway.response.modification.RatePreviewResponse;
import com.mmt.hotels.clientgateway.restexecutors.AvailRoomsExecutor;
import com.mmt.hotels.clientgateway.restexecutors.CorporateExecutor;
import com.mmt.hotels.clientgateway.restexecutors.PaymentExecutor;
import com.mmt.hotels.clientgateway.restexecutors.SearchRoomsExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.BookingModificationFactory;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.UserGlobalInfo;
import com.mmt.hotels.model.request.corporate.InitApprovalRequest;
import com.mmt.hotels.model.request.payment.BeginCheckoutReqBody;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import org.apache.commons.collections.CollectionUtils;
import org.codehaus.plexus.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;

import static com.mmt.hotels.clientgateway.constants.Constants.ENTITY_NAME;
import static com.mmt.hotels.clientgateway.constants.Constants.USER_COUNTRY;

@Component
public class BookingModificationService {

    @Autowired
    CommonHelper commonHelper;

    @Autowired
    BookingModificationFactory bkgModFactory;

    @Autowired
    SearchRoomsExecutor searchRoomsExecutor;

    @Autowired
    AvailRoomsExecutor availRoomsExecutor;

    @Autowired
    CorporateExecutor corporateExecutor;

    @Autowired
    PaymentExecutor paymentExecutor;

    @Autowired
    MetricErrorLogger metricErrorLogger;

    private static final Logger logger = LoggerFactory.getLogger(BookingModificationService.class);


    public RatePreviewResponse fetchDetailPrice(RatePreviewRequest rateReviewRequest , Map<String,String> httpHeaderMap){
        try {

            if(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(rateReviewRequest.getIdContext()) &&  ( CollectionUtils.isEmpty(rateReviewRequest.getTravellerEmailCommIds()) || StringUtils.isBlank(rateReviewRequest.getTravellerEmailCommIds().get(0)))){
                throw new ValidationException(DependencyLayer.CLIENTS, ErrorType.VALIDATION,
                        ValidationErrors.EMPTY_TRAVELLER_ID.getErrorCode(), ValidationErrors.EMPTY_TRAVELLER_ID.getErrorMsg());
            }

            CommonModifierResponse commonModifierResponse = commonHelper.processRequestForBkgMod( httpHeaderMap,rateReviewRequest,Constants.PAGE_CONTEXT_DETAIL);
            PriceByHotelsRequestBody priceByHotelsRequestBody = bkgModFactory.
                    getRequestService(rateReviewRequest.getClient()).convertPriceRequest(rateReviewRequest, commonModifierResponse,Constants.PAGE_CONTEXT_DETAIL);
            if (httpHeaderMap.containsKey(ENTITY_NAME)) {
                priceByHotelsRequestBody.setUserGlobalInfo(getUserGlobalInfoObj(httpHeaderMap.get(ENTITY_NAME), httpHeaderMap.get(USER_COUNTRY)));
            }
            Future<RoomDetailsResponse> roomDetailsResponse = null;
            try {
                roomDetailsResponse = searchRoomsExecutor.getRoomPrices(priceByHotelsRequestBody,null, httpHeaderMap, null);
            } catch (Exception ex){
                throw ex;
            }
            return bkgModFactory.getResponseService(rateReviewRequest.getClient()).convertPriceResponse(roomDetailsResponse.get(),rateReviewRequest,Constants.PAGE_CONTEXT_DETAIL,commonModifierResponse );

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in BkgMod fetchDetail Price: " + e.getMessage());
            } else
                logger.error("error occurred in BkgMod fetchDetail Price: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            RatePreviewResponse response =  new RatePreviewResponse();
            response.setErrorMessage(exceptionHandlerResponse.getMetricError().getErrorMessage());
            response.setErrorCode(exceptionHandlerResponse.getMetricError().getErrorCode());
            return response;
        }

    }

    public UserGlobalInfo getUserGlobalInfoObj(String entity, String userCountry) {
        UserGlobalInfo userGlobalInfo = new UserGlobalInfo();
        userGlobalInfo.setEntityName(entity);
        userGlobalInfo.setUserCountry(userCountry);
        return userGlobalInfo;
    }

    public RatePreviewResponse fetchReviewPrice(RatePreviewRequest rateReviewRequest , Map<String,String> httpHeaderMap){
        try {

            if(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(rateReviewRequest.getIdContext()) &&  ( CollectionUtils.isEmpty(rateReviewRequest.getTravellerEmailCommIds()) || StringUtils.isBlank(rateReviewRequest.getTravellerEmailCommIds().get(0)))){
                throw new ValidationException(DependencyLayer.CLIENTS, ErrorType.VALIDATION,
                        ValidationErrors.EMPTY_TRAVELLER_ID.getErrorCode(), ValidationErrors.EMPTY_TRAVELLER_ID.getErrorMsg());
            }
            CommonModifierResponse commonModifierResponse = commonHelper.processRequestForBkgMod( httpHeaderMap,rateReviewRequest,Constants.PAGE_CONTEXT_REVIEW);
            PriceByHotelsRequestBody priceByHotelsRequestBody = bkgModFactory.
                    getRequestService(rateReviewRequest.getClient()).convertPriceRequest(rateReviewRequest, commonModifierResponse,Constants.PAGE_CONTEXT_REVIEW);
            if (httpHeaderMap.containsKey(ENTITY_NAME)) {
                priceByHotelsRequestBody.setUserGlobalInfo(getUserGlobalInfoObj(httpHeaderMap.get(ENTITY_NAME), httpHeaderMap.get(USER_COUNTRY)));
            }
            RoomDetailsResponse roomDetailsResponse = null;
            try {
                roomDetailsResponse = availRoomsExecutor.availRooms(priceByHotelsRequestBody,null, httpHeaderMap);
            } catch (Exception ex){
                throw ex;
            }
            return bkgModFactory.getResponseService(rateReviewRequest.getClient()).convertPriceResponse(roomDetailsResponse,rateReviewRequest,Constants.PAGE_CONTEXT_REVIEW ,commonModifierResponse);

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in BkgMod fetchDetail Price: " + e.getMessage());
            } else
                logger.error("error occurred in BkgMod fetchDetail Price: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            RatePreviewResponse response =  new RatePreviewResponse();
            response.setErrorMessage(exceptionHandlerResponse.getMetricError().getErrorMessage());
            response.setErrorCode(exceptionHandlerResponse.getMetricError().getErrorCode());
            return response;
        }
    }

    public ProBookingResponse createProBooking(ProBookingRequest proBookingRequest , Map<String,String> httpHeaderMap){
        try {

            if(StringUtils.isNotBlank(proBookingRequest.getWorkflowStatus())
                   && Constants.WORKFLOW_PENDING.equalsIgnoreCase(proBookingRequest.getWorkflowStatus())){
                InitApprovalRequest initApprovalRequest = bkgModFactory.
                        getRequestService(proBookingRequest.getPaymentDetail().getChannel()).convertRequestApproval(proBookingRequest, httpHeaderMap);
               CGServerResponse response ;
                try {
                     response = corporateExecutor.requestApproval(initApprovalRequest, null,initApprovalRequest.getCorrelationKey(),  httpHeaderMap);
                } catch (Exception ex) {
                    throw ex;
                }
                return bkgModFactory.getResponseService(proBookingRequest.getPaymentDetail().getChannel()).convertRequestApprovalResponse(response,proBookingRequest.getHashKey());

            }else{
                BeginCheckoutReqBody paymentRequest = bkgModFactory.
                        getRequestService(proBookingRequest.getPaymentDetail().getChannel()).convertPaymentRequest(proBookingRequest, httpHeaderMap);
                long startTime = new Date().getTime();
                PaymentCheckoutResponse paymentResponse = null;
                try {
                    paymentResponse = paymentExecutor.beginPaymentCheckoutForModifyBooking(paymentRequest, httpHeaderMap);
                } catch (Exception ex) {
                    throw ex;
                }
                return bkgModFactory.getResponseService(proBookingRequest.getPaymentDetail().getChannel()).convertPaymentRsponse(paymentResponse,proBookingRequest.getHashKey());
            }

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in BkgMod fetchDetail Price: " + e.getMessage());
            } else
                logger.error("error occurred in BkgMod fetchDetail Price: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            ProBookingResponse response =  new ProBookingResponse();
            response.setErrorMessage(exceptionHandlerResponse.getMetricError().getErrorMessage());
            response.setErrorCode(exceptionHandlerResponse.getMetricError().getErrorCode());
            return response;
        }
    }


}
