package com.mmt.hotels.clientgateway.service;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class LogService {
    private static final Logger logger = LoggerFactory.getLogger(LogService.class);

    @Value("${logging.file.path:/opt/logs/tomcat/hotels-clientgateway.log}")
    private String logFilePath;

    public String getLogs(String correlationKey, String query, String api) {
        if(StringUtils.isBlank(correlationKey)){
            return "";
        }
        try {
            Path logPath = Paths.get(logFilePath);
            if (!Files.exists(logPath)) {
                String message = String.format("Log file not found at path: %s", logFilePath);
                logger.warn(message);
                return message;
            }

            try (Stream<String> lines = Files.lines(logPath)) {
                Stream<String> logs = lines.filter(line -> line.contains("unique_request_id:" + correlationKey));
                if(StringUtils.isNotBlank(api)){
                    logs = logs.filter(line -> (line.contains("curl:") && line.contains(api)));
                }
                if (StringUtils.isNotBlank(query)) {
                    logs = logs.filter(line -> line.contains(query));
                }
                List<String> logsList =  logs.collect(Collectors.toList());
                return String.join("\n<br/><br/> ", logsList);
            }
        } catch (IOException e) {
            logger.error("Error reading log file: {}", e.getMessage(), e);
            return "";
        }
    }
} 