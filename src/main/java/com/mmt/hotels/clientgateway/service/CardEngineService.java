package com.mmt.hotels.clientgateway.service;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.restexecutors.CardEngineExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.transformer.request.SearchHotelsRequestTransformer;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.orchestrator.enums.SubPageContext;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;

@Component
public class CardEngineService {

    private static final Logger logger = LoggerFactory.getLogger(CardEngineService.class);
    @Autowired
    CardEngineExecutor cardEngineExecutor;
    @Autowired
    MetricErrorLogger metricErrorLogger;
    @Autowired
    ObjectMapperUtil objectMapperUtil;
    @Autowired
    @Qualifier("listingThreadPool")
    private ThreadPoolTaskExecutor listingThreadPool;

    @Autowired
    SearchHotelsRequestTransformer searchHotelsRequestTransformer;

    @Autowired
    CommonHelper commonHelper;

    @Autowired
    private SearchHotelsFactory searchHotelsFactory;

    public String getCard(SearchHotelsRequest searchHotelsRequest, Map<String, String> headerMap, @Nullable CommonModifierResponse commonModifierResponse) throws ClientGatewayException {
        // Building searchHotelRequest into searchWrapperInputRequest;
        try {
            // Step 1:- convert SearchHotelRequest to searchWrapperInputRequest
            if(commonModifierResponse == null) {
                commonModifierResponse = commonHelper.processRequest(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest, headerMap);
            }
            SearchWrapperInputRequest searchWrapperInputRequest = searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())
                    .convertSearchRequest(searchHotelsRequest, commonModifierResponse);
            // Step 2:- calling Card-Engine-Service
            return cardEngineExecutor.getCard(searchWrapperInputRequest);
        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in getCard: " + e.getMessage());
                logger.debug("error occurred in getCard: " + e.getMessage(), e);
            } else
                logger.error("error occurred in getCard: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public Future<String> executeCardEngineServiceAsync(SearchHotelsRequest searchHotelsRequest, String correlationKey, Map<String, String> headerMap, CommonModifierResponse commonModifierResponse) {
        if(searchHotelsRequest != null && searchHotelsRequest.getSearchCriteria() != null && StringUtils.isNotEmpty(searchHotelsRequest.getSearchCriteria().getLastHotelId())){
            return listingThreadPool.submit(() -> StringUtils.EMPTY);
        }
        Map<String, String> mdcMap = MDC.getCopyOfContextMap();
        return listingThreadPool.submit(() -> {
            //logger.warn("listingThreadPool executeCardEngineService activeCount : {}", listingThreadPool.getActiveCount());
            logger.warn("listingThreadPool executeCardEngineService activeCount : " + listingThreadPool.getActiveCount() + " "
                    + listingThreadPool.getThreadPoolExecutor().getQueue().remainingCapacity() + " "
                    + listingThreadPool.getThreadPoolExecutor().getLargestPoolSize() + " "
                    + listingThreadPool.getThreadPoolExecutor().getTaskCount() + " "
                    + listingThreadPool.getThreadPoolExecutor().getCompletedTaskCount());
            long startTime = System.currentTimeMillis();
            try {
                if (mdcMap != null) {
                    MDC.setContextMap(mdcMap);
                }
                searchHotelsRequest.setCorrelationKey(correlationKey);
                return getCard(searchHotelsRequest, headerMap, commonModifierResponse);
            } catch (Exception ex) {
                logger.error("error occurred in getCard: " + ex.getMessage());
                logger.error("Exception while getting cards from card-Engine for locationId {}", searchHotelsRequest.getSearchCriteria().getLocationId());
            }finally {
                logger.warn("Time taken by executeCardEngineService {} millis", System.currentTimeMillis() - startTime);
                MDC.clear();
            }
            return null;
        });
    }

//    public Future<String> executeCardEngineServiceAsync(SearchWrapperInputRequest searchWrapperInputRequest) {
//        Map<String, String> mdcMap = MDC.getCopyOfContextMap();
//        return listingThreadPool.submit(() -> {
//            long startTime = System.currentTimeMillis();
//            try {
//                if (mdcMap != null) {
//                    MDC.setContextMap(mdcMap);
//                }
//                return cardEngineExecutor.getCard(searchWrapperInputRequest);
//            } catch (Exception ex) {
//                logger.error("error occurred in getCard: " + ex.getMessage());
//                logger.error("Exception while getting cards from card-Engine for locationId {}", searchWrapperInputRequest.getLocationId());
//            }finally {
//                logger.debug("Time taken by executeCardEngineService {} millis", System.currentTimeMillis() - startTime);
//            }
//            return null;
//        });
//    }

    public void setCardIdInSearchHotelRequest(SearchHotelsRequest searchHotelsRequest) {

            SubPageContext subPageContext = SubPageContext.resolve(searchHotelsRequest.getRequestDetails().getSubPageContext());
            switch (subPageContext) {
                case LISTING_COLLECTION:
                    searchHotelsRequest.setCardId(Constants.DPT_COLLECTIONS);
                    break;
                default:
                    searchHotelsRequest.setCardId(Constants.SEASON_CARD_ID);
                    break;
            }
    }
}
