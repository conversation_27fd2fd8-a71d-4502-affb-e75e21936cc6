package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.ValidationErrors;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.hostcalling.HostCallingInitiateRequestBody;
import com.mmt.hotels.clientgateway.restexecutors.HostCallingExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.HostCallingFactory;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.Utility;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.CHAIN_NAME_PLACEHOLDER;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.HOST_CALLING_DOWNSTREAM_ERROR_MESSAGE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.HOST_CALLING_MOBILE_MISSING_ERROR_MESSAGE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.MISSED_CALL_MESSAGE_CONSTANT;

@Component
public class HostCallingService {

    private static final Logger logger = LoggerFactory.getLogger(HostCallingService.class);

    @Autowired
    private HostCallingExecutor hostCallingExecutor;

    @Autowired
    private HostCallingFactory hostCallingFactory;

    @Autowired
    private CommonHelper commonHelper;

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    private OrchHostCallingService orchHostCallingService;

    @Autowired
    private Utility utility;

    @Autowired
    private MetricErrorLogger metricErrorLogger;

    public com.mmt.hotels.clientgateway.response.hostcalling.HostCallingInitiateResponse initiateHostCalling(HostCallingInitiateRequestBody request, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {
            logger.info("Initiating host calling for hotel: {}, visitor: {}", 
                request.getSearchCriteria().getHotelId(), request.getRequestDetails().getVisitorId());

            // Process common request - same as static detail
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(request.getSearchCriteria(),request,httpHeaderMap);
            boolean rearchFlow = utility.isDetailPageRearchFlow(true, request.getRequestDetails().getRequestId(), commonModifierResponse.getExpDataMap());
            
            // Check if mobile number is missing and throw error with polyglot message
            if (commonModifierResponse.getMobile() == null || commonModifierResponse.getMobile().trim().isEmpty()) {
                logger.warn("Mobile number is missing for hotel: {}, visitor: {}", 
                    request.getSearchCriteria().getHotelId(), request.getRequestDetails().getVisitorId());
                
                String mobileErrorMessage = polyglotService.getTranslatedData(HOST_CALLING_MOBILE_MISSING_ERROR_MESSAGE);
                throw new ErrorResponseFromDownstreamException(DependencyLayer.CLIENTGATEWAY, ErrorType.VALIDATION,
                        ValidationErrors.INVALID_REQUEST.getErrorCode(), mobileErrorMessage);
            }

            if (rearchFlow) {
                return orchHostCallingService.hostCalling(request, parameterMap, httpHeaderMap, commonModifierResponse);
            }
            
            // Use factory pattern to get client-specific transformer and convert request
            com.mmt.hotels.pojo.request.detail.mob.HostCallingInitiateRequestBody hesRequest = 
                hostCallingFactory.getRequestService(request.getClient())
                    .convertHostCallingRequest(request, commonModifierResponse);

            // Set mobile from CommonModifierResponse
            hesRequest.setMobile(commonModifierResponse.getMobile());

            // Call HES using the correct method from executor
            com.mmt.hotels.pojo.response.detail.HostCallingInitiateResponse hesResponse = hostCallingExecutor.getEntityServiceResponse(hesRequest, parameterMap, httpHeaderMap);

            // Check if HES returned an error BEFORE proceeding with response transformation
            if (hesResponse != null && hesResponse.getErrorEntity() != null) {
                logger.warn("HES returned error for hotel: {}, errorCode: {}, message: {}", 
                    request.getSearchCriteria().getHotelId(),
                    hesResponse.getErrorEntity().getErrorCode(),
                    hesResponse.getErrorEntity().getMsg());
                
                // Get polyglot error message, fallback to original message if polyglot fails
                String errorMessage = polyglotService.getTranslatedData(HOST_CALLING_DOWNSTREAM_ERROR_MESSAGE);
                
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,
                    hesResponse.getErrorEntity().getErrorCode(), errorMessage);
            }

            // Use factory pattern for response transformation - same as static detail
            com.mmt.hotels.clientgateway.response.hostcalling.HostCallingInitiateResponse cgResponse = 
                hostCallingFactory.getResponseService(request.getClient()).convertHostCallingResponse(hesResponse, request.getClient(), request.getRequestDetails().getVisitorId());

            // Add missed call message using polyglot service
            if (cgResponse != null && StringUtils.isNotEmpty(cgResponse.getChainName())) {
                String missedCallMessage = getMissedCallMessage(cgResponse.getChainName());
                cgResponse.setMissedCallMessage(missedCallMessage);
            }

            return cgResponse;

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in initiateHostCalling: " + e.getMessage());
                logger.debug("error occurred in initiateHostCalling: " + e.getMessage(), e);
            } else
                logger.error("error occurred in initiateHostCalling: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    /**
     * Gets missed call message using polyglot service
     * Similar to the original implementation
     */
    private String getMissedCallMessage(String chainName) {
        try {
            // Get the base message from polyglot
            String baseMessage = polyglotService.getTranslatedData(MISSED_CALL_MESSAGE_CONSTANT);

            if (StringUtils.isNotEmpty(baseMessage) && StringUtils.isNotEmpty(chainName)) {
                // Replace the chainName placeholder with actual chain name
                return baseMessage.replace(CHAIN_NAME_PLACEHOLDER, chainName);
            }

            return baseMessage;
        } catch (Exception e) {
            return "Host calling is currently unavailable. Please try again during operational hours.";
        }
    }
}