package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.exception.AuthenticationException;
import com.mmt.hotels.clientgateway.grpcexecutor.PricingEngineExecutor;
import com.mmt.hotels.clientgateway.request.EMIDetailRequest;
import com.mmt.hotels.clientgateway.request.UpdatedEmiRequest;
import com.mmt.hotels.clientgateway.response.emi.EMIDetailResponse;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.transformer.factory.EMIFactory;
import com.mmt.hotels.clientgateway.transformer.factory.SearchRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.response.EmiDetailResponseTransformer;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.emi.detail.EmiDetailRequest;
import com.mmt.hotels.emi.detail.EmiDetailResponse;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.restexecutors.EmiDetailExecutor;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.model.request.emi.UpdateEmiDetailRequest;

import javax.validation.Valid;
import java.util.Map;

@Component
public class EmiService {

	@Autowired
	private CommonHelper commonHelper;

	@Autowired
	private EmiDetailExecutor emiExecutor;

	@Autowired
	private EMIFactory emiFactory;

	@Autowired
	PricingEngineExecutor pricingEngineExecutor;

	@Autowired
	EmiDetailResponseTransformer emiDetailResponseTransformer;

	@Autowired
	private MetricErrorLogger metricErrorLogger;

	@Autowired
	private SearchRoomsFactory searchRoomsFactory;

	private static final Logger logger = LoggerFactory.getLogger(EmiService.class);

	public String getUpdateEmiResponse(UpdateEmiDetailRequest updateEmiDetailRequest, Map<String, String[]> parameterMap, String correlationKey, Map<String, String> httpHeaderMap) throws ClientGatewayException {
		try {
			updateUserDetails(updateEmiDetailRequest, correlationKey, httpHeaderMap);
			return emiExecutor.getUpdatedEmiDetails(updateEmiDetailRequest, parameterMap, httpHeaderMap, correlationKey);
		} catch (Throwable e) {
			if(e instanceof AuthenticationException){
				return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
			}
			else if (e instanceof ErrorResponseFromDownstreamException) {
				logger.error("error occured in fetch get updated emi detail: " + e.getMessage());
        		logger.debug("error occured in fetch get updated emi detail: " + e.getMessage(), e);
        	}else 
        		logger.error("error occured in fetch get updated emi detail: " + e.getMessage(), e);
			
			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}

	private void updateUserDetails(UpdateEmiDetailRequest updateEmiDetailRequest, String correlationKey, Map<String, String> httpHeaderMap) throws ClientGatewayException {
		String mmtAuth = commonHelper.getMMTAuth(httpHeaderMap, updateEmiDetailRequest.getBookingDevice());
		UserServiceResponse userServiceRsp = commonHelper.getUserDetails(mmtAuth, null, null, null, correlationKey, null, updateEmiDetailRequest.getSiteDomain(),null, httpHeaderMap);
		if (userServiceRsp != null && userServiceRsp.getResult() != null
				&& userServiceRsp.getResult().getExtendedUser() != null) {
			ExtendedUser user = userServiceRsp.getResult().getExtendedUser();
			updateEmiDetailRequest.setUuid(user.getUuid());
			updateEmiDetailRequest.setProfileType(user.getProfileType());

		}
	}

	public SearchRoomsResponse updatedEmiDetails(@Valid UpdatedEmiRequest updatedEmiRequest,Map<String, String[]> parameterMap,
												 String correlationKey, Map<String, String> httpHeaderMap) throws ClientGatewayException {
		try {
			CommonModifierResponse commonModifierResponse = commonHelper.processRequest(updatedEmiRequest.getSearchCriteria(), updatedEmiRequest, httpHeaderMap);
			UpdateEmiDetailRequest updateEmiDetailRequest = emiFactory.
					getRequestService(updatedEmiRequest.getClient()).convertEmiRequest(updatedEmiRequest, commonModifierResponse);
			RoomDetailsResponse roomDetailsResponse = emiExecutor.getUpdatedEmi(updateEmiDetailRequest, parameterMap, httpHeaderMap, correlationKey);
			return searchRoomsFactory.getResponseService(updatedEmiRequest.getClient()).convertSearchRoomsResponse(roomDetailsResponse, null, null, null, null, updatedEmiRequest.getSearchCriteria(),null,null,updatedEmiRequest.getRequestDetails());
		} catch (Throwable e) {
			if (e instanceof ErrorResponseFromDownstreamException) {
				logger.error("error occured in searchRooms: " + e.getMessage());
				logger.debug("error occured in searchRooms: " + e.getMessage(), e);
			} else
				logger.error("error occured in searchRooms: " + e.getMessage(), e);

			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}

	public EMIDetailResponse fetchEmiDetails(EMIDetailRequest emiDetailsRequest) throws ClientGatewayException {
		EMIDetailResponse emiDetailResponseCG = null;
		try {
			EmiDetailRequest emiDetailRequest = emiFactory.getRequestService(emiDetailsRequest.getClient()).buildEmiDetailRequest(emiDetailsRequest);
			EmiDetailResponse emiDetailResponse = pricingEngineExecutor.executeEmiDetail(emiDetailRequest);
			emiDetailResponseCG =  emiDetailResponseTransformer.convertEmiDetailResponse(emiDetailResponse);
		} catch (Throwable e) {
			logger.error("error occurred in fetch emi detail: {}", e.getMessage(), e);
			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
		return emiDetailResponseCG;
	}
}
