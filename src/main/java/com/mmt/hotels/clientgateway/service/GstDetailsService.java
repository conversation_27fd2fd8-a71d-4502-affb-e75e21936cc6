package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.BaseSearchRequest;
import com.mmt.hotels.clientgateway.request.gstDetails.SaveGstDetailsRequest;
import com.mmt.hotels.clientgateway.request.gstDetails.TravellerGstDetails;
import com.mmt.hotels.clientgateway.response.gstDetails.GstDetailsResponse;
import com.mmt.hotels.clientgateway.restexecutors.MyPartnerCoreRestExecutor;
import com.mmt.hotels.clientgateway.transformer.request.GstDetailsRequestTransformer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class GstDetailsService {

    @Autowired
    private CommonHelper commonHelper;

    @Autowired
    private GstDetailsRequestTransformer gstDetailsRequestTransformer;

    @Autowired
    private MyPartnerCoreRestExecutor myPartnerCoreRestExecutor;

    public GstDetailsResponse<TravellerGstDetails> saveGstDetails(SaveGstDetailsRequest saveGstDetailsRequest, Map<String, String[]> parameterMap, Map<String, String> headerMap, String client) throws ClientGatewayException {
        CommonModifierResponse commonModifierResponse = commonHelper.processRequest(null, saveGstDetailsRequest, headerMap);

        TravellerGstDetails travellerGstDetails = gstDetailsRequestTransformer.convertToTravellerGstDetails(saveGstDetailsRequest, commonModifierResponse);

        return myPartnerCoreRestExecutor.saveGstDetails(travellerGstDetails, parameterMap, headerMap, client);
    }


    public GstDetailsResponse<List<TravellerGstDetails>> getGstDetails(BaseSearchRequest getGstDetailsRequest, Map<String, String[]> parameterMap, Map<String, String> headerMap, String client) throws ClientGatewayException {
        CommonModifierResponse commonModifierResponse = commonHelper.processRequest(null, getGstDetailsRequest, headerMap);

        if (null == commonModifierResponse || null == commonModifierResponse.getExtendedUser()) {
            log.error("Could not fetch uuid for request: {}", getGstDetailsRequest);
            return GstDetailsResponse.<List<TravellerGstDetails>>builder()
                    .success(false)
                    .error(Constants.ERROR_MESSAGE_COULD_NOT_FETCH_UUID)
                    .build();
        }

        String uuid = commonModifierResponse.getExtendedUser().getUuid();

        return myPartnerCoreRestExecutor.getGstDetails(uuid, parameterMap, headerMap, client);
    }
}
