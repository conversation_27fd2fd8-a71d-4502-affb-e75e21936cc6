package com.mmt.hotels.clientgateway.service;

import com.google.gson.JsonParseException;
import com.mmt.hotels.clientgateway.enums.AuthenticationErrors;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.PermissionErrors;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.response.HotelPermissions;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.json.JacksonJsonParser;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.restexecutors.MobLandingExecutor;
// import com.mmt.hotels.clientgateway.thirdparty.request.Hotels;
import com.mmt.hotels.clientgateway.thirdparty.request.HotelsPermission;
import com.mmt.hotels.clientgateway.thirdparty.request.PartnerAffiliateDetails;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.txn.Hotels;

import java.util.Map;

@Component
public class LandingService {

	@Autowired
	private MobLandingExecutor mobLandingExecutor;
	@Autowired
	private Utility utility;
	@Autowired
	CommonHelper commonHelper;

	public String getLatLngFromGooglePlaceId(String placeId, Double lat, Double lng) throws ClientGatewayException {
		return mobLandingExecutor.getLatLngFromGooglePlaceId(placeId, lat, lng);
	}

	private Hotels convertToHotels(HotelsPermission hotelsPermission) {
		Hotels hotels = new Hotels();
		hotels.setSearchEnabled(hotelsPermission.getSearchEnabled());
		hotels.setBookingEnabled(hotelsPermission.getBookingEnabled());
		hotels.setBnplEnabled(hotelsPermission.getBnplEnabled());
		return hotels;
	}

	private String extractUuidFromUserService(SearchWrapperInputRequest request, Map<String, String> headers)
			throws ClientGatewayException {
		try {
			UserServiceResponse response = commonHelper.getUserDetails(
					request.getMmtAuth(), request.getEmail(), request.getMobile(),
					request.getChannel(), request.getCorrelationKey(), request.getIdContext(),
					request.getSiteDomain(), null, headers);

			if (response == null || response.getResult() == null ||
					response.getResult().getExtendedUser() == null ||
					response.getResult().getExtendedUser().getUuid() == null) {
				throw new ClientGatewayException(
						DependencyLayer.USERSERVICE,
						ErrorType.DOWNSTREAM,
						AuthenticationErrors.UUID_NOT_FOUND.getErrorCode(),
						AuthenticationErrors.UUID_NOT_FOUND.getErrorMsg());
			}

			return response.getResult().getExtendedUser().getUuid();
		} catch (Exception e) {
			throw e;
		}
	}

	private HotelsPermission fetchHotelsPermission(String uuid) throws ClientGatewayException {
		try {
			String bookingType = "booking";
			String pageContext = "hotel";
			PartnerAffiliateDetails details = mobLandingExecutor.fetchCTAMoAffId(uuid, bookingType, pageContext);

			if (details == null || details.getData() == null ||
					details.getData().getPermissions() == null ||
					details.getData().getPermissions().getHotels() == null) {
				throw new ClientGatewayException(
						DependencyLayer.CLIENTGATEWAY,
						ErrorType.DOWNSTREAM,
						PermissionErrors.PERMISSIONS_NOT_FOUND.getErrorCode(),
						PermissionErrors.PERMISSIONS_NOT_FOUND.getErrorMsg());
			}

			return details.getData().getPermissions().getHotels();
		} catch (JsonParseException e) {
			throw new ClientGatewayException(
					DependencyLayer.CLIENTGATEWAY,
					ErrorType.DOWNSTREAM,
					PermissionErrors.PERMISSION_API_PARSE_FAILED.getErrorCode(),
					PermissionErrors.PERMISSION_API_PARSE_FAILED.getErrorMsg());
		} catch (Exception e) {
			throw e;
		}
	}

	public HotelPermissions getFeatureBasedAccessDetails(SearchWrapperInputRequest request, Map<String, String> headers)
			throws ClientGatewayException {
		try {
			String uuid = extractUuidFromUserService(request, headers);
			HotelsPermission hotelsPermission = fetchHotelsPermission(uuid);
			Hotels hotels = convertToHotels(hotelsPermission);
			return utility.buildHotelPermissions(hotels);
		} catch (Exception e) {
			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}
}