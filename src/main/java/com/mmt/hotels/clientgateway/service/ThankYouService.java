package com.mmt.hotels.clientgateway.service;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.MarshallingErrors;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.request.BaseRequest;
import com.mmt.hotels.clientgateway.request.ThankYouRequest;
import com.mmt.hotels.clientgateway.response.thankyou.ThankYouResponse;
import com.mmt.hotels.clientgateway.restexecutors.ThankYouExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.ThankYouFactory;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.response.base.FailureReason;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import com.mmt.hotels.model.response.txn.PersistanceMultiRoomResponseEntity;
import com.mmt.hotels.util.Tuple;
import com.mmt.propertymanager.config.PropertyManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.UnsupportedEncodingException;
import java.text.MessageFormat;
import java.util.*;

@Component
@RefreshScope
public class ThankYouService {

    @Value("${consul.enable}")
    private boolean consulFlag;

    @Autowired
    CommonConfigConsul commonConfigConsul;
    @Autowired
    private ThankYouExecutor thankYouExecutor;

    @Autowired
    private ThankYouFactory thankYouFactory;

    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
    private PropertyManager propertyManager;

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    private long thankYouTime;

    private static final Logger logger = LoggerFactory.getLogger(ThankYouService.class);

    @PostConstruct
    private void init() {
        try {
            if(consulFlag){
                thankYouTime = commonConfigConsul.getThankYouPageExpiry();
                logger.debug("Fetching values from commonConfig consul........  : "+thankYouFactory);
            }
            else {
                CommonConfig prop = propertyManager.getProperty("commonConfig", CommonConfig.class);
                prop.addPropertyChangeListener("thankYouPageExpiry", event -> {
                    thankYouTime = prop.thankYouPageExpiry();
                });
                thankYouTime = prop.thankYouPageExpiry();
            }
        } catch (Exception e) {
            logger.error("Error Occure in HotelTxnController ", e);
        }

    }

    public ThankYouResponse getThankYouResponse(ThankYouRequest thankYouRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {

            String [] thankYouPageContext = {Constants.PAGE_CONTEXT_THANK_YOU};
            Map<String, String[]> newParamMap = new HashMap<>();
            newParamMap.putAll(parameterMap);
            newParamMap.put(Constants.PAGE_CONTEXT, thankYouPageContext);
            //Adding pageContext as thankYou as we need this for pdt logging of thankYou page in getTxnData api at HES
            PersistanceMultiRoomResponseEntity txnData = getTxnData(thankYouRequest, newParamMap, httpHeaderMap);
            validateTTL(txnData);
            //convert to new format
            ThankYouResponse thankYouResponse = thankYouFactory.getResponseService(thankYouRequest.getClient()).convertThankYouResponse(txnData,null, httpHeaderMap);
            if(thankYouRequest != null && Constants.ANDROID.equalsIgnoreCase(thankYouRequest.getClient()) && thankYouResponse != null && thankYouResponse.getBookingDetails() != null){
                logger.warn(MessageFormat.format("Checkin date: {0} , Checkout date: {1}, txnKey: {2}",thankYouResponse.getBookingDetails().getCheckInDate(),thankYouResponse.getBookingDetails().getCheckOutDate(), thankYouRequest.getTxnKey()));
            }
            return thankYouResponse;
        } catch (Throwable e) {
            return handleThankYouException(e);
        }
    }


    public String getThankYouResponseOld(ThankYouRequest thankYouRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        String serverResponse = null;
        try {
            PersistanceMultiRoomResponseEntity txnData = getTxnData(thankYouRequest, parameterMap, httpHeaderMap);
            try{
                validateTTL(txnData);
            }catch (ClientGatewayException e){
                Tuple<ResponseErrors, FailureReason> responseErrorsFailureReasonTuple = ClientBackendUtility.setResponseErrors(CBError.NO_DATA_FOUND_THANKYOU);
                txnData.setResponseErrors(responseErrorsFailureReasonTuple.getX());
                txnData.setPersistedData(null);
            }
            serverResponse = objectMapperUtil.getJsonFromObject(txnData, DependencyLayer.CLIENTGATEWAY);
        } catch (Throwable e) {
            handleThankYouException(e);
        }
        return serverResponse;
    }

    private PersistanceMultiRoomResponseEntity getTxnData(ThankYouRequest thankYouRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException, UnsupportedEncodingException {
        PersistanceMultiRoomResponseEntity txnData = thankYouExecutor.getThankYouReponse(thankYouRequest, parameterMap, httpHeaderMap);
        return txnData;
    }

    private void validateTTL(PersistanceMultiRoomResponseEntity txnData) throws ClientGatewayException {
        long currentTime = new Date().getTime();
        if (txnData != null && txnData.getPersistedData() != null && txnData.getPersistedData().getTimeOfBooking() != null && txnData.getPersistedData().getTimeOfBooking() > 0 && (currentTime - txnData.getPersistedData().getTimeOfBooking()) > thankYouTime) {
            throw new ClientGatewayException(DependencyLayer.CLIENTGATEWAY, ErrorType.MARSHALLING, MarshallingErrors.NO_DATA_FOUND.getErrorCode(), MarshallingErrors.NO_DATA_FOUND.getErrorMsg());
        }
    }

    private ThankYouResponse handleThankYouException(Throwable e) throws ClientGatewayException {
        if (e instanceof ErrorResponseFromDownstreamException) {
            logger.error("error occured in fetch thankYou: " + e.getMessage());
            logger.debug("error occured in fetch thankYou: " + e.getMessage(), e);
        } else
            logger.error("error occured in fetch thankYou: " + e.getMessage(), e);

        ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
        metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
        throw exceptionHandlerResponse.getClientGatewayException();
    }

}
