package com.mmt.hotels.clientgateway.service;

import com.google.common.base.Enums;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.LocationType;
import com.mmt.hotels.clientgateway.exception.AuthenticationException;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.CalendarAvailabilityRequest;
import com.mmt.hotels.clientgateway.request.PlatformUgcCategoryRequest;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.request.StaticDetailRequest;
import com.mmt.hotels.clientgateway.request.UpdatePriceRequest;
import com.mmt.hotels.clientgateway.request.dayuse.DayUseRoomsRequest;
import com.mmt.hotels.clientgateway.request.wishlist.WishListedHotelsDetailRequest;
import com.mmt.hotels.clientgateway.response.CalendarAvailabilityResponse;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.rooms.UpdatePriceResponse;
import com.mmt.hotels.clientgateway.response.staticdetail.StaticDetailResponse;
import com.mmt.hotels.clientgateway.response.wishlist.WishListedHotelsDetailResponse;
import com.mmt.hotels.clientgateway.restexecutors.CalendarExecutor;
import com.mmt.hotels.clientgateway.restexecutors.SearchRoomsExecutor;
import com.mmt.hotels.clientgateway.restexecutors.StaticDetailExecutor;
import com.mmt.hotels.clientgateway.restexecutors.UpdatedPriceExecutor;
import com.mmt.hotels.clientgateway.thirdparty.request.UgcReviewRequest;
import com.mmt.hotels.clientgateway.transformer.factory.SearchRoomsFactory;
import com.mmt.hotels.clientgateway.transformer.factory.StaticDetailFactory;
import com.mmt.hotels.clientgateway.transformer.factory.UpdatedPriceFactory;
import com.mmt.hotels.clientgateway.transformer.request.OldToNewerRequestTransformer;
import com.mmt.hotels.clientgateway.util.ClientBackendUtility;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.CityGuideRequest;
import com.mmt.hotels.model.request.HotelsImagesSearchRequest;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.response.flyfish.UGCPlatformReviewSummaryDTO;
import com.mmt.hotels.model.response.flyfish.UgcReviewResponseData;
import com.mmt.hotels.model.response.flyfish.UgcSummaryRequest;
import com.mmt.hotels.model.response.flyfish.UserReviewResponseForListing;
import com.mmt.hotels.model.response.pricing.PriceBreakDownResponse;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.staticdata.CityGuideResponse;
import com.mmt.hotels.model.response.staticdata.HotelImage;
import com.mmt.hotels.pojo.CalendarAvailability.CalendarCriteria;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.request.wishlist.FlyfishReviewRequestBody;
import com.mmt.hotels.pojo.request.wishlist.HotStoreHotelsRequestBody;
import com.mmt.hotels.pojo.response.detail.HotelDetailWrapperResponse;
import com.mmt.hotels.pojo.response.wishlist.FlyfishReviewWrapperResponse;
import com.mmt.hotels.pojo.response.wishlist.HotStoreHotelsWrapperResponse;
import com.mmt.model.HotelsRoomInfoResponseEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

import static com.mmt.hotels.clientgateway.constants.ControllerConstants.DETAIL_SEARCH_ROOMS;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.DETAIL_SEARCH_SLOTS;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.WISHLISTED_STATIC_DETAILS;

@Component
public class DetailService {
	
	@Autowired
	private SearchRoomsFactory searchRoomsFactory;
	
	@Autowired
	private SearchRoomsExecutor searchRoomsExecutor;
	
	@Autowired
	private StaticDetailFactory staticDetailFactory;
	
	@Autowired
	private StaticDetailExecutor staticDetailExecutor;

	@Autowired
	private UpdatedPriceFactory updatedPriceFactory;

	@Autowired
	private UpdatedPriceExecutor updatedPriceExecutor;
	
	@Autowired
    private MetricErrorLogger metricErrorLogger;
	
	@Autowired
	private OldToNewerRequestTransformer oldToNewerRequestTransformer;
	
	@Autowired
	private CommonHelper commonHelper;

	@Autowired
	private MetricAspect metricAspect;

	@Autowired
	ObjectMapperUtil objectMapperUtil;

	@Autowired
	CommonConfigConsul commonConfigConsul;

	@Autowired
	private Utility utility;

	private boolean calendarRearch;

	@Autowired
	CalendarExecutor calendarExecutor;

	@Value("${use.new.listing.service:true}")
	private boolean useNewListingService;

	@Autowired
	private OrchDetailService orchDetailService;

	private static final Logger logger = LoggerFactory.getLogger(DetailService.class);

	@PostConstruct
	public void init() {
		calendarRearch = commonConfigConsul.isCalendarRearch();
	}
	
	public SearchRoomsResponse searchRooms(SearchRoomsRequest searchRoomsRequest, Map<String,String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
		try {
			long startTime = new Date().getTime();
			CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchRoomsRequest.getSearchCriteria(), searchRoomsRequest, httpHeaderMap);
			metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_COMMON_REQUEST_PROCESS, DETAIL_SEARCH_ROOMS, System.currentTimeMillis() - startTime);
			PriceByHotelsRequestBody priceByHotelsRequestBody = searchRoomsFactory.
					getRequestService(searchRoomsRequest.getClient()).convertSearchRoomsRequest(searchRoomsRequest, commonModifierResponse);
			HotelsImagesSearchRequest hotelsImagesSearchRequest = buildHotelsImageSearchRequest(priceByHotelsRequestBody, commonModifierResponse);
			boolean rearchFlow = utility.isDetailPageRearchFlow(true, searchRoomsRequest.getRequestDetails().getRequestId(), commonModifierResponse.getExpDataMap());
			startTime = new Date().getTime();
			RoomDetailsResponse roomDetailsResponse = null;
			HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity = null;
			HotelImage hotelImage = null;
			int totalCandidates=0;
			try {
				if (rearchFlow) {
					return orchDetailService.searchRooms(searchRoomsRequest, parameterMap, httpHeaderMap, commonModifierResponse);
				}
				if (searchRoomsRequest.getSearchCriteria() != null) {
					utility.setLoggingParametersToMDC(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates(), searchRoomsRequest.getSearchCriteria().getCheckIn(),
							searchRoomsRequest.getSearchCriteria().getCheckOut());
					totalCandidates=utility.getTotalAdultsFromRequest(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates())+utility.getTotalChildrenFromRequest(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates());
				}
				CountDownLatch countDownLatchAsync = utility.getCountDownLatch();
				Future<RoomDetailsResponse> roomDetailsResponseFuture = null;
				Future<HotelsRoomInfoResponseEntity> hotelsRoomInfoResponseEntityFuture = null;
				Future<HotelImage> hotelImageFuture = null;
				try{
					roomDetailsResponseFuture = searchRoomsExecutor.getRoomPrices(priceByHotelsRequestBody, parameterMap, httpHeaderMap, countDownLatchAsync);
				}catch (Exception e){
					logger.warn("ExecutionException occurred in searchRooms - getRoomPrices: {}", e.getMessage());
					countDownLatchAsync.countDown();
				}
				try{
					hotelsRoomInfoResponseEntityFuture = searchRoomsExecutor.getRoomStaticDetails(searchRoomsRequest.getSearchCriteria().getHotelId(),totalCandidates, searchRoomsRequest.getCorrelationKey(), parameterMap, httpHeaderMap, countDownLatchAsync, Constants.PAGE_CONTEXT_DETAIL, searchRoomsRequest.getExpData());
				}catch (Exception e){
					logger.warn("ExecutionException occurred in searchRooms - getRoomStaticDetails: {}", e.getMessage());
					countDownLatchAsync.countDown();
				}
				try{
					hotelImageFuture = searchRoomsExecutor.getHotelImages(hotelsImagesSearchRequest, searchRoomsRequest, parameterMap, httpHeaderMap, countDownLatchAsync, Constants.PAGE_CONTEXT_DETAIL);
				}catch (Exception e){
					logger.warn("ExecutionException occurred in searchRooms - getHotelImages: {}", e.getMessage());
					countDownLatchAsync.countDown();
				}
				countDownLatchAsync.await();

				if(roomDetailsResponseFuture != null){
					roomDetailsResponse = roomDetailsResponseFuture.get();
				}
				if(hotelsRoomInfoResponseEntityFuture != null){
					hotelsRoomInfoResponseEntity = hotelsRoomInfoResponseEntityFuture.get();
				}
				if(hotelImageFuture != null){
					hotelImage = hotelImageFuture.get();
				}
			}catch (ExecutionException ex) {
				logger.warn("ExecutionException occurred in searchRooms: {}", ex.getMessage());
				throw ex.getCause();
			} catch (Exception ex) {
				logger.warn("Exception occurred in searchRooms:", ex);
				throw ex;
			} finally {
				metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "searchRoomsTotal", new Date().getTime() - startTime);
			}

			/*
			* myPartner change log :
			* 	Overloaded convertSearchRoomsResponse created with commonModifierResponse
			* 	The other method is used to keep the test cases intact. All calls to that from test cases is forwarded to the new overloaded method
			* */
			return searchRoomsFactory.getResponseService(searchRoomsRequest.getClient()).convertSearchRoomsResponse(searchRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage,
							searchRoomsRequest.getExpData(), searchRoomsRequest.getSearchCriteria().getRoomStayCandidates(),
					searchRoomsRequest.getSearchCriteria(),searchRoomsRequest.getFilterCriteria(),searchRoomsRequest.getRequestDetails(),searchRoomsRequest.getExpVariantKeys(), commonModifierResponse);

		} catch (Throwable e) {
			if (e instanceof ErrorResponseFromDownstreamException) {
				logger.error("downstream error occurred in searchRooms: {} ", e.getMessage());
				logger.debug("error occurred in searchRooms: {}", e.getMessage(), e);
			} else
				logger.error("error occurred in searchRooms: {}", e.getMessage(), e);

			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
		}
	}

	/*
		HotelsImagesSearchRequest built to send userContext and pokus information to hes
	 */
	public HotelsImagesSearchRequest buildHotelsImageSearchRequest(PriceByHotelsRequestBody priceByHotelsRequestBody, CommonModifierResponse commonModifierResponse) {
		HotelsImagesSearchRequest hotelsImagesSearchRequest = new HotelsImagesSearchRequest();
		hotelsImagesSearchRequest.setBookingDevice(priceByHotelsRequestBody.getBookingDevice());
		hotelsImagesSearchRequest.setBrand(priceByHotelsRequestBody.getBrand());
		hotelsImagesSearchRequest.setContentExpDataMap(priceByHotelsRequestBody.getContentExpDataMap());
		hotelsImagesSearchRequest.setDeviceId(priceByHotelsRequestBody.getDeviceId());
		hotelsImagesSearchRequest.setProfileType(priceByHotelsRequestBody.getProfileType());
		hotelsImagesSearchRequest.setSubProfileType(priceByHotelsRequestBody.getSubProfileType());
		hotelsImagesSearchRequest.setUUID(priceByHotelsRequestBody.getUuid());
		hotelsImagesSearchRequest.setLocationId(priceByHotelsRequestBody.getLocationId());
		hotelsImagesSearchRequest.setLocationType(priceByHotelsRequestBody.getLocationType());
		hotelsImagesSearchRequest.setCheckIn(priceByHotelsRequestBody.getCheckin());
		hotelsImagesSearchRequest.setCheckOut(priceByHotelsRequestBody.getCheckout());
		hotelsImagesSearchRequest.setRoomStayCandidates(CollectionUtils.isNotEmpty(priceByHotelsRequestBody.getRoomStayCandidates())?priceByHotelsRequestBody.getRoomStayCandidates():null);
		hotelsImagesSearchRequest.setExpData(priceByHotelsRequestBody.getExperimentData());
		if (commonModifierResponse.getHydraResponse() != null && CollectionUtils.isNotEmpty(commonModifierResponse.getHydraResponse().getHydraMatchedSegment())) {
			String[] segmentList = new String[commonModifierResponse.getHydraResponse().getHydraMatchedSegment()
					.size()];
			int i = 0;
			for (String segment : commonModifierResponse.getHydraResponse().getHydraMatchedSegment()) {
				segmentList[i] = segment;
				i++;
			}
			hotelsImagesSearchRequest.setUserSegments(segmentList);
		}

		return hotelsImagesSearchRequest;
	}

    public WishListedHotelsDetailResponse wishListedStaticDetail(WishListedHotelsDetailRequest wishListedHotelsDetailRequest, Map<String, String[]> parameterMap,
																 Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {
            long startTime = new Date().getTime();
			SearchCriteria searchCriteria = buildSearchCriteria(wishListedHotelsDetailRequest);
			CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchCriteria, wishListedHotelsDetailRequest, httpHeaderMap);
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_COMMON_REQUEST_PROCESS, WISHLISTED_STATIC_DETAILS, System.currentTimeMillis() - startTime);

			HotStoreHotelsRequestBody hotStoreHotelRequestBody = staticDetailFactory.getRequestService(wishListedHotelsDetailRequest.getClient())
					.getHotStoreHotelsRequest(wishListedHotelsDetailRequest, commonModifierResponse);

			FlyfishReviewRequestBody flyfishReviewRequestBody = staticDetailFactory.getRequestService(wishListedHotelsDetailRequest.getClient())
					.getFlyfishReviewRequest(wishListedHotelsDetailRequest, commonModifierResponse);

			UgcSummaryRequest ugcSummaryRequest = staticDetailFactory.getRequestService(wishListedHotelsDetailRequest.getClient())
					.getUgcSummaryRequest(wishListedHotelsDetailRequest, commonModifierResponse);

			startTime = new Date().getTime();
			HotStoreHotelsWrapperResponse hotStoreHotelsWrapperResponse = null;
			FlyfishReviewWrapperResponse flyfishReviewWrapperResponse = null;
			UserReviewResponseForListing userReviewResponseForListing = null;
            try {
				 hotStoreHotelsWrapperResponse = staticDetailExecutor.getWishListedHotelsFromHotStore(hotStoreHotelRequestBody, httpHeaderMap);
				userReviewResponseForListing = staticDetailExecutor.getPlatformsUgcSummary(ugcSummaryRequest, httpHeaderMap, wishListedHotelsDetailRequest.getCorrelationKey());
			} catch (Exception ex) {
                throw ex;
            } finally {
                metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "wishListedStaticDetailTotal", new Date().getTime() - startTime);
            }

            return staticDetailFactory.getResponseService(wishListedHotelsDetailRequest.getClient()).
					convertWishListedStaticDetailResponse(hotStoreHotelsWrapperResponse, flyfishReviewWrapperResponse,
							wishListedHotelsDetailRequest.getClient(), wishListedHotelsDetailRequest, commonModifierResponse, userReviewResponseForListing);

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in wishListedStaticDetail: " + e.getMessage());
                logger.debug("error occurred in wishListedStaticDetail: " + e.getMessage(), e);
            } else
                logger.error("error occurred in wishListedStaticDetail: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

	public SearchCriteria buildSearchCriteria(WishListedHotelsDetailRequest wishListedHotelsDetailRequest) {
		SearchCriteria searchCriteria = new SearchCriteria();
		searchCriteria.setCityCode(wishListedHotelsDetailRequest.getSearchCriteria().getCityCode());
		searchCriteria.setLocationId(wishListedHotelsDetailRequest.getSearchCriteria().getLocationId());
		searchCriteria.setLocationType(wishListedHotelsDetailRequest.getSearchCriteria().getLocationType());
		return searchCriteria;
	}

	public StaticDetailResponse staticDetail(StaticDetailRequest staticDetailRequest, Map<String, String[]> parameterMap,
			Map<String, String> httpHeaderMap) throws ClientGatewayException{
		try {
			CommonModifierResponse commonModifierResponse = commonHelper.processRequest(staticDetailRequest.getSearchCriteria(), staticDetailRequest, httpHeaderMap);
			if (utility.shouldRedirectStaticDetailToOrch(staticDetailRequest)) {
				return orchDetailService.staticDetails(staticDetailRequest, parameterMap, httpHeaderMap, commonModifierResponse);
			}
			HotelDetailsMobRequestBody hotelDetailsMobRequestBody = staticDetailFactory.
					getRequestService(staticDetailRequest.getClient()).convertStaticDetailRequest(staticDetailRequest, commonModifierResponse);
			//Adding Log to check LocationType for SWAT-7266394 will revert this after someTime
			if (staticDetailRequest.getSearchCriteria() != null && StringUtils.isNotEmpty(staticDetailRequest.getSearchCriteria().getLocationType()) && !Enums.getIfPresent(LocationType.class, staticDetailRequest.getSearchCriteria().getLocationType()).isPresent()) {
				logger.warn("Getting Strange Location type in Static Detail request from Client {}", objectMapperUtil.getJsonFromObject(staticDetailRequest, DependencyLayer.CLIENTGATEWAY));
			}

			HotelDetailWrapperResponse hotelDetailWrapperResponse = staticDetailExecutor.
					getStaticDetail(hotelDetailsMobRequestBody, parameterMap, httpHeaderMap);
			return staticDetailFactory.getResponseService(staticDetailRequest.getClient()).
					convertStaticDetailResponse(hotelDetailWrapperResponse, staticDetailRequest.getClient(), staticDetailRequest, commonModifierResponse);

		} catch (Throwable e) {
			if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occurred in staticDetail: " + e.getMessage());
        		logger.debug("error occurred in staticDetail: " + e.getMessage(), e);
        	} else
        		logger.error("error occurred in staticDetail: " + e.getMessage(), e);

			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}

	public UGCPlatformReviewSummaryDTO ugcCategoryDetail(PlatformUgcCategoryRequest ugcCategoryDetailRequest, Map<String, String[]> parameterMap,
														 Map<String, String> httpHeaderMap) throws ClientGatewayException{
		try {
			if (commonConfigConsul.isUgcSummaryOrchEnabled()) {
                return orchDetailService.ugcSummary(ugcCategoryDetailRequest, parameterMap, httpHeaderMap);
			}

			UGCPlatformReviewSummaryDTO summary = staticDetailExecutor.getUgcCategoryDetail(ugcCategoryDetailRequest, parameterMap, httpHeaderMap);
			if (MapUtils.isNotEmpty(parameterMap)
					&& parameterMap.containsKey(Constants.DES_CON)
					&& parameterMap.get(Constants.DES_CON)[0] != null
					&& StringUtils.isNotEmpty(parameterMap.get(Constants.DES_CON)[0])
					&& !Constants.DOM_COUNTRY.equalsIgnoreCase(parameterMap.get(Constants.DES_CON)[0])) {
				summary.setRatingBreakup(null);
			}
			return summary;
		} catch (Exception e) {
			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}


	public UpdatePriceResponse updatePrice(UpdatePriceRequest updatePriceRequest, Map<String, String[]> parameterMap,
			Map<String, String> httpHeaderMap) throws ClientGatewayException{
		try {

			CommonModifierResponse commonModifierResponse = commonHelper.processRequest(updatePriceRequest.getSearchCriteria(), updatePriceRequest, httpHeaderMap);
			
			// Check for rearchFlow condition similar to searchRooms
			boolean rearchFlow = utility.isDetailPageRearchFlow(true, updatePriceRequest.getRequestDetails().getRequestId(), commonModifierResponse.getExpDataMap());
			
			if (rearchFlow) {
				return orchDetailService.updatePrice(updatePriceRequest, parameterMap, httpHeaderMap, commonModifierResponse);
			}
			
			// Traditional flow
			PriceByHotelsRequestBody priceByHotelsRequestBody = updatedPriceFactory.
					getRequestService(updatePriceRequest.getClient()).convertUpdatedPriceRequest(updatePriceRequest, commonModifierResponse);
			PriceBreakDownResponse priceBreakDownResponse = updatedPriceExecutor.updatedPrice(priceByHotelsRequestBody, parameterMap, httpHeaderMap);

			return updatedPriceFactory.getResponseService(updatePriceRequest.getClient()).convertUpdatedPriceResponse(priceBreakDownResponse, updatePriceRequest,commonModifierResponse);

		} catch (Throwable e) {
			if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occured in updatePrice: " + e.getMessage());
        		logger.debug("error occured in updatePrice: " + e.getMessage(), e);
        	} else
        		logger.error("error occured in updatePrice: " + e.getMessage(), e);

			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
		}
	}
	
	public String searchPriceOld(PriceByHotelsRequestBody priceByHotelsRequestBody, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
		try {
			SearchRoomsRequest searchRoomsRequest = oldToNewerRequestTransformer.updateSearchRoomsRequest(priceByHotelsRequestBody);
			CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchRoomsRequest.getSearchCriteria(), searchRoomsRequest, httpHeaderMap);
			PriceByHotelsRequestBody priceByHotelsRequestBodyModified = searchRoomsFactory.
					getRequestService(searchRoomsRequest.getClient()).convertSearchRoomsRequest(searchRoomsRequest, commonModifierResponse);

			return searchRoomsExecutor.getRoomPricesOld(priceByHotelsRequestBodyModified, parameterMap, httpHeaderMap);

		} catch (Throwable e) {
			if (e instanceof AuthenticationException){
				return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
			}
			else if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occurred in searchPriceOld: " + e.getMessage());
        		logger.debug("error occurred in searchPriceOld: " + e.getMessage(), e);
        	} else
        		logger.error("error occurred in searchPriceOld: " + e.getMessage(), e);

			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}
	
	public String alternateDatesPriceOld(PriceByHotelsRequestBody priceByHotelsRequestBody, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
		try{
			SearchRoomsRequest searchRoomsRequest = oldToNewerRequestTransformer.updateSearchRoomsRequest(priceByHotelsRequestBody);
			CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchRoomsRequest.getSearchCriteria(), searchRoomsRequest, httpHeaderMap);
			PriceByHotelsRequestBody priceByHotelsRequestBodyModified = searchRoomsFactory.
					getRequestService(searchRoomsRequest.getClient()).convertSearchRoomsRequest(searchRoomsRequest, commonModifierResponse);
			if(calendarRearch) {
				return orchDetailService.alternateDatesPrice(priceByHotelsRequestBody, parameterMap, httpHeaderMap, commonModifierResponse);
			}

			return searchRoomsExecutor.alternateDatesPriceOld(priceByHotelsRequestBodyModified, parameterMap, httpHeaderMap);

		} catch (Throwable e) {
			if (e instanceof AuthenticationException){
				return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
			}
			else if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occurred in alternateDatesPriceOld: " + e.getMessage());
        		logger.debug("error occurred in alternateDatesPriceOld: " + e.getMessage(), e);
        	} else
        		logger.error("error occurred in alternateDatesPriceOld: " + e.getMessage(), e);

			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}


	public String getStaticDetailsResponse(HotelDetailsMobRequestBody request, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException{
        try {
			SearchRoomsRequest searchRequest = oldToNewerRequestTransformer.updateSearchRoomRequest(request);
			CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchRequest.getSearchCriteria(), searchRequest, httpHeaderMap);
			HotelDetailsMobRequestBody hotelDetailsMobRequestBody = staticDetailFactory.
					getRequestService(searchRequest.getClient()).convertStaticDetailRequest(searchRequest, commonModifierResponse, request);

			return staticDetailExecutor.getStaticDetailsResponse(hotelDetailsMobRequestBody, parameterMap, httpHeaderMap);

        } catch (Throwable e) {
			if (e instanceof AuthenticationException){
				return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
			} else if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occurred in getStaticDetailsResponse: " + e.getMessage());
        		logger.debug("error occurred in getStaticDetailsResponse: " + e.getMessage(), e);
        	} else
        		logger.error("error occurred in getStaticDetailsResponse: " + e.getMessage(), e);

			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}


	public CityGuideResponse cityGuideData(com.mmt.hotels.clientgateway.request.CityGuideRequest cityGuideRequest, Map<String, String[]> parameterMap,
										   Map<String, String> httpHeaderMap, String correlationKey) throws ClientGatewayException{
		try {
			CityGuideRequest request = staticDetailFactory.
					getRequestService(cityGuideRequest.getClient()).convertCityGuideRequest(cityGuideRequest, correlationKey);
			return staticDetailExecutor.
					getCityGuildeResponse(request, parameterMap, httpHeaderMap);
		} catch (Throwable e) {
			if (e instanceof ErrorResponseFromDownstreamException) {
				logger.error("error occurred in cityGuide: " + e.getMessage());
				logger.debug("error occurred in cityGuide: " + e.getMessage(), e);
			} else
				logger.error("error occurred in cityGuide: " + e.getMessage(), e);

			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}

	public CalendarAvailabilityResponse fetchCalendarAvailability(CalendarAvailabilityRequest calendarAvailabilityRequest, String correlationKey,
																  Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
		try {
			CommonModifierResponse commonModifierResponse = commonHelper
					.processRequest(calendarAvailabilityRequest.getSearchCriteria(), calendarAvailabilityRequest, httpHeaderMap);
			if(calendarRearch) {
				return orchDetailService.calendarAvailability(calendarAvailabilityRequest, correlationKey , parameterMap, httpHeaderMap, commonModifierResponse);
			}
			PriceByHotelsRequestBody calendarAvailabilityRequestModified = searchRoomsFactory.getRequestService(calendarAvailabilityRequest.getClient())
					.convertSearchRoomsRequest(calendarAvailabilityRequest, commonModifierResponse);
			calendarAvailabilityRequestModified.setCalendarCriteria(buildCalendarCriteria(calendarAvailabilityRequest));
			com.mmt.hotels.model.response.CalendarAvailabilityResponse calendarAvailabilityResponse = calendarExecutor
					.getCalendarAvailability(calendarAvailabilityRequestModified, correlationKey, parameterMap, httpHeaderMap);
			String currency = calendarAvailabilityRequest.getSearchCriteria() != null ? calendarAvailabilityRequest.getSearchCriteria().getCurrency() : "INR";
			return searchRoomsFactory.getResponseService(calendarAvailabilityRequest.getClient()).convertCalendarAvailabilityResponse(calendarAvailabilityResponse, currency);

		} catch(Throwable e){
			if (e instanceof ErrorResponseFromDownstreamException) {
				logger.error("error occurred in fetchCalendarAvailability : " + e.getMessage());
				logger.debug("error occurred in fetchCalendarAvailability : " + e.getMessage(), e);
			} else {
				logger.error("error occurred in fetchCalendarAvailability : " + e.getMessage(), e);
			}
			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}

	public CalendarCriteria buildCalendarCriteria(CalendarAvailabilityRequest calendarAvailabilityRequest) {
		if(null == calendarAvailabilityRequest.getCalendarCriteria())
			return null;
		com.mmt.hotels.pojo.CalendarAvailability.CalendarCriteria calendarCriteria = new com.mmt.hotels.pojo.CalendarAvailability.CalendarCriteria();
		calendarCriteria.setAdvanceDays(calendarAvailabilityRequest.getCalendarCriteria().getAdvanceDays());
		calendarCriteria.setAvailable(calendarAvailabilityRequest.getCalendarCriteria().isAvailable());
		calendarCriteria.setMaxDate(calendarAvailabilityRequest.getCalendarCriteria().getMaxDate());
		calendarCriteria.setMlos(calendarAvailabilityRequest.getCalendarCriteria().getMlos());
		return calendarCriteria;
	}

	public DayUseRoomsResponse searchSlots(DayUseRoomsRequest dayUseRoomsRequest, Map<String,String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
		try {
			long startTime = new Date().getTime();
			CommonModifierResponse commonModifierResponse = commonHelper.processRequest(dayUseRoomsRequest.getSearchCriteria(), dayUseRoomsRequest, httpHeaderMap);
			metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_COMMON_REQUEST_PROCESS, DETAIL_SEARCH_SLOTS, System.currentTimeMillis() - startTime);
			PriceByHotelsRequestBody priceByHotelsRequestBody = searchRoomsFactory.
					getRequestService(dayUseRoomsRequest.getClient()).convertSearchRoomsRequest(dayUseRoomsRequest, commonModifierResponse);
			boolean rearchFlow = utility.isDetailPageRearchFlow(true, dayUseRoomsRequest.getRequestDetails() != null ? dayUseRoomsRequest.getRequestDetails().getRequestId() : "", commonModifierResponse.getExpDataMap());
			startTime = new Date().getTime();
			RoomDetailsResponse roomDetailsResponse = null;
			HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity = null;
			HotelImage hotelImage = null;
			int totalCandidates=0;
			try {
				if (rearchFlow) {
					return orchDetailService.searchSlots(dayUseRoomsRequest, parameterMap, httpHeaderMap, commonModifierResponse);
				}
				parameterMap = utility.addFunnelSourceToParameterMap(priceByHotelsRequestBody, parameterMap);
				CountDownLatch countDownLatchAsync = utility.getCountDownLatch();
				Future<RoomDetailsResponse> roomDetailsResponseFuture = null;
				Future<HotelsRoomInfoResponseEntity> hotelsRoomInfoResponseEntityFuture = null;
				Future<HotelImage> hotelImageFuture = null;

				try{
					roomDetailsResponseFuture = searchRoomsExecutor.getRoomPrices(priceByHotelsRequestBody, parameterMap, httpHeaderMap, countDownLatchAsync);
				}catch (Exception e){
					logger.warn("ExecutionException occurred in searchSlots - getRoomPrices: {}", e.getMessage());
					countDownLatchAsync.countDown();
				}
				try{
					hotelsRoomInfoResponseEntityFuture = searchRoomsExecutor.getRoomStaticDetails(dayUseRoomsRequest.getSearchCriteria().getHotelId(),totalCandidates, dayUseRoomsRequest.getCorrelationKey(), parameterMap, httpHeaderMap, countDownLatchAsync, Constants.PAGE_CONTEXT_DETAIL, dayUseRoomsRequest.getExpData());
				}catch (Exception e){
					logger.warn("ExecutionException occurred in searchSlots - getRoomStaticDetails: {}", e.getMessage());
					countDownLatchAsync.countDown();
				}
				try{
					hotelImageFuture = searchRoomsExecutor.getHotelImages(dayUseRoomsRequest, parameterMap, httpHeaderMap, countDownLatchAsync, Constants.PAGE_CONTEXT_DETAIL);
				}catch (Exception e){
					logger.warn("ExecutionException occurred in searchSlots - getHotelImages: {}", e.getMessage());
					countDownLatchAsync.countDown();
				}
				countDownLatchAsync.await();

				if(roomDetailsResponseFuture != null){
					roomDetailsResponse = roomDetailsResponseFuture.get();
				}
				if(hotelsRoomInfoResponseEntityFuture != null){
					hotelsRoomInfoResponseEntity = hotelsRoomInfoResponseEntityFuture.get();
				}
				if(hotelImageFuture != null){
					hotelImage = hotelImageFuture.get();
				}
			} catch (ExecutionException ex) {
				logger.warn("ExecutionException occurred in searchSlots:", ex);
				throw ex.getCause();
			} catch (Exception ex) {
				logger.warn("Exception occurred in searchSlots:", ex);
				throw ex;
			} finally {
				metricAspect.addToTime(DependencyLayer.ORCHESTRATOR.name(), "searchSlotsTotal", new Date().getTime() - startTime);
			}

			return searchRoomsFactory.getResponseService(dayUseRoomsRequest.getClient())
					.convertSearchSlotsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity, hotelImage, dayUseRoomsRequest, commonModifierResponse);

		} catch (Throwable e) {
			if (e instanceof ErrorResponseFromDownstreamException) {
				logger.error("downstream error occurred in searchSlots: {} ", e.getMessage());
				logger.debug("error occurred in searchSlots: {}", e.getMessage(), e);
			} else
				logger.error("error occurred in searchSlots: {}", e.getMessage(), e);

			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}

	}

	public UgcReviewResponseData getUgcReviewsFromHes(UgcReviewRequest ugcReviewRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
		try {
			if (commonConfigConsul.isUgcReviewsOrchEnabled()) {
				return orchDetailService.ugcReviews(ugcReviewRequest, parameterMap, httpHeaderMap);
			}

            long startTime = System.currentTimeMillis();
			UgcReviewResponseData ugcReviewResponseData = staticDetailExecutor.getReviewData(ugcReviewRequest, parameterMap);
            metricAspect.addToTime(DependencyLayer.CLIENTGATEWAY.name(), "getUgcReviewsFromHes", System.currentTimeMillis() - startTime);
			return ugcReviewResponseData;
		} catch (Exception e) {
            logger.error("Exception occured in getUgcReviewsFromHes : {}", e.getMessage(), e);
			return null;
		}
	}

}
