package com.mmt.hotels.clientgateway.service;

import com.gommt.hotels.orchestrator.detail.enums.Brand;
import com.gommt.hotels.orchestrator.detail.enums.Country;
import com.gommt.hotels.orchestrator.detail.enums.DeviceType;
import com.gommt.hotels.orchestrator.detail.enums.Funnel;
import com.gommt.hotels.orchestrator.detail.enums.IdContext;
import com.gommt.hotels.orchestrator.detail.enums.Language;
import com.gommt.hotels.orchestrator.detail.enums.PageContext;
import com.gommt.hotels.orchestrator.detail.enums.ProfileType;
import com.gommt.hotels.orchestrator.detail.enums.Region;
import com.gommt.hotels.orchestrator.detail.enums.SiteDomain;
import com.gommt.hotels.orchestrator.detail.enums.SubProfileType;
import com.gommt.hotels.orchestrator.detail.enums.TrafficSource;
import com.gommt.hotels.orchestrator.detail.enums.TrafficType;
import com.gommt.hotels.orchestrator.detail.model.objects.BookingDevice;
import com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails;
import com.gommt.hotels.orchestrator.detail.model.request.DetailRequest;
import com.gommt.hotels.orchestrator.detail.model.request.common.ClientDetails;
import com.gommt.hotels.orchestrator.detail.model.request.common.GeoLocationDetails;
import com.gommt.hotels.orchestrator.detail.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.detail.model.response.HostCallingResponse;
import com.gommt.hotels.orchestrator.detail.model.state.ChatbotDetails;
import com.gommt.hotels.orchestrator.detail.model.state.UserDetails;
import com.gommt.hotels.orchestrator.detail.model.state.UserSessionData;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.ImageCategory;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.request.StaticDetailCriteria;
import com.mmt.hotels.clientgateway.request.UserGlobalInfo;
import com.mmt.hotels.clientgateway.request.hostcalling.HostCallingInitiateRequestBody;
import com.mmt.hotels.clientgateway.restexecutors.OrchDetailExecutor;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.transformer.factory.HostCallingFactory;
import com.mmt.hotels.clientgateway.transformer.response.OrchHostCallingResponseTransformer;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.UserLocation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.EXACT_ROOM_VALUE;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.DETAIL_SEARCH_ROOMS;

@Component
public class OrchHostCallingService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrchHostCallingService.class);

    @Autowired
    private Utility utility;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    private OrchDetailExecutor orchDetailExecutor;

    @Autowired
    private HostCallingFactory hostCallingFactory;

    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
    private OrchHostCallingResponseTransformer orchHostCallingResponseTransformer;

    public com.mmt.hotels.clientgateway.response.hostcalling.HostCallingInitiateResponse hostCalling(HostCallingInitiateRequestBody hostCallingRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, CommonModifierResponse commonModifierResponse) throws ClientGatewayException {
        try {
            LOGGER.warn("Orchestrator search rooms v2 flow");
            long startTime = System.currentTimeMillis();

            // FIX: Add null safety for hostCallingRequest
            if (hostCallingRequest != null && hostCallingRequest.getSearchCriteria() != null) {
                utility.setLoggingParametersToMDC(hostCallingRequest.getSearchCriteria().getRoomStayCandidates(), hostCallingRequest.getSearchCriteria().getCheckIn(),
                        hostCallingRequest.getSearchCriteria().getCheckOut());
            }
//            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_COMMON_REQUEST_PROCESS, DETAIL_SEARCH_ROOMS, System.currentTimeMillis() - startTime);


            DetailRequest detailRequest = buildSearchRoomsRequest(hostCallingRequest, commonModifierResponse);


            HostCallingResponse response = orchDetailExecutor.hostCalling(detailRequest, parameterMap, httpHeaderMap);
//            hostCallingRequest.getDeviceDetails().getBookingDevice();
            //RESPONSE TIME
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_RESPONSE_PROCESS, DETAIL_SEARCH_ROOMS, System.currentTimeMillis() - startTime);
            
            // FIX: Add null safety for response and client/visitorId fields
            String client = Optional.ofNullable(hostCallingRequest)
                    .map(HostCallingInitiateRequestBody::getClient)
                    .orElse("");
            String visitorId = Optional.ofNullable(hostCallingRequest)
                    .map(HostCallingInitiateRequestBody::getRequestDetails)
                    .map(com.mmt.hotels.clientgateway.request.RequestDetails::getVisitorId)
                    .orElse("");
            
            return orchHostCallingResponseTransformer.convertHostCallingResponse(response, client, visitorId);

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                LOGGER.error("error occurred in searchRooms: {}", e.getMessage());
                LOGGER.debug("error occurred in searchRooms: {}", e.getMessage(), e);
            } else
                LOGGER.error("error occurred in searchRooms: {}", e.getMessage(), e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }

    }

    public DetailRequest buildSearchRoomsRequest(HostCallingInitiateRequestBody cgRequest, CommonModifierResponse commonModifierResponse) {
        if (cgRequest == null) {
            LOGGER.error("ListingSearchRequest is null");
            throw new IllegalArgumentException("ListingSearchRequest cannot be null");
        }

        if (commonModifierResponse == null) {
            LOGGER.error("CommonModifierResponse is null");
            throw new IllegalArgumentException("CommonModifierResponse cannot be null");
        }

        DetailRequest orchRequest = new DetailRequest();

        // Extract search criteria safely
        StaticDetailCriteria searchCriteria = Optional.ofNullable(cgRequest.getSearchCriteria())
                .orElseThrow(() -> new IllegalArgumentException("SearchRoomsCriteria cannot be null"));

        // Build location details using specific criteria
        orchRequest.setHotelId(searchCriteria.getHotelId());
        orchRequest.setLocation(buildLocationDetails(searchCriteria));

        // Set check-in, check-out, and other parameters
        orchRequest.setCheckIn(searchCriteria.getCheckIn());
        orchRequest.setCheckOut(searchCriteria.getCheckOut());

        orchRequest.setSlotDetails(buildSlotDetails(searchCriteria.getSlot()));

        // Build room criteria (Room Stay Candidates) using specific data
        orchRequest.setRooms(buildRoomDetails(searchCriteria.getRoomStayCandidates(), cgRequest.getExpDataMap()));

        // Build client details using specific request data
        orchRequest.setClientDetails(buildClientDetails(cgRequest.getRequestDetails(), cgRequest.getDeviceDetails(),
                cgRequest.getFeatureFlags(), cgRequest.getFilterCriteria(), searchCriteria, cgRequest.getExpDataMap(), commonModifierResponse));

        // Build image details using specific details
        orchRequest.setImageDetails(buildImageDetails(cgRequest.getImageDetails()));

        // Set experiment data and additional fields
        orchRequest.setExperimentData(cgRequest.getExpData());
        orchRequest.setExpVariantKeys(cgRequest.getExpVariantKeys());
        orchRequest.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
        orchRequest.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());
        orchRequest.setUserSearchType(searchCriteria.getUserSearchType());

        //orchRequest.setValidExpList(cgRequest.getValidExpList());
        orchRequest.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
        orchRequest.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());
        LOGGER.info("Successfully built ListingRequest");
        return orchRequest;
    }

    private LocationDetails buildLocationDetails(SearchCriteria searchCriteria) {
        if (searchCriteria == null) {
            LOGGER.warn("SearchCriteria is null while building LocationDetails, returning empty LocationDetails.");
            return new LocationDetails();
        }

        LocationDetails locationDetails = new LocationDetails();
        GeoLocationDetails geoLocationDetails = new GeoLocationDetails();

        // Use the base SearchCriteria fields directly since both SearchRoomsCriteria and StaticDetailCriteria extend it
        locationDetails.setId(Optional.ofNullable(searchCriteria.getLocationId()).orElse(""));
        locationDetails.setType(Optional.ofNullable(searchCriteria.getLocationType()).orElse(""));
        locationDetails.setCityId(Optional.ofNullable(searchCriteria.getCityCode()).orElse(""));
        locationDetails.setCityName(Optional.ofNullable(searchCriteria.getCityName()).orElse(""));
        locationDetails.setCountryId(Optional.ofNullable(searchCriteria.getCountryCode()).orElse(""));

        geoLocationDetails.setLatitude(searchCriteria.getLat() != null ? String.valueOf(searchCriteria.getLat()) : "");
        geoLocationDetails.setLongitude(searchCriteria.getLng() != null ? String.valueOf(searchCriteria.getLng()) : "");

        locationDetails.setGeo(geoLocationDetails);
        return locationDetails;
    }

    private com.gommt.hotels.orchestrator.detail.model.request.common.SlotDetails buildSlotDetails(com.mmt.hotels.clientgateway.request.dayuse.Slot slot) {
        com.gommt.hotels.orchestrator.detail.model.request.common.SlotDetails slotDetails = new
                com.gommt.hotels.orchestrator.detail.model.request.common.SlotDetails();
        if (slot == null) {
            return slotDetails;
        }
        slotDetails.setTimeSlot(slot.getTimeSlot() != null ? slot.getTimeSlot() : 0);
        slotDetails.setDuration(slot.getDuration() != null ? slot.getDuration() : 0);
        return slotDetails;
    }

    private List<RoomDetails> buildRoomDetails(List<RoomStayCandidate> roomStayCandidates, Map<String, String> expDataMap) {
        if (roomStayCandidates == null || roomStayCandidates.isEmpty()) {
            LOGGER.warn("RoomStayCandidates is null or empty, returning an empty RoomDetails list.");
            return Collections.emptyList();
        }

        // Handle distribution logic for SearchRoomsRequest (when expDataMap is provided)
        if (expDataMap != null && utility.isDistributeRoomStayCandidates(roomStayCandidates, expDataMap)) {
            List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidatesHES = utility.buildRoomStayDistribution(roomStayCandidates, expDataMap);
            return roomStayCandidatesHES.stream().map(roomStayCandidate -> {
                com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails roomDetails = new com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails();
                int adultCount = 0;
                List<Integer> childrenAges = new ArrayList<>();
                
                // FIX: Add null safety for guestCounts
                List<com.mmt.hotels.model.request.GuestCount> guestCounts = Optional.ofNullable(roomStayCandidate.getGuestCounts())
                        .orElse(Collections.emptyList());
                
                for (com.mmt.hotels.model.request.GuestCount guestCount : guestCounts) {
                    if (guestCount != null) {
                        // FIX: Add null safety for count
                        String countStr = guestCount.getCount();
                        if (StringUtils.isNotEmpty(countStr) && StringUtils.isNumeric(countStr)) {
                            adultCount = adultCount + Integer.parseInt(countStr);
                        }
                        if (CollectionUtils.isNotEmpty(guestCount.getAges())) {
                            childrenAges.addAll(guestCount.getAges());
                        }
                    }
                }
                roomDetails.setAdults(adultCount);
                roomDetails.setChildrenAges(childrenAges);
                return roomDetails;
            }).collect(Collectors.toList());
        }

        List<com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails> roomDetailsList = new ArrayList<>();

        // Iterate over room stay candidates and build room details
        for (RoomStayCandidate roomStayCandidate : roomStayCandidates) {
            if (roomStayCandidate != null) {
                com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails roomDetails = new com.gommt.hotels.orchestrator.detail.model.objects.RoomDetails();
                roomDetails.setAdults(roomStayCandidate.getAdultCount());
                roomDetails.setChildrenAges(roomStayCandidate.getChildAges());
                roomDetailsList.add(roomDetails);
            } else {
                LOGGER.warn("Encountered a null RoomStayCandidate, skipping.");
            }
        }

        LOGGER.info("Successfully built RoomDetails for {} rooms.", roomDetailsList.size());
        return roomDetailsList;
    }

    private ClientDetails buildClientDetails(com.mmt.hotels.clientgateway.request.RequestDetails requestDetails,
                                             DeviceDetails deviceDetails, FeatureFlags featureFlags, List<Filter> filterCriteria, SearchCriteria searchCriteria,
                                             Map<String, String> expDataMap, CommonModifierResponse commonModifierResponse) {
        ClientDetails clientDetails = new ClientDetails();
        clientDetails.setFeatureFlags(buildFeatureFlags(requestDetails, deviceDetails, featureFlags, filterCriteria, expDataMap, commonModifierResponse));
        clientDetails.setRequestDetails(buildRequestDetails(requestDetails, deviceDetails, searchCriteria, featureFlags, commonModifierResponse));
        clientDetails.setUserDetails(buildUserDetails(requestDetails, commonModifierResponse));

        String visitorId = Optional.ofNullable(requestDetails)
                .map(com.mmt.hotels.clientgateway.request.RequestDetails::getVisitorId)
                .orElse("");

        clientDetails.setVisitorId(visitorId);
        clientDetails.setMcId(commonModifierResponse.getMcId());
        clientDetails.setChatbotDetails(buildChatbotDetails(requestDetails));
        return clientDetails;
    }

    private com.gommt.hotels.orchestrator.detail.model.state.ImageDetails buildImageDetails(com.mmt.hotels.clientgateway.request.ImageDetails imageDetailsRequest) {
        com.gommt.hotels.orchestrator.detail.model.state.ImageDetails imageDetails = new com.gommt.hotels.orchestrator.detail.model.state.ImageDetails();

        if (imageDetailsRequest == null) {
            return imageDetails;
        }

        // Safely retrieve and set categories, defaulting to an empty list if null
        List<ImageCategory> categories = Optional.ofNullable(imageDetailsRequest.getCategories())
                .orElse(Collections.emptyList());
        imageDetails.setCategories(buildImageCategory(categories));

        // Safely set types, defaulting to null if null (matching OrchListingService line 773)
        List<String> types = Optional.ofNullable(imageDetailsRequest.getTypes()).orElse(null);
        imageDetails.setTypes(types);

        return imageDetails;
    }

    private List<com.gommt.hotels.orchestrator.detail.model.objects.ImageCategory> buildImageCategory(List<ImageCategory> inputCategories) {
        // CRITICAL BUG FIX: Initialize list outside the loop (matching OrchListingService lines 776-787)
        List<com.gommt.hotels.orchestrator.detail.model.objects.ImageCategory> imageCategoryList = new ArrayList<>();
        
        // FIX: Add null safety for inputCategories list
        if (inputCategories == null) {
            return imageCategoryList;
        }
        
        for (ImageCategory imageCategory : inputCategories) {
            if (imageCategory != null) {
                com.gommt.hotels.orchestrator.detail.model.objects.ImageCategory result = new com.gommt.hotels.orchestrator.detail.model.objects.ImageCategory();
                // FIX: Add null safety for all imageCategory fields
                result.setType(Optional.ofNullable(imageCategory.getType()).orElse(""));
                result.setCount(Optional.ofNullable(imageCategory.getCount()).orElse(0));
                result.setHeight(Optional.ofNullable(imageCategory.getHeight()).orElse(0));
                result.setImageFormat(Optional.ofNullable(imageCategory.getImageFormat()).orElse(""));
                result.setWidth(Optional.ofNullable(imageCategory.getWidth()).orElse(0));
                imageCategoryList.add(result);
            }
        }
        return imageCategoryList;
    }

    private com.gommt.hotels.orchestrator.detail.model.state.FeatureFlags buildFeatureFlags(com.mmt.hotels.clientgateway.request.RequestDetails requestDetails, DeviceDetails deviceDetails, FeatureFlags featureFlags, List<Filter> filterCriteria, Map<String, String> expDataMap, CommonModifierResponse commonModifierResponse) {
        final FeatureFlags inputFeatureFlags = Optional.ofNullable(featureFlags).orElse(new FeatureFlags());
        com.gommt.hotels.orchestrator.detail.model.state.FeatureFlags result = new com.gommt.hotels.orchestrator.detail.model.state.FeatureFlags();

        // Safely set FlightBooker, defaulting to false
        result.setFlightBooker(Optional.ofNullable(commonModifierResponse.getHydraResponse())
                .map(HydraResponse::isFlightBooker)
                .orElse(false));
        result.setRoomLevelDetails(true);

        // Safely set WalletRequired, defaulting to false
        result.setWalletRequired(Optional.of(inputFeatureFlags).map(FeatureFlags::isWalletRequired).orElse(false));

        // Safely set CityTaxExclusive based on condition
        result.setCityTaxExclusive(Optional.of(commonModifierResponse).map(CommonModifierResponse::isCityTaxExclusive).orElse(false));

        // Override CityTaxExclusive if CORP_ID_CONTEXT matches
        result.setCityTaxExclusive(!Constants.CORP_ID_CONTEXT.equalsIgnoreCase(
                Optional.ofNullable(requestDetails)
                        .map(com.mmt.hotels.clientgateway.request.RequestDetails::getIdContext)
                        .orElse("")) && result.isCityTaxExclusive());

        // Safely set BestCoupon, defaulting to false
        result.setBestCoupon(Optional.of(inputFeatureFlags).map(FeatureFlags::isCoupon).orElse(false));

        // Safely set ComparatorHotelRequest, defaulting to false
        result.setComparatorHotelRequest(Optional.of(inputFeatureFlags).map(FeatureFlags::isComparator).orElse(false));

        // Check RoomPreferenceEnabled from applied filters, defaulting to false - using original business logic
        result.setRoomPreferenceEnabled(utility.checkIfFilterValueExistsInAppliedFilterMap(filterCriteria));

        // IMPLEMENTED BUSINESS LOGIC: EXACT_ROOM_VALUE logic (from OrchListingService lines 849-851)
        if (expDataMap != null && EXACT_ROOM_VALUE.equalsIgnoreCase(expDataMap.get("roomCountDefault"))) {
            result.setRoomPreferenceEnabled(!result.isRoomPreferenceEnabled());
        }

        result.setCollectionRequest(false);

        // Hardcoded value for AdvancedFiltering
        result.setAdvancedFiltering(Boolean.TRUE);

        result.setSeoDS(inputFeatureFlags.isSeoDS());

        // Safely set CheckAvailability, defaulting to false
        result.setCheckAvailability(Optional.of(inputFeatureFlags).map(FeatureFlags::isCheckAvailability).orElse(false));

        // Safely set HomeStayV2Flow, defaulting to false
        result.setHomeStayV2Flow(Optional.of(commonModifierResponse).map(CommonModifierResponse::isHomestayV2Flow).orElse(false));

        // Safely set Orientation, defaulting to null if not present
        result.setOrientation(Optional.of(inputFeatureFlags).map(FeatureFlags::getOrientation).orElse(null));

        // Safely set SimilarHotels, defaulting to false
        result.setSimilarHotels(Optional.of(inputFeatureFlags).map(FeatureFlags::isSimilarHotel).orElse(false));
        result.setMaskedPropertyName(Optional.of(inputFeatureFlags).map(FeatureFlags::getMaskedPropertyName).orElse(false));
        result.setPropSearch(Optional.of(inputFeatureFlags).map(FeatureFlags::isPropSearch).orElse(false));

        // TODO: These feature flags were not found on CG, defaulting to false
        result.setAllSoldOutRequired(false);
        result.setSoldOutInfoRequired(false);
        result.setListingMapShortStays(false);
        result.setBookingModification(false);
        result.setAddHCPToHotelDiscount(false);
        result.setEnableSabre(false);
        result.setBnplExtended(false);
        result.setBlockEmi(false);
        result.setSoldOut(false);
        result.setScarcityFlow(false);
        result.setUnmodifiedAmenities(false);
        result.setSameCityFlightItineraryAvailable(false);
        result.setLocus(false);
        result.setBlackUser(false);
        result.setMmtPrime(false);
        result.setDoubleBlackUser(false);
        result.setLimitedFilterCall(false);
        result.setPremiumThemesCardRequired(inputFeatureFlags.isPremiumThemesCardRequired());
        result.setUpsellRateplanRequired(Optional.of(inputFeatureFlags).map(FeatureFlags::isUpsellRateplanRequired).orElse(false));
        LOGGER.info("Successfully built FeatureFlags.");
        return result;
    }

    private ChatbotDetails buildChatbotDetails(com.mmt.hotels.clientgateway.request.RequestDetails requestDetails) {
        if (requestDetails == null || StringUtils.isEmpty(requestDetails.getMyraMsgId())) {
            return null;
        }
        ChatbotDetails chatbotDetails = new ChatbotDetails();
        chatbotDetails.setMyraMsgId(requestDetails.getMyraMsgId());
        return chatbotDetails;
    }

    private com.gommt.hotels.orchestrator.detail.model.state.RequestDetails buildRequestDetails(com.mmt.hotels.clientgateway.request.RequestDetails requestDetails, DeviceDetails deviceDetails, SearchCriteria searchCriteria, FeatureFlags featureFlags, CommonModifierResponse commonModifierResponse) {
        com.gommt.hotels.orchestrator.detail.model.state.RequestDetails orchRequestDetails = new com.gommt.hotels.orchestrator.detail.model.state.RequestDetails();

        // Safely retrieve BookingDevice from deviceDetails, defaulting to an empty device
        orchRequestDetails.setBookingDevice(buildBookingDevice(Optional.ofNullable(deviceDetails).orElse(new DeviceDetails())));

        // Set currency using original business logic from OrchListingService
        String currency = searchCriteria != null && StringUtils.isNotEmpty(searchCriteria.getCurrency()) ?
                searchCriteria.getCurrency().toUpperCase() : "INR";
        orchRequestDetails.setCurrency(com.gommt.hotels.orchestrator.detail.enums.Currency.valueOf(currency));

        // Set MultiCurrency info using original business logic
        orchRequestDetails.setMultiCurrencyInfo(buildMultiCurrencyInfo(searchCriteria != null ? searchCriteria.getMultiCurrencyInfo() : null));

        // Set UserGlobalInfo if available
        if (searchCriteria != null && searchCriteria.getUserGlobalInfo() != null) {
            orchRequestDetails.setUserGlobalInfo(buildUserGlobalInfo(searchCriteria.getUserGlobalInfo()));
        }

        // Set additional fields from the original business logic
        orchRequestDetails.setAffiliateId(Optional.ofNullable(commonModifierResponse.getAffiliateId()).orElse(""));
        orchRequestDetails.setApplicationId(String.valueOf(Optional.of(commonModifierResponse.getApplicationId()).orElse(0)));
        orchRequestDetails.setRequestType("B2CAgent"); // Hardcoded as per current implementation
        orchRequestDetails.setCdfContextId(Optional.ofNullable(commonModifierResponse.getCdfContextId()).orElse(""));
        
        // FIX: Add null safety for requestDetails.isPremium()
        String reqContext = Optional.ofNullable(requestDetails)
                .map(com.mmt.hotels.clientgateway.request.RequestDetails::isPremium)
                .map(isPremium -> isPremium ? Constants.PREMIUM : Constants.DEFAULT)
                .orElse(Constants.DEFAULT);
        orchRequestDetails.setReqContext(reqContext);

        // TODO: Set semantic search details if available - needs correct import resolution

        // Set B2B attributes and SEO fields using original business logic from OrchListingService
        orchRequestDetails.setB2bAttributes(Collections.emptyMap());

        // Set SEO cohort and template using original business logic from OrchListingService (lines 1018-1019)
        orchRequestDetails.setSeoCohort(Optional.ofNullable(featureFlags).map(FeatureFlags::getSeoCohort).orElse(""));
        orchRequestDetails.setSeoDescTemplate("defaultSeoDescTemplate");  // Hardcoded value as per original

        // MISSING BUSINESS LOGIC: Set ContentExpDataMap and ManthanExpDataMap (from OrchListingService lines 1012-1013)
        // Note: These were commented out in OrchListingService but are set in the method signature
        // They should be set here to match the pattern used elsewhere in the method
        orchRequestDetails.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());
        orchRequestDetails.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
        
        // FIX: Don't call methods on potentially null requestDetails - use safely created orchRequestDetails
        String requestId = Optional.ofNullable(requestDetails)
                .map(com.mmt.hotels.clientgateway.request.RequestDetails::getRequestId)
                .filter(StringUtils::isNotEmpty)
                .orElse(UUID.randomUUID().toString());
        orchRequestDetails.setRequestId(requestId);
        
        String journeyId = Optional.ofNullable(requestDetails)
                .map(com.mmt.hotels.clientgateway.request.RequestDetails::getJourneyId)
                .orElse("");
        orchRequestDetails.setJourneyId(journeyId);
        
        Funnel funnel = Optional.ofNullable(requestDetails)
                .map(com.mmt.hotels.clientgateway.request.RequestDetails::getFunnelSource)
                .map(Funnel::fromValue)
                .orElse(Funnel.HOTELS);
        orchRequestDetails.setFunnelSource(funnel);  // Set the enum, not the name

        // Set language from MDC
        Language language = Optional.ofNullable(MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue()))
                .map(String::toLowerCase)
                .map(Language::fromValue)
                .orElse(Language.ENGLISH);
        orchRequestDetails.setLanguage(language);

        // Set region from MDC
        Region region = Optional.ofNullable(MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()))
                .map(String::toLowerCase)
                .map(Region::fromValue)
                .orElse(Region.IN);
        orchRequestDetails.setRegion(region);

        // Set country using original business logic from OrchListingService (line 991)
        boolean dhCall = Constants.DOM_COUNTRY.equalsIgnoreCase(searchCriteria != null ? searchCriteria.getCountryCode() : "");
        orchRequestDetails.setCountry(!dhCall ? Country.IH : Country.DH);

        // Set the original traffic source
        orchRequestDetails.setOriginalTrafficSource(Optional.ofNullable(commonModifierResponse.getOriginalTrafficSource())
                .orElse(TrafficSource.DEFAULT.getName()));

        // Set application-specific fields
        orchRequestDetails.setRequestType("B2CAgent");
        orchRequestDetails.setApplicationId(String.valueOf(Optional.of(commonModifierResponse.getApplicationId()).orElse(0)));
        orchRequestDetails.setAffiliateId(Optional.ofNullable(commonModifierResponse.getAffiliateId()).orElse(""));
        orchRequestDetails.setCdfContextId(Optional.ofNullable(commonModifierResponse.getCdfContextId()).orElse(""));

        // Set additional maps
        orchRequestDetails.setSessionData(Collections.emptyMap());
        orchRequestDetails.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
        orchRequestDetails.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());

        // Basic request details - FIX: Add null safety for all requestDetails field access
        if (requestDetails != null) {
            orchRequestDetails.setRequestor(requestDetails.getRequestor());
            orchRequestDetails.setVisitorId(Optional.ofNullable(requestDetails.getVisitorId()).orElse(""));
            orchRequestDetails.setChannel(Optional.ofNullable(requestDetails.getChannel()).orElse(""));
            orchRequestDetails.setSessionId(Optional.ofNullable(requestDetails.getSessionId()).orElse(""));

            // Set funnel source
            orchRequestDetails.setFunnelSource(Optional.ofNullable(requestDetails.getFunnelSource())
                    .map(Funnel::fromValue)
                    .orElse(Funnel.HOTELS));

            // Set page context
            orchRequestDetails.setPageContext(Optional.ofNullable(requestDetails.getPageContext())
                    .map(PageContext::fromValue)
                    .orElse(PageContext.LISTING));

            // Set brand
            orchRequestDetails.setBrand(Optional.ofNullable(requestDetails.getBrand())
                    .map(Brand::valueOf)
                    .orElse(Brand.MMT));

            // Set ID context
            orchRequestDetails.setIdContext(Optional.ofNullable(requestDetails.getIdContext())
                    .map(IdContext::valueOf)
                    .orElse(IdContext.B2C));

            // Set site domain
            orchRequestDetails.setSiteDomain(Optional.ofNullable(requestDetails.getSiteDomain())
                    .map(String::toUpperCase)
                    .map(SiteDomain::valueOf)
                    .orElse(SiteDomain.IN));

            // Set traffic source and type - FIX: Add null safety for trafficSource
            if (requestDetails.getTrafficSource() != null) {
                orchRequestDetails.setTrafficSource(requestDetails.getTrafficSource().getSource());
                orchRequestDetails.setTrafficFlowType(requestDetails.getTrafficSource().getFlowType());
            }

            // Traffic Source and Type - Safely set from requestDetails
            if (StringUtils.isNotEmpty(requestDetails.getSiteDomain()) && Utility.isRegionGccOrKsa(requestDetails.getSiteDomain())) {
                orchRequestDetails.setTrafficType(TrafficType.GCC);
            } else if (utility.isMyPartner(commonModifierResponse)) {
                orchRequestDetails.setTrafficType(TrafficType.MYPARTNER);
            } else {
                orchRequestDetails.setTrafficType(Optional.ofNullable(requestDetails.getTrafficSource())
                        .map(com.mmt.hotels.clientgateway.request.TrafficSource::getType)
                        .map(TrafficType::fromValue)
                        .orElse(TrafficType.B2C));  // Default to B2C if not present
            }

            // Set semantic search details if available
            if (requestDetails.getSemanticSearchDetails() != null) {
                com.gommt.hotels.orchestrator.detail.model.request.matchmaker.SemanticSearchDetails semanticSearchDetails =
                        new com.gommt.hotels.orchestrator.detail.model.request.matchmaker.SemanticSearchDetails();
                semanticSearchDetails.setQueryText(requestDetails.getSemanticSearchDetails().getQueryText());
                semanticSearchDetails.setSemanticData(requestDetails.getSemanticSearchDetails().getSemanticData());
                orchRequestDetails.setSemanticSearchDetails(semanticSearchDetails);
            }

            // Set request context
            orchRequestDetails.setReqContext(requestDetails.isPremium() ? Constants.PREMIUM : Constants.DEFAULT);
        } else {
            // Set default values when requestDetails is null
            orchRequestDetails.setRequestor("");
            orchRequestDetails.setVisitorId("");
            orchRequestDetails.setChannel("");
            orchRequestDetails.setSessionId("");
            orchRequestDetails.setFunnelSource(Funnel.HOTELS);
            orchRequestDetails.setPageContext(PageContext.LISTING);
            orchRequestDetails.setBrand(Brand.MMT);
            orchRequestDetails.setIdContext(IdContext.B2C);
            orchRequestDetails.setSiteDomain(SiteDomain.IN);
            orchRequestDetails.setTrafficType(TrafficType.B2C);
            orchRequestDetails.setReqContext(Constants.DEFAULT);
        }

        LOGGER.info("Successfully built RequestDetails with requestId: {}", orchRequestDetails.getRequestId());
        return orchRequestDetails;
    }

    private BookingDevice buildBookingDevice(DeviceDetails deviceDetails) {
        if (deviceDetails == null) {
            LOGGER.warn("DeviceDetails is null, returning an empty BookingDevice.");
            return BookingDevice.builder().build();  // Return an empty BookingDevice if deviceDetails is null
        }

        BookingDevice.BookingDeviceBuilder bookingDeviceBuilder = BookingDevice.builder();

        // Safely set device fields, defaulting to empty strings or enums if values are null
        bookingDeviceBuilder.deviceId(Optional.ofNullable(deviceDetails.getDeviceId()).orElse(""));
        bookingDeviceBuilder.deviceName(Optional.ofNullable(deviceDetails.getDeviceName()).orElse(""));

        // FIX: Add null safety for getBookingDevice
        DeviceType deviceType = Optional.ofNullable(deviceDetails.getBookingDevice())
                .map(DeviceType::fromValue)
                .orElse(DeviceType.DESKTOP);
        bookingDeviceBuilder.deviceType(deviceType);

        bookingDeviceBuilder.appVersion(Optional.ofNullable(deviceDetails.getAppVersion()).orElse(""));
        bookingDeviceBuilder.networkType(
                Optional.ofNullable(deviceDetails.getNetworkType())
                        .orElse("")
                        .toUpperCase()
        );
        LOGGER.info("Successfully built BookingDevice with deviceId: {}", deviceDetails.getDeviceId());
        return bookingDeviceBuilder.build();
    }

    private com.gommt.hotels.orchestrator.detail.model.objects.MultiCurrencyInfo buildMultiCurrencyInfo(com.mmt.hotels.clientgateway.request.MultiCurrencyInfo multiCurrencyInfo) {
        if(multiCurrencyInfo==null)
            return null;
        return com.gommt.hotels.orchestrator.detail.model.objects.MultiCurrencyInfo.builder()
                .userCurrency(multiCurrencyInfo.getUserCurrency())
                .regionCurrency(multiCurrencyInfo.getRegionCurrency())
                .build();
    }

    private UserDetails buildUserDetails(com.mmt.hotels.clientgateway.request.RequestDetails requestDetails, CommonModifierResponse commonModifierResponse) {
        UserDetails userDetails = new UserDetails();

        // Safely set the location details using LocationDetails builder
        LocationDetails locationDetails = LocationDetails.builder()
                .cityName(Optional.ofNullable(commonModifierResponse.getUserLocation())
                        .map(UserLocation::getCity)
                        .orElse(""))  // Default to empty string if city is null
                .cityId(Optional.ofNullable(commonModifierResponse.getUserLocation())
                        .map(UserLocation::getCity)
                        .orElse(""))  // Default to empty string if cityId is null
                .countryId(Optional.ofNullable(commonModifierResponse.getUserLocation())
                        .map(UserLocation::getCountry)
                        .orElse(""))  // Default to empty string if countryId is null
                .stateId(Optional.ofNullable(commonModifierResponse.getUserLocation())
                        .map(UserLocation::getState)
                        .orElse(""))  // Default to empty string if stateId is null
                .build();
        userDetails.setLocation(locationDetails);
        userDetails.setMobile(commonModifierResponse.getMobile());

        // FIX: Cache extendedUser to avoid multiple null checks
        ExtendedUser extendedUser = commonModifierResponse.getExtendedUser();
        
        // Safely set mmtAuth, uuid, profileType, and subProfileType with default values if null
        userDetails.setMmtAuth(Optional.ofNullable(commonModifierResponse.getMmtAuth()).orElse(""));
        userDetails.setUuid(Optional.ofNullable(extendedUser).map(ExtendedUser::getUuid).orElse(""));

        UserSessionData userSessionData = new UserSessionData();
        // FIXED BUSINESS LOGIC: Use getSessionId() instead of getSessionId() (matching OrchListingService line 1127)
        userSessionData.setSessionId(Optional.ofNullable(requestDetails)
                .map(com.mmt.hotels.clientgateway.request.RequestDetails::getSessionId)
                .orElse(""));

        userSessionData.setUuid(userDetails.getUuid());
        userDetails.setSessionData(userSessionData);

        // FIX: Safely map profileType and subProfileType using cached extendedUser
        userDetails.setProfileType(Optional.ofNullable(extendedUser)
                .map(ExtendedUser::getProfileType)
                .map(ProfileType::valueOf)
                .orElse(null));  // Default to null if profileType is null

        userDetails.setSubProfileType(Optional.ofNullable(extendedUser)
                .map(ExtendedUser::getAffiliateId)
                .map(SubProfileType::fromValue)
                .orElse(SubProfileType.DEFAULT));
        // Default to DEFAULT if subProfileType is null

        // Safely set loggedIn, defaulting to false if null
        userDetails.setLoggedIn(Optional.ofNullable(requestDetails)
                .map(com.mmt.hotels.clientgateway.request.RequestDetails::isLoggedIn)
                .orElse(false));

        // Safely set user segments
        List<String> userSegmentsList = new ArrayList<>(Optional.ofNullable(commonModifierResponse.getHydraResponse())
                .map(HydraResponse::getHydraMatchedSegment)
                .orElse(Collections.emptySet()));
        userDetails.setUserSegments(userSegmentsList);
        LOGGER.info("Successfully built UserDetails for UUID: {}", userDetails.getUuid());
        return userDetails;
    }

    public static com.gommt.hotels.orchestrator.detail.model.objects.UserGlobalInfo buildUserGlobalInfo(UserGlobalInfo userGlobalInfo) {
        com.gommt.hotels.orchestrator.detail.model.objects.UserGlobalInfo userGlobalInfoOrch = new com.gommt.hotels.orchestrator.detail.model.objects.UserGlobalInfo();
        userGlobalInfoOrch.setUserCountry(userGlobalInfo.getUserCountry());
        userGlobalInfoOrch.setEntityName(userGlobalInfo.getEntityName());
        return userGlobalInfoOrch;
    }
}
