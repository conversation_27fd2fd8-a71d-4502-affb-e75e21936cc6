package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.request.hostcalling.HostCallingInitiateRequestBody;
import com.mmt.hotels.clientgateway.response.hostcalling.HostCallingInitiateResponse;
import com.mmt.hotels.clientgateway.service.HostCallingService;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.util.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.ControllerConstants.HOST_CALLING_INITIATE;

@RestController
@RequestMapping("/")
@Slf4j
public class HostCallingController {

    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    private HostCallingService hostCallingService;

    @Autowired
    private MetricAspect metricAspect;

    @PostMapping(value = "cg/hostCalling/initiate/{client}/{version}")
    public ResponseEntity<ResponseWrapper<HostCallingInitiateResponse>> initiateHostCalling(
            @PathVariable("client") String client,
            @PathVariable("version") String version,
            @Valid @RequestBody HostCallingInitiateRequestBody hostCallingRequest,
            HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
            @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId
    ) throws ClientGatewayException {
        
        long startTime = new Date().getTime();
        
        try {
            client = client.toUpperCase();
            ck = requestHandler.effectiveCorrelationKey(requestId, ck);
            
            Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, hostCallingRequest.getRequestDetails().getIdContext(), HOST_CALLING_INITIATE, hostCallingRequest);
            String correlationKey = tup.getX();
            MDC.put(MDCHelper.MDCKeys.CORRELATION.getStringValue(), correlationKey);
            
            // Set client in request for factory pattern (same as static detail)
            hostCallingRequest.setClient(client);
            
            HostCallingInitiateResponse response = hostCallingService.initiateHostCalling(hostCallingRequest, httpServletRequest.getParameterMap(), tup.getY());
            
            // Create ResponseWrapper with the service response
            ResponseWrapper<HostCallingInitiateResponse> responseWrapper = new ResponseWrapper<>();
            responseWrapper.setResponse(response);
            responseWrapper.setCorrelationKey(correlationKey);
            
            return new ResponseEntity<>(responseWrapper, HttpStatus.OK);
            
        } finally {
            metricAspect.addToTime(
                new Date().getTime() - startTime, 
                HOST_CALLING_INITIATE,
                MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), 
                MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), 
                client, 
                MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue())
            );
            MDC.clear();
        }
    }
} 