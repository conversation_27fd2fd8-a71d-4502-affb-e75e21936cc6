package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.request.OfferDetailsRequest;
import com.mmt.hotels.clientgateway.request.payment.PaymentRequestClient;
import com.mmt.hotels.clientgateway.response.PaymentResponse;
import com.mmt.hotels.clientgateway.restexecutors.AvailRoomsExecutor;
import com.mmt.hotels.clientgateway.service.PaymentService;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.util.ClientBackendUtility;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.model.request.payment.BeginCheckoutReqBody;
import com.mmt.hotels.model.response.base.FailureReason;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import com.mmt.hotels.model.response.payment.PaymentCheckoutResponse;
import com.mmt.hotels.model.response.payment.offerdetails.OfferDetailsResponse;
import com.mmt.hotels.util.Tuple;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.Map;

@RestController
@RequestMapping("/")
public class PaymentController {

    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private MetricAspect metricAspect;

    @RequestMapping(value = "entity/api/hotels/paymentCheckout", method = RequestMethod.POST)
    public PaymentCheckoutResponse paymentCheckout(
            @RequestParam(name = Constants.REGION, required = false) String siteDomain,
            @RequestParam(name = Constants.CURRENCY, required = false) String currency,
            @RequestParam(name = Constants.LANGUAGE, required = false) String language,
            @RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
            @Valid @RequestBody BeginCheckoutReqBody paymentRequest, HttpServletRequest httpRequest , HttpServletResponse httpResponse)
             {
        PaymentCheckoutResponse response = null;
        long startTime = new Date().getTime();
        try {
            correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
            Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpRequest,httpResponse,correlationKey,null, paymentRequest.getIdContext() , "entity/api/hotels/paymentCheckout",null);
            correlationKey = tup.getX();
            paymentRequest.setCorrelationKey(correlationKey);
            if(StringUtils.isNotBlank(siteDomain)){
                paymentRequest.setSiteDomain(siteDomain.toUpperCase());
            }
            paymentRequest.setSkipDoubleBlack(paymentRequest.isDoubleBlackValidated());
            response = (paymentService.paymentCheckoutOld(paymentRequest, httpRequest));
        } catch (Exception e) {
            response  = new PaymentCheckoutResponse();
            Tuple<ResponseErrors, FailureReason> tup=  ClientBackendUtility.setResponseErrors(CBError.GENERIC_ERROR);
            response.setResponseErrors(tup.getX());
            response.setFailureReason(tup.getY());
        } finally {
            metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/hotels/paymentCheckout",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()),"", MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

            MDC.clear();
        }
        return response;
    }

    private static final Logger logger = LoggerFactory.getLogger(AvailRoomsExecutor.class);

    @RequestMapping(value = "cg/payment-checkout/{client}/{version}", method = RequestMethod.POST)
    public PaymentResponse paymentCheckoutV2(
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody PaymentRequestClient paymentRequest, HttpServletRequest httpRequest, HttpServletResponse httpResponse) throws ClientGatewayException {
        client=client.toUpperCase();
        if(paymentRequest.isTncClauseChecked()){
            logger.debug("User has accepted Terms & Conditions");
        }
        long startTime = new Date().getTime();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String,Map<String,String>> tup = requestHandler.handleCommonRequest(httpRequest,httpResponse, ck,client, paymentRequest.getIdContext(),"cg/paymentcheckout",null);
        ck =  tup.getX();
        paymentRequest.setCorrelationKey(ck);
        PaymentResponse response  = (paymentService.paymentCheckout(paymentRequest, httpRequest, client, version));
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/payment_checkout",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
        return response;
    }

    @RequestMapping(value = "cg/getOfferDetails", method = RequestMethod.POST)
    public OfferDetailsResponse getOfferDetails(@Valid @RequestBody OfferDetailsRequest offerDetailsRequest, HttpServletRequest httpRequest, HttpServletResponse httpResponse) throws JsonParseException, RestConnectorException {
        long startTime = new Date().getTime();
        Tuple<String,Map<String,String>> tup = requestHandler.handleCommonRequest(httpRequest,httpResponse, null,null, null,"cg/getOfferDetails",null);
        offerDetailsRequest.setCorrelationKey(tup.getX());
        return paymentService.getOfferDetails(offerDetailsRequest, httpRequest);
    }

}