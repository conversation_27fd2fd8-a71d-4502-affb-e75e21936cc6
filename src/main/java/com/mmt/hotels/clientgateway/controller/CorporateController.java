package com.mmt.hotels.clientgateway.controller;

import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ControllerConstants;
import com.mmt.hotels.clientgateway.enums.ValidationErrors;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CorporateHelper;
import com.mmt.hotels.clientgateway.request.InitApprovalRequest;
import com.mmt.hotels.clientgateway.request.UpdateApprovalRequest;
import com.mmt.hotels.clientgateway.request.UpdatePolicyRequest;
import com.mmt.hotels.clientgateway.request.corporate.GuestHouseRequest;
import com.mmt.hotels.clientgateway.response.InitApprovalResponse;
import com.mmt.hotels.clientgateway.response.corporate.*;
import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.CorporateService;
import com.mmt.hotels.clientgateway.util.HeadersUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.clientgateway.util.MaskingUtil;
import com.mmt.hotels.util.Tuple;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.GUEST_HOUSES_API;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.DETAIL_STATIC_DETAIL;

@RestController
@RequestMapping("/")
public class CorporateController {

    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    CorporateService corporateService;

    @Autowired
    CorporateHelper corporateHelper;

    @Autowired
    MetricAspect metricAspect;

    private static final Gson gson = new Gson();
    private static final Logger LOGGER = LoggerFactory.getLogger(CorporateController.class);


    @RequestMapping(value = "cg/update-policy/{client}/{version}", method = RequestMethod.POST)
    public UpdatePolicyResponse updateCorpPolicy(@RequestBody UpdatePolicyRequest request, @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
                                                                                  @PathVariable("client") String client, @PathVariable("version") String version,
                                                                                  HttpServletRequest httpServletRequest,
                                                                                  HttpServletResponse httpServletResponse) throws ClientGatewayException {

        long startTime = new Date().getTime();
            client=client.toUpperCase();
            correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
            Tuple<String,Map<String,String>> tup = requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,correlationKey,client, Constants.CORP_ID_CONTEXT , "cg/update-policy", null);
             correlationKey =  tup.getX();

            UpdatePolicyResponse updatePolicyResponse = (corporateService.updatePolicy(request, httpServletRequest.getParameterMap(), tup.getY(), client, correlationKey));
         metricAspect.addToTime(new Date().getTime() - startTime, "cg/update_policy",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
            return updatePolicyResponse;

    }


    @RequestMapping(value = "cg/approvals/{client}/{version}", method = RequestMethod.POST)
    public InitApprovalResponse initApproval(@RequestBody InitApprovalRequest request, @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
                                         @PathVariable("client") String client, @PathVariable("version") String version,
                                         HttpServletRequest httpServletRequest,
                                         HttpServletResponse httpServletResponse) throws ClientGatewayException {

        InitApprovalResponse initApprovalResponse = new InitApprovalResponse();
        long startTime = new Date().getTime();
            client=client.toUpperCase();
            correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
            Tuple<String,Map<String,String>> tup = requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,correlationKey,client, Constants.CORP_ID_CONTEXT , "cg/update-policy", null);
            correlationKey =  tup.getX();
            LOGGER.warn("Init approval request from client:{}", MaskingUtil.maskSensitiveDataAndLog(gson.toJson(request)));
            initApprovalResponse = corporateService.initiateApproval(request, httpServletRequest.getParameterMap(), tup.getY(), client, correlationKey);
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/approvals",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
            return initApprovalResponse;

    }


    @RequestMapping(value = "cg/get-approvals/{client}/{version}", method = RequestMethod.GET)
    public ResponseEntity<ResponseWrapper<GetApprovalsResponse>> getApprovalsInfo(@PathVariable("client") String srcClient,
                                                                                  @PathVariable("version") String version,
                                                                                  @RequestParam(name = Constants.WORK_FLOW_ID, required = false) String workflowId,
                                                                                  @RequestParam(name = Constants.auth_code, required = false) String authCode,
                                                                                  @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
                                                                                  @RequestParam(name = Constants.LANGUAGE, required = false) String language,
                                                                                  @RequestParam(name = Constants.REGION, required = false) String siteDomain,
                                                                                  @RequestParam(name = Constants.CURRENCY, required = false) String currency,
                                                                                  @RequestParam(name = Constants.ID_CONTEXT, required = false) String idContext,
                                                                                  HttpServletRequest httpRequest) throws ClientGatewayException {

        ResponseWrapper<GetApprovalsResponse> getApprovalsResponseResponseWrapper = new ResponseWrapper<>();

        siteDomain = StringUtils.isNotBlank(siteDomain) ? siteDomain : Constants.DEFAULT_SITE_DOMAIN;
        language = StringUtils.isNotBlank(language) ? language : Constants.DEFAULT_LANGUAGE;
        currency = StringUtils.isNotBlank(currency) ? currency : Constants.DEFAULT_CUR_INR;

        MDCHelper.createMDC(srcClient, null, correlationKey, siteDomain.toUpperCase(), language, currency.toUpperCase(), ControllerConstants.GET_APPROVALS, idContext, StringUtils.EMPTY, null, null);
        Map<String,String> httpHeaderMap = HeadersUtil.getHeadersFromServletRequest(httpRequest);
        getApprovalsResponseResponseWrapper.setResponse(corporateService.approvalsInfo(workflowId,authCode, httpRequest.getParameterMap(), httpHeaderMap, correlationKey, siteDomain, srcClient));
        getApprovalsResponseResponseWrapper.setCorrelationKey(correlationKey);
        MDC.clear();
        return  new ResponseEntity<>(getApprovalsResponseResponseWrapper, HttpStatus.OK);
    }

    @RequestMapping(value = "cg/update-approvals/{client}/{version}", method = RequestMethod.PUT)
    public ResponseEntity<ResponseWrapper<UpdateApprovalResponse>> updateApproval(
        @RequestBody UpdateApprovalRequest updateApprovalRequest,
        @NotEmpty @PathVariable(Constants.CLIENT) String client,
        @NotEmpty @PathVariable(Constants.VERSION) String version,
        @RequestParam(name = Constants.WORK_FLOW_ID, required = false, defaultValue = Constants.EMPTY_STRING) String workflowId,
        @RequestParam(name = Constants.auth_code, required = false, defaultValue = Constants.EMPTY_STRING) String authCode,
        @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
        @RequestParam(name = Constants.ID_CONTEXT, required = false, defaultValue = Constants.CORP_ID_CONTEXT) String idContext,
        HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws ClientGatewayException {
        long startTime = new Date().getTime();
        client=client.toUpperCase();
        Tuple<String, Map<String, String>> tup = requestHandler
                                                     .handleCommonRequest(httpServletRequest, httpServletResponse,
                                                                          correlationKey, client, idContext,
                                                                          "cg/update-approvals", null);
        correlationKey =  tup.getX();
        ResponseWrapper<UpdateApprovalResponse> updateApprovalResponseWrapper = new ResponseWrapper<>();
        updateApprovalResponseWrapper.setCorrelationKey(correlationKey);
        if(StringUtils.isBlank(workflowId) && StringUtils.isBlank(authCode)) {
            updateApprovalResponseWrapper.setError(
                    new Error(ValidationErrors.WORKFLOW_ID_AND_AUTHCODE_NULL.getErrorCode(),
                            ValidationErrors.WORKFLOW_ID_AND_AUTHCODE_NULL.getErrorMsg()));
        }
        else {
            UpdateApprovalResponse response = corporateService.updateApprovals(updateApprovalRequest,
                                                        httpServletRequest.getParameterMap(), tup.getY(), authCode, workflowId, correlationKey, client);
            updateApprovalResponseWrapper.setResponse(response);
            updateApprovalResponseWrapper.setError(response.getError());

        }
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/update-approvals",
                               MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()),
                               MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client,
                               MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
        MDC.clear();
        return new ResponseEntity<>(updateApprovalResponseWrapper, HttpStatus.OK);
    }

    @PostMapping(value = {"cg/guest-houses/details", "cg/guest-houses/details/{client}/{version}"})
    public ResponseEntity<ResponseWrapper<GuestHouses>> getGuestHouseDetails(@RequestBody GuestHouseRequest request,
                                                                             HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
                                                                             @RequestParam(name = "requestId") String requestId,
                                                                             @PathVariable(name = "client", required = false) String client,
                                                                             @PathVariable(name = "version", required = false) String version,
                                                                             @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey) throws ClientGatewayException {
        long startTime = new Date().getTime();
        client = (client != null) ? client.toUpperCase() : "DEFAULT_CLIENT";
        version = (version != null) ? version : "DEFAULT_VERSION";
        Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, correlationKey, client, Constants.CORP_ID_CONTEXT, GUEST_HOUSES_API, null);
        correlationKey = tup.getX();
        GuestHouseResponse response = corporateService.getAvailableGuestHouses(request.getTransactionKey(), httpServletRequest.getParameterMap(), correlationKey, tup.getY());
        GuestHouses guestHouses = new GuestHouses();
        guestHouses.setAvailableGuestHouse(response);
        ResponseWrapper<GuestHouses> updateApprovalResponseWrapper = new ResponseWrapper<>();
        updateApprovalResponseWrapper.setCorrelationKey(requestId);
        updateApprovalResponseWrapper.setResponse(guestHouses);
        updateApprovalResponseWrapper.setError(response.getError());
        metricAspect.addToTime(new Date().getTime() - startTime, GUEST_HOUSES_API,
                MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()),
                MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client,
                MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
        MDC.clear();
        return new ResponseEntity<>(updateApprovalResponseWrapper, HttpStatus.OK);
    }

}
