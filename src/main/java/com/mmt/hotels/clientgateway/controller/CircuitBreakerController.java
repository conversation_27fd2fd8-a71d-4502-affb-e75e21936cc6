package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.service.CircuitBreakerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Circuit Breaker Controller
 * 
 * Provides monitoring and management endpoints for circuit breaker functionality.
 * This controller handles circuit breaker state monitoring, metrics collection,
 * and health checks for all configured circuit breakers in the system.
 * 
 * <AUTHOR> Breaker Team
 * @version 1.0
 */
@RestController
@RequestMapping("/")
public class CircuitBreakerController {

    private static final Logger logger = LoggerFactory.getLogger(CircuitBreakerController.class);

    @Autowired
    private CircuitBreakerService circuitBreakerService;

    /**
     * Circuit Breaker State Monitoring Endpoint
     * 
     * Returns the current state of a specific circuit breaker.
     * Possible states: CLOSED, OPEN, HALF_OPEN, DISABLED, FORCED_OPEN
     * 
     * @param circuitBreakerName Name of the circuit breaker to monitor
     * @return ResponseEntity containing circuit breaker state information
     */
    @RequestMapping(value = "cg/circuit-breaker/state/{circuitBreakerName}", method = RequestMethod.GET)
    public ResponseEntity<Map<String, String>> getCircuitBreakerState(
            @PathVariable("circuitBreakerName") String circuitBreakerName) {

        logger.debug("Getting circuit breaker state for: {}", circuitBreakerName);

        String state = circuitBreakerService.getCircuitBreakerState(circuitBreakerName);
        Map<String, String> response = new HashMap<>();
        response.put("circuitBreakerName", circuitBreakerName);
        response.put("state", state);
        response.put("timestamp", new Date().toString());

        logger.debug("Circuit breaker {} state: {}", circuitBreakerName, state);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    /**
     * Circuit Breaker Metrics Monitoring Endpoint
     * 
     * Returns detailed metrics for a specific circuit breaker including:
     * - Number of successful calls
     * - Number of failed calls
     * - Failure rate
     * - Slow call rate
     * - Current state
     * 
     * @param circuitBreakerName Name of the circuit breaker to get metrics for
     * @return ResponseEntity containing circuit breaker metrics
     */
    @RequestMapping(value = "cg/circuit-breaker/metrics/{circuitBreakerName}", method = RequestMethod.GET)
    public ResponseEntity<Map<String, Object>> getCircuitBreakerMetrics(
            @PathVariable("circuitBreakerName") String circuitBreakerName) {

        logger.debug("Getting circuit breaker metrics for: {}", circuitBreakerName);

        Map<String, Object> metrics = circuitBreakerService.getCircuitBreakerMetrics(circuitBreakerName);
        metrics.put("timestamp", new Date().toString());

        logger.debug("Circuit breaker {} metrics retrieved successfully", circuitBreakerName);
        return new ResponseEntity<>(metrics, HttpStatus.OK);
    }

    /**
     * All Circuit Breakers Health Check Endpoint
     * 
     * Returns comprehensive health status for all configured circuit breakers.
     * This endpoint provides a consolidated view of:
     * - mob-landing circuit breaker
     * - detail-api circuit breaker  
     * - listing-api circuit breaker
     * 
     * Each circuit breaker entry includes both state and metrics information.
     * 
     * @return ResponseEntity containing health status of all circuit breakers
     */
    @RequestMapping(value = "cg/circuit-breaker/health", method = RequestMethod.GET)
    public ResponseEntity<Map<String, Object>> getCircuitBreakersHealth() {

        logger.debug("Getting health status for all circuit breakers");

        Map<String, Object> mobLandingHealth = new HashMap<>();
        mobLandingHealth.put("state", circuitBreakerService.getCircuitBreakerState("mob-landing"));
        mobLandingHealth.put("metrics", circuitBreakerService.getCircuitBreakerMetrics("mob-landing"));

        Map<String, Object> detailApiHealth = new HashMap<>();
        detailApiHealth.put("state", circuitBreakerService.getCircuitBreakerState("detail-api"));
        detailApiHealth.put("metrics", circuitBreakerService.getCircuitBreakerMetrics("detail-api"));

        Map<String, Object> listingApiHealth = new HashMap<>();
        listingApiHealth.put("state", circuitBreakerService.getCircuitBreakerState("listing-api"));
        listingApiHealth.put("metrics", circuitBreakerService.getCircuitBreakerMetrics("listing-api"));

        Map<String, Object> healthStatus = new HashMap<>();
        healthStatus.put("mobLanding", mobLandingHealth);
        healthStatus.put("detailApi", detailApiHealth);
        healthStatus.put("listingApi", listingApiHealth);
        healthStatus.put("timestamp", new Date().toString());

        logger.debug("Circuit breakers health status retrieved successfully");
        return new ResponseEntity<>(healthStatus, HttpStatus.OK);
    }
}
