package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ControllerConstants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.BaseSearchRequest;
import com.mmt.hotels.clientgateway.request.gstDetails.SaveGstDetailsRequest;
import com.mmt.hotels.clientgateway.request.gstDetails.TravellerGstDetails;
import com.mmt.hotels.clientgateway.response.gstDetails.GstDetailsResponse;
import com.mmt.hotels.clientgateway.service.GstDetailsService;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.util.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/")
@Slf4j
public class GstDetailsController {

    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    private GstDetailsService gstDetailsService;

    @Autowired
    private MetricAspect metricAspect;

    @PostMapping(ControllerConstants.SAVE_TRAVELLER_GST_DETAILS + ControllerConstants.COMMON_QUERY_PARAMS)
    public ResponseEntity<GstDetailsResponse<TravellerGstDetails>> saveGstDetails(
            @PathVariable(Constants.CLIENT) String client,
            @PathVariable(Constants.VERSION) String version,
            @RequestBody SaveGstDetailsRequest saveGstDetailsRequest,
            HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
            @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId
    ) throws ClientGatewayException {
        long startTime = new Date().getTime();

        client=client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);

        Tuple<String, Map<String,String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, Constants.B2B_ID_CONTEXT, ControllerConstants.SAVE_TRAVELLER_GST_DETAILS, null);

        GstDetailsResponse<TravellerGstDetails> response = gstDetailsService.saveGstDetails(saveGstDetailsRequest, httpServletRequest.getParameterMap(), tup.getY(), client);
        response.setCorrelationKey(ck);

        metricAspect.addToTime(new Date().getTime() - startTime, ControllerConstants.SAVE_TRAVELLER_GST_DETAILS, MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @PostMapping(ControllerConstants.GET_TRAVELLER_GST_DETAILS + ControllerConstants.COMMON_QUERY_PARAMS)
    public ResponseEntity<GstDetailsResponse<List<TravellerGstDetails>>> getGstDetails(
            @PathVariable(Constants.CLIENT) String client,
            @PathVariable(Constants.VERSION) String version,
            @RequestBody BaseSearchRequest getGstDetailsRequest,
            HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
            @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId
    ) throws ClientGatewayException {
        long startTime = new Date().getTime();

        client=client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);

        Tuple<String, Map<String,String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, Constants.B2B_ID_CONTEXT, ControllerConstants.GET_TRAVELLER_GST_DETAILS, null);

        GstDetailsResponse<List<TravellerGstDetails>> response = gstDetailsService.getGstDetails(getGstDetailsRequest, httpServletRequest.getParameterMap(), tup.getY(), client);
        response.setCorrelationKey(ck);

        metricAspect.addToTime(new Date().getTime() - startTime, ControllerConstants.GET_TRAVELLER_GST_DETAILS, MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
}
