package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.ControllerConstants;
import com.mmt.hotels.clientgateway.request.modification.ProBookingRequest;
import com.mmt.hotels.clientgateway.request.modification.RatePreviewRequest;
import com.mmt.hotels.clientgateway.response.modification.ProBookingResponse;
import com.mmt.hotels.clientgateway.response.modification.RatePreviewResponse;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.BookingModificationService;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.util.Tuple;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.ControllerConstants.DETAIL_SEARCH_ROOMS;

@RestController
@RequestMapping("/")
public class BookingModificationController {

    @Autowired
    BookingModificationService bkgModService;

    @Autowired
    RequestHandler requestHandler;

    @Autowired
    Utility utility;

    private static final Logger logger = LoggerFactory.getLogger(BookingModificationController.class);

    @RequestMapping("cg/{version}/rebook/avail")
    public ResponseEntity<RatePreviewResponse> fetchDetailPrice(@Valid @RequestBody RatePreviewRequest rateReviewRequest,
            @PathVariable("version") String version,
             HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse){
        utility.logRequestResponse(rateReviewRequest,"rebook avail request {}");
        String client = StringUtils.isNotBlank(rateReviewRequest.getClient())?  rateReviewRequest.getClient().toUpperCase():"";
        logger.debug("Booking Modification avail request is :{}", rateReviewRequest);
        Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, null, client, rateReviewRequest.getIdContext(), ControllerConstants.REBOOK_DETAIL, null);
        String correlationKey = tup.getX();
        utility.logRequestResponse(tup.getY(),"rebook avail request headers {}");
        RatePreviewResponse ratePreviewResponse = bkgModService.fetchDetailPrice(rateReviewRequest, tup.getY());
        utility.logRequestResponse(ratePreviewResponse,"rebook avail response {}");
        MDC.clear();
        return new ResponseEntity<RatePreviewResponse>(ratePreviewResponse, HttpStatus.OK);
    }

    @RequestMapping("cg/{version}/rebook/avail-validate")
    public ResponseEntity<RatePreviewResponse> fetchReviewPrice( @Valid @RequestBody RatePreviewRequest rateReviewRequest,
             @PathVariable("version") String version,
             HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse){
        utility.logRequestResponse(rateReviewRequest,"rebook avail validate request {}");

        String client = StringUtils.isNotBlank(rateReviewRequest.getClient())?  rateReviewRequest.getClient().toUpperCase():"";
        Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, null, client, rateReviewRequest.getIdContext(), ControllerConstants.REBOOK_REVIEW, null);
        String correlationKey = tup.getX();
        utility.logRequestResponse(tup.getY(),"rebook avail-validate request headers {}");
        RatePreviewResponse ratePreviewResponse = bkgModService.fetchReviewPrice(rateReviewRequest, tup.getY());
        utility.logRequestResponse(ratePreviewResponse,"rebook avail validate response {}");

        MDC.clear();
        return new ResponseEntity<RatePreviewResponse>(ratePreviewResponse, HttpStatus.OK);
    }

    @RequestMapping("cg/{version}/rebook/create-provisional-booking")
    public ResponseEntity<ProBookingResponse> createProBooking(@Valid @RequestBody ProBookingRequest proBookingRequest,
               @PathVariable("version") String version,
             HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse){
        utility.logRequestResponse(proBookingRequest,"rebook create-provisional-booking request {}");
        String client = proBookingRequest.getPaymentDetail() != null && StringUtils.isNotBlank(proBookingRequest.getPaymentDetail().getMode())?  proBookingRequest.getPaymentDetail().getMode().toUpperCase():"";

        Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, null, client,proBookingRequest.getPaymentDetail() != null ? proBookingRequest.getPaymentDetail().getMode(): null, ControllerConstants.REBOOK_PAYMENT, null);
        String correlationKey = tup.getX();
        utility.logRequestResponse(tup.getY(),"rebook create-provisional-booking request headers {}");
        ProBookingResponse proBookingResponse = bkgModService.createProBooking(proBookingRequest, tup.getY());
        utility.logRequestResponse(proBookingResponse,"rebook create-provisional-booking response {}");
        MDC.clear();
        return new ResponseEntity<ProBookingResponse>(proBookingResponse, HttpStatus.OK);
    }
}

