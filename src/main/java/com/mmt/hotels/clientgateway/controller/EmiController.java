package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ControllerConstants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.EMIDetailRequest;
import com.mmt.hotels.clientgateway.response.emi.EMIDetailResponse;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.EmiService;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.util.Tuple;
import org.apache.commons.collections.MapUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.BRAND;
import static com.mmt.hotels.clientgateway.constants.Constants.BRAND_MMT;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.REVIEW_AVAIL_ROOMS;

@RestController
@RequestMapping("/cg")
public class EmiController {

    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    private EmiService emiService;

    @Autowired
    private MetricAspect metricAspect;

    @RequestMapping(value = "/fetch-emi-details/v1.0/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<EMIDetailResponse>> fetchEmiDetails(
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody EMIDetailRequest emiDetailRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, defaultValue = Constants.EMPTY_STRING) String ck,
            @RequestParam(name = BRAND, defaultValue = BRAND_MMT) String brand) throws ClientGatewayException {
        long startTime = new Date().getTime();
        client = client.toUpperCase();
        Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, null, ControllerConstants.FETCH_EMI_DETAILS, emiDetailRequest);
        String correlationKey = tup.getX();
        requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, emiDetailRequest, correlationKey);
        if (MapUtils.isNotEmpty(tup.getY())) {
            tup.getY().put(Constants.EMI_DETAIL_REQUEST, "true");
        }

        ResponseWrapper<EMIDetailResponse> emiDetailsResponseWrapper = new ResponseWrapper<>();
        emiDetailRequest.setBrand(brand);
        EMIDetailResponse emiDetailResponse = emiService.fetchEmiDetails(emiDetailRequest);
        emiDetailsResponseWrapper.setResponse(emiDetailResponse);
        emiDetailsResponseWrapper.setCorrelationKey(correlationKey);
        metricAspect.addToTime(new Date().getTime() - startTime, "/fetch-emi-details/v1.0/", MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
        return new ResponseEntity<>(emiDetailsResponseWrapper, HttpStatus.OK);
    }
}
