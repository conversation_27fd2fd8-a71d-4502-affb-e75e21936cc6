package com.mmt.hotels.clientgateway.controller;

import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.constants.ByPassUrls;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.UnexpectedErrors;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.restexecutors.ByPassExecutor;
import com.mmt.hotels.clientgateway.util.ClientBackendUtility;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.model.response.altaccodata.AltAcooDataBenifitsResponse;
import com.mmt.hotels.util.Tuple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

@RestController
@RequestMapping("/")
@Deprecated
public class AltAccoControllerCB {

    @Autowired
    private ByPassExecutor byPassExecutor;

    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    private MetricErrorLogger metricErrorLogger;

    private static final Gson gson = new Gson();

    private static final Logger logger = LoggerFactory.getLogger(AltAccoControllerCB.class);

    @RequestMapping(value = ByPassUrls.SOURCE_ALT_ACCO_BENEFITS, method = RequestMethod.GET)
    public String getAltAccoBenefits(
            @RequestParam(name="cityCode",required = true)String cityCode,
            @RequestParam(name="countryCode",required = true)String countryCode,
            @RequestParam(name = Constants.REGION, required = false, defaultValue = Constants.DEFAULT_SITE_DOMAIN) String region,
            @RequestParam(name="srcClient",required = true)String deviceType,
            @RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
            HttpServletRequest httpRequest, HttpServletResponse httpResponse){

        String response = null;
        try {
            correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
            Tuple<String, Map<String,String>> tup= requestHandler.handleCommonRequest(httpRequest,httpResponse,correlationKey,deviceType,"", httpRequest.getRequestURI(),null);
            correlationKey = tup.getX();
            String destinationUrl = String.format(ByPassUrls.DESTINATION_ALT_ACCO_BENEFITS_API_URL, correlationKey,countryCode,deviceType,cityCode,region);
            response = byPassExecutor.executeGetByPassRequest(tup.getY(), destinationUrl, correlationKey);
            AltAcooDataBenifitsResponse responseEntity = gson.fromJson(response, AltAcooDataBenifitsResponse.class);
            if(responseEntity!=null && responseEntity.getBenifits()!=null  && responseEntity.getResponseErrors()==null){
                return response;
            }else {
                response=ClientBackendUtility.setCBErrorResponse(CBError.NO_DATA_FOUND);
            }


        } catch (Exception e) {
            if (e instanceof ClientGatewayException)
                metricErrorLogger.logClientGatewayException((ClientGatewayException) e, "getByPass");
            else
                metricErrorLogger.logGeneralException(e, "getByPass", DependencyLayer.CLIENTGATEWAY,
                        ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
            response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
        } finally {
            MDC.clear();
        }
        return response;

    }
}
