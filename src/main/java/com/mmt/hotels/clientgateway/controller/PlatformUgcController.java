package com.mmt.hotels.clientgateway.controller;


import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.request.UpvoteRequest;
import com.mmt.hotels.clientgateway.restexecutors.ByPassExecutor;
import com.mmt.hotels.clientgateway.service.ReviewService;
import com.mmt.hotels.clientgateway.thirdparty.request.UgcUpvoteRequest;
import com.mmt.hotels.clientgateway.util.ClientBackendUtility;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.util.Tuple;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.ByPassUrls.DESTINATION_UGC_REVIEWS_UPVOTE_DOWNVOTE_API_URL;
import static com.mmt.hotels.clientgateway.constants.Constants.BRAND;
import static com.mmt.hotels.clientgateway.constants.Constants.BRAND_MMT;
import static com.mmt.hotels.clientgateway.constants.Constants.CLIENT;
import static com.mmt.hotels.clientgateway.constants.Constants.DEFAULT_SITE_DOMAIN;
import static com.mmt.hotels.clientgateway.constants.Constants.DES_CON;
import static com.mmt.hotels.clientgateway.constants.Constants.DOM_HOTEL;
import static com.mmt.hotels.clientgateway.constants.Constants.INTEL_HOTEL;
import static com.mmt.hotels.clientgateway.constants.Constants.VERSION;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.DETAIL_UGC_UPVOTE;
import static com.mmt.hotels.clientgateway.util.HeadersUtil.getHeadersFromServletRequest;

@RestController
@RequestMapping("/")
public class PlatformUgcController {

    @Autowired
    ObjectMapperUtil objectMapperUtil;

    @Autowired
    ReviewService reviewService;

    @Autowired
    private ByPassExecutor byPassExecutor;

    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    private MetricAspect metricAspect;

//    @RequestMapping(value = "cg/ugc-reviews/{client}/{version}", method = RequestMethod.POST)
//    public ResponseEntity<UgcReviewResponseData> getUGCReviews(
//            @Valid @RequestBody UgcReviewRequest ugcReviewRequest, HttpServletRequest httpServletRequest,
//            HttpServletResponse httpServletResponse, @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false,
//            defaultValue = Constants.EMPTY_STRING) String ck,
//            @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
//            @PathVariable(VERSION) String version, @PathVariable(CLIENT) String client,
//            @RequestParam(name = DES_CON, defaultValue = DEFAULT_SITE_DOMAIN) String countryCode)
//            throws ClientGatewayException {
//
//        long startTime = new Date().getTime();
//        client = client.toUpperCase();
//        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
//        Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, Constants.EMPTY_STRING, DETAIL_UGC_REVIEWS);
//        String correlationKey = tup.getX();
//        requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, ugcReviewRequest, correlationKey);
//        String lob = countryCode.equalsIgnoreCase(DEFAULT_SITE_DOMAIN) ? DOM_HOTEL : INTEL_HOTEL;
//        ugcReviewRequest.setLob(lob);
//        ugcReviewRequest.setClient(client);
//        if (StringUtils.isEmpty(ugcReviewRequest.getSortCriteria())) {
//            ugcReviewRequest.setSortCriteria(Constants.LATEST_FIRST);
//        }
//        UgcReviewResponseData ugcReviewResponseDTO = reviewService.platformUgcReview(ugcReviewRequest);
//        String currency = DEFAULT_CUR_INR;
//        if (tup.getY() != null && tup.getY().containsKey(USER_COUNTRY)) {
//            currency = tup.getY().get(USER_CURRENCY);
//        }
//        if (ugcReviewResponseDTO.getResponse() != null) {
//            ugcReviewResponseDTO.getResponse().setCurrency(currency);
//        }
//        metricAspect.addToTime(new Date().getTime() - startTime, DETAIL_UGC_REVIEWS, MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
//        MDC.clear();
//        return new ResponseEntity<>(ugcReviewResponseDTO, HttpStatus.OK);
//    }

    @RequestMapping(value = "cg/ugc-upvote/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<String> UgcUpvote(
            @PathVariable(CLIENT) String client, @PathVariable(VERSION) String version,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
            @RequestBody UpvoteRequest upvoteRequest,@RequestParam(name = DES_CON, defaultValue = DEFAULT_SITE_DOMAIN) String countryCode,
            @RequestParam(name = BRAND, defaultValue = BRAND_MMT) String org,
            HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse){
        String response;
        try {
            client=client.toUpperCase();
            ck = requestHandler.effectiveCorrelationKey(requestId, ck);
            Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, Constants.EMPTY_STRING, DETAIL_UGC_UPVOTE);
            String correlationKey = tup.getX();
            String lob = countryCode.equalsIgnoreCase(DEFAULT_SITE_DOMAIN) ? DOM_HOTEL : INTEL_HOTEL;
            upvoteRequest.setLob(lob);
            upvoteRequest.setOrg(org);
            requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, upvoteRequest, correlationKey);
            Map<String, String> headersFromServletRequest = getHeadersFromServletRequest(httpServletRequest);
            UgcUpvoteRequest request = requestHandler.createUpvoteRequest(upvoteRequest);
            response = byPassExecutor.executeByPassRequestFlyfish(objectMapperUtil.getJsonFromObject(request, DependencyLayer.CLIENTGATEWAY), headersFromServletRequest,DESTINATION_UGC_REVIEWS_UPVOTE_DOWNVOTE_API_URL,Constants.EMPTY_STRING);
        } catch (Exception e) {
            response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
        } finally {
            MDC.clear();
        }
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
}
