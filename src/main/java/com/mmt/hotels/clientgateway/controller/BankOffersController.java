package com.mmt.hotels.clientgateway.controller;

import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.ValidationErrors;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.request.BankOffersRequestCG;
import com.mmt.hotels.clientgateway.response.BankOffersResponseCG;
import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.BankOffersService;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.util.Tuple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.CK_CORRELATION_KEY;
import static com.mmt.hotels.clientgateway.constants.Constants.REQUEST_IDENTIFIER;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.GET_BANK_OFFERS;

@RestController
@RequestMapping("/")
public class BankOffersController {

    private static final Logger logger = LoggerFactory.getLogger(BankOffersController.class);

    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    private Utility utility;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    private BankOffersService bankOffersService;

    @RequestMapping(value = "cg/bankOffers/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<BankOffersResponseCG>> bankOffers(
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody BankOffersRequestCG bankOffersRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
            @RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException {
        long startTime = new Date().getTime();
        if (bankOffersRequest.getDeltaDays() == null) {
            logger.error("INVALID_REQUEST: Delta days is mandatory for bank offers request. Client: {}, CorrelationKey: {}", client, ck);
            throw new ClientGatewayException(DependencyLayer.CLIENTGATEWAY, ErrorType.VALIDATION, ValidationErrors.INVALID_REQUEST.getErrorCode(), "Delta days is mandatory");
        }
        ResponseWrapper<BankOffersResponseCG> bankOffersResponseResponseWrapper = new ResponseWrapper<>();
        client = client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, bankOffersRequest.getRequestDetails().getIdContext(), GET_BANK_OFFERS, bankOffersRequest);
        String correlationKey = tup.getX();
        requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, bankOffersRequest, correlationKey);
        Map<String, String[]> parameterMap = utility.addSrcReqToParameterMap(httpServletRequest.getParameterMap());
        bankOffersResponseResponseWrapper.setResponse(bankOffersService.bankOffers(bankOffersRequest, parameterMap, tup.getY()));
        bankOffersResponseResponseWrapper.setCorrelationKey(correlationKey);
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/bankOffers", MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
        MDC.clear();
        return new ResponseEntity<>(bankOffersResponseResponseWrapper, HttpStatus.OK);
    }

}
