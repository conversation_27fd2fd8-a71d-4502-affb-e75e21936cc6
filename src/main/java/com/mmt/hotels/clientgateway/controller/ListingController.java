package com.mmt.hotels.clientgateway.controller;

import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.context.ListingContext;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.operations.TreelsListingOperation;
import com.mmt.hotels.clientgateway.request.CityOverviewRequest;
import com.mmt.hotels.clientgateway.request.FetchCollectionRequest;
import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.request.GroupBookingRequest;
import com.mmt.hotels.clientgateway.request.ListingMapRequest;
import com.mmt.hotels.clientgateway.request.ListingSearchRequestV2;
import com.mmt.hotels.clientgateway.request.MobLandingRequest;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.request.TravelTipRequest;
import com.mmt.hotels.clientgateway.request.TreelsFilterCountRequest;
import com.mmt.hotels.clientgateway.response.GroupBookingResponse;
import com.mmt.hotels.clientgateway.response.TravelTipResponse;
import com.mmt.hotels.clientgateway.response.UserSessionData;
import com.mmt.hotels.clientgateway.response.filter.Filter;
import com.mmt.hotels.clientgateway.response.filter.FilterResponse;
import com.mmt.hotels.clientgateway.response.listing.ListingResponse;
import com.mmt.hotels.clientgateway.response.listing.UpsellRateplanResponse;
import com.mmt.hotels.clientgateway.response.listingmap.ListingMapResponse;
import com.mmt.hotels.clientgateway.response.moblanding.MatchMakerResponseCG;
import com.mmt.hotels.clientgateway.response.moblanding.MobLandingResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.FetchCollectionResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.FetchCollectionResponseV2;
import com.mmt.hotels.clientgateway.response.searchHotels.SearchHotelsResponse;
import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.request.deeplink.DeepLinkGenerationRequest;
import com.mmt.hotels.clientgateway.response.deeplink.DeepLinkGenerationResponse;
import com.mmt.hotels.clientgateway.service.ListingService;
import com.mmt.hotels.clientgateway.service.TreelsFilterService;

import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.DPTExperimentDetails;
import com.mmt.hotels.util.Tuple;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.mmt.hotels.clientgateway.constants.Constants.CK_CORRELATION_KEY;
import static com.mmt.hotels.clientgateway.constants.Constants.CallToBook;
import static com.mmt.hotels.clientgateway.constants.Constants.EMPTY_STRING;
import static com.mmt.hotels.clientgateway.constants.Constants.FALSE;
import static com.mmt.hotels.clientgateway.constants.Constants.HighValue;
import static com.mmt.hotels.clientgateway.constants.Constants.LISTING_MAP;
import static com.mmt.hotels.clientgateway.constants.Constants.ListAllProp;
import static com.mmt.hotels.clientgateway.constants.Constants.REQUEST_IDENTIFIER;
import static com.mmt.hotels.clientgateway.constants.Constants.TRUE;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.CITY_OVERVIEW;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.FETCH_COLLECTIONS;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.FETCH_UPSELL_RATEPLAN;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.GROUP_BOOKING;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.LISTING_BATCH_FILTER;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.LISTING_DEEPLINK_GENERATION;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.LISTING_FILTER_COUNT;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.LISTING_MOB_LANDING;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.LISTING_SEARCH_HOTELS;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.LISTING_SEARCH_TREELS;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.TRAVEL_INSIGHT;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.TREELS_LISTING_FILTER_COUNT;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.WISHLISTED_HOTELS;

@RestController
@RequestMapping("/")
public class ListingController {
	
	@Autowired
	private RequestHandler requestHandler;
	
	@Autowired
	private ListingService listingService;

	@Autowired
	private TreelsFilterService treelsFilterService;

	@Autowired
	private MetricAspect metricAspect;

	@Autowired
	private TreelsListingOperation treelsListingOperation;

	@Autowired
	private ListingContext listingContext;

	@Autowired
	private Utility utility;



	@Value("${request.callback.count}")
	private int requestCallbackCount;


	Gson gson = new Gson();

	@Value("${use.new.listing.service:true}")
	private boolean useNewListingService;

	private static final Logger logger = LoggerFactory.getLogger(ListingController.class);

	@RequestMapping(value = "cg/search-hotels/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<SearchHotelsResponse>> searchHotels(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody SearchHotelsRequest searchHotelsRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
					throws ClientGatewayException{
		long startTime = new Date().getTime();
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,searchHotelsRequest.getRequestDetails().getIdContext() ,LISTING_SEARCH_HOTELS,searchHotelsRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, searchHotelsRequest, correlationKey);
		ResponseWrapper<SearchHotelsResponse> searchHotelsResponseWrapper = new ResponseWrapper<>();
		Map<String, String[]> parameterMap = utility.addSrcReqToParameterMap(httpServletRequest.getParameterMap());

		searchHotelsResponseWrapper.setResponse(listingService.searchHotels(searchHotelsRequest, parameterMap, tup.getY()));
		searchHotelsResponseWrapper.setCorrelationKey(correlationKey);
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/search_hotels",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

		MDC.clear();
		return new ResponseEntity<>(searchHotelsResponseWrapper, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/search-treels/{client}/{version}" , method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<ListingResponse>> searchTreelsListing(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody ListingSearchRequestV2 searchTreelsRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
			throws ClientGatewayException{
		long startTime = new Date().getTime();
		logger.warn("Search Treels Request Start time: {}", startTime);
		ResponseWrapper<ListingResponse> searchHotelsResponseWrapper = new ResponseWrapper<>();
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,searchTreelsRequest.getRequestDetails().getIdContext() ,LISTING_SEARCH_TREELS,searchTreelsRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, searchTreelsRequest, correlationKey);
		Map<String, String[]> parameterMap = utility.addSrcReqToParameterMap(httpServletRequest.getParameterMap());
		searchHotelsResponseWrapper.setResponse(listingContext.executeStrategy(Constants.TREELS,searchTreelsRequest, parameterMap, tup.getY()));
		searchHotelsResponseWrapper.setCorrelationKey(correlationKey);
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/search_treels",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		return new ResponseEntity<>(searchHotelsResponseWrapper, HttpStatus.OK);
	}

	/**
	 	This Api is used to give City Map Overview response on the basis of which City map is rendered on Listing Page. HTL-39615
	**/
	@RequestMapping(value = "cg/city-overview/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<MatchMakerResponseCG>> fetchCityOverview(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody CityOverviewRequest cityOverviewRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId
	) throws ClientGatewayException {
		long startTime = new Date().getTime();
		logger.debug("City Overview Request Start time: {}", startTime);
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,cityOverviewRequest.getRequestDetails().getIdContext() ,CITY_OVERVIEW,cityOverviewRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, cityOverviewRequest, correlationKey);
		Map<String, String[]> parameterMap = utility.addSrcReqToParameterMap(httpServletRequest.getParameterMap());
		ResponseWrapper<MatchMakerResponseCG> matchMakerResponse = new ResponseWrapper<>();
		matchMakerResponse.setResponse(listingService.fetchCityOverViewResponse(cityOverviewRequest, parameterMap, tup.getY(),correlationKey));
		matchMakerResponse.setCorrelationKey(correlationKey);
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/city_overview",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		logger.debug("City Overview Request End time: {}",new Date().getTime() - startTime);
		return new ResponseEntity<>(matchMakerResponse, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/fetch-upsell-rateplan/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<UpsellRateplanResponse>> fetchUpsellRateplan(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody SearchHotelsRequest fetchUpsellRateplanRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId
	) throws ClientGatewayException {
		long startTime = new Date().getTime();
		logger.debug("Fetch Upsell Rateplan Request Start time: {}", startTime);
		client = client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, fetchUpsellRateplanRequest.getRequestDetails().getIdContext(), FETCH_UPSELL_RATEPLAN, fetchUpsellRateplanRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, fetchUpsellRateplanRequest, correlationKey);
		Map<String, String[]> parameterMap = utility.addSrcReqToParameterMap(httpServletRequest.getParameterMap());
		ResponseWrapper<UpsellRateplanResponse> upsellRateplanResponseResponseWrapper = new ResponseWrapper<>();
		upsellRateplanResponseResponseWrapper.setResponse(listingService.fetchUpsellRateplanResponse(fetchUpsellRateplanRequest, parameterMap, tup.getY(), correlationKey));
		upsellRateplanResponseResponseWrapper.setCorrelationKey(correlationKey);
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/fetch-upsell-rateplan", MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		logger.debug("Fetch upsell rateplan request End time: {}", new Date().getTime() - startTime);
		return new ResponseEntity<>(upsellRateplanResponseResponseWrapper, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/fetchCollections/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<FetchCollectionResponse>> fetchCollection(@Valid @RequestBody FetchCollectionRequest fetchCollectionRequest,
																					@PathVariable("client") String client,
																					@PathVariable("version") String version,
																					HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
																					@RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId) throws ClientGatewayException {
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, fetchCollectionRequest.getRequestDetails().getIdContext(), FETCH_COLLECTIONS, fetchCollectionRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, fetchCollectionRequest, correlationKey);
		ResponseWrapper<FetchCollectionResponse>  fetchCollectionResponse=new ResponseWrapper<>();
		fetchCollectionResponse.setResponse(listingService.fetchCollections(fetchCollectionRequest, httpServletRequest.getParameterMap(),tup.getY()));
		fetchCollectionResponse.setCorrelationKey(correlationKey);
		MDC.clear();
		return new ResponseEntity<>(fetchCollectionResponse, HttpStatus.OK);
	}


	@RequestMapping(value = "/cg/v2.0/fetchCollections/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<FetchCollectionResponseV2>> fetchCollectionV2(@Valid @RequestBody FetchCollectionRequest fetchCollectionRequest,
																					@PathVariable("client") String client,
																					@PathVariable("version") String version,
																					HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
																					@RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId) throws ClientGatewayException {
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, fetchCollectionRequest.getRequestDetails().getIdContext(), FETCH_COLLECTIONS, fetchCollectionRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, fetchCollectionRequest, correlationKey);
		ResponseWrapper<FetchCollectionResponseV2>  fetchCollectionResponse = new ResponseWrapper<>();
		fetchCollectionResponse.setResponse(listingService.fetchCollectionsV2(fetchCollectionRequest, httpServletRequest.getParameterMap(),tup.getY()));
		fetchCollectionResponse.setCorrelationKey(correlationKey);
		MDC.clear();
		return new ResponseEntity<>(fetchCollectionResponse, HttpStatus.OK);
	}


	@RequestMapping(value = "cg/mob-landing/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<MobLandingResponse>> mobLanding(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody MobLandingRequest mobLandingRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
	throws ClientGatewayException{
		long startTime = new Date().getTime();
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client, mobLandingRequest.getRequestDetails().getIdContext()  , LISTING_MOB_LANDING,mobLandingRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, mobLandingRequest, correlationKey);
		ResponseWrapper<MobLandingResponse> mobLandingResponseWrapper = new ResponseWrapper<>();
		mobLandingResponseWrapper.setResponse(listingService.mobLanding(mobLandingRequest, httpServletRequest.getParameterMap(), tup.getY()));
		mobLandingResponseWrapper.setCorrelationKey(correlationKey);
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/mob_landing",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

		MDC.clear();
		return new ResponseEntity<>(mobLandingResponseWrapper, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/filter-count/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<FilterResponse>> filterCount(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody FilterCountRequest filterCountRequest, @RequestParam(name = "seoCorp", required = false) boolean seoCorp,
			HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
			throws ClientGatewayException{
		long startTime = new Date().getTime();
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,
				 filterCountRequest.getRequestDetails().getIdContext()  , LISTING_FILTER_COUNT,filterCountRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, filterCountRequest, correlationKey);
		ResponseWrapper<FilterResponse> filterCountResponse = new ResponseWrapper<>();
		List<DPTExperimentDetails> dptExperimentDetailsList = new ArrayList<>();
		filterCountResponse.setResponse(listingService.filterCount(filterCountRequest, httpServletRequest.getParameterMap(), tup.getY(), seoCorp, dptExperimentDetailsList));
		filterCountResponse.setCorrelationKey(correlationKey);
		listingService.filterCountLogging(filterCountRequest,filterCountResponse.getResponse(),tup.getY(), seoCorp, dptExperimentDetailsList);
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/filter_count",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		return new ResponseEntity<>(filterCountResponse, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/treels-filter-count/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<FilterResponse>> treelsFilterCount(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody TreelsFilterCountRequest treelsFilterCountRequest, @RequestParam(name = "seoCorp", required = false) boolean seoCorp,
			HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
			throws ClientGatewayException{
		long startTime = new Date().getTime();
		logger.warn("Treels Filter Count Request Start time: {}", startTime);
		ResponseWrapper<FilterResponse> filterCountResponse = new ResponseWrapper<>();
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,
				treelsFilterCountRequest.getRequestDetails().getIdContext()  , TREELS_LISTING_FILTER_COUNT,treelsFilterCountRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, treelsFilterCountRequest, correlationKey);
		filterCountResponse.setResponse(treelsFilterService.filterCount(treelsFilterCountRequest, httpServletRequest.getParameterMap(), tup.getY()));
		filterCountResponse.setCorrelationKey(correlationKey);
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/treels_filter_count",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		logger.warn("Treels Filter Count Request End time: {}", new Date().getTime() - startTime);
		return new ResponseEntity<>(filterCountResponse, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/batch-filters/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<FilterResponse>> batchFilter(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody FilterCountRequest batchFilterRequest, @RequestParam(name = "seoCorp", required = false) boolean seoCorp,
			HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
			throws ClientGatewayException{
		long startTime = new Date().getTime();
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,
				 batchFilterRequest.getRequestDetails().getIdContext()  , LISTING_BATCH_FILTER,batchFilterRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, batchFilterRequest, correlationKey);
		ResponseWrapper<FilterResponse> filterCountResponse = new ResponseWrapper<>();
		filterCountResponse.setResponse(listingService.batchFilterResponse(batchFilterRequest, tup.getY(), seoCorp, httpServletRequest.getParameterMap()));
		filterCountResponse.setCorrelationKey(correlationKey);
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/batch_filters",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		return new ResponseEntity<>(filterCountResponse, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/listing-map/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<ListingMapResponse>> listingMap(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody ListingMapRequest listingMapRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
			throws ClientGatewayException {

		ResponseWrapper<ListingMapResponse> listingMapResponse = requestHandler.validateListingMapRequest(listingMapRequest);

		if(listingMapRequest.getRequestDetails()!=null && listingMapRequest.getSearchCriteria()!=null && listingMapRequest.getSearchCriteria().getCollectionCriteria()!=null) {
			if(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(listingMapRequest.getRequestDetails().getIdContext())) {
				listingMapRequest.getSearchCriteria().getCollectionCriteria().setAthenaCategory("myBiz");
			} else if(StringUtils.isEmpty(listingMapRequest.getSearchCriteria().getCollectionCriteria().getAthenaCategory())){
				listingMapRequest.getSearchCriteria().getCollectionCriteria().setAthenaCategory("All");
			}
		}
		if(listingMapResponse != null) {
			return new ResponseEntity<>(listingMapResponse , HttpStatus.BAD_REQUEST);
		}
		long startTime = new Date().getTime();
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,
				 listingMapRequest.getRequestDetails().getIdContext() ,LISTING_MAP,listingMapRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, listingMapRequest, correlationKey);
		listingMapResponse = new ResponseWrapper<>();
		listingMapResponse.setResponse(listingService.listingMap(listingMapRequest, httpServletRequest.getParameterMap(), tup.getY()));
		listingMapResponse.setCorrelationKey(correlationKey);
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/listing_map",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		return new ResponseEntity<>(listingMapResponse, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/groupBooking/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<GroupBookingResponse>> groupBooking(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody GroupBookingRequest groupBookingRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
			throws ClientGatewayException{
		long startTime = new Date().getTime();
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,
				Constants.B2C ,GROUP_BOOKING,groupBookingRequest);
		String correlationKey = tup.getX();
		groupBookingRequest.setCorrelationKey(correlationKey);
		// hit user-service here to get the personal details of user, in case of loggedOut users we are making dummy user-service response
		UserSessionData userSessionData = new UserSessionData();
		String callBackType = Optional.ofNullable(groupBookingRequest)
				.map(GroupBookingRequest::getRequestDetails)
				.map(RequestDetails::getCallBackType)
				.orElse("");
		boolean isCallToBookReq = Optional.ofNullable(groupBookingRequest)
				.map(GroupBookingRequest::getRequestDetails)
				.map(RequestDetails::isRequestCallBack)
				.orElse(false);

		boolean isListAllPropCall = ListAllProp.equalsIgnoreCase(callBackType);
		boolean isCallBackReq = StringUtils.isNotEmpty(callBackType) ? CallToBook.equalsIgnoreCase(callBackType) : isCallToBookReq;
		boolean isHighValueCall = HighValue.equalsIgnoreCase(callBackType);

		if(isListAllPropCall || isCallBackReq || isHighValueCall) {
			userSessionData = utility.addUserServiceDataToRequest(groupBookingRequest, tup.getY(), correlationKey, isCallBackReq);
		}
		ResponseWrapper<GroupBookingResponse> groupBookingResponse = new ResponseWrapper<>();
		if(groupBookingRequest!=null && groupBookingRequest.getRequestDetails()!=null) {
			groupBookingRequest.getRequestDetails().setPushDataToCallToBookQ(isCallBackReq ? TRUE : FALSE);
			groupBookingRequest.getRequestDetails().setPushDataToListAllPropQ(isListAllPropCall ? TRUE : FALSE);
		}
		int requestCallBackCount = userSessionData!=null && userSessionData.getRequestCallBackCount()!=null ? userSessionData.getRequestCallBackCount() : 0;
		if((utility.isCallToBookRequest(groupBookingRequest, userSessionData) || isCallBackReq)
				&& userSessionData.getRequestCallBackCount()<requestCallbackCount) {
			groupBookingResponse.setResponse(listingService.submitGroupBooking(groupBookingRequest, requestCallBackCount));
		} else if((utility.isCallToBookRequest(groupBookingRequest, userSessionData) || isCallBackReq)
				&& userSessionData.getRequestCallBackCount()>=requestCallbackCount) {
			String device = (groupBookingRequest!=null && groupBookingRequest.getDeviceDetails()!=null && StringUtils.isNotEmpty(groupBookingRequest.getDeviceDetails().getBookingDevice()))?groupBookingRequest.getDeviceDetails().getBookingDevice(): EMPTY_STRING;
			groupBookingResponse.setResponse(listingService.buildGroupBookingResponse(requestCallBackCount, device));
		} else if((groupBookingRequest!=null && groupBookingRequest.getRequestDetails()!=null && !groupBookingRequest.getRequestDetails().isRequestCallBack()) || isHighValueCall || isListAllPropCall) {
			groupBookingResponse.setResponse(listingService.submitGroupBooking(groupBookingRequest, requestCallBackCount));
		}
		groupBookingResponse.setCorrelationKey(correlationKey);
		metricAspect.addToTime(new Date().getTime() - startTime, GROUP_BOOKING,MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		return new ResponseEntity<>(groupBookingResponse, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/wishListed-hotels/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<SearchHotelsResponse>> wishListedHotels(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody SearchHotelsRequest searchHotelsRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
			throws ClientGatewayException{
		long startTime = new Date().getTime();
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,searchHotelsRequest.getRequestDetails().getIdContext(), WISHLISTED_HOTELS, searchHotelsRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, searchHotelsRequest, correlationKey);
		ResponseWrapper<SearchHotelsResponse> searchHotelsResponseWrapper = new ResponseWrapper<>();
		searchHotelsRequest.getSearchCriteria().setWishListedSearch(true);
		searchHotelsResponseWrapper.setResponse(listingService.searchHotels(searchHotelsRequest, httpServletRequest.getParameterMap(), tup.getY()));
		searchHotelsResponseWrapper.setCorrelationKey(correlationKey);
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/wishListed-hotels",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		return new ResponseEntity<>(searchHotelsResponseWrapper, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/listing/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<SearchHotelsResponse>> listing(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody SearchHotelsRequest searchHotelsRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
			throws ClientGatewayException{
		long startTime = System.currentTimeMillis();
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,searchHotelsRequest.getRequestDetails().getIdContext() ,LISTING_SEARCH_HOTELS,searchHotelsRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, searchHotelsRequest, correlationKey);
		ResponseWrapper<SearchHotelsResponse> searchHotelsResponseWrapper = new ResponseWrapper<>();
		searchHotelsResponseWrapper.setResponse(listingService.executeListing(httpServletRequest,client,searchHotelsRequest,correlationKey,tup));
		searchHotelsResponseWrapper.setCorrelationKey(correlationKey);
		long timeTaken = System.currentTimeMillis() - startTime;
		logger.warn("Time taken by cg/listing endPoint {} millis", timeTaken);
		metricAspect.addToTime(timeTaken, "cg/listing",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		return new ResponseEntity<>(searchHotelsResponseWrapper, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/travel-insight/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<TravelTipResponse>> travelInsight(
			@PathVariable("client") String client,
			@PathVariable("version") String version,
			@Valid @RequestBody TravelTipRequest travelTipRequest,
			HttpServletRequest httpServletRequest,
			HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck
	) throws ClientGatewayException {
		long startTime = new Date().getTime();
		logger.debug("Travel Insight Request Start time: {}", startTime);
		client = client.toUpperCase();
		Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, travelTipRequest.getRequestDetails().getIdContext(),TRAVEL_INSIGHT, travelTipRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, travelTipRequest, correlationKey);
		Map<String, String[]> parameterMap = utility.addSrcReqToParameterMap(httpServletRequest.getParameterMap());
		ResponseWrapper<TravelTipResponse> travelTipResponseWrapper = new ResponseWrapper<>();
		travelTipResponseWrapper.setResponse(listingService.travelTip(travelTipRequest, parameterMap, tup.getY()));
		travelTipResponseWrapper.setCorrelationKey(correlationKey);
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/travel-insight", MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		logger.debug("Travel Insight Request End time: {}", new Date().getTime() - startTime);
		return new ResponseEntity<>(travelTipResponseWrapper, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/smart-filters/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<List<Filter>>> smartFilter(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody FilterCountRequest filterCountRequest, @RequestParam(name = "seoCorp", required = false) boolean seoCorp,
			HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
			throws ClientGatewayException{
		long startTime = new Date().getTime();
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client, filterCountRequest.getRequestDetails().getIdContext()  , LISTING_FILTER_COUNT,filterCountRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, filterCountRequest, correlationKey);
		ResponseWrapper<List<Filter>> filtersResponse = new ResponseWrapper<>();
		List<DPTExperimentDetails> dptExperimentDetailsList = new ArrayList<>();
		List<Filter> response = listingService.smartFilters(filterCountRequest, httpServletRequest.getParameterMap(), tup.getY(), seoCorp, dptExperimentDetailsList);

		if (CollectionUtils.isNotEmpty(response)) {
			filtersResponse.setResponse(response);
		} else {
			filtersResponse.setError(new Error("200", "No Filters Found"));
		}

		metricAspect.addToTime(new Date().getTime() - startTime, "cg/smart-filters",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		return new ResponseEntity<>(filtersResponse, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/gen-redirect-url/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<DeepLinkGenerationResponse>> generateDeepLink(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@RequestBody DeepLinkGenerationRequest deepLinkRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
					throws ClientGatewayException{
		long startTime = new Date().getTime();
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		
		String idContext = deepLinkRequest.getRequestDetails() != null ?
			deepLinkRequest.getRequestDetails().getIdContext() : "";
		
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,idContext ,LISTING_DEEPLINK_GENERATION,deepLinkRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, deepLinkRequest, correlationKey);
		ResponseWrapper<DeepLinkGenerationResponse> deepLinkResponseWrapper = new ResponseWrapper<>();

		deepLinkResponseWrapper.setResponse(listingService.generateDeepLink(deepLinkRequest));
		deepLinkResponseWrapper.setCorrelationKey(correlationKey);
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/gen_redirect_url",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

		MDC.clear();
		return new ResponseEntity<>(deepLinkResponseWrapper, HttpStatus.OK);
	}


}