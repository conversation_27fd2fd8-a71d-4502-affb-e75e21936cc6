package com.mmt.hotels.clientgateway.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.constants.ByPassUrls;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.UnexpectedErrors;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.SummaryCategoryRequest;
import com.mmt.hotels.clientgateway.response.cbresponse.CBGenericResponse;
import com.mmt.hotels.clientgateway.restexecutors.ByPassExecutor;
import com.mmt.hotels.clientgateway.util.ClientBackendUtility;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.model.request.flyfish.FlyFishReviewsRequest;
import com.mmt.hotels.model.response.flyfish.FlyFishReviewsResponse;
import com.mmt.hotels.util.Tuple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.ROOM_CODE;

@RestController
@RequestMapping("/")
@Deprecated
public class FlyfishControllerCB {

    @Autowired
    private ByPassExecutor byPassExecutor;

    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    ObjectMapperUtil objectMapperUtil;

    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
    private CommonHelper commonHelper;

    @Autowired
    private MetricAspect metricAspect;

    private static final Gson gson = new Gson();

    @Value("${chat.gpt.review.summary.exp.map}")
    private String chatGptReviewSummaryExp;

    @Value("${consul.enable}")
    private boolean consulFlag;

    Map<String, List<String>> chatGptReviewSummaryExpMap;

    @Autowired
    private CommonConfigConsul commonConfigConsul;

    private static final Logger logger = LoggerFactory.getLogger(FlyfishControllerCB.class);

    @PostConstruct
    void init() {
        try {
            if(consulFlag){
                chatGptReviewSummaryExpMap = commonConfigConsul.getChatGptReviewSummaryExp();
            } else{
                chatGptReviewSummaryExpMap = objectMapperUtil.getObjectFromJsonWithType(chatGptReviewSummaryExp, new TypeReference<Map<String, List<String>>>() {},DependencyLayer.CLIENTGATEWAY);
            }
        }catch (Exception e) {
            logger.error("Error in Getting Properties File: ", e);
        }
    }

    @RequestMapping(value = ByPassUrls.SOURCE_GET_FLYFISH_CATEGORY_LIST, method = RequestMethod.POST)
    @ResponseBody
    public String getFlyFishCategoryList(@Valid @RequestBody SummaryCategoryRequest summaryCategoryRequest,
                                         @PathVariable String hotelId,
                                         @RequestParam(name = Constants.REGION, required = false) String siteDomain,
                                         @RequestParam(name = Constants.CURRENCY, required = false) String currency,
                                         @RequestParam(name = Constants.LANGUAGE, required = false) String language,
                                         @RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String corelationKey,
                                         @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
                                         @RequestParam(name = Constants.CONTEXT_TYPE, required = false, defaultValue = Constants.ALL) String contextType, String correlationKey, HttpServletRequest httpRequest,
                                         HttpServletResponse httpResponse){

        String response = null;
        long startTime = new Date().getTime();
        try {
            correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
            Tuple<String, Map<String,String>> tup= requestHandler.handleCommonRequest(httpRequest,httpResponse,correlationKey,null,"","/entity/api/hotels/hotelId/summary/category",null);
            correlationKey= tup.getX();

            response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);//byPassExecutor.executeByPassRequestFlyfish(objectMapperUtil.getJsonFromObject(flyFishSummaryRequest, DependencyLayer.CLIENTGATEWAY), tup.getY(), destinationUrl, correlationKey);

        } catch (Exception e) {
            if (e instanceof ClientGatewayException)
                metricErrorLogger.logClientGatewayException((ClientGatewayException) e, "getFlyFishCategoryList");
            else
                metricErrorLogger.logGeneralException(e, "postByPass", DependencyLayer.CLIENTGATEWAY,
                        ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
            response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
        } finally {
            metricAspect.addToTime(new Date().getTime() - startTime, ByPassUrls.SOURCE_GET_FLYFISH_CATEGORY_LIST,MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), "", MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

            MDC.clear();
        }

        return response;
    }



    @RequestMapping(value = ByPassUrls.SOURCE_GET_FLYFISH_REVIEWS, method = RequestMethod.POST)
    @ResponseBody
    public CBGenericResponse<FlyFishReviewsResponse> getFlyFishReviews(@RequestBody FlyFishReviewsRequest flyFishReviewsRequest, HttpServletRequest httpRequest,
                                                                       @PathVariable String hotelId, @RequestParam String srcClient,
                                                                       @RequestParam(name = Constants.REGION, required = false) String siteDomain,
                                                                       @RequestParam(name = Constants.CURRENCY, required = false) String currency,
                                                                       @RequestParam(name = Constants.LANGUAGE, required = false) String language,
                                                                       @RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
                                                                       @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
                                                                       @RequestParam(name = Constants.STYLE, required = false, defaultValue = Constants.EMPTY_STRING) String style,
                                                                       @RequestParam(name = Constants.CONTEXT_TYPE, required = false, defaultValue = Constants.ALL) String contextType,
                                                                       @RequestParam(name = ROOM_CODE, required = false) String roomCode,
                                                                       HttpServletResponse httpResponse) {

        CBGenericResponse<FlyFishReviewsResponse> response = new CBGenericResponse<>();
        long startTime = new Date().getTime();
        try {
            correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
            Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpRequest, httpResponse, correlationKey, null, "", "/entity/api/hotel/hotelId/flyfishReviews", null);
            correlationKey = tup.getX();
            ClientBackendUtility.setCBErrorResponse(response, CBError.GENERIC_ERROR);
        } catch (Exception e) {
            if (e instanceof ClientGatewayException)
                metricErrorLogger.logClientGatewayException((ClientGatewayException) e, "getFlyFishCategoryList");
            else
                metricErrorLogger.logGeneralException(e, "postByPass", DependencyLayer.CLIENTGATEWAY,
                        ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
            ClientBackendUtility.setCBErrorResponse(response, CBError.GENERIC_ERROR);
        } finally {
            metricAspect.addToTime(new Date().getTime() - startTime, ByPassUrls.SOURCE_GET_FLYFISH_REVIEWS, MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), "", MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
            MDC.clear();
        }

        return response;
    }

//    private String addRoomCodeInQueryParam(String url, String roomCode) {
//        if (StringUtils.isBlank(url) || StringUtils.isBlank(roomCode))
//            return url;
//        if (StringUtils.contains(roomCode, COMMA)) {
//            List<String> roomCodeList = Arrays.asList(roomCode.split(COMMA));
//            roomCode = roomCodeList.get(0);
//        }
//        url += Constants.AMP + ROOM_CODE + EQUI + roomCode;
//        return url;
//    }

//    private String updateDestinationUrl(String destinationUrl, String style) {
//        StringBuilder sb = new StringBuilder(destinationUrl);
//
//        if (StringUtils.isNotEmpty(style)) {
//            sb.append("&");
//            sb.append(Constants.STYLE);
//            sb.append("=");
//            sb.append(style);
//        }
//
//        return sb.toString();
//    }

//    private FlyfishSummaryRequest modifyFlyFishSummaryCategoryRequest(SummaryCategoryRequest request){
//        FlyfishSummaryRequest flyFishSummaryRequest = new FlyfishSummaryRequest();
//        FlyfishFilterCriteria filterCriteriaDTO = new FlyfishFilterCriteria();
//        filterCriteriaDTO.setCategoryList(request.getFilter().getCategoryList());
//        filterCriteriaDTO.setOtas(request.getFilter().getOtas());
//        flyFishSummaryRequest.setFilter(filterCriteriaDTO);
//        if(StringUtils.isNotEmpty(request.getFilter().getChatGptSummary()) && MapUtils.isNotEmpty(chatGptReviewSummaryExpMap)){
//            flyFishSummaryRequest.setReviewSummaryExp(chatGptReviewSummaryExpMap.getOrDefault(request.getFilter().getChatGptSummary(), null));
//        }
//        return flyFishSummaryRequest;
//    }

}
