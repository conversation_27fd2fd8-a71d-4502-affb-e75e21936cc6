package com.mmt.hotels.clientgateway.controller;

import java.util.Date;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.mmt.hotels.clientgateway.util.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.service.DetailService;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.util.Tuple;

@RestController
@RequestMapping("/")
public class DetailControllerCB {

	private static final Logger LOGGER = LoggerFactory.getLogger(DetailControllerCB.class);
	
    @Autowired
    private RequestHandler requestHandler;

	@Autowired
	private DetailService detailService;

	@Autowired
    private MetricAspect metricAspect;

    @RequestMapping(value = "entity/api/mob/staticDetail", method = RequestMethod.POST)
    @ResponseBody
    public String getStaticDetailsResponse(@RequestBody HotelDetailsMobRequestBody request,
                                             @RequestParam(name = Constants.REGION, required = false) String siteDomain,
                                             @RequestParam(name = Constants.CURRENCY, required = false) String currency,
                                             @RequestParam(name = Constants.LANGUAGE, required = false) String language,
                                             @RequestParam(name = Constants.CORRELATIONKEY, required = false) String correlationKey,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
                                             HttpServletRequest httpRequest, HttpServletResponse httpServletResponse) {
    	
        String  response = null;
        long startTime = new Date().getTime();
        try {
            correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
            Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpRequest, httpServletResponse, correlationKey, request.getBookingDevice(), request.getIdContext(), "entity/api/mob/staticDetail",null);
            correlationKey = tup.getX();
            if (StringUtils.isNotBlank(siteDomain)) {
                request.setSiteDomain(siteDomain.toUpperCase());
            }
            request.setCorrelationKey(correlationKey);
            response = detailService.getStaticDetailsResponse(request, httpRequest.getParameterMap(), tup.getY());
        } catch (Exception ex) {
        	response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
            LOGGER.error("Error Occur in getting static details response", ex);
        } finally {
            metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/mob/staticDetail",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), request.getBookingDevice(), MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
            MDC.clear();
        }
        return response;
    }

}
