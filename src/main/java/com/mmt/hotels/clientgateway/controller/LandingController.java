package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.response.HotelPermissions;
import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.LandingService;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.util.Tuple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.Map;

import static com.mmt.hotels.clientgateway.enums.ConnectivityErrors.REST_ERROR;

@RestController
@RequestMapping("/")
public class LandingController {

    @Autowired
    private LandingService landingService;

    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    private MetricAspect metricAspect;

    private static final Logger LOGGER = LoggerFactory.getLogger(LandingController.class);

    @RequestMapping(value = "cg/permissions/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<HotelPermissions>> getPermissionsInfo(
            @PathVariable("client") String client,
            @PathVariable("version") String version,
            @RequestBody SearchWrapperInputRequest searchWrapperInputRequest,
            @RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
            @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
            @RequestParam(name = "srcClient", required = false) String srcClient,
            HttpServletRequest httpRequest,
            HttpServletResponse httpResponse) {

        ResponseWrapper<HotelPermissions> response = new ResponseWrapper<>();
        long startTime = System.currentTimeMillis();

        try {
            // Setup correlation key and headers
            correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
            Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(
                    httpRequest, httpResponse, correlationKey,
                    searchWrapperInputRequest.getBookingDevice(),
                    searchWrapperInputRequest.getIdContext(),
                    httpRequest.getRequestURI(), null);

            correlationKey = tup.getX();
            Map<String, String> httpHeaderMap = tup.getY();
            httpHeaderMap.put("srcClient", srcClient);
            searchWrapperInputRequest.setCorrelationKey(correlationKey);

            // Core logic
            HotelPermissions permissions = landingService.getFeatureBasedAccessDetails(searchWrapperInputRequest,
                    httpHeaderMap);
            response.setResponse(permissions);

        } catch (ClientGatewayException e) {
            LOGGER.error("ClientGatewayException in fetching permissions: {}", e.getMessage());
            response.setError(new Error(e.getCode(), e.getMessage()));

        } catch (Exception e) {
            LOGGER.error("Unexpected exception while fetching permissions", e);
            response.setError(new Error(
                    REST_ERROR.getErrorCode(),
                    REST_ERROR.getErrorMsg() + " " + e.getMessage() + " " + srcClient));
        } finally {
            metricAspect.addToTime(
                    System.currentTimeMillis() - startTime,
                    "entity/api/hotels/permission",
                    MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()),
                    MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()),
                    srcClient,
                    MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
            MDC.clear();
        }

        return ResponseEntity.ok(response);
    }

}
