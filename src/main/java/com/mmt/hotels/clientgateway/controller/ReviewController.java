package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ControllerConstants;
import com.mmt.hotels.clientgateway.enums.AuthenticationErrors;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.AuthenticationException;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.payLater.PayLaterEligibilityRequest;
import com.mmt.hotels.clientgateway.request.ugc.ClientLoadProgramRequest;
import com.mmt.hotels.clientgateway.request.ugc.ClientSubmitApiRequest;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.request.ugc.QuestionSet;
import com.mmt.hotels.clientgateway.response.AvailRoomsResponse;
import com.mmt.hotels.clientgateway.response.FetchLocationsResponse;
import com.mmt.hotels.clientgateway.response.PoliciesResponse;
import com.mmt.hotels.clientgateway.response.RoomInfoResponse;
import com.mmt.hotels.clientgateway.response.SearchAddonsResponse;
import com.mmt.hotels.clientgateway.response.TotalPriceResponse;
import com.mmt.hotels.clientgateway.response.ValidateCouponResponseBody;
import com.mmt.hotels.clientgateway.response.payLater.PayLaterEligibilityResponse;
import com.mmt.hotels.clientgateway.response.ugc.ClientUgcResponse;
import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.restexecutors.UserServiceExecutor;
import com.mmt.hotels.clientgateway.service.AddonsService;
import com.mmt.hotels.clientgateway.service.DiscountService;
import com.mmt.hotels.clientgateway.service.PolicyService;
import com.mmt.hotels.clientgateway.service.ReviewService;
import com.mmt.hotels.clientgateway.service.RoomInfoService;
import com.mmt.hotels.clientgateway.service.UgcService;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.giftcard.GiftCardClaimErrorResponse;
import com.mmt.hotels.model.giftcard.GiftCardClaimResponse;
import com.mmt.hotels.util.Tuple;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.CG_UGCQUESTIONS;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.CG_UGCSUBMIT;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.FETCH_LOCATIONS;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.PAY_LATER_ELIGIBILITY;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.REVIEW_AVAIL_ROOMS;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.REVIEW_GET_POLICIES;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.REVIEW_ROOM_INFO;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.REVIEW_ROOM_INFOS;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.REVIEW_SEARCH_ADD_ONS;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.REVIEW_TOTAL_PRICING;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.REVIEW_VAILDATE_COUPON;

@RestController
@RequestMapping("/")
public class ReviewController {

    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    private ReviewService reviewService;

    @Autowired
    private UgcService ugcService;

    @Autowired
    private AddonsService addonsService;

    @Autowired
    private DiscountService discountService;

    @Autowired
    private PolicyService policyService;

    @Autowired
    private RoomInfoService roomInfoService;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    private Utility utility;

    @Autowired
    private UserServiceExecutor userServiceExecutor;

    @Autowired
    private CommonHelper commonHelper;

    @RequestMapping(value = "cg/avail-rooms/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<AvailRoomsResponse>> availRooms(
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody AvailRoomsRequest availRoomsRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
            @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException {
        ResponseEntity<ResponseWrapper<AvailRoomsResponse>> response = null;
        ResponseWrapper<AvailRoomsResponse> availRoomsRoomsResponseWrapper = new ResponseWrapper<>();
        long startTime = new Date().getTime();
        client = client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, availRoomsRequest.getRequestDetails().getIdContext(), REVIEW_AVAIL_ROOMS, availRoomsRequest);
        String correlationKey = tup.getX();
        requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, availRoomsRequest, correlationKey);
        requestHandler.validateB2bHeadersAndCreateMDC(httpServletRequest, availRoomsRequest);
        try {
            availRoomsRoomsResponseWrapper.setResponse(reviewService.availRooms(availRoomsRequest, httpServletRequest.getParameterMap(), tup.getY()));
            availRoomsRoomsResponseWrapper.setCorrelationKey(correlationKey);
            response = new ResponseEntity<>(availRoomsRoomsResponseWrapper, HttpStatus.OK);
        } catch (AuthenticationException authenticationException) {
            if (FUNNEL_SOURCE_MYPARTNER.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.FUNNEL_SOURCE.getStringValue()))) {
                String errorCode = DependencyLayer.USERSERVICE.getCode() + ErrorType.CONNECTIVITY.getCode() + AuthenticationErrors.CONNECTION_FAILURE.getErrorCode();
                Error error = new Error(errorCode, AuthenticationErrors.CONNECTION_FAILURE.getErrorMsg());
                availRoomsRoomsResponseWrapper.setError(error);
                availRoomsRoomsResponseWrapper.setCorrelationKey(correlationKey);
                response = new ResponseEntity<>(availRoomsRoomsResponseWrapper, HttpStatus.OK);
            } else {
                throw authenticationException;
            }
        } finally {
            metricAspect.addToTime(new Date().getTime() - startTime, "cg/avail_rooms", MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
            MDC.clear();
        }

        return response;
    }

    @RequestMapping(value = "cg/avail-rooms/v2.0/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<AvailRoomsResponse>> availRoomsV2(
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody AvailRoomsRequest availRoomsRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException{
        long startTime = new Date().getTime();
        client = client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, availRoomsRequest.getRequestDetails().getIdContext(), REVIEW_AVAIL_ROOMS, availRoomsRequest);
        String correlationKey = tup.getX();
        requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, availRoomsRequest, correlationKey);
        ResponseWrapper<AvailRoomsResponse> availRoomsRoomsResponseWrapper = new ResponseWrapper<>();
        if (MapUtils.isNotEmpty(tup.getY())) {
            tup.getY().put(Constants.REVIEW_PRICE_REQUEST, "true");
        }
        AvailRoomsResponse availRoomsResponse = reviewService.availRooms(availRoomsRequest, httpServletRequest.getParameterMap(), tup.getY());
        // We are explicitly suppressing the nodes from the avail-rooms for the GCC HPA case
        suppressNodesForAvailRoomsV2(availRoomsResponse);
        availRoomsRoomsResponseWrapper.setResponse(availRoomsResponse);
        availRoomsRoomsResponseWrapper.setCorrelationKey(correlationKey);
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/availRoomsV2", MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
        return new ResponseEntity<>(availRoomsRoomsResponseWrapper, HttpStatus.OK);
    }

    @RequestMapping(value = "cg/giftCard/claim/v1/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<GiftCardsData>> claimGiftCard(
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody ClaimGiftCardRequest claimGiftCardRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
            @RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException{
        long startTime = new Date().getTime();
        client = client.toUpperCase();
        Map<String, String> httpHeaderMap = HeadersUtil.getHeadersFromServletRequest(httpServletRequest);
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        String correlationKey = UUID.randomUUID().toString();
        String region = httpHeaderMap.containsKey(Constants.REGION) && StringUtils.isNotBlank(httpHeaderMap.get(Constants.REGION)) ? httpHeaderMap.get(Constants.REGION) : Constants.DEFAULT_SITE_DOMAIN;
        String language = httpHeaderMap.containsKey(Constants.LANGUAGE)  && StringUtils.isNotBlank(httpHeaderMap.get(Constants.LANGUAGE)) ? httpHeaderMap.get(Constants.LANGUAGE) : Constants.DEFAULT_LANGUAGE;
        String currency = httpHeaderMap.containsKey(Constants.CURRENCY)  && StringUtils.isNotBlank(httpHeaderMap.get(Constants.CURRENCY)) ? httpHeaderMap.get(Constants.CURRENCY) : Constants.DEFAULT_CUR_INR;
        String funnelSource = null;
        String controller = ControllerConstants.GIFT_CARD_CLAIM;

        String mmtAuth = commonHelper.getMMTAuth(httpHeaderMap, client);
        httpHeaderMap.put("mmt-auth",mmtAuth);
        UserServiceResponse userServiceResponse = userServiceExecutor.getUserServiceResponse(mmtAuth, null, null, null, correlationKey, "IN", null, null, httpHeaderMap);
        if(userServiceResponse == null || userServiceResponse.getResult() == null || userServiceResponse.getResult().getExtendedUser() == null) {
            throw new ClientGatewayException(DependencyLayer.USERSERVICE, ErrorType.AUTHENTICATION, AuthenticationErrors.UUID_NOT_FOUND.getErrorCode(), AuthenticationErrors.UUID_NOT_FOUND.getErrorMsg());
        }

        String country = "";

        MDCHelper.createMDC(client, null, ck, region.toUpperCase(), language, currency.toUpperCase(), controller, StringUtils.EMPTY,
                StringUtils.EMPTY, funnelSource, country);
        ResponseWrapper<GiftCardsData> giftCardsResponseWrapper = new ResponseWrapper<>();

        GiftCardClaimResponse giftCardsResponse = reviewService.claimGiftCard(claimGiftCardRequest, httpServletRequest.getParameterMap(), httpHeaderMap);//reviewService.availRooms(availRoomsRequest, httpServletRequest.getParameterMap(), tup.getY());
        // We are explicitly suppressing the nodes from the avail-rooms for the GCC HPA case

        if (giftCardsResponse.getResponse() != null) {
            giftCardsResponseWrapper.setResponse(utility.convertClaimResponse(giftCardsResponse.getResponse().getGiftCardData()));
            giftCardsResponseWrapper.setCorrelationKey(correlationKey);
            metricAspect.addToTime(new Date().getTime() - startTime, "giftCard/claim", MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
        } else if(giftCardsResponse.getError() != null) {
            GiftCardClaimErrorResponse errorHes = giftCardsResponse.getError();
            Error error = new Error(errorHes.getErrorCode(), errorHes.getMessage(), null, "Gift Card Redemption Failed");
            giftCardsResponseWrapper.setError(error);
        }
        MDC.clear();
        return new ResponseEntity<>(giftCardsResponseWrapper, HttpStatus.OK);
    }

    /***
     * We are suppressing V2 few nodes of availRooms for avail-rooms-v2.0
     * @param availRoomsResponse
     */
    private void suppressNodesForAvailRoomsV2(AvailRoomsResponse availRoomsResponse) {
        availRoomsResponse.setAddons(null);
        availRoomsResponse.setPropertyRules(null);
        availRoomsResponse.setPanInfo(null);
        availRoomsResponse.setFeatureFlags(null);
        availRoomsResponse.setAlerts(null);
        availRoomsResponse.setSafetyPersuasionMap(null);
        availRoomsResponse.setHydraSegments(null);
        availRoomsResponse.setCardData(null);
        availRoomsResponse.setHotelPersuasions(null);
        availRoomsResponse.setHighDemand(null);
    }

    @RequestMapping(value = "cg/validate-coupon/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<ValidateCouponResponseBody>> validateCoupon(
            @Valid @RequestBody ValidateCouponRequest validateCouponRequest,
            @PathVariable("client") String client, @PathVariable("version") String version,
            HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException{
        long startTime = new Date().getTime();
        client=client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,"", REVIEW_VAILDATE_COUPON, null);
        String correlationKey = tup.getX();
        ResponseWrapper<ValidateCouponResponseBody> validateCouponResponseWrapper = new ResponseWrapper<>();

        validateCouponResponseWrapper.setResponse(discountService.validateCoupon(validateCouponRequest, httpServletRequest.getParameterMap(), tup.getY(), client));
        validateCouponResponseWrapper.setCorrelationKey(correlationKey);
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/validate_coupon/",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
        return new ResponseEntity<>(validateCouponResponseWrapper, HttpStatus.OK);
    }


    @RequestMapping(value = "cg/search-addons/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<SearchAddonsResponse>> searchAddons(
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody SearchAddonsRequest searchAddonsRequest,
            HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException {
        long startTime = new Date().getTime();
        client=client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,searchAddonsRequest.getRequestDetails().getIdContext(),REVIEW_SEARCH_ADD_ONS,searchAddonsRequest);
        ResponseWrapper<SearchAddonsResponse> searchAddonsResponseWrapper = new ResponseWrapper<>();
        searchAddonsResponseWrapper.setResponse(addonsService.getAddons(searchAddonsRequest, httpServletRequest.getParameterMap(), tup.getY()));
        searchAddonsResponseWrapper.setCorrelationKey(tup.getX());
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/search_addons/",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
        return new ResponseEntity<>(searchAddonsResponseWrapper, HttpStatus.OK);
    }
    
    @RequestMapping(value = "cg/total-pricing/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<TotalPriceResponse>> getTotalPrice(
            @Valid @RequestBody TotalPricingRequest getTotalPricingRequest,
            @PathVariable("client") String client, @PathVariable("version") String version,
            HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException{
        long startTime = new Date().getTime();
        client = client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String,Map<String,String>> tup = requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,"", REVIEW_TOTAL_PRICING,null);
        String correlationKey =  tup.getX();
		ResponseWrapper<TotalPriceResponse> totalPriceResponseWrapper = new ResponseWrapper<>();
        totalPriceResponseWrapper.setCorrelationKey(correlationKey);
        totalPriceResponseWrapper.setResponse(reviewService.getTotalPrice(getTotalPricingRequest, httpServletRequest.getParameterMap(), correlationKey, tup.getY(), client));
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/total_pricing/",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
        return new ResponseEntity<>(totalPriceResponseWrapper, HttpStatus.OK);
    }

    @RequestMapping(value = "cg/get-policies/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<PoliciesResponse> getPolicies(
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody PoliciesRequest policiesRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException {
        long startTime = new Date().getTime();
        client=client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String, Map<String,String>> tup = requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,"",REVIEW_GET_POLICIES,null);
        String correlationKey =  tup.getX();
        requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, policiesRequest, correlationKey);

        PoliciesResponse policiesResponse = policyService.getPolicies(policiesRequest, httpServletRequest.getParameterMap());
        policiesResponse.setCorrelationKey(correlationKey);
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/get_policies/",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
        return new ResponseEntity<>(policiesResponse, HttpStatus.OK);
    }

    @RequestMapping(value = "cg/room-info/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<RoomInfoResponse> getRoomInfo(
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody RoomInfoRequest roomInfoRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException {
        long startTime = new Date().getTime();
        client=client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String, Map<String,String>> tup = requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,"",REVIEW_ROOM_INFO,null);
        String correlationKey =  tup.getX();
        requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, roomInfoRequest, correlationKey);
        RoomInfoResponse roomInfoResponse = roomInfoService.getRoomInfo(roomInfoRequest,httpServletRequest.getParameterMap());
        roomInfoResponse.setCorrelationKey(correlationKey);
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/room_info/",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
        return new ResponseEntity<>(roomInfoResponse, HttpStatus.OK);
    }

    @RequestMapping(value = "cg/room-infos/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<RoomInfoResponse>> getRoomInfos(
            @PathVariable("client") String client, @PathVariable("version") String version,
            @Valid @RequestBody RoomInfoRequest roomInfoRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException {
        long startTime = new Date().getTime();
        client=client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String, Map<String,String>> tup = requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,"", REVIEW_ROOM_INFOS,null);
        String correlationKey =  tup.getX();
        requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, roomInfoRequest, correlationKey);
        ResponseWrapper<RoomInfoResponse> roomInfoResponseWrapper = new ResponseWrapper<>();
        roomInfoResponseWrapper.setResponse(roomInfoService.getRoomInfos(roomInfoRequest,httpServletRequest.getParameterMap()));
        roomInfoResponseWrapper.setCorrelationKey(correlationKey);
        metricAspect.addToTime(new Date().getTime() - startTime, "cg/room_infos/",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        MDC.clear();
        return new ResponseEntity<>(roomInfoResponseWrapper, HttpStatus.OK);
    }

    @RequestMapping(value = "cg/pay-later-eligibility/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<PayLaterEligibilityResponse>> checkPayLaterEligibility(
            @Valid @RequestBody PayLaterEligibilityRequest request,
            @PathVariable("client") String client, @PathVariable("version") String version,
            HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException{
        long startTime = new Date().getTime();
        client=client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,"", PAY_LATER_ELIGIBILITY, null);
        String correlationKey = tup.getX();
        ResponseWrapper<PayLaterEligibilityResponse> responseWrapper = new ResponseWrapper<>();

        responseWrapper.setResponse(reviewService.fetchPayLaterEligibility(request, correlationKey,httpServletRequest.getParameterMap(), tup.getY(), client));
        responseWrapper.setCorrelationKey(correlationKey);
        metricAspect.addToTime(new Date().getTime() - startTime, PAY_LATER_ELIGIBILITY,MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
        MDC.clear();
        return new ResponseEntity<>(responseWrapper, HttpStatus.OK);
    }

    @RequestMapping(value = "cg/fetch-locations/{client}/{version}", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<FetchLocationsResponse>> fetchLocations(
            @Valid @RequestBody FetchLocationsRequest request,
            @PathVariable("client") String client, @PathVariable("version") String version,
            HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException {
        long startTime = System.currentTimeMillis();
        client = client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, "", FETCH_LOCATIONS, null);
        String correlationKey = tup.getX();
        ResponseWrapper<FetchLocationsResponse> responseWrapper = new ResponseWrapper<>();

        responseWrapper.setResponse(reviewService.fetchLocations(request, correlationKey, httpServletRequest.getParameterMap(), tup.getY(), client));
        metricAspect.addToTime(System.currentTimeMillis() - startTime, FETCH_LOCATIONS, MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
        MDC.clear();
        return new ResponseEntity<>(responseWrapper, HttpStatus.OK);
    }

    @RequestMapping(value = "cg/ugc-question/{srcClient}/2", method = RequestMethod.POST)
    public ResponseEntity<ResponseWrapper<ClientUgcResponse>> loadProgram(
            @PathVariable("srcClient") String client,
            @RequestBody ClientLoadProgramRequest request,
            HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId) {
        long startTime = System.currentTimeMillis();
        client = client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, "", CG_UGCQUESTIONS, null);
        String correlationKey = tup.getX();
        ResponseWrapper<ClientUgcResponse> responseWrapper = new ResponseWrapper<>();
        responseWrapper.setResponse(ugcService.fetchProgram18Question(request, httpServletRequest.getParameterMap(), correlationKey, tup.getY()));
        metricAspect.addToTime(System.currentTimeMillis() - startTime, CG_UGCQUESTIONS, MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
        MDC.clear();
        return new ResponseEntity<>(responseWrapper, HttpStatus.OK);
    }


    @RequestMapping(value = "cg/ugc-submit/partial/{srcClient}/2", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResponseWrapper<ClientUgcResponse>> submitAnswers(
            @PathVariable("srcClient") String client,
            @RequestPart(value = "file") ClientSubmitApiRequest requestData, MultipartRequest multipartRequest,
            HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
            @RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
            throws ClientGatewayException, IOException {
        long startTime = System.currentTimeMillis();
        client = client.toUpperCase();
        ck = requestHandler.effectiveCorrelationKey(requestId, ck);
        Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, "", CG_UGCSUBMIT, null);
        String correlationKey = tup.getX();
        ResponseWrapper<ClientUgcResponse> responseWrapper = new ResponseWrapper<>();
        validateRequest(requestData);
        responseWrapper.setResponse(ugcService.submitAnswers(requestData, multipartRequest, httpServletRequest.getParameterMap(), correlationKey, tup.getY()));
        metricAspect.addToTime(System.currentTimeMillis() - startTime, CG_UGCSUBMIT, MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
        MDC.clear();
        return new ResponseEntity<>(responseWrapper, HttpStatus.OK);
    }

    private void validateRequest(ClientSubmitApiRequest requestData) {

        if (requestData != null && requestData.getQuestion() != null &&   StringUtils.isNotBlank(requestData.getQuestion().getText())) {
            String text = requestData.getQuestion().getText();
            int half = text.length() / 2;
            String firstHalf = text.substring(0, half).trim();
            String secondHalf = text.substring(half).trim();

            if (firstHalf.equals(secondHalf)) {
                QuestionSet questionSet = requestData.getQuestion();
                questionSet.setText(firstHalf);
                requestData.setQuestion(questionSet);
            }
        }
    }
}
