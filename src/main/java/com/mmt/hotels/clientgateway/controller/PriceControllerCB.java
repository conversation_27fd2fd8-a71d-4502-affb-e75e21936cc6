package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.service.*;
import com.mmt.hotels.clientgateway.util.ClientBackendUtility;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.addon.GetAddonsRequest;
import com.mmt.hotels.model.request.emi.UpdateEmiDetailRequest;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.response.ErrorEntity;
import com.mmt.hotels.pojo.response.detail.upsell.UpsellSimilarResponse;
import com.mmt.hotels.util.Tuple;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.Map;

/**
 * This Controller is just to support older apps
 */

@RestController
@RequestMapping("/")
@Deprecated
public class PriceControllerCB {

    @Autowired
    DetailService detailService;

    @Autowired
    AddonsService addonsService;

    @Autowired
    ReviewService reviewService;

    @Autowired
    DiscountService discountService;

    @Autowired
    private EmiService emiService;

    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    private MetricAspect metricAspect;


    @RequestMapping(value = "entity/api/hotels/searchPrice", method = RequestMethod.POST)
    public String getPriceDetailsByHotels(@RequestBody PriceByHotelsRequestBody priceByHotelsRequestBody,
                                          @RequestParam(name = Constants.REGION, required = false) String siteDomain,
                                          @RequestParam(name = Constants.CURRENCY, required = false) String currency,
                                          @RequestParam(name = Constants.LANGUAGE, required = false) String language,
                                          @RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
                                          HttpServletRequest httpRequest, HttpServletResponse httpServletResponse) throws ClientGatewayException {
        String response;
        long startTime = new Date().getTime();
        try {
            Map<String, String> httpHeaderMap =preparingHeaderAndSettingFieldsFromParams(priceByHotelsRequestBody, siteDomain, correlationKey,requestId, httpRequest, httpServletResponse);
            response = detailService.searchPriceOld(priceByHotelsRequestBody, httpRequest.getParameterMap(), httpHeaderMap);
        } catch (Exception e) {
            response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
        } finally {
            metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/hotels/searchPrice",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), priceByHotelsRequestBody.getBookingDevice(), MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

            MDC.clear();
        }
        return response;
    }
    
    @RequestMapping(value = "entity/api/hotels/alternateDates", method = RequestMethod.POST)
    public String getAlternateDatesPriceResponse(@RequestBody PriceByHotelsRequestBody priceByHotelsRequestBody,
                                          @RequestParam(name = Constants.REGION, required = false) String siteDomain,
                                          @RequestParam(name = Constants.CURRENCY, required = false) String currency,
                                          @RequestParam(name = Constants.LANGUAGE, required = false) String language,
                                          @RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
                                          HttpServletRequest httpRequest, HttpServletResponse httpServletResponse) throws ClientGatewayException {
        String response;
        long startTime = new Date().getTime();
        try {
            Map<String, String> httpHeaderMap = preparingHeaderAndSettingFieldsFromParams(priceByHotelsRequestBody, siteDomain, correlationKey, requestId,httpRequest, httpServletResponse);
            response = detailService.alternateDatesPriceOld(priceByHotelsRequestBody, httpRequest.getParameterMap(), httpHeaderMap);
        } catch (Exception e) {
            response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
        } finally {
            metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/hotels/alternateDates",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), priceByHotelsRequestBody.getBookingDevice(), MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

            MDC.clear();
        }
        return response;
    }

    private Map<String, String> preparingHeaderAndSettingFieldsFromParams(@RequestBody PriceByHotelsRequestBody priceByHotelsRequestBody,
                                                                          String siteDomain,
                                                                          String correlationKey,
                                                                          String requestId,
                                                                          HttpServletRequest httpRequest,
                                                                          HttpServletResponse httpServletResponse) {
        long startTime = new Date().getTime();
        correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
        Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpRequest, httpServletResponse, correlationKey,priceByHotelsRequestBody.getBookingDevice(),priceByHotelsRequestBody.getIdContext(), httpRequest.getRequestURI(),null);
        correlationKey = tup.getX();
        Map<String,String> httpHeaderMap = tup.getY();
        httpHeaderMap.put("srcClient",priceByHotelsRequestBody.getBookingDevice());
        priceByHotelsRequestBody.setCorrelationKey(correlationKey);
        if (StringUtils.isNotBlank(siteDomain)) {
            priceByHotelsRequestBody.setSiteDomain(siteDomain.toUpperCase());
        }
        return tup.getY();
    }

    @RequestMapping(value = "entity/api/hotels/availPrice", method = RequestMethod.POST)
    public String getAvailPrice(@RequestBody PriceByHotelsRequestBody priceByHotelsRequestBody,
                                @RequestParam(name = Constants.REGION, required = false) String siteDomain,
                                @RequestParam(name = Constants.CURRENCY, required = false) String currency,
                                @RequestParam(name = Constants.LANGUAGE, required = false) String language,
                                @RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
                                HttpServletRequest httpRequest, HttpServletResponse httpServletResponse) throws ClientGatewayException {
        String response;
        long startTime = new Date().getTime();
        try {
            Map<String, String> httpHeaderMap = preparingHeaderAndSettingFieldsFromParams(priceByHotelsRequestBody, siteDomain, correlationKey, requestId, httpRequest, httpServletResponse);
            response = reviewService.getAvailPriceOld(priceByHotelsRequestBody, httpRequest.getParameterMap(), httpHeaderMap);
        } catch (Exception e) {
            response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
        } finally {
            metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/hotels/availPrice",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), priceByHotelsRequestBody.getBookingDevice(), MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

            MDC.clear();
        }
        return response;
    }
    
    @RequestMapping(value = "entity/api/hotels/getUpdatedDisplayPricing", method = RequestMethod.POST)
    public String getUpdatedPriceOccuLess(@RequestBody PriceByHotelsRequestBody priceByHotelsRequestBody,
                                @RequestParam(name = Constants.REGION, required = false) String siteDomain,
                                @RequestParam(name = Constants.CURRENCY, required = false) String currency,
                                @RequestParam(name = Constants.LANGUAGE, required = false) String language,
                                @RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
                                HttpServletRequest httpRequest, HttpServletResponse httpServletResponse) throws ClientGatewayException {
        String response;
        long startTime = new Date().getTime();
        try {
            Map<String, String> httpHeaderMap = preparingHeaderAndSettingFieldsFromParams(priceByHotelsRequestBody, siteDomain, correlationKey, requestId,httpRequest, httpServletResponse);
            response = reviewService.getUpdatedPriceOccuLessOld(priceByHotelsRequestBody, httpRequest.getParameterMap(), httpHeaderMap);
        } catch (Exception e) {
            response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
        } finally {
            metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/hotels/getUpdatedDisplayPricing",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), priceByHotelsRequestBody.getBookingDevice(), MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

            MDC.clear();
        }
        return response;
    }

    @RequestMapping(value = "entity/api/hotels/getUpdatedEmi", method = RequestMethod.POST)
    public String getUpdatedEmi(@RequestBody UpdateEmiDetailRequest updateEmiDetailRequest,
                                @RequestParam(name = Constants.CORRELATIONKEY, required = false) String correlationKey,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
                                HttpServletRequest httpRequest,
                                HttpServletResponse httpServletResponse) {
        String response;
        long startTime = new Date().getTime();
        try {
            correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
            Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpRequest, httpServletResponse, correlationKey,updateEmiDetailRequest.getBookingDevice(),updateEmiDetailRequest.getIdContext(), "entity/api/hotels/getUpdatedEmi",null);
            correlationKey = tup.getX();
            response = emiService.getUpdateEmiResponse(updateEmiDetailRequest, httpRequest.getParameterMap(), correlationKey, tup.getY());
        } catch (Exception e) {
            response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
        } finally {
            metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/hotels/getUpdatedEmi",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), updateEmiDetailRequest.getBookingDevice(), MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

            MDC.clear();
        }
        return response;
    }

    @RequestMapping(value ="entity/api/hotels/getAddOns", method = RequestMethod.POST)
    @ResponseBody
    public String getAddOnsForHotels(@RequestBody GetAddonsRequest getAddonsRequest,
                                     @RequestParam(name = Constants.REGION, required = false) String siteDomain,
                                     @RequestParam(name = Constants.CURRENCY, required = false) String currency,
                                     @RequestParam(name = Constants.LANGUAGE, required = false) String language,
                                     @RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
                                     HttpServletRequest httpRequest , HttpServletResponse httpServletResponse) {

        String response;
        long startTime = new Date().getTime();
        try {
            correlationKey = requestHandler.effectiveCorrelationKey(requestId, correlationKey);
            Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpRequest, httpServletResponse, correlationKey,getAddonsRequest.getBookingDevice(),getAddonsRequest.getIdContext() ,"entity/api/hotels/getAddOns",null);
            correlationKey = tup.getX();
            getAddonsRequest.setCorrelationKey(correlationKey);
            if (StringUtils.isNotBlank(siteDomain)) {
                getAddonsRequest.setSiteDomain(siteDomain.toUpperCase());
            }
             response =  addonsService.getAddons(getAddonsRequest, httpRequest.getParameterMap(), tup.getY());
        } catch (Exception e) {
            response = ClientBackendUtility.setCBErrorResponse(CBError.GENERIC_ERROR);
        } finally {
            metricAspect.addToTime(new Date().getTime() - startTime, "entity/api/hotels/getAddOns",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), getAddonsRequest.getBookingDevice(), MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

            MDC.clear();
        }
       return  response;
    }

    @RequestMapping(value = "/entity/api/hotel/getUpsellHotels", method = RequestMethod.POST)
    @ResponseBody
    public UpsellSimilarResponse getUpsellApiResponse(@RequestBody HotelDetailsMobRequestBody requestBody,
                                                                      @RequestParam(name = Constants.REGION, required = false) String siteDomain,
                                                                      @RequestParam(name = Constants.CURRENCY, required = false) String currency,
                                                                      @RequestParam(name = Constants.LANGUAGE, required = false) String language,
                                                                      @RequestParam(name = Constants.CORRELATIONKEY, required = false, defaultValue = Constants.EMPTY_STRING) String correlationKey,
@RequestParam(name = Constants.REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId,
                                                                      HttpServletRequest httpRequest , HttpServletResponse httpServletResponse) {

        long startTime = new Date().getTime();
        UpsellSimilarResponse response = new UpsellSimilarResponse();
        ErrorEntity errorEntity = new ErrorEntity(CBError.HOTEL_UPSELL_API_ERROR.getDescription(), CBError.HOTEL_UPSELL_API_ERROR.getCode());
        response.setErrorEntity(errorEntity);
        metricAspect.addToTime(new Date().getTime() - startTime, "/entity/api/hotel/getUpsellHotels",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), requestBody.getBookingDevice(), MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

        return response;

    }

}