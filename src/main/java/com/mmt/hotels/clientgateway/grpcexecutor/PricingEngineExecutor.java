package com.mmt.hotels.clientgateway.grpcexecutor;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.grpcconnector.PricingEngineConnector;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.emi.detail.EmiDetailRequest;
import com.mmt.hotels.emi.detail.EmiDetailResponse;
import com.mmt.hotels.emi.detail.EmiDetailServiceGrpc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

import static com.mmt.hotels.clientgateway.constants.ControllerConstants.FETCH_EMI_DETAILS;

@Component
public class PricingEngineExecutor {

    @Autowired
    PricingEngineConnector pricingEngineConnector;

    @Value("${emi.detail.default.timeout}")
    private int emiDetailDefaultTimeout;

    @Autowired
    private MetricAspect metricAspect;

    private static final Logger LOGGER = LoggerFactory.getLogger(PricingEngineExecutor.class);

    public EmiDetailResponse executeEmiDetail(EmiDetailRequest request) {
        LOGGER.warn("Sending emiDetails request to pricing engine");
        try {
            long startTime = System.currentTimeMillis();
            LOGGER.debug("EmiDetails request is: {}", JsonFormat.printer().omittingInsignificantWhitespace().print(request));
            EmiDetailResponse response = EmiDetailServiceGrpc.newBlockingStub(pricingEngineConnector.getChannel(request.getDeviceDetails().getBookingDevice()))
                    .withDeadlineAfter(emiDetailDefaultTimeout, TimeUnit.MILLISECONDS).getEmiDetails(request);
            LOGGER.debug("EmiDetails response is: {}", JsonFormat.printer().omittingInsignificantWhitespace().print(response));

            //logging the time taken to fetch the emi details from downstream
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_EMI_DETAILS_DOWNSTREAM_PROCESS, FETCH_EMI_DETAILS, System.currentTimeMillis() - startTime);
            return response;
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("Error while printing request/response", e);
        }
        return null;
    }

}
