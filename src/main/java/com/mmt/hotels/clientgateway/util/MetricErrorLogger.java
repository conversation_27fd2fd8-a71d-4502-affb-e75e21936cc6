package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.util.MDCHelper.MDCKeys;
import com.mmt.hotels.kafka.AsyncKafkaLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

@Component
@EnableAsync
public class MetricErrorLogger {
	
	@Value("${metric.error.log.topic.id:48}")
	private String metricErrorLogTopicId;
	
	@Value("${metric.error.log.template.id:10072}")
	private String metricErrorLogTemplateId;

	@Value("${enable.kafka.logging}")
	private boolean enableKafkaLogging;
//
//	@Autowired
//	@Qualifier("asyncKafkaLogger")
	AsyncKafkaLogger<MetricErrorCodes> asyncKafkaLogger;

	@Autowired
	MetricAspect metricAspect;
	
	private static final Logger logger = LoggerFactory.getLogger(MetricErrorLogger.class);
	
	public void logClientGatewayException(ClientGatewayException e, String functionName) {
		if (e instanceof ErrorResponseFromDownstreamException) {
			logger.error("error occured in functionName: " + functionName + " : "+ e.getMessage());
			logger.debug("error occured in functionName: " + functionName + " : "+ e.getMessage(), e);
		}else
			logger.error("error occured in functionName: " + functionName + " : "+ e.getMessage(), e);
		MetricError metricError = new MetricError(e.getDependencyLayer(), 
				e.getErrorType(), e.getCode(), 
				e.getMessage());
		logErrorInMetric(metricError, MDC.getCopyOfContextMap());
	}
	
	public void logGeneralException(Exception e, String functionName, DependencyLayer dependencyLayer,
									ErrorType errorType, String code, String message) {
		logger.error("error occured in functionName: " + functionName + " : "+ e.getMessage(), e);
		MetricError metricError = new MetricError(dependencyLayer, 
				errorType, code, message);
		logErrorInMetric(metricError, MDC.getCopyOfContextMap());
	}
	
	@Async("kafkaThreadPool")
	public void logErrorInMetric(MetricError metricError, Map<String, String> contextMap) {
		MetricErrorCodes metricErrorCodes = getMetricErrorCodes(metricError, contextMap);
		logger.warn("Metric error: {}", metricErrorCodes);
		if (enableKafkaLogging) {
			//asyncKafkaLogger.log(metricErrorCodes, metricErrorLogTopicId, metricErrorLogTemplateId, true);
		}
	}

	private MetricErrorCodes getMetricErrorCodes(MetricError metricError, Map<String, String> contextMap) {
		MetricErrorCodes metricErrorCodes = new MetricErrorCodes();
		metricErrorCodes.setM2(metricErrorLogTopicId);
		metricErrorCodes.setTpl1(metricErrorLogTemplateId);
		metricErrorCodes.setApi208(StringUtils.isNotBlank(contextMap.get(MDCKeys.CORRELATION.getStringValue()))?contextMap.get(MDCKeys.CORRELATION.getStringValue()):"");
		metricErrorCodes.setPs438(getMetricErrorCode(metricError));
		metricErrorCodes.setPd147(StringUtils.isNotBlank(metricError.getErrorMessage())?metricError.getErrorMessage():"");
		metricErrorCodes.setPm626(null!=metricError.getDependencyLayer()?metricError.getDependencyLayer().name():"");
		
		SimpleDateFormat timeStampFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
		String hostAddress = "";
		try {
			hostAddress = InetAddress.getLocalHost().getHostAddress();
		} catch (UnknownHostException e) {
			logger.error("error occured in getting hostAddress", e.getMessage());
		}
		metricErrorCodes.setM124(timeStampFormat.format(new Date()));
		metricErrorCodes.setM126(true);
		//metricErrorCodes.setU125("");
		metricErrorCodes.setM127(hostAddress);
		metricErrorCodes.setBd359(dateFormatter.format(new Date()));
		metricErrorCodes.setM101(StringUtils.isNotBlank(contextMap.get(MDCKeys.CONTROLLER.getStringValue()))
				?contextMap.get(MDCKeys.CONTROLLER.getStringValue()):"");
		//metricErrorCodes.setPm627("");
		metricErrorCodes.setM123(timeStampFormat.format(new Date()));
		return metricErrorCodes;
	}

	private String getMetricErrorCode(MetricError metricError) {
		StringBuilder metricErrorCode  = new StringBuilder();
		metricErrorCode.append(Constants.ERR_CODE_API).append(metricError.getDependencyLayer().getCode())
			.append(metricError.getErrorType().getCode()).append(metricError.getErrorCode());
		metricAspect.addToCounter(metricError.getDependencyLayer(), metricError.getErrorType(), metricErrorCode.toString());
		return metricErrorCode.toString();
	}
}
