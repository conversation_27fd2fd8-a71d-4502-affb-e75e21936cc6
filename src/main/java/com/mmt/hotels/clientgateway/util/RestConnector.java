package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.ConnectivityErrors;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.RestErrors;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpRequest;
import org.apache.http.HttpResponse;
import org.apache.http.ParseException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

@Component
public class RestConnector {

	@Value("#{'${gcc.downstream.common.headers}'.split(',')}")
	private List<String> gccHeaderKeys;

	@Value("#{'${retry.enabled.urls}'.split(',')}")
	private List<String> retryEnabledUrls;

	@Value("${spring.profiles.active:}")
	private String activeProfile;

	private static final Logger LOGGER = LoggerFactory.getLogger(RestConnector.class);


	public String postMessages(HttpClient httpClient, String requestBody, Map<String, String> headers,
							   String url, DependencyLayer dependencyLayer) throws RestConnectorException {
		return postMessages(httpClient, requestBody, headers, url, dependencyLayer, 0);
	}

	public String postMessages(HttpClient httpClient, String requestBody, Map<String, String> headers,
			String url, DependencyLayer dependencyLayer, int retriesLeft) throws RestConnectorException {
		url = appendGCCQueryParamsInUrl(url);
		url = appendSrcClientInUrl(url,headers);
		url = appendLoggingParameters(url);
		url = appendPaginatedValue(url);
		HttpPost httpPost = new HttpPost(url);
		populateHeaders(headers, httpPost);
		HttpEntity httpEntity = new StringEntity(requestBody, StandardCharsets.UTF_8.name());
		httpPost.setEntity(httpEntity);
		HttpResponse response = null;
		String result = null;
		try {
			response = httpClient.execute(httpPost);
			setResponseHeadersInMdc(response.getAllHeaders());
			result = EntityUtils.toString(response.getEntity());
			EntityUtils.consume(response.getEntity());
		} catch (ConnectTimeoutException | SocketTimeoutException e) {
			handleTimeoutExceptions(e, url, httpClient, requestBody, headers, dependencyLayer, retriesLeft);
		} catch (IOException | ParseException e) {
			throw new RestConnectorException(dependencyLayer, ErrorType.CONNECTIVITY,
					ConnectivityErrors.IO_ERROR.getErrorCode(), e.getMessage());
		} catch (Exception e) {
			throw new RestConnectorException(dependencyLayer, ErrorType.CONNECTIVITY, 
					ConnectivityErrors.REST_ERROR.getErrorCode(), e.getMessage());
		}finally {
			if (activeProfile != null && activeProfile.contains(Constants.PROFILE_DEV)) {
				logRequestCurl(requestBody, headers, url, dependencyLayer, "POST");
			}
			if(null!=response && null!=response.getStatusLine() && response.getStatusLine().getStatusCode()!=200){
				LOGGER.error("error in restcall from CG, url:{}, requestBody:{}, reason:{}, statusCode:{}", url, requestBody, response.getStatusLine().getReasonPhrase(), response.getStatusLine().getStatusCode());
				if(null!=httpPost && null!=httpPost.getAllHeaders()) {
					LOGGER.error("header:{}",httpPost.getAllHeaders());
				}

			}
		}
		return result;
	}

	private void logRequestCurl(String requestBody, Map<String, String> headers, String url, DependencyLayer dependencyLayer, String method) {
		String curl = HCurlCreatorUtility.generateCurl(url,method , headers, requestBody);
		LOGGER.debug("\n{} curl: {}", dependencyLayer.name(), curl);
	}

	public String postMessagesMultipart(HttpClient httpClient, HttpEntity requestBody, Map<String, String> headers,
							   String url, DependencyLayer dependencyLayer) throws RestConnectorException {
		HttpPost httpPost = new HttpPost(url);
		httpPost.setEntity(requestBody);
		HttpResponse response = null;
		String result = null;
		try {
			response = httpClient.execute(httpPost);
			setResponseHeadersInMdc(response.getAllHeaders());
			result = EntityUtils.toString(response.getEntity());
			EntityUtils.consume(response.getEntity());
		} catch (IOException | ParseException e) {
			throw new RestConnectorException(dependencyLayer, ErrorType.CONNECTIVITY,
					ConnectivityErrors.IO_ERROR.getErrorCode(), e.getMessage());
		} catch (Exception e) {
			throw new RestConnectorException(dependencyLayer, ErrorType.CONNECTIVITY,
					ConnectivityErrors.REST_ERROR.getErrorCode(), e.getMessage());
		}finally {
			if(null!=response && null!=response.getStatusLine() && response.getStatusLine().getStatusCode()!=200){
				LOGGER.error("error in restcall from CG, url:{}, requestBody:{} , response:{}, reason:{}, statusCode:{}", url, requestBody, response, response.getStatusLine().getReasonPhrase(), response.getStatusLine().getStatusCode());
				if(null!=httpPost && null!=httpPost.getAllHeaders()) {
					LOGGER.error("header:{}",httpPost.getAllHeaders());
				}

			}
		}
		return result;
	}
	
	public String getMessages(HttpClient httpClient,Map<String, String> headers, String url, DependencyLayer dependencyLayer) 
			throws RestConnectorException{
		url = appendGCCQueryParamsInUrl(url);
		url = appendSrcClientInUrl(url,headers);
		url = appendLoggingParameters(url);
		url = appendPaginatedValue(url);
		HttpGet httpGet = new HttpGet(url);
		for (Map.Entry<String, String> header : headers.entrySet()) {
			httpGet.addHeader(header.getKey(), header.getValue());
		}
		populateGCCHeadersInHttpRequest(httpGet);
		HttpResponse response = null;
		String result = null;
		try {
			response = httpClient.execute(httpGet);
			setResponseHeadersInMdc(response.getAllHeaders());
			result = EntityUtils.toString(response.getEntity());
			EntityUtils.consume(response.getEntity());
			if (activeProfile != null && Constants.PROFILE_DEV.contains(activeProfile)) {
				logRequestCurl("", headers, url, dependencyLayer,"GET");
			}
		} catch (IOException | ParseException e) {
			throw new RestConnectorException(dependencyLayer, ErrorType.CONNECTIVITY, 
					ConnectivityErrors.IO_ERROR.getErrorCode(), e.getMessage());
		} catch (Exception e) {
			throw new RestConnectorException(dependencyLayer, ErrorType.CONNECTIVITY, 
					ConnectivityErrors.REST_ERROR.getErrorCode(), e.getMessage());
		}
		return result;
	}

	private void handleTimeoutExceptions(Exception e, String url, HttpClient httpClient, String requestBody, Map<String, String> headers, DependencyLayer dependencyLayer, int retriesLeft) throws RestConnectorException {
		LOGGER.error("Exception occurred for url:{}", url, e);
		if(retriesLeft > 0 && retryEnabledUrls.stream().anyMatch(url.toUpperCase()::contains)) {
			LOGGER.warn("retrying call for url: {} with {} retries left", url, --retriesLeft);
			postMessages(httpClient, requestBody, headers, url, dependencyLayer, retriesLeft);
			return;
		}
		else if (retriesLeft == 0 && retryEnabledUrls.stream().anyMatch(url.toUpperCase()::contains)) {
			LOGGER.error("No more retries for url: {} ", url);
		}

		String errorCode = e instanceof ConnectTimeoutException ? RestErrors.CO_TIMEOUT_ERROR.getErrorCode() : RestErrors.SO_TIMEOUT_ERROR.getErrorCode();
		throw new RestConnectorException(dependencyLayer, ErrorType.CONNECTIVITY, errorCode, e.getMessage());
	}

	private void setResponseHeadersInMdc(Header[] headerlist){
		if(headerlist != null && headerlist.length > 0 ){
			for(int i=0; i< headerlist.length; i++){
				if (headerlist[i].getName().equalsIgnoreCase(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue())){
					MDC.put(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue(), headerlist[i].getValue());
				}
				if (headerlist[i].getName().equalsIgnoreCase(MDCHelper.MDCKeys.COUNTRY.getStringValue())){
					MDC.put(MDCHelper.MDCKeys.COUNTRY.getStringValue(), headerlist[i].getValue());
				}
			}
		}
	}
	
	private void populateGccHeadersFromMdc(Map<String, String> headers) {
		if (!headers.containsKey(Constants.HEADER_REGION)) {
			if (StringUtils.isNotBlank(MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()))) {
				headers.put(Constants.HEADER_REGION, MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()));
			} else {
				headers.put(Constants.HEADER_REGION, Constants.DEFAULT_SITE_DOMAIN);
			}
		}
		if (!headers.containsKey(Constants.HEADER_CURRENCY)) {
			if (StringUtils.isNotBlank(MDC.get(MDCHelper.MDCKeys.CURRENCY.getStringValue()))) {
				headers.put(Constants.HEADER_CURRENCY, MDC.get(MDCHelper.MDCKeys.CURRENCY.getStringValue()));
			} else {
				headers.put(Constants.HEADER_CURRENCY, Constants.DEFAULT_CUR_INR);
			}
		}
		if (!headers.containsKey(Constants.HEADER_LANGUAGE)) {
			if (StringUtils.isNotBlank(MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue()))) {
				headers.put(Constants.HEADER_LANGUAGE, MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue()));
			} else {
				headers.put(Constants.HEADER_LANGUAGE, Constants.DEFAULT_LANGUAGE);
			}
		}
	}
	
	private void populateGCCHeadersInHttpRequest(HttpRequest httpRequest) {
		HashMap<String, String> gccExtraHeaders = new HashMap<>();
		populateGccHeadersFromMdc(gccExtraHeaders);
		if (MapUtils.isNotEmpty(gccExtraHeaders)) {
			for (String headerKey : gccExtraHeaders.keySet()) {
				httpRequest.addHeader(headerKey, gccExtraHeaders.get(headerKey));
			}
		}
	}
	
	private void populateHeaders(Map<String, String> headers, HttpPost httpPost) {
		if (MapUtils.isNotEmpty(headers)) {
			populateGccHeadersFromMdc(headers);
			setHeadersInHttpRequest(headers, httpPost);
		}else{
			headers = new HashMap<>();
			populateGccHeadersFromMdc(headers);
			setHeadersInHttpRequest(headers, httpPost);
		}

		if(MapUtils.isNotEmpty(headers)
				&& (!headers.containsKey(Constants.HEADER_CONTENT_TYPE) && !headers.containsKey(Constants.HEADER_CONTENT_TYPE.toLowerCase()))){
			headers.put(Constants.HEADER_CONTENT_TYPE, Constants.HEADER_CONTENT_APPLICATION_JSON);
		}

        if(MapUtils.isNotEmpty(headers)
                && (!headers.containsKey(Constants.HEADER_ACCEPT_TYPE) && !headers.containsKey(Constants.HEADER_ACCEPT_TYPE.toLowerCase()))){
            headers.put(Constants.HEADER_ACCEPT_TYPE, Constants.HEADER_CONTENT_APPLICATION_JSON_UTF_8);
        }
	}
	
	private void setHeadersInHttpRequest(Map<String, String> headers, HttpRequest httpRequest) {
		String headerKey;
		String headerValue;
		for (Entry<String, String> entry : headers.entrySet()) {
			headerKey = entry.getKey();
			headerValue = entry.getValue();
			if (!(Constants.CONTENT_LENGTH).equals(headerKey)) {
				httpRequest.addHeader(headerKey, headerValue);
			}
		}
	}

	private String appendGCCQueryParamsInUrl(String relativeURL) {
		String region = StringUtils.isNotBlank(MDC.get(MDCHelper.MDCKeys.REGION.getStringValue())) ? MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()) : Constants.DEFAULT_SITE_DOMAIN;
		String language = StringUtils.isNotBlank(MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue())) ? MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue()) : Constants.DEFAULT_LANGUAGE;
		String currency = StringUtils.isNotBlank(MDC.get(MDCHelper.MDCKeys.CURRENCY.getStringValue())) ? MDC.get(MDCHelper.MDCKeys.CURRENCY.getStringValue()) : Constants.DEFAULT_CUR_INR;
		StringBuilder urlBuilder = new StringBuilder(relativeURL);
		try {
			for (String queryParamKey : gccHeaderKeys) {
				switch (queryParamKey) {
					case Constants.REGION:
						if(!relativeURL.contains("&"+queryParamKey+"=") && !relativeURL.contains("?"+queryParamKey+"=")){
							if(urlBuilder.toString().indexOf("?") == -1)
								urlBuilder.append("?").append(queryParamKey+"=").append(region);
							else
								urlBuilder.append("&").append(queryParamKey+"=").append(region);
						}
						break;
					case Constants.LANGUAGE:
						if(!relativeURL.contains("&"+queryParamKey+"=") && !relativeURL.contains("?"+queryParamKey+"=")){
							if(urlBuilder.toString().indexOf("?") == -1)
								urlBuilder.append("?").append(queryParamKey+"=").append(language);
							else
								urlBuilder.append("&").append(queryParamKey+"=").append(language);
						}
						break;
					case Constants.CURRENCY:
						if(!relativeURL.contains("&"+queryParamKey+"=") && !relativeURL.contains("?"+queryParamKey+"=")){
							if(urlBuilder.toString().indexOf("?") == -1)
								urlBuilder.append("?").append(queryParamKey+"=").append(currency);
							else
								urlBuilder.append("&").append(queryParamKey+"=").append(currency);
						}
						break;
				}
			}
			return urlBuilder.toString();
		} catch (Exception ex) {
			LOGGER.error("Error while updating gcc query params. Falling back to supplied url:{} ", relativeURL, ex);
		}
		return relativeURL;
	}

	private String appendSrcClientInUrl(String url,Map<String, String> headers) {
		if (StringUtils.isNotBlank(url) && !url.contains("?srcClient=") && !url.contains("&srcClient=")) {
			StringBuilder urlBuilder = new StringBuilder(url);
			String srcClient = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
			if (StringUtils.isBlank(srcClient) && MapUtils.isNotEmpty(headers) && headers.containsKey("srcClient")) {
				/* Pick from header - old CB urls compatibility : To be removed once CB is deprecated */
				srcClient = headers.get("srcClient");
			}
			if (!urlBuilder.toString().contains("?"))
				urlBuilder.append("?").append("srcClient"+"=").append(srcClient);
			else
				urlBuilder.append("&").append("srcClient"+"=").append(srcClient);
			return urlBuilder.toString();
		}
		return url;
	}

	private String appendLoggingParameters(String relativeUrl) {
		StringBuilder sb = new StringBuilder(relativeUrl);
		if (MDC.get(MDCHelper.MDCKeys.LENGTH_OF_STAY.getStringValue()) != null) {
			sb.append(Constants.AMP_LENGTH_OF_STAY).append(MDC.get(MDCHelper.MDCKeys.LENGTH_OF_STAY.getStringValue()));
		}
		if (MDC.get(MDCHelper.MDCKeys.ADVANCE_PURCHASE.getStringValue()) != null) {
			sb.append(Constants.AMP_ADVANCE_PURCHASE).append(MDC.get(MDCHelper.MDCKeys.ADVANCE_PURCHASE.getStringValue()));
		}
		if (MDC.get(MDCHelper.MDCKeys.ADULT_COUNT.getStringValue()) != null) {
			sb.append(Constants.AMP_ADULT_COUNT).append(MDC.get(MDCHelper.MDCKeys.ADULT_COUNT.getStringValue()));
		}
		if (MDC.get(MDCHelper.MDCKeys.CHILD_COUNT.getStringValue()) != null) {
			sb.append(Constants.AMP_CHILD_COUNT).append(MDC.get(MDCHelper.MDCKeys.CHILD_COUNT.getStringValue()));
		}
		return sb.toString();
	}

	private String appendPaginatedValue(String relativeUrl) {
		StringBuilder sb = new StringBuilder(relativeUrl);
		if (MDC.get(MDCHelper.MDCKeys.PAGINATED.getStringValue()) != null) {
			sb.append(Constants.AMP_PAGINATED).append(MDC.get(MDCHelper.MDCKeys.PAGINATED.getStringValue()));
		}
		return sb.toString();
	}

	public void setUgcRequestHeaders(Map<String, String> headers, String contentType) {
		headers.put(Constants.HEADER_CONTENT_TYPE, contentType);
	}

}
