package com.mmt.hotels.clientgateway.util;

import org.jboss.logging.MDC;

public class MDCHelper {

	public enum MDCKeys {
		CLIENT("client"),
		CORRELATION("correlation"),
		TID("tid"),
		REGION("region"),
		LANGUAGE("language"),
		CURRENCY("currency"),
		IDCONTEXT("idContext"),
		CONTROLLER("controller"),
		MDC_SRC_CHANNEL( "src_channel"),
		COUNTRY("country"),
		ADVANCE_PURCHASE("advance_purchase"),
		LENGTH_OF_STAY("length_of_stay"),
		ADULT_COUNT("adult_count"),
		CHILD_COUNT("child_count"),
		PAGINATED("paginated"),
		FUNNEL_SOURCE("funnel"),
		TRAFFIC_TYPE("trafficType"),
		LDRSEGREGATION("ldrsegregation"),
		B2B_PARTNER_NAME("b2b_partner_name"),
		B2B_PARTNER_AUTH("b2b_partner_auth");
		
		private String value;

		private MDCKeys(String value) {
			this.value = value;
		}

		public String getStringValue() {
			return value;
		}
	}
	
	public static void createMDC(String client, String tid, String correlationKey,String region,String language, String currency,String controller, String idContext, String trafficType, String funnelSource, String country) {
		MDC.put(MDCKeys.CLIENT.getStringValue(), client);
		MDC.put(MDCKeys.TID.getStringValue(), tid);
		MDC.put(MDCKeys.CORRELATION.getStringValue(), correlationKey);
		MDC.put(MDCKeys.REGION.getStringValue(), region);
		MDC.put(MDCKeys.LANGUAGE.getStringValue(), language);
		MDC.put(MDCKeys.CURRENCY.getStringValue(), currency);
		MDC.put(MDCKeys.CONTROLLER.getStringValue(), controller);
		MDC.put(MDCKeys.IDCONTEXT.getStringValue(), idContext);
		MDC.put(MDCKeys.TRAFFIC_TYPE.getStringValue(), trafficType);
		MDC.put(MDCKeys.FUNNEL_SOURCE.getStringValue(), funnelSource);
		MDC.put(MDCKeys.COUNTRY.getStringValue(), country);
	}
}
