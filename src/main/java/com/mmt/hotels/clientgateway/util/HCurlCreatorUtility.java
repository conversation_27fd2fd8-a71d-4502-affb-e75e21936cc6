package com.mmt.hotels.clientgateway.util;

import com.jayway.restassured.response.Cookie;
import com.jayway.restassured.response.Header;
import com.jayway.restassured.specification.FilterableRequestSpecification;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class HCurlCreatorUtility {

    public static String generateCurl(String uri, String method, Map<String, String> headers, String body) {

        List<String> curlParts = new ArrayList<>();
        curlParts.add("curl");

        // Add method if not GET
        if (!method.equals("GET")) {
            curlParts.add("-X");
            curlParts.add(method);
        }

        // Add headers
        if (headers != null) {
            for (Map.Entry<String, String> header : headers.entrySet()) {
                curlParts.add("-H");
                curlParts.add("'" + header.getKey() + ": " + header.getValue() + "'");
            }
        }

        // Add body
        if (body != null && !body.isEmpty()) {
            curlParts.add("-d");
            curlParts.add("'" + escapeQuotes(body) + "'");
        }

        // Add URL
        curlParts.add("'" + uri + "'");

        return String.join(" ", curlParts);
    }

    private static String escapeQuotes(String input) {
        return input.replace("'", "'\\''");
    }

}
