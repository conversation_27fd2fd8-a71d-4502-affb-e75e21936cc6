package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.response.moblanding.LocationData;
import com.mmt.hotels.clientgateway.response.moblanding.LocationDataCategory;
import com.mmt.hotels.clientgateway.response.moblanding.LocationDataTag;
import com.mmt.hotels.model.response.staticdata.BbLatLong;
import com.mmt.hotels.model.response.staticdata.BboxLocationDetails;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class LocationDataUtil {
    public LocationData buildLocationData(com.mmt.hotels.pojo.LocationRecommendation.LocationData locationDataHES){
        if(locationDataHES == null){
            return null;
        }
        LocationData locationDataCG = new LocationData();
        locationDataCG.setCategory(buildLocationDataCategory(locationDataHES.getCategory()));
        locationDataCG.setMeta(buildMetaData(locationDataHES.getMeta()));
        if (locationDataCG.getCategory() == null && locationDataCG.getMeta() == null) {
            return null;
        }
        return locationDataCG;
    }

    private List<LocationDataCategory> buildLocationDataCategory(List<com.mmt.hotels.pojo.LocationRecommendation.LocationDataCategory> categoryList) {
        if (CollectionUtils.isEmpty(categoryList)) {
            return null;
        }
        List<LocationDataCategory> locationDataCategoryCG = new ArrayList<>();
        for(com.mmt.hotels.pojo.LocationRecommendation.LocationDataCategory category : categoryList) {
            List<LocationDataTag> locationDataTagList = buildLocationDataTagList(category.getTags());
            if(CollectionUtils.isEmpty(locationDataTagList)){
                return null;
            }
            LocationDataCategory locationDataCategory = new LocationDataCategory();
            locationDataCategory.setTags(locationDataTagList);
            locationDataCategory.setCategoryId(category.getCategoryId());
            locationDataCategory.setDesc(category.getDesc());
            locationDataCategoryCG.add(locationDataCategory);
        }
        return locationDataCategoryCG;
    }
    private List<LocationDataTag> buildLocationDataTagList(List<com.mmt.hotels.pojo.LocationRecommendation.LocationDataTagList> tagLists) {
        if(CollectionUtils.isEmpty(tagLists))
            return null;
        List<LocationDataTag> tagListCG = new ArrayList<>();
        for (com.mmt.hotels.pojo.LocationRecommendation.LocationDataTagList tag : tagLists) {
            // Skip adding tags if locusType is "city"
            if (Constants.TYPE_CITY.equalsIgnoreCase(tag.getLocusType())) {
                continue;
            }
            if(CollectionUtils.isEmpty(tag.getTags())) {
                return null;
            }
            for (com.mmt.hotels.pojo.LocationRecommendation.LocationDataTag locationDataTag : tag.getTags()) {
                LocationDataTag tagCG = new LocationDataTag();
                tagCG.setMatchmakerId(locationDataTag.getMatchmakerId());
                tagCG.setMatchmakerType(locationDataTag.getMatchmakerType());
                tagCG.setName(locationDataTag.getName());
                tagCG.setTagline(locationDataTag.getTagline());
                tagCG.setCategory(locationDataTag.getCategory());
                tagCG.setContext(locationDataTag.getContext());
                tagCG.setTag(locationDataTag.getTag());
                tagCG.setHighlights(locationDataTag.getHighlights());
                tagCG.setDetail_description(locationDataTag.getDetail_description());
                tagCG.setMedia(locationDataTag.getMedia());
                tagCG.setCityName(locationDataTag.getCityName());
                tagCG.setMeta(buildMetaData(locationDataTag.getMeta()));
                tagListCG.add(tagCG);
            }
        }
        if(CollectionUtils.isEmpty(tagListCG)) {
            return null;
        }
        return tagListCG;
    }
    private BboxLocationDetails buildMetaData(BboxLocationDetails metaHES){
        if(metaHES == null)
            return null;
        BboxLocationDetails metaCG = new BboxLocationDetails();
        metaCG.setSimplifiedBoundary(metaHES.getSimplifiedBoundary());
        metaCG.setZoomLevel(metaHES.getZoomLevel());
        metaCG.setBbox(metaHES.getBbox());
        metaCG.setCentrePoint(buildCentre(metaHES));
        return metaCG;
    }

    private com.mmt.hotels.pojo.matchmaker.LatLongAndBounds buildCentre(BboxLocationDetails metaHES){
        com.mmt.hotels.pojo.matchmaker.LatLongAndBounds centrePoint = null;
        if(metaHES.getMatchMakerTagLatLngObject() != null){
            centrePoint = new com.mmt.hotels.pojo.matchmaker.LatLongAndBounds();
            centrePoint.setLat(metaHES.getMatchMakerTagLatLngObject().getLat());
            centrePoint.setLng(metaHES.getMatchMakerTagLatLngObject().getLng());
        }
        else if(metaHES.getBbox() != null) {
            centrePoint = getCentreOfBbox(metaHES.getBbox());
        }
        return centrePoint;
    }
    private static com.mmt.hotels.pojo.matchmaker.LatLongAndBounds getCentreOfBbox(BbLatLong bbox){
        com.mmt.hotels.pojo.matchmaker.LatLongAndBounds centrePoint = null;
        if(bbox != null && bbox.getNe() != null && bbox.getSw() != null) {
            centrePoint = new com.mmt.hotels.pojo.matchmaker.LatLongAndBounds();
            double centerLat = (bbox.getNe().getLat() + bbox.getSw().getLat()) / 2.0;
            double centerLng = (bbox.getNe().getLng() + bbox.getSw().getLng()) / 2.0;
            centrePoint.setLat(String.valueOf(centerLat));
            centrePoint.setLng(String.valueOf(centerLng));
        }
        return centrePoint;
    }
}
