package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.constants.Constants;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.TextStyle;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

@Component
public class DateUtil {

    /*
    HH:MM => 24 hour format
    hh::mm => 12 hour format
    a      => am/pm
    MMM    => Jan/Feb
    DD     -> Date(22 / 02)
    YYYY   => 2022
     */
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String MMDDYYYY = "MMddyyyy";
    public static final String DD_MMM_HH_MM_a = "dd MMM,HH:MM a"; // 24 hour format (Capital HH:MM) (a for am/pm)
    public static final String DD_MMM_hh_mm_a = "dd MMM,hh:mm a"; // 12 hour format (Small case hh::mm)
    public static final String DD_MMM_HH_MM = "dd MMM,HH:MM";
    public static final String DD_MMM_YYYY = "dd MMM, yyyy";
    public static final String MMM_DD_YYYY = "MMM dd, yyyy";
    public static final String DD_MM_YYYY_HH_MM_A = "dd-MM-yyyy HH:mm a";
    public static final String HH_MM = "HH:mm";

    public static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat(YYYY_MM_DD);

    private static final Logger LOGGER = LoggerFactory.getLogger(DateUtil.class);

    public String getDateFormatted(String inputDate, String inputFormat, String outputFormat) {
        String formattedDate = null;
        try {
            formattedDate = format(parse(inputDate, inputFormat), outputFormat);
        } catch (Exception e) {
            LOGGER.error("Error while getting formatted date :", e);
        }

        return formattedDate;
    }

    public Date parse(String dateStr, String format) {
        Date date = null;
        try {
            date = fetchSimpleDateFormat(format).parse(dateStr);
        } catch (Exception e) {
            LOGGER.error("ERROR parsing date :: ", e);
        }

        return date;
    }

    public String format(Date inputDate, String dateFormat) {
        return fetchSimpleDateFormat(dateFormat).format(inputDate);
    }

    public SimpleDateFormat fetchSimpleDateFormat(String dateFormat) {
        return new SimpleDateFormat(dateFormat);
    }

    public int getDaysDiff(LocalDate start, LocalDate end) {
        return Math.toIntExact(Math.abs(ChronoUnit.DAYS.between(start, end)));
    }

    public int getDaysDiff(String start) throws ParseException {
        LocalDate checkInDate = simpleDateFormat.parse(start).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate now = LocalDate.now();
        return getDaysDiff(now, checkInDate);
    }

    public int getDaysDiff(String start, String end) throws ParseException {
        try {
            List<String> patterns = new ArrayList<>(Arrays.asList(
                    "yyyy-MM-dd",
                    "yyyy-MM-dd HH:mm:ss",
                    "dd-MM-yyyy HH:mm:ss",
                    "yyyy-MM-dd HH:mm",
                    "dd-MM-yyyy HH:mm",
                    "dd-MM-yyyy",
                    "dd/MM/yyyy",
                    "yyyy/MM/dd",
                    "dd/MM/yyyy hh:mm:ss",
                    "yyyy/MM/dd hh:mm:ss"
            ));

            for (String pattern : patterns) {
                try {
                    java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern(pattern);
                    LocalDate checkInDate = LocalDate.parse(start, formatter);
                    LocalDate checkOutDate = LocalDate.parse(end, formatter);
                    return getDaysDiff(checkInDate, checkOutDate);
                } catch (Exception e) {
                    continue;
                }
            }
        }catch (Exception e){
            LOGGER.debug("Not a valid date format to calculate los");
        }

        //default los
        return 1;
    }

    public String convertEpochToDateTime(long epoch, String pattern) {
        try {
            LOGGER.debug("Converting DateTime to epoch for epoch {} with pattern {}",epoch, pattern);
            DateTime tillDate = new DateTime(epoch);
            DateTimeFormatter formatter = DateTimeFormat.forPattern(pattern);
            String date =  tillDate.toString(formatter);
            LOGGER.debug("Date after converting from epoch {} ",date);
            return date;
        }  catch(Exception ex) {
            LOGGER.error("Error in converting epoch to DateTime {}",ex.getStackTrace());
            LOGGER.debug("Error in converting epoch to DateTime for {} and pattern {}",epoch,pattern);
        }
        return null;
    }

    public long getEpochTimeFromDateString(String dateString, String format) {
        Date date = parse(dateString, format);
        return date != null ? date.getTime() : 0;
    }

    /**
     * This Method will be used to find the duration timeLine slot for requestTime.
     * If the search time is X:00 pm we will check the in which timeline this X time is present based on the config provided by product i.e-
     * "05:00 - 07:00": "earlyMorningTime",
     * "07:00 - 18:00": "dayTime",
     * "18:00 - 20:00": "earlyNightTime",
     * "20:00 - 23:59": "lateNightTime",
     * "00:00  - 05:00": "lateNightTime"
     */
    public String findDurationTimeSlot(Map<String, String> rtbTimeLineMap) throws ParseException {
        Date currentTime = new Date();
        DateFormat dateFormat = new SimpleDateFormat(HH_MM);
        if (MapUtils.isNotEmpty(rtbTimeLineMap)) {
            for (Map.Entry<String, String> entry : rtbTimeLineMap.entrySet()) {
                String rangeString = entry.getKey();
                String[] times = rangeString.split(DASH_SEPARATOR);

                //Start Time
                Date startTime = new SimpleDateFormat(HH_MM).parse(times[0]);
                Calendar startTimeCalendar = Calendar.getInstance();
                startTimeCalendar.setTime(startTime);

                //Current Time
                Date checkTime = new SimpleDateFormat(HH_MM).parse(dateFormat.format(currentTime));
                Calendar currentTimeCalendar = Calendar.getInstance();
                currentTimeCalendar.setTime(checkTime);

                //End Time
                Date endTime = new SimpleDateFormat(HH_MM).parse(times[1]);
                Calendar endTimeCalendar = Calendar.getInstance();
                endTimeCalendar.setTime(endTime);

                java.util.Date actualTime = currentTimeCalendar.getTime();
                if ((actualTime.after(startTimeCalendar.getTime()) ||
                        actualTime.compareTo(startTimeCalendar.getTime()) == 0) &&
                        actualTime.before(endTimeCalendar.getTime())) {
                    return entry.getValue();
                }
            }
        }
        return StringUtils.EMPTY;
    }

    public int getNoOfNights(Date start, Date end, String funnelSource) {
        DateTime dt1 = new DateTime(start);
        DateTime dt2 = new DateTime(end);
        int days = Days.daysBetween(dt1, dt2).getDays();
        if (days == 0 && FUNNEL_DAYUSE.equalsIgnoreCase(funnelSource)) {
            return 1;
        }
        return days;
    }

    public static String parseDateStringForParticularFormat(String dateStr, String inputFormatStr, String outputFormatStr) {
        try {
            SimpleDateFormat inputFormat = new SimpleDateFormat(inputFormatStr, Locale.ENGLISH);
            SimpleDateFormat outputFormat = new SimpleDateFormat(outputFormatStr, Locale.ENGLISH);
            Date date = inputFormat.parse(dateStr);
            return outputFormat.format(date);
        } catch (ParseException e) {
            LOGGER.error("Error while parsing date: " + dateStr, e);
            return null;
        }
    }

    public long convertToTimestamp(String dateTimeString) {
        DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        try {
            DateTime dateTime = formatter.parseDateTime(dateTimeString);
            return dateTime.getMillis();
        } catch (Exception e) {
            LOGGER.debug("Invalid date-time format. Expected format is yyyy-MM-dd HH:mm:ss : {}", e.getMessage());
        }
        return 0l;
    }

    public String getDateString(String inputDate) {
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat outputFormat = new SimpleDateFormat("dd MMM");
        try {
            Date date = inputFormat.parse(inputDate);
            return outputFormat.format(date);
        } catch (ParseException e) {
            LOGGER.error("Error is parsing date in getDateString: {}, {}",inputDate, e.getMessage());
        }
        return Constants.SPACE;
    }

    public String concatDate(String startDate, String endDate) {
        SimpleDateFormat inputFormat = new SimpleDateFormat(YYYY_MM_DD);
        SimpleDateFormat outputFormat = new SimpleDateFormat("EEE, dd MMM");
        try {
            Date start = inputFormat.parse(startDate);
            String outDate =  outputFormat.format(start);
            Date end = inputFormat.parse(endDate);
            return outDate + " - " + outputFormat.format(end);
        } catch (ParseException e) {
            LOGGER.error("Error is parsing date in concatDate: {}, {}, {}",startDate, endDate, e.getMessage());
        }
        return "";
    }

    public String changeDateFormat(String inputDate, String inputFormatStr, String outputFormatStr) {
        SimpleDateFormat inputFormat = new SimpleDateFormat(inputFormatStr);
        SimpleDateFormat outputFormat = new SimpleDateFormat(outputFormatStr);
        Date date = null;
        try {
            date = inputFormat.parse(inputDate);
            return outputFormat.format(date);
        } catch (ParseException e) {
            LOGGER.warn("dateParsing Issue: {}",e.getMessage(), e);
        }
        return StringUtils.EMPTY;
    }
    public String dayOfWeek(String dateStr) {
        if (StringUtils.isNotEmpty(dateStr)) {
            try {
                java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern(YYYY_MM_DD);
                LocalDate localDate = LocalDate.parse(dateStr,  formatter);
                DayOfWeek dayOfWeek = localDate.getDayOfWeek();
                return dayOfWeek.getDisplayName(TextStyle.SHORT, Locale.ENGLISH);
            } catch (Exception ex) {
                LOGGER.warn("Error while preparing day of week {}", ex.getMessage());
            }
        }
        return EMPTY_STRING;
    }

    public LocalDate getLocalDate(String date, String dateFormat) {
        if (StringUtils.isNotEmpty(date) && StringUtils.isNotEmpty(dateFormat)) {
            return LocalDate.parse(date, java.time.format.DateTimeFormatter.ofPattern(dateFormat));
        }
        return null;
    }

    public LocalDate getLocalDate(Date date) {
        if (date != null) {
            return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDate();
        }
        return null;
    }

    public boolean isSameDay(String d1, String d2) {
        return StringUtils.equalsIgnoreCase(d1, d2);
    }
}
