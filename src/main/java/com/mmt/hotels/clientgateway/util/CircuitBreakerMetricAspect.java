package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.constants.MetricConstant;
import com.mmt.lib.MetricManager;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Circuit Breaker Metrics Integration with existing MetricAspect
 * Provides circuit breaker state and performance metrics
 */
@Component
public class CircuitBreakerMetricAspect {

    private static final Logger logger = LoggerFactory.getLogger(CircuitBreakerMetricAspect.class);

    @Autowired
    private MetricManager metricManager;

    @Autowired
    private CircuitBreakerRegistry circuitBreakerRegistry;

    @Value("${pool.name:MAIN}")
    private String poolName;

    private static final String CIRCUIT_BREAKER_METRIC_TYPE = "CIRCUIT_BREAKER";
    private static final String CIRCUIT_BREAKER_STATE_METRIC = "CIRCUIT_BREAKER_STATE";
    private static final String CIRCUIT_BREAKER_PERFORMANCE_METRIC = "CIRCUIT_BREAKER_PERFORMANCE";

    /**
     * Logs circuit breaker state change events
     */
    public void logCircuitBreakerStateChange(String circuitBreakerName, 
                                           CircuitBreaker.State fromState, 
                                           CircuitBreaker.State toState) {
        try {
            Map<String, String> tags = generateCircuitBreakerStateTags(circuitBreakerName, fromState.toString(), toState.toString());
            metricManager.logCounter(tags, 1);
            
            logger.warn("Circuit breaker state change logged: {} from {} to {}",
                       circuitBreakerName, fromState, toState);
        } catch (Exception e) {
            logger.error("Error logging circuit breaker state change metric", e);
        }
    }

    /**
     * Logs circuit breaker failure rate exceeded events
     */
    public void logCircuitBreakerFailureRateExceeded(String circuitBreakerName, float failureRate) {
        try {
            Map<String, String> tags = generateCircuitBreakerFailureTags(circuitBreakerName, failureRate);
            metricManager.logCounter(tags, 1);
            
            logger.warn("Circuit breaker failure rate exceeded logged: {} with rate {}%",
                       circuitBreakerName, failureRate);
        } catch (Exception e) {
            logger.error("Error logging circuit breaker failure rate metric", e);
        }
    }

    /**
     * Logs circuit breaker call execution time
     */
    public void logCircuitBreakerCallTime(String circuitBreakerName, long executionTime, boolean successful) {
        try {
            Map<String, String> tags = generateCircuitBreakerCallTags(circuitBreakerName, successful);
            metricManager.logTimer(tags, executionTime, TimeUnit.MILLISECONDS);
            
            logger.warn("Circuit breaker call time logged: {} execution time {}ms, successful: {}",
                        circuitBreakerName, executionTime, successful);
        } catch (Exception e) {
            logger.error("Error logging circuit breaker call time metric", e);
        }
    }

    /**
     * Logs circuit breaker fallback execution
     */
    public void logCircuitBreakerFallbackExecution(String circuitBreakerName, String fallbackType) {
        try {
            Map<String, String> tags = generateCircuitBreakerFallbackTags(circuitBreakerName, fallbackType);
            metricManager.logCounter(tags, 1);
            
            logger.warn("Circuit breaker fallback execution logged: {} fallback type {}",
                       circuitBreakerName, fallbackType);
        } catch (Exception e) {
            logger.error("Error logging circuit breaker fallback metric", e);
        }
    }

    /**
     * Scheduled method to periodically log circuit breaker metrics
     */
    @Scheduled(fixedRate = 60000) // Every minute
    public void logPeriodicCircuitBreakerMetrics() {
        try {
            // Log metrics for all registered circuit breakers
            circuitBreakerRegistry.getAllCircuitBreakers().forEach(circuitBreaker -> {
                logCircuitBreakerPerformanceMetrics(circuitBreaker);
            });
        } catch (Exception e) {
            logger.error("Error in periodic circuit breaker metrics logging", e);
        }
    }

    /**
     * Logs detailed performance metrics for a circuit breaker
     */
    private void logCircuitBreakerPerformanceMetrics(CircuitBreaker circuitBreaker) {
        try {
            String circuitBreakerName = circuitBreaker.getName();
            CircuitBreaker.Metrics metrics = circuitBreaker.getMetrics();
            
            // Log current state as counter (since logGauge is not available)
            Map<String, String> stateTags = generateCircuitBreakerPerformanceTags(circuitBreakerName, "STATE");
            stateTags.put("state_value", String.valueOf(getStateValue(circuitBreaker.getState())));
            metricManager.logCounter(stateTags, 1);

            // Log failure rate as counter
            Map<String, String> failureRateTags = generateCircuitBreakerPerformanceTags(circuitBreakerName, "FAILURE_RATE");
            failureRateTags.put("failure_rate", String.valueOf(metrics.getFailureRate()));
            metricManager.logCounter(failureRateTags, 1);

            // Log slow call rate as counter
            Map<String, String> slowCallRateTags = generateCircuitBreakerPerformanceTags(circuitBreakerName, "SLOW_CALL_RATE");
            slowCallRateTags.put("slow_call_rate", String.valueOf(metrics.getSlowCallRate()));
            metricManager.logCounter(slowCallRateTags, 1);

            // Log number of buffered calls as counter (available in 1.7.1)
            try {
                Map<String, String> callsTags = generateCircuitBreakerPerformanceTags(circuitBreakerName, "NUMBER_OF_BUFFERED_CALLS");
                metricManager.logCounter(callsTags, metrics.getNumberOfBufferedCalls());
            } catch (Exception e) {
                logger.debug("Could not log number of buffered calls metric for {}: {}", circuitBreakerName, e.getMessage());
            }

            // Log number of failed calls as counter
            try {
                Map<String, String> failedCallsTags = generateCircuitBreakerPerformanceTags(circuitBreakerName, "FAILED_CALLS");
                metricManager.logCounter(failedCallsTags, metrics.getNumberOfFailedCalls());
            } catch (Exception e) {
                logger.debug("Could not log failed calls metric for {}: {}", circuitBreakerName, e.getMessage());
            }

            // Log number of successful calls as counter
            try {
                Map<String, String> successfulCallsTags = generateCircuitBreakerPerformanceTags(circuitBreakerName, "SUCCESSFUL_CALLS");
                metricManager.logCounter(successfulCallsTags, metrics.getNumberOfSuccessfulCalls());
            } catch (Exception e) {
                logger.debug("Could not log successful calls metric for {}: {}", circuitBreakerName, e.getMessage());
            }
            
        } catch (Exception e) {
            logger.error("Error logging performance metrics for circuit breaker: {}", circuitBreaker.getName(), e);
        }
    }

    /**
     * Converts circuit breaker state to numeric value for metrics
     */
    private double getStateValue(CircuitBreaker.State state) {
        switch (state) {
            case CLOSED:
                return 0.0;
            case OPEN:
                return 1.0;
            case HALF_OPEN:
                return 0.5;
            case DISABLED:
                return -1.0;
            case FORCED_OPEN:
                return 2.0;
            default:
                return -2.0;
        }
    }

    /**
     * Generates metric tags for circuit breaker state changes
     */
    private Map<String, String> generateCircuitBreakerStateTags(String circuitBreakerName, String fromState, String toState) {
        Map<String, String> tags = new HashMap<>();
        tags.put(MetricConstant.METRIC_TYPE, CIRCUIT_BREAKER_STATE_METRIC);
        tags.put("circuit_breaker_name", circuitBreakerName);
        tags.put("from_state", fromState);
        tags.put("to_state", toState);
        tags.put("pool_name", poolName);
        tags.put(MetricConstant.CONTROLLER_TAG, getControllerFromMDC());
        tags.put(MetricConstant.TAG_DEVICE, getDeviceFromMDC());
        return tags;
    }

    /**
     * Generates metric tags for circuit breaker failure rate events
     */
    private Map<String, String> generateCircuitBreakerFailureTags(String circuitBreakerName, float failureRate) {
        Map<String, String> tags = new HashMap<>();
        tags.put(MetricConstant.METRIC_TYPE, CIRCUIT_BREAKER_METRIC_TYPE);
        tags.put("circuit_breaker_name", circuitBreakerName);
        tags.put("event_type", "FAILURE_RATE_EXCEEDED");
        tags.put("failure_rate", String.valueOf(failureRate));
        tags.put("pool_name", poolName);
        tags.put(MetricConstant.CONTROLLER_TAG, getControllerFromMDC());
        tags.put(MetricConstant.TAG_DEVICE, getDeviceFromMDC());
        return tags;
    }

    /**
     * Generates metric tags for circuit breaker call execution
     */
    private Map<String, String> generateCircuitBreakerCallTags(String circuitBreakerName, boolean successful) {
        Map<String, String> tags = new HashMap<>();
        tags.put(MetricConstant.METRIC_TYPE, CIRCUIT_BREAKER_METRIC_TYPE);
        tags.put("circuit_breaker_name", circuitBreakerName);
        tags.put("call_result", successful ? "SUCCESS" : "FAILURE");
        tags.put("pool_name", poolName);
        tags.put(MetricConstant.CONTROLLER_TAG, getControllerFromMDC());
        tags.put(MetricConstant.TAG_DEVICE, getDeviceFromMDC());
        return tags;
    }

    /**
     * Generates metric tags for circuit breaker fallback execution
     */
    private Map<String, String> generateCircuitBreakerFallbackTags(String circuitBreakerName, String fallbackType) {
        Map<String, String> tags = new HashMap<>();
        tags.put(MetricConstant.METRIC_TYPE, CIRCUIT_BREAKER_METRIC_TYPE);
        tags.put("circuit_breaker_name", circuitBreakerName);
        tags.put("event_type", "FALLBACK_EXECUTED");
        tags.put("fallback_type", fallbackType);
        tags.put("pool_name", poolName);
        tags.put(MetricConstant.CONTROLLER_TAG, getControllerFromMDC());
        tags.put(MetricConstant.TAG_DEVICE, getDeviceFromMDC());
        return tags;
    }

    /**
     * Generates metric tags for circuit breaker performance metrics
     */
    private Map<String, String> generateCircuitBreakerPerformanceTags(String circuitBreakerName, String metricName) {
        Map<String, String> tags = new HashMap<>();
        tags.put(MetricConstant.METRIC_TYPE, CIRCUIT_BREAKER_PERFORMANCE_METRIC);
        tags.put("circuit_breaker_name", circuitBreakerName);
        tags.put("metric_name", metricName);
        tags.put("pool_name", poolName);
        return tags;
    }

    /**
     * Helper method to get controller from MDC
     */
    private String getControllerFromMDC() {
        String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
        return controller != null ? controller : "UNKNOWN";
    }

    /**
     * Helper method to get device from MDC
     */
    private String getDeviceFromMDC() {
        String device = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
        return device != null ? device : "UNKNOWN";
    }
}
