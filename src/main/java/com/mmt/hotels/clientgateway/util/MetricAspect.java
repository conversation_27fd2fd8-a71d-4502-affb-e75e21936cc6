package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.constants.MetricConstant;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.lib.MetricManager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
public class MetricAspect {

	private static final Logger LOGGER = LoggerFactory.getLogger(MetricAspect.class);

    private static final String B2C = "B2C";
	private static final String AE = "AE";
	private static final String GCC = "GCC";
    
    @Autowired
    private MetricManager metricManager;


    public void addToCounter(DependencyLayer dependencyLayer, ErrorType errorType, String errorCode) {

        try {
            String controller = getControllerRequestStringWoSpecialCharacters(MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue()));
            String bookingDevice = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
            HashMap<String, String> metricTagMap = generateMetricTagMap(dependencyLayer.toString(), errorType.toString(), errorCode, controller, bookingDevice, getOrgId());
            metricManager.logCounter(metricTagMap, 1);

        } catch (Exception e) {
            LOGGER.error("Error Occured Hin addToCounter=", e);
        }

    }


	private String getOrgId() {
		String orgId = B2C;
		String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
		if(region != null && Utility.isRegionGccOrKsa(region)){
			orgId = GCC;
		}else {
			String idContext = MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue());
			if(StringUtils.isNotBlank(idContext)){
				orgId = idContext;	
			}
		}
		return orgId;	
	}


    private HashMap<String, String> generateMetricTagMap(String dependencyLayer, String errorType, String errorCode, String controller, String bookingDevice, String orgId) {
        HashMap<String, String> metricTags = new HashMap<String, String>();

        metricTags.put(MetricConstant.DEPENDENCY_TAG, dependencyLayer);
        metricTags.put(MetricConstant.TAG_ERROR_TYPE, errorType);
        metricTags.put(MetricConstant.TAG_ERROR_CODE, errorCode);
        metricTags.put(MetricConstant.CONTROLLER_TAG, controller);
        metricTags.put(MetricConstant.TAG_DEVICE, bookingDevice);
        metricTags.put(MetricConstant.TAG_ORGID, StringUtils.isNotBlank(orgId) ? orgId :B2C);
        return metricTags;

    }

    private String getControllerRequestStringWoSpecialCharacters(String controller){
        String request = "";
        StringBuilder finalRequest = new StringBuilder(request);
        if(StringUtils.isBlank(controller))
            return  request;
        request =controller;
        if (request.contains("{")) {
            String[] arrRequest = request.split("\\{");
            finalRequest.append(arrRequest[0]);
            if (arrRequest.length > 1) {
                String[] arrReq2 = arrRequest[1].split("\\}");
                if (arrReq2.length > 1) {
                    finalRequest.append(arrReq2[0].toUpperCase()).append(arrReq2[1]);
                } else {
                    finalRequest.append(arrReq2[0]);
                }
            }
        } else {
            finalRequest.append(request);
        }
        return finalRequest.toString();
    }

    public void addToTime(String downstreamDependencyName, String apiName, long totalTime) {
        addToTime(downstreamDependencyName, apiName, totalTime, "");
    }

    public void addToTimeInternalProcess(String processName, String apiName, long totalTime) {
        try {
            if (StringUtils.isEmpty(processName) || totalTime <= 0) {
                return;
            }
            apiName = StringUtils.isNotBlank(apiName) ? apiName : "";
            Map<String, String> tags = new HashMap<>(3);
            tags.put(MetricConstant.PROCESS_TAG, processName);
            tags.put(MetricConstant.API_NAME, apiName);
            tags.put(MetricConstant.METRIC_TYPE, MetricConstant.PROCESS_METRIC_TYPE);
            metricManager.logTimer(tags, totalTime, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            LOGGER.error("Error while logging internal process time in metric", e);
        }
    }

    public void addToCounterCache(String language, String region, String metricType) {

        try {
            String controller = getControllerRequestStringWoSpecialCharacters(MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue()));
            String bookingDevice = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
            Map<String, String> metricTagMap = generateMetricTagsForCache(metricType, region, language, controller, bookingDevice);
            metricManager.logCounter(metricTagMap, 1);

        } catch (Exception e) {
            LOGGER.error("Error Occured in addToCounterCache: ", e);
        }

    }

    public void addToTime(String downstreamDependencyName, String apiName, long totalTime , String country) {
        try {
            if (downstreamDependencyName == null) {
                return;
            }
            if (totalTime > 0) {
                Map<String, String> tags = generateMetricTagsForTime(downstreamDependencyName, "TOTAL_TIME",apiName,country);
                metricManager.logTimer(tags, totalTime, TimeUnit.MILLISECONDS);
            }
        } catch (Exception e) {
            LOGGER.error("exception in addToTime method: ",e);
        }
    }

    public void addToTime(long totalTime , String apiName, String country, String region, String srcClient, String channel) {
        try {
            if (StringUtils.isBlank(apiName)) {
                return;
            }
            if (totalTime > 0) {
                Map<String, String> tags = generateMetricTagsForSLA(apiName, country, region, srcClient, channel);
                metricManager.logTimer(tags, totalTime, TimeUnit.MILLISECONDS);
            }
        } catch (Exception e) {
            LOGGER.error("exception in addToTime method: ",e);
        }
    }

    private Map<String, String> generateMetricTagsForSLA(String apiName, String country, String region, String srcClient, String channel) {
        Map<String, String> tags = new HashMap<>(5);
        if(StringUtils.isBlank(region))
            region = "UNKNOWN";
        if(StringUtils.isBlank(country))
            country = "UNKNOWN";
        if(StringUtils.isBlank(srcClient))
            srcClient = "UNKNOWN";
        if(StringUtils.isBlank(channel))
            channel = "UNKNOWN";
        tags.put(MetricConstant.TAG_REGION +"_" + MetricConstant.TAG_COUNTRY, region + "_"+ country);
        tags.put(MetricConstant.API_NAME, apiName);
        tags.put(MetricConstant.METRIC_TYPE, "TOTAL_TIME_SLA");
        tags.put(MetricConstant.TAG_DEVICE, srcClient);
        tags.put(MetricConstant.TAG_CHANNEL, channel);
        return tags;
    }

    private Map<String, String> generateMetricTagsForTime(String downstreamDependencyName, String metricType,String apiName,String country) {
        Map<String, String> tags = new HashMap<>(5);
        tags.put(MetricConstant.DEPENDENCY_TAG, downstreamDependencyName);
        tags.put(MetricConstant.API_NAME, apiName);
        tags.put(MetricConstant.METRIC_TYPE, metricType);
        tags.put(MetricConstant.TAG_COUNTRY, country);
        return tags;
    }

    private Map<String, String> generateMetricTagsForCache(String metricType, String region ,String language, String controller, String bookingDevice) {
        Map<String, String> tags = new HashMap<>(5);

        tags.put(MetricConstant.METRIC_TYPE, metricType);
        tags.put(MetricConstant.TAG_REGION, region);
        tags.put(MetricConstant.TAG_LANGUAGE, language);
        tags.put(MetricConstant.CONTROLLER_TAG, controller);
        tags.put(MetricConstant.TAG_DEVICE, bookingDevice);

        return tags;
    }

}
