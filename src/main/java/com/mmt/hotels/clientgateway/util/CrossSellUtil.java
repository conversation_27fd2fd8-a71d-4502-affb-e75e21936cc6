package com.mmt.hotels.clientgateway.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.searchHotels.*;
import com.mmt.hotels.clientgateway.restexecutors.CardEngineExecutor;
import com.mmt.hotels.clientgateway.service.CardEngineService;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.model.persuasion.response.Persuasion;
import com.mmt.hotels.model.persuasion.response.PersuasionData;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.response.landing.CardResponse;
import com.mmt.hotels.model.response.searchwrapper.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.CROSS_SELL_BENEFIT_COUNT_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.CROSS_SELL_BENEFIT_COUNT_TEXT_SINGULAR;

@Component
public class CrossSellUtil {

    @Autowired
    PolyglotService polyglotService;

    @Autowired
    private CommonConfigHelper commonConfigHelper;

    @Autowired
    private CardEngineService cardEngineService;

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    CardEngineExecutor cardEngineExecutor;

    private static final Logger logger = LoggerFactory.getLogger(CrossSellUtil.class);

    private Map<String, String> getCrossSellDataMap() {
        return commonConfigHelper.getCrossSellDataMap();
    }

    //Flights
    public CrossSellData getCrossSellData(boolean isFallBackCase, String bookingDevice, String cityName, SearchHotelsRequest searchHotelsRequest, Map<String,String> httpHeaderMap, @Nullable CommonModifierResponse commonModifierResponse) {
        CrossSellData crossSellData = new CrossSellData();
        crossSellData.setTitle(polyglotService.getTranslatedData(CROSS_SELL_TITLE_FT));
        crossSellData.setSubtitle(polyglotService.getTranslatedData(CROSS_SELL_SUBTITLE_FT));
        if (CLIENT_DESKTOP.equalsIgnoreCase(bookingDevice)) {
            crossSellData.setLogoUrl(getCrossSellDataMap().get(CROSS_SELL_LOGO_URL_DT));
        } else {
            crossSellData.setLogoUrl(getCrossSellDataMap().get(CROSS_SELL_LOGO_URL_APP));
        }
        crossSellData.setCoupon(getCrossSellCoupon());
        crossSellData.setAnimation(getCrossSellAnimation());
        List<String> bgGradientList = new ArrayList<>();
        bgGradientList.add(getCrossSellDataMap().get(BG_GRAD_COLOR_1));
        bgGradientList.add(getCrossSellDataMap().get(BG_GRAD_COLOR_2));
        bgGradientList.add(getCrossSellDataMap().get(BG_GRAD_COLOR_3));
        crossSellData.setBgGradient(bgGradientList);
        crossSellData.setButton(getCrossSellButton());
        crossSellData.setOffer(getCrossSellOffer(isFallBackCase, bookingDevice, cityName));
        try {
            String cardEngineServiceResponse = null;
            if (searchHotelsRequest != null) {
                searchHotelsRequest.setCardId(Constants.PERSUASION_CARD_ID);
                if(MapUtils.isEmpty(httpHeaderMap)){
                    httpHeaderMap = new HashMap<>();
                }
                cardEngineServiceResponse = cardEngineService.getCard(searchHotelsRequest, httpHeaderMap,commonModifierResponse);
            }
            crossSellData.setPersuasion(getCrossSellPersuasion(cardEngineServiceResponse));
        } catch (Exception ex) {
            logger.error("error occurred in getCrossSellPersuasion from Card engine: {}", ex.getMessage());
        }
        return crossSellData;
    }

    private CrossSellOffer getCrossSellOffer(boolean isFallBackCase, String bookingDevice, String cityName) {
        CrossSellOffer offer = new CrossSellOffer();
        offer.setFont(getCrossSellDataMap().get(FONT));
        offer.setText(polyglotService.getTranslatedData(CROSS_SELL_OFFER_TEXT_FT));
        offer.setIcon(getCrossSellDataMap().get(ICON));
        offer.setFooterDesc(polyglotService.getTranslatedData(CROSS_SELL_FOOTER_TEXT_FT));
        if (CLIENT_DESKTOP.equalsIgnoreCase(bookingDevice)) {
            offer.setImageUrl(getCrossSellDataMap().get(CROSS_SELL_FALLBACK_IMG_DT));
        } else {
            offer.setImageUrl(getCrossSellDataMap().get(CROSS_SELL_FALLBACK_IMG_APP));
        }
        offer.setSubText(StringUtils.isNotEmpty(polyglotService.getTranslatedData(CROSS_SELL_OFFER_SUBTEXT_FT)) ?
                polyglotService.getTranslatedData(CROSS_SELL_OFFER_SUBTEXT_FT).replace("{city}", cityName) : null);
        return offer;
    }

    private CrossSellButton getCrossSellButton() {
        CrossSellButton button = new CrossSellButton();
        button.setBgColor(getCrossSellDataMap().get(BTN_BG_COLOR));
        button.setClickText(polyglotService.getTranslatedData(CROSS_SELL_BUTTON_TEXT_FT));
        return button;
    }

    private CrossSellAnimation getCrossSellAnimation() {
        CrossSellAnimation animation = new CrossSellAnimation();
        animation.setLoopCount(Integer.valueOf(getCrossSellDataMap().get(LOOP_COUNT)));
        List<String> borderColorList = new ArrayList<>();
        borderColorList.add(getCrossSellDataMap().get(BORDER_COLOR_1));
        borderColorList.add(getCrossSellDataMap().get(BORDER_COLOR_2));
        borderColorList.add(getCrossSellDataMap().get(BORDER_COLOR_3));
        borderColorList.add(getCrossSellDataMap().get(BORDER_COLOR_4));
        borderColorList.add(getCrossSellDataMap().get(BORDER_COLOR_5));
        animation.setBorderColor(borderColorList);
        return animation;
    }

    private CrossSellCoupon getCrossSellCoupon() {
        CrossSellCoupon crossSellCoupon = new CrossSellCoupon();
        crossSellCoupon.setCode(polyglotService.getTranslatedData(CROSS_SELL_COUPON_CODE_FT));
        crossSellCoupon.setDelay(Integer.valueOf(getCrossSellDataMap().get(ANIMATION_DELAY)));
        crossSellCoupon.setCornerColor(getCrossSellDataMap().get(CORNER_COLOR));
        crossSellCoupon.setOverlayColor(getCrossSellDataMap().get(OVERLAY_COLOR));
        return crossSellCoupon;
    }

    public boolean isCrossSellRequest(SearchHotelsRequest searchHotelsRequest) {
        return searchHotelsRequest.getRequestDetails() != null && searchHotelsRequest.getRequestDetails().getTrafficSource() != null
                && StringUtils.isNotEmpty(searchHotelsRequest.getRequestDetails().getTrafficSource().getSource())
                && CROSS_SELL_FLIGHT_REQUEST.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getTrafficSource().getSource());
    }

    //Commons - Homepage, TY
    public CrossSellDataHES getCrossSellDataScion(String cityName, SearchWrapperInputRequest searchWrapperInputRequest, boolean isFallBackCase) {
        CrossSellDataHES crossSellData = new CrossSellDataHES();
        String pageContext = searchWrapperInputRequest.getCommonPageContext();
        if (COMMONS_LANDING_PAGECONTEXT.equalsIgnoreCase(pageContext) || PAGE_CONTEXT_LISTING.equalsIgnoreCase(pageContext)) {
            crossSellData.setTitle(polyglotService.getTranslatedData(CROSS_SELL_TITLE_HP));
            crossSellData.setSubtitle(polyglotService.getTranslatedData(CROSS_SELL_SUBTITLE_HP));
        } else {
            crossSellData.setTitle(polyglotService.getTranslatedData(CROSS_SELL_TITLE_TP));
            crossSellData.setSubtitle(polyglotService.getTranslatedData(CROSS_SELL_SUBTITLE_TP));
        }
        if (CLIENT_DESKTOP.equalsIgnoreCase(searchWrapperInputRequest.getDeviceType())) {
            crossSellData.setLogoUrl(getCrossSellDataMap().get(CROSS_SELL_LOGO_URL_DT));
        } else {
            crossSellData.setLogoUrl(getCrossSellDataMap().get(CROSS_SELL_LOGO_URL_APP));
        }
        crossSellData.setCoupon(getCrossSellDataCoupon(pageContext));
        crossSellData.setAnimation(getCrossSellDataAnimation());
        List<String> bgGradientList = new ArrayList<>();
        bgGradientList.add(getCrossSellDataMap().get(BG_GRAD_COLOR_1));
        bgGradientList.add(getCrossSellDataMap().get(BG_GRAD_COLOR_2));
        bgGradientList.add(getCrossSellDataMap().get(BG_GRAD_COLOR_3));
        crossSellData.setBgGradient(bgGradientList);
        crossSellData.setButton(getCrossSellDataButton(pageContext));
        crossSellData.setOffer(getCrossSellDataOffer(isFallBackCase, searchWrapperInputRequest.getDeviceType(), cityName, pageContext));
        try {
            searchWrapperInputRequest.setCardId(Constants.PERSUASION_CARD_ID);
            String cardEngineServiceResponse = cardEngineExecutor.getCard(searchWrapperInputRequest);
            crossSellData.setPersuasion(getCrossSellPersuasion(cardEngineServiceResponse));
        } catch (Exception ex) {
            logger.warn("error occurred in getCrossSellPersuasion from Card engine: {}", ex.getMessage());
        }
        return crossSellData;
    }

    public LoyaltyPersuasion modifyHotelPersuasions(SearchWrapperHotelEntityAbridged hotel){
        try {
            LoyaltyPersuasion persuasionData = null;
            if (hotel != null && hotel.getHotelPersuasions() != null) {
                Map<String, Object> persuasions = (Map<String, Object>) hotel.getHotelPersuasions();
                hotel.setHotelPersuasions(null);  // setting null as only Black persuasion is required. Remove when more persuasions are required
                for (Map.Entry<String, Object> entry : persuasions.entrySet()) {
                    if (SCION_PERSUASION_PLACEHOLDER.equalsIgnoreCase(entry.getKey()) && entry.getValue() != null) {
                        Persuasion loyaltyPersuasion = objectMapperUtil.getObjectFromJsonWithType(objectMapperUtil.getJsonFromObject(entry.getValue(), DependencyLayer.CLIENTGATEWAY), new TypeReference<Persuasion>() {}, DependencyLayer.CLIENTGATEWAY);
                        List<PersuasionData> loyaltyPersuasionData = loyaltyPersuasion!=null ? loyaltyPersuasion.getData() : null;
                        if(CollectionUtils.isNotEmpty(loyaltyPersuasionData)) {
                            String blackImageLogo = loyaltyPersuasion.getStyle() != null && StringUtils.isNotEmpty(loyaltyPersuasion.getStyle().getImageUrl()) ? loyaltyPersuasion.getStyle().getImageUrl() : null;
                            persuasionData = mapToLoyaltyPersuasion(loyaltyPersuasionData.get(0), loyaltyPersuasionData.size() - 1, blackImageLogo);
                        }
                    }
                }
            }
            return persuasionData;
        } catch (Exception e) {
            logger.error("error occurred in modifyHotelPersuasions: {}", e.getMessage());
        }
        return null;
    }
    private LoyaltyPersuasion mapToLoyaltyPersuasion(PersuasionData persuasionData, int blackBenefitCount, String blackImageLogo) {
        LoyaltyPersuasion loyaltyPersuasion = new LoyaltyPersuasion();
        loyaltyPersuasion.setLogo(blackImageLogo);
        loyaltyPersuasion.setBenefitIconUrl(persuasionData.getIconurl());
        loyaltyPersuasion.setBenefitText(persuasionData.getText());
        if(persuasionData.getStyle() != null) {
            loyaltyPersuasion.setBenefitTextColor(persuasionData.getStyle().getTextColor());
            loyaltyPersuasion.setBgColor(persuasionData.getStyle().getBgColor());
            loyaltyPersuasion.setBorderColor(persuasionData.getStyle().getBorderColor());
        }
        if(blackBenefitCount > 0) {
            String blackBenefitCountText = blackBenefitCount == 1 ? polyglotService.getTranslatedData(CROSS_SELL_BENEFIT_COUNT_TEXT_SINGULAR) : polyglotService.getTranslatedData(CROSS_SELL_BENEFIT_COUNT_TEXT);
            loyaltyPersuasion.setBenefitCountText(StringUtils.isNotEmpty(blackBenefitCountText) ? blackBenefitCountText.replace("{count}", String.valueOf(blackBenefitCount)):"");
            loyaltyPersuasion.setBenefitCountTextColor(persuasionData.getSubtextStyle()!=null?persuasionData.getSubtextStyle().getTextColor():null);
        }
        return loyaltyPersuasion;
    }

    private CrossSellPersuasion getCrossSellPersuasion(String cardEngineServiceResponse) {
        try {
            if (StringUtils.isEmpty(cardEngineServiceResponse)) {
                return null;
            }
            CardResponse cardResponse = objectMapperUtil.getObjectFromJson(cardEngineServiceResponse, CardResponse.class, DependencyLayer.CLIENTGATEWAY);
            if (cardResponse != null && cardResponse.getCardData() != null) {
                cardEngineServiceResponse = objectMapperUtil.getJsonFromObject(cardResponse.getCardData(), DependencyLayer.CLIENTGATEWAY);
                return objectMapperUtil.getObjectFromJson(cardEngineServiceResponse, CrossSellData.class, DependencyLayer.CLIENTGATEWAY).getPersuasion();
            }
        } catch (Exception e) {
            logger.error("error occurred in getCrossSellPersuasion: {}", e.getMessage());
        }
        return null;
    }

    private CrossSellDataCoupon getCrossSellDataCoupon(String pageContext) {
        CrossSellDataCoupon crossSellCoupon = new CrossSellDataCoupon();
        if (COMMONS_LANDING_PAGECONTEXT.equalsIgnoreCase(pageContext) || PAGE_CONTEXT_LISTING.equalsIgnoreCase(pageContext)) {
            crossSellCoupon.setCode(polyglotService.getTranslatedData(CROSS_SELL_COUPON_CODE_HP));
        } else {
            crossSellCoupon.setCode(polyglotService.getTranslatedData(CROSS_SELL_COUPON_CODE_TP));
        }
        crossSellCoupon.setDelay(Integer.valueOf(getCrossSellDataMap().get(ANIMATION_DELAY)));
        crossSellCoupon.setCornerColor(getCrossSellDataMap().get(CORNER_COLOR));
        crossSellCoupon.setOverlayColor(getCrossSellDataMap().get(OVERLAY_COLOR));
        return crossSellCoupon;
    }

    private CrossSellDataAnimation getCrossSellDataAnimation() {
        CrossSellDataAnimation animation = new CrossSellDataAnimation();
        animation.setLoopCount(Integer.valueOf(getCrossSellDataMap().get(LOOP_COUNT)));
        List<String> borderColorList = new ArrayList<>();
        borderColorList.add(getCrossSellDataMap().get(BORDER_COLOR_1));
        borderColorList.add(getCrossSellDataMap().get(BORDER_COLOR_2));
        borderColorList.add(getCrossSellDataMap().get(BORDER_COLOR_3));
        borderColorList.add(getCrossSellDataMap().get(BORDER_COLOR_4));
        borderColorList.add(getCrossSellDataMap().get(BORDER_COLOR_5));
        animation.setBorderColor(borderColorList);
        return animation;
    }

    private CrossSellDataButton getCrossSellDataButton(String pageContext) {
        CrossSellDataButton button = new CrossSellDataButton();
        button.setBgColor(getCrossSellDataMap().get(BTN_BG_COLOR));
        if (COMMONS_LANDING_PAGECONTEXT.equalsIgnoreCase(pageContext) || PAGE_CONTEXT_LISTING.equalsIgnoreCase(pageContext)) {
            button.setClickText(polyglotService.getTranslatedData(CROSS_SELL_BUTTON_TEXT_HP));
        } else {
            button.setClickText(polyglotService.getTranslatedData(CROSS_SELL_BUTTON_TEXT_TP));
        }
        return button;
    }

    private CrossSellDataOffer getCrossSellDataOffer(boolean isFallBackCase, String deviceType, String cityName, String pageContext) {
        CrossSellDataOffer offer = new CrossSellDataOffer();
        offer.setFont(getCrossSellDataMap().get(FONT));
        if (COMMONS_LANDING_PAGECONTEXT.equalsIgnoreCase(pageContext) || PAGE_CONTEXT_LISTING.equalsIgnoreCase(pageContext)) {
            offer.setText(polyglotService.getTranslatedData(CROSS_SELL_OFFER_TEXT_HP));
            offer.setFooterDesc(polyglotService.getTranslatedData(CROSS_SELL_FOOTER_TEXT_HP));
            offer.setSubText(polyglotService.getTranslatedData(CROSS_SELL_OFFER_SUBTEXT_HP) != null && cityName != null ?
                    polyglotService.getTranslatedData(CROSS_SELL_OFFER_SUBTEXT_HP).replace("{city}", cityName) : null);
        } else {
            offer.setText(polyglotService.getTranslatedData(CROSS_SELL_OFFER_TEXT_TP));
            offer.setFooterDesc(polyglotService.getTranslatedData(CROSS_SELL_FOOTER_TEXT_TP));
            offer.setSubText(polyglotService.getTranslatedData(CROSS_SELL_OFFER_SUBTEXT_TP) != null && cityName != null ?
                    polyglotService.getTranslatedData(CROSS_SELL_OFFER_SUBTEXT_TP).replace("{city}", cityName) : null);
        }
        if (isFallBackCase) {
            offer.setIcon(getCrossSellDataMap().get(ICON));
            if (CLIENT_DESKTOP.equalsIgnoreCase(deviceType)) {
                offer.setImageUrl(getCrossSellDataMap().get(CROSS_SELL_FALLBACK_IMG_DT));
            } else {
                offer.setImageUrl(getCrossSellDataMap().get(CROSS_SELL_FALLBACK_IMG_APP));
            }
        }
        return offer;
    }
}
