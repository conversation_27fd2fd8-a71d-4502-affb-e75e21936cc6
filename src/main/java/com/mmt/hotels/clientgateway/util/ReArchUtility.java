package com.mmt.hotels.clientgateway.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.gommt.hotels.orchestrator.detail.model.response.common.DisplayItem;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityAttribute;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenitySubAttribute;
import com.gommt.hotels.orchestrator.detail.model.response.content.common.PersuasionData;
import com.gommt.hotels.orchestrator.detail.model.response.content.media.RoomEntity;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.ArrangementInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.Space;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.RatingData;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.response.AmenitiesTag;
import com.mmt.hotels.clientgateway.response.BhfPersuasion;
import com.mmt.hotels.clientgateway.response.HighlightedAmenity;
import com.mmt.hotels.clientgateway.response.SelectRoomAmenities;
import com.mmt.hotels.clientgateway.response.SelectRoomFacility;
import com.mmt.hotels.clientgateway.response.rooms.SharedInfo;
import com.mmt.hotels.clientgateway.response.staticdetail.ComparatorResponse;
import com.mmt.hotels.model.request.upsell.UpsellAmenityDetail;
import com.mmt.model.UGCRatingData;
import com.mmt.model.util.RatingDetail;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.mmt.hotels.clientgateway.constants.Constants.EMPTY_STRING;
import static com.mmt.hotels.clientgateway.constants.Constants.HIGH_BHF;
import static com.mmt.hotels.clientgateway.constants.Constants.LBI;
import static com.mmt.hotels.clientgateway.constants.Constants.POPULAR;
import static java.lang.Math.max;
import static java.util.Comparator.comparingInt;

@Component
public class ReArchUtility extends Utility {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReArchUtility.class);

    private static final String SPACE = " ";
    private static final String AMP = "&";
    private static final String COMMA_SPACE = ", ";
    private static final String EXTRA = "extra";
    private static final String AVAILABLE = "AVAILABLE";
    private static final String PIPE_SEPARATOR = " | ";
    private static final String PIPE_SEPARATOR_WITH_BACKSLASH = "\\|";
    private static final String OCCUPANCY_PARAMETER = "{{OCCUPANCY}}";

    @Value("${loved.by.indians.icon.url}")
    private String lovedByIndiansIconUrl;

    @Value("${value.stays.icon.new.detail.page}")
    private String valueStaysIconNewDetailPage;

    @Value("${bhf.persuasion.intervention.config}")
    protected String bhfPersuasionInterventionConfig;

    protected Map<String, Map<String, String>> bhfPersuasionInterventionMap;

    @Value("${bhf.persuasion.style.config}")
    protected String bhfPersuasionStyleConfig;

    protected Map<String, Map<String, String>> bhfPersuasionStyleConfigMap;

    @PostConstruct
    public void init() {
        // Call parent initialization first to ensure all parent fields are initialized
        super.init();
        
        try {
            chatbotInfoConsul = commonConfigConsul.getChatbotInfo();
            chatBotInfoConsulV2 = commonConfigConsul.getChatbotInfoV2();
            chatbotInfoMediaV2 = commonConfigConsul.getChatbotInfoMediaV2();
            bhfPersuasionStyleConfigMap = objectMapperUtil.getObjectFromJsonWithType(bhfPersuasionStyleConfig, new TypeReference<Map<String, Map<String, String>>>() {}, DependencyLayer.CLIENTGATEWAY);
            bhfPersuasionInterventionMap = objectMapperUtil.getObjectFromJsonWithType(bhfPersuasionInterventionConfig, new TypeReference<Map<String, Map<String, String>>>() {}, DependencyLayer.CLIENTGATEWAY);
        } catch (JsonParseException e) {
            LOGGER.error("Exception while parsing json config : {}", e.getMessage());
        }
    }

    /**
     * Build SharedInfo from DisplayItem
     */
    public SharedInfo buildSharedInfo(DisplayItem hesSharedInfo) {
        if (hesSharedInfo == null)
            return null;
        SharedInfo sharedInfo = new SharedInfo();
        sharedInfo.setIconUrl(hesSharedInfo.getIconUrl());
        sharedInfo.setInfoText(hesSharedInfo.getText());
        return sharedInfo;
    }

    /**
     * Build space inclusion list
     */
    public List<String> buildSpaceInclusion(Space hesSpace) {
        StringBuilder responseString = new StringBuilder();
        if (hesSpace != null) {
            if (hesSpace.getSleepingDetails() != null) {
                if (hesSpace.getSleepingDetails().getBedInfo() != null) {
                    for (ArrangementInfo bedsInfo : hesSpace.getSleepingDetails().getBedInfo()) {
                        if (StringUtils.isNotEmpty(responseString.toString())) {
                            responseString.append(SPACE).append(AMP).append(SPACE).append(bedsInfo.getType());
                        } else {
                            responseString.append(bedsInfo.getCount()).append(SPACE).append(bedsInfo.getType());
                        }
                    }
                }
                if (hesSpace.getSleepingDetails().getExtraBedInfo() != null) {
                    for (ArrangementInfo bedsInfo : hesSpace.getSleepingDetails().getExtraBedInfo()) {
                        responseString.append(COMMA_SPACE).append(EXTRA).append(SPACE).append(bedsInfo.getCount()).append(SPACE).append(bedsInfo.getType()).append(SPACE).append(AVAILABLE.toLowerCase());
                    }
                }
            }
            if (StringUtils.isNotEmpty(responseString.toString()) && StringUtils.isNotEmpty(hesSpace.getDescriptionText())) {
                responseString.append(PIPE_SEPARATOR);
            }
            if (StringUtils.isNotEmpty(hesSpace.getDescriptionText())) {
                responseString.append(hesSpace.getDescriptionText());
            }
        }
        return StringUtils.isNotEmpty(responseString.toString()) ? Arrays.asList(responseString.toString().split(PIPE_SEPARATOR_WITH_BACKSLASH)) : null;
    }


    /**
     * Get space data V2
     */
    public com.mmt.hotels.clientgateway.response.rooms.SpaceData getSpaceDataV2(SpaceData hesSpaceData, boolean isPrivateSpace, Set< com.mmt.hotels.clientgateway.response.rooms.Space> spacesList) {
        if (hesSpaceData == null)
            return null;

        com.mmt.hotels.clientgateway.response.rooms.SpaceData cgSpaceData = new com.mmt.hotels.clientgateway.response.rooms.SpaceData();
        int extraBedCount = 0, totalBaseOccupancy = 0, totalMaxOccupancy = 0;
        
        if (hesSpaceData != null) {
            cgSpaceData.setDescriptive(hesSpaceData.getDescriptive());
            cgSpaceData.setSharedInfo(buildSharedInfo(hesSpaceData.getDisplayItem()));
            List<com.mmt.hotels.clientgateway.response.rooms.Space> spaceList = new ArrayList<>();
            
            for (Space hesSpace : hesSpaceData.getSpaces()) {
                com.mmt.hotels.clientgateway.response.rooms.Space cgSpace = new com.mmt.hotels.clientgateway.response.rooms.Space();
                cgSpace.setName(hesSpace.getName());
                cgSpace.setSpaceId(hesSpace.getSpaceId());
                cgSpace.setSpaceType(hesSpace.getSpaceType());
                cgSpace.setAreaText(hesSpace.getAreaText());
                cgSpace.setSpaceInclusions(buildSpaceInclusion(hesSpace));
                
                if (isPrivateSpace) {
                    cgSpace.setAreaText(null);
                } else {
                    cgSpace.setDescriptionText(hesSpace.getDescriptionText());
                    String subText = hesSpace.getSubText();
                    if (hesSpace.getSpaceType() != null && (hesSpace.getSpaceType().equalsIgnoreCase(Constants.BEDROOM) || hesSpace.getSpaceType().equalsIgnoreCase(Constants.LIVING_ROOM))) {
                        int finalOccupancy = hesSpace.getFinalOccupancy();
                        int occupancy = max(finalOccupancy, hesSpace.getBaseOccupancy());
                        if (occupancy > 0)
                            subText = (occupancy > 1) ? polyglotService.getTranslatedData("SPACE_OCCUPANCY_TEXT") : polyglotService.getTranslatedData("SPACE_SINGLE_OCCUPANCY_TEXT");
                        else
                            subText = null;
                        if (subText != null) {
                            subText = subText.replace(OCCUPANCY_PARAMETER, String.valueOf(occupancy));
                        }
                    }
                    cgSpace.setSubText(subText);
                }
                
                if (CollectionUtils.isNotEmpty(hesSpace.getMedia())) {
                    List<com.mmt.hotels.clientgateway.response.rooms.MediaData> mediaDataList = new ArrayList<>();
                    for (RoomEntity hesMediaData : hesSpace.getMedia()) {
                        com.mmt.hotels.clientgateway.response.rooms.MediaData cgMediaData = new com.mmt.hotels.clientgateway.response.rooms.MediaData();
                        cgMediaData.setMediaType(hesMediaData.getCategory());
                        cgMediaData.setUrl(hesMediaData.getUrl());
                        mediaDataList.add(cgMediaData);
                    }
                    cgSpace.setMedia(mediaDataList);
                }
                spaceList.add(cgSpace);
            }
            
            if (CollectionUtils.isNotEmpty(spaceList)) {
                spacesList.addAll(spaceList);
            }
            cgSpaceData.setSpaces(spaceList);
        }
        
        cgSpaceData.setBaseGuests(totalBaseOccupancy);
        cgSpaceData.setExtraBeds(extraBedCount);
        cgSpaceData.setMaxGuests(totalMaxOccupancy);
        return cgSpaceData;
    }

    public static com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo buildRuleTableInfo(com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleTableInfo ruleTableInfoCB){
        if (ruleTableInfoCB == null || CollectionUtils.isEmpty(ruleTableInfoCB.getInfoList())) {
            return null;
        }

        List<com.mmt.hotels.clientgateway.response.staticdetail.RuleInfo> ruleInfoListCg = new ArrayList<>();
        ruleTableInfoCB.getInfoList()
                .stream().filter(ruleInfo -> StringUtils.isNotEmpty(ruleInfo.getKey()) && CollectionUtils.isNotEmpty(ruleInfo.getValue()))
                .forEach(ruleInfo -> {
                    com.mmt.hotels.clientgateway.response.staticdetail.RuleInfo ruleInfoCg = new com.mmt.hotels.clientgateway.response.staticdetail.RuleInfo();
                    ruleInfoCg.setKey(ruleInfo.getKey());
                    ruleInfoCg.setValue(ruleInfo.getValue());
                    ruleInfoListCg.add(ruleInfoCg);
                });

        com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo ruleTableInfoCg = new  com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo();
        ruleTableInfoCg.setKeyTitle(StringUtils.isBlank(ruleTableInfoCB.getKeyTitle()) ? EMPTY_STRING : ruleTableInfoCB.getKeyTitle());
        ruleTableInfoCg.setValueTitle(StringUtils.isBlank(ruleTableInfoCB.getValueTitle()) ? EMPTY_STRING : ruleTableInfoCB.getValueTitle());
        ruleTableInfoCg.setInfoList(ruleInfoListCg);
        return ruleTableInfoCg;
    }

    public String getPriceColorForPriceDrop(com.gommt.hotels.orchestrator.detail.enums.PriceVariationType type) {
        String color = null;
        if (type == com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.DROP) {
            color = "#007E7D";
        } else if( type == com.gommt.hotels.orchestrator.detail.enums.PriceVariationType.SURGE) {
            color = "#CF8100";
        }
        return color;
    }

    /**
     * Get amenities
     */
    public List<SelectRoomAmenities> getAmenities(List<AmenityGroup> amenities, boolean isAmenitiesV2Enabled, int amenitiesRatingCardPos) {
        List<SelectRoomAmenities> amenitiesList = new ArrayList<>();
        int currentPos = 0;
        if (CollectionUtils.isNotEmpty(amenities)) {
            for (AmenityGroup fg : amenities) {
                ++currentPos;
                SelectRoomAmenities selectRoomAmenities = new SelectRoomAmenities();
                String id = fg.getId();
                if (id != null && (id.equalsIgnoreCase(LBI) || id.equalsIgnoreCase(POPULAR))) {
                    if (id.equalsIgnoreCase(LBI)) {
                        selectRoomAmenities.setIconUrl(lovedByIndiansIconUrl);
                    }
                    selectRoomAmenities.setMergeId(LBI + "_" + POPULAR);
                }
                boolean showGuestRating = currentPos == amenitiesRatingCardPos;
                selectRoomAmenities.setShowGuestRating(showGuestRating);
                selectRoomAmenities.setId(id);
                selectRoomAmenities.setName(fg.getName());
                selectRoomAmenities.setPillTitle(fg.getName());

                if (StringUtils.isNotEmpty(fg.getAccessType())) {
                    AmenitiesTag tag = new AmenitiesTag();
                    tag.setId(fg.getAccessType());
                    tag.setText(fg.getAccessType());
                    tag.setColor(getColorBasedOnTag(fg.getAccessType()));
                    selectRoomAmenities.setAmenitySectionTag(tag);
                }

                if (CollectionUtils.isNotEmpty(fg.getAmenities())) {
                    selectRoomAmenities.setFacilities(processFacilities(fg.getAmenities(), false));
                }
                if (isAmenitiesV2Enabled && CollectionUtils.isNotEmpty(selectRoomAmenities.getFacilities()) && CollectionUtils.isNotEmpty(fg.getStrikeoutAmenities())) {
                    selectRoomAmenities.getFacilities().addAll(processFacilities(fg.getStrikeoutAmenities(), true));
                }
                selectRoomAmenities.setImages(fg.getImages());
                amenitiesList.add(selectRoomAmenities);
            }
        }
        return amenitiesList;
    }

    private List<SelectRoomFacility> processFacilities(List<Amenity> amenities, boolean isStrikeThrough) {
        List<SelectRoomFacility> selectRoomFacilities = new ArrayList<>();
        if (CollectionUtils.isEmpty(amenities)) {
            return selectRoomFacilities;
        }
        for (Amenity amenity : amenities) {
            SelectRoomFacility selectRoomFacility = new SelectRoomFacility();
            selectRoomFacility.setName(amenity.getName());
            selectRoomFacility.setIconUrl(amenity.getIconUrl());
            selectRoomFacility.setStrikeThrough(isStrikeThrough);
            if (CollectionUtils.isNotEmpty(amenity.getChildAttributes()) && StringUtils.isNotEmpty(amenity.getDisplayType())) {
                if ("1".equalsIgnoreCase(amenity.getDisplayType())) {
                    StringBuilder stringBuilder = new StringBuilder();
                    for (AmenityAttribute cf : amenity.getChildAttributes()) {
                        stringBuilder.append(cf.getName()).append(Constants.COMMA);
                    }
                    selectRoomFacility.setSubText(Constants.AMENITIES_OPEN_BRACE +
                            StringUtils.chop(stringBuilder.toString()) + Constants.AMENITIES_CLOSING_BRACE);
                }
                if ("2".equalsIgnoreCase(amenity.getDisplayType())) {
                    StringBuilder stringBuilder = new StringBuilder();
                    AmenityAttribute childAttribute = amenity.getChildAttributes().get(0);
                    stringBuilder.append(childAttribute.getName())
                            .append(Constants.SPACE)
                            .append(Constants.HYPEN)
                            .append(Constants.SPACE);
                    if (CollectionUtils.isNotEmpty(childAttribute.getSubAttributes())) {
                        for (AmenitySubAttribute subAttributeFacility : childAttribute.getSubAttributes()) {
                            stringBuilder.append(subAttributeFacility.getName()).append(Constants.COMMA);
                        }
                    }
                    selectRoomFacility.setSubText(Constants.AMENITIES_OPEN_BRACE +
                            StringUtils.chop(stringBuilder.toString()) + Constants.AMENITIES_CLOSING_BRACE);
                }
            }
            selectRoomFacilities.add(selectRoomFacility);
        }
        return selectRoomFacilities;
    }

    /**
     * Get highlighted amenities
     */
    public List<String> getHighlightedAmenities(List<AmenityGroup> highlightedAmenities) {
        List<String> hltAmenities = null;
        if (CollectionUtils.isNotEmpty(highlightedAmenities)) {
            hltAmenities = new ArrayList<>();
            for (AmenityGroup fg : highlightedAmenities) {
                if (CollectionUtils.isNotEmpty(fg.getAmenities())) {
                    for (Amenity amenity : fg.getAmenities()) {
                        hltAmenities.add(amenity.getName());
                    }
                }
            }
        }
        return hltAmenities;
    }

    /**
     * Get highlighted amenities V2
     */
    public List<HighlightedAmenity> getHighlightedAmenitiesV2(List<AmenityGroup> highlightedAmenities) {
        List<HighlightedAmenity> hltAmenities = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(highlightedAmenities)) {
            for (AmenityGroup fg : highlightedAmenities) {
                if (CollectionUtils.isNotEmpty(fg.getAmenities())) {
                    for (Amenity facility : fg.getAmenities()) {
                        HighlightedAmenity amenity = new HighlightedAmenity();
                        amenity.setTitle(facility.getName());
                        if(StringUtils.isNotEmpty(facility.getIconUrl())) {
                            amenity.setIconUrl(facility.getIconUrl());
                        }
                        hltAmenities.add(amenity);
                    }
                }
            }
        }
        return hltAmenities;
    }

    public List<com.mmt.hotels.clientgateway.response.FacilityGroup> buildAmenities(List<AmenityGroup> facilityWithGrp, List<AmenityGroup> starFacilities, List<AmenityGroup> highlightedFacilities) {
        if (CollectionUtils.isEmpty(facilityWithGrp))
            return null;
        List<com.mmt.hotels.clientgateway.response.FacilityGroup> amenitiesCGList = new ArrayList<>();

        Set<String> starAmenitySet = new HashSet<>();

        if (CollectionUtils.isNotEmpty(starFacilities)) {
            for (AmenityGroup facilityGroup : starFacilities) {
                if (CollectionUtils.isNotEmpty(facilityGroup.getAmenities())) {
                    for (Amenity facility : facilityGroup.getAmenities()) {
                        starAmenitySet.add(facility.getName());
                    }
                }

            }
        }

        List<com.mmt.hotels.clientgateway.response.Facility> starFacilityCGs = new ArrayList<>();
        for (AmenityGroup facilityGroup : facilityWithGrp) {
            com.mmt.hotels.clientgateway.response.FacilityGroup facility = new com.mmt.hotels.clientgateway.response.FacilityGroup();
            facility.setName(facilityGroup.getName());
            List<com.mmt.hotels.clientgateway.response.Facility> facilityCGs = new ArrayList<>();
            for (Amenity facilityHes : facilityGroup.getAmenities()) {

                com.mmt.hotels.clientgateway.response.Facility facilityCG = new com.mmt.hotels.clientgateway.response.Facility();
                facilityCG.setAttributeName(facilityHes.getAttributeName());
                facilityCG.setCategoryName(facilityHes.getCategoryName());
                facilityCG.setDisplayType(facilityHes.getDisplayType());
                facilityCG.setHighlightedName(facilityHes.getHighlightedName());
                facilityCG.setName(facilityHes.getName());
                facilityCG.setSequence(facilityHes.getSequence());
                facilityCG.setTags(facilityHes.getTags());
                facilityCG.setType(facilityHes.getType());
                if(facilityHes.getChildAttributes() != null) {
                    List<com.mmt.hotels.clientgateway.response.AttributesFacility> childAttributesCG = buildChildAttributesCgFromHes(facilityHes.getChildAttributes());
                    facilityCG.setChildAttributes(CollectionUtils.isNotEmpty(childAttributesCG) ? childAttributesCG : null);
                }

                if (starAmenitySet.contains(facilityHes.getName())) {
                    starAmenitySet.remove(facilityHes.getName());
                    starFacilityCGs.add(facilityCG);
                } else {
                    facilityCGs.add(facilityCG);
                }
            }

            if (CollectionUtils.isNotEmpty(facilityCGs)) {
                facility.setFacilities(facilityCGs);
                amenitiesCGList.add(facility);
            }
        }

        if (CollectionUtils.isNotEmpty(starFacilities) && !starAmenitySet.isEmpty()) {
            for (AmenityGroup facilityGroup : starFacilities) {
                for (Amenity facilityHes : facilityGroup.getAmenities()) {

                    if (!starAmenitySet.contains(facilityHes.getName())) {
                        continue;
                    }
                    com.mmt.hotels.clientgateway.response.Facility facilityCG = new com.mmt.hotels.clientgateway.response.Facility();
                    facilityCG.setAttributeName(facilityHes.getAttributeName());
                    facilityCG.setCategoryName(facilityHes.getCategoryName());
                    facilityCG.setDisplayType(facilityHes.getDisplayType());
                    facilityCG.setHighlightedName(facilityHes.getHighlightedName());
                    facilityCG.setName(facilityHes.getName());
                    facilityCG.setSequence(facilityHes.getSequence());
                    facilityCG.setTags(facilityHes.getTags());
                    facilityCG.setType(facilityHes.getType());
                    if(facilityHes.getChildAttributes() != null) {
                        List<com.mmt.hotels.clientgateway.response.AttributesFacility> childAttributesCG = buildChildAttributesCgFromHes(facilityHes.getChildAttributes());
                        facilityCG.setChildAttributes(CollectionUtils.isNotEmpty(childAttributesCG) ? childAttributesCG : null);
                    }
                    starFacilityCGs.add(facilityCG);
                }
            }

        }

        if (CollectionUtils.isNotEmpty(starFacilityCGs)) {
            Collections.sort(starFacilityCGs,comparingInt(starFacilityCG -> (starFacilityCG.getSequence() == null ? Integer.MAX_VALUE : starFacilityCG.getSequence())));
            com.mmt.hotels.clientgateway.response.FacilityGroup facility = new com.mmt.hotels.clientgateway.response.FacilityGroup();
            facility.setName(polyglotService.getTranslatedData(ConstantsTranslation.STAR_FACILITIES));
            facility.setType(Constants.BOLD_TYPE);
            facility.setFacilities(starFacilityCGs);
            amenitiesCGList.add(0, facility);
        }


        return amenitiesCGList;
    }

    public List<com.mmt.hotels.clientgateway.response.AttributesFacility> buildChildAttributesCgFromHes(List<AmenityAttribute> childAttributesHes) {

        List<com.mmt.hotels.clientgateway.response.AttributesFacility> childAttributesCG = new ArrayList<>();
        for(AmenityAttribute childAttributeHes : childAttributesHes) {
            com.mmt.hotels.clientgateway.response.AttributesFacility childAttributeCG = new com.mmt.hotels.clientgateway.response.AttributesFacility();
            BeanUtils.copyProperties(childAttributeHes, childAttributeCG);
            childAttributesCG.add(childAttributeCG);
        }
        return  childAttributesCG;
    }

    public UGCRatingData buildRatingData(RatingData ratingData, UGCRatingData ugcRatingData) {
        if (ratingData == null) {
            return null;
        }

        // Map title
        if (ratingData.getTitle() != null) {
            ugcRatingData.setTitle(ratingData.getTitle());
        }

        // Map subtitle
        if (ratingData.getSubTitle() != null) {
            ugcRatingData.setSubTitle(ratingData.getSubTitle());
        }

        // Map show icon flag
        ugcRatingData.setShowIcon(ratingData.isShowIcon());

        // Map summary if available
        if (ratingData.getSummary() != null) {
            RatingDetail summaryDetail = new RatingDetail();
            if (ratingData.getSummary().getText() != null) {
                summaryDetail.setText(ratingData.getSummary().getText());
            }
            // Set icon URL from rating data if available
            if (ratingData.getSummary().getIconUrl() != null) {
                summaryDetail.setIconUrl(ratingData.getSummary().getIconUrl());
            }
            ugcRatingData.setSummary(summaryDetail);
        }

        // Map highlights if available
        if (CollectionUtils.isNotEmpty(ratingData.getHighlights())) {
            List<RatingDetail> highlights = getRatingDetails(ratingData);
            ugcRatingData.setHighlights(highlights);
        }

        return ugcRatingData;
    }

    private static List<RatingDetail> getRatingDetails(RatingData ratingData) {
        List<RatingDetail> highlights = new ArrayList<>();
        for (DisplayItem highlight : ratingData.getHighlights()) {
            RatingDetail highlightDetail = new RatingDetail();
            if (StringUtils.isNotEmpty(highlight.getIconUrl())) highlightDetail.setIconUrl(highlight.getIconUrl());
            if (StringUtils.isNotEmpty(highlight.getTheme())) highlightDetail.setTheme(highlight.getTheme());
            if (StringUtils.isNotEmpty(highlight.getText())) {
                highlightDetail.setText(highlight.getText());

                // add highlightDetail in arrayList
                highlights.add(highlightDetail);
            }
        }
        return highlights;
    }

    public List<BhfPersuasion> buildBhfPersuasions(List<PersuasionData> bhfPersuasions, Set<String> categories) {
        if (MapUtils.isEmpty(bhfPersuasionStyleConfigMap) || MapUtils.isEmpty(bhfPersuasionInterventionMap)) {
            return null;
        }

        Map<String, String> interventionMap = categories.contains(HIGH_BHF) ? bhfPersuasionInterventionMap.get("bhfHighToInterventionMap") : bhfPersuasionInterventionMap.get("bhfFlowToInterventionMap");
        if (CollectionUtils.isNotEmpty(bhfPersuasions) && MapUtils.isNotEmpty(interventionMap)) {
            List<BhfPersuasion> bhfPersuasionList = new ArrayList<>();

            for (Map.Entry<String, String> entry : interventionMap.entrySet()) {
                if ("T".equalsIgnoreCase(entry.getValue())) {
                    Map<String, String> styles = bhfPersuasionStyleConfigMap.get(entry.getKey());
                    Optional<String> bhfText = bhfPersuasions.stream()
                            .filter(pd -> entry.getKey().equalsIgnoreCase(pd.getType()) && StringUtils.isNotEmpty(pd.getValue()))
                            .map(PersuasionData::getValue).findFirst();

                    if (!bhfText.isPresent() || StringUtils.isEmpty(bhfText.get()) || MapUtils.isEmpty(styles)) {
                        continue;
                    }

                    BhfPersuasion bhfPersuasion = new BhfPersuasion();
                    bhfPersuasion.setName(entry.getKey());
                    bhfPersuasion.setText(bhfText.get());

                    if (StringUtils.isNotEmpty(styles.get("bgColor")))
                        bhfPersuasion.setBgColor(styles.get("bgColor"));

                    if (StringUtils.isNotEmpty(styles.get("textColor")))
                        bhfPersuasion.setTextColor(styles.get("textColor"));

                    if ("Details_Blocker_BHF".equalsIgnoreCase(entry.getKey())) {
                        bhfPersuasion.setAdditionalText(polyglotService.getTranslatedData(ConstantsTranslation.BHF_BLOCKER_ADDITIONAL_TEXT));
                        bhfPersuasion.setLeftCTA(polyglotService.getTranslatedData(ConstantsTranslation.BHF_BLOCKER_LEFT_CTA));
                        bhfPersuasion.setRightCTA(polyglotService.getTranslatedData(ConstantsTranslation.BHF_BLOCKER_RIGHT_CTA));
                        bhfPersuasion.setHeading(polyglotService.getTranslatedData(ConstantsTranslation.BHF_BLOCKER_HEADING));

                        if (StringUtils.isNotEmpty(styles.get("additionalTextColor"))) {
                            bhfPersuasion.setAdditionalTextColor(styles.get("additionalTextColor"));
                        }
                    }
                    bhfPersuasionList.add(bhfPersuasion);
                }
            }

            return CollectionUtils.isEmpty(bhfPersuasionList) ? null : bhfPersuasionList;
        }
        return null;
    }

    /**
     * Builds hotel display map with headings for desktop comparator
     */
    public void buildHotelDisplayMap(ComparatorResponse comparatorResponse,
                                      com.gommt.hotels.orchestrator.detail.model.response.ComparatorResponse recommendedHotels,
                                      SearchHotelsRequest searchHotelsRequest) {

        // Desktop amenities sequence from config
        List<String> amenitiesSequence = Arrays.asList("DEAL", "PAH", "WIFI", "PT", "DT", "FC", "FB");

        Map<String, List<UpsellAmenityDetail>> hotelDetailMap = new HashMap<>();

        if (recommendedHotels != null && recommendedHotels.getPersonalizedSections() != null &&
                CollectionUtils.isNotEmpty(recommendedHotels.getPersonalizedSections().getHotels())) {

            List<com.gommt.hotels.orchestrator.model.response.listing.HotelDetails> hotels = recommendedHotels.getPersonalizedSections().getHotels();
            boolean isBudgetHotelPresent = hotels.stream().anyMatch(hotel -> hotel.getCategories() != null && hotel.getCategories().contains(Constants.MMT_VALUE_STAYS));

            for (com.gommt.hotels.orchestrator.model.response.listing.HotelDetails hotel : hotels) {
                List<UpsellAmenityDetail> upsellAmenityDetails = new ArrayList<>();

                // Handle budget hotel (Value Stays)
                if (isBudgetHotelPresent) {
                    UpsellAmenityDetail upsellAmenityDetail = new UpsellAmenityDetail();
                    if (hotel.getCategories() != null && hotel.getCategories().contains(Constants.MMT_VALUE_STAYS)) {
                        upsellAmenityDetail.setDisplayIcon(valueStaysIconNewDetailPage);
                        upsellAmenityDetail.setValue(true);
                    } else {
                        upsellAmenityDetail.setDisplayText(polyglotService.getTranslatedData(ConstantsTranslation.MMT_VALUESTAY_TITLE));
                        upsellAmenityDetail.setValue(false);
                    }
                    upsellAmenityDetails.add(upsellAmenityDetail);
                }

                // Process amenities sequence
                for (String amenity : amenitiesSequence) {
                    UpsellAmenityDetail upsellAmenityDetail = new UpsellAmenityDetail();
                    switch (amenity) {
                        case "FC":
                            upsellAmenityDetail.setDisplayText(polyglotService.getTranslatedData(ConstantsTranslation.FREE_CANCELLATION));
                            upsellAmenityDetail.setValue(hotel.isFreeCancellationAvailable());

                            if (!hotel.isFreeCancellationAvailable() && hotel.isFreeCancellation()) {
                                upsellAmenityDetail.setDisplayText(polyglotService.getTranslatedData(ConstantsTranslation.FREE_CANCELLATION_HIGHER_PRICE));
                                upsellAmenityDetail.setValue(hotel.isFreeCancellation());
                            }
                            break;
                        case "FB":
//                        upsellAmenityDetail.setDisplayText(polyglotService.getTranslatedData("CP_MEALPLAN_TEXT"));
//                        upsellAmenityDetail.setValue(searchWrapperHotelEntity.isBreakFast());
                            if (hotel.isBreakFastAvailable()) {
                                upsellAmenityDetail.setDisplayText(polyglotService.getTranslatedData(ConstantsTranslation.FREE_BREAKFAST_AT_HIGHER_PRICE));
                                upsellAmenityDetail.setValue(hotel.isBreakFastAvailable());
                            }
                            break;
//                        case "PAH":
//                            if (CollectionUtils.isNotEmpty(searchHotelsRequest.getFilterCriteria()) &&
//                                    searchHotelsRequest.getFilterCriteria().stream().anyMatch(fC -> fC.getFilterGroup().equals(FilterGroup.PAY_AT_HOTEL))) {
//                                upsellAmenityDetail.setDisplayText(polyglotService.getTranslatedData(TranslationConstants.PAH_AVAILABLE));
//                                upsellAmenityDetail.setValue(searchWrapperHotelEntity.getIsPAHAvailable());
//                                if (!searchWrapperHotelEntity.getIsPAHAvailable() && searchWrapperHotelEntity.getIsPAHTariffAvailable()) {
//                                    upsellAmenityDetail.setDisplayText(polyglotService.getTranslatedData(TranslationConstants.PAH_OTHER_TARIFF));
//                                    upsellAmenityDetail.setValue(searchWrapperHotelEntity.getIsPAHTariffAvailable());
//                                }
//                            }
//                            break;
                        case "WIFI":
                            if (CollectionUtils.isNotEmpty(searchHotelsRequest.getFilterCriteria()) &&
                                    searchHotelsRequest.getFilterCriteria().stream().anyMatch(fC -> Constants.FILTER_WI_FI.equalsIgnoreCase(fC.getFilterValue()))) {
                                upsellAmenityDetail.setDisplayText(polyglotService.getTranslatedData(ConstantsTranslation.FREE_WIFI));
                                upsellAmenityDetail.setValue(hotel.getPropertyFlags() != null && hotel.getPropertyFlags().isWifi());
                            }
                            break;
                        case "PT":
                            if (StringUtils.isNotEmpty(hotel.getPropertyType())) {
                                upsellAmenityDetail.setDisplayText(hotel.getPropertyType());
                                upsellAmenityDetail.setValue(true);
                            }
                            break;
                        case "DT":
                            if (CollectionUtils.isNotEmpty(hotel.getLocationPersuasions()) && hotel.getLocationPersuasions().size() > 1) {
                                upsellAmenityDetail.setDisplayText(hotel.getLocationPersuasions().get(1));
                                upsellAmenityDetail.setValue(true);
                            } else {
                                upsellAmenityDetail.setDisplayText("");
                                upsellAmenityDetail.setValue(true);
                            }
                            break;
                        default:
                            break;
                    }
                    if (upsellAmenityDetail.getDisplayText() != null) {
                        upsellAmenityDetails.add(upsellAmenityDetail);
                    }
                }
                hotelDetailMap.put(hotel.getId(), upsellAmenityDetails);
            }

            comparatorResponse.setHotelDisplayMap(hotelDetailMap);
        }
    }
} 