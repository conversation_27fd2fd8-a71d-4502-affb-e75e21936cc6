package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.enums.*;
import com.mmt.hotels.clientgateway.exception.AuthenticationException;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ValidationException;
import org.apache.commons.lang3.StringUtils;

public class CustomValidator {

	public static void validate(String tid, String client) throws ClientGatewayException {
		validateTransactionId(tid);
		validateClient(client);
	}
	
	private static void validateTransactionId(String tid) throws ValidationException {
		if (StringUtils.isEmpty(tid)) 
			throw new ValidationException(DependencyLayer.CLIENTS, ErrorType.VALIDATION,
					ValidationErrors.EMPTY_TID.getErrorCode(), ValidationErrors.EMPTY_TID.getErrorMsg());
	}
	
	private static void validateClient(String client) throws ClientGatewayException{
		if (StringUtils.isEmpty(client))
			throw new ValidationException(DependencyLayer.CLIENTS, ErrorType.VALIDATION, 
					ValidationErrors.EMPTY_CLIENT.getErrorCode(), ValidationErrors.EMPTY_CLIENT.getErrorMsg());
		for (Clients registeredClient: Clients.values()) {
			if (registeredClient.name().equals(client))
				return;
		}
		throw new AuthenticationException(DependencyLayer.CLIENTS, ErrorType.AUTHENTICATION, 
				AuthenticationErrors.INVALID_CLIENT.getErrorCode(), AuthenticationErrors.INVALID_CLIENT.getErrorMsg());
	}
}