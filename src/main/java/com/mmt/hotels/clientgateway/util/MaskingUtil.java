package com.mmt.hotels.clientgateway.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.propertymanager.config.PropertyManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.mmt.hotels.clientgateway.constants.Constants.EMPTY_STRING;

@Component
public class MaskingUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private static final Logger logger = LoggerFactory.getLogger(MaskingUtil.class);

    private boolean enableCustomMasking;

    @Autowired
    private PropertyManager propertyManager;


    // Define regex patterns for sensitive values
    private static final List<Pattern> MASK_PATTERNS = Arrays.asList(
            Pattern.compile("\"(name|firstName|lastName|customerName|gender|title|employeeId|panCard)\"\\s*:\\s*\"(.*?)\""), // Mask specific fields
            Pattern.compile("\"[^\"]*\"\\s*:\\s*\"(\\d{10,12})\""), // Generic phone number masking (10-12 digit numbers)
            Pattern.compile("\"[^\"]*\"\\s*:\\s*\"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})\"") // Generic email masking
    );

    public static String maskSensitiveDataAndLog(Object object) {
        String defaultString = EMPTY_STRING;
        try {
            long startTime = new Date().getTime();
            logger.warn("Masking is enabled");
            ObjectMapper objectMapper = new ObjectMapper();
            String json = object instanceof String ? (String) object : objectMapper.writeValueAsString(object);
            defaultString = json;

            for (Pattern pattern : MASK_PATTERNS) {
                Matcher matcher = pattern.matcher(json);
                StringBuffer maskedJson = new StringBuffer();

                while (matcher.find()) {
                    String key = matcher.group().split(":")[0]; // Extracts key (e.g., "name")
                    matcher.appendReplacement(maskedJson, key + ": \"*************\"");
                }
                matcher.appendTail(maskedJson);
                json = maskedJson.toString();
            }
            logger.warn("Time taken to mask data : {}", new Date().getTime() - startTime);
            return json;
        } catch (Exception e) {
            return defaultString;
        }
    }
}
