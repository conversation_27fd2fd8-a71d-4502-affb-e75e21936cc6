package com.mmt.hotels.clientgateway.util;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.HeaderConstants;
import com.mmt.hotels.model.request.B2BAuthInfo;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.ValidationErrors;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.listingmap.ListingMapResponse;
import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.thirdparty.request.UgcUpvoteRequest;
import com.mmt.hotels.util.Tuple;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.UUID;

import static com.mmt.hotels.clientgateway.constants.Constants.FUNNEL_SOURCE;
import static com.mmt.hotels.clientgateway.constants.Constants.SEARCH_ROOMS_END_POINT;

@Component
public class RequestHandler {
	
	@Autowired
	private MetricErrorLogger metricErrorLogger;
	
	private static final Logger logger = LoggerFactory.getLogger(RequestHandler.class);
	
	public void validatHeadersAndCreateMDC(HttpServletRequest httpServletRequest, String client, 
			BaseRequest baseRequest, String correlationKey) throws ClientGatewayException {
		String tid = httpServletRequest.getHeader("tid");
		logger.warn("tid received: {} client: {} correlationKey: {}", tid, client, correlationKey);
		try {
			CustomValidator.validate(tid, client);
			baseRequest.setCorrelationKey(correlationKey);
			baseRequest.setClient(client);
		} catch (Exception e) {
			logger.error("error occurred in validateHeadersAndCreateMDC: " + e.getMessage(), e);
			ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
			metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
			throw exceptionHandlerResponse.getClientGatewayException();
		}
	}

	public Tuple<String,Map<String,String>> handleCommonRequest(HttpServletRequest httpRequest, HttpServletResponse httpResponse, String correlationKey, String client , String idContext, String controller, BaseSearchRequest request){
		if (StringUtils.isEmpty(correlationKey)) {
			logger.debug("correlationKey is missing for : {}", MDCHelper.MDCKeys.CONTROLLER.getStringValue());
			correlationKey = UUID.randomUUID().toString();
		}
		Map<String,String> httpHeaderMap = HeadersUtil.getHeadersFromServletRequest(httpRequest);
		logger.debug("Headers are : {}", httpHeaderMap);
		String region = httpHeaderMap.containsKey(Constants.REGION) && StringUtils.isNotBlank(httpHeaderMap.get(Constants.REGION)) ? httpHeaderMap.get(Constants.REGION) : Constants.DEFAULT_SITE_DOMAIN;
		String language = httpHeaderMap.containsKey(Constants.LANGUAGE)  && StringUtils.isNotBlank(httpHeaderMap.get(Constants.LANGUAGE)) ? httpHeaderMap.get(Constants.LANGUAGE) : Constants.DEFAULT_LANGUAGE;
		String currency = httpHeaderMap.containsKey(Constants.CURRENCY)  && StringUtils.isNotBlank(httpHeaderMap.get(Constants.CURRENCY)) ? httpHeaderMap.get(Constants.CURRENCY) : Constants.DEFAULT_CUR_INR;
		String funnelSource = null;
		if (httpRequest.getParameterMap() != null && httpRequest.getParameterMap().get(FUNNEL_SOURCE) != null && httpRequest.getParameterMap().get(FUNNEL_SOURCE)[0] != null)
			funnelSource = httpRequest.getParameterMap().get(FUNNEL_SOURCE)[0];

		String country = "";
		if(SEARCH_ROOMS_END_POINT.equalsIgnoreCase(controller) && request instanceof SearchRoomsRequest && ((SearchRoomsRequest) request).getSearchCriteria()!=null) {
			country = ((SearchRoomsRequest) request).getSearchCriteria().getCountryCode();
		}
		MDCHelper.createMDC(client, null, correlationKey, region.toUpperCase(), language, currency.toUpperCase(), controller, idContext,
				request != null && request.getRequestDetails() != null && request.getRequestDetails().getTrafficSource() != null ?
						request.getRequestDetails().getTrafficSource().getType() : StringUtils.EMPTY, funnelSource, country);

		if (request != null && request.getRequestDetails() != null)
			request.getRequestDetails().setSiteDomain(region.toUpperCase());

		HeadersUtil.prepareHttpServletResponsefromMap(httpHeaderMap, httpResponse);
		httpResponse.addHeader(Constants.CORRELATIONKEY, correlationKey);
		return new Tuple<>(correlationKey,httpHeaderMap);
	}

	public Tuple<String,Map<String,String>> handleCommonRequest(HttpServletRequest httpRequest, HttpServletResponse httpResponse, String correlationKey, String client , String idContext, String controller){
		if (StringUtils.isEmpty(correlationKey)) {
			correlationKey = UUID.randomUUID().toString();
		}
		Map<String,String> httpHeaderMap = HeadersUtil.getHeadersFromServletRequest(httpRequest);
		String region = httpHeaderMap.containsKey(Constants.REGION) && StringUtils.isNotBlank(httpHeaderMap.get(Constants.REGION)) ? httpHeaderMap.get(Constants.REGION) : Constants.DEFAULT_SITE_DOMAIN;
		String language = httpHeaderMap.containsKey(Constants.LANGUAGE)  && StringUtils.isNotBlank(httpHeaderMap.get(Constants.LANGUAGE)) ? httpHeaderMap.get(Constants.LANGUAGE) : Constants.DEFAULT_LANGUAGE;
		String currency = httpHeaderMap.containsKey(Constants.CURRENCY)  && StringUtils.isNotBlank(httpHeaderMap.get(Constants.CURRENCY)) ? httpHeaderMap.get(Constants.CURRENCY) : Constants.DEFAULT_CUR_INR;

		MDCHelper.createMDC(client, null, correlationKey, region.toUpperCase(), language, currency.toUpperCase(), controller, idContext, StringUtils.EMPTY,null, null);

		HeadersUtil.prepareHttpServletResponsefromMap(httpHeaderMap, httpResponse);
		httpResponse.addHeader(Constants.CORRELATIONKEY, correlationKey);
		return new Tuple<>(correlationKey,httpHeaderMap);
	}

	/**
	 * @param listingMapRequest
	 * @return
	 * Below method is for additional /listing-map request validations
	 */
	public ResponseWrapper<ListingMapResponse> validateListingMapRequest(ListingMapRequest listingMapRequest) {

		//If map-details are not present for any funnel other than Shortstays return error response
		if (listingMapRequest != null
				&& !Constants.FUNNEL_SOURCE_SHORTSTAYS.equalsIgnoreCase(listingMapRequest.getRequestDetails().getFunnelSource())
				&& !Constants.FUNNEL_SOURCE_HOTELS.equalsIgnoreCase(listingMapRequest.getRequestDetails().getFunnelSource())
				&& listingMapRequest.getMapDetails() == null) {

			String correlationKey = MDC.get(MDCHelper.MDCKeys.CORRELATION.getStringValue());
			String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
			String funnelSource = listingMapRequest.getRequestDetails().getFunnelSource();
			
			logger.error("INVALID_REQUEST: Map details are required for listing-map request. FunnelSource: {}, CorrelationKey: {}, Client: {}", funnelSource, correlationKey, client);
			
			ResponseWrapper responseWrapper = new ResponseWrapper<>();
			responseWrapper.setError(new Error((Constants.ERR_CODE_API).concat(DependencyLayer.CLIENTGATEWAY.getCode()).concat(ErrorType.VALIDATION.getCode()).concat(ValidationErrors.INVALID_REQUEST.getErrorCode()), Constants.MAP_DETAILS_EMPTY_MSG));
			return responseWrapper;
		}
		return null;
	}

	public UgcUpvoteRequest createUpvoteRequest(UpvoteRequest upvoteRequest) {
		UgcUpvoteRequest ugcUpvoteRequest = new UgcUpvoteRequest();
		ugcUpvoteRequest.setUgcId(upvoteRequest.getReviewId());
		ugcUpvoteRequest.setUserAction(upvoteRequest.getStatus());
		ugcUpvoteRequest.setLob(upvoteRequest.getLob());
		ugcUpvoteRequest.setUuid(upvoteRequest.getMmtAuth());
		ugcUpvoteRequest.setOrg(upvoteRequest.getOrg());

		return ugcUpvoteRequest;
	}
	public String effectiveCorrelationKey(String requestId, String correlationKey) {
		if (!StringUtils.isEmpty(requestId)) {
			return requestId;
		}
		return correlationKey;
	}

	public void validateB2bHeadersAndCreateMDC(HttpServletRequest httpServletRequest, BaseRequest baseRequest) {
		try {
			Map<String, String> httpHeaderMap = HeadersUtil.getHeadersFromServletRequest(httpServletRequest);

			String partnerAuth = httpHeaderMap.get(HeaderConstants.X_GOMMT_B2B_PARTNER_AUTH);
			String partnerName = httpHeaderMap.get(HeaderConstants.X_GOMMT_B2B_PARTNER_NAME);

			if (StringUtils.isNotBlank(partnerAuth) || StringUtils.isNotBlank(partnerName)) {
				logger.debug("B2B headers found - partnerName: {}, partnerAuth: {}", 
					StringUtils.isNotBlank(partnerName) ? "present" : "missing",
					StringUtils.isNotBlank(partnerAuth) ? "present" : "missing");

				B2BAuthInfo b2bAuthInfo = new B2BAuthInfo();
				b2bAuthInfo.setPartnerName(partnerName);
				b2bAuthInfo.setPartnerAuthToken(partnerAuth);

				baseRequest.setB2bAuthInformation(b2bAuthInfo);

				MDC.put(MDCHelper.MDCKeys.B2B_PARTNER_NAME.getStringValue(), 
					StringUtils.isNotBlank(partnerName) ? partnerName : "");
				MDC.put(MDCHelper.MDCKeys.B2B_PARTNER_AUTH.getStringValue(), 
					StringUtils.isNotBlank(partnerAuth) ? "present" : ""); // Don't log actual auth token for security
				
				logger.info("B2B authentication information set for partner: {}", partnerName);
			} else {
				logger.debug("No B2B headers found in request");
			}
			
		} catch (Exception e) {
			logger.error("Error occurred while processing B2B headers: {}", e.getMessage(), e);
		}
	}
}