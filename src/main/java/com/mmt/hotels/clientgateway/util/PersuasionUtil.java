package com.mmt.hotels.clientgateway.util;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.PersuasionTemplate;
import com.mmt.hotels.clientgateway.response.BGGradient;
import com.mmt.hotels.clientgateway.response.HotelCategoryDataWeb;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.Style;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.model.enums.InclusionCategory;
import com.mmt.hotels.model.persuasion.response.Persuasion;
import com.mmt.hotels.model.persuasion.response.StyleResponseBO;
import com.mmt.hotels.model.response.emi.NoCostEmiDetails;
import com.mmt.hotels.model.response.mmtprime.BlackInfo;
import com.mmt.hotels.model.response.HotelBenefitInfo;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.HeroTierDetails;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.text.WordUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SBPP_VALUE_1_PERS_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SBPP_VALUE_2_PERS_TEXT;


@Component
public class PersuasionUtil {

    @Value("${hidden.gems.persuasion.style}")
    String hiddenGemPersuasionConfig;

    @Value("${flyer.exclusive.rates.persuasion.apps}")
    String flyerExclusiveRatesPersuasionConfigApps;

    @Value("${flyer.exclusive.rates.persuasion.DT}")
    String flyerExclusiveRatesPersuasionConfigDT;

    @Value("${flyer.exclusive.rates.persuasion.pwa}")
    String flyerExclusiveRatesPersuasionConfigPWA;

    @Value("${bus.exclusive.rates.persuasion.apps}")
    String busExclusiveRatesPersuasionConfigApps;

    @Value("${bus.exclusive.rates.persuasion.DT}")
    String busExclusiveRatesPersuasionConfigDT;

    @Value("${bus.exclusive.rates.persuasion.pwa}")
    String busExclusiveRatesPersuasionConfigPWA;

    @Value("${train.exclusive.rates.persuasion.apps}")
    String trainExclusiveRatesPersuasionConfigApps;

    @Value("${train.exclusive.rates.persuasion.DT}")
    String trainExclusiveRatesPersuasionConfigDT;

    @Value("${train.exclusive.rates.persuasion.pwa}")
    String trainExclusiveRatesPersuasionConfigPWA;

    @Value("${vistara.exclusive.rates.persuasion.apps}")
    String vistaraExclusiveRatesPersuasionConfigApps;

    @Value("${scarcity.price.bottom.persuasion.app}")
    String scarcityPriceBottomPersuasionAppsConfig;

    @Value("${vistara.exclusive.rates.persuasion.DT}")
    String vistaraExclusiveRatesPersuasionConfigDT;

    @Value("${vistara.exclusive.rates.persuasion.pwa}")
    String vistaraExclusiveRatesPersuasionConfigPWA;

    @Value("${flyer.deal.rates.persuasion.apps}")
    String flyerDealRatesPersuasionConfigApps;

    @Value("${flyer.deal.rates.persuasion.DT}")
    String flyerDealRatesPersuasionConfigDT;

    @Value("${flyer.deal.rates.persuasion.pwa}")
    String flyerDealRatesPersuasionConfigPWA;

    @Value("${gcc.meta.wallet.cashback.persuasion.apps}")
    String gccMetaWalletCashbackPersuasionConfigApps;

    @Value("${gcc.meta.wallet.cashback.persuasion.DT}")
    String gccMetaWalletCashbackPersuasionConfigDT;

    @Value("${gcc.meta.wallet.cashback.persuasion.pwa}")
    String gccMetaWalletCashbackPersuasionConfigPWA;

    @Value("${sale.campaign.persuasion.DT}")
    String saleCampaignPersuasionConfigDT;

    @Value("${sale.campaign.persuasion.apps}")
    String saleCampaignPersuasionConfigApps;

    @Value("${bnpl.review.page.persuasion.apps}")
    String bnplReviewPagePersuasionAppsConfig;

    @Value("${coupons.review.page.persuasion.apps}")
    String couponsReviewPagePersuasionAppsConfig;

    @Value("${exclusive.deal.persuasion.img.height}")
    private Integer imgHeight;

    @Value("${exclusive.deal.persuasion.img.width}")
    private Integer imgWidth;

    @Value("${mmt.black.mycash.persuasion.apps}")
    String mmtBlackMyCashPersuasionConfig;

    Persuasion mmtBlackMyCashPersuasion;

    @Value("${mmt.black.revamp.persuasion.apps}")
    String mmtBlackRevampPersuasionConfig;
    Persuasion mmtBlackRevampPersuasion;

    @Value("${hotelbenefit.persuasion.apps}")
    String hotelBenefitPersuasionConfig;
    Persuasion hotelBenefitPersuasion;

    @Value("${mmt.select.icon}")
    private String mmtSelectIcon;

    Map<String, Map<String, PersuasionData>> hiddenGemPersuasionConfigMap;

    Persuasion flyerExclusiveRatesPersuasionApps;

    Persuasion flyerExclusiveRatesPersuasionDT;

    Persuasion flyerExclusiveRatesPersuasionPWA;

    Persuasion busExclusiveRatesPersuasionApps;

    Persuasion busExclusiveRatesPersuasionDT;

    Persuasion busExclusiveRatesPersuasionPWA;

    Persuasion trainExclusiveRatesPersuasionApps;

    Persuasion trainExclusiveRatesPersuasionDT;

    Persuasion trainExclusiveRatesPersuasionPWA;

    Persuasion vistaraExclusiveRatesPersuasionApps;

    Persuasion scarcityPriceBottomPersuasionApps;

    Persuasion vistaraExclusiveRatesPersuasionDT;

    Persuasion vistaraExclusiveRatesPersuasionPWA;

    Persuasion flyerDealRatesPersuasionApps;

    Persuasion flyerDealRatesPersuasionDT;

    Persuasion flyerDealRatesPersuasionPWA;

    Persuasion gccMetaWalletCashbackPersuasionApps;

    Persuasion gccMetaWalletCashbackPersuasionDT;

    Persuasion gccMetaWalletCashbackPersuasionPWA;

    Persuasion noCostEmiApplicablePersuasion;

    Persuasion saleCampaignPersuasionApps;

    Persuasion saleCampaignPersuasionDT;

    Persuasion bnplReviewPagePersuasionApps = new Persuasion();

    Persuasion couponsReviewPagePersuasionsApps = new Persuasion();

    @Autowired
    ObjectMapperUtil objectMapperUtil;

    @Autowired
    protected PolyglotService polyglotService;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    static final String HIDDEN_GEM_STYLE = "hiddenGem";
    static final String HIDDEN_GEM_ICON_STYLE = "hiddenGemIcon";
    static final String HOME_STAY_TITLE_STYLE = "homeStayTitle";
    static final String HOME_STAY_SUB_TITLE_STYLE = "homeStaySubTitle";
    static final String PERSUASION_TEXT_PLACEHOLDER = "{PERSUASION_TEXT}";

    @Value("${old.persuasion.class.to.new}")
    private String oldPersuasionClassToNewConfig;

    Map<String,String> oldPersuasionClassToNewMap;
    private static final Logger logger = LoggerFactory.getLogger(PersuasionUtil.class);

    @PostConstruct
    public void init() {
        try {
            hiddenGemPersuasionConfigMap = new Gson().fromJson(hiddenGemPersuasionConfig, new TypeToken<Map<String, Map<String, PersuasionData>>>() {
            }.getType());
            flyerExclusiveRatesPersuasionApps = new Gson().fromJson(flyerExclusiveRatesPersuasionConfigApps, new TypeToken<Persuasion>() {
            }.getType());
            flyerExclusiveRatesPersuasionDT = new Gson().fromJson(flyerExclusiveRatesPersuasionConfigDT, new TypeToken<Persuasion>() {
            }.getType());
            flyerExclusiveRatesPersuasionPWA = new Gson().fromJson(flyerExclusiveRatesPersuasionConfigPWA, new TypeToken<Persuasion>() {
            }.getType());
            busExclusiveRatesPersuasionApps = new Gson().fromJson(busExclusiveRatesPersuasionConfigApps, new TypeToken<Persuasion>() {
            }.getType());
            busExclusiveRatesPersuasionDT = new Gson().fromJson(busExclusiveRatesPersuasionConfigDT, new TypeToken<Persuasion>() {
            }.getType());
            busExclusiveRatesPersuasionPWA = new Gson().fromJson(busExclusiveRatesPersuasionConfigPWA, new TypeToken<Persuasion>() {
            }.getType());
            trainExclusiveRatesPersuasionApps = new Gson().fromJson(trainExclusiveRatesPersuasionConfigApps, new TypeToken<Persuasion>() {
            }.getType());
            trainExclusiveRatesPersuasionDT = new Gson().fromJson(trainExclusiveRatesPersuasionConfigDT, new TypeToken<Persuasion>() {
            }.getType());
            trainExclusiveRatesPersuasionPWA = new Gson().fromJson(trainExclusiveRatesPersuasionConfigPWA, new TypeToken<Persuasion>() {
            }.getType());

            scarcityPriceBottomPersuasionApps = new Gson().fromJson(scarcityPriceBottomPersuasionAppsConfig, new TypeToken<Persuasion>() {
            }.getType());

            vistaraExclusiveRatesPersuasionApps = new Gson().fromJson(vistaraExclusiveRatesPersuasionConfigApps, new TypeToken<Persuasion>() {
            }.getType());
            vistaraExclusiveRatesPersuasionDT = new Gson().fromJson(vistaraExclusiveRatesPersuasionConfigDT, new TypeToken<Persuasion>() {
            }.getType());
            vistaraExclusiveRatesPersuasionPWA = new Gson().fromJson(vistaraExclusiveRatesPersuasionConfigPWA, new TypeToken<Persuasion>() {
            }.getType());

            flyerDealRatesPersuasionApps = new Gson().fromJson(flyerDealRatesPersuasionConfigApps, new TypeToken<Persuasion>() {
            }.getType());
            flyerDealRatesPersuasionDT = new Gson().fromJson(flyerDealRatesPersuasionConfigDT, new TypeToken<Persuasion>() {
            }.getType());
            flyerDealRatesPersuasionPWA = new Gson().fromJson(flyerDealRatesPersuasionConfigPWA, new TypeToken<Persuasion>() {
            }.getType());
            gccMetaWalletCashbackPersuasionApps = new Gson().fromJson(gccMetaWalletCashbackPersuasionConfigApps, new TypeToken<Persuasion>() {
            }.getType());
            gccMetaWalletCashbackPersuasionDT = new Gson().fromJson(gccMetaWalletCashbackPersuasionConfigDT, new TypeToken<Persuasion>() {
            }.getType());
            gccMetaWalletCashbackPersuasionPWA = new Gson().fromJson(gccMetaWalletCashbackPersuasionConfigPWA, new TypeToken<Persuasion>() {
            }.getType());
            mmtBlackMyCashPersuasion = new Gson().fromJson(mmtBlackMyCashPersuasionConfig, new TypeToken<Persuasion>() {
            }.getType());
            oldPersuasionClassToNewMap = new Gson().fromJson(oldPersuasionClassToNewConfig, new TypeToken<Map<String, String>>() {
            }.getType());
            mmtBlackRevampPersuasion = new Gson().fromJson(mmtBlackRevampPersuasionConfig, new TypeToken<Persuasion>() {
            }.getType());
            hotelBenefitPersuasion = new Gson().fromJson(hotelBenefitPersuasionConfig, new TypeToken<Persuasion>() {
            }.getType());
            saleCampaignPersuasionConfigDT = commonConfigConsul.getReviewPagePersuasion().get(SALE_CAMPAIGN_PERSUASION_DT);
            saleCampaignPersuasionDT = objectMapperUtil.getObjectFromJson(saleCampaignPersuasionConfigDT, Persuasion.class, DependencyLayer.CLIENTGATEWAY);

            saleCampaignPersuasionConfigApps = commonConfigConsul.getReviewPagePersuasion().get(Constants.SALE_CAMPAIGN_PERSUASION_APPS);
            saleCampaignPersuasionApps = objectMapperUtil.getObjectFromJson(saleCampaignPersuasionConfigApps, Persuasion.class, DependencyLayer.CLIENTGATEWAY);

            bnplReviewPagePersuasionApps = objectMapperUtil.getObjectFromJson(bnplReviewPagePersuasionAppsConfig, Persuasion.class, DependencyLayer.CLIENTGATEWAY);
            couponsReviewPagePersuasionsApps = objectMapperUtil.getObjectFromJson(couponsReviewPagePersuasionAppsConfig, Persuasion.class, DependencyLayer.CLIENTGATEWAY);
        } catch (Exception e) {
            logger.error("Exception while parsing persuasion from properties file:", e);
        }
    }

    /**
     * Method to build Hidden Gem Persuasion with Text,
     * Styling is configured in application-properties against each client,
     * and text is fetched from HES -> Hotstore (Hidden Gem USP)
     */
    public PersuasionObject buildHiddenGemPersuasion(SearchWrapperHotelEntity hotelEntity, String deviceType) {
        PersuasionObject persuasion = null;
        Map<String, PersuasionData> persuasionConfigMap = hiddenGemPersuasionConfigMap.get(deviceType);
        PersuasionData persuasionStyleConfig = MapUtils.isNotEmpty(persuasionConfigMap) ? persuasionConfigMap.get(HIDDEN_GEM_STYLE) : new PersuasionData();
        if (hotelEntity != null && StringUtils.isNotEmpty(hotelEntity.getHiddenGemPersuasionText()) && persuasionStyleConfig != null && persuasionStyleConfig.getStyle() != null) {
            persuasion = new PersuasionObject();
            persuasion.setData(new ArrayList<>());
            persuasion.setTemplate(PersuasionTemplate.MULTI_PERSUASION_V.name());
            PersuasionData persuasionData = new PersuasionData();
            //Building html tag for this persuasion
            String persuasionText = "<font color=\"" + persuasionStyleConfig.getStyle().getTextColor() + "\"><i>\"" + hotelEntity.getHiddenGemPersuasionText() + "\"</i> </font>";
            persuasionData.setText(persuasionText);
            persuasionData.setHasAction(false);
            persuasionData.setHtml(true);
            persuasionData.setIconurl(persuasionStyleConfig.getIconurl());
            persuasionData.setIcontype(persuasionStyleConfig.getIcontype());

            PersuasionStyle persuasionStyle = new PersuasionStyle();
            BeanUtils.copyProperties(persuasionStyleConfig.getStyle() != null ? persuasionStyleConfig.getStyle() : new PersuasionStyle(), persuasionStyle);
            persuasionData.setStyle(persuasionStyle);

            persuasion.getData().add(persuasionData);
        }
        return persuasion;
    }

    /**
     * This method will return persuasion for review page based on device type
     * Persuasion config is present in Properties
     *
     * @param clientType
     * @return
     */
    public Map<String, Persuasion> buildFlyerExclusiveRatesPersuasionForReviewPage(String clientType) {
        Map<String, Persuasion> persuasionMap = new HashMap<>();
        String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
        if(Utility.isRegionGccOrKsa(region)){
            if (ANDROID.equalsIgnoreCase(clientType) || DEVICE_IOS.equalsIgnoreCase(clientType)) {
                persuasionMap.put(flyerDealRatesPersuasionApps.getPlaceholder(), flyerDealRatesPersuasionApps);
            } else if (DEVICE_OS_DESKTOP.equalsIgnoreCase(clientType)) {
                persuasionMap.put(flyerDealRatesPersuasionDT.getPlaceholder(), flyerDealRatesPersuasionDT);
            } else {
                persuasionMap.put(flyerDealRatesPersuasionPWA.getPlaceholder(), flyerDealRatesPersuasionPWA);
            }
        }else {
            if (ANDROID.equalsIgnoreCase(clientType) || DEVICE_IOS.equalsIgnoreCase(clientType)) {
                persuasionMap.put(flyerExclusiveRatesPersuasionApps.getPlaceholder(), flyerExclusiveRatesPersuasionApps);
            } else if (DEVICE_OS_DESKTOP.equalsIgnoreCase(clientType)) {
                persuasionMap.put(flyerExclusiveRatesPersuasionDT.getPlaceholder(), flyerExclusiveRatesPersuasionDT);
            } else {
                persuasionMap.put(flyerExclusiveRatesPersuasionPWA.getPlaceholder(), flyerExclusiveRatesPersuasionPWA);
            }
        }
        return persuasionMap;
    }

    public Map<String, Persuasion> buildBusExclusiveRatesPersuasionForReviewPage(String clientType) {
        Map<String, Persuasion> persuasionMap = new HashMap<>();
        if (ANDROID.equalsIgnoreCase(clientType) || DEVICE_IOS.equalsIgnoreCase(clientType)) {
            persuasionMap.put(busExclusiveRatesPersuasionApps.getPlaceholder(), busExclusiveRatesPersuasionApps);
        } else if (DEVICE_OS_DESKTOP.equalsIgnoreCase(clientType)) {
            persuasionMap.put(busExclusiveRatesPersuasionDT.getPlaceholder(), busExclusiveRatesPersuasionDT);
        } else {
            persuasionMap.put(busExclusiveRatesPersuasionPWA.getPlaceholder(), busExclusiveRatesPersuasionPWA);
        }
        return persuasionMap;
    }

    public Map<String, Persuasion> buildTrainExclusiveRatesPersuasionForReviewPage(String clientType) {
        Map<String, Persuasion> persuasionMap = new HashMap<>();
        if (ANDROID.equalsIgnoreCase(clientType) || DEVICE_IOS.equalsIgnoreCase(clientType)) {
            persuasionMap.put(trainExclusiveRatesPersuasionApps.getPlaceholder(), trainExclusiveRatesPersuasionApps);
        } else if (DEVICE_OS_DESKTOP.equalsIgnoreCase(clientType)) {
            persuasionMap.put(trainExclusiveRatesPersuasionDT.getPlaceholder(), trainExclusiveRatesPersuasionDT);
        } else {
            persuasionMap.put(trainExclusiveRatesPersuasionPWA.getPlaceholder(), trainExclusiveRatesPersuasionPWA);
        }
        return persuasionMap;
    }

    public Map<String, Persuasion> buildVistaraExclusiveRatesPersuasionForReviewPage(String clientType) {
        Map<String, Persuasion> persuasionMap = new HashMap<>();
        if (ANDROID.equalsIgnoreCase(clientType) || DEVICE_IOS.equalsIgnoreCase(clientType)) {
            persuasionMap.put(vistaraExclusiveRatesPersuasionApps.getPlaceholder(), vistaraExclusiveRatesPersuasionApps);
        } else if (DEVICE_OS_DESKTOP.equalsIgnoreCase(clientType)) {
            persuasionMap.put(vistaraExclusiveRatesPersuasionDT.getPlaceholder(), vistaraExclusiveRatesPersuasionDT);
        } else {
            persuasionMap.put(vistaraExclusiveRatesPersuasionPWA.getPlaceholder(), vistaraExclusiveRatesPersuasionPWA);
        }
        return persuasionMap;
    }

    public Map<String, Persuasion> buildHomestayPersuasionForReviewPage(String price, String variant, String currency) {
        Map<String, Persuasion> persuasionMap = new HashMap<>();
        if (scarcityPriceBottomPersuasionApps != null &&
                scarcityPriceBottomPersuasionApps.getData() != null &&
                scarcityPriceBottomPersuasionApps.getData().size() > 1 &&
                scarcityPriceBottomPersuasionApps.getData().get(1) != null) {

            String persuasionText;
            if ("1".equalsIgnoreCase(variant)) {
                persuasionText = polyglotService.getTranslatedData(SBPP_VALUE_1_PERS_TEXT);
            } else if ("2".equalsIgnoreCase(variant)) {
                persuasionText = polyglotService.getTranslatedData(SBPP_VALUE_2_PERS_TEXT);
            } else {
                return persuasionMap; // Variant not recognized, return empty map
            }

            persuasionText = persuasionText.replace("{currency}", currency)
                    .replace("{price}", price);

            scarcityPriceBottomPersuasionApps.getData().get(1).setText(persuasionText);

            String placeholder = scarcityPriceBottomPersuasionApps.getPlaceholder();
            if (placeholder != null) {
                persuasionMap.put(placeholder, scarcityPriceBottomPersuasionApps);
            }
        }
        return persuasionMap;
    }


    public Map<String, Persuasion> buildSaleCampaignPersuasionForReviewPage(String saleCampaignIconUrl, String clientType) {
        if (StringUtils.isNoneEmpty(saleCampaignIconUrl, clientType)) {
            Optional<Persuasion> persuasionOptional = Optional.empty();
            if (DEVICE_OS_DESKTOP.equalsIgnoreCase(clientType)) {
                persuasionOptional = Optional.ofNullable(saleCampaignPersuasionDT);
            } else {
                persuasionOptional = Optional.ofNullable(saleCampaignPersuasionApps);
            }

            persuasionOptional.ifPresent(persuasion -> {
                persuasion.getData().get(0).setIconurl(saleCampaignIconUrl);
            });

            return persuasionOptional.map(persuasion -> Collections.singletonMap(persuasion.getPlaceholder(), persuasion))
                    .orElse(Collections.emptyMap());
        }
        return Collections.emptyMap();
    }

    public void buildHotelPersuasionOfExclusiveDealForDetail(SearchRoomsResponse searchRoomsResponse){
        Map<String,PersuasionData> hotelPersuasion= new HashMap<>();
        hotelPersuasion.put(EXCLUSIVE_DEAL_TAG, new PersuasionData());
        hotelPersuasion.get(EXCLUSIVE_DEAL_TAG).setIconurl(EXCLUSIVE_DEAL_IMAGE_URL);
        searchRoomsResponse.setHotelPersuasions(hotelPersuasion);
    }
    public void buildHotelPersuasionOfExclusiveDealForReviewAndThankyou(HotelResult hotelResult){
        HotelCategoryDataWeb hotelCategoryDataWeb = new HotelCategoryDataWeb();
        hotelCategoryDataWeb.setImageUrl(EXCLUSIVE_DEAL_IMAGE_URL);
        hotelCategoryDataWeb.setStyleClass(EXCLUSIVE_DEAL_STYLE_CLASS);
        hotelCategoryDataWeb.setImgHeight(imgHeight);
        hotelCategoryDataWeb.setImgWidth(imgWidth);
        if (hotelResult.getApplicableHotelCategoryDataWeb() == null)
            hotelResult.setApplicableHotelCategoryDataWeb(new LinkedHashMap<String, HotelCategoryDataWeb>());
        hotelResult.getApplicableHotelCategoryDataWeb().put(EXCLUSIVE_DEAL_TAG, hotelCategoryDataWeb);
    }

//    public Map<String, Persuasion> buildBNPLPersuasionForReviewPage(String clientType) {
//        Map<String, Persuasion> persuasionMap = new HashMap<>();
//        if (ANDROID.equalsIgnoreCase(clientType) || DEVICE_IOS.equalsIgnoreCase(clientType)) {
//            persuasionMap.put(bnplReviewPagePersuasionApps.getPlaceholder(), bnplReviewPagePersuasionApps);
//        }
//        return persuasionMap;
//    }

    public Map<String, Persuasion> buildCouponPersuasionsForReviewPage(String persuasionText) {
        Map<String, Persuasion> persuasionMap = new HashMap<>();
        Persuasion persuasion = new Persuasion();
        BeanUtils.copyProperties(couponsReviewPagePersuasionsApps, persuasion);
        if(CollectionUtils.isNotEmpty(persuasion.getData())) {
            persuasion.getData().forEach(persuasionData -> persuasionData.setText(persuasionData.getText().replace(PERSUASION_TEXT_PLACEHOLDER, persuasionText)));
        }
        persuasionMap.put(couponsReviewPagePersuasionsApps.getPlaceholder(), persuasion);
        return persuasionMap;
    }

    /**
     * Method to build Hidden Gem Icon Persuasion.
     * Styling is configured in application-properties against each client, and Hidden Gem boolean is decided on the basis of
     * category which is fetched from HES -> Hotstore (Hidden Gem USP).
     */
    public PersuasionObject buildHiddenGemIconPersuasion(SearchWrapperHotelEntity hotelEntity, String deviceType) {
        PersuasionObject persuasion = null;
        Map<String, PersuasionData> persuasionConfigMap = hiddenGemPersuasionConfigMap.get(deviceType);
        PersuasionData persuasionStyleConfig = MapUtils.isNotEmpty(persuasionConfigMap) ? persuasionConfigMap.get(HIDDEN_GEM_ICON_STYLE) : new PersuasionData();
        if (hotelEntity != null && hotelEntity.isHiddenGem() && persuasionStyleConfig != null && persuasionStyleConfig.getStyle() != null) {
            persuasion = new PersuasionObject();
            persuasion.setData(new ArrayList<>());
            persuasion.setTemplate(PersuasionTemplate.IMAGE_TEXT_H.name());
            PersuasionData persuasionData = new PersuasionData();
            persuasionData.setHasAction(false);
            persuasionData.setIconurl(persuasionStyleConfig.getIconurl());
            persuasionData.setIcontype(persuasionStyleConfig.getIcontype());

            PersuasionStyle persuasionStyle = new PersuasionStyle();
            BeanUtils.copyProperties(persuasionStyleConfig.getStyle() != null ? persuasionStyleConfig.getStyle() : new PersuasionStyle(), persuasionStyle);
            persuasionData.setStyle(persuasionStyle);

            persuasion.getData().add(persuasionData);
        }
        return persuasion;
    }

    /**
     * Method to build Home Stays Title Persuasion.
     * Styling is configured in application-properties against each client,
     * and Home Stays Details data is prepared in HES.
     */
    public PersuasionObject buildHomeStaysTitlePersuasion(SearchWrapperHotelEntity hotelEntity, String deviceType) {
        PersuasionObject persuasion = null;
        Map<String, PersuasionData> persuasionConfigMap = hiddenGemPersuasionConfigMap.get(deviceType);
        PersuasionData persuasionStyleConfig = MapUtils.isNotEmpty(persuasionConfigMap) ? persuasionConfigMap.get(HOME_STAY_TITLE_STYLE) : new PersuasionData();
        if (hotelEntity != null && hotelEntity.getHomeStayDetails() != null && StringUtils.isNotEmpty(hotelEntity.getHomeStayDetails().getStayType()) &&
                !Constants.STAY_TYPE_HOTEL.equalsIgnoreCase(hotelEntity.getHomeStayDetails().getStayType()) && !hotelEntity.isLastBooked() && persuasionStyleConfig != null && persuasionStyleConfig.getStyle() != null) {
            persuasion = new PersuasionObject();
            persuasion.setData(new ArrayList<>());
            persuasion.setTemplate(PersuasionTemplate.MULTI_PERSUASION_V.name());
            PersuasionData persuasionData = new PersuasionData();
            persuasionData.setPersuasionType(Constants.STAY_TYPE);
            persuasionData.setText(hotelEntity.getHomeStayDetails().getStayType());
            persuasionData.setHasAction(false);
            persuasionData.setHtml(true);

            PersuasionStyle persuasionStyle = new PersuasionStyle();
            PersuasionStyle topLevelStyle = new PersuasionStyle();
            BeanUtils.copyProperties(persuasionStyleConfig.getStyle() != null ? persuasionStyleConfig.getStyle() : new PersuasionStyle(), persuasionStyle);
            BeanUtils.copyProperties(persuasionStyleConfig.getTopLevelStyle() !=null ? persuasionStyleConfig.getTopLevelStyle() : new PersuasionStyle(), topLevelStyle);
            persuasionData.setStyle(persuasionStyle);
            persuasion.setStyle(topLevelStyle);

            persuasion.getData().add(persuasionData);
        }
        return persuasion;
    }

    /**
     * Method to build Home Stays Title Persuasion.
     * Styling is configured in application-properties against each client
     * and Home Stays Details data is prepared in HES.
     */
    public PersuasionObject buildHomeStaysSubTitlePersuasion(SearchWrapperHotelEntity hotelEntity, String deviceType) {
        PersuasionObject persuasion = null;
        Map<String, PersuasionData> persuasionConfigMap = hiddenGemPersuasionConfigMap.get(deviceType);
        PersuasionData persuasionStyleConfig = MapUtils.isNotEmpty(persuasionConfigMap) ? persuasionConfigMap.get(HOME_STAY_SUB_TITLE_STYLE) : new PersuasionData();
        if (hotelEntity != null && hotelEntity.getHomeStayDetails() != null && StringUtils.isNotEmpty(hotelEntity.getHomeStayDetails().getStayTypeInfo()) && persuasionStyleConfig != null && persuasionStyleConfig.getStyle() != null) {
            persuasion = new PersuasionObject();
            persuasion.setData(new ArrayList<>());
            persuasion.setTemplate(PersuasionTemplate.MULTI_PERSUASION_V.name());
            PersuasionData persuasionData = new PersuasionData();
            persuasionData.setPersuasionType(Constants.STAY_TYPE);
            persuasionData.setText(hotelEntity.getHomeStayDetails().getStayTypeInfo());
            persuasionData.setHasAction(false);
            persuasionData.setHtml(true);

            PersuasionStyle persuasionStyle = new PersuasionStyle();
            PersuasionStyle topLevelStyle = new PersuasionStyle();
            BeanUtils.copyProperties(persuasionStyleConfig.getStyle() != null ? persuasionStyleConfig.getStyle() : new PersuasionStyle(), persuasionStyle);
            BeanUtils.copyProperties(persuasionStyleConfig.getTopLevelStyle() !=null ? persuasionStyleConfig.getTopLevelStyle() : new PersuasionStyle(), topLevelStyle);
            persuasionData.setStyle(persuasionStyle);
            persuasion.setStyle(topLevelStyle);

            persuasion.getData().add(persuasionData);
        }
        return persuasion;
    }

    public Map<String, Persuasion> buildGccMetaWalletCashbackPersuasionForReviewPage(String clientType) {
        Map<String, Persuasion> persuasionMap = new HashMap<>();
        if (ANDROID.equalsIgnoreCase(clientType) || DEVICE_IOS.equalsIgnoreCase(clientType)) {
            persuasionMap.put(gccMetaWalletCashbackPersuasionApps.getPlaceholder(), gccMetaWalletCashbackPersuasionApps);
        } else if (DEVICE_OS_DESKTOP.equalsIgnoreCase(clientType)) {
            persuasionMap.put(gccMetaWalletCashbackPersuasionDT.getPlaceholder(), gccMetaWalletCashbackPersuasionDT);
        } else {
            persuasionMap.put(gccMetaWalletCashbackPersuasionPWA.getPlaceholder(), gccMetaWalletCashbackPersuasionPWA);
        }
        return persuasionMap;
    }

	public void addShortStayPeithoPersuasionToHotelPersuasion(Hotel hotel, String uspShortStayValue, List<String> hotelPersuasion) {
		
		if(StringUtils.isNotBlank(uspShortStayValue)) {
			
			//Peitho persuasion for ShortStay Funnel to Show in Map.
			if(CollectionUtils.isNotEmpty(hotelPersuasion)) {
				if (hotel.getHotelPersuasions() == null)
					hotel.setHotelPersuasions(new HashMap<String,Object>());
				PersuasionObject hotelMapPers = new PersuasionObject();
				hotelMapPers.setData(new ArrayList<>());
				hotelMapPers.setTemplate(Constants.IMAGE_TEXT_H);
				hotelMapPers.setPlaceholder(Constants.SHORTSTAYS_PERSUASION_PLACEHOLDER_ID_MAP);
				hotelMapPers.setTemplateType(Constants.SHORTSTAYS_PERSUASION_TEMPLATE_TYPE);
				PersuasionData hotelMapPersuasionData = new PersuasionData();
				hotelMapPersuasionData.setHasAction(false);
				hotelMapPersuasionData.setHtml(false);
				hotelMapPersuasionData.setId(Constants.SHORTSTAYS_PERSUASION_ID_MAP);
				hotelMapPersuasionData.setPersuasionType(Constants.SHORTSTAYS_PERSUASION_TYPE);
				hotelMapPersuasionData.setText(uspShortStayValue);
				hotelMapPersuasionData.setStyle(new PersuasionStyle());
				hotelMapPersuasionData.getStyle().setTextColor(Constants.SHORTSTAYS_STYLE_TEXT_COLOR_MAP);
				hotelMapPersuasionData.getStyle().setFontSize(Constants.SHORTSTAYS_STYLE_FONT);
				hotelMapPersuasionData.setMultiPersuasionPriority(0);
				hotelMapPers.getData().add(hotelMapPersuasionData);
				
				try {
					((Map<Object,Object>) hotel.getHotelPersuasions()).put(Constants.SHORTSTAYS_PERSUASION_PLACEHOLDER_ID_MAP,hotelMapPers);
				} catch (ClassCastException e) {
					logger.error("Hotel Persuasion could not be added for Map due to ClassCastException : {} ",e.getMessage());
				} catch (Exception e) {
					logger.error("Hotel Persuasion could not be added for Map due to : {} ",e.getMessage());
				}

			}
		}		
						
	}

    public void updatePersuasionsForDesktopAndPwa(Map<String, Map<String, Object>> persuasionMap){
        if(MapUtils.isEmpty(persuasionMap)){
            return;
        }
        try {
            for (Map.Entry<String, Map<String, Object>> entry : persuasionMap.entrySet()) {
                if (entry.getValue() != null && entry.getValue().get(DATA) != null) {
                    List<Map<String, Object>> data = (List<Map<String, Object>>) entry.getValue().get(DATA);
                    boolean isRemoveIconType = false;
                    for (Map<String, Object> element : data) {
                        Map<String, Object> styleObjectMap = (Map<String, Object>) element.get(STYLE);
                        List<String> styleClassesList = (MapUtils.isNotEmpty(styleObjectMap) && styleObjectMap.containsKey(STYLE_CLASSES)) ? (List<String>) styleObjectMap.get(STYLE_CLASSES) : new ArrayList<>();
                        isRemoveIconType = getAdditionalStyleClasses(styleClassesList);
                        styleObjectMap.put(STYLE_CLASSES, styleClassesList);
                        if (isRemoveIconType) {
                            element.remove(ICON_TYPE);
                        }
                        if (element.containsKey("persuasionType") && STAY_TYPE.equalsIgnoreCase(String.valueOf(element.get("persuasionType")))) {
                            element.replace("text", WordUtils.capitalize(String.valueOf(element.get("text")).toLowerCase()));
                        }
                    }
                    Map<String, Object> styleObjectMap = (Map<String, Object>) entry.getValue().get(STYLE);
                    if (MapUtils.isNotEmpty(styleObjectMap)) {
                        List<String> styleClassesList = (MapUtils.isNotEmpty(styleObjectMap) && styleObjectMap.containsKey(STYLE_CLASSES)) ? (List<String>) styleObjectMap.get(STYLE_CLASSES) : new ArrayList<>();
                        getAdditionalStyleClasses(styleClassesList);
                        styleObjectMap.put(STYLE_CLASSES, styleClassesList);
                    }
                }
            }
        } catch (Exception e){
            logger.error("Exception occured while updating Persuasion for Desktop: message: {}",e.getMessage(), e);
        }
    }

    private boolean getAdditionalStyleClasses(List<String> styleClassesList){
        boolean isRemoveIconType = false;
        List<String> additionalStyleClasses = new ArrayList<>();
        List<String> removeClasses = new ArrayList<>();
        for(String styleClass:styleClassesList){
            if(PC__PEITHO.equalsIgnoreCase(styleClass) || PC__CASHBACKDEAL.equalsIgnoreCase(styleClass)){
                isRemoveIconType = true;
            }
            if(MapUtils.isNotEmpty(oldPersuasionClassToNewMap) && oldPersuasionClassToNewMap.containsKey(styleClass) && !styleClassesList.contains(oldPersuasionClassToNewMap.get(styleClass))){
                additionalStyleClasses.add(oldPersuasionClassToNewMap.get(styleClass));
                removeClasses.add(styleClass);
                break;
            }
        }
        styleClassesList.removeAll(removeClasses);
        styleClassesList.addAll(additionalStyleClasses);
        return isRemoveIconType;
    }

    public Persuasion buildBlackPersuasionForReviewPage(BlackInfo blackInfo, boolean blackRevamp) {
        if (blackInfo != null && CollectionUtils.isNotEmpty(blackInfo.getInclusionsList()) &&
                mmtBlackMyCashPersuasion != null && CollectionUtils.isNotEmpty(mmtBlackMyCashPersuasion.getData()) &&
                mmtBlackMyCashPersuasion.getData().get(0) != null) {

            if (blackRevamp) {
                String myCashInclusion = blackInfo.getInclusionsList().stream()
                        .filter(inclusion -> InclusionCategory.MY_CASH.getLeafCategory().equalsIgnoreCase(inclusion.getLeafCategory()))
                        .findFirst()
                        .orElse(new Inclusion()).getValue();
                String discountInclusion = blackInfo.getInclusionsList().stream()
                        .filter(inclusion -> InclusionCategory.DISCOUNT.getLeafCategory().equalsIgnoreCase(inclusion.getLeafCategory()))
                        .findFirst()
                        .orElse(new Inclusion()).getValue();
                discountInclusion = StringUtils.isEmpty(discountInclusion) ? "" : discountInclusion + DOT + SPACE;
                myCashInclusion = StringUtils.isEmpty(myCashInclusion) ? discountInclusion : discountInclusion + myCashInclusion;
                Persuasion blackPersuasion = null;
                if (StringUtils.isNotEmpty(myCashInclusion) && mmtBlackRevampPersuasion != null && CollectionUtils.isNotEmpty(mmtBlackRevampPersuasion.getData()) &&
                        mmtBlackRevampPersuasion.getData().get(0) != null) {
                    blackPersuasion = new Persuasion();
                    BeanUtils.copyProperties(mmtBlackRevampPersuasion, blackPersuasion);
                    if (blackPersuasion.getStyle() == null) {
                        blackPersuasion.setStyle(new StyleResponseBO());
                    }
                    blackPersuasion.getStyle().setBorderGradient(blackInfo.getBorderGradient());
                    blackPersuasion.getData().get(0).setText(myCashInclusion);
                    blackPersuasion.getData().get(0).setIconurl(blackInfo.getIconUrl());
                }
                return blackPersuasion;
            }

            List<String> inclusionValues = blackInfo.getInclusionsList().stream()
                    .map(Inclusion::getValue)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());

            if (!inclusionValues.isEmpty()) {
                mmtBlackMyCashPersuasion.getData().get(0).setText(String.join(".\n", inclusionValues));
                if(Utility.isGCC()) {
                    mmtBlackMyCashPersuasion.getData().get(0).setIconurl(mmtSelectIcon);
                }
                return mmtBlackMyCashPersuasion;
            }
            return null;
        }
        return null;
    }

    public Persuasion buildHotelBenefitPersuasionForReviewPage(HotelBenefitInfo benefitInfo) {
        try {
            if (benefitInfo != null) {
                Persuasion benefitPersuasion = new Persuasion();
               BeanUtils.copyProperties(hotelBenefitPersuasion, benefitPersuasion);
                if (benefitPersuasion.getData() != null && !benefitPersuasion.getData().isEmpty()) {
                    if (benefitInfo.getText() != null) {
                        benefitPersuasion.getData().get(0).setText(benefitInfo.getText());
                    }
                }
                benefitPersuasion.getData().get(0).getExtraData().setIconUrl(
                        benefitInfo.getIconUrl() != null ? benefitInfo.getIconUrl() : null
                );
                benefitPersuasion.getData().get(0).setPersuasionType(benefitInfo.getBenefitType());
                return benefitPersuasion;
            }
        } catch (Exception e) {
            logger.error("Exception while building hotel benefit persuasion: ", e);
        }
        return null;
    }

    public static void buildTierPersuasion(HeroTierDetails heroTierDetails, PersuasionResponse persuasion) {
        if(heroTierDetails == null) {
            return;
        }
        persuasion.setHtml(true);
        persuasion.setIconUrl(heroTierDetails.getTierIcon());
        Style style = new Style();
        style.setTextColor(heroTierDetails.getTextColor());
        BGGradient bgGradient = new BGGradient();
        bgGradient.setColor(heroTierDetails.getBgGradient());
        style.setBgGradient(bgGradient);
        persuasion.setStyle(style);
    }

    public void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap) {
        PersuasionResponse persuasion = new PersuasionResponse();
        StringBuilder persuasionAppliedText = new StringBuilder();
        if (StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage())) {
            logger.debug("loyalty_offer_message: {}", coupon.getLoyaltyOfferMessage());
            persuasionAppliedText.append(String.format(polyglotService.getTranslatedData(ConstantsTranslation.LOYALTY_OFFER_TEXT), coupon.getLoyaltyOfferMessage()));
        } else {
            logger.debug("Promo_Cash_Amount: {}", coupon.getHybridDiscounts().get("CTW"));
            int cashbackDiscountAmtRounded = (int) Math.round(coupon.getHybridDiscounts().get("CTW"));
            persuasionAppliedText.append(String.format(polyglotService.getTranslatedData(ConstantsTranslation.CASHBACK_OFFER_TEXT), cashbackDiscountAmtRounded));
        }
        String iconType = StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage()) ? HERO_OFFER_PERSUASION_ICON_TYPE : CASHBACK_OFFER_PERSUASION_ICON_TYPE;
        persuasion.setPersuasionText(persuasionAppliedText.toString());
        persuasion.setHtml(true);
        persuasion.setIconType(iconType);
        persuasionMap.put(CASHBACK_HERO_OFFER_PERSUASION_NODE, persuasion);
    }

    public void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap, int myPartnerCashback, HeroTierDetails heroTierDetails) {
        PersuasionResponse persuasion = new PersuasionResponse();
        String persuasionAppliedText = "";
        persuasionAppliedText = polyglotService.getTranslatedData(ConstantsTranslation.MYPARTNER_TIER_OFFER_TEXT);

        if(myPartnerCashback > 0 && StringUtils.isNotEmpty(persuasionAppliedText) && heroTierDetails.getTierName() != null) {
            logger.debug("myPartner_Tier_Cash_Amount: {}", myPartnerCashback);
            persuasionAppliedText = persuasionAppliedText.replace(TIER_NAME, heroTierDetails.getTierName()).replace(CASHBACK_AMOUNT, Integer.toString(myPartnerCashback));
            persuasion.setPersuasionText(persuasionAppliedText);
            buildTierPersuasion(heroTierDetails, persuasion);
            String iconType = coupon != null && StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage()) ? HERO_OFFER_PERSUASION_ICON_TYPE : CASHBACK_OFFER_PERSUASION_ICON_TYPE;
            persuasion.setIconType(iconType);
            persuasion.setTemplate(MY_PARTNER_HERO_EFFECTIVE);
            persuasionMap.put(CASHBACK_HERO_OFFER_PERSUASION_NODE, persuasion);
        } else {
            buildLoyaltyCashbackPersuasions(coupon, persuasionMap);
        }
    }

    public boolean checkIfIndianessPersuasionExists(Object hotelPersuasions){
        if(hotelPersuasions == null)
            return false;
        try {
            JSONObject hotelPersuasionsV1 = new JSONObject(objectMapperUtil.getJsonFromObject(hotelPersuasions, DependencyLayer.CLIENTGATEWAY));
            for (String placeHolder : hotelPersuasionsV1.keySet()) {
                JSONObject persuasion = hotelPersuasionsV1.has(placeHolder) ? hotelPersuasionsV1.getJSONObject(placeHolder) : null;
                if (null != persuasion && persuasion.has("data")) {
                    JSONArray persuasionDataList = persuasion.getJSONArray("data");
                    if(persuasionDataList != null) {
                        for (int i = 0; i < persuasionDataList.length(); i++) {
                            JSONObject persuasionData = persuasionDataList.getJSONObject(i);
                            if (persuasionData != null && persuasionData.has(PERSUASION_KEY) && persuasionData.get(PERSUASION_KEY) != null &&
                                    LOVED_BY_INDIANS.equalsIgnoreCase((String) persuasionData.get(PERSUASION_KEY))) {
                                return true;
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            logger.error("Error Occurred while accessing for indianess persuasions", e);
        }
        return false;
    }
}
