# 🚀 JMeter Circuit Breaker Testing Guide

## 📋 **Overview**
This guide provides comprehensive instructions for load testing and circuit breaker testing using JMeter for the mob-landing API.

## 📁 **Files Created**
- `jmeter-circuit-breaker-test.jmx` - Main JMeter test plan
- `mob-landing-payload.json` - Request payload
- `circuit-breaker-monitoring.jmx` - Circuit breaker monitoring test plan
- `test-scenarios.md` - Test scenarios documentation

## 🛠️ **Prerequisites**
1. **JMeter Installation**
   ```bash
   # Download JMeter from https://jmeter.apache.org/download_jmeter.cgi
   # Or install via package manager
   brew install jmeter  # macOS
   sudo apt-get install jmeter  # Ubuntu
   ```

2. **Application Running**
   ```bash
   # Start your application
   mvn spring-boot:run -Dspring.profiles.active=hotels
   # Or
   java -jar target/Hotels-ClientGateway.jar
   ```

## 🎯 **Test Scenarios**

### **Scenario 1: Normal Load Testing**
**Objective**: Test normal application behavior with circuit breaker enabled

**Configuration**:
- Threads: 50 users
- Ramp-up: 30 seconds
- Duration: 5 minutes
- Expected: All requests succeed, circuit breaker stays CLOSED

### **Scenario 2: Circuit Breaker Triggering**
**Objective**: Force circuit breaker to OPEN state

**Steps**:
1. Stop downstream service (simulate failure)
2. Run load test
3. Monitor circuit breaker state transitions
4. Expected: CLOSED → OPEN → fallback responses

### **Scenario 3: Circuit Breaker Recovery**
**Objective**: Test circuit breaker recovery (HALF_OPEN → CLOSED)

**Steps**:
1. Start with circuit breaker in OPEN state
2. Restart downstream service
3. Wait for recovery period (60s for MAIN, 90s for BOT)
4. Monitor state transitions
5. Expected: OPEN → HALF_OPEN → CLOSED

### **Scenario 4: Stress Testing**
**Objective**: Test circuit breaker under extreme load

**Configuration**:
- Threads: 200 users
- Ramp-up: 60 seconds
- Duration: 10 minutes
- Expected: Circuit breaker protects system from overload

## 🔧 **JMeter Test Plan Configuration**

### **Variables (Configurable)**
```
BASE_URL = 127.0.0.1
PORT = 8081
THREADS = 50
RAMP_UP = 30
DURATION = 300
```

### **Thread Groups**
1. **Normal Load Test**: Standard load testing
2. **Circuit Breaker Monitor**: Monitors CB state during tests
3. **Stress Test**: High load testing

## 📊 **Monitoring During Tests**

### **Circuit Breaker Endpoints**
```bash
# Monitor circuit breaker state
curl "http://localhost:8081/clientbackend/cg/circuit-breaker/state/mob-landing"

# Monitor circuit breaker metrics
curl "http://localhost:8081/clientbackend/cg/circuit-breaker/metrics/mob-landing"

# Monitor overall health
curl "http://localhost:8081/clientbackend/cg/circuit-breaker/health"
```

### **Key Metrics to Watch**
- **Response Time**: Should remain stable
- **Error Rate**: Should be low when CB is CLOSED
- **Throughput**: Should be consistent
- **Circuit Breaker State**: CLOSED/OPEN/HALF_OPEN
- **Failure Rate**: Should trigger CB when threshold exceeded

## 🚀 **Running Tests**

### **Method 1: JMeter GUI**
```bash
# Open JMeter GUI
jmeter

# Load test plan: jmeter-circuit-breaker-test.jmx
# Configure variables as needed
# Run test and monitor results
```

### **Method 2: Command Line (Recommended for CI/CD)**
```bash
# Run normal load test
jmeter -n -t jmeter-circuit-breaker-test.jmx \
       -l results/normal-load-test.jtl \
       -e -o results/normal-load-report \
       -Jthreads=50 -Jramp_up=30 -Jduration=300

# Run stress test
jmeter -n -t jmeter-circuit-breaker-test.jmx \
       -l results/stress-test.jtl \
       -e -o results/stress-test-report \
       -Jthreads=200 -Jramp_up=60 -Jduration=600
```

### **Method 3: Automated Testing Script**
```bash
# Make script executable
chmod +x run-circuit-breaker-tests.sh

# Run all test scenarios
./run-circuit-breaker-tests.sh
```

## 📈 **Expected Results**

### **Normal Operation (Circuit Breaker CLOSED)**
- ✅ Response time: < 2 seconds
- ✅ Error rate: < 1%
- ✅ Throughput: Stable
- ✅ Circuit breaker state: CLOSED

### **Failure Scenario (Circuit Breaker OPEN)**
- ⚠️ Response time: Very fast (fallback)
- ⚠️ Error rate: 0% (fallback responses)
- ⚠️ Throughput: Maintained
- ⚠️ Circuit breaker state: OPEN

### **Recovery Scenario (HALF_OPEN → CLOSED)**
- 🔄 Response time: Gradually improves
- 🔄 Error rate: Decreases
- 🔄 Throughput: Returns to normal
- 🔄 Circuit breaker state: HALF_OPEN → CLOSED

## 🔍 **Troubleshooting**

### **Common Issues**
1. **Connection Refused**
   - Check if application is running
   - Verify port configuration

2. **High Error Rate**
   - Check downstream service availability
   - Verify circuit breaker configuration

3. **Circuit Breaker Not Triggering**
   - Check failure rate threshold
   - Verify minimum number of calls

### **Debug Commands**
```bash
# Check application logs
tail -f logs/application.log | grep "Circuit breaker"

# Check JMeter logs
tail -f jmeter.log

# Monitor system resources
top -p $(pgrep java)

# Test circuit breaker endpoints
curl "http://localhost:8081/clientbackend/cg/circuit-breaker/state/mob-landing"
curl "http://localhost:8081/clientbackend/cg/circuit-breaker/metrics/mob-landing"
```

## 📋 **Test Checklist**

### **Pre-Test**
- [ ] Application is running
- [ ] Circuit breaker is enabled
- [ ] Monitoring endpoints are accessible
- [ ] JMeter test plan is configured

### **During Test**
- [ ] Monitor circuit breaker state
- [ ] Watch response times
- [ ] Check error rates
- [ ] Monitor system resources

### **Post-Test**
- [ ] Analyze JMeter reports
- [ ] Review application logs
- [ ] Document findings
- [ ] Update configurations if needed

## 📊 **Reporting**

### **JMeter Reports**
- HTML reports generated in `results/` directory
- Key metrics: Response time, throughput, error rate
- Graphs: Response time over time, active threads

### **Circuit Breaker Metrics**
- State transition logs
- Failure rate trends
- Recovery time measurements
- Fallback execution counts

## 🎯 **Success Criteria**

### **Functional**
- ✅ Circuit breaker opens when failure threshold exceeded
- ✅ Fallback responses returned during OPEN state
- ✅ Circuit breaker recovers to CLOSED state
- ✅ No system crashes or memory leaks

### **Performance**
- ✅ Response time < 2s during normal operation
- ✅ Fallback response time < 100ms
- ✅ System handles target load without degradation
- ✅ Recovery time within acceptable limits

### **Reliability**
- ✅ No data corruption during failures
- ✅ Consistent behavior across test runs
- ✅ Proper error handling and logging
- ✅ Graceful degradation under load
