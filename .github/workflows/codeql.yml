name: Run CodeQL

on:
  pull_request:
    types: [closed]
    branches:
      - release
  push:
    branches:
      - release

jobs:
  call-workflow:
    name: Call Reusable Workflow
    permissions:
      security-events: write
      actions: read
      contents: read
    uses: MMT-PROD/DVOP-GitHub-Workflows/.github/workflows/codeql-buildless.yml@release
    with:
      language: 'java-kotlin'
      runner-type: 'java8-ci-worker'
