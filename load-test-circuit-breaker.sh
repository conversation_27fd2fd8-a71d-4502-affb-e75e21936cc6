#!/bin/bash

# Circuit Breaker Load Testing Script
# Usage: ./load-test-circuit-breaker.sh [base_url] [concurrent_users] [total_requests]

BASE_URL=${1:-"http://localhost:8080"}
CONCURRENT_USERS=${2:-10}
TOTAL_REQUESTS=${3:-100}
CB_NAME="mob-landing"

echo "🚀 Circuit Breaker Load Testing"
echo "================================"
echo "Base URL: $BASE_URL"
echo "Concurrent Users: $CONCURRENT_USERS"
echo "Total Requests: $TOTAL_REQUESTS"
echo "Circuit Breaker: $CB_NAME"
echo "================================"

# Create test payload
cat > mob-landing-payload.json << EOF
{
  "searchCriteria": {
    "locationId": "123",
    "checkIn": "2024-12-15",
    "checkOut": "2024-12-17"
  },
  "client": "web"
}
EOF

# Function to check circuit breaker state
check_cb_state() {
    local state=$(curl -s "$BASE_URL/cg/circuit-breaker/state/$CB_NAME" | jq -r '.state' 2>/dev/null || echo "UNKNOWN")
    echo "$state"
}

# Function to get circuit breaker metrics
get_cb_metrics() {
    curl -s "$BASE_URL/cg/circuit-breaker/metrics/$CB_NAME" | jq '.' 2>/dev/null || echo "Failed to get metrics"
}

# Function to make API call
make_api_call() {
    local user_id=$1
    local request_id=$2
    
    local start_time=$(date +%s%N)
    local response=$(curl -s -w "%{http_code}:%{time_total}" \
        -X POST "$BASE_URL/cg/mob-landing" \
        -H "Content-Type: application/json" \
        -d @mob-landing-payload.json 2>/dev/null)
    
    local end_time=$(date +%s%N)
    local duration=$(( (end_time - start_time) / 1000000 )) # Convert to milliseconds
    
    local http_code=$(echo "$response" | tail -c 10 | cut -d':' -f1)
    local curl_time=$(echo "$response" | tail -c 10 | cut -d':' -f2)
    
    echo "User:$user_id Request:$request_id HTTP:$http_code Time:${duration}ms"
}

# Function to run load test
run_load_test() {
    echo "📊 Starting load test..."
    echo "Initial Circuit Breaker State: $(check_cb_state)"
    echo ""
    
    local pids=()
    local requests_per_user=$((TOTAL_REQUESTS / CONCURRENT_USERS))
    
    # Start concurrent users
    for user in $(seq 1 $CONCURRENT_USERS); do
        {
            for request in $(seq 1 $requests_per_user); do
                make_api_call $user $request
                sleep 0.1 # Small delay between requests
            done
        } &
        pids+=($!)
    done
    
    # Monitor circuit breaker state during load test
    {
        while kill -0 ${pids[0]} 2>/dev/null; do
            echo "🔍 CB State: $(check_cb_state) at $(date)"
            sleep 2
        done
    } &
    monitor_pid=$!
    
    # Wait for all users to complete
    for pid in "${pids[@]}"; do
        wait $pid
    done
    
    # Stop monitoring
    kill $monitor_pid 2>/dev/null
    
    echo ""
    echo "✅ Load test completed!"
    echo "Final Circuit Breaker State: $(check_cb_state)"
    echo ""
    echo "📈 Final Metrics:"
    get_cb_metrics
}

# Function to test circuit breaker opening
test_circuit_breaker_opening() {
    echo ""
    echo "🔥 Testing Circuit Breaker Opening (Failure Scenario)"
    echo "======================================================"
    
    # This would require you to simulate failures
    # For example, by stopping the downstream service
    echo "To test circuit breaker opening:"
    echo "1. Stop the downstream mob-landing service"
    echo "2. Run: ./load-test-circuit-breaker.sh $BASE_URL 20 200"
    echo "3. Watch the circuit breaker state change from CLOSED -> OPEN"
    echo "4. Observe fallback responses being returned"
}

# Function to test circuit breaker recovery
test_circuit_breaker_recovery() {
    echo ""
    echo "🔄 Testing Circuit Breaker Recovery"
    echo "=================================="
    
    echo "To test circuit breaker recovery:"
    echo "1. After circuit breaker is OPEN, restart downstream service"
    echo "2. Wait for wait-duration (60s for MAIN pool, 90s for BOT pool)"
    echo "3. Make a few requests to trigger HALF_OPEN state"
    echo "4. Watch state transition: OPEN -> HALF_OPEN -> CLOSED"
}

# Main execution
echo "🏁 Starting Circuit Breaker Load Test..."
echo ""

# Check if service is running
if ! curl -s "$BASE_URL/actuator/health" > /dev/null 2>&1; then
    echo "❌ Service is not running at $BASE_URL"
    echo "Please start the application first"
    exit 1
fi

# Run the load test
run_load_test

# Show additional testing scenarios
test_circuit_breaker_opening
test_circuit_breaker_recovery

# Cleanup
rm -f mob-landing-payload.json

echo ""
echo "🎯 Load Testing Complete!"
echo "Check the metrics above to analyze circuit breaker behavior"
