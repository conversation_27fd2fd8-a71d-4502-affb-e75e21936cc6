<VirtualHost *:80>
    ServerName www.makemytrip.com
    ServerAlias www.makemytrip.com
    JkMountCopy On
    JkUnMount /*.GiF clientbackend
    DocumentRoot /opt/tomcat/webapps/

    <Directory "/opt/tomcat/webapps/">
        DirectoryIndex index.php home.shtml index.html index.jsp
        Options None
        AllowOverride All
        Order allow,deny
        Allow from all
        Require all granted
        Satisfy Any

        Header unset ETag
        FileETag None

        <Limit GET POST >
            Order allow,deny
            Allow from all
        </Limit>
        <LimitExcept GET POST >
            Order deny,allow
            Deny from all
        </LimitExcept>

    </Directory>

    <Directory />
        Options None
            AllowOverride None
            Order deny,allow
            Deny from all
            Require all denied
    </Directory>

    
    LogFormat "%{X-Forwarded-For}i %h %l %u %t \"%r\" %>s %b %D %D \"%{Referer}i\" \"%{User-Agent}i\" \"%{Cookie}i\" %D %P %T %B %{x-akamai-device-characteristics}i %{Akamai-Bot}i %{x-akamai-edgescape}i %{x-akamai-device-characteristics}o %{Akamai-Bot}o" clientbackend
    CustomLog /opt/logs/httpd/access.log clientbackend
    ErrorLog /opt/logs/httpd/error.log

    ErrorDocument 413 http://www.makemytrip.com/RETL.php
    ErrorDocument 400 http://www.makemytrip.com/RETL.php

    JkMount /* clientbackend
    JkMount /progressivist/* clientbackend
    JkMount /clientbackend/* clientbackend
    JkMount /*.jsp clientbackend
    JkMount /*.do clientbackend
    JkMount /imint/* clientbackend
    JkMount /*.js clientbackend

    JkMount /*.ico clientbackend
    JkMount /*.jpg clientbackend
    JkMount /*.gif clientbackend
    #JkUnMount /*.png clientbackend
    #JkUnMount /*.js clientbackend
    #JkUnMount /*.css clientbackend
    JkMount /*.GIF clientbackend
    JkUnMount /*.php clientbackend

    JkMount /*.ICO clientbackend
    JkMount /*.JPG clientbackend
    JkMount /*.PNG clientbackend
    JkMount /*.JS clientbackend
    JkMount /*.CSS clientbackend
    JkMount /*.GIF clientbackend

    RewriteRule (^|/)(CVS|\.svn|\.git|\.sh)/ - [F,L]

    # Cookie MMYTUUID addition
    #CookieTracking on
    #CookieDomain '.makemytrip.com'
    #CookieName MMYTUUID
    #CookieExpires '3 years'
    #CookieIPHeader 'X-Forwarded-For'
    #CookieDNTComply off

    <IfModule mod_unique_id.c>
           Header always set Set-Cookie "MMYTUUID=%{UNIQUE_ID}e; path=/; domain=.makemytrip.com; max-age=94608000;"
           Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline';"
    </IfModule>

    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript

</VirtualHost>
