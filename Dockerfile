# Base image
FROM 524881529748.dkr.ecr.ap-south-1.amazonaws.com/mmt-ecs-base:java-8-ubuntu22.04-multiarch-v1

# Tomcat version
ENV TOMCATVER 9.0.48

# Install Apache HTTP Server
RUN apt update -y
RUN apt-get update && apt-get install -y libapache2-mod-jk
RUN apt install apache2 apache2-dev -y

# Copy Apache HTTP Server configuration files to the appropriate directory
COPY .docker/mod_jk.conf .docker/clientgateway.conf /etc/apache2/conf-available/
COPY .docker/workers.properties .docker/mime.types /etc/apache2/

# Enable the configurations
RUN a2enconf mod_jk && a2enconf clientgateway
RUN a2enmod headers rewrite usertrack unique_id
RUN a2dismod mpm_worker mpm_event && a2enmod mpm_prefork

# Reload Apache to apply configurations (will ensure no syntax errors during build)
RUN service apache2 reload || true

# Create necessary directories for mod_jk and logging with proper ownership
RUN install -d -o www-data -g www-data /var/run/mod_jk /opt/logs/httpd

# Create additional directories for Kerberos, Tomcat, and application-specific files
RUN mkdir -p /etc/kerberos /opt/tomcat /opt/clientgateway

# Download and extract the specified version of Tomcat
ADD http://apaxy.mmt.mmt/SOFTWARES/tomcat/apache-tomcat-${TOMCATVER}.tar.gz /tmp/
RUN tar -xf /tmp/apache-tomcat-${TOMCATVER}.tar.gz --strip 1 -C /opt/tomcat && rm /tmp/apache-tomcat-${TOMCATVER}.tar.gz

# Remove unnecessary Tomcat webapps
RUN rm -rf /opt/tomcat/webapps/docs/  && \
    rm -rf /opt/tomcat/webapps/manager/ && \
    rm -rf /opt/tomcat/webapps/examples/ && \
    rm -rf /opt/tomcat/webapps/ROOT/ && \
    rm -rf /opt/tomcat/webapps/host-manager  

# Copy additional configuration files
COPY .docker/server.xml .docker/context.xml .docker/logging.properties /opt/tomcat/conf/
COPY .docker/hotel_cb.keytab .docker/jaas-cache.conf .docker/krb5.conf /etc/kerberos/
COPY .docker/jmxremote.access /opt/tomcat/conf/jmxremote.access

# Set permissions for JMX files
RUN chmod 777 /opt/tomcat/conf/jmxremote.access

COPY .docker/jmxremote.password /opt/tomcat/conf/jmxremote.password

RUN chmod 600 /opt/tomcat/conf/jmxremote.password

# Copy and configure environment settings
COPY .docker/setenv.sh /opt/tomcat/bin/

RUN mkdir -p /opt/manthan

COPY .docker/san-aws-ecs-mmt.crt /opt/manthan/

RUN sed -i '/JAVA_OPTS="$JAVA_OPTS $JSSE_OPTS"/a JAVA_OPTS="$JAVA_OPTS -Xms6g -Xmx6g -XX:MaxPermSize=512m -XX:+UseG1GC -verbose:gc -Xloggc:/opt/logs/gc.log -XX:+PrintGCTimeStamps -XX:+PrintGCDateStamps -Ddrools.schema.validating=false -Denv=prod"' /opt/tomcat/bin/catalina.sh

# Deploy the application WAR file to Tomcat's webapps directory
COPY target/clientbackend.war /opt/tomcat/webapps/

# Set environment variable
ENV env prod

# Copy supervisord configuration
COPY .docker/supervisord.conf /etc/supervisord.conf

# Set working directory
WORKDIR /opt

# Copy entrypoint script and set executable permissions
COPY .docker/entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/entrypoint.sh

# Set entrypoint and default command
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]

# Default command to run supervisord
CMD ["/usr/bin/supervisord"]
