# Base image
FROM 524881529748.dkr.ecr.ap-south-1.amazonaws.com/mmt-ecs-base:java-8-ubuntu22.04-multiarch-v1

# Define BIZEYE environment variables
ENV BIZEYE http://bizeye.mmt.mmt:1234
ENV BIZEYE_FOLDER HOTEL

# Install Apache HTTP Server
RUN apt update -y
RUN apt-get update && apt-get install -y libapache2-mod-jk
RUN apt install apache2 apache2-dev -y

# Enable the configurations
RUN a2enmod headers rewrite usertrack unique_id
RUN a2dismod mpm_worker mpm_event && a2enmod mpm_prefork

# Reload Apache to apply configurations (will ensure no syntax errors during build)
RUN service apache2 reload || true

RUN cd /etc/ && rm localtime && ln -s /usr/share/zoneinfo/Asia/Kolkata localtime

#Install tomcat
ADD ${BIZEYE}/PUPPET/tomcat/apache-tomcat-8.0.32.tar.gz /opt/tomcat/

#Install Java
ADD ${BIZEYE}/PUPPET/java/jdk1.8.0_101.tar.gz /opt/
RUN mkdir /opt/java

RUN tar -xf /opt/tomcat/apache-tomcat-8.0.32.tar.gz -C /opt/tomcat/ && ls -l /opt/tomcat/

RUN tar -xf /opt/jdk1.8.0_101.tar.gz -C /opt/java && ls -l /opt/java

RUN ln -s /opt/java/jdk1.8.0_101 /opt/java/jdk8

RUN mkdir -p /opt/logs/tomcat/tomcat8/ && mkdir -p /opt/mmtwebapps8/

# Create necessary directories for logs, webapps, Kerberos, and applications
RUN mkdir -p /etc/kerberos/conf/ /opt/clientbackend/

# Copy Kerberos configuration files
COPY ./docker/kerberos/conf/* /etc/kerberos/

# Copy application WAR file
COPY ./target/clientbackend.war /opt/mmtwebapps8/

# Create symbolic links for Tomcat directories
RUN rm -rf /opt/tomcat/apache-tomcat-8.0.32.tar.gz /opt/jdk1.8.0_101.tar.gz

RUN ln -s /opt/logs/tomcat/tomcat8/ /opt/tomcat/apache-tomcat-8.0.32/logs

RUN ln -s /opt/mmtwebapps8/ /opt/tomcat/apache-tomcat-8.0.32/webapps

RUN ln -s /opt/tomcat/apache-tomcat-8.0.32/ /opt/tomcat/tomcat8

RUN ln -s /opt/tomcat/apache-tomcat-8.0.32/ /opt/apache-tomcat-8.0.32

# Copy configuration files for Tomcat and application
COPY ./docker/conf/setclasspath.sh /opt/tomcat/tomcat8/bin/
COPY ./docker/conf/startup.sh /opt/tomcat/tomcat8/bin/
COPY ./docker/conf/catalina.sh /opt/tomcat/tomcat8/bin/
RUN chmod +x /opt/tomcat/tomcat8/bin/*

# Add Jacoco agent for code coverage
RUN mkdir -p /opt/jacoco/lib/
COPY ./docker/jacocoagent.jar /opt/jacoco/lib/
RUN chmod +x /opt/jacoco/lib/*

RUN mkdir -p /opt/manthan

COPY .docker/san-aws-ecs-mmt.crt /opt/manthan/

# Copy hosts file for configuration
COPY ./docker/conf/hosts /tmp/
RUN pip install meld3==0.6.7 supervisor==4.2.4
COPY ./docker/supervisord.conf /etc/supervisord.conf

#Set environment variables
ENV JAVA_HOME /opt/java/jdk8/
ENV CATALINA_HOME /opt/tomcat/tomcat8/

# Set entrypoint to supervisord
#CMD ["/usr/bin/supervisord"]
ENTRYPOINT ["/usr/bin/supervisord"]
