2025-09-15 18:12:08,343 INFO o.a.j.u.JMeterUtils: Setting Locale to en_EN
2025-09-15 18:12:08,353 INFO o.a.j.JMeter: Loading user properties from: /opt/homebrew/Cellar/jmeter/5.6.3/libexec/bin/user.properties
2025-09-15 18:12:08,354 INFO o.a.j.JMeter: Loading system properties from: /opt/homebrew/Cellar/jmeter/5.6.3/libexec/bin/system.properties
2025-09-15 18:12:08,354 INFO o.a.j.JMeter: Setting JMeter property: THREADS=50
2025-09-15 18:12:08,354 INFO o.a.j.JMeter: Setting JMeter property: RAMP_UP=30
2025-09-15 18:12:08,354 INFO o.a.j.JMeter: Setting JMeter property: DURATION=300
2025-09-15 18:12:08,356 INFO o.a.j.JMeter: Copyright (c) 1998-2024 The Apache Software Foundation
2025-09-15 18:12:08,356 INFO o.a.j.JMeter: Version 5.6.3
2025-09-15 18:12:08,356 INFO o.a.j.JMeter: java.version=21.0.8
2025-09-15 18:12:08,357 INFO o.a.j.JMeter: java.vm.name=OpenJDK 64-Bit Server VM
2025-09-15 18:12:08,357 INFO o.a.j.JMeter: os.name=Mac OS X
2025-09-15 18:12:08,357 INFO o.a.j.JMeter: os.arch=aarch64
2025-09-15 18:12:08,357 INFO o.a.j.JMeter: os.version=15.5
2025-09-15 18:12:08,357 INFO o.a.j.JMeter: file.encoding=UTF-8
2025-09-15 18:12:08,357 INFO o.a.j.JMeter: java.awt.headless=true
2025-09-15 18:12:08,357 INFO o.a.j.JMeter: Max memory     =1073741824
2025-09-15 18:12:08,357 INFO o.a.j.JMeter: Available Processors =12
2025-09-15 18:12:08,362 INFO o.a.j.JMeter: Default Locale=English (EN)
2025-09-15 18:12:08,363 INFO o.a.j.JMeter: JMeter  Locale=English (EN)
2025-09-15 18:12:08,363 INFO o.a.j.JMeter: JMeterHome=/opt/homebrew/Cellar/jmeter/5.6.3/libexec
2025-09-15 18:12:08,363 INFO o.a.j.JMeter: user.dir  =/Users/<USER>/IdeaProjects/Hotels-ClientGateway
2025-09-15 18:12:08,363 INFO o.a.j.JMeter: PWD       =/Users/<USER>/IdeaProjects/Hotels-ClientGateway
2025-09-15 18:12:08,384 INFO o.a.j.JMeter: IP: ************* Name: GGN-MAC-7703 FullName: *************
2025-09-15 18:12:08,390 INFO o.a.j.s.FileServer: Default base='/Users/<USER>/IdeaProjects/Hotels-ClientGateway'
2025-09-15 18:12:08,391 INFO o.a.j.s.FileServer: Set new base='/Users/<USER>/IdeaProjects/Hotels-ClientGateway'
2025-09-15 18:12:08,478 INFO o.a.j.s.SaveService: Testplan (JMX) version: 2.2. Testlog (JTL) version: 2.2
2025-09-15 18:12:08,500 INFO o.a.j.s.SaveService: Using SaveService properties version 5.0
2025-09-15 18:12:08,501 INFO o.a.j.s.SaveService: Using SaveService properties file encoding UTF-8
2025-09-15 18:12:08,502 INFO o.a.j.s.SaveService: Loading file: working-circuit-breaker-test.jmx
2025-09-15 18:12:08,529 INFO o.a.j.p.h.s.HTTPSamplerBase: Parser for text/html is org.apache.jmeter.protocol.http.parser.LagartoBasedHtmlParser
2025-09-15 18:12:08,529 INFO o.a.j.p.h.s.HTTPSamplerBase: Parser for application/xhtml+xml is org.apache.jmeter.protocol.http.parser.LagartoBasedHtmlParser
2025-09-15 18:12:08,530 INFO o.a.j.p.h.s.HTTPSamplerBase: Parser for application/xml is org.apache.jmeter.protocol.http.parser.LagartoBasedHtmlParser
2025-09-15 18:12:08,530 INFO o.a.j.p.h.s.HTTPSamplerBase: Parser for text/xml is org.apache.jmeter.protocol.http.parser.LagartoBasedHtmlParser
2025-09-15 18:12:08,530 INFO o.a.j.p.h.s.HTTPSamplerBase: Parser for text/vnd.wap.wml is org.apache.jmeter.protocol.http.parser.RegexpHTMLParser
2025-09-15 18:12:08,530 INFO o.a.j.p.h.s.HTTPSamplerBase: Parser for text/css is org.apache.jmeter.protocol.http.parser.CssParser
2025-09-15 18:12:08,544 INFO o.a.j.JMeter: Creating summariser <summary>
2025-09-15 18:12:08,551 INFO o.a.j.e.StandardJMeterEngine: Running the test!
2025-09-15 18:12:08,551 INFO o.a.j.s.SampleEvent: List of sample_variables: []
2025-09-15 18:12:08,551 INFO o.a.j.s.SampleEvent: List of sample_variables: []
2025-09-15 18:12:08,552 INFO o.a.j.e.u.CompoundVariable: Note: Function class names must contain the string: '.functions.'
2025-09-15 18:12:08,553 INFO o.a.j.e.u.CompoundVariable: Note: Function class names must not contain the string: '.gui.'
2025-09-15 18:12:08,579 INFO o.a.j.r.ClassFinder: Will scan jar /opt/homebrew/Cellar/jmeter/5.6.3/libexec/lib/ext/jmeter-plugins-manager-1.9.jar with filter ExtendsClassFilter [parents=[interface org.apache.jmeter.functions.Function], inner=false, contains=null, notContains=null]. Consider exposing JMeter plugins via META-INF/services, and add JMeter-Skip-Class-Scanning=true manifest attribute so JMeter can skip classfile scanning
2025-09-15 18:12:08,617 INFO o.a.j.JMeter: Running test (1757940128617)
2025-09-15 18:12:08,626 INFO o.a.j.e.StandardJMeterEngine: Starting ThreadGroup: 1 : Load Test
2025-09-15 18:12:08,626 INFO o.a.j.e.StandardJMeterEngine: Starting 5 threads for group Load Test.
2025-09-15 18:12:08,626 INFO o.a.j.e.StandardJMeterEngine: Thread will continue on error
2025-09-15 18:12:08,626 INFO o.a.j.t.ThreadGroup: Starting thread group... number=1 threads=5 ramp-up=5 delayedStart=false
2025-09-15 18:12:08,629 INFO o.a.j.t.JMeterThread: Thread started: Load Test 1-1
2025-09-15 18:12:08,629 INFO o.a.j.t.ThreadGroup: Started thread group number 1
2025-09-15 18:12:08,629 INFO o.a.j.e.StandardJMeterEngine: All thread groups have been started
2025-09-15 18:12:09,631 INFO o.a.j.t.JMeterThread: Thread started: Load Test 1-2
2025-09-15 18:12:09,642 INFO o.a.j.p.h.s.HTTPHCAbstractImpl: Local host = GGN-MAC-7703
2025-09-15 18:12:09,644 INFO o.a.j.p.h.s.HTTPHC4Impl: HTTP request retry count = 0
2025-09-15 18:12:09,644 INFO o.a.j.s.SampleResult: Note: Sample TimeStamps are START times
2025-09-15 18:12:09,644 INFO o.a.j.s.SampleResult: sampleresult.default.encoding is set to UTF-8
2025-09-15 18:12:09,644 INFO o.a.j.s.SampleResult: sampleresult.useNanoTime=true
2025-09-15 18:12:09,644 INFO o.a.j.s.SampleResult: sampleresult.nanoThreadSleep=5000
2025-09-15 18:12:10,632 INFO o.a.j.t.JMeterThread: Thread started: Load Test 1-3
2025-09-15 18:12:11,628 INFO o.a.j.t.JMeterThread: Thread started: Load Test 1-4
2025-09-15 18:12:12,631 INFO o.a.j.t.JMeterThread: Thread started: Load Test 1-5
2025-09-15 18:12:18,771 INFO o.a.j.t.JMeterThread: Thread is done: Load Test 1-1
2025-09-15 18:12:18,771 INFO o.a.j.t.JMeterThread: Thread finished: Load Test 1-1
2025-09-15 18:12:19,699 INFO o.a.j.t.JMeterThread: Thread is done: Load Test 1-2
2025-09-15 18:12:19,699 INFO o.a.j.t.JMeterThread: Thread finished: Load Test 1-2
2025-09-15 18:12:20,706 INFO o.a.j.t.JMeterThread: Thread is done: Load Test 1-3
2025-09-15 18:12:20,707 INFO o.a.j.t.JMeterThread: Thread finished: Load Test 1-3
2025-09-15 18:12:21,704 INFO o.a.j.t.JMeterThread: Thread is done: Load Test 1-4
2025-09-15 18:12:21,704 INFO o.a.j.t.JMeterThread: Thread finished: Load Test 1-4
2025-09-15 18:12:22,713 INFO o.a.j.t.JMeterThread: Thread is done: Load Test 1-5
2025-09-15 18:12:22,713 INFO o.a.j.t.JMeterThread: Thread finished: Load Test 1-5
2025-09-15 18:12:22,713 INFO o.a.j.e.StandardJMeterEngine: Notifying test listeners of end of test
2025-09-15 18:12:22,715 INFO o.a.j.r.Summariser: summary =     50 in 00:00:14 =    3.5/s Avg:     3 Min:     1 Max:    32 Err:    50 (100.00%)
