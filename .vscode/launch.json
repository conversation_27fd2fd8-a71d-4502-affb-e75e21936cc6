{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "Current File",
            "request": "launch",
            "mainClass": "${file}"
        },
        {
            "type": "java",
            "name": "HotelsClientGatewayApplication",
            "request": "launch",
            "mainClass": "com.mmt.hotels.clientgateway.configuration.HotelsClientGatewayApplication",
            "projectName": "clientbackend",
            "args": "--spring.profiles.active=dev"
        }
    ]
}