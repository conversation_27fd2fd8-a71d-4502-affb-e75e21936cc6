# 🚀 Quick Start Guide - JMeter Circuit Breaker Testing

## 📋 **Prerequisites**
1. **Install JMeter**
   ```bash
   # macOS
   brew install jmeter
   
   # Ubuntu/Debian
   sudo apt-get install jmeter
   
   # Or download from: https://jmeter.apache.org/download_jmeter.cgi
   ```

2. **Install jq (for JSON parsing)**
   ```bash
   # macOS
   brew install jq
   
   # Ubuntu/Debian
   sudo apt-get install jq
   ```

3. **Start Your Application**
   ```bash
   mvn spring-boot:run -Dspring.profiles.active=hotels
   ```

## 🎯 **Quick Test Execution**

### **Option 1: Automated Testing (Recommended)**
```bash
# Run all tests automatically
./run-circuit-breaker-tests.sh all

# Run specific test types
./run-circuit-breaker-tests.sh normal    # Normal load test
./run-circuit-breaker-tests.sh stress    # Stress test
./run-circuit-breaker-tests.sh failure   # Failure simulation (manual)
```

### **Option 2: Manual JMeter Execution**
```bash
# GUI Mode (for development/debugging)
jmeter -t jmeter-circuit-breaker-test.jmx

# Command Line Mode (for CI/CD)
jmeter -n -t jmeter-circuit-breaker-test.jmx \
       -l results/test-results.jtl \
       -e -o results/html-report \
       -JTHREADS=50 -JRAMP_UP=30 -JDURATION=300
```

### **Option 3: Simple Load Test**
```bash
# Basic load test with 10 users for 2 minutes
jmeter -n -t jmeter-circuit-breaker-test.jmx \
       -l results/simple-test.jtl \
       -JTHREADS=10 -JRAMP_UP=10 -JDURATION=120
```

## 📊 **Monitor Circuit Breaker During Tests**

### **Real-time Monitoring**
```bash
# Watch circuit breaker state
watch -n 2 'curl -s "http://localhost:8081/clientbackend/cg/circuit-breaker/state/mob-landing" | jq'

# Monitor metrics
watch -n 5 'curl -s "http://localhost:8081/clientbackend/cg/circuit-breaker/metrics/mob-landing" | jq'
```

### **Manual Monitoring Commands**
```bash
# Check current state
curl "http://localhost:8081/clientbackend/cg/circuit-breaker/state/mob-landing" | jq

# Get detailed metrics
curl "http://localhost:8081/clientbackend/cg/circuit-breaker/metrics/mob-landing" | jq

# Health check all circuit breakers
curl "http://localhost:8081/clientbackend/cg/circuit-breaker/health" | jq
```

## 🔧 **Test Configuration**

### **Modify Test Parameters**
Edit variables in `jmeter-circuit-breaker-test.jmx` or use command line:

```bash
# Custom configuration
jmeter -n -t jmeter-circuit-breaker-test.jmx \
       -JBASE_URL=localhost \
       -JPORT=8080 \
       -JTHREADS=100 \
       -JRAMP_UP=60 \
       -JDURATION=600 \
       -l results/custom-test.jtl
```

### **Test Scenarios**
| Scenario | Threads | Ramp-up | Duration | Purpose |
|----------|---------|---------|----------|---------|
| Light Load | 10 | 10s | 2min | Basic functionality |
| Normal Load | 50 | 30s | 5min | Standard testing |
| Heavy Load | 100 | 60s | 10min | Performance testing |
| Stress Test | 200+ | 60s | 10min | Circuit breaker triggering |

## 📈 **Expected Results**

### **Normal Operation (Circuit Breaker CLOSED)**
- ✅ Response time: < 2000ms
- ✅ Error rate: < 1%
- ✅ Circuit breaker state: CLOSED
- ✅ Successful calls increasing

### **Circuit Breaker Triggered (OPEN)**
- ⚠️ Response time: < 100ms (fallback)
- ⚠️ Error rate: 0% (fallback responses)
- ⚠️ Circuit breaker state: OPEN
- ⚠️ Failed calls stopped

### **Recovery (HALF_OPEN → CLOSED)**
- 🔄 Response time: Improving
- 🔄 Error rate: Decreasing
- 🔄 Circuit breaker state: HALF_OPEN → CLOSED
- 🔄 Normal operation resumed

## 🔍 **Troubleshooting**

### **Common Issues**
1. **"Connection refused"**
   ```bash
   # Check if application is running
   curl http://localhost:8081/clientbackend/health
   ```

2. **"Circuit breaker not found"**
   ```bash
   # Verify circuit breaker is enabled
   grep "circuit.breaker.mob-landing.enabled" src/main/resources/application*.properties
   ```

3. **JMeter test fails**
   ```bash
   # Check JMeter logs
   tail -f jmeter.log
   
   # Verify test plan
   jmeter -t jmeter-circuit-breaker-test.jmx -l /dev/null
   ```

### **Debug Commands**
```bash
# Application logs
tail -f logs/application.log | grep -i "circuit"

# Test connectivity
curl -v "http://localhost:8081/clientbackend/cg/mob-landing/android/2"

# Check system resources
top -p $(pgrep java)
```

## 📋 **Quick Checklist**

### **Before Testing**
- [ ] Application is running on correct port
- [ ] Circuit breaker is enabled
- [ ] JMeter is installed and accessible
- [ ] Test files are in correct location

### **During Testing**
- [ ] Monitor circuit breaker state
- [ ] Watch response times in JMeter
- [ ] Check application logs for errors
- [ ] Monitor system resources

### **After Testing**
- [ ] Review JMeter HTML reports
- [ ] Analyze circuit breaker metrics
- [ ] Document any issues found
- [ ] Save test results for comparison

## 🎯 **Success Criteria**
- ✅ Circuit breaker opens when failure threshold exceeded (30% for MAIN, 50% for BOT)
- ✅ Fallback responses returned during OPEN state
- ✅ Circuit breaker recovers to CLOSED state after downstream recovery
- ✅ No application crashes or memory leaks during testing
- ✅ Response times within acceptable limits
- ✅ System handles target load without degradation

## 📊 **Results Location**
- **JMeter Reports**: `results/*/report/index.html`
- **Raw Data**: `results/*/results.jtl`
- **Circuit Breaker Logs**: `results/*/circuit-breaker-monitor.log`
- **Summary**: `results/test-summary.md`

## 🆘 **Need Help?**
1. Check the detailed guide: `circuit-breaker-jmeter-guide.md`
2. Review application logs for circuit breaker events
3. Verify configuration in property files
4. Test individual endpoints manually with curl
