graph TD
    Client[Client Applications\niOS/Android/Web] -->|HTTP Request| Controller[DetailController\n/cg/search-rooms]
    Controller -->|SearchRoomsRequest| Service[DetailService\nsearchRooms()]
    Service -->|Get Transformer| Factory[SearchRoomsFactory]
    Factory -->|Client-specific transformer| RequestTransformer[SearchRoomsRequestTransformer]
    RequestTransformer -->|Transform request| Executor[SearchRoomsExecutor]
    
    Executor -->|Parallel Execution| PricingAPI[Room Pricing API\nPrice & Offers]
    Executor -->|Parallel Execution| StaticAPI[Room Static API\nAmenities & Details]
    Executor -->|Parallel Execution| ImagesAPI[Hotel Images API\nRoom Photos]
    
    PricingAPI --> Aggregator[Data Aggregation\n& Processing]
    StaticAPI --> Aggregator
    ImagesAPI --> Aggregator
    
    Aggregator --> ResponseTransformer[SearchRoomsResponseTransformer]
    ResponseTransformer -->|Client-specific response| Service
    
    Service -->|SearchRoomsResponse| Controller
    Controller -->|HTTP Response| Client
    
    ErrorHandler[Error Handling\n& Metrics Collection] -.->|Monitor| Executor
    ErrorHandler -.->|Monitor| Aggregator
    ErrorHandler -.->|Monitor| Service