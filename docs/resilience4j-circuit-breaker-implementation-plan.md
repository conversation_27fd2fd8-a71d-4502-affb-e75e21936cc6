# Resilience4j Circuit Breaker Implementation Plan for 'cg/mob-landing'

## Overview
This document outlines the implementation plan for adding Resilience4j circuit breaker functionality to the 'cg/mob-landing' endpoint. The implementation is designed to be easily extensible to other APIs and supports different configurations for MAIN and BOT pools.

## Table of Contents
1. [Phase 1: Dependencies and Configuration Setup](#phase-1-dependencies-and-configuration-setup)
2. [Phase 2: Circuit Breaker Configuration Classes](#phase-2-circuit-breaker-configuration-classes)
3. [Phase 3: Service Layer Implementation](#phase-3-service-layer-implementation)
4. [Phase 4: Controller Layer Integration](#phase-4-controller-layer-integration)
5. [Phase 5: Monitoring and Metrics](#phase-5-monitoring-and-metrics)
6. [Phase 6: Testing Strategy](#phase-6-testing-strategy)
7. [Phase 7: Deployment Strategy](#phase-7-deployment-strategy)
8. [Phase 8: Documentation and Training](#phase-8-documentation-and-training)
9. [Configuration Examples](#configuration-examples)
10. [Success Criteria](#success-criteria)

---

## Phase 1: Dependencies and Configuration Setup

### 1.1 Add Dependencies
- Add Resilience4j Spring Boot starter to `pom.xml`
- Add Resilience4j circuit breaker dependency
- Add Resilience4j metrics dependency for monitoring

### 1.2 Update Existing Pool-Specific Configuration Files
- **MAIN pool**: Add circuit breaker configurations to `src/main/resources/application-hotels.properties`
- **BOT pool**: Add circuit breaker configurations to `.docker/application-oth.properties`
- Leverage existing pool differentiation mechanism (pool.name=MAIN vs pool.name=BOT)

### 1.3 Configuration Structure
Pool-specific configs to be added to existing files:
- Circuit breaker timeout durations
- Failure rate thresholds
- Wait duration in open state
- Sliding window sizes
- Minimum number of calls
- Permitted calls in half-open state

---

## Phase 2: Circuit Breaker Configuration Classes

### 2.1 Create Base Configuration Class
- `CircuitBreakerConfig.java` - Base configuration with common settings
- Use `@ConfigurationProperties` for pool-specific property binding
- Read from existing property files based on pool.name value
- Implement factory pattern for creating circuit breaker instances

### 2.2 Create Pool-Aware Configuration Class
- `PoolAwareCircuitBreakerConfig.java` - Single config class that reads pool.name
- Use conditional logic to apply MAIN vs BOT pool configurations
- Integrate with existing `PropertyManagerConfig.java` pattern
- Leverage existing PMS (Property Management System) infrastructure

### 2.3 Circuit Breaker Registry Setup
- Create `CircuitBreakerRegistry` bean
- Configure different circuit breakers for different API categories:
  - `mob-landing-circuit-breaker`
  - `detail-api-circuit-breaker` 
  - `listing-api-circuit-breaker`
  - `search-api-circuit-breaker`
- Use pool.name to determine which configuration set to apply

---

## Phase 3: Service Layer Implementation

### 3.1 Create Circuit Breaker Service Wrapper
- `CircuitBreakerService.java` - Generic wrapper for all API calls
- Implement fallback methods for different API types
- Add retry logic integration with circuit breaker
- Integrate with existing thread pool configurations (detailServiceThreadPool, listingThreadPool, etc.)

### 3.2 Update Existing Service Classes
- Modify `MobLandingService.java` to use circuit breaker wrapper
- Update `RestConnector.java` to support circuit breaker annotations
- Ensure backward compatibility with existing timeout configurations from both property files
- Leverage existing `CommonConfig` and `MobConfigProps` interfaces

### 3.3 Fallback Strategy Implementation
- Create fallback response builders for mob-landing endpoint
- Implement graceful degradation strategies
- Cache last successful responses for fallback scenarios
- Integrate with existing `PropertyTextConfig` for fallback messages

---

## Phase 4: Controller Layer Integration

### 4.1 Update MobLanding Controller
- Add circuit breaker annotations to `cg/mob-landing` endpoint
- Implement controller-level fallback methods
- Add circuit breaker state monitoring endpoints
- Ensure compatibility with existing `@PropertySource` loading mechanism

### 4.2 Error Handling Enhancement
- Update existing error handling in controllers
- Map circuit breaker exceptions to appropriate HTTP status codes
- Ensure consistent error response format with existing `CBError` enum

---

## Phase 5: Monitoring and Metrics

### 5.1 Circuit Breaker Metrics Integration
- Integrate with existing `MetricAspect.java`
- Add circuit breaker state metrics (OPEN, CLOSED, HALF_OPEN)
- Track failure rates and response times per pool
- Use existing pool.name property for metric tagging

### 5.2 Health Check Integration
- Update existing health check endpoints to include circuit breaker status
- Add circuit breaker state to application health indicators
- Configure alerts for circuit breaker state changes
- Leverage existing Consul integration for health monitoring

### 5.3 Logging Enhancement
- Add circuit breaker state change logging
- Include pool information (MAIN/BOT) in log messages
- Integrate with existing correlation key tracking
- Use existing Kafka logging infrastructure if needed

---

## Phase 6: Testing Strategy

### 6.1 Unit Testing
- Create unit tests for circuit breaker configurations
- Test fallback method execution
- Verify pool-specific configuration loading from existing property files
- Test with both `application-hotels.properties` and `application-oth.properties`

### 6.2 Integration Testing
- Test circuit breaker behavior under load
- Verify fallback responses match expected format
- Test configuration switching between pools
- Validate integration with existing PMS property loading

### 6.3 Load Testing
- Simulate high failure rates to trigger circuit breaker
- Test recovery behavior when services become healthy
- Validate that other APIs remain unaffected
- Test both MAIN and BOT pool behaviors separately

---

## Phase 7: Deployment Strategy

### 7.1 Gradual Rollout Plan
- Deploy to BOT pool first (using `.docker/application-oth.properties`)
- Monitor circuit breaker behavior and metrics
- Gradually enable for MAIN pool (using `application-hotels.properties`)

### 7.2 Feature Toggle Implementation
- Add feature flags to existing property files to enable/disable circuit breaker per pool
- Allow runtime configuration changes via existing Consul integration
- Implement graceful fallback to original behavior
- Leverage existing PMS subscription mechanism for dynamic updates

### 7.3 Rollback Strategy
- Prepare rollback scripts for quick reversion
- Document configuration changes in existing property files
- Test rollback scenarios in staging environment
- Use existing property file versioning if available

---

## Phase 8: Documentation and Training

### 8.1 Configuration Documentation
- Document all pool-specific configurations in existing property files
- Create troubleshooting guides for circuit breaker issues
- Document fallback behavior and expected responses
- Update existing property file documentation

### 8.2 Operational Runbooks
- Create runbooks for circuit breaker state management
- Document monitoring and alerting procedures
- Provide guidance for tuning circuit breaker parameters
- Integrate with existing operational procedures

---

## Configuration Examples

### MAIN Pool (`src/main/resources/application-hotels.properties`)
```properties
# Existing pool identifier
pool.name=MAIN

# New Circuit Breaker configurations for MAIN pool (High Traffic)
resilience4j.circuitbreaker.instances.mob-landing.failure-rate-threshold=30
resilience4j.circuitbreaker.instances.mob-landing.timeout-duration=8s
resilience4j.circuitbreaker.instances.mob-landing.sliding-window-size=100
resilience4j.circuitbreaker.instances.mob-landing.minimum-number-of-calls=20
resilience4j.circuitbreaker.instances.mob-landing.wait-duration-in-open-state=60s
resilience4j.circuitbreaker.instances.mob-landing.permitted-number-of-calls-in-half-open-state=10

# Circuit breaker for other APIs (extensible)
resilience4j.circuitbreaker.instances.detail-api.failure-rate-threshold=25
resilience4j.circuitbreaker.instances.detail-api.timeout-duration=10s
resilience4j.circuitbreaker.instances.listing-api.failure-rate-threshold=35
resilience4j.circuitbreaker.instances.listing-api.timeout-duration=12s
```

### BOT Pool (`.docker/application-oth.properties`)
```properties
# Existing pool identifier  
pool.name=BOT

# New Circuit Breaker configurations for BOT pool (Lower Traffic)
resilience4j.circuitbreaker.instances.mob-landing.failure-rate-threshold=50
resilience4j.circuitbreaker.instances.mob-landing.timeout-duration=12s
resilience4j.circuitbreaker.instances.mob-landing.sliding-window-size=50
resilience4j.circuitbreaker.instances.mob-landing.minimum-number-of-calls=10
resilience4j.circuitbreaker.instances.mob-landing.wait-duration-in-open-state=90s
resilience4j.circuitbreaker.instances.mob-landing.permitted-number-of-calls-in-half-open-state=5

# Circuit breaker for other APIs (extensible)
resilience4j.circuitbreaker.instances.detail-api.failure-rate-threshold=40
resilience4j.circuitbreaker.instances.detail-api.timeout-duration=15s
resilience4j.circuitbreaker.instances.listing-api.failure-rate-threshold=45
resilience4j.circuitbreaker.instances.listing-api.timeout-duration=18s
```

### Feature Toggle Configuration
```properties
# Feature toggles for circuit breaker
circuit.breaker.mob-landing.enabled=true
circuit.breaker.detail-api.enabled=false
circuit.breaker.listing-api.enabled=false

# Fallback configurations
circuit.breaker.mob-landing.fallback.cache.enabled=true
circuit.breaker.mob-landing.fallback.cache.ttl=300000
```

---

## Implementation Timeline

| Phase | Duration | Dependencies | Deliverables |
|-------|----------|--------------|--------------|
| Phase 1 | 2 days | None | Dependencies added, configurations updated |
| Phase 2 | 3 days | Phase 1 | Configuration classes implemented |
| Phase 3 | 4 days | Phase 2 | Service layer integration complete |
| Phase 4 | 2 days | Phase 3 | Controller integration complete |
| Phase 5 | 3 days | Phase 4 | Monitoring and metrics implemented |
| Phase 6 | 5 days | Phase 5 | Testing complete |
| Phase 7 | 3 days | Phase 6 | Deployment to production |
| Phase 8 | 2 days | Phase 7 | Documentation complete |

**Total Estimated Duration: 24 days**

---

## Risk Assessment

### High Risk
- **Thread pool exhaustion during fallback**: Mitigation - Implement separate thread pools for fallback operations
- **Configuration conflicts between pools**: Mitigation - Thorough testing with both property files

### Medium Risk
- **Performance impact of circuit breaker overhead**: Mitigation - Performance testing and optimization
- **Fallback response compatibility**: Mitigation - Ensure fallback responses match existing API contracts

### Low Risk
- **Monitoring integration issues**: Mitigation - Leverage existing monitoring infrastructure
- **Documentation gaps**: Mitigation - Comprehensive documentation review

---

## Success Criteria

1. **Functional Requirements**
   - Circuit breaker prevents cascade failures
   - Other APIs remain unaffected during mob-landing issues
   - Graceful degradation with meaningful fallback responses

2. **Technical Requirements**
   - Pool-specific configurations work correctly using existing property files
   - Easy extensibility to other endpoints
   - Seamless integration with existing property management infrastructure

3. **Operational Requirements**
   - Comprehensive monitoring and alerting
   - Clear operational runbooks
   - Successful rollback capability

4. **Performance Requirements**
   - No significant performance degradation under normal conditions
   - Improved overall system stability during failures
   - Reduced MTTR (Mean Time To Recovery) for service issues

---

## Appendix

### A. Related Documentation
- Existing property file documentation
- Current monitoring and alerting procedures
- Deployment procedures for MAIN and BOT pools

### B. Contact Information
- **Technical Lead**: [Name]
- **DevOps Team**: [Contact]
- **Monitoring Team**: [Contact]

### C. References
- [Resilience4j Documentation](https://resilience4j.readme.io/)
- [Spring Boot Circuit Breaker Guide](https://spring.io/guides/gs/circuit-breaker/)
- Internal architecture documentation

---

**Document Version**: 1.0  
**Last Updated**: [Date]  
**Next Review**: [Date + 3 months]