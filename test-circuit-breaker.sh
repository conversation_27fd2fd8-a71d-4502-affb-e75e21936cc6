#!/bin/bash

# Circuit Breaker Testing Script
# Usage: ./test-circuit-breaker.sh [base_url]

BASE_URL=${1:-"http://localhost:8081"}
CB_NAME="mob-landing"

echo "🧪 Testing Circuit Breaker Functionality"
echo "Base URL: $BASE_URL"
echo "Circuit Breaker: $CB_NAME"
echo "=================================="

# Function to check circuit breaker state
check_state() {
    echo "📊 Checking circuit breaker state..."
    curl -s "$BASE_URL/cg/circuit-breaker/state/$CB_NAME" | jq '.'
    echo ""
}

# Function to check circuit breaker metrics
check_metrics() {
    echo "📈 Checking circuit breaker metrics..."
    curl -s "$BASE_URL/cg/circuit-breaker/metrics/$CB_NAME" | jq '.'
    echo ""
}

# Function to check health
check_health() {
    echo "🏥 Checking overall health..."
    curl -s "$BASE_URL/cg/circuit-breaker/health" | jq '.mobLanding'
    echo ""
}

# Function to make mob-landing API call
make_api_call() {
    echo "🔄 Making mob-landing API call..."
    curl -s -X POST "$BASE_URL/cg/mob-landing" \
        -H "Content-Type: application/json" \
        -d '{
            "searchCriteria": {
                "locationId": "123",
                "checkIn": "2024-12-15",
                "checkOut": "2024-12-17"
            },
            "client": "web"
        }' | head -c 200
    echo ""
}

# Test 1: Initial state check
echo "🔍 Test 1: Initial State Check"
check_state
check_metrics

# Test 2: Make successful API calls
echo "✅ Test 2: Making Successful API Calls"
for i in {1..5}; do
    echo "Call $i:"
    make_api_call
    sleep 1
done

echo "📊 Metrics after successful calls:"
check_metrics

# Test 3: Health check
echo "🏥 Test 3: Health Check"
check_health

# Test 4: Load testing (optional)
echo "⚡ Test 4: Load Testing (10 concurrent calls)"
for i in {1..10}; do
    make_api_call &
done
wait

echo "📊 Metrics after load test:"
check_metrics

echo "✅ Circuit Breaker Testing Complete!"
echo "=================================="
echo "Manual Tests to Perform:"
echo "1. Stop downstream service and test failure scenarios"
echo "2. Monitor state transitions: CLOSED -> OPEN -> HALF_OPEN -> CLOSED"
echo "3. Verify fallback responses during OPEN state"
echo "4. Test with different pool configurations (MAIN vs BOT)"
