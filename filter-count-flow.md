```mermaid
flowchart LR
    Client[Client] -->|filter-count request| CG[Client Gateway]
    CG -->|filter-count request| Orch[Orchestrator]
    Orch -->|filter-count request| KG[Knowledge Graph]
    KG -->|process request| CTN[Collection Theme Node]
    CTN -->|return theme data| KG
    KG -->|filter-count response| Orch
    Orch -->|filter-count response| CG
    CG -->|filter-count response| Client

    style Client fill:#f9f,font-size:16px
    style CG fill:#bbf,font-size:16px
    style Orch fill:#bfb,font-size:16px
    style KG fill:#fbb,font-size:16px
    style CTN fill:#fbf,font-size:16px

    linkStyle default font-size:14px
```

## Flow Description

1. **Request Flow**:
   - Client initiates filter-count API request with funnel=STAYCATIONS
   - Request flows through Client Gateway → Orchestrator → Knowledge Graph
   - Knowledge Graph processes request and interacts with Collection Theme Node
   - Collection Theme Node returns theme data to Knowledge Graph

2. **Response Flow**:
   - Knowledge Graph sends response back through Orchestrator
   - Orchestrator forwards response to Client Gateway
   - Client Gateway delivers response to Client

3. **Key Points**:
   - This flow is specific to GCC funnel
   - Collection Theme Node is a new separate node in the Knowledge Graph
   - All communication follows the same path in both directions
   - The diagram shows clear separation of concerns between different components 